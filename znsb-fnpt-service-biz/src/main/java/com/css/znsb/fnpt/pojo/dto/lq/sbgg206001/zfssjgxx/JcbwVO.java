package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.zfssjgxx;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 经办报文VO
 * <p>
 * 序号	数据项名称	                    数据项标识符	        是否可空	字段类型	  长度	  备注
 * 1	    经办人姓名	                  jbr	            N	    VARCHAR	  150
 * 2	    经办人身份证件类型代码	        jbrsfzjlxDm	    N	    CHAR	    3
 * 3	    经办人身份证件号码	          jbrsfzjhm	      N	    VARCHAR	  30
 */
@Schema(description = "经办报文")
@Data
public class JcbwVO implements Serializable {

    /**
     * 经办人姓名
     */
    @Schema(description = "经办人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "陈一一")
    @JsonProperty(value = "jbr")
    private String jbr;

    /**
     * 经办人身份证件类型代码
     */
    @Schema(description = "经办人身份证件类型代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "201")
    @JsonProperty(value = "jbrsfzjlxDm")
    private String jbrsfzjlxDm;

    /**
     * 经办人身份证件号码
     */
    @Schema(description = "经办人身份证件号码", requiredMode = Schema.RequiredMode.REQUIRED, example = "510522199907122342")
    @JsonProperty(value = "jbrsfzjhm")
    private String jbrsfzjhm;

    public JcbwVO() {
    }
}
