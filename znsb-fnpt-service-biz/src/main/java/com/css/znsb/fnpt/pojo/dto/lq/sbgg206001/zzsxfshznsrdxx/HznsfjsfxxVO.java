package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.zzsxfshznsrdxx;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "汇总纳税分支机构税费信息")
@Data
public class HznsfjsfxxVO implements Serializable {

    /**
     * 被汇总人登记序号
     */
    @Schema(description = "被汇总人登记序号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10116101000052508218")
    @JsonProperty(value = "bhzrdjxh")
    private String bhzrdjxh;

    /**
     * 被汇总纳税企业纳税人识别号
     */
    @Schema(description = "被汇总纳税企业纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11111")
    @JsonProperty(value = "bhznsqynsrsbh")
    private String bhznsqynsrsbh;

    /**
     * 被汇总纳税企业纳税人名称
     */
    @Schema(description = "被汇总纳税企业纳税人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "11111")
    @JsonProperty(value = "bhznsqynsrmc")
    private String bhznsqynsrmc;

    /**
     * 征收项目代码
     */
    @Schema(description = "征收项目代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "10101")
    @JsonProperty(value = "zsxmDm")
    private String zsxmDm;

    /**
     * 征收品目代码
     */
    @Schema(description = "征收品目代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1010100002")
    @JsonProperty(value = "zspmDm")
    private String zspmDm;

    /**
     * 税率
     */
    @Schema(description = "税率", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.03")
    @JsonProperty(value = "sl")
    private BigDecimal sl;

    public HznsfjsfxxVO() {
    }
}
