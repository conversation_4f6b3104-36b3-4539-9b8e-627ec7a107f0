package com.css.znsb.fnpt.service.openApi.lq.impl.ggnl;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.sbmx.SbmxReqDTO;
import com.css.znsb.fnpt.service.openApi.lq.LqParameterHandleService;
import com.css.znsb.framework.common.util.json.JsonUtils;
import org.springframework.stereotype.Service;

@Service("GgnlCxsbmxServiceImpl")
public class GgnlCxsbmxServiceImpl implements LqParameterHandleService {


    @Override
    public Object preHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, SbmxReqDTO.class);
    }

    @Override
    public String afterHandle(String jsonStr) {
        return jsonStr;
    }
}
