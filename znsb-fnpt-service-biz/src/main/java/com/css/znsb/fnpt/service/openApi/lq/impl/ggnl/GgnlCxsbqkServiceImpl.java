package com.css.znsb.fnpt.service.openApi.lq.impl.ggnl;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.sbqk.SbqkReqDTO;
import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.sbqk.SbqkResDTO;
import com.css.znsb.fnpt.service.openApi.lq.LqParameterHandleService;
import com.css.znsb.framework.common.util.json.JsonUtils;
import org.springframework.stereotype.Service;

@Service("GgnlCxsbqkServiceImpl")
public class GgnlCxsbqkServiceImpl implements LqParameterHandleService {


    @Override
    public Object preHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, SbqkReqDTO.class);
    }

    @Override
    public Object afterHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, SbqkResDTO.class);
    }
}
