package com.css.znsb.fnpt.service.openApi.lq.impl.ggnl;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.bqsfkxsxe.BqsfkxsxeReqDTO;
import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.bqsfkxsxe.BqsfkxsxeResDTO;
import com.css.znsb.fnpt.service.openApi.lq.LqParameterHandleService;
import com.css.znsb.framework.common.util.json.JsonUtils;
import org.springframework.stereotype.Service;

@Service("GgnlCxbqkxsxeServiceImpl")
public class GgnlCxbqkxsxeServiceImpl implements LqParameterHandleService {


    @Override
    public Object preHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, BqsfkxsxeReqDTO.class);
    }

    @Override
    public Object afterHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, BqsfkxsxeResDTO.class);
    }
}
