package com.css.znsb.fnpt.service.jyss.ypss;

import cn.hutool.core.util.IdUtil;
import com.css.znsb.fnpt.enums.*;
import com.css.znsb.fnpt.feign.JyssApi;
import com.css.znsb.fnpt.pojo.domain.mhzc.ZnsbMhzcQybqxxDO;
import com.css.znsb.fnpt.pojo.domain.mhzc.ZnsbMhzcQyjbxxmxDO;
import com.css.znsb.fnpt.pojo.dto.yp.DeductionRequest;
import com.css.znsb.fnpt.pojo.dto.yp.DeductionSpxx;
import com.css.znsb.fnpt.pojo.jyss.yp.Qybq;
import com.css.znsb.fnpt.pojo.jyss.yp.SbzbRequest;
import com.css.znsb.fnpt.pojo.jyss.yp.SbzbResponse;
import com.css.znsb.fnpt.pojo.jyss.yp.YpssRequest;
import com.css.znsb.fnpt.pojo.jyss.yp.jxfp.JxfpJjdjDTO;
import com.css.znsb.fnpt.util.NsrxxUtils;
import com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class YpssServiceImpl implements YpssService{

    @Resource
    private JyssApi jyssApi;

    @Override
    public String handle(DeductionRequest request) {
        String fplx = FplxEnum.getFplxByOpenFplx(request.getFplx());

        Map<String, String> skssqRange = NsrxxUtils.getSkssqRange(request.getNsrsbh(), request.getSkssq(), "10101");
        final String skssqq = skssqRange.get("skssqq");
        final String skssqz = skssqRange.get("skssqz");

        List<ZnsbMhzcQyjbxxmxDO> qyjbxx = NsrxxUtils.getQyjbxxByNsrsbh(request.getNsrsbh());
        final Qybq qybq = new Qybq();
        qybq.setQydSsYwxwfl(QydSsYwxwflEnum.KSPZGX.getDm());

        final YpssRequest reqDTO = new YpssRequest();
        reqDTO.setSkssqq(skssqq);
        reqDTO.setSkssqz(skssqz);
        reqDTO.setUuid(IdUtil.fastSimpleUUID());
        reqDTO.setHyDm(qyjbxx.get(0).getHyDm());
        reqDTO.setQybq(qybq);
        reqDTO.setDjxh(request.getDjxh());
        reqDTO.setFphm(request.getFphm());
        reqDTO.setSe(request.getYxdkse());
        reqDTO.setXse(request.getYxdkje());
        if (FplxEnum.HGJKS.getFplx().equals(fplx)){
            DeductionSpxx deductionSpxx = request.getSpxx().get(0);
            reqDTO.setSsywflDm("0209");
            reqDTO.setSfjzjtbz(deductionSpxx.getJzjtbz());
            reqDTO.setYspzlxDm("FPPZ015");
            reqDTO.setYqkszzszyfpbz(deductionSpxx.getYqrzbz());
            reqDTO.setGxdklx(request.getHxyt());
        } else if (FplxEnum.DKDJ.getFplx().equals(fplx)) {
            DeductionSpxx deductionSpxx = request.getSpxx().get(0);
            reqDTO.setSsywflDm("0209");
            reqDTO.setQybq(qybq);
            reqDTO.setSfjzjtbz(deductionSpxx.getJzjtbz());
            reqDTO.setYspzlxDm("FPPZ016");
            reqDTO.setYqkszzszyfpbz(deductionSpxx.getYqrzbz());
            reqDTO.setGxdklx(request.getHxyt());
        } else {
            reqDTO.setTdyslxDm(request.getTdyslxDm());
            reqDTO.setGxdklx(HxytDmEnum.CKTS.getHxytDm().equals(request.getHxyt())?"2":request.getHxyt());//ckts类型需要转换为2

            final List<YpssRequest.Spxx> spxxList = new ArrayList<>();
            request.getSpxx().forEach(mx -> {
                final YpssRequest.Spxx spxx = new YpssRequest.Spxx();

                String spbm = mx.getSpbm();
                //计算商品偏码
                String sppm = "";
                //计算商编篇码
                String sbpm = "";
                if (!GyUtils.isNull(mx.getSpbm())) {
                    if (spbm.startsWith("1")) {
                        sppm = "01";
                        sbpm = "1";
                    } else if (spbm.startsWith("2")) {
                        sppm = "02";
                        sbpm = "2";
                    } else if (spbm.startsWith("3")) {
                        sppm = "03";
                        sbpm = "3";
                    } else if (spbm.startsWith("4")) {
                        sppm = "04";
                        sbpm = "4";
                    } else if (spbm.startsWith("5")) {
                        sppm = "05";
                        sbpm = "5";
                    } else if (spbm.startsWith("6")) {
                        sppm = "06";
                        sbpm = "6";
                    }
                }

                //写入商品信息
                spxx.setSe(mx.getSe());
                spxx.setXse(mx.getXse());
                spxx.setSpbm(spbm);
                spxx.setSpmc(mx.getSpmc());
                spxx.setSppm(sppm);
                spxx.setSbpm(sbpm);
                spxx.setSfjzjt(mx.getJzjtbz());
                spxx.setYqkszzszyfpbz(mx.getYqrzbz());

                spxxList.add(spxx);
            });

            reqDTO.setSpxxList(spxxList);
            boolean tpfFlag = false;
            for (YpssRequest.Spxx spxx:spxxList) {
                if ("3049900000000000000".equals(spxx.getSpbm())) {
                    tpfFlag = true;
                    break;
                }
            }

            if ((Arrays.asList("81","82","83","84","85","86","87","88","51","61").contains(fplx)
                    && Arrays.asList("12","16").contains(request.getTdyslxDm()))
                    || (Arrays.asList("01","02","03","04","08","10","11","14","15").contains(fplx)
                    && Arrays.asList("01","02").contains(request.getTdyslxDm()))) {
                //农产品固定为FPHZ011
                //数电票：特定要素类型代码为12农产品销售发票、16农产品收购
                //税控票：特殊票种为01农产品销售、02农产品收购
                reqDTO.setYspzlxDm("FPHZ011");
            }

            if (tpfFlag) {
                reqDTO.setYspzlxDm(SsxwYwflEnum.getSsxwYwflEnum(null, "FPFS-81").getYspzlxDm());
            } else {
                reqDTO.setYspzlxDm(SsxwYwflEnum.getSsxwYwflEnum(null, "FPFS-" + fplx).getYspzlxDm());
            }
        }

        //不为代扣代缴、海关缴款书、旅客运输发票的进项发票，执行代扣代缴分支
        if (!Arrays.asList(FplxEnum.HGJKS.getFplx(),FplxEnum.DKDJ.getFplx(),"51","61").contains(fplx)) {
            //加计抵减判断
            final List<ZnsbMhzcQybqxxDO> nsrbqxx = NsrxxUtils.getQybqxxByNsrsbh(request.getNsrsbh());
            boolean jjdjFlag = false;
            boolean jzjtFlag = false;
            BigDecimal jjdjl = BigDecimal.ZERO;
            for (ZnsbMhzcQybqxxDO bqUnit:nsrbqxx){
                if (bqUnit.getBqmc().contains(NsrbqEnum.ZZSJJDJ.getCode())){
                    qybq.setSyjjdjzcqybz("Y");
                    jjdjl = bqUnit.getJjdjl();
                    jjdjFlag = true;
                }
                if (bqUnit.getBqmc().contains(NsrbqEnum.GYMJ.getCode())) {
                    qybq.setGymjjjdjqybz("Y");
                    jjdjl = bqUnit.getJjdjl();
                    jjdjFlag = true;
                }
                if (bqUnit.getBqmc().contains(NsrbqEnum.XJZZY.getCode())) {
                    qybq.setXjzzjjdjqybz("Y");
                    jjdjl = bqUnit.getJjdjl();
                    jjdjFlag = true;
                }
                if (bqUnit.getBqmc().contains(NsrbqEnum.JCDL.getCode())) {
                    qybq.setJcdljjdjqybz("Y");
                    jjdjl = bqUnit.getJjdjl();
                    jjdjFlag = true;
                }
                if (bqUnit.getBqmc().contains(NsrbqEnum.ZZSJZJT.getCode())){
                    jzjtFlag = true;
                }
            }

            if (jjdjFlag) {
                //构建算税DTO
                final SbzbRequest jjdjReqDTO = new SbzbRequest();

                //写入补录内容
                final JxfpJjdjDTO blnrDTO = new JxfpJjdjDTO();
                blnrDTO.setDjxh(request.getDjxh());
                blnrDTO.setNsrsbh(request.getNsrsbh());
                blnrDTO.setJjdjl(GyUtils.isNull(jjdjl)?"":jjdjl.toPlainString());
                blnrDTO.setSfjzjtbz(jzjtFlag?"Y":"N");
                blnrDTO.setSe(request.getYxdkse());
                blnrDTO.setYxbz("Y");
                blnrDTO.setNsjctzlx("1");//1:加计抵减 2:加计调减
                blnrDTO.setAzzsjjdjkm("1");//1:加计抵减 2:加计调减
                blnrDTO.setLrrq(new Date());
                blnrDTO.setLrrsfid("SYS");
                blnrDTO.setXgrq(new Date());
                blnrDTO.setXgrsfid("SYS");

                //写入基础参数
                jjdjReqDTO.setDjxh(request.getDjxh());
                jjdjReqDTO.setSspzhm(IdUtil.fastSimpleUUID());
                jjdjReqDTO.setYwfsrq(DateUtils.getSystemCurrentTime(3));
                jjdjReqDTO.setSkssqq(skssqq);
                jjdjReqDTO.setSkssqz(skssqz);
                jjdjReqDTO.setXwgzlxDm("0");
                jjdjReqDTO.setZsxmDm("10101");
                jjdjReqDTO.setSsywflDm("0568");//0568（加计抵减税额）
                jjdjReqDTO.setQybq(qybq);
                jjdjReqDTO.setBlnr(JsonUtils.toJson(Collections.singletonList(blnrDTO)));

                try {
                    final CommonResult<SbzbResponse> jjdjResult = jyssApi.sbzb(jjdjReqDTO);
                    log.info("申报准备响应:{}",jjdjResult);
                } catch (Exception e) {
                    log.error("开票算税报错",e);
                    throw new RuntimeException(e);
                }
            }
        }

        try {
            final CommonResult<SbzbResponse> result = jyssApi.ypss(reqDTO);
            log.info("用票算税响应:{}",result);
            Integer code = result.getCode();
            if (GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode().equals(code)){
                log.error("用票算税报错-{}",result.getMsg());
                throw new RuntimeException("算税异常");
            }
            return "算税成功";
        } catch (Exception e) {
            log.error("用票算税报错",e);
            throw new RuntimeException(e);
        }

    }
}
