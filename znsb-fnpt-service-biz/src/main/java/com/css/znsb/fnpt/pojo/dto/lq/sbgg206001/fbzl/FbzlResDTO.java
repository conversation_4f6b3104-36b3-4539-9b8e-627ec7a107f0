package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.fbzl;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "附报资料上传")
@Data
public class FbzlResDTO {

    @Schema(description = "资料uuid",requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "3af111c63c0d49f5b2efea9223be3754")
    @JsonProperty("zluuid")
    private String zluuid;
}
