package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.bqsfkxsxe;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 本期税费可享受限额VO
 * <p>
 * 序号	数据项名称	          数据项标识符	  是否可空	字段类型	  长度	  备注
 * 1	    税款所属期起	      skssqq	    N	    DATE		      格式：YYYY-MM-DD
 * 2	    税款所属期止	      skssqz	    N	    DATE		      格式：YYYY-MM-DD
 * 3	    本属期可享受限额	  bsqkxsxe	    Y	    NUMBER	  18,2	  本属期可享受限额=期初扣减限额+本期扣减限额
 * 4	    期初扣减限额	      qckjxz	    Y	    NUMBER	  18,2
 * 5	    本期扣减限额	      bqkjxz	    Y	    NUMBER	  18,2
 * 6	    减免性质代码	      jmxzDm	    Y	    VARCHAR	  10
 * 7	    税务事项代码	      swsxDm	    Y	    CHAR	    12
 */
@Schema(description = "本期税费可享受限额")
@Data
public class BsqkxsxeVO implements Serializable {

    /**
     * 税款所属期起
     */
    @Schema(description = "税款所属期起", requiredMode = Schema.RequiredMode.REQUIRED, example = "2019-01-01")
    @JsonProperty(value = "skssqq")
    private String skssqq;

    /**
     * 税款所属期止
     */
    @Schema(description = "税款所属期止", requiredMode = Schema.RequiredMode.REQUIRED, example = "2027-12-31")
    @JsonProperty(value = "skssqz")
    private String skssqz;

    /**
     * 本属期可享受限额
     */
    @Schema(description = "本属期可享受限额", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0")
    @JsonProperty(value = "bsqkxsxe")
    private BigDecimal bsqkxsxe;

    /**
     * 期初扣减限额
     */
    @Schema(description = "期初扣减限额", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0")
    @JsonProperty(value = "qckjxz")
    private BigDecimal qckjxz;

    /**
     * 本期扣减限额
     */
    @Schema(description = "本期扣减限额", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0")
    @JsonProperty(value = "bqkjxz")
    private BigDecimal bqkjxz;

    /**
     * 减免性质代码
     */
    @Schema(description = "减免性质代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0001011814")
    @JsonProperty(value = "jmxzDm")
    private String jmxzDm;

    /**
     * 税务事项代码
     */
    @Schema(description = "税务事项代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "SXA031900832")
    @JsonProperty(value = "swsxDm")
    private String swsxDm;

    public BsqkxsxeVO() {
    }
}
