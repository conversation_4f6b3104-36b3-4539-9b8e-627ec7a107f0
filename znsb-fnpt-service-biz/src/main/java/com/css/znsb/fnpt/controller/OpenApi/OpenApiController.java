package com.css.znsb.fnpt.controller.OpenApi;


import com.css.znsb.fnpt.pojo.dto.openApi.ExpandInfo;
import com.css.znsb.fnpt.pojo.dto.openApi.OpenApiRequest;
import com.css.znsb.fnpt.service.openApi.OpenApiService;
import com.css.znsb.fnpt.service.openApi.lq.LqCommonService;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.ioc.SpringIocUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Tag(name = "赋能平台开放接口")
@RestController
@RequestMapping("/openApi")
public class OpenApiController {

    @Resource
    private LqCommonService lqCommonService;

    @PostMapping("/v1")
    public CommonResult<String> openApi(@RequestBody OpenApiRequest request){
        ExpandInfo expand = request.getExpand();
        String serviceCode = expand.getServiceCode();
        Map<String, Object> tableData = CacheUtils.getTableData("cs_fnpt_fwdz", serviceCode);
        String serviceName = (String) tableData.get("dzfwbm");
        String ywlx = (String) tableData.get("ywlx");
        if ("JYSS".equals(ywlx)){
            OpenApiService openApiService = SpringIocUtils.getBean(serviceName, OpenApiService.class);
            return openApiService.handle(request);
        }else {
            return lqCommonService.handle(request);
        }
    }

}
