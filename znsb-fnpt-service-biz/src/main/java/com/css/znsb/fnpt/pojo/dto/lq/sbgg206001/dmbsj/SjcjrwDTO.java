package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.dmbsj;

import com.css.znsb.framework.common.pojo.ExtendInfoDTO;
import lombok.Data;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.nssb.pojo.dto.sjcj.config
 * @file SjcjrwDTO.java 创建时间:2024/5/8 21:17
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 智能申报
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Data
public class SjcjrwDTO extends ExtendInfoDTO {
    private String rwbm1;
    private String rwsm;
    private String rwlxDm;
    private String ywdlbm;
    private String ywzlbm;
    private String ywxlbm;
    private String smzqbm;
    private String jkdz;
    private String bcbz;
    private String topic;
    private String yxbz;
}
