package com.css.znsb.fnpt.service.openApi.lq.impl.ggnl;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.zfssjgxx.ZfssjgxxReqDTO;
import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.zfssjgxx.ZfssjgxxResDTO;
import com.css.znsb.fnpt.service.openApi.lq.LqParameterHandleService;
import com.css.znsb.framework.common.util.json.JsonUtils;
import org.springframework.stereotype.Service;

@Service("GgnlZfssjgxxServiceImpl")
public class GgnlZfssjgxxServiceImpl implements LqParameterHandleService {


    @Override
    public Object preHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, ZfssjgxxReqDTO.class);
    }

    @Override
    public Object afterHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, ZfssjgxxResDTO.class);
    }
}
