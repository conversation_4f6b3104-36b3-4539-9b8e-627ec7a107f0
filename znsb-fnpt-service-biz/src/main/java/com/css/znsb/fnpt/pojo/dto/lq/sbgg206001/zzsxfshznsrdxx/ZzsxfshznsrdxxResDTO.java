package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.zzsxfshznsrdxx;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.nsrxx.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "增值税、消费税汇总纳税认定信息")
@Data
public class ZzsxfshznsrdxxResDTO {

    @Schema(description = "登记序号",requiredMode = Schema.RequiredMode.REQUIRED, example = "1111")
    @JsonProperty("djxh")
    private String djxh;

    @Schema(description = "纳税人识别号（社会信用代码）",requiredMode = Schema.RequiredMode.REQUIRED, example = "222222")
    @JsonProperty("nsrsbh")
    private String nsrsbh;

    @Schema(description = "汇总纳税企业认定申请明细信息表-被汇总纳税人信息",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("hznsqyrdsqmxbGrid")
    private List<HznsqyrdsqmxxxbBhznsrxxVO> hznsqyrdsqmxbGrid;

    @Schema(description = "汇总纳税企业附加税费信息",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("hznsqyfjsfxxGrid")
    private List<HznsfjsfxxVO> hznsqyfjsfxxGrid;
}
