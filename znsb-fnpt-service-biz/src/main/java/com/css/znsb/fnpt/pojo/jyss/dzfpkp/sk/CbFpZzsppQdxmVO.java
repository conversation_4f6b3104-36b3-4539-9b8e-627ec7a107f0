package com.css.znsb.fnpt.pojo.jyss.dzfpkp.sk;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 税控增值税普票清单明细表
 */
@Data
public class CbFpZzsppQdxmVO {

    private String fpdm;
    private String fphm;
    private int mxxh;
    private String mc;
    private String ggxh;
    private String jldw;
    private String sl;
    private String dj;
    private BigDecimal je;
    private BigDecimal slv;
    private BigDecimal se;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSSSSS",timezone="GMT+8")
    private Date jssj;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSSSSS",timezone="GMT+8")
    private Date kprq;
    private String spbm;
    private String zxbm;
    private String yhzcbs;
    private String zzstsgl;
    private String lslbs;
    private String wplb;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSSSSS",timezone="GMT+8")
    private Date sjtbSj;
    private int tbzt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSSSSS",timezone="GMT+8")
    private Date xczxsj;
    private int cscs;

    /**
     * 征收项目代码
     */
    private String zsxmDm;

    /**
     * 征收子目代码
     */
    private String zszmDm;

    /**
     * 征收品目代码
     */
    private String zspmDm;
}
