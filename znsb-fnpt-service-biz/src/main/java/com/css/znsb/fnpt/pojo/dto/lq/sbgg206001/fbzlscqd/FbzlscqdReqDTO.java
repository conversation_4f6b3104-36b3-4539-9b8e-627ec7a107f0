package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.fbzlscqd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "附报资料上传清单请求")
@Data
public class FbzlscqdReqDTO {

    @Schema(description = "税务事项代码",requiredMode = Schema.RequiredMode.REQUIRED, example = "SXA061001034")
    private String swsxDm;

    @Schema(description = "减免性质代码列表",requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[\"0001124302\"]")
    private List<String> swsxDmlb;
}
