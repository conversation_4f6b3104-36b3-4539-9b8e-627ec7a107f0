package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.fbzlscqd;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 附列资料VO
 * <p>
 * 序号	数据项名称	                数据项标识符	      是否可空	字段类型	  长度	    备注
 * 1	    税务事项代码	            swsxDm	        N	    VARCHAR	  12
 * 2	    附列资料代码	            flzlDm	        N	    VARCHAR	  10
 * 3	    附列资料名称	            flzlmc	        N	    VARCHAR	  1500
 * 4	    附列资料uuid	            flzluuid	      N	    VARCHAR	  32
 * 5	    附列资料报送类型代码	      flzlbslxDm	    N	    VARCHAR	  1	      1：必报；2：条件报送；3：可容缺。
 * 6	    附列资料报送类型名称	      flzlbslxmc	    N	    VARCHAR	  150
 * 7	    附列资料处理类型代码	      flzlcllxDm	    N	    VARCHAR	  1
 * 8	    附列资料处理类型名称	      flzlcllxmc	    N	    VARCHAR	  150
 * 9	    办税份数	              bsfs	          N	    INT	    2
 * 10	  有效期起	              yxqq	          N	    DATE		        格式：YYYY-MM-DD
 * 11	  有效期止	              yxqz	          N	    DATE		        格式：YYYY-MM-DD
 * 12	  有效标志	              yxbz	          N	    CHAR	    1
 */
@Schema(description = "附列资料")
@Data
public class FhsjVO implements Serializable {

    /**
     * 税务事项代码
     */
    @Schema(description = "税务事项代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "SXA061001034")
    @JsonProperty(value = "swsxDm")
    private String swsxDm;

    /**
     * 附列资料代码
     */
    @Schema(description = "附列资料代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "003360")
    @JsonProperty(value = "flzlDm")
    private String flzlDm;

    /**
     * 附列资料名称
     */
    @Schema(description = "附列资料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "《外国政府和国际组织无偿援助项目在华采购货物明细表》")
    @JsonProperty(value = "flzlmc")
    private String flzlmc;

    /**
     * 附列资料uuid
     */
    @Schema(description = "附列资料uuid", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @JsonProperty(value = "flzluuid")
    private String flzluuid;

    /**
     * 附列资料报送类型代码 || 1：必报；2：条件报送；3：可容缺
     */
    @Schema(description = "附列资料报送类型代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @JsonProperty(value = "flzlbslxDm")
    private String flzlbslxDm;

    /**
     * 附列资料报送类型名称
     */
    @Schema(description = "附列资料报送类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "必报")
    @JsonProperty(value = "flzlbslxmc")
    private String flzlbslxmc;

    /**
     * 附列资料处理类型代码
     */
    @Schema(description = "附列资料处理类型代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @JsonProperty(value = "flzlcllxDm")
    private String flzlcllxDm;

    /**
     * 附列资料处理类型名称
     */
    @Schema(description = "附列资料处理类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "归档")
    @JsonProperty(value = "flzlcllxmc")
    private String flzlcllxmc;

    /**
     * 办税份数
     */
    @Schema(description = "办税份数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @JsonProperty(value = "bsfs")
    private BigDecimal bsfs;

    /**
     * 有效期起
     */
    @Schema(description = "有效期起", requiredMode = Schema.RequiredMode.REQUIRED, example = "2000-01-01")
    @JsonProperty(value = "yxqq")
    private String yxqq;

    /**
     * 有效期止
     */
    @Schema(description = "有效期止", requiredMode = Schema.RequiredMode.REQUIRED, example = "2099-12-31")
    @JsonProperty(value = "yxqz")
    private String yxqz;

    /**
     * 有效标志
     */
    @Schema(description = "有效标志", requiredMode = Schema.RequiredMode.REQUIRED, example = "Y")
    @JsonProperty(value = "yxbz")
    private String yxbz;

    public FhsjVO() {
    }
}
