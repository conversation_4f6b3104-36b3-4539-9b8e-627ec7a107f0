package com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.zzsxfshznsrdxx;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "汇总纳税信息", example = "汇总纳税信息示例")
@Data
public class HznsqyrdsqmxxxbBhznsrxxVO implements Serializable {

    /**
     * 汇总纳税明细UUID
     */
    @Schema(description = "汇总纳税明细UUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1111111")
    @JsonProperty(value = "hznsmxuuid")
    private String hznsmxuuid;

    /**
     * 汇总纳税UUID
     */
    @Schema(description = "汇总纳税UUID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11111111111")
    @JsonProperty(value = "hznsuuid")
    private String hznsuuid;

    /**
     * 登记序号
     */
    @Schema(description = "登记序号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10116101000051261000")
    @JsonProperty(value = "djxh")
    private String djxh;

    /**
     * 征收项目代码
     */
    @Schema(description = "征收项目代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "10101")
    @JsonProperty(value = "zsxmDm")
    private String zsxmDm;

    /**
     * 被汇总纳税人是否申请标志
     */
    @Schema(description = "被汇总纳税人是否申请标志", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Y")
    @JsonProperty(value = "bhznsrsfsqbz")
    private String bhznsrsfsqbz;

    /**
     * 被汇总人登记序号
     */
    @Schema(description = "被汇总人登记序号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "10116101000052509000")
    @JsonProperty(value = "bhzrdjxh")
    private String bhzrdjxh;

    /**
     * 被汇总纳税企业纳税人识别号
     */
    @Schema(description = "被汇总纳税企业纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED, example = "610197693843042")
    @JsonProperty(value = "bhznsqynsrsbh")
    private String bhznsqynsrsbh;

    /**
     * 被汇总纳税企业纳税人名称
     */
    @Schema(description = "被汇总纳税企业纳税人名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "陕西艺之峰模型有限责任公司第一分公司")
    @JsonProperty(value = "bhznsqynsrmc")
    private String bhznsqynsrmc;

    /**
     * 申报缴纳方式代码
     */
    @Schema(description = "申报缴纳方式代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    @JsonProperty(value = "sbjnfsDm")
    private String sbjnfsDm;

    /**
     * 预征率
     */
    @Schema(description = "预征率", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0.02")
    @JsonProperty(value = "yzl")
    private BigDecimal yzl;

    /**
     * 定额税率
     */
    @Schema(description = "定额税率", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0.05")
    @JsonProperty(value = "desl1")
    private BigDecimal desl1;

    /**
     * 被汇总纳税人原纳税模式
     */
    @Schema(description = "被汇总纳税人原纳税模式", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "一般纳税人")
    @JsonProperty(value = "bhznsrynsms")
    private String bhznsrynsms;

    /**
     * 汇总纳税有效期起
     */
    @Schema(description = "汇总纳税有效期起", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "2009-11-01")
    @JsonProperty(value = "hznsyxqq")
    private String hznsyxqq;

    /**
     * 汇总纳税有效期止
     */
    @Schema(description = "汇总纳税有效期止", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "9999-12-31")
    @JsonProperty(value = "hznsyxqz")
    private String hznsyxqz;

    /**
     * 被汇总纳税企业地址
     */
    @Schema(description = "被汇总纳税企业地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "西安市雁塔区幸福南路西侧")
    @JsonProperty(value = "bhznsqydz")
    private String bhznsqydz;

    /**
     * 被汇总纳税企业登记注册类型代码
     */
    @Schema(description = "被汇总纳税企业登记注册类型代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "159")
    @JsonProperty(value = "bhznsqydjzclxDm")
    private String bhznsqydjzclxDm;

    /**
     * 被汇总纳税企业适用税率
     */
    @Schema(description = "被汇总纳税企业适用税率", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0.13")
    @JsonProperty(value = "bhznsqysysl")
    private BigDecimal bhznsqysysl;

    /**
     * 被汇总纳税企业是否为分支机构标志
     */
    @Schema(description = "被汇总纳税企业是否为分支机构标志", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Y")
    @JsonProperty(value = "bhznsqysfwfzjgbz")
    private String bhznsqysfwfzjgbz;

    /**
     * 汇总纳税范围代码
     */
    @Schema(description = "汇总纳税范围代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "4")
    @JsonProperty(value = "hznsfwDm")
    private String hznsfwDm;

    /**
     * 有效标志
     */
    @Schema(description = "有效标志", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Y")
    @JsonProperty(value = "yxbz")
    private String yxbz;

    /**
     * 录入日期
     */
    @Schema(description = "录入日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2009-12-02")
    @JsonProperty(value = "lrrq")
    private String lrrq;

    /**
     * 修改日期
     */
    @Schema(description = "修改日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "2009-12-02")
    @JsonProperty(value = "xgrq")
    private String xgrq;

    /**
     * 数据同步时间
     */
    @Schema(description = "数据同步时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "2023-06-01 12:00:00")
    @JsonProperty(value = "sjtbSj")
    private String sjtbSj;

    /**
     * 预征比例
     */
    @Schema(description = "预征比例", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0.02")
    @JsonProperty(value = "yzbl")
    private BigDecimal yzbl;

    /**
     * 汇总企业类型代码
     */
    @Schema(description = "汇总企业类型代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "05")
    @JsonProperty(value = "hzqylxDm")
    private String hzqylxDm;

    /**
     * 营改增纳税人类型代码
     */
    @Schema(description = "营改增纳税人类型代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "22")
    @JsonProperty(value = "ygznsrlxDm")
    private String ygznsrlxDm;

    /**
     * 预征税款范围代码
     */
    @Schema(description = "预征税款范围代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "4")
    @JsonProperty(value = "yzskfwDm")
    private String yzskfwDm;

    /**
     * 是否连锁企业
     */
    @Schema(description = "是否连锁企业", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "N")
    @JsonProperty(value = "sflsqy")
    private String sflsqy;

    /**
     * 纳税人状态代码
     */
    @Schema(description = "纳税人状态代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "05")
    @JsonProperty(value = "nsrztDm")
    private String nsrztDm;

    /**
     * 被汇总数据来源类型
     */
    @Schema(description = "被汇总数据来源类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "")
    @JsonProperty(value = "bhzsjlylx")
    private String bhzsjlylx;

    /**
     * 外省登记序号
     */
    @Schema(description = "外省登记序号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0")
    @JsonProperty(value = "wsdjxh")
    private BigDecimal wsdjxh;

    /**
     * 省行政区划代码
     */
    @Schema(description = "省行政区划代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "310000")
    @JsonProperty(value = "sxzqhDm")
    private String sxzqhDm;

    public HznsqyrdsqmxxxbBhznsrxxVO() {
    }
}
