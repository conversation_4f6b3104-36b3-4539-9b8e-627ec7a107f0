package com.css.znsb.fnpt.pojo.vo.xgmnsr.lq.queryJyss;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(name ="增值税纳税申报表（小规模纳税人适用）附列资料")
@Data
public class SsSbytZzsXgmFbFlzlVO {

    /**
     * 期初余额
     */
    @Schema(name = "期初余额")
    @JsonProperty(value = "qcye")
    private BigDecimal qcye;

    /**
     * 本期发生额
     */
    @Schema(name = "本期发生额")
    @JsonProperty(value = "bqfse")
    private BigDecimal bqfse;

    /**
     * 本期扣除额
     */
    @Schema(name = "本期扣除额")
    @JsonProperty(value = "bqkce")
    private BigDecimal bqkce;

    /**
     * 期末余额
     */
    @Schema(name = "期末余额")
    @JsonProperty(value = "qmye")
    private BigDecimal qmye;

    /**
     * 全部含税收入
     */
    @Schema(name = "全部含税收入")
    @JsonProperty(value = "ysfwxsqbhssr")
    private BigDecimal ysfwxsqbhssr;

    /**
     * 不含税销售额
     */
    @Schema(name = "不含税销售额")
    @JsonProperty(value = "ysfwxsbhsxse")
    private BigDecimal ysfwxsbhsxse;

    /**
     * 含税销售额
     */
    @Schema(name = "含税销售额")
    @JsonProperty(value = "ysfwxshsxse")
    private BigDecimal ysfwxshsxse;

    /**
     * 期初余额（5%征收率）
     */
    @Schema(name = "期初余额（5%征收率）")
    @JsonProperty(value = "qcye5")
    private BigDecimal qcye5;

    /**
     * 本期扣除额（5%征收率）
     */
    @Schema(name = "本期扣除额（5%征收率）")
    @JsonProperty(value = "bqkce5")
    private BigDecimal bqkce5;

    /**
     * 期末余额（5%征收率）
     */
    @Schema(name = "期末余额（5%征收率）")
    @JsonProperty(value = "qmye5")
    private BigDecimal qmye5;

    /**
     * 含税销售额（5%征收率）
     */
    @Schema(name = "含税销售额（5%征收率）")
    @JsonProperty(value = "ysfwxshsxse5")
    private BigDecimal ysfwxshsxse5;

    /**
     * 不含税销售额（5%征收率）
     */
    @Schema(name = "不含税销售额（5%征收率）")
    @JsonProperty(value = "ysfwxsbhsxse5")
    private BigDecimal ysfwxsbhsxse5;

    /**
     * 全部含税收入（5%征收率）
     */
    @Schema(name = "全部含税收入（5%征收率）")
    @JsonProperty(value = "ysfwxsqbhssr5")
    private BigDecimal ysfwxsqbhssr5;

    /**
     * 本期发生额（5%征收率）
     */
    @Schema(name = "本期发生额（5%征收率）")
    @JsonProperty(value = "bqfse5")
    private BigDecimal bqfse5;

}
