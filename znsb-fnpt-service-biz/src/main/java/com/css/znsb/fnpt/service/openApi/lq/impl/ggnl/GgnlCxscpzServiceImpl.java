package com.css.znsb.fnpt.service.openApi.lq.impl.ggnl;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.scpzxx.ScpzxxReqDTO;
import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.scpzxx.ScpzxxResDTO;
import com.css.znsb.fnpt.service.openApi.lq.LqParameterHandleService;
import com.css.znsb.framework.common.util.json.JsonUtils;
import org.springframework.stereotype.Service;

@Service("GgnlCxscpzServiceImpl")
public class GgnlCxscpzServiceImpl implements LqParameterHandleService {


    @Override
    public Object preHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, ScpzxxReqDTO.class);
    }

    @Override
    public Object afterHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, ScpzxxResDTO.class);
    }
}
