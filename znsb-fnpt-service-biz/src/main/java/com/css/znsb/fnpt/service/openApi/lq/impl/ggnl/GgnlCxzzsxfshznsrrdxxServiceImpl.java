package com.css.znsb.fnpt.service.openApi.lq.impl.ggnl;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.zzsxfshznsrdxx.ZzsxfshznsrdxxReqDTO;
import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.zzsxfshznsrdxx.ZzsxfshznsrdxxResDTO;
import com.css.znsb.fnpt.service.openApi.lq.LqParameterHandleService;
import com.css.znsb.framework.common.util.json.JsonUtils;
import org.springframework.stereotype.Service;

@Service("GgnlCxzzsxfshznsrrdxxServiceImpl")
public class GgnlCxzzsxfshznsrrdxxServiceImpl implements LqParameterHandleService {


    @Override
    public Object preHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, ZzsxfshznsrdxxReqDTO.class);
    }

    @Override
    public Object afterHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, ZzsxfshznsrdxxResDTO.class);
    }
}
