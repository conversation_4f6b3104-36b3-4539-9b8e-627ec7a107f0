package com.css.znsb.fnpt.service.openApi.lq.impl.ggnl;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.dmbsj.DmbsjReqDTO;
import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.dmbsj.SjcjSjjhBwnrDTO;
import com.css.znsb.fnpt.service.openApi.lq.LqParameterHandleService;
import com.css.znsb.framework.common.util.json.JsonUtils;
import org.springframework.stereotype.Service;

@Service("GgnlCxdmbsjServiceImpl")
public class GgnlCxdmbsjServiceImpl implements LqParameterHandleService {


    @Override
    public Object preHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, DmbsjReqDTO.class);
    }

    @Override
    public Object afterHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, SjcjSjjhBwnrDTO.class);
    }
}
