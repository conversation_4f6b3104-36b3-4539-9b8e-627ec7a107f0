package com.css.znsb.fnpt.service.openApi.lq.impl.ggnl;

import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.nsrxx.NsrJbxxReqDTO;
import com.css.znsb.fnpt.pojo.dto.lq.sbgg206001.nsrxx.NsrjbxxResDTO;
import com.css.znsb.fnpt.service.openApi.lq.LqParameterHandleService;
import com.css.znsb.framework.common.util.json.JsonUtils;
import org.springframework.stereotype.Service;

@Service("GgnlCxnsrqtxxServiceImpl")
public class GgnlCxnsrqtxxServiceImpl implements LqParameterHandleService {


    @Override
    public Object preHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, NsrJbxxReqDTO.class);
    }

    @Override
    public Object afterHandle(String jsonStr) {
        return JsonUtils.toBean(jsonStr, NsrjbxxResDTO.class);
    }
}
