package com.css.framework.xxzx.service.xxfb.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.css.framework.qx.entity.XxVO;
import com.css.framework.qx.entity.DdingMsgActionCardVO;
import com.css.framework.qx.entity.DdingMsgActionCardBtnVO;
import com.css.framework.xxzx.dingding.constant.DdptConstants;
import com.css.framework.xxzx.config.TxzxConstant;
import com.css.framework.xxzx.kafka.XxzxKafkaService;
import com.css.framework.xxzx.mapper.xxtx.TxzxXtxxfbMapper;
import com.css.framework.xxzx.mapper.xxtx.XxzxXxfbMapper;
import com.css.framework.xxzx.mapper.xxtx.XxzxXxfbglJsdxMapper;
import com.css.framework.xxzx.mapper.xxtx.XxzxXxfbglMapper;
import com.css.framework.xxzx.mapper.xxtx.XxzxXxfjGxbMapper;
import com.css.framework.xxzx.mapper.xxtx.XxzxXxfjMapper;
import com.css.framework.xxzx.pojo.dto.xx.TxzxXxMbDTO;
import com.css.framework.xxzx.pojo.dto.xx.TxzxZrrXxfbDTO;
import com.css.framework.xxzx.pojo.vo.tjxxmb.TxzxXxMbResVO;
import com.css.framework.xxzx.pojo.vo.tjxxmb.TxzxXxMbVO;
import com.css.framework.xxzx.pojo.vo.xx.GetPcfsjgVO;
import com.css.framework.xxzx.pojo.vo.xxfb.RqDateVO;
import com.css.framework.xxzx.pojo.vo.xxfb.SaveZrrXtxxReqVO;
import com.css.framework.xxzx.pojo.vo.xxfb.ZrrXtxxVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.GetXxfbglDetailsReqVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.GetXxfbglDetailsResVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.ListXxfbglReqVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.ListXxfbglResVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.SaveXxfbglJsOneVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.SaveXxfbglReqVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.UpdateXxfbglFbztReqVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.UpdateXxfbglReqVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.XtxxFbglVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.XtxxXxfbglVo;
import com.css.framework.xxzx.pojo.vo.xxfbgl.XxfbglVO;
import com.css.framework.xxzx.pojo.vo.xxfbgl.XxzxXxfbgl;
import com.css.framework.xxzx.pojo.vo.xxfbgl.XxzxXxfbglJsdx;
import com.css.framework.xxzx.pojo.vo.xxgy.ErrorListVO1;
import com.css.framework.xxzx.pojo.vo.xxgy.ListZrrXtxxResVO;
import com.css.framework.xxzx.pojo.vo.xxgy.ListZrrxtxxReqVO;
import com.css.framework.xxzx.pojo.vo.xxgy.SaveNsrXtxxReqVO;
import com.css.framework.xxzx.pojo.vo.xxgy.TxzxXxYdztVO;
import com.css.framework.xxzx.pojo.vo.xxgy.UpdateNsrXtxxYdztResVO;
import com.css.framework.xxzx.pojo.vo.xxgy.UpdateXtxxYdztReqVO;
import com.css.framework.xxzx.pojo.vo.xxgy.UpdateXtxxYdztResVO;
import com.css.framework.xxzx.pojo.vo.xxgy.UpdateZrrXtxxYdztResVO;
import com.css.framework.xxzx.pojo.vo.xxtx.*;
import com.css.framework.xxzx.pojo.vo.xxtx.wdxx.DeleteGgxxReqVO;
import com.css.framework.xxzx.pojo.vo.xxtx.wdxx.DeleteGgxxResVO;
import com.css.framework.xxzx.pojo.vo.xxtx.wdxx.GetXtxxCountReqVO;
import com.css.framework.xxzx.pojo.vo.xxtx.wdxx.GetXtxxCountResVO;
import com.css.framework.xxzx.pojo.vo.xxtx.wdxx.XxflCountVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxfj.GetFjxxReqVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxfj.GetFjxxResVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxfj.UploadFjReqVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxfj.UploadFjResVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxfj.XxzxXxfjDTO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxfj.XxzxXxfjGxb;
import com.css.framework.xxzx.pojo.vo.xxtx.xxxq.ButtonVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxxq.FjxxVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxxq.GetXxtxDetailsReqVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxxq.GetXxtxDetailsResVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxxq.UpdateYdztDTO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxxq.UpdateYdztReqVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxxq.UpdateYdztResVO;
import com.css.framework.xxzx.pojo.vo.xxtx.xxxq.XxzxXxydztDTO;
import com.css.framework.xxzx.service.channel.IMsgChannelManager;
import com.css.framework.xxzx.service.xxfb.TxzxXtxxfbService;
import com.css.framework.xxzx.service.xxfb.XxzxXxfbService;
import com.css.framework.xxzx.service.xxfb.XxzxXxydztService;
import com.css.framework.xxzx.utils.GYCastUtils;
import com.css.framework.xxzx.utils.TxzxGyUtils;
import com.css.springframework.core.ext.dmcs.service.CssDmcsService;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.template.TemplateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.file.utils.ZnsbFileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @BelongProject:xxzxnsrd-service
 * @BelongPackage:cn.gov.chinatax.gt4.txzx.service.impl
 * <AUTHOR>
 * @CreateTime:2023-02-17 10:52
 * @Descriptiom: 用于控制系统消息（业务系统发布消息）的接入身份校验与消息保存。
 * @Version
 */

@Service
@Slf4j
public class TxzxXtxxfbServiceImpl implements TxzxXtxxfbService {
    @Resource
    private TxzxGyUtils txzxGyUtils;
    @Resource
    private TxzxXtxxfbMapper txzxXtxxfbMapper;
    @Resource
    private XxzxXxydztService xxzxXxydztService;
    @Resource
    private XxzxXxfjMapper xxzxXxfjMapper;
    @Resource
    private XxzxXxfbMapper xxzxXxfbMapper;
    @Resource
    private CssDmcsService cssDmcsService;
    @Resource
    private XxzxKafkaService xxzxKafkaService;
    @Resource
    private XxzxXxfjGxbMapper xxzxXxfjGxbMapper;
    @Resource
    private XxzxXxfbglMapper xxzxXxfbglMapper;
    @Resource
    private XxzxXxfbglJsdxMapper xxzxXxfbglJsdxMapper;
    @Resource
    private IMsgChannelManager msgChannelManager;
    @Resource
    private XxzxXxfbService xxzxXxfbService;


    /**
     * @name 中文名称
     * @description 保存系统消息
     * @time 创建时间:2024年06月03日下午08:33:12
     * @param saveNsrxxtxReqVOList
     * @return {@link ErrorResVO }
     * @throws Exception
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    //@Transactional(rollbackFor = Exception.class)
    public ErrorResVO saveXtxx(SaveXtxxReqVO saveNsrxxtxReqVOList) throws ExecutionException, InterruptedException, TimeoutException {
        ErrorResVO res = new ErrorResVO();
        List<ErrorListVO> erroMessageResVOList = new ArrayList<>();
        log.info("纳税人消息保存逻辑校验开始");
        ErrorListVO vo = checkXtxxCsOne(saveNsrxxtxReqVOList);
        if (!GyUtils.isNull(vo) && !GyUtils.isNull(vo.getErrorMsg())) {
            erroMessageResVOList.add(vo);
        }
        res.setSuccessNum(0);
        res.setErrorList(erroMessageResVOList);
        if (!CollectionUtils.isEmpty(erroMessageResVOList)) {
            return res;
        }

        //获取模板信息
        Object[] xxmbAndNrmbs = txzxGyUtils.getXtxxmbAndNrmbs(saveNsrxxtxReqVOList.getYwxtid(), saveNsrxxtxReqVOList.getXxmbbh());
        if (String.valueOf(xxmbAndNrmbs[0]).equals("2")) {
            res.setErrorList(checkXxmbAndXxnrmb(xxmbAndNrmbs));
            return res;
        }
        Object xxmbObj = xxmbAndNrmbs[2];
        Map<String, Object> xxmb = JSON.parseObject(JSON.toJSONString(xxmbObj), Map.class);
        Object xxnrmbObj = xxmbAndNrmbs[3];
        //List<Map> xxnrmbMapList = JSONArray.parseArray(JSON.toJSONString(xxnrmbObj), Map.class);
        List<Map<String, Object>> xxnrmbMapList = (List<Map<String, Object>>) xxnrmbObj;
        erroMessageResVOList = txzxGyUtils.checkMbCs(xxnrmbMapList, saveNsrxxtxReqVOList);
        if (!CollectionUtils.isEmpty(erroMessageResVOList)) {
            res.setErrorList(erroMessageResVOList);
            return res;
        }
        //获取数据归属地区和数据产生地区
        //Map<String, Object> sjdq = cssDmcsService.getIndexData("CS_MH_XTCS", "MH000000000000002");
        //按消息模板组装xxfb,并保存
        XtxxXxfbVo xxfbVo = checkTxzxXxfb(saveNsrxxtxReqVOList, xxmb, xxnrmbMapList);
        if (!GyUtils.isNull(xxfbVo.getErrorList())) {
            erroMessageResVOList = xxfbVo.getErrorList();
        }
        int saveResult = 0;
        xxfbVo.setXxmb(xxmb);

        if ("Y".equals(saveNsrxxtxReqVOList.getSfhb())){
            xxfbVo.setSfhb(saveNsrxxtxReqVOList.getSfhb());
            //获取模板信息
            Object[] TBXxmbAndNrmbs = txzxGyUtils.getXtxxmbAndNrmbs(saveNsrxxtxReqVOList.getYwxtid(), saveNsrxxtxReqVOList.getXxTbMbbh());
            if (String.valueOf(TBXxmbAndNrmbs[0]).equals("2")) {
                res.setErrorList(checkXxmbAndXxnrmb(TBXxmbAndNrmbs));
                return res;
            }
            Object TBXxmbObj = TBXxmbAndNrmbs[2];
            Map<String, Object> TBXxmb = JSON.parseObject(JSON.toJSONString(TBXxmbObj), Map.class);
            Object TBXxnrmbObj = TBXxmbAndNrmbs[3];
            List<Map<String, Object>> TBXxnrmbMapList = (List<Map<String, Object>>) TBXxnrmbObj;
            erroMessageResVOList = txzxGyUtils.checkTbMbCs(TBXxnrmbMapList, saveNsrxxtxReqVOList);
            if (!CollectionUtils.isEmpty(erroMessageResVOList)) {
                res.setErrorList(erroMessageResVOList);
                return res;
            }
            //按消息模板组装xxfb,并保存
            XtxxXxfbVo xxBtVo = checkTxzxXxBtfb(saveNsrxxtxReqVOList, TBXxmb, TBXxnrmbMapList);
            if (!GyUtils.isNull(xxBtVo.getErrorList())) {
                erroMessageResVOList = xxBtVo.getErrorList();
            }
            if (GyUtils.isNotNull(xxBtVo.getXxfbList())){
                xxfbVo.setXxTbMb(TBXxmb);
                xxfbVo.setXxTbVOList(xxBtVo.getXxfbList());
            }
        }

        //9准备tdmp推送
        saveResult = xxfbVo.getXxfbList().size();
        //int zfSaveResult=xxfbVo.getZfqdList().size();
        //if(saveResult>0 || zfSaveResult>0){//至少成功一条则发消息
        if (saveResult > 0) {//至少成功一条则发消息
            String isFlag = CacheUtils.getXtcs("XXZX-XXZX-000002");
            if (!"Y" .equals(isFlag)) {
                isFlag = "N";
            }
            //默认走kafka，配置系统参数为Y,不走kafka
            if ("N" .equals(isFlag)) {
                xxzxKafkaService.kafkaProducerSend(xxfbVo, "topic_nssb_xxzx_xxfb");
            } else {
                xxzxKafkaService.addXxfbSendDxAsync(xxfbVo);
            }
        }
        res.setSuccessNum(saveResult);
        res.setErrorList(erroMessageResVOList);
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SendDdxxResVO sendDdxxByGh(SendDdxxReqVO sendDdxxReqVO) {
        log.info("开始处理钉钉消息发送，业务系统ID：{}，录入人身份ID：{}，消息数量：{}", 
                sendDdxxReqVO.getYwxtId(), sendDdxxReqVO.getLrrsfId(),
                sendDdxxReqVO.getMsgList() != null ? sendDdxxReqVO.getMsgList().size() : 0);
        
        SendDdxxResVO resVO = new SendDdxxResVO();
        List<MsgVO> msgVOList = sendDdxxReqVO.getMsgList();
        
        // 参数校验
        if (GyUtils.isNull(msgVOList) || msgVOList.isEmpty()) {
            resVO.setCode("1");
            resVO.setMsg("msgList 参数为空！");
            log.warn("消息发送失败：msgList 参数为空");
            return resVO;
        }
        
        if (GyUtils.isNull(sendDdxxReqVO.getYwxtId())) {
            resVO.setCode("1");
            resVO.setMsg("业务系统ID不能为空！");
            log.warn("消息发送失败：业务系统ID不能为空");
            return resVO;
        }
        
        if (GyUtils.isNull(sendDdxxReqVO.getLrrsfId())) {
            resVO.setCode("1");
            resVO.setMsg("录入人身份ID不能为空！");
            log.warn("消息发送失败：录入人身份ID不能为空");
            return resVO;
        }
        
        String finalCode = "1";
        StringBuilder msgBuilder = new StringBuilder();
        int successCount = 0;
        int failCount = 0;
        
        // 修复数组索引错误：从0开始遍历
        for (int i = 0; i < msgVOList.size(); i++) {
            MsgVO msgVO = msgVOList.get(i);
            int messageIndex = i + 1; // 用于错误消息显示
            
            try {
                log.info("开始处理第{}条消息", messageIndex);
                
                // 验证单条消息参数
                String validationError = validateMsgVO(msgVO, messageIndex);
                if (GyUtils.isNotNull(validationError)) {
                    failCount++;
                    finalCode = "99";
                    msgBuilder.append(validationError).append("; ");
                    log.warn("第{}条消息参数验证失败：{}", messageIndex, validationError);
                    continue;
                }
                
                // 手动转换MsgVO到XxVO，确保字段正确映射
                XxVO xxVO = convertMsgVOToXxVO(msgVO);
                xxVO.setYwxtid(sendDdxxReqVO.getYwxtId());
                xxVO.setLrrsfid(sendDdxxReqVO.getLrrsfId());
                
                log.info("第{}条消息转换完成，开始发送钉钉消息", messageIndex);

                // 发送钉钉消息
                String errorMsg = this.msgChannelManager.processChannelMsgByGh(xxVO);
                
                if (GyUtils.isNotNull(errorMsg) && !errorMsg.trim().isEmpty()) {
                    failCount++;
                    finalCode = "99";
                    String failureMsg = String.format("第%d条消息发送失败！失败原因：%s", messageIndex, errorMsg);
                    msgBuilder.append(failureMsg).append("; ");
                    log.error("第{}条消息发送失败：{}", messageIndex, errorMsg);
                } else {
                    successCount++;
                    log.info("第{}条消息发送成功", messageIndex);
                }
                
            } catch (Exception e) {
                failCount++;
                finalCode = "1";
                String exceptionMsg = String.format("第%d条消息处理异常：%s", messageIndex, e.getMessage());
                msgBuilder.append(exceptionMsg).append("; ");
                log.error("第{}条消息处理异常", messageIndex, e);
            }
        }
        
        // 设置返回结果
        resVO.setCode(finalCode);
        
        // 构建详细的返回消息
        String resultMsg;
        if ("1".equals(finalCode)) {
            resultMsg = String.format("所有消息发送成功！共处理%d条消息", successCount);
        } else {
            resultMsg = String.format("部分消息发送失败！成功：%d条，失败：%d条。详细信息：%s", 
                    successCount, failCount, msgBuilder);
        }
        resVO.setMsg(resultMsg);
        
        log.info("钉钉消息发送完成，成功：{}条，失败：{}条", successCount, failCount);
        return resVO;
    }
    
    /**
     * 验证单条消息参数
     */
    private String validateMsgVO(MsgVO msgVO, int messageIndex) {
        if (GyUtils.isNull(msgVO)) {
            return String.format("第%d条消息为空", messageIndex);
        }
        
        if (GyUtils.isNull(msgVO.getMsgType()) || msgVO.getMsgType().trim().isEmpty()) {
            return String.format("第%d条消息的msgType参数为空", messageIndex);
        }
        
        if (GyUtils.isNull(msgVO.getMsgContent()) || msgVO.getMsgContent().trim().isEmpty()) {
            return String.format("第%d条消息的msgContent参数为空", messageIndex);
        }
        
        if (GyUtils.isNull(msgVO.getGhList()) || msgVO.getGhList().isEmpty()) {
            return String.format("第%d条消息的ghList参数为空", messageIndex);
        }
        
        // 验证工号列表中是否有空值
        for (int i = 0; i < msgVO.getGhList().size(); i++) {
            String gh = msgVO.getGhList().get(i);
            if (GyUtils.isNull(gh) || gh.trim().isEmpty()) {
                return String.format("第%d条消息的ghList中第%d个工号为空", messageIndex, i + 1);
            }
        }
        
        // 如果是卡片消息，验证卡片参数
        if ("action_card".equals(msgVO.getMsgType()) && GyUtils.isNull(msgVO.getDdingMsgActionCardVO())) {
            return String.format("第%d条消息类型为action_card时，ddingMsgActionCardVO不能为空", messageIndex);
        }
        
        return null; // 验证通过
    }
    
    /**
     * 转换MsgVO到XxVO，处理字段映射问题
     */
    private XxVO convertMsgVOToXxVO(MsgVO msgVO) {
        XxVO xxVO = new XxVO();
        xxVO.setMsgtype(msgVO.getMsgType());
        
        // 处理消息内容，根据消息类型进行不同处理
        String msgContent = processMessageContent(msgVO.getMsgContent(), msgVO.getMsgType());
        xxVO.setMsgContent(msgContent);
        xxVO.setGhList(msgVO.getGhList());
        
        // 处理DdingMsgActionCardVO类型转换
        if (GyUtils.isNotNull(msgVO.getDdingMsgActionCardVO())) {
            com.css.framework.xxzx.pojo.vo.xxtx.DdingMsgActionCardVO sourceDdingVO = msgVO.getDdingMsgActionCardVO();
            DdingMsgActionCardVO targetDdingVO = new DdingMsgActionCardVO();
            
            // 转换基本字段
            targetDdingVO.setBt(sourceDdingVO.getBt());
            targetDdingVO.setBtnOrientation(sourceDdingVO.getBtnOrientation());
            
            // 转换按钮列表
            if (GyUtils.isNotNull(sourceDdingVO.getActionCardBtnVOList())) {
                List<DdingMsgActionCardBtnVO> targetBtnList = new ArrayList<>();
                for (com.css.framework.xxzx.pojo.vo.xxtx.DdingMsgActionCardBtnVO sourceBtn : sourceDdingVO.getActionCardBtnVOList()) {
                    DdingMsgActionCardBtnVO targetBtn = new DdingMsgActionCardBtnVO();
                    targetBtn.setTitle(sourceBtn.getTitle());
                    targetBtn.setAction_url(sourceBtn.getAction_url());
                    targetBtnList.add(targetBtn);
                }
                targetDdingVO.setActionCardBtnVOList(targetBtnList);
            }
            
            xxVO.setDdingMsgActionCardVO(targetDdingVO);
        }
        
        return xxVO;
    }
    
    /**
     * 处理消息内容中的换行符
     * 根据消息类型进行不同处理
     */
    private String processMessageContent(String msgContent, String msgtype) {
        if (GyUtils.isNull(msgContent) || msgContent.trim().isEmpty()) {
            return msgContent;
        }
        
        String originalContent = msgContent;
        
        // 如果是action_card类型，先将/r/n转换为markdown格式的换行符
        if (DdptConstants.DINGDING_MSGTYPE_ACTION_CARD.equals(msgtype) && msgContent.contains("/r/n")) {
            msgContent = processSlashRSlashNForMarkdown(msgContent);
        }
        // 其他类型，按原来的方式处理 /r/n 格式的换行符
        else if (msgContent.contains("/r/n")) {
            msgContent = processSlashRSlashN(msgContent);
        }
        
        // 如果是action_card类型，转换为markdown格式
        if (DdptConstants.DINGDING_MSGTYPE_ACTION_CARD.equals(msgtype)) {
            msgContent = convertToMarkdown(msgContent);
        }
        
        // 记录转换情况（仅在内容发生变化时记录）
        if (!originalContent.equals(msgContent)) {
            log.debug("消息内容转换：类型={}，原内容长度={}，转换后长度={}",
                     msgtype, originalContent.length(), msgContent.length());
        }
        
        return msgContent;
    }
    
    /**
     * 将 /r/n 转换为 <br>，用于markdown换行
     */
    private String processSlashRSlashNForMarkdown(String msgContent) {
        if (GyUtils.isNull(msgContent) || !msgContent.contains("/r/n")) {
            return msgContent;
        }
        try {
            String result = msgContent.replace("/r/n", "<br>");
            log.debug("处理 /r/n 为 <br>：原内容='{}' -> 转换后='{}'", msgContent, result);
            return result;
        } catch (Exception e) {
            log.warn("处理 /r/n 为 <br> 异常：{}", e.getMessage());
            return msgContent;
        }
    }
    
    /**
     * 处理 /r/n 格式的换行符
     * 将 "测试 /r/n 测试测试3" 转换为 "测试:\r\n测试测试3" 格式
     */
    private String processSlashRSlashN(String msgContent) {
        if (GyUtils.isNull(msgContent) || !msgContent.contains("/r/n")) {
            return msgContent;
        }
        
        try {
            StringBuilder result = new StringBuilder();
            
            // 使用正则表达式匹配 /r/n 前后的内容
            String[] parts = msgContent.split("\\s*/r/n\\s*");
            
            for (int i = 0; i < parts.length; i++) {
                String part = parts[i].trim();
                
                if (!part.isEmpty()) {
                    if (i == 0) {
                        // 第一部分：如果以空格结尾，转换为冒号
                        if (msgContent.contains(part + " /r/n")) {
                            result.append(part).append(":");
                        } else {
                            result.append(part);
                        }
                    } else {
                        // 其他部分直接添加
                        result.append(part);
                    }
                }
                
                // 如果不是最后一部分，添加换行符
                if (i < parts.length - 1) {
                    result.append("\r\n");
                }
            }
            
            String finalResult = result.toString();
            log.debug("处理 /r/n 格式换行符：原内容='{}' -> 转换后='{}'", msgContent, finalResult);
            
            // 如果开启了详细日志，显示类似append的格式
            if (log.isTraceEnabled()) {
                logAppendFormat(msgContent, parts);
            }
            
            return finalResult;
            
        } catch (Exception e) {
            log.warn("处理 /r/n 格式换行符异常：{}", e.getMessage());
            // 异常情况下返回原内容
            return msgContent;
        }
    }
    
    /**
     * 记录类似append格式的日志（仅用于调试）
     */
    private void logAppendFormat(String originalContent, String[] parts) {
        StringBuilder appendLog = new StringBuilder();
        appendLog.append("消息内容append格式：\n");
        
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i].trim();
            if (!part.isEmpty()) {
                if (i == 0 && originalContent.contains(part + " /r/n")) {
                    appendLog.append("xxnr.append(\"").append(part).append(":\");\n");
                } else {
                    appendLog.append("xxnr.append(\"").append(part).append("\");\n");
                }
            }
            
            // 如果不是最后一部分，添加换行符的append
            if (i < parts.length - 1) {
                appendLog.append("xxnr.append(\"\\r\\n\");\n");
            }
        }
        
        log.trace(appendLog.toString());
    }

    /**
     * 将消息内容转换为markdown格式
     * 支持换行符、粗体、斜体等基本markdown语法
     */
    private String convertToMarkdown(String msgContent) {
        if (GyUtils.isNull(msgContent) || msgContent.trim().isEmpty()) {
            return msgContent;
        }
        
        String originalContent = msgContent;
        
        try {
            // 1. 处理换行符 - 将\r\n和\n转换为markdown换行
            msgContent = msgContent.replace("\r\n", "\n")
                                   .replace("\r", "\n");
            
            // 2. 处理多个连续换行符，统一为两个换行符（markdown段落分隔）
            msgContent = msgContent.replaceAll("\n{3,}", "\n\n");
            
            // 3. 处理粗体标记 - 将**文本**转换为markdown粗体
            msgContent = msgContent.replaceAll("\\*\\*(.*?)\\*\\*", "**$1**");
            
            // 4. 处理斜体标记 - 将*文本*转换为markdown斜体
            msgContent = msgContent.replaceAll("\\*([^*]+)\\*", "*$1*");
            
            // 5. 处理代码标记 - 将`代码`转换为markdown代码
            msgContent = msgContent.replaceAll("`([^`]+)`", "`$1`");
            
            // 6. 处理链接 - 将[文本](链接)转换为markdown链接
            msgContent = msgContent.replaceAll("\\[([^\\]]+)\\]\\(([^)]+)\\)", "[$1]($2)");
            
            // 7. 处理列表 - 将行首的- 或 * 转换为markdown列表
            String[] lines = msgContent.split("\n");
            StringBuilder result = new StringBuilder();
            
            for (String line : lines) {
                String trimmedLine = line.trim();
                
                // 处理无序列表
                if (trimmedLine.startsWith("- ") || trimmedLine.startsWith("* ")) {
                    result.append(trimmedLine).append("\n");
                }
                // 处理有序列表（数字. 格式）
                else if (trimmedLine.matches("^\\d+\\.\\s.*")) {
                    result.append(trimmedLine).append("\n");
                }
                // 处理标题（# 格式）
                else if (trimmedLine.startsWith("#")) {
                    result.append(trimmedLine).append("\n");
                }
                // 普通文本
                else if (!trimmedLine.isEmpty()) {
                    result.append(trimmedLine).append("\n");
                } else {
                    // 空行保持空行
                    result.append("\n");
                }
            }
            
            msgContent = result.toString().trim();
            
            // 8. 确保内容以换行符结尾（markdown格式要求）
            if (!msgContent.endsWith("\n")) {
                msgContent += "\n";
            }
            
            log.debug("转换为markdown格式：原内容='{}' -> 转换后='{}'", 
                     originalContent, msgContent);
            
            return msgContent;
            
        } catch (Exception e) {
            log.warn("转换为markdown格式异常：{}", e.getMessage());
            // 异常情况下返回原内容
            return originalContent;
        }
    }

    /**
     * @name 中文名称
     * @description 保存消息发布管理接口
     * @time 创建时间:2024年06月05日下午08:13:45
     * @param saveXxfbglReqVO
     * @return {@link ErrorResVO }
     * @throws IOException
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public ErrorResVO saveXxfbgl(SaveXxfbglReqVO saveXxfbglReqVO) {
        ErrorResVO res = new ErrorResVO();
        List<ErrorListVO> erroMessageResVOList = new ArrayList<>();
        log.info("消息发布管理保存逻辑校验开始");
        //参数校验
        ErrorListVO vo = checkXxfbglCs(saveXxfbglReqVO);
        if (!GyUtils.isNull(vo) && !GyUtils.isNull(vo.getErrorMsg())) {
            erroMessageResVOList.add(vo);
        }
        res.setSuccessNum(0);
        res.setErrorList(erroMessageResVOList);
        if (!CollectionUtils.isEmpty(erroMessageResVOList)) {
            return res;
        }
        log.info("消息发布管理保存逻辑校验结束");
        //获取模板信息
        Object[] xxmbAndNrmbs = txzxGyUtils.getXtxxmbAndNrmbs(saveXxfbglReqVO.getYwxtid(), saveXxfbglReqVO.getXxmbbh());
        if (String.valueOf(xxmbAndNrmbs[0]).equals("2")) {
            res.setErrorList(checkXxmbAndXxnrmb(xxmbAndNrmbs));
            return res;
        }

        Object xxmbObj = xxmbAndNrmbs[2];
        Map<String, Object> xxmb = JSON.parseObject(JSON.toJSONString(xxmbObj), Map.class);
        Object xxnrmbObj = xxmbAndNrmbs[3];
        //List<Map> xxnrmbMapList = JSONArray.parseArray(JSON.toJSONString(xxnrmbObj), Map.class);
        List<Map<String, Object>> xxnrmbMapList = (List<Map<String, Object>>) xxnrmbObj;
        //获取消息发布管理接口插库全部数据
        XtxxXxfbglVo xxfbVo = getXxzxXxfbgl(saveXxfbglReqVO, xxmb, xxnrmbMapList);
        if (!GyUtils.isNull(xxfbVo.getErrorList())) {
            erroMessageResVOList = xxfbVo.getErrorList();
        }
        int saveResult = 0;
        xxfbVo.setXxmb(xxmb);

        //9准备tdmp推送
        saveResult = xxfbVo.getXxfbglList().size();

        if (saveResult > 0) {//至少成功一条则发消息
            //xxzxKafkaService.kafkaProducerSend(xxfbVo, "topic_nssb_xxzx_xxfb");
            //实际没走kafka借用类进行对库操作
            xxzxKafkaService.addXxfbglAsync(xxfbVo);
        }
        res.setXxfbglxx(xxfbVo.getXxfbglxx());
        res.setSuccessNum(saveResult);
        res.setErrorList(erroMessageResVOList);
        return res;
    }

    /**
     *
     * @param updateXxfbglReqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErrorResVO updateXxfbgl(UpdateXxfbglReqVO updateXxfbglReqVO) {
        ErrorResVO res = new ErrorResVO();
        List<ErrorListVO> erroMessageResVOList = new ArrayList<>();
        log.info("消息发布管理更新逻辑校验开始");
        //参数校验
        ErrorListVO errvo = checkUpXxfbglCs(updateXxfbglReqVO);
        if (!GyUtils.isNull(errvo) && !GyUtils.isNull(errvo.getErrorMsg())) {
            erroMessageResVOList.add(errvo);
        }
        res.setSuccessNum(0);
        res.setErrorList(erroMessageResVOList);
        if (!CollectionUtils.isEmpty(erroMessageResVOList)) {
            return res;
        }
        log.info("消息发布管理更新逻辑校验结束");
        //正式进入业务逻辑
        //发布状态 01 拟稿中， 03 已发布，04 已撤回， 09 作废
        final String reqFbztDm = updateXxfbglReqVO.getFbztDm();
        final String xxfbuuid = updateXxfbglReqVO.getXxfbuuid();

        LambdaQueryWrapper<XxzxXxfbgl> xxfbglWrapper = new LambdaQueryWrapper<>();
        xxfbglWrapper.eq(XxzxXxfbgl::getXxfbuuid, xxfbuuid);
        xxfbglWrapper.eq(XxzxXxfbgl::getYxbz, "Y");
        XxzxXxfbgl xxfbglDTO = xxzxXxfbglMapper.selectOne(xxfbglWrapper);
        int i = 0;
        if (GyUtils.isNull(xxfbglDTO)) {
            errvo.setErrorCode("5");
            errvo.setErrorMsg("未查询到对应发布管理消息，无法更新！");
            erroMessageResVOList.add(errvo);
            res.setErrorList(erroMessageResVOList);
            return res;
        } else {
            final String fbztDm = xxfbglDTO.getFbztDm();
            if ("09" .equals(fbztDm)) {
                //库中状态为作废
                errvo.setErrorCode("2");
                errvo.setErrorMsg("该消息已为作废状态，无法更新！");
                erroMessageResVOList.add(errvo);
                res.setErrorList(erroMessageResVOList);
                return res;
            }
            if ("01" .equals(reqFbztDm)) {
                //传入状态为拟稿。 对应场景：拟稿页编辑保存，撤回页编辑保存。
                if ("01" .equals(fbztDm) || "04" .equals(fbztDm)) {
                    //库中状态为拟稿 或者 撤回情况。 逻辑处理一样， 对传入数据进行更新操作即可。 fbzt置为01拟稿
                    // 更新fbzt为01，更新传入字段， 删除 已存在jsdx表 fjgx表， 重新插入jsdx表，有附件重新插入fjgx
                    //1、先更新xxfbgl 状态为01 拟稿,更新传入数据
                    final String ywzj = updateXxfbglReqVO.getYwzj();
                    final String xxcs = updateXxfbglReqVO.getXxcs();
                    final String Xxyxqq = updateXxfbglReqVO.getXxyxqq();
                    final String Xxyxqz = updateXxfbglReqVO.getXxyxqz();
                    final String fbr = updateXxfbglReqVO.getFbr();
                    String xxyxj = updateXxfbglReqVO.getXxyxj();
                    if (!"9" .equals(xxyxj)) {
                        xxyxj = "0";
                    }
                    final List<FjxxVO> wjxxjh = updateXxfbglReqVO.getWjxxjh();

                    final LambdaUpdateWrapper<XxzxXxfbgl> updateXxfbglWrapper = new LambdaUpdateWrapper<>();
                    updateXxfbglWrapper.eq(XxzxXxfbgl::getXxfbuuid, xxfbuuid);
                    updateXxfbglWrapper.set(XxzxXxfbgl::getFbztDm, reqFbztDm);
                    updateXxfbglWrapper.set(XxzxXxfbgl::getXgrsfid, updateXxfbglReqVO.getLrrsfid());
                    updateXxfbglWrapper.set(XxzxXxfbgl::getXgrq, new Date());
                    if (GyUtils.isNotNull(ywzj)) {
                        updateXxfbglWrapper.set(XxzxXxfbgl::getYwzj, ywzj);
                    }
                    if (GyUtils.isNotNull(xxcs)) {
                        updateXxfbglWrapper.set(XxzxXxfbgl::getXxnrcs, xxcs);
                        //查询取的标题是直接取库。内容是重新拼。 所以库里的标题需要重新赋值
                        final String xxzsbtmb = xxfbglDTO.getXxzsbtmb();
                        ErrorListVO vo = xxnrByXxcs(xxzsbtmb, xxcs);

                        updateXxfbglWrapper.set(XxzxXxfbgl::getXxzsbt, vo.getJguuid());
                    }
                    if (GyUtils.isNotNull(fbr)) {
                        updateXxfbglWrapper.set(XxzxXxfbgl::getFbr1, fbr);
                    }
                    if (GyUtils.isNotNull(Xxyxqq)) {
                        updateXxfbglWrapper.set(XxzxXxfbgl::getXxyxqq, DateUtils.stringToDate(Xxyxqq));
                    }
                    if (GyUtils.isNotNull(Xxyxqz)) {
                        updateXxfbglWrapper.set(XxzxXxfbgl::getXxyxqz, DateUtils.stringToDate(Xxyxqz));
                    }
                    if (GyUtils.isNotNull(xxyxj)) {
                        updateXxfbglWrapper.set(XxzxXxfbgl::getXxyxj, xxyxj);
                    }
                    if (GyUtils.isNotNull(wjxxjh)) {
                        updateXxfbglWrapper.set(XxzxXxfbgl::getSfyfj, "Y");
                    }

                    int j = xxzxXxfbglMapper.update(updateXxfbglWrapper);

                    if (j > 0) {
                        final LambdaQueryWrapper<XxzxXxfbglJsdx> jsdxWrapper = new LambdaQueryWrapper<>();
                        jsdxWrapper.eq(XxzxXxfbglJsdx::getXxfbuuid, xxfbglDTO.getXxfbuuid());
                        int k = xxzxXxfbglJsdxMapper.delete(jsdxWrapper);

                        if (k > 0) {
                            //先删 jsdx  再插
                            SaveXxfbglJsOneVO saveXxfbglJsOneVO = new SaveXxfbglJsOneVO();
                            saveXxfbglJsOneVO.setXxfbgl(xxfbglDTO);

                            List<String> jsdxzList = getJsdxzList(updateXxfbglReqVO.getYhuuidList(), updateXxfbglReqVO.getJguuidList(), updateXxfbglReqVO.getZzuuidList(), updateXxfbglReqVO.getGhList());
                            saveXxfbglJsOneVO.setJsdxzList(jsdxzList);

                            List<XxzxXxfbglJsdx> xxfbglJsdxList = getXxfbglJsdxDTOList(saveXxfbglJsOneVO);

                            List<XxzxXxfjGxb> xxfjGxList = new ArrayList<>();
                            if (GyUtils.isNotNull(wjxxjh)) {
                                final LambdaQueryWrapper<XxzxXxfjGxb> fjGxbWrapper = new LambdaQueryWrapper<>();
                                fjGxbWrapper.eq(XxzxXxfjGxb::getXxuuid, xxfbuuid);
                                int l = xxzxXxfjGxbMapper.delete(fjGxbWrapper);
                                if (l > 0) {
                                    //先删附件 再插
                                    for (FjxxVO vo : wjxxjh) {
                                        XxzxXxfjGxb xxzxXxfjGxb = new XxzxXxfjGxb();
                                        xxzxXxfjGxb.setXxuuid(xxfbuuid);
                                        xxzxXxfjGxb.setFjuuid(vo.getFjuuid());
                                        //此逻辑固定 ，02 关联消息发布管理
                                        xxzxXxfjGxb.setGlgxlx("02");
                                        xxzxXxfjGxb.setYxbz("Y");
                                        xxzxXxfjGxb.setLrrsfid(updateXxfbglReqVO.getLrrsfid());//接口加入参
                                        xxzxXxfjGxb.setLrrq(new Date());
                                        xxzxXxfjGxb.setSjgsdq("00000000000");
                                        xxzxXxfjGxb.setSjcsdq("00000000000");
                                        xxzxXxfjGxb.setYwqdDm("ZNSB.MHZC");
                                        xxfjGxList.add(xxzxXxfjGxb);
                                    }
                                }
                            }
                            i = xxzxKafkaService.tbXxfbgl(xxfbglJsdxList, xxfjGxList);
                        }

                        if (i <= 0) {
                            errvo.setErrorCode("2");
                            errvo.setErrorMsg("更新库失败");
                            erroMessageResVOList.add(errvo);
                            res.setErrorList(erroMessageResVOList);
                            return res;
                        }
                    }
                } else {
                    errvo.setErrorCode("2");
                    errvo.setErrorMsg("已发布、已作废的消息，无法置为拟稿状态！");
                    erroMessageResVOList.add(errvo);
                    res.setErrorList(erroMessageResVOList);
                    return res;
                }
            } else {
                errvo.setErrorCode("2");
                errvo.setErrorMsg("此接口只支持传入拟稿状态！");
                erroMessageResVOList.add(errvo);
                res.setErrorList(erroMessageResVOList);
                return res;
            }
        }
        return res;
    }

    /**
     *
     * @param updateXxfbglFbztReqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErrorResVO updateXxfbglFbzt(UpdateXxfbglFbztReqVO updateXxfbglFbztReqVO) {
        ErrorResVO res = new ErrorResVO();
        List<ErrorListVO> erroMessageResVOList = new ArrayList<>();
        log.info("消息发布管理更新逻辑校验开始");
        //参数校验
        ErrorListVO vo = checkUpXxfbglFbztCs(updateXxfbglFbztReqVO);
        if (!GyUtils.isNull(vo) && !GyUtils.isNull(vo.getErrorMsg())) {
            erroMessageResVOList.add(vo);
        }
        res.setSuccessNum(0);
        res.setErrorList(erroMessageResVOList);
        if (!CollectionUtils.isEmpty(erroMessageResVOList)) {
            return res;
        }
        log.info("消息发布管理更新逻辑校验结束");

        //正式进入业务逻辑
        //发布状态 01 拟稿中， 03 已发布，04 已撤回， 09 作废
        final String reqFbztDm = updateXxfbglFbztReqVO.getFbztDm();
        final String xxfbuuid = updateXxfbglFbztReqVO.getXxfbuuid();

        LambdaQueryWrapper<XxzxXxfbgl> xxfbglWrapper = new LambdaQueryWrapper<>();
        xxfbglWrapper.eq(XxzxXxfbgl::getXxfbuuid, xxfbuuid);
        xxfbglWrapper.eq(XxzxXxfbgl::getYxbz, "Y");
        XxzxXxfbgl xxfbglDTO = xxzxXxfbglMapper.selectOne(xxfbglWrapper);
        ErrorListVO errvo = new ErrorListVO();
        int i = 0;
        if (GyUtils.isNull(xxfbglDTO)) {
            errvo.setErrorCode("5");
            errvo.setErrorMsg("未查询到对应发布管理消息，无法更新！");
            erroMessageResVOList.add(errvo);
            res.setErrorList(erroMessageResVOList);
            return res;
        } else {
            final String fbztDm = xxfbglDTO.getFbztDm();
            if ("09" .equals(fbztDm)) {
                //库中状态为作废
                errvo.setErrorCode("2");
                errvo.setErrorMsg("该消息已为作废状态，无法更新！");
                erroMessageResVOList.add(errvo);
                res.setErrorList(erroMessageResVOList);
                return res;
            }
            if ("03" .equals(reqFbztDm)) {
                //传入状态为发布。 对应场景：拟稿页编辑保存  20240704支持库中撤回状态，直接发布。对应场景，撤回页直接发布，不上一步编辑
                if ("01" .equals(fbztDm) || "04" .equals(fbztDm)) {
                    //库中状态为拟稿。 进行发布逻辑处理。 更新 fbgl表的发布状态，同步xxfb表，存在附件同时同步fjgxb 。  这个是大活儿！

                    //1、先更新xxfbgl 状态为03 已发布
                    final LambdaUpdateWrapper<XxzxXxfbgl> updateXxfbglWrapper = new LambdaUpdateWrapper<>();
                    updateXxfbglWrapper.eq(XxzxXxfbgl::getXxfbuuid, xxfbuuid);
                    updateXxfbglWrapper.set(XxzxXxfbgl::getFbztDm, reqFbztDm);
                    updateXxfbglWrapper.set(XxzxXxfbgl::getXgrsfid, updateXxfbglFbztReqVO.getLrrsfid());
                    updateXxfbglWrapper.set(XxzxXxfbgl::getXgrq, new Date());
                    int j = xxzxXxfbglMapper.update(updateXxfbglWrapper);

                    // 2、 拟稿状态，xxfbgl有数据，jsdx有数据，fjgx有数据。 此接口只需同步到xxfb及后续逻辑
                    if (j > 0) {
                        LambdaQueryWrapper<XxzxXxfbglJsdx> xxfbglJsdxWrapper = new LambdaQueryWrapper<>();
                        xxfbglJsdxWrapper.eq(XxzxXxfbglJsdx::getXxfbuuid, xxfbuuid);
                        List<XxzxXxfbglJsdx> xxzxXxfbglJsdxList = xxzxXxfbglJsdxMapper.selectList(xxfbglJsdxWrapper);
                        List<String> jsdxzList = xxzxXxfbglJsdxList.stream().map(XxzxXxfbglJsdx::getJsdxz).collect(Collectors.toList());

                        SaveXxfbglJsOneVO saveXxfbglJsOneVO = new SaveXxfbglJsOneVO();
                        saveXxfbglJsOneVO.setXxfbgl(xxfbglDTO);
                        saveXxfbglJsOneVO.setJsdxzList(jsdxzList);

                        LambdaQueryWrapper<XxzxXxfjGxb> xxzxXxfjGxbWrapper = new LambdaQueryWrapper<>();
                        xxzxXxfjGxbWrapper.eq(XxzxXxfjGxb::getXxuuid, xxfbuuid);
                        List<XxzxXxfjGxb> xxzxXxfjGxbList = xxzxXxfjGxbMapper.selectList(xxzxXxfjGxbWrapper);
                        //如果存在附件
                        if (GyUtils.isNotNull(xxzxXxfjGxbList)) {
                            List<FjxxVO> wjxxjh = new ArrayList<>();
                            xxzxXxfjGxbList.forEach(t -> {
                                FjxxVO fjxxvo = new FjxxVO();
                                fjxxvo.setFjuuid(t.getFjuuid());
                                wjxxjh.add(fjxxvo);
                            });
                            saveXxfbglJsOneVO.setWjxxjh(wjxxjh);
                        }
                        //传入消息发布管理 和 jsdxzlist 及附件信息， 同步 xxfb及fjgx
                        i = tbXxfb(saveXxfbglJsOneVO);
                    }
                } else {
                    errvo.setErrorCode("2");
                    errvo.setErrorMsg("已发布、已作废的消息，无法进行发布！");
                    erroMessageResVOList.add(errvo);
                    res.setErrorList(erroMessageResVOList);
                    return res;
                }
            } else if ("04" .equals(reqFbztDm)) {
                //传入状态为撤回。 对应场景：发布页点击撤回
                if ("03" .equals(fbztDm)) {
                    //库中状态为发布。 进行撤回处理。 xxfbgl 置为 04 撤回状态， xxfb表同步为 YXBZ为N。
                    i = zfchXxfbgl(xxfbuuid, reqFbztDm, updateXxfbglFbztReqVO.getLrrsfid(), fbztDm);
                } else {
                    errvo.setErrorCode("2");
                    errvo.setErrorMsg("只有已发布状态才用撤回！");
                    erroMessageResVOList.add(errvo);
                    res.setErrorList(erroMessageResVOList);
                    return res;
                }
            } else if ("09" .equals(reqFbztDm)) {
                //传入状态为作废。 对应场景：拟稿页点击删除，撤回页点击删除
                if ("01" .equals(fbztDm) || "04" .equals(fbztDm) || "03" .equals(fbztDm)) {
                    //库中状态为拟稿 或者 撤回情况。 逻辑处理一样。
                    //20240716 升级公告删除场景，库中状态为发布，也能作废
                    i = zfchXxfbgl(xxfbuuid, reqFbztDm, updateXxfbglFbztReqVO.getLrrsfid(), fbztDm);
                } else {
                    errvo.setErrorCode("2");
                    errvo.setErrorMsg("只有拟稿、撤回、发布状态才有作废！");
                    erroMessageResVOList.add(errvo);
                    res.setErrorList(erroMessageResVOList);
                    return res;
                }
            }
        }
        if (i <= 0) {
            errvo.setErrorCode("2");
            errvo.setErrorMsg("更新库失败");
            erroMessageResVOList.add(errvo);
            res.setErrorList(erroMessageResVOList);
        }
        return res;
    }

    /**
     * @name 中文名称
     * @description 传入消息发布管理 和 jsdxzlist 及附件信息， 同步 xxfb及fjgx。
     * @time 创建时间:2024年06月07日下午03:40:22
     * @param saveXxfbglJsOneVO
     * @return int
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private int tbXxfb(SaveXxfbglJsOneVO saveXxfbglJsOneVO) {
        List<String> jsdxzList = saveXxfbglJsOneVO.getJsdxzList();
        List<XxzxXxfb> xxfbList = new ArrayList<>();
        List<XxzxXxfjGxb> xxfjGxList = new ArrayList<>();
        for (String jsdxz : jsdxzList) {
            //组装消息发布表DTO   xxzx_xxfb
            XxzxXxfb xxfb = getXxzxXxfbOne(saveXxfbglJsOneVO, jsdxz);
            //组装附件关系表List<DTO> xxzx_xxfj_gxb
            List<FjxxVO> wjxxjh = saveXxfbglJsOneVO.getWjxxjh();
            if (GyUtils.isNotNull(wjxxjh)) {
                xxfb.setSfyfj("Y");
                for (FjxxVO vo : wjxxjh) {
                    XxzxXxfjGxb xxzxXxfjGxb = new XxzxXxfjGxb();
                    xxzxXxfjGxb.setXxuuid(xxfb.getXxuuid());
                    xxzxXxfjGxb.setFjuuid(vo.getFjuuid());
                    //此逻辑固定 ，01 关联消息发布表 
                    xxzxXxfjGxb.setGlgxlx("01");
                    xxzxXxfjGxb.setYxbz("Y");
                    xxzxXxfjGxb.setLrrsfid(xxfb.getLrrsfid());//接口加入参
                    xxzxXxfjGxb.setLrrq(new Date());
                    xxzxXxfjGxb.setSjgsdq("00000000000");
                    xxzxXxfjGxb.setSjcsdq("00000000000");
                    xxzxXxfjGxb.setYwqdDm("ZNSB.MHZC");
                    xxfjGxList.add(xxzxXxfjGxb);
                }
            }
            xxfbList.add(xxfb);
        }
        return xxzxKafkaService.tbXxfb(xxfbList, xxfjGxList);
    }

    /**
     * @name 中文名称
     * @description xxfbgl的发布状态置为传入状态（04 撤回、09 作废） 作废情况YXBZ置N，xxfb表同步为 YXBZ为N。
     * @time 创建时间:2024年06月07日上午11:19:59
     * @param xxfbuuid
     * @param reqFbztDm
     * @param lrrsfid
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Transactional
    public int zfchXxfbgl(String xxfbuuid, String reqFbztDm, String lrrsfid, String fbztDm) {
        int i = 0;
        //reqFbztDm  传入04 撤回、09 作废 进入此方法   fbztDm 库中可能存在 03发布、 01拟稿、04撤回
        final LambdaUpdateWrapper<XxzxXxfbgl> updateXxfbglWrapper = new LambdaUpdateWrapper<>();
        updateXxfbglWrapper.eq(XxzxXxfbgl::getXxfbuuid, xxfbuuid);
        /*if("09".equals(reqFbztDm)){
            //只有09作废，才将yxbz置为N
            updateXxfbglWrapper.set(XxzxXxfbgl::getYxbz, "N");
        }*/
        updateXxfbglWrapper.set(XxzxXxfbgl::getFbztDm, reqFbztDm);
        updateXxfbglWrapper.set(XxzxXxfbgl::getXgrsfid, lrrsfid);
        updateXxfbglWrapper.set(XxzxXxfbgl::getXgrq, new Date());
        i = xxzxXxfbglMapper.update(updateXxfbglWrapper);
        if ("03" .equals(fbztDm)) {
            //只有库中发布状态，xxfb表才有数据。 拟稿 xxfb没有数据，撤回数据为N等于没有数据。
            if (i > 0) {
                final LambdaUpdateWrapper<XxzxXxfb> updateXxfbWrapper = new LambdaUpdateWrapper<>();
                updateXxfbWrapper.eq(XxzxXxfb::getYwzj, xxfbuuid);
                updateXxfbWrapper.set(XxzxXxfb::getYxbz, "N");
                updateXxfbWrapper.set(XxzxXxfb::getXgrsfid, lrrsfid);
                updateXxfbWrapper.set(XxzxXxfb::getXgrq, new Date());
                i = xxzxXxfbMapper.update(updateXxfbWrapper);
            }
        }
        return i;
    }

    //查询纳税人消息列表
    @Override
    public ListXtxxResVO listXtxx(ListXtxxReqVO listXtxxReqVO) {
        ListXtxxResVO listXtxxResVO = new ListXtxxResVO();
        log.info("查询消息列表入参" + listXtxxReqVO);
        if (GyUtils.isNull(listXtxxReqVO.getJsqd())) {
            listXtxxResVO.setReturnCode("2");
            listXtxxResVO.setReturnMsg("消息接收渠道不能为空");
            return listXtxxResVO;
        }
        if (GyUtils.isNotNull(listXtxxReqVO.getXxzsbt())) {
            listXtxxReqVO.setXxzsbt("%" + listXtxxReqVO.getXxzsbt() + "%");
        }
        /*if (GyUtils.isNull(listXtxxReqVO.getJguuidList())) {
            listXtxxResVO.setReturnCode("2");
            listXtxxResVO.setReturnMsg("机构ID不能为空");
            return listXtxxResVO;
        }
        if (GyUtils.isNull(listXtxxReqVO.getYhuuid())) {
            listXtxxResVO.setReturnCode("2");
            listXtxxResVO.setReturnMsg("用户ID不能为空");
            return listXtxxResVO;
        }
        if (GyUtils.isNull(listXtxxReqVO.getZzuuid())) {
            listXtxxResVO.setReturnCode("2");
            listXtxxResVO.setReturnMsg("组织ID不能为空");
            return listXtxxResVO;
        }*/
        //jguuid集合、zzuuid、yhuuid拼成一个list进行后台数据校验
        List<String> yhidList = listXtxxReqVO.getYhuuidList();
        List<String> jgidList = listXtxxReqVO.getJguuidList();
        List<String> zzidList = listXtxxReqVO.getZzuuidList();
        List<String> ghList = listXtxxReqVO.getGhList();

        List<String> jsdxzList = getJsdxzList(yhidList, jgidList, zzidList,ghList);
        jsdxzList.add("all");//公开情况永远可以查到
        if (GyUtils.isNull(listXtxxReqVO.getPageNum()) || GyUtils.isNull(listXtxxReqVO.getPageSize())) {
            listXtxxReqVO.setPageNum("1");
            listXtxxReqVO.setPageSize("10");
        }
        Integer pageNum = Integer.parseInt(listXtxxReqVO.getPageNum());
        Integer pageSize = Integer.parseInt(listXtxxReqVO.getPageSize());
        if (pageNum == null || pageNum <= 0 || pageSize == null || pageSize <= 0) {
            listXtxxResVO.setReturnCode("4");
            listXtxxResVO.setReturnMsg("当前页码或当前页大小不正确");
            return listXtxxResVO;
        }
        if (pageSize > 100) {
            listXtxxResVO.setReturnCode("4");
            listXtxxResVO.setReturnMsg("单页查询数量不能超过100!");
            return listXtxxResVO;
        }
        RqDateVO rqvo = new RqDateVO();
        rqvo.setDqrq(DateUtils.getSystemCurrentTime().getTime());
        if (GyUtils.isNotNull(listXtxxReqVO.getCxrqq())) {
            rqvo.setRqq(DateUtils.stringToDate(listXtxxReqVO.getCxrqq()));
        }
        if (GyUtils.isNotNull(listXtxxReqVO.getCxrqz())) {
            rqvo.setRqz(DateUtils.stringToDate(listXtxxReqVO.getCxrqz()));
        }
        //通过消息模板编号判断查询纳税人消息列表，如果xxmbbh为空，则按照原来逻辑，否则按照模板编号查询全部消息
        //查询已阅读消息
        /*if ("Y".equals(listXtxxReqVO.getSfyyd())) {
            //查询总记录数
            int rowCount = xxzxXxfbMapper.listXxfbTotalSizeYd(jsdxzList ,listXtxxReqVO,rqvo);
            if (rowCount == 0) {
                listXtxxResVO.setReturnCode("5");
                listXtxxResVO.setReturnMsg("没有查到对应的数据");
                return listXtxxResVO;
            }
            //定义查询起始值
            Integer startIndex = (pageNum - 1) * pageSize;
            listXtxxResVO.setPageNum(pageNum);
            listXtxxResVO.setPageSize(pageSize);
            List<XxzxXxYdfb> txzxXxfbDTOList = xxzxXxfbMapper.listXxfbYd(startIndex, listXtxxReqVO,jsdxzList, pageSize, rqvo);

            List<XtxxVO> xtxxVOLists = getXtxxVOList(txzxXxfbDTOList);
            listXtxxResVO.setData(xtxxVOLists);
            listXtxxResVO.setTotalSize(rowCount);
            listXtxxResVO.setTotalPage(rowCount / pageSize + (rowCount % pageSize != 0 ? 1 : 0));
            listXtxxResVO.setReturnCode("1");
            listXtxxResVO.setReturnMsg("查询成功");
            log.info("查询纳税人消息列表出参Size" + xtxxVOLists.size());
        }
        //查询未阅读消息
        if ("N".equals(listXtxxReqVO.getSfyyd())) {
            //查询总记录数
            int rowCount = xxzxXxfbMapper.listXxfbTotalSizeWd(jsdxzList, listXtxxReqVO,rqvo);
            if (rowCount == 0) {
                listXtxxResVO.setReturnCode("5");
                listXtxxResVO.setReturnMsg("没有查到对应的数据");
                return listXtxxResVO;
            }
            //定义查询起始值
            Integer startIndex = (pageNum - 1) * pageSize;
            listXtxxResVO.setPageNum(pageNum);
            listXtxxResVO.setPageSize(pageSize);
            List<XxzxXxYdfb> txzxXxfbDTOList = xxzxXxfbMapper.listXxfbWd(startIndex, listXtxxReqVO,jsdxzList, pageSize,rqvo);
            List<XtxxVO> xtxxVOLists = getXtxxVOList(txzxXxfbDTOList);
            listXtxxResVO.setData(xtxxVOLists);
            listXtxxResVO.setTotalSize(rowCount);
            listXtxxResVO.setTotalPage(rowCount / pageSize + (rowCount % pageSize != 0 ? 1 : 0));
            listXtxxResVO.setReturnCode("1");
            listXtxxResVO.setReturnMsg("查询成功");
            log.info("查询消息列表出参Size" + xtxxVOLists.size());
        }
        //查询全部消息
        if (GyUtils.isNull(listXtxxReqVO.getSfyyd())) {*/
        //20240709 将 sql 合并，sfyyd  在xml 判断。
        //查询总记录数
        int rowCount = xxzxXxfbMapper.listXxfbTotalSize(jsdxzList, listXtxxReqVO, rqvo);
        if (rowCount == 0) {
            listXtxxResVO.setReturnCode("5");
            listXtxxResVO.setReturnMsg("没有查到对应的数据");
            return listXtxxResVO;
        }
        //定义查询起始值
        Integer startIndex = (pageNum - 1) * pageSize;
        listXtxxResVO.setPageNum(pageNum);
        listXtxxResVO.setPageSize(pageSize);
        List<XxzxXxYdfb> txzxNsrXxfbDTOList = xxzxXxfbMapper.listXxfb(startIndex, listXtxxReqVO, jsdxzList, pageSize, rqvo);
        List<XtxxVO> xtxxVOLists = new ArrayList<>();
        List<XxzxXxYdfb> xxfbDTOList = new ArrayList<>();
        // 根据业务主键查询是否有需要拼接的消息
        for (XxzxXxYdfb ydfb : txzxNsrXxfbDTOList){
            if (!"00000000000_00000024".equals(ydfb.getXxmbbh()) && !"00000000000_00000025".equals(ydfb.getXxmbbh())){
                xxfbDTOList.add(ydfb);
                continue;
            }
            List<XxzxXxfb> xxfbList = xxzxXxfbMapper.listXxfbByYwzj(ydfb.getYwzj());
            if (GyUtils.isNotNull(xxfbList) && xxfbList.size() > 1){
                XtxxVO pjXtxxVO = getPjXtxxVO(ydfb,xxfbList);
                xtxxVOLists.add(pjXtxxVO);
            }
        }
        xtxxVOLists.addAll(getXtxxVOList(xxfbDTOList));
        listXtxxResVO.setData(xtxxVOLists);
        listXtxxResVO.setTotalSize(rowCount);
        listXtxxResVO.setTotalPage(rowCount / pageSize + (rowCount % pageSize != 0 ? 1 : 0));
        listXtxxResVO.setReturnCode("1");
        listXtxxResVO.setReturnMsg("查询成功");
        log.info("查询消息列表出参Size" + xtxxVOLists.size());
//        }
        return listXtxxResVO;
    }

    /**
     *
     * @param listXxfbglReqVO
     * @return
     */
    @Override
    public ListXxfbglResVO listXxfbgl(ListXxfbglReqVO listXxfbglReqVO) {
        ListXxfbglResVO res = new ListXxfbglResVO();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("查询消息发布管理列表入参" + listXxfbglReqVO);
        
        /*if(GyUtils.isNotNull(listXxfbglReqVO.getXxzsbt())){
            listXxfbglReqVO.setXxzsbt("%"+listXxfbglReqVO.getXxzsbt()+"%");
        }*/
        /*if (GyUtils.isNull(listXxfbglReqVO.getCzrsfid())) {
            res.setReturnCode("2");
            res.setReturnMsg("操作人身份不能为空");
            return res;
        }*/
        Integer pageNum = Integer.parseInt(listXxfbglReqVO.getPageNum());
        Integer pageSize = Integer.parseInt(listXxfbglReqVO.getPageSize());
        if (pageNum == null || pageNum <= 0 || pageSize == null || pageSize <= 0) {
            res.setReturnCode("4");
            res.setReturnMsg("当前页码或当前页大小不正确");
            return res;
        }
        if (pageSize > 100) {
            res.setReturnCode("4");
            res.setReturnMsg("单页查询数量不能超过100!");
            return res;
        }
        final LambdaQueryWrapper<XxzxXxfbgl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(XxzxXxfbgl::getYxbz, "Y");
        if (GyUtils.isNotNull(listXxfbglReqVO.getCzrsfid())) {
            wrapper.eq(XxzxXxfbgl::getXgrsfid, listXxfbglReqVO.getCzrsfid());
        }
        if (GyUtils.isNotNull(listXxfbglReqVO.getXxflDm())) {
            wrapper.eq(XxzxXxfbgl::getXxflDm, listXxfbglReqVO.getXxflDm());
        }
        if (GyUtils.isNotNull(listXxfbglReqVO.getFbztDm())) {
            wrapper.eq(XxzxXxfbgl::getFbztDm, listXxfbglReqVO.getFbztDm());
        }
        if (GyUtils.isNotNull(listXxfbglReqVO.getXxzsbt())) {
            wrapper.like(XxzxXxfbgl::getXxzsbt, listXxfbglReqVO.getXxzsbt());
        }
        wrapper.orderByDesc(XxzxXxfbgl::getXxyxj).orderByDesc(XxzxXxfbgl::getXxyxqq);
        //查询总记录数
        long pageTotal = xxzxXxfbglMapper.selectCount(wrapper);
        int rowCount = (int) pageTotal;
        //int rowCount = xxzxXxfbMapper.listXxfbglTotalSize(listXxfbglReqVO);
        if (rowCount == 0) {
            res.setReturnCode("5");
            res.setReturnMsg("没有查到对应的数据");
            return res;
        }
        //定义查询起始值
        //Integer startIndex = (pageNum - 1) * pageSize;
        res.setPageNum(pageNum);
        res.setPageSize(pageSize);
        final IPage<XxzxXxfbgl> page = new Page<>(pageNum, pageSize);
        List<XxzxXxfbgl> xxfbglDTOList = xxzxXxfbglMapper.selectPage(page, wrapper).getRecords();
        //List<XxzxXxfbgl> xxfbglDTOList = xxzxXxfbMapper.listXxfbgl(startIndex, listXxfbglReqVO, pageSize);

        List<XtxxFbglVO> xtxxVOList = new ArrayList<>();
        for (XxzxXxfbgl vo : xxfbglDTOList) {
            XtxxFbglVO xtxxvo = new XtxxFbglVO();
            xtxxvo.setXxuuid(vo.getXxfbuuid());
            xtxxvo.setXxflDm(vo.getXxflDm());
            xtxxvo.setXxzsbt(vo.getXxzsbt());
            if (GyUtils.isNotNull(vo.getBqxx())) {
                xtxxvo.setBqxxVOs(getBqxxVOs(vo.getBqxx()));
            }
            xtxxvo.setXxlj(vo.getXxlj());
            xtxxvo.setXxyxqq(sdf.format(vo.getXxyxqq()));
            xtxxvo.setXxyxqz(sdf.format(vo.getXxyxqz()));
            xtxxvo.setSfsytc(vo.getSfsytc());
            xtxxvo.setXxzsfs(vo.getXxzsfs());
            xtxxvo.setZwnrgl(getZwnrgl(vo.getXxnrmb(), vo.getXxnrcs()));
            xtxxVOList.add(xtxxvo);
        }

        res.setData(xtxxVOList);
        res.setTotalSize(rowCount);
        res.setTotalPage(rowCount / pageSize + (rowCount % pageSize != 0 ? 1 : 0));
        res.setReturnCode("1");
        res.setReturnMsg("查询成功");
        log.info("查询消息发布管理列表出参Size" + xtxxVOList.size());
        return res;
    }

    /**
     * @name 组装查询出参XtxxVO
     * @description 相关说明
     * @time 创建时间:2024年05月29日下午08:09:22
     * @param txzxXxfbDTOList
     * @return {@link List<XtxxVO> }
     * @throws IOException
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<XtxxVO> getXtxxVOList(List<XxzxXxYdfb> txzxXxfbDTOList) {
        List<XtxxVO> xtxxVOList = new ArrayList<>();
        for (XxzxXxYdfb vo : txzxXxfbDTOList) {
            XtxxVO xtxxvo = new XtxxVO();
            xtxxvo.setXxuuid(vo.getXxuuid());
            xtxxvo.setXxflDm(vo.getXxflDm());
            xtxxvo.setXxzsbt(vo.getXxzsbt());
            if (GyUtils.isNotNull(vo.getBqxx())) {
                xtxxvo.setBqxxVOs(getBqxxVOs(vo.getBqxx()));
            }
            xtxxvo.setXxyxj(vo.getXxyxj());
            xtxxvo.setJsdxlx(vo.getJsdxlx());
            if ("1" .equals(vo.getJsdxlx())) {
                xtxxvo.setYhuuid(vo.getJsdxz().substring(2));
            } else if ("2" .equals(vo.getJsdxlx())) {
                xtxxvo.setJguuid(vo.getJsdxz().substring(2));
            } else if ("3" .equals(vo.getJsdxlx())) {
                xtxxvo.setZzuuid(vo.getJsdxz().substring(2));
            }
            xtxxvo.setXxlj(vo.getXxlj());
            xtxxvo.setXxyxqq(vo.getXxyxqq());
            xtxxvo.setXxyxqz(vo.getXxyxqz());
            xtxxvo.setSfsytc(vo.getSfsytc());
            xtxxvo.setSfyyd(vo.getSfyyd());
            xtxxvo.setXxzsfs(vo.getXxzsfs());
            xtxxvo.setZwnrgl(getZwnrgl(vo.getXxnrmb(), vo.getXxnrcs()));
            xtxxVOList.add(xtxxvo);
        }

        return xtxxVOList;
    }

    /**
     * @name 组装查询出参XtxxVO
     * @description 相关说明
     * @time 创建时间:2024年05月29日下午08:09:22
     * @param xxfbList
     * @return {@link List<XtxxVO> }
     * @throws IOException
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public XtxxVO getPjXtxxVO(XxzxXxYdfb ydfb,List<XxzxXxfb> xxfbList) {
        XtxxVO xtxxvo = new XtxxVO();
        xtxxvo.setXxuuid(ydfb.getXxuuid());
        xtxxvo.setXxflDm(ydfb.getXxflDm());
        xtxxvo.setXxzsbt(ydfb.getXxzsbt());
        if (GyUtils.isNotNull(ydfb.getBqxx())) {
            xtxxvo.setBqxxVOs(getBqxxVOs(ydfb.getBqxx()));
        }
        xtxxvo.setXxyxj(ydfb.getXxyxj());
        xtxxvo.setJsdxlx(ydfb.getJsdxlx());
        if ("1" .equals(ydfb.getJsdxlx())) {
            xtxxvo.setYhuuid(ydfb.getJsdxz().substring(2));
        } else if ("2" .equals(ydfb.getJsdxlx())) {
            xtxxvo.setJguuid(ydfb.getJsdxz().substring(2));
        } else if ("3" .equals(ydfb.getJsdxlx())) {
            xtxxvo.setZzuuid(ydfb.getJsdxz().substring(2));
        }
        xtxxvo.setXxlj(ydfb.getXxlj());
        xtxxvo.setXxyxqq(ydfb.getXxyxqq());
        xtxxvo.setXxyxqz(ydfb.getXxyxqz());
        xtxxvo.setSfsytc(ydfb.getSfsytc());
        xtxxvo.setSfyyd(ydfb.getSfyyd());
        xtxxvo.setXxzsfs(ydfb.getXxzsfs());
        String msgCtx = "";
        StringBuffer msg = new StringBuffer();
        // 获取头部信息
        // 获取消息头部内容
        String tbNr = getZwnrgl(ydfb.getXxnrmb(), ydfb.getXxnrcs());
        msg.append(tbNr).append("\n");
        int xh = 1;
        for (XxzxXxfb vo : xxfbList) {
            if (ydfb.getXxuuid().equals(vo.getXxuuid())){
                continue;
            }
            msg.append(xh).append(".");
            msg.append(getZwnrgl(vo.getXxnrmb(), vo.getXxnrcs())).append("\n");
            xh = xh + 1;
        }
        if (GyUtils.isNotNull(msg)){
            msgCtx = msg.toString();
        }
        xtxxvo.setZwnrgl(msgCtx);
        return xtxxvo;
    }

    /**
     * @name 组装查询出参XtxxVO
     * @description 相关说明
     * @time 创建时间:2024年05月29日下午08:09:22
     * @param xxfbList
     * @return {@link List<XtxxVO> }
     * @throws IOException
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public String getPjXtxxNr(XxzxXxfb ydfb,List<XxzxXxfb> xxfbList) {
        String msgCtx = "";
        StringBuffer msg = new StringBuffer();
        // 获取头部信息
        // 获取消息头部内容
        String tbNr = getZwnrgl(ydfb.getXxnrmb(), ydfb.getXxnrcs());
        msg.append(tbNr).append("\n");
        int xh = 1;
        for (XxzxXxfb vo : xxfbList) {
            if (ydfb.getXxuuid().equals(vo.getXxuuid())){
                continue;
            }
            msg.append(xh).append(".");
            msg.append(getZwnrgl(vo.getXxnrmb(), vo.getXxnrcs())).append("\n");
            xh = xh + 1;
        }
        if (GyUtils.isNotNull(msg)){
            msgCtx = msg.toString();
        }
        return msgCtx;
    }

    /**
     * @name 将入参bqxx的json串解析为List<BqxxVO>
     * @description 相关说明
     * @time 创建时间:2024年05月29日下午08:09:53
     * @param bqxx
     * @return {@link List<BqxxVO> }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<BqxxVO> getBqxxVOs(String bqxx) {

        final Map<String, List<BqxxVO>> params = (Map<String, List<BqxxVO>>) JSONObject.parse(bqxx);
        return params.get("tags");

    }

    /**
     * @name 将xxanpz的json数据解析为list vo
     * @description 相关说明
     * @time 创建时间:2024年05月31日下午02:17:24
     * @param xxanpz
     * @return {@link List<ButtonVO> }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<ButtonVO> getXxanVOs(String xxanpz) {

        final Map<String, List<ButtonVO>> params = (Map<String, List<ButtonVO>>) JSONObject.parse(xxanpz);
        return params.get("buttons");

    }

    /**
     * @name 根据xxuuid去附件表获取数据，组装List<FjxxVO>
     * @description 相关说明
     * @time 创建时间:2024年05月31日下午02:56:29
     * @param xxuuid
     * @return {@link List<FjxxVO> }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<FjxxVO> getFjxxVOs(String xxuuid) {
        List<FjxxVO> list = new ArrayList<>();
        LambdaQueryWrapper<XxzxXxfjGxb> wrapperGxb = new LambdaQueryWrapper<>();
        wrapperGxb.eq(XxzxXxfjGxb::getXxuuid, xxuuid);
        List<XxzxXxfjGxb> xxfjgxList = xxzxXxfjGxbMapper.selectList(wrapperGxb);

        List<String> fjuuidS = xxfjgxList.stream().map(XxzxXxfjGxb::getFjuuid).collect(Collectors.toList());
        LambdaQueryWrapper<XxzxXxfjDTO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(XxzxXxfjDTO::getFjuuid, fjuuidS);
        List<XxzxXxfjDTO> xxfjList = xxzxXxfjMapper.selectList(wrapper);

        if (GyUtils.isNotNull(xxfjList)) {
            xxfjList.forEach(t -> {
                FjxxVO vo = new FjxxVO();
                vo.setSclx(t.getSclx());
                vo.setFjlx(t.getFjlx());
                vo.setFjmc(t.getFjmc());
                vo.setFilesize(t.getFilesize());
                vo.setFjuuid(t.getFjuuid());
                list.add(vo);
            });
        }
        return list;
    }

    /**
     * @name 根据 消息内容模板和 消息json参数，获取正文内容概览
     * @description 相关说明
     * @time 创建时间:2024年05月30日上午10:00:36
     * @param xxnrmb
     * @param xxnrcs
     * @return {@link String }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public String getZwnrgl(String xxnrmb, String xxnrcs) {
        if (GyUtils.isNull(xxnrmb) || GyUtils.isNull(xxnrcs)) {
            return "";
        } else {
            //json校验
            try {
                JSONObject.parseObject(xxnrcs, Map.class);
            } catch (Exception e) {
                log.error(e.getMessage());
                return "";
            }
            //freemark 模板填充
            ErrorListVO vo = xxnrByXxcs(xxnrmb, xxnrcs);
            if ("1" .equals(vo.getErrorCode())) {
                final String xxnr = vo.getJguuid();
                //同时去除空格和换行符
                return xxnr.replaceAll("\\s+|\\n", "");
            }
        }
        return "";
    }

    //查询自然人消息列表
    @Override
    public ListZrrXtxxResVO listZrrXtxx(ListZrrxtxxReqVO listZrrxtxxReqVO) throws IOException {
        ListZrrXtxxResVO listZrrXtxxResVO = new ListZrrXtxxResVO();
        log.info("查询自然人消息列表入参" + listZrrxtxxReqVO);
        if (GyUtils.isNull(listZrrxtxxReqVO.getZrrwlsfid())) {
            listZrrXtxxResVO.setReturnCode("2");
            listZrrXtxxResVO.setReturnMsg("自然人网络身份ID不能为空");
            return listZrrXtxxResVO;
        }
        if (GyUtils.isNull(listZrrxtxxReqVO.getPageNum()) || GyUtils.isNull(listZrrxtxxReqVO.getPageSize())) {
            listZrrxtxxReqVO.setPageNum("1");
            listZrrxtxxReqVO.setPageSize("10");
        }
        Integer pageNum = Integer.parseInt(listZrrxtxxReqVO.getPageNum());
        Integer pageSize = Integer.parseInt(listZrrxtxxReqVO.getPageSize());
        if (pageNum == null || pageNum <= 0 || pageSize == null || pageSize <= 0) {
            listZrrXtxxResVO.setReturnCode("4");
            listZrrXtxxResVO.setReturnMsg("当前页码或当前页大小不正确");
            return listZrrXtxxResVO;
        }
        //查询总记录数
        int rowCount = txzxXtxxfbMapper.listZrrXxfbTotalSize(listZrrxtxxReqVO);
        if (rowCount == 0) {
            listZrrXtxxResVO.setReturnCode("5");
            listZrrXtxxResVO.setReturnMsg("没有查到对应数据");
            return listZrrXtxxResVO;
        }
        //定义查询起始值
        Integer startIndex = (pageNum - 1) * pageSize;
        List<TxzxZrrXxfbDTO> txzxZrrXxfbDTOS = txzxXtxxfbMapper.listZrrXxfb(startIndex, listZrrxtxxReqVO, pageSize);
        List<ZrrXtxxVO> zrrXtxxVOLists = zrrXtxxVOList(txzxZrrXxfbDTOS);
        listZrrXtxxResVO.setData(zrrXtxxVOLists);
        listZrrXtxxResVO.setPageNum(pageNum);
        listZrrXtxxResVO.setPageSize(pageSize);
        listZrrXtxxResVO.setTotalSize(rowCount);
        listZrrXtxxResVO.setTotalPage(rowCount / pageSize + (rowCount % pageSize != 0 ? 1 : 0));
        listZrrXtxxResVO.setReturnCode("1");
        listZrrXtxxResVO.setReturnMsg("查询成功");
        log.info("查询自然人消息列表入参Size" + zrrXtxxVOLists);

        return listZrrXtxxResVO;
    }

    //修改纳税人阅读状态
    @Override
    public UpdateNsrXtxxYdztResVO updateNsrXtxxYdzt(UpdateXtxxYdztReqVO updateXtxxYdztReqVO) {
        UpdateNsrXtxxYdztResVO updateNsrXtxxYdztResVO = new UpdateNsrXtxxYdztResVO();
        //  BeanUtils.copyProperties(updateNsrXtxxYdztReqVO,updateXtxxYdztReqVO);
        int ydbz = txzxXtxxfbMapper.updateNsrXtxxYdzt(updateXtxxYdztReqVO);
        if (ydbz > 0) {
            updateNsrXtxxYdztResVO.setReturnCode("1");
            updateNsrXtxxYdztResVO.setReturnMsg("修改成功");
        } else {
            updateNsrXtxxYdztResVO.setReturnCode("2");
            updateNsrXtxxYdztResVO.setReturnMsg("修改失败");
        }
        return updateNsrXtxxYdztResVO;
    }

    //修改自然人阅读状态
    @Override
    public UpdateZrrXtxxYdztResVO updateZrrXtxxYdzt(UpdateXtxxYdztReqVO updateXtxxYdztReqVO) {
        UpdateZrrXtxxYdztResVO updateZrrXtxxYdztResVO = new UpdateZrrXtxxYdztResVO();
        int ydbz = txzxXtxxfbMapper.updateZrrXtxxYdzt(updateXtxxYdztReqVO);
        if (ydbz > 0) {
            updateZrrXtxxYdztResVO.setReturnCode("1");
            updateZrrXtxxYdztResVO.setReturnMsg("修改成功");
        } else {
            updateZrrXtxxYdztResVO.setReturnCode("2");
            updateZrrXtxxYdztResVO.setReturnMsg("修改失败");
        }
        return updateZrrXtxxYdztResVO;
    }

    @Override
    public int saveNsrXtxxYdzt(TxzxXxYdztVO txzxXxYdztVO) {
        int res = txzxXtxxfbMapper.saveNsrXtxxYdzt(txzxXxYdztVO);
        return res;
    }


    @Override
    public UpdateXtxxYdztResVO updateXtxxYdzt(UpdateXtxxYdztReqVO updateXtxxYdztReqVO) {
        log.info("对外提供更新消息阅读状态入参" + updateXtxxYdztReqVO);
        UpdateXtxxYdztResVO updateXtxxYdztResVO = new UpdateXtxxYdztResVO();
        int ydbz = 0;
        //接收人身份类型=1，纳税人；接收人身份类型=2，自然人
        if ("1" .equals(updateXtxxYdztReqVO.getJsrlx())) {
            if (GyUtils.isNull(updateXtxxYdztReqVO.getDjxh()) || GyUtils.isNull(updateXtxxYdztReqVO.getFrwlsfid())) {
                updateXtxxYdztResVO.setReturnCode("4");
                updateXtxxYdztResVO.setReturnMsg("登记序号或法人网络身份ID不可空");
                return updateXtxxYdztResVO;
            }
            String uuid = GyUtils.getUuid();
            TxzxXxYdztVO txzxXxYdztVO = new TxzxXxYdztVO();
            txzxXxYdztVO.setUuid(uuid);
            txzxXxYdztVO.setXxuuid(updateXtxxYdztReqVO.getXxuuid());
            txzxXxYdztVO.setFrwlsfid(updateXtxxYdztReqVO.getFrwlsfid());
            txzxXxYdztVO.setDjxh(updateXtxxYdztReqVO.getDjxh());
            txzxXxYdztVO.setZrrwlsfid(updateXtxxYdztReqVO.getZrrwlsfid());
            txzxXxYdztVO.setSfyyd("Y");
            txzxXxYdztVO.setYxbz("Y");
            txzxXxYdztVO.setLrrsfid(updateXtxxYdztReqVO.getFrwlsfid());
            txzxXxYdztVO.setXgrsfid(updateXtxxYdztReqVO.getFrwlsfid());
            txzxXxYdztVO.setYwqdDm(txzxGyUtils.getYwqddm(updateXtxxYdztReqVO.getYwqdDm()));
            //获取数据归属地区和数据产生地区
            Map<String, Object> sjdq = cssDmcsService.getIndexData("CS_MH_XTCS", "MH000000000000002");
            txzxXxYdztVO.setSjcsdq(String.valueOf(sjdq.get("SJXX")));
            txzxXxYdztVO.setSjgsdq(String.valueOf(sjdq.get("SJXX")));
            txzxXxYdztVO.setYdrsfhj(null);
            txzxXxYdztVO.setLjs("1001");
            log.info("start sync add ydzt：" + System.currentTimeMillis());
            ydbz = txzxXtxxfbMapper.saveNsrXtxxYdzt(txzxXxYdztVO);
        } else {
            ydbz = txzxXtxxfbMapper.updateZrrXtxxYdzt(updateXtxxYdztReqVO);
        }
        if (ydbz > 0) {
            updateXtxxYdztResVO.setReturnCode("1");
            updateXtxxYdztResVO.setReturnMsg("修改成功");
        } else {
            updateXtxxYdztResVO.setReturnCode("2");
            updateXtxxYdztResVO.setReturnMsg("修改失败");
        }
        return updateXtxxYdztResVO;
    }

    //查询纳税人、自然人消息详情
    @Override
    public GetXxtxDetailsResVO getXxtxDetails(GetXxtxDetailsReqVO getXxtxDetailsReqVO) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        GetXxtxDetailsResVO getXxtxDetailsResVO = new GetXxtxDetailsResVO();
        log.info("打印查询消息详情入参" + getXxtxDetailsReqVO);
        log.info("查询消息详情开始时间：" + System.currentTimeMillis());
        long str = System.currentTimeMillis();
        final LambdaQueryWrapper<XxzxXxfb> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(XxzxXxfb::getXxuuid, getXxtxDetailsReqVO.getXxuuid());
        wrapper.eq(XxzxXxfb::getYxbz, "Y");
        XxzxXxfb xxfbDTO = xxzxXxfbMapper.selectOne(wrapper);
        //TxzxNsrXxfbDTO txzxNsrXxfbDTO = xxzxXxfbMapper.getNsrXxFb(getXxtxDetailsReqVO.getXxuuid());
        if (GyUtils.isNull(xxfbDTO)) {
            getXxtxDetailsResVO.setReturnCode("5");
            getXxtxDetailsResVO.setReturnMsg("未查到详情数据");
            return getXxtxDetailsResVO;
        } else {
            Map<String, Object> txzxXxNrmbmap = txzxGyUtils.queryXxnrmbCache(xxfbDTO.getXxmbbh(), getXxtxDetailsReqVO.getJsqd());
            if (GyUtils.isNull(txzxXxNrmbmap)) {
                getXxtxDetailsResVO.setReturnCode("2");
                getXxtxDetailsResVO.setReturnMsg("查询失败");
                return getXxtxDetailsResVO;
            } else {
                if ("00000000000_00000024".equals(xxfbDTO.getXxmbbh()) || "00000000000_00000025".equals(xxfbDTO.getXxmbbh())){
                    List<XxzxXxfb> xxfbList = xxzxXxfbMapper.listXxfbByYwzj(xxfbDTO.getYwzj());
                    if (GyUtils.isNotNull(xxfbList) && xxfbList.size() > 1){
                        getXxtxDetailsResVO.setXxnr(getPjXtxxNr(xxfbDTO,xxfbList));
                    }
                }else {
                    String txzxXxNrmb = String.valueOf(txzxXxNrmbmap.get("xxnrmb"));
                    ErrorListVO vo = xxnrByXxcs(txzxXxNrmb, xxfbDTO.getXxnrcs());
                    getXxtxDetailsResVO.setXxnr(vo.getJguuid());
                }
                getXxtxDetailsResVO.setXxflDm(xxfbDTO.getXxflDm());
                getXxtxDetailsResVO.setXxzsbt(xxfbDTO.getXxzsbt());
                getXxtxDetailsResVO.setXxlj(xxfbDTO.getXxlj());
                getXxtxDetailsResVO.setXxzsfs(xxfbDTO.getXxzsfs());
                if (GyUtils.isNotNull(xxfbDTO.getBqxx())) {
                    getXxtxDetailsResVO.setBqxxVOs(getBqxxVOs(xxfbDTO.getBqxx()));
                }
                if (GyUtils.isNotNull(xxfbDTO.getXxanpz())) {
                    ErrorListVO vo1 = xxnrByXxcs(xxfbDTO.getXxanpz(), xxfbDTO.getXxnrcs());
                    getXxtxDetailsResVO.setXxanpz(getXxanVOs(vo1.getJguuid()));
                }
                getXxtxDetailsResVO.setXxyxqq(sdf.format(xxfbDTO.getXxyxqq()));
                getXxtxDetailsResVO.setXxyxqz(sdf.format(xxfbDTO.getXxyxqz()));
                getXxtxDetailsResVO.setJsdxz(xxfbDTO.getJsdxz());
                if ("Y" .equals(xxfbDTO.getSfyfj())) {//"Y".equals(xxfbDTO.getSfbhfj)
                    getXxtxDetailsResVO.setWjxxjh(getFjxxVOs(xxfbDTO.getXxuuid()));
                }
                getXxtxDetailsResVO.setJsdxlx(xxfbDTO.getJsdxlx());
                if ("1" .equals(xxfbDTO.getJsdxlx())) {
                    getXxtxDetailsResVO.setYhuuid(xxfbDTO.getJsdxz().substring(2));
                } else if ("2" .equals(xxfbDTO.getJsdxlx())) {
                    getXxtxDetailsResVO.setJguuid(xxfbDTO.getJsdxz().substring(2));
                } else if ("3" .equals(xxfbDTO.getJsdxlx())) {
                    getXxtxDetailsResVO.setZzuuid(xxfbDTO.getJsdxz().substring(2));
                } else if ("4" .equals(xxfbDTO.getJsdxlx())) {
                    getXxtxDetailsResVO.setGh(xxfbDTO.getJsdxz().substring(2));
                }
                getXxtxDetailsResVO.setFbr(xxfbDTO.getFbr1());//20240820加
                getXxtxDetailsResVO.setLrrsfid(xxfbDTO.getLrrsfid());
                getXxtxDetailsResVO.setReturnCode("1");
                getXxtxDetailsResVO.setReturnMsg("查询成功");
            }
        }
        log.info("查询消息详情结束用时：" + (System.currentTimeMillis() - str) + "毫秒");
        return getXxtxDetailsResVO;
    }

    /**
     *
     * @param getXxfbglDetailsReqVO
     * @return
     */
    @Override
    public GetXxfbglDetailsResVO getXxfbglDetails(GetXxfbglDetailsReqVO getXxfbglDetailsReqVO) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        GetXxfbglDetailsResVO res = new GetXxfbglDetailsResVO();
        log.info("打印查询消息发布管理详情入参" + getXxfbglDetailsReqVO);
        log.info("查询消息发布管理详情开始时间：" + System.currentTimeMillis());
        long str = System.currentTimeMillis();
        final LambdaQueryWrapper<XxzxXxfbgl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(XxzxXxfbgl::getXxfbuuid, getXxfbglDetailsReqVO.getXxfbgluuid());
        wrapper.eq(XxzxXxfbgl::getYxbz, "Y");
        XxzxXxfbgl xxfbglDTO = xxzxXxfbglMapper.selectOne(wrapper);

        //TxzxNsrXxfbDTO txzxNsrXxfbDTO = xxzxXxfbMapper.getNsrXxFb(getXxtxDetailsReqVO.getXxuuid());
        if (GyUtils.isNull(xxfbglDTO)) {
            res.setReturnCode("5");
            res.setReturnMsg("未查到详情数据");
            return res;
        } else {
            List<String> zzuuidList = new ArrayList<>();
            if ("0301" .equals(xxfbglDTO.getXxflDm())) {
                //  通知公告，需要获取 zzuuid list
                final LambdaQueryWrapper<XxzxXxfbglJsdx> wrapperJsdx = new LambdaQueryWrapper<>();
                wrapperJsdx.eq(XxzxXxfbglJsdx::getXxfbuuid, getXxfbglDetailsReqVO.getXxfbgluuid());
                List<XxzxXxfbglJsdx> xxzxXxfbglJsdxList = xxzxXxfbglJsdxMapper.selectList(wrapperJsdx);
                if (GyUtils.isNull(xxzxXxfbglJsdxList)) {
                    res.setReturnCode("5");
                    res.setReturnMsg("未查到详情数据");
                    return res;
                } else {
                    xxzxXxfbglJsdxList.forEach(t -> {
                        zzuuidList.add(t.getJsdxz().substring(2));
                    });
                    /*for(XxzxXxfbglJsdx  vo : xxzxXxfbglJsdxList ){
                        zzuuidList.add(vo.getJsdxz().substring(2));
                    }*/
                    //zzuuidList = xxzxXxfbglJsdxList.stream().map(XxzxXxfbglJsdx::getJsdxz).collect(Collectors.toList());
                }
            }

            Map<String, Object> txzxXxNrmbmap = txzxGyUtils.queryXxnrmbCache(xxfbglDTO.getXxmbbh(), "01");
            if (GyUtils.isNull(txzxXxNrmbmap)) {
                res.setReturnCode("2");
                res.setReturnMsg("查询失败");
                return res;

            } else {
                String txzxXxNrmb = String.valueOf(txzxXxNrmbmap.get("xxnrmb"));
                ErrorListVO vo = xxnrByXxcs(txzxXxNrmb, xxfbglDTO.getXxnrcs());
                res.setXxnr(vo.getJguuid());

                res.setXxflDm(xxfbglDTO.getXxflDm());
                res.setXxzsbt(xxfbglDTO.getXxzsbt());
                res.setXxlj(xxfbglDTO.getXxlj());
                res.setXxzsfs(xxfbglDTO.getXxzsfs());
                if (GyUtils.isNotNull(xxfbglDTO.getBqxx())) {
                    res.setBqxxVOs(getBqxxVOs(xxfbglDTO.getBqxx()));
                }
                if (GyUtils.isNotNull(xxfbglDTO.getXxanpz())) {
                    res.setXxanpz(getXxanVOs(xxfbglDTO.getXxanpz()));
                }
                res.setXxyxqq(sdf.format(xxfbglDTO.getXxyxqq()));
                res.setXxyxqz(sdf.format(xxfbglDTO.getXxyxqz()));
                res.setXxyxj(xxfbglDTO.getXxyxj());
                if ("Y" .equals(xxfbglDTO.getSfyfj())) {//"Y".equals(xxfbDTO.getSfbhfj)
                    res.setWjxxjh(getFjxxVOs(xxfbglDTO.getXxfbuuid()));
                }
                res.setZzuuidList(zzuuidList);
                res.setFbr(xxfbglDTO.getFbr1());//20240820加
                res.setReturnCode("1");
                res.setReturnMsg("查询成功");
            }
        }
        log.info("查询消息发布管理详情结束用时：" + (System.currentTimeMillis() - str) + "毫秒");
        return res;
    }



    @Override
    public GetXtxxCountResVO getXtxxWdsl(GetXtxxCountReqVO getXtxxCountReqVO) {
        log.info("打印查询系统消息未读数量入参" + getXtxxCountReqVO);
        log.info("查询系统消息未读数量开始时间：" + System.currentTimeMillis());
        long str = System.currentTimeMillis();
        GetXtxxCountResVO getXtxxCountResVO = new GetXtxxCountResVO();

        if (GyUtils.isNull(getXtxxCountReqVO.getJsqd())) {
            getXtxxCountResVO.setReturnCode("2");
            getXtxxCountResVO.setReturnMsg("消息接收渠道不能为空");
            return getXtxxCountResVO;
        }
        /*if (GyUtils.isNull(listXtxxReqVO.getJguuidList())) {
            listXtxxResVO.setReturnCode("2");
            listXtxxResVO.setReturnMsg("机构ID不能为空");
            return listXtxxResVO;
        }
        if (GyUtils.isNull(listXtxxReqVO.getYhuuid())) {
            listXtxxResVO.setReturnCode("2");
            listXtxxResVO.setReturnMsg("用户ID不能为空");
            return listXtxxResVO;
        }
        if (GyUtils.isNull(listXtxxReqVO.getZzuuid())) {
            listXtxxResVO.setReturnCode("2");
            listXtxxResVO.setReturnMsg("组织ID不能为空");
            return listXtxxResVO;
        }*/
        RqDateVO rqvo = new RqDateVO();
        rqvo.setDqrq(DateUtils.getSystemCurrentTime().getTime());
        //jguuid集合、zzuuid、yhuuid拼成一个list进行后台数据校验
        List<String> yhidList = getXtxxCountReqVO.getYhuuidList();
        List<String> jgidList = getXtxxCountReqVO.getJguuidList();
        List<String> zzidList = getXtxxCountReqVO.getZzuuidList();
        List<String> ghList = getXtxxCountReqVO.getGhList();
        List<String> jsdxzList = getJsdxzList(yhidList, jgidList, zzidList, ghList);
        jsdxzList.add("all");//公开情况永远可以查到
        List<XxflCountVO> xxflCountVOs = new ArrayList<>();
        //ListXtxxReqVO cxvo = new ListXtxxReqVO();
        //cxvo.setJsqd(getXtxxCountReqVO.getJsqd());
        if (GyUtils.isNull(getXtxxCountReqVO.getXxflDm())) {
            //cxvo.setXxflDm(getXtxxCountReqVO.getXxflDm());
            int total = xxzxXxfbMapper.listXxfbTotalSizeWdSL(jsdxzList, getXtxxCountReqVO, rqvo);
            if (total > 0) {
                xxflCountVOs = xxzxXxfbMapper.listXxfbXxflfzWD(jsdxzList, getXtxxCountReqVO.getJsqd(), getXtxxCountReqVO.getLrrsfid(), rqvo);
                if (GyUtils.isNotNull(xxflCountVOs)) {
                    getXtxxCountResVO.setAllCount(GYCastUtils.cast2Str(total));
                    getXtxxCountResVO.setXxflCountVOs(xxflCountVOs);
                    getXtxxCountResVO.setReturnCode("1");
                    getXtxxCountResVO.setReturnMsg("查询成功");
                }
            } else {
                List<Map<String, Object>> xxflDmList = CacheUtils.getTableData("dm_xxzx_xxfl");
                if (GyUtils.isNotNull(xxflDmList)) {
                    for (Map<String, Object> xxflMap : xxflDmList) {
                        XxflCountVO vo = new XxflCountVO();
                        vo.setXxflDm((String) xxflMap.get("xxflDm"));
                        vo.setCount("0");
                        xxflCountVOs.add(vo);
                    }
                    getXtxxCountResVO.setXxflCountVOs(xxflCountVOs);
                }

                getXtxxCountResVO.setAllCount("0");
                getXtxxCountResVO.setReturnCode("5");
                getXtxxCountResVO.setReturnMsg("没有查到对应的数据");
                return getXtxxCountResVO;
            }
        } else {
            //cxvo.setXxflDm(getXtxxCountReqVO.getXxflDm());
            int total = xxzxXxfbMapper.listXxfbTotalSizeWdSL(jsdxzList, getXtxxCountReqVO, rqvo);
            if (total > 0) {
                XxflCountVO xxflVO = new XxflCountVO();
                xxflVO.setXxflDm(getXtxxCountReqVO.getXxflDm());
                xxflVO.setCount(GYCastUtils.cast2Str(total));
                xxflCountVOs.add(xxflVO);
                getXtxxCountResVO.setXxflCountVOs(xxflCountVOs);
                getXtxxCountResVO.setReturnCode("1");
                getXtxxCountResVO.setReturnMsg("查询成功");
            } else {
                XxflCountVO xxflVO = new XxflCountVO();
                xxflVO.setXxflDm(getXtxxCountReqVO.getXxflDm());
                xxflVO.setCount("0");
                xxflCountVOs.add(xxflVO);
                getXtxxCountResVO.setXxflCountVOs(xxflCountVOs);
                getXtxxCountResVO.setAllCount("0");
                getXtxxCountResVO.setReturnCode("5");
                getXtxxCountResVO.setReturnMsg("没有查到对应的数据");
                return getXtxxCountResVO;
            }
        }
        log.info("查询系统消息未读数量结束用时：" + (System.currentTimeMillis() - str) + "毫秒");
        return getXtxxCountResVO;
    }

    /**
     * @name 中文名称
     * @description 相关说明
     * @time 创建时间:2024年06月01日上午11:35:12
     * @param updateYdztReqVO
     * @return {@link UpdateYdztResVO }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public UpdateYdztResVO updateYdzt(UpdateYdztReqVO updateYdztReqVO) {
        UpdateYdztResVO updateYdztResVO = new UpdateYdztResVO();
        if (GyUtils.isNull(updateYdztReqVO.getJsqd())) {
            updateYdztResVO.setReturnCode("2");
            updateYdztResVO.setReturnMsg("接收渠道不可为空");
            return updateYdztResVO;
        }
        RqDateVO rqvo = new RqDateVO();
        rqvo.setDqrq(DateUtils.getSystemCurrentTime().getTime());

        List<String> jsdxzList = getJsdxzList(updateYdztReqVO.getYhuuidList(), updateYdztReqVO.getJguuidList(), updateYdztReqVO.getZzuuidList(), updateYdztReqVO.getGhList());
        jsdxzList.add("all");//公开情况永远可以查到
        List<XxzxXxfb> xxfbList = xxzxXxfbMapper.queryXxuuidListWd(updateYdztReqVO, jsdxzList, rqvo);

        if (GyUtils.isNull(xxfbList)) {

            updateYdztResVO.setReturnCode("5");
            updateYdztResVO.setReturnMsg("没有查到未阅读消息，无法更新阅读状态");
            return updateYdztResVO;

        } else {
            List<UpdateYdztDTO> xxuuidList = new ArrayList<>();
            for (XxzxXxfb vo : xxfbList) {
                UpdateYdztDTO dto = new UpdateYdztDTO();
                dto.setLrrsfid(updateYdztReqVO.getLrrsfid());
                dto.setXxuuid(vo.getXxuuid());
                xxuuidList.add(dto);
            }
            final List<XxzxXxydztDTO> list = getXxydztDTOlist(xxuuidList, updateYdztReqVO.getJsqd());
            xxzxXxydztService.saveBatch(list);
            updateYdztResVO.setReturnCode("1");
            updateYdztResVO.setReturnMsg("更新成功");
        }
        return updateYdztResVO;
    }

    /**
     * @name 中文名称
     * @description 作废公告信息
     * @time 创建时间:2024年06月03日上午11:47:51
     * @param deleteGgxxReqVO
     * @return {@link DeleteGgxxResVO }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public DeleteGgxxResVO deleteGgxx(DeleteGgxxReqVO deleteGgxxReqVO) {
        DeleteGgxxResVO deleteGgxxResVO = checkDeleteGgxxJy(deleteGgxxReqVO);
        if (GyUtils.isNotNull(deleteGgxxResVO) && GyUtils.isNotNull(deleteGgxxResVO.getReturnMsg())) {
            return deleteGgxxResVO;
        }
        final LambdaQueryWrapper<XxzxXxfb> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(XxzxXxfb::getXxuuid, deleteGgxxReqVO.getXxuuid());
        wrapper.eq(XxzxXxfb::getXxflDm, deleteGgxxReqVO.getXxflDm());
        XxzxXxfb xxfb = xxzxXxfbMapper.selectOne(wrapper);

        if (GyUtils.isNull(xxfb)) {
            deleteGgxxResVO.setReturnCode("5");
            deleteGgxxResVO.setReturnMsg("无此消息，无法作废");
            return deleteGgxxResVO;
        }

        if ((deleteGgxxReqVO.getXxmbbh()).equals(xxfb.getXxmbbh())) {
            final LambdaUpdateWrapper<XxzxXxfb> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(XxzxXxfb::getXxuuid, deleteGgxxReqVO.getXxuuid());
            updateWrapper.set(XxzxXxfb::getYxbz, "N");
            updateWrapper.set(XxzxXxfb::getXgrsfid, deleteGgxxReqVO.getCzrsfid());
            updateWrapper.set(XxzxXxfb::getXgrq, new Date());
            int i = xxzxXxfbMapper.update(updateWrapper);
            if (i > 0) {
                deleteGgxxResVO.setReturnCode("1");
                deleteGgxxResVO.setReturnMsg("作废成功");
            } else {
                deleteGgxxResVO.setReturnCode("3");
                deleteGgxxResVO.setReturnMsg("作废失败");
            }
        } else {
            deleteGgxxResVO.setReturnCode("2");
            deleteGgxxResVO.setReturnMsg("消息与模板不匹配，无法作废");
            return deleteGgxxResVO;
        }
        return deleteGgxxResVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UploadFjResVO uploadFj(UploadFjReqVO uploadFjReqVO) {
        UploadFjResVO uploadFjResVO = checkUploadFjJY(uploadFjReqVO);

        if (GyUtils.isNotNull(uploadFjResVO) && GyUtils.isNotNull(uploadFjResVO.getReturnMsg())) {
            return uploadFjResVO;
        }

        final String fileData = uploadFjReqVO.getFileData();
        final String fjuuid = GyUtils.getUuid();
//        String buket = "qyd";
        /*if("03".equals(uploadFjReqVO.getSclx())){
            buket = "qyd-xxzx";
        }*/
        //DecimalFormat decimalFormat = new DecimalFormat("#.##"); // 保留一位小数，但不显示不必要的尾随零  
        final double fjsize = Math.ceil(TxzxGyUtils.decode(fileData).length / 1024.0);
        //final String fjsize = decimalFormat.format(filesize);

        XxzxXxfjDTO dto = new XxzxXxfjDTO();
        dto.setFjuuid(fjuuid);
        dto.setSclx(uploadFjReqVO.getSclx());
        dto.setFjmc(uploadFjReqVO.getFjmc());
        dto.setFjlx(uploadFjReqVO.getFjlx());
        dto.setFilesize(GyUtils.cast2Str(fjsize));
        dto.setCcfs("01");  //01 消息中心存储   02 外部存储  走此接口都是01
        dto.setYxbz("Y");
        dto.setLrrsfid(uploadFjReqVO.getCzrsfid());
        dto.setLrrq(new Date());
        dto.setYwqdDm("ZNSB.MHZC");
        dto.setSjcsdq("00000000000");
        dto.setSjgsdq("00000000000");

        int i = xxzxXxfjMapper.insert(dto);
        if (i > 0) {
            ZnsbFileUtils.upload(TxzxGyUtils.decode(fileData), fjuuid, uploadFjReqVO.getFjlx());
//            ZnsbFileUtils.upload(TxzxGyUtils.decode(fileData), fjuuid, uploadFjReqVO.getFjlx(),bucket);
//            minioUtils.uploadFile(fjuuid,new ByteArrayInputStream(TxzxGyUtils.decode(fileData)),uploadFjReqVO.getFjlx());
            uploadFjResVO.setFjuuid(fjuuid);
            uploadFjResVO.setFjsize(GyUtils.cast2Str(fjsize));
            uploadFjResVO.setReturnCode("1");
            uploadFjResVO.setReturnMsg("上传文件成功");
        } else {
            uploadFjResVO.setReturnCode("2");
            uploadFjResVO.setReturnMsg("上传文件失败");
        }
        return uploadFjResVO;
    }

    /**
     * @name 中文名称
     * @description 获取附件
     * @time 创建时间:2024年06月04日上午09:10:47
     * @param getFjxxReqVO
     * @return {@link GetFjxxResVO }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public GetFjxxResVO getFjxx(GetFjxxReqVO getFjxxReqVO) {
        GetFjxxResVO getFjxxResVO = new GetFjxxResVO();
        if (GyUtils.isNull(getFjxxReqVO.getFjuuid())) {
            getFjxxResVO.setReturnCode("2");
            getFjxxResVO.setReturnMsg("缺少附件UUID");
            return getFjxxResVO;
        }
//        InputStream inputStream = minioUtils.downloadFile(getFjxxReqVO.getFjuuid());
//        final byte[] bytes = TxzxGyUtils.decode(MhzcGyUtils.getImageStr(inputStream));
        //此接口固定为 消息中心的bucket
        final byte[] bytes = ZnsbFileUtils.getContent(getFjxxReqVO.getFjuuid());
//        final byte[] bytes = ZnsbFileUtils.getContent(getFjxxReqVO.getFjuuid(), bucket);
        if (GyUtils.isNull(bytes)) {
            getFjxxResVO.setReturnCode("5");
            getFjxxResVO.setReturnMsg("未查询到对应附件");
            return getFjxxResVO;
        }
        getFjxxResVO.setFileData(Base64Utils.encodeToString(bytes));
        getFjxxResVO.setReturnCode("1");
        getFjxxResVO.setReturnMsg("下载成功");

        return getFjxxResVO;
    }

    private UploadFjResVO checkUploadFjJY(UploadFjReqVO uploadFjReqVO) {
        UploadFjResVO vo = new UploadFjResVO();
        final String fjlx = "application/doc,application/docx,application/xls,application/xlsx,application/pdf";//后期弄系统参数
        if (GyUtils.isNull(uploadFjReqVO.getFjlx())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少附件类型");
            return vo;
        } else {
            if (!fjlx.contains(uploadFjReqVO.getFjlx())) {
                vo.setReturnCode("2");
                vo.setReturnMsg("此附件类型不支持");
                return vo;
            }
        }
        if (GyUtils.isNull(uploadFjReqVO.getSclx())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少上传类型");
            return vo;
        }
        if (GyUtils.isNull(uploadFjReqVO.getFjmc())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少附件名称");
            return vo;
        }
        if (GyUtils.isNull(uploadFjReqVO.getCzrsfid())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少操作人身份ID");
            return vo;
        }
        if (GyUtils.isNull(uploadFjReqVO.getFileData())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少文件编码数据");
            return vo;
        }
        return vo;
    }

    public DeleteGgxxResVO checkDeleteGgxxJy(DeleteGgxxReqVO deleteGgxxReqVO) {
        DeleteGgxxResVO vo = new DeleteGgxxResVO();
        final String xxflDm = deleteGgxxReqVO.getXxflDm();
        if (!"0302" .equals(xxflDm) || GyUtils.isNull(xxflDm)) {
            vo.setReturnCode("2");
            vo.setReturnMsg("xxflDM必传，且只支持升级公告类型0302！");
            return vo;
        }
        if (GyUtils.isNull(deleteGgxxReqVO.getXxuuid())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少消息UUid");
            return vo;
        }
        if (GyUtils.isNull(deleteGgxxReqVO.getJsqd())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少接收渠道");
            return vo;
        }
        if (GyUtils.isNull(deleteGgxxReqVO.getYwxtid())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少业务系统id");
            return vo;
        }
        if (GyUtils.isNull(deleteGgxxReqVO.getYwxtid())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少业务系统id");
            return vo;
        }
        if (GyUtils.isNull(deleteGgxxReqVO.getYwxtfwkl())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少业务系统访问令牌");
            return vo;
        }
        if (GyUtils.isNull(deleteGgxxReqVO.getCzrsfid())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少操作人人身份id");
            return vo;
        }
        if (!txzxGyUtils.checkYwxtJrsf(deleteGgxxReqVO.getYwxtid(), deleteGgxxReqVO.getYwxtfwkl())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("应用ID和令牌检验失败，请检查或联系运维刷新ywstms缓存");
            return vo;
        }
        if (GyUtils.isNull(deleteGgxxReqVO.getXxmbbh())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少消息模板编号");
            return vo;
        }
        return vo;
    }

    /**
     * @param yhidList
     * @param jgidList
     * @param zzidList
     * @param ghList
     * @return {@link List<String> }
     * @name 中文名称
     * @description 根据入参获取  jsdxzList
     * @time 创建时间:2024年06月03日上午10:02:32
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<String> getJsdxzList(List<String> yhidList, List<String> jgidList, List<String> zzidList, List<String> ghList) {
        List<String> jsdxzList = new ArrayList<>();
        if (GyUtils.isNotNull(yhidList)) {
            yhidList.forEach(t -> {
                jsdxzList.add("1_" + t);
            });
        }
        if (GyUtils.isNotNull(jgidList)) {
            jgidList.forEach(t -> {
                jsdxzList.add("2_" + t);
            });
        }
        if (GyUtils.isNotNull(zzidList)) {
            zzidList.forEach(t -> {
                jsdxzList.add("3_" + t);
            });
        }
        if (GyUtils.isNotNull(ghList)) {
            ghList.forEach(t -> {
                jsdxzList.add("4_" + t);
            });
        }
        return jsdxzList;
    }

    /**
     * @name 中文名称
     * @description 批量组装阅读状态表DTO进行插入操作
     * @time 创建时间:2024年06月01日下午05:07:22
     * @param xxuuidList
     * @param jsqd
     * @return {@link List<XxzxXxydztDTO> }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<XxzxXxydztDTO> getXxydztDTOlist(List<UpdateYdztDTO> xxuuidList, String jsqd) {
        final List<XxzxXxydztDTO> list = new ArrayList<>();
        for (UpdateYdztDTO map : xxuuidList) {
            XxzxXxydztDTO txzxXxYdztVO = new XxzxXxydztDTO();
            txzxXxYdztVO.setUuid(GyUtils.getUuid());
            txzxXxYdztVO.setXxuuid(map.getXxuuid());
            txzxXxYdztVO.setYhid(map.getLrrsfid());
            txzxXxYdztVO.setSfyyd("Y");
            txzxXxYdztVO.setYxbz("Y");
            txzxXxYdztVO.setLrrsfid(map.getLrrsfid());
            txzxXxYdztVO.setLrrq(new Date());
            if ("01" .equals(jsqd)) {
                txzxXxYdztVO.setYwqdDm("ZNSB.MHZC");
            } else {
                txzxXxYdztVO.setYwqdDm(jsqd);
            }
            txzxXxYdztVO.setSjcsdq("00000000000");
            txzxXxYdztVO.setSjgsdq("00000000000");
            list.add(txzxXxYdztVO);
        }
        return list;
    }

    /**
     * @name 中文名称
     * @description 消息发布管理保存接口入参校验
     * @time 创建时间:2024年06月05日下午08:01:57
     * @param saveXxfbglReqVO
     * @return {@link ErrorListVO }
     * @throws IOException
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public ErrorListVO checkXxfbglCs(SaveXxfbglReqVO saveXxfbglReqVO) {
        ErrorListVO vo = new ErrorListVO();
        if (GyUtils.isNull(saveXxfbglReqVO.getYwxtid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少业务系统id");
            return vo;
        }
        if (GyUtils.isNull(saveXxfbglReqVO.getYwxtfwkl())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少业务系统访问令牌");
            return vo;
        }
        if (GyUtils.isNull(saveXxfbglReqVO.getLrrsfid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少录入人身份id");
            return vo;
        }
        final String fbztDm = saveXxfbglReqVO.getFbztDm();
        if (GyUtils.isNull(fbztDm)) {
            vo.setErrorCode("2");
            vo.setErrorMsg("发布状态必录,01拟稿，03发布");
            return vo;
        } else {
            if (!"01" .equals(fbztDm) && !"03" .equals(fbztDm)) {
                vo.setErrorCode("2");
                vo.setErrorMsg("不识别的发布状态代码,此接口只支持传入:01 拟稿;03 发布");
                return vo;
            }
        }
        if (!txzxGyUtils.checkYwxtJrsf(saveXxfbglReqVO.getYwxtid(), saveXxfbglReqVO.getYwxtfwkl())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("应用ID和令牌检验失败，请检查或联系运维刷新ywstms缓存");
            return vo;
        }
        if (GyUtils.isNull(saveXxfbglReqVO.getXxmbbh())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少消息模板编号");
            return vo;
        }
        //20240802 置顶传9  不置顶传0
        /*if (GyUtils.isNotNull(saveXxfbglReqVO.getXxyxj())) {//消息优先级只支持9
            if (!"9".equals(saveXxfbglReqVO.getXxyxj())){
                vo.setErrorCode("1");
                vo.setErrorMsg("参数Xxyxj消息优先级只支持传9置顶，校验不通过");
                return vo;
            }
        }*/
        if (!GyUtils.isNull(saveXxfbglReqVO.getXxyxqq())) {
            if (!TxzxGyUtils.isValidDate(saveXxfbglReqVO.getXxyxqq(), "yyyy-MM-dd HH:mm:ss")) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数 Xxyxqq 格式错误，校验不通过");
                return vo;
            }
        }
        if (!GyUtils.isNull(saveXxfbglReqVO.getXxyxqz())) {
            if (!TxzxGyUtils.isValidDate(saveXxfbglReqVO.getXxyxqz(), "yyyy-MM-dd HH:mm:ss")) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数 Xxyxqz 格式错误，校验不通过");
                return vo;
            }
        }
        Object[] xxmbAndNrmbs = txzxGyUtils.getXtxxmbAndNrmbs(saveXxfbglReqVO.getYwxtid(), saveXxfbglReqVO.getXxmbbh());
        if (GyUtils.isNull(xxmbAndNrmbs)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("ywxtid下的该模板编号未定义");
            return vo;
        }
        Object xxmbObj = xxmbAndNrmbs[2];
        Map<String, Object> xxmb = JSON.parseObject(JSON.toJSONString(xxmbObj), Map.class);
        if (GyUtils.isNull(xxmb)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("模板未定义");
            return vo;
        }
        Object xxnrmbObj = xxmbAndNrmbs[3];
        //List<Map> xxnrmbMapList= JSONArray.parseArray(JSON.toJSONString(xxnrmbObj),Map.class);
        List<Map<String, Object>> xxnrmbMapList = (List<Map<String, Object>>) xxnrmbObj;
        if (GyUtils.isNull(xxnrmbMapList)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("内容模板未定义");
            return vo;
        }
        final String csInput = saveXxfbglReqVO.getXxcs();
        if (!CollectionUtils.isEmpty(xxnrmbMapList)) {
            for (Map<String, Object> xxnrmb : xxnrmbMapList) {
                String cs = xxnrmb.get("xxmbcsys").toString();
                //检查模板下的参数，传入的参数是否有漏的
                if (checkCsAll(cs, csInput)) {
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数校验不通过");
                    return vo;
                }
            }
        }
        if ("2" .equals(xxmb.get("jsdxlx"))) {//2到用户（yhid）
            if (GyUtils.isNull(saveXxfbglReqVO.getJguuidList())) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数Jguuid为空，校验不通过");
                return vo;
            }
        }
        if ("1" .equals(xxmb.get("jsdxlx"))) {//1到机构（管理企业）
            if (GyUtils.isNull(saveXxfbglReqVO.getYhuuidList())) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数Yhuuid为空，校验不通过");
                return vo;
            }
        }
        if ("3" .equals(xxmb.get("jsdxlx"))) {//3到组织（zzuuid）
            if (GyUtils.isNull(saveXxfbglReqVO.getZzuuidList())) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数Zzuuid为空，校验不通过");
                return vo;
            }
        }
        return vo;
    }

    /**
     * @name 中文名称
     * @description 更新消息发布管理接口入参校验
     * @time 创建时间:2024年06月06日下午07:29:46
     * @param saveXxfbglReqVO
     * @return {@link ErrorListVO }
     * @throws IOException
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public ErrorListVO checkUpXxfbglCs(UpdateXxfbglReqVO saveXxfbglReqVO) {
        ErrorListVO vo = new ErrorListVO();
        if (GyUtils.isNull(saveXxfbglReqVO.getXxfbuuid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少消息发布UUID");
            return vo;
        }
        if (GyUtils.isNull(saveXxfbglReqVO.getYwxtid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少业务系统id");
            return vo;
        }
        if (GyUtils.isNull(saveXxfbglReqVO.getYwxtfwkl())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少业务系统访问令牌");
            return vo;
        }
        if (GyUtils.isNull(saveXxfbglReqVO.getLrrsfid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少录入人身份id");
            return vo;
        }
        final String fbztDm = saveXxfbglReqVO.getFbztDm();
        if (GyUtils.isNull(fbztDm)) {
            vo.setErrorCode("2");
            vo.setErrorMsg("发布状态必录,此接口只支持传入，01 拟稿");
            return vo;
        } else {
            if (!"01" .equals(fbztDm)) {
                vo.setErrorCode("2");
                vo.setErrorMsg("不识别的发布状态代码,此接口只支持传入，01 拟稿");
                return vo;
            }
        }
        if (!txzxGyUtils.checkYwxtJrsf(saveXxfbglReqVO.getYwxtid(), saveXxfbglReqVO.getYwxtfwkl())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("应用ID和令牌检验失败，请检查或联系运维刷新ywstms缓存");
            return vo;
        }
        if (GyUtils.isNull(saveXxfbglReqVO.getXxmbbh())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少消息模板编号");
            return vo;
        }
        if (!GyUtils.isNull(saveXxfbglReqVO.getXxyxqq())) {
            if (!TxzxGyUtils.isValidDate(saveXxfbglReqVO.getXxyxqq(), "yyyy-MM-dd HH:mm:ss")) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数 Xxyxqq 格式错误，校验不通过");
                return vo;
            }
        }
        if (!GyUtils.isNull(saveXxfbglReqVO.getXxyxqz())) {
            if (!TxzxGyUtils.isValidDate(saveXxfbglReqVO.getXxyxqz(), "yyyy-MM-dd HH:mm:ss")) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数 Xxyxqz 格式错误，校验不通过");
                return vo;
            }
        }
        Object[] xxmbAndNrmbs = txzxGyUtils.getXtxxmbAndNrmbs(saveXxfbglReqVO.getYwxtid(), saveXxfbglReqVO.getXxmbbh());
        if (GyUtils.isNull(xxmbAndNrmbs)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("ywxtid下的该模板编号未定义");
            return vo;
        }
        Object xxmbObj = xxmbAndNrmbs[2];
        Map<String, Object> xxmb = JSON.parseObject(JSON.toJSONString(xxmbObj), Map.class);
        if (GyUtils.isNull(xxmb)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("模板未定义");
            return vo;
        }
        Object xxnrmbObj = xxmbAndNrmbs[3];
        //List<Map> xxnrmbMapList= JSONArray.parseArray(JSON.toJSONString(xxnrmbObj),Map.class);
        List<Map<String, Object>> xxnrmbMapList = (List<Map<String, Object>>) xxnrmbObj;
        if (GyUtils.isNull(xxnrmbMapList)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("内容模板未定义");
            return vo;
        }
        final String csInput = saveXxfbglReqVO.getXxcs();
        if (!CollectionUtils.isEmpty(xxnrmbMapList)) {
            for (Map<String, Object> xxnrmb : xxnrmbMapList) {
                String cs = xxnrmb.get("xxmbcsys").toString();
                //检查模板下的参数，传入的参数是否有漏的
                if (checkCsAll(cs, csInput)) {
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数校验不通过");
                    return vo;
                }
            }
        }
        //检验xxcs是json格式
        vo = checkXxcs("cs", saveXxfbglReqVO.getXxcs());
        if (vo.getErrorCode().equals("2")) {
            return vo;
        }
        if ("2" .equals(xxmb.get("jsdxlx"))) {//2到用户（yhid）
            if (GyUtils.isNull(saveXxfbglReqVO.getJguuidList())) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数Jguuid为空，校验不通过");
                return vo;
            }
        }
        if ("1" .equals(xxmb.get("jsdxlx"))) {//1到机构（管理企业）
            if (GyUtils.isNull(saveXxfbglReqVO.getYhuuidList())) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数Yhuuid为空，校验不通过");
                return vo;
            }
        }
        if ("3" .equals(xxmb.get("jsdxlx"))) {//3到组织（zzuuid）
            if (GyUtils.isNull(saveXxfbglReqVO.getZzuuidList())) {
                vo.setErrorCode("1");
                vo.setErrorMsg("参数Zzuuid为空，校验不通过");
                return vo;
            }
        }
        return vo;
    }

    /**
     * @name 中文名称
     * @description 更新消息发布管理发布状态接口入参校验
     * @time 创建时间:2024年06月07日下午02:28:53
     * @param updateXxfbglFbztReqVO
     * @return {@link ErrorListVO }
     * @throws IOException
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public ErrorListVO checkUpXxfbglFbztCs(UpdateXxfbglFbztReqVO updateXxfbglFbztReqVO) {
        ErrorListVO vo = new ErrorListVO();
        if (GyUtils.isNull(updateXxfbglFbztReqVO.getXxfbuuid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少消息发布UUID");
            return vo;
        }
        if (GyUtils.isNull(updateXxfbglFbztReqVO.getYwxtid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少业务系统id");
            return vo;
        }
        if (GyUtils.isNull(updateXxfbglFbztReqVO.getYwxtfwkl())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少业务系统访问令牌");
            return vo;
        }
        if (GyUtils.isNull(updateXxfbglFbztReqVO.getLrrsfid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少录入人身份id");
            return vo;
        }
        final String fbztDm = updateXxfbglFbztReqVO.getFbztDm();
        if (GyUtils.isNull(fbztDm)) {
            vo.setErrorCode("2");
            vo.setErrorMsg("发布状态必录,此接口只支持传入，03 发布,04 撤回,09 作废");
            return vo;
        } else {
            if (!"03" .equals(fbztDm) && !"04" .equals(fbztDm) && !"09" .equals(fbztDm)) {
                vo.setErrorCode("2");
                vo.setErrorMsg("不识别的发布状态代码,此接口只支持传入，03 发布,04 撤回,09 作废");
                return vo;
            }
        }
        if (!txzxGyUtils.checkYwxtJrsf(updateXxfbglFbztReqVO.getYwxtid(), updateXxfbglFbztReqVO.getYwxtfwkl())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("应用ID和令牌检验失败，请检查或联系运维刷新ywstms缓存");
            return vo;
        }
        if (GyUtils.isNull(updateXxfbglFbztReqVO.getXxmbbh())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少消息模板编号");
            return vo;
        }
        Object[] xxmbAndNrmbs = txzxGyUtils.getXtxxmbAndNrmbs(updateXxfbglFbztReqVO.getYwxtid(), updateXxfbglFbztReqVO.getXxmbbh());
        if (GyUtils.isNull(xxmbAndNrmbs)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("ywxtid下的该模板编号未定义");
            return vo;
        }
        Object xxmbObj = xxmbAndNrmbs[2];
        Map<String, Object> xxmb = JSON.parseObject(JSON.toJSONString(xxmbObj), Map.class);
        if (GyUtils.isNull(xxmb)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("模板未定义");
            return vo;
        }
        Object xxnrmbObj = xxmbAndNrmbs[3];
        //List<Map> xxnrmbMapList= JSONArray.parseArray(JSON.toJSONString(xxnrmbObj),Map.class);
        List<Map<String, Object>> xxnrmbMapList = (List<Map<String, Object>>) xxnrmbObj;
        if (GyUtils.isNull(xxnrmbMapList)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("内容模板未定义");
            return vo;
        }
        return vo;
    }

    //实体类转换
    public List<ZrrXtxxVO> zrrXtxxVOList(List<TxzxZrrXxfbDTO> txzxZrrXxfbDTOS) {
        List<ZrrXtxxVO> zrrXtxxVOLists = new ArrayList<>();
        for (int i = 0; i < txzxZrrXxfbDTOS.size(); i++) {
            ZrrXtxxVO zrrXtxxVO = new ZrrXtxxVO();
            zrrXtxxVO.setXxuuid(txzxZrrXxfbDTOS.get(i).getXxuuid());
            zrrXtxxVO.setPcbh(txzxZrrXxfbDTOS.get(i).getPcbh());
            zrrXtxxVO.setXxmbbh(txzxZrrXxfbDTOS.get(i).getXxmbbh());
//            zrrXtxxVO.setZrrwlsfid(txzxZrrXxfbDTOS.get(i).getZrrwlsfid());
            zrrXtxxVO.setXxflDm(txzxZrrXxfbDTOS.get(i).getXxflDm());
            zrrXtxxVO.setXxzsbt(txzxZrrXxfbDTOS.get(i).getWebxxzsbt());
            zrrXtxxVO.setXxljApp(txzxZrrXxfbDTOS.get(i).getAppxxlj());
//            if ((!GyUtil.isNull(txzxZrrXxfbDTOS.get(i).getWebxxlj())&&txzxZrrXxfbDTOS.get(i).getWebxxlj().contains("$"))||(!GyUtil.isNull(txzxZrrXxfbDTOS.get(i).getAppxxlj())&&txzxZrrXxfbDTOS.get(i).getAppxxlj().contains("$"))||(!GyUtil.isNull(txzxZrrXxfbDTOS.get(i).getAppxxsytclj())&&txzxZrrXxfbDTOS.get(i).getAppxxsytclj().contains("$"))) {
//                String xtid=txzxZrrXxfbDTOS.get(i).getWebxxlj();
//                String xtidapp=txzxZrrXxfbDTOS.get(i).getAppxxlj();
//                String sytcweblj=txzxZrrXxfbDTOS.get(i).getWebxxsytclj();
//                String sytcapplj=txzxZrrXxfbDTOS.get(i).getAppxxsytclj();
//                String zhxxlj = txzxGyUtils.fillXtljdz(xtid);
//                String zhxxljapp = txzxGyUtils.fillXtljdz(xtidapp);
//                zrrXtxxVO.setAppxxsytclj(txzxGyUtils.fillXtljdz(sytcapplj));
//                zrrXtxxVO.setWebxxsytclj(txzxGyUtils.fillXtljdz(sytcweblj));
//                zrrXtxxVO.setXxlj(zhxxlj);
//                zrrXtxxVO.setXxljApp(zhxxljapp);
//            } else {
            zrrXtxxVO.setXxlj(txzxZrrXxfbDTOS.get(i).getWebxxlj());
            zrrXtxxVO.setXxljApp(txzxZrrXxfbDTOS.get(i).getAppxxlj());
            zrrXtxxVO.setAppxxsytclj(txzxZrrXxfbDTOS.get(i).getAppxxsytclj());
            zrrXtxxVO.setWebxxsytclj(txzxZrrXxfbDTOS.get(i).getWebxxsytclj());
//            }
            zrrXtxxVO.setXxzsbtApp(txzxZrrXxfbDTOS.get(i).getAppxxzsbt());
            zrrXtxxVO.setXxljApp(txzxZrrXxfbDTOS.get(i).getAppxxlj());
            zrrXtxxVO.setXxyxqq(txzxZrrXxfbDTOS.get(i).getXxyxqq());
            zrrXtxxVO.setXxyxqz(txzxZrrXxfbDTOS.get(i).getXxyxqz());
            //    zrrXtxxVO.setXxnrcs(txzxZrrXxfbDTOS.get(i).getXxnrcs());
            zrrXtxxVO.setSfsytc(txzxZrrXxfbDTOS.get(i).getSfsytc());
            zrrXtxxVO.setZyxyxj(txzxZrrXxfbDTOS.get(i).getZyxyxj());
            zrrXtxxVO.setSfyyd(txzxZrrXxfbDTOS.get(i).getSfyyd());
//            zrrXtxxVO.setXxssywlxDm(txzxZrrXxfbDTOS.get(i).getXxssywlxDm());
            zrrXtxxVO.setXxzsfs(txzxZrrXxfbDTOS.get(i).getXxzsfs());
            zrrXtxxVO.setYxbz(txzxZrrXxfbDTOS.get(i).getYxbz());
            zrrXtxxVOLists.add(zrrXtxxVO);
        }

        return zrrXtxxVOLists;
    }

    @Override
    public List<Map<String, Object>> getXxXxmbList(String xxmbbh, String bbh) {
        return txzxXtxxfbMapper.getXxXxmbList(xxmbbh, bbh);
    }

    @Override
    @DS("csdm")
    public Map<String, Object> queryXxmb(String xxmbbh) {
        return txzxXtxxfbMapper.queryXxmb(xxmbbh);
    }

    @Override
    public List<Map<String, Object>> getXxNrmbList(String xxmbbh, String bbh) {
        return txzxXtxxfbMapper.getXxNrmbList(xxmbbh, bbh);
    }

    @Override
    @DS("csdm")
    public Map<String, Object> queryXxNrmb(String xxmbbh, String jsqddm) {
        return txzxXtxxfbMapper.queryXxNrmb(xxmbbh, jsqddm);
    }


    /**
     * 纳税人单条待办，基本参数校验
     * @param saveNsrxxtxReqVOList
     * @return
     * @throws IOException
     */
    public ErrorListVO checkXtxxCsOne(SaveXtxxReqVO saveNsrxxtxReqVOList) {
        ErrorListVO vo = new ErrorListVO();
        /*Map<String, Object> txzxMap = cssDmcsService.getIndexData("CS_MH_XTCS", "MH000000000000017");
        if(!GyUtils.isNull(txzxMap) && "N".equals(String.valueOf(txzxMap.get("SJXX")))){
            vo.setReturnCode("2");
            vo.setReturnMsg("消息中心2.0接口未开放");
            return vo;
        }*/
        if (GyUtils.isNull(saveNsrxxtxReqVOList.getYwxtid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少业务系统id");
            return vo;
        }
        if (GyUtils.isNull(saveNsrxxtxReqVOList.getYwxtfwkl())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少业务系统访问令牌");
            return vo;
        }
        if (GyUtils.isNull(saveNsrxxtxReqVOList.getLrrsfid())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少录入人身份id");
            return vo;
        }
        if (!txzxGyUtils.checkYwxtJrsf(saveNsrxxtxReqVOList.getYwxtid(), saveNsrxxtxReqVOList.getYwxtfwkl())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("应用ID和令牌检验失败，请检查或联系运维刷新ywstms缓存");
            return vo;
        }
        if (GyUtils.isNull(saveNsrxxtxReqVOList.getXxmbbh())) {
            vo.setErrorCode("2");
            vo.setErrorMsg("缺少消息模板编号");
            return vo;
        }
        Object[] xxmbAndNrmbs = txzxGyUtils.getXtxxmbAndNrmbs(saveNsrxxtxReqVOList.getYwxtid(), saveNsrxxtxReqVOList.getXxmbbh());
        if (GyUtils.isNull(xxmbAndNrmbs)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("ywxtid下的该模板编号未定义");
            return vo;
        }
        /*if(saveNsrxxtxReqVOList.getXtxxCsVOs().size()>1){
            vo.setErrorCode("该发送信息超过1条");
            vo.setErrorMsg("2");
            return vo;
        }*/
        Object xxmbObj = xxmbAndNrmbs[2];
        Map<String, Object> xxmb = JSON.parseObject(JSON.toJSONString(xxmbObj), Map.class);
        if (GyUtils.isNull(xxmb)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("模板未定义");
            return vo;
        }
        //原系统1、消息 2、待办。  现无此字段和区分
        /*String xxmblx=String.valueOf(xxmb.get("xxmblx"));
        if(!"1".equals(xxmblx)){
            vo.setReturnCode("-1");
            vo.setReturnMsg("该消息模板编号不可用于消息发送");
            return vo;
        }*/
        //1到机构（管理企业），2到用户（yhid），3到组织（zzuuid），到组织人员角色（zzuuid+yhid+ryjs）
        /*String xxjsrlx=String.valueOf(xxmb.get("jsdxlx"));
        if(!"1".equals(xxjsrlx)){
            vo.setReturnCode("-1");
            vo.setReturnMsg("该消息模板编号不可发送给纳税人");
            return vo;
        }*/
        //是否强制批量管理。 现无此字段
        /*String sfqzpcgl=String.valueOf(xxmb.get("sfqzpcgl"));
        if("Y".equals(sfqzpcgl)){
            vo.setReturnCode("-1");
            vo.setReturnMsg("该消息模板编号不支持单条发送");
            return vo;
        }*/
        Object xxnrmbObj = xxmbAndNrmbs[3];
        //List<Map> xxnrmbMapList= JSONArray.parseArray(JSON.toJSONString(xxnrmbObj),Map.class);
        List<Map<String, Object>> xxnrmbMapList = (List<Map<String, Object>>) xxnrmbObj;
        if (GyUtils.isNull(xxnrmbMapList)) {
            vo.setErrorCode("-1");
            vo.setErrorMsg("内容模板未定义");
            return vo;
        }
        return vo;
    }

    /**
     * 自然人批量保存，革本信息校验
     * @param saveZrrxxtxReqVOList
     * @return
     */
    public ErrorListVO1 checkZrrXtxxCs(SaveZrrXtxxReqVO saveZrrxxtxReqVOList) {
        ErrorListVO1 vo = new ErrorListVO1();
        Map<String, Object> txzxMap = cssDmcsService.getIndexData("CS_MH_XTCS", "MH000000000000017");
        if (!GyUtils.isNull(txzxMap) && "N" .equals(String.valueOf(txzxMap.get("SJXX")))) {
            vo.setReturnCode("2");
            vo.setReturnMsg("消息中心2.0接口未开放");
            return vo;
        }
        if (GyUtils.isNull(saveZrrxxtxReqVOList.getPcbh())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少批次编号");
            return vo;
        }
        if (GyUtils.isNull(saveZrrxxtxReqVOList.getFzxh())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少批次序号");
            return vo;
        }
        if (CollectionUtils.isEmpty(saveZrrxxtxReqVOList.getZrrXtxxCsVOs())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("自然人批量参数不能为空");
            return vo;
        }
        if (!CollectionUtils.isEmpty(saveZrrxxtxReqVOList.getZrrXtxxCsVOs()) && saveZrrxxtxReqVOList.getZrrXtxxCsVOs().size() > 100) {
            vo.setReturnCode("2");
            vo.setReturnMsg("自然人批量参数不能超过100");
            return vo;
        }
        return vo;
    }

    /**
     * 自然人批量保存，pch信息校验,保存时不再验证ywxtid
     * @param pchMap
     * @param saveNsrXtxxListReqVO
     * @param pcxhNum
     * @return
     */
    public List<ErrorListVO1> checkPchMap(Map<String, Object> pchMap, SaveZrrXtxxReqVO saveNsrXtxxListReqVO, int pcxhNum) {
        List<ErrorListVO1> list = new ArrayList<>();
        if (GyUtils.isNull(pchMap)) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("批次号不存在或输入错误");
            list.add(vo);
            return list;
        }
        String xxmblx = String.valueOf(pchMap.get("xxmblx"));
        if (!xxmblx.equals("1")) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("批次编号不支持消息发送");
            list.add(vo);
            return list;
        }
        if (pcxhNum > 0) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("该组已发送, 请勿重复发送！");
            list.add(vo);
            return list;
        }
        //批量保存不再验证ywxtid  2023-06-28 lzl
//        String pcywstid=String.valueOf(pchMap.get("ywxtid"));
//        if(!pcywstid.equals(saveNsrXtxxListReqVO.getYwxtid())){
//            ErrorListVO vo = new ErrorListVO();
//            vo.setReturnCode("-1");
//            vo.setReturnMsg("没有使用该批次的权限！");
//            list.add(vo);
//            return list;
//        }
        String pchyxsj = String.valueOf(pchMap.get("pchyxsj"));
        Date pchyxsjTime = DateUtils.toDate(pchyxsj, "yyyy-MM-dd hh:mm:ss");
        if (System.currentTimeMillis() > pchyxsjTime.getTime()) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("该批次已超过有效时间");
            list.add(vo);
            return list;
        }
        return list;
    }

    /**
     * 纳税人批量保存，pch信息校验,批量不再验证ywxtid
     * @param pchMap
     * @param saveZrrXtxxListReqVO
     * @param pcxhNum
     * @return
     */
    public List<ErrorListVO1> checkPchMap(Map<String, Object> pchMap, SaveNsrXtxxReqVO saveZrrXtxxListReqVO, int pcxhNum) {
        List<ErrorListVO1> list = new ArrayList<>();
        if (GyUtils.isNull(pchMap)) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("批次号不存在或输入错误");
            list.add(vo);
            return list;
        }
        String xxmblx = String.valueOf(pchMap.get("xxmblx"));
        if (!xxmblx.equals("1")) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("批次编号不支持消息发送");
            list.add(vo);
            return list;
        }
        if (pcxhNum > 0) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("该组已发送, 请勿重复发送！");
            list.add(vo);
            return list;
        }
        //批量保存不再验证ywxtid  2023-06-28 lzl
//        String pcywstid=String.valueOf(pchMap.get("ywxtid"));
//        if(!pcywstid.equals(saveZrrXtxxListReqVO.getYwxtid())){
//            ErrorListVO vo = new ErrorListVO();
//            vo.setReturnCode("-1");
//            vo.setReturnMsg("没有使用该批次的权限！");
//            list.add(vo);
//            return list;
//        }
        String pchyxsj = String.valueOf(pchMap.get("pchyxsj"));
        Date pchyxsjTime = DateUtils.toDate(pchyxsj, "yyyy-MM-dd hh:mm:ss");
        if (System.currentTimeMillis() > pchyxsjTime.getTime()) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("该批次已超过有效时间");
            list.add(vo);
            return list;
        }
        return list;
    }

    public List<ErrorListVO1> checkXxnrmbMap(Map<String, Object> pchMap) {
        List<ErrorListVO1> list = new ArrayList<>();
        if (GyUtils.isNull(pchMap)) {
            ErrorListVO1 vo = new ErrorListVO1();
            vo.setReturnCode("-1");
            vo.setReturnMsg("模板内容未配置");
            list.add(vo);
            return list;
        }
        return list;
    }

    /**
     * @name 中文名称
     * @description 获取消息发布管理接口插库全部数据
     * @time 创建时间:2024年06月06日上午11:36:30
     * @param saveXxfbglReqVO
     * @param xxmb
     * @param xxnrmbList
     * @return {@link XtxxXxfbglVo }
     * @throws Exception
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public XtxxXxfbglVo getXxzxXxfbgl(SaveXxfbglReqVO saveXxfbglReqVO, Map<String, Object> xxmb, List<Map<String, Object>> xxnrmbList) {
        XtxxXxfbglVo resVo = new XtxxXxfbglVo();
        List<XxzxXxfbgl> xxfbglList = new ArrayList<>();
        List<XxzxXxfjGxb> xxfjGxList = new ArrayList<>();
        List<XxzxXxfbglJsdx> xxzxXxfbglJsdxList = new ArrayList<>();
        List<XxzxXxfb> xxfbList = new ArrayList<>();
        List<XxfbglVO> xxfbglxx = new ArrayList<>();
        List<ErrorListVO> errList = new ArrayList<>();
        String jsqd = GYCastUtils.cast2Str(xxmb.get("xxjsqd"));

        //检查模板下的参数，传入的参数是否有漏的。如果渠道有多个，就判断多次，添加的记录届要保存多次
        if (jsqd.indexOf(",") == -1) {

            //组装一个消息发布管理表及对应表 List<DTO>数据
            //xxfbgl、xxfbgl_jsdx（fjgx）  fbzt为03 已发布 （xxfb、fjgx和前面的不同uuid、glgxlx不同）
            XtxxXxfbglVo listfb = saveXxFbglOne(saveXxfbglReqVO, xxmb, xxnrmbList, jsqd);

            if (GyUtils.isNotNull(listfb.getXxfbglList())) {
                xxfbglList.addAll(listfb.getXxfbglList());
                XxfbglVO xxfbgl = new XxfbglVO();
                xxfbgl.setJsqd(jsqd);
                //此逻辑就一条
                xxfbgl.setXxfbuuid(listfb.getXxfbglList().get(0).getXxfbuuid());
                xxfbglxx.add(xxfbgl);
            }
            if (GyUtils.isNotNull(listfb.getErrorList())) {
                errList.addAll(listfb.getErrorList());
            }
            if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                xxfjGxList.addAll(listfb.getXxfjGxList());
            }
            if (GyUtils.isNotNull(listfb.getXxfbglJsdxList())) {
                xxzxXxfbglJsdxList.addAll(listfb.getXxfbglJsdxList());
            }
            if (GyUtils.isNotNull(listfb.getXxfbList())) {
                xxfbList.addAll(listfb.getXxfbList());
            }
        } else {
            String[] jsqds = jsqd.split(",");
            for (int i = 0; i < jsqds.length; i++) {
                //组装一个消息发布管理表及对应表 List<DTO>数据
                //xxfbgl、xxfbgl_jsdx（fjgx）  fbzt为03 已发布 （xxfb、fjgx和前面的不同uuid、glgxlx不同）
                XtxxXxfbglVo listfb = saveXxFbglOne(saveXxfbglReqVO, xxmb, xxnrmbList, jsqds[i]);

                if (GyUtils.isNotNull(listfb.getXxfbglList())) {
                    xxfbglList.addAll(listfb.getXxfbglList());
                    XxfbglVO xxfbgl = new XxfbglVO();
                    xxfbgl.setJsqd(jsqds[i]);
                    //此逻辑就一条
                    xxfbgl.setXxfbuuid(listfb.getXxfbglList().get(0).getXxfbuuid());
                    xxfbglxx.add(xxfbgl);
                }
                if (GyUtils.isNotNull(listfb.getErrorList())) {
                    errList.addAll(listfb.getErrorList());
                }
                if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                    xxfjGxList.addAll(listfb.getXxfjGxList());
                }
                if (GyUtils.isNotNull(listfb.getXxfbglJsdxList())) {
                    xxzxXxfbglJsdxList.addAll(listfb.getXxfbglJsdxList());
                }
                if (GyUtils.isNotNull(listfb.getXxfbList())) {
                    xxfbList.addAll(listfb.getXxfbList());
                }
            }
        }
        resVo.setXxfbglxx(xxfbglxx);
        resVo.setXxfbglList(xxfbglList);
        resVo.setErrorList(errList);
        resVo.setXxfjGxList(xxfjGxList);
        resVo.setXxfbglJsdxList(xxzxXxfbglJsdxList);
        resVo.setXxfbList(xxfbList);
        return resVo;
    }


    /**
     * 纳税人消息单条保存实体赋值
     * @param saveXtxxListReqVO
     * @param xxmb
     * @param xxnrmbList
     * @return
     */
    public XtxxXxfbVo checkTxzxXxfb(SaveXtxxReqVO saveXtxxListReqVO, Map<String, Object> xxmb, List<Map<String, Object>> xxnrmbList) {
        XtxxXxfbVo resVo = new XtxxXxfbVo();
        //List<TxzxWxgzh> wxList = new ArrayList<>();
        XxzxXxfb xxTbVO = new XxzxXxfb();
        List<XxzxXxfb> xxfblist = new ArrayList<>();
        List<XxzxXxfjGxb> xxfjGxList = new ArrayList<>();
        List<ErrorListVO> errList = new ArrayList<>();
        //List<TxzxXxZfqdjl> zfqdList=new ArrayList<TxzxXxZfqdjl>();
        List<XtxxCSVO> djxhList = saveXtxxListReqVO.getXtxxCsVOs();
        String jsqd = GYCastUtils.cast2Str(xxmb.get("xxjsqd"));
        String jsdxlx = GYCastUtils.cast2Str(xxmb.get("jsdxlx"));
        //List<DxDjxhVo> dxDjxhVoListXx=null;
        for (XtxxCSVO xtxxListCsVO : djxhList) {
            if ("2" .equals(jsdxlx)) {//2到机构（管理企业）
                if (GyUtils.isNull(xtxxListCsVO.getJguuidList())) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数Jguuid为空，校验不通过");
                    errList.add(vo);
                    continue;
                }
            }
            if ("1" .equals(jsdxlx)) {//1到用户（yhid）
                if (GyUtils.isNull(xtxxListCsVO.getYhuuidList())) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数Yhuuid为空，校验不通过");
                    errList.add(vo);
                    continue;
                }
            }
            if ("3" .equals(jsdxlx)) {//3到组织（zzuuid）
                if (GyUtils.isNull(xtxxListCsVO.getZzuuidList())) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数Zzuuid为空，校验不通过");
                    errList.add(vo);
                    continue;
                }
            }
            if ("4" .equals(jsdxlx)) {//4到工号（gh）
                if (GyUtils.isNull(xtxxListCsVO.getGhList())) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数Gh为空，校验不通过");
                    errList.add(vo);
                    continue;
                }
            }
            //20240802 置顶传9  不置顶传0
            /*if (GyUtils.isNotNull(xtxxListCsVO.getXxyxj())) {//消息优先级只支持9
                if (!"9".equals(xtxxListCsVO.getXxyxj())){
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数Xxyxj消息优先级只支持传9置顶，校验不通过");
                    errList.add(vo);
                    continue;
                }
            }*/
            if (!GyUtils.isNull(xtxxListCsVO.getXxyxqq())) {
                if (!TxzxGyUtils.isValidDate(xtxxListCsVO.getXxyxqq(), "yyyy-MM-dd HH:mm:ss")) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数 Xxyxqq 格式错误，校验不通过");
                    errList.add(vo);
                    continue;
                }
            }
            if (!GyUtils.isNull(xtxxListCsVO.getXxyxqz())) {
                if (!TxzxGyUtils.isValidDate(xtxxListCsVO.getXxyxqz(), "yyyy-MM-dd HH:mm:ss")) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数 Xxyxqz 格式错误，校验不通过");
                    errList.add(vo);
                    continue;
                }
            }
            SaveXtxxOneVO saveXtxxOneVO = new SaveXtxxOneVO();
            saveXtxxOneVO.setLrrsfid(saveXtxxListReqVO.getLrrsfid());
            saveXtxxOneVO.setYwxtid(saveXtxxListReqVO.getYwxtid());
            saveXtxxOneVO.setFbr(saveXtxxListReqVO.getFbr());//20240820加
            if (GyUtils.isNotNull(xtxxListCsVO.getWjxxjh())) {
                saveXtxxOneVO.setWjxxjh(xtxxListCsVO.getWjxxjh());
            }
            //检查模板下的参数，传入的参数是否有漏的。如果渠道有多个，就判断多次，添加的记录届要保存多次
            String[] jsqdList = jsqd.split(",");
            for (final String jsqdDm : jsqdList) {
                XtxxXxfbVo listfb;
                if ("1" .equals(jsdxlx)) {//yh
                    List<String> yhidList = xtxxListCsVO.getYhuuidList();
                    for (String yhid : yhidList) {
                        saveXtxxOneVO.setYhuuid(yhid);
                        listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                        if (GyUtils.isNotNull(listfb.getXxfbList())) {
                            xxfblist.addAll(listfb.getXxfbList());
                        }
                        if (GyUtils.isNotNull(listfb.getErrorList())) {
                            errList.addAll(listfb.getErrorList());
                        }
                        if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                            xxfjGxList.addAll(listfb.getXxfjGxList());
                        }
                    }
                } else if ("2" .equals(jsdxlx)) {//jg
                    List<String> jgidList = xtxxListCsVO.getJguuidList();
                    for (String jgid : jgidList) {
                        saveXtxxOneVO.setJguuid(jgid);
                        listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                        if (GyUtils.isNotNull(listfb.getXxfbList())) {
                            xxfblist.addAll(listfb.getXxfbList());
                        }
                        if (GyUtils.isNotNull(listfb.getErrorList())) {
                            errList.addAll(listfb.getErrorList());
                        }
                        if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                            xxfjGxList.addAll(listfb.getXxfjGxList());
                        }
                    }
                } else if ("3" .equals(jsdxlx)) {//zz
                    List<String> zzidList = xtxxListCsVO.getZzuuidList();
                    for (String zzid : zzidList) {
                        saveXtxxOneVO.setZzuuid(zzid);
                        listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                        if (GyUtils.isNotNull(listfb.getXxfbList())) {
                            xxfblist.addAll(listfb.getXxfbList());
                        }
                        if (GyUtils.isNotNull(listfb.getErrorList())) {
                            errList.addAll(listfb.getErrorList());
                        }
                        if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                            xxfjGxList.addAll(listfb.getXxfjGxList());
                        }
                    }

                } else if ("4" .equals(jsdxlx)) {//zz
                    List<String> ghList = xtxxListCsVO.getGhList();
                    for (String gh : ghList) {
                        saveXtxxOneVO.setGh(gh);
                        listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                        if (GyUtils.isNotNull(listfb.getXxfbList())) {
                            xxfblist.addAll(listfb.getXxfbList());
                        }
                        if (GyUtils.isNotNull(listfb.getErrorList())) {
                            errList.addAll(listfb.getErrorList());
                        }
                        if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                            xxfjGxList.addAll(listfb.getXxfjGxList());
                        }
                    }

                } else if ("0" .equals(jsdxlx)) {//公开
                    listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                    if (GyUtils.isNotNull(listfb.getXxfbList())) {
                        xxfblist.addAll(listfb.getXxfbList());
                    }
                    if (GyUtils.isNotNull(listfb.getErrorList())) {
                        errList.addAll(listfb.getErrorList());
                    }
                    if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                        xxfjGxList.addAll(listfb.getXxfjGxList());
                    }
                }
            }

        }
        resVo.setXxfbList(xxfblist);
        resVo.setErrorList(errList);
        resVo.setXxfjGxList(xxfjGxList);
        return resVo;
    }

    /**
     * 纳税人消息单条保存实体赋值
     * @param saveXtxxListReqVO
     * @param xxmb
     * @param xxnrmbList
     * @return
     */
    public XtxxXxfbVo checkTxzxXxBtfb(SaveXtxxReqVO saveXtxxListReqVO, Map<String, Object> xxmb, List<Map<String, Object>> xxnrmbList) {
        XtxxXxfbVo resVo = new XtxxXxfbVo();
        List<XxzxXxfb> xxfblist = new ArrayList<>();
        List<XxzxXxfjGxb> xxfjGxList = new ArrayList<>();
        List<ErrorListVO> errList = new ArrayList<>();
        List<XtxxCSVO> xtxxCsVOList = saveXtxxListReqVO.getXxTbCsVOs();
        String jsqd = GYCastUtils.cast2Str(xxmb.get("xxjsqd"));
        String jsdxlx = GYCastUtils.cast2Str(xxmb.get("jsdxlx"));
        for (XtxxCSVO xtxxListCsVO : xtxxCsVOList){
            if ("2" .equals(jsdxlx)) {//2到机构（管理企业）
                if (GyUtils.isNull(xtxxListCsVO.getJguuidList())) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数Jguuid为空，校验不通过");
                    errList.add(vo);
                }
            }
            if ("1" .equals(jsdxlx)) {//1到用户（yhid）
                if (GyUtils.isNull(xtxxListCsVO.getYhuuidList())) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数Yhuuid为空，校验不通过");
                    errList.add(vo);
                }
            }
            if ("3" .equals(jsdxlx)) {//3到组织（zzuuid）
                if (GyUtils.isNull(xtxxListCsVO.getZzuuidList())) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数Zzuuid为空，校验不通过");
                    errList.add(vo);
                }
            }
            if (!GyUtils.isNull(xtxxListCsVO.getXxyxqq())) {
                if (!TxzxGyUtils.isValidDate(xtxxListCsVO.getXxyxqq(), "yyyy-MM-dd HH:mm:ss")) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数 Xxyxqq 格式错误，校验不通过");
                    errList.add(vo);
                }
            }
            if (!GyUtils.isNull(xtxxListCsVO.getXxyxqz())) {
                if (!TxzxGyUtils.isValidDate(xtxxListCsVO.getXxyxqz(), "yyyy-MM-dd HH:mm:ss")) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("参数 Xxyxqz 格式错误，校验不通过");
                    errList.add(vo);
                }
            }
            SaveXtxxOneVO saveXtxxOneVO = new SaveXtxxOneVO();
            saveXtxxOneVO.setLrrsfid(saveXtxxListReqVO.getLrrsfid());
            saveXtxxOneVO.setYwxtid(saveXtxxListReqVO.getYwxtid());
            saveXtxxOneVO.setFbr(saveXtxxListReqVO.getFbr());//20240820加
            if (GyUtils.isNotNull(xtxxListCsVO.getWjxxjh())) {
                saveXtxxOneVO.setWjxxjh(xtxxListCsVO.getWjxxjh());
            }
            //检查模板下的参数，传入的参数是否有漏的。如果渠道有多个，就判断多次，添加的记录届要保存多次
            String[] jsqdList = jsqd.split(",");
            for (final String jsqdDm : jsqdList) {
                XtxxXxfbVo listfb;
                if ("1" .equals(jsdxlx)) {//yh
                    List<String> yhidList = xtxxListCsVO.getYhuuidList();
                    for (String yhid : yhidList) {
                        saveXtxxOneVO.setYhuuid(yhid);
                        listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                        if (GyUtils.isNotNull(listfb.getXxfbList())) {
                            xxfblist.addAll(listfb.getXxfbList());
                        }
                        if (GyUtils.isNotNull(listfb.getErrorList())) {
                            errList.addAll(listfb.getErrorList());
                        }
                        if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                            xxfjGxList.addAll(listfb.getXxfjGxList());
                        }
                    }
                } else if ("2" .equals(jsdxlx)) {//jg
                    List<String> jgidList = xtxxListCsVO.getJguuidList();
                    for (String jgid : jgidList) {
                        saveXtxxOneVO.setJguuid(jgid);
                        listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                        if (GyUtils.isNotNull(listfb.getXxfbList())) {
                            xxfblist.addAll(listfb.getXxfbList());
                        }
                        if (GyUtils.isNotNull(listfb.getErrorList())) {
                            errList.addAll(listfb.getErrorList());
                        }
                        if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                            xxfjGxList.addAll(listfb.getXxfjGxList());
                        }
                    }
                } else if ("3" .equals(jsdxlx)) {//zz
                    List<String> zzidList = xtxxListCsVO.getZzuuidList();
                    for (String zzid : zzidList) {
                        saveXtxxOneVO.setZzuuid(zzid);
                        listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                        if (GyUtils.isNotNull(listfb.getXxfbList())) {
                            xxfblist.addAll(listfb.getXxfbList());
                        }
                        if (GyUtils.isNotNull(listfb.getErrorList())) {
                            errList.addAll(listfb.getErrorList());
                        }
                        if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                            xxfjGxList.addAll(listfb.getXxfjGxList());
                        }
                    }

                } else if ("0" .equals(jsdxlx)) {//公开
                    listfb = saveXtxxOne(saveXtxxOneVO, xtxxListCsVO, xxmb, xxnrmbList, jsqdDm);
                    if (GyUtils.isNotNull(listfb.getXxfbList())) {
                        xxfblist.addAll(listfb.getXxfbList());
                    }
                    if (GyUtils.isNotNull(listfb.getErrorList())) {
                        errList.addAll(listfb.getErrorList());
                    }
                    if (GyUtils.isNotNull(listfb.getXxfjGxList())) {
                        xxfjGxList.addAll(listfb.getXxfjGxList());
                    }
                }
            }
        }
        resVo.setXxfbList(xxfblist);
        resVo.setErrorList(errList);
        resVo.setXxfjGxList(xxfjGxList);
        return resVo;
    }

    public XtxxXxfbVo saveXtxxOne(SaveXtxxOneVO saveXtxxOneVO, XtxxCSVO xtxxListCsVO, Map<String, Object> xxmb, List<Map<String, Object>> xxnrmbList, String jsqd) {
        XtxxXxfbVo resVo = new XtxxXxfbVo();
        Date date = new Date();
        List<XxzxXxfb> xxfblist = new ArrayList<>();
        List<ErrorListVO> errList = new ArrayList<>();
        String jsdxlx = GYCastUtils.cast2Str(xxmb.get("jsdxlx"));
        int qsyxts = GYCastUtils.cast2Integer(xxmb.get("qsyxts"));
        if (GyUtils.isNull(qsyxts)) {
            qsyxts = TxzxConstant.YXTS_DAY;
        }
        Map<String, Object> xxnrmb = getXxnrmbByJsqd(xxnrmbList, jsqd);
        String cs = String.valueOf(xxnrmb.get("xxmbcsys"));
        //检查xxcs是否是合法的json格式
        ErrorListVO errVo = checkXxcs(cs, xtxxListCsVO.getXxcs());
        if (errVo.getErrorCode().equals("2")) {
            errList.add(errVo);
            resVo.setErrorList(errList);
            return resVo;
        }
        if (checkCsAll(cs, xtxxListCsVO.getXxcs())) {
            ErrorListVO vo = new ErrorListVO();
            vo.setErrorCode("1");
            vo.setJguuid(saveXtxxOneVO.getJguuid());
            vo.setYhuuid(saveXtxxOneVO.getYhuuid());
            vo.setZzuuid(saveXtxxOneVO.getZzuuid());
            vo.setErrorMsg("参数校验不通过，请检查，Xxcs应包括：" + cs);
            errList.add(vo);
            resVo.setErrorList(errList);
            return resVo;
        } else {
            String xxuuid = GyUtils.getUuid();
            XxzxXxfb xxzxXxfb = new XxzxXxfb();
            xxzxXxfb.setXxuuid(xxuuid);
            xxzxXxfb.setPcbh(GyUtils.getUuid());
            xxzxXxfb.setXxmbbh(String.valueOf(xxmb.get("xxmbbh")));
            xxzxXxfb.setYwxtid(saveXtxxOneVO.getYwxtid());
            xxzxXxfb.setJsdxlx(String.valueOf(xxmb.get("jsdxlx")));
            if ("1" .equals(jsdxlx)) {//yh
                xxzxXxfb.setJsdxz("1_" + saveXtxxOneVO.getYhuuid());
            } else if ("2" .equals(jsdxlx)) {//jg
                xxzxXxfb.setJsdxz("2_" + saveXtxxOneVO.getJguuid());
            } else if ("3" .equals(jsdxlx)) {//zz
                xxzxXxfb.setJsdxz("3_" + saveXtxxOneVO.getZzuuid());
            } else if ("4" .equals(jsdxlx)) {//gh
                xxzxXxfb.setJsdxz("4_" + saveXtxxOneVO.getGh());
            } else if ("0" .equals(jsdxlx)) {//公开
                xxzxXxfb.setJsdxz("all");
            }
            xxzxXxfb.setSfsytc(String.valueOf(xxmb.get("sfsytc")));
            xxzxXxfb.setZsqy(String.valueOf(xxmb.get("zsqy")));
            xxzxXxfb.setXxflDm(String.valueOf(xxmb.get("xxflDm")));
            xxzxXxfb.setXxzsfs(String.valueOf(xxmb.get("xxzsfs")));
            xxzxXxfb.setQsyxts(qsyxts);

            xxzxXxfb.setXxljmb((String) xxnrmb.get("xxljmb"));
            ErrorListVO voxxlj = xxnrByXxcs((String) xxnrmb.get("xxljmb"), xtxxListCsVO.getXxcs());
            if ("2" .equals(voxxlj.getErrorCode())) {
                voxxlj.setJguuid(saveXtxxOneVO.getJguuid());
                voxxlj.setYhuuid(saveXtxxOneVO.getYhuuid());
                voxxlj.setZzuuid(saveXtxxOneVO.getZzuuid());
                errList.add(voxxlj);
                resVo.setErrorList(errList);
                return resVo;
            }
            xxzxXxfb.setXxlj(voxxlj.getJguuid());//作了转换

            xxzxXxfb.setXxnrcs(xtxxListCsVO.getXxcs());
            xxzxXxfb.setXxmbcsys((String) xxnrmb.get("xxmbcsys"));

            xxzxXxfb.setXxanpzmb((String) xxnrmb.get("xxanpzmb"));
            ErrorListVO voxxan = xxnrByXxcs((String) xxnrmb.get("xxanpzmb"), xtxxListCsVO.getXxcs());
            if ("2" .equals(voxxan.getErrorCode())) {
                voxxan.setJguuid(saveXtxxOneVO.getJguuid());
                voxxan.setYhuuid(saveXtxxOneVO.getYhuuid());
                voxxan.setZzuuid(saveXtxxOneVO.getZzuuid());
                errList.add(voxxan);
                resVo.setErrorList(errList);
                return resVo;
            }
            xxzxXxfb.setXxanpz(voxxan.getJguuid());

            xxzxXxfb.setBqxxpzmb((String) xxnrmb.get("bqxxpzmb"));
            ErrorListVO vobqxx = xxnrByXxcs((String) xxnrmb.get("bqxxpzmb"), xtxxListCsVO.getXxcs());
            if ("2" .equals(vobqxx.getErrorCode())) {
                vobqxx.setJguuid(saveXtxxOneVO.getJguuid());
                vobqxx.setYhuuid(saveXtxxOneVO.getYhuuid());
                vobqxx.setZzuuid(saveXtxxOneVO.getZzuuid());
                errList.add(vobqxx);
                resVo.setErrorList(errList);
                return resVo;
            }
            final String bqxx = vobqxx.getJguuid();//作了转换
            xxzxXxfb.setBqxx(bqxx);

            ErrorListVO vobqjh = getBqjh(bqxx);//bqxx解析name值  逗号分隔
            if ("2" .equals(vobqjh.getErrorCode())) {
                vobqjh.setJguuid(saveXtxxOneVO.getJguuid());
                vobqjh.setYhuuid(saveXtxxOneVO.getYhuuid());
                vobqjh.setZzuuid(saveXtxxOneVO.getZzuuid());
                errList.add(vobqjh);
                resVo.setErrorList(errList);
                return resVo;
            }
            xxzxXxfb.setBqjh(vobqjh.getJguuid());//作了转换

            xxzxXxfb.setYwzj(xtxxListCsVO.getYwzj());
            xxzxXxfb.setXxnrmb((String) xxnrmb.get("xxnrmb"));
            xxzxXxfb.setXxmbms(GYCastUtils.cast2Str(xxmb.get("xxmbms")));
            if ("9" .equals(xtxxListCsVO.getXxyxj())) {
                xxzxXxfb.setXxyxj(xtxxListCsVO.getXxyxj());
            } else {
                xxzxXxfb.setXxyxj("0");
            }
//            xxzxXxfb.setXxjsqdhj(Integer.parseInt(TxzxGyUtils.getUserSfhjForSave(jsqd)));//
            xxzxXxfb.setXxjsqd(jsqd);
            xxzxXxfb.setFbr1(saveXtxxOneVO.getFbr());
            xxzxXxfb.setLrrsfid(saveXtxxOneVO.getLrrsfid());//接口加入参
            xxzxXxfb.setLrrq(date);
            xxzxXxfb.setXxfbsj(date);
            xxzxXxfb.setSjgsdq("00000000000");
            xxzxXxfb.setSjcsdq("00000000000");
            if (GyUtils.isNull(xtxxListCsVO.getXxyxqq())) {
                xxzxXxfb.setXxyxqq(date);
            } else {
                xxzxXxfb.setXxyxqq(DateUtils.stringToDate(xtxxListCsVO.getXxyxqq()));
            }
            if (GyUtils.isNull(xtxxListCsVO.getXxyxqz())) {
                xxzxXxfb.setXxyxqz(DateUtils.addDate(date, 5, qsyxts));
            } else {
                xxzxXxfb.setXxyxqz(DateUtils.stringToDate(xtxxListCsVO.getXxyxqz()));
            }
            xxzxXxfb.setYxbz("Y");
            if (jsqd.equals(TxzxConstant.QD_WEB)) {
                Map<String, Object> jsqdMap = CacheUtils.getTableData("dm_xxzx_jsqd", TxzxConstant.QD_WEB);
                if (GyUtils.isNull(jsqdMap)) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("web渠道通过开关控制不可以发送");
                    errList.add(vo);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                xxzxXxfb.setXxzsbtmb((String) xxnrmb.get("xxzsbtmb"));
                ErrorListVO voxxbt = xxnrByXxcs((String) xxnrmb.get("xxzsbtmb"), xtxxListCsVO.getXxcs());
                if ("2" .equals(voxxbt.getErrorCode())) {
                    errList.add(voxxbt);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                xxzxXxfb.setXxzsbt(voxxbt.getJguuid());//作了转换

                xxzxXxfb.setYwqdDm("ZNSB.MHZC");

                List<XxzxXxfjGxb> xxfjGxList = new ArrayList<>();
                List<FjxxVO> wjxxjh = saveXtxxOneVO.getWjxxjh();
                if (GyUtils.isNotNull(wjxxjh)) {
                    xxzxXxfb.setSfyfj("Y");
                    for (FjxxVO vo : wjxxjh) {
                        XxzxXxfjGxb xxzxXxfjGxb = new XxzxXxfjGxb();
                        xxzxXxfjGxb.setXxuuid(xxuuid);
                        xxzxXxfjGxb.setFjuuid(vo.getFjuuid());
                        //此接口固定 ，01 关联消息发布表 
                        xxzxXxfjGxb.setGlgxlx("01");
                        xxzxXxfjGxb.setYxbz("Y");
                        xxzxXxfjGxb.setLrrsfid(saveXtxxOneVO.getLrrsfid());//接口加入参
                        xxzxXxfjGxb.setLrrq(date);
                        xxzxXxfjGxb.setSjgsdq("00000000000");
                        xxzxXxfjGxb.setSjcsdq("00000000000");
                        xxzxXxfjGxb.setYwqdDm("ZNSB.MHZC");
                        xxfjGxList.add(xxzxXxfjGxb);
                    }
                }

                xxfblist.add(xxzxXxfb);
                resVo.setXxfbList(xxfblist);
                resVo.setXxfjGxList(xxfjGxList);
            } else if (jsqd.equals(TxzxConstant.QD_APP)) {
                Map<String, Object> jsqdMap = CacheUtils.getTableData("dm_xxzx_jsqd", TxzxConstant.QD_APP);
                if (GyUtils.isNull(jsqdMap)) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("APP渠道通过开关控制不可以发送");
                    errList.add(vo);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                ErrorListVO vo = new ErrorListVO();
                vo.setErrorCode("1");
                vo.setErrorMsg("暂未实现APP渠道不可以发送");
                errList.add(vo);
                resVo.setErrorList(errList);
                return resVo;
            } else if (jsqd.equals(TxzxConstant.QD_WXGZH)) {//微信公众号
                Map<String, Object> jsqdMap = CacheUtils.getTableData("dm_xxzx_jsqd", TxzxConstant.QD_WXGZH);
                if (GyUtils.isNull(jsqdMap)) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("微信公众号渠道通过开关控制不可以发送");
                    errList.add(vo);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                ErrorListVO vo = new ErrorListVO();
                vo.setErrorCode("1");
                vo.setErrorMsg("暂未实现微信公众号渠道不可以发送");
                errList.add(vo);
                resVo.setErrorList(errList);
                return resVo;
            } else if (jsqd.equals(TxzxConstant.QD_DDING)){
                Map<String, Object> jsqdMap = CacheUtils.getTableData("dm_xxzx_jsqd", TxzxConstant.QD_DDING);
                if (GyUtils.isNull(jsqdMap)) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("渠道通过开关控制不可以发送");
                    errList.add(vo);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                xxzxXxfb.setXxzsbtmb((String) xxnrmb.get("xxzsbtmb"));
                xxzxXxfb.setYwqdDm("ZNSB.MHZC");
                xxfblist.add(xxzxXxfb);
                resVo.setXxfbList(xxfblist);
            }else {
                ErrorListVO vo = new ErrorListVO();
                vo.setErrorCode("1");
                vo.setErrorMsg("无此渠道不可以发送");
                errList.add(vo);
                resVo.setErrorList(errList);
                return resVo;
            }
        }
        return resVo;
    }

    /**
     * @name xxfbgl、xxfbgl_jsdx（fjgx）  fbzt为03 已发布 （xxfb、fjgx和前面的不同uuid、glgxlx不同）
     * @description 组装一个消息发布管理表及对应表 List<DTO>数据
     * @time 创建时间:2024年06月06日下午03:36:55
     * @param saveXxfbglReqVO
     * @param xxmb
     * @param xxnrmbList
     * @return {@link XtxxXxfbglVo }
     * @throws Exception
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public XtxxXxfbglVo saveXxFbglOne(SaveXxfbglReqVO saveXxfbglReqVO, Map<String, Object> xxmb, List<Map<String, Object>> xxnrmbList, String jsqd) {
        XtxxXxfbglVo resVo = new XtxxXxfbglVo();

        List<XxzxXxfbgl> xxfbglList = new ArrayList<>();
        List<ErrorListVO> errList = new ArrayList<>();

        Date date = new Date();
        int qsyxts = GYCastUtils.cast2Integer(xxmb.get("qsyxts"));
        if (GyUtils.isNull(qsyxts)) {
            qsyxts = TxzxConstant.YXTS_DAY;
        }
        Map<String, Object> xxnrmb = getXxnrmbByJsqd(xxnrmbList, jsqd);
        String cs = String.valueOf(xxnrmb.get("xxmbcsys"));
        //检查xxcs是否是合法的json格式
        ErrorListVO errVo = checkXxcs(cs, saveXxfbglReqVO.getXxcs());
        if (errVo.getErrorCode().equals("2")) {
            errList.add(errVo);
            resVo.setErrorList(errList);
            return resVo;
        }
        if (checkCsAll(cs, saveXxfbglReqVO.getXxcs())) {
            ErrorListVO vo = new ErrorListVO();
            vo.setErrorCode("1");
            vo.setErrorMsg("参数校验不通过，请检查，Xxcs应包括：" + cs);
            errList.add(vo);
            resVo.setErrorList(errList);
            return resVo;
        } else {
            String xxfbuuid = GyUtils.getUuid();
            XxzxXxfbgl xxzxXxfbgl = new XxzxXxfbgl();
            xxzxXxfbgl.setXxfbuuid(xxfbuuid);
            xxzxXxfbgl.setXxmbbh(String.valueOf(xxmb.get("xxmbbh")));
            xxzxXxfbgl.setYwxtid(saveXxfbglReqVO.getYwxtid());
            xxzxXxfbgl.setJsdxlx(String.valueOf(xxmb.get("jsdxlx")));
            xxzxXxfbgl.setSfsytc(String.valueOf(xxmb.get("sfsytc")));
            xxzxXxfbgl.setXxflDm(String.valueOf(xxmb.get("xxflDm")));
            xxzxXxfbgl.setXxzsfs(String.valueOf(xxmb.get("xxzsfs")));
            xxzxXxfbgl.setQsyxts(qsyxts);

            xxzxXxfbgl.setXxljmb((String) xxnrmb.get("xxljmb"));
            ErrorListVO voxxlj = xxnrByXxcs((String) xxnrmb.get("xxljmb"), saveXxfbglReqVO.getXxcs());
            if ("2" .equals(voxxlj.getErrorCode())) {
                errList.add(voxxlj);
                resVo.setErrorList(errList);
                return resVo;
            }
            xxzxXxfbgl.setXxlj(voxxlj.getJguuid());//作了转换

            xxzxXxfbgl.setXxnrcs(saveXxfbglReqVO.getXxcs());
            xxzxXxfbgl.setXxmbcsys((String) xxnrmb.get("xxmbcsys"));

            xxzxXxfbgl.setXxanpzmb((String) xxnrmb.get("xxanpzmb"));
            ErrorListVO voxxan = xxnrByXxcs((String) xxnrmb.get("xxanpzmb"), saveXxfbglReqVO.getXxcs());
            if ("2" .equals(voxxan.getErrorCode())) {
                errList.add(voxxan);
                resVo.setErrorList(errList);
                return resVo;
            }
            xxzxXxfbgl.setXxanpz(voxxan.getJguuid());

            xxzxXxfbgl.setBqxxpzmb((String) xxnrmb.get("bqxxpzmb"));
            ErrorListVO vobqxx = xxnrByXxcs((String) xxnrmb.get("bqxxpzmb"), saveXxfbglReqVO.getXxcs());
            if ("2" .equals(vobqxx.getErrorCode())) {
                errList.add(vobqxx);
                resVo.setErrorList(errList);
                return resVo;
            }
            final String bqxx = vobqxx.getJguuid();//作了转换
            xxzxXxfbgl.setBqxx(bqxx);

            ErrorListVO vobqjh = getBqjh(bqxx);//bqxx解析name值  逗号分隔
            if ("2" .equals(vobqjh.getErrorCode())) {
                errList.add(vobqjh);
                resVo.setErrorList(errList);
                return resVo;
            }
            xxzxXxfbgl.setBqjh(vobqjh.getJguuid());//作了转换

            xxzxXxfbgl.setYwzj(saveXxfbglReqVO.getYwzj());
            xxzxXxfbgl.setXxnrmb((String) xxnrmb.get("xxnrmb"));
            xxzxXxfbgl.setXxmbms(GYCastUtils.cast2Str(xxmb.get("xxmbms")));
            //20240802 消息优先级置顶传9 不置顶传0
            if ("9" .equals(saveXxfbglReqVO.getXxyxj())) {
                xxzxXxfbgl.setXxyxj(saveXxfbglReqVO.getXxyxj());
            } else {
                xxzxXxfbgl.setXxyxj("0");
            }
//            xxzxXxfbgl.setXxjsqdhj(Integer.parseInt(TxzxGyUtils.getUserSfhjForSave(jsqd)));//
            xxzxXxfbgl.setXxjsqd(jsqd);
            xxzxXxfbgl.setFbr1(saveXxfbglReqVO.getFbr());//20240820加
            xxzxXxfbgl.setLrrsfid(saveXxfbglReqVO.getLrrsfid());//接口加入参
            xxzxXxfbgl.setXgrsfid(saveXxfbglReqVO.getLrrsfid());//查询列表接口，传入czrsfid
            xxzxXxfbgl.setLrrq(date);
            xxzxXxfbgl.setXxfbsj(date);
            xxzxXxfbgl.setSjgsdq("00000000000");
            xxzxXxfbgl.setSjcsdq("00000000000");
            if (GyUtils.isNull(saveXxfbglReqVO.getXxyxqq())) {
                xxzxXxfbgl.setXxyxqq(date);
            } else {
                xxzxXxfbgl.setXxyxqq(DateUtils.stringToDate(saveXxfbglReqVO.getXxyxqq()));
            }
            if (GyUtils.isNull(saveXxfbglReqVO.getXxyxqz())) {
                xxzxXxfbgl.setXxyxqz(DateUtils.addDate(date, 5, qsyxts));
            } else {
                xxzxXxfbgl.setXxyxqz(DateUtils.stringToDate(saveXxfbglReqVO.getXxyxqz()));
            }
            xxzxXxfbgl.setYxbz("Y");
            xxzxXxfbgl.setFbztDm(saveXxfbglReqVO.getFbztDm());

            //消息附件关系表xxfj_gxb
            List<XxzxXxfjGxb> xxfjGxList = new ArrayList<>();
            List<FjxxVO> wjxxjh = saveXxfbglReqVO.getWjxxjh();
            if (GyUtils.isNotNull(wjxxjh)) {
                xxzxXxfbgl.setSfyfj("Y");
                for (FjxxVO vo : wjxxjh) {
                    XxzxXxfjGxb xxzxXxfjGxb = new XxzxXxfjGxb();
                    xxzxXxfjGxb.setXxuuid(xxfbuuid);
                    //针对前端附件保存报错，传入为空情况
                    if (GyUtils.isNull(vo.getFjuuid())) {
                        ErrorListVO fjerrvo = new ErrorListVO();
                        fjerrvo.setErrorCode("2");
                        fjerrvo.setErrorMsg("附件异常，消息发布失败！");
                        errList.add(fjerrvo);
                        resVo.setErrorList(errList);
                        return resVo;
                    } else {
                        xxzxXxfjGxb.setFjuuid(vo.getFjuuid());
                    }
                    //此接口固定 ，02 关联消息发布管理  
                    xxzxXxfjGxb.setGlgxlx("02");
                    xxzxXxfjGxb.setYxbz("Y");
                    xxzxXxfjGxb.setLrrsfid(saveXxfbglReqVO.getLrrsfid());//接口加入参
                    xxzxXxfjGxb.setLrrq(date);
                    xxzxXxfjGxb.setSjgsdq("00000000000");
                    xxzxXxfjGxb.setSjcsdq("00000000000");
                    xxzxXxfjGxb.setYwqdDm("ZNSB.MHZC");
                    xxfjGxList.add(xxzxXxfjGxb);
                }
            }

            if (jsqd.equals(TxzxConstant.QD_WEB)) {
                Map<String, Object> jsqdMap = CacheUtils.getTableData("dm_xxzx_jsqd", TxzxConstant.QD_WEB);
                if (GyUtils.isNull(jsqdMap)) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("web渠道通过开关控制不可以发送");
                    errList.add(vo);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                xxzxXxfbgl.setXxzsbtmb((String) xxnrmb.get("xxzsbtmb"));
                ErrorListVO voxxbt = xxnrByXxcs((String) xxnrmb.get("xxzsbtmb"), saveXxfbglReqVO.getXxcs());
                if ("2" .equals(voxxbt.getErrorCode())) {
                    errList.add(voxxbt);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                xxzxXxfbgl.setXxzsbt(voxxbt.getJguuid());//作了转换

                xxzxXxfbgl.setYwqdDm("ZNSB.MHZC");

                //消息发布管理表  1对多  消息发布管理接收对象表，
                SaveXxfbglJsOneVO saveXxfbglJsOneVO = new SaveXxfbglJsOneVO();
                saveXxfbglJsOneVO.setXxfbgl(xxzxXxfbgl);
                saveXxfbglJsOneVO.setWjxxjh(wjxxjh);

                List<String> jsdxzList = getJsdxzList(saveXxfbglReqVO.getYhuuidList(), saveXxfbglReqVO.getJguuidList(), saveXxfbglReqVO.getZzuuidList(), saveXxfbglReqVO.getGhList());
                saveXxfbglJsOneVO.setJsdxzList(jsdxzList);

                //组装消息发布管理表 外的   关联表 (xxfbgl_jsdx 、如果发布状态为03，同步xxfb、xxfj_gxb)。  
                XtxxXxfbglVo xxfbglJsdxAndXxfbVO = getXxfbglJsdxAndXxfbDTOList(saveXxfbglJsOneVO);

                if (GyUtils.isNotNull(xxfbglJsdxAndXxfbVO.getXxfjGxList())) {
                    xxfjGxList.addAll(xxfbglJsdxAndXxfbVO.getXxfjGxList());
                }

                xxfbglList.add(xxzxXxfbgl);
                resVo.setXxfbglJsdxList(xxfbglJsdxAndXxfbVO.getXxfbglJsdxList());
                resVo.setXxfbList(xxfbglJsdxAndXxfbVO.getXxfbList());
                resVo.setXxfbglList(xxfbglList);
                resVo.setXxfjGxList(xxfjGxList);
            } else if (jsqd.equals(TxzxConstant.QD_APP)) {
                Map<String, Object> jsqdMap = CacheUtils.getTableData("dm_xxzx_jsqd", TxzxConstant.QD_APP);
                if (GyUtils.isNull(jsqdMap)) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("APP渠道通过开关控制不可以发送");
                    errList.add(vo);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                ErrorListVO vo = new ErrorListVO();
                vo.setErrorCode("1");
                vo.setErrorMsg("暂未实现APP渠道不可以发送");
                errList.add(vo);
                resVo.setErrorList(errList);
                return resVo;
            } else if (jsqd.equals(TxzxConstant.QD_WXGZH)) {//微信公众号
                Map<String, Object> jsqdMap = CacheUtils.getTableData("dm_xxzx_jsqd", TxzxConstant.QD_WXGZH);
                if (GyUtils.isNull(jsqdMap)) {
                    ErrorListVO vo = new ErrorListVO();
                    vo.setErrorCode("1");
                    vo.setErrorMsg("微信公众号渠道通过开关控制不可以发送");
                    errList.add(vo);
                    resVo.setErrorList(errList);
                    return resVo;
                }
                ErrorListVO vo = new ErrorListVO();
                vo.setErrorCode("1");
                vo.setErrorMsg("暂未实现微信公众号渠道不可以发送");
                errList.add(vo);
                resVo.setErrorList(errList);
                return resVo;
            } else {
                ErrorListVO vo = new ErrorListVO();
                vo.setErrorCode("1");
                vo.setErrorMsg("无此渠道不可以发送");
                errList.add(vo);
                resVo.setErrorList(errList);
                return resVo;
            }
        }
        return resVo;
    }

    /**
     * @name 中文名称
     * @description 消息发布管理表，存 1对 多消息发布管理接收对象 ， 当 fbzt为03已发布，  1对1 xxfb表， 当存在附件， xxfb表 1对多 fjgx表。 组装最终报文数据。
     * @time 创建时间:2024年06月06日下午03:18:19
     * @param saveXxfbglJsOneVO
     * @return {@link XtxxXxfbglVo }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private XtxxXxfbglVo getXxfbglJsdxAndXxfbDTOList(SaveXxfbglJsOneVO saveXxfbglJsOneVO) {
        XtxxXxfbglVo res = new XtxxXxfbglVo();
        List<XxzxXxfbglJsdx> xxzxXxfbglJsdxList = new ArrayList<>();
        List<XxzxXxfb> xxfbList = new ArrayList<>();
        List<XxzxXxfjGxb> xxfjGxList = new ArrayList<>();

        final String jsdxlx = saveXxfbglJsOneVO.getXxfbgl().getJsdxlx();
        final String fbzt = saveXxfbglJsOneVO.getXxfbgl().getFbztDm();

        final List<String> jsdxzList = saveXxfbglJsOneVO.getJsdxzList();
        if ("0" .equals(jsdxlx)) {
            jsdxzList.add("all");
        }
        //组装xxfbgl_jsdx  List<DTO>  一个 jsdxz 对应一条
        for (String jsdxz : jsdxzList) {
            XxzxXxfbglJsdx xxfbgljsdxvo = new XxzxXxfbglJsdx();
            xxfbgljsdxvo.setUuid(GyUtils.getUuid());
            xxfbgljsdxvo.setXxfbuuid(saveXxfbglJsOneVO.getXxfbgl().getXxfbuuid());
            //已发布状态
            if ("03" .equals(fbzt)) {
                //组装消息发布表DTO   xxzx_xxfb
                XxzxXxfb xxfb = getXxzxXxfbOne(saveXxfbglJsOneVO, jsdxz);
                //组装附件关系表List<DTO> xxzx_xxfj_gxb
                List<FjxxVO> wjxxjh = saveXxfbglJsOneVO.getWjxxjh();
                if (GyUtils.isNotNull(wjxxjh)) {
                    xxfb.setSfyfj("Y");
                    for (FjxxVO vo : wjxxjh) {
                        XxzxXxfjGxb xxzxXxfjGxb = new XxzxXxfjGxb();
                        xxzxXxfjGxb.setXxuuid(xxfb.getXxuuid());
                        xxzxXxfjGxb.setFjuuid(vo.getFjuuid());
                        //此逻辑固定 ，01 关联消息发布表 
                        xxzxXxfjGxb.setGlgxlx("01");
                        xxzxXxfjGxb.setYxbz("Y");
                        xxzxXxfjGxb.setLrrsfid(xxfb.getLrrsfid());//接口加入参
                        xxzxXxfjGxb.setLrrq(new Date());
                        xxzxXxfjGxb.setSjgsdq("00000000000");
                        xxzxXxfjGxb.setSjcsdq("00000000000");
                        xxzxXxfjGxb.setYwqdDm("ZNSB.MHZC");
                        xxfjGxList.add(xxzxXxfjGxb);
                    }
                }
                xxfbList.add(xxfb);
                xxfbgljsdxvo.setXxuuid(xxfb.getXxuuid());
            }
            xxfbgljsdxvo.setJsdxlx(jsdxlx);
            xxfbgljsdxvo.setJsdxz(jsdxz);
            xxfbgljsdxvo.setYxbz("Y");
            xxfbgljsdxvo.setLrrsfid(saveXxfbglJsOneVO.getXxfbgl().getLrrsfid());
            xxfbgljsdxvo.setLrrq(new Date());
            xxfbgljsdxvo.setYwqdDm("ZNSB.MHZC");
            xxfbgljsdxvo.setSjcsdq("00000000000");
            xxfbgljsdxvo.setSjgsdq("00000000000");
            xxzxXxfbglJsdxList.add(xxfbgljsdxvo);
        }
        res.setXxfbList(xxfbList);
        res.setXxfbglJsdxList(xxzxXxfbglJsdxList);
        res.setXxfjGxList(xxfjGxList);
        return res;
    }

    /**
     * @name 中文名称
     * @description 消息发布管理表，同步至消息发布表。  ywzj 传 uuid
     * @time 创建时间:2024年06月06日下午03:16:51
     * @param saveXxfbglJsOneVO
     * @param jsdxz
     * @return {@link XxzxXxfb }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private XxzxXxfb getXxzxXxfbOne(SaveXxfbglJsOneVO saveXxfbglJsOneVO, String jsdxz) {
        final XxzxXxfbgl xxfbgl = saveXxfbglJsOneVO.getXxfbgl();
        XxzxXxfb xxfb = new XxzxXxfb();
        xxfb.setXxuuid(GyUtils.getUuid());
        xxfb.setPcbh(GyUtils.getUuid());
        xxfb.setXxmbbh(xxfbgl.getXxmbbh());
        xxfb.setYwxtid(xxfbgl.getYwxtid());
//        xxfb.setXxjsqdhj(xxfbgl.getXxjsqdhj());
        xxfb.setXxjsqd(xxfbgl.getXxjsqd());
        xxfb.setJsdxlx(xxfbgl.getJsdxlx());
        xxfb.setJsdxz(jsdxz);
        xxfb.setSfsytc(xxfbgl.getSfsytc());
        xxfb.setZsqy("N");
        xxfb.setXxflDm(xxfbgl.getXxflDm());
        xxfb.setXxzsfs(xxfbgl.getXxzsfs());
        xxfb.setQsyxts(xxfbgl.getQsyxts());
        xxfb.setXxzsbtmb(xxfbgl.getXxzsbtmb());
        xxfb.setXxzsbt(xxfbgl.getXxzsbt());
        xxfb.setXxnrmb(xxfbgl.getXxnrmb());
        xxfb.setXxmbms(xxfbgl.getXxmbms());
        xxfb.setXxljmb(xxfbgl.getXxljmb());
        xxfb.setXxlj(xxfbgl.getXxlj());
        xxfb.setXxmbcsys(xxfbgl.getXxmbcsys());
        xxfb.setXxanpzmb(xxfbgl.getXxanpzmb());
        xxfb.setXxanpz(xxfbgl.getXxanpz());
        xxfb.setYwzj(xxfbgl.getXxfbuuid());
        xxfb.setBqxxpzmb(xxfbgl.getBqxxpzmb());
        xxfb.setBqxx(xxfbgl.getBqxx());
        xxfb.setBqjh(xxfbgl.getBqjh());
        xxfb.setXxyxj(xxfbgl.getXxyxj());
        xxfb.setXxyxqq(xxfbgl.getXxyxqq());
        xxfb.setXxyxqz(xxfbgl.getXxyxqz());
        xxfb.setXxnrcs(xxfbgl.getXxnrcs());
        xxfb.setXxfbsj(xxfbgl.getXxfbsj());
        xxfb.setYxbz(xxfbgl.getYxbz());
        xxfb.setFbr1(xxfbgl.getFbr1());
        xxfb.setLrrsfid(xxfbgl.getLrrsfid());
        xxfb.setLrrq(xxfbgl.getLrrq());
        xxfb.setYwqdDm(xxfbgl.getYwqdDm());
        xxfb.setSjcsdq(xxfbgl.getSjcsdq());
        xxfb.setSjgsdq(xxfbgl.getSjgsdq());
        return xxfb;
    }

    /**
     * @name 中文名称
     * @description 消息发布管理 角色对象表，组装数据。
     * @time 创建时间:2024年06月06日上午11:02:55
     * @param saveXxfbglJsOneVO
     * @return {@link List<XxzxXxfbglJsdx> }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private List<XxzxXxfbglJsdx> getXxfbglJsdxDTOList(SaveXxfbglJsOneVO saveXxfbglJsOneVO) {
        List<XxzxXxfbglJsdx> xxzxXxfbglJsdxList = new ArrayList<>();
        final String jsdxlx = saveXxfbglJsOneVO.getXxfbgl().getJsdxlx();
        final List<String> jsdxzList = saveXxfbglJsOneVO.getJsdxzList();
        if ("0" .equals(jsdxlx)) {
            jsdxzList.add("all");
        }
        for (String jsdxz : jsdxzList) {
            XxzxXxfbglJsdx xxfbgljsdxvo = new XxzxXxfbglJsdx();
            xxfbgljsdxvo.setUuid(GyUtils.getUuid());
            xxfbgljsdxvo.setXxfbuuid(saveXxfbglJsOneVO.getXxfbgl().getXxfbuuid());
            xxfbgljsdxvo.setJsdxlx(jsdxlx);
            xxfbgljsdxvo.setJsdxz(jsdxz);
            xxfbgljsdxvo.setYxbz("N");
            xxfbgljsdxvo.setLrrsfid(saveXxfbglJsOneVO.getXxfbgl().getLrrsfid());
            xxfbgljsdxvo.setLrrq(new Date());
            xxfbgljsdxvo.setYwqdDm("ZNSB.MHZC");
            xxfbgljsdxvo.setSjcsdq("00000000000");
            xxfbgljsdxvo.setSjgsdq("00000000000");
            xxzxXxfbglJsdxList.add(xxfbgljsdxvo);
        }
        return xxzxXxfbglJsdxList;
    }

    /**
     * @name 替换参数后的消息内容，报错时，将异常返回 借用ErrorListVO封装一层
     * @description 用freemark 传入参数xxcs，替换传入 txt文件中  ${xxcs} ,并返回
     * @time 创建时间:2024年05月27日下午07:15:45
     * @param xxcs
     * @return {@link String }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public ErrorListVO xxnrByXxcs(String xx, String xxcs) {
        ErrorListVO vo = new ErrorListVO();
        if (GyUtils.isNull(xx)) {
            vo.setErrorCode("1");
            vo.setJguuid("");//借用字段 实际为xx
        } else {
            if (GyUtils.isNull(xxcs)) {
                vo.setErrorCode("1");
                vo.setJguuid(xx);//借用字段 实际为xx
            } else {
                try {
                    final Object params = JsonUtils.parse(xxcs);
                    if (!GyUtils.isNull(params)) {
                        xx = TemplateUtils.parseContent(xx, params);
                    }
                    vo.setErrorCode("1");
                    vo.setJguuid(xx);//借用字段 实际为xx
                } catch (Exception e) {
                    vo.setErrorCode("2");
                    vo.setErrorMsg(e.getMessage());
                }
            }
        }
        return vo;
    }

    /**
     * @name 报错时，将异常返回 借用ErrorListVO封装一层
     * @description 组装bqjh，规则为 bqxx解析 name值用逗号分隔
     * @time 创建时间:2024年05月28日上午10:06:02
     * @param bqxx
     * @return {@link String }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public ErrorListVO getBqjh(String bqxx) {
        ErrorListVO vo = new ErrorListVO();
        if (GyUtils.isNull(bqxx)) {
            vo.setErrorCode("1");
            vo.setJguuid("");//借用字段 实际为bqjh
        } else {
            try {
                final Map<String, List<Map<String, String>>> params = (Map<String, List<Map<String, String>>>) JSONObject.parse(bqxx);
                final List<Map<String, String>> param = params.get("tags");
                /*String bqjh = "";
                for (Map<String, String> map : param) {
                    if (GyUtils.isNull(bqjh)) {
                        bqjh = map.get("name");
                    } else {
                        bqjh = bqjh + "," + map.get("name");
                    }
                }*/
                StringBuilder bqjh = new StringBuilder();
                for (Map<String, String> map : param) {
                    if (GyUtils.isNull(bqjh.toString())) {
                        bqjh.append(map.get("name"));
                    } else {
                        bqjh.append("," + map.get("name"));
                    }
                }
                vo.setErrorCode("1");
                vo.setJguuid(bqjh.toString());//借用字段 实际为bqjh
            } catch (Exception e) {
                vo.setErrorCode("2");
                vo.setErrorMsg(e.getMessage());
            }
        }
        return vo;
    }

    /**
     * 从模板内容列表中按渠道获取对应的模板内容
     * @param nrmbList
     * @param jsqd
     * @return
     */
    public Map<String, Object> getXxnrmbByJsqd(List<Map<String, Object>> nrmbList, String jsqd) {
        Map<String, Object> resMqp = new HashMap<>();
        if (GyUtils.isNull(nrmbList)) {
            return resMqp;
        } else {
            for (Map<String, Object> map : nrmbList) {
                if (String.valueOf(map.get("jsqdDm")).equals(jsqd)) {
                    resMqp = map;
                    break;
                }
            }
        }
        return resMqp;
    }

    /**
     * 按内容模板配的参数预设，检查输入的入参 是不是都传了
     * 如模板内容表参数预设是nsrmc,rq 刚传入的Xxcs 中必须有nsrmc,rq，少一个都参数不行，可以多传
     * @param cs
     * @param csinput
     * @return
     */
    public boolean checkCsAll(String cs, String csinput) {
        if (GyUtils.isNull(cs)) {
            return false;
        }
        if (!GyUtils.isNull(cs) && GyUtils.isNull(csinput)) {
            return true;
        }
        boolean res = false;
        String[] a = cs.split(",");
        for (int i = 0; i < a.length; i++) {
            if (!csinput.contains(a[i].trim())) {//先检查是否有这参数
                res = true;
                break;
            }
//           Map<String,Object> jsonmap= JSONObject.parseObject(csinput,Map.class);
//           if(GyUtil.isNull(jsonmap.get(a[i]))){//再检查参数是否传了值,2023-03-09需求说不用检验值，有key 就行
//               res=true;
//               break;
//           }
        }
        return res;
    }

    /**
     * 替换标题或链接地址中的参数，如标题是： #nscmc#的通知， 传入的xxcs中"nsrmc":"张三"，则换成 张三的通知
     * 有多个时，全部替换
     * @param xxzsbt
     * @param xxcs
     * @return
     */
    public String zsbtcs(String xxzsbt, String xxcs) {
        if (GyUtils.isNull(xxzsbt)) {
            return "";
        } else {
            if (GyUtils.isNull(xxcs)) {
                return xxzsbt;
            } else {
                Map<String, Object> xxcsMap = JSONObject.parseObject(xxcs, Map.class);
                if (!GyUtils.isNull(xxcsMap)) {
                    for (String key : xxcsMap.keySet()) {
                        String str = "#" + key + "#";
                        if (xxzsbt.indexOf(str) > -1) {
                            xxzsbt = xxzsbt.replace(str, String.valueOf(xxcsMap.get(key)));
                            //break;
                        }
                    }
                }
            }
        }
        return xxzsbt;
    }

    //运维，查询批次号流水表信息
    @Override
    public List<Map<Object, Object>> selYwPchls(int startIndex, int pageSize, GetPcfsjgVO getPcfsjgVO) {
        return txzxXtxxfbMapper.selYwPchls(startIndex, pageSize, getPcfsjgVO);
    }

    //运维，查询批次号流水表信息总数
    @Override
    public int selCountByPchls(GetPcfsjgVO getPcfsjgVO) {
        return txzxXtxxfbMapper.selCountByPchls(getPcfsjgVO);
    }


    /**
     * 转发渠道对象赋值，批量保存，传的是查询出来的pchMap
     * @param sjdq
     * @param pchMap
     * @param ywuuid
     * @param frwlsfid
     * @param djxh
     * @param zrrwlsfid
     * @param xxbw
     * @param jsqd
     * @return
     */
    /*public TxzxXxZfqdjl setZfqdjl(Map<String,Object> sjdq, Map<String, Object> pchMap, String ywuuid, String frwlsfid, String djxh, String zrrwlsfid, String xxbw, String jsqd,String sjhm,String qddm){
        TxzxXxZfqdjl zfqdjl=new TxzxXxZfqdjl();
        zfqdjl.setUuid(GyUtils.getUuid());
        zfqdjl.setPcbh(String.valueOf(pchMap.get("pcbh")));
        zfqdjl.setXxmbbh(String.valueOf(pchMap.get("xxmbbh")));
        zfqdjl.setBbh(String.valueOf(pchMap.get("bbh")));
        zfqdjl.setXxuuid(ywuuid);
        zfqdjl.setXxmblx(String.valueOf(pchMap.get("xxmblx")));
        zfqdjl.setFrwlsfid(frwlsfid);
        zfqdjl.setDjxh(djxh);
        zfqdjl.setZrrwlsfid(zrrwlsfid);
        zfqdjl.setXxbw(xxbw);
        zfqdjl.setXxfsbz("N");
        zfqdjl.setSfyyd("N");
        zfqdjl.setYxbz(TxzxConstant.YXBZ);
        zfqdjl.setLrrsfid(TxzxConstant.LRRSFID);
        zfqdjl.setYwqdDm(txzxGyUtils.getYwqddm(qddm));
        zfqdjl.setSjgsdq(String.valueOf(sjdq.get("SJXX")));
        zfqdjl.setSjcsdq(String.valueOf(sjdq.get("SJXX")));
        zfqdjl.setQdDm(jsqd);
        zfqdjl.setSjhm(sjhm);
        return zfqdjl;
    }*/

    /**
     * 转发渠道对象赋值，单条保存，传的是保存前的 pchDTO
     * @param sjdq
     * @param pchxxDTO
     * @param ywuuid
     * @param frwlsfid
     * @param djxh
     * @param zrrwlsfid
     * @param xxbw
     * @param jsqd
     * @return
     */
   /* public TxzxXxZfqdjl setZfqdjl(Map<String,Object> sjdq, PchxxDTO pchxxDTO, String ywuuid, String frwlsfid, String djxh, String zrrwlsfid, String xxbw, String jsqd, String sjhm, String qddm){
        TxzxXxZfqdjl zfqdjl=new TxzxXxZfqdjl();
        zfqdjl.setUuid(GyUtils.getUuid());
        zfqdjl.setPcbh(pchxxDTO.getPcbh());
        zfqdjl.setXxmbbh(pchxxDTO.getXxmbbh());
        zfqdjl.setBbh(String.valueOf(pchxxDTO.getBbh()));
        zfqdjl.setXxuuid(ywuuid);
        zfqdjl.setXxmblx(pchxxDTO.getXxmblx());
        zfqdjl.setFrwlsfid(frwlsfid);
        zfqdjl.setDjxh(djxh);
        zfqdjl.setZrrwlsfid(zrrwlsfid);
        zfqdjl.setXxbw(xxbw);
        zfqdjl.setXxfsbz("N");
        zfqdjl.setSfyyd("N");
        zfqdjl.setYxbz(TxzxConstant.YXBZ);
        zfqdjl.setLrrsfid(TxzxConstant.LRRSFID);
        zfqdjl.setYwqdDm(txzxGyUtils.getYwqddm(qddm));
        zfqdjl.setSjgsdq(String.valueOf(sjdq.get("SJXX")));
        zfqdjl.setSjcsdq(String.valueOf(sjdq.get("SJXX")));
        zfqdjl.setQdDm(jsqd);
        zfqdjl.setSjhm(sjhm);
        return zfqdjl;
    }*/

    /**
     * 模板检查，返回了模板不存
     * @param obj
     * @return
     */
    public List<ErrorListVO> checkXxmbAndXxnrmb(Object[] obj) {
        List<ErrorListVO> res = new ArrayList<>();
        ErrorListVO vo = new ErrorListVO();
        vo.setErrorCode("2");
        vo.setErrorMsg(String.valueOf(obj[1]));
        res.add(vo);
        return res;
    }

    /**
     * 检查xxcs 是否是标准的json
     * @param xxmbcs
     * @param xxcs
     * @return
     */
    public ErrorListVO checkXxcs(String xxmbcs, String xxcs) {
        ErrorListVO vo = new ErrorListVO();
        //如果模板内容没有配xxmbcs这字段，说明不需传参，则不校验
        if (GyUtils.isNull(xxmbcs)) {
            vo.setErrorCode("1");
            return vo;
        } else {
            if (GyUtils.isNull(xxcs)) {
                vo.setErrorCode("2");
                vo.setErrorMsg("Xxcs参数为空");
                return vo;
            } else {
                try {
                    JSONObject.parseObject(xxcs, Map.class);
                    vo.setErrorCode("1");
                } catch (Exception e) {
                    vo.setErrorCode("2");
                    vo.setErrorMsg("Xxcs参数不是标准的Json格式");
                    return vo;
                }
            }
        }

        return vo;
    }

    /**
     * 短信发送接口
     * 1.先验基本信息
     * 2.验证接收人信息
     * 3.获取短信内内容，拼装成znhd对象发送
     * 4.更新转发渠道状态
     * @param txzxSendSmsReqVO
     * @return
     * @throws IOException
     * author yangf
     */
   /* @Override
    @DS("txzx")
    @Transactional(rollbackFor = Exception.class)
    public ErrorResVO sendSms(TxzxSendSmsReqVO txzxSendSmsReqVO) throws IOException {
        log.info("短信投递开始");
        ErrorResVO res=new ErrorResVO();
        List<ErrorListVO> erroMessageResVOList=new ArrayList<ErrorListVO>();
        ErrorListVO vo=checkSendSmsReq(txzxSendSmsReqVO);
        List<DxDjxhVo> dxDjxhVoList=null;
        if(!GyUtils.isNull(vo.getReturnMsg())){
            erroMessageResVOList.add(vo);
            res.setErrorList(erroMessageResVOList);
            return res;
        }
        List<SmsJsrVO> smsJsrVOList = txzxSendSmsReqVO.getSmsJsrVOs();
        //2.验证接收人信息
        vo=checkJsrVoList(smsJsrVOList);
        if(!GyUtils.isNull(vo.getReturnMsg())){
            erroMessageResVOList.add(vo);
            res.setErrorList(erroMessageResVOList);
            return res;
        }
        //获取模板
        Map<String, Object> sjdq = cssDmcsService.getIndexData("CS_MH_XTCS", "MH000000000000002");
        Object[] xxmbAndNrmbs = txzxGyUtils.getXtxxmbAndNrmbs(txzxSendSmsReqVO.getYwxtid(), txzxSendSmsReqVO.getXxmbbh());
        if(String.valueOf(xxmbAndNrmbs[0]).equals("2")){
            res.setErrorList(checkXxmbAndXxnrmb(xxmbAndNrmbs));
            return res;
        }
        Map<String, Object> xxmb = new HashMap<>();
        Object xxmbObj = xxmbAndNrmbs[2];
        xxmb = JSON.parseObject(JSON.toJSONString(xxmbObj), Map.class);
        List<Map> xxnrmbMapList = new ArrayList<>();
        Object xxnrmbObj=xxmbAndNrmbs[3];
        xxnrmbMapList= JSONArray.parseArray(JSON.toJSONString(xxnrmbObj),Map.class);
        Map<String,Object> xxnrmb=getXxnrmbByJsqd(xxnrmbMapList,TxzxConstant.QD_DX);
        String content=String.valueOf(xxnrmb.get("xxnrmb"));
        String xxmbcs=String.valueOf(xxnrmb.get("xxmbcsys"));
        //3.添加转发记录
        List<TxzxXxZfqdjl> zfqdList=new ArrayList<TxzxXxZfqdjl>();
        for(SmsJsrVO smsJsrVO:smsJsrVOList){
            vo=checkSmsData(xxmbcs,smsJsrVO.getData(),smsJsrVO.getFrwlsfid(),smsJsrVO.getZrrwlsfid());
            if(vo.getReturnCode().equals("2")){
                erroMessageResVOList.add(vo);
                continue;
            }
            vo=checkJsrVo(smsJsrVO);
            if(!GyUtils.isNull(vo.getReturnCode())){//单条有问题不处理，不影响其它短信的发送
                erroMessageResVOList.add(vo);
                continue;
            }
            if(checkCsAll(xxmbcs,smsJsrVO.getData())){//单条参数不全，不处理，不影响其它短信的发送
                vo.setReturnCode("2");
                vo.setReturnMsg("接收人入参data不全，不能全部替换短信内容，不发此条");
                vo.setFrrwlsfid(smsJsrVO.getFrwlsfid());
                vo.setZrrwlsfid(smsJsrVO.getZrrwlsfid());
                erroMessageResVOList.add(vo);
                continue;
            }
            if(GyUtils.isNull(smsJsrVO.getSjhm()) && !GyUtils.isNull(smsJsrVO.getDjxh())){
                log.info("短信获取手机号测试开始=======================================");
                //http://100.78.62.6/api/v1.0
                //http://tpass88.sat.tax.cn/api/v1.0
//                String url1 = "http://100.78.62.6/api/v1.0/idm/ah/external/company/queryRelationListWithRegNumber";
                String tpassurl=userinfoUrl;  //这个url
                String[] aa=tpassurl.split("/api/v1.0");
                String url1=aa[0].concat("/api/v1.0/idm/ah/external/company/queryRelationListWithRegNumber");
                log.info("xx短信获取可信url1====================================="+url1);
                JSONObject parame = new JSONObject();
                parame.put("regNumber", smsJsrVO.getDjxh());
                log.info("根据登记序号获取企业信息与关联方信息，url:{}, parame:{}", url1, parame);
                try {
//              String returnStrs= HttpUtils.sendPostRequest(url1,parame.toJSONString(),null);
                    TpassResult tpassResult = tpassUtil.sendPostToPassSimple(url1, parame.toJSONString(), null);
                    log.info("根据登记序号获取企业信息与关联方信息，TpassResult:{}", JSON.toJSONString(tpassResult));
                    String datagram = tpassResult.getDatagram();
                    JSONObject jsonObject=JSONObject.parseObject(datagram);
                    String grxx=String.valueOf(jsonObject.get("relationList"));
                    dxDjxhVoList=JSONArray.parseArray(grxx,DxDjxhVo.class);
                    log.info("根据登记序号获取企业信息与关联方信息手机号转换List，TpassResult:{}",dxDjxhVoList);
                    for(int i=0;i<dxDjxhVoList.size();i++){
                        if(String.valueOf(xxmb.get("xxjsrsflx")).indexOf(dxDjxhVoList.get(i).getRelatedType())>-1 && !GyUtil.isNull(dxDjxhVoList.get(i).getMobile())){
                            smsJsrVO.setSjhm(dxDjxhVoList.get(i).getMobile());
                            content=zsbtcs(content,smsJsrVO.getData());
                            TxzxXxZfqdjl zfqd=setSmsZfqdjl(sjdq,xxmb,UuidUtil.getUUID(),smsJsrVO.getFrwlsfid(),smsJsrVO.getDjxh(),smsJsrVO.getZrrwlsfid(),content,TxzxConstant.QD_DX,smsJsrVO.getSjhm(),txzxSendSmsReqVO.getYwqdDm());
                            zfqdList.add(zfqd);
                        }
                    }
//                  log.info("根据登记序号获取企业信息与关联方信息，TpassResult:{}", JSON.toJSONString(returnStrs));
                } catch (Exception e) {
                    log.info("根据登记序号获取企业信息与关联方信息失败。", e);
                }
                log.info("短信获取手机号测试结束=====================================");
            }
            content=zsbtcs(content,smsJsrVO.getData());
            TxzxXxZfqdjl zfqd=setSmsZfqdjl(sjdq,xxmb,GyUtils.getUuid(),smsJsrVO.getFrwlsfid(),smsJsrVO.getDjxh(),smsJsrVO.getZrrwlsfid(),content,TxzxConstant.QD_DX,smsJsrVO.getSjhm(),txzxSendSmsReqVO.getYwqdDm());
            zfqdList.add(zfqd);
        }
        res.setSuccessNum(0);
        res.setErrorList(erroMessageResVOList);
        //4.znhd推送成功，单条改状态
        if(!GyUtils.isNull(zfqdList)){
            res.setSuccessNum(zfqdList.size());
            txzxXxZfqdjlMapper.batchSaveTxzxXxZfqdjl(zfqdList);
            for(TxzxXxZfqdjl zfqdVo:zfqdList){
                SaveZnhdReqVO saveZnhdReqVO =setZnhdReqVo(zfqdVo);
                log.info("打印短信征纳互动入参vo: "+saveZnhdReqVO);
                SaveZnhdResVO saveZnhdResVO = getGrxxFeignClient.pushMessage(saveZnhdReqVO);
                log.info("打印短信征纳互动回参vo: "+saveZnhdResVO);
                if("0".equals(saveZnhdResVO.getCode())){//发送成功，改发送状态为Y
                   zfqdVo.setXxfsbz("Y");
                   txzxXxZfqdjlMapper.updateZfqdFszt(zfqdVo);
                }
            }
        }
        log.info("短信发送结束，打印错误日志："+erroMessageResVOList);
        return res;
    }*/

    /**
     * 发短信基本参数校验
     * @param txzxSendSmsReqVO
     * @return
     * @throws IOException
     */
    /*public ErrorListVO1 checkSendSmsReq(TxzxSendSmsReqVO txzxSendSmsReqVO) throws IOException {
        ErrorListVO1 vo=new ErrorListVO1();
        if(GyUtils.isNull(txzxSendSmsReqVO.getYwxtid())){
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少业务系统id");
            return vo;
        }
        if(GyUtils.isNull(txzxSendSmsReqVO.getYwxtfwkl())){
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少业务系统访问令牌");
            return vo;
        }
        if (!txzxGyUtils.checkYwxtJrsf(txzxSendSmsReqVO.getYwxtid(), txzxSendSmsReqVO.getYwxtfwkl())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("应用ID和令牌检验失败，请检查或联系运维刷新ywstms缓存");
            return vo;
        }
        if(GyUtils.isNull(txzxSendSmsReqVO.getXxmbbh())){
            vo.setReturnCode("2");
            vo.setReturnMsg("缺少消息模板编号");
            return vo;
        }
        Map<String, Object> dxmbMap = cssDmcsService.getIndexData("CS_MH_XTCS", "MH000000000000020");
        if(!GyUtils.isNull(dxmbMap)){
            String dxmb= String.valueOf(dxmbMap.get("SJXX"));
            if(dxmb.contains(txzxSendSmsReqVO.getXxmbbh())){
                vo.setReturnCode("2");
                vo.setReturnMsg("该模板已经配置参数不支持发短信");
                return vo;
            }
        }
        Object[] xxmbAndNrmbs=txzxGyUtils.getXtxxmbAndNrmbs(txzxSendSmsReqVO.getYwxtid(),txzxSendSmsReqVO.getXxmbbh());
        if(GyUtils.isNull(xxmbAndNrmbs)){
            vo.setReturnCode("-1");
            vo.setReturnMsg("ywxtid下的该模板编号未定义");
            return vo;
        }
        Map<String, Object> xxmb = new HashMap<>();
        Object xxmbObj = xxmbAndNrmbs[2];
        xxmb = JSON.parseObject(JSON.toJSONString(xxmbObj), Map.class);
        if(GyUtils.isNull(xxmb)){
            vo.setReturnCode("-1");
            vo.setReturnMsg("模板未定义");
            return vo;
        }
        String xxjsqd=String.valueOf(xxmb.get("xxjsqd"));
        if(xxjsqd.indexOf("05")==-1){
            vo.setReturnCode("-1");
            vo.setReturnMsg("该消息模板编号不可用于短信发送");
            return vo;
        }
        List<Map> xxnrmbMapList = new ArrayList<>();
        Object xxnrmbObj=xxmbAndNrmbs[3];
        xxnrmbMapList= JSONArray.parseArray(JSON.toJSONString(xxnrmbObj),Map.class);
        if(GyUtils.isNull(xxnrmbMapList)){
            vo.setReturnCode("-1");
            vo.setReturnMsg("短信内容模板未定义");
            return vo;
        }
        return vo;
    }*/

    /**
     * 接收人信息基本参数校验
     * @param smsJsrVO
     * @return
     * @throws IOException
     */
    /*public ErrorListVO1 checkJsrVoList(List<SmsJsrVO> smsJsrVO) throws IOException {
        ErrorListVO1 vo = new ErrorListVO1();
        if (GyUtils.isNull(smsJsrVO)) {
            vo.setReturnCode("2");
            vo.setReturnMsg("接收人至少添加1条");
            return vo;
        }
        if (smsJsrVO.size()>100) {
            vo.setReturnCode("2");
            vo.setReturnMsg("接收人最多添加100条");
            return vo;
        }
        return vo;
    }*/

    /**
     * 接收人信息基本参数校验
     * @param smsJsrVO
     * @return
     * @throws IOException
     */
    /*public ErrorListVO1 checkJsrVo(SmsJsrVO smsJsrVO) throws IOException {
        ErrorListVO1 vo = new ErrorListVO1();
        if (GyUtils.isNull(smsJsrVO.getSjhm()) && GyUtils.isNull(smsJsrVO.getDjxh())) {
            vo.setReturnCode("2");
            vo.setReturnMsg("手机号码与djxh不能同时为空");
            return vo;
        }
        return vo;
    }*/
    /*public SaveZnhdReqVO setZnhdReqVo(TxzxXxZfqdjl zfqdVO){
        SaveZnhdReqVO saveZnhdReqVO = new SaveZnhdReqVO();
        List<FjVo> fjList = new ArrayList<>();
        List<jsdxList> jsdxList = new ArrayList<>();
        //业务主键uuid赋值
        saveZnhdReqVO.setYwzjuuid(UuidUtil.getUUID());
        //消息标题
        saveZnhdReqVO.setXxbt("新电子税务局短信");
        //短信内容
        saveZnhdReqVO.setXxnr(zfqdVO.getXxbw());
        //事项类型代码 15是服务提醒
        saveZnhdReqVO.setSxlxDm("15");
        //26是短信
        saveZnhdReqVO.setTsqd("26");
        //验证码标识
        saveZnhdReqVO.setYzmbs("N");
        //录入人id
        saveZnhdReqVO.setFsrxm(TxzxConstant.LRRSFID);
        //消息链接地址
        //功能办理地址
        //业务渠道代码 07 新电子税务局
        saveZnhdReqVO.setYwqdDm("07");
        saveZnhdReqVO.setFjList(fjList);
        //接收对象
        jsdxList jj = new jsdxList();
        //接收对象uuid
        jj.setYwxtjsdxuuid(UuidUtil.getUUID());
        //接收对象01 纳税人
        jj.setJsdxlx("01");
        //自然人网络身份id
        jj.setWlsfid(zfqdVO.getZrrwlsfid());
        //登记序号，发短信时znhd这接口要求必传djxh，没有随便传个123也行
        if(GyUtils.isNull(zfqdVO.getDjxh())){
            jj.setDjxh("1234567890");
        }else{
            jj.setDjxh(zfqdVO.getDjxh());
        }
        //纳税人识别号
        jj.setDhhm(zfqdVO.getSjhm());
        jsdxList.add(jj);
        saveZnhdReqVO.setJsdxList(jsdxList);
        return saveZnhdReqVO;
    }*/

    /**
     * 转发渠道对象赋值，批量保存，传的是查询出来的pchMap
     * @param sjdq
     * @param xxmbMap
     * @param ywuuid
     * @param frwlsfid
     * @param djxh
     * @param zrrwlsfid
     * @param xxbw
     * @param jsqd
     * @return
     */
    /*public TxzxXxZfqdjl setSmsZfqdjl(Map<String,Object> sjdq, Map<String, Object> xxmbMap, String ywuuid, String frwlsfid, String djxh, String zrrwlsfid, String xxbw, String jsqd,String sjhm,String qddm){
        TxzxXxZfqdjl zfqdjl=new TxzxXxZfqdjl();
        zfqdjl.setUuid(GyUtils.getUuid());
        zfqdjl.setPcbh("DX");
        zfqdjl.setXxmbbh(String.valueOf(xxmbMap.get("xxmbbh")));
        zfqdjl.setBbh(String.valueOf(xxmbMap.get("bbh")));
        zfqdjl.setXxuuid(ywuuid);
        zfqdjl.setXxmblx(String.valueOf(xxmbMap.get("xxmblx")));
        zfqdjl.setFrwlsfid(frwlsfid);
        zfqdjl.setDjxh(djxh);
        zfqdjl.setZrrwlsfid(zrrwlsfid);
        zfqdjl.setXxbw(xxbw);
        zfqdjl.setXxfsbz("N");
        zfqdjl.setSfyyd("N");
        zfqdjl.setYxbz(TxzxConstant.YXBZ);
        zfqdjl.setLrrsfid(TxzxConstant.LRRSFID);
        zfqdjl.setYwqdDm(txzxGyUtils.getYwqddm(qddm));
        zfqdjl.setSjgsdq(String.valueOf(sjdq.get("SJXX")));
        zfqdjl.setSjcsdq(String.valueOf(sjdq.get("SJXX")));
        zfqdjl.setQdDm(jsqd);
        zfqdjl.setSjhm(sjhm);
        return zfqdjl;
    }*/

    /**
     * 检查xxcs 是否是标准的json
     * @param xxmbcs
     * @param xxcs
     * @param frwlsfid
     * @param zrrwlsfid
     * @return
     */
   /* public ErrorListVO1 checkSmsData(String xxmbcs, String xxcs, String frwlsfid, String zrrwlsfid){
        ErrorListVO1 vo=new ErrorListVO1();
        //如果模板内容没有配xxmbcs这字段，说明不需传参，则不校验
        if(GyUtils.isNull(xxmbcs)){
            vo.setReturnCode("1");
            return vo;
        }else{
            if(GyUtils.isNull(xxcs)){
                vo.setReturnCode("2");
                vo.setReturnMsg("接收人入参data参数为空，不发此条");
                vo.setFrrwlsfid(frwlsfid);
                vo.setZrrwlsfid(zrrwlsfid);
                return vo;
            }else{
                try{
                    JSONObject.parseObject(xxcs,Map.class);
                    vo.setReturnCode("1");
                }catch (Exception e){
                    vo.setReturnCode("2");
                    vo.setReturnMsg("接收人入参data参数不是标准的Json格式，不发此条");
                    vo.setFrrwlsfid(frwlsfid);
                    vo.setZrrwlsfid(zrrwlsfid);
                    return vo;
                }
            }
        }

        return vo;
    }*/

    /**
     * 异步方法，自调用同一个类中的async方法，不起作用
     * 原因是越过了代理直接调用了方法
     * @param
     */
    @Async
    public void addXxydztAnysc(GetXxtxDetailsReqVO getXxtxDetailsReqVO) {
        log.info("add  ydzt start :({})", System.currentTimeMillis());
        XxzxXxydztDTO txzxXxYdztVO = new XxzxXxydztDTO();
        txzxXxYdztVO.setUuid(GyUtils.getUuid());
        txzxXxYdztVO.setXxuuid(getXxtxDetailsReqVO.getXxuuid());
        txzxXxYdztVO.setYhid(getXxtxDetailsReqVO.getLrrsfid());
        txzxXxYdztVO.setSfyyd("Y");
        txzxXxYdztVO.setYxbz("Y");
        txzxXxYdztVO.setLrrsfid("system");
        txzxXxYdztVO.setLrrq(new Date());
        if ("01" .equals(getXxtxDetailsReqVO.getJsqd())) {
            txzxXxYdztVO.setYwqdDm("ZNSB.MHZC");
        }
        txzxXxYdztVO.setSjcsdq("00000000000");
        txzxXxYdztVO.setSjgsdq("00000000000");
        //log.info("add ydzt start："+System.currentTimeMillis());
        LambdaQueryWrapper<XxzxXxydztDTO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(XxzxXxydztDTO::getXxuuid, getXxtxDetailsReqVO.getXxuuid());
        wrapper.eq(XxzxXxydztDTO::getYhid, getXxtxDetailsReqVO.getLrrsfid());
        if (0 == xxzxXxydztService.getBaseMapper().selectCount(wrapper)) {
            xxzxXxydztService.save(txzxXxYdztVO);
        }
        log.info("add  ydzt end: ({})", System.currentTimeMillis());
    }

    @Override
    //保存信息模板
    public int saveXxmb(@RequestBody TxzxXxMbVO txzxXxMbVO) {
        int res = txzxXtxxfbMapper.saveXxmb(txzxXxMbVO);
        return res;
    }

    @Override
    //查询信息模板
    public TxzxXxMbResVO getListXxmb(@RequestBody TxzxXxMbVO txzxXxMbVO) {

        TxzxXxMbResVO txzxXxMbResVO = new TxzxXxMbResVO();
        List<TxzxXxMbDTO> txzxXxMbDTOS = txzxXtxxfbMapper.getListXxmb(txzxXxMbVO);
        List<TxzxXxMbVO> txzxXxMbVOS = JSONArray.parseArray(JSON.toJSONString(txzxXxMbDTOS), TxzxXxMbVO.class);
        //  List<TxzxXxMbVO> txzxXxMbVOS=BeanCopierUtil.copyList(txzxXxMbDTOS,TxzxXxMbVO.class);
        txzxXxMbResVO.setData(txzxXxMbVOS);

        return txzxXxMbResVO;
    }

    @Override
    //修改信息模板
    public int updateXxmb(@RequestBody TxzxXxMbVO txzxXxMbVO) {
        int res = txzxXtxxfbMapper.updateXxmb(txzxXxMbVO);
        return res;
    }

}
