package com.css.framework.xxzx.pojo.vo.xxtx.xxfj;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UploadFjReqVO {
    //03代表上传文件附件，对应buket（块）:qyd_xxzx
    @JsonProperty(value = "sclx")
    private  String sclx;
    //doc/docx/xls/xlsx/pdf
    
    @JsonProperty(value = "fjlx")
    private  String fjlx;
    //附件名称.doc
    @JsonProperty(value = "fjmc")
    private  String fjmc;
    
    //base64
    @JsonProperty(value = "fileData")
    private  String fileData;
    
    @JsonProperty(value = "czrsfid")
    private String czrsfid;
    
}
