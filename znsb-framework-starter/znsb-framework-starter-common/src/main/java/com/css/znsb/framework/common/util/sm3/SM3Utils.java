package com.css.znsb.framework.common.util.sm3;

import com.css.znsb.framework.common.util.znsb.GyUtils;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class SM3Utils {

    public static String sm3(String input) {
        if (GyUtils.isNull(input)) {
            return null;
        }
        return sm3(input.getBytes(StandardCharsets.UTF_8));
    }

    public static String sm3(byte[] inputBytes) {
        // 计算SM3哈希值
        byte[] sm3Hash = new byte[32]; // SM3输出长度为32字节
        SM3Digest sm3Digest = new SM3Digest();
        sm3Digest.update(inputBytes, 0, inputBytes.length);
        sm3Digest.doFinal(sm3Hash, 0);
        return bytesToHex(sm3Hash);
    }


    // 将字节数组转换为十六进制字符串
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }


    public static String pxhjm(Map<String, MultipartFile> fileMap) throws IOException {
        // 使用 Stream 对 Map 进行排序
        Map<String, MultipartFile> sortedMap = fileMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey()) // 按照 key 排序
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1, // 解决重复键的情况（通常不会发生）
                        LinkedHashMap::new // 使用 LinkedHashMap 保持插入顺序
                ));
        byte[] decodedBytes = new byte[0];
        for (Map.Entry<String, MultipartFile> entry : sortedMap.entrySet()) {
            MultipartFile file = entry.getValue();
            byte[] addbyte = loadFromStream(file.getInputStream());
            decodedBytes = mergeByteArrays(decodedBytes, addbyte);
        }
        return SM3Utils.sm3(decodedBytes);
    }


    public static byte[] mergeByteArrays(byte[]... arrays) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            for (byte[] array : arrays) {
                outputStream.write(array);
            }
            return outputStream.toByteArray();
        } catch (IOException e) {
            return new byte[0]; // 出现异常时返回空数组
        }
    }

    public static byte[] loadFromStream(InputStream input) {
        ByteArrayOutputStream var1 = new ByteArrayOutputStream();
        byte[] var2 = new byte[8192];
        Exception var4 = null;

        try {
            int var16;
            while ((var16 = input.read(var2)) != -1) {
                var1.write(var2, 0, var16);
            }

            var2 = var1.toByteArray();
        } catch (IOException var14) {
            var4 = new Exception("UT-06008:读取文件或写入字节流时发生错误", var14);
        } finally {
            try {
                input.close();
            } catch (IOException var13) {
                var4 = var4 == null ? new Exception("UT-06007:关闭文件时发生错误", var13) : var4;
            }

        }

        if (var4 != null) {
            try {
                throw var4;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return var2;
        }
    }
}
