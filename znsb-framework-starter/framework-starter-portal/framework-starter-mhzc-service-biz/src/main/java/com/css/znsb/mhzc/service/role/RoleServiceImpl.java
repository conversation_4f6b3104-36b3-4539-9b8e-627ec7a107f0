package com.css.znsb.mhzc.service.role;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.mhzc.constants.ErrorCodeEnum;
import com.css.znsb.mhzc.constants.MhzcConstants;
import com.css.znsb.mhzc.mapper.gxb.ZnsbMhqxYhjsGxbMapper;
import com.css.znsb.mhzc.mapper.xxb.ZnsbMhqxJsxxbMapper;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxYhjsGxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxJsxxbDO;
import com.css.znsb.mhzc.pojo.role.RoleInfoDTO;
import com.css.znsb.mhzc.pojo.vo.role.RoleInfoVO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class RoleServiceImpl implements RoleService{

    @Resource
    private ZnsbMhqxJsxxbMapper jsxxbMapper;

    @Resource
    private ZnsbMhqxYhjsGxbMapper yhjsGxbMapper;

    @Override
    public List<RoleInfoVO> getAllRole() {
        final List<ZnsbMhqxJsxxbDO> jsxxbDOS = jsxxbMapper.selectJsxx();
        List<RoleInfoVO> result = new ArrayList<>();
        if (!GyUtils.isNull(jsxxbDOS)){
            result.addAll(BeanUtils.toBean(jsxxbDOS, RoleInfoVO.class));
        }
        return result;
    }

    @Override
    public Integer insert(RoleInfoVO roleInfoVO) {
        final Date date = new Date();

        final ZnsbMhqxJsxxbDO znsbMhqxJsxxbDO1 = jsxxbMapper.selectByJsmc(roleInfoVO.getJsmc());
        if (!GyUtils.isNull(znsbMhqxJsxxbDO1) && !GyUtils.isNull(znsbMhqxJsxxbDO1.getJsmc())){
            throw ServiceExceptionUtil.exception(ErrorCodeEnum.JSMC_EXISTED.getErrorCode());
        }

        final ZnsbMhqxJsxxbDO znsbMhqxJsxxbDO = new ZnsbMhqxJsxxbDO();
        znsbMhqxJsxxbDO.setJsuuid(IdUtil.fastSimpleUUID());
        znsbMhqxJsxxbDO.setJsmc(roleInfoVO.getJsmc());
        znsbMhqxJsxxbDO.setBz(roleInfoVO.getBz());
        znsbMhqxJsxxbDO.setYxbz("Y");
        znsbMhqxJsxxbDO.setYwqdDm(MhzcConstants.YWQDDM);
        znsbMhqxJsxxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
        znsbMhqxJsxxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
        znsbMhqxJsxxbDO.setLrrq(date);
        znsbMhqxJsxxbDO.setXgrq(date);
        znsbMhqxJsxxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
        znsbMhqxJsxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
        znsbMhqxJsxxbDO.setSjtbSj(date);

        return jsxxbMapper.insert(znsbMhqxJsxxbDO);
    }

    @Override
    public void batch(List<ZnsbMhqxJsxxbDO> list) {
        final Date date = new Date();
        for (ZnsbMhqxJsxxbDO jsxxbDO:list){
            final ZnsbMhqxJsxxbDO znsbMhqxJsxxbDO1 = jsxxbMapper.selectByJsmc(jsxxbDO.getJsmc());
            if (!GyUtils.isNull(znsbMhqxJsxxbDO1) && !GyUtils.isNull(znsbMhqxJsxxbDO1.getJsmc())){
                jsxxbDO.setJsmc(jsxxbDO.getJsmc()+"1");
            }
            jsxxbDO.setJsuuid(IdUtil.fastSimpleUUID());
            jsxxbDO.setYxbz("Y");
            jsxxbDO.setYwqdDm(MhzcConstants.YWQDDM);
            jsxxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
            jsxxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
            jsxxbDO.setLrrq(date);
            jsxxbDO.setXgrq(date);
            jsxxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
            jsxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
            jsxxbDO.setSjtbSj(date);
        }
        jsxxbMapper.insertBatch(list);
    }

    @Override
    public Map<String, List<ZnsbMhqxJsxxbDO>> getJsidByMc(List<String> jsmcList) {
        QueryWrapper<ZnsbMhqxJsxxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxJsxxbDO::getJsuuid,ZnsbMhqxJsxxbDO::getJsmc);
//                .in(ZnsbMhqxJsxxbDO::getJsmc,jsmcList);
        List<ZnsbMhqxJsxxbDO> znsbMhqxJsxxbDOS = jsxxbMapper.selectList(wrapper);
        return znsbMhqxJsxxbDOS.stream().map(v->{
            String s = v.getJsmc().replaceAll("\\(.*\\)", "");
            v.setJsmc(s);
            return v;
        }).collect(Collectors.groupingBy(ZnsbMhqxJsxxbDO::getJsmc));
    }

    @Override
    public Integer update(RoleInfoVO roleInfoVO) {
        final ZnsbMhqxJsxxbDO jsxxbDO = new ZnsbMhqxJsxxbDO();
        jsxxbDO.setJsuuid(roleInfoVO.getJsuuid());
        jsxxbDO.setJsmc(roleInfoVO.getJsmc());
        jsxxbDO.setBz(roleInfoVO.getBz());
        jsxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
        jsxxbDO.setXgrq(new Date());
        return jsxxbMapper.updateById(jsxxbDO);
    }

    @Override
    public Integer delete(RoleInfoVO roleInfoVO) {
        yhjsGxbMapper.deleteByJsuuid(roleInfoVO.getJsuuid());
        return jsxxbMapper.deleteById(roleInfoVO.getJsuuid());
    }

    @Override
    public List<RoleInfoDTO> getRolesOfUser(String yhuuid, String zzuuid) {
        List<RoleInfoDTO> result = new ArrayList<>();
        final List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByUuid(yhuuid, zzuuid);
        if (!GyUtils.isNull(znsbMhqxYhjsGxbDOS)){
            final List<String> collect = znsbMhqxYhjsGxbDOS.stream().map(ZnsbMhqxYhjsGxbDO::getJsuuid).collect(Collectors.toList());
            final List<ZnsbMhqxJsxxbDO> jsxxbDOS = jsxxbMapper.selectJsxxByUuids(collect);
            if (!GyUtils.isNull(jsxxbDOS)){
                result.addAll(BeanUtils.toBean(jsxxbDOS,RoleInfoDTO.class));
            }
        }

        return result;
    }
}
