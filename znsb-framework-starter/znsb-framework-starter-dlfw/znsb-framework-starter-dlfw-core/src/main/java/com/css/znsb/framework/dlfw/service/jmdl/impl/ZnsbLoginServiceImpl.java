package com.css.znsb.framework.dlfw.service.jmdl.impl;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.springframework.stereotype.Service;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.dlfw.constants.JmdlConstants;
import com.css.znsb.framework.dlfw.pojo.domain.rpa.ZnsbDzswjdzCzpzDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.FactorAccountLoginResDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.PublicKeyDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.SSOLoginResDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.SendSmsCodeByUuidResDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.SmsMessageLoginResDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.UserinfoDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.ZnsbLoginDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.ZnsbLoginReqDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.ZrrlxDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.captcha.CaptchaDlpzExtDTO;
import com.css.znsb.framework.dlfw.service.jmdl.ICaptchaCheckService;
import com.css.znsb.framework.dlfw.service.jmdl.IZnsbLoginService;
import com.css.znsb.framework.dlfw.util.SM4Utils;
import com.css.znsb.framework.dlfw.util.ZnsbOkHttpUtils;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import lombok.extern.slf4j.Slf4j;

/**
 * 静默登录
 *
 * @project 智能申报系统
 * @module 静默登录
 * @package com.css.znsb.dlfw.service
 * @file ZnsbLoginServiceImpl
 * @createDate 2024-05-14
 * @title 静默登录
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * <AUTHOR>
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */

@Slf4j
@Service("ZnsbLoginServiceImpl")
public class ZnsbLoginServiceImpl implements IZnsbLoginService {

	@Resource
	private ICaptchaCheckService captchaCheckService;

	/**
	 * 获取公钥
	 *
	 * @name 获取公钥
	 * @time 2024-05-14
	 * @param ZnsbLoginReqDTO
	 * @return ZnsbLoginDTO
	 * @throws AppException
	 * <AUTHOR>
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 */
	@Override
	public ZnsbLoginDTO getPublicKey(ZnsbLoginReqDTO dto) {
		final String requestJson = JsonUtils.toJson(dto.toParmMap());
		final Map<String, String> headers = new HashMap<>();
		addHeaders(headers);
		if (GyUtils.isNotNull(dto.getCookie())) {
			headers.put("Cookie", dto.getCookie());
		}
		String result = null; // 接收税局相应报文（未解密）
		try {
			String requestUrl = getBaseURL(dto) + JmdlConstants.PUBLICKEY_URI;
			result = ZnsbOkHttpUtils.postXdj(requestUrl, requestJson, headers);
			dto.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
		} catch (Exception e) {
			log.info("getPublicKey exception happern . ，{}", e.getMessage());
			ZnsbLoginDTO res = new ZnsbLoginDTO();
			res.setCode("1");
			res.setMsg(e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			String httpStatusCode = (String) resJsonObj.get(JmdlConstants.LETTER_HTTP_STATUS_CODE);
			String responseBody = (String) resJsonObj.get(JmdlConstants.LETTER_RESPONSE_BODY);
			if (!GyUtils.isNull(responseBody)) {
				final ZnsbLoginDTO res = JsonUtils.toBean(responseBody, ZnsbLoginDTO.class);
				PublicKeyDTO pubKeyDTO = JsonUtils.toBean(res.getDatagram(), PublicKeyDTO.class);
				res.setPubKeyDTO(pubKeyDTO);
				return res;
			}
			if (!"200".equals(httpStatusCode)) {
				ZnsbLoginDTO res = new ZnsbLoginDTO();
				res.setCode("1");
				res.setMsg(JmdlConstants.ERROR_PREX + httpStatusCode);
				return res;
			}
		}
		return null;
	}

	private void addHeaders(Map<String, String> headers) {

		headers.put("Accept", "application/json, text/plain, */*");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Authorization", "");
		headers.put("Connection", "keep-alive");
		headers.put("Content-Type", "application/json");
		// headers.put("Origin", "https://tpass.anhui.chinatax.gov.cn:8443");
		// headers.put("Referer", "https://tpass.anhui.chinatax.gov.cn:8443/");
		headers.put("Sec-Fetch-Dest", "empty");
		headers.put("Sec-Fetch-Mode", "cors");
		headers.put("Sec-Fetch-Site", "same-origin");
		headers.put("User-Agent",
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
		headers.put("X-APP-CLIENTID", "");
		headers.put("X-LANG-ID", "");
		headers.put("X-NATURE-IP", "");
		headers.put("X-SM4-INFO", "0");
		// headers.put("X-TEMP-INFO", "ca74b161b0ab48fa8bfb7e1b45b2b82d");
		headers.put("X-TICKET-ID", "");
		headers.put("deviceIdentyNo", "kcBo3Ku0Tapownu2aGbGT0iIRzFnY8E0");
		headers.put("hUid", "");
		headers.put("sec-ch-ua",
				"\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
		headers.put("sec-ch-ua-mobile", "?0");
		headers.put("sec-ch-ua-platform", "\"Windows\"");
		/**
		 * Cookie:_sdk_v_=V2.2.1_20240226; _bs_device_id=BID-4523605565171-4683BABCB93;
		 * x_host_key=18f79cf0b4d-2580948c417370d5c25a7cf1a253c0bedefb4a3b
		 *
		 */

	}

	private String getBaseURL(ZnsbLoginReqDTO request) {
		// return JmdlConstants.BASE_URI;
		return request.getBaseURL();
	}

	/**
	 * 向后台发送秘钥
	 *
	 * @name 向后台发送秘钥
	 * @time 2024-05-14
	 * @param ZnsbLoginReqDTO
	 * @return ZnsbLoginDTO
	 * @throws AppException
	 * <AUTHOR>
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 */
	@Override
	public ZnsbLoginDTO sendSm4(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		request.setEncryptCode("0");
		request.initSignature(request.getRandomNum());
		Map<String, String> dataMap = new HashMap<>();
		dataMap.put("uuid", lgdto.getPubKeyDTO().getUuid());
		SM2 sm2 = SmUtil.sm2(null, lgdto.getPubKeyDTO().getPublicKey());
		String secret = sm2.encryptHex(request.getRandomNum(), KeyType.PublicKey).substring(2);
		dataMap.put("secret", secret);
		request.setDatagram(JsonUtils.toJson(dataMap));
		final String requestJson = JsonUtils.toJson(request.toParmMap());
		final Map<String, String> headers = new HashMap<>();
		addHeaders(headers);
		if (GyUtils.isNotNull(request.getCookie())) {
			headers.put("Cookie", request.getCookie());
		}
		String result = null; // 接收税局相应报文（未解密）
		try {
			String requestUrl = getBaseURL(request) + JmdlConstants.SENDSM4_URI;
			result = ZnsbOkHttpUtils.postXdj(requestUrl, requestJson, headers);
			request.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
		} catch (Exception e) {
			log.info("sendSm4 Exception happen.{}", e.getMessage());
			ZnsbLoginDTO res = new ZnsbLoginDTO();
			res.setCode("1");
			res.setMsg(e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			String httpStatusCode = (String) resJsonObj.get(JmdlConstants.LETTER_HTTP_STATUS_CODE);
			String responseBody = (String) resJsonObj.get(JmdlConstants.LETTER_RESPONSE_BODY);
			if (!GyUtils.isNull(responseBody)) {
				return JsonUtils.toBean(responseBody, ZnsbLoginDTO.class);
			}
			if (!"200".equals(httpStatusCode)) {
				ZnsbLoginDTO res = new ZnsbLoginDTO();
				res.setCode("1");
				res.setMsg(JmdlConstants.ERROR_PREX + httpStatusCode);
				return res;
			}
		}
		return null;
	}

	public ZnsbLoginDTO processLogin(ZnsbLoginReqDTO request) {
		// ZnsbLoginReqDTO request = new ZnsbLoginReqDTO();
		ssoLogin(request);
		log.info("location 302 exchange is done.");
		request.setEncryptCode("0");
		request.initSignature();
		ZnsbLoginDTO lgdto = getPublicKey(request);
		log.info("first getPublicKey is done.");
		if (GyUtils.isNull(lgdto) || GyUtils.isEquals("1", lgdto.getCode())) {
			lgdto = getPublicKey(request);
			log.info("second getPublicKey is done.");
		}
		// 二次连接没有数据后 返回
		if (GyUtils.isNull(lgdto) || GyUtils.isEquals("1", lgdto.getCode())) {
			log.info("getPublicKey request is fail.");
			return null;
		}
		String pubKey = lgdto.getPubKeyDTO().getPublicKey();
		if (GyUtils.isNotNull(pubKey)) {
			ZnsbLoginDTO sendsm4Dto = sendSm4(request, lgdto);
			log.info("first sendSm4 is done.");
			if (GyUtils.isNull(sendsm4Dto)) {
				sendsm4Dto = sendSm4(request, lgdto);
				log.info("second sendSm4 is done.");
			}
			// 二次连接没有数据后 返回
			if (GyUtils.isNull(sendsm4Dto)) {
				log.info("sendSm4 request is fail.");
				return null;
			}
			// 调用成功后继续
			ZnsbLoginDTO fatDTO = null;
			if (GyUtils.isEquals("1000", sendsm4Dto.getCode())) {
				fatDTO = factorAccountLogin(request, lgdto);
				log.info("first factorAccountLogin is done.");
			}
			if (GyUtils.isNull(fatDTO) || (GyUtils.isNotNull(fatDTO)
					&& GyUtils.isEquals(JmdlConstants.CODE_1, fatDTO.getCode()))) {
				fatDTO = factorAccountLogin(request, lgdto);
				log.info("second factorAccountLogin is done.");
			}
			// 二次连接没有数据后 返回
			if (GyUtils.isNull(fatDTO) || (GyUtils.isNotNull(fatDTO)
					&& GyUtils.isEquals(JmdlConstants.CODE_1, fatDTO.getCode()))) {
				log.info("factorAccountLogin request is fail.");
				return fatDTO;
			}
			ZnsbLoginDTO sscDTO = sendSmsCodeByUuid(request, lgdto);
			log.info("first sendSmsCodeByUuid is done.");
			if (GyUtils.isNull(sscDTO)) {
				sscDTO = sendSmsCodeByUuid(request, lgdto);
				log.info("second sendSmsCodeByUuid is done.");
			}
			// 二次连接没有数据后 返回
			if (GyUtils.isNull(sscDTO)) {
				log.info("sendSmsCodeByUuid request is fail.");
				return null;
			}
			ZnsbLoginDTO smlDTO = smsMessageLogin(request, lgdto);
			log.info("first smsMessageLogin is done.");
			if (GyUtils.isNull(smlDTO)) {
				smlDTO = smsMessageLogin(request, lgdto);
				log.info("second smsMessageLogin is done.");
			}
			// 二次连接没有数据后 返回
			if (GyUtils.isNull(smlDTO)) {
				log.info("smsMessageLogin request is fail.");
				return null;
			}
			ZnsbLoginDTO userResDTO = getUserInfo(request, lgdto);
			log.info("first getUserInfo is done.");
			if (GyUtils.isNull(userResDTO)) {
				getUserInfo(request, lgdto);
				log.info("second getUserInfo is done.");
			} else {
				log.info("login is success.");
			}
		}
		return lgdto;
	}

	@Override
	public ZnsbLoginDTO factorAccountLogin(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		request.setEncryptCode("2");
		Map<String, String> dataMap = new HashMap<>();
		dataMap.put(JmdlConstants.LETTER_CLIENT_ID, request.getClientId());
		// dataMap.put("creditCode", "91340200MA2U59YU1Q");
		// dataMap.put("account", "340207199905010014");
		// dataMap.put("password", "Abcd1234");
		dataMap.put("creditCode", request.getCreditCode());
		dataMap.put("account", request.getAccount());
		dataMap.put("password", request.getPdParam());
		dataMap.put("redirect_uri", request.getRedirectUri());
		// SM4 sm4 = SmUtil.sm4(Hex.encodeHexString(request.getPassword().getBytes()).getBytes());
		// String secret = sm4.encryptHex(JsonUtils.toJson(dataMap));
		String hexKey = Hex.encodeHexString(request.getPassword().getBytes());
		String secret = Hex.encodeHexString(SM4Utils.encrypt(hexKey, JsonUtils.toJson(dataMap)));
		request.changeEncryptSignature(secret);
		request.setDatagram(secret);
		final String requestJson = JsonUtils.toJson(request.toParmMap());
		final Map<String, String> headers = new HashMap<>();
		addHeaders(headers);
		ZnsbOkHttpUtils.addHeaders(request, lgdto, headers);
		// if (GyUtils.isNotNull( request.getCookie())) {
		// headers.put("Cookie", request.getCookie());
		// }
		// headers.put("X-TEMP-INFO", lgdto.getPubKeyDTO().getUuid());
		// headers.put("X-APP-CLIENTID", request.getClientId());

		String result = null; // 接收税局相应报文（未解密）
		try {
			String requestUrl = getBaseURL(request) + JmdlConstants.FACTORACCOUNTLOGIN_URI;
			result = ZnsbOkHttpUtils.postXdj(requestUrl, requestJson, headers);
			request.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
		} catch (Exception e) {
			log.info("factorAccountLogin Exception happens.{}", e.getMessage());
			ZnsbLoginDTO res = new ZnsbLoginDTO();
			res.setCode("1");
			res.setMsg(e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			String httpStatusCode = (String) resJsonObj.get(JmdlConstants.LETTER_HTTP_STATUS_CODE);
			String responseBody = (String) resJsonObj.get(JmdlConstants.LETTER_RESPONSE_BODY);
			if (!GyUtils.isNull(responseBody)) {
				final ZnsbLoginDTO res = JsonUtils.toBean(responseBody, ZnsbLoginDTO.class);
				String datagram = res.getDatagram();
				if (GyUtils.isNotNull(datagram)) {
					FactorAccountLoginResDTO resdto =
							decryptDatagram(hexKey, datagram, FactorAccountLoginResDTO.class);
					lgdto.setFactorAccountLoginResDTO(resdto);
					// {"mobile":"158****1111","uuid":"ac22f9a446cc46849427de9275b9c0ee"}"
					return res;
				} else {
					log.info("返回数据为空。code:{},messag:{}", res.getCode(), res.getMsg());
					res.setCode("1");
					return res;
				}
			}
			if (!"200".equals(httpStatusCode)) {
				ZnsbLoginDTO res = new ZnsbLoginDTO();
				res.setCode("1");
				res.setMsg(JmdlConstants.ERROR_PREX + httpStatusCode);
				return res;
			}
		}
		return null;
	}

	private static <T> T decryptDatagram(String hexKey, String datagram, Class<T> class1) {
		if (GyUtils.isNull(hexKey) || GyUtils.isNull(datagram)) {
			return null;
		}
		try {
			byte[] bytes = SM4Utils.decrypt(hexKey, Hex.decodeHex(datagram));
			return JsonUtils.toBean(new String(bytes), class1);
		} catch (DecoderException e) {
			log.info("DecoderException happens.{}", e.getMessage());
		}
		return null;
	}

	/**
	 * 验证码登录
	 *
	 * @name 验证码登录
	 * @time 2024-05-15
	 * @param ZnsbLoginReqDTO
	 * @return ZnsbLoginDTO
	 * @throws AppException
	 * <AUTHOR>
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 */
	@Override
	public ZnsbLoginDTO smsMessageLogin(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		request.setEncryptCode("2");
		Map<String, String> dataMap = new HashMap<>();
		dataMap.put("uuid", lgdto.getFactorAccountLoginResDTO().getUuid());
		dataMap.put(JmdlConstants.LETTER_CLIENT_ID, request.getClientId());
		dataMap.put("redirect_uri", request.getRedirectUri());
		// dataMap.put("smsCode", getSmsCode(lgdto.getSendSmsCodeByUuidResDTO().getSmscode_id()));
		dataMap.put("smsCode", request.getSmsCode());
		dataMap.put("smscode_id", lgdto.getSendSmsCodeByUuidResDTO().getSmscode_id());
		String hexKey = Hex.encodeHexString(request.getPassword().getBytes());
		String secret = Hex.encodeHexString(SM4Utils.encrypt(hexKey, JsonUtils.toJson(dataMap)));
		request.changeEncryptSignature(secret);
		request.setDatagram(secret);
		final String requestJson = JsonUtils.toJson(request.toParmMap());
		// final Map<String, String> headers = new HashMap<>();
		final Map<String, String> headers = ZnsbOkHttpUtils.getHeaders(request, lgdto);
		addHeaders(headers);
		// if (GyUtils.isNotNull( request.getCookie())) {
		// headers.put("Cookie", request.getCookie());
		// }
		// headers.put("X-TEMP-INFO", lgdto.getPubKeyDTO().getUuid());
		// headers.put("X-APP-CLIENTID", request.getClientId());
		String result = null; // 接收税局相应报文（未解密）
		try {
			String requestUrl = getBaseURL(request) + JmdlConstants.SMSMESSAGELOGIN_URI;
			result = ZnsbOkHttpUtils.postXdj(requestUrl, requestJson, headers);
			request.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
		} catch (Exception e) {
			log.info("smsMessageLogin Exception happens.{}", e.getMessage());
			ZnsbLoginDTO res = new ZnsbLoginDTO();
			res.setCode("1");
			res.setMsg(e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			String httpStatusCode = (String) resJsonObj.get(JmdlConstants.LETTER_HTTP_STATUS_CODE);
			String responseBody = (String) resJsonObj.get(JmdlConstants.LETTER_RESPONSE_BODY);
			log.info("smsMessageLogin responseBody:{}", responseBody);
			if (!GyUtils.isNull(responseBody)) {
				final ZnsbLoginDTO res = JsonUtils.toBean(responseBody, ZnsbLoginDTO.class);
				String datagram = res.getDatagram();
				SmsMessageLoginResDTO resdto =
						decryptDatagram(hexKey, datagram, SmsMessageLoginResDTO.class);
				log.info("smsMessageLogin resdto:{}", resdto);
				lgdto.setSmsMessageLoginResDTO(resdto);
				if (GyUtils.isNotNull(resdto)) {
					if (GyUtils.isNull(resdto.getAccess_token())
							&& GyUtils.isNotNull(resdto.getUuid())) {
						quickLogin(request, lgdto);
					}
				}
				if (GyUtils.isNull(lgdto.getSmsMessageLoginResDTO().getAccess_token())) {
					return null;
				}
				// {"access_token":"eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjVhYzZlYjYwMjEzYzQ4YWNiOTY3NmIyYjAyNTE4MDc0In0.VMANndXjaVkywUPQaITQ8IknHoop1yMuogalZA3tVFG2YEl5gbWxa9Jin1uOtwpuN_3FLnR15_h7v7E4gRMapw",
				// "refresh_token":"eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImM5YzhiMmZlM2NkMjQ1MWFiODVhZjFkNmY2MTNlOWQ0In0.J3GfdsH1dcx2rJe4YPogmUkVTe-1SmbVzCEHX-C1MIbbSZQtqJki-l2fDzGWwLLNY6r7vaLMCjmZVpOm4vLDeg",
				// "expires_in":3600,
				// "code":"CF8631CCCDFA4672A590767E2F53430F",
				// "token_type":"bearer",
				// "scope":"","bz":"1","uid":"","fid":"",
				// "reg_number":"","trusted_level":"",
				// "overseas":"","clientId":"","qrcode_status":""}"
				return res;

				// "access_token":" "参数 查询userinfo
				// 返回值
				// "{client_id:"d27dk9ssee2b4c7dak959e67sad55ksk",
				// "client_type":"02",
				// "user_type":"2",
				// "user_name":"ZZ77777",
				// "full_name":"陶鑫洋","trusted_level":"四级",
				// "newEtaxFlag":"0","overseas":"0",
				// "registrationTypeOfLevySubject":"1110",
				// "functionMenue":["I0000000019","I0000000020","I0000000021","I0000000022","I0000000160","I0000000164","I3400000001"],
				// "agreementNumber":"","expires_in":3600,
				// "taxStatus":"0","businessAddress":"生产经营地址",
				// "login_level":"二级","last_trusted_data":"2024-05-16 11:41:42",
				// "last_trusted_date":"2024-05-16 11:41:42",
				// "lastLoginTime":"2024-05-16 11:10:35",
				// "user_id":"34000dc548a621c546ca9de3185db0602d4b",
				// "enterprise_id":"3400ea80a6df56154fa99f6d2b18527ad4e1",
				// "agency_enterprise_id":"","tax_authority_code":"***********","tax_authority_level":"","tax_authority_name":"国家税务总局芜湖市鸠江区税务局税源管理一股","taxpayer_id":"91340200MA2U59YU1Q","company_name":"纳税人******738","last_face_verify_date":"","id_card":"340207199905010014","card_type":"201","reg_number":"10213402000001133452","trust_code":"91340200MA2U59YU1Q","phone":"***********","mobile":"***********","tax_file_no":"91340200MA2U59YU1Q","position":"03","login_type":"2","relatedType":"03","creditCode":"91340200MA2U59YU1Q","sprole_list":"","taxer_code":"","taxer_name":"陶鑫洋","tax_id":"34000dc548a621c546ca9de3185db0602d4b","taxer_status_code":"","taxer_status_name":"","agencyCreditCode":"","agencyName":"","agencyReg_number":"","areaPrefix":"3400","areaPreName":"安徽省","enterprise_type":"0","initial_enterprise_type":"0","crossInspectionNumber":"","projectName":"","crossRegionalPropertyTaxSubjectRegistrationMark":"","gender":"1","nationality":"156","startDate":"20100101","endDate":"20291129","birthdate":"","address":"安徽省淮南市凤台县和平里3号","email":"<EMAIL>","enterpriseStatus":"01","agencyTaxpayer_id":"","agencyTaxpayerStatusCode":"","taxpayerStatusCode":"03","register_time":"2023-10-27
				// 15:56:45","agencyType":"","isHavePsw":"1","competent_tax_bureau_code":"***********","competent_tax_bureau_name":"国家税务总局芜湖市鸠江区税务局","mainIdentity":"1","outgoingBusinessCertificateUUID":"","tax_authority_abbreviation":"芜湖市鸠江区税务局税源管理一股","competent_tax_bureau_abbreviation":"芜湖市鸠江区税务局","lang":"null","rarely":"0","areaName":"安徽"}"

				// 参数{uid:34000dc548a621c546ca9de3185db0602d4b} // 用户id
				// "/auth/user/agreementListQuery"
				// "[{"agreementVersion":"2021.11.01.1","signingTaxAuthorityCode":"***********","endDate":"2099-12-31","signingStatus":"1","agreementName":"安徽省电子税务局用户协议","agreementCode":"01","signingTime":"2023-10-30
				// 11:33:10","startDate":"2021-11-01"},{"agreementVersion":"2022.03.22.2","signingTaxAuthorityCode":"***********","endDate":"2071-11-25","signingStatus":"1","agreementName":"个人信息保护告知同意书","agreementCode":"02","signingTime":"2023-10-30
				// 11:33:10","startDate":"2022-03-22"},{"agreementVersion":"2022.03.22.2","signingTaxAuthorityCode":"***********","endDate":"2071-11-25","signingStatus":"1","agreementName":"人脸识别服务协议","agreementCode":"03","signingTime":"2023-10-30
				// 11:33:10","startDate":"2022-03-22"}]"
			}
			if (!"200".equals(httpStatusCode)) {
				ZnsbLoginDTO res = new ZnsbLoginDTO();
				res.setCode("1");
				res.setMsg(JmdlConstants.ERROR_PREX + httpStatusCode);
				return res;
			}
		}
		return null;
	}

	// private String getSmsCode(String smscode_id) {
	// return "123456";
	// }

	/**
	 * 发送手机验证码
	 *
	 * @name 发送手机验证码
	 * @time 2024-05-15
	 * @param ZnsbLoginReqDTO
	 * @return ZnsbLoginDTO
	 * @throws AppException
	 * <AUTHOR>
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 */
	@Override
	public ZnsbLoginDTO sendSmsCodeByUuid(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		request.setEncryptCode("2");
		Map<String, String> dataMap = new HashMap<>();
		dataMap.put("uuid", lgdto.getFactorAccountLoginResDTO().getUuid());
		// SM4 sm4 = SmUtil.sm4(Hex.encodeHexString(request.getPassword().getBytes()).getBytes());
		// String secret = sm4.encryptHex(JsonUtils.toJson(dataMap));
		String hexKey = Hex.encodeHexString(request.getPassword().getBytes());
		String secret = Hex.encodeHexString(SM4Utils.encrypt(hexKey, JsonUtils.toJson(dataMap)));
		request.changeEncryptSignature(secret);
		request.setDatagram(secret);
		final String requestJson = JsonUtils.toJson(request.toParmMap());
		final Map<String, String> headers = new HashMap<>();
		addHeaders(headers);
		ZnsbOkHttpUtils.addHeaders(request, lgdto, headers);
		// if (GyUtils.isNotNull( request.getCookie())) {
		// headers.put("Cookie", request.getCookie());
		// }
		// headers.put("X-TEMP-INFO", lgdto.getPubKeyDTO().getUuid());
		// headers.put("X-APP-CLIENTID", request.getClientId());
		String result = null; // 接收税局相应报文（未解密）
		try {
			String requestUrl = getBaseURL(request) + JmdlConstants.SENDSMSCODEBYUUID_URI;
			result = ZnsbOkHttpUtils.postXdj(requestUrl, requestJson, headers);
			request.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
		} catch (Exception e) {
			log.info("sendSmsCodeByUuid Exception happens . {}", e.getMessage());
			ZnsbLoginDTO res = new ZnsbLoginDTO();
			res.setCode("1");
			res.setMsg(e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			String httpStatusCode = (String) resJsonObj.get(JmdlConstants.LETTER_HTTP_STATUS_CODE);
			String responseBody = (String) resJsonObj.get(JmdlConstants.LETTER_RESPONSE_BODY);
			if (!GyUtils.isNull(responseBody)) {
				final ZnsbLoginDTO res = JsonUtils.toBean(responseBody, ZnsbLoginDTO.class);
				String datagram = res.getDatagram();
				SendSmsCodeByUuidResDTO resdto =
						decryptDatagram(hexKey, datagram, SendSmsCodeByUuidResDTO.class);
				lgdto.setSendSmsCodeByUuidResDTO(resdto);
				// {"mobile":"158****1111","bz":"1","expire_in":300,"smscode_id":"cab714a93c4b40c2a3ecb7feabcb3c8b"}"
				return res;
			}
			if (!"200".equals(httpStatusCode)) {
				ZnsbLoginDTO res = new ZnsbLoginDTO();
				res.setCode("1");
				res.setMsg(JmdlConstants.ERROR_PREX + httpStatusCode);
				return res;
			}
		}
		return null;

	}

	/**
	 * 统一登录请求
	 *
	 * @name 统一登录请求
	 * @time 2024-05-16
	 * @param ZnsbLoginReqDTO
	 * @return ZnsbLoginDTO
	 * @throws AppException
	 * <AUTHOR>
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 */
	@Override
	public ZnsbLoginDTO ssoLogin(ZnsbLoginReqDTO request) {
		try {
			final String requestJson = null;
			final Map<String, String> headers = new HashMap<>();
			// ZnsbOkHttpUtils.getXdjLocation(JmdlConstants.FIRST_URI, requestJson, headers);
			ZnsbOkHttpUtils.getXdjLocation(request.getMainURL(), requestJson, headers);
			// String requestUrl = JmdlConstants.SSOLOGIN_URI_1;
			String requestUrl = request.getLoginURL();
			String location2 = ZnsbOkHttpUtils.getXdjLocation(requestUrl, requestJson, headers);
			String location = null;
			if (GyUtils.isNotNull(location2)) {
				location = ZnsbOkHttpUtils.getXdjLocation(location2, requestJson, headers);
				if (GyUtils.isNull(location)) {
					log.info("登录location is null .");
					return null;
				}
			}
			if (GyUtils.isNull(location)) {
				location = request.getLoginURL();
			}
			// baseurl
			int indexLoc = location.indexOf("/api/");
			request.setBaseURL(location.substring(0, indexLoc));
			request.setLocation(location);
			request.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
			int index = location.indexOf("?");
			if (-1 != index) {
				String searchInfo = location.substring(index + 1);
				if (GyUtils.isNotNull(searchInfo)) {
					String[] searchList = searchInfo.split("&");
					Map<String, Object> map = request.getSearchMap();
					for (int i = 0; i < searchList.length; i++) {
						String str = searchList[i];
						// String[] strs = str.split("=");
						// map.put(strs[0], strs[1]);
						int ind = str.indexOf("=");
						if (-1 != ind) {
							String name = str.substring(0, ind);
							String value = str.substring(ind + 1);
							map.put(name, value);
						}
					}
					SSOLoginResDTO resdto = new SSOLoginResDTO(map);
					request.setClientId((String) map.get(JmdlConstants.LETTER_CLIENT_ID));
					String uri = URLDecoder.decode((String) map.get("redirect_uri"), "UTF-8");
					request.setRedirectUri(uri);
					request.setSSOLoginResDTO(resdto);
				}
			}
			ZnsbOkHttpUtils.getXdjLocation(location, requestJson, headers);
		} catch (Exception e) {
			log.error("Exception happens . {}", e.getMessage());
		}
		return null;
	}

	/**
	 * 获取用户信息
	 *
	 * @name 获取用户信息
	 * @time 2024-05-16
	 * @param ZnsbLoginReqDTO
	 * @return ZnsbLoginDTO
	 * @throws AppException
	 * <AUTHOR>
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 */
	@Override
	public ZnsbLoginDTO getUserInfo(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		request.setEncryptCode("2");
		Map<String, String> dataMap = new HashMap<>();
		if (GyUtils.isNull(lgdto.getSmsMessageLoginResDTO())
				|| GyUtils.isNull(lgdto.getSmsMessageLoginResDTO().getAccess_token())) {
			log.info("access_token is null .");
			return null;
		}
		dataMap.put("access_token", lgdto.getSmsMessageLoginResDTO().getAccess_token());
		String hexKey = Hex.encodeHexString(request.getPassword().getBytes());
		String secret = Hex.encodeHexString(SM4Utils.encrypt(hexKey, JsonUtils.toJson(dataMap)));
		request.changeEncryptSignature(secret);
		request.setDatagram(secret);
		final String requestJson = JsonUtils.toJson(request.toParmMap());
		final Map<String, String> headers = new HashMap<>();
		addHeaders(headers);
		ZnsbOkHttpUtils.addHeaders(request, lgdto, headers);
		request.setAccessToken(lgdto.getSmsMessageLoginResDTO().getAccess_token());
		// request.addTokenCookie();
		// if (GyUtils.isNotNull( request.getCookie())) {
		// headers.put("Cookie", request.getCookie());
		// }
		//// headers.put("X-Sm4-Info", "0");
		// //headers.put("X-KEY-INFO", "");
		// headers.put("X-TEMP-INFO", lgdto.getPubKeyDTO().getUuid());
		// headers.put("X-APP-CLIENTID", request.getClientId());
		// headers.put("Authorization", lgdto.getSmsMessageLoginResDTO().getAccess_token());
		String result = null; // 接收税局相应报文（未解密）
		try {
			String requestUrl = getBaseURL(request) + JmdlConstants.GETUSERINFO_URI;
			result = ZnsbOkHttpUtils.postXdj(requestUrl, requestJson, headers);
			request.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
		} catch (Exception e) {
			log.info("getUserInfo Exception happens.{}", e.getMessage());
			ZnsbLoginDTO res = new ZnsbLoginDTO();
			res.setCode("1");
			res.setMsg(e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			String httpStatusCode = (String) resJsonObj.get(JmdlConstants.LETTER_HTTP_STATUS_CODE);
			String responseBody = (String) resJsonObj.get(JmdlConstants.LETTER_RESPONSE_BODY);
			if (!GyUtils.isNull(responseBody)) {
				final ZnsbLoginDTO res = JsonUtils.toBean(responseBody, ZnsbLoginDTO.class);
				String datagram = res.getDatagram();
				UserinfoDTO resdto = decryptDatagram(hexKey, datagram, UserinfoDTO.class);
				log.info("userinfo:{}", resdto);
				lgdto.setUserinfoDTO(resdto);
				return res;
			}
			if (!"200".equals(httpStatusCode)) {
				ZnsbLoginDTO res = new ZnsbLoginDTO();
				res.setCode("1");
				res.setMsg(JmdlConstants.ERROR_PREX + httpStatusCode);
				return res;
			}
		}
		return null;

	}

	@Override
	public ZnsbLoginDTO processSmsCodeLogin(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		ZnsbLoginDTO smlDTO = smsMessageLogin(request, lgdto);
		log.info("first smsMessageLogin is done.");
		if (GyUtils.isNull(smlDTO)) {
			smlDTO = smsMessageLogin(request, lgdto);
			log.info("second smsMessageLogin is done.");
		}
		// 二次连接没有数据后 返回
		if (GyUtils.isNull(smlDTO)) {
			log.info("smsMessageLogin request is fail.");
			return null;
		} else {
			ZnsbLoginDTO userResDTO = getUserInfo(request, lgdto);
			log.info("first getUserInfo is done.");
			if (GyUtils.isNull(userResDTO)) {
				getUserInfo(request, lgdto);
				log.info("second getUserInfo is done.");
			} else {
				request.setSuccessFlag(JmdlConstants.CODE_Y);
				log.info("login is success.");
			}
		}
		return lgdto;
	}

	@Override
	public ZnsbLoginDTO processAccountLogin(ZnsbLoginReqDTO request) {

		// ZnsbLoginReqDTO request = new ZnsbLoginReqDTO();
		ssoLogin(request);
		log.info("location 302 exchange is done.");
		request.setEncryptCode("0");
		request.initSignature();
		ZnsbLoginDTO lgdto = getPublicKey(request);
		log.info("first getPublicKey is done.");
		if (GyUtils.isNull(lgdto) || GyUtils.isEquals("1", lgdto.getCode())) {
			lgdto = getPublicKey(request);
			log.info("second getPublicKey is done.");
		}
		// 二次连接没有数据后 返回
		if (GyUtils.isNull(lgdto) || GyUtils.isEquals("1", lgdto.getCode())) {
			log.info("getPublicKey request is fail.");
			return null;
		}
		String pubKey = lgdto.getPubKeyDTO().getPublicKey();
		if (GyUtils.isNotNull(pubKey)) {
			ZnsbLoginDTO sendsm4Dto = sendSm4(request, lgdto);
			log.info("first sendSm4 is done.");
			if (GyUtils.isNull(sendsm4Dto)) {
				sendsm4Dto = sendSm4(request, lgdto);
				log.info("second sendSm4 is done.");
			}
			// 二次连接没有数据后 返回
			if (GyUtils.isNull(sendsm4Dto)) {
				log.info("sendSm4 request is fail.");
				return null;
			}
			// 是否调用图片验证
			boolean bool = false;
			// 获取操作配置
			if (GyUtils.isNotNull(request.getDzswjdzDO().getPzuuid())) {
				ZnsbDzswjdzCzpzDTO czpz = CacheUtils.getTableData("dlfw_dzswj_dlpz",
						request.getDzswjdzDO().getPzuuid(), ZnsbDzswjdzCzpzDTO.class);
				if (GyUtils.isNotNull(czpz)) {
					CaptchaDlpzExtDTO dlpzDTO = czpz.extendInfo(CaptchaDlpzExtDTO.class);
					if (GyUtils.isNotNull(dlpzDTO) && GyUtils
							.isEquals(dlpzDTO.getCaptchaCheckFlag(), JmdlConstants.CODE_Y)) {
						bool = true;
					}
				}
				log.info("获取到操作配置，{}", czpz);
			} else {
				log.info("未获取到操作配置！");
			}
			// 调用图片验证
			if (bool) {
				// 添加重试机制，最多重试5次
				ZnsbLoginDTO procCap = null;
				boolean captchaSuccess = false;
				int maxRetries = 5;
				int retryCount = 0;

				while (!captchaSuccess && retryCount < maxRetries) {
					try {
						if (retryCount > 0) {
							log.info("正在进行第{}次验证码校验重试...", retryCount);
							// 重试间隔，避免频繁请求
							Thread.sleep(1000);
						}

						procCap = captchaCheckService.processCaptchaCheck(request, lgdto);

						if (!GyUtils.isEquals(JmdlConstants.CODE_1, procCap.getCode())) {
							captchaSuccess = true;
							log.info("验证码校验成功，共尝试{}次", retryCount + 1);
						} else {
							retryCount++;
							log.info("验证码校验失败，已尝试{}次，错误码：{}", retryCount, procCap.getCode());
						}
					} catch (Exception e) {
						retryCount++;
						log.error("验证码校验异常，已尝试{}次，异常信息：{}", retryCount, e.getMessage());
						if (retryCount >= maxRetries) {
							log.error("验证码校验重试次数已达上限");
						}
					}
				}

				if (!captchaSuccess) {
					log.info("验证码校验失败，已达到最大重试次数{}", maxRetries);
					return null;
				}
			}
			// 调用成功后继续
			ZnsbLoginDTO fatDTO = null;
			if (GyUtils.isEquals("1000", sendsm4Dto.getCode())) {
				fatDTO = factorAccountLogin(request, lgdto);
				log.info("first factorAccountLogin is done.");
			}
			if (GyUtils.isNull(fatDTO)) {
				fatDTO = factorAccountLogin(request, lgdto);
				log.info("second factorAccountLogin is done.");
			}
			// 二次连接没有数据后 返回
			if (GyUtils.isNull(fatDTO)) {
				log.info("factorAccountLogin request is fail.");
				return null;
			}
			ZnsbLoginDTO sscDTO = sendSmsCodeByUuid(request, lgdto);
			log.info("first sendSmsCodeByUuid is done.");
			if (GyUtils.isNull(sscDTO)) {
				sscDTO = sendSmsCodeByUuid(request, lgdto);
				log.info("second sendSmsCodeByUuid is done.");
			}
			// 二次连接没有数据后 返回
			if (GyUtils.isNull(sscDTO)) {
				log.info("sendSmsCodeByUuid request is fail.");
				return null;
			} else {
				request.setSuccessFlag(JmdlConstants.CODE_Y);
				log.info("processAccountLogin request is success.");
			}
		}
		return lgdto;
	}

	@Override
	public ZnsbLoginDTO quickLogin(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		request.setEncryptCode("2");
		Map<String, String> dataMap = new HashMap<>();
		dataMap.put("uuid", lgdto.getSmsMessageLoginResDTO().getUuid());
		dataMap.put(JmdlConstants.LETTER_CLIENT_ID, request.getClientId());
		dataMap.put("redirect_uri", request.getRedirectUri());
		List<ZrrlxDTO> list = lgdto.getSmsMessageLoginResDTO().getRelationPoList();
		if (GyUtils.isNull(list)) {
			log.info("quickLogin 缺少参数 relationPoList");
			return null;
		}
		// 根据优先级选择relatedType：02 > 03 > 第一个
		String relatedType = list.get(0).getZrrlx(); // 默认选择第一个
		for (ZrrlxDTO item : list) {
			if ("02".equals(item.getZrrlx())) {
				relatedType = "02";
				break; // 找到02就直接使用，优先级最高
			} else if ("03".equals(item.getZrrlx())) {
				relatedType = "03"; // 暂存03，继续查找是否有02
			}
		}
		dataMap.put("relatedType", relatedType);
		// dataMap.put("smsCode", request.getSmsCode());
		// dataMap.put("smscode_id", lgdto.getSendSmsCodeByUuidResDTO().getSmscode_id());
		String hexKey = Hex.encodeHexString(request.getPassword().getBytes());
		String secret = Hex.encodeHexString(SM4Utils.encrypt(hexKey, JsonUtils.toJson(dataMap)));
		request.changeEncryptSignature(secret);
		request.setDatagram(secret);
		final String requestJson = JsonUtils.toJson(request.toParmMap());
		final Map<String, String> headers = new HashMap<>();
		addHeaders(headers);
		ZnsbOkHttpUtils.addHeaders(request, lgdto, headers);
		// if (GyUtils.isNotNull( request.getCookie())) {
		// headers.put("Cookie", request.getCookie());
		// }
		// headers.put("X-TEMP-INFO", lgdto.getPubKeyDTO().getUuid());
		// headers.put("X-APP-CLIENTID", request.getClientId());
		String result = null; // 接收税局相应报文（未解密）
		try {
			String requestUrl = getBaseURL(request) + JmdlConstants.QUICKLOGIN_URI;
			result = ZnsbOkHttpUtils.postXdj(requestUrl, requestJson, headers);
			request.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
		} catch (Exception e) {
			log.info("quickLogin Exception happens.{}", e.getMessage());
			ZnsbLoginDTO res = new ZnsbLoginDTO();
			res.setCode("1");
			res.setMsg(e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			log.info("resJsonObj:{}", resJsonObj);
			String httpStatusCode = (String) resJsonObj.get(JmdlConstants.LETTER_HTTP_STATUS_CODE);
			String responseBody = (String) resJsonObj.get(JmdlConstants.LETTER_RESPONSE_BODY);
			if (!GyUtils.isNull(responseBody)) {
				final ZnsbLoginDTO res = JsonUtils.toBean(responseBody, ZnsbLoginDTO.class);
				log.info("res:{}", res);
				String datagram = res.getDatagram();
				SmsMessageLoginResDTO resdto =
						decryptDatagram(hexKey, datagram, SmsMessageLoginResDTO.class);
				resdto.setUuid(lgdto.getSmsMessageLoginResDTO().getUuid());
				resdto.setRelationPoList(list);
				lgdto.setSmsMessageLoginResDTO(resdto);

				// {"access_token":"eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjVhYzZlYjYwMjEzYzQ4YWNiOTY3NmIyYjAyNTE4MDc0In0.VMANndXjaVkywUPQaITQ8IknHoop1yMuogalZA3tVFG2YEl5gbWxa9Jin1uOtwpuN_3FLnR15_h7v7E4gRMapw",
				// "refresh_token":"eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImM5YzhiMmZlM2NkMjQ1MWFiODVhZjFkNmY2MTNlOWQ0In0.J3GfdsH1dcx2rJe4YPogmUkVTe-1SmbVzCEHX-C1MIbbSZQtqJki-l2fDzGWwLLNY6r7vaLMCjmZVpOm4vLDeg",
				// "expires_in":3600,
				// "code":"CF8631CCCDFA4672A590767E2F53430F",
				// "token_type":"bearer",
				// "scope":"","bz":"1","uid":"","fid":"",
				// "reg_number":"","trusted_level":"",
				// "overseas":"","clientId":"","qrcode_status":""}"
				return res;

				// "access_token":" "参数 查询userinfo
				// 返回值
				// "{LETTER_CLIENT_ID:"d27dk9ssee2b4c7dak959e67sad55ksk",
				// "client_type":"02",
				// "user_type":"2",
				// "user_name":"ZZ77777",
				// "full_name":"陶鑫洋","trusted_level":"四级",
				// "newEtaxFlag":"0","overseas":"0",
				// "registrationTypeOfLevySubject":"1110",
				// "functionMenue":["I0000000019","I0000000020","I0000000021","I0000000022","I0000000160","I0000000164","I3400000001"],
				// "agreementNumber":"","expires_in":3600,
				// "taxStatus":"0","businessAddress":"生产经营地址",
				// "login_level":"二级","last_trusted_data":"2024-05-16 11:41:42",
				// "last_trusted_date":"2024-05-16 11:41:42",
				// "lastLoginTime":"2024-05-16 11:10:35",
				// "user_id":"34000dc548a621c546ca9de3185db0602d4b",
				// "enterprise_id":"3400ea80a6df56154fa99f6d2b18527ad4e1",
				// "agency_enterprise_id":"","tax_authority_code":"***********","tax_authority_level":"","tax_authority_name":"国家税务总局芜湖市鸠江区税务局税源管理一股","taxpayer_id":"91340200MA2U59YU1Q","company_name":"纳税人******738","last_face_verify_date":"","id_card":"340207199905010014","card_type":"201","reg_number":"10213402000001133452","trust_code":"91340200MA2U59YU1Q","phone":"***********","mobile":"***********","tax_file_no":"91340200MA2U59YU1Q","position":"03","login_type":"2","relatedType":"03","creditCode":"91340200MA2U59YU1Q","sprole_list":"","taxer_code":"","taxer_name":"陶鑫洋","tax_id":"34000dc548a621c546ca9de3185db0602d4b","taxer_status_code":"","taxer_status_name":"","agencyCreditCode":"","agencyName":"","agencyReg_number":"","areaPrefix":"3400","areaPreName":"安徽省","enterprise_type":"0","initial_enterprise_type":"0","crossInspectionNumber":"","projectName":"","crossRegionalPropertyTaxSubjectRegistrationMark":"","gender":"1","nationality":"156","startDate":"20100101","endDate":"20291129","birthdate":"","address":"安徽省淮南市凤台县和平里3号","email":"<EMAIL>","enterpriseStatus":"01","agencyTaxpayer_id":"","agencyTaxpayerStatusCode":"","taxpayerStatusCode":"03","register_time":"2023-10-27
				// 15:56:45","agencyType":"","isHavePsw":"1","competent_tax_bureau_code":"***********","competent_tax_bureau_name":"国家税务总局芜湖市鸠江区税务局","mainIdentity":"1","outgoingBusinessCertificateUUID":"","tax_authority_abbreviation":"芜湖市鸠江区税务局税源管理一股","competent_tax_bureau_abbreviation":"芜湖市鸠江区税务局","lang":"null","rarely":"0","areaName":"安徽"}"

				// 参数{uid:34000dc548a621c546ca9de3185db0602d4b} // 用户id
				// "/auth/user/agreementListQuery"
				// "[{"agreementVersion":"2021.11.01.1","signingTaxAuthorityCode":"***********","endDate":"2099-12-31","signingStatus":"1","agreementName":"安徽省电子税务局用户协议","agreementCode":"01","signingTime":"2023-10-30
				// 11:33:10","startDate":"2021-11-01"},{"agreementVersion":"2022.03.22.2","signingTaxAuthorityCode":"***********","endDate":"2071-11-25","signingStatus":"1","agreementName":"个人信息保护告知同意书","agreementCode":"02","signingTime":"2023-10-30
				// 11:33:10","startDate":"2022-03-22"},{"agreementVersion":"2022.03.22.2","signingTaxAuthorityCode":"***********","endDate":"2071-11-25","signingStatus":"1","agreementName":"人脸识别服务协议","agreementCode":"03","signingTime":"2023-10-30
				// 11:33:10","startDate":"2022-03-22"}]"
			}
			if (!"200".equals(httpStatusCode)) {
				ZnsbLoginDTO res = new ZnsbLoginDTO();
				res.setCode("1");
				res.setMsg(JmdlConstants.ERROR_PREX + httpStatusCode);
				return res;
			}
		}
		return null;

	}

	@Override
	public ZnsbLoginDTO accountLogin(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		request.setEncryptCode("2");
		Map<String, String> dataMap = new HashMap<>();
		dataMap.put(JmdlConstants.LETTER_CLIENT_ID, request.getClientId());
		// dataMap.put("client_id", "default_client_id");

		// dataMap.put("creditCode", "91340200MA2U59YU1Q");
		// dataMap.put("account", "340207199905010014");
		// dataMap.put("password", "Abcd1234");
		dataMap.put("creditCode", request.getCreditCode());
		dataMap.put("account", request.getAccount());
		dataMap.put("password", request.getPdParam());
		dataMap.put("redirect_uri", request.getRedirectUri());
		// dataMap.put("redirect_uri",
		// "https://tpass.zhejiang.chinatax.gov.cn:8443/oauth2/callback");
		// SM4 sm4 = SmUtil.sm4(Hex.encodeHexString(request.getPassword().getBytes()).getBytes());
		// String secret = sm4.encryptHex(JsonUtils.toJson(dataMap));
		String hexKey = Hex.encodeHexString(request.getPassword().getBytes());
		String secret = Hex.encodeHexString(SM4Utils.encrypt(hexKey, JsonUtils.toJson(dataMap)));

		request.changeEncryptSignature(secret);
		request.setDatagram(secret);
		final String requestJson = JsonUtils.toJson(request.toParmMap());
		final Map<String, String> headers = new HashMap<>();
		addHeaders(headers);
		ZnsbOkHttpUtils.addHeaders(request, lgdto, headers);
		// if (GyUtils.isNotNull( request.getCookie())) {
		// headers.put("Cookie", request.getCookie());
		// }
		// headers.put("X-TEMP-INFO", lgdto.getPubKeyDTO().getUuid());
		// headers.put("X-APP-CLIENTID", request.getClientId());

		String result = null; // 接收税局相应报文（未解密）
		try {
			String requestUrl = getBaseURL(request) + JmdlConstants.ACCOUNTLOGIN_URI;
			result = ZnsbOkHttpUtils.postXdj(requestUrl, requestJson, headers);
			request.setCookie(headers.get(JmdlConstants.LETTER_SET_COOKIE));
		} catch (Exception e) {
			log.info("factorAccountLogin Exception happens.{}", e.getMessage());
			ZnsbLoginDTO res = new ZnsbLoginDTO();
			res.setCode("1");
			res.setMsg(e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			String httpStatusCode = (String) resJsonObj.get(JmdlConstants.LETTER_HTTP_STATUS_CODE);
			String responseBody = (String) resJsonObj.get(JmdlConstants.LETTER_RESPONSE_BODY);
			if (!GyUtils.isNull(responseBody)) {
				final ZnsbLoginDTO res = JsonUtils.toBean(responseBody, ZnsbLoginDTO.class);
				String datagram = res.getDatagram();
				if (GyUtils.isNotNull(datagram)) {
					SmsMessageLoginResDTO resdto =
							decryptDatagram(hexKey, datagram, SmsMessageLoginResDTO.class);
					lgdto.setSmsMessageLoginResDTO(resdto);
					// {"mobile":"158****1111","uuid":"ac22f9a446cc46849427de9275b9c0ee"}"
					return res;
				} else {
					log.info("返回数据为空。code:{},messag:{}", res.getCode(), res.getMsg());
					res.setCode("1");
					return res;
				}
			}
			if (!"200".equals(httpStatusCode)) {
				ZnsbLoginDTO res = new ZnsbLoginDTO();
				res.setCode("1");
				res.setMsg(JmdlConstants.ERROR_PREX + httpStatusCode);
				return res;
			}
		}
		return null;
	}


	@Override
	public boolean verifyCaptcha(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		boolean captchaSuccess = false;
		// 是否调用图片验证
		boolean bool = false;
		// 获取操作配置
		if (GyUtils.isNotNull(request.getDzswjdzDO().getPzuuid())) {
			ZnsbDzswjdzCzpzDTO czpz = CacheUtils.getTableData("dlfw_dzswj_dlpz",
					request.getDzswjdzDO().getPzuuid(), ZnsbDzswjdzCzpzDTO.class);
			if (GyUtils.isNotNull(czpz)) {
				CaptchaDlpzExtDTO dlpzDTO = czpz.extendInfo(CaptchaDlpzExtDTO.class);
				if (GyUtils.isNotNull(dlpzDTO)
						&& GyUtils.isEquals(dlpzDTO.getCaptchaCheckFlag(), JmdlConstants.CODE_Y)) {
					bool = true;
				}
			}
			log.info("获取到操作配置，{}", czpz);
		} else {
			log.info("未获取到操作配置！");
		}
		// 调用图片验证
		if (bool) {
			// 添加重试机制，最多重试5次
			ZnsbLoginDTO procCap = null;

			int maxRetries = 5;
			int retryCount = 0;

			while (!captchaSuccess && retryCount < maxRetries) {
				try {
					if (retryCount > 0) {
						log.info("正在进行第{}次验证码校验重试...", retryCount);
						// 重试间隔，避免频繁请求
						Thread.sleep(1000);
					}

					procCap = captchaCheckService.processCaptchaCheck(request, lgdto);

					if (!GyUtils.isEquals(JmdlConstants.CODE_1, procCap.getCode())) {
						captchaSuccess = true;
						log.info("验证码校验成功，共尝试{}次", retryCount + 1);
					} else {
						retryCount++;
						log.info("验证码校验失败，已尝试{}次，错误码：{}", retryCount, procCap.getCode());
					}
				} catch (Exception e) {
					retryCount++;
					log.error("验证码校验异常，已尝试{}次，异常信息：{}", retryCount, e.getMessage());
					if (retryCount >= maxRetries) {
						log.error("验证码校验重试次数已达上限");
					}
				}
			}

			if (!captchaSuccess) {
				log.info("验证码校验失败，已达到最大重试次数{}", maxRetries);

			}
		} else {
			captchaSuccess = true;
		}
		return captchaSuccess;
	}

	/**
	 * 测试SM4解密功能的主方法
	 * 
	 * @param args 命令行参数
	 * @throws DecoderException 十六进制解码异常
	 */
	// public static void main(String[] args) throws DecoderException {
	// String pwd = "diAJQrBKkYoWp3ZZ";
	// String hexKey = Hex.encodeHexString(pwd.getBytes());
	// String datagram =
	// "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";
	//
	// // 使用Map接收解密后的JSON数据，避免类型不匹配错误
	// @SuppressWarnings("unchecked")
	// Map<String, Object> resultMap = decryptDatagram(hexKey, datagram, Map.class);
	//
	// log.info("解密结果: {}", resultMap);
	//
	// }

	// public static void main(String[] args) throws DecoderException {
	// String randomNum = "OfQgiu32g4qeuDhg";
	// String salt = "dt!Ppb9t";
	// String password = randomNum.substring(0, 8) + salt;
	// String hexKey = org.apache.commons.codec.binary.Hex.encodeHexString(password.getBytes());
	// String datagram =
	// "0958bd7080922d758d1dc8ffee867039ab6d3737a6028daaad43815ce9e4d3c22dd6c37aa89d95f61c4ad9298950886b8ab5ebac790c8d64691d59b36368c881ec5450c8e5f508a809380295226875a777d8e244e9cc1312757734e8e8c7a8223ee05d1c027941ca299b9109569e703c2551e3e0c97c3106d56806acb2c034a83776b9101f14d54f2c240d5c9f9cbc40a584de9ebb0f958c0c664e5d9017697a4522ad2a18c3361adc1bacd6e70685d4a00de89c4fd5ab43b3a9b9780b2ffc4d4a3b2b11dd9fef1f79feb55542e74363";
	// byte[] bytes = SM4Utils.decrypt(hexKey,
	// org.apache.commons.codec.binary.Hex.decodeHex(datagram));
	// System.out.println(new String(bytes));
	// }
}
