package com.css.znsb.framework.dlfw.util;

import java.io.IOException;
import java.net.Proxy;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import com.css.znsb.framework.dlfw.constants.JmdlConstants;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.RemoteResDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.ZnsbLoginDTO;
import com.css.znsb.framework.dlfw.pojo.dto.jmdl.ZnsbLoginReqDTO;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Slf4j
public class ZnsbOkHttpUtils {

	private ZnsbOkHttpUtils() {
		// 构造方法
	}

	public static String post(String url, String requestStr, Map<String, String> header) throws IOException {
		MediaType mediaType = MediaType.Companion.parse("application/json");
//		RequestBody body = RequestBody.create(mediaType, requestStr);
		RequestBody body = RequestBody.Companion.create(requestStr, mediaType);
		Request.Builder requestBuild = new Request.Builder();

		if (!GyUtils.isNull(header)) {
			header.forEach(requestBuild::addHeader);
		}
		Request request = requestBuild.url(url).method("POST", body).addHeader("Content-Type", "application/json")
				.addHeader("Accept", "application/json").build();

		final OkHttpClient httpClient = getUnsafeOkHttpClient();
		Response response = httpClient.newCall(request).execute();
		JSONObject responseJsonObj = new JSONObject();
		int httpStatusCode = response.code();
		responseJsonObj.append("HTTP_STATUS_CODE", httpStatusCode);
		if (200 == httpStatusCode) {
			assert response.body() != null;
			String resBody = Objects.requireNonNull(response.body()).string();
			JSONObject resBodyJsonObj = JSONUtil.parseObj(resBody);
			if (!GyUtils.isNull(resBodyJsonObj.get("response"))) {
				JSONObject responseSrc = (JSONObject) resBodyJsonObj.get("response");
				JSONObject error = (JSONObject) responseSrc.get("error");
				error.set("Code", error.get("code"));
				error.set("Message", error.get("message"));
				responseSrc.set("Error", error);
				JSONObject znsbRes = new JSONObject();
				znsbRes.set("Response", responseSrc);
				resBody = znsbRes.toString();
			}
			if (!GyUtils.isNull(resBodyJsonObj.get("returnCode"))) {
				JSONObject resType2 = new JSONObject();
				JSONObject responseType2 = new JSONObject();
				responseType2.set("Data", resBodyJsonObj);
				responseType2.set("RequestId", "tscl");
				resType2.set("Response", responseType2);
				resBody = resType2.toString();
			}
			responseJsonObj.append("RESPONSE_BODY", resBody);
		}
		return responseJsonObj.toString();
	}

	private static OkHttpClient getUnsafeOkHttpClient() {
		return getUnsafeOkHttpClient(null);
//        try {
//            // 创建一个信任所有证书的TrustManager
//            final TrustManager[] trustAllCerts = new TrustManager[]{
//                new X509TrustManager() {
//                    @Override
//                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
//                    }
//
//                    @Override
//                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
//                    }
//
//                    @Override
//                    public X509Certificate[] getAcceptedIssuers() {
//                        return new X509Certificate[0];
//                    }
//                }
//            };
//
//            // 创建一个不验证证书的 SSLContext，并使用上面的TrustManager初始化
//            SSLContext sslContext = SSLContext.getInstance("SSL");
//            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
//
//            // 使用上面创建的SSLContext创建一个SSLSocketFactory
//            javax.net.ssl.SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
//
//            OkHttpClient.Builder builder = new OkHttpClient.Builder();
//            builder.connectTimeout(600, TimeUnit.SECONDS); // 设置连接超时时间
//            builder.readTimeout(120, TimeUnit.SECONDS); // 设置读取超时时间
//            builder.writeTimeout(120, TimeUnit.SECONDS); // 设置写入超时时间
//            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
//            builder.hostnameVerifier((hostname, session) -> true);
//            return builder.build();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
	}

	private static OkHttpClient getUnsafeOkHttpClient(Boolean followRedirects) {

		try {
			// 创建一个信任所有证书的TrustManager
			final TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
				@Override
				public void checkClientTrusted(X509Certificate[] chain, String authType) {
				}

				@Override
				public void checkServerTrusted(X509Certificate[] chain, String authType) {
				}

				@Override
				public X509Certificate[] getAcceptedIssuers() {
					return new X509Certificate[0];
				}
			} };

			// 创建一个不验证证书的 SSLContext，并使用上面的TrustManager初始化
			SSLContext sslContext = SSLContext.getInstance("SSL");
			sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

			// 使用上面创建的SSLContext创建一个SSLSocketFactory
			javax.net.ssl.SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

			OkHttpClient.Builder builder = new OkHttpClient.Builder();
			builder.connectTimeout(600, TimeUnit.SECONDS); // 设置连接超时时间
			builder.readTimeout(120, TimeUnit.SECONDS); // 设置读取超时时间
			builder.writeTimeout(120, TimeUnit.SECONDS); // 设置写入超时时间
			builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
			builder.hostnameVerifier((hostname, session) -> true);
			if (GyUtils.isNotNull(followRedirects)) {
				builder.followRedirects(followRedirects);
			}
			final Proxy proxy = DlfwProxyUtils.getSystemHttpProxy();
			if (GyUtils.isNotNull(proxy)) {
				builder.proxy(proxy);
			}
			return builder.build();
		} catch (Exception e) {
//			throw new RuntimeException(e);
			log.error("Exception happens . {}", e.getMessage());
		}
		return null;
	}

	public static String postXdj(String url, String requestStr, Map<String, String> header) throws IOException {
//		MediaType mediaType = MediaType.parse("application/json");
//		RequestBody body = RequestBody.create(mediaType, requestStr);
		MediaType mediaType = MediaType.Companion.parse("application/json");
		RequestBody body = RequestBody.Companion.create(requestStr, mediaType);
		Request.Builder requestBuild = new Request.Builder();
		if (!GyUtils.isNull(header)) {
//            header.forEach(requestBuild::addHeader);
			Set<String> keyset = header.keySet();
			if (GyUtils.isNotNull(keyset)) {
				keyset.stream().forEach(key -> {
					String val = header.get(key);
					if (GyUtils.isNotNull(val)) {
						requestBuild.addHeader((String) key, val);
					}
				});
			}
		}
		Request request = requestBuild.url(url).method("POST", body).build();

		final OkHttpClient httpClient = getUnsafeOkHttpClient();
		log.info("url:{},header:{}", url, header);
		Response response = httpClient.newCall(request).execute();
//        header.put("Set-Cookie", response.headers().get("Set-Cookie"));
		List<String> list = response.headers("Set-Cookie");
		if (GyUtils.isNotNull(list)) {
			StringBuilder sb = new StringBuilder();
			for (String cookieStr : list) {
				sb.append(cookieStr.split(";")[0]).append("; ");
			}
			header.put("Set-Cookie", sb.toString());
		}
//        log.info("url:{},cookie:{}",url,response.headers().get("Set-Cookie"));
		Map<String, String> responseJsonObj = new HashMap<>();
		int httpStatusCode = response.code();
		responseJsonObj.put("HTTP_STATUS_CODE", String.valueOf(httpStatusCode));
		if (200 == httpStatusCode) {
			assert response.body() != null;
			String resBody = Objects.requireNonNull(response.body()).string();
			responseJsonObj.put("RESPONSE_BODY", resBody);
		}
		return JsonUtils.toJson(responseJsonObj);
	}

	public static String getXdjLocation(String url, String requestStr, Map<String, String> header) throws IOException {
		Request.Builder builder = new Request.Builder();
		builder.header("accept", "*/*");
		builder.header("connection", "Keep-Alive");
		if (GyUtils.isNotNull(header) && GyUtils.isNotNull(header.get("Set-Cookie"))) {
			builder.header("Cookie", header.get("Set-Cookie"));
		}
		Request request = builder.url(url).get().build();

		final OkHttpClient client = getUnsafeOkHttpClient(false);

//        OkHttpClient client = new OkHttpClient()
//                .newBuilder()
//                .followRedirects(false)
//                .connectTimeout(300, TimeUnit.SECONDS)//设置连接超时时间
//                .writeTimeout(10, TimeUnit.SECONDS)
//                .readTimeout(10, TimeUnit.SECONDS)//设置读取超时时间
//                .build();
//        client.writeTimeoutMillis();
//        String[] loc = new String[1];
		log.info("url:{},header:{}", url, header);
		try {
			Response response = client.newCall(request).execute();
//            String cookie = response.headers().get("Set-Cookie");
//            log.info("url:{},cookie:{}",url,cookie);
			List<String> list = response.headers("Set-Cookie");
//            if (GyUtils.isNotNull(cookie)) {
//            	header.put("Set-Cookie", cookie);
//            }
			if (GyUtils.isNotNull(list)) {
				StringBuilder sb = new StringBuilder();
				for (String cookieStr : list) {
					sb.append(cookieStr.split(";")[0]).append("; ");
				}
				header.put("Set-Cookie", sb.toString());
			}
			int code = response.code();
			if (code == 302) {
				String location = response.headers().get("Location");
				return location;
			}
		} catch (Exception e) {
			log.info("getXdjLocation error:{}", e.getMessage());
		}
		return null;
	}

	public static String getXdj(String url, String requestStr, Map<String, String> header) throws IOException {
		Request.Builder requestBuild = new Request.Builder();
		if (!GyUtils.isNull(header)) {
			Set<String> keyset = header.keySet();
			if (GyUtils.isNotNull(keyset)) {
				keyset.stream().forEach(key -> {
					String val = header.get(key);
					if (GyUtils.isNotNull(val)) {
						requestBuild.addHeader((String) key, val);
					}
				});
			}
		}
		Request request = requestBuild.url(url).get().build();
		final OkHttpClient httpClient = getUnsafeOkHttpClient();
		log.info("url:{},header:{}", url, header);
		Response response = httpClient.newCall(request).execute();
		header.put("Set-Cookie", response.headers().get("Set-Cookie"));
//	        log.info("url:{},cookie:{}",url,response.headers().get("Set-Cookie"));
		Map<String, String> responseJsonObj = new HashMap<>();
		int httpStatusCode = response.code();
		responseJsonObj.put("HTTP_STATUS_CODE", String.valueOf(httpStatusCode));
		if (200 == httpStatusCode) {
			assert response.body() != null;
			String resBody = Objects.requireNonNull(response.body()).string();
			responseJsonObj.put("RESPONSE_BODY", resBody);
		}
		return JsonUtils.toJson(responseJsonObj);
	}

	public static String postJcy(String requestUrl, String requestJson, Map<String, String> header) throws IOException {
//		MediaType mediaType = MediaType.parse("application/json");
//		RequestBody body = RequestBody.create(mediaType, requestJson);
		MediaType mediaType = MediaType.Companion.parse("application/json");
		RequestBody body = RequestBody.Companion.create(requestJson, mediaType);
		Request.Builder requestBuild = new Request.Builder();
		if (!GyUtils.isNull(header)) {
			Set<String> keyset = header.keySet();
			if (GyUtils.isNotNull(keyset)) {
				keyset.stream().forEach(key -> {
					String val = header.get(key);
					if (GyUtils.isNotNull(val)) {
						requestBuild.addHeader((String) key, val);
					}
				});
			}
		}

		Request request = requestBuild.url(requestUrl).method("POST", body)
				.addHeader("Content-Type", "application/json").addHeader("Accept", "application/json").build();
		final OkHttpClient httpClient = getUnsafeOkHttpClient();
		Response response = httpClient.newCall(request).execute();
		JSONObject responseJsonObj = new JSONObject();
//	        Map<String, Object> responseJsonObj = new HashMap<>();
		int httpStatusCode = response.code();
//		responseJsonObj.put("HTTP_STATUS_CODE", httpStatusCode + "");
		responseJsonObj.set("HTTP_STATUS_CODE", httpStatusCode + "");
//	        responseJsonObj.put("HTTP_STATUS_CODE",httpStatusCode);
		if (200 == httpStatusCode) {
			assert response.body() != null;
			String resBody = Objects.requireNonNull(response.body()).string();
//			responseJsonObj.put("RESPONSE_BODY", resBody);
			responseJsonObj.set("RESPONSE_BODY", resBody);
		}
		else {
			log.info("response:{}",response);
			log.info("response.body:{}",response.body());
		}
//	        return JsonUtils.toJson(responseJsonObj);
		return responseJsonObj.toString();
	}

	public static Map<String, String> getHeaders(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto) {
		final Map<String, String> headers = new HashMap<>();
		if (GyUtils.isNotNull(request.getCookie())) {
			headers.put("Cookie", request.getCookie());
		}
		headers.put("X-TEMP-INFO", lgdto.getPubKeyDTO().getUuid());
		headers.put("X-APP-CLIENTID", request.getClientId());
		if (GyUtils.isNotNull(lgdto.getSmsMessageLoginResDTO())) {
			headers.put("Authorization", lgdto.getSmsMessageLoginResDTO().getAccess_token());
		}
		headers.put("X-SM4-INFO", JmdlConstants.CODE_0);
		if (GyUtils.isNotNull(lgdto.getVerifyCaptchaResDTO())) {
			headers.put("X-TICKET-ID", lgdto.getVerifyCaptchaResDTO().getTicket());
		}
		return headers;
	}

	public static Map<String, String> getQueryMap(String url) {
		if (GyUtils.isNull(url)) {
			return null;
		}
		Map<String, String> map = new HashMap<>();
		int index = url.indexOf("?");
		if (-1 != index) {
			String searchInfo = url.substring(index + 1);
			if (GyUtils.isNotNull(searchInfo)) {
				String[] searchList = searchInfo.split("&");
				for (int i = 0; i < searchList.length; i++) {
					String str = searchList[i];
					int ind = str.indexOf("=");
					if (-1 != ind) {
						String name = str.substring(0, ind);
						String value = str.substring(ind + 1);
						map.put(name, value);
					}
				}
			}
		}
		return map;
	}

	public static void addHeaders(ZnsbLoginReqDTO request, ZnsbLoginDTO lgdto, Map<String, String> headers) {
		if (GyUtils.isNull(headers)) {
			return;
		}
		if (GyUtils.isNotNull(request.getCookie())) {
			headers.put("Cookie", request.getCookie());
		}
		headers.put("X-TEMP-INFO", lgdto.getPubKeyDTO().getUuid());
		headers.put("X-APP-CLIENTID", request.getClientId());
		if (GyUtils.isNotNull(lgdto.getSmsMessageLoginResDTO())) {
			headers.put("Authorization", lgdto.getSmsMessageLoginResDTO().getAccess_token());
		}
		headers.put("X-SM4-INFO", JmdlConstants.CODE_0);
		if (GyUtils.isNotNull(lgdto.getVerifyCaptchaResDTO())) {
			headers.put("X-TICKET-ID", lgdto.getVerifyCaptchaResDTO().getTicket());
		}
	}

	public static RemoteResDTO getXdjByCookieAndURL(String cookie, String url) {
//		long beginTime = System.currentTimeMillis();
		final String requestJson = null;
		Map<String, String> headers = new HashMap<>();
		String result = null; // 接收税局相应报文（未解密）
		try {
			headers.put("cookie", cookie);
			headers.put("Accept", "application/json, text/plain, */*");
			String requestUrl = url;
			result = getXdj(requestUrl, requestJson, headers);
		} catch (Exception e) {
			log.info("getXdjByCookieAndURL失败，{}", e.getMessage());
		}
		if (!GyUtils.isNull(result)) {
			Map<String, Object> resJsonObj = JsonUtils.toMap(result);
			String httpStatusCode = (String) resJsonObj.get("HTTP_STATUS_CODE");
			String responseBody = (String) resJsonObj.get("RESPONSE_BODY");
			RemoteResDTO res = new RemoteResDTO(httpStatusCode, responseBody);
			return res;
		}
		return null;
	}

	public static String postHxzg(String url, String requestJson, Map<String, String> header) {
//		HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
//		// HttpClient
//		CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
//		HttpPost httpPost = new HttpPost(url);
//		RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).build();
//		httpPost.setConfig(requestConfig);
//		try {
////            httpPost.setHeader("Content-Type", "text/xml;charset=UTF-8"); 
////            httpPost.setHeader("SOAPAction", soapAction); 
//			if (!GyUtils.isNull(header)) {
//				Set<String> keyset = header.keySet();
//				if (GyUtils.isNotNull(keyset)) {
//					keyset.stream().forEach(key -> {
//						String val = header.get(key);
//						if (GyUtils.isNotNull(val)) {
//							httpPost.setHeader((String) key, val);
//						}
//					});
//				}
//			}
//			StringEntity data = new StringEntity(requestJson, Charset.forName("UTF-8"));
//			httpPost.setEntity(data);
//			CloseableHttpResponse response = closeableHttpClient.execute(httpPost);
//			HttpEntity httpEntity = response.getEntity();
//			if (httpEntity != null) {
//				String retStr = EntityUtils.toString(httpEntity, "UTF-8");
//                log.info("response:{}" , retStr); 
//			}
//			closeableHttpClient.close();
//		} catch (Exception e) {
//			log.error("exception in postHxzg,{}", e.getMessage()); 
//		}
//
//		return null;
		MediaType mediaType = MediaType.Companion.parse("text/xml;charset=UTF-8");
		RequestBody body = RequestBody.Companion.create(requestJson,mediaType);
		Request.Builder requestBuild = new Request.Builder();
		if (!GyUtils.isNull(header)) {
			Set<String> keyset = header.keySet();
			if (GyUtils.isNotNull(keyset)) {
				keyset.stream().forEach(key -> {
					String val = header.get(key);
					if (GyUtils.isNotNull(val)) {
						requestBuild.addHeader((String) key, val);
					}
				});
			}
		}
		Request request = requestBuild.url(url).method("POST", body).build();
		final OkHttpClient httpClient = getUnsafeOkHttpClient();
		log.info("url:{},header:{}", url, header);
		Response response = null;
		try {
			response = httpClient.newCall(request).execute();
			if (GyUtils.isNull(response)) {
				log.error("response is null . ");
				return null;
			} 
			Map<String, String> responseJsonObj = new HashMap<>();
			int httpStatusCode = response.code(); 
			responseJsonObj.put("HTTP_STATUS_CODE", String.valueOf(httpStatusCode));
			if (200 == httpStatusCode) {
				assert response.body() != null;
				String resBody = Objects.requireNonNull(response.body()).string();
				responseJsonObj.put("RESPONSE_BODY", resBody);
			}
			return JsonUtils.toJson(responseJsonObj);
		} catch (IOException e) {
			log.error("postHxzg IOException happens . {} ",e.getMessage());
		}
		return null;
	}
}
