2025-07-24 17:39:26.663  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [RpcClientFactory] create a new rpc client of 57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0
2025-07-24 17:39:26.930  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] org.reflections.Reflections              : Reflections took 249 ms to scan 1 urls, producing 3 keys and 6 values
2025-07-24 17:39:27.160  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] org.reflections.Reflections              : Reflections took 224 ms to scan 1 urls, producing 4 keys and 9 values
2025-07-24 17:39:27.379  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] org.reflections.Reflections              : Reflections took 217 ms to scan 1 urls, producing 3 keys and 10 values
2025-07-24 17:39:33.339  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] org.reflections.Reflections              : Reflections took 5957 ms to scan 316 urls, producing 0 keys and 0 values
2025-07-24 17:39:33.594  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] org.reflections.Reflections              : Reflections took 252 ms to scan 1 urls, producing 1 keys and 5 values
2025-07-24 17:39:33.826  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] org.reflections.Reflections              : Reflections took 226 ms to scan 1 urls, producing 1 keys and 7 values
2025-07-24 17:39:34.083  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] org.reflections.Reflections              : Reflections took 255 ms to scan 1 urls, producing 2 keys and 8 values
2025-07-24 17:39:40.872  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] org.reflections.Reflections              : Reflections took 6786 ms to scan 316 urls, producing 0 keys and 0 values
2025-07-24 17:39:40.878  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
2025-07-24 17:39:40.880  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$433/858952163
2025-07-24 17:39:40.883  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$434/1201484275
2025-07-24 17:39:40.884  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-24 17:39:40.884  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-24 17:39:40.892  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
2025-07-24 17:39:41.415 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.start(RpcClient.java:390)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.ensureRpcClient(ClientWorker.java:885)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.getOneRunningClient(ClientWorker.java:1044)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.queryConfig(ClientWorker.java:940)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:397)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:166)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:94)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.css.znsb.NssbApplication.main(NssbApplication.java:9)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:41.418  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
2025-07-24 17:39:41.421 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.start(RpcClient.java:390)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.ensureRpcClient(ClientWorker.java:885)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.getOneRunningClient(ClientWorker.java:1044)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.queryConfig(ClientWorker.java:940)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:397)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:166)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:94)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.css.znsb.NssbApplication.main(NssbApplication.java:9)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:41.422  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
2025-07-24 17:39:41.426 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.start(RpcClient.java:390)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.ensureRpcClient(ClientWorker.java:885)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.getOneRunningClient(ClientWorker.java:1044)
	at com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.queryConfig(ClientWorker.java:940)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:397)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:166)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:94)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.css.znsb.NssbApplication.main(NssbApplication.java:9)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:41.430  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-24 17:39:41.430  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 17:39:41.431  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$442/574434418
2025-07-24 17:39:41.440 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:41.549 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:41.550  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:41.570 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=37b74f4a687089340abdf847b3e274cf, Client-RequestTS=1753349981432, exConfigInfo=true, notify=false, Timestamp=1753349981433}, requestId='null'}, retryTimes = 0, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:41.676 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=37b74f4a687089340abdf847b3e274cf, Client-RequestTS=1753349981432, exConfigInfo=true, notify=false, Timestamp=1753349981433}, requestId='null'}, retryTimes = 1, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:41.756 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:41.759  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:41.781 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=37b74f4a687089340abdf847b3e274cf, Client-RequestTS=1753349981432, exConfigInfo=true, notify=false, Timestamp=1753349981433}, requestId='null'}, retryTimes = 2, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:41.783  WARN 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[nssb-service] & group[DEFAULT_GROUP]
2025-07-24 17:39:41.886 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=bf80e91d88041895f9e54320ce413e9a, Client-RequestTS=1753349981785, exConfigInfo=true, notify=false, Timestamp=1753349981785}, requestId='null'}, retryTimes = 0, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:41.992 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=bf80e91d88041895f9e54320ce413e9a, Client-RequestTS=1753349981785, exConfigInfo=true, notify=false, Timestamp=1753349981785}, requestId='null'}, retryTimes = 1, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:42.066 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:42.068  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:42.093 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=bf80e91d88041895f9e54320ce413e9a, Client-RequestTS=1753349981785, exConfigInfo=true, notify=false, Timestamp=1753349981785}, requestId='null'}, retryTimes = 2, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:42.094  WARN 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[nssb-service.properties] & group[DEFAULT_GROUP]
2025-07-24 17:39:42.197 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=2655864c50ab86c32d8267e4b821bbf6, Client-RequestTS=1753349982094, exConfigInfo=true, notify=false, Timestamp=1753349982094}, requestId='null'}, retryTimes = 0, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:42.302 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=2655864c50ab86c32d8267e4b821bbf6, Client-RequestTS=1753349982094, exConfigInfo=true, notify=false, Timestamp=1753349982094}, requestId='null'}, retryTimes = 1, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:42.407 ERROR 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] com.alibaba.nacos.common.remote.client   : Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=2655864c50ab86c32d8267e4b821bbf6, Client-RequestTS=1753349982094, exConfigInfo=true, notify=false, Timestamp=1753349982094}, requestId='null'}, retryTimes = 2, errorMessage = Client not connected, current status:STARTING
2025-07-24 17:39:42.408  WARN 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[nssb-service-dev.properties] & group[DEFAULT_GROUP]
2025-07-24 17:39:42.409  INFO 27250 --- [main] [TID: N/A] [HHID:ef61ae9aefbf4484b1b175cacad5162c] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-nssb-service-dev.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-nssb-service.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-nssb-service,DEFAULT_GROUP'}]
2025-07-24 17:39:42.450  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.css.znsb.NssbApplication             : The following 1 profile is active: "dev"
2025-07-24 17:39:42.495 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:42.498  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:43.004 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:43.005  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:43.613 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:43.613  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:44.333 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:44.335  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:45.221 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:45.221  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:45.491  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-24 17:39:45.494  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-24 17:39:45.728  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 218 ms. Found 0 Redis repository interfaces.
2025-07-24 17:39:46.130 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:46.131  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:46.458  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'xfsSsjgMapper' and 'com.css.znsb.nssb.mapper.xfs.XfsSsjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.458  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'djXfsGdsbgzsszbysbaMapper' and 'com.css.znsb.nssb.mapper.xfs.DjXfsGdsbgzsszbysbaMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.458  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbJkyzzbMapper' and 'com.css.znsb.nssb.mapper.skjn.ZnsbNssbJkyzzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.458  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbYyjkjlMapper' and 'com.css.znsb.nssb.mapper.skjn.ZnsbNssbYyjkjlMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.458  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbJksmxMapper' and 'com.css.znsb.nssb.mapper.skjn.ZnsbNssbJksmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.458  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbJkyzmxMapper' and 'com.css.znsb.nssb.mapper.skjn.ZnsbNssbJkyzmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.459  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbJksjgjMapper' and 'com.css.znsb.nssb.mapper.skjn.ZnsbNssbJksjgjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.459  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbJksxxMapper' and 'com.css.znsb.nssb.mapper.skjn.ZnsbNssbJksxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.459  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSbYsbtjMapper' and 'com.css.znsb.nssb.mapper.ysbtj.ZnsbNssbSbYsbtjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.459  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbYyssyxxMxDOMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.yyssycj.ZnsbNssbYyssyxxMxDOMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.459  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbTdzzssyxxbMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.tdzzs.ZnsbNssbTdzzssyxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.461  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxsTdzzssyxxbMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.tdzzs.ZnsbNssbCxsTdzzssyxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxsTdzzsYzcjbMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.tdzzs.ZnsbNssbCxsTdzzsYzcjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxsGdzyssyxxMxMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.gdzys.ZnsbNssbCxsGdzyssyxxMxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxsGdzyssyxxMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.gdzys.ZnsbNssbCxsGdzyssyxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxsTdsyxxcjbDOMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDOMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxsTdsyysxxMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyysxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbZnsbCxsYsjmxzxxMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbZnsbCxsYsjmxzxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbFtdcjxxbMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbFtdcjxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbCxsCjjzsymxbMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsCjjzsymxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbFtdcjglbMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbFtdcjglbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbCxsFyxxcjbMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.463  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbCxsCzjzsymxbMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsCzjzsymxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.464  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbYhslfkblMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhslfkblMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.464  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbJyssSsSsjgYhsMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbJyssSsSsjgYhsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.464  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbYhscjMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhscjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.464  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxsbtxxMapper' and 'com.css.znsb.nssb.mapper.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbQhjtjcsssjBsxxmxMapper' and 'com.css.znsb.nssb.mapper.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjBsxxmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbQhjtjcsssjBsqcMapper' and 'com.css.znsb.nssb.mapper.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjBsqcMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZdsyqyjqMapper' and 'com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqyjqMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZdsyqyjqQnycMapper' and 'com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqyjqQnycMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZdsysjcjBsqcMapper' and 'com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsysjcjBsqcMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZdsyqyssxxbMapper' and 'com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqyssxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZdsyqyjbxxbMapper' and 'com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqyjbxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZdsyqycwxxMapper' and 'com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqycwxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.465  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZdsygyqyzycpyssxxbMapper' and 'com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsygyqyzycpyssxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.466  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZdsyqyxxpzbMapper' and 'com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqyxxpzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.466  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbCwbbZlbscjbMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbCwbbZlbscjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.466  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbCwbbQykjzzybqyZcfzbyzxMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbCwbbQykjzzybqyZcfzbyzxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.466  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxHszzdzMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbTzzxHszzdzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.466  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFzjgfztzMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbTzzxFzjgfztzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.466  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbCwbbQykjzzybqyLrbyzxMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbCwbbQykjzzybqyLrbyzxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.466  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbCwbbQykjzzybqyXjllbMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbCwbbQykjzzybqyXjllbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.467  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxBpcjysjMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbTzzxBpcjysjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.467  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbCwbbQykjzzybqySyzqyMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbCwbbQykjzzybqySyzqyMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.467  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxQysdsyjtzMapper' and 'com.css.znsb.nssb.mapper.yjzt.ZnsbTzzxQysdsyjtzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.467  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsxgmnsrSbbckMapper' and 'com.css.znsb.nssb.mapper.zzsxgmsb.ZzsxgmnsrSbbckMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.467  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsxgmnsrYsbjgbMapper' and 'com.css.znsb.nssb.mapper.zzsxgmsb.ZzsxgmnsrYsbjgbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.468  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsXgmFbFlzlMapper' and 'com.css.znsb.nssb.mapper.zzsxgmsb.SbZzsXgmFbFlzlMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.468  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsXgmMapper' and 'com.css.znsb.nssb.mapper.zzsxgmsb.SbZzsXgmMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.468  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'ftsJ3cxMapper' and 'com.css.znsb.nssb.mapper.fts.FtsJ3cxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.468  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZcxxMapper' and 'com.css.znsb.nssb.mapper.zcxx.ZnsbNssbZcxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.468  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbCxsFtdcjglbMapper' and 'com.css.znsb.nssb.mapper.fcscztdsyssycj.SbCxsFtdcjglbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.468  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSrhyhzbMapper' and 'com.css.znsb.nssb.mapper.srhy.ZnsbNssbSrhyhzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.469  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsybnsrZcsbCshMapper' and 'com.css.znsb.nssb.mapper.gy.ZzsybnsrZcsbCshMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.469  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxfbzlscqdMapper' and 'com.css.znsb.nssb.mapper.gy.ZnsbNssbCxfbzlscqdMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.469  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'rdHznsqyrdsqMxbMapper' and 'com.css.znsb.nssb.mapper.gy.RdHznsqyrdsqMxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.469  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSlxxMapper' and 'com.css.znsb.nssb.mapper.gy.SbSlxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.469  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'commonMapper' and 'com.css.znsb.nssb.mapper.gy.CommonMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.469  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSbflzlqdlbMapper' and 'com.css.znsb.nssb.mapper.gy.ZnsbNssbSbflzlqdlbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSblsbMapper' and 'com.css.znsb.nssb.mapper.gy.ZnsbNssbSblsbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbFlzlscMapper' and 'com.css.znsb.nssb.mapper.gy.ZnsbNssbFlzlscMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbMhzcSfzrdxxMapper' and 'com.css.znsb.nssb.mapper.gy.ZnsbMhzcSfzrdxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'hznsqyfjsfxxMapper' and 'com.css.znsb.nssb.mapper.gy.HznsqyfjsfxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbShgjsbqkjlbMapper' and 'com.css.znsb.nssb.mapper.gy.ZnsbNssbShgjsbqkjlbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSbkzbMapper' and 'com.css.znsb.nssb.mapper.gy.ZnsbNssbSbkzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbMhzcQyjbxxmxMapper' and 'com.css.znsb.nssb.mapper.gy.ZnsbMhzcQyjbxxmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSdsJmcz21yjdZczjmxbMapper' and 'com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZczjmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSdsFzjgfpbZjgMapper' and 'com.css.znsb.nssb.mapper.qysds.SbSdsFzjgfpbZjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.470  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSdsFzjgfpbFzjgMapper' and 'com.css.znsb.nssb.mapper.qysds.SbSdsFzjgfpbFzjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.471  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'qysdsJyssMapper' and 'com.css.znsb.nssb.mapper.qysds.QysdsJyssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.475  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbYsbjgbQysdsyjsbMapper' and 'com.css.znsb.nssb.mapper.qysds.ZnsbNssbYsbjgbQysdsyjsbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.478  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'csSbQysdsXxwlqycspzbMapper' and 'com.css.znsb.nssb.mapper.qysds.CsSbQysdsXxwlqycspzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.479  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSdsJmcz21yjdZbjmxxMapper' and 'com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZbjmxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.480  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSdsJmczYjdDynsbabMapper' and 'com.css.znsb.nssb.mapper.qysds.SbSdsJmczYjdDynsbabMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.481  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSdsJmcz18yjdMapper' and 'com.css.znsb.nssb.mapper.qysds.SbSdsJmcz18yjdMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.481  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSdsJmcz21yjdZbfbsxMapper' and 'com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZbfbsxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.481  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbQysdsbaxxMapper' and 'com.css.znsb.nssb.mapper.qysds.ZnsbNssbQysdsbaxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.481  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'mhtjMapper' and 'com.css.znsb.nssb.mapper.mhtj.MhtjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.481  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSjiwhbMapper' and 'com.css.znsb.nssb.mapper.sjiwh.ZnsbNssbSjiwhbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.481  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbBqzykcsejsbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbBqzykcsejsbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.482  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbZyfpmxMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbZyfpmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.482  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbDksejkcjsCpyMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbDksejkcjsCpyMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.482  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbBqjmsemxbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbBqjmsemxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.482  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbBxrlyclyqkCpyMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbBxrlyclyqkCpyMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.482  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsZbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsZbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.482  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.483  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbSnyrlyydtjbKchyqkMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbSnyrlyydtjbKchyqkMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.483  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbStfdkdjskqkMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbStfdkdjskqkMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.483  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbHznsqyxfsfpbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbHznsqyxfsfpbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.483  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbSnyrlyydtjbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbSnyrlyydtjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.483  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbHznsqyxfsfpbZjgMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbHznsqyxfsfpbZjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.483  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbHzscjyxfsqkbgbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbHzscjyxfsqkbgbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.483  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbFjsfQtxxXfsMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbFjsfQtxxXfsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.484  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbYxftsczztjMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbYxftsczztjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.484  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbJypfqyyfxsmxMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbJypfqyyfxsmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.484  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbGdsbgzsszbxsmxbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbGdsbgzsszbxsmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.485  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'djDjhglglMapper' and 'com.css.znsb.nssb.mapper.xfssb.DjDjhglglMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.485  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbYhdzdjsjgbjqdMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbYhdzdjsjgbjqdMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.485  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbFjsfHznsfjsffpbXfsMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbFjsfHznsfjsffpbXfsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.485  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbXswsqkmxZbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbXswsqkmxZbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.485  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbWgpzmxZbMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbWgpzmxZbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.485  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbWtjghsdysxfplyqkMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbWtjghsdysxfplyqkMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.485  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbXswsqkmxMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbXswsqkmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.486  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbFjsfXfsMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbFjsfXfsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.486  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbSnyrlyydtjbDdzgjhMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbSnyrlyydtjbDdzgjhMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.486  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbXfsFbWgpzmxMapper' and 'com.css.znsb.nssb.mapper.xfssb.SbXfsFbWgpzmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.486  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbHztycddMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHztycddMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.486  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFb2BqjxsemxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb2BqjxsemxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.486  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsYbnsrSbmxGjMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.ZzsYbnsrSbmxGjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.486  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbJyzyfjyxxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbJyzyfjyxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.486  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbHznsfpMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHznsfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.487  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbDxqyQdjxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDxqyQdjxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.487  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbHkysQdjxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHkysQdjxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.487  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbYzqyYjnMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbYzqyYjnMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.487  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbTlysYjnMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbTlysYjnMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.487  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbDlqyxxhjxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDlqyxxhjxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.487  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbNcpzjxsMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbNcpzjxsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.487  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFb1BqxsqkmxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb1BqxsqkmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbDlqyxxjxJxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDlqyxxjxJxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbFjsfQtxxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbFjsfQtxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbTlysQdjxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbTlysQdjxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFb3YsfwkcxmMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb3YsfwkcxmMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsybnsrSsjgMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.ZzsybnsrSsjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbFjsfHznsfjsffpbMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbFjsfHznsfjsffpbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbCbfhdncpMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbCbfhdncpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbDlqyxxjxXxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDlqyxxjxXxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsybnsrYsbjgbMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.ZzsybnsrYsbjgbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.488  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbTljsjjMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbTljsjjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsybnsrTzxxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.ZzsybnsrTzxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbHkysYjnMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHkysYjnMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbHztycddJxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHztycddJxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbNcpyyscjyMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbNcpyyscjyMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbHdncpjxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHdncpjxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFb4SedjqkMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb4SedjqkMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbHznsfpZbMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHznsfpZbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSsytxxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.ZnsbNssbSsytxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsFbZzsjmssbmxbMsxmMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsFbZzsjmssbmxbMsxmMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbYzqyQdjxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbYzqyQdjxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.489  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbDkdjjksdkMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDkdjjksdkMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbJyzyxshzMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbJyzyxshzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbDxqyYjnMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDxqyYjnMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbFjsfMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbFjsfMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsFbZzsjmssbmxbJsxmMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsFbZzsjmssbmxbJsxmMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsybnsrSbbckMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.ZzsybnsrSbbckMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbHztycddXxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHztycddXxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbCpygxcqkMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbCpygxcqkMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZzsNcpbaxxMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.ZnsbNssbZzsNcpbaxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.490  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbNcphdkchzMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbNcphdkchzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsYbnsrFbCpygxcslMapper' and 'com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbCpygxcslMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zbtjMapper' and 'com.css.znsb.nssb.mapper.zbtj.ZbtjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZbtjbMapper' and 'com.css.znsb.nssb.mapper.zbtj.ZnsbNssbZbtjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbPwxscjbMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbPwxscjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'cxsSsjgMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.CxsSsjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZsjcxxcjbMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbZsjcxxcjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbSbCxsHbsSbjscjZsMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjZsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'ssSsjgFcsMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.SsSsjgFcsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbDjHbsjcxxcjSyxxbMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbDjHbsjcxxcjSyxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'hbsQuerySbbzMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.HbsQuerySbbzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxstyjmxxMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxstyjmxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbYsbjgbCchxwsMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbYsbjgbCchxwsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxstysbxxMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxstysbxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'ssSsjgYhsMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.SsSsjgYhsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxsshgjsyxxjlbMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxsshgjsyxxjlbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'ssSsjgCztdsysMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.SsSsjgCztdsysMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbHbsdqswrjcxxcjbMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbHbsdqswrjcxxcjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbSbCxsHbsSbjscjDqsMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjDqsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCxstysbzbMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxstysbzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbGtfwwrfzssMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbGtfwwrfzssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbSbCxsHbsSbjscjGfMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjGfMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbDjHbsjcxxcjbMapper' and 'com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbDjHbsjcxxcjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbcwgzMapper' and 'com.css.znsb.nssb.mapper.sbcwgz.SbcwgzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.491  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbCwbbQykjzzybqyLrbyzxMapper' and 'com.css.znsb.nssb.mapper.cwbb.SbCwbbQykjzzybqyLrbyzxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.492  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZlbsQykjzzfzMapper' and 'com.css.znsb.nssb.mapper.cwbb.SbZlbsQykjzzfzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.492  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbCwbbZlbscjbMapper' and 'com.css.znsb.nssb.mapper.cwbb.SbCwbbZlbscjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.492  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbCwbbQykjzzybqySyzqyMapper' and 'com.css.znsb.nssb.mapper.cwbb.SbCwbbQykjzzybqySyzqyMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.492  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbCwbbQykjzzybqyXjllbMapper' and 'com.css.znsb.nssb.mapper.cwbb.SbCwbbQykjzzybqyXjllbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.492  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbCwbbQykjzzybqyZcfzbyzxMapper' and 'com.css.znsb.nssb.mapper.cwbb.SbCwbbQykjzzybqyZcfzbyzxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.492  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbQcxxMapper' and 'com.css.znsb.nssb.mapper.qcxx.ZnsbNssbQcxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.492  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbLzsSstjdcqcMapper' and 'com.css.znsb.nssb.mapper.sstjdc.ZnsbLzsSstjdcqcMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbDjNsrckzhzhxxMapper' and 'com.css.znsb.nssb.mapper.lqzlsbjk.ZnsbDjNsrckzhzhxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbDjJwskyhzhxxMapper' and 'com.css.znsb.nssb.mapper.lqzlsbjk.ZnsbDjJwskyhzhxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbSfxyxxMapper' and 'com.css.znsb.nssb.mapper.lqzlsbjk.ZnsbSfxyxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbJmbaxxMapper' and 'com.css.znsb.nssb.mapper.jmbaxx.ZnsbNssbJmbaxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sjtjMapper' and 'com.css.znsb.nssb.mapper.sjtj.SjtjMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSbglqhjlbMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbglqhjlbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbrwMxMapper' and 'com.css.znsb.nssb.mapper.sbrw.SbrwMxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbBdjgmxMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbBdjgmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCwkjzdbaxxMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbCwkjzdbaxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.493  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSbxxDOMapper' and 'com.css.znsb.nssb.mapper.sbrw.SbSbxxDOMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.494  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSbrwDOMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwDOMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.494  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSbbDOMapper' and 'com.css.znsb.nssb.mapper.sbrw.SbSbbDOMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.494  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbrwXxMapper' and 'com.css.znsb.nssb.mapper.sbrw.SbrwXxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.501  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbCwkjzdbaxxKjbbxxMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbCwkjzdbaxxKjbbxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.501  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbBdjgMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbBdjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.501  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSbrwCwbbMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwCwbbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.501  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSbrwMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.501  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbSbrwmxDOMapper' and 'com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwmxDOMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zzsyjSbbckMapper' and 'com.css.znsb.nssb.mapper.zzsyjsb.ZzsyjSbbckMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbNssbZzsyjnssbYjssbMapper' and 'com.css.znsb.nssb.mapper.zzsyjsb.ZnsbNssbZzsyjnssbYjssbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsyjnssbYwbMapper' and 'com.css.znsb.nssb.mapper.zzsyjsb.SbZzsyjnssbYwbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dmGySwjgMapper' and 'com.css.znsb.nssb.mapper.zzsyjsb.DmGySwjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dmGyJdxzMapper' and 'com.css.znsb.nssb.mapper.zzsyjsb.DmGyJdxzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbZzsyjnssbSjbMapper' and 'com.css.znsb.nssb.mapper.zzsyjsb.SbZzsyjnssbSjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dmGyXzqhMapper' and 'com.css.znsb.nssb.mapper.zzsyjsb.DmGyXzqhMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'xgmzybrtxMapper' and 'com.css.znsb.nssb.mapper.xgmzybrtx.XgmzybrtxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxBzpzMapper' and 'com.css.znsb.gjss.mapper.bzpz.ZnsbTzzxBzpzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxBzpzClztMapper' and 'com.css.znsb.gjss.mapper.bzpz.ZnsbTzzxBzpzClztMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'odsZnsbTzzxSrzzmxbMapper' and 'com.css.znsb.gjss.mapper.srzz.cxj.OdsZnsbTzzxSrzzmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gjssZnsbTzzxSrzzbZjbMapper' and 'com.css.znsb.gjss.mapper.srzz.jyk.GjssZnsbTzzxSrzzbZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gjssZnsbTzzxSrzzbMapper' and 'com.css.znsb.gjss.mapper.srzz.jyk.GjssZnsbTzzxSrzzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gjssZnsbTzzxSrzzmxbMapper' and 'com.css.znsb.gjss.mapper.srzz.jyk.GjssZnsbTzzxSrzzmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.502  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZzshzfpbZjbMapper' and 'com.css.znsb.gjss.mapper.srzz.jyk.ZnsbTzzxZzshzfpbZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxGsdmSapMapper' and 'com.css.znsb.gjss.mapper.sap.ZnsbTzzxGsdmSapMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dmTzzxKhzMapper' and 'com.css.znsb.gjss.mapper.sap.DmTzzxKhzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dmTzzxPzlxMapper' and 'com.css.znsb.gjss.mapper.sap.DmTzzxPzlxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dmTzzxKmbmMapper' and 'com.css.znsb.gjss.mapper.sap.DmTzzxKmbmMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dmTzzxKjfpMapper' and 'com.css.znsb.gjss.mapper.sap.DmTzzxKjfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dmTzzxKplxMapper' and 'com.css.znsb.gjss.mapper.sap.DmTzzxKplxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzdzHwxxDzfpMapper' and 'com.css.znsb.gjss.mapper.kp.sk.DzdzHwxxDzfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzdzHwxxZzsfpMapper' and 'com.css.znsb.gjss.mapper.kp.sk.DzdzHwxxZzsfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzdzFpxxDzfpMapper' and 'com.css.znsb.gjss.mapper.kp.sk.DzdzFpxxDzfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzdzHwxxPtfpMapper' and 'com.css.znsb.gjss.mapper.kp.sk.DzdzHwxxPtfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzdzFpxxDzzpMapper' and 'com.css.znsb.gjss.mapper.kp.sk.DzdzFpxxDzzpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzdzFpxxZzsfpMapper' and 'com.css.znsb.gjss.mapper.kp.sk.DzdzFpxxZzsfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzdzHwxxDzzpMapper' and 'com.css.znsb.gjss.mapper.kp.sk.DzdzHwxxDzzpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzdzFpxxPtfpMapper' and 'com.css.znsb.gjss.mapper.kp.sk.DzdzFpxxPtfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzfpKpywFpmxxxbMapper' and 'com.css.znsb.gjss.mapper.kp.qd.DzfpKpywFpmxxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'dzfpKpywFpjcxxbMapper' and 'com.css.znsb.gjss.mapper.kp.qd.DzfpKpywFpjcxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxMdmdataMapper' and 'com.css.znsb.gjss.mapper.semir.ZnsbTzzxMdmdataMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'kjKmyebMapper' and 'com.css.znsb.gjss.mapper.semir.KjKmyebMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.503  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gjssZnsbTzzxLqsshzBqynsessMapper' and 'com.css.znsb.gjss.mapper.lqsshz.GjssZnsbTzzxLqsshzBqynsessMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gjssZnsbTzzxLqsshzBsqkxsxeMapper' and 'com.css.znsb.gjss.mapper.lqsshz.GjssZnsbTzzxLqsshzBsqkxsxeMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gjssZnsbTzzxLqsshzTsssMapper' and 'com.css.znsb.gjss.mapper.lqsshz.GjssZnsbTzzxLqsshzTsssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gjssZnsbTzzxLqsshzDjxmssMapper' and 'com.css.znsb.gjss.mapper.lqsshz.GjssZnsbTzzxLqsshzDjxmssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFcshtxxBpmMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxFcshtxxBpmMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxHtmxDlMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxHtmxDlMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTbFlbCqrzMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.ZnsbTbFlbCqrzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxHtzzMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxHtzzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFcshtxxBpmZlxxMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxFcshtxxBpmZlxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxHtzzBpmzjbMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxHtzzBpmzjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxYhslfkblMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxYhslfkblMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFcshtxxBpmSpzbMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxFcshtxxBpmSpzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gsdmDlMapper' and 'com.css.znsb.gjss.mapper.yhs.httz.GsdmDlMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxBpcjysjRzbMapper' and 'com.css.znsb.gjss.mapper.yhs.yyzb.ZnsbTzzxBpcjysjRzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.504  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxPzmxFsbMapper' and 'com.css.znsb.gjss.mapper.yhs.pzmx.ZnsbTzzxPzmxFsbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxPzmxZjbMapper' and 'com.css.znsb.gjss.mapper.yhs.pzmx.ZnsbTzzxPzmxZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxPzmxMapper' and 'com.css.znsb.gjss.mapper.yhs.pzmx.ZnsbTzzxPzmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxKhbmhylbdzbMapper' and 'com.css.znsb.gjss.mapper.yhs.khbmhylbdz.ZnsbTzzxKhbmhylbdzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxYhsCqzysjtzMapper' and 'com.css.znsb.gjss.mapper.yhs.cqzysjtz.ZnsbTzzxYhsCqzysjtzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'cxkZnsbTzzxXxfphwfwmxbMapper' and 'com.css.znsb.gjss.mapper.zzs.xxfp.cxk.CxkZnsbTzzxXxfphwfwmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'cxkZnsbTzzxXxfpmxbMapper' and 'com.css.znsb.gjss.mapper.zzs.xxfp.cxk.CxkZnsbTzzxXxfpmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'cxkZnsbTzxxXxfpzzbMapper' and 'com.css.znsb.gjss.mapper.zzs.xxfp.cxk.CxkZnsbTzxxXxfpzzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'jykZnsbTzzxXxfphwfwmxbMapper' and 'com.css.znsb.gjss.mapper.zzs.xxfp.jyk.JykZnsbTzzxXxfphwfwmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxBdycSrbMapper' and 'com.css.znsb.gjss.mapper.zzs.xxfp.jyk.ZnsbTzzxBdycSrbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'jykZnsbTzzxXxfpmxbMapper' and 'com.css.znsb.gjss.mapper.zzs.xxfp.jyk.JykZnsbTzzxXxfpmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.505  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'jykZnsbTzxxXxfpzzbMapper' and 'com.css.znsb.gjss.mapper.zzs.xxfp.jyk.JykZnsbTzxxXxfpzzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'jykZnsbTzxxXxfpzzbZjbMapper' and 'com.css.znsb.gjss.mapper.zzs.xxfp.jyk.JykZnsbTzxxXxfpzzbZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'cxkZnsbTzzxJxsezcmxzMapper' and 'com.css.znsb.gjss.mapper.zzs.jxsezc.cxk.CxkZnsbTzzxJxsezcmxzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxsezcmxzGjssMapper' and 'com.css.znsb.gjss.mapper.zzs.jxsezc.jyk.ZnsbTzzxJxsezcmxzGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxsezczzGjssMapper' and 'com.css.znsb.gjss.mapper.zzs.jxsezc.jyk.ZnsbTzzxJxsezczzGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxsezczzZjbMapper' and 'com.css.znsb.gjss.mapper.zzs.jxsezc.jyk.ZnsbTzzxJxsezczzZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxQtkspzzzGjssMapper' and 'com.css.znsb.gjss.mapper.zzs.qtkjpz.jyk.ZnsbTzzxQtkspzzzGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxQtkspzzzZjbMapper' and 'com.css.znsb.gjss.mapper.zzs.qtkjpz.jyk.ZnsbTzzxQtkspzzzZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'cxkZnsbTzzxJxfpmxbMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.cxk.CxkZnsbTzzxJxfpmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'odsZnsbTzzxHgwsmxMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.cxk.OdsZnsbTzzxHgwsmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'odsZnsbTzzxLkysfwkspzmxzMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.cxk.OdsZnsbTzzxLkysfwkspzmxzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'odsZnsbTzzxDkdjpzmxMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.cxk.OdsZnsbTzzxDkdjpzmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.506  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'cxkZnsbTzzxJxfphwfwmxbMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.cxk.CxkZnsbTzzxJxfphwfwmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLkysfwkspzmxzGjssMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.jyk.ZnsbTzzxLkysfwkspzmxzGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'gjssZnsbTzzxJxfphwfwmxbGjssMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.jyk.GjssZnsbTzzxJxfphwfwmxbGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFpxxDrzMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.jyk.ZnsbTzzxFpxxDrzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxfpmxbZjbMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.jyk.ZnsbTzzxJxfpmxbZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxfpzzZjbMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.jyk.ZnsbTzzxJxfpzzZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxfpmxbGjssMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.jyk.ZnsbTzzxJxfpmxbGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxfpzzGjssMapper' and 'com.css.znsb.gjss.mapper.zzs.jxfp.jyk.ZnsbTzzxJxfpzzGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCkhwznxzmmxbZjbMapper' and 'com.css.znsb.gjss.mapper.zzs.ckhwznxzmmx.jyk.ZnsbTzzxCkhwznxzmmxbZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCkhwznxzmmxbGjssMapper' and 'com.css.znsb.gjss.mapper.zzs.ckhwznxzmmx.jyk.ZnsbTzzxCkhwznxzmmxbGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'odsZnsbTzzxJmsmxbMapper' and 'com.css.znsb.gjss.mapper.jms.cxj.OdsZnsbTzzxJmsmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJmszzGjssMapper' and 'com.css.znsb.gjss.mapper.jms.jyk.ZnsbTzzxJmszzGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJmszzZjbMapper' and 'com.css.znsb.gjss.mapper.jms.jyk.ZnsbTzzxJmszzZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.507  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJmsmxbGjssMapper' and 'com.css.znsb.gjss.mapper.jms.jyk.ZnsbTzzxJmsmxbGjssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFcjcxxtzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxFcjcxxtzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxDkjxzzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxDkjxzzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxXxfpzzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxXxfpzzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxYyzbtz1Mapper' and 'com.css.znsb.tzzx.mapper.yhstz.ZnsbTzzxYyzbtz1Mapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxYhszzCjxxMapper' and 'com.css.znsb.tzzx.mapper.yhstz.ZnsbTzzxYhszzCjxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxYhszzxxMapper' and 'com.css.znsb.tzzx.mapper.yhstz.ZnsbTzzxYhszzxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'zcalPa005YhshdMapper' and 'com.css.znsb.tzzx.mapper.yhstz.ZcalPa005YhshdMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCzfcsymxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxCzfcsymxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxSrzzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxQtkspzzzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxQtkspzzzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxYyzbtzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxYyzbtzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCkhwznxzmmxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxCkhwznxzmmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxXxfpmxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxXxfpmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJjdjtzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxJjdjtzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLrzxdzbMapper' and 'com.css.znsb.tzzx.mapper.zzstz.xxfpzz.ZnsbTzzxLrzxdzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZzsxxfpzzjlbMapper' and 'com.css.znsb.tzzx.mapper.zzstz.xxfpzz.ZnsbTzzxZzsxxfpzzjlbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxfphwfwmxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxJxfphwfwmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxHgwsmxMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxHgwsmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxXxfpmxbZjbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxXxfpmxbZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxXxfpzzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxXxfpzzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLrzxqyshdzbMapper' and 'com.css.znsb.tzzx.mapper.lrzxqyshdz.ZnsbTzzxLrzxqyshdzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxDkdjpzmxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxDkdjpzmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.508  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxSrcybdhzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxSrcybdhzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZyfcsymxzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxZyfcsymxzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'pzFlMxMapper' and 'com.css.znsb.tzzx.mapper.PzFlMxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxTzpzxxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxTzpzxxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZzsyjKqyjytzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxZzsyjKqyjytzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFssrtysbMapper' and 'com.css.znsb.tzzx.mapper.fssrtysb.ZnsbTzzxFssrtysbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLkysfwkspzmxzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxLkysfwkspzmxzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxTdjcxxtzMapper' and 'com.css.znsb.tzzx.mapper.cztdsytz.ZnsbTzzxTdjcxxtzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJmszzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxJmszzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCybdhlbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxCybdhlbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFcssyfcdzbMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxFcssyfcdzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFcszcxxMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxFcszcxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCzpzmxbZjbMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxCzpzmxbZjbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFctzmxbMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxFctzmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxHttzxxMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxHttzxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCzpzmxbMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxCzpzmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.509  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFcssyfckmfpdzbMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxFcssyfckmfpdzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.510  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'htxxGlcxMapper' and 'com.css.znsb.tzzx.mapper.fcstz.HtxxGlcxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.510  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxHttzxxMxMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxHttzxxMxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.510  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxFcszcxxdzbMapper' and 'com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxFcszcxxdzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.510  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJmsmxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxJmsmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.510  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxYhszzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxYhszzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.510  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCzbdcyjmxMapper' and 'com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxCzbdcyjmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.510  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZzsyjmxMapper' and 'com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxZzsyjmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZzsyjjhMapper' and 'com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxZzsyjjhMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxsezczzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxJxsezczzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxSrcybdmxMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxSrcybdmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCybdmxMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxCybdmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLqsshzTsssMapper' and 'com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzTsssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLqsshzBsqkxsxeMapper' and 'com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzBsqkxsxeMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLqsshzDjxmssMapper' and 'com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzDjxmssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLqsshzBqynsessMapper' and 'com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzBqynsessMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLqsshzMapper' and 'com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxLqsshzJxsezcssMapper' and 'com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzJxsezcssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxfpmxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxJxfpmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZzshzfpbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxZzshzfpbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.511  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZzsyjFzjgtzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxZzsyjFzjgtzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxDatalljkbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxDatalljkbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxHttzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxHttzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxCqzysjtzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxCqzysjtzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxSrzzmxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxXxfphwfwmxbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxXxfphwfwmxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxsezcmxzMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxJxsezcmxzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxZzsldtsMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxZzsldtsMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'resetJyssMapper' and 'com.css.znsb.tzzx.mapper.resetjyss.ResetJyssMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbTzzxJxfpzzbMapper' and 'com.css.znsb.tzzx.mapper.ZnsbTzzxJxfpzzbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSbxxMapper' and 'com.css.znsb.hgjc.biz.mapper.query.SbSbxxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.512  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sbSbbMapper' and 'com.css.znsb.hgjc.biz.mapper.query.SbSbbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.513  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'djhglglMapper' and 'com.css.znsb.hgjc.biz.mapper.query.DjhglglMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.513  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'hznsqyrdsqMxbMapper' and 'com.css.znsb.hgjc.biz.mapper.query.HznsqyrdsqMxbMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.514  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbHgjcQyYwmxMapper' and 'com.css.znsb.hgjc.mapper.ZnsbHgjcQyYwmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.514  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbHgjcQyJcjgMapper' and 'com.css.znsb.hgjc.mapper.ZnsbHgjcQyJcjgMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.514  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbHgjcJkxmMapper' and 'com.css.znsb.hgjc.mapper.ZnsbHgjcJkxmMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.515  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbHgjcQyYwMapper' and 'com.css.znsb.hgjc.mapper.ZnsbHgjcQyYwMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.515  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbHgjcQyPzMapper' and 'com.css.znsb.hgjc.mapper.ZnsbHgjcQyPzMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.515  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbHgjcYwMapper' and 'com.css.znsb.hgjc.mapper.ZnsbHgjcYwMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.515  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbHgjcYwmxMapper' and 'com.css.znsb.hgjc.mapper.ZnsbHgjcYwmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.515  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'znsbHgjcQyJcjgmxMapper' and 'com.css.znsb.hgjc.mapper.ZnsbHgjcQyJcjgmxMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.515  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sjjhMapper' and 'com.css.znsb.framework.sjjh.mapper.SjjhMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.517  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[com.css.znsb]' package. Please check your configuration.
2025-07-24 17:39:46.531  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.m.s.mapper.ClassPathMapperScanner      : Skipping MapperFactoryBean with name 'sjjhMapper' and 'com.css.znsb.framework.sjjh.mapper.SjjhMapper' mapperInterface. Bean already defined with the same name!
2025-07-24 17:39:46.834  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.s.cloud.context.scope.GenericScope     : BeanFactory id=c9181238-ecf7-349b-8c47-45cf09a963e7
2025-07-24 17:39:47.071  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] ptablePropertiesBeanFactoryPostProcessor : Post-processing PropertySource instances
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource bootstrapProperties-nssb-service-dev.properties,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource bootstrapProperties-nssb-service.properties,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource bootstrapProperties-nssb-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource jasypt-bootstrap-config [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource commandLineArgs [org.springframework.core.env.SimpleCommandLinePropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Config resource 'class path resource [application.yaml]' via location 'optional:classpath:/' (document #1) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Config resource 'class path resource [application.yaml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 17:39:47.074  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 17:39:47.092  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.filter.DefaultLazyPropertyFilter   : Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-24 17:39:47.100  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.r.DefaultLazyPropertyResolver      : Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-24 17:39:47.102  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.u.j.d.DefaultLazyPropertyDetector      : Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-24 17:39:47.138 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:47.138  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:48.003  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 17:39:48.006  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 17:39:48.008  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$666/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 17:39:48.015  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 17:39:48.078  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] trationDelegate$BeanPostProcessorChecker : Bean 'znsbAsyncAutoConfiguration' of type [com.css.znsb.framework.job.config.ZnsbAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 17:39:48.253 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:48.254  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:48.701  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 48100 (http)
2025-07-24 17:39:48.714  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-24 17:39:48.714  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-24 17:39:48.833  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.a.c.c.C.[.[localhost].[/devwang]       : Initializing Spring embedded WebApplicationContext
2025-07-24 17:39:48.833  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6360 ms
2025-07-24 17:39:49.472 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:49.474  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:49.513  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [0] datasource,Please add your primary datasource or check your configuration
2025-07-24 17:39:49.640  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-24 17:39:49.640  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-24 17:39:49.641  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-24 17:39:49.862  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.s.boot.web.servlet.RegistrationBean    : Filter corsFilter was not registered (possibly already registered?)
2025-07-24 17:39:50.453  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.c.sentinel.SentinelWebMvcConfigurer  : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-24 17:39:50.779 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:50.780  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:51.356  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.s.c.openfeign.FeignClientFactoryBean   : For 'dlfw-service' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 17:39:52.197 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:52.202  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:52.388  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] org.redisson.Version                     : Redisson 3.18.0
2025-07-24 17:39:52.906  WARN 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisUtil': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [com/css/znsb/framework/redis/config/ZnsbRedisAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: localhost/127.0.0.1:6379
2025-07-24 17:39:52.910  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-07-24 17:39:52.910  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-07-24 17:39:52.911  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-07-24 17:39:53.275  INFO 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 17:39:53.299 ERROR 27250 --- [main] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisUtil': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [com/css/znsb/framework/redis/config/ZnsbRedisAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: localhost/127.0.0.1:6379
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.css.znsb.NssbApplication.main(NssbApplication.java:9)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [com/css/znsb/framework/redis/config/ZnsbRedisAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: localhost/127.0.0.1:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:801)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:479)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:550)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: localhost/127.0.0.1:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:801)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	... 41 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: localhost/127.0.0.1:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:481)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	... 55 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: localhost/127.0.0.1:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648)
	... 69 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: localhost/127.0.0.1:6379
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$0(ConnectionPool.java:154)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:750)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:318)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$6(ConnectionPool.java:277)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:750)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.client.RedisClient$1$2.run(RedisClient.java:250)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.util.concurrent.CompletionException: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: localhost/127.0.0.1:6379
	at java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:326)
	at java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:338)
	at java.util.concurrent.CompletableFuture.uniRelay(CompletableFuture.java:925)
	at java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:913)
	... 11 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:53.715 ERROR 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.c.remote.client.grpc.GrpcClient    : Server check fail, please check server 127.0.0.1 ,port 9848 is available , error ={}

java.util.concurrent.ExecutionException: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:566)
	at com.alibaba.nacos.shaded.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:445)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.serverCheck(GrpcClient.java:148)
	at com.alibaba.nacos.common.remote.client.grpc.GrpcClient.connectToServer(GrpcClient.java:264)
	at com.alibaba.nacos.common.remote.client.RpcClient.reconnect(RpcClient.java:522)
	at com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(RpcClient.java:370)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.alibaba.nacos.shaded.io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at com.alibaba.nacos.shaded.io.grpc.Status.asRuntimeException(Status.java:533)
	at com.alibaba.nacos.shaded.io.grpc.stub.ClientCalls$UnaryStreamToFuture.onClose(ClientCalls.java:490)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusStatsModule$StatsClientInterceptor$1$1.onClose(CensusStatsModule.java:700)
	at com.alibaba.nacos.shaded.io.grpc.PartialForwardingClientCallListener.onClose(PartialForwardingClientCallListener.java:39)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener.onClose(ForwardingClientCallListener.java:23)
	at com.alibaba.nacos.shaded.io.grpc.ForwardingClientCallListener$SimpleForwardingClientCallListener.onClose(ForwardingClientCallListener.java:40)
	at com.alibaba.nacos.shaded.io.grpc.internal.CensusTracingModule$TracingClientInterceptor$1$1.onClose(CensusTracingModule.java:399)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:510)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:66)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.close(ClientCallImpl.java:630)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl.access$700(ClientCallImpl.java:518)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:692)
	at com.alibaba.nacos.shaded.io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:681)
	at com.alibaba.nacos.shaded.io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at com.alibaba.nacos.shaded.io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:123)
	... 3 common frames omitted
Caused by: com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /127.0.0.1:9848
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:336)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:685)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:632)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:549)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:511)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:918)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

2025-07-24 17:39:53.717  INFO 27250 --- [com.alibaba.nacos.client.remote.worker] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] com.alibaba.nacos.common.remote.client   : [57bdb1e6-c24b-496b-95b5-370b1aadf2b4_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-24 17:39:54.261  WARN 27250 --- [Thread-3] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-24 17:39:54.261  WARN 27250 --- [Thread-8] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-07-24 17:39:54.262  WARN 27250 --- [Thread-8] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-07-24 17:39:54.262  WARN 27250 --- [Thread-3] [TID: N/A] [HHID:d6b9f912e6914744964c4b6ad27131f7] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
