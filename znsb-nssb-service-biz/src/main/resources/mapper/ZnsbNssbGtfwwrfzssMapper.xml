<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbGtfwwrfzssMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.hbs.ZnsbNssbGtfwwrfzssDO">
            <id property="fzssuuid" column="fzssuuid" jdbcType="VARCHAR"/>
            <result property="hbsjcxxuuid" column="hbsjcxxuuid" jdbcType="VARCHAR"/>
            <result property="gtfwwrfzsslbdm" column="gtfwwrfzsslbdm" jdbcType="CHAR"/>
            <result property="cshssmc" column="cshssmc" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="gtfwlyDm" column="gtfwly_dm" jdbcType="CHAR"/>
            <result property="zhlycw" column="zhlycw" jdbcType="VARCHAR"/>
            <result property="csssjbqk" column="csssjbqk" jdbcType="VARCHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="zhlyhczhccrlnl" column="zhlyhczhccrlnl" jdbcType="DECIMAL"/>
            <result property="zhlyzyfssDm" column="zhlyzyfss_dm" jdbcType="VARCHAR"/>
            <result property="ssbm" column="ssbm" jdbcType="VARCHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="czqk" column="czqk" jdbcType="VARCHAR"/>
            <result property="ysbbz" column="ysbbz" jdbcType="CHAR"/>
            <result property="hgbhssybh" column="hgbhssybh" jdbcType="VARCHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        fzssuuid,hbsjcxxuuid,gtfwwrfzsslbdm,
        cshssmc,zspm_dm,zszm_dm,
        gtfwly_dm,zhlycw,csssjbqk,
        lrr_dm,xgr_dm,zhlyhczhccrlnl,
        zhlyzyfss_dm,ssbm,yxbz,
        sjblbz,czqk,ysbbz,
        hgbhssybh,syuuid,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
