<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxDkjxzzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxDkjxzzDO">
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="qysh" column="qysh" jdbcType="VARCHAR"/>
            <result property="qymc" column="qymc" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="xmDm" column="xm_dm" jdbcType="VARCHAR"/>
            <result property="fpfs" column="fpfs" jdbcType="DECIMAL"/>
            <result property="je" column="je" jdbcType="DECIMAL"/>
            <result property="se" column="se" jdbcType="DECIMAL"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,qysh,qymc,
        sszq,xm_dm,fpfs,
        je,se,uuid,
        ywqd_dm,lrrq,xgrq,
        scbz,sjcsdq,sjgsdq,
        lrrsfid,xgrsfid,sjtb_sj,
        scsj_1
    </sql>
    <update id="updateDkjxZzByJxfpmx" parameterType="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxDkjxzzDO">
        update znsb_tzzx_dkjxzz set
        <if test="entity.xgrsfid != null and entity.xgrsfid != ''">
            xgrsfid = #{entity.xgrsfid},
        </if>
        <if test="entity.je != null">
            je = #{entity.je},
        </if>
        <if test="entity.je == null">
            je = null,
        </if>
        <if test="entity.se != null">
            se = #{entity.se},
        </if>
        <if test="entity.se == null">
            se = null,
        </if>
        <if test="entity.fpfs != null">
            fpfs = #{entity.fpfs},
        </if>
        <if test="entity.fpfs == null">
            fpfs = null,
        </if>
        xgrq = #{entity.xgrq}
        where uuid = #{entity.uuid};
    </update>
</mapper>
