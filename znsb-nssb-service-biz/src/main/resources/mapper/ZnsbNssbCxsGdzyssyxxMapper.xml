<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.gdzys.ZnsbNssbCxsGdzyssyxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.gdzys.ZnsbNssbCxsGdzyssyxxDO">
        <id property="gdzysuuid" column="gdzysuuid" jdbcType="VARCHAR"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="nsrlx" column="nsrlx" jdbcType="CHAR"/>
        <result property="djzclxDm" column="djzclx_dm" jdbcType="CHAR"/>
        <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
        <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
        <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
        <result property="sfzjlxDm" column="sfzjlx_dm" jdbcType="CHAR"/>
        <result property="sfzjhm" column="sfzjhm" jdbcType="VARCHAR"/>
        <result property="lxr" column="lxr" jdbcType="VARCHAR"/>
        <result property="lxfs" column="lxfs" jdbcType="VARCHAR"/>
        <result property="zdfs1Dm" column="zdfs1_dm" jdbcType="VARCHAR"/>
        <result property="xmmc" column="xmmc" jdbcType="VARCHAR"/>
        <result property="pzzdwh" column="pzzdwh" jdbcType="VARCHAR"/>
        <result property="pzzdbm" column="pzzdbm" jdbcType="VARCHAR"/>
        <result property="pzzdmj" column="pzzdmj" jdbcType="DECIMAL"/>
        <result property="smtzrq" column="smtzrq" jdbcType="TIMESTAMP"/>
        <result property="pzsj" column="pzsj" jdbcType="TIMESTAMP"/>
        <result property="sjzdrq" column="sjzdrq" jdbcType="TIMESTAMP"/>
        <result property="shgdfs" column="shgdfs" jdbcType="VARCHAR"/>
        <result property="shgdrdrq" column="shgdrdrq" jdbcType="TIMESTAMP"/>
        <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
        <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
        <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
        <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
        <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
        <result property="setgbl1" column="setgbl1" jdbcType="DECIMAL"/>
        <result property="bqsfsyxgmyhzc" column="bqsfsyxgmyhzc" jdbcType="CHAR"/>
        <result property="phjzbl" column="phjzbl" jdbcType="DECIMAL"/>
        <result property="ysbbz" column="ysbbz" jdbcType="CHAR"/>
        <result property="rdshgdmj" column="rdshgdmj" jdbcType="DECIMAL"/>
        <result property="rdsjzdmj" column="rdsjzdmj" jdbcType="DECIMAL"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="bczt" column="bczt" jdbcType="CHAR"/>
        <result property="hxbuuid" column="hxbuuid" jdbcType="VARCHAR"/>
        <result property="cwxx" column="cwxx" jdbcType="VARCHAR"/>
        <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        gdzysuuid
        ,nsrsbh,nsrmc,
        djxh,nsrlx,djzclx_dm,
        hy_dm,sbsx_dm_1,zgswskfj_dm,
        sfzjlx_dm,sfzjhm,lxr,
        lxfs,zdfs1_dm,xmmc,
        pzzdwh,pzzdbm,pzzdmj,
        smtzrq,pzsj,sjzdrq,
        shgdfs,shgdrdrq,skssqq,
        skssqz,zfr_dm,zfrq_1,
        zfbz_1,setgbl1,bqsfsyxgmyhzc,
        phjzbl,ysbbz,rdshgdmj,
        rdsjzdmj,lrrq,xgrq,
        lrrsfid,xgrsfid,ywqd_dm,
        sjcsdq,sjtb_sj,sjgsdq,bczt,
        hxbuuid,cwxx,pclsh
    </sql>
</mapper>
