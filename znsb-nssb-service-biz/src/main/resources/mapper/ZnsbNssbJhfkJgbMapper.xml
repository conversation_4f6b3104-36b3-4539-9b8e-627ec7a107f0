<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sxydfkjh.ZnsbNssbJhfkJgbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sxydfkjh.ZnsbNssbJhfkJgbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="xh" column="xh" jdbcType="DECIMAL"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nd" column="nd" jdbcType="VARCHAR"/>
            <result property="zsxmmc" column="zsxmmc" jdbcType="VARCHAR"/>
            <result property="ygje" column="ygje" jdbcType="DECIMAL"/>
            <result property="ssfxmc" column="ssfxmc" jdbcType="VARCHAR"/>
            <result property="ssfxms1" column="ssfxms_1" jdbcType="VARCHAR"/>
            <result property="zcyj" column="zcyj" jdbcType="VARCHAR"/>
            <result property="zbjssm" column="zbjssm" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,xh,
        nsrmc,nsrsbh,nd,
        zsxmmc,ygje,ssfxmc,
        ssfxms_1,zcyj,zbjssm,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
