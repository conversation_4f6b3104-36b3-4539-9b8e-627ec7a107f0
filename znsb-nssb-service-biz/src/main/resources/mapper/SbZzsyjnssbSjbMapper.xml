<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsyjsb.SbZzsyjnssbSjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zzsyjsb.SbZzsyjnssbSjbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="yzxmDm" column="yzxm_dm" jdbcType="CHAR"/>
            <result property="xse" column="xse" jdbcType="DECIMAL"/>
            <result property="kcje" column="kcje" jdbcType="DECIMAL"/>
            <result property="yzl" column="yzl" jdbcType="DECIMAL"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,yzxm_dm,
        xse,kcje,yzl,
        sl_1,pzxh,sjblbz,
        ywqd_dm,lrrq,lrrsfid,
        xgrq,xgrsfid,sjcsdq,
        sjgsdq,sjtb_sj
    </sql>
</mapper>
