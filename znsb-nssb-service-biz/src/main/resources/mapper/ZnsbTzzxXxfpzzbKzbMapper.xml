<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.gjss.mapper.srzz.jyk.ZnsbTzzxXxfpzzbKzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxXxfpzzbKzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="wkpfjzjtje" column="wkpfjzjtje" jdbcType="DECIMAL"/>
            <result property="wkpfjzjtse" column="wkpfjzjtse" jdbcType="DECIMAL"/>
            <result property="wkpjzjtje" column="wkpjzjtje" jdbcType="DECIMAL"/>
            <result property="wkpjzjtse" column="wkpjzjtse" jdbcType="DECIMAL"/>
            <result property="nsjctzje" column="nsjctzje" jdbcType="DECIMAL"/>
            <result property="nsjctzse" column="nsjctzse" jdbcType="DECIMAL"/>
            <result property="jzjtbz" column="jzjtbz" jdbcType="CHAR"/>
            <result property="nsjctzbz" column="nsjctzbz" jdbcType="CHAR"/>
            <result property="wkpfjzjtjyssbz" column="wkpfjzjtjyssbz" jdbcType="CHAR"/>
            <result property="wkpjzjtjyssbz" column="wkpjzjtjyssbz" jdbcType="CHAR"/>
            <result property="nsjctzjyssbz" column="nsjctzjyssbz" jdbcType="CHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,wkpfjzjtje,wkpfjzjtse,
        wkpjzjtje,wkpjzjtse,nsjctzje,
        nsjctzse,jzjtbz,nsjctzbz,
        wkpfjzjtjyssbz,wkpjzjtjyssbz,nsjctzjyssbz,djxh,sszq
    </sql>
</mapper>
