<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzBsqkxsxeMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.sshz.ZnsbTzzxLqsshzBsqkxsxeDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zzuuid" column="zzuuid" jdbcType="VARCHAR"/>
            <result property="bsqkxsxe" column="bsqkxsxe" jdbcType="DECIMAL"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="jyssbz" column="jyssbz" jdbcType="CHAR"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zzuuid,bsqkxsxe,
        ssjmxz_dm,swsx_dm,jyssbz,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
