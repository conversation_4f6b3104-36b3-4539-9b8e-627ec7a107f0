<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.fcscztdsyssycj.SbCxsFtdcjglbMapper">
    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.fcsCztdsyssycj.SbCxsFtdcjglbDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
        <result property="gluuid1" column="gluuid_1" jdbcType="VARCHAR"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="VARCHAR"/>
        <result property="bdcqzh" column="bdcqzh" jdbcType="VARCHAR"/>
        <result property="bdcdyh" column="bdcdyh" jdbcType="VARCHAR"/>
        <result property="tdzldz" column="tdzldz" jdbcType="VARCHAR"/>
        <result property="ztDm" column="zt_dm" jdbcType="VARCHAR"/>
        <result property="flbz" column="flbz" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="VARCHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,syuuid,gluuid_1,nsrsbh,zgswskfj_dm,
        bdcqzh,bdcdyh,tdzldz,zt_dm,flbz,yxbz,
        lrrq,xgrq,lrrsfid,xgrsfid,
        ywqd_dm,sjcsdq,sjgsdq,sjtb_sj
    </sql>

    <insert id="insertFtdcjglb" parameterType="com.css.znsb.nssb.pojo.domain.fcsCztdsyssycj.SbCxsFtdcjglbDO">
        insert into sb_cxs_ftdcjglb (<include refid="Base_Column_List"/>)
        values
        (#{uuid},#{djxh},#{syuuid},#{gluuid1},#{nsrsbh},#{zgswskfjDm},
         #{bdcqzh},#{bdcdyh},#{tdzldz},#{ztDm},#{flbz},#{yxbz},
         #{lrrq},#{xgrq},#{lrrsfid},#{xgrsfid},
         #{ywqdDm},#{sjcsdq},#{sjgsdq},#{sjtbSj})
    </insert>

    <select id="queryFtdcjglbByGluuid" parameterType="string" resultType="com.css.znsb.nssb.pojo.domain.fcsCztdsyssycj.SbCxsFtdcjglbDO" >
        select <include refid="Base_Column_List"/> from sb_cxs_ftdcjglb where yxbz = 'Y' and djxh = #{djxh} and gluuid_1 = #{gluuid}
    </select>

    <update id="updateFtdcjglbByUuid" parameterType="com.css.znsb.nssb.pojo.domain.fcsCztdsyssycj.SbCxsFtdcjglbDO">
        update sb_cxs_ftdcjglb
        set
            xgrsfid = #{xgrsfid},
            xgrq = #{xgrq}
            <if test = "syuuid != null and syuuid != ''" >
                ,syuuid = #{syuuid}
            </if>
            <if test = "ztDm != null and ztDm != ''">
                ,zt_dm = #{ztDm}
            </if>
        where yxbz = 'Y'
            and uuid = #{uuid}
    </update>

    <delete id="deleteFtdcjglbBySyuuid">
        delete from sb_cxs_ftdcjglb where syuuid in
        <foreach collection="list" item="syuuid" index="index" open="(" close=")" separator=",">
            #{syuuid}
        </foreach>
    </delete>

    <delete id="deleteFtdcjglbBySyuuidYxbz" parameterType="com.css.znsb.nssb.pojo.domain.fcsCztdsyssycj.SbCxsFtdcjglbDO">
        update sb_cxs_ftdcjglb
        set
            yxbz = 'N'
        where yxbz = 'Y'
            and syuuid in
        <foreach collection="list" item="syuuid" index="index" open="(" close=")" separator=",">
            #{syuuid}
        </foreach>
    </delete>


    <resultMap id="FwxxGridlbVO" type="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.FwxxGridlbVO">
        <result property="fyxxuuid" column="fyxxuuid" jdbcType="VARCHAR"/>
        <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="tbrq1" column="tbrq_1" jdbcType="TIMESTAMP"/>
        <result property="fwcqzsh" column="fwcqzsh" jdbcType="VARCHAR"/>
        <result property="fcmc" column="fcmc" jdbcType="VARCHAR"/>
        <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
        <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
        <result property="fwzldz" column="fwzldz" jdbcType="VARCHAR"/>
        <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
        <result property="dwlsgxDm" column="dwlsgx_dm" jdbcType="CHAR"/>
        <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
        <result property="nsrlx" column="nsrlx" jdbcType="CHAR"/>
        <result property="qsrnsrsbh" column="qsrnsrsbh" jdbcType="VARCHAR"/>
        <result property="qsrnsrmc" column="qsrnsrmc" jdbcType="VARCHAR"/>
        <result property="tdsybh" column="tdsybh" jdbcType="VARCHAR"/>
        <result property="fcytDm" column="fcyt_dm" jdbcType="CHAR"/>
        <result property="jzmj" column="jzmj" jdbcType="DECIMAL"/>
        <result property="csqdsj" column="csqdsj" jdbcType="TIMESTAMP"/>
        <result property="nsywzzsj" column="nsywzzsj" jdbcType="TIMESTAMP"/>
        <result property="dlr" column="dlr" jdbcType="VARCHAR"/>
        <result property="dlrsfzjhm1" column="dlrsfzjhm1" jdbcType="VARCHAR"/>
        <result property="slrq" column="slrq" jdbcType="TIMESTAMP"/>
        <result property="slswjgDm" column="slswjg_dm" jdbcType="CHAR"/>
        <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
        <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
        <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
        <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="slr" column="slr" jdbcType="VARCHAR"/>
        <result property="tbr" column="tbr" jdbcType="VARCHAR"/>
        <result property="fcsnsrlx" column="fcsnsrlx" jdbcType="CHAR"/>
        <result property="czrsj" column="czrsj" jdbcType="TIMESTAMP"/>
        <result property="cjpkbz" column="cjpkbz" jdbcType="CHAR"/>
        <result property="czpkbz" column="czpkbz" jdbcType="CHAR"/>
        <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
        <result property="dlrsfzjzlDm1" column="dlrsfzjzl_dm_1" jdbcType="CHAR"/>
        <result property="bdcdyh" column="bdcdyh" jdbcType="VARCHAR"/>
        <result property="dljgqz" column="dljgqz" jdbcType="VARCHAR"/>
        <result property="jbrsfzjzlDm" column="jbrsfzjzl_dm" jdbcType="VARCHAR"/>
        <result property="sfyybdcdydm" column="sfyybdcdydm" jdbcType="CHAR"/>
        <result property="bdcqzlx" column="bdcqzlx" jdbcType="CHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
        <result property="bczt" column="bczt" jdbcType="CHAR"/>
        <result property="cssm" column="cssm" jdbcType="VARCHAR"/>
        <result property="ycyy" column="ycyy" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="FwxxGridlbVO_Column_List">
        fyxxuuid,fybh,djxh,
        tbrq_1,fwcqzsh,fcmc,
        xzqhsz_dm,jdxz_dm,fwzldz,
        hy_dm,dwlsgx_dm,zgswskfj_dm,
        nsrlx,qsrnsrsbh,qsrnsrmc,
        tdsybh,fcyt_dm,jzmj,
        csqdsj,nsywzzsj,dlr,
        dlrsfzjhm1,slrq,slswjg_dm,
        yxqq,yxqz,yxbz,
        lrr_dm,lrrq,xgr_dm,
        xgrq,sjgsdq,sjtb_sj,
        slr,tbr,fcsnsrlx,
        czrsj,cjpkbz,czpkbz,
        sjblbz,dlrsfzjzl_dm_1,bdcdyh,
        dljgqz,jbrsfzjzl_dm,sfyybdcdydm,
        bdcqzlx,lrrsfid,xgrsfid,
        ywqd_dm,sjcsdq,uuid,pclsh,bczt,cssm,
        ycyy
    </sql>


    <select id="queryDcjGlxxByDjxh" parameterType="string" resultType="com.css.znsb.nssb.pojo.domain.fcsCztdsyssycj.SbCxsFtdcjglbDO" >
        select <include refid="Base_Column_List"/> from sb_cxs_ftdcjglb where yxbz = 'Y' and djxh = #{djxh}
    </select>

    <insert id="saveFcsfyxx">
        insert into znsb_cxs_fyxxcjb (fyxxuuid,fybh,djxh,tbrq_1,fwcqzsh,fcmc,xzqhsz_dm,jdxz_dm,fwzldz,hy_dm,dwlsgx_dm,zgswskfj_dm,nsrlx,qsrnsrsbh,qsrnsrmc,
            tdsybh,fcyt_dm,jzmj,csqdsj,nsywzzsj,dlr,dlrsfzjhm1,slrq,slswjg_dm,yxqq,yxqz,yxbz,lrr_dm,lrrq,xgr_dm,xgrq,slr,tbr,fcsnsrlx,czrsj,
            cjpkbz,czpkbz,dlrsfzjzl_dm_1,bdcdyh,dljgqz,jbrsfzjzl_dm,uuid,bczt,cssm,bdcqzlx)
        values
        (#{fyxxuuid},#{fybh},#{djxh},#{tbrq1},#{fwcqzsh},#{fcmc},#{xzqhszDm},#{jdxzDm},#{fwzldz},#{hyDm},#{dwlsgxDm},#{zgswskfjDm},
        #{nsrlx},#{qsrnsrsbh},#{qsrnsrmc},#{tdsybh},#{fcytDm},#{jzmj},#{csqdsj},#{nsywzzsj},#{dlr},#{dlrsfzjhm1},#{slrq},#{slswjgDm},
        #{yxqq},#{yxqz},#{yxbz},#{lrrDm},#{lrrq},#{xgrDm},#{xgrq},#{slr},#{tbr},#{fcsnsrlx},
        #{czrsj},#{cjpkbz},#{czpkbz},#{dlrsfzjzlDm1},#{bdcdyh},#{dljgqz},#{jbrsfzjzlDm},#{uuid},#{bczt},#{bdcqzlx})
    </insert>


    <insert id="saveCjsymx">
        INSERT INTO znsb_cxs_cjjzsymxb (cjsyuuid, fyxxuuid, fcyz, czwyz, czfcmj, jsbl, yxqq, yxqz, ycjsyuuid, zspm_dm, yxbz, bgbm, lrr_dm, lrrq, xgr_dm, xgrq, jmxzdmxx, jmshj,uuid)
        VALUES
        (#{cjsyuuid},#{fyxxuuid},#{fcyz},#{czwyz},#{czfcmj},#{jsbl},#{yxqq},#{yxqz},#{ycjsyuuid},
         #{zspmDm},#{yxbz},#{bgbm},#{lrrDm},#{lrrq},#{xgrDm},#{xgrq},#{jmxzdmxx},#{jmshj},#{uuid})
    </insert>

    <update id="updateCjsymx">
        UPDATE znsb_cxs_cjjzsymxb
        <set>
            <if test="fcyz != null">fcyz = #{fcyz},</if>
            <if test="czwyz != null">czwyz = #{czwyz},</if>
            <if test="czfcmj != null">czfcmj = #{czfcmj},</if>
            <if test="jsbl != null">jsbl = #{jsbl},</if>
            <if test="yxqq != null">yxqq = #{yxqq},</if>
            <if test="yxqz != null">yxqz = #{yxqz},</if>
            <if test="zspmDm != null">zspm_dm = #{zspmDm},</if>
            <if test="bgbm != null">bgbm = #{bgbm},</if>
            <if test="xgrq != null">xgrq = #{xgrq},</if>
            <if test="jmxzdmxx != null">jmxzdmxx = #{jmxzdmxx},</if>
            <if test="jmshj != null">jmshj = #{jmshj}</if>
            <if test="ycjsyuuid != null">ycjsyuuid = #{ycjsyuuid}</if>
        </set>
        WHERE cjsyuuid = #{cjsyuuid}
    </update>

    <insert id="saveCzsymx">
        INSERT INTO znsb_cxs_czjzsymxb (czsyuuid, fyxxuuid, czfnsrsbh, czfnsrmc, czmj, htzjzsr, htydzlqq, htydzlqz, sbzjsr, sbzjsszlqq,
        sbzjsszlqz, sl_1, yxqq, yxqz, yczsyuuid, yxbz, zspm_dm, bgbm, lrr_dm, lrrq, xgr_dm, xgrq, pkbz, jmxzdmxx, jmshj, czfdjxh,uuid)
        VALUES
        (#{czsyuuid},#{fyxxuuid},#{czfnsrsbh},#{czfnsrmc},#{czmj},#{htzjzsr},#{htydzlqq},#{htydzlqz},#{sbzjsr},#{sbzjsszlqq}
        ,#{sbzjsszlqz},#{sl1},#{yxqq},#{yxqz},#{yczsyuuid},#{yxbz},#{zspmDm},#{bgbm},#{lrrDm},#{lrrq},
         #{xgrDm},#{xgrq},#{pkbz},#{jmxzdmxx},#{jmshj},#{czfdjxh},#{uuid})
    </insert>

    <update id="updateCzsymx">
        UPDATE znsb_cxs_czjzsymxb
        <set>
            <if test="czfnsrsbh != null">czfnsrsbh = #{czfnsrsbh},</if>
            <if test="czfnsrmc != null">czfnsrmc = #{czfnsrmc},</if>
            <if test="czmj != null">czmj = #{czmj},</if>
            <if test="htzjzsr != null">htzjzsr = #{htzjzsr},</if>
            <if test="htydzlqq != null">htydzlqq = #{htydzlqq},</if>
            <if test="htydzlqz != null">htydzlqz = #{htydzlqz},</if>
            <if test="sbzjsr != null">sbzjsr = #{sbzjsr},</if>
            <if test="sbzjsszlqq != null">sbzjsszlqq = #{sbzjsszlqq},</if>
            <if test="sbzjsszlqz != null">sbzjsszlqz = #{sbzjsszlqz},</if>
            <if test="sl1 != null">sl_1 = #{sl1},</if>
            <if test="yxqq != null">yxqq = #{yxqq},</if>
            <if test="yxqz != null">yxqz = #{yxqz},</if>
            <if test="yczsyuuid != null">yczsyuuid = #{yczsyuuid},</if>
            <if test="yxbz != null">yxbz = #{yxbz},</if>
            <if test="zspmDm != null">zspm_dm = #{zspmDm},</if>
            <if test="bgbm != null">bgbm = #{bgbm},</if>
            <if test="xgrq != null">xgrq = #{xgrq},</if>
            <if test="jmxzdmxx != null">jmxzdmxx = #{jmxzdmxx},</if>
            <if test="jmshj != null">jmshj = #{jmshj},</if>
            <if test="czfdjxh != null">czfdjxh = #{czfdjxh}</if>
        </set>
        WHERE czsyuuid = #{czsyuuid}
    </update>

    <select id="queryFwxxGridlb" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.FwxxGridlbVO" >
        select <include refid="FwxxGridlbVO_Column_List"/> from znsb_cxs_fyxxcjb where yxbz = 'Y' and djxh = #{djxh}
        <if test='fybh != null and fybh != ""'>
            and fybh LIKE CONCAT('%', #{fybh}, '%')
        </if>
        <if test='fcmc != null and fcmc != ""'>
            and fcmc LIKE CONCAT('%', #{fcmc}, '%')
        </if>
        <if test='fwzldz != null and fwzldz != ""'>
            and fwzldz LIKE CONCAT('%', #{fwzldz}, '%')
        </if>
        <if test='nsyxqq != null and nsyxqq != ""'>
            and csqdsj &gt;= STR_TO_DATE(#{nsyxqq},'%Y-%m-%d')
        </if>
        <if test='nsyxqz != null and nsyxqz != ""'>
            and csqdsj &lt;= STR_TO_DATE(#{nsyxqz},'%Y-%m-%d')
        </if>
    </select>

    <select id="queryFcsFwBysyuuid" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.FwxxGridlbVO" >
        select <include refid="FwxxGridlbVO_Column_List"/> from znsb_cxs_fyxxcjb where yxbz = 'Y' and fyxxuuid = #{fyxxuuid}
    </select>

    <select id="queryFcsFwByuuid" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.FwxxGridlbVO" >
        select <include refid="FwxxGridlbVO_Column_List"/> from znsb_cxs_fyxxcjb where yxbz = 'Y' and uuid = #{uuid}
    </select>

    <select id="queryWsbSyuuid" resultType="com.css.znsb.nssb.pojo.vo.hxzg.SB10733.SBCxsFyxxcjbVO" >
        select <include refid="FwxxGridlbVO_Column_List"/> from znsb_cxs_fyxxcjb where yxbz = 'Y' and djxh = #{djxh} and bczt = '30'
    </select>

<!--    <insert id="saveFcsfyxx">-->
<!--        insert into znsb_cxs_fyxxcjb (fyxxuuid,fybh,djxh,tbrq_1,fwcqzsh,fcmc,xzqhsz_dm,jdxz_dm,fwzldz,hy_dm,dwlsgx_dm,zgswskfj_dm,nsrlx,qsrnsrsbh,qsrnsrmc,-->
<!--                                      tdsybh,fcyt_dm,jzmj,csqdsj,nsywzzsj,dlr,dlrsfzjhm1,slrq,slswjg_dm,yxqq,yxqz,yxbz,lrr_dm,lrrq,xgr_dm,xgrq,slr,tbr,fcsnsrlx,czrsj,-->
<!--                                      cjpkbz,czpkbz,dlrsfzjzl_dm_1,bdcdyh,dljgqz,jbrsfzjzl_dm)-->
<!--        values-->
<!--        (#{fyxxuuid},#{fybh},#{djxh},#{tbrq1},#{fwcqzsh},#{fcmc},#{xzqhszDm},#{jdxzDm},#{fwzldz},#{hyDm},#{dwlsgxDm},#{zgswskfjDm},-->
<!--         #{nsrlx},#{qsrnsrsbh},#{qsrnsrmc},#{tdsybh},#{fcytDm},#{jzmj},#{csqdsj},#{nsywzzsj},#{dlr},#{dlrsfzjhm1},#{slrq},#{slswjgDm},-->
<!--         #{yxqq},#{yxqz},#{yxbz},#{lrrDm},#{lrrq},#{xgrDm},#{xgrq},#{slr},#{tbr},#{fcsnsrlx},-->
<!--         #{czrsj},#{cjpkbz},#{czpkbz},#{dlrsfzjzlDm1},#{bdcdyh},#{dljgqz},#{jbrsfzjzlDm})-->
<!--    </insert>-->

    <update id="updateFcsfyxx">
        UPDATE znsb_cxs_fyxxcjb
        <set>
            <if test="fyxxuuid != null">fyxxuuid = #{fyxxuuid},</if>
            <if test="fybh != null">fybh = #{fybh},</if>
            <if test="djxh != null">djxh = #{djxh},</if>
            <if test="tbrq1 != null">tbrq_1 = #{tbrq1},</if>
            <if test="fwcqzsh != null">fwcqzsh = #{fwcqzsh},</if>
            <if test="fcmc != null">fcmc = #{fcmc},</if>
            <if test="xzqhszDm != null">xzqhsz_dm = #{xzqhszDm},</if>
            <if test="jdxzDm != null">jdxz_dm = #{jdxzDm},</if>
            <if test="fwzldz != null">fwzldz = #{fwzldz},</if>
            <if test="hyDm != null">hy_dm = #{hyDm},</if>
            <if test="dwlsgxDm != null">dwlsgx_dm = #{dwlsgxDm},</if>
            <if test="zgswskfjDm != null">zgswskfj_dm = #{zgswskfjDm},</if>
            <if test="nsrlx != null">nsrlx = #{nsrlx},</if>
            <if test="qsrnsrsbh != null">qsrnsrsbh = #{qsrnsrsbh},</if>
            <if test="qsrnsrmc != null">qsrnsrmc = #{qsrnsrmc},</if>
            <if test="tdsybh != null">tdsybh = #{tdsybh},</if>
            <if test="fcytDm != null">fcyt_dm = #{fcytDm},</if>
            <if test="jzmj != null">jzmj = #{jzmj},</if>
            <if test="csqdsj != null">csqdsj = #{csqdsj},</if>
            <if test="nsywzzsj != null">nsywzzsj = #{nsywzzsj},</if>
            <if test="dlr != null">dlr = #{dlr},</if>
            <if test="dlrsfzjhm1 != null">dlrsfzjhm1 = #{dlrsfzjhm1},</if>
            <if test="slrq != null">slrq = #{slrq},</if>
            <if test="slswjgDm != null">slswjg_dm = #{slswjgDm},</if>
            <if test="yxqq != null">yxqq = #{yxqq},</if>
            <if test="yxqz != null">yxqz = #{yxqz},</if>
            <if test="yxbz != null">yxbz = #{yxbz},</if>
            <if test="lrrDm != null">lrr_dm = #{lrrDm},</if>
            <if test="lrrq != null">lrrq = #{lrrq},</if>
            <if test="xgrDm != null">xgr_dm = #{xgrDm},</if>
            <if test="xgrq != null">xgrq = #{xgrq},</if>
            <if test="slr != null">slr = #{slr},</if>
            <if test="tbr != null">tbr = #{tbr},</if>
            <if test="fcsnsrlx != null">fcsnsrlx = #{fcsnsrlx},</if>
            <if test="czrsj != null">czrsj = #{czrsj},</if>
            <if test="cjpkbz != null">cjpkbz = #{cjpkbz},</if>
            <if test="czpkbz != null">czpkbz = #{czpkbz},</if>
            <if test="dlrsfzjzlDm1 != null">dlrsfzjzl_dm_1 = #{dlrsfzjzlDm1},</if>
            <if test="bdcdyh != null">bdcdyh = #{bdcdyh},</if>
            <if test="dljgqz != null">dljgqz = #{dljgqz},</if>
            <if test="jbrsfzjzlDm != null">jbrsfzjzl_dm = #{jbrsfzjzlDm},</if>
            <if test="bczt != null">bczt = #{bczt},</if>
            <if test="cssm != null">cssm = #{cssm},</if>
            <if test="pclsh != null">pclsh = #{pclsh}</if>
            <if test="ycyy != null">ycyy = #{ycyy}</if>
            <if test="bgzt != null">ycyy = #{bgzt}</if>
        </set>
        WHERE uuid = #{uuid}
    </update>

    <update id="cxywzz">
        UPDATE znsb_cxs_fyxxcjb
        set
            nsywzzsj = #{nsywzzsj},
            czrsj = #{czrsj}
        WHERE uuid = #{uuid}
    </update>


    <!--从价-->
    <resultMap id="CjResultMap" type="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CjjzsymxbGridlbVO">
        <id property="cjsyuuid" column="cjsyuuid" jdbcType="VARCHAR"/>
        <result property="fyxxuuid" column="fyxxuuid" jdbcType="VARCHAR"/>
        <result property="fcyz" column="fcyz" jdbcType="DECIMAL"/>
        <result property="czwyz" column="czwyz" jdbcType="DECIMAL"/>
        <result property="czfcmj" column="czfcmj" jdbcType="DECIMAL"/>
        <result property="jsbl" column="jsbl" jdbcType="DECIMAL"/>
        <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
        <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
        <result property="ycjsyuuid" column="ycjsyuuid" jdbcType="VARCHAR"/>
        <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
        <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
        <result property="bgbm" column="bgbm" jdbcType="CHAR"/>
        <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="jmxzdmxx" column="jmxzdmxx" jdbcType="VARCHAR"/>
        <result property="jmshj" column="jmshj" jdbcType="DECIMAL"/>
        <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
        <result property="blbyzyy" column="blbyzyy" jdbcType="VARCHAR"/>
        <result property="yxqbyzyy" column="yxqbyzyy" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Cj_Column_List">
        cjsyuuid,fyxxuuid,fcyz,
        czwyz,czfcmj,jsbl,
        yxqq,yxqz,ycjsyuuid,
        zspm_dm,yxbz,bgbm,
        lrr_dm,lrrq,xgr_dm,
        xgrq,sjgsdq,sjtb_sj,
        jmxzdmxx,jmshj,sjblbz,
        blbyzyy,yxqbyzyy,lrrsfid,
        xgrsfid,sjcsdq,ywqd_dm,uuid
    </sql>

    <select id="queryCjList" parameterType="string" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CjjzsymxbGridlbVO" >
        select <include refid="Cj_Column_List"/> from znsb_cxs_cjjzsymxb where yxbz = 'Y' and uuid = #{fyxxuuid}
    </select>

    <select id="queryCjysxxbyId" parameterType="string" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CjjzsymxbGridlbVO" >
        select <include refid="Cj_Column_List"/> from znsb_cxs_cjjzsymxb where yxbz = 'Y' and cjsyuuid = #{cjsyuuid}
    </select>
    <!--从租-->
    <resultMap id="CzResultMap" type="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CzjzsymxbGridlbVO">
        <id property="czsyuuid" column="czsyuuid" jdbcType="VARCHAR"/>
        <result property="fyxxuuid" column="fyxxuuid" jdbcType="VARCHAR"/>
        <result property="czfnsrsbh" column="czfnsrsbh" jdbcType="VARCHAR"/>
        <result property="czfnsrmc" column="czfnsrmc" jdbcType="VARCHAR"/>
        <result property="czmj" column="czmj" jdbcType="DECIMAL"/>
        <result property="htzjzsr" column="htzjzsr" jdbcType="DECIMAL"/>
        <result property="htydzlqq" column="htydzlqq" jdbcType="TIMESTAMP"/>
        <result property="htydzlqz" column="htydzlqz" jdbcType="TIMESTAMP"/>
        <result property="sbzjsr" column="sbzjsr" jdbcType="DECIMAL"/>
        <result property="sbzjsszlqq" column="sbzjsszlqq" jdbcType="TIMESTAMP"/>
        <result property="sbzjsszlqz" column="sbzjsszlqz" jdbcType="TIMESTAMP"/>
        <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
        <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
        <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
        <result property="yczsyuuid" column="yczsyuuid" jdbcType="VARCHAR"/>
        <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
        <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
        <result property="bgbm" column="bgbm" jdbcType="CHAR"/>
        <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="pkbz" column="pkbz" jdbcType="CHAR"/>
        <result property="jmxzdmxx" column="jmxzdmxx" jdbcType="VARCHAR"/>
        <result property="jmshj" column="jmshj" jdbcType="DECIMAL"/>
        <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
        <result property="czfdjxh" column="czfdjxh" jdbcType="DECIMAL"/>
        <result property="zjzdpgj" column="zjzdpgj" jdbcType="DECIMAL"/>
        <result property="blbyzyy" column="blbyzyy" jdbcType="VARCHAR"/>
        <result property="yxqbyzyy" column="yxqbyzyy" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Cz_Column_List">
        czsyuuid,fyxxuuid,czfnsrsbh,
        czfnsrmc,czmj,htzjzsr,
        htydzlqq,htydzlqz,sbzjsr,
        sbzjsszlqq,sbzjsszlqz,sl_1,
        yxqq,yxqz,yczsyuuid,
        yxbz,zspm_dm,bgbm,
        lrr_dm,lrrq,xgr_dm,
        xgrq,sjgsdq,sjtb_sj,
        pkbz,jmxzdmxx,jmshj,
        sjblbz,czfdjxh,zjzdpgj,
        blbyzyy,yxqbyzyy,ywqd_dm,
        sjcsdq,xgrsfid,lrrsfid,uuid
    </sql>

    <select id="queryCzList" parameterType="string" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CzjzsymxbGridlbVO" >
        select <include refid="Cz_Column_List"/> from znsb_cxs_czjzsymxb where yxbz = 'Y' and uuid = #{fyxxuuid}
    </select>

    <select id="queryCzysxxbyId" parameterType="string" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CzjzsymxbGridlbVO" >
        select <include refid="Cz_Column_List"/> from znsb_cxs_czjzsymxb where yxbz = 'Y' and czsyuuid = #{czsyuuid}
    </select>

    <resultMap id="JmxxResultMap" type="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.FcsjmxxGridlbVO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="ysuuid" column="ysuuid" jdbcType="VARCHAR"/>
        <result property="jmmj" column="jmmj" jdbcType="DECIMAL"/>
        <result property="yjmsje1" column="yjmsje_1" jdbcType="DECIMAL"/>
        <result property="jmsxmmc1" column="jmsxmmc_1" jdbcType="VARCHAR"/>
        <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
        <result property="jmfsDm" column="jmfs_dm" jdbcType="VARCHAR"/>
        <result property="jmsl" column="jmsl" jdbcType="DECIMAL"/>
        <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
        <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
        <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
        <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="jmqxq" column="jmqxq" jdbcType="TIMESTAMP"/>
        <result property="jmqxz" column="jmqxz" jdbcType="TIMESTAMP"/>
        <result property="jmszjsr" column="jmszjsr" jdbcType="DECIMAL"/>
        <result property="jmed" column="jmed" jdbcType="DECIMAL"/>
        <result property="jmfd" column="jmfd" jdbcType="DECIMAL"/>
        <result property="jmzlxDm" column="jmzlx_dm" jdbcType="VARCHAR"/>
        <result property="jmslxDm" column="jmslx_dm" jdbcType="VARCHAR"/>
        <result property="jmsfcyz" column="jmsfcyz" jdbcType="DECIMAL"/>
        <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
        <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
        <result property="ftdqybz" column="ftdqybz" jdbcType="CHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Jmxx_Column_List">
        uuid,ysuuid,jmmj,
        yjmsje_1,jmsxmmc_1,ssjmxz_dm,
        jmfs_dm,jmsl,yxqq,
        yxqz,yxbz,lrr_dm,
        lrrq,xgr_dm,xgrq,
        sjgsdq,sjtb_sj,jmqxq,
        jmqxz,jmszjsr,jmed,
        jmfd,jmzlx_dm,jmslx_dm,
        jmsfcyz,swsx_dm,sjblbz,
        ftdqybz,ywqd_dm,sjcsdq,
        xgrsfid,lrrsfid
    </sql>

    <insert id="saveJmxx" parameterType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.FcsjmxxGridlbVO">
        insert into znsb_znsb_cxs_ysjmxzxx (uuid,ysuuid,jmmj,
                                            yjmsje_1,jmsxmmc_1,ssjmxz_dm,
                                            jmfs_dm,jmsl,yxqq,
                                            yxqz,yxbz,jmqxq,
                                            jmqxz,jmszjsr,jmed,
                                            jmfd,jmzlx_dm,jmslx_dm,
                                            jmsfcyz,swsx_dm,ftdqybz)
        values
        (#{uuid},#{ysuuid},#{jmmj},#{yjmsje1},#{jmsxmmc1},#{ssjmxzDm},
        #{jmfsDm},#{jmsl},#{yxqq},#{yxqz},#{yxbz},#{jmqxq},
        #{jmqxz},#{jmszjsr},#{jmed},#{jmfd},
        #{jmzlxDm},#{jmslxDm},#{jmsfcyz},#{swsxDm},#{ftdqybz})
    </insert>

    <update id="updateJmxx">
        UPDATE znsb_znsb_cxs_ysjmxzxx
        <set>
            <if test="ysuuid != null">ysuuid = #{ysuuid},</if>
            <if test="jmmj != null">jmmj = #{jmmj},</if>
            <if test="yjmsje1 != null">yjmsje_1 = #{yjmsje1},</if>
            <if test="jmsxmmc1 != null">jmsxmmc_1 = #{jmsxmmc1},</if>
            <if test="ssjmxzDm != null">ssjmxz_dm = #{ssjmxzDm},</if>
            <if test="jmfsDm != null">jmfs_dm = #{jmfsDm},</if>
            <if test="jmsl != null">jmsl = #{jmsl},</if>
            <if test="yxqq != null">yxqq = #{yxqq},</if>
            <if test="yxqz != null">yxqz = #{yxqz},</if>
            <if test="yxbz != null">yxbz = #{yxbz},</if>
            <if test="jmqxq != null">jmqxq = #{jmqxq},</if>
            <if test="jmqxz != null">jmqxz = #{jmqxz},</if>
            <if test="jmszjsr != null">jmszjsr = #{jmszjsr},</if>
            <if test="jmed != null">jmed = #{jmed},</if>
            <if test="jmfd != null">jmfd = #{jmfd},</if>
            <if test="jmzlxDm != null">jmzlx_dm = #{jmzlxDm},</if>
            <if test="jmslxDm != null">jmslx_dm = #{jmslxDm},</if>
            <if test="jmsfcyz != null">jmsfcyz = #{jmsfcyz},</if>
            <if test="swsxDm != null">swsx_dm = #{swsxDm},</if>
            <if test="ftdqybz != null">ftdqybz = #{ftdqybz},</if>
        </set>
        WHERE uuid = #{uuid}
    </update>

    <select id="queryFcssysyxxByFyxx" parameterType="string" resultType="com.css.znsb.nssb.pojo.vo.fcscztdsys.SBCxsFcssysyxxVO" >
        SELECT * FROM SB_CXS_FCSSYSYXX T WHERE T.ZFBZ_1 = 'N' and T.fyxxuuid = #{fyxxuuid}
    </select>

    <select id="queryfcscjmxbyfyxxuuid" parameterType="string" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CjjzsymxbGridlbVO" >
        SELECT * FROM SB_CXS_CJJZSYMXB WHERE YXBZ = 'Y' And FYXXUUID = #{fyxxuuid}
    </select>

    <select id="queryfcsczmxbyfyxxuuid" parameterType="string" resultType="com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CzjzsymxbGridlbVO" >
        SELECT * FROM SB_CXS_CZJZSYMXB WHERE YXBZ = 'Y' AND FYXXUUID = #{fyxxuuid}
    </select>

    <delete id="deleteFcsCj">
        update znsb_cxs_cjjzsymxb
        set
        yxbz = 'N'
        where yxbz = 'Y'
        and uuid in
        <foreach collection="list" item="uuid" index="index" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deleteFcsCz">
        update znsb_cxs_czjzsymxb
        set
        yxbz = 'N'
        where yxbz = 'Y'
        and uuid in
        <foreach collection="list" item="uuid" index="index" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deletecjsyxxbyuuid">
        update znsb_cxs_czjzsymxb
        set yxbz = 'N',xgrq = #{xgrq}
        where yxbz = 'Y'
        and czsyuuid = #{czsyuuid}
    </delete>

    <delete id="deleteFcsFw">
        update znsb_cxs_fyxxcjb
        set
        yxbz = 'N'
        where yxbz = 'Y'
        and uuid in
        <foreach collection="list" item="uuid" index="index" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deleteJmxxbysuuid">
        update znsb_znsb_cxs_ysjmxzxx
        set
        yxbz = 'N',zfbz_1 = 'Y'
        where yxbz = 'Y'
        and ysuuid = #{ysuuid}
    </delete>

    <delete id="deletecjxxbyuuid">
        update znsb_cxs_cjjzsymxb
        set
        yxbz = 'N',zfbz_1 = 'Y'
        where yxbz = 'Y'
        and uuid = #{uuid}
    </delete>

    <delete id="deleteczxxbyuuid">
        update znsb_cxs_czjzsymxb
        set
        yxbz = 'N',zfbz_1 = 'Y'
        where yxbz = 'Y'
        and uuid = #{uuid}
    </delete>

    <select id="getJmxxByYsuuid" parameterType="string" resultType="com.css.znsb.nssb.pojo.vo.fcscztdsys.SBCxsYsjmxzxxVO" >
        select <include refid="Jmxx_Column_List"/> from znsb_znsb_cxs_ysjmxzxx where ysuuid= #{ysuuid} and yxbz='Y'
    </select>

    <update id="updateFcsbczt">
        UPDATE znsb_cxs_fyxxcjb
        <set>
            bczt = '30'
        </set>
        WHERE fyxxuuid in ('5fd01cbffc3a42ffac85ad3f27d505dc','1c0ecb5bf5654966a90068e38b438cc4','20f9760ff1de4f428881553cd86eaf57','2cb08275fd40465cb9c9b308c7fc4d91')
    </update>
</mapper>
