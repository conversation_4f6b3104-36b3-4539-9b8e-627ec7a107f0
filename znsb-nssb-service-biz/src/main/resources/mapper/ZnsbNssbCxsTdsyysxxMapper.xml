<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyysxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyysxxDo">
            <id property="ysuuid" column="ysuuid" jdbcType="VARCHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="tddjDm" column="tddj_dm" jdbcType="CHAR"/>
            <result property="ystdmj" column="ystdmj" jdbcType="DECIMAL"/>
            <result property="dwse" column="dwse" jdbcType="DECIMAL"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="yysuuid" column="yysuuid" jdbcType="VARCHAR"/>
            <result property="bgbm" column="bgbm" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="jmxxuuid1" column="jmxxuuid1" jdbcType="VARCHAR"/>
            <result property="jmxxuuid2" column="jmxxuuid2" jdbcType="VARCHAR"/>
            <result property="jmxxuuid3" column="jmxxuuid3" jdbcType="VARCHAR"/>
            <result property="jmxzdmxx" column="jmxzdmxx" jdbcType="VARCHAR"/>
            <result property="jmshj" column="jmshj" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ysuuid,syuuid,zspm_dm,
        tddj_dm,ystdmj,dwse,
        yxqq,yxqz,yysuuid,
        bgbm,yxbz,lrr_dm,
        lrrq,xgr_dm,xgrq,
        sjgsdq,sjtb_sj,jmxxuuid1,
        jmxxuuid2,jmxxuuid3,jmxzdmxx,
        jmshj,sjblbz,ywqd_dm,
        sjcsdq,xgrsfid,lrrsfid,
        uuid
    </sql>
</mapper>
