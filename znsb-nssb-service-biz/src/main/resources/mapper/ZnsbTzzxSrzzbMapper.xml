<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrzzbDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="sszq" column="sszq" jdbcType="INTEGER"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
        <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
        <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
        <result property="srlxDm" column="srlx_dm" jdbcType="VARCHAR"/>
        <result property="jsfsDm1" column="jsfs_dm_1" jdbcType="VARCHAR"/>
        <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
        <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
        <result property="xssrkmdm" column="xssrkmdm" jdbcType="VARCHAR"/>
        <result property="xssrkmmc" column="xssrkmmc" jdbcType="VARCHAR"/>
        <result property="xssr" column="xssr" jdbcType="DECIMAL"/>
        <result property="xssekmdm" column="xssekmdm" jdbcType="VARCHAR"/>
        <result property="xssekmmc" column="xssekmmc" jdbcType="VARCHAR"/>
        <result property="xsse" column="xsse" jdbcType="DECIMAL"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
        <result property="xxsr" column="xxsr" jdbcType="DECIMAL"/>
        <result property="xscb" column="xscb" jdbcType="DECIMAL"/>
        <result property="cblrl" column="cblrl" jdbcType="DECIMAL"/>
        <result property="jsje" column="jsje" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sszq,djxh,
        nsrsbh,nsrmc,gsh_2,
        lrzx,srlx_dm,jsfs_dm_1,
        zsxm_dm,sl_1,xssrkmdm,
        xssrkmmc,xssr,xssekmdm,
        xssekmmc,xsse,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj,yxbz,xxsr,xscb,cblrl,jsje
    </sql>
    <select id="querSrzzHj" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzInitReqVO" resultType="java.util.Map">
        SELECT SUM(xssr) as xssr,SUM(xsse) as xsse,SUM(jsje) as jsje FROM znsb_tzzx_srzzb  where djxh = #{vo.djxh} AND yxbz = 'Y'
        <if test="vo.jsfsDm1 != null and vo.jsfsDm1 != ''">
            AND jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sszqq != null and vo.sszqq != ''" >
            AND sszq &gt;= #{vo.sszqq}
        </if>
        <if test = "vo.sszqz != null and vo.sszqz != ''" >
            AND sszq &lt;= #{vo.sszqz}
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.srlxdm != null and vo.srlxdm != ''" >
            AND srlxdm in
            <foreach collection="vo.srlxdm.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.xssrkmdm != null and vo.xssrkmdm != ''" >
            AND xssrkmdm in
            <foreach collection="vo.xssrkmdm.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="querySrzzAndSrcybdhzb" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT * FROM (
        (SELECT '' uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        '视同销售收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
                <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '130'
        GROUP BY
        srzzb.uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1)
        UNION ALL
        (SELECT
        srcybdhzb.uuid,
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srcybdhzb.nsrsbh,
        srcybdhzb.nsrmc,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1,
        srcybdhzb.zyjqtxssr AS xssr,
        srcybdhzb.xxse,
        srcybdhzb.xxskmse,
        srcybdhzb.cyse,
        srcybdhzb.hlwc,
        srcybdhzb.pzsl_2 pzsl,
        hlb.czrymc,
        hlb.lrrq czsj
        FROM
        znsb_tzzx_srcybdhzb srcybdhzb
        LEFT JOIN znsb_tzzx_cybdhlb hlb ON MD5(CONCAT(
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        srcybdhzb.nsrsbh,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1)) = hlb.md5 AND srcybdhzb.cyse = hlb.cyse
        WHERE
        srcybdhzb.sszq &gt;= #{vo.sszqq} AND srcybdhzb.sszq &lt;= #{vo.sszqz} AND srcybdhzb.djxh = #{vo.djxh}

        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srcybdhzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srcybdhzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srcybdhzb.sl1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srcybdhzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        )
        ) AS T
        ORDER BY sszq,abs(cyse) desc,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>
    <select id="querySrcybdhzb" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
       SELECT
        srcybdhzb.uuid,
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srcybdhzb.nsrsbh,
        srcybdhzb.nsrmc,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1,
        srcybdhzb.zyjqtxssr AS xssr,
        srcybdhzb.xxse,
        srcybdhzb.xxskmse,
        srcybdhzb.cyse,
        srcybdhzb.hlwc,
        srcybdhzb.pzsl_2 pzsl,
        if(hlb.uuid is not null, 'Y', 'N') hlbz
        FROM
        znsb_tzzx_srcybdhzb srcybdhzb
        LEFT JOIN znsb_tzzx_cybdhlb hlb ON MD5(CONCAT(
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        srcybdhzb.nsrsbh,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1)) = hlb.md5 AND srcybdhzb.cyse = hlb.cyse
        WHERE
        srcybdhzb.sszq &gt;= #{vo.sszqq} AND srcybdhzb.sszq &lt;= #{vo.sszqz}

        <if test = "vo.djxh != null and vo.djxh != ''" >
            AND srcybdhzb.djxh = #{vo.djxh}
        </if>
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srcybdhzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srcybdhzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srcybdhzb.sl1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srcybdhzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = 'vo.czcy != null and vo.czcy != "" and vo.czcy == "Y"' >
            AND abs(srcybdhzb.cyse) > 0
        </if>
        <if test = 'vo.hlbz != null and vo.hlbz != ""' >
            AND if(hlb.uuid is not null, 'Y', 'N') = #{vo.hlbz}
        </if>
        ORDER BY cyse,sszq,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>
    <select id="querySrzzhzb" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        '视同销售收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '130'
        GROUP BY
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1
        ORDER BY cyse,sszq,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>
    <select id="querySrzzhzbHj" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="java.util.Map">
        SELECT
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        1 as pzsl
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '130'
    </select>
    <select id="querySrcybdhzbHj" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="java.util.Map">
        SELECT
        sum(srcybdhzb.zyjqtxssr) AS xssr,
        sum(srcybdhzb.xxse) as xxse,
        sum(srcybdhzb.xxskmse) as xxskmse,
        sum(srcybdhzb.cyse) as cyse
        FROM
        znsb_tzzx_srcybdhzb srcybdhzb
        WHERE
        srcybdhzb.sszq &gt;= #{vo.sszqq} AND srcybdhzb.sszq &lt;= #{vo.sszqz} AND srcybdhzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srcybdhzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srcybdhzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srcybdhzb.sl1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srcybdhzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="querySrzzAndSrcybdhzbCybd" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT * FROM (
        (SELECT '' uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        '视同销售收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '130'
        GROUP BY
        srzzb.uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1)
        UNION ALL
        (SELECT
        srcybdhzb.uuid,
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srcybdhzb.nsrsbh,
        srcybdhzb.nsrmc,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1,
        srcybdhzb.zyjqtxssr AS xssr,
        srcybdhzb.xxse,
        srcybdhzb.xxskmse,
        srcybdhzb.cyse,
        srcybdhzb.hlwc,
        srcybdhzb.pzsl_2 pzsl,
        hlb.czrymc,
        hlb.lrrq czsj
        FROM
        znsb_tzzx_srcybdhzb srcybdhzb
        LEFT JOIN znsb_tzzx_cybdhlb hlb ON MD5(CONCAT(
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        srcybdhzb.nsrsbh,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1)) = hlb.md5 AND srcybdhzb.cyse = hlb.cyse
        WHERE
        srcybdhzb.sszq &gt;= #{vo.sszqq} AND srcybdhzb.sszq &lt;= #{vo.sszqz} AND srcybdhzb.djxh = #{vo.djxh}

        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srcybdhzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srcybdhzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srcybdhzb.sl1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srcybdhzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        )
        ) AS T
        ORDER BY sszq,abs(cyse) desc,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>

    <select id="initXgmnsrSrzzQuery" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzInitReqVO" resultType="com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrzzbDO">
        select jd sszq, SUM(xssr) as xssr,SUM(xsse) as xsse, xssekmdm,djxh,
        xssekmmc,
        srlxdm,
        gsh_2,
        lrzx,
        jsfs_dm_1,
        zsxm_dm,
        sl_1
         from
        (SELECT *, concat(left(sszq, 4), FLOOR((right(sszq, 2) - 1) / 3)) jd
        FROM znsb_tzzx_srzzb
        where djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND sl_1 in
            <foreach collection="vo.sl1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.srlxdm != null and vo.srlxdm != ''" >
            AND srlxdm in
            <foreach collection="vo.srlxdm.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.xssrkmdm != null and vo.xssrkmdm != ''" >
            AND xssrkmdm in
            <foreach collection="vo.xssrkmdm.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sszqq != null and vo.sszqq != ''" >
            and sszq &gt;= #{vo.sszqq}
        </if>
        <if test = "vo.sszqz != null and vo.sszqz != ''" >
            and sszq &lt;= #{vo.sszqz}
        </if>
            ) t
        GROUP BY
        djxh,
        xssekmdm,
        xssekmmc,
        jd,
        srlxdm,
        gsh_2,
        lrzx,
        jsfs_dm_1,
        zsxm_dm,
        sl_1
        ORDER BY lrzx,srlxdm,sl_1,xssekmdm
    </select>

    <select id="queryXgmnsrSrcybdhzb" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        select jd sszq, SUM(xssr) as xssr,SUM(xxse) as xxse,
        SUM(cyse) as cyse,
        SUM(pzsl) as pzsl, SUM(xxskmse) xxskmse, djxh,
        srlxdm,
        srlxmc,
        nsrsbh,
        nsrmc,
        gsh_2,
        lrzx,
        jsfs_dm_1,
        zsxm_dm,
        sl1,
        hlwc
        from
        (
        SELECT concat(left(sszq, 4), FLOOR((right(sszq, 2) - 1) / 3)) jd,
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srcybdhzb.nsrsbh,
        srcybdhzb.nsrmc,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1,
        srcybdhzb.zyjqtxssr AS xssr,
        srcybdhzb.xxse,
        srcybdhzb.xxskmse,
        srcybdhzb.cyse,
        srcybdhzb.hlwc,
        srcybdhzb.pzsl_2 pzsl
        FROM
        znsb_tzzx_srcybdhzb srcybdhzb
        WHERE
        srcybdhzb.sszq &gt;= #{vo.sszqq} AND srcybdhzb.sszq &lt;= #{vo.sszqz}

        <if test = "vo.djxh != null and vo.djxh != ''" >
            AND srcybdhzb.djxh = #{vo.djxh}
        </if>
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srcybdhzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srcybdhzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srcybdhzb.sl1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srcybdhzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = 'vo.czcy != null and vo.czcy != "" and vo.czcy == "Y"' >
            AND abs(srcybdhzb.cyse) > 0
        </if>
        <if test = 'vo.hlbz != null and vo.hlbz != ""' >
            AND if(hlb.uuid is not null, 'Y', 'N') = #{vo.hlbz}
        </if>
        ) t
        GROUP BY
        t.djxh,
        t.jd,
        t.srlxdm,
        t.srlxmc,
        t.nsrsbh,
        t.nsrmc,
        t.gsh_2,
        t.lrzx,
        t.jsfs_dm_1,
        t.zsxm_dm,
        t.sl1,
        t.hlwc
        ORDER BY cyse,sszq,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>
    <select id="querSrzzSxydHj" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzInitReqVO" resultType="java.util.Map">
        SELECT SUM(xssr) as xssr,
        SUM(
        CASE
        WHEN NOT (
        jsfs_dm_1 = '02'
        AND ROUND(sl_1, 2) = 0.05
        AND zsxm_dm IN ('03', '04', '05')
        )
        THEN xssr * 0.03
        ELSE xsse
        END
        ) AS xsse
        FROM znsb_tzzx_srzzb  where djxh = #{vo.djxh}
        AND yxbz = 'Y'
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sszqq != null and vo.sszqq != ''" >
            AND sszq &gt;= #{vo.sszqq}
        </if>
        <if test = "vo.sszqz != null and vo.sszqz != ''" >
            AND sszq &lt;= #{vo.sszqz}
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.srlxdm != null and vo.srlxdm != ''" >
            AND srlxdm in
            <foreach collection="vo.srlxdm.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.xssrkmdm != null and vo.xssrkmdm != ''" >
            AND xssrkmdm in
            <foreach collection="vo.xssrkmdm.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="querySrzzAndSrcybdhzbSxyd" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT * FROM (
        (SELECT '' uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        '视同销售收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '130'
        GROUP BY
        srzzb.uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1)
        UNION ALL
        (
        SELECT '' uuid,
        srzzb.djxh,
        srzzb.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm in ('110','120')
        GROUP BY
        srzzb.uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1
        )
        ) AS T
        ORDER BY sszq,abs(cyse) desc,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>
    <select id="querySrcybdhzbSxyd" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT '' uuid,
        srzzb.djxh,
        srzzb.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm in ('110','120')
        GROUP BY
        srzzb.uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1
    </select>
    <select id="querySrzzhzbqszgsr" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        '暂估收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '140'
        GROUP BY
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1
        ORDER BY cyse,sszq,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>
    <select id="querySrzzAndSrcybdhzbqs" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT * FROM (
        (SELECT '' uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        '视同销售收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '130'
        GROUP BY
        srzzb.uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1)
        UNION ALL
        (SELECT '' uuid,
        srzzb1.djxh,
        srzzb1.sszq,
        srzzb1.srlxdm,
        '暂估收入' as srlxmc,
        srzzb1.nsrsbh,
        srzzb1.nsrmc,
        srzzb1.gsh_2,
        srzzb1.lrzx,
        srzzb1.jsfs_dm_1,
        srzzb1.zsxm_dm,
        srzzb1.sl_1 AS sl1,
        SUM( srzzb1.xssr ) AS xssr,
        SUM( srzzb1.xsse ) AS xxse,
        SUM( srzzb1.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb1
        WHERE
        srzzb1.sszq &gt;= #{vo.sszqq} AND srzzb1.sszq &lt;= #{vo.sszqz} AND srzzb1.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb1.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb1.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb1.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb1.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb1.srlxdm = '140'
        GROUP BY
        srzzb1.djxh,
        srzzb1.sszq,
        srzzb1.srlxdm,
        srzzb1.nsrsbh,
        srzzb1.nsrmc,
        srzzb1.gsh_2,
        srzzb1.lrzx,
        srzzb1.jsfs_dm_1,
        srzzb1.zsxm_dm,
        srzzb1.sl_1)
        UNION ALL
        (SELECT
        srcybdhzb.uuid,
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srcybdhzb.nsrsbh,
        srcybdhzb.nsrmc,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1,
        srcybdhzb.zyjqtxssr AS xssr,
        srcybdhzb.xxse,
        srcybdhzb.xxskmse,
        srcybdhzb.cyse,
        srcybdhzb.hlwc,
        srcybdhzb.pzsl_2 pzsl,
        hlb.czrymc,
        hlb.lrrq czsj
        FROM
        znsb_tzzx_srcybdhzb srcybdhzb
        LEFT JOIN znsb_tzzx_cybdhlb hlb ON MD5(CONCAT(
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        srcybdhzb.nsrsbh,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1)) = hlb.md5 AND srcybdhzb.cyse = hlb.cyse
        WHERE
        srcybdhzb.sszq &gt;= #{vo.sszqq} AND srcybdhzb.sszq &lt;= #{vo.sszqz} AND srcybdhzb.djxh = #{vo.djxh}

        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srcybdhzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srcybdhzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srcybdhzb.sl1 = #{vo.sl1}
        </if>
        <if test="vo.lrzx != null and vo.lrzx != ''">
            AND srcybdhzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        )
        ) AS T
        ORDER BY sszq,abs(cyse) desc,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>
    <select id="queryZyqtAndStxsBySszq" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh,
               srlxdm,
               sum(xssr) as je,
               sum(xxse) as xxse,
               lrzx,
               sl1       as sl
        from ((
                  select ''                 uuid,
                         srzzb.djxh,
                         srzzb.sszq,
                         srzzb.srlxdm,
                         '视同销售收入'        as srlxmc,
                         srzzb.nsrsbh,
                         srzzb.nsrmc,
                         srzzb.gsh_2,
                         srzzb.lrzx,
                         srzzb.jsfs_dm_1,
                         srzzb.zsxm_dm,
                         srzzb.sl_1      as sl1,
                         SUM(srzzb.xssr) as xssr,
                         SUM(srzzb.xsse) as xxse,
                         SUM(srzzb.xsse) as xxskmse,
                         0               as cyse,
                         0               as hlwc,
                         1               as pzsl,
                         null               czrymc,
                         null               czsj
                  from znsb_tzzx_srzzb srzzb
                  where srzzb.sszq = #{sszq}
                    and srzzb.srlxdm = '130'
                  group by srzzb.uuid,
                           srzzb.djxh,
                           srzzb.sszq,
                           srzzb.srlxdm,
                           srzzb.nsrsbh,
                           srzzb.nsrmc,
                           srzzb.gsh_2,
                           srzzb.lrzx,
                           srzzb.jsfs_dm_1,
                           srzzb.zsxm_dm,
                           srzzb.sl_1)
              union all
              (
                  select srcybdhzb.uuid,
                         srcybdhzb.djxh,
                         srcybdhzb.sszq,
                         '180'               as srlxdm,
                         '主营及其他业务收入'         as srlxmc,
                         srcybdhzb.nsrsbh,
                         srcybdhzb.nsrmc,
                         srcybdhzb.gsh_2,
                         srcybdhzb.lrzx,
                         srcybdhzb.jsfs_dm_1,
                         srcybdhzb.zsxm_dm,
                         srcybdhzb.sl1,
                         srcybdhzb.zyjqtxssr as xssr,
                         srcybdhzb.xxse,
                         srcybdhzb.xxskmse,
                         srcybdhzb.cyse,
                         srcybdhzb.hlwc,
                         srcybdhzb.pzsl_2       pzsl,
                         hlb.czrymc,
                         hlb.lrrq               czsj
                  from znsb_tzzx_srcybdhzb srcybdhzb
                           left join znsb_tzzx_cybdhlb hlb on
                              MD5(CONCAT(srcybdhzb.djxh, srcybdhzb.sszq, srcybdhzb.nsrsbh, srcybdhzb.gsh_2,
                                         srcybdhzb.lrzx, srcybdhzb.jsfs_dm_1, srcybdhzb.zsxm_dm, srcybdhzb.sl1)) =
                              hlb.md5
                          and srcybdhzb.cyse = hlb.cyse
                  where srcybdhzb.sszq = #{sszq})) as T
        group by djxh,
                 sl1,
                 lrzx,
                 srlxdm
    </select>
    <select id="querySrzzhzbHjqs" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="java.util.Map">
        SELECT
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        1 as pzsl
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm in('130','140')
    </select>
    <select id="querySrzzhzbHjqszgsr" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="java.util.Map">
        SELECT
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        1 as pzsl
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '140'
    </select>

    <select id="querySrzzAndSrcybdhzbqsZzssjjtb" parameterType="com.css.znsb.gjss.pojo.vo.zzssjjtb.ZzssjjtbReqVO" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT * FROM (
        (SELECT '' uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        '视同销售收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq = #{vo.sszq}
        <if test="vo.nsrsbh != null and vo.nsrsbh != ''">
            AND srzzb.nsrsbh = #{vo.nsrsbh}
        </if>
        <if test="vo.nsrmc != null and vo.nsrmc != ''">
            AND srzzb.nsrmc like #{vo.nsrmc}
        </if>
        AND srzzb.srlxdm = '130'
        GROUP BY
        srzzb.uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.nsrmc,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1)
        UNION ALL
        (SELECT '' uuid,
        srzzb1.djxh,
        srzzb1.sszq,
        srzzb1.srlxdm,
        '暂估收入' as srlxmc,
        srzzb1.nsrsbh,
        srzzb1.nsrmc,
        srzzb1.gsh_2,
        srzzb1.lrzx,
        srzzb1.jsfs_dm_1,
        srzzb1.zsxm_dm,
        srzzb1.sl_1 AS sl1,
        SUM( srzzb1.xssr ) AS xssr,
        SUM( srzzb1.xsse ) AS xxse,
        SUM( srzzb1.xsse ) AS xxskmse,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb1
        WHERE
        srzzb1.sszq = #{vo.sszq}
        <if test="vo.nsrsbh != null and vo.nsrsbh != ''">
            AND srzzb1.nsrsbh = #{vo.nsrsbh}
        </if>
        <if test="vo.nsrmc != null and vo.nsrmc != ''">
            AND srzzb1.nsrmc like #{vo.nsrmc}
        </if>
        AND srzzb1.srlxdm = '140'
        GROUP BY
        srzzb1.djxh,
        srzzb1.sszq,
        srzzb1.srlxdm,
        srzzb1.nsrsbh,
        srzzb1.nsrmc,
        srzzb1.gsh_2,
        srzzb1.lrzx,
        srzzb1.jsfs_dm_1,
        srzzb1.zsxm_dm,
        srzzb1.sl_1)
        UNION ALL
        (SELECT
        srcybdhzb.uuid,
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srcybdhzb.nsrsbh,
        srcybdhzb.nsrmc,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1,
        srcybdhzb.zyjqtxssr AS xssr,
        srcybdhzb.xxse,
        srcybdhzb.xxskmse,
        srcybdhzb.cyse,
        srcybdhzb.hlwc,
        srcybdhzb.pzsl_2 pzsl,
        hlb.czrymc,
        hlb.lrrq czsj
        FROM
        znsb_tzzx_srcybdhzb srcybdhzb
        LEFT JOIN znsb_tzzx_cybdhlb hlb ON MD5(CONCAT(
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        srcybdhzb.nsrsbh,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1)) = hlb.md5 AND srcybdhzb.cyse = hlb.cyse
        WHERE
        srcybdhzb.sszq = #{vo.sszq}
        <if test="vo.nsrsbh != null and vo.nsrsbh != ''">
            AND srcybdhzb.nsrsbh = #{vo.nsrsbh}
        </if>
        <if test="vo.nsrmc != null and vo.nsrmc != ''">
            AND srcybdhzb.nsrmc like #{vo.nsrmc}
        </if>
        )
        ) AS T
        ORDER BY sszq,abs(cyse) desc,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>
    <select id="querySrcybdhzbdlzg" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT
        srcybdhzb.uuid,
        srcybdhzb.djxh,
        srcybdhzb.sszq,
        srcybdhzb.nsrsbh,
        srcybdhzb.nsrmc,
        srcybdhzb.gsh_2,
        srcybdhzb.lrzx,
        srcybdhzb.jsfs_dm_1,
        srcybdhzb.zsxm_dm,
        srcybdhzb.sl1,
        srcybdhzb.zyjqtxssr AS xssr,
        srcybdhzb.xxse,
        srcybdhzb.xxskmse,
        srcybdhzb.cyse,
        srcybdhzb.hlwc,
        srcybdhzb.ywqd_dm,
        srcybdhzb.pzsl_2 pzsl
        FROM
        znsb_tzzx_srcybdhzb srcybdhzb
        WHERE
        srcybdhzb.sszq &gt;= #{vo.sszqq} AND srcybdhzb.sszq &lt;= #{vo.sszqz}
        <if test = "vo.djxh != null and vo.djxh != ''" >
            AND srcybdhzb.djxh = #{vo.djxh}
        </if>
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srcybdhzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srcybdhzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srcybdhzb.sl1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srcybdhzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = 'vo.czcy != null and vo.czcy != "" and vo.czcy == "Y"' >
            AND abs(srcybdhzb.cyse) > 0
        </if>
        ORDER BY cyse,sszq,gsh_2,lrzx,sl1,jsfs_dm_1,zsxm_dm,ywqd_dm
    </select>
    <select id="querySrzzbwlj" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO">
        SELECT '' uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xssr ) AS xssr,
        SUM( srzzb.xsse ) AS xxse,
        SUM( srzzb.jsje ) AS jsje,
        0 as cyse,
        0 as hlwc,
        1 as pzsl,
        null czrymc,
        null czsj
        FROM
        znsb_tzzx_srzzb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        srzzb.uuid,
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1
    </select>

</mapper>
