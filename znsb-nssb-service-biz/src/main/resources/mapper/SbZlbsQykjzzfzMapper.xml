<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cwbb.SbZlbsQykjzzfzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cwbb.SbZlbsQykjzzfzDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="zlbscjuuid" column="zlbscjuuid" jdbcType="VARCHAR"/>
        <result property="bz11" column="bz_11" jdbcType="VARCHAR"/>
        <result property="bz12" column="bz_12" jdbcType="VARCHAR"/>
        <result property="bz13" column="bz_13" jdbcType="VARCHAR"/>
        <result property="bz14" column="bz_14" jdbcType="VARCHAR"/>
        <result property="bz15" column="bz_15" jdbcType="VARCHAR"/>
        <result property="bz16" column="bz_16" jdbcType="VARCHAR"/>
        <result property="bz17" column="bz_17" jdbcType="VARCHAR"/>
        <result property="bz18" column="bz_18" jdbcType="VARCHAR"/>
        <result property="kjzdfz" column="kjzdfz" jdbcType="VARCHAR"/>
        <result property="kjzdfz1" column="kjzdfz_1" jdbcType="VARCHAR"/>
        <result property="kjzdfz2" column="kjzdfz_2" jdbcType="VARCHAR"/>
        <result property="kjzdfz3" column="kjzdfz_3" jdbcType="VARCHAR"/>
        <result property="kjzdfz4" column="kjzdfz_4" jdbcType="VARCHAR"/>
        <result property="kjzdfz5" column="kjzdfz_5" jdbcType="VARCHAR"/>
        <result property="kjzdfz6" column="kjzdfz_6" jdbcType="VARCHAR"/>
        <result property="kjzdfz7" column="kjzdfz_7" jdbcType="VARCHAR"/>
        <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,zlbscjuuid,bz_11,
        bz_12,bz_13,bz_14,
        bz_15,bz_16,bz_17,
        bz_18,kjzdfz,kjzdfz_1,
        kjzdfz_2,kjzdfz_3,kjzdfz_4,
        kjzdfz_5,kjzdfz_6,kjzdfz_7,
        sjblbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
