<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cxtj.ldtssqxsetjb.ZnsbNssbLdtssqxsetjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cxtj.ldtssqxsetjb.ZnsbNssbLdtssqxsetjbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="tdhyxse" column="tdhyxse" jdbcType="DECIMAL"/>
            <result property="tqqbxse" column="tqqbxse" jdbcType="DECIMAL"/>
            <result property="zb" column="zb" jdbcType="DECIMAL"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="xybz" column="xybz" jdbcType="CHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sszq,nsrsbh,
        nsrmc,tdhyxse,tqqbxse,
        zb,yxbz,xybz,
        lrrsfid,xgrsfid,
        sjgsdq,sjcsdq,xgrq,
        lrrq
    </sql>
</mapper>
