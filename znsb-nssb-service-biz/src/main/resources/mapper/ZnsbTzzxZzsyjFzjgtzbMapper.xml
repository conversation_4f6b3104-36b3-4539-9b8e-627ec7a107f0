<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxZzsyjFzjgtzbMapper">
    <select id="getZzsyjFzjgtzHj" resultType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxZzsyjFzjgHj">
        select sum(bqfse) as bqfse
        from znsb_tzzx_zzsyj_fzjgtzb
        where 1 = 1
        <if test="znsbTzzxZzsyjFzjgtzbVO.djxh != null and znsbTzzxZzsyjFzjgtzbVO.djxh != ''">
            and djxh = #{znsbTzzxZzsyjFzjgtzbVO.djxh}
        </if>
        <if test="znsbTzzxZzsyjFzjgtzbVO.skssqq != null">
            and skssqq >= #{znsbTzzxZzsyjFzjgtzbVO.skssqq}
        </if>
        <if test="znsbTzzxZzsyjFzjgtzbVO.djxh != null">
            and skssqz &lt; DATE_ADD(#{znsbTzzxZzsyjFzjgtzbVO.skssqz}, INTERVAL 1 DAY)
        </if>
        <if test="znsbTzzxZzsyjFzjgtzbVO.nsrsbh != null and znsbTzzxZzsyjFzjgtzbVO.nsrsbh != ''">
            and nsrsbh like concat('%', #{znsbTzzxZzsyjFzjgtzbVO.nsrsbh}, '%')
        </if>
        <if test="znsbTzzxZzsyjFzjgtzbVO.nsrmc != null and znsbTzzxZzsyjFzjgtzbVO.nsrmc != ''">
            and nsrmc like concat('%', #{znsbTzzxZzsyjFzjgtzbVO.nsrmc}, '%')
        </if>
    </select>
</mapper>