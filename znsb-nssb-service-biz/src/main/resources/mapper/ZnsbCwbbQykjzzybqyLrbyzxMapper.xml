<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.yjzt.ZnsbCwbbQykjzzybqyLrbyzxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.yjtz.ZnsbCwbbQykjzzybqyLrbyzxDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="lrzxDm" column="lrzx_dm" jdbcType="VARCHAR"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="ssqq" column="ssqq" jdbcType="TIMESTAMP"/>
        <result property="ssqz" column="ssqz" jdbcType="TIMESTAMP"/>
        <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
        <result property="hmc" column="hmc" jdbcType="VARCHAR"/>
        <result property="bqje" column="bqje" jdbcType="DECIMAL"/>
        <result property="sqje1" column="sqje_1" jdbcType="DECIMAL"/>
        <result property="byfse" column="byfse" jdbcType="DECIMAL"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
        <result property="zlbscjuuid" column="zlbscjuuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,lrzx_dm,nsrsbh,
        ssqq,ssqz,ewbhxh,
        hmc,bqje,sqje_1,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,gsh_2,
        zlbscjuuid,byfse
    </sql>
</mapper>
