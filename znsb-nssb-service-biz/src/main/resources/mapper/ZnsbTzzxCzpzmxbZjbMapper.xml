<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxCzpzmxbZjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.fcs.ZnsbTzzxCzpzmxbZjbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
            <result property="gsh" column="gsh" jdbcType="VARCHAR"/>
            <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
            <result property="kmbm" column="kmbm" jdbcType="VARCHAR"/>
            <result property="kmmc" column="kmmc" jdbcType="VARCHAR"/>
            <result property="kjpzbh" column="kjpzbh" jdbcType="VARCHAR"/>
            <result property="pzrq" column="pzrq" jdbcType="TIMESTAMP"/>
            <result property="je" column="je" jdbcType="DECIMAL"/>
            <result property="ly" column="ly" jdbcType="CHAR"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="tbbz" column="tbbz" jdbcType="CHAR"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,nsrsbh,
        nsrmc,fybh,gsh,
        lrzx,kmbm,kmmc,
        kjpzbh,pzrq,je,
        ly,scbz,scsj_1,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,tbbz,
        tzlx_dm
    </sql>
</mapper>
