<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxJxsezczzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.jxsezc.ZnsbTzzxJxsezczz">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="jxsezcxmDm" column="jxsezcxm_dm" jdbcType="CHAR"/>
            <result property="zcse" column="zcse" jdbcType="DECIMAL"/>
            <result property="jzjtse" column="jzjtse" jdbcType="DECIMAL"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="ly" column="ly" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sszq,djxh,
        nsrsbh,nsrmc,jxsezcxm_dm,
        zcse,jzjtse,
        tzlx_dm,ly,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
