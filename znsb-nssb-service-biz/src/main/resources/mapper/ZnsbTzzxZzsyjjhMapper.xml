<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxZzsyjjhMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.zzsyj.ZnsbTzzxZzsyjjhDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="htbh" column="htbh" jdbcType="VARCHAR"/>
            <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
            <result property="sbny" column="sbny" jdbcType="CHAR"/>
            <result property="zlrqq" column="zlrqq" jdbcType="TIMESTAMP"/>
            <result property="zlrqz" column="zlrqz" jdbcType="TIMESTAMP"/>
            <result property="sbje" column="sbje" jdbcType="DECIMAL"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,htbh,fybh,
        sbny,zlrqq,zlrqz,
        sbje,scbz,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
