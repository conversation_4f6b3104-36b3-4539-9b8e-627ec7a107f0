<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxXxfpzzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxXxfpzzDO">
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="qysh" column="qysh" jdbcType="VARCHAR"/>
            <result property="qymc" column="qymc" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="zsfsDm" column="zsfs_dm" jdbcType="CHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="zpje" column="zpje" jdbcType="DECIMAL"/>
            <result property="zpse" column="zpse" jdbcType="DECIMAL"/>
            <result property="qtfpje" column="qtfpje" jdbcType="DECIMAL"/>
            <result property="qtfpse" column="qtfpse" jdbcType="DECIMAL"/>
            <result property="wkpje" column="wkpje" jdbcType="DECIMAL"/>
            <result property="wkpse" column="wkpse" jdbcType="DECIMAL"/>
            <result property="nsjctzje" column="nsjctzje" jdbcType="DECIMAL"/>
            <result property="nsjctzse" column="nsjctzse" jdbcType="DECIMAL"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
            <result property="scbz" column="scbz" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,qysh,qymc,
        sszq,zsfs_dm,zsxm_dm,
        sl_1,zpje,zpse,
        qtfpje,qtfpse,wkpje,
        wkpse,nsjctzje,nsjctzse,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,uuid,scsj_1,scbz
    </sql>
    <update id="updateXxfpZzByXxfpmx" parameterType="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxXxfpzzDO">
        update znsb_tzzx_xxfpzz set
            <if test="entity.xgrsfid != null and entity.xgrsfid != ''">
                xgrsfid = #{entity.xgrsfid},
            </if>
            <if test="entity.zpje != null">
                zpje = #{entity.zpje},
            </if>
            <if test="entity.zpje == null">
                zpje = null,
            </if>
            <if test="entity.zpse != null">
                zpse = #{entity.zpse},
            </if>
            <if test="entity.zpse == null">
                zpse = null,
            </if>
            xgrq = #{entity.xgrq}
        where uuid = #{entity.uuid};
    </update>
    <select id="queryXxfpZz" resultType="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxXxfpzzDO">
        select <include refid="Base_Column_List"/> from znsb_tzzx_xxfpzz where djxh = #{data.djxh} and sszq = #{data.sszq};
    </select>
</mapper>
