<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.sshz.ZnsbTzzxLqsshzDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="returncode" column="returncode" jdbcType="VARCHAR"/>
            <result property="ycyy" column="ycyy" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="skssq" column="skssq" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,returncode,ycyy,
        djxh,nsrsbh,skssq,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
