<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhscjMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.yhs.ZnsbNssbYhscjDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
        <result property="ynspzsllsrq" column="ynspzsllsrq" jdbcType="TIMESTAMP"/>
        <result property="yspzmc" column="yspzmc" jdbcType="VARCHAR"/>
        <result property="sbqxlx" column="sbqxlx" jdbcType="CHAR"/>
        <result property="yspzsl" column="yspzsl" jdbcType="BIGINT"/>
        <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
        <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
        <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
        <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
        <result property="jsjehjs" column="jsjehjs" jdbcType="DECIMAL"/>
        <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
        <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
        <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
        <result property="jmse" column="jmse" jdbcType="DECIMAL"/>
        <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
        <result property="nsqxDm" column="nsqx_dm" jdbcType="CHAR"/>
        <result property="dfslrmc" column="dfslrmc" jdbcType="VARCHAR"/>
        <result property="dfslrsjje" column="dfslrsjje" jdbcType="DECIMAL"/>
        <result property="dfslrnsrsbh" column="dfslrnsrsbh" jdbcType="VARCHAR"/>
        <result property="gzbz" column="gzbz" jdbcType="CHAR"/>
        <result property="sfsfzrd" column="sfsfzrd" jdbcType="CHAR"/>
        <result property="version" column="version" jdbcType="DECIMAL"/>
        <result property="sfksbqxlxbz" column="sfksbqxlxbz" jdbcType="CHAR"/>
        <result property="sfdeletebz" column="sfdeletebz" jdbcType="CHAR"/>
        <result property="yspzswbh" column="yspzswbh" jdbcType="VARCHAR"/>
        <result property="ynspzbh" column="ynspzbh" jdbcType="VARCHAR"/>
        <result property="hdbl" column="hdbl" jdbcType="DECIMAL"/>
        <result property="hdlx2" column="hdlx_2" jdbcType="CHAR"/>
        <result property="bh" column="bh" jdbcType="VARCHAR"/>
        <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
        <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
        <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
        <result property="sjjsrq" column="sjjsrq" jdbcType="TIMESTAMP"/>
        <result property="sjjsje" column="sjjsje" jdbcType="DECIMAL"/>
        <result property="ysbbz" column="ysbbz" jdbcType="CHAR"/>
        <result property="sbsxdm1" column="sbsxdm1" jdbcType="CHAR"/>
        <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="bczt" column="bczt" jdbcType="CHAR"/>
        <result property="hxbuuid" column="hxbuuid" jdbcType="VARCHAR"/>
        <result property="ybczt" column="ybczt" jdbcType="CHAR"/>
        <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
        <result property="zzuuid" column="zzuuid" jdbcType="VARCHAR"/>
        <result property="hxbmxuuid" column="hxbmxuuid" jdbcType="VARCHAR"/>
        <result property="yjsjehjs" column="yjsjehjs" jdbcType="DECIMAL"/>
        <result property="bgzt" column="bgzt" jdbcType="CHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="jyssbz" column="jyssbz" jdbcType="CHAR"/>
        <result property="ssrq1" column="ssrq_1" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,ynspzsllsrq,
        yspzmc,sbqxlx,yspzsl,
        zspm_dm,zszm_dm,skssqq,
        skssqz,jsjehjs,sl_1,
        ynse,ssjmxz_dm,jmse,
        ybtse,nsqx_dm,dfslrmc,
        dfslrsjje,dfslrnsrsbh,gzbz,
        sfsfzrd,version,sfksbqxlxbz,
        sfdeletebz,yspzswbh,ynspzbh,
        hdbl,hdlx_2,bh,
        zfr_dm,zfrq_1,zfbz_1,
        sjjsrq,sjjsje,ysbbz,
        sbsxdm1,yjse,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj,bczt,hxbuuid,
        ybczt,pclsh,zzuuid,
        hxbmxuuid,yjsjehjs,bgzt,
        djxh,jyssbz,ssrq_1
    </sql>

    <!-- 查询本地保存的立法后印花税税源信息  -->
    <select id="queryYhslfYcjmxxx" resultType="com.css.znsb.nssb.pojo.vo.cchxwssb.yhssycj.YhshbcjxxbVO">
        <if test='nsqxDmList != null and nsqxDmList.size() >0 and nsqxDmSbrw !="11"'>
            SELECT scy.*,scc.sbsx_dm_1,'new' version,scc.cwxx FROM znsb_nssb_cxsbtxx scc ,znsb_nssb_yhscj scy
            WHERE scc.UUID =scy.ZBUUID
            AND COALESCE(scc.ZFBZ_1,'N') = 'N'
            AND COALESCE(scy.ZFBZ_1,'N') = 'N'
            AND scy.NSQX_DM IN
            <foreach collection="nsqxDmList" item="nsqxdm" open="(" separator="," close=")">
                #{nsqxdm}
            </foreach>
            AND scc.djxh=#{djxh}
            AND scc.SKSSQQ &gt;=STR_TO_DATE(#{skssqq},'%Y-%m-%d')
            AND scc.SKSSQZ &lt;= STR_TO_DATE(#{skssqz},'%Y-%m-%d')
            <if test="syuuidList != null and syuuidList.size() > 0">
                AND (scc.hxbuuid IN
                <foreach collection="syuuidList" item="syuuid" open="(" separator="," close=")">
                    #{syuuid}
                </foreach>
                OR scc.UUID IN
                <foreach collection="syuuidList" item="syuuid" open="(" separator="," close=")">
                    #{syuuid}
                </foreach>)
            </if>
            <if test="syuuidList == null or syuuidList.size() == 0">
                AND scc.BCZT != '50'
                AND scc.YSBBZ ='N'
            </if>
        </if>
        <if test='nsqxDmSbrw == "11"'>
            SELECT scy.*,scc.sbsx_dm_1,'new' version,scc.cwxx FROM znsb_nssb_cxsbtxx scc ,znsb_nssb_yhscj scy
            WHERE scc.UUID =scy.ZBUUID
            AND COALESCE(scc.ZFBZ_1,'N') = 'N'
            AND COALESCE(scy.ZFBZ_1,'N') = 'N'
            AND scy.NSQX_DM ='11'
            AND scc.djxh=#{djxh}
            AND scc.SKSSQZ &lt;= STR_TO_DATE(#{acskssqq},'%Y-%m-%d')
            <if test="syuuidList != null and syuuidList.size() > 0">
                AND (scc.hxbuuid IN
                <foreach collection="syuuidList" item="syuuid" open="(" separator="," close=")">
                    #{syuuid}
                </foreach>
                OR scc.UUID IN
                <foreach collection="syuuidList" item="syuuid" open="(" separator="," close=")">
                    #{syuuid}
                </foreach>)
            </if>
            <if test="syuuidList == null or syuuidList.size() == 0">
                AND scc.BCZT != '50'
                AND scc.YSBBZ ='N'
            </if>
        </if>
    </select>

    <!-- 查询本地保存的印花税立法可变列采集表信息  -->
    <select id="queryYhskbl" resultType="com.css.znsb.nssb.pojo.vo.cchxwssb.yhssycj.YhscjkblxxVO">
        SELECT kbl.ZBUUID ,kbl.UUID ,kbl.SYUUID ,kbl.SJJSJE ,STR_TO_DATE(kbl.SJJSRQ,'%Y-%m-%d')SJJSRQ,kbl.DFSLRMC
        ,kbl.DFSLRNSRSBH ,kbl.DFSLRSJJE FROM znsb_nssb_cxsbtxx scc ,znsb_nssb_yhslfkbl kbl
        WHERE scc.UUID =kbl.ZBUUID
        AND scc.ZFBZ_1 ='N'
        AND kbl.ZFBZ_1 ='N'
        AND kbl.zbuuid in
        <foreach collection="yhshbcjxxbList" item="item" index="index" open="(" separator="," close=")">
            #{item.zbuuid}
        </foreach>
    </select>

    <!-- 根据djxh skssqq skssqz查询立法后已经申报税源  -->
    <select id="queryYhslfysbsymx" resultType="com.css.znsb.nssb.pojo.vo.cchxwssb.yhssycj.YhshbcjxxbVO">
        SELECT scy.*,scc.sbsx_dm_1,'new' version,scc.ysbbz FROM  znsb_nssb_cxsbtxx scc ,znsb_nssb_yhscj scy
        WHERE scc.UUID =scy.ZBUUID
          AND COALESCE(scc.ZFBZ_1,'N') = 'N'
          AND scc.YSBBZ ='Y'
          AND COALESCE(scy.ZFBZ_1,'N') = 'N'
          AND scc.djxh=#{djxh}
          AND scc.SKSSQQ &gt;=STR_TO_DATE(#{skssqq},'%Y-%m-%d')
          AND scc.SKSSQZ &lt;=STR_TO_DATE(#{skssqz},'%Y-%m-%d')
    </select>

    <update id="zfsycjxx">
        update znsb_nssb_yhscj
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="zfbz_1 =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.zfbz1!=null">
                        when uuid=#{i.uuid} then #{i.zfbz1}
                    </if>
                </foreach>
            </trim>
            <trim prefix="zfr_dm =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.zfrDm!=null">
                        when uuid=#{i.uuid} then #{i.zfrDm}
                    </if>
                </foreach>
            </trim>
            <trim prefix="zfrq_1 =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.zfrq1!=null">
                        when uuid=#{i.uuid} then #{i.zfrq1}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="i" index="index" >
            uuid=#{i.uuid}
        </foreach>
    </update>

    <update id="updateYhslfcjxx" parameterType="list">
        update znsb_nssb_yhscj
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="jsjehjs=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.jsjehjs!=null">
                        when uuid=#{i.uuid} then #{i.jsjehjs}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ynse=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.ynse!=null">
                        when uuid=#{i.uuid} then #{i.ynse}
                    </if>
                </foreach>
            </trim>

            <trim prefix="ssjmxz_dm=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.ssjmxzDm!=null">
                        when uuid=#{i.uuid} then #{i.ssjmxzDm}
                    </if>
                </foreach>
            </trim>
            <trim prefix="jmse=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.jmse!=null">
                        when uuid=#{i.uuid} then #{i.jmse}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ybtse=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.ybtse!=null">
                        when uuid=#{i.uuid} then #{i.ybtse}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yspzmc=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.yspzmc!=null">
                        when uuid=#{i.uuid} then #{i.yspzmc}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yspzsl=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.yspzsl!=null">
                        when uuid=#{i.uuid} then #{i.yspzsl}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="i" index="index">
            uuid=#{i.uuid}
        </foreach>
    </update>
    <update id="updateBcztByPclsh">
        update znsb_nssb_yhscj
        set bczt=#{bczt},
            hxbuuid=#{hxbuuid},
            xgrsfid='lqjkhd',
            xgrq=now()
        where pclsh = #{pclsh}
          and (hxbuuid is null or hxbuuid = '')
    </update>
    <update id="zfYhssycjxx">
        update znsb_nssb_yhscj
        set zfbz_1 = 'Y',
        zfrq_1 = now(),
        xgrsfid='qyd',
        xgrq=now(),
        bczt = '30'
        where
        <foreach collection="list" separator="or" item="i" index="index">
            uuid=#{i.uuid}
        </foreach>
    </update>

    <select id="queryyhsycjxxbyuuid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM znsb_nssb_yhscj
        WHERE ZBUUID = #{uuid} and COALESCE(zfbz_1, 'N') = 'N'
    </select>

    <select id="queryYsbsymxNew" resultType="com.css.znsb.nssb.pojo.vo.cchxwssb.yhssycj.YhshbcjxxbVO">
        SELECT scy.*,scc.sbsx_dm_1,scc.ysbbz,'new' version
        FROM znsb_nssb_cxsbtxx scc ,znsb_nssb_yhscj scy
        WHERE scc.UUID =scy.ZBUUID
        AND COALESCE(scc.ZFBZ_1,'N') = 'N'
        AND scc.YSBBZ ='Y'
        AND COALESCE(scy.ZFBZ_1,'N') = 'N'
        AND scc.uuid in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.syuuid}
        </foreach>
    </select>

    <select id="queryYsbsymxOld" resultType="com.css.znsb.nssb.pojo.vo.cchxwssb.yhssycj.YhscjCjxxbVO">
        SELECT scy.*,scc.SBSX_DM_1 ,scc.ysbbz,CASE WHEN scy.NSQX_DM ='11' THEN '01' ELSE '00' END sbqxlx,'old' version
        FROM znsb_nssb_cxsbtxx scc ,znsb_nssb_yhscj scy
        WHERE scc.UUID =scy.ZBUUID
        AND scc.ZFBZ_1 ='N'
        AND scc.YSBBZ ='Y'
        AND scy.ZFBZ_1 ='N'
        AND scc.uuid in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.syuuid}
        </foreach>
    </select>

    <select id="queryOldIdByHxbuuid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM znsb_nssb_yhscj
        WHERE (zbuuid = #{hxbuuid} or hxbuuid = #{hxbuuid})
        AND COALESCE(zfbz_1, 'N') = 'N'
        AND bczt = '30'
    </select>


    <select id="queryYhscjxx" resultType="com.css.znsb.nssb.pojo.vo.cchxwssb.yhssycj.YhshbcjxxbVO">
        SELECT scy.*
        FROM znsb_nssb_cxsbtxx scc ,znsb_nssb_yhscj scy
        WHERE scc.UUID =scy.ZBUUID
          AND COALESCE(scc.ZFBZ_1,'N') = 'N'
          AND COALESCE(scy.ZFBZ_1,'N') = 'N'
          AND scc.yzpzzl_dm = #{yzpzzlDm}
          AND scc.djxh = #{djxh}
          AND scy.skssqq = #{skssqq}
          AND scy.skssqz = #{skssqz}
          AND scy.zspm_dm = #{zspmDm}
          AND scy.zszm_dm = #{zszmDm}
    </select>

    <delete id="deleteYshjyssj">
        DELETE FROM znsb_nssb_yhscj
        WHERE zbuuid in (select uuid from znsb_nssb.znsb_nssb_cxsbtxx where djxh = #{djxh});
    </delete>
</mapper>
