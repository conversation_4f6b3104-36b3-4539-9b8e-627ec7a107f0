<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.gjss.mapper.srzz.jyk.ZnsbTzzxJzjtxxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxJzjtxxbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
            <result property="jsfsDm1" column="jsfs_dm_1" jdbcType="CHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="xxje" column="xxje" jdbcType="DECIMAL"/>
            <result property="xxse" column="xxse" jdbcType="DECIMAL"/>
            <result property="sjkce" column="sjkce" jdbcType="DECIMAL"/>
            <result property="jxse" column="jxse" jdbcType="DECIMAL"/>
            <result property="sjtse" column="sjtse" jdbcType="DECIMAL"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="ly" column="ly" jdbcType="CHAR"/>
            <result property="tbbz" column="tbbz" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="zzscbz" column="zzscbz" jdbcType="CHAR"/>
            <result property="jyssbz" column="jyssbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="srlxDm" column="srlxdm" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sszq,djxh,
        nsrsbh,nsrmc,gsh_2,
        jsfs_dm_1,zsxm_dm,sl_1,
        xxje,xxse,sjkce,
        jxse,sjtse,tzlx_dm,
        ly,tbbz,yxbz,
        zzscbz,jyssbz,ywqd_dm,
        lrrq,lrrsfid,xgrq,
        xgrsfid,sjcsdq,sjgsdq,
        sjtb_sj,srlxdm
    </sql>
</mapper>
