<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbNcphdkchzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbNcphdkchzSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="scjyBz" column="scjy_bz" jdbcType="VARCHAR"/>
            <result property="scjyJxse" column="scjy_jxse" jdbcType="DECIMAL"/>
            <result property="zjxsBz" column="zjxs_bz" jdbcType="VARCHAR"/>
            <result property="zjxsJxse" column="zjxs_jxse" jdbcType="DECIMAL"/>
            <result property="schwCbfBz" column="schw_cbf_bz" jdbcType="VARCHAR"/>
            <result property="schwCbfJxse" column="schw_cbf_jxse" jdbcType="DECIMAL"/>
            <result property="schwTrccfBz" column="schw_trccf_bz" jdbcType="VARCHAR"/>
            <result property="schwTrccfJxse" column="schw_trccf_jxse" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        scjy_bz,scjy_jxse,zjxs_bz,
        zjxs_jxse,schw_cbf_bz,schw_cbf_jxse,
        schw_trccf_bz,schw_trccf_jxse,sjblbz,
        lrrq,xgrq,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq,
        sjtb_sj,sjgsdq
    </sql>
</mapper>
