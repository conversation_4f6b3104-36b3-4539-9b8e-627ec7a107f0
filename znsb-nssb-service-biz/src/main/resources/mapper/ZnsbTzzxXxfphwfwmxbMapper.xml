<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxXxfphwfwmxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.xxfp.ZnsbTzzxXxfphwfwmxbDO">
            <id property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="fppzDm" column="fppz_dm" jdbcType="CHAR"/>
            <result property="fpdm" column="fpdm" jdbcType="VARCHAR"/>
            <result property="fpdmhm" column="fpdmhm" jdbcType="VARCHAR"/>
            <result property="fphm" column="fphm" jdbcType="VARCHAR"/>
            <result property="kprq" column="kprq" jdbcType="TIMESTAMP"/>
            <result property="gmfnsrsbh" column="gmfnsrsbh" jdbcType="VARCHAR"/>
            <result property="gmfnsrmc" column="gmfnsrmc" jdbcType="VARCHAR"/>
            <result property="hwhyslwfwmc" column="hwhyslwfwmc" jdbcType="VARCHAR"/>
            <result property="sphfwssflhbbm" column="sphfwssflhbbm" jdbcType="CHAR"/>
            <result property="ggxh" column="ggxh" jdbcType="VARCHAR"/>
            <result property="fpspsl" column="fpspsl" jdbcType="VARCHAR"/>
            <result property="fpspdj" column="fpspdj" jdbcType="VARCHAR"/>
            <result property="dw" column="dw" jdbcType="VARCHAR"/>
            <result property="je" column="je" jdbcType="DECIMAL"/>
            <result property="se" column="se" jdbcType="DECIMAL"/>
            <result property="jshj" column="jshj" jdbcType="DECIMAL"/>
            <result property="kce" column="kce" jdbcType="DECIMAL"/>
            <result property="zsfsDm" column="zsfs_dm_1" jdbcType="VARCHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="jzjtbz" column="jzjtbz" jdbcType="CHAR"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,uuid,zbuuid,
        sszq,nsrsbh,nsrmc,
        fppz_dm,fpdm,fpdmhm,
        fphm,kprq,gmfnsrsbh,
        gmfnsrmc,hwhyslwfwmc,sphfwssflhbbm,
        ggxh,fpspsl,fpspdj,
        dw,je,se,
        jshj,kce,zsfs_dm_1,
        zsxm_dm,jzjtbz,tzlx_dm,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,sl_1
    </sql>
    <update id="updateJzjtbz">
        update znsb_tzzx_xxfphwfwmxb set jzjtbz = #{data.jzjtbz},tzlx_dm = '4', xgrq = #{data.xgrq} where uuid = #{data.uuid} and djxh = #{data.djxh}
    </update>
    <select id="queryCount" resultType="java.lang.Long">
        select count(1) from znsb_tzzx_xxfphwfwmxb where  djxh = #{data.djxh} and sszq = #{data.sszq} and tzlx_Dm in ('1','4');
    </select>
    <select id="queryTzCount" resultType="java.lang.Integer">
        select count(1) from znsb_tzzx_xxfphwfwmxb where  djxh = #{data.djxh} and sszq = #{data.sszq};
    </select>
    <update id="updateTzlx1to2">
        update znsb_tzzx_xxfphwfwmxb set tzlx_dm = '2', xgrq = #{data.xgrq} where uuid = #{data.uuid} and djxh = #{data.djxh} and tzlx_dm='1';
    </update>
    <update id="updateTzlx4to2">
        update znsb_tzzx_xxfphwfwmxb set tzlx_dm = '2', xgrq = #{data.xgrq} where uuid = #{data.uuid} and djxh = #{data.djxh} and tzlx_dm ='4';
    </update>
    <select id="queryAllHwmxByFphm" resultType="com.css.znsb.tzzx.pojo.domain.tzzx.xxfp.ZnsbTzzxXxfphwfwmxbDO">
        select <include refid="Base_Column_List"/> from znsb_tzzx_xxfphwfwmxb where fphm =#{data.fphm} <if test="data.fpdm != null"> and fpdm = #{data.fpdm}</if> and fpdmhm = #{data.fpdmhm}
            and  djxh = #{data.djxh} and sszq = #{data.sszq} and tzlx_dm in ('1','4');
    </select>
</mapper>
