<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxSrcybdmxMapper">
    <select id="querySrcybdmxHj" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrcybdmxQueryVO" resultType="java.util.Map">
        SELECT sum(cyse) as cyse,sum(zyjqtxssr) as xssr,sum(xxse) as xxse,sum(xxskmse) as xxskmse FROM znsb_tzzx_srcybdmx where djxh = #{vo.djxh} and ly = '0'
        <if test = "vo.sszq != null and vo.sszq != ''" >
            AND sszq = #{vo.sszq}
        </if>
        <if test = "vo.sszqq != null and vo.sszqq != ''" >
            AND sszq &gt;= #{vo.sszqq}
        </if>
        <if test = "vo.sszqz != null and vo.sszqz != ''" >
            AND sszq &lt;= #{vo.sszqz}
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND sl1 = #{vo.sl1}
        </if>
        <if test = "vo.ckpzh != null and vo.ckpzh != ''" >
            AND ckpzh like CONCAT('%', #{vo.ckpzh}, '%')
        </if>
        <if test = "vo.nsrsbh != null and vo.nsrsbh != ''" >
            AND nsrsbh = #{vo.nsrsbh}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND lrzx = #{vo.lrzx}
        </if>
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND jsfs_dm_1 = #{vo.jsfsDm1}
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND zsxm_dm = #{vo.zsxmDm1}
        </if>
        <if test = 'vo.ccyz != null and vo.ccyz != "" and vo.ccyz == "Y"' >
            AND abs(cyse/pzsl_2) &gt; hlwc
        </if>
    </select>
</mapper>