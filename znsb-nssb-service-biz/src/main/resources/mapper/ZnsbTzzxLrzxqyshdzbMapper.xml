<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.lrzxqyshdz.ZnsbTzzxLrzxqyshdzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.lrzxqyshdz.ZnsbTzzxLrzxqyshdzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="lrzxDm" column="lrzx_dm" jdbcType="VARCHAR"/>
            <result property="lrzxMc" column="lrzx_mc" jdbcType="VARCHAR"/>
            <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
            <result property="sfzzshzsb" column="sfzzshzsb" jdbcType="CHAR"/>
            <result property="bhznsqynsrsbh" column="bhznsqynsrsbh" jdbcType="VARCHAR"/>
            <result property="bz" column="bz" jdbcType="VARCHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,nsrsbh,nsrmc,
        lrzx_dm,lrzx_mc,gsh_2,
        sfzzshzsb,bhznsqynsrsbh,bz,
        yxbz,ywqd_dm,lrrq,
        lrrsfid,xgrq,xgrsfid,
        sjcsdq,sjgsdq,sjtb_sj
    </sql>
</mapper>
