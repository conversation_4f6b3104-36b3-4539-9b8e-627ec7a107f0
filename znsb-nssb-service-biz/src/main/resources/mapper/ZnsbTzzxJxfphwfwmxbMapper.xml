<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxJxfphwfwmxbMapper">
    <select id="queryXkjshj" resultType="com.css.znsb.tzzx.pojo.domain.tzzx.jxfp.ZnsbTzzxJxfphwfwmxbDO">
        SELECT sum(jshj) jshj from znsb_tzzx_jxfphwfwmxb
        where nsrsbh = #{nsrsbh}
        <if test = "sszqq != null and sszqq != ''" >
            AND sszq &gt;= #{sszqq}
        </if>
        <if test = "sszqz != null and sszqz != ''" >
            AND sszq &lt;= #{sszqz}
        </if>
        and ( (sphfwssflhbbm = '3040201050000000000' and jshj % 140 = 0 )
        or (sphfwssflhbbm = '3040201030000000000' and jshj % 140 = 0 )
        or (sphfwssflhbbm = '1090513070000000000' and hwhyslwfwmc like '%报税盘%' and jshj % 60 = 0 )
        or (sphfwssflhbbm = '1090513070000000000' and hwhyslwfwmc like '%税控盘%' and jshj % 160 = 0 )
        )
    </select>
</mapper>