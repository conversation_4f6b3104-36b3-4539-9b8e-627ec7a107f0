<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.srhy.ZnsbNssbSrhyhzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.srhy.ZnsbNssbSrhyhzbDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
        <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
        <result property="ssny" column="ssny" jdbcType="INTEGER"/>
        <result property="yysr" column="yysr" jdbcType="DECIMAL"/>
        <result property="zyywsr" column="zyywsr" jdbcType="DECIMAL"/>
        <result property="zyyssr" column="zyyssr" jdbcType="DECIMAL"/>
        <result property="zycpxs" column="zycpxs" jdbcType="DECIMAL"/>
        <result property="zycpxs1" column="zycpxs1" jdbcType="DECIMAL"/>
        <result property="zycckd" column="zycckd" jdbcType="DECIMAL"/>
        <result property="zyspfw" column="zyspfw" jdbcType="DECIMAL"/>
        <result property="zyjwfy" column="zyjwfy" jdbcType="DECIMAL"/>
        <result property="zyfyssr" column="zyfyssr" jdbcType="DECIMAL"/>
        <result property="zyqhth" column="zyqhth" jdbcType="DECIMAL"/>
        <result property="zyyjfl" column="zyyjfl" jdbcType="DECIMAL"/>
        <result property="zyhyjf" column="zyhyjf" jdbcType="DECIMAL"/>
        <result property="zysrce" column="zysrce" jdbcType="DECIMAL"/>
        <result property="zymssr" column="zymssr" jdbcType="DECIMAL"/>
        <result property="qtywsr" column="qtywsr" jdbcType="DECIMAL"/>
        <result property="qtgf" column="qtgf" jdbcType="DECIMAL"/>
        <result property="qtsbzj" column="qtsbzj" jdbcType="DECIMAL"/>
        <result property="qtkmjbyf" column="qtkmjbyf" jdbcType="DECIMAL"/>
        <result property="qtdswl" column="qtdswl" jdbcType="DECIMAL"/>
        <result property="qtzlsr" column="qtzlsr" jdbcType="DECIMAL"/>
        <result property="qtdswl1" column="qtdswl1" jdbcType="DECIMAL"/>
        <result property="qtppsq" column="qtppsq" jdbcType="DECIMAL"/>
        <result property="qtccfw" column="qtccfw" jdbcType="DECIMAL"/>
        <result property="qto2ofw" column="qto2ofw" jdbcType="DECIMAL"/>
        <result property="qtgffwf" column="qtgffwf" jdbcType="DECIMAL"/>
        <result property="qtzlsr1" column="qtzlsr1" jdbcType="DECIMAL"/>
        <result property="qtspyqzj" column="qtspyqzj" jdbcType="DECIMAL"/>
        <result property="qtzgfwf" column="qtzgfwf" jdbcType="DECIMAL"/>
        <result property="qtms" column="qtms" jdbcType="DECIMAL"/>
        <result property="qtsrce" column="qtsrce" jdbcType="DECIMAL"/>
        <result property="qtsaptz" column="qtsaptz" jdbcType="DECIMAL"/>
        <result property="qtsaptz1" column="qtsaptz1" jdbcType="DECIMAL"/>
        <result property="qtsaptz2" column="qtsaptz2" jdbcType="DECIMAL"/>
        <result property="qtsaptz3" column="qtsaptz3" jdbcType="DECIMAL"/>
        <result property="qtsaptz4" column="qtsaptz4" jdbcType="DECIMAL"/>
        <result property="zzsyssr" column="zzsyssr" jdbcType="DECIMAL"/>
        <result property="zzsysybjssr" column="zzsysybjssr" jdbcType="DECIMAL"/>
        <result property="zzsyszyywsr" column="zzsyszyywsr" jdbcType="DECIMAL"/>
        <result property="zzsyszycpxs" column="zzsyszycpxs" jdbcType="DECIMAL"/>
        <result property="zzsyszyxssr" column="zzsyszyxssr" jdbcType="DECIMAL"/>
        <result property="zzsyszyxszkzr" column="zzsyszyxszkzr" jdbcType="DECIMAL"/>
        <result property="zzsyszyqjfykd" column="zzsyszyqjfykd" jdbcType="DECIMAL"/>
        <result property="zzsyszyjwfy" column="zzsyszyjwfy" jdbcType="DECIMAL"/>
        <result property="zzsyszycpxs1" column="zzsyszycpxs1" jdbcType="DECIMAL"/>
        <result property="zzsyszycckd" column="zzsyszycckd" jdbcType="DECIMAL"/>
        <result property="zzsyszyspfw" column="zzsyszyspfw" jdbcType="DECIMAL"/>
        <result property="zzsqtywsr" column="zzsqtywsr" jdbcType="DECIMAL"/>
        <result property="zzsqtgf" column="zzsqtgf" jdbcType="DECIMAL"/>
        <result property="zzsqtsbzj" column="zzsqtsbzj" jdbcType="DECIMAL"/>
        <result property="zzsqtkmjbyf" column="zzsqtkmjbyf" jdbcType="DECIMAL"/>
        <result property="zzsqtdswl" column="zzsqtdswl" jdbcType="DECIMAL"/>
        <result property="zzsqtzlsr" column="zzsqtzlsr" jdbcType="DECIMAL"/>
        <result property="zzsqtdswl1" column="zzsqtdswl1" jdbcType="DECIMAL"/>
        <result property="zzsqtppsq" column="zzsqtppsq" jdbcType="DECIMAL"/>
        <result property="zzsqtccfw" column="zzsqtccfw" jdbcType="DECIMAL"/>
        <result property="zzsqto2ofw" column="zzsqto2ofw" jdbcType="DECIMAL"/>
        <result property="zzsqtgf1" column="zzsqtgf1" jdbcType="DECIMAL"/>
        <result property="zzsqtcyh" column="zzsqtcyh" jdbcType="DECIMAL"/>
        <result property="zzsqtcyh1" column="zzsqtcyh1" jdbcType="DECIMAL"/>
        <result property="zzsqtcyh2" column="zzsqtcyh2" jdbcType="DECIMAL"/>
        <result property="zzsqtcyh3" column="zzsqtcyh3" jdbcType="DECIMAL"/>
        <result property="zzsstxssr" column="zzsstxssr" jdbcType="DECIMAL"/>
        <result property="zzsstxssrjmslx" column="zzsstxssrjmslx" jdbcType="DECIMAL"/>
        <result property="zzsstxssrzydf" column="zzsstxssrzydf" jdbcType="DECIMAL"/>
        <result property="zzsstxssrjzsr" column="zzsstxssrjzsr" jdbcType="DECIMAL"/>
        <result property="zzsstxssrdxst" column="zzsstxssrdxst" jdbcType="DECIMAL"/>
        <result property="zzsstxssrwyjst" column="zzsstxssrwyjst" jdbcType="DECIMAL"/>
        <result property="zzsstxssrwyjst6" column="zzsstxssrwyjst6" jdbcType="DECIMAL"/>
        <result property="zzsstxssrwyjst5" column="zzsstxssrwyjst5" jdbcType="DECIMAL"/>
        <result property="zzsstxssrsfsr" column="zzsstxssrsfsr" jdbcType="DECIMAL"/>
        <result property="zzsstxssrbdyqzj" column="zzsstxssrbdyqzj" jdbcType="DECIMAL"/>
        <result property="zzsstxssrdklx" column="zzsstxssrdklx" jdbcType="DECIMAL"/>
        <result property="zzsstxssrbblc" column="zzsstxssrbblc" jdbcType="DECIMAL"/>
        <result property="zzsstxssrwyf" column="zzsstxssrwyf" jdbcType="DECIMAL"/>
        <result property="zzsstxssrgssxffh" column="zzsstxssrgssxffh" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlstscj13" column="zzsstxssrlstscj13" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlstscj9" column="zzsstxssrlstscj9" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlstscj6" column="zzsstxssrlstscj6" jdbcType="DECIMAL"/>
        <result property="zzsstxssrcyh" column="zzsstxssrcyh" jdbcType="DECIMAL"/>
        <result property="zzsstxssrcyh1" column="zzsstxssrcyh1" jdbcType="DECIMAL"/>
        <result property="zzsstxssrcyh2" column="zzsstxssrcyh2" jdbcType="DECIMAL"/>
        <result property="zzsstxssrjyjssr" column="zzsstxssrjyjssr" jdbcType="DECIMAL"/>
        <result property="zzsstxssrqtywsr" column="zzsstxssrqtywsr" jdbcType="DECIMAL"/>
        <result property="zzsstxssrzlsr" column="zzsstxssrzlsr" jdbcType="DECIMAL"/>
        <result property="zzsstxssrstxssr" column="zzsstxssrstxssr" jdbcType="DECIMAL"/>
        <result property="zzsstxssrbdspyqzj" column="zzsstxssrbdspyqzj" jdbcType="DECIMAL"/>
        <result property="zzsstxssrwdspzj" column="zzsstxssrwdspzj" jdbcType="DECIMAL"/>
        <result property="zzsstxssrgdzccl" column="zzsstxssrgdzccl" jdbcType="DECIMAL"/>
        <result property="zzsstxssrcyh3" column="zzsstxssrcyh3" jdbcType="DECIMAL"/>
        <result property="zzsstxssrcyh4" column="zzsstxssrcyh4" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqtzpz" column="zzsstxssrlqtzpz" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqtzpz1" column="zzsstxssrlqtzpz1" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqtzpz2" column="zzsstxssrlqtzpz2" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqtzpz3" column="zzsstxssrlqtzpz3" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqtzpz4" column="zzsstxssrlqtzpz4" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqtzpz5" column="zzsstxssrlqtzpz5" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqwxpz" column="zzsstxssrlqwxpz" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqwxpz1" column="zzsstxssrlqwxpz1" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqwxpz2" column="zzsstxssrlqwxpz2" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqwxpz3" column="zzsstxssrlqwxpz3" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqwxpz4" column="zzsstxssrlqwxpz4" jdbcType="DECIMAL"/>
        <result property="zzsstxssrlqwxpz5" column="zzsstxssrlqwxpz5" jdbcType="DECIMAL"/>
        <result property="qt0sltz" column="qt0sltz" jdbcType="DECIMAL"/>
        <result property="qt0sltz1" column="qt0sltz1" jdbcType="DECIMAL"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
        <result property="sjlx" column="sjlx" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,djxh,nsrsbh,
        nsrmc,yxbz,ssny,
        yysr,zyywsr,zyyssr,
        zycpxs,zycpxs1,zycckd,
        zyspfw,zyjwfy,zyfyssr,
        zyqhth,zyyjfl,zyhyjf,
        zysrce,zymssr,qtywsr,qtgf,
        qtsbzj,qtkmjbyf,qtdswl,
        qtzlsr,qtdswl1,qtppsq,
        qtccfw,qto2ofw,qtgffwf,
        qtzlsr1,qtspyqzj,qtzgfwf,
        qtms,qtsrce,qtsaptz,
        qtsaptz1,qtsaptz2,qtsaptz3,
        qtsaptz4,zzsyssr,zzsysybjssr,
        zzsyszyywsr,zzsyszycpxs,zzsyszyxssr,
        zzsyszyxszkzr,zzsyszyqjfykd,zzsyszyjwfy,
        zzsyszycpxs1,zzsyszycckd,zzsyszyspfw,
        zzsqtywsr,zzsqtgf,zzsqtsbzj,
        zzsqtkmjbyf,zzsqtdswl,zzsqtzlsr,
        zzsqtdswl1,zzsqtppsq,zzsqtccfw,
        zzsqto2ofw,zzsqtgf1,zzsqtcyh,
        zzsqtcyh1,zzsqtcyh2,zzsqtcyh3,
        zzsstxssr,zzsstxssrjmslx,zzsstxssrzydf,
        zzsstxssrjzsr,zzsstxssrdxst,zzsstxssrsfsr,zzsstxssrbdyqzj,
        zzsstxssrdklx,zzsstxssrbblc,zzsstxssrwyf,
        zzsstxssrgssxffh,zzsstxssrcyh,zzsstxssrcyh1,
        zzsstxssrcyh2,zzsstxssrjyjssr,zzsstxssrqtywsr,
        zzsstxssrzlsr,zzsstxssrstxssr,zzsstxssrbdspyqzj,
        zzsstxssrwdspzj,zzsstxssrgdzccl,zzsstxssrcyh3,
        zzsstxssrcyh4,zzsstxssrlqtzpz,zzsstxssrlqtzpz1,
        zzsstxssrlqtzpz2,zzsstxssrlqtzpz3,zzsstxssrlqtzpz4,
        zzsstxssrlqtzpz5,zzsstxssrlqwxpz,zzsstxssrlqwxpz1,
        zzsstxssrlqwxpz2,zzsstxssrlqwxpz3,zzsstxssrlqwxpz4,
        zzsstxssrlqwxpz5,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,
        lrzx,sjlx,zzsstxssrwyjst,zzsstxssrlstscj13,zzsstxssrlstscj9,zzsstxssrlstscj6,zzsstxssrwyjst6,zzsstxssrwyjst5,qt0sltz,qt0sltz1
    </sql>
    <select id="queryHzxx" resultType="com.css.znsb.nssb.pojo.domain.srhy.ZnsbNssbSrhyhzbDO">
        select sjlx,
               sum(yysr)              as yysr,
               sum(zyywsr)            as zyywsr,
               sum(zyyssr)            as zyyssr,
               sum(zycpxs)            as zycpxs,
               sum(zycpxs1)           as zycpxs1,
               sum(zycckd)            as zycckd,
               sum(zyspfw)            as zyspfw,
               sum(zyjwfy)            as zyjwfy,
               sum(zyfyssr)           as zyfyssr,
               sum(zyqhth)            as zyqhth,
               sum(zyyjfl)            as zyyjfl,
               sum(zyhyjf)            as zyhyjf,
               sum(zymssr)            as zymssr,
               sum(zysrce)            as zysrce,
               sum(qtywsr)            as qtywsr,
               sum(qtgf)              as qtgf,
               sum(qtsbzj)            as qtsbzj,
               sum(qtkmjbyf)          as qtkmjbyf,
               sum(qtdswl)            as qtdswl,
               sum(qtzlsr)            as qtzlsr,
               sum(qtdswl1)           as qtdswl1,
               sum(qtppsq)            as qtppsq,
               sum(qtccfw)            as qtccfw,
               sum(qto2ofw)           as qto2ofw,
               sum(qtgffwf)           as qtgffwf,
               sum(qtzlsr1)           as qtzlsr1,
               sum(qtspyqzj)          as qtspyqzj,
               sum(qtzgfwf)           as qtzgfwf,
               sum(qtms)              as qtms,
               sum(qtsrce)            as qtsrce,
               sum(qtsaptz)           as qtsaptz,
               sum(qtsaptz1)          as qtsaptz1,
               sum(qtsaptz2)          as qtsaptz2,
               sum(qtsaptz3)          as qtsaptz3,
               sum(qtsaptz4)          as qtsaptz4,
               sum(zzsyssr)           as zzsyssr,
               sum(zzsysybjssr)       as zzsysybjssr,
               sum(zzsyszyywsr)       as zzsyszyywsr,
               sum(zzsyszycpxs)       as zzsyszycpxs,
               sum(zzsyszyxssr)       as zzsyszyxssr,
               sum(zzsyszyxszkzr)     as zzsyszyxszkzr,
               sum(zzsyszyqjfykd)     as zzsyszyqjfykd,
               sum(zzsyszyjwfy)       as zzsyszyjwfy,
               sum(zzsyszycpxs1)      as zzsyszycpxs1,
               sum(zzsyszycckd)       as zzsyszycckd,
               sum(zzsyszyspfw)       as zzsyszyspfw,
               sum(zzsqtywsr)         as zzsqtywsr,
               sum(zzsqtgf)           as zzsqtgf,
               sum(zzsqtsbzj)         as zzsqtsbzj,
               sum(zzsqtkmjbyf)       as zzsqtkmjbyf,
               sum(zzsqtdswl)         as zzsqtdswl,
               sum(zzsqtzlsr)         as zzsqtzlsr,
               sum(zzsqtdswl1)        as zzsqtdswl1,
               sum(zzsqtppsq)         as zzsqtppsq,
               sum(zzsqtccfw)         as zzsqtccfw,
               sum(zzsqto2ofw)        as zzsqto2ofw,
               sum(zzsqtgf1)          as zzsqtgf1,
               sum(zzsqtcyh)          as zzsqtcyh,
               sum(zzsqtcyh1)         as zzsqtcyh1,
               sum(zzsqtcyh2)         as zzsqtcyh2,
               sum(zzsqtcyh3)         as zzsqtcyh3,
               sum(zzsstxssr)         as zzsstxssr,
               sum(zzsstxssrjmslx)    as zzsstxssrjmslx,
               sum(zzsstxssrzydf)     as zzsstxssrzydf,
               sum(zzsstxssrjzsr)     as zzsstxssrjzsr,
               sum(zzsstxssrdxst)     as zzsstxssrdxst,
               sum(zzsstxssrsfsr)     as zzsstxssrsfsr,
               sum(zzsstxssrbdyqzj)   as zzsstxssrbdyqzj,
               sum(zzsstxssrdklx)     as zzsstxssrdklx,
               sum(zzsstxssrbblc)     as zzsstxssrbblc,
               sum(zzsstxssrwyf)      as zzsstxssrwyf,
               sum(zzsstxssrgssxffh)  as zzsstxssrgssxffh,
               sum(zzsstxssrcyh)      as zzsstxssrcyh,
               sum(zzsstxssrcyh1)     as zzsstxssrcyh1,
               sum(zzsstxssrcyh2)     as zzsstxssrcyh2,
               sum(zzsstxssrjyjssr)   as zzsstxssrjyjssr,
               sum(zzsstxssrqtywsr)   as zzsstxssrqtywsr,
               sum(zzsstxssrzlsr)     as zzsstxssrzlsr,
               sum(zzsstxssrstxssr)   as zzsstxssrstxssr,
               sum(zzsstxssrbdspyqzj) as zzsstxssrbdspyqzj,
               sum(zzsstxssrwdspzj)   as zzsstxssrwdspzj,
               sum(zzsstxssrgdzccl)   as zzsstxssrgdzccl,
               sum(zzsstxssrcyh3)     as zzsstxssrcyh3,
               sum(zzsstxssrcyh4)     as zzsstxssrcyh4,
               sum(zzsstxssrlqtzpz)   as zzsstxssrlqtzpz,
               sum(zzsstxssrlqtzpz1)  as zzsstxssrlqtzpz1,
               sum(zzsstxssrlqtzpz2)  as zzsstxssrlqtzpz2,
               sum(zzsstxssrlqtzpz3)  as zzsstxssrlqtzpz3,
               sum(zzsstxssrlqtzpz4)  as zzsstxssrlqtzpz4,
               sum(zzsstxssrlqtzpz5)  as zzsstxssrlqtzpz5,
               sum(zzsstxssrlqwxpz)   as zzsstxssrlqwxpz,
               sum(zzsstxssrlqwxpz1)  as zzsstxssrlqwxpz1,
               sum(zzsstxssrlqwxpz2)  as zzsstxssrlqwxpz2,
               sum(zzsstxssrlqwxpz3)  as zzsstxssrlqwxpz3,
               sum(zzsstxssrlqwxpz4)  as zzsstxssrlqwxpz4,
               sum(zzsstxssrlqwxpz5)  as zzsstxssrlqwxpz5,
               sum(zzsstxssrwyjst)    as zzsstxssrwyjst,
               sum(zzsstxssrwyjst6)   as zzsstxssrwyjst6,
               sum(zzsstxssrwyjst5)   as zzsstxssrwyjst5,
               sum(zzsstxssrlstscj13) as zzsstxssrlstscj13,
               sum(zzsstxssrlstscj9)  as zzsstxssrlstscj9,
               sum(zzsstxssrlstscj6)  as zzsstxssrlstscj6,
               sum(qt0sltz)           as qt0sltz,
               sum(qt0sltz1)          as qt0sltz1
        from znsb_nssb_srhyhzb
        where yxbz = 'Y'
          and ssny = #{ssny}
          and djxh = #{djxh}
        group by sjlx;
    </select>
    <select id="queryMxxx" resultType="com.css.znsb.nssb.pojo.domain.srhy.ZnsbNssbSrhyhzbDO">
        select *
        from znsb_nssb_srhyhzb
        where yxbz = 'Y'
          and ssny = #{ssny}
          and djxh = #{djxh}
        order by lrzx asc;
    </select>
</mapper>
