<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbFjsfHznsfjsffpbXfsMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbFjsfHznsfjsffpbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="fzjgdjxh" column="fzjgdjxh" jdbcType="DECIMAL"/>
            <result property="fzjgnsrsbh" column="fzjgnsrsbh" jdbcType="VARCHAR"/>
            <result property="fzjgmc" column="fzjgmc" jdbcType="VARCHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="jsyj" column="jsyj" jdbcType="DECIMAL"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="rdpzuuid" column="rdpzuuid" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="phjmxzDm" column="phjmxz_dm" jdbcType="VARCHAR"/>
            <result property="phjmswsxDm" column="phjmswsx_dm" jdbcType="CHAR"/>
            <result property="phjzbl" column="phjzbl" jdbcType="DECIMAL"/>
            <result property="phjmse" column="phjmse" jdbcType="DECIMAL"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        fzjgdjxh,fzjgnsrsbh,fzjgmc,
        zsxm_dm,zspm_dm,jsyj,
        sl_1,ybtse,rdpzuuid,
        lrrq,xgrq,sjtb_sj,
        sjgsdq,sjblbz,phjmxz_dm,
        phjmswsx_dm,phjzbl,phjmse,
        lrrsfid,xgrsfid,ywqd_dm,
        sjcsdq
    </sql>
</mapper>
