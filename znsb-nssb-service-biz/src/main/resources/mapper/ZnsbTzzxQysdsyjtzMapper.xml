<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.yjzt.ZnsbTzzxQysdsyjtzMapper">

    <select id="queryYjtz" resultType="com.css.znsb.nssb.pojo.domain.yjtz.ZnsbTzzxQysdsyjtzDO">
       SELECT *
        FROM znsb_tzzx_qysdsyjtz
        where nsrsbh = #{params.nsrsbh}
        <if test = 'params.gslx != null' >
            and gslx_dm = #{params.gslx}
        </if>
        <if test = "params.sszqq != null and params.sszqq != ''" >
            and sszq &gt;= #{params.sszqq}
        </if>
        <if test = "params.sszqz != null and params.sszqz != ''" >
            and sszq &lt;= #{params.sszqz}
        </if>
        order by sszq desc
    </select>
</mapper>
