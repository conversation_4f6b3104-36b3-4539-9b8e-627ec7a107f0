<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbJypfqyyfxsmxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbJypfqyyfxsmxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="bzsptm" column="bzsptm" jdbcType="VARCHAR"/>
            <result property="phgg" column="phgg" jdbcType="VARCHAR"/>
            <result property="jylbDm1" column="jylb_dm_1" jdbcType="VARCHAR"/>
            <result property="jylxDm1" column="jylx_dm_1" jdbcType="VARCHAR"/>
            <result property="xsjg" column="xsjg" jdbcType="DECIMAL"/>
            <result property="xssl" column="xssl" jdbcType="DECIMAL"/>
            <result property="xse" column="xse" jdbcType="DECIMAL"/>
            <result property="bz" column="bz" jdbcType="VARCHAR"/>
            <result property="shbz" column="shbz" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="xsjg1" column="xsjg1" jdbcType="DECIMAL"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,bzsptm,phgg,
        jylb_dm_1,jylx_dm_1,xsjg,
        xssl,xse,bz,
        shbz,sjblbz,xsjg1,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
