<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sxydfkjh.ZnsbNssbJhfkZbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sxydfkjh.ZnsbNssbJhfkZbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nd" column="nd" jdbcType="VARCHAR"/>
            <result property="jhsl" column="jhsl" jdbcType="DECIMAL"/>
            <result property="jhzcsl" column="jhzcsl" jdbcType="DECIMAL"/>
            <result property="jhmzsl" column="jhmzsl" jdbcType="DECIMAL"/>
            <result property="jhwcbz" column="jhwcbz" jdbcType="CHAR"/>
            <result property="jhsj" column="jhsj" jdbcType="TIMESTAMP"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,nsrsbh,nd,
        jhsl,jhzcsl,jhmzsl,
        jhwcbz,jhsj,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
