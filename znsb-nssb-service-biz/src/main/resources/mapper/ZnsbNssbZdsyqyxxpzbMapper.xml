<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqyxxpzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zdsy.ZnsbNssbZdsyqyxxpzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="gsh1" column="gsh_1" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="zjgdjxh" column="zjgdjxh" jdbcType="DECIMAL"/>
            <result property="zjgnsrsbh" column="zjgnsrsbh" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,nsrsbh,
        nsrmc,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,
        gsh_1,sl_1,zjgdjxh,
        zjgnsrsbh
    </sql>
</mapper>
