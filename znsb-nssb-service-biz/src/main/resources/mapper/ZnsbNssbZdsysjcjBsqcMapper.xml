<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsysjcjBsqcMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zdsy.ZnsbNssbZdsysjcjBsqc">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="ywqd_dm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtb_sj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="djxh" column="djxh" jdbcType="VARCHAR"/>
            <result property="sbqkuuid" column="sbqkuuid" jdbcType="VARCHAR"/>
            <result property="bbmc" column="bbmc" jdbcType="VARCHAR"/>
            <result property="ssny" column="ssny" jdbcType="INTEGER"/>
            <result property="tbztbm" column="tbztbm" jdbcType="VARCHAR"/>
            <result property="shztbm" column="shztbm" jdbcType="VARCHAR"/>
            <result property="cjsj" column="cjsj" jdbcType="TIMESTAMP"/>
            <result property="sdbz" column="sdbz" jdbcType="CHAR"/>
            <result property="dcztbm" column="dcztbm" jdbcType="VARCHAR"/>
            <result property="ssqq" column="ssqq" jdbcType="TIMESTAMP"/>
            <result property="ssqz" column="ssqz" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,
        djxh,sbqkuuid,bbmc,
        ssny,tbztbm,shztbm,
        cjsj,sdbz,dcztbm,
        ssqq,ssqz
    </sql>
</mapper>
