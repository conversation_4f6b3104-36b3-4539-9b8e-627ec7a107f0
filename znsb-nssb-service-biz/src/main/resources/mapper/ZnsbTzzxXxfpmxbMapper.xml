<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxXxfpmxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.xxfp.ZnsbTzzxXxfpmxbDO">
            <id property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="fppzDm" column="fppz_dm" jdbcType="CHAR"/>
            <result property="fpdm" column="fpdm" jdbcType="VARCHAR"/>
            <result property="fpdmhm" column="fpdmhm" jdbcType="VARCHAR"/>
            <result property="fphm" column="fphm" jdbcType="VARCHAR"/>
            <result property="je" column="je" jdbcType="DECIMAL"/>
            <result property="se" column="se" jdbcType="DECIMAL"/>
            <result property="jshj" column="jshj" jdbcType="DECIMAL"/>
            <result property="kprq" column="kprq" jdbcType="TIMESTAMP"/>
            <result property="gmfnsrsbh" column="gmfnsrsbh" jdbcType="VARCHAR"/>
            <result property="gmfnsrmc" column="gmfnsrmc" jdbcType="VARCHAR"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,uuid,sszq,
        nsrsbh,nsrmc,fppz_dm,
        fpdm,fpdmhm,fphm,
        je,se,jshj,
        kprq,gmfnsrsbh,gmfnsrmc,
        tzlx_dm,lrrsfid,lrrq,
        xgrsfid,xgrq,ywqd_dm,
        sjcsdq,sjgsdq,sjtb_sj
    </sql>
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.css.znsb.tzzx.pojo.vo.xxfp.XxfpMxQueryVO">
        select count(1) from znsb_tzzx_xxfpmxb
        where djxh = #{data.djxh} and sszq = #{data.sszq} and tzlx_dm ='0';
    </select>
    <select id="selectSumJshj" resultType="java.math.BigDecimal">
        select sum(jshj) from znsb_tzzx_xxfpmxb where fphm =#{data.fphm} and djxh = #{data.djxh} and tzlx_dm ='1' ;
    </select>
</mapper>
