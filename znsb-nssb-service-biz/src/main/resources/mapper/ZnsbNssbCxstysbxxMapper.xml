<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxstysbxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxstysbxxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="jsyj" column="jsyj" jdbcType="DECIMAL"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
            <result property="jmse" column="jmse" jdbcType="DECIMAL"/>
            <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="sybh1" column="sybh_1" jdbcType="VARCHAR"/>
            <result property="yqsbbz" column="yqsbbz" jdbcType="CHAR"/>
            <result property="yqts" column="yqts" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="cxstysbuuid" column="cxstysbuuid" jdbcType="VARCHAR"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="VARCHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="VARCHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,zsxm_dm,
        zspm_dm,zszm_dm,skssqq,
        skssqz,jsyj,sl_1,
        ynse,jmse,yjse,
        ybtse,syuuid,sybh_1,
        yqsbbz,yqts,lrrq,
        lrrsfid,xgrq,xgrsfid,
        ywqd_dm,sjcsdq,sjgsdq,
        sjtb_sj,cxstysbuuid,yzpzzl_dm,zfbz_1,zfrq_1,pzxh
    </sql>
    <select id="queryZbmxList" resultType="com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.CchxwsnssbzbmxVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM  znsb_nssb_cxstysbxx
        WHERE (pzxh = #{pzxh}
        OR cxstysbuuid = #{cxstysbuuid})
        AND zfbz_1 ='N'
    </select>

</mapper>
