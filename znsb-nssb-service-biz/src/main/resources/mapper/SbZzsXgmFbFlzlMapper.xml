<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsxgmsb.SbZzsXgmFbFlzlMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zzsxgmsb.SbZzsXgmFbFlzlDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="qcye" column="qcye" jdbcType="DECIMAL"/>
            <result property="bqfse" column="bqfse" jdbcType="DECIMAL"/>
            <result property="bqkce" column="bqkce" jdbcType="DECIMAL"/>
            <result property="qmye" column="qmye" jdbcType="DECIMAL"/>
            <result property="ysfwxsqbhssr" column="ysfwxsqbhssr" jdbcType="DECIMAL"/>
            <result property="ysfwxshsxse" column="ysfwxshsxse" jdbcType="DECIMAL"/>
            <result property="ysfwxsbhsxse" column="ysfwxsbhsxse" jdbcType="DECIMAL"/>
            <result property="qcye5" column="qcye5" jdbcType="DECIMAL"/>
            <result property="bqfse5" column="bqfse5" jdbcType="DECIMAL"/>
            <result property="bqkce5" column="bqkce5" jdbcType="DECIMAL"/>
            <result property="qmye5" column="qmye5" jdbcType="DECIMAL"/>
            <result property="ysfwxsqbhssr5" column="ysfwxsqbhssr5" jdbcType="DECIMAL"/>
            <result property="ysfwxshsxse5" column="ysfwxshsxse5" jdbcType="DECIMAL"/>
            <result property="ysfwxsbhsxse5" column="ysfwxsbhsxse5" jdbcType="DECIMAL"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        qcye,bqfse,bqkce,
        qmye,ysfwxsqbhssr,ysfwxshsxse,
        ysfwxsbhsxse,qcye5,bqfse5,
        bqkce5,qmye5,ysfwxsqbhssr5,
        ysfwxshsxse5,ysfwxsbhsxse5,ywqd_dm,
        lrrq,lrrsfid,xgrq,
        xgrsfid,sjcsdq,sjgsdq,
        sjtb_sj
    </sql>
</mapper>
