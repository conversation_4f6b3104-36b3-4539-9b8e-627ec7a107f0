<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzTsssMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.sshz.ZnsbTzzxLqsshzTsssDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="mdtytse" column="mdtytse" jdbcType="DECIMAL"/>
            <result property="zzsmdse" column="zzsmdse" jdbcType="DECIMAL"/>
            <result property="jzjtsjtse" column="jzjtsjtse" jdbcType="DECIMAL"/>
            <result property="mdtytsejyssbz" column="mdtytsejyssbz" jdbcType="VARCHAR"/>
            <result property="zzsmdsejyssbz" column="zzsmdsejyssbz" jdbcType="VARCHAR"/>
            <result property="jzjtsjtsejyssbz" column="jzjtsjtsejyssbz" jdbcType="VARCHAR"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,mdtytse,
        zzsmdse,jzjtsjtse,mdtytsejyssbz,
        zzsmdsejyssbz,jzjtsjtsejyssbz,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
