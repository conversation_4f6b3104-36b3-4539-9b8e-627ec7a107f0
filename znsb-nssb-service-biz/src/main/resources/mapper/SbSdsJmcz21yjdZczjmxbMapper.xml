<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZczjmxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sds.SbSdsJmcz21yjdZczjmxbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="yhswsx" column="yhswsx" jdbcType="CHAR"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="ewbhgjz" column="ewbhgjz" jdbcType="VARCHAR"/>
            <result property="bnxsyhdzcyz" column="bnxsyhdzcyz" jdbcType="DECIMAL"/>
            <result property="bnljzjkcjedzz" column="bnljzjkcjedzz" jdbcType="DECIMAL"/>
            <result property="bnljzjkcjedaz" column="bnljzjkcjedaz" jdbcType="DECIMAL"/>
            <result property="bnljzjkcjedxs" column="bnljzjkcjedxs" jdbcType="DECIMAL"/>
            <result property="bnljzjkcjedns" column="bnljzjkcjedns" jdbcType="DECIMAL"/>
            <result property="bnljzjkcjedjs" column="bnljzjkcjedjs" jdbcType="DECIMAL"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,yhswsx,ssjmxz_dm,
        ewbhgjz,bnxsyhdzcyz,bnljzjkcjedzz,
        bnljzjkcjedaz,bnljzjkcjedxs,bnljzjkcjedns,
        bnljzjkcjedjs,lrrq,xgrq,
        sjgsdq,sjtb_sj,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq
    </sql>
    <select id="queryBDA0611159A201020" resultType="java.util.Map">
        select t.sbuuid,         t.ewbhxh,         t.yhswsx,
               t.ssjmxz_dm,         t.ewbhgjz,         t.bnxsyhdzcyz,
               t.bnljzjkcjedzz,         t.bnljzjkcjedaz,         t.bnljzjkcjedxs,
               t.bnljzjkcjedns,         t.bnljzjkcjedjs
        from SB_SDS_JMCZ_21YJD_ZCZJMXB t
        where sbuuid = #{sbuuid}
        order by t.ewbhxh
    </select>
</mapper>
