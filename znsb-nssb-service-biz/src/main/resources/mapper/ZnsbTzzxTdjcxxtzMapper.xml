<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.cztdsytz.ZnsbTzzxTdjcxxtzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxTdjcxxtzDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="fzjgmc" column="fzjgmc" jdbcType="VARCHAR"/>
            <result property="bdcqzlx" column="bdcqzlx" jdbcType="CHAR"/>
            <result property="tdsyzbh" column="tdsyzbh" jdbcType="VARCHAR"/>
            <result property="bdcdyh" column="bdcdyh" jdbcType="VARCHAR"/>
            <result property="tdmc1" column="tdmc_1" jdbcType="VARCHAR"/>
            <result property="dh1" column="dh_1" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="tdzldz" column="tdzldz" jdbcType="VARCHAR"/>
            <result property="tdytDm" column="tdyt_dm" jdbcType="CHAR"/>
            <result property="zytdmj1" column="zytdmj1" jdbcType="DECIMAL"/>
            <result property="tddj" column="tddj" jdbcType="DECIMAL"/>
            <result property="syqqxq" column="syqqxq" jdbcType="TIMESTAMP"/>
            <result property="syqqxz" column="syqqxz" jdbcType="TIMESTAMP"/>
            <result property="tdxzDm" column="tdxz_dm" jdbcType="CHAR"/>
            <result property="tdqdfsDm" column="tdqdfs_dm" jdbcType="CHAR"/>
            <result property="ystdmj" column="ystdmj" jdbcType="DECIMAL"/>
            <result property="tddjDm" column="tddj_dm" jdbcType="CHAR"/>
            <result property="dwse" column="dwse" jdbcType="DECIMAL"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="sfyscsy" column="sfyscsy" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="qlr" column="qlr" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="VARCHAR"/>
            <result property="cjuuid" column="cjuuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,nsrsbh,
        nsrmc,fzjgmc,bdcqzlx,
        tdsyzbh,bdcdyh,tdmc1,
        dh1,xzqhszDm,jdxzDm,
        tdzldz,tdytDm,zytdmj1,
        tddj,syqqxq,syqqxz,
        tdxzDm,tdqdfsDm,ystdmj,
        tddjDm,dwse,yxqq,
        sfyscsy,lrrq,lrrsfid,
        xgrq,xgrsfid,scbz,
        scsj1,ywqdDm,sjcsdq,
        sjgsdq,sjtbSj,qlr,
        zspmDm,cjuuid
    </sql>
</mapper>
