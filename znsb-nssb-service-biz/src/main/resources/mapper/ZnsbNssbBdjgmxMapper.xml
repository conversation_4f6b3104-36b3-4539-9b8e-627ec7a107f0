<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sbrw.ZnsbNssbBdjgmxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbBdjgmxDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
        <result property="xh" column="xh" jdbcType="INTEGER"/>
        <result property="sbsxMc" column="sbsx_mc" jdbcType="VARCHAR"/>
        <result property="sbbdxmDm" column="sbbdxm_dm" jdbcType="CHAR"/>
        <result property="bdxmmc" column="bdxmmc" jdbcType="VARCHAR"/>
        <result property="btgyy" column="btgyy" jdbcType="VARCHAR"/>
        <result property="sfqzzdbz" column="sfqzzdbz" jdbcType="CHAR"/>
        <result property="tsxx" column="tsxx" jdbcType="VARCHAR"/>
        <result property="bdjg" column="bmdbz" jdbcType="CHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,xh,
        sbsx_mc,sbbdxm_dm,bdxmmc,
        btgyy,sfqzzdbz,TSXX,
        bdjg,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
