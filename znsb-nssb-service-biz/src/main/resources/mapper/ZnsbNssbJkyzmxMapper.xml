<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.skjn.ZnsbNssbJkyzmxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.skjn.ZnsbNssbJkyzmxDO">
        <id property="zsuuid" column="zsuuid" jdbcType="VARCHAR"/>
        <result property="yzpzxh" column="yzpzxh" jdbcType="DECIMAL"/>
        <result property="yzpzmxxh" column="yzpzmxxh" jdbcType="DECIMAL"/>
        <result property="glyzpzmxxh" column="glyzpzmxxh" jdbcType="DECIMAL"/>
        <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="zsdlfsDm" column="zsdlfs_dm" jdbcType="CHAR"/>
        <result property="nssbrq" column="nssbrq" jdbcType="TIMESTAMP"/>
        <result property="yzfsrq" column="yzfsrq" jdbcType="TIMESTAMP"/>
        <result property="yzgsrq" column="yzgsrq" jdbcType="TIMESTAMP"/>
        <result property="kjjzrq" column="kjjzrq" jdbcType="TIMESTAMP"/>
        <result property="yzclrq" column="yzclrq" jdbcType="TIMESTAMP"/>
        <result property="czlxDm" column="czlx_dm" jdbcType="CHAR"/>
        <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
        <result property="skcllxDm" column="skcllx_dm" jdbcType="CHAR"/>
        <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
        <result property="jsyj" column="jsyj" jdbcType="DECIMAL"/>
        <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
        <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
        <result property="jmse" column="jmse" jdbcType="DECIMAL"/>
        <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
        <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
        <result property="znjje" column="znjje" jdbcType="DECIMAL"/>
        <result property="fkje" column="fkje" jdbcType="DECIMAL"/>
        <result property="lxje" column="lxje" jdbcType="DECIMAL"/>
        <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
        <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
        <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
        <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
        <result property="jkqx" column="jkqx" jdbcType="TIMESTAMP"/>
        <result property="kjdjxh" column="kjdjxh" jdbcType="DECIMAL"/>
        <result property="zsfsDm" column="zsfs_dm" jdbcType="CHAR"/>
        <result property="skzlDm" column="skzl_dm" jdbcType="CHAR"/>
        <result property="sksxDm" column="sksx_dm" jdbcType="CHAR"/>
        <result property="yjskztDm" column="yjskzt_dm" jdbcType="CHAR"/>
        <result property="sbfsDm" column="sbfs_dm" jdbcType="CHAR"/>
        <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
        <result property="sybh1" column="sybh_1" jdbcType="VARCHAR"/>
        <result property="skssswjgDm" column="skssswjg_dm" jdbcType="CHAR"/>
        <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
        <result property="zsswjgDm" column="zsswjg_dm" jdbcType="CHAR"/>
        <result property="rkrq" column="rkrq" jdbcType="TIMESTAMP"/>
        <result property="clztDm" column="clzt_dm" jdbcType="CHAR"/>
        <result property="jkyzzbuuid" column="jkyzzbuuid" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="sfxyh" column="sfxyh" jdbcType="VARCHAR"/>
        <result property="znjtsjscs" column="znjtsjscs" jdbcType="CHAR"/>
        <result property="yybz" column="yybz" jdbcType="CHAR"/>
        <result property="yyrq" column="yyrq" jdbcType="TIMESTAMP"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="dzsphm" column="dzsphm" jdbcType="DECIMAL"/>
        <result property="skgkDm" column="skgkDm" jdbcType="CHAR"/>
        <result property="sjje" column="sjje" jdbcType="DECIMAL"/>
        <result property="kjrq" column="kjrq" jdbcType="TIMESTAMP"/>
        <result property="yyuuid" column="yyuuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        zsuuid,yzpzxh,yzpzmxxh,
        glyzpzmxxh,yzpzzl_dm,djxh,
        zsdlfs_dm,nssbrq,yzfsrq,
        yzgsrq,kjjzrq,yzclrq,
        czlx_dm,tzlx_dm,skcllx_dm,
        sbsx_dm_1,jsyj,sl_1,
        ynse,jmse,yjse,
        ybtse,znjje,fkje,
        lxje,zsxm_dm,zspm_dm,
        skssqq,skssqz,jkqx,
        kjdjxh,zsfs_dm,skzl_dm,
        sksx_dm,yjskzt_dm,sbfs_dm,
        zszm_dm,sybh_1,skssswjg_dm,
        zgswskfj_dm,zsswjg_dm,rkrq,
        clzt_dm,jkyzzbuuid,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj,sfxyh,znjtsjscs,
        yybz,yyrq,nsrsbh,
        dzsphm,skgk_dm,sjje,kjrq,yyuuid
    </sql>

    <update id="updateJkyzmxByYzpzxhmx">
        update znsb_nssb_jkyzmx set dzsphm = #{data.dzsphm},skgk_dm = #{data.skgkDm},CLZT_DM = #{data.clztDm},sjje = #{data.sjje},kjrq = #{data.kjrq}, xgrq = #{data.xgrq} , xgrsfid = #{data.xgrsfid} , nsrsbh = #{data.nsrsbh} , znjtsjscs = #{data.znjtsjscs} , sfxyh = #{data.sfxyh} where yzpzxh = #{data.yzpzxh} and yzpzmxxh = #{data.yzpzmxxh} ;
    </update>

    <update id="updateJkyzmxByDzsphm">
        update znsb_nssb_jkyzmx set CLZT_DM = #{data.clztDm}, xgrq = #{data.xgrq}, xgrsfid = #{data.xgrsfid} where dzsphm = #{data.dzsphm} and skssswjg_dm = #{data.skssswjgDm}  ;
    </update>

    <update id="updateJkyzmxByBccl">
        update znsb_nssb_jkyzmx set CLZT_DM = '01', xgrq = #{data.xgrq}, xgrsfid = #{data.xgrsfid},dzsphm = #{data.dzsphm},sjje = #{data.sjje},kjrq = #{data.kjrq} where  yzpzxh = #{data.yzpzxh} and djxh = #{data.djxh} and zsxm_dm = #{data.zsxmDm}  and  zspm_dm = #{data.zspmDm} and  skssqq = #{data.skssqq} and  skssqz = #{data.skssqz}  and  ybtse = #{data.ybtse} and  skzl_dm = #{data.skzlDm} and  nssbrq = #{data.nssbrq} and  sbfs_dm = #{data.sbfsDm}  ;
    </update>

</mapper>
