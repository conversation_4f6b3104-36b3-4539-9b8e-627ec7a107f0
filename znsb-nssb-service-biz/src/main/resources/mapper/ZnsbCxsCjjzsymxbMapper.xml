<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsCjjzsymxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsCjjzsymxbDO">
            <id property="cjsyuuid" column="cjsyuuid" jdbcType="VARCHAR"/>
            <result property="fyxxuuid" column="fyxxuuid" jdbcType="VARCHAR"/>
            <result property="fcyz" column="fcyz" jdbcType="DECIMAL"/>
            <result property="czwyz" column="czwyz" jdbcType="DECIMAL"/>
            <result property="czfcmj" column="czfcmj" jdbcType="DECIMAL"/>
            <result property="jsbl" column="jsbl" jdbcType="DECIMAL"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="ycjsyuuid" column="ycjsyuuid" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="bgbm" column="bgbm" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="jmxzdmxx" column="jmxzdmxx" jdbcType="VARCHAR"/>
            <result property="jmshj" column="jmshj" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="blbyzyy" column="blbyzyy" jdbcType="VARCHAR"/>
            <result property="yxqbyzyy" column="yxqbyzyy" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        cjsyuuid,fyxxuuid,fcyz,
        czwyz,czfcmj,jsbl,
        yxqq,yxqz,ycjsyuuid,
        zspm_dm,yxbz,bgbm,
        lrr_dm,lrrq,xgr_dm,
        xgrq,sjgsdq,sjtb_sj,
        jmxzdmxx,jmshj,sjblbz,
        blbyzyy,yxqbyzyy,lrrsfid,
        xgrsfid,sjcsdq,ywqd_dm,
        uuid,zfbz_1
    </sql>
</mapper>
