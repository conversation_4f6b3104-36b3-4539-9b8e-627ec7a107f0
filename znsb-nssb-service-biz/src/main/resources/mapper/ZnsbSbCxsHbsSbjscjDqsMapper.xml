<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjDqsMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.hbs.ZnsbSbCxsHbsSbjscjDqsDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="hgbhssybh" column="hgbhssybh" jdbcType="VARCHAR"/>
            <result property="yf" column="yf" jdbcType="CHAR"/>
            <result property="pfkmc" column="pfkmc" jdbcType="VARCHAR"/>
            <result property="zywrwlbDm" column="zywrwlb_dm" jdbcType="VARCHAR"/>
            <result property="fzspmDm" column="fzspm_dm" jdbcType="CHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="wrwpfljsffDm" column="wrwpfljsff_dm" jdbcType="CHAR"/>
            <result property="pfl" column="pfl" jdbcType="DECIMAL"/>
            <result property="scldz" column="scldz" jdbcType="DECIMAL"/>
            <result property="dyyxyjld" column="dyyxyjld" jdbcType="DECIMAL"/>
            <result property="zgscldz" column="zgscldz" jdbcType="DECIMAL"/>
            <result property="jsjs1" column="jsjs1" jdbcType="DECIMAL"/>
            <result property="cwxs" column="cwxs" jdbcType="DECIMAL"/>
            <result property="pwxs" column="pwxs" jdbcType="DECIMAL"/>
            <result property="wrwpfl" column="wrwpfl" jdbcType="DECIMAL"/>
            <result property="wrdlz" column="wrdlz" jdbcType="DECIMAL"/>
            <result property="wrdls" column="wrdls" jdbcType="DECIMAL"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
            <result property="jmse" column="jmse" jdbcType="DECIMAL"/>
            <result property="sbbz" column="sbbz" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="wrwdwDm" column="wrwdw_dm" jdbcType="VARCHAR"/>
            <result property="jmfd" column="jmfd" jdbcType="DECIMAL"/>
            <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,skssqq,skssqz,
        sbsx_dm_1,syuuid,hgbhssybh,
        yf,pfkmc,zywrwlb_dm,
        fzspm_dm,zspm_dm,wrwpfljsff_dm,
        pfl,scldz,dyyxyjld,
        zgscldz,jsjs1,cwxs,
        pwxs,wrwpfl,wrdlz,
        wrdls,swsx_dm,ssjmxz_dm,
        sl_1,ynse,jmse,
        sbbz,yxbz,lrr_dm,
        xgr_dm,wrwdw_dm,jmfd,
        yjse,ybtse,zszm_dm,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
