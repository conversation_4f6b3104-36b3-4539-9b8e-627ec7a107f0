<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxFcssyfckmfpdzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.fcs.ZnsbTzzxFcssyfckmfpdzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="gsh1" column="gsh_1" jdbcType="VARCHAR"/>
            <result property="sybh1" column="sybh_1" jdbcType="VARCHAR"/>
            <result property="fwcqzsh" column="fwcqzsh" jdbcType="VARCHAR"/>
            <result property="kmbm" column="kmbm" jdbcType="VARCHAR"/>
            <result property="kjfpDm" column="kjfp_dm" jdbcType="VARCHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="xybz" column="xybz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="cznsqxDm" column="cznsqx_dm" jdbcType="VARCHAR"/>
            <result property="cjnsqxDm" column="cjnsqx_dm" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,gsh1,sybh1,
        fwcqzsh,kmbm,kjfp_dm,
        yxbz,xybz,ywqdDm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtbSj,cznsqxDm,cjnsqxDm
    </sql>
</mapper>
