<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjBsqcMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjBsqcDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="bsuuid" column="bsuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="bsqq" column="bsqq" jdbcType="TIMESTAMP"/>
            <result property="bsqz" column="bsqz" jdbcType="TIMESTAMP"/>
            <result property="bsqx" column="bsqx" jdbcType="TIMESTAMP"/>
            <result property="bsztDm" column="bszt_dm" jdbcType="VARCHAR"/>
            <result property="bsrq" column="bsrq" jdbcType="TIMESTAMP"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="ysqxxid" column="ysqxxid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="bsssq" column="bsssq" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,bsuuid,djxh,
        bsqq,bsqz,bsqx,
        bszt_dm,bsrq,lrrq,
        xgrq,lrrsfid,xgrsfid,
        ywqd_dm,ysqxxid,sjcsdq,
        sjgsdq,sjtb_sj,bsssq,
        nsrsbh,xzqhsz_dm
    </sql>
</mapper>
