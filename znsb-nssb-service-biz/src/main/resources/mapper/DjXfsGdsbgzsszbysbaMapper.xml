<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfs.DjXfsGdsbgzsszbysbaMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.xfs.lqfzpt.gdsbbab.XfsGdsbBaDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="spmc" column="spmc" jdbcType="VARCHAR"/>
            <result property="ggxhhgsh" column="ggxhhgsh" jdbcType="VARCHAR"/>
            <result property="bmxlh" column="bmxlh" jdbcType="VARCHAR"/>
            <result property="fpDm" column="fp_dm" jdbcType="VARCHAR"/>
            <result property="fphm" column="fphm" jdbcType="VARCHAR"/>
            <result property="hgjkxfszyjkshm" column="hgjkxfszyjkshm" jdbcType="VARCHAR"/>
            <result property="kjrq" column="kjrq" jdbcType="TIMESTAMP"/>
            <result property="kcsl2" column="kcsl_2" jdbcType="DECIMAL"/>
            <result property="kcje1" column="kcje_1" jdbcType="DECIMAL"/>
            <result property="tbrq" column="tbrq" jdbcType="TIMESTAMP"/>
            <result property="tbr" column="tbr" jdbcType="VARCHAR"/>
            <result property="slswjgDm" column="slswjg_dm" jdbcType="CHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="djbz1" column="djbz_1" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="reserve1" column="reserve1" jdbcType="VARCHAR"/>
            <result property="reserve2" column="reserve2" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,spmc,
        ggxhhgsh,bmxlh,fp_dm,
        fphm,hgjkxfszyjkshm,kjrq,
        kcsl_2,kcje_1,tbrq,
        tbr,slswjg_dm,zspm_dm,
        zfbz_1,zfr_dm,djbz_1,
        zfrq_1,reserve1,reserve2,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
