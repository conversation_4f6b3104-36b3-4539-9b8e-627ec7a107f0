<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxCzbdcyjmxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.zzsyj.ZnsbTzzxCzbdcyjmxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
            <result property="fwzldz" column="fwzldz" jdbcType="VARCHAR"/>
            <result property="fwcqzsh" column="fwcqzsh" jdbcType="VARCHAR"/>
            <result property="htbh" column="htbh" jdbcType="VARCHAR"/>
            <result property="htmc" column="htmc" jdbcType="VARCHAR"/>
            <result property="gsmc" column="gsmc" jdbcType="VARCHAR"/>
            <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
            <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
            <result property="htqdrq" column="htqdrq" jdbcType="TIMESTAMP"/>
            <result property="htydsxrq" column="htydsxrq" jdbcType="TIMESTAMP"/>
            <result property="htydzzrq" column="htydzzrq" jdbcType="TIMESTAMP"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="skfs" column="skfs" jdbcType="CHAR"/>
            <result property="zzrq" column="zzrq" jdbcType="TIMESTAMP"/>
            <result property="czmj" column="czmj" jdbcType="DECIMAL"/>
            <result property="zlrqq" column="zlrqq" jdbcType="TIMESTAMP"/>
            <result property="zlrqz" column="zlrqz" jdbcType="TIMESTAMP"/>
            <result property="yzjsr" column="yzjsr" jdbcType="DECIMAL"/>
            <result property="hsyzjsr" column="hsyzjsr" jdbcType="DECIMAL"/>
            <result property="zgswjgDm" column="zgswjg_dm" jdbcType="CHAR"/>
            <result property="ly" column="ly" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,nsrsbh,
        nsrmc,fybh,fwzldz,
        fwcqzsh,htbh,htmc,
        gsmc,gsh_2,lrzx,
        htqdrq,htydsxrq,htydzzrq,
        sl_1,skfs,zzrq,
        czmj,zlrqq,zlrqz,
        yzjsr,hsyzjsr,zgswjg_dm,
        ly,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
