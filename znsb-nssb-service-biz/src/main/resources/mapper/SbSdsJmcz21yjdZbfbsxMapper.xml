<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZbfbsxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sds.SbSdsJmcz21yjdZbfbsxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="xxbz" column="xxbz" jdbcType="VARCHAR"/>
            <result property="jehxxz" column="jehxxz" jdbcType="VARCHAR"/>
            <result property="ewbhgjz" column="ewbhgjz" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        xxbz,jehxxz,ewbhgjz,
        lrrq,xgrq,sjgsdq,
        sjtb_sj,lrrsfid,xgrsfid,
        ywqd_dm,sjcsdq
    </sql>
    <select id="queryBDA0611159A200000" resultType="java.util.Map">
        select t.sbuuid,t.jehxxz,t.ewbhgjz from SB_SDS_JMCZ_21YJD_ZBFBSX t
        WHERE t.xxbz = 'Y' and T.SBUUID = #{sbuuid}
    </select>
</mapper>
