<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.gy.ZnsbNssbShgjsbqkjlbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.gy.ZnsbNssbShgjsbqkjlbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbrwuuid" column="sbrwuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="gjbz" column="gjbz" jdbcType="CHAR"/>
            <result property="zxcs" column="zxcs" jdbcType="DECIMAL"/>
            <result property="sczxsj" column="sczxsj" jdbcType="TIMESTAMP"/>
            <result property="xybw" column="xybw" jdbcType="VARCHAR"/>
            <result property="cgbz" column="cgbz" jdbcType="CHAR"/>
            <result property="ycxx" column="ycxx" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="sbmxsjgjbz" column="sbmxsjgjbz" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbrwuuid,djxh,
        skssqq,skssqz,yzpzzl_dm,
        zsxm_dm,sbuuid,pzxh,
        gjbz,zxcs,sczxsj,
        xybw,cgbz,ycxx,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,nsrsbh,
        xzqhsz_dm,syuuid,sbmxsjgjbz
    </sql>
</mapper>
