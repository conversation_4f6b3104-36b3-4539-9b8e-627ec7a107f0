<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbStfdkdjskqkMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbStfdkdjskqkDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="stfkjywrmc" column="stfkjywrmc" jdbcType="VARCHAR"/>
            <result property="stfkjywrsbh" column="stfkjywrsbh" jdbcType="VARCHAR"/>
            <result property="stfdjxh" column="stfdjxh" jdbcType="DECIMAL"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="ssjkshm" column="ssjkshm" jdbcType="VARCHAR"/>
            <result property="ssjkskjrq" column="ssjkskjrq" jdbcType="TIMESTAMP"/>
            <result property="ysxfpmc" column="ysxfpmc" jdbcType="VARCHAR"/>
            <result property="sphfwssflbm" column="sphfwssflbm" jdbcType="DECIMAL"/>
            <result property="sysl" column="sysl" jdbcType="DECIMAL"/>
            <result property="wtjgshsl" column="wtjgshsl" jdbcType="DECIMAL"/>
            <result property="dsdjsk" column="dsdjsk" jdbcType="DECIMAL"/>
            <result property="wtjgshysxfpjsjg" column="wtjgshysxfpjsjg" jdbcType="DECIMAL"/>
            <result property="syslDesl" column="sysl_desl" jdbcType="DECIMAL"/>
            <result property="syslBlsl" column="sysl_blsl" jdbcType="DECIMAL"/>
            <result property="wtjgksxzqhDm" column="wtjgksxzqh_dm" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        stfkjywrmc,stfkjywrsbh,stfdjxh,
        zspm_dm,ssjkshm,ssjkskjrq,
        ysxfpmc,sphfwssflbm,sysl,
        wtjgshsl,dsdjsk,wtjgshysxfpjsjg,
        sysl_desl,sysl_blsl,wtjgksxzqh_dm,
        sjblbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
