<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqycwxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zdsy.ZnsbNssbZdsyqycwxxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zlbscjuuid" column="zlbscjuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="ewbhlbm" column="ewbhlbm" jdbcType="VARCHAR"/>
            <result property="ljxmmc" column="ljxmmc" jdbcType="VARCHAR"/>
            <result property="nd" column="nd" jdbcType="VARCHAR"/>
            <result property="bnlj" column="bnlj" jdbcType="DECIMAL"/>
            <result property="snlj" column="snlj" jdbcType="DECIMAL"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zlbscjuuid,ewbhxh,
        ewbhlbm,ljxmmc,nd,
        bnlj,snlj,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
