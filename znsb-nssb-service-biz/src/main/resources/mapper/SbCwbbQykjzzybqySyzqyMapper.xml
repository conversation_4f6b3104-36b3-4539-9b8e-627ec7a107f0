<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cwbb.SbCwbbQykjzzybqySyzqyMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cwbb.SbCwbbQykjzzybqySyzqyDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="zlbscjuuid" column="zlbscjuuid" jdbcType="VARCHAR"/>
        <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
        <result property="hmc" column="hmc" jdbcType="VARCHAR"/>
        <result property="bnsszbhgb" column="bnsszbhgb" jdbcType="DECIMAL"/>
        <result property="snyygj" column="snyygj" jdbcType="DECIMAL"/>
        <result property="snwfply" column="snwfply" jdbcType="DECIMAL"/>
        <result property="snsyzqyhj" column="snsyzqyhj" jdbcType="DECIMAL"/>
        <result property="bnzbgj" column="bnzbgj" jdbcType="DECIMAL"/>
        <result property="bnjkcg" column="bnjkcg" jdbcType="DECIMAL"/>
        <result property="bnyygj" column="bnyygj" jdbcType="DECIMAL"/>
        <result property="bnwfply" column="bnwfply" jdbcType="DECIMAL"/>
        <result property="bnsyzqyhj" column="bnsyzqyhj" jdbcType="DECIMAL"/>
        <result property="snsszbhgb" column="snsszbhgb" jdbcType="DECIMAL"/>
        <result property="snzbgj" column="snzbgj" jdbcType="DECIMAL"/>
        <result property="snjkcg" column="snjkcg" jdbcType="DECIMAL"/>
        <result property="bnqtzhsy" column="bnqtzhsy" jdbcType="DECIMAL"/>
        <result property="snqtzhsy" column="snqtzhsy" jdbcType="DECIMAL"/>
        <result property="qtqygjyxg1" column="qtqygjyxg_1" jdbcType="DECIMAL"/>
        <result property="qtqygjyxz1" column="qtqygjyxz_1" jdbcType="DECIMAL"/>
        <result property="qtqygjqt1" column="qtqygjqt_1" jdbcType="DECIMAL"/>
        <result property="qtqygjyxg2" column="qtqygjyxg_2" jdbcType="DECIMAL"/>
        <result property="qtqygjyxz2" column="qtqygjyxz_2" jdbcType="DECIMAL"/>
        <result property="qtqygjqt2" column="qtqygjqt_2" jdbcType="DECIMAL"/>
        <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
        <result property="bnzxcb" column="bnzxcb" jdbcType="DECIMAL"/>
        <result property="snzxcb" column="snzxcb" jdbcType="DECIMAL"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,zlbscjuuid,ewbhxh,
        hmc,bnsszbhgb,snyygj,
        snwfply,snsyzqyhj,bnzbgj,
        bnjkcg,bnyygj,bnwfply,
        bnsyzqyhj,snsszbhgb,snzbgj,
        snjkcg,bnqtzhsy,snqtzhsy,
        qtqygjyxg_1,qtqygjyxz_1,qtqygjqt_1,
        qtqygjyxg_2,qtqygjyxz_2,qtqygjqt_2,
        sjblbz,bnzxcb,snzxcb,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
