<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.lqzlsbjk.ZnsbDjNsrckzhzhxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.lqzlsbjk.ZnsbDjNsrckzhzhxx">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="ckzhuuid" column="ckzhuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="yhzhxzDm" column="yhzhxz_dm" jdbcType="CHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="zhmc" column="zhmc" jdbcType="VARCHAR"/>
            <result property="yhhbDm" column="yhhb_dm" jdbcType="CHAR"/>
            <result property="yhzh" column="yhzh" jdbcType="VARCHAR"/>
            <result property="hbszDm" column="hbsz_dm" jdbcType="CHAR"/>
            <result property="yhkhdjzh" column="yhkhdjzh" jdbcType="VARCHAR"/>
            <result property="ffrq" column="ffrq" jdbcType="TIMESTAMP"/>
            <result property="tszhbz" column="tszhbz" jdbcType="CHAR"/>
            <result property="khrq" column="khrq" jdbcType="TIMESTAMP"/>
            <result property="bgrq" column="bgrq" jdbcType="TIMESTAMP"/>
            <result property="zxrq" column="zxrq" jdbcType="TIMESTAMP"/>
            <result property="bz" column="bz" jdbcType="VARCHAR"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="yhyywdDm" column="yhyywd_dm" jdbcType="VARCHAR"/>
            <result property="sbjbjg" column="sbjbjg" jdbcType="VARCHAR"/>
            <result property="sbbm" column="sbbm" jdbcType="VARCHAR"/>
            <result property="sbfzhbz" column="sbfzhbz" jdbcType="CHAR"/>
            <result property="sbdjxh" column="sbdjxh" jdbcType="DECIMAL"/>
            <result property="djkskzhbz" column="djkskzhbz" jdbcType="CHAR"/>
            <result property="sxjszhbz" column="sxjszhbz" jdbcType="CHAR"/>
            <result property="sxjfzhbz" column="sxjfzhbz" jdbcType="CHAR"/>
            <result property="tfzhbz" column="tfzhbz" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="cktszhbz" column="cktszhbz" jdbcType="CHAR"/>
            <result property="khyhhh" column="khyhhh" jdbcType="VARCHAR"/>
            <result property="qsyhhh" column="qsyhhh" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="yyztDm" column="yyztDm" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,ckzhuuid,djxh,yhzhxz_dm,
        xzqhsz_dm,zhmc,yhhb_dm,
        yhzh,hbsz_dm,yhkhdjzh,
        ffrq,tszhbz,khrq,
        bgrq,zxrq,bz,
        yxqq,yxqz,yhyywd_dm,
        sbjbjg,sbbm,sbfzhbz,
        sbdjxh,djkskzhbz,sxjszhbz,
        sxjfzhbz,tfzhbz,yxbz,
        cktszhbz,khyhhh,qsyhhh,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,yyztDm
    </sql>

    <update id="updateCkzhbgByDjxh">
        update znsb_dj_nsrckzhzhxx set yxbz='N',xgrq = #{data.xgrq}, xgrsfid = #{data.xgrsfid} where djxh = #{data.djxh};
    </update>

    <update id="updateCkzhbgByDjxhYhzhUuid">
        update znsb_dj_nsrckzhzhxx set xgrq = #{data.xgrq}, xgrsfid = #{data.xgrsfid},uuid=#{data.ckzhuuid},ckzhuuid=#{data.ckzhuuid},yyzt_dm=#{data.yyztDm},yxbz=#{data.yxbz},yxqq=#{data.yxqq},yxqz=#{data.yxqz} where djxh = #{data.djxh} And yhzh = #{data.yhzh} and uuid=#{data.uuid};
    </update>

</mapper>
