<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.yjzt.ZnsbTzzxFzjgfztzMapper">

    <select id="queryFzjgfztz" resultType="com.css.znsb.nssb.pojo.domain.yjtz.ZnsbTzzxFzjgfztzDO">
        select * from znsb_tzzx_fzjgfztz
        where nsrsbh = #{params.nsrsbh}
        <if test = "params.sszqq != null and params.sszqq != ''" >
            and skssqq &gt;= STR_TO_DATE(#{params.sszqq},'%Y-%m-%d')
        </if>
        <if test = "params.sszqz != null and params.sszqz != ''" >
            and skssqz &lt;= STR_TO_DATE(#{params.sszqz},'%Y-%m-%d')
        </if>
    </select>
</mapper>
