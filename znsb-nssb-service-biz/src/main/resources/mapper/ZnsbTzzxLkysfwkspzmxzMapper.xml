<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxLkysfwkspzmxzMapper">

    <select id="dataCompareSum" resultType="java.math.BigDecimal">
        select sum(kdse)
        from znsb_tzzx_lkysfwkspzmxz
        where djxh = #{djxh}
          and sszq = #{sszq}
          and ly = '0'
    </select>

</mapper>