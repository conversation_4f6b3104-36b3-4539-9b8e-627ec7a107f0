<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cwbb.SbCwbbQykjzzybqyZcfzbwzxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cwbb.SbCwbbQykjzzybqyZcfzbwzxDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="zlbscjuuid" column="zlbscjuuid" jdbcType="VARCHAR"/>
        <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
        <result property="qmyeZc" column="qmye_zc" jdbcType="DECIMAL"/>
        <result property="snnmyeZc" column="snnmye_zc" jdbcType="DECIMAL"/>
        <result property="qyxmmc" column="qyxmmc" jdbcType="VARCHAR"/>
        <result property="qmyeQy" column="qmye_qy" jdbcType="DECIMAL"/>
        <result property="snnmyeQy" column="snnmye_qy" jdbcType="DECIMAL"/>
        <result property="byfseZc" column="byfse_zc" jdbcType="DECIMAL"/>
        <result property="byfseQy" column="byfse_qy" jdbcType="DECIMAL"/>
        <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="zcxmmc" column="zcxmmc" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,zlbscjuuid,ewbhxh,
        qmye_zc,snnmye_zc,qyxmmc,
        qmye_qy,snnmye_qy,byfse_zc,
        byfse_qy,sjblbz,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj,zcxmmc
    </sql>
</mapper>
