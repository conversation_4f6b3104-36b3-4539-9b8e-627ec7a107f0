<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.srhy.ZnsbNssbSrhyBzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.srhy.ZnsbNssbSrhyBzbDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
        <result property="qydmz" column="qydmz" jdbcType="VARCHAR"/>
        <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
        <result property="ssny" column="ssny" jdbcType="INTEGER"/>
        <result property="sjlx" column="sjlx" jdbcType="CHAR"/>
        <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
        <result property="ywmc" column="ywmc" jdbcType="VARCHAR"/>
        <result property="hmc" column="hmc" jdbcType="VARCHAR"/>
        <result property="xzbm" column="xzbm" jdbcType="VARCHAR"/>
        <result property="xmbm" column="xmbm" jdbcType="VARCHAR"/>
        <result property="slmc" column="slmc" jdbcType="VARCHAR"/>
        <result property="je" column="je" jdbcType="DECIMAL"/>
        <result property="se" column="se" jdbcType="DECIMAL"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>

    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,djxh,nsrsbh,
        nsrmc,qydmz,lrzx,
        ssny,sjlx,ewbhxh,
        ywmc,hmc,je,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,zbuuid,xzbm,xmbm,slmc
    </sql>
    <select id="queryHzxx" resultType="java.util.Map">
        select sum(je) as je, sum(se) as se, ywmc
        from znsb_nssb_srhy_bzb
        where ssny = #{ssny}
          and djxh = #{djxh}
        group by ywmc;
    </select>
    <select id="queryMxxx" resultType="java.util.Map">
        select ywmc, lrzx, sum(je) as je, sum(se) as se
        from znsb_nssb_srhy_bzb
        where ssny = #{ssny}
          and djxh = #{djxh}
        group by ywmc, lrzx;
    </select>
    <select id="queryAll" resultType="com.css.znsb.nssb.pojo.domain.srhy.ZnsbNssbSrhyBzbDO">
        select *
        from znsb_nssb_srhy_bzb
        where ssny = #{ssny}
          and djxh = #{djxh};
    </select>
    <select id="querySrhybxx" resultType="com.css.znsb.nssb.pojo.domain.srhy.ZnsbNssbSrhyBzbDO">
        select * from znsb_nssb_srhy_bzb where 1 = 1
        <if test="nsrsbh != null and nsrsbh != ''">
            and nsrsbh = #{nsrsbh}
        </if>
        <if test="qydm != null and qydm != ''">
            and qydmz = #{qydm}
        </if>
        <if test="ssny != null and ssny != ''">
            and ssny = #{ssny}
        </if>
    </select>
</mapper>
