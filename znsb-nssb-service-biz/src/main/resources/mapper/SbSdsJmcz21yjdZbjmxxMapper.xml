<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZbjmxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sds.SbSdsJmcz21yjdZbjmxxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="yhswsx" column="yhswsx" jdbcType="CHAR"/>
            <result property="zyywsrzb" column="zyywsrzb" jdbcType="DECIMAL"/>
            <result property="yhjmje" column="yhjmje" jdbcType="DECIMAL"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="ewbhgjz" column="ewbhgjz" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="jejslx" column="jejslx" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,yhswsx,zyywsrzb,
        yhjmje,ssjmxz_dm,ewbhgjz,
        lrrq,xgrq,sjgsdq,
        sjtb_sj,jejslx,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq
    </sql>
    <select id="queryBDA0611159A200000" resultType="java.util.Map">
        select t.sbuuid,         t.ewbhgjz,         t.ewbhxh,         t.ssjmxz_dm,
               t.yhswsx,         t.yhjmje
        from SB_SDS_JMCZ_21YJD_ZBJMXX t
        where t.sbuuid = #{sbuuid}
    </select>
</mapper>
