<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsyqyjqMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zdsy.ZnsbNssbZdsyqyjqDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zlbscjuuid" column="zlbscjuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="ewbhlbm" column="ewbhlbm" jdbcType="VARCHAR"/>
            <result property="hmc" column="hmc" jdbcType="VARCHAR"/>
            <result property="nd" column="nd" jdbcType="VARCHAR"/>
            <result property="jd" column="jd" jdbcType="CHAR"/>
            <result property="zk" column="zk" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="nd1" column="nd_1" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zlbscjuuid,ewbhxh,
        ewbhlbm,hmc,nd,
        jd,zk,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj,nd_1
    </sql>
</mapper>
