<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.znjsq.ZnsbNssbFjmkjbsNsywpdfabMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.znjsq.ZnsbNssbFjmkjbsNsywpdfabDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="kjsbbm" column="kjsbbm" jdbcType="CHAR"/>
            <result property="wtxx" column="wtxx" jdbcType="VARCHAR"/>
            <result property="famc" column="famc" jdbcType="VARCHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="syfw" column="syfw" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,kjsbbm,
        wtxx,famc,zfbz_1,
        zfr_dm,zfrq_1,syfw,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>

<!--    <update id="updateNsywpdjg">-->
<!--        update sb_fjmkjbs_nsywpdfab set-->
<!--        <if test="param.famc != null">-->
<!--            famc = #{param.famc}-->
<!--        </if>-->
<!--        <if test="param.zfbz_1 != null">-->
<!--            zfbz_1 = #{param.zfbz_1}-->
<!--        </if>-->

<!--        where UUID = #{fauuid} and zfbz_1 is null and djxh = #{djxh}-->
<!--    </update>-->

<!--    <select id="selectAllByDjxh" resultType="com.css.znsb.nssb.pojo.domain.znjsq.ZnsbNssbFjmkjbsNsywpdfabDO">-->
<!--        select UUID, djxh, KJSBBM, WTXX, famc, ZFBZ_1-->
<!--        from sb_fjmkjbs_nsywpdfab-->
<!--        where djxh = #{djxh}-->
<!--        and KJSBBM = #{kjsbbm}-->
<!--        <if test='"Y" == yxbz'>-->
<!--            and ZFBZ_1 is null-->
<!--        </if>-->
<!--    </select>-->
</mapper>
