<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.skjn.ZnsbNssbJksjgjMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.vo.jkkp.ZnsbNssbJksjgjVO">
            <id property="zsuuid" column="zsuuid" jdbcType="VARCHAR"/>
            <result property="yzpzxh" column="yzpzxh" jdbcType="DECIMAL"/>
            <result property="yzpzmxxh" column="yzpzmxxh" jdbcType="INTEGER"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="glyzpzmxxh" column="glyzpzmxxh" jdbcType="INTEGER"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="djzclxDm" column="djzclx_dm" jdbcType="CHAR"/>
            <result property="dwlsgxDm" column="dwlsgx_dm" jdbcType="CHAR"/>
            <result property="hssrtjlx" column="hssrtjlx" jdbcType="VARCHAR"/>
            <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="jkqx" column="jkqx" jdbcType="TIMESTAMP"/>
            <result property="jmse" column="jmse" jdbcType="DECIMAL"/>
            <result property="jsyj" column="jsyj" jdbcType="DECIMAL"/>
            <result property="jzjtskbz" column="jzjtskbz" jdbcType="CHAR"/>
            <result property="kjdjxh" column="kjdjxh" jdbcType="DECIMAL"/>
            <result property="kjjzrq" column="kjjzrq" jdbcType="TIMESTAMP"/>
            <result property="kmblgktsgzDm" column="kmblgktsgz_dm" jdbcType="VARCHAR"/>
            <result property="kssl" column="kssl" jdbcType="DECIMAL"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="nssbrq" column="nssbrq" jdbcType="TIMESTAMP"/>
            <result property="qrmxuuid" column="qrmxuuid" jdbcType="VARCHAR"/>
            <result property="rkrq" column="rkrq" jdbcType="TIMESTAMP"/>
            <result property="sbfsDm" column="sbfs_dm" jdbcType="CHAR"/>
            <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="skcllxDm" column="skcllx_dm" jdbcType="CHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="skssswjgDm" column="skssswjg_dm" jdbcType="CHAR"/>
            <result property="sksxDm" column="sksx_dm" jdbcType="CHAR"/>
            <result property="skzlDm" column="skzl_dm" jdbcType="CHAR"/>
            <result property="skzzfsDm" column="skzzfs_dm" jdbcType="CHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="ssglyDm" column="ssgly_dm" jdbcType="CHAR"/>
            <result property="sybh1" column="sybh_1" jdbcType="VARCHAR"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="yjkqx" column="yjkqx" jdbcType="TIMESTAMP"/>
            <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
            <result property="yjskztDm" column="yjskzt_dm" jdbcType="CHAR"/>
            <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
            <result property="yskzlDm" column="yskzl_dm" jdbcType="CHAR"/>
            <result property="yyzpzmxxh" column="yyzpzmxxh" jdbcType="INTEGER"/>
            <result property="yzclrq" column="yzclrq" jdbcType="TIMESTAMP"/>
            <result property="yzfsrq" column="yzfsrq" jdbcType="TIMESTAMP"/>
            <result property="yzgsrq" column="yzgsrq" jdbcType="TIMESTAMP"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="zsdlfsDm" column="zsdlfs_dm" jdbcType="CHAR"/>
            <result property="zsfsDm" column="zsfs_dm" jdbcType="CHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="zsswjgDm" column="zsswjg_dm" jdbcType="CHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="bz" column="bz" jdbcType="VARCHAR"/>
            <result property="cbsxDm" column="cbsx_dm" jdbcType="CHAR"/>
            <result property="czlxDm" column="czlx_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        zsuuid,yzpzxh,yzpzmxxh,
        yzpzzl_dm,glyzpzmxxh,djxh,
        djzclx_dm,dwlsgx_dm,hssrtjlx,
        hy_dm,jdxz_dm,jkqx,
        jmse,jsyj,jzjtskbz,
        kjdjxh,kjjzrq,kmblgktsgz_dm,
        kssl,lrr_dm,lrrq,
        nssbrq,qrmxuuid,rkrq,
        sbfs_dm,sbsx_dm_1,sjblbz,
        sjgsdq,sjtb_sj,skcllx_dm,
        skssqq,skssqz,skssswjg_dm,
        sksx_dm,skzl_dm,skzzfs_dm,
        sl_1,ssgly_dm,sybh_1,
        tzlx_dm,xgr_dm,xgrq,
        ybtse,yjkqx,yjse,
        yjskzt_dm,ynse,yskzl_dm,
        yyzpzmxxh,yzclrq,yzfsrq,
        yzgsrq,zgswskfj_dm,zsdlfs_dm,
        zsfs_dm,zspm_dm,zsswjg_dm,
        zsxm_dm,zszm_dm,bz,
        cbsx_dm,czlx_dm,ywqd_dm,
        sjcsdq,xgrsfid,lrrsfid
    </sql>
</mapper>
