<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.gy.ZnsbNssbFlzlscMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.gy.flzl.ZnsbNssbFlzlscDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sxuuid" column="sxuuid" jdbcType="VARCHAR"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="flzlDm" column="flzl_dm" jdbcType="VARCHAR"/>
            <result property="flzlmc" column="flzlmc" jdbcType="VARCHAR"/>
            <result property="wjm" column="wjm" jdbcType="VARCHAR"/>
            <result property="zluuid" column="zluuid" jdbcType="VARCHAR"/>
            <result property="sczt1" column="sczt_1" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sxuuid,swsx_dm,
        djxh,flzl_dm,flzlmc,
        wjm,zluuid,sczt_1,
        lrrq,xgrq,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq,
        sjtb_sj,sjgsdq
    </sql>
</mapper>
