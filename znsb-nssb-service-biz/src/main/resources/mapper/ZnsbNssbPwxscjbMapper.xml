<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbPwxscjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.hbs.ZnsbNssbPwxscjbDO">
            <id property="pwxsuuid" column="pwxsuuid" jdbcType="VARCHAR"/>
            <result property="dqswrwjcxxuuid" column="dqswrwjcxxuuid" jdbcType="VARCHAR"/>
            <result property="cpmc1" column="cpmc_1" jdbcType="VARCHAR"/>
            <result property="ylmc1" column="ylmc_1" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="mdzljsmc" column="mdzljsmc" jdbcType="VARCHAR"/>
            <result property="gymc" column="gymc" jdbcType="VARCHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="jsjsdw" column="jsjsdw" jdbcType="VARCHAR"/>
            <result property="gmdj" column="gmdj" jdbcType="VARCHAR"/>
            <result property="cwxs" column="cwxs" jdbcType="DECIMAL"/>
            <result property="pwxs" column="pwxs" jdbcType="DECIMAL"/>
            <result property="wrwdwDm" column="wrwdw_dm" jdbcType="VARCHAR"/>
            <result property="zywrwlbDm" column="zywrwlb_dm" jdbcType="VARCHAR"/>
            <result property="hbsjcxxuuid" column="hbsjcxxuuid" jdbcType="VARCHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        pwxsuuid,dqswrwjcxxuuid,cpmc_1,
        ylmc_1,zspm_dm,mdzljsmc,
        gymc,lrr_dm,xgr_dm,
        yxbz,jsjsdw,gmdj,
        cwxs,pwxs,wrwdw_dm,
        zywrwlb_dm,hbsjcxxuuid,zszm_dm,
        sjblbz,syuuid,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
