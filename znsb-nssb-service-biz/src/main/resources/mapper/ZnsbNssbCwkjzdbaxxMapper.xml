<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sbrw.ZnsbNssbCwkjzdbaxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbCwkjzdbaxxDO">
            <id property="cwkjzdbauuid" column="cwkjzdbauuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="cbhsffbz" column="cbhsffbz" jdbcType="VARCHAR"/>
            <result property="cbhsffDm" column="cbhsff_dm" jdbcType="VARCHAR"/>
            <result property="cbhsffmc" column="cbhsffmc" jdbcType="VARCHAR"/>
            <result property="cwkjzdbz" column="cwkjzdbz" jdbcType="VARCHAR"/>
            <result property="cwzdbz" column="cwzdbz" jdbcType="VARCHAR"/>
            <result property="dzyhptxffbz" column="dzyhptxffbz" jdbcType="VARCHAR"/>
            <result property="dzyhptxffDm" column="dzyhptxff_dm" jdbcType="CHAR"/>
            <result property="dzyhptxffmc" column="dzyhptxffmc" jdbcType="VARCHAR"/>
            <result property="kcyqzyqykfzczjffDm" column="kcyqzyqykfzczjff_dm" jdbcType="CHAR"/>
            <result property="kcyqzyqykfzczjffmc" column="kcyqzyqykfzczjffmc" jdbcType="VARCHAR"/>
            <result property="kcyqzyqykqqyzczhffDm" column="kcyqzyqykqqyzczhff_dm" jdbcType="CHAR"/>
            <result property="kcyqzyqykqqyzczhffmc" column="kcyqzyqykqqyzczhffmc" jdbcType="VARCHAR"/>
            <result property="kcyqzyqyktzctxffDm" column="kcyqzyqyktzctxff_dm" jdbcType="CHAR"/>
            <result property="kcyqzyqyktzctxffmc" column="kcyqzyqyktzctxffmc" jdbcType="VARCHAR"/>
            <result property="kjhsrjbbh" column="kjhsrjbbh" jdbcType="VARCHAR"/>
            <result property="kjhsrjbz" column="kjhsrjbz" jdbcType="VARCHAR"/>
            <result property="kjhsrjmc" column="kjhsrjmc" jdbcType="VARCHAR"/>
            <result property="kjhsrjqysj" column="kjhsrjqysj" jdbcType="TIMESTAMP"/>
            <result property="kjhsrjsjklxmc" column="kjhsrjsjklxmc" jdbcType="VARCHAR"/>
            <result property="kjzdzzDm" column="kjzdzz_dm" jdbcType="CHAR"/>
            <result property="kjzdzzmc" column="kjzdzzmc" jdbcType="VARCHAR"/>
            <result property="qycwzdDm" column="qycwzd_dm" jdbcType="CHAR"/>
            <result property="qycwzdmc" column="qycwzdmc" jdbcType="VARCHAR"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="zjffbz" column="zjffbz" jdbcType="VARCHAR"/>
            <result property="zjfsdlDm" column="zjfsdl_dm" jdbcType="CHAR"/>
            <result property="zjfsdlmc" column="zjfsdlmc" jdbcType="VARCHAR"/>
            <result property="zjfsxlDm" column="zjfsxl_dm" jdbcType="CHAR"/>
            <result property="zjfsxlmc" column="zjfsxlmc" jdbcType="VARCHAR"/>
            <result property="zlbsdlDm" column="zlbsdl_dm" jdbcType="CHAR"/>
            <result property="zlbsdlmc" column="zlbsdlmc" jdbcType="VARCHAR"/>
            <result property="zlbsxlDm" column="zlbsxl_dm" jdbcType="CHAR"/>
            <result property="zlbsxlmc" column="zlbsxlmc" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        cwkjzdbauuid,djxh,cbhsffbz,
        cbhsff_dm,cbhsffmc,cwkjzdbz,
        cwzdbz,dzyhptxffbz,dzyhptxff_dm,
        dzyhptxffmc,kcyqzyqykfzczjff_dm,kcyqzyqykfzczjffmc,
        kcyqzyqykqqyzczhff_dm,kcyqzyqykqqyzczhffmc,kcyqzyqyktzctxff_dm,
        kcyqzyqyktzctxffmc,kjhsrjbbh,kjhsrjbz,
        kjhsrjmc,kjhsrjqysj,kjhsrjsjklxmc,
        kjzdzz_dm,kjzdzzmc,qycwzd_dm,
        qycwzdmc,yxqq,yxqz,
        zjffbz,zjfsdl_dm,zjfsdlmc,
        zjfsxl_dm,zjfsxlmc,zlbsdl_dm,
        zlbsdlmc,zlbsxl_dm,zlbsxlmc,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
