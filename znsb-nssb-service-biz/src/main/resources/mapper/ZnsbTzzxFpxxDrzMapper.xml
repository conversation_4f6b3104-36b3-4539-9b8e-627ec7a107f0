<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.gjss.mapper.zzs.jxfp.jyk.ZnsbTzzxFpxxDrzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.gjss.pojo.domain.zzs.jxfp.ZnsbTzzxFpxxDrzDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="skssq" column="skssq" jdbcType="VARCHAR"/>
            <result property="je" column="je" jdbcType="DECIMAL"/>
            <result property="se" column="se" jdbcType="DECIMAL"/>
            <result property="jshj" column="jshj" jdbcType="DECIMAL"/>
            <result property="gxrzsj" column="gxrzsj" jdbcType="TIMESTAMP"/>
            <result property="hxytDm" column="hxyt_dm" jdbcType="VARCHAR"/>
            <result property="gxzt" column="gxzt" jdbcType="CHAR"/>
            <result property="bdkyyDm" column="bdkyy_dm" jdbcType="VARCHAR"/>
            <result property="yxdkje" column="yxdkje" jdbcType="DECIMAL"/>
            <result property="yxdkse" column="yxdkse" jdbcType="DECIMAL"/>
            <result property="dkpzlx" column="dkpzlx" jdbcType="CHAR"/>
            <result property="dkpzkjrq" column="dkpzkjrq" jdbcType="TIMESTAMP"/>
            <result property="dkpzhm" column="dkpzhm" jdbcType="VARCHAR"/>
            <result property="fplxDm" column="fplx_dm" jdbcType="CHAR"/>
            <result property="fppzDm" column="fppz_dm" jdbcType="CHAR"/>
            <result property="gfsbh" column="gfsbh" jdbcType="VARCHAR"/>
            <result property="gfmc" column="gfmc" jdbcType="VARCHAR"/>
            <result property="xfsbh" column="xfsbh" jdbcType="VARCHAR"/>
            <result property="xfmc" column="xfmc" jdbcType="VARCHAR"/>
            <result property="jkdwrnsrsbh" column="jkdwrnsrsbh" jdbcType="VARCHAR"/>
            <result property="jkdwrnsrmc" column="jkdwrnsrmc" jdbcType="VARCHAR"/>
            <result property="bkjnsrsbh" column="bkjnsrsbh" jdbcType="VARCHAR"/>
            <result property="bkjnsrmc" column="bkjnsrmc" jdbcType="VARCHAR"/>
            <result property="yqrzbz" column="yqrzbz" jdbcType="CHAR"/>
            <result property="znxzmbh" column="znxzmbh" jdbcType="VARCHAR"/>
            <result property="tbbz" column="tbbz" jdbcType="CHAR"/>
            <result property="ywfssj" column="ywfssj" jdbcType="TIMESTAMP"/>
            <result property="tdyslxDm" column="tdyslx_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,nsrsbh,
        nsrmc,skssq,je,
        se,jshj,gxrzsj,
        hxyt_dm,gxzt,bdkyy_dm,
        yxdkje,yxdkse,dkpzlx,
        dkpzkjrq,dkpzhm,fplx_dm,
        fppz_dm,gfsbh,gfmc,
        xfsbh,xfmc,jkdwrnsrsbh,
        jkdwrnsrmc,bkjnsrsbh,bkjnsrmc,
        yqrzbz,znxzmbh,tbbz,
        ywfssj,tdyslx_dm,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
