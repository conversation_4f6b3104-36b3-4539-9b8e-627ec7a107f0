<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.lqzlsbjk.ZnsbDjJwskyhzhxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.lqzlsbjk.ZnsbDjJwskyhzhxx">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="jwskyhuuid" column="jwskyhuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="lcslid" column="lcslid" jdbcType="CHAR"/>
            <result property="jwskyhhbDm" column="jwskyhhb_dm" jdbcType="CHAR"/>
            <result property="jwskyhmc" column="jwskyhmc" jdbcType="VARCHAR"/>
            <result property="jwskyhdz" column="jwskyhdz" jdbcType="VARCHAR"/>
            <result property="skrmc" column="skrmc" jdbcType="VARCHAR"/>
            <result property="skrzh" column="skrzh" jdbcType="VARCHAR"/>
            <result property="skrdz" column="skrdz" jdbcType="VARCHAR"/>
            <result property="hkyt" column="hkyt" jdbcType="VARCHAR"/>
            <result property="yjkbzDm" column="yjkbz_dm" jdbcType="CHAR"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="yyztDm" column="yyzt_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,jwskyhuuid,djxh,lcslid,
        jwskyhhb_dm,jwskyhmc,jwskyhdz,
        skrmc,skrzh,skrdz,
        hkyt,yjkbz_dm,yxqq,
        yxqz,yxbz,yyzt_dm,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>

    <update id="updateJwskyhzhxxByDjxh">
        update znsb_dj_jwskyhzhxx set yxbz='N',xgrq = #{data.xgrq}, xgrsfid = #{data.xgrsfid} where djxh = #{data.djxh};
    </update>

    <update id="updateJwskyhzhxxByDjxhYhzhUuid">
        update znsb_dj_jwskyhzhxx set xgrq = #{data.xgrq}, xgrsfid = #{data.xgrsfid},uuid=#{data.jwskyyhuuid},jwskyyhuuid=#{data.jwskyyhuuid},yyzt_dm=#{data.yyztDm} where djxh = #{data.djxh} And yhzh = #{data.yhzh} and uuid=#{data.uuid};
    </update>
</mapper>
