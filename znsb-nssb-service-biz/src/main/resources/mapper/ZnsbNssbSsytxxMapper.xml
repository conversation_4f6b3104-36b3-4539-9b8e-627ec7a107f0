<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.ZnsbNssbSsytxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zzsybnsr.ytxx.ZnsbNssbSsytxxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="sxytsj" column="sxytsj" jdbcType="TIMESTAMP"/>
            <result property="sslbDm" column="sslb_dm" jdbcType="VARCHAR"/>
            <result property="businessclob" column="businessclob" jdbcType="VARCHAR"/>
            <result property="cgbz" column="cgbz" jdbcType="CHAR"/>
            <result property="ycxx" column="ycxx" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,skssqq,
        skssqz,yzpzzl_dm,sxytsj,
        sslb_dm,businessclob,cgbz,
        ycxx,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
