<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.yjzt.ZnsbTzzxBpcjysjMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.yjtz.ZnsbTzzxBpcjysjDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="bbmc" column="bbmc" jdbcType="VARCHAR"/>
            <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
            <result property="gslxDm" column="gslx_dm" jdbcType="VARCHAR"/>
            <result property="yssj2" column="yssj_2" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,nsrsbh,nsrmc,
        sszq,bbmc,gsh_2,
        gslx_dm,yssj_2,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>

    <select id="getYyzbTbsj" resultType="com.css.znsb.nssb.pojo.domain.yjtz.ZnsbTzzxBpcjysjDO">
        select bpc.*
        from znsb_tzzx_Bpcjysj bpc
        left join (select * from znsb_tzzx_bpcjysj_rzb where gnmc = 'YYZB') rzb
            on bpc.uuid = rzb.zbuuid
        where rzb.sjtb_sj is null
          and bpc.bbmc like '%SM01%'
    </select>
    <select id="selectLrbPlb" resultType="com.css.znsb.nssb.pojo.domain.yjtz.ZnsbTzzxBpcjysjDO">
        select *
        from znsb_tzzx_bpcjysj
        where sszq = #{tjqjq}
        and gsh_2 = #{qydmz}
        and bbmc like '%SM03%'
        <if test='gslx != null and gslx == "1"'>
            and gslx_dm = 'GH'
        </if>
        <if test='gslx != null and gslx == "2"'>
            and gslx_dm in ('H','S','E')
        </if>
    </select>
    <select id="getByGshList" resultType="com.css.znsb.nssb.pojo.domain.yjtz.ZnsbTzzxBpcjysjDO">
        select
        *
        from
        znsb_tzzx_bpcjysj
        where
        gsh_2 =#{gsh}
        and (
        <foreach collection="sszqList" item="sszq" separator=" or ">
            sszq = #{sszq}
        </foreach>
        )

        and gslx_dm = #{gslx}

        and (
        <foreach collection="bbmcList" item="bbmc" separator=" or ">
            bbmc LIKE CONCAT('%', #{bbmc}, '%')
        </foreach>
        )

    </select>
</mapper>
