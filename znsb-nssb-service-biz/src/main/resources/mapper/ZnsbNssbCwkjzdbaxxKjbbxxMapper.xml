<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sbrw.ZnsbNssbCwkjzdbaxxKjbbxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbCwkjzdbaxxKjbbxxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="cwkjzdbauuid" column="cwkjzdbauuid" jdbcType="VARCHAR"/>
            <result property="cwbbzlDm" column="cwbbzl_dm" jdbcType="VARCHAR"/>
            <result property="cwbbzlmc" column="cwbbzlmc" jdbcType="VARCHAR"/>
            <result property="kjbbbsqxDm" column="kjbbbsqx_dm" jdbcType="VARCHAR"/>
            <result property="kjbbbsqxmc" column="kjbbbsqxmc" jdbcType="VARCHAR"/>
            <result property="bbbsqDm" column="bbbsq_dm" jdbcType="CHAR"/>
            <result property="bbbsqmc" column="bbbsqmc" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,cwkjzdbauuid,cwbbzl_dm,
        cwbbzlmc,kjbbbsqx_dm,kjbbbsqxmc,
        bbbsq_dm,bbbsqmc,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
