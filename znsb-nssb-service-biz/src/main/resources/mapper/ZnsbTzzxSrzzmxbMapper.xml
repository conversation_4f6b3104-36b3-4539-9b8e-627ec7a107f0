<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzmxbMapper">


    <select id="dataCompareSum" resultType="java.math.BigDecimal">
        select sum(bbje)
        from znsb_tzzx_srmxb
        where djxh = #{djxh}
        and sszq = #{sszq}
        and sl_1 = #{slv}
        and jsfs_dm_1 = #{jsfs}
        <if test="zsxm=='01'">
            and zsxm_dm in ('01','02')
        </if>
        <if test="zsxm=='03'">
            and zsxm_dm in ('03','04','05')
        </if>
        and ly = '0'
        and pztt !='乐企调账'
    </select>

    <select id="hqMxxxFz1" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select kmdm, djxh, sum(bbje) as je, sl_1 as sl, lrzx
        from znsb_tzzx_srmxb ztl
        where (kmdm like '6001%' or kmdm like '6051%' or kmdm in ('6041000000', '6601221600', '6601221700'))
          and ly = '0'
          and pztt !='乐企调账' and sszq =#{sszq}
        group by kmdm, djxh, sl_1, lrzx
    </select>

    <select id="hqMxxxFz2" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je, sl_1 as sl, lrzx, srlxdm
        from znsb_tzzx_srmxb ztl
        where pztt = '乐企调账'
          and sszq = #{sszq}
        group by djxh, sl_1, lrzx, srlxdm
    </select>

    <select id="hqMxxxFz3" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je, sl_1 as sl, lrzx
        from znsb_tzzx_srmxb ztl
        where ly = '0'
          and srlxdm in ('110', '120')
          and sszq = #{sszq}
        group by djxh, sl_1, lrzx
    </select>

    <select id="hqMxxxFz4" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je, sl_1 as sl, kjfp, lrzx, kmdm
        from znsb_tzzx_srmxb ztl
        where kmdm in ('2221010400', '2221080000')
          and ly = '0'
          and pztt !='乐企调账'  and sszq =#{sszq}
        group by djxh, sl_1, kjfp, lrzx, kmdm
    </select>

    <select id="hqMxxxFz5" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je, sl_1 as sl, lrzx
        from znsb_tzzx_srmxb ztl
        where ly = '0'
          and srlxdm = '130'
          and sszq = #{sszq}
        group by djxh, sl_1, lrzx
    </select>

    <select id="hqMxxxFz6" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je, sl_1 as sl, lrzx, srlxdm
        from znsb_tzzx_srmxb ztl
        where ly in ('1', '2')
          and sszq = #{sszq}
        group by djxh, sl_1, lrzx, srlxdm
    </select>
    <select id="hqMxxxFz7" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je, sl_1 as sl, lrzx
        from znsb_tzzx_srmxb ztl
        where ly = '4'
          and sszq = #{sszq}
        group by djxh, sl_1, lrzx
    </select>
    <select id="qylrzxFz" resultType="java.util.Map">
        select djxh, lrzx, gsh_2 as qydmz
        from znsb_tzzx_srmxb
        where sszq = #{sszq}
        group by djxh, lrzx, qydmz
    </select>

    <select id="querSrmxzdyXseandSe" parameterType="com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb" resultType="com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrmxbDTO">
        SELECT * FROM (
        (SELECT srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        '视同销售收入' as srlxmc,
        srzzb.nsrsbh,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1 AS sl1,
        SUM( srzzb.xse ) AS xse,
        SUM( srzzb.se ) AS se
        FROM
        znsb_tzzx_srmxb srzzb
        WHERE
        srzzb.sszq &gt;= #{vo.sszqq} AND srzzb.sszq &lt;= #{vo.sszqz} AND srzzb.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb.srlxdm = '130'
        AND ((srzzb.xse is not null and srzzb.xse  != '') or (srzzb.se is not null and srzzb.se  != ''))
        GROUP BY
        srzzb.djxh,
        srzzb.sszq,
        srzzb.srlxdm,
        srzzb.nsrsbh,
        srzzb.gsh_2,
        srzzb.lrzx,
        srzzb.jsfs_dm_1,
        srzzb.zsxm_dm,
        srzzb.sl_1)
        UNION ALL
        (SELECT srzzb1.djxh,
        srzzb1.sszq,
        srzzb1.srlxdm,
        '暂估收入' as srlxmc,
        srzzb1.nsrsbh,
        srzzb1.gsh_2,
        srzzb1.lrzx,
        srzzb1.jsfs_dm_1,
        srzzb1.zsxm_dm,
        srzzb1.sl_1 AS sl1,
        SUM( srzzb1.xse ) AS xse,
        SUM( srzzb1.se ) AS se
        FROM
        znsb_tzzx_srmxb srzzb1
        WHERE
        srzzb1.sszq &gt;= #{vo.sszqq} AND srzzb1.sszq &lt;= #{vo.sszqz} AND srzzb1.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb1.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb1.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb1.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb1.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb1.srlxdm = '140'
        AND ((srzzb1.xse is not null and srzzb1.xse  != '') or (srzzb1.se is not null and srzzb1.se  != ''))
        GROUP BY
        srzzb1.djxh,
        srzzb1.sszq,
        srzzb1.srlxdm,
        srzzb1.nsrsbh,
        srzzb1.gsh_2,
        srzzb1.lrzx,
        srzzb1.jsfs_dm_1,
        srzzb1.zsxm_dm,
        srzzb1.sl_1)
        UNION ALL
        (SELECT srzzb2.djxh,
        srzzb2.sszq,
        '180' AS srlxdm,
        '主营及其他业务收入' as srlxmc,
        srzzb2.nsrsbh,
        srzzb2.gsh_2,
        srzzb2.lrzx,
        srzzb2.jsfs_dm_1,
        srzzb2.zsxm_dm,
        srzzb2.sl_1 AS sl1,
        SUM( srzzb2.xse ) AS xse,
        SUM( srzzb2.se ) AS se
        FROM
        znsb_tzzx_srmxb srzzb2
        WHERE
        srzzb2.sszq &gt;= #{vo.sszqq} AND srzzb2.sszq &lt;= #{vo.sszqz} AND srzzb2.djxh = #{vo.djxh}
        <if test = "vo.jsfsDm1 != null and vo.jsfsDm1 != ''" >
            AND srzzb2.jsfs_dm_1 in
            <foreach collection="vo.jsfsDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.zsxmDm1 != null and vo.zsxmDm1 != ''" >
            AND srzzb2.zsxm_dm in
            <foreach collection="vo.zsxmDm1.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "vo.sl1 != null and vo.sl1 != ''" >
            AND srzzb2.sl_1 = #{vo.sl1}
        </if>
        <if test = "vo.lrzx != null and vo.lrzx != ''" >
            AND srzzb2.lrzx in
            <foreach collection="vo.lrzx.split(',')" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND srzzb2.srlxdm in('110','120')
        AND ((srzzb2.xse is not null and srzzb2.xse  != '') or (srzzb2.se is not null and srzzb2.se  != ''))
        GROUP BY
        srzzb2.djxh,
        srzzb2.sszq,
        srzzb2.srlxdm,
        srzzb2.nsrsbh,
        srzzb2.gsh_2,
        srzzb2.lrzx,
        srzzb2.jsfs_dm_1,
        srzzb2.zsxm_dm,
        srzzb2.sl_1
        )
        ) AS T
        ORDER BY sszq,gsh_2,lrzx,sl1,srlxdm,jsfs_dm_1,zsxm_dm
    </select>


    <select id="hqMxxxFzQt0sltz" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je, sl_1 as sl, lrzx
        from znsb_tzzx_srmxb ztl
        where (kmdm like '600101%' or kmdm like '600103%' or kmdm like '600106%' or kmdm like '600107%')
          and ly = '0'
          and sl_1 = 0
          and pztt !='乐企调账' and sszq =#{sszq}
        group by djxh, sl_1, lrzx

    </select>

    <select id="hqMxxxFzQt0sltz1" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je, sl_1 as sl, lrzx
        from znsb_tzzx_srmxb ztl
        where (kmdm like '605101%' or kmdm like '605102%' or kmdm like '605103%' or
               kmdm in ('6051050100', '6051050200', '6051060000'))
          and ly = '0'
          and sl_1 = 0
          and pztt !='乐企调账' and sszq =#{sszq}
        group by djxh, sl_1, lrzx

    </select>

    <select id="selectYYsr" resultType="com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO">
        select djxh, sum(bbje) as je
        from znsb_tzzx_srmxb ztl
        where kmdm in ('6001010100','6001010200','6041000000','6051010100','6051020100','6051030000','6051030100','6051010200','6051020200')
          and djxh = #{djxh}
          and sszq =#{sszq}
    </select>

</mapper>