<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxXxfpmxbZjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxXxfpmxbZjbDO">
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="fpdm" column="fpdm" jdbcType="VARCHAR"/>
            <result property="fphm" column="fphm" jdbcType="VARCHAR"/>
            <result property="fplxDm" column="fplx_dm" jdbcType="CHAR"/>
            <result property="fplxMc" column="fplx_mc" jdbcType="VARCHAR"/>
            <result property="je" column="je" jdbcType="DECIMAL"/>
            <result property="se" column="se" jdbcType="DECIMAL"/>
            <result property="jshj" column="jshj" jdbcType="DECIMAL"/>
            <result property="kprq" column="kprq" jdbcType="TIMESTAMP"/>
            <result property="gfsh" column="gfsh" jdbcType="VARCHAR"/>
            <result property="gfmc" column="gfmc" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="sfcezsfp" column="sfcezsfp" jdbcType="CHAR"/>
            <result property="jzjtkce" column="jzjtkce" jdbcType="DECIMAL"/>
            <result property="jzjtje" column="jzjtje" jdbcType="DECIMAL"/>
            <result property="jzjtse" column="jzjtse" jdbcType="DECIMAL"/>
            <result property="slcezsJrspzrJshj6" column="6slcezs_jrspzr_jshj" jdbcType="DECIMAL"/>
            <result property="slcezsFjrspzrJshj6" column="6slcezs_fjrspzr_jshj" jdbcType="DECIMAL"/>
            <result property="zsfsDm" column="zsfs_dm" jdbcType="CHAR"/>
            <result property="zsfsMc" column="zsfs_mc" jdbcType="VARCHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zsxmMc" column="zsxm_mc" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="zzid" column="zzid" jdbcType="VARCHAR"/>
            <result property="qysh" column="qysh" jdbcType="VARCHAR"/>
            <result property="qymc" column="qymc" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,fpdm,fphm,
        fplx_dm,fplx_mc,je,
        se,jshj,kprq,
        gfsh,gfmc,sszq,
        sfcezsfp,jzjtkce,jzjtje,
        jzjtse,6slcezs_jrspzr_jshj,6slcezs_fjrspzr_jshj,
        zsfs_dm,zsfs_mc,zsxm_dm,
        zsxm_mc,sl_1,lrrsfid,
        lrrq,xgrsfid,xgrq,
        scbz,zzid,qysh,
        qymc,ywqd_dm,sjcsdq,
        sjgsdq,sjtb_sj,scsj_1
    </sql>
</mapper>
