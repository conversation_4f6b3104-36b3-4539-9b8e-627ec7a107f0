<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb1BqxsqkmxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFb1BqxsqkmxSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="hmc" column="hmc" jdbcType="VARCHAR"/>
            <result property="kjskzzszyfpXse" column="kjskzzszyfp_xse" jdbcType="DECIMAL"/>
            <result property="kjskzzszyfpXxynse" column="kjskzzszyfp_xxynse" jdbcType="DECIMAL"/>
            <result property="kjqtfpXse" column="kjqtfp_xse" jdbcType="DECIMAL"/>
            <result property="kjqtfpXxynse" column="kjqtfp_xxynse" jdbcType="DECIMAL"/>
            <result property="wkjfpXse" column="wkjfp_xse" jdbcType="DECIMAL"/>
            <result property="wkjfpXxynse" column="wkjfp_xxynse" jdbcType="DECIMAL"/>
            <result property="nsjctzdxse" column="nsjctzdxse" jdbcType="DECIMAL"/>
            <result property="nsjctzXxynse" column="nsjctz_xxynse" jdbcType="DECIMAL"/>
            <result property="xse" column="xse" jdbcType="DECIMAL"/>
            <result property="hjXxynse" column="hj_xxynse" jdbcType="DECIMAL"/>
            <result property="jshj" column="jshj" jdbcType="DECIMAL"/>
            <result property="ysfwkcxmbqsjkcje" column="ysfwkcxmbqsjkcje" jdbcType="DECIMAL"/>
            <result property="kchHsmsxse" column="kch_hsmsxse" jdbcType="DECIMAL"/>
            <result property="kchXxynse" column="kch_xxynse" jdbcType="DECIMAL"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        ewbhxh,hmc,kjskzzszyfp_xse,
        kjskzzszyfp_xxynse,kjqtfp_xse,kjqtfp_xxynse,
        wkjfp_xse,wkjfp_xxynse,nsjctzdxse,
        nsjctz_xxynse,xse,hj_xxynse,
        jshj,ysfwkcxmbqsjkcje,kch_hsmsxse,
        kch_xxynse,
        lrrq,xgrq,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq,
        sjtb_sj,sjgsdq
    </sql>
</mapper>
