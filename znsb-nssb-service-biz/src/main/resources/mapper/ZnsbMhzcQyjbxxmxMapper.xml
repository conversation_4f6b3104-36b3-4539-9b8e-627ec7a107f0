<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.gy.ZnsbMhzcQyjbxxmxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.common.ZnsbMhzcQyjbxxmxDTO">
            <id property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="zcdzxzqhszDm" column="zcdzxzqhsz_dm" jdbcType="CHAR"/>
            <result property="scjydzxzqhszDm" column="scjydzxzqhsz_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="jdxzmc" column="jdxzmc" jdbcType="VARCHAR"/>
            <result property="scjydz" column="scjydz" jdbcType="VARCHAR"/>
            <result property="zcdz" column="zcdz" jdbcType="VARCHAR"/>
            <result property="zcdlxdh" column="zcdlxdh" jdbcType="VARCHAR"/>
            <result property="fddbrxm" column="fddbrxm" jdbcType="VARCHAR"/>
            <result property="djzclxDm" column="djzclx_dm" jdbcType="CHAR"/>
            <result property="djzclxmc" column="djzclxmc" jdbcType="VARCHAR"/>
            <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
            <result property="hymc" column="hymc" jdbcType="VARCHAR"/>
            <result property="kzztdjlxDm" column="kzztdjlx_dm" jdbcType="CHAR"/>
            <result property="kzztdjlxmc" column="kzztdjlxmc" jdbcType="VARCHAR"/>
            <result property="zgswjDm" column="zgswj_dm" jdbcType="CHAR"/>
            <result property="swjgmc" column="swjgmc" jdbcType="VARCHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="zgswskfjmc" column="zgswskfjmc" jdbcType="VARCHAR"/>
            <result property="cwfzrxm" column="cwfzrxm" jdbcType="VARCHAR"/>
            <result property="bsrxm" column="bsrxm" jdbcType="VARCHAR"/>
            <result property="zfjglxDm" column="zfjglx_dm" jdbcType="CHAR"/>
            <result property="zzsqylxDm" column="zzsqylx_dm" jdbcType="CHAR"/>
            <result property="nsrztDm" column="nsrzt_dm" jdbcType="CHAR"/>
            <result property="kqccsztdjbz" column="kqccsztdjbz" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="fddbrsfzjlxDm" column="fddbrsfzjlx_dm" jdbcType="CHAR"/>
            <result property="fddbrsfzjhm" column="fddbrsfzjhm" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,nsrsbh,nsrmc,
        zcdzxzqhsz_dm,scjydzxzqhsz_dm,jdxz_dm,
        jdxzmc,scjydz,zcdz,
        zcdlxdh,fddbrxm,djzclx_dm,
        djzclxmc,hy_dm,hymc,
        kzztdjlx_dm,kzztdjlxmc,zgswj_dm,
        swjgmc,zgswskfj_dm,zgswskfjmc,
        cwfzrxm,bsrxm,zfjglx_dm,
        zzsqylx_dm,nsrzt_dm,kqccsztdjbz,
        lrrq,xgrq,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq,
        sjtb_sj,sjgsdq,fddbrsfzjlx_dm,
        fddbrsfzjhm
    </sql>
</mapper>
