<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zbtj.ZnsbNssbZbtjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="gsh1" column="gsh_1" jdbcType="VARCHAR"/>
            <result property="gsmc" column="gsmc" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="xzqhmc" column="xzqhmc" jdbcType="VARCHAR"/>
            <result property="tjqjq" column="tjqjq" jdbcType="CHAR"/>
            <result property="tjqjz" column="tjqjz" jdbcType="CHAR"/>
            <result property="zbbm" column="zbbm" jdbcType="VARCHAR"/>
            <result property="zbmc2" column="zbmc_2" jdbcType="VARCHAR"/>
            <result property="jehj" column="jehj" jdbcType="DECIMAL"/>
            <result property="bz" column="bz" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="jguuid1" column="jguuid_1" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,gsh_1,gsmc,
        nsrsbh,lrzx,xzqhsz_dm,
        xzqhmc,tjqjq,tjqjz,
        zbbm,zbmc_2,jehj,
        bz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,
        jguuid_1
    </sql>
    <select id="selectJyfx" resultType="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
        select sum(jehj) as jehj,tjqjq
        from ${tablename}
        where tjqjq &gt;= #{tjqjq}
        and tjqjz &lt;= #{tjqjz}
        <if test="zbbm != null and zbbm != ''">
            and zbbm = #{zbbm}
        </if>
        <if test="zbbm == null or zbbm == ''">
            and zbbm in('ZC0004','SR0001','QTZHSY0002','FY0001','QTZHSY0003','QTZHSY0004','SJ0002')
        </if>
        and jguuid_1 in
        <foreach item="item" index="index" collection="jguuidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="xzqhszDm != null and xzqhszDm != ''">
            AND xzqhsz_dm = #{xzqhszDm}
        </if>
        group by tjqjq
    </select>
    <select id="selectJyfxAll" resultType="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
        select <include refid="Base_Column_List"/>
        from ${tablename}
        where tjqjq &gt;= #{tjqjq}
        and tjqjz &lt;= #{tjqjz}
        <if test="zbbm != null and zbbm != ''">
            and zbbm = #{zbbm}
        </if>
        <if test="zbbm == null or zbbm == ''">
            and zbbm in('ZC0004','SR0001','QTZHSY0002','FY0001','QTZHSY0003','QTZHSY0004','SJ0002')
        </if>
        and jguuid_1 in
        <foreach item="item" index="index" collection="jguuidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="xzqhszDm != null and xzqhszDm != ''">
            AND xzqhsz_dm = #{xzqhszDm}
        </if>
    </select>
    <select id="selectPmxxByDq" resultType="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
        select sum(jehj) as jehj,xzqhsz_dm,xzqhmc
        from znsb_nssb_zbtjb
        where tjqjq &gt;= #{tjqjq}
        and tjqjz &lt;= #{tjqjz}
        and zbbm = #{zbbm}
        and jguuid_1 in
        <foreach item="item" index="index" collection="jguuidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        group by xzqhsz_dm,xzqhmc
        order by jehj desc
    </select>
    <select id="selectPmxxByQy" resultType="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
        select sum(jehj) as jehj,jguuid_1,gsmc
        from znsb_nssb_zbtjb
        where tjqjq &gt;= #{tjqjq}
        and tjqjz &lt;= #{tjqjz}
        and zbbm = #{zbbm}
        and jguuid_1 in
        <foreach item="item" index="index" collection="jguuidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        group by jguuid_1,gsmc
        order by jehj desc
    </select>
    <select id="selectSzfb" resultType="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
        select sum(jehj) as jehj,zbbm
        from znsb_nssb_zbtjb
        where zbbm in('FZ0002','FZ0003','FZ0004','FZ0017','FZ0018','FZ0019','FZ0020','FZ0021','FZ0022')
        and tjqjq &gt;= #{tjqjq}
        and tjqjz &lt;= #{tjqjz}
        and jguuid_1 in
        <foreach item="item" index="index" collection="jguuidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="xzqhszDm != null and xzqhszDm != ''">
            AND xzqhsz_dm = #{xzqhszDm}
        </if>
        group by zbbm
    </select>
    <select id="selectSzfbAll" resultType="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
        select <include refid="Base_Column_List"/>
        from znsb_nssb_zbtjb
        where zbbm in('FZ0002','FZ0003','FZ0004','FZ0017','FZ0018','FZ0019','FZ0020','FZ0021','FZ0022')
        and tjqjq &gt;= #{tjqjq}
        and tjqjz &lt;= #{tjqjz}
        and jguuid_1 in
        <foreach item="item" index="index" collection="jguuidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="xzqhszDm != null and xzqhszDm != ''">
            AND xzqhsz_dm = #{xzqhszDm}
        </if>
    </select>
    <select id="selectsjfbAll" resultType="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
        select <include refid="Base_Column_List"/>
        from znsb_nssb_zbtjb
        where zbbm in('SJ0003','SJ0004','SJ0006','SJ0007','SJ0008','SJ0009','SJ0010','SJ0011')
        and tjqjq &gt;= #{tjqjq}
        and tjqjz &lt;= #{tjqjz}
        and jguuid_1 in
        <foreach item="item" index="index" collection="jguuidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="xzqhszDm != null and xzqhszDm != ''">
            AND xzqhsz_dm = #{xzqhszDm}
        </if>
    </select>
    <select id="selectSjfb" resultType="com.css.znsb.nssb.pojo.domain.zbtj.ZnsbNssbZbtjbDO">
        select sum(jehj) as jehj,zbbm
        from znsb_nssb_zbtjb
        where zbbm in('SJ0003','SJ0004','SJ0006','SJ0007','SJ0008','SJ0009','SJ0010','SJ0011')
        and tjqjq &gt;= #{tjqjq}
        and tjqjz &lt;= #{tjqjz}
        and jguuid_1 in
        <foreach item="item" index="index" collection="jguuidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="xzqhszDm != null and xzqhszDm != ''">
            AND xzqhsz_dm = #{xzqhszDm}
        </if>
        group by zbbm
    </select>

</mapper>
