<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbZnsbCxsYsjmxzxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbNssbCxsYsjmxzxxDo">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="ysuuid" column="ysuuid" jdbcType="VARCHAR"/>
            <result property="jmmj" column="jmmj" jdbcType="DECIMAL"/>
            <result property="yjmsje1" column="yjmsje_1" jdbcType="DECIMAL"/>
            <result property="jmsxmmc1" column="jmsxmmc_1" jdbcType="VARCHAR"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="jmfsDm" column="jmfs_dm" jdbcType="VARCHAR"/>
            <result property="jmsl" column="jmsl" jdbcType="DECIMAL"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="jmqxq" column="jmqxq" jdbcType="TIMESTAMP"/>
            <result property="jmqxz" column="jmqxz" jdbcType="TIMESTAMP"/>
            <result property="jmszjsr" column="jmszjsr" jdbcType="DECIMAL"/>
            <result property="jmed" column="jmed" jdbcType="DECIMAL"/>
            <result property="jmfd" column="jmfd" jdbcType="DECIMAL"/>
            <result property="jmzlxDm" column="jmzlx_dm" jdbcType="VARCHAR"/>
            <result property="jmslxDm" column="jmslx_dm" jdbcType="VARCHAR"/>
            <result property="jmsfcyz" column="jmsfcyz" jdbcType="DECIMAL"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ftdqybz" column="ftdqybz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,ysuuid,jmmj,
        yjmsje_1,jmsxmmc_1,ssjmxz_dm,
        jmfs_dm,jmsl,yxqq,
        yxqz,yxbz,lrr_dm,
        lrrq,xgr_dm,xgrq,
        sjgsdq,sjtb_sj,jmqxq,
        jmqxz,jmszjsr,jmed,
        jmfd,jmzlx_dm,jmslx_dm,
        jmsfcyz,swsx_dm,sjblbz,
        ftdqybz,ywqd_dm,sjcsdq,
        xgrsfid,lrrsfid
    </sql>
</mapper>
