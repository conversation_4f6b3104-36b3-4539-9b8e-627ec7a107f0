<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsyjsb.SbZzsyjnssbYwbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zzsyjsb.SbZzsyjnssbYwbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="syybjsff" column="syybjsff" jdbcType="CHAR"/>
            <result property="xmbh" column="xmbh" jdbcType="VARCHAR"/>
            <result property="xmmc" column="xmmc" jdbcType="VARCHAR"/>
            <result property="xmdz" column="xmdz" jdbcType="VARCHAR"/>
            <result property="zxsb" column="zxsb" jdbcType="CHAR"/>
            <result property="dlr" column="dlr" jdbcType="VARCHAR"/>
            <result property="dlrdz" column="dlrdz" jdbcType="VARCHAR"/>
            <result property="slr" column="slr" jdbcType="VARCHAR"/>
            <result property="slrq" column="slrq" jdbcType="TIMESTAMP"/>
            <result property="slswjg" column="slswjg" jdbcType="CHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="ybrbz" column="ybrbz" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="bqybtsecjs" column="bqybtsecjs" jdbcType="DECIMAL"/>
            <result property="bqybtsejyfj" column="bqybtsejyfj" jdbcType="DECIMAL"/>
            <result property="bqybtsedfjyfj" column="bqybtsedfjyfj" jdbcType="DECIMAL"/>
            <result property="wcjyhdssglzmbh" column="wcjyhdssglzmbh" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,djxh,
        skssqq,skssqz,syybjsff,
        xmbh,xmmc,xmdz,
        zxsb,dlr,dlrdz,
        slr,slrq,slswjg,
        pzxh,ybrbz,sjblbz,
        bqybtsecjs,bqybtsejyfj,bqybtsedfjyfj,
        wcjyhdssglzmbh,ywqd_dm,lrrq,
        lrrsfid,xgrq,sjcsdq,
        sjgsdq,xgrsfid,sjtb_sj
    </sql>
</mapper>
