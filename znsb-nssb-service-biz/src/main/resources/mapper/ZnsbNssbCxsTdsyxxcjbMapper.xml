<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDOMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDo">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrlx" column="nsrlx" jdbcType="CHAR"/>
            <result property="tdsybh" column="tdsybh" jdbcType="VARCHAR"/>
            <result property="dh1" column="dh_1" jdbcType="VARCHAR"/>
            <result property="tdsyzbh" column="tdsyzbh" jdbcType="VARCHAR"/>
            <result property="tdmc1" column="tdmc_1" jdbcType="VARCHAR"/>
            <result property="tdsyqrnsrsbh" column="tdsyqrnsrsbh" jdbcType="VARCHAR"/>
            <result property="tdsyqrmc" column="tdsyqrmc" jdbcType="VARCHAR"/>
            <result property="tdxzDm" column="tdxz_dm" jdbcType="CHAR"/>
            <result property="tdqdfsDm" column="tdqdfs_dm" jdbcType="CHAR"/>
            <result property="tdytDm" column="tdyt_dm" jdbcType="CHAR"/>
            <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="tdzldz" column="tdzldz" jdbcType="VARCHAR"/>
            <result property="zytdmj1" column="zytdmj1" jdbcType="DECIMAL"/>
            <result property="tddj" column="tddj" jdbcType="DECIMAL"/>
            <result property="qzqdtdsyqzfje" column="qzqdtdsyqzfje" jdbcType="DECIMAL"/>
            <result property="qztdkfcb" column="qztdkfcb" jdbcType="DECIMAL"/>
            <result property="csqdsj" column="csqdsj" jdbcType="TIMESTAMP"/>
            <result property="nsywzzsj" column="nsywzzsj" jdbcType="TIMESTAMP"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="lsgx" column="lsgx" jdbcType="VARCHAR"/>
            <result property="slr" column="slr" jdbcType="VARCHAR"/>
            <result property="slrq" column="slrq" jdbcType="TIMESTAMP"/>
            <result property="dlrsfzjhm1" column="dlrsfzjhm_1" jdbcType="VARCHAR"/>
            <result property="slswjg" column="slswjg" jdbcType="CHAR"/>
            <result property="tbrq" column="tbrq" jdbcType="TIMESTAMP"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="djzclxDm" column="djzclx_dm" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="tdsyqrdjxh" column="tdsyqrdjxh" jdbcType="DECIMAL"/>
            <result property="czrsj" column="czrsj" jdbcType="TIMESTAMP"/>
            <result property="zxrq" column="zxrq" jdbcType="TIMESTAMP"/>
            <result property="pkbz" column="pkbz" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="dlrsfzjzlDm1" column="dlrsfzjzl_dm_1" jdbcType="CHAR"/>
            <result property="dlr" column="dlr" jdbcType="VARCHAR"/>
            <result property="tbr" column="tbr" jdbcType="VARCHAR"/>
            <result property="bdcdyh" column="bdcdyh" jdbcType="VARCHAR"/>
            <result property="dljgqz" column="dljgqz" jdbcType="VARCHAR"/>
            <result property="jbrsfzjzlDm" column="jbrsfzjzl_dm" jdbcType="VARCHAR"/>
            <result property="sfyybdcdydm" column="sfyybdcdydm" jdbcType="CHAR"/>
            <result property="bdcqzlx" column="bdcqzlx" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="bdcqzlxDm" column="bdcqzlx_dm" jdbcType="VARCHAR"/>
            <result property="ycyy" column="ycyy" jdbcType="VARCHAR"/>
            <result property="bgzt" column="bgzt" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,syuuid,djxh,
        nsrsbh,nsrlx,tdsybh,
        dh_1,tdsyzbh,tdmc_1,
        tdsyqrnsrsbh,tdsyqrmc,tdxz_dm,
        tdqdfs_dm,tdyt_dm,hy_dm,
        xzqhsz_dm,jdxz_dm,zgswskfj_dm,
        tdzldz,zytdmj1,tddj,
        qzqdtdsyqzfje,qztdkfcb,csqdsj,
        nsywzzsj,yxqq,yxqz,
        lsgx,slr,slrq,
        dlrsfzjhm_1,slswjg,tbrq,
        yxbz,xgr_dm,xgrq,
        djzclx_dm,sjgsdq,lrr_dm,
        lrrq,sjtb_sj,tdsyqrdjxh,
        czrsj,zxrq,pkbz,
        sjblbz,dlrsfzjzl_dm_1,dlr,
        tbr,bdcdyh,dljgqz,
        jbrsfzjzl_dm,sfyybdcdydm,bdcqzlx,
        ywqd_dm,sjcsdq,xgrsfid,
        lrrsfid,bdcqzlx_dm,ycyy,
        bgzt
    </sql>
</mapper>
