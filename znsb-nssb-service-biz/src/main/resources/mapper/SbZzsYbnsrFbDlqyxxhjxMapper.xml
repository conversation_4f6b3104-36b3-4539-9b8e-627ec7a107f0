<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDlqyxxhjxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbDlqyxxhjxSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="bnljjnzzs" column="bnljjnzzs" jdbcType="DECIMAL"/>
            <result property="bqqjzzs" column="bqqjzzs" jdbcType="DECIMAL"/>
            <result property="bqyjzzs" column="bqyjzzs" jdbcType="DECIMAL"/>
            <result property="bqyjzzs1" column="bqyjzzs_1" jdbcType="DECIMAL"/>
            <result property="bqsjdkjx" column="bqsjdkjx" jdbcType="DECIMAL"/>
            <result property="zr" column="zr" jdbcType="DECIMAL"/>
            <result property="fzcss" column="fzcss" jdbcType="DECIMAL"/>
            <result property="mshwy" column="mshwy" jdbcType="DECIMAL"/>
            <result property="bqfsjx" column="bqfsjx" jdbcType="DECIMAL"/>
            <result property="deslhyzl" column="deslhyzl" jdbcType="DECIMAL"/>
            <result property="ysJwfy" column="ys_jwfy" jdbcType="DECIMAL"/>
            <result property="bzsJwfy" column="bzs_jwfy" jdbcType="DECIMAL"/>
            <result property="sdsr" column="sdsr" jdbcType="DECIMAL"/>
            <result property="dj" column="dj" jdbcType="DECIMAL"/>
            <result property="xsdl" column="xsdl" jdbcType="DECIMAL"/>
            <result property="slsdljt" column="slsdljt" jdbcType="VARCHAR"/>
            <result property="qylxDm" column="qylx_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        ewbhxh,bnljjnzzs,bqqjzzs,
        bqyjzzs,bqyjzzs_1,bqsjdkjx,
        zr,fzcss,mshwy,
        bqfsjx,deslhyzl,ys_jwfy,
        bzs_jwfy,sdsr,dj,
        xsdl,slsdljt,qylx_dm,
        lrrq,xgrq,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq,
        sjtb_sj,sjgsdq
    </sql>
</mapper>
