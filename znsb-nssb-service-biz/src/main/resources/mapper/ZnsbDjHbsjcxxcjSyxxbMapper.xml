<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbDjHbsjcxxcjSyxxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.hbs.ZnsbDjHbsjcxxcjSyxxbDO">
            <id property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="hbsjcxxuuid" column="hbsjcxxuuid" jdbcType="VARCHAR"/>
            <result property="pwxkzbh" column="pwxkzbh" jdbcType="VARCHAR"/>
            <result property="scjydz" column="scjydz" jdbcType="VARCHAR"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="zywrwlbDm" column="zywrwlb_dm" jdbcType="VARCHAR"/>
            <result property="pfkbh" column="pfkbh" jdbcType="VARCHAR"/>
            <result property="pfkmc" column="pfkmc" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="pfkwz" column="pfkwz" jdbcType="VARCHAR"/>
            <result property="wd" column="wd" jdbcType="DECIMAL"/>
            <result property="pffsDm" column="pffs_dm" jdbcType="CHAR"/>
            <result property="wspfqxDm" column="wspfqx_dm" jdbcType="CHAR"/>
            <result property="pfklxDm" column="pfklx_dm" jdbcType="CHAR"/>
            <result property="dqwrwpfklbDm" column="dqwrwpfklb_dm" jdbcType="CHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="jd_2" column="jd_2" jdbcType="DECIMAL"/>
            <result property="syyxqq" column="syyxqq" jdbcType="TIMESTAMP"/>
            <result property="syyxqz" column="syyxqz" jdbcType="TIMESTAMP"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="hgbhssybh" column="hgbhssybh" jdbcType="VARCHAR"/>
            <result property="gjhbjgDm" column="gjhbjg_dm" jdbcType="CHAR"/>
            <result property="xkzfbwybm" column="xkzfbwybm" jdbcType="VARCHAR"/>
            <result property="wbjhpfkuuid" column="wbjhpfkuuid" jdbcType="VARCHAR"/>
            <result property="pfkwybm" column="pfkwybm" jdbcType="VARCHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="wrwpfljsffDm" column="wrwpfljsff_dm" jdbcType="CHAR"/>
            <result property="fzspmDm" column="fzspm_dm" jdbcType="CHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="sbbz" column="sbbz" jdbcType="CHAR"/>
            <result property="pwxkzxxuuid" column="pwxkzxxuuid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        syuuid,hbsjcxxuuid,pwxkzbh,
        scjydz,yxqq,yxqz,
        zywrwlb_dm,pfkbh,pfkmc,
        xzqhsz_dm,jdxz_dm,pfkwz,
        wd,pffs_dm,wspfqx_dm,
        pfklx_dm,dqwrwpfklb_dm,zgswskfj_dm,
        jd_2,syyxqq,syyxqz,
        yxbz,lrr_dm,xgr_dm,
        hgbhssybh,gjhbjg_dm,xkzfbwybm,
        wbjhpfkuuid,pfkwybm,sjblbz,
        wrwpfljsff_dm,fzspm_dm,zspm_dm,
        zszm_dm,sbbz,pwxkzxxuuid,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
