<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.SbSdsFzjgfpbFzjgMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sds.SbSdsFzjgfpbFzjgDO">
            <id property="fzjgfpbfzjguuid" column="fzjgfpbfzjguuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="zjgnsrsbh" column="zjgnsrsbh" jdbcType="VARCHAR"/>
            <result property="zjgdjxh" column="zjgdjxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="fzjgmc" column="fzjgmc" jdbcType="VARCHAR"/>
            <result property="fzjglxlb" column="fzjglxlb" jdbcType="VARCHAR"/>
            <result property="srze" column="srze" jdbcType="DECIMAL"/>
            <result property="gzze" column="gzze" jdbcType="DECIMAL"/>
            <result property="zcze" column="zcze" jdbcType="DECIMAL"/>
            <result property="fpbl" column="fpbl" jdbcType="DECIMAL"/>
            <result property="fzjgfpse" column="fzjgfpse" jdbcType="DECIMAL"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="sfxsdfjm" column="sfxsdfjm" jdbcType="VARCHAR"/>
            <result property="xsdfjmje" column="xsdfjmje" jdbcType="DECIMAL"/>
            <result property="xsdfjmfd" column="xsdfjmfd" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="fzjgxsqyxyhqk" column="fzjgxsqyxyhqk" jdbcType="CHAR"/>
            <result property="zgswjDm" column="zgswj_dm" jdbcType="CHAR"/>
            <result property="zgswjgDm" column="zgswjg_dm" jdbcType="CHAR"/>
            <result property="zgswjgmc" column="zgswjgmc" jdbcType="VARCHAR"/>
            <result property="jzfd8wjd" column="jzfd_8wjd" jdbcType="DECIMAL"/>
            <result property="nsrztDm" column="nsrzt_dm" jdbcType="CHAR"/>
            <result property="fzjgqnljfpje" column="fzjgqnljfpje" jdbcType="DECIMAL"/>
            <result property="xsmzdfyhfd" column="xsmzdfyhfd" jdbcType="DECIMAL"/>
            <result property="fzjgqnljyxsmzdfyhje" column="fzjgqnljyxsmzdfyhje" jdbcType="DECIMAL"/>
            <result property="fzjgqnljyxsmzdfyhje1" column="fzjgqnljyxsmzdfyhje_1" jdbcType="DECIMAL"/>
            <result property="ymzdfyhtzfpje" column="ymzdfyhtzfpje" jdbcType="DECIMAL"/>
            <result property="sjfpse" column="sjfpse" jdbcType="DECIMAL"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        fzjgfpbfzjguuid,pzxh,sbuuid,
        zjgnsrsbh,zjgdjxh,nsrsbh,
        djxh,fzjgmc,fzjglxlb,
        srze,gzze,zcze,
        fpbl,fzjgfpse,zfbz_1,
        zfrq_1,zfr_dm,lrr_dm,
        xgr_dm,sfxsdfjm,xsdfjmje,
        xsdfjmfd,sjblbz,fzjgxsqyxyhqk,
        zgswj_dm,zgswjg_dm,zgswjgmc,
        jzfd_8wjd,nsrzt_dm,fzjgqnljfpje,
        xsmzdfyhfd,fzjgqnljyxsmzdfyhje,fzjgqnljyxsmzdfyhje_1,
        ymzdfyhtzfpje,sjfpse,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
    <select id="queryBDA0611033A202000" resultType="java.util.Map">
        select t.nsrsbh as fzjgnsrsbh ,t.fzjgmc,t.srze as fzjgsrze,t.gzze as fzjggzze,
               t.zcze as fzjgzcze,t.fpbl as fpbl ,t.xsdfjmfd , t.xsdfjmje,t.fzjgfpse
        from sb_sds_fzjgfpb_fzjg t where sbuuid = #{sbuuid}
    </select>
</mapper>
