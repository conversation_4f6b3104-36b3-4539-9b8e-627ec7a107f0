<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.gjss.mapper.yhs.yyzb.ZnsbTzzxBpcjysjRzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.gjss.pojo.domain.yhs.yyzb.ZnsbTzzxBpcjysjRzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
            <result property="gslxDm" column="gslx_dm" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="gnmc" column="gnmc" jdbcType="VARCHAR"/>
            <result property="ywzt" column="ywzt" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,nsrsbh,
        nsrmc,gsh_2,gslx_dm,
        sszq,gnmc,ywzt,
        sjtb_sj
    </sql>
</mapper>
