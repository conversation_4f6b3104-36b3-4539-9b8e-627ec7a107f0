<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.yhstz.ZnsbTzzxYyzbtz1Mapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.yhstz.ZnsbTzzxYyzbtz1DO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="ssnd" column="ssnd" jdbcType="VARCHAR"/>
            <result property="zuuid" column="zuuid" jdbcType="VARCHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="kmmc" column="kmmc" jdbcType="VARCHAR"/>
            <result property="dqjehj" column="dqjehj" jdbcType="DECIMAL"/>
            <result property="wqzgdjehj" column="wqzgdjehj" jdbcType="DECIMAL"/>
            <result property="jsyj" column="jsyj" jdbcType="DECIMAL"/>
            <result property="cqbz" column="cqbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="symxuuid" column="symxuuid" jdbcType="VARCHAR"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="zzscbz" column="zzscbz" jdbcType="CHAR"/>
            <result property="jyssbz" column="jyssbz" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,ssnd,
        zuuid,syuuid,kmmc,
        dqjehj,wqzgdjehj,jsyj,
        cqbz,ywqd_dm,lrrq,
        xgrq,scbz,scsj_1,
        xgrsfid,lrrsfid,sjcsdq,
        sjgsdq,sjtb_sj,symxuuid,
        tzlx_dm,sszq,zzscbz,
        jyssbz
    </sql>
</mapper>
