<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.gy.ZzsybnsrZcsbCshMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.gy.ZzsybnsrZcsbCshResDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="businessclob" column="businessclob" jdbcType="VARCHAR"/>
    </resultMap>
        <select id="getZzsybnsrZcsbCsh" resultType="com.css.znsb.nssb.pojo.dto.gy.ZzsybnsrZcsbCshResDTO">
                select * from ZNSB_NSSB_ZZSYBNSRZCSBCSH where
                        DJXH = #{djxh} and YZPZZL_DM = #{yzpzzlDm} and SKSSQQ = #{skssqq} and
                        SKSSQZ = #{skssqz}
                <if test="pzxh != null and pzxh != ''">
                        AND PZXH = #{pzxh}
                </if>
                <if test="sbuuid != null and sbuuid != ''">
                        AND SBUUID = #{sbuuid}
                </if>
        </select>
        <insert id="insertZzsybnsrZcsbCsh">
                INSERT INTO ZNSB_NSSB_ZZSYBNSRZCSBCSH(UUID,DJXH,SKSSQQ,SKSSQZ,YZPZZL_DM,BUSINESSCLOB,
                                                      SJCSDQ,SJGSDQ,YWQD_DM,LRRQ,XGRQ                               
                )VALUES(#{dto.uuid},#{dto.djxh},#{dto.skssqq},#{dto.skssqz},#{dto.yzpzzlDm},#{dto.businessclob},#{dto.sjcsdq},#{dto.sjgsdq},#{dto.ywqdDm},#{dto.lrrq},#{dto.xgrq})
        </insert>
</mapper>
