<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.SbSdsFzjgfpbZjgMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sds.SbSdsFzjgfpbZjgDO">
            <id property="fzjgfpbzjguuid" column="fzjgfpbzjguuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="zjgnsrsbh" column="zjgnsrsbh" jdbcType="VARCHAR"/>
            <result property="zjgdjxh" column="zjgdjxh" jdbcType="DECIMAL"/>
            <result property="ynsdse" column="ynsdse" jdbcType="DECIMAL"/>
            <result property="zjgftsdse" column="zjgftsdse" jdbcType="DECIMAL"/>
            <result property="zjgczjzfpsdse" column="zjgczjzfpsdse" jdbcType="DECIMAL"/>
            <result property="fzjgftdsdse" column="fzjgftdsdse" jdbcType="DECIMAL"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="sfxsdfjm" column="sfxsdfjm" jdbcType="VARCHAR"/>
            <result property="xsdfjmfd" column="xsdfjmfd" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        fzjgfpbzjguuid,pzxh,sbuuid,
        skssqq,skssqz,zjgnsrsbh,
        zjgdjxh,ynsdse,zjgftsdse,
        zjgczjzfpsdse,fzjgftdsdse,zfbz_1,
        zfrq_1,zfr_dm,lrr_dm,
        xgr_dm,sfxsdfjm,xsdfjmfd,
        sjblbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
    <select id="queryBDA0611033A202000" resultType="java.util.Map">
        select t.zjgnsrsbh,         t.ynsdse,         t.zjgftsdse,
               t.fzjgftdsdse,         t.zjgczjzfpsdse,         t.skssqq,
               t.skssqz, t.zjgdjxh
        from sb_sds_fzjgfpb_zjg t
        where t.sbuuid = #{sbuuid}
    </select>
</mapper>
