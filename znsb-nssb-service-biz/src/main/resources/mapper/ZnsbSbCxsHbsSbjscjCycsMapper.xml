<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjCycsMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.hbs.ZnsbSbCxsHbsSbjscjCycsDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="hgbhssybh" column="hgbhssybh" jdbcType="VARCHAR"/>
            <result property="yf" column="yf" jdbcType="CHAR"/>
            <result property="pfkmc" column="pfkmc" jdbcType="VARCHAR"/>
            <result property="zywrwlbDm" column="zywrwlb_dm" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="tzzb" column="tzzb" jdbcType="VARCHAR"/>
            <result property="jsjsdwmc" column="jsjsdwmc" jdbcType="VARCHAR"/>
            <result property="zbz" column="zbz" jdbcType="DECIMAL"/>
            <result property="tzcwxs" column="tzcwxs" jdbcType="DECIMAL"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
            <result property="jmse" column="jmse" jdbcType="DECIMAL"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="fzspmDm" column="fzspm_dm" jdbcType="CHAR"/>
            <result property="wrdlz" column="wrdlz" jdbcType="DECIMAL"/>
            <result property="wrdls" column="wrdls" jdbcType="DECIMAL"/>
            <result property="tzzbz1" column="tzzbz_1" jdbcType="CHAR"/>
            <result property="sfczxj" column="sfczxj" jdbcType="CHAR"/>
            <result property="tzzjsf" column="tzzjsf" jdbcType="CHAR"/>
            <result property="sbbz" column="sbbz" jdbcType="CHAR"/>
            <result property="sqspuuid" column="sqspuuid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,skssqq,skssqz,
        sbsx_dm_1,syuuid,hgbhssybh,
        yf,pfkmc,zywrwlb_dm,
        zspm_dm,tzzb,jsjsdwmc,
        zbz,tzcwxs,swsx_dm,
        ssjmxz_dm,sl_1,ynse,
        jmse,lrr_dm,xgr_dm,
        yxbz,yjse,ybtse,
        fzspm_dm,wrdlz,wrdls,
        tzzbz_1,sfczxj,tzzjsf,
        sbbz,sqspuuid,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
