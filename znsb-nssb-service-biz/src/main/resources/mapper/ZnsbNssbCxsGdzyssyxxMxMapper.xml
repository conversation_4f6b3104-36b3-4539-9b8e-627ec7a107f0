<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.gdzys.ZnsbNssbCxsGdzyssyxxMxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.gdzys.ZnsbNssbCxsGdzyssyxxMxDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="gdzysuuid" column="gdzysuuid" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="sybh1" column="sybh_1" jdbcType="VARCHAR"/>
        <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
        <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
        <result property="tdzldz" column="tdzldz" jdbcType="VARCHAR"/>
        <result property="zdytDm" column="zdyt_dm" jdbcType="VARCHAR"/>
        <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
        <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
        <result property="jsmj1" column="jsmj_1" jdbcType="DECIMAL"/>
        <result property="jsmj" column="jsmj" jdbcType="DECIMAL"/>
        <result property="msmj" column="msmj" jdbcType="DECIMAL"/>
        <result property="sysl" column="sysl" jdbcType="DECIMAL"/>
        <result property="jzse" column="jzse" jdbcType="DECIMAL"/>
        <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
        <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
        <result property="jsse" column="jsse" jdbcType="DECIMAL"/>
        <result property="msse" column="msse" jdbcType="DECIMAL"/>
        <result property="phjmxzDm" column="phjmxz_dm" jdbcType="VARCHAR"/>
        <result property="phjmswsxDm" column="phjmswsx_dm" jdbcType="CHAR"/>
        <result property="phjmse" column="phjmse" jdbcType="DECIMAL"/>
        <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
        <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
        <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="gzbz" column="gzbz" jdbcType="CHAR"/>
        <result property="bczt" column="bczt" jdbcType="CHAR"/>
        <result property="bgzt" column="bgzt" jdbcType="CHAR"/>
        <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
        <result property="hxbuuid" column="hxbuuid" jdbcType="VARCHAR"/>
        <result property="hxbmxuuid" column="hxbmxuuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,gdzysuuid,djxh,
        sybh_1,xzqhsz_dm,jdxz_dm,
        tdzldz,zdyt_dm,zspm_dm,
        zszm_dm,jsmj_1,jsmj,
        msmj,sysl,jzse,
        ssjmxz_dm,swsx_dm,jsse,
        msse,phjmxz_dm,phjmswsx_dm,
        phjmse,yjse,ybtse,
        yxbz,lrrq,xgrq,
        lrrsfid,xgrsfid,ywqd_dm,
        sjcsdq,sjtb_sj,sjgsdq,
        gzbz,bczt,bgzt,pclsh,hxbuuid,hxbmxuuid
    </sql>
</mapper>
