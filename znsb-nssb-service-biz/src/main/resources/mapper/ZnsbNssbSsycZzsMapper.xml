<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.css.znsb.nssb.mapper.ssyc.ZnsbNssbSsycZzsMapper">
    <select id="queryZzsHj" resultType="com.css.znsb.nssb.pojo.domain.ssyc.ZnsbNssbSsycZzsDO">
        SELECT
        COALESCE(SUM(tzje_2), 0) AS tzje_2,
        COALESCE(SUM(zzsse), 0) AS zzsse,
        COALESCE(SUM(zzsfjse), 0) AS zzsfjse,
        COALESCE(SUM(zzsyssr), 0) AS zzsyssr,
        COALESCE(SUM(zzsldse), 0) AS zzsldse,
        COALESCE(SUM(zzsxxse), 0) AS zzsxxse,
        COALESCE(SUM(zzsjxrzdk), 0) AS zzsjxrzdk,
        COALESCE(SUM(zzsjxjsdk), 0) AS zzsjxjsdk,
        COALESCE(SUM(zzsjxsezc), 0) AS zzsjxsezc,
        COALESCE(SUM(zzsjmse), 0) AS zzsjmse,
        COALESCE(SUM(zzsjyjsse), 0) AS zzsjyjsse,
        COALESCE(SUM(zzsdrzjxsezp), 0) AS zzsdrzjxsezp
        FROM znsb_nssb_ssyc_zzs
        <where>
            ssq = #{ssq}
            <if test="zzuuidList != null and zzuuidList.size() > 0">
                AND zzuuid IN
                <foreach item="id" collection="zzuuidList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="qydmzList != null and qydmzList.size() > 0">
                AND qydmz IN
                <foreach item="code" collection="qydmzList" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>
</mapper>