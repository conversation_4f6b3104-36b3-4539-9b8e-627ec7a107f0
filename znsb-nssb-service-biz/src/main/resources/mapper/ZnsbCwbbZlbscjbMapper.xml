<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.yjzt.ZnsbCwbbZlbscjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.yjtz.ZnsbCwbbZlbscjbDO">
        <id property="zlbscjuuid" column="zlbscjuuid" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="zlbsuuid" column="zlbsuuid" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="qydmz" column="qydmz" jdbcType="VARCHAR"/>
        <result property="sszq" column="sszq" jdbcType="INTEGER"/>
        <result property="ssqq" column="ssqq" jdbcType="TIMESTAMP"/>
        <result property="ssqz" column="ssqz" jdbcType="TIMESTAMP"/>
        <result property="bszlDm" column="bszl_dm" jdbcType="CHAR"/>
        <result property="bbh" column="bbh" jdbcType="VARCHAR"/>
        <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
        <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
        <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
        <result property="lrswjgDm" column="lrswjg_dm" jdbcType="CHAR"/>
        <result property="lrswjgmc" column="lrswjgmc" jdbcType="VARCHAR"/>
        <result property="zfswjg1Dm" column="zfswjg_1_dm" jdbcType="CHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="zfswjgmc1" column="zfswjgmc1" jdbcType="VARCHAR"/>
        <result property="lrrmc" column="lrrmc" jdbcType="VARCHAR"/>
        <result property="zfrmc" column="zfrmc" jdbcType="VARCHAR"/>
        <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        zlbscjuuid
        ,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,zlbsuuid,
        djxh,ssqq,ssqz,
        bszl_dm,bbh,zfbz_1,
        zfrq_1,zfr_dm,lrswjg_dm,
        lrswjgmc,zfswjg_1_dm,sjtb_sj,
        zfswjgmc1,lrrmc,zfrmc,
        sjblbz,sszq,qydmz
    </sql>
</mapper>
