<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwDOMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO">
            <id property="sbrwuuid" column="sbrwuuid" jdbcType="VARCHAR"/>
            <result property="glqyid" column="glqyid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="nsqxDm" column="nsqx_dm" jdbcType="CHAR"/>
            <result property="sbny" column="sbny" jdbcType="CHAR"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="sbqx" column="sbqx" jdbcType="TIMESTAMP"/>
            <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
            <result property="flzlpclsh" column="flzlpclsh" jdbcType="VARCHAR"/>
            <result property="sbcgbz" column="sbcgbz" jdbcType="CHAR"/>
            <result property="nsrsbztDm" column="nsrsbzt_dm" jdbcType="VARCHAR"/>
            <result property="sbrq1" column="sbrq_1" jdbcType="TIMESTAMP"/>
            <result property="sbyysm" column="sbyysm" jdbcType="VARCHAR"/>
            <result property="shztDm" column="shzt_dm" jdbcType="CHAR"/>
            <result property="bdjgbz" column="bdjgbz" jdbcType="CHAR"/>
            <result property="zfrq" column="zfrq" jdbcType="TIMESTAMP"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="bsflzlbz" column="bsflzlbz" jdbcType="CHAR"/>
            <result property="bsssjgbz" column="bsssjgbz" jdbcType="CHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbjkztDm" column="sbjkzt_dm" jdbcType="CHAR"/>
            <result property="bwnr" column="bwnr" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="ybse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="ytse" column="ytse" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        sbrwuuid,glqyid,djxh,
        nsrsbh,nsrmc,yzpzzl_dm,
        skssqq,skssqz,nsqx_dm,
        sbny,ybtse,sbuuid,
        sbqx,pclsh,flzlpclsh,
        sbcgbz,nsrsbzt_dm,sbrq_1,
        sbyysm,shzt_dm,bdjgbz,
        zfrq,zfbz_1,bsflzlbz,
        bsssjgbz,pzxh,sbjkzt_dm,
        bwnr,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,ybse,ytse
    </sql>
</mapper>
