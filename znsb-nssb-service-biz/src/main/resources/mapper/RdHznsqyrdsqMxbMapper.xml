<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.gy.RdHznsqyrdsqMxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.gy.RdHznsqyrdsqMxbDO">
            <id property="hznsmxuuid" column="hznsmxuuid" jdbcType="VARCHAR"/>
            <result property="hznsuuid" column="hznsuuid" jdbcType="VARCHAR"/>
            <result property="lcslid" column="lcslid" jdbcType="CHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="bhznsrsfsqbz" column="bhznsrsfsqbz" jdbcType="CHAR"/>
            <result property="bhzrdjxh" column="bhzrdjxh" jdbcType="DECIMAL"/>
            <result property="bhznsqynsrsbh" column="bhznsqynsrsbh" jdbcType="VARCHAR"/>
            <result property="bhznsqynsrmc" column="bhznsqynsrmc" jdbcType="VARCHAR"/>
            <result property="sbjnfsDm" column="sbjnfs_dm" jdbcType="VARCHAR"/>
            <result property="yzl" column="yzl" jdbcType="DECIMAL"/>
            <result property="desl1" column="desl_1" jdbcType="DECIMAL"/>
            <result property="bhznsrynsms" column="bhznsrynsms" jdbcType="VARCHAR"/>
            <result property="hznsyxqq" column="hznsyxqq" jdbcType="TIMESTAMP"/>
            <result property="hznsyxqz" column="hznsyxqz" jdbcType="TIMESTAMP"/>
            <result property="bhznsqydz" column="bhznsqydz" jdbcType="VARCHAR"/>
            <result property="bhznsqydjzclxDm" column="bhznsqydjzclx_dm" jdbcType="CHAR"/>
            <result property="bhznsqysysl" column="bhznsqysysl" jdbcType="DECIMAL"/>
            <result property="bhznsqysfwfzjgbz" column="bhznsqysfwfzjgbz" jdbcType="CHAR"/>
            <result property="hznsfwDm" column="hznsfw_dm" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="yzbl" column="yzbl" jdbcType="DECIMAL"/>
            <result property="hzqylxDm" column="hzqylx_dm" jdbcType="CHAR"/>
            <result property="yzskfwDm" column="yzskfw_dm" jdbcType="CHAR"/>
            <result property="sflsqy" column="sflsqy" jdbcType="CHAR"/>
            <result property="bhzsjlylx" column="bhzsjlylx" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="wsdjxh" column="wsdjxh" jdbcType="DECIMAL"/>
            <result property="sxzqhDm" column="sxzqh_dm" jdbcType="CHAR"/>
            <result property="xtbm" column="xtbm" jdbcType="VARCHAR"/>
            <result property="ygznsrlxDm" column="ygznsrlx_dm" jdbcType="CHAR"/>
            <result property="nsrztDm" column="nsrzt_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        hznsmxuuid,hznsuuid,lcslid,
        djxh,zsxm_dm,bhznsrsfsqbz,
        bhzrdjxh,bhznsqynsrsbh,bhznsqynsrmc,
        sbjnfs_dm,yzl,desl_1,
        bhznsrynsms,hznsyxqq,hznsyxqz,
        bhznsqydz,bhznsqydjzclx_dm,bhznsqysysl,
        bhznsqysfwfzjgbz,hznsfw_dm,yxbz,
        yzbl,hzqylx_dm,yzskfw_dm,
        sflsqy,bhzsjlylx,sjblbz,
        wsdjxh,sxzqh_dm,xtbm,
        ygznsrlx_dm,nsrzt_dm,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj,skssqq,skssqz
    </sql>
</mapper>
