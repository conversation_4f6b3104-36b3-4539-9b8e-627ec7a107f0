<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxZyfcsymxzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.fcs.ZnsbTzzxZyfcsymxzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
            <result property="fcmc" column="fcmc" jdbcType="VARCHAR"/>
            <result property="fwzldz" column="fwzldz" jdbcType="VARCHAR"/>
            <result property="jsbl" column="jsbl" jdbcType="DECIMAL"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="jzrq2" column="jzrq_2" jdbcType="TIMESTAMP"/>
            <result property="fcyz" column="fcyz" jdbcType="DECIMAL"/>
            <result property="fcs1" column="fcs_1" jdbcType="DECIMAL"/>
            <result property="sfscysmx" column="sfscysmx" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
            <result property="ly" column="ly" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,djxh,
        nsrsbh,nsrmc,fybh,
        fcmc,fwzldz,jsbl,
        sl1,yxqq,yxqz,
        jzrq2,fcyz,fcs1,
        sfscysmx,ywqdDm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtbSj,
        scbz,scsj1,ly
    </sql>
</mapper>
