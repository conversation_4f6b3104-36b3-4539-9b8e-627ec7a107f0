<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHznsfpZbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbHznsfpZbSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="fzjgnsrsbh" column="fzjgnsrsbh" jdbcType="VARCHAR"/>
            <result property="fzjgmc" column="fzjgmc" jdbcType="VARCHAR"/>
            <result property="fzjgxssr" column="fzjgxssr" jdbcType="DECIMAL"/>
            <result property="fzjgybhwjlwxssr" column="fzjgybhwjlwxssr" jdbcType="DECIMAL"/>
            <result property="fzjgybhwjlwjzjtxssr" column="fzjgybhwjlwjzjtxssr" jdbcType="DECIMAL"/>
            <result property="fzjgysfwxssr" column="fzjgysfwxssr" jdbcType="DECIMAL"/>
            <result property="fzjgysfwjzjtxssr" column="fzjgysfwjzjtxssr" jdbcType="DECIMAL"/>
            <result property="fzjgFpbl" column="fzjg_fpbl" jdbcType="DECIMAL"/>
            <result property="fzjgybhwjlwfpbl" column="fzjgybhwjlwfpbl" jdbcType="DECIMAL"/>
            <result property="fzjgybhwjlwjzjtfpbl" column="fzjgybhwjlwjzjtfpbl" jdbcType="DECIMAL"/>
            <result property="fzjgysfwfpbl" column="fzjgysfwfpbl" jdbcType="DECIMAL"/>
            <result property="fzjgysfwjzjtfpbl" column="fzjgysfwjzjtfpbl" jdbcType="DECIMAL"/>
            <result property="fzjgfpse" column="fzjgfpse" jdbcType="DECIMAL"/>
            <result property="fzjgybhwjlwfpse" column="fzjgybhwjlwfpse" jdbcType="DECIMAL"/>
            <result property="fzjgybhwjlwjzjtfpse" column="fzjgybhwjlwjzjtfpse" jdbcType="DECIMAL"/>
            <result property="fzjgysfwfpse" column="fzjgysfwfpse" jdbcType="DECIMAL"/>
            <result property="fzjgysfwjzjtfpse" column="fzjgysfwjzjtfpse" jdbcType="DECIMAL"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        fzjgnsrsbh,fzjgmc,fzjgxssr,
        fzjgybhwjlwxssr,fzjgybhwjlwjzjtxssr,fzjgysfwxssr,
        fzjgysfwjzjtxssr,fzjg_fpbl,fzjgybhwjlwfpbl,
        fzjgybhwjlwjzjtfpbl,fzjgysfwfpbl,fzjgysfwjzjtfpbl,
        fzjgfpse,fzjgybhwjlwfpse,fzjgybhwjlwjzjtfpse,
        fzjgysfwfpse,fzjgysfwjzjtfpse,lrrq,
        xgrq,sjgsdq,sjblbz,
        sjtb_sj,lrrsfid,xgrsfid,
        ywqd_dm,sjcsdq
    </sql>
</mapper>
