<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbHzscjyxfsqkbgbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbHzscjyxfsqkbgbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="ppscfnsrmc" column="ppscfnsrmc" jdbcType="VARCHAR"/>
            <result property="ppscfnsrsbh" column="ppscfnsrsbh" jdbcType="VARCHAR"/>
            <result property="ppscfdjxh" column="ppscfdjxh" jdbcType="DECIMAL"/>
            <result property="ppsrfnsrmc" column="ppsrfnsrmc" jdbcType="VARCHAR"/>
            <result property="ppsrfnsrsbh" column="ppsrfnsrsbh" jdbcType="VARCHAR"/>
            <result property="ppsrfdjxh" column="ppsrfdjxh" jdbcType="DECIMAL"/>
            <result property="sptm" column="sptm" jdbcType="VARCHAR"/>
            <result property="phgg" column="phgg" jdbcType="VARCHAR"/>
            <result property="xssl" column="xssl" jdbcType="DECIMAL"/>
            <result property="xsjg" column="xsjg" jdbcType="DECIMAL"/>
            <result property="xsje" column="xsje" jdbcType="DECIMAL"/>
            <result property="ppsrfyjnsk" column="ppsrfyjnsk" jdbcType="DECIMAL"/>
            <result property="sphfwssflbm" column="sphfwssflbm" jdbcType="DECIMAL"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        ppscfnsrmc,ppscfnsrsbh,ppscfdjxh,
        ppsrfnsrmc,ppsrfnsrsbh,ppsrfdjxh,
        sptm,phgg,xssl,
        xsjg,xsje,ppsrfyjnsk,
        sphfwssflbm,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
