<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbJyzyxshzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbJyzyxshzSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="tbr" column="tbr" jdbcType="VARCHAR"/>
            <result property="xsjeBtgjyjLjs" column="xsje_btgjyj_ljs" jdbcType="DECIMAL"/>
            <result property="xsjeBtgjyjBys" column="xsje_btgjyj_bys" jdbcType="DECIMAL"/>
            <result property="xsjeTgjyjLjs" column="xsje_tgjyj_ljs" jdbcType="DECIMAL"/>
            <result property="xsjeTgjyjBys" column="xsje_tgjyj_bys" jdbcType="DECIMAL"/>
            <result property="ysxsslLjs" column="ysxssl_ljs" jdbcType="DECIMAL"/>
            <result property="ysxsslBys" column="ysxssl_bys" jdbcType="DECIMAL"/>
            <result property="ykcylJcLjs" column="ykcyl_jc_ljs" jdbcType="DECIMAL"/>
            <result property="ykcylDcLjs" column="ykcyl_dc_ljs" jdbcType="DECIMAL"/>
            <result property="ykcylDkLjs" column="ykcyl_dk_ljs" jdbcType="DECIMAL"/>
            <result property="ykcylZyLjs" column="ykcyl_zy_ljs" jdbcType="DECIMAL"/>
            <result property="ykcylJcBys" column="ykcyl_jc_bys" jdbcType="DECIMAL"/>
            <result property="ykcylDcBys" column="ykcyl_dc_bys" jdbcType="DECIMAL"/>
            <result property="ykcylDkBys" column="ykcyl_dk_bys" jdbcType="DECIMAL"/>
            <result property="ykcylZyBys" column="ykcyl_zy_bys" jdbcType="DECIMAL"/>
            <result property="cyslLjs" column="cysl_ljs" jdbcType="DECIMAL"/>
            <result property="cyslBys" column="cysl_bys" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        tbr,xsje_btgjyj_ljs,xsje_btgjyj_bys,
        xsje_tgjyj_ljs,xsje_tgjyj_bys,ysxssl_ljs,
        ysxssl_bys,ykcyl_jc_ljs,ykcyl_dc_ljs,
        ykcyl_dk_ljs,ykcyl_zy_ljs,ykcyl_jc_bys,
        ykcyl_dc_bys,ykcyl_dk_bys,ykcyl_zy_bys,
        cysl_ljs,cysl_bys,sjblbz,
        lrrq,xgrq,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq,
        sjtb_sj,sjgsdq
    </sql>
</mapper>
