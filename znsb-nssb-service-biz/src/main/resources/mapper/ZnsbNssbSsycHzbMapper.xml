<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.css.znsb.nssb.mapper.ssyc.ZnsbNssbSsycHzbMapper">

    <select id="queryHzbHj" resultType="com.css.znsb.nssb.pojo.domain.ssyc.ZnsbNssbSsycHzbDO">
        SELECT
        COALESCE(SUM(sfhj), 0) AS sfhj,
        COALESCE(SUM(zzsse), 0) AS zzsse,
        COALESCE(SUM(zzsfjse), 0) AS zzsfjse,
        COALESCE(SUM(qysdsse), 0) AS qysdsse,
        COALESCE(SUM(grsdsse), 0) AS grsdsse,
        COALESCE(SUM(fcsse), 0) AS fcsse,
        COALESCE(SUM(tdsysse), 0) AS tdsysse,
        COALESCE(SUM(yhsse), 0) AS yhsse
        FROM znsb_nssb_ssyc_hzb
        <where>
            ssq = #{ssq}
            <if test="zzuuidList != null and zzuuidList.size() > 0">
                AND zzuuid IN
                <foreach item="id" collection="zzuuidList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="qydmzList != null and qydmzList.size() > 0">
                AND qydmz IN
                <foreach item="code" collection="qydmzList" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>
</mapper>