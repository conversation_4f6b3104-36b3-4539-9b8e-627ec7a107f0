<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.tdzzs.ZnsbNssbCxsTdzzsYzcjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.tdzzs.ZnsbNssbCxsTdzzsYzcjbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="cjuuid" column="cjuuid" jdbcType="VARCHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="dzbzdszlDm" column="dzbzdszl_dm" jdbcType="VARCHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="bdcxmbh" column="bdcxmbh" jdbcType="VARCHAR"/>
            <result property="yssr" column="yssr" jdbcType="DECIMAL"/>
            <result property="hbsr" column="hbsr" jdbcType="DECIMAL"/>
            <result property="swsrjqtsr" column="swsrjqtsr" jdbcType="DECIMAL"/>
            <result property="yzl" column="yzl" jdbcType="DECIMAL"/>
            <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
            <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="stxssr" column="stxssr" jdbcType="DECIMAL"/>
            <result property="xmbh" column="xmbh" jdbcType="VARCHAR"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,syuuid,dzbzdszl_dm,
        zsxm_dm,zspm_dm,zszm_dm,
        bdcxmbh,yssr,hbsr,
        swsrjqtsr,yzl,ynse,
        yjse,ybtse,djxh,
        stxssr,xmbh,swsx_dm,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
