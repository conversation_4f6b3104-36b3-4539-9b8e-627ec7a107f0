<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxJxfpmxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxJxfpmxbDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="sszq" column="sszq" jdbcType="INTEGER"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
        <result property="fplxDm" column="fplx_dm" jdbcType="CHAR"/>
        <result property="fppzDm" column="fppz_dm" jdbcType="CHAR"/>
        <result property="fpdm" column="fpdm" jdbcType="VARCHAR"/>
        <result property="fpdmhm" column="fpdmhm" jdbcType="VARCHAR"/>
        <result property="fphm" column="fphm" jdbcType="VARCHAR"/>
        <result property="je" column="je" jdbcType="DECIMAL"/>
        <result property="se" column="se" jdbcType="DECIMAL"/>
        <result property="jshj" column="jshj" jdbcType="DECIMAL"/>
        <result property="kprq" column="kprq" jdbcType="TIMESTAMP"/>
        <result property="skssq" column="skssq" jdbcType="VARCHAR"/>
        <result property="xfsh" column="xfsh" jdbcType="VARCHAR"/>
        <result property="xfmc" column="xfmc" jdbcType="VARCHAR"/>
        <result property="gxrzsj" column="gxrzsj" jdbcType="TIMESTAMP"/>
        <result property="hxytDm" column="hxyt_dm" jdbcType="VARCHAR"/>
        <result property="bdkyyDm" column="bdkyy_dm" jdbcType="VARCHAR"/>
        <result property="dkje2" column="dkje_2" jdbcType="DECIMAL"/>
        <result property="dkse" column="dkse" jdbcType="DECIMAL"/>
        <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
        <result property="scbz" column="scbz" jdbcType="CHAR"/>
        <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
        <result property="zzscbz" column="zzscbz" jdbcType="CHAR"/>
        <result property="jyssbz" column="jyssbz" jdbcType="CHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="tdyslxDm" column="tdyslx_dm" jdbcType="CHAR"/>
        <result property="gmfnsrsbh" column="gmfnsrsbh" jdbcType="VARCHAR"/>
        <result property="gmfnsrmc" column="gmfnsrmc" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sszq,djxh,
        nsrsbh,nsrmc,fplx_dm,
        fppz_dm,fpdm,fpdmhm,
        fphm,je,se,
        jshj,kprq,skssq,
        xfsh,xfmc,gxrzsj,
        hxyt_dm,bdkyy_dm,dkje_2,
        dkse,tzlx_dm,scbz,
        scsj_1,zzscbz,jyssbz,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,tdyslx_dm,
        gmfnsrsbh,gmfnsrmc
    </sql>
</mapper>
