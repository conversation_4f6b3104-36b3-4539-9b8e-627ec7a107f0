<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxYhszzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxYhszzDO">
            <id property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="smDm" column="sm_dm" jdbcType="VARCHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="yspzmc" column="yspzmc" jdbcType="VARCHAR"/>
            <result property="yspzbh" column="yspzbh" jdbcType="VARCHAR"/>
            <result property="yspzsl" column="yspzsl" jdbcType="BIGINT"/>
            <result property="sbqxlx" column="sbqxlx" jdbcType="CHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="ynspzsllsrq" column="ynspzsllsrq" jdbcType="TIMESTAMP"/>
            <result property="jsjehjs" column="jsjehjs" jdbcType="DECIMAL"/>
            <result property="hdbl" column="hdbl" jdbcType="DECIMAL"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
            <result property="sjjsje" column="sjjsje" jdbcType="DECIMAL"/>
            <result property="sjjsrq" column="sjjsrq" jdbcType="TIMESTAMP"/>
            <result property="dfslrmc" column="dfslrmc" jdbcType="VARCHAR"/>
            <result property="dfslrnsrsbh" column="dfslrnsrsbh" jdbcType="VARCHAR"/>
            <result property="dfslrsjje" column="dfslrsjje" jdbcType="DECIMAL"/>
            <result property="cqbz" column="cqbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,uuid,sszq,
        syuuid,sm_dm,zszm_dm,
        yspzmc,yspzbh,yspzsl,
        sbqxlx,skssqq,skssqz,
        ynspzsllsrq,jsjehjs,hdbl,
        sl_1,ssjmxz_dm,ynse,
        sjjsje,sjjsrq,dfslrmc,
        dfslrnsrsbh,dfslrsjje,cqbz,
        ywqd_dm,lrrq,xgrq,
        scbz,scsj_1,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
