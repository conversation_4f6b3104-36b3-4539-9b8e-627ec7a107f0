<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zdsy.ZnsbNssbZdsygyqyzycpyssxxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zdsy.ZnsbNssbZdsygyqyzycpyssxxbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zlbscjuuid" column="zlbscjuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="hmc" column="hmc" jdbcType="VARCHAR"/>
            <result property="tjlxDm" column="tjlx_dm" jdbcType="CHAR"/>
            <result property="cpmc1" column="cpmc_1" jdbcType="VARCHAR"/>
            <result property="jldwDm" column="jldw_dm" jdbcType="CHAR"/>
            <result property="jldwmc" column="jldwmc" jdbcType="VARCHAR"/>
            <result property="bnQccpkcl" column="bn_qccpkcl" jdbcType="DECIMAL"/>
            <result property="snQccpkcl" column="sn_qccpkcl" jdbcType="DECIMAL"/>
            <result property="bnCpclgjl" column="bn_cpclgjl" jdbcType="DECIMAL"/>
            <result property="snCpclgjl" column="sn_cpclgjl" jdbcType="DECIMAL"/>
            <result property="bnCpxl" column="bn_cpxl" jdbcType="DECIMAL"/>
            <result property="snCpxl" column="sn_cpxl" jdbcType="DECIMAL"/>
            <result property="bnCpxsjg" column="bn_cpxsjg" jdbcType="DECIMAL"/>
            <result property="snCpxsjg" column="sn_cpxsjg" jdbcType="DECIMAL"/>
            <result property="bnCkcpxl" column="bn_ckcpxl" jdbcType="DECIMAL"/>
            <result property="snCkcpxl" column="sn_ckcpxl" jdbcType="DECIMAL"/>
            <result property="bnXssr" column="bn_xssr" jdbcType="DECIMAL"/>
            <result property="snXssr" column="sn_xssr" jdbcType="DECIMAL"/>
            <result property="bnCpxslr" column="bn_cpxslr" jdbcType="DECIMAL"/>
            <result property="snCpxslr" column="sn_cpxslr" jdbcType="DECIMAL"/>
            <result property="bnZjcl" column="bn_zjcl" jdbcType="DECIMAL"/>
            <result property="snZjcl" column="sn_zjcl" jdbcType="DECIMAL"/>
            <result property="bnZjrg" column="bn_zjrg" jdbcType="DECIMAL"/>
            <result property="snZjrg" column="sn_zjrg" jdbcType="DECIMAL"/>
            <result property="bnZzfy" column="bn_zzfy" jdbcType="DECIMAL"/>
            <result property="snZzfy" column="sn_zzfy" jdbcType="DECIMAL"/>
            <result property="bnJzxfsxse" column="bn_jzxfsxse" jdbcType="DECIMAL"/>
            <result property="snJzxfsxse" column="sn_jzxfsxse" jdbcType="DECIMAL"/>
            <result property="bnYnzys" column="bn_ynzys" jdbcType="DECIMAL"/>
            <result property="snYnzys" column="sn_ynzys" jdbcType="DECIMAL"/>
            <result property="ynxfsecjjzsebn" column="ynxfsecjjzsebn" jdbcType="DECIMAL"/>
            <result property="ynxfsecjjzsesn" column="ynxfsecjjzsesn" jdbcType="DECIMAL"/>
            <result property="ynxfsecljzsebn" column="ynxfsecljzsebn" jdbcType="DECIMAL"/>
            <result property="ynxfsecljzsesn" column="ynxfsecljzsesn" jdbcType="DECIMAL"/>
            <result property="bnCkxssr" column="bn_ckxssr" jdbcType="DECIMAL"/>
            <result property="snCkxssr" column="sn_ckxssr" jdbcType="DECIMAL"/>
            <result property="bnMsxssr" column="bn_msxssr" jdbcType="DECIMAL"/>
            <result property="snMsxssr" column="sn_msxssr" jdbcType="DECIMAL"/>
            <result property="bnYnssr" column="bn_ynssr" jdbcType="DECIMAL"/>
            <result property="snYnssr" column="sn_ynssr" jdbcType="DECIMAL"/>
            <result property="bnQmcpkcl" column="bn_qmcpkcl" jdbcType="DECIMAL"/>
            <result property="snQmcpkcl" column="sn_qmcpkcl" jdbcType="DECIMAL"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zlbscjuuid,ewbhxh,
        hmc,tjlx_dm,cpmc_1,
        jldw_dm,jldwmc,bn_qccpkcl,
        sn_qccpkcl,bn_cpclgjl,sn_cpclgjl,
        bn_cpxl,sn_cpxl,bn_cpxsjg,
        sn_cpxsjg,bn_ckcpxl,sn_ckcpxl,
        bn_xssr,sn_xssr,bn_cpxslr,
        sn_cpxslr,bn_zjcl,sn_zjcl,
        bn_zjrg,sn_zjrg,bn_zzfy,
        sn_zzfy,bn_jzxfsxse,sn_jzxfsxse,
        bn_ynzys,sn_ynzys,ynxfsecjjzsebn,
        ynxfsecjjzsesn,ynxfsecljzsebn,ynxfsecljzsesn,
        bn_ckxssr,sn_ckxssr,bn_msxssr,
        sn_msxssr,bn_ynssr,sn_ynssr,
        bn_qmcpkcl,sn_qmcpkcl,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
