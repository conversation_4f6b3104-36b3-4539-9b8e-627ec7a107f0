<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbJyssSsSsjgYhsMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.yhs.ZnsbJyssSsSsjgYhsDO">
        <id property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <id property="ssjguuid" column="ssjguuid" jdbcType="VARCHAR"/>
        <result property="shxydm" column="shxydm" jdbcType="VARCHAR"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="frwlsfid" column="frwlsfid" jdbcType="VARCHAR"/>
        <result property="ssgcwdDm" column="ssgcwd_dm" jdbcType="VARCHAR"/>
        <result property="ssgcwd1Dm" column="ssgcwd1_dm" jdbcType="VARCHAR"/>
        <result property="ssgcwd2Dm" column="ssgcwd2_dm" jdbcType="VARCHAR"/>
        <result property="ssgcwd3Dm" column="ssgcwd3_dm" jdbcType="VARCHAR"/>
        <result property="ssgcwd4Dm" column="ssgcwd4_dm" jdbcType="VARCHAR"/>
        <result property="ssgcwd5Dm" column="ssgcwd5_dm" jdbcType="VARCHAR"/>
        <result property="spbm" column="spbm" jdbcType="VARCHAR"/>
        <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
        <result property="sspzflDm" column="sspzfl_dm" jdbcType="CHAR"/>
        <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
        <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
        <result property="jmsxmDm" column="jmsxm_dm" jdbcType="CHAR"/>
        <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
        <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
        <result property="gzlxDm" column="gzlx_dm" jdbcType="CHAR"/>
        <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
        <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
        <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
        <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
        <result property="zgswjgDm" column="zgswjg_dm" jdbcType="CHAR"/>
        <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
        <result property="djzclxDm" column="djzclx_dm" jdbcType="CHAR"/>
        <result property="ybnsrbz" column="ybnsrbz" jdbcType="VARCHAR"/>
        <result property="qygmDm" column="qygm_dm" jdbcType="CHAR"/>
        <result property="kzztdjlxDm" column="kzztdjlx_dm" jdbcType="CHAR"/>
        <result property="ssjezj" column="ssjezj" jdbcType="DECIMAL"/>
        <result property="ssjejs" column="ssjejs" jdbcType="DECIMAL"/>
        <result property="ssjeye" column="ssjeye" jdbcType="DECIMAL"/>
        <result property="ssslzj" column="ssslzj" jdbcType="DECIMAL"/>
        <result property="sssljs" column="sssljs" jdbcType="DECIMAL"/>
        <result property="ssslye" column="ssslye" jdbcType="DECIMAL"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
        <result property="spmc" column="spmc" jdbcType="VARCHAR"/>
        <result property="ssywsq" column="ssywsq" jdbcType="INTEGER"/>
        <result property="sksxDm" column="sksx_dm" jdbcType="CHAR"/>
        <result property="skzlDm" column="skzl_dm" jdbcType="CHAR"/>
        <result property="cbsxDm" column="cbsx_dm" jdbcType="CHAR"/>
        <result property="jmfsDm" column="jmfs_dm" jdbcType="VARCHAR"/>
        <result property="jkqx" column="jkqx" jdbcType="TIMESTAMP"/>
        <result property="yjkqx" column="yjkqx" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh
        ,ssjguuid,shxydm,
        nsrsbh,frwlsfid,ssgcwd_dm,
        ssgcwd1_dm,ssgcwd2_dm,ssgcwd3_dm,
        ssgcwd4_dm,ssgcwd5_dm,spbm,
        sl_1,sspzfl_dm,zspm_dm,
        zszm_dm,jmsxm_dm,ssjmxz_dm,
        swsx_dm,gzlx_dm,pzxh,
        yzpzzl_dm,skssqq,skssqz,
        zgswjg_dm,hy_dm,djzclx_dm,
        ybnsrbz,qygm_dm,kzztdjlx_dm,
        ssjezj,ssjejs,ssjeye,
        ssslzj,sssljs,ssslye,
        lrrq,lrrsfid,xgrq,
        xgrsfid,ywqd_dm,sjgsdq,
        sjcsdq,sjtb_sj,nsrmc,
        spmc,ssywsq,sksx_dm,
        skzl_dm,cbsx_dm,jmfs_dm,
        jkqx,yjkqx
    </sql>
</mapper>
