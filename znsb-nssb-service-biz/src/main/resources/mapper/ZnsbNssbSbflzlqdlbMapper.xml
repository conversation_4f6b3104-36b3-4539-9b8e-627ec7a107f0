<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.gy.ZnsbNssbSbflzlqdlbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.gy.SbflzlqdlbResDTO">
            <id property="uuid" column="uuid " jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="VARCHAR"/>
        <result property="zsxmDm" column="zsxmDm" jdbcType="VARCHAR"/>
        <result property="yzpzzlDm" column="yzpzzlDm" jdbcType="VARCHAR"/>
        <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
        <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
        <result property="flzlDm" column="flzl_dm" jdbcType="VARCHAR"/>
            <result property="flzlmc" column="flzlmc" jdbcType="VARCHAR"/>
            <result property="fileext" column="fileext" jdbcType="VARCHAR"/>
            <result property="flzlbslxDm" column="flzlbslx_dm" jdbcType="CHAR"/>
            <result property="filesizes" column="filesizes" jdbcType="SMALLINT"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        UUID,DJXH,ZSXM_DM,YZPZZL_DM,SKSSQQ,SKSSQZ,FLZL_DM,FLZLMC,FILEEXT,
        FLZLBSLX_DM,FILESIZES,YWQD_DM,SJGSDQ,
        SJCSDQ,LRRSFID,XGRSFID,
        LRRQ,XGRQ,SJTB_SJ
    </sql>
    
    <select id="getSbflzlqdlb" resultType="com.css.znsb.nssb.pojo.dto.gy.SbflzlqdlbResDTO">
        select <include refid="Base_Column_List"/> from ZNSB_NSSB_SBFLZLQDLB 
                 where DJXH = #{djxh} and ZSXM_DM = #{zsxmDm} and
                     YZPZZL_DM = #{yzpzzlDm} and SKSSQQ = #{skssqq} and 
                     SKSSQZ = #{skssqz}
    </select>
    <insert id="insertSbflzlqdlb" parameterType="java.util.List">
        INSERT INTO ZNSB_NSSB_SBFLZLQDLB (
        UUID,DJXH,ZSXM_DM,YZPZZL_DM,SKSSQQ,SKSSQZ,FLZL_DM,FLZLMC,FILEEXT,
        FLZLBSLX_DM,FILESIZES,YWQD_DM,SJGSDQ,
        SJCSDQ,LRRSFID,XGRSFID,
        LRRQ,XGRQ,SJTB_SJ)
        VALUES
        <foreach collection="list" item="dto" separator=",">
            (#{dto.uuid},#{dto.djxh},#{dto.zsxmDm},#{dto.yzpzzlDm},
             #{dto.skssqq},#{dto.skssqz},#{dto.flzlDm},#{dto.flzlmc},
            #{dto.fileext},#{dto.flzlbslxDm},
             #{dto.filesizes},#{dto.ywqdDm},#{dto.sjgsdq},#{dto.sjcsdq},
             #{dto.lrrsfid},#{dto.xgrsfid},#{dto.lrrq},
             #{dto.xgrq},#{dto.sjtbSj})
        </foreach>
    </insert>
    
</mapper>
