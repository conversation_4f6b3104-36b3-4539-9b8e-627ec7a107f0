<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbHznsqyxfsfpbZjgMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbHznsqyxfsfpbZjgDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="khyh" column="khyh" jdbcType="VARCHAR"/>
            <result property="yhzh" column="yhzh" jdbcType="VARCHAR"/>
            <result property="bsrymc" column="bsrymc" jdbcType="VARCHAR"/>
            <result property="dhhm" column="dhhm" jdbcType="VARCHAR"/>
            <result property="zfjgxsehj" column="zfjgxsehj" jdbcType="DECIMAL"/>
            <result property="zjgxssr" column="zjgxssr" jdbcType="DECIMAL"/>
            <result property="zjgfpbl" column="zjgfpbl" jdbcType="DECIMAL"/>
            <result property="zjgfpse" column="zjgfpse" jdbcType="DECIMAL"/>
            <result property="bsdw" column="bsdw" jdbcType="VARCHAR"/>
            <result property="zgswjgshyj" column="zgswjgshyj" jdbcType="VARCHAR"/>
            <result property="slry" column="slry" jdbcType="VARCHAR"/>
            <result property="zgswjgmc" column="zgswjgmc" jdbcType="VARCHAR"/>
            <result property="slrq" column="slrq" jdbcType="TIMESTAMP"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        khyh,yhzh,bsrymc,
        dhhm,zfjgxsehj,zjgxssr,
        zjgfpbl,zjgfpse,bsdw,
        zgswjgshyj,slry,zgswjgmc,
        slrq,sjblbz,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
