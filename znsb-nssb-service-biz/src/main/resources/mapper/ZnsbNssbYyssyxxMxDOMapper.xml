<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.yyssycj.ZnsbNssbYyssyxxMxDOMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.yyssycj.ZnsbNssbYyssyxxMxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="yysgje" column="yysgje" jdbcType="DECIMAL"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="hxbuuid" column="hxbuuid" jdbcType="VARCHAR"/>
            <result property="hxbmxuuid" column="hxbmxuuid" jdbcType="VARCHAR"/>
            <result property="bczt" column="bczt" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,yysgje,
        sl_1,ynse,lrrq,
        lrr_dm,xgrq,xgr_dm,
        sjgsdq,sjtb_sj,sjblbz,
        zspm_dm,ywqd_dm,sjcsdq,
        xgrsfid,lrrsfid,hxbuuid,
        hxbmxuuid,bczt
    </sql>
    <select id="queryByZbuuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from znsb_nssb_yyssyxx_mx
        where
        zbuuid = #{zbuuid,jdbcType=VARCHAR}
    </select>
</mapper>
