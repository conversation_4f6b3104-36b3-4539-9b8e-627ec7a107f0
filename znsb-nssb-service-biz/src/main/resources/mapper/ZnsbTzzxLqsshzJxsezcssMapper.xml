<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzJxsezcssMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.sshz.ZnsbTzzxLqsshzJxsezcssDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="hzzyfpjxsezc" column="hzzyfpjxsezc" jdbcType="DECIMAL"/>
            <result property="ldsedqjxsezc" column="ldsedqjxsezc" jdbcType="DECIMAL"/>
            <result property="ldsetsjxsezc" column="ldsetsjxsezc" jdbcType="DECIMAL"/>
            <result property="ycpzjxsezc" column="ycpzjxsezc" jdbcType="DECIMAL"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="hzzyfpjxsezcjyssbz" column="hzzyfpjxsezcjyssbz" jdbcType="CHAR"/>
            <result property="ldsedqjxsezcjyssbz" column="ldsedqjxsezcjyssbz" jdbcType="CHAR"/>
            <result property="ldsetsjxsezcjyssbz" column="ldsetsjxsezcjyssbz" jdbcType="CHAR"/>
            <result property="ycpzjxsezcjyssbz" column="ycpzjxsezcjyssbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="cdefine1" column="cDefine1" jdbcType="VARCHAR"/>
            <result property="cdefine2" column="cDefine2" jdbcType="VARCHAR"/>
            <result property="cdefine3" column="cDefine3" jdbcType="VARCHAR"/>
            <result property="cdefine4" column="cDefine4" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zbuuid,hzzyfpjxsezc,
        ldsedqjxsezc,ldsetsjxsezc,ycpzjxsezc,
        hzzyfpjxsezcjyssbz,ldsedqjxsezcjyssbz,ldsetsjxsezcjyssbz,
        ycpzjxsezcjyssbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,
        cDefine1,cDefine2,cDefine3,cDefine4
    </sql>

    <select id="listByJyssbz" parameterType="Long" resultType="com.css.znsb.gjss.pojo.dto.lqsshz.ZnsbTzzxLqsshzJxsezcssDTO">
        SELECT t1.*, t2.djxh, t2.nsrsbh, t2.skssq FROM znsb_tzzx_lqsshz_jxsezcss t1
                                                           LEFT JOIN znsb_tzzx_lqsshz t2 ON t1.zbuuid = t2.uuid
        WHERE
            hzzyfpjxsezcjyssbz = 'N'
           or ldsedqjxsezcjyssbz = 'N'
           or ldsetsjxsezcjyssbz = 'N'
           or ycpzjxsezcjyssbz = 'N'
            limit #{parameter}
    </select>
</mapper>
