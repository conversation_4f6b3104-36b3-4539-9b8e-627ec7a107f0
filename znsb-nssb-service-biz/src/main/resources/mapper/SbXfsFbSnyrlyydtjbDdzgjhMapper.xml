<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbSnyrlyydtjbDdzgjhMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbSnyrlyydtjbDdzgjhDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="xmmc" column="xmmc" jdbcType="VARCHAR"/>
            <result property="gcRly" column="gc_rly" jdbcType="DECIMAL"/>
            <result property="gcSny" column="gc_sny" jdbcType="DECIMAL"/>
            <result property="qygrRly" column="qygr_rly" jdbcType="DECIMAL"/>
            <result property="qygrSny" column="qygr_sny" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,xmmc,gc_rly,
        gc_sny,qygr_rly,qygr_sny,
        sjblbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
