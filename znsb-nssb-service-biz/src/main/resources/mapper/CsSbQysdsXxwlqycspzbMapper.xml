<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.CsSbQysdsXxwlqycspzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.qysds.czzsyjd.CsSbQysdsXxwlqycspzbDto">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="swjgDm" column="swjg_dm" jdbcType="CHAR"/>
            <result property="yxqq" column="yxqq" jdbcType="DATE"/>
            <result property="yxqz" column="yxqz" jdbcType="DATE"/>
            <result property="xxwlqyedxx" column="xxwlqyedxx" jdbcType="DECIMAL"/>
            <result property="xxpdbz" column="xxpdbz" jdbcType="CHAR"/>
            <result property="xxwlqyedsx" column="xxwlqyedsx" jdbcType="DECIMAL"/>
            <result property="sxpdbz" column="sxpdbz" jdbcType="CHAR"/>
            <result property="xxwlqysyjmsl" column="xxwlqysyjmsl" jdbcType="DECIMAL"/>
            <result property="jksjxx" column="jksjxx" jdbcType="VARCHAR"/>
            <result property="sydzsfs" column="sydzsfs" jdbcType="VARCHAR"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="bz1" column="bz1" jdbcType="VARCHAR"/>
            <result property="bz2" column="bz2" jdbcType="VARCHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="sfjbsl" column="sfjbsl" jdbcType="CHAR"/>
            <result property="ljjme" column="ljjme" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,swjg_dm,yxqq,
        yxqz,xxwlqyedxx,xxpdbz,
        xxwlqyedsx,sxpdbz,xxwlqysyjmsl,
        jksjxx,sydzsfs,ssjmxz_dm,
        bz1,bz2,yxbz,
        sfjbsl,ljjme
    </sql>
</mapper>
