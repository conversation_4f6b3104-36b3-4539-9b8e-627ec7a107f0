<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbCpygxcqkMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbCpygxcqkSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="ypxh" column="ypxh" jdbcType="VARCHAR"/>
            <result property="qckcZgsl" column="qckc_zgsl" jdbcType="DECIMAL"/>
            <result property="qckcDcsl" column="qckc_dcsl" jdbcType="DECIMAL"/>
            <result property="qckcZgje" column="qckc_zgje" jdbcType="DECIMAL"/>
            <result property="qckcDcje" column="qckc_dcje" jdbcType="DECIMAL"/>
            <result property="bqrkZgsl" column="bqrk_zgsl" jdbcType="DECIMAL"/>
            <result property="bqrkDcsl" column="bqrk_dcsl" jdbcType="DECIMAL"/>
            <result property="bqrkZgje" column="bqrk_zgje" jdbcType="DECIMAL"/>
            <result property="bqrkDcje" column="bqrk_dcje" jdbcType="DECIMAL"/>
            <result property="bqckYsbfsl" column="bqck_ysbfsl" jdbcType="DECIMAL"/>
            <result property="bqckYsbfje" column="bqck_ysbfje" jdbcType="DECIMAL"/>
            <result property="bqckFysbfZysl" column="bqck_fysbf_zysl" jdbcType="DECIMAL"/>
            <result property="bqckFysbfDcsl" column="bqck_fysbf_dcsl" jdbcType="DECIMAL"/>
            <result property="bqckFysbfZyje" column="bqck_fysbf_zyje" jdbcType="DECIMAL"/>
            <result property="bqckFysbfDcje" column="bqck_fysbf_dcje" jdbcType="DECIMAL"/>
            <result property="qmkcZgsl" column="qmkc_zgsl" jdbcType="DECIMAL"/>
            <result property="qmkcDcsl" column="qmkc_dcsl" jdbcType="DECIMAL"/>
            <result property="qmkcZgje" column="qmkc_zgje" jdbcType="DECIMAL"/>
            <result property="qmkcDcje" column="qmkc_dcje" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,ypxh,qckc_zgsl,
        qckc_dcsl,qckc_zgje,qckc_dcje,
        bqrk_zgsl,bqrk_dcsl,bqrk_zgje,
        bqrk_dcje,bqck_ysbfsl,bqck_ysbfje,
        bqck_fysbf_zysl,bqck_fysbf_dcsl,bqck_fysbf_zyje,
        bqck_fysbf_dcje,qmkc_zgsl,qmkc_dcsl,
        qmkc_zgje,qmkc_dcje,sjblbz,
        lrrq,xgrq,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq,
        sjtb_sj,sjgsdq
    </sql>
</mapper>
