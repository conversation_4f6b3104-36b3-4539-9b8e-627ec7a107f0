<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.znjsq.ZnsbNssbFjmkjbsFahtglbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.znjsq.ZnsbNssbFjmkjbsFahtglbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="kjsbbm" column="kjsbbm" jdbcType="CHAR"/>
            <result property="fjmkjbsnsywpdfabuuid" column="fjmkjbsnsywpdfabuuid" jdbcType="VARCHAR"/>
            <result property="xthtbh" column="xthtbh" jdbcType="VARCHAR"/>
            <result property="fahtbdrq" column="fahtbdrq" jdbcType="TIMESTAMP"/>
            <result property="fahtjbrq" column="fahtjbrq" jdbcType="TIMESTAMP"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,kjsbbm,
        fjmkjbsnsywpdfabuuid,xthtbh,fahtbdrq,
        fahtjbrq,yxbz
    </sql>

<!--    <delete id="deleteByFauuids">-->
<!--        delete from SB_FJMKJBS_FAHTGLB-->
<!--        where NSYWPDJGUUID = #{fauuid} and  XTHTBH in-->
<!--        <foreach collection="htbhs" item="item" open="(" separator="," close=")">-->
<!--            #{item}-->
<!--        </foreach>-->

<!--    </delete>-->
<!--    <select id="getFaglhtListByFaxxUuids" resultType="com.css.znsb.nssb.pojo.domain.znjsq.ZnsbNssbFjmkjbsFahtglbDO">-->
<!--        select FJMKJBSNSYWPDFABUUID, KJSBBM, xthtbh, UUID from SB_FJMKJBS_FAHTGLB-->
<!--        where FJMKJBSNSYWPDFABUUID in-->
<!--        (-->
<!--        <foreach collection="faxxbUuids" item="item" index="index"-->
<!--                 separator=",">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--        )-->
<!--        and yxbz = 'Y' and djxh = #{djxh} and FAHTBDRQ <![CDATA[<=]]> now()  and FAHTJBRQ <![CDATA[>]]> now()-->
<!--    </select>-->
</mapper>
