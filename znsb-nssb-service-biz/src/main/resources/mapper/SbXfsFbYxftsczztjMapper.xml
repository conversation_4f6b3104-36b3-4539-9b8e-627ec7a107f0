<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbYxftsczztjMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbYxftsczztjDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="zzmc1" column="zzmc_1" jdbcType="VARCHAR"/>
            <result property="yltrqkLljgs" column="yltrqk_lljgs" jdbcType="DECIMAL"/>
            <result property="yltrLltjSnysl" column="yltr_lltj_snysl" jdbcType="DECIMAL"/>
            <result property="yltrLltjRnlsl" column="yltr_lltj_rnlsl" jdbcType="DECIMAL"/>
            <result property="yltrCwhsSnysl" column="yltr_cwhs_snysl" jdbcType="DECIMAL"/>
            <result property="yltrCwhsRnlsl" column="yltr_cwhs_rnlsl" jdbcType="DECIMAL"/>
            <result property="cpccLltjgs" column="cpcc_lltjgs" jdbcType="DECIMAL"/>
            <result property="cpccLltjYxsl" column="cpcc_lltj_yxsl" jdbcType="DECIMAL"/>
            <result property="cpccLltjFtsl" column="cpcc_lltj_ftsl" jdbcType="DECIMAL"/>
            <result property="cpccCwhsYxsl" column="cpcc_cwhs_yxsl" jdbcType="DECIMAL"/>
            <result property="cpccCwhsFtsl" column="cpcc_cwhs_ftsl" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,zzmc_1,yltrqk_lljgs,
        yltr_lltj_snysl,yltr_lltj_rnlsl,yltr_cwhs_snysl,
        yltr_cwhs_rnlsl,cpcc_lltjgs,cpcc_lltj_yxsl,
        cpcc_lltj_ftsl,cpcc_cwhs_yxsl,cpcc_cwhs_ftsl,
        sjblbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
