<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsCzjzsymxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsCzjzsymxbDO">
            <id property="czsyuuid" column="czsyuuid" jdbcType="VARCHAR"/>
            <result property="fyxxuuid" column="fyxxuuid" jdbcType="VARCHAR"/>
            <result property="czfnsrsbh" column="czfnsrsbh" jdbcType="VARCHAR"/>
            <result property="czfnsrmc" column="czfnsrmc" jdbcType="VARCHAR"/>
            <result property="czmj" column="czmj" jdbcType="DECIMAL"/>
            <result property="htzjzsr" column="htzjzsr" jdbcType="DECIMAL"/>
            <result property="htydzlqq" column="htydzlqq" jdbcType="TIMESTAMP"/>
            <result property="htydzlqz" column="htydzlqz" jdbcType="TIMESTAMP"/>
            <result property="sbzjsr" column="sbzjsr" jdbcType="DECIMAL"/>
            <result property="sbzjsszlqq" column="sbzjsszlqq" jdbcType="TIMESTAMP"/>
            <result property="sbzjsszlqz" column="sbzjsszlqz" jdbcType="TIMESTAMP"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="yczsyuuid" column="yczsyuuid" jdbcType="VARCHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="bgbm" column="bgbm" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="pkbz" column="pkbz" jdbcType="CHAR"/>
            <result property="jmxzdmxx" column="jmxzdmxx" jdbcType="VARCHAR"/>
            <result property="jmshj" column="jmshj" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="czfdjxh" column="czfdjxh" jdbcType="DECIMAL"/>
            <result property="zjzdpgj" column="zjzdpgj" jdbcType="DECIMAL"/>
            <result property="blbyzyy" column="blbyzyy" jdbcType="VARCHAR"/>
            <result property="yxqbyzyy" column="yxqbyzyy" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        czsyuuid,fyxxuuid,czfnsrsbh,
        czfnsrmc,czmj,htzjzsr,
        htydzlqq,htydzlqz,sbzjsr,
        sbzjsszlqq,sbzjsszlqz,sl_1,
        yxqq,yxqz,yczsyuuid,
        yxbz,zspm_dm,bgbm,
        lrr_dm,lrrq,xgr_dm,
        xgrq,sjgsdq,sjtb_sj,
        pkbz,jmxzdmxx,jmshj,
        sjblbz,czfdjxh,zjzdpgj,
        blbyzyy,yxqbyzyy,ywqd_dm,
        sjcsdq,xgrsfid,lrrsfid,
        uuid,zfbz_1
    </sql>
</mapper>
