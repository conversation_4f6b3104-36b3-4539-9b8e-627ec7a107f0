<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="ysbbz" column="ysbbz" jdbcType="CHAR"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="bczt" column="bczt" jdbcType="CHAR"/>
            <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="hxbuuid" column="hxbuuid" jdbcType="VARCHAR"/>
            <result property="ybczt" column="ybczt" jdbcType="VARCHAR"/>
            <result property="cwxx" column="cwxx" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,uuid,zgswskfj_dm,
        zfr_dm,zfrq_1,zfbz_1,
        ysbbz,yzpzzl_dm,sbsx_dm_1,
        skssqq,skssqz,hy_dm,
        xzqhsz_dm,jdxz_dm,bczt,
        pclsh,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,hxbuuid,ybczt,cwxx
    </sql>

    <select id="querysybtxxbyzbuuid" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List"/> from znsb_nssb_cxsbtxx
        where uuid=#{uuid}
        and djxh=#{djxh}
    </select>

    <update id="zfsybtxx">
        update znsb_nssb_cxsbtxx
        set zfbz_1=#{zfbz1},
            zfr_dm=#{zfrDm},
            zfrq_1=#{zfrq1},
            xgrsfid=#{xgrsfid},
            xgrq=#{xgrq},
            bczt = '30'
        where uuid = #{uuid}
    </update>

    <update id="updateBcztByPclsh">
        update znsb_nssb_cxsbtxx
        set bczt=#{bczt},
            hxbuuid=#{hxbuuid},
            xgrsfid='lqjkhd',
            xgrq=now()
        where pclsh = #{pclsh}
          and (hxbuuid is null or hxbuuid = '')
    </update>

    <select id="queryYcjxxBtforCx" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from znsb_nssb_cxsbtxx
        where COALESCE(zfbz_1, 'N') = 'N'
        AND YZPZZL_DM='BDA0610794'
        AND DJXH=#{djxh}
        <if test='nsqxDm == "11"'>
            AND SKSSQQ &gt;=STR_TO_DATE(#{skssqq},'%Y-%m-%d')
            and skssqz &lt;=STR_TO_DATE(#{skssqz},'%Y-%m-%d')
            and skssqq = skssqz
        </if>
        <if test='nsqxDm == "06"'>
            AND SKSSQQ &gt;=STR_TO_DATE(#{skssqq},'%Y-%m-%d')
            and skssqz &lt;=STR_TO_DATE(#{skssqz},'%Y-%m-%d')
            and skssqq != skssqz
            and 10 &gt;= DATEDIFF(skssqz, skssqq)
            and DATEDIFF(skssqz, skssqq) &lt;= 40
        </if>
        <if test='nsqxDm == "08"'>
            AND SKSSQQ &gt;=STR_TO_DATE(#{skssqq},'%Y-%m-%d')
            and skssqz &lt;=STR_TO_DATE(#{skssqz},'%Y-%m-%d')
            and skssqq != skssqz
            and 40 &gt;= DATEDIFF(skssqz, skssqq)
            and DATEDIFF(skssqz, skssqq) &lt;= 120
        </if>
        <if test='nsqxDm == "10"'>
            AND SKSSQQ &gt;=STR_TO_DATE(#{skssqq},'%Y-%m-%d')
            and skssqz &lt;=STR_TO_DATE(#{skssqz},'%Y-%m-%d')
            and skssqq != skssqz
            and 120 &gt;= DATEDIFF(skssqz, skssqq)
            and DATEDIFF(skssqz, skssqq) &lt;= 370
        </if>
        AND SBSX_DM_1 = #{sbsxdm1}
        -- AND BCZT = '30'
    </select>

    <select id="queryYcjxxBt" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from znsb_nssb_cxsbtxx
        where COALESCE(zfbz_1, 'N') = 'N'
        AND YZPZZL_DM='BDA0610794'
        AND DJXH=#{djxh}
        AND SKSSQQ &gt;=STR_TO_DATE(#{skssqq},'%Y-%m-%d')
        and skssqz &lt;=STR_TO_DATE(#{skssqz},'%Y-%m-%d')
        AND SBSX_DM_1 = #{sbsxdm1}
        -- AND BCZT = '30'
    </select>

    <select id="queryHxbuuidByZbuuid" resultType="java.lang.String">
        select hxbuuid
        from znsb_nssb_cxsbtxx
        where uuid = #{uuid}
    </select>

    <select id="queryYcjxxBtByUUID" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM znsb_nssb_cxsbtxx WHERE COALESCE(zfbz_1, 'N') = 'N' AND UUID = #{uuid}

    </select>
    <select id="queryYcjxxBtByHxbuuid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM znsb_nssb_cxsbtxx WHERE uuid = #{hxbuuid} or hxbuuid = #{hxbuuid}
    </select>

    <select id="queryOneByHxbuuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from znsb_nssb_cxsbtxx
        where
        hxbuuid = #{hxbuuid,jdbcType=VARCHAR}
        AND
        COALESCE(zfbz_1, 'N') = 'N'
    </select>

    <delete id="deleteYshjyssj">
        DELETE FROM znsb_nssb.znsb_nssb_cxsbtxx
        where djxh = #{djxh}
    </delete>
</mapper>
