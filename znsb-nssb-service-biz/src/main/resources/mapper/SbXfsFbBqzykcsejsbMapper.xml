<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbBqzykcsejsbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbBqzykcsejsbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="qckcwtjgynse" column="qckcwtjgynse" jdbcType="DECIMAL"/>
            <result property="dqshwtjgynse" column="dqshwtjgynse" jdbcType="DECIMAL"/>
            <result property="qmkcwtjgynse" column="qmkcwtjgynse" jdbcType="DECIMAL"/>
            <result property="bqbzykcwtjgysynsk" column="bqbzykcwtjgysynsk" jdbcType="DECIMAL"/>
            <result property="dqzykcwtjgynse" column="dqzykcwtjgynse" jdbcType="DECIMAL"/>
            <result property="qckcwgmj" column="qckcwgmj" jdbcType="DECIMAL"/>
            <result property="dqgjmj" column="dqgjmj" jdbcType="DECIMAL"/>
            <result property="qmkcwgmj" column="qmkcwgmj" jdbcType="DECIMAL"/>
            <result property="bqbzykcwgysmj" column="bqbzykcwgysmj" jdbcType="DECIMAL"/>
            <result property="syslCj" column="sysl_cj" jdbcType="DECIMAL"/>
            <result property="dqzykcwgynse" column="dqzykcwgynse" jdbcType="DECIMAL"/>
            <result property="qckcwgsl" column="qckcwgsl" jdbcType="DECIMAL"/>
            <result property="dqwgsl" column="dqwgsl" jdbcType="DECIMAL"/>
            <result property="qmkcwgsl" column="qmkcwgsl" jdbcType="DECIMAL"/>
            <result property="bqbzykcwgyssl" column="bqbzykcwgyssl" jdbcType="DECIMAL"/>
            <result property="syslCl" column="sysl_cl" jdbcType="DECIMAL"/>
            <result property="jldw" column="jldw" jdbcType="VARCHAR"/>
            <result property="bqzykcwgysynsk" column="bqzykcwgysynsk" jdbcType="DECIMAL"/>
            <result property="bqzykcsehj" column="bqzykcsehj" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,zspm_dm,qckcwtjgynse,
        dqshwtjgynse,qmkcwtjgynse,bqbzykcwtjgysynsk,
        dqzykcwtjgynse,qckcwgmj,dqgjmj,
        qmkcwgmj,bqbzykcwgysmj,sysl_cj,
        dqzykcwgynse,qckcwgsl,dqwgsl,
        qmkcwgsl,bqbzykcwgyssl,sysl_cl,
        jldw,bqzykcwgysynsk,bqzykcsehj,
        sjblbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
