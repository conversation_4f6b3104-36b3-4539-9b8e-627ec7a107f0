<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.jmbaxx.ZnsbNssbJmbaxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.jmbaxx.ZnsbNssbJmbaxxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="yhpzuuid" column="yhpzuuid" jdbcType="VARCHAR"/>
            <result property="jmsspsxDm" column="jmsspsx_dm" jdbcType="CHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="jmxmdlDm" column="jmxmdl_dm" jdbcType="CHAR"/>
            <result property="jmxmxlDm" column="jmxmxl_dm" jdbcType="CHAR"/>
            <result property="jmwjDm" column="jmwj_dm" jdbcType="VARCHAR"/>
            <result property="jmlxDm" column="jmlx_dm" jdbcType="CHAR"/>
            <result property="jmfsDm" column="jmfs_dm" jdbcType="VARCHAR"/>
            <result property="jmzlxDm" column="jmzlx_dm" jdbcType="VARCHAR"/>
            <result property="jmqxq" column="jmqxq" jdbcType="TIMESTAMP"/>
            <result property="jmqxz" column="jmqxz" jdbcType="TIMESTAMP"/>
            <result property="ssjmxzhzDm" column="ssjmxzhz_dm" jdbcType="VARCHAR"/>
            <result property="ssjmxzdlDm" column="ssjmxzdl_dm" jdbcType="CHAR"/>
            <result property="ssjmxzxlDm" column="ssjmxzxl_dm" jdbcType="CHAR"/>
            <result property="jzfd" column="jzfd" jdbcType="DECIMAL"/>
            <result property="jzsl" column="jzsl" jdbcType="DECIMAL"/>
            <result property="jzed" column="jzed" jdbcType="DECIMAL"/>
            <result property="sjjzrq" column="sjjzrq" jdbcType="TIMESTAMP"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,skssqq,
        skssqz,yzpzzl_dm,yhpzuuid,
        jmsspsx_dm,zsxm_dm,zspm_dm,
        jmxmdl_dm,jmxmxl_dm,jmwj_dm,
        jmlx_dm,jmfs_dm,jmzlx_dm,
        jmqxq,jmqxz,ssjmxzhz_dm,
        ssjmxzdl_dm,ssjmxzxl_dm,jzfd,
        jzsl,jzed,sjjzrq,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
