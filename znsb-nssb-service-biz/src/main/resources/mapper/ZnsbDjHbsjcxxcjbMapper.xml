<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbDjHbsjcxxcjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.hbs.ZnsbDjHbsjcxxcjbDO">
            <id property="hbsjcxxuuid" column="hbsjcxxuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="hblxr" column="hblxr" jdbcType="VARCHAR"/>
            <result property="lxdh" column="lxdh" jdbcType="VARCHAR"/>
            <result property="sfcycycsfjsbz" column="sfcycycsfjsbz" jdbcType="CHAR"/>
            <result property="sfqdpwxkzbz" column="sfqdpwxkzbz" jdbcType="CHAR"/>
            <result property="tbrq" column="tbrq" jdbcType="TIMESTAMP"/>
            <result property="zgswjgDm" column="zgswjg_dm" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="zfbz_1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="zfrq_1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="jbr" column="jbr" jdbcType="VARCHAR"/>
            <result property="tbrxm" column="tbrxm" jdbcType="VARCHAR"/>
            <result property="zywrwlbsDm" column="zywrwlbs_dm" jdbcType="VARCHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="sfscxwsjzclcs" column="sfscxwsjzclcs" jdbcType="CHAR"/>
            <result property="sfsshljjzclcs" column="sfsshljjzclcs" jdbcType="CHAR"/>
            <result property="sfczhygc" column="sfczhygc" jdbcType="CHAR"/>
            <result property="gjhbjgDm" column="gjhbjg_dm" jdbcType="CHAR"/>
            <result property="gfzxbz" column="gfzxbz" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="acsb" column="acsb" jdbcType="CHAR"/>
            <result property="pwxkzbh" column="pwxkzbh" jdbcType="VARCHAR"/>
            <result property="zzsyhzcmc" column="zzsyhzcmc" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="hxbuuid" column="hxbuuid" jdbcType="VARCHAR"/>
            <result property="bczt" column="bczt" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        hbsjcxxuuid,djxh,xzqhsz_dm,
        hblxr,lxdh,sfcycycsfjsbz,
        sfqdpwxkzbz,tbrq,zgswjg_dm,
        lrr_dm,xgr_dm,sjtb_sj,
        zfbz_1,zfr_dm,zfrq_1,
        jbr,tbrxm,zywrwlbs_dm,
        zgswskfj_dm,jdxz_dm,sfscxwsjzclcs,
        sfsshljjzclcs,sfczhygc,gjhbjg_dm,
        gfzxbz,sjblbz,acsb,
        pwxkzbh,zzsyhzcmc,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,hxbuuid,bczt
    </sql>
</mapper>
