<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsyjsb.ZnsbNssbZzsyjnssbYjssbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zzsyjsb.ZnsbNssbZzsyjnssbYjssb">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="ybrbz" column="ybrbz" jdbcType="CHAR"/>
            <result property="bqsfsyxgmyhzc" column="bqsfsyxgmyhzc" jdbcType="CHAR"/>
            <result property="jzzcsyztDm" column="jzzcsyzt_dm" jdbcType="VARCHAR"/>
            <result property="nsqxDm" column="nsqx_dm" jdbcType="CHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="wcjyhdssglzmbh" column="wcjyhdssglzmbh" jdbcType="VARCHAR"/>
            <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="xmbh" column="xmbh" jdbcType="VARCHAR"/>
            <result property="xmmc" column="xmmc" jdbcType="VARCHAR"/>
            <result property="xmdz" column="xmdz" jdbcType="VARCHAR"/>
            <result property="bdcqzh" column="bdcqzh" jdbcType="VARCHAR"/>
            <result property="fwzlxxdz" column="fwzlxxdz" jdbcType="VARCHAR"/>
            <result property="zgswjDm" column="zgswj_dm" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,nsrsbh,skssqq,
        skssqz,ybrbz,bqsfsyxgmyhzc,
        jzzcsyzt_dm,nsqx_dm,djxh,
        wcjyhdssglzmbh,fybh,jdxz_dm,
        xmbh,xmmc,xmdz,
        bdcqzh,fwzlxxdz,zgswj_dm,
        yxbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
