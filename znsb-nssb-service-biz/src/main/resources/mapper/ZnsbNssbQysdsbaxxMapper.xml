<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.ZnsbNssbQysdsbaxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sds.ZnsbNssbQysdsbaxxDO">
            <id property="hznsuuid" column="hznsuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="bjdyjqysdsyyDm" column="bjdyjqysdsyy_dm" jdbcType="CHAR"/>
            <result property="bjdyjqysdsyy" column="bjdyjqysdsyy" jdbcType="VARCHAR"/>
            <result property="btsldqbs" column="btsldqbs" jdbcType="CHAR"/>
            <result property="cyndhqjbz" column="cyndhqjbz" jdbcType="CHAR"/>
            <result property="fzjgcjDm" column="fzjgcj_dm" jdbcType="VARCHAR"/>
            <result property="fzjgcjmc" column="fzjgcjmc" jdbcType="VARCHAR"/>
            <result property="fzjgjxfpbz" column="fzjgjxfpbz" jdbcType="CHAR"/>
            <result property="hjqylxDm" column="hjqylx_dm" jdbcType="CHAR"/>
            <result property="hjqylxmc" column="hjqylxmc" jdbcType="VARCHAR"/>
            <result property="hzhbnsqyjglbDm" column="hzhbnsqyjglb_dm" jdbcType="VARCHAR"/>
            <result property="hzhbnsqyjglbmc" column="hzhbnsqyjglbmc" jdbcType="VARCHAR"/>
            <result property="jdjnbs" column="jdjnbs" jdbcType="CHAR"/>
            <result property="kdqsszyqylxDm" column="kdqsszyqylx_dm" jdbcType="CHAR"/>
            <result property="kdqsszyqylxmc" column="kdqsszyqylxmc" jdbcType="VARCHAR"/>
            <result property="ndhsqjysfpblDm" column="ndhsqjysfpbl_dm" jdbcType="CHAR"/>
            <result property="ndhsqjysfpblmc" column="ndhsqjysfpblmc" jdbcType="VARCHAR"/>
            <result property="ndhsqjysjcDm" column="ndhsqjysjc_dm" jdbcType="CHAR"/>
            <result property="ndhsqjysjcmc" column="ndhsqjysjcmc" jdbcType="VARCHAR"/>
            <result property="ndhsqjyskmDm" column="ndhsqjyskm_dm" jdbcType="VARCHAR"/>
            <result property="ndhsqjyskmmc" column="ndhsqjyskmmc" jdbcType="VARCHAR"/>
            <result property="yjdyjysfpblDm" column="yjdyjysfpbl_dm" jdbcType="CHAR"/>
            <result property="yjdyjysfpblmc" column="yjdyjysfpblmc" jdbcType="VARCHAR"/>
            <result property="yjdyjysjcDm" column="yjdyjysjc_dm" jdbcType="CHAR"/>
            <result property="yjdyjysjcmc" column="yjdyjysjcmc" jdbcType="VARCHAR"/>
            <result property="yjdyjyskmDm" column="yjdyjyskm_dm" jdbcType="VARCHAR"/>
            <result property="yjdyjyskmmc" column="yjdyjyskmmc" jdbcType="VARCHAR"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="zfjglxDm" column="zfjglx_dm" jdbcType="CHAR"/>
            <result property="zfjglxmc" column="zfjglxmc" jdbcType="VARCHAR"/>
            <result property="zjgzgzt" column="zjgzgzt" jdbcType="CHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="blr" column="blr" jdbcType="VARCHAR"/>
            <result property="blrysfzjhm" column="blrysfzjhm" jdbcType="VARCHAR"/>
            <result property="blrysfzjlxDm" column="blrysfzjlx_dm" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        hznsuuid,djxh,bjdyjqysdsyy_dm,
        bjdyjqysdsyy,btsldqbs,cyndhqjbz,
        fzjgcj_dm,fzjgcjmc,fzjgjxfpbz,
        hjqylx_dm,hjqylxmc,hzhbnsqyjglb_dm,
        hzhbnsqyjglbmc,jdjnbs,kdqsszyqylx_dm,
        kdqsszyqylxmc,ndhsqjysfpbl_dm,ndhsqjysfpblmc,
        ndhsqjysjc_dm,ndhsqjysjcmc,ndhsqjyskm_dm,
        ndhsqjyskmmc,yjdyjysfpbl_dm,yjdyjysfpblmc,
        yjdyjysjc_dm,yjdyjysjcmc,yjdyjyskm_dm,
        yjdyjyskmmc,yxqq,yxqz,
        zfjglx_dm,zfjglxmc,zjgzgzt,
        zfbz_1,zfrq_1,zfr_dm,
        blr,blrysfzjhm,blrysfzjlx_dm,
        lrr_dm,xgr_dm,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
