<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxJmsmxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxJmsmxbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="gsh" column="gsh" jdbcType="VARCHAR"/>
            <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
            <result property="kjpzbh" column="kjpzbh" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="kmdm" column="kmdm" jdbcType="VARCHAR"/>
            <result property="kmmc" column="kmmc" jdbcType="VARCHAR"/>
            <result property="jmzlxDm" column="jmzlx_dm" jdbcType="VARCHAR"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="bqfse" column="bqfse" jdbcType="DECIMAL"/>
            <result property="mzzzsxmxse" column="mzzzsxmxse" jdbcType="DECIMAL"/>
            <result property="mzxsekcxmbqsjkcje" column="mzxsekcxmbqsjkcje" jdbcType="DECIMAL"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ly" column="ly" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="jyssbz" column="jyssbz" jdbcType="CHAR"/>
            <result property="zzscbz" column="zzscbz" jdbcType="CHAR"/>
            <result property="scbz" column="sjgsdq" jdbcType="CHAR"/>
            <result property="scsj1" column="sjgsdq" jdbcType="TIMESTAMP"/>
            <result property="zzuuid" column="sjgsdq" jdbcType="VARCHAR"/>
            <result property="kjfpDm" column="kjfp_dm" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sszq,djxh,
        nsrsbh,nsrmc,gsh,
        lrzx,kjpzbh,sl_1,
        kmdm,kmmc,
        jmzlx_dm,ssjmxz_dm,
        swsx_dm,bqfse,
        mzzzsxmxse,mzxsekcxmbqsjkcje,tzlx_dm,
        lrrq,lrrsfid,xgrq,
        xgrsfid,ly,ywqd_dm,
        sjcsdq,sjtb_sj,sjgsdq,jyssbz,
        zzscbz,scbz,scsj1,zzuuid,kjfp_dm
    </sql>

    <select id="dataCompareSum" resultType="java.math.BigDecimal">
        select sum(bqfse)
        from znsb_tzzx_jmsmxb
        where djxh = #{djxh}
          and sszq = #{sszq}
          and ly = '0'
    </select>


</mapper>
