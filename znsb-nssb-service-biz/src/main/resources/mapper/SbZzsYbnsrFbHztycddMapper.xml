<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHztycddMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbHztycddSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="bz" column="bz" jdbcType="VARCHAR"/>
            <result property="bjse" column="bjse" jdbcType="DECIMAL"/>
            <result property="yjse1" column="yjse_1" jdbcType="DECIMAL"/>
            <result property="yzl" column="yzl" jdbcType="DECIMAL"/>
            <result property="xse" column="xse" jdbcType="DECIMAL"/>
            <result property="zsxm" column="zsxm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,bz,bjse,
        yjse_1,yzl,xse,
        zsxm,lrrq,xgrq,
        sjgsdq,sjblbz,sjtb_sj,
        lrrsfid,xgrsfid,ywqd_dm,
        sjcsdq
    </sql>
</mapper>
