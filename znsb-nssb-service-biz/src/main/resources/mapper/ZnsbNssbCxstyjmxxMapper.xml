<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxstyjmxxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxstyjmxxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="cxstysbuuid" column="cxstysbuuid" jdbcType="VARCHAR"/>
            <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="zywrwlbDm" column="zywrwlb_dm" jdbcType="VARCHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="jmse" column="jmse" jdbcType="DECIMAL"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="sybh" column="sybh" jdbcType="CHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,cxstysbuuid,zbuuid,
        zsxm_dm,zspm_dm,zszm_dm,
        zywrwlb_dm,syuuid,jmse,
        ssjmxz_dm,swsx_dm,sybh,
        zfbz_1,zfrq_1,pzxh,
        skssqq,skssqz,lrrq,
        lrrsfid,xgrq,xgrsfid,
        ywqd_dm,sjcsdq,sjgsdq,
        sjtb_sj
    </sql>
</mapper>
