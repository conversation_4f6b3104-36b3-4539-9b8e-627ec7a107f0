<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.gjss.mapper.srzz.jyk.ZnsbTzzxWkpmxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxWkpmxbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <id property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
            <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
            <result property="jsfsDm1" column="jsfs_dm_1" jdbcType="CHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="kmdm" column="kmdm" jdbcType="VARCHAR"/>
            <result property="kmmc" column="kmmc" jdbcType="VARCHAR"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="ly" column="ly" jdbcType="CHAR"/>
            <result property="kjpzbh" column="kjpzbh" jdbcType="VARCHAR"/>
            <result property="kjfp" column="kjfp" jdbcType="VARCHAR"/>
            <result property="je" column="je" jdbcType="DECIMAL"/>
            <result property="se" column="se" jdbcType="DECIMAL"/>
            <result property="ckpzh" column="ckpzh" jdbcType="VARCHAR"/>
            <result property="tbbz" column="tbbz" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="zzscbz" column="zzscbz" jdbcType="CHAR"/>
            <result property="jyssbz" column="jyssbz" jdbcType="CHAR"/>
            <result property="pzsl" column="pzsl_2" jdbcType="BIGINT"/>
            <result property="pztt" column="pztt" jdbcType="VARCHAR"/>
            <result property="wbzlmc" column="wbzlmc" jdbcType="VARCHAR"/>
            <result property="czr" column="czr" jdbcType="VARCHAR"/>
            <result property="tzyy" column="tzyy" jdbcType="VARCHAR"/>
            <result property="tzsj" column="tzsj_1" jdbcType="TIMESTAMP"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sszq,djxh,
        nsrsbh,nsrmc,gsh_2,
        lrzx,jsfs_dm_1,zsxm_dm,
        sl_1,kmdm,kmmc,
        tzlx_dm,ly,kjpzbh,
        kjfp,je,se,
        ckpzh,tbbz,yxbz,
        zzscbz,jyssbz,pzsl_2,
        pztt,wbzlmc,czr,
        tzyy,tzsj_1,ywqd_dm,
        lrrq,lrrsfid,xgrq,
        xgrsfid,sjcsdq,sjgsdq,
        sjtb_sj
    </sql>
</mapper>
