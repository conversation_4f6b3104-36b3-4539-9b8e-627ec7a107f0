<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.SbSdsJmczYjdDynsbabMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sds.SbSdsJmczYjdDynsbabDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="mxxh" column="mxxh" jdbcType="BIGINT"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="jscgmc" column="jscgmc" jdbcType="VARCHAR"/>
            <result property="jscglx" column="jscglx" jdbcType="VARCHAR"/>
            <result property="jscgbh" column="jscgbh" jdbcType="VARCHAR"/>
            <result property="gyjz" column="gyjz" jdbcType="DECIMAL"/>
            <result property="jsjc" column="jsjc" jdbcType="DECIMAL"/>
            <result property="gqqdsj" column="gqqdsj" jdbcType="TIMESTAMP"/>
            <result property="dysd" column="dysd" jdbcType="DECIMAL"/>
            <result property="btzqymc" column="btzqymc" jdbcType="VARCHAR"/>
            <result property="btznsrsbh" column="btznsrsbh" jdbcType="VARCHAR"/>
            <result property="swjgmc" column="swjgmc" jdbcType="VARCHAR"/>
            <result property="sfwglqy" column="sfwglqy" jdbcType="CHAR"/>
            <result property="bz" column="bz" jdbcType="VARCHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        mxxh,zfbz_1,jscgmc,
        jscglx,jscgbh,gyjz,
        jsjc,gqqdsj,dysd,
        btzqymc,btznsrsbh,swjgmc,
        sfwglqy,bz,sjblbz,
        lrrq,xgrq,sjgsdq,
        sjtb_sj,lrrsfid,xgrsfid,
        ywqd_dm,sjcsdq
    </sql>
    <select id="queryBDA0611033Dynsbab" resultType="java.util.Map">
        select t.mxxh as ewbhxh,t.jscgmc,
               case t.jscglx when '1' then '专利技术' when '2' then '计算机软件著作权' when '3' then '集成电路布图设计权'
                             when '4'then '植物新品种（权）' when '5' then '生物医药新品种' when 'Z' then '其他技术成果' end as jscglx ,
               t.jscgbh,t.gyjz,t.jsjc,
               DATE_FORMAT(CAST(t.gqqdsj AS DATE), '%Y-%m-%d') as gqqdsj,
               t.dysd,t.btzqymc,t.btznsrsbh,
               case t.sfwglqy when 'N' then '否' when 'Y' then '是' end as sfwglqy,t.swjgmc,t.bz
        from sb_sds_jmcz_yjd_dynsbab t
        where t.sbuuid = #{sbuuid}
        order by mxxh asc
    </select>
</mapper>
