<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjGfMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.hbs.ZnsbSbCxsHbsSbjscjGfDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="hgbhssybh" column="hgbhssybh" jdbcType="VARCHAR"/>
            <result property="yf" column="yf" jdbcType="CHAR"/>
            <result property="pfkmc" column="pfkmc" jdbcType="VARCHAR"/>
            <result property="zywrwlbDm" column="zywrwlb_dm" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
            <result property="dqcsl" column="dqcsl" jdbcType="DECIMAL"/>
            <result property="dqccl" column="dqccl" jdbcType="DECIMAL"/>
            <result property="dqczl" column="dqczl" jdbcType="DECIMAL"/>
            <result property="dqzhlyl" column="dqzhlyl" jdbcType="DECIMAL"/>
            <result property="wrwpfl" column="wrwpfl" jdbcType="DECIMAL"/>
            <result property="wrdlz" column="wrdlz" jdbcType="DECIMAL"/>
            <result property="wrdls" column="wrdls" jdbcType="DECIMAL"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="ynse" column="ynse" jdbcType="DECIMAL"/>
            <result property="jmse" column="jmse" jdbcType="DECIMAL"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="yjse" column="yjse" jdbcType="DECIMAL"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="sbbz" column="sbbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,skssqq,skssqz,
        sbsx_dm_1,syuuid,hgbhssybh,
        yf,pfkmc,zywrwlb_dm,
        zspm_dm,zszm_dm,dqcsl,
        dqccl,dqczl,dqzhlyl,
        wrwpfl,wrdlz,wrdls,
        swsx_dm,ssjmxz_dm,sl_1,
        ynse,jmse,lrr_dm,
        xgr_dm,yxbz,yjse,
        ybtse,sbbz,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
