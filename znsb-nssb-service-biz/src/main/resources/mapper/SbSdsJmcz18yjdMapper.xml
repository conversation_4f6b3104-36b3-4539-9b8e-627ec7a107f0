<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.qysds.SbSdsJmcz18yjdMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sds.SbSdsJmcz18yjdDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="qmcyrs" column="qmcyrs" jdbcType="BIGINT"/>
            <result property="sfsyxxwlqy" column="sfsyxxwlqy" jdbcType="CHAR"/>
            <result property="sfkjxzxqy" column="sfkjxzxqy" jdbcType="CHAR"/>
            <result property="sfgxjsqy" column="sfgxjsqy" jdbcType="CHAR"/>
            <result property="sffsjsrgdynssx" column="sffsjsrgdynssx" jdbcType="CHAR"/>
            <result property="yysrLj" column="yysr_lj" jdbcType="DECIMAL"/>
            <result property="yycbLj" column="yycb_lj" jdbcType="DECIMAL"/>
            <result property="lrzeLj" column="lrze_lj" jdbcType="DECIMAL"/>
            <result property="tdywjsdynssdeLj" column="tdywjsdynssde_lj" jdbcType="DECIMAL"/>
            <result property="bzssrLj" column="bzssr_lj" jdbcType="DECIMAL"/>
            <result property="mssrLj" column="mssr_lj" jdbcType="DECIMAL"/>
            <result property="gdzcjszjkctjeLj" column="gdzcjszjkctje_lj" jdbcType="DECIMAL"/>
            <result property="mbyqndksLj" column="mbyqndks_lj" jdbcType="DECIMAL"/>
            <result property="sjlreLj" column="sjlre_lj" jdbcType="DECIMAL"/>
            <result property="slLj" column="sl_lj" jdbcType="DECIMAL"/>
            <result property="ynsdseLj" column="ynsdse_lj" jdbcType="DECIMAL"/>
            <result property="jmsdseLj" column="jmsdse_lj" jdbcType="DECIMAL"/>
            <result property="sjyyjsdseLj" column="sjyyjsdse_lj" jdbcType="DECIMAL"/>
            <result property="tdywyjzsdseLj" column="tdywyjzsdse_lj" jdbcType="DECIMAL"/>
            <result property="ybtsdseLj" column="ybtsdse_lj" jdbcType="DECIMAL"/>
            <result property="zjgybtsdseBq" column="zjgybtsdse_bq" jdbcType="DECIMAL"/>
            <result property="zjgyftsdseBq" column="zjgyftsdse_bq" jdbcType="DECIMAL"/>
            <result property="zjgdlscjybmyftsdseBq" column="zjgdlscjybmyftsdse_bq" jdbcType="DECIMAL"/>
            <result property="fpbl" column="fpbl" jdbcType="DECIMAL"/>
            <result property="fzjgfpsdseBq" column="fzjgfpsdse_bq" jdbcType="DECIMAL"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="fddbr" column="fddbr" jdbcType="VARCHAR"/>
            <result property="fddbrqzrq" column="fddbrqzrq" jdbcType="TIMESTAMP"/>
            <result property="kjzg" column="kjzg" jdbcType="VARCHAR"/>
            <result property="dlsbzjjg" column="dlsbzjjg" jdbcType="VARCHAR"/>
            <result property="dlsbrq" column="dlsbrq" jdbcType="TIMESTAMP"/>
            <result property="jbr" column="jbr" jdbcType="VARCHAR"/>
            <result property="jbrzyzjhm" column="jbrzyzjhm" jdbcType="VARCHAR"/>
            <result property="slr" column="slr" jdbcType="VARCHAR"/>
            <result property="slrq" column="slrq" jdbcType="TIMESTAMP"/>
            <result property="zgswjg" column="zgswjg" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="yjfs" column="yjfs" jdbcType="CHAR"/>
            <result property="sbqylx" column="sbqylx" jdbcType="CHAR"/>
            <result property="zjgftbl" column="zjgftbl" jdbcType="DECIMAL"/>
            <result property="fzjgftbl" column="fzjgftbl" jdbcType="DECIMAL"/>
            <result property="zjgczjzftbl" column="zjgczjzftbl" jdbcType="DECIMAL"/>
            <result property="dlscjybmftbl" column="dlscjybmftbl" jdbcType="DECIMAL"/>
            <result property="tbrq1" column="tbrq_1" jdbcType="TIMESTAMP"/>
            <result property="sfdlsb" column="sfdlsb" jdbcType="CHAR"/>
            <result property="blr" column="blr" jdbcType="VARCHAR"/>
            <result property="blrysfzjlxDm" column="blrysfzjlx_dm" jdbcType="CHAR"/>
            <result property="blrysfzjhm" column="blrysfzjhm" jdbcType="VARCHAR"/>
            <result property="czjzfpsdseBq" column="czjzfpsdse_bq" jdbcType="DECIMAL"/>
            <result property="fzjgyftsdseBq" column="fzjgyftsdse_bq" jdbcType="DECIMAL"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="qczcze" column="qczcze" jdbcType="DECIMAL"/>
            <result property="qmzcze" column="qmzcze" jdbcType="DECIMAL"/>
            <result property="qccyrs" column="qccyrs" jdbcType="BIGINT"/>
            <result property="gjxzhjzhy" column="gjxzhjzhy" jdbcType="CHAR"/>
            <result property="dljgtyshxydm" column="dljgtyshxydm" jdbcType="VARCHAR"/>
            <result property="qccyrs1" column="qccyrs1" jdbcType="BIGINT"/>
            <result property="qmcyrs1" column="qmcyrs1" jdbcType="BIGINT"/>
            <result property="qccyrs2" column="qccyrs2" jdbcType="BIGINT"/>
            <result property="qmcyrs2" column="qmcyrs2" jdbcType="BIGINT"/>
            <result property="qccyrs3" column="qccyrs3" jdbcType="BIGINT"/>
            <result property="qmcyrs3" column="qmcyrs3" jdbcType="BIGINT"/>
            <result property="qccyrs4" column="qccyrs4" jdbcType="BIGINT"/>
            <result property="qmcyrs4" column="qmcyrs4" jdbcType="BIGINT"/>
            <result property="qycyrsQnpjrs" column="qycyrs_qnpjrs" jdbcType="DECIMAL"/>
            <result property="qczcze1" column="qczcze1" jdbcType="DECIMAL"/>
            <result property="qmzcze1" column="qmzcze1" jdbcType="DECIMAL"/>
            <result property="qczcze2" column="qczcze2" jdbcType="DECIMAL"/>
            <result property="qmzcze2" column="qmzcze2" jdbcType="DECIMAL"/>
            <result property="qczcze3" column="qczcze3" jdbcType="DECIMAL"/>
            <result property="qmzcze3" column="qmzcze3" jdbcType="DECIMAL"/>
            <result property="qczcze4" column="qczcze4" jdbcType="DECIMAL"/>
            <result property="qmzcze4" column="qmzcze4" jdbcType="DECIMAL"/>
            <result property="zczeQnpjs" column="zcze_qnpjs" jdbcType="DECIMAL"/>
            <result property="yhjnsds" column="yhjnsds" jdbcType="CHAR"/>
            <result property="fhtjxwqyyhjzsdseLj" column="fhtjxwqyyhjzsdse_lj" jdbcType="DECIMAL"/>
            <result property="bhzsm" column="bhzsm" jdbcType="VARCHAR"/>
            <result property="sdjmLj" column="sdjm_lj" jdbcType="DECIMAL"/>
            <result property="zyjsrsjynseBq" column="zyjsrsjynse_bq" jdbcType="DECIMAL"/>
            <result property="dfjsrynseBq" column="dfjsrynse_bq" jdbcType="DECIMAL"/>
            <result property="jzmzlx" column="jzmzlx" jdbcType="VARCHAR"/>
            <result property="jzfd" column="jzfd" jdbcType="DECIMAL"/>
            <result property="mzzzdqdfjmBq" column="mzzzdqdfjm_bq" jdbcType="DECIMAL"/>
            <result property="mzzzdqdfjmLj" column="mzzzdqdfjm_lj" jdbcType="DECIMAL"/>
            <result property="mzzzdqdfjmzfjghjLj" column="mzzzdqdfjmzfjghj_lj" jdbcType="DECIMAL"/>
            <result property="dfjsrsjynseBq" column="dfjsrsjynse_bq" jdbcType="DECIMAL"/>
            <result property="sjybtsdseBq" column="sjybtsdse_bq" jdbcType="DECIMAL"/>
            <result property="jzfd8wjd" column="jzfd_8wjd" jdbcType="DECIMAL"/>
            <result property="fzjgqnljfpje" column="fzjgqnljfpje" jdbcType="DECIMAL"/>
            <result property="fzjgqnljyxsmzdfyhje" column="fzjgqnljyxsmzdfyhje" jdbcType="DECIMAL"/>
            <result property="fzjgqnljyxsmzdfyhje1" column="fzjgqnljyxsmzdfyhje_1" jdbcType="DECIMAL"/>
            <result property="bxsxwyhly" column="bxsxwyhly" jdbcType="VARCHAR"/>
            <result property="qysdsyjsbmsDm" column="qysdsyjsbms_dm" jdbcType="CHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        qmcyrs,sfsyxxwlqy,sfkjxzxqy,
        sfgxjsqy,sffsjsrgdynssx,yysr_lj,
        yycb_lj,lrze_lj,tdywjsdynssde_lj,
        bzssr_lj,mssr_lj,gdzcjszjkctje_lj,
        mbyqndks_lj,sjlre_lj,sl_lj,
        ynsdse_lj,jmsdse_lj,sjyyjsdse_lj,
        tdywyjzsdse_lj,ybtsdse_lj,zjgybtsdse_bq,
        zjgyftsdse_bq,zjgdlscjybmyftsdse_bq,fpbl,
        fzjgfpsdse_bq,lrrq,xgrq,
        sjgsdq,fddbr,fddbrqzrq,
        kjzg,dlsbzjjg,dlsbrq,
        jbr,jbrzyzjhm,slr,
        slrq,zgswjg,sjtb_sj,
        yjfs,sbqylx,zjgftbl,
        fzjgftbl,zjgczjzftbl,dlscjybmftbl,
        tbrq_1,sfdlsb,blr,
        blrysfzjlx_dm,blrysfzjhm,czjzfpsdse_bq,
        fzjgyftsdse_bq,xzqhsz_dm,sjblbz,
        qczcze,qmzcze,qccyrs,
        gjxzhjzhy,dljgtyshxydm,qccyrs1,
        qmcyrs1,qccyrs2,qmcyrs2,
        qccyrs3,qmcyrs3,qccyrs4,
        qmcyrs4,qycyrs_qnpjrs,qczcze1,
        qmzcze1,qczcze2,qmzcze2,
        qczcze3,qmzcze3,qczcze4,
        qmzcze4,zcze_qnpjs,yhjnsds,
        fhtjxwqyyhjzsdse_lj,bhzsm,sdjm_lj,
        zyjsrsjynse_bq,dfjsrynse_bq,jzmzlx,
        jzfd,mzzzdqdfjm_bq,mzzzdqdfjm_lj,
        mzzzdqdfjmzfjghj_lj,dfjsrsjynse_bq,sjybtsdse_bq,
        jzfd_8wjd,fzjgqnljfpje,fzjgqnljyxsmzdfyhje,
        fzjgqnljyxsmzdfyhje_1,bxsxwyhly,qysdsyjsbms_dm,
        lrrsfid,xgrsfid,ywqd_dm,
        sjcsdq
    </sql>
    <select id="queryBDA0611159A200000" resultType="java.util.Map">
        SELECT T.*,
               case T.YJFS
                   when '1' then '按照实际利润额预缴'
                   when '2' then '按照上一纳税年度应纳税所得额平均额预缴'
                   when '3' then '按照税务机关确定的其他方法预缴' end AS yjfs1,
               case T.SBQYLX
                   when '0' then '一般企业'
                   when '1' then '跨地区经营汇总纳税企业总机构'
                   when '2' then '跨地区经营汇总纳税企业分支机构' end AS sbqylx1,
               case T.SFGXJSQY when 'N' then '否' when 'Y' then '是' end AS sfgxjsqy1,
               case T.SFKJXZXQY when 'N' then '否' when 'Y' then '是' end AS sfkjxzxqy1,
               case T.SFFSJSRGDYNSSX when 'N' then '否' when 'Y' then '是' end AS sffsjsrgdynssx1,
               case T.GJXZHJZHY when 'N' then '否' when 'Y' then '是' end AS gjxzhjzhy1,
               case T.SFSYXXWLQY when 'N' then '否' when 'Y' then '是' end AS sfsyxxwlqy1,
               case T.GJXZHJZHY when 'N' then '否' when 'Y' then '是' end AS gjxzhjzhy,
               case T.YHJNSDS when 'N' then '否' when 'Y' then '是' end AS yhjnsds
        FROM SB_SDS_JMCZ_18YJD T
        WHERE T.SBUUID = #{sbuuid}
    </select>
</mapper>
