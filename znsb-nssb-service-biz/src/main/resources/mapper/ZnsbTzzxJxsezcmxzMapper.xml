<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxJxsezcmxzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.jxsezc.ZnsbTzzxJxsezcmxz">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="zzuuid" column="zzuuid" jdbcType="VARCHAR"/>
        <result property="sszq" column="sszq" jdbcType="INTEGER"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
        <result property="gsh" column="gsh" jdbcType="VARCHAR"/>
        <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
        <result property="kjpzbh" column="kjpzbh" jdbcType="VARCHAR"/>
        <result property="jxsezcxmDm" column="jxsezcxm_dm" jdbcType="CHAR"/>
        <result property="zcrq" column="zcrq" jdbcType="TIMESTAMP"/>
        <result property="zcse" column="zcse" jdbcType="DECIMAL"/>
        <result property="jzjtse" column="jzjtse" jdbcType="DECIMAL"/>
        <result property="kmbm" column="kmbm" jdbcType="VARCHAR"/>
        <result property="kmmc" column="kmmc" jdbcType="VARCHAR"/>
        <result property="kjfpDm" column="kjfp_dm" jdbcType="VARCHAR"/>
        <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
        <result property="scbz" column="scbz" jdbcType="CHAR"/>
        <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
        <result property="ly" column="ly" jdbcType="CHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="zzscbz" column="zzscbz" jdbcType="CHAR"/>
        <result property="jyssbz" column="jyssbz" jdbcType="CHAR"/>
        <result property="jzjtbz" column="jzjtbz" jdbcType="CHAR"/>
        <result property="pztt" column="pztt" jdbcType="VARCHAR"/>
        <result property="czr" column="czr" jdbcType="VARCHAR"/>
        <result property="tzsj" column="tzsj_1" jdbcType="TIMESTAMP"/>
        <result property="tzyy" column="tzyy" jdbcType="VARCHAR"/>
        <result property="glzxxuuid" column="glzxxuuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,zzuuid,sszq,
        djxh,nsrsbh,nsrmc,
        gsh,lrzx,kjpzbh,
        jxsezcxm_dm,zcrq,zcse,
        jzjtse,kmbm,kmmc,
        kjfp_dm,tzlx_dm,scbz,
        scsj_1,ly,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj,zzscbz,jyssbz,
        jzjtbz,pztt,czr,
        tzsj_1,tzyy,glzxxuuid
    </sql>

    <select id="queryJxsezcmxzData"  resultType="com.css.znsb.tzzx.pojo.domain.jxsezc.ZnsbTzzxJxsezczz">
        select djxh,nsrsbh,nsrmc,sszq,jxsezcxm_dm,sum(zcse) zcse,sum(jzjtse) jzjtse from znsb_tzzx.znsb_tzzx_jxsezcmxz where sszq = #{sszq} and djxh = #{djxh} and nsrsbh = #{nsrsbh} and nsrmc = #{nsrmc} group by sszq,jxsezcxm_dm,djxh,nsrsbh,nsrmc
    </select>

    <select id="dataCompareSum" resultType="java.math.BigDecimal">
        select sum(zcse)
        from znsb_tzzx_jxsezcmxz
        where djxh = #{djxh}
        and sszq = #{sszq}
        and ly = '0'
    </select>

</mapper>
