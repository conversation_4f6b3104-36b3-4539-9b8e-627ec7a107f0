<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbCbfhdncpMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbCbfhdncpSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="dqyxdkncpjxse" column="dqyxdkncpjxse" jdbcType="DECIMAL"/>
            <result property="ncphyl" column="ncphyl" jdbcType="DECIMAL"/>
            <result property="dqzyywcb" column="dqzyywcb" jdbcType="DECIMAL"/>
            <result property="kcl" column="kcl" jdbcType="DECIMAL"/>
            <result property="cpmc1" column="cpmc_1" jdbcType="VARCHAR"/>
            <result property="xh" column="xh" jdbcType="INTEGER"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        dqyxdkncpjxse,ncphyl,dqzyywcb,
        kcl,cpmc_1,xh,
        lrr_dm,lrrq,xgr_dm,
        xgrq,sjgsdq,sjblbz,
        lrrsfid,xgrsfid,ywqd_dm,
        sjcsdq,sjtb_sj
    </sql>
</mapper>
