<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.ysbtj.ZnsbNssbSbYsbtjMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.ysbtj.ZnsbNssbSbYsbtjDO">
            <id property="ysbtjuuid" column="ysbtjuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
            <result property="nd" column="nd" jdbcType="VARCHAR"/>
            <result property="yf" column="yf" jdbcType="CHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="nsqxDm" column="nsqx_dm" jdbcType="CHAR"/>
            <result property="sbqx" column="sbqx" jdbcType="TIMESTAMP"/>
            <result property="ysbqx" column="ysbqx" jdbcType="TIMESTAMP"/>
            <result property="nssbrq" column="nssbrq" jdbcType="TIMESTAMP"/>
            <result property="sbfsDm" column="sbfs_dm" jdbcType="CHAR"/>
            <result property="zsfsDm" column="zsfs_dm" jdbcType="CHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="ssglyDm" column="ssgly_dm" jdbcType="CHAR"/>
            <result property="zgswjDm" column="zgswj_dm" jdbcType="CHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
            <result property="zsdlfsDm" column="zsdlfs_dm" jdbcType="CHAR"/>
            <result property="sjlybz" column="sjlybz" jdbcType="CHAR"/>
            <result property="sybh1" column="sybh_1" jdbcType="VARCHAR"/>
            <result property="kjzdzzDm" column="kjzdzz_dm" jdbcType="CHAR"/>
            <result property="sfcbjjkwsb" column="sfcbjjkwsb" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="lbDm" column="lb_dm" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ysbtjuuid,djxh,zsxm_dm,
        zspm_dm,nd,yf,
        skssqq,skssqz,nsqx_dm,
        sbqx,ysbqx,nssbrq,
        sbfs_dm,zsfs_dm,zfbz_1,
        zfrq_1,zfr_dm,ssgly_dm,
        zgswj_dm,zgswskfj_dm,hy_dm,
        zsdlfs_dm,sjlybz,sybh_1,
        kjzdzz_dm,sfcbjjkwsb,sjblbz,
        lb_dm,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
    <!--财行税Column-->
    <sql id="Cxs_Column_List">
        djxh,nsrsbh,nsrmc,xzqhsz_dm,zsxm_dm,skssqq,skssqz,nsqx_dm,sbqx
    </sql>
    <select id="queryCxsYsbtjsj" resultType="com.css.znsb.nssb.pojo.domain.ysbtj.ZnsbNssbSbYsbtjDO">
        select <include refid="Cxs_Column_List"/>
            from znsb_nssb_sb_ysbtj
            where nd = #{nd} and yf = #{yf} and nsqx_dm != '11'
            and zsxm_dm in
            <foreach item="zsxmDm" collection="zsxmDmList" open="(" separator="," close=")">
                #{zsxmDm}
            </foreach>
            group by <include refid="Cxs_Column_List"/>
    </select>
</mapper>
