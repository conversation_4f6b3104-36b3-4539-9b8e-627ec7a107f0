<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cjrjybzj.ZnsbCjrjybzjMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cjrjybzj.ZnsbCjrjybzjDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="scjydz" column="scjydz" jdbcType="VARCHAR"/>
            <result property="zcdlxdh" column="zcdlxdh" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="snzzzggzze" column="snzzzggzze" jdbcType="DECIMAL"/>
            <result property="snzzzgrs" column="snzzzgrs" jdbcType="DECIMAL"/>
            <result property="yapcjrjybl" column="yapcjrjybl" jdbcType="DECIMAL"/>
            <result property="snsjapcjrjyrs" column="snsjapcjrjyrs" jdbcType="DECIMAL"/>
            <result property="snzzzgnpjgz" column="snzzzgnpjgz" jdbcType="DECIMAL"/>
            <result property="bqynse" column="bqynse" jdbcType="DECIMAL"/>
            <result property="bqjmse" column="bqjmse" jdbcType="DECIMAL"/>
            <result property="bqyjse1" column="bqyjse_1" jdbcType="DECIMAL"/>
            <result property="bqybtse" column="bqybtse" jdbcType="DECIMAL"/>
            <result property="sbrq" column="sbrq" jdbcType="TIMESTAMP"/>
            <result property="slswjgDm" column="slswjg_dm" jdbcType="CHAR"/>
            <result property="slrDm" column="slr_dm" jdbcType="CHAR"/>
            <result property="slrq" column="slrq" jdbcType="TIMESTAMP"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="jbr" column="jbr" jdbcType="VARCHAR"/>
            <result property="xzqhsf" column="xzqhsf" jdbcType="CHAR"/>
            <result property="xzqhds" column="xzqhds" jdbcType="CHAR"/>
            <result property="xzqhqx" column="xzqhqx" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="rdpzuuid" column="rdpzuuid" jdbcType="VARCHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        nsrsbh,nsrmc,djxh,
        scjydz,zcdlxdh,skssqq,
        skssqz,snzzzggzze,snzzzgrs,
        yapcjrjybl,snsjapcjrjyrs,snzzzgnpjgz,
        bqynse,bqjmse,bqyjse_1,
        bqybtse,sbrq,slswjg_dm,
        slr_dm,slrq,ssjmxz_dm,
        jbr,xzqhsf,xzqhds,
        xzqhqx,jdxz_dm,rdpzuuid,
        sjblbz,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
