<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.tdzzs.ZnsbNssbCxsTdzzssyxxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.tdzzs.ZnsbNssbCxsTdzzssyxxbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="tdzzssbblx" column="tdzzssbblx" jdbcType="CHAR"/>
            <result property="dzbzdszlDm" column="dzbzdszl_dm" jdbcType="VARCHAR"/>
            <result property="ysbbz" column="ysbbz" jdbcType="CHAR"/>
            <result property="xmbh" column="xmbh" jdbcType="VARCHAR"/>
            <result property="xmmc" column="xmmc" jdbcType="VARCHAR"/>
            <result property="xmdz" column="xmdz" jdbcType="VARCHAR"/>
            <result property="djzclxmc" column="djzclxmc" jdbcType="VARCHAR"/>
            <result property="hymc" column="hymc" jdbcType="VARCHAR"/>
            <result property="lxdh" column="lxdh" jdbcType="VARCHAR"/>
            <result property="swjgmc" column="swjgmc" jdbcType="VARCHAR"/>
            <result property="nsrdz" column="nsrdz" jdbcType="VARCHAR"/>
            <result property="yzbm" column="yzbm" jdbcType="CHAR"/>
            <result property="khyh" column="khyh" jdbcType="VARCHAR"/>
            <result property="yhzh" column="yhzh" jdbcType="VARCHAR"/>
            <result property="sfwhdzs" column="sfwhdzs" jdbcType="CHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="zksmj" column="zksmj" jdbcType="DECIMAL"/>
            <result property="zyhczmj" column="zyhczmj" jdbcType="DECIMAL"/>
            <result property="ysmj" column="ysmj" jdbcType="DECIMAL"/>
            <result property="qzptzzysmj" column="qzptzzysmj" jdbcType="DECIMAL"/>
            <result property="qzfptzzysmj" column="qzfptzzysmj" jdbcType="DECIMAL"/>
            <result property="qzqtlxfdcysmj" column="qzqtlxfdcysmj" jdbcType="DECIMAL"/>
            <result property="xmzksmj" column="xmzksmj" jdbcType="DECIMAL"/>
            <result property="qssysmj" column="qssysmj" jdbcType="DECIMAL"/>
            <result property="qshsyksmj" column="qshsyksmj" jdbcType="DECIMAL"/>
            <result property="sybzDm1" column="sybz_dm_1" jdbcType="CHAR"/>
            <result property="ssqq" column="ssqq" jdbcType="TIMESTAMP"/>
            <result property="bz1" column="bz_1" jdbcType="CHAR"/>
            <result property="bczt" column="bczt" jdbcType="CHAR"/>
            <result property="sbyy" column="sbyy" jdbcType="VARCHAR"/>
            <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="zfrDm" column="zfr_dm" jdbcType="VARCHAR"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,syuuid,djxh,
        nsrsbh,sbsx_dm_1,skssqq,
        skssqz,tdzzssbblx,dzbzdszl_dm,
        ysbbz,xmbh,xmmc,
        xmdz,djzclxmc,hymc,
        lxdh,swjgmc,nsrdz,
        yzbm,khyh,yhzh,
        sfwhdzs,zgswskfj_dm,jdxz_dm,
        zksmj,zyhczmj,ysmj,
        qzptzzysmj,qzfptzzysmj,qzqtlxfdcysmj,
        xmzksmj,qssysmj,qshsyksmj,
        sybz_dm_1,ssqq,bz_1,
        bczt,sbyy,pclsh,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
