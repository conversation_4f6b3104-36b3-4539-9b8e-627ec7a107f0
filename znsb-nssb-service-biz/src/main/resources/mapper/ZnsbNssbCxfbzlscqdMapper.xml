<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.gy.ZnsbNssbCxfbzlscqdMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.gy.flzl.ZnsbNssbCxfbzlscqdDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="bsfs" column="bsfs" jdbcType="CHAR"/>
            <result property="bsnsrlx" column="bsnsrlx" jdbcType="CHAR"/>
            <result property="dzbzdszlDm" column="dzbzdszl_dm" jdbcType="VARCHAR"/>
            <result property="flzlDm" column="flzl_dm" jdbcType="VARCHAR"/>
            <result property="flzlbslxDm" column="flzlbslx_dm" jdbcType="CHAR"/>
            <result property="flzlbslxmc" column="flzlbslxmc" jdbcType="VARCHAR"/>
            <result property="flzlbz" column="flzlbz" jdbcType="VARCHAR"/>
            <result property="flzlcllxDm" column="flzlcllx_dm" jdbcType="VARCHAR"/>
            <result property="flzlcllxmc" column="flzlcllxmc" jdbcType="VARCHAR"/>
            <result property="flzlmc" column="flzlmc" jdbcType="VARCHAR"/>
            <result property="flzlsycj" column="flzlsycj" jdbcType="VARCHAR"/>
            <result property="flzluuid" column="flzluuid" jdbcType="CHAR"/>
            <result property="lcswsxDm" column="lcswsx_dm" jdbcType="VARCHAR"/>
            <result property="rqbllx" column="rqbllx" jdbcType="VARCHAR"/>
            <result property="rqfw" column="rqfw" jdbcType="VARCHAR"/>
            <result property="secondloadtag" column="secondloadtag" jdbcType="VARCHAR"/>
            <result property="swjgDm" column="swjg_dm" jdbcType="CHAR"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="xh" column="xh" jdbcType="INTEGER"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="flzlxzmbdz" column="flzlxzmbdz" jdbcType="VARCHAR"/>
            <result property="fybqh" column="fybqh" jdbcType="VARCHAR"/>
            <result property="fybqmc" column="fybqmc" jdbcType="VARCHAR"/>
            <result property="sfmgzl" column="sfmgzl" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,bsfs,bsnsrlx,
        dzbzdszl_dm,flzl_dm,flzlbslx_dm,
        flzlbslxmc,flzlbz,flzlcllx_dm,
        flzlcllxmc,flzlmc,flzlsycj,
        flzluuid,lcswsx_dm,rqbllx,
        rqfw,secondloadtag,swjg_dm,
        swsx_dm,xh,yxbz,
        flzlxzmbdz,fybqh,fybqmc,
        sfmgzl,lrrq,xgrq,
        lrrsfid,xgrsfid,ywqd_dm,
        sjcsdq,sjtb_sj,sjgsdq
    </sql>
</mapper>
