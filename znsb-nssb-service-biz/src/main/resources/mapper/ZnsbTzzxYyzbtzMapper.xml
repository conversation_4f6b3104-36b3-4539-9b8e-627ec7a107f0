<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxYyzbtzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxYyzbtzDO">
            <id property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="zzid" column="zzid" jdbcType="VARCHAR"/>
            <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
            <result property="kmDm" column="km_dm" jdbcType="VARCHAR"/>
            <result property="kmmc" column="kmmc" jdbcType="VARCHAR"/>
            <result property="sqjyjf" column="sqjyjf" jdbcType="DECIMAL"/>
            <result property="sqjydf" column="sqjydf" jdbcType="DECIMAL"/>
            <result property="bqfsjf" column="bqfsjf" jdbcType="DECIMAL"/>
            <result property="bqfsdf" column="bqfsdf" jdbcType="DECIMAL"/>
            <result property="bqjyjf" column="bqjyjf" jdbcType="DECIMAL"/>
            <result property="bqjydf" column="bqjydf" jdbcType="DECIMAL"/>
            <result property="cqbz" column="cqbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="zbrq1" column="zbrq_sj" jdbcType="TIMESTAMP"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        djxh,uuid,sszq,
        zzid,syuuid,km_dm,
        kmmc,sqjyjf,sqjydf,
        bqfsjf,bqfsdf,bqjyjf,
        bqjydf,cqbz,ywqd_dm,
        lrrq,xgrq,scbz,
        scsj_1,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,zbrq_sj,sjtb_sj
    </sql>
</mapper>
