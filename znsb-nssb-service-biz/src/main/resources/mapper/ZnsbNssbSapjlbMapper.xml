<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sswszm.ZnsbNssbSapjlbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sswszm.ZnsbNssbSapjlbDO">
            <id property="lq_uuid" column="lq_uuid" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="sap_bw" column="sap_bw" jdbcType="VARCHAR"/>
            <result property="sap_code" column="sap_code" jdbcType="VARCHAR"/>
            <result property="sap_msg" column="sap_msg" jdbcType="VARCHAR"/>
            <result property="sap_pzbh" column="sap_pzbh" jdbcType="VARCHAR"/>
            <result property="cscs" column="cscs" jdbcType="BIGINT"/>
            <result property="xczxsj" column="xczxsj" jdbcType="TIMESTAMP"/>
            <result property="cgbz" column="cgbz" jdbcType="CHAR"/>
            <result property="ywqd_dm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtb_sj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        lq_uuid,nsrsbh,sap_bw,
        sap_code,sap_msg,sap_pzbh,
        cscs,xczxsj,cgbz,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
