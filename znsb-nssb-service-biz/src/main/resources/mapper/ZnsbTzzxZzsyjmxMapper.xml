<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxZzsyjmxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.zzsyj.ZnsbTzzxZzsyjmxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
            <result property="fwcqzsh" column="fwcqzsh" jdbcType="VARCHAR"/>
            <result property="zgswjgDm" column="zgswjg_dm" jdbcType="CHAR"/>
            <result property="fwzldz" column="fwzldz" jdbcType="VARCHAR"/>
            <result property="yjlx" column="yjlx" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="hsje" column="hsje" jdbcType="DECIMAL"/>
            <result property="ly" column="ly" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sbje" column="sbje" jdbcType="DECIMAL"/>
            <result property="nsrsbztDm" column="nsrsbzt_dm" jdbcType="VARCHAR"/>
            <result property="htbh" column="htbh" jdbcType="VARCHAR"/>
            <result property="htmc" column="htmc" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,nsrsbh,
        nsrmc,fybh,fwcqzsh,
        zgswjg_dm,fwzldz,yjlx,
        skssqq,skssqz,hsje,
        ly,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,
        sbje,nsrsbztDm,htbh,htmc
    </sql>
</mapper>
