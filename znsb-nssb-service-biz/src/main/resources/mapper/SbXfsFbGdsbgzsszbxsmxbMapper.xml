<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbGdsbgzsszbxsmxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbGdsbgzsszbxsmxbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="bqxssl" column="bqxssl" jdbcType="DECIMAL"/>
            <result property="sntqxssl" column="sntqxssl" jdbcType="BIGINT"/>
            <result property="bqxsje" column="bqxsje" jdbcType="DECIMAL"/>
            <result property="sntqxsje" column="sntqxsje" jdbcType="DECIMAL"/>
            <result property="bdqckcsl" column="bdqckcsl" jdbcType="DECIMAL"/>
            <result property="bdbqxssl" column="bdbqxssl" jdbcType="DECIMAL"/>
            <result property="bdqmkcsl" column="bdqmkcsl" jdbcType="DECIMAL"/>
            <result property="bdqckcje" column="bdqckcje" jdbcType="DECIMAL"/>
            <result property="bdbqxsje" column="bdbqxsje" jdbcType="DECIMAL"/>
            <result property="bdqmkcje" column="bdqmkcje" jdbcType="DECIMAL"/>
            <result property="snCpxl" column="sn_cpxl" jdbcType="DECIMAL"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        ewbhxh,bqxssl,sntqxssl,
        bqxsje,sntqxsje,bdqckcsl,
        bdbqxssl,bdqmkcsl,bdqckcje,
        bdbqxsje,bdqmkcje,sn_cpxl,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
