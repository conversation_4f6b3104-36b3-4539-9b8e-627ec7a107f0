<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfssb.SbXfsFbSnyrlyydtjbKchyqkMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.xfssb.SbXfsFbSnyrlyydtjbKchyqkDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="ewblxh" column="ewblxh" jdbcType="BIGINT"/>
            <result property="lmc" column="lmc" jdbcType="VARCHAR"/>
            <result property="qckcypsl" column="qckcypsl" jdbcType="DECIMAL"/>
            <result property="bqscypsl" column="bqscypsl" jdbcType="DECIMAL"/>
            <result property="bqxshsypsl" column="bqxshsypsl" jdbcType="DECIMAL"/>
            <result property="bqzxddzgjhxsypsl" column="bqzxddzgjhxsypsl" jdbcType="DECIMAL"/>
            <result property="bqhzfwbzzszyfpypsl" column="bqhzfwbzzszyfpypsl" jdbcType="DECIMAL"/>
            <result property="kjptbzzszyfpypsl" column="kjptbzzszyfpypsl" jdbcType="DECIMAL"/>
            <result property="scyxftlcphyypsl" column="scyxftlcphyypsl" jdbcType="DECIMAL"/>
            <result property="scyxlcphyypsl" column="scyxlcphyypsl" jdbcType="DECIMAL"/>
            <result property="scftlcphyypsl" column="scftlcphyypsl" jdbcType="DECIMAL"/>
            <result property="scfyxftlcphyypsl" column="scfyxftlcphyypsl" jdbcType="DECIMAL"/>
            <result property="qmkcsl" column="qmkcsl" jdbcType="DECIMAL"/>
            <result property="wgqckcypsl" column="wgqckcypsl" jdbcType="DECIMAL"/>
            <result property="qckcmsypsl" column="qckcmsypsl" jdbcType="DECIMAL"/>
            <result property="qckchsypsl" column="qckchsypsl" jdbcType="DECIMAL"/>
            <result property="dqwgypsl" column="dqwgypsl" jdbcType="DECIMAL"/>
            <result property="dqwgmsypsl" column="dqwgmsypsl" jdbcType="DECIMAL"/>
            <result property="dqwghsypsl" column="dqwghsypsl" jdbcType="DECIMAL"/>
            <result property="bqxsypsl" column="bqxsypsl" jdbcType="DECIMAL"/>
            <result property="bqxsmsypsl" column="bqxsmsypsl" jdbcType="DECIMAL"/>
            <result property="wgbqxshsypsl" column="wgbqxshsypsl" jdbcType="DECIMAL"/>
            <result property="wgscfyxftlcphyypsl" column="wgscfyxftlcphyypsl" jdbcType="DECIMAL"/>
            <result property="scmsypsl" column="scmsypsl" jdbcType="DECIMAL"/>
            <result property="schsypsl" column="schsypsl" jdbcType="DECIMAL"/>
            <result property="kyyscyxftypsl" column="kyyscyxftypsl" jdbcType="DECIMAL"/>
            <result property="kyyscmsypsl" column="kyyscmsypsl" jdbcType="DECIMAL"/>
            <result property="kyyschsypsl" column="kyyschsypsl" jdbcType="DECIMAL"/>
            <result property="sjscyxlcphyypsl" column="sjscyxlcphyypsl" jdbcType="DECIMAL"/>
            <result property="sjscyxmsypsl" column="sjscyxmsypsl" jdbcType="DECIMAL"/>
            <result property="sjscyxhsypsl" column="sjscyxhsypsl" jdbcType="DECIMAL"/>
            <result property="sjscftlcphyypsl" column="sjscftlcphyypsl" jdbcType="DECIMAL"/>
            <result property="sjscftmsypsl" column="sjscftmsypsl" jdbcType="DECIMAL"/>
            <result property="sjschsypsl" column="sjschsypsl" jdbcType="DECIMAL"/>
            <result property="sqtshsypsl" column="sqtshsypsl" jdbcType="DECIMAL"/>
            <result property="qmkcypsl" column="qmkcypsl" jdbcType="DECIMAL"/>
            <result property="qmkcmsypsl" column="qmkcmsypsl" jdbcType="DECIMAL"/>
            <result property="qmkchsypsl" column="qmkchsypsl" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbuuid,pzxh,
        ewblxh,lmc,qckcypsl,
        bqscypsl,bqxshsypsl,bqzxddzgjhxsypsl,
        bqhzfwbzzszyfpypsl,kjptbzzszyfpypsl,scyxftlcphyypsl,
        scyxlcphyypsl,scftlcphyypsl,scfyxftlcphyypsl,
        qmkcsl,wgqckcypsl,qckcmsypsl,
        qckchsypsl,dqwgypsl,dqwgmsypsl,
        dqwghsypsl,bqxsypsl,bqxsmsypsl,
        wgbqxshsypsl,wgscfyxftlcphyypsl,scmsypsl,
        schsypsl,kyyscyxftypsl,kyyscmsypsl,
        kyyschsypsl,sjscyxlcphyypsl,sjscyxmsypsl,
        sjscyxhsypsl,sjscftlcphyypsl,sjscftmsypsl,
        sjschsypsl,sqtshsypsl,qmkcypsl,
        qmkcmsypsl,qmkchsypsl,sjblbz,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
