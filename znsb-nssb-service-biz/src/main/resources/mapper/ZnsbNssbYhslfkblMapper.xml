<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhslfkblMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.yhs.ZnsbNssbYhslfkblDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="zbuuid" column="zbuuid" jdbcType="VARCHAR"/>
        <result property="zfrDm" column="zfr_dm" jdbcType="CHAR"/>
        <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
        <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
        <result property="sjjsrq" column="sjjsrq" jdbcType="TIMESTAMP"/>
        <result property="sjjsje" column="sjjsje" jdbcType="DECIMAL"/>
        <result property="dfslrmc" column="dfslrmc" jdbcType="VARCHAR"/>
        <result property="dfslrsjje" column="dfslrsjje" jdbcType="DECIMAL"/>
        <result property="dfslrnsrsbh" column="dfslrnsrsbh" jdbcType="VARCHAR"/>
        <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        zbuuid
        ,uuid,zfr_dm,
        zfrq_1,zfbz_1,sjjsrq,
        sjjsje,dfslrmc,dfslrsjje,
        dfslrnsrsbh,syuuid,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>

    <update id="zfkbl">
        update znsb_nssb_yhslfkbl
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="zfbz_1 =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.zfbz1!=null">
                        when zbuuid=#{i.zbuuid} then #{i.zfbz1}
                    </if>
                </foreach>
            </trim>
            <trim prefix="zfr_dm =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.zfr_dm!=null">
                        when zbuuid=#{i.zbuuid} then #{i.zfr_dm}
                    </if>
                </foreach>
            </trim>
            <trim prefix="zfrq_1 =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.zfrq1!=null">
                        when zbuuid=#{i.zbuuid} then #{i.zfrq1}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="i" index="index">
            zbuuid=#{i.zbuuid}
        </foreach>
    </update>

    <delete id="deletekblByzbuuid">
        delete from znsb_nssb_yhslfkbl
        where zbuuid in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.zbuuid}
        </foreach>
    </delete>

    <delete id="deletekByZbuuid">
        delete from znsb_nssb_yhslfkbl
        where zbuuid in
        <foreach collection="list" item="zbuuid" index="index" open="(" separator="," close=")">
            #{zbuuid}
        </foreach>
    </delete>
    <update id="updateBySyuuid">
        update
            znsb_nssb_yhslfkbl
        set zfbz_1 ='Y',
            zfrq_1 = NOW(),
            xgrsfid='qyd',
            xgrq=NOW()
        where syuuid =
              #{syuuid};
    </update>

    <update id="updateByZbuuid">
        update
            znsb_nssb_yhslfkbl
        set zfbz_1 ='Y',
            zfrq_1 = NOW(),
            xgrsfid='qyd',
            xgrq=NOW()
        where zbuuid =
              #{zbuuid};
    </update>

    <select id="queryycjxxkblbyzbuuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from znsb_nssb_yhslfkbl
        where zbuuid = #{zbuuid} and COALESCE(zfbz_1, 'N') = 'N'
    </select>
</mapper>
