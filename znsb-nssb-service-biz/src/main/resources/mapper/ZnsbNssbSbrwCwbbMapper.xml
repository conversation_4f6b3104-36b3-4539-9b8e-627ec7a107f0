<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwCwbbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwCwbbDO">
            <id property="sbrwuuid" column="sbrwuuid" jdbcType="VARCHAR"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="yzpzzlmc" column="yzpzzlmc" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="nsqxDm" column="nsqx_dm" jdbcType="CHAR"/>
            <result property="sbqx" column="sbqx" jdbcType="TIMESTAMP"/>
            <result property="bsy" column="bsy" jdbcType="VARCHAR"/>
            <result property="rwztDm" column="rwzt_dm" jdbcType="VARCHAR"/>
            <result property="nsrsbztDm" column="nsrsbzt_dm" jdbcType="VARCHAR"/>
            <result property="qydmz" column="qydmz" jdbcType="VARCHAR"/>
            <result property="jguuid1" column="jguuid_1" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="cwkjzdbauuid" column="cwkjzdbauuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="sbny" column="sbny" jdbcType="CHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="zlxlDm" column="zlxl_dm" jdbcType="CHAR"/>
            <result property="sbyysm" column="sbyysm" jdbcType="VARCHAR"/>
            <result property="gslxDm" column="gslx_dm" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        sbrwuuid,yzpzzl_dm,yzpzzlmc,
        skssqq,skssqz,nsqx_dm,
        sbqx,bsy,rwzt_dm,
        nsrsbzt_dm,qydmz,jguuid_1,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,cwkjzdbauuid,
        djxh,nsrsbh,nsrmc,
        sbny,xzqhsz_dm,zlxl_dm,sbyysm,gslx_dm
    </sql>
</mapper>
