<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.css.znsb.nssb.mapper.ssyc.ZnsbNssbSsycQysdsMapper">
    <select id="queryQysdsHj" resultType="com.css.znsb.nssb.pojo.domain.ssyc.ZnsbNssbSsycQysdsDO">
        SELECT
        COALESCE(SUM(yyjse), 0) AS yyjse,
        COALESCE(SUM(djje_1), 0) AS djje_1,
        COALESCE(SUM(lrzeswlrgj), 0) AS lrzeswlrgj,
        COALESCE(SUM(lrtzje), 0) AS lrtzje,
        COALESCE(SUM(qysdsse), 0) AS qysdsse,
        COALESCE(SUM(lrze), 0) AS lrze,
        COALESCE(SUM(yjxsfl), 0) AS yjxsfl,
        COALESCE(SUM(yysrqhth), 0) AS yysrqhth,
        COALESCE(SUM(yycbqhth), 0) AS yycbqhth,
        COALESCE(SUM(yycbzxdchdjzb), 0) AS yycbzxdchdjzb,
        COALESCE(SUM(gyzzbdsy), 0) AS gyzzbdsy,
        COALESCE(SUM(xyjzss), 0) AS xyjzss,
        COALESCE(SUM(zcjzss), 0) AS zcjzss,
        COALESCE(SUM(hyjf), 0) AS hyjf,
        COALESCE(SUM(yfjjkc), 0) AS yfjjkc,
        COALESCE(SUM(qtse), 0) AS qtse
        FROM znsb_nssb_ssyc_qysds
        <where>
            ssq = #{ssq}
            <if test="zzuuidList != null and zzuuidList.size() > 0">
                AND zzuuid IN
                <foreach item="id" collection="zzuuidList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="qydmzList != null and qydmzList.size() > 0">
                AND qydmz IN
                <foreach item="code" collection="qydmzList" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>
</mapper>