<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.skjn.ZnsbNssbYyjkjlMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.skjn.ZnsbNssbYyjkjlDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="yyrq" column="yyrq" jdbcType="TIMESTAMP"/>
            <result property="yysjd" column="yysjd" jdbcType="VARCHAR"/>
            <result property="yyzt" column="yyzt" jdbcType="VARCHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="jkrq" column="jkrq_1" jdbcType="TIMESTAMP"/>
            <result property="sfxyh" column="sfxyh" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,nsrsbh,
        yyrq,yysjd,yyzt,
        yxbz,jkrq_1,sfxyh,ywqd_dm,
        lrrq,xgrq,sjcsdq,
        sjgsdq,xgrsfid,lrrsfid,
        sjtb_sj
    </sql>
</mapper>
