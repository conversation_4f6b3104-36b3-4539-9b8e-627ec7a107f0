<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxstysbzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxstysbzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="cxstysbuuid" column="cxstysbuuid" jdbcType="VARCHAR"/>
            <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
            <result property="bczt" column="bczt" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="zfrq1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="zfbz1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zxbztzsuuid" column="zxbztzsuuid" jdbcType="VARCHAR"/>
            <result property="xgmjzzczzsj" column="xgmjzzczzsj" jdbcType="TIMESTAMP"/>
            <result property="xgmjzzcqssj" column="xgmjzzcqssj" jdbcType="TIMESTAMP"/>
            <result property="bqsfsyzzsxgmnsrjzzc" column="bqsfsyzzsxgmnsrjzzc" jdbcType="CHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="sbsxDm1" column="sbsx_dm_1" jdbcType="CHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="jzzcsyztDm" column="jzzcsyzt_dm" jdbcType="VARCHAR"/>
            <result property="sbrq1" column="sbrq_1" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,cxstysbuuid,pclsh,
        bczt,jdxz_dm,xzqhsz_dm,
        hy_dm,zgswskfj_dm,zfrq_1,
        zfbz_1,zxbztzsuuid,xgmjzzczzsj,
        xgmjzzcqssj,bqsfsyzzsxgmnsrjzzc,skssqq,
        skssqz,sbsx_dm_1,djxh,
        nsrsbh,nsrmc,jzzcsyzt_dm,
        sbrq_1,xgrq,xgrsfid,
        lrrq,lrrsfid,sjgsdq,
        sjcsdq,ywqd_dm,sjtb_sj
    </sql>
</mapper>
