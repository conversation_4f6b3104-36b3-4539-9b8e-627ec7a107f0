<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbFtdcjxxbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbNssbFtdcjxxb">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="tdsybh" column="tdsybh" jdbcType="VARCHAR"/>
            <result property="bdcqzlxDm" column="bdcqzlx_dm" jdbcType="CHAR"/>
            <result property="bdcdyh" column="bdcdyh" jdbcType="VARCHAR"/>
            <result property="tdsyzbh" column="tdsyzbh" jdbcType="VARCHAR"/>
            <result property="dh1" column="dh_1" jdbcType="VARCHAR"/>
            <result property="tdxzDm" column="tdxz_dm" jdbcType="CHAR"/>
            <result property="tdqdfsDm" column="tdqdfs_dm" jdbcType="CHAR"/>
            <result property="zytdmj1" column="zytdmj1" jdbcType="DECIMAL"/>
            <result property="tddj" column="tddj" jdbcType="DECIMAL"/>
            <result property="tdqdsj" column="tdqdsj" jdbcType="TIMESTAMP"/>
            <result property="tdzldz" column="tdzldz" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="tyshxydm" column="tyshxydm" jdbcType="VARCHAR"/>
            <result property="sjscrq" column="sjscrq" jdbcType="TIMESTAMP"/>
            <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
            <result property="fwcqzsh" column="fwcqzsh" jdbcType="VARCHAR"/>
            <result property="jzmj" column="jzmj" jdbcType="DECIMAL"/>
            <result property="fwzldz" column="fwzldz" jdbcType="VARCHAR"/>
            <result property="flbz" column="flbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="bczt" column="bczt" jdbcType="CHAR"/>
            <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="csqdsj" column="csqdsj" jdbcType="TIMESTAMP"/>
            <result property="sfzjhm" column="sfzjhm" jdbcType="VARCHAR"/>
            <result property="fcyz" column="fcyz" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,tdsybh,bdcqzlx_dm,
        bdcdyh,tdsyzbh,dh_1,
        tdxz_dm,tdqdfs_dm,zytdmj1,
        tddj,tdqdsj,tdzldz,
        xzqhsz_dm,jdxz_dm,tyshxydm,
        sjscrq,fybh,fwcqzsh,
        jzmj,fwzldz,flbz,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,djxh,
        bczt,pclsh,zgswskfj_dm,
        csqdsj,sfzjhm,fcyz
    </sql>
</mapper>
