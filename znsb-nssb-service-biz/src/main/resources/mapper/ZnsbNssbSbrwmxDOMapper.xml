<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwmxDOMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwmxDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sbrwuuid" column="sbrwuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
            <result property="yzpzzlmc" column="yzpzzlmc" jdbcType="VARCHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="zsxmmc" column="zsxmmc" jdbcType="VARCHAR"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="sbny" column="sbny" jdbcType="CHAR"/>
            <result property="ybtse" column="ybtse" jdbcType="DECIMAL"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sbrwuuid,djxh,
        yzpzzl_dm,yzpzzlmc,zsxm_dm,
        zsxmmc,skssqq,skssqz,
        sbny,ybtse,yxbz,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj
    </sql>
</mapper>
