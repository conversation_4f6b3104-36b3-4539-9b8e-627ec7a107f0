<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.xfs.XfsSsjgMapper">


    <select id="listWkpsrSsgc" parameterType="com.css.znsb.nssb.pojo.dto.xfs.lqfzpt.ssgc.XfsSsgcCxReqVO"
            resultType="com.css.znsb.nssb.pojo.dto.xfs.lqfzpt.ssgc.XfsSsgcmxVO" databaseId="oracle">
        SELECT
            ssgcuuid as ssgcuuid,
            ssgcwdzt1_dm as jsxzt1_dm,
            ssgcwdzt2_dm as jsxzt2_dm,
            spbmdl as spbmdl,
            spbm as spbm,
            sspzfl_dm as sspzfl_dm,
            zspm_dm as zspm_dm,
            jsff_dm_1 as jsff_dm_1,
            dw as jldw_dm,
            fphm as fphm,
            sl_1 as sl_1,
            sply_dm as sply_dm,
            '' as kcfs_dm,
            'BDA06111111' as yzpzzl_dm,
            spyt_dm as spyt_dm,
            spmc as spmc,
            '' as kclb,
            ssyspzlx_dm as ssyspzlx_dm,
            sspzhm as sspzhm,
            ssjmxz_dm as ssjmxz_dm,
            swsx_dm as swsx_dm,
            jmsxm_dm as jmsxm_dm,
            ssmsbz_dm as ssmsbz_dm,
            '' as skzl_dm,
            '' as cbsx_dm,
            '' as jmfs_dm,
            null as jkqx,
            null as yjkqx,
            '' as wkjfpdx_dm,
            ywfsrq as ywfsrq,
            skssqq as skssqq,
            skssqz as skssqz,
            ssywsq as ssywsq,
            ssjmxz_dm as ssjmxz_dm,
            swsx_dm as swsx_dm,
            zzsyjlx_dm as zsyjlx_dm,
            jxsezclx_dm as jxsezclx_dm,
            nsjctzlx_dm as nsjctzlx_dm,
            gjbdcbz as gjbdcbz,
            jrspzrbz as jrspzrbz,
            ldsezt_dm as ldsezt_dm,
            mtszc as mtszc_dm,
            hxyt_dm as gxdklx_dm,
            ttsjlx_dm as ttsjlx_dm,
            azzsjjdjkm_dm as azzsjjdjkmlx_dm,
            yqkszzszyfpbz as yqkszzszyfpbz,
            jsff_dm_1 as jsff_dm,
            tdyslx_dm as tdyslx_dm,
            fphm as fphm,
            spmc as wkjfpspmc,
            wkjfpdx as wkjfpdx_dm,
            wkjfpywlx as wkjfpywlx_dm,
            ywfsrq as ywfsrq,
            ssgcje as ssgcje,
            ssgcsl as ssgcsl,
            lrrq as lrrq,
            bz as bz,
            fpztbz_dm as fpztbz_dm,
            ywxwfl_dm as ywxwfl_dm
        FROM ss_ssgc_xfs
        WHERE djxh = #{djxh}
          AND skssqq &gt;= #{skssqq}
          AND skssqz &lt;= #{skssqz}
          AND ywxwfl_dm = 'XW002'
    </select>
</mapper>
