<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.fssrtysb.ZnsbTzzxFssrtysbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.fssrtysb.ZnsbTzzxFssrtysbDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
        <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
        <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
        <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
        <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
        <result property="zsxmmc" column="zsxmmc" jdbcType="CHAR"/>
        <result property="zspmDm" column="zspm_dm" jdbcType="CHAR"/>
        <result property="zspmmc" column="zspmmc" jdbcType="CHAR"/>
        <result property="zszmDm" column="zszm_dm" jdbcType="CHAR"/>
        <result property="zszmmc" column="zszmmc" jdbcType="CHAR"/>
        <result property="jsyj" column="jsyj" jdbcType="DECIMAL"/>
        <result property="sbqxDm" column="sbqx_dm" jdbcType="CHAR"/>
        <result property="nsqxDm" column="nsqx_dm" jdbcType="CHAR"/>
        <result property="ysbbz" column="ysbbz" jdbcType="CHAR"/>
        <result property="sbuuid" column="sbuuid" jdbcType="CHAR"/>
        <result property="pzxh" column="pzxh" jdbcType="CHAR"/>
        <result property="sbrwuuid" column="sbrwuuid" jdbcType="CHAR"/>
        <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
        <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
        <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,djxh,nsrsbh,
        nsrmc,yzpzzl_dm,skssqq,
        skssqz,zsxm_dm,zsxmmc,zspm_dm,zspmmc,
        zszm_dm,zszmmc,jsyj,sbqx_dm,
        nsqx_dm,ysbbz,yxbz,
        zgswskfj_dm,xzqhsz_dm,ywqd_dm,
        lrrq,lrrsfid,xgrq,
        xgrsfid,sjtb_sj,sjcsdq,
        sjgsdq,sbuuid,pzxh,sbrwuuid
    </sql>
</mapper>
