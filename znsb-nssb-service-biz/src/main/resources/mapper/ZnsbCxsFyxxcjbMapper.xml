<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjb">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="fyxxuuid" column="fyxxuuid" jdbcType="VARCHAR"/>
            <result property="fybh" column="fybh" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="tbrq1" column="tbrq_1" jdbcType="TIMESTAMP"/>
            <result property="fwcqzsh" column="fwcqzsh" jdbcType="VARCHAR"/>
            <result property="fcmc" column="fcmc" jdbcType="VARCHAR"/>
            <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
            <result property="jdxzDm" column="jdxz_dm" jdbcType="CHAR"/>
            <result property="fwzldz" column="fwzldz" jdbcType="VARCHAR"/>
            <result property="hyDm" column="hy_dm" jdbcType="VARCHAR"/>
            <result property="dwlsgxDm" column="dwlsgx_dm" jdbcType="CHAR"/>
            <result property="zgswskfjDm" column="zgswskfj_dm" jdbcType="CHAR"/>
            <result property="nsrlx" column="nsrlx" jdbcType="CHAR"/>
            <result property="qsrnsrsbh" column="qsrnsrsbh" jdbcType="VARCHAR"/>
            <result property="qsrnsrmc" column="qsrnsrmc" jdbcType="VARCHAR"/>
            <result property="tdsybh" column="tdsybh" jdbcType="VARCHAR"/>
            <result property="fcytDm" column="fcyt_dm" jdbcType="CHAR"/>
            <result property="jzmj" column="jzmj" jdbcType="DECIMAL"/>
            <result property="csqdsj" column="csqdsj" jdbcType="TIMESTAMP"/>
            <result property="nsywzzsj" column="nsywzzsj" jdbcType="TIMESTAMP"/>
            <result property="dlr" column="dlr" jdbcType="VARCHAR"/>
            <result property="dlrsfzjhm1" column="dlrsfzjhm1" jdbcType="VARCHAR"/>
            <result property="slrq" column="slrq" jdbcType="TIMESTAMP"/>
            <result property="slswjgDm" column="slswjg_dm" jdbcType="CHAR"/>
            <result property="yxqq" column="yxqq" jdbcType="TIMESTAMP"/>
            <result property="yxqz" column="yxqz" jdbcType="TIMESTAMP"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="lrrDm" column="lrr_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrDm" column="xgr_dm" jdbcType="CHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="slr" column="slr" jdbcType="VARCHAR"/>
            <result property="tbr" column="tbr" jdbcType="VARCHAR"/>
            <result property="fcsnsrlx" column="fcsnsrlx" jdbcType="CHAR"/>
            <result property="czrsj" column="czrsj" jdbcType="TIMESTAMP"/>
            <result property="cjpkbz" column="cjpkbz" jdbcType="CHAR"/>
            <result property="czpkbz" column="czpkbz" jdbcType="CHAR"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="dlrsfzjzlDm1" column="dlrsfzjzl_dm_1" jdbcType="CHAR"/>
            <result property="bdcdyh" column="bdcdyh" jdbcType="VARCHAR"/>
            <result property="dljgqz" column="dljgqz" jdbcType="VARCHAR"/>
            <result property="jbrsfzjzlDm" column="jbrsfzjzl_dm" jdbcType="VARCHAR"/>
            <result property="sfyybdcdydm" column="sfyybdcdydm" jdbcType="CHAR"/>
            <result property="bdcqzlx" column="bdcqzlx" jdbcType="CHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
            <result property="bczt" column="bczt" jdbcType="CHAR"/>
            <result property="cssm" column="cssm" jdbcType="VARCHAR"/>
            <result property="ycyy" column="ycyy" jdbcType="VARCHAR"/>
            <result property="bgzt" column="bgzt" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,fyxxuuid,fybh,
        djxh,tbrq_1,fwcqzsh,
        fcmc,xzqhsz_dm,jdxz_dm,
        fwzldz,hy_dm,dwlsgx_dm,
        zgswskfj_dm,nsrlx,qsrnsrsbh,
        qsrnsrmc,tdsybh,fcyt_dm,
        jzmj,csqdsj,nsywzzsj,
        dlr,dlrsfzjhm1,slrq,
        slswjg_dm,yxqq,yxqz,
        yxbz,lrr_dm,lrrq,
        xgr_dm,xgrq,sjgsdq,
        sjtb_sj,slr,tbr,
        fcsnsrlx,czrsj,cjpkbz,
        czpkbz,sjblbz,dlrsfzjzl_dm_1,
        bdcdyh,dljgqz,jbrsfzjzl_dm,
        sfyybdcdydm,bdcqzlx,lrrsfid,
        xgrsfid,ywqd_dm,sjcsdq,
        pclsh,bczt,cssm,
        ycyy,bgzt
    </sql>
</mapper>
