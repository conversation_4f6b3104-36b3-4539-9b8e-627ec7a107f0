<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.tzzx.mapper.ZnsbTzzxJmszzMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxJmszzDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="gsh" column="gsh" jdbcType="VARCHAR"/>
            <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="jmzlxDm" column="jmzlx_dm" jdbcType="VARCHAR"/>
            <result property="ssjmxzDm" column="ssjmxz_dm" jdbcType="VARCHAR"/>
            <result property="swsxDm" column="swsx_dm" jdbcType="CHAR"/>
            <result property="bqfse" column="bqfse" jdbcType="DECIMAL"/>
            <result property="mzzzsxmxse" column="mzzzsxmxse" jdbcType="DECIMAL"/>
            <result property="mzxsekcxmbqsjkcje" column="mzxsekcxmbqsjkcje" jdbcType="DECIMAL"/>
            <result property="tzlxDm" column="tzlx_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="scbz" column="scbz" jdbcType="CHAR"/>
            <result property="scsj1" column="scsj_1" jdbcType="TIMESTAMP"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,sszq,djxh,
        nsrsbh,nsrmc,gsh,
        lrzx,sl_1,jmzlx_dm,
        ssjmxz_dm,swsx_dm,
        bqfse,mzzzsxmxse,
        mzxsekcxmbqsjkcje,tzlx_dm,lrrq,
        lrrsfid,xgrq,xgrsfid,
        scbz,scsj_1,ywqd_dm,
        sjcsdq,sjtb_sj,sjgsdq
    </sql>

    <select id="selectInit" resultType="com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxJmszzDO">
        SELECT REPLACE(UUID(), '-', '') uuid, djxh,nsrsbh, gsh,sszq,
        '--' lrzx,jmzlx_dm,
        ssjmxz_dm,swsx_dm,
        sum(mzzzsxmxse) mzzzsxmxse,
        sum(mzxsekcxmbqsjkcje) mzxsekcxmbqsjkcje, sum(bqfse) bqfse from znsb_tzzx_jmszz
        where ssjmxz_dm = '0001129914'
        <if test = "params.djxh != null and params.djxh != ''" >
            and djxh = #{params.djxh}
        </if>
        <if test = "params.uuidList != null and params.uuidList.size() > 0" >
            and uuid in #{params.uuidList}
        </if>
        <if test = "params.ssyfq != null and params.ssyfq != ''" >
            AND sszq &gt;= #{params.ssyfq}
        </if>
        <if test = "params.ssyfz != null and params.ssyfz != ''" >
            AND sszq &lt;= #{params.ssyfz}
        </if>
        AND scbz = '0'
        AND tzlx_dm in ('1', '4')
        GROUP BY djxh,nsrsbh, lrzx, gsh, sszq, jmzlx_dm, ssjmxz_dm, swsx_dm
        UNION
        (SELECT uuid, djxh,nsrsbh,gsh,sszq,
        lrzx,jmzlx_dm,
        ssjmxz_dm,swsx_dm,
        mzzzsxmxse,
        mzxsekcxmbqsjkcje, bqfse from znsb_tzzx_jmszz
        where ssjmxz_dm != '0001129914'
        <if test = "params.djxh != null and params.djxh != ''" >
            and djxh = #{params.djxh}
        </if>
        <if test = "params.uuidList != null and params.uuidList.size() > 0" >
            and uuid in #{params.uuidList}
        </if>
        <if test = "params.ssyfq != null and params.ssyfq != ''" >
            AND sszq &gt;= #{params.ssyfq}
        </if>
        <if test = "params.ssyfz != null and params.ssyfz != ''" >
            AND sszq &lt;= #{params.ssyfz}
        </if>
        AND scbz = '0'
        AND tzlx_dm in ('1', '4')
        ORDER BY LRRQ DESC)
    </select>
</mapper>
