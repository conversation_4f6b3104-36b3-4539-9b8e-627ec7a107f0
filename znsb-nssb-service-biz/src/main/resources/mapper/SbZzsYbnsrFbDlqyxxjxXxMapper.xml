<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDlqyxxjxXxMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbDlqyxxjxXxSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="ewbhxh" column="ewbhxh" jdbcType="BIGINT"/>
            <result property="xsdl" column="xsdl" jdbcType="DECIMAL"/>
            <result property="dj2" column="dj_2" jdbcType="DECIMAL"/>
            <result property="sdsr" column="sdsr" jdbcType="DECIMAL"/>
            <result property="bzsdjwfy" column="bzsdjwfy" jdbcType="DECIMAL"/>
            <result property="ysjwfy" column="ysjwfy" jdbcType="DECIMAL"/>
            <result property="deslhyzl" column="deslhyzl" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        ewbhxh,xsdl,dj_2,
        sdsr,bzsdjwfy,ysjwfy,
        deslhyzl,sjblbz,lrrq,
        xgrq,lrrsfid,xgrsfid,
        ywqd_dm,sjcsdq,sjtb_sj,
        sjgsdq
    </sql>
</mapper>
