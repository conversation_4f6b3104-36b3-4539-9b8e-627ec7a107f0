<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxsshgjsyxxjlbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.cchxwssb.ZnsbNssbCxsshgjsyxxjlbDO">
        <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="syuuid" column="syuuid" jdbcType="VARCHAR"/>
        <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
        <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
        <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
        <result property="yzpzzlDm" column="yzpzzl_dm" jdbcType="CHAR"/>
        <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
        <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
        <result property="gjbz" column="gjbz" jdbcType="CHAR"/>
        <result property="zxcs" column="zxcs" jdbcType="DECIMAL"/>
        <result property="sczxsj" column="sczxsj" jdbcType="TIMESTAMP"/>
        <result property="qqbw" column="qqbw" jdbcType="VARCHAR"/>
        <result property="xybw" column="xybw" jdbcType="VARCHAR"/>
        <result property="bw" column="bw" jdbcType="VARCHAR"/>
        <result property="cgbz" column="cgbz" jdbcType="CHAR"/>
        <result property="ycxx" column="ycxx" jdbcType="VARCHAR"/>
        <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
        <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
        <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
        <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
        <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
        <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
        <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
        <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="xzqhszDm" column="xzqhsz_dm" jdbcType="CHAR"/>
        <result property="pclsh" column="pclsh" jdbcType="VARCHAR"/>
        <result property="sjjhlxDm" column="sjjhlx_Dm" jdbcType="VARCHAR"/>
        <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,syuuid,djxh,
        skssqq,skssqz,yzpzzl_dm,
        zsxm_dm,pzxh,gjbz,
        zxcs,sczxsj,qqbw,
        xybw,bw,cgbz,
        ycxx,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj,
        nsrsbh,xzqhsz_dm,pclsh,sjjhlx_Dm,nsrmc
    </sql>
</mapper>
