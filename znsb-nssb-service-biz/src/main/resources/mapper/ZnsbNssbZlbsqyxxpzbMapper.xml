<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zlbs.ZnsbNssbZlbsqyxxpzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.domain.zlbs.ZnsbNssbZlbsqyxxpzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="gsh1" column="gsh_1" jdbcType="VARCHAR"/>
            <result property="bsbbbm" column="bsbbbm" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="zjgdjxh" column="zjgdjxh" jdbcType="DECIMAL"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,nsrsbh,
        nsrmc,gsh_1,bsbbbm,
        sl_1,zjgdjxh,yxbz
    </sql>
</mapper>
