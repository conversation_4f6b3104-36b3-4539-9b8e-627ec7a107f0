<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.gjss.mapper.zzs.qtkjpz.jyk.ZnsbTzzxQtkspzzzZjbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.gjss.pojo.domain.zzs.qtkspz.ZnsbTzzxQtkspzzzZjbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="jxzzuuid" column="jxzzuuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="nsrmc" column="nsrmc" jdbcType="VARCHAR"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="skssqq" column="skssqq" jdbcType="TIMESTAMP"/>
            <result property="skssqz" column="skssqz" jdbcType="TIMESTAMP"/>
            <result property="qtkspzxmlx" column="qtkspzxmlx" jdbcType="CHAR"/>
            <result property="dkfs2" column="dkfs_2" jdbcType="DECIMAL"/>
            <result property="dkje2" column="dkje_2" jdbcType="DECIMAL"/>
            <result property="dkse" column="dkse" jdbcType="DECIMAL"/>
            <result property="ddkfs" column="ddkfs" jdbcType="DECIMAL"/>
            <result property="ddkje" column="ddkje" jdbcType="DECIMAL"/>
            <result property="ddkse" column="ddkse" jdbcType="DECIMAL"/>
            <result property="qddm" column="qddm" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,jxzzuuid,djxh,
        nsrsbh,nsrmc,sszq,
        skssqq,skssqz,qtkspzxmlx,
        dkfs_2,dkje_2,dkse,
        ddkfs,ddkje,ddkse,
        qddm,ywqd_dm,lrrq,
        xgrq,sjcsdq,sjgsdq,
        xgrsfid,lrrsfid,sjtb_sj
    </sql>
</mapper>
