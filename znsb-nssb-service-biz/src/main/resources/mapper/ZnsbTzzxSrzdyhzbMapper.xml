<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.gjss.mapper.srzz.jyk.ZnsbTzzxSrzdyhzbMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.gjss.pojo.domain.srzz.ZnsbTzzxSrzdyhzbDO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="sszq" column="sszq" jdbcType="INTEGER"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="gsh2" column="gsh_2" jdbcType="VARCHAR"/>
            <result property="lrzx" column="lrzx" jdbcType="VARCHAR"/>
            <result property="srlxdm" column="srlxdm" jdbcType="VARCHAR"/>
            <result property="jsfsDm1" column="jsfs_dm_1" jdbcType="CHAR"/>
            <result property="zsxmDm" column="zsxm_dm" jdbcType="VARCHAR"/>
            <result property="sl1" column="sl_1" jdbcType="DECIMAL"/>
            <result property="xssrkmdm" column="xssrkmdm" jdbcType="VARCHAR"/>
            <result property="bz" column="bz" jdbcType="VARCHAR"/>
            <result property="yxbz" column="yxbz" jdbcType="CHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="xse" column="xse" jdbcType="DECIMAL"/>
            <result property="se" column="se" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,sszq,
        nsrsbh,gsh_2,lrzx,
        srlxdm,jsfs_dm_1,zsxm_dm,
        sl_1,xssrkmdm,bz,
        yxbz,ywqd_dm,lrrq,
        lrrsfid,xgrq,xgrsfid,
        sjcsdq,sjgsdq,sjtb_sj,xse,se
    </sql>
</mapper>
