<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbYzqyYjnMapper">

    <resultMap id="BaseResultMap" type="com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbYzqyYjnSjgjDTO">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="pzxh" column="pzxh" jdbcType="DECIMAL"/>
            <result property="sbuuid" column="sbuuid" jdbcType="VARCHAR"/>
            <result property="dbsdwllywcjjrqdsrje" column="dbsdwllywcjjrqdsrje" jdbcType="DECIMAL"/>
            <result property="qtyzfwDlsdwllywBz" column="qtyzfw_dlsdwllyw_bz" jdbcType="VARCHAR"/>
            <result property="qtyzfwDlsdwllywYdk" column="qtyzfw_dlsdwllyw_ydk" jdbcType="DECIMAL"/>
            <result property="qtyzfwDlsdwllywXse" column="qtyzfw_dlsdwllyw_xse" jdbcType="DECIMAL"/>
            <result property="qtyzfwDljrbxywBz" column="qtyzfw_dljrbxyw_bz" jdbcType="VARCHAR"/>
            <result property="qtyzfwDljrbxywYdk" column="qtyzfw_dljrbxyw_ydk" jdbcType="DECIMAL"/>
            <result property="qtyzfwDljrbxywXse" column="qtyzfw_dljrbxyw_xse" jdbcType="DECIMAL"/>
            <result property="yztsfwBz" column="yztsfw_bz" jdbcType="VARCHAR"/>
            <result property="yztsfwYdk" column="yztsfw_ydk" jdbcType="DECIMAL"/>
            <result property="yztsfwXse" column="yztsfw_xse" jdbcType="DECIMAL"/>
            <result property="yzpbfwBz" column="yzpbfw_bz" jdbcType="VARCHAR"/>
            <result property="yzpbfwYdk" column="yzpbfw_ydk" jdbcType="DECIMAL"/>
            <result property="yzpbfwXse" column="yzpbfw_xse" jdbcType="DECIMAL"/>
            <result property="qtBz" column="qt_bz" jdbcType="VARCHAR"/>
            <result property="qtCbse" column="qt_cbse" jdbcType="DECIMAL"/>
            <result property="qtSysl" column="qt_sysl" jdbcType="DECIMAL"/>
            <result property="qtCbxse" column="qt_cbxse" jdbcType="DECIMAL"/>
            <result property="qtYjse" column="qt_yjse" jdbcType="DECIMAL"/>
            <result property="qtYzl" column="qt_yzl" jdbcType="DECIMAL"/>
            <result property="qtYdk" column="qt_ydk" jdbcType="DECIMAL"/>
            <result property="qtXse" column="qt_xse" jdbcType="DECIMAL"/>
            <result property="ypxsBz" column="ypxs_bz" jdbcType="VARCHAR"/>
            <result property="ypxsCbse" column="ypxs_cbse" jdbcType="DECIMAL"/>
            <result property="ypxsSysl" column="ypxs_sysl" jdbcType="DECIMAL"/>
            <result property="ypxsCbxse" column="ypxs_cbxse" jdbcType="DECIMAL"/>
            <result property="ypxsYjse" column="ypxs_yjse" jdbcType="DECIMAL"/>
            <result property="ypxsYzl" column="ypxs_yzl" jdbcType="DECIMAL"/>
            <result property="ypxsYdk" column="ypxs_ydk" jdbcType="DECIMAL"/>
            <result property="ypxsXse" column="ypxs_xse" jdbcType="DECIMAL"/>
            <result property="zgswjgDm" column="zgswjg_dm" jdbcType="CHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="yzyfwhjBz" column="yzyfwhj_bz" jdbcType="VARCHAR"/>
            <result property="yzyfwhjCbse" column="yzyfwhj_cbse" jdbcType="DECIMAL"/>
            <result property="yzyfwhjSysl" column="yzyfwhj_sysl" jdbcType="DECIMAL"/>
            <result property="yzyfwhjCbxse" column="yzyfwhj_cbxse" jdbcType="DECIMAL"/>
            <result property="yzyfwhjYjse" column="yzyfwhj_yjse" jdbcType="DECIMAL"/>
            <result property="yzyfwhjYzl" column="yzyfwhj_yzl" jdbcType="DECIMAL"/>
            <result property="yzyfwhjYdk" column="yzyfwhj_ydk" jdbcType="DECIMAL"/>
            <result property="yzyfwhjXse" column="yzyfwhj_xse" jdbcType="DECIMAL"/>
            <result property="yzpbfwYzl" column="yzpbfw_yzl" jdbcType="DECIMAL"/>
            <result property="yzpbfwYjse" column="yzpbfw_yjse" jdbcType="DECIMAL"/>
            <result property="yzpbfwCbxse" column="yzpbfw_cbxse" jdbcType="DECIMAL"/>
            <result property="yzpbfwSysl" column="yzpbfw_sysl" jdbcType="DECIMAL"/>
            <result property="yzpbfwCbse" column="yzpbfw_cbse" jdbcType="DECIMAL"/>
            <result property="yztsfwYzl" column="yztsfw_yzl" jdbcType="DECIMAL"/>
            <result property="yztsfwYjse" column="yztsfw_yjse" jdbcType="DECIMAL"/>
            <result property="yztsfwCbxse" column="yztsfw_cbxse" jdbcType="DECIMAL"/>
            <result property="yztsfwSysl" column="yztsfw_sysl" jdbcType="DECIMAL"/>
            <result property="yztsfwCbse" column="yztsfw_cbse" jdbcType="DECIMAL"/>
            <result property="qtyzfwDljrbxywYzl" column="qtyzfw_dljrbxyw_yzl" jdbcType="DECIMAL"/>
            <result property="qtyzfwDljrbxywYjse" column="qtyzfw_dljrbxyw_yjse" jdbcType="DECIMAL"/>
            <result property="qtyzfwDljrbxywCbxse" column="qtyzfw_dljrbxyw_cbxse" jdbcType="DECIMAL"/>
            <result property="qtyzfwDljrbxywSysl" column="qtyzfw_dljrbxyw_sysl" jdbcType="DECIMAL"/>
            <result property="qtyzfwDljrbxywCbse" column="qtyzfw_dljrbxyw_cbse" jdbcType="DECIMAL"/>
            <result property="qtyzfwDlsdwllywYzl" column="qtyzfw_dlsdwllyw_yzl" jdbcType="DECIMAL"/>
            <result property="qtyzfwDlsdwllywYjse" column="qtyzfw_dlsdwllyw_yjse" jdbcType="DECIMAL"/>
            <result property="qtyzfwDlsdwllywCbxse" column="qtyzfw_dlsdwllyw_cbxse" jdbcType="DECIMAL"/>
            <result property="qtyzfwDlsdwllywSysl" column="qtyzfw_dlsdwllyw_sysl" jdbcType="DECIMAL"/>
            <result property="qtyzfwDlsdwllywCbse" column="qtyzfw_dlsdwllyw_cbse" jdbcType="DECIMAL"/>
            <result property="sjblbz" column="sjblbz" jdbcType="TINYINT"/>
            <result property="sjtbSj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="ywqdDm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,pzxh,sbuuid,
        dbsdwllywcjjrqdsrje,qtyzfw_dlsdwllyw_bz,qtyzfw_dlsdwllyw_ydk,
        qtyzfw_dlsdwllyw_xse,qtyzfw_dljrbxyw_bz,qtyzfw_dljrbxyw_ydk,
        qtyzfw_dljrbxyw_xse,yztsfw_bz,yztsfw_ydk,
        yztsfw_xse,yzpbfw_bz,yzpbfw_ydk,
        yzpbfw_xse,qt_bz,qt_cbse,
        qt_sysl,qt_cbxse,qt_yjse,
        qt_yzl,qt_ydk,qt_xse,
        ypxs_bz,ypxs_cbse,ypxs_sysl,
        ypxs_cbxse,ypxs_yjse,ypxs_yzl,
        ypxs_ydk,ypxs_xse,zgswjg_dm,
        lrrq,xgrq,sjgsdq,
        yzyfwhj_bz,yzyfwhj_cbse,yzyfwhj_sysl,
        yzyfwhj_cbxse,yzyfwhj_yjse,yzyfwhj_yzl,
        yzyfwhj_ydk,yzyfwhj_xse,yzpbfw_yzl,
        yzpbfw_yjse,yzpbfw_cbxse,yzpbfw_sysl,
        yzpbfw_cbse,yztsfw_yzl,yztsfw_yjse,
        yztsfw_cbxse,yztsfw_sysl,yztsfw_cbse,
        qtyzfw_dljrbxyw_yzl,qtyzfw_dljrbxyw_yjse,qtyzfw_dljrbxyw_cbxse,
        qtyzfw_dljrbxyw_sysl,qtyzfw_dljrbxyw_cbse,qtyzfw_dlsdwllyw_yzl,
        qtyzfw_dlsdwllyw_yjse,qtyzfw_dlsdwllyw_cbxse,qtyzfw_dlsdwllyw_sysl,
        qtyzfw_dlsdwllyw_cbse,sjblbz,sjtb_sj,
        lrrsfid,xgrsfid,ywqd_dm,
        sjcsdq
    </sql>
</mapper>
