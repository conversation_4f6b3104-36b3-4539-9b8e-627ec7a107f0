
--- #################### sentinel相关配置 ####################
spring:
  cloud:
    sentinel:
      transport:
        dashboard: ***********:8090 #控制台地址
        http-method-specify: true #开启请求方式前缀
xxl:
  job:
    enabled: true
    admin:
      addresses: http://***********:30080/xxl-job-admin
    executor:
      appname: ${spring.application.name} # 执行器 AppName
      logpath: ${user.dir}/logs/xxl-job/${spring.application.name} # 执行器运行日志文件存储磁盘路径
    access-token: 9!1%Sw#QydXxlJobToken
feign:
  sentinel:
    enabled: true
--- #################### xxl-job相关配置 ####################

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1 # 初始连接数
        min-idle: 1 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: znsb_nssb
      datasource:
        znsb_nssb:
          url: *********************************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: znsb_yy
          password: zNdd_yY0322
        znsb_csdm:
          url: *********************************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: znsb_yy
          password: zNdd_yY0322
        cxk:
          url: ************************************************************************************************************************************************************************************************
          username: admin
          password: uyLr(Sha_NX7gtyKqvL
        mhzc:
          url: *********************************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: znsb_yy
          password: zNdd_yY0322
        jyss:
          url: ****************************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: znsb_yy
          password: zNdd_yY0322

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: *********** # 地址
    port: 6379 # 端口
    password: 9!1%Sw#QydRedis0322
    database: 0 # 数据库索引
  kafka:
    bootstrap-servers: ***********:9094
    producer:
      acks: 1
      batch-size: 16384
      retries: 0
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      compression-type: lz4
    consumer:
      enable: true
      zsdz:
      group-id: ${spring.application.name}-${random.uuid}
      enable-auto-commit: true
      auto-commit-interval: 100
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      heartbeat-interval: 9000
      properties:
        session:
          timeout:
            ms: 25000

css:
  cxtj:
    magic-api:
      resource:
        type: dev
      datasource:
        primary: csdm
        refs:
          csdm: znsb_csdm
          nssb: znsb_nssb
          mhzc:
          cxk:
      security:
        username: admin # 登录用的用户名
        password: css@123456 # 登录用的密码
