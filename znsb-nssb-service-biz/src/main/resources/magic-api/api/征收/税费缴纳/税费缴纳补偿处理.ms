{
  "properties" : { },
  "id" : "db7578c26be54ec09ec0df5a9d5a2026",
  "script" : null,
  "groupId" : "ab40c5864e864f9e935aa49f9389ff02",
  "name" : "税费缴纳补偿处理",
  "createTime" : null,
  "updateTime" : 1736828796029,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "admin",
  "path" : "/skjnbccl",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select B.nsrsbh,a.yzpzxh,a.djxh,b.xzqhsz_dm,c.zgswskfj_dm,DATE(DATE_ADD(max(a.skssqz), INTERVAL 1 DAY)) AS jsrqq,LAST_DAY(DATE_ADD(max(a.skssqz), INTERVAL 1 MONTH)) AS jsrqz 
from  znsb_nssb.znsb_nssb_jkyzmx a,znsb_mhzc.znsb_mhqx_jgxxb B,znsb_mhzc.znsb_mhzc_qyjbxxmx c
where A.djxh = B.djxh  
and A.djxh = C.djxh 
and a.dzsphm is null 
and a.yzfsrq >=DATE_ADD(NOW(), INTERVAL -6 MONTH)
group by B.nsrsbh,a.yzpzxh,a.djxh, b.xzqhsz_dm, c.zgswskfj_dm;
"""
return db.nssb.select(sql)