{
  "properties" : { },
  "id" : "03212e34bcc74040a49520e8e90e1316",
  "script" : null,
  "groupId" : "ce42a5ca15ab4d05b85f7b8ad6a36c4d",
  "name" : "查询未归集申报情况数据",
  "createTime" : null,
  "updateTime" : 1749740116455,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/cxwgjsbqksj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
// 步骤1：开启事务
var nssb = db.nssb;
var tx = nssb.transaction();
try {
    // 步骤2：执行查询（获取待更新数据的uuid列表）
    var result = nssb.select("""
        select uuid as ywuuid, djxh, nsrsbh, xzqhsz_dm, 
               date_format(skssqq, '%Y-%m-%d') as skssqq, 
               date_format(skssqz, '%Y-%m-%d') as skssqz, 
               yzpzzl_dm, zsxm_dm, sbuuid, pzxh, syuuid 
        from znsb_nssb_shgjsbqkjlb 
        where gjbz='N' and cgbz='N' and zxcs<=20
    """);
    
    // 步骤3：提取uuid集合
    var uuids = result.map(item => item.ywuuid);
    
    // 步骤4：批量更新符合条件的记录
    if (uuids.length > 0) {
        nssb.update("""
            update znsb_nssb_shgjsbqkjlb 
            set gjbz = 'Z' 
            where uuid in 
            <foreach item='item' index='index' collection='uuids' open="(" separator="," close=")">
                #{item}
            </foreach>
        """);
    }
    
    // 步骤5：提交事务
    tx.commit();
    return result;  // 返回原始查询结果
} catch (e) {
    tx.rollback();  // 失败时回滚
}