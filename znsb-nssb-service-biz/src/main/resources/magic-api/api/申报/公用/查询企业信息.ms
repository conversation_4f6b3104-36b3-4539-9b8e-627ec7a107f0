{
  "properties" : { },
  "id" : "573fd57a56c846adb1e74b739fc79988",
  "script" : null,
  "groupId" : "ce42a5ca15ab4d05b85f7b8ad6a36c4d",
  "name" : "查询企业信息",
  "createTime" : null,
  "updateTime" : 1736478513898,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/listAllCompnyInfo",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "{\r\n    \"djxhList\":[10213410000000135383, 10111525000003330002]\r\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "nsrsbhList",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Array",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  },
  "responseBodyDefinition" : null
}
================================
const sql = """
WITH RankedZnsbMhzcNsrzgxx AS (
    SELECT
        m.*,
        ROW_NUMBER() OVER (PARTITION BY m.djxh ORDER BY m.lrrq DESC) AS rn
    FROM
        znsb_mhzc_nsrzgxx m
    WHERE
        m.yxqq <= CURRENT_DATE AND m.yxqz >= CURRENT_DATE
)

SELECT
    j.djxh,
    j.nsrsbh,
    j.jgmc_1 as nsrmc,
    j.xzqhsz_dm as xzqhsz_dm,
    j.jguuid_1,
<if test="_databaseId=='oracle'">
	nvl(rz.zzsnsrlxDm, '02') as zzsnsrlx_dm,
</if>
<else>
	ifnull(rz.zzsnsrlxDm, '02') as zzsnsrlx_dm,
</else>
    j.qydmz
FROM
    znsb_mhqx_jgxxb j
LEFT JOIN znsb_mhzc_qyjbxxmx n ON j.djxh = n.djxh
LEFT JOIN (
    SELECT
        rz.djxh,
        (case when rz.nsrlx in (1, 3, 4) then '01' else '02' end) as zzsnsrlxDm
    FROM
        RankedZnsbMhzcNsrzgxx rz
    WHERE
        rz.rn = 1
) rz ON j.djxh = rz.djxh
where j.djxh in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
       #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
       CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
<if test="nsrsbh != null and nsrsbh != ''">
    and j.nsrsbh = #{nsrsbh}
</if>
order by rz.zzsnsrlxDm
"""
return db.mhzc.select(sql)