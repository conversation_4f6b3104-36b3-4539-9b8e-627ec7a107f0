{
  "properties" : { },
  "id" : "b825be9587ef43a790e30e086eb7572e",
  "script" : null,
  "groupId" : "32dff0a0ff6b4c1d9e24306476766793",
  "name" : "查询重点税源采集列表",
  "createTime" : 1723530868401,
  "updateTime" : 1720164688803,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/cxzdsycjlb",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"msg\": \"success\",\n    \"data\": [\n        {\n            \"djxh\": 10213410000000135000,\n            \"nsrsbh\": \"91341024151783006H\",\n            \"xzqhszDm\": \"340000\",\n            \"ssnyq\": \"202306\",\n            \"ssnyz\": \"202406\",\n            \"pagenum\": 1,\n            \"pagesize\": 1000\n        },\n        {\n            \"djxh\": 1021000000078857,\n            \"nsrsbh\": \"91340100790122917R\",\n            \"xzqhszDm\": \"340000\",\n            \"ssnyq\": \"202306\",\n            \"ssnyz\": \"202406\",\n            \"pagenum\": 1,\n            \"pagesize\": 1000\n        }\n    ],\n    \"timestamp\": 1718327664939,\n    \"requestTime\": 1718327664923,\n    \"executeTime\": 16\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "1",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "msg",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "data",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Array",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ {
        "name" : "",
        "value" : "",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ {
          "name" : "djxh",
          "value" : "1021000000078857",
          "description" : "",
          "required" : false,
          "dataType" : "Long",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "nsrsbh",
          "value" : "91340100790122917R",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "xzqhszDm",
          "value" : "340000",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "ssnyq",
          "value" : "202306",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "ssnyz",
          "value" : "202406",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "pagenum",
          "value" : "1",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "pagesize",
          "value" : "1000",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        } ]
      } ]
    }, {
      "name" : "timestamp",
      "value" : "1718264929433",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "requestTime",
      "value" : "1718264929421",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "executeTime",
      "value" : "12",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
const sql = """
select djxh,nsrsbh ,xzqhsz_dm,
date_format(NOW() - INTERVAL 1 year,'%Y%m') as ssnyq,
date_format(now(),'%Y%m') as ssnyz,
1 as pageNum,
1000 as pageSize
from znsb_mhzc.znsb_mhqx_jgxxb
"""
return db.mhzc.select(sql)