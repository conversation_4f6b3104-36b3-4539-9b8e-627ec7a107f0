{
  "properties" : { },
  "id" : "7599a7070b454d41b4e6181894c297bb",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "申报概览导出申报明细查询",
  "createTime" : null,
  "updateTime" : 1758764372264,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/exportSbmxList",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sql = """
SELECT
    s.*
FROM
    znsb_nssb_sbrw s
where s.DJXH in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
<if test="yzpzzlDm != null and yzpzzlDm != ''">
    and s.yzpzzl_dm = #{yzpzzlDm}
</if>
<if test="zsxmDm != null and zsxmDm != ''">
    and s.zsxm_dm = #{zsxmDm}
</if>
and s.sbny = #{sbny}
"""
return db.nssb.select(sql)