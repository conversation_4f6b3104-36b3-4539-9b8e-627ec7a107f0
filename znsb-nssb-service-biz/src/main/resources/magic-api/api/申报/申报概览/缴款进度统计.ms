{
  "properties" : { },
  "id" : "6ccc5f55e4994f4ab41b86c26201eb54",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "缴款进度统计",
  "createTime" : null,
  "updateTime" : 1754448956684,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/jkjdtj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "{\r\n    \"djxhList\":[\"10111525000001030001\", \"10214406050000000214\"]\r\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sbny = DateUtil.format(new Date(), "yyyyMM");
const sql = """
with
    sbrw_cte as (select * from znsb_nssb_sbrw where DJXH in
<if test="_databaseId=='oracle'">
<foreach item='item' index ='index' collection='djxhList'
    open ="(" separator ="," close =")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index ='index' collection='djxhList'
    open ="(" separator ="," close =")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
    and sbny = #{sbny}
    and rwzt_dm = '02'
    ),
    sbrw_cte2 as (select * from znsb_nssb_sbrw where DJXH in
<if test="_databaseId=='oracle'">
<foreach item='item' index ='index' collection='djxhList'
    open ="(" separator ="," close =")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index ='index' collection='djxhList'
    open ="(" separator ="," close =")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
    and sbny = #{sbny}),
    bqyjsf_cte as (select * from znsb_nssb_jkyzmx where yzpzxh in (select pzxh from sbrw_cte where pzxh is not null) and clzt_dm = '01'),
<if test="_databaseId=='oracle'">
	yjkje_cte as (select sum(nvl(ybtse, 0)) as yjkje from sbrw_cte where ybtse > 1),
    yjkje1_cte as (select sum(nvl(ybtse, 0)) as yjkje1 from bqyjsf_cte),
</if>
<else>
	yjkje_cte as (select sum(ifnull(ybtse, 0)) as yjkje from sbrw_cte where ybtse > 1),
    yjkje1_cte as (select sum(ifnull(ybtse, 0)) as yjkje1 from bqyjsf_cte),
</else>
    yjksbbsl_cte as (select sum(case when (c.sbjkzt_dm = '03' or c.ybtse <= 1) then 1 else 0 end) as yjksl from sbrw_cte c),
    yjksbrw_cte as (select * from sbrw_cte rw where rw.sbjkzt_dm IN ('03') or rw.ybtse <= 1),
    yjkhs_cte as (select count(1) as yjkhs from (select rw.djxh,count(rw.djxh) as yjkhs from yjksbrw_cte rw group by rw.djxh) rwlb join (select b.djxh,count(b.djxh) as zhs from sbrw_cte2 b group by b.djxh) blb on rwlb.djxh = blb.djxh where rwlb.yjkhs = blb.zhs),
    zhs_cte as (select count(distinct rw.djxh) as zhs from sbrw_cte2 rw),
    zsl_cte as (select count(*) as zsl from sbrw_cte2 rw)
<if test="_databaseId=='oracle'">
	select (case when zbs = 0 or yjksbbs = 0 then concat(0, '') else concat(round(round(ifnull(yjksbbs, 0) / ifnull(zbs, 0), 2) * 100, 0), '') end) as jkjd, jktj.* from (select
    nvl((select yjkje from yjkje_cte), 0) as yjkje,
    nvl((select yjkje1 from yjkje1_cte), 0) as yjkje1,
    nvl((select yjksl from yjksbbsl_cte), 0) as yjksbbs,
    nvl((select yjkhs from yjkhs_cte), 0) as yjkhs,
    nvl((select zhs from zhs_cte), 0) as zhs,
    nvl((select zsl from zsl_cte), 0) as zbs from dual) jktj
</if>
<else>
	select (case when zbs = 0 or yjksbbs = 0 then concat(0, '') else concat(round(round(ifnull(yjksbbs, 0) / ifnull(zbs, 0), 2) * 100, 0), '') end) as jkjd, jktj.* from (select
    ifnull((select yjkje from yjkje_cte), 0) as yjkje,
    ifnull((select yjkje1 from yjkje1_cte), 0) as yjkje1,
    ifnull((select yjksl from yjksbbsl_cte), 0) as yjksbbs,
    ifnull((select yjkhs from yjkhs_cte), 0) as yjkhs,
    ifnull((select zhs from zhs_cte), 0) as zhs,
    ifnull((select zsl from zsl_cte), 0) as zbs) as jktj
</else>

"""
return db.nssb.select(sql)