{
  "properties" : { },
  "id" : "c8b532337d6b4531ac5290557afee420",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "管理企业资格信息统计",
  "createTime" : null,
  "updateTime" : 1730881951141,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/glqyzgxxtj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select
	count(*) as glqyhs,
	sum(case when a.nsrlx in ('1', '3', '4') then 1 else 0 end) as ybnsrhs,
	sum(case when a.nsrlx not in ('1', '3', '4') or a.nsrlx is null then 1 else 0 end) as xgmnsrhs
from
	(
	select
		jb.*,
		rz.nsrlx
	from
		znsb_mhzc_qyjbxxmx jb
	left join (
		select
			rz.djxh,
			rz.nsrlx
		from
			(SELECT
        m.*,
        ROW_NUMBER() OVER (PARTITION BY m.djxh ORDER BY m.lrrq DESC) AS rn
    FROM
        znsb_mhzc_nsrzgxx m
    WHERE
        m.yxqq <= CURRENT_DATE AND m.yxqz >= CURRENT_DATE) rz
		where
			rz.rn = 1
) rz on
		jb.djxh = rz.djxh
	where jb.DJXH in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>

        ) 
<if test="_databaseId=='mysql'">
	as
</if>		
 a
"""
return db.mhzc.select(sql)