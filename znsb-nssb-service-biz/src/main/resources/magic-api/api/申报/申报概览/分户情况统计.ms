{
  "properties" : { },
  "id" : "56a111d2e42040368a14be06e30ca89f",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "分户情况统计",
  "createTime" : null,
  "updateTime" : 1731479627380,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/fhqktj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "{\r\n    \"nsrsbhList\": []\r\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "nsrsbhList",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Array",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ {
        "name" : "",
        "value" : "91340100790122917R",
        "description" : "",
        "required" : false,
        "dataType" : "String",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      } ]
    } ]
  },
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
SELECT
    s.djxh,
    sum(case when s.rwzt_dm = '02' then 1 else 0 end) as sbsl,
    count(s.djxh) as rwzs,
    SUM(CASE WHEN (s.sbjkzt_dm = '03' or (s.ybtse <= 1 and s.nsrsbzt_dm != '31')) THEN 1 ELSE 0 END) as jksl
FROM
    znsb_nssb_sbrw s
where s.DJXH in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
<if test="nsrsbh != null and nsrsbh != ''">
    and s.nsrsbh = #{nsrsbh}
</if>
and s.sbny = #{sbny}
GROUP BY
    s.djxh
"""
return db.nssb.select(sql)