{
  "properties" : { },
  "id" : "63f077af893245469eb1671c6a09b07a",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "查询管理企业",
  "createTime" : null,
  "updateTime" : 1730881902696,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/listGlqymx",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select distinct nsrsbh, nsrmc as jgmc
from znsb_mhzc_qyjbxxmx
where DJXH in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else> 
"""
return db.mhzc.select(sql)