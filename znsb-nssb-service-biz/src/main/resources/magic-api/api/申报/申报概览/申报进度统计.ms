{
  "properties" : { },
  "id" : "34d77c58b6374ec696311cc2f5447045",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "申报进度统计",
  "createTime" : null,
  "updateTime" : 1754448771428,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/sbjdtj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "{\"djxhList\":[\"10111525000001030001\"]}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sbny = DateUtil.format(new Date(), "yyyyMM");
const sql = """
with sbrw_cte as (select * from znsb_nssb_sbrw where DJXH in
<if test="_databaseId=='oracle'">
   <foreach item='item' index ='index' collection='djxhList'
    open ="(" separator ="," close =")">
    #{item}
   </foreach>
</if>
<else>
   <foreach item='item' index ='index' collection='djxhList'
    open ="(" separator ="," close =")">
    CAST( #{item} AS DECIMAL(20,0))
   </foreach>
</else>
    and sbny = #{sbny}
    ),
zsl_djxh_cte as (select djxh, count(*) as zsl from sbrw_cte group by djxh),
sbsl_djxh_cte as (select djxh, count(*) as sbsl from sbrw_cte where rwzt_dm = '02' group by djxh),
zbs_cte as (select count(*) as zbs from sbrw_cte),
<if test="_databaseId=='oracle'">
	ysbje_cte as (select sum(case when rwzt_dm = '02' then nvl(ybtse, 0.00) else 0.00 end) as ysbje from sbrw_cte),
</if>
<else>
	ysbje_cte as (select sum(case when rwzt_dm = '02' then ifnull(ybtse, 0.00) else 0.00 end) as ysbje from sbrw_cte),
</else>
sbbs_cte as (select sum(case when rwzt_dm = '02' then 1 else 0 end) as sbbs from sbrw_cte),
sbhs_cte as (select sum(case when a.zsl = b.sbsl then 1 else 0 end) as sbhs from zsl_djxh_cte a left join sbsl_djxh_cte b on a.djxh = b.djxh),
zhs_cte as (select count(distinct djxh) as zhs from sbrw_cte),
<if test="_databaseId=='oracle'">
	sbjd_cte as (select concat(round(round(s.sbbs / nvl(z.zbs, 0), 2) * 100, 0), '') as sbjd from sbbs_cte s, zbs_cte z)
</if>
<else>
	sbjd_cte as (select concat(round(round(s.sbbs / ifnull(z.zbs, 0), 2) * 100, 0), '') as sbjd from sbbs_cte s, zbs_cte z)
</else>
<if test="_databaseId=='oracle'">
	select
    nvl((select zbs from zbs_cte), 0) as zbs,
    nvl((select ysbje from ysbje_cte), 0.00) as ysbje,
    nvl((select sbbs from sbbs_cte), 0) as sbbs,
    nvl((select sbhs from sbhs_cte), 0) as sbhs,
    nvl((select zhs from zhs_cte), 0) as zhs,
    nvl((select sbjd from sbjd_cte), '0') as sbjd,
    0.00 from dual dsbsess
</if>
<else>
	select
    ifnull((select zbs from zbs_cte), 0) as zbs,
    ifnull((select ysbje from ysbje_cte), 0.00) as ysbje,
    ifnull((select sbbs from sbbs_cte), 0) as sbbs,
    ifnull((select sbhs from sbhs_cte), 0) as sbhs,
    ifnull((select zhs from zhs_cte), 0) as zhs,
    ifnull((select sbjd from sbjd_cte), '0') as sbjd,
    0.00 as dsbsess
</else>
"""
return db.nssb.select(sql)