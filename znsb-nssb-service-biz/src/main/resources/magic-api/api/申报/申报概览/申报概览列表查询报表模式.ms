{
  "properties" : { },
  "id" : "8806882439364fd79d3489d241d68571",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "申报概览列表查询报表模式",
  "createTime" : null,
  "updateTime" : 1756346227890,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/querySbglBbms",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;


const sql = """
select s.rwlx_dm,s.yzpzzl_dm,s.zsxm_dm,
sum(case when s.rwzt_dm = '02' then 1 else 0 end) as sbsl,
count(s.djxh) as rwzs,
SUM(CASE WHEN (s.sbjkzt_dm = '03' or (s.ybtse <= 1 and s.nsrsbzt_dm != '31')) THEN 1 ELSE 0 END) as jksl
from znsb_nssb_sbrw s
where s.sbny = #{sbny} and djxh in
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
       CAST( #{item} AS DECIMAL(20,0))
</foreach>
and s.yzpzzl_dm in('BDA0610606','BDA0611148','BDA0610611','BDA0611159','BDA0610865','ZLA0610208') 
group by rwlx_dm,yzpzzl_dm,zsxm_dm
order by rwlx_dm,zsxm_dm,yzpzzl_dm


"""
return db.nssb.select(sql)