{
  "properties" : { },
  "id" : "ac23d7943d0f46ca97413de8ac1af587",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "申报概览列表查询企业模式",
  "createTime" : null,
  "updateTime" : 1757922224063,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/querySbglQyms",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sql = """
SELECT
    s.djxh,
    sum(case when s.rwzt_dm = '02' then 1 else 0 end) as sbsl,
    count(s.djxh) as rwzs,
    SUM(CASE WHEN (s.sbjkzt_dm = '03' or (s.ybtse <= 1 and s.nsrsbzt_dm != '31')) THEN 1 ELSE 0 END) as jksl
FROM
    znsb_nssb_sbrw s
where s.DJXH in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
<if test="nsrsbh != null and nsrsbh != ''">
    and s.nsrsbh = #{nsrsbh}
</if>
<if test="yzpzzlDm != null and yzpzzlDm != ''">
    and s.yzpzzl_dm = #{yzpzzlDm}
</if>
<if test="zsxmDm != null and zsxmDm != ''">
    and s.zsxm_dm = #{zsxmDm}
</if>
and s.sbny = #{sbny}
GROUP BY
    s.djxh
"""
return db.nssb.select(sql)