{
  "properties" : { },
  "id" : "0133e7dfc97e46f6919c85b9b4f6c8d1",
  "script" : null,
  "groupId" : "081355eca4e94a33ad4aa0310709053a",
  "name" : "审批进度统计",
  "createTime" : null,
  "updateTime" : 1730881967177,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/spjdxx",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
<if test="_databaseId=='oracle'">
	select
    nvl(sum(case when nsrsbzt_dm in ('01', '02', '03', '41', '42', '43') then 1 else 0 end), 0) as wtjsl,
    nvl(sum(case when nsrsbzt_dm in ('04', '21', '44') then 1 else 0 end), 0) as spzsl,
    nvl(sum(case when nsrsbzt_dm in ('05', '06', '07', '08', '22', '23', '24', '45', '46', '47', '48', '49', '50', '51') then 1 else 0 end), 0) as zswsl
</if>
<else>
	select
    ifnull(sum(case when nsrsbzt_dm in ('01', '02', '03', '41', '42', '43') then 1 else 0 end), 0) as wtjsl,
    ifnull(sum(case when nsrsbzt_dm in ('04', '21', '44') then 1 else 0 end), 0) as spzsl,
    ifnull(sum(case when nsrsbzt_dm in ('05', '06', '07', '08', '22', '23', '24', '45', '46', '47', '48', '49', '50', '51') then 1 else 0 end), 0) as zswsl
</else>
from
    znsb_nssb_sbrw
where
    sbny=#{sbny}
    and DJXH in
<if test="_databaseId=='oracle'">
    <foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
    </foreach>
</if>
<else>
    <foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
    </foreach>
</else>

"""
return db.nssb.select(sql)