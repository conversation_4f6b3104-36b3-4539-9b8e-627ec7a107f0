{
  "properties" : { },
  "id" : "ad5772a2ece34b4f89eb721ff599d323",
  "script" : null,
  "groupId" : "23754f44a205445cb4fa52f9d2da3974",
  "name" : "企业端上线初始化申报数据归集查询企业信息",
  "createTime" : 1723530868367,
  "updateTime" : 1719556092149,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/initQueryQyxx",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"msg\": \"success\",\n    \"data\": [\n        {\n            \"nsrsbh\": \"91340200MA2U59YU1Q\",\n            \"djxh\": 10213402000001133000,\n            \"xzqhszDm\": \"340000\",\n            \"nsrmc\": \"安徽预生产-测试缴款归集\"\n        }\n    ],\n    \"timestamp\": 1719556083472,\n    \"requestTime\": 1719556083456,\n    \"executeTime\": 16\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select nsrsbh, djxh, xzqhsz_Dm, jgmc_1 as nsrmc from znsb_mhqx_jgxxb where nsrsbh = '91340200MA2U59YU1Q'
"""
return db.mhzc.select(sql)