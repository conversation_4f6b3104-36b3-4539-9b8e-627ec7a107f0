{
  "properties" : { },
  "id" : "fdcdf5b159844574ace2b902ce4d4cbd",
  "script" : null,
  "groupId" : "d496b98d1bdb48e19fcc3c14e926359f",
  "name" : "增值税一般纳税人按报表查询申报任务明细(分页查询)",
  "createTime" : null,
  "updateTime" : 1743646076485,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "admin",
  "path" : "/querySbrwmxForZzsybnsrFy",
  "method" : "POST",
  "parameters" : [ {
    "name" : "pageStart",
    "value" : "",
    "description" : null,
    "required" : true,
    "dataType" : "Integer",
    "type" : null,
    "defaultValue" : "0",
    "validateType" : "pass",
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "pageSize",
    "value" : "",
    "description" : null,
    "required" : true,
    "dataType" : "Integer",
    "type" : null,
    "defaultValue" : "10",
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

//const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select s.* ,z.zzsbqybtse zzsse,z.bqybtsecjs cswhjssse,z.bqybtsejyfj jyffjse,z.bqybtsedfjyfj dfjyfjse, (case when z.ksbbz is null then 'N' else z.ksbbz end) as ksbbz
from znsb_nssb_sbrw s 
left join znsb_nssb_ysbjgb_zzsybnsr z
on s.sbrwuuid = z.sbrwuuid
where s.yzpzzl_dm = 'BDA0610606'
and s.zsxm_dm = '10101'
and s.sbny = #{sbny}
and s.djxh in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
and (s.nsrsbh like #{nsrsbh} or s.nsrmc like #{nsrsbh} or s.qydmz like #{nsrsbh})
and s.bsy like #{bsy}
<if test="rwztDm != null and rwztDm != ''">
    and s.rwzt_dm = #{rwztDm}
</if>
order by s.qydmz,s.nsrmc,s.rwzt_dm,s.nsrsbzt_dm,s.sbrwuuid

"""
return db.nssb.page(sql,pageSize,pageStart)