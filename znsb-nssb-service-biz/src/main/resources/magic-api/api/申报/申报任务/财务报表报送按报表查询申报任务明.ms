{
  "properties" : { },
  "id" : "b828281560c64a0d852325d3b3eb9fdc",
  "script" : null,
  "groupId" : "d496b98d1bdb48e19fcc3c14e926359f",
  "name" : "财务报表报送按报表查询申报任务明",
  "createTime" : 1756691522604,
  "updateTime" : null,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : null,
  "path" : "querySbrwmxForCwbbbs",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

//const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select s.*
from znsb_nssb_sbrw s 
where s.yzpzzl_dm = 'ZLA0610208'
and s.sbny = #{sbny}
and s.djxh in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
and (s.nsrsbh like #{nsrsbh} or s.nsrmc like #{nsrsbh} or s.qydmz like #{nsrsbh})
and s.bsy like #{bsy}
<if test="rwztDm != null and rwztDm != ''">
    and s.rwzt_dm = #{rwztDm}
</if>
order by s.qydmz,s.nsrmc,s.rwzt_dm,s.nsrsbzt_dm,s.sbrwuuid

"""
return db.nssb.select(sql)