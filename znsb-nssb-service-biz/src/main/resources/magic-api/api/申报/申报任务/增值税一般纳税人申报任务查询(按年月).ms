{
  "properties" : { },
  "id" : "f2122abde638412f8e76983dc83c839c",
  "script" : null,
  "groupId" : "d496b98d1bdb48e19fcc3c14e926359f",
  "name" : "增值税一般纳税人申报任务查询(按年月)",
  "createTime" : 1723530868383,
  "updateTime" : 1716286920113,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/listZzsybnsrSbrwBySbny",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;


const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select * from znsb_nssb_sbrw where yzpzzl_dm = 'BDA0610606' and sbny = #{sbny};
"""

return db.nssb.select(sql)