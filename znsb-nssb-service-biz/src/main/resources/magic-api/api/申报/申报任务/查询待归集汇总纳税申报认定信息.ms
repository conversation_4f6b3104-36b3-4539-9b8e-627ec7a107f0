{
  "properties" : { },
  "id" : "ef7210568aba4a36855c71c995d9a20e",
  "script" : null,
  "groupId" : "d496b98d1bdb48e19fcc3c14e926359f",
  "name" : "查询待归集汇总纳税申报认定信息",
  "createTime" : null,
  "updateTime" : 1733455602911,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "admin",
  "path" : "listDgjHznssbRdxx",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;


const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select sbrwuuid as ywuuid, djxh, nsrsbh, xzqhsz_dm, date_format(skssqq, '%Y-%m-%d') as skssqq, date_format(skssqz, '%Y-%m-%d') as skssqz, zsxm_dm from znsb_nssb_sbrw where yzpzzl_dm in ('BDA0610606', 'BDA0611111') and sbny = #{sbny};
"""

return db.nssb.select(sql)