{
  "properties" : { },
  "id" : "a406fb44a10a47d4a19df4b3a0d05ff7",
  "script" : null,
  "groupId" : "d496b98d1bdb48e19fcc3c14e926359f",
  "name" : "企业所得税预缴按报表查询申报任务明细(分页查询)",
  "createTime" : null,
  "updateTime" : 1743646060329,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/querySbrwmxForQysdsyjFy",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

//const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select s.* ,z.bqybtse bqybtse, z.sjybtse sjybtse
from znsb_nssb_sbrw s 
left join znsb_nssb_ysbjgb_qysdsyjsb z
on s.sbrwuuid = z.sbrwuuid
where s.yzpzzl_dm = 'BDA0611159'
and s.zsxm_dm = '10104'
and s.sbny = #{sbny}
and s.djxh in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
and (s.nsrsbh like #{nsrsbh} or s.nsrmc like #{nsrsbh} or s.qydmz like #{nsrsbh})
and s.bsy like #{bsy}
<if test="rwztDm != null and rwztDm != ''">
    and s.rwzt_dm = #{rwztDm}
</if>
order by s.qydmz,s.nsrmc,s.rwzt_dm,s.nsrsbzt_dm,s.sbrwuuid

"""
return db.nssb.page(sql,pageSize,pageStart)