{
  "properties" : { },
  "id" : "a190b29b60a8cafedbee5a3a952e0536",
  "script" : null,
  "groupId" : "d496b98d1bdb48e19fcc3c14e926359f",
  "name" : "增值税预缴按报表查询申报任务明细",
  "createTime" : null,
  "updateTime" : 1756781348634,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/querySbrwmxForZzsyj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

//const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select s.* ,z.zzsbqybtse zzsse,z.bqybtsecjs cswhjssse,z.bqybtsejyfj jyffjse,z.bqybtsedfjyfj dfjyfjse,z.ksbbz
(case when z.ksbbz is null then 'N' else z.ksbbz end) as ksbbz,
z.xse,z.xzqhsz_dm xzqhszDm,z.fwcqzsh bdcqzh,z.fwzldz fydz,z.zgswjg_dm zgswjgDm
from znsb_nssb_sbrw s 
left join znsb_nssb_ysbjgb_zzsyj z
on s.sbrwuuid = z.sbrwuuid
where s.yzpzzl_dm = 'BDA0610865'
and s.zsxm_dm = '10101'
and s.sbny = #{sbny}
and s.djxh in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
and (s.nsrsbh like #{nsrsbh} or s.nsrmc like #{nsrsbh} or s.qydmz like #{nsrsbh})
and s.bsy like #{bsy}
<if test="rwztDm != null and rwztDm != ''">
    and s.rwzt_dm = #{rwztDm}
</if>
<if test="xzqhszDm != null and xzqhszDm != ''">
    and s.xzqhsz_dm = #{xzqhszDm}
</if>
<if test="bdcqzh != null and bdcqzh != '' and bdcqzh != '%%'">
    and z.fwcqzsh like #{bdcqzh}
</if>
<if test="fydz != null and fydz != '' and bdcdyh != '%%'">
    and z.fwzldz like #{fydz}
</if>
order by s.qydmz,s.nsrmc,s.rwzt_dm,s.nsrsbzt_dm,s.sbrwuuid

"""
return db.nssb.select(sql)