{
  "properties" : { },
  "id" : "ff7151c6dada43688b646a66b7beb387",
  "script" : null,
  "groupId" : "d496b98d1bdb48e19fcc3c14e926359f",
  "name" : "企业端归集应申报统计数据",
  "createTime" : 1723530868381,
  "updateTime" : 1722324362516,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/qydgjysbtjsj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": -1,\n    \"msg\": \"系统内部出现错误\",\n    \"data\": null,\n    \"timestamp\": 1722324322626,\n    \"requestTime\": 1722324322623,\n    \"executeTime\": 3\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select nsrsbh, djxh, xzqhsz_Dm, jgmc_1 as nsrmc from znsb_mhqx_jgxxb
"""
return db.mhzc.select(sql)