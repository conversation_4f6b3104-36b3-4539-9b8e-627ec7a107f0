{
  "properties" : { },
  "id" : "bbbc77b26823433fa90f03a2c2f0e1bf",
  "script" : null,
  "groupId" : "d496b98d1bdb48e19fcc3c14e926359f",
  "name" : "房产税按报表查询申报任务明细(分页查询)",
  "createTime" : null,
  "updateTime" : 1752025830978,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/querySbrwmxForFcsFy",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

//const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select s.* ,z.ybtse bqybtse,z.bdcqzh,z.cjybtse,z.czybtse
from znsb_nssb_sbrw s 
left join znsb_nssb_ysbjgb_cchxws z
on s.sbrwuuid = z.sbrwuuid
where s.yzpzzl_dm = 'BDA0611148'
and s.zsxm_dm = '10110'
and s.sbny = #{sbny}
and s.djxh in
<if test="_databaseId=='oracle'">
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    #{item}
</foreach>
</if>
<else>
<foreach item='item' index='index' collection='djxhList'
      open="(" separator="," close=")">
    CAST( #{item} AS DECIMAL(20,0))
</foreach>
</else>
and (s.nsrsbh like #{nsrsbh} or s.nsrmc like #{nsrsbh} or s.qydmz like #{nsrsbh})
and s.bsy like #{bsy}
<if test="rwztDm != null and rwztDm != ''">
    and s.rwzt_dm = #{rwztDm}
</if>
<if test="xzqhszDm != null and xzqhszDm != ''">
    and s.xzqhsz_dm = #{xzqhszDm}
</if>
<if test="bdcqzh != null and bdcqzh != '' and bdcqzh != '%%'">
    and z.bdcqzh like #{bdcqzh}
</if>
order by s.qydmz,s.nsrmc,s.rwzt_dm,s.nsrsbzt_dm,s.sbrwuuid

"""
return db.nssb.page(sql,pageSize,pageStart)