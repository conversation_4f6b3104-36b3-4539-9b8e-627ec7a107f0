{
  "properties" : { },
  "id" : "24aa72d86ebf4221ad29d76591846695",
  "script" : null,
  "groupId" : "e64eda74b6da45bf8cb02b1825dee3d4",
  "name" : "查询待拉取预填数据列表",
  "createTime" : null,
  "updateTime" : 1733110515868,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "admin",
  "path" : "/listDlqYtsj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select
    a.sbrwuuid,
	a.djxh,
	date_format(a.skssqq, '%Y-%m-%d') as skssqq,
	date_format(a.skssqz, '%Y-%m-%d') as skssqz,
	b.businessclob as qcxx
from
	znsb_nssb_ysbjgb_zzsybnsr a
left join znsb_nssb_qcxx b on
	a.sbrwuuid = b.sbrwuuid
	and b.yxbz = 'Y'
	and b.clcgbz = 'Y'
where
	a.xczxsj <= SYSDATE()
"""
return db.nssb.select(sql)