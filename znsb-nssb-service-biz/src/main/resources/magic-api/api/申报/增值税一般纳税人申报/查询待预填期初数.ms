{
  "properties" : { },
  "id" : "2f70453b791f4070a4bfad14982e7dc5",
  "script" : null,
  "groupId" : "e64eda74b6da45bf8cb02b1825dee3d4",
  "name" : "查询待预填期初数",
  "createTime" : null,
  "updateTime" : 1733110547990,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "admin",
  "path" : "/listDytQcxx",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select
	a.sbrwuuid,
	a.djxh,
	a.nsrsbh,
	date_format(a.skssqq, '%Y-%m-%d') as skssqq,
	date_format(a.skssqz, '%Y-%m-%d') as skssqz,
	b.businessclob as qcxx
from
	znsb_nssb_sbrw a
left join znsb_nssb_qcxx b on
	a.sbrwuuid = b.sbrwuuid
	and b.yxbz = 'Y'
	and b.clcgbz = 'Y'
where
	a.yzpzzl_dm = 'BDA0610606'
	and a.sbny =#{sbny} 
	and a.rwzt_dm = '01'
	<if test="djxh != null and djxh != ''">
	    and a.djxh = #{djxh}
	</if>
"""
return db.nssb.select(sql)