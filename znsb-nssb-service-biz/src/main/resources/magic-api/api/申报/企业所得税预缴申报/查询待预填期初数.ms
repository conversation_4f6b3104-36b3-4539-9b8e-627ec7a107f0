{
  "properties" : { },
  "id" : "6edcec8dc6794a8e8158c269562182fe",
  "script" : null,
  "groupId" : "fe6104e670e647279c8f4ef56265bbe6",
  "name" : "查询待预填期初数",
  "createTime" : 1748935803149,
  "updateTime" : null,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : null,
  "path" : "/listDytQcxx",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select
	a.sbrwuuid,
	a.djxh,
	a.nsrsbh,
	date_format(a.skssqq, '%Y-%m-%d') as skssqq,
	date_format(a.skssqz, '%Y-%m-%d') as skssqz,
	b.businessclob as qcxx
from
	znsb_nssb_sbrw a
left join znsb_nssb_qcxx b on
	a.sbrwuuid = b.sbrwuuid
	and b.yxbz = 'Y'
	and b.clcgbz = 'Y'
where
	a.yzpzzl_dm = 'BDA0611159'
	and a.sbny =#{sbny} 
	and a.nsrsbzt_dm in ('01', '02', '06', '08', '99')
"""
return db.nssb.select(sql)