{
  "properties" : { },
  "id" : "c9a1469abf864a6aa874e1c04f8d5e29",
  "script" : null,
  "groupId" : "fe6104e670e647279c8f4ef56265bbe6",
  "name" : "查询待拉取预填数据列表",
  "createTime" : 1748935863889,
  "updateTime" : null,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : null,
  "path" : "/listDlqYtsj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select
    a.sbrwuuid,
	a.djxh,
	date_format(a.skssqq, '%Y-%m-%d') as skssqq,
	date_format(a.skssqz, '%Y-%m-%d') as skssqz,
	b.businessclob as qcxx
from
	znsb_nssb_ysbjgb_qysdsyjsb a
left join znsb_nssb_qcxx b on
	a.sbrwuuid = b.sbrwuuid
	and b.yxbz = 'Y'
	and b.clcgbz = 'Y'
where
	a.xczxsj <= SYSDATE()
"""
return db.nssb.select(sql)