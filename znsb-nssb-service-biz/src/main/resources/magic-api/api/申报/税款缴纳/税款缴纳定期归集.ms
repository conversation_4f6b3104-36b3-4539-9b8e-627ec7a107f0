{
  "properties" : { },
  "id" : "21e084ed0f684031a6dd1ec6dc51d713",
  "script" : null,
  "groupId" : "56aab11323c44904aee03244081a6715",
  "name" : "税款缴纳定期归集",
  "createTime" : 1723530868398,
  "updateTime" : 1718952713315,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/cxzsyjsf",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"msg\": \"success\",\n    \"data\": [\n        {\n            \"nsrsbh\": \"91341024151783006H\",\n            \"djxh\": 10213410000000135000,\n            \"xzqhszDm\": \"340000\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"11111\",\n            \"djxh\": null,\n            \"xzqhszDm\": \"1111\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"123456789123456789\",\n            \"djxh\": null,\n            \"xzqhszDm\": \"11011\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL707CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"test1\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"tes2\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"9111000071093573CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": \"110101\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB001CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"340000\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB010CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"430000\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB004CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"123456789123\",\n            \"djxh\": null,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB006CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL712CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB002CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91610112333643573D\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL70BCS\",\n            \"djxh\": 10210000000788572,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL70BCS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91610133MA6TXHHD53\",\n            \"djxh\": 10116101000051606000,\n            \"xzqhszDm\": \"610112\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"9144030070846113CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL711CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL702CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL703CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB005CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"111\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"110101\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL709CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91110000MA001HYKCS\",\n            \"djxh\": 10111525000001000000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB003CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"147258\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"110101\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL710CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB009CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL706CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB011CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91340100790122917R\",\n            \"djxh\": 1021000000078857,\n            \"xzqhszDm\": \"340000\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"9144000010000589CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL705CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL701CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"1223\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"222222\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA2SB008CS\",\n            \"djxh\": 10111525000003330000,\n            \"xzqhszDm\": \"152500\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"9111000010001770CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91310000MA1FL708CS\",\n            \"djxh\": 10111525000001030000,\n            \"xzqhszDm\": null,\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91610000773814479K\",\n            \"djxh\": 10116101000051860000,\n            \"xzqhszDm\": \"440000\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        },\n        {\n            \"nsrsbh\": \"91340200MA2U59YU1Q\",\n            \"djxh\": 10213402000001133000,\n            \"xzqhszDm\": \"340000\",\n            \"nssbrqq\": \"2024-06-20\",\n            \"nssbrqz\": \"2024-06-20\"\n        }\n    ],\n    \"timestamp\": 1718952695174,\n    \"requestTime\": 1718952695013,\n    \"executeTime\": 161\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "1",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "msg",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "data",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Array",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ {
        "name" : "",
        "value" : "",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ {
          "name" : "nsrsbh",
          "value" : "91340200MA2U59YU1Q",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "djxh",
          "value" : "10213402000001133000",
          "description" : "",
          "required" : false,
          "dataType" : "Long",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "xzqhszDm",
          "value" : "340000",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "nssbrqq",
          "value" : "2010-01-01",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "nssbrqz",
          "value" : "2099-12-31",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        } ]
      } ]
    }, {
      "name" : "timestamp",
      "value" : "1717746609296",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "requestTime",
      "value" : "1717746609156",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "executeTime",
      "value" : "140",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const nssbrq = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");

const sql = """
select nsrsbh, djxh, xzqhsz_Dm from znsb_mhqx_jgxxb
"""

return db.mhzc.select(sql).each(item=>{item.put('nssbrqq',nssbrq);item.put('nssbrqz',nssbrq);});
//return db.mhzc.select(sql).each(item=>{item.put('nssbrqq','2010-01-01');item.put('nssbrqz','2099-12-31');});

