{
  "properties" : { },
  "id" : "fc480b85798b458a8206560adb4221ab",
  "script" : null,
  "groupId" : "36d8386a0afc45e4af3d274974a537b0",
  "name" : "查询待拉取预填数据列表",
  "createTime" : null,
  "updateTime" : 1749459556175,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "admin",
  "path" : "/listDlqYtsj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select
    a.sbrwuuid,
	a.djxh,
	date_format(a.skssqq, '%Y-%m-%d') as skssqq,
	date_format(a.skssqz, '%Y-%m-%d') as skssqz,
	b.businessclob as qcxx
from
	znsb_nssb_ysbjgb_zzsxgmnsr a
left join znsb_nssb_qcxx b on
	a.sbrwuuid = b.sbrwuuid
	and b.yxbz = 'Y'
	and b.clcgbz = 'Y'
where
	a.xczxsj <= SYSDATE()
"""
return db.nssb.select(sql)