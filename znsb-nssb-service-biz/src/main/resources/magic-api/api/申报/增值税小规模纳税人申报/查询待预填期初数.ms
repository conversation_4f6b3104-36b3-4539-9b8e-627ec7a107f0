{
  "properties" : { },
  "id" : "fd0b5f41a8c243288c296793fa569000",
  "script" : null,
  "groupId" : "36d8386a0afc45e4af3d274974a537b0",
  "name" : "查询待预填期初数",
  "createTime" : null,
  "updateTime" : 1749436943052,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "admin",
  "path" : "/listDytQcxx",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'cn.hutool.core.date.DateUtil' as DateUtil;
import 'java.util.Date' as Date;

const sbny = DateUtil.format(new Date(), "yyyyMM");

const sql = """
select
	a.sbrwuuid,
	a.djxh,
	a.nsrsbh,
	date_format(a.skssqq, '%Y-%m-%d') as skssqq,
	date_format(a.skssqz, '%Y-%m-%d') as skssqz,
	b.businessclob as qcxx
from
	znsb_nssb_sbrw a
left join znsb_nssb_qcxx b on
	a.sbrwuuid = b.sbrwuuid
	and b.yxbz = 'Y'
	and b.clcgbz = 'Y'
where
	a.yzpzzl_dm = 'BDA0610611'
	and a.sbny =#{sbny} 
	and a.nsrsbzt_dm in ('01', '02', '06', '08', '99')
"""
return db.nssb.select(sql)