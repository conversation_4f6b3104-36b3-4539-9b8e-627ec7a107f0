{
  "properties" : { },
  "id" : "1c5fa29a92cc472c93820558d6e5f6d2",
  "script" : null,
  "groupId" : "2b08857797394158a8e7aa6dba56a96e",
  "name" : "房产税税源归集",
  "createTime" : null,
  "updateTime" : 1728456712697,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/fcssyxxgj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select a.nsrsbh, a.djxh, a.xzqhsz_dm, b.zgswskfj_dm from znsb_mhqx_jgxxb a, znsb_mhzc_qyjbxxmx b where a.djxh = b.djxh
"""
return db.mhzc.select(sql)