{
  "properties" : { },
  "id" : "6f7057c0ba244106aea8eaeee633a59f",
  "script" : null,
  "groupId" : "665c2bf53f4b42d38c6c17a70972cafe",
  "name" : "乐企算税汇总",
  "createTime" : null,
  "updateTime" : 1743477297918,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/lqsshz",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select t1.nsrsbh, t1.djxh, t1.xzqhsz_Dm,
concat(date_format(NOW() - INTERVAL 1 month,'%Y-%m'),'-01') as skssqq,
date_format(str_to_date(concat(date_format(NOW(),'%Y-%m'),'-01'),'%Y-%m-%d')- INTERVAL 1 day,'%Y-%m-%d') as skssqz
from znsb_mhqx_jgxxb t1  JOIN znsb_mhzc_qyjbxxmx t2 on t1.djxh = t2.djxh and t2.kqccsztdjbz = 'N' where t1.yxbz = 'Y'
"""
return db.mhzc.select(sql)