{
  "properties" : { },
  "id" : "********************************",
  "script" : null,
  "groupId" : "113808ccfe104ae49e4d8d5f008b1bad",
  "name" : "4.2.2获取请求参数",
  "createTime" : 1723551830287,
  "updateTime" : 1679918577468,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : null,
  "path" : "/param",
  "method" : "POST",
  "parameters" : [ {
    "name" : "name",
    "value" : "magic-script",
    "description" : "",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "{\r\n\t\"data\" : {\r\n        \"id\" : 123456\r\n    }\r\n}",
  "headers" : [ {
    "name" : "token",
    "value" : "123456",
    "description" : "",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"message\": \"success\",\n    \"data\": {\n        \"Request\": \"magic-script\",\n        \"Header\": \"123456\",\n        \"RequestBody\": 123456,\n        \"Path\": null\n    },\n    \"timestamp\": 1615816974752,\n    \"executeTime\": 3\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import request;
return {
    'Request': name,
    'Header' : header.token,
    'RequestBody': body.data.id,
    'Path' : path.id,
    'file' : request.getFile('file')
}