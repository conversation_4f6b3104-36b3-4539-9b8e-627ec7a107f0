{
  "properties" : { },
  "id" : "62fe9e49f6f54b67a0aa351baf8d0107",
  "script" : null,
  "groupId" : "0859f600cd3848c993220c10ec7ccdf5",
  "name" : "4.3.4lambda循环list",
  "createTime" : 1723551830300,
  "updateTime" : 1679918617710,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : null,
  "path" : "/lambda/list",
  "method" : "GET",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
var list = [1,2,3,4,5,6,7,8,9,10]
var sum = 0;
list.each(it => sum+= it + 1)
return sum