{
  "properties" : { },
  "id" : "beccc26e2bd24c569546b9e53449915d",
  "script" : null,
  "groupId" : "113808ccfe104ae49e4d8d5f008b1bad",
  "name" : "4.2.5生成图片",
  "createTime" : 1723551830293,
  "updateTime" : 1679918589909,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : null,
  "path" : "/image",
  "method" : "GET",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "{\r\n\t\r\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "iVBORw0KGgoAAAANSUhEUgAAAMgAAABFCAIAAACAFD7PAAAGJklEQVR42u3cTahNURQA4FMyeJSQ8jfwRo+JYohM0CsZSJm+gdHrJfH01DMxkr+SiagnQwPFjNRLvfwkUlIGxIAyMlAmBpSupV3Hbv+svfY++/ecvVuje885t+79Wmvtdc+9zaiuugKspuP5Sz++QtT3sa4Ki7pOfd4GUYmkgdXjVWFVWINbY+NTLNxOX1y5CaIwWO/fPIPI/IP5cme+UElGTyu2XoWosFLaypwXM7Tm22MI+lkUWLUUDouXMi3Zwqo9VuVlUeAqrMrLAlPdFZa0Zp7ez4pXj9MSFdbC2QMQvbG17u4NiPi8+iFpauIYhM+MxXiVjgxsdYFlxauXOck/rN4g61gTcV4lSjo4N5ljj1WiMF+2KGnp+eJaJOgv5HaRM7NPIBBSMVU5Nu9lpTFnW0pJSHFMBQsnFTlRedsV8sj2TrwWoixbeE6antwO0fKyhUW0Rb8C84STSqXKz7ihZaQTlrMteqvEw1KmrjiwiJ6M5e/Bw9sQ+cKSsxQvLKtayWz53cHxvELDInrCE9XOjUsssoalVKVDlspThFkA42WEZbSFeIIg0kQSVauqjYJhJWn5dZIgaY3/vBnoRSm8rGC1nug5D++oZFjhbDXeVQkHIC3/+ObVENHSEpCCYLZC8GKfPc6LcgU+RdFPJw4USoVldQUGyy2N2RY4hkmw5VcYL6Dtveg4hBRldS6QInIZBCyr8Zhbq/Tj+gcInlE4W7IDZfbSDaLkqkeEJasqDxaxneq+GK/uTbcOlmArECz2oMBLN4hSnovDastfF1iBbP2HNfdoFiItLGVa6tjyK1WF6Ld0sFic3vcIQjc4UALCYfEdlZWV8mA5OyOmJYd9JaKKtxUOVvs4g6WbRVnBkvt0qzwUG5aX4UK0gTsFmVFVays0LL6FUn7bSIfVfUYVpxpaN+84pj+H/kXkb3KUwnSMlE95sYXPNgUfyMhed0F8oJBbNXTcFRphJRmy82lMp0oJa+TjBhvZkzGZ4dtGQRU+x+peDS+9fQGRHSzh8VG6xazIyJBk1s7lO8IS8pMRlpKXfBZTZZyvdqyGWcPKYcluhFqpg+VsCyl5RljyTJV/ipU/4uA+q2rYN1hIa4XUSv4KVraQQZTgAN8zKnnxpNxgJdwbNsNRJRzDCxOuY7SlvCnKCyx+oOBw400+e8P+wKKowgslv69U2sJvsnOApVOla+19wVKqqrDcVemOVO4reVuUm+yMCYZ/6uS73caBgvyFowMsJZcKy7MqvHOXhxdgi37fJh0WqJIPlmdUut6riy0dqQrLYrzeBVbrCWwRv0pyg3Vi5haEcvKpvF3CDRY9Mpq855mu6D0W3kUJzRaCjNJry+mKwdIdjI/su8BSHpwyY5WSrnR6dE8hJU+3SRSEUWAJqvAZFXJBnJctrBAfTcGw6KqQIP6aCh9A8GkMgcWrokw+jVIRXtF6qb7BwrnMr7oPQfFEv2fGONxinz1SK9udIHHySZxj6XilIlUwLGPJQ2C1nhxuH8VtyQ54YUyVcp7eERalOMZfTf9UKQ/mUxRx3GBrC3EAqoCX7TzdYfKeD6/CYNkOF9qu3OFEW1s4rHagIDdkHmHlw6snGQsefzl9BAIZHBjH7r5qorCQG/SC/oI3Aq+L7z9BeBuQjnK670qAZdzleYFFt0X8s5dwPxMPyss/rAwX8bsXX7CMtliiant2+gohLH5lbPrhif43LB5hGROVg6pwaSxy49WUTqp7i5anqkDIovFqCvXkQMpj827s0/2q8l4rI/BqhuApDiy+Tw8Ky1caC8rLEdahLZchci55MWEJA4U4qnwhC8QrGaz1u6YhQqeoCD1Wqn+PNSJLyytZKURghfCECPOVqEKv+cPnIYK+hEdeGfVYgVKUkVcpiSoCLI+8mnxIFbQzzbD8xeF1YcceCEdYx1e+guhrivLbpw+NFwXWlWtHIRLAKtFTholq//YNENF40Q/Wwqolr4hEFRNWpj1WuSlqOB1VMbA+/l4OUbSnAXZUucMCTAxW0W9NJZULrNJLHl3Vr3tjEFVPcFi98UQsfxVWcFjZklp2ZwGilr8+7woLgmVc575vgqhuhgsr0KqwKqy6Uq6/LRRAFiT5gZMAAAAASUVORK5CYII=",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import 'java.awt.image.BufferedImage' as BufferedImage;
import 'java.awt.Color' as Color;
import 'java.awt.Font' as Font;
import 'java.io.ByteArrayOutputStream' as ByteArrayOutputStream;
import 'java.util.Random' as Random;
import 'javax.imageio.ImageIO' as ImageIO;
import response;
import log;

var width = 200;
var height = 69;
var image = new BufferedImage(width,height,BufferedImage.TYPE_INT_RGB);
var graphics = image.getGraphics();
graphics.setColor(Color.WHITE);
graphics.fillRect(0,0,width,height);
graphics.setFont(new Font("微软雅黑", Font.BOLD, 40));
var letter = '123456789abcdefghijklmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ';
var random = new Random();
var randomColor = ()=>new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256));
var x = 10;
var code = '';
for (i in range(0, 3)){ //验证码
    graphics.setColor(randomColor());
    var degree = random.nextInt() % 30;
    var ch = letter.charAt(random.nextInt(letter.length()));
    code = code + ch;
    graphics.rotate(degree * 3.1415926535 / 180, x, 45);
    graphics.drawString(ch + '', x, 45);
    graphics.rotate(-degree * 3.1415926535 / 180, x, 45);
    x = x + 48;
}
log.info('生成的验证码:{}',code)
for (i in range(0, 6)) {    //干扰线
    graphics.setColor(randomColor());
    graphics.drawLine(random.nextInt(width), random.nextInt(height),random.nextInt(width), random.nextInt(height));
}

for(i in range(0, 30)){  //噪点
    graphics.setColor(randomColor());
    graphics.fillRect(random.nextInt(width), random.nextInt(height), 2,2);

}
graphics.dispose();
var baos = new ByteArrayOutputStream();
ImageIO.write(image,"png",baos);
baos.flush();
baos.close();
return response.image(baos.toByteArray(),'image/png');
