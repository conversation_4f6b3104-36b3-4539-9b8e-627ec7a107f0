{
  "properties" : { },
  "id" : "e0d200eb516e4f4294bf8d0080d8f204",
  "script" : null,
  "groupId" : "9a9356c555f74514b08c4fb667e97040",
  "name" : "4.1.5各类运算符",
  "createTime" : 1723551830278,
  "updateTime" : 1680938094611,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : null,
  "path" : "/operator",
  "method" : "GET",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"message\": \"success\",\n    \"data\": {\n        \"+\": 579,\n        \"-\": -333,\n        \"-a\": -123,\n        \"*\": 56088,\n        \"/\": 0,\n        \"%\": 123,\n        \"++\": 123,\n        \"--\": 124,\n        \">\": false,\n        \">=\": false,\n        \"<\": true,\n        \"<=\": true,\n        \"==\": false,\n        \"===\": false,\n        \"!=\": true,\n        \"!==\": true,\n        \"&&\": 456,\n        \"||\": 123,\n        \">>\": -2,\n        \">>>\": 2,\n        \"<<\": 4,\n        \"^\": 3,\n        \"&\": 0,\n        \"|\": 3\n    },\n    \"timestamp\": 1632190932561,\n    \"executeTime\": 7\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "1",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "data",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ {
        "name" : "+",
        "value" : "579",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "-",
        "value" : "-333",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "-a",
        "value" : "-123",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "*",
        "value" : "56088",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "/",
        "value" : "0",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "%",
        "value" : "123",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "++",
        "value" : "123",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "--",
        "value" : "124",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : ">",
        "value" : "false",
        "description" : "",
        "required" : false,
        "dataType" : "Boolean",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : ">=",
        "value" : "false",
        "description" : "",
        "required" : false,
        "dataType" : "Boolean",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "<",
        "value" : "true",
        "description" : "",
        "required" : false,
        "dataType" : "Boolean",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "<=",
        "value" : "true",
        "description" : "",
        "required" : false,
        "dataType" : "Boolean",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "==",
        "value" : "false",
        "description" : "",
        "required" : false,
        "dataType" : "Boolean",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "===",
        "value" : "false",
        "description" : "",
        "required" : false,
        "dataType" : "Boolean",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "!=",
        "value" : "true",
        "description" : "",
        "required" : false,
        "dataType" : "Boolean",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "!==",
        "value" : "true",
        "description" : "",
        "required" : false,
        "dataType" : "Boolean",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "&&",
        "value" : "456",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "||",
        "value" : "123",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : ">>",
        "value" : "-2",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : ">>>",
        "value" : "2",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "<<",
        "value" : "4",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "^",
        "value" : "3",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "&",
        "value" : "0",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "|",
        "value" : "3",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      } ]
    }, {
      "name" : "timestamp",
      "value" : "1632190932561",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "executeTime",
      "value" : "7",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
var a = 123;    // 定义int型变量，定义变量只能使用var。var可以省略
var b = 456;
return {
    '+': a + b,
    '-': a - b,
    '-a' : -a,
    '*': a * b,
    '/': a / b,
    '%': a % b,
    '++': a++,
    '--': a--,
    '>': a > b,
    '>=': a >= b,
    '<': a < b,
    '<=': a <= b,
    '>=': a >= b,
    '==': a == b,
    '===': a === b, // 与JS类似，需要类型一致，值一致才返回true
    '!=': a != b,
    '!==': a !== b, // 与JS类似，需类型不一致或 值不一致 才返回true 与 === 相反
    '&&': a && b,   // 与JS类似，如：true && true --> true ，123 && 456 --> 456 ， null && 123 --> 456
    '||': a || b,    // 与JS类似，如：false && true --> true ，123 || 456 --> 123 ，null || 123 --> 123
    '>>':  8 >> 2,
    '>>>': 8 >>> 2,
    '<<' : 1 << 2,
    '^' : 1 ^ 2,
    '&': 1 & 2,
    '|': 1 | 2
}