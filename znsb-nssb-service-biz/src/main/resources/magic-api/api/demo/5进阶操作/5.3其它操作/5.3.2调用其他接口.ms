{
  "properties" : { },
  "id" : "e51d7756285f4e3c8ec28c7fb94fdc16",
  "script" : null,
  "groupId" : "1ee3e804ddc145d3892cb0eb0c409302",
  "name" : "5.3.2调用其他接口",
  "createTime" : 1723551830332,
  "updateTime" : 1679918857089,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : null,
  "path" : "/call",
  "method" : "GET",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import '@get:/base/module/assert' as test;
var id = '1';
var message = 'hello'
return test();