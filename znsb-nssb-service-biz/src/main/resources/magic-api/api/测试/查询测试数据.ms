{
  "properties" : { },
  "id" : "55d83fa479ab45c4946eb17b7cdf742b",
  "script" : null,
  "groupId" : "26b1720b48624771abce6305a3c7cd67",
  "name" : "查询测试数据",
  "createTime" : 1723530868354,
  "updateTime" : 1717638893682,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/test002",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"msg\": \"success\",\n    \"data\": [\n        {\n            \"jguuid1\": \"91340100790122917R\",\n            \"jgmc1\": \"安徽千户集团测试纳税人-勿删\",\n            \"nsrsbh\": \"91340100790122917R\",\n            \"shxydm\": \"91340100790122917R\",\n            \"ssjswjgDm\": \"13400000000\",\n            \"djxh\": null,\n            \"sjJguuid\": null,\n            \"bz\": \"测试企业-勿删\",\n            \"yxbz\": \"Y\",\n            \"ywqdDm\": \"ZNSB.MHZC.HD\",\n            \"lrrq\": \"2024-05-10 11:36:28\",\n            \"xgrq\": \"2024-05-10 11:36:31\",\n            \"sjcsdq\": \"00000000000\",\n            \"sjgsdq\": \"00000000000\",\n            \"xgrsfid\": null,\n            \"lrrsfid\": null,\n            \"sjtbSj\": null,\n            \"xzqhszDm\": \"340000\"\n        }\n    ],\n    \"timestamp\": 1715423132760,\n    \"requestTime\": 1715423132706,\n    \"executeTime\": 54\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
const sql = """
select * from znsb_mhqx_jgxxb where where yxbz = 'Y' and TABLE_NAME='DM_SB_NCPKCBZCPLX_Backup'
"""
return db.mhzc.select(sql)
