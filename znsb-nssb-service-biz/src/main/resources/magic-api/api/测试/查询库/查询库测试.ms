{
  "properties" : { },
  "id" : "0a8181cf6e12455081fb37314368c3ff",
  "script" : null,
  "groupId" : "c141fc7cc2ec4d64ab24e5854b20161e",
  "name" : "查询库测试",
  "createTime" : 1723530868359,
  "updateTime" : 1717058117393,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/test001",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"msg\": \"success\",\n    \"data\": {\n        \"fpList\": [\n            {\n                \"uuid\": \"fp00320240424094225\",\n                \"sszq\": 202404,\n                \"djxh\": 333,\n                \"nsrsbh\": \"333\",\n                \"nsrmc\": \"69856\",\n                \"fppzDm\": \"1\",\n                \"fpdm\": null,\n                \"fpdmhm\": \"666\",\n                \"fphm\": \"fp003\",\n                \"je\": 100,\n                \"se\": 99,\n                \"jshj\": 200,\n                \"kprq\": \"2024-04-24 09:42:25\",\n                \"gmfnsrsbh\": \"556\",\n                \"gmfmc\": \"44\",\n                \"tzlxDm\": \"1\",\n                \"tbbz\": \"Y\",\n                \"yxbz\": \"Y\",\n                \"lrrswjgDm\": null,\n                \"lrrq\": null,\n                \"lrrsfid\": null,\n                \"xgrq\": null,\n                \"xgrsfid\": null,\n                \"ywqdDm\": null,\n                \"sjcsdq\": null,\n                \"sjgsdq\": null,\n                \"sjtbSj\": \"2024-05-24 19:02:55\"\n            },\n            {\n                \"uuid\": \"fp00320240429103154\",\n                \"sszq\": 202404,\n                \"djxh\": 333,\n                \"nsrsbh\": \"333\",\n                \"nsrmc\": \"333请求\",\n                \"fppzDm\": \"1\",\n                \"fpdm\": null,\n                \"fpdmhm\": \"666\",\n                \"fphm\": \"fp003\",\n                \"je\": 100,\n                \"se\": 99,\n                \"jshj\": 200,\n                \"kprq\": \"2024-04-29 10:31:54\",\n                \"gmfnsrsbh\": \"556\",\n                \"gmfmc\": \"44\",\n                \"tzlxDm\": \"1\",\n                \"tbbz\": \"Y\",\n                \"yxbz\": \"Y\",\n                \"lrrswjgDm\": null,\n                \"lrrq\": null,\n                \"lrrsfid\": null,\n                \"xgrq\": null,\n                \"xgrsfid\": null,\n                \"ywqdDm\": null,\n                \"sjcsdq\": null,\n                \"sjgsdq\": null,\n                \"sjtbSj\": \"2024-05-29 11:45:11\"\n            }\n        ],\n        \"fpmxList\": [\n            {\n                \"uuid\": \"b4efd32afc6b7061329b4e82625789b9\",\n                \"zbuuid\": \"333111\",\n                \"sszq\": 202404,\n                \"djxh\": 333,\n                \"nsrsbh\": \"333\",\n                \"nsrmc\": \"333\",\n                \"fppzDm\": \"1\",\n                \"fpdm\": null,\n                \"fpdmhm\": \"666\",\n                \"fphm\": \"888\",\n                \"kprq\": \"2024-05-24 19:03:05\",\n                \"gmfnsrsbh\": \"311\",\n                \"gmfnsrmc\": \"311\",\n                \"hwhyslwfwmc\": \"311\",\n                \"sphfwssflhbbm\": \"311\",\n                \"ggxh\": null,\n                \"fpspsl\": \"22\",\n                \"fpspdj\": \"231\",\n                \"dw\": \"Yuan\",\n                \"je\": 2563,\n                \"se\": 225,\n                \"jshj\": 2788,\n                \"kce\": 10,\n                \"zsfsDm1\": \"1\",\n                \"zsxmDm\": \"1\",\n                \"jzjtbz\": \"N\",\n                \"tzlxDm\": \"1\",\n                \"tbbz\": \"Y\",\n                \"yxbz\": \"Y\",\n                \"lrrswjgDm\": null,\n                \"lrrq\": \"2024-05-24 19:03:05\",\n                \"lrrsfid\": null,\n                \"xgrq\": \"2024-05-24 19:03:05\",\n                \"xgrsfid\": null,\n                \"ywqdDm\": \"222\",\n                \"sjcsdq\": \"6352\",\n                \"sjgsdq\": \"2222\",\n                \"sjtbSj\": \"2024-05-24 19:03:05\",\n                \"sl1\": 111.1\n            },\n            {\n                \"uuid\": \"d203eafdf45665f9534a7dcc695f9ff5\",\n                \"zbuuid\": \"20240529001\",\n                \"sszq\": 202404,\n                \"djxh\": 333,\n                \"nsrsbh\": \"333\",\n                \"nsrmc\": \"333请求\",\n                \"fppzDm\": \"1\",\n                \"fpdm\": null,\n                \"fpdmhm\": \"666\",\n                \"fphm\": \"888\",\n                \"kprq\": \"2024-05-29 11:45:22\",\n                \"gmfnsrsbh\": \"311\",\n                \"gmfnsrmc\": \"311\",\n                \"hwhyslwfwmc\": \"311\",\n                \"sphfwssflhbbm\": \"311\",\n                \"ggxh\": null,\n                \"fpspsl\": \"22\",\n                \"fpspdj\": \"231\",\n                \"dw\": \"Yuan\",\n                \"je\": 2563,\n                \"se\": 225,\n                \"jshj\": 2788,\n                \"kce\": 10,\n                \"zsfsDm1\": \"1\",\n                \"zsxmDm\": \"1\",\n                \"jzjtbz\": \"N\",\n                \"tzlxDm\": \"1\",\n                \"tbbz\": \"Y\",\n                \"yxbz\": \"Y\",\n                \"lrrswjgDm\": null,\n                \"lrrq\": \"2024-05-29 11:45:22\",\n                \"lrrsfid\": null,\n                \"xgrq\": \"2024-05-29 11:45:22\",\n                \"xgrsfid\": null,\n                \"ywqdDm\": \"222\",\n                \"sjcsdq\": \"6352\",\n                \"sjgsdq\": \"2222\",\n                \"sjtbSj\": \"2024-05-29 11:45:22\",\n                \"sl1\": 111.1\n            }\n        ]\n    },\n    \"timestamp\": 1716961690610,\n    \"requestTime\": 1716961690200,\n    \"executeTime\": 410\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "-1",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "msg",
      "value" : "系统内部出现错误",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "data",
      "value" : "null",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1716959569908",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "requestTime",
      "value" : "1716959569897",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "executeTime",
      "value" : "11",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import magic;
import log;

const fpList = magic.invoke('/test/queryFpxx', null)


const fpmxSQL = """
select * from znsb_tzzx_bz_xxfpmx
"""
log.info("123")
const fpmxList = db.cxk.select(fpmxSQL)
return {fpList, fpmxList}
