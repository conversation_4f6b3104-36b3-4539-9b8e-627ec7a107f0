{
  "properties" : { },
  "id" : "********************************",
  "script" : null,
  "groupId" : "71da65a4dcbd4e6eb3fa9546545e0574",
  "name" : "获取清册",
  "createTime" : 1723530868344,
  "updateTime" : 1715951153341,
  "lock" : null,
  "createBy" : "admin",
  "updateBy" : "admin",
  "path" : "/getqc",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"msg\": \"success\",\n    \"data\": [\n        {\n            \"bsssq\": \"2024-02\",\n            \"nsrsbh\": \"91340100790122917R\",\n            \"xzqhszDm\": \"340000\",\n            \"bsuuid\": \"f5f39c1fe4e5448ea87f9ebab7450b9f\"\n        },\n        {\n            \"bsssq\": \"2024-03\",\n            \"nsrsbh\": \"91340100790122917R\",\n            \"xzqhszDm\": \"340000\",\n            \"bsuuid\": \"5822520c8133443bb539ef383055e88e\"\n        },\n        {\n            \"bsssq\": \"2024-01\",\n            \"nsrsbh\": \"91340100790122917R\",\n            \"xzqhszDm\": \"340000\",\n            \"bsuuid\": \"f440ace0df6f44378de36a58d6aeeb45\"\n        },\n        {\n            \"bsssq\": \"2024-04\",\n            \"nsrsbh\": \"91340100790122917R\",\n            \"xzqhszDm\": \"340000\",\n            \"bsuuid\": \"02fc51d895eb4448b8ccc354503816e8\"\n        }\n    ],\n    \"timestamp\": 1715907958194,\n    \"requestTime\": 1715907958184,\n    \"executeTime\": 10\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "-1",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "msg",
      "value" : "系统内部出现错误",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "data",
      "value" : "null",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1715404578736",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "requestTime",
      "value" : "1715404578718",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "executeTime",
      "value" : "18",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
const sql = """
select bsssq,nsrsbh,xzqhsz_dm,bsuuid from znsb_nssb_qhjtjcsssj_bsqc where bsuuid is not null
"""
return db.nssb.select(sql)