package com.css.znsb.gjss.service.srzz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.framework.mybatis.core.query.QueryWrapperX;
import com.css.znsb.gjss.constants.enums.JtbmEnum;
import com.css.znsb.gjss.mapper.srzz.jyk.GjssZnsbTzzxSrzzbMapper;
import com.css.znsb.gjss.mapper.srzz.jyk.GjssZnsbTzzxSrzzbZjbMapper;
import com.css.znsb.gjss.mapper.srzz.jyk.GjssZnsbTzzxSrzzmxbMapper;
import com.css.znsb.gjss.mapper.srzz.jyk.ZnsbTzzxSrzdyhzbMapper;
import com.css.znsb.gjss.pojo.domain.srzz.ZnsbTzzxSrmxbDO;
import com.css.znsb.gjss.pojo.domain.srzz.ZnsbTzzxSrzdyhzbDO;
import com.css.znsb.gjss.pojo.domain.srzz.ZnsbTzzxSrzzbDO;
import com.css.znsb.gjss.pojo.domain.srzz.ZnsbTzzxSrzzbZjbDO;
import com.css.znsb.gjss.service.srzz.ZnsbTzzxSrzzbService;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.tzzx.util.TzzxXgmnsrUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class ZnsbTzzxSrzzbServiceImpl implements ZnsbTzzxSrzzbService {

    @Resource
    private GjssZnsbTzzxSrzzbMapper gjssZnsbTzzxSrzzbMapper;

    @Resource
    private GjssZnsbTzzxSrzzbZjbMapper gjssZnsbTzzxSrzzbZjbMapper;

    @Resource
    private GjssZnsbTzzxSrzzmxbMapper gjssZnsbTzzxSrmxbMapper;

    @Resource
    private ZnsbTzzxSrzzbServiceImpl znsbTzzxSrzzbServiceImpl;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private ZnsbTzzxSrzdyhzbMapper srzdyhzbMapper;

    public List<String> generateRecentPeriods(int n) {
        if (n <= 0) {
            n = 1;//返回当前月
        }
        List<String> periods = new ArrayList<>();
        YearMonth currentYearMonth = YearMonth.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        for (int i = 0; i < n; i++) {
            YearMonth period = currentYearMonth.minusMonths(i);
            periods.add(period.format(formatter));
        }
        return periods;
    }

    @Override
    public void execute(long limit,int startDays,String djxh,String sszq) {

        List<String> sszqlist = CollectionUtil.newArrayList();
        if(GyUtils.isNull(djxh) && GyUtils.isNull(sszq)){
            sszqlist = generateRecentPeriods(startDays);
        }else{
            sszqlist.add(sszq);
        }

        log.info("收入总账明细汇总任务sszqlist{}:",JsonUtils.toJson(sszqlist));
        final String jtbmSys = CacheUtils.getXtcs("GY00000001");
        QueryWrapperX<ZnsbTzzxSrmxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().eq(ZnsbTzzxSrmxbDO::getZzscbz, "N")
                .in(ZnsbTzzxSrmxbDO::getSszq,sszqlist)
                .eq(GyUtils.isNotNull(djxh), ZnsbTzzxSrmxbDO::getDjxh,djxh);
        if(JtbmEnum.QS.getJtbm().equals(jtbmSys)){//泉膳
            //禅道2269
            //wrapperX.lambda().and(wrapper -> {wrapper.isNull(ZnsbTzzxSrmxbDO::getWbzlmc).or().notLike(ZnsbTzzxSrmxbDO::getWbzlmc, "%乐企调账%").or().notLike(ZnsbTzzxSrmxbDO::getWbzlmc, "%系统生成尾差凭证%");});
            wrapperX.lambda().and(wrapper ->
                    wrapper.isNull(ZnsbTzzxSrmxbDO::getWbzlmc)
                            .or(w -> w.notLike(ZnsbTzzxSrmxbDO::getWbzlmc, "%乐企调账%")
                                    .notLike(ZnsbTzzxSrmxbDO::getWbzlmc, "%系统生成尾差凭证%"))
            );
        }else{//森马等之前的
            wrapperX.lambda().and(wrapper -> {wrapper.isNull(ZnsbTzzxSrmxbDO::getPztt).or().ne(ZnsbTzzxSrmxbDO::getPztt, "乐企调账");});
        }

        wrapperX.lambda().last("limit " + limit);

        List<ZnsbTzzxSrmxbDO> data = gjssZnsbTzzxSrmxbMapper.selectList(wrapperX);

        //之前的暂时注释
        /*List<ZnsbTzzxSrmxbDO> data = gjssZnsbTzzxSrmxbMapper.selectList(new QueryWrapperX<ZnsbTzzxSrmxbDO>().lambda()
                .eq(ZnsbTzzxSrmxbDO::getZzscbz, "N")
                .in(ZnsbTzzxSrmxbDO::getSszq,sszqlist)
                .eq(GyUtils.isNotNull(djxh), ZnsbTzzxSrmxbDO::getDjxh,djxh)
                .and(wrapper -> {wrapper.isNull(ZnsbTzzxSrmxbDO::getPztt).or().ne(ZnsbTzzxSrmxbDO::getPztt, "乐企调账");})
                .last("limit " + limit));*/

        if (GyUtils.isNull(data)){
            return;
        }

        //森马 将小规模人所属账期 自动归属所在季度的第一个月汇总处理
        if(JtbmEnum.SM.getJtbm().equals(jtbmSys)){
            data.stream()
                    .filter(fpmx -> TzzxXgmnsrUtil.isXgmnsr(new BigDecimal("0.03"), fpmx.getDjxh(), fpmx.getNsrsbh()))
                    .peek(fpmx -> {
                        String original = fpmx.getSszq();
                        String newSszq = TzzxXgmnsrUtil.calculateQuarterFirstMonth(original);
                        fpmx.setSszq(newSszq);
                    }).collect(Collectors.toList());
        }

        Multimap<String, String> zjpid2mxbid = ArrayListMultimap.create();
        List<CompletableFuture<Boolean>> runningFuture = new ArrayList<>();
        int shardElementSize = (data.size()+5) / 5;
        Stream.iterate(0, n->n+1)
                .limit(5)
                .map(s -> data.stream()
                        .skip((long) s * shardElementSize)
                        .limit(shardElementSize)
                        .collect(Collectors.toList()))
                .forEach(shardData -> {
                    CompletableFuture<Boolean> result = CompletableFuture.supplyAsync(
                                    ()-> znsbTzzxSrzzbServiceImpl.handleMx(shardData, zjpid2mxbid))
                            .exceptionally(ex -> {
                                throw new RuntimeException(ex);
                            });
                    runningFuture.add(result);
                });
        try {
            CompletableFuture.allOf(runningFuture.toArray(new CompletableFuture[0])).get();
            //汇总子线程执行结果
            List<ZnsbTzzxSrzzbZjbDO> dos = gjssZnsbTzzxSrzzbZjbMapper.selectList();
            znsbTzzxSrzzbServiceImpl.handleZjb(dos, zjpid2mxbid);
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional
    public Boolean handleMx(List<ZnsbTzzxSrmxbDO> odsZnsbTzzxSrmxbDOList, Multimap<String, String> zjpid2mxbid) {
        if (CollectionUtils.isEmpty(odsZnsbTzzxSrmxbDOList)) {
            return true;
        }
        // 分组 LRZX，SRLX_DM，ZSFS_DM_1，ZSXM_DM_1，SL_1
        //20250711 去掉nsrmc分组+ item.getNsrmc()
        Map<String, List<ZnsbTzzxSrmxbDO>> znsbTzzxSrmxbDOGroups = odsZnsbTzzxSrmxbDOList
                .stream()
                .collect(Collectors.groupingBy(item -> item.getSszq() + item.getDjxh()
                        + item.getNsrsbh() + item.getGsh2() + item.getLrzx() + item.getSrlxdm() + item.getJsfsDm1()
                        + item.getZsxmDm() + item.getSl1() + item.getKmDm()));
        List<ZnsbTzzxSrzzbZjbDO> znsbTzzxSrzzbDOList = znsbTzzxSrmxbDOGroups.values().stream().map(item -> {
            ZnsbTzzxSrmxbDO odsZnsbTzzxSrmxbDO = item.get(0);
            ZnsbTzzxSrzzbZjbDO znsbTzzxSrzzbDO = new ZnsbTzzxSrzzbZjbDO();
            BeanUtils.copyProperties(odsZnsbTzzxSrmxbDO, znsbTzzxSrzzbDO);
            znsbTzzxSrzzbDO.setXssekmdm(odsZnsbTzzxSrmxbDO.getKmDm());
            znsbTzzxSrzzbDO.setXssekmmc(odsZnsbTzzxSrmxbDO.getKmmc());
            znsbTzzxSrzzbDO.setXssrkmdm(odsZnsbTzzxSrmxbDO.getKmDm());
            znsbTzzxSrzzbDO.setXssrkmmc(odsZnsbTzzxSrmxbDO.getKmmc());
            BigDecimal bbjeTotal = item.stream()
                    .map(mxbDO -> new BigDecimal(mxbDO.getBbje()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            //20250226 禅道1122 针对森马增加调整后的 销售额与税额 没有反算逻辑 不为空 任意值不等于0 直接写入
            //销售额合计
            BigDecimal xseTotal = item.stream()
                    .filter(mxbDO -> GyUtils.isNotNull(mxbDO.getXse()))
                    .map(mxbDO -> new BigDecimal(String.valueOf(mxbDO.getXse())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (GyUtils.isNotNull(xseTotal)){
                znsbTzzxSrzzbDO.setXse(xseTotal);
            }

            //税额合计
            BigDecimal seTotal = item.stream()
                    .filter(mxbDO -> GyUtils.isNotNull(mxbDO.getSe()))
                    .map(mxbDO -> new BigDecimal(String.valueOf(mxbDO.getSe())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (GyUtils.isNotNull(seTotal)){
                znsbTzzxSrzzbDO.setSe(seTotal);
            }

            // TODO 优化
            String jtbm = CacheUtils.getXtcs("GY00000001");//获取集团编码进行查询
            List<Map<String, Object>> caches = CacheUtils.getTableData("cs_tzzx_ywkmdzb");
            String srsebz = "";
            for (Map<String, Object> c : caches) {
                String kmbm = String.valueOf(c.get("kmbm"));
                String jtbmstr = String.valueOf(c.get("jtbm"));
                if (odsZnsbTzzxSrmxbDO.getKmDm().matches("^" + kmbm.replace("*", ".*")) && jtbm.equals(jtbmstr)) {
                    srsebz = String.valueOf(c.get("srsebz"));
                    break;
                }
            }
            if (Objects.equals(srsebz, "1")) {
                znsbTzzxSrzzbDO.setXssr(bbjeTotal.setScale(2, RoundingMode.HALF_UP));
                znsbTzzxSrzzbDO.setXsse(bbjeTotal.multiply(znsbTzzxSrzzbDO.getSl1()).setScale(2, RoundingMode.HALF_UP));
            } else if (Objects.equals(srsebz, "2")) {
                znsbTzzxSrzzbDO.setXsse(bbjeTotal.setScale(2, RoundingMode.HALF_UP));
                znsbTzzxSrzzbDO.setXssr(bbjeTotal.divide(znsbTzzxSrzzbDO.getSl1(), 2, RoundingMode.HALF_UP));
            }


            znsbTzzxSrzzbDO.setLrrq(new Date());
            znsbTzzxSrzzbDO.setXgrq(new Date());
            znsbTzzxSrzzbDO.setLrrsfid("ZNSB.GJSS.JOB");
            znsbTzzxSrzzbDO.setYwqdDm("SYS");
            znsbTzzxSrzzbDO.setSjcsdq("00000000000");
            znsbTzzxSrzzbDO.setSjgsdq("00000000000");
            znsbTzzxSrzzbDO.setSjtbSj(new Date());
            znsbTzzxSrzzbDO.setUuid(IdUtil.fastSimpleUUID());

            item.forEach(srmxbDO -> {
                synchronized (zjpid2mxbid) {
                    zjpid2mxbid.put(znsbTzzxSrzzbDO.getUuid(), srmxbDO.getUuid());
                }
            });

            return znsbTzzxSrzzbDO;
        }).collect(Collectors.toList());

        ZnsbTzzxSrmxbDO record = new ZnsbTzzxSrmxbDO();
        record.setZzscbz("Y");
        gjssZnsbTzzxSrmxbMapper.update(record, new UpdateWrapper<ZnsbTzzxSrmxbDO>().lambda()
                .in(ZnsbTzzxSrmxbDO::getUuid, odsZnsbTzzxSrmxbDOList.stream()
                        .map(ZnsbTzzxSrmxbDO::getUuid)
                        .collect(Collectors.toList())));
        return gjssZnsbTzzxSrzzbZjbMapper.insertBatch(znsbTzzxSrzzbDOList);
    }

    @Transactional
    public void handleZjb(List<ZnsbTzzxSrzzbZjbDO> znsbTzzxSrzzbZjbDOList, Multimap<String, String> zjpid2mxbid) {
        if (CollectionUtils.isEmpty(znsbTzzxSrzzbZjbDOList)) {
            return;
        }
        // 分组 LRZX，SRLX_DM，ZSFS_DM_1，ZSXM_DM_1，SL_1
        //20250711 去掉nsrmc分组+ item.getNsrmc()
        Map<String, List<ZnsbTzzxSrzzbZjbDO>> znsbTzzxSrzzbZjbDOGroups = znsbTzzxSrzzbZjbDOList
                .stream()
                .collect(Collectors.groupingBy(item -> item.getSszq() + item.getDjxh()
                        + item.getNsrsbh() + item.getGsh2() + item.getLrzx() + item.getSrlxdm() + item.getJsfsDm1()
                        + item.getZsxmDm() + item.getSl1() + item.getXssrkmdm()));

        Map<String, BigDecimal> srxgz = new HashMap<>();

        Multimap<String, String> zzid2zjbid = ArrayListMultimap.create();
        List<ZnsbTzzxSrzzbDO> znsbTzzxSrzzbDOList = znsbTzzxSrzzbZjbDOGroups.values().stream().map(item -> {
            ZnsbTzzxSrzzbDO znsbTzzxSrzzbDO = new ZnsbTzzxSrzzbDO();
            BeanUtils.copyProperties(item.get(0), znsbTzzxSrzzbDO);
            // 金额相加
            znsbTzzxSrzzbDO.setXssr(item.stream().map(ZnsbTzzxSrzzbZjbDO::getXssr)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP));
            znsbTzzxSrzzbDO.setXsse(znsbTzzxSrzzbDO.getXssr().multiply(znsbTzzxSrzzbDO.getSl1()).setScale(2, RoundingMode.HALF_UP));

            //20250226 禅道1122 针对森马增加调整后的 销售额与税额 没有反算逻辑 不为空 任意值不等于0 直接写入
            //销售额合计
            BigDecimal xseTotal = item.stream()
                    .filter(mxbDO -> GyUtils.isNotNull(mxbDO.getXse()))
                    .map(mxbDO -> new BigDecimal(String.valueOf(mxbDO.getXse())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            //税额合计
            BigDecimal seTotal = item.stream()
                    .filter(mxbDO -> GyUtils.isNotNull(mxbDO.getSe()))
                    .map(mxbDO -> new BigDecimal(String.valueOf(mxbDO.getSe())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            //+ item.get(0).getNsrmc() 去掉纳税人名称
            String key = item.get(0).getSszq() + item.get(0).getDjxh()
                    + item.get(0).getNsrsbh() + item.get(0).getGsh2() + item.get(0).getLrzx() + item.get(0).getSrlxdm() + item.get(0).getJsfsDm1()
                    + item.get(0).getZsxmDm() + item.get(0).getSl1() + item.get(0).getXssrkmdm();

            srxgz.put(key+"xseTotal",xseTotal);
            srxgz.put(key+"seTotal",seTotal);

            znsbTzzxSrzzbDO.setXgrq(new Date());
            znsbTzzxSrzzbDO.setXgrsfid("ZNSB.GJSS.JOB");
            znsbTzzxSrzzbDO.setUuid(IdUtil.fastSimpleUUID());

            item.forEach(zjbDO -> {
                zzid2zjbid.put(znsbTzzxSrzzbDO.getUuid(), zjbDO.getUuid());
            });

            return znsbTzzxSrzzbDO;
        }).collect(Collectors.toList());

        log.info("增值税收入明细汇总srxgz{}:",JsonUtils.toJson(srxgz));

        /*String jtbm = CacheUtils.getXtcs("GY00000001");//获取集团编码
        if(JtbmEnum.QS.getJtbm().equals(jtbm)){//泉膳自定义销售额 和税额 不进行收入明细汇总处理
            // 清空map
            srxgz.clear();
        }*/

        znsbTzzxSrzzbDOList.forEach(item -> {
            ZnsbTzzxSrzzbDO oldZnsbTzzxSrzzbDO =
                    gjssZnsbTzzxSrzzbMapper.selectOne(new QueryWrapperX<ZnsbTzzxSrzzbDO>().lambda()
                            .eq(ZnsbTzzxSrzzbDO::getSszq, item.getSszq())
                            .eq(ZnsbTzzxSrzzbDO::getDjxh, item.getDjxh())
                            //.eq(ZnsbTzzxSrzzbDO::getNsrmc, item.getNsrmc())
                            .eq(ZnsbTzzxSrzzbDO::getNsrsbh, item.getNsrsbh())
                            .eq(ZnsbTzzxSrzzbDO::getGsh2, item.getGsh2())
                            .eq(ZnsbTzzxSrzzbDO::getLrzx, item.getLrzx())
                            .eq(ZnsbTzzxSrzzbDO::getSrlxdm, item.getSrlxdm())
                            .eq(ZnsbTzzxSrzzbDO::getJsfsDm1, item.getJsfsDm1())
                            .eq(ZnsbTzzxSrzzbDO::getZsxmDm, item.getZsxmDm())
                            .eq(ZnsbTzzxSrzzbDO::getSl1, item.getSl1())
                            .eq(ZnsbTzzxSrzzbDO::getXssrkmdm, item.getXssrkmdm())
                    );
            if (oldZnsbTzzxSrzzbDO == null) {
                item.setLrrq(new Date());
                item.setLrrsfid("ZNSB.GJSS.JOB");
                item.setYwqdDm("SYS");
                item.setSjcsdq("00000000000");
                item.setSjgsdq("00000000000");
                item.setSjtbSj(new Date());
                srhzgzcladd(item,srxgz);
                gjssZnsbTzzxSrzzbMapper.insert(item);
            } else {
                String flag = srhzgzclflag(item,srxgz);//是否存在自定义修改标志
                if("N".equals(flag)){// 没有自定义
                    item.setXssr(item.getXssr().add(oldZnsbTzzxSrzzbDO.getXssr())
                            .setScale(2, RoundingMode.HALF_UP));
                    item.setXsse(item.getXssr().multiply(item.getSl1())
                            .setScale(2, RoundingMode.HALF_UP));
                    //针对森马 已经存在对应税率且是视同销售情况相爱 此处需要add处理
                    srhzgzcladd(item,srxgz);
                }else{// 有自定义
                    srhzgzclupdate(item,srxgz,oldZnsbTzzxSrzzbDO);
                }

                zzid2zjbid.putAll(oldZnsbTzzxSrzzbDO.getUuid(), zzid2zjbid.get(item.getUuid()));
                zzid2zjbid.removeAll(item.getUuid());

                item.setUuid(oldZnsbTzzxSrzzbDO.getUuid());
                gjssZnsbTzzxSrzzbMapper.updateById(item);
            }
        });

        gjssZnsbTzzxSrzzbZjbMapper.deleteBatchIds(znsbTzzxSrzzbZjbDOList.stream().map(ZnsbTzzxSrzzbZjbDO::getUuid)
                .collect(Collectors.toList()));
    }

    private String srhzgzclflag(ZnsbTzzxSrzzbDO znsbTzzxSrzzbDO,Map<String, BigDecimal> srxgz) {
        String flag = "N";
        //+ znsbTzzxSrzzbDO.getNsrmc() 去掉名称
        String key = znsbTzzxSrzzbDO.getSszq() + znsbTzzxSrzzbDO.getDjxh()
                + znsbTzzxSrzzbDO.getNsrsbh() + znsbTzzxSrzzbDO.getGsh2() + znsbTzzxSrzzbDO.getLrzx() + znsbTzzxSrzzbDO.getSrlxdm() + znsbTzzxSrzzbDO.getJsfsDm1()
                + znsbTzzxSrzzbDO.getZsxmDm() + znsbTzzxSrzzbDO.getSl1() + znsbTzzxSrzzbDO.getXssrkmdm();


        /*String zdyxgbz = stringRedisTemplate.opsForValue().get("zzstzsrmxhz:"+key+":zdyTz");
        log.info("增值税收入明细汇总zdyxgbz:"+zdyxgbz);
        if("Y".equals(zdyxgbz)){
            flag  =  zdyxgbz;
        }*/

        ZnsbTzzxSrzdyhzbDO srzdyhzbDO = zdyquery(key);
        if(GyUtils.isNotNull(srzdyhzbDO)){
            flag  =  "Y";
        }
        log.info("增值税收入明细汇总zdyxgbz:"+flag);
        return flag;
    }

    private void srhzgzcladd(ZnsbTzzxSrzzbDO znsbTzzxSrzzbDO,Map<String, BigDecimal> srxgz) {
        //20250226 禅道1122 针对森马增加调整后的 销售额与税额 没有反算逻辑 不为空 任意值不等于0 直接写入
        //销售额有值 税额没值 销售额写到销售收入里 税额写入到销项税额为0
        //销售额没值 税额有值 销售额写到销售收入里为0 税额写入到销项税额
        //销售额有值 税额有值 销售额写到销售收入里 税额写入到销项税额

        if(CollectionUtils.isEmpty(srxgz)){
            return;
        }
        //+ znsbTzzxSrzzbDO.getNsrmc() 去掉纳税人名称

        String key = znsbTzzxSrzzbDO.getSszq() + znsbTzzxSrzzbDO.getDjxh()
                + znsbTzzxSrzzbDO.getNsrsbh() + znsbTzzxSrzzbDO.getGsh2() + znsbTzzxSrzzbDO.getLrzx() + znsbTzzxSrzzbDO.getSrlxdm() + znsbTzzxSrzzbDO.getJsfsDm1()
                + znsbTzzxSrzzbDO.getZsxmDm() + znsbTzzxSrzzbDO.getSl1() + znsbTzzxSrzzbDO.getXssrkmdm();

        BigDecimal xseTotal = srxgz.get(key+"xseTotal");
        BigDecimal seTotal = srxgz.get(key+"seTotal");

        log.info("增值税收入明细汇总销售额addxseTotal:"+ xseTotal);
        log.info("增值税收入明细汇总税额addseTotal:"+ seTotal);
        if(xseTotal.compareTo(BigDecimal.ZERO) != 0 || seTotal.compareTo(BigDecimal.ZERO) != 0){
            //新增 首次赋值
            //stringRedisTemplate.opsForValue().set("zzstzsrmxhz:"+key+":xseTotal", String.valueOf(xseTotal),30, TimeUnit.DAYS);
            //stringRedisTemplate.opsForValue().set("zzstzsrmxhz:"+key+":seTotal", String.valueOf(seTotal),30, TimeUnit.DAYS);
            //存在过自定义修改标志
            //stringRedisTemplate.opsForValue().set("zzstzsrmxhz:"+key+":zdyTz", "Y",30, TimeUnit.DAYS);
            if(xseTotal.compareTo(BigDecimal.ZERO) != 0 && seTotal.compareTo(BigDecimal.ZERO) != 0){
                BigDecimal xssr1 = NumberUtil.add(znsbTzzxSrzzbDO.getXssr(), xseTotal.setScale(2, RoundingMode.HALF_UP));
                log.info("增值税收入明细汇总销售收入xssr1:"+xssr1);
                znsbTzzxSrzzbDO.setXssr(xssr1);
                BigDecimal xsse1 = NumberUtil.add(znsbTzzxSrzzbDO.getXsse(), seTotal.setScale(2, RoundingMode.HALF_UP));
                log.info("增值税收入明细汇总税额xsse1:"+xsse1);
                znsbTzzxSrzzbDO.setXsse(xsse1);
            }else if(xseTotal.compareTo(BigDecimal.ZERO) != 0 && seTotal.compareTo(BigDecimal.ZERO) == 0){
                BigDecimal xssr2 = NumberUtil.add(znsbTzzxSrzzbDO.getXssr(), xseTotal.setScale(2, RoundingMode.HALF_UP));
                log.info("增值税收入明细汇总销售收入xssr2:"+xssr2);
                znsbTzzxSrzzbDO.setXssr(xssr2);
                //znsbTzzxSrzzbDO.setXsse(BigDecimal.ZERO);
            }else if(xseTotal.compareTo(BigDecimal.ZERO) == 0 && seTotal.compareTo(BigDecimal.ZERO) != 0){
                //znsbTzzxSrzzbDO.setXssr(BigDecimal.ZERO);
                BigDecimal xsse2 = NumberUtil.add(znsbTzzxSrzzbDO.getXsse(), seTotal.setScale(2, RoundingMode.HALF_UP));
                log.info("增值税收入明细汇总税额xsse2:"+xsse2);
                znsbTzzxSrzzbDO.setXsse(xsse2);
            }
            //自定义销售额和税额存表 便于后续更正申报
            zdyadd(znsbTzzxSrzzbDO, xseTotal, seTotal, key);

        }
    }

    private void srhzgzclupdate(ZnsbTzzxSrzzbDO znsbTzzxSrzzbDO,Map<String, BigDecimal> srxgz,ZnsbTzzxSrzzbDO oldZnsbTzzxSrzzbDO) {
        //20250226 禅道1122 针对森马增加调整后的 销售额与税额 没有反算逻辑 不为空 任意值不等于0 直接写入
        //销售额有值 税额没值 销售额写到销售收入里 税额写入到销项税额为0
        //销售额没值 税额有值 销售额写到销售收入里为0 税额写入到销项税额
        //销售额有值 税额有值 销售额写到销售收入里 税额写入到销项税额

        if(CollectionUtils.isEmpty(srxgz)){
            return;
        }
        //+ znsbTzzxSrzzbDO.getNsrmc() 去掉纳税人名称
        String key = znsbTzzxSrzzbDO.getSszq() + znsbTzzxSrzzbDO.getDjxh()
                + znsbTzzxSrzzbDO.getNsrsbh() + znsbTzzxSrzzbDO.getGsh2() + znsbTzzxSrzzbDO.getLrzx() + znsbTzzxSrzzbDO.getSrlxdm() + znsbTzzxSrzzbDO.getJsfsDm1()
                + znsbTzzxSrzzbDO.getZsxmDm() + znsbTzzxSrzzbDO.getSl1() + znsbTzzxSrzzbDO.getXssrkmdm();

        BigDecimal xseTotal = srxgz.get(key+"xseTotal");
        BigDecimal seTotal = srxgz.get(key+"seTotal");

        log.info("增值税收入明细汇总销售额updatexseTotal:"+ xseTotal);
        log.info("增值税收入明细汇总税额updateseTotal:"+ seTotal);

        if(xseTotal.compareTo(BigDecimal.ZERO) != 0 || seTotal.compareTo(BigDecimal.ZERO) != 0){

            ZnsbTzzxSrzdyhzbDO srzdyhzbDO = zdyquery(key);

            BigDecimal hcxseTotal = NumberUtil.toBigDecimal(srzdyhzbDO.getXse());
            BigDecimal hcseTotal = NumberUtil.toBigDecimal(srzdyhzbDO.getSe());

            //BigDecimal hcxseTotal = NumberUtil.toBigDecimal(stringRedisTemplate.opsForValue().get("zzstzsrmxhz:"+key+":xseTotal"));
            //BigDecimal hcseTotal = NumberUtil.toBigDecimal(stringRedisTemplate.opsForValue().get("zzstzsrmxhz:"+key+":seTotal"));

            log.info("增值税收入明细汇总销售额hcxseTotal:"+ hcxseTotal);
            log.info("增值税收入明细汇总税额hcseTotal:"+ hcseTotal);

            //计算未自定义调整之前的原始收入 表里存在的销售收入减去缓存收入
            BigDecimal oldxssr = NumberUtil.sub(oldZnsbTzzxSrzzbDO.getXssr(),hcxseTotal);
            log.info("增值税收入明细汇总销售额oldxssr:"+ oldxssr);

            //原始逻辑计算
            znsbTzzxSrzzbDO.setXssr(znsbTzzxSrzzbDO.getXssr().add(oldxssr)
                    .setScale(2, RoundingMode.HALF_UP));
            znsbTzzxSrzzbDO.setXsse(znsbTzzxSrzzbDO.getXssr().multiply(znsbTzzxSrzzbDO.getSl1())
                    .setScale(2, RoundingMode.HALF_UP));

            //汇总新缓存的钱和老缓存的钱 自定义
            BigDecimal newxseTotal = NumberUtil.add(hcxseTotal,xseTotal);
            BigDecimal newseTotal = NumberUtil.add(hcseTotal,seTotal);
            log.info("增值税收入明细汇总销售额newxseTotal:"+ newxseTotal);
            log.info("增值税收入明细汇总税额newseTotal:"+ newseTotal);

            //修改 再次赋值
            //stringRedisTemplate.opsForValue().set("zzstzsrmxhz:"+key+":xseTotal", String.valueOf(newxseTotal),30, TimeUnit.DAYS);
            //stringRedisTemplate.opsForValue().set("zzstzsrmxhz:"+key+":seTotal", String.valueOf(newseTotal),30, TimeUnit.DAYS);
            //存在过自定义修改标志
            //stringRedisTemplate.opsForValue().set("zzstzsrmxhz:"+key+":zdyTz", "Y",30, TimeUnit.DAYS);

            //表里赋值
            BigDecimal xssr3 = NumberUtil.add(znsbTzzxSrzzbDO.getXssr(), newxseTotal.setScale(2, RoundingMode.HALF_UP));
            log.info("增值税收入明细汇总销售收入xssr3:"+xssr3);
            znsbTzzxSrzzbDO.setXssr(xssr3);
            BigDecimal xsse3 = NumberUtil.add(znsbTzzxSrzzbDO.getXsse(), newseTotal.setScale(2, RoundingMode.HALF_UP));
            log.info("增值税收入明细汇总税额xsse3:"+xsse3);
            znsbTzzxSrzzbDO.setXsse(xsse3);

            //自定义销售额和税额存表 便于后续更正申报
            zdyupdate(key, newxseTotal, newseTotal);

        }else{//没有修改逻辑情况 走原有逻辑计算
            //原始逻辑计算
            znsbTzzxSrzzbDO.setXssr(znsbTzzxSrzzbDO.getXssr().add(oldZnsbTzzxSrzzbDO.getXssr())
                    .setScale(2, RoundingMode.HALF_UP));
            znsbTzzxSrzzbDO.setXsse(znsbTzzxSrzzbDO.getXssr().multiply(znsbTzzxSrzzbDO.getSl1())
                    .setScale(2, RoundingMode.HALF_UP));

        }
    }

    @Schema(description = "自定义新增")
    private void zdyadd(ZnsbTzzxSrzzbDO znsbTzzxSrzzbDO, BigDecimal xseTotal, BigDecimal seTotal, String key) {
        ZnsbTzzxSrzdyhzbDO addzydDO =  new ZnsbTzzxSrzdyhzbDO();
        addzydDO.setUuid(IdUtil.fastSimpleUUID());
        addzydDO.setDjxh(znsbTzzxSrzzbDO.getDjxh());
        addzydDO.setSszq(Integer.valueOf(znsbTzzxSrzzbDO.getSszq()));
        addzydDO.setNsrsbh(znsbTzzxSrzzbDO.getNsrsbh());
        addzydDO.setGsh2(znsbTzzxSrzzbDO.getGsh2());
        addzydDO.setLrzx(znsbTzzxSrzzbDO.getLrzx());
        addzydDO.setSrlxdm(znsbTzzxSrzzbDO.getSrlxdm());
        addzydDO.setJsfsDm1(znsbTzzxSrzzbDO.getJsfsDm1());
        addzydDO.setZsxmDm(znsbTzzxSrzzbDO.getZsxmDm());
        addzydDO.setSl1(znsbTzzxSrzzbDO.getSl1());
        addzydDO.setXssrkmdm(znsbTzzxSrzzbDO.getXssrkmdm());
        addzydDO.setXse(xseTotal);
        addzydDO.setSe(seTotal);
        addzydDO.setBz(key);//用于后续查询
        addzydDO.setYxbz("Y");
        addzydDO.setLrrq(LocalDateTime.now());
        addzydDO.setXgrq(LocalDateTime.now());
        srzdyhzbMapper.insert(addzydDO);
    }

    @Schema(description = "自定义修改")
    private void zdyupdate(String key, BigDecimal newxseTotal, BigDecimal newseTotal) {
        LambdaUpdateWrapper<ZnsbTzzxSrzdyhzbDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ZnsbTzzxSrzdyhzbDO::getBz, key).eq(ZnsbTzzxSrzdyhzbDO::getYxbz, "Y")
                .set(ZnsbTzzxSrzdyhzbDO::getXgrq, LocalDateTime.now())
                .set(ZnsbTzzxSrzdyhzbDO::getXse, newxseTotal)
                .set(ZnsbTzzxSrzdyhzbDO::getSe, newseTotal);
        srzdyhzbMapper.update(updateWrapper);
    }

    @Schema(description = "自定义查询")
    private ZnsbTzzxSrzdyhzbDO zdyquery(String key) {
        LambdaQueryWrapperX<ZnsbTzzxSrzdyhzbDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(ZnsbTzzxSrzdyhzbDO::getBz, key).eq(ZnsbTzzxSrzdyhzbDO::getYxbz, "Y");
        return srzdyhzbMapper.selectOne(queryWrapper);
    }


}
