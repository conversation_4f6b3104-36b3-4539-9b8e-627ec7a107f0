package com.css.znsb.gjss.pojo.domain.jxfp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class JxfpzzUploadVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("uuid")
    private String uuid;

    @JsonProperty("xh")
    @ExcelProperty("序号")
    private String xh;

    @JsonProperty("nsrsbh")
    @ExcelProperty("企业税号")
    private String nsrsbh;

    @JsonProperty("nsrmc")
    @ExcelProperty("企业名称")
    private String nsrmc;

    @JsonProperty("sszq")
    @ExcelProperty("所属账期")
    private Integer sszq;

    @JsonProperty("xmxl")
    private String xmxl;

    // 临时字段用于接收Excel中的完整值
    @ExcelProperty("项目小类")
    private String rawXmxl;

    @JsonProperty("fs")
    @ExcelProperty("份数")
    private BigDecimal fs;

    @JsonProperty("je")
    @ExcelProperty("金额")
    private BigDecimal je;

    @JsonProperty("se")
    @ExcelProperty("税额")
    private BigDecimal se;
}
