package com.css.znsb.gjss.pojo.dto.sxyd.jxfpAndHwmx;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(description = "进项发票明细")
@Data
public class JxfpAndHwmxResDTO {

    @Schema(description = "购买方纳税人识别号")
    @JsonProperty("gmfnsrsbh")
    private String gmfnsrsbh;

    @Schema(description = "购买方名称")
    @JsonProperty("gmfmc")
    private String gmfmc;

    @Schema(description = "发票票种")
    @JsonProperty("fppzDm")
    private String fppzDm;

    @Schema(description = "发票代码")
    @JsonProperty("fpdm")
    private String fpdm;

    @Schema(description = "发票号码")
    @JsonProperty("fphm")
    private String fphm;

    @Schema(description = "发票号码")
    @JsonProperty("fphmdm")
    private String fphmdm;

    @Schema(description = "开票日期")
    @JsonProperty("kprq")
    private String kprq;

    @Schema(description = "销方税号")
    @JsonProperty("xfsbh")
    private String xfsbh;

    @Schema(description = "销方名称")
    @JsonProperty("xsfmc")
    private String xsfmc;

    @Schema(description = "勾选日期")
    @JsonProperty("gxrzsj")
    private String gxrzsj;

    @Schema(description = "勾选状态")
    @JsonProperty("gxzt")
    private String gxzt;

    @Schema(description = "核选用途")
    @JsonProperty("hxytDm")
    private String hxytDm;

    @Schema(description = "不抵扣原因代码")
    @JsonProperty("bdkyyDm")
    private String bdkyyDm;

    @Schema(description = "可抵扣税额")
    @JsonProperty("yxdkse")
    private BigDecimal yxdkse;

    @Schema(description = "发票类型")
    @JsonProperty("fplxDm")
    private String fplxDm;

    @Schema(description = "金额")
    @JsonProperty("je")
    private BigDecimal je;

    @Schema(description = "税额")
    @JsonProperty("se")
    private BigDecimal se;

    @Schema(description = "价税合计")
    @JsonProperty("jshj")
    private BigDecimal jshj;

    @Schema(description = "税款所属期")
    @JsonProperty("skssq_1")
    private String skssq;

    @Schema(description = "逾期扣税凭证")
    @JsonProperty("yqkszzszyfpbz")
    private String yqkszzszyfpbz;

    @Schema(description = "发票货物明细")
    @JsonProperty("fphwmx")
    private List<JxfphwmxResDTO> fphwmx;

    @Schema(description = "修改日期")
    @JsonProperty("xgrq")
    private String xgrq;

    @Schema(description = "特定要素类型代码")
    @JsonProperty("tdyslxDm")
    private String tdyslxDm;

}
