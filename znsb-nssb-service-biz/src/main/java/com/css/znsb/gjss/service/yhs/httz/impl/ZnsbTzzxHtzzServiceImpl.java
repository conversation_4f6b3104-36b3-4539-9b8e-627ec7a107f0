package com.css.znsb.gjss.service.yhs.httz.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.pojo.PageResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.constants.enums.JtbmEnum;
import com.css.znsb.gjss.constants.enums.TzlxDmEnum;
import com.css.znsb.gjss.constants.enums.YspzlxEnum;
import com.css.znsb.gjss.feign.BpmConfig;
import com.css.znsb.gjss.feign.jyss.BPMSjcjApi;
import com.css.znsb.gjss.feign.jyss.JyssApi;
import com.css.znsb.gjss.mapper.yhs.httz.*;
import com.css.znsb.gjss.pojo.domain.yhs.httz.*;
import com.css.znsb.gjss.pojo.domain.yhs.httz.bpmSjtb.*;
import com.css.znsb.gjss.pojo.vo.yhs.HttzInsertReqVO;
import com.css.znsb.gjss.pojo.vo.yp.SbzbRequest;
import com.css.znsb.gjss.pojo.vo.yp.SbzbResponse;
import com.css.znsb.gjss.pojo.vo.yp.httz.HttzJyssDTO;
import com.css.znsb.gjss.service.yhs.httz.ZnsbTzzxHtzzService;
import com.css.znsb.gjss.util.GjssGyUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.*;
import com.css.znsb.nssb.constants.BcztConstants;
import com.css.znsb.nssb.constants.enums.NsqxEnum;
import com.css.znsb.nssb.mapper.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxMapper;
import com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhscjMapper;
import com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhslfkblMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxDO;
import com.css.znsb.nssb.pojo.domain.cchxwssb.yhs.ZnsbNssbYhscjDO;
import com.css.znsb.nssb.pojo.domain.cchxwssb.yhs.ZnsbNssbYhslfkblDO;
import com.css.znsb.nssb.pojo.dto.yhssycj.tzScsy.TzScYhsSyRequestDTO;
import com.css.znsb.nssb.pojo.dto.yhssycj.tzScsy.TzScYhsSySkssqqzDTO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.yhssycj.GyJsSfzVO;
import com.css.znsb.nssb.service.cchxwssb.yhssjcj.YhssycjNewService;
import com.css.znsb.nssb.service.cchxwssb.yhssjcj.YhssycjhdService;
import com.css.znsb.nssb.util.NsqxUtils;
import com.css.znsb.tzzx.constants.enums.YzpzzlEnum;
import com.css.znsb.tzzx.pojo.PageRecordsVO;
import com.css.znsb.tzzx.pojo.tzzx.httz.*;
import com.css.znsb.tzzx.pojo.vo.yhs.YhsZszmVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【znsb_tzzx_htzz(智能申报_台账中心_合同台帐)】的数据库操作Service实现
 * @createDate 2024-07-16 16:21:46
 */
@Slf4j
@Service
public class ZnsbTzzxHtzzServiceImpl extends ServiceImpl<ZnsbTzzxHtzzMapper, ZnsbTzzxHtzzDO>
        implements ZnsbTzzxHtzzService{

    /**
     * @description 日志标题
     * @value value:logTitle
     */
    private final String logTitle = "合同台账：";

    /**
     * @description 合同台账Mapper
     * @value value:htzzMapper
     */
    @Resource
    private ZnsbTzzxHtzzMapper htzzMapper;

    /**
     * @description 立法可变列Mapper
     * @value value:yhslfkblMapper
     */
    @Resource
    private ZnsbTzzxYhslfkblMapper yhslfkblMapper;

    /**
     * @description 财行税表头信息Mapper
     * @value value:cxsbtxxMapper
     */
    @Resource
    private ZnsbNssbCxsbtxxMapper cxsbtxxMapper;

    /**
     * @description 印花税采集Mapper
     * @value value:yhscjMapper
     */
    @Resource
    private ZnsbNssbYhscjMapper yhscjMapper;

    /**
     * @description nssb立法可变列Mapper
     * @value value:nssbYhslfkblMapper
     */
    @Resource
    private ZnsbNssbYhslfkblMapper nssbYhslfkblMapper;

    /**
     * @description bpm中继表Mapper
     * @value value:bpmzjbMapper
     */
    @Resource
    private ZnsbTzzxHtzzBpmzjbMapper bpmzjbMapper;

    /**
     * @description 房产税bpm合同信息Mapper
     * @value value:fcshtxxBpmMapper
     */
    @Resource
    private ZnsbTzzxFcshtxxBpmMapper fcshtxxBpmMapper;

    /**
     * @description 房产税bpm合同信息商铺子表Mapper
     * @value value:fcsSpBpmMapper
     */
    @Resource
    private ZnsbTzzxFcshtxxBpmSpzbMapper fcsSpBpmMapper;

    /**
     * @description 房产税bpm合同信息租赁信息Mapper
     * @value value:fcsZlxxBpmMapper
     */
    @Resource
    private ZnsbTzzxFcshtxxBpmZlxxMapper fcsZlxxBpmMapper;

    @Resource
    private ZnsbTzzxHtzzServiceImpl htzzService;

    @Resource
    private YhssycjhdService yhssycjhdService;

    @Resource
    private YhssycjNewService yhssycjnewservice;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private JyssApi jyssApi;

    @Resource
    private BPMSjcjApi bpmSjcjApi;

    @Resource
    private BpmConfig bpmConfig;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Value("${bpm-config.url:}")
    private String bpmUrl;

    /**
     * @param httzReqVO
     * @return {@link PageRecordsVO }
     * @name 查询合同台账信息
     * @description 相关说明
     * @time 创建时间:2024年07月17日上午11:08:29
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public PageResult<HttzResVO> queryHttz(HttzReqVO httzReqVO) {
        log.info(logTitle + "查询合同台账信息开始");
        final List<HttzResVO> resList = new ArrayList<>();

        //获取合同台账主表信息
        final PageResult<ZnsbTzzxHtzzDO> httzPageDate = htzzMapper.getHttzPage(httzReqVO);
        final List<ZnsbTzzxHtzzDO> httzList = httzPageDate.getList();

        //循环合同信息获取结算信息和对方树立人信息
        for (ZnsbTzzxHtzzDO httzUnit:httzList){
            final String zbuuid = httzUnit.getUuid();

            //根据主表uuid获取可变列信息
            final List<ZnsbTzzxYhslfkblDO> kblList = yhslfkblMapper.getYhslfkblByZbuuid(zbuuid);

            //区分可变列记录的具体内容
            final List<ZnsbTzzxYhslfkblDO> sjjsList = new ArrayList<>();//实际结算信息
            final List<ZnsbTzzxYhslfkblDO> dfslList = new ArrayList<>();//对方书立信息

            for (ZnsbTzzxYhslfkblDO kblUnit:kblList){
                if (!GyUtils.isNull(kblUnit.getSjjsje())){
                    //结算信息
                    sjjsList.add(kblUnit);
                } else if (!GyUtils.isNull(kblUnit.getDfslrnsrsbh())){
                    //对方书立人信息
                    dfslList.add(kblUnit);
                }
            }

            //拼装此条返回信息
            final HttzResVO resVO = new HttzResVO();
            resVO.setUuid(httzUnit.getUuid());
            resVO.setZzuuid(httzUnit.getZzuuid());
            resVO.setHtbh(httzUnit.getHtbh());
            resVO.setHtmc(httzUnit.getHtmc());
            resVO.setHtydsxrq(httzUnit.getHtydsxrq());
            resVO.setHtydzzrq(httzUnit.getHtydzzrq());
            resVO.setYnspzsllsrq(httzUnit.getYnspzsllsrq());
            resVO.setZspmDm(httzUnit.getZspmDm());
            resVO.setZszmDm(httzUnit.getZszmDm());
            resVO.setHtzjk1(httzUnit.getHtzjk1());
            resVO.setBhsje(httzUnit.getBhsje());
            resVO.setPzbh(httzUnit.getPzbh());
            resVO.setPch(httzUnit.getPch());
            resVO.setFbhtbj(httzUnit.getFbhtbj());
            resVO.setKjhtbj(httzUnit.getKjhtbj());
            resVO.setYwqdDm(httzUnit.getYwqdDm());
            resVO.setZspmmc(CacheUtils.dm2mc("dm_gy_zspm", httzUnit.getZspmDm()));
            resVO.setZszmmc(CacheUtils.dm2mc("dm_gy_zszm", httzUnit.getZszmDm()));
            resVO.setLrzx(httzUnit.getLrzx());
            resVO.setHtbt(httzUnit.getHtbt());
            resVO.setHtsm1(httzUnit.getHtsm1());
            resVO.setSszq(httzUnit.getSszq());
            resVO.setKhbh(httzUnit.getKhbh());
            resVO.setGysbm(httzUnit.getGysbm());

            //写入实际结算金额总值
            final BigDecimal sjjsjeSum = sjjsList.stream()
                    .map(ZnsbTzzxYhslfkblDO::getSjjsje)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            resVO.setSjjsje(sjjsjeSum);

            resList.add(resVO);
        }

        //返回数据
        final PageResult<HttzResVO> resData = new PageResult<>();
        resData.setList(resList);
        resData.setTotal(httzPageDate.getTotal());


        log.info(logTitle + "查询合同台账信息结束");
        return resData;
    }

    /**
     * @param httzReqVO
     * @return {@link HttzResVO }
     * @name 查询合同台账合计
     * @description 相关说明
     * @time 创建时间:2024年12月12日上午09:51:11
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public HttzResVO queryHtzzHj(HttzReqVO httzReqVO) {
        log.info(logTitle + "查询合同台账合计开始");

        //获取信息
        final List<ZnsbTzzxHtzzDO> allDate = htzzMapper.getHttzAll(httzReqVO);

        BigDecimal htzjkHj = BigDecimal.ZERO;
        BigDecimal bhsjeHj = BigDecimal.ZERO;
        for (ZnsbTzzxHtzzDO unit:allDate) {
            if (!GyUtils.isNull(unit.getHtzjk1())) {
                htzjkHj = htzjkHj.add(unit.getHtzjk1());
            }
            if (!GyUtils.isNull(unit.getBhsje())) {
                bhsjeHj = bhsjeHj.add(unit.getBhsje());
            }
        }

        final HttzResVO result = new HttzResVO();
        result.setHtzjk1(htzjkHj);
        result.setBhsje(bhsjeHj);

        log.info(logTitle + "查询合同台账合计结束");
        return result;
    }

    /**
     * @param uuid
     * @return {@link YhslfkblResVO }
     * @name 查询立法可变列
     * @description 相关说明
     * @time 创建时间:2024年07月17日下午05:55:53
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public YhslfkblResVO queryYhslfkbl(String uuid) {
        log.info(logTitle + "查询立法可变列开始");

        //根据主表uuid获取可变列信息
        final List<ZnsbTzzxYhslfkblDO> kblList = yhslfkblMapper.getYhslfkblByZbuuid(uuid);

        //区分可变列记录的具体内容
        final List<SjjsxxVO> sjjsList = new ArrayList<>();//实际结算信息
        final List<DfslxxVO> dfslList = new ArrayList<>();//对方书立信息

        for (ZnsbTzzxYhslfkblDO kblUnit:kblList){
            if (!GyUtils.isNull(kblUnit.getSjjsje())){
                //结算信息
                final SjjsxxVO sjjsxxUnit = new SjjsxxVO();
                sjjsxxUnit.setUuid(kblUnit.getUuid());
                sjjsxxUnit.setSjjsrq(kblUnit.getSjjsrq());
                sjjsxxUnit.setSjjsje(kblUnit.getSjjsje());

                sjjsList.add(sjjsxxUnit);
            } else if (!GyUtils.isNull(kblUnit.getDfslrnsrsbh())){
                //对方书立人信息
                final DfslxxVO dfslxxUnit = new DfslxxVO();
                dfslxxUnit.setUuid(kblUnit.getUuid());
                dfslxxUnit.setDfslrmc(kblUnit.getDfslrmc());
                dfslxxUnit.setDfslrnsrsbh(kblUnit.getDfslrnsrsbh());
                dfslxxUnit.setDfslrsjje(kblUnit.getDfslrsjje());

                dfslList.add(dfslxxUnit);
            }
        }

        //返回信息
        final YhslfkblResVO resVO = new YhslfkblResVO();
        resVO.setDfslxxList(dfslList);
        resVO.setSjjsxxList(sjjsList);

        log.info(logTitle + "查询立法可变列结束");
        return resVO;
    }

    /**
     * @param uuidList
     * @return int
     * @name 删除所选数据
     * @description 相关说明
     * @time 创建时间:2024年07月18日上午11:54:25
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public int deleteSelected(List<String> uuidList) {
        log.info(logTitle + "删除所选合同台账开始");

        //查询删除数据
        final List<ZnsbTzzxHtzzDO> deleteList = htzzMapper.selectBatchIds(uuidList);

        //循环生成调整数据
        final List<ZnsbTzzxHtzzDO> tzList = new ArrayList<>();
        for (ZnsbTzzxHtzzDO deleteUnit:deleteList){
            //原数据调账类型更改为2
            deleteUnit.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
            deleteUnit.setXgrq(new Date());
            tzList.add(deleteUnit);

            //新增调账类型为3的对应数据
            final ZnsbTzzxHtzzDO newUnit = new ZnsbTzzxHtzzDO();
            BeanUtils.copyBean(deleteUnit, newUnit);
            newUnit.setUuid(IdUtil.fastSimpleUUID());
            newUnit.setZzuuid("");
            newUnit.setHtzjk1(deleteUnit.getHtzjk1().negate());
            newUnit.setBhsje(deleteUnit.getBhsje().negate());
            newUnit.setTzlxDm(TzlxDmEnum.TZHCJ.getTzlxDm());
            newUnit.setSjtbSj(new Date());
            newUnit.setZzscbz("N");
            newUnit.setJyssbz("N");
            tzList.add(newUnit);
        }

        //主表调账
        final boolean zbscFlag = htzzMapper.insertOrUpdateBatch(tzList);

        //删除副表（逻辑删除）
        final List<ZnsbTzzxYhslfkblDO> yhslfkblList = yhslfkblMapper.getYhslfkblByZbuuidList(uuidList);
        final List<ZnsbNssbYhslfkblDO> nssbYhslfkblList = new ArrayList<>();
        for (ZnsbTzzxYhslfkblDO unit:yhslfkblList) {
            unit.setScbz("1");
            unit.setScsj(new Date());

            //拼装nssb修改数据
            final ZnsbNssbYhslfkblDO nssbUnit = new ZnsbNssbYhslfkblDO();
            nssbUnit.setUuid(unit.getUuid());
            nssbUnit.setZfbz1("Y");
            nssbUnit.setZfrq1(new Date());
            nssbUnit.setZfrDm("ZNSB.TZZX");
            nssbYhslfkblList.add(nssbUnit);
        }
        if (!GyUtils.isNull(yhslfkblList)) {
            yhslfkblMapper.updateBatch(yhslfkblList);
        }

        //删除nssb对应立法可变列
        if (!GyUtils.isNull(nssbYhslfkblList)){
            nssbYhslfkblMapper.updateBatch(nssbYhslfkblList);
        }


        log.info(logTitle + "删除所选合同台账结束");
        return zbscFlag?uuidList.size():0;
    }

    /**
     * @param insertVO
     * @return int
     * @name 新增合同台账
     * @description 相关说明
     * @time 创建时间:2024年07月22日上午11:23:14
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public int insertHttz(HttzUpdateVO insertVO) {
        log.info(logTitle + "新增合同台账开始");

        //插入主表
        final ZnsbTzzxHtzzDO htzzDO = this.buildHtzzDO(insertVO);
        final int httzCount = htzzMapper.insert(htzzDO);

        //插入附表
        final List<ZnsbTzzxYhslfkblDO> yhslfkblDOList = this.buildYhslfkblDO(insertVO, htzzDO.getUuid(), htzzDO.getZspmDm());
        yhslfkblMapper.insertBatch(yhslfkblDOList);

        log.info(logTitle + "新增合同台账结束");
        return httzCount;
    }

    /**
     * @param updateVO
     * @return boolean
     * @name 修改合同台账
     * @description 相关说明
     * @time 创建时间:2024年07月22日上午11:23:28
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public boolean updateHttz(HttzUpdateVO updateVO) {
        log.info(logTitle + "修改合同台账开始");

        //拼装主表新记录
        final List<ZnsbTzzxHtzzDO> updateTzList = new ArrayList<>();
        final ZnsbTzzxHtzzDO htzzDO = this.buildHtzzDO(updateVO);
        htzzDO.setTzlxDm(TzlxDmEnum.XTZ.getTzlxDm());
        htzzDO.setZzscbz("N");
        htzzDO.setJyssbz("N");

        //获取主表旧纪录
        final ZnsbTzzxHtzzDO htzzRecordDO = htzzMapper.selectById(updateVO.getHttzxx().getUuid());
        htzzRecordDO.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
        htzzRecordDO.setXgrq(new Date());

        //生成主表调账冲减记录
        final ZnsbTzzxHtzzDO htzzRecordCjDO = new ZnsbTzzxHtzzDO();
        BeanUtils.copyBean(htzzRecordDO, htzzRecordCjDO);
        htzzRecordCjDO.setUuid(IdUtil.fastSimpleUUID());
        htzzRecordCjDO.setZzuuid("");
        htzzRecordCjDO.setHtzjk1(htzzRecordDO.getHtzjk1().negate());
        htzzRecordCjDO.setBhsje(htzzRecordDO.getBhsje().negate());
        htzzRecordCjDO.setTzlxDm(TzlxDmEnum.TZHCJ.getTzlxDm());
        htzzRecordCjDO.setZzscbz("N");
        htzzRecordCjDO.setJyssbz("N");
        htzzRecordCjDO.setSjtbSj(new Date());

        //插入主表
        updateTzList.add(htzzRecordDO);
        updateTzList.add(htzzRecordCjDO);
        updateTzList.add(htzzDO);
        final boolean httzUpdateFlag = htzzMapper.insertOrUpdateBatch(updateTzList);

        //修改附表
        final List<ZnsbTzzxYhslfkblDO> yhslfkblDOList = this.buildYhslfkblDO(updateVO, htzzDO.getUuid(), htzzDO.getZspmDm());
        final Boolean kblInsert = yhslfkblMapper.insertOrUpdateBatch(yhslfkblDOList);

        log.info(logTitle + "修改合同台账结束");
        return httzUpdateFlag && kblInsert;
    }


    /**
     * @param insertVO
     * @return {@link ZnsbTzzxHtzzDO }
     * @name 构建合同台账DO
     * @description 相关说明
     * @time 创建时间:2024年07月22日上午11:23:38
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private ZnsbTzzxHtzzDO buildHtzzDO(HttzUpdateVO insertVO){
        //获取信息
        final String djxh = insertVO.getDjxh();
        final String sszq = insertVO.getSszq();
        final HttzResVO httzxx = insertVO.getHttzxx();

        //主表生成主表DO
        final ZnsbTzzxHtzzDO htzzDO = new ZnsbTzzxHtzzDO();
        htzzDO.setDjxh(djxh);
        htzzDO.setSszq(Integer.parseInt(sszq));
        htzzDO.setHtbh(httzxx.getHtbh());
        htzzDO.setHtmc(httzxx.getHtmc());
        htzzDO.setHtydsxrq(httzxx.getHtydsxrq());
        htzzDO.setHtydzzrq(httzxx.getHtydzzrq());
        htzzDO.setYnspzsllsrq(httzxx.getYnspzsllsrq());
        htzzDO.setZspmDm(httzxx.getZspmDm());
        htzzDO.setZszmDm(httzxx.getZszmDm());
        htzzDO.setHtzjk1(httzxx.getHtzjk1());
        htzzDO.setBhsje(httzxx.getBhsje());
        htzzDO.setPzbh(httzxx.getPzbh());
        htzzDO.setPch(httzxx.getPch());
        htzzDO.setFbhtbj("N");
        htzzDO.setKjhtbj("N");
        htzzDO.setCqbz("1");
        htzzDO.setScbz("0");
        htzzDO.setUuid(IdUtil.fastSimpleUUID());
        htzzDO.setLrzx(httzxx.getLrzx());
        htzzDO.setHtbt(httzxx.getHtbt());
        htzzDO.setHtsm1(httzxx.getHtsm1());

        final String sszqStr = DateUtils.dateToString(httzxx.getYnspzsllsrq(), 17);
        htzzDO.setSszq(Integer.parseInt(sszqStr));

        //新增修改差异化处理
        if (GyUtils.isNull(httzxx.getUuid())){
            //新增
            htzzDO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
        } else {
            //修改
            htzzDO.setTzlxDm(TzlxDmEnum.XTZ.getTzlxDm());
        }

        //录入固定字段
        htzzDO.setZzscbz("N");
        htzzDO.setJyssbz("N");
        htzzDO.setLrrq(new Date());
        htzzDO.setXgrq(new Date());
        htzzDO.setLrrsfid("ZNSB.TZZX");
        htzzDO.setYwqdDm("01");
        htzzDO.setSjcsdq("00000000000");
        htzzDO.setSjgsdq("00000000000");
        htzzDO.setSjtbSj(new Date());

        return htzzDO;
    }

    /**
     * @param insertVO
     * @param zbuuid
     * @param zspmDm
     * @return {@link List<ZnsbTzzxYhslfkblDO> }
     * @name 构建印花税立法可变列DO
     * @description 相关说明
     * @time 创建时间:2024年07月22日上午11:24:31
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private List<ZnsbTzzxYhslfkblDO> buildYhslfkblDO(HttzUpdateVO insertVO, String zbuuid, String zspmDm){
        final List<ZnsbTzzxYhslfkblDO> doList = new ArrayList<>();

        //获取信息
//        final String djxh = insertVO.getDjxh();
//        final String sszq = insertVO.getSszq();
        final List<DfslxxVO> dfslxxList = insertVO.getDfslxxList();
        final List<SjjsxxVO> sjjsxxList = insertVO.getSjjsxxList();

        //循环处理对方书立信息
        for (DfslxxVO dfslxxUnit:dfslxxList){
            final ZnsbTzzxYhslfkblDO yhslfkblDO = new ZnsbTzzxYhslfkblDO();

            //写入数据
            yhslfkblDO.setDfslrmc(dfslxxUnit.getDfslrmc());
            yhslfkblDO.setDfslrnsrsbh(dfslxxUnit.getDfslrnsrsbh());
            yhslfkblDO.setDfslrsjje(dfslxxUnit.getDfslrsjje());
            yhslfkblDO.setZspmDm(zspmDm);
            yhslfkblDO.setScbz("0");

            //新增和修改差异化处理
            if (!GyUtils.isNull(dfslxxUnit.getUuid())){
                //修改
                yhslfkblDO.setUuid(dfslxxUnit.getUuid());
                yhslfkblDO.setZbuuid(zbuuid);
            } else {
                //新增
                yhslfkblDO.setUuid(IdUtil.fastSimpleUUID());
                yhslfkblDO.setZbuuid(zbuuid);
                yhslfkblDO.setScsj(new Date());
            }

            //录入固定字段
            yhslfkblDO.setZzscbz("N");
            yhslfkblDO.setJyssbz("N");
            yhslfkblDO.setLrrq(new Date());
            yhslfkblDO.setXgrq(new Date());
            yhslfkblDO.setLrrsfid("ZNSB.TZZX");
            yhslfkblDO.setYwqdDm("TZ_USER");
            yhslfkblDO.setSjcsdq("00000000000");
            yhslfkblDO.setSjgsdq("00000000000");
            yhslfkblDO.setSjtbSj(new Date());

            //写入集合
            doList.add(yhslfkblDO);
        }

        //循环处理实际结算信息
        for (SjjsxxVO sjjsxxUnit:sjjsxxList){
            final ZnsbTzzxYhslfkblDO yhslfkblDO = new ZnsbTzzxYhslfkblDO();

            //写入数据
            yhslfkblDO.setSjjsje(sjjsxxUnit.getSjjsje());
            yhslfkblDO.setSjjsrq(sjjsxxUnit.getSjjsrq());
            yhslfkblDO.setZspmDm(zspmDm);
            yhslfkblDO.setScbz("0");

            //新增和修改差异化处理
            if (!GyUtils.isNull(sjjsxxUnit.getUuid())){
                //修改
                yhslfkblDO.setUuid(sjjsxxUnit.getUuid());
                yhslfkblDO.setZbuuid(zbuuid);
            } else {
                //新增
                yhslfkblDO.setUuid(IdUtil.fastSimpleUUID());
                yhslfkblDO.setZbuuid(zbuuid);
                yhslfkblDO.setScsj(new Date());
            }

            //录入固定字段
            yhslfkblDO.setZzscbz("N");
            yhslfkblDO.setJyssbz("N");
            yhslfkblDO.setLrrq(new Date());
            yhslfkblDO.setXgrq(new Date());
            yhslfkblDO.setLrrsfid("ZNSB.TZZX");
            yhslfkblDO.setYwqdDm("TZ_USER");
            yhslfkblDO.setSjcsdq("00000000000");
            yhslfkblDO.setSjgsdq("00000000000");
            yhslfkblDO.setSjtbSj(new Date());

            //写入集合
            doList.add(yhslfkblDO);
        }

        return doList;
    }


    /**
     * @param limit
     * @param days
     * @return boolean
     * @name 合同台账交易算税投递及生成总账
     * @description 相关说明
     * @time 创建时间:2024年07月24日下午04:52:17
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public boolean httzZzCalculate(long limit,int days){
        log.info(logTitle + "总账计算开始");

        //取最近的两个分区
        LocalDate startTime = LocalDate.now().minusDays(days);
        LocalDate endTime = LocalDate.now().plusDays(1);

        //获取需汇总数据
        final List<ZnsbTzzxHtzzDO> jyssData = htzzMapper.getJyssData(limit, startTime, endTime);
        log.info(logTitle + "需投递数据条目" + jyssData.size());

        //按登记序号、所属账期、征收品目、征收子目、合同编号分类，进行交易算税投递
        Function<ZnsbTzzxHtzzDO, List<Object>> compositeKeyJyss = mxbDO ->
                Arrays.asList(mxbDO.getDjxh(), mxbDO.getSszq(), mxbDO.getZspmDm(), mxbDO.getZszmDm(), mxbDO.getHtbh());
        Map<List<Object>, List<ZnsbTzzxHtzzDO>> groupingMapJyss =
                jyssData.stream().collect(Collectors.groupingBy(compositeKeyJyss, Collectors.toList()));


            groupingMapJyss.forEach((keys, httzDOS) -> {
                try{
                    //查询纳税人信息
                    ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                    znsbMhzcQyjbxxmxReqVO.setDjxh(httzDOS.get(0).getDjxh());
                    CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
                    List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
                    final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
                    final String zgswjDm = jbxxmxsj.getZgswjDm();

                    //获取税款所属期
                    final Map<String, String> skssqqzMap = GjssGyUtils.calYhsSkssq(sfzrdmxxx, Integer.toString(httzDOS.get(0).getSszq()), httzDOS.get(0).getZspmDm(), httzDOS.get(0).getZszmDm(), httzDOS.get(0).getYnspzsllsrq());
                    final String skssqq = skssqqzMap.get("skssqq");
                    final String skssqz = skssqqzMap.get("skssqz");

                    //查询表头信息
                    List<ZnsbNssbCxsbtxxDO> btxx = cxsbtxxMapper.getCxsbtxx(httzDOS.get(0).getDjxh(), skssqq, skssqz);
                    if (!GyUtils.isNull(btxx)) {
                        //如果该表头当前保存状态正在处理中或者已申报（其实是申报中），不生成总账
                        if (BcztConstants.CLZ.equals(btxx.get(0).getBczt()) || BcztConstants.SBZ.equals(btxx.get(0).getBczt())){
                            return;
                        }
                    }

                    //获取纳税期限，获取不到则默认按次
                    final String nsqxDm = sfzrdmxxx.stream()
                            .filter(sfzrdmxxxVO -> httzDOS.get(0).getZspmDm().equals(sfzrdmxxxVO.getZspmDm())).map(SfzrdmxxxVO::getNsqxDm).findFirst().orElse(NsqxEnum.TIME.getDm());

                    //2024.11.13:按次申报不在汇总时进行交易算税投递
                    if (NsqxEnum.TIME.getDm().equals(nsqxDm)) {
                        return;
                    }

                    //更新标志List
                    final List<ZnsbTzzxHtzzDO> gxJyssbzList = new ArrayList<>();

                    //通过调账记录生成blnr
                    final List<HttzJyssDTO> blnr = new ArrayList<>();
                    for (ZnsbTzzxHtzzDO httzDO:httzDOS){
                        final HttzJyssDTO blnrUnit = new HttzJyssDTO();
                        blnrUnit.setYwfsrq(DateUtils.dateToString(httzDO.getYnspzsllsrq(), 3));//2024.10.17:业务发生时间改为取合同签订日期DateUtils.getSystemCurrentTime(3)
                        blnrUnit.setNsqx(nsqxDm);
                        blnrUnit.setYspzlxDm(YspzlxEnum.getYspzlxByZspm(httzDO.getZspmDm()));
                        blnrUnit.setYspzbh(httzDO.getHtbh());
    //                blnrUnit.setZy();
    //                blnrUnit.setBz();
                        blnrUnit.setSspzhm(httzDO.getUuid());
                        blnrUnit.setZspmDm(httzDO.getZspmDm());
                        blnrUnit.setZszmDm(httzDO.getZszmDm());
                        blnrUnit.setYspzmc(httzDO.getHtmc());
                        blnrUnit.setYspzsl("1");
                        blnrUnit.setJmse(BigDecimal.ZERO);


                        //业务分类
                        if ("101110200".equals(httzDO.getZspmDm())) {
                            //产权转移书据
                            if (TzlxDmEnum.WTZ.getTzlxDm().equals(httzDO.getTzlxDm())){
                                blnrUnit.setYwfl("CQ001");
                            } else {
                                blnrUnit.setYwfl("CQ002");
                            }
                        } else {
                            //其他类型
                            if (TzlxDmEnum.WTZ.getTzlxDm().equals(httzDO.getTzlxDm())){
                                blnrUnit.setYwfl("HT001");
                            } else {
                                blnrUnit.setYwfl("HT002");
                            }
                        }

                        //计算金额
                        final BigDecimal je;
                        if (!GyUtils.isNull(httzDO.getBhsje())){
                            je = httzDO.getBhsje();
                        } else {
                            je = httzDO.getHtzjk1();
                        }
                        blnrUnit.setJe(je);

                        //获取税率（2024.10.12：更新税率获取规则，先从税费种认定开始获取，获取不到再从关联表获取）
    //                    Map<String, Object> zspmData = CacheUtils.getTableData("dm_gy_zspm", httzDO.getZspmDm());
    //                    final BigDecimal sl = BigDecimal.valueOf((Double) zspmData.get("sl1"));
                        String slStr = skssqqzMap.get("sl");//先从税费种认定里获取
                        if (GyUtils.isNull(slStr)) {
                            //若税费种认定没有，使用公共方法查询表cs_gy_glb_zspm
                            slStr = this.getYhsSlByRedis("10111", httzDO.getZspmDm(), httzDO.getZszmDm(), skssqq, skssqz, zgswjDm);
                        }
                        if (GyUtils.isNull(slStr) || "null".equals(slStr)) {
                            log.info(logTitle + "合同明细按期投递交易算税获取税率失败，参数：djxh:" + httzDO.getDjxh() + "|" + httzDO.getZspmDm() + "|" + httzDO.getZszmDm() + JsonUtils.toJson(skssqqzMap));
                            continue;
                        }
                        final BigDecimal sl = new BigDecimal(slStr);


                        blnrUnit.setSl(sl);

                        //计算税额
                        blnrUnit.setSe(je.multiply(sl));

                        //加入List
                        blnr.add(blnrUnit);

                        //记录交易算税更新
                        final ZnsbTzzxHtzzDO gxJyssbzUnit = new ZnsbTzzxHtzzDO();
                        gxJyssbzUnit.setUuid(httzDO.getUuid());
                        gxJyssbzUnit.setJyssbz("Y");
                        gxJyssbzList.add(gxJyssbzUnit);
                    }

                    //装填算税接口基础参数
                    SbzbRequest sbzbRequest = new SbzbRequest();
                    sbzbRequest.setDjxh(httzDOS.get(0).getDjxh());
                    sbzbRequest.setSspzhm(httzDOS.get(0).getUuid());
                    sbzbRequest.setSsywflDm("0520");//0520 补录-印花税-合同采集
                    sbzbRequest.setYwfsrq(DateUtils.getSystemCurrentTime(3));
                    sbzbRequest.setSkssqq(skssqq);
                    sbzbRequest.setSkssqz(skssqz);
                    sbzbRequest.setXwgzlxDm("0");
                    sbzbRequest.setZsxmDm("10111");
                    sbzbRequest.setBlnr(JsonUtils.toJson(blnr));

                    //调用交易算税接口
                    log.info(logTitle + "调用交易算税接口请求：" + JsonUtils.toJson(sbzbRequest));
                    final CommonResult<SbzbResponse> result = jyssApi.sbzb(sbzbRequest);
                    log.info(logTitle + "调用交易算税接口返回：" + JsonUtils.toJson(result));

                    Integer code = result.getCode();
                    if (!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code)){
                        throw new RuntimeException(result.getMsg());
                    }

                    //更改交易算税标志
                    htzzService.updateHttzJyssbz(gxJyssbzList);
                } catch (Exception e) {
                    log.error(logTitle + "交易算税投递接口调用失败，分片信息：" + JsonUtils.toJson(keys),e);
//                    throw new RuntimeException(e.getMessage());
                }
            });


        //获取需汇总数据
        final List<ZnsbTzzxHtzzDO> zzjsData = htzzMapper.getZzjsData(limit, startTime, endTime);
        log.info(logTitle + "需汇总数据条目" + zzjsData.size());

        //按登记序号、所属账期、征收品目、征收子目分类
        final Map<String, CommonResult<ZnsbMhzcQyjbxxmxResVO>> nsrxxMap = new HashMap<>();
        Function<ZnsbTzzxHtzzDO, List<Object>> compositeKey = mxbDO ->
                Arrays.asList(mxbDO.getDjxh(),mxbDO.getSszq(),mxbDO.getZspmDm(),mxbDO.getZszmDm());
        Map<List<Object>, List<ZnsbTzzxHtzzDO>> groupingMap =
                zzjsData.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));

//        //一次性获取历史记录
//        final List<String> queryRecordKeyList = new ArrayList<>();
//        for (List<Object> keys:groupingMap.keySet()) {
//            final String djxh = (String)keys.get(0);
//            final String sszq = Integer.toString((Integer)keys.get(1));
//            final String zspmDm = (String)keys.get(2);
//            final String zszmDm = (String)keys.get(3);
//
//            queryRecordKeyList.add(djxh + zspmDm + (GyUtils.isNull(zszmDm)?"":zszmDm) + sszq);
//        }
//        final List<ZnsbTzzxHtzzDO> httzRecordList = htzzMapper.getHttzRecordList(queryRecordKeyList);
//
//        //将历史记录按照处理需要分类
        final Map<String, List<ZnsbTzzxHtzzDO>> httzRecordMap = new HashMap<>();
//        Function<ZnsbTzzxHtzzDO, List<Object>> compositeKey2 = mxbDO ->
//                Arrays.asList(mxbDO.getDjxh(),mxbDO.getZzuuid(),mxbDO.getZspmDm(),mxbDO.getZszmDm());
//        Map<List<Object>, List<ZnsbTzzxHtzzDO>> groupingMap2 =
//                httzRecordList.stream().collect(Collectors.groupingBy(compositeKey2, Collectors.toList()));
//        groupingMap2.forEach((keys, httzDOS) -> {
//            //获取参数
//            final String djxh = (String)keys.get(0);
//            final String zzuuid = (String)keys.get(1);
//            final String zspmDm = (String)keys.get(2);
//            final String zszmDm = (String)keys.get(3);
//            final String key = djxh + zzuuid + zspmDm + zszmDm;
//
//            httzRecordMap.put(key, httzDOS);
//        });
//        log.info(logTitle + "一次性查询合同台账生成总账历史纪录条目" + httzRecordList.size() + "，分类数量" + httzRecordMap.keySet().size());

        //待汇总数据按照需要进行汇总
        groupingMap.forEach((keys, httzDOS) -> {
            //获取参数
            final String djxh = (String)keys.get(0);
            final String sszq = Integer.toString((Integer)keys.get(1));
            final String zspmDm = (String)keys.get(2);
            final String zszmDm = (String)keys.get(3);


            //查询纳税人信息
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh;
            if (nsrxxMap.containsKey(djxh)) {
                //获取过的情况直接取值
                nsrxxByNsrsbh = nsrxxMap.get(djxh);
            } else {
                //未获取过的情况调用接口获取
                String systemCurrentTime1 = DateUtils.getSystemCurrentTime(14);
                ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
                nsrxxByNsrsbh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
                nsrxxMap.put(djxh, nsrxxByNsrsbh);
                String systemCurrentTime2 = DateUtils.getSystemCurrentTime(14);
                log.info(logTitle + "获取nsrxx开始时间：" + systemCurrentTime1 + ",结束时间：" + systemCurrentTime2);
            }
            List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
            //final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);

            //获取纳税期限，获取不到则默认按次
            final String nsqxDmTemp = sfzrdmxxx.stream()
                    .filter(sfzrdmxxxVO -> zspmDm.equals(sfzrdmxxxVO.getZspmDm())).map(SfzrdmxxxVO::getNsqxDm).findFirst().orElse("no sfzrd");
            final String nsqxDm = nsqxDmTemp.equals("no sfzrd")?NsqxEnum.TIME.getDm():nsqxDmTemp;
            final String sfsfzrd = nsqxDmTemp.equals("no sfzrd")?"N":"Y";

            //如果按次申报，则需要按照季度细分处理
            if (NsqxEnum.TIME.getDm().equals(nsqxDm)){
//                final Map<String, List<ZnsbTzzxHtzzDO>> collect = new HashMap<>();
//
//                //分类
//                for (ZnsbTzzxHtzzDO httzUnit:httzDOS){
//                    String skssq = DateUtils.toDateStrByFormatIndex(httzUnit.getYnspzsllsrq(), 3);
//                    if (collect.containsKey(skssq)) {
//                        collect.get(skssq).add(httzUnit);
//                    } else {
//                        final List<ZnsbTzzxHtzzDO> skssqList = new ArrayList<>();
//                        skssqList.add(httzUnit);
//                        collect.put(skssq, skssqList);
//                    }
//                }
//
//                //处理
//                for (String skssq:collect.keySet()){
//                    htzzService.httzAcsbZzsc(collect.get(skssq), djxh, sszq, zspmDm,zszmDm,nsqxDm, sfsfzrd);
//                }
                //删除印花税税源明细需重新执行生成税源流程的数据集合
                final Map<String, TzScYhsSyRequestDTO> scYhsSyMap = new HashMap<>();
                htzzService.httzAcsbZzsc(httzDOS, djxh, sszq, zspmDm,zszmDm,nsqxDm, sfsfzrd, nsrxxByNsrsbh, httzRecordMap, scYhsSyMap);
                for (String scDjxh:scYhsSyMap.keySet()) {
                    log.info(logTitle + "按次申报作废税额为0税源入参：" + JsonUtils.toJson(scYhsSyMap.get(scDjxh)));
                    yhssycjnewservice.tzScYhsSy(scYhsSyMap.get(scDjxh));
                }
            } else {
                htzzService.httzZzsc(httzDOS, djxh, sszq, zspmDm,zszmDm,nsqxDm, sfsfzrd, nsrxxByNsrsbh, httzRecordMap);
            }

        });

        log.info(logTitle + "总账计算结束");

        return true;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateHttzJyssbz(List<ZnsbTzzxHtzzDO> updateDOS) {
        htzzMapper.updateBatch(updateDOS);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void httzZzsc(List<ZnsbTzzxHtzzDO> httzDOS, String djxh, String sszq, String zspmDm, String zszmDm, String nsqxDm, String sfsfzrd, CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh, Map<String, List<ZnsbTzzxHtzzDO>> httzRecordMap) {
        //集团编码，用于过滤森马特殊规则
        final String jtbmSys = CacheUtils.getXtcs("GY00000001");

        //查询纳税人信息
        List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
        final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
        final String zgswjDm = jbxxmxsj.getZgswjDm();

        //获取税款所属期
        final Map<String, String> skssqqzMap = GjssGyUtils.calYhsSkssq(sfzrdmxxx, sszq, zspmDm, zszmDm, httzDOS.get(0).getYnspzsllsrq());
        final String skssqq = skssqqzMap.get("skssqq");
        final String skssqz = skssqqzMap.get("skssqz");
        final String sbqxlx = skssqqzMap.get("sbqxlx");

        //获取税率（2024.10.12：更新税率获取规则，先从税费种认定开始获取，获取不到再从关联表获取）
//        Map<String, Object> zspmData = CacheUtils.getTableData("dm_gy_zspm", zspmDm);
//        final BigDecimal sl = BigDecimal.valueOf((Double) zspmData.get("sl1"));
        String slStr = skssqqzMap.get("sl");//先从税费种认定里获取
        if (GyUtils.isNull(slStr)) {
            String systemCurrentTime1 = DateUtils.getSystemCurrentTime(14);
            //若税费种认定没有，使用公共方法查询表cs_gy_glb_zspm
            slStr = this.getYhsSlByRedis("10111", zspmDm, zszmDm, skssqq, skssqz, zgswjDm);
            String systemCurrentTime2 = DateUtils.getSystemCurrentTime(14);
            log.info(logTitle + "获取税率开始时间：" + systemCurrentTime1 + ",结束时间：" + systemCurrentTime2);
        }
        if (GyUtils.isNull(slStr) || "null".equals(slStr)) {
            log.info(logTitle + "合同明细按期汇总获取税率失败，参数：djxh:" + djxh + "|" + zspmDm + "|" + zszmDm + JsonUtils.toJson(skssqqzMap));
            return;
        }
        final BigDecimal sl = new BigDecimal(slStr);

        //查询表头信息
        String btxxStart = DateUtils.getSystemCurrentTime(14);
        List<ZnsbNssbCxsbtxxDO> btxx = cxsbtxxMapper.getCxsbtxx(djxh, skssqq, skssqz);
        String btxxEnd = DateUtils.getSystemCurrentTime(14);
        log.info(logTitle + "查询表头时间：" + btxxStart + "|" + btxxEnd);
        final String zbuuid;
        final String bczt;
        if (GyUtils.isNull(btxx)) {
            //如果表头信息不存在，则构建表头
            zbuuid = IdUtil.fastSimpleUUID();
            bczt = BcztConstants.DTJ;
            final ZnsbNssbCxsbtxxDO newBt = new ZnsbNssbCxsbtxxDO();
            newBt.setUuid(zbuuid);
            newBt.setDjxh(new BigDecimal(djxh));
            newBt.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
            newBt.setSbsxDm1("11");
            newBt.setSkssqq(GyUtils.cast2Date(skssqq));
            newBt.setSkssqz(GyUtils.cast2Date(skssqz));

            newBt.setZgswskfjDm(jbxxmxsj.getZgswskfjDm());
            newBt.setZfbz1("N");
            newBt.setYsbbz("N");
            newBt.setHyDm(jbxxmxsj.getHyDm());
            newBt.setXzqhszDm(jbxxmxsj.getZcdzxzqhszDm());
            newBt.setJdxzDm(jbxxmxsj.getJdxzDm());
            newBt.setBczt(BcztConstants.DTJ);

            //录入固定字段
            newBt.setLrrq(new Date());
            newBt.setXgrq(new Date());
            newBt.setLrrsfid("ZNSB.TZZX");
            newBt.setYwqdDm("TZ_SYS");
            newBt.setSjcsdq("00000000000");
            newBt.setSjgsdq("00000000000");
            newBt.setSjtbSj(new Date());

            //写入表头
            cxsbtxxMapper.insert(newBt);

            //表头首次写入进行零申报初始化
            this.yhstzLsbInit(sfzrdmxxx, zbuuid, skssqq, skssqz);
        } else {
            //如果表头存在，获取uuid
            zbuuid = btxx.get(0).getUuid();
            bczt = btxx.get(0).getBczt();

            //如果该表头当前保存状态正在处理中或者已申报（其实是申报中），不生成总账
            if (BcztConstants.CLZ.equals(btxx.get(0).getBczt()) || BcztConstants.SBZ.equals(btxx.get(0).getBczt())){
                return;
            }
        }

        //2025.04.10：合同信息分类集合，按登记序号、利润中心、合同标题、客户编号、供应商编码分类（客户编号和供应商编码不会同时存在）
        final List<String> htxxFlList = new ArrayList<>();

        //循环计算计税依据和原始凭证数量(新数据部分)
        final List<String> zzjsRecordUuidList = new ArrayList<>();
        int yspzsl = 0;
        BigDecimal jsyj = BigDecimal.ZERO;
        String ynspzbh = "";
        for (ZnsbTzzxHtzzDO httzUnit:httzDOS){
            //记录历史记录uuid
            zzjsRecordUuidList.add(httzUnit.getUuid());

            //计算计税依据
            if (!GyUtils.isNull(httzUnit.getBhsje())){
                jsyj = jsyj.add(httzUnit.getBhsje());
            } else {
                jsyj = jsyj.add(httzUnit.getHtzjk1());
            }

            //计算原始凭证数量，记录调账类型为1和4的
            if (TzlxDmEnum.WTZ.getTzlxDm().equals(httzUnit.getTzlxDm())
                    || TzlxDmEnum.XTZ.getTzlxDm().equals(httzUnit.getTzlxDm())){
                if ("000001".equals(jtbmSys) && "02".equals(httzUnit.getYwqdDm())) {
                    //2025.04.10：森马特殊规则
                    //凭证汇总合同按登记序号、利润中心、合同标题、客户编号、供应商编码分类，同类别可能有多个属期，数量记为1
                    final String key = httzUnit.getDjxh() + httzUnit.getLrzx() + httzUnit.getHtbt() + httzUnit.getKhbh() + "|" + httzUnit.getGysbm();
                    if (!htxxFlList.contains(key)) {//去重
                        htxxFlList.add(key);
                        yspzsl++;
                    }
                } else {
                    //森马以外正常加工
                    //森马：bpm和手录合同正常加工
                    yspzsl++;
                }
            }

            //记录一条合同编号
            if (GyUtils.isNull(ynspzbh) && !GyUtils.isNull(httzUnit.getHtbh())) {
                ynspzbh = httzUnit.getHtbh();
            }
        }

        //获得该类总账生成的历史记录
//        final List<ZnsbTzzxHtzzDO> zzjsRecord = httzRecordMap.get(djxh + zbuuid + zspmDm + zszmDm);
        String zzjsStart = DateUtils.getSystemCurrentTime(14);
        final List<ZnsbTzzxHtzzDO> zzjsRecord = htzzMapper.getZzjsRecord(djxh, zbuuid, zspmDm, zszmDm);
        String zzjsEnd = DateUtils.getSystemCurrentTime(14);
        log.info(logTitle + "获得总账下其他已汇总合同时间：" + zzjsStart + "|" + zzjsEnd);

        //循环计算计税依据和原始凭证数量(历史记录部分)
        for (ZnsbTzzxHtzzDO zzjsUnit:zzjsRecord){
            //修改时，新数据也会在历史记录被查询到，需要排除
            if (zzjsRecordUuidList.contains(zzjsUnit.getUuid())){
                continue;
            }

            //记录历史记录uuid
            zzjsRecordUuidList.add(zzjsUnit.getUuid());

            //计算计税依据
            if (!GyUtils.isNull(zzjsUnit.getBhsje())){
                jsyj = jsyj.add(zzjsUnit.getBhsje());
            } else {
                jsyj = jsyj.add(zzjsUnit.getHtzjk1());
            }

            //计算原始凭证数量，记录调账类型为1和4的
            if (TzlxDmEnum.WTZ.getTzlxDm().equals(zzjsUnit.getTzlxDm())
                    || TzlxDmEnum.XTZ.getTzlxDm().equals(zzjsUnit.getTzlxDm())){
                if ("000001".equals(jtbmSys) && "02".equals(zzjsUnit.getYwqdDm())) {
                    //2025.04.10：森马特殊规则
                    //凭证汇总合同按登记序号、利润中心、合同标题、客户编号、供应商编码分类，同类别可能有多个属期，数量记为1
                    final String key = zzjsUnit.getDjxh() + zzjsUnit.getLrzx() + zzjsUnit.getHtbt() + zzjsUnit.getKhbh() + "|" + zzjsUnit.getGysbm();
                    if (!htxxFlList.contains(key)) {//去重
                        htxxFlList.add(key);
                        yspzsl++;
                    }
                } else {
                    //森马以外正常加工
                    //森马：bpm和手录合同正常加工
                    yspzsl++;
                }
            }
        }

        //更新或新增总账
        final String zzuuid;
        final List<ZnsbNssbYhscjDO> yhscjJgList = new ArrayList<>();
        final List<ZnsbNssbYhscjDO> yhscjRecord = yhscjMapper.getYhscjRecord(zbuuid, zspmDm, zszmDm);
        log.info(logTitle + "税源明细查询历史记录结束时间：" + DateUtils.getSystemCurrentTime(14));
        if (GyUtils.isNull(yhscjRecord)){
            //新增
            final ZnsbNssbYhscjDO newYhscj = new ZnsbNssbYhscjDO();
            zzuuid = IdUtil.fastSimpleUUID();
            newYhscj.setUuid(zzuuid);
            newYhscj.setZbuuid(zbuuid);
            newYhscj.setZspmDm(zspmDm);
            newYhscj.setZszmDm(zszmDm);
            newYhscj.setSkssqq(GyUtils.cast2Date(skssqq));
            newYhscj.setSkssqz(GyUtils.cast2Date(skssqz));
            newYhscj.setJsjehjs(jsyj);
            newYhscj.setSl1(sl);
            newYhscj.setYnse(jsyj.multiply(sl));
            newYhscj.setYbtse(jsyj.multiply(sl));
            newYhscj.setNsqxDm(nsqxDm);
            newYhscj.setZfbz1("N");
            newYhscj.setYnspzbh(GyUtils.isNull(ynspzbh)?null:ynspzbh);
            newYhscj.setBczt(BcztConstants.DTJ);
            newYhscj.setHdbl(BigDecimal.ONE);
            newYhscj.setYsbbz("N");
            newYhscj.setYspzsl(Long.valueOf(yspzsl));
            newYhscj.setSfsfzrd(sfsfzrd);
            newYhscj.setSbqxlx(sbqxlx);//2024.12.20修改，按期取税费种认定，按次取04//00按期申报、01按次申报
            newYhscj.setYspzmc(CacheUtils.dm2mc("dm_gy_zspm", zspmDm));
            newYhscj.setYnspzsllsrq(httzDOS.get(0).getYnspzsllsrq());
            newYhscj.setSbsxdm1("11");

            //2024.12.26：税源明细应纳税额如果为0，则金额和应税凭证数量也需要置为0
            //2025.01.21：如果数值为负，也置为0
            final BigDecimal ynseSswr = newYhscj.getYnse().setScale(2, BigDecimal.ROUND_HALF_UP);
            if (jsyj.compareTo(BigDecimal.ZERO) <= 0 || ynseSswr.compareTo(BigDecimal.ZERO) <= 0) {
                newYhscj.setYspzsl(0L);
                newYhscj.setYnse(BigDecimal.ZERO);
                newYhscj.setJsjehjs(BigDecimal.ZERO);
            }

            //2024.12.27：置为0的税源明细还需要进行交易算税冲账，以保证算税也是0申报
            BigDecimal cysz = newYhscj.getJsjehjs().subtract(jsyj);//当前差异数值
            BigDecimal yjzCysz = BigDecimal.ZERO;//已校正差异数值
            final List<ZnsbTzzxHtzzDO> cyjzHtList = htzzMapper.getCyjzHtxx(zzuuid);
            for (ZnsbTzzxHtzzDO cyjzUnit:cyjzHtList) {
                yjzCysz = yjzCysz.add(cyjzUnit.getBhsje());
            }
            if (cysz.compareTo(yjzCysz) != 0) {
                //待校正差异数值
                BigDecimal djzCysz = cysz.subtract(yjzCysz);

                //生成校正虚拟合同
                final ZnsbTzzxHtzzDO htzzDO = new ZnsbTzzxHtzzDO();
                String lsrq = sszq.substring(0,4) + "-" + sszq.substring(4,6) + "-01";
                htzzDO.setUuid(IdUtil.fastSimpleUUID());
                htzzDO.setZzuuid(zzuuid);
                htzzDO.setDjxh(djxh);
                htzzDO.setSszq(Integer.parseInt(sszq));
                htzzDO.setHtbh("");
                htzzDO.setHtmc("强制零申报校正虚拟合同");
//                htzzDO.setHtydsxrq(GyUtils.isNull(htksrq)?null:DateUtils.toDate(htksrq, "yyyy-MM-dd"));
//                htzzDO.setHtydzzrq(GyUtils.isNull(htjsrq)?null:DateUtils.toDate(htjsrq, "yyyy-MM-dd"));
                htzzDO.setYnspzsllsrq(DateUtils.toDate(lsrq, "yyyy-MM-dd"));
                htzzDO.setZspmDm(zspmDm);
                htzzDO.setZszmDm(zszmDm);
                htzzDO.setBhsje(djzCysz);
                htzzDO.setFbhtbj("N");
                htzzDO.setKjhtbj("N");
                htzzDO.setCqbz("0");
                htzzDO.setScbz("0");
//                htzzDO.setLrzx(gzlrzx);
                htzzDO.setHtbt("强制零申报校正虚拟合同");
                htzzDO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
                htzzDO.setCqbz("3");

                //录入固定字段
                htzzDO.setZzscbz("N");
                htzzDO.setJyssbz("N");
                htzzDO.setLrrq(new Date());
                htzzDO.setXgrq(new Date());
                htzzDO.setLrrsfid("ZNSB.TZZX");
                htzzDO.setYwqdDm("03");
                htzzDO.setSjcsdq("00000000000");
                htzzDO.setSjgsdq("00000000000");
                htzzDO.setSjtbSj(new Date());

                htzzMapper.insertOrUpdate(htzzDO);
            }

            //录入固定字段
            newYhscj.setLrrq(new Date());
            newYhscj.setXgrq(new Date());
            newYhscj.setLrrsfid("ZNSB.TZZX");
            newYhscj.setYwqdDm("TZ_SYS");
            newYhscj.setSjcsdq("00000000000");
            newYhscj.setSjgsdq("00000000000");
            newYhscj.setSjtbSj(new Date());

            //是否已采集
            if (BcztConstants.DTJ.equals(bczt) || BcztConstants.CLSB.equals(bczt)) {
                //未采集
                newYhscj.setGzbz("0");

//                //未采集如果删除，作废标志直接改为Y
//                if (BigDecimal.ZERO.compareTo(jsyj) == 0){
//                    newYhscj.setZfbz1("Y");
//                    newYhscj.setZfrq1(new Date());
//                }

            } else {
                //2024.08.15新增：删除一条税源且还未点击生成税源时，页面显示改条税源，金额均为0，变更状态为已变更，高亮显示
                //已生成税源时，所有修改都为"已变更"
                newYhscj.setBgzt("Y");

                //将gzbz置为0(新增)
                newYhscj.setGzbz("0");

                //已采集，则将其他条目gzbz置为1
                List<ZnsbNssbYhscjDO> otheryhscjDO = yhscjMapper.getYhscjByZbuuid(zbuuid);
                for (ZnsbNssbYhscjDO unit:otheryhscjDO){
                    //已经是新增未处理的或删除的不进行修改
                    if (unit.getGzbz().equals("0") || unit.getGzbz().equals("2")){
                        continue;
                    }

                    unit.setGzbz("1");
//                    unit.setBczt(BcztConstants.DTJ);
                    yhscjJgList.add(unit);
                }
            }

            yhscjJgList.add(newYhscj);
        } else {
            //修改
            zzuuid = yhscjRecord.get(0).getUuid();
            yhscjRecord.get(0).setJsjehjs(jsyj);
            yhscjRecord.get(0).setYnse(jsyj.multiply(sl));
            yhscjRecord.get(0).setYbtse(jsyj.multiply(sl));
            yhscjRecord.get(0).setYspzsl(Long.valueOf(yspzsl));
//            yhscjRecord.get(0).setBczt(BcztConstants.DTJ);
            yhscjRecord.get(0).setXgrq(new Date());
            yhscjRecord.get(0).setXgrsfid("ZNSB.TZZX");
            if (GyUtils.isNull(yhscjRecord.get(0).getYnspzbh()) && !GyUtils.isNull(ynspzbh)) {
                yhscjRecord.get(0).setYnspzbh(ynspzbh);
            }

            //2024.12.26：税源明细应纳税额如果为0，则应税凭证数量也需要置为0
            final BigDecimal ynseSswr = yhscjRecord.get(0).getYnse().setScale(2, BigDecimal.ROUND_HALF_UP);
            if (jsyj.compareTo(BigDecimal.ZERO) <= 0 || ynseSswr.compareTo(BigDecimal.ZERO) <= 0) {
                yhscjRecord.get(0).setYspzsl(0L);
                yhscjRecord.get(0).setYnse(BigDecimal.ZERO);
                yhscjRecord.get(0).setJsjehjs(BigDecimal.ZERO);
            }
            //2024.12.27：置为0的税源明细还需要进行交易算税冲账，以保证算税也是0申报
            BigDecimal cysz = yhscjRecord.get(0).getJsjehjs().subtract(jsyj);//当前差异数值
            BigDecimal yjzCysz = BigDecimal.ZERO;//已校正差异数值
            final List<ZnsbTzzxHtzzDO> cyjzHtList = htzzMapper.getCyjzHtxx(zzuuid);
            for (ZnsbTzzxHtzzDO cyjzUnit:cyjzHtList) {
                yjzCysz = yjzCysz.add(cyjzUnit.getBhsje());
            }
            if (cysz.compareTo(yjzCysz) != 0) {
                //待校正差异数值
                BigDecimal djzCysz = cysz.subtract(yjzCysz);

                //生成校正虚拟合同
                final ZnsbTzzxHtzzDO htzzDO = new ZnsbTzzxHtzzDO();
                String lsrq = sszq.substring(0,4) + "-" + sszq.substring(4,6) + "-01";
                htzzDO.setUuid(IdUtil.fastSimpleUUID());
                htzzDO.setZzuuid(zzuuid);
                htzzDO.setDjxh(djxh);
                htzzDO.setSszq(Integer.parseInt(sszq));
                htzzDO.setHtbh("");
                htzzDO.setHtmc("强制零申报校正虚拟合同");
//                htzzDO.setHtydsxrq(GyUtils.isNull(htksrq)?null:DateUtils.toDate(htksrq, "yyyy-MM-dd"));
//                htzzDO.setHtydzzrq(GyUtils.isNull(htjsrq)?null:DateUtils.toDate(htjsrq, "yyyy-MM-dd"));
                htzzDO.setYnspzsllsrq(DateUtils.toDate(lsrq, "yyyy-MM-dd"));
                htzzDO.setZspmDm(zspmDm);
                htzzDO.setZszmDm(zszmDm);
                htzzDO.setBhsje(djzCysz);
                htzzDO.setFbhtbj("N");
                htzzDO.setKjhtbj("N");
                htzzDO.setScbz("0");
//                htzzDO.setLrzx(gzlrzx);
                htzzDO.setHtbt("强制零申报校正虚拟合同");
                htzzDO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
                htzzDO.setCqbz("3");

                //录入固定字段
                htzzDO.setZzscbz("N");
                htzzDO.setJyssbz("N");
                htzzDO.setLrrq(new Date());
                htzzDO.setXgrq(new Date());
                htzzDO.setLrrsfid("ZNSB.TZZX");
                htzzDO.setYwqdDm("03");
                htzzDO.setSjcsdq("00000000000");
                htzzDO.setSjgsdq("00000000000");
                htzzDO.setSjtbSj(new Date());

                htzzMapper.insertOrUpdate(htzzDO);
            }

            //是否已采集
            if (BcztConstants.DTJ.equals(bczt) || BcztConstants.CLSB.equals(bczt)) {
                //未采集
                yhscjRecord.get(0).setGzbz("0");

//                //未采集如果删除，作废标志直接改为Y
//                if (BigDecimal.ZERO.compareTo(jsyj) == 0){
//                    yhscjRecord.get(0).setZfbz1("Y");
//                    yhscjRecord.get(0).setZfrq1(new Date());
//                }

            } else if (BcztConstants.SBCG.equals(bczt)) {
                //已申报的，保存状态保持已申报
                yhscjRecord.get(0).setBgzt("Y");
                yhscjRecord.get(0).setGzbz("1");
            } else {
                //2024.08.15新增：删除一条税源且还未点击生成税源时，页面显示改条税源，金额均为0，变更状态为已变更，高亮显示
                //已生成税源时，所有修改都为"已变更"
                yhscjRecord.get(0).setBgzt("Y");


                //查看本条记录是否为删除、是否已经生成税源
//                if (BigDecimal.ZERO.compareTo(jsyj) == 0){
//                    //记录为删除
//                    if (GyUtils.isNull(yhscjRecord.get(0).getHxbuuid())){
//                        //hxbuuid若为空，则未生成税源，直接删除
//                        yhscjRecord.get(0).setZfbz1("Y");
//                        yhscjRecord.get(0).setZfrq1(new Date());
//                    } else {
//                        //hxbuuid若不为空，则已生成税源，进行删除更正，将gzbz置为2(删除)
//                        yhscjRecord.get(0).setGzbz("2");
//                    }
//                } else {
                //正常更正
                yhscjRecord.get(0).setGzbz("1");
//                }

                //如果不为直接删除，则需改变其他条目更正状态
                if (!"Y".equals(yhscjRecord.get(0).getZfbz1())){
                    //已采集，则将其他条目gzbz置为1
                    List<ZnsbNssbYhscjDO> otheryhscjDO = yhscjMapper.getYhscjByZbuuid(zbuuid);
                    for (ZnsbNssbYhscjDO unit:otheryhscjDO){
                        //除去修改中的数据自身
                        if (yhscjRecord.get(0).getUuid().equals(unit.getUuid())) {
                            continue;
                        }

                        //已经是新增未处理的不进行修改
                        if (unit.getGzbz().equals("0")){
                            continue;
                        }

                        unit.setGzbz("1");
//                        unit.setBczt(BcztConstants.DTJ);
                        yhscjJgList.add(unit);
                    }
                }
            }

            yhscjJgList.add(yhscjRecord.get(0));
        }
        log.info(logTitle + "汇总总账新增和修改条目结束时间：" + DateUtils.getSystemCurrentTime(14));

        //写入总账
        yhscjMapper.insertOrUpdateBatch(yhscjJgList);
        log.info(logTitle + "新增或更新总账" + yhscjJgList.size());
//        //将本次新增记录也计入历史记录，以使其他账期使用时计算的金额总数正确
//        httzRecordMap.get(djxh + zbuuid + zspmDm + zszmDm).addAll(httzDOS);

        //更新合同台账总账生成标志
        for (ZnsbTzzxHtzzDO httzUnit:httzDOS){
            httzUnit.setZzscbz("Y");
            httzUnit.setZzuuid(zbuuid);
        }
        htzzMapper.updateBatch(httzDOS);
        log.info(logTitle + "变更总账生成标志" + httzDOS.size());

        //同步立法可变列，将主表uuid置为表头uuid,syuuid置为yhscj的uuid
        List<ZnsbTzzxYhslfkblDO> allYhslfkbl = yhslfkblMapper.getYhslfkblByZbuuidList(zzjsRecordUuidList);
        List<ZnsbNssbYhslfkblDO> nssbYhslfkblDOList = new ArrayList<>();
        for (ZnsbTzzxYhslfkblDO lfkblUnit:allYhslfkbl){
            ZnsbNssbYhslfkblDO nssbYhslfkblDO = BeanUtils.toBean(lfkblUnit, ZnsbNssbYhslfkblDO.class);
            nssbYhslfkblDO.setZbuuid(zbuuid);
            nssbYhslfkblDO.setSyuuid(zzuuid);
            nssbYhslfkblDO.setZfbz1("N");
            nssbYhslfkblDOList.add(nssbYhslfkblDO);
        }

        nssbYhslfkblMapper.insertOrUpdateBatch(nssbYhslfkblDOList);
        log.info(logTitle + "新增或更新立法可变列" + nssbYhslfkblDOList.size());
    }


    /**
     * @param httzDOS
     * @param djxh
     * @param sszq
     * @param zspmDm
     * @param zszmDm
     * @param nsqxDm
     * @param sfsfzrd
     * @name 合同台账按次申报合同生成总账
     * @description 相关说明
     * @time 创建时间:2024年11月26日下午05:20:09
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void httzAcsbZzsc(List<ZnsbTzzxHtzzDO> httzDOS, String djxh, String sszq, String zspmDm, String zszmDm, String nsqxDm, String sfsfzrd, CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh, Map<String, List<ZnsbTzzxHtzzDO>> httzRecordMap, Map<String, TzScYhsSyRequestDTO> scYhsSyMap) {
        //集团编码，用于过滤森马特殊规则
        final String jtbmSys = CacheUtils.getXtcs("GY00000001");

        //查询纳税人信息
        List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
        final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
        final String zgswjDm = jbxxmxsj.getZgswjDm();
        final String nsrsbh = jbxxmxsj.getNsrsbh();

        //2025.07.25:不同企业可能对按次申报汇总范围的需求不同，此处更改为系统参数配置，参数值参考dm_gy_nsqx，默认为按季汇总
        final String mode = CacheUtils.getXtcs("YHS_ACSB_HZQX");
        //获取汇总范围开始、结束时间
        final Map<String, String> skssqMap = this.getAcsbHzsjqz(sszq, mode);
        final String skssqq = skssqMap.get("skssqq");
        final String skssqz = skssqMap.get("skssqz");

        BigDecimal sl = sfzrdmxxx.stream()
                .filter(sfzrdmxxxVO -> (zspmDm.equals(sfzrdmxxxVO.getZspmDm())) && (GyUtils.isNull(zszmDm) || zszmDm.equals(sfzrdmxxxVO.getZszmDm()))).map(SfzrdmxxxVO::getSlhdwse).findFirst().orElse(null);

        //获取税率（2024.10.12：更新税率获取规则，先从税费种认定开始获取，获取不到再从关联表获取）
        if (GyUtils.isNull(sl)) {
            //若税费种认定没有，使用公共方法查询表cs_gy_glb_zspm
            String systemCurrentTime1 = DateUtils.getSystemCurrentTime(14);
            final GyJsSfzVO gyJsSfzVO = new GyJsSfzVO();
            gyJsSfzVO.setZsxmDm("10111");
            gyJsSfzVO.setZspmDm(zspmDm);
            gyJsSfzVO.setZszmDm(zszmDm);
            gyJsSfzVO.setSkssqq(skssqq);
            gyJsSfzVO.setSkssqz(skssqz);
            gyJsSfzVO.setSjjg(GyUtils.isNull(zgswjDm)?"00000000000":zgswjDm);

            final String slStr = this.getYhsSlByRedis("10111", zspmDm, zszmDm, skssqq, skssqz, zgswjDm);

            if (GyUtils.isNull(slStr) || "null".equals(slStr)) {
                log.info(logTitle + "合同明细按次汇总获取税率失败，参数：djxh:" + djxh + JsonUtils.toJson(gyJsSfzVO));
                return;
            }

            sl = new BigDecimal(slStr);
            String systemCurrentTime2 = DateUtils.getSystemCurrentTime(14);
            log.info(logTitle + "获取税率开始时间：" + systemCurrentTime1 + ",结束时间：" + systemCurrentTime2);
        }

        //查询同类台账是否存在
        String systemCurrentTime1 = DateUtils.getSystemCurrentTime(14);
        final List<ZnsbNssbYhscjDO> yhscjRecord = yhscjMapper.getYhscjAcsbWscsyRecord(djxh, skssqz, zspmDm, zszmDm);
        String systemCurrentTime2 = DateUtils.getSystemCurrentTime(14);
        log.info(logTitle + "查询同类台账开始时间：" + systemCurrentTime1 + ",结束时间：" + systemCurrentTime2);

        //2025.04.10：合同信息分类集合，按登记序号、利润中心、合同标题、客户编号、供应商编码分类（客户编号和供应商编码不会同时存在）
        final List<String> htxxFlList = new ArrayList<>();

        //循环计算计税依据和原始凭证数量(新数据部分)
        final List<String> zzjsRecordUuidList = new ArrayList<>();
        int yspzsl = 0;
        BigDecimal jsyj = BigDecimal.ZERO;
        String ynspzbh = "";
        for (ZnsbTzzxHtzzDO httzUnit:httzDOS){
            //记录历史记录uuid
            zzjsRecordUuidList.add(httzUnit.getUuid());

            //计算计税依据
            if (!GyUtils.isNull(httzUnit.getBhsje())){
                jsyj = jsyj.add(httzUnit.getBhsje());
            } else {
                jsyj = jsyj.add(httzUnit.getHtzjk1());
            }

            //计算原始凭证数量，记录调账类型为1和4的
            if (TzlxDmEnum.WTZ.getTzlxDm().equals(httzUnit.getTzlxDm())
                    || TzlxDmEnum.XTZ.getTzlxDm().equals(httzUnit.getTzlxDm())){
                if (JtbmEnum.SM.getJtbm().equals(jtbmSys) && "02".equals(httzUnit.getYwqdDm())) {
                    //2025.04.10：森马特殊规则
                    //凭证汇总合同按登记序号、利润中心、合同标题、客户编号、供应商编码分类，同类别可能有多个属期，数量记为1
                    final String key = httzUnit.getDjxh() + httzUnit.getLrzx() + httzUnit.getHtbt() + httzUnit.getKhbh() + "|" + httzUnit.getGysbm();
                    if (!htxxFlList.contains(key)) {//去重
                        htxxFlList.add(key);
                        yspzsl++;
                    }
                } else {
                    //森马以外正常加工
                    //森马：bpm和手录合同正常加工
                    yspzsl++;
                }
            }

            //记录一条合同编号
            if (GyUtils.isNull(ynspzbh) && !GyUtils.isNull(httzUnit.getHtbh())) {
                ynspzbh = httzUnit.getHtbh();
            }
        }

        //获得该类总账生成的历史记录
        if (!GyUtils.isNull(yhscjRecord)) {
//            final List<ZnsbTzzxHtzzDO> zzjsRecord = httzRecordMap.get(djxh + yhscjRecord.get(0).getUuid() + zspmDm + zszmDm);
            String zzjsStart = DateUtils.getSystemCurrentTime(14);
            final List<ZnsbTzzxHtzzDO> zzjsRecord = htzzMapper.getZzjsRecord(djxh, yhscjRecord.get(0).getUuid(), zspmDm, zszmDm);
            String zzjsEnd = DateUtils.getSystemCurrentTime(14);
            log.info(logTitle + "获得总账下其他已汇总合同时间：" + zzjsStart + "|" + zzjsEnd);

            //循环计算计税依据和原始凭证数量(历史记录部分)
            for (ZnsbTzzxHtzzDO zzjsUnit:zzjsRecord){
                //修改时，新数据也会在历史记录被查询到，需要排除
                if (zzjsRecordUuidList.contains(zzjsUnit.getUuid())){
                    continue;
                }

                //记录历史记录uuid
                zzjsRecordUuidList.add(zzjsUnit.getUuid());

                //计算计税依据
                if (!GyUtils.isNull(zzjsUnit.getBhsje())){
                    jsyj = jsyj.add(zzjsUnit.getBhsje());
                } else {
                    jsyj = jsyj.add(zzjsUnit.getHtzjk1());
                }

                //计算原始凭证数量，记录调账类型为1和4的
                if (TzlxDmEnum.WTZ.getTzlxDm().equals(zzjsUnit.getTzlxDm())
                        || TzlxDmEnum.XTZ.getTzlxDm().equals(zzjsUnit.getTzlxDm())){
                    if ("000001".equals(jtbmSys) && "02".equals(zzjsUnit.getYwqdDm())) {
                        //2025.04.10：森马特殊规则
                        //凭证汇总合同按登记序号、利润中心、合同标题、客户编号、供应商编码分类，同类别可能有多个属期，数量记为1
                        final String key = zzjsUnit.getDjxh() + zzjsUnit.getLrzx() + zzjsUnit.getHtbt() + zzjsUnit.getKhbh() + "|" + zzjsUnit.getGysbm();
                        if (!htxxFlList.contains(key)) {//去重
                            htxxFlList.add(key);
                            yspzsl++;
                        }
                    } else {
                        //森马以外正常加工
                        //森马：bpm和手录合同正常加工
                        yspzsl++;
                    }
                }
            }
        }

        //判断台账是否存在，若存在，则修改，否则新增
        log.info(logTitle + "按次申报开始汇总税源明细：" + DateUtils.getSystemCurrentTime(14));
        final String zzuuid;
        final List<ZnsbNssbYhscjDO> yhscjJgList = new ArrayList<>();
        if (GyUtils.isNull(yhscjRecord)){
            //新增
            final ZnsbNssbYhscjDO newYhscj = new ZnsbNssbYhscjDO();
            zzuuid = IdUtil.fastSimpleUUID();
            newYhscj.setUuid(zzuuid);
            newYhscj.setZbuuid("");
            newYhscj.setZspmDm(zspmDm);
            newYhscj.setZszmDm(zszmDm);
//            newYhscj.setSkssqq(GyUtils.cast2Date(skssqq));
//            newYhscj.setSkssqz(GyUtils.cast2Date(skssqz));
            newYhscj.setJsjehjs(jsyj);
            newYhscj.setSl1(sl);
            newYhscj.setYnse(jsyj.multiply(sl));
            newYhscj.setYbtse(jsyj.multiply(sl));
            newYhscj.setNsqxDm(nsqxDm);
            newYhscj.setZfbz1("N");
            newYhscj.setBczt(BcztConstants.DTJ);
            newYhscj.setHdbl(BigDecimal.ONE);
            newYhscj.setYsbbz("N");
            newYhscj.setYspzsl(Long.valueOf(yspzsl));
            newYhscj.setYnspzbh(GyUtils.isNull(ynspzbh)?null:ynspzbh);
            newYhscj.setSfsfzrd(sfsfzrd);
            newYhscj.setSbqxlx("04");//2024.12.20修改，按期取税费种认定，按次取04//00按期申报、01按次申报
            newYhscj.setYspzmc(CacheUtils.dm2mc("dm_gy_zspm", zspmDm));
            newYhscj.setYnspzsllsrq(GyUtils.cast2Date(skssqz));
            newYhscj.setSsrq1(GyUtils.cast2Date(skssqz));
            newYhscj.setSbsxdm1("11");
            newYhscj.setDjxh(djxh);

//            //2024.12.26：税源明细应纳税额如果为0，按次申报不生成
//            //2025.01.21：相关逻辑修改为：正常生成税源明细，小于等于0时不查询
//            final BigDecimal ynseSswr = newYhscj.getYnse().setScale(2, BigDecimal.ROUND_HALF_UP);
//            if (jsyj.compareTo(BigDecimal.ZERO) == 0 || ynseSswr.compareTo(BigDecimal.ZERO) == 0) {
//                return;
//            }

            //录入固定字段
            newYhscj.setLrrq(new Date());
            newYhscj.setXgrq(new Date());
            newYhscj.setLrrsfid("ZNSB.TZZX");
            newYhscj.setYwqdDm("TZ_SYS");
            newYhscj.setSjcsdq("00000000000");
            newYhscj.setSjgsdq("00000000000");
            newYhscj.setSjtbSj(new Date());
            newYhscj.setGzbz("0");

            yhscjJgList.add(newYhscj);
        } else {
            //修改
            zzuuid = yhscjRecord.get(0).getUuid();
            yhscjRecord.get(0).setJsjehjs(jsyj);
            yhscjRecord.get(0).setYnse(jsyj.multiply(sl));
            yhscjRecord.get(0).setYbtse(jsyj.multiply(sl));
            yhscjRecord.get(0).setYspzsl(Long.valueOf(yspzsl));
//            if (!BcztConstants.SBCG.equals(yhscjRecord.get(0).getBczt())) {
//                yhscjRecord.get(0).setBczt(BcztConstants.DTJ);
//            }
            yhscjRecord.get(0).setXgrq(new Date());
            yhscjRecord.get(0).setXgrsfid("ZNSB.TZZX");
            yhscjRecord.get(0).setGzbz("0");
            if (GyUtils.isNull(yhscjRecord.get(0).getYnspzbh()) && !GyUtils.isNull(ynspzbh)) {
                yhscjRecord.get(0).setYnspzbh(ynspzbh);
            }

            //是否已采集
            String zbuuid = yhscjRecord.get(0).getZbuuid();
            if (!GyUtils.isNull(zbuuid)) {
                ZnsbNssbCxsbtxxDO btxx = cxsbtxxMapper.selectById(zbuuid);

                //如果该表头当前保存状态正在处理中或者已申报（其实是申报中），不生成总账
                if (BcztConstants.CLZ.equals(btxx.getBczt()) || BcztConstants.SBZ.equals(btxx.getBczt())){
                    return;
                }

                if (BcztConstants.DTJ.equals(btxx.getBczt()) || BcztConstants.CLSB.equals(btxx.getBczt())) {
                    //未采集
                    yhscjRecord.get(0).setGzbz("0");

                } else if (BcztConstants.SBCG.equals(btxx.getBczt())) {
                    //已申报的，保存状态保持已申报
                    yhscjRecord.get(0).setBgzt("Y");
                    yhscjRecord.get(0).setGzbz("1");
                } else {
                    //2024.08.15新增：删除一条税源且还未点击生成税源时，页面显示改条税源，金额均为0，变更状态为已变更，高亮显示
                    //已生成税源时，所有修改都为"已变更"
                    yhscjRecord.get(0).setBgzt("Y");


                    //正常更正
                    yhscjRecord.get(0).setGzbz("1");
//                }

                    //2025.07.17：山西移动规则：如果已生成税源，且本次税额变更为小于0.005，则需要删除税源
                    if (JtbmEnum.SXYD.getJtbm().equals(jtbmSys)
                            && BcztConstants.CLCG.equals(btxx.getBczt())
                            && new BigDecimal(0.005).compareTo(jsyj.multiply(sl)) > 0) {
                        yhscjRecord.get(0).setBgzt("Y");
                        yhscjRecord.get(0).setGzbz("2");

                        if (scYhsSyMap.containsKey(djxh)) {
                            final TzScYhsSyRequestDTO reqDTO = scYhsSyMap.get(djxh);
                            final TzScYhsSySkssqqzDTO skssqqz = new TzScYhsSySkssqqzDTO();
                            skssqqz.setSkssqq(yhscjRecord.get(0).getSkssqq());
                            skssqqz.setSkssqz(yhscjRecord.get(0).getSkssqz());
                            reqDTO.getSkssqqzList().add(skssqqz);
                        } else {
                            final TzScYhsSyRequestDTO reqDTO = new TzScYhsSyRequestDTO();
                            reqDTO.setDjxh(djxh);
                            reqDTO.setNsrsbh(nsrsbh);

                            final List<TzScYhsSySkssqqzDTO> skssqqzList = new ArrayList<>();
                            reqDTO.setSkssqqzList(skssqqzList);
                            final TzScYhsSySkssqqzDTO skssqqz = new TzScYhsSySkssqqzDTO();
                            skssqqz.setSkssqq(yhscjRecord.get(0).getSkssqq());
                            skssqqz.setSkssqz(yhscjRecord.get(0).getSkssqz());
                            skssqqzList.add(skssqqz);

                            scYhsSyMap.put(djxh, reqDTO);
                        }
                    }

                    //如果不为直接删除，则需改变其他条目更正状态
                    if (!"Y".equals(yhscjRecord.get(0).getZfbz1())){
                        //已采集，则将其他条目gzbz置为1
                        List<ZnsbNssbYhscjDO> otheryhscjDO = yhscjMapper.getYhscjByZbuuid(zbuuid);
                        for (ZnsbNssbYhscjDO unit:otheryhscjDO){
                            //除去修改中的数据自身
                            if (yhscjRecord.get(0).getUuid().equals(unit.getUuid())) {
                                continue;
                            }

                            //已经是新增未处理的不进行修改
                            if (unit.getGzbz().equals("0")){
                                continue;
                            }

                            unit.setGzbz("1");
//                            unit.setBczt(BcztConstants.DTJ);
                            yhscjJgList.add(unit);
                        }
                    }
                }
            }

            yhscjJgList.add(yhscjRecord.get(0));
        }
        log.info(logTitle + "按次申报结束汇总税源明细：" + DateUtils.getSystemCurrentTime(14));

        //写入总账
        if (!GyUtils.isNull(yhscjJgList)) {
            yhscjMapper.insertOrUpdateBatch(yhscjJgList);
        }
        log.info(logTitle + "按次申报新增或更新总账" + yhscjJgList.size());
//        //将本次新增记录也计入历史记录，以使其他账期使用时计算的金额总数正确
//        httzRecordMap.get(djxh + yhscjRecord.get(0).getUuid() + zspmDm + zszmDm).addAll(httzDOS);

        //更新合同台账总账生成标志
        for (ZnsbTzzxHtzzDO httzUnit:httzDOS){
            httzUnit.setZzscbz("Y");
            httzUnit.setZzuuid(zzuuid);
        }
        htzzMapper.updateBatch(httzDOS);
        log.info(logTitle + "按次申报变更总账生成标志" + httzDOS.size());

        //同步立法可变列，将zzuuid置为yhscj的uuid
        List<ZnsbTzzxYhslfkblDO> allYhslfkbl = yhslfkblMapper.getYhslfkblByZbuuidList(zzjsRecordUuidList);
        List<ZnsbNssbYhslfkblDO> nssbYhslfkblDOList = new ArrayList<>();
        for (ZnsbTzzxYhslfkblDO lfkblUnit:allYhslfkbl){
            ZnsbNssbYhslfkblDO nssbYhslfkblDO = BeanUtils.toBean(lfkblUnit, ZnsbNssbYhslfkblDO.class);
            nssbYhslfkblDO.setZbuuid("");
            nssbYhslfkblDO.setSyuuid(zzuuid);
            nssbYhslfkblDO.setZfbz1("N");
            nssbYhslfkblDOList.add(nssbYhslfkblDO);
        }

        nssbYhslfkblMapper.insertOrUpdateBatch(nssbYhslfkblDOList);
        log.info(logTitle + "按次申报新增或更新立法可变列" + nssbYhslfkblDOList.size());
    }

    //根据汇总模式计算按次申报汇总范围起止
    private Map<String, String> getAcsbHzsjqz(String sszq, String mode) {
        final Map<String, String> resultMap = new HashMap<>();

        if (NsqxEnum.MONTH.getDm().equals(mode)) {
            //按月
            final String skssqq = sszq.substring(0, 4) + "-" + sszq.substring(4, 6) + "-01";
            final String skssqz = NsqxUtils.getLastDayOfMonth(skssqq);
            resultMap.put("skssqq", skssqq);
            resultMap.put("skssqz", skssqz);
        } else if (NsqxEnum.HALF_YEAR.getDm().equals(mode)) {
            //按半年
            final String ssnf = sszq.substring(0, 4);
            final String ssyf = sszq.substring(4, 6);
            if (Arrays.asList("01","02","03","04","05","06").contains(ssyf)) {
                //上半年
                resultMap.put("skssqq", ssnf + "-01-01");
                resultMap.put("skssqz", ssnf + "-06-30");
            } else {
                //下半年
                resultMap.put("skssqq", ssnf + "-07-01");
                resultMap.put("skssqz", ssnf + "-12-31");
            }
        } else if (NsqxEnum.YEAR.getDm().equals(mode)) {
            //按年
            final String ssnf = sszq.substring(0, 4);
            resultMap.put("skssqq", ssnf + "-01-01");
            resultMap.put("skssqz", ssnf + "-12-31");
        } else {
            //默认按季
            final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            final LocalDate date = LocalDate.parse(sszq + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
            final String skssqq = dateTimeFormatter.format(this.getStartOrEndDayOfQuarter(date,true));
            final String skssqz = dateTimeFormatter.format(this.getStartOrEndDayOfQuarter(date,false));
            resultMap.put("skssqq", skssqq);
            resultMap.put("skssqz", skssqz);
        }

        log.info("印花税按次申报合同获取聚合范围，模式：" + mode + ",所属账期" + sszq + ",范围" + JsonUtils.toJson(resultMap));

        return resultMap;
    }

    /**
     * @param sfzrdmxxx
     * @param zbuuid
     * @param skssqq
     * @param skssqz
     * @name 印花税台账零申报初始化
     * @description 相关说明
     * @time 创建时间:2024年11月21日下午08:45:55
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public void yhstzLsbInit(List<SfzrdmxxxVO> sfzrdmxxx, String zbuuid, String skssqq, String skssqz) {
        final List<ZnsbNssbYhscjDO> yhscjLsbList = new ArrayList<>();

        //去重集合
        final List<String> zspmQcList = new ArrayList<>();
        final List<String> zszmQcList = new ArrayList<>();

        //循环构筑零申报条目
        for (SfzrdmxxxVO sfzrd:sfzrdmxxx) {
            //获取认定信息
            final String zsxmDm = sfzrd.getZsxmDm();
            final String zspmDm = sfzrd.getZspmDm();
            final String zszmDm = sfzrd.getZszmDm();
            final String nsqxDm = sfzrd.getNsqxDm();
            final String sbqxlx = sfzrd.getSbqxDm();
            final Date rdyxqq = sfzrd.getRdyxqq();
            final Date rdyxqz = sfzrd.getRdyxqz();
            final BigDecimal sl = sfzrd.getSlhdwse();

            //印花税季报进行零申报初始化
            if ("10111".equals(zsxmDm) && "08".equals(nsqxDm)){
                //去重(使用征收品目，如征收子目存在，也使用征收子目)
                if (zspmQcList.contains(zspmDm) &&
                        ((!GyUtils.isNull(zszmDm) && zszmQcList.contains(zszmDm)) || GyUtils.isNull(zszmDm))) {
                    continue;
                } else {
                    //2025.02.07：不在算税原始凭证类型映射范围的征收品目不生成零申报，以防存在范围以外印花税认定情况
                    if (GyUtils.isNull(YspzlxEnum.getYspzlxByZspm(zspmDm))) {
                        continue;
                    }

                    //2025.04.10：当前属期不在认定有效期限内的征收品目不生成零申报，以防存在超期或未开始的认定，认定时间不全的也略过
                    if (GyUtils.isNull(rdyxqq) || GyUtils.isNull(rdyxqz)
                            || rdyxqq.compareTo(GyUtils.cast2Date(skssqq)) > 0 || rdyxqz.compareTo(GyUtils.cast2Date(skssqz)) < 0) {
                        continue;
                    }

                    zspmQcList.add(zspmDm);
                    if (!GyUtils.isNull(zszmDm)) {
                        zszmQcList.add(zszmDm);
                    }
                }

                //构造数据
                final ZnsbNssbYhscjDO newYhscj = new ZnsbNssbYhscjDO();
                newYhscj.setUuid(IdUtil.fastSimpleUUID());
                newYhscj.setZbuuid(zbuuid);
                newYhscj.setZspmDm(zspmDm);
                newYhscj.setZszmDm(GyUtils.isNull(zszmDm)?"":zszmDm);
                newYhscj.setSkssqq(GyUtils.cast2Date(skssqq));
                newYhscj.setSkssqz(GyUtils.cast2Date(skssqz));
                newYhscj.setJsjehjs(BigDecimal.ZERO);
                newYhscj.setSl1(sl);
                newYhscj.setYnse(BigDecimal.ZERO);
                newYhscj.setYbtse(BigDecimal.ZERO);
                newYhscj.setNsqxDm(nsqxDm);
                newYhscj.setZfbz1("N");
                newYhscj.setBczt(BcztConstants.DTJ);
                newYhscj.setHdbl(BigDecimal.ONE);
                newYhscj.setYsbbz("N");
                newYhscj.setYspzsl(0L);
                newYhscj.setSfsfzrd("Y");
                newYhscj.setSbqxlx(sbqxlx);//2024.12.20修改，按期取税费种认定，按次取04//00按期申报、01按次申报
                newYhscj.setYspzmc(CacheUtils.dm2mc("dm_gy_zspm", zspmDm));
                newYhscj.setYnspzsllsrq(DateUtils.strToDate(skssqq));
                newYhscj.setSbsxdm1("11");
                newYhscj.setJyssbz("N");

                //录入固定字段
                newYhscj.setLrrq(new Date());
                newYhscj.setXgrq(new Date());
                newYhscj.setLrrsfid("ZNSB.TZZX");
                newYhscj.setYwqdDm("TZ_SYS");
                newYhscj.setSjcsdq("00000000000");
                newYhscj.setSjgsdq("00000000000");
                newYhscj.setSjtbSj(new Date());
                newYhscj.setGzbz("0");//未采集

                yhscjLsbList.add(newYhscj);
            }
        }

        //写入总账
        yhscjMapper.insertOrUpdateBatch(yhscjLsbList);

        log.info(logTitle + "零申报初始化" + yhscjLsbList.size());
    }

    /**
     * @param sszq
     * @name 零申报补偿
     * @description 相关说明
     * @time 创建时间:2025年04月10日上午10:06:31
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    @Transactional
    public void yhsLsbBc(String sszq, String djxhReq){
        log.info(logTitle + "零申报初始化开始，所属账期：" + sszq + "，登记序号：" + djxhReq);

        //所属账期为空，生成当前月份
        if (GyUtils.isNull(sszq)) {
            sszq = DateUtils.getSystemCurrentTime(17);
        }

        //获取上季开始、结束时间
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        final LocalDate thisQuarterDay = LocalDate.parse(sszq + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        final LocalDate lastQuarterDay = thisQuarterDay.plusMonths(-3);
        final String skssqq = dateTimeFormatter.format(this.getStartOrEndDayOfQuarter(lastQuarterDay,true));
        final String skssqz = dateTimeFormatter.format(this.getStartOrEndDayOfQuarter(lastQuarterDay,false));
        log.info(logTitle + "零申报初始化属期为" + skssqq + "到" + skssqz);

        //如果未传入djxh则获取所有djxh
        List<String> djxhList = new ArrayList<>();
        if (GyUtils.isNull(djxhReq)) {
            djxhList = companyApi.getAllDjxh().getData();
        } else {
            djxhList.add(djxhReq);
        }
        log.info(logTitle + "零申报初始化登记序号列表数量为" + djxhList.size());

        //逐个处理
        for (String djxh:djxhList) {
            //查询表头信息
            List<ZnsbNssbCxsbtxxDO> btxx = cxsbtxxMapper.getCxsbtxx(djxh, skssqq, skssqz);

            //如果没有表头，进行零申报初始化
            if (GyUtils.isNull(btxx)) {
                //查询纳税人信息
                final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
                final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
                List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
                final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);

                //过滤有效税费种认定
                final List<SfzrdmxxxVO> yhsYxSrzrdxxList = new ArrayList<>();
                for (SfzrdmxxxVO sfzrd:sfzrdmxxx) {
                    //获取认定信息
                    final String zsxmDm = sfzrd.getZsxmDm();
                    final String zspmDm = sfzrd.getZspmDm();
                    final String nsqxDm = sfzrd.getNsqxDm();
                    final Date rdyxqq = sfzrd.getRdyxqq();
                    final Date rdyxqz = sfzrd.getRdyxqz();

                    //印花税季报进行零申报初始化
                    if ("10111".equals(zsxmDm) && "08".equals(nsqxDm)){
                        //不在算税原始凭证类型映射范围的征收品目筛除
                        if (GyUtils.isNull(YspzlxEnum.getYspzlxByZspm(zspmDm))) {
                            continue;
                        }

                        //筛除不在当前生效的认定
                        if (GyUtils.isNull(rdyxqq) || GyUtils.isNull(rdyxqz)
                                || rdyxqq.compareTo(GyUtils.cast2Date(skssqq)) > 0 || rdyxqz.compareTo(GyUtils.cast2Date(skssqz)) < 0) {
                            continue;
                        }

                        yhsYxSrzrdxxList.add(sfzrd);
                    }
                }

                //有税费种认定信息才进行初始化
                if (!GyUtils.isNull(yhsYxSrzrdxxList) && yhsYxSrzrdxxList.size() > 0) {
                    //如果表头信息不存在，则构建表头
                    final String zbuuid = IdUtil.fastSimpleUUID();
                    final ZnsbNssbCxsbtxxDO newBt = new ZnsbNssbCxsbtxxDO();
                    newBt.setUuid(zbuuid);
                    newBt.setDjxh(new BigDecimal(djxh));
                    newBt.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
                    newBt.setSbsxDm1("11");
                    newBt.setSkssqq(GyUtils.cast2Date(skssqq));
                    newBt.setSkssqz(GyUtils.cast2Date(skssqz));

                    newBt.setZgswskfjDm(jbxxmxsj.getZgswskfjDm());
                    newBt.setZfbz1("N");
                    newBt.setYsbbz("N");
                    newBt.setHyDm(jbxxmxsj.getHyDm());
                    newBt.setXzqhszDm(jbxxmxsj.getZcdzxzqhszDm());
                    newBt.setJdxzDm(jbxxmxsj.getJdxzDm());
                    newBt.setBczt(BcztConstants.DTJ);

                    //录入固定字段
                    newBt.setLrrq(new Date());
                    newBt.setXgrq(new Date());
                    newBt.setLrrsfid("ZNSB.TZZX");
                    newBt.setYwqdDm("TZ_SYS");
                    newBt.setSjcsdq("00000000000");
                    newBt.setSjgsdq("00000000000");
                    newBt.setSjtbSj(new Date());

                    log.info(logTitle + "零申报初始化预定执行初始化操作，登记序号:" + djxh + ",税款所属期" + skssqq + "-" + skssqz + ",表头uuid:" + zbuuid + ",税费种认定数:" + sfzrdmxxx.size());
                    //写入表头
                    cxsbtxxMapper.insert(newBt);

                    //表头首次写入进行零申报初始化
                    this.yhstzLsbInit(yhsYxSrzrdxxList, zbuuid, skssqq, skssqz);
                }
            }
        }

        log.info(logTitle + "零申报初始化结束");
    }

    /**
     * @name 中文名称
     * @description 相关说明
     * @time 创建时间:2024年12月07日下午02:16:24
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public void lsbJyss(){
        log.info(logTitle + "零申报交易算税开始");
        //获取数据
        final List<ZnsbNssbYhscjDO> jyssData = yhscjMapper.getLsbJyssData();
        log.info(logTitle + "零申报交易算税数据数目：" + jyssData.size());

        //以zbuuid分类
        Function<ZnsbNssbYhscjDO, List<Object>> compositeKeyJyss = mxbDO ->
                Arrays.asList(mxbDO.getZbuuid());
        Map<List<Object>, List<ZnsbNssbYhscjDO>> groupingMapJyss =
                jyssData.stream().collect(Collectors.groupingBy(compositeKeyJyss, Collectors.toList()));

        groupingMapJyss.forEach((keys, yhscjLsbList) -> {
            final String zbuuid = (String) keys.get(0);

            //获取表头信息
            final ZnsbNssbCxsbtxxDO btxx = cxsbtxxMapper.selectById(zbuuid);

            //2024.12.18:零申报投递更改为同时投递+1-1两条，不然算税不接收
            //进行零申报正投递
            final List<ZnsbNssbYhscjDO> gxJyssbzList = new ArrayList<>();
            final List<HttzJyssDTO> blnr = new ArrayList<>();
            for (ZnsbNssbYhscjDO yhscjUnit:yhscjLsbList){
                final HttzJyssDTO blnrUnit = new HttzJyssDTO();
                blnrUnit.setYwfsrq(DateUtils.dateToString(yhscjUnit.getSkssqq(), 3));//2024.10.17:业务发生时间改为取合同签订日期DateUtils.getSystemCurrentTime(3)
                blnrUnit.setNsqx(yhscjUnit.getNsqxDm());
                blnrUnit.setYspzlxDm(YspzlxEnum.getYspzlxByZspm(yhscjUnit.getZspmDm()));
                blnrUnit.setYspzbh("");
                blnrUnit.setSspzhm(yhscjUnit.getUuid());
                blnrUnit.setZspmDm(yhscjUnit.getZspmDm());
                blnrUnit.setZszmDm(yhscjUnit.getZszmDm());
                blnrUnit.setYspzmc(yhscjUnit.getYspzmc());
                blnrUnit.setYspzsl("1");
                blnrUnit.setJmse(BigDecimal.ZERO);
                blnrUnit.setYwfl("HT001");//零申报必然为新增
                blnrUnit.setJe(BigDecimal.ONE);
                blnrUnit.setSl(yhscjUnit.getSl1());
                blnrUnit.setSe(BigDecimal.ZERO);

                //业务分类
                if ("101110200".equals(yhscjUnit.getZspmDm())) {
                    //产权转移书据
                    blnrUnit.setYwfl("CQ001");//零申报必然为新增
                } else {
                    //其他类型
                    blnrUnit.setYwfl("HT001");//零申报必然为新增
                }

                //加入List
                blnr.add(blnrUnit);

                //进行零申报负投递
                final HttzJyssDTO blnrUnitNegate = new HttzJyssDTO();
                BeanUtils.copyBean(blnrUnit, blnrUnitNegate);
                blnrUnitNegate.setJe(blnrUnit.getJe().negate());
                blnrUnitNegate.setYspzsl("-1");
                blnr.add(blnrUnitNegate);

                //记录交易算税更新
                final ZnsbNssbYhscjDO gxJyssbzUnit = new ZnsbNssbYhscjDO();
                gxJyssbzUnit.setUuid(yhscjUnit.getUuid());
                gxJyssbzUnit.setJyssbz("Y");
                gxJyssbzList.add(gxJyssbzUnit);
            }

            //装填算税接口基础参数
            SbzbRequest sbzbRequest = new SbzbRequest();
            sbzbRequest.setDjxh(btxx.getDjxh().toString());
            sbzbRequest.setSspzhm(btxx.getUuid());
            sbzbRequest.setSsywflDm("0520");//0520 补录-印花税-合同采集
            sbzbRequest.setYwfsrq(DateUtils.getSystemCurrentTime(3));
            sbzbRequest.setSkssqq(DateUtils.dateToString(btxx.getSkssqq(), 3));
            sbzbRequest.setSkssqz(DateUtils.dateToString(btxx.getSkssqz(), 3));
            sbzbRequest.setXwgzlxDm("0");
            sbzbRequest.setZsxmDm("10111");
            sbzbRequest.setBlnr(JsonUtils.toJson(blnr));

            //调用交易算税接口
            log.info(logTitle + "零申报调用交易算税接口请求：" + JsonUtils.toJson(sbzbRequest));
            final CommonResult<SbzbResponse> result = jyssApi.sbzb(sbzbRequest);
            log.info(logTitle + "零申报调用交易算税接口返回：" + JsonUtils.toJson(result));

            Integer code = result.getCode();
            if (!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code)){
                throw new RuntimeException(result.getMsg());
            }

            //更改交易算税标志
            yhscjMapper.updateBatch(gxJyssbzList);
        });

        log.info(logTitle + "零申报交易算税结束");
    }


    /**
     * @param zzuuidList
     * @param mrSkssq 默认税款所属期起止、签订时间
     * @return boolean
     * @name 按次申报投递交易算税
     * @description 相关说明
     * @time 创建时间:2024年11月13日下午04:36:13
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public boolean yhsAcsbJyss(List<String> zzuuidList, String mrSkssq){
        log.info(logTitle + "按次申报交易算税开始");

        boolean successFlag = true;

        for (String zzuuid:zzuuidList) {
            //查询信息
            final ZnsbNssbYhscjDO yhscjRecord = yhscjMapper.selectById(zzuuid);
            final String djxh = yhscjRecord.getDjxh();
            final String zspmDm = yhscjRecord.getZspmDm();
            final String zszmDm = yhscjRecord.getZszmDm();
            final String sbqxlx = yhscjRecord.getSbqxlx();
            final String nsqxDmThis = yhscjRecord.getNsqxDm();
            final String skssq = GyUtils.isNull(yhscjRecord.getSkssqq())?mrSkssq:DateUtils.dateToString(yhscjRecord.getSkssqq(), 3);

            //生成税源时，会将所有uuid传入，将按次以外筛除
            //2024.12.20修改，按期取税费种认定，按次取04//00按期申报、01按次申报
            if (!NsqxEnum.TIME.getDm().equals(nsqxDmThis)) {
                continue;
            }

            //获取用户信息
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
            List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
            final String zgswjDm = jbxxmxsj.getZgswjDm();

            //查询表头条件下是否有相同类型的按次申报
            final List<ZnsbNssbYhscjDO> yhscjRecords = yhscjMapper.getYhscjAcsbRecord(djxh, skssq, zspmDm, zszmDm);
            //有则跳过(同一表头下不允许有相同税源)(2024.12.23:跳过自己)
            if (!GyUtils.isNull(yhscjRecords) && !zzuuid.equals(yhscjRecords.get(0).getUuid())) {
                continue;
            }

            //获取需汇总数据
            final List<ZnsbTzzxHtzzDO> jyssData = htzzMapper.getAcsbJyssData(zzuuid);
            //无数据则跳过
            if (GyUtils.isNull(jyssData)) {
                continue;
            }
            log.info(logTitle + "按次申报需投递数据条目" + jyssData.size() + "，总账uuid：" + zzuuid);

            //按登记序号、所属账期、征收品目、征收子目，进行交易算税投递
            Function<ZnsbTzzxHtzzDO, List<Object>> compositeKeyJyss = mxbDO ->
                    Arrays.asList(mxbDO.getDjxh(), mxbDO.getSszq(), mxbDO.getZspmDm(), mxbDO.getZszmDm());
            Map<List<Object>, List<ZnsbTzzxHtzzDO>> groupingMapJyss =
                    jyssData.stream().collect(Collectors.groupingBy(compositeKeyJyss, Collectors.toList()));

            try{
                groupingMapJyss.forEach((keys, httzDOS) -> {
                    //查询纳税人信息
//                    ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
//                    znsbMhzcQyjbxxmxReqVO.setDjxh(httzDOS.get(0).getDjxh());
//                    CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
//                    List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
//                    final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
//                    final String zgswjDm = jbxxmxsj.getZgswjDm();

                    //获取纳税期限，获取不到则默认按次
                    final String nsqxDm = sfzrdmxxx.stream()
                            .filter(sfzrdmxxxVO -> httzDOS.get(0).getZspmDm().equals(sfzrdmxxxVO.getZspmDm())).map(SfzrdmxxxVO::getNsqxDm).findFirst().orElse("11");

                    //获取税率（2024.10.12：更新税率获取规则，先从税费种认定开始获取，获取不到再从关联表获取）
                    final Map<String, String> skssqqzMap = GjssGyUtils.calYhsSkssq(sfzrdmxxx, Integer.toString(httzDOS.get(0).getSszq()), httzDOS.get(0).getZspmDm(), httzDOS.get(0).getZszmDm(), DateUtils.toDate(skssq, "yyyy-MM-dd"));
                    String slStr = skssqqzMap.get("sl");//先从税费种认定里获取
                    if (GyUtils.isNull(slStr)) {
                        //若税费种认定没有，使用公共方法查询表cs_gy_glb_zspm
                        slStr = this.getYhsSlByRedis("10111", zspmDm, zszmDm, skssq, zgswjDm);
                    }
                    final BigDecimal sl = new BigDecimal(slStr);

                    //更新标志List
                    final List<ZnsbTzzxHtzzDO> gxJyssbzList = new ArrayList<>();

                    //通过调账记录生成blnr
                    final List<HttzJyssDTO> blnr = new ArrayList<>();
                    for (ZnsbTzzxHtzzDO httzDO:httzDOS){
                        final HttzJyssDTO blnrUnit = new HttzJyssDTO();
                        blnrUnit.setYwfsrq(skssq);//2024.10.17:业务发生时间改为取合同签订日期DateUtils.getSystemCurrentTime(3)
                        blnrUnit.setNsqx(nsqxDm);
                        blnrUnit.setYspzlxDm(YspzlxEnum.getYspzlxByZspm(httzDO.getZspmDm()));
                        blnrUnit.setYspzbh(httzDO.getHtbh());
                        blnrUnit.setSspzhm(httzDO.getUuid());
                        blnrUnit.setZspmDm(httzDO.getZspmDm());
                        blnrUnit.setZszmDm(httzDO.getZszmDm());
                        blnrUnit.setYspzmc(httzDO.getHtmc());
                        blnrUnit.setYspzsl("1");
                        blnrUnit.setJmse(BigDecimal.ZERO);

                        //业务分类
                        if ("101110200".equals(httzDO.getZspmDm())) {
                            //产权转移书据
                            if (TzlxDmEnum.WTZ.getTzlxDm().equals(httzDO.getTzlxDm())){
                                blnrUnit.setYwfl("CQ001");
                            } else {
                                blnrUnit.setYwfl("CQ002");
                            }
                        } else {
                            //其他类型
                            if (TzlxDmEnum.WTZ.getTzlxDm().equals(httzDO.getTzlxDm())){
                                blnrUnit.setYwfl("HT001");
                            } else {
                                blnrUnit.setYwfl("HT002");
                            }
                        }

                        //计算金额
                        final BigDecimal je;
                        if (!GyUtils.isNull(httzDO.getBhsje())){
                            je = httzDO.getBhsje();
                        } else {
                            je = httzDO.getHtzjk1();
                        }
                        blnrUnit.setJe(je);

                        blnrUnit.setSl(sl);

                        //计算税额
                        blnrUnit.setSe(je.multiply(sl));

                        //加入List
                        blnr.add(blnrUnit);

                        //记录交易算税更新
                        final ZnsbTzzxHtzzDO gxJyssbzUnit = new ZnsbTzzxHtzzDO();
                        gxJyssbzUnit.setUuid(httzDO.getUuid());
                        gxJyssbzUnit.setJyssbz("Y");
                        gxJyssbzUnit.setSssj1(DateUtils.toDate(skssq, "yyyy-MM-dd"));
                        gxJyssbzList.add(gxJyssbzUnit);
                    }

                    //装填算税接口基础参数
                    SbzbRequest sbzbRequest = new SbzbRequest();
                    sbzbRequest.setDjxh(httzDOS.get(0).getDjxh());
                    sbzbRequest.setSspzhm(httzDOS.get(0).getUuid());
                    sbzbRequest.setSsywflDm("0520");//0520 补录-印花税-合同采集
                    sbzbRequest.setYwfsrq(DateUtils.getSystemCurrentTime(3));
                    sbzbRequest.setSkssqq(skssq);
                    sbzbRequest.setSkssqz(skssq);
                    sbzbRequest.setXwgzlxDm("0");
                    sbzbRequest.setZsxmDm("10111");
                    sbzbRequest.setBlnr(JsonUtils.toJson(blnr));

                    //调用交易算税接口
                    log.info(logTitle + "按次申报调用交易算税接口请求：" + JsonUtils.toJson(sbzbRequest));
                    final CommonResult<SbzbResponse> result = jyssApi.sbzb(sbzbRequest);
                    log.info(logTitle + "按次申报调用交易算税接口返回：" + JsonUtils.toJson(result));

                    Integer code = result.getCode();
                    if (!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code)){
                        throw new RuntimeException(result.getMsg());
                    }

                    //更改交易算税标志
                    htzzService.updateHttzJyssbz(gxJyssbzList);
                });

                //全部投递成功之后查找表头信息
                List<ZnsbNssbCxsbtxxDO> btxx = cxsbtxxMapper.getCxsbtxx(djxh, skssq, skssq);
                final String bczt;
                final String zbuuid;
                if (GyUtils.isNull(btxx)) {
                    //如果表头信息不存在，则构建表头
                    zbuuid = IdUtil.fastSimpleUUID();
                    final ZnsbNssbCxsbtxxDO newBt = new ZnsbNssbCxsbtxxDO();
                    newBt.setUuid(zbuuid);
                    newBt.setDjxh(new BigDecimal(djxh));
                    newBt.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
                    newBt.setSbsxDm1("11");
                    newBt.setSkssqq(GyUtils.cast2Date(skssq));
                    newBt.setSkssqz(GyUtils.cast2Date(skssq));

                    newBt.setZgswskfjDm(jbxxmxsj.getZgswskfjDm());
                    newBt.setZfbz1("N");
                    newBt.setYsbbz("N");
                    newBt.setHyDm(jbxxmxsj.getHyDm());
                    newBt.setXzqhszDm(jbxxmxsj.getZcdzxzqhszDm());
                    newBt.setJdxzDm(jbxxmxsj.getJdxzDm());
                    newBt.setBczt(BcztConstants.DTJ);

                    //录入固定字段
                    newBt.setLrrq(new Date());
                    newBt.setXgrq(new Date());
                    newBt.setLrrsfid("ZNSB.TZZX");
                    newBt.setYwqdDm("TZ_SYS");
                    newBt.setSjcsdq("00000000000");
                    newBt.setSjgsdq("00000000000");
                    newBt.setSjtbSj(new Date());

                    //写入表头
                    cxsbtxxMapper.insert(newBt);

                    //保存状态记录为未保存
                    bczt = BcztConstants.DTJ;
                } else {
                    //如果表头存在，获取uuid
                    zbuuid = btxx.get(0).getUuid();
                    bczt = btxx.get(0).getBczt();
                }

                //全部投递之后印花税台账更新属期
                final List<ZnsbNssbYhscjDO> yhscjJgList = new ArrayList();

                //将当前台账挂到表头下，填写属期和领受日期
                yhscjRecord.setZbuuid(zbuuid);
                yhscjRecord.setSkssqq(GyUtils.cast2Date(skssq));
                yhscjRecord.setSkssqz(GyUtils.cast2Date(skssq));
                yhscjRecord.setYnspzsllsrq(DateUtils.strToDate(skssq));

                //是否已采集
                if (!(BcztConstants.DTJ.equals(bczt) || BcztConstants.CLSB.equals(bczt))) {
                    //2024.08.15新增：删除一条税源且还未点击生成税源时，页面显示改条税源，金额均为0，变更状态为已变更，高亮显示
                    //已生成税源时，所有修改都为"已变更"
                    yhscjRecord.setBgzt("Y");

                    //已采集，则将其他条目gzbz置为1
                    List<ZnsbNssbYhscjDO> otheryhscjDO = yhscjMapper.getYhscjByZbuuid(zbuuid);
                    for (ZnsbNssbYhscjDO unit:otheryhscjDO){
                        //已经是新增未处理的或删除的不进行修改
                        if (unit.getGzbz().equals("0") || unit.getGzbz().equals("2")){
                            continue;
                        }

                        unit.setGzbz("1");
//                        unit.setBczt(BcztConstants.DTJ);
                        yhscjJgList.add(unit);
                    }
                }

                //录入固定字段
                yhscjRecord.setXgrq(new Date());
                yhscjRecord.setXgrsfid("ZNSB.TZZX");

                yhscjJgList.add(yhscjRecord);

                //更新yhscj
                yhscjMapper.insertOrUpdateBatch(yhscjJgList);

                //同步修改nssb可变列归属表头和总账
                List<ZnsbNssbYhslfkblDO> allYhslfkbl = nssbYhslfkblMapper.getYhslfkblBySyuuid(zzuuid);
                for (ZnsbNssbYhslfkblDO lfkblUnit:allYhslfkbl){
                    lfkblUnit.setZbuuid(zbuuid);
                }
                if (!GyUtils.isNull(allYhslfkbl)) {
                    nssbYhslfkblMapper.updateBatch(allYhslfkbl);
                }
            } catch (Exception e) {
                successFlag = false;
                log.error(logTitle + "按次申报交易算税投递接口调用失败",e);
                throw new RuntimeException(e.getMessage());
            }
        }

        log.info(logTitle + "按次申报交易算税结束");

        return successFlag;
    }

    /**
     * @param zzuuidList
     * @param skssq
     * @param bcbz 补偿标志，Y/N
     * @return boolean
     * @name 按次申报逾期申报红冲交易算税投递
     * @description 相关说明
     * @time 创建时间:2024年11月26日下午05:04:56
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public boolean yhsAcsbJyssHc(List<String> zzuuidList, String skssq, String bcbz){
        log.info(logTitle + "按次申报逾期申报红冲和交易算税开始");

        boolean successFlag = true;

        for (String zzuuidOld:zzuuidList) {
            //如果非补偿，重新构建表头和印花税总账
            String zzuuid = "";
            String zbuuid = "";
            if (!"Y".equals(bcbz)) {
                //如果冲账时间与原时间相同，跳过
                ZnsbNssbYhscjDO yhscjOld = yhscjMapper.selectById(zzuuidOld);
                if (GyUtils.cast2Date(skssq).equals(yhscjOld.getSkssqz())) {
                    continue;
                }

                //查询旧表头
                ZnsbNssbCxsbtxxDO btxxOld = cxsbtxxMapper.selectById(yhscjOld.getZbuuid());

                final String djxh = yhscjOld.getDjxh();
                final String zspmDm = yhscjOld.getZspmDm();
                final String zszmDm = yhscjOld.getZszmDm();

                //查询表头信息
                List<ZnsbNssbCxsbtxxDO> btxx = cxsbtxxMapper.getCxsbtxx(djxh, skssq, skssq);
                final String bczt;
                if (GyUtils.isNull(btxx)) {
                    //如果表头信息不存在，则构建表头
                    zbuuid = IdUtil.fastSimpleUUID();
                    final ZnsbNssbCxsbtxxDO newBt = new ZnsbNssbCxsbtxxDO();
                    newBt.setUuid(zbuuid);
                    newBt.setDjxh(new BigDecimal(djxh));
                    newBt.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
                    newBt.setSbsxDm1("11");
                    newBt.setSkssqq(GyUtils.cast2Date(skssq));
                    newBt.setSkssqz(GyUtils.cast2Date(skssq));

                    newBt.setZgswskfjDm(btxxOld.getZgswskfjDm());
                    newBt.setZfbz1("N");
                    newBt.setYsbbz("N");
                    newBt.setHyDm(btxxOld.getHyDm());
                    newBt.setXzqhszDm(btxxOld.getXzqhszDm());
                    newBt.setJdxzDm(btxxOld.getJdxzDm());
                    newBt.setBczt(BcztConstants.DTJ);

                    //录入固定字段
                    newBt.setLrrq(new Date());
                    newBt.setXgrq(new Date());
                    newBt.setLrrsfid("ZNSB.TZZX");
                    newBt.setYwqdDm("TZ_SYS");
                    newBt.setSjcsdq("00000000000");
                    newBt.setSjgsdq("00000000000");
                    newBt.setSjtbSj(new Date());

                    //写入表头
                    cxsbtxxMapper.insert(newBt);

                    //保存状态记录为未保存
                    bczt = BcztConstants.DTJ;
                } else {
                    //如果表头存在，获取uuid
                    zbuuid = btxx.get(0).getUuid();
                    bczt = btxx.get(0).getBczt();
                }

                //查询表头条件下是否有相同类型的按次申报
                final List<ZnsbNssbYhscjDO> yhscjRecords = yhscjMapper.getYhscjAcsbRecord(djxh, skssq, zspmDm, zszmDm);
                if (GyUtils.isNull(yhscjRecords)) {
                    final List<ZnsbNssbYhscjDO> yhscjJgList = new ArrayList();

                    //若无相关台账，则新增
                    final ZnsbNssbYhscjDO newYhscj = new ZnsbNssbYhscjDO();
                    BeanUtils.copyBean(yhscjOld, newYhscj);

                    zzuuid = IdUtil.fastSimpleUUID();

                    newYhscj.setUuid(zzuuid);
                    newYhscj.setZbuuid(zbuuid);
                    newYhscj.setSkssqq(GyUtils.cast2Date(skssq));
                    newYhscj.setSkssqz(GyUtils.cast2Date(skssq));
                    newYhscj.setZfbz1("N");
                    newYhscj.setBczt(BcztConstants.DTJ);
                    newYhscj.setHdbl(BigDecimal.ONE);
                    newYhscj.setYsbbz("N");
                    newYhscj.setYnspzsllsrq(DateUtils.strToDate(skssq));

                    //是否已采集
                    if (BcztConstants.DTJ.equals(bczt) || BcztConstants.CLSB.equals(bczt)) {
                        //未采集
                        newYhscj.setGzbz("0");
                    } else {
                        //2024.08.15新增：删除一条税源且还未点击生成税源时，页面显示改条税源，金额均为0，变更状态为已变更，高亮显示
                        //已生成税源时，所有修改都为"已变更"
                        newYhscj.setBgzt("Y");

                        //将gzbz置为0(新增)
                        newYhscj.setGzbz("0");

                        //已采集，则将其他条目gzbz置为1
                        List<ZnsbNssbYhscjDO> otheryhscjDO = yhscjMapper.getYhscjByZbuuid(zbuuid);
                        for (ZnsbNssbYhscjDO unit:otheryhscjDO){
                            //已经是新增未处理的或删除的不进行修改
                            if (unit.getGzbz().equals("0") || unit.getGzbz().equals("2")){
                                continue;
                            }

                            unit.setGzbz("1");
//                            unit.setBczt(BcztConstants.DTJ);
                            yhscjJgList.add(unit);
                        }
                    }

                    //录入固定字段
                    newYhscj.setLrrq(new Date());
                    newYhscj.setXgrq(new Date());
                    newYhscj.setLrrsfid("ZNSB.TZZX");
                    newYhscj.setYwqdDm("TZ_SYS");
                    newYhscj.setSjcsdq("00000000000");
                    newYhscj.setSjgsdq("00000000000");
                    newYhscj.setSjtbSj(new Date());
                    newYhscj.setGzbz("0");//未采集

                    yhscjJgList.add(newYhscj);

                    yhscjMapper.insertOrUpdateBatch(yhscjJgList);

                    //作废原yhscj
//                    yhscjOld.setZfbz1("Y");
//                    yhscjOld.setZfrq1(new Date());
//                    yhscjOld.setZfrDm("ZNSB.TZZX");
//                    yhscjMapper.updateById(yhscjOld);
                } else {
                    final List<ZnsbNssbYhscjDO> yhscjJgList = new ArrayList();

                    //若有相关台账，则更新
                    ZnsbNssbYhscjDO yhscjRecord = yhscjRecords.get(0);
                    zzuuid = yhscjRecord.getUuid();

                    yhscjRecord.setJsjehjs(yhscjRecord.getJsjehjs().add(yhscjOld.getJsjehjs()));
                    yhscjRecord.setYnse(yhscjRecord.getYnse().add(yhscjOld.getYnse()));
                    yhscjRecord.setYbtse(yhscjRecord.getYbtse().add(yhscjOld.getYbtse()));
                    yhscjRecord.setYspzsl(yhscjRecord.getYspzsl() + yhscjOld.getYspzsl());
//                    yhscjRecord.setBczt(BcztConstants.DTJ);
                    yhscjRecord.setXgrq(new Date());
                    yhscjRecord.setXgrsfid("ZNSB.TZZX");

                    //是否已采集
                    if (BcztConstants.DTJ.equals(bczt) || BcztConstants.CLSB.equals(bczt)) {
                        //未采集
                        yhscjRecord.setGzbz("0");
                    } else {
                        //2024.08.15新增：删除一条税源且还未点击生成税源时，页面显示改条税源，金额均为0，变更状态为已变更，高亮显示
                        //已生成税源时，所有修改都为"已变更"
                        yhscjRecord.setBgzt("Y");

                        //正常更正
                        yhscjRecord.setGzbz("1");

                        //如果不为直接删除，则需改变其他条目更正状态
                        if (!"Y".equals(yhscjRecord.getZfbz1())){
                            //已采集，则将其他条目gzbz置为1
                            List<ZnsbNssbYhscjDO> otheryhscjDO = yhscjMapper.getYhscjByZbuuid(zbuuid);
                            for (ZnsbNssbYhscjDO unit:otheryhscjDO){
                                //除去修改中的数据自身
                                if (yhscjRecord.getUuid().equals(unit.getUuid())) {
                                    continue;
                                }

                                //已经是新增未处理的不进行修改
                                if (unit.getGzbz().equals("0")){
                                    continue;
                                }

                                unit.setGzbz("1");
//                                unit.setBczt(BcztConstants.DTJ);
                                yhscjJgList.add(unit);
                            }
                        }
                    }

                    yhscjJgList.add(yhscjRecord);

                    yhscjMapper.insertOrUpdateBatch(yhscjJgList);

                    //作废原yhscj
//                    yhscjOld.setZfbz1("Y");
//                    yhscjOld.setZfrq1(new Date());
//                    yhscjOld.setZfrDm("ZNSB.TZZX");
//                    yhscjMapper.updateById(yhscjOld);
                }

                //修改htzz的zzuuid
                htzzMapper.updateZzuuid(zzuuidOld, zzuuid);

                //同步修改nssb可变列归属表头和总账
                List<ZnsbNssbYhslfkblDO> allYhslfkbl = nssbYhslfkblMapper.getYhslfkblBySyuuid(zzuuidOld);
                if (!GyUtils.isNull(allYhslfkbl)) {
                    for (ZnsbNssbYhslfkblDO lfkblUnit:allYhslfkbl){
                        lfkblUnit.setZbuuid(zbuuid);
                        lfkblUnit.setSyuuid(zzuuid);
                    }
                    nssbYhslfkblMapper.updateBatch(allYhslfkbl);
                }

                //获取需红冲数据
                final List<ZnsbTzzxHtzzDO> hcData = htzzMapper.getAcsbHcData(zzuuid);
                log.info(logTitle + "按次申报逾期申报红冲数据条目" + hcData.size() + "，新总账uuid：" + zzuuid + "，旧总账uuid：" + zzuuidOld);

                //进行冲账操作
                for (ZnsbTzzxHtzzDO hcUnit:hcData) {
                    final List<ZnsbTzzxHtzzDO> hctzList = new ArrayList<>();

                    //更改旧纪录
                    final ZnsbTzzxHtzzDO htzzRecordDO = new ZnsbTzzxHtzzDO();
                    BeanUtils.copyBean(hcUnit, htzzRecordDO);
                    htzzRecordDO.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
                    htzzRecordDO.setXgrq(new Date());
                    htzzRecordDO.setHcbz1("N");//初始化红冲标记，记录N为开始红冲还未结束

                    //生成冲减记录
                    final ZnsbTzzxHtzzDO htzzRecordCjDO = new ZnsbTzzxHtzzDO();
                    BeanUtils.copyBean(hcUnit, htzzRecordCjDO);
                    htzzRecordCjDO.setUuid(IdUtil.fastSimpleUUID());
                    htzzRecordCjDO.setHtzjk1(GyUtils.isNull(htzzRecordDO.getHtzjk1())?null:htzzRecordDO.getHtzjk1().negate());
                    htzzRecordCjDO.setBhsje(htzzRecordDO.getBhsje().negate());
                    htzzRecordCjDO.setTzlxDm(TzlxDmEnum.TZHCJ.getTzlxDm());
                    htzzRecordCjDO.setZzscbz("Y");
                    htzzRecordCjDO.setJyssbz("N");
                    htzzRecordCjDO.setLrrq(new Date());
                    htzzRecordCjDO.setXgrq(new Date());
                    htzzRecordCjDO.setSjtbSj(new Date());
                    htzzRecordCjDO.setHcbz1("N");//初始化红冲标记，记录N为开始红冲还未结束

                    //拼装主表新记录
                    final ZnsbTzzxHtzzDO htzzNewDO = new ZnsbTzzxHtzzDO();
                    BeanUtils.copyBean(hcUnit, htzzNewDO);
                    final String htzzNewDOUuid = IdUtil.fastSimpleUUID();
                    htzzNewDO.setUuid(htzzNewDOUuid);
                    htzzNewDO.setTzlxDm(TzlxDmEnum.XTZ.getTzlxDm());
                    htzzNewDO.setZzscbz("Y");
                    htzzNewDO.setJyssbz("N");
                    htzzNewDO.setSssj1(DateUtils.toDate(skssq, "yyyy-MM-dd"));
                    htzzNewDO.setLrrq(new Date());
                    htzzNewDO.setXgrq(new Date());
                    htzzNewDO.setSjtbSj(new Date());
                    htzzNewDO.setHcbz1("N");//初始化红冲标记，记录N为开始红冲还未结束

                    //写入集合
                    hctzList.add(htzzRecordDO);
                    hctzList.add(htzzRecordCjDO);
                    hctzList.add(htzzNewDO);

                    //进行更新
                    htzzMapper.insertOrUpdateBatch(hctzList);

                    //更新tzzx立法可变列zbuuid
                    List<ZnsbTzzxYhslfkblDO> lfkblList = yhslfkblMapper.getYhslfkblByZbuuid(htzzRecordDO.getUuid());
                    if (!GyUtils.isNull(lfkblList)) {
                        for (ZnsbTzzxYhslfkblDO lfkbl:lfkblList) {
                            lfkbl.setZbuuid(htzzNewDOUuid);
                        }
                        yhslfkblMapper.updateBatch(lfkblList);
                    }
                }
            } else {
                //若本次为补偿操作，zzuuid即为传入的httz记录的zzuuidOld
                zzuuid = zzuuidOld;
            }

            //获取投递数据
            final List<ZnsbTzzxHtzzDO> jyssData = htzzMapper.getAcsbHcJyssData(zzuuid);
            log.info(logTitle + "按次申报逾期申报交易算税需投递数据条目" + jyssData.size() + "，总账uuid：" + zzuuid);

            //按登记序号、所属账期、征收品目、征收子目、算税时间，进行交易算税投递
            Function<ZnsbTzzxHtzzDO, List<Object>> compositeKeyJyss = mxbDO ->
                    Arrays.asList(mxbDO.getDjxh(), mxbDO.getSszq(), mxbDO.getZspmDm(), mxbDO.getZszmDm(), mxbDO.getSssj1());
            Map<List<Object>, List<ZnsbTzzxHtzzDO>> groupingMapJyss =
                    jyssData.stream().collect(Collectors.groupingBy(compositeKeyJyss, Collectors.toList()));

            try{
                groupingMapJyss.forEach((keys, httzDOS) -> {
                    //获取基本信息
                    String sssj = DateUtils.dateToString((Date)keys.get(4), 3);//算税时间

                    //查询纳税人信息
                    ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                    znsbMhzcQyjbxxmxReqVO.setDjxh(httzDOS.get(0).getDjxh());
                    CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
                    List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
                    final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
                    final String zgswjDm = jbxxmxsj.getZgswjDm();

                    //获取纳税期限，获取不到则默认按次
                    final String nsqxDm = sfzrdmxxx.stream()
                            .filter(sfzrdmxxxVO -> httzDOS.get(0).getZspmDm().equals(sfzrdmxxxVO.getZspmDm())).map(SfzrdmxxxVO::getNsqxDm).findFirst().orElse("11");

                    //更新标志List
                    final List<ZnsbTzzxHtzzDO> gxJyssbzList = new ArrayList<>();

                    //通过调账记录生成blnr和红冲blnr
                    final List<HttzJyssDTO> hcBlnr = new ArrayList<>();
                    for (ZnsbTzzxHtzzDO httzDO:httzDOS){
                        //获取税率（2024.10.12：更新税率获取规则，先从税费种认定开始获取，获取不到再从关联表获取）
                        final Map<String, String> skssqqzMap = GjssGyUtils.calYhsSkssq(sfzrdmxxx, Integer.toString(httzDOS.get(0).getSszq()), httzDOS.get(0).getZspmDm(), httzDOS.get(0).getZszmDm(), DateUtils.toDate(sssj, "yyyy-MM-dd"));
                        String slStr = skssqqzMap.get("sl");//先从税费种认定里获取
                        if (GyUtils.isNull(slStr)) {
                            //若税费种认定没有，使用公共方法查询表cs_gy_glb_zspm
                            slStr = this.getYhsSlByRedis("10111", httzDO.getZspmDm(), httzDO.getZszmDm(), sssj, zgswjDm);
                        }
                        final BigDecimal sl = new BigDecimal(slStr);

                        //计算金额税额
                        final BigDecimal je;
                        if (!GyUtils.isNull(httzDO.getBhsje())){
                            je = httzDO.getBhsje();
                        } else {
                            je = httzDO.getHtzjk1();
                        }
                        final BigDecimal se = je.multiply(sl);

                        //构筑红冲数据
                        final HttzJyssDTO hcBlnrUnit = new HttzJyssDTO();
                        hcBlnrUnit.setYwfsrq(GyUtils.isNull(httzDO.getSssj1())?"":DateUtils.dateToString(httzDO.getSssj1()));//2024.10.17:业务发生时间改为取合同签订日期DateUtils.getSystemCurrentTime(3)
                        hcBlnrUnit.setNsqx(nsqxDm);
                        hcBlnrUnit.setYspzlxDm(YspzlxEnum.getYspzlxByZspm(httzDO.getZspmDm()));
                        hcBlnrUnit.setYspzbh(httzDO.getHtbh());
                        hcBlnrUnit.setSspzhm(httzDO.getUuid());
                        hcBlnrUnit.setZspmDm(httzDO.getZspmDm());
                        hcBlnrUnit.setZszmDm(httzDO.getZszmDm());
                        hcBlnrUnit.setYspzmc(httzDO.getHtmc());
                        hcBlnrUnit.setJmse(BigDecimal.ZERO);
                        hcBlnrUnit.setJe(je);
                        hcBlnrUnit.setSl(sl);
                        hcBlnrUnit.setSe(se);
                        if (TzlxDmEnum.TZHCJ.getTzlxDm().equals(httzDO.getTzlxDm())) {
                            hcBlnrUnit.setYspzsl("-1");
                        } else {
                            hcBlnrUnit.setYspzsl("1");
                        }

                        //业务分类
                        if ("101110200".equals(httzDO.getZspmDm())) {
                            //产权转移书据
                            hcBlnrUnit.setYwfl("CQ002");
                        } else {
                            //其他类型
                            hcBlnrUnit.setYwfl("HT002");
                        }

                        //加入红冲List
                        hcBlnr.add(hcBlnrUnit);

                        //记录交易算税更新
                        final ZnsbTzzxHtzzDO gxJyssbzUnit = new ZnsbTzzxHtzzDO();
                        gxJyssbzUnit.setUuid(httzDO.getUuid());
                        gxJyssbzUnit.setJyssbz("Y");
                        gxJyssbzUnit.setXgrq(new Date());
                        gxJyssbzList.add(gxJyssbzUnit);
                    }

                    //装填算税接口基础参数
                    SbzbRequest sbzbRequest = new SbzbRequest();
                    sbzbRequest.setDjxh(httzDOS.get(0).getDjxh());
                    sbzbRequest.setSspzhm(httzDOS.get(0).getUuid());
                    sbzbRequest.setSsywflDm("0520");//0520 补录-印花税-合同采集
                    sbzbRequest.setYwfsrq(DateUtils.getSystemCurrentTime(3));
                    sbzbRequest.setSkssqq(sssj);
                    sbzbRequest.setSkssqz(sssj);
                    sbzbRequest.setXwgzlxDm("0");
                    sbzbRequest.setZsxmDm("10111");
                    sbzbRequest.setBlnr(JsonUtils.toJson(hcBlnr));

                    //调用交易算税接口
                    log.info(logTitle + "按次申报逾期申报调用交易算税接口请求：" + JsonUtils.toJson(sbzbRequest));
                    final CommonResult<SbzbResponse> result = jyssApi.sbzb(sbzbRequest);
                    log.info(logTitle + "按次申报逾期申报调用交易算税接口返回：" + JsonUtils.toJson(result));

                    Integer code = result.getCode();
                    if (!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code)){
                        throw new RuntimeException(result.getMsg());
                    }

                    //更改交易算税标志
                    htzzService.updateHttzJyssbz(gxJyssbzList);
                });

                //全部成功后，将zzuuid下所有红冲标志改为Y，标志本次红冲全部完成
                htzzMapper.updateHcbz(zzuuid);

//                //印花税台账将最后红冲时间更新为属期
//                final ZnsbTzzxHtzzDO acsbHcLastSssj = htzzMapper.getAcsbHcLastSssj(zzuuid);
//                final String sssj = DateUtils.toDateStrByFormatIndex(acsbHcLastSssj.getSssj1(), 3);
//                yhscjMapper.acsbUpdateSkssq(zzuuid, sssj);
            } catch (Exception e) {
//                successFlag = false;
                log.error(logTitle + "按次申报逾期申报交易算税投递接口调用失败",e);
            }
        }

        log.info(logTitle + "按次申报逾期申报红冲和交易算税结束");

        return successFlag;
    }

    /**
     * @name 同步bpm合同信息
     * @description 相关说明
     * @time 创建时间:2024年08月29日下午02:47:42
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void getBPMData(String gsdm) {
        //2025.02.27:未配置bpm的环境，不进行bpm提取，防止报错
        if (GyUtils.isNull(bpmUrl) || "N".equals(bpmUrl)) {
            log.info("BPM接口未配置");
            return;
        }

        //计算合同签订日期限制
        ////只获取上季度及之后的bpm合同
        final String sszq = DateUtils.getSystemCurrentTime(17);
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        final LocalDate thisQuarterDay = LocalDate.parse(sszq + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        final LocalDate lastQuarterDay = thisQuarterDay.plusMonths(-3);
        final String lastQuarterFirstDay = dateTimeFormatter.format(this.getStartOrEndDayOfQuarter(lastQuarterDay,true));

        //获取接口调用参数
        final String systemid = bpmConfig.systemid;
        final String d_password = bpmConfig.password;
        final String pagesize = bpmConfig.pagesize;
        final String timestamp = this.getTimestamp();
        final String md5Source = systemid + d_password + timestamp;
        final String md5OfStr = this.getMD5Str(md5Source).toLowerCase();
        final int pageSize = GyUtils.isNull(pagesize)?2000:Integer.parseInt(pagesize);

        //循环获取BPM全量数据
        final Map<String, MainTable> bpmData = new HashMap<>();
        this.getHtxxBPMData(bpmData, "HTXX", systemid, md5OfStr, timestamp, pageSize, gsdm);
//        this.getHtxxBPMData(bpmData, "XMB", systemid, md5OfStr, timestamp, pageSize);
        this.getHtxxBPMData(bpmData, "FW", systemid, md5OfStr, timestamp, pageSize, gsdm);
        this.getHtxxBPMData(bpmData, "CZZZ", systemid, md5OfStr, timestamp, pageSize, gsdm);
        this.getHtxxBPMData(bpmData, "SPZP", systemid, md5OfStr, timestamp, pageSize, gsdm);

        //获取记录
        final List<ZnsbTzzxHtzzBpmzjbDO> AllBpmRecord = bpmzjbMapper.getAllRecord();
        final Map<String, ZnsbTzzxHtzzBpmzjbDO> bpmRecordMap = new HashMap<>();
        for (ZnsbTzzxHtzzBpmzjbDO recordUnit:AllBpmRecord) {
            bpmRecordMap.put(recordUnit.getUuid(), recordUnit);
        }

        //获取合同类型征收品目对照
        final String jtbmSys = CacheUtils.getXtcs("GY00000001");
        final List<String> tzjgHtlxList = new ArrayList<>();//2024.04.08：增加需台账加工加工的合同类型列表
        final Map<String, String> htlxToZspm = new HashMap<>();
        final Map<String, String> htlxToZszm = new HashMap<>();
        final Map<String, String> htlxXyMap = new HashMap<>();//2024.12.23：增加合同类型选用标志，用以标识该类合同是否参与算税
        final Map<String, String> mcToHtlxDmMap = new HashMap<>();//2025.04.08：印花代码和印花税合同类型两者都用来判断合同种类，需要增加名称转代码map
        final List<Map<String, Object>> htlxDzbList = CacheUtils.getTableData("cs_tzzx_htlxdzb");
        for (Map<String, Object> dzbUnit:htlxDzbList){
            //判断集团编码和适配功能是否正确
            final String jtbm = this.toStr(dzbUnit.get("jtbm"));
            final String gnmc = this.toStr(dzbUnit.get("gnmc"));
            if (!jtbmSys.equals(jtbm)){
                continue;
            }

            //获取对照
            final String htlxmc = this.toStr(dzbUnit.get("htlxmc"));
            final String mc = this.toStr(dzbUnit.get("mc"));
            final String zspmDm = GyUtils.isNull(this.toStr(dzbUnit.get("zspmDm")))?"":this.toStr(dzbUnit.get("zspmDm"));
            final String zszmDm = this.toStr(dzbUnit.get("zszmDm"));
            final String xybz = this.toStr(dzbUnit.get("xybz"));
            htlxToZspm.put(htlxmc, zspmDm);
            htlxToZszm.put(htlxmc, zszmDm);
            htlxXyMap.put(htlxmc, xybz);
            mcToHtlxDmMap.put(mc, htlxmc);

            //录入需加工列表
            if (GyUtils.isNull(gnmc) || (gnmc.indexOf("yhshttz") < 0 && gnmc.indexOf("cqzysj") < 0)) {
                continue;
            }

            tzjgHtlxList.add(htlxmc);
        }

        //循环查看合同信息与记录是否有变化
        final List<ZnsbTzzxHtzzDO> htzzChangeList = new ArrayList<>();
        final List<ZnsbTzzxHtzzBpmzjbDO> recordChangeList = new ArrayList<>();
        final Map<String, MainTable> htzzUpdateMainTable = new HashMap<>();
        final List<String> htzzUpdateList = new ArrayList<>();
        final Map<String, String> gsdmDjxhDzMap = new HashMap<>();
        for (String htxxId:bpmData.keySet()){
            //2025.04.08如印花税合同类型为空且印花代码不为空，进行印花税合同类型代码转换
            final MainTable mainTable = bpmData.get(htxxId);
            if (GyUtils.isNull(mainTable.getYhshtlx()) && !GyUtils.isNull(mainTable.getYhdm())) {
                mainTable.setYhshtlx(mcToHtlxDmMap.get(mainTable.getYhdm()));
            }

            //获取比较信息
            final String htbh = mainTable.getHtbh();
            final String htsm = mainTable.getHtsm();
            final String htxm = mainTable.getHtxm();
            final String htjf = mainTable.getHtjf();
            final String bhsje = mainTable.getBhsje();
            final String htqdrq = mainTable.getHtqdrq();
            final String htksrq = GyUtils.isNull(mainTable.getHtksrq())?"":mainTable.getHtksrq();
            final String htjsrq = GyUtils.isNull(mainTable.getHtjsrq())?"":mainTable.getHtjsrq();
            final String gsdm1 = mainTable.getGsdm1();
            final String yhdm = mainTable.getYhdm();
            final String yhshtlx = mainTable.getYhshtlx();
            final String gzlrzx = mainTable.getGzlrzx();

            //判断是否为合同系统旧数据
            //2024.10.18新增规则合同开始结束时间为空的数据也为正常数据，需要抽取
            //2025.04.08印花代码和印花税合同类型均用于判断合同类型，有一项有值即可
            boolean oldDataFlag = false;
            if ((GyUtils.isNull(yhdm) && GyUtils.isNull(yhshtlx)) || GyUtils.isNull(htqdrq) || GyUtils.isNull(gzlrzx)
                    || GyUtils.isNull(gsdm1) || GyUtils.isNull(bhsje)){
                oldDataFlag = true;
            }

            //2025.04.08如印花代码和印花税合同类型均不在映射范围内，该类数据不加工
            boolean noPzFlag = false;
            if (!oldDataFlag && htlxToZspm.get(yhshtlx) == null) {
                log.info(logTitle + "bpm合同同步，合同类型不在约定范围内：" + JsonUtils.toJson(mainTable));
                noPzFlag = true;
            }

            //2025.04.10只加工两期之内的bpm合同
            boolean beforeLastQusrterFlag = false;
            if (GyUtils.isNull(htqdrq)
                    || DateUtils.parseDate(htqdrq, 3).compareTo(DateUtils.parseDate(lastQuarterFirstDay, 3)) < 0) {
                beforeLastQusrterFlag = true;
                if (GyUtils.isNotNull(htqdrq)) {
                    beforeLastQusrterFlag = true;
                }
            }

            //判断是否抽取
            boolean bcqFlag = false;
            final String zspmDm = htlxToZspm.get(yhshtlx);
            final String xybz = htlxXyMap.get(yhshtlx);//2025.01.20:不参与算税的合同类型也修改为不抽取
            if (!tzjgHtlxList.contains(yhshtlx) || !"Y".equals(xybz)){
                bcqFlag = true;
            }
            final String zszmDm = GyUtils.isNull(htlxToZszm.get(yhshtlx))?"":htlxToZszm.get(yhshtlx);

            //生成Md5
            final String md5Resource = htbh + htsm + htxm + htjf + bhsje + htqdrq + htksrq + htjsrq
                    + gsdm1 + yhdm + yhshtlx + gzlrzx;
            final String md5Code = this.getMD5Str(md5Resource);

            //获取记录md5
            final ZnsbTzzxHtzzBpmzjbDO bpmRecord = bpmRecordMap.get(htxxId);
            String recordMd5Code = null;
            if (!GyUtils.isNull(bpmRecord)) {
                recordMd5Code = bpmRecord.getMd5();
            }

            //进行md5比较，若有记录且无变化，跳过本条合同，
            //如查询到的数据为无法处理的旧数据，也跳过(2025.03.27修订：无法处理的旧数据，还是要带出，但不加工)
            //合同为不抽取类型，也跳过(2025.03.26修订：合同类型为不抽取的，还是要显示)
            //2025.04.08:印花税合同类型代码不在映射范围内的，不加工
            //2025.04.10:合同签订日期在上季度之前的，不加工
            if ((!GyUtils.isNull(recordMd5Code) && md5Code.equals(recordMd5Code)) || oldDataFlag || noPzFlag || beforeLastQusrterFlag) {// || oldDataFlag || bcqFlag
                continue;
            }

            //添加htzz表写入和更改记录
            final String newZbuuid;
            if (GyUtils.isNull(recordMd5Code)){
                //新增
                //获取用户信息，为了减少接口调用次数，记录已查询过的gsdm，同一个gsdm只查询一次
                String djxh = null;
                if (!gsdmDjxhDzMap.containsKey(gsdm1)){
                    //获取用户信息
                    ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                    znsbMhzcQyjbxxmxReqVO.setQydmz(gsdm1);
                    CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByQydmz = nsrxxApi.getNsrxxByQydmz(znsbMhzcQyjbxxmxReqVO);
                    if (GyUtils.isNull(nsrxxByQydmz.getData())){
                        //如果企业不包含在机构信息表内，跳过
                        djxh = null;
                    } else {
                        final List<JbxxmxsjVO> jbxxmxsj = nsrxxByQydmz.getData().getJbxxmxsj();

                        //筛选主户
                        for (JbxxmxsjVO jbxxUnit:jbxxmxsj) {
                            final String kqccsztdjbz = jbxxUnit.getKqccsztdjbz();
                            final String kzztdjlxDm = jbxxUnit.getKzztdjlxDm();
                            final String nsrztDm = jbxxUnit.getNsrztDm();

                            if ("N".equals(kqccsztdjbz)
                                    && ("1110".equals(kzztdjlxDm) || "1120".equals(kzztdjlxDm))
                                    && "03".equals(nsrztDm)) {
                                djxh = jbxxUnit.getDjxh();
                                break;
                            }
                        }
                    }

                    gsdmDjxhDzMap.put(gsdm1, djxh);
                } else {
                    djxh = gsdmDjxhDzMap.get(gsdm1);
                }

                //如果未查询到djxh，跳过
                if (GyUtils.isNull(djxh)){
                    continue;
                }

                //主表生成主表DO
                final ZnsbTzzxHtzzDO htzzDO = new ZnsbTzzxHtzzDO();
                final String sszqStr = GyUtils.isNull(htqdrq)?"":DateUtils.dateToString(DateUtils.toDate(htqdrq, "yyyy-MM-dd"), 17);
                newZbuuid = IdUtil.fastSimpleUUID();
                htzzDO.setUuid(newZbuuid);
                htzzDO.setDjxh(djxh);
                htzzDO.setSszq(GyUtils.isNull(sszqStr)?0:Integer.parseInt(sszqStr));
                htzzDO.setHtbh(htbh);
                htzzDO.setHtmc(htxm);
                htzzDO.setHtydsxrq(GyUtils.isNull(htksrq)?null:DateUtils.toDate(htksrq, "yyyy-MM-dd"));
                htzzDO.setHtydzzrq(GyUtils.isNull(htjsrq)?null:DateUtils.toDate(htjsrq, "yyyy-MM-dd"));
                htzzDO.setYnspzsllsrq(GyUtils.isNull(htqdrq)?null:DateUtils.toDate(htqdrq, "yyyy-MM-dd"));
                htzzDO.setZspmDm(GyUtils.isNull(zspmDm)?"":zspmDm);
                htzzDO.setZszmDm(GyUtils.isNull(zszmDm)?"":zszmDm);
                htzzDO.setBhsje(GyUtils.isNull(bhsje)?null:new BigDecimal(bhsje));
                htzzDO.setFbhtbj("N");
                htzzDO.setKjhtbj("N");
                htzzDO.setCqbz("0");
                htzzDO.setScbz("0");
                htzzDO.setLrzx(gzlrzx);
                htzzDO.setHtbt(htjf);
                htzzDO.setHtsm1(htsm);
                htzzDO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());

                //2024.12.23:根据选用标志标记抽取的合同是否参与交易算税和总账生成
                //2025.01.20:不参与算税的合同类型也修改为不抽取，此处逻辑应当不再生效
//                final String xybz = htlxXyMap.get(yhshtlx);
                //2025.03.27:旧数据和不算税类型都要抽取，以备进行额外展示，记为抽取标志2
                if (oldDataFlag || bcqFlag){//!"Y".equals(xybz)
                    //不参与算税的合同抽取标志标记为2
                    htzzDO.setCqbz("2");
                }

                //录入固定字段
                htzzDO.setZzscbz("N");
                htzzDO.setJyssbz("N");
                htzzDO.setLrrq(new Date());
                htzzDO.setXgrq(new Date());
                htzzDO.setLrrsfid("ZNSB.TZZX");
                htzzDO.setYwqdDm("00");
                htzzDO.setSjcsdq("00000000000");
                htzzDO.setSjgsdq("00000000000");
                htzzDO.setSjtbSj(new Date());

                htzzChangeList.add(htzzDO);

                //新增bpm记录
                final ZnsbTzzxHtzzBpmzjbDO newBpmRecord = new ZnsbTzzxHtzzBpmzjbDO();
                newBpmRecord.setUuid(htxxId);
                newBpmRecord.setMd5(md5Code);
                newBpmRecord.setXgrq(new Date());
                newBpmRecord.setSjtbSj(new Date());
                newBpmRecord.setZbuuid(newZbuuid);

                recordChangeList.add(newBpmRecord);
            } else {
                //修改
                //记录zbuuid，一起进行htzz信息查询，再进行调账处理，减少数据库交互次数
                htzzUpdateList.add(bpmRecord.getZbuuid());

                final MainTable mainTableChangeId = new MainTable();
                BeanUtils.copyBean(mainTable, mainTableChangeId);
                mainTableChangeId.setId(htxxId);
                htzzUpdateMainTable.put(bpmRecord.getZbuuid(), mainTableChangeId);
            }
        }

        //处理更新的htzz
        if (!GyUtils.isNull(htzzUpdateList)){
            //一次性获取所有更新的htzz原纪录
            final List<ZnsbTzzxHtzzDO> updateHtzzList = htzzMapper.getByUuids(htzzUpdateList);

            for (ZnsbTzzxHtzzDO updateUnit:updateHtzzList){
                //获取对应bpm数据和bpm记录
                String uuid = updateUnit.getUuid();
                final MainTable mainTable = htzzUpdateMainTable.get(uuid);
                final ZnsbTzzxHtzzBpmzjbDO bpmRecord = bpmRecordMap.get(mainTable.getId());

                //2025.04.08如印花税合同类型为空且印花代码不为空，进行印花税合同类型代码转换
                if (GyUtils.isNull(mainTable.getYhshtlx()) && !GyUtils.isNull(mainTable.getYhdm())) {
                    mainTable.setYhshtlx(mcToHtlxDmMap.get(mainTable.getYhdm()));
                }

                //获取数据项
                final String htbh = mainTable.getHtbh();
                final String htsm = mainTable.getHtsm();
                final String htxm = mainTable.getHtxm();
                final String htjf = mainTable.getHtjf();
                final String bhsje = mainTable.getBhsje();
                final String htqdrq = mainTable.getHtqdrq();
                final String htksrq = GyUtils.isNull(mainTable.getHtksrq())?"":mainTable.getHtksrq();
                final String htjsrq = GyUtils.isNull(mainTable.getHtjsrq())?"":mainTable.getHtjsrq();
                final String gsdm1 = mainTable.getGsdm1();
                final String yhdm = mainTable.getYhdm();
                final String yhshtlx = mainTable.getYhshtlx();
                final String gzlrzx = mainTable.getGzlrzx();

                final String md5Resource = htbh + htsm + htxm + htjf + bhsje + htqdrq + htksrq + htjsrq
                        + gsdm1 + yhdm + yhshtlx + gzlrzx;
                final String md5Code = this.getMD5Str(md5Resource);

                //更改原记录
                final ZnsbTzzxHtzzDO htzzRecordDO = new ZnsbTzzxHtzzDO();
                BeanUtils.copyBean(updateUnit, htzzRecordDO);
                htzzRecordDO.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
                htzzRecordDO.setXgrq(new Date());

                //生成冲减记录
                final ZnsbTzzxHtzzDO htzzRecordCjDO = new ZnsbTzzxHtzzDO();
                BeanUtils.copyBean(updateUnit, htzzRecordCjDO);
                htzzRecordCjDO.setUuid(IdUtil.fastSimpleUUID());
                htzzRecordCjDO.setZzuuid("");
                htzzRecordCjDO.setHtzjk1(GyUtils.isNull(htzzRecordDO.getHtzjk1())?null:htzzRecordDO.getHtzjk1().negate());
                htzzRecordCjDO.setBhsje(GyUtils.isNull(htzzRecordDO.getBhsje())?null:htzzRecordDO.getBhsje().negate());
                htzzRecordCjDO.setTzlxDm(TzlxDmEnum.TZHCJ.getTzlxDm());
                htzzRecordCjDO.setZzscbz("N");
                htzzRecordCjDO.setJyssbz("N");
                htzzRecordCjDO.setLrrq(new Date());
                htzzRecordCjDO.setSjtbSj(new Date());
                htzzRecordCjDO.setXgrq(new Date());
                htzzRecordCjDO.setLrrsfid("ZNSB.TZZX");
                htzzRecordCjDO.setXgrsfid("ZNSB.TZZX");
                //2024.12.25:不参与算税的bpm合同旧纪录变更时可能存在cqbz被置为0的情况，因此，冲减项也应该强制置为2
                //2025.01.20:不参与算税的合同类型也修改为不抽取，此处逻辑应当不再生效
                final String xybz = htlxXyMap.get(yhshtlx);
                if (!"Y".equals(xybz)){
                    //不参与算税的合同抽取标志标记为2
                    htzzRecordCjDO.setCqbz("2");
                }

                //生成新纪录
                final ZnsbTzzxHtzzDO htzzDO = new ZnsbTzzxHtzzDO();
                final String newZbuuid = IdUtil.fastSimpleUUID();
                htzzDO.setUuid(newZbuuid);
                htzzDO.setZzuuid("");
                htzzDO.setDjxh(updateUnit.getDjxh());
                htzzDO.setSszq(updateUnit.getSszq());
                htzzDO.setHtbh(htbh);
                htzzDO.setHtmc(htxm);
                htzzDO.setHtydsxrq(GyUtils.isNull(htksrq)?null:DateUtils.toDate(htksrq, "yyyy-MM-dd"));
                htzzDO.setHtydzzrq(GyUtils.isNull(htjsrq)?null:DateUtils.toDate(htjsrq, "yyyy-MM-dd"));
                htzzDO.setYnspzsllsrq(DateUtils.toDate(htqdrq, "yyyy-MM-dd"));
                htzzDO.setZspmDm(GyUtils.isNull(htlxToZspm.get(yhshtlx))?"":htlxToZspm.get(yhshtlx));
                htzzDO.setZszmDm(GyUtils.isNull(htlxToZszm.get(yhshtlx))?"":htlxToZszm.get(yhshtlx));
                htzzDO.setBhsje(new BigDecimal(bhsje));
                htzzDO.setFbhtbj("N");
                htzzDO.setKjhtbj("N");
                htzzDO.setCqbz("0");
                htzzDO.setScbz("0");
                htzzDO.setLrzx(gzlrzx);
                htzzDO.setHtbt(htjf);
                htzzDO.setHtsm1(htsm);
                htzzDO.setTzlxDm(TzlxDmEnum.XTZ.getTzlxDm());

                //2024.12.23:根据选用标志标记抽取的合同是否参与交易算税和总账生成
                //2025.01.20:不参与算税的合同类型也修改为不抽取，此处逻辑应当不再生效
//                final String xybz = htlxXyMap.get(yhshtlx);
                if (!"Y".equals(xybz)){
                    //不参与算税的合同抽取标志标记为2
                    htzzDO.setCqbz("2");
                }

                //录入固定字段
                htzzDO.setZzscbz("N");
                htzzDO.setJyssbz("N");
                htzzDO.setLrrq(new Date());
                htzzDO.setXgrq(new Date());
                htzzDO.setLrrsfid("ZNSB.TZZX");
                htzzDO.setYwqdDm("00");
                htzzDO.setSjcsdq("00000000000");
                htzzDO.setSjgsdq("00000000000");
                htzzDO.setSjtbSj(new Date());

                //写入htzz记录
                htzzChangeList.add(htzzRecordDO);
                htzzChangeList.add(htzzRecordCjDO);
                htzzChangeList.add(htzzDO);

                //修改bpm记录
                final ZnsbTzzxHtzzBpmzjbDO newBpmRecord = new ZnsbTzzxHtzzBpmzjbDO();
                newBpmRecord.setUuid(mainTable.getId());
                newBpmRecord.setMd5(md5Code);
                newBpmRecord.setXgrq(new Date());
                newBpmRecord.setZbuuid(newZbuuid);

                recordChangeList.add(newBpmRecord);
            }
        }

        //更新htzz
        htzzMapper.insertOrUpdateBatch(htzzChangeList);

        //更新bpm记录
        bpmzjbMapper.insertOrUpdateBatch(recordChangeList);
    }


    /**
     * @param bpmData
     * @param type
     * @param systemid
     * @param md5OfStr
     * @param timestamp
     * @param pageSize
     * @param gsdm
     * @name 接口获取bpm合同信息
     * @description 相关说明
     * @time 创建时间:2024年08月29日下午02:46:18
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void getHtxxBPMData(Map<String, MainTable> bpmData, String type, String systemid, String md5OfStr, String timestamp, int pageSize, String gsdm){
        //类型不正确直接返回，另SPZP为房产税用，印花税不应该调用
        //2025.07.18：SPZP合同现在按SC006租赁合同计算印花税
        if (!"HTXX".equals(type) && !"XMB".equals(type)
                && !"FW".equals(type) && !"CZZZ".equals(type) && !"SPZP".equals(type)){
            return;
        }

        int pageNo = 1;
        boolean endFlag = false;
        while (!endFlag){
            //拼装请求参数
            final GetHtxxRequest req = new GetHtxxRequest();

            final DataJson dataJson = new DataJson();

            dataJson.setHeader(new Header());
            dataJson.setMainTable(new MainTable());
            dataJson.setOperationinfo(new Operationinfo());
            dataJson.setPageInfo(new PageInfo());

            dataJson.getHeader().setSystemid(systemid);
            dataJson.getHeader().setMd5(md5OfStr);
            dataJson.getHeader().setCurrentDateTime(timestamp);

            dataJson.getPageInfo().setPageNo(pageNo);
            dataJson.getPageInfo().setPageSize(pageSize);

            dataJson.getOperationinfo().setOperator("1");

//            req.setDatajson(JSONUtil.toJsonStr(dataJson));

            //根据获取类型不通，调用不同接口获取数据
            final String htxx;
            if ("HTXX".equals(type)){
                dataJson.getMainTable().setGsdm1(GyUtils.isNull(gsdm)?"":gsdm);
                req.setDatajson(JSONUtil.toJsonStr(dataJson));
                htxx = bpmSjcjApi.getHtxx(req);
            } else if ("XMB".equals(type)){
                dataJson.getMainTable().setGsdm(GyUtils.isNull(gsdm)?"":gsdm);
                req.setDatajson(JSONUtil.toJsonStr(dataJson));
                htxx = bpmSjcjApi.xmbhtxx(req);
            } else if ("FW".equals(type)){
                dataJson.getMainTable().setGsdm(GyUtils.isNull(gsdm)?"":gsdm);
                req.setDatajson(JSONUtil.toJsonStr(dataJson));
                htxx = bpmSjcjApi.fwhtxx(req);
            } else if ("CZZZ".equals(type)){
                dataJson.getMainTable().setGsdm(GyUtils.isNull(gsdm)?"":gsdm);
                req.setDatajson(JSONUtil.toJsonStr(dataJson));
                htxx = bpmSjcjApi.czzzhtxx(req);
            } else if ("SPZP".equals(type)){
                dataJson.getMainTable().setGsdm(GyUtils.isNull(gsdm)?"":gsdm);
                req.setDatajson(JSONUtil.toJsonStr(dataJson));
                htxx = bpmSjcjApi.spzphtxx(req);
            } else {
                htxx = "";
            }

            Map<String, Object> stringObjectMap = JsonUtils.toMap(htxx);

            List<Map> maps = JsonUtils.toList(stringObjectMap.get("result").toString(), Map.class);

            //如果获取不到数据，则返回
            if (!GyUtils.isNull(maps)) {
                for (Map<String, Object> unit:maps) {
                    final String mainTableStr = JsonUtils.toJson(unit.get("mainTable"));

                    //不同的接口返回值统一格式
                    final MainTable mainTable;
                    if ("HTXX".equals(type)){
                        mainTable = JsonUtils.toBean(mainTableStr, MainTable.class);
                    } else if ("XMB".equals(type)){
                        final MainTableXmb mainTableXmb = JsonUtils.toBean(mainTableStr, MainTableXmb.class);
                        mainTable = JsonUtils.toBean(mainTableStr, MainTable.class);
                        mainTable.setHtbh(mainTableXmb.getLcbh());
                        mainTable.setHtxm(mainTableXmb.getHtmc());
                        mainTable.setHtsm(mainTableXmb.getSqsy());
                        mainTable.setHtjf(mainTableXmb.getGsmc());
                        mainTable.setHtksrq(mainTableXmb.getHtyjksrq());
                        mainTable.setHtjsrq(mainTableXmb.getHtyjjsrq());
                        mainTable.setGzlrzx(mainTableXmb.getLrzx());
                        mainTable.setGsdm1(mainTableXmb.getGsdm());
                    } else if ("FW".equals(type)){
                        final MainTableFw mainTableFw = JsonUtils.toBean(mainTableStr, MainTableFw.class);
                        mainTable = JsonUtils.toBean(mainTableStr, MainTable.class);
                        mainTable.setHtxm(mainTableFw.getHtmc());
                        mainTable.setHtjf(mainTableFw.getHtzt());
                        mainTable.setGzlrzx(mainTableFw.getSybbh());
                        mainTable.setGsdm1(mainTableFw.getGsdm());
                    } else if ("CZZZ".equals(type)){
                        final MainTableCzzz mainTableCzzz = JsonUtils.toBean(mainTableStr, MainTableCzzz.class);
                        mainTable = JsonUtils.toBean(mainTableStr, MainTable.class);
                        mainTable.setHtxm(mainTableCzzz.getHtmc1());
                        mainTable.setHtjf(mainTableCzzz.getGsmc());
                        mainTable.setHtksrq(mainTableCzzz.getZlqsr1().substring(0,10));
                        mainTable.setHtjsrq(mainTableCzzz.getZljsr1().substring(0,10));
                        mainTable.setGzlrzx(mainTableCzzz.getLrzx1());
                        mainTable.setGsdm1(mainTableCzzz.getGsdm());
                    } else if ("SPZP".equals(type)){
                        final MainTableSpzp mainTableSpzp = JsonUtils.toBean(mainTableStr, MainTableSpzp.class);
                        mainTable = JsonUtils.toBean(mainTableStr, MainTable.class);
                        mainTable.setHtxm(mainTableSpzp.getHtmc());
                        mainTable.setHtjf(mainTableSpzp.getHtzt());
                        mainTable.setGzlrzx(mainTableSpzp.getSybbh());
                        mainTable.setGsdm1(mainTableSpzp.getGsdm());
                        mainTable.setYhshtlx("SC006");
                    } else {
                        mainTable = new MainTable();
                    }

                    if (!bpmData.containsKey(mainTable.getId())) {
                        bpmData.put(type + "-" + mainTable.getId(), mainTable);
                    }
                }

                pageNo++;
            } else {
                endFlag = true;
            }
        }
    }

    /**
     * @name 同步商铺租赁BPM合同信息
     * @description 相关说明
     * @time 创建时间:2024年08月29日下午02:50:10
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    @Transactional
    public void getSpzpBPMData(String gsdm) {
        log.info(logTitle + "同步商铺租赁BPM合同信息开始，gsdm" + gsdm);

        //2025.02.27:未配置bpm的环境，不进行bpm提取，防止报错
        if (GyUtils.isNull(bpmUrl) || "N".equals(bpmUrl)) {
            log.info("BPM接口未配置");
            return;
        }

        //获取接口调用参数
        final String systemid = bpmConfig.systemid;
        final String d_password = bpmConfig.password;
        final String pagesize = bpmConfig.pagesize;
        final String timestamp = this.getTimestamp();
        final String md5Source = systemid + d_password + timestamp;
        final String md5OfStr = this.getMD5Str(md5Source).toLowerCase();
        final int pageSize = GyUtils.isNull(pagesize)?2000:Integer.parseInt(pagesize);

        //查询全量MD5以供比对
        final List<ZnsbTzzxFcshtxxBpmDO> allMd5List = fcshtxxBpmMapper.selectAllMd5();
        final Map<String, String> md5Map = new HashMap<>();
        for (ZnsbTzzxFcshtxxBpmDO unit:allMd5List) {
            md5Map.put(unit.getUuid(), unit.getMd5());
        }

        //循环获取BPM全量数据
        final List<ZnsbTzzxFcshtxxBpmDO> fcshtxxList = new ArrayList<>();//合同信息
        final List<ZnsbTzzxFcshtxxBpmSpzbDO> spxxList = new ArrayList<>();//商铺信息
        final List<ZnsbTzzxFcshtxxBpmZlxxDO> zlxxList = new ArrayList<>();//租赁信息
        final List<String> zbuuidList = new ArrayList<>();//所有更改的房产税合同uuid
        int pageNo = 1;
        boolean endFlag = false;
        while (!endFlag){
            //拼装请求参数
            final GetHtxxRequest req = new GetHtxxRequest();

            final DataJson dataJson = new DataJson();

            dataJson.setHeader(new Header());
            dataJson.setMainTable(new MainTable());
            dataJson.getMainTable().setGsdm(GyUtils.isNull(gsdm)?"":gsdm);
            dataJson.setOperationinfo(new Operationinfo());
            dataJson.setPageInfo(new PageInfo());

            dataJson.getHeader().setSystemid(systemid);
            dataJson.getHeader().setMd5(md5OfStr);
            dataJson.getHeader().setCurrentDateTime(timestamp);

            dataJson.getPageInfo().setPageNo(pageNo);
            dataJson.getPageInfo().setPageSize(pageSize);

            dataJson.getOperationinfo().setOperator("1");

            req.setDatajson(JSONUtil.toJsonStr(dataJson));

            //调用接口获取商铺租赁合同信息
            final String htxx = bpmSjcjApi.spzphtxx(req);
            Map<String, Object> stringObjectMap = JsonUtils.toMap(htxx);
            List<Map> maps = JsonUtils.toList(stringObjectMap.get("result").toString(), Map.class);

            //如果获取不到数据，则返回
            if (!GyUtils.isNull(maps)) {
                for (Map<String, Object> unit:maps) {
                    ////提取主表部分
                    //获取数据
                    final String mainTableStr = JsonUtils.toJson(unit.get("mainTable"));
                    final MainTableSpzp mainTableSpzp = JsonUtils.toBean(mainTableStr, MainTableSpzp.class);

                    //进行MD5比对，已有记录且报文无变化则跳过
                    final String uuid = "SPZP-" + mainTableSpzp.getId();
                    final String oldMd5Str = md5Map.get(uuid);
                    final String newMd5Str = this.getMD5Str(JsonUtils.toJson(unit));
                    if (!GyUtils.isNull(oldMd5Str) && newMd5Str.equals(oldMd5Str)) {
                        continue;
                    }

                    //记录变更条目的id
                    zbuuidList.add(uuid);

                    //名称转换代码
                    final String skfsMc = mainTableSpzp.getSkfs();
                    final String skfs;
                    if ("月付".equals(skfsMc)) {
                        skfs = "0";
                    } else if ("季付".equals(skfsMc)){
                        skfs = "1";
                    } else if ("半年付".equals(skfsMc)){
                        skfs = "2";
                    } else if ("年付".equals(skfsMc)){
                        skfs = "3";
                    } else {
                        skfs = "";
                    }

                    //录入业务字段
                    final ZnsbTzzxFcshtxxBpmDO fcshtxxBpmDO = new ZnsbTzzxFcshtxxBpmDO();
                    fcshtxxBpmDO.setUuid(uuid);
                    fcshtxxBpmDO.setHtbh(mainTableSpzp.getHtbh());
                    fcshtxxBpmDO.setHtmc(mainTableSpzp.getHtmc());
                    fcshtxxBpmDO.setGsmc(mainTableSpzp.getHtzt());
                    fcshtxxBpmDO.setGsh2(mainTableSpzp.getGsdm());
                    fcshtxxBpmDO.setLrzx(mainTableSpzp.getSybbh());
                    fcshtxxBpmDO.setHtqdrq(GyUtils.isNull(mainTableSpzp.getHtqdrq())?null:DateUtils.toDate(mainTableSpzp.getHtqdrq(), "yyyy-MM-dd"));
                    fcshtxxBpmDO.setHtydsxrq(GyUtils.isNull(mainTableSpzp.getHtksrq())?null:DateUtils.toDate(mainTableSpzp.getHtksrq(), "yyyy-MM-dd"));
                    fcshtxxBpmDO.setHtydzzrq(GyUtils.isNull(mainTableSpzp.getHtjsrq())?null:DateUtils.toDate(mainTableSpzp.getHtjsrq(), "yyyy-MM-dd"));
                    fcshtxxBpmDO.setBhsje(GyUtils.isNull(mainTableSpzp.getBhsje())?null:new BigDecimal(mainTableSpzp.getBhsje()));
                    fcshtxxBpmDO.setHtlxmc(mainTableSpzp.getYhshtlx());
                    fcshtxxBpmDO.setSkfs(skfs);
                    fcshtxxBpmDO.setZzrq(GyUtils.isNull(mainTableSpzp.getZzrq())?null:DateUtils.toDate(mainTableSpzp.getZzrq(), "yyyy-MM-dd"));
                    fcshtxxBpmDO.setMd5(newMd5Str);
                    fcshtxxBpmDO.setGzbz("Y");

                    //录入固定字段
                    fcshtxxBpmDO.setLrrq(new Date());
                    fcshtxxBpmDO.setXgrq(new Date());
                    fcshtxxBpmDO.setLrrsfid("ZNSB.TZZX");
                    fcshtxxBpmDO.setYwqdDm("00");
                    fcshtxxBpmDO.setSjcsdq("00000000000");
                    fcshtxxBpmDO.setSjgsdq("00000000000");
                    fcshtxxBpmDO.setSjtbSj(new Date());

                    //写入集合
                    fcshtxxList.add(fcshtxxBpmDO);

                    ////抽取子表1（商铺信息）
                    final String detail1Str = JsonUtils.toJson(unit.get("detail1"));
                    final List<SpxxVO> detail1List = JsonUtils.toList(detail1Str, SpxxVO.class);
                    if (!GyUtils.isNull(detail1List)){
                        for (SpxxVO spxxUnit:detail1List){
                            final ZnsbTzzxFcshtxxBpmSpzbDO spxx = new ZnsbTzzxFcshtxxBpmSpzbDO();
                            spxx.setUuid("SPZP-" + spxxUnit.getId());
                            spxx.setZbuuid(fcshtxxBpmDO.getUuid());
                            spxx.setCzmj(GyUtils.isNull(spxxUnit.getCzmj())?null:new BigDecimal(spxxUnit.getCzmj()));
                            spxx.setSpmc1(spxxUnit.getGlsp());

                            //录入固定字段
                            spxx.setLrrq(new Date());
                            spxx.setXgrq(new Date());
                            spxx.setLrrsfid("ZNSB.TZZX");
                            spxx.setYwqdDm("00");
                            spxx.setSjcsdq("00000000000");
                            spxx.setSjgsdq("00000000000");
                            spxx.setSjtbSj(new Date());

                            //写入集合
                            spxxList.add(spxx);
                        }
                    }

                    ////抽取子表2（租赁信息）
                    final String detail2Str = JsonUtils.toJson(unit.get("detail2"));
                    final List<ZlxxVO> detail2List = JsonUtils.toList(detail2Str, ZlxxVO.class);
                    if (!GyUtils.isNull(detail2List)){
                        for (ZlxxVO zlxxUnit:detail2List){
                            final ZnsbTzzxFcshtxxBpmZlxxDO zlxx = new ZnsbTzzxFcshtxxBpmZlxxDO();
                            zlxx.setUuid("SPZP-" + zlxxUnit.getId());
                            zlxx.setZbuuid(fcshtxxBpmDO.getUuid());
                            zlxx.setZlrqq(GyUtils.isNull(zlxxUnit.getKsrq())?null:DateUtils.toDate(zlxxUnit.getKsrq(), "yyyy-MM-dd"));
                            zlxx.setZlrqz(GyUtils.isNull(zlxxUnit.getJsrq())?null:DateUtils.toDate(zlxxUnit.getJsrq(), "yyyy-MM-dd"));
                            zlxx.setCzzje(GyUtils.isNull(zlxxUnit.getCzzje())?null:new BigDecimal(zlxxUnit.getCzzje()));
                            zlxx.setCzjemy(GyUtils.isNull(zlxxUnit.getCzjemy())?null:new BigDecimal(zlxxUnit.getCzjemy()));
                            zlxx.setTs(GyUtils.isNull(zlxxUnit.getZlts())?null:Integer.parseInt(zlxxUnit.getZlts()));
                            zlxx.setZlys(GyUtils.isNull(zlxxUnit.getZlys())?null:Integer.parseInt(zlxxUnit.getZlys()));
                            zlxx.setSllxmc(zlxxUnit.getSl());
                            zlxx.setDm(zlxxUnit.getSm());

                            //录入固定字段
                            zlxx.setLrrq(new Date());
                            zlxx.setXgrq(new Date());
                            zlxx.setLrrsfid("ZNSB.TZZX");
                            zlxx.setYwqdDm("00");
                            zlxx.setSjcsdq("00000000000");
                            zlxx.setSjgsdq("00000000000");
                            zlxx.setSjtbSj(new Date());

                            //写入集合
                            zlxxList.add(zlxx);
                        }
                    }
                }

                pageNo++;
            } else {
                endFlag = true;
            }
        }

        //预先清理变化数据的子表
        if (!GyUtils.isNull(zbuuidList)) {
            fcsSpBpmMapper.deleteXgSpzpBpm(zbuuidList);
            fcsZlxxBpmMapper.deleteXgSpzpBpm(zbuuidList);
        }

        //全量插入新数据
        if (!GyUtils.isNull(fcshtxxList)){
            fcshtxxBpmMapper.insertOrUpdateBatch(fcshtxxList);
        }
        if (!GyUtils.isNull(spxxList)){
            fcsSpBpmMapper.insertOrUpdateBatch(spxxList);
        }
        if (!GyUtils.isNull(zlxxList)){
            fcsZlxxBpmMapper.insertOrUpdateBatch(zlxxList);
        }

        log.info(logTitle + "同步商铺租赁BPM合同信息结束，清理数据" + zbuuidList.size() + ",更新合同" + fcshtxxList.size() + ",新增子表" + spxxList.size() + "|" + zlxxList.size());
    }




    private String deleteMode = "delete";
    @Override
    @Transactional
    public List<HttzInsertReqVO> htzzInsertByJk(List<HttzInsertReqVO> dataList) {
        //错误数据集合
        final List<HttzInsertReqVO> errorDataList = new ArrayList<>();

        //按uuid分类
        final Map<String, List<HttzInsertReqVO>> dataMap = new HashMap<>();
        for (HttzInsertReqVO dataUnit:dataList) {
            //校验必录项
            if (!"3".equals(dataUnit.getBgbz()) && (GyUtils.isNull(dataUnit.getUuid()) || GyUtils.isNull(dataUnit.getNsrsbh())
                    || GyUtils.isNull(dataUnit.getHtbh()) || GyUtils.isNull(dataUnit.getHtmc())
                    || GyUtils.isNull(dataUnit.getHtqdrq()) || GyUtils.isNull(dataUnit.getZspmDm())
                    || GyUtils.isNull(dataUnit.getBhsje()) || GyUtils.isNull(dataUnit.getKjhtbz())
                    || GyUtils.isNull(dataUnit.getLrrq()) || GyUtils.isNull(dataUnit.getXgrq()))) {
                //2025.07.11：变更标志不为3(删除)时，按全字段校验
                //必录项有空则记录为错误数据
                errorDataList.add(dataUnit);
            } else if ("3".equals(dataUnit.getBgbz()) && (GyUtils.isNull(dataUnit.getUuid())
                    || GyUtils.isNull(dataUnit.getHtbh()) || GyUtils.isNull(dataUnit.getXgrq()))) {
                //2025.07.11：变更标志为3(删除)时，只校验uuid、htbh、xgrq
                //必录项有空则记录为错误数据
                errorDataList.add(dataUnit);
            } else {
                final String uuid = dataUnit.getUuid();

                //录入日期、修改日期转date
                dataUnit.setLrrqDate(DateUtils.parseDate(dataUnit.getLrrq(),0));
                dataUnit.setXgrqDate(DateUtils.parseDate(dataUnit.getXgrq(),0));

                //计入数据
                if (dataMap.containsKey(uuid)) {
                    dataMap.get(uuid).add(dataUnit);
                } else {
                    final List<HttzInsertReqVO> unitList = new ArrayList<>();
                    unitList.add(dataUnit);
                    dataMap.put(uuid, unitList);
                }
            }
        }

        //没有有效内容直接返回
        if (!GyUtils.isNull(dataMap)) {
            //获取记录
            final List<ZnsbTzzxHtzzBpmzjbDO> AllBpmRecord = bpmzjbMapper.getRecordsByUuidList(new ArrayList<>(dataMap.keySet()));
            final Map<String, ZnsbTzzxHtzzBpmzjbDO> bpmRecordMap = new HashMap<>();
            for (ZnsbTzzxHtzzBpmzjbDO recordUnit:AllBpmRecord) {
                bpmRecordMap.put(recordUnit.getUuid(), recordUnit);
            }

            //循环查看合同信息与记录是否有变化
            final List<ZnsbTzzxHtzzDO> htzzChangeList = new ArrayList<>();
            final List<ZnsbTzzxHtzzBpmzjbDO> recordChangeList = new ArrayList<>();
            final Map<String, HttzInsertReqVO> htzzUpdateMainTable = new HashMap<>();
            final List<String> htzzUpdateList = new ArrayList<>();
            final Map<String, Map<String, String>> gsdmDjxhDzMap = new HashMap<>();
            for (String uuid:dataMap.keySet()){
                //获取该合同最新的修改记录
                final List<HttzInsertReqVO> htxxList = dataMap.get(uuid);
                HttzInsertReqVO lastXg = null;
                for (HttzInsertReqVO htxxUnit:htxxList) {
                    if (GyUtils.isNull(lastXg)) {
                        //首次记录
                        lastXg = htxxUnit;
                    } else {
                        //比较修改日期，记录最新的
                        if (lastXg.getXgrq().compareTo(htxxUnit.getXgrq()) < 0) {
                            lastXg = htxxUnit;
                        }
                    }
                }

                //查看该合同历史记录
                final ZnsbTzzxHtzzBpmzjbDO record = bpmRecordMap.get(uuid);
                if (!GyUtils.isNull(record) && lastXg.getXgrqDate().compareTo(record.getXgrq()) <= 0) {
                    //如果最后修改时间与历史记录相同或更靠前，不重新加工
                    continue;
                }

                //获取信息
                final String nsrsbh = lastXg.getNsrsbh();
                final String htqdrq = lastXg.getHtqdrq();
                final String htbh = lastXg.getHtbh();
                final String htsm = lastXg.getHtsm();
                final String htxm = lastXg.getHtmc();
                final String htzjk = lastXg.getHtzje();
                final String bhsje = lastXg.getBhsje();
                final String htksrq = GyUtils.isNull(lastXg.getHtydsxrq())?"":lastXg.getHtydsxrq();
                final String htjsrq = GyUtils.isNull(lastXg.getHtydzzrq())?"":lastXg.getHtydzzrq();
                final String zspmDm = lastXg.getZspmDm();
                final String zszmDm = lastXg.getZszmDm();
                final String gzlrzx = lastXg.getLrzxDm();
                final Date xgrq = lastXg.getXgrqDate();
                final Date lrrq = lastXg.getLrrqDate();

                //2025.07.11：如果新纪录为删除，历史记录也为删除，不进行加工，但需要记录修改时间
                if ("3".equals(lastXg.getBgbz()) && deleteMode.equals(record.getMd5())) {
                    //记录修改时间
                    final ZnsbTzzxHtzzBpmzjbDO newBpmRecord = new ZnsbTzzxHtzzBpmzjbDO();
                    BeanUtils.copyBean(record, newBpmRecord);
                    newBpmRecord.setXgrq(lastXg.getXgrqDate());
                    newBpmRecord.setSjtbSj(new Date());
                    recordChangeList.add(newBpmRecord);
                    continue;
                }

                //添加htzz表写入和更改记录
                final String newZbuuid;
                if (GyUtils.isNull(record) || deleteMode.equals(record.getMd5())){
                    //新增
                    //2025.07.11：增加了删除合同功能，记录表md5为delete时代表之前合同已被删除，也按照新增处理
                    //获取用户信息，为了减少接口调用次数，记录已查询过的gsdm，同一个gsdm只查询一次
                    String djxh = null;
                    String nsrmc = null;
                    if (!gsdmDjxhDzMap.containsKey(nsrsbh)){
                        //获取用户信息
                        ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
                        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
                        if (GyUtils.isNull(nsrxxByNsrsbh.getData())){
                            //如果企业不包含在机构信息表内，跳过
                            djxh = null;
                            nsrmc = null;
                            errorDataList.addAll(htxxList);
                        } else {
                            final List<JbxxmxsjVO> jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj();

                            //筛选主户
                            for (JbxxmxsjVO jbxxUnit:jbxxmxsj) {
                                final String kqccsztdjbz = jbxxUnit.getKqccsztdjbz();
                                final String kzztdjlxDm = jbxxUnit.getKzztdjlxDm();
                                final String nsrztDm = jbxxUnit.getNsrztDm();

                                if ("N".equals(kqccsztdjbz)
                                        && ("1110".equals(kzztdjlxDm) || "1120".equals(kzztdjlxDm))
                                        && "03".equals(nsrztDm)) {
                                    djxh = jbxxUnit.getDjxh();
                                    nsrmc = jbxxUnit.getNsrmc();
                                    break;
                                }
                            }
                        }

                        final Map<String, String> nsrxxMap = new HashMap<>();
                        nsrxxMap.put("djxh", djxh);
                        nsrxxMap.put("nsrmc", nsrmc);
                        gsdmDjxhDzMap.put(nsrsbh, nsrxxMap);
                    } else {
                        djxh = gsdmDjxhDzMap.get(nsrsbh).get("djxh");
                        nsrmc = gsdmDjxhDzMap.get(nsrsbh).get("nsrmc");
                    }

                    //如果未查询到djxh，跳过
                    if (GyUtils.isNull(djxh)){
                        continue;
                    }

                    //主表生成主表DO
                    final ZnsbTzzxHtzzDO htzzDO = new ZnsbTzzxHtzzDO();
                    final String sszqStr = GyUtils.isNull(htqdrq)?"":DateUtils.dateToString(DateUtils.toDate(htqdrq, "yyyy-MM-dd"), 17);
                    newZbuuid = IdUtil.fastSimpleUUID();
                    htzzDO.setUuid(newZbuuid);
                    htzzDO.setDjxh(djxh);
                    htzzDO.setSszq(GyUtils.isNull(sszqStr)?0:Integer.parseInt(sszqStr));
                    htzzDO.setHtbh(htbh);
                    htzzDO.setHtmc(htxm);
                    htzzDO.setHtydsxrq(GyUtils.isNull(htksrq)?null:DateUtils.toDate(htksrq, htksrq.length() <= 10?"yyyy-MM-dd":"yyyy-MM-dd HH:mm:ss"));
                    htzzDO.setHtydzzrq(GyUtils.isNull(htjsrq)?null:DateUtils.toDate(htjsrq, htjsrq.length() <= 10?"yyyy-MM-dd":"yyyy-MM-dd HH:mm:ss"));
                    htzzDO.setYnspzsllsrq(GyUtils.isNull(htqdrq)?null:DateUtils.toDate(htqdrq, htqdrq.length() <= 10?"yyyy-MM-dd":"yyyy-MM-dd HH:mm:ss"));
                    htzzDO.setZspmDm(GyUtils.isNull(zspmDm)?"":zspmDm);
                    htzzDO.setZszmDm(GyUtils.isNull(zszmDm)?"":zszmDm);
                    htzzDO.setHtzjk1(GyUtils.isNull(htzjk)?null:new BigDecimal(htzjk));
                    htzzDO.setBhsje(GyUtils.isNull(bhsje)?null:new BigDecimal(bhsje));
                    htzzDO.setFbhtbj("N");
                    htzzDO.setKjhtbj("N");
                    htzzDO.setCqbz("0");
                    htzzDO.setScbz("0");
                    htzzDO.setLrzx(gzlrzx);
                    htzzDO.setHtbt(nsrmc);
                    htzzDO.setHtsm1(htsm);
                    htzzDO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());

                    //录入固定字段
                    htzzDO.setZzscbz("N");
                    htzzDO.setJyssbz("N");
                    htzzDO.setLrrq(lrrq);
                    htzzDO.setXgrq(xgrq);
                    htzzDO.setLrrsfid("ZNSB.TZZX");
                    htzzDO.setYwqdDm("00");
                    htzzDO.setSjcsdq("00000000000");
                    htzzDO.setSjgsdq("00000000000");
                    htzzDO.setSjtbSj(new Date());

                    htzzChangeList.add(htzzDO);

                    //新增bpm记录
                    final ZnsbTzzxHtzzBpmzjbDO newBpmRecord = new ZnsbTzzxHtzzBpmzjbDO();
                    newBpmRecord.setUuid(uuid);
                    newBpmRecord.setMd5("");
                    newBpmRecord.setXgrq(lastXg.getXgrqDate());
                    newBpmRecord.setSjtbSj(new Date());
                    newBpmRecord.setZbuuid(newZbuuid);

                    recordChangeList.add(newBpmRecord);
                } else {
                    //修改
                    //记录zbuuid，一起进行htzz信息查询，再进行调账处理，减少数据库交互次数
                    htzzUpdateList.add(record.getZbuuid());

                    final HttzInsertReqVO mainTableChangeId = new HttzInsertReqVO();
                    BeanUtils.copyBean(lastXg, mainTableChangeId);
                    htzzUpdateMainTable.put(record.getZbuuid(), mainTableChangeId);
                }
            }

            //处理更新的htzz
            if (!GyUtils.isNull(htzzUpdateList)){
                //一次性获取所有更新的htzz原纪录
                final List<ZnsbTzzxHtzzDO> updateHtzzList = htzzMapper.getByUuids(htzzUpdateList);

                for (ZnsbTzzxHtzzDO updateUnit:updateHtzzList){
                    //获取对应bpm数据和bpm记录
                    String uuid = updateUnit.getUuid();
                    final HttzInsertReqVO lastXg = htzzUpdateMainTable.get(uuid);
                    final ZnsbTzzxHtzzBpmzjbDO record = bpmRecordMap.get(lastXg.getUuid());

                    //2025.07.11：判断是否为删除
                    boolean isDelete = "3".equals(lastXg.getBgbz());

                    //获取数据项
                    final String nsrsbh = lastXg.getNsrsbh();
                    final String htqdrq = lastXg.getHtqdrq();
                    final String htbh = lastXg.getHtbh();
                    final String htsm = lastXg.getHtsm();
                    final String htxm = lastXg.getHtmc();
                    final String htzjk = lastXg.getHtzje();
                    final String bhsje = lastXg.getBhsje();
                    final String htksrq = GyUtils.isNull(lastXg.getHtydsxrq())?"":lastXg.getHtydsxrq();
                    final String htjsrq = GyUtils.isNull(lastXg.getHtydzzrq())?"":lastXg.getHtydzzrq();
                    final String zspmDm = lastXg.getZspmDm();
                    final String zszmDm = lastXg.getZszmDm();
                    final String gzlrzx = lastXg.getLrzxDm();
                    final Date xgrq = lastXg.getXgrqDate();
                    final Date lrrq = lastXg.getLrrqDate();

                    //更改原记录
                    final ZnsbTzzxHtzzDO htzzRecordDO = new ZnsbTzzxHtzzDO();
                    BeanUtils.copyBean(updateUnit, htzzRecordDO);
                    htzzRecordDO.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
                    htzzRecordDO.setXgrq(xgrq);

                    //生成冲减记录
                    final ZnsbTzzxHtzzDO htzzRecordCjDO = new ZnsbTzzxHtzzDO();
                    BeanUtils.copyBean(updateUnit, htzzRecordCjDO);
                    final String deleteNewZbuuid = IdUtil.fastSimpleUUID();
                    htzzRecordCjDO.setUuid(deleteNewZbuuid);
                    htzzRecordCjDO.setZzuuid("");
                    htzzRecordCjDO.setHtzjk1(GyUtils.isNull(htzzRecordDO.getHtzjk1())?null:htzzRecordDO.getHtzjk1().negate());
                    htzzRecordCjDO.setBhsje(GyUtils.isNull(htzzRecordDO.getBhsje())?null:htzzRecordDO.getBhsje().negate());
                    htzzRecordCjDO.setTzlxDm(TzlxDmEnum.TZHCJ.getTzlxDm());
                    htzzRecordCjDO.setZzscbz("N");
                    htzzRecordCjDO.setJyssbz("N");
                    htzzRecordCjDO.setLrrq(htzzRecordDO.getLrrq());
                    htzzRecordCjDO.setSjtbSj(new Date());
                    htzzRecordCjDO.setXgrq(xgrq);
                    htzzRecordCjDO.setLrrsfid("ZNSB.TZZX");
                    htzzRecordCjDO.setXgrsfid("ZNSB.TZZX");

                    //写入htzz记录
                    htzzChangeList.add(htzzRecordDO);
                    htzzChangeList.add(htzzRecordCjDO);

                    //2025.07.11：不删除则生成新记录
                    final String newZbuuid = IdUtil.fastSimpleUUID();
                    if (!isDelete) {
                        //生成新纪录
                        final ZnsbTzzxHtzzDO htzzDO = new ZnsbTzzxHtzzDO();
                        htzzDO.setUuid(newZbuuid);
                        htzzDO.setZzuuid("");
                        htzzDO.setDjxh(updateUnit.getDjxh());
                        htzzDO.setSszq(updateUnit.getSszq());
                        htzzDO.setHtbh(htbh);
                        htzzDO.setHtmc(htxm);
                        htzzDO.setHtydsxrq(GyUtils.isNull(htksrq)?null:DateUtils.toDate(htksrq, "yyyy-MM-dd"));
                        htzzDO.setHtydzzrq(GyUtils.isNull(htjsrq)?null:DateUtils.toDate(htjsrq, "yyyy-MM-dd"));
                        htzzDO.setYnspzsllsrq(DateUtils.toDate(htqdrq, "yyyy-MM-dd"));
                        htzzDO.setZspmDm(GyUtils.isNull(zspmDm)?"":zspmDm);
                        htzzDO.setZszmDm(GyUtils.isNull(zszmDm)?"":zszmDm);
                        htzzDO.setHtzjk1(GyUtils.isNull(htzjk)?null:new BigDecimal(htzjk));
                        htzzDO.setBhsje(GyUtils.isNull(bhsje)?null:new BigDecimal(bhsje));
                        htzzDO.setFbhtbj("N");
                        htzzDO.setKjhtbj("N");
                        htzzDO.setCqbz("0");
                        htzzDO.setScbz("0");
                        htzzDO.setLrzx(gzlrzx);
                        htzzDO.setHtbt(updateUnit.getHtbt());
                        htzzDO.setHtsm1(htsm);
                        htzzDO.setTzlxDm(TzlxDmEnum.XTZ.getTzlxDm());

                        //录入固定字段
                        htzzDO.setZzscbz("N");
                        htzzDO.setJyssbz("N");
                        htzzDO.setLrrq(lrrq);
                        htzzDO.setXgrq(xgrq);
                        htzzDO.setLrrsfid("ZNSB.TZZX");
                        htzzDO.setYwqdDm("00");
                        htzzDO.setSjcsdq("00000000000");
                        htzzDO.setSjgsdq("00000000000");
                        htzzDO.setSjtbSj(new Date());

                        //写入htzz记录
                        htzzChangeList.add(htzzDO);
                    }

                    //修改bpm记录
                    final ZnsbTzzxHtzzBpmzjbDO newBpmRecord = new ZnsbTzzxHtzzBpmzjbDO();
                    newBpmRecord.setUuid(lastXg.getUuid());
                    if (isDelete) {
                        //2025.07.11：删除，bpm记录表md5记录特殊标志，记录最新的3为主表uuid
                        newBpmRecord.setMd5(deleteMode);
                        newBpmRecord.setXgrq(xgrq);
                        newBpmRecord.setZbuuid(deleteNewZbuuid);
                    } else {
                        //正常修改，记录最新生效合同的uuid
                        newBpmRecord.setMd5("");
                        newBpmRecord.setXgrq(xgrq);
                        newBpmRecord.setZbuuid(newZbuuid);
                    }

                    recordChangeList.add(newBpmRecord);
                }
            }

            //更新htzz
            if (!GyUtils.isNull(htzzChangeList)) {
                htzzMapper.insertOrUpdateBatch(htzzChangeList);
            }

            //更新bpm记录
            if (!GyUtils.isNull(recordChangeList)) {
                bpmzjbMapper.insertOrUpdateBatch(recordChangeList);
            }
        }

        return errorDataList;
    }


    @Override
    public PageResult<HttzResVO> queryCqzysj(HttzReqVO httzReqVO) {
        log.info(logTitle + "查询产权转移书据信息开始");
        final List<HttzResVO> resList = new ArrayList<>();

        //获取合同台账主表信息
        final PageResult<ZnsbTzzxHtzzDO> httzPageDate = htzzMapper.getCqzysjPage(httzReqVO);
        final List<ZnsbTzzxHtzzDO> httzList = httzPageDate.getList();

        //循环合同信息获取结算信息和对方树立人信息
        for (ZnsbTzzxHtzzDO httzUnit:httzList){
            final String zbuuid = httzUnit.getUuid();

            //根据主表uuid获取可变列信息
            final List<ZnsbTzzxYhslfkblDO> kblList = yhslfkblMapper.getYhslfkblByZbuuid(zbuuid);

            //区分可变列记录的具体内容
            final List<ZnsbTzzxYhslfkblDO> sjjsList = new ArrayList<>();//实际结算信息
            final List<ZnsbTzzxYhslfkblDO> dfslList = new ArrayList<>();//对方书立信息

            for (ZnsbTzzxYhslfkblDO kblUnit:kblList){
                if (!GyUtils.isNull(kblUnit.getSjjsje())){
                    //结算信息
                    sjjsList.add(kblUnit);
                } else if (!GyUtils.isNull(kblUnit.getDfslrnsrsbh())){
                    //对方书立人信息
                    dfslList.add(kblUnit);
                }
            }

            //拼装此条返回信息
            final HttzResVO resVO = new HttzResVO();
            resVO.setUuid(httzUnit.getUuid());
            resVO.setHtbh(httzUnit.getHtbh());
            resVO.setHtmc(httzUnit.getHtmc());
            resVO.setHtydsxrq(httzUnit.getHtydsxrq());
            resVO.setHtydzzrq(httzUnit.getHtydzzrq());
            resVO.setYnspzsllsrq(httzUnit.getYnspzsllsrq());
            resVO.setZspmDm(httzUnit.getZspmDm());
            resVO.setZszmDm(httzUnit.getZszmDm());
            resVO.setHtzjk1(httzUnit.getHtzjk1());
            resVO.setBhsje(httzUnit.getBhsje());
            resVO.setPzbh(httzUnit.getPzbh());
            resVO.setPch(httzUnit.getPch());
            resVO.setFbhtbj(httzUnit.getFbhtbj());
            resVO.setKjhtbj(httzUnit.getKjhtbj());
            resVO.setYwqdDm(httzUnit.getYwqdDm());
            resVO.setZspmmc(CacheUtils.dm2mc("dm_gy_zspm", httzUnit.getZspmDm()));
            resVO.setZszmmc(CacheUtils.dm2mc("dm_gy_zszm", httzUnit.getZszmDm()));
            resVO.setLrzx(httzUnit.getLrzx());
            resVO.setHtbt(httzUnit.getHtbt());
            resVO.setHtsm1(httzUnit.getHtsm1());

            //写入实际结算金额总值
            final BigDecimal sjjsjeSum = sjjsList.stream()
                    .map(ZnsbTzzxYhslfkblDO::getSjjsje)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            resVO.setSjjsje(sjjsjeSum);

            resList.add(resVO);
        }

        //返回数据
        final PageResult<HttzResVO> resData = new PageResult<>();
        resData.setList(resList);
        resData.setTotal(httzPageDate.getTotal());


        log.info(logTitle + "查询产权转移书据信息结束");
        return resData;
    }

    @Override
    public String checkYsb(List<String> zzuuidList) {
        String ysbFlag = "N";

        //查按次
        final String zzuuid = zzuuidList.get(0);
        final ZnsbNssbYhscjDO yhscj = yhscjMapper.selectById(zzuuid);
        if (!GyUtils.isNull(yhscj)) {
            if ("Y".equals(yhscj.getYsbbz())) {
                ysbFlag = "Y";
            }
        } else {
            //查按季
            final ZnsbNssbCxsbtxxDO btxx = cxsbtxxMapper.selectById(zzuuid);
            if (!GyUtils.isNull(btxx)) {
                if ("Y".equals(btxx.getYsbbz())) {
                    ysbFlag = "Y";
                }
            }
        }

        return ysbFlag;
    }




    private String getMD5Str(String plainText){
        //定义一个字节数组
        byte[] secretBytes = null;
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            //对字符串进行加密
            md.update(plainText.getBytes());
            //获得加密后的数据
            secretBytes = md.digest();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有md5这个算法！");
        }
        //将加密后的数据转换为16进制数字
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        // 不能把变量放到循环条件，值改变之后会导致条件变化。如果生成30位 只能生成31位md5
        int tempIndex = 32 - md5code.length();
        for (int i = 0; i < tempIndex; i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    /**
     * 获取当前日期时间。 YYYY-MM-DD HH:MM:SS
     * @return		当前日期时间
     */
    private static String getCurDateTime() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        return (timestamp.toString()).substring(0, 19);
    }

    /**
     * 获取时间戳   格式如：19990101235959
     * @return
     */
    private static String getTimestamp(){
        return getCurDateTime().replace("-", "").replace(":", "").replace(" ", "");
    }

    private String toStr(Object strObj){
        if (GyUtils.isNull(strObj)){
            return null;
        } else {
            return strObj.toString();
        }
    }

    private static LocalDate getStartOrEndDayOfQuarter(LocalDate today, Boolean isFirst) {
        LocalDate resDate = LocalDate.now();
        if (today == null) {
            today = resDate;
        }
        Month month = today.getMonth();
        Month firstMonthOfQuarter = month.firstMonthOfQuarter();
        Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);
        if (isFirst) {
            resDate = LocalDate.of(today.getYear(), firstMonthOfQuarter, 1);
        } else {
            resDate = LocalDate.of(today.getYear(), endMonthOfQuarter, endMonthOfQuarter.length(today.isLeapYear()));
        }
        return resDate;
    }

    @Override
    public PageResult<HttzResVO> queryHttzFiltered(HttzReqVO httzReqVO) {
        log.info(logTitle + "查询合同台账信息开始");
        final List<HttzResVO> resList = new ArrayList<>();

        //获取合同台账主表信息
        final PageResult<ZnsbTzzxHtzzDO> httzPageDate = htzzMapper.getHttzFilteredPage(httzReqVO);
        final List<ZnsbTzzxHtzzDO> httzList = httzPageDate.getList();

        //循环合同信息获取结算信息和对方树立人信息
        for (ZnsbTzzxHtzzDO httzUnit:httzList){
            final String zbuuid = httzUnit.getUuid();

            //根据主表uuid获取可变列信息
            final List<ZnsbTzzxYhslfkblDO> kblList = yhslfkblMapper.getYhslfkblByZbuuid(zbuuid);

            //区分可变列记录的具体内容
            final List<ZnsbTzzxYhslfkblDO> sjjsList = new ArrayList<>();//实际结算信息
            final List<ZnsbTzzxYhslfkblDO> dfslList = new ArrayList<>();//对方书立信息

            for (ZnsbTzzxYhslfkblDO kblUnit:kblList){
                if (!GyUtils.isNull(kblUnit.getSjjsje())){
                    //结算信息
                    sjjsList.add(kblUnit);
                } else if (!GyUtils.isNull(kblUnit.getDfslrnsrsbh())){
                    //对方书立人信息
                    dfslList.add(kblUnit);
                }
            }

            //拼装此条返回信息
            final HttzResVO resVO = new HttzResVO();
            resVO.setUuid(httzUnit.getUuid());
            resVO.setZzuuid(httzUnit.getZzuuid());
            resVO.setHtbh(httzUnit.getHtbh());
            resVO.setHtmc(httzUnit.getHtmc());
            resVO.setHtydsxrq(httzUnit.getHtydsxrq());
            resVO.setHtydzzrq(httzUnit.getHtydzzrq());
            resVO.setYnspzsllsrq(httzUnit.getYnspzsllsrq());
            resVO.setZspmDm(httzUnit.getZspmDm());
            resVO.setZszmDm(httzUnit.getZszmDm());
            resVO.setHtzjk1(httzUnit.getHtzjk1());
            resVO.setBhsje(httzUnit.getBhsje());
            resVO.setPzbh(httzUnit.getPzbh());
            resVO.setPch(httzUnit.getPch());
            resVO.setFbhtbj(httzUnit.getFbhtbj());
            resVO.setKjhtbj(httzUnit.getKjhtbj());
            resVO.setYwqdDm(httzUnit.getYwqdDm());
            resVO.setZspmmc(CacheUtils.dm2mc("dm_gy_zspm", httzUnit.getZspmDm()));
            resVO.setZszmmc(CacheUtils.dm2mc("dm_gy_zszm", httzUnit.getZszmDm()));
            resVO.setLrzx(httzUnit.getLrzx());
            resVO.setHtbt(httzUnit.getHtbt());
            resVO.setHtsm1(httzUnit.getHtsm1());
            resVO.setSszq(httzUnit.getSszq());
            resVO.setKhbh(httzUnit.getKhbh());
            resVO.setGysbm(httzUnit.getGysbm());

            //写入实际结算金额总值
            final BigDecimal sjjsjeSum = sjjsList.stream()
                    .map(ZnsbTzzxYhslfkblDO::getSjjsje)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            resVO.setSjjsje(sjjsjeSum);

            resList.add(resVO);
        }

        //返回数据
        final PageResult<HttzResVO> resData = new PageResult<>();
        resData.setList(resList);
        resData.setTotal(httzPageDate.getTotal());


        log.info(logTitle + "查询合同台账信息结束");
        return resData;
    }

    @Override
    public HttzResVO queryHtzzFilteredHj(HttzReqVO httzReqVO) {
        log.info(logTitle + "查询合同台账合计开始");

        //获取信息
        final List<ZnsbTzzxHtzzDO> allDate = htzzMapper.getHttzFilteredAll(httzReqVO);

        BigDecimal htzjkHj = BigDecimal.ZERO;
        BigDecimal bhsjeHj = BigDecimal.ZERO;
        for (ZnsbTzzxHtzzDO unit:allDate) {
            if (!GyUtils.isNull(unit.getHtzjk1())) {
                htzjkHj = htzjkHj.add(unit.getHtzjk1());
            }
            if (!GyUtils.isNull(unit.getBhsje())) {
                bhsjeHj = bhsjeHj.add(unit.getBhsje());
            }
        }

        final HttzResVO result = new HttzResVO();
        result.setHtzjk1(htzjkHj);
        result.setBhsje(bhsjeHj);

        log.info(logTitle + "查询合同台账合计结束");
        return result;
    }


    /**
     * @param file
     * @return {@link Map<String, Object> }
     * @throws IOException
     * @name excel导入
     * @description 相关说明
     * @time 创建时间:2024年09月06日下午04:35:13
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public Map<String, Object> uploadExcel(MultipartFile file) throws IOException {
        log.info(logTitle + "方法input开始");
        final Map<String, Object> returnMap = new HashMap<>();

        //校验excel文件
        if(GyUtils.isNull(file)) {
            returnMap.put("code", "01");
            returnMap.put("msg", "请选择Excel文件");
            log.info(logTitle + "方法input结束，导入失败，返回参数：" + JsonUtils.toJson(returnMap));
            return returnMap;
        }

        //校验文件类型
        final String fullName = file.getOriginalFilename();
        final String fillType = fullName.substring(fullName.lastIndexOf(".") + 1);
        if (!"xls".equals(fillType) && !"xlsx".equals(fillType)) {
            returnMap.put("code", "01");
            returnMap.put("msg", "文件类型不正确");
            log.info(logTitle + "方法input结束，导入失败，返回参数：" + JsonUtils.toJson(returnMap));
            return returnMap;
        }

        //将文件转换为list
//        final InputStream inputStream = file.getInputStream();
//        final List<ReadSheet> readSheets = EasyExcel.read(inputStream).build().excelExecutor().sheetList();
//        final ReadSheet sheet = readSheets.get(0);
//        final List<HttzUploadVO> excelList = this.sheetToList(file.getInputStream(), sheet, HttzUploadVO.class);
        HttzUploadListener listener = new HttzUploadListener();
        EasyExcel.read(file.getInputStream(), HttzUploadVO.class, listener)
                .headRowNumber(3) // 表头在第2行（索引从0开始）
                .sheet()
                .doRead();
        final List<HttzUploadVO> excelList = listener.getDataList();

        //excel内容校验
        if (GyUtils.isNull(excelList) || excelList.size() == 0) {
            //校验空表格
            returnMap.put("code", "01");
            returnMap.put("msg", "导入失败：导入文件内容为空");
            log.info(logTitle + "方法input结束，导入失败，返回参数：" + JsonUtils.toJson(returnMap));
            return returnMap;
        }
        // 需求未完善，暂时不做校验处理
//        //应当查询已存入库中的合同信息，为后续覆盖写入做处理
//        final HttzReqVO httzReqVO = new HttzReqVO();
//        //查询记录集合
//        final List<ZnsbTzzxHtzzDO> recordList = htzzMapper.getHttzPage(httzReqVO).getList();
//        final Map<String, ZnsbTzzxHtzzDO> recordMap = new HashMap<>();
//        for (ZnsbTzzxHtzzDO recordUnit:recordList){
//            if (!recordMap.containsKey(recordUnit.getKhbh())){
//                recordMap.put(recordUnit.getDjxh(), recordUnit);
//            }
//        }

        //行内数据校验，生成DOList
        final List<ZnsbTzzxHtzzDO> inportDOList = new ArrayList<>();
        for (HttzUploadVO excelUnit:excelList) {

            //跳过空行
            if (GyUtils.isNull(excelUnit.getXh()) && GyUtils.isNull(excelUnit.getNsrsbh())
                    && GyUtils.isNull(excelUnit.getNsrmc()) && GyUtils.isNull(excelUnit.getSszq()) && GyUtils.isNull(excelUnit.getHtydsxrq())
                    && GyUtils.isNull(excelUnit.getHtydzzrq()) && GyUtils.isNull(excelUnit.getYnspzsllsrq()) && GyUtils.isNull(excelUnit.getZspmDm())
                    && GyUtils.isNull(excelUnit.getBhsje())) {
                continue;
            }

            //获取行数据
            final String xh = excelUnit.getXh();
            final String qysh = excelUnit.getNsrsbh();
            final String qymc = excelUnit.getNsrmc();
            final Integer sszq = excelUnit.getSszq();
            final Date htydsxrq = excelUnit.getHtydsxrq();
            final Date htydzzrq = excelUnit.getHtydzzrq();
            final Date ynspzsllsrq = excelUnit.getYnspzsllsrq();
            final String zspmDm = excelUnit.getZspmDm();
            final String zszmDm = excelUnit.getZszmDm();
            final BigDecimal bhsje = excelUnit.getBhsje();

            //必录数据校验
            if (GyUtils.isNull(xh) || GyUtils.isNull(qysh) || GyUtils.isNull(qymc) || GyUtils.isNull(sszq)
                    || GyUtils.isNull(htydsxrq) || GyUtils.isNull(htydzzrq) || GyUtils.isNull(ynspzsllsrq) || GyUtils.isNull(zspmDm) || GyUtils.isNull(bhsje)) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "）");
                log.info(logTitle + "方法input结束，导入失败，返回参数：" + JsonUtils.toJson(returnMap));
                return returnMap;
            }

            //校验征收子目和征收品目是否匹配
            if (!GyUtils.isNull(zszmDm)) {
                List<Map<String, Object>> dmGyZszm = CacheUtils.getTableData("dm_gy_zszm");
                List<YhsZszmVO> collect = dmGyZszm.stream().filter(stringObjectMap -> zszmDm.equals(stringObjectMap.get("zszmDm")))
                        .filter(stringObjectMap -> zspmDm.equals(stringObjectMap.get("zspmDm")))
                        .map(stringObjectMap -> {
                            YhsZszmVO yhsZszmVO = new YhsZszmVO();
                            yhsZszmVO.setZspmDm((String) stringObjectMap.get("zspmDm"));
                            yhsZszmVO.setZszmDm((String) stringObjectMap.get("zszmDm"));
                            yhsZszmVO.setZszmmc((String) stringObjectMap.get("zszmmc"));
                            return yhsZszmVO;
                        }).collect(Collectors.toList());

                if (GyUtils.isNull(collect)) {
                    returnMap.put("code", "01");
                    returnMap.put("msg", "导入失败：企业税号" + qysh + "，合同子类:" + zszmDm + "|"+CacheUtils.dm2mc("dm_gy_zszm",zszmDm) + "不属于合同类型:" + zspmDm + "|"+CacheUtils.dm2mc("dm_gy_zspm",zspmDm) + "，请确认是否维护正确。");
                    log.info(logTitle + "方法input结束，导入失败，返回参数：" + JsonUtils.toJson(returnMap));
                    return returnMap;
                }
            }

            //获取djxh
            final ZnsbMhzcQyjbxxmxReqVO nsrreqVO = new ZnsbMhzcQyjbxxmxReqVO();
            nsrreqVO.setNsrsbh(qysh);
            final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByNsrsbh(nsrreqVO);
            final String djxh = nsrxxRes.getData().getJbxxmxsj().get(0).getDjxh();

            final ZnsbTzzxHtzzDO newDataUnit = new ZnsbTzzxHtzzDO();
            newDataUnit.setDjxh(djxh);
            newDataUnit.setSszq(sszq);
            newDataUnit.setHtbh(null);
            newDataUnit.setHtmc(null);
            newDataUnit.setHtydsxrq(htydsxrq);
            newDataUnit.setHtydzzrq(htydzzrq);
            newDataUnit.setYnspzsllsrq(ynspzsllsrq);
            newDataUnit.setZspmDm(zspmDm);
            newDataUnit.setZszmDm(zszmDm);
            newDataUnit.setHtzjk1(bhsje);
            newDataUnit.setBhsje(bhsje);
            newDataUnit.setPzbh(null);
            newDataUnit.setPch(null);
            newDataUnit.setFbhtbj("N");
            newDataUnit.setKjhtbj("N");
            newDataUnit.setCqbz("1");
            newDataUnit.setScbz("0");
            newDataUnit.setUuid(IdUtil.fastSimpleUUID());
            newDataUnit.setLrzx(null);
            newDataUnit.setHtbt(null);
            newDataUnit.setHtsm1(null);

            final String sszqStr = DateUtils.dateToString(ynspzsllsrq, 17);
            newDataUnit.setSszq(Integer.parseInt(sszqStr));

            //新增
            newDataUnit.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());

            //录入固定字段
            newDataUnit.setZzscbz("N");
            newDataUnit.setJyssbz("N");
            newDataUnit.setLrrq(new Date());
            newDataUnit.setXgrq(new Date());
            newDataUnit.setLrrsfid("ZNSB.TZZX");
            newDataUnit.setYwqdDm("01");
            newDataUnit.setSjcsdq("00000000000");
            newDataUnit.setSjgsdq("00000000000");
            newDataUnit.setSjtbSj(new Date());

            //记入
            inportDOList.add(newDataUnit);

//            //获取对应记录
//            final ZnsbTzzxHtzzDO record = recordMap.get(djxh);
//            //构造数据
//            if (GyUtils.isNull(record)){
//                //无记录则新增
//                final ZnsbTzzxHtzzDO newDataUnit = new ZnsbTzzxHtzzDO();
//                newDataUnit.setDjxh(djxh);
//                newDataUnit.setSszq(sszq);
//                newDataUnit.setHtbh(null);
//                newDataUnit.setHtmc(null);
//                newDataUnit.setHtydsxrq(htydsxrq);
//                newDataUnit.setHtydzzrq(htydzzrq);
//                newDataUnit.setYnspzsllsrq(ynspzsllsrq);
//                newDataUnit.setZspmDm(zspmDm);
//                newDataUnit.setZszmDm(zszmDm);
//                newDataUnit.setHtzjk1(null);
//                newDataUnit.setBhsje(bhsje);
//                newDataUnit.setPzbh(null);
//                newDataUnit.setPch(null);
//                newDataUnit.setFbhtbj("N");
//                newDataUnit.setKjhtbj("N");
//                newDataUnit.setCqbz("1");
//                newDataUnit.setScbz("0");
//                newDataUnit.setUuid(IdUtil.fastSimpleUUID());
//                newDataUnit.setLrzx(null);
//                newDataUnit.setHtbt(null);
//                newDataUnit.setHtsm1(null);
//
//                final String sszqStr = DateUtils.dateToString(ynspzsllsrq, 17);
//                newDataUnit.setSszq(Integer.parseInt(sszqStr));
//
//                //新增
//                newDataUnit.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
//
//                //录入固定字段
//                newDataUnit.setZzscbz("N");
//                newDataUnit.setJyssbz("N");
//                newDataUnit.setLrrq(new Date());
//                newDataUnit.setXgrq(new Date());
//                newDataUnit.setLrrsfid("ZNSB.TZZX");
//                newDataUnit.setYwqdDm("01");
//                newDataUnit.setSjcsdq("00000000000");
//                newDataUnit.setSjgsdq("00000000000");
//                newDataUnit.setSjtbSj(new Date());
//
//                //记入
//                inportDOList.add(newDataUnit);
//            } else {
//                //有记录则修改
//                final ZnsbTzzxHtzzDO newDataUnit = new ZnsbTzzxHtzzDO();
//                newDataUnit.setUuid(record.getUuid());
//                newDataUnit.setSszq(sszq);
//                newDataUnit.setHtydsxrq(htydsxrq);
//                newDataUnit.setHtydzzrq(htydzzrq);
//                newDataUnit.setYnspzsllsrq(ynspzsllsrq);
//                newDataUnit.setZspmDm(zspmDm);
//                newDataUnit.setZszmDm(zszmDm);
//                newDataUnit.setBhsje(bhsje);
//
//                //修改
//                newDataUnit.setTzlxDm(TzlxDmEnum.XTZ.getTzlxDm());
//
//                //录入固定字段
//                newDataUnit.setXgrq(new Date());
////                newDataUnit.setXgrsfid("SAP");
//
//                //记入
//                inportDOList.add(newDataUnit);
//            }
        }

        //导入
        htzzMapper.insertOrUpdateBatch(inportDOList);

        //写入返回信息
        returnMap.put("code", "00");
        returnMap.put("msg", "本次共导入" + inportDOList.size() + "条记录");

        log.info(logTitle + "方法input结束，返回参数：" + JsonUtils.toJson(returnMap));
        return returnMap;
    }


    //根据class类型处理sheet页数据
    public <T> List<T> sheetToList(InputStream inputStream, ReadSheet sheet, Class<T> clazz) {
        List<T> list = new ArrayList<>();
        EasyExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {

            @Override
            public void invoke(T data, AnalysisContext context) {
                list.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).excelType(ExcelTypeEnum.XLSX).sheet(sheet.getSheetNo()).doRead();
        return list;
    }

    public String getYhsSlByRedis(String zsxmDm, String zspmDm, String zszmDm, String skssq, String zgswjDm) {
        return this.getYhsSlByRedis(zsxmDm, zspmDm, zszmDm, skssq, skssq, zgswjDm);
    }

    public String getYhsSlByRedis(String zsxmDm, String zspmDm, String zszmDm, String skssqq, String skssqz, String zgswjDm) {
        if (GyUtils.isNull(zszmDm)) {
            zszmDm = "";
        }

        String redisKey = formatKey(zsxmDm + "|" + zspmDm + "|" + zszmDm + "|" + zgswjDm.substring(0, 3) + "00000000");
        Boolean hasKey = stringRedisTemplate.hasKey(redisKey);

        String slStr = "";
        if (hasKey) {
            slStr = stringRedisTemplate.opsForValue().get(redisKey);
        } else {
            //若税费种认定没有，使用公共方法查询表cs_gy_glb_zspm
            final GyJsSfzVO gyJsSfzVO = new GyJsSfzVO();
            gyJsSfzVO.setZsxmDm("10111");
            gyJsSfzVO.setZspmDm(zspmDm);
            gyJsSfzVO.setZszmDm(zszmDm);
            gyJsSfzVO.setSkssqq(skssqq);
            gyJsSfzVO.setSkssqz(skssqz);
            gyJsSfzVO.setSjjg(GyUtils.isNull(zgswjDm)?"00000000000":zgswjDm);
            GyJsSfzVO slzslVO = yhssycjhdService.getSlzsl(gyJsSfzVO);

            slStr = slzslVO.getSl();

            stringRedisTemplate.opsForValue().set(redisKey, GyUtils.isNull(slStr)?"null":slStr, 1, TimeUnit.DAYS);
        }

        return slStr;
    }

    private static String formatKey(String key) {
        return String.format("yhsSl:%s", key);
    }

}




