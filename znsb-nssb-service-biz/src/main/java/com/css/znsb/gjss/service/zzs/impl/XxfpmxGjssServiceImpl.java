package com.css.znsb.gjss.service.zzs.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.constants.enums.JtbmEnum;
import com.css.znsb.gjss.feign.jyss.JyssApi;
import com.css.znsb.gjss.mapper.kp.qd.DzfpKpywFpjcxxbMapper;
import com.css.znsb.gjss.mapper.kp.qd.DzfpKpywFpmxxxbMapper;
import com.css.znsb.gjss.mapper.kp.sk.DzdzFpxxDzfpMapper;
import com.css.znsb.gjss.mapper.kp.sk.DzdzFpxxDzzpMapper;
import com.css.znsb.gjss.mapper.kp.sk.DzdzFpxxPtfpMapper;
import com.css.znsb.gjss.mapper.kp.sk.DzdzFpxxZzsfpMapper;
import com.css.znsb.gjss.mapper.kp.sk.DzdzHwxxDzfpMapper;
import com.css.znsb.gjss.mapper.kp.sk.DzdzHwxxDzzpMapper;
import com.css.znsb.gjss.mapper.kp.sk.DzdzHwxxPtfpMapper;
import com.css.znsb.gjss.mapper.kp.sk.DzdzHwxxZzsfpMapper;
import com.css.znsb.gjss.mapper.srzz.jyk.GjssZnsbTzzxSrzzbMapper;
import com.css.znsb.gjss.mapper.srzz.jyk.ZnsbTzzxXxfpzzbKzbMapper;
import com.css.znsb.gjss.mapper.zzs.xxfp.jyk.JykZnsbTzxxXxfpzzbMapper;
import com.css.znsb.gjss.mapper.zzs.xxfp.jyk.JykZnsbTzxxXxfpzzbZjbMapper;
import com.css.znsb.gjss.mapper.zzs.xxfp.jyk.JykZnsbTzzxXxfphwfwmxbMapper;
import com.css.znsb.gjss.mapper.zzs.xxfp.jyk.JykZnsbTzzxXxfpmxbMapper;
import com.css.znsb.gjss.pojo.domain.kp.qd.DzfpKpywFpjcxxbDO;
import com.css.znsb.gjss.pojo.domain.kp.qd.DzfpKpywFpmxxxbDO;
import com.css.znsb.gjss.pojo.domain.kp.sk.DzdzFpxxDzfpDO;
import com.css.znsb.gjss.pojo.domain.kp.sk.DzdzFpxxDzzpDO;
import com.css.znsb.gjss.pojo.domain.kp.sk.DzdzFpxxPtfpDO;
import com.css.znsb.gjss.pojo.domain.kp.sk.DzdzFpxxZzsfpDO;
import com.css.znsb.gjss.pojo.domain.kp.sk.DzdzHwxxDzfpDO;
import com.css.znsb.gjss.pojo.domain.kp.sk.DzdzHwxxDzzpDO;
import com.css.znsb.gjss.pojo.domain.kp.sk.DzdzHwxxPtfpDO;
import com.css.znsb.gjss.pojo.domain.kp.sk.DzdzHwxxZzsfpDO;
import com.css.znsb.gjss.pojo.domain.srzz.ZnsbTzzxSrzzbDO;
import com.css.znsb.gjss.pojo.domain.zzs.xxfp.ZnsbTzxxXxfpzzbDO;
import com.css.znsb.gjss.pojo.domain.zzs.xxfp.ZnsbTzxxXxfpzzbZjbDO;
import com.css.znsb.gjss.pojo.domain.zzs.xxfp.ZnsbTzzxXxfphwfwmxbDO;
import com.css.znsb.gjss.pojo.domain.zzs.xxfp.ZnsbTzzxXxfpmxbDO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.message.BaseInfo;
import com.css.znsb.gjss.pojo.vo.dzfpkp.message.DataMap;
import com.css.znsb.gjss.pojo.vo.dzfpkp.message.Message;
import com.css.znsb.gjss.pojo.vo.dzfpkp.message.Qybq;
import com.css.znsb.gjss.pojo.vo.dzfpkp.qd.DzfpKpywFpjcxxbVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.qd.DzfpKpywFpmxxxbVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.sk.CbFpDzVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.sk.CbFpDzXmVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.sk.CbFpDzzpVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.sk.CbFpDzzpXmVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.sk.CbFpZzsppFyxmVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.sk.CbFpZzsppVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.sk.CbFpZzszpFyxmVO;
import com.css.znsb.gjss.pojo.vo.dzfpkp.sk.CbFpZzszpVO;
import com.css.znsb.gjss.pojo.vo.yp.SbzbResponse;
import com.css.znsb.gjss.service.zzs.XxfpmxGjssService;
import com.css.znsb.gjss.util.GjssGyUtils;
import com.css.znsb.gjss.util.StreamUtils;
import com.css.znsb.gjss.util.ThreadPoolManager;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.NsrzgxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZfjgVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.utils.GYCastUtils;
import com.css.znsb.tzzx.mapper.ZnsbTzzxSrcybdhzbMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzbMapper;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrcybdhzbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxXxfpzzbKzbDO;
import com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb;
import com.css.znsb.tzzx.util.TzzxFzjgUtil;
import com.css.znsb.tzzx.util.TzzxXgmnsrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class XxfpmxGjssServiceImpl implements XxfpmxGjssService {

    @Resource
    private JykZnsbTzzxXxfpmxbMapper xxfpmxbMapper;

    @Resource
    private JykZnsbTzzxXxfphwfwmxbMapper fphwfwmxbMapper;

    @Resource
    private JykZnsbTzxxXxfpzzbZjbMapper zjbMapper;

    @Resource
    private JykZnsbTzxxXxfpzzbMapper xxfpzzbMapper;

    @Resource
    private XxfpmxGjssServiceImpl xxfpmxService;

    @Resource
    private GjssZnsbTzzxSrzzbMapper srzzbMapper;

    @Resource
    private DzfpKpywFpjcxxbMapper fpjcxxbMapper;
    @Resource
    private DzfpKpywFpmxxxbMapper fpmxxxbMapper;

    @Resource
    private ZnsbTzzxSrcybdhzbMapper znsbTzzxSrcybdhzbMapper;

    @Resource
    private ZnsbTzzxSrzzbMapper znsbTzzxSrzzbMapper;

    @Resource
    private JyssApi jyssApi;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private DzdzFpxxZzsfpMapper zzsfpMapper;
    @Resource
    private DzdzFpxxPtfpMapper ptfpMapper;
    @Resource
    private DzdzFpxxDzzpMapper dzzpMapper;
    @Resource
    private DzdzFpxxDzfpMapper dzfpMapper;
    @Resource
    private DzdzHwxxZzsfpMapper hwxxZzsfpMapper;
    @Resource
    private DzdzHwxxPtfpMapper hwxxPtfpMapper;
    @Resource
    private DzdzHwxxDzzpMapper hwxxDzzpMapper;
    @Resource
    private DzdzHwxxDzfpMapper hwxxDzfpMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final double sxydfzjgyzl = 0.03;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private ZnsbTzzxXxfpzzbKzbMapper kzbMapper;

    @Override
    public boolean execute(long limit, Integer startDays) {
        //取最近的两个分区
        LocalDate startTime = LocalDate.now().minusDays(startDays);
        LocalDate endTime = LocalDate.now().plusDays(1);

        List<ZnsbTzzxXxfphwfwmxbDO> datacx = fphwfwmxbMapper.getData(limit, startTime, endTime);

        // 过滤掉zsxmDm等于"06"的元素 06的 就是 sphfwssflhbbm 6开头的
        // sphfwssflhbbm 6开头的，一般都是税率为0且属于不征税发票 故不再算税
        // 山西移动和森马都存在类似发票
        if (GyUtils.isNotNull(datacx)){
            datacx = datacx.stream().filter(Objects::nonNull)  // 过滤掉null对象
                    .filter(item ->!"06".equals(item.getZsxmDm())  // 保留不等于"06"的
                    ).collect(Collectors.toList());
        }
        if (GyUtils.isNull(datacx)){
            return false;
        }
        List<ZnsbTzzxXxfphwfwmxbDO> data =  datacx;
        //森马 将小规模人所属账期 自动归属所在季度的第一个月汇总处理
        final String jtbmSys = CacheUtils.getXtcs("**********");
        if(JtbmEnum.SM.getJtbm().equals(jtbmSys)){
            data.stream()
                    .filter(fpmx -> TzzxXgmnsrUtil.isXgmnsr(new BigDecimal("0.03"), fpmx.getDjxh(), fpmx.getNsrsbh()))
                    .peek(fpmx -> {
                        Integer originalSszq = fpmx.getSszq();
                        fpmx.setSszq(TzzxXgmnsrUtil.getQuarterFirstMonth(originalSszq)); // 动态设置季度首月
                    }).collect(Collectors.toList());
        }

        List<CompletableFuture<Boolean>> runningFuture = new ArrayList<>();
        int shardElementSize = (data.size()+5) / 5;
        Stream.iterate(0, n->n+1)
                .limit(5)
                .map(s -> data.stream()
                        .skip((long) s * shardElementSize)
                        .limit(shardElementSize)
                        .collect(Collectors.toList()))
                .forEach(shardData -> {
                    CompletableFuture<Boolean> result = CompletableFuture.supplyAsync(()-> xxfpmxService.handleMx(shardData)).exceptionally(ex -> {throw new RuntimeException(ex);});
                    runningFuture.add(result);
                });
        try {
            CompletableFuture.allOf(runningFuture.toArray(new CompletableFuture[0])).get();
            //汇总子线程执行结果
            List<ZnsbTzxxXxfpzzbZjbDO> dos = zjbMapper.selectList();
            xxfpmxService.handleZjb(dos);
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    @Override
    public boolean jyssHandle(long limit,Integer startDays) {
        //取最近的两个分区
        LocalDate startTime = LocalDate.now().minusDays(startDays);
        LocalDate endTime = LocalDate.now().plusDays(1);

        List<ZnsbTzzxXxfpmxbDO> data = xxfpmxbMapper.getData(limit, startTime, endTime);
        if (GyUtils.isNull(data)){
            return false;
        }
        final String jtbmSys = CacheUtils.getXtcs("**********");
        if(JtbmEnum.SXYD.getJtbm().equals(jtbmSys)){//山西移动特殊处理 处理月季算税问题
            YearMonth currentYearMonth = YearMonth.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
            YearMonth period = currentYearMonth.minusMonths(1);
            String sszq = period.format(formatter);
            for(int i = data.size()-1; i >= 0; i--) {
                ZnsbTzzxXxfpmxbDO ssvo = data.get(i);
                if(!sszq.equals(GYCastUtils.cast2Str(ssvo.getSszq()))){
                    data.remove(i);
                }
            }
        }
        if (GyUtils.isNull(data)){
            return false;
        }
        log.info("销项发票算税xxfpss开票数据投递条数:{}",data.size());

        data.forEach(odsZnsbTzzxXxfphwfwmxbDO -> ThreadPoolManager.dzfpkp.execute(() -> {
            try {
                sendJyss(odsZnsbTzzxXxfphwfwmxbDO);
            } catch (Exception e) {
                log.error("销项发票算税xxfpsssendJyssError",e);
            }
        }));

        return false;
    }

    //@Transactional
    @Override
    public void srzbHandle(ZnsbTzzxSrzzbDO srzzbDO) {
       /* String djxh = srzzbDO.getDjxh();
        String sszq = srzzbDO.getSszq();
        String jsfsDm1 = srzzbDO.getJsfsDm1();
        String zsxmDm = srzzbDO.getZsxmDm();
        BigDecimal sl1 = srzzbDO.getSl1();

        ZnsbTzxxXxfpzzbDO oldData = xxfpzzbMapper.getData(djxh, Integer.valueOf(sszq), jsfsDm1, zsxmDm, sl1);
        if (!GyUtils.isNull(oldData)){
            ZnsbTzxxXxfpzzbDO tz2 = new ZnsbTzxxXxfpzzbDO();
            BeanUtils.copyBean(oldData,tz2);
            tz2.setTzlxDm("2");
            tz2.setXgrq(new Date());
            xxfpzzbMapper.updateById(tz2);

            ZnsbTzxxXxfpzzbDO tz3 = new ZnsbTzxxXxfpzzbDO();
            BeanUtils.copyBean(oldData,tz3);
            tz3.setUuid(IdUtil.fastSimpleUUID());
            tz3.setJyssbz("N");
            tz3.setLrrq(new Date());
            tz3.setXgrq(new Date());
            tz3.setSjtbSj(new Date());
            tz3.setWkpje(tz3.getWkpje().negate());
            tz3.setWkpse(tz3.getWkpse().negate());
            tz3.setZyfpje(tz3.getZyfpje().negate());
            tz3.setZyfpse(tz3.getZyfpse().negate());
            tz3.setQtfpje(tz3.getQtfpje().negate());
            tz3.setQtfpse(tz3.getQtfpse().negate());
            tz3.setTzlxDm("3");
            xxfpzzbMapper.insert(tz3);

            ZnsbTzxxXxfpzzbDO tz4 = new ZnsbTzxxXxfpzzbDO();
            BeanUtils.copyBean(oldData,tz4);

            tz4.setUuid(IdUtil.fastSimpleUUID());
            tz4.setZyfpje(oldData.getZyfpje());
            tz4.setZyfpse(oldData.getZyfpse());
            tz4.setQtfpje(oldData.getQtfpje());
            tz4.setQtfpse(oldData.getQtfpse());
            BigDecimal wkpje = srzzbDO.getXssr().subtract(oldData.getZyfpje()).subtract(oldData.getQtfpje());
            BigDecimal wkpse = srzzbDO.getXsse().subtract(oldData.getZyfpse()).subtract(oldData.getQtfpse());
            tz4.setWkpje(wkpje);
            tz4.setWkpse(wkpse);
            tz4.setLrrq(new Date());
            tz4.setXgrq(new Date());
            tz4.setLrrsfid("ZNSB.GJSS.JOB");
            tz4.setYwqdDm("SYS");
            tz4.setJyssbz("N");
            tz4.setTzlxDm("4");
            tz4.setSjtbSj(new Date());
            xxfpzzbMapper.insert(tz4);
        }*/
    }

    @Transactional
    public Boolean handleMx(List<ZnsbTzzxXxfphwfwmxbDO> data){
        //根据DJXH,SSZQ,ZSFS_DM_1，ZSXM_DM_1，SL_1进行汇总
        Function<ZnsbTzzxXxfphwfwmxbDO, List<Object>> compositeKey = xxfphwfwmxbDO ->
                Arrays.asList(xxfphwfwmxbDO.getDjxh(),xxfphwfwmxbDO.getSszq(),xxfphwfwmxbDO.getJsfsDm1(),xxfphwfwmxbDO.getZsxmDm(),xxfphwfwmxbDO.getSl1());
        Map<List<Object>, List<ZnsbTzzxXxfphwfwmxbDO>> groupingMap =
                data.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));

        //山西移动分机构处理 必须再中间表处理 再汇总台账处理已经来不及了
        //20250618 根据山西移动需求文档2.7版本 分支机构销项台账税额不用再转换为乘以预征率后的数值 但是代码先保留 防止7月份再变回去
        String sxydbz;//山西移动标志
        final String jtbmSys = CacheUtils.getXtcs("**********");
        if(JtbmEnum.SXYD.getJtbm().equals(jtbmSys)){
            sxydbz = "Y";
        } else {
            sxydbz = "N";
        }

        List<ZnsbTzxxXxfpzzbZjbDO> result = new ArrayList<>();
        groupingMap.forEach((keys, xxfphwfwmxbDOS) -> {
            ZnsbTzzxXxfphwfwmxbDO znsbTzzxXxfphwfwmxbDO = xxfphwfwmxbDOS.get(0);
            ZnsbTzxxXxfpzzbZjbDO xxfpZjb = new ZnsbTzxxXxfpzzbZjbDO();
            BeanUtil.copyProperties(znsbTzzxXxfphwfwmxbDO,xxfpZjb,false);
            xxfpZjb.setUuid(IdUtil.fastSimpleUUID());
            xxfpZjb.setDjxh((String) keys.get(0));
            xxfpZjb.setSszq((Integer) keys.get(1));
            xxfpZjb.setZsfsDm1((String) keys.get(2));
            xxfpZjb.setZsxmDm((String) keys.get(3));
            xxfpZjb.setSl1((BigDecimal) keys.get(4));

            //分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他开票、未开票都按销售收入×预征率计算后带出
            //山西移动特色 预征率 固定为3%  只改税额
            //20250618 根据山西移动需求文档2.7版本 分支机构销项台账税额不用再转换为乘以预征率后的数值 但是代码先保留 防止7月份再变回去
            /*String isSxydfzjgJgbz = getSxydJgbz(znsbTzzxXxfphwfwmxbDO.getDjxh(),znsbTzzxXxfphwfwmxbDO.getNsrsbh(),null);//山西移动分支机构加工标志 Y 加工 N 不加工
            String fpbz = "N";
            if("Y".equals(isSxydfzjgJgbz) && !("02".equals(znsbTzzxXxfphwfwmxbDO.getJsfsDm1()) && znsbTzzxXxfphwfwmxbDO.getSl1().compareTo(new BigDecimal("0.05")) == 0 && Arrays.asList("03","04","05").contains(znsbTzzxXxfphwfwmxbDO.getZsxmDm()))){
                fpbz = "Y";
            }*/
            String fpbz = "N";

            if("N".equals(sxydbz) || "N".equals(fpbz)){//森马和山西没有特殊加工（包括总机构和分支机构除了简易计税5%征收率的服务、不动产和无形资产）的走原来的逻辑
                //专用发票金额
                BigDecimal zyfpje = xxfphwfwmxbDOS.stream()
                        .filter(xxfphwfwmxbDO -> Arrays.asList("01", "03").contains(xxfphwfwmxbDO.getFppzDm()))
                        .map(ZnsbTzzxXxfphwfwmxbDO::getJe)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                xxfpZjb.setZyfpje(zyfpje);

                //专用发票税额
                BigDecimal zyfpse = xxfphwfwmxbDOS.stream()
                        .filter(xxfphwfwmxbDO -> Arrays.asList("01", "03").contains(xxfphwfwmxbDO.getFppzDm()))
                        .map(ZnsbTzzxXxfphwfwmxbDO::getSe)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                xxfpZjb.setZyfpse(zyfpse);


                //其他发票金额
                BigDecimal qtfpje = xxfphwfwmxbDOS.stream()
                        .filter(xxfphwfwmxbDO -> !Arrays.asList("01", "03").contains(xxfphwfwmxbDO.getFppzDm()))
                        .map(ZnsbTzzxXxfphwfwmxbDO::getJe)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                xxfpZjb.setQtfpje(qtfpje);

                //其他发票税额
                BigDecimal qtfpse = xxfphwfwmxbDOS.stream()
                        .filter(xxfphwfwmxbDO -> !Arrays.asList("01", "03").contains(xxfphwfwmxbDO.getFppzDm()))
                        .map(ZnsbTzzxXxfphwfwmxbDO::getSe)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                xxfpZjb.setQtfpse(qtfpse);
            }else if("Y".equals(fpbz)){//山西特色分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他开票、未开票都按销售收入×预征率计算后带出
                //20250618 根据山西移动需求文档2.7版本 分支机构销项台账税额不用再转换为乘以预征率后的数值 但是代码先保留 防止7月份再变回去
                //这个分支应该不会再走了
                //专用发票金额
                BigDecimal zyfpje = xxfphwfwmxbDOS.stream()
                        .filter(xxfphwfwmxbDO -> Arrays.asList("01", "03").contains(xxfphwfwmxbDO.getFppzDm()))
                        .map(ZnsbTzzxXxfphwfwmxbDO::getJe)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                xxfpZjb.setZyfpje(zyfpje);

                //山西移动专用发票税额
                xxfpZjb.setZyfpse(NumberUtil.mul(xxfpZjb.getZyfpje(), new BigDecimal(sxydfzjgyzl)));

                //其他发票金额
                BigDecimal qtfpje = xxfphwfwmxbDOS.stream()
                        .filter(xxfphwfwmxbDO -> !Arrays.asList("01", "03").contains(xxfphwfwmxbDO.getFppzDm()))
                        .map(ZnsbTzzxXxfphwfwmxbDO::getJe)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                xxfpZjb.setQtfpje(qtfpje);

                //山西移动其他发票税额
                xxfpZjb.setQtfpse(NumberUtil.mul(xxfpZjb.getQtfpje(), new BigDecimal(sxydfzjgyzl)));
                //税率特殊赋值 保证票面不变 但是汇总后分机构税率只有3%和5% 与报表统一
                xxfpZjb.setSl1(new BigDecimal(sxydfzjgyzl));

            }

            xxfpZjb.setNsjctzje(BigDecimal.ZERO);
            xxfpZjb.setNsjctzse(BigDecimal.ZERO);
            xxfpZjb.setSjtbSj(new Date());
            xxfpZjb.setLrrq(new Date());
            xxfpZjb.setSjcsdq("00000000000");
            xxfpZjb.setSjgsdq("00000000000");
            xxfpZjb.setYwqdDm("ZNSB.GJSS.JOB");
            result.add(xxfpZjb);
        });

        List<String> uuids = data.stream().map(ZnsbTzzxXxfphwfwmxbDO::getUuid).collect(Collectors.toList());
        StreamUtils.iterate(uuids,100,shardData -> fphwfwmxbMapper.updateZzscbzBatch(shardData));

        return zjbMapper.insertBatch(result);
    }

    @Transactional
    public void handleZjb(List<ZnsbTzxxXxfpzzbZjbDO> dos){
        if (GyUtils.isNull(dos)){
            return;
        }

        Function<ZnsbTzxxXxfpzzbZjbDO, List<Object>> compositeKey = zjbDO ->
                Arrays.asList(zjbDO.getDjxh(),zjbDO.getSszq(),zjbDO.getZsfsDm1(),zjbDO.getZsxmDm(),zjbDO.getSl1());
        Map<List<Object>, List<ZnsbTzxxXxfpzzbZjbDO>> groupingMap =
                dos.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));

        groupingMap.forEach((keys, data) -> {
            ZnsbTzxxXxfpzzbZjbDO znsbTzxxXxfpzzbZjbDO = data.get(0);
            String djxh = (String) keys.get(0);
            Integer sszq = (Integer) keys.get(1);
            String zsfsDm = (String) keys.get(2);
            String zsxmDm = (String) keys.get(3);
            BigDecimal sl1 = (BigDecimal) keys.get(4);
            ZnsbTzxxXxfpzzbDO znsbTzxxXxfpzzbDO = new ZnsbTzxxXxfpzzbDO();
            znsbTzxxXxfpzzbDO.setDjxh(djxh);
            znsbTzxxXxfpzzbDO.setSszq(sszq);
            znsbTzxxXxfpzzbDO.setJsfsDm1(zsfsDm);
            znsbTzxxXxfpzzbDO.setZsxmDm(zsxmDm);
            znsbTzxxXxfpzzbDO.setSl1(sl1);
            znsbTzxxXxfpzzbDO.setXgrq(new Date());
            znsbTzxxXxfpzzbDO.setXgrsfid("ZNSB.GJSS.JOB");

            //专用发票金额
            BigDecimal zyfpje = data.stream()
                    .map(ZnsbTzxxXxfpzzbZjbDO::getZyfpje)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            znsbTzxxXxfpzzbDO.setZyfpje(zyfpje);

            //专用发票税额
            BigDecimal zyfpse = data.stream()
                    .map(ZnsbTzxxXxfpzzbZjbDO::getZyfpse)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            znsbTzxxXxfpzzbDO.setZyfpse(zyfpse);


            //其他发票金额
            BigDecimal qtfpje = data.stream()
                    .map(ZnsbTzxxXxfpzzbZjbDO::getQtfpje)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            znsbTzxxXxfpzzbDO.setQtfpje(qtfpje);

            //其他发票税额
            BigDecimal qtfpse = data.stream()
                    .map(ZnsbTzxxXxfpzzbZjbDO::getQtfpse)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            znsbTzxxXxfpzzbDO.setQtfpse(qtfpse);

            BigDecimal nsjctzje = data.stream()
                    .map(ZnsbTzxxXxfpzzbZjbDO::getNsjctzje)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            znsbTzxxXxfpzzbDO.setNsjctzje(nsjctzje);

            BigDecimal nsjctzse = data.stream()
                    .map(ZnsbTzxxXxfpzzbZjbDO::getNsjctzse)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            znsbTzxxXxfpzzbDO.setNsjctzse(nsjctzse);

            WkpData wkpData = getWkpjeAndWkpse(keys, znsbTzxxXxfpzzbZjbDO.getNsrsbh(), znsbTzxxXxfpzzbDO);
            znsbTzxxXxfpzzbDO.setWkpje(wkpData.getWkpje());
            znsbTzxxXxfpzzbDO.setWkpse(wkpData.getWkpse());
            BigDecimal xssrje = wkpData.getXssrje();
            BigDecimal xssrse = wkpData.getXssrse();
            //20250828 getWkpjeAndWkpse 已经算好了，外面不需要再重新算一遍 故先去掉
            BigDecimal wkpje = wkpData.getWkpje();
            BigDecimal wkpse = wkpData.getWkpse();

            WkpkzData wkpkzData = wkpData.getWkpkzData();

            boolean isXgmnsr = TzzxXgmnsrUtil.isXgmnsr(sl1, djxh, znsbTzxxXxfpzzbZjbDO.getNsrsbh());
            BigDecimal cxsl1 = sl1;
            if (sl1.compareTo(new BigDecimal("0.03")) == 0 &&  zyfpje.compareTo(BigDecimal.ZERO) == 0 &&  isXgmnsr ) {//普票
                cxsl1 = new BigDecimal("0.01");
            }
            ZnsbTzxxXxfpzzbDO oldData = xxfpzzbMapper.getData(djxh, sszq, zsfsDm, zsxmDm, cxsl1);
            if (!GyUtils.isNull(oldData)){
                ZnsbTzxxXxfpzzbDO tz2 = new ZnsbTzxxXxfpzzbDO();
                BeanUtils.copyBean(oldData,tz2);
                tz2.setTzlxDm("2");
                tz2.setXgrq(new Date());
                xxfpzzbMapper.updateById(tz2);

                ZnsbTzxxXxfpzzbDO tz3 = new ZnsbTzxxXxfpzzbDO();
                BeanUtils.copyBean(oldData,tz3);
                tz3.setUuid(IdUtil.fastSimpleUUID());
                tz3.setJyssbz("N");
                tz3.setLrrq(new Date());
                tz3.setXgrq(new Date());
                tz3.setSjtbSj(new Date());
                tz3.setWkpje(tz3.getWkpje().negate());
                tz3.setWkpse(tz3.getWkpse().negate());
                tz3.setZyfpje(tz3.getZyfpje().negate());
                tz3.setZyfpse(tz3.getZyfpse().negate());
                tz3.setQtfpje(tz3.getQtfpje().negate());
                tz3.setQtfpse(tz3.getQtfpse().negate());
                //20250829 新增纳税检查调整扩展
                tz3.setNsjctzje(tz3.getNsjctzje().negate());
                tz3.setNsjctzse(tz3.getNsjctzse().negate());
                tz3.setTzlxDm("3");
                xxfpzzbMapper.insert(tz3);

                //扩展体系
                kzbDataczcl(oldData, tz3);

                ZnsbTzxxXxfpzzbDO tz4 = new ZnsbTzxxXxfpzzbDO();
                BeanUtils.copyBean(oldData,tz4);

                tz4.setUuid(IdUtil.fastSimpleUUID());
                tz4.setZyfpje(oldData.getZyfpje().add(znsbTzxxXxfpzzbDO.getZyfpje()));
                tz4.setZyfpse(oldData.getZyfpse().add(znsbTzxxXxfpzzbDO.getZyfpse()));
                tz4.setQtfpje(oldData.getQtfpje().add(znsbTzxxXxfpzzbDO.getQtfpje()));
                tz4.setQtfpse(oldData.getQtfpse().add(znsbTzxxXxfpzzbDO.getQtfpse()));
                //BigDecimal wkpje = xssrje.subtract(tz4.getZyfpje()).subtract(tz4.getQtfpje());
                //BigDecimal wkpse = xssrse.subtract(tz4.getZyfpse()).subtract(tz4.getQtfpse());
                tz4.setWkpje(wkpje);
                tz4.setWkpse(wkpse);
                tz4.setLrrq(new Date());
                tz4.setXgrq(new Date());
                tz4.setLrrsfid("ZNSB.GJSS.JOB");
                tz4.setYwqdDm("SYS");
                tz4.setJyssbz("N");
                tz4.setTzlxDm("4");
                tz4.setSjtbSj(new Date());
                //扩展体系
                kzbDatacl(wkpkzData, tz4);
                xxfpzzbMapper.insert(tz4);
            }else {
                znsbTzxxXxfpzzbDO.setNsrsbh(znsbTzxxXxfpzzbZjbDO.getNsrsbh());
                znsbTzxxXxfpzzbDO.setNsrmc(znsbTzxxXxfpzzbZjbDO.getNsrmc());
                znsbTzxxXxfpzzbDO.setGsh2(znsbTzxxXxfpzzbZjbDO.getGsh2());
                znsbTzxxXxfpzzbDO.setTzlxDm("1");
                znsbTzxxXxfpzzbDO.setLrrq(new Date());
                znsbTzxxXxfpzzbDO.setLrrsfid("ZNSB.GJSS.JOB");
                znsbTzxxXxfpzzbDO.setYwqdDm("SYS");
                znsbTzxxXxfpzzbDO.setSjcsdq("00000000000");
                znsbTzxxXxfpzzbDO.setSjgsdq("00000000000");
                znsbTzxxXxfpzzbDO.setSjtbSj(new Date());
                znsbTzxxXxfpzzbDO.setUuid(IdUtil.fastSimpleUUID());
                znsbTzxxXxfpzzbDO.setJyssbz("N");
                /*BigDecimal wkpje = xssrje.subtract(znsbTzxxXxfpzzbDO.getZyfpje()).subtract(znsbTzxxXxfpzzbDO.getQtfpje());
                BigDecimal wkpse = xssrse.subtract(znsbTzxxXxfpzzbDO.getZyfpse()).subtract(znsbTzxxXxfpzzbDO.getQtfpse());
                if(isXgmnsr && znsbTzxxXxfpzzbDO.getZyfpje().compareTo(BigDecimal.ZERO) > 0){
                    wkpje = BigDecimal.ZERO.subtract(znsbTzxxXxfpzzbDO.getQtfpje());
                    wkpse = BigDecimal.ZERO.subtract(znsbTzxxXxfpzzbDO.getQtfpse());
                }*/
                znsbTzxxXxfpzzbDO.setWkpje(wkpje);
                znsbTzxxXxfpzzbDO.setWkpse(wkpse);
                //扩展体系
                kzbDatacl(wkpkzData, znsbTzxxXxfpzzbDO);
                xxfpzzbMapper.insert(znsbTzxxXxfpzzbDO);

            }
        });

        zjbMapper.deleteBatchIds(dos.stream().map(ZnsbTzxxXxfpzzbZjbDO::getUuid).collect(Collectors.toList()));
    }

    private void kzbDatacl(WkpkzData wkpkzData, ZnsbTzxxXxfpzzbDO znsbTzxxXxfpzzbDO) {
        if(GyUtils.isNotNull(wkpkzData)){
            if("Y".equals(wkpkzData.getNsjctzbz())){
                znsbTzxxXxfpzzbDO.setNsjctzje(wkpkzData.getNsjctzje());
                znsbTzxxXxfpzzbDO.setNsjctzse(wkpkzData.getNsjctzse());
            }
            if("Y".equals(wkpkzData.getJzjtbz()) || "Y".equals(wkpkzData.getNsjctzbz())){
                ZnsbTzzxXxfpzzbKzbDO kzbDO = new ZnsbTzzxXxfpzzbKzbDO();
                kzbDO.setUuid(znsbTzxxXxfpzzbDO.getUuid());//用于后续关联
                kzbDO.setNsjctzjyssbz("N");
                kzbDO.setWkpfjzjtjyssbz("N");
                kzbDO.setWkpjzjtjyssbz("N");

                kzbDO.setNsjctzbz(wkpkzData.getNsjctzbz());
                kzbDO.setNsjctzje(wkpkzData.getNsjctzje());
                kzbDO.setNsjctzse(wkpkzData.getNsjctzse());

                kzbDO.setJzjtbz(wkpkzData.getJzjtbz());
                kzbDO.setWkpfjzjtje(wkpkzData.getWkpfjzjtje());
                kzbDO.setWkpfjzjtse(wkpkzData.getWkpfjzjtse());
                kzbDO.setWkpjzjtje(wkpkzData.getWkpjzjtje());
                kzbDO.setWkpjzjtse(wkpkzData.getWkpjzjtse());

                kzbDO.setDjxh(znsbTzxxXxfpzzbDO.getDjxh());
                kzbDO.setSszq(znsbTzxxXxfpzzbDO.getSszq());

                kzbMapper.insert(kzbDO);
            }
        }
    }

    private void kzbDataczcl(ZnsbTzxxXxfpzzbDO oldData, ZnsbTzxxXxfpzzbDO tz3) {
        final LambdaQueryWrapper<ZnsbTzzxXxfpzzbKzbDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ZnsbTzzxXxfpzzbKzbDO::getUuid, oldData.getUuid());//uuid
        ZnsbTzzxXxfpzzbKzbDO oldkzbData = kzbMapper.selectOne(queryWrapper);
        if(GyUtils.isNotNull(oldkzbData)){
            ZnsbTzzxXxfpzzbKzbDO kzbDO = new ZnsbTzzxXxfpzzbKzbDO();
            kzbDO.setUuid(tz3.getUuid());//用于后续关联
            kzbDO.setNsjctzjyssbz("N");
            kzbDO.setWkpfjzjtjyssbz("N");
            kzbDO.setWkpjzjtjyssbz("N");

            kzbDO.setNsjctzbz(oldkzbData.getNsjctzbz());
            kzbDO.setNsjctzje(oldkzbData.getNsjctzje().negate());
            kzbDO.setNsjctzse(oldkzbData.getNsjctzse().negate());

            kzbDO.setJzjtbz(oldkzbData.getJzjtbz());
            kzbDO.setWkpfjzjtje(oldkzbData.getWkpfjzjtje().negate());
            kzbDO.setWkpfjzjtse(oldkzbData.getWkpfjzjtse().negate());
            kzbDO.setWkpjzjtje(oldkzbData.getWkpjzjtje().negate());
            kzbDO.setWkpjzjtse(oldkzbData.getWkpjzjtse().negate());

            kzbDO.setDjxh(oldData.getDjxh());
            kzbDO.setSszq(oldData.getSszq());

            kzbMapper.insert(kzbDO);
        }
    }

    @Override
    public void srzbListen(Integer n) {
        //防止大征期 业务加工数据较多造成程序数据错误
        // 1 n 正常业务场景
        // 2 当n为101时 只加工当前月份 包括小规模
        // 3 当n为102时 只加工上个月份 包括小规模
        // 4 当n为103时 只加工当前月份 不包括小规模
        // 5 当n为104时 只加工上个月份 不包括小规模
        // 6 当n为105时 只加工小规模
        List<ZnsbTzzxSrzzbDO> srzzbDOS = CollectionUtil.newArrayList();
        if(n != 105){
            srzzbDOS = srzzbMapper.selectBySszq(n);
        }
        //森马 将小规模人所属账期 获取指定月份自动处理，不影响前台定时任务增值税一般人参数处理 后台自动处理
        final String jtbmSys = CacheUtils.getXtcs("**********");
        //由于目前只有森马有小规模业务 故加参数控制
        if(JtbmEnum.SM.getJtbm().equals(jtbmSys) && ( n <=100 || n == 101 || n == 102 || n == 105)){
            xgmsrsrzbListen(n, srzzbDOS);
        }
        if(GyUtils.isNotNull(srzzbDOS)){
            srzbListenHandle(srzzbDOS);
        }
    }

    @Override
    public void srzbListen(String djxh, String sszq) {
        List<ZnsbTzzxSrzzbDO> znsbTzzxSrzzbDOS = srzzbMapper.selectBySszq(djxh, sszq);
        srzbListenHandle(znsbTzzxSrzzbDOS);
    }

    private void srzbListenHandle(List<ZnsbTzzxSrzzbDO> srzzbDOS) {
        if (!GyUtils.isNull(srzzbDOS)){
            //根据DJXH,SSZQ,ZSFS_DM_1，ZSXM_DM_1，SL_1进行汇总
            Function<ZnsbTzzxSrzzbDO, List<Object>> compositeKey = xxfphwfwmxbDO ->
                    Arrays.asList(xxfphwfwmxbDO.getDjxh(),xxfphwfwmxbDO.getSszq(),xxfphwfwmxbDO.getJsfsDm1(),xxfphwfwmxbDO.getZsxmDm(),xxfphwfwmxbDO.getSl1());
            Map<List<Object>, List<ZnsbTzzxSrzzbDO>> groupingMap =
                    srzzbDOS.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));

            groupingMap.forEach((keys, znsbTzzxSrzzbDOS) -> {
                String djxh = (String) keys.get(0);
                String sszq = (String) keys.get(1);
                String zsfsDm = (String) keys.get(2);
                String zsxmDm = (String) keys.get(3);
                BigDecimal sl1 = (BigDecimal) keys.get(4);
                boolean isXgmnsr = TzzxXgmnsrUtil.isXgmnsr(sl1, djxh, znsbTzzxSrzzbDOS.get(0).getNsrsbh());
                //销项总账里是无法对冲的
                if (sl1.compareTo(new BigDecimal("0.03")) == 0 &&  isXgmnsr ) {//收入总账里的 小规模目前3% 需要一刀切 折算1% 与 销项台账1%的对冲
                    sl1 = new BigDecimal("0.01");
                }
                ZnsbTzxxXxfpzzbDO data = xxfpzzbMapper.getData(djxh, Integer.valueOf(sszq), zsfsDm, zsxmDm, sl1);
                if (GyUtils.isNull(data) || GyUtils.isNull(data.getUuid())){
                    ZnsbTzxxXxfpzzbDO znsbTzxxXxfpzzbDO = new ZnsbTzxxXxfpzzbDO();
                    znsbTzxxXxfpzzbDO.setDjxh(djxh);
                    znsbTzxxXxfpzzbDO.setSszq(Integer.valueOf(sszq));
                    znsbTzxxXxfpzzbDO.setJsfsDm1(zsfsDm);
                    znsbTzxxXxfpzzbDO.setZsxmDm(zsxmDm);
                    znsbTzxxXxfpzzbDO.setSl1(sl1);
                    znsbTzxxXxfpzzbDO.setXgrq(new Date());
                    znsbTzxxXxfpzzbDO.setXgrsfid("ZNSB.GJSS.JOB");
                    //专用发票金额
                    znsbTzxxXxfpzzbDO.setZyfpje(BigDecimal.ZERO);
                    //专用发票税额
                    znsbTzxxXxfpzzbDO.setZyfpse(BigDecimal.ZERO);
                    //其他发票金额
                    znsbTzxxXxfpzzbDO.setQtfpje(BigDecimal.ZERO);
                    //其他发票税额
                    znsbTzxxXxfpzzbDO.setQtfpse(BigDecimal.ZERO);
                    znsbTzxxXxfpzzbDO.setNsjctzje(BigDecimal.ZERO);
                    znsbTzxxXxfpzzbDO.setNsjctzse(BigDecimal.ZERO);
                    znsbTzxxXxfpzzbDO.setNsrsbh(znsbTzzxSrzzbDOS.get(0).getNsrsbh());
                    znsbTzxxXxfpzzbDO.setNsrmc(znsbTzzxSrzzbDOS.get(0).getNsrmc());
                    znsbTzxxXxfpzzbDO.setGsh2(znsbTzzxSrzzbDOS.get(0).getGsh2());
                    znsbTzxxXxfpzzbDO.setTzlxDm("1");
                    znsbTzxxXxfpzzbDO.setLrrq(new Date());
                    znsbTzxxXxfpzzbDO.setLrrsfid("ZNSB.GJSS.JOB");
                    znsbTzxxXxfpzzbDO.setYwqdDm("SYS");
                    znsbTzxxXxfpzzbDO.setSjcsdq("00000000000");
                    znsbTzxxXxfpzzbDO.setSjgsdq("00000000000");
                    znsbTzxxXxfpzzbDO.setSjtbSj(new Date());
                    znsbTzxxXxfpzzbDO.setUuid(IdUtil.fastSimpleUUID());
                    znsbTzxxXxfpzzbDO.setJyssbz("N");

                    WkpData wkpData = getWkpjeAndWkpse(keys, znsbTzzxSrzzbDOS.get(0).getNsrsbh(), znsbTzxxXxfpzzbDO);
                    znsbTzxxXxfpzzbDO.setWkpje(wkpData.getWkpje());
                    znsbTzxxXxfpzzbDO.setWkpse(wkpData.getWkpse());
                    xxfpzzbMapper.insert(znsbTzxxXxfpzzbDO);
                }else {
                    WkpData wkpData = getWkpjeAndWkpse(keys, znsbTzzxSrzzbDOS.get(0).getNsrsbh(), data);
                    BigDecimal wkpje = wkpData.getWkpje();
                    BigDecimal wkpse = wkpData.getWkpse();

                    if (wkpje.compareTo(data.getWkpje())!=0 || wkpse.compareTo(data.getWkpse())!=0){
                        ZnsbTzxxXxfpzzbDO tz2 = new ZnsbTzxxXxfpzzbDO();
                        BeanUtils.copyBean(data,tz2);
                        tz2.setTzlxDm("2");
                        tz2.setXgrq(new Date());
                        xxfpzzbMapper.updateById(tz2);

                        ZnsbTzxxXxfpzzbDO tz3 = new ZnsbTzxxXxfpzzbDO();
                        BeanUtils.copyBean(data,tz3);
                        tz3.setUuid(IdUtil.fastSimpleUUID());
                        tz3.setJyssbz("N");
                        tz3.setLrrq(new Date());
                        tz3.setXgrq(new Date());
                        tz3.setSjtbSj(new Date());
                        tz3.setWkpje(tz3.getWkpje().negate());
                        tz3.setWkpse(tz3.getWkpse().negate());
                        tz3.setZyfpje(tz3.getZyfpje().negate());
                        tz3.setZyfpse(tz3.getZyfpse().negate());
                        tz3.setQtfpje(tz3.getQtfpje().negate());
                        tz3.setQtfpse(tz3.getQtfpse().negate());
                        tz3.setTzlxDm("3");
                        xxfpzzbMapper.insert(tz3);

                        ZnsbTzxxXxfpzzbDO tz4 = new ZnsbTzxxXxfpzzbDO();
                        BeanUtils.copyBean(data,tz4);

                        tz4.setUuid(IdUtil.fastSimpleUUID());
                        tz4.setWkpje(wkpje);
                        tz4.setWkpse(wkpse);
                        tz4.setLrrq(new Date());
                        tz4.setXgrq(new Date());
                        tz4.setLrrsfid("ZNSB.GJSS.JOB");
                        tz4.setYwqdDm("SYS");
                        tz4.setJyssbz("N");
                        tz4.setTzlxDm("4");
                        tz4.setSjtbSj(new Date());
                        xxfpzzbMapper.insert(tz4);
                    }
                }
            });
        }
    }


    private WkpData getWkpjeAndWkpse(List<Object> keys, String nsrsbh, ZnsbTzxxXxfpzzbDO znsbTzxxXxfpzzbDO) {
        String djxh = (String) keys.get(0);
        String sszq = String.valueOf(keys.get(1));
        String zsfsDm = (String) keys.get(2);
        String zsxmDm = (String) keys.get(3);
        BigDecimal sl1 = (BigDecimal) keys.get(4);

        BigDecimal xssrje = BigDecimal.ZERO;
        BigDecimal xssrse = BigDecimal.ZERO;

        /*
          小规模纳税人
          需要对收入台账收入合计，反算计算申报总收入，计算规则为：收入*1.03/1.01
          未开票收入计算：申报总收入-3%税率已开票销售额-1%税率已开票销售额，未开票收入算入到1%税率中
         */
        boolean isXgmnsr = TzzxXgmnsrUtil.isXgmnsr(sl1, djxh, nsrsbh);
        BigDecimal cxsrhzsl1 = sl1;
        if(isXgmnsr && znsbTzxxXxfpzzbDO.getZyfpje().compareTo(BigDecimal.ZERO) == 0 && (sl1.compareTo(new BigDecimal("0.01")) == 0 || sl1.compareTo(new BigDecimal("0.03")) == 0)){//普票
            cxsrhzsl1 = new BigDecimal("0.03");
        }
        Page<SrzzAndSrcybdhzbDTO> page = new Page<>(1, Integer.MAX_VALUE);
        ZnsbTzzxSrzzQuerySrcybdhzb params = new ZnsbTzzxSrzzQuerySrcybdhzb();
        params.setDjxh(djxh);
        params.setSszqq(Integer.valueOf(sszq));
        params.setSszqz(Integer.valueOf(sszq));
        params.setZsxmDm1(zsxmDm);
        params.setSl1(String.valueOf(cxsrhzsl1));
        params.setJsfsDm1(zsfsDm);

        final String jtbmSys = CacheUtils.getXtcs("**********");
        IPage<SrzzAndSrcybdhzbDTO> iPage = new Page<>(1, Integer.MAX_VALUE);
        if(JtbmEnum.SM.getJtbm().equals(jtbmSys)){//森马
             iPage = znsbTzzxSrzzbMapper.querySrzzAndSrcybdhzb(page, params);
        }else if(JtbmEnum.SXYD.getJtbm().equals(jtbmSys)){//山西移动 参照森马180的
             iPage = znsbTzzxSrzzbMapper.querySrcybdhzb(page,params);
        }else if(JtbmEnum.QS.getJtbm().equals(jtbmSys)){//泉膳
            iPage = znsbTzzxSrzzbMapper.querySrzzAndSrcybdhzbqs(page,params);
        }else if (JtbmEnum.TYGJ.getJtbm().equals(jtbmSys)){//通用国际
            iPage = znsbTzzxSrzzbMapper.querySrzzAndSrcybdhzb(page, params);
        }else if(JtbmEnum.DLZG.getJtbm().equals(jtbmSys)){//大连重工
            iPage = znsbTzzxSrzzbMapper.querySrcybdhzbdlzg(page,params);
        }else if(JtbmEnum.WLJ.getJtbm().equals(jtbmSys)){//王老吉  只查znsb_tzzx_srzzb
            iPage = znsbTzzxSrzzbMapper.querySrzzbwlj(page,params);
        }

        if(JtbmEnum.WLJ.getJtbm().equals(jtbmSys)){//王老吉 未开票单独汇总 不再减去发票
            BigDecimal wkpje = BigDecimal.ZERO;
            BigDecimal wkpse = BigDecimal.ZERO;
            if (!GyUtils.isNull(iPage.getRecords())){
                wkpje = iPage.getRecords().stream().map(SrzzAndSrcybdhzbDTO::getJsje).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                wkpse = iPage.getRecords().stream().map(SrzzAndSrcybdhzbDTO::getXxse).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
            return new WkpData(xssrje, xssrse, wkpje, wkpse,null);
        }else if(JtbmEnum.DLZG.getJtbm().equals(jtbmSys)){//大连 暂时不考虑小规模
            return getWkpDataKz(djxh, iPage);
        }else{//除了大连以外的 都走以下体系 且大部分都是以下体系

            BigDecimal cxsl1 = sl1;
            if(isXgmnsr && znsbTzxxXxfpzzbDO.getZyfpje().compareTo(BigDecimal.ZERO) == 0 && sl1.compareTo(new BigDecimal("0.03")) == 0){//普票
                cxsl1 = new BigDecimal("0.01");
            }

            if (!GyUtils.isNull(iPage.getRecords())){
                xssrje = iPage.getRecords().stream().map(SrzzAndSrcybdhzbDTO::getXssr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                xssrse = iPage.getRecords().stream().map(SrzzAndSrcybdhzbDTO::getXxse).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (isXgmnsr && cxsl1.compareTo(new BigDecimal("0.01")) == 0) {
                    xssrje = xssrje.multiply(new BigDecimal("1.03")).divide(new BigDecimal("1.01"), 2, RoundingMode.HALF_UP);
                    //禅道2032 1.对于小规模销项应纳税额以及合计销项税额也调整为减按1%的模式进行计算，以确保数据一致性。
                    //20250722 与需求王博讨论后 不通过Xxse走反算逻辑 直接通过收入乘以1%计算 然后再计算未开票
                    xssrse = xssrje.multiply(new BigDecimal("0.01"));
                    xssrse = xssrse.setScale(2, RoundingMode.HALF_UP);
                }
            }

            BigDecimal wkpje = xssrje.subtract(znsbTzxxXxfpzzbDO.getZyfpje()).subtract(znsbTzxxXxfpzzbDO.getQtfpje());
            BigDecimal wkpse = xssrse.subtract(znsbTzxxXxfpzzbDO.getZyfpse()).subtract(znsbTzxxXxfpzzbDO.getQtfpse());
            if(isXgmnsr && znsbTzxxXxfpzzbDO.getZyfpje().compareTo(BigDecimal.ZERO) != 0){
                wkpje = BigDecimal.ZERO.subtract(znsbTzxxXxfpzzbDO.getQtfpje());
                wkpse = BigDecimal.ZERO.subtract(znsbTzxxXxfpzzbDO.getQtfpse());
            }
            //20250728 森马6107出现问题 因为发票票面按照业务 金额和税额是单独统计的 就会出现
            //金额 是10787.47 税额是 107.88 差了0.01的尾差，导致未开票金额是0 未开票税额是-0.01
            if(isXgmnsr && wkpje.compareTo(BigDecimal.ZERO) == 0){
                //&& wkpse.abs().compareTo(new BigDecimal("0.01")) == 0
                wkpse = BigDecimal.ZERO;
            }

            return new WkpData(xssrje, xssrse, wkpje, wkpse,null);
        }
    }

    private WkpData getWkpDataKz(String djxh, IPage<SrzzAndSrcybdhzbDTO> iPage) {
        WkpkzData wkpkzData = new WkpkzData();
        BigDecimal wkpje = BigDecimal.ZERO;//未开票汇总后的总账金额
        BigDecimal wkpse = BigDecimal.ZERO;// 未开票汇总后的总账税额

        //20250828 与需求李俊刚交流 有即征即退标签才是即征即退企业 不用管即征即退台账和发票中的即征即退数据
        boolean isJzjtnsr = TzzxXgmnsrUtil.isJzjtnsrBydjxh(djxh);
        if(isJzjtnsr){
            wkpkzData.setJzjtbz("Y");
        }else{
            wkpkzData.setJzjtbz("N");
            wkpkzData.setWkpjzjtje(BigDecimal.ZERO);
            wkpkzData.setWkpjzjtse(BigDecimal.ZERO);
            wkpkzData.setWkpfjzjtje(BigDecimal.ZERO);
            wkpkzData.setWkpfjzjtse(BigDecimal.ZERO);
        }
        //默认没有即征即退
        wkpkzData.setNsjctzbz("N");
        wkpkzData.setNsjctzje(BigDecimal.ZERO);
        wkpkzData.setNsjctzse(BigDecimal.ZERO);

        if (!GyUtils.isNull(iPage.getRecords())){

            wkpje = iPage.getRecords().stream().filter(SrzzAndSrcybdhzbDTO -> "wkp".equals(SrzzAndSrcybdhzbDTO.getYwqdDm())).map(SrzzAndSrcybdhzbDTO::getXssr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            wkpse = iPage.getRecords().stream().filter(SrzzAndSrcybdhzbDTO -> "wkp".equals(SrzzAndSrcybdhzbDTO.getYwqdDm())).map(SrzzAndSrcybdhzbDTO::getXxse).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            wkpje = wkpje.setScale(2, RoundingMode.HALF_UP);
            wkpse = wkpse.setScale(2, RoundingMode.HALF_UP);

            List<SrzzAndSrcybdhzbDTO> nsjctzList = iPage.getRecords().stream()
                    .filter(SrzzAndSrcybdhzbDTO -> "nsjctz".equals(SrzzAndSrcybdhzbDTO.getYwqdDm()))
                    .collect(Collectors.toList());
            if(GyUtils.isNotNull(nsjctzList) && nsjctzList.size()>0){//代表有纳税检查调整
                wkpkzData.setNsjctzbz("Y");
                BigDecimal nsjctzje = iPage.getRecords().stream().filter(SrzzAndSrcybdhzbDTO -> "nsjctz".equals(SrzzAndSrcybdhzbDTO.getYwqdDm())).map(SrzzAndSrcybdhzbDTO::getXssr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal nsjctzse = iPage.getRecords().stream().filter(SrzzAndSrcybdhzbDTO -> "nsjctz".equals(SrzzAndSrcybdhzbDTO.getYwqdDm())).map(SrzzAndSrcybdhzbDTO::getXxse).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                wkpkzData.setNsjctzje(nsjctzje.setScale(2, RoundingMode.HALF_UP));
                wkpkzData.setNsjctzse(nsjctzse.setScale(2, RoundingMode.HALF_UP));
            }else{
                wkpkzData.setNsjctzbz("N");
                wkpkzData.setNsjctzje(BigDecimal.ZERO);
                wkpkzData.setNsjctzse(BigDecimal.ZERO);
            }

            if(isJzjtnsr){
                BigDecimal jzjtzzje = iPage.getRecords().stream().filter(SrzzAndSrcybdhzbDTO -> "jzjtzz".equals(SrzzAndSrcybdhzbDTO.getYwqdDm())).map(SrzzAndSrcybdhzbDTO::getXssr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal jzjtzzse = iPage.getRecords().stream().filter(SrzzAndSrcybdhzbDTO -> "jzjtzz".equals(SrzzAndSrcybdhzbDTO.getYwqdDm())).map(SrzzAndSrcybdhzbDTO::getXxse).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                BigDecimal kpjzjtje = iPage.getRecords().stream().filter(SrzzAndSrcybdhzbDTO -> "jzjtfp".equals(SrzzAndSrcybdhzbDTO.getYwqdDm())).map(SrzzAndSrcybdhzbDTO::getXssr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal kpjzjtse = iPage.getRecords().stream().filter(SrzzAndSrcybdhzbDTO -> "jzjtfp".equals(SrzzAndSrcybdhzbDTO.getYwqdDm())).map(SrzzAndSrcybdhzbDTO::getXxse).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                BigDecimal wkpjzjtje = jzjtzzje.subtract(kpjzjtje);
                BigDecimal wkpjzjtse = jzjtzzse.subtract(kpjzjtse);

                BigDecimal wkpfjzjtje = jzjtzzje.subtract(kpjzjtje);
                BigDecimal wkpfjzjtse = jzjtzzse.subtract(kpjzjtse);

                wkpkzData.setWkpjzjtje(wkpjzjtje.setScale(2, RoundingMode.HALF_UP));
                wkpkzData.setWkpjzjtse(wkpjzjtse.setScale(2, RoundingMode.HALF_UP));
                wkpkzData.setWkpfjzjtje(wkpfjzjtje.setScale(2, RoundingMode.HALF_UP));
                wkpkzData.setWkpfjzjtse(wkpfjzjtse.setScale(2, RoundingMode.HALF_UP));

            }

        }

        return new WkpData(BigDecimal.ZERO, BigDecimal.ZERO, wkpje, wkpse, wkpkzData);
    }

    public String getStartMonth(String endMonthCode) {
        // 提取年份和月份部分
        String year = endMonthCode.substring(0, 4);
        int month = Integer.parseInt(endMonthCode.substring(4, 6));

        // 计算季度起始月份（假设输入始终是有效的季度末月份）
        int startMonth = ((month / 3) - 1) * 3 + 1;

        // 格式化并组合结果
        return year + String.format("%02d", startMonth);
    }


    @Data
    @AllArgsConstructor
    class WkpData {
        private BigDecimal xssrje;
        private BigDecimal xssrse;
        private BigDecimal wkpje;
        private BigDecimal wkpse;
        //以下是大连体系扩展字段
        private WkpkzData wkpkzData;
    }

    @Data
    class WkpkzData {
        //以下是大连体系扩展字段 后续扩展体系都可以应用
        private BigDecimal jzjtzzje;
        private BigDecimal jzjtzzse;
        private BigDecimal kpjzjtje;
        private BigDecimal kpjzjtse;
        private BigDecimal wkpfjzjtje;
        private BigDecimal wkpfjzjtse;
        private BigDecimal wkpjzjtje;
        private BigDecimal wkpjzjtse;
        private BigDecimal nsjctzje;
        private BigDecimal nsjctzse;
        private String jzjtbz;
        private String nsjctzbz;
    }

    public void sendJyss(ZnsbTzzxXxfpmxbDO data){
        String fplxDm = data.getFplxDm();
        String fpdmhm = data.getFpdmhm();
        String fpdm = data.getFpdm();
        Date kprq = data.getKprq();

        //分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他开票、未开票都按销售收入×预征率计算后带出
        //山西移动特色 预征率 固定为3%  只改税额
        //20250618 根据山西移动需求文档2.7版本 调整分支机构预征率算税规则：分支机构开票都按原税率计算税额，预征率计算在申报时处理台账无需特殊处理 但是代码先保留 防止7月份再变回去
        //String isSxydfzjgJgbz = getSxydJgbz(data.getDjxh(),data.getNsrsbh(),data.getXsfnsrsbh());//山西移动分支机构加工标志 Y 加工 N 不加工
        String isSxydfzjgJgbz = "N";//山西移动分支机构加工标志 Y 加工 N 不加工

        Function<DzfpKpywFpmxxxbVO, List<Object>> compositeKey = fpmx ->
                Arrays.asList(fpmx.getSphfwssflhbbm(),fpmx.getHwhyslwfwmc(),fpmx.getSl1());

        DataMap dataMap = new DataMap();
        //在查询库 电票和税控的表是不一样的
        //fplx是电票的标准
        if (Arrays.asList("81","82","85","86").contains(fplxDm)){//数字电票
            DzfpKpywFpjcxxbDO dzfpKpywFpjcxxbDO = fpjcxxbMapper.selectByFphm(fpdmhm, kprq);
            List<DzfpKpywFpmxxxbDO> dzfpKpywFpmxxxbDOS = fpmxxxbMapper.selectMx(fpdmhm, kprq);

            // 判断集合中是否存在不征税的发票
            // sphfwssflhbbm 6开头的，一般都是税率为0且属于不征税发票 故不再算税
            // 山西移动和森马都存在类似发票
            boolean existsbzsfp = dzfpKpywFpmxxxbDOS != null &&  dzfpKpywFpmxxxbDOS.stream()
                            .filter(Objects::nonNull) // 过滤掉null对象
                            .anyMatch(item ->
                                    item.getSphfwssflhbbm() != null && // 确保字段不为null
                                    item.getSphfwssflhbbm().startsWith("6") // 以"6"开头
                            );

            if (existsbzsfp) {
                return; // 存在符合条件的元素，直接返回
            }

            //山西移动特色 分支机构数电票
            sxydfzjhsdpcl(isSxydfzjgJgbz, dzfpKpywFpmxxxbDOS, sxydfzjgyzl, dzfpKpywFpjcxxbDO);

            DzfpKpywFpjcxxbVO fpjcxxb = new DzfpKpywFpjcxxbVO();
            BeanUtil.copyProperties(dzfpKpywFpjcxxbDO,fpjcxxb,false);
            List<DzfpKpywFpmxxxbVO> dzfpKpywFpmxxxbVOS = BeanUtil.copyToList(dzfpKpywFpmxxxbDOS, DzfpKpywFpmxxxbVO.class);
            fpjcxxb.setXsfnsrsbh(data.getNsrsbh());
            dataMap.setDzfpKpywFpjcxxbVO(fpjcxxb);

            List<DzfpKpywFpmxxxbVO> resultList = new ArrayList<>();
            Map<List<Object>, List<DzfpKpywFpmxxxbVO>> collect = dzfpKpywFpmxxxbVOS.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));
            collect.forEach((objects, dzfpKpywFpmxxxbVOS1) -> {
                DzfpKpywFpmxxxbVO target = new DzfpKpywFpmxxxbVO();
                DzfpKpywFpmxxxbVO dzfpKpywFpmxxxbVO = dzfpKpywFpmxxxbVOS1.get(0);
                BeanUtils.copyBean(dzfpKpywFpmxxxbVO,target);
                target.setJe(dzfpKpywFpmxxxbVOS1.stream().map(DzfpKpywFpmxxxbVO::getJe).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                target.setSe(dzfpKpywFpmxxxbVOS1.stream().map(DzfpKpywFpmxxxbVO::getSe).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

                resultList.add(target);
            });
            dataMap.setDzfpKpywFpmxxxbVOList(resultList);
        }else if (Arrays.asList("01","04","08","10").contains(fplxDm)){//税控票
            if ("01".equals(fplxDm)){
                sk01(dataMap,data,isSxydfzjgJgbz, sxydfzjgyzl);
            }else if ("04".equals(fplxDm)){
                sk04(dataMap,data,isSxydfzjgJgbz, sxydfzjgyzl);
            } else if ("08".equals(fplxDm)) {
                sk08(dataMap,data,isSxydfzjgJgbz, sxydfzjgyzl);
            } else if ("10".equals(fplxDm)) {
                sk10(dataMap,data,isSxydfzjgJgbz, sxydfzjgyzl);
            }
        }

        BaseInfo baseInfo = new BaseInfo();
        baseInfo.setUuid(data.getUuid());
        baseInfo.setFphm(fpdmhm);
        if (!GyUtils.isNull(fpdm)){
            baseInfo.setFpdm(fpdm);
        }
        baseInfo.setLysf(data.getSjcsdq());
        baseInfo.setMbsf(data.getSjcsdq());
        baseInfo.setLllxDm("");
        baseInfo.setYwxxlx("FPFS-"+fplxDm);
        baseInfo.setBwbbh("1.0");
        baseInfo.setXxcssj(String.valueOf(new Date().getTime()));

        Message message = new Message();
        message.setDataMap(dataMap);
        message.setBaseInfo(baseInfo);

        Qybq qybq = new Qybq();
        qybq.setQydSsYwxwfl("XW001");
        qybq.setYbnsrbz(GjssGyUtils.getYbnsrbz(data.getDjxh(),data.getNsrsbh()));
        message.setQybq(qybq);


        log.info("销项发票算税xxfpss报文:{}", JsonUtils.toJson(message));
        try {
            CommonResult<SbzbResponse> kpss = jyssApi.kpss(message);
            Integer code = kpss.getCode();
            log.info("销项发票算税xxfpsscode:"+code);
            if (GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode().equals(code)){
                log.info("销项发票算税xxfpss500异常");
                throw new RuntimeException(kpss.getMsg());
            }
            log.info("销项发票算税xxfpss开票算税调用结束");
            xxfpmxbMapper.updateJyssbz(data.getUuid());
        } catch (Exception e) {
            log.error("销项发票算税xxfpss交易算税报错",e);
        }
    }

    private void sk01(DataMap dataMap,ZnsbTzzxXxfpmxbDO data,String isSxydfzjgJgbz, Double yzl){
        String fphm = data.getFphm();
        Date kprq = data.getKprq();

        DzdzFpxxZzsfpDO dz = zzsfpMapper.selectByFphm(fphm, kprq);
        List<DzdzHwxxZzsfpDO> dzdzHwxxZzsfpDOS = hwxxZzsfpMapper.selectMx(fphm, kprq);

        CbFpZzszpVO sk = BeanUtils.toBean(dz, CbFpZzszpVO.class);
        sk.setTspz(dz.getTspz_dm());
        sk.setFpztbz(dz.getFpztBz());
        sk.setXsfsbh(dz.getXfsbh());
        sk.setXsfmc(dz.getXfmc());
        sk.setGmfsbh(dz.getGfsbh());
        sk.setGmfmc(dz.getGfmc());

        List<CbFpZzszpFyxmVO> skmxList = new ArrayList<>();
        if (!GyUtils.isNull(dzdzHwxxZzsfpDOS)){
            //final BigDecimal[] newHjse = {BigDecimal.ZERO};
            dzdzHwxxZzsfpDOS.forEach(dzmx -> {
                CbFpZzszpFyxmVO skmx = BeanUtils.toBean(dzmx, CbFpZzszpFyxmVO.class);
                skmx.setMxxh(Integer.parseInt(dzmx.getMxxh()));
                skmx.setSjtbSj(new Date());
                if("Y".equals(isSxydfzjgJgbz)){
                    //分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他开票、未开票都按销售收入×预征率计算后带出
                    //山西移动特色 预征率 固定为3%  只改税额
                    //03 服务 04 无形资产 05 不动产
                    if(!( skmx.getSlv().compareTo(new BigDecimal("0.05")) == 0 && Arrays.asList("3","4","5").contains(skmx.getSpbm().substring(0,1)))){
                        skmx.setSe(NumberUtil.mul(skmx.getJe(), new BigDecimal(yzl)));
                        skmx.setSlv(new BigDecimal(yzl));//与交易算税人员沟通需要重新赋值
                        //newHjse[0] = NumberUtil.add(newHjse[0],skmx.getSe());
                    }
                }
                skmxList.add(skmx);
            });
            //与交易算税人员沟通不需要重新赋值
            /*if("Y".equals(isSxydfzjgJgbz) && newHjse[0].compareTo(BigDecimal.ZERO) != 0){
                //税额重新取累加合计值进行赋值
                sk.setSe(newHjse[0]);
                //价税合计 等于 金额加上税额 重新赋值
                sk.setJshj(NumberUtil.add(sk.getSe(),sk.getJe()));
                //税率重新赋值
                sk.setSlv(new BigDecimal(yzl));
            }*/
        }

        dataMap.setCbFpZzszpVO(sk);
        dataMap.setCbFpZzszpFyxmVOList(skmxList);
    }

    private void sk04(DataMap dataMap,ZnsbTzzxXxfpmxbDO data,String isSxydfzjgJgbz, Double yzl){
        String fphm = data.getFphm();
        Date kprq = data.getKprq();

        DzdzFpxxPtfpDO dz = ptfpMapper.selectByFphm(fphm, kprq);
        List<DzdzHwxxPtfpDO> dzdzHwxxPtfpDOS = hwxxPtfpMapper.selectMx(fphm, kprq);

        CbFpZzsppVO sk = BeanUtils.toBean(dz, CbFpZzsppVO.class);
        sk.setTspz(dz.getTspz_dm());
        sk.setFpztbz(dz.getFpztBz());
        sk.setXsfsbh(dz.getXfsbh());
        sk.setXsfmc(dz.getXfmc());
        sk.setGmfsbh(dz.getGfsbh());
        sk.setGmfmc(dz.getGfmc());

        List<CbFpZzsppFyxmVO> skmxList = new ArrayList<>();
        if (!GyUtils.isNull(dzdzHwxxPtfpDOS)){
            //final BigDecimal[] newHjse = {BigDecimal.ZERO};
            dzdzHwxxPtfpDOS.forEach(dzmx -> {
                CbFpZzsppFyxmVO skmx = BeanUtils.toBean(dzmx, CbFpZzsppFyxmVO.class);
                skmx.setMxxh(Integer.parseInt(dzmx.getMxxh()));
                skmx.setSjtbSj(new Date());
                if("Y".equals(isSxydfzjgJgbz)){
                    //分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他开票、未开票都按销售收入×预征率计算后带出
                    //山西移动特色 预征率 固定为3%  只改税额
                    //03 服务 04 无形资产 05 不动产
                    if(!( skmx.getSlv().compareTo(new BigDecimal("0.05")) == 0 && Arrays.asList("3","4","5").contains(skmx.getSpbm().substring(0,1)))){
                        skmx.setSe(NumberUtil.mul(skmx.getJe(), new BigDecimal(yzl)));
                        skmx.setSlv(new BigDecimal(yzl));//与交易算税人员沟通需要重新赋值
                        //newHjse[0] = NumberUtil.add(newHjse[0],skmx.getSe());
                    }
                }
                skmxList.add(skmx);
            });
            //与交易算税人员沟通不需要重新赋值
            /*if("Y".equals(isSxydfzjgJgbz) && newHjse[0].compareTo(BigDecimal.ZERO) != 0){
                //税额重新取累加合计值进行赋值
                sk.setSe(newHjse[0]);
                //价税合计 等于 金额加上税额 重新赋值
                sk.setJshj(NumberUtil.add(sk.getSe(),sk.getJe()));
                //税率重新赋值
                sk.setSlv(new BigDecimal(yzl));
            }*/
        }

        dataMap.setCbFpZzsppVO(sk);
        dataMap.setCbFpZzsppFyxmVOList(skmxList);
    }

    private void sk08(DataMap dataMap,ZnsbTzzxXxfpmxbDO data,String isSxydfzjgJgbz, Double yzl){
        String fphm = data.getFphm();
        Date kprq = data.getKprq();

        DzdzFpxxDzzpDO dz = dzzpMapper.selectByFphm(fphm, kprq);
        List<DzdzHwxxDzzpDO> dzdzHwxxDzzpDOS = hwxxDzzpMapper.selectMx(fphm, kprq);

        CbFpDzzpVO sk = BeanUtils.toBean(dz, CbFpDzzpVO.class);
        List<CbFpDzzpXmVO> skmxList = new ArrayList<>();
        if (!GyUtils.isNull(dzdzHwxxDzzpDOS)){
            //final BigDecimal[] newHjse = {BigDecimal.ZERO};
            dzdzHwxxDzzpDOS.forEach(dzmx -> {
                CbFpDzzpXmVO skmx = BeanUtils.toBean(dzmx, CbFpDzzpXmVO.class);
                skmx.setMxxh(Integer.parseInt(dzmx.getMxxh()));
                skmx.setSjtbSj(new Date());
                if("Y".equals(isSxydfzjgJgbz)){
                    //分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他开票、未开票都按销售收入×预征率计算后带出
                    //山西移动特色 预征率 固定为3%  只改税额
                    //03 服务 04 无形资产 05 不动产
                    if(!( skmx.getSlv().compareTo(new BigDecimal("0.05")) == 0 && Arrays.asList("3","4","5").contains(skmx.getSpbm().substring(0,1)))){
                        skmx.setSe(NumberUtil.mul(skmx.getJe(), new BigDecimal(yzl)));
                        skmx.setSlv(new BigDecimal(yzl));//与交易算税人员沟通需要重新赋值
                        //newHjse[0] = NumberUtil.add(newHjse[0],skmx.getSe());
                    }
                }
                skmxList.add(skmx);
            });
            //与交易算税人员沟通不需要重新赋值
            /*if("Y".equals(isSxydfzjgJgbz) && newHjse[0].compareTo(BigDecimal.ZERO) != 0){
                //税额重新取累加合计值进行赋值
                sk.setSe(newHjse[0]);
                //价税合计 等于 金额加上税额 重新赋值
                sk.setJshj(NumberUtil.add(sk.getSe(),sk.getJe()));
                //税率重新赋值
                sk.setSlv(new BigDecimal(yzl));
            }*/
        }

        dataMap.setCbFpDzzpVO(sk);
        dataMap.setCbFpDzzpXmVOList(skmxList);
    }

    private void sk10(DataMap dataMap,ZnsbTzzxXxfpmxbDO data,String isSxydfzjgJgbz, Double yzl){
        String fphm = data.getFphm();
        Date kprq = data.getKprq();

        DzdzFpxxDzfpDO dz = dzfpMapper.selectByFphm(fphm, kprq);
        List<DzdzHwxxDzfpDO> dzdzHwxxDzfpDOS = hwxxDzfpMapper.selectMx(fphm, kprq);

        CbFpDzVO sk = BeanUtils.toBean(dz, CbFpDzVO.class);
        sk.setTspz(dz.getTspzDm());
        sk.setFpztbz(dz.getFpztBz());
        sk.setXsfsbh(dz.getXfsbh());
        sk.setXsfmc(dz.getXfmc());
        sk.setGmfsbh(dz.getGfsbh());
        sk.setGmfmc(dz.getGfmc());

        List<CbFpDzXmVO> skmxList = new ArrayList<>();
        if (!GyUtils.isNull(dzdzHwxxDzfpDOS)){
            //final BigDecimal[] newHjse = {BigDecimal.ZERO};
            dzdzHwxxDzfpDOS.forEach(dzmx -> {
                CbFpDzXmVO skmx = BeanUtils.toBean(dzmx, CbFpDzXmVO.class);
                skmx.setMxxh(Integer.parseInt(dzmx.getMxxh()));
                skmx.setSjtbSj(new Date());
                if("Y".equals(isSxydfzjgJgbz)){
                    //分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他开票、未开票都按销售收入×预征率计算后带出
                    //山西移动特色 预征率 固定为3%  只改税额
                    //03 服务 04 无形资产 05 不动产
                    if(!( skmx.getSlv().compareTo(new BigDecimal("0.05")) == 0 && Arrays.asList("3","4","5").contains(skmx.getSpbm().substring(0,1)))){
                        skmx.setSe(NumberUtil.mul(skmx.getJe(), new BigDecimal(yzl)));
                        skmx.setSlv(new BigDecimal(yzl));//与交易算税人员沟通需要重新赋值
                        //newHjse[0] = NumberUtil.add(newHjse[0],skmx.getSe());
                    }
                }
                skmxList.add(skmx);
            });
            //与交易算税人员沟通不需要重新赋值
            /*if("Y".equals(isSxydfzjgJgbz) && newHjse[0].compareTo(BigDecimal.ZERO) != 0){
                //税额重新取累加合计值进行赋值
                sk.setSe(newHjse[0]);
                //价税合计 等于 金额加上税额 重新赋值
                sk.setJshj(NumberUtil.add(sk.getSe(),sk.getJe()));
                //税率重新赋值
                sk.setSlv(new BigDecimal(yzl));
            }*/
        }

        dataMap.setCbFpDzVO(sk);
        dataMap.setCbFpDzXmVOList(skmxList);
    }

    @Schema(description = "获取纳税人基本信息")
    private JbxxmxsjVO getNsrjbxx(String nsrsbh, String djxh){
        final ZnsbMhzcQyjbxxmxReqVO nsrreqVO = new ZnsbMhzcQyjbxxmxReqVO();
        nsrreqVO.setNsrsbh(nsrsbh);
        nsrreqVO.setDjxh(djxh);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByDjxh(nsrreqVO);
        return nsrxxRes.getData().getJbxxmxsj().get(0);
    }

    @Schema(description = "纳税人是否相等")
    private boolean isNsrsbheq(String djxh, String nsrsbh) {
        String eqflag = "1";//1相等 2不相等 默认相等 查询为空或者入参nsrsbh为空 默认为相等不处理该数据
        if(GyUtils.isNull(nsrsbh)){
            log.info("山西移动分支机构判断nsrsbh为空djxh:"+djxh);
            return true;
        }
        String key = djxh + nsrsbh;
        String nsrsbheq = stringRedisTemplate.opsForValue().get("sxydfpfzjgpd:"+key+":zdyTz");
        if(GyUtils.isNull(nsrsbheq)){
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByDjxh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            JbxxmxsjVO jbxx = nsrxxByDjxh.getData().getJbxxmxsj().get(0);
            if (!GyUtils.isNull(jbxx)){
                String jbxxNsrsbh = jbxx.getNsrsbh();
                if(GyUtils.isNull(jbxxNsrsbh) || jbxxNsrsbh.equals(nsrsbh)){
                    eqflag = "1";
                }else{
                    eqflag = "2";
                }
            }
            stringRedisTemplate.opsForValue().set("sxydfpfzjgpd:"+key+":zdyTz", eqflag,30, TimeUnit.DAYS);
        }else{
            log.info("山西移动分支机构缓存有值key:"+key+"eqflag:"+eqflag);
        }
        return "1".equals(eqflag);
    }

    @Schema(description = "是否为分支机构")
    private boolean isFzjg(String djxh, String nsrsbh) {
        String eqflag = "2";//1是 2不是
        if(GyUtils.isNull(nsrsbh)){
            log.info("山西移动分支机构判断nsrsbh为空djxh:"+djxh);
            return true;
        }
        String key = djxh + nsrsbh;
        String nsrsbheq = stringRedisTemplate.opsForValue().get("sxydfzjgbz:"+key+":zdyTz");
        if(GyUtils.isNull(nsrsbheq)){
            CommonResult<List<ZfjgVO>> nsrxx = nsrxxApi.getZjgxxList(nsrsbh);
            List<ZfjgVO> list =  nsrxx.getData();
            String fzjgbz = "";
            if(GyUtils.isNull(list)){//空代表 总机构 不存在于 select * from znsb_mhzc.znsb_mhzc_zfjgb nsrsbh
                fzjgbz = "zjg";
            }else{//有代表 能查询下面所属分支机构
                fzjgbz = "fjg";
            }
            if("fjg".equals(fzjgbz)){
                eqflag = "1";
            }else{
                eqflag = "2";
            }
            stringRedisTemplate.opsForValue().set("sxydfzjgbz:"+key+":zdyTz", eqflag,30, TimeUnit.DAYS);
        }else{
            log.info("山西移动分支机构缓存有值key:"+key+"eqflag:"+eqflag);
        }
        return "1".equals(eqflag);
    }

    @Schema(description = "获取山西移动加工标志")
    private String getSxydJgbz(String djxh, String nsrsbh, String xsfnsrsbh) {
        String isSxydfzjgJgbz = "N";//山西移动分支机构加工标志 Y 加工 N 不加工
        final String jtbmSys = CacheUtils.getXtcs("**********");
        if(JtbmEnum.SXYD.getJtbm().equals(jtbmSys)){//山西移动一般人分支结构特殊处理
            Map<String,String> map = TzzxFzjgUtil.getZfjgbz(nsrsbh);
            String fzjgbz = map.get("fzjgbz");
            if("fjg".equals(fzjgbz)){
                isSxydfzjgJgbz = "Y";
            }
        }
        return isSxydfzjgJgbz;
    }

    @Schema(description = "山西移动分支机构数电票处理")
    private static void sxydfzjhsdpcl(String isSxydfzjgJgbz, List<DzfpKpywFpmxxxbDO> dzfpKpywFpmxxxbDOS, Double yzl, DzfpKpywFpjcxxbDO dzfpKpywFpjcxxbDO) {
        if("Y".equals(isSxydfzjgJgbz)){
            //分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他开票、未开票都按销售收入×预征率计算后带出
            //山西移动特色 预征率 固定为3%  只改税额
            //Double newHjse = 0.0;
            for(DzfpKpywFpmxxxbDO dzfpKpywFpmxxxbDO : dzfpKpywFpmxxxbDOS){
                // jsfs_dm_1 when ((mx.slv <= 0.06 and mx.slv > 0) or (mx.spbm like '1%' and mx.slv = 0.06)) then '02'
                /*CASE
                when mx.sphfwssflhbbm like '1%' THEN '01'
                when mx.sphfwssflhbbm like '2%' THEN '02'
                when mx.sphfwssflhbbm like '3%' THEN '03'
                when mx.sphfwssflhbbm like '5%' THEN '05'
                when mx.sphfwssflhbbm like '4%' THEN '04'
                when mx.sphfwssflhbbm like '6%' THEN '06'
                END zsxm_dm*/
                //03 服务 04 无形资产 05 不动产
                if(!(0.05 == dzfpKpywFpmxxxbDO.getSl1() && Arrays.asList("3","4","5").contains(dzfpKpywFpmxxxbDO.getSphfwssflhbbm().substring(0,1)))){
                    dzfpKpywFpmxxxbDO.setSe(NumberUtil.mul(dzfpKpywFpmxxxbDO.getJe(), yzl));
                    dzfpKpywFpmxxxbDO.setSl1(yzl);//与交易算税人员沟通需要重新赋值
                    //newHjse = NumberUtil.add(newHjse,dzfpKpywFpmxxxbDO.getSe());
                }
            }
            //税额重新取累加合计值进行赋值
            //dzfpKpywFpjcxxbDO.setHjse(newHjse);//与交易算税人员沟通不需要重新赋值
            //价税合计 等于 金额加上税额 重新赋值
            //dzfpKpywFpjcxxbDO.setJshj(NumberUtil.add(dzfpKpywFpjcxxbDO.getHjje(), dzfpKpywFpjcxxbDO.getHjse()));//与交易算税人员沟通不需要重新赋值
        }
    }

    private void xgmsrsrzbListen(Integer n, List<ZnsbTzzxSrzzbDO> srzzbDOS) {
        // 1 n 正常业务场景
        // 2 当n为101时 只加工当前月份 包括小规模
        // 3 当n为102时 只加工上个月份 包括小规模
        // 4 当n为103时 只加工当前月份 不包括小规模
        // 5 当n为104时 只加工上个月份 不包括小规模
        // 6 当n为105时 只加工小规模
        if (GyUtils.isNull(n) || n <= 0) {
            n = 1;//返回当前月
        }
        List<String> periods = new ArrayList<>();
        YearMonth currentYearMonth = YearMonth.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        if(n == 101){
            YearMonth period = currentYearMonth.minusMonths(0);
            periods.add(period.format(formatter));
        }else if(n == 102){
            YearMonth period = currentYearMonth.minusMonths(1);
            periods.add(period.format(formatter));
        }else if(n == 105){
            periods.add("9999-12-31");
        }else{
            for (int i = 0; i < n; i++) {
                YearMonth period = currentYearMonth.minusMonths(i);
                periods.add(period.format(formatter));
            }
        }
        String xgmsszq = TzzxXgmnsrUtil.generateDateString();
        if(!periods.contains(xgmsszq)){
            List<String> djxhArr = companyApi.getAllDjxh().getData();
            List<String> djxhList = djxhArr.stream()
                    .filter(djxh -> TzzxXgmnsrUtil.isXgmnsrBydjxh(djxh))
                    .filter(djxh -> !TzzxXgmnsrUtil.isKqhBydjxh(djxh))
                    .collect(Collectors.toList());
            List<ZnsbTzzxSrzzbDO> xgmDOS = srzzbMapper.selectBySszqandIndjxhlist(xgmsszq,djxhList);
            if(n == 105){
                srzzbDOS = xgmDOS ;
            }else if(GyUtils.isNotNull(srzzbDOS) && GyUtils.isNotNull(xgmDOS)){
                srzzbDOS.addAll(xgmDOS);
            }
        }
    }

    @Override
    public void xxzzhzbListen(Integer n, String djxh) {

        final String jtbmSys = CacheUtils.getXtcs("**********");
        //目前只有大连走以前体系 后续要从加参数扩展体系
        if(!jtbmSys.equals(JtbmEnum.DLZG.getJtbm())){
            return;
        }
        if (GyUtils.isNull(n) || n <= 0) {
            n = 1;//返回当前月
        }
        List<String> periods = new ArrayList<>();
        YearMonth currentYearMonth = YearMonth.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        for (int i = 0; i < n; i++) {
            YearMonth period = currentYearMonth.minusMonths(i);
            periods.add(period.format(formatter));
        }
        QueryWrapper<ZnsbTzzxSrcybdhzbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ZnsbTzzxSrcybdhzbDO::getSszq, periods);
        if(GyUtils.isNotNull(djxh)){
            wrapper.lambda().eq(ZnsbTzzxSrcybdhzbDO::getDjxh, djxh);
        }
        List<ZnsbTzzxSrcybdhzbDO>  listdata = znsbTzzxSrcybdhzbMapper.selectList(wrapper);
        //主要是为了分组，listdata包含了4种数据，重新比对，是为了将数据在销项总账表里补全
        //return selectList(wrapper);
        xxzzhzbListenHandle(listdata);
    }

    @Override
    public void xxzzhzbListen(String djxh, String sszq) {

        final String jtbmSys = CacheUtils.getXtcs("**********");
        //目前只有大连走以前体系 后续要从加参数扩展体系
        if(!jtbmSys.equals(JtbmEnum.DLZG.getJtbm())){
            return;
        }
        QueryWrapper<ZnsbTzzxSrcybdhzbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ZnsbTzzxSrcybdhzbDO::getSszq, sszq);
        wrapper.lambda().eq(ZnsbTzzxSrcybdhzbDO::getDjxh, djxh);
        List<ZnsbTzzxSrcybdhzbDO>  listdata = znsbTzzxSrcybdhzbMapper.selectList(wrapper);
        //主要是为了分组，listdata包含了4种数据，重新比对，是为了将数据在销项总账表里补全
        //return selectList(wrapper);
        xxzzhzbListenHandle(listdata);
    }

    private void xxzzhzbListenHandle(List<ZnsbTzzxSrcybdhzbDO> listdata) {
        if (!GyUtils.isNull(listdata)){
            //根据DJXH,SSZQ,ZSFS_DM_1，ZSXM_DM_1，SL_1进行汇总
            Function<ZnsbTzzxSrcybdhzbDO, List<Object>> compositeKey = xxfphwfwmxbDO ->
                    Arrays.asList(xxfphwfwmxbDO.getDjxh(),xxfphwfwmxbDO.getSszq(),xxfphwfwmxbDO.getJsfsDm1(),xxfphwfwmxbDO.getZsxmDm(),xxfphwfwmxbDO.getSl1());
            Map<List<Object>, List<ZnsbTzzxSrcybdhzbDO>> groupingMap =
                    listdata.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));

            groupingMap.forEach((keys, znsbTzzxSrzzbDOS) -> {
                String djxh1 = (String) keys.get(0);
                String sszq = (String) keys.get(1);
                String zsfsDm = (String) keys.get(2);
                String zsxmDm = (String) keys.get(3);
                BigDecimal sl1 = (BigDecimal) keys.get(4);

                ZnsbTzxxXxfpzzbDO data = xxfpzzbMapper.getData(djxh1, Integer.valueOf(sszq), zsfsDm, zsxmDm, sl1);
                if (GyUtils.isNull(data) || GyUtils.isNull(data.getUuid())){
                    ZnsbTzxxXxfpzzbDO znsbTzxxXxfpzzbDO = new ZnsbTzxxXxfpzzbDO();
                    znsbTzxxXxfpzzbDO.setDjxh(djxh1);
                    znsbTzxxXxfpzzbDO.setSszq(Integer.valueOf(sszq));
                    znsbTzxxXxfpzzbDO.setJsfsDm1(zsfsDm);
                    znsbTzxxXxfpzzbDO.setZsxmDm(zsxmDm);
                    znsbTzxxXxfpzzbDO.setSl1(sl1);
                    znsbTzxxXxfpzzbDO.setXgrq(new Date());
                    znsbTzxxXxfpzzbDO.setXgrsfid("ZNSB.XXZZKZB.JOB");
                    //专用发票金额
                    znsbTzxxXxfpzzbDO.setZyfpje(BigDecimal.ZERO);
                    //专用发票税额
                    znsbTzxxXxfpzzbDO.setZyfpse(BigDecimal.ZERO);
                    //其他发票金额
                    znsbTzxxXxfpzzbDO.setQtfpje(BigDecimal.ZERO);
                    //其他发票税额
                    znsbTzxxXxfpzzbDO.setQtfpse(BigDecimal.ZERO);
                    znsbTzxxXxfpzzbDO.setNsjctzje(BigDecimal.ZERO);
                    znsbTzxxXxfpzzbDO.setNsjctzse(BigDecimal.ZERO);
                    znsbTzxxXxfpzzbDO.setNsrsbh(znsbTzzxSrzzbDOS.get(0).getNsrsbh());
                    znsbTzxxXxfpzzbDO.setNsrmc(TzzxXgmnsrUtil.cxnsrmcBydjxh(djxh1));
                    znsbTzxxXxfpzzbDO.setGsh2(znsbTzzxSrzzbDOS.get(0).getGsh2());
                    znsbTzxxXxfpzzbDO.setTzlxDm("1");
                    znsbTzxxXxfpzzbDO.setLrrq(new Date());
                    znsbTzxxXxfpzzbDO.setLrrsfid("ZNSB.XXZZKZB.JOB");
                    znsbTzxxXxfpzzbDO.setYwqdDm("SYS");
                    znsbTzxxXxfpzzbDO.setSjcsdq("00000000000");
                    znsbTzxxXxfpzzbDO.setSjgsdq("00000000000");
                    znsbTzxxXxfpzzbDO.setSjtbSj(new Date());
                    znsbTzxxXxfpzzbDO.setUuid(IdUtil.fastSimpleUUID());
                    znsbTzxxXxfpzzbDO.setJyssbz("N");

                    WkpData wkpData = getWkpjeAndWkpse(keys, znsbTzzxSrzzbDOS.get(0).getNsrsbh(), znsbTzxxXxfpzzbDO);
                    znsbTzxxXxfpzzbDO.setWkpje(wkpData.getWkpje());
                    znsbTzxxXxfpzzbDO.setWkpse(wkpData.getWkpse());
                    //扩展体系
                    WkpkzData wkpkzData = wkpData.getWkpkzData();
                    kzbDatacl(wkpkzData, znsbTzxxXxfpzzbDO);

                    xxfpzzbMapper.insert(znsbTzxxXxfpzzbDO);
                }else {
                    WkpData wkpData = getWkpjeAndWkpse(keys, znsbTzzxSrzzbDOS.get(0).getNsrsbh(), data);
                    BigDecimal wkpje = wkpData.getWkpje();
                    BigDecimal wkpse = wkpData.getWkpse();
                    //监控未开票 纳税检查调整
                    WkpkzData wkpkzData = wkpData.getWkpkzData();
                    BigDecimal nsjctzje = wkpkzData.getNsjctzje();
                    BigDecimal nsjctzse = wkpkzData.getNsjctzse();
                    BigDecimal wkpjzjtje = wkpkzData.getWkpjzjtje();
                    BigDecimal wkpjzjtse = wkpkzData.getWkpjzjtse();
                    BigDecimal wkpfjzjtje = wkpkzData.getWkpfjzjtje();
                    BigDecimal wkpfjzjtse = wkpkzData.getWkpfjzjtse();
                    //以下是非即征即退用户监控比较
                    boolean isJzjtnsr = TzzxXgmnsrUtil.isJzjtnsrBydjxh(djxh1);
                    if (!isJzjtnsr &&
                            (wkpje.compareTo(data.getWkpje())!=0 || wkpse.compareTo(data.getWkpse())!=0
                                    || nsjctzje.compareTo(data.getNsjctzje())!=0 || nsjctzse.compareTo(data.getNsjctzse())!=0)){
                        xxzzbandxxhzbcl(data, wkpje, wkpse, wkpkzData);
                    }
                    //即征即退用户监控比较 需要单独另外比较 未开票非即征即退与未开票即征即退部分
                    //查询扩展表历史记录
                    final LambdaQueryWrapper<ZnsbTzzxXxfpzzbKzbDO> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ZnsbTzzxXxfpzzbKzbDO::getUuid, data.getUuid());//uuid
                    ZnsbTzzxXxfpzzbKzbDO oldkzbData = kzbMapper.selectOne(queryWrapper);

                    if (isJzjtnsr &&
                            (wkpje.compareTo(data.getWkpje())!=0 || wkpse.compareTo(data.getWkpse())!=0
                                    || nsjctzje.compareTo(data.getNsjctzje())!=0 || nsjctzse.compareTo(data.getNsjctzse())!=0)
                            || (GyUtils.isNotNull(oldkzbData) && (wkpjzjtje.compareTo(oldkzbData.getWkpjzjtje())!=0
                            || wkpjzjtse.compareTo(oldkzbData.getWkpjzjtse())!=0
                            || wkpfjzjtje.compareTo(oldkzbData.getWkpfjzjtje())!=0
                            || wkpfjzjtse.compareTo(oldkzbData.getWkpfjzjtse())!=0
                    ))
                    ){
                        xxzzbandxxhzbcl(data, wkpje, wkpse, wkpkzData);
                    }
                }
            });
        }
    }

    private void xxzzbandxxhzbcl(ZnsbTzxxXxfpzzbDO data, BigDecimal wkpje, BigDecimal wkpse, WkpkzData wkpkzData) {
        ZnsbTzxxXxfpzzbDO tz2 = new ZnsbTzxxXxfpzzbDO();
        BeanUtils.copyBean(data,tz2);
        tz2.setTzlxDm("2");
        tz2.setXgrq(new Date());
        xxfpzzbMapper.updateById(tz2);

        ZnsbTzxxXxfpzzbDO tz3 = new ZnsbTzxxXxfpzzbDO();
        BeanUtils.copyBean(data,tz3);
        tz3.setUuid(IdUtil.fastSimpleUUID());
        tz3.setJyssbz("N");
        tz3.setLrrq(new Date());
        tz3.setXgrq(new Date());
        tz3.setSjtbSj(new Date());
        tz3.setWkpje(tz3.getWkpje().negate());
        tz3.setWkpse(tz3.getWkpse().negate());
        tz3.setZyfpje(tz3.getZyfpje().negate());
        tz3.setZyfpse(tz3.getZyfpse().negate());
        tz3.setQtfpje(tz3.getQtfpje().negate());
        tz3.setQtfpse(tz3.getQtfpse().negate());
        tz3.setNsjctzje(tz3.getNsjctzje().negate());
        tz3.setNsjctzse(tz3.getNsjctzse().negate());
        tz3.setTzlxDm("3");
        xxfpzzbMapper.insert(tz3);
        //扩展体系
        kzbDataczcl(data, tz3);

        ZnsbTzxxXxfpzzbDO tz4 = new ZnsbTzxxXxfpzzbDO();
        BeanUtils.copyBean(data,tz4);

        tz4.setUuid(IdUtil.fastSimpleUUID());
        tz4.setWkpje(wkpje);
        tz4.setWkpse(wkpse);
        tz4.setLrrq(new Date());
        tz4.setXgrq(new Date());
        tz4.setLrrsfid("ZNSB.XXZZKZB.JOB");
        tz4.setYwqdDm("SYS");
        tz4.setJyssbz("N");
        tz4.setTzlxDm("4");
        tz4.setSjtbSj(new Date());
        //扩展体系
        kzbDatacl(wkpkzData, tz4);
        xxfpzzbMapper.insert(tz4);
    }

}
