package com.css.znsb.gjss.pojo.domain.zzs.jxfp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 进项发票货物服务明细表
 * @TableName znsb_tzzx_jxfphwfwmxb
 */
@TableName(value ="znsb_tzzx_jxfphwfwmxb")
@Data
public class ZnsbTzzxJxfphwfwmxbDO implements Serializable {
    /**
     * UUID||uuid
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 所属账期
     */
    @TableField(value = "sszq")
    private Integer sszq;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private String djxh;

    /**
     * 纳税人识别号
     */
    @TableField(value = "nsrsbh")
    private String nsrsbh;

    /**
     * 纳税人名称
     */
    @TableField(value = "nsrmc")
    private String nsrmc;

    /**
     * 发票类型代码
     */
    @TableField(value = "fplx_dm")
    private String fplxDm;

    /**
     * 发票票种代码
     */
    @TableField(value = "fppz_dm")
    private String fppzDm;

    /**
     * fpdm||fpdm
     */
    @TableField(value = "fpdm")
    private String fpdm;

    /**
     * 发票代码号码
     */
    @TableField(value = "fpdmhm")
    private String fpdmhm;

    /**
     * 发票号码
     */
    @TableField(value = "fphm")
    private String fphm;

    /**
     * 开票日期
     */
    @TableField(value = "kprq")
    private Date kprq;

    /**
     * 销售方纳税人识别代号
     */
    @TableField(value = "xsfnsrsbh")
    private String xsfnsrsbh;

    /**
     * 销售方名称
     */
    @TableField(value = "xsfmc")
    private String xsfmc;

    /**
     * 货物或应税劳务、服务名称
     */
    @TableField(value = "hwhyslwfwmc")
    private String hwhyslwfwmc;

    /**
     * 商品和服务税收分类合并编码
     */
    @TableField(value = "sphfwssflhbbm")
    private String sphfwssflhbbm;

    /**
     * 规格型号
     */
    @TableField(value = "ggxh")
    private String ggxh;

    /**
     * 数量
     */
    @TableField(value = "fpspsl")
    private String fpspsl;

    /**
     * 单价
     */
    @TableField(value = "fpspdj")
    private String fpspdj;

    /**
     * 单位
     */
    @TableField(value = "dw")
    private String dw;

    /**
     * 金额
     */
    @TableField(value = "je")
    private BigDecimal je;

    /**
     * 税额
     */
    @TableField(value = "se")
    private BigDecimal se;

    /**
     * 价税合计
     */
    @TableField(value = "jshj")
    private BigDecimal jshj;

    /**
     * 即征即退标志
     */
    @TableField(value = "jzjtbz")
    private String jzjtbz;

    /**
     * 调账类型代码
     */
    @TableField(value = "tzlx_dm")
    private String tzlxDm;

    /**
     * 税率
     */
    @TableField(value = "sl_1")
    private BigDecimal sl1;

    /**
     * 总账生成标志||总账生成标志
     */
    @TableField(value = "zzscbz")
    private String zzscbz;

    /**
     * 交易算税标志||交易算税标志
     */
    @TableField(value = "jyssbz")
    private String jyssbz;

    /**
     * 业务渠道代码
     */
    @TableField(value = "ywqd_dm")
    private String ywqdDm;

    /**
     * 录入日期
     */
    @TableField(value = "lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField(value = "xgrq")
    private Date xgrq;

    /**
     * 数据产生地区
     */
    @TableField(value = "sjcsdq")
    private String sjcsdq;

    /**
     * 数据归属地区
     */
    @TableField(value = "sjgsdq")
    private String sjgsdq;

    /**
     * 修改人身份id
     */
    @TableField(value = "xgrsfid")
    private String xgrsfid;

    /**
     * 录入人身份id
     */
    @TableField(value = "lrrsfid")
    private String lrrsfid;

    /**
     * 数据同步时间
     */
    @TableField(value = "sjtb_sj")
    private Date sjtbSj;

    /**
     * 逾期认证标志||N非逾期认证 Y逾期认证
     */
    @TableField(value = "yqrzbz")
    private String yqrzbz;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ZnsbTzzxJxfphwfwmxbDO other = (ZnsbTzzxJxfphwfwmxbDO) that;
        return (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
            && (this.getSszq() == null ? other.getSszq() == null : this.getSszq().equals(other.getSszq()))
            && (this.getDjxh() == null ? other.getDjxh() == null : this.getDjxh().equals(other.getDjxh()))
            && (this.getNsrsbh() == null ? other.getNsrsbh() == null : this.getNsrsbh().equals(other.getNsrsbh()))
            && (this.getNsrmc() == null ? other.getNsrmc() == null : this.getNsrmc().equals(other.getNsrmc()))
            && (this.getFplxDm() == null ? other.getFplxDm() == null : this.getFplxDm().equals(other.getFplxDm()))
            && (this.getFppzDm() == null ? other.getFppzDm() == null : this.getFppzDm().equals(other.getFppzDm()))
            && (this.getFpdm() == null ? other.getFpdm() == null : this.getFpdm().equals(other.getFpdm()))
            && (this.getFpdmhm() == null ? other.getFpdmhm() == null : this.getFpdmhm().equals(other.getFpdmhm()))
            && (this.getFphm() == null ? other.getFphm() == null : this.getFphm().equals(other.getFphm()))
            && (this.getKprq() == null ? other.getKprq() == null : this.getKprq().equals(other.getKprq()))
            && (this.getXsfnsrsbh() == null ? other.getXsfnsrsbh() == null : this.getXsfnsrsbh().equals(other.getXsfnsrsbh()))
            && (this.getXsfmc() == null ? other.getXsfmc() == null : this.getXsfmc().equals(other.getXsfmc()))
            && (this.getHwhyslwfwmc() == null ? other.getHwhyslwfwmc() == null : this.getHwhyslwfwmc().equals(other.getHwhyslwfwmc()))
            && (this.getSphfwssflhbbm() == null ? other.getSphfwssflhbbm() == null : this.getSphfwssflhbbm().equals(other.getSphfwssflhbbm()))
            && (this.getGgxh() == null ? other.getGgxh() == null : this.getGgxh().equals(other.getGgxh()))
            && (this.getFpspsl() == null ? other.getFpspsl() == null : this.getFpspsl().equals(other.getFpspsl()))
            && (this.getFpspdj() == null ? other.getFpspdj() == null : this.getFpspdj().equals(other.getFpspdj()))
            && (this.getDw() == null ? other.getDw() == null : this.getDw().equals(other.getDw()))
            && (this.getJe() == null ? other.getJe() == null : this.getJe().equals(other.getJe()))
            && (this.getSe() == null ? other.getSe() == null : this.getSe().equals(other.getSe()))
            && (this.getJshj() == null ? other.getJshj() == null : this.getJshj().equals(other.getJshj()))
            && (this.getJzjtbz() == null ? other.getJzjtbz() == null : this.getJzjtbz().equals(other.getJzjtbz()))
            && (this.getTzlxDm() == null ? other.getTzlxDm() == null : this.getTzlxDm().equals(other.getTzlxDm()))
            && (this.getSl1() == null ? other.getSl1() == null : this.getSl1().equals(other.getSl1()))
            && (this.getZzscbz() == null ? other.getZzscbz() == null : this.getZzscbz().equals(other.getZzscbz()))
            && (this.getJyssbz() == null ? other.getJyssbz() == null : this.getJyssbz().equals(other.getJyssbz()))
            && (this.getYwqdDm() == null ? other.getYwqdDm() == null : this.getYwqdDm().equals(other.getYwqdDm()))
            && (this.getLrrq() == null ? other.getLrrq() == null : this.getLrrq().equals(other.getLrrq()))
            && (this.getXgrq() == null ? other.getXgrq() == null : this.getXgrq().equals(other.getXgrq()))
            && (this.getSjcsdq() == null ? other.getSjcsdq() == null : this.getSjcsdq().equals(other.getSjcsdq()))
            && (this.getSjgsdq() == null ? other.getSjgsdq() == null : this.getSjgsdq().equals(other.getSjgsdq()))
            && (this.getXgrsfid() == null ? other.getXgrsfid() == null : this.getXgrsfid().equals(other.getXgrsfid()))
            && (this.getLrrsfid() == null ? other.getLrrsfid() == null : this.getLrrsfid().equals(other.getLrrsfid()))
            && (this.getSjtbSj() == null ? other.getSjtbSj() == null : this.getSjtbSj().equals(other.getSjtbSj()))
            && (this.getYqrzbz() == null ? other.getYqrzbz() == null : this.getYqrzbz().equals(other.getYqrzbz()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getSszq() == null) ? 0 : getSszq().hashCode());
        result = prime * result + ((getDjxh() == null) ? 0 : getDjxh().hashCode());
        result = prime * result + ((getNsrsbh() == null) ? 0 : getNsrsbh().hashCode());
        result = prime * result + ((getNsrmc() == null) ? 0 : getNsrmc().hashCode());
        result = prime * result + ((getFplxDm() == null) ? 0 : getFplxDm().hashCode());
        result = prime * result + ((getFppzDm() == null) ? 0 : getFppzDm().hashCode());
        result = prime * result + ((getFpdm() == null) ? 0 : getFpdm().hashCode());
        result = prime * result + ((getFpdmhm() == null) ? 0 : getFpdmhm().hashCode());
        result = prime * result + ((getFphm() == null) ? 0 : getFphm().hashCode());
        result = prime * result + ((getKprq() == null) ? 0 : getKprq().hashCode());
        result = prime * result + ((getXsfnsrsbh() == null) ? 0 : getXsfnsrsbh().hashCode());
        result = prime * result + ((getXsfmc() == null) ? 0 : getXsfmc().hashCode());
        result = prime * result + ((getHwhyslwfwmc() == null) ? 0 : getHwhyslwfwmc().hashCode());
        result = prime * result + ((getSphfwssflhbbm() == null) ? 0 : getSphfwssflhbbm().hashCode());
        result = prime * result + ((getGgxh() == null) ? 0 : getGgxh().hashCode());
        result = prime * result + ((getFpspsl() == null) ? 0 : getFpspsl().hashCode());
        result = prime * result + ((getFpspdj() == null) ? 0 : getFpspdj().hashCode());
        result = prime * result + ((getDw() == null) ? 0 : getDw().hashCode());
        result = prime * result + ((getJe() == null) ? 0 : getJe().hashCode());
        result = prime * result + ((getSe() == null) ? 0 : getSe().hashCode());
        result = prime * result + ((getJshj() == null) ? 0 : getJshj().hashCode());
        result = prime * result + ((getJzjtbz() == null) ? 0 : getJzjtbz().hashCode());
        result = prime * result + ((getTzlxDm() == null) ? 0 : getTzlxDm().hashCode());
        result = prime * result + ((getSl1() == null) ? 0 : getSl1().hashCode());
        result = prime * result + ((getZzscbz() == null) ? 0 : getZzscbz().hashCode());
        result = prime * result + ((getJyssbz() == null) ? 0 : getJyssbz().hashCode());
        result = prime * result + ((getYwqdDm() == null) ? 0 : getYwqdDm().hashCode());
        result = prime * result + ((getLrrq() == null) ? 0 : getLrrq().hashCode());
        result = prime * result + ((getXgrq() == null) ? 0 : getXgrq().hashCode());
        result = prime * result + ((getSjcsdq() == null) ? 0 : getSjcsdq().hashCode());
        result = prime * result + ((getSjgsdq() == null) ? 0 : getSjgsdq().hashCode());
        result = prime * result + ((getXgrsfid() == null) ? 0 : getXgrsfid().hashCode());
        result = prime * result + ((getLrrsfid() == null) ? 0 : getLrrsfid().hashCode());
        result = prime * result + ((getSjtbSj() == null) ? 0 : getSjtbSj().hashCode());
        result = prime * result + ((getYqrzbz() == null) ? 0 : getYqrzbz().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", uuid=").append(uuid);
        sb.append(", sszq=").append(sszq);
        sb.append(", djxh=").append(djxh);
        sb.append(", nsrsbh=").append(nsrsbh);
        sb.append(", nsrmc=").append(nsrmc);
        sb.append(", fplxDm=").append(fplxDm);
        sb.append(", fppzDm=").append(fppzDm);
        sb.append(", fpdm=").append(fpdm);
        sb.append(", fpdmhm=").append(fpdmhm);
        sb.append(", fphm=").append(fphm);
        sb.append(", kprq=").append(kprq);
        sb.append(", xsfnsrsbh=").append(xsfnsrsbh);
        sb.append(", xsfmc=").append(xsfmc);
        sb.append(", hwhyslwfwmc=").append(hwhyslwfwmc);
        sb.append(", sphfwssflhbbm=").append(sphfwssflhbbm);
        sb.append(", ggxh=").append(ggxh);
        sb.append(", fpspsl=").append(fpspsl);
        sb.append(", fpspdj=").append(fpspdj);
        sb.append(", dw=").append(dw);
        sb.append(", je=").append(je);
        sb.append(", se=").append(se);
        sb.append(", jshj=").append(jshj);
        sb.append(", jzjtbz=").append(jzjtbz);
        sb.append(", tzlxDm=").append(tzlxDm);
        sb.append(", sl1=").append(sl1);
        sb.append(", zzscbz=").append(zzscbz);
        sb.append(", jyssbz=").append(jyssbz);
        sb.append(", ywqdDm=").append(ywqdDm);
        sb.append(", lrrq=").append(lrrq);
        sb.append(", xgrq=").append(xgrq);
        sb.append(", sjcsdq=").append(sjcsdq);
        sb.append(", sjgsdq=").append(sjgsdq);
        sb.append(", xgrsfid=").append(xgrsfid);
        sb.append(", lrrsfid=").append(lrrsfid);
        sb.append(", sjtbSj=").append(sjtbSj);
        sb.append(", yqrzbz=").append(yqrzbz);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}