package com.css.znsb.gjss.pojo.domain.yhs.pzmx;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 原始凭证明细表
 * @TableName znsb_tzzx_yspzmxb
 */
@TableName(value ="znsb_tzzx_yspzmxb")
@Data
public class ZnsbTzzxYspzmxbDO implements Serializable {
    /**
     * UUID||uuid
     */
    @TableId
    private String uuid;

    /**
     * 纳税人识别号
     */
    private String nsrsbh;

    /**
     * 票证号||票证号
     */
    private String pzh;

    /**
     * 凭证抬头
     */
    private String pztt;

    /**
     * 记账日期
     */
    private Date jzrq;

    /**
     * fpdm||fpdm
     */
    private String fpdm;

    /**
     * 备注
     */
    private String bz;

    /**
     * 借贷方标识
     */
    private String jdfbs;

    /**
     * 凭证金额
     */
    private BigDecimal pzje;

    /**
     * 项目代码
     */
    private String xmdm;

    /**
     * 科目编码
     */
    private String kmbm;

    /**
     * 传真
     */
    private String cz;

    /**
     * 银行名称
     */
    private String yhmc;

    /**
     * 票证类型||票证类型
     */
    @TableField("pzlx_1")
    private String pzlx1;

    /**
     * 利润中心
     */
    private String lrzx;

    /**
     * 期间||期间
     */
    private String qj;

    /**
     * 本币金额
     */
    private BigDecimal bbje;

    /**
     * 业务渠道代码
     */
    private String ywqdDm;

    /**
     * 录入日期
     */
    private Date lrrq;

    /**
     * 修改日期
     */
    private Date xgrq;

    /**
     * 数据产生地区
     */
    private String sjcsdq;

    /**
     * 数据归属地区
     */
    private String sjgsdq;

    /**
     * 修改人身份id
     */
    private String xgrsfid;

    /**
     * 录入人身份id
     */
    private String lrrsfid;

    /**
     * 数据同步时间
     */
    private Date sjtbSj;

    /**
     * 数据状态
     */
    private String zt;

    /**
     * 异常信息
     */
    private String ycxx;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}