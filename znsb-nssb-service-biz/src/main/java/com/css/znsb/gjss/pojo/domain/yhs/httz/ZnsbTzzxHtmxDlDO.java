package com.css.znsb.gjss.pojo.domain.yhs.httz;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: guorui
 * @Date: 2025/6/17 14:32
 * @Description: 智能申报_台账中心_合同明细_大连
 * @TableName: ZNSB_TZZX_HTMX_DL"
 **/

@TableName("ZNSB_TZZX_HTMX_DL")
@Data
public class ZnsbTzzxHtmxDlDO implements Serializable  {

    /**
     * UUID
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 组织UUID||组织UUID
     */
    @TableField(value = "zzuuid")
    private String zzuuid;

    /**
     * 主表UUID
     */
    @TableField(value = "zbuuid")
    private String zbuuid;

    /**
     * 纳税人识别号
     */
    @TableField(value = "nsrsbh")
    private String nsrsbh;

    /**
     * 纳税人名称
     */
    @TableField(value = "nsrmc")
    private String nsrmc;

    /**
     * 公司号
     */
    @TableField(value = "gsh_2")
    private String gsh2;

    /**
     * 登记序号
     */
    @TableField("djxh")
    private String djxh;

    /**
     * 所属账期
     */
    @TableField("sszq")
    private Integer sszq;

    /**
     * 业务范围
     */
    @TableField("ywfw")
    private String ywfw;

    /**
     * 应税凭证编号/应税凭证名称
     */
    @TableField("yzpzbh")
    private String yzpzbh;

    /**
     * 合同类型
     */
    @TableField("htlx")
    private String htlx;

    /**
     * 合同子类
     */
    @TableField("htzl")
    private String htzl;

    /**
     * 合同签订日期
     */
    @TableField("htqdrq")
    private Date htqdrq;

    /**
     * 不含税金额
     */
    @TableField("bhsje")
    private BigDecimal bhsje;

    /**
     * 异地标志
     */
    @TableField("ydbz")
    private String ydbz;

    /**
     * 不含税合同额标志
     */
    @TableField("bhshtebz")
    private String bhshtebz;

    /**
     * 是否计入印花税标志
     */
    @TableField("jryhsbz")
    private String jryhsbz;

    /**
     * 是否是不征税合同标志
     */
    @TableField("bzshtbz")
    private String bzshtbz;

    /**
     * 不征税原因
     */
    @TableField("bzsyy")
    private String bzsyy;

    /**
     * 书立地点
     */
    @TableField("sldd")
    private String sldd;

    /**
     * 分包合同标记
     */
    @TableField("fbhtbj")
    private String fbhtbj;

    /**
     * 框架合同标记
     */
    @TableField("kjhtbj")
    private String kjhtbj;

    /**
     * 结算日期起
     */
    @TableField("jsrqq")
    private Date jsrqq;

    /**
     * 结算日期止
     */
    @TableField("jsrqz")
    private Date jsrqz;

    /**
     * 备注
     */
    @TableField("bz")
    private String bz;

    /**
     * 调账类型代码
     */
    @TableField("tzlx_dm")
    private String tzlxDm;

    /**
     * 总账生成标志
     */
    @TableField("zzscbz")
    private String zzscbz;

    /**
     * 交易算税标志
     */
    @TableField("jyssbz")
    private String jyssbz;

    /**
     * 业务渠道代码
     */
    @TableField("ywqd_dm")
    private String ywqdDm;

    /**
     * 录入日期
     */
    @TableField("lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField("xgrq")
    private Date xgrq;

    /**
     * 数据产生地区
     */
    @TableField("sjcsdq")
    private String sjcsdq;

    /**
     * 数据归属地区
     */
    @TableField("sjgsdq")
    private String sjgsdq;

    /**
     * 修改人身份ID
     */
    @TableField("xgrsfid")
    private String xgrsfid;

    /**
     * 录入人身份ID
     */
    @TableField("lrrsfid")
    private String lrrsfid;

    /**
     * 数据同步时间
     */
    @TableField("sjtb_sj")
    private Date sjtbSj;

    /**
     * 成功标志（导入用）
     */
    @TableField("cgbz")
    private String cgbz;

    /**
     * 导入失败原因
     */
    @TableField("sbyy")
    private String sbyy;
}
