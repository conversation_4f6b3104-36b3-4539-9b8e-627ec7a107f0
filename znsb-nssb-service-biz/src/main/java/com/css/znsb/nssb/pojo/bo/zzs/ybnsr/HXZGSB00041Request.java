package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税一般人申报保存接入报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HXZGSB00041Request", propOrder = { "sbxxGrid", "jmxxGrid", "yjxxGrid", "sbsBbcTjqtxxVO", "sBzzsybnsrldxxVO", "zzsybsbSbbdxxVO" })
@XmlRootElement(name = "taxML")
@Getter
@Setter
public class HXZGSB00041Request extends TaxDoc {
    @XmlElement(nillable = true, required = true)
    protected SbxxGrid sbxxGrid;

    @XmlElement(nillable = true, required = true)
    protected JmxxGrid jmxxGrid;

    @XmlElement(nillable = true, required = true)
    protected YjxxGrid yjxxGrid;

    /**
     * 申报保存请求交互
     */
    @XmlElement(nillable = true, name = "SBSBbcTjqtxxVO", required = true)
    @JSONField(name = "SBSBbcTjqtxxVO")
    protected SBSBbcTjqtxxVO sbsBbcTjqtxxVO;

    /**
     * 申报增值税一般纳税人留底信息
     */
    @XmlElement(nillable = true, name = "SBzzsybnsrldxxVO", required = true)
    @JSONField(name = "SBzzsybnsrldxxVO")
    protected SBSaveLdxxVO sBzzsybnsrldxxVO;

    /**
     * 增值税一般人申报信息
     */
    @XmlElement(nillable = true, required = true)
    protected ZzsybsbSbbdxxVO zzsybsbSbbdxxVO;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "jmxxGridlb" })
    @Getter
    @Setter
    public static class JmxxGrid {
        protected List<JmxxGridlb> jmxxGridlb;

        /**
         * Gets the value of the jmxxGridlb property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jmxxGridlb property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getJmxxGridlb().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link JmxxGridlb}
         */
        public List<JmxxGridlb> getJmxxGridlb() {
            if (jmxxGridlb == null) {
                jmxxGridlb = new ArrayList<JmxxGridlb>();
            }
            return this.jmxxGridlb;
        }

        /**
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = { "yhpzuuid" })
        @Getter
        @Setter
        public static class JmxxGridlb {
            @XmlElement(nillable = true, required = true)
            protected Object yhpzuuid;
        }
    }

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "sbxxGridlb" })
    @Getter
    @Setter
    public static class SbxxGrid {
        protected List<SbxxGridlb> sbxxGridlb;

        /**
         * Gets the value of the sbxxGridlb property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the sbxxGridlb property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getSbxxGridlb().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SbxxGridlb}
         */
        public List<SbxxGridlb> getSbxxGridlb() {
            if (sbxxGridlb == null) {
                sbxxGridlb = new ArrayList<SbxxGridlb>();
            }
            return this.sbxxGridlb;
        }

        /**
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = { "rdpzuuid" })
        @Getter
        @Setter
        public static class SbxxGridlb {
            @XmlElement(nillable = true, required = true)
            protected Object rdpzuuid;
        }
    }

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "yjxxGridlb" })
    @Getter
    @Setter
    public static class YjxxGrid {
        protected List<YjxxGridlb> yjxxGridlb;

        /**
         * Gets the value of the yjxxGridlb property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the yjxxGridlb property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getYjxxGridlb().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link YjxxGridlb}
         */
        public List<YjxxGridlb> getYjxxGridlb() {
            if (yjxxGridlb == null) {
                yjxxGridlb = new ArrayList<YjxxGridlb>();
            }
            return this.yjxxGridlb;
        }

        /**
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = { "yjskuuid" })
        @Getter
        @Setter
        public static class YjxxGridlb {
            @XmlElement(nillable = true, required = true)
            protected Object yjskuuid;
        }
    }
}