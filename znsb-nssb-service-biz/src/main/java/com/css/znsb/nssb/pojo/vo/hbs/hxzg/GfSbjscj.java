
package com.css.znsb.nssb.pojo.vo.hbs.hxzg;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 固废申报计算及减免信息
 * 
 * <p>GfSbjscj complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="GfSbjscj">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="wrdls" type="{http://www.chinatax.gov.cn/dataspec/}wrdls" minOccurs="0"/>
 *         &lt;element name="dqzhlyl" type="{http://www.chinatax.gov.cn/dataspec/}dqzhlyl" minOccurs="0"/>
 *         &lt;element name="zszmDm" type="{http://www.chinatax.gov.cn/dataspec/}zszmDm" minOccurs="0"/>
 *         &lt;element name="zywrwlbDm" type="{http://www.chinatax.gov.cn/dataspec/}zywrwlbDm" minOccurs="0"/>
 *         &lt;element name="wrdlz" type="{http://www.chinatax.gov.cn/dataspec/}wrdlz" minOccurs="0"/>
 *         &lt;element name="dqccl" type="{http://www.chinatax.gov.cn/dataspec/}dqccl" minOccurs="0"/>
 *         &lt;element name="hgbhssybh" type="{http://www.chinatax.gov.cn/dataspec/}hgbhssybh" minOccurs="0"/>
 *         &lt;element name="syuuid" type="{http://www.chinatax.gov.cn/dataspec/}syuuid" minOccurs="0"/>
 *         &lt;element name="sbsxDm1" type="{http://www.chinatax.gov.cn/dataspec/}sbsxDm1" minOccurs="0"/>
 *         &lt;element name="swsxDm" type="{http://www.chinatax.gov.cn/dataspec/}swsxDm" minOccurs="0"/>
 *         &lt;element name="pfkmc" type="{http://www.chinatax.gov.cn/dataspec/}pfkmc" minOccurs="0"/>
 *         &lt;element name="yf" type="{http://www.chinatax.gov.cn/dataspec/}yf" minOccurs="0"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq" minOccurs="0"/>
 *         &lt;element name="dqczl" type="{http://www.chinatax.gov.cn/dataspec/}dqczl" minOccurs="0"/>
 *         &lt;element name="dqcsl" type="{http://www.chinatax.gov.cn/dataspec/}dqcsl" minOccurs="0"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq" minOccurs="0"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="jmse" type="{http://www.chinatax.gov.cn/dataspec/}jmse" minOccurs="0"/>
 *         &lt;element name="ynse" type="{http://www.chinatax.gov.cn/dataspec/}ynse" minOccurs="0"/>
 *         &lt;element name="sl1" type="{http://www.chinatax.gov.cn/dataspec/}sl1" minOccurs="0"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz" minOccurs="0"/>
 *         &lt;element name="uuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz" minOccurs="0"/>
 *         &lt;element name="sjtbSj" type="{http://www.chinatax.gov.cn/dataspec/}sjtbSj" minOccurs="0"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="ssjmxzDm" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzDm" minOccurs="0"/>
 *         &lt;element name="wrwpfl" type="{http://www.chinatax.gov.cn/dataspec/}wrwpfl" minOccurs="0"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="yjse" type="{http://www.chinatax.gov.cn/dataspec/}yjse"/>
 *         &lt;element name="ybtse" type="{http://www.chinatax.gov.cn/dataspec/}ybtse"/>
 *         &lt;element name="sbbz" type="{http://www.chinatax.gov.cn/dataspec/}sbbz" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GfSbjscj", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "wrdls",
    "dqzhlyl",
    "zszmDm",
    "zywrwlbDm",
    "wrdlz",
    "dqccl",
    "hgbhssybh",
    "syuuid",
    "sbsxDm1",
    "swsxDm",
    "pfkmc",
    "yf",
    "skssqq",
    "dqczl",
    "dqcsl",
    "zspmDm",
    "xgrq",
    "lrrDm",
    "jmse",
    "ynse",
    "sl1",
    "skssqz",
    "uuid",
    "yxbz",
    "sjtbSj",
    "sjgsdq",
    "ssjmxzDm",
    "wrwpfl",
    "xgrDm",
    "lrrq",
    "yjse",
    "ybtse",
    "sbbz"
})
public class GfSbjscj
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double wrdls;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dqzhlyl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zszmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zywrwlbDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double wrdlz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dqccl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String hgbhssybh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String syuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbsxDm1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String swsxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pfkmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yf;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dqczl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dqcsl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jmse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ynse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double sl1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String uuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sjtbSj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssjmxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double wrwpfl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double yjse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double ybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbbz;

    /**
     * 获取wrdls属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getWrdls() {
        return wrdls;
    }

    /**
     * 设置wrdls属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setWrdls(Double value) {
        this.wrdls = value;
    }

    /**
     * 获取dqzhlyl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDqzhlyl() {
        return dqzhlyl;
    }

    /**
     * 设置dqzhlyl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDqzhlyl(Double value) {
        this.dqzhlyl = value;
    }

    /**
     * 获取zszmDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZszmDm() {
        return zszmDm;
    }

    /**
     * 设置zszmDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZszmDm(String value) {
        this.zszmDm = value;
    }

    /**
     * 获取zywrwlbDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZywrwlbDm() {
        return zywrwlbDm;
    }

    /**
     * 设置zywrwlbDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZywrwlbDm(String value) {
        this.zywrwlbDm = value;
    }

    /**
     * 获取wrdlz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getWrdlz() {
        return wrdlz;
    }

    /**
     * 设置wrdlz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setWrdlz(Double value) {
        this.wrdlz = value;
    }

    /**
     * 获取dqccl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDqccl() {
        return dqccl;
    }

    /**
     * 设置dqccl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDqccl(Double value) {
        this.dqccl = value;
    }

    /**
     * 获取hgbhssybh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHgbhssybh() {
        return hgbhssybh;
    }

    /**
     * 设置hgbhssybh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHgbhssybh(String value) {
        this.hgbhssybh = value;
    }

    /**
     * 获取syuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSyuuid() {
        return syuuid;
    }

    /**
     * 设置syuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSyuuid(String value) {
        this.syuuid = value;
    }

    /**
     * 获取sbsxDm1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbsxDm1() {
        return sbsxDm1;
    }

    /**
     * 设置sbsxDm1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbsxDm1(String value) {
        this.sbsxDm1 = value;
    }

    /**
     * 获取swsxDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSwsxDm() {
        return swsxDm;
    }

    /**
     * 设置swsxDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSwsxDm(String value) {
        this.swsxDm = value;
    }

    /**
     * 获取pfkmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPfkmc() {
        return pfkmc;
    }

    /**
     * 设置pfkmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPfkmc(String value) {
        this.pfkmc = value;
    }

    /**
     * 获取yf属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYf() {
        return yf;
    }

    /**
     * 设置yf属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYf(String value) {
        this.yf = value;
    }

    /**
     * 获取skssqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * 设置skssqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * 获取dqczl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDqczl() {
        return dqczl;
    }

    /**
     * 设置dqczl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDqczl(Double value) {
        this.dqczl = value;
    }

    /**
     * 获取dqcsl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDqcsl() {
        return dqcsl;
    }

    /**
     * 设置dqcsl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDqcsl(Double value) {
        this.dqcsl = value;
    }

    /**
     * 获取zspmDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * 设置zspmDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * 获取xgrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * 设置xgrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * 获取lrrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * 设置lrrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * 获取jmse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJmse() {
        return jmse;
    }

    /**
     * 设置jmse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJmse(Double value) {
        this.jmse = value;
    }

    /**
     * 获取ynse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYnse() {
        return ynse;
    }

    /**
     * 设置ynse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYnse(Double value) {
        this.ynse = value;
    }

    /**
     * 获取sl1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSl1() {
        return sl1;
    }

    /**
     * 设置sl1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSl1(Double value) {
        this.sl1 = value;
    }

    /**
     * 获取skssqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * 设置skssqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * 获取uuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * 设置uuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUuid(String value) {
        this.uuid = value;
    }

    /**
     * 获取yxbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxbz() {
        return yxbz;
    }

    /**
     * 设置yxbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxbz(String value) {
        this.yxbz = value;
    }

    /**
     * 获取sjtbSj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjtbSj() {
        return sjtbSj;
    }

    /**
     * 设置sjtbSj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjtbSj(String value) {
        this.sjtbSj = value;
    }

    /**
     * 获取sjgsdq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * 设置sjgsdq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * 获取ssjmxzDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmxzDm() {
        return ssjmxzDm;
    }

    /**
     * 设置ssjmxzDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmxzDm(String value) {
        this.ssjmxzDm = value;
    }

    /**
     * 获取wrwpfl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getWrwpfl() {
        return wrwpfl;
    }

    /**
     * 设置wrwpfl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setWrwpfl(Double value) {
        this.wrwpfl = value;
    }

    /**
     * 获取xgrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * 设置xgrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * 获取lrrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * 设置lrrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * 获取yjse属性的值。
     * 
     */
    public double getYjse() {
        return yjse;
    }

    /**
     * 设置yjse属性的值。
     * 
     */
    public void setYjse(double value) {
        this.yjse = value;
    }

    /**
     * 获取ybtse属性的值。
     * 
     */
    public double getYbtse() {
        return ybtse;
    }

    /**
     * 设置ybtse属性的值。
     * 
     */
    public void setYbtse(double value) {
        this.ybtse = value;
    }

    /**
     * 获取sbbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbbz() {
        return sbbz;
    }

    /**
     * 设置sbbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbbz(String value) {
        this.sbbz = value;
    }

}
