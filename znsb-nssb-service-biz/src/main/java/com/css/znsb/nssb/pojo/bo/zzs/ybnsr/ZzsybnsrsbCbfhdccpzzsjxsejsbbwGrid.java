package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import com.alibaba.fastjson.annotation.JSONField;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 《成本法核定农产品增值税进项税额计算表》
 *
 * <p>zzsybnsrsb_cbfhdccpzzsjxsejsbbwGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="zzsybnsrsb_cbfhdccpzzsjxsejsbbwGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="zzsybnsrsb_cbfhdccpzzsjxsejsbbwGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_cbfhdccpzzsjxsejsbbwGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbwGrid", propOrder = { "zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO" })
public class ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid {
    @XmlElement(nillable = true, name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbwGridlbVO", required = true)
    @JSONField(name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbwGridlbVO")
    protected ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO;

    /**
     * 获取zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO属性的值。
     */
    public ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO getZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO() {
        return zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO;
    }

    /**
     * 设置zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO属性的值。
     */
    public void setZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO(ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO value) {
        this.zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO = value;
    }
}