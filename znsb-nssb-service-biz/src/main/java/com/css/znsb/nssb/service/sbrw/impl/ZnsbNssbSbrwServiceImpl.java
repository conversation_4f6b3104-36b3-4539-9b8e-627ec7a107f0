package com.css.znsb.nssb.service.sbrw.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.framework.xxzx.api.xxtx.XxtxAPI;
import com.css.framework.xxzx.pojo.vo.xxtx.SaveXtxxReqVO;
import com.css.framework.xxzx.pojo.vo.xxtx.SaveXtxxResVO;
import com.css.framework.xxzx.pojo.vo.xxtx.XtxxCSVO;
import com.css.znsb.cxtj.api.SimpleQueryApi;
import com.css.znsb.cxtj.pojo.SimpleQueryReq;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.gjss.service.zzs.ZzsCybdService;
import com.css.znsb.gjss.util.ThreadPoolManager;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.api.zqrl.ZqrlApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.mhzc.pojo.company.DjxhReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.*;
import com.css.znsb.mhzc.pojo.xzj.GszqxxVO;
import com.css.znsb.mhzc.pojo.yhxx.RyxxVO;
import com.css.znsb.nssb.constants.SbrwztConstants;
import com.css.znsb.nssb.constants.SfEnum;
import com.css.znsb.nssb.constants.enums.NsqxEnum;
import com.css.znsb.nssb.constants.enums.YesOrNoEnum;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.constants.enums.ZsxmDmEnum;
import com.css.znsb.nssb.mapper.gy.RdHznsqyrdsqMxbMapper;
import com.css.znsb.nssb.mapper.qcxx.ZnsbNssbQcxxMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbglqhjlbMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwCwbbMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.mapper.zzsyjsb.ZzsyjYsbjgbMapper;
import com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc.general.SBSbbdBdjgVO;
import com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc.general.SBSbbdBdjgmxVO;
import com.css.znsb.nssb.pojo.domain.gy.RdHznsqyrdsqMxbDO;
import com.css.znsb.nssb.pojo.domain.qcxx.ZnsbNssbQcxxDO;
import com.css.znsb.nssb.pojo.domain.sbrw.*;
import com.css.znsb.nssb.pojo.domain.sds.ZnsbNssbQysdsbaxxDO;
import com.css.znsb.nssb.pojo.dto.RefreshSbrwZtReqDTO;
import com.css.znsb.nssb.pojo.dto.qcxx.ZnsbNssbQcxxDTO;
import com.css.znsb.nssb.pojo.dto.sbjgcx.SbjgcxRespDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbmxxxDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbqkcxNewReqDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbqkcxRespDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbxxDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.*;
import com.css.znsb.nssb.pojo.vo.gy.sbzf.SbzfRespVO;
import com.css.znsb.nssb.pojo.vo.jkkp.ZnsbNssbJksjgjVO;
import com.css.znsb.nssb.pojo.vo.qysds.czzsyjd.csh.QysdsCzzsYjdQcxxItemVO;
import com.css.znsb.nssb.pojo.vo.sbrw.*;
import com.css.znsb.nssb.pojo.vo.xgmnsr.lq.qcxx.XgmnsrQcxxItemVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrQcxxItemVO;
import com.css.znsb.nssb.pojo.vo.zzsyjsb.lq.plsb.ZzsyjPlsbVO;
import com.css.znsb.nssb.service.cchxwssb.plsb.CxsPlsbService;
import com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.plsb.QysdsyjPlsbService;
import com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.qysdsbaxx.ZnsbNssbQysdsbaxxService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbBdjgService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbBdjgmxService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.css.znsb.nssb.service.skjn.ZnsbNssbJksmxService;
import com.css.znsb.nssb.service.skjn.ZnsbNssbYyjkjlService;
import com.css.znsb.nssb.service.skjn.ZnsbSkjnService;
import com.css.znsb.nssb.service.zzsxgmnsrsb.plsb.ZzsxmgnsrPlsbService;
import com.css.znsb.nssb.service.zzsybnsrsb.plsb.ZzsybnsrPlsbService;
import com.css.znsb.nssb.service.zzsyjsb.plsb.ZzsyjPlsbService;
import com.css.znsb.nssb.util.NsqxUtils;
import com.css.znsb.nssb.utils.CchxwsnssbGyUtils;
import com.css.znsb.nssb.utils.FtsCxsUtils;
import com.css.znsb.tzzx.mapper.zzstz.xxfpzz.ZnsbTzzxLrzxdzbMapper;
import com.css.znsb.tzzx.pojo.domain.tzzx.zzstz.xxfpzz.ZnsbTzzxLrzxdzbDO;
import com.css.znsb.tzzx.pojo.tzzx.ZzsCybdReqVO;
import com.css.znsb.tzzx.pojo.tzzx.ZzsCybdResVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_FORMAT;
import static cn.hutool.core.date.DatePattern.SIMPLE_MONTH_PATTERN;
import static com.css.znsb.nssb.constants.SbrwztConstants.*;
import static com.css.znsb.nssb.constants.enums.YzpzzlEnum.*;

/**
 * <AUTHOR>
 * @description 针对表【znsb_nssb_sbrw(申报任务表)】的数据库操作Service实现
 * @createDate 2024-04-27 09:52:55
 */
@Service
@Slf4j
public class ZnsbNssbSbrwServiceImpl extends ServiceImpl<ZnsbNssbSbrwMapper, ZnsbNssbSbrwDO>
    implements ZnsbNssbSbrwService {

    @Resource
    private ZnsbNssbBdjgService bdjgService;

    @Resource
    private ZnsbNssbBdjgmxService bdjgmxService;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private SimpleQueryApi queryApi;

    @Resource
    private ZqrlApi zqrlApi;

    @Resource
    private XxtxAPI xxtxAPI;

    @Resource
    private ZnsbNssbSbglqhjlbMapper znsbNssbSbglqhjlbMapper;

    @Resource
    private ZzsybnsrPlsbService zzsybnsrPlsbService;

    @Resource
    private ZzsxmgnsrPlsbService zzsxmgnsrPlsbService;

    @Resource
    private ZzsyjPlsbService zzsyjPlsbService;

    @Resource
    private ZzsCybdService cybdService;

    @Resource
    private ZnsbNssbQcxxMapper znsbNssbQcxxMapper;
    @Autowired
    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;

    @Resource
    private ZnsbSkjnService znsbSkjnService;

    @Resource
    private SjjhService sjjhService;

    @Resource
    private CxsPlsbService cxsPlsbService;

    private static final ZoneId systemDefaultZoneId = ZoneId.systemDefault();

    @Resource
    private ZnsbNssbJksmxService jksmxService;

    @Resource
    private RdHznsqyrdsqMxbMapper rdHznsqyrdsqMxbMapper;

    @Resource
    private ZnsbNssbQysdsbaxxService znsbNssbQysdsbaxxService;

    @Resource
    private QysdsyjPlsbService qysdsyjPlsbService;

    @Resource
    private ZnsbNssbSbrwCwbbMapper znsbNssbSbrwCwbbMapper;

    @Resource
    private ZnsbNssbYyjkjlService yyjkjlService;

    @Resource
    private ZzsyjYsbjgbMapper zzsyjYsbjgbMapper;

    @Resource
    private ZnsbTzzxLrzxdzbMapper znsbTzzxLrzxdzbMapper;

    @Override
    public ZnsbNssbSbrwDO getSbrwByDjxh(String djxh, Date skssqq, Date skssqz, String yzpzzlDm) {
        return baseMapper.selectOne(new LambdaQueryWrapperX<ZnsbNssbSbrwDO>().eq(ZnsbNssbSbrwDO::getDjxh, djxh)
            .eq(ZnsbNssbSbrwDO::getSkssqq, skssqq).eq(ZnsbNssbSbrwDO::getSkssqz, skssqz)
            .eq(ZnsbNssbSbrwDO::getYzpzzlDm, yzpzzlDm));
    }

    /**
     * 更新归集标志
     * 
     * @param sbrwuuid 申报任务UUID
     * @param gjbz 归集标志
     */
    @Override
    public void updateGjbz(String sbrwuuid, String gjbz) {
        if (GyUtils.isNull(sbrwuuid)) {
            return;
        }
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        if (GyUtils.isNotNull(sbrw)) {
            sbrw.setGjbz(gjbz);
            this.getBaseMapper().updateById(sbrw);
        }
    }

    /**
     * 更新申报任务批次流水号
     * 
     * @param sbrwuuid 申报任务UUID
     * @param nsrsbztDm 纳税人申报状态代码
     * @param pclsh 批次流水号
     */
    @Override
    public void updateSbrwPclsh(String sbrwuuid, String nsrsbztDm, String pclsh) {
        if (GyUtils.isNull(sbrwuuid)) {
            return;
        }
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        if (GyUtils.isNotNull(sbrw)) {
            sbrw.setNsrsbztDm(nsrsbztDm);
            sbrw.setPclsh(pclsh);
            this.getBaseMapper().updateById(sbrw);
        }
    }

    /**
     * 申报成功后更新
     * 
     * @param sbrwuuid
     * @param sbjgcxRespDTO
     */
    @Override
    public void updateSbcg(String sbrwuuid, SbjgcxRespDTO sbjgcxRespDTO) {
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        sbrw.setNsrsbztDm(SBZT_SBCG_DM);
        sbrw.setRwztDm(RWZT_YSB_DM);
        sbrw.setPclsh(sbjgcxRespDTO.getPclsh());
        sbrw.setPzxh(sbjgcxRespDTO.getPzxh());
        sbrw.setSbrq1(new Date());
        sbrw.setSbuuid(sbjgcxRespDTO.getSbuuid());
        sbrw.setYbtse(
            GyUtils.isNull(sbjgcxRespDTO.getYbtse()) ? new BigDecimal(0) : new BigDecimal(sbjgcxRespDTO.getYbtse()));
        sbrw.setGjbz("N");
        this.getBaseMapper().updateById(sbrw);
        // 发送消息提醒
        this.sendSbcgxxzx(sbrwuuid);
    }

    @Override
    public void sendSbcgxxzx(String sbrwuuid) {
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        // 区分申报类还是资料报送类
        if (RWLX_ZLBS_DM.equals(sbrw.getRwlxDm())) {
            this.getBscgParamXxzx(sbrw);
        } else {
            this.getSbcgParamXxzx(sbrw);
        }

    }

    // 组装申报成功请求消息中心参数
    private void getSbcgParamXxzx(ZnsbNssbSbrwDO sbrw) {
        SaveXtxxReqVO saveXtxxReqVO = new SaveXtxxReqVO();
        saveXtxxReqVO.setYwxtid("LXPT_ZNSB");
        saveXtxxReqVO.setYwxtfwkl("1980D5F999384736E063990C170AE6F4");
        saveXtxxReqVO.setLrrsfid("SYSTEM");
        saveXtxxReqVO.setXxmbbh("00000000000_00000006");
        List<XtxxCSVO> xtxxCSVOList = new ArrayList<>();
        XtxxCSVO xtxxCSVO = new XtxxCSVO();
        xtxxCSVO.setYwzj(sbrw.getSbrwuuid());
        // 根据djxh和nsrsbh获取jguuid
        // 根据当前登记序号和纳税人识别号查询机构uuid
        CommonResult<CompanyBasicInfoDTO> qyxxRes = companyApi.basicInfo(sbrw.getDjxh(), sbrw.getNsrsbh());
        if (!GyUtils.isNull(qyxxRes.getData())) {
            List<String> jguuidList = new ArrayList<>();
            jguuidList.add(qyxxRes.getData().getJguuid());
            xtxxCSVO.setJguuidList(jguuidList);
        } else {
            throw ServiceExceptionUtil.exception(500, "未查询到当前企业机构信息!");
        }
        // 组装消息模板报文参数
        final Map<String, Object> jsonmap = new HashMap<>();
        jsonmap.put("sbcgxxbt", "申报成功提醒");
        jsonmap.put("tbrq", DateUtils.getSystemCurrentTime(7));
        jsonmap.put("qydm", qyxxRes.getData().getQydmz());
        jsonmap.put("nsrmc", qyxxRes.getData().getNsrmc());
        jsonmap.put("sbbmc", YzpzzlEnum.getYzpzzlByDm(sbrw.getYzpzzlDm()).getMc());
        // 判断如果是财行税需要特殊处理，将名称后按照征收项目代码对应名称拼接
        if (YzpzzlEnum.CXS.getDm().equals(sbrw.getYzpzzlDm())) {
            final String zsxmMc = Objects.requireNonNull(ZsxmDmEnum.getZsxmDm(sbrw.getZsxmDm())).getName();
            jsonmap.put("sbbmc",
                new StringBuilder().append(jsonmap.get("sbbmc")).append("(").append(zsxmMc).append(")"));
        }
        jsonmap.put("skssqq", DateUtils.toDateStrByFormatIndex(sbrw.getSkssqq(), 7));
        jsonmap.put("skssqz", DateUtils.toDateStrByFormatIndex(sbrw.getSkssqz(), 7));
        jsonmap.put("ybtse", GyUtils.isNull(sbrw.getYbtse()) ? "0.00"
            : sbrw.getYbtse().setScale(2, RoundingMode.HALF_UP).toPlainString());
        jsonmap.put("qclurl","/znsb/view/nssb/sbrwmx?jguuid=" + sbrw.getJguuid());
        xtxxCSVO.setXxcs(JsonUtils.toJson(jsonmap));
        xtxxCSVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
        xtxxCSVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)), 0));
        xtxxCSVO.setXxyxj("9");
        xtxxCSVOList.add(xtxxCSVO);
        saveXtxxReqVO.setXtxxCsVOs(xtxxCSVOList);
        CommonResult<SaveXtxxResVO> result = xxtxAPI.saveXtxx(saveXtxxReqVO);
        log.info("申报成功发送消息中心保存返回报文：{}", JsonUtils.toJson(result));
    }

    // 组装资料报送成功消息中心请求参数
    private void getBscgParamXxzx(ZnsbNssbSbrwDO sbrw) {
        SaveXtxxReqVO saveXtxxReqVO = new SaveXtxxReqVO();
        saveXtxxReqVO.setYwxtid("LXPT_ZNSB");
        saveXtxxReqVO.setYwxtfwkl("1980D5F999384736E063990C170AE6F4");
        saveXtxxReqVO.setLrrsfid("SYSTEM");
        saveXtxxReqVO.setXxmbbh("00000000000_00000009");
        List<XtxxCSVO> xtxxCSVOList = new ArrayList<>();
        XtxxCSVO xtxxCSVO = new XtxxCSVO();
        xtxxCSVO.setYwzj(sbrw.getSbrwuuid());
        // 根据djxh和nsrsbh获取jguuid
        // 根据当前登记序号和纳税人识别号查询机构uuid
        CommonResult<CompanyBasicInfoDTO> qyxxRes = companyApi.basicInfo(sbrw.getDjxh(), sbrw.getNsrsbh());
        if (!GyUtils.isNull(qyxxRes.getData())) {
            List<String> jguuidList = new ArrayList<>();
            jguuidList.add(qyxxRes.getData().getJguuid());
            xtxxCSVO.setJguuidList(jguuidList);
        } else {
            throw ServiceExceptionUtil.exception(500, "未查询到当前企业机构信息!");
        }
        // 组装消息模板报文参数
        final Map<String, Object> jsonmap = new HashMap<>();
        jsonmap.put("zlbsxxbt", "资料报送成功提醒");
        jsonmap.put("tbrq", DateUtils.getSystemCurrentTime(7));
        jsonmap.put("qydm", qyxxRes.getData().getQydmz());
        jsonmap.put("nsrmc", qyxxRes.getData().getNsrmc());
        jsonmap.put("sbbmc", YzpzzlEnum.getYzpzzlByDm(sbrw.getYzpzzlDm()).getMc());
        jsonmap.put("skssqq", DateUtils.toDateStrByFormatIndex(sbrw.getSkssqq(), 7));
        jsonmap.put("skssqz", DateUtils.toDateStrByFormatIndex(sbrw.getSkssqz(), 7));
        xtxxCSVO.setXxcs(JsonUtils.toJson(jsonmap));
        xtxxCSVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
        xtxxCSVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)), 0));
        xtxxCSVO.setXxyxj("9");
        xtxxCSVOList.add(xtxxCSVO);
        saveXtxxReqVO.setXtxxCsVOs(xtxxCSVOList);
        CommonResult<SaveXtxxResVO> result = xxtxAPI.saveXtxx(saveXtxxReqVO);
        log.info("资料报送成功发送消息中心保存返回报文：{}", JsonUtils.toJson(result));
    }

    @Override
    public void sendSbsbxxzx(String sbrwuuid) {
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        // 区分申报还是资料报送类
        if (RWLX_ZLBS_DM.equals(sbrw.getRwlxDm())) {
            this.getBssbParamXxzx(sbrw);
        } else {
            this.getSbsbParamXxzx(sbrw);
        }

    }

    // 组装申报失败消息中心入参
    void getSbsbParamXxzx(ZnsbNssbSbrwDO sbrw) {
        SaveXtxxReqVO saveXtxxReqVO = new SaveXtxxReqVO();
        saveXtxxReqVO.setYwxtid("LXPT_ZNSB");
        saveXtxxReqVO.setYwxtfwkl("1980D5F999384736E063990C170AE6F4");
        saveXtxxReqVO.setLrrsfid("SYSTEM");
        saveXtxxReqVO.setXxmbbh("00000000000_00000008");
        List<XtxxCSVO> xtxxCSVOList = new ArrayList<>();
        XtxxCSVO xtxxCSVO = new XtxxCSVO();
        xtxxCSVO.setYwzj(sbrw.getSbrwuuid());
        // 根据djxh和nsrsbh获取jguuid
        // 根据当前登记序号和纳税人识别号查询机构uuid
        CommonResult<CompanyBasicInfoDTO> qyxxRes = companyApi.basicInfo(sbrw.getDjxh(), sbrw.getNsrsbh());
        if (!GyUtils.isNull(qyxxRes.getData())) {
            List<String> jguuidList = new ArrayList<>();
            jguuidList.add(qyxxRes.getData().getJguuid());
            xtxxCSVO.setJguuidList(jguuidList);
        } else {
            throw ServiceExceptionUtil.exception(500, "未查询到当前企业机构信息!");
        }
        // 组装消息模板报文参数
        final Map<String, Object> jsonmap = new HashMap<>();
        jsonmap.put("sbsbxxbt", "申报失败提醒");
        jsonmap.put("tbrq", DateUtils.getSystemCurrentTime(7));
        jsonmap.put("qydm", qyxxRes.getData().getQydmz());
        jsonmap.put("nsrmc", qyxxRes.getData().getNsrmc());
        jsonmap.put("sbbmc", YzpzzlEnum.getYzpzzlByDm(sbrw.getYzpzzlDm()).getMc());
        // 判断如果是财行税需要特殊处理，将名称后按照征收项目代码对应名称拼接
        if (YzpzzlEnum.CXS.getDm().equals(sbrw.getYzpzzlDm())) {
            final String zsxmMc = Objects.requireNonNull(ZsxmDmEnum.getZsxmDm(sbrw.getZsxmDm())).getName();
            jsonmap.put("sbbmc",
                new StringBuilder().append(jsonmap.get("sbbmc")).append("(").append(zsxmMc).append(")"));
        }
        jsonmap.put("skssqq", DateUtils.toDateStrByFormatIndex(sbrw.getSkssqq(), 7));
        jsonmap.put("skssqz", DateUtils.toDateStrByFormatIndex(sbrw.getSkssqz(), 7));
        // 跳转按钮链接(跳转当前企业税费申报页面)
        jsonmap.put("qclparam", qyxxRes.getData().getJguuid());
        jsonmap.put("qclurl","/znsb/view/nssb/sbrwmx?jguuid=" + sbrw.getJguuid());
        xtxxCSVO.setXxcs(JsonUtils.toJson(jsonmap));
        xtxxCSVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
        xtxxCSVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)), 0));
        xtxxCSVO.setXxyxj("9");
        xtxxCSVOList.add(xtxxCSVO);
        saveXtxxReqVO.setXtxxCsVOs(xtxxCSVOList);
        CommonResult<SaveXtxxResVO> result = xxtxAPI.saveXtxx(saveXtxxReqVO);
        log.info("申报失败发送消息中心保存返回报文：{}", JsonUtils.toJson(result));
    }

    // 组装资料报送消息中心入参
    void getBssbParamXxzx(ZnsbNssbSbrwDO sbrw) {
        SaveXtxxReqVO saveXtxxReqVO = new SaveXtxxReqVO();
        saveXtxxReqVO.setYwxtid("LXPT_ZNSB");
        saveXtxxReqVO.setYwxtfwkl("1980D5F999384736E063990C170AE6F4");
        saveXtxxReqVO.setLrrsfid("SYSTEM");
        saveXtxxReqVO.setXxmbbh("00000000000_00000010");
        List<XtxxCSVO> xtxxCSVOList = new ArrayList<>();
        XtxxCSVO xtxxCSVO = new XtxxCSVO();
        xtxxCSVO.setYwzj(sbrw.getSbrwuuid());
        // 根据djxh和nsrsbh获取jguuid
        // 根据当前登记序号和纳税人识别号查询机构uuid
        CommonResult<CompanyBasicInfoDTO> qyxxRes = companyApi.basicInfo(sbrw.getDjxh(), sbrw.getNsrsbh());
        if (!GyUtils.isNull(qyxxRes.getData())) {
            List<String> jguuidList = new ArrayList<>();
            jguuidList.add(qyxxRes.getData().getJguuid());
            xtxxCSVO.setJguuidList(jguuidList);
        } else {
            throw ServiceExceptionUtil.exception(500, "未查询到当前企业机构信息!");
        }
        // 组装消息模板报文参数
        final Map<String, Object> jsonmap = new HashMap<>();
        jsonmap.put("zlbsxxbt", "资料报送失败提醒");
        jsonmap.put("tbrq", DateUtils.getSystemCurrentTime(7));
        jsonmap.put("qydm", qyxxRes.getData().getQydmz());
        jsonmap.put("nsrmc", qyxxRes.getData().getNsrmc());
        jsonmap.put("sbbmc", YzpzzlEnum.getYzpzzlByDm(sbrw.getYzpzzlDm()).getMc());
        jsonmap.put("skssqq", DateUtils.toDateStrByFormatIndex(sbrw.getSkssqq(), 7));
        jsonmap.put("skssqz", DateUtils.toDateStrByFormatIndex(sbrw.getSkssqz(), 7));
        // 跳转按钮链接(跳转当前企业税费申报页面)
        jsonmap.put("qclurl", qyxxRes.getData().getJguuid());
        xtxxCSVO.setXxcs(JsonUtils.toJson(jsonmap));
        xtxxCSVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
        xtxxCSVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)), 0));
        xtxxCSVO.setXxyxj("9");
        xtxxCSVOList.add(xtxxCSVO);
        saveXtxxReqVO.setXtxxCsVOs(xtxxCSVOList);
        CommonResult<SaveXtxxResVO> result = xxtxAPI.saveXtxx(saveXtxxReqVO);
        log.info("资料报送失败发送消息中心保存返回报文：{}", JsonUtils.toJson(result));
    }

    /**
     * 申报失败更新
     * 
     * @param sbrwuuid
     * @param sbjgcxRespDTO
     */
    @Override
    public void updateSbsb(String sbrwuuid, SbjgcxRespDTO sbjgcxRespDTO) {
        // 状态改变，更新申报任务表
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        final String sfczzzssbbdycwcl = sbjgcxRespDTO.getSfczzzssbbdycwcl();
        sbrw.setPclsh(sbjgcxRespDTO.getPclsh());
        sbrw.setNsrsbztDm(sbjgcxRespDTO.getSbztDm());
        sbrw.setSbyysm(sbjgcxRespDTO.getSbyysm());
        if (YesOrNoEnum.Y.getCode().equals(sfczzzssbbdycwcl)) {
            final SBSbbdBdjgVO bdjgVO = sbjgcxRespDTO.getBdjgVO();
            bdjgVO.setSbrwuuid(sbrwuuid);
            final List<SBSbbdBdjgmxVO> bdjgmxList = sbjgcxRespDTO.getBdjgmxList();
            bdjgService.deleteBdjg(sbrwuuid);
            bdjgService.saveBdjg(bdjgVO);
            bdjgmxService.saveBdjgmx(bdjgmxList);
            // 异常比对失败
            sbrw.setPclsh(sbjgcxRespDTO.getPclsh());
            sbrw.setPzxh(sbjgcxRespDTO.getPzxh());
            sbrw.setSbrq1(new Date());
            sbrw.setSbuuid(sbjgcxRespDTO.getSbuuid());
            if (GyUtils.isNotNull(sbjgcxRespDTO.getYbtse())) {
                sbrw.setYbtse(new BigDecimal(sbjgcxRespDTO.getYbtse()));
            }
            sbrw.setSfczzzssbbdycwcl(sfczzzssbbdycwcl);
        }
        this.getBaseMapper().updateById(sbrw);
        // 发送消息提醒
        this.sendSbsbxxzx(sbrwuuid);
    }

    @Override
    public ZnsbNssbSbrwDTO getSbrwBySbrwuuid(String sbrwuuid) {
        return BeanUtils.toBean(this.getBaseMapper().querySbrwBySbrwuuid(sbrwuuid), ZnsbNssbSbrwDTO.class);
    }

    @Override
    public ZnsbNssbSbrwDO querySbrwBySbrwuuid(String sbrwuuid) {
        return this.getBaseMapper().querySbrwBySbrwuuid(sbrwuuid);
    }

    @Override
    public void saveSbrw(List<ZnsbNssbSbrwDTO> sbrwList) {
        final List<ZnsbNssbSbrwDO> sbrwDOS = BeanUtils.toBean(sbrwList, ZnsbNssbSbrwDO.class);
        sbrwDOS.forEach(t -> {
            t.setYwqdDm("lq-qyd");
            t.setLrrq(LocalDateTimeUtil.of(new Date()));
            t.setXgrq(LocalDateTimeUtil.of(new Date()));
        });
        boolean b = this.getBaseMapper().insertBatch(sbrwDOS);
        log.debug("结果：" + b);

    }

    /**
     * 归集前删除申报任务
     * 
     * @param nsrsbh 纳税人识别号
     * @param sbny 申报年月
     */
    @Override
    public void gjqScSbrw(String nsrsbh, String sbny) {
        this.getBaseMapper().deleteSbrw(nsrsbh, sbny);
    }

    /**
     * 归集前删除申报任务(业务渠道代码)
     * 
     * @param nsrsbh 纳税人识别号
     * @param sbny 申报年月
     * @param ywqddm 业务渠道代码
     */
    @Override
    public void gjqScSbrwByYwqddm(String nsrsbh, String sbny, String ywqddm) {
        this.getBaseMapper().deleteSbrwByYwqddm(nsrsbh, sbny, ywqddm);
    }

    @Override
    public List<ZnsbNssbSbrwDTO> sbrwmxInit(SbrwmxInitReqDTO sbrwmxInitReqDTO) {
        // 组装返回数据，包括正常列表根据djxh和nsrsbh查询，以及增值税预缴只根据nsrsbh查询
        List<ZnsbNssbSbrwDTO> resultList = new ArrayList<>();
        List<ZnsbNssbSbrwDTO> zcList = BeanUtils.toBean(this.getBaseMapper().sbrwmxInit(sbrwmxInitReqDTO.getNsrsbh(),
            sbrwmxInitReqDTO.getSbny().replaceAll("-", ""), sbrwmxInitReqDTO.getHlbz(), sbrwmxInitReqDTO.getRwztDm(),
            sbrwmxInitReqDTO.getDjxh(), sbrwmxInitReqDTO.getBsy()), ZnsbNssbSbrwDTO.class);
        if (!GyUtils.isNull(zcList)) {
            resultList.addAll(zcList);
        }
        //增值税预缴
        List<ZnsbNssbSbrwDTO> zzsyjList = new ArrayList<>();
        //房产税
        List<ZnsbNssbSbrwDTO> fcsList = new ArrayList<>();
        //根据企业类型值判断查询条件是否带djxh
        if ("1".equals(sbrwmxInitReqDTO.getQylxz())){
            zzsyjList = BeanUtils.toBean(
                    this.getBaseMapper().sbrwmxInitZzsyj(sbrwmxInitReqDTO.getNsrsbh(), sbrwmxInitReqDTO.getSbny().replaceAll("-", ""),
                            sbrwmxInitReqDTO.getHlbz(), sbrwmxInitReqDTO.getRwztDm(), sbrwmxInitReqDTO.getBsy()),
                    ZnsbNssbSbrwDTO.class);
            fcsList = BeanUtils.toBean(
                    this.getBaseMapper().sbrwmxInitFcs(sbrwmxInitReqDTO.getNsrsbh(), sbrwmxInitReqDTO.getSbny().replaceAll("-", ""),
                            sbrwmxInitReqDTO.getHlbz(), sbrwmxInitReqDTO.getRwztDm(), sbrwmxInitReqDTO.getBsy()),
                    ZnsbNssbSbrwDTO.class);
        }else {
            zzsyjList = BeanUtils.toBean(
                    this.getBaseMapper().sbrwmxInitZzsyjKqu(sbrwmxInitReqDTO.getNsrsbh(), sbrwmxInitReqDTO.getSbny().replaceAll("-", ""),
                            sbrwmxInitReqDTO.getDjxh(), sbrwmxInitReqDTO.getHlbz(), sbrwmxInitReqDTO.getRwztDm(), sbrwmxInitReqDTO.getBsy()),
                    ZnsbNssbSbrwDTO.class);
            fcsList = BeanUtils.toBean(
                    this.getBaseMapper().sbrwmxInitFcsKqu(sbrwmxInitReqDTO.getNsrsbh(), sbrwmxInitReqDTO.getSbny().replaceAll("-", ""),
                            sbrwmxInitReqDTO.getDjxh(), sbrwmxInitReqDTO.getHlbz(), sbrwmxInitReqDTO.getRwztDm(), sbrwmxInitReqDTO.getBsy()),
                    ZnsbNssbSbrwDTO.class);
        }

        if (!GyUtils.isNull(zzsyjList)) {
            resultList.addAll(zzsyjList);
        }
        if (!GyUtils.isNull(fcsList)){
            resultList.addAll(fcsList);
        }
        return resultList;
    }

    @Override
    public List<ZnsbNssbSbrwDTO> lbljrwInit(SbrwmxInitReqDTO sbrwmxInitReqDTO) {
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        final Map<String, Object> paramsMap = new HashMap<>();
        List<String> djxhList = sbrwmxInitReqDTO.getDjxhList();
        if (GyUtils.isNull(djxhList)){
            djxhList = ZnsbSessionUtils.getKczDjxhList();
        }
        paramsMap.put("djxhList", djxhList);
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(sbrwmxInitReqDTO.getNsrsbh())?"":sbrwmxInitReqDTO.getNsrsbh());
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(sbrwmxInitReqDTO.getBsy())?"":sbrwmxInitReqDTO.getBsy());
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        paramsMap.put("sbny",sbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        //计算申报期限为当前时间三天内到期
        Date nowDate = DateUtils.toDate(DateUtils.getSystemCurrentTime(3),"yyyy-MM-dd");
        Calendar calendar = DateUtils.getSystemCurrentTime();
        calendar.add(Calendar.DAY_OF_MONTH, 3);
        Date maxDate = DateUtils.toDate(DateUtils.toLocalDateTime(calendar));
        paramsMap.put("nowdate",nowDate);
        paramsMap.put("maxdate",maxDate);
        //区分漏报任务还是漏缴任务
        if ("1".equals(sbrwmxInitReqDTO.getTabValue())){
            //漏报任务
            queryReq.setPath("/sb/sbrw/querylbrw");
        }else{
            //漏缴任务
            queryReq.setPath("/sb/sbrw/queryljrw");
        }
        queryReq.setParams(paramsMap);
        final List<Map<String, Object>> sbrwmxResult = queryApi.queryList(queryReq);
        List<ZnsbNssbSbrwDTO> sbrwList = BeanUtils.toBean(sbrwmxResult,ZnsbNssbSbrwDTO.class);
        return sbrwList;
    }

    @Override
    public void sbrwmxUpdateHlzt(SbrwmxUpdateHlztReqDTO sbrwmxUpdateHlztReqDTO) {
        this.getBaseMapper()
            .update(new UpdateWrapper<ZnsbNssbSbrwDO>().lambda()
                .set(ZnsbNssbSbrwDO::getHlbz, sbrwmxUpdateHlztReqDTO.getHlbz())
                .eq(ZnsbNssbSbrwDO::getSbrwuuid, sbrwmxUpdateHlztReqDTO.getSbrwuuid()));
    }

    @Override
    public void cxsgjqScSbrw(String sbny, String ywqddm) {
        this.getBaseMapper().deleteCxsSbrw(sbny, ywqddm, ZsxmDmEnum.getCxsZsxmDmList());
    }

    @Override
    public List<ZnsbNssbSbrwDTO> querySbrwBySbny(String sbny) {
        return BeanUtils.toBean(this.getBaseMapper().querySbrwBySbny(sbny), ZnsbNssbSbrwDTO.class);
    }

    @Override
    public List<ZnsbNssbSbrwDTO> querySbrwAllBySbny(String sbny) {
        return BeanUtils.toBean(this.getBaseMapper().querySbrwAllBySbny(sbny), ZnsbNssbSbrwDTO.class);
    }

    /**
     * 查询税费申报列表
     * 
     * @param reqVO 请求VO
     * @return 税费申报 - 申报任务列表
     */
    @Override
    public List<SfsbCxResultGridlbVO> querySfsbList(@Valid SfsbCxReqVO reqVO) {
        final String sbny = DateUtil.format(new Date(), SIMPLE_MONTH_PATTERN);
        final List<ZnsbNssbSbrwDO> sbrwList =
            this.getBaseMapper().querySbrwByNsrsbhAndSbny(reqVO.getNsrsbh(), sbny, reqVO.getNsrsbztDm());
        final List<SfsbCxResultGridlbVO> resultList = new ArrayList<>();
        for (ZnsbNssbSbrwDO sbrwDO : sbrwList) {
            final SfsbCxResultGridlbVO gridlbVO = BeanUtils.toBean(sbrwDO, SfsbCxResultGridlbVO.class);
            final Map<String, Object> tzljdzb = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", gridlbVO.getYzpzzlDm());
            if (GyUtils.isNull(tzljdzb)) {
                continue;
            }
            gridlbVO.setSkssqq(DateUtil.format(sbrwDO.getSkssqq(), NORM_DATE_FORMAT));
            gridlbVO.setSkssqz(DateUtil.format(sbrwDO.getSkssqz(), NORM_DATE_FORMAT));
            gridlbVO.setSbbmc(StrUtil.toString(tzljdzb.get("sbbmc")));
            gridlbVO.setTzlj(StrUtil.toString(tzljdzb.get("tzlj")));
            gridlbVO.setQsbbz("N");
            final String sbztDm = sbrwDO.getNsrsbztDm();
            // 未申报、已暂存、申报失败、作废成功状态时判断审批状态
            if (SBZT_WSB_DM.equals(sbztDm) || SBZT_YZC_DM.equals(sbztDm) || SBZT_SBSB_DM.equals(sbztDm)
                || SBZT_ZF_ZFCG_DM.equals(sbztDm) || SBZT_ZLBS_WBS_DM.equals(sbztDm) || SBZT_ZLBS_BSZ_DM.equals(sbztDm)
                || SBZT_ZLBS_BSSB_DM.equals(sbztDm) || SBZT_ZLBS_SJSH_BSSB_DM.equals(sbztDm)
                || SBZT_SHBTG_DM.equals(sbztDm)) {
                gridlbVO.setQsbbz("Y");
            }
            resultList.add(gridlbVO);
        }
        return resultList;
    }

    /**
     * 申报概览统计
     * 
     * @return
     */
    public SbrwsyRespVO sbgltj() {
        final SbrwsyRespVO respVO = new SbrwsyRespVO();
        final String sbny = DateUtil.format(new Date(), SIMPLE_MONTH_PATTERN);
        final List<ZnsbNssbSbrwDTO> sbrwList = this.getBaseMapper().querySbrw(sbny);

        return respVO;
    }

    /**
     * 获取管理企业信息
     * 
     * @return
     */
    @Override
    public SbrwsyGlqyxxVO getGlqyxx() {
        final SbrwsyGlqyxxVO glqyxx = new SbrwsyGlqyxxVO();
        final CommonResult<List<CompanyInfoResDTO>> result = companyApi.getAllCompanyInfo();
        if (result.isSuccess()) {
            final List<CompanyInfoResDTO> companyList = result.getData();
            glqyxx.setGlqyhs(StrUtil.toString(companyList.size()));
            int ybnsrhs = 0;
            int xgmnsrhs = 0;
            for (CompanyInfoResDTO companyInfo : companyList) {
                final String nsrsbh = companyInfo.getNsrsbh();
                if (this.isYbnsr(nsrsbh)) {
                    ybnsrhs += 1;
                } else {
                    xgmnsrhs += 1;
                }
            }
            glqyxx.setXgmnsrhs(StrUtil.toString(xgmnsrhs));
            glqyxx.setYbnsrhs(StrUtil.toString(ybnsrhs));
            glqyxx.setZqjsts(NsqxUtils.jsjlzqjsts());
        }
        return glqyxx;
    }

    @Override
    public SbrwsySbjdxxVO getSbjd() {
        final CommonResult<List<CompanyInfoResDTO>> result = companyApi.getAllCompanyInfo();
        final SbrwsySbjdxxVO sbjdxxVO = new SbrwsySbjdxxVO();
        if (result.isSuccess()) {
            final List<String> nsrsbhList =
                result.getData().stream().map(CompanyInfoResDTO::getNsrsbh).collect(Collectors.toList());
            final String sbny = DateUtil.format(new Date(), SIMPLE_MONTH_PATTERN);
            final List<ZnsbNssbSbrwDO> sbrwList = this.getBaseMapper().querySbrwByNsrsbhAndSbny(nsrsbhList, sbny);
            // 过滤出申报成功的数据
            if (GyUtils.isNotNull(sbrwList)) {
                final List<ZnsbNssbSbrwDO> sbcgSbrwList = sbrwList.stream()
                    .filter(t -> SBZT_SBCG_DM.equals(t.getNsrsbztDm()) || SBZT_CWGZ_GZCG_DM.equals(t.getNsrsbztDm())
                        || SBZT_CWGZ_GZZ_DM.equals(t.getNsrsbztDm()) || SBZT_CWGZ_GZSB_DM.equals(t.getNsrsbztDm())
                        || SBZT_ZF_ZFSB_DM.equals(t.getNsrsbztDm()))
                    .collect(Collectors.toList());

                final String sbcgCnt = StrUtil.toString(GyUtils.isNull(sbcgSbrwList.size()) ? 0 : sbcgSbrwList.size());
                final String zsl = StrUtil.toString(sbrwList.size());
                final String sbjd = NumberUtil.formatPercent(NumberUtil.div(sbcgCnt, zsl, 2).doubleValue(), 0);
                sbjdxxVO.setSbjd(sbjd);
                // 申报成功状态数据应补退税额求和
                String ysbje = "0.00";
                if (GyUtils.isNotNull(sbcgSbrwList)) {
                    ysbje = StrUtil.toString(NumberUtil.round(
                        sbcgSbrwList.stream().map(ZnsbNssbSbrwDO::getYbtse).reduce(BigDecimal.ZERO, BigDecimal::add),
                        2));
                }
                sbjdxxVO.setYsbje(ysbje);

                final Map<String, List<ZnsbNssbSbrwDO>> nsrSbrwMap =
                    sbrwList.stream().collect(Collectors.groupingBy(ZnsbNssbSbrwDO::getNsrsbh));

                int sbhs = 0;
                for (String nsrsbh : nsrSbrwMap.keySet()) {
                    final List<ZnsbNssbSbrwDO> nsrSbrwList = nsrSbrwMap.get(nsrsbh);
                    final long sbsl = nsrSbrwList.stream()
                        .filter(t -> SBZT_SBCG_DM.equals(t.getNsrsbztDm()) || SBZT_CWGZ_GZCG_DM.equals(t.getNsrsbztDm())
                            || SBZT_CWGZ_GZZ_DM.equals(t.getNsrsbztDm()) || SBZT_CWGZ_GZSB_DM.equals(t.getNsrsbztDm())
                            || SBZT_ZF_ZFSB_DM.equals(t.getNsrsbztDm()))
                        .count();
                    if (sbsl > 0) {
                        sbhs += 1;
                    }
                }
                sbjdxxVO.setSbhs(StrUtil.toString(sbhs));
                sbjdxxVO.setZhs(StrUtil.toString(nsrSbrwMap.size()));
                sbjdxxVO.setSbbs(sbcgCnt);
                sbjdxxVO.setZbs(zsl);
            }
        }
        return sbjdxxVO;
    }

    /**
     * 申报概览企业模式查询
     */
    @Override
    public List<SbrwsyFhqkVO> getFhqkxxNew(@Valid SbrwsyReqVO reqVO) {
        final String sbny = reqVO.getSbny().replace("-", "");
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/gy/listAllCompnyInfo");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("nsrsbh", reqVO.getNsrsbh());
        final List<String> djxhList = new ArrayList<>(reqVO.getDjxhList());
        List<Map<String,Object>> kqjgList = reqVO.getKqjgList();
        if (!GyUtils.isNull(kqjgList)){
            for (Map<String,Object> kqjg:kqjgList){
                djxhList.add(String.valueOf(kqjg.get("djxh")));
            }
        }
        paramsMap.put("djxhList", djxhList);
        queryReq.setParams(paramsMap);
        final List<Map<String, Object>> companyResult = queryApi.queryList(queryReq);
        List<SbrwsyFhqkVO> fhqkList = new ArrayList<>();
        if (GyUtils.isNotNull(companyResult)) {
            fhqkList = companyResult.stream().map(t -> {
                final SbrwsyFhqkVO fhqkVO = new SbrwsyFhqkVO();
                fhqkVO.setNsrsbh(GyUtils.isNull(MapUtil.getStr(t, "nsrsbh")) ? "" : MapUtil.getStr(t, "nsrsbh"));
                fhqkVO.setNsrmc(MapUtil.getStr(t, "nsrmc"));
                fhqkVO.setXzqhszDm(MapUtil.getStr(t, "xzqhszDm"));
                fhqkVO.setSfmc(SfEnum.getSfmcByXzqhszDm(MapUtil.getStr(t, "xzqhszDm")));
                fhqkVO.setZzsnsrlxDm(MapUtil.getStr(t, "zzsnsrlxDm"));
                fhqkVO.setJguuid1(MapUtil.getStr(t, "jguuid1"));
                fhqkVO.setDjxh(MapUtil.getStr(t, "djxh"));
                fhqkVO.setSbny(sbny);
                fhqkVO.setRwzs("0");
                fhqkVO.setSbsl("0");
                fhqkVO.setJksl("0");
                if (GyUtils.isNull(MapUtil.getStr(t, "qydmz"))) {
                    fhqkVO.setQydmz(null);
                } else {
                    fhqkVO.setQydmz(MapUtil.getStr(t, "qydmz"));
                }
                // 调用门户接口组装办税员名称展示
                try {
                    DjxhReqVO djxhReqVO = new DjxhReqVO();
                    djxhReqVO.setDjxh(fhqkVO.getDjxh());
                    CommonResult<List<RyxxVO>> res = companyApi.getBsyByQy(djxhReqVO);
                    if (!GyUtils.isNull(res.getData())) {
                        fhqkVO.setBsrxm(res.getData().get(0).getZsxm1());
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
                return fhqkVO;
            }).collect(Collectors.toList());

            final SimpleQueryReq sbrwtjReq = new SimpleQueryReq();
            sbrwtjReq.setPath("/sb/sbgl/querySbglQyms");
            paramsMap.put("sbny",sbny);
            final String yzpzzlDm = !GyUtils.isNull(reqVO.getYzpzzlDm())?reqVO.getYzpzzlDm().split("\\|")[0]:"";
            final String zsxmDm = !GyUtils.isNull(reqVO.getYzpzzlDm())?reqVO.getYzpzzlDm().split("\\|")[1]:"";
            if (!GyUtils.isNull(yzpzzlDm)){
                paramsMap.put("yzpzzlDm",yzpzzlDm);
            }
            if (!GyUtils.isNull(zsxmDm)){
                paramsMap.put("zsxmDm",zsxmDm);
            }
            sbrwtjReq.setParams(paramsMap);
            final List<Map<String, Object>> sbrwtjResult = queryApi.queryList(sbrwtjReq);
            if (GyUtils.isNotNull(sbrwtjResult)) {
                for (Map<String, Object> map : sbrwtjResult) {
                    final String djxh = MapUtil.getStr(map, "djxh");
                    for (SbrwsyFhqkVO fhqkVO : fhqkList) {
                        if (!djxh.equals(fhqkVO.getDjxh())) {
                            continue;
                        }
                        final Integer rwzs = MapUtil.getInt(map, "rwzs");
                        final Integer sbsl = MapUtil.getInt(map, "sbsl");
                        final Integer jksl = MapUtil.getInt(map, "jksl");
                        fhqkVO.setRwzs(MapUtil.getStr(map, "rwzs"));
                        fhqkVO.setSbsl(MapUtil.getStr(map, "sbsl"));
                        fhqkVO.setJksl(MapUtil.getStr(map, "jksl"));
                        if (sbsl == 0) {
                            fhqkVO.setSbztDm("00");
                        } else if (sbsl < rwzs) {
                            fhqkVO.setSbztDm("01");
                        } else {
                            fhqkVO.setSbztDm("02");
                        }
                        if (jksl == 0) {
                            fhqkVO.setJkztDm("00");
                        } else if (jksl < rwzs) {
                            fhqkVO.setJkztDm("01");
                        } else {
                            fhqkVO.setJkztDm("02");
                        }
                    }
                }
            }

        }
        //根据跨区税源户合并
        final List<SbrwsyFhqkVO> kqList = new ArrayList<>();
        if (!GyUtils.isNull(reqVO.getKqjgList())){
            for (SbrwsyFhqkVO fhqkVO : fhqkList) {
                String kqjgbz = "N";
                String kqjghbbz = "N";
                int rwzs = Integer.parseInt(fhqkVO.getRwzs());
                int sbsl = Integer.parseInt(fhqkVO.getSbsl());
                int jksl = Integer.parseInt(fhqkVO.getJksl());
                for (Map<String,Object> kqjg:reqVO.getKqjgList()){
                    if (fhqkVO.getNsrsbh().equals(String.valueOf(kqjg.get("nsrsbh")))) {
                        kqjgbz = "Y";
                        if (fhqkVO.getDjxh().equals(String.valueOf(kqjg.get("djxh")))){
                            kqjghbbz = "Y";
                        }
                    }
                }
                if ("Y".equals(kqjghbbz)){
                    continue;
                }
                if ("N".equals(kqjgbz)){
                    kqList.add(fhqkVO);
                    continue;
                }
                //跨区税源户合并行找子行合并计算后加入列表中
                for (SbrwsyFhqkVO chilerenVO : fhqkList){
                    if (fhqkVO.getNsrsbh().equals(chilerenVO.getNsrsbh())
                            && !fhqkVO.getDjxh().equals(chilerenVO.getDjxh())){
                        rwzs += Integer.parseInt(chilerenVO.getRwzs());
                        sbsl += Integer.parseInt(chilerenVO.getSbsl());
                        jksl += Integer.parseInt(chilerenVO.getJksl());
                    }
                }
                fhqkVO.setRwzs(String.valueOf(rwzs));
                fhqkVO.setSbsl(String.valueOf(sbsl));
                fhqkVO.setJksl(String.valueOf(jksl));
                if (sbsl == 0) {
                    fhqkVO.setSbztDm("00");
                } else if (sbsl < rwzs) {
                    fhqkVO.setSbztDm("01");
                } else {
                    fhqkVO.setSbztDm("02");
                }
                if (jksl == 0) {
                    fhqkVO.setJkztDm("00");
                } else if (jksl < rwzs) {
                    fhqkVO.setJkztDm("01");
                } else {
                    fhqkVO.setJkztDm("02");
                }
                kqList.add(fhqkVO);
            }
        }else{
            kqList.addAll(fhqkList);
        }


        final List<String> lsztDmList = reqVO.getLszt();
        final List<String> nsrsbztDmList = reqVO.getSbzt();
        final List<String> sbjkztDmList = reqVO.getJkzt();

        final List<SbrwsyFhqkVO> fhtjList = kqList.stream()
                .filter(t -> (GyUtils.isNull(lsztDmList) || lsztDmList.contains(t.getLsztDm()))
                        && (GyUtils.isNull(nsrsbztDmList) || nsrsbztDmList.contains(t.getSbztDm()))
                        && (GyUtils.isNull(sbjkztDmList) || sbjkztDmList.contains(t.getJkztDm()))
                        && (GyUtils.isNull(reqVO.getQymc()) || (GyUtils.isNull(t.getNsrmc())?"":t.getNsrmc()).contains(reqVO.getQymc()) || (GyUtils.isNull(t.getQydmz())?"":t.getQydmz()).contains(reqVO.getQymc()) || (GyUtils.isNull(t.getNsrsbh())?"":t.getNsrsbh()).contains(reqVO.getQymc()))
                        && (GyUtils.isNull(reqVO.getXzqhszDm()) || reqVO.getXzqhszDm().equals(t.getXzqhszDm()))
                        && (GyUtils.isNull(reqVO.getQyzg()) || reqVO.getQyzg().equals(t.getZzsnsrlxDm()))
                        && (GyUtils.isNull(reqVO.getBsr()) || (GyUtils.isNull(t.getBsrxm())?"":t.getBsrxm()).contains(reqVO.getBsr()))
                )
                .collect(Collectors.toList());

        final List<SbrwsyFhqkVO> sortedList = fhtjList.stream()
                .sorted(Comparator.comparing(SbrwsyFhqkVO::getSbztDm,Comparator.nullsLast(String::compareTo))
                                .thenComparing(SbrwsyFhqkVO::getQydmz, Comparator.nullsLast(String::compareTo))
                                .thenComparing(SbrwsyFhqkVO::getNsrmc, Comparator.nullsLast(String::compareTo))
                        )
                .collect(Collectors.toList());
        //判断是否仅显示需申报企业
        if (reqVO.isCheckedWxsb()){
            final List<SbrwsyFhqkVO> resultList = sortedList.stream()
                    .filter(t -> (!"0".equals(t.getRwzs())))
                    .collect(Collectors.toList());
            return resultList;
        }
        return sortedList;
    }

    /**
     * 获取分户情况信息
     */
    @Override
    public List<SbrwsyFhqkVO> getFhqkxx(@Valid SbrwsyReqVO reqVO) {
        final String sbny = DateUtil.format(new Date(), SIMPLE_MONTH_PATTERN);
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/gy/listAllCompnyInfo");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("nsrsbh", reqVO.getNsrsbh());
        final List<String> djxhList = new ArrayList<>(reqVO.getDjxhList());
        List<Map<String,Object>> kqjgList = reqVO.getKqjgList();
        if (!GyUtils.isNull(kqjgList)){
            for (Map<String,Object> kqjg:kqjgList){
                djxhList.add(String.valueOf(kqjg.get("djxh")));
            }
        }
        paramsMap.put("djxhList", djxhList);
        queryReq.setParams(paramsMap);
        final List<Map<String, Object>> companyResult = queryApi.queryList(queryReq);
        List<SbrwsyFhqkVO> fhqkList = new ArrayList<>();
        if (GyUtils.isNotNull(companyResult)) {
            fhqkList = companyResult.stream().map(t -> {
                final SbrwsyFhqkVO fhqkVO = new SbrwsyFhqkVO();
                fhqkVO.setNsrsbh(GyUtils.isNull(MapUtil.getStr(t, "nsrsbh")) ? "" : MapUtil.getStr(t, "nsrsbh"));
                fhqkVO.setNsrmc(MapUtil.getStr(t, "nsrmc"));
                fhqkVO.setXzqhszDm(MapUtil.getStr(t, "xzqhszDm"));
                fhqkVO.setSfmc(SfEnum.getSfmcByXzqhszDm(MapUtil.getStr(t, "xzqhszDm")));
                fhqkVO.setZzsnsrlxDm(MapUtil.getStr(t, "zzsnsrlxDm"));
                fhqkVO.setJguuid1(MapUtil.getStr(t, "jguuid1"));
                fhqkVO.setDjxh(MapUtil.getStr(t, "djxh"));
                fhqkVO.setSbny(sbny);
                fhqkVO.setRwzs("0");
                fhqkVO.setSbsl("0");
                fhqkVO.setJksl("0");
                if (GyUtils.isNull(MapUtil.getStr(t, "qydmz"))) {
                    fhqkVO.setQydmz(null);
                } else {
                    fhqkVO.setQydmz(MapUtil.getStr(t, "qydmz"));
                }
                // 调用门户接口组装办税员名称展示
                try {
                    DjxhReqVO djxhReqVO = new DjxhReqVO();
                    djxhReqVO.setDjxh(fhqkVO.getDjxh());
                    CommonResult<List<RyxxVO>> res = companyApi.getBsyByQy(djxhReqVO);
                    if (!GyUtils.isNull(res.getData())) {
                        fhqkVO.setBsrxm(res.getData().get(0).getZsxm1());
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
                return fhqkVO;
            }).collect(Collectors.toList());

            final SimpleQueryReq sbrwtjReq = new SimpleQueryReq();
            sbrwtjReq.setPath("/sb/sbgl/fhqktj");
            sbrwtjReq.setParams(paramsMap);
            final List<Map<String, Object>> sbrwtjResult = queryApi.queryList(sbrwtjReq);
            if (GyUtils.isNotNull(sbrwtjResult)) {
                for (Map<String, Object> map : sbrwtjResult) {
                    final String djxh = MapUtil.getStr(map, "djxh");
                    for (SbrwsyFhqkVO fhqkVO : fhqkList) {
                        if (!djxh.equals(fhqkVO.getDjxh())) {
                            continue;
                        }
                        final Integer rwzs = MapUtil.getInt(map, "rwzs");
                        final Integer sbsl = MapUtil.getInt(map, "sbsl");
                        final Integer jksl = MapUtil.getInt(map, "jksl");
                        fhqkVO.setRwzs(MapUtil.getStr(map, "rwzs"));
                        fhqkVO.setSbsl(MapUtil.getStr(map, "sbsl"));
                        fhqkVO.setJksl(MapUtil.getStr(map, "jksl"));
                        if (sbsl == 0) {
                            fhqkVO.setSbztDm("00");
                        } else if (sbsl < rwzs) {
                            fhqkVO.setSbztDm("01");
                        } else {
                            fhqkVO.setSbztDm("02");
                        }
                        if (jksl == 0) {
                            fhqkVO.setJkztDm("00");
                        } else if (jksl < rwzs) {
                            fhqkVO.setJkztDm("01");
                        } else {
                            fhqkVO.setJkztDm("02");
                        }
                    }
                }
            }

        }
        //根据跨区税源户合并
        final List<SbrwsyFhqkVO> kqList = new ArrayList<>();
        if (!GyUtils.isNull(reqVO.getKqjgList())){
            for (SbrwsyFhqkVO fhqkVO : fhqkList) {
                String kqjgbz = "N";
                String kqjghbbz = "N";
                int rwzs = Integer.parseInt(fhqkVO.getRwzs());
                int sbsl = Integer.parseInt(fhqkVO.getSbsl());
                int jksl = Integer.parseInt(fhqkVO.getJksl());
                for (Map<String,Object> kqjg:reqVO.getKqjgList()){
                    if (fhqkVO.getNsrsbh().equals(String.valueOf(kqjg.get("nsrsbh")))) {
                        kqjgbz = "Y";
                        if (fhqkVO.getDjxh().equals(String.valueOf(kqjg.get("djxh")))){
                            kqjghbbz = "Y";
                        }
                    }
                }
                if ("Y".equals(kqjghbbz)){
                    continue;
                }
                if ("N".equals(kqjgbz)){
                    kqList.add(fhqkVO);
                    continue;
                }
                //跨区税源户合并行找子行合并计算后加入列表中
                for (SbrwsyFhqkVO chilerenVO : fhqkList){
                    if (fhqkVO.getNsrsbh().equals(chilerenVO.getNsrsbh())
                            && !fhqkVO.getDjxh().equals(chilerenVO.getDjxh())){
                        rwzs += Integer.parseInt(chilerenVO.getRwzs());
                        sbsl += Integer.parseInt(chilerenVO.getSbsl());
                        jksl += Integer.parseInt(chilerenVO.getJksl());
                    }
                }
                fhqkVO.setRwzs(String.valueOf(rwzs));
                fhqkVO.setSbsl(String.valueOf(sbsl));
                fhqkVO.setJksl(String.valueOf(jksl));
                if (sbsl == 0) {
                    fhqkVO.setSbztDm("00");
                } else if (sbsl < rwzs) {
                    fhqkVO.setSbztDm("01");
                } else {
                    fhqkVO.setSbztDm("02");
                }
                if (jksl == 0) {
                    fhqkVO.setJkztDm("00");
                } else if (jksl < rwzs) {
                    fhqkVO.setJkztDm("01");
                } else {
                    fhqkVO.setJkztDm("02");
                }
                kqList.add(fhqkVO);
            }
        }else{
            kqList.addAll(fhqkList);
        }


        final List<String> lsztDmList = reqVO.getLszt();
        final List<String> nsrsbztDmList = reqVO.getSbzt();
        final List<String> sbjkztDmList = reqVO.getJkzt();

        final List<SbrwsyFhqkVO> fhtjList = kqList.stream()
            .filter(t -> (GyUtils.isNull(lsztDmList) || lsztDmList.contains(t.getLsztDm()))
                && (GyUtils.isNull(nsrsbztDmList) || nsrsbztDmList.contains(t.getSbztDm()))
                && (GyUtils.isNull(sbjkztDmList) || sbjkztDmList.contains(t.getJkztDm()))
                && (GyUtils.isNull(reqVO.getQymc()) || (GyUtils.isNull(t.getNsrmc())?"":t.getNsrmc()).contains(reqVO.getQymc()) || (GyUtils.isNull(t.getQydmz())?"":t.getQydmz()).contains(reqVO.getQymc()) || (GyUtils.isNull(t.getNsrsbh())?"":t.getNsrsbh()).contains(reqVO.getQymc()))
                && (GyUtils.isNull(reqVO.getXzqhszDm()) || reqVO.getXzqhszDm().equals(t.getXzqhszDm())))
            .collect(Collectors.toList());

        final List<SbrwsyFhqkVO> sortedList = fhtjList.stream()
            .sorted(Comparator.comparing(SbrwsyFhqkVO::getQydmz, Comparator.nullsLast(String::compareTo))
                .thenComparing(SbrwsyFhqkVO::getNsrmc))
            .collect(Collectors.toList());
        return sortedList;
    }

    @Override
    public List<SbrwsyZqtsVO> listZqts() {
        final List<SbrwsyZqtsVO> zqtsList = new ArrayList<>();
        final CommonResult<List<GszqxxVO>> zqrlResult = zqrlApi.getSfAndZqList();
        if (zqrlResult.isSuccess() && GyUtils.isNotNull(zqrlResult.getData())) {
            final List<GszqxxVO> zqrlList = zqrlResult.getData();
            final List<List<GszqxxVO>> fzZqrlList = CollectionUtil.groupByField(zqrlList, "zqsj");
            final Date today = DateUtil.parse(DateUtil.today(), NORM_DATE_FORMAT);
            for (List<GszqxxVO> gszqxxVOS : fzZqrlList) {
                final Date zqsj = DateUtil.parse(gszqxxVOS.get(0).getZqsj(), NORM_DATE_FORMAT);
                long ts = DateUtil.betweenDay(today, zqsj, true);
                if (ts < 0) {
                    ts = 0;
                }
                if (gszqxxVOS.size() > 1) {
                    final SbrwsyZqtsVO zqts = new SbrwsyZqtsVO();
                    zqts.setName("距离征期结束还有" + ts + "天");
                    zqtsList.add(zqts);
                }
                if (gszqxxVOS.size() == 1) {
                    final SbrwsyZqtsVO zqts = new SbrwsyZqtsVO();
                    zqts.setName(gszqxxVOS.get(0).getXzqhmc() + "距离征期结束还有" + ts + "天");
                    zqtsList.add(zqts);
                }
            }
        }
        return zqtsList;
    }

    private boolean isYbnsr(String nsrsbh) {
        final ZnsbMhzcQyjbxxmxReqVO nsrxxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        nsrxxReqVO.setNsrsbh(nsrsbh);
        nsrxxReqVO.setFhNsrJbxx(true);
        nsrxxReqVO.setFhNsrsfzrdxx(false);
        nsrxxReqVO.setFhNsrbqxx(false);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByNsrsbh(nsrxxReqVO);
        if (nsrxxResult.isSuccess()) {
            final List<NsrzgxxVO> zgxxList = nsrxxResult.getData().getNsrzgxx();
            final NsrzgxxVO ybnsrzglx = CollectionUtil.findOne(zgxxList,
                t -> "1".equals(t.getNsrlx()) || "3".equals(t.getNsrlx()) || "4".equals(t.getNsrlx()));
            return GyUtils.isNotNull(ybnsrzglx);
        }
        return false;
    }

    @Override
    public String queryYsbse() {
        final CommonResult<List<CompanyInfoResDTO>> result = companyApi.getAllCompanyInfo();
        if (result.isSuccess()) {
            final List<String> nsrsbhList =
                result.getData().stream().map(CompanyInfoResDTO::getNsrsbh).collect(Collectors.toList());
            final String sbny = DateUtil.format(new Date(), SIMPLE_MONTH_PATTERN);
            final List<ZnsbNssbSbrwDO> sbrwList = this.getBaseMapper().querySbrwByNsrsbhAndSbny(nsrsbhList, sbny);
            if (GyUtils.isNotNull(sbrwList)) {
                return StrUtil.toString(NumberUtil.round(sbrwList.stream()
                    .filter(t -> SBZT_SBCG_DM.equals(t.getNsrsbztDm()) || SBZT_CWGZ_GZCG_DM.equals(t.getNsrsbztDm())
                        || SBZT_CWGZ_GZZ_DM.equals(t.getNsrsbztDm()) || SBZT_CWGZ_GZSB_DM.equals(t.getNsrsbztDm())
                        || SBZT_ZF_ZFSB_DM.equals(t.getNsrsbztDm()))
                    .map(ZnsbNssbSbrwDO::getYbtse).reduce(BigDecimal.ZERO, BigDecimal::add), 2));
            }
        }
        return "0.00";
    }

    @Override
    public ZnsbNssbSbrwDO getSbrwByNsrsbh(String nsrsbh, Date skssqq, Date skssqz, String yzpzzlDm) {
        return baseMapper.selectOne(new LambdaQueryWrapperX<ZnsbNssbSbrwDO>().eq(ZnsbNssbSbrwDO::getNsrsbh, nsrsbh)
            .eq(ZnsbNssbSbrwDO::getSkssqq, skssqq).eq(ZnsbNssbSbrwDO::getSkssqz, skssqz)
            .eq(ZnsbNssbSbrwDO::getYzpzzlDm, yzpzzlDm));
    }

    @Override
    public boolean updateShxx(String sbrwuuid, String sbztDm, String shyjsm) {
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        if (GyUtils.isNotNull(sbrw)) {
            sbrw.setNsrsbztDm(sbztDm);
            sbrw.setShyjsm(shyjsm);
            sbrw.setCzsj(new Date());
            sbrw.setYhuuid1(ZnsbSessionUtils.getYhUuid());
            sbrw.setCzrmc1(ZnsbSessionUtils.getZsxm());
            return this.getBaseMapper().updateById(sbrw) == 1;
        }
        return false;
    }

    /**
     * @param sbrwuuid
     * @param sbjgcxRespDTO
     * @name 申报错误更正-更改状态为错误更正成功
     * @description 相关说明
     * @time 创建时间:2024年05月22日下午08:18:06
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public void updateCwgz(String sbrwuuid, SbjgcxRespDTO sbjgcxRespDTO) {
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        sbrw.setNsrsbztDm(SBZT_CWGZ_GZCG_DM);
        sbrw.setRwztDm(RWZT_YSB_DM);
        sbrw.setPclsh(sbjgcxRespDTO.getPclsh());
        sbrw.setPzxh(sbjgcxRespDTO.getPzxh());
        sbrw.setSbrq1(new Date());
        sbrw.setSbuuid(sbjgcxRespDTO.getSbuuid());
        sbrw.setYbtse(new BigDecimal(sbjgcxRespDTO.getYbtse()));
        sbrw.setGjbz("N");
        this.getBaseMapper().updateById(sbrw);
    }

    /**
     * @param sbrwuuid
     * @param sbjgcxRespDTO
     * @name 申报错误更正-失败
     * @description 相关说明
     * @time 创建时间:2024年05月22日下午08:17:22
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public void updateCwgzsb(String sbrwuuid, SbjgcxRespDTO sbjgcxRespDTO) {
        // 状态改变，更新申报任务表
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        final String sfczzzssbbdycwcl = sbjgcxRespDTO.getSfczzzssbbdycwcl();
        sbrw.setPclsh(sbjgcxRespDTO.getPclsh());
        sbrw.setNsrsbztDm(sbjgcxRespDTO.getSbztDm());
        sbrw.setSbyysm(sbjgcxRespDTO.getSbyysm());
        if (YesOrNoEnum.Y.getCode().equals(sfczzzssbbdycwcl)) {
            final SBSbbdBdjgVO bdjgVO = sbjgcxRespDTO.getBdjgVO();
            final List<SBSbbdBdjgmxVO> bdjgmxList = sbjgcxRespDTO.getBdjgmxList();
            bdjgService.deleteBdjg(sbrwuuid);
            bdjgService.saveBdjg(bdjgVO);
            bdjgmxService.saveBdjgmx(bdjgmxList);
            // 异常比对失败
            sbrw.setPclsh(sbjgcxRespDTO.getPclsh());
            sbrw.setPzxh(sbjgcxRespDTO.getPzxh());
            sbrw.setSbrq1(new Date());
            sbrw.setSbuuid(sbjgcxRespDTO.getSbuuid());
            sbrw.setYbtse(new BigDecimal(GyUtils.isNull(sbjgcxRespDTO.getYbtse()) ? "0" : sbjgcxRespDTO.getYbtse()));
            sbrw.setSfczzzssbbdycwcl(sfczzzssbbdycwcl);
        }
        this.getBaseMapper().updateById(sbrw);
    }

    /**
     * @param sbrwuuid
     * @param sbzfRespVO
     * @name 申报作废-更改状态为未申报
     * @description 相关说明
     * @time 创建时间:2024年05月22日下午08:18:06
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public void updateSbzf(String sbrwuuid, SbzfRespVO sbzfRespVO) {
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        sbrw.setNsrsbztDm(SBZT_ZF_ZFCG_DM);
        sbrw.setRwztDm(RWZT_WSB_DM);
        sbrw.setSbrq1(new Date());
        this.getBaseMapper().updateById(sbrw);
    }

    /**
     * @param sbuuid
     * @param sbzfRespVO
     * @name 申报作废-更改状态为未申报
     * @description 相关说明
     * @time 创建时间:2024年05月22日下午08:18:06
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public void updateSbzfBySbuuid(String sbuuid, SbzfRespVO sbzfRespVO) {
        final List<ZnsbNssbSbrwDO> sbrwList = this.getBaseMapper().querySbrwBySbuuid(sbuuid);
        if (GyUtils.isNotNull(sbrwList)) {
            sbrwList.forEach(t -> {
                t.setNsrsbztDm(SBZT_ZF_ZFCG_DM);
                t.setRwztDm(RWZT_WSB_DM);
                t.setSbrq1(new Date());
            });
        }
        this.getBaseMapper().updateBatch(sbrwList);
    }

    /**
     * @param sbrwuuid
     * @param sbjgcxRespDTO
     * @name 报作废-失败
     * @description 相关说明
     * @time 创建时间:2024年05月22日下午08:17:22
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public void updateSbzfsb(String sbrwuuid, SbjgcxRespDTO sbjgcxRespDTO) {
        // 状态改变，更新申报任务表
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        sbrw.setNsrsbztDm(sbjgcxRespDTO.getSbztDm());
        sbrw.setSbyysm(sbjgcxRespDTO.getSbyysm());
        this.getBaseMapper().updateById(sbrw);
    }

    public ZnsbNssbSbrwDO getSbrwBySbuuid(String sbuuid) {
        return this.getBaseMapper()
            .selectOne(new LambdaQueryWrapperX<ZnsbNssbSbrwDO>().eq(ZnsbNssbSbrwDO::getSbuuid, sbuuid));
    }

    @Override
    public void updateJkzt(List<ZnsbNssbJksjgjVO> yjsfList) {
        if (GyUtils.isNull(yjsfList)) {
            return;
        }
        // 根据应征凭证序号分组
        final Map<String, List<ZnsbNssbJksjgjVO>> fzYjsfMap =
            yjsfList.stream().collect(Collectors.groupingBy(ZnsbNssbJksjgjVO::getYzpzxh));
        for (String yzpzxh : fzYjsfMap.keySet()) {
            final List<ZnsbNssbJksjgjVO> pzxhYjsfList = fzYjsfMap.get(yzpzxh);
            final int yjsfZsl = pzxhYjsfList.size();
            final ZnsbNssbSbrwDO sbrwDO = this.getBaseMapper().querySbrwByPzxh(yzpzxh);
            if (GyUtils.isNull(sbrwDO)) {
                continue;
            }
            final int jksl =
                (int)pzxhYjsfList.stream().filter(t -> ("1".equals(t.getTzlxDm()) || "4".equals(t.getTzlxDm()))
                    && !("0".equals(t.getSkcllxDm()) || "5".equals(t.getSkcllxDm()))).count();
            if (0 == jksl) {
                // 未缴款
                sbrwDO.setSbjkztDm("01");
            } else if (jksl <= yjsfZsl) {
                // 部分缴款
                sbrwDO.setSbjkztDm("02");
            } else {
                // 已缴款
                sbrwDO.setSbjkztDm("03");
            }
            this.getBaseMapper().updateById(sbrwDO);
        }
    }

    @Override
    public void deleteBySbrwuuid(String sbrwuuid) {
        this.getBaseMapper().deleteById(sbrwuuid);
    }

    @Override
    public void refreshSbrwZt(String sbrwuuid, BigDecimal ybtse, String sbuuid, String nsrsbztDm, Date sbrq1,
        String pzxh, String rwztDm) {
        final ZnsbNssbSbrwDO sbrwDO = this.getBaseMapper().selectById(sbrwuuid);
        if (!GyUtils.isNull(sbrwDO)) {
            sbrwDO.setYbtse(ybtse);
            sbrwDO.setSbuuid(sbuuid);
            sbrwDO.setNsrsbztDm(nsrsbztDm);
            sbrwDO.setSbrq1(sbrq1);
            sbrwDO.setPzxh(pzxh);
            sbrwDO.setRwztDm(rwztDm);
            this.getBaseMapper().updateById(sbrwDO);
        }
    }

    @Override
    public ZnsbNssbSbrwDO getSbrwByPclsh(String pclsh) {
        return this.getBaseMapper()
            .selectOne(new LambdaQueryWrapperX<ZnsbNssbSbrwDO>().eq(ZnsbNssbSbrwDO::getPclsh, pclsh));
    }

    @Override
    public void deleteBySyuuid(String syuuid) {
        this.getBaseMapper().delete(ZnsbNssbSbrwDO::getSyuuid, syuuid);
    }

    @Override
    public List<ZnsbNssbSbrwDO> querySbrwByZsxmDm(String djxh, Date skssqq, Date skssqz, String yzpzzlDm,
        String zsxmDm) {
        return baseMapper.selectList(new LambdaQueryWrapperX<ZnsbNssbSbrwDO>().eq(ZnsbNssbSbrwDO::getDjxh, djxh)
            .eq(ZnsbNssbSbrwDO::getSkssqq, skssqq).eq(ZnsbNssbSbrwDO::getSkssqz, skssqz)
            .eq(ZnsbNssbSbrwDO::getYzpzzlDm, yzpzzlDm).eq(ZnsbNssbSbrwDO::getZsxmDm, zsxmDm));
    }

    public ZnsbNssbSbrwDO getSbrwBySyuuid(String syuuid, Date skssqq, Date skssqz) {
        return this.getBaseMapper()
            .selectOne(new LambdaQueryWrapperX<ZnsbNssbSbrwDO>().eq(ZnsbNssbSbrwDO::getSkssqq, skssqq)
                .eq(ZnsbNssbSbrwDO::getSkssqz, skssqz).eq(ZnsbNssbSbrwDO::getSyuuid, syuuid));
    }

    public ZnsbNssbSbrwDO getSbrwBySyuuidZspmDm(String syuuid, Date skssqq, Date skssqz, String zspmDm) {
        final List<ZnsbNssbSbrwDO> sbrwDOList = this.getBaseMapper().selectList(new LambdaQueryWrapperX<ZnsbNssbSbrwDO>().eq(ZnsbNssbSbrwDO::getSkssqq, skssqq)
                .eq(ZnsbNssbSbrwDO::getSkssqz, skssqz).eq(ZnsbNssbSbrwDO::getSyuuid, syuuid));
        log.info("sbrwDOList{}",sbrwDOList);
        ZnsbNssbSbrwDO sbrwDO = null;
        for (ZnsbNssbSbrwDO znsbNssbSbrwDO : sbrwDOList){
            if(GyUtils.isNotNull(zspmDm) && zspmDm.equals(znsbNssbSbrwDO.getZspmDm())){
                sbrwDO = znsbNssbSbrwDO;
            } else if (GyUtils.isNull(zspmDm) && GyUtils.isNull(znsbNssbSbrwDO.getZspmDm())){
                sbrwDO = znsbNssbSbrwDO;
            }
        }
        return sbrwDO;
    }

    @Override
    public List<SbrwAbbqktjVO> getAbbqktjListNew(SbrwsyReqVO reqVO) {
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbgl/querySbglBbms");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", reqVO.getDjxhList());
        paramsMap.put("sbny",reqVO.getSbny().replace("-", ""));
        queryReq.setParams(paramsMap);
        final List<Map<String, Object>> abbResult = queryApi.queryList(queryReq);
        List<SbrwAbbqktjVO> abbqkList = new ArrayList<>();
        if (GyUtils.isNotNull(abbResult)) {
            try {
                abbqkList = abbResult.stream().map(t -> {
                    final SbrwAbbqktjVO abbqkVO = new SbrwAbbqktjVO();
                    abbqkVO.setYzpzzlDm(MapUtil.getStr(t, "yzpzzlDm"));
                    abbqkVO.setYzpzzlMc(YzpzzlEnum.getYzpzzlByDm(MapUtil.getStr(t, "yzpzzlDm")).getMc());
                    abbqkVO.setRwzs(MapUtil.getStr(t, "rwzs"));
                    abbqkVO.setSbsl(MapUtil.getStr(t, "sbsl"));
                    abbqkVO.setJksl(MapUtil.getStr(t, "jksl"));
                    abbqkVO.setZsxmDm(MapUtil.getStr(t, "zsxmDm"));
                    this.getBbmcByZsxmDm(abbqkVO);
                    return abbqkVO;
                }).collect(Collectors.toList());

                // 根据报表匹配缓存配置的去申报跳转路径
                List<Map<String, Object>> cacheList = CacheUtils.getTableData("cs_znsb_abbsbgltzlj");
                // 先获取可操作登记序号列表，根据登记序号列表每五个分组循环调用处理数据
                final List<List<String>> groupDjxhList = this.getGroupDjxhList(ZnsbSessionUtils.getKczDjxhList());
                final List<SbrwAbbqktjVO> abbqkCollList = Collections.synchronizedList(new ArrayList<>());
                abbqkCollList.addAll(abbqkList);
                // 组装最终数据
                for (SbrwAbbqktjVO abbqktjVO : abbqkCollList) {
                    final Integer rwzs = Integer.parseInt(abbqktjVO.getRwzs());
                    final Integer sbsl = Integer.parseInt(abbqktjVO.getSbsl());
                    final Integer jksl = Integer.parseInt(abbqktjVO.getJksl());
                    if (sbsl == 0) {
                        abbqktjVO.setSbztDm("00");
                    } else if (sbsl < rwzs) {
                        abbqktjVO.setSbztDm("01");
                    } else {
                        abbqktjVO.setSbztDm("02");
                    }
                    if (jksl == 0) {
                        abbqktjVO.setJkztDm("00");
                    } else if (jksl < rwzs) {
                        abbqktjVO.setJkztDm("01");
                    } else {
                        abbqktjVO.setJkztDm("02");
                    }

                    if (!GyUtils.isNull(cacheList)) {
                        List<Map<String, Object>> resultList = cacheList.stream()
                                .filter(map -> MapUtil.getStr(map, "yzpzzlDm").equals(abbqktjVO.getYzpzzlDm())
                                        && MapUtil.getStr(map, "zsxmDm").equals(abbqktjVO.getZsxmDm()))
                                .collect(Collectors.toList());
                        if (!GyUtils.isNull(resultList)) {
                            abbqktjVO.setQsbAction(MapUtil.getStr(resultList.get(0), "sbglabburl"));
                        }else{
                            //财务报表报送只用yzpzzlDm去匹配数据筛选
                            if (CWBBBS.getDm().equals(abbqktjVO.getYzpzzlDm())){
                                List<Map<String, Object>> zlbsList = cacheList.stream()
                                        .filter(map -> MapUtil.getStr(map, "yzpzzlDm").equals(abbqktjVO.getYzpzzlDm()))
                                        .collect(Collectors.toList());
                                if (!GyUtils.isNull(zlbsList)) {
                                    abbqktjVO.setQsbAction(MapUtil.getStr(zlbsList.get(0), "sbglabburl"));
                                    abbqktjVO.setZsxmDm("00000");
                                }
                            }
                        }

                    }
                    //增值税一般人需要获取财务合规检查差异比对情况
                    if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(abbqktjVO.getYzpzzlDm())
                            && ZsxmDmEnum.ZZS.getCode().equals(abbqktjVO.getZsxmDm())) {
                        List<Map<String, Object>> zzsCybdCollList = Collections.synchronizedList(new ArrayList<>());
                        final CountDownLatch latch = new CountDownLatch(groupDjxhList.size());
                        // 调用台账接口查询财务合规检查情况
                        for (List<String> djxhList : groupDjxhList) {
                            ThreadPoolManager.SBGL_ABBINIT.execute(() -> {
                                List<Map<String, Object>> zzsCybdResVOList = new ArrayList<>();
                                try {
                                    List<Map<String, Object>> skssqList = this.getSbrwSkssqByDjxh(djxhList, abbqktjVO.getYzpzzlDm(), abbqktjVO.getZsxmDm());
                                    skssqList.forEach(map -> {
                                        ZzsCybdReqVO zzsCybdReqVO = new ZzsCybdReqVO();
                                        zzsCybdReqVO.setZsxmDm(abbqktjVO.getZsxmDm());
                                        zzsCybdReqVO.setSkssqq(MapUtil.getStr(map, "skssqq"));
                                        zzsCybdReqVO.setSkssqz(MapUtil.getStr(map, "skssqz"));
                                        zzsCybdReqVO.setDjxh((List<String>) map.get("djxhList"));
                                        log.info("申报概览调用台账差异比对接口入参：{}", JsonUtils.toJson(zzsCybdReqVO));
                                        List<ZzsCybdResVO> resesVOList = cybdService.getCybdxx(zzsCybdReqVO);
                                        Map<String, Object> zzsCybdResVOMap = new HashMap<>();
                                        zzsCybdResVOMap.put("skssqmap", map);
                                        zzsCybdResVOMap.put("resesVOList", resesVOList);
                                        zzsCybdResVOList.add(zzsCybdResVOMap);
                                        log.info("申报概览调用台账差异比对接口出参：{}", JsonUtils.toJson(resesVOList));
                                    });
                                } catch (Exception e) {
                                    log.error("", e);
                                    log.info("申报概览按报表加载时调用差异比对接口出现异常：{}", e.getMessage());
                                } finally {
                                    zzsCybdCollList.addAll(zzsCybdResVOList);
                                    latch.countDown();
                                }
                            });
                        }
                        latch.await();
                        // 根据台账返回数据设置财务合规检查通过数量
                        final int tgsl = this.getzzsCybdTgsl(zzsCybdCollList, abbqktjVO);
                        abbqktjVO.setTgsl(String.valueOf(tgsl));
                        if (tgsl == 0) {
                            abbqktjVO.setCwhgjcztDm("00");
                        } else if (tgsl < rwzs) {
                            abbqktjVO.setCwhgjcztDm("01");
                        } else {
                            abbqktjVO.setCwhgjcztDm("02");
                        }
                    } else {
                        //其他报表默认全部通过
                        abbqktjVO.setTgsl(String.valueOf(rwzs));
                        //增值税一般人调用台账差异比对接口
                        if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(abbqktjVO.getYzpzzlDm())
                                && ZsxmDmEnum.ZZS.getCode().equals(abbqktjVO.getZsxmDm())) {
                            List<Map<String, Object>> zzsCybdCollList = Collections.synchronizedList(new ArrayList<>());
                            final CountDownLatch latch = new CountDownLatch(groupDjxhList.size());
                            // 调用台账接口查询财务合规检查情况
                            for (List<String> djxhList : groupDjxhList) {
                                ThreadPoolManager.SBGL_ABBINIT.execute(() -> {
                                    List<Map<String, Object>> zzsCybdResVOList = new ArrayList<>();
                                    try {
                                        List<Map<String, Object>> skssqList = this.getSbrwSkssqByDjxh(djxhList, abbqktjVO.getYzpzzlDm(), abbqktjVO.getZsxmDm());
                                        skssqList.forEach(map -> {
                                            ZzsCybdReqVO zzsCybdReqVO = new ZzsCybdReqVO();
                                            zzsCybdReqVO.setZsxmDm(abbqktjVO.getZsxmDm());
                                            zzsCybdReqVO.setSkssqq(MapUtil.getStr(map, "skssqq"));
                                            zzsCybdReqVO.setSkssqz(MapUtil.getStr(map, "skssqz"));
                                            zzsCybdReqVO.setDjxh(djxhList);
                                            log.info("申报概览调用台账差异比对接口入参：{}", JsonUtils.toJson(zzsCybdReqVO));
                                            List<ZzsCybdResVO> resesVOList = cybdService.getCybdxx(zzsCybdReqVO);
                                            Map<String, Object> zzsCybdResVOMap = new HashMap<>();
                                            zzsCybdResVOMap.put("skssqmap", map);
                                            zzsCybdResVOMap.put("resesVOList", resesVOList);
                                            zzsCybdResVOList.add(zzsCybdResVOMap);
                                            log.info("申报概览调用台账差异比对接口出参：{}", JsonUtils.toJson(resesVOList));
                                        });
                                    } catch (Exception e) {
                                        log.error("", e);
                                        log.info("申报概览按报表加载时调用差异比对接口出现异常：{}", e.getMessage());
                                    } finally {
                                        zzsCybdCollList.addAll(zzsCybdResVOList);
                                        latch.countDown();
                                    }
                                });
                            }
                            latch.await();
                            // 根据台账返回数据设置财务合规检查通过数量
                            final int tgsl = this.getzzsCybdTgsl(zzsCybdCollList, abbqktjVO);
                            abbqktjVO.setTgsl(String.valueOf(tgsl));
                            if (tgsl == 0) {
                                abbqktjVO.setCwhgjcztDm("00");
                            } else if (tgsl < rwzs) {
                                abbqktjVO.setCwhgjcztDm("01");
                            } else {
                                abbqktjVO.setCwhgjcztDm("02");
                            }
                        } else {
                            //其他报表默认通过
                            abbqktjVO.setCwhgjcztDm("02");
                        }

                    }
                }
            } catch (Exception e) {
                log.info("初始化加载申报概览按报表列表异常：", e);
            }
        }
        // 根据条件过滤
        final List<String> nsrsbztDmList = reqVO.getSbzt();
        final List<String> sbjkztDmList = reqVO.getJkzt();
        final List<String> cwhgjcztDmList = reqVO.getCwhgjcqk();
        final String yzpzzlDm = !GyUtils.isNull(reqVO.getYzpzzlDm())?reqVO.getYzpzzlDm().split("\\|")[0]:"";

        final List<SbrwAbbqktjVO> fhtjList = abbqkList.stream()
                .filter(t -> (GyUtils.isNull(nsrsbztDmList) || nsrsbztDmList.contains(t.getSbztDm()))
                        && (GyUtils.isNull(sbjkztDmList) || sbjkztDmList.contains(t.getJkztDm()))
                        && (GyUtils.isNull(cwhgjcztDmList) || cwhgjcztDmList.contains(t.getCwhgjcztDm()))
                        && (GyUtils.isNull(yzpzzlDm) || t.getYzpzzlDm().equals(yzpzzlDm)))
                .collect(Collectors.toList());

        return fhtjList;
    }

    @Override
    public List<SbrwAbbqktjVO> getAbbqktjList(SbrwsyReqVO reqVO) {
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbgl/abbqktj");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", reqVO.getDjxhList());
        queryReq.setParams(paramsMap);
        final List<Map<String, Object>> abbResult = queryApi.queryList(queryReq);
        List<SbrwAbbqktjVO> abbqkList = new ArrayList<>();
        if (GyUtils.isNotNull(abbResult)) {
            try {
                abbqkList = abbResult.stream().map(t -> {
                    final SbrwAbbqktjVO abbqkVO = new SbrwAbbqktjVO();
                    abbqkVO.setYzpzzlDm(MapUtil.getStr(t, "yzpzzlDm"));
                    abbqkVO.setYzpzzlMc(YzpzzlEnum.getYzpzzlByDm(MapUtil.getStr(t, "yzpzzlDm")).getMc());
                    abbqkVO.setRwzs(MapUtil.getStr(t, "rwzs"));
                    abbqkVO.setSbsl(MapUtil.getStr(t, "sbsl"));
                    abbqkVO.setJksl(MapUtil.getStr(t, "jksl"));
                    abbqkVO.setZsxmDm(MapUtil.getStr(t, "zsxmDm"));
                    this.getBbmcByZsxmDm(abbqkVO);
                    return abbqkVO;
                }).collect(Collectors.toList());

                // 根据报表匹配缓存配置的去申报跳转路径
                List<Map<String, Object>> cacheList = CacheUtils.getTableData("cs_znsb_abbsbgltzlj");
                // 先获取可操作登记序号列表，根据登记序号列表每五个分组循环调用处理数据
                final List<List<String>> groupDjxhList = this.getGroupDjxhList(ZnsbSessionUtils.getKczDjxhList());
                final List<SbrwAbbqktjVO> abbqkCollList = Collections.synchronizedList(new ArrayList<>());
                abbqkCollList.addAll(abbqkList);
                // 组装最终数据
                for (SbrwAbbqktjVO abbqktjVO : abbqkCollList) {
                    final Integer rwzs = Integer.parseInt(abbqktjVO.getRwzs());
                    final Integer sbsl = Integer.parseInt(abbqktjVO.getSbsl());
                    final Integer jksl = Integer.parseInt(abbqktjVO.getJksl());
                    if (sbsl == 0) {
                        abbqktjVO.setSbztDm("00");
                    } else if (sbsl < rwzs) {
                        abbqktjVO.setSbztDm("01");
                    } else {
                        abbqktjVO.setSbztDm("02");
                    }
                    if (jksl == 0) {
                        abbqktjVO.setJkztDm("00");
                    } else if (jksl < rwzs) {
                        abbqktjVO.setJkztDm("01");
                    } else {
                        abbqktjVO.setJkztDm("02");
                    }

                    if (!GyUtils.isNull(cacheList)) {
                        List<Map<String, Object>> resultList = cacheList.stream()
                                .filter(map -> MapUtil.getStr(map, "yzpzzlDm").equals(abbqktjVO.getYzpzzlDm())
                                        && MapUtil.getStr(map, "zsxmDm").equals(abbqktjVO.getZsxmDm()))
                                .collect(Collectors.toList());
                        if (!GyUtils.isNull(resultList)) {
                            abbqktjVO.setQsbAction(MapUtil.getStr(resultList.get(0), "sbglabburl"));
                        }
                    }
                    //增值税一般人需要获取财务合规检查差异比对情况
                    if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(abbqktjVO.getYzpzzlDm())
                            && ZsxmDmEnum.ZZS.getCode().equals(abbqktjVO.getZsxmDm())) {
                        List<Map<String, Object>> zzsCybdCollList = Collections.synchronizedList(new ArrayList<>());
                        final CountDownLatch latch = new CountDownLatch(groupDjxhList.size());
                        // 调用台账接口查询财务合规检查情况
                        for (List<String> djxhList : groupDjxhList) {
                            ThreadPoolManager.SBGL_ABBINIT.execute(() -> {
                                List<Map<String, Object>> zzsCybdResVOList = new ArrayList<>();
                                try {
                                    List<Map<String, Object>> skssqList = this.getSbrwSkssqByDjxh(djxhList, abbqktjVO.getYzpzzlDm(), abbqktjVO.getZsxmDm());
                                    skssqList.forEach(map -> {
                                        ZzsCybdReqVO zzsCybdReqVO = new ZzsCybdReqVO();
                                        zzsCybdReqVO.setZsxmDm(abbqktjVO.getZsxmDm());
                                        zzsCybdReqVO.setSkssqq(MapUtil.getStr(map, "skssqq"));
                                        zzsCybdReqVO.setSkssqz(MapUtil.getStr(map, "skssqz"));
                                        zzsCybdReqVO.setDjxh((List<String>) map.get("djxhList"));
                                        log.info("申报概览调用台账差异比对接口入参：{}", JsonUtils.toJson(zzsCybdReqVO));
                                        List<ZzsCybdResVO> resesVOList = cybdService.getCybdxx(zzsCybdReqVO);
                                        Map<String, Object> zzsCybdResVOMap = new HashMap<>();
                                        zzsCybdResVOMap.put("skssqmap", map);
                                        zzsCybdResVOMap.put("resesVOList", resesVOList);
                                        zzsCybdResVOList.add(zzsCybdResVOMap);
                                        log.info("申报概览调用台账差异比对接口出参：{}", JsonUtils.toJson(resesVOList));
                                    });
                                } catch (Exception e) {
                                    log.error("", e);
                                    log.info("申报概览按报表加载时调用差异比对接口出现异常：{}", e.getMessage());
                                } finally {
                                    zzsCybdCollList.addAll(zzsCybdResVOList);
                                    latch.countDown();
                                }
                            });
                        }
                        latch.await();
                        // 根据台账返回数据设置财务合规检查通过数量
                        final int tgsl = this.getzzsCybdTgsl(zzsCybdCollList, abbqktjVO);
                        abbqktjVO.setTgsl(String.valueOf(tgsl));
                        if (tgsl == 0) {
                            abbqktjVO.setCwhgjcztDm("00");
                        } else if (tgsl < rwzs) {
                            abbqktjVO.setCwhgjcztDm("01");
                        } else {
                            abbqktjVO.setCwhgjcztDm("02");
                        }
                    } else {
                        //其他报表默认全部通过
                        abbqktjVO.setTgsl(String.valueOf(rwzs));
                        //增值税一般人调用台账差异比对接口
                        if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(abbqktjVO.getYzpzzlDm())
                                && ZsxmDmEnum.ZZS.getCode().equals(abbqktjVO.getZsxmDm())) {
                            List<Map<String, Object>> zzsCybdCollList = Collections.synchronizedList(new ArrayList<>());
                            final CountDownLatch latch = new CountDownLatch(groupDjxhList.size());
                            // 调用台账接口查询财务合规检查情况
                            for (List<String> djxhList : groupDjxhList) {
                                ThreadPoolManager.SBGL_ABBINIT.execute(() -> {
                                    List<Map<String, Object>> zzsCybdResVOList = new ArrayList<>();
                                    try {
                                        List<Map<String, Object>> skssqList = this.getSbrwSkssqByDjxh(djxhList, abbqktjVO.getYzpzzlDm(), abbqktjVO.getZsxmDm());
                                        skssqList.forEach(map -> {
                                            ZzsCybdReqVO zzsCybdReqVO = new ZzsCybdReqVO();
                                            zzsCybdReqVO.setZsxmDm(abbqktjVO.getZsxmDm());
                                            zzsCybdReqVO.setSkssqq(MapUtil.getStr(map, "skssqq"));
                                            zzsCybdReqVO.setSkssqz(MapUtil.getStr(map, "skssqz"));
                                            zzsCybdReqVO.setDjxh(djxhList);
                                            log.info("申报概览调用台账差异比对接口入参：{}", JsonUtils.toJson(zzsCybdReqVO));
                                            List<ZzsCybdResVO> resesVOList = cybdService.getCybdxx(zzsCybdReqVO);
                                            Map<String, Object> zzsCybdResVOMap = new HashMap<>();
                                            zzsCybdResVOMap.put("skssqmap", map);
                                            zzsCybdResVOMap.put("resesVOList", resesVOList);
                                            zzsCybdResVOList.add(zzsCybdResVOMap);
                                            log.info("申报概览调用台账差异比对接口出参：{}", JsonUtils.toJson(resesVOList));
                                        });
                                    } catch (Exception e) {
                                        log.error("", e);
                                        log.info("申报概览按报表加载时调用差异比对接口出现异常：{}", e.getMessage());
                                    } finally {
                                        zzsCybdCollList.addAll(zzsCybdResVOList);
                                        latch.countDown();
                                    }
                                });
                            }
                            latch.await();
                            // 根据台账返回数据设置财务合规检查通过数量
                            final int tgsl = this.getzzsCybdTgsl(zzsCybdCollList, abbqktjVO);
                            abbqktjVO.setTgsl(String.valueOf(tgsl));
                            if (tgsl == 0) {
                                abbqktjVO.setCwhgjcztDm("00");
                            } else if (tgsl < rwzs) {
                                abbqktjVO.setCwhgjcztDm("01");
                            } else {
                                abbqktjVO.setCwhgjcztDm("02");
                            }
                        } else {
                            //其他报表默认通过
                            abbqktjVO.setCwhgjcztDm("02");
                        }

                    }
                }
            } catch (Exception e) {
                log.info("初始化加载申报概览按报表列表异常：", e);
            }
        }
        // 根据条件过滤
        final List<String> nsrsbztDmList = reqVO.getSbzt();
        final List<String> sbjkztDmList = reqVO.getJkzt();
        final List<String> cwhgjcztDmList = reqVO.getCwhgjcqk();

        final List<SbrwAbbqktjVO> fhtjList = abbqkList.stream()
            .filter(t -> (GyUtils.isNull(nsrsbztDmList) || nsrsbztDmList.contains(t.getSbztDm()))
                && (GyUtils.isNull(sbjkztDmList) || sbjkztDmList.contains(t.getJkztDm()))
                && (GyUtils.isNull(cwhgjcztDmList) || cwhgjcztDmList.contains(t.getCwhgjcztDm()))
                && (GyUtils.isNull(reqVO.getBbmc()) || t.getYzpzzlMc().contains(reqVO.getBbmc())))
            .collect(Collectors.toList());

        return fhtjList;
    }

    private List<Map<String, Object>> getSbrwSkssqByDjxh(List<String> djxhList, String yzpzzlDm, String zsxmDm) {
        List<Map<String, Object>> skssqList = new ArrayList<>();
        List<Map<String, Object>> uniqueList = new ArrayList<>();
        //List<ZnsbNssbSbrwDO> sbrwList = this.getBaseMapper().selecAbbSbgl(djxhList,yzpzzlDm,zsxmDm);
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/querySbrwmxForZzsybnsr");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", djxhList);
        paramsMap.put("rwztDm", "");
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        paramsMap.put("sbny",DateUtils.getSystemCurrentTime(17));
        queryReq.setParams(paramsMap);
        final List<Map<String, Object>> sbrwmxResult = queryApi.queryList(queryReq);
        List<ZnsbNssbSbrwDO> sbrwList = BeanUtils.toBean(sbrwmxResult,ZnsbNssbSbrwDO.class);
        log.info("sbrwList:{}",JsonUtils.toJson(sbrwList));
        if (!GyUtils.isNull(sbrwList)){
            sbrwList.forEach(sbrw -> {
                Map<String, Object> map = new HashMap<>();
                map.put("skssqq", DateUtils.dateToString(sbrw.getSkssqq(), 3));
                map.put("skssqz", DateUtils.dateToString(sbrw.getSkssqz(), 3));
                skssqList.add(map);
            });
            //去重
            uniqueList = skssqList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(vo -> vo.get("skssqq") + ";" + vo.get("skssqz")))),
                            ArrayList::new));
            //组装djxhList
            uniqueList.forEach(skssqmap -> {
                List<String> djxhGroupSkssqList = new ArrayList<>();
                sbrwList.forEach(sbrw -> {
                    if (DateUtils.dateToString(sbrw.getSkssqq(), 3).equals(MapUtil.getStr(skssqmap,"skssqq"))
                    && DateUtils.dateToString(sbrw.getSkssqz(), 3).equals(MapUtil.getStr(skssqmap,"skssqz"))){
                        djxhGroupSkssqList.add(sbrw.getDjxh());
                    }
                });
                skssqmap.put("djxhList",djxhGroupSkssqList);
            });
        }
        return uniqueList;
    }

    private List<List<String>> getGroupDjxhList(List<String> djxhList) {
        List<List<String>> groupDjxhList = new ArrayList<>();
        if (!GyUtils.isNull(djxhList)) {
            // 分隔数量
            int partitionSize = 5;
            groupDjxhList = new ArrayList<>(djxhList.stream()
                .collect(Collectors.groupingBy(it -> (djxhList.indexOf(it) / partitionSize))).values());
        }
        return groupDjxhList;
    }

    private void getBbmcByZsxmDm(SbrwAbbqktjVO abbqkVO) {
        // 财行税需要按照征收项目代码组装名称
        if (YzpzzlEnum.CXS.getDm().equals(abbqkVO.getYzpzzlDm())) {
            ZsxmDmEnum zsxmDmEnum = ZsxmDmEnum.getZsxmDm(abbqkVO.getZsxmDm());
            String zsxmMc = "";
            if (!GyUtils.isNull(zsxmDmEnum)) {
                zsxmMc = zsxmDmEnum.getName();
            }
            abbqkVO.setYzpzzlMc(abbqkVO.getYzpzzlMc() + "（" + zsxmMc + "）");

        }

        // 财务报表需要重新定义名称，根据纳税期限代码将名称加后缀
        if (YzpzzlEnum.CWBB_QYKJZD.getDm().equals(abbqkVO.getYzpzzlDm())) {
            abbqkVO.setYzpzzlMc("财务报表报送与信息采集");
        }

        // 企业所得税月季报重定义名称
        if (YzpzzlEnum.QYSDSCZZS.getDm().equals(abbqkVO.getYzpzzlDm())) {
            abbqkVO.setYzpzzlMc("居民企业（查账征收）企业所得税月（季）度申报");
        }

        // 企业所得税年报重定义名称
        if (YzpzzlEnum.QYSDS_NDA.getDm().equals(abbqkVO.getYzpzzlDm())) {
            abbqkVO.setYzpzzlMc("居民企业（查账征收）企业所得税年度申报");
        }

        // 通用申报根据征收项目代码组装报表名称
        if (YzpzzlEnum.TYSBB_SJFZSF.getDm().equals(abbqkVO.getYzpzzlDm())) {
            abbqkVO.setYzpzzlMc("通用申报");
            ZsxmDmEnum zsxmDmEnum = ZsxmDmEnum.getZsxmDm(abbqkVO.getZsxmDm());
            String zsxmMc = "";
            if (!GyUtils.isNull(zsxmDmEnum)) {
                zsxmMc = zsxmDmEnum.getName();
            }
            abbqkVO.setYzpzzlMc(abbqkVO.getYzpzzlMc() + "（" + zsxmMc + "）");
        }
    }

    private int getzzsCybdTgsl(List<Map<String,Object>> zzsCybdResVOList, SbrwAbbqktjVO abbqktjVO) {
        log.info("申报概览调用台账查询比对结果组装返回数据为：{}",JsonUtils.toJson(zzsCybdResVOList));
        if (!GyUtils.isNull(zzsCybdResVOList)) {
            int tgsl = 0;
            for (Map<String,Object> resmap : zzsCybdResVOList) {
                //获取List和对应skssq
                Map<String,Object> skssqmap = (Map<String,Object>)resmap.get("skssqmap");
                final String skssqq = MapUtil.getStr(skssqmap,"skssqq");
                final String skssqz = MapUtil.getStr(skssqmap,"skssqz");
                final List<ZzsCybdResVO> resVOList = (List<ZzsCybdResVO>)resmap.get("resesVOList");
                for (ZzsCybdResVO resvo:resVOList){
                    // 校验是否存在申报任务，不存在任务的不做统计
                    List<ZnsbNssbSbrwDO> znsbNssbSbrwDOList = this.getBaseMapper().getZzsybnsrSbrwByDjxh(resvo.getDjxh(),DateUtils.strToDate(skssqq),DateUtils.strToDate(skssqz));
                    if (GyUtils.isNull(znsbNssbSbrwDOList)) {
                        continue;
                    }
                    if ("Y".equals(resvo.getJxsebdjg()) && "Y".equals(resvo.getJxsezcbdjg())
                            && "Y".equals(resvo.getXxsebdjg()) && "Y".equals(resvo.getWkpbdjg())
                            && "Y".equals(resvo.getJmsebdjg())) {
                        tgsl++;
                    }
                }
            }
            return tgsl;
        } else {
            return 0;
        }
    }

    @Override
    public String getsbgljlByYhuuid() {
        String s = "1";
        final String yhuuid = ZnsbSessionUtils.getYhUuid();
        List<ZnsbNssbSbglqhjlbDO> list = znsbNssbSbglqhjlbMapper.getJlByYhuuid(yhuuid);
        if (!GyUtils.isNull(list)) {
            s = list.get(0).getBz();
        }
        return s;
    }

    @Override
    public void saveSbglJlb(String tabIndex) {
        ZnsbNssbSbglqhjlbDO znsbNssbSbglqhjlbDO = new ZnsbNssbSbglqhjlbDO();
        znsbNssbSbglqhjlbDO.setYhuuid1(ZnsbSessionUtils.getYhUuid());
        znsbNssbSbglqhjlbDO.setLrrq(DateUtils.toLocalDateTime(DateUtils.getSystemCurrentTime()));
        znsbNssbSbglqhjlbDO.setUuid(GyUtils.getUuid());
        znsbNssbSbglqhjlbDO.setBz(tabIndex);
        znsbNssbSbglqhjlbMapper.insert(znsbNssbSbglqhjlbDO);
    }

    @Override
    public ZzsybnsrSbrwmxInitRespDTO queryZzsybnsrSbrwmx(ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO) {
        ZzsybnsrSbrwmxInitRespDTO respDTO = new ZzsybnsrSbrwmxInitRespDTO();
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/querySbrwmxForZzsybnsrFy");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():zzsybnsrSbrwmxInitReqDTO.getDjxhList());
        paramsMap.put("rwztDm", zzsybnsrSbrwmxInitReqDTO.getSbzt());
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getQysh());
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        // 办税员模糊查询BDA0610611
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        bsyStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBsy()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBsy());
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        paramsMap.put("sbny",zzsybnsrSbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        paramsMap.put("pageSize", zzsybnsrSbrwmxInitReqDTO.getPageSize());
        paramsMap.put("pageStart",
                (zzsybnsrSbrwmxInitReqDTO.getPageNumber() - 1) * zzsybnsrSbrwmxInitReqDTO.getPageSize());
        queryReq.setParams(paramsMap);
        final Map<String, Object> sbrwmxResult = (Map<String, Object>)queryApi.query(queryReq);
        final Integer total = MapUtil.getInt(sbrwmxResult, "total");
        final List<Map<String, Object>> sbrwmxListMap = (List<Map<String, Object>>)sbrwmxResult.get("list");
        //final List<Map<String, Object>> sbrwmxListMap = queryApi.queryList(queryReq);
        final List<ZzsybnsrSbrwmxDataRespDTO> sbrwmxDataList =
                BeanUtils.toBean(sbrwmxListMap, ZzsybnsrSbrwmxDataRespDTO.class);
        // 设定返回前端的分页参数
        respDTO.setPageSize(zzsybnsrSbrwmxInitReqDTO.getPageSize());
        respDTO.setPageNumber(zzsybnsrSbrwmxInitReqDTO.getPageNumber());
        respDTO.setPageTotal(GyUtils.isNull(total)?0:total);
        //respDTO.setPageTotal(GyUtils.isNull(sbrwmxDataList)?0:sbrwmxDataList.size());

        // 循环组装返回数据
        if (!GyUtils.isNull(sbrwmxDataList)) {
            try {
                // 获取征期列表
                CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                // 获取征期时间
                List<GszqxxVO> zqList = zqRes.getData();

                for (ZzsybnsrSbrwmxDataRespDTO mxDO : sbrwmxDataList) {
                    // 企业代码值，此处分页查询数据不超过20条，直接调用门户接口查询即可
                    CommonResult<CompanyBasicInfoDTO> companyRes = companyApi.basicInfo(mxDO.getDjxh(), mxDO.getNsrsbh());
                    if (!GyUtils.isNull(companyRes.getData())) {
                        mxDO.setJguuid(companyRes.getData().getJguuid());
                    }
                    // 任务状态名称
                    mxDO.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", mxDO.getRwztDm()));
                    // 纳税人申报状态名称ZDY_DM_SBRW_SBZT
                    mxDO.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", mxDO.getNsrsbztDm()));

                    // 调用台账财务合规检查接口组装统计数据
                    ZzsCybdReqVO zzsCybdReqVO = new ZzsCybdReqVO();
                    zzsCybdReqVO.setDjxh(Collections.singletonList(mxDO.getDjxh()));
                    zzsCybdReqVO.setSkssqq(DateUtils.dateToString(mxDO.getSkssqq(), 3));
                    zzsCybdReqVO.setSkssqz(DateUtils.dateToString(mxDO.getSkssqz(), 3));
                    zzsCybdReqVO.setZsxmDm(mxDO.getZsxmDm());
                    List<ZzsCybdResVO> zzscybdList = cybdService.getCybdxx(zzsCybdReqVO);
                    if (!GyUtils.isNull(zzscybdList)) {
                        ZzsCybdResVO resVO = zzscybdList.get(0);
                        mxDO.setCybdXxse(this.getcybd(resVO.getXxsebdjg()));
                        mxDO.setCybdJxse(this.getcybd(resVO.getJxsebdjg()));
                        mxDO.setCybdJxsezc(this.getcybd(resVO.getJxsezcbdjg()));
                        mxDO.setCybdJmse(this.getcybd(resVO.getJmsebdjg()));
                        mxDO.setCybdWkpxse(this.getcybd(resVO.getWkpbdjg()));
                    } else {
                        // 为空则默认全为不通过
                        mxDO.setCybdXxse(this.getcybd("N"));
                        mxDO.setCybdJxse(this.getcybd("N"));
                        mxDO.setCybdJxsezc(this.getcybd("N"));
                        mxDO.setCybdJmse(this.getcybd("N"));
                        mxDO.setCybdWkpxse(this.getcybd("N"));
                    }

                    // 取配置去申报按钮跳转路径 系统异常重新提交的路径
                    Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", mxDO.getYzpzzlDm());
                    if (GyUtils.isNull(qsbMap)) {
                        mxDO.setActionQsb("");
                        mxDO.setActionError("");
                    } else {
                        mxDO.setActionQsb(String.valueOf(qsbMap.get("caption")));
                        mxDO.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
                    }

                    // 取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
                    mxDO.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", mxDO.getYzpzzlDm()+mxDO.getZsxmDm()));

                    // 组装征期时间，资料报送类的不需要
                    if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(mxDO.getRwlxDm())) {
                        mxDO.setZqsj(this.getzqsj(mxDO, zqList));
                    }

                    mxDO.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

                    //判断无需缴款状态进行更改
                    if (!GyUtils.isNull(mxDO.getYbtse())){
                        //小于等于1 并且已申报
                        if (SbrwztConstants.RWZT_YSB_DM.equals(mxDO.getRwztDm())
                                && mxDO.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                            mxDO.setSbjkztDm(JKZT_WXJK_DM);
                        }
                    }

                }
            }catch (Exception e){
                log.error("",e);
                log.info("加载申报概览按报表列表数据处理时出现异常：{}",e.getMessage());
            }
        }

        //按企业代码值排序，由于企业代码值没有关联表查询并且为后台分页，暂时只在同一页中进行排序
        /*if (!GyUtils.isNull(sbrwmxDataList)){
            //先过滤企业税号
            final String qysh = GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh())?"":zzsybnsrSbrwmxInitReqDTO.getQysh();
            List<ZzsybnsrSbrwmxDataRespDTO> filterList = sbrwmxDataList.stream().filter(sbrw ->
                    StringUtils.contains(GyUtils.isNull(sbrw.getQydmz())?"":sbrw.getQydmz(), qysh)||
                    StringUtils.contains(GyUtils.isNull(sbrw.getNsrsbh())?"":sbrw.getNsrsbh(), qysh)||
                    StringUtils.contains(GyUtils.isNull(sbrw.getNsrmc())?"":sbrw.getNsrmc(), qysh))
                    .collect(Collectors.toList());
            respDTO.setPageTotal(filterList.size());
            if (!GyUtils.isNull(filterList)){
                //再排序
                List<ZzsybnsrSbrwmxDataRespDTO> sortedList = filterList.stream()
                        .sorted(Comparator.comparing(ZzsybnsrSbrwmxDataRespDTO::getQydmz, Comparator.nullsLast(String::compareTo))
                                .thenComparing(ZzsybnsrSbrwmxDataRespDTO::getNsrmc).thenComparing(ZzsybnsrSbrwmxDataRespDTO::getRwztDm)
                                .thenComparing(ZzsybnsrSbrwmxDataRespDTO::getNsrsbztDm))
                        .collect(Collectors.toList());
                //分页处理返回数据
                List<ZzsybnsrSbrwmxDataRespDTO> pageList = CollUtil.page(zzsybnsrSbrwmxInitReqDTO.getPageNumber()-1, zzsybnsrSbrwmxInitReqDTO.getPageSize(), sortedList);
                //new PageResult<ZzsybnsrSbrwmxDataRespDTO>().setList(pageList);
                respDTO.setDataList(pageList);
            }
        }*/
        respDTO.setDataList(sbrwmxDataList);
        return respDTO;
    }

    @Override
    public ZzsybnsrSbrwmxInitRespDTO queryZzsxgmnsrSbrwmx(ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO) {
        ZzsybnsrSbrwmxInitRespDTO respDTO = new ZzsybnsrSbrwmxInitRespDTO();
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/querySbrwmxForZzsxgmnsrFy");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():zzsybnsrSbrwmxInitReqDTO.getDjxhList());
        paramsMap.put("rwztDm", zzsybnsrSbrwmxInitReqDTO.getSbzt());
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getQysh());
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        // 办税员模糊查询BDA0610611
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        bsyStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBsy()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBsy());
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        paramsMap.put("sbny",zzsybnsrSbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        paramsMap.put("pageSize", zzsybnsrSbrwmxInitReqDTO.getPageSize());
        paramsMap.put("pageStart",
                (zzsybnsrSbrwmxInitReqDTO.getPageNumber() - 1) * zzsybnsrSbrwmxInitReqDTO.getPageSize());
        queryReq.setParams(paramsMap);
        final Map<String, Object> sbrwmxResult = (Map<String, Object>)queryApi.query(queryReq);
        final Integer total = MapUtil.getInt(sbrwmxResult, "total");
        final List<Map<String, Object>> sbrwmxListMap = (List<Map<String, Object>>)sbrwmxResult.get("list");
        //final List<Map<String, Object>> sbrwmxListMap = queryApi.queryList(queryReq);
        final List<ZzsybnsrSbrwmxDataRespDTO> sbrwmxDataList =
                BeanUtils.toBean(sbrwmxListMap, ZzsybnsrSbrwmxDataRespDTO.class);
        // 设定返回前端的分页参数
        respDTO.setPageSize(zzsybnsrSbrwmxInitReqDTO.getPageSize());
        respDTO.setPageNumber(zzsybnsrSbrwmxInitReqDTO.getPageNumber());
        respDTO.setPageTotal(GyUtils.isNull(total)?0:total);
        //respDTO.setPageTotal(GyUtils.isNull(sbrwmxDataList)?0:sbrwmxDataList.size());

        // 循环组装返回数据
        if (!GyUtils.isNull(sbrwmxDataList)) {
            try {
                // 获取征期列表
                CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                // 获取征期时间
                List<GszqxxVO> zqList = zqRes.getData();

                for (ZzsybnsrSbrwmxDataRespDTO mxDO : sbrwmxDataList) {
                    // 企业代码值，此处分页查询数据不超过20条，直接调用门户接口查询即可
                    CommonResult<CompanyBasicInfoDTO> companyRes = companyApi.basicInfo(mxDO.getDjxh(), mxDO.getNsrsbh());
                    if (!GyUtils.isNull(companyRes.getData())) {
                        mxDO.setJguuid(companyRes.getData().getJguuid());
                    }
                    // 任务状态名称
                    mxDO.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", mxDO.getRwztDm()));
                    // 纳税人申报状态名称ZDY_DM_SBRW_SBZT
                    mxDO.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", mxDO.getNsrsbztDm()));

                    // 调用台账财务合规检查接口组装统计数据
                    ZzsCybdReqVO zzsCybdReqVO = new ZzsCybdReqVO();
                    zzsCybdReqVO.setDjxh(Collections.singletonList(mxDO.getDjxh()));
                    zzsCybdReqVO.setSkssqq(DateUtils.dateToString(mxDO.getSkssqq(), 3));
                    zzsCybdReqVO.setSkssqz(DateUtils.dateToString(mxDO.getSkssqz(), 3));
                    zzsCybdReqVO.setZsxmDm(mxDO.getZsxmDm());
                    List<ZzsCybdResVO> zzscybdList = cybdService.getCybdxx(zzsCybdReqVO);
                    if (!GyUtils.isNull(zzscybdList)) {
                        ZzsCybdResVO resVO = zzscybdList.get(0);
                        mxDO.setCybdXxse(this.getcybd(resVO.getXxsebdjg()));
                        /*mxDO.setCybdJxse(this.getcybd(resVO.getJxsebdjg()));
                        mxDO.setCybdJxsezc(this.getcybd(resVO.getJxsezcbdjg()));
                        mxDO.setCybdJmse(this.getcybd(resVO.getJmsebdjg()));*/
                        mxDO.setCybdWkpxse(this.getcybd(resVO.getWkpbdjg()));
                    } else {
                        // 为空则默认全为不通过
                        mxDO.setCybdXxse(this.getcybd("N"));
                        /*mxDO.setCybdJxse(this.getcybd("N"));
                        mxDO.setCybdJxsezc(this.getcybd("N"));
                        mxDO.setCybdJmse(this.getcybd("N"));*/
                        mxDO.setCybdWkpxse(this.getcybd("N"));
                    }

                    // 取配置去申报按钮跳转路径 系统异常重新提交的路径
                    Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", mxDO.getYzpzzlDm());
                    if (GyUtils.isNull(qsbMap)) {
                        mxDO.setActionQsb("");
                        mxDO.setActionError("");
                    } else {
                        mxDO.setActionQsb(String.valueOf(qsbMap.get("caption")));
                        mxDO.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
                    }

                    // 取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
                    mxDO.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", mxDO.getYzpzzlDm()+mxDO.getZsxmDm()));

                    // 组装征期时间，资料报送类的不需要
                    if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(mxDO.getRwlxDm())) {
                        mxDO.setZqsj(this.getzqsj(mxDO, zqList));
                    }

                    mxDO.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

                    //判断无需缴款状态进行更改
                    if (!GyUtils.isNull(mxDO.getYbtse())){
                        //小于等于1 并且已申报
                        if (SbrwztConstants.RWZT_YSB_DM.equals(mxDO.getRwztDm())
                                && mxDO.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                            mxDO.setSbjkztDm(JKZT_WXJK_DM);
                        }
                    }

                }
            }catch (Exception e){
                log.error("",e);
                log.info("加载申报概览按报表列表数据处理时出现异常：{}",e.getMessage());
            }
        }
        respDTO.setDataList(sbrwmxDataList);
        return respDTO;
    }

    @Override
    public ZzsybnsrSbrwmxInitRespDTO queryZzsyjSbrwmx(ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO) {
        ZzsybnsrSbrwmxInitRespDTO respDTO = new ZzsybnsrSbrwmxInitRespDTO();
        // 查询申报任务
        ZzsyjPlsbVO zzsyjPlsbVO = new ZzsyjPlsbVO();
        zzsyjPlsbVO.setDjxhList(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():zzsybnsrSbrwmxInitReqDTO.getDjxhList());
        zzsyjPlsbVO.setRwztDm(zzsybnsrSbrwmxInitReqDTO.getSbzt());
        // 纳税人识别号模糊查询
//        StringBuilder nsrsbhStr = new StringBuilder();
//        nsrsbhStr.append("%");
//        nsrsbhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getQysh());
//        nsrsbhStr.append("%");
//        paramsMap.put("nsrsbh", nsrsbhStr);
        // 办税员模糊查询BDA0610865
        String bsyStr = GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBsy()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBsy();
        zzsyjPlsbVO.setBsy(bsyStr);
        zzsyjPlsbVO.setSbny(zzsybnsrSbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        zzsyjPlsbVO.setBdcqzh(zzsybnsrSbrwmxInitReqDTO.getBdcqzh());
        zzsyjPlsbVO.setFydz(zzsybnsrSbrwmxInitReqDTO.getFydz());
        zzsyjPlsbVO.setXzqhszDm(zzsybnsrSbrwmxInitReqDTO.getXzqhszDm());
        final List<Map<String, Object>> sbrwmxListMap = zzsyjYsbjgbMapper.queryYsbjgb(zzsyjPlsbVO);
        respDTO.setPageSize(zzsybnsrSbrwmxInitReqDTO.getPageSize());
        respDTO.setPageNumber(zzsybnsrSbrwmxInitReqDTO.getPageNumber());
        respDTO.setPageTotal(sbrwmxListMap.size());
        final List<ZzsybnsrSbrwmxDataRespDTO> sbrwmxDataList =
                BeanUtils.toBean(sbrwmxListMap, ZzsybnsrSbrwmxDataRespDTO.class);

        // 循环组装返回数据
        if (!GyUtils.isNull(sbrwmxDataList)) {
            try {
                // 获取征期列表
                CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                // 获取征期时间
                List<GszqxxVO> zqList = zqRes.getData();

                for (ZzsybnsrSbrwmxDataRespDTO mxDO : sbrwmxDataList) {
                    // 企业代码值，此处分页查询数据不超过20条，直接调用门户接口查询即可
                    CommonResult<CompanyBasicInfoDTO> companyRes = companyApi.basicInfo(mxDO.getDjxh(), mxDO.getNsrsbh());
                    if (!GyUtils.isNull(companyRes.getData())) {
                        mxDO.setJguuid(companyRes.getData().getJguuid());
                    }
                    // 任务状态名称
                    mxDO.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", mxDO.getRwztDm()));
                    // 纳税人申报状态名称ZDY_DM_SBRW_SBZT
                    mxDO.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", mxDO.getNsrsbztDm()));

                    // 调用台账财务合规检查接口组装统计数据
                    ZzsCybdReqVO zzsCybdReqVO = new ZzsCybdReqVO();
                    zzsCybdReqVO.setDjxh(Collections.singletonList(mxDO.getDjxh()));
                    zzsCybdReqVO.setSkssqq(DateUtils.dateToString(mxDO.getSkssqq(), 3));
                    zzsCybdReqVO.setSkssqz(DateUtils.dateToString(mxDO.getSkssqz(), 3));
                    zzsCybdReqVO.setZsxmDm(mxDO.getZsxmDm());
                    List<ZzsCybdResVO> zzscybdList = cybdService.getCybdxx(zzsCybdReqVO);
                    if (!GyUtils.isNull(zzscybdList)) {
                        ZzsCybdResVO resVO = zzscybdList.get(0);
                        mxDO.setCybdXxse(this.getcybd(resVO.getXxsebdjg()));
                        mxDO.setCybdWkpxse(this.getcybd(resVO.getWkpbdjg()));
                    } else {
                        // 为空则默认全为不通过
                        mxDO.setCybdXxse(this.getcybd("N"));
                        mxDO.setCybdWkpxse(this.getcybd("N"));
                    }

                    // 取配置去申报按钮跳转路径 系统异常重新提交的路径
                    Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", mxDO.getYzpzzlDm());
                    if (GyUtils.isNull(qsbMap)) {
                        mxDO.setActionQsb("");
                        mxDO.setActionError("");
                    } else {
                        mxDO.setActionQsb(String.valueOf(qsbMap.get("caption")));
                        mxDO.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
                    }

                    // 取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
                    mxDO.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", mxDO.getYzpzzlDm()+mxDO.getZsxmDm()));

                    // 组装征期时间，资料报送类的不需要
                    if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(mxDO.getRwlxDm())) {
                        mxDO.setZqsj(this.getzqsj(mxDO, zqList));
                    }

                    mxDO.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

                    //判断无需缴款状态进行更改
                    if (!GyUtils.isNull(mxDO.getYbtse())){
                        //小于等于1 并且已申报
                        if (SbrwztConstants.RWZT_YSB_DM.equals(mxDO.getRwztDm())
                                && mxDO.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                            mxDO.setSbjkztDm(JKZT_WXJK_DM);
                        }
                    }

                    //根据sjcsdq获取税务机关名称(简称)
                    Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg", mxDO.getSjcsdq());
                    if(!GyUtils.isNull(swjgMap)){
                        mxDO.setSwjgmc(this.getswjgmc(swjgMap));
                    }else {
                        mxDO.setSwjgmc("");
                    }

                    //行政区划数字代码转名称
                    Map<String, Object> xzqhMap = CacheUtils.getTableData("dm_gy_xzqh", mxDO.getXzqhszDm());
                    if (!GyUtils.isNull(xzqhMap)){
                        mxDO.setXzqhszmc(MapUtil.getStr(xzqhMap,"xzqhmc"));
                    }

                }
            }catch (Exception e){
                log.error("",e);
                log.info("加载申报概览按报表列表数据处理时出现异常：{}",e.getMessage());
            }
        }
        respDTO.setDataList(CollectionUtil.page(zzsybnsrSbrwmxInitReqDTO.getPageNumber()-1, zzsybnsrSbrwmxInitReqDTO.getPageSize(), sbrwmxDataList));
        return respDTO;
    }

    @Override
    public ZzsybnsrSbrwmxInitRespDTO queryYhsSbrwmx(ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO) {
        ZzsybnsrSbrwmxInitRespDTO respDTO = new ZzsybnsrSbrwmxInitRespDTO();
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/querySbrwmxForYhsFy");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():zzsybnsrSbrwmxInitReqDTO.getDjxhList());
        paramsMap.put("rwztDm", zzsybnsrSbrwmxInitReqDTO.getSbzt());
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getQysh());
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        // 办税员模糊查询
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        bsyStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBsy()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBsy());
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        paramsMap.put("sbny",zzsybnsrSbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        paramsMap.put("pageSize", zzsybnsrSbrwmxInitReqDTO.getPageSize());
        paramsMap.put("pageStart",
                (zzsybnsrSbrwmxInitReqDTO.getPageNumber() - 1) * zzsybnsrSbrwmxInitReqDTO.getPageSize());
        queryReq.setParams(paramsMap);
        final Map<String, Object> sbrwmxResult = (Map<String, Object>)queryApi.query(queryReq);
        final Integer total = MapUtil.getInt(sbrwmxResult, "total");
        final List<Map<String, Object>> sbrwmxListMap = (List<Map<String, Object>>)sbrwmxResult.get("list");
        //final List<Map<String, Object>> sbrwmxListMap = queryApi.queryList(queryReq);
        final List<ZzsybnsrSbrwmxDataRespDTO> sbrwmxDataList =
                BeanUtils.toBean(sbrwmxListMap, ZzsybnsrSbrwmxDataRespDTO.class);
        // 设定返回前端的分页参数
        respDTO.setPageSize(zzsybnsrSbrwmxInitReqDTO.getPageSize());
        respDTO.setPageNumber(zzsybnsrSbrwmxInitReqDTO.getPageNumber());
        respDTO.setPageTotal(GyUtils.isNull(total)?0:total);
        //respDTO.setPageTotal(GyUtils.isNull(sbrwmxDataList)?0:sbrwmxDataList.size());

        // 循环组装返回数据
        if (!GyUtils.isNull(sbrwmxDataList)) {
            try {
                // 获取征期列表
                CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                // 获取征期时间
                List<GszqxxVO> zqList = zqRes.getData();

                for (ZzsybnsrSbrwmxDataRespDTO mxDO : sbrwmxDataList) {
                    // 企业代码值，此处分页查询数据不超过20条，直接调用门户接口查询即可
                    CommonResult<CompanyBasicInfoDTO> companyRes = companyApi.basicInfo(mxDO.getDjxh(), mxDO.getNsrsbh());
                    if (!GyUtils.isNull(companyRes.getData())) {
                        mxDO.setJguuid(companyRes.getData().getJguuid());
                    }
                    // 任务状态名称
                    mxDO.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", mxDO.getRwztDm()));
                    // 纳税人申报状态名称ZDY_DM_SBRW_SBZT
                    mxDO.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", mxDO.getNsrsbztDm()));

                    // 取配置去申报按钮跳转路径 系统异常重新提交的路径
                    Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", mxDO.getYzpzzlDm());
                    if (GyUtils.isNull(qsbMap)) {
                        mxDO.setActionQsb("");
                        mxDO.setActionError("");
                    } else {
                        mxDO.setActionQsb(String.valueOf(qsbMap.get("caption")));
                        mxDO.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
                    }

                    // 取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
                    mxDO.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", mxDO.getYzpzzlDm()+mxDO.getZsxmDm()));

                    // 组装征期时间，资料报送类的不需要
                    if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(mxDO.getRwlxDm())) {
                        mxDO.setZqsj(this.getzqsj(mxDO, zqList));
                    }

                    mxDO.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

                    //判断无需缴款状态进行更改
                    if (!GyUtils.isNull(mxDO.getYbtse())){
                        //小于等于1 并且已申报
                        if (SbrwztConstants.RWZT_YSB_DM.equals(mxDO.getRwztDm())
                                && mxDO.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                            mxDO.setSbjkztDm(JKZT_WXJK_DM);
                        }
                    }

                }
            }catch (Exception e){
                log.error("",e);
                log.info("加载申报概览按报表列表数据处理时出现异常：{}",e.getMessage());
            }
        }

        respDTO.setDataList(sbrwmxDataList);
        return respDTO;
    }

    @Override
    public ZzsybnsrSbrwmxInitRespDTO queryFcsSbrwmx(ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO) {
        ZzsybnsrSbrwmxInitRespDTO respDTO = new ZzsybnsrSbrwmxInitRespDTO();
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/querySbrwmxForFcsFy");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():zzsybnsrSbrwmxInitReqDTO.getDjxhList());
        paramsMap.put("rwztDm", zzsybnsrSbrwmxInitReqDTO.getSbzt());
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getQysh());
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        // 办税员模糊查询
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        bsyStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBsy()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBsy());
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        //不动产权证号
        StringBuilder bdcqzhStr = new StringBuilder();
        bdcqzhStr.append("%");
        bdcqzhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBdcqzh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBdcqzh());
        bdcqzhStr.append("%");
        paramsMap.put("bdcqzh", bdcqzhStr);
        paramsMap.put("xzqhszDm",zzsybnsrSbrwmxInitReqDTO.getXzqhszDm());
        paramsMap.put("sbny",zzsybnsrSbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        paramsMap.put("pageSize", zzsybnsrSbrwmxInitReqDTO.getPageSize());
        paramsMap.put("pageStart",
                (zzsybnsrSbrwmxInitReqDTO.getPageNumber() - 1) * zzsybnsrSbrwmxInitReqDTO.getPageSize());
        queryReq.setParams(paramsMap);
        final Map<String, Object> sbrwmxResult = (Map<String, Object>)queryApi.query(queryReq);
        final Integer total = MapUtil.getInt(sbrwmxResult, "total");
        final List<Map<String, Object>> sbrwmxListMap = (List<Map<String, Object>>)sbrwmxResult.get("list");
        //final List<Map<String, Object>> sbrwmxListMap = queryApi.queryList(queryReq);
        final List<ZzsybnsrSbrwmxDataRespDTO> sbrwmxDataList =
                BeanUtils.toBean(sbrwmxListMap, ZzsybnsrSbrwmxDataRespDTO.class);
        // 设定返回前端的分页参数
        respDTO.setPageSize(zzsybnsrSbrwmxInitReqDTO.getPageSize());
        respDTO.setPageNumber(zzsybnsrSbrwmxInitReqDTO.getPageNumber());
        respDTO.setPageTotal(GyUtils.isNull(total)?0:total);
        //respDTO.setPageTotal(GyUtils.isNull(sbrwmxDataList)?0:sbrwmxDataList.size());

        // 循环组装返回数据
        if (!GyUtils.isNull(sbrwmxDataList)) {
            try {
                // 获取征期列表
                CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                // 获取征期时间
                List<GszqxxVO> zqList = zqRes.getData();

                for (ZzsybnsrSbrwmxDataRespDTO mxDO : sbrwmxDataList) {
                    // 企业代码值，此处分页查询数据不超过20条，直接调用门户接口查询即可
                    CommonResult<CompanyBasicInfoDTO> companyRes = companyApi.basicInfo(mxDO.getDjxh(), mxDO.getNsrsbh());
                    if (!GyUtils.isNull(companyRes.getData())) {
                        mxDO.setJguuid(companyRes.getData().getJguuid());
                    }
                    // 任务状态名称
                    mxDO.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", mxDO.getRwztDm()));
                    // 纳税人申报状态名称ZDY_DM_SBRW_SBZT
                    mxDO.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", mxDO.getNsrsbztDm()));

                    // 取配置去申报按钮跳转路径 系统异常重新提交的路径
                    Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", mxDO.getYzpzzlDm());
                    if (GyUtils.isNull(qsbMap)) {
                        mxDO.setActionQsb("");
                        mxDO.setActionError("");
                    } else {
                        mxDO.setActionQsb(String.valueOf(qsbMap.get("caption")));
                        mxDO.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
                    }

                    // 取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
                    mxDO.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", mxDO.getYzpzzlDm()+mxDO.getZsxmDm()));

                    // 组装征期时间，资料报送类的不需要
                    if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(mxDO.getRwlxDm())) {
                        mxDO.setZqsj(this.getzqsj(mxDO, zqList));
                    }

                    mxDO.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

                    //判断无需缴款状态进行更改
                    if (!GyUtils.isNull(mxDO.getYbtse())){
                        //小于等于1 并且已申报
                        if (SbrwztConstants.RWZT_YSB_DM.equals(mxDO.getRwztDm())
                                && mxDO.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                            mxDO.setSbjkztDm(JKZT_WXJK_DM);
                        }
                    }

                    //根据sjcsdq获取税务机关名称(简称)
                    Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg", mxDO.getSjcsdq());
                    if(!GyUtils.isNull(swjgMap)){
                        mxDO.setSwjgmc(this.getswjgmc(swjgMap));
                    }else {
                        mxDO.setSwjgmc("");
                    }

                    //行政区划数字代码转名称
                    Map<String, Object> xzqhMap = CacheUtils.getTableData("dm_gy_xzqh", mxDO.getXzqhszDm());
                    if (!GyUtils.isNull(xzqhMap)){
                        mxDO.setXzqhszmc(MapUtil.getStr(xzqhMap,"xzqhmc"));
                    }

                }
            }catch (Exception e){
                log.error("",e);
                log.info("加载申报概览按报表列表数据处理时出现异常：{}",e.getMessage());
            }
        }

        respDTO.setDataList(sbrwmxDataList);
        return respDTO;
    }

    private String getswjgmc(Map<String, Object> swjgMap) {
        if (GyUtils.isNull(swjgMap)){
            return "";
        }
        //获取此条机关对应的行政区划
        final String xzqhszDm = MapUtil.getStr(swjgMap,"xzqhszDm");
        if (GyUtils.isNull(xzqhszDm)){
            return MapUtil.getStr(swjgMap,"swjgjc");
        }
        //根据行政区划数字代码查询代码表数据
        Map<String, Object> xzqhMap = CacheUtils.getTableData("dm_gy_xzqh", xzqhszDm);
        if (GyUtils.isNull(xzqhMap)){
            return MapUtil.getStr(swjgMap,"swjgjc");
        }
        return MapUtil.getStr(xzqhMap,"ssxzqmc") + "/" + MapUtil.getStr(swjgMap,"swjgjc");
    }

    @Override
    public ZzsybnsrSbrwmxInitRespDTO queryCztdsysSbrwmx(ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO) {
        ZzsybnsrSbrwmxInitRespDTO respDTO = new ZzsybnsrSbrwmxInitRespDTO();
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/querySbrwmxForCztdsysFy");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():zzsybnsrSbrwmxInitReqDTO.getDjxhList());
        paramsMap.put("rwztDm", zzsybnsrSbrwmxInitReqDTO.getSbzt());
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getQysh());
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        // 办税员模糊查询
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        bsyStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBsy()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBsy());
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        //土地编号
        StringBuilder tdbhhStr = new StringBuilder();
        tdbhhStr.append("%");
        tdbhhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getTdbh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getTdbh());
        tdbhhStr.append("%");
        paramsMap.put("tdbh", tdbhhStr);
        paramsMap.put("xzqhszDm",zzsybnsrSbrwmxInitReqDTO.getXzqhszDm());
        paramsMap.put("sbny",zzsybnsrSbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        paramsMap.put("pageSize", zzsybnsrSbrwmxInitReqDTO.getPageSize());
        paramsMap.put("pageStart",
                (zzsybnsrSbrwmxInitReqDTO.getPageNumber() - 1) * zzsybnsrSbrwmxInitReqDTO.getPageSize());
        queryReq.setParams(paramsMap);
        final Map<String, Object> sbrwmxResult = (Map<String, Object>)queryApi.query(queryReq);
        final Integer total = MapUtil.getInt(sbrwmxResult, "total");
        final List<Map<String, Object>> sbrwmxListMap = (List<Map<String, Object>>)sbrwmxResult.get("list");
        //final List<Map<String, Object>> sbrwmxListMap = queryApi.queryList(queryReq);
        final List<ZzsybnsrSbrwmxDataRespDTO> sbrwmxDataList =
                BeanUtils.toBean(sbrwmxListMap, ZzsybnsrSbrwmxDataRespDTO.class);
        // 设定返回前端的分页参数
        respDTO.setPageSize(zzsybnsrSbrwmxInitReqDTO.getPageSize());
        respDTO.setPageNumber(zzsybnsrSbrwmxInitReqDTO.getPageNumber());
        respDTO.setPageTotal(GyUtils.isNull(total)?0:total);
        //respDTO.setPageTotal(GyUtils.isNull(sbrwmxDataList)?0:sbrwmxDataList.size());

        // 循环组装返回数据
        if (!GyUtils.isNull(sbrwmxDataList)) {
            try {
                // 获取征期列表
                CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                // 获取征期时间
                List<GszqxxVO> zqList = zqRes.getData();

                for (ZzsybnsrSbrwmxDataRespDTO mxDO : sbrwmxDataList) {
                    // 企业代码值，此处分页查询数据不超过20条，直接调用门户接口查询即可
                    CommonResult<CompanyBasicInfoDTO> companyRes = companyApi.basicInfo(mxDO.getDjxh(), mxDO.getNsrsbh());
                    if (!GyUtils.isNull(companyRes.getData())) {
                        mxDO.setJguuid(companyRes.getData().getJguuid());
                    }
                    // 任务状态名称
                    mxDO.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", mxDO.getRwztDm()));
                    // 纳税人申报状态名称ZDY_DM_SBRW_SBZT
                    mxDO.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", mxDO.getNsrsbztDm()));

                    // 取配置去申报按钮跳转路径 系统异常重新提交的路径
                    Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", mxDO.getYzpzzlDm());
                    if (GyUtils.isNull(qsbMap)) {
                        mxDO.setActionQsb("");
                        mxDO.setActionError("");
                    } else {
                        mxDO.setActionQsb(String.valueOf(qsbMap.get("caption")));
                        mxDO.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
                    }

                    // 取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
                    mxDO.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", mxDO.getYzpzzlDm()+mxDO.getZsxmDm()));

                    // 组装征期时间，资料报送类的不需要
                    if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(mxDO.getRwlxDm())) {
                        mxDO.setZqsj(this.getzqsj(mxDO, zqList));
                    }

                    mxDO.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

                    //判断无需缴款状态进行更改
                    if (!GyUtils.isNull(mxDO.getYbtse())){
                        //小于等于1 并且已申报
                        if (SbrwztConstants.RWZT_YSB_DM.equals(mxDO.getRwztDm())
                                && mxDO.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                            mxDO.setSbjkztDm(JKZT_WXJK_DM);
                        }
                    }

                    //根据sjcsdq获取税务机关名称(简称)
                    Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg", mxDO.getSjcsdq());
                    if(!GyUtils.isNull(swjgMap)){
                        mxDO.setSwjgmc(this.getswjgmc(swjgMap));
                    }else {
                        mxDO.setSwjgmc("");
                    }

                    //行政区划数字代码转名称
                    Map<String, Object> xzqhMap = CacheUtils.getTableData("dm_gy_xzqh", mxDO.getXzqhszDm());
                    if (!GyUtils.isNull(xzqhMap)){
                        mxDO.setXzqhszmc(MapUtil.getStr(xzqhMap,"xzqhmc"));
                    }

                }
            }catch (Exception e){
                log.error("",e);
                log.info("加载申报概览按报表列表数据处理时出现异常：{}",e.getMessage());
            }
        }

        respDTO.setDataList(sbrwmxDataList);
        return respDTO;
    }

    @Override
    public ZzsybnsrSbrwmxInitRespDTO queryCwbbSbrwmx(ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO){
        ZzsybnsrSbrwmxInitRespDTO respDTO = new ZzsybnsrSbrwmxInitRespDTO();
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/querySbrwmxForCwbbbsFy");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():zzsybnsrSbrwmxInitReqDTO.getDjxhList());
        paramsMap.put("rwztDm", zzsybnsrSbrwmxInitReqDTO.getSbzt());
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getQysh());
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        // 办税员模糊查询
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        bsyStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBsy()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBsy());
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        paramsMap.put("sbny",zzsybnsrSbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        paramsMap.put("pageSize", zzsybnsrSbrwmxInitReqDTO.getPageSize());
        paramsMap.put("pageStart",
                (zzsybnsrSbrwmxInitReqDTO.getPageNumber() - 1) * zzsybnsrSbrwmxInitReqDTO.getPageSize());
        queryReq.setParams(paramsMap);
        final Map<String, Object> sbrwmxResult = (Map<String, Object>)queryApi.query(queryReq);
        final Integer total = MapUtil.getInt(sbrwmxResult, "total");
        final List<Map<String, Object>> sbrwmxListMap = (List<Map<String, Object>>)sbrwmxResult.get("list");
        //final List<Map<String, Object>> sbrwmxListMap = queryApi.queryList(queryReq);
        final List<ZzsybnsrSbrwmxDataRespDTO> sbrwmxDataList =
                BeanUtils.toBean(sbrwmxListMap, ZzsybnsrSbrwmxDataRespDTO.class);
        // 设定返回前端的分页参数
        respDTO.setPageSize(zzsybnsrSbrwmxInitReqDTO.getPageSize());
        respDTO.setPageNumber(zzsybnsrSbrwmxInitReqDTO.getPageNumber());
        respDTO.setPageTotal(GyUtils.isNull(total)?0:total);
        //respDTO.setPageTotal(GyUtils.isNull(sbrwmxDataList)?0:sbrwmxDataList.size());

        // 循环组装返回数据
        if (!GyUtils.isNull(sbrwmxDataList)) {
            try {
                // 获取征期列表
                CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                // 获取征期时间
                List<GszqxxVO> zqList = zqRes.getData();

                for (ZzsybnsrSbrwmxDataRespDTO mxDO : sbrwmxDataList) {
                    // 企业代码值，此处分页查询数据不超过20条，直接调用门户接口查询即可
                    CommonResult<CompanyBasicInfoDTO> companyRes = companyApi.basicInfo(mxDO.getDjxh(), mxDO.getNsrsbh());
                    if (!GyUtils.isNull(companyRes.getData())) {
                        mxDO.setJguuid(companyRes.getData().getJguuid());
                    }
                    // 任务状态名称
                    mxDO.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", mxDO.getRwztDm()));
                    // 纳税人申报状态名称ZDY_DM_SBRW_SBZT
                    mxDO.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", mxDO.getNsrsbztDm()));

                    // 取配置去申报按钮跳转路径 系统异常重新提交的路径
                    Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", mxDO.getYzpzzlDm());
                    if (GyUtils.isNull(qsbMap)) {
                        mxDO.setActionQsb("");
                        mxDO.setActionError("");
                    } else {
                        mxDO.setActionQsb(String.valueOf(qsbMap.get("caption")));
                        mxDO.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
                    }

                    // 取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
                    mxDO.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", mxDO.getYzpzzlDm()+mxDO.getZsxmDm()));

                    // 组装征期时间，资料报送类的不需要
                    if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(mxDO.getRwlxDm())) {
                        mxDO.setZqsj(this.getzqsj(mxDO, zqList));
                    }

                    mxDO.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

                    //判断无需缴款状态进行更改
                    if (!GyUtils.isNull(mxDO.getYbtse())){
                        //小于等于1 并且已申报
                        if (SbrwztConstants.RWZT_YSB_DM.equals(mxDO.getRwztDm())
                                && mxDO.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                            mxDO.setSbjkztDm(JKZT_WXJK_DM);
                        }
                    }

                }
            }catch (Exception e){
                log.error("",e);
                log.info("加载申报概览按报表列表数据处理时出现异常：{}",e.getMessage());
            }
        }

        respDTO.setDataList(sbrwmxDataList);
        return respDTO;
    }

    @Override
    public List<ZnsbNssbSbrwDO> getQhbbSbrwList() {
        return this.getBaseMapper().getQhbbSbrwList(getLastThreeMonthSbny());
    }

    @Override
    public List<ZnsbNssbSbrwDTO> exportList(SbrwsyReqVO reqVO) {
        final String sbny = reqVO.getSbny().replace("-", "");
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbgl/exportSbmxList");
        final Map<String, Object> paramsMap = new HashMap<>();
        final List<String> djxhList = new ArrayList<>(reqVO.getDjxhList());
        List<Map<String,Object>> kqjgList = reqVO.getKqjgList();
        if (!GyUtils.isNull(kqjgList)){
            for (Map<String,Object> kqjg:kqjgList){
                djxhList.add(String.valueOf(kqjg.get("djxh")));
            }
        }
        paramsMap.put("djxhList", djxhList);
        paramsMap.put("sbny",sbny);
        final String yzpzzlDm = !GyUtils.isNull(reqVO.getYzpzzlDm())?reqVO.getYzpzzlDm().split("\\|")[0]:"";
        final String zsxmDm = !GyUtils.isNull(reqVO.getYzpzzlDm())?reqVO.getYzpzzlDm().split("\\|")[1]:"";
        if (!GyUtils.isNull(yzpzzlDm)){
            paramsMap.put("yzpzzlDm",yzpzzlDm);
        }
        if (!GyUtils.isNull(zsxmDm)){
            paramsMap.put("zsxmDm",zsxmDm);
        }
        queryReq.setParams(paramsMap);
        final List<Map<String, Object>> sbrwmxResult = queryApi.queryList(queryReq);
        log.info("sbrwmxResult:{}",JsonUtils.toJson(sbrwmxResult));
        List<ZnsbNssbSbrwDTO> resultList = BeanUtils.toBean(sbrwmxResult,ZnsbNssbSbrwDTO.class);
        return resultList;
    }

    private String getLastThreeMonthSbny(){
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 减去3个月
        LocalDate threeMonthsAgo = currentDate.minusMonths(3);

        // 定义yyyyMM格式的 DateTimeFormatter
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");

        // 格式化日期
        return threeMonthsAgo.format(formatter);
    }

    @Override
    public ZzsybnsrSbrwmxInitRespDTO queryQysdsyjSbrwmx(ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO) {
        ZzsybnsrSbrwmxInitRespDTO respDTO = new ZzsybnsrSbrwmxInitRespDTO();
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/querySbrwmxForQysdsyjFy");
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():zzsybnsrSbrwmxInitReqDTO.getDjxhList());
        paramsMap.put("rwztDm", zzsybnsrSbrwmxInitReqDTO.getSbzt());
        // 纳税人识别号模糊查询
        StringBuilder nsrsbhStr = new StringBuilder();
        nsrsbhStr.append("%");
        nsrsbhStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getQysh()) ? "" : zzsybnsrSbrwmxInitReqDTO.getQysh());
        nsrsbhStr.append("%");
        paramsMap.put("nsrsbh", nsrsbhStr);
        // 办税员模糊查询
        StringBuilder bsyStr = new StringBuilder();
        bsyStr.append("%");
        bsyStr.append(GyUtils.isNull(zzsybnsrSbrwmxInitReqDTO.getBsy()) ? "" : zzsybnsrSbrwmxInitReqDTO.getBsy());
        bsyStr.append("%");
        paramsMap.put("bsy", bsyStr);
        paramsMap.put("sbny",zzsybnsrSbrwmxInitReqDTO.getSbny().replaceAll("-", ""));
        paramsMap.put("pageSize", zzsybnsrSbrwmxInitReqDTO.getPageSize());
        paramsMap.put("pageStart",
                (zzsybnsrSbrwmxInitReqDTO.getPageNumber() - 1) * zzsybnsrSbrwmxInitReqDTO.getPageSize());
        queryReq.setParams(paramsMap);
        final Map<String, Object> sbrwmxResult = (Map<String, Object>)queryApi.query(queryReq);
        final Integer total = MapUtil.getInt(sbrwmxResult, "total");
        final List<Map<String, Object>> sbrwmxListMap = (List<Map<String, Object>>)sbrwmxResult.get("list");
        //final List<Map<String, Object>> sbrwmxListMap = queryApi.queryList(queryReq);
        final List<ZzsybnsrSbrwmxDataRespDTO> sbrwmxDataList =
                BeanUtils.toBean(sbrwmxListMap, ZzsybnsrSbrwmxDataRespDTO.class);
        // 设定返回前端的分页参数
        respDTO.setPageSize(zzsybnsrSbrwmxInitReqDTO.getPageSize());
        respDTO.setPageNumber(zzsybnsrSbrwmxInitReqDTO.getPageNumber());
        respDTO.setPageTotal(GyUtils.isNull(total)?0:total);
        //respDTO.setPageTotal(GyUtils.isNull(sbrwmxDataList)?0:sbrwmxDataList.size());

        // 循环组装返回数据
        if (!GyUtils.isNull(sbrwmxDataList)) {
            try {
                // 获取征期列表
                CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                // 获取征期时间
                List<GszqxxVO> zqList = zqRes.getData();

                for (ZzsybnsrSbrwmxDataRespDTO mxDO : sbrwmxDataList) {
                    // 企业代码值，此处分页查询数据不超过20条，直接调用门户接口查询即可
                    CommonResult<CompanyBasicInfoDTO> companyRes = companyApi.basicInfo(mxDO.getDjxh(), mxDO.getNsrsbh());
                    if (!GyUtils.isNull(companyRes.getData())) {
                        mxDO.setJguuid(companyRes.getData().getJguuid());
                    }
                    // 任务状态名称
                    mxDO.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", mxDO.getRwztDm()));
                    // 纳税人申报状态名称ZDY_DM_SBRW_SBZT
                    mxDO.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", mxDO.getNsrsbztDm()));

                    // 取配置去申报按钮跳转路径 系统异常重新提交的路径
                    Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", mxDO.getYzpzzlDm());
                    if (GyUtils.isNull(qsbMap)) {
                        mxDO.setActionQsb("");
                        mxDO.setActionError("");
                    } else {
                        mxDO.setActionQsb(String.valueOf(qsbMap.get("caption")));
                        mxDO.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
                    }

                    // 取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
                    mxDO.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", mxDO.getYzpzzlDm()+mxDO.getZsxmDm()));

                    // 组装征期时间，资料报送类的不需要
                    if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(mxDO.getRwlxDm())) {
                        mxDO.setZqsj(this.getzqsj(mxDO, zqList));
                    }

                    mxDO.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

                    //判断无需缴款状态进行更改
                    if (!GyUtils.isNull(mxDO.getYbtse())){
                        //小于等于1 并且已申报
                        if (SbrwztConstants.RWZT_YSB_DM.equals(mxDO.getRwztDm())
                                && mxDO.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                            mxDO.setSbjkztDm(JKZT_WXJK_DM);
                        }
                    }

                }
            }catch (Exception e){
                log.error("",e);
                log.info("加载申报概览按报表列表数据处理时出现异常：{}",e.getMessage());
            }
        }

        respDTO.setDataList(sbrwmxDataList);
        return respDTO;
    }

    private Date getzqsj(SbrwmxInitRespDTO dto, List<GszqxxVO> zqList) {
        String xzqhszDm = dto.getXzqhszDm();
        if (GyUtils.isNull(xzqhszDm)) {
            // 根据djxh和nsrsbh获取
            CommonResult<CompanyBasicInfoDTO> result = companyApi.basicInfo(dto.getDjxh(), dto.getNsrsbh());
            if (!GyUtils.isNull(result.getData())) {
                xzqhszDm = result.getData().getXzqhszDm();
            } else {
                return DateUtils.toDate(DateUtils.getSystemCurrentTime(0), "yyyy-MM-dd HH:mm:ss");
            }
        }
        final String finalXzqhszDm = xzqhszDm;
        GszqxxVO zqvo = zqList.stream().filter(vo -> vo.getXzqhszDm().contains(finalXzqhszDm)).findAny().orElse(null);
        //判断税费申报征期控制参数
        final String zqcs = CacheUtils.getXtcs("SBRW-ZQCS");
        if (!GyUtils.isNull(zqvo)) {
            log.info("税费申报获取到的当前征期为：{}", JsonUtils.toJson(zqvo));
            if ("Y".equals(zqcs)){
                return DateUtils.toDate(zqvo.getZqsj(), "yyyy-MM-dd hh:mm:ss");
            }else{
                //返回本月最后一天作为征期
                return this.nowMonthLastDay();
            }
        } else {
            log.info("税费申报初始化未获取到有效的征期参数，传入参数sbny，xzqhszDm为：{}，{}", DateUtils.getSystemCurrentTime(17), finalXzqhszDm);
        }
        return DateUtils.toDate(DateUtils.getSystemCurrentTime(0), "yyyy-MM-dd HH:mm:ss");
    }

    private Date nowMonthLastDay() {
        // 获取当前年份和月份
        YearMonth yearMonth = YearMonth.now();

        // 使用TemporalAdjusters获取当前月的最后一天
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();
        return Date.from(lastDayOfMonth.atStartOfDay().atZone(systemDefaultZoneId).toInstant());
    }

    private String getcybd(String bdjg) {
        if ("Y".equals(bdjg)) {
            return "通过";
        } else {
            return "不通过";
        }
    }

    private Date getLastYearFirstDay() {
        // 获取当前日历实例
        Calendar calendar = Calendar.getInstance();

        // 将年份减1，得到去年
        calendar.add(Calendar.YEAR, -1);

        // 设置为去年的1月
        calendar.set(Calendar.MONTH, Calendar.JANUARY);

        // 设置为1月1日
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        // 设置时间为00:00:00.000
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 返回Date类型
        return calendar.getTime();
    }

    @Override
    public void initSbrwByRdxx() {
        // 根据机构信息表全量数据每户进行生成申报任务
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath("/sb/sbrw/ysbxxcj");
        final Map<String, Object> paramsMap = new HashMap<>();
        queryReq.setParams(paramsMap);
        final List<Map<String, Object>> jgxxList = queryApi.queryList(queryReq);
        if (!GyUtils.isNull(jgxxList)) {
            // 获取相关配置
            final List<Map<String, Object>> sbqxwbAll = FtsCxsUtils.getAllCacheData("cs_gy_sbqxwh_sbqx");// 申报期限维护表，700条左右
            final List<Map<String, Object>> jjrAll = FtsCxsUtils.getAllCacheData("cs_gy_jjr");// 节假日配置表，4000条左右
            final List<Map<String, Object>> zqtzAll = FtsCxsUtils.getAllCacheData("cs_gy_zqtz");// 征期调整配置表。50条
            final Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup =
                sbqxwbAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                    .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
            final Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup =
                jjrAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                    .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
            final Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup =
                zqtzAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                    .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
            //获取征期列表
            CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
            for (Map<String, Object> jgxxmap : jgxxList) {
                final String djxh = MapUtil.getStr(jgxxmap, "djxh");
                final String nsrsbh = MapUtil.getStr(jgxxmap, "nsrsbh");
                try {
                    // 调用办税员列表获取办税员
                    String bsymc = StringUtils.EMPTY;
                    DjxhReqVO bsyreqVO = new DjxhReqVO();
                    bsyreqVO.setDjxh(djxh);
                    CommonResult<List<RyxxVO>> ryxxResult = companyApi.getBsyByQy(bsyreqVO);
                    if (!GyUtils.isNull(ryxxResult.getData())) {
                        List<RyxxVO> ryxxList = ryxxResult.getData();
                        if (!GyUtils.isNull(ryxxList)) {
                            bsymc = ryxxList.get(0).getZsxm1();
                        }
                    }
                    // 调用纳税人信息接口查询认定信息
                    ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                    znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
                    znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
                    CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
                    if (!GyUtils.isNull(nsrxxRes.getData())) {
                        ZnsbMhzcQyjbxxmxResVO znsbMhzcQyjbxxmxResVO = nsrxxRes.getData();
                        List<SfzrdmxxxVO> sfzrdmxxxList = znsbMhzcQyjbxxmxResVO.getSfzrdmxxx();
                        // 纳税人基本信息
                        List<JbxxmxsjVO> jbxxmxsj = znsbMhzcQyjbxxmxResVO.getJbxxmxsj();
                        JbxxmxsjVO jbxxmxsjVO = new JbxxmxsjVO();
                        if (!GyUtils.isNull(jbxxmxsj)) {
                            jbxxmxsjVO = jbxxmxsj.get(0);
                        }
                        // 纳税人资格信息
                        List<NsrzgxxVO> nsrzgxx = znsbMhzcQyjbxxmxResVO.getNsrzgxx();
                        NsrzgxxVO nsrzgxxVO = new NsrzgxxVO();
                        if (!GyUtils.isNull(nsrzgxx)) {
                            nsrzgxxVO = nsrzgxx.get(0);
                        }

                        // 查询企业信息的行政区划数字代码
                        CompanyBasicInfoDTO companyBasicInfoDTO = new CompanyBasicInfoDTO();
                        CommonResult<CompanyBasicInfoDTO> companyBasicInfoDTOCommonResult =
                            companyApi.basicInfo(djxh, nsrsbh);
                        if (!GyUtils.isNull(companyBasicInfoDTOCommonResult.getData())) {
                            companyBasicInfoDTO = companyBasicInfoDTOCommonResult.getData();
                        }
                        //获取对应行政区划的征期时间
                        final String xzqhszDm = companyBasicInfoDTO.getXzqhszDm();
                        List<GszqxxVO> zqList = zqRes.getData();
                        //默认征期时间为15号
                        String zqsj = getNowDate15();
                        if (!GyUtils.isNull(zqList)){
                            //只取yyyy-MM-dd格式部分
                            zqsj = zqList.get(0).getZqsj().substring(0,10);
                        }
                        // 取主管税务所科分局代码
                        final List<String> swjgDmList = new ArrayList<>();
                        // 根据行政区划取省级机关传入
                        FtsCxsUtils.getSwjgList(swjgDmList,
                            SfEnum.getSjSwjgDmByXzqhszDm(companyBasicInfoDTO.getXzqhszDm()), new HashMap<>());
                        if (!GyUtils.isNull(sfzrdmxxxList)) {
                            // 先过滤认定有效期起止数据,纳税期限为按次的不处理

                            List<SfzrdmxxxVO> sfzrdYxqList = sfzrdmxxxList.stream()
                                .filter(vo -> DateUtils.diff(getLastYearFirstDay(), vo.getRdyxqq()) >= 0
                                    && DateUtils.diff(new Date(), vo.getRdyxqz()) <= 0 && !"11".equals(vo.getNsqxDm()))
                                .collect(Collectors.toList());

                            // 过滤后，根据征收项目代码和纳税期限代码去重
                            if (!GyUtils.isNull(sfzrdYxqList)) {
                                List<SfzrdmxxxVO> sfzrdResultList = sfzrdYxqList.stream()
                                    .collect(Collectors.collectingAndThen(
                                        Collectors.toCollection(() -> new TreeSet<>(
                                            Comparator.comparing(vo -> vo.getZsxmDm() + ";" + vo.getNsqxDm()))),
                                        ArrayList::new));

                                for (SfzrdmxxxVO sfzrdmxxxVO : sfzrdResultList) {
                                    // 根据征收项目代码校验过滤
                                    if (!this.checkZsxmDmFliter(sfzrdmxxxVO, nsrzgxxVO)) {
                                        continue;
                                    }
                                    // 先匹配应征凭证种类代码，未匹配到的数据不生成申报任务
                                    String yzpzzlDm = matchYzpzzlDmByZsxmDm(sfzrdmxxxVO.getZsxmDm(),
                                        nsrzgxxVO.getNsrlx(), sfzrdmxxxVO.getNsqxDm(), jbxxmxsjVO.getFjmqybz(),
                                        jbxxmxsjVO.getQysdszsfsDm());
                                    if (GyUtils.isNull(yzpzzlDm)) {
                                        //判断是否为水利，水利的应征凭证通过系统参数获取
                                        if (ZsxmDmEnum.SLJSJJ.getCode().equals(sfzrdmxxxVO.getZsxmDm())){
                                            //默认是非税通用申报，通过配置判断是否为通用申报
                                            yzpzzlDm = FSSR_TYSB.getDm();
                                            Map<String, Object> cacheMap = CacheUtils.getTableData("cs_znsb_sbrwsljssj_yzpzzldm",xzqhszDm);
                                            if (!GyUtils.isNull(cacheMap)){
                                                yzpzzlDm = String.valueOf(cacheMap.get("yzpzzlDm"));
                                            }
                                        }else{
                                            continue;
                                        }
                                    }
                                    // 根据配置表判断是否生成该税种的申报任务
                                    if (!this.checkPzbFliter(sfzrdmxxxVO, yzpzzlDm)) {
                                        continue;
                                    }
                                    // 计算属期和申报日期止
                                    final String nsqxDm = sfzrdmxxxVO.getNsqxDm();
                                    final String sbqxDm = sfzrdmxxxVO.getSbqxDm();
                                    final String zsxmDm = sfzrdmxxxVO.getZsxmDm();
                                    // 计算税款所属期
                                    Map<String,Object> skssqmap = CchxwsnssbGyUtils.jsSkssq(nsqxDm,
                                            DateUtil.parse(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"),
                                            CchxwsnssbGyUtils.checkIsQnsb(sbqxDm));
                                    //残保金按年的固定取去年属期起止
                                    if (CJRJYBZJ.getDm().equals(yzpzzlDm) && "10".equals(nsqxDm)){
                                        skssqmap = CchxwsnssbGyUtils.jsSkssq(nsqxDm,
                                                DateUtil.parse(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"),
                                                "N");
                                    }
                                    final String skssqq = DateUtil.format((Date)skssqmap.get("skssqq"), "yyyy-MM-dd");
                                    final String skssqz = DateUtil.format((Date)skssqmap.get("skssqz"), "yyyy-MM-dd");
                                    //校验属期是否在税费种认定有效期起止范围内
                                    if (!this.checkSksssq(DateUtils.toDate(skssqq, "yyyy-MM-dd"),DateUtils.toDate(skssqz, "yyyy-MM-dd"),sfzrdmxxxVO)){
                                        continue;
                                    }
                                    final Calendar calendarMrskssqq = Calendar.getInstance();
                                    calendarMrskssqq.setTime(DateUtils.toDate(skssqq, "yyyy-MM-dd"));
                                    final Calendar calendarMrskssqz = Calendar.getInstance();
                                    calendarMrskssqz.setTime(DateUtils.toDate(skssqz, "yyyy-MM-dd"));
                                    final String sbqx = CchxwsnssbGyUtils.getSbqx(swjgDmList, sbqxDm, nsqxDm, zsxmDm,
                                        calendarMrskssqq, calendarMrskssqz, sbqxwbGroup, jjrGroup, zqtzGroup);
                                    // 先查询申报任务中是否已经存在该任务，已存在的不处理，不存在的进行插入
                                    List<ZnsbNssbSbrwDO> queryList = this.getBaseMapper().selectListOne(djxh, nsrsbh,
                                        yzpzzlDm, zsxmDm, skssqq, skssqz, nsqxDm);
                                    if (!queryList.isEmpty()) {
                                        continue;
                                    }
                                    String sbny = DateUtils.getSystemCurrentTime(17);
                                    String nsrsbztDm = SbrwztConstants.SBZT_WSB_DM;
                                    String rwztDm = SbrwztConstants.RWZT_WSB_DM;
                                    //如果为年报，将申报年月更新为当前月份,只针对残保金，其他的当月才会生成任务
                                    if ("10".equals(nsqxDm) && CJRJYBZJ.getDm().equals(yzpzzlDm)){
                                        //判断是否已申报，已申报不处理，未申报的更新或插入
                                        //调用乐企申报情况查询接口判断是否已申报
                                        SjjhDTO sjjhDTO = new SjjhDTO();
                                        sjjhDTO.setSjjhlxDm("SBRWZT0002");
                                        sjjhDTO.setYwbm("SBRWZT-LQ");
                                        sjjhDTO.setYwuuid(GyUtils.getUuid());
                                        sjjhDTO.setDjxh(djxh);
                                        sjjhDTO.setNsrsbh(nsrsbh);
                                        sjjhDTO.setXzqhszDm(xzqhszDm);
                                        //组装乐企报文
                                        SbqkcxNewReqDTO sbqkcxReqDTO = new SbqkcxNewReqDTO();
                                        sbqkcxReqDTO.setDjxh(djxh);
                                        sbqkcxReqDTO.setSkssqq(skssqq);
                                        sbqkcxReqDTO.setSkssqz(skssqz);
                                        sbqkcxReqDTO.setYzpzzlDm(yzpzzlDm);
                                        sbqkcxReqDTO.setZsxmDm(zsxmDm);
                                        sjjhDTO.setBwnr(JsonUtils.toJson(sbqkcxReqDTO));
                                        CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
                                        if (result.getCode() != 1){
                                            log.info("查询申报情况返回错误：{}",result.getMsg());
                                            continue;
                                        }
                                        SbqkcxRespDTO sbqkcxRespDTO = JsonUtils.toBean(String.valueOf(result.getData()), SbqkcxRespDTO.class);
                                        if ("00".equals(sbqkcxRespDTO.getReturncode())){
                                            if (!GyUtils.isNull(sbqkcxRespDTO.getSbxxList())){
                                                //已申报
                                                if (GyUtils.isNull(queryList)){
                                                    //写入历史申报任务数据
                                                    sbny = this.getSbnyBySbrq(DateUtils.dateToString(sbqkcxRespDTO.getSbxxList().get(0).getSbrq1(),3));
                                                    nsrsbztDm = SbrwztConstants.SBZT_SBCG_DM;
                                                    rwztDm = SbrwztConstants.RWZT_YSB_DM;
                                                }else{
                                                    //不做处理跳过
                                                    continue;
                                                }
                                            }else{
                                                //未申报
                                                //如果已存在 需更新申报年月为当月
                                                if (!GyUtils.isNull(queryList)){
                                                    for (ZnsbNssbSbrwDO sbrwDO:queryList){
                                                        sbrwDO.setSbny(DateUtils.getSystemCurrentTime(17));
                                                        this.getBaseMapper().updateById(sbrwDO);
                                                    }
                                                    continue;
                                                }
                                            }
                                        }else {
                                            continue;
                                        }
                                    }
                                    // 校验申报期限为当月的才会生成申报任务（不校验年报，年报更新申报年月为当前月份,残保金年报1）
                                    if (!this.checkSbqx(sbqx)) {
                                        if (!(CJRJYBZJ.getDm().equals(yzpzzlDm) && "10".equals(nsqxDm))){
                                            continue;
                                        }
                                    }
                                    //校验汇总纳税企业认定申请
                                    if (this.checkHznsqyrd(djxh,yzpzzlDm)){
                                        continue;
                                    }
                                    //校验企业所得税备案信息
                                    if (this.checkQysdsbaxx(djxh,skssqq,skssqz,yzpzzlDm)){
                                        continue;
                                    }
                                    //校验利润中心增值税汇总申报标志为“Y”,且企业税号与增值税汇总企业税号不同的企业
                                    if (this.checkLrzx(nsrsbh,zsxmDm)){
                                        continue;
                                    }
                                    final ZnsbNssbSbrwDO znsbNssbSbrwDO = new ZnsbNssbSbrwDO();
                                    znsbNssbSbrwDO.setSbrwuuid(GyUtils.getUuid());
                                    znsbNssbSbrwDO.setDjxh(djxh);
                                    znsbNssbSbrwDO.setNsrsbh(nsrsbh);
                                    znsbNssbSbrwDO.setNsrmc(jbxxmxsjVO.getNsrmc());
                                    znsbNssbSbrwDO.setXzqhszDm(companyBasicInfoDTO.getXzqhszDm());
                                    znsbNssbSbrwDO.setYzpzzlDm(yzpzzlDm);
                                    znsbNssbSbrwDO.setZsxmDm(zsxmDm);
                                    //通用申报需要增加写入征收品目
                                    if (yzpzzlDm.equals(TYSB.getDm())){
                                        znsbNssbSbrwDO.setZspmDm(sfzrdmxxxVO.getZspmDm());
                                    }
                                    znsbNssbSbrwDO.setNsqxDm(nsqxDm);
                                    znsbNssbSbrwDO.setSkssqq(DateUtils.toDate(skssqq, "yyyy-MM-dd"));
                                    znsbNssbSbrwDO.setSkssqz(DateUtils.toDate(skssqz, "yyyy-MM-dd"));
                                    znsbNssbSbrwDO.setSbqx(DateUtils.toDate(sbqx, "yyyy-MM-dd"));
                                    znsbNssbSbrwDO.setSbny(sbny);
                                    znsbNssbSbrwDO.setNsrsbztDm(nsrsbztDm);
                                    znsbNssbSbrwDO.setRwztDm(rwztDm);
                                    znsbNssbSbrwDO.setRwlxDm(matchRwlxDmByYzpzzlDm(yzpzzlDm));
                                    znsbNssbSbrwDO.setBsy(bsymc);
                                    znsbNssbSbrwDO.setQydmz(companyBasicInfoDTO.getQydmz());
                                    znsbNssbSbrwDO.setJguuid(companyBasicInfoDTO.getJguuid());
                                    this.save(znsbNssbSbrwDO);
                                }

                            }

                        }
                    }
                } catch (Exception e) {
                    log.error("", e);
                    log.info("处理当前企业生成申报任务报错,djxh：{},报错信息：{}", djxh, e.getMessage());
                }

            }
        }
    }

    private String getSbnyBySbrq(String ymdDate) {
        // 定义输入格式的 DateTimeFormatter
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 定义输出格式的 DateTimeFormatter
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyyMM");

        // 解析输入字符串为 LocalDate 对象
        LocalDate date = LocalDate.parse(ymdDate, inputFormatter);
        // 格式化为目标字符串
        return date.format(outputFormatter);
    }

    private boolean checkSksssq(Date skssqq, Date skssqz, SfzrdmxxxVO sfzrdmxxxVO) {
        //属期需在认定有效期起止范围内
        return DateUtils.diff(skssqq, sfzrdmxxxVO.getRdyxqq()) >= 0 && DateUtils.diff(skssqz, sfzrdmxxxVO.getRdyxqz()) <= 0;
    }

    private String getNowDate15(){
        // 1. 定义格式化器：指定输出格式为 yyyy-MM-dd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 2. 获取当前日期 → 调整“日”为15（若当前月无15日？不可能，所有月份都有15日）
        LocalDate currentMonth15th = LocalDate.now()
                .withDayOfMonth(15); // 核心：将“日”设为15

        // 3. 格式化并输出
        return currentMonth15th.format(formatter);
    }

    private boolean checkLrzx(String nsrsbh,String zsxmDm) {
        if (ZsxmDmEnum.ZZS.getCode().equals(zsxmDm)){
            List<ZnsbTzzxLrzxdzbDO> lrzxDOList = znsbTzzxLrzxdzbMapper.zzsSbrwCheck(nsrsbh);
            return !GyUtils.isNull(lrzxDOList);
        }
        return false;
    }

    private boolean checkQysdsbaxx(String djxh, String skssqq, String skssqz, String yzpzzlDm) {
        if (!YzpzzlEnum.QYSDSYJDA.getDm().equals(yzpzzlDm)){
            return false;
        }
        List<ZnsbNssbQysdsbaxxDO> baxxList = znsbNssbQysdsbaxxService.queryQysdsbaxx(djxh,skssqq,skssqz);
        if (GyUtils.isNull(baxxList)){
            return false;
        }
        //就地缴纳标识为N不生成申报任务(存在为空的情况，如果为空默认为N同样不生成申报任务)
        return GyUtils.isNull(baxxList.get(0).getJdjnbs()) || "N".equals(baxxList.get(0).getJdjnbs());
    }

    private boolean checkHznsqyrd(String djxh, String yzpzzlDm) {
        boolean result = false;
        //判断增值税一般人时，查询汇总纳税企业认定信息校验是否生成申报任务
        if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(yzpzzlDm)){
            List<RdHznsqyrdsqMxbDO> rdList = rdHznsqyrdsqMxbMapper.checkSbrwInit(djxh,DateUtils.lastMonthLastDay());
            if (!GyUtils.isNull(rdList)){
                result = true;
            }
        }
        return result;
    }

    private boolean checkSbqx(String sbqx) {
        return DateUtils.getSystemCurrentTime(17)
            .equals(DateUtils.dateToString(DateUtils.toDate(sbqx, "yyyy-MM-dd"), 17));
    }

    private boolean checkZsxmDmFliter(SfzrdmxxxVO sfzrdmxxxVO, NsrzgxxVO nsrzgxxVO) {
        // 只判断增值税一般人10101和印花税10111
        // 1，3，4为一般人，否则为小规模
        final String nsrlx = nsrzgxxVO.getNsrlx();
        //只根据征收代理方式代码为0的税费种认定加工申报任务
        if (!"0".equals(sfzrdmxxxVO.getZsdlfsDm())){
            return false;
        }
        //增值税
        /*if ("10101".equals(sfzrdmxxxVO.getZsxmDm())){
            return true;
        }
        //印花税
        if ("10111".equals(sfzrdmxxxVO.getZsxmDm())){
            return true;
        }
        //企业所得税
        if ("10104".equals(sfzrdmxxxVO.getZsxmDm())){
            return true;
        }
        //通用申报（水利）
        if ("30221".equals(sfzrdmxxxVO.getZsxmDm())){
            return true;
        }
        //通用申报（工会经费）
        if ("39900".equals(sfzrdmxxxVO.getZsxmDm())){
            return true;
        }
        //非税通用申报（城镇垃圾处理费）
        if ("30433".equals(sfzrdmxxxVO.getZsxmDm())){
            return true;
        }
        //残疾人就业保障金
        if ("30218".equals(sfzrdmxxxVO.getZsxmDm())){
            return true;
        }*/
        /*if ("1".equals(nsrlx) || "3".equals(nsrlx) || "4".equals(nsrlx)) {
            return "10101".equals(sfzrdmxxxVO.getZsxmDm()) || "10111".equals(sfzrdmxxxVO.getZsxmDm());
        }*/
        return true;
    }

    private boolean checkPzbFliter(SfzrdmxxxVO sfzrdmxxxVO, String yzpzzlDm){
        final List<Map<String,Object>> pzbList = CacheUtils.getTableData("cs_znsb_sbrwbyrdxx_pz");
        if (!GyUtils.isNull(pzbList)){
            for (Map<String,Object> pzbmap:pzbList){
                if (MapUtil.getStr(pzbmap,"yzpzzlDm").equals(yzpzzlDm) && MapUtil.getStr(pzbmap,"zsxmDm").equals(sfzrdmxxxVO.getZsxmDm())){
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkYbr(NsrzgxxVO nsrzgxxVO) {
        // 1，3，4为一般人，否则为小规模
        final String nsrlx = nsrzgxxVO.getNsrlx();
        return "1".equals(nsrlx) || "3".equals(nsrlx) || "4".equals(nsrlx);
    }

    @Override
    public SbrwmxAbbPlsbRespDTO sbrwmxAbbPlsb(SbrwmxAbbPlsbReqDTO sbrwmxAbbPlsbReqDTO) {
        SbrwmxAbbPlsbRespDTO respDTO = new SbrwmxAbbPlsbRespDTO();
        log.info("批量申报请求入参：{}", sbrwmxAbbPlsbReqDTO.toString());
        //TODO 暂时去掉全选判断，后续修改实现
//        sbrwmxAbbPlsbReqDTO.setCheckAllFlag(false);
        // 根据全选标记判断是否全选操作，组装申报任务uuid列表
        List<String> sbrwuuidList = new ArrayList<>();
        if (sbrwmxAbbPlsbReqDTO.isCheckAllFlag()) {
            // 根据查询条件重新查询数据
            Map<String, Object> cxFormMap = sbrwmxAbbPlsbReqDTO.getCxForm();
            final String sbzt = MapUtil.getStr(cxFormMap, "sbzt");
            final String qysh = MapUtil.getStr(cxFormMap, "qysh");
            final String bsy = MapUtil.getStr(cxFormMap, "bsy");
            final List<String> djxhList = (List<String>)cxFormMap.get("djxhList");
            final SimpleQueryReq queryReq = new SimpleQueryReq();
            this.getQueryPath(queryReq,sbrwmxAbbPlsbReqDTO);
            final Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("djxhList", GyUtils.isNull(djxhList)? ZnsbSessionUtils.getKczDjxhList():djxhList);
            // 只查询未申报的数据
            paramsMap.put("rwztDm", "01");
            // 纳税人识别号模糊查询
            StringBuilder nsrsbhStr = new StringBuilder();
            nsrsbhStr.append("%");
            nsrsbhStr.append(GyUtils.isNull(qysh) ? "" : qysh);
            nsrsbhStr.append("%");
            paramsMap.put("nsrsbh", nsrsbhStr);
            StringBuilder bsyStr = new StringBuilder();
            bsyStr.append("%");
            bsyStr.append(GyUtils.isNull(bsy) ? "" : bsy);
            bsyStr.append("%");
            paramsMap.put("bsy", bsyStr);
            //不动产权证号
            StringBuilder bdcqzhStr = new StringBuilder();
            bdcqzhStr.append("%");
            bdcqzhStr.append(GyUtils.isNull(sbrwmxAbbPlsbReqDTO.getBdcqzh()) ? "" : sbrwmxAbbPlsbReqDTO.getBdcqzh());
            bdcqzhStr.append("%");
            paramsMap.put("bdcqzh", bdcqzhStr);
            paramsMap.put("xzqhszDm",sbrwmxAbbPlsbReqDTO.getXzqhszDm());
            //土地编号
            StringBuilder tdbhhStr = new StringBuilder();
            tdbhhStr.append("%");
            tdbhhStr.append(GyUtils.isNull(sbrwmxAbbPlsbReqDTO.getTdbh()) ? "" : sbrwmxAbbPlsbReqDTO.getTdbh());
            tdbhhStr.append("%");
            paramsMap.put("tdbh", tdbhhStr);
            paramsMap.put("sbny",sbrwmxAbbPlsbReqDTO.getSbny().replaceAll("-", ""));
            paramsMap.put("fydz",sbrwmxAbbPlsbReqDTO.getFydz());
            queryReq.setParams(paramsMap);
            final List<Map<String, Object>> sbrwmxResult = queryApi.queryList(queryReq);
            if (!GyUtils.isNull(sbrwmxResult)) {
                final List<String> cxList = sbrwmxResult.stream()
                        .filter(t -> "Y".equals(GyUtils.cast2Str(t.get("ksbbz"))))
                        .map(t -> MapUtil.getStr(t, "sbrwuuid"))
                        .collect(Collectors.toList());
                sbrwuuidList.addAll(cxList);
            }
        } else {
            sbrwuuidList = sbrwmxAbbPlsbReqDTO.getSbrwuuidList();
        }
        // 根据申报任务uuid列表查询申报任务状态判断是否可申报
        boolean ksbCheck = true;
        for (String sbrwuuid : sbrwuuidList) {
            ZnsbNssbSbrwDO znsbNssbSbrwDO = this.getBaseMapper().querySbrwBySbrwuuid(sbrwuuid);
            if (!this.checkNsrsbzt(znsbNssbSbrwDO.getNsrsbztDm())) {
                ksbCheck = false;
            }
        }
        if (ksbCheck) {
            // 调用批量申报接口
            // 增值税一般纳税人
            if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                    && ZsxmDmEnum.ZZS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())) {
                this.updateSbzBySbrwuuidList(sbrwuuidList);
                zzsybnsrPlsbService.plsb(sbrwuuidList, sbrwmxAbbPlsbReqDTO.getPljkbz());
            }
            //增值税小规模纳税人
            if (YzpzzlEnum.ZZS_XGMNSR.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                    && ZsxmDmEnum.ZZS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())) {
                this.updateSbzBySbrwuuidList(sbrwuuidList);
                zzsxmgnsrPlsbService.plsb(sbrwuuidList);
            }
            //财行税统一接口
            if (YzpzzlEnum.CXS.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())) {
                this.updateSbzBySbrwuuidList(sbrwuuidList);
                cxsPlsbService.plsb(sbrwuuidList);
            }
            //企业所得税预缴批量申报
            if (YzpzzlEnum.QYSDSYJDA.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())){
                this.updateSbzBySbrwuuidList(sbrwuuidList);
                qysdsyjPlsbService.plsb(sbrwuuidList);
            }
            //增值税预缴
            if (YzpzzlEnum.ZZS_YJSB.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                    && ZsxmDmEnum.ZZS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())) {
                this.updateSbzBySbrwuuidList(sbrwuuidList);
                zzsyjPlsbService.plsb(sbrwuuidList);
            }
        } else {
            respDTO.setCgbz("N");
            respDTO.setMsg("勾选的申报任务中存在不可提交申报的任务，请检查勾选数据后重新操作！");
            return respDTO;
        }
        respDTO.setCgbz("Y");
        respDTO.setMsg("批量申报提交成功，请稍后查询申报状态！");
        return respDTO;
    }

    private void getQueryPath(SimpleQueryReq queryReq, SbrwmxAbbPlsbReqDTO sbrwmxAbbPlsbReqDTO) {
        // 增值税一般纳税人
        if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.ZZS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())){
            queryReq.setPath("/sb/sbrw/querySbrwmxForZzsybnsr");
        }
        // 增值税小规模纳税人
        if (YzpzzlEnum.ZZS_XGMNSR.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.ZZS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())){
            queryReq.setPath("/sb/sbrw/querySbrwmxForZzsxgmnsr");
        }
        // 增值税预缴
        if (YzpzzlEnum.ZZS_YJSB.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.ZZS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())){
            queryReq.setPath("/sb/sbrw/querySbrwmxForZzsyj");
        }
        //印花税
        if (YzpzzlEnum.CXS.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.YHS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())){
            queryReq.setPath("/sb/sbrw/querySbrwmxForYhs");
        }
        //房产税
        if (YzpzzlEnum.CXS.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.FCS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())){
            queryReq.setPath("/sb/sbrw/querySbrwmxForFcs");
        }
        //城镇土地使用税
        if (YzpzzlEnum.CXS.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.CZTDSYS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())){
            queryReq.setPath("/sb/sbrw/querySbrwmxForCztdsys");
        }
        //企业所得税预缴
        if (YzpzzlEnum.QYSDSCZZS.getDm().equals(sbrwmxAbbPlsbReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.QYSDS.getCode().equals(sbrwmxAbbPlsbReqDTO.getZsxmDm())){
            queryReq.setPath("/sb/sbrw/querySbrwmxForQysdsyj");
        }

    }

    private void updateSbzBySbrwuuidList(List<String> sbrwuuidList) {
        // 先将申报任务状态更新为申报中
        for (String sbrwuuid : sbrwuuidList) {
            ZnsbNssbSbrwDO znsbNssbSbrwDO = this.getBaseMapper().querySbrwBySbrwuuid(sbrwuuid);
            znsbNssbSbrwDO.setBsy(ZnsbSessionUtils.getZsxm());
            znsbNssbSbrwDO.setNsrsbztDm(SBZT_SBZ_DM);
            this.getBaseMapper().updateById(znsbNssbSbrwDO);
        }
    }

    private boolean checkNsrsbzt(String nsrsbztDm) {
        boolean result = false;
        if (SBZT_WSB_DM.equals(nsrsbztDm) || SBZT_SHBTG_DM.equals(nsrsbztDm) || SBZT_SBSB_DM.equals(nsrsbztDm)
            || SBZT_BDBTG_DM.equals(nsrsbztDm) || SBZT_XTERROR_DM.equals(nsrsbztDm) || SBZT_ZF_ZFCG_DM.equals(nsrsbztDm)
            || SBZT_ZLBS_WBS_DM.equals(nsrsbztDm) || SBZT_ZLBS_SHBTG_DM.equals(nsrsbztDm)
            || SBZT_ZLBS_BSSB_DM.equals(nsrsbztDm) || SBZT_ZLBS_SJSH_BSSB_DM.equals(nsrsbztDm)) {
            result = true;
        }

        return result;
    }

    @Override
    public boolean updateBsrxm(String sbrwuuid, String bsrxm) {
        final ZnsbNssbSbrwDO sbrwDO = new ZnsbNssbSbrwDO();
        sbrwDO.setSbrwuuid(sbrwuuid);
        sbrwDO.setBsy(bsrxm);
        return 1 == this.getBaseMapper().updateById(sbrwDO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InitYsSbrwRespDTO initYsSbrw(InitYsSbrwReqDTO initYsSbrwReqDTO) {
        InitYsSbrwRespDTO initYsSbrwRespDTO = new InitYsSbrwRespDTO();
        String code = "00";
        String msg = "成功";
        try {
            // 获取相关配置
            final List<Map<String, Object>> sbqxwbAll = FtsCxsUtils.getAllCacheData("cs_gy_sbqxwh_sbqx");// 申报期限维护表，700条左右
            final List<Map<String, Object>> jjrAll = FtsCxsUtils.getAllCacheData("cs_gy_jjr");// 节假日配置表，4000条左右
            final List<Map<String, Object>> zqtzAll = FtsCxsUtils.getAllCacheData("cs_gy_zqtz");// 征期调整配置表。50条
            final Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup =
                sbqxwbAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                    .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
            final Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup =
                jjrAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                    .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
            final Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup =
                zqtzAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                    .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
            final String djxh = initYsSbrwReqDTO.getDjxh();
            final String nsrsbh = initYsSbrwReqDTO.getNsrsbh();
            // 调用办税员列表获取办税员
            String bsymc = StringUtils.EMPTY;
            DjxhReqVO bsyreqVO = new DjxhReqVO();
            bsyreqVO.setDjxh(djxh);
            CommonResult<List<RyxxVO>> ryxxResult = companyApi.getBsyByQy(bsyreqVO);
            if (!GyUtils.isNull(ryxxResult.getData())) {
                List<RyxxVO> ryxxList = ryxxResult.getData();
                if (!GyUtils.isNull(ryxxList)) {
                    bsymc = ryxxList.get(0).getZsxm1();
                }
            }
            // 调用纳税人信息接口查询认定信息
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
            znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            if (!GyUtils.isNull(nsrxxRes.getData())) {
                ZnsbMhzcQyjbxxmxResVO znsbMhzcQyjbxxmxResVO = nsrxxRes.getData();
                List<SfzrdmxxxVO> sfzrdmxxxList = znsbMhzcQyjbxxmxResVO.getSfzrdmxxx();
                // 纳税人基本信息
                List<JbxxmxsjVO> jbxxmxsj = znsbMhzcQyjbxxmxResVO.getJbxxmxsj();
                JbxxmxsjVO jbxxmxsjVO = new JbxxmxsjVO();
                if (!GyUtils.isNull(jbxxmxsj)) {
                    jbxxmxsjVO = jbxxmxsj.get(0);
                }
                // 纳税人资格信息
                List<NsrzgxxVO> nsrzgxx = znsbMhzcQyjbxxmxResVO.getNsrzgxx();
                NsrzgxxVO nsrzgxxVO = new NsrzgxxVO();
                if (!GyUtils.isNull(nsrzgxx)) {
                    nsrzgxxVO = nsrzgxx.get(0);
                }

                // 取主管税务所科分局代码
                final List<String> swjgDmList = new ArrayList<>();
                FtsCxsUtils.getSwjgList(swjgDmList, jbxxmxsjVO.getZgswskfjDm(), new HashMap<>());

                // 查询企业信息的行政区划数字代码
                CompanyBasicInfoDTO companyBasicInfoDTO = new CompanyBasicInfoDTO();
                CommonResult<CompanyBasicInfoDTO> companyBasicInfoDTOCommonResult = companyApi.basicInfo(djxh, nsrsbh);
                if (!GyUtils.isNull(companyBasicInfoDTOCommonResult.getData())) {
                    companyBasicInfoDTO = companyBasicInfoDTOCommonResult.getData();
                }

                List<ZnsbNssbSbrwDTO> list = this.getZdySbrwLb(nsrzgxxVO);
                if (!GyUtils.isNull(list)) {
                    for (ZnsbNssbSbrwDTO listvo : list) {
                        // 先根据登记序号删除申报任务再生成
                        znsbNssbSbrwMapper.deleteYssbrw(djxh, listvo.getYzpzzlDm());
                        // 计算属期和申报日期止
                        final String nsqxDm = listvo.getNsqxDm();
                        final String sbqxDm = sfzrdmxxxList.get(0).getSbqxDm();
                        final String zsxmDm = listvo.getZsxmDm();
                        // 计算税款所属期
                        final Map<String,
                            Object> skssqmap = CchxwsnssbGyUtils.jsSkssq(nsqxDm,
                                DateUtil.parse(initYsSbrwReqDTO.getSsrq(), "yyyy-MM-dd"),
                                CchxwsnssbGyUtils.checkIsQnsb(sbqxDm));
                        final String skssqq = DateUtil.format((Date)skssqmap.get("skssqq"), "yyyy-MM-dd");
                        final String skssqz = DateUtil.format((Date)skssqmap.get("skssqz"), "yyyy-MM-dd");
                        final Calendar calendarMrskssqq = Calendar.getInstance();
                        calendarMrskssqq.setTime(DateUtils.toDate(skssqq, "yyyy-MM-dd"));
                        final Calendar calendarMrskssqz = Calendar.getInstance();
                        calendarMrskssqz.setTime(DateUtils.toDate(skssqz, "yyyy-MM-dd"));
                        final String sbqx = CchxwsnssbGyUtils.getSbqx(swjgDmList, sbqxDm, nsqxDm, zsxmDm,
                            calendarMrskssqq, calendarMrskssqz, sbqxwbGroup, jjrGroup, zqtzGroup);
                        final ZnsbNssbSbrwDO znsbNssbSbrwDO = new ZnsbNssbSbrwDO();
                        String sbrwuuid = GyUtils.getUuid();
                        znsbNssbSbrwDO.setSbrwuuid(sbrwuuid);
                        znsbNssbSbrwDO.setDjxh(djxh);
                        znsbNssbSbrwDO.setNsrsbh(nsrsbh);
                        znsbNssbSbrwDO.setNsrmc(jbxxmxsjVO.getNsrmc());
                        znsbNssbSbrwDO.setXzqhszDm(companyBasicInfoDTO.getXzqhszDm());
                        znsbNssbSbrwDO.setYzpzzlDm(listvo.getYzpzzlDm());
                        znsbNssbSbrwDO.setZsxmDm(zsxmDm);
                        znsbNssbSbrwDO.setNsqxDm(nsqxDm);
                        znsbNssbSbrwDO.setSkssqq(DateUtils.toDate(skssqq, "yyyy-MM-dd"));
                        znsbNssbSbrwDO.setSkssqz(DateUtils.toDate(skssqz, "yyyy-MM-dd"));
                        znsbNssbSbrwDO.setSbqx(DateUtils.toDate(sbqx, "yyyy-MM-dd"));
                        znsbNssbSbrwDO.setSbny(DateUtils.getSystemCurrentTime(17));
                        znsbNssbSbrwDO.setNsrsbztDm(SbrwztConstants.SBZT_WSB_DM);
                        znsbNssbSbrwDO.setRwztDm(SbrwztConstants.RWZT_WSB_DM);
                        znsbNssbSbrwDO.setRwlxDm(listvo.getRwlxDm());
                        znsbNssbSbrwDO.setBsy(bsymc);
                        if (YzpzzlEnum.ZZS_YJSB.getDm().equals(listvo.getYzpzzlDm())){
                            znsbNssbSbrwDO.setFybh("F31011220190021614");
                        }
                        znsbNssbSbrwDO.setSjgsdq(companyBasicInfoDTO.getZgswskfjDm());
                        znsbNssbSbrwDO.setSjcsdq(companyBasicInfoDTO.getZgswskfjDm());
                        this.save(znsbNssbSbrwDO);
                        // 生成qcxx表数据
                        ZnsbNssbQcxxDTO qcxxDTO =
                            this.getYsQcxx(sbrwuuid, djxh, listvo.getYzpzzlDm(), DateUtils.toDate(skssqq, "yyyy-MM-dd"),
                                DateUtils.toDate(skssqz, "yyyy-MM-dd"), nsrsbh, jbxxmxsjVO.getNsrmc());
                        znsbNssbQcxxMapper.insert(BeanUtils.toBean(qcxxDTO, ZnsbNssbQcxxDO.class));
                    }
                }
                //插入一条财务报表任务
                Map<String, String> ydssqMap = getLastMonthRange();
                ZnsbNssbSbrwCwbbDO znsbNssbSbrwCwbbDO = new ZnsbNssbSbrwCwbbDO();
                znsbNssbSbrwCwbbDO.setSbrwuuid(GyUtils.getUuid());
                znsbNssbSbrwCwbbDO.setYzpzzlDm(YzpzzlEnum.CWBBBS.getDm());
                znsbNssbSbrwCwbbDO.setYzpzzlmc(YzpzzlEnum.CWBBBS.getMc());
                znsbNssbSbrwCwbbDO.setSkssqq(DateUtils.toLocalDateTime(ydssqMap.get("skssqq"),"yyyy-MM-dd"));
                znsbNssbSbrwCwbbDO.setSkssqz(DateUtils.toLocalDateTime(ydssqMap.get("skssqz"),"yyyy-MM-dd"));
                znsbNssbSbrwCwbbDO.setNsqxDm("06");
                // 申报期限口径：先默认取当月最后一天
                znsbNssbSbrwCwbbDO.setSbqx(DateUtils.toLocalDateTime(getNowMonthLastDay(),"yyyy-MM-dd"));
                znsbNssbSbrwCwbbDO.setBsy(bsymc);
                znsbNssbSbrwCwbbDO.setRwztDm(SbrwztConstants.RWZT_WBS_DM);
                znsbNssbSbrwCwbbDO.setNsrsbztDm(SbrwztConstants.SBZT_ZLBS_WBS_DM);
                znsbNssbSbrwCwbbDO.setJguuid1(companyBasicInfoDTO.getJguuid());
                znsbNssbSbrwCwbbDO.setLrrq(DateUtils.toLocalDateTime(DateUtils.getSystemCurrentTime()));
                znsbNssbSbrwCwbbDO.setXgrq(DateUtils.toLocalDateTime(DateUtils.getSystemCurrentTime()));
                znsbNssbSbrwCwbbDO.setCwkjzdbauuid("");
                znsbNssbSbrwCwbbDO.setDjxh(companyBasicInfoDTO.getDjxh());
                znsbNssbSbrwCwbbDO.setNsrsbh(companyBasicInfoDTO.getNsrsbh());
                znsbNssbSbrwCwbbDO.setSbny(DateUtils.dateToString(new Date(),17));
                znsbNssbSbrwCwbbDO.setXzqhszDm(companyBasicInfoDTO.getXzqhszDm());
                znsbNssbSbrwCwbbDO.setZlxlDm("ZL1001001");
                znsbNssbSbrwCwbbMapper.insert(znsbNssbSbrwCwbbDO);
            }
        } catch (Exception e) {
            code = "01";
            msg = "失败！";
            if (!GyUtils.isNull(e)) {
                msg = e.getMessage();
            }
            log.error("", e);
        } finally {
            initYsSbrwRespDTO.setCode(code);
            initYsSbrwRespDTO.setMsg(msg);
        }
        return initYsSbrwRespDTO;
    }

    private String getNowMonthLastDay(){
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 获取当月的最后一天
        LocalDate lastDayOfMonth = currentDate.with(TemporalAdjusters.lastDayOfMonth());

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 格式化为字符串
        return lastDayOfMonth.format(formatter);
    }

    private Map<String, String> getLastMonthRange() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取上个月的第一天
        LocalDate firstDayOfLastMonth = currentDate.minusMonths(1).withDayOfMonth(1);
        // 获取上个月的最后一天
        LocalDate lastDayOfLastMonth = currentDate.minusMonths(1).withDayOfMonth(currentDate.minusMonths(1).lengthOfMonth());

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 创建结果Map
        Map<String, String> result = new HashMap<>();
        result.put("skssqq", firstDayOfLastMonth.format(formatter));
        result.put("skssqz", lastDayOfLastMonth.format(formatter));

        return result;
    }

    @Override
    public UpdateSbrwJkztRespDTO updateSbrwJkzt(UpdateSbrwJkztReqDTO updateSbrwJkztReqDTO) {
        UpdateSbrwJkztRespDTO updateSbrwJkztRespDTO = new UpdateSbrwJkztRespDTO();
        final String djxh = updateSbrwJkztReqDTO.getDjxh();
        final String pzxh = updateSbrwJkztReqDTO.getPzxh();
        String code = "00";
        String msg = "";
        try {
            BigDecimal hjjeNum = znsbSkjnService.queryYjkje(djxh, pzxh);//已缴款金额
            log.info(" hjjeNum: " + hjjeNum);
            if (hjjeNum.compareTo(BigDecimal.ZERO) == 0){
                hjjeNum = jksmxService.queryYjkje(djxh, pzxh);
            }
            log.info(" hjjeNum: " + hjjeNum);
            log.info("updateSbrwJkzt 入参 登记序号：" + djxh + " 凭证序号：" + pzxh);
            // 根据凭证序号查询申报任务表数据
            List<ZnsbNssbSbrwDO> sbrwDOList = this.getBaseMapper().querySbrwListByPzxh(pzxh);
            log.info("sbrwDOList: " + sbrwDOList);
            for (ZnsbNssbSbrwDO sbrwDO : sbrwDOList){
                BigDecimal sbje = sbrwDO.getYbtse();
                log.info(" 查询到已缴款合计金额 " + hjjeNum + " 申报任务应补退税额 " + sbje);
                // 如果未查到不处理
                if (!GyUtils.isNull(sbrwDO)) {
                    // 未缴款
                    String jkztDm = JKZT_WJK_DM;
                    // 判断缴款状态
                    if (sbje.compareTo(BigDecimal.ZERO) == 0) {
                        // 无需缴款
                        jkztDm = JKZT_WXJK_DM;
                        log.info("无需缴款 djxh: "+djxh+"pzxh: "+pzxh+" 申报金额："+sbje);
                    } else {
                        if (sbje.compareTo(hjjeNum) > 0 && hjjeNum.compareTo(BigDecimal.ZERO) > 0) {
                            // 部分缴款
                            jkztDm = JKZT_BFJK_DM;
                            log.info("部分缴款 djxh: "+djxh+"pzxh: "+pzxh+" 申报金额："+sbje+" 已缴款金额："+hjjeNum);
                        } else if(hjjeNum.compareTo(sbje) >= 0){
                            // 已缴款
                            jkztDm = JKZT_YJK_DM;
                            log.info("已缴款 djxh: "+djxh+"pzxh: "+pzxh+" 申报金额："+sbje+" 已缴款金额："+hjjeNum);
                        } else {
                            // 未缴款
                            log.info("未缴款 djxh: "+djxh+"pzxh: "+pzxh+" 申报金额："+sbje+" 已缴款金额："+hjjeNum);
                        }
                    }
                    // 更新申报任务
                    sbrwDO.setSbjkztDm(jkztDm);
                    int upNum = this.getBaseMapper().updateById(sbrwDO);
                    if (upNum > 0) {
                        msg = "成功";
                        updateSbrwJkztRespDTO.setSbjkztDm(jkztDm);
                    } else {
                        msg = "失败";
                    }
                } else {
                    msg = "根据凭证序号(" + updateSbrwJkztReqDTO.getPzxh() + ")未查询到申报任务数据。";
                }

            }
        }catch (Exception e) {
            log.error("", e);
            code = "01";
            msg = "失败";
            if (!GyUtils.isNull(e)) {
                msg = e.getMessage();
            }
        } finally {
            updateSbrwJkztRespDTO.setCode(code);
            updateSbrwJkztRespDTO.setMsg(msg);
        }
        return updateSbrwJkztRespDTO;
    }

    @Override
    public String initQcxxBySbrwuuid(InitQcxxBySbrwuuidReqDTO initQcxxBySbrwuuidReqDTO) {
        String res = "";
        try {
            //根据申报任务数据归集期初信息数据
            ZnsbNssbSbrwDTO sbrw = BeanUtils.toBean(this.getBaseMapper().selectById(initQcxxBySbrwuuidReqDTO.getSbrwuuid()),ZnsbNssbSbrwDTO.class);
            if (!GyUtils.isNull(sbrw)){
                SjjhDTO sjjhDTO = new SjjhDTO();
                sjjhDTO.setSjjhlxDm("QCXXGJ0001");
                sjjhDTO.setYwbm(sbrw.getYzpzzlDm());
                sjjhDTO.setYwuuid(sbrw.getSbrwuuid());
                sjjhDTO.setDjxh(sbrw.getDjxh());
                sjjhDTO.setNsrsbh(sbrw.getNsrsbh());
                sjjhDTO.setXzqhszDm(sbrw.getXzqhszDm());
                //传入报文内容的行政区划数字代码
                sjjhDTO.setBwnr(JsonUtils.toJson(initQcxxBySbrwuuidReqDTO));
                //单条调用数据交换，防止报错后中断归集信息
                sjjhService.saveSjjhJob(sjjhDTO);
                res = "成功";
            }else{
                res = "根据申报任务uuid未查询到申报任务";
            }

        }catch (Exception e){
            log.error("", e);
            res = e.getMessage();
        }

        return res;
    }

    @Override
    public void updateSybh(String syuuid, String sybh) {
        this.getBaseMapper()
                .update(new UpdateWrapper<ZnsbNssbSbrwDO>().lambda().eq(ZnsbNssbSbrwDO::getSyuuid, syuuid)
                        .set(ZnsbNssbSbrwDO::getFybh, sybh));
    }

    List<ZnsbNssbSbrwDTO> getZdySbrwLb(NsrzgxxVO nsrzgxxVO) {
        List<ZnsbNssbSbrwDTO> zdyList = new ArrayList<>();
        ZnsbNssbSbrwDTO znsbNssbSbrwDTO;
        // 根据企业类型判断小规模还是一般人
        if (this.checkYbr(nsrzgxxVO)) {
            // 增值税一般人
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.ZZS_YBNSR.getDm());
            znsbNssbSbrwDTO.setZsxmDm(ZsxmDmEnum.ZZS.getCode());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.MONTH.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_HLS_DM);
            zdyList.add(znsbNssbSbrwDTO);

            // 增值税预缴
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.ZZS_YJSB.getDm());
            znsbNssbSbrwDTO.setZsxmDm(ZsxmDmEnum.ZZS.getCode());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.MONTH.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_HLS_DM);
            znsbNssbSbrwDTO.setFybh("F31011220190021612");
            zdyList.add(znsbNssbSbrwDTO);

            // 企业所得税预缴
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.QYSDSCZZS.getDm());
            znsbNssbSbrwDTO.setZsxmDm(ZsxmDmEnum.QYSDS.getCode());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.QUARTER.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_SDS_DM);
            zdyList.add(znsbNssbSbrwDTO);

            // 企业所得税年报
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.QYSDS_NDA.getDm());
            znsbNssbSbrwDTO.setZsxmDm(ZsxmDmEnum.QYSDS.getCode());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.YEAR.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_SDS_DM);
            zdyList.add(znsbNssbSbrwDTO);

            // 千户集团基础涉税事项报送
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.QHJTJCSSSJBS.getDm());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.MONTH.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_ZLBS_DM);
            zdyList.add(znsbNssbSbrwDTO);

            // 税收统计调查
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.CWBB_SSTJDC.getDm());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.YEAR.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_ZLBS_DM);
            zdyList.add(znsbNssbSbrwDTO);

            // 重点税源补充信息采集
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.CWBB_ZDSYBCXX.getDm());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.MONTH.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_ZLBS_DM);
            zdyList.add(znsbNssbSbrwDTO);

        } else {
            // 增值税小规模
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.ZZS_XGMNSR.getDm());
            znsbNssbSbrwDTO.setZsxmDm(ZsxmDmEnum.ZZS.getCode());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.QUARTER.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_HLS_DM);
            zdyList.add(znsbNssbSbrwDTO);

            // 企业所得税预缴
            znsbNssbSbrwDTO = new ZnsbNssbSbrwDTO();
            znsbNssbSbrwDTO.setYzpzzlDm(YzpzzlEnum.QYSDSCZZS.getDm());
            znsbNssbSbrwDTO.setZsxmDm(ZsxmDmEnum.QYSDS.getCode());
            znsbNssbSbrwDTO.setNsqxDm(NsqxEnum.QUARTER.getDm());
            znsbNssbSbrwDTO.setRwlxDm(SbrwztConstants.RWLX_SDS_DM);
            zdyList.add(znsbNssbSbrwDTO);
        }
        return zdyList;
    }

    ZnsbNssbQcxxDTO getYsQcxx(String sbrwuuid, String djxh, String yzpzzlDm, Date skssqq, Date skssqz, String nsrsbh,
        String nsrmc) {
        ZnsbNssbQcxxDTO resDTO = new ZnsbNssbQcxxDTO();
        resDTO.setUuid(GyUtils.getUuid());
        resDTO.setDjxh(djxh);
        resDTO.setSbrwuuid(sbrwuuid);
        resDTO.setYzpzzlDm(yzpzzlDm);
        resDTO.setSkssqq(skssqq);
        resDTO.setSkssqz(skssqz);
        resDTO.setYxbz("Y");
        resDTO.setCwgzbz("N");
        resDTO.setClcgbz("Y");
        // 根据yzpzzlDm判断查询
        if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(yzpzzlDm)) {
            // 增值税一般人
            ZnsbNssbQcxxDO qcxxDO = znsbNssbQcxxMapper.selectById("e2ab6a0b276c4e58ace0244a3ac45245");
            if (!GyUtils.isNull(qcxxDO)) {
                List<YbnsrQcxxItemVO> qcxxbw = JsonUtils.toList(qcxxDO.getBusinessclob(), YbnsrQcxxItemVO.class);
                qcxxbw.get(0).getNsrxx().setDjxh(djxh);
                qcxxbw.get(0).getNsrxx().setNsrsbh(nsrsbh);
                qcxxbw.get(0).getNsrxx().setNsrmc(nsrmc);
                qcxxbw.get(0).getSbsx().setSkssqq(skssqq);
                qcxxbw.get(0).getSbsx().setSkssqz(skssqz);
                resDTO.setBusinessclob(JsonUtils.toJson(qcxxbw));
            }
        }
        if (YzpzzlEnum.QYSDSCZZS.getDm().equals(yzpzzlDm)) {
            // 企业所得税预缴
            ZnsbNssbQcxxDO qcxxDO = znsbNssbQcxxMapper.selectById("bf64f0a188e245b89416edac28cb6d88");
            if (!GyUtils.isNull(qcxxDO)) {
                List<QysdsCzzsYjdQcxxItemVO> qcxxbw =
                    JsonUtils.toList(qcxxDO.getBusinessclob(), QysdsCzzsYjdQcxxItemVO.class);
                qcxxbw.get(0).setDjxh(djxh);
                qcxxbw.get(0).setNsrsbh(nsrsbh);
                qcxxbw.get(0).setNsrmc(nsrmc);
                qcxxbw.get(0).setSkssqq(DateUtils.dateToString(skssqq));
                qcxxbw.get(0).setSkssqz(DateUtils.dateToString(skssqz));
                resDTO.setBusinessclob(JsonUtils.toJson(qcxxbw));
            }

        }
        if (YzpzzlEnum.ZZS_XGMNSR.getDm().equals(yzpzzlDm)) {
            // 增值税小规模
            ZnsbNssbQcxxDO qcxxDO = znsbNssbQcxxMapper.selectById("0c18dca41da44c8e866e0e07240532c1");
            if (!GyUtils.isNull(qcxxDO)) {
                List<XgmnsrQcxxItemVO> qcxxbw = JsonUtils
                    .toList(qcxxDO.getBusinessclob(), XgmnsrQcxxItemVO.class);
                if (!GyUtils.isNull(qcxxbw)){
                    qcxxbw.get(0).getNsrxx().setDjxh(djxh);
                    qcxxbw.get(0).getNsrxx().setNsrsbh(nsrsbh);
                    qcxxbw.get(0).getNsrxx().setNsrmc(nsrmc);
                    qcxxbw.get(0).getSbsx().setSkssqq(skssqq);
                    qcxxbw.get(0).getSbsx().setSkssqz(skssqz);
                    resDTO.setBusinessclob(JsonUtils.toJson(qcxxbw));
                }
            }

        }

        return resDTO;
    }

    @Override
    public void sbrwJkxxtsJob() {
        //扫描申报任务表中，需要提示缴款的申报任务
        List<ZnsbNssbSbrwDO> sbrwList = this.getBaseMapper().queryJktsSbrw();
        if (!GyUtils.isNull(sbrwList)){
            //按登记序号分组发送消息
            Map<String,List<ZnsbNssbSbrwDO>> sbrwGroupDjxhMap = sbrwList.stream()
                    .collect(Collectors.groupingBy(ZnsbNssbSbrwDO::getDjxh));
            //按分组后的企业循环发送消息，相同企业发一条提醒消息，每条消息包括多个报表
            sbrwGroupDjxhMap.forEach((djxh,sbrwGroupList) -> {
                this.getLjktsParamXxzx(sbrwGroupList);
            });

        }
    }

    // 组装申报漏报提醒消息请求报文
    private void getLjktsParamXxzx(List<ZnsbNssbSbrwDO> sbrwList) {
        final List<String> jguuidList = new ArrayList<>();
        jguuidList.add(sbrwList.get(0).getJguuid());
        final StringBuilder bbString = new StringBuilder();
        bbString.append("  \n  ");
        for (int i = 1 ; i <= sbrwList.size(); i++) {
            SbrwAbbqktjVO abbqkVO = new SbrwAbbqktjVO();
            abbqkVO.setYzpzzlDm(sbrwList.get(i-1).getYzpzzlDm());
            abbqkVO.setYzpzzlMc(YzpzzlEnum.getYzpzzlByDm(sbrwList.get(i-1).getYzpzzlDm()).getMc());
            abbqkVO.setZsxmDm(sbrwList.get(i-1).getZsxmDm());
            this.getBbmcByZsxmDm(abbqkVO);
            final String bb = i + "." +  abbqkVO.getYzpzzlMc()
                    + ",申报期限："+DateUtils.dateToString(sbrwList.get(i-1).getSbqx(),4);
            bbString.append(bb).append("  \n  ");
        }
        SaveXtxxReqVO saveXtxxReqVO = new SaveXtxxReqVO();
        saveXtxxReqVO.setYwxtid("LXPT_ZNSB");
        saveXtxxReqVO.setYwxtfwkl("1980D5F999384736E063990C170AE6F4");
        saveXtxxReqVO.setLrrsfid("SYSTEM");
        saveXtxxReqVO.setXxmbbh("00000000000_00000028");
        List<XtxxCSVO> xtxxCSVOList = new ArrayList<>();
        XtxxCSVO xtxxCSVO = new XtxxCSVO();
        xtxxCSVO.setYwzj(GyUtils.getUuid());
        xtxxCSVO.setJguuidList(jguuidList);
        // 组装消息模板报文参数
        final Map<String, Object> jsonmap = new HashMap<>();
        jsonmap.put("lbtsxxbt", "缴款漏缴提醒");
        jsonmap.put("qydm", sbrwList.get(0).getQydmz());
        jsonmap.put("nsrmc", sbrwList.get(0).getNsrmc());
        jsonmap.put("bbList", bbString);
        jsonmap.put("qsbparam", sbrwList.get(0).getJguuid());
        jsonmap.put("qclurl","/znsb/view/nssb/skjnbylq?jguuid=" + sbrwList.get(0).getJguuid());
        xtxxCSVO.setXxcs(JsonUtils.toJson(jsonmap));
        xtxxCSVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
        xtxxCSVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)), 0));
        xtxxCSVO.setXxyxj("9");
        xtxxCSVOList.add(xtxxCSVO);
        saveXtxxReqVO.setXtxxCsVOs(xtxxCSVOList);
        CommonResult<SaveXtxxResVO> result = xxtxAPI.saveXtxx(saveXtxxReqVO);
        log.info("缴款漏缴提醒发送消息中心保存返回报文：{}", JsonUtils.toJson(result));
    }

    @Override
    public void sbrwlbtsJob() {
        //扫描申报任务表中，需要提示申报的申报任务
        List<ZnsbNssbSbrwDO> sbrwList = this.getBaseMapper().queryLbtsSbrw();
        if (!GyUtils.isNull(sbrwList)){
            //按登记序号分组发送消息
            Map<String,List<ZnsbNssbSbrwDO>> sbrwGroupDjxhMap = sbrwList.stream()
                    .collect(Collectors.groupingBy(ZnsbNssbSbrwDO::getDjxh));
            //按分组后的企业循环发送消息，相同企业发一条提醒消息，每条消息包括多个报表
            sbrwGroupDjxhMap.forEach((djxh,sbrwGroupList) -> {
                this.getLbtsParamXxzx(sbrwGroupList);
            });

        }

    }

    // 组装申报漏报提醒消息请求报文
    private void getLbtsParamXxzx(List<ZnsbNssbSbrwDO> sbrwList) {
        final List<String> jguuidList = new ArrayList<>();
        jguuidList.add(sbrwList.get(0).getJguuid());
        final StringBuilder bbString = new StringBuilder();
        for (int i = 1 ; i <= sbrwList.size(); i++) {
            SbrwAbbqktjVO abbqkVO = new SbrwAbbqktjVO();
            abbqkVO.setYzpzzlDm(sbrwList.get(i-1).getYzpzzlDm());
            abbqkVO.setYzpzzlMc(YzpzzlEnum.getYzpzzlByDm(sbrwList.get(i-1).getYzpzzlDm()).getMc());
            abbqkVO.setZsxmDm(sbrwList.get(i-1).getZsxmDm());
            this.getBbmcByZsxmDm(abbqkVO);
            final String bb = i + "." +  abbqkVO.getYzpzzlMc()
                    + ",申报期限："+DateUtils.dateToString(sbrwList.get(i-1).getSbqx(),4);
            bbString.append(bb).append("  \n  ");
        }
        SaveXtxxReqVO saveXtxxReqVO = new SaveXtxxReqVO();
        saveXtxxReqVO.setYwxtid("LXPT_ZNSB");
        saveXtxxReqVO.setYwxtfwkl("1980D5F999384736E063990C170AE6F4");
        saveXtxxReqVO.setLrrsfid("SYSTEM");
        saveXtxxReqVO.setXxmbbh("00000000000_00000016");
        List<XtxxCSVO> xtxxCSVOList = new ArrayList<>();
        XtxxCSVO xtxxCSVO = new XtxxCSVO();
        xtxxCSVO.setYwzj(GyUtils.getUuid());
        xtxxCSVO.setJguuidList(jguuidList);
        // 组装消息模板报文参数
        final Map<String, Object> jsonmap = new HashMap<>();
        jsonmap.put("lbtsxxbt", "申报即将到期提醒");
        jsonmap.put("qydm", sbrwList.get(0).getQydmz());
        jsonmap.put("nsrmc", sbrwList.get(0).getNsrmc());
        jsonmap.put("bbList", bbString);
        jsonmap.put("qsbparam", sbrwList.get(0).getJguuid());
        jsonmap.put("qclurl","/znsb/view/nssb/sbrwmx?jguuid=" + sbrwList.get(0).getJguuid());
        xtxxCSVO.setXxcs(JsonUtils.toJson(jsonmap));
        xtxxCSVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
        xtxxCSVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)), 0));
        xtxxCSVO.setXxyxj("9");
        xtxxCSVOList.add(xtxxCSVO);
        saveXtxxReqVO.setXtxxCsVOs(xtxxCSVOList);
        CommonResult<SaveXtxxResVO> result = xxtxAPI.saveXtxx(saveXtxxReqVO);
        log.info("申报即将到期提醒发送消息中心保存返回报文：{}", JsonUtils.toJson(result));
    }

    @Override
    public ZnsbNssbSbrwDTO queryDqWsbSbrw(String djxh, String yzpzzlDm, Date skssqq, Date skssqz) {
        return this.getBaseMapper().queryDqWsbSbrw(djxh, yzpzzlDm, skssqq, skssqz);
    }

    @Override
    public List<ZnsbNssbSbrwDO> querySbrwByDjxhAndSkssq(String djxh, String skssqq, String skssqz) {
        return this.getBaseMapper().querySbrwByDjxhAndSkssq(djxh, skssqq, skssqz);
    }

    @Override
    public Map<String, Object> plyyjk(SbrwmxAbbPlsbReqDTO sbrwmxAbbPlsbReqDTO) {
        SimpleQueryReq queryReq = new SimpleQueryReq();
        this.getQueryPath(queryReq,sbrwmxAbbPlsbReqDTO);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("djxhList", GyUtils.isNull(sbrwmxAbbPlsbReqDTO.getDjxhList())? ZnsbSessionUtils.getKczDjxhList():sbrwmxAbbPlsbReqDTO.getDjxhList());
        // 只查询已申报的数据
        paramsMap.put("rwztDm", "02");
        // 纳税人识别号模糊查询
        paramsMap.put("nsrsbh", "%%");
        paramsMap.put("bsy", "%%");
        paramsMap.put("sbny",sbrwmxAbbPlsbReqDTO.getSbny().replaceAll("-", ""));
        queryReq.setParams(paramsMap);
        List<Map<String, Object>> sbrwmxResult = queryApi.queryList(queryReq);

        if (GyUtils.isNotNull(sbrwmxResult)) {
            for (Map<String, Object> sbrwmxMap : sbrwmxResult) {
                String djxh = GyUtils.cast2Str(sbrwmxMap.get("djxh"));
                yyjkjlService.saveYyjkjl(djxh);
            }
        }

        Map<String, Object> resMap = new HashMap<>();
        resMap.put("code", 1);
        return resMap;
    }

    @Override
    public boolean updateSbrwCwbbBscg(String sbrwuuid) {
        //先根据sbrwuuid查询申报任务
        final ZnsbNssbSbrwDO sbrw = this.getBaseMapper().selectById(sbrwuuid);
        if (GyUtils.isNull(sbrw)){
            return false;
        }
        sbrw.setNsrsbztDm(SBZT_SBCG_DM);
        sbrw.setRwztDm(RWZT_YSB_DM);
        this.getBaseMapper().updateById(sbrw);
        //先删除再写入
        znsbNssbSbrwCwbbMapper.deleteCwbb(sbrw);
        final ZnsbNssbSbrwCwbbDO znsbNssbSbrwCwbbDO = BeanUtils.toBean(sbrw,ZnsbNssbSbrwCwbbDO.class);
        znsbNssbSbrwCwbbDO.setNsrsbztDm(SBZT_ZLBS_BSCG_DM);
        znsbNssbSbrwCwbbDO.setRwztDm(RWZT_YBS_DM);
        znsbNssbSbrwCwbbMapper.insert(znsbNssbSbrwCwbbDO);
        return true;
    }

}
