package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税减免税申报明细表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsjmssbmxb", propOrder = { "zzsjmssbmxbjsxmGrid", "zzsjmssbmxbmsxmGrid" })
@XmlSeeAlso({ Zzsjmssbmxbywbw.Zzsjmssbmxb.class })
@Getter
@Setter
public class Zzsjmssbmxb {
    @XmlElement(nillable = true, required = true)
    protected ZzsjmssbmxbjsxmGrid zzsjmssbmxbjsxmGrid;

    @XmlElement(nillable = true, required = true)
    protected ZzsjmssbmxbmsxmGrid zzsjmssbmxbmsxmGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "zzsjmssbmxbjsxmGridlbVO" })
    @Getter
    @Setter
    public static class ZzsjmssbmxbjsxmGrid {
        @XmlElement(nillable = true, required = true)
        protected List<ZzsjmssbmxbjsxmGridlbVO> zzsjmssbmxbjsxmGridlbVO;

        /**
         * Gets the value of the zzsjmssbmxbjsxmGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the zzsjmssbmxbjsxmGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getZzsjmssbmxbjsxmGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ZzsjmssbmxbjsxmGridlbVO}
         */
        public List<ZzsjmssbmxbjsxmGridlbVO> getZzsjmssbmxbjsxmGridlbVO() {
            if (zzsjmssbmxbjsxmGridlbVO == null) {
                zzsjmssbmxbjsxmGridlbVO = new ArrayList<ZzsjmssbmxbjsxmGridlbVO>();
            }
            return this.zzsjmssbmxbjsxmGridlbVO;
        }
    }

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "zzsjmssbmxbmsxmGridlbVO" })
    @Getter
    @Setter
    public static class ZzsjmssbmxbmsxmGrid {
        @XmlElement(nillable = true, required = true)
        protected List<ZzsjmssbmxbmsxmGridlbVO> zzsjmssbmxbmsxmGridlbVO;

        /**
         * Gets the value of the zzsjmssbmxbmsxmGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the zzsjmssbmxbmsxmGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getZzsjmssbmxbmsxmGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ZzsjmssbmxbmsxmGridlbVO}
         */
        public List<ZzsjmssbmxbmsxmGridlbVO> getZzsjmssbmxbmsxmGridlbVO() {
            if (zzsjmssbmxbmsxmGridlbVO == null) {
                zzsjmssbmxbmsxmGridlbVO = new ArrayList<ZzsjmssbmxbmsxmGridlbVO>();
            }
            return this.zzsjmssbmxbmsxmGridlbVO;
        }
    }
}