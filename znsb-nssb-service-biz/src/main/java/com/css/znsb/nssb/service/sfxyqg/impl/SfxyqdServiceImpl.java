package com.css.znsb.nssb.service.sfxyqg.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.Base64Utils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.constants.SfEnum;
import com.css.znsb.nssb.constants.enums.SfxyClztEnum;
import com.css.znsb.nssb.constants.enums.SfxyZtdmEnum;
import com.css.znsb.nssb.mapper.lqzlsbjk.ZnsbSfxyxxMapper;
import com.css.znsb.nssb.pojo.domain.lqzlsbjk.ZnsbSfxyxx;
import com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00039.HXZGZS00039Request;
import com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00040.HXZGZS00040Request;
import com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.HXZGZS00041Request;
import com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00042.HXZGZS00042Request;
import com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00193.HXZGZS00193Request;
import com.css.znsb.nssb.pojo.vo.sfxyqd.*;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.LqSfxyJcbw;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.LqSfxyReqVO;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.LqSfxySbsjVO;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.LqSfxyYwbw;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.addsfxy.ZSSfxyAddUpdResVO;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.gjsfxy.LqGjSfxxResVO;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.querysfxy.LqQuerySfxxReqVO;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.querysfxy.LqQuerySfxxResVO;
import com.css.znsb.nssb.service.lqzlsbjk.nsrckzhzhxx.CkzhzhbgService;
import com.css.znsb.nssb.service.lqzlsbjk.sfxyxx.ZnsbSfxyxxService;
import com.css.znsb.nssb.service.sfxyqg.SfxyqdService;
import com.css.znsb.nssb.util.SjryUtil;
import com.css.znsb.nssb.utils.GYCastUtils;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


//@Log4j
@Slf4j
@Service
public class SfxyqdServiceImpl implements SfxyqdService {

    private static final Logger logger = LoggerFactory.getLogger(SfxyqdServiceImpl.class);

    private static Map<String, String> pkztMap = new HashMap<>();

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CkzhzhbgService ckzhzhbgService;

    @Resource
    private SjjhService sjjhService;

    @Resource
    private ZnsbSfxyxxMapper znsbSfxyxxMapper;

    @Resource
    private ZnsbSfxyxxService znsbSfxyxxService;

    static{
        //批扣状态列表
        pkztMap.put("0", "所有非批扣");
        pkztMap.put("1", "所有批扣");
        pkztMap.put("2", "税批扣");
        pkztMap.put("3", "费批扣");
        pkztMap.put("4", "税非批扣");
        pkztMap.put("5", "费非批扣");
    }

    protected HashMap<String, String> getRequestMap(String swjgDm) {
        // 配置核心接口需要的数据
        HashMap<String, String> map = new HashMap<>();
        map.put("sjjg", swjgDm);
        map.put("sjry", SjryUtil.getSjry(swjgDm));
        return map;
    }

//    @Override
//    public Map<String, Object> createSfxyhm(String swjgDm,String djxh,String nsrsbh,String xzqhszDm) {
//        //1.	调用核心服务SWZJ.HXZG.ZS.SCSFXYXX，如果返回值不为空，
//        HashMap<String, Object> map = new HashMap<>();
//        HXZGZS00038Request hxzgzs00038Request = new HXZGZS00038Request();
//        hxzgzs00038Request.setSkssswjgDm(swjgDm);
//        HXZGZS00038Response invoke = null;
//        long startTime = System.currentTimeMillis();
//        try {
//            String t1 = "{\"sfxyh\":\"213\"}";
//            invoke = JsonUtils.toBean(t1, HXZGZS00038Response.class);
//            //invoke = Gt3Invoker.invoke("SWZJ.HXZG.ZS.SCSFXYXX", getRequestMap(swjgDm), hxzgzs00038Request, HXZGZS00038Response.class);
//        } catch (Exception e) {
//            log.error("调用核心SWZJ.HXZG.ZS.SCSFXYXX接口异常:", e);
//        }
//        long cost = System.currentTimeMillis() - startTime;
//        log.info("***接口SCSFXYXX耗时【{}】ms***", cost);
//        String sfxyh = "";
//        if(!GyUtils.isNull(invoke))
//            sfxyh = invoke.getSfxyh();
//        if (StringUtils.isEmpty(sfxyh)) {
//            //2.	如果核心服务返回空，则结果编码
//            // ,结果消息=三方协议号码生成失败，请稍候尝试。
//            map.put("returnCode", "fail");
//            map.put("returnMsg", "三方协议号码生成失败，请稍后尝试");
//        } else {
//            // 则按如下规则构建返回，结果编码,结果消息,sfxyhm=生成的三方协议号码。
//            map.put("returnCode", "success");
//            map.put("returnMsg", "sfxyhm=生成的三方协议号码");
//
//            //禅道：50239 修改三方协议号与核心一致
//            map.put("sfxyhm", sfxyh);
//        }
//        return map;
//    }

    //查询纳税人已签订的三方协议信息。
    @Override
    public SfxyxxDTO listSfhjxy(String djxh,String swjgDm) {
        SfxyxxDTO sfxyxxDTO = new SfxyxxDTO();
        final SfhjxyxxDTO req = new SfhjxyxxDTO();
        req.setDjxh(djxh);
        req.setSfxyztDm("02");
        final List<SfhjxyxxDTO> response = querySfhjxyxxListLocalData(req);
        if (!GyUtils.isNull(response)) {
            // 结果消息,sfhjxyxxList=三方协议信息；
            sfxyxxDTO.setReturnCode("success");
            sfxyxxDTO.setReturnMsg("sfhjxyxxList=三方协议信息");
            List<SfhjxyxxDTO> dataList = new ArrayList<>();
            for(SfhjxyxxDTO SfhjxyxxDTO : response){
                SfhjxyxxDTO dto = new SfhjxyxxDTO();
                BeanUtils.copyProperties(SfhjxyxxDTO, dto);
                Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg", dto.getSkssswjgDm());
                if(GyUtils.isNotNull(swjgMap) && swjgMap.containsKey("swjgmc")){
                    dto.setSkssswjgMc(swjgMap.get("swjgmc").toString());
                }
                Map<String, Object> yhhbMap = CacheUtils.getTableData("dm_gy_yhhb", dto.getYhhbDm());
                if(GyUtils.isNotNull(yhhbMap) && yhhbMap.containsKey("yhhbmc")){
                    dto.setYhhbMc(yhhbMap.get("yhhbmc").toString());
                }
                Map<String, Object> yhyywdMap = CacheUtils.getTableData("dm_gy_yhyywd", dto.getYhyywdDm());
                String yhyywdmc = "";
                if (GyUtils.isNotNull(yhyywdMap) && GyUtils.isNotNull(yhyywdMap.get("yhyywdmc"))) {
                    yhyywdmc = yhyywdMap.get("yhyywdmc").toString();
                }
                dto.setYhyywdMc(yhyywdmc);
                String pkbzMc = pkztMap.get(dto.getPkbz());
                dto.setPkbzmc(pkbzMc);
                dataList.add(dto);
            }
            sfxyxxDTO.setSfhjxyxxList(dataList);
        } else {
            // 否则返回结果编码,结果消息=未查询到满足条件的数据
            sfxyxxDTO.setReturnCode("fail");
            sfxyxxDTO.setReturnMsg("未查询到满足条件的数据");
        }
        return sfxyxxDTO;
    }

    @Override
    public SfxyxxDTO listSfhjxyJ3cx(Map<String,Object> reqMap) {
        SfxyxxDTO sfxyxxDTO = new SfxyxxDTO();
        final SfhjxyxxDTO req = new SfhjxyxxDTO();
        req.setDjxh(GYCastUtils.cast2Str(reqMap.get("djxh")));
        final List<SfhjxyxxDTO> response = querySfhjxyxxListLocalData(req);
        if (!GyUtils.isNull(response)) {
            // 结果消息,sfhjxyxxList=三方协议信息；
            sfxyxxDTO.setReturnCode("success");
            sfxyxxDTO.setReturnMsg("sfhjxyxxList=三方协议信息");
            List<SfhjxyxxDTO> dataList = new ArrayList<>();
            for(SfhjxyxxDTO SfhjxyxxDTO : response){
                SfhjxyxxDTO dto = new SfhjxyxxDTO();
                BeanUtils.copyProperties(SfhjxyxxDTO, dto);
                Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg", dto.getSkssswjgDm());
                if(GyUtils.isNotNull(swjgMap) && swjgMap.containsKey("swjgmc")){
                    dto.setSkssswjgMc(swjgMap.get("swjgmc").toString());
                }
                Map<String, Object> yhhbMap = CacheUtils.getTableData("dm_gy_yhhb", dto.getYhhbDm());
                if(GyUtils.isNotNull(yhhbMap) && yhhbMap.containsKey("yhhbmc")){
                    dto.setYhhbMc(yhhbMap.get("yhhbmc").toString());
                }
                Map<String, Object> yhyywdMap = CacheUtils.getTableData("dm_gy_yhyywd", dto.getYhyywdDm());
                String yhyywdmc = "";
                if (GyUtils.isNotNull(yhyywdMap) && GyUtils.isNotNull(yhyywdMap.get("yhyywdmc"))) {
                    yhyywdmc = yhyywdMap.get("yhyywdmc").toString();
                }
                dto.setYhyywdMc(yhyywdmc);
                String pkbzMc = pkztMap.get(dto.getPkbz());
                dto.setPkbzmc(pkbzMc);
                dataList.add(dto);
            }
            sfxyxxDTO.setSfhjxyxxList(dataList);
        } else {
            // 否则返回结果编码,结果消息=未查询到满足条件的数据
            sfxyxxDTO.setReturnCode("fail");
            sfxyxxDTO.setReturnMsg("未查询到满足条件的数据");
        }
        return sfxyxxDTO;
    }

    //  查询存款账户信息。
    @Override
    public CkzhxxDTO listCkzhxx(String djxh) {
        //  2022/10/25  用核心征管接口，但未在服务清册中找到，目前以挡板构建
        /*CkzhxxDTO ckzhxxDTO = listsfxyxxjApi.listCkzhxx(djxh);
        return ckzhxxDTO;*/
        // 根据djxh调用存款账户服务，
        CkzhxxDTO ckzhxxDTO = new CkzhxxDTO();
        List<com.css.znsb.nssb.pojo.vo.lqzlsbjk.xmlVO.DJNsrckzhzhxxVO> djNsrckzhzhxxVOList = null;
        try {
            djNsrckzhzhxxVOList = ckzhzhbgService.listYhzhXx(djxh);
        } catch (Exception e) {
            log.error("调用接口发生错误" + e.getMessage());
        }

        //如果返回结果为空，则返回结果编码,
        // 结果消息=未查询到满足条件的数据，业务阻断。
//        HashMap<Object, Object> map = new HashMap<>();
        if (djNsrckzhzhxxVOList == null || djNsrckzhzhxxVOList.isEmpty()) {
            ckzhxxDTO.setReturnCode("fail");
            ckzhxxDTO.setReturnMsg("未查询到满足条件的数据，业务阻断");
        } else {
            ckzhxxDTO.setReturnCode("success");
            List<CkxxDTO> ckzhxxList = djNsrckzhzhxxVOList.stream()
                    .map(djNsrckzhzhxxVO -> {
                        CkxxDTO ckxxDTO = new CkxxDTO();
                        BeanUtils.copyProperties(djNsrckzhzhxxVO,ckxxDTO);
                        return ckxxDTO;
                    })
                    .collect(Collectors.toList());
            ckzhxxDTO.setCkzhxxList(ckzhxxList);
        }
        //  调用mapper层方法listYqdsfxyYhk查询已签订三方协议的银行卡
//        List<YhzhxxDTO> listYqdsfxyYhk = yshjsfxyMapper.listYqdsfxyYhk(djxh);
        //并将已签订协议的银行卡，从步骤1的结果集中过滤掉
//        for (int i = 0; i < yhzhxxDTOList.size(); i++) {
//            for (int j = 0; j < listYqdsfxyYhk.size(); j++) {
//                if (yhzhxxDTOList.get(i).equals(listYqdsfxyYhk.get(j))) {
//                    yhzhxxDTOList.remove(j);
//                }
//            }
//        }
//        map.put("yhzhxxDTOList", yhzhxxDTOList);
        return ckzhxxDTO;
    }

    //保存新增三方协议信息：该接口调整时，发邮件给唐文敏、曾凯、赵继平、抄送赵凯鹏、嵇云梅、以及各个组长
    @Override
    public Map<String, Object> saveSfhjxy(ZSSfxyVO zSsfxyVO, String sglrbj,Map<String,Object> sjjhMap) {
        Map<String, Object> map = new HashMap<>();
        try{
            final String sfxyh = zSsfxyVO.getSfxyh();
            final SfhjxyxxDTO req = new SfhjxyxxDTO();
            req.setDjxh(zSsfxyVO.getDjxh());
            req.setSfxyh(zSsfxyVO.getSfxyh());
            final List<SfhjxyxxDTO> sfhjxyxxList = querySfhjxyxxListLocalData(req);
            SfhjxyxxDTO checkSfhjxyxxDTO = new SfhjxyxxDTO();
            //通过三方协议号匹配本地表三方协议信息，并赋值到checkSfhjxyxxDTO
            if (!GyUtils.isNull(sfhjxyxxList)){
                for(int i=0;i<sfhjxyxxList.size();i++){
                    if(!GyUtils.isNull(sfxyh)){
                        if(sfxyh.equals(sfhjxyxxList.get(i).getSfxyh())){
                            checkSfhjxyxxDTO = sfhjxyxxList.get(i);
                        }
                    }
                }
            }
            if(GyUtils.isNull(checkSfhjxyxxDTO.getSfxydjuuid())){
                //判断是否是直签银行
                boolean flag = this.checkYhzq(zSsfxyVO.getYhyywdDm(), map);
                // 禅道54680：纳税人名称与缴款账户名称不一致，则不能直签
                if(flag){
                    String nsrmc="";
                    ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
                    znsbmhzcqyjbxxmxreqvo.setDjxh(zSsfxyVO.getDjxh());
                    CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVo = nsrxxApi.getNsrxxByDjxh(znsbmhzcqyjbxxmxreqvo);
                    List<JbxxmxsjVO> jbxxmxsj = nsrxxVo.getData().getJbxxmxsj();
                    if (CollectionUtils.isNotEmpty(jbxxmxsj)){
                        nsrmc = jbxxmxsj.get(0).getNsrmc();
                    }
                    if(!GyUtils.isNull(nsrmc)){
                        flag=nsrmc.equals(zSsfxyVO.getJkzhmc());
                    }
                }
                com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00039.ZSSfxyVO hxzg39Vo = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00039.ZSSfxyVO();
                BeanUtils.copyProperties(zSsfxyVO, hxzg39Vo);
                map = saveAfterCheck(hxzg39Vo, flag,sjjhMap);
            }else{
                if("1".equals(sglrbj)){
                    //手工录入逻辑
                    SfxyxxResDTO resDTO = new SfxyxxResDTO();
                    resDTO.setReturnCode("-1");
                    resDTO.setReturnMsg("三方协议号在核心征管中已存在，该三方协议号不能再使用");
                    map.put("resDTO", resDTO);
                    map.put("returnCode", "fail");
                    map.put("returnMsg", "三方协议号在核心征管中已存在，该三方协议号不能再使用");
                }else{
                    //存在三方协议登记uuid，直接发起验签
                    com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO zssfxyvoParam = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO();
                    BeanUtils.copyProperties(zSsfxyVO, zssfxyvoParam);
                    zssfxyvoParam.setSfxydjuuid(checkSfhjxyxxDTO.getSfxydjuuid());
//                    SfxyxxResDTO resDTO = checkSfhjxy(zssfxyvoParam,sjjhMap);
//                    map.put("resDTO", resDTO);
//                    map.put("sfxydjuuid", checkSfhjxyxxDTO.getSfxydjuuid());
//                    if("1".equals(resDTO.getReturnCode())){
//                        map.put("returnCode", "success");
//                        map.put("returnMsg", "保存成功");
//                    }else{
//                        map.put("returnCode", "fail");
//                        map.put("returnMsg", resDTO.getReturnMsg());
//                    }
                    CommonResult<Object> sjjhYqResult = checkSfhjxy(zssfxyvoParam,sjjhMap);
                }
            }
        }catch (Exception e){
            log.error("保存三方协议报错:"+e);
            map.put("returnCode", "fail");
            map.put("returnMsg", "三方协议号验证失败，协议保存失败请稍后重试");
        }
        return map;
    }


    //判断是否是直签银行
    public boolean checkYhzq(ZSSfxyVO zSsfxyVO, HashMap<String, Object> map){
        //TODO
        Map<String, Object> yhzqMap = new HashMap<>();
        //Map<String, Object> yhzqMap = cssDmcsService.getIndexData("CS_GY_XTCS_XDJ", "DZSWJ00XXBG290005");
        boolean flag = false;
        if(GyUtils.isNotNull(yhzqMap) && !yhzqMap.isEmpty()){
            String [] yhzqsz = yhzqMap.get("CSZ").toString().split(",");
            for(String yhzq:yhzqsz){
                String [] value = yhzq.split(":");
                if(zSsfxyVO.getYhhbDm().equals(value[0]) && "Y".equals(value[1])){
                    flag = true;
                }
            }
        }
        if(!flag){
            Map<String, Object> yhhbMcMap = CacheUtils.getTableData("dm_gy_yhhb", zSsfxyVO.getYhhbDm());
            if(yhhbMcMap == null){
                yhhbMcMap = new HashMap<>();
                yhhbMcMap.put("YHHBMC", "银行");
            }
            SfxyxxResDTO resDTO = new SfxyxxResDTO();
            resDTO.setReturnCode("0");
            resDTO.setReturnMsg("您选择的"+yhhbMcMap.get("YHHBMC")+"未开通直签业务，您可打印纸质三方协议到开户银行办理相关业务；或者更换其他银行");
            map.put("resDTO", resDTO);
            map.put("returnCode", "0");
            map.put("returnMsg", "您选择的"+yhhbMcMap.get("YHHBMC")+"未开通直签业务，您可打印纸质三方协议到开户银行办理相关业务；或者更换其他银行");
        }
        return flag;
    }

    // 替换原校验
    public boolean checkYhzq(String yhyywdDm, Map<String,Object> map) {
        //乐企没有提供校验直签配置表，故无法校验，再保存时，由乐企自行匹配
        return true;
        //获取税库银直签银行配置，存在则为直签银行
//        Map<String, Object> csZsSkyYhdz = new HashMap<String, Object>();
//        //Map<String, Object> csZsSkyYhdz = cssDmcsService.getIndexData("CS_ZS_SKY_YHDZ_YHYYWD", yhyywdDm);//新电局获取写法
//        if (ObjectUtils.isNotNull(csZsSkyYhdz) && "Y".equals(csZsSkyYhdz.get("YXBZ").toString())
//                && "Y".equals(csZsSkyYhdz.get("XYBZ").toString())) {
//            return true;
//        }
//        Map<String, Object> yhhbMcMap = CacheUtils.getTableData("dm_gy_yhyywd", yhyywdDm);
//        String yhyywdMc = "";
//        if(ObjectUtils.isNull(yhhbMcMap)){
//            yhyywdMc = "该营业网点";
//        }else {
//            yhyywdMc = yhhbMcMap.get("yhyywdmc").toString();
//        }
//        SfxyxxResDTO resDTO = new SfxyxxResDTO();
//        resDTO.setReturnCode("0");
//        resDTO.setReturnMsg("您选择的"+yhyywdMc+"未开通直签业务，您可打印纸质三方协议到开户银行办理相关业务；或者更换其他银行");
//        map.put("resDTO", resDTO);
//        map.put("returnCode", "0");
//        map.put("returnMsg", "您选择的"+yhyywdMc+"未开通直签业务，您可打印纸质三方协议到开户银行办理相关业务；或者更换其他银行");
//        return false;
    }

    //保存三方协议
    public Map<String, Object> saveAfterCheck(com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00039.ZSSfxyVO zSsfxyVO, boolean checkFlag,Map<String,Object> sjjhMap){
        final String djxh = GYCastUtils.cast2Str(sjjhMap.get("djxh"));
        final String nsrsbh = GYCastUtils.cast2Str(sjjhMap.get("nsrsbh"));
        final String xzqhszDm = GYCastUtils.cast2Str(sjjhMap.get("xzqhszDm"));

        Map<String, Object> map = new HashMap<>();
        // 调用核心服务SWZJ.HXZG.ZS.BCSFXYXX,如果失败，返回提示，业务阻断；
        HXZGZS00039Request hxzgzs00039Request = new HXZGZS00039Request();
        zSsfxyVO.setSfxyztDm("01");
        zSsfxyVO.setSfxyyzxx("");
//        String sfzjlxDm= GyUtils.isNull(zSsfxyVO.getSfzjlxDm()) ? "01":zSsfxyVO.getSfzjlxDm();//证件类型非必录项目，不明白为啥加此代码，暂时注释掉
//        zSsfxyVO.setSfzjlxDm(sfzjlxDm);
        hxzgzs00039Request.setZSsfxyVO(zSsfxyVO);
        //企业端改造，为了异步查询传参
//        hxzgzs00039Request.setCheckFlag(checkFlag);
        HashMap<String, String> extendMap = getRequestMap(zSsfxyVO.getSkssswjgDm());
        //渠道区分的sjry赋值：如果web、app传入的sjry是空则使用原有的sjry，否则使用传入的sjry
        String sjry=zSsfxyVO.getSjry();
        if(StringUtils.isNotEmpty(sjry)){
            extendMap.put("sjry", sjry);
        }

        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("BC00000002");
        sjjhDTO.setYwbm("SFXYQD001");
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
//        sjjhDTO.setBwnr(createRequestXml("03","SXN022011401","bcsfxyxx",GYCastUtils.cast2Str(sjjhMap.get("djxh")),GYCastUtils.cast2Str(sjjhMap.get("nsrsbh")),GYCastUtils.cast2Str(sjjhMap.get("xzqhszDm")),hxzgzs00039Request));
        sjjhDTO.setBwnr(new Gson().toJson(hxzgzs00039Request));
        log.info("乐企-新增三方协议-请求报文："+new Gson().toJson(hxzgzs00039Request));
        CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
        log.info("乐企-新增三方协议-响应报文："+sjjhResult.getData());

        if (sjjhResult.isSuccess() && GyUtils.isNotNull(sjjhResult.getData())) {
            final ZSSfxyAddUpdResVO zsSfxyAddUpdResVO = JsonUtils.toBean((String)sjjhResult.getData(),ZSSfxyAddUpdResVO.class);
            final String returncode = zsSfxyAddUpdResVO.getReturncode();
            final String sfxydjuuid = zsSfxyAddUpdResVO.getSfxydjuuid();


            final com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00039.ZSSfxyVO zsSfxyVO = hxzgzs00039Request.getZSsfxyVO();
            //将核心三方协议号赋值给原对象
            zsSfxyVO.setSfxydjuuid(sfxydjuuid);

            ZnsbSfxyxx znsbSfxyxx = new ZnsbSfxyxx();
            com.css.znsb.framework.common.util.object.BeanUtils.copyBean(zsSfxyVO,znsbSfxyxx);
            String lqsfxyh ="";
            if("00".equals(returncode)){

                //乐企未返回三方协议号，经沟通，让调用查询三方协议接口获取，其后续代码需要优化
                LqQuerySfxxReqVO lqreqVo = new LqQuerySfxxReqVO();
                lqreqVo.setDjxh(djxh);
                final SjjhDTO sjjhDTO1 = new SjjhDTO();
                sjjhDTO1.setSjjhlxDm("CX00000001");
                sjjhDTO1.setYwbm("SFXYQD001");
                sjjhDTO1.setDjxh(djxh);
                sjjhDTO1.setNsrsbh(nsrsbh);
                sjjhDTO1.setXzqhszDm(xzqhszDm);
                sjjhDTO1.setBwnr(JsonUtils.toJson(lqreqVo));
                log.info("乐企-查询三方协议-请求报文："+JsonUtils.toJson(lqreqVo));
                CommonResult<Object> sjjhResult1 = sjjhService.saveSjjhJob(sjjhDTO1);
                log.info("乐企-查询三方协议-响应报文："+JsonUtils.toJson(sjjhResult1.getData()));
                if (sjjhResult1.isSuccess() && GyUtils.isNotNull(sjjhResult1.getData())) {
                    final LqGjSfxxResVO lqresVo = JsonUtils.toBean((String)sjjhResult1.getData(), LqGjSfxxResVO.class);
                    log.info("乐企-查询三方协议-响应报文："+JsonUtils.toJson(lqresVo));
                    final List<SfhjxyxxDTO> sfxyxxGrid = lqresVo.getSfxyxxGrid().getSfxyxxGridlb();
                    for(int i=0;i<sfxyxxGrid.size();i++){
                        final String lqsfxydjuuid = sfxyxxGrid.get(i).getSfxydjuuid();
                        lqsfxyh = sfxyxxGrid.get(i).getSfxyh();
                        String qsyhhh = sfxyxxGrid.get(i).getQsyhhh();
                        String khyhhh = sfxyxxGrid.get(i).getKhyhhh();
                        if(sfxydjuuid.equals(lqsfxydjuuid)){
                            znsbSfxyxx.setSfxyh(lqsfxyh);
                            znsbSfxyxx.setQsyhhh(qsyhhh);
                            znsbSfxyxx.setKhyhhh(khyhhh);
                            log.info("乐企-新增三方协议赋值三方协议号："+JsonUtils.toJson(znsbSfxyxx));
                        }
                    }
                }
                //乐企未返回三方协议号，经沟通，让调用查询三方协议接口获取，其后续代码需要优化
                znsbSfxyxx.setSfxyztDm(SfxyZtdmEnum.WYZ.getDm());//成功时，更改状态为验签成功 02
                znsbSfxyxx.setSfxyyzxx(SfxyZtdmEnum.WYZ.getMc());//成功时，更改状态为验签成功 02
                znsbSfxyxx.setClztDm(SfxyClztEnum.CG_BCSFXYH.getDm());
                znsbSfxyxx.setClztmc(SfxyClztEnum.CG_BCSFXYH.getMc());
            }else{
                znsbSfxyxx.setClztDm(SfxyClztEnum.CG_SCSFXYH.getDm());
                znsbSfxyxx.setClztmc(SfxyClztEnum.CG_SCSFXYH.getMc());
            }
            znsbSfxyxx.setXgrq(LocalDateTime.now());
            boolean flag = znsbSfxyxxService.saveOrUpdate(znsbSfxyxx);
            //核心与本地均保存成功后，发起验签操作
//            if (flag&&checkFlag){
            if (flag&&"00".equals(returncode)){
                log.info("***三方协议签订***保存三方协议号成功之后***发起验证流程" + checkFlag);
                final SjjhDTO sjjhYqDTO = new SjjhDTO();
                sjjhYqDTO.setSjjhlxDm("CX00000002");
                sjjhYqDTO.setYwbm("SFXYQD001");
                sjjhYqDTO.setDjxh(djxh);
                sjjhYqDTO.setNsrsbh(nsrsbh);
                sjjhYqDTO.setXzqhszDm(xzqhszDm);
                HXZGZS00041Request hxzgzs00041Request = new HXZGZS00041Request();
                com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO sfxy041Vo = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO();
                com.css.znsb.framework.common.util.object.BeanUtils.copyBean(zsSfxyVO,sfxy041Vo);
                sfxy041Vo.setSfxyh(lqsfxyh);
                log.info("乐企-本次新增配置的三方协议号 ：" + lqsfxyh);
                hxzgzs00041Request.setZSsfxyVO(sfxy041Vo);
                sjjhYqDTO.setBwnr(new Gson().toJson(hxzgzs00041Request));

                log.info("乐企-验签三方协议-请求报文：" + new Gson().toJson(hxzgzs00041Request));
                CommonResult<Object> sjjhYqResult = sjjhService.saveSjjhJob(sjjhYqDTO);
                log.info("乐企-验签三方协议-响应报文：" + sjjhYqResult.getData());

                //根据验签结果反写本地表数据
                if(sjjhYqResult.isSuccess() && GyUtils.isNotNull(sjjhYqResult.getData())){
                    SfxyxxResDTO sfxyxxResDTO = JsonUtils.toBean((String) sjjhYqResult.getData(),SfxyxxResDTO.class);
                    final String yqReturncode = sfxyxxResDTO.getReturnCode();
                    if("00".equals(yqReturncode)){
                        znsbSfxyxx.setSfxyztDm(SfxyZtdmEnum.YZTG.getDm());//成功时，更改状态为验签成功 02
                        znsbSfxyxx.setSfxyyzxx(SfxyZtdmEnum.YZTG.getMc());//成功时，更改状态为验签成功 02
                        znsbSfxyxx.setSfxyyztgRq(new Date());//成功时，更改验签日期为当前日期
                        znsbSfxyxx.setClztDm(SfxyClztEnum.CG_YZSFXYH.getDm());
                        znsbSfxyxx.setClztmc(SfxyClztEnum.CG_YZSFXYH.getMc());
                    }else{
                        znsbSfxyxx.setSfxyztDm(SfxyZtdmEnum.YZSB.getDm());//成功时，更改状态为验签失败 03
                        znsbSfxyxx.setSfxyyzxx(SfxyZtdmEnum.YZSB.getMc());
                        znsbSfxyxx.setClztDm(SfxyClztEnum.SB_YZSFXYH.getDm());
                        znsbSfxyxx.setClztmc(SfxyClztEnum.SB_YZSFXYH.getMc());
                    }
                    znsbSfxyxx.setXgrq(LocalDateTime.now());
                    boolean yqflag = znsbSfxyxxService.saveOrUpdate(znsbSfxyxx);
                }else{
                    map.put("returncode", "02");
                    map.put("returncode", "企业端保存成功，验签失败");
                }
            }else{
                map.put("returncode", "01");
                map.put("returncode", "企业端保存成功，未验签");
            }
        }else{
            map.put("returncode", "99");
            map.put("returncode", "调用接口存在异常，未保存成功");
        }
        return map;
    }


    //验证三方协议
    @Override
    public CommonResult<Object> checkSfhjxy(com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO zssfxyvoParam,Map<String,Object> sjjhMap) {
        SfxyxxResDTO sfxyxxResDTO = new SfxyxxResDTO();
        String returnCode = "1";
        String returnMsg = "";
        HXZGZS00041Request hxzgzs00041Request = new HXZGZS00041Request();
        hxzgzs00041Request.setZSsfxyVO(zssfxyvoParam);

        final String djxh = GYCastUtils.cast2Str(sjjhMap.get("djxh"));
        final String nsrsbh = GYCastUtils.cast2Str(sjjhMap.get("nsrsbh"));
        final String xzqhszDm = GYCastUtils.cast2Str(sjjhMap.get("xzqhszDm"));
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CX00000002");
        sjjhDTO.setYwbm("SFXYQD001");
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
        sjjhDTO.setBwnr(new Gson().toJson(hxzgzs00041Request));
        log.info("乐企-验签三方协议-请求报文：" + new Gson().toJson(hxzgzs00041Request));
        CommonResult<Object> sjjhYqResult = sjjhService.saveSjjhJob(sjjhDTO);
        log.info("乐企-验签三方协议-响应报文：" + sjjhYqResult.getData());
        ZnsbSfxyxx znsbSfxyxx = new ZnsbSfxyxx();
        com.css.znsb.framework.common.util.object.BeanUtils.copyBean(zssfxyvoParam,znsbSfxyxx);
        if(sjjhYqResult.isSuccess() && GyUtils.isNotNull(sjjhYqResult.getData())){
            sfxyxxResDTO = JsonUtils.toBean((String) sjjhYqResult.getData(),SfxyxxResDTO.class);
            final String yqReturncode = sfxyxxResDTO.getReturnCode();
            //根据验签结果反写本地表数据
            if("00".equals(yqReturncode)){
                znsbSfxyxx.setSfxyztDm(SfxyZtdmEnum.YZTG.getDm());//成功时，更改状态为验签成功 02
                znsbSfxyxx.setSfxyyzxx(SfxyZtdmEnum.YZTG.getMc());
                znsbSfxyxx.setSfxyyztgRq(new Date());//成功时，更改验签日期为当前日期
                znsbSfxyxx.setClztDm(SfxyClztEnum.CG_YZSFXYH.getDm());
                znsbSfxyxx.setClztmc(SfxyClztEnum.CG_YZSFXYH.getMc());
            }else{
                znsbSfxyxx.setSfxyztDm(SfxyZtdmEnum.YZSB.getDm());//成功时，更改状态为验签失败  03
                znsbSfxyxx.setSfxyyzxx(SfxyZtdmEnum.YZSB.getMc());
                znsbSfxyxx.setClztDm(SfxyClztEnum.SB_YZSFXYH.getDm());
                znsbSfxyxx.setClztmc(SfxyClztEnum.SB_YZSFXYH.getMc());
            }
            znsbSfxyxx.setXgrq(LocalDateTime.now());
            boolean yqflag = znsbSfxyxxService.saveOrUpdate(znsbSfxyxx);
        }

        return sjjhYqResult;
    }

    //更新三方协议
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Object> updateSfhjxy(ZSSfxyVO zSsfxyVO,LqSfxyReqVO lqSfxyReqVO) {
        log.info("开始三方协议更新。。。。。。。。。。");
        final String djxh = zSsfxyVO.getDjxh();
        final String nsrsbh = zSsfxyVO.getNsrsbh();
        final String xzqhszDm = zSsfxyVO.getXzqhszDm();
        Map<String, Object> result = new HashMap<>();
        HXZGZS00193Request hxzgzs00193Request = new HXZGZS00193Request();
        zSsfxyVO.setSfxyztDm("02");
//        zSsfxyVO.setSfzjlxDm("01");
        com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00193.ZSSfxyVO zsSfxyVO = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00193.ZSSfxyVO();
        BeanUtils.copyProperties(zSsfxyVO,zsSfxyVO);
        zsSfxyVO.setSfxydjuuid(zSsfxyVO.getSfxyuuid());//赋值uuid
        zsSfxyVO.setDjxh(lqSfxyReqVO.getDjxh());//赋值登记序号
        hxzgzs00193Request.setZSsfxyVO(zsSfxyVO);
        HashMap<String, String> extendMap = getRequestMap(zSsfxyVO.getSkssswjgDm());
        //渠道区分的sjry赋值：如果web、app传入的sjry是空则使用原有的sjry，否则使用传入的sjry
        String sjry=zSsfxyVO.getSjry();
        if(StringUtils.isNotEmpty(sjry)){
            extendMap.put("sjry", sjry);
        }
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("BC00000003");
        sjjhDTO.setYwbm("SFXYQD001");
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
        sjjhDTO.setBwnr(new Gson().toJson(hxzgzs00193Request));
        log.info("乐企-修改三方协议-请求报文：" + JsonUtils.toJson(sjjhDTO));
        CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (sjjhResult.isSuccess() && GyUtils.isNotNull(sjjhResult.getData())) {
            log.info("乐企-修改三方协议-响应报文：" + JsonUtils.toJson(sjjhResult.getData()));
            final ZSSfxyAddUpdResVO zsSfxyAddUpdResVO = JsonUtils.toBean((String) sjjhResult.getData(), ZSSfxyAddUpdResVO.class);
            final String returncode = zsSfxyAddUpdResVO.getReturncode();
            ZnsbSfxyxx znsbSfxyxx = new ZnsbSfxyxx();//执行数据dto
            if ("00".equals(returncode)){
                //修改成功
                znsbSfxyxx.setClztDm(SfxyClztEnum.CG_GZSFXYH.getDm());
                znsbSfxyxx.setClztmc(SfxyClztEnum.CG_GZSFXYH.getMc());
                com.css.znsb.framework.common.util.object.BeanUtils.copyBean(zsSfxyVO,znsbSfxyxx);//成功的话，执行修改
            }else{
                //修改失败
                znsbSfxyxx.setClztDm(SfxyClztEnum.SB_GZSFXYH.getDm());
                znsbSfxyxx.setClztmc(SfxyClztEnum.SB_GZSFXYH.getMc());
                znsbSfxyxx.setSfxydjuuid(zsSfxyVO.getSfxydjuuid());//未成功的话，只修改处理状态和名称
            }
            znsbSfxyxx.setXgrq(LocalDateTime.now());
            boolean flag = znsbSfxyxxService.saveOrUpdate(znsbSfxyxx);
            //修改成功后，需要再次调用验签接口
            if ("00".equals(returncode)){
                // 如果以上操作都成功，则调用微服务/yssfhjxy/v1/checkSfhjxy,进行验证。
                long updateEnd = System.currentTimeMillis();
                com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO zssfxyvoParam = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO();
                BeanUtils.copyProperties(zSsfxyVO, zssfxyvoParam);
                zssfxyvoParam.setSfxydjuuid(zSsfxyVO.getSfxyuuid());
                log.info("====================================开始调用核心服务验证三方协议=================================");
                final Map<String,Object> sjjhMap = new HashMap<>();
                sjjhMap.put("djxh",djxh);
                sjjhMap.put("nsrsbh",nsrsbh);
                sjjhMap.put("xzqhszDm",xzqhszDm);
                CommonResult<Object> sjjhYqResult = checkSfhjxy(zssfxyvoParam,sjjhMap);
//                if (sjjhYqResult.isSuccess() && GyUtils.isNotNull(sjjhYqResult.getData())) {
//
//                }
                log.info("====================================验证三方协议结束，耗时："+(System.currentTimeMillis()-updateEnd)+"=================================");
                //返回处理结果标识与处理结果说明
            }
        }
        return sjjhResult;

//            //HXZGZS00193Response invoke = Gt3Invoker.invoke("SWZJ.HXZG.ZS.GXSFXYXX", getRequestMap(zSsfxyVO.getSkssswjgDm()), hxzgzs00193Request, HXZGZS00193Response.class);
//            String ss = "{\"returnmessage\":\"修改成功\"}";
//            HXZGZS00193Response invoke = JsonUtils.toBean(ss, HXZGZS00193Response.class);
//            //HXZGZS00193Response invoke = Gt3Invoker.invoke("SWZJ.HXZG.ZS.GXSFXYXX", extendMap, hxzgzs00193Request, HXZGZS00193Response.class);
//            long updateEnd = System.currentTimeMillis();
//            log.info("====================================更新三方协议结束，耗时："+(updateEnd-start)+"=================================");
//            if("修改成功".equals(invoke.getReturnmessage())){
//                //验证失败的修改再修改文书
//                if (StringUtils.isNotEmpty(zSsfxyVO.getKhyhhh()) && StringUtils.isNotEmpty(zSsfxyVO.getQsyhhh())){
//                    //更新申请审批和文书中心
//                    // 保存申请审批信息
//                    SfhjxyxxDTO sfxyxxByUuid = this.getSfxyxxByUuid(zSsfxyVO.getDjxh(), zSsfxyVO.getSfxydjuuid());
//                    if (StringUtils.isBlank(zSsfxyVO.getSkssswjgDm())){
//                        zSsfxyVO.setSkssswjgDm(sfxyxxByUuid.getSkssswjgDm());
//                    }
//                    String sxuuid = GyUtils.getUuid();
//                    //企业端没有审批
////                    Map<String, Object> sqspMap = this.saveSqsp(zSsfxyVO, sxuuid, yhlx);
////                    result.putAll(sqspMap);
//                    //保存文书
////                    Map<String, Object> stringObjectMap = saveSqspws(zSsfxyVO,yhlx,sxuuid);
////                    result.putAll(stringObjectMap);
//                }
//                // 如果以上操作都成功，则调用微服务/yssfhjxy/v1/checkSfhjxy,进行验证。
//                com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO zssfxyvoParam = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO();
//                BeanUtils.copyProperties(zSsfxyVO, zssfxyvoParam);
//                zssfxyvoParam.setSfxydjuuid(zSsfxyVO.getSfxydjuuid());
//                log.info("====================================开始调用核心服务验证三方协议=================================");
//                //SfxyxxResDTO dto = checkSfhjxy(zssfxyvoParam);
//                log.info("====================================验证三方协议结束，耗时："+(System.currentTimeMillis()-updateEnd)+"=================================");
//                //返回处理结果标识与处理结果说明
////                result.put("dto", dto);
////                if("1".equals(dto.getReturnCode())){
////                    result.put("returnCode", "success");
////                    result.put("returnMsg", "成功");
////                }else{
////                    result.put("returnCode", "fail");
////                    //result.put("returnMsg", "修改失败");
////                    result.put("returnMsg", dto.getReturnMsg());
////                }
//            }else{
//                result.put("returnCode", "fail");
//                result.put("returnMsg", invoke.getReturnmessage());
//            }
//        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
//            String returnCode = "fail";
//            String returnMsg = e.getMessage();
//            if(returnMsg != null && returnMsg.contains("所属应用：核心征管后端，异常原因")){
//                String [] errors = returnMsg.split("所属应用：核心征管后端，异常原因");
//                if(errors.length>1 && errors[1].length()>20){
//                    String error = errors[1].substring(18);
//                    returnMsg = error.replace(",系统内部异常","");
//                }
//            }
//            result.put("returnCode", returnCode);
//            result.put("returnMsg", "所属应用：核心征管后端，异常原因："+returnMsg);
//        }
////        }
//        return result;
    }

    //更新三方协议
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Object> cancelSfhjxy(String djxh, String nsrsbh, String xzqhszDm,String sfxyuuid) {
//        SfxyxxResDTO sfxyxxResDTO = new SfxyxxResDTO();
//        String returnCode = null;
//        String returnMsg = null;
//
//        sfxyxxResDTO.setReturnCode(returnCode);
//        sfxyxxResDTO.setReturnMsg(returnMsg);

        HXZGZS00040Request hxzgzs00040Request = new HXZGZS00040Request();
        hxzgzs00040Request.setSfxydjuuid(sfxyuuid);
        hxzgzs00040Request.setZfrDm("qyd");

        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("BC00000004");
        sjjhDTO.setYwbm("SFXYQD001");
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
//        sjjhDTO.setBwnr(yssfhjxyService.createRequestXml("03","SXN022011401","zfsfxyxx",djxh,nsrsbh,xzqhszDm,hxzgzs00040Request));
        sjjhDTO.setBwnr(JsonUtils.toJson(hxzgzs00040Request));
        log.info("乐企-删除三方协议-请求报文：" + JsonUtils.toJson(sjjhDTO));
        CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (sjjhResult.isSuccess() && GyUtils.isNotNull(sjjhResult.getData())) {
            log.info("乐企-删除三方协议-响应报文：" + JsonUtils.toJson(sjjhResult.getData()));
            final ZSSfxyAddUpdResVO zsSfxyAddUpdResVO = JsonUtils.toBean((String) sjjhResult.getData(), ZSSfxyAddUpdResVO.class);
            final String returnCode = zsSfxyAddUpdResVO.getReturncode();
            ZnsbSfxyxx znsbSfxyxx = new ZnsbSfxyxx();//执行数据dto
            com.css.znsb.framework.common.util.object.BeanUtils.copyBean(hxzgzs00040Request,znsbSfxyxx);
            if ("00".equals(returnCode)){
                znsbSfxyxx.setClztDm(SfxyClztEnum.CG_ZFSFXYH.getDm());
                znsbSfxyxx.setClztmc(SfxyClztEnum.CG_ZFSFXYH.getMc());
                znsbSfxyxx.setSfxyztDm(SfxyZtdmEnum.ZZXY.getDm());//作废成功，改为04，终止三方协议
                znsbSfxyxx.setSfxyyzxx(SfxyZtdmEnum.ZZXY.getMc());
            }else{
                znsbSfxyxx.setClztDm(SfxyClztEnum.SB_ZFSFXYH.getDm());
                znsbSfxyxx.setClztmc(SfxyClztEnum.SB_ZFSFXYH.getMc());
            }
            znsbSfxyxx.setXgrq(LocalDateTime.now());
            boolean flag = znsbSfxyxxService.saveOrUpdate(znsbSfxyxx);
        }
        return sjjhResult;
    }

//    //作废三方协议
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public SfxyxxResDTO cancelSfhjxy(String sfxyuuid,String swjgDm) {
//        SfxyxxResDTO sfxyxxResDTO = new SfxyxxResDTO();
//        String returnCode = null;
//        String returnMsg = null;
//        // 开启事务，调用mapper层setSfhjxyZt方法,如果执行成功，
////        boolean b = yshjsfxyMapper.setSfhjxyZt(sfxyuuid);
////        if (b == true) {
//        //  则调用核心服务SWZJ.HXZG.ZS.ZFSFXYXX，核心服务执行成功，提交事务
//        HXZGZS00040Request hxzgzs00040Request = new HXZGZS00040Request();
//        try {
//            hxzgzs00040Request.setSfxydjuuid(sfxyuuid);
//            hxzgzs00040Request.setZfrDm("sys");
//            String ss = "{\"returnmessage\":\"作废成功\"}";
//            HXZGZS00040Response invoke = JsonUtils.toBean(ss, HXZGZS00040Response.class);
//            //HXZGZS00040Response invoke = Gt3Invoker.invoke("SWZJ.HXZG.ZS.ZFSFXYXX", getRequestMap(swjgDm), hxzgzs00040Request, HXZGZS00040Response.class);
//            if ("作废成功".equals(invoke.getReturnmessage())) {
//                returnCode = "1";
//                returnMsg = "作废成功";
//            } else {
//                returnCode = "-1";
//                returnMsg = invoke.getReturnmessage();
//            }
//        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
//            returnCode = "-1";
//            returnMsg = e.getMessage();
//        }
////        }
//        sfxyxxResDTO.setReturnCode(returnCode);
//        sfxyxxResDTO.setReturnMsg(returnMsg);
//        return sfxyxxResDTO;
//    }

    //注销三方协议
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Object> stopSfhjxy(com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00042.ZSSfxyVO hxzgZSSfxyVO, String djxh, String nsrsbh, String xzqhszDm) {
        SfxyxxResDTO sfxyxxResDTO = new SfxyxxResDTO();
        String returnCode = null;
        String returnMsg = null;
        //则调用乐企服务，对应核心服务SWZJ.HXZG.ZS.QXSFXYXX
        HXZGZS00042Request hxzgzs00042Request = new HXZGZS00042Request();
        hxzgZSSfxyVO.setNsrsbh(nsrsbh);
        hxzgzs00042Request.setZSsfxyVO(hxzgZSSfxyVO);

        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("BC00000005");
        sjjhDTO.setYwbm("SFXYQD001");
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
        sjjhDTO.setBwnr(new Gson().toJson(hxzgzs00042Request));
        log.info("乐企-删除三方协议-请求报文：" + new Gson().toJson(hxzgzs00042Request));
        CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (sjjhResult.isSuccess() && GyUtils.isNotNull(sjjhResult.getData())) {
            log.info("乐企-删除三方协议-响应报文：" + JsonUtils.toJson(sjjhResult.getData()));
            final ZSSfxyAddUpdResVO zsSfxyAddUpdResVO = JsonUtils.toBean((String) sjjhResult.getData(), ZSSfxyAddUpdResVO.class);
            returnCode = zsSfxyAddUpdResVO.getReturncode();
            ZnsbSfxyxx znsbSfxyxx = new ZnsbSfxyxx();//执行数据dto
            com.css.znsb.framework.common.util.object.BeanUtils.copyBean(hxzgZSSfxyVO,znsbSfxyxx);
            if ("00".equals(returnCode)){
                znsbSfxyxx.setClztDm(SfxyClztEnum.CG_ZZSFXYH.getDm());//乐企自定义三方协议状态码，注销成功
                znsbSfxyxx.setClztmc(SfxyClztEnum.CG_ZZSFXYH.getMc());
                znsbSfxyxx.setSfxyztDm(SfxyZtdmEnum.ZZXY.getDm());//作废成功，改为04，终止三方协议
                znsbSfxyxx.setSfxyyzxx(SfxyZtdmEnum.ZZXY.getMc());
            }else{
                znsbSfxyxx.setClztDm(SfxyClztEnum.SB_ZZSFXYH.getDm());//乐企自定义三方协议状态码,注销失败
                znsbSfxyxx.setClztmc(SfxyClztEnum.SB_ZZSFXYH.getMc());
            }
            znsbSfxyxx.setXgrq(LocalDateTime.now());
            boolean flag = znsbSfxyxxService.saveOrUpdate(znsbSfxyxx);
            //注销成功后，需要再次调用验签接口
            if ("00".equals(returnCode)){
                // 如果以上操作都成功，则调用微服务/yssfhjxy/v1/checkSfhjxy,进行验证。
                long updateEnd = System.currentTimeMillis();
                com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO zssfxyvoParam = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO();
                BeanUtils.copyProperties(hxzgZSSfxyVO, zssfxyvoParam);
                zssfxyvoParam.setSfxydjuuid(hxzgZSSfxyVO.getSfxydjuuid());
                log.info("====================================开始调用核心服务验证三方协议=================================");
                final Map<String,Object> sjjhMap = new HashMap<>();
                sjjhMap.put("djxh",djxh);
                sjjhMap.put("nsrsbh",nsrsbh);
                sjjhMap.put("xzqhszDm",xzqhszDm);
                CommonResult<Object> sjjhYqResult = checkSfhjxy(zssfxyvoParam,sjjhMap);
                log.info("====================================验证三方协议结束，耗时："+(System.currentTimeMillis()-updateEnd)+"=================================");
                //返回处理结果标识与处理结果说明
            }
            //更新本地数据失败
            if(!flag){
                returnCode="99";
                returnMsg="更新本地数据失败";
            }
        }
        sfxyxxResDTO.setReturnCode(returnCode);
        sfxyxxResDTO.setReturnMsg(returnMsg);
        return CommonResult.success(sfxyxxResDTO);
    }

    public String getPkbzCs(String swjgDm){
        return this.getSwjgPkbzCs(swjgDm);
    }

    public String getSwjgPkbzCs(String swjgDm) {
        // 禅道46728 压力测试优化
        //        int subBegin = 9;
        //        boolean flag = true;
        //        while(flag){
        //            CommonResult<String> pkbz = yssfhjxyApi.getPkbzCs(swjgDm);
        ////            CommonResult<String> pkbz = this.getPkbzCsFromRedis(swjgDm);
        //            if(StringUtils.isNotBlank(pkbz.getResponse().getData())){
        //                return pkbz.getResponse().getData();
        //            }else{
        //                swjgDm = swjgDm.substring(0, subBegin);
        //                swjgDm = StringUtils.rightPad(swjgDm, 11, "0");
        //                subBegin = subBegin -2;
        //                if(subBegin < 0){
        //                    return "N";
        //                }
        //            }
        //        }
        //        return "N";
        //参数修改为A0000001071003162，原参数是A0000001061001077
        //TODO
        Map<String, Object> xtcsMap = new HashMap<>();
        //Map<String, Object> xtcsMap = CacheUtils.getXtcsInfo("A0000001071003162", swjgDm);
        String pkbz = (String) xtcsMap.get("csz");
        if (StringUtils.isBlank(pkbz)) {
            pkbz="N";
        }
        return pkbz;
    }


    public String getSfxyAllPkbz(String djxh, String sfxyuuid){
        //return yssfhjxyApi.getSfxyAllPkbz(djxh, sfxyuuid).getResponse().getData();
        //SELECT distinct t.PKBZ from HX_ZS.ZS_SKY_SFXY t WHERE t.djxh = #{djxh} and sfxyzt_dm = '02' AND SFXYDJUUID != #{sfxyuuid}
        Set<String> set = new HashSet<>();
        final SfhjxyxxDTO req = new SfhjxyxxDTO();
        req.setDjxh(djxh);
        List<SfhjxyxxDTO> list = querySfhjxyxxListLocalData(req);
        if (GyUtils.isNotNull(list)){
            for (SfhjxyxxDTO sfhjxyxxDTO : list) {
                if (!sfxyuuid.equals(sfhjxyxxDTO.getSfxydjuuid())&&"02".equals(sfhjxyxxDTO.getSfxyztDm())){
                    set.add(sfhjxyxxDTO.getPkbz());
                }
            }
        }
        return String.join(",", set);
    }


    public SfhjxyxxDTO getSfxyxxByUuid(String djxh, String sfxyuuid){
        final SfhjxyxxDTO req = new SfhjxyxxDTO();
        req.setDjxh(djxh);
        req.setSfxydjuuid(sfxyuuid);
        List<SfhjxyxxDTO> list = querySfhjxyxxListLocalData(req);

        SfhjxyxxDTO sfhjxyxxDTO = new SfhjxyxxDTO();
        if (!GyUtils.isNull(list))
            sfhjxyxxDTO = list.get(0);
        SfhjxyxxDTO dto = new SfhjxyxxDTO();
        if(GyUtils.isNotNull(sfhjxyxxDTO.getSfxydjuuid())){
            BeanUtils.copyProperties(sfhjxyxxDTO, dto);
            ZSSfxyVO zSsfxyVO = new ZSSfxyVO();
            zSsfxyVO.setYhhbDm(sfhjxyxxDTO.getYhhbDm());
            zSsfxyVO.setYhyywdDm(sfhjxyxxDTO.getYhyywdDm());
            //判断是否直签
            boolean flag = this.checkYhzq(zSsfxyVO.getYhyywdDm(), new HashMap<>());
            dto.setZqyhbz(flag?"Y":"N");
            Map<String, Object> map = CacheUtils.getTableData("dm_gy_yhyywd", sfhjxyxxDTO.getYhyywdDm());
            String yhyywdmc = "";
            if (GyUtils.isNotNull(map) && GyUtils.isNotNull(map.get("yhyywdmc"))) {
                yhyywdmc = map.get("yhyywdmc").toString();
            }
            dto.setYhyywdMc(yhyywdmc);
        }
        return dto;
    }

    public List<Map<String,Object>> getPkbzList(){
        List<Map<String,Object>> pkbzList = new ArrayList<>();
        Map<String, Object> map0 = new HashMap<>();
        map0.put("XYBZ", "Y");
        map0.put("SFXYZTMC", "所有非批扣");
        map0.put("SFXYZT_DM", "0");
        map0.put("YXBZ", "Y");
        Map<String, Object> map1 = new HashMap<>();
        map1.put("XYBZ", "Y");
        map1.put("SFXYZTMC", "所有批扣");
        map1.put("SFXYZT_DM", "1");
        map1.put("YXBZ", "Y");
        Map<String, Object> map2 = new HashMap<>();
        map2.put("XYBZ", "Y");
        map2.put("SFXYZTMC", "税批扣");
        map2.put("SFXYZT_DM", "2");
        map2.put("YXBZ", "Y");
        Map<String, Object> map3 = new HashMap<>();
        map3.put("XYBZ", "Y");
        map3.put("SFXYZTMC", "费批扣");
        map3.put("SFXYZT_DM", "3");
        map3.put("YXBZ", "Y");
        Map<String, Object> map4 = new HashMap<>();
        map4.put("XYBZ", "Y");
        map4.put("SFXYZTMC", "税非批扣");
        map4.put("SFXYZT_DM", "4");
        map4.put("YXBZ", "Y");
        Map<String, Object> map5 = new HashMap<>();
        map5.put("XYBZ", "Y");
        map5.put("SFXYZTMC", "费非批扣");
        map5.put("SFXYZT_DM", "5");
        map5.put("YXBZ", "Y");
        pkbzList.add(map0);
        pkbzList.add(map1);
        pkbzList.add(map2);
        pkbzList.add(map3);
        pkbzList.add(map4);
        pkbzList.add(map5);
        return pkbzList;
    }

    public Map<String, String> checkZqyh(String yhhbDm){
        Map<String, String> map = new HashMap<>();
        if(checkYhzq(yhhbDm,new HashMap<>())){
            map.put("returnCode", "1");
            map.put("returnMsg", "银行已开通直签业务");
        }else{
            map.put("returnCode", "-1");
            map.put("returnMsg", "所填银行未开通直签业务");
        }
        return map;
    }

    public List<Map<String, Object>> getQsyhhhKhyhhh(String yhyywdDm){
        List<Map<String, Object>> result = new ArrayList<>();
        //TODO
        List<Map<String, Object>> list = new ArrayList<>();
        //List<Map<String, Object>> list = cssDmcsService.getGroupIndexData("CS_ZS_SKY_YHDZ", yhyywdDm);
        if(GyUtils.isNotNull(list)){
            list.forEach(obj->{
                result.add(obj);
            });
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getXzqhTreeData() {
        List<Map<String,Object>> result = new ArrayList<>();
        List<Map<String, Object>> dmGyXzqh = CacheUtils.getTableData("dm_gy_xzqh");
        if (CollectionUtils.isNotEmpty(dmGyXzqh)) {

            for (Map<String, Object> map : dmGyXzqh) {
                // 取全部数据的
                if (GyUtils.isNull(map.get("sjxzqhszDm"))) {
                    String xzqhszDm = GyUtils.isNull(map.get("xzqhszDm")) ? "" : String.valueOf(map.get("xzqhszDm"));
                    List<Map<String,Object>> children = listChildren1(xzqhszDm,dmGyXzqh);
                    if (CollectionUtils.isNotEmpty(children)) {
                        map.put("children", children);
                    }else {
                        map.put("children",false);
                    }
                    result.add(map);
                }
            }
        }
        return result;
    }

    @Override
    public List<XzqhVO> getXzqhTreeList(String xzqhDm) {
        // 1.参数校验
        if (StringUtils.isBlank(xzqhDm)) {
//            throw new GoffException(PersonalErrorCode.KQYSSSXBG_REQUEST_XZQHDM_NULL);
            // 查询全部数据
            return getXzqhList(xzqhDm);
        }
        // 2.查询该行政区划相关信息
        Map<String, Object> xzqhMap = CacheUtils.getTableData("dm_gy_xzqh", xzqhDm);
        if (ObjectUtils.isEmpty(xzqhMap)) {
            List<XzqhVO> list = new ArrayList<>();
            return list;
        }
        // 3.取出该行政区划上级行政区划代码和
        String sjxzqhDm = xzqhMap.get("sjxzqhszDm") == null ? "" : xzqhMap.get("sjxzqhszDm").toString();
        // 如果是省级或者单列市，则取全部数据
        if (StringUtils.isBlank(sjxzqhDm) || "210200,330200,350200,370200,440300".contains(xzqhMap.get("xzqhszDm").toString())) {
            // 查询全部数据
            return getXzqhList(xzqhDm);
        }
        // 4.查询省级（一级）行政区划代码集合
        List<Map<String, Object>> topXzqhList = getTopXzqhList();
        // 5.获取省级和市级（一二级）树形集合
        List<XzqhVO> returnList = getReturnList(topXzqhList, sjxzqhDm);
        // 6.取出省级中包含市级的集合（是否含有children节点）
        List<XzqhVO> childernList = returnList.stream().filter(
                xzqhDTO -> ObjectUtils.isNotNull(xzqhDTO.getChildren())
                        && StringUtils.isNotBlank(xzqhDTO.getChildren().toString())
        ).collect(Collectors.toList());
        // 存在子节点再取子节点下的行政区划
        if (childernList.size() > 0) {
            List<XzqhVO> finalSonChildren = new ArrayList<>();
            List<XzqhVO> sonChildren  = childernList.get(0).getChildren();
            sonChildren.forEach(childern -> {
                finalSonChildren.add(childern);
                if (sjxzqhDm.equals(childern.getXzqhDm())) {
                    childern.setChildren(queryXzqhList(sjxzqhDm));
                }
            });

            returnList.forEach(returnXzqh -> {
                if (sjxzqhDm.equals(returnXzqh.getXzqhDm())) {
                    returnXzqh.setChildren(finalSonChildren);
                }
            });
        }
        return returnList;
    }


    public List<XzqhVO> getXzqhList(String xzqhDm) {
        List<XzqhVO> result = new ArrayList<>();
        List<Map<String, Object>> allList = new ArrayList<>();
        //List<Map<String, Object>> jjqhList = CacheUtils.getTableData("dm_gy_jjqh");
        List<Map<String, Object>> dmGyXzqh = CacheUtils.getTableData("dm_gy_xzqh");
//`        if (ObjectUtils.isNotEmpty(jjqhList)) {
//            allList.addAll(jjqhList);
//        }`
        if (ObjectUtils.isNotEmpty(dmGyXzqh)) {
            allList.addAll(dmGyXzqh);
        }
        if (CollectionUtils.isNotEmpty(allList)) {
            for (Map<String, Object> map : allList) {
                // 取全部数据的
                if ("1".equals(map.get("xzqhjc")) || "210200,330200,350200,370200,440300".contains(map.get("xzqhszDm").toString())) {
                    String xzqhszDm = map.get("xzqhszDm") == null ? "" : map.get("xzqhszDm").toString();
                    XzqhVO xzqhDTO = new XzqhVO();
                    xzqhDTO.setXzqhDm(xzqhszDm);
                    xzqhDTO.setXzqhMc(map.get("xzqhmc") == null ? "" : map.get("xzqhmc").toString());
                    xzqhDTO.setXzqhjc("1");
                    xzqhDTO.setSjxzqhszDm(null);
                    List<XzqhVO> children = listChildren(xzqhszDm, allList);
                    if (CollectionUtils.isNotEmpty(children)) {
                        xzqhDTO.setChildren(children);
                    }
//                    if (StringUtils.isNotEmpty(xzqhDm) && xzqhDm.equals(xzqhszDm)) {
//                        xzqhDTO.setDisabled(true);
//                    } else {
//                        xzqhDTO.setDisabled(false);
//                    }
                    log.info("getXzqhList 获取全部行政区划数据 "+JsonUtils.toJson(xzqhDTO));
                    result.add(xzqhDTO);
                }

            }
        }
        return result;
    }


    private List<XzqhVO> listChildren(String sjxzqhszDm, List<Map<String, Object>> dmGyXzqh) {
        List<XzqhVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmGyXzqh)) {
            for (Map<String, Object> map : dmGyXzqh) {
                if (sjxzqhszDm.equals(map.get("sjxzqhszDm")) ) {
                    String xzqhszDm = map.get("xzqhszDm") == null ? "" : map.get("xzqhszDm").toString();
                    // 将省级下的这些数据去掉
                    if ("210200,330200,350200,370200,440300".contains(xzqhszDm)) {
                        continue;
                    }
                    XzqhVO xzqhDTO = new XzqhVO();
                    xzqhDTO.setXzqhDm(xzqhszDm);
                    xzqhDTO.setXzqhMc(map.get("xzqhmc") == null ? "" : map.get("xzqhmc").toString());
                    xzqhDTO.setXzqhjc(map.get("xzqhjc") == null ? "" : map.get("xzqhjc").toString());
                    xzqhDTO.setSjxzqhszDm(sjxzqhszDm);
                    List<XzqhVO> children =  listChildren(xzqhszDm,dmGyXzqh);
                    if (CollectionUtils.isNotEmpty(children)) {
                        xzqhDTO.setChildren(children);
                    }
                    result.add(xzqhDTO);
                }
            }
        }
        return result;
    }

    public List<Map<String, Object>> getTopXzqhList() {
        List<Map<String, Object>> topXzqhList = new ArrayList<>();
        //筛选省级行政区划
        List<Map<String, Object>> xzqhList = CacheUtils.getTableData("dm_gy_xzqh");
        for (Map<String, Object> xzqhMap : xzqhList) {
            if ("1".equals(String.valueOf(xzqhMap.get("xzqhjc")))) {
                topXzqhList.add(xzqhMap);
                continue;
            }
            if ("210200,330200,350200,370200,440300".contains(xzqhMap.get("xzqhszDm").toString())) {
                Map<String, Object> map = new HashMap<>();
                for (Map.Entry<String, Object> entry: xzqhMap.entrySet()) {
                    map.put("xzqhjc","1");
                    map.put(entry.getKey(),entry.getValue());
                }
                topXzqhList.add(map);
                continue;
            }
        }
        return topXzqhList;
    }

    public List<XzqhVO> getReturnList(List<Map<String, Object>> topXzqhList ,String sjxzqhDm) {
        // 1.查询二级或一级行政区划相关信息
        Map<String, Object> sjxzqhMap = CacheUtils.getTableData("dm_gy_xzqh", sjxzqhDm);
        // 2.取出二级或一级行政区划代码
        String sjxzqhDmNew = sjxzqhMap.get("sjxzqhszDm") == null ? "" : sjxzqhMap.get("sjxzqhszDm").toString();
        // 3.取出当前级层代码
        String sjxzqhjc = sjxzqhMap.get("xzqhjc") == null ? "" : sjxzqhMap.get("xzqhjc").toString();
        // 4.校验是否为省级
        if ((StringUtils.isBlank(sjxzqhDmNew) && "1".equals(sjxzqhjc)) || "210200,330200,350200,370200,440300".contains(sjxzqhMap.get("xzqhszDm").toString())) {
            // 5.1 封装返回用例并返回
            return getXzqhtreeList(topXzqhList,sjxzqhDm);
        } else {
            // 5.2 继续向上查询
            return getReturnList(topXzqhList,sjxzqhDmNew);
        }
    }

    private List<XzqhVO> getXzqhtreeList(List<Map<String,Object>> topXzqhList, String xzqhdm) {
        if (ObjectUtils.isEmpty(topXzqhList)) {
            return new ArrayList<>();
        }
        // 封装返回用例
        List<XzqhVO> returnXzqhList = new ArrayList<>();
        topXzqhList.forEach(topXzqh -> {
            XzqhVO xzqhDTO = new XzqhVO();
            String xzqhszDm = topXzqh.get("xzqhszDm") == null ? "" : topXzqh.get("xzqhszDm").toString();
            xzqhDTO.setXzqhDm(xzqhszDm);
            xzqhDTO.setXzqhMc(topXzqh.get("xzqhmc") == null ? "" : topXzqh.get("xzqhmc").toString());
            xzqhDTO.setXzqhjc(topXzqh.get("xzqhjc") == null ? "" : topXzqh.get("xzqhjc").toString());
            if (xzqhdm.equals(xzqhszDm)) {
                xzqhDTO.setChildren(queryXzqhList(xzqhdm));
//                xzqhDTO.setDisabled(true);
            }
//            else {
//                xzqhDTO.setDisabled(false);
//            }
            returnXzqhList.add(xzqhDTO);
        });
        return returnXzqhList;
    }

    @Override
    public List<XzqhVO> queryXzqhList(String xzqhDm) {
        List<Map<String, Object>> dmGyXzqh = new ArrayList<>();
        List<Map<String,Object>> jjqhList = new ArrayList<>();
        if (StringUtils.isBlank(xzqhDm)) {
            // 查询全部数据
            return getXzqhList(xzqhDm);
        } else {
            final List<Map<String,Object>> xzqhListMap = CacheUtils.getTableData("dm_gy_xzqh_sxj");
            for (Map<String, Object> map : xzqhListMap) {
                if (xzqhDm.equals(GYCastUtils.cast2Str(map.get("sjxzqhszDm")))){
                    dmGyXzqh.add(map);
                }
            }
//            if (ObjectUtils.isNotEmpty(dmGyXzqh)) {
//                jjqhList = cssDmcsService.getGroupIndexData("dm_gy_jjqh",xzqhDm);
//            }
        }
        List<XzqhVO> list = new ArrayList<>();
        if (ObjectUtils.isEmpty(dmGyXzqh) &&  ObjectUtils.isEmpty(jjqhList)) {
            return list;
        }
        if (ObjectUtils.isNotEmpty(dmGyXzqh) && !dmGyXzqh.isEmpty()) {
            dmGyXzqh.forEach(xzqh -> {
                String xzqhszDm = xzqh.get("xzqhszDm") == null ? "" : xzqh.get("xzqhszDm").toString();
                String sjxzqhszDm = xzqh.get("sjxzqhszDm") == null ? "" : xzqh.get("sjxzqhszDm").toString();
                if (!xzqhszDm.equals(sjxzqhszDm)) {
                    XzqhVO xzqhDTO = new XzqhVO();
                    xzqhDTO.setXzqhDm(xzqhszDm);
                    xzqhDTO.setXzqhMc(xzqh.get("xzqhmc") == null ? "" : xzqh.get("xzqhmc").toString());
                    xzqhDTO.setXzqhjc(xzqh.get("xzqhjc") == null ? "" : xzqh.get("xzqhjc").toString());
                    xzqhDTO.setSjxzqhszDm(sjxzqhszDm);
                    list.add(xzqhDTO);
                }
            });
        }
        if (ObjectUtils.isNotEmpty(jjqhList) && !jjqhList.isEmpty()) {
            jjqhList.forEach(xzqh -> {
                String xzqhszDm = xzqh.get("xzqhszDm") == null ? "" : xzqh.get("xzqhszDm").toString();
                String sjxzqhszDm = xzqh.get("sjxzqhszDm") == null ? "" : xzqh.get("sjxzqhszDm").toString();
                if (!xzqhszDm.equals(sjxzqhszDm)) {
                    XzqhVO xzqhDTO = new XzqhVO();
                    xzqhDTO.setXzqhDm(xzqhszDm);
                    xzqhDTO.setXzqhMc(xzqh.get("xzqhmc") == null ? "" : xzqh.get("xzqhmc").toString());
                    xzqhDTO.setXzqhjc("");
                    xzqhDTO.setSjxzqhszDm(sjxzqhszDm);
                    list.add(xzqhDTO);
                }
            });
        }
        return list;
    }







    private List<Map<String,Object>> listChildren1(String sjxzqhszDm, List<Map<String, Object>> dmGyXzqh) {
        List<Map<String,Object>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmGyXzqh)) {
            for (Map<String, Object> map : dmGyXzqh) {
                if (sjxzqhszDm.equals(map.get("sjxzqhszDm")) ) {
                    String xzqhszDm = map.get("xzqhszDm") == null ? "" : map.get("xzqhszDm").toString();
                    List<Map<String,Object>> children =  listChildren1(xzqhszDm,dmGyXzqh);
                    if (CollectionUtils.isNotEmpty(children)) {
                        map.put("children", children);
                    }else {
                        map.put("children",false);
                    }
                    result.add(map);
                }
            }
        }
        return result;
    }


//    public List<Map<String, Object>> getQsyhhhKhyhhhNew(String yhyywdDm,String zgswjDm) {
//        List<Map<String, Object>> hhMapList=getQsyhhhKhyhhh(yhyywdDm);
//        List<Map<String, Object>> yhyywdDmList = null;
//        if (CollectionUtils.isNotEmpty(hhMapList) && hhMapList.stream().count() > 1) {
////            List<Map<String, Object>> swjgTree =
////                    cssDmcsService.getTreeCacheData("DM_GY_SWJG_TREE", zgswjDm, "02", true);
//            final List<Map<String, Object>> swjgTree = getBjAndSjSwjgList(zgswjDm);
//            if(!GyUtils.isNull(swjgTree)){
//                List<String> swjgList = swjgTree.stream().map(obj -> obj.get("swjgDm").toString()).collect(Collectors.toList());
//                Collections.sort(swjgList, Collections.reverseOrder());
//                for (String str:swjgList){
//                    yhyywdDmList = hhMapList.stream().filter(swjgMap -> str.equals(swjgMap.get("swjgDm"))).collect(Collectors.toList());
//                    if(!GyUtils.isNull(yhyywdDmList)) break;
//                }
//            }
//        }else{
//            return hhMapList;
//        }
//
//        return yhyywdDmList;
//    }

    /**********************************测试用******************************/

    /**
     * @param zgswjDm
     * @return {@link List<Map<String, Object>> }
     * @name 获取本上级税务机关代码
     * @description 相关说明
     * @time 创建时间:2024年08月08日下午07:54:57
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public List<Map<String, Object>> getBjAndSjSwjgList(String zgswjDm) {
        final List<Map<String,Object>> swjgList = CacheUtils.getTableData("dm_gy_swjg");
        List<Map<String, Object>> result = new ArrayList<>();

        // 遍历列表以找到本级税务机关
        for (Map<String, Object> taxAuthority : swjgList) {
            if (zgswjDm.equals(taxAuthority.get("swjgDm"))) {
                // 找到本级税务机关，添加到结果列表中
                result.add(taxAuthority);

                // 尝试找到上级税务机关
                String parentCode = (String) taxAuthority.get("sjswjgDm");
                if (GyUtils.isNotNull(parentCode)) {
                    // 遍历列表以找到上级税务机关
                    for (Map<String, Object> parentAuthority : swjgList) {
                        if (parentCode.equals(parentAuthority.get("swjgDm"))) {
                            // 找到上级税务机关，添加到结果列表中
                            result.add(parentAuthority);
                            break; // 找到上级后退出内层循环
                        }
                    }
                }

                // 找到本级和上级后退出外层循环（如果只需要一个本级和一个上级）
                break;
            }
        }
        return result;
    }

    /**
     * @param reqMap
     * @return {@link List<SfhjxyxxDTO> }
     * @name 乐企接口，根据登记序号查询所有三方协议信息
     * @description 多个方法使用，自己过滤
     * @time 创建时间:2024年08月19日下午03:27:45
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public List<SfhjxyxxDTO> querySfhjxyxxList(Map<String, Object> reqMap) {
        final String djxh = GYCastUtils.cast2Str(reqMap.get("djxh"));
        final String nsrsbh = GYCastUtils.cast2Str(reqMap.get("nsrsbh"));
        final String xzqhszDm = GYCastUtils.cast2Str(reqMap.get("xzqhszDm"));

        LqQuerySfxxReqVO lqQuerySfxxReqVO = new LqQuerySfxxReqVO();
        lqQuerySfxxReqVO.setDjxh(djxh);

        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CX00000001");
        sjjhDTO.setYwbm("SFXYQD001");
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
        sjjhDTO.setBwnr(new Gson().toJson(lqQuerySfxxReqVO));
        CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
        final LqQuerySfxxResVO resVO = JsonUtils.toBean(JsonUtils.toJson(sjjhResult.getData()), LqQuerySfxxResVO.class);
        return resVO.getSfxyxxGrid();
    }

    /**
     * @param req
     * @return {@link List<SfhjxyxxDTO> }
     * @name 查询本地表的三方协议
     * @description 相关说明
     * @time 创建时间:2024年08月21日上午11:00:21
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public List<SfhjxyxxDTO> querySfhjxyxxListLocalData(SfhjxyxxDTO req) {
        LambdaQueryWrapper<ZnsbSfxyxx> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ZnsbSfxyxx::getDjxh, req.getDjxh());
        queryWrapper.eq(GyUtils.isNotNull(req.getSfxyh()), ZnsbSfxyxx::getSfxyh, req.getSfxyh());
        queryWrapper.eq(GyUtils.isNotNull(req.getSfxydjuuid()), ZnsbSfxyxx::getSfxydjuuid, req.getSfxydjuuid());
        queryWrapper.eq(GyUtils.isNotNull(req.getSfxyztDm()), ZnsbSfxyxx::getSfxyztDm, req.getSfxyztDm());
        List<ZnsbSfxyxx> list = znsbSfxyxxMapper.selectList(queryWrapper);
        final List<SfhjxyxxDTO> sfhjxyxxList = BeanUtil.copyToList(list,SfhjxyxxDTO.class);
        return sfhjxyxxList;
    }




    /**
     * @param ywlxDm
     * @param swsxDm
     * @param ywlx
     * @return {@link LqSfxyReqVO }
     * @name 构造乐企请求报文
     * @description 相关说明
     * @time 创建时间:2024年08月20日上午11:00:16
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public String createRequestXml(String ywlxDm, String swsxDm, String ywlx, String djxh, String nsrsbh, String xzqhszDm, Object zsbw) {

        //构造解锁报文
        LqSfxyReqVO lqSfxyReqVO = new LqSfxyReqVO();
        LqSfxyJcbw lqSfxyJcbw = new LqSfxyJcbw();
        List<LqSfxyYwbw> lqSkjnKkztYwbwList = new ArrayList<>();
        LqSfxyYwbw lqSfxyYwbw = new LqSfxyYwbw();
        //申报数据
        LqSfxySbsjVO lqSfxySbsjVO = new LqSfxySbsjVO();
        lqSfxySbsjVO.setYwlx(ywlx);
        lqSfxySbsjVO.setZsbw(zsbw);
        //业务报文
        lqSfxyYwbw.setDjxh(djxh);
        lqSfxyYwbw.setYwlxDm(ywlxDm);
        lqSfxyYwbw.setMxid(GyUtils.getUuid());
        lqSfxyYwbw.setSbsj(Base64Utils.encode(JsonUtils.toJson(lqSfxySbsjVO)));
        lqSfxyYwbw.setSwjgDm(SfEnum.getSwjgDmByXzqhszDm(xzqhszDm));
        lqSfxyYwbw.setSwsxDm(swsxDm);
        lqSkjnKkztYwbwList.add(lqSfxyYwbw);
        //经办人数据
        getJbrxx(djxh,nsrsbh,lqSfxyJcbw);
        //构造请求报文、
        lqSfxyReqVO.setRequestId(GyUtils.getUuid());
        lqSfxyReqVO.setJcbw(lqSfxyJcbw);
        lqSfxyReqVO.setYwbw(lqSkjnKkztYwbwList);

        //数据交换赋值
        lqSfxyReqVO.setDjxh(djxh);
        lqSfxyReqVO.setNsrsbh(nsrsbh);
        lqSfxyReqVO.setXzqhszDm(xzqhszDm);

        return JsonUtils.toJson(lqSfxyReqVO);
    }

    /**
     * @param djxh
     * @param nsrsbh
     * @param lqSfxyJcbw
     * @return {@link JbxxmxsjVO }
     * @name 获取经办人信息
     * @description 相关说明
     * @time 创建时间:2024年08月20日上午11:07:44
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private JbxxmxsjVO getJbrxx(String djxh, String nsrsbh, LqSfxyJcbw lqSfxyJcbw){
        JbxxmxsjVO jbxxmxsjVO = new JbxxmxsjVO();

        ZnsbMhzcQyjbxxmxReqVO var1 = new ZnsbMhzcQyjbxxmxReqVO();
        var1.setDjxh(djxh);
        var1.setNsrsbh(nsrsbh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByNsrsbh(var1);
        if (GyUtils.isNotNull(nsrxxByNsrsbh)&&GyUtils.isNotNull(nsrxxByNsrsbh.getData())&&GyUtils.isNotNull(nsrxxByNsrsbh.getData().getJbxxmxsj())){
            jbxxmxsjVO = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
            lqSfxyJcbw.setJbr(jbxxmxsjVO.getBsrxm());
            lqSfxyJcbw.setJbrsfzjhm(jbxxmxsjVO.getBsrsfzjhm());
            lqSfxyJcbw.setJbrsfzjlxDm(jbxxmxsjVO.getBsrsfzjlxDm());
        }
        return jbxxmxsjVO;
    }


    /**
     * 先将纳税人三方协议改为作废状态，再根据查询结果更新本地三方协议
     * @param sfxyxxGrid
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void processSfxycxApiResponse(String djxh,List<ZnsbSfxyxx> sfxyxxGrid) {

//        final ZnsbSfxyxx znsbSfxyxx= new ZnsbSfxyxx();
//        znsbSfxyxx.setDjxh(djxh);
//        znsbSfxyxx.setXgrsfid("sfxygj");
//        znsbSfxyxxMapper.updateSfxyxxByDjxh(znsbSfxyxx);
//        log.info("processSfxycxApiResponse 执行作废三方协议成功");

        log.info("processSfxycxApiResponse 执行插入 djxh:"+djxh+" sfxyxxGrid:"+JsonUtils.toJson(sfxyxxGrid));
        znsbSfxyxxMapper.insertOrUpdateBatch(sfxyxxGrid);
        log.info("processSfxycxApiResponse 执行归集三方协议成功");

    }
}

