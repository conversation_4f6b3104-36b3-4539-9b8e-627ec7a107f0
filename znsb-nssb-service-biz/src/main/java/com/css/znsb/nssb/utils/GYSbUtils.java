package com.css.znsb.nssb.utils;

import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.constants.GyConstants;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.SBJmxxVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @project 金四电子税务局
 * @package cn.gov.chinatax.gt4.cxssb.common.utils
 * @file GYSbUtils.java 创建时间:2022/10/20 11:10
 * @title 公共类型转换类
 * @description 提供日期转字符串、字符串转日期等公共方法
 * @copyright Copyright (c) 2022 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块:
 * <AUTHOR>
 * @reviewer
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */
@Slf4j
@Component
public class GYSbUtils {

//    /**
//     *@name    根据税源编号、税源标志及税款所属期，获取减免性质下拉列表。
//     *@Description 根据税源编号、税源标志及税款所属期，获取减免性质下拉列表。
//     *              必传参数：税务机关代码、登记序号、税源编号、税源种类标志、税款所属期起、税款所属期止。
//     *              选传参数：减免征类型代码
//     *             其中税源种类标志说明:F:房产税源、C:车船税源、T:城镇土地使用税源、J:建筑业项目、B:不动产项目、H:环保税源
//     *             税源编号：可以传入房源编号、车辆识别代号、船舶识别号、土地税源编号、建筑业项目编号、不动产项目编号、环保税源编号。
//     *             根据登记序号和税源编号，获取税源备案的减免信息，获取默认减免性质，再获取税源涉及到的征收项目对应的减免性质列表。合并返回。
//     *             此方法目前只支持实现车船税根据税源取备案信息，目前税收减免审批备案还没有支持其他税源。
//     *@Time    创建时间:2022/10/20 11:10
//     *@param swjgDm - 税务机关代码
//     *@param djxh - 登记序号
//     *@param sybh - 税源编号
//     *@param syzlbz - 税源种类标志
//     *@param skssqq - 税款所属期起
//     *@param skssqz - 税款所属期止
//     *@param jmzlxDm -减免征类型代码
//     *@return  - 税收减免性质列表
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> getSsjmxxDmListBySybhAndSkssq(String swjgDm, String djxh, String sybh, String syzlbz, String skssqq, String skssqz, String jmzlxDm) {
//        final List<Map<String, Object>> jmxzList = new ArrayList<Map<String, Object>>();
//        if (syzlbz == null || "".equals(syzlbz)) {
//            return null;
//        }
//        String zsxmDm = null;
//        if ("F".equals(syzlbz)) {
//            zsxmDm = "10110";
//        } else if ("C".equals(syzlbz)) {
//            zsxmDm = "10114";
//            // 需求张颖确认，车船税已取消减免备案（包括自然人），无需再查减免备案
//            //final List<YHCcsjmbaspjgxxVO> ccsspxxList = (List<YHCcsjmbaspjgxxVO>) BizServiceUtil.invokeBizService("SWZJ.HXZG.YH.QUERYCLJMBAXX", djxh, sybh, skssqq, skssqz);
//            //if (ccsspxxList == null || ccsspxxList.size() == 0) {
//            return getSsjmxzListByZsxmDmAndssq(swjgDm, zsxmDm, skssqq, skssqz);
//            //} else {
//            //    jmxzList = transCcsvoTojmxzList(ccsspxxList);
//            //}
//        } else if ("T".equals(syzlbz)) {
//            zsxmDm = "10112";
//        } else if ("H".equals(syzlbz)){
//            zsxmDm = "10121";
//            return getSsjmxzListByZsxmDmAndssq(swjgDm, zsxmDm, skssqq, skssqz);
//        }
//        else {
//            //建筑业项目、不动产项目，目前没有按项目审批优惠的，先按营业税来实现。
//            zsxmDm = "10103";
//
//        }
//
//        return jmxzList;
//    }
//
    /**
     *
     *@name    中文名称
     *@Description 根据税务机关、征收项目、税款所属期起、税款所属期止获取此征收项目关联的减免性质列表
     *@Time    创建时间:2022/10/20 11:10
     *@param swjgDm - 税务机关
     *@param zsxmDm - 征收项目代码
     *@param skssqq - 税款所属期起
     *@param skssqz - 税款所属期止
     *@return - 减免性质列表
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static List<Map<String, Object>> getSsjmxzListByZsxmDmAndssq(String swjgDm, String zsxmDm, String skssqq, String skssqz){
        final List<Map<String, Object>> ssjmxzList = new ArrayList<Map<String, Object>>();
        if (skssqq == null || skssqz.equals("") || skssqz == null || skssqq.equals("")) {
            return ssjmxzList;
        }

        //从参数表中获取征收项目关联的所有税收减免性质，然后在每条根据代码表中的有效期进行比较，只返回在税款所属期内有效的减免性质列表。
        final List<Map<String, Object>> csbssjmxzList = getSsjmxxDmListByZsxmDmAndSwjgDm(zsxmDm, swjgDm, skssqz);
        if (csbssjmxzList != null && csbssjmxzList.size() > 0) {
            for (int i = 0; i < csbssjmxzList.size(); i++) {
                final Map<String, Object> csbjmxzMap = csbssjmxzList.get(i);
                final String ssjmxzDm = (String) csbjmxzMap.get("ssjmxzDm");
                if (ssjmxzDm != null && !"".equals(ssjmxzDm)) {
                    final Map<String, Object> ssjmxzMap = (Map<String, Object>) CacheUtils.getTableData("dm_gy_ssjmxz_yx", ssjmxzDm);
                    if (ssjmxzMap != null) {
                        final Calendar dmYxqq = GYCastUtils.cast2Calendar(GyUtils.cast2Date(ssjmxzMap.get("yxqq")));
                        final Calendar dmYxqz = GYCastUtils.cast2Calendar(GyUtils.cast2Date(ssjmxzMap.get("yxqz")));
                        if (!GYCastUtils.cast2Calendar(GyUtils.cast2Date(skssqq)).after(dmYxqz) && !GYCastUtils.cast2Calendar(GyUtils.cast2Date(skssqz)).before(dmYxqq)) {
                            csbjmxzMap.put("zsxmDm", zsxmDm);
                            final Map<String,Object> csMap = NsrglYhUtils.getSsjmxzJmfdedslgx(ssjmxzDm, swjgDm);//getJmslFdEdBySsjmxzDm(ssjmxzDm) ;
                            csbjmxzMap.put("jmfd", csMap.get("jmfd")) ;
                            csbjmxzMap.put("jmed", csMap.get("jmed")) ;
                            csbjmxzMap.put("jmsl", csMap.get("jmsl")) ;
                            csbjmxzMap.put("jmqxq", ssjmxzMap.get("yxqq")) ;//增加有效期起止
                            csbjmxzMap.put("jmqxz", ssjmxzMap.get("yxqz")) ;
                            csbjmxzMap.put("spbaBz", "N");
                            ssjmxzList.add(csbjmxzMap);
                        }
                    }
                }
            }
        }
        if("10101".equals(zsxmDm))
        {
            filterJzjtJmxzListForZzsAndXw(ssjmxzList);//过滤掉增值税即征即退相关减免性质和小微企业相关减免性质
        }
        filterJmxzListForSpjmxm(ssjmxzList, swjgDm);//过滤掉审批内税收减免性质
        return ssjmxzList;
    }

    /**
     *@name    中文名称
     *@Description 根据征收项目代码和税务机关代码获取税收减免性质列表
     *@Time    创建时间:2022/10/20 11:10
     *@param zsxmDm - 征收项目代码
     *@param swjgDm - 税务机关代码
     *@param skssq - 税款所属期
     *@return - 税收减免性质列表
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static List<Map<String, Object>> getSsjmxxDmListByZsxmDmAndSwjgDm(String zsxmDm, String swjgDm,String skssq) {
        if ((zsxmDm == null || "".equals(zsxmDm)) || (swjgDm == null || "".equals(swjgDm))) {
            return null ;
        }
        final List<Map<String, Object>> ssjmxzList = new ArrayList<Map<String, Object>>();
        /*final Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("ZSXM_DM", zsxmDm);*/
//        final List<Map<String, Object>> jmxzcsList = CacheUtils.getTableData("cs_yh_swsxjmxzdzb_zsxm", zsxmDm);
        List<Map<String,Object>> jmxzcsList = CacheUtils.getTableData("cs_yh_swsxjmxzdzb_zsxm");
        jmxzcsList = jmxzcsList.stream().filter(m -> m.get("zsxmDm").equals(zsxmDm)).collect(Collectors.toList());
        final List<Map<String, Object>> ssjmxzmcdzList = getSsjmxzmcdzList();
        if (jmxzcsList != null && jmxzcsList.size() > 0) {
            //查找本机及本上级
            final List<String> swjgDmList = findBjsjSwjgDmInCache(swjgDm);
            for (int i = 0; i < jmxzcsList.size(); i++) {
                final Map<String, Object> jmxzcsMap = jmxzcsList.get(i);
                final String xybz = (String) jmxzcsMap.get("xybz");
                final String yxbz = (String) jmxzcsMap.get("yxbz");
                final String comSwjgDm = (String) jmxzcsMap.get("swjgDm");
                //只返回选用标志和有效标志为Y且税务机关代码为当前税务机关本机及本上级的数据
                if ("Y".equals(xybz) && "Y".equals(yxbz) && swjgDmList.contains(comSwjgDm))
                {
                    final String ssjmxz = (String) jmxzcsMap.get("ssjmxzDm");
//                    final String jmxmDm = getJmxmDmBySsjmxzDm(ssjmxz) ;
                    final String jmxmDm = (String) jmxzcsMap.get("swsxDm");
                    final String jmxmmc = getJmxmMcByJmxmDm(jmxmDm) ;
                    final Map<String, Object> jmxzMap = new HashMap<String, Object>();
                    jmxzMap.put("ssjmxzDm", ssjmxz);
                    jmxzMap.put("swsxDm", jmxmDm);
                    //jmxzMap.put("ssjmxzmc", jmxmmc +"|"+getSsjmxzMcByssjmxzDm(ssjmxz));modify by yangdalin 20210508 需要通过有效期进行选择性显示减免性质名称
                    jmxzMap.put("ssjmxzmc", jmxmmc +"|"+getSsjmxzmc(GyUtils.cast2Date(skssq),ssjmxz,ssjmxzmcdzList));
                    jmxzMap.put("zsxmDm", zsxmDm);
                    // TODO 报错
                    jmxzMap.put("jmzlxDm", getJmzlxDmBySsjmxzDm(ssjmxz)) ;
                    ssjmxzList.add(jmxzMap);
                }

            }
        }
        //对税收减免性质进去重
        if(ssjmxzList != null && ssjmxzList.size() > 0)
        {
            final Map<String, Object> map = new HashMap<String, Object>() ;
            for( int i = ssjmxzList.size()-1 ;i >= 0 ; i-- )
            {
                final Map<String, Object> jmxzMap = ssjmxzList.get(i) ;
                String ssjmxz = (String) jmxzMap.get("ssjmxzDm");
                //20191018 按减免性质代码和税务事项代码共同过滤
                final String swsxDm = (String) jmxzMap.get("swsxDm");
                ssjmxz = ssjmxz+swsxDm;
                final String comssjmxz = (String)map.get(ssjmxz) ;
                if(comssjmxz == null)
                {
                    map.put(ssjmxz, ssjmxz) ;

                }
                else
                {
                    ssjmxzList.remove(i);
                }
            }
        }
        //过滤征收退税专用减免性质
        filterTszyJmxz(ssjmxzList);
        return ssjmxzList ;
    }

    /**
     *
     *@name    获取税收减免性质代码名称对照下拉列表
     *@description  获取税收减免性质代码名称对照下拉列表
     *@time    创建时间:2022/10/20 11:10
     *@return  获取税收减免性质代码名称对照下拉列表
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static List<Map<String, Object>> getSsjmxzmcdzList() {
        final List<Map<String, Object>> ssjmxzmcdzList = new ArrayList<>(CacheUtils.getTableData("cs_yh_ssjmxzdmmcdz"));
        return ssjmxzmcdzList;
    }

    /**
     *@name 过滤征收退税专用减免性质代码
     *@description 相关说明
     *@time 创建时间:2022/10/20 11:10
     *@param ssjmxzList 减免性质列表
     *@return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static List<Map<String, Object>> filterTszyJmxz(List<Map<String, Object>> ssjmxzList) {
        if (ssjmxzList != null && ssjmxzList.size() > 0) {
            final Map<String, String> tszyMap = new HashMap<>();
            final Iterator shzbit = CacheUtils.getTableData("cs_sb_jsjfshzbpzb").iterator();
            while (shzbit.hasNext()) {
                final Map<String, Object> zbMap = (Map<String, Object>) shzbit.next();
                final String tszybz = (String) zbMap.get("tszybz");
                if ("Y".equals(tszybz)) {
                    tszyMap.put(GyUtils.getStringValue(zbMap.get("ssjmxzDm")), tszybz);
                }
            }

            for (int i = ssjmxzList.size() - 1; i >= 0; i--) {
                final String ssjmxzDm = (String) ssjmxzList.get(i).get("ssjmxzDm");
                if (!GYObjectUtils.isNull(tszyMap.get(ssjmxzDm))) {
                    ssjmxzList.remove(i);
                }
            }
        }
        return ssjmxzList;
    }


    /**
     *
     *@name    中文名称
     *@Description 去掉增值税即征即退相关税收减免性质和小微企业减免的减免性质
     *@Time    创建时间:2022/10/20 11:10
     *@param ssjmxzList - 税收减免性质列表
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private static void filterJzjtJmxzListForZzsAndXw(List<Map<String, Object>> ssjmxzList) {
        if(ssjmxzList != null && ssjmxzList.size() > 0)
        {
            final int jmxzlistSzie = ssjmxzList.size() ;
            for(int i = jmxzlistSzie-1 ; i >= 0 ; i--)
            {
                final Map<String,Object> jmxzMap = ssjmxzList.get(i) ;
                final String ssjmxzDm = (String) jmxzMap.get("ssjmxzDm");
                final String jmxmDm = getJmxmDmBySsjmxzDm(ssjmxzDm) ;
                if(jmxmDm != null && !"".equals(jmxmDm) && isZzsJzjtJmxm(jmxmDm))
                {
                    ssjmxzList.remove(i) ;
                }
                //过滤小微企业相关的税收减免性质，不能选择小微企业的减免性质
                if("0001045301".equals(ssjmxzDm) || "0001045302".equals(ssjmxzDm) || "0001045303".equals(ssjmxzDm) || "0001045304".equals(ssjmxzDm) || "0001045305".equals(ssjmxzDm)
                        ||"0001042801".equals(ssjmxzDm)||"0001042804".equals(ssjmxzDm)|"0001042803".equals(ssjmxzDm) || "0001045306".equals(ssjmxzDm) || "0001045307".equals(ssjmxzDm))
                {
                    ssjmxzList.remove(i) ;
                }
            }
        }
    }

    /**
     *@name    中文名称
     *@Description 校验减免项目是否即征即退减免项目
     *@Time    创建时间:2022/10/20 11:10
     *@param jmxmDm - 减免项目代码
     *@return true or false
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    public static boolean isZzsJzjtJmxm(String jmxmDm) {
        boolean flag = false ;
        final Map<String, Object> jmxmMap = CacheUtils.getTableData("dm_gy_swsx", jmxmDm);
        if (jmxmMap != null && jmxmMap.get("ywlcDm") != null) {
            final String ywlcDm = (String)jmxmMap.get("ywlcDm") ;
            final String jmxmmc = (String)jmxmMap.get("swsxmc") ;
            if("LCA031002".equals(ywlcDm) || jmxmmc.indexOf("即征即退") > 0)
            {
                flag = true ;
            }
        }
        return flag ;
    }

    /**
     *
     *@name    中文名称
     *@description 过滤审批类税收减免性质
     *@time    创建时间:2022/10/20 11:10
     *@param ssjmxzList - 税收减免性质列表
     *@param swjgDm - 税务机关代码
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private static void filterJmxzListForSpjmxm(List<Map<String, Object>> ssjmxzList,String swjgDm) {
        if(ssjmxzList == null || ssjmxzList.size() == 0)
        {
            return ;
        }
        //去掉审批内税收减免性质代码
        if(ssjmxzList != null && ssjmxzList.size() > 0)
        {
            for( int i = ssjmxzList.size()-1 ;i >= 0 ; i-- )
            {
                final Map<String, Object> jmxzMap = ssjmxzList.get(i) ;
                final String ssjmxz = (String) jmxzMap.get("ssjmxzDm");
                final String jmxmDm = getJmxmDmBySsjmxzDm(ssjmxz) ;
                if(checkSlswsxSpOrBa(jmxmDm, swjgDm))
                {
                    ssjmxzList.remove(i) ;
                }
            }
        }
    }

    /**
     *@name    根据税收减免性质代码获取减免项目代码
     *@Description 根据税收减免性质代码获取减免项目代码
     *@Time    创建时间:2022/10/20 11:10
     *@param ssjmxzDm - 税收减免性质代码
     *@return - 减免项目代码(税务事项代码DM_GY_SWSX)
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    public static String getJmxmDmBySsjmxzDm(String ssjmxzDm) {
        List<Map<String,Object>> csYhSwsxjmxzdzbList = CacheUtils.getTableData("cs_yh_swsxjmxzdzb");
        csYhSwsxjmxzdzbList = csYhSwsxjmxzdzbList.stream().filter(m -> m.get("ssjmxzDm").equals(ssjmxzDm)).collect(Collectors.toList());
        if(csYhSwsxjmxzdzbList != null && !csYhSwsxjmxzdzbList.isEmpty()){
            final Map<String,Object> map = csYhSwsxjmxzdzbList.get(0);
            return GyUtils.getStringValue(map.get("SWSX_DM"));
        }
        return "";
    }

    /**
     *
     *@name    根据税务事项，税务机关判断此税务事项是审批类还是备案类税务事项
     *@description 相关说明
     *@time    创建时间:2022/10/20 11:10
     *@param swsxDm 税务事项
     *@param swjgDm 税务机关
     *@return true/false
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    public static boolean checkSlswsxSpOrBa(String swsxDm, String swjgDm) {
        //根据税务事项代码获取受理税务事项代码列表
        if(swsxDm == null || "".equals(swsxDm) || swjgDm == null)
        {
            return false ;
        }
        final Map<String, Object> slswsxMap = (Map<String, Object>) CacheUtils.getTableData("dm_gy_swsx", swsxDm);
        if (slswsxMap == null) {
            final Map<String, Object> expMap = new HashMap<String, Object>();
            expMap.put("swsxDm", swsxDm);
//            throw new SwordBizCheckedException("1010010097000042",expMap); //增加校验后，数据存在问题时，无法测试使用，所以去掉校验。
            return false ;
        }
        final String slswsxDm = GyUtils.getStringValue(slswsxMap.get("slswsxDm"));
        if(slswsxDm == null ||"".equals(slswsxDm))
        {
            return false ;
        }
        //根据受理税务事项代码，取受理税务事项与流程税务事项对照表列表
        // TODO 注意这里怎么取值的
        final List<Map<String, Object>> slswsxAndLcswsxList = (List<Map<String, Object>>) CacheUtils.getTableData("cs_gy_slswsxylcswsxdzb", slswsxDm);
        if (slswsxAndLcswsxList == null || slswsxAndLcswsxList.size() <= 0) {
            final Map<String, Object> expMap = new HashMap<String, Object>();
            expMap.put("slswsxDm", slswsxDm);
            // todo 异常处理
            // throw new SwordBizCheckedException("1010010097000043",expMap);
        }
        //查询本上级的税务机关
        final List<String> swjgList = findBjsjSwjgDmInCache(swjgDm);
        for (int j = 0; j < swjgList.size(); j++) {
            final String jgDm = swjgList.get(j);
            for (int k = 0; k < slswsxAndLcswsxList.size(); k++) {
                final Map<String, Object> slswsxAndLcswsxMap = slswsxAndLcswsxList.get(k);
                final String jg = GyUtils.getStringValue(slswsxAndLcswsxMap.get("swjgDm"));
                final String lcswsxDm = GyUtils.getStringValue(slswsxAndLcswsxMap.get("lcswsxDm"));
                if (jgDm.equals(jg)) {
                    return "LCSXA031003001".equals(lcswsxDm);
                }
            }
        }
        return false;
    }

    /**
     *@name     查找税务机关对应的上级税务机关
     *@description 相关说明
     *@time     创建时间:2022/10/20 11:10
     *@param    swjgDm 税务机关代码
     *@return   税务机关代码
     *<AUTHOR>
     *@history  修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    public static String findSjswjgInCache(String swjgDm) {
        String sjswjgDm = new String();
        Map<String, Object> swjgMap = (Map<String, Object>) CacheUtils.getTableData("dm_gy_swjg", swjgDm);
        //final Map<String,Object> swjgMap = findSingleInCache(swjgDm,"SWJG_DM",T_DM_SWJG);
        if (swjgMap != null && !swjgMap.isEmpty()) {
            final String eachSjswjgDm = (String) swjgMap.get("sjswjgDm");
            if (eachSjswjgDm != null) {
                sjswjgDm = eachSjswjgDm;
            }
        }
        return sjswjgDm;
    }

    /**
     *@name         把Map中大写的key转成标准的小写驼峰式key
     *@description  相关说明
     *@time         创建时间:2022/10/20 11:10
     *@param req    输入Map
     *@return       Map<String,Object>
     *<AUTHOR>
     *@history      修订历史（历次修订内容、修订人、修订时间等）
     */
    public static Map<String, Object> cast2DefKeyMap(Map<String, Object> req) {
        return GYCastUtils.cast2DefKeyMap(req);
    }
//
    /**
     *@name     查找本级上级税务机关list
     *@description 相关说明
     *@time     创建时间:2022/10/20 11:10
     *@param    swjgDm 本级税务机关代码
     *@return   bjsjSwjgDmList 本级上级税务机关list
     *<AUTHOR>
     *@history  修订历史（历次修订内容、修订人、修订时间等）
     */
    public static List<String> findBjsjSwjgDmInCache(String swjgDm)  {
        final List<String> bjsjSwjgDmList = new ArrayList<String>();
        bjsjSwjgDmList.add(swjgDm);
        String bjswjgDm = swjgDm;//本级税务机关
        String sjswjgDm = findSjswjgInCache(bjswjgDm);//上级税务机关
        //上溯，直到找不到上级机关为止
        while (GYObjectUtils.isNotNull(sjswjgDm) && !bjswjgDm.equals("00000000000") && !sjswjgDm.equals(swjgDm)) {
            bjsjSwjgDmList.add(sjswjgDm);//同时添加上级税务机关
            //上溯一层机关
            bjswjgDm = sjswjgDm;//本级税务机关
            sjswjgDm = findSjswjgInCache(bjswjgDm);//上级税务机关
        }
        return bjsjSwjgDmList;
    }

    /**
     * @name    中文名称
     * @Description 根据项目项目代码获取减免项目名称
     * @Time    创建时间:2022/10/20 11:10
     * @param jmxmDm - 减免项目代码
     * @return - 减免项目名称
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static String getJmxmMcByJmxmDm(String jmxmDm){
        final Map<String, Object> jmxmMap = CacheUtils.getTableData("dm_gy_swsx", jmxmDm);
        if (jmxmMap != null && jmxmMap.get("swsxmc") != null) {
            return (String) jmxmMap.get("swsxmc");
        }
        return "";
    }

    /**
     *@name    中文名称
     *@Description 根据税收减免性质代码获取减免征类型代码，如果反馈为空，则表示此税收减免性质即适用免征，也适用减征。如果只返回一个字符，则此税收减免性质代码只
     *          适用于一种减免征类型
     *@Time    创建时间:2022/10/20 11:10
     *@param ssjmxzDm - 税收减免性质代码
     *@return 减免征类型代码
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static String getJmzlxDmBySsjmxzDm(String ssjmxzDm){
        //TODO 取数有问题
//        final List<Map<String, Object>> ssjmxzList = CacheUtils.getTableData("cs_gy_ssjmxzyjmzlxdzb", ssjmxzDm);
        List<Map<String,Object>> ssjmxzList = CacheUtils.getTableData("cs_gy_ssjmxzyjmzlxdzb");
        ssjmxzList = ssjmxzList.stream().filter(m -> m.get("ssjmxzDm").equals(ssjmxzDm)).collect(Collectors.toList());
        //TODO 换成取Map的方式
        // Map<String, Object> ssjmxzMap= CacheGyUtils.getHcbMapByKey("CS_GY_SSJMXZYJMZLXDZB", ssjmxzDm,"Y");
        String jmzlxDm = null;
        if(!GYObjectUtils.isNull(ssjmxzList)){
            for (int j = 0; j < ssjmxzList.size(); j++) {
                final Map<String, Object> jmzlxcsMap = ssjmxzList.get(j);
                final String csJmzlxDm = (String) jmzlxcsMap.get("jmzlxDm");
                if (jmzlxDm == null || jmzlxDm.equals(csJmzlxDm)) {
                    jmzlxDm = csJmzlxDm;
                } else {
                    jmzlxDm = null;
                    break;
                }

            }
        }/*else {
            if (!GYObjectUtils.isNull(ssjmxzMap)) {
                jmzlxDm = (String) ssjmxzMap.get("JMZLX_DM");
            }
        }*/
        return jmzlxDm;
    }

    /**
     *@name    获取转换后的税收减免性质名称
     *@description  获取转换后的税收减免性质名称
     * @param skssq 税款所属期
     * @param ssjmxzDm 减免性质代码
     * @param ssjmxzmcdzList 税收减免性质代码名称对照下拉列表
     * @return 减免新增名称
     */
    public static  String getSsjmxzmc(Date skssq,String ssjmxzDm,List<Map<String, Object>> ssjmxzmcdzList){
        final String ssjmxzmc = "";
        Date skssqTemp = skssq;
        if (GYObjectUtils.isNull(ssjmxzDm)) {
            return ssjmxzmc;
        }
        if(GYObjectUtils.isNull(skssqTemp))
        {
            skssqTemp = Calendar.getInstance().getTime();
        }
        if (!GYObjectUtils.isNull(ssjmxzmcdzList)) {
            for (Map<String, Object> ssjmxzMap : ssjmxzmcdzList) {
                final String dmStr = (String) ssjmxzMap.get("ssjmxzDm");
                final Date yxqq = GyUtils.cast2Date(ssjmxzMap.get("yxqq"));
                Date yxqz = GyUtils.cast2Date(ssjmxzMap.get("yxqz"));
                if (GYObjectUtils.isNull(yxqz)) {
                    yxqz = GyUtils.cast2Date("2099-12-31");
                }

                if (ssjmxzDm.equals(dmStr) && (!skssqTemp.before(yxqq)) && (!skssqTemp.after(yxqz))) {
                    return GyUtils.getStringValue(ssjmxzMap.get("ssjmxzmc"));
                }
            }
        }
        final Map<String, Object> ssjmxzMap = CacheUtils.getTableData("dm_gy_ssjmxz", ssjmxzDm);
        if (ssjmxzMap != null && ssjmxzMap.get("ssjmxzmc") != null) {
            return (String) ssjmxzMap.get("ssjmxzmc");
        }
        return ssjmxzmc;
    }
//
//
//    /**
//     *
//     *@name    获取计税月份数
//     *@description 相关说明,获取计税月份数
//     *@time    创建时间:2015-4-27下午04:29:23
//     *@param skssqq 税款所属期起
//     *@param skssqz 税款所属期止
//     *@param yxqq1   有效期起
//     *@param yxqz1 有效期止
//     *@return  jsyfs 计税月份数
//     *@throws
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static int getJsyfsforCcs(LocalDate skssqq, LocalDate skssqz, LocalDate yxqq1, LocalDate yxqz1){
//        int jsyfs = 0;// 默认1
//        LocalDate yxqz = yxqz1;
//        LocalDate yxqq = yxqq1;
//        if (GYObjectUtils.isNull(skssqq)) {
//            return jsyfs;
//        }
//        if (GYObjectUtils.isNull(skssqz)) {
//            return jsyfs;
//        }
//        if (GYObjectUtils.isNull(yxqq1)) {
//            return jsyfs;
//        }
//        //计算应补退税额和期数
//        int zjyfs = 0;
//        if (GYObjectUtils.isNull(yxqz1)) {
//            yxqz = LocalDate.of(9999,12,31);
//        }else{
//            zjyfs = 1;
//        }
//
//        // 税款所属期在有效期内 正常算法取期数
//        if (yxqq.compareTo(skssqq) < 0 && skssqz.compareTo(yxqz) <= 0) {
//            jsyfs = jsYfkd(skssqq, skssqz);
//            // 税款所属期起在有效期起之前，取有效期起与税款所属期止的月份差+1
//        } else if (yxqq.compareTo(skssqq) >= 0 && skssqz.compareTo(yxqz) <= 0) {
//            jsyfs = jsYfkd(yxqq, skssqz);
//            // 税款所属期止在有效期止之后，取税款所属期起与有效期止的月份差+1
//        } else if (yxqq.compareTo(skssqq) < 0 && skssqz.compareTo(yxqz) > 0) {
//            jsyfs = jsYfkd(skssqq, yxqz)  - zjyfs;
//            // 有效期在税款所属期之内，取有效期起止的月份差+1
//        } else if (yxqq.compareTo(skssqq) >= 0 && skssqz.compareTo(yxqz) > 0) {
//            jsyfs = jsYfkd(skssqq, yxqz) - zjyfs;
//        }
//        //车船税含头不含尾,如果yxqz在skssqz内,则月份数-1
//        if(skssqz.compareTo(yxqz)==0){
//            jsyfs--;
//        }
//        if (jsyfs < 0) {
//            jsyfs = 0;
//        }
//        return jsyfs;
//    }
//
//    /**
//     *@name    计算月份跨度
//     *@description 相关说明
//     *@time    创建时间:2022/10/20
//     *@param   qq 期起
//     *@param   qz 期止
//     *@return  月份跨度
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static int jsYfkd(LocalDate qq, LocalDate qz) {
//        int yfkd = 1;
//        if (qq != null && qz != null) {
//            //期起>期止，非法，跨度计为0
//            if (qq.compareTo(qz) > 0) {
//                yfkd = 0;
//                return yfkd;
//            }
//            final int qqYear = qq.getYear();//年
//            final int qzYear = qz.getYear();//年
//            final int qqMonth = qq.getMonthValue();// 月
//            final int qzMonth = qz.getMonthValue();// 月
//            yfkd = (qzYear - qqYear) * 12 + qzMonth - qqMonth + 1;//年间隔*12个月每年+月间隔+1
//        }
//        return yfkd;
//    }
//
//    /**
//     *
//     *@name    中文名称
//     *@Description 批量获取税收减免性质列表
//     *@Time
//     *@param djxh 登记序号
//     *@param swjgDm - 税务机关代码
//     *@param reqList - 输入参数:列表类型为Map,包括key值：zsxmDm、zspmDm、skssqq、skssqz、jmzlxDm
//     *@return - 税收减免性质
//     *@throws
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static List<Map<String, Object>> getSsjmxxDmListByZsxmDmAndSkssqForP2(String djxh,String swjgDm, List<Map<String, Object>> reqList)  {
//        final List<SBJmxxVO> ssjmspList=new ArrayList<SBJmxxVO>();
//        final Map<String, Object> csMap= new HashMap<String, Object>();
//        if (reqList != null && reqList.size() > 0) {
//            for (int i = 0; i < reqList.size(); i++) {
//                List<YHJmsspjgVO> ssjmspList1 = null ;
////                final String jmfsDm = "01" ; //
//                final Map<String, Object> reqMap = reqList.get(i);
//                final String zsxmDm = (String) reqMap.get("zsxmDm");
//                final String zspmDm = (String) reqMap.get("zspmDm");
//                final String skssqq = (String) reqMap.get("skssqq");
//                final String skssqz = (String) reqMap.get("skssqz");
//                HXZGYH00155Request request = new HXZGYH00155Request();
//                request.setDjxh(djxh);
//                request.setZgswskfjDm(swjgDm);
//                request.setJmqxq(skssqq);
//                request.setJmqxz(skssqz);
//                request.setZsxmDm(zsxmDm);
//                request.setJmfsDm("01");
//                Map<String,String> expend = new HashMap<>();
//                expend.put("sjjg" , swjgDm);
//                expend.put("sjry" , SjryUtil.getSjry(swjgDm));
//                HXZGYH00155Response invoke = null;
//                // final String skssqq = DateUtils.dateToString(GYCastUtils.parseDate((String) reqMap.get("skssqq"), 3));
//                //  final String skssqz = DateUtils.dateToString(SwordDateUtils.parseDate((String) reqMap.get("skssqz"), 3));
//                if (!csMap.containsKey(zsxmDm+zspmDm+skssqq+skssqz)){
//                    if("10113".equals(zsxmDm)){
//                        // ssjmspList1 = (List<YHJmsspjgVO>)BizServiceUtil.invokeBizService("SWZJ.HXZG.DJ.QUERYNSRJMBAXXFORYSL", djxh, zsxmDm,"",skssqq,skssqz,jmfsDm,swjgDm);
//                    }else{
//                        //ssjmspList1 = (List<YHJmsspjgVO>)BizServiceUtil.invokeBizService("SWZJ.HXZG.YH.QUERYNSRJMBAXX", djxh, zsxmDm,"",skssqq,skssqz,jmfsDm,swjgDm);
//                         invoke = Gt3Invoker.invoke("SWZJ.HXZG.YH.QUERYNSRJMBAXX",expend, request, HXZGYH00155Response.class);
//                    }
//                    csMap.put(zsxmDm+zspmDm+skssqq+skssqz,zsxmDm+zspmDm+skssqq+skssqz);
//                    if (ssjmspList1 != null && ssjmspList1.size() > 0) {
//                        final List<SBJmxxVO> sbJmxxVOList = BeanCopierUtil.copyList(invoke.getJmsspjgvoGrid().getJmsspjgvoGridlb(), SBJmxxVO.class);
//                        //保存，过滤选中的减免数据
//                        ssjmspList.addAll(sbJmxxVOList);
//                    }
//                }
//            }
//        }
//
//        if (reqList != null && reqList.size() > 0) {
//            for (int i = 0; i < reqList.size(); i++) {
//                final Map<String, Object> reqMap = reqList.get(i);
//                final String zsxmDm = (String) reqMap.get("zsxmDm");
//                final String zspmDm = (String) reqMap.get("zspmDm");
//                final String skssqq = (String) reqMap.get("skssqq");
//                final String skssqz = (String) reqMap.get("skssqz");
//                final String jmzlxDm = (String) reqMap.get("jmzlxDm");
//                final List<Map<String, Object>> ssjmxzList = getSsjmxzListByZsxmDmAndssq(swjgDm, zsxmDm, skssqq, skssqz);
//                if (zspmDm != null && !zspmDm.equals("")) {
//                    //根据征收品目进行过滤结果集
//                    filterJmxzListByZspmdm(ssjmxzList, zsxmDm, zspmDm);
//                }
//                if (jmzlxDm != null && !jmzlxDm.equals("")) {
//                    // 根据减免征类型代码过滤结果集
//                    filterJmxzListByJmzlxDm(ssjmxzList, jmzlxDm);
//                }
//                //将税收减免审批、备案结果与过滤后的结果进行合并
//                final List<Map<String, Object>> sortSsjmxzList = sortJmxzListBySsjmjgxxList(ssjmxzList, GYSbUtils.queryJmxxList(zsxmDm,skssqq,skssqz,ssjmspList,true), skssqq, skssqz);
//                //reBuildSsjmxzList方法根据减免性质查找税务事项时，会查询出其他项目的税务事项，后续根据减免性质过滤时可能会把需要的征收项目对应的税务事项过滤掉
////                reqMap.put("ssjmxzList",  reBuildSsjmxzList(sortSsjmxzList));
//                reqMap.put("ssjmxzList",  sortSsjmxzList);
//            }
//        }
//
//        return  reqList;
//    }
    /**
     *
     *@name    中文名称
     *@Description 根据参数表中的税收减免性质列表和税收减免审批、税收减免备案结果表中的减免信息，重新构造减免性质列表，将税收减免备案（审批）
     *              结果表中已存在的信息，排在最前面，并将税收减免审批、备案中的减征额度、减征幅度、减免（减按）税率返回。
     *@Time    创建时间:2015-6-8下午06:02:58
     *@param jmxzList - 参数表中减免性质列表
     *@param ssjmspList - 税收减免审批、税收减免备案的结果集
     @param skssqq 税款所属期起
     @param skssqz 税款所属期止
     *@return - 减免性质列表
     *@throws
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static List<Map<String, Object>> sortJmxzListBySsjmjgxxList(List<Map<String, Object>> jmxzList, List<SBJmxxVO> ssjmspList, String skssqq, String skssqz)  {
        final List<Map<String, Object>> jmxxListnew = new ArrayList<Map<String, Object>>();
        if (ssjmspList==null) {
            jmxxListnew.addAll(jmxzList);
            return jmxxListnew;
        }
        final Date skssqqDate = GyUtils.cast2Date(skssqq);
        final Date skssqzDate = GyUtils.cast2Date(skssqz);
        final List<Map<String, Object>> ssjmxzmcdzList =getSsjmxzmcdzList();
        for (SBJmxxVO sbJmxxVO : ssjmspList) {
            //当税收减免审批结果表中的减免性质为空时，跳过此循环
            if (sbJmxxVO.getSsjmxzhzDm() == null) {
                continue;
            }
            final String[] jmxzArray = sbJmxxVO.getSsjmxzhzDm().split(",");
            if (jmxzArray!=null) {
                for (int i = 0; i < jmxzArray.length; i++) {
                    final Map<String, Object> ssjmxzMap = new HashMap<String, Object>();
                    ssjmxzMap.put("ssjmxzDm", jmxzArray[i]);
                    //如果税收减免性质名称为空，则此减免性质已失效，不能使用。
                    final String ssjmxzmc = getSsjmxzMcByssjmxzDm(jmxzArray[i]);
                    if (ssjmxzmc == null) {
                        continue;
                    }
                    //ssjmxzMap.put("ssjmxzmc", getJmxmMcByJmxmDm(sbJmxxVO.getJmsspsxDm()) + "|" + getSsjmxzMcByssjmxzDm(jmxzArray[i])); 20210508 yangdalin
                    ssjmxzMap.put("ssjmxzmc", getJmxmMcByJmxmDm(sbJmxxVO.getJmsspsxDm()) + "|" + getSsjmxzmc(skssqzDate,jmxzArray[i],ssjmxzmcdzList));
                    ssjmxzMap.put("jmsl", sbJmxxVO.getJzsl());
                    ssjmxzMap.put("jmed", sbJmxxVO.getJzed());
                    ssjmxzMap.put("jmfd", sbJmxxVO.getJzfd());
                    ssjmxzMap.put("swsxDm", sbJmxxVO.getJmsspsxDm());
                    ssjmxzMap.put("jmzlxDm", sbJmxxVO.getJmzlxDm());
                    ssjmxzMap.put("jmqxq", sbJmxxVO.getJmqxq());
                    ssjmxzMap.put("jmqxz", sbJmxxVO.getJmqxz());
                    ssjmxzMap.put("sybh1", sbJmxxVO.getSybh1());
                    ssjmxzMap.put("spbaBz", "Y");
                    //2018-04-09 GDD44_201706210001 程序在获取减征额度的时候，没有过滤征收品目。房产税需优先根据品目匹配,处理备案到品目的场景
                    final String zsxmDm = sbJmxxVO.getZsxmDm();
                    if (GyConstants.getDmGyZsxmFcs().equals(zsxmDm) || GyConstants.getDmGyZsxmCztdsys().equals(zsxmDm)) {//房产税
                        ssjmxzMap.put("zspmDm", sbJmxxVO.getZspmDm());//返回征收品目代码
                    }
                    jmxxListnew.add(ssjmxzMap);
                    //ZOG00_201905270002 房土查往期和当期属期内的减免配置和备案。如果备案时间太短，会导致备案时间以外的属期无法享受减免。
                    final Date jmqxqDate = GyUtils.cast2Date(sbJmxxVO.getJmqxq());
                    final Date jmqxzDate = GyUtils.cast2Date(sbJmxxVO.getJmqxz());
                    for (int j = jmxzList.size() - 1; j >= 0; j--) {
                        final Map<String, Object> jmxzMap = jmxzList.get(j);
                        final String ssjmxzDm = GyUtils.getStringValue(jmxzMap.get("ssjmxzDm"));
                        if (ssjmxzDm.equals(jmxzArray[i])) {
                            //如果是房土，减免期限包含了属期才删除配置的重复减免性质
                            if (GyConstants.getDmGyZsxmFcs().equals(zsxmDm) || GyConstants.getDmGyZsxmCztdsys().equals(zsxmDm)) {
                                if (!GYObjectUtils.isNull(skssqqDate) && !GYObjectUtils.isNull(skssqzDate) && !jmqxqDate.after(skssqqDate) && !jmqxzDate.before(skssqzDate)) {
                                    jmxzList.remove(jmxzList.get(j));
                                }
                            } else {
                                jmxzList.remove(jmxzList.get(j));
                            }
                        }
                    }
                }
            }
        }
        jmxxListnew.addAll(jmxzList);
        return jmxxListnew;
    }

    /**
     *
     *@name    中文名称
     *@Description 根据税收减免性质代码获取税收减免性质名称
     *@Time    创建时间:2015-6-8下午09:37:58
     *@param ssjmxzDm - 税收减免性质代码
     *@return - 税收减免性质名称
     *@throws
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    public static String getSsjmxzMcByssjmxzDm(String ssjmxzDm)  {
        final Map<String, Object> ssjmxzMap = CacheUtils.getTableData("dm_gy_ssjmxz_yx", ssjmxzDm);// HxzgCacheUtils.getHcbMapByKey("DM_GY_SSJMXZ", ssjmxzDm);
        if (ssjmxzMap != null && ssjmxzMap.get("ssjmxzmc") != null) {
            return (String) ssjmxzMap.get("ssjmxzmc");
        }
        return "";
    }

    /**
     *
     *@name    获取减免税信息
     *@description 相关说明
     *@time    创建时间:2015-6-16下午9:29:58
     *@param sbzsxmDm 征收项目代码
     *@param sbskssqq 税款所属期起
     *@param sbskssqz 税款所属期止
     *@param jmxxList 减免信息列表
     *@param bz 标志
     *@return 减免List
     *@throws
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static List<SBJmxxVO> queryJmxxList(String sbzsxmDm,String sbskssqq,String sbskssqz, List<SBJmxxVO> jmxxList, boolean bz)   {
        final List<SBJmxxVO> yhJmxxVOList = new ArrayList<SBJmxxVO>();

        if(GYObjectUtils.isNull(jmxxList)){
            return yhJmxxVOList;
        }
        for (SBJmxxVO yhvo : jmxxList) {
            if (!sbzsxmDm.equals(yhvo.getZsxmDm())) {
                continue;
            }
            final String yhrqq = yhvo.getJmqxq();
            final String yhrqz = yhvo.getJmqxz();
            if (GYObjectUtils.isNull(yhrqq) || GYObjectUtils.isNull(yhrqz)) {
                continue;
            }
            //判断日期刚好在优惠中生效，否则不生效
            if (DateUtils.strToDate(sbskssqq).compareTo(DateUtils.strToDate(yhrqq) )>= 0 && DateUtils.strToDate(sbskssqz).compareTo(DateUtils.strToDate(yhrqz)) <= 0) {
                yhJmxxVOList.add(yhvo);
            }
        }
        return yhJmxxVOList;
    }
//
//    /**
//     *
//     *@name    中文名称
//     *@Description 根据征收品目过滤减免性质列表 ，根据参数表CS_GY_SSJMXZZSPMDZB规则进行过滤
//     *              CS_GY_SSJMXZZSPMDZB中配置了的税收减免性质，如果对应的征收品目代码与传入不匹配，则从结果集中去掉此税收减免性质
//     *@Time    创建时间:2015-6-8下午10:06:09
//     *@param ssjmxzList - 减免性质列表
//     *@param zsxmDm - 征收项目代码
//     *@param zspmDm - 征收品目代码
//     *@throws
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    private static void filterJmxzListByZspmdm(List<Map<String, Object>> ssjmxzList, String zsxmDm, String zspmDm)   {
//        if(zspmDm == null || zspmDm.equals(""))
//        {
//            return  ;
//        }
//        if(ssjmxzList == null || ssjmxzList.size() == 0)
//        {
//            return ;
//        }
//        if("10101".equals(zsxmDm)){//增值税因为存在两个货劳(或者服务)的品目，申报时过滤掉一条品目，只取一条品目，导致参数表中的货劳(服务)对应的减免性质取不全。
//            for (int i = ssjmxzList.size()-1; i >= 0 ; i--) {
//                final String ssjmxzDm = (String) ssjmxzList.get(i).get("ssjmxzDm");
//                final Map<String, Object> paramMap = new HashMap<String, Object>();
//                paramMap.put("    ", ssjmxzDm);
//                final List<Map<String, Object>> zspmCsList = CacheUtils.getTableData("CS_GY_SSJMXZZSPMDZB", paramMap);
//                if(zspmCsList != null && zspmCsList.size() > 0)
//                {
//                    boolean flag = false ;
//                    for(int j = 0 ; j < zspmCsList.size() ; j++)
//                    {
//                        final Map<String,Object> zspmcsMap = zspmCsList.get(j) ;
//                        final String csZspmDm = (String)zspmcsMap.get("ZSPM_DM") ;
//                        String comZspmDm = null ;
//                        if(csZspmDm.indexOf("%") == -1)
//                        {
//                            comZspmDm = csZspmDm ;
//                        }
//                        else
//                        {
//                            comZspmDm = csZspmDm.substring(0, csZspmDm.indexOf("%")) ;
//                        }
//                        if((zspmDm.startsWith("101016")||zspmDm.startsWith("101017")) && (comZspmDm.startsWith("101016")||comZspmDm.startsWith("101017")))
//                        {
//                            //如果是服务品目 ，需要加载所有的服务的减免性质代码
//                            flag = true ;
//                            break ;
//                        }else if(zspmDm.startsWith("10101") && !zspmDm.startsWith("101016") && !zspmDm.startsWith("101017")
//                                && comZspmDm.startsWith("10101") && !comZspmDm.startsWith("101016") && !comZspmDm.startsWith("101017")){
//                            //如果是货劳品目 ，需要加载所有的货劳的减免性质代码
//                            flag = true ;
//                            break ;
//                        }
//                    }
//                    if(!flag)
//                    {
//                        ssjmxzList.remove(i) ;
//                    }
//                }
//            }
//        }else{//非增值税还按照原来的逻辑正常根据品目过滤。
//            for (int i = ssjmxzList.size()-1; i >= 0 ; i--) {
//                final String ssjmxzDm = (String) ssjmxzList.get(i).get("ssjmxzDm");
//                final Map<String, Object> paramMap = new HashMap<String, Object>();
//                paramMap.put("SSJMXZ_DM", ssjmxzDm);
//                final List<Map<String, Object>> zspmCsList = CacheUtils.getTableData("CS_GY_SSJMXZZSPMDZB", paramMap);
//                if(zspmCsList != null && zspmCsList.size() > 0)
//                {
//                    boolean flag = false ;
//                    for(int j = 0 ; j < zspmCsList.size() ; j++)
//                    {
//                        final Map<String,Object> zspmcsMap = zspmCsList.get(j) ;
//                        final String csZspmDm = (String)zspmcsMap.get("ZSPM_DM") ;
//                        String comZspmDm = null ;
//                        if(csZspmDm.indexOf("%") == -1)
//                        {
//                            comZspmDm = csZspmDm ;
//                        }
//                        else
//                        {
//                            comZspmDm = csZspmDm.substring(0, csZspmDm.indexOf("%")) ;
//
//                        }
//                        if(zspmDm.startsWith(comZspmDm))
//                        {
//                            flag = true ;
//                            break ;
//                        }
//                    }
//                    if(!flag)
//                    {
//                        ssjmxzList.remove(i) ;
//                    }
//                }
//            }
//        }
//    }
//
//    /**
//     *
//     *@name    中文名称
//     *@Description 根据减免征类型代码过滤税收减免性质列表，根据参数表CS_GY_SSJMXZYJMZLXDZB规则进行过滤。
//     *              CS_GY_SSJMXZYJMZLXDZB中配置了的税收减免性质，如果对应的减免征类型代码与传入不匹配，则从结果集中去掉此税收减免性质
//     *@Time    创建时间:2015-6-8下午10:06:13
//     *@param ssjmxzList - 税收减免性质列表
//     *@param jmzlxdm - 减免征类型代码
//     *@throws
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    private static void filterJmxzListByJmzlxDm(List<Map<String, Object>> ssjmxzList, String jmzlxdm)   {
//        if(jmzlxdm == null || jmzlxdm.equals(""))
//        {
//            return ;
//        }
//        if(ssjmxzList == null || ssjmxzList.size() == 0)
//        {
//            return ;
//        }
//        for (int i = ssjmxzList.size()-1 ; i >=0 ; i--) {
//            final String ssjmxzDm = (String) ssjmxzList.get(i).get("ssjmxzDm");
//            final List<Map<String, Object>> jmzlxCsList = CacheUtils.getTableData("CS_GY_SSJMXZYJMZLXDZB", ssjmxzDm,"Y");
//            if(jmzlxCsList != null && jmzlxCsList.size() > 0)
//            {
//                boolean flag = false ;
//                for(int j = 0 ; j < jmzlxCsList.size() ; j++)
//                {
//                    final Map<String,Object> jmzlxcsMap = jmzlxCsList.get(j) ;
//                    final String csJmzlxDm = (String)jmzlxcsMap.get("JMZLX_DM") ;
//                    if(jmzlxdm.equals(csJmzlxDm))
//                    {
//                        flag = true ;
//                        break ;
//                    }
//                }
//                if(!flag)
//                {
//                    ssjmxzList.remove(i) ;
//                }
//            }
//        }
//
//
//    }
//
//    /**
//     * @param djxh 登记序号
//     * @return String 自然人标志
//     * @name 判断是会否为自然人
//     * @description 相关说明
//     * @time 创建时间: 2022-11-05
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String isZrr(Object djxh){
//        if (djxh == null) {
//            return null;
//        }
//        String djxhStr = "";
//        if (djxh instanceof BigDecimal) {
//            djxhStr = String.valueOf(djxh);
//        } else if (djxh instanceof String) {
//            djxhStr = (String) djxh;
//        }
//        String zrrBz = null;
//        if (djxhStr.startsWith("2") || djxhStr.startsWith("3")) {//为自然人
//            zrrBz = "Y";
//        }else{
//            zrrBz = "N";
//        }
//        return zrrBz;
//    }
//
//    /**
//     *
//     *@name    判断税务机关是否为计划单列市税务机关
//     *@description 相关说明
//     *@time    创建时间:2022-11-14
//     *@param swjgDm swjgDm
//     *@return 是否计划单列市
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean checkIsJhdlsSwjg(String swjgDm){
//        final String swjgDm4 = swjgDm.substring(1, 5);
//        return checkIsJhdls(swjgDm4);
//    }
//
//    /**
//     *
//     *@name    校验是否计划单列市
//     *@description 相关说明
//     *@time    创建时间:2022-11-14
//     *@param   xzqhszDm 行政区划数字代码
//     *@return 计划单列市标志
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean checkIsJhdls(String xzqhszDm){
//        return xzqhszDm.startsWith("2102") || //大连
//                xzqhszDm.startsWith("3302") || //宁波
//                xzqhszDm.startsWith("3502") || //厦门
//                xzqhszDm.startsWith("3702") || //青岛
//                xzqhszDm.startsWith("4403"); //深圳
//    }
//
//    /**
//     * 获取省级国地税机关代码
//     * 计划单列市取前5位后面补0，非计划单列市取前3位后面补0
//     * @param swjgDm
//     * @return
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getSjgdsjgdm(String swjgDm) {
//        if (GYObjectUtils.isNull(swjgDm) || swjgDm.length() < 11) {
//            return "";
//        }
//        String sjswjgDm = "";
//        final String xzqhDm = swjgDm.substring(1, swjgDm.length());
//        if(checkIsJhdls(xzqhDm)){
//            sjswjgDm = swjgDm.substring(0, 5) + "000000";
//        }else{
//            sjswjgDm = swjgDm.substring(0, 3) + "00000000";
//        }
//        return sjswjgDm;
//    }
//
//    /**
//     *@name    中文名称
//     *@Description 根据征收项目代码和税务机关代码获取税收减免性质列表
//     *@Time    创建时间:2015-6-27上午11:09:16
//     *@param zsxmDm - 征收项目代码
//     *@param jmxzDm - 税务机关代码
//     *@param swjgDm - 税务机关代码
//     *@param skssqq - 税款所属期 //add by yangdalin
//     *@param skssqz - 税款所属期 //add by yangdalin
//     *@return - 税收减免性质列表
//     *@throws SwordBaseCheckedException - 架构异常
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, String> getZdyJmxz(String zsxmDm, String jmxzDm, String swjgDm,String skssqq,String skssqz) {
//        if (GYObjectUtils.isNull(zsxmDm) || GYObjectUtils.isNull(swjgDm) || GYObjectUtils.isNull(jmxzDm)) {
//            return null;
//        }
//        final Map<String, String> ssjmxzMap = new HashMap<String, String>();
//        List<Map<String, Object>> csSbSwsxjmxzdzbList = Optional.ofNullable(cssDmcsService.getAllIndexData("CS_SB_SWSXJMXZDZB_ZDY")).orElseGet(ArrayList::new);
//        final List<Map<String, Object>> jmxzcsList = csSbSwsxjmxzdzbList.stream().filter(r -> jmxzDm.equals(r.get("SSJMXZ_DM")) && zsxmDm.equals(r.get("ZSXM_DM"))).collect(Collectors.toList());
//        if (jmxzcsList != null && jmxzcsList.size() > 0) {
//            //查找本机及本上级
//            final List<String> swjgDmList = findBjsjSwjgDmInCache(swjgDm);
//            for (int i = 0; i < jmxzcsList.size(); i++) {
//                final Map<String, Object> jmxzcsMap = jmxzcsList.get(i);
//                final String xybz = (String) jmxzcsMap.get("XYBZ");
//                final String yxbz = (String) jmxzcsMap.get("YXBZ");
//                final String comSwjgDm = (String) jmxzcsMap.get("SWJG_DM");
//                //只返回选用标志和有效标志为Y且税务机关代码为当前税务机关本机及本上级的数据
//                if ("Y".equals(xybz) && "Y".equals(yxbz) && swjgDmList.contains(comSwjgDm)) {
//                    final Map<String, Object> jmxzMap = CacheUtils.getTableData("DM_GY_SSJMXZ", jmxzDm);
//                    final Date yxqq = GyUtils.cast2Date(jmxzcsMap.get("YXQQ"));
//                    final Date yxqz = GyUtils.cast2Date(jmxzcsMap.get("YXQZ"));
//                    final Date ssqq = GYObjectUtils.isNull(skssqq) ? yxqq : GyUtils.cast2Date(skssqq);
//                    final Date ssqz = GYObjectUtils.isNull(skssqz) ? yxqz : GyUtils.cast2Date(skssqz);
//                    if (ssqq.compareTo(yxqz) <= 0 && ssqz.compareTo(yxqq) >= 0) {
//                        final String ssjmxzDm = (String) jmxzcsMap.get("SSJMXZ_DM");
//                        final String jmxmDm = (String) jmxzcsMap.get("SWSX_DM");
//                        ssjmxzMap.put("ssjmxzDm", ssjmxzDm);
//                        ssjmxzMap.put("swsxDm", jmxmDm);
//                        break;
//                    }
//                }
//            }
//        }
//        return ssjmxzMap;
//    }


    /**
     *@name    校验是否可以申报（十税合一不允许提前申报）
     *@description
     *1.如果税款所属期止小于当前时间，返回true表示可申报。
     *2.如果税款所属期止大于或等于当前时间，判断申报期限代码是否为期内申报，如果是，且当前时间是否大于或等于税款所属期起，返回true表示可以申报。
     *3.如果申报期限为“99其他”，则返回true表示可以申报。
     *4.如果纳税期限代码是“11次、12定额期、99其他”时，返回true表示可以申报。
     *5.不满足以上条件的，返回false，表示不可以申报。
     *@time    创建时间:2021年4月8日上午11:09:01
     *@param sbqxDm 申报期限代码
     *@param nsqxDm 纳税期限代码
     *@param skssqq 税款所属期起
     *@param skssqz 税款所属期止
     *@param sfjktqsb 是否监控提前申报
     *@return true:可申报/ false:不可申报
     *@throws Exception 异常
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static boolean checkSfksb(String sbqxDm, String nsqxDm, String skssqq, String skssqz, String sfjktqsb) {
        if(!"Y".equals(sfjktqsb)) {//不监控提前申报
            return true;
        }
        if ("99".equals(sbqxDm) || "11".equals(nsqxDm) || "12".equals(nsqxDm) || "99".equals(nsqxDm)) {
            return true;
        }
        if (skssqq.equals(skssqz)) {//属期起止相等，为按次申报
            return true;
        }
        final Date dqrqDate = GyUtils.cast2Date(new Date());
        final Date skssqzDate = GyUtils.cast2Date(skssqz);
        if (skssqzDate.before(dqrqDate)) {
            return true;
        }
        final Date skssqqDate = GyUtils.cast2Date(skssqq);
        if (GYSbUtils.checkIsQnsb(sbqxDm) && !dqrqDate.before(skssqqDate)) {
            return true;
        }
        return false;
    }

    /**
     *@name    校验是否期内申报
     *@description 相关说明
     *@time    创建时间:2021年4月8日上午10:52:34
     *@param sbqxDm 申报期限
     *@return true:期内申报/false:不是期内申报
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static boolean checkIsQnsb(String sbqxDm) {
        if (GyUtils.isNull(sbqxDm)) {
            return false;
        } else {
            return "09".equals(sbqxDm) || "20".equals(sbqxDm) || "21".equals(sbqxDm) || "22".equals(sbqxDm) || "23".equals(sbqxDm) || "24".equals(sbqxDm) || "25".equals(sbqxDm) || "26".equals(sbqxDm)
                    || "27".equals(sbqxDm) || "30".equals(sbqxDm) || "31".equals(sbqxDm) || "32".equals(sbqxDm) || "33".equals(sbqxDm) || "34".equals(sbqxDm) || "35".equals(sbqxDm)
                    || "37".equals(sbqxDm) || "38".equals(sbqxDm) || "39".equals(sbqxDm) || "40".equals(sbqxDm) || "49".equals(sbqxDm) || "54".equals(sbqxDm) || "55".equals(sbqxDm)
                    || "56".equals(sbqxDm) || "59".equals(sbqxDm) || "64".equals(sbqxDm) || "65".equals(sbqxDm) || "66".equals(sbqxDm) || "72".equals(sbqxDm) || "73".equals(sbqxDm)
                    || "75".equals(sbqxDm) || "76".equals(sbqxDm) || "77".equals(sbqxDm) || "78".equals(sbqxDm) || "79".equals(sbqxDm) || "80".equals(sbqxDm) || "81".equals(sbqxDm)
                    || "82".equals(sbqxDm) || "86".equals(sbqxDm) || "87".equals(sbqxDm) || "88".equals(sbqxDm);
        }
    }

}
