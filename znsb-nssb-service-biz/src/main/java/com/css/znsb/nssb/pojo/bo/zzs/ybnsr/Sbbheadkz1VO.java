package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 通用申报表表头扩展，增加申报类型，日期
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbheadkz1VO", propOrder = { "sbsxDm1", "sbrq1" })
@Getter
@Setter
public class Sbbheadkz1VO extends SbbheadVO {
    /**
     * 申报类型代码
     */
    @XmlElement(nillable = true, required = true)
    protected String sbsxDm1;

    /**
     * 申报日期
     */
    @XmlElement(nillable = true, required = true)
    protected String sbrq1;
}