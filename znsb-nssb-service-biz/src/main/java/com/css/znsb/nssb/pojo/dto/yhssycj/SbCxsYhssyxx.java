package com.css.znsb.nssb.pojo.dto.yhssycj;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 印花税税源采集表
 * @TableName sb_cxs_yhssyxx
 */
@TableName(value ="sb_cxs_yhssyxx")
@Data
public class SbCxsYhssyxx implements Serializable {
    /**
     * UUID||uuid
     */
    @TableId
    private String uuid;

    @TableField("djxh")
    private String djxh;
    /**
     * 业务渠道代码
     */
    @TableField("ywqd_dm")
    private String ywqdDm;

    /**
     * 录入日期
     */
    @TableField("lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField("xgrq")
    private Date xgrq;

    /**
     * 数据产生地区
     */
    @TableField("sjcsdq")
    private String sjcsdq;

    /**
     * 数据归属地区
     */
    @TableField("sjgsdq")
    private String sjgsdq;

    /**
     * 修改人身份id
     */
    @TableField("xgrsfid")
    private String xgrsfid;

    /**
     * 录入人身份id
     */
    @TableField("lrrsfid")
    private String lrrsfid;

    /**
     * 数据同步时间
     */
    @TableField("sjtb_sj")
    private Date sjtbSj;

    /**
     * 主表UUID
     */
    @TableField("zbuuid")
    private String zbuuid;

    /**
     * 税款所属期起
     */
    @TableField("skssqq")
    private Date skssqq;

    /**
     * 税款所属期止
     */
    @TableField("skssqz")
    private Date skssqz;

    /**
     * 纳税期限代码
     */
    @TableField("nsqx_dm")
    private String nsqxDm;

    /**
     * 核定比例
     */
    @TableField("hdbl")
    private BigDecimal hdbl;

    /**
     * 税率
     */
    @TableField("sl_1")
    private BigDecimal sl1;

    /**
     * 已缴税额
     */
    @TableField("yjse")
    private BigDecimal yjse;

    /**
     * 应纳税额
     */
    @TableField("ynse")
    private BigDecimal ynse;

    /**
     * 征收品目代码
     */
    @TableField("zspm_dm")
    private String zspmDm;

    /**
     * 税收减免性质代码
     */
    @TableField("ssjmxz_dm")
    private String ssjmxzDm;

    /**
     * 减免税额
     */
    @TableField("jmse")
    private BigDecimal jmse;

    /**
     * 应纳税凭证编号
     */
    @TableField("ynspzbh")
    private String ynspzbh;

    /**
     * 应纳税凭证书立(领受)日期
     */
    @TableField("ynspzsllsrq")
    private Date ynspzsllsrq;

    /**
     * 计税金额或件数
     */
    @TableField("jsjehjs")
    private BigDecimal jsjehjs;

    /**
     * 核定类型||1为定额核定、2为比例核定
     */
    @TableField("hdlx_2")
    private String hdlx2;

    /**
     * 作废标志
     */
    @TableField("zfbz_1")
    private String zfbz1;

    /**
     * 作废人代码
     */
    @TableField("zfr_dm")
    private String zfrDm;

    /**
     * 作废日期
     */
    @TableField("zfrq_1")
    private Date zfrq1;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SbCxsYhssyxx other = (SbCxsYhssyxx) that;
        return (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
                && (this.getDjxh() == null ? other.getDjxh() == null : this.getDjxh().equals(other.getDjxh()))
            && (this.getYwqdDm() == null ? other.getYwqdDm() == null : this.getYwqdDm().equals(other.getYwqdDm()))
            && (this.getLrrq() == null ? other.getLrrq() == null : this.getLrrq().equals(other.getLrrq()))
            && (this.getXgrq() == null ? other.getXgrq() == null : this.getXgrq().equals(other.getXgrq()))
            && (this.getSjcsdq() == null ? other.getSjcsdq() == null : this.getSjcsdq().equals(other.getSjcsdq()))
            && (this.getSjgsdq() == null ? other.getSjgsdq() == null : this.getSjgsdq().equals(other.getSjgsdq()))
            && (this.getXgrsfid() == null ? other.getXgrsfid() == null : this.getXgrsfid().equals(other.getXgrsfid()))
            && (this.getLrrsfid() == null ? other.getLrrsfid() == null : this.getLrrsfid().equals(other.getLrrsfid()))
            && (this.getSjtbSj() == null ? other.getSjtbSj() == null : this.getSjtbSj().equals(other.getSjtbSj()))
            && (this.getZbuuid() == null ? other.getZbuuid() == null : this.getZbuuid().equals(other.getZbuuid()))
            && (this.getSkssqq() == null ? other.getSkssqq() == null : this.getSkssqq().equals(other.getSkssqq()))
            && (this.getSkssqz() == null ? other.getSkssqz() == null : this.getSkssqz().equals(other.getSkssqz()))
            && (this.getNsqxDm() == null ? other.getNsqxDm() == null : this.getNsqxDm().equals(other.getNsqxDm()))
            && (this.getHdbl() == null ? other.getHdbl() == null : this.getHdbl().equals(other.getHdbl()))
            && (this.getSl1() == null ? other.getSl1() == null : this.getSl1().equals(other.getSl1()))
            && (this.getYjse() == null ? other.getYjse() == null : this.getYjse().equals(other.getYjse()))
            && (this.getYnse() == null ? other.getYnse() == null : this.getYnse().equals(other.getYnse()))
            && (this.getZspmDm() == null ? other.getZspmDm() == null : this.getZspmDm().equals(other.getZspmDm()))
            && (this.getSsjmxzDm() == null ? other.getSsjmxzDm() == null : this.getSsjmxzDm().equals(other.getSsjmxzDm()))
            && (this.getJmse() == null ? other.getJmse() == null : this.getJmse().equals(other.getJmse()))
            && (this.getYnspzbh() == null ? other.getYnspzbh() == null : this.getYnspzbh().equals(other.getYnspzbh()))
            && (this.getYnspzsllsrq() == null ? other.getYnspzsllsrq() == null : this.getYnspzsllsrq().equals(other.getYnspzsllsrq()))
            && (this.getJsjehjs() == null ? other.getJsjehjs() == null : this.getJsjehjs().equals(other.getJsjehjs()))
            && (this.getHdlx2() == null ? other.getHdlx2() == null : this.getHdlx2().equals(other.getHdlx2()))
            && (this.getZfbz1() == null ? other.getZfbz1() == null : this.getZfbz1().equals(other.getZfbz1()))
            && (this.getZfrDm() == null ? other.getZfrDm() == null : this.getZfrDm().equals(other.getZfrDm()))
            && (this.getZfrq1() == null ? other.getZfrq1() == null : this.getZfrq1().equals(other.getZfrq1()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getDjxh() == null) ? 0 : getDjxh().hashCode());
        result = prime * result + ((getYwqdDm() == null) ? 0 : getYwqdDm().hashCode());
        result = prime * result + ((getLrrq() == null) ? 0 : getLrrq().hashCode());
        result = prime * result + ((getXgrq() == null) ? 0 : getXgrq().hashCode());
        result = prime * result + ((getSjcsdq() == null) ? 0 : getSjcsdq().hashCode());
        result = prime * result + ((getSjgsdq() == null) ? 0 : getSjgsdq().hashCode());
        result = prime * result + ((getXgrsfid() == null) ? 0 : getXgrsfid().hashCode());
        result = prime * result + ((getLrrsfid() == null) ? 0 : getLrrsfid().hashCode());
        result = prime * result + ((getSjtbSj() == null) ? 0 : getSjtbSj().hashCode());
        result = prime * result + ((getZbuuid() == null) ? 0 : getZbuuid().hashCode());
        result = prime * result + ((getSkssqq() == null) ? 0 : getSkssqq().hashCode());
        result = prime * result + ((getSkssqz() == null) ? 0 : getSkssqz().hashCode());
        result = prime * result + ((getNsqxDm() == null) ? 0 : getNsqxDm().hashCode());
        result = prime * result + ((getHdbl() == null) ? 0 : getHdbl().hashCode());
        result = prime * result + ((getSl1() == null) ? 0 : getSl1().hashCode());
        result = prime * result + ((getYjse() == null) ? 0 : getYjse().hashCode());
        result = prime * result + ((getYnse() == null) ? 0 : getYnse().hashCode());
        result = prime * result + ((getZspmDm() == null) ? 0 : getZspmDm().hashCode());
        result = prime * result + ((getSsjmxzDm() == null) ? 0 : getSsjmxzDm().hashCode());
        result = prime * result + ((getJmse() == null) ? 0 : getJmse().hashCode());
        result = prime * result + ((getYnspzbh() == null) ? 0 : getYnspzbh().hashCode());
        result = prime * result + ((getYnspzsllsrq() == null) ? 0 : getYnspzsllsrq().hashCode());
        result = prime * result + ((getJsjehjs() == null) ? 0 : getJsjehjs().hashCode());
        result = prime * result + ((getHdlx2() == null) ? 0 : getHdlx2().hashCode());
        result = prime * result + ((getZfbz1() == null) ? 0 : getZfbz1().hashCode());
        result = prime * result + ((getZfrDm() == null) ? 0 : getZfrDm().hashCode());
        result = prime * result + ((getZfrq1() == null) ? 0 : getZfrq1().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", uuid=").append(uuid);
        sb.append(", djxh=").append(djxh);
        sb.append(", ywqdDm=").append(ywqdDm);
        sb.append(", lrrq=").append(lrrq);
        sb.append(", xgrq=").append(xgrq);
        sb.append(", sjcsdq=").append(sjcsdq);
        sb.append(", sjgsdq=").append(sjgsdq);
        sb.append(", xgrsfid=").append(xgrsfid);
        sb.append(", lrrsfid=").append(lrrsfid);
        sb.append(", sjtbSj=").append(sjtbSj);
        sb.append(", zbuuid=").append(zbuuid);
        sb.append(", skssqq=").append(skssqq);
        sb.append(", skssqz=").append(skssqz);
        sb.append(", nsqxDm=").append(nsqxDm);
        sb.append(", hdbl=").append(hdbl);
        sb.append(", sl1=").append(sl1);
        sb.append(", yjse=").append(yjse);
        sb.append(", ynse=").append(ynse);
        sb.append(", zspmDm=").append(zspmDm);
        sb.append(", ssjmxzDm=").append(ssjmxzDm);
        sb.append(", jmse=").append(jmse);
        sb.append(", ynspzbh=").append(ynspzbh);
        sb.append(", ynspzsllsrq=").append(ynspzsllsrq);
        sb.append(", jsjehjs=").append(jsjehjs);
        sb.append(", hdlx2=").append(hdlx2);
        sb.append(", zfbz1=").append(zfbz1);
        sb.append(", zfrDm=").append(zfrDm);
        sb.append(", zfrq1=").append(zfrq1);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}