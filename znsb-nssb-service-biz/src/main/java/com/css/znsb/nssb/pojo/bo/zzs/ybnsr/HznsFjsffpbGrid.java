package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 汇总纳税附加税费分配表Grid
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hznsFjsffpbGrid", propOrder = { "hznsFjsffpbGridlb" })
@Getter
@Setter
public class HznsFjsffpbGrid {
    protected List<SbFjsfHznsfjsffpbVO> hznsFjsffpbGridlb;

    /**
     * Gets the value of the hznsFjsffpbGridlb property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the hznsFjsffpbGridlb property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getHznsFjsffpbGridlb().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SbFjsfHznsfjsffpbVO}
     */
    public List<SbFjsfHznsfjsffpbVO> getHznsFjsffpbGridlb() {
        if (hznsFjsffpbGridlb == null) {
            hznsFjsffpbGridlb = new ArrayList<SbFjsfHznsfjsffpbVO>();
        }
        return this.hznsFjsffpbGridlb;
    }
}