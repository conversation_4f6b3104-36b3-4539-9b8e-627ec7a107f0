package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.nssb.service.zbtj.ZbtjJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class ZbtjJob {


    @Resource
    private ZbtjJobService zbtjJobService;

    /**
     * 申报数据归集
     */
    @XxlJob("zbtjJob")
    public void execute() {
        log.info("==========开始进行指标统计数据归集==========");
        String jobParam = XxlJobHelper.getJobParam();
        Map<String,Object> jobParamMap = JsonUtils.toMap(jobParam);
        zbtjJobService.initJob(String.valueOf(jobParamMap.get("zbbm")),String.valueOf(jobParamMap.get("tjqjq")),String.valueOf(jobParamMap.get("tjqjz")), String.valueOf(jobParamMap.get("nsrsbh")));
        log.info("==========指标统计数据归集完成==========");
    }

}
