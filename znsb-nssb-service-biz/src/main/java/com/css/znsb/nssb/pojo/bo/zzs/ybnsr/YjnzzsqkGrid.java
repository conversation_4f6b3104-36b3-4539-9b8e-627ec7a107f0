package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 已缴纳增值税情况Grid
 *
 * <p>yjnzzsqkGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="yjnzzsqkGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="yjnzzsqkGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}yjnzzsqkGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "yjnzzsqkGrid", propOrder = { "yjnzzsqkGridlbVO" })
public class YjnzzsqkGrid {
    @XmlElement(nillable = true, required = true)
    protected List<YjnzzsqkGridlbVO> yjnzzsqkGridlbVO;

    /**
     * Gets the value of the yjnzzsqkGridlbVO property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the yjnzzsqkGridlbVO property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getYjnzzsqkGridlbVO().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link YjnzzsqkGridlbVO}
     */
    public List<YjnzzsqkGridlbVO> getYjnzzsqkGridlbVO() {
        if (yjnzzsqkGridlbVO == null) {
            yjnzzsqkGridlbVO = new ArrayList<YjnzzsqkGridlbVO>();
        }
        return this.yjnzzsqkGridlbVO;
    }
}