package com.css.znsb.nssb.pojo.dto.sb.core.support.sbbc;

import com.css.znsb.nssb.constants.enums.YesOrNoEnum;
import com.css.znsb.nssb.pojo.dto.sb.core.support.common.SbJmxxDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString(callSuper = true)
public class SbBcJmxxRequestDto extends SbJmxxDto {

    /**
     * 税源编号
     */
    private String sybh1;

    /**
     * 税收减免申报栏目代码
     */
    private String ssjmsblmDm;

    /**
     * 减免收入
     */
    private BigDecimal jmsr;

    /**
     * 减免税额
     */
    private BigDecimal jmse;

    /**
     * 税款所属期起
     */
    private Date skssqq;

    /**
     * 税款所属期止
     */
    private Date skssqz;

    /**
     * 征前标志
     */
    private String zqbz;

    /**
     * 折算标志
     */
    private String zsbz;

    /**
     * 税款所属税务机关代码
     */
    private String skssswjgDm;

    /**
     * 街道乡镇代码
     */
    private String jdxzDm;

    /**
     * 是否规则定义
     */
    private String ruleDefined = YesOrNoEnum.N.getCode();

}
