package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税纳税申报表附表一（本期销售情况明细表）》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr01_bqxsqkmxb", propOrder = { "sbbhead", "bqxsqkmxbGrid" })
@Getter
@Setter
public class Zzssyyybnsr01Bqxsqkmxb {
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 增值税纳税申报表附列资料（附表一）
     */
    @XmlElement(nillable = true, required = true)
    protected BqxsqkmxbGrid bqxsqkmxbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "bqxsqkmxbGridlbVO" })
    @Getter
    @Setter
    public static class BqxsqkmxbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<BqxsqkmxbGridlbVO> bqxsqkmxbGridlbVO;

        /**
         * Gets the value of the bqxsqkmxbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the bqxsqkmxbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getBqxsqkmxbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link BqxsqkmxbGridlbVO}
         */
        public List<BqxsqkmxbGridlbVO> getBqxsqkmxbGridlbVO() {
            if (bqxsqkmxbGridlbVO == null) {
                bqxsqkmxbGridlbVO = new ArrayList<BqxsqkmxbGridlbVO>();
            }
            return this.bqxsqkmxbGridlbVO;
        }
    }
}