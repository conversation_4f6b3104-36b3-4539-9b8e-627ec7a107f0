package com.css.znsb.nssb.service.cchxwssb.plsb.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.Base64Utils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.nsrxx.*;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjbMapper;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDOMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjb;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDo;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.*;
import com.css.znsb.nssb.pojo.dto.cchxwssb.plsb.CxsYsbjgbDTO;
import com.css.znsb.nssb.pojo.dto.gy.lqpt.jbrxx.GyJbrxxDTO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.*;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.BqjmsemxbGridlbVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.Bqjmsemxbywbw;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.Bqjmsemxbywbw.Bqjmsemxb;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.HXZGSB10735Response;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.HXZGSB10735Response.JmxxGrid;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.HXZGSB10735Response.SbxxGrid;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.SbxxGridlbVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.sbxxgrid.SymxGridlbVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.gy.CxstysbNsrxx;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.gy.SbCxstysbSlxx;
import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.CchxwshbsbService;
import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.CchxwsnssbService;
import com.css.znsb.nssb.service.cchxwssb.plsb.CxsYsbsjcjService;
import com.css.znsb.nssb.service.cchxwssb.plsb.ZnsbNssbYsbjgbCchxwsService;
import com.css.znsb.nssb.utils.CchxwsnssbGyUtils;
import com.css.znsb.nssb.utils.GyJbrxxUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.css.znsb.nssb.constants.enums.YzpzzlEnum.CXS;
import static com.css.znsb.nssb.constants.enums.ZsxmDmEnum.CZTDSYS;
import static com.css.znsb.nssb.constants.enums.ZsxmDmEnum.FCS;

@Slf4j
@Validated
@Service
public class CxsYsbsjclServiceImpl implements CxsYsbsjcjService {

    @Resource
    private CompanyApi companyApi;

    @Resource
    private ZnsbNssbYsbjgbCchxwsService ysbjgbService;

    @Resource
    NsrxxApi nsrxxApi;

    @Resource
    ZnsbNssbSbrwMapper znsbNssbSbrwMapper;

    @Resource
    CchxwsnssbService cchxwsnssbService;

    @Resource
    CchxwshbsbService cchxwshbsbService;
    @Resource
    ZnsbNssbCxsTdsyxxcjbDOMapper tdsyxxcjbDOMapper;
    @Resource
    ZnsbCxsFyxxcjbMapper cxsFyxxcjbMapper;

    /**
     * 处理预申报数据
     * 该方法用于处理特定日期的预申报数据，主要流程包括：
     * 1. 查询当前日期待申报的任务
     * 2. 对每个任务，查询并保存相应的预填数据
     */
    @Override
    public void handlerYsbsj() {
        // 获取当前日期，格式为YYYYMM
        final String sbny = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));

        // 查询当前日期待申报的任务列表
        final List<ZnsbNssbSbrwDO> sbrwDOList = znsbNssbSbrwMapper.queryCxsDsbrwBySbny(sbny);
        // 如果任务列表为空，则直接返回
        if (GyUtils.isNull(sbrwDOList)) {
            return;
        }
        // 遍历待申报任务列表
        for (ZnsbNssbSbrwDO sbrwDO : sbrwDOList) {
            // 如果税源UUID为空，则跳过当前任务
            if (GyUtils.isNull(sbrwDO.getSyuuid())) {
                continue;
            }
            // 如果为房产税且房源编号为空，则跳过当前任务
            if (FCS.getCode().equals(sbrwDO.getZsxmDm()) && GyUtils.isNull(sbrwDO.getFybh())) {
                continue;
            }

            this.saveOrUpdateYsbjgb(sbrwDO);
        }
    }

    /**
     * 保存或更新预申报数据
     *
     * @param sbrwDO 采集任务对象，包含预申报所需的信息
     * @return 返回一个包含操作结果代码和消息的映射
     */
    @Override
    public Map<String,String> saveOrUpdateYsbjgb(ZnsbNssbSbrwDO sbrwDO) {
        final Map<String,String> resultMap = new HashMap<>();
        String code = "0";
        String msg = "保存预申报数据成功";
        try {
            // 判断sbrwuuid是否为空，为空则根据syuuid和skssq查询
            if (GyUtils.isNull(sbrwDO.getSbrwuuid())){
                sbrwDO = znsbNssbSbrwMapper.querySbrwBySyuuid(sbrwDO.getSkssqq(),
                        sbrwDO.getSkssqz(),sbrwDO.getSyuuid());

                if (GyUtils.isNull(sbrwDO)){
                    log.info("根据syuuid:{}查询申报任务为空！", sbrwDO.getSyuuid());
                    resultMap.put("code","1");
                    resultMap.put("msg","根据syuuid:" + sbrwDO.getSyuuid() + "查询申报任务为空！");
                    return resultMap;
                }
            }

            // 保存预申报数据
            final CxsYsbjgbDTO ysbjgbDTO = this.queryYsbsj(sbrwDO);
            // 如果预填数据为空，则跳过当前任务
            if (GyUtils.isNull(ysbjgbDTO)) {
                code = "1";
                msg = "预填数据为空";
            }
            // 保存或更新预填数据
            ysbjgbService.saveOrUpdateYsbjgb(ysbjgbDTO);

            // 保存暂存表数据
            cchxwsnssbService.saveGzlzcb(ysbjgbDTO.getCchxwsnssbGzlVO(),GyUtils.getUuid());
        } catch (Exception e) {
            code = "1";
            msg = "处理预申报数据异常，任务uuid："+ sbrwDO.getSbrwuuid() + "，异常信息：" + e.getMessage();
            log.error("处理预申报数据异常，任务uuid：{}", sbrwDO.getSbrwuuid(), e);
        }finally {
            resultMap.put("code",code);
            resultMap.put("msg",msg);
        }
        return resultMap;
    }


    /**
     * 查询预申报数据
     *
     * @param sbrwDO 任务信息对象，包含纳税人识别号和税源UUID等信息
     * @return 预申报数据DTO对象，包含预申报所需的各种信息
     * @throws Exception 如果查询过程中发生错误，则抛出异常
     */
    private CxsYsbjgbDTO queryYsbsj(ZnsbNssbSbrwDO sbrwDO) throws Exception {
        if (GyUtils.isNull(sbrwDO.getNsrsbh()) || GyUtils.isNull(sbrwDO.getSyuuid())){
            return null;
        }

        // 财行税统一申报纳税人信息
        final CxstysbNsrxx cxstysbNsrxx = new CxstysbNsrxx();

        // 财行税统一申报受理信息
        final SbCxstysbSlxx sbCxstysbSlxx = new SbCxstysbSlxx();

        // 获取企业基本信息
        final ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO = this.getQyJbxx(sbrwDO.getDjxh());

        // 组装财行税统一申报纳税人信息和受理信息
        this.handlerCxstysbNsrxxDTO(sbrwDO, cxstysbNsrxx, qyjbxxmxResVO);

        // 组装税源信息
        final String syuuid = sbrwDO.getSyuuid();
        final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
        SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
        symxGridlbVO.setSyuuid(syuuid);
        symxGridlb.add(symxGridlbVO);

        // 获取财行税预填数据
        final CxsQuerymxInitDTO cxsQuerymxInitDTO = this.handlerCxsYtReqDTO(sbrwDO, cxstysbNsrxx, symxGridlb);
        final HXZGSB10735Response hxzgsb10735Response = cchxwshbsbService.getCxsSbYtsj(sbrwDO, cxsQuerymxInitDTO);

        // 提取申报信息
        final SbxxGrid sbxxGrid = Optional.ofNullable(hxzgsb10735Response)
                .map(HXZGSB10735Response::getSbxxGrid)
                .orElse(null);

        // 如果申报信息网格为空，则记录错误日志并返回null
        if (GyUtils.isNull(sbxxGrid)) {
            log.error("{}:财行税预填数据为空!", sbrwDO.getSbrwuuid());
            return null;
        }

        // 提取申报信息列表
        final List<SbxxGridlbVO> sbxxGridlbVOList = sbxxGrid.getSbxxGridlb();

        // 提取减免信息
        final JmxxGrid jmxxGrid = hxzgsb10735Response.getJmxxGrid();
        final List<Bqjmsemxbywbw> bqjmsemxbywbwList = Optional.ofNullable(jmxxGrid)
                .map(JmxxGrid::getJmxxGridlb)
                .filter(gridlb -> !gridlb.isEmpty())
                .orElse(null);

        // 组装预申报报文
        final CxsRequestDTO cxsRequestDTO = this.handlerCxsSbBw(sbrwDO, cxstysbNsrxx, sbCxstysbSlxx, qyjbxxmxResVO);
        if (GyUtils.isNull(cxsRequestDTO)){
            return null;
        }
        // 将预申报报文转换为JSON字符串
        final String ysbsj = JsonUtils.toJson(cxsRequestDTO);

        // 组装暂存信息报文
        final CchxwsnssbGzlVO cchxwsnssbGzlVO = this.handlerZcxx(sbrwDO, cxstysbNsrxx, sbxxGridlbVOList,bqjmsemxbywbwList,cxsRequestDTO);
        if (GyUtils.isNull(cchxwsnssbGzlVO)){
            return null;
        }

        // 计算应补退税额
        final Map<String,BigDecimal> ybtseMap = this.getYbtse(sbxxGridlbVOList,bqjmsemxbywbwList, sbrwDO);

        log.info("四舍五入后的ybtse:{}",ybtseMap);

        // 组装预填数据
        return getYsbjgbDTO(ysbsj, ybtseMap, sbrwDO, cchxwsnssbGzlVO);
    }

    /**
     * 处理暂存信息
     *
     * @param sbrwDO            任务信息对象，包含任务相关数据
     * @param cxstysbNsrxx      纳税人信息对象，包含纳税人相关数据
     * @param sbxxGridlbVOList  申报信息列表，用于组装申报表格
     * @param bqjmsemxbywbwList 减免信息列表，用于组装减免附表
     * @param cxsRequestDTO
     * @return 返回处理后的综合测验视图对象，如果处理失败则返回null
     * @throws Exception 如果处理过程中发生错误，则抛出异常
     */
    private CchxwsnssbGzlVO handlerZcxx(ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx, List<SbxxGridlbVO> sbxxGridlbVOList,
                                        List<Bqjmsemxbywbw> bqjmsemxbywbwList, CxsRequestDTO cxsRequestDTO) throws Exception {

        // 初始化请求对象
        final CchxwsnssbInitReqVO cchxwsnssbInitReqVO = new CchxwsnssbInitReqVO();
        // 设置公共属性
        this.setCommonProperties(cchxwsnssbInitReqVO, sbrwDO, cxstysbNsrxx);

        // 调用财行税初始化接口获取初始化信息
        final CchxwsnssbInitResVO initResVO = cchxwsnssbService.cxsInit(cchxwsnssbInitReqVO);
        // 检查初始化信息是否为空
        if (GyUtils.isNull(initResVO) || GyUtils.isNull(initResVO.getInitResMxVOList())) {
            return null;
        }

        // 过滤初始化结果明细列表，获取当前税种的初始化信息
        final List<CchxwsnssbInitResMxVO> initMxVOList = this.filterInitResMxVOList(initResVO.getInitResMxVOList(), sbrwDO, cxstysbNsrxx);

        // 组装主表和减免附表信息
        final CchxwsnssbbxxVO sbbxxVO = cchxwsnssbService.handleZfbmxList(sbxxGridlbVOList, bqjmsemxbywbwList, sbrwDO.getZsxmDm());
        final List<CchxwsnssbzbmxVO> zbmxmcVOList = sbbxxVO.getCchxwsnssbzbmxVOList();
        final List<CchxwsnssbfbmxVO> fbmxmcVOList = sbbxxVO.getCchxwsnssbfbmxVOList();

        // 创建并组装返回对象
        final CchxwsnssbGzlVO cchxwsnssbGzlVO = new CchxwsnssbGzlVO();
        // 设置公共属性
        this.setCommonProperties(cchxwsnssbGzlVO, sbrwDO, cxstysbNsrxx);
        // 设置唯一标识符列表
        cchxwsnssbGzlVO.setSyuuid(Collections.singletonList(sbrwDO.getSyuuid()));
        // 设置初始化结果明细列表
        cchxwsnssbGzlVO.setInitResMxVOList(initMxVOList);
        // 设置主表明细视图列表
        cchxwsnssbGzlVO.setCchxwsnssbzbmxVOList(zbmxmcVOList);
        // 设置减免附表明细视图列表
        cchxwsnssbGzlVO.setCchxwsnssbfbmxVOList(fbmxmcVOList);
        // 设置减免附表明细视图列表
        cchxwsnssbGzlVO.setCxsRequestDTO(cxsRequestDTO);
        // 返回组装后的对象
        return cchxwsnssbGzlVO;
    }

    /**
     * 设置公共属性
     * 该方法用于将任务信息和纳税人信息中的相关属性赋值给目标对象
     * 这些属性在不同的场景下被共同使用，故称为公共属性
     *
     * @param target 目标对象，用于接收公共属性的设置
     * @param sbrwDO 任务信息对象，包含任务相关的属性，如电子凭证号和纳税人识别号
     * @param cxstysbNsrxx 纳税人信息对象，包含纳税人的起始和终止日期
     */
    private void setCommonProperties(CchxwsnssbInitReqVO target, ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx) {
        target.setDjxh(sbrwDO.getDjxh());
        target.setNsrsbh(sbrwDO.getNsrsbh());
        target.setCsskssqq(cxstysbNsrxx.getSkssqq());
        target.setCsskssqz(cxstysbNsrxx.getSkssqz());
    }

    /**
     * 设置公共属性
     * 该方法用于将任务信息和纳税人信息中的相关属性赋值给目标对象
     * 这些属性在不同的场景下被共同使用，故称为公共属性
     *
     * @param target 目标对象，用于接收公共属性的设置
     * @param sbrwDO 任务信息对象，包含任务相关的属性，如电子凭证号和纳税人识别号
     * @param cxstysbNsrxx 纳税人信息对象，包含纳税人的起始和终止日期
     */
    private void setCommonProperties(CchxwsnssbGzlVO target, ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx) {
        target.setDjxh(sbrwDO.getDjxh());
        target.setNsrsbh(sbrwDO.getNsrsbh());
        target.setSkssqq(cxstysbNsrxx.getSkssqq());
        target.setSkssqz(cxstysbNsrxx.getSkssqz());
        target.setZsxmDm(sbrwDO.getZsxmDm());
        target.setNsrmc(sbrwDO.getNsrmc());
        target.setSbrwuuid(sbrwDO.getSbrwuuid());
        target.setXzqhszDm(cxstysbNsrxx.getXzqhszDm());
    }

    /**
     * 过滤初始化资源明细列表
     *
     * 根据任务信息和纳税人信息，从初始化资源明细列表中筛选出匹配的记录
     *
     * @param initResMxVOList 初始化资源明细列表，用于筛选
     * @param sbrwDO 任务信息，包含用于筛选的条件如征税项目代码和纳税区间代码
     * @param cxstysbNsrxx 纳税人信息，包含用于筛选的条件如纳税开始时间
     * @return 返回筛选后的初始化资源明细列表如果输入列表为空或null，则返回空列表
     */
    private List<CchxwsnssbInitResMxVO> filterInitResMxVOList(List<CchxwsnssbInitResMxVO> initResMxVOList, ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx) {
        // 检查输入列表是否为空或null，如果是，则直接返回空列表
        if (GyUtils.isNull(initResMxVOList)) {
            return Collections.emptyList();
        }

        // 次报不按属期过滤
        if ("11".equals(sbrwDO.getNsqxDm())){
            return initResMxVOList.stream()
                    // 过滤条件：征收项目代码匹配
                    .filter(initResMxVO -> Objects.equals(initResMxVO.getZsxmdm(), sbrwDO.getZsxmDm()))
                    // 过滤条件：纳税期限代码匹配
                    .filter(initResMxVO -> Objects.equals(initResMxVO.getNsqxdm(), sbrwDO.getNsqxDm()))
                    .collect(Collectors.toList());
        }else{
            return initResMxVOList.stream()
                    // 过滤条件：征收项目代码匹配
                    .filter(initResMxVO -> Objects.equals(initResMxVO.getZsxmdm(), sbrwDO.getZsxmDm()))
                    // 过滤条件：纳税期限代码匹配
                    .filter(initResMxVO -> Objects.equals(initResMxVO.getNsqxdm(), sbrwDO.getNsqxDm()))
                    // 过滤条件：税款所属期起匹配
                    .filter(initResMxVO -> Objects.equals(initResMxVO.getMrskssqq(), cxstysbNsrxx.getSkssqq()))
                    // 过滤条件：税款所属期止匹配
                    .filter(initResMxVO -> Objects.equals(initResMxVO.getMrskssqz(), cxstysbNsrxx.getSkssqz()))
                    // 收集匹配的记录并返回
                    .collect(Collectors.toList());
        }
    }



    /**
     * 处理财行税申报数据并生成请求报文
     * 该方法根据输入的多个数据对象，组装成财行税申报请求报文（CxsRequestDTO）
     * 主要处理基础信息、业务信息和税务机关信息，并将这些信息设置到请求报文中
     *
     * @param sbrwDO        申报任务对象，包含申报任务的基本信息
     * @param cxstysbNsrxx  纳税人信息，用于申报
     * @param sbCxstysbSlxx 申报受理信息
     * @param qyjbxxmxResVO 企业基本信息响应对象，包含企业基础信息明细
     * @return 返回组装好的财行税申报请求报文对象（CxsRequestDTO）
     */
    private CxsRequestDTO handlerCxsSbBw(ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx, SbCxstysbSlxx sbCxstysbSlxx,
                                         ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO) {
        // 组装财行税申报报文
        final CxsRequestDTO cxsRequestDTO = new CxsRequestDTO();
        // 设置请求报文的唯一标识符
        cxsRequestDTO.setRequestId(GyUtils.getUuid());
        // 如果采集流水号不为空，则设置到报文中
        if (GyUtils.isNotNull(sbrwDO.getPclsh())) {
            cxsRequestDTO.setPclsh(sbrwDO.getPclsh());
        }
        // 生成明细ID
        final String mxid = GyUtils.getUuid();
        // 获取企业基本信息明细
        final JbxxmxsjVO jbxxmxsjVO = qyjbxxmxResVO.getJbxxmxsj().get(0);
        // 获取经办人信息
        final GyJbrxxDTO jbrxxDTO = GyJbrxxUtils.getJbrxx(jbxxmxsjVO.getNsrsbh(),jbxxmxsjVO.getDjxh());
        sbCxstysbSlxx.setJbr(jbrxxDTO.getJbr());
        sbCxstysbSlxx.setJbrsfzjhm(jbrxxDTO.getJbrsfzjhm());
        //jcbw基础报文处理
        final CxsjcbwDTO cxsjcbwDTO = this.jcbw(jbrxxDTO);
        //ywbw业务报文处理
        final List<CxsywbwDTO> cxsywbwDTOList = this.ywbw(sbrwDO, cxstysbNsrxx, sbCxstysbSlxx, mxid);
        //ssjg业务报文处理
        final CxsssjgDTO cxsssjgDTO = this.ssjg(cxstysbNsrxx, sbrwDO, mxid);
        if (GyUtils.isNull(cxsjcbwDTO) || GyUtils.isNull(cxsywbwDTOList) || GyUtils.isNull(cxsssjgDTO)){
            return null;
        }
        // 将基础报文、业务报文和税务机关信息设置到请求报文中
        cxsRequestDTO.setJcbw(cxsjcbwDTO);
        cxsRequestDTO.setYwbw(cxsywbwDTOList);
        cxsRequestDTO.setSsjg(cxsssjgDTO);
        // 返回组装好的请求报文
        return cxsRequestDTO;
    }

    /**
     * 创建并初始化查询数据窗口的DTO对象
     * 此方法用于将基本信息明细数据转换为查询数据窗口的DTO对象
     * 主要负责将办税人的相关信息设置到DTO中，以便后续的查询和展示
     *
     * @param jbrxxDTO 基本信息明细视图对象，包含办税人的基本信息
     * @return 返回一个初始化后的查询数据窗口DTO对象，包含办税人相关信息
     */
    private CxsjcbwDTO jcbw(GyJbrxxDTO jbrxxDTO) {
        // 创建查询数据窗口的DTO对象
        CxsjcbwDTO cxsjcbwDTO = new CxsjcbwDTO();

        // 设置办税人姓名
        cxsjcbwDTO.setJbr(jbrxxDTO.getJbr());
        // 设置办税人身份证号码
        cxsjcbwDTO.setJbrsfzjhm(jbrxxDTO.getJbrsfzjhm());
        // 设置办税人身份证件类型代码
        cxsjcbwDTO.setJbrsfzjlxDm(jbrxxDTO.getJbrsfzjlxDm());

        // 返回初始化后的查询数据窗口DTO对象
        return cxsjcbwDTO;
    }

    /**
     * 准备业务数据并构造查询税务报表的请求
     *
     * @param sbrwDO        申报任务信息
     * @param cxstysbNsrxx  纳税人信息
     * @param sbCxstysbSlxx 受理信息
     * @param mxid          明细ID
     * @return 返回构造好的查询税务报表DTO列表
     */
    private List<CxsywbwDTO> ywbw(ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx,
                                  SbCxstysbSlxx sbCxstysbSlxx, String mxid) {
        CxsywbwDTO cxsywbwDTO = new CxsywbwDTO();
        cxsywbwDTO.setSwsxDm("SXA061041005");
        cxsywbwDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsywbwDTO.setSwjgDm(cxstysbNsrxx.getZgswskfjDm().substring(0, 3) + "00000000");
        cxsywbwDTO.setSkssqq(sbrwDO.getSkssqq());
        cxsywbwDTO.setSkssqz(sbrwDO.getSkssqz());
        cxsywbwDTO.setGzbz("N");
        cxsywbwDTO.setMxid(mxid);
        // 处理申报数据
        CxsYwbwSbsjDTO cxsYwbwSbsjDTO = this.sbsj(sbrwDO, cxstysbNsrxx, sbCxstysbSlxx);
        if (GyUtils.isNull(cxsYwbwSbsjDTO)){
            return null;
        }
        cxsywbwDTO.setSbsj(Base64Utils.encode(JacksonUtils.toJson(cxsYwbwSbsjDTO)));
        List<CxsywbwDTO> cxsywbwDTOS = new ArrayList<>();
        cxsywbwDTOS.add(cxsywbwDTO);
        return cxsywbwDTOS;
    }

    /**
     * 构建并返回财产和行为税申报数据传输对象
     *
     * @param sbrwDO        申报任务数据对象，包含申报任务的基本信息
     * @param cxstysbNsrxx  纳税人信息对象，用于获取纳税人详细信息
     * @param sbCxstysbSlxx 申报受理信息对象，包含申报的受理详情
     * @return 返回一个构建好的财产和行为税申报数据传输对象
     */
    private CxsYwbwSbsjDTO sbsj(ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx,
                                SbCxstysbSlxx sbCxstysbSlxx) {
        final CxsYwbwSbsjDTO cxsYwbwSbsjDTO = new CxsYwbwSbsjDTO();
        final String yzpzzlDm = YzpzzlEnum.getYzpzzlDmByZsxmDm(sbrwDO.getZsxmDm(),null);
        //保存财行税通用申报业务报文
        CxstysbnsrxxDTO cxstysbnsrxxDTO = BeanUtil.copyProperties(cxstysbNsrxx, CxstysbnsrxxDTO.class);
        cxstysbnsrxxDTO.setSkssqq(sbrwDO.getSkssqq());
        cxstysbnsrxxDTO.setSkssqz(sbrwDO.getSkssqz());
        cxstysbnsrxxDTO.setSbrq1(convertToDate(cxstysbNsrxx.getSbrq1()));
        cxstysbnsrxxDTO.setXgmjzzcqssj(convertToDate(cxstysbNsrxx.getXgmjzzcqssj()));
        cxstysbnsrxxDTO.setXgmjzzczzsj(convertToDate(cxstysbNsrxx.getXgmjzzczzsj()));
        cxstysbnsrxxDTO.setYzpzzlDm(CXS.getDm());

        // 构建SbxxGrid对象及其内嵌结构
        final CxssbxxGridDTO cxssbxxGridDTO = new CxssbxxGridDTO();

        // 通过链式调用填充对象
        final CxssymxDTO cxssymxDTO = new CxssymxDTO();
        cxssymxDTO.setSyuuid(sbrwDO.getSyuuid());
        cxssymxDTO.setSybzDm1(null);

        final CxssymxGridDTO cxssymxGridDTO = new CxssymxGridDTO();
        cxssymxGridDTO.setSymxGridlb(Collections.singletonList(cxssymxDTO));

        final CxssbxxGridlbDTO cxssbxxGridlbDTO = new CxssbxxGridlbDTO();
        cxssbxxGridlbDTO.setZsxmDm(sbrwDO.getZsxmDm());
        cxssbxxGridlbDTO.setYzpzzlDm(yzpzzlDm);
        cxssbxxGridlbDTO.setSymxGrid(cxssymxGridDTO);

        // 设置sbxxGridlb列表
        cxssbxxGridDTO.setSbxxGridlb(Collections.singletonList(cxssbxxGridlbDTO));


        // 受理信息
        CxstysbslxxDTO cxstysbslxxDTO = BeanUtil.copyProperties(sbCxstysbSlxx, CxstysbslxxDTO.class);
        cxstysbslxxDTO.setSlrq(convertToDate(sbCxstysbSlxx.getSlrq()));
        cxsYwbwSbsjDTO.setCxstysbnsrxx(cxstysbnsrxxDTO);
        cxsYwbwSbsjDTO.setSbxxGrid(cxssbxxGridDTO);
        cxsYwbwSbsjDTO.setCxstysbslxx(cxstysbslxxDTO);
        return cxsYwbwSbsjDTO;
    }

    /**
     * 根据不同的税收项目代码处理不同的算税结果
     *
     * @param cxstysbNsrxx 纳税人信息对象，包含纳税人的相关信息
     * @param sbrwDO       任务对象，包含任务的详细信息，如税收项目代码等
     * @param mxid         明细ID，用于标识特定的明细记录
     * @return 返回税算税结果
     */
    private CxsssjgDTO ssjg(CxstysbNsrxx cxstysbNsrxx, ZnsbNssbSbrwDO sbrwDO, String mxid) {
        CxsssjgDTO cxsssjgDTO = new CxsssjgDTO();
        String syuuid = sbrwDO.getSyuuid();
        String zsxmDm = sbrwDO.getZsxmDm();

        Map<String, BiConsumer<CxsssjgDTO, String>> handlers = new HashMap<>();
        handlers.put("10110", (dto, id) -> cchxwsnssbService.fcsssjg(dto, null, null, cxstysbNsrxx, syuuid, id));
        handlers.put("10111", (dto, id) -> cchxwsnssbService.yhsssjg(dto, null, null, cxstysbNsrxx, id));
        handlers.put("10112", (dto, id) -> cchxwsnssbService.cztdsysssjg(dto, null, null, cxstysbNsrxx, syuuid, id));

        // 获取处理逻辑
        BiConsumer<CxsssjgDTO, String> handler = handlers.get(zsxmDm);

        // 如果处理逻辑存在，则执行
        if (handler != null) {
            handler.accept(cxsssjgDTO, mxid);
            return checkAndReturn(cxsssjgDTO, sbrwDO);
        } else {
            log.error("未知的zsxmDm: {}", zsxmDm);
            return null;
        }
    }

    /**
     * 执行空值检查并返回处理后的DTO对象
     * 此方法主要用于检查传入的DTO对象和任务对象是否为null，并根据任务对象中的代码模型获取DTO对象中的相应字段值
     * 如果任一对象为null，或者指定字段的值为null，方法将记录错误并返回null
     *
     * @param cxsssjgDTO 税收结果DTO对象，包含不同的税收结果字段
     * @param sbrwDO 任务对象，包含任务详细信息，如代码模型
     * @return 如果检查通过，返回原cxsssjgDTO对象，否则返回null
     */
    private CxsssjgDTO checkAndReturn(CxsssjgDTO cxsssjgDTO, ZnsbNssbSbrwDO sbrwDO) {
        // 根据不同的代码模型，映射到DTO对象中的不同字段获取方法
        Map<String, Supplier<Object>> fieldGetters = new HashMap<>();
        fieldGetters.put("10110", cxsssjgDTO::getFcsssjg);
        fieldGetters.put("10111", cxsssjgDTO::getYhsssjg);
        fieldGetters.put("10112", cxsssjgDTO::getCztdsysssjg);

        // 根据任务对象中的代码模型获取对应的字段值获取器
        Supplier<Object> fieldGetter = fieldGetters.get(sbrwDO.getZsxmDm());

        // 检查是否找到了对应的字段值获取器
        if (fieldGetter != null) {
            // 使用获取器获取字段值
            Object field = fieldGetter.get();

            // 空值判断
            if (GyUtils.isNull(field)) {
                log.error("{}:算税结果为空，zsxmDm: {}", sbrwDO.getSbrwuuid(), sbrwDO.getZsxmDm());
                return null;
            }
            return cxsssjgDTO;
        } else {
            // 如果没有找到对应的字段值获取器，记录未知的代码模型错误
            log.error("未知的 zsxmDm: {}", sbrwDO.getZsxmDm());
            return null;
        }
    }


    /**
     * 组装财行税预填请求DTO
     *
     * @param sbrwDO
     * @param cxstysbNsrxx
     * @param symxGridlb
     * @return
     */
    private CxsQuerymxInitDTO handlerCxsYtReqDTO(ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx,
                                                 List<SymxGridlbVO> symxGridlb) {
        CxsQuerymxInitDTO cxsQuerymxInitDTO = new CxsQuerymxInitDTO();
        // 财行税通用申报初始化
        cxsQuerymxInitDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsQuerymxInitDTO.setSkssqq(sbrwDO.getSkssqq());
        cxsQuerymxInitDTO.setSkssqz(sbrwDO.getSkssqz());
        cxsQuerymxInitDTO.setNsqxDm(sbrwDO.getNsqxDm());
        cxsQuerymxInitDTO.setBssfsylslfjmzc(cxstysbNsrxx.getBqsfsyzzsxgmnsrjzzc());
        cxsQuerymxInitDTO.setJmsyzt(cxstysbNsrxx.getJzzcsyztDm());
        cxsQuerymxInitDTO.setSyjzzcq(DateUtils.parseDate(cxstysbNsrxx.getXgmjzzcqssj(), 3));
        cxsQuerymxInitDTO.setSyjzzcz(DateUtils.parseDate(cxstysbNsrxx.getXgmjzzczzsj(), 3));
        cxsQuerymxInitDTO.setSyuuidList(symxGridlb.stream().map(SymxGridlbVO::getSyuuid).collect(Collectors.toList()));
        cxsQuerymxInitDTO.setGzbz("N");
        return cxsQuerymxInitDTO;
    }

    /**
     * 获取企业基本信息
     *
     * @param djxh
     * @return
     */
    private ZnsbMhzcQyjbxxmxResVO getQyJbxx(String djxh) {
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        return nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO).getData();
    }

    /**
     * 获取机构信息
     *
     * @param djxh
     * @return
     */
    private CompanyBasicInfoDTO getJgxx(String djxh,String nsrsbh) {
        return companyApi.basicInfo(djxh, nsrsbh).getData();
    }

    /**
     * 组装财行税统一申报纳税人信息
     *
     * @param sbrwDO
     * @param cxstysbNsrxx
     * @return
     */
    private void handlerCxstysbNsrxxDTO(ZnsbNssbSbrwDO sbrwDO, CxstysbNsrxx cxstysbNsrxx, ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO) {
        final String djxh = sbrwDO.getDjxh();
        final String nsrsbh = sbrwDO.getNsrsbh();
        final String skssqq = DateUtils.dateToString(sbrwDO.getSkssqq(), 3);
        final String skssqz = DateUtils.dateToString(sbrwDO.getSkssqz(), 3);
        // 获取企业基本信息明细  筛选出主户的信息
        final JbxxmxsjVO jbxxmxsjVO = qyjbxxmxResVO.getJbxxmxsj().get(0);
        final NsrzgxxVO nsrzgxxVO = qyjbxxmxResVO.getNsrzgxx().get(0);
        final List<NsrbqxxVO> nsrbqxxVOList = qyjbxxmxResVO.getNsrbqxx();
        // 获取行政区划数字代码
        final String xzqhszDm = getXzqhszDm(djxh, nsrsbh);

        //组装财行税统一申报纳税人信息
        cxstysbNsrxx.setDjxh(djxh);
        cxstysbNsrxx.setNsrsbh(nsrsbh);
        cxstysbNsrxx.setNsrmc(jbxxmxsjVO.getNsrmc());
        cxstysbNsrxx.setSbsxDm1("11");
        cxstysbNsrxx.setSbrq1(CchxwsnssbGyUtils.getDayofNow());
        cxstysbNsrxx.setHyDm(jbxxmxsjVO.getHyDm());
        cxstysbNsrxx.setYzpzzlDm(CXS.getDm());
        cxstysbNsrxx.setSkssqq(skssqq);
        cxstysbNsrxx.setSkssqz(skssqz);
        cxstysbNsrxx.setXzqhszDm(xzqhszDm);
        cxstysbNsrxx.setZgswskfjDm(jbxxmxsjVO.getZgswskfjDm());
        cxstysbNsrxx.setKzztlxDm(jbxxmxsjVO.getKzztdjlxDm());
        cxstysbNsrxx.setDjzclxDm(jbxxmxsjVO.getDjzclxDm());
        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc("N");
        final CxsLslfJmxxDTO cxsLslfJmxxDTO = cchxwshbsbService.getLslfJmxx(skssqq, skssqz, nsrzgxxVO, nsrbqxxVOList);
        if (GyUtils.isNotNull(cxsLslfJmxxDTO) && "Y".equals(cxsLslfJmxxDTO.getBqsfsyzzsxgmnsrjzzc())) {
            cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc("Y");
            cxstysbNsrxx.setJzzcsyztDm(cxsLslfJmxxDTO.getJzzcsyztDm());
            cxstysbNsrxx.setXgmjzzcqssj(cxsLslfJmxxDTO.getXgmjzzcqssj());
            cxstysbNsrxx.setXgmjzzczzsj(cxsLslfJmxxDTO.getXgmjzzczzsj());
        }
    }

    /**
     * 计算应补退税额
     *
     * @param sbxxGridlbVOList  申报信息列表
     * @param bqjmsemxbywbwList 减免信息列表
     * @param sbrwDO
     * @return 应补退税额，经过四舍五入处理
     */
    private Map<String,BigDecimal> getYbtse(List<SbxxGridlbVO> sbxxGridlbVOList, List<Bqjmsemxbywbw> bqjmsemxbywbwList, ZnsbNssbSbrwDO sbrwDO) {
        final List<BqjmsemxbGridlbVO> bqjmsemxbGridlbVOList = Optional.ofNullable(bqjmsemxbywbwList)
                .map(gridlb -> gridlb.get(0))
                .map(Bqjmsemxbywbw::getBqjmsemxb)
                .map(Bqjmsemxb::getBqjmsemxbGridlb)
                .orElse(null);

        // 计算六税两费减免信息
        this.clLslfJmxx(sbxxGridlbVOList,bqjmsemxbGridlbVOList);

        Map<String,BigDecimal> ybtseMap = new HashMap<>();

        // 房产税分别计算从租和从价应补退税额
        BigDecimal czYbtse = BigDecimal.ZERO;
        BigDecimal cjYbtse = BigDecimal.ZERO;

        if (FCS.getCode().equals(sbrwDO.getZsxmDm())) {
            for (SbxxGridlbVO vo : sbxxGridlbVOList) {
                if ("101100700".equals(vo.getZspmDm())) {
                    BigDecimal ybtse = BigDecimal.valueOf(vo.getYbtse());
                    cjYbtse = cjYbtse.add(ybtse);
                } else if ("101100800".equals(vo.getZspmDm())) {
                    BigDecimal ybtse = BigDecimal.valueOf(vo.getYbtse());
                    czYbtse = czYbtse.add(ybtse);
                }
            }

            ybtseMap.put("czYbtse",czYbtse.setScale(2, RoundingMode.HALF_UP));
            ybtseMap.put("cjYbtse",cjYbtse.setScale(2, RoundingMode.HALF_UP));
        }

        // 使用 Stream API 简化循环逻辑，并过滤掉 null 值
        BigDecimal result = sbxxGridlbVOList.stream().map(SbxxGridlbVO::getYbtse)
                .filter(Objects::nonNull)
                .peek(ybtse -> log.info("Individual ybtse value: {}", ybtse)) // 打印每个有效的 ybtse 值
                .map(BigDecimal::valueOf) // 将 double 转换为 BigDecimal
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("ybtse:{}",result);

        ybtseMap.put("ybtse",result.setScale(2, RoundingMode.HALF_UP));

        return ybtseMap;
    }


    /**
     * 处理六税两费减免信息
     *
     * @param sbxxGridlbVOList  接口名称
     * @param bqjmsemxbGridlbVOList 入参
     */
    private void clLslfJmxx(List<SbxxGridlbVO> sbxxGridlbVOList , List<BqjmsemxbGridlbVO> bqjmsemxbGridlbVOList) {
        // 检查输入列表是否为空
        if (GyUtils.isNull(sbxxGridlbVOList) || GyUtils.isNull(bqjmsemxbGridlbVOList)) {
            return;
        }

        // 判断是否为房产税、城镇土地使用税
        final String zsxmDm = sbxxGridlbVOList.get(0).getZsxmDm();

        Map<String, Double> fbmxGroupSums;

        // 根据不同的税种代码，采用不同的分组求和策略
        if (FCS.getCode().equals(zsxmDm) || CZTDSYS.getCode().equals(zsxmDm)) {
            // 房产和城土的减免已经计算到ybtse中
            return;
        } else {
            // 按照 zspm, zsxm, skssqq, skssqz, ynse 分组并求和 fbmxmcVOList 中的 jmse
            fbmxGroupSums = bqjmsemxbGridlbVOList.stream()
                    .collect(Collectors.groupingBy(
                            vo -> vo.getZsxmDm() + "|" + vo.getZspmDm() + "|" + vo.getSkssqq() + "|" + vo.getSkssqz(),
                            Collectors.summingDouble(BqjmsemxbGridlbVO::getJmse)
                    ));
        }

        // 更新 sbxxGridlbVOList 中的 jmse 和 ybtse
        sbxxGridlbVOList.forEach(vo -> {
            Double jmse = calculateJmse(vo, fbmxGroupSums);
            vo.setJmse(roundToTwoDecimalPlaces(jmse));  // 设置 sbxxGridlbVOList 的 jmse 为 fbmxmcVOList 对应分组的 jmse 总和，并保留两位小数

            Double ybtse = vo.getYnse() - vo.getJmse();
            vo.setYbtse(roundToTwoDecimalPlaces(ybtse));  // 保留两位小数
        });
    }


    /**
     * 根据分组键计算jmse
     * 本方法旨在根据不同的税收代码确定合适的分组键，并利用该分组键从预计算的Map中获取对应的jmse值
     *
     * @param vo 包含税收相关信息的SbxxGridlbVO对象，用于获取税收代码和其他必要信息
     * @param fbmxGroupSums 一个映射，将分组键（根据税收代码和期间等信息构建的字符串）映射到对应的jmse值
     * @return 返回从fbmxGroupSums中获取的与计算出的分组键对应的jmse值，如果不存在，则返回0.0
     */
    private Double calculateJmse(SbxxGridlbVO vo, Map<String, Double> fbmxGroupSums) {
        String groupKey;
        // 根据税收代码区分房产税和城镇土地使用税与其他税收
        if (FCS.getCode().equals(vo.getZsxmDm()) || CZTDSYS.getCode().equals(vo.getZsxmDm())) {
            // 对于房产税、城镇土地使用税，使用sybh作为分组键
            // 这是因为这些税收的计算可能特别依赖于财产的唯一标识
            groupKey = vo.getSyuuid();
        } else {
            // 对于其他情况，使用zspm, zsxm, skssqq, skssqz组合键
            // 这种组合键确保了在不同条件下的唯一性，以便准确计算jmse
            groupKey = vo.getZsxmDm() + "|" + vo.getZspmDm() + "|" + vo.getSkssqq() + "|" + vo.getSkssqz();
        }
        // 使用计算出的分组键从fbmxGroupSums中获取对应的jmse值，如果不存在，则返回0.0
        // 这一步是实际计算jmse的核心，通过预计算的Map快速获取结果
        return fbmxGroupSums.getOrDefault(groupKey, 0.0);
    }


    /**
     * 将double值四舍五入到两位小数
     * 如果输入值为null，则返回0.0
     * 使用BigDecimal进行精确计算，确保四舍五入的准确性
     *
     * @param value 待四舍五入的double值
     * @return 四舍五入到两位小数的double值，或0.0（如果输入为null）
     */
    private static Double roundToTwoDecimalPlaces(Double value) {
        // 检查输入值是否为null，如果是，则返回0.0
        if (value == null) return 0.0;

        // 使用BigDecimal对double值进行四舍五入到两位小数
        BigDecimal bigDecimal = new BigDecimal(value);
        return bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    /**
     * 获取预申报结果数据
     *
     * @param ysbsj  预申报数据
     * @param ybtseMap 应补退税额
     * @param sbrwDO 申报任务do
     * @param cchxwsnssbGzlVO 暂存信息
     * @return 结果
     */
    private CxsYsbjgbDTO getYsbjgbDTO(String ysbsj, Map<String,BigDecimal> ybtseMap, ZnsbNssbSbrwDO sbrwDO, CchxwsnssbGzlVO cchxwsnssbGzlVO) {
        // 提取并转换申报任务的开始和结束日期为字符串格式
        String skssqq = GyUtils.cast2Str(sbrwDO.getSkssqq());
        String skssqz = GyUtils.cast2Str(sbrwDO.getSkssqz());

        final String yzpzzlDm = YzpzzlEnum.getYzpzzlDmByZsxmDm(sbrwDO.getZsxmDm(),null);
        // 创建预申报结果数据对象
        CxsYsbjgbDTO ysbjgbDTO = new CxsYsbjgbDTO();

        // 房产税和城镇土地使用税获取不动产权证号和土地编号
        final Map<String,String> bhMap = queryBhBySyuuid(sbrwDO);

        // 设置预申报结果数据的属性值
        ysbjgbDTO.setUuid(GyUtils.getUuid());
        ysbjgbDTO.setSbrwuuid(sbrwDO.getSbrwuuid());
        ysbjgbDTO.setDjxh(GyUtils.cast2Str(sbrwDO.getDjxh()));
        ysbjgbDTO.setYzpzzlDm(yzpzzlDm);
        ysbjgbDTO.setSkssqq(DateUtil.parse(skssqq));
        ysbjgbDTO.setSkssqz(DateUtil.parse(skssqz));
        ysbjgbDTO.setKsbbz("Y");
        ysbjgbDTO.setSyuuid(sbrwDO.getSyuuid());
        ysbjgbDTO.setYbtse(ybtseMap.get("ybtse"));
        ysbjgbDTO.setCzybtse(ybtseMap.get("czYbtse"));
        ysbjgbDTO.setCjybtse(ybtseMap.get("cjYbtse"));
        ysbjgbDTO.setBdcqzh(bhMap.get("bdcqzh"));
        ysbjgbDTO.setTdbh(bhMap.get("tdbh"));
        ysbjgbDTO.setNsqxDm(sbrwDO.getNsqxDm());
        ysbjgbDTO.setZsxmDm(sbrwDO.getZsxmDm());
        ysbjgbDTO.setBusinessclob(ysbsj);
        ysbjgbDTO.setXczxsj(DateUtil.parse("9999-12-31 23:59:59", DatePattern.NORM_DATETIME_PATTERN));
        ysbjgbDTO.setCchxwsnssbGzlVO(cchxwsnssbGzlVO);

        // 返回构建好的预申报结果数据对象
        return ysbjgbDTO;
    }

    /**
     * 根据税源UUID查询编号信息
     *
     * 该方法根据不同税种类型，从相应的数据表中查询对应的编号信息：
     * - 房产税：查询房屋产权证书号（fwcqzsh）
     * - 土地使用税：查询土地使用权编号（tdsybh）
     *
     * @param sbrwDO 申报任务对象，包含税源UUID和税种代码等信息
     * @return 返回包含编号信息的Map集合，key为字段名，value为对应编号值
     *         - 房产税：key为"bdcqzh"，value为房屋产权证书号
     *         - 土地使用税：key为"tdbh"，value为土地使用权编号
     */
    private Map<String, String> queryBhBySyuuid(ZnsbNssbSbrwDO sbrwDO) {
        Map<String, String> bhMap = new HashMap<>();

        final String syuuid = sbrwDO.getSyuuid();
        final String zsxmDM = sbrwDO.getZsxmDm();

        if (FCS.getCode().equals(zsxmDM)){
            ZnsbCxsFyxxcjb fyxxcjb = cxsFyxxcjbMapper.queryByFwxxuuid(syuuid);
            bhMap.put("bdcqzh", fyxxcjb.getFwcqzsh());
        }

        if (CZTDSYS.getCode().equals(zsxmDM)){
            ZnsbNssbCxsTdsyxxcjbDo tdsyxxcjbDO = tdsyxxcjbDOMapper.queryTdxxbysyuuuid(syuuid);
            bhMap.put("tdbh", tdsyxxcjbDO.getTdsybh());
        }

        return bhMap;
    }

    /**
     * 获取行政区划数字代码
     *
     * @param djxh
     * @param nsrsbh
     * @return 结果
     */
    private String getXzqhszDm(String djxh, String nsrsbh) {
        log.info("开始获取行政区划数字代码，入参：djxh={}, nsrsbh={}", djxh, nsrsbh);
        CommonResult<CompanyBasicInfoDTO> basicInfo = companyApi.basicInfo(djxh, nsrsbh);
        String xzqhszDm = "";
        if (basicInfo.isSuccess()) {
            xzqhszDm = basicInfo.getData().getXzqhszDm();
        }
        log.info("获取到行政区划数字代码：{}", xzqhszDm);
        return xzqhszDm;
    }

    /**
     * 将字符串日期转换为Date对象
     * 此方法主要用于处理日期字符串，将其转换为Date对象，以便在Java程序中使用
     * 如果输入的字符串为null或空字符串，方法将返回null，避免解析错误
     *
     * @param dateStr 日期字符串，格式应为"yyyy-MM-dd"
     * @return 对应的Date对象，如果输入为空或格式不正确则返回null
     */
    private Date convertToDate(String dateStr) {
        // 使用GyUtils工具类检查日期字符串是否非空
        // 如果非空，使用DateUtils工具类将字符串转换为Date对象，指定格式为"yyyy-MM-dd"
        // 如果为空，返回null
        return GyUtils.isNotNull(dateStr) ? DateUtils.toDate(dateStr, "yyyy-MM-dd") : null;
    }



}
