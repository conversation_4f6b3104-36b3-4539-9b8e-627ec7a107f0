package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 成品油购销存情况明细表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "cpygxcqkmxbGridlbVO", propOrder = { "ewbhxh", "qmkcDcje", "qmkcZgje", "qmkcDcsl", "qmkcZgsl", "bqckFysbfDcje", "bqckFysbfZyje", "bqckFysbfDcsl", "bqckFysbfZysl", "bqckYsbfje", "bqckYsbfsl", "bqrkDcje", "bqrkZgje", "bqrkDcsl", "bqrkZgsl", "qckcDcje", "qckcZgje", "qckcDcsl", "qckcZgsl", "ypxh" })
@Getter
@Setter
public class CpygxcqkmxbGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 期末库存金额代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmkcDcje;

    /**
     * 期末库存金额自购
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmkcZgje;

    /**
     * 期末库存数量代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmkcDcsl;

    /**
     * 期末库存数量自购
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmkcZgsl;

    /**
     * 本期出库非应税部分金额代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqckFysbfDcje;

    /**
     * 本期出库非应税部分金额自用
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqckFysbfZyje;

    /**
     * 本期出库非应税部分数量代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqckFysbfDcsl;

    /**
     * 本期出库非应税部分数量自用
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqckFysbfZysl;

    /**
     * 本期出库应税部分金额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqckYsbfje;

    /**
     * 本期出库应税部分数量
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqckYsbfsl;

    /**
     * 本期入库金额代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqrkDcje;

    /**
     * 本期入库金额自购
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqrkZgje;

    /**
     * 本期入库数量代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqrkDcsl;

    /**
     * 本期入库数量自购
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqrkZgsl;

    /**
     * 期初库存金额代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qckcDcje;

    /**
     * 期初库存金额自购
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qckcZgje;

    /**
     * 期初库存数量代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qckcDcsl;

    /**
     * 期初库存数量自购
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qckcZgsl;

    /**
     * 油品型号
     */
    @XmlElement(nillable = true, required = true)
    protected String ypxh;
}