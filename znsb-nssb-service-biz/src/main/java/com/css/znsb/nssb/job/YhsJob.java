package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.cchxwssb.yhssjcj.YhssycjNewService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 印花税job
 */
@Slf4j
@Component
public class YhsJob {
    @Resource
    private YhssycjNewService yhssycjNewService;

    @XxlJob(value = "scAcSbrw")
    public void execute() {
        String aysc = XxlJobHelper.getJobParam();
        yhssycjNewService.scAcSbrw(aysc);
    }
}
