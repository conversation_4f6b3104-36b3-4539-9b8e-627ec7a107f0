package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税纳税申报表附表一（本期销售情况明细表）》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr01_bqxsqkmxbywbw", propOrder = { "zzssyyybnsr01Bqxsqkmxb" })
@Getter
@Setter
public class Zzssyyybnsr01Bqxsqkmxbywbw extends TaxDoc {
    /**
     * 《增值税纳税申报表附表一（本期销售情况明细表）》业务报文
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr01_bqxsqkmxb", required = true)
    @JSONField(name = "zzssyyybnsr01_bqxsqkmxb")
    protected Zzssyyybnsr01Bqxsqkmxb zzssyyybnsr01Bqxsqkmxb;
}