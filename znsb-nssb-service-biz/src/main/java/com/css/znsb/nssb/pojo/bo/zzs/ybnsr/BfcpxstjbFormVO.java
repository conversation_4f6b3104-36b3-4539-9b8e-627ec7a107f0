package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 部分产品销售统计表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bfcpxstjbFormVO", propOrder = { "nsrsbh", "nsrmc", "skssqq", "skssqz", "tbrq", "ltxssl", "ltxse", "zwxltxssl", "zwxltxse", "xjltxssl", "xjltxse", "jjxssl", "jjxse", "yyycqydjjxssl", "yyycqydjjxse", "syjjxssl", "syjjxse", "qtjjxssl", "qtjjxse", "mtcxssl", "mtcxse" })
@Getter
@Setter
public class BfcpxstjbFormVO {
    /**
     * 纳税人识别号
     */
    protected String nsrsbh;

    /**
     * 纳税人名称
     */
    protected String nsrmc;

    /**
     * 税款所属期起
     */
    protected String skssqq;

    /**
     * 开户银行
     */
    protected String skssqz;

    /**
     * 填表日期
     */
    protected String tbrq;

    /**
     * 轮胎销售数量
     */
    protected BigDecimal ltxssl;

    /**
     * 轮胎销售额
     */
    protected BigDecimal ltxse;

    /**
     * 子午线轮胎销售数量
     */
    protected BigDecimal zwxltxssl;

    /**
     * 子午线轮胎销售额
     */
    protected BigDecimal zwxltxse;

    /**
     * 斜交轮胎销售数量
     */
    protected BigDecimal xjltxssl;

    /**
     * 斜交轮胎销售额
     */
    protected BigDecimal xjltxse;

    /**
     * 酒精销售数量
     */
    protected BigDecimal jjxssl;

    /**
     * 酒精销售额
     */
    protected BigDecimal jjxse;

    /**
     * 用于乙醇汽油的酒精销售数量
     */
    protected BigDecimal yyycqydjjxssl;

    /**
     * 用于乙醇汽油的酒精销售额
     */
    protected BigDecimal yyycqydjjxse;

    /**
     * 食用酒精销售数量
     */
    protected BigDecimal syjjxssl;

    /**
     * 食用酒精销售额
     */
    protected BigDecimal syjjxse;

    /**
     * 其他酒精销售数量
     */
    protected BigDecimal qtjjxssl;

    /**
     * 其他酒精销售额
     */
    protected BigDecimal qtjjxse;

    /**
     * 摩托车销售数量
     */
    protected BigDecimal mtcxssl;

    /**
     * 摩托车销售额
     */
    protected BigDecimal mtcxse;
}