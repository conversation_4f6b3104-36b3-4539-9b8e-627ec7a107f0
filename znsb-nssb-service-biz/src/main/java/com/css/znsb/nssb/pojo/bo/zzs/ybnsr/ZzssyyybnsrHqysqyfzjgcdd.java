package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《航空运输企业分支机构传递单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_hqysqyfzjgcdd", propOrder = { "sbbhead", "trccfhdncpzzsjxseGrid" })
@XmlSeeAlso({ ZzssyyybnsrHqysqyfzjgcddywbw.ZzssyyybnsrHqysqyfzjgcdd.class })
@Getter
@Setter
public class ZzssyyybnsrHqysqyfzjgcdd {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    @XmlElement(nillable = true, required = true)
    protected TrccfhdncpzzsjxseGrid trccfhdncpzzsjxseGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "trccfhdncpzzsjxseGridlbVO" })
    @Getter
    @Setter
    public static class TrccfhdncpzzsjxseGrid {
        @XmlElement(nillable = true, required = true)
        protected List<TrccfhdncpzzsjxseGridlbVO> trccfhdncpzzsjxseGridlbVO;

        /**
         * Gets the value of the trccfhdncpzzsjxseGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the trccfhdncpzzsjxseGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getTrccfhdncpzzsjxseGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link TrccfhdncpzzsjxseGridlbVO}
         */
        public List<TrccfhdncpzzsjxseGridlbVO> getTrccfhdncpzzsjxseGridlbVO() {
            if (trccfhdncpzzsjxseGridlbVO == null) {
                trccfhdncpzzsjxseGridlbVO = new ArrayList<TrccfhdncpzzsjxseGridlbVO>();
            }
            return this.trccfhdncpzzsjxseGridlbVO;
        }
    }
}