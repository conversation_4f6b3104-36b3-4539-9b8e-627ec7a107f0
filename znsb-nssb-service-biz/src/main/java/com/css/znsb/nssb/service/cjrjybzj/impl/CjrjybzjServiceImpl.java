package com.css.znsb.nssb.service.cjrjybzj.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyDTO;
import com.css.znsb.nssb.constants.GyEnum;
import com.css.znsb.nssb.constants.SbrwztConstants;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.constants.enums.ZsxmDmEnum;
import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxsshgjsyxxjlbMapper;
import com.css.znsb.nssb.mapper.cjrjybzj.ZnsbCjrjybzjMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.ZnsbNssbCxsshgjsyxxjlbDO;
import com.css.znsb.nssb.pojo.domain.cjrjybzj.ZnsbCjrjybzjDO;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.dto.sbjgcx.SbjgcxRespDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbxxDTO;
import com.css.znsb.nssb.service.cjrjybzj.CjrjybzjService;
import com.css.znsb.nssb.service.sbcwgz.SbcwgzService;
import com.css.znsb.nssb.service.sbqkcx.SbqkcxSbxxService;
import com.css.znsb.nssb.service.sbqkcx.SbqkcxSbxxmxService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.css.znsb.nssb.utils.GYCastUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CjrjybzjServiceImpl implements CjrjybzjService {

    @Resource
    private ZnsbNssbCxsshgjsyxxjlbMapper znsbNssbCxsshgjsyxxjlbMapper;
    @Resource
    private SjjhService sjjhService;
    @Resource
    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;
    @Resource
    private ZnsbNssbSbrwService znsbNssbSbrwService;
    @Resource
    private SbqkcxSbxxService sbbService;
    @Resource
    private SbqkcxSbxxmxService sbxxService;
    @Resource
    private SbcwgzService sbcwgzService;
    @Resource
    private CompanyApi companyApi;
    @Resource
    private ZnsbCjrjybzjMapper cjrjybzjMapper;
    @Override
    public ZnsbNssbCxsshgjsyxxjlbDO saveRzb(Map reqMap) {
        ZnsbNssbCxsshgjsyxxjlbDO cxsshgjsyxxjlbDO = new ZnsbNssbCxsshgjsyxxjlbDO();
        cxsshgjsyxxjlbDO.setUuid(GyUtils.getUuid());
        cxsshgjsyxxjlbDO.setDjxh((String) reqMap.get("djxh"));
        cxsshgjsyxxjlbDO.setSkssqq(DateUtils.strToDate((String) reqMap.get("skssqq")));
        cxsshgjsyxxjlbDO.setSkssqz(DateUtils.strToDate((String) reqMap.get("skssqz")));
        cxsshgjsyxxjlbDO.setYzpzzlDm(YzpzzlEnum.CJRJYBZJ.getDm());
        cxsshgjsyxxjlbDO.setQqbw(JsonUtils.toJson(reqMap));
        cxsshgjsyxxjlbDO.setGjbz("N");
        cxsshgjsyxxjlbDO.setZxcs(BigDecimal.ZERO);
        cxsshgjsyxxjlbDO.setCgbz("N");
        cxsshgjsyxxjlbDO.setNsrsbh((String) reqMap.get("nsrsbh"));
        cxsshgjsyxxjlbDO.setPclsh("");
        cxsshgjsyxxjlbDO.setXzqhszDm((String) reqMap.get("xzqhszDm"));
        cxsshgjsyxxjlbDO.setNsrmc((String) reqMap.get("nsrmc"));
        cxsshgjsyxxjlbDO.setSyuuid((String) reqMap.get("sbuuid"));
        cxsshgjsyxxjlbDO.setLrrq(new Date());
        znsbNssbCxsshgjsyxxjlbMapper.insert(cxsshgjsyxxjlbDO);
        return cxsshgjsyxxjlbDO;
    }
    @Override
    public CommonResult<Object> getSbResult(Map reqMap) {
        CommonResult<Object> result = invokeSjjh(YzpzzlEnum.CJRJYBZJ.getDm(),"CX00000002", JsonUtils.toJson(reqMap.get("bw")), reqMap);
        if ("1".equals(String.valueOf(result.getCode()))) {
            final Map<String, Object> dataMap = JsonUtils.toMap((String)result.getData());
            if ("00".equals(dataMap.get("BizCode"))) {
                Object body = dataMap.get("Body");
                final Map<String, Object> bodyMap = JsonUtils.toMap(String.valueOf(body));
                String errInfo = "";
                if(bodyMap.containsKey("errInfo")&& GyUtils.isNotNull(bodyMap.get("errInfo"))){
                    errInfo = String.valueOf(bodyMap.get("errInfo"));
                }
                if (GyUtils.isNotNull(errInfo)) {
                    SbjgcxRespDTO sbjgcxRespDTO = new SbjgcxRespDTO();
                    sbjgcxRespDTO.setSbztDm(SbrwztConstants.SBZT_SBSB_DM);
                    sbjgcxRespDTO.setSbyysm(errInfo);
                    if(GyUtils.isNotNull(reqMap.get("sbrwuuid"))){
                        znsbNssbSbrwService.updateSbsb((String) reqMap.get("sbrwuuid"),sbjgcxRespDTO);
                    }
                    return CommonResult.error(2, errInfo);
                }
                final String sssqQ = (String) reqMap.get("skssqq");
                final String sssqZ = (String) reqMap.get("skssqz");
                Map<String, Object> xmpm = (Map<String, Object>) bodyMap.get("xmpm");
                // 生成申报数据
                List<SbxxDTO> sbxxList = new ArrayList<>();
                SbxxDTO sbxx = BeanUtils.toBean(bodyMap, SbxxDTO.class);
                String sbuuid = sbxx.getSbuuid();
                if(GyUtils.isNotNull(reqMap.get("sbuuid"))){
                    sbbService.updateSbbData((String) reqMap.get("sbuuid"));//作废前一条
                }
//                sbxxService.updateSbxxData(sbuuid);
                sbxx.setGzlxDm1("1");
                CommonResult<List<CompanyDTO>> companyRes = companyApi.getjgxxsByNsrsbh((String)reqMap.get("nsrsbh"));
                if(GyUtils.isNotNull(companyRes.getData())){
                    sbxx.setNsrmc(companyRes.getData().get(0).getJgmc1());
                }
                sbxx.setNsrsbh((String) reqMap.get("nsrsbh"));
                sbxx.setSbsxDm("11");
                sbxx.setPzxh(String.valueOf(bodyMap.get("yzpzxh")));
                sbxx.setSkssqq(DateUtils.strToDate(sssqQ));
                sbxx.setSkssqz(DateUtils.strToDate(sssqZ));
                sbxx.setZfbz1("N");
                sbxx.setSbrq1(DateUtils.strToDate(DateUtils.dateToString(new Date(), 3)));
                sbxxList.add(sbxx);
                sbbService.saveOrUpdate(sbxxList);
                String ybtse = "";
                if(GyUtils.isNotNull(bodyMap.get("ybtse"))){
                    ybtse = String.valueOf(bodyMap.get("ybtse"));
                }
                String pzxh = "";
                if(GyUtils.isNotNull(bodyMap.get("yzpzxh"))){
                    pzxh = String.valueOf(bodyMap.get("yzpzxh"));
                }
                if (GyUtils.isNotNull(ybtse)&& GyUtils.isNotNull(pzxh)) {
                    SbjgcxRespDTO sbjgcxRespDTO = new SbjgcxRespDTO();
                    sbjgcxRespDTO.setYbtse(ybtse);
                    sbjgcxRespDTO.setSbuuid(sbuuid);
                    sbjgcxRespDTO.setPzxh(pzxh);
                    if(GyUtils.isNotNull(reqMap.get("sbrwuuid"))){//更正时无法获取sbrwuuid怎么回写申报任务？？
                        znsbNssbSbrwService.updateSbcg((String) reqMap.get("sbrwuuid"), sbjgcxRespDTO);
                        updateCjrbzjSbb(sbxx, sbuuid, pzxh);
                    }else {
                        ZnsbNssbSbrwDO sbrwDO = znsbNssbSbrwService.getSbrwBySbuuid((String)reqMap.get("sbuuid"));
                        if(GyUtils.isNotNull(sbrwDO)){
                            znsbNssbSbrwService.updateSbcg(sbrwDO.getSbrwuuid(), sbjgcxRespDTO);
                            updateCjrbzjSbb(sbxx, sbuuid, pzxh);
                        }else {
                            return CommonResult.error("未查询到申报uuid为"+ sbuuid+"的申报任务。");
                        }
                    }
                }
            }
            return CommonResult.success(dataMap);
        }
        return CommonResult.error("返回异常："+ JsonUtils.toJson(result));
    }

    private void updateCjrbzjSbb(SbxxDTO sbxx, String sbuuid, String pzxh) {
        ZnsbCjrjybzjDO queryParam = new ZnsbCjrjybzjDO();
        queryParam.setNsrsbh(sbxx.getNsrsbh());
        queryParam.setSkssqq(sbxx.getSkssqq().toInstant()
                .atZone(ZoneId.systemDefault()) // 指定时区（如系统默认时区）
                .toLocalDate());
        queryParam.setSkssqz(sbxx.getSkssqz().toInstant()
                .atZone(ZoneId.systemDefault()) // 指定时区（如系统默认时区）
                .toLocalDate());
        List cjrjybzjDOList = queryLocalCbj(queryParam);
        if(GyUtils.isNotNull(cjrjybzjDOList)){
            ZnsbCjrjybzjDO cjrjybzjDO = (ZnsbCjrjybzjDO) cjrjybzjDOList.get(0);
            cjrjybzjDO.setSbuuid(sbuuid);
            cjrjybzjDO.setPzxh(GYCastUtils.cast2BigDecimal(pzxh));
            cjrjybzjDO.setSbrq(sbxx.getSbrq1().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
            this.saveOrUpdateCjrjybzj(cjrjybzjDO);
        }
    }

    @Override
    public CommonResult<Object> invokeSjjh(String ywbm, String sjjhlxDm, String jsonStr, Map reqMap) {
        log.error(ywbm+"_"+sjjhlxDm+"入参:"+ JsonUtils.toJson(reqMap));
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm(sjjhlxDm);
        sjjhDTO.setYwbm(ywbm);
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setDjxh((String) reqMap.get("djxh"));
        sjjhDTO.setNsrsbh((String) reqMap.get("nsrsbh"));
        sjjhDTO.setXzqhszDm((String) reqMap.get("xzqhszDm"));
        sjjhDTO.setBwnr(JsonUtils.toJson(jsonStr));
        CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
        return result;
    }
    @Override
    public CommonResult<Object> sbZf(Map<String, Object> reqVO) {
        final Map<String, String> sbsj = (Map<String, String>) reqVO.get("sbsj");
        final String Pzxh = sbsj.get("pzxh");
        final String SbbMc = "《残疾人就业保障金缴费申报表》";
        final String Sbuuid = sbsj.get("sbuuid");
        final String Skssqq = (String) reqVO.get("skssqq");
        final String Skssqz = (String) reqVO.get("skssqz");
        final String YzpzzlDm = YzpzzlEnum.CJRJYBZJ.getDm();
        final String ZsxmDm = ZsxmDmEnum.CBJ.getCode();
        final String Zfyy = sbsj.get("zfyy");
        final String djxh = (String) reqVO.get("djxh");
        final String nsrsbh = (String) reqVO.get("nsrsbh");
        String xzqhszDm = (String) reqVO.get("xzqhszDm");
        Map<String, String> zfMap = new HashMap<>();
        zfMap.put("Pzxh", Pzxh);
        zfMap.put("SbbMc", SbbMc);
        zfMap.put("Sbuuid", Sbuuid);
        zfMap.put("Skssqq", Skssqq);
        zfMap.put("Skssqz", Skssqz);
        zfMap.put("ZsxmDm", ZsxmDm);
        zfMap.put("Zfyy", Zfyy);
        zfMap.put("YzpzzlDm", YzpzzlDm);
        Map req = new HashMap();
        req.put("djxh",djxh);
        req.put("nsrsbh",nsrsbh);
        req.put("xzqhszDm",xzqhszDm);
        CommonResult<Object> result = this.invokeSjjh(GyEnum.SBZF.getDm(),"ZF00000001", JsonUtils.toJson(zfMap), req);
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("sbuuid", Sbuuid);
        Map<String, String> map = new HashMap<>();
        if ("1".equals(String.valueOf(result.getCode()))) {
            final Map<String, Object> dataMap = (Map<String, Object>) result.getData();
            Map<String, Object> resultMap = (Map<String, Object>) dataMap.get("Result");
            if ("0000".equals(String.valueOf(resultMap.get("code")))) {
                reqMap.put("result", "true");
                reqMap.put("msg", "");
                sbcwgzService.tysbzfAfter(reqMap);
                map.put("code", "1");
                return CommonResult.success(map);
            } else {
                reqMap.put("result", "false");
                reqMap.put("msg", String.valueOf(resultMap.get("message")));
                sbcwgzService.tysbzfAfter(reqMap);
//                return CommonResult.error(-1, String.valueOf(resultMap.get("message")), "N");
                map.put("code", "-1");
                map.put("msg", String.valueOf(resultMap.get("message")));
                return CommonResult.success(map);
            }

        } else {
            reqMap.put("result", "false");
            reqMap.put("msg", result.getMsg());
            sbcwgzService.tysbzfAfter(reqMap);
//            return CommonResult.error(-1, result.getMsg(),"N");
            map.put("code", "-1");
            map.put("msg", result.getMsg());
            return CommonResult.success(map);
        }
    }
    @Override
    public List<ZnsbCjrjybzjDO> queryLocalCbj(ZnsbCjrjybzjDO cjrjybzjDO) {
        LambdaQueryWrapperX<ZnsbCjrjybzjDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eqIfPresent(ZnsbCjrjybzjDO::getSbuuid, cjrjybzjDO.getSbuuid());
        queryWrapper.eqIfPresent(ZnsbCjrjybzjDO::getSkssqq, cjrjybzjDO.getSkssqq());
        queryWrapper.eqIfPresent(ZnsbCjrjybzjDO::getSkssqz, cjrjybzjDO.getSkssqz());
        queryWrapper.eqIfPresent(ZnsbCjrjybzjDO::getNsrsbh, cjrjybzjDO.getNsrsbh());
        List<ZnsbCjrjybzjDO> result = cjrjybzjMapper.selectList(queryWrapper);
        return result;
    }
    @Override
    public boolean saveOrUpdateCjrjybzj(ZnsbCjrjybzjDO cjrjybzjDO) {
        if(GyUtils.isNull(cjrjybzjDO.getUuid())){
            cjrjybzjDO.setUuid(GyUtils.getUuid());
        }
        Boolean result = cjrjybzjMapper.insertOrUpdate(cjrjybzjDO);
        return result;
    }
}
