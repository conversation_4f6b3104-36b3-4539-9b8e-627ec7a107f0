package com.css.znsb.nssb.service.xfs.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.ServiceException;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.nsrxx.*;
import com.css.znsb.nssb.constants.SfEnum;
import com.css.znsb.nssb.constants.dto.QueryHznsbaxxReqDTO;
import com.css.znsb.nssb.constants.dto.QueryHznsbaxxRespDTO;
import com.css.znsb.nssb.constants.enums.*;
import com.css.znsb.nssb.constants.vo.sbrw.LzsXtcsBmConstant;
import com.css.znsb.nssb.mapper.sbrw.SbSbxxDOMapper;
import com.css.znsb.nssb.pojo.bo.qcxx.common.SbQcSbsxBO;
import com.css.znsb.nssb.pojo.bo.qcxx.common.SbSbbItem;
import com.css.znsb.nssb.pojo.bo.qcxx.common.fzxx.SbQcNsrxxVO;
import com.css.znsb.nssb.pojo.domain.csdm.CsZnsbLzsSbbpzDO;
import com.css.znsb.nssb.pojo.domain.sbrw.SbSbxxDO;
import com.css.znsb.nssb.pojo.dto.sb.core.support.common.XwqyLslfJmxzDTO;
import com.css.znsb.nssb.pojo.dto.xfs.*;
import com.css.znsb.nssb.pojo.dto.xfs.sbbc.XfsSbBcSbbDTO;
import com.css.znsb.nssb.pojo.dto.xfs.zcsb.*;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.csh.ZzsybnsrCshLqReqDTO;
import com.css.znsb.nssb.pojo.vo.xfs.XfsQcFzxxVO;
import com.css.znsb.nssb.pojo.vo.xfs.XfsQcxxItemVO;
import com.css.znsb.nssb.pojo.vo.xfs.qcxxcx.XfsLqptQcsResponseDTO;
import com.css.znsb.nssb.pojo.vo.xfs.qcxxcx.XfsLqptQcxxResponseDTO;
import com.css.znsb.nssb.service.gy.RdHznsqyrdsqMxbService;
import com.css.znsb.nssb.service.phjm.PhjmService;
import com.css.znsb.nssb.service.xfs.XfsSbQcxxService;
import com.css.znsb.nssb.service.zzsybnsrsb.ZzsybnsrNsrxxService;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.nssb.utils.BigDecimalUtils;
import com.css.znsb.nssb.utils.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_FORMAT;
import static com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.css.znsb.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.css.znsb.nssb.constants.CsdmConstants.*;
import static com.css.znsb.nssb.constants.enums.NssbErrorCodeConstants.NSRXX_NOT_EXISTS;
import static com.css.znsb.nssb.constants.enums.NssbErrorCodeConstants.setZzsybnsrsbCommonError;

/***
 *
 * @description 消费税申报期初信息ServiceImpl
 * @package com.css.znsb.nssb.service.xfs.impl
 * @file XfsSbQcxxServiceImpl 创建时间:2024/4/28 9:39
 * @copyright Copyright (c) 2022 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Slf4j
@Validated
@Service
public class XfsSbQcxxServiceImpl implements XfsSbQcxxService {

    @Resource
    private PhjmService phjmService;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private SbSbxxDOMapper sbxxDOMapper;

    @Resource
    private RdHznsqyrdsqMxbService hznsqyrdsqMxbService;

    @Resource
    private ZzsybnsrNsrxxService nsrxxService;

    @Resource
    private SjjhService sjjhService;


    private static final String YSXFP_CPY_ZSPMDM = "*********,*********,*********,*********,*********,*********";

    /**
     * @name 消费税期初信息查询
     * @time 2024/4/28 10:01
     * @param: djxh
     * @param: nsrsbh
     * @param: sbrwuuid
     * @param: skssqq
     * @param: skssqz
     * @param: qcxxResponseDTO
     * @return: java.util.List<com.css.znsb.nssb.pojo.vo.xfs.XfsQcxxItemVO>
     * <AUTHOR>
     **/
    @Override
    public List<XfsQcxxItemVO> buildXfsZcsbQcxx(String djxh, String nsrsbh, String sbrwuuid, String skssqq,
                                                String skssqz, XfsLqptQcsResponseDTO qcxxResponseDTO) {
        log.info("消费税乐企辅助平台正常申报期初信息数据TO页面数据转换开始, resp---" + qcxxResponseDTO);
        List<XfsQcxxItemVO> itemVOList = new ArrayList<>();
        final Date ssqq = cn.hutool.core.date.DateUtil.parse(skssqq);
        final Date ssqz = cn.hutool.core.date.DateUtil.parse(skssqz);
        // 1、获取纳税人信息，获取标签信息
        final ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO = this.getNsrxx(djxh, nsrsbh, ssqq, ssqz);
        XfsQcxxItemVO itemVO = new XfsQcxxItemVO();
        // 2、设置纳税人信息,
        this.setNsrxx(qyjbxxmxResVO, itemVO);
        // 3、初始化申报表list
        final List<String> sbbidList = new ArrayList<>();
        itemVO.setSbbidList(sbbidList);
        // 4、补充标签信息
        this.bqbqxx(qyjbxxmxResVO);
        // 5、生成最终数据
        getXfsQcxx(djxh, skssqq, skssqz, itemVO, qyjbxxmxResVO, qcxxResponseDTO);
        itemVO.setSbrwuuid(sbrwuuid);
        itemVO.getSbsx().setJylxDm(JylxDmEnum.ZCSB.getCode());
        itemVO.setGzsbbz(YesOrNoEnum.N.getCode());
        itemVOList.add(itemVO);
        return itemVOList;
    }

    @Override
    public List<XfsQcxxItemVO> cwgzcsh(String djxh, String nsrsbh, String skssqq, String skssqz) {
        // 1、先查询乐企错误更正初始化数据
        final List<XfsQcxxItemVO> qcxxList = this.getCwgzcshDataByLqfzpt(djxh, nsrsbh, skssqq, skssqz);
        return qcxxList;
    }

    @Override
    public List<XfsQcxxItemVO> buildXfsGzsbQcxx(String djxh, String nsrsbh, String skssqq, String skssqz, XfsLqptQcsResponseDTO qcxxResponseDTO) {
        log.info("消费税乐企辅助平台更正申报期初信息数据TO页面数据转换开始, resp---" + qcxxResponseDTO);
        List<XfsQcxxItemVO> itemVOList = new ArrayList<>();
        final Date ssqq = cn.hutool.core.date.DateUtil.parse(skssqq);
        final Date ssqz = cn.hutool.core.date.DateUtil.parse(skssqz);
        // 1、获取纳税人信息，获取标签信息
        final ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO = this.getNsrxx(djxh, nsrsbh, ssqq, ssqz);
        // 2、补充标签信息
        this.bqbqxx(qyjbxxmxResVO);
        XfsQcxxItemVO itemVO = new XfsQcxxItemVO();
        // 3、设置纳税人信息,
        this.setNsrxx(qyjbxxmxResVO, itemVO);
        // 4、初始化申报表list
        final List<String> sbbidList = new ArrayList<>();
        itemVO.setSbbidList(sbbidList);
        // 5、生成最终数据
        getXfsQcxx(djxh, skssqq, skssqz, itemVO, qyjbxxmxResVO, qcxxResponseDTO);
        itemVO.getSbsx().setJylxDm(JylxDmEnum.GZSB.getCode());
        itemVOList.add(itemVO);
        return itemVOList;
    }


    @Override
    public String queryPpfqymc(String nsrsbh) {
        String nsrmc = "";
        final ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
        reqVO.setNsrsbh(nsrsbh);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByNsrsbh(reqVO);

        if (GyUtils.isNull(nsrxxRes.getData())) {
            return nsrmc;
        }
        if (GyUtils.isNull(nsrxxRes.getData().getJbxxmxsj())) {
            return nsrmc;
        }
        final ZnsbMhzcQyjbxxmxResVO nsrxx = nsrxxRes.getData();
        nsrmc = nsrxx.getJbxxmxsj().get(0).getNsrmc();
        return nsrmc;
    }

    /**
     * 获取错误更正初始化数据（通过乐企辅助平台）
     *
     * @param djxh   登记序号
     * @param nsrsbh 纳税人识别号
     * @param skssqq 税款所属期起
     * @param skssqz 税款所属期止
     * @return 更正申报初始化响应
     */
    private List<XfsQcxxItemVO> getCwgzcshDataByLqfzpt(String djxh, String nsrsbh, String skssqq, String skssqz)
            throws ServiceException {
        final ZzsybnsrCshLqReqDTO reqDTO = new ZzsybnsrCshLqReqDTO();
        reqDTO.setDjxh(djxh);
        reqDTO.setSkssqq(skssqq);
        reqDTO.setSkssqz(skssqz);

        final CommonResult<CompanyBasicInfoDTO> jgxxResult = companyApi.basicInfo(djxh, nsrsbh);
        String xzqhszDm = "";
        if (jgxxResult.isSuccess() && GyUtils.isNotNull(jgxxResult.getData())) {
            xzqhszDm = jgxxResult.getData().getXzqhszDm();
        }
        xzqhszDm = SfEnum.getSsjXzqhszDmByXzqhszDm(xzqhszDm);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setYwbm(YzpzzlEnum.XFS.getDm());
        sjjhDTO.setSjjhlxDm("CX00000002");
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
        sjjhDTO.setBwnr(JSONUtil.toJsonStr(reqDTO));

        final CommonResult<Object> cwgzcshResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (cwgzcshResult.isSuccess() && GyUtils.isNotNull(cwgzcshResult.getData())) {
            final String resJson = (String) cwgzcshResult.getData();
            final XfsLqptQcxxResponseDTO qcxx = JsonUtils.toBean(resJson, XfsLqptQcxxResponseDTO.class);
            List<XfsQcxxItemVO> qcxxItemVOList = this.buildXfsGzsbQcxx(djxh, nsrsbh, skssqq, skssqz, qcxx.getLsxxList().get(0));
            qcxxItemVOList.get(0).setGzsbbz(YesOrNoEnum.Y.getCode());
            return qcxxItemVOList;
        } else {
            throw exception(setZzsybnsrsbCommonError("获取错误更正初始化数据失败。"));
        }
    }

    /**
     * @name 通过税费种认定和CS_SB_XFSSLPZB补全标签信息
     * @time 2024/6/26 20:03
     * @param: qyjbxxmxResVO
     * <AUTHOR>
     **/
    private void bqbqxx(ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO) {
        // 获取税费种认定信息
        List<SfzrdmxxxVO> sfzrdmxxx = qyjbxxmxResVO.getSfzrdmxxx();
        sfzrdmxxx = sfzrdmxxx.stream().sorted(Comparator.comparing(SfzrdmxxxVO::getZspmDm)).collect(Collectors.toList());
        final List<String> sfzZspmList = sfzrdmxxx.stream().filter(item -> item.getZsxmDm().startsWith(ZsxmDmEnum.XFS.getCode()))
                .map(SfzrdmxxxVO::getZspmDm).collect(Collectors.toList());
        // 获取标签信息
        final List<NsrbqxxVO> nsrbqxx = qyjbxxmxResVO.getNsrbqxx();
        final List<String> nsrBqList = nsrbqxx.stream().map(NsrbqxxVO::getBqmc).collect(Collectors.toList());
        // 根据征收品目来查询CS_SB_XFSSLPZB，根据是否允许扣除，是否委托加工，是否减免税来判断有无对应标签
        // 通过税费种认定来判断是否为高档手表或者贵重首饰标签
        for (String zspmDm : sfzZspmList) {
            final Map<String, Object> xfsslpzMap = CacheUtils.getTableData(CS_SB_XFSSLPZB, zspmDm);
            final String sfkydk = xfsslpzMap.get("sfkydk").toString();
            final String sffsmssx = xfsslpzMap.get("sffsmssx").toString();
            final String sffswtjgyw = xfsslpzMap.get("sffswtjgyw").toString();
            if (YesOrNoEnum.Y.getCode().equals(sfkydk) && !nsrBqList.contains(NsrbqEnum.ZYKCXFSQY.getCode())) {
                final NsrbqxxVO nsrbqxxVO = new NsrbqxxVO();
                nsrbqxxVO.setBqmc(NsrbqEnum.ZYKCXFSQY.getCode());
                nsrbqxx.add(nsrbqxxVO);
                nsrBqList.add(NsrbqEnum.ZYKCXFSQY.getCode());
            }
            if (YesOrNoEnum.Y.getCode().equals(sffswtjgyw) && !nsrBqList.contains(NsrbqEnum.ZYKCWTJGYNSKXFSQY.getCode())) {
                final NsrbqxxVO nsrbqxxVO = new NsrbqxxVO();
                nsrbqxxVO.setBqmc(NsrbqEnum.ZYKCWTJGYNSKXFSQY.getCode());
                nsrbqxx.add(nsrbqxxVO);
                nsrBqList.add(NsrbqEnum.ZYKCWTJGYNSKXFSQY.getCode());
            }
            if (YesOrNoEnum.Y.getCode().equals(sffsmssx) && !nsrBqList.contains(NsrbqEnum.XFSJMQY.getCode())) {
                final NsrbqxxVO nsrbqxxVO = new NsrbqxxVO();
                nsrbqxxVO.setBqmc(NsrbqEnum.XFSJMQY.getCode());
                nsrbqxx.add(nsrbqxxVO);
                nsrBqList.add(NsrbqEnum.XFSJMQY.getCode());
            }
            if ((XfsGdsbDmEnum.GDSB.getZspmDm().equals(zspmDm) || XfsGdsbDmEnum.GZSSJZBYS.getZspmDm().equals(zspmDm))
                    && !nsrBqList.contains(NsrbqEnum.XSGDSBGZSSZBYSXFSQY.getCode())) {
                final NsrbqxxVO nsrbqxxVO = new NsrbqxxVO();
                nsrbqxxVO.setBqmc(NsrbqEnum.XSGDSBGZSSZBYSXFSQY.getCode());
                nsrbqxx.add(nsrbqxxVO);
                nsrBqList.add(NsrbqEnum.XFSJMQY.getCode());
            }
        }
    }

    /**
     * @name 乐企未返回预填信息，自己构造初始化数据
     * @time 2024/5/18 14:57
     * @param: skssqq
     * @param: skssqz
     * @param: qyjbxxmxResVO
     * @param: qcxxResponseDTO
     * <AUTHOR>
     **/
    @Deprecated
    private void buildLqQcxxWithoutYtxx(Date skssqq, Date skssqz, ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO, XfsLqfzptQcxxResponseDTO qcxxResponseDTO) {
        // 1. 获取纳税人标签信息和税费种认定信息
        final List<SfzrdmxxxVO> sfzrdmxxx = qyjbxxmxResVO.getSfzrdmxxx();
        final List<NsrbqxxVO> nsrbqxx = qyjbxxmxResVO.getNsrbqxx();
        final List<String> nsrBqList = nsrbqxx.stream().map(NsrbqxxVO::getBqmc).collect(Collectors.toList());
        // 2. 根据标签和税费种认定信息构建当期申报初始化数据
        XfsSbxxFormVO dqywbw = new XfsSbxxFormVO();
        // 2.1 主表
        XfsSbxxZbVO xfssbzb = new XfsSbxxZbVO();
        // 2.1.1纳税人信息
        Nsrxxvo nsrxxvo = BeanUtil.toBean(qyjbxxmxResVO.getJbxxmxsj().get(0), Nsrxxvo.class);
        // 2.1.2税款信息列表
        List<XfsSbxxSkxxVO> xfssbxxGrid = getDefaultSkxxList(sfzrdmxxx);
        // 2.1.3申报数据信息表单
        XfsSbFormxxVO sbsjxxForm = new XfsSbFormxxVO();

        // 2.1.4受理信息
        XfsSbxxSlxxVO slxxForm = new XfsSbxxSlxxVO();
        xfssbzb.setNsrxxvo(nsrxxvo);
        xfssbzb.setXfssbxxGrid(xfssbxxGrid);
        xfssbzb.setSbsjxxForm(sbsjxxForm);
        xfssbzb.setSlxxForm(slxxForm);
        dqywbw.setXfssbzb(xfssbzb);
        // 2.2 附加税
        XfsSbxxFjsVO xfsFjs = new XfsSbxxFjsVO();

        dqywbw.setXfsFjs(xfsFjs);
        // 2.3 成品油企业
        if (nsrBqList.contains(NsrbqEnum.CPYXFSQY.getCode())) {
            // 2.3.1 本期准予扣除计算表成品油
            final XfsSbxxBqzykcsejsbCpyVO bqzykcsejsbcpy = new XfsSbxxBqzykcsejsbCpyVO();
            List<XfsBqdksejkcjsCpyVO> dksejkcjsGrid = new ArrayList<>();
            List<XfsBxrlyclycqkCpyVO> bxrlyclycqkGrid = new ArrayList<>();
            bqzykcsejsbcpy.setDksejkcjsGrid(dksejkcjsGrid);
            bqzykcsejsbcpy.setBxrlyclycqkGrid(bxrlyclycqkGrid);
            dqywbw.setBqzykcsejsbcpy(bqzykcsejsbcpy);
            // 2.3.2 石脑油、燃料油生产、外购、耗用、库存月度统计表
            final XfsSnyrlyscwghykcydtjbVO snyrlyscwghykcydtjbywbw = new XfsSnyrlyscwghykcydtjbVO();

            // 2.3.3 使用企业外购石脑油、燃料油凭证明细表
            XfsSyqywgsnyrlypzmxbVO syqywgsnyrlypzmxbywbw = new XfsSyqywgsnyrlypzmxbVO();


        }
        // 3.《本期委托加工收回情况报告表》	标签为准予扣除委托加工已纳税款的消费税企业的纳税人下发。
        if (nsrBqList.contains(NsrbqEnum.ZYKCWTJGYNSKXFSQY.getCode())) {

        }
        // 4.《本期减（免）税额明细表》	标签为消费税减免企业的纳税人下发
        if (nsrBqList.contains(NsrbqEnum.XFSJMQY.getCode())) {

        }
        // 5.《卷烟批发企业月份销售明细清单》（卷烟批发环节消费税纳税人适用）标签为卷烟批发企业的纳税人下发
        if (nsrBqList.contains(NsrbqEnum.JYPFQY.getCode())) {

        }
        // 《本期减（免）税额明细表》标签为消费税减免企业的纳税人下发
        if (nsrBqList.contains(NsrbqEnum.XFSJMQY.getCode())) {

        }
        // 6.《汇总纳税企业消费税分配表》标签为消费税分摊缴纳总机构企业的纳税人下发。
        if (nsrBqList.contains(NsrbqEnum.XFSFTJNZJGQY.getCode())) {

        }
        // 7.《卷烟生产企业合作生产卷烟消费税情况报告表（卷烟生产环节消费税纳税人适用）》	标签为卷烟生产企业的纳税人下发。
        if (nsrBqList.contains(NsrbqEnum.JYSCQY.getCode())) {

        }
        // 8.判断是否下发高档手表、贵重首饰及珠宝玉石销售明细表
        if (nsrBqList.contains(NsrbqEnum.XSGDSBGZSSZBYSXFSQY.getCode())) {

        }
        // 9.《本期准予扣除税额计算表》	标签为准予扣除的消费税企业或准予扣除委托加工已纳税款的消费税企业
        // 且标签不为成品油消费税企业的纳税人下发
        if (!nsrBqList.contains(NsrbqEnum.CPYXFSQY.getCode()) && (nsrBqList.contains(NsrbqEnum.ZYKCWTJGYNSKXFSQY.getCode())
                || nsrBqList.contains(NsrbqEnum.ZYKCXFSQY.getCode()))) {

        }
        qcxxResponseDTO.setDqywbw(dqywbw);
    }

    private List<XfsSbxxSkxxVO> getDefaultSkxxList(List<SfzrdmxxxVO> sfzrdmxxx) {
        final List<XfsSbxxSkxxVO> skxxVOList = new ArrayList<>();
        final List<String> zspmDmList = new ArrayList<>();
        final List<Map<String, Object>> tableData = CacheUtils.getTableData(CS_SB_XFSSLPZB);
        final Map<String, Map<String, Object>> zspmMap = tableData.stream()
                .collect(Collectors.toMap(
                        m -> m.get("zspmDm").toString(),
                        m -> m));
        for (SfzrdmxxxVO vo : sfzrdmxxx) {
            // 1. 如果税费种认定中存在“纯生物柴油”时，消费税申报时应税消费税品目需报送“柴油”。
            if ("101020611".equals(vo.getZspmDm())) {
                final Map<String, Object> xfsslMap = zspmMap.get("*********");
                XfsSbxxSkxxVO skxxVO = new XfsSbxxSkxxVO();
                skxxVO.setZspmDm("*********");
                skxxVO.setZspmmc(xfsslMap.get("zspmmc").toString());
                skxxVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                skxxVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                skxxVO.setBnljxse(BigDecimal.ZERO);
                skxxVO.setBnljxssl(BigDecimal.ZERO);
                skxxVO.setBnljynse(BigDecimal.ZERO);
                skxxVO.setYnse(BigDecimal.ZERO);
                skxxVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                skxxVO.setXse(BigDecimal.ZERO);
                skxxVO.setXssl(BigDecimal.ZERO);
                zspmDmList.add("*********");
                skxxVOList.add(skxxVO);
            } else if ("101020610".equals(vo.getZspmDm())) {
                //如果征收品目存在“101020610废矿物油”，那么检查本次申报信息中，是否同时存在下面5个征收品代码
                //********* 柴油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    XfsSbxxSkxxVO skxxVO = new XfsSbxxSkxxVO();
                    skxxVO.setZspmDm("*********");
                    skxxVO.setZspmmc(xfsslMap.get("zspmmc").toString());
                    skxxVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    skxxVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    skxxVO.setBnljxse(BigDecimal.ZERO);
                    skxxVO.setBnljxssl(BigDecimal.ZERO);
                    skxxVO.setBnljynse(BigDecimal.ZERO);
                    skxxVO.setYnse(BigDecimal.ZERO);
                    skxxVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    skxxVO.setXse(BigDecimal.ZERO);
                    skxxVO.setXssl(BigDecimal.ZERO);
                    zspmDmList.add("*********");
                    skxxVOList.add(skxxVO);
                }
                //********* 石脑油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    XfsSbxxSkxxVO skxxVO = new XfsSbxxSkxxVO();
                    skxxVO.setZspmDm("*********");
                    skxxVO.setZspmmc(xfsslMap.get("zspmmc").toString());
                    skxxVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    skxxVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    skxxVO.setBnljxse(BigDecimal.ZERO);
                    skxxVO.setBnljxssl(BigDecimal.ZERO);
                    skxxVO.setBnljynse(BigDecimal.ZERO);
                    skxxVO.setYnse(BigDecimal.ZERO);
                    skxxVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    skxxVO.setXse(BigDecimal.ZERO);
                    skxxVO.setXssl(BigDecimal.ZERO);
                    zspmDmList.add("*********");
                    skxxVOList.add(skxxVO);
                }
                //********* 润滑油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    XfsSbxxSkxxVO skxxVO = new XfsSbxxSkxxVO();
                    skxxVO.setZspmDm("*********");
                    skxxVO.setZspmmc(xfsslMap.get("zspmmc").toString());
                    skxxVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    skxxVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    skxxVO.setBnljxse(BigDecimal.ZERO);
                    skxxVO.setBnljxssl(BigDecimal.ZERO);
                    skxxVO.setBnljynse(BigDecimal.ZERO);
                    skxxVO.setYnse(BigDecimal.ZERO);
                    skxxVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    skxxVO.setXse(BigDecimal.ZERO);
                    skxxVO.setXssl(BigDecimal.ZERO);
                    zspmDmList.add("*********");
                    skxxVOList.add(skxxVO);
                }
                //********* 燃料油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    XfsSbxxSkxxVO skxxVO = new XfsSbxxSkxxVO();
                    skxxVO.setZspmDm("*********");
                    skxxVO.setZspmmc(xfsslMap.get("zspmmc").toString());
                    skxxVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    skxxVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    skxxVO.setBnljxse(BigDecimal.ZERO);
                    skxxVO.setBnljxssl(BigDecimal.ZERO);
                    skxxVO.setBnljynse(BigDecimal.ZERO);
                    skxxVO.setYnse(BigDecimal.ZERO);
                    skxxVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    skxxVO.setXse(BigDecimal.ZERO);
                    skxxVO.setXssl(BigDecimal.ZERO);
                    zspmDmList.add("*********");
                    skxxVOList.add(skxxVO);
                }
                //********* 汽油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    XfsSbxxSkxxVO skxxVO = new XfsSbxxSkxxVO();
                    skxxVO.setZspmDm("*********");
                    skxxVO.setZspmmc(xfsslMap.get("zspmmc").toString());
                    skxxVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    skxxVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    skxxVO.setBnljxse(BigDecimal.ZERO);
                    skxxVO.setBnljxssl(BigDecimal.ZERO);
                    skxxVO.setBnljynse(BigDecimal.ZERO);
                    skxxVO.setYnse(BigDecimal.ZERO);
                    skxxVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    skxxVO.setXse(BigDecimal.ZERO);
                    skxxVO.setXssl(BigDecimal.ZERO);
                    zspmDmList.add("*********");
                    skxxVOList.add(skxxVO);
                }
                continue;
            }
            if (ZsxmDmEnum.XFS.getCode().equals(vo.getZsxmDm()) && !zspmDmList.contains(vo.getZsxmDm())) {
                final Map<String, Object> xfsslMap = zspmMap.get(vo.getZspmDm());
                XfsSbxxSkxxVO skxxVO = new XfsSbxxSkxxVO();
                skxxVO.setZspmDm(vo.getZspmDm());
                skxxVO.setZspmmc(xfsslMap.get("zspmmc").toString());
                skxxVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                skxxVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                skxxVO.setBnljxse(BigDecimal.ZERO);
                skxxVO.setBnljxssl(BigDecimal.ZERO);
                skxxVO.setBnljynse(BigDecimal.ZERO);
                skxxVO.setYnse(BigDecimal.ZERO);
                skxxVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                skxxVO.setXse(BigDecimal.ZERO);
                skxxVO.setXssl(BigDecimal.ZERO);
                zspmDmList.add(vo.getZspmDm());
                skxxVOList.add(skxxVO);
            }
        }
        return skxxVOList;
    }

    /**
     * @name 根据乐企返回的当期数据构建预填信息
     * @time 2024/5/10 18:19
     * @param: djxh
     * @param: skssqq
     * @param: skssqz
     * @param: itemVO
     * @param: qyjbxxmxResVO
     * @param: qcxxResponseDTO
     * @param: cwgzFlag
     * <AUTHOR>
     **/
    private void getXfsQcxx(String djxh, String skssqq, String skssqz, XfsQcxxItemVO itemVO,
                            ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO, XfsLqptQcsResponseDTO qcxxResponseDTO) {
        log.info("消费税乐企辅助平台根据当期数据TO页面数据转换开始, resp---" + qcxxResponseDTO);
        // 设置申报事项信息
        this.setSbsx(qcxxResponseDTO, itemVO);
        // 设置预填信息
        this.setYtxx(qcxxResponseDTO, itemVO, qyjbxxmxResVO);
        // 设置辅助项信息
        this.setFzxx(qcxxResponseDTO, itemVO, qyjbxxmxResVO);
        // 设置md5
        final String md5DigestAsHex = DigestUtils.md5DigestAsHex(JsonUtils.toJson(itemVO.getYtxx()).getBytes(StandardCharsets.UTF_8));
        itemVO.getSbsx().setQcxxMd5(md5DigestAsHex);
        final String zgswjDm = itemVO.getNsrxx().getZgswjDm();
        // 预填信息特殊处理（含dm转名称）
        handleYtxx(qyjbxxmxResVO, itemVO.getYtxx(), djxh, skssqq, skssqz, zgswjDm);
        // 【消费税】本期委托加工表，需要差异化下拉框
        // １.成品油：固定可选择6个油品（石脑油、汽油、柴油、润滑油、燃料油、溶剂油）；
        // ２.非成品油：根据主表下发的品目来
        reSetYsxfpCpyLb(itemVO.getYtxx(), qyjbxxmxResVO.getNsrbqxx());
        // 初始化本期准予扣除(成品油)表节点
        reSetBqzykcCpyNode(itemVO.getYtxx(), qyjbxxmxResVO.getNsrbqxx());
    }

    /**
     * @name 设置申报事项信息
     * @time 2024/4/28 15:11
     * @param: qcxxResponseDTO
     * @param: itemVO
     * <AUTHOR>
     **/
    private void setSbsx(XfsLqptQcsResponseDTO qcxxResponseDTO, XfsQcxxItemVO itemVO) {
        final SbQcSbsxBO sbsx = qcxxResponseDTO.getSbsx();
        if (GyUtils.isNull(qcxxResponseDTO.getSbsx())) {
            return;
        }
        sbsx.setSbmsDm("3");
        sbsx.setRdSbmsDm("3");
        sbsx.setYzpzzlDm(YzpzzlEnum.XFS.getDm());
        sbsx.setYzpzzlMc(YzpzzlEnum.XFS.getMc());
        sbsx.setSbrq(cn.hutool.core.date.DateUtil.today());
        itemVO.setSbsx(sbsx);
    }

    /**
     * 获取纳税人信息
     *
     * @return {@link ZnsbMhzcQyjbxxmxResVO}
     */
    private ZnsbMhzcQyjbxxmxResVO getNsrxx(String djxh, String nsrsbh, Date skssqq, Date skssqz) {
        final ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
        reqVO.setNsrsbh(nsrsbh);
        reqVO.setDjxh(djxh);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByNsrsbh(reqVO);
        final ZnsbMhzcQyjbxxmxResVO nsrxx = nsrxxRes.getData();
        if (GyUtils.isNull(nsrxx) || GyUtils.isNull(nsrxx.getJbxxmxsj())) {
            // 未获取到纳税人信息
            throw exception(NSRXX_NOT_EXISTS);
        }

        // 根据登记序号匹配第一条纳税人信息
        final Optional<JbxxmxsjVO> jbxxmxsj = nsrxx.getJbxxmxsj().stream()
                .filter(t -> djxh.equals(t.getDjxh()))
                .findFirst();

        if (!jbxxmxsj.isPresent()) {
            throw exception(NSRXX_NOT_EXISTS);
        } else {
            JbxxmxsjVO jbxx = jbxxmxsj.get();
            nsrxx.setJbxxmxsj(CollectionUtil.newArrayList(jbxx));
        }

        if (GyUtils.isNotNull(nsrxx.getSfzrdmxxx())) {
            final List<SfzrdmxxxVO> sfzrdList = nsrxx.getSfzrdmxxx().stream()
                    .filter(t -> djxh.equals(t.getDjxh())
                            && cn.hutool.core.date.DateUtil.compare(skssqq, t.getRdyxqq()) >= 0
                            && cn.hutool.core.date.DateUtil.compare(skssqz, t.getRdyxqz()) <= 0)
                    .collect(Collectors.toList());
            nsrxx.setSfzrdmxxx(sfzrdList);
        }

        if (GyUtils.isNotNull(nsrxx.getNsrbqxx())) {
            final List<NsrbqxxVO> bqxxList = nsrxx.getNsrbqxx().stream()
                    .filter(t -> djxh.equals(t.getDjxh())
                            && cn.hutool.core.date.DateUtil.compare(skssqq, t.getYxqq()) >= 0
                            && cn.hutool.core.date.DateUtil.compare(skssqz, t.getYxqz()) <= 0
                            && YesOrNoEnum.Y.getCode().equals(t.getYxbz()))
                    .collect(Collectors.toList());
            nsrxx.setNsrbqxx(bqxxList);
        }

        if (GyUtils.isNotNull(nsrxx.getNsrzgxx())) {
            final List<NsrzgxxVO> zgxxList = nsrxx.getNsrzgxx().stream()
                    .filter(t -> djxh.equals(t.getDjxh())
                            && cn.hutool.core.date.DateUtil.compare(skssqq, t.getYxqq()) >= 0
                            && cn.hutool.core.date.DateUtil.compare(skssqz, t.getYxqz()) <= 0)
                    .collect(Collectors.toList());
            nsrxx.setNsrzgxx(zgxxList);
        }
        return nsrxx;
    }

    /**
     * @name 设置纳税人信息
     * @time 2024/4/28 15:11
     * @param: qyjbxxmxResVO qyjbxxmxResVO
     * @param: itemVO itemVO
     * <AUTHOR>
     **/
    private void setNsrxx(ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO, XfsQcxxItemVO itemVO) {
        final SbQcNsrxxVO nsrxx = new SbQcNsrxxVO();
        if (ObjectUtil.isNotNull(qyjbxxmxResVO)) {
            // 获取纳税人行政区划代码
            if (CollectionUtil.isNotEmpty(qyjbxxmxResVO.getJbxxmxsj())) {
                final JbxxmxsjVO jbxxmxsjVO = qyjbxxmxResVO.getJbxxmxsj().get(0);
                BeanUtils.copyBean(jbxxmxsjVO, nsrxx);
            }
        }
        itemVO.setNsrxx(nsrxx);
    }

    /**
     * @name 设置预填信息
     * @time 2024/4/28 14:54
     * @param: qcxxResponseDTO
     * @param: itemVO
     * @param: qyjbxxmxResVO
     * <AUTHOR>
     **/
    private void setYtxx(XfsLqptQcsResponseDTO qcxxResponseDTO, XfsQcxxItemVO itemVO,
                         ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO) {
        final XfsSbBcSbbDTO ytxx = qcxxResponseDTO.getLsxx();
        StringBuffer errorMesg = new StringBuffer();
        if (ObjectUtil.isNull(qcxxResponseDTO)) {
            return;
        }
        final List<String> sbbidList = itemVO.getSbbidList();
        final List<NsrbqxxVO> nsrbqxx = qyjbxxmxResVO.getNsrbqxx();
        final List<String> nsrBqList = nsrbqxx.stream().map(NsrbqxxVO::getBqmc).collect(Collectors.toList());

        // 初始化《本期准予扣除税额计算表》（成品油）节点
        if (!nsrBqList.contains(NsrbqEnum.CPYXFSQY.getCode())) {
            ytxx.setSbXfsFbBqzykcsejsbCpy(null);
            ytxx.setSbXfsFbSnyrlyscwghykcydtjb(null);
            ytxx.setSbXfsFbYxftsczztj(null);
            ytxx.setSbXfsFbWgpzmxb(null);
            ytxx.setSbXfsFbZyfp(null);
            ytxx.setSbXfsFbScqyxshssnyrlywsqkmx(null);
        } else {
            sbbidList.add("003");
            sbbidList.add("012");
            sbbidList.add("013");
            sbbidList.add("014");
            sbbidList.add("015");
            sbbidList.add("016");
            final Bqzykcsejsbcpy sbXfsFbBqzykcsejsbCpy = ytxx.getSbXfsFbBqzykcsejsbCpy();
            if (BeanUtil.isNotEmpty(sbXfsFbBqzykcsejsbCpy)) {
                final List<DksejkcjsVO> sbXfsFbZykcCpyDksejkcjs = sbXfsFbBqzykcsejsbCpy.getSbXfsFbZykcCpyDksejkcjs();
                if (CollectionUtils.isEmpty(sbXfsFbZykcCpyDksejkcjs)) {
                    sbXfsFbBqzykcsejsbCpy.setSbXfsFbZykcCpyDksejkcjs(Lists.newArrayList());
                    errorMesg.append("《本期准予扣除税额计算表》（成品油）节点：sbXfsFbZykcCpyDksejkcjs，不允许为空;");
                } else {
                    for (DksejkcjsVO dksejkcjsVO : sbXfsFbZykcCpyDksejkcjs) {
                        final XfsCpyDmEnum cpyEnum = XfsCpyDmEnum.getCpyEnumByXh(dksejkcjsVO.getEwbhxh());
                        if (cpyEnum != null) {
                            // 本期准予扣除下发默认值
                            dksejkcjsVO.setEwbhlbm(cpyEnum.getEwbhlbm());
                            dksejkcjsVO.setZspmDm(cpyEnum.getZspmDm());
                            dksejkcjsVO.setZspmMc(cpyEnum.getZspmMc());
                        }
                    }
                    sbXfsFbBqzykcsejsbCpy.setSbXfsFbZykcCpyDksejkcjs(sbXfsFbZykcCpyDksejkcjs);
                }
                final List<BxrlyclycqkVO> sbXfsFbZykcCpyBxrlyclyqk = sbXfsFbBqzykcsejsbCpy.getSbXfsFbZykcCpyBxrlyclyqk();
                if (CollectionUtils.isEmpty(sbXfsFbZykcCpyBxrlyclyqk)) {
                    sbXfsFbBqzykcsejsbCpy.setSbXfsFbZykcCpyBxrlyclyqk(Lists.newArrayList());
                    errorMesg.append("《本期准予扣除税额计算表》（成品油）节点：sbXfsFbZykcCpyBxrlyclyqk，不允许为空;");
                } else {
                    for (BxrlyclycqkVO lyqkVO : sbXfsFbZykcCpyBxrlyclyqk) {
                        final XfsCpyLyqkDmEnum cpyLyqkEnum = XfsCpyLyqkDmEnum.getCpyEnumByXh(Long.valueOf(lyqkVO.getEwbhxh()));
                        if (cpyLyqkEnum != null) {
                            lyqkVO.setEwbhlbm(cpyLyqkEnum.getEwbhlbm());
                        }
                    }
                    sbXfsFbBqzykcsejsbCpy.setSbXfsFbZykcCpyBxrlyclyqk(sbXfsFbZykcCpyBxrlyclyqk);
                }
                ytxx.setSbXfsFbBqzykcsejsbCpy(sbXfsFbBqzykcsejsbCpy);

                // 初始化"石脑油、燃料油生产、外购、耗用、库存月度统计表"节点
                final Snyrlyscwghykcydtjb snyrlyscwghykcydtjb = ytxx.getSbXfsFbSnyrlyscwghykcydtjb();
                // 获取默认--产品情况列表
                final List<CpqkGridlbVO> defaultCpqkList = getDefaultSnyrlyCpqk();
                // 获取默认--定点直供计划情况
                final List<DdzgjhqkGridlbVO> defaultDdzgList = getDefaultDdzgjhqk();
                // 获取默认--库存耗用情况本期数
                final List<KchyqkGridlbVO> defaultKchyqkBqsList = getDefaultKchyqkBqs();
                // 获取默认--库存耗用情况累计数
                final List<KchyqkGridlbVO> defaultKchyqkLjsList = getDefaultKchyqkLjs();
                if (Objects.isNull(snyrlyscwghykcydtjb)) {
                    final Snyrlyscwghykcydtjb snyrlyscwghykcydtjbNew = new Snyrlyscwghykcydtjb();
                    snyrlyscwghykcydtjbNew.setSbXfsFbSnyrlyydtjb(defaultCpqkList);
                    snyrlyscwghykcydtjbNew.setSbXfsFbSnyrlyydtjbDdzgjh(defaultDdzgList);
                    snyrlyscwghykcydtjbNew.setSbXfsFbSnyrlyydtjbKchyqk(defaultKchyqkBqsList);
                    snyrlyscwghykcydtjbNew.getSbXfsFbSnyrlyydtjbKchyqk().addAll(defaultKchyqkLjsList);
                    ytxx.setSbXfsFbSnyrlyscwghykcydtjb(snyrlyscwghykcydtjbNew);
                    errorMesg.append("《石脑油、燃料油生产、外购、耗用、库存月度统计表》节点：sbXfsFbSnyrlyscwghykcydtjb，不允许为空;");
                } else {
                    if (CollectionUtils.isEmpty(snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjb())) {
                        snyrlyscwghykcydtjb.setSbXfsFbSnyrlyydtjb(defaultCpqkList);
                    } else {
                        snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjb().forEach(item -> {
                            if (!Objects.isNull(item.getEwbhxh())) {
                                final String ewbhxh = String.valueOf(item.getEwbhxh());
                                final XfsYdtjCpqkEnum enumByEwbhXh = XfsYdtjCpqkEnum.getEnumByEwbhXh(Integer.parseInt(ewbhxh));
                                if(enumByEwbhXh != null){
                                    item.setEwbhlbm(enumByEwbhXh.getEwbhlbm());
                                }
                            }
                        });
                    }

                    if (CollectionUtils.isEmpty(snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjbDdzgjh())) {
                        snyrlyscwghykcydtjb.setSbXfsFbSnyrlyydtjbDdzgjh(defaultDdzgList);
                    } else {
                        snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjbDdzgjh().forEach(item -> {
                            if (!Objects.isNull(item.getEwbhxh())) {
                                final String ewbhxh = String.valueOf(item.getEwbhxh());
                                final XfsYdtjDdzgEnum enumByEwbhXh = XfsYdtjDdzgEnum.getEnumByEwbhXh(Integer.parseInt(ewbhxh));
                                if(enumByEwbhXh != null){
                                    item.setEwbhlbm(enumByEwbhXh.getEwbhlbm());
                                    item.setXmmc(enumByEwbhXh.getXmmc());
                                }
                            }
                        });
                    }

                    if (CollectionUtils.isEmpty(snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjbKchyqk())) {
                        snyrlyscwghykcydtjb.setSbXfsFbSnyrlyydtjbKchyqk(defaultKchyqkBqsList);
                        errorMesg.append("《石脑油、燃料油生产、外购、耗用、库存月度统计表》-库存耗用情况节点：sbXfsFbSnyrlyydtjbKchyqk，不允许为空;");
                    } else {
                        // 如果只下发了4个，只会下发累计数，所以补充本期数即可
                        if (snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjbKchyqk().size() > 0 && snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjbKchyqk().size() < 5) {
                            snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjbKchyqk().addAll(defaultKchyqkBqsList);
                        }

                        // 默认给二维表行列编码和栏名称等赋值
                        snyrlyscwghykcydtjb.getSbXfsFbSnyrlyydtjbKchyqk().forEach(item -> {
                            if ((StringUtils.isEmpty(item.getEwbhlbm()) || StringUtils.isEmpty(item.getLmc())) && !Objects.isNull(item.getEwblxh())) {
                                if (XfsYdtjKchyEnum.RLY_GC_LJS.getEwbhlbm().equals(item.getLmc())) {
                                    item.setEwblxh(XfsYdtjKchyEnum.RLY_GC_LJS.getEwblxh());
                                } else if (XfsYdtjKchyEnum.RLY_JK_LJS.getEwbhlbm().equals(item.getLmc())) {
                                    item.setEwblxh(XfsYdtjKchyEnum.RLY_JK_LJS.getEwblxh());
                                } else if (XfsYdtjKchyEnum.SNY_GC_LJS.getEwbhlbm().equals(item.getLmc())) {
                                    item.setEwblxh(XfsYdtjKchyEnum.SNY_GC_LJS.getEwblxh());
                                } else if (XfsYdtjKchyEnum.SNY_JK_LJS.getEwbhlbm().equals(item.getLmc())) {
                                    item.setEwblxh(XfsYdtjKchyEnum.SNY_JK_LJS.getEwblxh());
                                }
                                final String ewblxh = String.valueOf(item.getEwblxh());
                                final XfsYdtjKchyEnum enumByEwbhXh = XfsYdtjKchyEnum.getEnumByEwbhXh(Integer.parseInt(ewblxh));
                                if(enumByEwbhXh != null){
                                    item.setEwbhlbm(enumByEwbhXh.getEwbhlbm());
                                    item.setLmc(enumByEwbhXh.getLmc());
                                }
                            }
                        });
                    }
                }
                ytxx.setSbXfsFbSnyrlyscwghykcydtjb(snyrlyscwghykcydtjb);

                // 初始化乙烯、芳烃生产装置投入产出流量计统计表 节点
                if (GyUtils.isNull(ytxx.getSbXfsFbYxftsczztj())) {
                    final List<YxfjsczztrcclljtjbGridlbVO> sbXfsFbYxftsczztj = Lists.newArrayList();
                    sbXfsFbYxftsczztj.add(new YxfjsczztrcclljtjbGridlbVO());
                    ytxx.setSbXfsFbYxftsczztj(sbXfsFbYxftsczztj);
                }

                // 初始化使用企业外购石脑油、燃料油凭证明细表 节点
                final Syqywgsnyrlypzmxb syqywgsnyrlypzmxb = ytxx.getSbXfsFbWgpzmxb();
                if (GyUtils.isNull(syqywgsnyrlypzmxb) || GyUtils.isNull(syqywgsnyrlypzmxb.getSbXfsFbWgpzmx())) {
                    // 免税油品-汉字防伪和普通专用发票
                    SbXfsFbWgpzmxbWgmsypDTO sbXfsFbWgpzmxbWgmsyp = new SbXfsFbWgpzmxbWgmsypDTO();
                    sbXfsFbWgpzmxbWgmsyp.setSbXfsFbWgpzmxbWgmsypHzfwzyfp(getDefaultSyqywgsnyrlypzmxb("外购免税油品", "汉字防伪版增值税专用发票"));
                    sbXfsFbWgpzmxbWgmsyp.setSbXfsFbWgpzmxbWgmsypPtzyfp(getDefaultSyqywgsnyrlypzmxb("外购免税油品", "普通版增值税专用发票"));

                    // 含税油品-汉字防伪和普通专用发票
                    SbXfsFbWgpzmxbWghsypDTO sbXfsFbWgpzmxbWghsyp = new SbXfsFbWgpzmxbWghsypDTO();
                    sbXfsFbWgpzmxbWghsyp.setSbXfsFbWgpzmxbWghsypHzfwzyfp(getDefaultSyqywgsnyrlypzmxb("外购含税油品", "汉字防伪版增值税专用发票"));
                    sbXfsFbWgpzmxbWghsyp.setSbXfsFbWgpzmxbWghsypPtzyfp(getDefaultSyqywgsnyrlypzmxb("外购含税油品", "普通版增值税专用发票"));

                    // 初始化免税-含税发票节点，用于页面自增行绑定节点
                    syqywgsnyrlypzmxb.setSbXfsFbWgpzmxbWgmsyp(sbXfsFbWgpzmxbWgmsyp);
                    syqywgsnyrlypzmxb.setSbXfsFbWgpzmxbWghsyp(sbXfsFbWgpzmxbWghsyp);

                    List<SyqywgsnyrlypzmxbGridlbVO> sbXfsFbWgpzmx = Lists.newArrayList();
                    sbXfsFbWgpzmx.add(new SyqywgsnyrlypzmxbGridlbVO());
                    syqywgsnyrlypzmxb.setSbXfsFbWgpzmx(sbXfsFbWgpzmx);
                }
                // 外购含税油品 - 海关进口消费税专用缴款书 默认赋值
                if (Objects.isNull(syqywgsnyrlypzmxb) || GyUtils.isNull(syqywgsnyrlypzmxb.getSbXfsFbWgpzmxZb())) {
                    syqywgsnyrlypzmxb.setSbXfsFbWgpzmxZb(getDefaultSbXfsFbWgpzmxZb("外购含税油品", "海关进口消费税专用缴款书"));
                }
                ytxx.setSbXfsFbWgpzmxb(syqywgsnyrlypzmxb);

                // 初始化生产企业定点直供石脑油、燃料油开具普通版增值税专用发票 节点
                final SbXfsFbZyfpDTO sbXfsFbZyfpDTO = ytxx.getSbXfsFbZyfp();
                if (GyUtils.isNull(sbXfsFbZyfpDTO)) {
                    SbXfsFbZyfpDTO sbXfsFbZyfpDTONew = new SbXfsFbZyfpDTO();
                    sbXfsFbZyfpDTONew.setSbXfsFbZyfpmxList(getDefaultZyfp());
                    // 燃料油
                    SbXfsFbZyfpxxDTO rlySbXfsFbZyfpxxDTO = new SbXfsFbZyfpxxDTO();
                    rlySbXfsFbZyfpxxDTO.setSbXfsFbZyfpmx(getDefaultZyfpMx("燃料油"));
                    sbXfsFbZyfpDTONew.setRly(rlySbXfsFbZyfpxxDTO);
                    // 石脑油
                    SbXfsFbZyfpxxDTO snySbXfsFbZyfpxxDTO = new SbXfsFbZyfpxxDTO();
                    snySbXfsFbZyfpxxDTO.setSbXfsFbZyfpmx(getDefaultZyfpMx("石脑油"));
                    sbXfsFbZyfpDTONew.setSny(snySbXfsFbZyfpxxDTO);
                }
                ytxx.setSbXfsFbZyfp(sbXfsFbZyfpDTO);

                // 初始化生产企业销售含税石脑油、燃料油完税情况明细表 节点
                Scqyxshssnyrlywsqkmxb scqyxshssnyrlywsqkmxb = ytxx.getSbXfsFbScqyxshssnyrlywsqkmx();
                if (GyUtils.isNull(scqyxshssnyrlywsqkmxb)) {
                    scqyxshssnyrlywsqkmxb = new Scqyxshssnyrlywsqkmxb();
                    // 需要初始化字段节点，校验中心需要绑定
                    final List<ScqyxshssnyrlywsqkmxbGridlbVO> sbXfsFbXswsqkmx = Lists.newArrayList();
                    sbXfsFbXswsqkmx.add(new ScqyxshssnyrlywsqkmxbGridlbVO());
                    scqyxshssnyrlywsqkmxb.setSbXfsFbXswsqkmx(sbXfsFbXswsqkmx);
                    scqyxshssnyrlywsqkmxb.setSbXfsFbXswsqkmxZb(new XfsWspzhjFormVO());
                } else {
                    if (CollectionUtils.isEmpty(scqyxshssnyrlywsqkmxb.getSbXfsFbXswsqkmx())) {
                        // 需要初始化字段节点，校验中心需要绑定
                        final List<ScqyxshssnyrlywsqkmxbGridlbVO> sbXfsFbXswsqkmx = Lists.newArrayList();
                        sbXfsFbXswsqkmx.add(new ScqyxshssnyrlywsqkmxbGridlbVO());
                        scqyxshssnyrlywsqkmxb.setSbXfsFbXswsqkmx(sbXfsFbXswsqkmx);
                    }
                    if (Objects.isNull(scqyxshssnyrlywsqkmxb.getSbXfsFbXswsqkmxZb())) {
                        scqyxshssnyrlywsqkmxb.setSbXfsFbXswsqkmxZb(new XfsWspzhjFormVO());
                    }
                }
                ytxx.setSbXfsFbScqyxshssnyrlywsqkmx(scqyxshssnyrlywsqkmxb);
            } else {
                sbXfsFbBqzykcsejsbCpy.setSbXfsFbZykcCpyDksejkcjs(new ArrayList<>());
                sbXfsFbBqzykcsejsbCpy.setSbXfsFbZykcCpyBxrlyclyqk(new ArrayList<>());
                // 字段为空抛异常
                errorMesg.append("《本期准予扣除税额计算表》（成品油）节点：sbXfsFbBqzykcsejsbCpy，不允许为空;");
            }
        }

        //《本期委托加工收回情况报告表》	标签为准予扣除委托加工已纳税款的消费税企业的纳税人下发。
        if (!nsrBqList.contains(NsrbqEnum.ZYKCWTJGYNSKXFSQY.getCode())) {
            ytxx.setSbXfsFbBqwtjgshqkbgb(null);
        } else {
            sbbidList.add("004");
            //委托加工受托方代扣代缴税款情况
            List<StfdkdjskqkGridlbVO> wtfVO = Lists.newArrayList();
            wtfVO.add(new StfdkdjskqkGridlbVO());
            // 委托加工收回的应税消费品领用存情况
            List<WtjghsdysxfplyqkGridlbVO> shfVO = Lists.newArrayList();
            shfVO.add(new WtjghsdysxfplyqkGridlbVO());

            // 初始化本期委托加工情况报告表 节点
            Bqwtjgshqkbgb bqwtjgshqkbgb = ytxx.getSbXfsFbBqwtjgshqkbgb();
            if (Objects.isNull(bqwtjgshqkbgb)) {
                bqwtjgshqkbgb = new Bqwtjgshqkbgb();
                bqwtjgshqkbgb.setSbXfsFbWtjgshStfdkdjskqk(wtfVO);
                bqwtjgshqkbgb.setSbXfsFbWtjgshYsxfplyqk(shfVO);
                errorMesg.append("《本期委托加工情况报告表》节点：sbXfsFbSnyrlyydtjbKchyqk，不允许为空;");
            } else {
                if (CollectionUtils.isEmpty(bqwtjgshqkbgb.getSbXfsFbWtjgshStfdkdjskqk())) {
                    bqwtjgshqkbgb.setSbXfsFbWtjgshStfdkdjskqk(wtfVO);
                    errorMesg.append("《本期委托加工情况报告表》-委托加工受托方代扣代缴税款情况节点：sbXfsFbWtjgshStfdkdjskqk，不允许为空;");
                }
                if (CollectionUtils.isEmpty(bqwtjgshqkbgb.getSbXfsFbWtjgshYsxfplyqk())) {
                    bqwtjgshqkbgb.setSbXfsFbWtjgshYsxfplyqk(shfVO);
                    errorMesg.append("《本期委托加工情况报告表》-委托加工收回的应税消费品领用存情况节点：sbXfsFbSnyrlyydtjbKchyqk，不允许为空;");
                }
            }
            ytxx.setSbXfsFbBqwtjgshqkbgb(bqwtjgshqkbgb);
        }
        //《本期减（免）税额明细表》	标签为消费税减免企业的纳税人下发
        if (!nsrBqList.contains(NsrbqEnum.XFSJMQY.getCode())) {
            ytxx.setSbXfsFbBqjmsemxb(null);
        } else {
            sbbidList.add("005");
            // 初始化《本期减（免）税额明细表》 节点
            final List<BqjmsemxbGridlbVO> gridlbVOS = ytxx.getSbXfsFbBqjmsemxb();
            if (CollectionUtils.isEmpty(gridlbVOS)) {
                ytxx.setSbXfsFbBqjmsemxb(getDefaultBqjmsemxb(Lists.newArrayList()));
                errorMesg.append("《本期减（免）税额明细表》-节点：sbXfsFbBqjmsemxb，不允许为空;");
            } else {
                boolean hasDefaultZspm = Boolean.FALSE;
                for (BqjmsemxbGridlbVO bqjmsemxbGridlbVO : gridlbVOS) {
                    if (StringUtils.equals("CKMS00000", bqjmsemxbGridlbVO.getZspmDm())) {
                        hasDefaultZspm = Boolean.TRUE;
                        break;
                    }
                }
                if (!hasDefaultZspm) {
                    // 第一位是zspmDm一定是CKMS00000
                    final BqjmsemxbGridlbVO gridlbVO = new BqjmsemxbGridlbVO();
                    gridlbVO.setZspmDm("CKMS00000");
                    gridlbVO.setZspmMc("出口免税");
                    gridlbVO.setEwbhxh(1L);
                    gridlbVO.setSsjmxzMc("");
                    gridlbVOS.add(0, gridlbVO);
                } else {
                    // 将出口退税"CKMS00000"放到第一位，且ewbhxh按顺序重置，否则前端会错乱
                    final List<BqjmsemxbGridlbVO> ckms00000 = gridlbVOS.stream().filter(i -> StringUtils.equals("CKMS00000", i.getZspmDm())).collect(Collectors.toList());
                    gridlbVOS.removeIf(i -> StringUtils.equals("CKMS00000", i.getZspmDm()));
                    gridlbVOS.add(0, ckms00000.get(0));
                    gridlbVOS.stream().forEach(i -> {
                        if (StringUtils.equals("CKMS00000", i.getZspmDm())) {
                            i.setEwbhxh(1L);
                            i.setZspmMc("出口免税");
                            i.setSsjmxzMc("");
                        }
                    });
                }
                // 设置二维表行序号
                long ewbhxh = 0;
                for (BqjmsemxbGridlbVO gridlbVO : gridlbVOS) {
                    ewbhxh = ewbhxh + 1;
                    gridlbVO.setEwbhxh(ewbhxh);
                }
                gridlbVOS.stream().forEach(i -> {
                    i.setZspmMc(CacheUtils.dm2mc(DM_GY_ZSPM, i.getZspmDm()));
                    i.setSsjmxzMc(CacheUtils.dm2mc(DM_GY_SSJMXZ, i.getSsjmxzDm()));
                });
                ytxx.setSbXfsFbBqjmsemxb(gridlbVOS);
            }
        }

        //《卷烟批发企业月份销售明细清单》（卷烟批发环节消费税纳税人适用）	标签为卷烟批发企业的纳税人下发
        if (!nsrBqList.contains(NsrbqEnum.JYPFQY.getCode())) {
            ytxx.setSbXfsFbJypfqyyfxsmx(null);
        } else {
            sbbidList.add("009");
            // 初始化《卷烟批发企业月份销售明细清单》 节点
            if (CollectionUtils.isEmpty(ytxx.getSbXfsFbJypfqyyfxsmx())) {
                final List<JypfqyyfxsmxqdGridlbVO> jypfqyList = Lists.newArrayList();
                final JypfqyyfxsmxqdGridlbVO jypfVO = new JypfqyyfxsmxqdGridlbVO();
                jypfqyList.add(jypfVO);
                ytxx.setSbXfsFbJypfqyyfxsmx(jypfqyList);
            }
        }

        if (!nsrBqList.contains(NsrbqEnum.XFSFTJNZJGQY.getCode())) {
            // 《汇总纳税企业消费税分配表》	标签为消费税分摊缴纳总机构企业的纳税人下发。
            ytxx.setSbXfsFbHznsqyfpb(null);
        } else {
            sbbidList.add("007");
            // 消费税附加税费计算表-分支机构信息
            if (CollectionUtils.isEmpty(ytxx.getSbFjsf().getSbFjsfHznsfjsffpb())) {
                final List<SbFjsfHznsfjsffpbVO> fjsfHznsfjsffpbVOS = new ArrayList<>();
                final SbFjsfQtxxDTO sbFjsfQtxx = new SbFjsfQtxxDTO();
                final List<FjsxxGridlbVO> sbFjsfMx = new ArrayList<>();
                final XfsFjs xfsFjs = new XfsFjs();
                xfsFjs.setSbFjsfHznsfjsffpb(fjsfHznsfjsffpbVOS);
                xfsFjs.setSbFjsfMx(sbFjsfMx);
                xfsFjs.setSbFjsfQtxx(sbFjsfQtxx);
                ytxx.setSbFjsf(xfsFjs);
            } else {
                final List<SbFjsfHznsfjsffpbVO> sbFjsfHznsfjsffpbVOS = ytxx.getSbFjsf().getSbFjsfHznsfjsffpb();
                sbFjsfHznsfjsffpbVOS.forEach(e -> {
                    e.setZsxmMc(CacheUtils.dm2mc(DM_GY_ZSXM, e.getZsxmDm()));
                    e.setZspmMc(CacheUtils.dm2mc(DM_GY_ZSPM, e.getZspmDm()));
                });
                ytxx.getSbFjsf().setSbFjsfHznsfjsffpb(sbFjsfHznsfjsffpbVOS);
            }
//            // 汇总纳税企业消费税分配-分支机构信息
//            final List<FzjgGridlbVO> fzjgGridlbVOs = ytxx.getSbXfsFbHznsqyfpb().getSbXfsFbHznsqyxfsfpb();
//            // 汇总纳税企业消费税分配-总机构信息
//            final ZjgForm zjgForm = ytxx.getSbXfsFbHznsqyfpb().getSbXfsFbHznsqyxfsfpbZjg();
//            final Hznsqyxfsfp hznsqyxfsfp = new Hznsqyxfsfp();
//            hznsqyxfsfp.setSbXfsFbHznsqyxfsfpb(fzjgGridlbVOs);
//            hznsqyxfsfp.setSbXfsFbHznsqyxfsfpbZjg(zjgForm);
//            ytxx.setSbXfsFbHznsqyfpb(hznsqyxfsfp);
        }

        if (!nsrBqList.contains(NsrbqEnum.JYSCQY.getCode())) {
            //《卷烟生产企业合作生产卷烟消费税情况报告表（卷烟生产环节消费税纳税人适用）》	标签为卷烟生产企业的纳税人下发。
            ytxx.setSbXfsFbHzscjyxfsqkbgb(null);
        } else {
            sbbidList.add("008");
            // TODO : 金三保存接口目前没有这个节点，需要金三那边提改造 目前搁置状态
            // 初始化《卷烟生产企业合作生产卷烟消费税情况报告表（卷烟生产环节消费税纳税人适用）》 节点
            if (CollectionUtils.isEmpty(ytxx.getSbXfsFbHzscjyxfsqkbgb())) {
                List<HzscjyxfsqkbgbGridlbVO> sbXfsFbHzscjyxfsqkbgb = Lists.newArrayList();
                sbXfsFbHzscjyxfsqkbgb.add(new HzscjyxfsqkbgbGridlbVO());
                ytxx.setSbXfsFbHzscjyxfsqkbgb(sbXfsFbHzscjyxfsqkbgb);
            }
        }

        // 判断是否下发高档手表、贵重首饰及珠宝玉石销售明细表
        if (!nsrBqList.contains(NsrbqEnum.XSGDSBGZSSZBYSXFSQY.getCode())) {
            //《高档手表、贵重首饰及珠宝玉石销售明细表》	标签为销售高档手表、贵重首饰及珠宝玉石的消费税企业的纳税人下发。
            ytxx.setSbXfsFbGdsbgzsszbxsmxb(null);
        } else {
            sbbidList.add("011");
            //初始化《高档手表、贵重首饰及珠宝玉石销售明细表》节点
            final List<GdsbgzssxsmxGridlbVO> sbXfsFbGdsbgzsszbxsmxb = ytxx.getSbXfsFbGdsbgzsszbxsmxb();
            if (CollectionUtils.isEmpty(sbXfsFbGdsbgzsszbxsmxb)) {
                errorMesg.append("《高档手表、贵重首饰及珠宝玉石销售明细表》-节点：sbXfsFbGdsbgzsszbxsmxb，不允许为空;");
            } else {
                for (GdsbgzssxsmxGridlbVO item : sbXfsFbGdsbgzsszbxsmxb) {
                    final String ewbhxh = String.valueOf(item.getEwbhxh());
                    item.setEwbhlbm(XfsGdsbDmEnum.getEnumByEwbhXh(Integer.parseInt(ewbhxh)).getEwbhlbm());
                    // TODO 存疑 税智撑口径为判断是否做过备案且被冻结，需要单独处理期初逻辑。 未备案应该会返回0
//                    if (StringUtils.equals(dqywbw.getGdsbgzsszbyssfdj(), YesOrNoEnum.Y.getCode())) {
//                    item.setBdqckcsl(BigDecimal.ZERO);
//                    item.setBdqckcje(BigDecimal.ZERO);
//                    }
                }
            }
            ytxx.setSbXfsFbGdsbgzsszbxsmxb(sbXfsFbGdsbgzsszbxsmxb);
        }
//        《本期准予扣除税额计算表》	标签为准予扣除的消费税企业或准予扣除委托加工已纳税款的消费税企业
//                且标签不为成品油消费税企业的纳税人下发
        if (nsrBqList.contains(NsrbqEnum.CPYXFSQY.getCode()) || (!nsrBqList.contains(NsrbqEnum.ZYKCWTJGYNSKXFSQY.getCode())
                && !nsrBqList.contains(NsrbqEnum.ZYKCXFSQY.getCode()))) {
            ytxx.setSbXfsFbBqzykcsejsb(null);
        } else {
            sbbidList.add("002");
            //初始化《本期准予扣除税额计算表》节点
            final List<BqzykcsejsbGridlbVO> bqzykcsejsbList = ytxx.getSbXfsFbBqzykcsejsb();
            if (CollectionUtils.isEmpty(bqzykcsejsbList)) {
                final List<BqzykcsejsbGridlbVO> bqzykcsejsbGridlbVOS = Lists.newArrayList();
                bqzykcsejsbGridlbVOS.add(new BqzykcsejsbGridlbVO());
                ytxx.setSbXfsFbBqzykcsejsb(bqzykcsejsbGridlbVOS);
                errorMesg.append("《本期准予扣除税额计算表》-节点：sbXfsFbBqzykcsejsb，不允许为空;");
            }
        }
        itemVO.setSbbidList(sbbidList);
        //《消费税及附加税费申报表》
        final Xfssbzb sbXfsFjsfsbb = ytxx.getSbXfsFjsfsbb();
        if (Objects.isNull(sbXfsFjsfsbb)) {
            ytxx.setSbXfsFjsfsbb(null);
            errorMesg.append("《消费税及附加税费申报表》节点：sbXfsFjsfsbb，不允许为空;");
        } else {
            List<XfssbxxGridlbVO> sbXfsZbList = new ArrayList<>();
            if (CollectionUtils.isEmpty(sbXfsFjsfsbb.getSbXfsZb())) {
                sbXfsFjsfsbb.setSbXfsZb(sbXfsZbList);
                errorMesg.append("《消费税及附加税费申报表》应税消费品节点：sbXfsZb，不允许为空;");
            } else {
                sbXfsZbList = sbXfsFjsfsbb.getSbXfsZb();
                for (XfssbxxGridlbVO vo : sbXfsZbList) {
                    final Map<String, Object> xfsslMap = CacheUtils.getTableData(CS_SB_XFSSLPZB, vo.getZspmDm());
                    if (xfsslMap != null) {
                        vo.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Double) xfsslMap.get("blsl")));
                        vo.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Double)xfsslMap.get("desl")));
                    }
                }
            }
            if (Objects.isNull(sbXfsFjsfsbb.getSbXfs())) {
                errorMesg.append("《消费税及附加税费申报表》节点：sbXfs，不允许为空;");
            }
            sbXfsFjsfsbb.setSbXfsZb(sbXfsZbList);
            ytxx.setSbXfsFjsfsbb(sbXfsFjsfsbb);
        }

        // 消费税附加税费计算表
        XfsFjs xfsFjs = ytxx.getSbFjsf();
        if (Objects.isNull(xfsFjs)) {
            ytxx.setSbFjsf(null);
            errorMesg.append("《消费税附加税费计算表》节点：sbFjsf，不允许为空;");
        }

        log.error("【消费税】税支撑异常信息，是否需要提示：{}， 获取到的节点异常信息：{}, ", LzsXtcsBmConstant.LZS_XFS_SZC_YCXXTS_SWITCH, errorMesg);
        if (StringUtils.isNotEmpty(errorMesg.toString()) && StringUtils.equals("Y", LzsXtcsBmConstant.LZS_XFS_SZC_YCXXTS_SWITCH)) {
            throw exception(INTERNAL_SERVER_ERROR, errorMesg.toString());
        }
        itemVO.setYtxx(ytxx);
    }


    /**
     * 获取是否为汇总纳税
     *
     * @param djxh djxh
     * @return {@link boolean}
     */
    private boolean getIsHzns(String djxh) {
        QueryHznsbaxxReqDTO queryHznsbaxxReqDTO = new QueryHznsbaxxReqDTO();
        queryHznsbaxxReqDTO.setDjxh(BigDecimalUtils.formatString2BigDecimal(djxh));
        QueryHznsbaxxRespDTO queryHznsbaxxRespDTO = hznsqyrdsqMxbService.queryHznsbaxx(queryHznsbaxxReqDTO);
        if (GyUtils.isNull(queryHznsbaxxRespDTO)) {
            return false;
        }
        return true;
    }


    /**
     * @name 设置辅助项信息
     * @time 2024/4/28 15:11
     * @param: qcxxResponseDTO
     * @param: itemVO
     * @param: qyjbxxmxResVO
     * <AUTHOR>
     **/
    private void setFzxx(XfsLqptQcsResponseDTO qcxxResponseDTO, XfsQcxxItemVO itemVO, ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO) {
        final XfsQcFzxxVO fzxx = qcxxResponseDTO.getFzxx();
        if (ObjectUtil.isNotNull(fzxx)) {
            final List<NsrbqxxVO> nsrbqxx = qyjbxxmxResVO.getNsrbqxx();
            final List<String> nsrBqList = nsrbqxx.stream().map(NsrbqxxVO::getBqmc).collect(Collectors.toList());
            final XfsSbBcSbbDTO ytxx = qcxxResponseDTO.getLsxx();
            final Date skssqq = itemVO.getSbsx().getSkssqq();
            final Date skssqz = itemVO.getSbsx().getSkssqz();
            final String yzpzzlDm = itemVO.getSbsx().getYzpzzlDm();
            final String djxh = itemVO.getNsrxx().getDjxh();
            final String zgswjDm = itemVO.getNsrxx().getZgswjDm();
            // 申报表
            final List<String> sbbidList = itemVO.getSbbidList();
            List<SbSbbItem> sbbList = new ArrayList<>();
            List<CsZnsbLzsSbbpzDO> sbbpzList = CacheUtils.getTableData(CS_ZNSB_LZS_SBBPZ,
                    CsZnsbLzsSbbpzDO.class,
                    csDo -> YzpzzlEnum.XFS.getDm().equals(csDo.getYzpzzlDm())
                            && DateUtil.compareTwoDate(itemVO.getSbsx().getSkssqq(), csDo.getYxqq()) >= 0
                            && DateUtil.compareTwoDate(itemVO.getSbsx().getSkssqz(), csDo.getYxqz()) <= 0);
            sbbpzList = sbbpzList.stream().sorted(Comparator.comparing(CsZnsbLzsSbbpzDO::getXh)).collect(Collectors.toList());
            for (CsZnsbLzsSbbpzDO sbbpzDO : sbbpzList) {
                if (GyUtils.isNull(sbbpzDO.getBbjdmc())) {
                    continue;
                }
                SbSbbItem sbSbbItem = BeanUtil.toBean(sbbpzDO, SbSbbItem.class);
                sbSbbItem.setBslxDm(sbbpzDO.getBslxbm());
                if ("1".equals(sbbpzDO.getXflxbm()) || sbbidList.contains(sbbpzDO.getBbid())) {
                    sbbList.add(sbSbbItem);
                }
            }
            fzxx.setSbbList(sbbList);
            // 台账信息
            final XfsSbQcTzxxVO tzxx = new XfsSbQcTzxxVO();
            tzxx.setBqyjse(itemVO.getYtxx().getSbXfsFjsfsbb().getSbXfs().getBqyjse());
            fzxx.setTzxx(tzxx);
            // 参数信息
            XfsSbQcCsxxDTO csxx = new XfsSbQcCsxxDTO();
            csxx.getXfs().setZzsxse(getZzsxse(skssqq, skssqz, yzpzzlDm, djxh));
            csxx.getXfs().setHdjsyj(BigDecimal.ZERO);
            fzxx.setCsxx(csxx);
            // 标签信息
            final XfsBqxxDTO bqxx = new XfsBqxxDTO();
            bqxx.setCpyxfsqy(nsrBqList.contains(NsrbqEnum.CPYXFSQY.getCode()) ? "Y" : "N");
            bqxx.setXsgdsbgzsszbysxfsqy(nsrBqList.contains(NsrbqEnum.XSGDSBGZSSZBYSXFSQY.getCode()) ? "Y" : "N");
            bqxx.setJypfqy(nsrBqList.contains(NsrbqEnum.JYPFQY.getCode()) ? "Y" : "N");
            bqxx.setJyscqy(nsrBqList.contains(NsrbqEnum.JYSCQY.getCode()) ? "Y" : "N");
            bqxx.setPflsjyssxxsqy(nsrBqList.contains(NsrbqEnum.PFLSJYSSXXSQY.getCode()) ? "Y" : "N");
            bqxx.setQytysb("");
            bqxx.setQyzdqtry("");
            bqxx.setSfzzsrhsb("N");
            bqxx.setSfzzssb("N");
            bqxx.setXfsftjnzjgqy(nsrBqList.contains(NsrbqEnum.XFSFTJNZJGQY.getCode()) ? "Y" : "N");
            bqxx.setXfsjmqy(nsrBqList.contains(NsrbqEnum.XFSJMQY.getCode()) ? "Y" : "N");
//            bqxx.setXfskdqssfxskqy(qcxxResponseDTO.getKdqsssrfpBz());
            bqxx.setZykcwtjgynskxfsqy(nsrBqList.contains(NsrbqEnum.ZYKCWTJGYNSKXFSQY.getCode()) ? "Y" : "N");
            bqxx.setZykcxfsqy(nsrBqList.contains(NsrbqEnum.ZYKCXFSQY.getCode()) ? "Y" : "N");
            bqxx.setGdsbgzsszbyssfdj(nsrBqList.contains(NsrbqEnum.GDSBGZSSZBYSSFDJ.getCode()) ? "Y" : "W");
            fzxx.setBqxx(bqxx);
            // 扩展信息
            final XfsSbQcKzxxVO kzxx = new XfsSbQcKzxxVO();
            // 原小微企业六税两费减免性质名称校验中心写死，现后端返回
            kzxx.setXwqyLslfJmxzList(phjmService.getXwqyLslfJmxz(
                    Arrays.asList(ZsxmDmEnum.CSWHJSS.getCode(), ZsxmDmEnum.JYFFJ.getCode(), ZsxmDmEnum.DFJYFJ.getCode()),
                    Arrays.asList(JzzcsyztEnum.XXWLQY.getCode(), JzzcsyztEnum.GTGSH.getCode()),
                    cn.hutool.core.date.DateUtil.format(skssqq, NORM_DATE_FORMAT),
                    cn.hutool.core.date.DateUtil.format(skssqz, NORM_DATE_FORMAT),
                    zgswjDm));
            fzxx.setKzxx(kzxx);
        }
        itemVO.setFzxx(fzxx);
    }

    /**
     * @name 获取增值税销售额
     * @time 2024/5/6 19:26
     * @param: skssqq
     * @param: skssqz
     * @param: yzpzzlDm
     * @param: djxh
     * @return: java.math.BigDecimal
     * <AUTHOR>
     **/
    private BigDecimal getZzsxse(Date skssqq, Date skssqz, String yzpzzlDm, String djxh) {
        final LambdaQueryWrapper<SbSbxxDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SbSbxxDO::getDjxh, djxh)
                .eq(SbSbxxDO::getZfbz1, "N")
                .eq(SbSbxxDO::getYzpzzlDm, yzpzzlDm);
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(skssqq);
        calendar1.add(Calendar.DAY_OF_MONTH, -1);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(skssqz);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        wrapper.ge(SbSbxxDO::getSkssqq, calendar1.getTime());
        wrapper.le(SbSbxxDO::getSkssqz, calendar.getTime());
        final SbSbxxDO sbSbxxDO = sbxxDOMapper.selectOne(wrapper);
        if (BeanUtil.isNotEmpty(sbSbxxDO) && sbSbxxDO.getJsyj() != null) {
            return sbSbxxDO.getJsyj();
        }
        return BigDecimal.ZERO;
    }


    /**
     * 获取默认--石脑油、燃料油生产、外购、耗用、库存月度统计表 -- 产品情况
     *
     * @return cpqkGridlbVOS
     */
    private List<CpqkGridlbVO> getDefaultSnyrlyCpqk() {
        List<CpqkGridlbVO> cpqkGridlbVOS = Lists.newArrayList();
        for (int i = 1; i <= 6; i++) {
            CpqkGridlbVO cpqkGridlbVO = new CpqkGridlbVO();
            cpqkGridlbVO.setEwbhxh(XfsYdtjCpqkEnum.getEnumByEwbhXh(i).getEwbhxh());
            cpqkGridlbVO.setEwbhlbm(XfsYdtjCpqkEnum.getEnumByEwbhXh(i).getEwbhlbm());
            cpqkGridlbVO.setCpmc1(XfsYdtjCpqkEnum.getEnumByEwbhXh(i).getCpmc1());
            cpqkGridlbVO.setCl(BigDecimal.ZERO);
            cpqkGridlbVO.setBl(BigDecimal.ZERO);
            cpqkGridlbVOS.add(cpqkGridlbVO);
        }
        return cpqkGridlbVOS;
    }

    /**
     * 获取默认--石脑油、燃料油生产、外购、耗用、库存月度统计表 -- 定点直供计划情况
     *
     * @return ddzgjhqkGridlbVOS
     */
    private List<DdzgjhqkGridlbVO> getDefaultDdzgjhqk() {
        List<DdzgjhqkGridlbVO> ddzgjhqkGridlbVOS = Lists.newArrayList();
        for (int i = 1; i <= 3; i++) {
            DdzgjhqkGridlbVO ddzgjhqkGridlbVO = new DdzgjhqkGridlbVO();
            ddzgjhqkGridlbVO.setEwbhxh(i);
            ddzgjhqkGridlbVO.setEwbhlbm(XfsYdtjDdzgEnum.getEnumByEwbhXh(i).getEwbhlbm());
            ddzgjhqkGridlbVO.setXmmc(XfsYdtjDdzgEnum.getEnumByEwbhXh(i).getXmmc());
            ddzgjhqkGridlbVO.setGcRly(BigDecimal.ZERO);
            ddzgjhqkGridlbVO.setGcSny(BigDecimal.ZERO);
            ddzgjhqkGridlbVO.setQygrRly(BigDecimal.ZERO);
            ddzgjhqkGridlbVO.setQygrSny(BigDecimal.ZERO);
            ddzgjhqkGridlbVOS.add(ddzgjhqkGridlbVO);
        }
        return ddzgjhqkGridlbVOS;
    }

    /**
     * 获取默认--石脑油、燃料油生产、外购、耗用、库存月度统计表 -- 库存耗用情况本期数
     *
     * @return kchyqkGridlbVOS
     */
    private List<KchyqkGridlbVO> getDefaultKchyqkBqs() {
        List<KchyqkGridlbVO> kchyqkGridlbVOS = Lists.newArrayList();
        kchyqkGridlbVOS.add(createKchyVO(1, "SNY_GC_BQS", "石脑油_国产_本期数"));
        kchyqkGridlbVOS.add(createKchyVO(2, "SNY_JK_BQS", "石脑油_进口_本期数"));
        kchyqkGridlbVOS.add(createKchyVO(5, "RLY_GC_BQS", "燃料油_国产_本期数"));
        kchyqkGridlbVOS.add(createKchyVO(6, "RLY_JK_BQS", "燃料油_进口_本期数"));
        return kchyqkGridlbVOS;
    }

    private KchyqkGridlbVO createKchyVO(int ewblxh, String ewbhlbm, String lmc) {
        KchyqkGridlbVO kchyqkGridlbVO = new KchyqkGridlbVO();
        kchyqkGridlbVO.setEwblxh(ewblxh);
        kchyqkGridlbVO.setEwbhlbm(ewbhlbm);
        kchyqkGridlbVO.setLmc(lmc);
        kchyqkGridlbVO.setQckcypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setBqscypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setBqxshsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setBqzxddzgjhxsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setBqhzfwbzzszyfpypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setKjptbzzszyfpypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setScyxftlcphyypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setScyxlcphyypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setScftlcphyypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setScfyxftlcphyypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setQmkcypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setQckcypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setQckcmsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setQckchsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setDqwgypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setDqwgmsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setDqwghsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setBqxsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setBqxsmsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setBqxshsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setScfyxftlcphyypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setScmsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setSchsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setKyyscyxftypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setKyyscmsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setKyyschsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setSjscyxlcphyypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setSjscyxmsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setSjscyxhsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setSjscftlcphyypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setSjscftmsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setSjschsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setSqtshsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setQmkcypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setQmkcmsypsl(BigDecimal.ZERO);
        kchyqkGridlbVO.setQmkchsypsl(BigDecimal.ZERO);
        return kchyqkGridlbVO;
    }

    /**
     * 获取默认--石脑油、燃料油生产、外购、耗用、库存月度统计表 -- 库存耗用情况累计数
     *
     * @return kchyqkGridlbVOS
     */
    private List<KchyqkGridlbVO> getDefaultKchyqkLjs() {
        List<KchyqkGridlbVO> kchyqkGridlbVOS = Lists.newArrayList();
        kchyqkGridlbVOS.add(createKchyVO(3, "SNY_GC_LJS", "石脑油_国产_累计数"));
        kchyqkGridlbVOS.add(createKchyVO(4, "SNY_JK_LJS", "石脑油_进口_累计数"));
        kchyqkGridlbVOS.add(createKchyVO(7, "RLY_GC_LJS", "燃料油_国产_累计数"));
        kchyqkGridlbVOS.add(createKchyVO(8, "RLY_JK_LJS", "燃料油_进口_累计数"));
        return kchyqkGridlbVOS;
    }

    /**
     * 初始化使用企业外购石脑油、燃料油凭证明细表
     *
     * @param qywgyplx 外购免税油品类型
     * @param fpzlmc   发票种类名称
     * @return zyfpList
     */
    private List<SyqywgsnyrlypzmxbGridlbVO> getDefaultSyqywgsnyrlypzmxb(String qywgyplx, String fpzlmc) {
        List<SyqywgsnyrlypzmxbGridlbVO> zyfpList = Lists.newArrayList();
        SyqywgsnyrlypzmxbGridlbVO zyfp = new SyqywgsnyrlypzmxbGridlbVO();
        zyfp.setQywgyplx(qywgyplx);
        zyfp.setFpzlmc(fpzlmc);
        zyfpList.add(zyfp);
        return zyfpList;
    }

    /**
     * 外购含税油品-海关进口消费税专用缴款书 默认赋值
     *
     * @param qywgyplx 外购免税油品类型
     * @param fpzlmc   发票种类名称
     * @return jksxxGridlbVOS
     */
    private List<JksxxGridlbVO> getDefaultSbXfsFbWgpzmxZb(String qywgyplx, String fpzlmc) {
        List<JksxxGridlbVO> jksxxGridlbVOS = Lists.newArrayList();
        JksxxGridlbVO jksxxGridlbVO = new JksxxGridlbVO();
        jksxxGridlbVO.setQywgyplx(qywgyplx);
        jksxxGridlbVO.setFpzlmc(fpzlmc);
        jksxxGridlbVOS.add(jksxxGridlbVO);
        return jksxxGridlbVOS;
    }

    /**
     * 初始化生产企业定点直供石脑油、燃料油开具普通版增值税专用发票
     *
     * @return sbXfsFbZyfpmxList
     */
    private List<KjptbzzszyfpmxbGridlbVO> getDefaultZyfp() {
        List<KjptbzzszyfpmxbGridlbVO> sbXfsFbZyfpmxList = Lists.newArrayList();
        sbXfsFbZyfpmxList.addAll(getDefaultZyfpMx("石脑油"));
        sbXfsFbZyfpmxList.addAll(getDefaultZyfpMx("燃料油"));
        return sbXfsFbZyfpmxList;
    }

    /**
     * 组织石脑油、燃料油的默认值
     *
     * @param hwmc hwmc
     * @return sbXfsFbZyfpmxList
     */
    private List<KjptbzzszyfpmxbGridlbVO> getDefaultZyfpMx(String hwmc) {
        List<KjptbzzszyfpmxbGridlbVO> sbXfsFbZyfpmxList = Lists.newArrayList();
        KjptbzzszyfpmxbGridlbVO zyfpmx = new KjptbzzszyfpmxbGridlbVO();
        zyfpmx.setHwmc(hwmc);
        sbXfsFbZyfpmxList.add(zyfpmx);
        return sbXfsFbZyfpmxList;
    }

    /**
     * 获取默认--《消费税本期减（免）税额明细表》节点
     *
     * @param bqjmseList bqjmseList
     * @return bqjmseList
     */
    private List<BqjmsemxbGridlbVO> getDefaultBqjmsemxb(List<BqjmsemxbGridlbVO> bqjmseList) {
        // 第一位是zspmDm一定是CKMS00000
        BqjmsemxbGridlbVO gridlbVO = new BqjmsemxbGridlbVO();
        gridlbVO.setZspmDm("CKMS00000");
        gridlbVO.setEwbhxh(1);
        bqjmseList.add(0, gridlbVO);

        // 初始化自增行节点，且用于页面绑值使用
        gridlbVO = new BqjmsemxbGridlbVO();
        gridlbVO.setZspmDm(StringUtils.EMPTY);
        gridlbVO.setEwbhxh(2);
        bqjmseList.add(gridlbVO);
        return bqjmseList;
    }

    private void handleYtxx(ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO, XfsSbBcSbbDTO ytxx, String djxh, String skssqq,
                            String skssqz, String zgswjgDm) {
        // 1、空判断处理
        List<XfssbxxGridlbVO> sbXfsZb = ytxx.getSbXfsFjsfsbb().getSbXfsZb();
        if (CollectionUtils.isEmpty(sbXfsZb)) {
            return;
        }
        // 获取税费种认定信息
        final List<SfzrdmxxxVO> sfzrdmxxx = qyjbxxmxResVO.getSfzrdmxxx();
        final boolean isContainsCswcy = isContainsCswcy(sfzrdmxxx);
        // 2、主表设置征收品名称
        setXfsZspmMc(sbXfsZb, isContainsCswcy);

        // 3、设置应税消费品列表下拉选（消费品目来源主表），用于前端在【本期准予扣除税额计算表】、【消费税附加税费计算表】等表中的下拉选使用
        setYsxfpLb(sbXfsZb, ytxx);

        // 4.获取增值税销售额（规范不可放在此处）
        BigDecimal zzsxse = this.queryZzsxse(djxh, skssqq, skssqz);
        ytxx.setZzsxse(zzsxse);

        // 5.设置附加税
        reBuildSbFjsf(ytxx, djxh, skssqq, skssqz, zgswjgDm, sfzrdmxxx);
    }

    /**
     * @name 税费种认定信息是否包含纯生物柴油
     * @time 2024/5/18 14:03
     * @param: sfzrdmxxx
     * @return: boolean
     * <AUTHOR>
     **/
    private boolean isContainsCswcy(List<SfzrdmxxxVO> sfzrdmxxx) {
        boolean result = false;
        for (SfzrdmxxxVO vo : sfzrdmxxx) {
            if ("101020611".equals(vo.getZspmDm())) {
                return true;
            }
        }
        return result;
    }


    /**
     * 设置申消费税主表的征收屏幕代码名称
     *
     * @param sbXfsZbList
     * @return
     */
    private void setXfsZspmMc(List<XfssbxxGridlbVO> sbXfsZbList, boolean isContainsCswcy) {
        final List<XfssbxxGridlbVO> sbXfsZbListNew = new ArrayList<>();
        final List<String> zspmDmList = new ArrayList<>();
        final List<Map<String, Object>> tableData = CacheUtils.getTableData(CS_SB_XFSSLPZB);
        final Map<String, Map<String, Object>> zspmMap = tableData.stream()
                .collect(Collectors.toMap(
                        m -> m.get("zspmDm").toString(),
                        m -> m, (oldValue, newValue) -> oldValue));
        for (XfssbxxGridlbVO sbxfsVo : sbXfsZbList) {
            // 1. 如果税费种认定中存在“纯生物柴油”时，消费税申报时应税消费税品目需报送“柴油”。
            if (isContainsCswcy && "101020611".equals(sbxfsVo.getZspmDm())) {
                final Map<String, Object> xfsslMap = zspmMap.get("*********");
                sbxfsVo.setZspmDm("*********");
                sbxfsVo.setZspmMc("柴油");
                sbxfsVo.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                sbxfsVo.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                sbxfsVo.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                sbXfsZbListNew.add(sbxfsVo);
                zspmDmList.add(sbxfsVo.getZspmDm());
            } else if ("101020610".equals(sbxfsVo.getZspmDm())) {
                //如果征收品目存在“101020610废矿物油”，那么检查本次申报信息中，是否同时存在下面5个征收品代码
                //********* 柴油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    final XfssbxxGridlbVO zbVO = new XfssbxxGridlbVO();
                    zbVO.setZspmDm("*********");
                    zbVO.setZspmMc(xfsslMap.get("zspmmc").toString());
                    zbVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    zbVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    zbVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    sbXfsZbListNew.add(zbVO);
                    zspmDmList.add(zbVO.getZspmDm());
                }
                //********* 石脑油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    final XfssbxxGridlbVO zbVO = new XfssbxxGridlbVO();
                    zbVO.setZspmDm("*********");
                    zbVO.setZspmMc(xfsslMap.get("zspmmc").toString());
                    zbVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    zbVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    zbVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    sbXfsZbListNew.add(zbVO);
                    zspmDmList.add(zbVO.getZspmDm());
                }
                //********* 润滑油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    final XfssbxxGridlbVO zbVO = new XfssbxxGridlbVO();
                    zbVO.setZspmDm("*********");
                    zbVO.setZspmMc(xfsslMap.get("zspmmc").toString());
                    zbVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    zbVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    zbVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    sbXfsZbListNew.add(zbVO);
                    zspmDmList.add(zbVO.getZspmDm());
                }
                //********* 燃料油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    final XfssbxxGridlbVO zbVO = new XfssbxxGridlbVO();
                    zbVO.setZspmDm("*********");
                    zbVO.setZspmMc(xfsslMap.get("zspmmc").toString());
                    zbVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    zbVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    zbVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    sbXfsZbListNew.add(zbVO);
                    zspmDmList.add(zbVO.getZspmDm());
                }
                //********* 汽油
                if (!zspmDmList.contains("*********")) {
                    final Map<String, Object> xfsslMap = zspmMap.get("*********");
                    final XfssbxxGridlbVO zbVO = new XfssbxxGridlbVO();
                    zbVO.setZspmDm("*********");
                    zbVO.setZspmMc(xfsslMap.get("zspmmc").toString());
                    zbVO.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                    zbVO.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                    zbVO.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                    sbXfsZbListNew.add(zbVO);
                    zspmDmList.add(zbVO.getZspmDm());
                }
            } else {
                final Map<String, Object> xfsslMap = zspmMap.get(sbxfsVo.getZspmDm());
                sbxfsVo.setZspmMc(CacheUtils.dm2mc(DM_GY_ZSPM, sbxfsVo.getZspmDm()));
                sbxfsVo.setBlsl(xfsslMap.get("blsl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("blsl")));
                sbxfsVo.setDesl(xfsslMap.get("desl") == null ? BigDecimal.ZERO : ((BigDecimal) xfsslMap.get("desl")));
                sbxfsVo.setJldwmc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
            }
        }
        sbXfsZbList = sbXfsZbListNew;
    }

    /**
     * 设置应税消费品列表消息
     * 各下拉选在每个表中带出的规则
     * 本期准予扣除税额计算表 ： 主表品目 + `SFKYDK` 或者 `SFFSWTJGYW` 为Y
     * 本期委托加工情况报告表 ： 主表品目 + `SFFSWTJGYW` 为Y
     * 本期减（免）税额明细表 ： 主表品目 + `SFFSMSSX` 为Y
     *
     * @param sbXfsZbList
     * @return
     */
    private void setYsxfpLb(List<XfssbxxGridlbVO> sbXfsZbList, XfsSbBcSbbDTO ytxx) {

        final List<YsxfpVO> ysxfpVoList = Lists.newArrayList();
        for (XfssbxxGridlbVO sbxfsVo : sbXfsZbList) {
            // 1、设置应税消费品列表相关字段
            final YsxfpVO ysxfpVO = new YsxfpVO();
            ysxfpVO.setZspmDm(sbxfsVo.getZspmDm());
            ysxfpVO.setYsxfpMc(sbxfsVo.getZspmMc());
            final Map<String, Object> xfsslMap = CacheUtils.getTableData(CS_SB_XFSSLPZB, sbxfsVo.getZspmDm());
            if (xfsslMap != null) {
                ysxfpVO.setBlsl(xfsslMap.get("blsl") == null ? 0.0d : (Double) xfsslMap.get("blsl"));
                ysxfpVO.setDesl(xfsslMap.get("desl") == null ? 0.0d : (Double) xfsslMap.get("desl"));
                ysxfpVO.setJldwDm(xfsslMap.get("jldwDm") == null ? "" : (String) xfsslMap.get("jldwDm"));
                ysxfpVO.setJldwMc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                ysxfpVO.setSfkydk(xfsslMap.get("sfkydk") == null ? "" : (String) xfsslMap.get("sfkydk"));
                ysxfpVO.setSffsmssx(xfsslMap.get("sffsmssx") == null ? "" : (String) xfsslMap.get("sffsmssx"));
                ysxfpVO.setSffswtjgyw(xfsslMap.get("sffswtjgyw") == null ? "" : (String) xfsslMap.get("sffswtjgyw"));
            }

            // 2、设置【商品和服务税收分类代码】， 只有成品油(柴油、石脑油、溶剂油、润滑油、燃料油、汽油)才下发，其余置空
            final Map<String, Object> spbmMap = CacheUtils.getTableData(CS_SB_XFSSPBMYSMDY, sbxfsVo.getZspmDm());
            if (spbmMap != null) {
                ysxfpVO.setSphfwssflbm(spbmMap.get("SPHFWSSFLBM") == null ? "" : String.valueOf(spbmMap.get("SPHFWSSFLBM")));
            }
            ysxfpVoList.add(ysxfpVO);
        }
        ytxx.setYsxfpLb(ysxfpVoList);
    }

    private void reSetYsxfpCpyLb(XfsSbBcSbbDTO ytxx, List<NsrbqxxVO> nsrbqxx) {
        final List<String> nsrBqList = nsrbqxx.stream().map(NsrbqxxVO::getBqmc).collect(Collectors.toList());
        // 非成品油企业，直接返回
        if (!nsrBqList.contains(NsrbqEnum.CPYXFSQY.getCode())) {
            ytxx.setYsxfpCpyLb(Lists.newArrayList());
            return;
        }
        List<YsxfpVO> ysxfpCpyLb = Lists.newArrayList();
        String[] zspmDmList = YSXFP_CPY_ZSPMDM.split(",");
        for (String zspmDm : zspmDmList) {
            YsxfpVO ysxfpVO = new YsxfpVO();
            ysxfpVO.setZspmDm(zspmDm);
            ysxfpVO.setYsxfpMc(CacheUtils.dm2mc(DM_GY_ZSPM, zspmDm));
            // 获取对应税率
            final Map<String, Object> xfsslMap = CacheUtils.getTableData(CS_SB_XFSSLPZB, zspmDm);
            if (xfsslMap != null) {
                ysxfpVO.setBlsl(xfsslMap.get("blsl") == null ? 0.0d : (Double) xfsslMap.get("blsl"));
                ysxfpVO.setDesl(xfsslMap.get("desl") == null ? 0.0d : (Double) xfsslMap.get("desl"));
                ysxfpVO.setJldwDm(xfsslMap.get("jldwDm") == null ? "" : (String) xfsslMap.get("jldwDm"));
                ysxfpVO.setJldwMc(xfsslMap.get("jldwmc") == null ? "" : (String) xfsslMap.get("jldwmc"));
                ysxfpVO.setSfkydk(xfsslMap.get("sfkydk") == null ? "" : (String) xfsslMap.get("sfkydk"));
                ysxfpVO.setSffsmssx(xfsslMap.get("sffsmssx") == null ? "" : (String) xfsslMap.get("sffsmssx"));
                ysxfpVO.setSffswtjgyw(xfsslMap.get("sffswtjgyw") == null ? "" : (String) xfsslMap.get("sffswtjgyw"));
            }
            // 2、设置【商品和服务税收分类代码】， 只有成品油(柴油、石脑油、溶剂油、润滑油、燃料油、汽油)才下发，其余置空
            final Map<String, Object> spbmMap = CacheUtils.getTableData(CS_SB_XFSSPBMYSMDY, zspmDm);
            if (spbmMap != null) {
                ysxfpVO.setSphfwssflbm(spbmMap.get("sphfwssflbm") == null ? "" : String.valueOf(spbmMap.get("sphfwssflbm")));
            }
            ysxfpCpyLb.add(ysxfpVO);
        }
        ytxx.setYsxfpCpyLb(ysxfpCpyLb);
    }

    private void reSetBqzykcCpyNode(XfsSbBcSbbDTO ytxx, List<NsrbqxxVO> nsrbqxx) {
        final List<String> nsrBqList = nsrbqxx.stream().map(NsrbqxxVO::getBqmc).collect(Collectors.toList());
        // 非成品油企业，直接返回
        if (!nsrBqList.contains(NsrbqEnum.CPYXFSQY.getCode())) {
            return;
        }
        Bqzykcsejsbcpy bqzykcsejsbCpy = ytxx.getSbXfsFbBqzykcsejsbCpy();
        if (bqzykcsejsbCpy == null || CollectionUtils.isEmpty(bqzykcsejsbCpy.getSbXfsFbZykcCpyDksejkcjs())) {
            log.debug("未查询到本期准予扣除成品油表数据，暂不初始化节点");
            return;
        }

        // 下发定额税率 + 二维表行列编码（更正） + 合计栏次
        List<DksejkcjsVO> dksejkcjsVOS = bqzykcsejsbCpy.getSbXfsFbZykcCpyDksejkcjs();
        BigDecimal sqkcslTotal = BigDecimal.ZERO;
        BigDecimal bqwgrkslTotal = BigDecimal.ZERO;
        BigDecimal wtjgshslTotal = BigDecimal.ZERO;
        BigDecimal bqzykcslTotal = BigDecimal.ZERO;
        BigDecimal bqzykcseTotal = BigDecimal.ZERO;
        BigDecimal bzykcslTotal = BigDecimal.ZERO;
        BigDecimal qmkcslTotal = BigDecimal.ZERO;
        for (DksejkcjsVO dksejkcjsVO : dksejkcjsVOS) {
            // 重新设置税率
            for (YsxfpVO ysxfpVO : ytxx.getYsxfpCpyLb()) {
                if (StringUtils.equals(dksejkcjsVO.getZspmDm(), ysxfpVO.getZspmDm())) {
                    dksejkcjsVO.setDesl(new BigDecimal(ysxfpVO.getDesl().toString()));
                    break;
                }
            }
            // 计算合计值
            if (!XfsCpyDmEnum.HJ.getEwbhlbm().equals(dksejkcjsVO.getEwbhlbm())) {
                sqkcslTotal = sqkcslTotal.add(GyUtils.isNull(dksejkcjsVO.getSqkcsl()) ? BigDecimal.ZERO : dksejkcjsVO.getSqkcsl());
                bqwgrkslTotal = bqwgrkslTotal.add(GyUtils.isNull(dksejkcjsVO.getBqwgsl()) ? BigDecimal.ZERO : dksejkcjsVO.getBqwgsl());
                wtjgshslTotal = wtjgshslTotal.add(GyUtils.isNull(dksejkcjsVO.getWtjghssl()) ? BigDecimal.ZERO : dksejkcjsVO.getWtjghssl());
                bqzykcslTotal = bqzykcslTotal.add(GyUtils.isNull(dksejkcjsVO.getBqzykcsl()) ? BigDecimal.ZERO : dksejkcjsVO.getBqzykcsl());
                bqzykcseTotal = bqzykcseTotal.add(GyUtils.isNull(dksejkcjsVO.getBqzykcse()) ? BigDecimal.ZERO : dksejkcjsVO.getBqzykcse());
                bzykcslTotal = bzykcslTotal.add(GyUtils.isNull(dksejkcjsVO.getBqlywyylxscbzykcsl()) ? BigDecimal.ZERO : dksejkcjsVO.getBqlywyylxscbzykcsl());
                qmkcslTotal = qmkcslTotal.add(GyUtils.isNull(dksejkcjsVO.getQmkcsl()) ? BigDecimal.ZERO : dksejkcjsVO.getQmkcsl());
            }
            // 重新设置二维表行列编码
            XfsCpyDmEnum cpyDmEnum = XfsCpyDmEnum.getZsxmDm(dksejkcjsVO.getZspmDm());
            if (cpyDmEnum != null) {
                dksejkcjsVO.setEwbhlbm(cpyDmEnum.getEwbhlbm());
            }
        }
        // 下发第六行合计栏次, 如果不存在就下发合计栏次
        boolean existHj = dksejkcjsVOS.stream().filter(i -> i.getEwbhlbm().equals("HJ")).findAny().isPresent();
        if (!existHj) {
            DksejkcjsVO dksejkcjsVO = new DksejkcjsVO();
            dksejkcjsVO.setEwbhlbm(XfsCpyDmEnum.HJ.getEwbhlbm());
            dksejkcjsVO.setEwbhxh(XfsCpyDmEnum.HJ.getEwbhxh());
            dksejkcjsVO.setSqkcsl(sqkcslTotal);
            dksejkcjsVO.setBqwgsl(bqwgrkslTotal);
            dksejkcjsVO.setWtjghssl(wtjgshslTotal);
            dksejkcjsVO.setBqzykcsl(bqzykcslTotal);
            dksejkcjsVO.setBqzykcse(bqzykcseTotal);
            dksejkcjsVO.setBqlywyylxscbzykcsl(bzykcslTotal);
            dksejkcjsVO.setQmkcsl(qmkcslTotal);
            dksejkcjsVOS.add(dksejkcjsVO);
        }
        bqzykcsejsbCpy.setSbXfsFbZykcCpyDksejkcjs(dksejkcjsVOS);
        ytxx.setSbXfsFbBqzykcsejsbCpy(bqzykcsejsbCpy);
    }

    /**
     * 查询增值税销售额
     *
     * @param djxh
     * @param skssqq
     * @param skssqz
     */
    private BigDecimal queryZzsxse(String djxh, String skssqq, String skssqz) {

        // TODO
        BigDecimal zzsxse = BigDecimal.ZERO;
        return zzsxse;
    }

    /**
     * 重置申报附加税节点，设置品名名称
     *
     * @param ytxx
     * @param djxh
     * @param skssqq
     * @param skssqz
     * @param swjgDm
     * @param sfzrdxxList
     */
    public void reBuildSbFjsf(XfsSbBcSbbDTO ytxx, String djxh, String skssqq, String skssqz, String swjgDm,
                              List<SfzrdmxxxVO> sfzrdxxList) {
        if (ytxx.getSbFjsf() != null && CollectionUtil.isNotEmpty(ytxx.getSbFjsf().getSbFjsfMx())) {
            Map<String, List<XwqyLslfJmxzDTO>> jmxzMap = phjmService.getXwqyLslfJmxz(
                            Arrays.asList(ZsxmDmEnum.CSWHJSS.getCode(), ZsxmDmEnum.JYFFJ.getCode(),
                                    ZsxmDmEnum.DFJYFJ.getCode()),
                            Arrays.asList(JzzcsyztEnum.XXWLQY.getCode(), JzzcsyztEnum.GTGSH.getCode()),
                            skssqq, skssqz, swjgDm)
                    .stream().collect(Collectors.groupingBy(XwqyLslfJmxzDTO::getSsjmxzDm));
            ytxx.getSbFjsf().getSbFjsfMx().forEach(sbFjsf -> {
                final Map<String, Object> zspmData = CacheUtils.getTableData(DM_GY_ZSPM, sbFjsf.getZspmDm());
                sbFjsf.setZspmMc(zspmData.get("zspmmc").toString());
                sbFjsf.setSl1(BigDecimalUtils.formatString2BigDecimal(zspmData.get("sl1").toString()));
                sbFjsf.setZsxmMc(CacheUtils.dm2mc(DM_GY_ZSXM, sbFjsf.getZsxmDm()));
                ZsxmDmEnum zsxmDmEnum = ZsxmDmEnum.getZsxmDm(sbFjsf.getZsxmDm());
                if (zsxmDmEnum != null) {
                    sbFjsf.setZsxmMc(zsxmDmEnum.getName());
                    if (ZsxmDmEnum.CSWHJSS.getCode().equals(zsxmDmEnum.getCode())) {
                        sbFjsf.setZspmMc("市区（消费税附征）");
                    } else if (ZsxmDmEnum.DFJYFJ.getCode().equals(zsxmDmEnum.getCode())) {
                        sbFjsf.setZspmMc("消费税地方教育附加");
                    } else if (ZsxmDmEnum.JYFFJ.getCode().equals(zsxmDmEnum.getCode())) {
                        sbFjsf.setZspmMc("消费税教育费附加");
                    }
                }
                String rdpzuuid = sfzrdxxList.stream().filter(sfz -> Objects.equals(sfz.getZsxmDm(), sbFjsf.getZsxmDm()))
                        .findFirst().map(SfzrdmxxxVO::getRdpzuuid).orElseGet(sbFjsf::getRdpzuuid);
                sbFjsf.setRdpzuuid(rdpzuuid);

                BigDecimal phjzbl = StringUtils.isNotEmpty(sbFjsf.getPhjmxzDm()) ? sbFjsf.getPhjzbl() : BigDecimal.ZERO;
                if (phjzbl.doubleValue() > 1.0) {
                    phjzbl = BigDecimal.valueOf(MathUtils.divide(phjzbl.doubleValue(), 100.0));
                }
                sbFjsf.setPhjzbl(phjzbl);

                // 临时设置普惠减免性质代码名称
                sbFjsf.setPhjmxzMc(Optional.ofNullable(jmxzMap.get(sbFjsf.getPhjmxzDm())).map(e -> e.get(0))
                        .map(XwqyLslfJmxzDTO::getSsjmxzmc).orElse(""));
            });
        }

        if (ytxx.getSbFjsf() != null && CollectionUtil.isNotEmpty(ytxx.getSbFjsf().getSbFjsfHznsfjsffpb())) {
            List<String> djxhList = ytxx.getSbFjsf().getSbFjsfHznsfjsffpb().stream().map(SbFjsfHznsfjsffpbVO::getFzjgdjxh)
                    .distinct().collect(Collectors.toList());
            List<String> zsxmList = ytxx.getSbFjsf().getSbFjsfHznsfjsffpb().stream().map(SbFjsfHznsfjsffpbVO::getZsxmDm)
                    .distinct().collect(Collectors.toList());
            ytxx.getSbFjsf().getSbFjsfHznsfjsffpb().forEach(fpb -> {
                String rdpzuuid = sfzrdxxList.stream()
                        .filter(sfz -> Objects.equals(sfz.getZsxmDm(), fpb.getZsxmDm()) && Objects.equals(sfz.getDjxh(),
                                fpb.getFzjgdjxh()))
                        .findFirst()
                        .map(SfzrdmxxxVO::getRdpzuuid)
                        .orElseGet(fpb::getRdpzuuid);

                fpb.setRdpzuuid(rdpzuuid);
            });
        }
    }
}
