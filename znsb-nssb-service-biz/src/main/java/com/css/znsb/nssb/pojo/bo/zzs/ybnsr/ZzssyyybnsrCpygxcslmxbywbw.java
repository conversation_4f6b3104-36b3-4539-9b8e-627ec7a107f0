package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《成品油购销存数量明细表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_cpygxcslmxbywbw", propOrder = { "zzssyyybnsrCpygxcslmxb" })
@Getter
@Setter
public class ZzssyyybnsrCpygxcslmxbywbw extends TaxDoc {
    /**
     * 成品油购销存数量明细表
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_cpygxcslmxb", required = true)
    @JSONField(name = "zzssyyybnsr_cpygxcslmxb")
    protected ZzssyyybnsrCpygxcslmxb zzssyyybnsrCpygxcslmxb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrCpygxcslmxb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrCpygxcslmxb {}
}