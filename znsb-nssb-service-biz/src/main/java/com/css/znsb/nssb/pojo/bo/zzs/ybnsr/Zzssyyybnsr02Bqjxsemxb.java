package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税纳税申报表附表二（本期进项税额明细表）》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr02_bqjxsemxb", propOrder = { "sbbhead", "bqjxsemxbGrid" })
@Getter
@Setter
public class Zzssyyybnsr02Bqjxsemxb {
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 增值税纳税申报表附列资料（附表二）
     */
    @XmlElement(nillable = true, required = true)
    protected BqjxsemxbGrid bqjxsemxbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "bqjxsemxbGridlbVO" })
    @Getter
    @Setter
    public static class BqjxsemxbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<BqjxsemxbGridlbVO> bqjxsemxbGridlbVO;

        /**
         * Gets the value of the bqjxsemxbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the bqjxsemxbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getBqjxsemxbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link BqjxsemxbGridlbVO}
         */
        public List<BqjxsemxbGridlbVO> getBqjxsemxbGridlbVO() {
            if (bqjxsemxbGridlbVO == null) {
                bqjxsemxbGridlbVO = new ArrayList<BqjxsemxbGridlbVO>();
            }
            return this.bqjxsemxbGridlbVO;
        }
    }
}