package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《铁路建设基金纳税申报表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_tljsjjnssbb", propOrder = { "sbbhead", "tljsjjnssbbGrid" })
@Getter
@Setter
public class ZzssyyybnsrTljsjjnssbb {
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 铁路建设基金纳税申报表
     */
    @XmlElement(nillable = true, required = true)
    protected TljsjjnssbbGrid tljsjjnssbbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "tljsjjnssbbGridlbVO" })
    @Getter
    @Setter
    public static class TljsjjnssbbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<TljsjjnssbbGridlbVO> tljsjjnssbbGridlbVO;

        /**
         * Gets the value of the tljsjjnssbbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the tljsjjnssbbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getTljsjjnssbbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link TljsjjnssbbGridlbVO}
         */
        public List<TljsjjnssbbGridlbVO> getTljsjjnssbbGridlbVO() {
            if (tljsjjnssbbGridlbVO == null) {
                tljsjjnssbbGridlbVO = new ArrayList<TljsjjnssbbGridlbVO>();
            }
            return this.tljsjjnssbbGridlbVO;
        }
    }
}