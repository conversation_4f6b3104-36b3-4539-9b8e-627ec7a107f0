package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 《加油站月销售油品汇总表》
 *
 * <p>jyzyxsyphzbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="jyzyxsyphzbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="jyzyxsyphzbGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}jyzyxsyphzbGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jyzyxsyphzbGrid", propOrder = { "jyzyxsyphzbGridlbVO" })
public class JyzyxsyphzbGrid {
    /**
     * 加油站月销售油品汇总表
     */
    @XmlElement(nillable = true, required = true)
    protected JyzyxsyphzbGridlbVO jyzyxsyphzbGridlbVO;

    /**
     * 获取jyzyxsyphzbGridlbVO属性的值。
     * <p>
     * 加油站月销售油品汇总表
     */
    public JyzyxsyphzbGridlbVO getJyzyxsyphzbGridlbVO() {
        return jyzyxsyphzbGridlbVO;
    }

    /**
     * 设置jyzyxsyphzbGridlbVO属性的值。
     */
    public void setJyzyxsyphzbGridlbVO(JyzyxsyphzbGridlbVO value) {
        this.jyzyxsyphzbGridlbVO = value;
    }
}