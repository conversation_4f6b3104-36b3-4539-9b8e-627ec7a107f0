package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表Grid
 *
 * <p>gjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="gjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid", propOrder = { "gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO" })
public class GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid {
    @XmlElement(nillable = true, required = true)
    protected GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO;

    /**
     * 获取gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO属性的值。
     */
    public GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO getGjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO() {
        return gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO;
    }

    /**
     * 设置gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO属性的值。
     */
    public void setGjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO(GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO value) {
        this.gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO = value;
    }
}