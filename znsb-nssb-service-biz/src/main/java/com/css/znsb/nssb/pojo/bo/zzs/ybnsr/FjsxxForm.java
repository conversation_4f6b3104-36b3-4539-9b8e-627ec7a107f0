package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 附加税申报信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "fjsxxForm", propOrder = { "bqsfsycjyhxqyjzzc", "dqxztze", "sqldkdmje", "jzxqkdmje", "dqxzldtse", "sqjcldtse", "jzxqldtse", "bchssqq", "bchssqz" })
@Getter
@Setter
public class FjsxxForm {
    /**
     * 本期是否适用试点建设培育产教融合型企业抵免政策
     */
    @XmlElement(nillable = true, required = true)
    protected String bqsfsycjyhxqyjzzc;

    /**
     * 减征政策适用主体
     */
    @XmlElement(nillable = true, required = true)
    protected String bqsfsyxgmyhzc;

    /**
     * 减征政策适用主体代码
     */
    protected String jzzcsyztDm;

    private String jsyjxgxz;

    private String jsyjxgyy;
    private String syxgmjzzcqssj;
    private String syxgmjzzczzsj;
    private String fjsddsbyy;
    private String mdsexgyy;

    /**
     * 当期新增投资额
     */
    protected BigDecimal dqxztze;

    /**
     * 上期留抵可抵免金额
     */
    protected BigDecimal sqldkdmje;

    /**
     * 结转下期可抵免金额
     */
    protected BigDecimal jzxqkdmje;

    /**
     * 当期新增留抵退税额
     */
    protected BigDecimal dqxzldtse;

    /**
     * 上期结存留抵退税额
     */
    protected BigDecimal sqjcldtse;

    /**
     * 结转下期留抵退税额
     */
    protected BigDecimal jzxqldtse;

    /**
     * 被红冲所属期起
     */
    @XmlElement(nillable = true, required = true)
    protected String bchssqq;

    /**
     * 被红冲所属期止
     */
    @XmlElement(nillable = true, required = true)
    protected String bchssqz;
}