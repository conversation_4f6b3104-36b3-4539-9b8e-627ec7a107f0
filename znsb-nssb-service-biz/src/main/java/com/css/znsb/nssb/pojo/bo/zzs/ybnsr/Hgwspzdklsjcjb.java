package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《海关完税凭证抵扣联数据采集表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hgwspzdklsjcjb", propOrder = { "sbbhead", "hgwspzdklsjGrid", "slxxForm" })
@XmlSeeAlso({ ZzssyyybnsrHgwspzdklsjcjbywbw.Hgwspzdklsjcjb.class })
@Getter
@Setter
public class Hgwspzdklsjcjb {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    @XmlElement(nillable = true, required = true)
    protected HgwspzdklsjGrid hgwspzdklsjGrid;

    /**
     * 受理信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbslxxVO slxxForm;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "hgwspzdklsjGridlbVO" })
    @Getter
    @Setter
    public static class HgwspzdklsjGrid {
        @XmlElement(nillable = true, required = true)
        protected List<HgwspzdklsjGridlbVO> hgwspzdklsjGridlbVO;

        /**
         * Gets the value of the hgwspzdklsjGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the hgwspzdklsjGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getHgwspzdklsjGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link HgwspzdklsjGridlbVO}
         */
        public List<HgwspzdklsjGridlbVO> getHgwspzdklsjGridlbVO() {
            if (hgwspzdklsjGridlbVO == null) {
                hgwspzdklsjGridlbVO = new ArrayList<HgwspzdklsjGridlbVO>();
            }
            return this.hgwspzdklsjGridlbVO;
        }
    }
}