package com.css.znsb.nssb.pojo.bo.hxzg.sb000;

import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;

import java.lang.reflect.Method;


/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.sbzs.sb.sb000
 * @file SBJmxxVO.java 创建时间:2014-7-12下午04:33:29
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR> 赵国良
 * @reviewer 审核人 邓文辉
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class SBJmxxVO extends TaxBaseVO {

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 848468606866957729L;
    
    /**
     * @description 减免收入
     * @value value:jmsr
     */
    private Double jmsr;
    
    /**
     * @description 税率
     * @value value:sl1
     */
    private Double sl1;
    /**
     * @description 减免税额
     * @value value:jmse
     */
    private Double jmse;
    /**
     * @description 申报信息表主键
     * @value value:sbxxuuid
     */
    private String sbxxuuid;
    /**
    * @description 减免方式代码
    * @value: jmfsDm
    */
    private String jmfsDm;
    /**
    * @description 减免征类型代码
    * @value: jmzlxDm
    */
    private String jmzlxDm;
    /**
    * @description 减免期限起
    * @value: jmqxq
    */
    private String jmqxq;
    /**
    * @description 减免期限止
    * @value: jmqxz
    */
    private String jmqxz;
    /**
    * @description 减免性质汇总代码
    * @value: ssjmxzhzDm
    */
    private String ssjmxzhzDm;
    /**
    * @description 税收减免性质大类代码
    * @value: ssjmxzdlDm
    */
    private String ssjmxzdlDm;
    /**
    * @description 税收减免性质小类代码
    * @value: ssjmxzxlDm
    */
    private String ssjmxzxlDm;
    /**
    * @description 减征幅度
    * @value: jzfd
    */
    private Double jzfd;
    /**
    * @description 减征税率
    * @value: jzsl
    */
    private Double jzsl;
    /**
    * @description 减征额度
    * @value: jzed
    */
    private Double jzed;
    /**
    * @description 数据截止日期||数据截止日期
    * @value: sjjzrq
    */
    private String sjjzrq;
    /**
    * @description 数据归属地区
    * @value: sjgsdq
    */
    private String sjgsdq;
    /**
    * @description 优惠凭证UUID
    * @value: yhpzuuid
    */
    private String yhpzuuid;
    /**
    * @description 登记序号
    * @value: djxh
    */
    private String djxh;

    /**
    * @description 减免税审批事项代码
    * @value: jmsspsxDm
    */
    private String jmsspsxDm;
    /**
    * @description 征收项目代码
    * @value: zsxmDm
    */
    private String zsxmDm;
    /**
    * @description 征收品目代码
    * @value: zspmDm
    */
    private String zspmDm;
    /**
    * @description 减免项目大类代码
    * @value: jmxmdlDm
    */
    private String jmxmdlDm;
    /**
    * @description 减免项目小类代码
    * @value: jmxmxlDm
    */
    private String jmxmxlDm;
    /**
    * @description 减免文件代码
    * @value: jmwjDm
    */
    private String jmwjDm;
    /**
    * @description 减免类型代码
    * @value: jmlxDm
    */
    private String jmlxDm;

    /**
     * @description 车辆识别代号
     * @value value:clsbdh
     */
    private String clsbdh;
    
    /**
     * @description 税收减免申报列名代码
     * @value value:ssjmsblmDm
     */
    private String ssjmsblmDm;
    
    /**
     * @description 字段功能描述
     * @value value:skssswjgDm
     */
    /**
     * @description 税款所属机关
     * @value value:skssswjgDm
     */
    private String skssswjgDm;
    
    /**
     * @description 主管税务所科分局
     * @value value:zgswskfjDm
     */
    private String zgswskfjDm;
    
    /**
     * @description 行业代码
     * @value value:hyDm
     */
    private String hyDm;
    
    /**
     * @description 登记注册类型代码
     * @value value:djzclxDm
     */
    private String djzclxDm;
    
    /**
     * @description 税款所属期起
     * @value value:skssqq
     */
    private String skssqq;   
    
    /**
     * @description 税款所属期止
     * @value value:skssqz
     */
    private String skssqz;
    
    /**
     * @description 凭证序号
     * @value value:pzxh
     */
    private String pzxh;
    
    /**
     * @description 税源编号
     * @value value:sybh
     */
    private String sybh;
    
    /**
     * @description 减免折算表存储（Y：储存  ； N或者空：不存储）
     * @value value:jmZsbj
     */
    private String jmZsbj;
    
    /**
     * @description 减免税收入种类代码
     * @value value:jmssrzl
     */
    private String jmssrzl;

    /**
     * @description 街道乡镇代码
     * @value value:jdxzDm
     */
    private String jdxzDm;
    
    /**
     * @description 单位隶属关系代码
     * @value value:dwlsgxDm
     */
    private String dwlsgxDm;
    
    /**
     * @description 征收子目代码
     * @value value:zszmDm
     */
    private String zszmDm;
    
    /**
     * @description 限额减免标记
     * @value value:xejmje
     */
    private String xejmBz;
    /**
     * @description 税务事项代码
     * @value value:swsxDm
     */
    private String swsxDm;
    /**
     * @description 是否附加税
     * @value value:sfFjs
     */
    private String sfFjs ="N";
    
    private String sybh1;
    
    public String getSfFjs() {
        return sfFjs;
    }

    public void setSfFjs(String sfFjs) {
        this.sfFjs = sfFjs;
    }

    public String getSwsxDm() {
        return swsxDm;
    }

    public void setSwsxDm(String swsxDm) {
        this.swsxDm = swsxDm;
    }

    public String getJdxzDm() {
        return jdxzDm;
    }

    public void setJdxzDm(String jdxzDm) {
        this.jdxzDm = jdxzDm;
    }

    public String getDwlsgxDm() {
        return dwlsgxDm;
    }

    public void setDwlsgxDm(String dwlsgxDm) {
        this.dwlsgxDm = dwlsgxDm;
    }

    public String getJmssrzl() {
        return jmssrzl;
    }

    public void setJmssrzl(String jmssrzl) {
        this.jmssrzl = jmssrzl;
    }

    public String getPzxh() {
        return pzxh;
    }

    public void setPzxh(String pzxh) {
        this.pzxh = pzxh;
    }

    public String getSybh() {
        return sybh;
    }

    public void setSybh(String sybh) {
        this.sybh = sybh;
    }

    /**
     *创建时间:2014-11-17下午07:32:36
     *get方法
     * @return the ssjmsblmDm
     */
    public String getSsjmsblmDm() {
        return ssjmsblmDm;
    }

    /**
     * 创建时间:2014-11-17下午07:32:36
     * set方法
     * @param ssjmsblmDm the ssjmsblmDm to set
     */
    public void setSsjmsblmDm(String ssjmsblmDm) {
        this.ssjmsblmDm = ssjmsblmDm;
    }

    /**
     * @description 车辆购置税免减税条件代码
     * @value value:clgzsmjstjDm
     */
    private String clgzsmjstjDm;
    
    /**
     * @description 特定减免标志（0、个体  1、企业   ）
     * @value value:tdjmBz
     */
    private String tdjmBz;
    
    /**
     * @description 即征即退标志
     * @value value:jzjtBz
     */
    private String jzjtBz;
    
    /**
     * @description 录入日期
     * @value value:lrrq
     */
    private String lrrq;


    public String getTdjmBz() {
        return tdjmBz;
    }

    public void setTdjmBz(String tdjmBz) {
        this.tdjmBz = tdjmBz;
    }
    public String getJzjtBz(){
        return jzjtBz;
    }
    public void setJzjtBz(String jzjtBz){
        this.jzjtBz = jzjtBz;
    }
    
    public String getLrrq(){
        return lrrq;
    }
    public void setLrrq(String lrrq){
        this.lrrq = lrrq;
    }

    public Double getJmsr() {
        return jmsr;
    }

    public void setJmsr(Double jmsr) {
        this.jmsr = jmsr;
    }

    public Double getSl1() {
        return sl1;
    }

    public void setSl1(Double sl1) {
        this.sl1 = sl1;
    }

    public Double getJmse() {
        return jmse;
    }

    public void setJmse(Double jmse) {
        this.jmse = jmse;
    }

    public String getSbxxuuid() {
        return sbxxuuid;
    }

    public void setSbxxuuid(String sbxxuuid) {
        this.sbxxuuid = sbxxuuid;
    }

    public String getJmfsDm() {
        return jmfsDm;
    }

    public void setJmfsDm(String jmfsDm) {
        this.jmfsDm = jmfsDm;
    }

    public String getJmzlxDm() {
        return jmzlxDm;
    }

    public void setJmzlxDm(String jmzlxDm) {
        this.jmzlxDm = jmzlxDm;
    }

    public String getJmqxq() {
        return jmqxq;
    }

    public void setJmqxq(String jmqxq) {
        this.jmqxq = jmqxq;
    }

    public String getJmqxz() {
        return jmqxz;
    }

    public void setJmqxz(String jmqxz) {
        this.jmqxz = jmqxz;
    }

    public String getSsjmxzhzDm() {
        return ssjmxzhzDm;
    }

    public void setSsjmxzhzDm(String ssjmxzhzDm) {
        this.ssjmxzhzDm = ssjmxzhzDm;
    }

    public String getSsjmxzdlDm() {
        return ssjmxzdlDm;
    }

    public void setSsjmxzdlDm(String ssjmxzdlDm) {
        this.ssjmxzdlDm = ssjmxzdlDm;
    }

    public String getSsjmxzxlDm() {
        return ssjmxzxlDm;
    }

    public void setSsjmxzxlDm(String ssjmxzxlDm) {
        this.ssjmxzxlDm = ssjmxzxlDm;
    }

    public Double getJzfd() {
        return jzfd;
    }

    public void setJzfd(Double jzfd) {
        this.jzfd = jzfd;
    }

    public Double getJzsl() {
        return jzsl;
    }

    public void setJzsl(Double jzsl) {
        this.jzsl = jzsl;
    }

    public Double getJzed() {
        return jzed;
    }

    public void setJzed(Double jzed) {
        this.jzed = jzed;
    }

    public String getSjjzrq() {
        return sjjzrq;
    }

    public void setSjjzrq(String sjjzrq) {
        this.sjjzrq = sjjzrq;
    }

    public String getSjgsdq() {
        return sjgsdq;
    }

    public void setSjgsdq(String sjgsdq) {
        this.sjgsdq = sjgsdq;
    }

    public String getYhpzuuid() {
        return yhpzuuid;
    }

    public void setYhpzuuid(String yhpzuuid) {
        this.yhpzuuid = yhpzuuid;
    }

    public String getDjxh() {
        return djxh;
    }

    public void setDjxh(String djxh) {
        this.djxh = djxh;
    }

    public String getJmsspsxDm() {
        return jmsspsxDm;
    }

    public void setJmsspsxDm(String jmsspsxDm) {
        this.jmsspsxDm = jmsspsxDm;
    }

    public String getZsxmDm() {
        return zsxmDm;
    }

    public void setZsxmDm(String zsxmDm) {
        this.zsxmDm = zsxmDm;
    }

    public String getZspmDm() {
        return zspmDm;
    }

    public void setZspmDm(String zspmDm) {
        this.zspmDm = zspmDm;
    }

    public String getJmxmdlDm() {
        return jmxmdlDm;
    }

    public void setJmxmdlDm(String jmxmdlDm) {
        this.jmxmdlDm = jmxmdlDm;
    }

    public String getJmxmxlDm() {
        return jmxmxlDm;
    }

    public void setJmxmxlDm(String jmxmxlDm) {
        this.jmxmxlDm = jmxmxlDm;
    }

    public String getJmwjDm() {
        return jmwjDm;
    }

    public void setJmwjDm(String jmwjDm) {
        this.jmwjDm = jmwjDm;
    }

    public String getJmlxDm() {
        return jmlxDm;
    }

    public void setJmlxDm(String jmlxDm) {
        this.jmlxDm = jmlxDm;
    }

    public String getClsbdh() {
        return clsbdh;
    }

    public void setClsbdh(String clsbdh) {
        this.clsbdh = clsbdh;
    }

    public String getClgzsmjstjDm() {
        return clgzsmjstjDm;
    }

    public void setClgzsmjstjDm(String clgzsmjstjDm) {
        this.clgzsmjstjDm = clgzsmjstjDm;
    }

    public String getSkssswjgDm() {
        return skssswjgDm;
    }

    public void setSkssswjgDm(String skssswjgDm) {
        this.skssswjgDm = skssswjgDm;
    }

    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    public void setZgswskfjDm(String zgswskfjDm) {
        this.zgswskfjDm = zgswskfjDm;
    }

    public String getHyDm() {
        return hyDm;
    }

    public void setHyDm(String hyDm) {
        this.hyDm = hyDm;
    }

    public String getDjzclxDm() {
        return djzclxDm;
    }

    public void setDjzclxDm(String djzclxDm) {
        this.djzclxDm = djzclxDm;
    }

    public String getSkssqq() {
        return skssqq;
    }

    public void setSkssqq(String skssqq) {
        this.skssqq = skssqq;
    }

    public String getSkssqz() {
        return skssqz;
    }

    public void setSkssqz(String skssqz) {
        this.skssqz = skssqz;
    }

    public String getJmZsbj() {
        return jmZsbj;
    }

    public void setJmZsbj(String jmZsbj) {
        this.jmZsbj = jmZsbj;
    }

    public String getZszmDm() {
        return zszmDm;
    }

    public void setZszmDm(String zszmDm) {
        this.zszmDm = zszmDm;
    }

    public String getXejmBz() {
        return xejmBz;
    }

    public void setXejmBz(String xejmBz) {
        this.xejmBz = xejmBz;
    }

    /**
     *创建时间:2017-9-6下午05:23:11
     *get方法
     * @return the sybh1
     */
    public String getSybh1() {
        return sybh1;
    }

    /**
     * 创建时间:2017-9-6下午05:23:11
     * set方法
     * @param sybh1 the sybh1 to set
     */
    public void setSybh1(String sybh1) {
        this.sybh1 = sybh1;
    }
    /**
     *@name    中文名称
     *@description 相关说明
     *@time    创建时间:2019年3月19日上午9:23:40
     *@param obj 比较对象
     *@return 是否相等
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public boolean equals(Object obj) {
        try {
            Method[] mes = this.getClass().getDeclaredMethods();
            if(mes!=null && mes.length>0){
                for(int i=0; i<mes.length; i++){
                    String msName = mes[i].getName();
                    if(msName.startsWith("get")){
                        Object ob = mes[i].invoke(this, new Object[]{});
                        Object ob1 = mes[i].invoke(obj, new Object[]{});
                        if(!(ob==ob1||(ob!=null&&ob.equals(ob1)))){
                            return false;
                        }
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
