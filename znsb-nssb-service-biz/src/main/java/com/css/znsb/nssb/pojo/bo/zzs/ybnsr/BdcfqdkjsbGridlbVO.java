package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bdcfqdkjsbGridlbVO", propOrder = { "ewbhxh", "hmc", "qcye", "bqydjse", "bqsjdjse", "bqsjdjse1", "bqfse", "qmye" })
@Getter
@Setter
public class BdcfqdkjsbGridlbVO {
    /**
     * 二维表行序号
     */
    protected long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 期初余额(期初待抵扣不动产进项税额)
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qcye;

    /**
     * 本期应抵减税额(本期可抵扣不动产进项税额)
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqydjse;

    /**
     * 本期实际抵减税额(本期转入的待抵扣不动产进项税额)
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqsjdjse;

    /**
     * 本期实际抵减税额1(本期转出的待抵扣不动产进项税额)
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqsjdjse1;

    /**
     * 本期发生额(本期不动产进项税额增加额)
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqfse;

    /**
     * 期末余额(期末待抵扣不动产进项税额)
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmye;
}