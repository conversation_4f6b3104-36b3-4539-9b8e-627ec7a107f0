
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《小汽车消费税纳税申报表》
 * 
 * <p>Java class for xfssb_xqc complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfssb_xqc">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="sbbhead" type="{http://www.chinatax.gov.cn/dataspec/}sbbheadkz1VO"/>
 *         &lt;element name="sbsjxxForm5" type="{http://www.chinatax.gov.cn/dataspec/}SBXfssbsjxxkz1VO"/>
 *         &lt;element name="sbsjxxGrid5">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded">
 *                   &lt;element name="sbsjxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBXfssbysxmVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="slxxFrom" type="{http://www.chinatax.gov.cn/dataspec/}sbbslxxkz3VO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfssb_xqc", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "sbbhead",
    "sbsjxxForm5",
    "sbsjxxGrid5",
    "slxxFrom"
})
public class XfssbXqc
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected Sbbheadkz1VO sbbhead;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected SBXfssbsjxxkz1VO sbsjxxForm5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected SbsjxxGrid5 sbsjxxGrid5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected Sbbslxxkz3VO slxxFrom;

    /**
     * Gets the value of the sbbhead property.
     * 
     * @return
     *     possible object is
     *     {@link Sbbheadkz1VO }
     *     
     */
    public Sbbheadkz1VO getSbbhead() {
        return sbbhead;
    }

    /**
     * Sets the value of the sbbhead property.
     * 
     * @param value
     *     allowed object is
     *     {@link Sbbheadkz1VO }
     *     
     */
    public void setSbbhead(Sbbheadkz1VO value) {
        this.sbbhead = value;
    }

    /**
     * Gets the value of the sbsjxxForm5 property.
     * 
     * @return
     *     possible object is
     *     {@link SBXfssbsjxxkz1VO }
     *     
     */
    public SBXfssbsjxxkz1VO getSbsjxxForm5() {
        return sbsjxxForm5;
    }

    /**
     * Sets the value of the sbsjxxForm5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link SBXfssbsjxxkz1VO }
     *     
     */
    public void setSbsjxxForm5(SBXfssbsjxxkz1VO value) {
        this.sbsjxxForm5 = value;
    }

    /**
     * Gets the value of the sbsjxxGrid5 property.
     * 
     * @return
     *     possible object is
     *     {@link SbsjxxGrid5 }
     *     
     */
    public SbsjxxGrid5 getSbsjxxGrid5() {
        return sbsjxxGrid5;
    }

    /**
     * Sets the value of the sbsjxxGrid5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link SbsjxxGrid5 }
     *     
     */
    public void setSbsjxxGrid5(SbsjxxGrid5 value) {
        this.sbsjxxGrid5 = value;
    }

    /**
     * Gets the value of the slxxFrom property.
     * 
     * @return
     *     possible object is
     *     {@link Sbbslxxkz3VO }
     *     
     */
    public Sbbslxxkz3VO getSlxxFrom() {
        return slxxFrom;
    }

    /**
     * Sets the value of the slxxFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link Sbbslxxkz3VO }
     *     
     */
    public void setSlxxFrom(Sbbslxxkz3VO value) {
        this.slxxFrom = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded">
     *         &lt;element name="sbsjxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBXfssbysxmVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "sbsjxxGridlb"
    })
    public static class SbsjxxGrid5
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
        protected List<SBXfssbysxmVO> sbsjxxGridlb;

        /**
         * Gets the value of the sbsjxxGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the sbsjxxGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getSbsjxxGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SBXfssbysxmVO }
         * 
         * 
         */
        public List<SBXfssbysxmVO> getSbsjxxGridlb() {
            if (sbsjxxGridlb == null) {
                sbsjxxGridlb = new ArrayList<SBXfssbysxmVO>();
            }
            return this.sbsjxxGridlb;
        }

        public void setSbsjxxGridlb(List<SBXfssbysxmVO> sbsjxxGridlb) {
            this.sbsjxxGridlb = sbsjxxGridlb;
        }
    }

}
