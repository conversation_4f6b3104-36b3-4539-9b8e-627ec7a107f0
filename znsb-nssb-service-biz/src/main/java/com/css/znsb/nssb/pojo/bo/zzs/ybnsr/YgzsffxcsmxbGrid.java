package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

/**
 * 营改增税负分析测算明细表
 *
 * <p>ygzsffxcsmxbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="ygzsffxcsmxbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ewbhxh" type="{http://www.chinatax.gov.cn/dataspec/}ewbhxh" minOccurs="0"/>
 *         &lt;element name="hmc" type="{http://www.chinatax.gov.cn/dataspec/}hmc"/>
 *         &lt;element name="ysxmdmjmc" type="{http://www.chinatax.gov.cn/dataspec/}ywmc" minOccurs="0"/>
 *         &lt;element name="zzsslhzsl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="yyssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="zzsbhsxse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzsxxynse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzsjshj" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzsbqsjkcje" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzskchhsxse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzskchxxynse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzsynse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysqcye" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysbqfse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysbqykcje" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysbqsjkcje" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysqmye" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysysyye" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysynse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ygzsffxcsmxbGrid", propOrder = { "ewbhxh", "hmc", "ysxmdmjmc", "zzsslhzsl", "yyssl", "zzsbhsxse", "zzsxxynse", "zzsjshj", "zzsbqsjkcje", "zzskchhsxse", "zzskchxxynse", "zzsynse", "yysqcye", "yysbqfse", "yysbqykcje", "yysbqsjkcje", "yysqmye", "yysysyye", "yysynse" })
public class YgzsffxcsmxbGrid {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 应税项目代码及名称
     */
    protected String ysxmdmjmc;

    /**
     * 增值税税率或征收率
     */
    protected BigDecimal zzsslhzsl;

    /**
     * 营业税税率
     */
    protected BigDecimal yyssl;

    /**
     * 增值税不含税销售额
     */
    protected BigDecimal zzsbhsxse;

    /**
     * 增值税销项应纳税额
     */
    protected BigDecimal zzsxxynse;

    /**
     * 增值税价税合计
     */
    protected BigDecimal zzsjshj;

    /**
     * 增值税本期实际扣除金额
     */
    protected BigDecimal zzsbqsjkcje;

    /**
     * 增值税扣除后含税销售额
     */
    protected BigDecimal zzskchhsxse;

    /**
     * 增值税扣除后销项应纳税额
     */
    protected BigDecimal zzskchxxynse;

    /**
     * 增值税应纳税额
     */
    protected BigDecimal zzsynse;

    /**
     * 营业税期初余额
     */
    protected BigDecimal yysqcye;

    /**
     * 营业税本期发生额
     */
    protected BigDecimal yysbqfse;

    /**
     * 营业税本期应扣除金额
     */
    protected BigDecimal yysbqykcje;

    /**
     * 营业税本期实际扣除金额
     */
    protected BigDecimal yysbqsjkcje;

    /**
     * 营业税期末余额
     */
    protected BigDecimal yysqmye;

    /**
     * 营业税应税营业额
     */
    protected BigDecimal yysysyye;

    /**
     * 营业税应纳税额
     */
    protected BigDecimal yysynse;

    /**
     * 获取ewbhxh属性的值。
     * <p>
     * 二维表行序号
     */
    public Long getEwbhxh() {
        return ewbhxh;
    }

    /**
     * 设置ewbhxh属性的值。
     */
    public void setEwbhxh(Long value) {
        this.ewbhxh = value;
    }

    /**
     * 获取hmc属性的值。
     * <p>
     * 行名称
     */
    public String getHmc() {
        return hmc;
    }

    /**
     * 设置hmc属性的值。
     */
    public void setHmc(String value) {
        this.hmc = value;
    }

    /**
     * 获取ysxmdmjmc属性的值。
     * <p>
     * 应税项目代码及名称
     */
    public String getYsxmdmjmc() {
        return ysxmdmjmc;
    }

    /**
     * 设置ysxmdmjmc属性的值。
     */
    public void setYsxmdmjmc(String value) {
        this.ysxmdmjmc = value;
    }

    /**
     * 获取zzsslhzsl属性的值。
     * <p>
     * 增值税税率或征收率
     */
    public BigDecimal getZzsslhzsl() {
        return zzsslhzsl;
    }

    /**
     * 设置zzsslhzsl属性的值。
     */
    public void setZzsslhzsl(BigDecimal value) {
        this.zzsslhzsl = value;
    }

    /**
     * 获取yyssl属性的值。
     * <p>
     * 营业税税率
     */
    public BigDecimal getYyssl() {
        return yyssl;
    }

    /**
     * 设置yyssl属性的值。
     */
    public void setYyssl(BigDecimal value) {
        this.yyssl = value;
    }

    /**
     * 获取zzsbhsxse属性的值。
     * <p>
     * 增值税不含税销售额
     */
    public BigDecimal getZzsbhsxse() {
        return zzsbhsxse;
    }

    /**
     * 设置zzsbhsxse属性的值。
     */
    public void setZzsbhsxse(BigDecimal value) {
        this.zzsbhsxse = value;
    }

    /**
     * 获取zzsxxynse属性的值。
     * <p>
     * 增值税销项应纳税额
     */
    public BigDecimal getZzsxxynse() {
        return zzsxxynse;
    }

    /**
     * 设置zzsxxynse属性的值。
     */
    public void setZzsxxynse(BigDecimal value) {
        this.zzsxxynse = value;
    }

    /**
     * 获取zzsjshj属性的值。
     * <p>
     * 增值税价税合计
     */
    public BigDecimal getZzsjshj() {
        return zzsjshj;
    }

    /**
     * 设置zzsjshj属性的值。
     */
    public void setZzsjshj(BigDecimal value) {
        this.zzsjshj = value;
    }

    /**
     * 获取zzsbqsjkcje属性的值。
     * <p>
     * 增值税本期实际扣除金额
     */
    public BigDecimal getZzsbqsjkcje() {
        return zzsbqsjkcje;
    }

    /**
     * 设置zzsbqsjkcje属性的值。
     */
    public void setZzsbqsjkcje(BigDecimal value) {
        this.zzsbqsjkcje = value;
    }

    /**
     * 获取zzskchhsxse属性的值。
     * <p>
     * 增值税扣除后含税销售额
     */
    public BigDecimal getZzskchhsxse() {
        return zzskchhsxse;
    }

    /**
     * 设置zzskchhsxse属性的值。
     */
    public void setZzskchhsxse(BigDecimal value) {
        this.zzskchhsxse = value;
    }

    /**
     * 获取zzskchxxynse属性的值。
     * <p>
     * 增值税扣除后销项应纳税额
     */
    public BigDecimal getZzskchxxynse() {
        return zzskchxxynse;
    }

    /**
     * 设置zzskchxxynse属性的值。
     */
    public void setZzskchxxynse(BigDecimal value) {
        this.zzskchxxynse = value;
    }

    /**
     * 获取zzsynse属性的值。
     * <p>
     * 增值税应纳税额
     */
    public BigDecimal getZzsynse() {
        return zzsynse;
    }

    /**
     * 设置zzsynse属性的值。
     */
    public void setZzsynse(BigDecimal value) {
        this.zzsynse = value;
    }

    /**
     * 获取yysqcye属性的值。
     * <p>
     * 营业税期初余额
     */
    public BigDecimal getYysqcye() {
        return yysqcye;
    }

    /**
     * 设置yysqcye属性的值。
     */
    public void setYysqcye(BigDecimal value) {
        this.yysqcye = value;
    }

    /**
     * 获取yysbqfse属性的值。
     * <p>
     * 营业税本期发生额
     */
    public BigDecimal getYysbqfse() {
        return yysbqfse;
    }

    /**
     * 设置yysbqfse属性的值。
     */
    public void setYysbqfse(BigDecimal value) {
        this.yysbqfse = value;
    }

    /**
     * 获取yysbqykcje属性的值。
     * <p>
     * 营业税本期应扣除金额
     */
    public BigDecimal getYysbqykcje() {
        return yysbqykcje;
    }

    /**
     * 设置yysbqykcje属性的值。
     */
    public void setYysbqykcje(BigDecimal value) {
        this.yysbqykcje = value;
    }

    /**
     * 获取yysbqsjkcje属性的值。
     * <p>
     * 营业税本期实际扣除金额
     */
    public BigDecimal getYysbqsjkcje() {
        return yysbqsjkcje;
    }

    /**
     * 设置yysbqsjkcje属性的值。
     */
    public void setYysbqsjkcje(BigDecimal value) {
        this.yysbqsjkcje = value;
    }

    /**
     * 获取yysqmye属性的值。
     * <p>
     * 营业税期末余额
     */
    public BigDecimal getYysqmye() {
        return yysqmye;
    }

    /**
     * 设置yysqmye属性的值。
     */
    public void setYysqmye(BigDecimal value) {
        this.yysqmye = value;
    }

    /**
     * 获取yysysyye属性的值。
     * <p>
     * 营业税应税营业额
     */
    public BigDecimal getYysysyye() {
        return yysysyye;
    }

    /**
     * 设置yysysyye属性的值。
     */
    public void setYysysyye(BigDecimal value) {
        this.yysysyye = value;
    }

    /**
     * 获取yysynse属性的值。
     * <p>
     * 营业税应纳税额
     */
    public BigDecimal getYysynse() {
        return yysynse;
    }

    /**
     * 设置yysynse属性的值。
     */
    public void setYysynse(BigDecimal value) {
        this.yysynse = value;
    }
}