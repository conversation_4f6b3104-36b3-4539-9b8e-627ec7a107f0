package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 跨省铁路企业增值税分配表Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "kstlqyzzsfpbGridlbVO", propOrder = { "szd", "fzjgmc", "fzjgfpbl", "bqynse" })
@Getter
@Setter
public class KstlqyzzsfpbGridlbVO {
    /**
     * 所在地
     */
    protected String szd;

    /**
     * 主管税务机关
     */
    protected String fzjgmc;

    /**
     * 分解比例
     */
    protected BigDecimal fzjgfpbl;

    /**
     * 本期应缴税额
     */
    protected BigDecimal bqynse;
}