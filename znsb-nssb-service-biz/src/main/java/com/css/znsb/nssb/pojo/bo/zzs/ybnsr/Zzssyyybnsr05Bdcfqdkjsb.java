package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税纳税申报表附列资料五（不动产分期抵扣计算表）》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr05_bdcfqdkjsb", propOrder = { "sbbhead", "bdcfqdkjsbGrid" })
@Getter
@Setter
public class Zzssyyybnsr05Bdcfqdkjsb {
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 增值税纳税申报表附列资料五（不动产分期抵扣计算表）
     */
    @XmlElement(nillable = true, required = true)
    protected BdcfqdkjsbGrid bdcfqdkjsbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "bdcfqdkjsbGridlbVO" })
    @Getter
    @Setter
    public static class BdcfqdkjsbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<BdcfqdkjsbGridlbVO> bdcfqdkjsbGridlbVO;

        /**
         * Gets the value of the bdcfqdkjsbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the bdcfqdkjsbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getBdcfqdkjsbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link BdcfqdkjsbGridlbVO}
         */
        public List<BdcfqdkjsbGridlbVO> getBdcfqdkjsbGridlbVO() {
            if (bdcfqdkjsbGridlbVO == null) {
                bdcfqdkjsbGridlbVO = new ArrayList<BdcfqdkjsbGridlbVO>();
            }
            return this.bdcfqdkjsbGridlbVO;
        }
    }
}