package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

/**
 * 增值税纳税申报表附列资料（附表五）
 *
 * <p>bdcfqdkjsbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="bdcfqdkjsbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ewbhxh" type="{http://www.chinatax.gov.cn/dataspec/}ewbhxh" minOccurs="0"/>
 *         &lt;element name="fs" type="{http://www.chinatax.gov.cn/dataspec/}fs" minOccurs="0"/>
 *         &lt;element name="je" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="se" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="hmc" type="{http://www.chinatax.gov.cn/dataspec/}hmc"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bdcfqdkjsbGrid", propOrder = { "ewbhxh", "fs", "je", "se", "hmc" })
public class BdcfqdkjsbGrid {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 份数
     */
    protected BigDecimal fs;

    /**
     * 金额
     */
    protected BigDecimal je;

    /**
     * 税额
     */
    protected BigDecimal se;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 获取ewbhxh属性的值。
     * <p>
     * 二维表行序号
     */
    public Long getEwbhxh() {
        return ewbhxh;
    }

    /**
     * 设置ewbhxh属性的值。
     */
    public void setEwbhxh(Long value) {
        this.ewbhxh = value;
    }

    /**
     * 获取fs属性的值。
     * <p>
     * 份数
     */
    public BigDecimal getFs() {
        return fs;
    }

    /**
     * 设置fs属性的值。
     */
    public void setFs(BigDecimal value) {
        this.fs = value;
    }

    /**
     * 获取je属性的值。
     * <p>
     * 金额
     */
    public BigDecimal getJe() {
        return je;
    }

    /**
     * 设置je属性的值。
     */
    public void setJe(BigDecimal value) {
        this.je = value;
    }

    /**
     * 获取se属性的值。
     * <p>
     * 税额
     */
    public BigDecimal getSe() {
        return se;
    }

    /**
     * 设置se属性的值。
     */
    public void setSe(BigDecimal value) {
        this.se = value;
    }

    /**
     * 获取hmc属性的值。
     * <p>
     * 行名称
     */
    public String getHmc() {
        return hmc;
    }

    /**
     * 设置hmc属性的值。
     */
    public void setHmc(String value) {
        this.hmc = value;
    }
}