package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 附加税申报
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsfjssb", propOrder = { "fjsxxGrid", "fjsxxForm", "hznsFjsffpbGrid" })
@Getter
@Setter
public class Zzsfjssb {
    /**
     * 增值税附加税申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected FjsxxGrid fjsxxGrid;

    /**
     * 增值税附加税其它信息
     */
    protected FjsxxForm fjsxxForm;

    /**
     * 汇总纳税附加税费分配表
     */
    protected HznsFjsffpbGrid hznsFjsffpbGrid;
}