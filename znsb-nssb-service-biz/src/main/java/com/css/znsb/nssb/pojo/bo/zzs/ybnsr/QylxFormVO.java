package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 企业类型信息
 *
 * <p>qylxFormVO complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="qylxFormVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="qylxDm" type="{http://www.chinatax.gov.cn/dataspec/}zzsqylxDm"/>
 *         &lt;element name="slsdljt" type="{http://www.chinatax.gov.cn/dataspec/}qymc"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "qylxFormVO", propOrder = { "qylxDm", "slsdljt" })
public class QylxFormVO {
    /**
     * 企业类型
     */
    @XmlElement(nillable = true, required = true)
    protected String qylxDm;

    /**
     * 所隶属电力集团
     */
    @XmlElement(nillable = true, required = true)
    protected String slsdljt;

    /**
     * 获取qylxDm属性的值。
     * <p>
     * 企业类型
     */
    public String getQylxDm() {
        return qylxDm;
    }

    /**
     * 设置qylxDm属性的值。
     */
    public void setQylxDm(String value) {
        this.qylxDm = value;
    }

    /**
     * 获取slsdljt属性的值。
     * <p>
     * 所隶属电力集团
     */
    public String getSlsdljt() {
        return slsdljt;
    }

    /**
     * 设置slsdljt属性的值。
     */
    public void setSlsdljt(String value) {
        this.slsdljt = value;
    }
}