
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 附加税申报
 * 
 * <p>Java class for fjsxxGrid complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="fjsxxGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="fjsxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}fjsxxGridlb"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "fjsxxGrid", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "fjsxxGridlb"
})
public class FjsxxGrid
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected List<FjsxxGridlb> fjsxxGridlb;

    /**
     * Gets the value of the fjsxxGridlb property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the fjsxxGridlb property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getFjsxxGridlb().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link FjsxxGridlb }
     * 
     * 
     */
    public List<FjsxxGridlb> getFjsxxGridlb() {
        if (fjsxxGridlb == null) {
            fjsxxGridlb = new ArrayList<FjsxxGridlb>();
        }
        return this.fjsxxGridlb;
    }
    
    /**
     * 创建时间:2014-11-11下午10:58:11
     * set方法
     * @param fjsxxGridlb the fjsxxGridlb to set
     */
    public void setFjsxxGridlb(List<FjsxxGridlb> fjsxxGridlb) {
        this.fjsxxGridlb = fjsxxGridlb;
    }
}
