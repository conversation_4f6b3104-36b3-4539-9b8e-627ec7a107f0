package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.skjn.ZnsbSkjnService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 缴款信息归集
 */
@Slf4j
@Component
public class YyjkJob {

    @Resource
    private ZnsbSkjnService znsbSkjnService;

    /**
     * 预约缴款处理
     */
    @XxlJob("YyjkJob")
    public void execute() {
        log.info("==========开始预约缴款处理==========");
        final String jobParam = XxlJobHelper.getJobParam();
        log.info("入参jobParam{}",jobParam);
        znsbSkjnService.yyjkcl(jobParam);
        log.info("==========预约缴款处理完成========== ");
    }

}
