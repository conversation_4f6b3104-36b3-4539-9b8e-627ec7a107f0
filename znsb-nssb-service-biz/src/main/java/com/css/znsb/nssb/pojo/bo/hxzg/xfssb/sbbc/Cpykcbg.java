
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 成品油库存报告表
 * 
 * <p>Java class for cpykcbg complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="cpykcbg">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="cpykcbgGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="cpykcbgGridlb" type="{http://www.chinatax.gov.cn/dataspec/}cpykcbgGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "cpykcbg", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "cpykcbgGrid"
})
public class Cpykcbg
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected CpykcbgGrid cpykcbgGrid;

    /**
     * Gets the value of the cpykcbgGrid property.
     * 
     * @return
     *     possible object is
     *     {@link CpykcbgGrid }
     *     
     */
    public CpykcbgGrid getCpykcbgGrid() {
        return cpykcbgGrid;
    }

    /**
     * Sets the value of the cpykcbgGrid property.
     * 
     * @param value
     *     allowed object is
     *     {@link CpykcbgGrid }
     *     
     */
    public void setCpykcbgGrid(CpykcbgGrid value) {
        this.cpykcbgGrid = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="cpykcbgGridlb" type="{http://www.chinatax.gov.cn/dataspec/}cpykcbgGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "cpykcbgGridlb"
    })
    public static class CpykcbgGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<CpykcbgGridlbVO> cpykcbgGridlb;

        /**
         * Gets the value of the cpykcbgGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the cpykcbgGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getCpykcbgGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link CpykcbgGridlbVO }
         * 
         * 
         */
        public List<CpykcbgGridlbVO> getCpykcbgGridlb() {
            if (cpykcbgGridlb == null) {
                cpykcbgGridlb = new ArrayList<CpykcbgGridlbVO>();
            }
            return this.cpykcbgGridlb;
        }

    }

}
