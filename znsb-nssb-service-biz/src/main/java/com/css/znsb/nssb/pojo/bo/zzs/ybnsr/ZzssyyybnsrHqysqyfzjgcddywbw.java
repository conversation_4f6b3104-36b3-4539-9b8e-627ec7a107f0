package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《航空运输企业分支机构传递单》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_hqysqyfzjgcddywbw", propOrder = { "zzssyyybnsrHqysqyfzjgcdd" })
@Getter
@Setter
public class ZzssyyybnsrHqysqyfzjgcddywbw extends TaxDoc {
    /**
     * 航空运输企业分支机构传递单
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_hqysqyfzjgcdd", required = true)
    @JSONField(name = "zzssyyybnsr_hqysqyfzjgcdd")
    protected ZzssyyybnsrHqysqyfzjgcdd zzssyyybnsrHqysqyfzjgcdd;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrHqysqyfzjgcdd extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrHqysqyfzjgcdd {}
}