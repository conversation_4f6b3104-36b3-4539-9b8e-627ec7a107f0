
package com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 部分产品销售统计表
 * 
 * <p>Java class for bfcpxstjbFormVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="bfcpxstjbFormVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="nsrmc" type="{http://www.chinatax.gov.cn/dataspec/}nsrmc" minOccurs="0"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq" minOccurs="0"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz" minOccurs="0"/>
 *         &lt;element name="tbrq" type="{http://www.chinatax.gov.cn/dataspec/}tbrq" minOccurs="0"/>
 *         &lt;element name="ltxssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="ltxse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *         &lt;element name="zwxltxssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="zwxltxse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *         &lt;element name="xjltxssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="xjltxse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *         &lt;element name="jjxssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="jjxse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *         &lt;element name="yyycqydjjxssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="yyycqydjjxse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *         &lt;element name="syjjxssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="syjjxse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *         &lt;element name="qtjjxssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="qtjjxse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *         &lt;element name="mtcxssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="mtcxse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bfcpxstjbFormVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "nsrsbh",
    "nsrmc",
    "skssqq",
    "skssqz",
    "tbrq",
    "ltxssl",
    "ltxse",
    "zwxltxssl",
    "zwxltxse",
    "xjltxssl",
    "xjltxse",
    "jjxssl",
    "jjxse",
    "yyycqydjjxssl",
    "yyycqydjjxse",
    "syjjxssl",
    "syjjxse",
    "qtjjxssl",
    "qtjjxse",
    "mtcxssl",
    "mtcxse"
})
public class BfcpxstjbFormVO
    implements Serializable
{

    private final static long serialVersionUID = 1468566763656402744L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nsrmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tbrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ltxssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ltxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zwxltxssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zwxltxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double xjltxssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double xjltxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jjxssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jjxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yyycqydjjxssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yyycqydjjxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double syjjxssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double syjjxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qtjjxssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qtjjxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mtcxssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mtcxse;

    /**
     * Gets the value of the nsrsbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrsbh() {
        return nsrsbh;
    }

    /**
     * Sets the value of the nsrsbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrsbh(String value) {
        this.nsrsbh = value;
    }

    /**
     * Gets the value of the nsrmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrmc() {
        return nsrmc;
    }

    /**
     * Sets the value of the nsrmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrmc(String value) {
        this.nsrmc = value;
    }

    /**
     * Gets the value of the skssqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * Sets the value of the skssqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * Gets the value of the skssqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * Sets the value of the skssqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * Gets the value of the tbrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTbrq() {
        return tbrq;
    }

    /**
     * Sets the value of the tbrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTbrq(String value) {
        this.tbrq = value;
    }

    /**
     * Gets the value of the ltxssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getLtxssl() {
        return ltxssl;
    }

    /**
     * Sets the value of the ltxssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setLtxssl(Double value) {
        this.ltxssl = value;
    }

    /**
     * Gets the value of the ltxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getLtxse() {
        return ltxse;
    }

    /**
     * Sets the value of the ltxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setLtxse(Double value) {
        this.ltxse = value;
    }

    /**
     * Gets the value of the zwxltxssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZwxltxssl() {
        return zwxltxssl;
    }

    /**
     * Sets the value of the zwxltxssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZwxltxssl(Double value) {
        this.zwxltxssl = value;
    }

    /**
     * Gets the value of the zwxltxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZwxltxse() {
        return zwxltxse;
    }

    /**
     * Sets the value of the zwxltxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZwxltxse(Double value) {
        this.zwxltxse = value;
    }

    /**
     * Gets the value of the xjltxssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getXjltxssl() {
        return xjltxssl;
    }

    /**
     * Sets the value of the xjltxssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setXjltxssl(Double value) {
        this.xjltxssl = value;
    }

    /**
     * Gets the value of the xjltxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getXjltxse() {
        return xjltxse;
    }

    /**
     * Sets the value of the xjltxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setXjltxse(Double value) {
        this.xjltxse = value;
    }

    /**
     * Gets the value of the jjxssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJjxssl() {
        return jjxssl;
    }

    /**
     * Sets the value of the jjxssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJjxssl(Double value) {
        this.jjxssl = value;
    }

    /**
     * Gets the value of the jjxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJjxse() {
        return jjxse;
    }

    /**
     * Sets the value of the jjxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJjxse(Double value) {
        this.jjxse = value;
    }

    /**
     * Gets the value of the yyycqydjjxssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYyycqydjjxssl() {
        return yyycqydjjxssl;
    }

    /**
     * Sets the value of the yyycqydjjxssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYyycqydjjxssl(Double value) {
        this.yyycqydjjxssl = value;
    }

    /**
     * Gets the value of the yyycqydjjxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYyycqydjjxse() {
        return yyycqydjjxse;
    }

    /**
     * Sets the value of the yyycqydjjxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYyycqydjjxse(Double value) {
        this.yyycqydjjxse = value;
    }

    /**
     * Gets the value of the syjjxssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSyjjxssl() {
        return syjjxssl;
    }

    /**
     * Sets the value of the syjjxssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSyjjxssl(Double value) {
        this.syjjxssl = value;
    }

    /**
     * Gets the value of the syjjxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSyjjxse() {
        return syjjxse;
    }

    /**
     * Sets the value of the syjjxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSyjjxse(Double value) {
        this.syjjxse = value;
    }

    /**
     * Gets the value of the qtjjxssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQtjjxssl() {
        return qtjjxssl;
    }

    /**
     * Sets the value of the qtjjxssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQtjjxssl(Double value) {
        this.qtjjxssl = value;
    }

    /**
     * Gets the value of the qtjjxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQtjjxse() {
        return qtjjxse;
    }

    /**
     * Sets the value of the qtjjxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQtjjxse(Double value) {
        this.qtjjxse = value;
    }

    /**
     * Gets the value of the mtcxssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getMtcxssl() {
        return mtcxssl;
    }

    /**
     * Sets the value of the mtcxssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setMtcxssl(Double value) {
        this.mtcxssl = value;
    }

    /**
     * Gets the value of the mtcxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getMtcxse() {
        return mtcxse;
    }

    /**
     * Sets the value of the mtcxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setMtcxse(Double value) {
        this.mtcxse = value;
    }

}
