package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.sbrw.SbrwgjService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 申报任务归集定时任务
 */
@Slf4j
@Component
public class SbrwgjJob {
    
    @Resource
    private SbrwgjService sbrwgjService;

    /**
     * 申报任务归集
     */
    @XxlJob("sbrwgj")
    public void execute() {
        log.info("==========开始进行申报任务归集==========");
        sbrwgjService.sbrwgj();
        log.info("==========申报任务归集完成==========");
    }
}
