
package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 申报纳税人减免数据信息对象
 * 
 * <p>Java class for SBJmxxJhVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBJmxxJhVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="yhpzuuid" type="{http://www.chinatax.gov.cn/dataspec/}yhpzuuid" minOccurs="0"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh" minOccurs="0"/>
 *         &lt;element name="jmsspsxDm" type="{http://www.chinatax.gov.cn/dataspec/}jmsspsxDm" minOccurs="0"/>
 *         &lt;element name="zsxmDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmDm" minOccurs="0"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm" minOccurs="0"/>
 *         &lt;element name="jmxmdlDm" type="{http://www.chinatax.gov.cn/dataspec/}jlyyDm" minOccurs="0"/>
 *         &lt;element name="jmxmxlDm" type="{http://www.chinatax.gov.cn/dataspec/}jlyyDm" minOccurs="0"/>
 *         &lt;element name="jmwjDm" type="{http://www.chinatax.gov.cn/dataspec/}nr" minOccurs="0"/>
 *         &lt;element name="jmlxDm" type="{http://www.chinatax.gov.cn/dataspec/}jmlxDm" minOccurs="0"/>
 *         &lt;element name="jmfsDm" type="{http://www.chinatax.gov.cn/dataspec/}jmfsDm" minOccurs="0"/>
 *         &lt;element name="jmzlxDm" type="{http://www.chinatax.gov.cn/dataspec/}nsrzglxDm" minOccurs="0"/>
 *         &lt;element name="jmqxq" type="{http://www.chinatax.gov.cn/dataspec/}jmqxq" minOccurs="0"/>
 *         &lt;element name="jmqxz" type="{http://www.chinatax.gov.cn/dataspec/}jmqxz" minOccurs="0"/>
 *         &lt;element name="ssjmxzhzDm" type="{http://www.chinatax.gov.cn/dataspec/}nr" minOccurs="0"/>
 *         &lt;element name="ssjmxzdlDm" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzdlDm" minOccurs="0"/>
 *         &lt;element name="ssjmxzxlDm" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzxlDm" minOccurs="0"/>
 *         &lt;element name="jzfd" type="{http://www.chinatax.gov.cn/dataspec/}jzfd" minOccurs="0"/>
 *         &lt;element name="jzsl" type="{http://www.chinatax.gov.cn/dataspec/}jzsl" minOccurs="0"/>
 *         &lt;element name="jzed" type="{http://www.chinatax.gov.cn/dataspec/}jzed" minOccurs="0"/>
 *         &lt;element name="sjjzrq" type="{http://www.chinatax.gov.cn/dataspec/}sjjzrq" minOccurs="0"/>
 *         &lt;element name="ssjmsblmDm" type="{http://www.chinatax.gov.cn/dataspec/}ssjmsblmDm" minOccurs="0"/>
 *         &lt;element name="tdjmBz" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBJmxxJhVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "yhpzuuid",
    "djxh",
    "jmsspsxDm",
    "zsxmDm",
    "zspmDm",
    "jmxmdlDm",
    "jmxmxlDm",
    "jmwjDm",
    "jmlxDm",
    "jmfsDm",
    "jmzlxDm",
    "jmqxq",
    "jmqxz",
    "ssjmxzhzDm",
    "ssjmxzdlDm",
    "ssjmxzxlDm",
    "jzfd",
    "jzsl",
    "jzed",
    "sjjzrq",
    "ssjmsblmDm",
    "tdjmBz",
    "sl1"
})
public class SBJmxxJhVO
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yhpzuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmsspsxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmxmdlDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmxmxlDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmwjDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmlxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmfsDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmzlxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmqxq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jmqxz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssjmxzhzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssjmxzdlDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssjmxzxlDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jzfd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jzsl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jzed;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sjjzrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssjmsblmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tdjmBz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = false)
    protected Double sl1;

    /**
     * Gets the value of the yhpzuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYhpzuuid() {
        return yhpzuuid;
    }

    /**
     * Sets the value of the yhpzuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYhpzuuid(String value) {
        this.yhpzuuid = value;
    }

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the jmsspsxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmsspsxDm() {
        return jmsspsxDm;
    }

    /**
     * Sets the value of the jmsspsxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmsspsxDm(String value) {
        this.jmsspsxDm = value;
    }

    /**
     * Gets the value of the zsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmDm() {
        return zsxmDm;
    }

    /**
     * Sets the value of the zsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmDm(String value) {
        this.zsxmDm = value;
    }

    /**
     * Gets the value of the zspmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * Sets the value of the zspmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * Gets the value of the jmxmdlDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmxmdlDm() {
        return jmxmdlDm;
    }

    /**
     * Sets the value of the jmxmdlDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmxmdlDm(String value) {
        this.jmxmdlDm = value;
    }

    /**
     * Gets the value of the jmxmxlDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmxmxlDm() {
        return jmxmxlDm;
    }

    /**
     * Sets the value of the jmxmxlDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmxmxlDm(String value) {
        this.jmxmxlDm = value;
    }

    /**
     * Gets the value of the jmwjDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmwjDm() {
        return jmwjDm;
    }

    /**
     * Sets the value of the jmwjDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmwjDm(String value) {
        this.jmwjDm = value;
    }

    /**
     * Gets the value of the jmlxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmlxDm() {
        return jmlxDm;
    }

    /**
     * Sets the value of the jmlxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmlxDm(String value) {
        this.jmlxDm = value;
    }

    /**
     * Gets the value of the jmfsDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmfsDm() {
        return jmfsDm;
    }

    /**
     * Sets the value of the jmfsDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmfsDm(String value) {
        this.jmfsDm = value;
    }

    /**
     * Gets the value of the jmzlxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmzlxDm() {
        return jmzlxDm;
    }

    /**
     * Sets the value of the jmzlxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmzlxDm(String value) {
        this.jmzlxDm = value;
    }

    /**
     * Gets the value of the jmqxq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmqxq() {
        return jmqxq;
    }

    /**
     * Sets the value of the jmqxq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmqxq(String value) {
        this.jmqxq = value;
    }

    /**
     * Gets the value of the jmqxz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmqxz() {
        return jmqxz;
    }

    /**
     * Sets the value of the jmqxz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmqxz(String value) {
        this.jmqxz = value;
    }

    /**
     * Gets the value of the ssjmxzhzDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmxzhzDm() {
        return ssjmxzhzDm;
    }

    /**
     * Sets the value of the ssjmxzhzDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmxzhzDm(String value) {
        this.ssjmxzhzDm = value;
    }

    /**
     * Gets the value of the ssjmxzdlDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmxzdlDm() {
        return ssjmxzdlDm;
    }

    /**
     * Sets the value of the ssjmxzdlDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmxzdlDm(String value) {
        this.ssjmxzdlDm = value;
    }

    /**
     * Gets the value of the ssjmxzxlDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmxzxlDm() {
        return ssjmxzxlDm;
    }

    /**
     * Sets the value of the ssjmxzxlDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmxzxlDm(String value) {
        this.ssjmxzxlDm = value;
    }

    /**
     * Gets the value of the jzfd property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJzfd() {
        return jzfd;
    }

    /**
     * Sets the value of the jzfd property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJzfd(Double value) {
        this.jzfd = value;
    }

    /**
     * Gets the value of the jzsl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJzsl() {
        return jzsl;
    }

    /**
     * Sets the value of the jzsl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJzsl(Double value) {
        this.jzsl = value;
    }

    /**
     * Gets the value of the jzed property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJzed() {
        return jzed;
    }

    /**
     * Sets the value of the jzed property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJzed(Double value) {
        this.jzed = value;
    }

    /**
     * Gets the value of the sjjzrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjjzrq() {
        return sjjzrq;
    }

    /**
     * Sets the value of the sjjzrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjjzrq(String value) {
        this.sjjzrq = value;
    }

    /**
     * Gets the value of the ssjmsblmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmsblmDm() {
        return ssjmsblmDm;
    }

    /**
     * Sets the value of the ssjmsblmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmsblmDm(String value) {
        this.ssjmsblmDm = value;
    }

    /**
     * Gets the value of the tdjmBz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdjmBz() {
        return tdjmBz;
    }

    /**
     * Sets the value of the tdjmBz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdjmBz(String value) {
        this.tdjmBz = value;
    }

    public Double getSl1() {
        return sl1;
    }

    public void setSl1(Double sl1) {
        this.sl1 = sl1;
    }
}
