package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

/**
 * 申报表附加信息VO
 *
 * <p>sbbFjxxVO complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="sbbFjxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="swdlrdwmc" type="{http://www.chinatax.gov.cn/dataspec/}swdlrdwmc" minOccurs="0"/>
 *         &lt;element name="swdlrlxdh" type="{http://www.chinatax.gov.cn/dataspec/}swdlrlxdh" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbFjxxVO", propOrder = { "swdlrdwmc", "swdlrlxdh" })
public class SbbFjxxVO {
    /**
     * 税务代理人名称
     */
    protected String swdlrdwmc;

    /**
     * 税务代理人名称
     */
    protected String swdlrlxdh;

    /**
     * 获取swdlrdwmc属性的值。
     * <p>
     * 税务代理人名称
     */
    public String getSwdlrdwmc() {
        return swdlrdwmc;
    }

    /**
     * 设置swdlrdwmc属性的值。
     */
    public void setSwdlrdwmc(String value) {
        this.swdlrdwmc = value;
    }

    /**
     * 获取swdlrlxdh属性的值。
     * <p>
     * 税务代理人名称
     */
    public String getSwdlrlxdh() {
        return swdlrlxdh;
    }

    /**
     * 设置swdlrlxdh属性的值。
     */
    public void setSwdlrlxdh(String value) {
        this.swdlrlxdh = value;
    }
}