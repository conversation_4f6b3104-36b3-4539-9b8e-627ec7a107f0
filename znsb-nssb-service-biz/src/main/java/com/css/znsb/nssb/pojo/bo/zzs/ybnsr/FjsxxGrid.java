package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 附加税申报
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "fjsxxGrid", propOrder = { "fjsxxGridlb" })
@Getter
@Setter
public class FjsxxGrid {
    @XmlElement(nillable = true, required = true)
    protected List<FjsxxGridlb> fjsxxGridlb;

    /**
     * Gets the value of the fjsxxGridlb property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the fjsxxGridlb property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getFjsxxGridlb().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link FjsxxGridlb}
     */
    public List<FjsxxGridlb> getFjsxxGridlb() {
        if (fjsxxGridlb == null) {
            fjsxxGridlb = new ArrayList<FjsxxGridlb>();
        }
        return this.fjsxxGridlb;
    }
}