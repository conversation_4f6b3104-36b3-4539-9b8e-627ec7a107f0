package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《汇总纳税企业增值税分配表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_hznsqyzzsfpbywbw", propOrder = { "zzssyyybnsrHznsqyzzsfpb" })
@Getter
@Setter
public class ZzssyyybnsrHznsqyzzsfpbywbw extends TaxDoc {
    /**
     * 汇总纳税企业增值税分配表
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_hznsqyzzsfpb", required = true)
    @JSONField(name = "zzssyyybnsr_hznsqyzzsfpb")
    protected ZzssyyybnsrHznsqyzzsfpb zzssyyybnsrHznsqyzzsfpb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrHznsqyzzsfpb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrHznsqyzzsfpb {}
}