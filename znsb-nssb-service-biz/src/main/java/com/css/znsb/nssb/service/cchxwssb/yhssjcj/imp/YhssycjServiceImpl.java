package com.css.znsb.nssb.service.cchxwssb.yhssjcj.imp;


import cn.hutool.core.bean.BeanUtil;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.mapper.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxMapper;
import com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhscjMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxDO;
import com.css.znsb.nssb.pojo.domain.cchxwssb.yhs.ZnsbNssbYhscjDO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.ccssycj.CxstysbNsrxxDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.ccssycj.SymxDTO;
import com.css.znsb.nssb.service.cchxwssb.yhssjcj.YhssycjService;
import com.css.znsb.nssb.utils.GYSbUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @param
 * <AUTHOR>
 * @project 金四新电子税务局
 * @file YhssycjServiceImpl.java 创建时间:2022年10月09日上午10:25:43
 * @name
 * @description
 * @time 印花税税源采集
 * @return
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */
@Slf4j
@Service
public class YhssycjServiceImpl implements YhssycjService {
//
//    @Resource
//    private SjjhService sjjhService;
//    @Resource
//    private YhssycjService yhssycjService;
//    @Resource
//    private YhsSbCwgzService yhsSbCwgzService;
//
    /**
     * znsb_nssb_cxsbtxx(财行税表头信息)
     */
    @Resource
    private ZnsbNssbCxsbtxxMapper znsbNssbCxsbtxxMapper;
//
//    @Resource
//    private ZnsbNssbCxsbtxxService znsbNssbCxsbtxxService;
//
//    @Resource
//    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;
//
    /**
     * znsb_nssb_yhscj(印花税采集)
     */
    @Resource
    private ZnsbNssbYhscjMapper znsbNssbYhscjMapper;
//    @Resource
//    private ZnsbNssbYhscjService znsbNssbYhscjService;
//
//    /**
//     * znsb_nssb_yhslfkbl(印花税采集可变列信息)
//     */
//    @Resource
//    private ZnsbNssbYhslfkblMapper znsbNssbYhslfkblMapper;
//    @Resource
//    private ZnsbNssbYhslfkblService znsbNssbYhslfkblService;
//
//    @Resource
//    private NsrxxApi nsrxxApi;
//
//    private final String YHSBC = "YHS-LFH-BC";
//
//    private final String YHSGZ = "YHS-LFH-GZ";
//
//    private final String YHSZF = "YHS-ZF";
//
//    /**
//     * 查询已经采集的税源信息  表头信息
//     *
//     * @param
//     * @return YhscjBtxxDTO
//     */
//    public YhsYcjsyxxResDTO queryYhsYcjxxNew(YhsCxSyxxReqDTO yhsCxSyxxReqDTO) {
//        YhsYcjsyxxResDTO yhsYcjsyxxResDTO = new YhsYcjsyxxResDTO();
//        String skssqq = "";
//        String skssqz = "";
//        String djxh = "";
//        List<YhscjNsrxxVO> yhscjNsrxxList = BeanUtils.toBean(yhsCxSyxxReqDTO.getYhscjNsrxxList(), YhscjNsrxxVO.class);
//        for (YhscjNsrxxVO yhscjNsrxxVO : yhscjNsrxxList) {
//            String nsqxDm = yhscjNsrxxVO.getNsqxDm();
//            djxh = yhscjNsrxxVO.getDjxh();
//            if ("11".equals(nsqxDm)) {
//                skssqq = yhscjNsrxxVO.getSkssqq();
//                skssqz = yhscjNsrxxVO.getSkssqz();
//                break;
//            }
//        }
//        //使用syuuid预览
//        final List<String> syuuid = yhsCxSyxxReqDTO.getSyuuid();
//        final String nsqxDmSbrw = yhsCxSyxxReqDTO.getNsqxDm();
//        //获取本地保存的印花税税源明细
//        List<YhshbcjxxbVO> yhshbcjxxbList = yhssycjService.queryYcjmxxxNew(BeanUtils.toBean(yhscjNsrxxList, YhscjNsrxxVO.class), syuuid, nsqxDmSbrw);
//        List<YhshbcjxxbDTO> yhshbcjxxbDTOList;
//        yhshbcjxxbDTOList = BeanUtils.toBean(yhshbcjxxbList, YhshbcjxxbDTO.class);
//        //预览页面直接返回，不构建认定数据
//        if (GyUtils.isNotNull(syuuid)) {
//            yhsYcjsyxxResDTO.setYhshbcjxxbList(yhshbcjxxbDTOList);
//            return yhsYcjsyxxResDTO;
//        }
//        //认定信息
//        List<YhshbcjxxbDTO> rdxxList = new ArrayList<>();
//        //预缴信息
//        List<SbYjxxJhDTO> sbYjxxJhList = new ArrayList<>();
//        //核定信息
//        List<YhshdDTO> yhshdList = new ArrayList<>();
//        List<String> sqList = new ArrayList<>();
//        //查询税费种认定
//        for (YhscjNsrxxVO yhscjNsrxxVO : yhscjNsrxxList) {
//            if ("11".equals(yhscjNsrxxVO.getNsqxDm()) || "11".equals(nsqxDmSbrw)) {
//                continue;
//            }
//            sqList.add(yhscjNsrxxVO.getSkssqq() + yhscjNsrxxVO.getSkssqz());
//            YhscjNsrxxDTO yhscjNsrxxDTO = new YhscjNsrxxDTO();
//            yhscjNsrxxDTO.setSkssqq(yhscjNsrxxVO.getSkssqq());
//            yhscjNsrxxDTO.setSkssqz(yhscjNsrxxVO.getSkssqz());
//            yhscjNsrxxDTO.setDjxh(yhscjNsrxxVO.getDjxh());
//            yhscjNsrxxDTO.setNsqxDm(yhscjNsrxxVO.getNsqxDm());
//            yhscjNsrxxDTO.setNsrsbh(yhscjNsrxxVO.getNsrsbh());
//            yhscjNsrxxDTO.setXzqhszDm(yhscjNsrxxVO.getXzqhszDm());
//            yhscjNsrxxDTO.setSbsxDm1("11");
//            //TODO 税费种认定--印花税税源采集初始化
//            //税费种认定--印花税税源采集初始化
//            YhsCjInitdataResDTO yhsrdxx = this.yhscjInitdata(yhscjNsrxxDTO, yhsCxSyxxReqDTO.getSjjg(), yhsCxSyxxReqDTO.getSjry());
//            if (!GyUtils.isNull(yhsrdxx.getYhscjCjxxbList())) {
//                rdxxList.addAll(BeanUtils.toBean(yhsrdxx.getYhscjCjxxbList(), YhshbcjxxbDTO.class));
//            }
//            if (!GyUtils.isNull(yhsrdxx.getSbYjxxJhList())) {
//                sbYjxxJhList.addAll(BeanUtils.toBean(yhsrdxx.getSbYjxxJhList(), SbYjxxJhDTO.class));
//            }
//            if (!GyUtils.isNull(yhsrdxx.getYhshdList())) {
//                yhshdList.addAll(BeanUtils.toBean(yhsrdxx.getYhshdList(), YhshdDTO.class));
//            }
////            final ClassPathResource resource = new ClassPathResource("/mock/yhs/yhscjInitData.json");
////            final YhsCjInitdataLqResDTO lqResDTO = JSONUtil.toBean(resource.readUtf8Str(), YhsCjInitdataLqResDTO.class);
////            if (!GyUtils.isNull(lqResDTO) && GyUtils.isEquals("00",lqResDTO.getReturncode())){
////                if (!GyUtils.isNull(lqResDTO.getYhscjCjxxbList())) {
////                    rdxxList.addAll(BeanUtils.toBean(lqResDTO.getYhscjCjxxbList(), YhshbcjxxbDTO.class));
////                }
////            }else{
////                YhsCjInitdataResDTO yhsrdxx = this.yhscjInitdata(yhscjNsrxxDTO, yhsCxSyxxReqDTO.getSjjg(), yhsCxSyxxReqDTO.getSjry());
////                if (!GyUtils.isNull(yhsrdxx.getYhscjCjxxbList())) {
////                    rdxxList.addAll(BeanUtils.toBean(yhsrdxx.getYhscjCjxxbList(), YhshbcjxxbDTO.class));
////                }
////                if (!GyUtils.isNull(yhsrdxx.getSbYjxxJhList())) {
////                    sbYjxxJhList.addAll(BeanUtils.toBean(yhsrdxx.getSbYjxxJhList(), SbYjxxJhDTO.class));
////                }
////                if (!GyUtils.isNull(yhsrdxx.getYhshdList())) {
////                    yhshdList.addAll(BeanUtils.toBean(yhsrdxx.getYhshdList(), YhshdDTO.class));
////                }
////            }
//        }
//        yhsYcjsyxxResDTO.setSbYjxxJhList(sbYjxxJhList);
//        yhsYcjsyxxResDTO.setYhshdList(yhshdList);
//        //yhsYcjsyxxResDTO.setYhssfzrdList(rdxxList);
//        //将按次的税种认定过滤掉
//        rdxxList = rdxxList.stream().filter(x -> !"11".equals(x.getNsqxDm())).collect(Collectors.toList());
//
//        for (YhshbcjxxbDTO rdxx : rdxxList) { //合并税费种认定信息
//            rdxx.setSbsxDm1("11");
//            rdxx.setSfsfzrd("Y");
//            rdxx.setSbqxlx("00");
//        }
//        List<String> zspmDmList = new ArrayList<>();
//        Map<String, Object> checkSfzrdcfcjMap = new HashMap<>();
//        for (YhshbcjxxbDTO yhshbcjxxbDTO : yhshbcjxxbDTOList) {
//            yhshbcjxxbDTO.setLrrq(yhshbcjxxbDTO.getLrrq().substring(0, 10));
//            yhshbcjxxbDTO.setSkssqq(yhshbcjxxbDTO.getSkssqq().substring(0, 10));
//            yhshbcjxxbDTO.setSkssqz(yhshbcjxxbDTO.getSkssqz().substring(0, 10));
//            yhshbcjxxbDTO.setYnspzsllsrq(yhshbcjxxbDTO.getYnspzsllsrq() == null ? "" : yhshbcjxxbDTO.getYnspzsllsrq().substring(0, 10));
//            yhshbcjxxbDTO.setSfsfzrd("N");
//            final String bczt = yhshbcjxxbDTO.getBczt();
//            //00:本地保存 01:上传成功 02:上传失败 03:业务校验中 04:业务校验成功 05:业务校验失败 06:算税数据保存成功
//            //07:算税数据保存失败 08：异常比对不通过 09:申报成功 10:申报失败 11:税源采集成功 12:税源采集失败
//            if ("00".equals(bczt) || "02".equals(bczt) || "05".equals(bczt) || "07".equals(bczt) || "08".equals(bczt)
//                    || "10".equals(bczt) || "12".equals(bczt)) {
//                yhshbcjxxbDTO.setSfxgbz("Y");
//                yhshbcjxxbDTO.setSfdeletebz("Y");
//            }
//            final String ycjpmzmDm = !GyUtils.isNull(yhshbcjxxbDTO.getZszmDm()) ? yhshbcjxxbDTO.getZspmDm() + yhshbcjxxbDTO.getZszmDm() : yhshbcjxxbDTO.getZspmDm();
//            if (!"11".equals(yhshbcjxxbDTO.getNsqxDm())) {
//                zspmDmList.add(ycjpmzmDm);
//            }
//            for (YhshbcjxxbDTO rdxx : rdxxList) { //判断是否可以删除  是否税费种认定
//                String zspmDm = rdxx.getZspmDm();
//                String zszmDm = rdxx.getZszmDm();
//                if (ycjpmzmDm.equals(!GyUtils.isNull(zszmDm) ? zspmDm + zszmDm : zspmDm)) {
//                    final String compmzmDm = (String) checkSfzrdcfcjMap.get(!GyUtils.isNull(zszmDm) ? zspmDm + zszmDm : zspmDm);
//                    if (GyUtils.isNull(compmzmDm)) {
//                        checkSfzrdcfcjMap.put(ycjpmzmDm, ycjpmzmDm);
//                        yhshbcjxxbDTO.setSfdeletebz("N");
//                    } else {
//                        yhshbcjxxbDTO.setSfdeletebz("Y");
//                    }
//                }
//            }
//            if ("11".equals(yhshbcjxxbDTO.getNsqxDm())) {
//                yhshbcjxxbDTO.setSfdeletebz("Y");
//            }
//            String sbqxlx = yhshbcjxxbDTO.getSbqxlx();
//            String zbuuid = yhshbcjxxbDTO.getZbuuid();
//            for (int i = yhshbcjxxbDTOList.size() - 1; i >= 0; i--) { //判断一个税源中是否存在跨申报期限的数据
//                YhshbcjxxbDTO uyhshbcjxxbDTO = yhshbcjxxbDTOList.get(i);
//                String usbqxlx = uyhshbcjxxbDTO.getSbqxlx();
//                String uzbuuid = uyhshbcjxxbDTO.getZbuuid();
//                if (zbuuid.equals(uzbuuid) && !sbqxlx.equals(usbqxlx)) {
//                    yhshbcjxxbDTO.setSfksbqxlxbz("Y");
//                    uyhshbcjxxbDTO.setSfksbqxlxbz("Y");
//                }
//            }
//        }
//        yhscjNsrxxList.sort(Comparator.comparing(YhscjNsrxxVO::getSkssqq));//根据属期起排序
//        List<YhshbcjxxbVO> yhshbcjxxbVOList = new ArrayList<>();
//        if (GyUtils.isNotNull(yhscjNsrxxList)) {
//            String ysbskssqq = yhscjNsrxxList.get(0).getSkssqq();
//            String ysbskssqz = yhscjNsrxxList.get(0).getSkssqz();
//            //TODO 乐企接口？
//            //根据djxh skssqq skssqz查询立法后已经申报税源
//            yhshbcjxxbVOList = this.queryYhslfysbsymx(djxh, ysbskssqq, ysbskssqz);
//        }
////        yhsJ3cxApi.queryYhslfysbsymx(djxh, ysbskssqq, ysbskssqz);
//        List<String> ysbZspmList = new ArrayList<>();
//        if (!GyUtils.isNull(yhshbcjxxbVOList)) {
//            for (YhshbcjxxbVO yhshbcjxxbVO : yhshbcjxxbVOList) {
//                if (!"11".equals(yhshbcjxxbVO.getNsqxDm())) {
//                    String sqstr = yhshbcjxxbVO.getSkssqq().substring(0, 10) + yhshbcjxxbVO.getSkssqz().substring(0, 10);
//                    if ("101110502".equals(yhshbcjxxbVO.getZspmDm())
//                            && (sqstr.startsWith("2022") || sqstr.startsWith("2023"))
//                            && "10".equals(yhshbcjxxbVO.getNsqxDm())) {
//                        sqstr = yhshbcjxxbVO.getSkssqq().substring(0, 4) + "-01-01" + yhshbcjxxbVO.getSkssqz().substring(0, 10);
//                    }
//                    if (sqList.contains(sqstr)) {
//                        ysbZspmList.add(!GyUtils.isNull(yhshbcjxxbVO.getZszmDm()) ? yhshbcjxxbVO.getZspmDm() + yhshbcjxxbVO.getZszmDm() : yhshbcjxxbVO.getZspmDm());
//                    }
//                }
//            }
//        }
//        if (!GyUtils.isNull(rdxxList)) {
//            for (YhshbcjxxbDTO rdxx : rdxxList) { //合并税费种认定信息
//                String zspmDm = rdxx.getZspmDm();
//                String zszmDm = rdxx.getZszmDm();
//                String rdpmzmDm = !GyUtils.isNull(zszmDm) ? zspmDm + zszmDm : zspmDm;
//                if (!zspmDmList.contains(rdpmzmDm) && !ysbZspmList.contains(rdpmzmDm) && !"101110501".equals(zspmDm)) {
//                    rdxx.setSfdeletebz("N");
//                    rdxx.setYnspzsllsrq(rdxx.getSkssqz());
//                    yhshbcjxxbDTOList.add(rdxx);
//                }
//            }
//        }
////        for (YhscjCjxxbDTO yhscjCjxxbDTO : yhscjCjxxbDTOList) {
////            yhscjCjxxbDTO.setLrrq(yhscjCjxxbDTO.getLrrq().substring(0, 10));
////            yhscjCjxxbDTO.setSkssqq(yhscjCjxxbDTO.getSkssqq().substring(0, 10));
////            yhscjCjxxbDTO.setSkssqz(yhscjCjxxbDTO.getSkssqz().substring(0, 10));
////            yhscjCjxxbDTO.setYnspzsllsrq(yhscjCjxxbDTO.getYnspzsllsrq() == null ? "" : yhscjCjxxbDTO.getYnspzsllsrq().substring(0, 10));
////            yhscjCjxxbDTO.setSfsfzrd("N");
////            yhscjCjxxbDTO.setSfdeletebz("Y");
////        }
//        yhsYcjsyxxResDTO.setYhshbcjxxbList(yhshbcjxxbDTOList);
////        yhsYcjsyxxResDTO.setYhscjxxList(yhscjCjxxbDTOList);
//        return yhsYcjsyxxResDTO;
//    }
//
//    /**
//     * 查有效的印花税采集税源明细信息  新版 当前按期的和往期按次包含旧版往期按次的
//     *
//     * @param
//     * @param yhscjNsrxxList syuuidList
//     * @return
//     */
//    public List<YhshbcjxxbVO> queryYcjmxxxNew(List<YhscjNsrxxVO> yhscjNsrxxList, List<String> syuuidList, String nsqxDmSbrw) {
//        List<YhshbcjxxbVO> yhshbcjxxbList = new ArrayList<>();
//        String acskssqq = "";
//        String acskssqz = "";
//        String djxh = "";
//        String skssqq = "";
//        String skssqz = "";
//        String nsqxDm = "";
//        final StringBuffer nsqxstr = new StringBuffer();
//        if (yhscjNsrxxList != null && yhscjNsrxxList.size() == 0) { //纳税人信息传过来为空,直接返回
//            return yhshbcjxxbList;
//        }
//        yhscjNsrxxList.sort(Comparator.comparing(YhscjNsrxxVO::getSkssqq));//根据属期起排序
//        skssqq = yhscjNsrxxList.get(0).getSkssqq();
//        skssqz = yhscjNsrxxList.get(0).getSkssqz();
//        List<String> sqList = new ArrayList<>();
//        List<String> nsqxdmList = new ArrayList<>();
//        for (YhscjNsrxxVO yhscjNsrxxVO : yhscjNsrxxList) {
//            djxh = yhscjNsrxxVO.getDjxh();
//            if (!"11".equals(yhscjNsrxxVO.getNsqxDm())) {
//                if (nsqxDmSbrw.equals(yhscjNsrxxVO.getNsqxDm())) {
//                    nsqxdmList.add(yhscjNsrxxVO.getNsqxDm());
//                }
//                sqList.add(yhscjNsrxxVO.getSkssqq() + yhscjNsrxxVO.getSkssqz());
//            } else {
//                acskssqq = yhscjNsrxxVO.getSkssqq();
//                acskssqz = yhscjNsrxxVO.getSkssqz();
//            }
//        }
//        if (GyUtils.isNull(sqList) && !"11".equals(nsqxDmSbrw)) {
//            return yhshbcjxxbList;
//        }
//
//        yhshbcjxxbList = znsbNssbYhscjMapper.queryYhslfYcjmxxx(djxh, skssqq, skssqz, nsqxdmList, acskssqq, syuuidList, nsqxDmSbrw);//新版查询按期税源信息
//        for (int i = yhshbcjxxbList.size() - 1; i >= 0; i--) {
//            YhshbcjxxbVO yhshbcjxxbVO = yhshbcjxxbList.get(i);
//            if (!"11".equals(yhshbcjxxbVO.getNsqxDm())) {
//                String sqstr = yhshbcjxxbVO.getSkssqq().substring(0, 10) + yhshbcjxxbVO.getSkssqz().substring(0, 10);
//                if ("101110502".equals(yhshbcjxxbVO.getZspmDm())
//                        && (sqstr.startsWith("2022") || sqstr.startsWith("2023"))
//                        && "10".equals(yhshbcjxxbVO.getNsqxDm())) {
//                    sqstr = yhshbcjxxbVO.getSkssqq().substring(0, 4) + "-01-01" + yhshbcjxxbVO.getSkssqz().substring(0, 10);
//                }
//                if (!sqList.contains(sqstr)) {
//                    yhshbcjxxbList.remove(i);
//                    continue;
//                }
//            }
//        }
//        if (!yhshbcjxxbList.isEmpty()) {
//
//            List<YhscjkblxxVO> yhscjkblxxList = new ArrayList<>();
//
//            List<List<YhshbcjxxbVO>> partition = ListUtils.partition(yhshbcjxxbList, 900);//分片
//            for (List<YhshbcjxxbVO> yhzbidList : partition) {
//                yhscjkblxxList.addAll(znsbNssbYhscjMapper.queryYhskbl(yhzbidList));
//            }
//
//
//            for (YhscjkblxxVO yhscjkblxxVO : yhscjkblxxList) {
//                yhscjkblxxVO.setTimebz(yhscjkblxxVO.getSyuuid());
//            }
//            for (YhshbcjxxbVO yhshbcjxxbVO : yhshbcjxxbList) {
//                List<YhscjkblxxVO> yhscjkblxxVOList = new ArrayList<>();
//                String uuid = yhshbcjxxbVO.getUuid();
//                for (YhscjkblxxVO yhscjkblxxVO : yhscjkblxxList) {
//                    if (uuid.equals(yhscjkblxxVO.getSyuuid())) {
//                        yhscjkblxxVOList.add(yhscjkblxxVO);
//                    }
//                }
//                yhshbcjxxbVO.setYhscjkblxxList(yhscjkblxxVOList);
//            }
//        }
//        return yhshbcjxxbList;
//    }
//
//    /**
//     * 印花税采集载入数据  新增页面  获取税费种认定 核定信息
//     *
//     * @param yhscjNsrxxDTO
//     * @return YhscjBtxxDTO
//     */
//    public YhsCjInitdataResDTO yhscjInitdata(YhscjNsrxxDTO yhscjNsrxxDTO, String sjjg, String sjrry) throws ServiceException {
//        YhsCjInitdataResDTO yhsCjInitdataResDTO = new YhsCjInitdataResDTO();
//        final Date dbrq = DateUtils.toDate("2022-07-01", "yyyy-MM-dd");
//        if (DateUtils.toDate(yhscjNsrxxDTO.getSkssqz(), "yyyy-MM-dd").compareTo(dbrq) >= 0) {
//            //根据本地税费种认定组装印花税税源初始化数据
//            List<YhscjCjxxbDTO> yhsCjxxVOS = this.yhscjInitData(yhscjNsrxxDTO, "N");
//            log.info("本地表获取印花税税源初始化数据：{}", yhsCjxxVOS);
//            yhsCjInitdataResDTO.setYhscjCjxxbList(yhsCjxxVOS);
////            //调用乐企接口获取印花税税源初始化数据
////            yhsCjInitdataResDTO = this.yhscjInitFromLqData(yhscjNsrxxDTO);
////            log.info("调用乐企接口获取印花税税源初始化数据：{}",yhsCjInitdataResDTO);
////            if (GyUtils.isNull(yhsCjInitdataResDTO)){
////
////            }
//        }
//        return yhsCjInitdataResDTO;
//    }
//
//    public YhsCjInitdataResDTO yhscjInitFromLqData(YhscjNsrxxDTO yhscjNsrxxDTO) {
//        final YhsCjInitdataResDTO yhsCjInitdataResDTO = new YhsCjInitdataResDTO();
//        //调用乐企接口获取印花税税源采集初始化信息
//        final LqYhssycjInitRequestDTO requestDTO = BeanUtils.toBean(yhscjNsrxxDTO, LqYhssycjInitRequestDTO.class);
//        requestDTO.setSbsxDm1("11");
//        requestDTO.setCfcjCheck("N");
//        //通过数据交换服务调用乐企接口
//        final String ywbw = JsonUtils.toJson(requestDTO);
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("CX00000001");
//        sjjhDTO.setYwbm(YzpzzlEnum.YHS.getDm());
//        sjjhDTO.setYwuuid(GyUtils.getUuid());
//        sjjhDTO.setDjxh(yhscjNsrxxDTO.getDjxh());
//        sjjhDTO.setNsrsbh(yhscjNsrxxDTO.getNsrsbh());
//        sjjhDTO.setXzqhszDm(yhscjNsrxxDTO.getXzqhszDm());
//        sjjhDTO.setBwnr(ywbw);
//        CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
//        if (GyUtils.isNotNull(result) && GyUtils.isNotNull(result.getData())) {
//            log.info("乐企返回印花税税源初始化接口报文{}", result);
//            final LqYhssycjInitResponseDTO respDTO = JsonUtils.toBean((String) result.getData(), LqYhssycjInitResponseDTO.class);
//            if ("00".equals(respDTO.getReturncode())) {
//                final List<YhscjCjxxbDTO> yhscjCjxxbDTOList = respDTO.getList();
//                yhsCjInitdataResDTO.setYhscjCjxxbList(BeanUtils.toBean(yhscjCjxxbDTOList, YhscjCjxxbDTO.class));
//                yhsCjInitdataResDTO.setYhsSfzrdxxbList(BeanUtils.toBean(yhscjCjxxbDTOList, YhsSfzrdxxbDTO.class));
//            } else {
//                log.error("调用乐企印花税税源初始化接口失败：{}", respDTO.getReturnmsg());
//            }
//        }
//        return yhsCjInitdataResDTO;
//    }
//
//
//    /**
//     * @param nsrxxFormVO 纳税人信息
//     * @param cfcjCheck   重复采集监控
//     * @return String
//     * @throws Exception 通用架构异常
//     * @name 印花税采集载入数据
//     * @description 印花税采集载入数据
//     * @time 创建时间:2020-07-17 上午 10:44:45
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
////    @Service(serviceName = "SWZJ.HXZG.SB.YHSLFCJINITDATA", cacheValue = false, memo = "业务服务：新印花税采集载入数据")
//    public List<YhscjCjxxbDTO> yhscjInitData(YhscjNsrxxDTO nsrxxFormVO, String cfcjCheck) throws ServiceException {
//        //先确认此期是否采集过
//        if (!"N".equals(cfcjCheck)) {
//            checkSfcj(nsrxxFormVO);
//        }
//        //入参
//        final String djxh = nsrxxFormVO.getDjxh();
//        final String skssqq = nsrxxFormVO.getSkssqq();
//        final String skssqz = nsrxxFormVO.getSkssqz();
//        final String nsqxDm = nsrxxFormVO.getNsqxDm();
//        final String nsrsbh = nsrxxFormVO.getNsrsbh();
//        //先获取认定(按次采集不获取)
//        final List<SfzrdmxxxVO> rdsfzrdxxbVOList = this.queryNsrSfzrdxxForYhs(djxh, nsrsbh, skssqq, skssqz, nsqxDm);
//        final List<YhscjCjxxbDTO> yhsCjxxVOS = new ArrayList<>();
//        if (!GyUtils.isNull(rdsfzrdxxbVOList)) {
//            for (SfzrdmxxxVO rdsfzrdxxbVO : rdsfzrdxxbVOList) {
//                final YhscjCjxxbDTO yhsCjxxVO = packageCjxx(rdsfzrdxxbVO, nsrxxFormVO);
//                yhsCjxxVOS.add(yhsCjxxVO);
//            }
//        }
//        return yhsCjxxVOS;
//    }
//
//    /**
//     * @param nsrxxFormVO 纳税人信息
//     * @param cfcjCheck   重复采集监控
//     * @return String
//     * @throws SwordBaseCheckedException 通用架构异常
//     * @name 印花税采集载入数据
//     * @description 印花税采集载入数据
//     * @time 创建时间:2020-07-17 上午 10:44:45
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
////    @Service(serviceName = "SWZJ.HXZG.SB.YHSCJINITDATA", cacheValue = false, memo = "业务服务：印花税采集载入数据")
////    public Object[] yhscjInitData(YhscjNsrxx nsrxxFormVO, String cfcjCheck) throws SwordBaseCheckedException {
////        //先确认此期是否采集过
////        if (!"N".equals(cfcjCheck)) {
////            checkSfcj(nsrxxFormVO);
////        }
////        //入参
////        final String djxh = nsrxxFormVO.getDjxh();
////        final String skssqq = nsrxxFormVO.getSkssqq();
////        final String skssqz = nsrxxFormVO.getSkssqz();
////        final String nsqxDm = nsrxxFormVO.getNsqxDm();
////        final String yzpzzlDm = "BDA0610794";
////        final String swjgDm = HxzgSessionUtils.getswjgdm();
////        //先获取认定(按次采集不获取)
////        final List<RDSfzrdxxbVO> rdsfzrdxxbVOList = queryNsrSfzrdxxForYhs(djxh, skssqq, skssqz, nsqxDm);
////        final List<YhscjCjxxbDTO> yhsCjxxVOS = new ArrayList<YhscjCjxxbDTO>();
////        if (!HxzgGyUtils.isNull(rdsfzrdxxbVOList)) {
////            for (RDSfzrdxxbVO rdsfzrdxxbVO : rdsfzrdxxbVOList) {
////                final YhscjCjxxb yhsCjxxVO = packageCjxx(rdsfzrdxxbVO, nsrxxFormVO);
////                yhsCjxxVOS.add(yhsCjxxVO);
////            }
////        }
////        //再获取核定信息
////        final List<ZsHdYhshdJgVO> yhshdList = this.getYhshdjgxx(djxh, skssqq, skssqz);
////        //根据核定信息,处理认定信息(当和认定不同时,以核定为准)
////        final List<YhscjCjxxbDTO> yhscjCjxxbs = handleRdxxWithHdxx(yhsCjxxVOS, yhshdList,nsqxDm,skssqq,skssqz);
////        //将上面获取的相关数据信息组装为返回参数
////        final Object[] res = new Object[3];
////        res[0] = yhscjCjxxbs;
////        //印花税核定情况
////        if (!HxzgGyUtils.isNull(yhshdList)) {
////            final List<YhshdVO> yhshdGridlb = SwordTypeUtils.copyBeans(yhshdList, YhshdVO.class);
////            res[1] = yhshdGridlb;
////        }
////        //查询预缴信息
////        final List<SBYjxxJhVO> yjxxList = getYjxxList(nsrxxFormVO);
////        res[2] = yjxxList;
////        return res;
////    }
//
//    /**
//     * @param djxh   登记序号
//     * @param skssqq 所属期起
//     * @param skssqz 所属期止
//     * @param nsqxDm 纳税期限
//     * @return 认定信息
//     * @throws Exception e
//     * @name 中文名称
//     * @description 相关说明
//     * @time 创建时间
//     * <AUTHOR>  张薇
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public List<SfzrdmxxxVO> queryNsrSfzrdxxForYhs(String djxh, String nsrsbh, String skssqq, String skssqz, String nsqxDm) throws ServiceException {
//        final List<SfzrdmxxxVO> newRdxxList = new ArrayList<>();
//        if ("11".equals(nsqxDm)) {
//            return newRdxxList;
//        }
//        final ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
//        reqVO.setDjxh(djxh);
//        reqVO.setNsrsbh(nsrsbh);
//        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVO = nsrxxApi.getNsrxxByNsrsbh(reqVO);
//        log.info("--该纳税人查询纳税人信息返回为空！---{}", nsrsbh);
//        if (GyUtils.isNull(nsrxxVO)) {
//            return newRdxxList;
//        }
//        final List<SfzrdmxxxVO> rdsfzrdxxbVOList = nsrxxVO.getData().getSfzrdmxxx();
//        log.info("--queryNsrSfzrdxxForYhs---{}", JsonUtils.toJsonList(rdsfzrdxxbVOList));
//        final Date dskssqq = DateUtils.toDate(skssqq, "yyyy-MM-dd");
//        final Date dskssqz = DateUtils.toDate(skssqz, "yyyy-MM-dd");
//        // 判断是月季度还是年度申报
//        final int[] skssqqArray = GyUtils.getYearMonthDayHH24MiMM(dskssqq);
//        final int[] skssqzArray = GyUtils.getYearMonthDayHH24MiMM(dskssqz);
//        final int year = skssqqArray[0];
//        final int month = skssqqArray[1];
//        final int qzMonth = skssqzArray[1];
//        // 区分是否年报
//        boolean ansbFlag = false;
//        if (month == 1 && qzMonth == 12) {
//            ansbFlag = true;
//        }
//        for (SfzrdmxxxVO rdxxVO : rdsfzrdxxbVOList) {
//            final Date rdyxqq = rdxxVO.getRdyxqq();
//            final Date rdyxqz = rdxxVO.getRdyxqz();
//            final String rdnsqxDm = rdxxVO.getNsqxDm();
//            //日期包含且nsqxDm要为指定的nsqxDm
//            if ("10111".equals(rdxxVO.getZsxmDm()) && "101110502".equals(rdxxVO.getZspmDm()) && "10".equals(rdnsqxDm) && ansbFlag && (year == 2022 || year == 2023)) {
//                final String nd = DateUtils.dateToString(rdxxVO.getRdyxqq()).substring(0, 4);//无论是什么格式，先截取前四位
//                final int intNd = Integer.parseInt(nd);
//                if (intNd == year) {
//                    if (rdyxqq.compareTo(dskssqq) >= 0 && rdyxqz.compareTo(dskssqq) >= 0) {
//                        //认定有效期起止包含 所属期起止
//                        rdxxVO.setRdyxqq(dskssqq);
//                        rdxxVO.setRdyxqz(dskssqz);
//                        newRdxxList.add(rdxxVO);
//                    }
//                } else if (year > intNd) {
//                    if (dskssqq.compareTo(rdyxqq) >= 0 && rdyxqz.compareTo(dskssqz) >= 0) {
//                        //认定有效期起止包含 所属期起止
//                        rdxxVO.setRdyxqq(dskssqq);
//                        rdxxVO.setRdyxqz(dskssqz);
//                        newRdxxList.add(rdxxVO);
//                    }
//                }
//
//            } else {
//                if ("10111".equals(rdxxVO.getZsxmDm()) && dskssqq.compareTo(rdyxqq) >= 0 && dskssqz.compareTo(rdyxqz) <= 0
//                        && (GyUtils.isNull(nsqxDm) || nsqxDm.equals(rdnsqxDm))) {
//                    //认定有效期起止包含 所属期起止
//                    rdxxVO.setRdyxqq(dskssqq);
//                    rdxxVO.setRdyxqz(dskssqz);
//                    newRdxxList.add(rdxxVO);
//                }
//            }
//        }
//        return newRdxxList;
//    }
//
//
//    /**
//     * @param nsrxxFormVO 纳税人信息
//     * @param rdxxVO      认定信息
//     * @return String
//     * @throws Exception 通用架构异常
//     * @name 组装采集信息
//     * @description 组装采集信息
//     * @time 创建时间:2020-07-17 上午 10:44:45
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private YhscjCjxxbDTO packageCjxx(SfzrdmxxxVO rdxxVO, YhscjNsrxxDTO nsrxxFormVO) throws ServiceException {
//        final YhscjCjxxbDTO yhsCjxxVO = BeanUtils.toBean(rdxxVO, YhscjCjxxbDTO.class);
//        yhsCjxxVO.setSkssqq(DateUtils.dateToString(rdxxVO.getRdyxqq()));
//        yhsCjxxVO.setSl1(rdxxVO.getSlhdwse().doubleValue());
//        yhsCjxxVO.setSkssqz(DateUtils.dateToString(rdxxVO.getRdyxqz()));
//        return yhsCjxxVO;
//    }
//
//    /**
//     * 根据djxh skssqq skssqz查询立法后已经申报税源
//     *
//     * @param djxh
//     * @param skssqq
//     * @param skssqz
//     * @return
//     */
//    public List<YhshbcjxxbVO> queryYhslfysbsymx(String djxh, String skssqq, String skssqz) {
//        final List<YhshbcjxxbVO> yhshbcjxxbList = znsbNssbYhscjMapper.queryYhslfysbsymx(djxh, skssqq, skssqz);
//        return yhshbcjxxbList;
//    }
////
//    /**
//     * 错误更正  单税源明细更正
//     *
//     * @param yhsCjCwgzReqDTO
//     * @return
//     */
////    public YhsCjCwgzResDTO dsyCwgzNew(YhsCjCwgzReqDTO yhsCjCwgzReqDTO) {
////        YhsCjCwgzResDTO yhsCjCwgzResDTO = new YhsCjCwgzResDTO();
////        HashMap<String, String> expand = new HashMap<>();
////        //技术报文头中的sjjg
////        expand.put("sjjg", yhsCjCwgzReqDTO.getSjjg());
////        YhscjNsrxxDTO yhscjNsrxx =BeanUtils.toBean(yhsCjCwgzReqDTO.getYhscjNsrxx(), YhscjNsrxxDTO.class);
////        String zbuuid = yhsCjCwgzReqDTO.getZbuuid();
////        final Date dbrq = DateUtils.toDate("2022-07-01","yyyy-MM-dd");
////        if (DateUtils.toDate(yhscjNsrxx.getSkssqz(),"yyyy-MM-dd").compareTo(dbrq) >= 0) { //2022-07-01后使用新版印花税更正
////            //查询已采集明细信息  二次初始化
////            HXZGSB11052Request hxzgsb11052Request = new HXZGSB11052Request();
////            hxzgsb11052Request.setUuid(zbuuid);
////            hxzgsb11052Request.setNsrmc(yhscjNsrxx.getNsrmc());
////            hxzgsb11052Request.setNsrsbh(yhscjNsrxx.getNsrsbh());
////            HXZGSB11052Response hxzgsb11052Response = gt3Invoker.invoke("SWZJ.HXZG.SB.YHSCWGZCJINIT", expand, hxzgsb11052Request, HXZGSB11052Response.class);
////            List<YhscjkblxxDTO> ycjyhscjkblxxList = new ArrayList<>();//已采集可变列表
////            List<YhshbcjxxbDTO> ycjyhshbcjxxbList = new ArrayList<>();//采集表主要信息
////            ycjyhscjkblxxList.addAll(hxzgsb11052Response.getKblGrid().getKblGridlb());
////            ycjyhshbcjxxbList.addAll(hxzgsb11052Response.getAcsbGrid().getAcsbGridlb());
////            //需要更正的税源信息和更正的可变列  前台传入
////            List<YhshbcjxxbDTO> yhsgzcjxxList = BeanUtils.toBean(yhsCjCwgzReqDTO.getYhshbcjxxbList(), YhshbcjxxbDTO.class);
////            YhshbcjxxbDTO yhshbcjxxbDTO = yhsCjCwgzReqDTO.getYhshbcjxxbList().get(0);
////            List<YhscjkblxxDTO> yhscjkblxxList = BeanUtils.toBean(yhshbcjxxbDTO.getYhscjkblxxList(), YhscjkblxxDTO.class);
////            String gzuuid = "";
////            for (YhshbcjxxbDTO yhshbcjxxb : ycjyhshbcjxxbList) {
////                String uuid = yhshbcjxxb.getUuid();
////                for (YhshbcjxxbDTO gzyhshbcjxxb : yhsgzcjxxList) {
////                    gzuuid = gzyhshbcjxxb.getUuid();
////                    if (uuid.equals(gzuuid)) {
////                        yhshbcjxxb.setSjjsje(gzyhshbcjxxb.getSjjsje());
////                        yhshbcjxxb.setJsjehjs(gzyhshbcjxxb.getJsjehjs());
////                        yhshbcjxxb.setYnse(gzyhshbcjxxb.getYnse());
////                        yhshbcjxxb.setJmse(gzyhshbcjxxb.getJmse());
////                        yhshbcjxxb.setSsjmxzDm(gzyhshbcjxxb.getSsjmxzDm());
////                    }
////                }
////            }
////            //过滤可变列
////            for (int i = ycjyhscjkblxxList.size() - 1; i >= 0; i--) {
////                YhscjkblxxDTO yhscjkblxx = ycjyhscjkblxxList.get(i);
////                String timebz = yhscjkblxx.getTimebz();
////                if (GyUtils.isNull(timebz)) {
////                    continue;
////                }
////                if (timebz.equals(gzuuid)) {
////                    ycjyhscjkblxxList.remove(i);
////                }
////
////            }
////            ycjyhscjkblxxList.addAll(yhscjkblxxList);
////            //保存更正
////            HXZGSB11051Request hxzgsb11051Request = new HXZGSB11051Request();
////            HXZGSB11051Request.CjkblxxGrid cjkblxxGrid = new HXZGSB11051Request.CjkblxxGrid();
////            HXZGSB11051Request.InsertCjxxGrid insertCjxxGrid = new HXZGSB11051Request.InsertCjxxGrid();
////            HXZGSB11051Request.UpdateCjxxGrid updateCjxxGrid = new HXZGSB11051Request.UpdateCjxxGrid();
////            HXZGSB11051Request.DeleteCjxxGrid deleteCjxxGrid = new HXZGSB11051Request.DeleteCjxxGrid();
////            updateCjxxGrid.getUpdateCjxxGridlb().addAll(ycjyhshbcjxxbList);
////            if (!GyUtils.isNull(ycjyhscjkblxxList)) {
////                cjkblxxGrid.getCjkblxxGridlb().addAll(ycjyhscjkblxxList);
////            }
////            hxzgsb11051Request.setZbuuid(zbuuid);
////            hxzgsb11051Request.setNsrxxFormVO(yhscjNsrxx);
////            hxzgsb11051Request.setCjkblxxGrid(cjkblxxGrid);
////            hxzgsb11051Request.setUpdateCjxxGrid(updateCjxxGrid);
////            hxzgsb11051Request.setInsertCjxxGrid(insertCjxxGrid);
////            hxzgsb11051Request.setDeleteCjxxGrid(deleteCjxxGrid);
////            HXZGSB11051Response hxzgsb11051Response = gt3Invoker.invoke("SWZJ.HXZG.SB.YHSLFCJCWGZ", expand, hxzgsb11051Request, HXZGSB11051Response.class);
////            if (hxzgsb11051Response != null) {
////                //String resmsg = updateYhslfcjxx(yhsgzcjxxList,yhscjkblxxList, null, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjry(), yhscjNsrxx.getDjxh(),yhsCjCwgzReqDTO.getYwqdDm());
////                yhsCjCwgzResDTO.setResmsg("更正成功");
////                yhsCjCwgzResDTO.setRescode("1001");
////                return yhsCjCwgzResDTO;
////            }
////        } else {
////            //查询已采集明细信息  二次初始化
////            HXZGSB10770Request hxzgsb10770Request = new HXZGSB10770Request();
////            hxzgsb10770Request.setUuid(zbuuid);
////            hxzgsb10770Request.setNsrmc(yhscjNsrxx.getNsrmc());
////            hxzgsb10770Request.setNsrsbh(yhscjNsrxx.getNsrsbh());
////            HXZGSB10770Response hxzgsb10770Response = gt3Invoker.invoke("SWZJ.HXZG.SB.YHSECCJINIT", expand, hxzgsb10770Request, HXZGSB10770Response.class);
////            //已经采集的税源明细
////            List<YhscjCjxxbDTO> yhsycjxxList = new ArrayList<>();
////            yhsycjxxList.addAll(hxzgsb10770Response.getAcsbGrid().getAcsbGridlb());
////            yhsycjxxList.addAll(hxzgsb10770Response.getAqsbGrid().getAqsbGridlb());
////            //需要更正的税源信息
////            List<YhscjCjxxbDTO> yhsgzcjxxList = new ArrayList<>();
////            yhsgzcjxxList = BeanUtils.toBean(yhsCjCwgzReqDTO.getYhscjCjxxbList(), YhscjCjxxbDTO.class);
////            //更新需要更正的税源信息
////            for (YhscjCjxxbDTO yhscjCjxxb : yhsycjxxList) {
////                String uuid = yhscjCjxxb.getUuid();
////                for (YhscjCjxxbDTO yhsgzcjCjxxb : yhsgzcjxxList) {
////                    String gzuuid = yhsgzcjCjxxb.getUuid();
////                    if (uuid.equals(gzuuid) && GyUtils.isNotNull(gzuuid)) {
////                        yhscjCjxxb.setJsjehjs(yhsgzcjCjxxb.getJsjehjs());
////                        yhscjCjxxb.setJmse(yhsgzcjCjxxb.getJmse());
////                        yhscjCjxxb.setYjse(yhsgzcjCjxxb.getYjse());
////                        yhscjCjxxb.setYnse(yhsgzcjCjxxb.getYnse());
////                        yhscjCjxxb.setSsjmxzDm(yhsgzcjCjxxb.getSsjmxzDm());
////                    }
////                }
////            }
////            final LqYhsCwgzRequestVO lqYhsCwgzRequestVO = new LqYhsCwgzRequestVO();
////            lqYhsCwgzRequestVO.setNsrxxFormVO(yhscjNsrxx);
////            lqYhsCwgzRequestVO.setUpdateCjxxGrid(yhsycjxxList);//更正只需要传入update
////            lqYhsCwgzRequestVO.setZbuuid(zbuuid);
////            //TODO 乐企保存错误更正
////            LqYhsCwgzResponseVO lqYhsCwgzResponseVO = new LqYhsCwgzResponseVO();//gt3Invoker.invoke("SWZJ.HXZG.SB.YHSCJBCCWGZ", expand, hxzgsb10769Request, HXZGSB10769Response.class);
////            if (GyUtils.isNotNull(lqYhsCwgzResponseVO) && "00".equals(lqYhsCwgzResponseVO.getReturncode())) {
////                //String resmsg=updateYhslfcjxx(null,null, yhsgzcjxxList, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjry(), yhscjNsrxx.getDjxh(),yhsCjCwgzReqDTO.getYwqdDm());
////                yhsCjCwgzResDTO.setResmsg("更正成功");
////                yhsCjCwgzResDTO.setRescode("1001");
////                return yhsCjCwgzResDTO;
////            }
////        }
////        yhsCjCwgzResDTO.setResmsg("-1");
////        yhsCjCwgzResDTO.setResmsg("更正失败");
////        return yhsCjCwgzResDTO;
////    }
////
////
//
//    /**
//     * 作废已经采集的税源明细
//     *
//     * @param yhsZfcjxxReqDTO
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public String zfYhYcjmxNew(YhsZfcjxxReqDTO yhsZfcjxxReqDTO) {
//        String rescode = "N";
//        YhscjNsrxxDTO yhscjNsrxx = new YhscjNsrxxDTO();
//        //技术报文头中的sjjg
//        String skssqq = yhsZfcjxxReqDTO.getSkssqq();
//        String skssqz = yhsZfcjxxReqDTO.getSkssqz();
//        String deletezbuuid = yhsZfcjxxReqDTO.getZbuuid();
//        String mxuuid = yhsZfcjxxReqDTO.getMxuuid();
//        String nsrsbh = yhsZfcjxxReqDTO.getNsrsbh();
//        String xzqhszDm = yhsZfcjxxReqDTO.getXzqhszDm();
//        List<YhscjBtxxDTO> yhscjBtxxList = new ArrayList<>();
//        YhscjBtxxDTO yhsbtxx = new YhscjBtxxDTO();
//        yhsbtxx.setUuid(deletezbuuid);
//        yhscjBtxxList.add(yhsbtxx);
//        yhscjNsrxx.setDjxh(yhsZfcjxxReqDTO.getDjxh());
//        yhscjNsrxx.setSkssqq(skssqq);
//        yhscjNsrxx.setSkssqz(skssqz);
//        yhscjNsrxx.setNsrsbh(yhsZfcjxxReqDTO.getNsrsbh());
//        final Date dbrq = GyUtils.cast2Date("2022-07-01");
//        if (GyUtils.cast2Date(skssqz).compareTo(dbrq) >= 0) { //2022-07-01后使用新版印花税更正  属期止在2022.07.01号后的
////            gt3Invoker.invoke("SWZJ.HXZG.SB.YHSCWGZCJINIT", expand, hxzgsb11052Request, HXZGSB11052Response.class);//二次采集初始化
//            //TODO 乐企错误更正采集初始化
//            List<ZnsbNssbYhscjDO> yhshbcjxxbList = znsbNssbYhscjMapper.queryyhsycjxxbyuuid(yhsbtxx.getUuid());
//            log.info("---yhshbcjxxbList---{}", JsonUtils.toJson(yhshbcjxxbList));
//            if (yhshbcjxxbList.size() > 1) { //调用更正
////                final List<String> zbuuid = yhshbcjxxbList.stream().map(ZnsbNssbYhscjDO::getUuid);
//                final List<ZnsbNssbYhslfkblDO> znsbNssbYhslfkblDOList = znsbNssbYhslfkblMapper.queryycjxxkblbyzbuuid(yhsbtxx.getUuid());
//                log.info("---znsbNssbYhslfkblDOList---{}", JsonUtils.toJson(znsbNssbYhslfkblDOList));
//                List<YhscjkblxxDTO> ycjyhscjkblxxList = BeanUtils.toBean(znsbNssbYhslfkblDOList, YhscjkblxxDTO.class);//已采集可变列表
////                ycjyhscjkblxxList.addAll(hxzgsb11052Response.getKblGrid().getKblGridlb());
//                //需要删除的税源信息  前台传入
//                List<YhshbcjxxbDTO> deletecjxxList = new ArrayList<>();
//                List<YhscjkblxxDTO> deleteyhscjkblxxList = new ArrayList<>();
//                for (ZnsbNssbYhscjDO yhshbcjxxb : yhshbcjxxbList) {
//                    String uuid = yhshbcjxxb.getUuid();
//                    if (uuid.equals(mxuuid)) {
//                        deletecjxxList.add(BeanUtils.toBean(yhshbcjxxb, YhshbcjxxbDTO.class));
//                        yhshbcjxxbList.remove(yhshbcjxxb);
//                        break;//跳出循环
//                    }
//                }
//                //过滤作废明细对应的可变列
//                for (int i = ycjyhscjkblxxList.size() - 1; i >= 0; i--) {
//                    YhscjkblxxDTO yhscjkblxx = ycjyhscjkblxxList.get(i);
//                    String timebz = yhscjkblxx.getTimebz();
//                    if (GyUtils.isNull(timebz)) {
//                        continue;
//                    }
//                    if (timebz.equals(mxuuid)) {
//                        ycjyhscjkblxxList.remove(i);
//                        deleteyhscjkblxxList.add(yhscjkblxx);
//                    }
//                }
//                //保存更正
//                //TODO 乐企错误更正接口
//                //gt3Invoker.invoke("SWZJ.HXZG.SB.YHSLFCJCWGZ", expand, hxzgsb11051Request, HXZGSB11051Response.class);
//                final LqYhsLfcjCwgzRequestDTO lqYhsLfcjCwgzRequestDTO = new LqYhsLfcjCwgzRequestDTO();
//                final String hxbuuid = znsbNssbCxsbtxxMapper.queryHxbuuidByZbuuid(deletezbuuid);
//                lqYhsLfcjCwgzRequestDTO.setZbuuid(hxbuuid);
//                lqYhsLfcjCwgzRequestDTO.setNsrxxFormVO(yhscjNsrxx);
//                lqYhsLfcjCwgzRequestDTO.setCjkblxxGrid(ycjyhscjkblxxList);
//                lqYhsLfcjCwgzRequestDTO.setUpdateCjxxGrid(BeanUtils.toBean(yhshbcjxxbList, YhshbcjxxbDTO.class));
//                lqYhsLfcjCwgzRequestDTO.setInsertCjxxGrid(null);
//                lqYhsLfcjCwgzRequestDTO.setDeleteCjxxGrid(deletecjxxList);
////                final LqYhsLfcjCwgzResponseVO lqYhsLfcjCwgzResponseVO = new LqYhsLfcjCwgzResponseVO();
////                lqYhsLfcjCwgzResponseVO.setReturncode("00");
////                lqYhsLfcjCwgzResponseVO.setPclsh(GyUtils.getUuid());
////                final DlfwReqDTO dlfwReqDTO = InvokeUtils.getDlfwReqDTO(nsrsbh,xzqhszDm,"YHSLFCJCWGZ", JsonUtils.toJson(lqYhsLfcjCwgzRequestVO),
////                        JsonUtils.toJson(lqYhsLfcjCwgzResponseVO), "1");
////                final DlfwResDTO res = invokeServiceApi.callService(dlfwReqDTO);
////                if (!"00".equals(res.getCode())) {
////                    log.error("调用乐企印花税错误更正接口异常：{},{}", res.getCode(), res.getMsg());
////                    throw (ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, res.getMsg()));
//////                yhsCjCwgzResDTO.setResmsg("更正失败");
//////                yhsCjCwgzResDTO.setRescode("-1");
////                }
////                //解析乐企印花税立法采集保存接口返回报文
////                final LqYhsLfcjBcResponseVO lqbcResponseVO = JsonUtils.toBean(res.getData(), LqYhsLfcjBcResponseVO.class);
////                //批次流水号
////                final String pclsh = lqbcResponseVO.getPclsh();
////                //乐企申报上传处理结果接口请求报文
////                final QuerySbscCljgReqDTO zcsbsccljgReqDTO = new QuerySbscCljgReqDTO();
////                zcsbsccljgReqDTO.setDjxh(yhsZfcjxxReqDTO.getDjxh());
////                zcsbsccljgReqDTO.setSbpclsh(pclsh);
////                //模拟乐企申报上传处理结果返回报文
////                final QuerySbscCljgResponseDTO cljgResponseDTO = new QuerySbscCljgResponseDTO();
////                cljgResponseDTO.setReturncode("00");
////                cljgResponseDTO.setYwztDm("30");
////                cljgResponseDTO.setSbpclsh(pclsh);
////                cljgResponseDTO.setSbuuid(GyUtils.getUuid());
////                //根据乐企返回的pclsh调用乐企申报上传处理结果接口--异步，异步回调接口为--nssb/cxscjsb/v1/yhsLfcjBcCallback
////                final DlfwAsyncReqDTO scztReqDTO = InvokeUtils.getDlfwAsyncReqDTO(nsrsbh,xzqhszDm,"LQ_CXZCSBSCCLJG", JsonUtils.toJson(zcsbsccljgReqDTO),
////                        "nssb/cxscjsb/v1/yhsLfcjBcCallback", JsonUtils.toJson(lqYhsLfcjCwgzRequestVO),
////                        JsonUtils.toJson(cljgResponseDTO), "1");
////                final DlfwAsyncResDTO cxzRes = invokeServiceApi.callAsyncService(scztReqDTO);
////                if (!"00".equals(cxzRes.getCode())) {
////                    log.error("调用乐企查询印花税申报上传处理结果接口异常：{},{}", res.getCode(), res.getMsg());
////                    throw (ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, res.getMsg()));
////                }
//                this.deleteYhsmx(deletecjxxList, deleteyhscjkblxxList, null, yhsZfcjxxReqDTO.getSjry(), yhsZfcjxxReqDTO.getSjry(), yhsZfcjxxReqDTO.getYwqdDm());
//                return "Y";
//            } else if (yhshbcjxxbList.size() == 1) {
//                if (!yhshbcjxxbList.get(0).getUuid().equals(mxuuid)) {
//                    return "Y";
//                }
//                final List<YhscjBtxxDTO> deleteCjxxGrid = new ArrayList<>();
//                deleteCjxxGrid.addAll(yhscjBtxxList);
////                gt3Invoker.invoke("SWZJ.HXZG.SB.ZFYHSYCJXX", expand, hxzgsb11054Request, HXZGSB11054Response.class);
//                //TODO 调用乐企接口
////                final DlfwReqDTO dlfwReqDTO = InvokeUtils.getDlfwReqDTO(nsrsbh,xzqhszDm,"ZFYHSYCJXX",
////                        this.getZfReqJson(yhsbtxx.getUuid(), skssqq, skssqz, yhsZfcjxxReqDTO.getDjxh()), "{\"flag\":\"Y\"}", "1");
////                DlfwResDTO res = invokeServiceApi.callService(dlfwReqDTO);
////                if (!"00".equals(res.getCode())) {
////                    log.error("调用乐企作废接口异常：{},{}", res.getCode(), res.getMsg());
////                    throw (ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, res.getMsg()));
////                }
////                final Map<String, String> zfResMap = JsonUtils.toBean(res.getData(), Map.class);
//                final Map<String, String> zfResMap = new HashMap<>();
//                zfResMap.put("flag", "Y");
//                if ("Y".equals(zfResMap.get("flag"))) { //更新业务表
//                    List<ZnsbNssbYhscjDO> savecjsyxxList = new ArrayList<>();
//                    final ZnsbNssbYhscjDO sbCxsYhslfsyxx = new ZnsbNssbYhscjDO();
//                    sbCxsYhslfsyxx.setUuid(mxuuid);
//                    sbCxsYhslfsyxx.setZfbz1("Y");
//                    sbCxsYhslfsyxx.setZfrDm(yhsZfcjxxReqDTO.getSjry());
//                    sbCxsYhslfsyxx.setZfrq1(new Date());
//                    sbCxsYhslfsyxx.setXgrsfid(yhsZfcjxxReqDTO.getSjry());
//                    sbCxsYhslfsyxx.setXgrq(new Date());
//                    savecjsyxxList.add(sbCxsYhslfsyxx);
//                    znsbNssbYhscjMapper.zfsycjxx(savecjsyxxList);
//                    rescode = "Y";
////                    int a = sbCxsYhslfsyxxMapper.zfsycjxx(savecjsyxxList);
//                }
//            }
//        }
////        else {
////            HXZGSB10770Request hxzgsb10770Request = new HXZGSB10770Request();
////            hxzgsb10770Request.setUuid(deletezbuuid);
////            HXZGSB10770Response hxzgsb10770Response = gt3Invoker.invoke("SWZJ.HXZG.SB.YHSECCJINIT", expand, hxzgsb10770Request, HXZGSB10770Response.class);
////            List<YhscjCjxxbDTO> yhscjCjxxbList = new ArrayList<>();
////            yhscjCjxxbList.addAll(hxzgsb10770Response.getAcsbGrid().getAcsbGridlb());
////            yhscjCjxxbList.addAll(hxzgsb10770Response.getAqsbGrid().getAqsbGridlb());
////            List<YhscjCjxxbDTO> deletecjxxList = new ArrayList<>();
////            if (yhscjCjxxbList.size() > 1) { //更正
////                //过滤需要删除的税源信息
////                for (YhscjCjxxb yhscjCjxxb : yhscjCjxxbList) {
////                    String uuid = yhscjCjxxb.getUuid();
////                    if (uuid.equals(mxuuid)) {
////                        deletecjxxList.add(yhscjCjxxb);
////                        yhscjCjxxbList.remove(yhscjCjxxb);
////                        break;//跳出循环
////                    }
////                }
////                HXZGSB10769Request hxzgsb10769Request = new HXZGSB10769Request();
////                hxzgsb10769Request.setNsrxxFormVO(yhscjNsrxx);
////                HXZGSB10769Request.InsertCjxxGrid insertCjxxGrid = new HXZGSB10769Request.InsertCjxxGrid();
////                HXZGSB10769Request.DeleteCjxxGrid deleteCjxxGrid = new HXZGSB10769Request.DeleteCjxxGrid();
////                HXZGSB10769Request.UpdateCjxxGrid updateCjxxGrid = new HXZGSB10769Request.UpdateCjxxGrid();
////                updateCjxxGrid.getUpdateCjxxGridlb().addAll(yhscjCjxxbList);
////                deleteCjxxGrid.getDeleteCjxxGridlb().addAll(deletecjxxList);
////
////                hxzgsb10769Request.setInsertCjxxGrid(insertCjxxGrid);
////                hxzgsb10769Request.setUpdateCjxxGrid(updateCjxxGrid);
////                hxzgsb10769Request.setDeleteCjxxGrid(deleteCjxxGrid);
////                hxzgsb10769Request.setZbuuid(deletezbuuid);
////                HXZGSB10769Response hxzgsb10769Response = gt3Invoker.invoke("SWZJ.HXZG.SB.YHSCJBCCWGZ", expand, hxzgsb10769Request, HXZGSB10769Response.class);
////                if (!GyUtils.isNull(hxzgsb10769Response)) {
////                    //String resmsg=deleteYhsmx(null,null, deletecjxxList, yhsZfcjxxReqDTO.getSjry(), yhsZfcjxxReqDTO.getSjry(),yhsZfcjxxReqDTO.getYwqdDm());
////                    return "Y";
////
////                }
////                return "N";
////            } else if (yhscjCjxxbList.size() == 1) {
////                if (!yhscjCjxxbList.get(0).getUuid().equals(mxuuid)) {
////                    return "Y";
////                }
////                //作废
////                final HXZGSB10771Request.DeleteCjxxGrid deleteCjxxGrid = new HXZGSB10771Request.DeleteCjxxGrid();
////                deleteCjxxGrid.getDeleteCjxxGridlb().addAll(yhscjBtxxList);
////                HXZGSB10771Request hxzgsb10771Request = new HXZGSB10771Request();
////                hxzgsb10771Request.setDeleteCjxxGrid(deleteCjxxGrid);
////                HXZGSB10771Response invoke = gt3Invoker.invoke("SWZJ.HXZG.SB.ZFYHSYCJMX", expand, hxzgsb10771Request, HXZGSB10771Response.class);
////                rescode = invoke.getFlag();
////                if ("Y".equals(rescode)) { //更新业务表
////                    List<SbCxsYhssyxx> savecjsyxxList = new ArrayList<>();
////                    for (YhscjBtxx yhscjBtxx : yhscjBtxxList) {
////                        SbCxsYhssyxx sbCxsYhssyxx = new SbCxsYhssyxx();
////                        sbCxsYhssyxx.setUuid(yhscjBtxx.getUuid());
////                        sbCxsYhssyxx.setZfbz1("Y");
////                        sbCxsYhssyxx.setZfrDm(yhsZfcjxxReqDTO.getSjry());
////                        sbCxsYhssyxx.setZfrq1(new Date());
////                        sbCxsYhssyxx.setXgrsfid(yhsZfcjxxReqDTO.getSjry());
////                        sbCxsYhssyxx.setXgrq(new Date());
////                        savecjsyxxList.add(sbCxsYhssyxx);
////                    }
////                    //int a = sbCxsYhssyxxMapper.zfyhssymx(savecjsyxxList);
////                }
////            } else {
////                return "Y";
////            }
////        }
//        return rescode;
//    }
//
//    /**
//     * 作废印花税税源信息
//     *
//     * @param hxbuuid
//     * @param yhscjNsrxx
//     * @return
//     * @throws ServiceException
//     */
//    public void zfYhsCjxxByLq(String hxbuuid, YhscjNsrxxDTO yhscjNsrxx, List<JbxxmxsjVO> qyjbxx) throws ServiceException {
//        LqZfYhsYwRequestDTO req = new LqZfYhsYwRequestDTO();
//        //拼装基础信息
//        LqZfYhsJcbwRequestDTO jcbw = new LqZfYhsJcbwRequestDTO();
//        jcbw.setJbr(qyjbxx.get(0).getBsrxm());
//        jcbw.setJbrsfzjlxDm(qyjbxx.get(0).getBsrsfzjlxDm());
//        jcbw.setJbrsfzjhm(qyjbxx.get(0).getBsrsfzjhm());
//
//        //拼装业务报文
//        List<LqZfYhsYwbwRequestDTO> ywbwList = new ArrayList<>();
//        LqZfYhsYwbwRequestDTO ywbw = new LqZfYhsYwbwRequestDTO();
//        ywbw.setSwsxDm("SXN022006701");
//        ywbw.setDjxh(yhscjNsrxx.getDjxh());
//        ywbw.setSwjgDm(qyjbxx.get(0).getZgswjDm());
//        ywbw.setSkssqq(yhscjNsrxx.getSkssqq());
//        ywbw.setSkssqz(yhscjNsrxx.getSkssqz());
//        ywbw.setSyczbz(YHSZF);
//        ywbw.setMxid(GyUtils.getUuid());
//
//        List<LqZfYhsYwDelLbRequestDTO> sbsjList = new ArrayList<>();
//        LqZfYhsYwDelLbRequestDTO sbsj = new LqZfYhsYwDelLbRequestDTO();
//        final String date = DateUtil.doDateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
//        sbsj.setUuid(hxbuuid);
//        sbsj.setSkssqq(yhscjNsrxx.getSkssqq());
//        sbsj.setSkssqz(yhscjNsrxx.getSkssqz());
//        sbsj.setYsbbz("");
//        sbsj.setSbsxDm1("11");
//        sbsj.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
//        sbsj.setHyDm(yhscjNsrxx.getHyDm());
//        sbsj.setXzqhszDm(yhscjNsrxx.getXzqhszDm());
//        sbsj.setJdxzDm(yhscjNsrxx.getJdxzDm());
//        sbsj.setZgswskfjDm(yhscjNsrxx.getZgswskfjDm());
//        sbsj.setDjxh(yhscjNsrxx.getDjxh());
//        sbsj.setZfbz1("N");
//        sbsj.setZfrq1(date);
//        sbsj.setZfrDm(qyjbxx.get(0).getBsrxm());
//        sbsj.setLrrDm(qyjbxx.get(0).getBsrxm());
//        sbsj.setLrrq(date);
//        sbsj.setXgrq(date);
//        sbsjList.add(sbsj);
//        ywbw.setSbsj(Base64Utils.encode(JsonUtils.toJson(sbsjList)));
//        ywbwList.add(ywbw);
//
//        req.setRequestId(GyUtils.getUuid());
//        req.setJcbw(jcbw);
//        req.setYwbw(ywbwList);
//        final String ywbwSjjh = JsonUtils.toJson(req);
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("BC00000003");
//        sjjhDTO.setYwbm(YzpzzlEnum.YHS.getDm());
//        sjjhDTO.setYwuuid(hxbuuid);
//        sjjhDTO.setDjxh(yhscjNsrxx.getDjxh());
//        sjjhDTO.setNsrsbh(yhscjNsrxx.getNsrsbh());
//        sjjhDTO.setXzqhszDm(yhscjNsrxx.getXzqhszDm());
//        sjjhDTO.setBwnr(ywbwSjjh);
//        final CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
//    }
//
//    /**
//     * 印花税错误更正初始化
//     *
//     * @param uuid
//     * @param yhscjNsrxx
//     * @return
//     * @throws ServiceException
//     */
////    @Service(serviceName = "SWZJ.HXZG.SB.YHSCWGZCJINIT", cacheValue = false, memo = "业务服务：新印花税采集载入数据")
//    public LqYhssyxxCxResponseDTO yhsCwgzCjInitData(String uuid, YhscjNsrxxDTO yhscjNsrxx) throws ServiceException {
//        final LqYhssyxxCxResponseDTO respDTO = new LqYhssyxxCxResponseDTO();
//        //乐企印花税错误更正初始化接口请求报文
//        final LqYhssyxxCxRequestDTO requestDTO = new LqYhssyxxCxRequestDTO();
//        requestDTO.setDjxh(yhscjNsrxx.getDjxh());
//        requestDTO.setSkssqq(yhscjNsrxx.getSkssqq());
//        requestDTO.setSkssqz(yhscjNsrxx.getSkssqz());
//        requestDTO.setSbbz("N");
//        //通过数据交换服务调用乐企接口
//        final String ywbw = JsonUtils.toJson(requestDTO);
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("CX00000004");
//        sjjhDTO.setYwbm(YzpzzlEnum.YHS.getDm());
//        sjjhDTO.setYwuuid(uuid);
//        sjjhDTO.setDjxh(yhscjNsrxx.getDjxh());
//        sjjhDTO.setNsrsbh(yhscjNsrxx.getNsrsbh());
//        sjjhDTO.setXzqhszDm(yhscjNsrxx.getXzqhszDm());
//        sjjhDTO.setBwnr(ywbw);
//        final CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
//        if (GyUtils.isNotNull(result) && GyUtils.isNotNull(result.getData())) {
//            log.info("乐企返回印花税错误更正初始化接口报文{}", result);
//            final LqYhssyxxCxResponseDTO resDTO = JsonUtils.toBean((String) result.getData(), LqYhssyxxCxResponseDTO.class);
//            if (!"00".equals(resDTO.getReturncode())) {
//                log.error("调用乐企印花税错误更正初始化接口失败：{}", resDTO.getReturnmsg());
//            }
//            final List<YhshbcjxxbDTO> yhshbcjxxbList = respDTO.getSymxGrid();
//            final List<YhscjkblxxDTO> yhscjkblxxDTOList = respDTO.getYhskblxxGrid();
//            if (GyUtils.isNotNull(yhshbcjxxbList)) {
//                respDTO.setSymxGrid(yhshbcjxxbList.stream().filter(cjxx -> uuid.equals(cjxx.getZbuuid())).collect(Collectors.toList()));
//            }
//            if (GyUtils.isNotNull(yhscjkblxxDTOList)) {
//                respDTO.setYhskblxxGrid(yhscjkblxxDTOList.stream().filter(kbl -> uuid.equals(kbl.getZbuuid())).collect(Collectors.toList()));
//            }
//        }
//        return respDTO;
//    }
//
//    /**
//     * 印花税错误更正初始化
//     *
//     * @param uuid
//     * @return
//     * @throws ServiceException
//     */
//    public LqYhssyxxCxResponseDTO yhsCwgzCjInitDataBd(String uuid) throws ServiceException {
//        final LqYhssyxxCxResponseDTO respDTO = new LqYhssyxxCxResponseDTO();
//        final List<ZnsbNssbYhscjDO> znsbNssbYhscjDOList = znsbNssbYhscjMapper.queryOldIdByHxbuuid(uuid);
//        if (GyUtils.isNotNull(znsbNssbYhscjDOList)) {
//            respDTO.setSymxGrid(BeanUtils.toBean(znsbNssbYhscjDOList.stream().filter(cjxx -> uuid.equals(cjxx.getZbuuid())).collect(Collectors.toList()), YhshbcjxxbDTO.class));
//        }
//        final List<ZnsbNssbYhslfkblDO> znsbNssbYhslfkblDOList = znsbNssbYhslfkblMapper.queryycjxxkblbyzbuuid(uuid);
//        if (GyUtils.isNotNull(znsbNssbYhslfkblDOList)) {
//            respDTO.setYhskblxxGrid(BeanUtils.toBean(znsbNssbYhslfkblDOList.stream().filter(kbl -> uuid.equals(kbl.getZbuuid())).collect(Collectors.toList()), YhscjkblxxDTO.class));
//        }
//        return respDTO;
//    }
//
//    /**
//     * 印花税税源信息插叙
//     *
//     * @param yhscjNsrxx
//     * @return
//     * @throws ServiceException
//     */
//    public LqYhssyxxCxResponseDTO yhssyxxCx(YhscjNsrxxDTO yhscjNsrxx) throws ServiceException {
//        LqYhssyxxCxResponseDTO respDTO = new LqYhssyxxCxResponseDTO();
//        //乐企印花税错误更正初始化接口请求报文
//        final LqYhssyxxCxRequestDTO requestDTO = new LqYhssyxxCxRequestDTO();
//        requestDTO.setDjxh(yhscjNsrxx.getDjxh());
//        requestDTO.setSkssqq(yhscjNsrxx.getSkssqq());
//        requestDTO.setSkssqz(yhscjNsrxx.getSkssqz());
//        //通过数据交换服务调用乐企接口
//        final String ywbw = JsonUtils.toJson(requestDTO);
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("CX00000004");
//        sjjhDTO.setYwbm(YzpzzlEnum.YHS.getDm());
//        sjjhDTO.setYwuuid(GyUtils.getUuid());
//        sjjhDTO.setDjxh(yhscjNsrxx.getDjxh());
//        sjjhDTO.setNsrsbh(yhscjNsrxx.getNsrsbh());
//        sjjhDTO.setXzqhszDm(yhscjNsrxx.getXzqhszDm());
//        sjjhDTO.setBwnr(ywbw);
//        final CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
//        if (GyUtils.isNotNull(result) && GyUtils.isNotNull(result.getData())) {
//            log.info("乐企返回印花税错误更正初始化接口报文{}", result);
//            respDTO = JsonUtils.toBean((String) result.getData(), LqYhssyxxCxResponseDTO.class);
//            if (!"00".equals(respDTO.getReturncode())) {
//                log.error("调用乐企印花税错误更正初始化接口失败：{}", respDTO.getReturnmsg());
//            }
//        }
//        return respDTO;
//    }
//
//    /**
//     * 印花税立法采集保存回调
//     *
//     * @param data
//     * @param callBackParams
//     */
//    @Override
//    public CommonResult<Map<String, Object>> yhsLfcjBcCallback(String data, String callBackParams) {
//        final Map<String, Object> resMap = new HashMap<>();
//        String code = "00";
//        String msg = "";
//        //乐企保存印花税税源采集信息接口请求报文
//        final LqYhsLfcjBcRequestDTO lqbcRequestVO = JsonUtils.toBean(callBackParams, LqYhsLfcjBcRequestDTO.class);
//        //申报上传处理结果查询返回报文
//        final QuerySbscCljgResponseDTO cljgResponseDTO = JsonUtils.toBean(data, QuerySbscCljgResponseDTO.class);
//        final String pclsh = cljgResponseDTO.getSbpclsh();
//        final String hxuuid = cljgResponseDTO.getSbuuid();
//        final String bczt = cljgResponseDTO.getYwztDm();
//        //根据pclsh修改znsb_nssb_cxsbtxx和znsb_nssb_yhscj中的bczt、hxbuuid
//        znsbNssbCxsbtxxMapper.updateBcztByPclsh(pclsh, bczt, hxuuid);
//        //根据pclsh修改znsb_nssb_cxsbtxx和znsb_nssb_yhscj中的bczt、hxbuuid
//        znsbNssbYhscjMapper.updateBcztByPclsh(pclsh, bczt, hxuuid);
//        resMap.put("code", code);
//        resMap.put("msg", msg);
//        return CommonResult.success(resMap);
//    }
//
//    private String getZfReqJson(String uuid, String skssqq, String skssqz, String djxh) {
//        final Map<String, String> dlfwReqMap = new HashMap<>();
//        dlfwReqMap.put("uuid", uuid);
//        dlfwReqMap.put("skssqq", skssqq);
//        dlfwReqMap.put("skssqz", skssqz);
//        dlfwReqMap.put("ysbbz", "N");
//        dlfwReqMap.put("sbsxDm1", "11");
//        dlfwReqMap.put("yzpzzlDm", "BDA0610794");
//        dlfwReqMap.put("hyDm", "");
//        dlfwReqMap.put("xzqhszDm", "");
//        dlfwReqMap.put("jdxzDm", "");
//        dlfwReqMap.put("zgswskfjDm", "");
//        dlfwReqMap.put("djxh", djxh);
//        dlfwReqMap.put("zfbz1", "Y");
//        return JsonUtils.toJson(dlfwReqMap);
//    }
//
//    private String getBcDbsj() {
//        final ClassPathResource resource = new ClassPathResource("/mock/yhs/yhsBc.json");
//        return resource.readUtf8Str();
//    }
//
//    /**
//     * 统一采集或者更正
//     *
//     * @param yhsCjCwgzReqDTO
//     * @return
//     */
//    public YhsCjCwgzResDTO yhsTySaveCjAndGzNew(YhsCjCwgzReqDTO yhsCjCwgzReqDTO) {
//        YhsCjCwgzResDTO yhsCjCwgzResDTO = new YhsCjCwgzResDTO();
//        //-1：保存时阻断报错
//        //-2：存在已申报属期
//        //-3：前置校验不通过
//        List<YhshbcjxxbDTO> yhshbcjxxbDTOListList = yhsCjCwgzReqDTO.getYhshbcjxxbList();
//        List<YhscjCjxxbDTO> yhscjCjxxbDTOList = yhsCjCwgzReqDTO.getYhscjCjxxbList();
//        log.info("印花税统一采集参数------" + "旧版参数：" + yhscjCjxxbDTOList + "新版参数：" + yhshbcjxxbDTOListList);
////        //正常保存 修改的采集信息
////        List<YhshbcjxxbDTO> yhshbcjxxbDTOListList = new ArrayList<>();
////        //删除的采集信息
////        List<YhshbcjxxbDTO> yhsDelcjxxbList = new ArrayList<>();
////        for (YhshbcjxxbDTO cjxxDTO : yhshbcjxxbAllDTOListList){
////            if ("delete".equals(cjxxDTO.getGzbz())){
////                yhsDelcjxxbList.add(cjxxDTO);
////            }else {
////                yhshbcjxxbDTOListList.add(cjxxDTO);
////            }
////        }
//        //做个校验，防止前端传过来立法前后数据错乱
//        try {
//            boolean resCheck = yhsSbCwgzService.checklfsj(yhshbcjxxbDTOListList, yhscjCjxxbDTOList);
//            if (!resCheck) {
//                yhsCjCwgzResDTO.setResmsg("立法校验未通过，保存失败");
//                yhsCjCwgzResDTO.setRescode("-3");
//                return yhsCjCwgzResDTO;
//            }
//            // 1）产品转移书据品目未认定到子目，不录入子目信息不允许提交。
//            // 2) 提交数据中有同品目子目数据不允许提交
//            yhsCjCwgzResDTO = this.checkYhssfksb(yhsCjCwgzReqDTO, yhsCjCwgzResDTO);
//            if ("-3".equals(yhsCjCwgzResDTO.getRescode())) {
//                return yhsCjCwgzResDTO;
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            yhsCjCwgzResDTO.setResmsg("上传所属期不规范，保存失败");
//            yhsCjCwgzResDTO.setRescode("-3");
//            return yhsCjCwgzResDTO;
//        }
//
//        if (!GyUtils.isNull(yhshbcjxxbDTOListList)) { //TODO 2022-07-01后使用新版印花税更正
//            yhshbcjxxbDTOListList.stream().filter(f -> "10".equals(f.getNsqxDm()) && "101110502".equals(f.getZspmDm()) &&
//                    !GyUtils.isNull(f.getSkssqq()) && f.getSkssqq().length() >= 10 && !"01-01".equals(f.getSkssqq().substring(5, 10)))
//                    .forEach(f -> {
//                        f.setSkssqqOld(f.getSkssqq());
//                        f.setSkssqq(f.getSkssqq().substring(0, 4) + "-01-01");
//                    });
//            Map aqzbuuiddzMap = new HashMap<>();//按期     主表uuid与更正 删除 新增对照map
//            Map aczbuuiddzMap = new HashMap<>();//按次采集  主表uuid与更正 删除 新增对照map
//            Map zbuuiddzMap = new HashMap<>();
//            List<Map<String, Object>> aqxzList = new ArrayList<>();
//            List<YhshbcjxxbDTO> newAccjxxList = new ArrayList<>();
//            List<YhshbcjxxbDTO> newAqcjxxList = new ArrayList<>();
//            List<Map<String, Object>> aqzbuuidList = new ArrayList<>();
//            List<Map<String, Object>> aczbuuidList = new ArrayList<>();
//            List<Map<String, Object>> zbuuidList = new ArrayList<>();
//            //按期和按次分开采集 (按期和按次有可能在同一个税源即同一个zbuuid)所以先分开
//            for (YhshbcjxxbDTO yhshbcjxxbDTO : yhshbcjxxbDTOListList) {
//                String sbqxlx = yhshbcjxxbDTO.getSbqxlx();
//                String ssjmxzDm = yhshbcjxxbDTO.getSsjmxzDm();
//                String zspmMc = yhshbcjxxbDTO.getZspmMc();
//                BigDecimal jmse = yhshbcjxxbDTO.getJmse();
//                if ("0".equals(yhshbcjxxbDTO.getGzbz())) {
//                    yhshbcjxxbDTO.setGzbz("insert");
//                } else if ("1".equals(yhshbcjxxbDTO.getGzbz())) {
//                    yhshbcjxxbDTO.setGzbz("update");
//                }
//                if (BigDecimal.ZERO.compareTo(jmse) > 0 && GyUtils.isNull(ssjmxzDm)) {
//                    yhsCjCwgzResDTO.setRescode("-3");
//                    yhsCjCwgzResDTO.setResmsg(zspmMc + "的减免税额大于0时，减免性质代码不能为空，请修改！");
//                    return yhsCjCwgzResDTO;
//                }
//                if ("01".equals(sbqxlx)) {//期满之日起5日内
//                    if ("insert".equals(yhshbcjxxbDTO.getGzbz())) {
//                        newAccjxxList.add(yhshbcjxxbDTO);
//                    } else if ("Y".equals(yhshbcjxxbDTO.getSfksbqxlxbz())) {
//                        newAqcjxxList.add(yhshbcjxxbDTO);
//                    } else {
//                        newAccjxxList.add(yhshbcjxxbDTO);
//                    }
//                } else {
//                    newAqcjxxList.add(yhshbcjxxbDTO);
//                }
//            }
//            //按期 1.判断如果既有按次又有按期 把按次的删除掉 2.按期  新增 修改 入同一个属期
//            List<YhshbcjxxbDTO> aqinsertcjxxList = new ArrayList<>();
//            List<YhshbcjxxbDTO> aqdeletecjxxList = new ArrayList<>();//记录按期中 出现按次的税源
//            for (YhshbcjxxbDTO yhshbcjxxbDTO : newAqcjxxList) { //按期采集 可能有多个属期 按季 按年度
//                String nsqxDm = yhshbcjxxbDTO.getNsqxDm();
//                String gzbz = yhshbcjxxbDTO.getGzbz();
//                String zbuuid = yhshbcjxxbDTO.getZbuuid();
//                String hxbuuid = yhshbcjxxbDTO.getHxbuuid();
//                Map<String, Object> zbuuidsqMap = new HashMap<>();
//                zbuuidsqMap.put("skssqq", yhshbcjxxbDTO.getSkssqq());
//                zbuuidsqMap.put("skssqz", yhshbcjxxbDTO.getSkssqz());
//                if ("11".equals(nsqxDm)) { //把一个属期内有按次的作废掉  同时把删除的按次税源匹配到按次采集中
//                    aqdeletecjxxList.add(yhshbcjxxbDTO);
//                    yhshbcjxxbDTO.setGzbz("insert");//作为按次的新增明细
//                    newAccjxxList.add(yhshbcjxxbDTO);
//                } else {
//                    if ("delete".equals(gzbz)) {
//                        zbuuidsqMap.put("zbuuid", zbuuid);
//                        zbuuidsqMap.put("nsqxDm", nsqxDm);
//                        zbuuidsqMap.put("hxbuuid", hxbuuid);
//                        aqzbuuidList.add(zbuuidsqMap);
//                    } else if ("update".equals(gzbz)) {
//                        zbuuidsqMap.put("zbuuid", zbuuid);
//                        zbuuidsqMap.put("nsqxDm", nsqxDm);
//                        zbuuidsqMap.put("hxbuuid", hxbuuid);
//                        aqzbuuidList.add(zbuuidsqMap);
//                    } else if ("insert".equals(gzbz)) { //新增没有zbuuid
//                        aqinsertcjxxList.add(yhshbcjxxbDTO);
//                    } else { //无标志默认更新
//                        zbuuidsqMap.put("zbuuid", zbuuid);
//                        zbuuidsqMap.put("nsqxDm", nsqxDm);
//                        zbuuidsqMap.put("hxbuuid", hxbuuid);
//                        aqzbuuidList.add(zbuuidsqMap);
//                    }
//                }
//            }
//            if (!GyUtils.isNull(aqzbuuidList)) {
//                //1.过滤掉重复的按期采集的zbuuid
//                if (GyUtils.isNotNull(aqzbuuidList) && aqzbuuidList.size() > 0) {
//                    final Map<String, Object> map = new HashMap<>();
//                    for (int i = aqzbuuidList.size() - 1; i >= 0; i--) {
//                        final Map<String, Object> zbuuidMap = aqzbuuidList.get(i);
//                        String zbuuid = (String) zbuuidMap.get("zbuuid");
//                        final String comzbuuid = (String) map.get(zbuuid);
//                        if (comzbuuid == null) {
//                            map.put(zbuuid, zbuuid);
//
//                        } else {
//                            aqzbuuidList.remove(i);
//                        }
//                    }
//                }
//                //2.按期采集  根据zbuuid把同一个zbuuid 下的更正 删除 组装
//                for (Map<String, Object> map : aqzbuuidList) {
//                    String zbuuid = map.get("zbuuid") == null ? "" : (String) map.get("zbuuid");
//                    log.info("印花税做按期采集更正或者保存时有数据zbuuid为空：djxh=" + yhsCjCwgzReqDTO.getYhscjNsrxx().getDjxh());
//                    List<YhshbcjxxbDTO> updatedzcjxxList = new ArrayList<>();
//                    List<YhshbcjxxbDTO> deletecdzjxxList = new ArrayList<>();
//                    List<YhscjkblxxDTO> yhscjkblxxList = new ArrayList<>();
//                    for (YhshbcjxxbDTO yhshbcjxxbDTO : newAqcjxxList) {
//                        String gzbz = yhshbcjxxbDTO.getGzbz();
//                        /*if (GyUtils.isNull(yhshbcjxxbDTO.getZbuuid())) {
//                            continue;
//                        }*/
//                        if (zbuuid.equals(yhshbcjxxbDTO.getZbuuid())) { //获取同一个zbuuid下更正和delete的明细
//                            if ("delete".equals(gzbz)) {
//                                if ("10".equals(yhshbcjxxbDTO.getNsqxDm()) && "101110502".equals(yhshbcjxxbDTO.getZspmDm()) &&
//                                        !GyUtils.isNull(yhshbcjxxbDTO.getSkssqqOld()) && yhshbcjxxbDTO.getSkssqqOld().length() >= 10 &&
//                                        !"01-01".equals(yhshbcjxxbDTO.getSkssqqOld().substring(5, 10))) {
//                                    yhshbcjxxbDTO.setSkssqq(yhshbcjxxbDTO.getSkssqqOld());
//                                }
//                                deletecdzjxxList.add(yhshbcjxxbDTO);
//                                if (!GyUtils.isNull(yhshbcjxxbDTO.getYhscjkblxxList())) {
//                                    yhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                                }
//                            } else if ("update".equals(gzbz)) {
//                                if ("10".equals(yhshbcjxxbDTO.getNsqxDm()) && "101110502".equals(yhshbcjxxbDTO.getZspmDm()) &&
//                                        !GyUtils.isNull(yhshbcjxxbDTO.getSkssqqOld()) && yhshbcjxxbDTO.getSkssqqOld().length() >= 10 &&
//                                        !"01-01".equals(yhshbcjxxbDTO.getSkssqqOld().substring(5, 10))) {
//                                    yhshbcjxxbDTO.setSkssqq(yhshbcjxxbDTO.getSkssqqOld());
//                                }
//                                updatedzcjxxList.add(yhshbcjxxbDTO);
//                                if (!GyUtils.isNull(yhshbcjxxbDTO.getYhscjkblxxList())) {
//                                    yhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                                }
//                            } else if ("insert".equals(gzbz)) { //新增
//
//                            } else { //无标志默认更新
//                                if ("10".equals(yhshbcjxxbDTO.getNsqxDm()) && "101110502".equals(yhshbcjxxbDTO.getZspmDm()) &&
//                                        !GyUtils.isNull(yhshbcjxxbDTO.getSkssqqOld()) && yhshbcjxxbDTO.getSkssqqOld().length() >= 10 &&
//                                        !"01-01".equals(yhshbcjxxbDTO.getSkssqqOld().substring(5, 10))) {
//                                    yhshbcjxxbDTO.setSkssqq(yhshbcjxxbDTO.getSkssqqOld());
//                                }
//                                updatedzcjxxList.add(yhshbcjxxbDTO);
//                                if (!GyUtils.isNull(yhshbcjxxbDTO.getYhscjkblxxList())) {
//                                    yhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                                }
//                            }
//                        }
//                    }
//                    //把分离出来的按次的税源 匹配到对应的zbuuid中 作废
//                    for (YhshbcjxxbDTO yhshbcjxxbDTO : aqdeletecjxxList) {
//                        if (zbuuid.equals(yhshbcjxxbDTO.getZbuuid())) {
//                            deletecdzjxxList.add(yhshbcjxxbDTO);
//                            if (!GyUtils.isNull(yhshbcjxxbDTO.getYhscjkblxxList())) {
//                                yhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                            }
//                        }
//                    }
//                    final Object[] resObj = new Object[4];
//                    resObj[0] = updatedzcjxxList;
//                    resObj[1] = deletecdzjxxList;
//                    resObj[2] = yhscjkblxxList;//记录可变列
//                    resObj[3] = new ArrayList<>();
//                    aqzbuuiddzMap.put(zbuuid, resObj);
//                }
//                List<YhshbcjxxbDTO> aqxzinsertcjxxList = new ArrayList<>();
//                //3.按期 判断按期新增的数据 相同属期入同一个属期
//                for (int i = aqinsertcjxxList.size() - 1; i >= 0; i--) {
//                    YhshbcjxxbDTO yhshbcjxxbDTO = aqinsertcjxxList.get(i);
//                    String iskssqq = yhshbcjxxbDTO.getSkssqq().substring(0, 10);
//                    String iskssqz = yhshbcjxxbDTO.getSkssqz().substring(0, 10);
//                    List<YhshbcjxxbDTO> updateinsertcjxxList = new ArrayList<>();
//                    for (Map<String, Object> zbxxmap : aqzbuuidList) {
//                        String uskssqq = (String) zbxxmap.get("skssqq");
//                        String uskssqz = (String) zbxxmap.get("skssqz");
//                        uskssqq = uskssqq.substring(0, 10);
//                        uskssqz = uskssqz.substring(0, 10);
//                        String zbuuid1 = (String) zbxxmap.get("zbuuid");
//                        if (iskssqq.equals(uskssqq) && iskssqz.equals(uskssqz)) { //属期相等
//                            Object[] obj = (Object[]) aqzbuuiddzMap.get(zbuuid1);
//                            List<YhscjkblxxDTO> kblList = (List<YhscjkblxxDTO>) obj[2];
//                            updateinsertcjxxList = (List<YhshbcjxxbDTO>) obj[3];//先取出再叠加
//                            updateinsertcjxxList.add(yhshbcjxxbDTO);
//                            kblList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                            obj[2] = kblList;
//                            obj[3] = updateinsertcjxxList;
//                            break;
//                        }
//                    }
//                    if (GyUtils.isNull(updateinsertcjxxList)) {
//                        aqxzinsertcjxxList.add(yhshbcjxxbDTO);
//                    }
//                }
//                //把本次新增明细  相同属期组装一起
//                for (int i = 0; i <= aqxzinsertcjxxList.size() - 1; i++) {
//                    YhshbcjxxbDTO yhshbcjxxbDTO = aqxzinsertcjxxList.get(i);
//                    String iskssqq = yhshbcjxxbDTO.getSkssqq();
//                    String iskssqz = yhshbcjxxbDTO.getSkssqz();
//                    Map<String, Object> aqxzMap = new HashMap<>();
//                    if ("101110502".equals(yhshbcjxxbDTO.getZspmDm())
//                            && (iskssqq.startsWith("2022") || iskssqq.startsWith("2023"))
//                            && "10".equals(yhshbcjxxbDTO.getNsqxDm())) {
//                        iskssqq = iskssqq.substring(0, 4) + "-01-01";
//                    }
//                    aqxzMap.put("skssqq", iskssqq);
//                    aqxzMap.put("skssqz", iskssqz);
//                    List<YhshbcjxxbDTO> saveinsertcjxxList = new ArrayList<>();//
//                    List<YhscjkblxxDTO> saveyhscjkblxxList = new ArrayList<>();//
//                    saveinsertcjxxList.add(yhshbcjxxbDTO);
//                    saveyhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                    for (int j = aqxzinsertcjxxList.size() - 1; j >= 0; j--) {
//                        if (i == j) {
//                            break;
//                        }
//                        YhshbcjxxbDTO uyhshbcjxxbDTO = aqxzinsertcjxxList.get(j);
//                        String uskssqq = uyhshbcjxxbDTO.getSkssqq();
//                        String uskssqz = uyhshbcjxxbDTO.getSkssqz();
//                        if (iskssqq.equals(uskssqq) && iskssqz.equals(uskssqz)) {
//                            saveinsertcjxxList.add(uyhshbcjxxbDTO);
//                            saveyhscjkblxxList.addAll(uyhshbcjxxbDTO.getYhscjkblxxList());
//                            aqxzinsertcjxxList.remove(j);
//                        }
//                    }
//                    saveinsertcjxxList.stream().filter(f -> "10".equals(f.getNsqxDm()) && "101110502".equals(f.getZspmDm()) &&
//                            !GyUtils.isNull(f.getSkssqqOld()) && f.getSkssqqOld().length() >= 10 && !"01-01".equals(f.getSkssqqOld().substring(5, 10)))
//                            .forEach(f -> {
//                                f.setSkssqq(f.getSkssqqOld());
//                            });
//                    aqxzMap.put("yhshbcjxxList", saveinsertcjxxList);
//                    aqxzMap.put("yhscjkblxxList", saveyhscjkblxxList);
//                    aqxzList.add(aqxzMap);
//                }
//                /* String aqzbuuid = (String) aqzbuuidList.get(0).get("zbuuid");
//                final Object[] resObj = new Object[4];
//                resObj[0] = BeanUtils.toBean(aqupdatecjxxList,Yhshbcjxxb.class);
//                resObj[1] = BeanUtils.toBean(aqdeletecjxxList,Yhshbcjxxb.class);
//                resObj[2] = BeanUtils.toBean(aqyhscjkblxxList,Yhscjkblxx.class);//记录可变列
//                resObj[3] =  BeanUtils.toBean(aqinsertcjxxList,Yhshbcjxxb.class);
//                AqzbuuiddzMap.put(aqzbuuid, resObj);*/
//            } else { //新增税源
//                /* Map<String,Object>aqxzMap = new HashMap<>();
//                AqinsertcjxxList = BeanUtils.toBean(aqinsertcjxxList,Yhshbcjxxb.class);
//                AqyhscjkblxxList = BeanUtils.toBean(aqyhscjkblxxList,Yhscjkblxx.class);
//                aqxzMap.put("skssqq",aqinsertcjxxList.get(0).getSkssqq());
//                aqxzMap.put("skssqz",aqinsertcjxxList.get(0).getSkssqz());
//                aqxzMap.put("yhshbcjxxList",BeanUtils.toBean(AqinsertcjxxList,Yhshbcjxxb.class));
//                aqxzMap.put("yhscjkblxxList",BeanUtils.toBean(AqyhscjkblxxList,Yhshbcjxxb.class));
//                aqxzList.add(aqxzMap);*/
//                //把本次新增明细  相同属期组装一起
//                for (int i = 0; i <= aqinsertcjxxList.size() - 1; i++) {
//                    YhshbcjxxbDTO yhshbcjxxbDTO = aqinsertcjxxList.get(i);
//                    String iskssqq = yhshbcjxxbDTO.getSkssqq();
//                    String iskssqz = yhshbcjxxbDTO.getSkssqz();
//                    Map<String, Object> aqxzMap = new HashMap<>();
//                    if ("101110502".equals(yhshbcjxxbDTO.getZspmDm())
//                            && (iskssqq.startsWith("2022") || iskssqq.startsWith("2023"))
//                            && "10".equals(yhshbcjxxbDTO.getNsqxDm())) {
//                        iskssqq = iskssqq.substring(0, 4) + "-01-01";
//                    }
//                    aqxzMap.put("skssqq", iskssqq);
//                    aqxzMap.put("skssqz", iskssqz);
//                    List<YhshbcjxxbDTO> saveinsertcjxxList = new ArrayList<>();//
//                    List<YhscjkblxxDTO> saveyhscjkblxxList = new ArrayList<>();//
//                    saveinsertcjxxList.add(yhshbcjxxbDTO);
//                    saveyhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                    for (int j = aqinsertcjxxList.size() - 1; j >= 0; j--) {
//                        if (i == j) {
//                            break;
//                        }
//                        YhshbcjxxbDTO uyhshbcjxxbDTO = aqinsertcjxxList.get(j);
//                        String uskssqq = uyhshbcjxxbDTO.getSkssqq();
//                        String uskssqz = uyhshbcjxxbDTO.getSkssqz();
//                        if (iskssqq.equals(uskssqq) && iskssqz.equals(uskssqz)) {
//                            saveinsertcjxxList.add(uyhshbcjxxbDTO);
//                            saveyhscjkblxxList.addAll(uyhshbcjxxbDTO.getYhscjkblxxList());
//                            aqinsertcjxxList.remove(j);
//                        }
//                    }
//                    saveinsertcjxxList.stream().filter(f -> "10".equals(f.getNsqxDm()) && "101110502".equals(f.getZspmDm()) &&
//                            !GyUtils.isNull(f.getSkssqqOld()) && f.getSkssqqOld().length() >= 10 && !"01-01".equals(f.getSkssqqOld().substring(5, 10)))
//                            .forEach(f -> {
//                                f.setSkssqq(f.getSkssqqOld());
//                            });
//                    aqxzMap.put("yhshbcjxxList", saveinsertcjxxList);
//                    aqxzMap.put("yhscjkblxxList", saveyhscjkblxxList);
//                    aqxzList.add(aqxzMap);
//                }
//            }
//            //按次 1.把zbuuid 提取 更正 新增 删除 分组
//            List<YhshbcjxxbDTO> acinsertcjxxList = new ArrayList<>();
//            List<YhshbcjxxbDTO> acupdatecjxxList = new ArrayList<>();
//            List<YhshbcjxxbDTO> acdeletecjxxList = new ArrayList<>();
//            for (YhshbcjxxbDTO yhshbcjxxbDTO : newAccjxxList) {
//                String gzbz = yhshbcjxxbDTO.getGzbz();
//                Map<String, Object> zbuuidsqMap = new HashMap<>();
//                zbuuidsqMap.put("skssqq", yhshbcjxxbDTO.getSkssqq());
//                zbuuidsqMap.put("skssqz", yhshbcjxxbDTO.getSkssqz());
//                if ("delete".equals(gzbz)) {
//                    zbuuidsqMap.put("zbuuid", yhshbcjxxbDTO.getZbuuid());
//                    zbuuidsqMap.put("hxbuuid", yhshbcjxxbDTO.getHxbuuid());
//                    aczbuuidList.add(zbuuidsqMap);
//                    acdeletecjxxList.add(yhshbcjxxbDTO);
//                } else if ("update".equals(gzbz)) {
//                    zbuuidsqMap.put("zbuuid", yhshbcjxxbDTO.getZbuuid());
//                    zbuuidsqMap.put("hxbuuid", yhshbcjxxbDTO.getHxbuuid());
//                    acupdatecjxxList.add(yhshbcjxxbDTO);
//                    aczbuuidList.add(zbuuidsqMap);
//                } else if ("insert".equals(gzbz)) { //新增没有zbuuid
//                    acinsertcjxxList.add(yhshbcjxxbDTO);
//                } else { //无标志默认更新
//                    zbuuidsqMap.put("zbuuid", yhshbcjxxbDTO.getZbuuid());
//                    zbuuidsqMap.put("hxbuuid", yhshbcjxxbDTO.getHxbuuid());
//                    acupdatecjxxList.add(yhshbcjxxbDTO);
//                    aczbuuidList.add(zbuuidsqMap);
//                }
//            }
//            //2.过滤掉重复的按次采集的zbuuid
//            if (aczbuuidList != null && aczbuuidList.size() > 0) {
//                final Map<String, Object> map = new HashMap<>();
//                for (int i = aczbuuidList.size() - 1; i >= 0; i--) {
//                    final Map<String, Object> zbuuidMap = aczbuuidList.get(i);
//                    String zbuuid = (String) zbuuidMap.get("zbuuid");
//                    final String comzbuuid = (String) map.get(zbuuid);
//                    if (comzbuuid == null) {
//                        map.put(zbuuid, zbuuid);
//                    } else {
//                        aczbuuidList.remove(i);
//                    }
//                }
//            }
//            //3.按次采集  根据zbuuid把同一个zbuuid 下的更正 删除 组装
//            for (Map<String, Object> map : aczbuuidList) {
//                String zbuuid = (String) map.get("zbuuid");
//                List<YhshbcjxxbDTO> updatedzcjxxList = new ArrayList<>();
//                List<YhshbcjxxbDTO> deletecdzjxxList = new ArrayList<>();
//                List<YhscjkblxxDTO> yhscjkblxxList = new ArrayList<>();
//                for (YhshbcjxxbDTO yhshbcjxxbDTO : newAccjxxList) {
//                    String gzbz = yhshbcjxxbDTO.getGzbz();
//                    if (zbuuid.equals(yhshbcjxxbDTO.getZbuuid())) { //获取同一个zbuuid下更正和delete的明细
//                        if ("delete".equals(gzbz)) {
//                            deletecdzjxxList.add(yhshbcjxxbDTO);
////                            if (!GyUtils.isNull(yhshbcjxxbDTO.getYhscjkblxxList())) {
////                                yhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
////                            }
//                        } else if ("update".equals(gzbz)) {
//                            updatedzcjxxList.add(yhshbcjxxbDTO);
//                            if (!GyUtils.isNull(yhshbcjxxbDTO.getYhscjkblxxList())) {
//                                yhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                            }
//                        } else if ("insert".equals(gzbz)) { //新增
//
//                        } else { //无标志默认更新
//                            updatedzcjxxList.add(yhshbcjxxbDTO);
//                            if (!GyUtils.isNull(yhshbcjxxbDTO.getYhscjkblxxList())) {
//                                yhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                            }
//                        }
//                    }
//                }
//                final Object[] resObj = new Object[4];
//                resObj[0] = updatedzcjxxList;
//                resObj[1] = deletecdzjxxList;
//                resObj[2] = yhscjkblxxList;//记录可变列
//                resObj[3] = new ArrayList<>();
//                aczbuuiddzMap.put(zbuuid, resObj);
//            }
//            List<YhshbcjxxbDTO> acxzinsertcjxxList = new ArrayList<>();//按次采集 记录已采集中没有匹配到属期的新增
//            //4.按次 判断按次新增的数据 相同属期入同一个属期  不同属期按新增属期处理
//            for (int i = acinsertcjxxList.size() - 1; i >= 0; i--) {
//                YhshbcjxxbDTO yhshbcjxxbDTO = acinsertcjxxList.get(i);
//                String iskssqq = yhshbcjxxbDTO.getSkssqq().substring(0, 10);
//                String iskssqz = yhshbcjxxbDTO.getSkssqz().substring(0, 10);
//                if (!GyUtils.isNull(aczbuuidList)) {
//                    List<YhshbcjxxbDTO> updateinsertcjxxList = new ArrayList<>();
//                    for (Map<String, Object> zbxxmap : aczbuuidList) {
//                        String uskssqq = (String) zbxxmap.get("skssqq");
//                        String uskssqz = (String) zbxxmap.get("skssqz");
//                        uskssqq = uskssqq.substring(0, 10);
//                        uskssqz = uskssqz.substring(0, 10);
//                        String zbuuid1 = (String) zbxxmap.get("zbuuid");
//                        if (iskssqq.equals(uskssqq) && iskssqz.equals(uskssqz)) { //属期相等
//                            Object[] obj = (Object[]) aczbuuiddzMap.get(zbuuid1);
//                            List<YhscjkblxxDTO> kblList = (List<YhscjkblxxDTO>) obj[2];
//                            updateinsertcjxxList = (List<YhshbcjxxbDTO>) obj[3];//先取出再叠加
//                            updateinsertcjxxList.add(yhshbcjxxbDTO);
//                            kblList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                            obj[2] = kblList;
//                            obj[3] = updateinsertcjxxList;
//                            break;
//                        }
//                    }
//                    if (GyUtils.isNull(updateinsertcjxxList)) { //未和已经采集的税源匹配上
//                        acxzinsertcjxxList.add(yhshbcjxxbDTO);
//                    }
//                } else {
//                    acxzinsertcjxxList.add(yhshbcjxxbDTO);
//                }
//            }
//            List<Map<String, Object>> acxzList = new ArrayList<>();
//            //把本次新增剩下的明细  相同属期组装一起
//            for (int i = 0; i <= acxzinsertcjxxList.size() - 1; i++) {
//                YhshbcjxxbDTO yhshbcjxxbDTO = acxzinsertcjxxList.get(i);
//                String iskssqq = yhshbcjxxbDTO.getSkssqq();
//                String iskssqz = yhshbcjxxbDTO.getSkssqz();
//                Map<String, Object> acxzMap = new HashMap<>();
//                acxzMap.put("skssqq", iskssqq);
//                acxzMap.put("skssqz", iskssqz);
//                List<YhshbcjxxbDTO> saveinsertcjxxList = new ArrayList<>();//按次采集
//                List<YhscjkblxxDTO> saveyhscjkblxxList = new ArrayList<>();//按次采集
//                saveinsertcjxxList.add(yhshbcjxxbDTO);
//                saveyhscjkblxxList.addAll(yhshbcjxxbDTO.getYhscjkblxxList());
//                for (int j = acxzinsertcjxxList.size() - 1; j >= 0; j--) {
//                    if (i == j) {
//                        break;
//                    }
//                    YhshbcjxxbDTO uyhshbcjxxbDTO = acxzinsertcjxxList.get(j);
//                    String uskssqq = uyhshbcjxxbDTO.getSkssqq();
//                    String uskssqz = uyhshbcjxxbDTO.getSkssqz();
//                    if (iskssqq.equals(uskssqq) && iskssqz.equals(uskssqz)) {
//                        saveinsertcjxxList.add(uyhshbcjxxbDTO);
//                        saveyhscjkblxxList.addAll(uyhshbcjxxbDTO.getYhscjkblxxList());
//                        acxzinsertcjxxList.remove(j);
//                    }
//                }
//                acxzMap.put("yhshbcjxxList", saveinsertcjxxList);
//                acxzMap.put("yhscjkblxxList", saveyhscjkblxxList);
//                acxzList.add(acxzMap);
//            }
//            List<Map<String, Object>> xzList = new ArrayList<>();
//            xzList.addAll(acxzList);
//            xzList.addAll(aqxzList);
//            //合并按次采集或者按期采集的更正税源明细
//            zbuuiddzMap.putAll(aqzbuuiddzMap);
//            zbuuiddzMap.putAll(aczbuuiddzMap);
//            zbuuidList.addAll(aqzbuuidList);
//            zbuuidList.addAll(aczbuuidList);
//            yhsCjCwgzResDTO = this.saveCjAndGzNew(yhsCjCwgzReqDTO, zbuuidList, zbuuiddzMap, xzList);
//        }
//        //2022.07.01前旧版印花税采集  旧版只有按次
////        if (!GyUtils.isNull(yhscjCjxxbDTOList)) {
////            List<YhscjCjxxbDTO> newAccjxxList = yhscjCjxxbDTOList;
////            Map aczbuuiddzMap = new HashMap<>();//按次采集  主表uuid与更正 删除 新增对照map
////            List<Map<String, Object>> aczbuuidList = new ArrayList<>();
////            //按次 1.把zbuuid 提取 更正 新增 删除 分组
////            List<YhscjCjxxbDTO> acinsertcjxxList = new ArrayList<>();
////            List<YhscjCjxxbDTO> acupdatecjxxList = new ArrayList<>();
////            List<YhscjCjxxbDTO> acdeletecjxxList = new ArrayList<>();
////            for (YhscjCjxxbDTO yhscjCjxxbDTO : newAccjxxList) {
////                String ssjmxzDm = yhscjCjxxbDTO.getSsjmxzDm();
////                String zspmMc = yhscjCjxxbDTO.getZspmMc();
////                Double jmse = yhscjCjxxbDTO.getJmse() == null ? 0.00 : yhscjCjxxbDTO.getJmse();
////                if (jmse > 0 && GyUtils.isNull(ssjmxzDm)) {
////                    yhsCjCwgzResDTO.setRescode("-1");
////                    yhsCjCwgzResDTO.setResmsg(zspmMc + "的减免税额大于0时，减免性质代码不能为空，请修改！");
////                    return yhsCjCwgzResDTO;
////                }
////                String gzbz = yhscjCjxxbDTO.getGzbz();
////                Map<String, Object> zbuuidsqMap = new HashMap<>();
////                zbuuidsqMap.put("skssqq", yhscjCjxxbDTO.getSkssqq());
////                zbuuidsqMap.put("skssqz", yhscjCjxxbDTO.getSkssqz());
////                if ("delete".equals(gzbz)) {
////                    zbuuidsqMap.put("zbuuid", yhscjCjxxbDTO.getZbuuid());
////                    aczbuuidList.add(zbuuidsqMap);
////                    acdeletecjxxList.add(yhscjCjxxbDTO);
////                } else if ("update".equals(gzbz)) {
////                    zbuuidsqMap.put("zbuuid", yhscjCjxxbDTO.getZbuuid());
////                    acupdatecjxxList.add(yhscjCjxxbDTO);
////                    aczbuuidList.add(zbuuidsqMap);
////                } else if ("insert".equals(gzbz)) { //新增没有zbuuid
////                    acinsertcjxxList.add(yhscjCjxxbDTO);
////                } else { //无标志默认更新
////                    if (!GyUtils.isNull(yhscjCjxxbDTO.getZbuuid())) {
////                        zbuuidsqMap.put("zbuuid", yhscjCjxxbDTO.getZbuuid());
////                        acupdatecjxxList.add(yhscjCjxxbDTO);
////                        aczbuuidList.add(zbuuidsqMap);
////                    }
////                }
////            }
////            //2.过滤掉重复的按次采集的zbuuid
////            if (aczbuuidList != null && aczbuuidList.size() > 0) {
////                final Map<String, Object> map = new HashMap<String, Object>();
////                for (int i = aczbuuidList.size() - 1; i >= 0; i--) {
////                    final Map<String, Object> zbuuidMap = aczbuuidList.get(i);
////                    String zbuuid = (String) zbuuidMap.get("zbuuid");
////                    final String comzbuuid = (String) map.get(zbuuid);
////                    if (comzbuuid == null) {
////                        map.put(zbuuid, zbuuid);
////
////                    } else {
////                        aczbuuidList.remove(i);
////                    }
////                }
////            }
////            //3.按次采集  根据zbuuid把同一个zbuuid 下的更正 删除 组装
////            for (Map<String, Object> map : aczbuuidList) {
////                String zbuuid = (String) map.get("zbuuid");
////                List<YhscjCjxxbDTO> updatedzcjxxList = new ArrayList<>();
////                List<YhscjCjxxbDTO> deletecdzjxxList = new ArrayList<>();
////                for (YhscjCjxxbDTO yhscjCjxxbDTO : newAccjxxList) {
////                    String gzbz = yhscjCjxxbDTO.getGzbz();
////                    if (zbuuid.equals(yhscjCjxxbDTO.getZbuuid())) { //获取同一个zbuuid下更正和delete的明细
////                        if ("delete".equals(gzbz)) {
////                            deletecdzjxxList.add(yhscjCjxxbDTO);
////                        } else if ("update".equals(gzbz)) {
////                            updatedzcjxxList.add(yhscjCjxxbDTO);
////                        } else if ("insert".equals(gzbz)) { //新增
////
////                        } else { //无标志默认更新
////                            updatedzcjxxList.add(yhscjCjxxbDTO);
////                        }
////                    }
////                }
////                final Object[] resObj = new Object[3];
////                resObj[0] = updatedzcjxxList;
////                resObj[1] = deletecdzjxxList;
////                resObj[2] = new ArrayList<>();
////                aczbuuiddzMap.put(zbuuid, resObj);
////            }
////            List<YhscjCjxxbDTO> acxzinsertcjxxList = new ArrayList<>();//按次采集 记录已采集中没有匹配到属期的新增
////            //4.按次 判断按次新增的数据 相同属期入同一个属期  不同属期按新增属期处理
////            for (int i = acinsertcjxxList.size() - 1; i >= 0; i--) {
////                YhscjCjxxbDTO yhscjCjxxbDTO = acinsertcjxxList.get(i);
////                String iskssqq = yhscjCjxxbDTO.getSkssqq().substring(0, 10);
////                String iskssqz = yhscjCjxxbDTO.getSkssqz().substring(0, 10);
////                if (!GyUtils.isNull(aczbuuidList)) {
////                    List<YhscjCjxxbDTO> updateinsertcjxxList = new ArrayList<>();
////                    for (Map<String, Object> zbxxmap : aczbuuidList) {
////                        String uskssqq = (String) zbxxmap.get("skssqq");
////                        String uskssqz = (String) zbxxmap.get("skssqz");
////                        uskssqq = uskssqq.substring(0, 10);
////                        uskssqz = uskssqz.substring(0, 10);
////                        String zbuuid1 = (String) zbxxmap.get("zbuuid");
////                        if (iskssqq.equals(uskssqq) && iskssqz.equals(uskssqz)) { //属期相等
////                            Object[] obj = (Object[]) aczbuuiddzMap.get(zbuuid1);
////                            updateinsertcjxxList = (List<YhscjCjxxbDTO>) obj[2];
////                            updateinsertcjxxList.add(yhscjCjxxbDTO);
////                            obj[2] = updateinsertcjxxList;
////                            break;
////                        }
////                        if (GyUtils.isNull(updateinsertcjxxList)) {
////                            acxzinsertcjxxList.add(yhscjCjxxbDTO);
////                        }
////                    }
////                } else {
////                    acxzinsertcjxxList.add(yhscjCjxxbDTO);
////                }
////            }
////            List<Map<String, Object>> acxzList = new ArrayList<>();
////            //把本次新增剩下的明细  相同属期组装一起
////            for (int i = 0; i <= acxzinsertcjxxList.size() - 1; i++) {
////                YhscjCjxxbDTO yhscjCjxxbDTO = acxzinsertcjxxList.get(i);
////                String iskssqq = yhscjCjxxbDTO.getSkssqq();
////                String iskssqz = yhscjCjxxbDTO.getSkssqz();
////                Map<String, Object> acxzMap = new HashMap<>();
////                acxzMap.put("skssqq", iskssqq);
////                acxzMap.put("skssqz", iskssqz);
////                List<YhscjCjxxbDTO> saveinsertcjxxList = new ArrayList<>();//按次采集
////                saveinsertcjxxList.add(yhscjCjxxbDTO);
////                for (int j = acxzinsertcjxxList.size() - 1; j >= 0; j--) {
////                    if (i == j) {
////                        break;
////                    }
////                    YhscjCjxxbDTO uyhscjCjxxbDTO = acxzinsertcjxxList.get(j);
////                    String uskssqq = uyhscjCjxxbDTO.getSkssqq();
////                    String uskssqz = uyhscjCjxxbDTO.getSkssqz();
////                    if (iskssqq.equals(uskssqq) && iskssqz.equals(uskssqz)) {
////                        saveinsertcjxxList.add(uyhscjCjxxbDTO);
////                        acxzinsertcjxxList.remove(j);
////                    }
////                }
////                acxzMap.put("yhscjcjxxList", saveinsertcjxxList);
////                acxzList.add(acxzMap);
////            }
////            String tempStr = "";
////            if ("-2".equals(yhsCjCwgzResDTO.getRescode())){
////                tempStr = yhsCjCwgzResDTO.getResmsg();
////            }
////            yhsCjCwgzResDTO = this.saveCjAndGzOld(yhsCjCwgzReqDTO, aczbuuidList, aczbuuiddzMap, acxzList);
////            if (!GyUtils.isNull(tempStr) && !"-1".equals(yhsCjCwgzResDTO.getRescode())){
////                yhsCjCwgzResDTO.setRescode("-2");
////                yhsCjCwgzResDTO.setResmsg(tempStr);
////            }
////        }
//        return yhsCjCwgzResDTO;
//    }
//
////
//
//    /**
//     * 校验印花税品目是否可按次申报
//     *
//     * @param yhsCjCwgzReqDTO
//     * @param yhsCjCwgzResDTO
//     * @return
//     */
//    public YhsCjCwgzResDTO checkYhssfksb(YhsCjCwgzReqDTO yhsCjCwgzReqDTO, YhsCjCwgzResDTO yhsCjCwgzResDTO) {
//        final StringBuffer msg = new StringBuffer();
//        final List<YhscjCjxxbDTO> yhscjCjxxbList = yhsCjCwgzReqDTO.getYhscjCjxxbList();
//        List<YhshbcjxxbDTO> yhshbcjxxbList = yhsCjCwgzReqDTO.getYhshbcjxxbList();
//        yhshbcjxxbList = yhshbcjxxbList.stream().filter(m -> !("delete").equals(m.getGzbz())).collect(Collectors.toList());
//        //有产品转移书据且未认定到子目，且纳税人未录入子目，阻断提交并提示信息(立法前不校验)
//        // 立法后
//        if (!GyUtils.isNull(yhshbcjxxbList)) {
//            for (YhshbcjxxbDTO yhshbcjxxbDTO : yhshbcjxxbList) {
//                if ("101110200".equals(yhshbcjxxbDTO.getZspmDm()) && GyUtils.isNull(yhshbcjxxbDTO.getZszmDm())) {
//                    msg.append("征收品目“产权转移书据”对应的征收子目为空，请联系税务机关补充税（费）种认定信息。");
//                }
//            }
//        }
//        if (msg.length() > 0) {
//            yhsCjCwgzResDTO.setRescode("-3");
//            yhsCjCwgzResDTO.setResmsg(msg.toString());
//            return yhsCjCwgzResDTO;
//        }
//        //判断页面上存在同品目同子目采集多条税源
//        // 立法前(立法前只有按次，且没有子目)
//        if (!GyUtils.isNull(yhscjCjxxbList)) {
//            final Map<String, Map<String, String>> map = new HashMap<>();
//            for (YhscjCjxxbDTO yhscjCjxxbDTO : yhscjCjxxbList) {
//                Map<String, String> pmmap = map.get(yhscjCjxxbDTO.getZspmDm());
//                if (GyUtils.isNull(pmmap)) {
//                    pmmap = new HashMap<>();
//                    pmmap.put("skssqq", yhscjCjxxbDTO.getSkssqq());
//                    pmmap.put("skssqz", yhscjCjxxbDTO.getSkssqz());
//                    pmmap.put("nsqxDm", yhscjCjxxbDTO.getNsqxDm());
//                    map.put(yhscjCjxxbDTO.getZspmDm(), pmmap);
//                } else {
//                    if (pmmap.get("skssqq").equals(yhscjCjxxbDTO.getSkssqq())
//                            && pmmap.get("skssqz").equals(yhscjCjxxbDTO.getSkssqz())) {
//                        msg.append("存在多条相同属期相同品目（子目）的采集信息，请确认采集信息是否正确。");
//                        yhsCjCwgzResDTO.setRescode("-3");
//                        yhsCjCwgzResDTO.setResmsg(msg.toString());
//                        return yhsCjCwgzResDTO;
//                    }
//                }
//            }
//        }
//        // 立法后
//        if (!GyUtils.isNull(yhshbcjxxbList)) {
//            final Map<String, Map<String, String>> map = new HashMap<>();
//            for (YhshbcjxxbDTO yhshbcjxxbDTO : yhshbcjxxbList) {
//                Map<String, String> pmzmmap = map.get(GyUtils.isNull(yhshbcjxxbDTO.getZszmDm()) ? yhshbcjxxbDTO.getZspmDm() : yhshbcjxxbDTO.getZspmDm() + yhshbcjxxbDTO.getZszmDm());
//                if (GyUtils.isNull(pmzmmap)) {
//                    pmzmmap = new HashMap<>();
//                    pmzmmap.put("skssqq", yhshbcjxxbDTO.getSkssqq());
//                    pmzmmap.put("skssqz", yhshbcjxxbDTO.getSkssqz());
//                    pmzmmap.put("nsqxDm", yhshbcjxxbDTO.getNsqxDm());
//                    if ("101110502".equals(yhshbcjxxbDTO.getZspmDm())
//                            && "10".equals(yhshbcjxxbDTO.getNsqxDm())
//                            && (yhshbcjxxbDTO.getSkssqq().startsWith("2022") || yhshbcjxxbDTO.getSkssqq().startsWith("2023"))) {
//                        pmzmmap.put("skssqq", yhshbcjxxbDTO.getSkssqq().substring(0, 4) + "-01-01");
//                    }
//                    map.put(GyUtils.isNull(yhshbcjxxbDTO.getZszmDm()) ? yhshbcjxxbDTO.getZspmDm() : yhshbcjxxbDTO.getZspmDm() + yhshbcjxxbDTO.getZszmDm(), pmzmmap);
//                } else {
//                    if (pmzmmap.get("skssqq").equals(yhshbcjxxbDTO.getSkssqq())
//                            && pmzmmap.get("skssqz").equals(yhshbcjxxbDTO.getSkssqz())
//                            && !GyUtils.isEquals("11", yhshbcjxxbDTO.getNsqxDm())) {
//                        msg.append("存在多条相同属期相同品目（子目）的采集信息，请确认采集信息是否正确。");
//                        yhsCjCwgzResDTO.setRescode("-3");
//                        yhsCjCwgzResDTO.setResmsg(msg.toString());
//                        return yhsCjCwgzResDTO;
//                    } else {
//                        if (!"11".equals(pmzmmap.get("nsqxDm")) && !"11".equals(yhshbcjxxbDTO.getNsqxDm())) {
//                            msg.append("同一征收品目（子目）不允许同时按年和按季采集。");
//                            yhsCjCwgzResDTO.setRescode("-3");
//                            yhsCjCwgzResDTO.setResmsg(msg.toString());
//                            return yhsCjCwgzResDTO;
//                        } else if ("11".equals(pmzmmap.get("nsqxDm")) && !"11".equals(yhshbcjxxbDTO.getNsqxDm())) {
//                            Date jlskssqq = GyUtils.cast2Date(pmzmmap.get("skssqq"));
//                            Date jlskssqz = GyUtils.cast2Date(pmzmmap.get("skssqz"));
//                            Date dqskssqq = GyUtils.cast2Date(yhshbcjxxbDTO.getSkssqq());
//                            Date dqskssqz = GyUtils.cast2Date(yhshbcjxxbDTO.getSkssqz());
//                            if (jlskssqq.compareTo(dqskssqq) >= 0 && jlskssqz.compareTo(dqskssqz) <= 0) {
//                                msg.append("同一征收品目（子目）不允许同时按次和按期采集。");
//                                yhsCjCwgzResDTO.setRescode("-3");
//                                yhsCjCwgzResDTO.setResmsg(msg.toString());
//                                return yhsCjCwgzResDTO;
//                            }
//                        } else if (!"11".equals(pmzmmap.get("nsqxDm")) && "11".equals(yhshbcjxxbDTO.getNsqxDm())) {
//                            Date jlskssqq = GyUtils.cast2Date(pmzmmap.get("skssqq"));
//                            Date jlskssqz = GyUtils.cast2Date(pmzmmap.get("skssqz"));
//                            Date dqskssqq = GyUtils.cast2Date(yhshbcjxxbDTO.getSkssqq());
//                            Date dqskssqz = GyUtils.cast2Date(yhshbcjxxbDTO.getSkssqz());
//                            if (dqskssqq.compareTo(jlskssqq) >= 0 && dqskssqz.compareTo(jlskssqz) <= 0) {
//                                msg.append("同一征收品目（子目）不允许同时按次和按期采集。");
//                                yhsCjCwgzResDTO.setRescode("-3");
//                                yhsCjCwgzResDTO.setResmsg(msg.toString());
//                                return yhsCjCwgzResDTO;
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        if (msg.length() > 0) {
//            yhsCjCwgzResDTO.setRescode("-3");
//            yhsCjCwgzResDTO.setResmsg(msg.toString());
//        }
//        return yhsCjCwgzResDTO;
//    }
//
//    /**
//     * 新版 采集和更正
//     *
//     * @param
//     * @return
//     */
//    public YhsCjCwgzResDTO saveCjAndGzNew(YhsCjCwgzReqDTO yhsCjCwgzReqDTO, List<Map<String, Object>> zbuuidList, Map<String, Object> zbuuiddzMap, List<Map<String, Object>> xzList) {
//        YhsCjCwgzResDTO yhsCjCwgzResDTO = new YhsCjCwgzResDTO();
//        final List<YhscjBtxxDTO> yhscjBtxxPlsbList = new ArrayList<>();
//        List<YhscjBtxxDTO> yhscjBtxxList = new ArrayList<>();
//
//        //查询纳税人信息
//        final ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
//        reqVO.setDjxh(yhsCjCwgzReqDTO.getYhscjNsrxx().getDjxh());
//        reqVO.setNsrsbh(yhsCjCwgzReqDTO.getYhscjNsrxx().getNsrsbh());
//        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVO = nsrxxApi.getNsrxxByNsrsbh(reqVO);
//        if (GyUtils.isNull(nsrxxVO)) {
//            throw new RuntimeException();
//        }
//        final List<JbxxmxsjVO> qyjbxx = nsrxxVO.getData().getJbxxmxsj();
//
//        for (Map<String, Object> acxzMap : xzList) { //按次和按期采集
//            final String skssqq = (String) acxzMap.get("skssqq");
//            final String skssqz = (String) acxzMap.get("skssqz");
//            try {
//                List<YhscjkblxxDTO> yhscjkblxxList = (List<YhscjkblxxDTO>) acxzMap.get("yhscjkblxxList");
//                List<YhshbcjxxbDTO> yhshbcjxxbList = (List<YhshbcjxxbDTO>) acxzMap.get("yhshbcjxxList");
//                YhscjNsrxxDTO yhscjNsrxx = yhsCjCwgzReqDTO.getYhscjNsrxx();
//                yhscjNsrxx.setNsqxDm(yhshbcjxxbList.get(0).getNsqxDm());
//                yhscjNsrxx.setSkssqq(skssqq);
//                yhscjNsrxx.setSkssqz(skssqz);
//                //TODO 保存印花税税源采集信息（立法后）
//                String zbuuid = yhshbcjxxbList.get(0).getZbuuid();
//                if (GyUtils.isNull(zbuuid)) {
//                    //保存至本地表
//                    //表头信息表UUID、数据交换服务YWUUID
//                    zbuuid = GyUtils.getUuid();
//                    YhscjBtxxDTO yhscjBtxx = BeanUtils.toBean(yhsCjCwgzReqDTO.getYhscjNsrxx(), YhscjBtxxDTO.class);
//                    yhscjBtxx.setUuid(zbuuid);
//                    this.insertYhslfcjxx(yhscjBtxx, yhshbcjxxbList, yhscjkblxxList, null, yhsCjCwgzReqDTO.getSjjg(), yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getYwqdDm());
//                } else {
//                    //更新至本地表
//                    this.updateYhslfcjxx(yhshbcjxxbList, yhscjkblxxList, null, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjjg(), yhscjNsrxx.getDjxh(), yhsCjCwgzReqDTO.getYwqdDm());
//                }
//                //通过数据交换服务调用乐企接口
////                this.yhslfcjBc(zbuuid, yhscjNsrxx, yhshbcjxxbList, yhscjkblxxList);
//                List<YhshbcjxxbDTO> updatecjxxList = null;
//                this.bcYhsCjxxByLq(zbuuid, "", yhscjNsrxx, updatecjxxList, yhshbcjxxbList, yhscjkblxxList, qyjbxx);
//                //gt3Invoker.invoke("SWZJ.HXZG.SB.YHSLFCJBC", expand, hxzgsb11050Request, HXZGSB11050Response.class);
//            } catch (Exception e) {
//                log.info("采集失败,错误信息:", e);
//                yhsCjCwgzResDTO.setResmsg("采集失败");
//                yhsCjCwgzResDTO.setRescode("-1");
//            }
//        }
//        yhsCjCwgzResDTO.setYhscjBtxxList(BeanUtils.toBean(yhscjBtxxList, YhscjBtxxDTO.class));
//        try {
//            if (!GyUtils.isNull(zbuuidList)) { //调用更正
//                final YhsCjCwgzResDTO tempDTO = this.cwgzNew(yhsCjCwgzReqDTO, zbuuidList, zbuuiddzMap, yhscjBtxxPlsbList, qyjbxx);
//                if (!GyUtils.isNull(yhscjBtxxList) && !GyUtils.isNull(tempDTO.getYhscjBtxxList())) {
//                    tempDTO.getYhscjBtxxList().addAll(BeanUtils.toBean(yhscjBtxxList, YhscjBtxxDTO.class));
//                }
//                return tempDTO;
//            } else {
//                if (!"-1".equals(yhsCjCwgzResDTO.getRescode())) {
//                    yhsCjCwgzResDTO.setResmsg("采集成功");
//                    yhsCjCwgzResDTO.setRescode("1001");
//                }
//                return yhsCjCwgzResDTO;
//            }
//        } catch (Exception e) {
//            log.info("更正失败，错误信息：", e);
//            yhsCjCwgzResDTO.setResmsg("更正失败");
//            yhsCjCwgzResDTO.setRescode("-1");
//            return yhsCjCwgzResDTO;
//        }
//    }
//
//    private void yhslfcjBc(String zbuuid, YhscjNsrxxDTO yhscjNsrxx, List<YhshbcjxxbDTO> yhshbcjxxbList, List<YhscjkblxxDTO> yhscjkblxxList) {
//        //乐企保存印花税税源采集信息接口请求报文
//        final LqYhsLfcjBcRequestDTO lqYhsLfcjBcRequestDTO = new LqYhsLfcjBcRequestDTO();
//        lqYhsLfcjBcRequestDTO.setCjkblxxGrid(yhscjkblxxList);
//        lqYhsLfcjBcRequestDTO.setCjxxGrid(yhshbcjxxbList);
//        lqYhsLfcjBcRequestDTO.setNsrxxFormVO(yhscjNsrxx);
//        //通过数据交换服务调用乐企接口
//        final String ywbw = JsonUtils.toJson(lqYhsLfcjBcRequestDTO);
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("BC00000001");
//        sjjhDTO.setYwbm(YzpzzlEnum.YHS.getDm());
//        sjjhDTO.setYwuuid(zbuuid);
//        sjjhDTO.setDjxh(yhscjNsrxx.getDjxh());
//        sjjhDTO.setNsrsbh(yhscjNsrxx.getNsrsbh());
//        sjjhDTO.setXzqhszDm(yhscjNsrxx.getXzqhszDm());
//        sjjhDTO.setBwnr(ywbw);
//        sjjhService.saveSjjhJob(sjjhDTO);
//    }
//
//    private void bcYhsCjxxByLq(String zbuuid, String hxbuuid, YhscjNsrxxDTO yhscjNsrxx, List<YhshbcjxxbDTO> updatecjxxList, List<YhshbcjxxbDTO> insertcjxxList, List<YhscjkblxxDTO> yhscjkblxxList, List<JbxxmxsjVO> qyjbxx) {
//        final LqBcYhsYwRequestDTO lqBcYhsYwRequestDTO = new LqBcYhsYwRequestDTO();
//        //拼装基础报文
//        LqBcYhsJcbwRequestDTO jcbw = new LqBcYhsJcbwRequestDTO();
//        jcbw.setJbr(qyjbxx.get(0).getBsrxm());
//        jcbw.setJbrsfzjlxDm(qyjbxx.get(0).getBsrsfzjlxDm());
//        jcbw.setJbrsfzjhm(qyjbxx.get(0).getBsrsfzjhm());
//
//        //拼装业务报文
//        List<LqBcYhsYwbwRequestDTO> ywbwList = new ArrayList<>();
//        LqBcYhsYwbwRequestDTO ywbw = new LqBcYhsYwbwRequestDTO();
//        ywbw.setSwsxDm("SXN022006701");
//        ywbw.setDjxh(yhscjNsrxx.getDjxh());
//        ywbw.setSwjgDm(qyjbxx.get(0).getZgswjDm());
//        ywbw.setSkssqq(yhscjNsrxx.getSkssqq());
//        ywbw.setSkssqz(yhscjNsrxx.getSkssqz());
//
//        LqBcYhsYwSbsjRequestDTO sbsj = new LqBcYhsYwSbsjRequestDTO();
//
//        //判断保存或更正
//        String syczbz = YHSBC;
//        if (GyUtils.isNotNull(updatecjxxList)) {
//            syczbz = YHSGZ;
//            sbsj.setZbuuid(hxbuuid);
//        }
//        ywbw.setSyczbz(syczbz);
//        ywbw.setMxid(GyUtils.getUuid());
//        //组装申报数据
//        sbsj.setNsrxxFormVO(BeanUtils.toBean(yhscjNsrxx, YhscjNsrxxDTO.class));
//        sbsj.setInsertCjxxGrid(BeanUtils.toBean(insertcjxxList, YhshbcjxxbDTO.class));
//        sbsj.setUpdateCjxxGrid(BeanUtils.toBean(updatecjxxList, YhshbcjxxbDTO.class));
//        sbsj.setCjkblxxGrid(BeanUtils.toBean(yhscjkblxxList, YhscjkblxxDTO.class));
//        ywbw.setSbsj(Base64Utils.encode(JsonUtils.toJson(sbsj)));
//
//        lqBcYhsYwRequestDTO.setRequestId(GyUtils.getUuid());
//        lqBcYhsYwRequestDTO.setJcbw(jcbw);
//        ywbwList.add(ywbw);
//        lqBcYhsYwRequestDTO.setYwbw(ywbwList);
//
//        //组装数据交换数据
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("**********");
//        sjjhDTO.setYwbm(YzpzzlEnum.YHS.getDm());
//        sjjhDTO.setYwuuid(zbuuid);
//        sjjhDTO.setDjxh(yhscjNsrxx.getDjxh());
//        sjjhDTO.setNsrsbh(yhscjNsrxx.getNsrsbh());
//        sjjhDTO.setXzqhszDm(yhscjNsrxx.getXzqhszDm());
//        sjjhDTO.setBwnr(JsonUtils.toJson(lqBcYhsYwRequestDTO));
//        sjjhService.saveSjjhJob(sjjhDTO);
//
//    }
//
//    /**
//     * 错误更正new
//     *
//     * @param yhsCjCwgzReqDTO
//     * @param zbuuidList
//     * @param zbuuiddzMap
//     * @return
//     */
//    @Override
//    public YhsCjCwgzResDTO cwgzNew(YhsCjCwgzReqDTO yhsCjCwgzReqDTO, List<Map<String, Object>> zbuuidList, Map<String, Object> zbuuiddzMap, List<YhscjBtxxDTO> yhscjBtxxPlsbList, List<JbxxmxsjVO> qyjbxx) {
//        YhsCjCwgzResDTO yhsCjCwgzResDTO = new YhsCjCwgzResDTO();
//        YhscjNsrxxDTO yhscjNsrxx = yhsCjCwgzReqDTO.getYhscjNsrxx();
//        List<YhscjBtxxDTO> yhscjBtxxList = new ArrayList<>();
//        for (Map<String, Object> map : zbuuidList) {
//            String zbuuid = (String) map.get("zbuuid");
//            String hxbuuid = (String) map.get("hxbuuid");
//            final Object[] obj = (Object[]) zbuuiddzMap.get(zbuuid);
//            List<YhshbcjxxbDTO> updatecjxxList = (List<YhshbcjxxbDTO>) obj[0];
//            List<YhshbcjxxbDTO> insertcjxxList = (List<YhshbcjxxbDTO>) obj[3];
//            List<YhshbcjxxbDTO> deletecjxxList = (List<YhshbcjxxbDTO>) obj[1];
//            List<YhscjkblxxDTO> yhscjkblxxList = (List<YhscjkblxxDTO>) obj[2];
//            YhscjBtxxDTO yhscjBtxx = BeanUtils.toBean(yhsCjCwgzReqDTO.getYhscjNsrxx(), YhscjBtxxDTO.class);
//            yhscjBtxx.setUuid(zbuuid);
//            String nsqxDm = "11";
//            String skssqq = "";
//            String skssqz = "";
//            if (!GyUtils.isNull(updatecjxxList)) {
//                nsqxDm = updatecjxxList.get(0).getNsqxDm();//重新更新纳税期
//                skssqq = updatecjxxList.get(0).getSkssqq();
//                if ("101110502".equals(updatecjxxList.get(0).getZspmDm())
//                        && (updatecjxxList.get(0).getSkssqq().startsWith("2022") || updatecjxxList.get(0).getSkssqq().startsWith("2023"))
//                        && "10".equals(nsqxDm)) {
//                    skssqq = updatecjxxList.get(0).getSkssqq().substring(0, 4) + "-01-01";
//                }
//                skssqz = updatecjxxList.get(0).getSkssqz();
//                yhscjNsrxx.setNsqxDm(nsqxDm);
//                yhscjNsrxx.setSkssqq(skssqq);
//                yhscjNsrxx.setSkssqz(skssqz);
//                //更新至本地表
//                this.updateYhslfcjxx(updatecjxxList, yhscjkblxxList, null, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjjg(), yhscjNsrxx.getDjxh(), yhsCjCwgzReqDTO.getYwqdDm());
//            }
////            //删除后剩下的税源可变列信息
////            List<YhscjkblxxDTO> ycjyhscjkblxxList = new ArrayList<>();
////            //删除后剩下的税源信息
//            List<YhshbcjxxbDTO> yhshbcjxxbDTOS = new ArrayList<>();
//            //删除的税源信息
//            List<YhscjkblxxDTO> deleteyhscjkblxxList = new ArrayList<>();
//            List<YhshbcjxxbDTO> yhshbcjxxbList = new ArrayList<>();
//            if (!GyUtils.isNull(deletecjxxList)) {
//
//                nsqxDm = deletecjxxList.get(0).getNsqxDm();//重新更新纳税期
//                skssqq = deletecjxxList.get(0).getSkssqq();
//                if ("101110502".equals(deletecjxxList.get(0).getZspmDm())
//                        && (deletecjxxList.get(0).getSkssqq().startsWith("2022") || deletecjxxList.get(0).getSkssqq().startsWith("2023"))
//                        && "10".equals(nsqxDm)) {
//                    skssqq = deletecjxxList.get(0).getSkssqq().substring(0, 4) + "-01-01";
//                }
//                skssqz = deletecjxxList.get(0).getSkssqz();
//                yhscjNsrxx.setNsqxDm(nsqxDm);
//                yhscjNsrxx.setSkssqq(skssqq);
//                yhscjNsrxx.setSkssqz(skssqz);
//                //TODO 乐企错误更正采集初始化
//                //调用乐企印花税错误更正初始化接口
////          final LqYhssyxxCxResponseDTO respDTO = this.yhsCwgzCjInitData(zbuuid,yhscjNsrxx);
//                final LqYhssyxxCxResponseDTO respDTO = this.yhsCwgzCjInitDataBd(zbuuid);
//                yhshbcjxxbList = respDTO.getSymxGrid();
//                if (yhshbcjxxbList.size() > 1) {
//
//
//                    //调用作废接口
//                    yhshbcjxxbDTOS.addAll(respDTO.getSymxGrid());
////                    if (GyUtils.isNotNull(respDTO.getYhskblxxGrid())) {
////                        ycjyhscjkblxxList.addAll(respDTO.getYhskblxxGrid());
////                    }
////                    //过滤作废明细
//                    final Iterator<YhshbcjxxbDTO> yhsiterator = yhshbcjxxbDTOS.iterator();
//                    while (yhsiterator.hasNext()) {
//                        YhshbcjxxbDTO yhshbcjxx = yhsiterator.next();
//                        for (YhshbcjxxbDTO del : deletecjxxList) {
//                            String uuid = yhshbcjxx.getUuid();
//                            if (uuid.equals(del.getUuid())) {
//                                yhsiterator.remove();
//                            }
//                        }
//                    }
//                    if (GyUtils.isNull(yhshbcjxxbDTOS)) {
//                        //调用乐企作废印花税税源信息
//                        this.zfYhsCjxxByLq(hxbuuid, yhscjNsrxx, qyjbxx);
//                        List<ZnsbNssbYhscjDO> savecjsyxxList = new ArrayList<>();
//                        QueryWrapperX<ZnsbNssbYhscjDO> queryWrapperX = new QueryWrapperX<>();
//                        queryWrapperX.lambda().eq(ZnsbNssbYhscjDO::getUuid, deletecjxxList.get(0).getUuid());
//                        final ZnsbNssbYhscjDO sbCxsYhslfsyxx = new ZnsbNssbYhscjDO();
//                        sbCxsYhslfsyxx.setBczt("20");
//                        sbCxsYhslfsyxx.setXgrsfid("");
//                        sbCxsYhslfsyxx.setXgrq(new Date());
//                        znsbNssbYhscjMapper.update(sbCxsYhslfsyxx, queryWrapperX);
//
//                        //作废主表
//                        QueryWrapperX<ZnsbNssbCxsbtxxDO> queryWrapperX1 = new QueryWrapperX<>();
//                        queryWrapperX1.lambda().eq(ZnsbNssbCxsbtxxDO::getUuid, zbuuid);
//                        ZnsbNssbCxsbtxxDO sbCxsCxssybtxx = new ZnsbNssbCxsbtxxDO();
//                        sbCxsCxssybtxx.setBczt("20");
//                        sbCxsCxssybtxx.setXgrsfid("");
//                        sbCxsCxssybtxx.setXgrq(new Date());
//                        znsbNssbCxsbtxxMapper.update(sbCxsCxssybtxx, queryWrapperX1);
//                    }
////                    //过滤作废明细对应的可变列
////                    final Iterator<YhscjkblxxDTO> iterator = ycjyhscjkblxxList.iterator();
////                    while (iterator.hasNext()) {
////                        YhscjkblxxDTO yhscjkblxx = iterator.next();
////                        String timebz = yhscjkblxx.getUuid();
////                        if (GyUtils.isNull(timebz)) {
////                            continue;
////                        }
////                        for (YhshbcjxxbDTO del : deletecjxxList) {
////                            if (timebz.equals(del.getUuid())) {
////                                iterator.remove();
////                                deleteyhscjkblxxList.add(yhscjkblxx);
////                            }
////                        }
////                    }
////
////                        //将同一zbuuid下的采集信息过滤掉删除的信息，放入updatecjxxList
////                        updatecjxxList.addAll(yhshbcjxxbDTOS);
////                    }
////                    if (GyUtils.isNotNull(ycjyhscjkblxxList)) {
////                        //将同一zbuuid下的可变列信息过滤掉删除的信息，放入yhscjkblxxList
////                        yhscjkblxxList.addAll(ycjyhscjkblxxList);
////                    }
//                } else if (yhshbcjxxbList.size() == 1) {
//                    //调用乐企作废印花税税源信息
//                    this.zfYhsCjxxByLq(hxbuuid, yhscjNsrxx, qyjbxx);
//                    List<ZnsbNssbYhscjDO> savecjsyxxList = new ArrayList<>();
//                    QueryWrapperX<ZnsbNssbYhscjDO> queryWrapperX = new QueryWrapperX<>();
//                    queryWrapperX.lambda().eq(ZnsbNssbYhscjDO::getUuid, deletecjxxList.get(0).getUuid());
//                    final ZnsbNssbYhscjDO sbCxsYhslfsyxx = new ZnsbNssbYhscjDO();
//                    sbCxsYhslfsyxx.setBczt("20");
//                    sbCxsYhslfsyxx.setXgrsfid("");
//                    sbCxsYhslfsyxx.setXgrq(new Date());
//                    znsbNssbYhscjMapper.update(sbCxsYhslfsyxx, queryWrapperX);
//
//                    //作废主表
//                    QueryWrapperX<ZnsbNssbCxsbtxxDO> queryWrapperX1 = new QueryWrapperX<>();
//                    queryWrapperX1.lambda().eq(ZnsbNssbCxsbtxxDO::getUuid, zbuuid);
//                    ZnsbNssbCxsbtxxDO sbCxsCxssybtxx = new ZnsbNssbCxsbtxxDO();
//                    sbCxsCxssybtxx.setBczt("20");
//                    sbCxsCxssybtxx.setXgrsfid("");
//                    sbCxsCxssybtxx.setXgrq(new Date());
//                    znsbNssbCxsbtxxMapper.update(sbCxsCxssybtxx, queryWrapperX1);
////                    //作废申报信息
////                    if (GyUtils.isNotNull(deletecjxxList.get(0).getHxbuuid())) {
////                        QueryWrapperX<ZnsbNssbSbrwDO> queryWrapperX1 = new QueryWrapperX<>();
////                        queryWrapperX1.lambda().eq(ZnsbNssbSbrwDO::getSyuuid, deletecjxxList.get(0).getHxbuuid());
////                        znsbNssbSbrwMapper.delete(queryWrapperX1);
////                    }
//                } else if(yhshbcjxxbList.size() == 0){
//                    //如果更正初始化接口无数据则表示数据未采集到核心则删除本地表
//                    this.zfYhslfcjxx(yhscjBtxx, deletecjxxList, deleteyhscjkblxxList, null, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjjg());
//                }
//            }
//            if (!GyUtils.isNull(insertcjxxList)) {
//                if (yhshbcjxxbList.size() == 1) {
//                    //保存至本地表
//                    //表头信息表UUID、数据交换服务YWUUID
////                    zbuuid = GyUtils.getUuid();
//                    YhscjBtxxDTO yhscjBtxxNew = BeanUtils.toBean(yhsCjCwgzReqDTO.getYhscjNsrxx(), YhscjBtxxDTO.class);
//                    this.insertTsqYhslfcjxx(yhscjBtxxNew, insertcjxxList, yhscjkblxxList, null, yhsCjCwgzReqDTO.getSjjg(), yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getYwqdDm());
////                    this.yhslfcjBc(yhscjBtxxNew.getUuid(), yhscjNsrxx, insertcjxxList, yhscjkblxxList);
//                    List<YhshbcjxxbDTO> cjSjUpdate = null;
//                    this.bcYhsCjxxByLq(zbuuid, "", yhscjNsrxx, cjSjUpdate, yhshbcjxxbList, yhscjkblxxList, qyjbxx);
//                    yhsCjCwgzResDTO.setResmsg("更正成功");
//                    yhsCjCwgzResDTO.setRescode("1001");
//                    return yhsCjCwgzResDTO;
//                }
//                skssqq = insertcjxxList.get(0).getSkssqq();
//                skssqz = insertcjxxList.get(0).getSkssqz();
//                yhscjNsrxx.setNsqxDm(nsqxDm);
//                yhscjNsrxx.setSkssqq(skssqq);
//                yhscjNsrxx.setSkssqz(skssqz);
//                //新增至本地表
//                this.insertTsqYhslfcjxx(yhscjBtxx, insertcjxxList, yhscjkblxxList, null, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjjg(), yhsCjCwgzReqDTO.getYwqdDm());
//            }
//            //TODO 乐企错误更正接口
//            //调用乐企错误更正信息保存接口
//            if (GyUtils.isNotNull(updatecjxxList) || (GyUtils.isNull(updatecjxxList) && deletecjxxList.size() > 1)) {
////                this.yhslfcjCwgz(zbuuid, hxbuuid, yhscjNsrxx, yhscjkblxxList, updatecjxxList, insertcjxxList, deletecjxxList);
//                this.bcYhsCjxxByLq(zbuuid, hxbuuid, yhscjNsrxx, updatecjxxList, insertcjxxList, yhscjkblxxList, qyjbxx);
//            }
//            yhsCjCwgzResDTO.setResmsg("更正成功");
//            yhsCjCwgzResDTO.setRescode("1001");
//        }
//        yhsCjCwgzResDTO.setYhscjBtxxList(BeanUtils.toBean(yhscjBtxxList, YhscjBtxxDTO.class));
//        return yhsCjCwgzResDTO;
//    }
//
//    private void yhslfcjCwgz(String zbuuid, String hxbuuid, YhscjNsrxxDTO yhscjNsrxx, List<YhscjkblxxDTO> yhscjkblxxList, List<YhshbcjxxbDTO> updatecjxxList, List<YhshbcjxxbDTO> insertcjxxList, List<YhshbcjxxbDTO> deletecjxxList) {
//        //乐企保存印花税税源采集信息接口请求报文
//        final LqYhsLfcjCwgzRequestDTO lqYhsLfcjCwgzRequestDTO = new LqYhsLfcjCwgzRequestDTO();
//        lqYhsLfcjCwgzRequestDTO.setZbuuid(hxbuuid);
//        lqYhsLfcjCwgzRequestDTO.setNsrxxFormVO(yhscjNsrxx);
//        lqYhsLfcjCwgzRequestDTO.setCjkblxxGrid(yhscjkblxxList);
//        lqYhsLfcjCwgzRequestDTO.setUpdateCjxxGrid(updatecjxxList);
//        lqYhsLfcjCwgzRequestDTO.setInsertCjxxGrid(insertcjxxList);
//        lqYhsLfcjCwgzRequestDTO.setDeleteCjxxGrid(deletecjxxList);
//        //通过数据交换服务调用乐企接口
//        final String ywbw = JsonUtils.toJson(lqYhsLfcjCwgzRequestDTO);
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("BC00000002");
//        sjjhDTO.setYwbm(YzpzzlEnum.YHS.getDm());
//        sjjhDTO.setYwuuid(zbuuid);
//        sjjhDTO.setDjxh(yhscjNsrxx.getDjxh());
//        sjjhDTO.setNsrsbh(yhscjNsrxx.getNsrsbh());
//        sjjhDTO.setXzqhszDm(yhscjNsrxx.getXzqhszDm());
//        sjjhDTO.setBwnr(ywbw);
//        sjjhService.saveSjjhJob(sjjhDTO);
//    }
//
////    /**
////     * 新版 采集和更正
////     *
////     * @param
////     * @return
////     */
////    public YhsCjCwgzResDTO saveCjAndGzOld(YhsCjCwgzReqDTO yhsCjCwgzReqDTO, List<Map<String, Object>> zbuuidList, Map<String, Object> zbuuiddzMap, List<Map<String, Object>> xzList) {
////        YhsCjCwgzResDTO yhsCjCwgzResDTO = new YhsCjCwgzResDTO();
////        HashMap<String, String> expand = new HashMap<>();
////        //技术报文头中的sjjg
////        expand.put("sjjg", yhsCjCwgzReqDTO.getSjjg());
////        try {
////            for (Map<String, Object> acxzMap : xzList) { //按次和按期采集
////                String skssqq = (String) acxzMap.get("skssqq");
////                String skssqz = (String) acxzMap.get("skssqz");
////                List<YhscjCjxxbDTO> yhscjCjxxbList = (List<YhscjCjxxbDTO>) acxzMap.get("yhscjcjxxList");
////                YhscjNsrxx yhscjNsrxx =BeanUtils.toBean(yhsCjCwgzReqDTO.getYhscjNsrxx(), YhscjNsrxx.class);
////                yhscjNsrxx.setNsqxDm(yhscjCjxxbList.get(0).getNsqxDm());
////                yhscjNsrxx.setSkssqq(skssqq);
////                yhscjNsrxx.setSkssqz(skssqz);
////                HXZGSB10768Request.CjxxGrid cjxxGrid = new HXZGSB10768Request.CjxxGrid();
////                cjxxGrid.getCjxxGridlb().addAll(yhscjCjxxbList);//未采集
////                HXZGSB10768Request hxzgsb10768Request = new HXZGSB10768Request();
////                hxzgsb10768Request.setNsrxxFormVO(yhscjNsrxx);
////                hxzgsb10768Request.setCjxxGrid(cjxxGrid);
////                HXZGSB10768Response hxzgsb10768Response = gt3Invoker.invoke("SWZJ.HXZG.SB.YHSCJBC", expand, hxzgsb10768Request, HXZGSB10768Response.class);
////                if (!GyUtils.isNull(hxzgsb10768Response) && hxzgsb10768Response.getInsertCjxxGrid() != null) {
////                    //保存本地业务表
////                    yhsCjCwgzResDTO.setResmsg("采集成功");
////                    yhsCjCwgzResDTO.setRescode("1001");
////                    return yhsCjCwgzResDTO;
////                }
////                yhsCjCwgzResDTO.setRescode("-1");
////                yhsCjCwgzResDTO.setResmsg("采集失败");
////            }
////            //更正
////            if (!GyUtils.isNull(zbuuidList)) { //调用更正
////                yhsCjCwgzResDTO = cwgzOld(yhsCjCwgzReqDTO, zbuuidList, zbuuiddzMap);
////                return yhsCjCwgzResDTO;
////            } else {
////                yhsCjCwgzResDTO.setResmsg("采集成功");
////                yhsCjCwgzResDTO.setRescode("1001");
////                return yhsCjCwgzResDTO;
////            }
////        } catch (Exception e) {
////            log.info("采集失败,错误信息:",e);
////            if (e.getMessage().contains("1010010097000045")) {
////                yhsCjCwgzResDTO.setResmsg("本属期已经采集，如需更正请通过更正采集处理。");
////            } else {
////                yhsCjCwgzResDTO.setResmsg(e.getMessage());
////            }
////            yhsCjCwgzResDTO.setRescode("-1");
////        }
////        return yhsCjCwgzResDTO;
////    }
//
////    /**
////     * 错误更正old
////     *
////     * @param yhsCjCwgzReqDTO
////     * @param zbuuidList
////     * @param zbuuiddzMap
////     * @return
////     */
////    public YhsCjCwgzResDTO cwgzOld(YhsCjCwgzReqDTO yhsCjCwgzReqDTO, List<Map<String, Object>> zbuuidList, Map<String, Object> zbuuiddzMap) {
////        YhsCjCwgzResDTO yhsCjCwgzResDTO = new YhsCjCwgzResDTO();
////        HashMap<String, String> expand = new HashMap<>();
////        //技术报文头中的sjjg
////        expand.put("sjjg", yhsCjCwgzReqDTO.getSjjg());
////        List<YhscjBtxxDTO> yhscjBtxxList = new ArrayList<>();
////        for (Map<String, Object> map : zbuuidList) {
////            YhscjNsrxxDTO yhscjNsrxx = new YhscjNsrxxDTO();
////            yhscjNsrxx =BeanUtils.toBean(yhsCjCwgzReqDTO.getYhscjNsrxx(), YhscjNsrxxDTO.class);
////            String zbuuid = (String) map.get("zbuuid");
////            final Object[] obj = (Object[]) zbuuiddzMap.get(zbuuid);
////            List<YhscjCjxxbDTO> updatecjxxList = (List<YhscjCjxxbDTO>) obj[0];
////            List<YhscjCjxxbDTO> insertcjxxList = (List<YhscjCjxxbDTO>) obj[2];
////            List<YhscjCjxxbDTO> deletecjxxList = (List<YhscjCjxxbDTO>) obj[1];
////            String nsqxDm = "11";
////            String skssqq = "";
////            String skssqz = "";
////            if (!GyUtils.isNull(updatecjxxList)) {
////                nsqxDm = updatecjxxList.get(0).getNsqxDm();//重新更新纳税期
////                skssqq = updatecjxxList.get(0).getSkssqq();
////                skssqz = updatecjxxList.get(0).getSkssqz();
////            } else if (!GyUtils.isNull(deletecjxxList)) {
////                nsqxDm = deletecjxxList.get(0).getNsqxDm();//重新更新纳税期
////                skssqq = deletecjxxList.get(0).getSkssqq();
////                skssqz = deletecjxxList.get(0).getSkssqz();
////            }
////            yhscjNsrxx.setNsqxDm(nsqxDm);
////            yhscjNsrxx.setSkssqq(skssqq);
////            yhscjNsrxx.setSkssqz(skssqz);
////
////            final LqYhsCwgzRequestVO lqYhsCwgzRequestVO = new LqYhsCwgzRequestVO();
////            lqYhsCwgzRequestVO.setNsrxxFormVO(yhscjNsrxx);
////            lqYhsCwgzRequestVO.setInsertCjxxGrid(insertcjxxList);
////            lqYhsCwgzRequestVO.setUpdateCjxxGrid(updatecjxxList);
////            lqYhsCwgzRequestVO.setDeleteCjxxGrid(deletecjxxList);
////            lqYhsCwgzRequestVO.setZbuuid(zbuuid);
////            //TODO 乐企错误更正接口
////            LqYhsCwgzResponseVO lqYhsCwgzResponseVO = new LqYhsCwgzResponseVO();
////            lqYhsCwgzResponseVO.setReturncode("00");
////            if (GyUtils.isNotNull(lqYhsCwgzResponseVO) && "00".equals(lqYhsCwgzResponseVO.getReturncode())) {
//////                yhscjBtxxList.add(hxzgsb10769Response.getYhscjBtxx());
//////                //重新取数 过滤掉更新的数据
//////                List<YhscjCjxxbDTO> yhscjCjxxbList = hxzgsb10769Response.getCjxxGrid().getCjxxGridlb();
//////                //过滤掉更新的数据的数据 剩下本次新增的
//////                for (int i = yhscjCjxxbList.size() - 1; i >= 0; i--) {
//////                    String uuid = yhscjCjxxbList.get(i).getUuid();
//////                    for (YhscjCjxxbDTO updateyhscjCjxxb : updatecjxxList) {
//////                        if (uuid.equals(updateyhscjCjxxb.getUuid())) {
//////                            yhscjCjxxbList.remove(i);
//////                            continue;
//////                        }
//////                    }
//////                }
////                //保存业务表
////                /*if (insertcjxxList != null) {
////                    String resmag = insertYhslfcjxx(yhscjBtxx, null, null, yhscjCjxxbList, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjjg(), yhsCjCwgzReqDTO.getYwqdDm());
////                } else if (updatecjxxList != null) {
////                    String resmag = updateYhslfcjxx(null, null, updatecjxxList, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjjg(), yhscjNsrxx.getDjxh(), yhsCjCwgzReqDTO.getYwqdDm());
////                } else {
////                    String resmag = zfYhslfcjxx(yhscjBtxx, null, null, deletecjxxList, yhsCjCwgzReqDTO.getSjry(), yhsCjCwgzReqDTO.getSjjg());
////                }*/
////                yhsCjCwgzResDTO.setResmsg("更正成功");
////                yhsCjCwgzResDTO.setRescode("1001");
////            } else {
////                yhsCjCwgzResDTO.setResmsg("更正失败");
////                yhsCjCwgzResDTO.setRescode("-1");
////            }
////        }
////        yhsCjCwgzResDTO.setYhscjBtxxList(BeanUtils.toBean(yhscjBtxxList, YhscjBtxxDTO.class));
////        return yhsCjCwgzResDTO;
////    }
////
////
//
//    /**
//     * 新增保存本地业务表
//     *
//     * @param yhshbcjxxbList
//     * @param yhscjkblxxList
//     * @param yhscjCjxxbList
//     * @param sjry
//     * @param sjjg
//     * @param ywqdDm
//     * @return
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void insertYhslfcjxx(YhscjBtxxDTO yhscjBtxx, List<YhshbcjxxbDTO> yhshbcjxxbList, List<YhscjkblxxDTO> yhscjkblxxList, List<YhscjCjxxbDTO> yhscjCjxxbList, String sjry, String sjjg, String ywqdDm) {
//        final String zbuuid = yhscjBtxx.getUuid();
//        //保存表头信息
//        final ZnsbNssbCxsbtxxDO sbCxsCxssybtxx = BeanUtils.toBean(yhscjBtxx, ZnsbNssbCxsbtxxDO.class);
////        sbCxsCxssybtxx.setUuid(zbuuid);
//        sbCxsCxssybtxx.setYsbbz("N");
//        sbCxsCxssybtxx.setZfbz1("N");
//        sbCxsCxssybtxx.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
//        sbCxsCxssybtxx.setSkssqq(GyUtils.cast2Date(yhscjBtxx.getSkssqq()));
//        sbCxsCxssybtxx.setSkssqz(GyUtils.cast2Date(yhscjBtxx.getSkssqz()));
//        sbCxsCxssybtxx.setBczt(BcztConstants.CLZ);
//        sbCxsCxssybtxx.setSjgsdq(sjjg);
//        sbCxsCxssybtxx.setSjcsdq(sjjg);
//        sbCxsCxssybtxx.setSjtbSj(new Date());
//        sbCxsCxssybtxx.setLrrq(new Date());
//        sbCxsCxssybtxx.setLrrsfid(sjry);
//        sbCxsCxssybtxx.setXgrsfid(sjry);
//        sbCxsCxssybtxx.setXgrq(new Date());
//        sbCxsCxssybtxx.setYwqdDm(ywqdDm);
//        final ZnsbNssbCxsbtxxDO sbCxsCxssybtxx1 = znsbNssbCxsbtxxMapper.querysybtxxbyzbuuid(sbCxsCxssybtxx);
//        if (sbCxsCxssybtxx1 == null) { //已经存在表头信息 不需要保存
//            znsbNssbCxsbtxxMapper.insert(sbCxsCxssybtxx);
//        }
//        final Map<String, String> rdMap = new HashMap<>();
//        if (!GyUtils.isNull(yhshbcjxxbList)) {
//            //保存本地业务表
//            List<ZnsbNssbYhscjDO> savecjsyxxList = new ArrayList<>();
//            for (YhshbcjxxbDTO yhshbcjxxb : yhshbcjxxbList) {
//                if ("insert".equals(yhshbcjxxb.getGzbz())) {
//                    yhshbcjxxb.setGzbz("0");
//                }
//                if ("update".equals(yhshbcjxxb.getGzbz())) {
//                    yhshbcjxxb.setGzbz("1");
//                }
//                if ("delete".equals(yhshbcjxxb.getGzbz())) {
//                    yhshbcjxxb.setGzbz("2");
//                }
//                ZnsbNssbYhscjDO sbCxsYhslfsyxx = BeanUtils.toBean(yhshbcjxxb, ZnsbNssbYhscjDO.class);
//                sbCxsYhslfsyxx.setUuid(GyUtils.getUuid());
//                sbCxsYhslfsyxx.setZbuuid(zbuuid);
//                sbCxsYhslfsyxx.setSkssqq(GyUtils.cast2Date(yhshbcjxxb.getSkssqq()));
//                sbCxsYhslfsyxx.setSkssqz(GyUtils.cast2Date(yhshbcjxxb.getSkssqz()));
//                sbCxsYhslfsyxx.setJsjehjs(yhshbcjxxb.getJsjehjs());
//                sbCxsYhslfsyxx.setJmse(yhshbcjxxb.getJmse());
//                sbCxsYhslfsyxx.setYbtse(yhshbcjxxb.getYbtse());
//                sbCxsYhslfsyxx.setSjjsje(yhshbcjxxb.getSjjsje());
//                sbCxsYhslfsyxx.setSjjsrq(GyUtils.cast2Date(yhshbcjxxb.getSjjsrq()));
//                sbCxsYhslfsyxx.setYnspzsllsrq(GyUtils.cast2Date(yhshbcjxxb.getYnspzsllsrq()));
////                sbCxsYhslfsyxx.setDjxh(djxh);
//                sbCxsYhslfsyxx.setYnse(yhshbcjxxb.getYnse());
//                sbCxsYhslfsyxx.setSl1(yhshbcjxxb.getSl1());
//                sbCxsYhslfsyxx.setSjgsdq(sjjg);
//                sbCxsYhslfsyxx.setSjcsdq(sjjg);
//                sbCxsYhslfsyxx.setSjtbSj(new Date());
//                sbCxsYhslfsyxx.setLrrq(new Date());
//                sbCxsYhslfsyxx.setLrrsfid(sjry);
//                sbCxsYhslfsyxx.setXgrsfid(sjry);
//                sbCxsYhslfsyxx.setXgrq(new Date());
//                sbCxsYhslfsyxx.setSjgsdq(sjjg);
//                sbCxsYhslfsyxx.setYwqdDm(ywqdDm);
//                sbCxsYhslfsyxx.setBczt(BcztConstants.CLZ);
//                sbCxsYhslfsyxx.setGzbz("0");
////                sbCxsYhslfsyxx.setPclsh(pclsh);
//                savecjsyxxList.add(sbCxsYhslfsyxx);
//                rdMap.put(yhshbcjxxb.getTimebz(), sbCxsYhslfsyxx.getUuid());
//            }
//            znsbNssbYhscjService.saveBatch(savecjsyxxList);
//        }
//        if (!GyUtils.isNull(yhscjkblxxList)) {
//            List<ZnsbNssbYhslfkblDO> sbCxsYhslfkblList = new ArrayList<>();
//            //可变列表  新增
//            for (YhscjkblxxDTO yhscjkblxx : yhscjkblxxList) {
//                ZnsbNssbYhslfkblDO sbCxsYhslfkbl = BeanUtils.toBean(yhscjkblxx, ZnsbNssbYhslfkblDO.class);
////                sbCxsYhslfkbl.setDjxh(yhscjBtxx.getDjxh());
//                sbCxsYhslfkbl.setUuid(GyUtils.getUuid());
//                sbCxsYhslfkbl.setZbuuid(zbuuid);
//                sbCxsYhslfkbl.setDfslrsjje(yhscjkblxx.getDfslrsjje());
//                sbCxsYhslfkbl.setSjjsje(yhscjkblxx.getSjjsje());
//                sbCxsYhslfkbl.setSjjsrq(GyUtils.cast2Date(yhscjkblxx.getSjjsrq()));
//                sbCxsYhslfkbl.setZfbz1("N");
//                sbCxsYhslfkbl.setSjgsdq(sjjg);
//                sbCxsYhslfkbl.setSjcsdq(sjjg);
//                sbCxsYhslfkbl.setSjtbSj(new Date());
//                sbCxsYhslfkbl.setLrrq(new Date());
//                sbCxsYhslfkbl.setLrrsfid(sjry);
//                sbCxsYhslfkbl.setXgrsfid(sjry);
//                sbCxsYhslfkbl.setXgrq(new Date());
//                sbCxsYhslfkbl.setSjgsdq(sjjg);
//                sbCxsYhslfkbl.setYwqdDm(ywqdDm);
//                final String syuuid = rdMap.get(yhscjkblxx.getTimebz());
//                sbCxsYhslfkbl.setSyuuid(syuuid);
//                sbCxsYhslfkblList.add(sbCxsYhslfkbl);
//            }
//            znsbNssbYhslfkblService.saveBatch(sbCxsYhslfkblList);
//        }
//    }
//
//    /**
//     * 新增保存本地业务表
//     *
//     * @param yhshbcjxxbList
//     * @param yhscjkblxxList
//     * @param yhscjCjxxbList
//     * @param sjry
//     * @param sjjg
//     * @param ywqdDm
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void insertTsqYhslfcjxx(YhscjBtxxDTO yhscjBtxx, List<YhshbcjxxbDTO> yhshbcjxxbList, List<YhscjkblxxDTO> yhscjkblxxList, List<YhscjCjxxbDTO> yhscjCjxxbList, String sjry, String sjjg, String ywqdDm) {
//        if (!GyUtils.isNull(yhshbcjxxbList)) {
//            //不存在表头信息
//            if (GyUtils.isNull(yhscjBtxx.getUuid())) {
//                yhscjBtxx.setUuid(GyUtils.getUuid());
//                //保存表头信息
//                final ZnsbNssbCxsbtxxDO sbCxsCxssybtxx = BeanUtils.toBean(yhscjBtxx, ZnsbNssbCxsbtxxDO.class);
//                sbCxsCxssybtxx.setYsbbz("N");
//                sbCxsCxssybtxx.setZfbz1("N");
//                sbCxsCxssybtxx.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
//                sbCxsCxssybtxx.setSkssqq(GyUtils.cast2Date(yhscjBtxx.getSkssqq()));
//                sbCxsCxssybtxx.setSkssqz(GyUtils.cast2Date(yhscjBtxx.getSkssqz()));
//                sbCxsCxssybtxx.setBczt(BcztConstants.CLZ);
//                sbCxsCxssybtxx.setSjgsdq(sjjg);
//                sbCxsCxssybtxx.setSjcsdq(sjjg);
//                sbCxsCxssybtxx.setSjtbSj(new Date());
//                sbCxsCxssybtxx.setLrrq(new Date());
//                sbCxsCxssybtxx.setLrrsfid(sjry);
//                sbCxsCxssybtxx.setXgrsfid(sjry);
//                sbCxsCxssybtxx.setXgrq(new Date());
//                sbCxsCxssybtxx.setYwqdDm(ywqdDm);
//                final ZnsbNssbCxsbtxxDO sbCxsCxssybtxx1 = znsbNssbCxsbtxxMapper.querysybtxxbyzbuuid(sbCxsCxssybtxx);
//                if (sbCxsCxssybtxx1 == null) { //已经存在表头信息 不需要保存
//                    znsbNssbCxsbtxxMapper.insert(sbCxsCxssybtxx);
//                }
//            }
//            final String zbuuid = yhscjBtxx.getUuid();
//            //保存本地业务表
////            List<ZnsbNssbYhscjDO> savecjsyxxList = new ArrayList<>();
//            for (YhshbcjxxbDTO yhshbcjxxb : yhshbcjxxbList) {
//                if ("insert".equals(yhshbcjxxb.getGzbz())) {
//                    yhshbcjxxb.setGzbz("0");
//                }
//                if ("update".equals(yhshbcjxxb.getGzbz())) {
//                    yhshbcjxxb.setGzbz("1");
//                }
//                if ("delete".equals(yhshbcjxxb.getGzbz())) {
//                    yhshbcjxxb.setGzbz("2");
//                }
//                ZnsbNssbYhscjDO sbCxsYhslfsyxx = BeanUtils.toBean(yhshbcjxxb, ZnsbNssbYhscjDO.class);
//                sbCxsYhslfsyxx.setUuid(GyUtils.getUuid());
//                sbCxsYhslfsyxx.setZbuuid(zbuuid);
//                sbCxsYhslfsyxx.setSkssqq(GyUtils.cast2Date(yhshbcjxxb.getSkssqq()));
//                sbCxsYhslfsyxx.setSkssqz(GyUtils.cast2Date(yhshbcjxxb.getSkssqz()));
//                sbCxsYhslfsyxx.setJsjehjs(yhshbcjxxb.getJsjehjs());
//                sbCxsYhslfsyxx.setJmse(yhshbcjxxb.getJmse());
//                sbCxsYhslfsyxx.setYbtse(yhshbcjxxb.getYbtse());
//                sbCxsYhslfsyxx.setSjjsje(yhshbcjxxb.getSjjsje());
//                sbCxsYhslfsyxx.setSjjsrq(GyUtils.cast2Date(yhshbcjxxb.getSjjsrq()));
//                sbCxsYhslfsyxx.setYnspzsllsrq(GyUtils.cast2Date(yhshbcjxxb.getYnspzsllsrq()));
////                sbCxsYhslfsyxx.setDjxh(djxh);
//                sbCxsYhslfsyxx.setYnse(yhshbcjxxb.getYnse());
//                sbCxsYhslfsyxx.setSl1(yhshbcjxxb.getSl1());
//                sbCxsYhslfsyxx.setSjgsdq(sjjg);
//                sbCxsYhslfsyxx.setSjcsdq(sjjg);
//                sbCxsYhslfsyxx.setSjtbSj(new Date());
//                sbCxsYhslfsyxx.setLrrq(new Date());
//                sbCxsYhslfsyxx.setLrrsfid(sjry);
//                sbCxsYhslfsyxx.setXgrsfid(sjry);
//                sbCxsYhslfsyxx.setXgrq(new Date());
//                sbCxsYhslfsyxx.setSjgsdq(sjjg);
//                sbCxsYhslfsyxx.setYwqdDm(ywqdDm);
//                sbCxsYhslfsyxx.setBczt(BcztConstants.CLZ);
//                sbCxsYhslfsyxx.setGzbz("0");
//                sbCxsYhslfsyxx.setHxbuuid(zbuuid);
////                sbCxsYhslfsyxx.setPclsh(pclsh);
//                znsbNssbYhscjService.save(sbCxsYhslfsyxx);
//
//                for (YhscjkblxxDTO insertkbl : yhshbcjxxb.getYhscjkblxxList()) {
//                    ZnsbNssbYhslfkblDO sbCxsYhslfkbl = BeanUtils.toBean(insertkbl, ZnsbNssbYhslfkblDO.class);
//                    sbCxsYhslfkbl.setUuid(GyUtils.getUuid());
//                    sbCxsYhslfkbl.setZbuuid(zbuuid);
//                    sbCxsYhslfkbl.setDfslrsjje(insertkbl.getDfslrsjje());
//                    sbCxsYhslfkbl.setSjjsje(insertkbl.getSjjsje());
//                    sbCxsYhslfkbl.setSjjsrq(GyUtils.cast2Date(insertkbl.getSjjsrq()));
//                    sbCxsYhslfkbl.setZfbz1("N");
//                    sbCxsYhslfkbl.setSjgsdq(sjjg);
//                    sbCxsYhslfkbl.setSjcsdq(sjjg);
//                    sbCxsYhslfkbl.setSjtbSj(new Date());
//                    sbCxsYhslfkbl.setLrrq(new Date());
//                    sbCxsYhslfkbl.setLrrsfid(sjry);
//                    sbCxsYhslfkbl.setXgrsfid(sjry);
//                    sbCxsYhslfkbl.setXgrq(new Date());
//                    sbCxsYhslfkbl.setSjgsdq(sjjg);
//                    sbCxsYhslfkbl.setYwqdDm(ywqdDm);
//                    sbCxsYhslfkbl.setSyuuid(sbCxsYhslfsyxx.getUuid());
//                    znsbNssbYhslfkblService.save(sbCxsYhslfkbl);
//                }
//            }
//        }
//    }
//
//    /**
//     * 删除税源明细
//     *
//     * @param
//     * @param yhshbcjxxbList
//     * @param yhscjkblxxList
//     * @param yhscjCjxxbList
//     * @param sjry
//     * @param sjjg
//     * @param ywqdDm
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public String deleteYhsmx(List<YhshbcjxxbDTO> yhshbcjxxbList, List<YhscjkblxxDTO> yhscjkblxxList, List<YhscjCjxxbDTO> yhscjCjxxbList, String sjry, String sjjg, String ywqdDm) {
//        if (!GyUtils.isNull(yhshbcjxxbList)) {
//            //作废本地业务表
//            List<ZnsbNssbYhscjDO> savecjsyxxList = new ArrayList<>();
//            for (YhshbcjxxbDTO yhshbcjxxb : yhshbcjxxbList) {
//                ZnsbNssbYhscjDO sbCxsYhslfsyxx = new ZnsbNssbYhscjDO();
//                sbCxsYhslfsyxx.setUuid(yhshbcjxxb.getUuid());
//                sbCxsYhslfsyxx.setZfbz1("Y");
//                sbCxsYhslfsyxx.setZfrDm(sjry);
//                sbCxsYhslfsyxx.setZfrq1(new Date());
//                sbCxsYhslfsyxx.setXgrsfid(sjry);
//                sbCxsYhslfsyxx.setXgrq(new Date());
//                savecjsyxxList.add(sbCxsYhslfsyxx);
//            }
//            znsbNssbYhscjMapper.zfsycjxx(savecjsyxxList);
//        }
//        if (!GyUtils.isNull(yhscjkblxxList)) {
//            List<ZnsbNssbYhslfkblDO> sbCxsYhslfkblList = new ArrayList<>();
//            for (YhscjkblxxDTO yhscjkblxx : yhscjkblxxList) {
//                ZnsbNssbYhslfkblDO sbCxsYhslfkbl = BeanUtils.toBean(yhscjkblxx, ZnsbNssbYhslfkblDO.class);
//                sbCxsYhslfkbl.setZfbz1("Y");
//                sbCxsYhslfkbl.setZfrDm(sjry);
//                sbCxsYhslfkbl.setZfrq1(new Date());
//                sbCxsYhslfkbl.setXgrsfid(sjry);
//                sbCxsYhslfkbl.setXgrq(new Date());
//                sbCxsYhslfkblList.add(sbCxsYhslfkbl);
//            }
//            znsbNssbYhslfkblMapper.zfkbl(sbCxsYhslfkblList);
//        }
//
////        if (!GyUtils.isNull(yhscjCjxxbList)) { //旧版印花税
////            //更正本地业务表
////            List<SbCxsYhssyxx> savecjsyxxList = new ArrayList<>();
////            for (YhscjCjxxb yhscjCjxxb : yhscjCjxxbList) {
////                SbCxsYhssyxx sbCxsYhssyxx = new SbCxsYhssyxx();
////                sbCxsYhssyxx.setUuid(yhscjCjxxb.getUuid());
////                sbCxsYhssyxx.setZfbz1("Y");
////                sbCxsYhssyxx.setZfrDm(sjry);
////                sbCxsYhssyxx.setZfrq1(new Date());
////                sbCxsYhssyxx.setXgrsfid(sjry);
////                sbCxsYhssyxx.setXgrq(new Date());
////                savecjsyxxList.add(sbCxsYhssyxx);
////            }
////            sbCxsYhssyxxMapper.zfyhssymx(savecjsyxxList);
////        }
//
//        return "作废成功";
//    }
//
//    /**
//     * 更正本地业务表信息
//     *
//     * @param
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void updateYhslfcjxx(List<YhshbcjxxbDTO> yhshbcjxxbList, List<YhscjkblxxDTO> yhscjkblxxList, List<YhscjCjxxbDTO> yhscjCjxxbList, String sjry, String sjjg, String djxh, String ywqdDm) {
//        List<String> updateMxTokblList = new ArrayList<>();
//        if (!GyUtils.isNull(yhshbcjxxbList)) {
//            //更正本地业务表
//            List<ZnsbNssbYhscjDO> updatecjsyxxList = new ArrayList<>();
//            for (YhshbcjxxbDTO yhshbcjxxb : yhshbcjxxbList) {
//                ZnsbNssbYhscjDO sbCxsYhslfsyxx = new ZnsbNssbYhscjDO();
//                sbCxsYhslfsyxx.setZbuuid(yhshbcjxxb.getZbuuid());
//                sbCxsYhslfsyxx.setUuid(yhshbcjxxb.getUuid());
//                sbCxsYhslfsyxx.setJsjehjs(yhshbcjxxb.getJsjehjs());
//                sbCxsYhslfsyxx.setJmse(yhshbcjxxb.getJmse());
//                sbCxsYhslfsyxx.setYnse(yhshbcjxxb.getYnse());
//                sbCxsYhslfsyxx.setYbtse(yhshbcjxxb.getYbtse());
//                sbCxsYhslfsyxx.setSsjmxzDm(yhshbcjxxb.getSsjmxzDm());
//                sbCxsYhslfsyxx.setXgrsfid(sjry);
//                sbCxsYhslfsyxx.setGzbz("1");
//                sbCxsYhslfsyxx.setXgrq(new Date());
//                sbCxsYhslfsyxx.setYspzmc(yhshbcjxxb.getYspzmc());
//                sbCxsYhslfsyxx.setYspzsl(yhshbcjxxb.getYspzsl());
//                sbCxsYhslfsyxx.setBczt(BcztConstants.CLZ);
//                updatecjsyxxList.add(sbCxsYhslfsyxx);
//                //删除可变列
//                znsbNssbYhslfkblMapper.updateBySyuuid(yhshbcjxxb.getUuid());
//                for (YhscjkblxxDTO yhscjkblxx : yhshbcjxxb.getYhscjkblxxList()) {
//                    ZnsbNssbYhslfkblDO sbCxsYhslfkbl = new ZnsbNssbYhslfkblDO();
//                    sbCxsYhslfkbl = BeanUtils.toBean(yhscjkblxx, ZnsbNssbYhslfkblDO.class);
//                    sbCxsYhslfkbl.setDfslrsjje(yhscjkblxx.getDfslrsjje());
//                    sbCxsYhslfkbl.setSjjsje(yhscjkblxx.getSjjsje());
//                    sbCxsYhslfkbl.setSjjsrq(GyUtils.cast2Date(yhscjkblxx.getSjjsrq()));
//                    sbCxsYhslfkbl.setUuid(GyUtils.getUuid());
//                    sbCxsYhslfkbl.setZbuuid(sbCxsYhslfsyxx.getZbuuid());
//                    sbCxsYhslfkbl.setZfbz1("N");
//                    sbCxsYhslfkbl.setSjgsdq(sjjg);
//                    sbCxsYhslfkbl.setSjcsdq(sjjg);
//                    sbCxsYhslfkbl.setSjtbSj(new Date());
//                    sbCxsYhslfkbl.setLrrq(new Date());
//                    sbCxsYhslfkbl.setLrrsfid(sjry);
//                    sbCxsYhslfkbl.setXgrsfid(sjry);
//                    sbCxsYhslfkbl.setXgrq(new Date());
//                    sbCxsYhslfkbl.setSjgsdq(sjjg);
//                    sbCxsYhslfkbl.setYwqdDm(ywqdDm);
//                    sbCxsYhslfkbl.setSyuuid(sbCxsYhslfsyxx.getUuid());
//                    znsbNssbYhslfkblService.save(sbCxsYhslfkbl);
//                }
//            }
//            znsbNssbYhscjMapper.updateYhslfcjxx(updatecjsyxxList);
//        }
//
////        if (!GyUtils.isNull(yhscjCjxxbList)) { //旧版印花税
////            //更正本地业务表
////            List<SbCxsYhssyxx> sbCxsYhssyxxList = new ArrayList<>();
////            for (YhscjCjxxb yhscjCjxxb : yhscjCjxxbList) {
////                SbCxsYhssyxx sbCxsYhssyxx = new SbCxsYhssyxx();
////                sbCxsYhssyxx.setUuid(yhscjCjxxb.getUuid());
////                sbCxsYhssyxx.setJsjehjs(new BigDecimal(yhscjCjxxb.getJsjehjs()));
////                sbCxsYhssyxx.setJmse(yhscjCjxxb.getJmse() == null ? new BigDecimal("0.00") : new BigDecimal(yhscjCjxxb.getJmse()));
////                sbCxsYhssyxx.setYnse(new BigDecimal(yhscjCjxxb.getYnse()));
////                sbCxsYhssyxx.setSsjmxzDm(yhscjCjxxb.getSsjmxzDm());
////                sbCxsYhssyxx.setXgrsfid(sjry);
////                sbCxsYhssyxx.setXgrq(new Date());
////                sbCxsYhssyxxList.add(sbCxsYhssyxx);
////            }
////            sbCxsYhssyxxMapper.updateYhscjxx(sbCxsYhssyxxList);
////        }
//
////        return "更正成功";
//    }
//
//    /**
//     * 更正作废本地数据
//     *
//     * @param yhshbcjxxbList
//     * @param yhscjkblxxList
//     * @param yhscjCjxxbList
//     * @param sjry
//     * @param sjjg
//     * @param
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void zfYhslfcjxx(YhscjBtxxDTO yhscjBtxx, List<YhshbcjxxbDTO> yhshbcjxxbList, List<YhscjkblxxDTO> yhscjkblxxList, List<YhscjCjxxbDTO> yhscjCjxxbList, String sjry, String sjjg) {
//        ZnsbNssbCxsbtxxDO sbCxsCxssybtxx = BeanUtils.toBean(yhscjBtxx, ZnsbNssbCxsbtxxDO.class);
//        sbCxsCxssybtxx.setZfrq1(new Date());
//        sbCxsCxssybtxx.setZfbz1("Y");
//        sbCxsCxssybtxx.setZfrDm(sjry);
//        znsbNssbCxsbtxxMapper.zfsybtxx(sbCxsCxssybtxx);
//        if (!GyUtils.isNull(yhshbcjxxbList)) {
//            //作废本地业务表
//            List<ZnsbNssbYhscjDO> savecjsyxxList = new ArrayList<>();
//            for (YhshbcjxxbDTO yhshbcjxxb : yhshbcjxxbList) {
//                ZnsbNssbYhscjDO sbCxsYhslfsyxx = new ZnsbNssbYhscjDO();
//                sbCxsYhslfsyxx.setUuid(yhshbcjxxb.getUuid());
//                sbCxsYhslfsyxx.setZfbz1("Y");
//                sbCxsYhslfsyxx.setZfrDm(sjry);
//                sbCxsYhslfsyxx.setZfrq1(new Date());
//                sbCxsYhslfsyxx.setXgrsfid(sjry);
//                sbCxsYhslfsyxx.setXgrq(new Date());
//                savecjsyxxList.add(sbCxsYhslfsyxx);
//                //作废可变列
//                znsbNssbYhslfkblMapper.updateBySyuuid(yhshbcjxxb.getUuid());
//
//            }
//            znsbNssbYhscjMapper.zfsycjxx(savecjsyxxList);
//        }
////        if (!GyUtils.isNull(yhscjkblxxList)) {
////            List<ZnsbNssbYhslfkblDO> sbCxsYhslfkblList = new ArrayList<>();
////            for (YhscjkblxxDTO yhscjkblxx : yhscjkblxxList) {
////                ZnsbNssbYhslfkblDO sbCxsYhslfkbl = new ZnsbNssbYhslfkblDO();
////                sbCxsYhslfkbl = BeanUtils.toBean(yhscjkblxx, ZnsbNssbYhslfkblDO.class);
////                sbCxsYhslfkbl.setZfbz1("Y");
////                sbCxsYhslfkbl.setZfrDm(sjry);
////                sbCxsYhslfkbl.setZfrq1(new Date());
////                sbCxsYhslfkbl.setXgrsfid(sjry);
////                sbCxsYhslfkbl.setXgrq(new Date());
////                sbCxsYhslfkblList.add(sbCxsYhslfkbl);
////            }
////            znsbNssbYhslfkblMapper.zfkbl(sbCxsYhslfkblList);
////        }
//
////        if (!GyUtils.isNull(yhscjCjxxbList)) { //旧版印花税
////            //更正本地业务表
////            List<SbCxsYhssyxx> savecjsyxxList = new ArrayList<>();
////            for (YhscjCjxxb yhscjCjxxb : yhscjCjxxbList) {
////                SbCxsYhssyxx sbCxsYhssyxx = new SbCxsYhssyxx();
////                sbCxsYhssyxx.setUuid(yhscjCjxxb.getUuid());
////                sbCxsYhssyxx.setZfbz1("Y");
////                sbCxsYhssyxx.setZfrDm(sjry);
////                sbCxsYhssyxx.setZfrq1(new Date());
////                sbCxsYhssyxx.setXgrsfid(sjry);
////                sbCxsYhssyxx.setXgrq(new Date());
////                savecjsyxxList.add(sbCxsYhssyxx);
////            }
////            sbCxsYhssyxxMapper.zfyhssymx(savecjsyxxList);
////        }
//
////        return "作废成功";
//    }
////
////    /**
////     * 印花税一键零申报功能
////     */
////    public YhsZeroSbResDTO saveZerosb(YhsZeroSbReqDTO yhsZeroSbReqDTO) {
////        YhsZeroSbResDTO yhsZeroSbResDTO = new YhsZeroSbResDTO();
////        String zbuuid = yhsZeroSbReqDTO.getZbuuid();
////        YhscjNsrxxDTO yhscjNsrxx = yhsZeroSbReqDTO.getYhscjNsrxx();
////
////
/////*        CxstysbNsrxx nsrxx =BeanUtils.toBean(yhscjNsrxx, CxstysbNsrxx.class);
////        nsrxx.setSbrq1(CchxwsnssbGyUtils.getDayofNow());
////        nsrxx.setBqsfsyzzsxgmnsrjzzc("N");*/
////        String sjjg = yhscjNsrxx.getZgswskfjDm();
////
////        final DwnsrxxVO dwnsrxxVO = CchxwsnssbGyUtils.queryNsrxxByApi(yhscjNsrxx.getDjxh(), yhscjNsrxx.getNsrsbh());
////        String hydm = "";
////        String bsrxm = "";
////        String bsrsfzjhm = "";
////        String jdxzdm = "";
////        String xzqhszdm = "";
////        if (!GyUtils.isNull(dwnsrxxVO)) {
////            hydm = GyUtils.isNull(dwnsrxxVO.getHyDm()) ? "" : dwnsrxxVO.getHyDm();
////            bsrxm = GyUtils.isNull(dwnsrxxVO.getBsrxm()) ? "" : dwnsrxxVO.getBsrxm();
////            bsrsfzjhm = GyUtils.isNull(dwnsrxxVO.getBsrsfzjhm()) ? "" : dwnsrxxVO.getBsrsfzjhm();
////            jdxzdm = GyUtils.isNull(dwnsrxxVO.getJdxzDm()) ? "" : dwnsrxxVO.getJdxzDm();
////            xzqhszdm = GyUtils.isNull(dwnsrxxVO.getScjydzxzqhszDm()) ? "" : dwnsrxxVO.getScjydzxzqhszDm();
////        }
////
////
////        final CxstysbNsrxx cxstysbNsrxx = new CxstysbNsrxx();
////        cxstysbNsrxx.setDjxh(yhscjNsrxx.getDjxh());
////        cxstysbNsrxx.setNsrsbh(yhscjNsrxx.getNsrsbh());
////        cxstysbNsrxx.setNsrmc(yhscjNsrxx.getNsrmc());
////        cxstysbNsrxx.setSbsxDm1("11");
////        cxstysbNsrxx.setSbrq1(CchxwsnssbGyUtils.getDayofNow());
////        cxstysbNsrxx.setHyDm(hydm);
////        cxstysbNsrxx.setJdxzDm(jdxzdm);
////        cxstysbNsrxx.setYzpzzlDm("BDA0611148");
////        cxstysbNsrxx.setXzqhszDm(xzqhszdm);
////        cxstysbNsrxx.setZgswskfjDm(sjjg);
////
////        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc("N");
////        cxstysbNsrxx.setXgmjzzcqssj(yhscjNsrxx.getSkssqq());
////        cxstysbNsrxx.setXgmjzzczzsj(yhscjNsrxx.getSkssqz());
////        cxstysbNsrxx.setSkssqq(yhscjNsrxx.getSkssqq());
////        cxstysbNsrxx.setSkssqz(yhscjNsrxx.getSkssqz());
////        cxstysbNsrxx.setSbrq1(CchxwsnssbGyUtils.getDayofNow());
////        //受理信息
////        final SbCxstysbSlxx sbCxstysbSlxx = new SbCxstysbSlxx();
////        sbCxstysbSlxx.setDljgqz("");
////        sbCxstysbSlxx.setJbr(bsrxm);
////        sbCxstysbSlxx.setSlr(SjryUtil.getSjry(sjjg));
////        sbCxstysbSlxx.setSlrq(CchxwsnssbGyUtils.getDayofNow());
////        sbCxstysbSlxx.setSlswjg(sjjg);
////        sbCxstysbSlxx.setJbrsfzjhm(bsrsfzjhm);
////        sbCxstysbSlxx.setDljgtyshxydm("");
////
////        final Map<String, String> expand = new HashMap<>();
////        expand.put("sjjg", sjjg);
////        //核心申报接口入参
////        final HXZGSB10736Request hxzgsb10736Request = new HXZGSB10736Request();
////        final HXZGSB10736Request.SbxxGrid sbxxGrid = new HXZGSB10736Request.SbxxGrid();
////        final SbxxGridlb10736VO sbxxGridlb10736VO = new SbxxGridlb10736VO();
////        final SbxxGridlb10736VO.SymxGrid symxGrid = new SbxxGridlb10736VO.SymxGrid();
////        sbxxGridlb10736VO.setZsxmDm("10111");
////
////        SymxGridlb10736VO symxGridlbvo = new SymxGridlb10736VO();
////        symxGridlbvo.setSyuuid(zbuuid);
////        symxGridlbvo.setSybzDm1("");
////        List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
////        symxGridlb.add(symxGridlbvo);
////        symxGrid.getSymxGridlb().addAll(symxGridlb);
////        sbxxGridlb10736VO.setSymxGrid(symxGrid);
////        sbxxGrid.getSbxxGridlb().add(sbxxGridlb10736VO);
////
////        hxzgsb10736Request.setCxstysbnsrxx(cxstysbNsrxx);
////        hxzgsb10736Request.setSbxxGrid(sbxxGrid);
////        hxzgsb10736Request.setCxstysbslxx(sbCxstysbSlxx);
////        //核心申报接口返回值
////        HXZGSB10736Response hxzgsb10736Response = null;
////        try {
////            hxzgsb10736Response = Gt3Invoker.invoke("SWZJ.HXZG.SB.SAVECXSTYSB", expand, hxzgsb10736Request, HXZGSB10736Response.class);
////        } catch (Exception e) {
////            if (e.getMessage().contains("1010010097000045")) {
////                yhsZeroSbResDTO.setMsg("纳税人该属期已申报，如需更正需通过申报错误更正业务处理");
////                yhsZeroSbResDTO.setRecode("-1");
////            }
////            log.error(e.getMessage());
////        }
////        StringBuffer msg = new StringBuffer();
////        msg.append("系统以为您完成以下：");
////        msg.append("");
////        msg.append("印花税零申报，如有疑问，请及时联系主管税务机关。");
////        String cxstysbuuid = hxzgsb10736Response.getReturnGrid().getReturnGridlb().get(0).getCxstysbuuid();
////
////        yhsZeroSbResDTO.setMsg(msg.toString());
////        yhsZeroSbResDTO.setCxstysbuuid(cxstysbuuid);
////        yhsZeroSbResDTO.setRecode("1");
////        return yhsZeroSbResDTO;
////    }
////
////    /**
////     * 校验征收品目子目
////     * @param checkZspmAndZszmReqVO
////     * @return
////     */
////    @Override
////    public Map<String, Object> checkZspmAndZszm(YhsCheckZspmAndZszmReqVO checkZspmAndZszmReqVO) {
////        Map<String, Object> resmap = new HashMap<>();
////        String djxh = checkZspmAndZszmReqVO.getDjxh();
////        String nsrsbh = checkZspmAndZszmReqVO.getNsrsbh();
////        String skssqq = checkZspmAndZszmReqVO.getSkssqq();
////        String skssqz = checkZspmAndZszmReqVO.getSkssqz();
////        String nsqxDm = checkZspmAndZszmReqVO.getNsqxDm();
////        String zspmDm = checkZspmAndZszmReqVO.getZspmDm();
////        String zszmDm = checkZspmAndZszmReqVO.getZszmDm();
////
//////        CxSfzrdxxReqVO cxSfzrdxxReqVO = new CxSfzrdxxReqVO();
//////        cxSfzrdxxReqVO.setDjxh(djxh);
//////        cxSfzrdxxReqVO.setSkssqq("");
//////        cxSfzrdxxReqVO.setSkssqz("");
//////        cxSfzrdxxReqVO.setZsxmDm("10111");
//////        ServerResponse<CxSfzrdxxResVO> cxSfzrdxxResVOServerResponse
//////                = nsrxxcxApi.querySfzrdxxBySql(cxSfzrdxxReqVO);
//////        CxSfzrdxxResVO cxSfzrdxxResVO = cxSfzrdxxResVOServerResponse.getResponse().getData();
//////        List<SfzrdxxVO> sfzrdxxList = cxSfzrdxxResVO.getSfzrdxxList();
////
////        final CxSfzrdxxResVO cxSfzrdxxResVO = new CxSfzrdxxResVO();
////        final ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
////        reqVO.setDjxh(djxh);
////        reqVO.setNsrsbh(nsrsbh);
////        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVO = nsrxxApi.getNsrxxByNsrsbh(reqVO);
////        final List<SfzrdmxxxVO> sfzrdxxList = nsrxxVO.getData().getSfzrdmxxx();
////        //1.过滤掉其他zsxmDm
////        if (sfzrdxxList != null && sfzrdxxList.size() > 0) {
////            for (int i = sfzrdxxList.size() - 1;i >= 0; i--) {
////                SfzrdmxxxVO sfzrdxxVO = sfzrdxxList.get(i);
////                String zsxmDm = sfzrdxxVO.getZsxmDm();
////                if (!"10111".equals(zsxmDm)) {
////                    sfzrdxxList.remove(i);
////                } else {
////                    final Date rdYxqq = sfzrdxxVO.getRdyxqq();
////                    final Date rdYxqz = sfzrdxxVO.getRdyxqz();
////                    if (!(GyUtils.cast2Date(skssqq).compareTo(rdYxqq) >= 0 && GyUtils.cast2Date(skssqz).compareTo(rdYxqz) <= 0 && GyUtils.cast2Date(skssqq).compareTo(rdYxqz) <= 0)) {
////                        if ("101110502".equals(sfzrdxxVO.getZspmDm())
////                                && GyUtils.cast2Date(skssqz).compareTo(rdYxqz) <= 0
////                                && GyUtils.cast2Date(skssqq).compareTo(rdYxqz) <= 0
////                                && skssqq.startsWith(GyUtils.cast2Str(sfzrdxxVO.getRdyxqq()).substring(0,4))
////                                && (skssqq.startsWith("2022") || skssqq.startsWith("2023"))
////                                && "10".equals(sfzrdxxVO.getNsqxDm())){
////                            continue;
////                        }
////                        sfzrdxxList.remove(i);
////                    }
////                }
////            }
////        }
////        // 查询税费种未认定已申报信息
////        List<Map<String,String>> sfzwrdysbZspmList = new ArrayList<>();
////        final List<CxsrDSfzwrdysbxxVO> yhsSfzwrdysbxxList = yhsJ3cxApi.querysfzwrdysbxx(djxh, "10111");
////        if (!GyUtils.isNull(yhsSfzwrdysbxxList)) {
////            for (CxsrDSfzwrdysbxxVO sfzwrdysbxxVO : yhsSfzwrdysbxxList) {
////                Map<String,String> map = new HashMap<>();
////                if (!"11".equals(sfzwrdysbxxVO.getNsqxDm())) {
////                    map.put("zspmDm",sfzwrdysbxxVO.getZspmDm());
////                    map.put("nsqxDm",sfzwrdysbxxVO.getNsqxDm());
////                    sfzwrdysbZspmList.add(map);
////                }
////            }
////        }
////        //查询已申报信息
////        String ysbskssqq = skssqq.substring(0, 4) + "-01-01";
////        String ysbskssqz = skssqz.substring(0, 4) + "-12-31";
////        List<YhshbcjxxbVO> yhshbcjxxbVOList = znsbNssbYhscjMapper.queryYhslfysbsymx(djxh, ysbskssqq, ysbskssqz);
////        if (GyUtils.isNull(zszmDm)){
////            if ("11".equals(nsqxDm)){
////                if (!GyUtils.isNull(sfzrdxxList)) {
////                    for (SfzrdmxxxVO sfzrdxxVO : sfzrdxxList) {
////                        if (!zspmDm.equals(sfzrdxxVO.getZspmDm()) || !GyUtils.isNull(sfzrdxxVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("10".equals(sfzrdxxVO.getNsqxDm()) || "08".equals(sfzrdxxVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季（年）税（费）种认定，不允许按次申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(sfzwrdysbZspmList)) {
////                    for (Map<String, String> map : sfzwrdysbZspmList) {
////                        if (!zspmDm.equals(map.get("zspmDm"))) {
////                            continue;
////                        }
////                        if ("10".equals(map.get("nsqxDm")) || "08".equals(map.get("nsqxDm"))) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季（年）税（费）种认定，不允许按次申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(yhshbcjxxbVOList)) {
////                    for (YhshbcjxxbVO yhshbcjxxbVO : yhshbcjxxbVOList) {
////                        if (!zspmDm.equals(yhshbcjxxbVO.getZspmDm()) || !GyUtils.isNull(yhshbcjxxbVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("10".equals(yhshbcjxxbVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按年申报记录，不允许按次申报。");
////                            return resmap;
////                        }
////                        if ("08".equals(yhshbcjxxbVO.getNsqxDm())
////                                && GyUtils.cast2Date(skssqq).compareTo(GyUtils.cast2Date(yhshbcjxxbVO.getSkssqq()))>=0
////                                && GyUtils.cast2Date(skssqz).compareTo(GyUtils.cast2Date(yhshbcjxxbVO.getSkssqz()))<=0) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季申报记录，不允许按次申报。");
////                            return resmap;
////                        }
////                    }
////                }
////            }else if ("10".equals(nsqxDm)){
////                if (!GyUtils.isNull(sfzrdxxList)) {
////                    for (SfzrdmxxxVO sfzrdxxVO : sfzrdxxList) {
////                        if (!zspmDm.equals(sfzrdxxVO.getZspmDm()) || !GyUtils.isNull(sfzrdxxVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("08".equals(sfzrdxxVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季税（费）种认定，不允许按年申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(sfzwrdysbZspmList)) {
////                    for (Map<String, String> map : sfzwrdysbZspmList) {
////                        if (!zspmDm.equals(map.get("zspmDm"))) {
////                            continue;
////                        }
////                        if ("08".equals(map.get("nsqxDm"))) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季税（费）种认定，不允许按年申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(yhshbcjxxbVOList)) {
////                    for (YhshbcjxxbVO yhshbcjxxbVO : yhshbcjxxbVOList) {
////                        if (!zspmDm.equals(yhshbcjxxbVO.getZspmDm()) || !GyUtils.isNull(yhshbcjxxbVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("08".equals(yhshbcjxxbVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季申报记录，不允许按年申报。");
////                            return resmap;
////                        }
////                        if ("11".equals(yhshbcjxxbVO.getNsqxDm())){
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按次申报记录，不允许按年申报。");
////                            return resmap;
////                        }
////                    }
////                }
////            }else if ("08".equals(nsqxDm)){
////                if (!GyUtils.isNull(sfzrdxxList)) {
////                    for (SfzrdmxxxVO sfzrdxxVO : sfzrdxxList) {
////                        if (!zspmDm.equals(sfzrdxxVO.getZspmDm()) || !GyUtils.isNull(sfzrdxxVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("10".equals(sfzrdxxVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按年税（费）种认定，不允许按季申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(sfzwrdysbZspmList)) {
////                    for (Map<String, String> map : sfzwrdysbZspmList) {
////                        if (!zspmDm.equals(map.get("zspmDm"))) {
////                            continue;
////                        }
////                        if ("10".equals(map.get("nsqxDm"))) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按年税（费）种认定，不允许按季申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(yhshbcjxxbVOList)) {
////                    for (YhshbcjxxbVO yhshbcjxxbVO : yhshbcjxxbVOList) {
////                        if (!zspmDm.equals(yhshbcjxxbVO.getZspmDm()) || !GyUtils.isNull(yhshbcjxxbVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("10".equals(yhshbcjxxbVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按年申报记录，不允许按季申报。");
////                            return resmap;
////                        }
////                        if ("11".equals(yhshbcjxxbVO.getNsqxDm())
////                                && GyUtils.cast2Date(yhshbcjxxbVO.getSkssqq()).compareTo(GyUtils.cast2Date(skssqq))>=0
////                                && GyUtils.cast2Date(yhshbcjxxbVO.getSkssqz()).compareTo(GyUtils.cast2Date(skssqz))<=0){
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按次申报记录，不允许按季申报。");
////                            return resmap;
////                        }
////                    }
////                }
////            }
////        }else {
////            List<Map<String,String>> sfzwrdysbZszmList = new ArrayList<>();
////            final List<CxsrDSfzwrdysbxxVO> yhsSfzwrdysbxxByZszmList = yhsJ3cxApi.querysfzwrdysbxxByZszm(djxh, "10111",zspmDm);
////            if (!GyUtils.isNull(yhsSfzwrdysbxxByZszmList)) {
////                for (CxsrDSfzwrdysbxxVO sfzwrdysbxxVO : yhsSfzwrdysbxxByZszmList) {
////                    Map<String,String> map = new HashMap<>();
////                    if (!"11".equals(sfzwrdysbxxVO.getNsqxDm())) {
////                        map.put("zspmDm",sfzwrdysbxxVO.getZspmDm());
////                        map.put("zszmDm",sfzwrdysbxxVO.getZszmDm());
////                        map.put("nsqxDm",sfzwrdysbxxVO.getNsqxDm());
////                        sfzwrdysbZszmList.add(map);
////                    }
////                }
////            }
////            if ("11".equals(nsqxDm)){
////                if (!GyUtils.isNull(sfzrdxxList)) {
////                    for (SfzrdmxxxVO sfzrdxxVO : sfzrdxxList) {
////                        if (!zspmDm.equals(sfzrdxxVO.getZspmDm()) || !zszmDm.equals(sfzrdxxVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("10".equals(sfzrdxxVO.getNsqxDm()) || "08".equals(sfzrdxxVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季（年）税（费）种认定，不允许按次申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(sfzwrdysbZszmList)) {
////                    for (Map<String, String> map : sfzwrdysbZszmList) {
////                        if (!zspmDm.equals(map.get("zspmDm")) || !zszmDm.equals(map.get("zszmDm"))) {
////                            continue;
////                        }
////                        if ("10".equals(map.get("nsqxDm")) || "08".equals(map.get("nsqxDm"))) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季（年）税（费）种认定，不允许按次申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(yhshbcjxxbVOList)) {
////                    for (YhshbcjxxbVO yhshbcjxxbVO : yhshbcjxxbVOList) {
////                        if (!zspmDm.equals(yhshbcjxxbVO.getZspmDm()) || !zszmDm.equals(yhshbcjxxbVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("10".equals(yhshbcjxxbVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按年申报记录，不允许按次申报。");
////                            return resmap;
////                        }
////                        if ("08".equals(yhshbcjxxbVO.getNsqxDm())
////                                && GyUtils.cast2Date(skssqq).compareTo(GyUtils.cast2Date(yhshbcjxxbVO.getSkssqq()))>=0
////                                && GyUtils.cast2Date(skssqz).compareTo(GyUtils.cast2Date(yhshbcjxxbVO.getSkssqz()))<=0) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季申报记录，不允许按次申报。");
////                            return resmap;
////                        }
////                    }
////                }
////            }else if ("10".equals(nsqxDm)){
////                if (!GyUtils.isNull(sfzrdxxList)) {
////                    for (SfzrdmxxxVO sfzrdxxVO : sfzrdxxList) {
////                        if (!zspmDm.equals(sfzrdxxVO.getZspmDm()) || !zszmDm.equals(sfzrdxxVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("08".equals(sfzrdxxVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季税（费）种认定，不允许按年申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(sfzwrdysbZszmList)) {
////                    for (Map<String, String> map : sfzwrdysbZszmList) {
////                        if (!zspmDm.equals(map.get("zspmDm")) || !zszmDm.equals(map.get("zszmDm"))) {
////                            continue;
////                        }
////                        if ("08".equals(map.get("nsqxDm"))) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季税（费）种认定，不允许按年申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(yhshbcjxxbVOList)) {
////                    for (YhshbcjxxbVO yhshbcjxxbVO : yhshbcjxxbVOList) {
////                        if (!zspmDm.equals(yhshbcjxxbVO.getZspmDm()) || !zszmDm.equals(yhshbcjxxbVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("08".equals(yhshbcjxxbVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按季申报记录，不允许按年申报。");
////                            return resmap;
////                        }
////                        if ("11".equals(yhshbcjxxbVO.getNsqxDm())){
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按次申报记录，不允许按年申报。");
////                            return resmap;
////                        }
////                    }
////                }
////            }else if ("08".equals(nsqxDm)){
////                if (!GyUtils.isNull(sfzrdxxList)) {
////                    for (SfzrdmxxxVO sfzrdxxVO : sfzrdxxList) {
////                        if (!zspmDm.equals(sfzrdxxVO.getZspmDm()) || !zszmDm.equals(sfzrdxxVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("10".equals(sfzrdxxVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按年税（费）种认定，不允许按季申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(sfzwrdysbZszmList)) {
////                    for (Map<String, String> map : sfzwrdysbZszmList) {
////                        if (!zspmDm.equals(map.get("zspmDm")) || !zszmDm.equals(map.get("zszmDm"))) {
////                            continue;
////                        }
////                        if ("10".equals(map.get("nsqxDm"))) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按年税（费）种认定，不允许按季申报。");
////                            return resmap;
////                        }
////                    }
////                }
////                if (!GyUtils.isNull(yhshbcjxxbVOList)) {
////                    for (YhshbcjxxbVO yhshbcjxxbVO : yhshbcjxxbVOList) {
////                        if (!zspmDm.equals(yhshbcjxxbVO.getZspmDm()) || !zszmDm.equals(yhshbcjxxbVO.getZszmDm())) {
////                            continue;
////                        }
////                        if ("10".equals(yhshbcjxxbVO.getNsqxDm())) {
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按年申报记录，不允许按季申报。");
////                            return resmap;
////                        }
////                        if ("11".equals(yhshbcjxxbVO.getNsqxDm())
////                                && GyUtils.cast2Date(yhshbcjxxbVO.getSkssqq()).compareTo(GyUtils.cast2Date(skssqq))>=0
////                                && GyUtils.cast2Date(yhshbcjxxbVO.getSkssqz()).compareTo(GyUtils.cast2Date(skssqz))<=0){
////                            resmap.put("Code", "-1");
////                            resmap.put("Message", "本期存在按次申报记录，不允许按季申报。");
////                            return resmap;
////                        }
////                    }
////                }
////            }
////        }
////        resmap.put("Code","1001");
////        resmap.put("Message","校验成功");
////        return resmap;
////    }
//
//    /**
//     * @param nsrxxFormVO 纳税人信息
//     * @throws ServiceException 通用架构异常
//     * @name 监控重复采集
//     * @description 监控重复采集
//     * @time 创建时间:2020-07-17 上午 10:44:45
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private void checkSfcj(YhscjNsrxxDTO nsrxxFormVO) throws ServiceException {
//        final String djxh = nsrxxFormVO.getDjxh();
//        final String skssqq = nsrxxFormVO.getSkssqq();
//        final String skssqz = nsrxxFormVO.getSkssqz();
//        final String sbsxdm1 = nsrxxFormVO.getSbsxDm1();
//        final String nsqxDm = nsrxxFormVO.getNsqxDm();
//        if ("11".equals(nsqxDm)) {
//            //按次采集不监控重复采集
//            return;
//        }
//        if (GyUtils.isNull(sbsxdm1)) {
//            throw ServiceExceptionUtil.exception(-1, "申报属性不能为空!");
//        }
//        final List<ZnsbNssbCxsbtxxDO> sbCxssybtxxGridlbVOS = znsbNssbCxsbtxxMapper.queryYcjxxBt(djxh, skssqq, skssqz, sbsxdm1);
//        //对属期进行过滤
//        for (int i = sbCxssybtxxGridlbVOS.size() - 1; i >= 0; i--) {
//            final ZnsbNssbCxsbtxxDO yhscjBtxx = sbCxssybtxxGridlbVOS.get(i);
//            if (!GyUtils.cast2Str(yhscjBtxx.getSkssqq()).substring(0, 10).equals(skssqq) || !GyUtils.cast2Str(yhscjBtxx.getSkssqz()).substring(0, 10).equals(skssqz)) {
//                sbCxssybtxxGridlbVOS.remove(i);
//            }
//        }
//        //如果本次采集是正常申报采集
//        if ("11".equals(sbsxdm1)) {
//            if (!GyUtils.isNull(sbCxssybtxxGridlbVOS)) {
//                throw ServiceExceptionUtil.exception(-1, "本属期已做过该税种的采集,如需更正需要通过更正采集处理。");
//            }
//        } else {
//            //自行补正
//            if (!GyUtils.isNull(sbCxssybtxxGridlbVOS)) {
//                for (ZnsbNssbCxsbtxxDO sbCxssybtxxGridlbVO : sbCxssybtxxGridlbVOS) {
//                    if ("N".equals(sbCxssybtxxGridlbVO.getYsbbz())) {
//                        throw ServiceExceptionUtil.exception(-1, "本属期已做过该税种的采集,如需更正需要通过更正采集处理。");
//                    }
//                }
//            }
//        }
//    }
//
////    /**
////     * @param nsrxxFormVO     纳税人信息
////     * @param cjVOS       采集列表
////     * @param xzcjlVOS     可变列表
////     * @param sbCxsYhscjxxVOS 采集信息
////     * @param sbCxsYhscjfjVOS 采集可变列信息
////     * @param btxxVO          表头
////     * @throws SwordBaseCheckedException 通用架构异常
////     * @name 印花税采集保存
////     * @description 印花税采集保存
////     * @time 创建时间
////     * <AUTHOR>
////     * @history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    @SuppressWarnings("unchecked")
////    private void assemblyXzxx(YhscjNsrxxDTO nsrxxFormVO,List<YhshbcjxxbDTO> cjVOS,List<YhscjkblxxDTO> xzcjlVOS, List<YhshbcjxxbDTO> sbCxsYhscjxxVOS,
////                              List<YhscjkblxxDTO>  sbCxsYhscjfjVOS, YhscjBtxxDTO btxxVO) throws ServiceException {
////        final String uuid = GyUtils.getUuid();
////        final String lrrDm = nsrxxFormVO.getNsrsbh();
////        final Date nowDate = new Date();
////        //印花税采集主表信息
////        for (int i = 0; i < cjVOS.size(); i++) {
////            YhshbcjxxbDTO sbCxsYhscjxxVO = new YhshbcjxxbDTO();
////            sbCxsYhscjxxVO = cjVOS.get(i);
////            sbCxsYhscjxxVO.setUuid(GyUtils.getUuid());
////            sbCxsYhscjxxVO.setZbuuid(uuid);
////            //需要生成应税凭证税务编号
////            sbCxsYhscjxxVO.setYspzswbh(assemblyyspzswbh(nsrxxFormVO));
////            sbCxsYhscjxxVO.setLrrDm(lrrDm);
////            sbCxsYhscjxxVO.setLrrq(GyUtils.cast2Str(nowDate));
////            sbCxsYhscjxxVO.setZfbz1("N");
////            sbCxsYhscjxxVOS.add(sbCxsYhscjxxVO);
////        }
////
////        //组装可变列信息
////        final Map<String, String> rdMap = new HashMap<>();
////        for (int i = 0; i < sbCxsYhscjxxVOS.size(); i++) {
////            final YhshbcjxxbDTO cjxxbVO = sbCxsYhscjxxVOS.get(i);
////            rdMap.put(cjxxbVO.getTimebz(), cjxxbVO.getUuid());
////        }
////        //获取新增list
////        for (YhscjkblxxDTO cjxxVO : xzcjlVOS) {
////            final String syuuid = rdMap.get(cjxxVO.getTimebz());
////            YhscjkblxxDTO sbCxsYhscjfjVO = new YhscjkblxxDTO();
////            sbCxsYhscjfjVO = cjxxVO;
////            sbCxsYhscjfjVO.setUuid(GyUtils.getUuid());
////            //主表uuid和税源采集表头是一样的
////            sbCxsYhscjfjVO.setZbuuid(uuid);
////            //syuuid用于存储明细信息里对应的uuid
////            sbCxsYhscjfjVO.setSyuuid(syuuid);
////            sbCxsYhscjfjVOS.add(sbCxsYhscjfjVO);
////        }
////
////        //构建表头信息
////        btxxVO = BeanUtils.toBean(sbCxsYhscjxxVOS.get(0),YhscjBtxxDTO.class);
////        btxxVO = BeanUtils.toBean(nsrxxFormVO,YhscjBtxxDTO.class);
////        btxxVO.setUuid(uuid);
////        btxxVO.setYzpzzlDm(GYDm02Constants.getDmGyDzbzdszlYhsnssbb());
////        btxxVO.setYsbbz("N");
////        btxxVO.setZfbz1("N");
////    }
//

    /**
     * @param nsrxx  纳税人信息
     * @param syList 税源list
     * @return List<Map < String, Object>> 税源
     * @name 查询印花税税源信息-申报页面调用
     * @description 相关说明
     * @time 创建时间:2020-5-25下午05:26:38
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
//    @Service(serviceName = "SWZJ.HXZG.SB.QUERYYHSSYXXFORSB", cacheValue = false, memo = "业务服务：查询印花税税源信息")
    @Override
    public List<Map<String, Object>> queryYhsSyxxForSb(CxstysbNsrxxDTO nsrxx, List<SymxDTO> syList) {
        final String skssqq = nsrxx.getSkssqq();
        final String skssqz = nsrxx.getSkssqz();
        final String cwgzbz = nsrxx.getScenceCs();
        final String nsqxDm = nsrxx.getNsqxDm();
        final List<Map<String, Object>> symxList = this.querySyxx(nsrxx, syList, nsqxDm);
        //过滤纳税期限和属期匹配的
        final List<Map<String, Object>> symxrtList = new ArrayList<>();
        if (!GyUtils.isNull(symxList)) {
            for (Map<String, Object> mp : symxList) {
                //对查询出的数据进行过滤,增加对提前申报的控制
                //先检查是否含有按次申报
                final String sbbz = (String) mp.get("ysbbz");
                final String skssqqSy = GyUtils.cast2Str(mp.get("skssqq"));
                final String skssqzSy = GyUtils.cast2Str(mp.get("skssqz"));
                //非按次申报
                if (!skssqqSy.equals(skssqzSy)) {
                    //检查这笔采集是否有按次采集
                    final List<ZnsbNssbYhscjDO> cjxxbs = this.queryYcjxxByZbuuid((String) mp.get("uuid"));
                    boolean flag = false;
                    if (GyUtils.isNull(cjxxbs)) {
                        final List<ZnsbNssbYhscjDO> lfcjxxbs = this.queryYcjxxByZbuuid((String) mp.get("uuid"));
                        for (int i = 0; i < lfcjxxbs.size(); i++) {
                            flag = "11".equals(lfcjxxbs.get(i).getNsqxDm());
                            if (flag) {
                                break;
                            }
                        }
                    } else {
                        for (int i = 0; i < cjxxbs.size(); i++) {
                            flag = "11".equals(cjxxbs.get(i).getNsqxDm());
                            if (flag) {
                                break;
                            }
                        }
                    }
                    //不含有按次申报
                    if (!flag) {
                        final boolean sfksb = GYSbUtils.checkSfksb(null, null, skssqqSy, skssqzSy, nsrxx.getSfjktqsb());
                        if (!sfksb) {
                            continue;
                        }
                    }
                }
                if ("cwgzbz".equals(cwgzbz) || "N".equals(sbbz)) {
                    //数据处理:
                    mp.put("syuuid", mp.get("hxbuuid"));
                    symxrtList.add(mp);
                }
            }
        }
        return symxrtList;
    }

//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updatePclshByZbuuid(String ywuuid, String pclsh) {
//        //根据uuid更新表头信息表的pclsh
//        final ZnsbNssbCxsbtxxDO cxsbtxxDO = new ZnsbNssbCxsbtxxDO();
//        cxsbtxxDO.setPclsh(pclsh);
//        cxsbtxxDO.setBczt(BcztConstants.CLZ);
//        final QueryWrapper<ZnsbNssbCxsbtxxDO> btQw = new QueryWrapper<>();
//        btQw.lambda().eq(ZnsbNssbCxsbtxxDO::getUuid, ywuuid);
//        znsbNssbCxsbtxxMapper.update(cxsbtxxDO, btQw);
//
//        //根据zbuuid更新印花税采集表表的pclsh
//        final ZnsbNssbYhscjDO yhscjDO = new ZnsbNssbYhscjDO();
//        yhscjDO.setPclsh(pclsh);
//        yhscjDO.setBczt(BcztConstants.CLZ);
//        final QueryWrapper<ZnsbNssbYhscjDO> cjQw = new QueryWrapper<>();
//        cjQw.lambda().eq(ZnsbNssbYhscjDO::getZbuuid, ywuuid);
//        znsbNssbYhscjMapper.update(yhscjDO, cjQw);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateSbztByZbuuid(String ywuuid, String pclsh, String sbuuid, String sbztDm, String msg) {
//        log.info("保存成功回调保存数据开始ywuuid----->{},pclsh---->{},sbuuid---->{}", ywuuid, pclsh, sbuuid);
//        //根据uuid更新表头信息表的pclsh
//        final ZnsbNssbCxsbtxxDO cxsbtxxDO = new ZnsbNssbCxsbtxxDO();
//        cxsbtxxDO.setHxbuuid(sbuuid);
//        cxsbtxxDO.setBczt(sbztDm);
//        if (GyUtils.isNotNull(msg)) {
//            cxsbtxxDO.setCwxx(msg);
//        }
//        final QueryWrapper<ZnsbNssbCxsbtxxDO> btQw = new QueryWrapper<>();
//        btQw.lambda().eq(ZnsbNssbCxsbtxxDO::getUuid, ywuuid)
//                .eq(ZnsbNssbCxsbtxxDO::getPclsh, pclsh);
//        znsbNssbCxsbtxxMapper.update(cxsbtxxDO, btQw);
//
//        //根据zbuuid更新印花税采集表表的pclsh
//        final ZnsbNssbYhscjDO yhscjDO = new ZnsbNssbYhscjDO();
//        yhscjDO.setHxbuuid(sbuuid);
//        yhscjDO.setBczt(sbztDm);
//        if (CLCG.equals(sbztDm)) {
//            yhscjDO.setGzbz("1");
//        }
//        final QueryWrapper<ZnsbNssbYhscjDO> cjQw = new QueryWrapper<>();
//        cjQw.lambda().eq(ZnsbNssbYhscjDO::getZbuuid, ywuuid)
//                .eq(ZnsbNssbYhscjDO::getPclsh, pclsh);
//        znsbNssbYhscjMapper.update(yhscjDO, cjQw);
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public void uppdateYhsCjxx(String ywuuid, String pclsh, String sbuuid, String sbztDm) {
//        //根据uuid更新表头信息表的pclsh
//        final ZnsbNssbCxsbtxxDO cxsbtxxDO = new ZnsbNssbCxsbtxxDO();
//        cxsbtxxDO.setHxbuuid(sbuuid);
//        cxsbtxxDO.setBczt(sbztDm);
//        final QueryWrapper<ZnsbNssbCxsbtxxDO> btQw = new QueryWrapper<>();
//        btQw.lambda().eq(ZnsbNssbCxsbtxxDO::getUuid, ywuuid)
//                .eq(ZnsbNssbCxsbtxxDO::getPclsh, pclsh);
//        znsbNssbCxsbtxxMapper.update(cxsbtxxDO, btQw);
//
//        //根据zbuuid更新印花税采集表表的pclsh
//        final ZnsbNssbYhscjDO yhscjDO = new ZnsbNssbYhscjDO();
//        yhscjDO.setHxbuuid(sbuuid);
//        yhscjDO.setBczt(sbztDm);
//        final QueryWrapper<ZnsbNssbYhscjDO> cjQw = new QueryWrapper<>();
//        cjQw.lambda().eq(ZnsbNssbYhscjDO::getZbuuid, ywuuid)
//                .eq(ZnsbNssbYhscjDO::getPclsh, pclsh);
//        znsbNssbYhscjMapper.update(yhscjDO, cjQw);
//    }
//
//    @Override
//    public String queryPclshByUuid(String ywuuid) {
//        //根据uuid查询表头信息表的pclsh
//        String pclsh = null;
//        final ZnsbNssbCxsbtxxDO cxsbtxxDO = znsbNssbCxsbtxxMapper.selectById(ywuuid);
//        if (GyUtils.isNotNull(cxsbtxxDO)) {
//            pclsh = cxsbtxxDO.getPclsh();
//        }
//        return pclsh;
//    }
//

    /**
     * @param nsrxx    纳税人信息
     * @param cxsylist 查询税源
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @name 查询税源
     * @description 相关说明
     * @time 创建时间:2020/7/30 17:07
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<Map<String, Object>> querySyxx(CxstysbNsrxxDTO nsrxx, List<SymxDTO> cxsylist, String nsqxDm) {
        List<Map<String, Object>> syList = new ArrayList<>();
        if (!GyUtils.isNull(cxsylist)) {
            final List<Map<String, Object>> clsyList = this.queryYhssyxx(nsrxx, cxsylist);
            if (!GyUtils.isNull(clsyList)) {
                syList.addAll(clsyList);
            }
        } else {
            //查询纳税人信息中
            final List<ZnsbNssbCxsbtxxDO> yhscjBtxxes = znsbNssbCxsbtxxMapper.queryYcjxxBtforCx(nsrxx.getDjxh(), nsrxx.getSkssqq(), nsrxx.getSkssqz(), nsrxx.getSbsxDm1(), nsqxDm);
            final List<Map<String, Object>> maps = new ArrayList<>();
            for (ZnsbNssbCxsbtxxDO cxsbtxxDO : yhscjBtxxes) {
                Map<String, Object> map = BeanUtil.beanToMap(cxsbtxxDO);
                maps.add(map);
            }
            syList.addAll(maps);
        }
        syList = syList.stream().sorted(Comparator.comparing((Map<String, Object> m) -> GyUtils.cast2Str(m.get("skssqq"))) // 先按skssqq排序
                .thenComparing((Map<String, Object> m) -> GyUtils.cast2Str(m.get("skssqz")))) // 如果skssqq相同，则按skssqz排序
                .collect(Collectors.toList());

        //对list进行排序
//        final SwordSortUtils.OrderType orderType = SwordSortUtils.OrderType.ASC;
//        final SwordSortUtils.SortDescription description1 = new SwordSortUtils.SortDescription("skssqq", orderType);
//        final SwordSortUtils.SortDescription description2 = new SwordSortUtils.SortDescription("skssqz", orderType);
//        syList = SwordSortUtils.sortMapList(syList, description1, description2);
        return syList;
    }

    /**
     * @param nsr      纳税人
     * @param cxsyList 查询的税源列表
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @name 查询车辆税源
     * @description 相关说明
     * @time 创建时间:2020/7/30 17:08
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<Map<String, Object>> queryYhssyxx(CxstysbNsrxxDTO nsr, List<SymxDTO> cxsyList) {
        final List<Map<String, Object>> syList = new ArrayList<>();
        final String djxh = nsr.getDjxh();
        final String skssqq = nsr.getSkssqq();
        final String skssqz = nsr.getSkssqz();
        final String sbsxDm1 = nsr.getSbsxDm1();
        //税源不为空根据税源查询，税源为空查询全量
        if (!GyUtils.isNull(cxsyList)) {
            final StringBuffer sbf = new StringBuffer();
            //对税源进行去重处理
            final Set<String> sySet = new HashSet<>();
            for (SymxDTO v : cxsyList) {
                sySet.add(v.getSyuuid());
            }
            for (String sy : sySet) {
                //进行一次明细查询
                final ZnsbNssbCxsbtxxDO yhscjBtxx = znsbNssbCxsbtxxMapper.queryYcjxxBtByUUID(sy);
                final Map<String, Object> map = BeanUtil.beanToMap(yhscjBtxx);
                syList.add(map);
            }
        } else {
            final List<ZnsbNssbCxsbtxxDO> yhscjBtxxes = znsbNssbCxsbtxxMapper.queryYcjxxBt(djxh, skssqq, skssqz, sbsxDm1);
            final List<Map<String, Object>> maps = new ArrayList<>();
            for (ZnsbNssbCxsbtxxDO cxsbtxxDO : yhscjBtxxes) {
                Map<String, Object> map = BeanUtil.beanToMap(cxsbtxxDO);
                maps.add(map);
            }
            syList.addAll(maps);
        }
        return syList;
    }

    /**
     * @param uuid uudi
     * @return SBCxsYhscjxxVO
     * @name 根据uuid查询已采集信息
     * @description 根据uuid查询已采集信息
     * @time 创建时间:2020-07-17 上午 10:44:45
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<ZnsbNssbYhscjDO> queryYcjxxByZbuuid(String uuid) {
        return znsbNssbYhscjMapper.queryyhsycjxxbyuuid(uuid);
    }
//
//    /**
//     * 处理查询印花税税源信息返回的数据
//     *
//     * @param jbxxmxsj
//     * @param symxGrid
//     * @param yhskblxxGrid
//     * @return
//     */
//    @Override
//    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
//    public void processYhssyxxApiResponse(JbxxmxsjVO jbxxmxsj, List<YhshbcjxxbDTO> symxGrid, List<YhscjkblxxDTO> yhskblxxGrid) {
//        //根据zbuuid对税源信息分组
//        final Map<String, List<YhshbcjxxbDTO>> cjxxmxList = symxGrid.stream().collect(Collectors.groupingBy(YhshbcjxxbDTO::getZbuuid));
//        //新增财行税表头信息集合
//        final List<ZnsbNssbCxsbtxxDO> insertCxsbtxxDOList = new ArrayList<>();
//        //新增印花税采集表信息
//        final List<ZnsbNssbYhscjDO> insertYhscjDOList = new ArrayList<>();
//        //更新印花税采集表信息
//        final List<ZnsbNssbYhscjDO> updateYhscjDOList = new ArrayList<>();
//        //作废印花税采集表信息
//        final List<ZnsbNssbYhscjDO> deleteYhscjDOList = new ArrayList<>();
//        //新增印花税可变列信息
//        final List<ZnsbNssbYhslfkblDO> insertYhskblDOList = new ArrayList<>();
//        //本地表中保存的财行税表头信息uuid
//        final List<String> oldZbuuidList = new ArrayList<>();
//        //循环zbuuid分组
//        cjxxmxList.entrySet().forEach(cjxxmx -> {
//            final String hxbuuid = cjxxmx.getKey();
//            final List<YhshbcjxxbDTO> yhshbcjxxbDTOList = new ArrayList<>(cjxxmx.getValue());
//            //根据hxbuuid查询财行税申报表表头信息，如果没有则新增
//            final ZnsbNssbCxsbtxxDO cxsbtxxDO = znsbNssbCxsbtxxMapper.queryYcjxxBtByHxbuuid(hxbuuid);
//            if (GyUtils.isNull(cxsbtxxDO)) {
//                //构建表头信息数据
//                final ZnsbNssbCxsbtxxDO nssbCxsbtxxDO = this.buildCxsBtxx(hxbuuid, jbxxmxsj, yhshbcjxxbDTOList.get(0));
//                //存入新增财行税表头信息List
//                insertCxsbtxxDOList.add(nssbCxsbtxxDO);
//                for (YhshbcjxxbDTO yhshbcjxxbDTO : yhshbcjxxbDTOList) {
//                    //组装印花税采集信息
//                    final ZnsbNssbYhscjDO yhscjDO = this.buildYhscjxx(yhshbcjxxbDTO);
//                    //存入新增印花税采集表信息List
//                    insertYhscjDOList.add(yhscjDO);
//                    //获取该税源下的可变列信息
//                    if (GyUtils.isNotNull(yhskblxxGrid)) {
//                        final List<YhscjkblxxDTO> yhscjkblxxDTOList = yhskblxxGrid.stream().filter(m -> m.getZbuuid().equals(yhscjDO.getUuid())).collect(Collectors.toList());
//                        if (GyUtils.isNotNull(yhscjkblxxDTOList)) {
//                            final List<ZnsbNssbYhslfkblDO> yhslfkblDOList = this.buildYhsKblxx(yhscjDO, yhscjkblxxDTOList);
//                            insertYhskblDOList.addAll(yhslfkblDOList);
//                        }
//                    }
//                }
//            } else {
//                //获取本地表中保存的财行税表头信息uuid,存入oldZbuuidList
//                final String oldUuid = cxsbtxxDO.getUuid();
//                oldZbuuidList.add(oldUuid);
//                //查询本地印花税采集表中zbuuid或hxbuuid是zbuuid的数据集合
//                final List<ZnsbNssbYhscjDO> znsbNssbYhscjDOList = znsbNssbYhscjMapper.queryOldIdByHxbuuid(hxbuuid);
//                //对报文中返回的印花税采集信息与本地印花税采集表中数据根据uuid进行过滤
//                //使用Iterator遍历报文中返回的印花税采集信息列表
//                final Iterator<YhshbcjxxbDTO> yhscjxxbDTOIterator = yhshbcjxxbDTOList.iterator();
//                //使用Map存储本地印花税采集表数据，以便快速查找
//                Map<String, ZnsbNssbYhscjDO> yhscjxxbDOMap = znsbNssbYhscjDOList.stream().collect(Collectors.toMap(ZnsbNssbYhscjDO::getUuid, Function.identity()));
//                while (yhscjxxbDTOIterator.hasNext()) {
//                    final YhshbcjxxbDTO yhshbcjxxbDTO = yhscjxxbDTOIterator.next();
//                    final String newUuid = yhshbcjxxbDTO.getUuid();
//                    final ZnsbNssbYhscjDO yhscjDO = yhscjxxbDOMap.get(newUuid);
//                    if (yhscjDO != null) {
//                        if (!"20".equals(yhscjDO.getBczt())) {
//                            updateYhscjDOList.add(this.buildYhscjxx(yhshbcjxxbDTO));
//                        }
//                        //获取该税源下的可变列信息
//                        if (GyUtils.isNotNull(yhskblxxGrid)) {
//                            final List<YhscjkblxxDTO> yhscjkblxxDTOList = yhskblxxGrid.stream().filter(m -> m.getZbuuid().equals(newUuid)).collect(Collectors.toList());
//                            if (GyUtils.isNotNull(yhscjkblxxDTOList)) {
//                                final List<ZnsbNssbYhslfkblDO> yhslfkblDOList = this.buildYhsKblxx(yhscjDO, yhscjkblxxDTOList);
//                                insertYhskblDOList.addAll(yhslfkblDOList);
//                            }
//                        }
//                        //删除报文返回的印花税采集信息
//                        yhscjxxbDTOIterator.remove();
//                        //删除本地印花税采集表的印花税采集信息
//                        yhscjxxbDOMap.remove(newUuid);
//                    } else {
//                        //返回报文中印花税采集信息多出的存入insertYhscjDOList
//                        insertYhscjDOList.add(this.buildYhscjxx(yhshbcjxxbDTO));
//                    }
//
//                }
//                //本地印花税采集表多出的存入deleteYhscjDOList
//                deleteYhscjDOList.addAll(yhscjxxbDOMap.values());
//            }
//        });
//        //保存财行税表头信息
//        if (GyUtils.isNotNull(insertCxsbtxxDOList)) {
//            znsbNssbCxsbtxxService.saveBatch(insertCxsbtxxDOList);
//        }
//        //保存印花税采集信息
//        if (GyUtils.isNotNull(insertYhscjDOList)) {
//            znsbNssbYhscjService.saveBatch(insertYhscjDOList);
//        }
//        //更新印花税采集信息
//        if (GyUtils.isNotNull(updateYhscjDOList)) {
//            znsbNssbYhscjMapper.updateYhslfcjxx(updateYhscjDOList);
//        }
//        //作废印花税采集信息
//        if (GyUtils.isNotNull(deleteYhscjDOList)) {
//            znsbNssbYhscjMapper.zfYhssycjxx(deleteYhscjDOList);
//        }
//        //删除印花税可变列信息
//        if (GyUtils.isNotNull(oldZbuuidList)) {
//            znsbNssbYhslfkblMapper.deletekByZbuuid(oldZbuuidList);
//        }
//        //保存印花税可变列信息
//        if (GyUtils.isNotNull(insertYhskblDOList)) {
//            znsbNssbYhslfkblService.saveBatch(insertYhskblDOList);
//        }
//    }
//
//    @Override
//    public void updateYhssyxxWsb(YhsCxSyxxReqDTO yhsCxSyxxReqDTO) {
//        final List<YhscjNsrxxVO> yhscjNsrxxList = BeanUtils.toBean(yhsCxSyxxReqDTO.getYhscjNsrxxList(), YhscjNsrxxVO.class);
//        Optional<YhscjNsrxxVO> minSkssqq = yhscjNsrxxList.stream().min(Comparator.comparing(YhscjNsrxxVO::getSkssqq));
//        Optional<YhscjNsrxxVO> maxSkssqz = yhscjNsrxxList.stream().max(Comparator.comparing(YhscjNsrxxVO::getSkssqz));
//        final YhscjNsrxxDTO yhscjNsrxx = yhsCxSyxxReqDTO.getYhscjNsrxxList().get(0);
//        //乐企印花税错误更正初始化接口请求报文
//        final LqYhssyxxCxRequestDTO requestDTO = new LqYhssyxxCxRequestDTO();
//        requestDTO.setDjxh(yhscjNsrxx.getDjxh());
//        requestDTO.setSkssqq(minSkssqq.get().getSkssqq());
//        requestDTO.setSkssqz(maxSkssqz.get().getSkssqz());
//        //通过数据交换服务调用乐企接口
//        final String ywbw = JsonUtils.toJson(requestDTO);
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("CX00000004");
//        sjjhDTO.setYwbm(YzpzzlEnum.YHS.getDm());
//        sjjhDTO.setYwuuid(GyUtils.getUuid());
//        sjjhDTO.setDjxh(yhscjNsrxx.getDjxh());
//        sjjhDTO.setNsrsbh(yhscjNsrxx.getNsrsbh());
//        sjjhDTO.setXzqhszDm(yhscjNsrxx.getXzqhszDm());
//        sjjhDTO.setBwnr(ywbw);
//        final CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
//        if (GyUtils.isNotNull(result) && GyUtils.isNotNull(result.getData())) {
//            log.info("乐企返回印花税错误更正初始化接口报文{}", result);
//            final LqYhssyxxCxResponseDTO resDTO = JsonUtils.toBean((String) result.getData(), LqYhssyxxCxResponseDTO.class);
//            if (!"00".equals(resDTO.getReturncode())) {
//                log.error("调用乐企印花税错误更正初始化接口失败：{}", resDTO.getReturnmsg());
//            }
//            final List<YhshbcjxxbDTO> symxGrid = resDTO.getSymxGrid();
//            List<YhscjkblxxDTO> yhskblxxGrid = resDTO.getYhskblxxGrid();
//            if (GyUtils.isNotNull(symxGrid)) {
//                //获取纳税人基本信息
//                final ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
//                reqVO.setDjxh(yhscjNsrxx.getDjxh());
//                reqVO.setNsrsbh(yhscjNsrxx.getNsrsbh());
//                final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVO = nsrxxApi.getNsrxxByNsrsbh(reqVO);
//                if (GyUtils.isNull(nsrxxVO) || GyUtils.isNull(nsrxxVO.getData()) || GyUtils.isNull(nsrxxVO.getData().getJbxxmxsj())) {
//                    return;
//                }
//                final JbxxmxsjVO jbxxmxsj = nsrxxVO.getData().getJbxxmxsj().get(0);
//                yhssycjService.processYhssyxxApiResponse(jbxxmxsj, symxGrid, yhskblxxGrid);
//            }
//        }
//    }
//
//    /**
//     * 印花税重复申报校验
//     *
//     * @param yhsHgjyReqVO
//     * @return
//     */
//    @Override
//    public Map<String, Object> yhsCfsbJy(YhsHgjyReqVO yhsHgjyReqVO) {
//        final Map<String, Object> resMap = new HashMap<>();
//        String code = "1001";
//        String message = "";
//        final QueryWrapper<ZnsbNssbCxsbtxxDO> qw = new QueryWrapper<>();
//        qw.lambda().eq(ZnsbNssbCxsbtxxDO::getDjxh, yhsHgjyReqVO.getDjxh())
//                .eq(ZnsbNssbCxsbtxxDO::getSkssqq, yhsHgjyReqVO.getSkssqq())
//                .eq(ZnsbNssbCxsbtxxDO::getSkssqz, yhsHgjyReqVO.getSkssqz());
//        List<ZnsbNssbCxsbtxxDO> znsbNssbCxsbtxxDOList = znsbNssbCxsbtxxMapper.selectList(qw);
//        if (GyUtils.isNotNull(znsbNssbCxsbtxxDOList)) {
//            code = "-1";
//            message = "您已完成本属期采集，如需调整，可进行修改";
//        }
//        resMap.put("Code", code);
//        resMap.put("Mseeage", message);
//        return resMap;
//    }
//
//
//    /**
//     * 组装财行税表头信息
//     *
//     * @param hxbuuid
//     * @param jbxxmxsj
//     * @param yhshbcjxxbDTO
//     * @return
//     */
//    public ZnsbNssbCxsbtxxDO buildCxsBtxx(String hxbuuid, JbxxmxsjVO jbxxmxsj, YhshbcjxxbDTO yhshbcjxxbDTO) {
//        final ZnsbNssbCxsbtxxDO nssbCxsbtxxDO = new ZnsbNssbCxsbtxxDO();
//        nssbCxsbtxxDO.setUuid(hxbuuid);
//        nssbCxsbtxxDO.setZgswskfjDm(jbxxmxsj.getZgswskfjDm());
//        nssbCxsbtxxDO.setZfbz1("N");
//        nssbCxsbtxxDO.setYsbbz("N");
//        nssbCxsbtxxDO.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
//        nssbCxsbtxxDO.setSbsxDm1("11");
//        nssbCxsbtxxDO.setDjxh(new BigDecimal(jbxxmxsj.getDjxh()));
//        nssbCxsbtxxDO.setSkssqq(GyUtils.cast2Date(yhshbcjxxbDTO.getSkssqq()));
//        nssbCxsbtxxDO.setSkssqz(GyUtils.cast2Date(yhshbcjxxbDTO.getSkssqz()));
//        nssbCxsbtxxDO.setHyDm(jbxxmxsj.getHyDm());
//        nssbCxsbtxxDO.setXzqhszDm(jbxxmxsj.getZcdzxzqhszDm());
//        nssbCxsbtxxDO.setJdxzDm(jbxxmxsj.getJdxzDm());
//        nssbCxsbtxxDO.setLrrq(new Date());
//        nssbCxsbtxxDO.setLrrsfid("SQGJ");
//        nssbCxsbtxxDO.setXgrq(new Date());
//        nssbCxsbtxxDO.setXgrsfid("SQGJ");
//        nssbCxsbtxxDO.setSjtbSj(new Date());
//        nssbCxsbtxxDO.setHxbuuid(hxbuuid);
//        nssbCxsbtxxDO.setBczt(BcztConstants.CLCG);
//        return nssbCxsbtxxDO;
//    }
//
//    /**
//     * 组装印花税采集信息
//     *
//     * @param yhshbcjxxbDTO
//     * @return
//     */
//    public ZnsbNssbYhscjDO buildYhscjxx(YhshbcjxxbDTO yhshbcjxxbDTO) {
//        final ZnsbNssbYhscjDO znsbNssbYhscjDO = BeanUtils.toBean(yhshbcjxxbDTO, ZnsbNssbYhscjDO.class);
//        znsbNssbYhscjDO.setHxbuuid(yhshbcjxxbDTO.getZbuuid());
//        znsbNssbYhscjDO.setJsjehjs(yhshbcjxxbDTO.getJsjehjs());
//        znsbNssbYhscjDO.setJmse(yhshbcjxxbDTO.getJmse());
//        znsbNssbYhscjDO.setYnse(yhshbcjxxbDTO.getYnse());
//        znsbNssbYhscjDO.setYbtse(yhshbcjxxbDTO.getYbtse());
//        znsbNssbYhscjDO.setBczt(BcztConstants.CLCG);
//        znsbNssbYhscjDO.setYsbbz("N");
//        znsbNssbYhscjDO.setSbsxdm1("11");
//        znsbNssbYhscjDO.setGzbz("1");
//        if (yhshbcjxxbDTO.getSkssqz().compareTo(yhshbcjxxbDTO.getSkssqq()) > 0) {
//            znsbNssbYhscjDO.setSfsfzrd("Y");
//            znsbNssbYhscjDO.setSfdeletebz("N");
//        } else {
//            znsbNssbYhscjDO.setSfsfzrd("N");
//            znsbNssbYhscjDO.setSfdeletebz("Y");
//        }
//        znsbNssbYhscjDO.setLrrq(new Date());
//        znsbNssbYhscjDO.setLrrsfid("SQGJ");
//        znsbNssbYhscjDO.setXgrq(new Date());
//        znsbNssbYhscjDO.setXgrsfid("SQGJ");
//        znsbNssbYhscjDO.setSjtbSj(new Date());
//        return znsbNssbYhscjDO;
//    }
//
//    /**
//     * 组装印花税可变列信息
//     *
//     * @param yhscjDO
//     * @param yhscjkblxxDTOList
//     * @return
//     */
//    public List<ZnsbNssbYhslfkblDO> buildYhsKblxx(ZnsbNssbYhscjDO yhscjDO, List<YhscjkblxxDTO> yhscjkblxxDTOList) {
//        final List<ZnsbNssbYhslfkblDO> kblDOList = new ArrayList<>();
//        for (YhscjkblxxDTO kblDTO : yhscjkblxxDTOList) {
//            final ZnsbNssbYhslfkblDO kblDO = BeanUtils.toBean(kblDTO, ZnsbNssbYhslfkblDO.class);
//            kblDO.setZbuuid(yhscjDO.getZbuuid());
//            kblDO.setSyuuid(yhscjDO.getUuid());
//            kblDO.setDfslrsjje(kblDTO.getDfslrsjje());
//            kblDO.setSjjsje(kblDTO.getSjjsje());
//            kblDO.setSjjsrq(GyUtils.cast2Date(kblDTO.getSjjsrq()));
//            kblDO.setZfbz1("N");
//            kblDO.setYwqdDm("SQGJ");
//            kblDO.setLrrq(new Date());
//            kblDO.setLrrsfid("SQGJ");
//            kblDO.setXgrq(new Date());
//            kblDO.setXgrsfid("SQGJ");
//            kblDO.setSjtbSj(new Date());
//            kblDOList.add(kblDO);
//        }
//        return kblDOList;
//    }
//
//    @Override
//    public HXZGSB10735Response getCxxx(CxsRequestDTO dto) {
//        HXZGSB10735Response res = new HXZGSB10735Response();
//        List<CxssbxxGridDTO> sbxxGrid = dto.getSbxxGrid();
//        List<String> syuuidList = new ArrayList<>();
//        for (CxssbxxGridDTO a : sbxxGrid) {
//            List<CxssymxDTO> symxGrid = a.getSymxGrid();
//            for (CxssymxDTO b : symxGrid) {
//                String syuuid = b.getSyuuid();
//                syuuidList.add(syuuid);
//            }
//        }
//        QueryWrapperX<ZnsbNssbYhscjDO> queryWrapperX = new QueryWrapperX<>();
//        queryWrapperX.lambda().in(ZnsbNssbYhscjDO::getHxbuuid, syuuidList)
//                .ne(ZnsbNssbYhscjDO::getZfbz1, "Y");
//        List<ZnsbNssbYhscjDO> yhshbcjxxbDTOList = znsbNssbYhscjMapper.selectList(queryWrapperX);
//        List<SbxxGridlbVO> sbxxList = new ArrayList<>();
//        HXZGSB10735Response.SbxxGrid sbxxgrid = new HXZGSB10735Response.SbxxGrid();
//
//        HXZGSB10735Response.JmxxGrid jmxz = new HXZGSB10735Response.JmxxGrid();
//        List<Bqjmsemxbywbw> jmxxGridlb = new ArrayList<>();
//        Bqjmsemxbywbw bqjmsemxbywbw = new Bqjmsemxbywbw();
//        List<BqjmsemxbGridlbVO> bqjmsemxbGridlb = new ArrayList<>();
//
//        for (ZnsbNssbYhscjDO yhscj : yhshbcjxxbDTOList) {
//            //申报
//            SbxxGridlbVO sbxxGridlbVO = BeanUtils.toBean(yhscj, SbxxGridlbVO.class);
//            sbxxGridlbVO.setZsxmDm("10111");
//            sbxxGridlbVO.setSyuuid(yhscj.getHxbuuid());
//            sbxxGridlbVO.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
//            sbxxGridlbVO.setSybh(yhscj.getBh());
//            sbxxGridlbVO.setSkssqq(DateUtil.doDateFormat(yhscj.getSkssqq(), "yyyy-MM-dd"));
//            sbxxGridlbVO.setSkssqz(DateUtil.doDateFormat(yhscj.getSkssqz(), "yyyy-MM-dd"));
//            sbxxList.add(sbxxGridlbVO);
//            //减免
//            Map<String, Object> zspmMap = CacheUtils.getTableData("dm_gy_zspm", yhscj.getZspmDm());
//            Map<String, Object> zszmMap = CacheUtils.getTableData("dm_gy_zszm", yhscj.getZszmDm());
//            BqjmsemxbGridlbVO bqjmsemxbGridlbVO = BeanUtils.toBean(yhscj, BqjmsemxbGridlbVO.class);
//            bqjmsemxbGridlbVO.setZsxmDm("10111");
//            if (GyUtils.isNotNull(zspmMap)) {
//                bqjmsemxbGridlbVO.setZspmmc(String.valueOf(zspmMap.get("zspmmc")));
//            }
//            if (GyUtils.isNotNull(zszmMap)) {
//                bqjmsemxbGridlbVO.setZszmmc(String.valueOf(zszmMap.get("zszmmc")));
//            }
//            bqjmsemxbGridlbVO.setSybh(yhscj.getHxbuuid());
//            bqjmsemxbGridlbVO.setSkssqq(DateUtil.doDateFormat(yhscj.getSkssqq(), "yyyy-MM-dd"));
//            bqjmsemxbGridlbVO.setSkssqz(DateUtil.doDateFormat(yhscj.getSkssqz(), "yyyy-MM-dd"));
//            bqjmsemxbGridlbVO.setSsjmxzDm(yhscj.getSsjmxzDm());
//            if (GyUtils.isNotNull(yhscj.getSsjmxzDm())) {
//                bqjmsemxbGridlbVO.setSsjmxzmc(getMcByDm(yhscj.getSsjmxzDm(), bqjmsemxbGridlbVO.getSkssqz()));
//            }
//            bqjmsemxbGridlb.add(bqjmsemxbGridlbVO);
//        }
//        sbxxgrid.getSbxxGridlb().addAll(sbxxList);
//
//        Bqjmsemxbywbw.Bqjmsemxb bqjmsemxb = new Bqjmsemxbywbw.Bqjmsemxb();
//        bqjmsemxb.getBqjmsemxbGridlb().addAll(bqjmsemxbGridlb);
//
//        bqjmsemxbywbw.setZsxmDm("10111");
//        bqjmsemxbywbw.setBqjmsemxb(bqjmsemxb);
//        jmxxGridlb.add(bqjmsemxbywbw);
//        jmxz.getJmxxGridlb().addAll(jmxxGridlb);
//
//        res.setSbxxGrid(sbxxgrid);
//        res.setJmxxGrid(jmxz);
//
//        return res;
//    }
//
//    /**
//     * 获取减免性质名称
//     *
//     * @param ssjmxzDm
//     * @return
//     */
//    public String getMcByDm(String ssjmxzDm, String skssqz) {
//        if (GyUtils.isNull(ssjmxzDm)) {
//            return "";
//        }
//        final List<CsZnsbYhSwsxjmxzdzbDO> ssjmxzList = CacheUtils.getTableData("cs_znsb_yh_swsxjmxzdzb_ssjmxz",
//                CsZnsbYhSwsxjmxzdzbDO.class,
//                m -> (m.getSsjmxzDm().equals(ssjmxzDm)));//取减免性质代码 税收事项代码
//        CsZnsbYhSwsxjmxzdzbDO ssjmxzdzbDO = new CsZnsbYhSwsxjmxzdzbDO();
//        if (GyUtils.isNotNull(ssjmxzList)) {
//            ssjmxzdzbDO = ssjmxzList.get(0);
//        } else {
//            return "";
//        }
//        final String swsxDmMc = GyUtils.isNull(ssjmxzdzbDO.getSwsxDm()) ? "" : GYSbUtils.getJmxmMcByJmxmDm(GyUtils.cast2Str(ssjmxzdzbDO.getSwsxDm())) + "│";//根据税务事项代码取税务事项名称
//        List<Map<String, Object>> ssjmxzmcdzList = new ArrayList<>();
//        Object ssjmxzmcdzObj = CacheUtils.getTableData("cs_znsb_yh_ssjmxzdmmcdz", ssjmxzDm);//获取最新的减免性质名称
//        if (ssjmxzmcdzObj instanceof List) {
//            ssjmxzmcdzList = (List<Map<String, Object>>) ssjmxzmcdzObj;
//        } else {
//
//        }
//        final String ssjmxzMc = GYSbUtils.getSsjmxzmc(GyUtils.cast2Date(skssqz), ssjmxzDm, ssjmxzmcdzList);
//        return swsxDmMc + ssjmxzMc;
//    }
//
//    public static void main(String[] args) {
//        LqBcYhsJgCxSbjgsjDTO lq = new LqBcYhsJgCxSbjgsjDTO();
//        List<LqBcYhsJgCxSyxxGridDTO> list = new ArrayList<>();
//        LqBcYhsJgCxSyxxGridDTO dto = new LqBcYhsJgCxSyxxGridDTO();
//
//        List<LqBcYhsJgCxSyxxGridlbDTO> listlbDo = new ArrayList<>();
//        LqBcYhsJgCxSyxxGridlbDTO lbDo = new LqBcYhsJgCxSyxxGridlbDTO();
//        lbDo.setMessage("123");
//        lbDo.setCode("00");
//        lbDo.setSyuuid("0987654321");
//        lbDo.setZbuuid("1234567890");
//        listlbDo.add(lbDo);
//        dto.setSyxxGridlb(listlbDo);
//        dto.setZsxmDm("10111");
//        dto.setSyczlx("04");
//        list.add(dto);
//        lq.setSyxxGrid(list);
//        String aa = Base64Utils.encode(JsonUtils.toJson(lq));
//        log.info("aaaaa--{}", aa);
//        LqBcYhsJgCxSbjgsjDTO assaa = JsonUtils.toBean(Base64Utils.decode(aa), LqBcYhsJgCxSbjgsjDTO.class);
//        log.info("BBBBBBb---{}", assaa);
//
//    }
//

}
