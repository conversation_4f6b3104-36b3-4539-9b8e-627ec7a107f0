package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 取得进项税额情况Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "qdjxseqkGridlbVO", propOrder = { "fzjgmc", "fpdmjhmhjkshm", "je", "jxse", "rzhjhyf", "bz" })
@Getter
@Setter
public class QdjxseqkGridlbVO {
    /**
     * 分支机构名称
     */
    protected String fzjgmc;

    /**
     * 发票代码及号码/缴款书号码
     */
    protected String fpdmjhmhjkshm;

    /**
     * 金额
     */
    protected BigDecimal je;

    /**
     * 进项税额
     */
    protected BigDecimal jxse;

    /**
     * 认证/稽核月份
     */
    protected String rzhjhyf;

    /**
     * 备注
     */
    protected String bz;
}