
package com.css.znsb.nssb.pojo.bo.hxzg.qysdscczsndsb.sb287;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 所得减免优惠明细表(A107020)表单
 * 
 * <p>A107020Ywbd complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="A107020Ywbd">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.chinatax.gov.cn/dataspec/}taxDoc">
 *       &lt;sequence>
 *         &lt;element name="sdjmyhmxbhjForm" type="{http://www.chinatax.gov.cn/dataspec/}sdjmyhmxbhjForm"/>
 *         &lt;element name="nlmfyxmGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="gjzdfzggjcssxmGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="hjbhjnjsxmGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="jszrxmGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="ssqjjzfzxmGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="nyglxmGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="qtGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="jcdlxm1Grid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="jcdlxm2Grid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="jcdlxmebGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="xjGrid" minOccurs="0">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "A107020Ywbd", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "sdjmyhmxbhjForm",
    "nlmfyxmGrid",
    "gjzdfzggjcssxmGrid",
    "hjbhjnjsxmGrid",
    "jszrxmGrid",
    "ssqjjzfzxmGrid",
    "nyglxmGrid",
    "qtGrid",
    "jcdlxm1Grid",
    "jcdlxm2Grid",
    "jcdlxmebGrid",
    "xjGrid"
})
public class A107020Ywbd
    extends TaxDoc implements Serializable 
{

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 8072910815403124489L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected SdjmyhmxbhjForm sdjmyhmxbhjForm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.NlmfyxmGrid nlmfyxmGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.GjzdfzggjcssxmGrid gjzdfzggjcssxmGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.HjbhjnjsxmGrid hjbhjnjsxmGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.JszrxmGrid jszrxmGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.SsqjjzfzxmGrid ssqjjzfzxmGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.NyglxmGrid nyglxmGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.QtGrid qtGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.Jcdlxm1Grid jcdlxm1Grid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.Jcdlxm2Grid jcdlxm2Grid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected A107020Ywbd.JcdlxmebGrid jcdlxmebGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected A107020Ywbd.XjGrid xjGrid;

    /**
     * 获取sdjmyhmxbhjForm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link SdjmyhmxbhjForm }
     *     
     */
    public SdjmyhmxbhjForm getSdjmyhmxbhjForm() {
        return sdjmyhmxbhjForm;
    }

    /**
     * 设置sdjmyhmxbhjForm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link SdjmyhmxbhjForm }
     *     
     */
    public void setSdjmyhmxbhjForm(SdjmyhmxbhjForm value) {
        this.sdjmyhmxbhjForm = value;
    }

    /**
     * 获取nlmfyxmGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.NlmfyxmGrid }
     *     
     */
    public A107020Ywbd.NlmfyxmGrid getNlmfyxmGrid() {
        return nlmfyxmGrid;
    }

    /**
     * 设置nlmfyxmGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.NlmfyxmGrid }
     *     
     */
    public void setNlmfyxmGrid(A107020Ywbd.NlmfyxmGrid value) {
        this.nlmfyxmGrid = value;
    }

    /**
     * 获取gjzdfzggjcssxmGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.GjzdfzggjcssxmGrid }
     *     
     */
    public A107020Ywbd.GjzdfzggjcssxmGrid getGjzdfzggjcssxmGrid() {
        return gjzdfzggjcssxmGrid;
    }

    /**
     * 设置gjzdfzggjcssxmGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.GjzdfzggjcssxmGrid }
     *     
     */
    public void setGjzdfzggjcssxmGrid(A107020Ywbd.GjzdfzggjcssxmGrid value) {
        this.gjzdfzggjcssxmGrid = value;
    }

    /**
     * 获取hjbhjnjsxmGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.HjbhjnjsxmGrid }
     *     
     */
    public A107020Ywbd.HjbhjnjsxmGrid getHjbhjnjsxmGrid() {
        return hjbhjnjsxmGrid;
    }

    /**
     * 设置hjbhjnjsxmGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.HjbhjnjsxmGrid }
     *     
     */
    public void setHjbhjnjsxmGrid(A107020Ywbd.HjbhjnjsxmGrid value) {
        this.hjbhjnjsxmGrid = value;
    }

    /**
     * 获取jszrxmGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.JszrxmGrid }
     *     
     */
    public A107020Ywbd.JszrxmGrid getJszrxmGrid() {
        return jszrxmGrid;
    }

    /**
     * 设置jszrxmGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.JszrxmGrid }
     *     
     */
    public void setJszrxmGrid(A107020Ywbd.JszrxmGrid value) {
        this.jszrxmGrid = value;
    }

    /**
     * 获取ssqjjzfzxmGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.SsqjjzfzxmGrid }
     *     
     */
    public A107020Ywbd.SsqjjzfzxmGrid getSsqjjzfzxmGrid() {
        return ssqjjzfzxmGrid;
    }

    /**
     * 设置ssqjjzfzxmGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.SsqjjzfzxmGrid }
     *     
     */
    public void setSsqjjzfzxmGrid(A107020Ywbd.SsqjjzfzxmGrid value) {
        this.ssqjjzfzxmGrid = value;
    }

    /**
     * 获取nyglxmGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.NyglxmGrid }
     *     
     */
    public A107020Ywbd.NyglxmGrid getNyglxmGrid() {
        return nyglxmGrid;
    }

    /**
     * 设置nyglxmGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.NyglxmGrid }
     *     
     */
    public void setNyglxmGrid(A107020Ywbd.NyglxmGrid value) {
        this.nyglxmGrid = value;
    }

    /**
     * 获取qtGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.QtGrid }
     *     
     */
    public A107020Ywbd.QtGrid getQtGrid() {
        return qtGrid;
    }

    /**
     * 设置qtGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.QtGrid }
     *     
     */
    public void setQtGrid(A107020Ywbd.QtGrid value) {
        this.qtGrid = value;
    }

    /**
     * 获取jcdlxm1Grid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.Jcdlxm1Grid }
     *     
     */
    public A107020Ywbd.Jcdlxm1Grid getJcdlxm1Grid() {
        return jcdlxm1Grid;
    }

    /**
     * 设置jcdlxm1Grid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.Jcdlxm1Grid }
     *     
     */
    public void setJcdlxm1Grid(A107020Ywbd.Jcdlxm1Grid value) {
        this.jcdlxm1Grid = value;
    }

    /**
     * 获取jcdlxm2Grid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.Jcdlxm2Grid }
     *     
     */
    public A107020Ywbd.Jcdlxm2Grid getJcdlxm2Grid() {
        return jcdlxm2Grid;
    }

    /**
     * 设置jcdlxm2Grid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.Jcdlxm2Grid }
     *     
     */
    public void setJcdlxm2Grid(A107020Ywbd.Jcdlxm2Grid value) {
        this.jcdlxm2Grid = value;
    }

    /**
     * 获取jcdlxmebGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.JcdlxmebGrid }
     *     
     */
    public A107020Ywbd.JcdlxmebGrid getJcdlxmebGrid() {
        return jcdlxmebGrid;
    }

    /**
     * 设置jcdlxmebGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.JcdlxmebGrid }
     *     
     */
    public void setJcdlxmebGrid(A107020Ywbd.JcdlxmebGrid value) {
        this.jcdlxmebGrid = value;
    }

    /**
     * 获取xjGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link A107020Ywbd.XjGrid }
     *     
     */
    public A107020Ywbd.XjGrid getXjGrid() {
        return xjGrid;
    }

    /**
     * 设置xjGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link A107020Ywbd.XjGrid }
     *     
     */
    public void setXjGrid(A107020Ywbd.XjGrid value) {
        this.xjGrid = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class GjzdfzggjcssxmGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = -1726758528840328603L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class HjbhjnjsxmGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = 3556268207483180905L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class Jcdlxm1Grid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = 6489850080219330948L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class Jcdlxm2Grid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = 2894370628969119479L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class JcdlxmebGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = 3177358581114461841L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class JszrxmGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = -1147095283677310737L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class NlmfyxmGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = 6928485389147197797L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class NyglxmGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = 1637370113628516714L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class QtGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = 4376372524833296946L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class SsqjjzfzxmGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = -3392660499441693121L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="xmGridlb" type="{http://www.chinatax.gov.cn/dataspec/}xmGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "xmGridlb"
    })
    public static class XjGrid implements Serializable {

        /**
         * @description 字段功能描述
         * @value value:serialVersionUID
         */
        private static final long serialVersionUID = -5996990185781665136L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<XmGridlbVO> xmGridlb;

        /**
         * Gets the value of the xmGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xmGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getXmGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XmGridlbVO }
         * 
         * 
         */
        public List<XmGridlbVO> getXmGridlb() {
            if (xmGridlb == null) {
                xmGridlb = new ArrayList<XmGridlbVO>();
            }
            return this.xmGridlb;
        }

    }

}
