package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 汇总纳税企业通用传递单
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hznsqytycddFormVO", propOrder = { "fddbrxm", "lxdh", "yydz", "khyh", "yhzh", "dhhm", "zgswjgshyj", "zgswry", "shswry", "bsdw" })
@Getter
@Setter
public class HznsqytycddFormVO {
    /**
     * 法定代表人姓名
     */
    @XmlElement(nillable = true, required = true)
    protected String fddbrxm;

    /**
     * 联系电话
     */
    protected String lxdh;

    /**
     * 营业地址
     */
    @XmlElement(nillable = true, required = true)
    protected String yydz;

    /**
     * 开户银行
     */
    protected String khyh;

    /**
     * 银行账户
     */
    protected String yhzh;

    /**
     * 电话号码
     */
    protected String dhhm;

    /**
     * 主管税务机关审核意见
     */
    protected String zgswjgshyj;

    /**
     * 主管税务人员
     */
    protected String zgswry;

    /**
     * 审核税务人员
     */
    protected String shswry;

    /**
     * 报送单位
     */
    protected String bsdw;
}