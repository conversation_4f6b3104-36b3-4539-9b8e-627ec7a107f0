
package com.css.znsb.nssb.pojo.bo.hxzg.rd.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 税（费）种认定信息表
 * 
 * <p>Java class for RDSfzrdxxbVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="RDSfzrdxxbVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="rdpzuuid" type="{http://www.chinatax.gov.cn/dataspec/}rdpzuuid"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="zfsbz" type="{http://www.chinatax.gov.cn/dataspec/}zfsbz"/>
 *         &lt;element name="rdzsuuid" type="{http://www.chinatax.gov.cn/dataspec/}rdzsuuid" minOccurs="0"/>
 *         &lt;element name="zsxmDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmDm"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm"/>
 *         &lt;element name="zszmDm" type="{http://www.chinatax.gov.cn/dataspec/}zszmDm" minOccurs="0"/>
 *         &lt;element name="rdyxqq" type="{http://www.chinatax.gov.cn/dataspec/}rdyxqq"/>
 *         &lt;element name="rdyxqz" type="{http://www.chinatax.gov.cn/dataspec/}rdyxqz"/>
 *         &lt;element name="hyDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm"/>
 *         &lt;element name="sbqxDm" type="{http://www.chinatax.gov.cn/dataspec/}sbqxDm"/>
 *         &lt;element name="nsqxDm" type="{http://www.chinatax.gov.cn/dataspec/}nsqxDm"/>
 *         &lt;element name="slhdwse" type="{http://www.chinatax.gov.cn/dataspec/}slhdwse"/>
 *         &lt;element name="yskmDm" type="{http://www.chinatax.gov.cn/dataspec/}yskmDm" minOccurs="0"/>
 *         &lt;element name="zsl" type="{http://www.chinatax.gov.cn/dataspec/}zsl"/>
 *         &lt;element name="ysfpblDm" type="{http://www.chinatax.gov.cn/dataspec/}ysfpblDm" minOccurs="0"/>
 *         &lt;element name="skgkDm" type="{http://www.chinatax.gov.cn/dataspec/}skgkDm" minOccurs="0"/>
 *         &lt;element name="jkqxDm" type="{http://www.chinatax.gov.cn/dataspec/}jkqxDm"/>
 *         &lt;element name="zsdlfsDm" type="{http://www.chinatax.gov.cn/dataspec/}zsdlfsDm" minOccurs="0"/>
 *         &lt;element name="zgswskfjDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswskfjDm"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq" minOccurs="0"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz"/>
 *         &lt;element name="zsxmcxbzDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmcxbzDm"/>
 *         &lt;element name="gfjbjgDm" type="{http://www.chinatax.gov.cn/dataspec/}gfjbjgDm"/>
 *         &lt;element name="jfjcDm" type="{http://www.chinatax.gov.cn/dataspec/}ysjcDm"/>
 *         &lt;element name="zszsxmDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmDm"/>
 *         &lt;element name="sfzyzspm" type="{http://www.chinatax.gov.cn/dataspec/}bz1"/>
 *         &lt;element name="gdszfsgluuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid"/>  
 *         &lt;element name="wzxgbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz"/>  
 *         &lt;element name="cpyspbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz"/> 
 *         &lt;element name="sfksqy" type="{http://www.chinatax.gov.cn/dataspec/}yxbz"/>   
 *         &lt;element name="swjgDm" type="{http://www.chinatax.gov.cn/dataspec/}swjgDm"/>    
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RDSfzrdxxbVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "rdpzuuid",
    "djxh",
    "zfsbz",
    "rdzsuuid",
    "zsxmDm",
    "zspmDm",
    "zszmDm",
    "rdyxqq",
    "rdyxqz",
    "hyDm",
    "sbqxDm",
    "nsqxDm",
    "slhdwse",
    "yskmDm",
    "zsl",
    "ysfpblDm",
    "skgkDm",
    "jkqxDm",
    "zsdlfsDm",
    "zgswskfjDm",
    "lrrDm",
    "lrrq",
    "xgrDm",
    "xgrq",
    "sjgsdq",
    "yxbz",
    "zsxmcxbzDm",
    "gfjbjgDm",
    "jfjcDm",
    "zszsxmDm",
    "sfzyzspm",
    "gdszfsgluuid",
    "wzxgbz",
    "cpyspbz",
    "sfksqy",
    "swjgDm"
})
public class RDSfzrdxxbVO
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String rdpzuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zfsbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String rdzsuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zszmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String rdyxqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String rdyxqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hyDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sbqxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String nsqxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double slhdwse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yskmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double zsl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ysfpblDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skgkDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String jkqxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsdlfsDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zgswskfjDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zsxmcxbzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String gfjbjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String jfjcDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zszsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sfzyzspm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String gdszfsgluuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String wzxgbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String cpyspbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfksqy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String swjgDm;

    /**
     * Gets the value of the rdpzuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdpzuuid() {
        return rdpzuuid;
    }

    /**
     * Sets the value of the rdpzuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdpzuuid(String value) {
        this.rdpzuuid = value;
    }

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the zfsbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfsbz() {
        return zfsbz;
    }

    /**
     * Sets the value of the zfsbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfsbz(String value) {
        this.zfsbz = value;
    }

    /**
     * Gets the value of the rdzsuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdzsuuid() {
        return rdzsuuid;
    }

    /**
     * Sets the value of the rdzsuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdzsuuid(String value) {
        this.rdzsuuid = value;
    }

    /**
     * Gets the value of the zsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmDm() {
        return zsxmDm;
    }

    /**
     * Sets the value of the zsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmDm(String value) {
        this.zsxmDm = value;
    }

    /**
     * Gets the value of the zspmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * Sets the value of the zspmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * Gets the value of the zszmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZszmDm() {
        return zszmDm;
    }

    /**
     * Sets the value of the zszmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZszmDm(String value) {
        this.zszmDm = value;
    }

    /**
     * Gets the value of the rdyxqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdyxqq() {
        return rdyxqq;
    }

    /**
     * Sets the value of the rdyxqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdyxqq(String value) {
        this.rdyxqq = value;
    }

    /**
     * Gets the value of the rdyxqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdyxqz() {
        return rdyxqz;
    }

    /**
     * Sets the value of the rdyxqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdyxqz(String value) {
        this.rdyxqz = value;
    }

    /**
     * Gets the value of the hyDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHyDm() {
        return hyDm;
    }

    /**
     * Sets the value of the hyDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHyDm(String value) {
        this.hyDm = value;
    }

    /**
     * Gets the value of the sbqxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbqxDm() {
        return sbqxDm;
    }

    /**
     * Sets the value of the sbqxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbqxDm(String value) {
        this.sbqxDm = value;
    }

    /**
     * Gets the value of the nsqxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsqxDm() {
        return nsqxDm;
    }

    /**
     * Sets the value of the nsqxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsqxDm(String value) {
        this.nsqxDm = value;
    }

    /**
     * Gets the value of the slhdwse property.
     * 
     */
    public double getSlhdwse() {
        return slhdwse;
    }

    /**
     * Sets the value of the slhdwse property.
     * 
     */
    public void setSlhdwse(double value) {
        this.slhdwse = value;
    }

    /**
     * Gets the value of the yskmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYskmDm() {
        return yskmDm;
    }

    /**
     * Sets the value of the yskmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYskmDm(String value) {
        this.yskmDm = value;
    }

    /**
     * Gets the value of the zsl property.
     * 
     */
    public double getZsl() {
        return zsl;
    }

    /**
     * Sets the value of the zsl property.
     * 
     */
    public void setZsl(double value) {
        this.zsl = value;
    }

    /**
     * Gets the value of the ysfpblDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYsfpblDm() {
        return ysfpblDm;
    }

    /**
     * Sets the value of the ysfpblDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYsfpblDm(String value) {
        this.ysfpblDm = value;
    }

    /**
     * Gets the value of the skgkDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkgkDm() {
        return skgkDm;
    }

    /**
     * Sets the value of the skgkDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkgkDm(String value) {
        this.skgkDm = value;
    }

    /**
     * Gets the value of the jkqxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJkqxDm() {
        return jkqxDm;
    }

    /**
     * Sets the value of the jkqxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJkqxDm(String value) {
        this.jkqxDm = value;
    }

    /**
     * Gets the value of the zsdlfsDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsdlfsDm() {
        return zsdlfsDm;
    }

    /**
     * Sets the value of the zsdlfsDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsdlfsDm(String value) {
        this.zsdlfsDm = value;
    }

    /**
     * Gets the value of the zgswskfjDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    /**
     * Sets the value of the zgswskfjDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswskfjDm(String value) {
        this.zgswskfjDm = value;
    }

    /**
     * Gets the value of the lrrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * Sets the value of the lrrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * Gets the value of the lrrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * Sets the value of the lrrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * Gets the value of the xgrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * Sets the value of the xgrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * Gets the value of the xgrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * Sets the value of the xgrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * Gets the value of the sjgsdq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * Sets the value of the sjgsdq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * Gets the value of the yxbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxbz() {
        return yxbz;
    }

    /**
     * Sets the value of the yxbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxbz(String value) {
        this.yxbz = value;
    }

    /**
     * Gets the value of the zsxmcxbzDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmcxbzDm() {
        return zsxmcxbzDm;
    }

    /**
     * Sets the value of the zsxmcxbzDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmcxbzDm(String value) {
        this.zsxmcxbzDm = value;
    }

    /**
     * Gets the value of the gfjbjgDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGfjbjgDm() {
        return gfjbjgDm;
    }

    /**
     * Sets the value of the gfjbjgDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGfjbjgDm(String value) {
        this.gfjbjgDm = value;
    }

    /**
     * Gets the value of the jfjcDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJfjcDm() {
        return jfjcDm;
    }

    /**
     * Sets the value of the jfjcDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJfjcDm(String value) {
        this.jfjcDm = value;
    }

    /**
     * Gets the value of the zszsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZszsxmDm() {
        return zszsxmDm;
    }

    /**
     * Sets the value of the zszsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZszsxmDm(String value) {
        this.zszsxmDm = value;
    }

    /**
     * Gets the value of the sfzyzspm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfzyzspm() {
        return sfzyzspm;
    }

    /**
     * Sets the value of the sfzyzspm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfzyzspm(String value) {
        this.sfzyzspm = value;
    }
    /**
     * Gets the value of the gdszfsgluuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdszfsgluuid() {
        return gdszfsgluuid;
    }
    /**
     * Sets the value of the gszsrdpzuuid property.
     * 
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdszfsgluuid(String gdszfsgluuid) {
        this.gdszfsgluuid = gdszfsgluuid;
    }
    /**
     * Gets the value of the wzxgbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWzxgbz() {
        return wzxgbz;
    }
    /**
     * Sets the value of the wzxgbz property.
     * 
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWzxgbz(String wzxgbz) {
        this.wzxgbz = wzxgbz;
    }
    /**
     * Gets the value of the cpyspbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpyspbz() {
        return cpyspbz;
    }
    /**
     * Sets the value of the cpyspbz property.
     * 
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpyspbz(String cpyspbz) {
        this.cpyspbz = cpyspbz;
    }
    /**
     * Gets the value of the sfksqy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfksqy() {
        return sfksqy;
    }
    /**
     * Sets the value of the sfksqy property.
     * 
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfksqy(String sfksqy) {
        this.sfksqy = sfksqy;
    }
    /**
     * Gets the value of the swjgDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSwjgDm() {
        return swjgDm;
    }

    /**
     * Sets the value of the swjgDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSwjgDm(String value) {
        this.swjgDm = value;
    }

}
