package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《本期抵扣进项税额结构明细表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_bqdkjxsejgmxbywbw", propOrder = { "zzssyyybnsrBqdkjxsejgmxb" })
@Getter
@Setter
public class ZzssyyybnsrBqdkjxsejgmxbywbw extends TaxDoc {
    /**
     * 《本期抵扣进项税额结构明细表》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_bqdkjxsejgmxb", required = true)
    @JSONField(name = "zzssyyybnsr_bqdkjxsejgmxb")
    protected ZzssyyybnsrBqdkjxsejgmxb zzssyyybnsrBqdkjxsejgmxb;
}