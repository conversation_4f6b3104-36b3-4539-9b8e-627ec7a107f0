package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;

import com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc.general.DoublePrecisionCommon2;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;

/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.sbzs.sb.general
 * @file SBSaveReturnVO.java 创建时间:2014-7-21下午11:54:05
 * @title 申报保存返回数据信息
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 核心征管-申报
 * <AUTHOR> 刘巍
 * @reviewer 审核人 邓文辉
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBSaveReturnVO", propOrder = {
        "pzxh",
        "ybtse",
        "returnBz",
        "iszsskBz",
        "ybse",
        "ytse",
        "sbuuid",
        "mesg",
        "swbfmesg",
        "sftsswbf",
        "zlxqgzxxbuuid",
        "sswfxwdjuuid"
})
public class SBSaveReturnVO implements Serializable{

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 7408603284849939349L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pzxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecisionCommon2.class)
    protected Double ybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String returnBz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String iszsskBz;//新增加字段用来判断错误更正产生1元以下是否提示征收开票。
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecisionCommon2.class)
    protected Double ybse;//增加应补税额
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecisionCommon2.class)
    protected Double ytse;//增加应退税额
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbuuid;//增加sbuuid
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String mesg;//返回消息
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String swbfmesg;//首违不罚提示信息
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sftsswbf;//是否提示跳转首违不罚
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zlxqgzxxbuuid;//跳转参数
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sswfxwdjuuid;//跳转参数

    @XmlTransient
    private String wflcslid; //首违不罚跳转lcslid

    public String getWflcslid() {
        return wflcslid;
    }

    public void setWflcslid(String wflcslid) {
        this.wflcslid = wflcslid;
    }

    public String getSwbfmesg() {
        return swbfmesg;
    }

    public void setSwbfmesg(String swbfmesg) {
        this.swbfmesg = swbfmesg;
    }

    public String getSftsswbf() {
        return sftsswbf;
    }

    public void setSftsswbf(String sftsswbf) {
        this.sftsswbf = sftsswbf;
    }

    public String getZlxqgzxxbuuid() {
        return zlxqgzxxbuuid;
    }

    public void setZlxqgzxxbuuid(String zlxqgzxxbuuid) {
        this.zlxqgzxxbuuid = zlxqgzxxbuuid;
    }

    public String getSswfxwdjuuid() {
        return sswfxwdjuuid;
    }

    public void setSswfxwdjuuid(String sswfxwdjuuid) {
        this.sswfxwdjuuid = sswfxwdjuuid;
    }

    public String getMesg() {
        return mesg;
    }

    public void setMesg(String mesg) {
        this.mesg = mesg;
    }
    /**
     * Gets the value of the pzxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPzxh() {
        return pzxh;
    }

    /**
     * Sets the value of the pzxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPzxh(String value) {
        this.pzxh = value;
    }

    /**
     * Gets the value of the ybtse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYbtse() {
        return ybtse;
    }

    /**
     * Sets the value of the ybtse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYbtse(Double value) {
        this.ybtse = value;
    }

    /**
     * Gets the value of the returnBz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReturnBz() {
        return returnBz;
    }

    /**
     * Sets the value of the returnBz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReturnBz(String value) {
        this.returnBz = value;
    }
    /**
     * Gets the value of the iszsskBz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIszsskBz() {
        return iszsskBz;
    }

    /**
     * Sets the value of the iszsskBz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIszsskBz(String value) {
        this.iszsskBz = value;
    }
    
    /**
     * Gets the value of the ybse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYbse() {
        return ybse;
    }

    /**
     * Sets the value of the ybse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYbse(Double value) {
        this.ybse = value;
    }
    
    /**
     * Gets the value of the ytse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYtse() {
        return ytse;
    }

    /**
     * Sets the value of the ytse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYtse(Double value) {
        this.ytse = value; 
    }
    

    /**
     * Gets the value of the sbuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbuuid() {
        return sbuuid;
    }

    /**
     * Sets the value of the sbuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbuuid(String value) {
        this.sbuuid = value;
    }

}
