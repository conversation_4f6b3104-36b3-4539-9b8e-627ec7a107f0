package com.css.znsb.nssb.pojo.dto.zzs.ybnsr.qcxx.bqxx;

import com.alibaba.fastjson.annotation.JSONField;
import com.css.znsb.nssb.pojo.bo.qcxx.common.fzxx.SbQcBqxxVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class YbnsrBqxxDTO extends SbQcBqxxVO implements Serializable {
    private final static long serialVersionUID = -1L;

    /**
     * 适用登记失业限额减免企业
     */
    
    @JSONField(name = "sydjsyxejmqy")
    private String sydjsyxejmqy;
    /**
     * 提供13%税率的服务、不动产和无形资产的企业
     */
    
    @JSONField(name = "xs13fwqy")
    private String xs13fwqy;
    /**
     * 住房租赁企业
     */
    
    @JSONField(name = "zfzlqy")
    private String zfzlqy;
    /**
     * 非金银首饰消费税的企业
     */
    
    @JSONField(name = "fjyssxfsqy")
    private String fjyssxfsqy;
    /**
     * 提供6%税率服务的企业
     */
    
    @JSONField(name = "xs6fwqy")
    private String xs6fwqy;
    /**
     * 差额扣除广告业文化事业建设费企业
     */
    
    @JSONField(name = "cekcggywhsyjsfqy")
    private String cekcggywhsyjsfqy;
    /**
     * 卷烟生产企业
     */
    
    @JSONField(name = "jyscqy")
    private String jyscqy;
    /**
     * 销售高档手表、贵重首饰及珠宝玉石的消费税企业（核定了高档手表、贵重首饰、及珠宝玉石消费税品目的纳税人）（身份标签）
     */
    
    @JSONField(name = "xsgdsbgzsszbysxfsqy")
    private String xsgdsbgzsszbysxfsqy;
    /**
     * 存在非正常损失的进项税额转出额的企业
     */
    
    @JSONField(name = "czfzcssjxsezceqy")
    private String czfzcssjxsezceqy;
    /**
     * 增值税跨地区税收分享税款企业
     */
    
    @JSONField(name = "zzskdqssfxskqy")
    private String zzskdqssfxskqy;
    /**
     * 存在税务检查未办结的企业
     */
    
    @JSONField(name = "czswjcwbjqy")
    private String czswjcwbjqy;
    /**
     * 个体工商户（登记注册类型为个体工商户的纳税人）（身份标签）
     */
    
    @JSONField(name = "gtgsh")
    private String gtgsh;
    /**
     * 存在农产品抵扣的企业
     */
    
    @JSONField(name = "czncpdkqy")
    private String czncpdkqy;
    /**
     * 成品油消费税企业
     */
    
    @JSONField(name = "cpyxfsqy")
    private String cpyxfsqy;
    /**
     * 预缴增值税的房地产开发企业
     */
    
    @JSONField(name = "yjzzsfdckfqy")
    private String yjzzsfdckfqy;

    /**
     * 增值税即征即退企业（存在有效即征即退备案的纳税人）（身份标签）
     */
    
    @JSONField(name = "zzsjzjtqy")
    private String zzsjzjtqy;
    /**
     * 铁路运输类增值税汇总纳税分支机构
     */
    
    @JSONField(name = "tlyszzshznsfzjg")
    private String tlyszzshznsfzjg;

    /**
     * 辅导期企业（存在有效的辅导期增值税一般纳税人资格的纳税人）（身份标签）
     */
    
    @JSONField(name = "fdqqy")
    private String fdqqy;
    /**
     * 提供5%税率的服务、不动产和无形资产的企业
     */
    
    @JSONField(name = "xs5fwqy")
    private String xs5fwqy;
    /**
     * 电信类增值税汇总纳税分支机构
     */
    
    @JSONField(name = "dxzzshznsfzjg")
    private String dxzzshznsfzjg;
    /**
     * 增值税应税服务企业（只认定了应税服务类增值税品目的纳税人）（身份标签）
     */
    
    @JSONField(name = "zzsysfwqy")
    private String zzsysfwqy;
    /**
     * 增值税预征率预征分支机构
     */
    
    @JSONField(name = "zzsyzfzjg")
    private String zzsyzfzjg;
    /**
     * 成品油经销企业
     */
    
    @JSONField(name = "cpyjxqy")
    private String cpyjxqy;
    /**
     * 成品油生产企业
     */
    
    @JSONField(name = "cpyscqy")
    private String cpyscqy;
    /**
     * 存在跨区不动产的企业
     */
    
    @JSONField(name = "czkqbdcqy")
    private String czkqbdcqy;
    /**
     * 出租住房减征增值税个体工商户
     */
    
    @JSONField(name = "czzfjzzzsgtgsh")
    private String czzfjzzzsgtgsh;
    /**
     * 本期缴税增值税的当年新办企业
     */
    
    @JSONField(name = "bqjnzzsdndnxbqy")
    private String bqjnzzsdndnxbqy;
    /**
     * 存在未开具发票销售收入的企业
     */
    
    @JSONField(name = "czwkjfpxssrqy")
    private String czwkjfpxssrqy;
    /**
     * 航空运输类增值税汇总纳税分支机构
     */
    
    @JSONField(name = "hkyszzshznsfzjg")
    private String hkyszzshznsfzjg;
    /**
     * 增值税分摊缴纳总机构
     */
    
    @JSONField(name = "zzsftjnzjg")
    private String zzsftjnzjg;
    /**
     * 提供9%税率的服务、不动产和无形资产的企业
     */
    
    @JSONField(name = "xs9fwqy")
    private String xs9fwqy;
    /**
     * 无增值税税费（种）登记的企业（上月末不存在有效增值税税费（种）登记的纳税人）（身份标签）
     */
    
    @JSONField(name = "wzzssfzdjqy")
    private String wzzssfzdjqy;
    /**
     * 存在代扣代缴税收缴款凭证抵扣的企业
     */
    
    @JSONField(name = "czdkdjssjkpzdkqy")
    private String czdkdjssjkpzdkqy;
    /**
     * 存在进项税额转出额的企业
     */
    
    @JSONField(name = "czjxsezceqy")
    private String czjxsezceqy;
    /**
     * 存在免税项目用的进项税额转出额的企业
     */
    
    @JSONField(name = "czmsxmyjxsezceqy")
    private String czmsxmyjxsezceqy;
    /**
     * 增值税分摊缴纳分支机构
     */
    
    @JSONField(name = "zzsftjnfzjg")
    private String zzsftjnfzjg;
    /**
     * 发（供）电类增值税汇总纳税总机构
     */
    
    @JSONField(name = "fgdzzshznszjg")
    private String fgdzzshznszjg;
    /**
     * 出租住房减征增值税住房租赁企业
     */
    
    @JSONField(name = "czzfjzzzszfzlqy")
    private String czzfjzzzszfzlqy;
    /**
     * 千户集团企业
     */
    
    @JSONField(name = "qhjtqy")
    private String qhjtqy;
    /**
     * 准予扣除的消费税企业（核定除卷烟批发、雪茄烟、烟丝、金银首饰、铂金首饰和钻石及钻石饰品、航空煤油、超豪华小汽车外其他消费税品目的纳税人）（身份标签）
     */
    
    @JSONField(name = "zykcxfsqy")
    private String zykcxfsqy;
    /**
     * 批发零售金银首饰的消费税企业
     */
    
    @JSONField(name = "pflsjyssxxsqy")
    private String pflsjyssxxsqy;
    /**
     * 增值税汇总纳税分支机构
     */
    
    @JSONField(name = "zzshznsfzjg")
    private String zzshznsfzjg;
    /**
     * 销售5%税率的货物及提供加工修理修配劳务的企业
     */
    
    @JSONField(name = "xs5hwjlwqy")
    private String xs5hwjlwqy;
    /**
     * 销售3%税率的货物及提供加工修理修配劳务的企业
     */
    
    @JSONField(name = "xs3hwjlwqy")
    private String xs3hwjlwqy;
    /**
     * 存在集体福利、个人消费的进项税额转出额的企业
     */
    
    @JSONField(name = "czjtflgrxsjxsezceqy")
    private String czjtflgrxsjxsezceqy;
    /**
     * 销售9%税率的货物及提供加工修理修配劳务的企业
     */
    
    @JSONField(name = "xs9hwjlwqy")
    private String xs9hwjlwqy;
    /**
     * 存在增值税其他扣税凭证抵扣的企业
     */
    
    @JSONField(name = "czzzsqtkspzdkqy")
    private String czzzsqtkspzdkqy;
    /**
     * 存在其他应作进项税额转出的情形的进项税额转出额的企业
     */
    
    @JSONField(name = "czqtyzjxsezceqy")
    private String czqtyzjxsezceqy;
    /**
     * 增值税混营企业（既认定了应税服务类增值税品目，又认定了货物劳务类增值税品目的纳税人）（身份标签）
     */
    
    @JSONField(name = "zzshyqy")
    private String zzshyqy;
    /**
     * 非差额扣除广告业文化事业建设费企业
     */
    
    @JSONField(name = "fcekcggywhsyjsfqy")
    private String fcekcggywhsyjsfqy;
    /**
     * 铁路运输类增值税汇总纳税总机构
     */
    
    @JSONField(name = "tlyszzshznszjg")
    private String tlyszzshznszjg;
    /**
     * 增值税多次切换填表式的纳税人（连续3期或3期以上推荐为确认式或补录式，并最终以填表式进行申报的企业，其中期数可配置）（操作行为标签）
     */
    
    @JSONField(name = "zzsdcqhtbsnsr")
    private String zzsdcqhtbsnsr;
    /**
     * 销售旧货减征增值税的企业
     */
    
    @JSONField(name = "xsjhjzzzsqy")
    private String xsjhjzzzsqy;
    /**
     * 适用退役士兵限额减免企业
     */
    
    @JSONField(name = "sytysbxejmqy")
    private String sytysbxejmqy;
    /**
     * 逾期未申请登记一般纳税人资格的企业
     */
    
    @JSONField(name = "yqwsqdjybnsrzgqy")
    private String yqwsqdjybnsrzgqy;
    /**
     * 国家限制和禁止行业企业（属于国家限制和禁止行业的纳税人）（身份标签）（需求中已不再用到此标签）
     */
    
    @JSONField(name = "gjxzhjzhyqy")
    private String gjxzhjzhyqy;
    /**
     * 试点建设培育产教融合型企业
     */
    
    @JSONField(name = "sdjspycjrhxqy")
    private String sdjspycjrhxqy;
    /**
     * 增值税汇总纳税总机构
     */
    
    @JSONField(name = "zzshznszjg")
    private String zzshznszjg;
    /**
     * 卷烟批发企业
     */
    
    @JSONField(name = "jypfqy")
    private String jypfqy;
    /**
     * 电信类增值税汇总纳税总机构
     */
    
    @JSONField(name = "dxzzshznszjg")
    private String dxzzshznszjg;
    /**
     * 有海关进口增值税专用缴款书抵扣的企业
     */
    
    @JSONField(name = "czhgjkzzszyjksdkqy")
    private String czhgjkzzszyjksdkqy;
    /**
     * 发（供）电类增值税汇总纳税分支机构
     */
    
    @JSONField(name = "fgdzzshznsfzjg")
    private String fgdzzshznsfzjg;
    /**
     * 全电发票用票企业
     */
    
    @JSONField(name = "qdfpypqy")
    private String qdfpypqy;
    /**
     * 航空运输类增值税汇总纳税总机构
     */
    
    @JSONField(name = "hkyszzshznszjg")
    private String hkyszzshznszjg;
    /**
     * 适用贫困人口限额减免企业
     */
    
    @JSONField(name = "sypkrkxejmqy")
    private String sypkrkxejmqy;
    /**
     * 二手车经销企业
     */
    
    @JSONField(name = "escjxqy")
    private String escjxqy;
    /**
     * 增值税加计抵减企业
     */
    
    @JSONField(name = "zzsjjdjqy")
    private String zzsjjdjqy;
    /**
     * 增值税不就地预缴总机构
     */
    
    @JSONField(name = "zzsbjdyjzjg")
    private String zzsbjdyjzjg;
    /**
     * 增值税差额扣除企业
     */
    
    @JSONField(name = "zzscekcqy")
    private String zzscekcqy;
    /**
     * 小微企业
     */
    
    @JSONField(name = "xwqy")
    private String xwqy;
    /**
     * 存在税控维护费抵扣企业
     */
    
    @JSONField(name = "czskwhfdjqy")
    private String czskwhfdjqy;
    /**
     * 准予扣除委托加工已纳税款的消费税企业（核定了除卷烟批发、雪茄烟、烟丝、金银首饰、铂金首饰和钻石及钻石饰品、航空煤油、超豪华小汽车外其他消费税品目的纳税人）（身份标签）
     */
    
    @JSONField(name = "zykcwtjgynskxfsqy")
    private String zykcwtjgynskxfsqy;
    /**
     * 消费税跨地区税收分享税款企业
     */
    
    @JSONField(name = "xfskdqssfxskqy")
    private String xfskdqssfxskqy;
    /**
     * 邮政类增值税汇总纳税分支机构
     */
    
    @JSONField(name = "yzzzshznsfzjg")
    private String yzzzshznsfzjg;
    /**
     * 增值税货物劳务企业（只认定了货物劳务类增值税品目的纳税人）（身份标签）
     */
    
    @JSONField(name = "zzshwlwqy")
    private String zzshwlwqy;
    /**
     * 消费税分摊缴纳总机构企业
     */
    
    @JSONField(name = "xsfftjnzjgqy")
    private String xsfftjnzjgqy;
    /**
     * 农产品核定扣除试点企业
     */
    
    @JSONField(name = "ncphedkcsdqy")
    private String ncphedkcsdqy;
    /**
     * 出口生产企业
     */
    
    @JSONField(name = "ckscqy")
    private String ckscqy;
    /**
     * 出口外贸企业
     */
    
    @JSONField(name = "ckwmqy")
    private String ckwmqy;
    /**
     * 娱乐业文化事业建设费企业
     */
    
    @JSONField(name = "ylywhsyjsfqy")
    private String ylywhsyjsfqy;
    /**
     * 增值税免税企业
     */
    
    @JSONField(name = "zzsmsqy")
    private String zzsmsqy;
    /**
     * 消费税减免企业（核定了成品油、涂料、电池消费税品目的纳税人）（身份标签）
     */
    
    @JSONField(name = "xfsjmqy")
    private String xfsjmqy;
    /**
     * 存在简易计税方法征收项目用的进项税额转出额的企业
     */
    
    @JSONField(name = "czjyjsffzsxmjxsezceqy")
    private String czjyjsffzsxmjxsezceqy;
    /**
     * 存在异常凭证转出进项税额的进项税额转出额的企业
     */
    
    @JSONField(name = "czycpzjxsezceqy")
    private String czycpzjxsezceqy;
    /**
     * 从事跨区建筑服务企业
     */
    
    @JSONField(name = "cskqjzfwqy")
    private String cskqjzfwqy;
    /**
     * 存在加计扣除农产品进项税额企业
     */
    
    @JSONField(name = "czjjkcncpjxseqy")
    private String czjjkcncpjxseqy;
    /**
     * 提供3%税率的服务、不动产和无形资产的企业
     */
    
    @JSONField(name = "xs3fwqy")
    private String xs3fwqy;
    /**
     * 当年新办企业（登记日期为本年度的纳税人）（身份标签）
     */
    
    @JSONField(name = "dnxbqy")
    private String dnxbqy;
    /**
     * 存在其他抵扣的企业
     */
    
    @JSONField(name = "czqtdkqy")
    private String czqtdkqy;
    /**
     * 销售13%税率的货物及提供加工修理修配劳务的企业
     */
    
    @JSONField(name = "xs13hwjlwqy")
    private String xs13hwjlwqy;
    /**
     * 存在购建不动产扣税凭证的企业
     */
    
    @JSONField(name = "czgzbdckspzqy")
    private String czgzbdckspzqy;
    /**
     * 邮政类增值税汇总纳税总机构
     */
    
    @JSONField(name = "yzzzshznszjg")
    private String yzzzshznszjg;

    /**是否本年度第一次申报表标志*/
    
    @JSONField(name = "sfbnddycsb")
    private String sfbnddycsb;
    /**
     * 跨省合资铁路标志
     */
    
    @JSONField(name = "kshztlbz")
    private String kshztlbz;
    /**
     * 本期完成抄报税的企业
     */
    
    @JSONField(name = "bqwccbsdQy")
    private String bqwccbsdQy;
    /**
     * 集成电路加计抵减企业
     */
    
    @JSONField(name = "jcdljjdjqy")
    private String jcdljjdjqy;
    /**
     * 工业母机加计抵减企业
     */
    
    @JSONField(name = "gymjjjdjqy")
    private String gymjjjdjqy;
    /**
     * 先进制造业加计抵减企业
     */
    
    @JSONField(name = "xjzzyjjdjqy")
    private String xjzzyjjdjqy;

    /**
     * 铁路运输企业（认定了101016105税费种的企业）
     */
    
    @JSONField(name = "tlysqy")
    private String tlysqy;
}
