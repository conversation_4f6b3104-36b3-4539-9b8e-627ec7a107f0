
package com.css.znsb.nssb.pojo.bo.hxzg.sb428;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 弥补亏损台账业务表单GridVO
 * 
 * <p>SBMbkstzjstzbGridVO complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SBMbkstzjstzbGridVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="mbkstzuuid" type="{http://www.chinatax.gov.cn/dataspec/}mbkstzuuid"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="pzxh" type="{http://www.chinatax.gov.cn/dataspec/}pzxh"/>
 *         &lt;element name="xh" type="{http://www.chinatax.gov.cn/dataspec/}xh"/>
 *         &lt;element name="xmmc" type="{http://www.chinatax.gov.cn/dataspec/}xmmc"/>
 *         &lt;element name="nd" type="{http://www.chinatax.gov.cn/dataspec/}nd"/>
 *         &lt;element name="zcxbqnd" type="{http://www.chinatax.gov.cn/dataspec/}zcxbqnd"/>
 *         &lt;element name="kshylje" type="{http://www.chinatax.gov.cn/dataspec/}kshylje"/>
 *         &lt;element name="hbflqyzrkmbkse" type="{http://www.chinatax.gov.cn/dataspec/}hbflqyzrkmbkse"/>
 *         &lt;element name="yqndhbflzrzclj" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="kmbdsde" type="{http://www.chinatax.gov.cn/dataspec/}kmbdsde"/>
 *         &lt;element name="kmbdsdeSn" type="{http://www.chinatax.gov.cn/dataspec/}kmbdsde"/>
 *         &lt;element name="yqndymbksehj" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yqndymbksehjSn" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yqndjzdkmbkye" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yqndjzdkmbkyeSn" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="flzckse" type="{http://www.chinatax.gov.cn/dataspec/}flzckse"/>
 *         &lt;element name="hbqyzrkse" type="{http://www.chinatax.gov.cn/dataspec/}hbqyzrkse"/>
 *         &lt;element name="hbqyzrkseBn" type="{http://www.chinatax.gov.cn/dataspec/}hbqyzrkseBn"/>
 *         &lt;element name="hbqyzrkseSn" type="{http://www.chinatax.gov.cn/dataspec/}hbqyzrkseSn"/>
 *         &lt;element name="qysdsmbksqylxDm" type="{http://www.chinatax.gov.cn/dataspec/}qysdsmbksqylxDm"/>
 *         &lt;element name="dndmbdkse" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="dndmbdkseZSn" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="dndmbdkseSn" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bnjnmbyjndksje" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bnjnmbyjndksjeSn" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bnjwmbyjndksje" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bnjwmbyjndksjeSn" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="kjzwmbwdksje" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="kjzwmbwdksjeSn" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="ybndjnsdembdyqndkse" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="ybndjwsdembdyqndkse" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yymbjnksze" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="djbnjnks" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="mbyqndksdje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="kjzyhndmbdksehj" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="tzyy" type="{http://www.chinatax.gov.cn/dataspec/}tzyy" minOccurs="0"/>
 *         &lt;element name="dysbnddmbnd" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="dysbnddmbjehj" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bqbz" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="kmbdsdeBn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndymbksehjBn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndjzdkmbkyeBn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hbqyzrkseBn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="dndmbdkseBn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="dndmbdkseBzSn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnjnmbyjndksjeBn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnjwmbyjndksjeBn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="kjzwmbwdksjeBn" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBMbkstzjstzbGridVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "mbkstzuuid",
    "djxh",
    "pzxh",
    "xh",
    "xmmc",
    "nd",
    "zcxbqnd",
    "kshylje",
    "hbflqyzrkmbkse",
    "yqndhbflzrzclj",
    "kmbdsde",
    "kmbdsdeBn",
    "kmbdsdeSn",
    "yqndymbksehj",
    "yqndymbksehjBn",
    "yqndymbksehjSn",
    "yqndjzdkmbkye",
    "yqndjzdkmbkyeBn",
    "yqndjzdkmbkyeSn",
    "flzckse",
    "hbqyzrkse",
    "hbqyzrkseBn",
    "hbqyzrkseSn",
    "qysdsmbksqylxDm",
    "dndmbdkse",
    "dndmbdkseBn",
    "dndmbdkseZSn",
    "dndmbdkseBzSn",
    "dndmbdkseSn",
    "bnjnmbyjndksje",
    "bnjnmbyjndksjeBn",
    "bnjnmbyjndksjeSn",
    "bnjwmbyjndksje",
    "bnjwmbyjndksjeBn",
    "bnjwmbyjndksjeSn",
    "kjzwmbwdksje",
    "kjzwmbwdksjeBn",
    "kjzwmbwdksjeSn",
    "ybndjnsdembdyqndkse",
    "ybndjwsdembdyqndkse",
    "yymbjnksze",
    "djbnjnks",
    "mbyqndksdje",
    "kjzyhndmbdksehj",
    "tzyy",
    "dysbnddmbnd",
    "dysbnddmbjehj",
    "bqbz"
})
public class SBMbkstzjstzbGridVO implements Serializable {

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -4776368593438798760L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String mbkstzuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String pzxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xmmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String nd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zcxbqnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String kshylje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbflqyzrkmbkse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqndhbflzrzclj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String kmbdsde;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kmbdsdeBn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String kmbdsdeSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqndymbksehj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqndymbksehjBn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqndymbksehjSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqndjzdkmbkye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqndjzdkmbkyeBn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqndjzdkmbkyeSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String flzckse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbqyzrkse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbqyzrkseBn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbqyzrkseSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String qysdsmbksqylxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dndmbdkse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dndmbdkseBn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dndmbdkseZSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dndmbdkseBzSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dndmbdkseSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bnjnmbyjndksje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bnjnmbyjndksjeBn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bnjnmbyjndksjeSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bnjwmbyjndksje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bnjwmbyjndksjeBn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bnjwmbyjndksjeSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjzwmbwdksje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjzwmbwdksjeBn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjzwmbwdksjeSn;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ybndjnsdembdyqndkse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ybndjwsdembdyqndkse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yymbjnksze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String djbnjnks;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String mbyqndksdje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjzyhndmbdksehj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tzyy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dysbnddmbnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dysbnddmbjehj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bqbz;
    

    /**
     * 获取mbkstzuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMbkstzuuid() {
        return mbkstzuuid;
    }

    /**
     * 设置mbkstzuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMbkstzuuid(String value) {
        this.mbkstzuuid = value;
    }

    /**
     * 获取djxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * 设置djxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * 获取pzxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPzxh() {
        return pzxh;
    }

    /**
     * 设置pzxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPzxh(String value) {
        this.pzxh = value;
    }

    /**
     * 获取xh属性的值。
     * 
     */
    public String getXh() {
        return xh;
    }

    /**
     * 设置xh属性的值。
     * 
     */
    public void setXh(String value) {
        this.xh = value;
    }

    /**
     * 获取xmmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXmmc() {
        return xmmc;
    }

    /**
     * 设置xmmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXmmc(String value) {
        this.xmmc = value;
    }

    /**
     * 获取nd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNd() {
        return nd;
    }

    /**
     * 设置nd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNd(String value) {
        this.nd = value;
    }

    /**
     * 获取zcxbqnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZcxbqnd() {
        return zcxbqnd;
    }

    /**
     * 设置zcxbqnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZcxbqnd(String value) {
        this.zcxbqnd = value;
    }

    /**
     * 获取kshylje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKshylje() {
        return kshylje;
    }

    /**
     * 设置kshylje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKshylje(String value) {
        this.kshylje = value;
    }

    /**
     * 获取hbflqyzrkmbkse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHbflqyzrkmbkse() {
        return hbflqyzrkmbkse;
    }

    /**
     * 设置hbflqyzrkmbkse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHbflqyzrkmbkse(String value) {
        this.hbflqyzrkmbkse = value;
    }

    /**
     * 获取yqndhbflzrzclj属性的值。
     * 
     */
    public String getYqndhbflzrzclj() {
        return yqndhbflzrzclj;
    }

    /**
     * 设置yqndhbflzrzclj属性的值。
     * 
     */
    public void setYqndhbflzrzclj(String value) {
        this.yqndhbflzrzclj = value;
    }

    /**
     * 获取kmbdsde属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKmbdsde() {
        return kmbdsde;
    }

    /**
     * 设置kmbdsde属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKmbdsde(String value) {
        this.kmbdsde = value;
    }

    /**
     * 获取kmbdsdeSn属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKmbdsdeSn() {
        return kmbdsdeSn;
    }

    /**
     * 设置kmbdsdeSn属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKmbdsdeSn(String value) {
        this.kmbdsdeSn = value;
    }

    /**
     * 获取yqndymbksehj属性的值。
     * 
     */
    public String getYqndymbksehj() {
        return yqndymbksehj;
    }

    /**
     * 设置yqndymbksehj属性的值。
     * 
     */
    public void setYqndymbksehj(String value) {
        this.yqndymbksehj = value;
    }

    /**
     * 获取yqndymbksehjSn属性的值。
     * 
     */
    public String getYqndymbksehjSn() {
        return yqndymbksehjSn;
    }

    /**
     * 设置yqndymbksehjSn属性的值。
     * 
     */
    public void setYqndymbksehjSn(String value) {
        this.yqndymbksehjSn = value;
    }

    /**
     * 获取yqndjzdkmbkye属性的值。
     * 
     */
    public String getYqndjzdkmbkye() {
        return yqndjzdkmbkye;
    }

    /**
     * 设置yqndjzdkmbkye属性的值。
     * 
     */
    public void setYqndjzdkmbkye(String value) {
        this.yqndjzdkmbkye = value;
    }

    /**
     * 获取yqndjzdkmbkyeSn属性的值。
     * 
     */
    public String getYqndjzdkmbkyeSn() {
        return yqndjzdkmbkyeSn;
    }

    /**
     * 设置yqndjzdkmbkyeSn属性的值。
     * 
     */
    public void setYqndjzdkmbkyeSn(String value) {
        this.yqndjzdkmbkyeSn = value;
    }

    /**
     * 获取flzckse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlzckse() {
        return flzckse;
    }

    /**
     * 设置flzckse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlzckse(String value) {
        this.flzckse = value;
    }

    /**
     * 获取hbqyzrkse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHbqyzrkse() {
        return hbqyzrkse;
    }

    /**
     * 设置hbqyzrkse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHbqyzrkse(String value) {
        this.hbqyzrkse = value;
    }

    /**
     *创建时间:2020年10月21日下午2:24:24
     *get方法
     * @return the hbqyzrkseBn
     */
    public String getHbqyzrkseBn() {
        return hbqyzrkseBn;
    }

    /**
     * 创建时间:2020年10月21日下午2:24:24
     * set方法
     * @param hbqyzrkseBn the hbqyzrkseBn to set
     */
    public void setHbqyzrkseBn(String hbqyzrkseBn) {
        this.hbqyzrkseBn = hbqyzrkseBn;
    }

    /**
     * 获取hbqyzrkseSn属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHbqyzrkseSn() {
        return hbqyzrkseSn;
    }

    /**
     * 设置hbqyzrkseSn属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHbqyzrkseSn(String value) {
        this.hbqyzrkseSn = value;
    }

    /**
     * 获取qysdsmbksqylxDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQysdsmbksqylxDm() {
        return qysdsmbksqylxDm;
    }

    /**
     * 设置qysdsmbksqylxDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQysdsmbksqylxDm(String value) {
        this.qysdsmbksqylxDm = value;
    }

    /**
     * 获取dndmbdkse属性的值。
     * 
     */
    public String getDndmbdkse() {
        return dndmbdkse;
    }

    /**
     * 设置dndmbdkse属性的值。
     * 
     */
    public void setDndmbdkse(String value) {
        this.dndmbdkse = value;
    }

    /**
     * 获取dndmbdkseZSn属性的值。
     * 
     */
    public String getDndmbdkseZSn() {
        return dndmbdkseZSn;
    }

    /**
     * 设置dndmbdkseZSn属性的值。
     * 
     */
    public void setDndmbdkseZSn(String value) {
        this.dndmbdkseZSn = value;
    }

    /**
     * 获取dndmbdkseSn属性的值。
     * 
     */
    public String getDndmbdkseSn() {
        return dndmbdkseSn;
    }

    /**
     * 设置dndmbdkseSn属性的值。
     * 
     */
    public void setDndmbdkseSn(String value) {
        this.dndmbdkseSn = value;
    }

    /**
     * 获取bnjnmbyjndksje属性的值。
     * 
     */
    public String getBnjnmbyjndksje() {
        return bnjnmbyjndksje;
    }

    /**
     * 设置bnjnmbyjndksje属性的值。
     * 
     */
    public void setBnjnmbyjndksje(String value) {
        this.bnjnmbyjndksje = value;
    }

    /**
     * 获取bnjnmbyjndksjeSn属性的值。
     * 
     */
    public String getBnjnmbyjndksjeSn() {
        return bnjnmbyjndksjeSn;
    }

    /**
     * 设置bnjnmbyjndksjeSn属性的值。
     * 
     */
    public void setBnjnmbyjndksjeSn(String value) {
        this.bnjnmbyjndksjeSn = value;
    }

    /**
     * 获取bnjwmbyjndksje属性的值。
     * 
     */
    public String getBnjwmbyjndksje() {
        return bnjwmbyjndksje;
    }

    /**
     * 设置bnjwmbyjndksje属性的值。
     * 
     */
    public void setBnjwmbyjndksje(String value) {
        this.bnjwmbyjndksje = value;
    }

    /**
     * 获取bnjwmbyjndksjeSn属性的值。
     * 
     */
    public String getBnjwmbyjndksjeSn() {
        return bnjwmbyjndksjeSn;
    }

    /**
     * 设置bnjwmbyjndksjeSn属性的值。
     * 
     */
    public void setBnjwmbyjndksjeSn(String value) {
        this.bnjwmbyjndksjeSn = value;
    }

    /**
     * 获取kjzwmbwdksje属性的值。
     * 
     */
    public String getKjzwmbwdksje() {
        return kjzwmbwdksje;
    }

    /**
     * 设置kjzwmbwdksje属性的值。
     * 
     */
    public void setKjzwmbwdksje(String value) {
        this.kjzwmbwdksje = value;
    }

    /**
     * 获取kjzwmbwdksjeSn属性的值。
     * 
     */
    public String getKjzwmbwdksjeSn() {
        return kjzwmbwdksjeSn;
    }

    /**
     * 设置kjzwmbwdksjeSn属性的值。
     * 
     */
    public void setKjzwmbwdksjeSn(String value) {
        this.kjzwmbwdksjeSn = value;
    }

    /**
     * 获取ybndjnsdembdyqndkse属性的值。
     * 
     */
    public String getYbndjnsdembdyqndkse() {
        return ybndjnsdembdyqndkse;
    }

    /**
     * 设置ybndjnsdembdyqndkse属性的值。
     * 
     */
    public void setYbndjnsdembdyqndkse(String value) {
        this.ybndjnsdembdyqndkse = value;
    }

    /**
     * 获取ybndjwsdembdyqndkse属性的值。
     * 
     */
    public String getYbndjwsdembdyqndkse() {
        return ybndjwsdembdyqndkse;
    }

    /**
     * 设置ybndjwsdembdyqndkse属性的值。
     * 
     */
    public void setYbndjwsdembdyqndkse(String value) {
        this.ybndjwsdembdyqndkse = value;
    }

    /**
     * 获取yymbjnksze属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYymbjnksze() {
        return yymbjnksze;
    }

    /**
     * 设置yymbjnksze属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYymbjnksze(String value) {
        this.yymbjnksze = value;
    }

    /**
     * 获取djbnjnks属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjbnjnks() {
        return djbnjnks;
    }

    /**
     * 设置djbnjnks属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjbnjnks(String value) {
        this.djbnjnks = value;
    }

    /**
     * 获取mbyqndksdje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMbyqndksdje() {
        return mbyqndksdje;
    }

    /**
     * 设置mbyqndksdje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMbyqndksdje(String value) {
        this.mbyqndksdje = value;
    }

    /**
     * 获取kjzyhndmbdksehj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKjzyhndmbdksehj() {
        return kjzyhndmbdksehj;
    }

    /**
     * 设置kjzyhndmbdksehj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKjzyhndmbdksehj(String value) {
        this.kjzyhndmbdksehj = value;
    }

    /**
     * 获取tzyy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTzyy() {
        return tzyy;
    }

    /**
     * 设置tzyy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTzyy(String value) {
        this.tzyy = value;
    }

    public String getDysbnddmbnd() {
        return dysbnddmbnd;
    }

    public void setDysbnddmbnd(String dysbnddmbnd) {
        this.dysbnddmbnd = dysbnddmbnd;
    }

    public String getDysbnddmbjehj() {
        return dysbnddmbjehj;
    }

    public void setDysbnddmbjehj(String dysbnddmbjehj) {
        this.dysbnddmbjehj = dysbnddmbjehj;
    }

    public String getBqbz() {
        return bqbz;
    }

    public void setBqbz(String bqbz) {
        this.bqbz = bqbz;
    }

    /**
     *创建时间:2020年11月25日上午9:20:02
     *get方法
     * @return the kmbdsdeBn
     */
    public String getKmbdsdeBn() {
        return kmbdsdeBn;
    }

    /**
     * 创建时间:2020年11月25日上午9:20:02
     * set方法
     * @param kmbdsdeBn the kmbdsdeBn to set
     */
    public void setKmbdsdeBn(String kmbdsdeBn) {
        this.kmbdsdeBn = kmbdsdeBn;
    }

    /**
     *创建时间:2020年11月25日上午9:20:02
     *get方法
     * @return the yqndymbksehjBn
     */
    public String getYqndymbksehjBn() {
        return yqndymbksehjBn;
    }

    /**
     * 创建时间:2020年11月25日上午9:20:02
     * set方法
     * @param yqndymbksehjBn the yqndymbksehjBn to set
     */
    public void setYqndymbksehjBn(String yqndymbksehjBn) {
        this.yqndymbksehjBn = yqndymbksehjBn;
    }

    /**
     *创建时间:2020年11月25日上午9:20:02
     *get方法
     * @return the yqndjzdkmbkyeBn
     */
    public String getYqndjzdkmbkyeBn() {
        return yqndjzdkmbkyeBn;
    }

    /**
     * 创建时间:2020年11月25日上午9:20:02
     * set方法
     * @param yqndjzdkmbkyeBn the yqndjzdkmbkyeBn to set
     */
    public void setYqndjzdkmbkyeBn(String yqndjzdkmbkyeBn) {
        this.yqndjzdkmbkyeBn = yqndjzdkmbkyeBn;
    }

    /**
     *创建时间:2020年11月25日上午9:20:02
     *get方法
     * @return the dndmbdkseBn
     */
    public String getDndmbdkseBn() {
        return dndmbdkseBn;
    }

    /**
     * 创建时间:2020年11月25日上午9:20:02
     * set方法
     * @param dndmbdkseBn the dndmbdkseBn to set
     */
    public void setDndmbdkseBn(String dndmbdkseBn) {
        this.dndmbdkseBn = dndmbdkseBn;
    }

    /**
     *创建时间:2020年11月25日上午9:20:02
     *get方法
     * @return the dndmbdkseBzSn
     */
    public String getDndmbdkseBzSn() {
        return dndmbdkseBzSn;
    }

    /**
     * 创建时间:2020年11月25日上午9:20:02
     * set方法
     * @param dndmbdkseBzSn the dndmbdkseBzSn to set
     */
    public void setDndmbdkseBzSn(String dndmbdkseBzSn) {
        this.dndmbdkseBzSn = dndmbdkseBzSn;
    }

    /**
     *创建时间:2020年11月25日上午9:20:02
     *get方法
     * @return the bnjnmbyjndksjeBn
     */
    public String getBnjnmbyjndksjeBn() {
        return bnjnmbyjndksjeBn;
    }

    /**
     * 创建时间:2020年11月25日上午9:20:02
     * set方法
     * @param bnjnmbyjndksjeBn the bnjnmbyjndksjeBn to set
     */
    public void setBnjnmbyjndksjeBn(String bnjnmbyjndksjeBn) {
        this.bnjnmbyjndksjeBn = bnjnmbyjndksjeBn;
    }

    /**
     *创建时间:2020年11月25日上午9:20:02
     *get方法
     * @return the bnjwmbyjndksjeBn
     */
    public String getBnjwmbyjndksjeBn() {
        return bnjwmbyjndksjeBn;
    }

    /**
     * 创建时间:2020年11月25日上午9:20:02
     * set方法
     * @param bnjwmbyjndksjeBn the bnjwmbyjndksjeBn to set
     */
    public void setBnjwmbyjndksjeBn(String bnjwmbyjndksjeBn) {
        this.bnjwmbyjndksjeBn = bnjwmbyjndksjeBn;
    }

    /**
     *创建时间:2020年11月25日上午9:20:02
     *get方法
     * @return the kjzwmbwdksjeBn
     */
    public String getKjzwmbwdksjeBn() {
        return kjzwmbwdksjeBn;
    }

    /**
     * 创建时间:2020年11月25日上午9:20:02
     * set方法
     * @param kjzwmbwdksjeBn the kjzwmbwdksjeBn to set
     */
    public void setKjzwmbwdksjeBn(String kjzwmbwdksjeBn) {
        this.kjzwmbwdksjeBn = kjzwmbwdksjeBn;
    }

 
}
