package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 成品油购销存数量明细表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "cpygxcslmxbGridlbVO", propOrder = { "ylxh", "qckcl", "bqgjl", "bqcklYsxsl", "bqcklYkcsl", "qmkcl" })
@Getter
@Setter
public class CpygxcslmxbGridlbVO {
    /**
     * 油量型号
     */
    protected String ylxh;

    /**
     * 期初库存量
     */
    protected BigDecimal qckcl;

    /**
     * 本期购进量
     */
    protected BigDecimal bqgjl;

    /**
     * 本期出库量应税销售量
     */
    protected BigDecimal bqcklYsxsl;

    /**
     * 本期出库量应扣除数量
     */
    protected BigDecimal bqcklYkcsl;

    /**
     * 期末库存量
     */
    protected BigDecimal qmkcl;
}