package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import com.alibaba.fastjson.annotation.JSONField;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

/**
 * <p>glysGridlbVO complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="glysGridlbVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="xh" type="{http://www.chinatax.gov.cn/dataspec/}xh"/>
 *         &lt;element name="fphm_gl" type="{http://www.chinatax.gov.cn/dataspec/}fphm"/>
 *         &lt;element name="kprq_gl" type="{http://www.chinatax.gov.cn/dataspec/}kprq"/>
 *         &lt;element name="ysdwmc_gl" type="{http://www.chinatax.gov.cn/dataspec/}nsrmc"/>
 *         &lt;element name="ysdwnsrsbh_gl" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="ysdwzgdfswjmc_gl" type="{http://www.chinatax.gov.cn/dataspec/}swjgmc"/>
 *         &lt;element name="ysdwzgdfswjdm_gl" type="{http://www.chinatax.gov.cn/dataspec/}swjgDm"/>
 *         &lt;element name="yfje_gl" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yxjsdkyfje_gl" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="jsdkdjxse_gl" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "glysGridlbVO", propOrder = { "xh", "fphmGl", "kprqGl", "ysdwmcGl", "ysdwnsrsbhGl", "ysdwzgdfswjmcGl", "ysdwzgdfswjdmGl", "yfjeGl", "yxjsdkyfjeGl", "jsdkdjxseGl" })
public class GlysGridlbVO {
    /**
     * 公路运输序号
     */
    protected int xh;

    /**
     * 公路运输发票号码
     */
    @XmlElement(nillable = true, name = "fphm_gl", required = true)
    @JSONField(name = "fphm_gl")
    protected String fphmGl;

    /**
     * 公路运输开票日期
     */
    @XmlElement(nillable = true, name = "kprq_gl", required = true)
    @JSONField(name = "kprq_gl")
    protected String kprqGl;

    /**
     * 公路运输运输单位名称
     */
    @XmlElement(nillable = true, name = "ysdwmc_gl", required = true)
    @JSONField(name = "ysdwmc_gl")
    protected String ysdwmcGl;

    /**
     * 公路运输运输单位纳税人识别号
     */
    @XmlElement(nillable = true, name = "ysdwnsrsbh_gl", required = true)
    @JSONField(name = "ysdwnsrsbh_gl")
    protected String ysdwnsrsbhGl;

    /**
     * 公路运输运输单位主管地方税务局名称
     */
    @XmlElement(nillable = true, name = "ysdwzgdfswjmc_gl", required = true)
    @JSONField(name = "ysdwzgdfswjmc_gl")
    protected String ysdwzgdfswjmcGl;

    /**
     * 公路运输运输单位主管地方税务局代码
     */
    @XmlElement(nillable = true, name = "ysdwzgdfswjdm_gl", required = true)
    @JSONField(name = "ysdwzgdfswjdm_gl")
    protected String ysdwzgdfswjdmGl;

    /**
     * 公路运输运费金额
     */
    @XmlElement(name = "yfje_gl")
    @JSONField(name = "yfje_gl")
    protected BigDecimal yfjeGl;

    /**
     * 公路运输允许计算抵扣运费金额
     */
    @XmlElement(name = "yxjsdkyfje_gl")
    @JSONField(name = "yxjsdkyfje_gl")
    protected BigDecimal yxjsdkyfjeGl;

    /**
     * 公路运输计算抵扣的进项税额
     */
    @XmlElement(name = "jsdkdjxse_gl")
    @JSONField(name = "jsdkdjxse_gl")
    protected BigDecimal jsdkdjxseGl;

    /**
     * 获取xh属性的值。
     * <p>
     * 公路运输序号
     */
    public int getXh() {
        return xh;
    }

    /**
     * 设置xh属性的值。
     */
    public void setXh(int value) {
        this.xh = value;
    }

    /**
     * 获取fphmGl属性的值。
     * <p>
     * 公路运输发票号码
     */
    public String getFphmGl() {
        return fphmGl;
    }

    /**
     * 设置fphmGl属性的值。
     */
    public void setFphmGl(String value) {
        this.fphmGl = value;
    }

    /**
     * 获取kprqGl属性的值。
     * <p>
     * 公路运输开票日期
     */
    public String getKprqGl() {
        return kprqGl;
    }

    /**
     * 设置kprqGl属性的值。
     */
    public void setKprqGl(String value) {
        this.kprqGl = value;
    }

    /**
     * 获取ysdwmcGl属性的值。
     * <p>
     * 公路运输运输单位名称
     */
    public String getYsdwmcGl() {
        return ysdwmcGl;
    }

    /**
     * 设置ysdwmcGl属性的值。
     */
    public void setYsdwmcGl(String value) {
        this.ysdwmcGl = value;
    }

    /**
     * 获取ysdwnsrsbhGl属性的值。
     * <p>
     * 公路运输运输单位纳税人识别号
     */
    public String getYsdwnsrsbhGl() {
        return ysdwnsrsbhGl;
    }

    /**
     * 设置ysdwnsrsbhGl属性的值。
     */
    public void setYsdwnsrsbhGl(String value) {
        this.ysdwnsrsbhGl = value;
    }

    /**
     * 获取ysdwzgdfswjmcGl属性的值。
     * <p>
     * 公路运输运输单位主管地方税务局名称
     */
    public String getYsdwzgdfswjmcGl() {
        return ysdwzgdfswjmcGl;
    }

    /**
     * 设置ysdwzgdfswjmcGl属性的值。
     */
    public void setYsdwzgdfswjmcGl(String value) {
        this.ysdwzgdfswjmcGl = value;
    }

    /**
     * 获取ysdwzgdfswjdmGl属性的值。
     * <p>
     * 公路运输运输单位主管地方税务局代码
     */
    public String getYsdwzgdfswjdmGl() {
        return ysdwzgdfswjdmGl;
    }

    /**
     * 设置ysdwzgdfswjdmGl属性的值。
     */
    public void setYsdwzgdfswjdmGl(String value) {
        this.ysdwzgdfswjdmGl = value;
    }

    /**
     * 获取yfjeGl属性的值。
     * <p>
     * 公路运输运费金额
     */
    public BigDecimal getYfjeGl() {
        return yfjeGl;
    }

    /**
     * 设置yfjeGl属性的值。
     */
    public void setYfjeGl(BigDecimal value) {
        this.yfjeGl = value;
    }

    /**
     * 获取yxjsdkyfjeGl属性的值。
     * <p>
     * 公路运输允许计算抵扣运费金额
     */
    public BigDecimal getYxjsdkyfjeGl() {
        return yxjsdkyfjeGl;
    }

    /**
     * 设置yxjsdkyfjeGl属性的值。
     */
    public void setYxjsdkyfjeGl(BigDecimal value) {
        this.yxjsdkyfjeGl = value;
    }

    /**
     * 获取jsdkdjxseGl属性的值。
     * <p>
     * 公路运输计算抵扣的进项税额
     */
    public BigDecimal getJsdkdjxseGl() {
        return jsdkdjxseGl;
    }

    /**
     * 设置jsdkdjxseGl属性的值。
     */
    public void setJsdkdjxseGl(BigDecimal value) {
        this.jsdkdjxseGl = value;
    }
}