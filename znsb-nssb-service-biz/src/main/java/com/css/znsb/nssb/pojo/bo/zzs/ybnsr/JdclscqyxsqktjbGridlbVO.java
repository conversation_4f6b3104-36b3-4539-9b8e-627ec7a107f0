package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 机动车辆生产企业销售情况统计表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jdclscqyxsqktjbGridlbVO", propOrder = { "ewbhxh", "pjxsjg", "xssl", "cpxh1", "xssr" })
@Getter
@Setter
public class JdclscqyxsqktjbGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 平均销售价格
     */
    protected BigDecimal pjxsjg;

    /**
     * 销售数量
     */
    protected BigDecimal xssl;

    /**
     * 厂牌型号
     */
    protected String cpxh1;

    /**
     * 销售收入
     */
    protected BigDecimal xssr;
}