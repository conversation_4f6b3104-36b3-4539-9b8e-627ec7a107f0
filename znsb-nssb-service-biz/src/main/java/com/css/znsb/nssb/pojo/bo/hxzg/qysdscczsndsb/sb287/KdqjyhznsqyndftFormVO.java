
package com.css.znsb.nssb.pojo.bo.hxzg.qysdscczsndsb.sb287;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 《跨地区经营汇总纳税企业年度分摊企业所得税明细表》
 * 
 * <p>Java class for kdqjyhznsqyndftFormVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="kdqjyhznsqyndftFormVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="zjgsjynsdse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwsdynsdse1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwsddmsdse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgyyftdbnsjynsdse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnljyysds" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgxqzjgldyfsdse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgyftsdse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="czjzyfpsdse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgfzjgftsde" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgztscftsdse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgbnyftybtse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgftybtse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="czjzfpybtse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgssfzftybtse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgztscjybmybtse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgjwsddmhynse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgbnybtse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgyjmdffxbfdzje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgyjmdffxbfdje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgyyhtzdffxbfdje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgbnsjybtse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "kdqjyhznsqyndftFormVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "zjgsjynsdse",
    "jwsdynsdse1",
    "jwsddmsdse",
    "zjgyyftdbnsjynsdse",
    "bnljyysds",
    "zjgxqzjgldyfsdse",
    "zjgyftsdse",
    "czjzyfpsdse",
    "zjgfzjgftsde",
    "zjgztscftsdse",
    "zjgbnyftybtse",
    "zjgftybtse",
    "czjzfpybtse",
    "zjgssfzftybtse",
    "zjgztscjybmybtse",
    "zjgjwsddmhynse",
    "zjgbnybtse",
    "zjgyjmdffxbfdzje",
    "zjgyjmdffxbfdje",
    "zjgyyhtzdffxbfdje",
    "zjgbnsjybtse"
})
public class KdqjyhznsqyndftFormVO
    implements Serializable
{

    private final static long serialVersionUID = -1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgsjynsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jwsdynsdse1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jwsddmsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgyyftdbnsjynsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnljyysds;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgxqzjgldyfsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgyftsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double czjzyfpsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgfzjgftsde;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgztscftsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgbnyftybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgftybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double czjzfpybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgssfzftybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgztscjybmybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgjwsddmhynse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgbnybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgyjmdffxbfdzje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgyjmdffxbfdje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgyyhtzdffxbfdje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgbnsjybtse;

    /**
     * Gets the value of the zjgsjynsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgsjynsdse() {
        return zjgsjynsdse;
    }

    /**
     * Sets the value of the zjgsjynsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgsjynsdse(Double value) {
        this.zjgsjynsdse = value;
    }

    /**
     * Gets the value of the jwsdynsdse1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJwsdynsdse1() {
        return jwsdynsdse1;
    }

    /**
     * Sets the value of the jwsdynsdse1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJwsdynsdse1(Double value) {
        this.jwsdynsdse1 = value;
    }

    /**
     * Gets the value of the jwsddmsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJwsddmsdse() {
        return jwsddmsdse;
    }

    /**
     * Sets the value of the jwsddmsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJwsddmsdse(Double value) {
        this.jwsddmsdse = value;
    }

    /**
     * Gets the value of the zjgyyftdbnsjynsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgyyftdbnsjynsdse() {
        return zjgyyftdbnsjynsdse;
    }

    /**
     * Sets the value of the zjgyyftdbnsjynsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgyyftdbnsjynsdse(Double value) {
        this.zjgyyftdbnsjynsdse = value;
    }

    /**
     * Gets the value of the bnljyysds property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnljyysds() {
        return bnljyysds;
    }

    /**
     * Sets the value of the bnljyysds property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnljyysds(Double value) {
        this.bnljyysds = value;
    }

    /**
     * Gets the value of the zjgxqzjgldyfsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgxqzjgldyfsdse() {
        return zjgxqzjgldyfsdse;
    }

    /**
     * Sets the value of the zjgxqzjgldyfsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgxqzjgldyfsdse(Double value) {
        this.zjgxqzjgldyfsdse = value;
    }

    /**
     * Gets the value of the zjgyftsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgyftsdse() {
        return zjgyftsdse;
    }

    /**
     * Sets the value of the zjgyftsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgyftsdse(Double value) {
        this.zjgyftsdse = value;
    }

    /**
     * Gets the value of the czjzyfpsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getCzjzyfpsdse() {
        return czjzyfpsdse;
    }

    /**
     * Sets the value of the czjzyfpsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setCzjzyfpsdse(Double value) {
        this.czjzyfpsdse = value;
    }

    /**
     * Gets the value of the zjgfzjgftsde property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgfzjgftsde() {
        return zjgfzjgftsde;
    }

    /**
     * Sets the value of the zjgfzjgftsde property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgfzjgftsde(Double value) {
        this.zjgfzjgftsde = value;
    }

    /**
     * Gets the value of the zjgztscftsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgztscftsdse() {
        return zjgztscftsdse;
    }

    /**
     * Sets the value of the zjgztscftsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgztscftsdse(Double value) {
        this.zjgztscftsdse = value;
    }

    /**
     * Gets the value of the zjgbnyftybtse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgbnyftybtse() {
        return zjgbnyftybtse;
    }

    /**
     * Sets the value of the zjgbnyftybtse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgbnyftybtse(Double value) {
        this.zjgbnyftybtse = value;
    }

    /**
     * Gets the value of the zjgftybtse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgftybtse() {
        return zjgftybtse;
    }

    /**
     * Sets the value of the zjgftybtse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgftybtse(Double value) {
        this.zjgftybtse = value;
    }

    /**
     * Gets the value of the czjzfpybtse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getCzjzfpybtse() {
        return czjzfpybtse;
    }

    /**
     * Sets the value of the czjzfpybtse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setCzjzfpybtse(Double value) {
        this.czjzfpybtse = value;
    }

    /**
     * Gets the value of the zjgssfzftybtse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgssfzftybtse() {
        return zjgssfzftybtse;
    }

    /**
     * Sets the value of the zjgssfzftybtse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgssfzftybtse(Double value) {
        this.zjgssfzftybtse = value;
    }

    /**
     * Gets the value of the zjgztscjybmybtse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgztscjybmybtse() {
        return zjgztscjybmybtse;
    }

    /**
     * Sets the value of the zjgztscjybmybtse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgztscjybmybtse(Double value) {
        this.zjgztscjybmybtse = value;
    }

    /**
     * Gets the value of the zjgjwsddmhynse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgjwsddmhynse() {
        return zjgjwsddmhynse;
    }

    /**
     * Sets the value of the zjgjwsddmhynse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgjwsddmhynse(Double value) {
        this.zjgjwsddmhynse = value;
    }

    /**
     * Gets the value of the zjgbnybtse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgbnybtse() {
        return zjgbnybtse;
    }

    /**
     * Sets the value of the zjgbnybtse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgbnybtse(Double value) {
        this.zjgbnybtse = value;
    }

    /**
     * Gets the value of the zjgyjmdffxbfdzje property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgyjmdffxbfdzje() {
        return zjgyjmdffxbfdzje;
    }

    /**
     * Sets the value of the zjgyjmdffxbfdzje property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgyjmdffxbfdzje(Double value) {
        this.zjgyjmdffxbfdzje = value;
    }

    /**
     * Gets the value of the zjgyjmdffxbfdje property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgyjmdffxbfdje() {
        return zjgyjmdffxbfdje;
    }

    /**
     * Sets the value of the zjgyjmdffxbfdje property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgyjmdffxbfdje(Double value) {
        this.zjgyjmdffxbfdje = value;
    }

    /**
     * Gets the value of the zjgyyhtzdffxbfdje property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgyyhtzdffxbfdje() {
        return zjgyyhtzdffxbfdje;
    }

    /**
     * Sets the value of the zjgyyhtzdffxbfdje property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgyyhtzdffxbfdje(Double value) {
        this.zjgyyhtzdffxbfdje = value;
    }

    /**
     * Gets the value of the zjgbnsjybtse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgbnsjybtse() {
        return zjgbnsjybtse;
    }

    /**
     * Sets the value of the zjgbnsjybtse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgbnsjybtse(Double value) {
        this.zjgbnsjybtse = value;
    }

}
