package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 税款征收信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "skzsFormVO", propOrder = { "qylxDm", "slsdljt", "xsdl", "dj", "sdsr", "bzsJwfy", "ysJwfy", "deslhyzl", "bqfsjx", "mshwy", "fzcss", "zr", "bqsjdkjx", "bqyjzzs1", "bqyjzzs", "bqqjzzs", "bnljjnzzs" })
@Getter
@Setter
public class SkzsFormVO {
    /**
     * 企业类型代码
     */
    protected String qylxDm;

    /**
     * 所隶属电力集团
     */
    protected String slsdljt;

    /**
     * 销售电量上网电量
     */
    protected BigDecimal xsdl;

    /**
     * 单价
     */
    protected BigDecimal dj;

    /**
     * 销项-售电收入
     */
    protected BigDecimal sdsr;

    /**
     * 价外费用其中不征税的价外费用
     */
    protected BigDecimal bzsJwfy;

    /**
     * 价外费用应税价外费用
     */
    protected BigDecimal ysJwfy;

    /**
     * 定额税率或预征率
     */
    protected BigDecimal deslhyzl;

    /**
     * 进项-本期发生进项
     */
    protected BigDecimal bqfsjx;

    /**
     * 免税货物用
     */
    protected BigDecimal mshwy;

    /**
     * 非正常损失
     */
    protected BigDecimal fzcss;

    /**
     * 折让
     */
    protected BigDecimal zr;

    /**
     * 本期实际抵扣进项
     */
    protected BigDecimal bqsjdkjx;

    /**
     * 本期应缴增值税
     */
    protected BigDecimal bqyjzzs1;

    /**
     * 税款征收-本期应缴增值税
     */
    protected BigDecimal bqyjzzs;

    /**
     * 税款征收-本期欠缴增值税
     */
    protected BigDecimal bqqjzzs;

    /**
     * 税款征收-本年累计缴纳增值税
     */
    protected BigDecimal bnljjnzzs;
}