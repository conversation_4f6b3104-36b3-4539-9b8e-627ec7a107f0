package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《代扣代缴税收通用缴款书抵扣清单》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_dkdjsstyjksdkqdbw", propOrder = { "zzsybnsrsbDkdjsstyjksdkqd" })
@Getter
@Setter
public class ZzsybnsrsbDkdjsstyjksdkqdbw extends TaxDoc {
    /**
     * 《代扣代缴税收通用缴款书抵扣清单》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_dkdjsstyjksdkqd", required = true)
    @JSONField(name = "zzsybnsrsb_dkdjsstyjksdkqd")
    protected ZzsybnsrsbDkdjsstyjksdkqd zzsybnsrsbDkdjsstyjksdkqd;
}