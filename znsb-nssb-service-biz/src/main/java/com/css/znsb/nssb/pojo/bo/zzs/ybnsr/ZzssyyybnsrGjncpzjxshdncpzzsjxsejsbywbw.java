package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《购进农产品直接销售核定农产品增值税进项税额计算表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_gjncpzjxshdncpzzsjxsejsbywbw", propOrder = { "zzssyyybnsrGjncpzjxshdncpzzsjxsejsb" })
@Getter
@Setter
public class ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw extends TaxDoc {
    /**
     * 购进农产品直接销售核定农产品增值税进项税额计算表
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb", required = true)
    @JSONField(name = "zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb")
    protected ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb zzssyyybnsrGjncpzjxshdncpzzsjxsejsb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb {}
}