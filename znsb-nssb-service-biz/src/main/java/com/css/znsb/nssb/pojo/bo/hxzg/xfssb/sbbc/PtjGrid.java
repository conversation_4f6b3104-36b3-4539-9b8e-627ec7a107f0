
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 葡萄酒（海关进口消费税专用缴款书）
 * 
 * <p>Java class for ptjGrid complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ptjGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *         &lt;element name="ptjGridlb" type="{http://www.chinatax.gov.cn/dataspec/}ptjGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ptjGrid", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "ptjGridlb"
})
public class PtjGrid
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected List<PtjGridlbVO> ptjGridlb;

    /**
     * Gets the value of the ptjGridlb property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the ptjGridlb property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPtjGridlb().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PtjGridlbVO }
     * 
     * 
     */
    public List<PtjGridlbVO> getPtjGridlb() {
        if (ptjGridlb == null) {
            ptjGridlb = new ArrayList<PtjGridlbVO>();
        }
        return this.ptjGridlb;
    }

}
