package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《成品油购销存情况明细表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_cpygxcqkmxbywbw", propOrder = { "zzssyyybnsrCpygxcqkmxb" })
@Getter
@Setter
public class ZzssyyybnsrCpygxcqkmxbywbw extends TaxDoc {
    /**
     * 《成品油购销存情况明细表》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_cpygxcqkmxb", required = true)
    @JSONField(name = "zzssyyybnsr_cpygxcqkmxb")
    protected ZzssyyybnsrCpygxcqkmxb zzssyyybnsrCpygxcqkmxb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrCpygxcqkmxb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrCpygxcqkmxb {}
}