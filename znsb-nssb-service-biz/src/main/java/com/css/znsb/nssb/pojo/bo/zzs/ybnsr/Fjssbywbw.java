package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 附加税申报业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "fjssbywbw", propOrder = { "fjssb" })
@Getter
@Setter
public class Fjssbywbw extends TaxDoc {
    /**
     * 增值税附加税申报
     */
    @XmlElement(nillable = true, required = true)
    protected Zzsfjssb fjssb;
}