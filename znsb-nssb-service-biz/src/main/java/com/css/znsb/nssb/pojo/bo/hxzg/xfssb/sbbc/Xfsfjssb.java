
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 附加税申报
 * 
 * <p>Java class for xfsfjssb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfsfjssb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="fjsxxGrid" type="{http://www.chinatax.gov.cn/dataspec/}fjsxxGrid"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfsfjssb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "fjsxxGrid"
})
public class Xfsfjssb
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected FjsxxGrid fjsxxGrid;

    /**
     * Gets the value of the fjsxxGrid property.
     * 
     * @return
     *     possible object is
     *     {@link FjsxxGrid }
     *     
     */
    public FjsxxGrid getFjsxxGrid() {
        return fjsxxGrid;
    }

    /**
     * Sets the value of the fjsxxGrid property.
     * 
     * @param value
     *     allowed object is
     *     {@link FjsxxGrid }
     *     
     */
    public void setFjsxxGrid(FjsxxGrid value) {
        this.fjsxxGrid = value;
    }

}
