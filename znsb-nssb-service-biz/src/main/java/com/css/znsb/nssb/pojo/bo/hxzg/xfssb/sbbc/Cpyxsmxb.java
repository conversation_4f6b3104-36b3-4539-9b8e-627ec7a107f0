
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《成品油销售明细表》
 * 
 * <p>Java class for cpyxsmxb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="cpyxsmxb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="cpyxsmxbGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="cpyxsmxbGridlb" type="{http://www.chinatax.gov.cn/dataspec/}cpyxsmxbGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "cpyxsmxb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "cpyxsmxbGrid"
})
public class Cpyxsmxb
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected CpyxsmxbGrid cpyxsmxbGrid;

    /**
     * Gets the value of the cpyxsmxbGrid property.
     * 
     * @return
     *     possible object is
     *     {@link CpyxsmxbGrid }
     *     
     */
    public CpyxsmxbGrid getCpyxsmxbGrid() {
        return cpyxsmxbGrid;
    }

    /**
     * Sets the value of the cpyxsmxbGrid property.
     * 
     * @param value
     *     allowed object is
     *     {@link CpyxsmxbGrid }
     *     
     */
    public void setCpyxsmxbGrid(CpyxsmxbGrid value) {
        this.cpyxsmxbGrid = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="cpyxsmxbGridlb" type="{http://www.chinatax.gov.cn/dataspec/}cpyxsmxbGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "cpyxsmxbGridlb"
    })
    public static class CpyxsmxbGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<CpyxsmxbGridlbVO> cpyxsmxbGridlb;

        /**
         * Gets the value of the cpyxsmxbGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the cpyxsmxbGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getCpyxsmxbGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link CpyxsmxbGridlbVO }
         * 
         * 
         */
        public List<CpyxsmxbGridlbVO> getCpyxsmxbGridlb() {
            if (cpyxsmxbGridlb == null) {
                cpyxsmxbGridlb = new ArrayList<CpyxsmxbGridlbVO>();
            }
            return this.cpyxsmxbGridlb;
        }

    }

}
