package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《油气田企业增值税分配表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_yqtqyzzsfpb", propOrder = { "yqtqyzzsfpbGrid", "yqtqyzzsfpbForm" })
@XmlSeeAlso({ ZzssyyybnsrYqtqyzzsfpbywbw.ZzssyyybnsrYqtqyzzsfpb.class })
@Getter
@Setter
public class ZzssyyybnsrYqtqyzzsfpb {
    @XmlElement(nillable = true, required = true)
    protected YqtqyzzsfpbGrid yqtqyzzsfpbGrid;

    /**
     * 油气田企业增值税分配表
     */
    @XmlElement(nillable = true, required = true)
    protected YqtqyzzsfpbFormVO yqtqyzzsfpbForm;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "yqtqyzzsfpbGridlbVO" })
    @Getter
    @Setter
    public static class YqtqyzzsfpbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<YqtqyzzsfpbGridlbVO> yqtqyzzsfpbGridlbVO;

        /**
         * Gets the value of the yqtqyzzsfpbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the yqtqyzzsfpbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getYqtqyzzsfpbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link YqtqyzzsfpbGridlbVO}
         */
        public List<YqtqyzzsfpbGridlbVO> getYqtqyzzsfpbGridlbVO() {
            if (yqtqyzzsfpbGridlbVO == null) {
                yqtqyzzsfpbGridlbVO = new ArrayList<YqtqyzzsfpbGridlbVO>();
            }
            return this.yqtqyzzsfpbGridlbVO;
        }
    }
}