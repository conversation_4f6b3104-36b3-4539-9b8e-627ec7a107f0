package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw", propOrder = { "zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb" })
@Getter
@Setter
public class ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw extends TaxDoc {
    /**
     * 购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb", required = true)
    @JSONField(name = "zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb")
    protected ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb {}
}