package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 已缴纳增值税情况
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "yjnzzskGridlb", propOrder = { "wlfzfwTljsjj", "wlfzfwCbse", "wlfzfwSl", "wlfzfwCbxse", "wlfzfwYjse", "wlfzfwYzl", "wlfzfwXse", "jtysfwTljsjj", "jtysfwCbse", "jtysfwSl", "jtysfwCbxse", "jtysfwYjse", "jtysfwYzl", "jtysfwXse" })
@Getter
@Setter
public class YjnzzskGridlb {
    /**
     * 物流辅助服务铁路建设基金
     */
    protected BigDecimal wlfzfwTljsjj;

    /**
     * 物流辅助服务查补税额
     */
    protected BigDecimal wlfzfwCbse;

    /**
     * 物流辅助服务税率
     */
    protected BigDecimal wlfzfwSl;

    /**
     * 物流辅助服务查补销售额
     */
    protected BigDecimal wlfzfwCbxse;

    /**
     * 物流辅助服务预缴税额
     */
    protected BigDecimal wlfzfwYjse;

    /**
     * 物流辅助服务预征率
     */
    protected BigDecimal wlfzfwYzl;

    /**
     * 物流辅助服务销售额不含铁路建设基金
     */
    protected BigDecimal wlfzfwXse;

    /**
     * 交通运输服务铁路建设基金
     */
    protected BigDecimal jtysfwTljsjj;

    /**
     * 交通运输服务查补税额
     */
    protected BigDecimal jtysfwCbse;

    /**
     * 交通运输服务税率
     */
    protected BigDecimal jtysfwSl;

    /**
     * 交通运输服务查补销售额
     */
    protected BigDecimal jtysfwCbxse;

    /**
     * 交通运输服务预缴税额
     */
    protected BigDecimal jtysfwYjse;

    /**
     * 交通运输服务预征率
     */
    protected BigDecimal jtysfwYzl;

    /**
     * 交通运输服务销售额不含铁路建设基金
     */
    protected BigDecimal jtysfwXse;
}