package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

/**
 * 铁路建设基金纳税申报表
 *
 * <p>tljsjjnssbbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="tljsjjnssbbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ewbhxh" type="{http://www.chinatax.gov.cn/dataspec/}ewbhxh" minOccurs="0"/>
 *         &lt;element name="bys" type="{http://www.chinatax.gov.cn/dataspec/}bys" minOccurs="0"/>
 *         &lt;element name="bnlj" type="{http://www.chinatax.gov.cn/dataspec/}bnlj" minOccurs="0"/>
 *         &lt;element name="hmc" type="{http://www.chinatax.gov.cn/dataspec/}hmc"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "tljsjjnssbbGrid", propOrder = { "ewbhxh", "bys", "bnlj", "hmc" })
public class TljsjjnssbbGrid {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 本月数
     */
    protected BigDecimal bys;

    /**
     * 本年累计
     */
    protected BigDecimal bnlj;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 获取ewbhxh属性的值。
     * <p>
     * 二维表行序号
     */
    public Long getEwbhxh() {
        return ewbhxh;
    }

    /**
     * 设置ewbhxh属性的值。
     */
    public void setEwbhxh(Long value) {
        this.ewbhxh = value;
    }

    /**
     * 获取bys属性的值。
     * <p>
     * 本月数
     */
    public BigDecimal getBys() {
        return bys;
    }

    /**
     * 设置bys属性的值。
     */
    public void setBys(BigDecimal value) {
        this.bys = value;
    }

    /**
     * 获取bnlj属性的值。
     * <p>
     * 本年累计
     */
    public BigDecimal getBnlj() {
        return bnlj;
    }

    /**
     * 设置bnlj属性的值。
     */
    public void setBnlj(BigDecimal value) {
        this.bnlj = value;
    }

    /**
     * 获取hmc属性的值。
     * <p>
     * 行名称
     */
    public String getHmc() {
        return hmc;
    }

    /**
     * 设置hmc属性的值。
     */
    public void setHmc(String value) {
        this.hmc = value;
    }
}