package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《代扣代缴税收通用缴款书抵扣清单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dkdjsstyjksdkqdGridlbVO", propOrder = { "se", "dkdjpzbh", "dkdjxm", "zsjgmc", "kjrmc", "kjrnsrsbh" })
@Getter
@Setter
public class DkdjsstyjksdkqdGridlbVO {
    /**
     * 税额
     */
    protected BigDecimal se;

    /**
     * 代扣代缴凭证编号
     */
    protected String dkdjpzbh;

    /**
     * 代扣代缴项目
     */
    protected String dkdjxm;

    /**
     * 征收机关名称
     */
    protected String zsjgmc;

    /**
     * 扣缴人名称
     */
    protected String kjrmc;

    /**
     * 扣缴人纳税人识别号
     */
    protected String kjrnsrsbh;
}