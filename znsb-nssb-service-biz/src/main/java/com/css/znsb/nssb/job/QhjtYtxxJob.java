package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.pojo.domain.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjBsqcDO;
import com.css.znsb.nssb.pojo.domain.skjn.ZnsbNssbJksmxDO;
import com.css.znsb.nssb.pojo.domain.zlbs.ZnsbNssbZlbsqyxxpzbDO;
import com.css.znsb.nssb.pojo.dto.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjBsqcDTO;
import com.css.znsb.nssb.pojo.dto.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjBsxxmxDTO;
import com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO;
import com.css.znsb.nssb.service.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjService;
import com.css.znsb.nssb.service.skjn.ZnsbNssbJksmxService;
import com.css.znsb.tzzx.mapper.PzFlMxMapper;
import com.css.znsb.tzzx.service.yjtz.ZnsbTzzxQysdsyjtzService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import static com.css.znsb.nssb.constants.qhjt.QhjtConstants.DECIMAL;
import static com.css.znsb.nssb.constants.qhjt.QhjtConstants.QHJTCONSTANTS;

@Slf4j
@Component
public class QhjtYtxxJob {

    @Resource
    private ZnsbNssbQhjtjcsssjService znsbNssbQhjtjcsssjService;
    @Resource
    private ZnsbNssbJksmxService znsbNssbJksmxService;
    @Resource
    private PzFlMxMapper pzFlMxMapper;
    @Resource
    private ZnsbTzzxQysdsyjtzService znsbTzzxQysdsyjtzService;

    /**
     * 千户集团预填信息
     */
    @XxlJob("qhjtDataQuery")
    public void execute() {
        log.info("==========开始千户集团企业预填信息归集==========");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("入参登记序号jobParam{}",jobParam);
        String djxh = "";
        String nyr = "";
        if(GyUtils.isNotNull(jobParam)) {
            String[] param = jobParam.split(";");
            djxh = param[0];
            nyr = param[1];
        }
        //当前时间
        LocalDate currentDate = LocalDate.now();
        if(GyUtils.isNotNull(nyr)){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            currentDate = LocalDate.parse(nyr, formatter);
        }
        //本期时间
        LocalDate bnbqLocal = currentDate.minusMonths(1);
        String bnbq = bnbqLocal.format(DateTimeFormatter.ofPattern("yyyyMM"));
        //上期时间
        LocalDate snbqLocal = bnbqLocal.minusYears(1);
        String snbq = snbqLocal.format(DateTimeFormatter.ofPattern("yyyyMM"));
        //本年年初
        LocalDate firstDayOfYear = currentDate.withDayOfYear(1);
        String yearFirstDay = firstDayOfYear.format(DateTimeFormatter.ofPattern("yyyyMM"));
        //本年年末
        LocalDate lastDayOfYear = bnbqLocal.with(TemporalAdjusters.lastDayOfMonth());
        String bqLastDay = lastDayOfYear.format(DateTimeFormatter.ofPattern("yyyyMM"));
        //本年年初
        LocalDate firstDayOfYearOld = snbqLocal.withDayOfYear(1);
        String yearFirstDayOld = firstDayOfYearOld.format(DateTimeFormatter.ofPattern("yyyyMM"));
        //本年年末
        LocalDate lastDayOfYearOld = snbqLocal.with(TemporalAdjusters.lastDayOfMonth());
        String bqLastDayOld = lastDayOfYearOld.format(DateTimeFormatter.ofPattern("yyyyMM"));

        List<ZnsbNssbZlbsqyxxpzbDO> qhjtList = znsbNssbQhjtjcsssjService.selectQhjtqyxx(djxh);
        for(ZnsbNssbZlbsqyxxpzbDO qhjtDO : qhjtList){
            final ZnsbNssbQhjtjcsssjBsqcDTO bsqcDTO = BeanUtils.toBean(qhjtDO,ZnsbNssbQhjtjcsssjBsqcDTO.class);
            bsqcDTO.setBsssq(bnbq);
            final ZnsbNssbQhjtjcsssjBsqcDO bsqcDO = znsbNssbQhjtjcsssjService.selectBsqcQcxx(bsqcDTO);
            String bsuuid = GyUtils.getUuid();
            if (GyUtils.isNull(bsqcDO)) {
                ZnsbNssbQhjtjcsssjBsqcDO bqbsqcDO = new ZnsbNssbQhjtjcsssjBsqcDO();
                bqbsqcDO.setUuid(GyUtils.getUuid());
                bqbsqcDO.setBsuuid(bsuuid);
                bqbsqcDO.setDjxh(new BigDecimal(bsqcDTO.getDjxh()));
                bqbsqcDO.setNsrsbh(bsqcDTO.getNsrsbh());
                bqbsqcDO.setBsqx(new Date());
                bqbsqcDO.setBsqq(new Date());
                bqbsqcDO.setBsqz(new Date());
                bqbsqcDO.setBsztDm("0");
                bqbsqcDO.setBsrq(new Date());
                bqbsqcDO.setBsssq(bnbq);
                znsbNssbQhjtjcsssjService.initBsqcQcxx(bqbsqcDO);
            }else{
                bsuuid = bsqcDO.getBsuuid();
            }
            ZnsbNssbJksmxDO znsbNssbJksmxDO = znsbNssbJksmxService.selectJkljs(qhjtDO.getDjxh()
                    , firstDayOfYear.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                    , lastDayOfYear.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            log.info("本年年初{}",firstDayOfYear.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            log.info("本年年末{}",lastDayOfYear.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            log.info("查询结果{}", JsonUtils.toJson(znsbNssbJksmxDO));
            ZnsbNssbJksmxDO sqZnsbNssbJksmxDO = znsbNssbJksmxService.selectJkljs(qhjtDO.getDjxh()
                    , firstDayOfYearOld.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                    , lastDayOfYearOld.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            log.info("去年年初{}",firstDayOfYearOld.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            log.info("去年年末{}",lastDayOfYearOld.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            log.info("查询结果{}", JsonUtils.toJson(sqZnsbNssbJksmxDO));
            Map<String, BigDecimal> bqZcfzbMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(qhjtDO.getGsh1(),bnbq,"SM01","G");
            Map<String, BigDecimal> bqLrbMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(qhjtDO.getGsh1(),bnbq,"SM03","G");
            Map<String, BigDecimal> bqXjllbMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(qhjtDO.getGsh1(),bnbq,"SM05","G");
            Map<String, BigDecimal> bqGdzcMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(qhjtDO.getGsh1(),bnbq,"SM14","G");
            Map<String, BigDecimal> sqZcfzbMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(qhjtDO.getGsh1(),snbq,"SM01","G");
            Map<String, BigDecimal> sqLrbMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(qhjtDO.getGsh1(),snbq,"SM03","G");
            Map<String, BigDecimal> sqXjllbMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(qhjtDO.getGsh1(),snbq,"SM05","G");
            Map<String, BigDecimal> sqGdzcMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(qhjtDO.getGsh1(),snbq,"SM14","G");


            final BigDecimal bqldzchj = GyUtils.isNotNull(bqZcfzbMap.get("SM01-01BS1010F99"))?bqZcfzbMap.get("SM01-01BS1010F99"):BigDecimal.ZERO;
            final BigDecimal bqfldzchj = GyUtils.isNotNull(bqZcfzbMap.get("SM01-01BS1020F99"))?bqZcfzbMap.get("SM01-01BS1020F99"):BigDecimal.ZERO;
            final BigDecimal sqldzchj = GyUtils.isNotNull(sqZcfzbMap.get("SM01-01BS1010F99"))?sqZcfzbMap.get("SM01-01BS1010F99"):BigDecimal.ZERO;
            final BigDecimal sqfldzchj = GyUtils.isNotNull(sqZcfzbMap.get("SM01-01BS1020F99"))?sqZcfzbMap.get("SM01-01BS1020F99"):BigDecimal.ZERO;


            final List<ZnsbNssbQhjtjcsssjBsxxmxDTO> bsxxmxList = JsonUtils.toList(QHJTCONSTANTS,ZnsbNssbQhjtjcsssjBsxxmxDTO.class);
            for(ZnsbNssbQhjtjcsssjBsxxmxDTO bsxxmxDTO : bsxxmxList){
                bsxxmxDTO.setDjxh(qhjtDO.getDjxh());
                bsxxmxDTO.setQhjtjcsssjbsuuid(bsuuid);
                if("1".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){
                    bsxxmxDTO.setQhjtjcsssjbnsq(divDec(znsbNssbJksmxDO.getYbtse()));
                    bsxxmxDTO.setQhjtjcsssjsntqsq(divDec(sqZnsbNssbJksmxDO.getYbtse()));
                }else if("2".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(divDec(bqldzchj.add(bqfldzchj)));
                    bsxxmxDTO.setQhjtjcsssjsntqsq(divDec(sqldzchj.add(sqfldzchj)));

                }else if("3".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(divDec(bqldzchj));
                    bsxxmxDTO.setQhjtjcsssjsntqsq(divDec(sqldzchj));

                }else if("4".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqZcfzbMap.get("SM01-01A1403F99"))?
                            divDec(bqZcfzbMap.get("SM01-01A1403F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqZcfzbMap.get("SM01-01A1403F99"))?
                            divDec(sqZcfzbMap.get("SM01-01A1403F99")):BigDecimal.ZERO);

                }else if("5".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){
                    BigDecimal bnsq = BigDecimal.ZERO;
                    BigDecimal sntqsq = BigDecimal.ZERO;
                    bnsq = bnsq
                            .add(GyUtils.isNotNull(bqGdzcMap.get("SM14-021601040000F51"))?
                                    divDec(bqGdzcMap.get("SM14-021601040000F51")):BigDecimal.ZERO)
                            .add(GyUtils.isNotNull(bqGdzcMap.get("SM14-021601020000F51"))?
                                    divDec(bqGdzcMap.get("SM14-021601020000F51")):BigDecimal.ZERO);
                    sntqsq = sntqsq
                            .add(GyUtils.isNotNull(sqGdzcMap.get("SM14-021601040000F51"))?
                                    divDec(sqGdzcMap.get("SM14-021601040000F51")):BigDecimal.ZERO)
                            .add(GyUtils.isNotNull(sqGdzcMap.get("SM14-021601020000F51"))?
                                    divDec(sqGdzcMap.get("SM14-021601020000F51")):BigDecimal.ZERO);
                    //没有对照
                    bsxxmxDTO.setQhjtjcsssjbnsq(bnsq);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(sntqsq);

                }else if("6".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(new BigDecimal(0.34));
                    bsxxmxDTO.setQhjtjcsssjsntqsq(new BigDecimal(0.34));

                }else if("8".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqZcfzbMap.get("SM01-01BS2000F99"))?
                            divDec(bqZcfzbMap.get("SM01-01BS2000F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqZcfzbMap.get("SM01-01BS2000F99"))?
                            divDec(sqZcfzbMap.get("SM01-01BS2000F99")):BigDecimal.ZERO);

                }else if("9".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqZcfzbMap.get("SM01-01BS2010F99"))?
                            divDec(bqZcfzbMap.get("SM01-01BS2010F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqZcfzbMap.get("SM01-01BS2010F99"))?
                            divDec(sqZcfzbMap.get("SM01-01BS2010F99")):BigDecimal.ZERO);

                }else if("10".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){
                    BigDecimal bnsq = GyUtils.isNotNull(bqZcfzbMap.get("SM01-01A2001F99"))?
                            divDec(bqZcfzbMap.get("SM01-01A2001F99")):BigDecimal.ZERO;
                    BigDecimal sntqsq = GyUtils.isNotNull(sqZcfzbMap.get("SM01-01A2001F99"))?
                            divDec(sqZcfzbMap.get("SM01-01A2001F99")):BigDecimal.ZERO;
                    //长期借款没有对照
                    bsxxmxDTO.setQhjtjcsssjbnsq(bnsq);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(sntqsq);

                }else if("11".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqLrbMap.get("SM03-11PL1000F99"))?
                            divDec(bqLrbMap.get("SM03-11PL1000F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqLrbMap.get("SM03-11PL1000F99"))?
                            divDec(sqLrbMap.get("SM03-11PL1000F99")):BigDecimal.ZERO);

                }else if("12".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){
                    BigDecimal bnsq = BigDecimal.ZERO;
                    BigDecimal sntqsq = BigDecimal.ZERO;
                    final String gshStr = "2800,3900,4000,4900,5500,7300,7600,8100";
                    final String[] List = gshStr.split(",");
                    for(String gsh : List){
                        Map<String, BigDecimal> bqJwMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(gsh,bnbq,"SM03","G");
                        Map<String, BigDecimal> sqJwMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(gsh,snbq,"SM03","G");
                        if (GyUtils.isNotNull(bqJwMap.get("SM03-11PL1000F99"))) {
                            bnsq = bnsq.add(bqJwMap.get("SM03-11PL1000F99"));
                        }
                        if (GyUtils.isNotNull(sqJwMap.get("SM03-11PL1000F99"))) {
                            sntqsq = sntqsq.add(sqJwMap.get("SM03-11PL1000F99"));
                        }
                    }
                    bsxxmxDTO.setQhjtjcsssjbnsq(divDec(bnsq));
                    bsxxmxDTO.setQhjtjcsssjsntqsq(divDec(sntqsq));

                }else if("13".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){
                    ZnsbNssbSrmxbDTO srmxbDTO = pzFlMxMapper.selectCkyysr(yearFirstDay,bqLastDay,"2900");
                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(srmxbDTO)?
                            divDec(srmxbDTO.getJe()):BigDecimal.ZERO);
                    srmxbDTO = pzFlMxMapper.selectCkyysr(yearFirstDayOld,bqLastDayOld,"2900");
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(srmxbDTO)?
                            divDec(srmxbDTO.getJe()):BigDecimal.ZERO);

                }else if("14".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqLrbMap.get("SM03-11PL2000F99"))?
                            divDec(bqLrbMap.get("SM03-11PL2000F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqLrbMap.get("SM03-11PL2000F99"))?
                            divDec(sqLrbMap.get("SM03-11PL2000F99")):BigDecimal.ZERO);

                }else if("15".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqXjllbMap.get("SM05-02CF102020F99"))?
                            divDec(bqXjllbMap.get("SM05-02CF102020F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqXjllbMap.get("SM05-02CF102020F99"))?
                            divDec(sqXjllbMap.get("SM05-02CF102020F99")):BigDecimal.ZERO);

                }else if("16".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqLrbMap.get("SM03-11A660301F99"))?
                            divDec(bqLrbMap.get("SM03-11A660301F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqLrbMap.get("SM03-11A660301F99"))?
                            divDec(sqLrbMap.get("SM03-11A660301F99")):BigDecimal.ZERO);

                }else if("17".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqLrbMap.get("SM03-11PL5000F99"))?
                            divDec(bqLrbMap.get("SM03-11PL5000F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqLrbMap.get("SM03-11PL5000F99"))?
                            divDec(sqLrbMap.get("SM03-11PL5000F99")):BigDecimal.ZERO);

                }else if("19".equals(String.valueOf(bsxxmxDTO.getEwbhxh()))){

                    bsxxmxDTO.setQhjtjcsssjbnsq(GyUtils.isNotNull(bqLrbMap.get("SM03-14A6601F99"))?
                            divDec(bqLrbMap.get("SM03-14A6601F99")):BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(GyUtils.isNotNull(sqLrbMap.get("SM03-14A6601F99"))?
                            divDec(sqLrbMap.get("SM03-14A6601F99")):BigDecimal.ZERO);

                }else{
                    bsxxmxDTO.setQhjtjcsssjbnsq(BigDecimal.ZERO);
                    bsxxmxDTO.setQhjtjcsssjsntqsq(BigDecimal.ZERO);
                }
                znsbNssbQhjtjcsssjService.insertBsxxmxQcxx(bsxxmxDTO);
            }
        }
    }


    private BigDecimal divDec(BigDecimal decimal){
        return decimal.divide(DECIMAL);
    }
}
