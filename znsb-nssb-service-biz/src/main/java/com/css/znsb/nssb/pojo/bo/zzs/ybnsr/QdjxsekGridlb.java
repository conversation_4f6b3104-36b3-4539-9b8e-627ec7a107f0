package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 取得进项税额情况
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "qdjxsekGridlb", propOrder = { "fpdmjhmhjkshm", "je", "rzhjhyf", "jxse", "bz" })
@Getter
@Setter
public class QdjxsekGridlb {
    /**
     * 发票代码及号码/缴款书号码
     */
    protected String fpdmjhmhjkshm;

    /**
     * 金额
     */
    protected BigDecimal je;

    /**
     * 认证/稽核月份
     */
    protected String rzhjhyf;

    /**
     * 进项税额
     */
    protected BigDecimal jxse;

    /**
     * 备注
     */
    protected String bz;
}