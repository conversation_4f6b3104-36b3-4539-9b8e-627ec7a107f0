package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bqjxsebGridlbVO", propOrder = { "ewbhxh", "hmc", "qcye", "bqydjse", "bqsjdjse", "bqfse", "qmye", "bqzce", "bqkjjdkjxse", "bqsjjjdkjxse" })
@Getter
@Setter
public class BqjxsebGridlbVO {
    /**
     * 二维表行序号
     */
    protected long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 期初余额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qcye;

    /**
     * 本期应抵减税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqydjse;

    /**
     * 本期实际抵减税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqsjdjse;

    /**
     * 本期发生额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqfse;

    /**
     * 期末余额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmye;

    /**
     * 本期转出额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqzce;

    /**
     * 本期可加计抵扣进项税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqkjjdkjxse;

    /**
     * 本期实际加计抵扣进项税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqsjjjdkjxse;
}