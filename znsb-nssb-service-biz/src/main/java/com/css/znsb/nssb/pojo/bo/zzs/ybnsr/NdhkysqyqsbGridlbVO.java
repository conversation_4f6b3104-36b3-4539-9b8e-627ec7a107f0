package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《年度航空运输企业年度清算表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ndhkysqyqsbGridlbVO", propOrder = { "ce", "ndyjse", "ndqsdynse", "ndxse", "szd", "fzjgmc", "fzjgsbh", "hkysqymc" })
@XmlSeeAlso({ ZzsybnsrsbNdhkysqyqsb.NdhkysqyqsbGrid.NdhkysqyqsbGridlb.class })
@Getter
@Setter
public class NdhkysqyqsbGridlbVO {
    /**
     * 差额
     */
    protected BigDecimal ce;

    /**
     * 年度预缴税额
     */
    protected BigDecimal ndyjse;

    /**
     * 年度清算的应纳税额
     */
    protected BigDecimal ndqsdynse;

    /**
     * 年度销售额
     */
    protected BigDecimal ndxse;

    /**
     * 所在地
     */
    protected String szd;

    /**
     * 分支机构名称
     */
    protected String fzjgmc;

    /**
     * 分支机构识别号
     */
    protected String fzjgsbh;

    /**
     * 航空运输企业名称
     */
    protected String hkysqymc;
}