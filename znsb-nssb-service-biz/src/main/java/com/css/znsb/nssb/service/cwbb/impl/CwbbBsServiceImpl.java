package com.css.znsb.nssb.service.cwbb.impl;

import cn.hutool.core.io.resource.ClassPathResource;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.ServiceException;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.common.util.znsb.MD5Utils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.constants.enums.ZlbsxlDmEnum;
import com.css.znsb.nssb.controller.cwbb.SsfwglUtil;
import com.css.znsb.nssb.mapper.cwbb.*;
import com.css.znsb.nssb.mapper.qcxx.ZnsbNssbQcxxMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwCwbbMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.mapper.yjzt.*;
import com.css.znsb.nssb.pojo.domain.cwbb.*;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwCwbbDO;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.domain.yjtz.*;
import com.css.znsb.nssb.pojo.dto.cwbb.*;
import com.css.znsb.nssb.pojo.dto.cwwb.CwbbBsInitYzxReqDTO;
import com.css.znsb.nssb.pojo.dto.cwwb.CwbbBsInitYzxRespDTO;
import com.css.znsb.nssb.pojo.dto.cwwb.CwbbBsSaveSbtjYzxReqDTO;
import com.css.znsb.nssb.pojo.dto.cwwb.rpa.*;
import com.css.znsb.nssb.pojo.vo.cwbb.*;
import com.css.znsb.nssb.pojo.vo.cwbb.cxbsmxsj.*;
import com.css.znsb.nssb.pojo.vo.cwbb.json.*;
import com.css.znsb.nssb.pojo.vo.cwbb.lqbs.CwbbBsReqVO;
import com.css.znsb.nssb.pojo.vo.cwbb.lqbs.sb.QykjzzfzVO;
import com.css.znsb.nssb.pojo.vo.cwbb.lqbs.sb099.*;
import com.css.znsb.nssb.pojo.vo.cwbb.lqbs.sb100.SB100VO;
import com.css.znsb.nssb.pojo.vo.cwbb.lqbs.sb100.SBCjbmxVO;
import com.css.znsb.nssb.pojo.vo.cwbb.lqbs.zlbs.CwbbZlbssldjNsrxxVO;
import com.css.znsb.nssb.pojo.vo.cwbb.query.CwbbBsInitYzxReqVO;
import com.css.znsb.nssb.pojo.vo.cwbb.query.CwbbBsInitYzxResVO;
import com.css.znsb.nssb.pojo.vo.cwbb.save.CwbbBsSaveSbtjYzxReqVO;
import com.css.znsb.nssb.pojo.vo.cwbb.save.CwbbBsSaveSbtjYzxResVO;
import com.css.znsb.nssb.pojo.vo.cwbb.save.ZlbsbcbzVO;
import com.css.znsb.nssb.pojo.vo.cwbb.sjtq.*;
import com.css.znsb.nssb.pojo.vo.sbrw.ZnsbNssbSbrwCwbbVO;
import com.css.znsb.nssb.service.cwbb.*;
import com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.qysdsbaxx.ZnsbNssbQysdsbaxxService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwCwbbService;
import com.css.znsb.nssb.service.yjzt.*;
import com.css.znsb.nssb.utils.cwbb.ZzppUtil;
import com.css.znsb.tzzx.service.yjtz.ZnsbTzzxQysdsyjtzService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CwbbBsServiceImpl implements CwbbBsService {

    @Resource
    private ZnsbTzzxBpcjysjMapper znsbTzzxBpcjysjMapper;
    @Resource
    private ZnsbNssbQcxxMapper znsbNssbQcxxMapper;
    @Resource
    private CompanyApi companyApi;
    @Resource
    private SbCwbbQykjzzybqyZcfzbyzxService sbCwbbQykjzzybqyZcfzbyzxService;
    @Resource
    private SbCwbbQykjzzybqyZcfzbyzxMapper sbCwbbQykjzzybqyZcfzbyzxMapper;
    @Resource
    private SbCwbbQykjzzybqyLrbyzxService sbCwbbQykjzzybqyLrbyzxService;
    @Resource
    private SbCwbbQykjzzybqyLrbyzxMapper sbCwbbQykjzzybqyLrbyzxMapper;
    @Resource
    private SbCwbbQykjzzybqyXjllbService sbCwbbQykjzzybqyXjllbService;
    @Resource
    private SbCwbbQykjzzybqyXjllbMapper sbCwbbQykjzzybqyXjllbMapper;
    @Resource
    private SbCwbbQykjzzybqySyzqyService sbCwbbQykjzzybqySyzqyService;
    @Resource
    private SbCwbbQykjzzybqySyzqyMapper sbCwbbQykjzzybqySyzqyMapper;
    @Resource
    private SbZlbsQykjzzfzMapper sbZlbsQykjzzfzMapper;
    @Resource
    private SbZlbsQykjzzfzService sbZlbsQykjzzfzService;
    @Resource
    private SbCwbbZlbscjbService sbCwbbZlbscjbService;
    @Resource
    private SbCwbbZlbscjbMapper sbCwbbZlbscjbMapper;


    @Resource
    private ZnsbCwbbQykjzzybqyZcfzbyzxService znsbCwbbQykjzzybqyZcfzbyzxService;
    @Resource
    private ZnsbCwbbQykjzzybqyZcfzbyzxMapper znsbCwbbQykjzzybqyZcfzbyzxMapper;
    @Resource
    private ZnsbCwbbQykjzzybqyLrbyzxService znsbCwbbQykjzzybqyLrbyzxService;
    @Resource
    private ZnsbCwbbQykjzzybqyLrbyzxMapper znsbCwbbQykjzzybqyLrbyzxMapper;
    @Resource
    private ZnsbCwbbQykjzzybqyZcfzbwzxService znsbCwbbQykjzzybqyZcfzbwzxService;
    @Resource
    private ZnsbCwbbQykjzzybqyZcfzbwzxMapper znsbCwbbQykjzzybqyZcfzbwzxMapper;
    @Resource
    private ZnsbCwbbQykjzzybqyLrbwzxService znsbCwbbQykjzzybqyLrbwzxService;
    @Resource
    private ZnsbCwbbQykjzzybqyLrbwzxMapper znsbCwbbQykjzzybqyLrbwzxMapper;
    @Resource
    private ZnsbCwbbQykjzzybqyXjllbService znsbCwbbQykjzzybqyXjllbService;
    @Resource
    private ZnsbCwbbQykjzzybqyXjllbMapper znsbCwbbQykjzzybqyXjllbMapper;
    @Resource
    private ZnsbCwbbQykjzzybqySyzqyService znsbCwbbQykjzzybqySyzqyService;
    @Resource
    private ZnsbCwbbQykjzzybqySyzqyMapper znsbCwbbQykjzzybqySyzqyMapper;
    @Resource
    private ZnsbNssbCqdtfybService znsbNssbCqdtfybService;
    @Resource
    private ZnsbNssbCqdtfybMapper znsbNssbCqdtfybMapper;
    @Resource
    private ZnsbNssbGdzcbService znsbNssbGdzcbService;
    @Resource
    private ZnsbNssbGdzcbMapper znsbNssbGdzcbMapper;
    @Resource
    private ZnsbNssbWxzcbService znsbNssbWxzcbService;
    @Resource
    private ZnsbNssbWxzcbMapper znsbNssbWxzcbMapper;
    @Resource
    private ZnsbCwbbZlbscjbService znsbCwbbZlbscjbService;
    @Resource
    private ZnsbCwbbZlbscjbMapper znsbCwbbZlbscjbMapper;
    @Resource
    private NsrxxApi nsrxxApi;
    @Resource
    private SjjhService sjjhService;
    @Resource
    private ZnsbNssbSbrwCwbbMapper znsbNssbSbrwCwbbMapper;
    @Resource
    private ZnsbNssbQysdsbaxxService qysdsbaxxService;
    @Resource
    private ZnsbNssbSbrwCwbbService znsbNssbSbrwCwbbService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Lazy
    @Resource
    private ZnsbTzzxQysdsyjtzService znsbTzzxQysdsyjtzService;
    @Lazy
    @Resource
    private CwbbBsService cwbbBsService;
    @Resource
    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;

    @Resource
    private ZnsbNssbYsbjgbCwbbMapper znsbNssbYsbjgbCwbbMapper;

    private static final List<String> BBMCLIST = Arrays.asList("SM01", "SM03", "SM05", "SM06", "SM14", "SM15", "SM16");

    private static final List<String> MOUTHLIST = Arrays.asList("12", "01", "02", "03", "04");

    private static final String ZCFZB = "[{\"ewbhxh\":1,\"zcxm_key\":\"SM01-01A1001F99\",\"zcxmmc\":\"货币资金\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1001F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"短期借款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":2,\"zcxm_key\":\"SM01-01A1101F99\",\"zcxmmc\":\"交易性金融资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1101F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"交易性金融负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":3,\"zcxm_key\":null,\"zcxmmc\":\"衍生金融资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"衍生金融负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":4,\"zcxm_key\":null,\"zcxmmc\":\"应收票据\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2201F99\",\"qyxmmc\":\"应付票据\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2201F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":5,\"zcxm_key\":\"SM01-01A1122F99\",\"zcxmmc\":\"应收账款\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1122F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2202F99\",\"qyxmmc\":\"应付账款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2202F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":6,\"zcxm_key\":null,\"zcxmmc\":\"应收款项融资\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"预收款项\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":7,\"zcxm_key\":\"SM01-01A1123F99\",\"zcxmmc\":\"预付款项\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1123F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2204F99\",\"qyxmmc\":\"合同负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2204F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":8,\"zcxm_key\":\"SM01-01BS1011F99\",\"zcxmmc\":\"其他应收款\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02BS1011F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2211F99\",\"qyxmmc\":\"应付职工薪酬\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2211F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":9,\"zcxm_key\":\"SM01-01A1403F99\",\"zcxmmc\":\"存货\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1403F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2221F99\",\"qyxmmc\":\"应交税费\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2221F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":10,\"zcxm_key\":null,\"zcxmmc\":\"合同资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS2011F99\",\"qyxmmc\":\"其他应付款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS2011F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":11,\"zcxm_key\":null,\"zcxmmc\":\"持有待售资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"持有待售负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":12,\"zcxm_key\":\"SM01-01A1472F99\",\"zcxmmc\":\"一年内到期的非流动资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1472F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2242F99\",\"qyxmmc\":\"一年内到期的非流动负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2242F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":13,\"zcxm_key\":\"SM01-01A1124F99\",\"zcxmmc\":\"其他流动资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1124F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2243F99\",\"qyxmmc\":\"其他流动负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2243F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":14,\"zcxm_key\":\"SM01-01BS1010F99\",\"zcxmmc\":\"流动资产合计\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02BS1010F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS2010F99\",\"qyxmmc\":\"流动负债合计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS2010F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":15,\"zcxm_key\":null,\"zcxmmc\":\"债权投资\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"长期借款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":16,\"zcxm_key\":null,\"zcxmmc\":\"其他债权投资\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"应付债券\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":17,\"zcxm_key\":null,\"zcxmmc\":\"长期应收款\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"其中：优先股\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":18,\"zcxm_key\":\"SM01-01A1511F99\",\"zcxmmc\":\"长期股权投资\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1511F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"永续债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":19,\"zcxm_key\":\"SM01-01A1504F99\",\"zcxmmc\":\"其他权益工具投资\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1504F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2702F99\",\"qyxmmc\":\"租赁负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2702F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":20,\"zcxm_key\":null,\"zcxmmc\":\"其他非流动金融资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"长期应付款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":21,\"zcxm_key\":\"SM01-01A1521F99\",\"zcxmmc\":\"投资性房地产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1521F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"预计负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":22,\"zcxm_key\":\"SM01-01A1601F99\",\"zcxmmc\":\"固定资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1601F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2401F99\",\"qyxmmc\":\"递延收益\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2401F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":23,\"zcxm_key\":\"SM01-01A1604F99\",\"zcxmmc\":\"在建工程\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1604F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"递延所得税负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":24,\"zcxm_key\":null,\"zcxmmc\":\"生产性生物资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"其他非流动负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":25,\"zcxm_key\":null,\"zcxmmc\":\"油气资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS2020F99\",\"qyxmmc\":\"非流动负债合计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS2020F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":26,\"zcxm_key\":\"SM01-01A1611F99\",\"zcxmmc\":\"使用权资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1611F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS2000F99\",\"qyxmmc\":\"负债合计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS2000F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":27,\"zcxm_key\":\"SM01-01A1701F99\",\"zcxmmc\":\"无形资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1701F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":null,\"qmye_qy\":null,\"snnmye_qy\":null,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":28,\"zcxm_key\":null,\"zcxmmc\":\"开发支出\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4001F99\",\"qyxmmc\":\"实收资本（或股本）\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4001F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":29,\"zcxm_key\":null,\"zcxmmc\":\"商誉\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"其他权益工具\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":30,\"zcxm_key\":\"SM01-01A1801F99\",\"zcxmmc\":\"长期待摊费用\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1801F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"其中：优先股\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":31,\"zcxm_key\":\"SM01-01A1811F99\",\"zcxmmc\":\"递延所得税资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1811F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"永续债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":32,\"zcxm_key\":\"SM01-01A1812F99\",\"zcxmmc\":\"其他非流动资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1812F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4002F99\",\"qyxmmc\":\"资本公积\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4002F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":33,\"zcxm_key\":\"SM01-01BS1020F99\",\"zcxmmc\":\"非流动资产合计\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02BS1020F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"减：库存股\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":34,\"zcxm_key\":null,\"zcxmmc\":\"资产总计\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4003F99\",\"qyxmmc\":\"其他综合收益\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4003F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":35,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"专项储备\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":36,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4101F99\",\"qyxmmc\":\"盈余公积\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4101F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":37,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4104F99\",\"qyxmmc\":\"未分配利润\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4104F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":38,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS3000F99\",\"qyxmmc\":\"所有者权益（或股东权益）合计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS3000F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":39,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"负债和所有者权益（或股东权益）总计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null}]";

    private static final String ZCFZBWZX = "[{\"ewbhxh\":1,\"zcxm_key\":\"SM01-01A1001F99\",\"zcxmmc\":\"货币资金\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1001F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"短期借款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":2,\"zcxm_key\":null,\"zcxmmc\":\"以公允价值计量且其变动计入当期损益的金融资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"以公允价值计量且其变动计入当期损益的金融负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":3,\"zcxm_key\":null,\"zcxmmc\":\"应收票据\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2201F99\",\"qyxmmc\":\"应付票据\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2201F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":4,\"zcxm_key\":\"SM01-01A1122F99\",\"zcxmmc\":\"应收账款\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1122F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2202F99\",\"qyxmmc\":\"应付账款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2202F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":5,\"zcxm_key\":\"SM01-01A1123F99\",\"zcxmmc\":\"预付款项\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1123F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"预收款项\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":6,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2211F99\",\"qyxmmc\":\"应付职工薪酬\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2211F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":7,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2221F99\",\"qyxmmc\":\"应交税费\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2221F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":8,\"zcxm_key\":\"SM01-01BS1011F99\",\"zcxmmc\":\"其他应收款\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02BS1011F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":null,\"qmye_qy\":null,\"snnmye_qy\":null,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":9,\"zcxm_key\":\"SM01-01A1403F99\",\"zcxmmc\":\"存货\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1403F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":null,\"qmye_qy\":null,\"snnmye_qy\":null,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":10,\"zcxm_key\":\"SM01-01A1472F99\",\"zcxmmc\":\"一年内到期的非流动资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1472F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS2011F99\",\"qyxmmc\":\"其他应付款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS2011F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":11,\"zcxm_key\":\"SM01-01A1124F99\",\"zcxmmc\":\"其他流动资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1124F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2242F99\",\"qyxmmc\":\"一年内到期的非流动负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2242F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":12,\"zcxm_key\":\"SM01-01BS1010F99\",\"zcxmmc\":\"流动资产合计\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02BS1010F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2243F99\",\"qyxmmc\":\"其他流动负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2243F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":13,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS2010F99\",\"qyxmmc\":\"流动负债合计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS2010F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":14,\"zcxm_key\":null,\"zcxmmc\":\"可供出售金融资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":null,\"qmye_qy\":null,\"snnmye_qy\":null,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":15,\"zcxm_key\":null,\"zcxmmc\":\"持有至到期投资\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"长期借款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":16,\"zcxm_key\":null,\"zcxmmc\":\"长期应收款\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"应付债券\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":17,\"zcxm_key\":\"SM01-01A1511F99\",\"zcxmmc\":\"长期股权投资\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1511F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"长期应付款\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":18,\"zcxm_key\":\"SM01-01A1521F99\",\"zcxmmc\":\"投资性房地产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1521F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":null,\"qmye_qy\":null,\"snnmye_qy\":null,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":19,\"zcxm_key\":\"SM01-01A1601F99\",\"zcxmmc\":\"固定资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1601F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"预计负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":20,\"zcxm_key\":\"SM01-01A1604F99\",\"zcxmmc\":\"在建工程\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1604F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"递延所得税负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":21,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"其他非流动负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":22,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS2020F99\",\"qyxmmc\":\"非流动负债合计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS2020F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":23,\"zcxm_key\":null,\"zcxmmc\":\"生产性生物资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS2000F99\",\"qyxmmc\":\"负债合计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS2000F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":24,\"zcxm_key\":null,\"zcxmmc\":\"油气资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":null,\"qmye_qy\":null,\"snnmye_qy\":null,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":25,\"zcxm_key\":\"SM01-01A1701F99\",\"zcxmmc\":\"无形资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1701F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4001F99\",\"qyxmmc\":\"实收资本（或股本）\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4001F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":26,\"zcxm_key\":null,\"zcxmmc\":\"开发支出\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4002F99\",\"qyxmmc\":\"资本公积\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4002F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":27,\"zcxm_key\":null,\"zcxmmc\":\"商誉\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"减：库存股\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":28,\"zcxm_key\":\"SM01-01A1801F99\",\"zcxmmc\":\"长期待摊费用\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1801F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4101F99\",\"qyxmmc\":\"盈余公积\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4101F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":29,\"zcxm_key\":\"SM01-01A1811F99\",\"zcxmmc\":\"递延所得税资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1811F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4104F99\",\"qyxmmc\":\"未分配利润\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4104F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":30,\"zcxm_key\":\"SM01-01A1812F99\",\"zcxmmc\":\"其他非流动资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02A1812F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01BS3000F99\",\"qyxmmc\":\"所有者权益（或股东权益）合计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02BS3000F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":31,\"zcxm_key\":\"SM01-01BS1020F99\",\"zcxmmc\":\"非流动资产合计\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":\"SM01-02BS1020F_Opening\",\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":null,\"qmye_qy\":null,\"snnmye_qy\":null,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":32,\"zcxm_key\":null,\"zcxmmc\":\"资产总计\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"负债和所有者权益（或股东权益）总计\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":33,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A2401F99\",\"qyxmmc\":\"递延收益\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A2401F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":34,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":\"SM01-01A4003F99\",\"qyxmmc\":\"其他综合收益\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":\"SM01-02A4003F_Opening\",\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":35,\"zcxm_key\":null,\"zcxmmc\":\"衍生金融资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"衍生金融负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":36,\"zcxm_key\":null,\"zcxmmc\":\"持有待售资产\",\"qmye_zc\":0.00,\"snnmye_zc\":0.00,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"持有待售负债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":37,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"其中：优先股\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":38,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"永续债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":39,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"其他权益工具\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":40.00,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"其中：优先股\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":41,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"永续债\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null},{\"ewbhxh\":42,\"zcxm_key\":null,\"zcxmmc\":null,\"qmye_zc\":null,\"snnmye_zc\":null,\"byfse_zc_key\":null,\"byfse_zc\":0.00,\"qyxm_key\":null,\"qyxmmc\":\"专项储备\",\"qmye_qy\":0.00,\"snnmye_qy\":0.00,\"byfse_qy_key\":null,\"byfse_qy\":0.00,\"sjblbz\":null}]";

    private static final String LRB = "[{\"ewbhxh\":1,\"hmc_key\":\"SM03-11PL1000F99\",\"hmc\":\"一、营业收入\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL1000F_PER\",\"byfse\":0.00},{\"ewbhxh\":2,\"hmc_key\":\"SM03-11PL2000F99\",\"hmc\":\"减：营业成本\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL2000F_PER\",\"byfse\":0.00},{\"ewbhxh\":3,\"hmc_key\":\"SM03-11A6403F99\",\"hmc\":\"税金及附加\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6403F_PER\",\"byfse\":0.00},{\"ewbhxh\":4,\"hmc_key\":\"SM03-12A6601F99\",\"hmc\":\"销售费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-02A6601F_PER\",\"byfse\":0.00},{\"ewbhxh\":5,\"hmc_key\":\"SM03-13A6601F99\",\"hmc\":\"管理费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-03A6601F_PER\",\"byfse\":0.00},{\"ewbhxh\":6,\"hmc_key\":\"SM03-14A6601F99\",\"hmc\":\"研发费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-04A6601F_PER\",\"byfse\":0.00},{\"ewbhxh\":7,\"hmc_key\":\"SM03-11A6603F99\",\"hmc\":\"财务费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6603F_PER\",\"byfse\":0.00},{\"ewbhxh\":8,\"hmc_key\":\"SM03-11A660301F99\",\"hmc\":\"其中：利息费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A660301F_PER\",\"byfse\":0.00},{\"ewbhxh\":9,\"hmc_key\":\"SM03-11A660302F99\",\"hmc\":\"利息收入\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A660302F_PER\",\"byfse\":0.00},{\"ewbhxh\":10,\"hmc_key\":\"SM03-11A6220F99\",\"hmc\":\"加：其他收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6220F_PER\",\"byfse\":0.00},{\"ewbhxh\":11,\"hmc_key\":\"SM03-11A6111F99\",\"hmc\":\"投资收益（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6111F_PER\",\"byfse\":0.00},{\"ewbhxh\":12,\"hmc_key\":null,\"hmc\":\"其中:对联营企业和合营企业的投资收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":13,\"hmc_key\":null,\"hmc\":\"以摊余成本计量的金融资产终止确认收益（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":14,\"hmc_key\":null,\"hmc\":\"净敞口套期收益（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":15,\"hmc_key\":\"SM03-11A6101F99\",\"hmc\":\"公允价值变动收益（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6101F_PER\",\"byfse\":0.00},{\"ewbhxh\":16,\"hmc_key\":\"SM03-11A6702F99\",\"hmc\":\"信用减值损失（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6702F_PER\",\"byfse\":0.00},{\"ewbhxh\":17,\"hmc_key\":\"SM03-11A6701F99\",\"hmc\":\"资产减值损失（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6701F_PER\",\"byfse\":0.00},{\"ewbhxh\":18,\"hmc_key\":\"SM03-11A6210F99\",\"hmc\":\"资产处置收益（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6210F_PER\",\"byfse\":0.00},{\"ewbhxh\":19,\"hmc_key\":\"SM03-11PL3000F99\",\"hmc\":\"二、营业利润（亏损以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL3000F_PER\",\"byfse\":0.00},{\"ewbhxh\":20,\"hmc_key\":\"SM03-11A6301F99\",\"hmc\":\"加：营业外收入\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6301F_PER\",\"byfse\":0.00},{\"ewbhxh\":21,\"hmc_key\":\"SM03-11A6711F99\",\"hmc\":\"减：营业外支出\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6711F_PER\",\"byfse\":0.00},{\"ewbhxh\":22,\"hmc_key\":\"SM03-11PL4000F99\",\"hmc\":\"三、利润总额（亏损总额以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL4000F_PER\",\"byfse\":0.00},{\"ewbhxh\":23,\"hmc_key\":\"SM03-11A6801F99\",\"hmc\":\"减：所得税费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6801F_PER\",\"byfse\":0.00},{\"ewbhxh\":24,\"hmc_key\":\"SM03-11PL5000F99\",\"hmc\":\"四、净利润（净亏损以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL5000F_PER\",\"byfse\":0.00},{\"ewbhxh\":25,\"hmc_key\":\"SM03-11PL5000F99\",\"hmc\":\"（一）持续经营净利润（净亏损以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL5000F_PER\",\"byfse\":0.00},{\"ewbhxh\":26,\"hmc_key\":null,\"hmc\":\"（二）终止经营净利润（净亏损以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":27,\"hmc_key\":\"SM03-11PL7000F99\",\"hmc\":\"五、其他综合收益的税后净额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL7000F_PER\",\"byfse\":0.00},{\"ewbhxh\":28,\"hmc_key\":null,\"hmc\":\"（一）不能重分类进损益的其他综合收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":29,\"hmc_key\":null,\"hmc\":\"1．重新计量设定受益计划变动额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":30,\"hmc_key\":null,\"hmc\":\"2．权益法下不能转损益的其他综合收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":31,\"hmc_key\":\"SM03-11PL70001030F99\",\"hmc\":\"3．其他权益工具投资公允价值变动\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL70001030F_PER\",\"byfse\":0.00},{\"ewbhxh\":32,\"hmc_key\":null,\"hmc\":\"4．企业自身信用风险公允价值变动\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":33,\"hmc_key\":null,\"hmc\":\"（二）将重分类进损益的其他综合收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":34,\"hmc_key\":null,\"hmc\":\"1．权益法下可转损益的其他综合收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":35,\"hmc_key\":null,\"hmc\":\"2．其他债权投资公允价值变动\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":36,\"hmc_key\":null,\"hmc\":\"3．金融资产重分类计入其他综合收益的金额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":37,\"hmc_key\":null,\"hmc\":\"4．其他债权投资信用减值准备\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":38,\"hmc_key\":null,\"hmc\":\"5．现金流量套期储备\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":39,\"hmc_key\":null,\"hmc\":\"6．外币财务报表折算差额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":40,\"hmc_key\":null,\"hmc\":\"六、综合收益总额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":41,\"hmc_key\":null,\"hmc\":\"（一）基本每股收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":42,\"hmc_key\":null,\"hmc\":\"（二）稀释每股收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00}]";

    private static final String LRBWZX = "[{\"ewbhxh\":1,\"hmc_key\":\"SM03-11PL1000F99\",\"hmc\":\"一、营业收入\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL1000F_PER\",\"byfse\":0.00},{\"ewbhxh\":2,\"hmc_key\":\"SM03-11PL2000F99\",\"hmc\":\"减：营业成本\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL2000F_PER\",\"byfse\":0.00},{\"ewbhxh\":3,\"hmc_key\":\"SM03-11A6403F99\",\"hmc\":\"税金及附加\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6403F_PER\",\"byfse\":0.00},{\"ewbhxh\":4,\"hmc_key\":\"SM03-12A6601F99\",\"hmc\":\"销售费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-02A6601F_PER\",\"byfse\":0.00},{\"ewbhxh\":5,\"hmc_key\":\"SM03-13A6601F99\",\"hmc\":\"管理费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-03A6601F_PER\",\"byfse\":0.00},{\"ewbhxh\":6,\"hmc_key\":null,\"hmc\":\"财务费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":7,\"hmc_key\":\"SM03-11A6701F99\",\"hmc\":\"资产减值损失（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6701F_PER\",\"byfse\":0.00},{\"ewbhxh\":8,\"hmc_key\":null,\"hmc\":null,\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":9,\"hmc_key\":null,\"hmc\":null,\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":10,\"hmc_key\":null,\"hmc\":null,\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":11,\"hmc_key\":\"SM03-11PL3000F99\",\"hmc\":\"二、营业利润（亏损以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL3000F_PER\",\"byfse\":0.00},{\"ewbhxh\":12,\"hmc_key\":\"SM03-11A6301F99\",\"hmc\":\"加：营业外收入\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6301F_PER\",\"byfse\":0.00},{\"ewbhxh\":13,\"hmc_key\":\"SM03-11A6711F99\",\"hmc\":\"减：营业外支出\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6711F_PER\",\"byfse\":0.00},{\"ewbhxh\":14,\"hmc_key\":null,\"hmc\":null,\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":15,\"hmc_key\":\"SM03-11PL4000F99\",\"hmc\":\"三、利润总额（亏损总额以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL4000F_PER\",\"byfse\":0.00},{\"ewbhxh\":16,\"hmc_key\":\"SM03-11A6801F99\",\"hmc\":\"减：所得税费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6801F_PER\",\"byfse\":0.00},{\"ewbhxh\":17,\"hmc_key\":\"SM03-11PL5000F99\",\"hmc\":\"四、净利润（净亏损以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL5000F_PER\",\"byfse\":0.00},{\"ewbhxh\":18,\"hmc_key\":null,\"hmc\":null,\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":19,\"hmc_key\":null,\"hmc\":\"（一）基本每股收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":20,\"hmc_key\":null,\"hmc\":\"（二）稀释每股收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":21,\"hmc_key\":null,\"hmc\":null,\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":22,\"hmc_key\":\"SM03-11PL7000F99\",\"hmc\":\"五、其他综合收益的税后净额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL7000F_PER\",\"byfse\":0.00},{\"ewbhxh\":23,\"hmc_key\":null,\"hmc\":\"（一）不能重分类进损益的其他综合收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":24,\"hmc_key\":null,\"hmc\":\"1．重新计量设定受益计划变动额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":25,\"hmc_key\":null,\"hmc\":\"2．权益法下不能转损益的其他综合收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":26,\"hmc_key\":null,\"hmc\":\"（二）将重分类进损益的其他综合收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":27,\"hmc_key\":null,\"hmc\":\"1. 权益法下可转损益的其他综合收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":28,\"hmc_key\":null,\"hmc\":\"2．可供出售金融资产公允价值变动损益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":29,\"hmc_key\":null,\"hmc\":\"3．持有至到期投资重分类为可供出售金融资产损益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":30,\"hmc_key\":null,\"hmc\":\"4．现金流量套期损益的有效部分\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":31,\"hmc_key\":null,\"hmc\":\"5．外币财务报表折算差额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":32,\"hmc_key\":null,\"hmc\":\"六、综合收益总额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":33,\"hmc_key\":\"SM03-11A6210F99\",\"hmc\":\"资产处置收益（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6210F_PER\",\"byfse\":0.00},{\"ewbhxh\":34,\"hmc_key\":null,\"hmc\":null,\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":35,\"hmc_key\":\"SM03-11PL5000F99\",\"hmc\":\"（一）持续经营净利润（净亏损以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01PL5000F_PER\",\"byfse\":0.00},{\"ewbhxh\":36,\"hmc_key\":null,\"hmc\":\"（二）终止经营净利润（净亏损以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":37,\"hmc_key\":\"SM03-14A6601F99\",\"hmc\":\"研发费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-04A6601F_PER\",\"byfse\":0.00},{\"ewbhxh\":38,\"hmc_key\":\"SM03-11A660301F99\",\"hmc\":\"其中：利息费用\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A660301F_PER\",\"byfse\":0.00},{\"ewbhxh\":39,\"hmc_key\":\"SM03-11A660302F99\",\"hmc\":\"利息收入\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A660302F_PER\",\"byfse\":0.00},{\"ewbhxh\":40,\"hmc_key\":\"SM03-11A6220F99\",\"hmc\":\"加：其他收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6220F_PER\",\"byfse\":0.00},{\"ewbhxh\":41,\"hmc_key\":\"SM03-11A6111F99\",\"hmc\":\"投资收益（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6111F_PER\",\"byfse\":0.00},{\"ewbhxh\":42,\"hmc_key\":null,\"hmc\":\"其中:对联营企业和合营企业的投资收益\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":43,\"hmc_key\":\"SM03-11A6101F99\",\"hmc\":\"公允价值变动收益（损失以“-”号填列）\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM03-01A6101F_PER\",\"byfse\":0.00}]";

    private static final String XJLB = "[{\"ewbhxh\":1,\"hmc_key\":\"SM05-02CF101010F99\",\"hmc\":\"销售商品、提供劳务收到的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF101010F_PER\",\"byfse\":0.00},{\"ewbhxh\":2,\"hmc_key\":\"SM05-02CF101020F99\",\"hmc\":\"收到的税费返还\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF101020F_PER\",\"byfse\":0.00},{\"ewbhxh\":3,\"hmc_key\":\"SM05-02CF101030F99\",\"hmc\":\"收到其他与经营活动有关的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF101030F_PER\",\"byfse\":0.00},{\"ewbhxh\":4,\"hmc_key\":\"SM05-02CF101000F99\",\"hmc\":\"经营活动现金流入小计\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF101000F_PER\",\"byfse\":0.00},{\"ewbhxh\":5,\"hmc_key\":\"SM05-02CF102010F99\",\"hmc\":\"购买商品、接受劳务支付的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF102010F_PER\",\"byfse\":0.00},{\"ewbhxh\":6,\"hmc_key\":\"SM05-02CF102020F99\",\"hmc\":\"支付给职工以及为职工支付的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF102020F_PER\",\"byfse\":0.00},{\"ewbhxh\":7,\"hmc_key\":\"SM05-02CF102030F99\",\"hmc\":\"支付的各项税费\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF102030F_PER\",\"byfse\":0.00},{\"ewbhxh\":8,\"hmc_key\":\"SM05-02CF102040F99\",\"hmc\":\"支付其他与经营活动有关的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF102040F_PER\",\"byfse\":0.00},{\"ewbhxh\":9,\"hmc_key\":\"SM05-02CF102000F99\",\"hmc\":\"经营活动现金流出小计\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF102000F_PER\",\"byfse\":0.00},{\"ewbhxh\":10,\"hmc_key\":null,\"hmc\":\"经营活动产生的现金流量净额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":11,\"hmc_key\":\"SM05-02CF201010F99\",\"hmc\":\"收回投资收到的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF201010F_PER\",\"byfse\":0.00},{\"ewbhxh\":12,\"hmc_key\":\"SM05-02CF201020F99\",\"hmc\":\"取得投资收益收到的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF201020F_PER\",\"byfse\":0.00},{\"ewbhxh\":13,\"hmc_key\":\"SM05-02CF201030F99\",\"hmc\":\"处置固定资产、无形资产和其他长期资产收回的现金净额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF201030F_PER\",\"byfse\":0.00},{\"ewbhxh\":14,\"hmc_key\":null,\"hmc\":\"处置子公司及其他营业单位收到的现金净额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":15,\"hmc_key\":\"SM05-02CF201050F99\",\"hmc\":\"收到其他与投资活动有关的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF201050F_PER\",\"byfse\":0.00},{\"ewbhxh\":16,\"hmc_key\":\"SM05-02CF201000F99\",\"hmc\":\"投资活动现金流入小计\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF201000F_PER\",\"byfse\":0.00},{\"ewbhxh\":17,\"hmc_key\":\"SM05-02CF202010F99\",\"hmc\":\"购建固定资产、无形资产和其他长期资产支付的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF202010F_PER\",\"byfse\":0.00},{\"ewbhxh\":18,\"hmc_key\":\"SM05-02CF202020F99\",\"hmc\":\"投资支付的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF202020F_PER\",\"byfse\":0.00},{\"ewbhxh\":19,\"hmc_key\":null,\"hmc\":\"取得子公司及其他营业单位支付的现金净额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":20,\"hmc_key\":\"SM05-02CF202040F99\",\"hmc\":\"支付其他与投资活动有关的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF202040F_PER\",\"byfse\":0.00},{\"ewbhxh\":21,\"hmc_key\":\"SM05-02CF202000F99\",\"hmc\":\"投资活动现金流出小计\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF202000F_PER\",\"byfse\":0.00},{\"ewbhxh\":22,\"hmc_key\":null,\"hmc\":\"投资活动产生的现金流量净额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":23,\"hmc_key\":null,\"hmc\":\"吸收投资收到的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":24,\"hmc_key\":null,\"hmc\":\"取得借款收到的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":25,\"hmc_key\":\"SM05-02CF301030F99\",\"hmc\":\"收到其他与筹资活动有关的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF301030F_PER\",\"byfse\":0.00},{\"ewbhxh\":26,\"hmc_key\":\"SM05-02CF301000F99\",\"hmc\":\"筹资活动现金流入小计\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF301000F_PER\",\"byfse\":0.00},{\"ewbhxh\":27,\"hmc_key\":null,\"hmc\":\"偿还债务支付的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":28,\"hmc_key\":\"SM05-02CF302020F99\",\"hmc\":\"分配股利、利润或偿付利息支付的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF302020F_PER\",\"byfse\":0.00},{\"ewbhxh\":29,\"hmc_key\":\"SM05-02CF302030F99\",\"hmc\":\"支付其他与筹资活动有关的现金\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF302030F_PER\",\"byfse\":0.00},{\"ewbhxh\":30,\"hmc_key\":\"SM05-02CF302000F99\",\"hmc\":\"筹资活动现金流出小计\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF302000F_PER\",\"byfse\":0.00},{\"ewbhxh\":31,\"hmc_key\":null,\"hmc\":\"筹资活动产生的现金流量净额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":32,\"hmc_key\":null,\"hmc\":\"四、汇率变动对现金及现金等价物的影响\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":null,\"byfse\":0.00},{\"ewbhxh\":33,\"hmc_key\":\"SM05-02CF600000F99\",\"hmc\":\"五、现金及现金等价物净增加额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF600000F_PER\",\"byfse\":0.00},{\"ewbhxh\":34,\"hmc_key\":\"SM05-02CF700000F99\",\"hmc\":\"加：期初现金及现金等价物余额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF700000F_PER\",\"byfse\":0.00},{\"ewbhxh\":35,\"hmc_key\":\"SM05-02CF800000F99\",\"hmc\":\"六、期末现金及现金等价物余额\",\"bqje\":0.00,\"sqje_1\":0.00,\"byfse_key\":\"SM05-01CF800000F_PER\",\"byfse\":0.00}]";

    private static final String SYZQYB = "[{\"ewbhxh\":1,\"hmc\":\"一、上年年末余额\",\"bnsszbhgb_key\":\"SM06-014001000000F00\",\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":\"SM06-014002010000F00\",\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":\"SM06-014003019900F00\",\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":\"SM06-014101010000F00\",\"bnyygj\":0.00,\"bnwfply_key\":\"SM06-014104010000F00\",\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":\"SM06-014001000000F00\",\"snsszbhgb\":0.00,\"snzbgj_key\":\"SM06-014002010000F00\",\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":\"SM06-014101010000F00\",\"snyygj\":0.00,\"snwfply_key\":\"SM06-014104010000F00\",\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":\"SM06-014003019900F00\",\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":2,\"hmc\":\"加：会计政策变更\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":3,\"hmc\":\"前期差错更正\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":4,\"hmc\":\"二、本年年初余额\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":5,\"hmc\":\"三、本年增减变动金额（减少以“-”号填列）\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":6,\"hmc\":\"（一）综合收益总额\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":\"SM06-064003019900F81\",\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":\"SM06-064104010000F81\",\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":\"SM06-064104010000F81\",\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":\"SM06-064003019900F81\",\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":7,\"hmc\":null},{\"ewbhxh\":8,\"hmc\":null},{\"ewbhxh\":9,\"hmc\":null},{\"ewbhxh\":10,\"hmc\":null},{\"ewbhxh\":11,\"hmc\":null},{\"ewbhxh\":12,\"hmc\":null},{\"ewbhxh\":13,\"hmc\":\"（二）所有者投入和减少资本\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":14,\"hmc\":\"1．所有者投入的普通股\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":15,\"hmc\":\"3．股份支付计入所有者权益的金额\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":\"SM06-094002010000F84\",\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":\"SM06-094002010000F84\",\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":16,\"hmc\":\"4．其他\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":17,\"hmc\":\"（三）利润分配\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":18,\"hmc\":\"1．提取盈余公积\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":\"SM06-114101010000F86\",\"bnyygj\":0.00,\"bnwfply_key\":\"SM06-114104010000F86\",\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":\"SM06-114101010000F86\",\"snyygj\":0.00,\"snwfply_key\":\"SM06-114104010000F86\",\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":19,\"hmc\":\"2．对所有者（或股东）的分配\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":\"SM06-124104010000F87\",\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":\"SM06-124104010000F87\",\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":20,\"hmc\":\"3．其他\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":21,\"hmc\":\"（四）所有者权益内部结转\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":22,\"hmc\":\"1．资本公积转增资本（或股本）\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":23,\"hmc\":\"2．盈余公积转增资本（或股本）\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":24,\"hmc\":\"3．盈余公积弥补亏损\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":25,\"hmc\":\"6．其他\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":26,\"hmc\":\"四、本年年末余额\",\"bnsszbhgb_key\":\"SM06-204001000000F99\",\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":\"SM06-204002010000F99\",\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":\"SM06-204003019900F99\",\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":\"SM06-204101010000F99\",\"bnyygj\":0.00,\"bnwfply_key\":\"SM06-204104010000F99\",\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":\"SM06-204001000000F99\",\"snsszbhgb\":0.00,\"snzbgj_key\":\"SM06-204002010000F99\",\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":\"SM06-204101010000F99\",\"snyygj\":0.00,\"snwfply_key\":\"SM06-204104010000F99\",\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":\"SM06-204003019900F99\",\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":27,\"hmc\":\"其他\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":28,\"hmc\":\"2．其他权益工具持有者投入资本\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":29,\"hmc\":\"4．设定收益计划变动额结转留存收益\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":null,\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":null,\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00},{\"ewbhxh\":30,\"hmc\":\"5．其他综合收益结转留存收益\",\"bnsszbhgb_key\":null,\"bnsszbhgb\":0.00,\"qtqygjyxg_1_key\":null,\"qtqygjyxg_1\":0.00,\"qtqygjyxz_1_key\":null,\"qtqygjyxz_1\":0.00,\"qtqygjqt_1_key\":null,\"qtqygjqt_1\":0.00,\"bnzbgj_key\":null,\"bnzbgj\":0.00,\"bnjkcg_key\":null,\"bnjkcg\":0.00,\"bnqtzhsy_key\":null,\"bnqtzhsy\":0.00,\"bnzxcb_key\":null,\"bnzxcb\":0.00,\"bnyygj_key\":null,\"bnyygj\":0.00,\"bnwfply_key\":\"SM06-184104010000F93\",\"bnwfply\":0.00,\"bnsyzqyhj_key\":null,\"bnsyzqyhj\":0.00,\"snsszbhgb_key\":null,\"snsszbhgb\":0.00,\"snzbgj_key\":null,\"snzbgj\":0.00,\"snjkcg_key\":null,\"snjkcg\":0.00,\"snzxcb_key\":null,\"snzxcb\":0.00,\"snyygj_key\":null,\"snyygj\":0.00,\"snwfply_key\":\"SM06-184104010000F93\",\"snwfply\":0.00,\"snsyzqyhj_key\":null,\"snsyzqyhj\":0.00,\"snqtzhsy_key\":null,\"snqtzhsy\":0.00,\"qtqygjyxg_2_key\":null,\"qtqygjyxg_2\":0.00,\"qtqygjyxz_2_key\":null,\"qtqygjyxz_2\":0.00,\"qtqygjqt_2_key\":null,\"qtqygjqt_2\":0.00,\"sjblbz\":0.00}]";

    private static final String GDZC = "[{\"ewbhxh\":\"1\",\"xmmc\":\"账面原值-期初余额\",\"fwjjz_key\":\"SM14-011601010000F_Opening\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-011601020000F_Opening\",\"dzsb\":0.00,\"jqjxsb_key\":\"SM14-011601030000F_Opening\",\"jqjxsb\":0.00,\"yssb_key\":\"SM14-011601040000F_Opening\",\"yssb\":0.00,\"gqjjj_key\":\"SM14-011601050000F_Opening\",\"gqjjj\":0.00},{\"ewbhxh\":\"2\",\"xmmc\":\"账面原值-本年增加-购置或计提\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-021601020000F51\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"3\",\"xmmc\":\"账面原值-本年增加-在建工程转入\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-031601020000F53\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"4\",\"xmmc\":\"账面原值-本年增加-自投资性房地产原值转入\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"5\",\"xmmc\":\"账面原值-本年增加-公司内部调拨\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"6\",\"xmmc\":\"账面原值-本年增加-合并增加\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"7\",\"xmmc\":\"账面原值-本年减少-处置或报废\",\"fwjjz_key\":\"SM14-071601010000F57\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-071601020000F57\",\"dzsb\":0.00,\"jqjxsb_key\":\"SM14-071601030000F57\",\"jqjxsb\":0.00,\"yssb_key\":\"SM14-071601040000F57\",\"yssb\":0.00,\"gqjjj_key\":\"SM14-071601050000F57\",\"gqjjj\":0.00},{\"ewbhxh\":\"8\",\"xmmc\":\"账面原值-本年减少-转出至投资性房地产原值\",\"fwjjz_key\":\"SM14-081601010000F58\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"9\",\"xmmc\":\"账面原值-本年减少-合并减少\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"10\",\"xmmc\":\"账面原值-外币报表折算差\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"11\",\"xmmc\":\"账面原值-期末余额\",\"fwjjz_key\":\"SM14-111601010000F99\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-111601020000F99\",\"dzsb\":0.00,\"jqjxsb_key\":\"SM14-111601030000F99\",\"jqjxsb\":0.00,\"yssb_key\":\"SM14-111601040000F99\",\"yssb\":0.00,\"gqjjj_key\":\"SM14-111601050000F99\",\"gqjjj\":0.00},{\"ewbhxh\":\"12\",\"xmmc\":\"累计折旧-期初余额\",\"fwjjz_key\":\"SM14-121602010000F_Opening\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-121602020000F_Opening\",\"dzsb\":0.00,\"jqjxsb_key\":\"SM14-121602030000F_Opening\",\"jqjxsb\":0.00,\"yssb_key\":\"SM14-121602040000F_Opening\",\"yssb\":0.00,\"gqjjj_key\":\"SM14-121602050000F_Opening\",\"gqjjj\":0.00},{\"ewbhxh\":\"13\",\"xmmc\":\"累计折旧-本年增加-购置或计提\",\"fwjjz_key\":\"SM14-131602010000F51\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-131602020000F51\",\"dzsb\":0.00,\"jqjxsb_key\":\"SM14-131602030000F51\",\"jqjxsb\":0.00,\"yssb_key\":\"SM14-131602040000F51\",\"yssb\":0.00,\"gqjjj_key\":\"SM14-131602050000F51\",\"gqjjj\":0.00},{\"ewbhxh\":\"14\",\"xmmc\":\"累计折旧-本年增加-公司内部调拨\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"15\",\"xmmc\":\"累计折旧-本年增加-自投资性房地产累计折旧转入\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"16\",\"xmmc\":\"累计折旧-本年增加-合并增加\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"17\",\"xmmc\":\"累计折旧-本年减少-处置或报废\",\"fwjjz_key\":\"SM14-171602010000F57\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-171602020000F57\",\"dzsb\":0.00,\"jqjxsb_key\":\"SM14-171602030000F57\",\"jqjxsb\":0.00,\"yssb_key\":\"SM14-171602040000F57\",\"yssb\":0.00,\"gqjjj_key\":\"SM14-171602050000F57\",\"gqjjj\":0.00},{\"ewbhxh\":\"18\",\"xmmc\":\"累计折旧-本年减少-转出至投资性房地产累计折旧\",\"fwjjz_key\":\"SM14-181602010000F58\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"19\",\"xmmc\":\"累计折旧-本年减少-合并减少\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"20\",\"xmmc\":\"累计折旧-外币报表折算差\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"21\",\"xmmc\":\"累计折旧-期末余额\",\"fwjjz_key\":\"SM14-211602010000F99\",\"fwjjz\":0.00,\"dzsb_key\":\"SM14-211602020000F99\",\"dzsb\":0.00,\"jqjxsb_key\":\"SM14-211602030000F99\",\"jqjxsb\":0.00,\"yssb_key\":\"SM14-211602040000F99\",\"yssb\":0.00,\"gqjjj_key\":\"SM14-211602050000F99\",\"gqjjj\":0.00},{\"ewbhxh\":\"22\",\"xmmc\":\"减值准备-期初余额\",\"fwjjz_key\":\"SM14-221603010000F_Opening\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"23\",\"xmmc\":\"减值准备-本年增加-购置或计提\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"24\",\"xmmc\":\"减值准备-本年增加-公司内部调拨\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"25\",\"xmmc\":\"减值准备-本年增加-自投资性房地产减值准备转入\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"26\",\"xmmc\":\"减值准备-本年增加-合并增加\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"27\",\"xmmc\":\"减值准备-本年减少-处置或报废\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"28\",\"xmmc\":\"减值准备-本年减少-转出至投资性房地产减值准备\",\"fwjjz_key\":\"SM14-281603010000F58\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"29\",\"xmmc\":\"减值准备-本年减少-合并减少\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"30\",\"xmmc\":\"减值准备-外币报表折算差\",\"fwjjz_key\":\"\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00},{\"ewbhxh\":\"31\",\"xmmc\":\"减值准备-期末余额\",\"fwjjz_key\":\"SM14-311603010000F99\",\"fwjjz\":0.00,\"dzsb_key\":\"\",\"dzsb\":0.00,\"jqjxsb_key\":\"\",\"jqjxsb\":0.00,\"yssb_key\":\"\",\"yssb\":0.00,\"gqjjj_key\":\"\",\"gqjjj\":0.00}]";

    private static final String WXZC = "[{\"ewbhxh\":\"1\",\"xmmc\":\"账面原值-期初余额\",\"tdsyq_key\":\"SM15-011701040000F_Opening\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"SM15-011701030000F_Opening\",\"rj\":0.00,\"sbq_key\":\"SM15-011701020000F_Opening\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"2\",\"xmmc\":\"账面原值-本年增加-购置或计提\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"SM15-021701030000F51\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"3\",\"xmmc\":\"账面原值-本年增加-在建工程转入\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"SM15-031701030000F53\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"4\",\"xmmc\":\"账面原值-本年增加-公司内部调拨\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"5\",\"xmmc\":\"账面原值-本年增加-合并增加\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"6\",\"xmmc\":\"账面原值-本年减少-处置或报废\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"7\",\"xmmc\":\"账面原值-本年减少-合并减少\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"8\",\"xmmc\":\"账面原值-外币报表折算差\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"9\",\"xmmc\":\"账面原值-期末余额\",\"tdsyq_key\":\"SM15-091701040000F99\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"SM15-091701030000F99\",\"rj\":0.00,\"sbq_key\":\"SM15-091701020000F99\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"10\",\"xmmc\":\"累计摊销-期初余额\",\"tdsyq_key\":\"SM15-101702040000F_Opening\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"SM15-101702030000F_Opening\",\"rj\":0.00,\"sbq_key\":\"SM15-101702020000F_Opening\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"11\",\"xmmc\":\"累计摊销-本年增加-购置或计提\",\"tdsyq_key\":\"SM15-111702040000F51\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"SM15-111702030000F51\",\"rj\":0.00,\"sbq_key\":\"SM15-111702020000F51\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"12\",\"xmmc\":\"累计摊销-本年增加-公司内部调拨\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"13\",\"xmmc\":\"累计摊销-本年增加-合并增加\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"14\",\"xmmc\":\"累计摊销-本年减少-处置或报废\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"15\",\"xmmc\":\"累计摊销-本年减少-合并减少\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"16\",\"xmmc\":\"累计摊销-外币报表折算差\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"17\",\"xmmc\":\"累计摊销-期末余额\",\"tdsyq_key\":\"SM15-171702040000F99\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"SM15-171702030000F99\",\"rj\":0.00,\"sbq_key\":\"SM15-171702020000F99\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"18\",\"xmmc\":\"减值准备-期初余额\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"19\",\"xmmc\":\"减值准备-本年增加-购置或计提\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"20\",\"xmmc\":\"减值准备-本年增加-公司内部调拨\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"21\",\"xmmc\":\"减值准备-本年增加-合并增加\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"22\",\"xmmc\":\"减值准备-本年减少-处置或报废\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"23\",\"xmmc\":\"减值准备-本年减少-合并减少\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"24\",\"xmmc\":\"减值准备-外币报表折算差\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00},{\"ewbhxh\":\"25\",\"xmmc\":\"减值准备-期末余额\",\"tdsyq_key\":\"\",\"tdsyq\":0.00,\"zlq_key\":\"\",\"zlq\":0.00,\"fzljs_key\":\"\",\"fzljs\":0.00,\"rj_key\":\"\",\"rj\":0.00,\"sbq_key\":\"\",\"sbq\":0.00,\"dpzlq_key\":\"\",\"dpzlq\":0.00,\"sjtz_key\":\"\",\"sjtz\":0.00}]";

    private static final String CQDTFY = "[{\"ewbhxh\":\"1\",\"xmmc\":\"装修费\",\"qczmye_key\":\"SM16-011801010100F_Opening\",\"qczmye\":0.00,\"bqzje_key\":\"SM16-021801010100F11\",\"bqzje\":0.00,\"bqtx_key\":\"SM16-031801010100F21\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"SM16-071801010100F99\",\"qmzmye\":0.00},{\"ewbhxh\":\"2\",\"xmmc\":\"广告费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"3\",\"xmmc\":\"福利费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"4\",\"xmmc\":\"租赁费-办公室租赁费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"5\",\"xmmc\":\"租赁费-店铺租赁费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"6\",\"xmmc\":\"租赁费-仓库租赁费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"7\",\"xmmc\":\"租赁费-场地租赁费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"8\",\"xmmc\":\"租赁费-设备租赁费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"9\",\"xmmc\":\"店铺物业管理费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"10\",\"xmmc\":\"长期待摊费用-服务费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"11\",\"xmmc\":\"长期待摊费用-道具辅料陈列费\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"12\",\"xmmc\":\"长期待摊费用-原值-审计调整\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00},{\"ewbhxh\":\"13\",\"xmmc\":\"长期待摊费用-累计摊销-审计调整\",\"qczmye_key\":\"\",\"qczmye\":0.00,\"bqzje_key\":\"\",\"bqzje\":0.00,\"bqtx_key\":\"\",\"bqtx\":0.00,\"wbbbzsc_key\":\"\",\"wbbbzsc\":0.00,\"hbzj_1_key\":\"\",\"hbzj_1\":0.00,\"hbjs_1_key\":\"\",\"hbjs_1\":0.00,\"qmzmye_key\":\"\",\"qmzmye\":0.00}]";

    @Async
    @Override
    public void cwbbSjtq(String sbny, String qydmz, String djxh, String key) {
        try {
            this.initDataFormBpc(sbny, qydmz, djxh);
        } catch (Exception e) {
            log.error("财务报表提起数据异常报错信息为---->{}", e.getMessage());
        } finally {
            stringRedisTemplate.delete(key);
        }

    }

    @Override
    public void initDataFormBpc(String sbny, String qydmz, String djxh) {
        List<CompanyInfoResDTO> companyList = companyApi.getAllCompanyInfo().getData();
        final String jwqyJson = CacheUtils.getXtcs("JWQYXX");
        if (GyUtils.isNotNull(jwqyJson)) {
            final List<Map<String, Object>> jwqyList = JsonUtils.toMapList(jwqyJson);
            for (Map<String, Object> jwqy : jwqyList) {
                CompanyInfoResDTO company = new CompanyInfoResDTO();
                company.setJgmc(String.valueOf(jwqy.get("jgmc")));
                company.setQydmz(String.valueOf(jwqy.get("qydmz")));
                company.setDjxh("");
                companyList.add(company);
            }

        }
        if (GyUtils.isNull(sbny)) {
            sbny = DateUtils.dateToString(DateUtils.addDate(new Date(), 2, -1), 17);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(sbny);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String ysbsjSbny = DateUtils.dateToString(DateUtils.addDate(date, 2, 1), 17);
        if (GyUtils.isNull(qydmz)) {
            for (CompanyInfoResDTO company : companyList) {
                qydmz = company.getQydmz();
                djxh = company.getDjxh();
                try {
                    //处理台账数据
                    cwbbBsService.cwbbsjjg(sbny, qydmz, djxh);
                } catch (Exception e) {
                    log.error("从BPC加工数据异常，qydmz->{},sbny->{}异常信息为---->{}", sbny, qydmz, e.getMessage());
                }

                try {

                    //处理预申报数据
                    cwbbBsService.ysbsjJg(ysbsjSbny, djxh);
                } catch (Exception e) {
                    log.error("从BPC加工数据生成预申报数据，qydmz->{},sbny->{}异常信息为---->{}", sbny, qydmz, e.getMessage());
                }
            }
        } else {
            try {
                cwbbBsService.cwbbsjjg(sbny, qydmz, djxh);
            } catch (Exception e) {
                log.error("从BPC加工数据异常，qydmz->{},ysbsjSbny->{}异常信息为---->{}", ysbsjSbny, qydmz, e.getMessage());
            }
            try {
                //处理预申报数据
                cwbbBsService.ysbsjJg(ysbsjSbny, djxh);
            } catch (Exception e) {
                log.error("从BPC加工数据生成预申报数据，qydmz->{},ysbsjSbny->{}异常信息为---->{}", ysbsjSbny, qydmz, e.getMessage());
            }
        }
    }

    @Override
    public void sjlqFromLq(String sbny, String djxh) {
        List<ZnsbNssbSbrwCwbbDO> sbrwList = znsbNssbSbrwCwbbMapper.queryBbxx(sbny, djxh);
        if (GyUtils.isNotNull(sbrwList)) {
            for (ZnsbNssbSbrwCwbbDO sbrw : sbrwList) {
                try {
                    ZnsbNssbSbrwCwbbVO sbrwCwbbVO = BeanUtils.toBean(sbrw, ZnsbNssbSbrwCwbbVO.class);
                    sbrwCwbbVO.setSkssqq(DateUtils.toDate(sbrw.getSkssqq()));
                    sbrwCwbbVO.setSkssqz(DateUtils.toDate(sbrw.getSkssqz()));
                    cwbbPlsjsx(sbrwCwbbVO);
                } catch (Exception e) {
                    log.error("从乐企提取数据异常，djxh->{},skssqq->{},skssqz->{},异常信息为---->{}", sbrw.getDjxh(), sbrw.getSkssqq(), sbrw.getSkssqz(), e.getMessage());
                }
            }
        }
    }

    @Override
    public void ysbsjJg(String sbny, String djxh) {
        final List<ZnsbNssbSbrwDO> sbrwDOList = znsbNssbSbrwMapper.queryCwbbDsbrwBySbny(sbny, djxh);
        // 如果任务列表为空，则直接返回
        if (GyUtils.isNull(sbrwDOList)) {
            return;
        }
        // 遍历待申报任务列表
        for (ZnsbNssbSbrwDO sbrwDO : sbrwDOList) {
            try {
                this.clYsbsj(sbrwDO);
            } catch (Exception e) {
                log.error("财务报表预申报数据加工失败失败djxh--->{},sbrwuuid---->{},原因为---->{}", sbrwDO.getDjxh(), sbrwDO.getSbrwuuid(), e.getMessage());
                log.error("财务报表预申报数据加工失败失败djxh--->{},sbrwuuid---->{},完整堆栈为---->{}", JsonUtils.toJson(e.getStackTrace()));
            }

        }
    }

    private void clYsbsj(ZnsbNssbSbrwDO sbrwDO) {
        final String djxh = sbrwDO.getDjxh();
        final String nsrsbh = sbrwDO.getNsrsbh();
        final String nsrmc = sbrwDO.getNsrmc();
        final String sssqQ = DateUtils.dateToString(sbrwDO.getSkssqq(), 3);
        final String sssqZ = DateUtils.dateToString(sbrwDO.getSkssqz(), 3);
        final String nsqxDm = sbrwDO.getNsqxDm();
        final String zxbz = "yzx";
        final String sjqzbz = "0";
        final String xzqhszDm = sbrwDO.getXzqhszDm();
        final String sbrwuuid = sbrwDO.getSbrwuuid();
        CwbbBsInitYzxReqVO reqVO = new CwbbBsInitYzxReqVO();
        reqVO.setDjxh(djxh);
        reqVO.setNsrsbh(nsrsbh);
        reqVO.setNsrmc(nsrmc);
        reqVO.setSssqQ(sssqQ);
        reqVO.setSssqZ(sssqZ);
        reqVO.setNsqxDm(nsqxDm);
        reqVO.setZxbz(zxbz);
        reqVO.setSjqzbz(sjqzbz);
        CwbbBsInitYzxResVO resVO = this.initData(reqVO);
        CwbbBsJbxxVO cwbbBsJbxxVO = resVO.getCwbbBsJbxxVO();
        CwbbBsSlxxVO cwbbBsSlxxVO = resVO.getCwbbBsSlxxVO();
        Map<String, String> cwbbBsZcfzbYzxVO = resVO.getCwbbBsZcfzbYzxVO();
        if (GyUtils.isNotNull(cwbbBsZcfzbYzxVO)) {
            // 流动资产合计（基础计算）
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h14l2", new ArrayList<>(Arrays.asList("h1l2", "h2l2", "h3l2", "h4l2",
                    "h5l2", "h6l2", "h7l2", "h8l2", "h9l2", "h10l2", "h11l2", "h12l2", "h13l2")));

            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h14l3", new ArrayList<>(Arrays.asList("h1l3", "h2l3", "h3l3", "h4l3",
                    "h5l3", "h6l3", "h7l3", "h8l3", "h9l3", "h10l3", "h11l3", "h12l3", "h13l3")));

            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h14l5", new ArrayList<>(Arrays.asList("h1l5", "h2l5", "h3l5", "h4l5",
                    "h5l5", "h6l5", "h7l5", "h8l5", "h9l5", "h10l5", "h11l5", "h12l5", "h13l5")));

            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h14l6", new ArrayList<>(Arrays.asList("h1l6", "h2l6", "h3l6", "h4l6",
                    "h5l6", "h6l6", "h7l6", "h8l6", "h9l6", "h10l6", "h11l6", "h12l6", "h13l6")));

            // 非流动负债合计（基础计算）
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h25l5", new ArrayList<>(Arrays.asList(
                    "h15l5", "h16l5", "h19l5", "h20l5", "h21l5", "h22l5", "h23l5", "h24l5"
            )));

            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h25l6", new ArrayList<>(Arrays.asList(
                    "h15l6", "h16l6", "h19l6", "h20l6", "h21l6", "h22l6", "h23l6", "h24l6"
            )));

            // 非流动资产合计（基础计算）
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h33l2", new ArrayList<>(Arrays.asList(
                    "h15l2", "h16l2", "h17l2", "h18l2", "h19l2", "h20l2", "h21l2", "h22l2",
                    "h23l2", "h24l2", "h25l2", "h26l2", "h27l2", "h28l2", "h29l2", "h30l2",
                    "h31l2", "h32l2"
            )));

            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h33l3", new ArrayList<>(Arrays.asList(
                    "h15l3", "h16l3", "h17l3", "h18l3", "h19l3", "h20l3", "h21l3", "h22l3",
                    "h23l3", "h24l3", "h25l3", "h26l3", "h27l3", "h28l3", "h29l3", "h30l3",
                    "h31l3", "h32l3"
            )));

            // ========== 第二阶段：计算一级依赖项 ==========

            // 负债合计（依赖h14l5/h14l6和h25l5/h25l6）
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h26l5", new ArrayList<>(Arrays.asList("h14l5", "h25l5")));
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h26l6", new ArrayList<>(Arrays.asList("h14l6", "h25l6")));

            // ========== 第三阶段：计算所有者权益（需要特殊处理-减法）==========

            // 需要扩展工具类支持减法操作，或者单独处理
            ZzppUtil.calculateWithMinus(cwbbBsZcfzbYzxVO, "h38l5",
                    new ArrayList<>(Arrays.asList("h28l5", "h29l5", "h32l5", "h34l5", "h35l5", "h36l5", "h37l5")),
                    new ArrayList<>(Arrays.asList("h33l5"))
            );

            ZzppUtil.calculateWithMinus(cwbbBsZcfzbYzxVO, "h38l6",
                    new ArrayList<>(Arrays.asList("h28l6", "h29l6", "h32l6", "h34l6", "h35l6", "h36l6", "h37l6")),
                    new ArrayList<>(Arrays.asList("h33l6"))
            );

            // ========== 第四阶段：计算总计项（最终结果）==========

            // 资产总计（依赖h14l2/h14l3和h33l2/h33l3）
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h34l2", new ArrayList<>(Arrays.asList("h14l2", "h33l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h34l3", new ArrayList<>(Arrays.asList("h14l3", "h33l3")));

            // 负债和所有者权益总计（依赖h26l5/h26l6和h38l5/h38l6）
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h39l5", new ArrayList<>(Arrays.asList("h26l5", "h38l5")));
            ZzppUtil.calculateAndUpdate(cwbbBsZcfzbYzxVO, "h39l6", new ArrayList<>(Arrays.asList("h26l6", "h38l6")));
        }
        Map<String, String> cwbbBsLrbYzxVO = resVO.getCwbbBsLrbYzxVO();
        if (GyUtils.isNotNull(cwbbBsLrbYzxVO)) {
            // 其他综合收益明细项计算
            ZzppUtil.calculateAndUpdate(cwbbBsLrbYzxVO, "h28l2", new ArrayList<>(Arrays.asList(
                    "h29l2", "h30l2", "h31l2", "h32l2"
            )));

            ZzppUtil.calculateAndUpdate(cwbbBsLrbYzxVO, "h28l3", new ArrayList<>(Arrays.asList(
                    "h29l3", "h30l3", "h31l3", "h32l3"
            )));

            ZzppUtil.calculateAndUpdate(cwbbBsLrbYzxVO, "h33l2", new ArrayList<>(Arrays.asList(
                    "h34l2", "h35l2", "h36l2", "h37l2", "h38l2", "h39l2"
            )));

            ZzppUtil.calculateAndUpdate(cwbbBsLrbYzxVO, "h33l3", new ArrayList<>(Arrays.asList(
                    "h34l3", "h35l3", "h36l3", "h37l3", "h38l3", "h39l3"
            )));

            // ========== 第二阶段：计算一级依赖项 ==========

            // 其他综合收益的税后净额（依赖h28l2/h28l3和h33l2/h33l3）
            ZzppUtil.calculateAndUpdate(cwbbBsLrbYzxVO, "h27l2", new ArrayList<>(Arrays.asList("h28l2", "h33l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsLrbYzxVO, "h27l3", new ArrayList<>(Arrays.asList("h28l3", "h33l3")));

            // 营业利润（需要特殊处理-嵌套计算）
            ZzppUtil.calculateWithMinus(cwbbBsLrbYzxVO, "h19l2",
                    new ArrayList<>(Arrays.asList("h1l2", "h10l2", "h11l2", "h14l2", "h18l2", "h17l2", "h16l2", "h15l2")),
                    new ArrayList<>(Arrays.asList("h2l2", "h3l2", "h4l2", "h5l2", "h6l2", "h7l2"))
            );

            ZzppUtil.calculateWithMinus(cwbbBsLrbYzxVO, "h19l3",
                    new ArrayList<>(Arrays.asList("h1l3", "h10l3", "h11l3", "h14l3", "h18l3", "h17l3", "h16l3", "h15l3")),
                    new ArrayList<>(Arrays.asList("h2l3", "h3l3", "h4l3", "h5l3", "h6l3", "h7l3"))
            );

            // ========== 第三阶段：计算二级依赖项 ==========

            // 利润总额（依赖h19l2/h19l3和h20l2/h20l3、h21l2/h21l3）
            ZzppUtil.calculateWithMinus(cwbbBsLrbYzxVO, "h22l2",
                    new ArrayList<>(Arrays.asList("h19l2", "h20l2")),
                    new ArrayList<>(Arrays.asList("h21l2"))
            );

            ZzppUtil.calculateWithMinus(cwbbBsLrbYzxVO, "h22l3",
                    new ArrayList<>(Arrays.asList("h19l3", "h20l3")),
                    new ArrayList<>(Arrays.asList("h21l3"))
            );

            // ========== 第四阶段：计算三级依赖项 ==========

            // 净利润（依赖h22l2/h22l3和h23l2/h23l3）
            ZzppUtil.calculateWithMinus(cwbbBsLrbYzxVO, "h24l2",
                    new ArrayList<>(Arrays.asList("h22l2")),
                    new ArrayList<>(Arrays.asList("h23l2"))
            );

            ZzppUtil.calculateWithMinus(cwbbBsLrbYzxVO, "h24l3",
                    new ArrayList<>(Arrays.asList("h22l3")),
                    new ArrayList<>(Arrays.asList("h23l3"))
            );

            // ========== 第五阶段：计算最终结果 ==========

            // 综合收益总额（依赖h24l2/h24l3和h27l2/h27l3）
            ZzppUtil.calculateAndUpdate(cwbbBsLrbYzxVO, "h40l2", new ArrayList<>(Arrays.asList("h24l2", "h27l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsLrbYzxVO, "h40l3", new ArrayList<>(Arrays.asList("h24l3", "h27l3")));
        }
        Map<String, String> cwbbBsXjllbYzxVO = resVO.getCwbbBsXjllbYzxVO();
        if (GyUtils.isNotNull(cwbbBsXjllbYzxVO)) {
            // 经营活动现金流入小计
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h4l2", new ArrayList<>(Arrays.asList("h1l2", "h2l2", "h3l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h4l3", new ArrayList<>(Arrays.asList("h1l3", "h2l3", "h3l3")));

            // 经营活动现金流出小计
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h9l2", new ArrayList<>(Arrays.asList("h5l2", "h6l2", "h7l2", "h8l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h9l3", new ArrayList<>(Arrays.asList("h5l3", "h6l3", "h7l3", "h8l3")));

            // 投资活动现金流入小计
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h16l2", new ArrayList<>(Arrays.asList("h11l2", "h12l2", "h13l2", "h14l2", "h15l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h16l3", new ArrayList<>(Arrays.asList("h11l3", "h12l3", "h13l3", "h14l3", "h15l3")));

            // 投资活动现金流出小计
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h21l2", new ArrayList<>(Arrays.asList("h17l2", "h18l2", "h19l2", "h20l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h21l3", new ArrayList<>(Arrays.asList("h17l3", "h18l3", "h19l3", "h20l3")));

            // 筹资活动现金流入小计
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h26l2", new ArrayList<>(Arrays.asList("h23l2", "h24l2", "h25l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h26l3", new ArrayList<>(Arrays.asList("h23l3", "h24l3", "h25l3")));

            // 筹资活动现金流出小计
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h30l2", new ArrayList<>(Arrays.asList("h27l2", "h28l2", "h29l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h30l3", new ArrayList<>(Arrays.asList("h27l3", "h28l3", "h29l3")));

            // ========== 第二阶段：计算三大活动现金流量净额（依赖小计项）==========

            // 经营活动产生的现金流量净额
            ZzppUtil.calculateWithMinus(cwbbBsXjllbYzxVO, "h10l2",
                    new ArrayList<>(Arrays.asList("h4l2")),
                    new ArrayList<>(Arrays.asList("h9l2"))
            );
            ZzppUtil.calculateWithMinus(cwbbBsXjllbYzxVO, "h10l3",
                    new ArrayList<>(Arrays.asList("h4l3")),
                    new ArrayList<>(Arrays.asList("h9l3"))
            );

            // 投资活动产生的现金流量净额
            ZzppUtil.calculateWithMinus(cwbbBsXjllbYzxVO, "h22l2",
                    new ArrayList<>(Arrays.asList("h16l2")),
                    new ArrayList<>(Arrays.asList("h21l2"))
            );
            ZzppUtil.calculateWithMinus(cwbbBsXjllbYzxVO, "h22l3",
                    new ArrayList<>(Arrays.asList("h16l3")),
                    new ArrayList<>(Arrays.asList("h21l3"))
            );

            // 筹资活动产生的现金流量净额
            ZzppUtil.calculateWithMinus(cwbbBsXjllbYzxVO, "h31l2",
                    new ArrayList<>(Arrays.asList("h26l2")),
                    new ArrayList<>(Arrays.asList("h30l2"))
            );
            ZzppUtil.calculateWithMinus(cwbbBsXjllbYzxVO, "h31l3",
                    new ArrayList<>(Arrays.asList("h26l3")),
                    new ArrayList<>(Arrays.asList("h30l3"))
            );

            // ========== 第三阶段：计算现金净增加额（依赖三大活动净额）==========

            // 现金及现金等价物净增加额
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h33l2", new ArrayList<>(Arrays.asList(
                    "h10l2", "h22l2", "h31l2", "h32l2"
            )));
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h33l3", new ArrayList<>(Arrays.asList(
                    "h10l3", "h22l3", "h31l3", "h32l3"
            )));

            // ========== 第四阶段：计算期末余额（最终结果）==========

            // 期末现金及现金等价物余额
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h35l2", new ArrayList<>(Arrays.asList("h33l2", "h34l2")));
            ZzppUtil.calculateAndUpdate(cwbbBsXjllbYzxVO, "h35l3", new ArrayList<>(Arrays.asList("h33l3", "h34l3")));
        }
        Map<String, String> cwbbBsSyzqybdbYzxVO = resVO.getCwbbBsSyzqybdbYzxVO();
        if (GyUtils.isNotNull(cwbbBsSyzqybdbYzxVO)) {
            // 转换 jsqh 调用
//jsqh("4", 2) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l2", new ArrayList<>(Arrays.asList("h1l2", "h2l2", "h3l2", "h27l2")));

//jsqh("4", 16) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l16", new ArrayList<>(Arrays.asList("h1l16", "h2l16", "h3l16", "h27l16")));

//jsqh("4", 17) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l17", new ArrayList<>(Arrays.asList("h1l17", "h2l17", "h3l17", "h27l17")));
//            jsqh("4", 18) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l18", new ArrayList<>(Arrays.asList("h1l18", "h2l18", "h3l18", "h27l18")));

//jsqh("4", 3) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l3", new ArrayList<>(Arrays.asList("h1l3", "h2l3", "h3l3", "h27l3")));
//jsqh("4", 4) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l4", new ArrayList<>(Arrays.asList("h1l4", "h2l4", "h3l4", "h27l4")));

//jsqh("4", 14) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l14", new ArrayList<>(Arrays.asList("h1l14", "h2l14", "h3l14", "h27l14")));

//jsqh("4", 22) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l22", new ArrayList<>(Arrays.asList("h1l22", "h2l22", "h3l22", "h27l22")));

//jsqh("4", 5) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l5", new ArrayList<>(Arrays.asList("h1l5", "h2l5", "h3l5", "h27l5")));

//jsqh("4", 6) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l6", new ArrayList<>(Arrays.asList("h1l6", "h2l6", "h3l6", "h27l6")));

//jsqh("4", 8) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l8", new ArrayList<>(Arrays.asList("h1l8", "h2l8", "h3l8", "h27l8")));

//jsqh("4", 19) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l19", new ArrayList<>(Arrays.asList("h1l19", "h2l19", "h3l19", "h27l19")));

//jsqh("4", 20) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l20", new ArrayList<>(Arrays.asList("h1l20", "h2l20", "h3l20", "h27l20")));

//jsqh("4", 21) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l21", new ArrayList<>(Arrays.asList("h1l21", "h2l21", "h3l21", "h27l21")));

//jsqh("4", 9) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l9", new ArrayList<>(Arrays.asList("h1l9", "h2l9", "h3l9", "h27l9")));

//jsqh("4", 10) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l10", new ArrayList<>(Arrays.asList("h1l10", "h2l10", "h3l10", "h27l10")));

//jsqh("4", 15) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l15", new ArrayList<>(Arrays.asList("h1l15", "h2l15", "h3l15", "h27l15")));

//jsqh("4", 23) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l23", new ArrayList<>(Arrays.asList("h1l23", "h2l23", "h3l23", "h27l23")));

//jsqh("4", 11) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l11", new ArrayList<>(Arrays.asList("h1l11", "h2l11", "h3l11", "h27l11")));

//jsqh("4", 12) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h4l12", new ArrayList<>(Arrays.asList("h1l12", "h2l12", "h3l12", "h27l12")));


            //jsqh("13", 2) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l2", new ArrayList<>(Arrays.asList("h14l2", "h15l2", "h16l2", "h28l2")));
//jsqh("13", 16) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l16", new ArrayList<>(Arrays.asList("h14l16", "h15l16", "h16l16", "h28l16")));
//jsqh("13", 17) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l17", new ArrayList<>(Arrays.asList("h14l17", "h15l17", "h16l17", "h28l17")));
//jsqh("13", 18) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l18", new ArrayList<>(Arrays.asList("h14l18", "h15l18", "h16l18", "h28l18")));
//jsqh("13", 3) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l3", new ArrayList<>(Arrays.asList("h14l3", "h15l3", "h16l3", "h28l3")));
//jsqh("13", 4) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l4", new ArrayList<>(Arrays.asList("h14l4", "h15l4", "h16l4", "h28l4")));
//jsqh("13", 14) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l14", new ArrayList<>(Arrays.asList("h14l14", "h15l14", "h16l14", "h28l14")));
//jsqh("13", 22) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l22", new ArrayList<>(Arrays.asList("h14l22", "h15l22", "h16l22", "h28l22")));
//jsqh("13", 5) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l5", new ArrayList<>(Arrays.asList("h14l5", "h15l5", "h16l5", "h28l5")));
//jsqh("13", 6) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l6", new ArrayList<>(Arrays.asList("h14l6", "h15l6", "h16l6", "h28l6")));
//jsqh("13", 8) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l8", new ArrayList<>(Arrays.asList("h14l8", "h15l8", "h16l8", "h28l8")));
//jsqh("13", 19) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l19", new ArrayList<>(Arrays.asList("h14l19", "h15l19", "h16l19", "h28l19")));
//jsqh("13", 20) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l20", new ArrayList<>(Arrays.asList("h14l20", "h15l20", "h16l20", "h28l20")));
//jsqh("13", 21) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l21", new ArrayList<>(Arrays.asList("h14l21", "h15l21", "h16l21", "h28l21")));
//jsqh("13", 9) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l9", new ArrayList<>(Arrays.asList("h14l9", "h15l9", "h16l9", "h28l9")));
//jsqh("13", 10) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l10", new ArrayList<>(Arrays.asList("h14l10", "h15l10", "h16l10", "h28l10")));
//jsqh("13", 15) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l15", new ArrayList<>(Arrays.asList("h14l15", "h15l15", "h16l15", "h28l15")));
//jsqh("13", 23) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l23", new ArrayList<>(Arrays.asList("h14l23", "h15l23", "h16l23", "h28l23")));
//jsqh("13", 11) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l11", new ArrayList<>(Arrays.asList("h14l11", "h15l11", "h16l11", "h28l11")));
//jsqh("13", 12) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h13l12", new ArrayList<>(Arrays.asList("h14l12", "h15l12", "h16l12", "h28l12")));


            //jsqh("17", 2) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l2", new ArrayList<>(Arrays.asList("h18l2", "h19l2", "h20l2")));
//jsqh("17", 16) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l16", new ArrayList<>(Arrays.asList("h18l16", "h19l16", "h20l16")));
//jsqh("17", 17) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l17", new ArrayList<>(Arrays.asList("h18l17", "h19l17", "h20l17")));
//jsqh("17", 18) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l18", new ArrayList<>(Arrays.asList("h18l18", "h19l18", "h20l18")));
//jsqh("17", 3) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l3", new ArrayList<>(Arrays.asList("h18l3", "h19l3", "h20l3")));
//jsqh("17", 4) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l4", new ArrayList<>(Arrays.asList("h18l4", "h19l4", "h20l4")));
//jsqh("17", 14) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l14", new ArrayList<>(Arrays.asList("h18l14", "h19l14", "h20l14")));
//jsqh("17", 22) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l22", new ArrayList<>(Arrays.asList("h18l22", "h19l22", "h20l22")));
//jsqh("17", 5) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l5", new ArrayList<>(Arrays.asList("h18l5", "h19l5", "h20l5")));
//jsqh("17", 6) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l6", new ArrayList<>(Arrays.asList("h18l6", "h19l6", "h20l6")));
//jsqh("17", 8) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l8", new ArrayList<>(Arrays.asList("h18l8", "h19l8", "h20l8")));
//jsqh("17", 19) → ;
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l19", new ArrayList<>(Arrays.asList("h18l19", "h19l19", "h20l19")));
//jsqh("17", 20) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l20", new ArrayList<>(Arrays.asList("h18l20", "h19l20", "h20l20")));
//jsqh("17", 21) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l21", new ArrayList<>(Arrays.asList("h18l21", "h19l21", "h20l21")));
//jsqh("17", 9) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l9", new ArrayList<>(Arrays.asList("h18l9", "h19l9", "h20l9")));
//jsqh("17", 10) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l10", new ArrayList<>(Arrays.asList("h18l10", "h19l10", "h20l10")));
//jsqh("17", 15) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l15", new ArrayList<>(Arrays.asList("h18l15", "h19l15", "h20l15")));
//jsqh("17", 23) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l23", new ArrayList<>(Arrays.asList("h18l23", "h19l23", "h20l23")));
//jsqh("17", 11) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l11", new ArrayList<>(Arrays.asList("h18l11", "h19l11", "h20l11")));
//jsqh("17", 12) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h17l12", new ArrayList<>(Arrays.asList("h18l12", "h19l12", "h20l12")));


            //jsqh("21", 2) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l2", new ArrayList<>(Arrays.asList("h22l2", "h23l2", "h24l2", "h25l2", "h29l2", "h30l2")));
//jsqh("21", 16) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l16", new ArrayList<>(Arrays.asList("h22l16", "h23l16", "h24l16", "h25l16", "h29l16", "h30l16")));
//jsqh("21", 17) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l17", new ArrayList<>(Arrays.asList("h22l17", "h23l17", "h24l17", "h25l17", "h29l17", "h30l17")));
//jsqh("21", 18) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l18", new ArrayList<>(Arrays.asList("h22l18", "h23l18", "h24l18", "h25l18", "h29l18", "h30l18")));
//jsqh("21", 3) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l3", new ArrayList<>(Arrays.asList("h22l3", "h23l3", "h24l3", "h25l3", "h29l3", "h30l3")));
//jsqh("21", 4) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l4", new ArrayList<>(Arrays.asList("h22l4", "h23l4", "h24l4", "h25l4", "h29l4", "h30l4")));
//jsqh("21", 14) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l14", new ArrayList<>(Arrays.asList("h22l14", "h23l14", "h24l14", "h25l14", "h29l14", "h30l14")));
//jsqh("21", 22) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l22", new ArrayList<>(Arrays.asList("h22l22", "h23l22", "h24l22", "h25l22", "h29l22", "h30l22")));
//jsqh("21", 5) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l5", new ArrayList<>(Arrays.asList("h22l5", "h23l5", "h24l5", "h25l5", "h29l5", "h30l5")));
//jsqh("21", 6) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l6", new ArrayList<>(Arrays.asList("h22l6", "h23l6", "h24l6", "h25l6", "h29l6", "h30l6")));
//jsqh("21", 8) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l8", new ArrayList<>(Arrays.asList("h22l8", "h23l8", "h24l8", "h25l8", "h29l8", "h30l8")));
//jsqh("21", 19) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l19", new ArrayList<>(Arrays.asList("h22l19", "h23l19", "h24l19", "h25l19", "h29l19", "h30l19")));
//jsqh("21", 20) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l20", new ArrayList<>(Arrays.asList("h22l20", "h23l20", "h24l20", "h25l20", "h29l20", "h30l20")));
//jsqh("21", 21) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l21", new ArrayList<>(Arrays.asList("h22l21", "h23l21", "h24l21", "h25l21", "h29l21", "h30l21")));
//jsqh("21", 9) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l9", new ArrayList<>(Arrays.asList("h22l9", "h23l9", "h24l9", "h25l9", "h29l9", "h30l9")));
//jsqh("21", 10) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l10", new ArrayList<>(Arrays.asList("h22l10", "h23l10", "h24l10", "h25l10", "h29l10", "h30l10")));
//jsqh("21", 15) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l15", new ArrayList<>(Arrays.asList("h22l15", "h23l15", "h24l15", "h25l15", "h29l15", "h30l15")));
//jsqh("21", 23) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l23", new ArrayList<>(Arrays.asList("h22l23", "h23l23", "h24l23", "h25l23", "h29l23", "h30l23")));
//jsqh("21", 11) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l11", new ArrayList<>(Arrays.asList("h22l11", "h23l11", "h24l11", "h25l11", "h29l11", "h30l11")));
//jsqh("21", 12) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h21l12", new ArrayList<>(Arrays.asList("h22l12", "h23l12", "h24l12", "h25l12", "h29l12", "h30l12")));

//jsqh("5", 2) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l2", new ArrayList<>(Arrays.asList("h6l2", "h13l2", "h17l2", "h21l2")));

//jsqh("5", 16) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l16", new ArrayList<>(Arrays.asList("h6l16", "h13l16", "h17l16", "h21l16")));

//jsqh("5", 17) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l17", new ArrayList<>(Arrays.asList("h6l17", "h13l17", "h17l17", "h21l17")));

//jsqh("5", 18) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l18", new ArrayList<>(Arrays.asList("h6l18", "h13l18", "h17l18", "h21l18")));

//jsqh("5", 3) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l3", new ArrayList<>(Arrays.asList("h6l3", "h13l3", "h17l3", "h21l3")));

//jsqh("5", 4) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l4", new ArrayList<>(Arrays.asList("h6l4", "h13l4", "h17l4", "h21l4")));

//jsqh("5", 14) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l14", new ArrayList<>(Arrays.asList("h6l14", "h13l14", "h17l14", "h21l14")));
//jsqh("5", 22) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l22", new ArrayList<>(Arrays.asList("h6l22", "h13l22", "h17l22", "h21l22")));
//jsqh("5", 5) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l5", new ArrayList<>(Arrays.asList("h6l5", "h13l5", "h17l5", "h21l5")));
//jsqh("5", 6) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l6", new ArrayList<>(Arrays.asList("h6l6", "h13l6", "h17l6", "h21l6")));
//jsqh("5", 8) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l8", new ArrayList<>(Arrays.asList("h6l8", "h13l8", "h17l8", "h21l8")));
//jsqh("5", 19) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l19", new ArrayList<>(Arrays.asList("h6l19", "h13l19", "h17l19", "h21l19")));
//jsqh("5", 20) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l20", new ArrayList<>(Arrays.asList("h6l20", "h13l20", "h17l20", "h21l20")));
//jsqh("5", 21) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l21", new ArrayList<>(Arrays.asList("h6l21", "h13l21", "h17l21", "h21l21")));
//jsqh("5", 9) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l9", new ArrayList<>(Arrays.asList("h6l9", "h13l9", "h17l9", "h21l9")));
//jsqh("5", 10) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l10", new ArrayList<>(Arrays.asList("h6l10", "h13l10", "h17l10", "h21l10")));
//jsqh("5", 15) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l15", new ArrayList<>(Arrays.asList("h6l15", "h13l15", "h17l15", "h21l15")));
//jsqh("5", 23) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l23", new ArrayList<>(Arrays.asList("h6l23", "h13l23", "h17l23", "h21l23")));
//jsqh("5", 11) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l11", new ArrayList<>(Arrays.asList("h6l11", "h13l11", "h17l11", "h21l11")));
//jsqh("5", 12) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h5l12", new ArrayList<>(Arrays.asList("h6l12", "h13l12", "h17l12", "h21l12")));


//jsqh("26", 2) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l2", new ArrayList<>(Arrays.asList("h4l2", "h5l2")));
//jsqh("26", 16) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l16", new ArrayList<>(Arrays.asList("h4l16", "h5l16")));
//jsqh("26", 17) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l17", new ArrayList<>(Arrays.asList("h4l17", "h5l17")));
//jsqh("26", 18) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l18", new ArrayList<>(Arrays.asList("h4l18", "h5l18")));
//jsqh("26", 3) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l3", new ArrayList<>(Arrays.asList("h4l3", "h5l3")));
//jsqh("26", 4) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l4", new ArrayList<>(Arrays.asList("h4l4", "h5l4")));
//jsqh("26", 14) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l14", new ArrayList<>(Arrays.asList("h4l14", "h5l14")));
//jsqh("26", 22) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l22", new ArrayList<>(Arrays.asList("h4l22", "h5l22")));
//jsqh("26", 5) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l5", new ArrayList<>(Arrays.asList("h4l5", "h5l5")));
//jsqh("26", 6) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l6", new ArrayList<>(Arrays.asList("h4l6", "h5l6")));
//jsqh("26", 8) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l8", new ArrayList<>(Arrays.asList("h4l8", "h5l8")));
//jsqh("26", 19) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l19", new ArrayList<>(Arrays.asList("h4l19", "h5l19")));
//jsqh("26", 20) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l20", new ArrayList<>(Arrays.asList("h4l20", "h5l20")));
//jsqh("26", 21) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l21", new ArrayList<>(Arrays.asList("h4l21", "h5l21")));
//jsqh("26", 9) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l9", new ArrayList<>(Arrays.asList("h4l9", "h5l9")));
//jsqh("26", 10) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l10", new ArrayList<>(Arrays.asList("h4l10", "h5l10")));
//jsqh("26", 15) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l15", new ArrayList<>(Arrays.asList("h4l15", "h5l15")));
//jsqh("26", 23) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l23", new ArrayList<>(Arrays.asList("h4l23", "h5l23")));
//jsqh("26", 11) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l11", new ArrayList<>(Arrays.asList("h4l11", "h5l11")));
//jsqh("26", 12) →
            ZzppUtil.calculateAndUpdate(cwbbBsSyzqybdbYzxVO, "h26l12", new ArrayList<>(Arrays.asList("h4l12", "h5l12")));

// 转换 jshh 调用（包含加减计算）
//jshh("1", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h1l7",
                    new ArrayList<>(Arrays.asList("h1l2", "h1l16", "h1l17", "h1l18", "h1l3", "h1l14", "h1l22", "h1l5", "h1l6")),
                    new ArrayList<>(Arrays.asList("h1l4")));
//jshh("1", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h1l13",
                    new ArrayList<>(Arrays.asList("h1l8", "h1l19", "h1l20", "h1l21", "h1l9", "h1l15", "h1l23", "h1l11", "h1l12")),
                    new ArrayList<>(Arrays.asList("h1l10")));
//jshh("2", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h2l7",
                    new ArrayList<>(Arrays.asList("h2l2", "h2l16", "h2l17", "h2l18", "h2l3", "h2l14", "h2l22", "h2l5", "h2l6")),
                    new ArrayList<>(Arrays.asList("h2l4")));
//jshh("2", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h2l13",
                    new ArrayList<>(Arrays.asList("h2l8", "h2l19", "h2l20", "h2l21", "h2l9", "h2l15", "h2l23", "h2l11", "h2l12")),
                    new ArrayList<>(Arrays.asList("h2l10")));
//jshh("3", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h3l13",
                    new ArrayList<>(Arrays.asList("h3l8", "h3l19", "h3l20", "h3l21", "h3l9", "h3l15", "h3l23", "h3l11", "h3l12")),
                    new ArrayList<>(Arrays.asList("h3l10")));
//jshh("27", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h27l7",
                    new ArrayList<>(Arrays.asList("h27l2", "h27l16", "h27l17", "h27l18", "h27l3", "h27l14", "h27l22", "h27l5", "h27l6")),
                    new ArrayList<>(Arrays.asList("h27l4")));
//jshh("27", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h27l13",
                    new ArrayList<>(Arrays.asList("h27l8", "h27l19", "h27l20", "h27l21", "h27l9", "h27l15", "h27l23", "h27l11", "h27l12")),
                    new ArrayList<>(Arrays.asList("h27l10")));
//jshh("4", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h4l7",
                    new ArrayList<>(Arrays.asList("h4l2", "h4l16", "h4l17", "h4l18", "h4l3", "h4l14", "h4l22", "h4l5", "h4l6")),
                    new ArrayList<>(Arrays.asList("h4l4")));
//jshh("4", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h4l13",
                    new ArrayList<>(Arrays.asList("h4l8", "h4l19", "h4l20", "h4l21", "h4l9", "h4l15", "h4l23", "h4l11", "h4l12")),
                    new ArrayList<>(Arrays.asList("h4l10")));
//jshh("5", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h5l7",
                    new ArrayList<>(Arrays.asList("h5l2", "h5l16", "h5l17", "h5l18", "h5l3", "h5l14", "h5l22", "h5l5", "h5l6")),
                    new ArrayList<>(Arrays.asList("h5l4")));
//jshh("5", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h5l13",
                    new ArrayList<>(Arrays.asList("h5l8", "h5l19", "h5l20", "h5l21", "h5l9", "h5l15", "h5l23", "h5l11", "h5l12")),
                    new ArrayList<>(Arrays.asList("h5l10")));
//jshh("6", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h6l7",
                    new ArrayList<>(Arrays.asList("h6l2", "h6l16", "h6l17", "h6l18", "h6l3", "h6l14", "h6l22", "h6l5", "h6l6")),
                    new ArrayList<>(Arrays.asList("h6l4")));
//jshh("6", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h6l13",
                    new ArrayList<>(Arrays.asList("h6l8", "h6l19", "h6l20", "h6l21", "h6l9", "h6l15", "h6l23", "h6l11", "h6l12")),
                    new ArrayList<>(Arrays.asList("h6l10")));
//jshh("13", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h13l7",
                    new ArrayList<>(Arrays.asList("h13l2", "h13l16", "h13l17", "h13l18", "h13l3", "h13l14", "h13l22", "h13l5", "h13l6")),
                    new ArrayList<>(Arrays.asList("h13l4")));
//jshh("13", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h13l13",
                    new ArrayList<>(Arrays.asList("h13l8", "h13l19", "h13l20", "h13l21", "h13l9", "h13l15", "h13l23", "h13l11", "h13l12")),
                    new ArrayList<>(Arrays.asList("h13l10")));
//jshh("14", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h14l7",
                    new ArrayList<>(Arrays.asList("h14l2", "h14l16", "h14l17", "h14l18", "h14l3", "h14l14", "h14l22", "h14l5", "h14l6")),
                    new ArrayList<>(Arrays.asList("h14l4")));
//jshh("14", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h14l13",
                    new ArrayList<>(Arrays.asList("h14l8", "h14l19", "h14l20", "h14l21", "h14l9", "h14l15", "h14l23", "h14l11", "h14l12")),
                    new ArrayList<>(Arrays.asList("h14l10")));
//jshh("15", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h15l7",
                    new ArrayList<>(Arrays.asList("h15l2", "h15l16", "h15l17", "h15l18", "h15l3", "h15l14", "h15l22", "h15l5", "h15l6")),
                    new ArrayList<>(Arrays.asList("h15l4")));
//jshh("15", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h15l13",
                    new ArrayList<>(Arrays.asList("h15l8", "h15l19", "h15l20", "h15l21", "h15l9", "h15l15", "h15l23", "h15l11", "h15l12")),
                    new ArrayList<>(Arrays.asList("h15l10")));
//jshh("16", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h16l7",
                    new ArrayList<>(Arrays.asList("h16l2", "h16l16", "h16l17", "h16l18", "h16l3", "h16l14", "h16l22", "h16l5", "h16l6")),
                    new ArrayList<>(Arrays.asList("h16l4")));
//jshh("16", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h16l13",
                    new ArrayList<>(Arrays.asList("h16l8", "h16l19", "h16l20", "h16l21", "h16l9", "h16l15", "h16l23", "h16l11", "h16l12")),
                    new ArrayList<>(Arrays.asList("h16l10")));
//jshh("28", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h28l7",
                    new ArrayList<>(Arrays.asList("h28l2", "h28l16", "h28l17", "h28l18", "h28l3", "h28l14", "h28l22", "h28l5", "h28l6")),
                    new ArrayList<>(Arrays.asList("h28l4")));
//jshh("28", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h28l13",
                    new ArrayList<>(Arrays.asList("h28l8", "h28l19", "h28l20", "h28l21", "h28l9", "h28l15", "h28l23", "h28l11", "h28l12")),
                    new ArrayList<>(Arrays.asList("h28l10")));
//jshh("17", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h17l7",
                    new ArrayList<>(Arrays.asList("h17l2", "h17l16", "h17l17", "h17l18", "h17l3", "h17l14", "h17l22", "h17l5", "h17l6")),
                    new ArrayList<>(Arrays.asList("h17l4")));
//jshh("17", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h17l13",
                    new ArrayList<>(Arrays.asList("h17l8", "h17l19", "h17l20", "h17l21", "h17l9", "h17l15", "h17l23", "h17l11", "h17l12")),
                    new ArrayList<>(Arrays.asList("h17l10")));
//jshh("18", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h18l7",
                    new ArrayList<>(Arrays.asList("h18l2", "h18l16", "h18l17", "h18l18", "h18l3", "h18l14", "h18l22", "h18l5", "h18l6")),
                    new ArrayList<>(Arrays.asList("h18l4")));
//jshh("18", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h18l13",
                    new ArrayList<>(Arrays.asList("h18l8", "h18l19", "h18l20", "h18l21", "h18l9", "h18l15", "h18l23", "h18l11", "h18l12")),
                    new ArrayList<>(Arrays.asList("h18l10")));
//jshh("19", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h19l7",
                    new ArrayList<>(Arrays.asList("h19l2", "h19l16", "h19l17", "h19l18", "h19l3", "h19l14", "h19l22", "h19l5", "h19l6")),
                    new ArrayList<>(Arrays.asList("h19l4")));
//jshh("19", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h19l13",
                    new ArrayList<>(Arrays.asList("h19l8", "h19l19", "h19l20", "h19l21", "h19l9", "h19l15", "h19l23", "h19l11", "h19l12")),
                    new ArrayList<>(Arrays.asList("h19l10")));
//jshh("20", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h20l7",
                    new ArrayList<>(Arrays.asList("h20l2", "h20l16", "h20l17", "h20l18", "h20l3", "h20l14", "h20l22", "h20l5", "h20l6")),
                    new ArrayList<>(Arrays.asList("h20l4")));
//jshh("20", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h20l13",
                    new ArrayList<>(Arrays.asList("h20l8", "h20l19", "h20l20", "h20l21", "h20l9", "h20l15", "h20l23", "h20l11", "h20l12")),
                    new ArrayList<>(Arrays.asList("h20l10")));
//jshh("21", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h21l7",
                    new ArrayList<>(Arrays.asList("h21l2", "h21l16", "h21l17", "h21l18", "h21l3", "h21l14", "h21l22", "h21l5", "h21l6")),
                    new ArrayList<>(Arrays.asList("h21l4")));
//jshh("21", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h21l13",
                    new ArrayList<>(Arrays.asList("h21l8", "h21l19", "h21l20", "h21l21", "h21l9", "h21l15", "h21l23", "h21l11", "h21l12")),
                    new ArrayList<>(Arrays.asList("h21l10")));
//jshh("22", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h22l7",
                    new ArrayList<>(Arrays.asList("h22l2", "h22l16", "h22l17", "h22l18", "h22l3", "h22l14", "h22l22", "h22l5", "h22l6")),
                    new ArrayList<>(Arrays.asList("h22l4")));
//jshh("22", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h22l13",
                    new ArrayList<>(Arrays.asList("h22l8", "h22l19", "h22l20", "h22l21", "h22l9", "h22l15", "h22l23", "h22l11", "h22l12")),
                    new ArrayList<>(Arrays.asList("h22l10")));
//jshh("23", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h23l7",
                    new ArrayList<>(Arrays.asList("h23l2", "h23l16", "h23l17", "h23l18", "h23l3", "h23l14", "h23l22", "h23l5", "h23l6")),
                    new ArrayList<>(Arrays.asList("h23l4")));
//jshh("23", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h23l13",
                    new ArrayList<>(Arrays.asList("h23l8", "h23l19", "h23l20", "h23l21", "h23l9", "h23l15", "h23l23", "h23l11", "h23l12")),
                    new ArrayList<>(Arrays.asList("h23l10")));
//jshh("24", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h24l7",
                    new ArrayList<>(Arrays.asList("h24l2", "h24l16", "h24l17", "h24l18", "h24l3", "h24l14", "h24l22", "h24l5", "h24l6")),
                    new ArrayList<>(Arrays.asList("h24l4")));
//jshh("24", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h24l13",
                    new ArrayList<>(Arrays.asList("h24l8", "h24l19", "h24l20", "h24l21", "h24l9", "h24l15", "h24l23", "h24l11", "h24l12")),
                    new ArrayList<>(Arrays.asList("h24l10")));
//jshh("25", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h25l7",
                    new ArrayList<>(Arrays.asList("h25l2", "h25l16", "h25l17", "h25l18", "h25l3", "h25l14", "h25l22", "h25l5", "h25l6")),
                    new ArrayList<>(Arrays.asList("h25l4")));
//jshh("25", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h25l13",
                    new ArrayList<>(Arrays.asList("h25l8", "h25l19", "h25l20", "h25l21", "h25l9", "h25l15", "h25l23", "h25l11", "h25l12")),
                    new ArrayList<>(Arrays.asList("h25l10")));
//jshh("29", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h29l7",
                    new ArrayList<>(Arrays.asList("h29l2", "h29l16", "h29l17", "h29l18", "h29l3", "h29l14", "h29l22", "h29l5", "h29l6")),
                    new ArrayList<>(Arrays.asList("h29l4")));
//jshh("29", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h29l13",
                    new ArrayList<>(Arrays.asList("h29l8", "h29l19", "h29l20", "h29l21", "h29l9", "h29l15", "h29l23", "h29l11", "h29l12")),
                    new ArrayList<>(Arrays.asList("h29l10")));
//jshh("30", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h30l7",
                    new ArrayList<>(Arrays.asList("h30l2", "h30l16", "h30l17", "h30l18", "h30l3", "h30l14", "h30l22", "h30l5", "h30l6")),
                    new ArrayList<>(Arrays.asList("h30l4")));
//jshh("30", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h30l13",
                    new ArrayList<>(Arrays.asList("h30l8", "h30l19", "h30l20", "h30l21", "h30l9", "h30l15", "h30l23", "h30l11", "h30l12")),
                    new ArrayList<>(Arrays.asList("h30l10")));
//jshh("26", 7) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h26l7",
                    new ArrayList<>(Arrays.asList("h26l2", "h26l16", "h26l17", "h26l18", "h26l3", "h26l14", "h26l22", "h26l5", "h26l6")),
                    new ArrayList<>(Arrays.asList("h26l4")));
//jshh("26", 13) →
            ZzppUtil.calculateWithMinus(cwbbBsSyzqybdbYzxVO, "h26l13",
                    new ArrayList<>(Arrays.asList("h26l8", "h26l19", "h26l20", "h26l21", "h26l9", "h26l15", "h26l23", "h26l11", "h26l12")),
                    new ArrayList<>(Arrays.asList("h26l10")));
        }

        List<String> ksbjy = new ArrayList<>();

        //资产负债表校验
        // 1. 资产总计_期末余额 vs 负债和所有者权益总计_期末余额
        BigDecimal h34l2 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h34l2"));
        BigDecimal h39l5 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h39l5"));

        if (!h34l2.equals(h39l5)) {
            ksbjy.add("资产负债表中[资产总计_期末余额]【" + cwbbBsZcfzbYzxVO.get("h34l2") +
                    "】应等于[负债和所有者权益(或股东权益)总计_期末余额]【" + cwbbBsZcfzbYzxVO.get("h39l5") + "】");
        } else if (h34l2.equals(BigDecimal.ZERO)) {
            ksbjy.add("资产负债表中[资产总计_期末余额]为【0】，请核实是否和实际相符。");
        } else if (h39l5.equals(BigDecimal.ZERO)) {
            ksbjy.add("资产负债表中[负债和所有者权益(或股东权益)总计_期末余额]为【0】，请核实是否和实际相符。");
        }

        // 2. 资产总计_上年期末余额 vs 负债和所有者权益总计_上年末余额
        BigDecimal h34l3 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h34l3"));
        BigDecimal h39l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h39l6"));

        if (!h34l3.equals(h39l6)) {
            ksbjy.add("资产负债表中[资产总计_上年期末余额]【" + cwbbBsZcfzbYzxVO.get("h34l3") +
                    "】应等于[负债和所有者权益(或股东权益)总计_上年末余额]【" + cwbbBsZcfzbYzxVO.get("h39l6") + "】");
        }

        // 3. 应付债券校验 - 期末余额
        BigDecimal h16l5 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h16l5"));
        BigDecimal h17l5 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h17l5"));
        BigDecimal h18l5 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h18l5"));

        if (h16l5.compareTo(h17l5.add(h18l5)) < 0) {
            ksbjy.add("[应付债券_期末余额]不能小于优先股+永续债的值。");
        }

        // 4. 应付债券校验 - 上年年末余额
        BigDecimal h16l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h16l6"));
        BigDecimal h17l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h17l6"));
        BigDecimal h18l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h18l6"));

        if (h16l6.compareTo(h17l6.add(h18l6)) < 0) {
            ksbjy.add("[应付债券_上年年末余额]不能小于优先股+永续债的值。");
        }

        // 5. 其他权益工具校验 - 期末余额
        BigDecimal h29l5 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h29l5"));
        BigDecimal h30l5 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h30l5"));
        BigDecimal h31l5 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h31l5"));

        if (h29l5.compareTo(h30l5.add(h31l5)) < 0) {
            ksbjy.add("[其他权益工具_期末余额]不能小于优先股,永续债之和。");
        }

        // 6. 其他权益工具校验 - 上年年末余额
        BigDecimal h29l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h29l6"));
        BigDecimal h30l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h30l6"));
        BigDecimal h31l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h31l6"));

        if (h29l6.compareTo(h30l6.add(h31l6)) < 0) {
            ksbjy.add("[其他权益工具_上年年末余额]不能小于优先股,永续债之和。");
        }

        //所有者权益表校验
        // h4l2 验证
        BigDecimal syzqyh4l2 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l2"));
        BigDecimal zcfzh28l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h28l6"));
        if (!syzqyh4l2.equals(zcfzh28l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额_本年金额_实收资本(或股本)]应等于同期资产负债表的[上年年末余额_实收资本(或股本)]! ");
        }

        // h4l16 验证
        BigDecimal syzqyh4l16 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l16"));
        BigDecimal zcfzh30l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h30l6"));
        if (!syzqyh4l16.equals(zcfzh30l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额-其他权益工具-优先股]应等于同期资产负债表的[上年年末余额_其他权益工具-优先股]!");
        }

        // h4l17 验证
        BigDecimal syzqyh4l17 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l17"));
        BigDecimal zcfzh31l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h31l6"));
        if (!syzqyh4l17.equals(zcfzh31l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额-其他权益工具-永续债]应等于同期资产负债表的[上年年末余额_其他权益工具-永续债]!");
        }

        // h4l3 验证
        BigDecimal syzqyh4l3 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l3"));
        BigDecimal zcfzh32l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h32l6"));
        if (!syzqyh4l3.equals(zcfzh32l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额资本公积]应等于同期资产负债表的[上年年末余额_资本公积]!");
        }

        // h4l4 验证
        BigDecimal syzqyh4l4 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l4"));
        BigDecimal zcfzh33l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h33l6"));
        if (!syzqyh4l4.equals(zcfzh33l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额减库存股]应等于同期资产负债表的[上年年末余额_减：库存股]!");
        }

        // h4l14 验证
        BigDecimal syzqyh4l14 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l14"));
        BigDecimal zcfzh34l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h34l6"));
        if (!syzqyh4l14.equals(zcfzh34l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额其他综合收益]应等于同期资产负债表的[上年年末余额_其他综合收益]!");
        }

        // h4l22 验证
        BigDecimal syzqyh4l22 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l22"));
        BigDecimal zcfzh35l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h35l6"));
        if (!syzqyh4l22.equals(zcfzh35l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额专项储备]应等于同期资产负债表的[上年年末余额_专项储备]!");
        }

        // h4l5 验证
        BigDecimal syzqyh4l5 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l5"));
        BigDecimal zcfzh36l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h36l6"));
        if (!syzqyh4l5.equals(zcfzh36l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额金额盈余公积]应等于同期资产负债表的[上年年末余额_盈余公积]!");
        }

        // h4l6 验证
        BigDecimal syzqyh4l6 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l6"));
        BigDecimal zcfzh37l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h37l6"));
        if (!syzqyh4l6.equals(zcfzh37l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额未分配利润]应等于同期资产负债表的[上年年末余额_未分配利润]!");
        }

        // h4l7 验证
        BigDecimal syzqyh4l7 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h4l7"));
        BigDecimal zcfzh38l6 = toBigDecimal(cwbbBsZcfzbYzxVO.get("h38l6"));
        if (!syzqyh4l7.equals(zcfzh38l6)) {
            ksbjy.add("所有者权益变动表的[二、本年年初余额本年金额所有者权益合计]应等于同期资产负债表的[上年年末余额_所有者权益合计]!");
        }

        // h5l7 公式验证
        BigDecimal h5l7 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h5l7"));
        BigDecimal h6l7 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h6l7"));
        BigDecimal h13l7 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h13l7"));
        BigDecimal h17l7 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h17l7"));
        BigDecimal h21l7 = toBigDecimal(cwbbBsSyzqybdbYzxVO.get("h21l7"));

        // 计算总和：h6l7 + h13l7 + h17l7 + h21l7
        BigDecimal sum = h6l7.add(h13l7).add(h17l7).add(h21l7);

        if (!h5l7.equals(sum)) {
            ksbjy.add("本年增减变动金额(减少以\"-\"号填列)本年所有者权益合计应满足公式：等于(一)综合收益总额_本年金额_所有者权益合计+(二)所有者投入和减少资本_本年金额_所有者权益合计+(三)利润分配_本年金额_所有者权益合计+(四)所有者权益内部结转_本年金额_所有者权益合计");
        }


        Map<String, String> bw = new HashMap<>();
        bw.put("zcfzbh14l2", cwbbBsZcfzbYzxVO.get("h14l2"));
        bw.put("zcfzbh14l3", cwbbBsZcfzbYzxVO.get("h14l3"));
        bw.put("zcfzbh14l5", cwbbBsZcfzbYzxVO.get("h14l5"));
        bw.put("zcfzbh14l6", cwbbBsZcfzbYzxVO.get("h14l6"));
        bw.put("zcfzbh33l2", cwbbBsZcfzbYzxVO.get("h33l2"));
        bw.put("zcfzbh33l3", cwbbBsZcfzbYzxVO.get("h33l3"));
        bw.put("zcfzbh25l5", cwbbBsZcfzbYzxVO.get("h25l5"));
        bw.put("zcfzbh25l6", cwbbBsZcfzbYzxVO.get("h25l6"));
        bw.put("zcfzbh38l5", cwbbBsZcfzbYzxVO.get("h38l5"));
        bw.put("zcfzbh38l6", cwbbBsZcfzbYzxVO.get("h38l6"));
        bw.put("lrbh1l2", cwbbBsLrbYzxVO.get("h1l2"));
        bw.put("lrbh1l3", cwbbBsLrbYzxVO.get("h1l3"));
        bw.put("lrbh22l2", cwbbBsLrbYzxVO.get("h22l2"));
        bw.put("lrbh22l3", cwbbBsLrbYzxVO.get("h22l3"));
        bw.put("lrbh24l2", cwbbBsLrbYzxVO.get("h24l2"));
        bw.put("lrbh24l3", cwbbBsLrbYzxVO.get("h24l3"));
        bw.put("xjllbh10l2", cwbbBsXjllbYzxVO.get("h10l2"));
        bw.put("xjllbh10l3", cwbbBsXjllbYzxVO.get("h10l3"));
        bw.put("xjllbh34l2", cwbbBsXjllbYzxVO.get("h34l2"));
        bw.put("xjllbh34l3", cwbbBsXjllbYzxVO.get("h34l3"));
        bw.put("xjllbh35l2", cwbbBsXjllbYzxVO.get("h35l2"));
        bw.put("xjllbh35l3", cwbbBsXjllbYzxVO.get("h35l3"));
        bw.put("syzqyh26l6", cwbbBsSyzqybdbYzxVO.get("h26l6"));
        bw.put("syzqyh26l12", cwbbBsSyzqybdbYzxVO.get("h26l12"));
        bw.put("syzqyh26l14", cwbbBsSyzqybdbYzxVO.get("h26l14"));
        bw.put("syzqyh26l15", cwbbBsSyzqybdbYzxVO.get("h26l15"));

        ZnsbNssbYsbjgbCwbbDO ysbjgbCwbbDO = new ZnsbNssbYsbjgbCwbbDO();
        ysbjgbCwbbDO.setUuid(GyUtils.getUuid());
        ysbjgbCwbbDO.setSbrwuuid(sbrwuuid);
        ysbjgbCwbbDO.setDjxh(djxh);
        ysbjgbCwbbDO.setYzpzzlDm(YzpzzlEnum.CWBB_YBQYKJZZ.getDm());
        ysbjgbCwbbDO.setSkssqq(DateUtils.strToDate(sssqQ));
        ysbjgbCwbbDO.setSkssqz(DateUtils.strToDate(sssqZ));
        ysbjgbCwbbDO.setBusinessclob("");
        ysbjgbCwbbDO.setNsqxDm(nsqxDm);
        ysbjgbCwbbDO.setZsxmDm("");
        ysbjgbCwbbDO.setBw(JsonUtils.toJson(bw));
        ysbjgbCwbbDO.setYwqdDm("SJJG");
        ysbjgbCwbbDO.setLrrq(new Date());
        if (GyUtils.isNotNull(ksbjy)) {
            ysbjgbCwbbDO.setKsbbz("N");
            ysbjgbCwbbDO.setNote(JsonUtils.toJson(ksbjy));
        } else {
            ysbjgbCwbbDO.setKsbbz("Y");
        }

        CwbbBsSaveSbtjYzxReqVO saveYzxReqVO = new CwbbBsSaveSbtjYzxReqVO();
        saveYzxReqVO.setCwbbBsJbxxVO(cwbbBsJbxxVO);
        saveYzxReqVO.setCwbbBsSlxxVO(cwbbBsSlxxVO);
        saveYzxReqVO.setCwbbBsZcfzbYzxVO(cwbbBsZcfzbYzxVO);
        saveYzxReqVO.setCwbbBsLrbYzxVO(cwbbBsLrbYzxVO);
        saveYzxReqVO.setCwbbBsXjllbYzxVO(cwbbBsXjllbYzxVO);
        saveYzxReqVO.setCwbbBsSyzqybdbYzxVO(cwbbBsSyzqybdbYzxVO);
        saveYzxReqVO.setDjxh(djxh);
        saveYzxReqVO.setXzqhszDm(xzqhszDm);
        saveYzxReqVO.setNsrmc(nsrmc);
        saveYzxReqVO.setNsrsbh(nsrsbh);
        saveYzxReqVO.setSkssqq(sssqQ);
        saveYzxReqVO.setSkssqz(sssqZ);
        saveYzxReqVO.setGzsbbz("");
        ysbjgbCwbbDO.setBusinessclob(JsonUtils.toJson(saveYzxReqVO));
        ZnsbNssbYsbjgbCwbbDO ysbsj = znsbNssbYsbjgbCwbbMapper.queryYsbsj(djxh, sssqQ, sssqZ);
        if (GyUtils.isNotNull(ysbsj)) {
            znsbNssbYsbjgbCwbbMapper.deleteById(ysbsj.getUuid());
        }
        znsbNssbYsbjgbCwbbMapper.insert(ysbjgbCwbbDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cwbbsjjg(String sbny, String qydmz, String djxh) {

        String gslx = getGsxl(qydmz);

        if (GyUtils.isNull(gslx)) {
            throw new ServiceException(-1, "获取公司类型异常");
        }

        String dqsj = sbny;
        int year = Integer.parseInt(sbny.substring(0, 4));
        String month = sbny.substring(4, 6);
        String sqnmsj = String.valueOf(year - 1) + "13";
        String sqsj = String.valueOf(year - 1) + month;
        boolean ssqBz = false;
        String ssqnmsj = "";
        if (MOUTHLIST.contains(String.valueOf(month))) {
            ssqBz = true;
            ssqnmsj = String.valueOf(year - 2) + "13";
        }

        List<String> sszqList = new ArrayList<>();
        sszqList.add(dqsj);
        sszqList.add(sqnmsj);
        sszqList.add(sqsj);
        if (ssqBz) {
            sszqList.add(ssqnmsj);
        }
        sszqList = sszqList.stream()
                .distinct()
                .collect(Collectors.toList());

//        znsbTzzxQysdsyjtzService.syncYjtzSxLssjBySszqList(qydmz, sszqList);
        List<ZnsbTzzxBpcjysjDO> jysjList = znsbTzzxBpcjysjMapper.getByGshList(qydmz, sszqList, gslx, BBMCLIST);
        if (GyUtils.isNotNull(jysjList)) {
            String zlbscjuuid = "";
            String zlbscjuuid13 = "";
            List<ZnsbCwbbZlbscjbDO> znsbCwbbZlbscjbDOList = new ArrayList<>();
            if (ssqBz) {

                ZnsbCwbbZlbscjbDO znsbCwbbZlbscjbDO = new ZnsbCwbbZlbscjbDO();
                zlbscjuuid = MD5Utils.md5(qydmz + djxh + dqsj);
                zlbscjuuid13 = MD5Utils.md5(qydmz + djxh + sqnmsj);
                znsbCwbbZlbscjbMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyZcfzbyzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyLrbyzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyZcfzbwzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyLrbwzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyXjllbMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqySyzqyMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbNssbGdzcbMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbNssbWxzcbMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbNssbCqdtfybMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbZlbscjbDO.setZlbsuuid(GyUtils.getUuid());
                znsbCwbbZlbscjbDO.setZlbscjuuid(zlbscjuuid);
                znsbCwbbZlbscjbDO.setDjxh(djxh);
                znsbCwbbZlbscjbDO.setSszq(dqsj);
                znsbCwbbZlbscjbDO.setQydmz(qydmz);
                znsbCwbbZlbscjbDO.setZfbz1("N");
                znsbCwbbZlbscjbDO.setYwqdDm("SJJG");
                znsbCwbbZlbscjbDO.setLrrq(new Date());
                znsbCwbbZlbscjbDO.setLrrsfid("SJJG");
                znsbCwbbZlbscjbDOList.add(znsbCwbbZlbscjbDO);

                ZnsbCwbbZlbscjbDO znsbCwbbZlbscjbDO13 = new ZnsbCwbbZlbscjbDO();
                znsbCwbbZlbscjbMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbCwbbQykjzzybqyZcfzbyzxMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbCwbbQykjzzybqyLrbyzxMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbCwbbQykjzzybqyZcfzbwzxMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbCwbbQykjzzybqyLrbwzxMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbCwbbQykjzzybqyXjllbMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbCwbbQykjzzybqySyzqyMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbNssbGdzcbMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbNssbWxzcbMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbNssbCqdtfybMapper.deleteByZlbscjuuid(zlbscjuuid13);
                znsbCwbbZlbscjbDO13.setZlbsuuid(GyUtils.getUuid());
                znsbCwbbZlbscjbDO13.setZlbscjuuid(zlbscjuuid13);
                znsbCwbbZlbscjbDO13.setDjxh(djxh);
                znsbCwbbZlbscjbDO13.setSszq(sqnmsj);
                znsbCwbbZlbscjbDO13.setQydmz(qydmz);
                znsbCwbbZlbscjbDO13.setZfbz1("N");
                znsbCwbbZlbscjbDO13.setYwqdDm("SJJG");
                znsbCwbbZlbscjbDO13.setLrrq(new Date());
                znsbCwbbZlbscjbDO13.setLrrsfid("SJJG");
                znsbCwbbZlbscjbDOList.add(znsbCwbbZlbscjbDO13);

            } else {
                ZnsbCwbbZlbscjbDO znsbCwbbZlbscjbDO = new ZnsbCwbbZlbscjbDO();
                zlbscjuuid = MD5Utils.md5(qydmz + djxh + dqsj);
                znsbCwbbZlbscjbMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyZcfzbyzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyLrbyzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyZcfzbwzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyLrbwzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqyXjllbMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbQykjzzybqySyzqyMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbNssbGdzcbMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbNssbWxzcbMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbNssbCqdtfybMapper.deleteByZlbscjuuid(zlbscjuuid);
                znsbCwbbZlbscjbDO.setZlbsuuid(GyUtils.getUuid());
                znsbCwbbZlbscjbDO.setZlbscjuuid(zlbscjuuid);
                znsbCwbbZlbscjbDO.setDjxh(djxh);
                znsbCwbbZlbscjbDO.setSszq(dqsj);
                znsbCwbbZlbscjbDO.setQydmz(qydmz);
                znsbCwbbZlbscjbDO.setZfbz1("N");
                znsbCwbbZlbscjbDO.setYwqdDm("SJJG");
                znsbCwbbZlbscjbDO.setLrrq(new Date());
                znsbCwbbZlbscjbDO.setLrrsfid("SJJG");
                znsbCwbbZlbscjbDOList.add(znsbCwbbZlbscjbDO);
            }
            znsbCwbbZlbscjbService.saveBatch(znsbCwbbZlbscjbDOList);

            Map<String, List<ZnsbTzzxBpcjysjDO>> bpcMap = jysjList.stream().collect(Collectors.groupingBy(ZnsbTzzxBpcjysjDO::getSszq));
            List<ZnsbTzzxBpcjysjDO> dqsjList = bpcMap.get(dqsj);
            List<Map<String, Object>> zcfzbJsonList = JsonUtils.toMapList(ZCFZB);
            List<ZnsbCwbbQykjzzybqyZcfzbyzxSjtqVO> zcfzbyzxVOList = BeanUtils.toBean(zcfzbJsonList, ZnsbCwbbQykjzzybqyZcfzbyzxSjtqVO.class);
            List<ZnsbCwbbQykjzzybqyZcfzbyzxSjtqVO> zcfzbyzxVO13List = BeanUtils.toBean(zcfzbJsonList, ZnsbCwbbQykjzzybqyZcfzbyzxSjtqVO.class);
            List<Map<String, Object>> lrbJsonList = JsonUtils.toMapList(LRB);
            List<ZnsbCwbbQykjzzybqyLrbyzxSjtqVO> lrbVOList = BeanUtils.toBean(lrbJsonList, ZnsbCwbbQykjzzybqyLrbyzxSjtqVO.class);
            List<ZnsbCwbbQykjzzybqyLrbyzxSjtqVO> lrbVO13List = BeanUtils.toBean(lrbJsonList, ZnsbCwbbQykjzzybqyLrbyzxSjtqVO.class);

            List<Map<String, Object>> zcfzbwxzJsonList = JsonUtils.toMapList(ZCFZBWZX);
            List<ZnsbCwbbQykjzzybqyZcfzbwzxSjtqVO> zcfzbwzxVOList = BeanUtils.toBean(zcfzbwxzJsonList, ZnsbCwbbQykjzzybqyZcfzbwzxSjtqVO.class);
            List<ZnsbCwbbQykjzzybqyZcfzbwzxSjtqVO> zcfzbwzxVO13List = BeanUtils.toBean(zcfzbwxzJsonList, ZnsbCwbbQykjzzybqyZcfzbwzxSjtqVO.class);
            List<Map<String, Object>> lrbwzxJsonList = JsonUtils.toMapList(LRBWZX);
            List<ZnsbCwbbQykjzzybqyLrbwzxSjtqVO> lrbwzxVOList = BeanUtils.toBean(lrbwzxJsonList, ZnsbCwbbQykjzzybqyLrbwzxSjtqVO.class);
            List<ZnsbCwbbQykjzzybqyLrbwzxSjtqVO> lrbwzxVO13List = BeanUtils.toBean(lrbwzxJsonList, ZnsbCwbbQykjzzybqyLrbwzxSjtqVO.class);

            List<Map<String, Object>> xjlbJsonList = JsonUtils.toMapList(XJLB);
            List<ZnsbCwbbQykjzzybqyXjllbSjtqVO> xjlVOList = BeanUtils.toBean(xjlbJsonList, ZnsbCwbbQykjzzybqyXjllbSjtqVO.class);
            List<ZnsbCwbbQykjzzybqyXjllbSjtqVO> xjlVO13List = BeanUtils.toBean(xjlbJsonList, ZnsbCwbbQykjzzybqyXjllbSjtqVO.class);
            List<Map<String, Object>> syzqybList = JsonUtils.toMapList(SYZQYB);
            List<ZnsbCwbbQykjzzybqySyzqySjtqVO> syzqyVOList = BeanUtils.toBean(syzqybList, ZnsbCwbbQykjzzybqySyzqySjtqVO.class);
            List<ZnsbCwbbQykjzzybqySyzqySjtqVO> syzqyVO13List = BeanUtils.toBean(syzqybList, ZnsbCwbbQykjzzybqySyzqySjtqVO.class);

            List<Map<String, Object>> gdzcList = JsonUtils.toMapList(GDZC);
            List<ZnsbNssbGdzcbSjtqVO> gdzcVOList = BeanUtils.toBean(gdzcList, ZnsbNssbGdzcbSjtqVO.class);
            List<ZnsbNssbGdzcbSjtqVO> gdzcVO13List = BeanUtils.toBean(gdzcList, ZnsbNssbGdzcbSjtqVO.class);

            List<Map<String, Object>> wxzcList = JsonUtils.toMapList(WXZC);
            List<ZnsbNssbWxzcbSjtqVO> wxzcVOList = BeanUtils.toBean(wxzcList, ZnsbNssbWxzcbSjtqVO.class);
            List<ZnsbNssbWxzcbSjtqVO> wxzcVO13List = BeanUtils.toBean(wxzcList, ZnsbNssbWxzcbSjtqVO.class);

            List<Map<String, Object>> cqdtfyList = JsonUtils.toMapList(CQDTFY);
            List<ZnsbNssbCqdtfybSjtqVO> cqdtfyVOList = BeanUtils.toBean(cqdtfyList, ZnsbNssbCqdtfybSjtqVO.class);
            List<ZnsbNssbCqdtfybSjtqVO> cqdtfyVO13List = BeanUtils.toBean(cqdtfyList, ZnsbNssbCqdtfybSjtqVO.class);


            if (ssqBz) {
                List<ZnsbTzzxBpcjysjDO> sqnmsjList = bpcMap.get(sqnmsj);
                if (GyUtils.isNotNull(sqnmsjList)) {
                    bbjg(zcfzbyzxVO13List, lrbVO13List, xjlVO13List, syzqyVO13List, zcfzbwzxVO13List, lrbwzxVO13List, gdzcVO13List, wxzcVO13List, cqdtfyVO13List, sqnmsjList, "3", zlbscjuuid, zlbscjuuid13);
                }
                List<ZnsbTzzxBpcjysjDO> ssqnmsjList = bpcMap.get(ssqnmsj);
                if (GyUtils.isNotNull(ssqnmsjList)) {
                    bbjg(zcfzbyzxVO13List, lrbVO13List, xjlVO13List, syzqyVO13List, zcfzbwzxVO13List, lrbwzxVO13List, gdzcVO13List, wxzcVO13List, cqdtfyVO13List, ssqnmsjList, "4", zlbscjuuid, zlbscjuuid13);
                }
                zcfzbyzxVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                lrbVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                zcfzbwzxVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                lrbwzxVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                xjlVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                syzqyVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                gdzcVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                wxzcVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                cqdtfyVO13List.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
                if (GyUtils.isNotNull(zcfzbyzxVO13List)) {
                    znsbCwbbQykjzzybqyZcfzbyzxService.saveBatch(BeanUtils.toBean(zcfzbyzxVO13List, ZnsbCwbbQykjzzybqyZcfzbyzxDO.class));
                }
                if (GyUtils.isNotNull(lrbVO13List)) {
                    znsbCwbbQykjzzybqyLrbyzxService.saveBatch(BeanUtils.toBean(lrbVO13List, ZnsbCwbbQykjzzybqyLrbyzxDO.class));
                }
                if (GyUtils.isNotNull(zcfzbwzxVO13List)) {
                    znsbCwbbQykjzzybqyZcfzbwzxService.saveBatch(BeanUtils.toBean(zcfzbwzxVO13List, ZnsbCwbbQykjzzybqyZcfzbwzxDO.class));
                }
                if (GyUtils.isNotNull(lrbwzxVO13List)) {
                    znsbCwbbQykjzzybqyLrbwzxService.saveBatch(BeanUtils.toBean(lrbwzxVO13List, ZnsbCwbbQykjzzybqyLrbwzxDO.class));
                }
                if (GyUtils.isNotNull(xjlVO13List)) {
                    znsbCwbbQykjzzybqyXjllbService.saveBatch(BeanUtils.toBean(xjlVO13List, ZnsbCwbbQykjzzybqyXjllbDO.class));
                }
                if (GyUtils.isNotNull(syzqyVO13List)) {
                    znsbCwbbQykjzzybqySyzqyService.saveBatch(BeanUtils.toBean(syzqyVO13List, ZnsbCwbbQykjzzybqySyzqyDO.class));
                }
                if (GyUtils.isNotNull(gdzcVO13List)) {
                    znsbNssbGdzcbService.saveBatch(BeanUtils.toBean(gdzcVO13List, ZnsbNssbGdzcbDO.class));
                }
                if (GyUtils.isNotNull(wxzcVO13List)) {
                    znsbNssbWxzcbService.saveBatch(BeanUtils.toBean(wxzcVO13List, ZnsbNssbWxzcbDO.class));
                }
                if (GyUtils.isNotNull(cqdtfyVO13List)) {
                    znsbNssbCqdtfybService.saveBatch(BeanUtils.toBean(cqdtfyVO13List, ZnsbNssbCqdtfybDO.class));
                }
            }

            if (GyUtils.isNotNull(dqsjList)) {
                bbjg(zcfzbyzxVOList, lrbVOList, xjlVOList, syzqyVOList, zcfzbwzxVOList, lrbwzxVOList, gdzcVOList, wxzcVOList, cqdtfyVOList, dqsjList, "0", zlbscjuuid, zlbscjuuid13);
            }

            List<ZnsbTzzxBpcjysjDO> sqnmsjList = bpcMap.get(sqnmsj);
            if (GyUtils.isNotNull(sqnmsjList)) {
                bbjg(zcfzbyzxVOList, lrbVOList, xjlVOList, syzqyVOList, zcfzbwzxVOList, lrbwzxVOList, gdzcVOList, wxzcVOList, cqdtfyVOList, sqnmsjList, "1", zlbscjuuid, zlbscjuuid13);
            }

            List<ZnsbTzzxBpcjysjDO> sqsjList = bpcMap.get(sqsj);
            if (GyUtils.isNotNull(sqsjList)) {
                bbjg(zcfzbyzxVOList, lrbVOList, xjlVOList, syzqyVOList, zcfzbwzxVOList, lrbwzxVOList, gdzcVOList, wxzcVOList, cqdtfyVOList, sqsjList, "2", zlbscjuuid, zlbscjuuid13);
            }

            zcfzbyzxVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            lrbVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            zcfzbwzxVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            lrbwzxVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            xjlVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            syzqyVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            gdzcVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            wxzcVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            cqdtfyVOList.removeIf(vo -> GyUtils.isNull(vo.getZlbscjuuid()));
            if (GyUtils.isNotNull(zcfzbyzxVOList)) {
                znsbCwbbQykjzzybqyZcfzbyzxService.saveBatch(BeanUtils.toBean(zcfzbyzxVOList, ZnsbCwbbQykjzzybqyZcfzbyzxDO.class));
            }
            if (GyUtils.isNotNull(lrbVOList)) {
                znsbCwbbQykjzzybqyLrbyzxService.saveBatch(BeanUtils.toBean(lrbVOList, ZnsbCwbbQykjzzybqyLrbyzxDO.class));
            }
            if (GyUtils.isNotNull(zcfzbwzxVOList)) {
                znsbCwbbQykjzzybqyZcfzbwzxService.saveBatch(BeanUtils.toBean(zcfzbwzxVOList, ZnsbCwbbQykjzzybqyZcfzbwzxDO.class));
            }
            if (GyUtils.isNotNull(lrbwzxVOList)) {
                znsbCwbbQykjzzybqyLrbwzxService.saveBatch(BeanUtils.toBean(lrbwzxVOList, ZnsbCwbbQykjzzybqyLrbwzxDO.class));
            }
            if (GyUtils.isNotNull(xjlVOList)) {
                znsbCwbbQykjzzybqyXjllbService.saveBatch(BeanUtils.toBean(xjlVOList, ZnsbCwbbQykjzzybqyXjllbDO.class));
            }
            if (GyUtils.isNotNull(syzqyVOList)) {
                znsbCwbbQykjzzybqySyzqyService.saveBatch(BeanUtils.toBean(syzqyVOList, ZnsbCwbbQykjzzybqySyzqyDO.class));
            }
            if (GyUtils.isNotNull(gdzcVOList)) {
                znsbNssbGdzcbService.saveBatch(BeanUtils.toBean(gdzcVOList, ZnsbNssbGdzcbDO.class));
            }
            if (GyUtils.isNotNull(wxzcVOList)) {
                znsbNssbWxzcbService.saveBatch(BeanUtils.toBean(wxzcVOList, ZnsbNssbWxzcbDO.class));
            }
            if (GyUtils.isNotNull(cqdtfyVOList)) {
                znsbNssbCqdtfybService.saveBatch(BeanUtils.toBean(cqdtfyVOList, ZnsbNssbCqdtfybDO.class));
            }
        }
    }


    public String getGsxl(String qydmz) {
        String gslx = null;
        if (GyUtils.isNull(qydmz)) {
            return gslx;
        } else {
            final String gshtb = qydmz.substring(0, 2);
            final String gshwb = qydmz.substring(qydmz.length() - 2, qydmz.length());
            if ("00".equals(gshwb)) {
                List<Map<String, Object>> zhxxList = CacheUtils.getTableData("dlfw_lq_zhxx");
                if (GyUtils.isNotNull(zhxxList)) {
                    List<Map<String, Object>> qydmList = zhxxList.stream()
                            .filter(o -> {
                                final String qydm = String.valueOf(o.get("qydm"));
                                return GyUtils.isNotNull(qydm) && gshtb.equals(qydm.substring(0, 2));
                            })
                            .collect(Collectors.toList());
                    if (!GyUtils.isNull(qydmList)) {
                        if (qydmList.size() > 1) {
                            gslx = "GH";
                        } else {
                            gslx = "E";
                        }
                    } else {
                        throw new ServiceException(-1, "获取企业代码值异常");
                    }
                } else {
                    throw new ServiceException(-1, "获取企业代码值异常");
                }

            } else {
                gslx = "S";
            }
        }
        return gslx;
    }

    @Override
    public CwbbBsInitYzxResVO initData(CwbbBsInitYzxReqVO reqVO) {

        CwbbBsInitYzxResVO resVO = new CwbbBsInitYzxResVO();
        final String djxh = reqVO.getDjxh();
        final Date sssqQ = DateUtils.strToDate(reqVO.getSssqQ());
        final Date sssqZ = DateUtils.strToDate(reqVO.getSssqZ());
        final String sjqzbz = reqVO.getSjqzbz();
        final String sbrwCkbz = reqVO.getSbrwCkbz();
        final String gzsbbz = reqVO.getGzsbbz();
        final String zxbz = reqVO.getZxbz();
        final String nsqxDm = reqVO.getNsqxDm();
        Map<String, String> zcfzbMap = new HashMap<>();
        Map<String, String> lrbMap = new HashMap<>();
        Map<String, String> zcfzbwzxMap = new HashMap<>();
        Map<String, String> lrbwzxMap = new HashMap<>();
        Map<String, String> xjllbMap = new HashMap<>();
        Map<String, String> syzqyMap = new HashMap<>();
        Map<String, String> qykjzzMap = new HashMap<>();
        List<CwbbBsSyzqybdbYzxGridVO> cwbbBsSyzqybdbYzxGrid = new ArrayList<>();
        if (("Y".equals(sbrwCkbz) || "Y".equals(gzsbbz)) && !"1".equals(sjqzbz)) {
            SbCwbbZlbscjbDO zbxx = sbCwbbZlbscjbMapper.queryXx(djxh, sssqQ, sssqZ);

            if (GyUtils.isNotNull(zbxx)) {
                final String zlbscjuuid = zbxx.getZlbscjuuid();
                if ("yzx".equals(zxbz)) {
                    List<SbCwbbQykjzzybqyZcfzbyzxDO> zcfzbDOList = sbCwbbQykjzzybqyZcfzbyzxMapper.queryZcfzb(zlbscjuuid);
                    if (GyUtils.isNotNull(zcfzbDOList)) {
                        zcfzbMap = SsfwglUtil.toGrid(zcfzbDOList, true);
                    }
                    List<SbCwbbQykjzzybqyLrbyzxDO> lrbyzxDOList = sbCwbbQykjzzybqyLrbyzxMapper.queryLrb(zlbscjuuid);
                    if (GyUtils.isNotNull(lrbyzxDOList)) {
                        lrbMap = SsfwglUtil.toGrid(lrbyzxDOList, true);
                    }
                }


                List<SbCwbbQykjzzybqyXjllbDO> xjllbDOList = sbCwbbQykjzzybqyXjllbMapper.queryXjllb(zlbscjuuid);
                if (GyUtils.isNotNull(xjllbDOList)) {
                    xjllbMap = SsfwglUtil.toGrid(xjllbDOList, true);
                }

                List<SbCwbbQykjzzybqySyzqyDO> syzqyDOList = sbCwbbQykjzzybqySyzqyMapper.querySyzqyb(zlbscjuuid);
                if (GyUtils.isNotNull(syzqyDOList)) {
                    cwbbBsSyzqybdbYzxGrid = BeanUtils.toBean(syzqyDOList, CwbbBsSyzqybdbYzxGridVO.class);
                    syzqyMap = SsfwglUtil.toGrid(syzqyDOList, true);
                }
                List<SbZlbsQykjzzfzDO> qykjzzDOList = sbZlbsQykjzzfzMapper.queryQykjzz(zlbscjuuid);
                if (GyUtils.isNotNull(qykjzzDOList)) {
                    SbZlbsQykjzzfzDO qykjzz = qykjzzDOList.get(0);
                    qykjzzMap.put("bz11", qykjzz.getBz11());
                    qykjzzMap.put("bz12", qykjzz.getBz12());
                    qykjzzMap.put("bz13", qykjzz.getBz13());
                    qykjzzMap.put("bz14", qykjzz.getBz14());
                    qykjzzMap.put("bz15", qykjzz.getBz15());
                    qykjzzMap.put("bz16", qykjzz.getBz16());
                    qykjzzMap.put("bz17", qykjzz.getBz17());
                    qykjzzMap.put("bz18", qykjzz.getBz18());

                }
            }
        } else {

            Date snSkssqq = ZzppUtil.getLastYearMonthFirstDay(sssqQ);
            Date snSkssqz = ZzppUtil.getLastYearMonthLastDay(sssqZ);
            SbCwbbZlbscjbDO snTqZbxx = sbCwbbZlbscjbMapper.queryXx(djxh, snSkssqq, snSkssqz);
            List<SbCwbbQykjzzybqyLrbyzxDO> lrbyzxSntqDOList = new ArrayList<>();
            List<SbCwbbQykjzzybqyXjllbDO> xjllbSntqDOList = new ArrayList<>();

            if (GyUtils.isNotNull(snTqZbxx)) {
                final String zlbscjuuid = snTqZbxx.getZlbscjuuid();
                lrbyzxSntqDOList = sbCwbbQykjzzybqyLrbyzxMapper.queryLrb(zlbscjuuid);
                xjllbSntqDOList = sbCwbbQykjzzybqyXjllbMapper.queryXjllb(zlbscjuuid);
            }

            Date snnc = ZzppUtil.getStartOfLastYear(sssqQ);
            Date snnm = ZzppUtil.getEndOfLastYear(sssqQ);
            SbCwbbZlbscjbDO snNbZbxx = sbCwbbZlbscjbMapper.queryXx(djxh, snnc, snnm);
            List<SbCwbbQykjzzybqyZcfzbyzxDO> zcfzbSnnmDOList = new ArrayList<>();
            List<SbCwbbQykjzzybqySyzqyDO> syzqySnnmDOList = new ArrayList<>();
            if (GyUtils.isNotNull(snNbZbxx)) {
                final String zlbscjuuid = snNbZbxx.getZlbscjuuid();
                zcfzbSnnmDOList = sbCwbbQykjzzybqyZcfzbyzxMapper.queryZcfzb(zlbscjuuid);
                syzqySnnmDOList = sbCwbbQykjzzybqySyzqyMapper.querySyzqyb(zlbscjuuid);
            } else {
                Date sn10 = ZzppUtil.getOctoberFirstLastYear(sssqQ);
                SbCwbbZlbscjbDO snJbZbxx = sbCwbbZlbscjbMapper.queryXx(djxh, sn10, snnm);
                if (GyUtils.isNotNull(snJbZbxx)) {
                    final String zlbscjuuid = snJbZbxx.getZlbscjuuid();
                    zcfzbSnnmDOList = sbCwbbQykjzzybqyZcfzbyzxMapper.queryZcfzb(zlbscjuuid);
                    syzqySnnmDOList = sbCwbbQykjzzybqySyzqyMapper.querySyzqyb(zlbscjuuid);
                }
            }

            String sszq = "";
            if ("10".equals(nsqxDm)) {
                sszq = reqVO.getSssqZ().replace("-", "").substring(0, 4) + "13";
            } else {
                sszq = reqVO.getSssqZ().replace("-", "").substring(0, 6);
            }

            ZnsbCwbbZlbscjbDO zbxx = znsbCwbbZlbscjbMapper.queryXxBySszq(djxh, sszq);
            List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> zcfzbDOList = new ArrayList<>();
            List<ZnsbCwbbQykjzzybqyLrbyzxDO> lrbyzxDOList = new ArrayList<>();
            List<ZnsbCwbbQykjzzybqyZcfzbwzxDO> zcfzbwzxDOList = new ArrayList<>();
            List<ZnsbCwbbQykjzzybqyLrbwzxDO> lrbwzxDOList = new ArrayList<>();
            List<ZnsbCwbbQykjzzybqyXjllbDO> xjllbDOList = new ArrayList<>();
            List<ZnsbCwbbQykjzzybqySyzqyDO> syzqyDOList = new ArrayList<>();
            if (GyUtils.isNotNull(zbxx)) {
                final String zlbscjuuid = zbxx.getZlbscjuuid();
                if ("yzx".equals(zxbz)) {
                    zcfzbDOList = znsbCwbbQykjzzybqyZcfzbyzxMapper.queryZcfzb(zlbscjuuid);
                    lrbyzxDOList = znsbCwbbQykjzzybqyLrbyzxMapper.queryLrb(zlbscjuuid);
                } else {
                    zcfzbwzxDOList = znsbCwbbQykjzzybqyZcfzbwzxMapper.queryZcfzb(zlbscjuuid);
                    lrbwzxDOList = znsbCwbbQykjzzybqyLrbwzxMapper.queryLrb(zlbscjuuid);
                }

                xjllbDOList = znsbCwbbQykjzzybqyXjllbMapper.queryXjllb(zlbscjuuid);
                syzqyDOList = znsbCwbbQykjzzybqySyzqyMapper.querySyzqyb(zlbscjuuid);
            } else {
                if ("yzx".equals(zxbz)) {
                    List<Map<String, Object>> zcfzbJsonList = JsonUtils.toMapList(ZCFZB);
                    zcfzbDOList = BeanUtils.toBean(zcfzbJsonList, ZnsbCwbbQykjzzybqyZcfzbyzxDO.class);
                    List<Map<String, Object>> lrbJsonList = JsonUtils.toMapList(LRB);
                    lrbyzxDOList = BeanUtils.toBean(lrbJsonList, ZnsbCwbbQykjzzybqyLrbyzxDO.class);
                } else {
                    List<Map<String, Object>> zcfzbwzxJsonList = JsonUtils.toMapList(ZCFZBWZX);
                    zcfzbwzxDOList = BeanUtils.toBean(zcfzbwzxJsonList, ZnsbCwbbQykjzzybqyZcfzbwzxDO.class);
                    List<Map<String, Object>> lrbwzxJsonList = JsonUtils.toMapList(LRBWZX);
                    lrbwzxDOList = BeanUtils.toBean(lrbwzxJsonList, ZnsbCwbbQykjzzybqyLrbwzxDO.class);
                }
                List<Map<String, Object>> xjlbJsonList = JsonUtils.toMapList(XJLB);
                xjllbDOList = BeanUtils.toBean(xjlbJsonList, ZnsbCwbbQykjzzybqyXjllbDO.class);
                List<Map<String, Object>> syzqybList = JsonUtils.toMapList(SYZQYB);
                syzqyDOList = BeanUtils.toBean(syzqybList, ZnsbCwbbQykjzzybqySyzqyDO.class);

            }
            if (GyUtils.isNull(zcfzbDOList)) {
                List<Map<String, Object>> zcfzbJsonList = new ArrayList<>();
                if ("yzx".equals(zxbz)) {
                    zcfzbJsonList = JsonUtils.toMapList(ZCFZB);
                }
                zcfzbDOList = BeanUtils.toBean(zcfzbJsonList, ZnsbCwbbQykjzzybqyZcfzbyzxDO.class);
            }
            if (GyUtils.isNull(lrbyzxDOList)) {
                List<Map<String, Object>> lrbJsonList = new ArrayList<>();
                if ("yzx".equals(zxbz)) {
                    lrbJsonList = JsonUtils.toMapList(LRB);
                }
                lrbyzxDOList = BeanUtils.toBean(lrbJsonList, ZnsbCwbbQykjzzybqyLrbyzxDO.class);
            }
            if (GyUtils.isNull(zcfzbwzxDOList)) {
                List<Map<String, Object>> zcfzbwzxJsonList = new ArrayList<>();
                if ("wzx".equals(zxbz)) {
                    zcfzbwzxJsonList = JsonUtils.toMapList(ZCFZBWZX);
                }
                zcfzbwzxDOList = BeanUtils.toBean(zcfzbwzxJsonList, ZnsbCwbbQykjzzybqyZcfzbwzxDO.class);
            }
            if (GyUtils.isNull(lrbwzxDOList)) {
                List<Map<String, Object>> lrbwzxJsonList = new ArrayList<>();
                if ("yzx".equals(zxbz)) {
                    lrbwzxJsonList = JsonUtils.toMapList(LRBWZX);
                }
                lrbwzxDOList = BeanUtils.toBean(lrbwzxJsonList, ZnsbCwbbQykjzzybqyLrbwzxDO.class);
            }
            if (GyUtils.isNull(xjllbDOList)) {
                List<Map<String, Object>> xjlbJsonList = JsonUtils.toMapList(XJLB);
                xjllbDOList = BeanUtils.toBean(xjlbJsonList, ZnsbCwbbQykjzzybqyXjllbDO.class);
            }
            if (GyUtils.isNull(syzqyDOList)) {
                List<Map<String, Object>> syzqybList = JsonUtils.toMapList(SYZQYB);
                syzqyDOList = BeanUtils.toBean(syzqybList, ZnsbCwbbQykjzzybqySyzqyDO.class);
            }

            if (GyUtils.isNotNull(zcfzbDOList)) {
                Map<String, Map<String, String>> zzppZcfzb = new HashMap<>();
                Map<String, String> keyMap = new HashMap<>();
                keyMap.put("snnmyeZc", "qmyeZc");
                keyMap.put("snnmyeQy", "qmyeQy");
                zzppZcfzb.put("ewbhxh", keyMap);
                ZzppUtil.mergeListsBasedOnKey(zcfzbDOList, zcfzbSnnmDOList, zzppZcfzb);
                zcfzbMap = SsfwglUtil.toGrid(zcfzbDOList, true);
            }

            if (GyUtils.isNotNull(lrbyzxDOList)) {
                Map<String, Map<String, String>> zzppLrb = new HashMap<>();
                Map<String, String> keyMap = new HashMap<>();
                keyMap.put("sqje1", "bqje");
                zzppLrb.put("ewbhxh", keyMap);
                ZzppUtil.mergeListsBasedOnKey(lrbyzxDOList, lrbyzxSntqDOList, zzppLrb);
                lrbMap = SsfwglUtil.toGrid(lrbyzxDOList, true);
            }

            if (GyUtils.isNotNull(zcfzbwzxDOList)) {
                zcfzbwzxMap = SsfwglUtil.toGrid(zcfzbwzxDOList, true);
            }

            if (GyUtils.isNotNull(lrbwzxDOList)) {
                lrbwzxMap = SsfwglUtil.toGrid(lrbwzxDOList, true);
            }

            if (GyUtils.isNotNull(xjllbDOList)) {
                Map<String, Map<String, String>> zzppXjllb = new HashMap<>();
                Map<String, String> keyMap = new HashMap<>();
                keyMap.put("sqje1", "bqje");
                zzppXjllb.put("ewbhxh", keyMap);
                ZzppUtil.mergeListsBasedOnKey(xjllbDOList, xjllbSntqDOList, zzppXjllb);

                xjllbMap = SsfwglUtil.toGrid(xjllbDOList, true);
            }

            if (GyUtils.isNotNull(syzqyDOList)) {
                Map<String, Map<String, String>> zzppXjllb = new HashMap<>();
                Map<String, String> keyMap = new HashMap<>();
                keyMap.put("snsszbhgb", "bnsszbhgb");
                keyMap.put("qtqygjyxg2", "qtqygjyxg1");
                keyMap.put("qtqygjyxz2", "qtqygjyxz1");
                keyMap.put("qtqygjqt2", "qtqygjqt1");
                keyMap.put("snzbgj", "bnzbgj");
                keyMap.put("snjkcg", "bnjkcg");
                keyMap.put("snqtzhsy", "bnqtzhsy");
                keyMap.put("snzxcb", "bnzxcb");
                keyMap.put("snyygj", "bnyygj");
                keyMap.put("snwfply", "bnwfply");
                keyMap.put("snsyzqyhj", "bnsyzqyhj");
                zzppXjllb.put("ewbhxh", keyMap);
                ZzppUtil.mergeListsBasedOnKey(syzqyDOList, syzqySnnmDOList, zzppXjllb);

                cwbbBsSyzqybdbYzxGrid = BeanUtils.toBean(syzqyDOList, CwbbBsSyzqybdbYzxGridVO.class);
                syzqyMap = SsfwglUtil.toGrid(syzqyDOList, true);
            }


        }
        CwbbBsJbxxVO cwbbBsJbxxVO = new CwbbBsJbxxVO();
        cwbbBsJbxxVO.setNsrsbh(reqVO.getNsrsbh());
        cwbbBsJbxxVO.setNsrmc(reqVO.getNsrmc());
        cwbbBsJbxxVO.setSkssqq(reqVO.getSssqQ());
        cwbbBsJbxxVO.setSkssqz(reqVO.getSssqZ());
        cwbbBsJbxxVO.setTbrq(DateUtils.dateToString(new Date(), 3));
        resVO.setCwbbBsJbxxVO(cwbbBsJbxxVO);
        CwbbBsSlxxVO cwbbBsSlxxVO = new CwbbBsSlxxVO();

        final ZnsbMhzcQyjbxxmxReqVO nsrreqVO = new ZnsbMhzcQyjbxxmxReqVO();
        nsrreqVO.setNsrsbh(reqVO.getNsrsbh());
        nsrreqVO.setDjxh(djxh);

        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByNsrsbh(nsrreqVO);
        final JbxxmxsjVO jbxxmxsjVO = nsrxxRes.getData().getJbxxmxsj().get(0);
        cwbbBsSlxxVO.setJbrzjlx(jbxxmxsjVO.getBsrsfzjlxDm());
        cwbbBsSlxxVO.setJbrzjhm(jbxxmxsjVO.getBsrsfzjhm());
//        cwbbBsSlxxVO.setCwfzrxm(jbxxmxsjVO.getCwfzrxm());
//        cwbbBsSlxxVO.setFddbrxm(jbxxmxsjVO.getFddbrxm());
//        cwbbBsSlxxVO.setBsrlxdh(lxdh);
//        cwbbBsSlxxVO.setDlrmc("");
//        cwbbBsSlxxVO.setBlrysfzjlxDm(sfzjlxDm);
//        cwbbBsSlxxVO.setBlrysfzjhm(sfzjhm);
        cwbbBsSlxxVO.setJbr(jbxxmxsjVO.getBsrxm());
//        cwbbBsSlxxVO.setLxfs(lxdh);

        resVO.setCwbbBsSlxxVO(cwbbBsSlxxVO);
        if ("yzx".equals(zxbz)) {
            resVO.setCwbbBsZcfzbYzxVO(zcfzbMap);
            resVO.setCwbbBsLrbYzxVO(lrbMap);
        } else {
            resVO.setCwbbBsZcfzbWzxVO(zcfzbwzxMap);
            resVO.setCwbbBsLrbWzxVO(lrbwzxMap);
        }
        resVO.setCwbbBsXjllbYzxVO(xjllbMap);
        resVO.setCwbbBsSyzqybdbYzxVO(syzqyMap);
        resVO.setCwbbBsSyzqybdbYzxGrid(cwbbBsSyzqybdbYzxGrid);
        resVO.setCwbbBsQykjzzfzbYzxVO(qykjzzMap);

        return resVO;
    }


    @Override
    public CwbbBsInitYzxResVO initYsbData(CwbbBsInitYzxReqVO reqVO) {
        CwbbBsInitYzxResVO resVO = new CwbbBsInitYzxResVO();
        final String djxh = reqVO.getDjxh();
        final String skssqq = reqVO.getSssqQ();
        final String skssqz = reqVO.getSssqZ();
        ZnsbNssbYsbjgbCwbbDO ysbsj = znsbNssbYsbjgbCwbbMapper.queryYsbsj(djxh, skssqq, skssqz);
        if (GyUtils.isNotNull(ysbsj)) {
            resVO = JsonUtils.toBean(ysbsj.getBusinessclob(), CwbbBsInitYzxResVO.class);
        }
        return resVO;

    }

    @Async
    @Override
    public void plsbYsbSjCwbb(List<String> sbrwuuid) {
        List<ZnsbNssbYsbjgbCwbbDO> ysbsjList = znsbNssbYsbjgbCwbbMapper.queryYsbsjList(sbrwuuid);
        if (GyUtils.isNotNull(ysbsjList)) {
            for (ZnsbNssbYsbjgbCwbbDO ysbsj : ysbsjList) {
                try {
                    CwbbBsSaveSbtjYzxReqVO reqVO = JsonUtils.toBean(ysbsj.getBusinessclob(), CwbbBsSaveSbtjYzxReqVO.class);
                    this.saveCwbb(reqVO);
                } catch (Exception e) {
                    log.error("财务报表预申报数据批量申报失败djxh--->{},sbrwuuid---->{},原因为---->{}", ysbsj.getDjxh(), ysbsj.getSbrwuuid(), e.getMessage());
                    log.error("财务报表预申报数据批量申报失败djxh--->{},sbrwuuid---->{},完整堆栈为---->{}", JsonUtils.toJson(e.getStackTrace()));
                }

            }
        }
    }

    /**
     * 将String转换为BigDecimal（基础版本）
     *
     * @param value 字符串值
     * @return BigDecimal对象，转换失败返回BigDecimal.ZERO
     */
    public static BigDecimal toBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<String> saveCwbb(CwbbBsSaveSbtjYzxReqVO reqVO) {
        final String sbrwuuid = reqVO.getSbrwuuid();
        final String xzqhszDm = reqVO.getXzqhszDm();
        final String nsrsbh = reqVO.getNsrsbh();
        final String nsrmc = reqVO.getNsrmc();
        final String gzsbbz = reqVO.getGzsbbz();
        final String bsrq = DateUtils.dateToString(new Date(), 3);
        final String skssqq = reqVO.getSkssqq();
        final String skssqz = reqVO.getSkssqz();
        final NsrxxForm1VO nsrxxVO1 = new NsrxxForm1VO();
        CwbbBsSlxxVO cwbbBsSlxxVO = reqVO.getCwbbBsSlxxVO();
        final Map<String, String> bdxxform3 = reqVO.getCwbbBsXjllbYzxVO();//现金流量表信息
        final Map<String, String> bdxxform4 = reqVO.getCwbbBsSyzqybdbYzxVO();//所有者权益变动表
        final Map<String, String> qykzzMap = reqVO.getCwbbBsQykjzzfzbYzxVO();
        //资产负债表（适用已执行新金融准则、新收入准则和新租赁准则的一般企业）
        final Map<String, String> bdxxform10 = reqVO.getCwbbBsZcfzbYzxVO();//获取资产负债表信息并转化为grid形式存储
        //利润表（适用已执行新金融准则、新收入准则和新租赁准则的一般企业）
        final Map<String, String> bdxxform11 = reqVO.getCwbbBsLrbYzxVO();//获取利润表信息并转化为grid形式存储

        final YbqyzcfzbVO zcfzb = new YbqyzcfzbVO();
        final YbqylrbVO lrb = new YbqylrbVO();
        final YbqyxjllbVO xjllb = new YbqyxjllbVO();
        final YbqysyzqybdbVO qybdb = new YbqysyzqybdbVO();
        final Ybqykjzzcwbywbw cwszzbw = new Ybqykjzzcwbywbw();//总的业务报文

        List<SbCwbbQykjzzybqyZcfzbyzxDO> zcfzbDOList = new ArrayList<>();
        List<SbCwbbQykjzzybqyLrbyzxDO> lrbyzxDOList = new ArrayList<>();
        List<SbCwbbQykjzzybqyXjllbDO> xjllbDOList = new ArrayList<>();
        List<SbCwbbQykjzzybqySyzqyDO> syzqyDOList = new ArrayList<>();
        SbZlbsQykjzzfzDO qykjzzDO = new SbZlbsQykjzzfzDO();

        if (bdxxform3 != null) {////获取现金流量表信息并转化为grid形式存储
            final NsrxxForm3VO nsrxxVO3 = new NsrxxForm3VO();
            final List<YbqyxjllbGridlbVO> bdxxlistVO3 = SsfwglUtil.toVOList(bdxxform3, YbqyxjllbGridlbVO.class, true);
            xjllbDOList = BeanUtils.toBean(bdxxlistVO3, SbCwbbQykjzzybqyXjllbDO.class);
            final YbqyxjllbVO.YbqyxjllbGrid ybqyxjllbGrid = new YbqyxjllbVO.YbqyxjllbGrid();
            ybqyxjllbGrid.getYbqyxjllbGridlb().addAll(bdxxlistVO3);
            xjllb.setYbqyxjllbGrid(ybqyxjllbGrid);
            xjllb.setNsrxxForm3(nsrxxVO3);
            cwszzbw.setYbqyxjllbVO(xjllb);
        }

        if (bdxxform4 != null) {////获取所有者权益变动表信息并转化为grid形式存储
            final NsrxxForm4VO nsrxxVO4 = new NsrxxForm4VO();
            final List<YbqysyzqybdbGridlbVO> bdxxlistVO4 = SsfwglUtil.toVOList(bdxxform4, YbqysyzqybdbGridlbVO.class, true);
            syzqyDOList = BeanUtils.toBean(bdxxlistVO4, SbCwbbQykjzzybqySyzqyDO.class);
            final YbqysyzqybdbVO.YbqysyzqybdbGrid ybqysyzqybdbGrid = new YbqysyzqybdbVO.YbqysyzqybdbGrid();
            ybqysyzqybdbGrid.getYbqysyzqybdbGridlb().addAll(bdxxlistVO4);
            qybdb.setYbqysyzqybdbGrid(ybqysyzqybdbGrid);
            qybdb.setNsrxxForm4(nsrxxVO4);
            cwszzbw.setYbqysyzqybdbVO(qybdb);
        }

        if (!GyUtils.isNull(bdxxform10)) {//资产负债表 add by yangdalin 20190923
            final List<SBCwbbQykjzzybqyZcfzbzxVO> bdxxlistVO10 = SsfwglUtil.toVOList(bdxxform10, SBCwbbQykjzzybqyZcfzbzxVO.class, true);
            zcfzbDOList = BeanUtils.toBean(bdxxlistVO10, SbCwbbQykjzzybqyZcfzbyzxDO.class);
            final Ybqykjzzcwbywbw.YbqyzcfzbyzxVO.YbqyzcfzbzbyzxGrid grid10 = new Ybqykjzzcwbywbw.YbqyzcfzbyzxVO.YbqyzcfzbzbyzxGrid();
            grid10.getYbqyzcfzbzbyzxGridlb().addAll(bdxxlistVO10);
            final Ybqykjzzcwbywbw.YbqyzcfzbyzxVO vo = new Ybqykjzzcwbywbw.YbqyzcfzbyzxVO();
            vo.setYbqyzcfzbzbyzxGrid(grid10);
            cwszzbw.setYbqyzcfzbyzxVO(vo);
        }
        if (!GyUtils.isNull(bdxxform11)) {//利润表  add by yangdalin 20190923
            final List<SBCwbbQykjzzybqyLrbyzxVO> bdxxlistVO11 = SsfwglUtil.toVOList(bdxxform11, SBCwbbQykjzzybqyLrbyzxVO.class, true);
            final Ybqykjzzcwbywbw.YbqylrbyzxVO.YbqylrbyzxGrid grid11 = new Ybqykjzzcwbywbw.YbqylrbyzxVO.YbqylrbyzxGrid();
            lrbyzxDOList = BeanUtils.toBean(bdxxlistVO11, SbCwbbQykjzzybqyLrbyzxDO.class);
            grid11.getYbqylrbyzxGridlb().addAll(bdxxlistVO11);
            final Ybqykjzzcwbywbw.YbqylrbyzxVO vo = new Ybqykjzzcwbywbw.YbqylrbyzxVO();
            vo.setYbqylrbyzxGrid(grid11);
            cwszzbw.setYbqylrbyzxVO(vo);
        }
        if (GyUtils.isNotNull(qykzzMap)) {
            QykjzzfzVO qykjzzfzVO = BeanUtils.toBean(qykzzMap, QykjzzfzVO.class);
            qykjzzDO = BeanUtils.toBean(qykzzMap, SbZlbsQykjzzfzDO.class);
            cwszzbw.setQykjzzfzVO(qykjzzfzVO);
        }
        zcfzb.setNsrxxForm1(nsrxxVO1);//纳税人信息表
        final String cjbdxml = JsonUtils.toJson(cwszzbw);


        final String gzbz = GyUtils.isNotNull(gzsbbz) && "Y".equals(gzsbbz) ? "Y" : "N";
        final String zlbsuuid = "";
        final String djxh = reqVO.getDjxh();
        final String slswsxDm = "SLSXC132002005";
        final String dzbzdszlDm = "BDA0610165";
//        final String nsrsbh = (String) req.getAttr("nsrsbh");
//        final String nsrmc = (String) req.getAttr("nsrmc");
//        final String skssqq = (String) req.getAttr("ssqq");
//        final String skssqz = (String) req.getAttr("ssqz");
//        final String zlbsdlDm = (String) req.getAttr("zlbsdlDm");
//        final String zlbsxlDm = (String) req.getAttr("zlbsxlDm");
//        final String zllx = (String) req.getAttr("zllx");
//        final Double zlsl = HxzgQdxtSBUtils.castToDouble(req.getAttr("zlsl"));
//        final String cfdz = (String) req.getAttr("cfdz");
//        final String zlbsdm = (String) req.getAttr("bszldm");
        final String xmlCode = "TaxMLbw_Qykjzzjrqycjywbw_V1.0.xsd";
        final SB100VO sb100VO1 = new SB100VO();
        final SB100VO.SB100BdxxVO sb100VO = new SB100VO.SB100BdxxVO();
//        sb100VO.setCfdz(cfdz);
        sb100VO.setDjxh(djxh);
        sb100VO.setNsrmc(nsrmc);
        sb100VO.setNsrsbh(nsrsbh);
        sb100VO.setZllx("1");
        sb100VO.setZlsl(4);
        sb100VO.setZlbsxlDm("*********");
        sb100VO.setZlbsdlDm("ZL1001");
        sb100VO.setZlbsuuid(zlbsuuid);
        sb100VO.setCjbdxml(cjbdxml);
        sb100VO.setDzbzdszlDm(dzbzdszlDm);

        CwbbZlbssldjNsrxxVO cwbbZlbssldjNsrxxVO = new CwbbZlbssldjNsrxxVO();
        cwbbZlbssldjNsrxxVO.setSsqq(skssqq);
        cwbbZlbssldjNsrxxVO.setSsqz(skssqz);
        cwbbZlbssldjNsrxxVO.setBlrysfzjhm(cwbbBsSlxxVO.getJbrzjhm());
        cwbbZlbssldjNsrxxVO.setBlrysfzjlxDm(cwbbBsSlxxVO.getJbrzjlx());
        final SBCjbmxVO cjbmxvo = new SBCjbmxVO();
        cjbmxvo.setBszlDm("ZLA0610008");
        cjbmxvo.setDjxh(djxh);
        cjbmxvo.setSsqq(skssqq);
        cjbmxvo.setSsqz(skssqz);
        cjbmxvo.setZlbsuuid(zlbsuuid);

        final SB100VO.SB100BdxxVO.SBCjbmxGrid sbcjmxGrid = new SB100VO.SB100BdxxVO.SBCjbmxGrid();
        final String ywbwname = "gov.gt3.vo.sbzs.sb.sb099.Ybqykjzzcwbywbw";
        sbcjmxGrid.getSBCjbmxGridlb().add(cjbmxvo);
        sb100VO.setSBCjbmxGrid(sbcjmxGrid);
        sb100VO1.setSB100BdxxVO(sb100VO);
        sb100VO1.setGzbz(gzbz);
        sb100VO1.setSlswsxDm(slswsxDm);
        sb100VO1.setYwbwname(ywbwname);
        sb100VO1.setXmlCode(xmlCode);
        CwbbBsReqVO lqbs = new CwbbBsReqVO();
        lqbs.setSb100VO(sb100VO1);
        lqbs.setZlbssldjNsrxxVO(cwbbZlbssldjNsrxxVO);
        System.out.println(JsonUtils.toJson(lqbs));
        //组装数据交换数据
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("**********");
        sjjhDTO.setYwbm(YzpzzlEnum.CWBB_YBQYKJZZ.getDm());
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
        sjjhDTO.setBwnr(JsonUtils.toJson(lqbs));
        CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
        System.out.println(JsonUtils.toJson(result));
        boolean bccgbz = false;
        String msg = "";
        if (1 == result.getCode()) {
            CwbbBsSaveSbtjYzxResVO cwbbResVO = JsonUtils.toBean(String.valueOf(result.getData()), CwbbBsSaveSbtjYzxResVO.class);
            if ("00".equals(cwbbResVO.getReturncode())) {
                ZlbsbcbzVO zlbsbcbzVO = cwbbResVO.getZlbsbcbzVO();
                if ("Y".equals(zlbsbcbzVO.getFlag())) {
                    bccgbz = true;
                } else {
                    msg = "";
                }
            } else {
                msg = cwbbResVO.getReturnmsg();
            }
        } else {
            msg = result.getMsg();
        }
        if (bccgbz) {

            SbCwbbZlbscjbDO sbCwbbZlbscjbDO = new SbCwbbZlbscjbDO();
            final String zlbscjuuid = MD5Utils.md5(djxh + skssqq + skssqz);
            sbCwbbZlbscjbMapper.deleteByZlbscjuuid(zlbscjuuid);
            sbCwbbQykjzzybqyZcfzbyzxMapper.deleteByZlbscjuuid(zlbscjuuid);
            sbCwbbQykjzzybqyLrbyzxMapper.deleteByZlbscjuuid(zlbscjuuid);
            sbCwbbQykjzzybqyXjllbMapper.deleteByZlbscjuuid(zlbscjuuid);
            sbCwbbQykjzzybqySyzqyMapper.deleteByZlbscjuuid(zlbscjuuid);
            sbZlbsQykjzzfzMapper.deleteByZlbscjuuid(zlbscjuuid);
            sbCwbbZlbscjbDO.setZlbsuuid(GyUtils.getUuid());
            sbCwbbZlbscjbDO.setZlbscjuuid(zlbscjuuid);
            sbCwbbZlbscjbDO.setDjxh(djxh);
            sbCwbbZlbscjbDO.setSsqq(DateUtils.strToDate(skssqq));
            sbCwbbZlbscjbDO.setSsqz(DateUtils.strToDate(skssqz));
            sbCwbbZlbscjbDO.setZfbz1("N");
            sbCwbbZlbscjbDO.setYwqdDm("SJJG");
            sbCwbbZlbscjbDO.setLrrq(new Date());
            sbCwbbZlbscjbDO.setLrrsfid("SJJG");
            sbCwbbZlbscjbMapper.insert(sbCwbbZlbscjbDO);

            if (GyUtils.isNotNull(zcfzbDOList)) {
                for (SbCwbbQykjzzybqyZcfzbyzxDO zcfzbDO : zcfzbDOList) {
                    zcfzbDO.setUuid(GyUtils.getUuid());
                    zcfzbDO.setZlbscjuuid(zlbscjuuid);
                }
            }
            if (GyUtils.isNotNull(lrbyzxDOList)) {
                for (SbCwbbQykjzzybqyLrbyzxDO lrbDO : lrbyzxDOList) {
                    lrbDO.setUuid(GyUtils.getUuid());
                    lrbDO.setZlbscjuuid(zlbscjuuid);
                }
            }
            if (GyUtils.isNotNull(xjllbDOList)) {
                for (SbCwbbQykjzzybqyXjllbDO xjllbDO : xjllbDOList) {
                    xjllbDO.setUuid(GyUtils.getUuid());
                    xjllbDO.setZlbscjuuid(zlbscjuuid);
                }
            }
            if (GyUtils.isNotNull(syzqyDOList)) {
                for (SbCwbbQykjzzybqySyzqyDO syzqyDO : syzqyDOList) {
                    syzqyDO.setUuid(GyUtils.getUuid());
                    syzqyDO.setZlbscjuuid(zlbscjuuid);
                }
            }
            if (GyUtils.isNotNull(qykjzzDO)) {
                qykjzzDO.setUuid(GyUtils.getUuid());
                qykjzzDO.setZlbscjuuid(zlbscjuuid);
            }
            sbCwbbQykjzzybqyZcfzbyzxService.saveBatch(zcfzbDOList);
            sbCwbbQykjzzybqyLrbyzxService.saveBatch(lrbyzxDOList);
            sbCwbbQykjzzybqyXjllbService.saveBatch(xjllbDOList);
            sbCwbbQykjzzybqySyzqyService.saveBatch(syzqyDOList);
            sbZlbsQykjzzfzService.save(qykjzzDO);
            ZnsbNssbYsbjgbCwbbDO znsbNssbYsbjgbCwbbDO = new ZnsbNssbYsbjgbCwbbDO();

            Map<String, String> bw = new HashMap<>();
            bw.put("zcfzbh14l2", bdxxform10.get("h14l2"));
            bw.put("zcfzbh14l3", bdxxform10.get("h14l3"));
            bw.put("zcfzbh14l5", bdxxform10.get("h14l5"));
            bw.put("zcfzbh14l6", bdxxform10.get("h14l6"));
            bw.put("zcfzbh33l2", bdxxform10.get("h33l2"));
            bw.put("zcfzbh33l3", bdxxform10.get("h33l3"));
            bw.put("zcfzbh25l5", bdxxform10.get("h25l5"));
            bw.put("zcfzbh25l6", bdxxform10.get("h25l6"));
            bw.put("zcfzbh38l5", bdxxform10.get("h38l5"));
            bw.put("zcfzbh38l6", bdxxform10.get("h38l6"));
            bw.put("lrbh1l2", bdxxform11.get("h1l2"));
            bw.put("lrbh1l3", bdxxform11.get("h1l3"));
            bw.put("lrbh22l2", bdxxform11.get("h22l2"));
            bw.put("lrbh22l3", bdxxform11.get("h22l3"));
            bw.put("lrbh24l2", bdxxform11.get("h24l2"));
            bw.put("lrbh24l3", bdxxform11.get("h24l3"));
            bw.put("xjllbh10l2", bdxxform3.get("h10l2"));
            bw.put("xjllbh10l3", bdxxform3.get("h10l3"));
            bw.put("xjllbh34l2", bdxxform3.get("h34l2"));
            bw.put("xjllbh34l3", bdxxform3.get("h34l3"));
            bw.put("xjllbh35l2", bdxxform3.get("h35l2"));
            bw.put("xjllbh35l3", bdxxform3.get("h35l3"));
            bw.put("syzqyh26l6", bdxxform4.get("h26l6"));
            bw.put("syzqyh26l12", bdxxform4.get("h26l12"));
            bw.put("syzqyh26l14", bdxxform4.get("h26l14"));
            bw.put("syzqyh26l15", bdxxform4.get("h26l15"));

            znsbNssbYsbjgbCwbbDO.setBusinessclob(JsonUtils.toJson(reqVO));
            znsbNssbYsbjgbCwbbDO.setBw(JsonUtils.toJson(bw));
            znsbNssbYsbjgbCwbbDO.setSbrwuuid(sbrwuuid);

            znsbNssbYsbjgbCwbbMapper.updateYsbsjBySbrwuuid(znsbNssbYsbjgbCwbbDO);

            znsbNssbSbrwCwbbService.updateBscg(sbrwuuid);
            return CommonResult.success("成功");
        } else {
            znsbNssbSbrwCwbbService.updateBssb(sbrwuuid, msg);
            return CommonResult.error(-1, "报送失败");
        }

    }

    private void bbjg
            (List<ZnsbCwbbQykjzzybqyZcfzbyzxSjtqVO> zcfzbyzxVOList, List<ZnsbCwbbQykjzzybqyLrbyzxSjtqVO> lrbVOList, List<ZnsbCwbbQykjzzybqyXjllbSjtqVO> xjlVOList,
             List<ZnsbCwbbQykjzzybqySyzqySjtqVO> syzqyVOList, List<ZnsbCwbbQykjzzybqyZcfzbwzxSjtqVO> zcfzbwzxVOList, List<ZnsbCwbbQykjzzybqyLrbwzxSjtqVO> lrbwzxVOList,
             List<ZnsbNssbGdzcbSjtqVO> gdzcVOList, List<ZnsbNssbWxzcbSjtqVO> wxzcVOList, List<ZnsbNssbCqdtfybSjtqVO> cqdtfyVOList,
             List<ZnsbTzzxBpcjysjDO> sjList, String qx, String zlbscjuuid, String zlbscjuuid13) {

        final List<ZnsbTzzxBpcjysjDO> SM01List = sjList.stream().filter(o -> o.getBbmc().contains("SM01")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM03List = sjList.stream().filter(o -> o.getBbmc().contains("SM03")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM05List = sjList.stream().filter(o -> o.getBbmc().contains("SM05")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM06List = sjList.stream().filter(o -> o.getBbmc().contains("SM06")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM14List = sjList.stream().filter(o -> o.getBbmc().contains("SM14")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM15List = sjList.stream().filter(o -> o.getBbmc().contains("SM15")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM16List = sjList.stream().filter(o -> o.getBbmc().contains("SM16")).collect(Collectors.toList());

        if (GyUtils.isNotNull(SM01List)) {
            ZnsbTzzxBpcjysjDO SM01 = SM01List.get(0);
            final List<Map<String, Object>> SM01ListMap = JsonUtils.toMapList(SM01.getYssj2());
            final Map<Object, List<Map<String, Object>>> SM01Group = SM01ListMap.stream().collect(Collectors.groupingBy(o -> o.get("key")));
            for (ZnsbCwbbQykjzzybqyZcfzbyzxSjtqVO zcfzb : zcfzbyzxVOList) {
                final String zcxmKey = zcfzb.getZcxmKey();
                final String qyxmKey = zcfzb.getQyxmKey();
                final String byfseZcKey = zcfzb.getByfseZcKey();
                final String byfseQyKey = zcfzb.getByfseQyKey();
                if ("0".equals(qx) || "3".equals(qx)) {
                    zcfzb.setQmyeZc(safeConvert(sjpp(SM01Group, zcxmKey)));
                    zcfzb.setQmyeQy(safeConvert(sjpp(SM01Group, qyxmKey)));
                    zcfzb.setByfseZc(safeConvert(sjpp(SM01Group, byfseZcKey)));
                    zcfzb.setByfseQy(safeConvert(sjpp(SM01Group, byfseQyKey)));
                } else if ("1".equals(qx) || "4".equals(qx)) {
                    zcfzb.setSnnmyeZc(safeConvert(sjpp(SM01Group, zcxmKey)));
                    zcfzb.setSnnmyeQy(safeConvert(sjpp(SM01Group, qyxmKey)));
                }
                if ("0".equals(qx) || "1".equals(qx)) {
                    zcfzb.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx) || "4".equals(qx)) {
                    zcfzb.setZlbscjuuid(zlbscjuuid13);
                }
                zcfzb.setUuid(GyUtils.getUuid());
                zcfzb.setLrrq(new Date());
                zcfzb.setLrrsfid("SJJG");
            }
            for (ZnsbCwbbQykjzzybqyZcfzbwzxSjtqVO zcfzbwzx : zcfzbwzxVOList) {
                final String zcxmKey = zcfzbwzx.getZcxmKey();
                final String qyxmKey = zcfzbwzx.getQyxmKey();
                final String byfseZcKey = zcfzbwzx.getByfseZcKey();
                final String byfseQyKey = zcfzbwzx.getByfseQyKey();
                if ("0".equals(qx) || "3".equals(qx)) {
                    zcfzbwzx.setQmyeZc(safeConvert(sjpp(SM01Group, zcxmKey)));
                    zcfzbwzx.setQmyeQy(safeConvert(sjpp(SM01Group, qyxmKey)));
                    zcfzbwzx.setByfseZc(safeConvert(sjpp(SM01Group, byfseZcKey)));
                    zcfzbwzx.setByfseQy(safeConvert(sjpp(SM01Group, byfseQyKey)));
                } else if ("1".equals(qx) || "4".equals(qx)) {
                    zcfzbwzx.setSnnmyeZc(safeConvert(sjpp(SM01Group, zcxmKey)));
                    zcfzbwzx.setSnnmyeQy(safeConvert(sjpp(SM01Group, qyxmKey)));
                }
                if ("0".equals(qx) || "1".equals(qx)) {
                    zcfzbwzx.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx) || "4".equals(qx)) {
                    zcfzbwzx.setZlbscjuuid(zlbscjuuid13);
                }
                zcfzbwzx.setUuid(GyUtils.getUuid());
                zcfzbwzx.setLrrq(new Date());
                zcfzbwzx.setLrrsfid("SJJG");
            }

        }

        if (GyUtils.isNotNull(SM03List)) {
            ZnsbTzzxBpcjysjDO SM03 = SM03List.get(0);
            final List<Map<String, Object>> SM03ListMap = JsonUtils.toMapList(SM03.getYssj2());
            final Map<Object, List<Map<String, Object>>> SM03Group = SM03ListMap.stream().collect(Collectors.groupingBy(o -> o.get("key")));
            for (ZnsbCwbbQykjzzybqyLrbyzxSjtqVO lrb : lrbVOList) {
                final String hmcKey = lrb.getHmcKey();
                final String byfseKey = lrb.getByfseKey();

                if ("0".equals(qx) || "3".equals(qx)) {
                    lrb.setBqje(safeConvert(sjpp(SM03Group, hmcKey)));
                    lrb.setByfse(safeConvert(sjpp(SM03Group, byfseKey)));
                } else if ("2".equals(qx) || "4".equals(qx)) {
                    lrb.setSqje1(safeConvert(sjpp(SM03Group, hmcKey)));
                }
                if ("0".equals(qx) || "2".equals(qx)) {
                    lrb.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx) || "4".equals(qx)) {
                    lrb.setZlbscjuuid(zlbscjuuid13);
                }

                lrb.setUuid(GyUtils.getUuid());
                lrb.setLrrq(new Date());
                lrb.setLrrsfid("SJJG");
            }
            for (ZnsbCwbbQykjzzybqyLrbwzxSjtqVO lrbwzx : lrbwzxVOList) {
                final String hmcKey = lrbwzx.getHmcKey();
                final String byfseKey = lrbwzx.getByfseKey();

                if ("0".equals(qx) || "3".equals(qx)) {
                    lrbwzx.setBqje(safeConvert(sjpp(SM03Group, hmcKey)));
                    lrbwzx.setByfse(safeConvert(sjpp(SM03Group, byfseKey)));
                } else if ("2".equals(qx) || "4".equals(qx)) {
                    lrbwzx.setSqje1(safeConvert(sjpp(SM03Group, hmcKey)));
                }

                if ("0".equals(qx) || "2".equals(qx)) {
                    lrbwzx.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx) || "4".equals(qx)) {
                    lrbwzx.setZlbscjuuid(zlbscjuuid13);
                }

                lrbwzx.setUuid(GyUtils.getUuid());
                lrbwzx.setLrrq(new Date());
                lrbwzx.setLrrsfid("SJJG");
            }

        }

        if (GyUtils.isNotNull(SM05List)) {
            ZnsbTzzxBpcjysjDO SM05 = SM05List.get(0);
            final List<Map<String, Object>> SM05ListMap = JsonUtils.toMapList(SM05.getYssj2());
            final Map<Object, List<Map<String, Object>>> SM05Group = SM05ListMap.stream().collect(Collectors.groupingBy(o -> o.get("key")));

            for (ZnsbCwbbQykjzzybqyXjllbSjtqVO xjlb : xjlVOList) {
                final String hmcKey = xjlb.getHmcKey();
                final String byfseKey = xjlb.getByfseKey();

                if ("0".equals(qx) || "3".equals(qx)) {
                    xjlb.setBqje(safeConvert(sjpp(SM05Group, hmcKey)));
                    xjlb.setByfse(safeConvert(sjpp(SM05Group, byfseKey)));
                } else if ("2".equals(qx) || "4".equals(qx)) {
                    xjlb.setSqje1(safeConvert(sjpp(SM05Group, hmcKey)));
                }
                if ("0".equals(qx) || "2".equals(qx)) {
                    xjlb.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx) || "4".equals(qx)) {
                    xjlb.setZlbscjuuid(zlbscjuuid13);
                }
                xjlb.setUuid(GyUtils.getUuid());
                xjlb.setLrrq(new Date());
                xjlb.setLrrsfid("SJJG");
            }
        }

        if (GyUtils.isNotNull(SM06List)) {
            ZnsbTzzxBpcjysjDO SM06 = SM06List.get(0);
            final List<Map<String, Object>> SM06ListMap = JsonUtils.toMapList(SM06.getYssj2());
            final Map<Object, List<Map<String, Object>>> SM06Group = SM06ListMap.stream().collect(Collectors.groupingBy(o -> o.get("key")));
            for (ZnsbCwbbQykjzzybqySyzqySjtqVO syzqyb : syzqyVOList) {

                final String bnsszbhgbKey = syzqyb.getBnsszbhgbKey();
                final String qtqygjyxg1Key = syzqyb.getQtqygjyxg1Key();
                final String qtqygjyxz1Key = syzqyb.getQtqygjyxz1Key();
                final String qtqygjqt1Key = syzqyb.getQtqygjqt1Key();
                final String bnzbgjKey = syzqyb.getBnzbgjKey();
                final String bnjkcgKey = syzqyb.getBnjkcgKey();
                final String bnqtzhsyKey = syzqyb.getBnqtzhsyKey();
                final String bnzxcbKey = syzqyb.getBnzxcbKey();
                final String bnyygjKey = syzqyb.getBnyygjKey();
                final String bnwfplyKey = syzqyb.getBnwfplyKey();
                final String bnsyzqyhjKey = syzqyb.getBnsyzqyhjKey();
                final String snsszbhgbKey = syzqyb.getSnsszbhgbKey();
                final String snzbgjKey = syzqyb.getSnzbgjKey();
                final String snjkcgKey = syzqyb.getSnjkcgKey();
                final String snzxcbKey = syzqyb.getSnzxcbKey();
                final String snyygjKey = syzqyb.getSnyygjKey();
                final String snwfplyKey = syzqyb.getSnwfplyKey();
                final String snsyzqyhjKey = syzqyb.getSnsyzqyhjKey();
                final String snqtzhsyKey = syzqyb.getSnqtzhsyKey();
                final String qtqygjyxg2Key = syzqyb.getQtqygjyxg2Key();
                final String qtqygjyxz2Key = syzqyb.getQtqygjyxz2Key();
                final String qtqygjqt2Key = syzqyb.getQtqygjqt2Key();
                if ("0".equals(qx) || "3".equals(qx)) {
                    //股本（实收资本）
                    syzqyb.setBnsszbhgb(safeConvert(sjpp(SM06Group, bnsszbhgbKey)));
                    syzqyb.setQtqygjyxg1(safeConvert(sjpp(SM06Group, qtqygjyxg1Key)));
                    syzqyb.setQtqygjyxz1(safeConvert(sjpp(SM06Group, qtqygjyxz1Key)));
                    syzqyb.setQtqygjqt1(safeConvert(sjpp(SM06Group, qtqygjqt1Key)));
                    //资本公积-资本（股本）溢价
                    syzqyb.setBnzbgj(safeConvert(sjpp(SM06Group, bnzbgjKey)));
                    syzqyb.setBnjkcg(safeConvert(sjpp(SM06Group, bnjkcgKey)));
                    //其他综合收益-不能重分类进损益-其他
                    syzqyb.setBnqtzhsy(safeConvert(sjpp(SM06Group, bnqtzhsyKey)));
                    syzqyb.setBnzxcb(safeConvert(sjpp(SM06Group, bnzxcbKey)));
                    //盈余公积-法定盈余公积
                    syzqyb.setBnyygj(safeConvert(sjpp(SM06Group, bnyygjKey)));
                    //未分配利润-年初未分配利润
                    syzqyb.setBnwfply(safeConvert(sjpp(SM06Group, bnwfplyKey)));
                    syzqyb.setBnsyzqyhj(safeConvert(sjpp(SM06Group, bnsyzqyhjKey)));

                } else if ("1".equals(qx) || "4".equals(qx)) {
                    //股本（实收资本）
                    syzqyb.setSnsszbhgb(safeConvert(sjpp(SM06Group, snsszbhgbKey)));
                    syzqyb.setQtqygjyxg2(safeConvert(sjpp(SM06Group, qtqygjyxg2Key)));
                    syzqyb.setQtqygjyxz2(safeConvert(sjpp(SM06Group, qtqygjyxz2Key)));
                    syzqyb.setQtqygjqt2(safeConvert(sjpp(SM06Group, qtqygjqt2Key)));
                    //资本公积-资本（股本）溢价
                    syzqyb.setSnzbgj(safeConvert(sjpp(SM06Group, snzbgjKey)));
                    syzqyb.setSnjkcg(safeConvert(sjpp(SM06Group, snjkcgKey)));
                    //其他综合收益-不能重分类进损益-其他
                    syzqyb.setSnqtzhsy(safeConvert(sjpp(SM06Group, snqtzhsyKey)));
                    syzqyb.setSnzxcb(safeConvert(sjpp(SM06Group, snzxcbKey)));
                    //盈余公积-法定盈余公积
                    syzqyb.setSnyygj(safeConvert(sjpp(SM06Group, snyygjKey)));
                    //未分配利润-年初未分配利润
                    syzqyb.setSnwfply(safeConvert(sjpp(SM06Group, snwfplyKey)));
                    syzqyb.setSnsyzqyhj(safeConvert(sjpp(SM06Group, snsyzqyhjKey)));

                }

                if ("0".equals(qx) || "1".equals(qx)) {
                    syzqyb.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx) || "4".equals(qx)) {
                    syzqyb.setZlbscjuuid(zlbscjuuid13);
                }
                syzqyb.setUuid(GyUtils.getUuid());
                syzqyb.setLrrq(new Date());
                syzqyb.setLrrsfid("SJJG");

            }
        }

        if (GyUtils.isNotNull(SM14List)) {
            ZnsbTzzxBpcjysjDO SM14 = SM14List.get(0);
            final List<Map<String, Object>> SM14ListMap = JsonUtils.toMapList(SM14.getYssj2());
            final Map<Object, List<Map<String, Object>>> SM14Group = SM14ListMap.stream().collect(Collectors.groupingBy(o -> o.get("key")));
            for (ZnsbNssbGdzcbSjtqVO gdzc : gdzcVOList) {
                final String fwjjzKey = gdzc.getFwjjzKey();
                final String dzsbKey = gdzc.getDzsbKey();
                final String jqjxsbKey = gdzc.getJqjxsbKey();
                final String yssbKey = gdzc.getYssbKey();
                final String gqjjjKey = gdzc.getGqjjjKey();
                if ("0".equals(qx) || "3".equals(qx)) {
                    gdzc.setFwjjz(safeConvert(sjpp(SM14Group, fwjjzKey)));
                    gdzc.setDzsb(safeConvert(sjpp(SM14Group, dzsbKey)));
                    gdzc.setJqjxsb(safeConvert(sjpp(SM14Group, jqjxsbKey)));
                    gdzc.setYssb(safeConvert(sjpp(SM14Group, yssbKey)));
                    gdzc.setGqjjj(safeConvert(sjpp(SM14Group, gqjjjKey)));
                }
                if ("0".equals(qx)) {
                    gdzc.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx)) {
                    gdzc.setZlbscjuuid(zlbscjuuid13);
                }
                gdzc.setUuid(GyUtils.getUuid());
                gdzc.setLrrq(new Date());
                gdzc.setLrrsfid("SJJG");
            }
        }

        if (GyUtils.isNotNull(SM15List)) {
            ZnsbTzzxBpcjysjDO SM15 = SM15List.get(0);
            final List<Map<String, Object>> SM15ListMap = JsonUtils.toMapList(SM15.getYssj2());
            final Map<Object, List<Map<String, Object>>> SM15Group = SM15ListMap.stream().collect(Collectors.groupingBy(o -> o.get("key")));
            for (ZnsbNssbWxzcbSjtqVO wxzc : wxzcVOList) {
                final String tdsyqKey = wxzc.getTdsyqKey();
                final String zlqKey = wxzc.getZlqKey();
                final String fzljsKey = wxzc.getFzljsKey();
                final String rjKey = wxzc.getRjKey();
                final String sbqKey = wxzc.getSbqKey();
                final String dpzlqKey = wxzc.getDpzlqKey();
                final String sjtzKey = wxzc.getSjtzKey();
                if ("0".equals(qx) || "3".equals(qx)) {
                    wxzc.setTdsyq(safeConvert(sjpp(SM15Group, tdsyqKey)));
                    wxzc.setZlq(safeConvert(sjpp(SM15Group, zlqKey)));
                    wxzc.setFzljs(safeConvert(sjpp(SM15Group, fzljsKey)));
                    wxzc.setRj(safeConvert(sjpp(SM15Group, rjKey)));
                    wxzc.setSbq(safeConvert(sjpp(SM15Group, sbqKey)));
                    wxzc.setDpzlq(safeConvert(sjpp(SM15Group, dpzlqKey)));
                    wxzc.setSjtz(safeConvert(sjpp(SM15Group, sjtzKey)));
                }
                if ("0".equals(qx)) {
                    wxzc.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx)) {
                    wxzc.setZlbscjuuid(zlbscjuuid13);
                }
                wxzc.setUuid(GyUtils.getUuid());
                wxzc.setLrrq(new Date());
                wxzc.setLrrsfid("SJJG");
            }
        }

        if (GyUtils.isNotNull(SM16List)) {
            ZnsbTzzxBpcjysjDO SM16 = SM16List.get(0);
            final List<Map<String, Object>> SM16ListMap = JsonUtils.toMapList(SM16.getYssj2());
            final Map<Object, List<Map<String, Object>>> SM16Group = SM16ListMap.stream().collect(Collectors.groupingBy(o -> o.get("key")));
            for (ZnsbNssbCqdtfybSjtqVO cqdtfy : cqdtfyVOList) {
                final String qczmyeKey = cqdtfy.getQczmyeKey();
                final String bqzjeKey = cqdtfy.getBqzjeKey();
                final String bqtxKey = cqdtfy.getBqtxKey();
                final String wbbbzscKey = cqdtfy.getWbbbzscKey();
                final String hbzj1Key = cqdtfy.getHbzj1Key();
                final String hbjs1Key = cqdtfy.getHbjs1Key();
                final String qmzmyeKey = cqdtfy.getQmzmyeKey();
                if ("0".equals(qx) || "3".equals(qx)) {
                    cqdtfy.setQczmye(safeConvert(sjpp(SM16Group, qczmyeKey)));
                    cqdtfy.setBqzje(safeConvert(sjpp(SM16Group, bqzjeKey)));
                    cqdtfy.setBqtx(safeConvert(sjpp(SM16Group, bqtxKey)));
                    cqdtfy.setWbbbzsc(safeConvert(sjpp(SM16Group, wbbbzscKey)));
                    cqdtfy.setHbzj1(safeConvert(sjpp(SM16Group, hbzj1Key)));
                    cqdtfy.setHbjs1(safeConvert(sjpp(SM16Group, hbjs1Key)));
                    cqdtfy.setQmzmye(safeConvert(sjpp(SM16Group, qmzmyeKey)));
                }
                if ("0".equals(qx)) {
                    cqdtfy.setZlbscjuuid(zlbscjuuid);
                } else if ("3".equals(qx)) {
                    cqdtfy.setZlbscjuuid(zlbscjuuid13);
                }
                cqdtfy.setUuid(GyUtils.getUuid());
                cqdtfy.setLrrq(new Date());
                cqdtfy.setLrrsfid("SJJG");
            }
        }
    }


    public static Object sjpp(Map<Object, List<Map<String, Object>>> group,
                              String keyValue) {

        // 参数校验
        if (GyUtils.isNull(group) || GyUtils.isNull(keyValue)) {
            return null;
        }
        final List<Map<String, Object>> list = group.get(keyValue);
        if (GyUtils.isNotNull(list)) {
            Map<String, Object> map = list.get(0);
            return map.get("SIGNEDDATA");
        }
        // 遍历查找匹配的Map

        return null; // 没有找到匹配项
    }

    public static Map<String, Object> deepRemoveKeySpaces(String str) {
        Map<String, Object> strTomap = JsonUtils.toMap(str);
        Map<String, Object> result = new HashMap<>();
        strTomap.forEach((k, v) -> {
            String newKey = k.replaceAll("\\s", "");
            result.put(newKey, v);
        });
        return result;
    }

    public static BigDecimal safeConvert(Object obj) {
        if (obj == null) {
            return BigDecimal.ZERO;
        }

        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        } else if (obj instanceof String) {
            return new BigDecimal((String) obj);
        } else if (obj instanceof Number) {
            return BigDecimal.valueOf(((Number) obj).doubleValue());
        } else {
            return BigDecimal.ZERO;
        }
    }

    private void sjpj(CwbbBsZcfzbYzxVO cwbbBsZcfzbYzxVO, CwbbBsLrbYzxVO cwbbBsLrbYzxVO, CwbbBsXjllbYzxVO
            cwbbBsXjllbYzxVO, List<ZnsbTzzxBpcjysjDO> sjList, String qx) {
        final List<ZnsbTzzxBpcjysjDO> SM01List = sjList.stream().filter(o -> o.getBbmc().contains("SM01")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM03List = sjList.stream().filter(o -> o.getBbmc().contains("SM03")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM05List = sjList.stream().filter(o -> o.getBbmc().contains("SM05")).collect(Collectors.toList());
        final List<ZnsbTzzxBpcjysjDO> SM06List = sjList.stream().filter(o -> o.getBbmc().contains("SM06")).collect(Collectors.toList());

        if (GyUtils.isNotNull(SM01List)) {
            ZnsbTzzxBpcjysjDO SM01 = SM01List.get(0);
            final String Json = SM01.getYssj2();
            if ("0".equals(qx)) {
                CwbbBsZcfzbYzxFromJsonQmyeVO sm01QmyeVo = JsonUtils.toBean(Json, CwbbBsZcfzbYzxFromJsonQmyeVO.class);
                BeanUtils.copyBean(sm01QmyeVo, cwbbBsZcfzbYzxVO);
            } else if ("1".equals(qx)) {
                CwbbBsZcfzbYzxFromJsonSnnmyeVO sm01SnnmyeVo = JsonUtils.toBean(Json, CwbbBsZcfzbYzxFromJsonSnnmyeVO.class);
                BeanUtils.copyBean(sm01SnnmyeVo, cwbbBsZcfzbYzxVO);
            }


        }

        if (GyUtils.isNotNull(SM03List)) {
            ZnsbTzzxBpcjysjDO SM03 = SM03List.get(0);
            final String Json = SM03.getYssj2();
            if ("0".equals(qx)) {
                CwbbBsLrbYzxFromJsonBqjeVO sm03BqjeVo = JsonUtils.toBean(Json, CwbbBsLrbYzxFromJsonBqjeVO.class);
                BeanUtils.copyBean(sm03BqjeVo, cwbbBsLrbYzxVO);
            } else if ("2".equals(qx)) {
                CwbbBsLrbYzxFromJsonSqjeVO sm03SqjeVo = JsonUtils.toBean(Json, CwbbBsLrbYzxFromJsonSqjeVO.class);
                BeanUtils.copyBean(sm03SqjeVo, cwbbBsLrbYzxVO);
            }

        }

        if (GyUtils.isNotNull(SM05List)) {
            ZnsbTzzxBpcjysjDO SM05 = SM05List.get(0);
            final String Json = SM05.getYssj2();
            if ("0".equals(qx)) {
                CwbbBsXjllbYzxFromJsonBqjeVO sm05BqjeVo = JsonUtils.toBean(Json, CwbbBsXjllbYzxFromJsonBqjeVO.class);
                BeanUtils.copyBean(sm05BqjeVo, cwbbBsXjllbYzxVO);
            } else if ("2".equals(qx)) {
                CwbbBsXjllbYzxFromJsonSqjeVO sm05SqjeVo = JsonUtils.toBean(Json, CwbbBsXjllbYzxFromJsonSqjeVO.class);
                BeanUtils.copyBean(sm05SqjeVo, cwbbBsXjllbYzxVO);
            }
        }
    }

    public static String getLastMonthWithJavaTime(String input, String bz) {
        int year = Integer.parseInt(input.substring(0, 4));
        int mounth = Integer.parseInt(input.substring(4, 6));
        YearMonth ym = YearMonth.of(year, 1); // 任意月份均可
        YearMonth lastYearLastMonth = null;
        if ("N".equals(bz)) {
            lastYearLastMonth = ym.minusYears(1).withMonth(12);
            return lastYearLastMonth.toString().replace("-", "");
        } else {
            lastYearLastMonth = ym.minusYears(1).withMonth(mounth);
            return lastYearLastMonth.toString().replace("-", "");
        }

    }

    @Override
    public CwbbBsInitYzxRespDTO initDataYzx(CwbbBsInitYzxReqDTO cwbbBsInitYzxReqDTO) {
        CwbbBsInitYzxRespDTO cwbbBsInitYzxRespDTO = new CwbbBsInitYzxRespDTO();

        final String nsrsbh = cwbbBsInitYzxReqDTO.getNsrsbh();

        //获取rpa组装报文
        CwbbBsQykjzzYzxRpaBwDTO cwbbBsQykjzzYzxRpaBwDTO = this.getRpaBwxx();

        //转换给前台返回的报文结构
        this.getInitRespDTO(cwbbBsInitYzxRespDTO, cwbbBsQykjzzYzxRpaBwDTO);
        cwbbBsInitYzxRespDTO.getCwbbBsJbxxVO().setNsrsbh(nsrsbh);
        return cwbbBsInitYzxRespDTO;
    }

    public CwbbBsInitYzxRespDTO initDataYzx2(CwbbBsInitYzxReqDTO cwbbBsInitYzxReqDTO) {
        CwbbBsInitYzxRespDTO cwbbBsInitYzxRespDTO = new CwbbBsInitYzxRespDTO();
        //获取rpa组装报文
        CwbbBsQykjzzYzxRpaBwDTO cwbbBsQykjzzYzxRpaBwDTO = this.getRpaBwxx2();

        //转换给前台返回的报文结构
        this.getInitRespDTO(cwbbBsInitYzxRespDTO, cwbbBsQykjzzYzxRpaBwDTO);
        return cwbbBsInitYzxRespDTO;
    }

    @Override
    public void saveSbtjYzx(CwbbBsSaveSbtjYzxReqDTO cwbbBsSaveSbtjYzxReqDTO) {
        //将前台传入报文转换为金财Rpa报文结构
        CwbbBsQykjzzYzxRpaBwDTO cwbbBsQykjzzYzxRpaBwDTO = this.reqToJcrpa(cwbbBsSaveSbtjYzxReqDTO);
    }

    public CwbbBsQykjzzYzxRpaBwDTO reqToJcrpa(CwbbBsSaveSbtjYzxReqDTO cwbbBsSaveSbtjYzxReqDTO) {
        CwbbBsQykjzzYzxRpaBwDTO cwbbBsQykjzzYzxRpaBwDTO = new CwbbBsQykjzzYzxRpaBwDTO();

        //version
        cwbbBsQykjzzYzxRpaBwDTO.setVersion("default");

        //head
        CwbbBsQykjzzYzxRpaHeadDTO cwbbBsQykjzzYzxRpaHeadDTO = BeanUtils.toBean(cwbbBsSaveSbtjYzxReqDTO.getCwbbBsJbxxVO(), CwbbBsQykjzzYzxRpaHeadDTO.class);
        cwbbBsQykjzzYzxRpaBwDTO.setHead(cwbbBsQykjzzYzxRpaHeadDTO);

        //body
        CwbbBsQykjzzYzxRpaBodyDTO cwbbBsQykjzzYzxRpaBodyDTO = new CwbbBsQykjzzYzxRpaBodyDTO();

        //资产负债表-xx
        CwbbBsQykjzzYzxRpaZcfzbDTO cwbbBsQykjzzYzxRpaZcfzbDTO = new CwbbBsQykjzzYzxRpaZcfzbDTO();
        CwbbBsQykjzzYzxRpaZcfzbxxDTO cwbbBsQykjzzYzxRpaZcfzbxxDTO = BeanUtils.toBean(cwbbBsSaveSbtjYzxReqDTO.getCwbbBsZcfzbYzxVO(), CwbbBsQykjzzYzxRpaZcfzbxxDTO.class);
        //资产负债表-tjxxb
        CwbbBsQykjzzYzxRpaTjxxbDTO cwbbBsQykjzzYzxRpaTjxxbDTO = BeanUtils.toBean(cwbbBsSaveSbtjYzxReqDTO.getCwbbBsSlxxVO(), CwbbBsQykjzzYzxRpaTjxxbDTO.class);
        cwbbBsQykjzzYzxRpaZcfzbDTO.setTjxxb(cwbbBsQykjzzYzxRpaTjxxbDTO);
        cwbbBsQykjzzYzxRpaZcfzbDTO.setZcfzbYzx(cwbbBsQykjzzYzxRpaZcfzbxxDTO);
        cwbbBsQykjzzYzxRpaBodyDTO.setCwbbBsQykjzzYzxRpaZcfzbDTO(cwbbBsQykjzzYzxRpaZcfzbDTO);

        //企业会计准则附注
        CwbbBsQykjzzYzxRpaQykjzzfzDTO cwbbBsQykjzzYzxRpaQykjzzfzDTO = new CwbbBsQykjzzYzxRpaQykjzzfzDTO();
        CwbbBsQykjzzYzxRpaQykjzzfzxxDTO cwbbBsQykjzzYzxRpaQykjzzfzxxDTO = BeanUtils.toBean(cwbbBsSaveSbtjYzxReqDTO.getCwbbBsQykjzzfzbYzxVO(), CwbbBsQykjzzYzxRpaQykjzzfzxxDTO.class);
        cwbbBsQykjzzYzxRpaQykjzzfzDTO.setQykjzefz(cwbbBsQykjzzYzxRpaQykjzzfzxxDTO);
        cwbbBsQykjzzYzxRpaBodyDTO.setCwbbBsQykjzzYzxRpaQykjzzfzDTO(cwbbBsQykjzzYzxRpaQykjzzfzDTO);

        //利润表
        CwbbBsQykjzzYzxRpaLrbDTO cwbbBsQykjzzYzxRpaLrbDTO = new CwbbBsQykjzzYzxRpaLrbDTO();
        CwbbBsQykjzzYzxRpaLrbxxDTO cwbbBsQykjzzYzxRpaLrbxxDTO = BeanUtils.toBean(cwbbBsSaveSbtjYzxReqDTO.getCwbbBsLrbYzxVO(), CwbbBsQykjzzYzxRpaLrbxxDTO.class);
        cwbbBsQykjzzYzxRpaLrbDTO.setLrbYzx(cwbbBsQykjzzYzxRpaLrbxxDTO);
        cwbbBsQykjzzYzxRpaBodyDTO.setCwbbBsQykjzzYzxRpaLrbDTO(cwbbBsQykjzzYzxRpaLrbDTO);

        //现金流量表
        CwbbBsQykjzzYzxRpaXjllbDTO cwbbBsQykjzzYzxRpaXjllbDTO = new CwbbBsQykjzzYzxRpaXjllbDTO();
        CwbbBsQykjzzYzxRpaXjllbxxDTO cwbbBsQykjzzYzxRpaXjllbxxDTO = BeanUtils.toBean(cwbbBsSaveSbtjYzxReqDTO.getCwbbBsXjllbYzxVO(), CwbbBsQykjzzYzxRpaXjllbxxDTO.class);
        cwbbBsQykjzzYzxRpaXjllbDTO.setXjllbYzx(cwbbBsQykjzzYzxRpaXjllbxxDTO);
        cwbbBsQykjzzYzxRpaBodyDTO.setCwbbBsQykjzzYzxRpaXjllbDTO(cwbbBsQykjzzYzxRpaXjllbDTO);

        //所有者权益变动表
        CwbbBsQykjzzYzxRpaSyzqybdbDTO cwbbBsQykjzzYzxRpaSyzqybdbDTO = new CwbbBsQykjzzYzxRpaSyzqybdbDTO();
        List<CwbbBsQykjzzYzxRpaSyzqybdbxxDTO> syzqybdbList = BeanUtils.toBean(cwbbBsSaveSbtjYzxReqDTO.getCwbbBsSyzqybdbYzxGrid(), CwbbBsQykjzzYzxRpaSyzqybdbxxDTO.class);
        cwbbBsQykjzzYzxRpaSyzqybdbDTO.setSyzqybdb(syzqybdbList);
        cwbbBsQykjzzYzxRpaBodyDTO.setCwbbBsQykjzzYzxRpaSyzqybdbDTO(cwbbBsQykjzzYzxRpaSyzqybdbDTO);

        cwbbBsQykjzzYzxRpaBwDTO.setBody(cwbbBsQykjzzYzxRpaBodyDTO);


        //check
        CwbbBsQykjzzYzxRpaCheckDTO checkDTO = new CwbbBsQykjzzYzxRpaCheckDTO();
        checkDTO.setSB9807R1("Y");
        checkDTO.setSB9806R5("Y");
        checkDTO.setSB9807R2("Y");
        checkDTO.setSB9807R3("Y");
        checkDTO.setSB9807R4("Y");
        checkDTO.setSB9807R5("N");
        checkDTO.setSB9807R99("N");
        cwbbBsQykjzzYzxRpaBwDTO.setCheck(checkDTO);

        //note
        CwbbBsQykjzzYzxRpaCheckDTO noteDTO = new CwbbBsQykjzzYzxRpaCheckDTO();
        noteDTO.setSB9807R1("资产负债表");
        noteDTO.setSB9806R5("企业会计准则附注");
        noteDTO.setSB9807R2("利润表");
        noteDTO.setSB9807R3("现金流量表");
        noteDTO.setSB9807R4("所有者权益变动表");
        noteDTO.setSB9807R5("生产经营信息表");
        noteDTO.setSB9807R99("Execl文件");
        cwbbBsQykjzzYzxRpaBwDTO.setNote(noteDTO);


        return cwbbBsQykjzzYzxRpaBwDTO;
    }

    @Override
    public void cwbbPlsjsx(ZnsbNssbSbrwCwbbVO reqVO) {
        final String djxh = reqVO.getDjxh();
        final String xzqhszDm = reqVO.getXzqhszDm();
        final String skssqq = DateUtils.dateToString(reqVO.getSkssqq(), 3);
        final String skssqz = DateUtils.dateToString(reqVO.getSkssqz(), 3);
        final String nsrsbh = reqVO.getNsrsbh();
        final String zlbsxlDm = "*********";
        final String sbrwuuid = reqVO.getSbrwuuid();
        CwbbCxsbmxReqVO cxjgreq = new CwbbCxsbmxReqVO();
        cxjgreq.setDjxh(djxh);
        cxjgreq.setSsqq(skssqq);
        cxjgreq.setSsqz(skssqz);
        cxjgreq.setZlbsxlDm(zlbsxlDm);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CX00000003");
        sjjhDTO.setYwbm(YzpzzlEnum.CWBB_YBQYKJZZ.getDm());
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
        sjjhDTO.setBwnr(JsonUtils.toJson(cxjgreq));
        CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
        if (1 == result.getCode()) {
            CwbbCxsbmxResVO resVO = JsonUtils.toBean(String.valueOf(result.getData()), CwbbCxsbmxResVO.class);
            if (GyUtils.isNotNull(resVO) && "00".equals(resVO.getReturncode())) {
                CwbbLqFhMxsjVO mxsj = resVO.getMxsj();
                if (GyUtils.isNotNull(mxsj)) {

                    final String zlbscjuuid = MD5Utils.md5(djxh + skssqq + skssqz);
                    sbCwbbZlbscjbMapper.deleteByZlbscjuuid(zlbscjuuid);
                    sbCwbbQykjzzybqyZcfzbyzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                    sbCwbbQykjzzybqyLrbyzxMapper.deleteByZlbscjuuid(zlbscjuuid);
                    sbCwbbQykjzzybqyXjllbMapper.deleteByZlbscjuuid(zlbscjuuid);
                    sbCwbbQykjzzybqySyzqyMapper.deleteByZlbscjuuid(zlbscjuuid);
                    sbZlbsQykjzzfzMapper.deleteByZlbscjuuid(zlbscjuuid);
                    Date date = new Date();
                    SbCwbbZlbscjbDO sbCwbbZlbscjbDO = new SbCwbbZlbscjbDO();
                    sbCwbbZlbscjbDO.setZlbsuuid(GyUtils.getUuid());
                    sbCwbbZlbscjbDO.setZlbscjuuid(zlbscjuuid);
                    sbCwbbZlbscjbDO.setDjxh(djxh);
                    sbCwbbZlbscjbDO.setSsqq(DateUtils.strToDate(skssqq));
                    sbCwbbZlbscjbDO.setSsqz(DateUtils.strToDate(skssqz));
                    sbCwbbZlbscjbDO.setZfbz1("N");
                    sbCwbbZlbscjbDO.setYwqdDm("SJJG");
                    sbCwbbZlbscjbDO.setLrrq(new Date());
                    sbCwbbZlbscjbDO.setLrrsfid("SJJG");
                    sbCwbbZlbscjbMapper.insert(sbCwbbZlbscjbDO);
                    final List<CwbbLqFhZcfzbVO> zcfzb = mxsj.getZcfzb();
                    if (GyUtils.isNotNull(zcfzb)) {
                        List<SbCwbbQykjzzybqyZcfzbyzxDO> zcfzbyzxDOList = zcfzb.stream().map(o -> {
                            SbCwbbQykjzzybqyZcfzbyzxDO zcfzbyzxDO = BeanUtils.toBean(o, SbCwbbQykjzzybqyZcfzbyzxDO.class);
                            zcfzbyzxDO.setQyxmmc(o.getFzhsyzqyxmmc());
                            zcfzbyzxDO.setQmyeQy(o.getQmyeFzhsyzqy());
                            zcfzbyzxDO.setSnnmyeQy(o.getSnnmyeFzhsyzqy());
                            zcfzbyzxDO.setUuid(GyUtils.getUuid());
                            zcfzbyzxDO.setZlbscjuuid(zlbscjuuid);
                            zcfzbyzxDO.setLrrq(date);
                            zcfzbyzxDO.setLrrsfid("SJJG");
                            return zcfzbyzxDO;
                        }).collect(Collectors.toList());
                        sbCwbbQykjzzybqyZcfzbyzxService.saveBatch(zcfzbyzxDOList);
                    }

                    final List<CwbbLqFhLrbVO> lrb = mxsj.getLrb();
                    if (GyUtils.isNotNull(lrb)) {
                        List<SbCwbbQykjzzybqyLrbyzxDO> lrbyzxDOList = lrb.stream().map(o -> {
                            SbCwbbQykjzzybqyLrbyzxDO lrbyzxDO = BeanUtils.toBean(o, SbCwbbQykjzzybqyLrbyzxDO.class);
                            lrbyzxDO.setSqje1(o.getSqje());
                            lrbyzxDO.setUuid(GyUtils.getUuid());
                            lrbyzxDO.setZlbscjuuid(zlbscjuuid);
                            lrbyzxDO.setLrrq(date);
                            lrbyzxDO.setLrrsfid("SJJG");
                            return lrbyzxDO;

                        }).collect(Collectors.toList());
                        sbCwbbQykjzzybqyLrbyzxService.saveBatch(lrbyzxDOList);

                    }

                    final List<CwbbLqFhXjllbVO> xjllb = mxsj.getXjllb();
                    if (GyUtils.isNotNull(xjllb)) {
                        List<SbCwbbQykjzzybqyXjllbDO> xjllbDOList = xjllb.stream().map(o -> {
                            SbCwbbQykjzzybqyXjllbDO xjllbDO = BeanUtils.toBean(o, SbCwbbQykjzzybqyXjllbDO.class);
                            xjllbDO.setSqje1(o.getSqje());
                            xjllbDO.setUuid(GyUtils.getUuid());
                            xjllbDO.setZlbscjuuid(zlbscjuuid);
                            xjllbDO.setLrrq(date);
                            xjllbDO.setLrrsfid("SJJG");
                            return xjllbDO;
                        }).collect(Collectors.toList());
                        sbCwbbQykjzzybqyXjllbService.saveBatch(xjllbDOList);

                    }
                    final List<CwbbLqFhSyzqybdbVO> syzqybdb = mxsj.getSyzqybdb();
                    if (GyUtils.isNotNull(syzqybdb)) {
                        List<SbCwbbQykjzzybqySyzqyDO> syzqybdbDOList = syzqybdb.stream().map(o -> {
                            SbCwbbQykjzzybqySyzqyDO syzqyDO = BeanUtils.toBean(o, SbCwbbQykjzzybqySyzqyDO.class);
                            syzqyDO.setBnsszbhgb(o.getSszbhgbbnje());
                            syzqyDO.setSnyygj(o.getYygjsnje());
                            syzqyDO.setSnwfply(o.getWfplrsnje());
                            syzqyDO.setSnsyzqyhj(o.getSyzqyhjsnje());
                            syzqyDO.setBnzbgj(o.getZbgjbnje());
                            syzqyDO.setBnjkcg(o.getJkcgbnje());
                            syzqyDO.setBnyygj(o.getYygjbnje());
                            syzqyDO.setBnwfply(o.getWfplrbnje());
                            syzqyDO.setBnsyzqyhj(o.getSyzqyhjbnje());
                            syzqyDO.setSnsszbhgb(o.getSszbhgbsnje());
                            syzqyDO.setSnzbgj(o.getZbgjsnje());
                            syzqyDO.setSnjkcg(o.getJkcgsnje());
                            syzqyDO.setBnqtzhsy(o.getQtzhsybnje());
                            syzqyDO.setSnqtzhsy(o.getQtzhsysnje());
                            syzqyDO.setQtqygjyxg1(o.getQtqygjyxgbnje());
                            syzqyDO.setQtqygjyxz1(o.getQtqygjyxzbnje());
                            syzqyDO.setQtqygjqt1(o.getQtqygjqtbnje());
                            syzqyDO.setQtqygjyxg2(o.getQtqygjyxgsnje());
                            syzqyDO.setQtqygjyxz2(o.getQtqygjyxzsnje());
                            syzqyDO.setQtqygjqt2(o.getQtqygjqtsnje());
                            syzqyDO.setBnzxcb(o.getZxcbbnje());
                            syzqyDO.setSnzxcb(o.getZxcbsnje());
                            syzqyDO.setUuid(GyUtils.getUuid());
                            syzqyDO.setZlbscjuuid(zlbscjuuid);
                            syzqyDO.setLrrq(date);
                            syzqyDO.setLrrsfid("SJJG");
                            return syzqyDO;


                        }).collect(Collectors.toList());
                        sbCwbbQykjzzybqySyzqyService.saveBatch(syzqybdbDOList);

                    }
                    final CwbbLqFhQykjzzfzVO qykjzzfz = mxsj.getQykjzzfz();
                    if (GyUtils.isNotNull(qykjzzfz)) {
                        SbZlbsQykjzzfzDO qykjzzfzDO = new SbZlbsQykjzzfzDO();
                        qykjzzfzDO.setBz11(qykjzzfz.getQyjbqk());
                        qykjzzfzDO.setBz12(qykjzzfz.getCwbbdbzjc());
                        qykjzzfzDO.setBz13(qykjzzfz.getZxqykjzzdsm());
                        qykjzzfzDO.setBz14(qykjzzfz.getZykjzchkjpg());
                        qykjzzfzDO.setBz15(qykjzzfz.getKjzchkjgjbgyjccgzdsm());
                        qykjzzfzDO.setBz16(qykjzzfz.getKjzchkjgjbgyjccgzdsm());
                        qykjzzfzDO.setBz17(qykjzzfz.getHyhcnsxzcfzbrhftzsxglfgxjqjydxysmdsx());
                        qykjzzfzDO.setBz18(qykjzzfz.getYzycwbbsyzpjqyglzbdmbzcjcxdxx());
                        qykjzzfzDO.setUuid(GyUtils.getUuid());
                        qykjzzfzDO.setZlbscjuuid(zlbscjuuid);
                        qykjzzfzDO.setLrrq(date);
                        qykjzzfzDO.setLrrsfid("SJJG");
                        sbZlbsQykjzzfzMapper.insert(qykjzzfzDO);
                    }

                    znsbNssbSbrwCwbbService.updateBscg(sbrwuuid);

                }


            }
        }
    }

    @Override
    public CwbbSjcxResVO cwbbDwcx(CwbbSjcxReqVO reqVO) {
        CwbbSjcxResVO resVO = new CwbbSjcxResVO();
        final String djxh = reqVO.getDjxh();
        final Date skssqq = reqVO.getSkssqq();
        final Date skssqz = reqVO.getSkssqz();
        final String zlbsxlDm = reqVO.getZlbsxlDm();
        final String sszq = reqVO.getSszq();

        if (ZlbsxlDmEnum.QYKJZZYBQY.getZlbsxlDm().equals(zlbsxlDm)) {
            QykjzzYbqyYzxVO qykjzzYbqyYzxVo = new QykjzzYbqyYzxVO();
            List<CwbbQykjzzybqyZcfzbyzxVO> cwbbBsZcfzbYzxVOList = new ArrayList<>();

            //利润表-已执行
            List<CwbbQykjzzybqyLrbyzxVO> cwbbBsLrbYzxVOList = new ArrayList<>();

            //现金流量表-已执行
            List<CwbbQykjzzybqyXjllbVO> cwbbBsXjllbYzxVOList = new ArrayList<>();

            //所有者权益变动表-已执行-列表
            List<CwbbQykjzzybqySyzqyVO> cwbbBsSyzqybdbYzxVOList = new ArrayList<>();

            //企业会计准则附注表-已执行
            ZlbsQykjzzfzVO cwbbBsQykjzzfzbYzxVO = new ZlbsQykjzzfzVO();

            SbCwbbZlbscjbDO zbxx = sbCwbbZlbscjbMapper.queryXx(djxh, skssqq, skssqz);
            if (GyUtils.isNotNull(zbxx)) {
                final String zlbscjuuid = zbxx.getZlbscjuuid();
                List<SbCwbbQykjzzybqyZcfzbyzxDO> zcfzbDOList = sbCwbbQykjzzybqyZcfzbyzxMapper.queryZcfzb(zlbscjuuid);
                if (GyUtils.isNotNull(zcfzbDOList)) {
                    cwbbBsZcfzbYzxVOList = BeanUtils.toBean(zcfzbDOList, CwbbQykjzzybqyZcfzbyzxVO.class);
                }
                List<SbCwbbQykjzzybqyLrbyzxDO> lrbyzxDOList = sbCwbbQykjzzybqyLrbyzxMapper.queryLrb(zlbscjuuid);
                if (GyUtils.isNotNull(lrbyzxDOList)) {
                    cwbbBsLrbYzxVOList = BeanUtils.toBean(lrbyzxDOList, CwbbQykjzzybqyLrbyzxVO.class);
                }

                List<SbCwbbQykjzzybqyXjllbDO> xjllbDOList = sbCwbbQykjzzybqyXjllbMapper.queryXjllb(zlbscjuuid);
                if (GyUtils.isNotNull(xjllbDOList)) {
                    cwbbBsXjllbYzxVOList = BeanUtils.toBean(xjllbDOList, CwbbQykjzzybqyXjllbVO.class);
                }

                List<SbCwbbQykjzzybqySyzqyDO> syzqyDOList = sbCwbbQykjzzybqySyzqyMapper.querySyzqyb(zlbscjuuid);
                if (GyUtils.isNotNull(syzqyDOList)) {
                    cwbbBsSyzqybdbYzxVOList = BeanUtils.toBean(syzqyDOList, CwbbQykjzzybqySyzqyVO.class);
                }
                List<SbZlbsQykjzzfzDO> qykjzzDOList = sbZlbsQykjzzfzMapper.queryQykjzz(zlbscjuuid);
                if (GyUtils.isNotNull(qykjzzDOList)) {
                    cwbbBsQykjzzfzbYzxVO = BeanUtils.toBean(qykjzzDOList.get(0), ZlbsQykjzzfzVO.class);
                }
            } else {
                ZnsbCwbbZlbscjbDO znsbZbxx = znsbCwbbZlbscjbMapper.queryXx(djxh, skssqq, skssqz);
                if (GyUtils.isNotNull(znsbZbxx)) {
                    final String zlbscjuuid = znsbZbxx.getZlbscjuuid();
                    List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> zcfzbDOList = znsbCwbbQykjzzybqyZcfzbyzxMapper.queryZcfzb(zlbscjuuid);
                    if (GyUtils.isNotNull(zcfzbDOList)) {
                        cwbbBsZcfzbYzxVOList = BeanUtils.toBean(zcfzbDOList, CwbbQykjzzybqyZcfzbyzxVO.class);
                    }
                    List<ZnsbCwbbQykjzzybqyLrbyzxDO> lrbyzxDOList = znsbCwbbQykjzzybqyLrbyzxMapper.queryLrb(zlbscjuuid);
                    if (GyUtils.isNotNull(lrbyzxDOList)) {
                        cwbbBsLrbYzxVOList = BeanUtils.toBean(lrbyzxDOList, CwbbQykjzzybqyLrbyzxVO.class);
                    }
                    List<ZnsbCwbbQykjzzybqyXjllbDO> xjllbDOList = znsbCwbbQykjzzybqyXjllbMapper.queryXjllb(zlbscjuuid);
                    if (GyUtils.isNotNull(xjllbDOList)) {
                        cwbbBsXjllbYzxVOList = BeanUtils.toBean(xjllbDOList, CwbbQykjzzybqyXjllbVO.class);
                    }
                    List<ZnsbCwbbQykjzzybqySyzqyDO> syzqyDOList = znsbCwbbQykjzzybqySyzqyMapper.querySyzqyb(zlbscjuuid);
                    if (GyUtils.isNotNull(syzqyDOList)) {
                        cwbbBsSyzqybdbYzxVOList = BeanUtils.toBean(syzqyDOList, CwbbQykjzzybqySyzqyVO.class);
                    }
                }
            }

            qykjzzYbqyYzxVo.setCwbbBsZcfzbYzxVOList(cwbbBsZcfzbYzxVOList);
            qykjzzYbqyYzxVo.setCwbbBsLrbYzxVOList(cwbbBsLrbYzxVOList);
            qykjzzYbqyYzxVo.setCwbbBsXjllbYzxVOList(cwbbBsXjllbYzxVOList);
            qykjzzYbqyYzxVo.setCwbbBsSyzqybdbYzxVOList(cwbbBsSyzqybdbYzxVOList);
            qykjzzYbqyYzxVo.setCwbbBsQykjzzfzbYzxVO(cwbbBsQykjzzfzbYzxVO);
            resVO.setZlbsxlDm(zlbsxlDm);
            resVO.setQykjzzYbqyYzxVo(qykjzzYbqyYzxVo);

        }
        return resVO;
    }

    @Override
    public CommonResult<CwbbSjcxResVO> cwbbDwcxSbbsj(CwbbSjcxReqVO reqVO) {
        CwbbSjcxResVO resVO = new CwbbSjcxResVO();
        final String djxh = reqVO.getDjxh();
        final Date skssqq = reqVO.getSkssqq();
        final Date skssqz = reqVO.getSkssqz();
        final String zlbsxlDm = reqVO.getZlbsxlDm();

        if (ZlbsxlDmEnum.QYKJZZYBQY.getZlbsxlDm().equals(zlbsxlDm)) {
            QykjzzYbqyYzxVO qykjzzYbqyYzxVo = new QykjzzYbqyYzxVO();
            List<CwbbQykjzzybqyZcfzbyzxVO> cwbbBsZcfzbYzxVOList = new ArrayList<>();

            //利润表-已执行
            List<CwbbQykjzzybqyLrbyzxVO> cwbbBsLrbYzxVOList = new ArrayList<>();

            //现金流量表-已执行
            List<CwbbQykjzzybqyXjllbVO> cwbbBsXjllbYzxVOList = new ArrayList<>();

            //所有者权益变动表-已执行-列表
            List<CwbbQykjzzybqySyzqyVO> cwbbBsSyzqybdbYzxVOList = new ArrayList<>();

            //企业会计准则附注表-已执行
            ZlbsQykjzzfzVO cwbbBsQykjzzfzbYzxVO = new ZlbsQykjzzfzVO();

            SbCwbbZlbscjbDO zbxx = sbCwbbZlbscjbMapper.queryXx(djxh, skssqq, skssqz);
            if (GyUtils.isNotNull(zbxx)) {
                final String zlbscjuuid = zbxx.getZlbscjuuid();
                List<SbCwbbQykjzzybqyZcfzbyzxDO> zcfzbDOList = sbCwbbQykjzzybqyZcfzbyzxMapper.queryZcfzb(zlbscjuuid);
                if (GyUtils.isNotNull(zcfzbDOList)) {
                    cwbbBsZcfzbYzxVOList = BeanUtils.toBean(zcfzbDOList, CwbbQykjzzybqyZcfzbyzxVO.class);
                }
                List<SbCwbbQykjzzybqyLrbyzxDO> lrbyzxDOList = sbCwbbQykjzzybqyLrbyzxMapper.queryLrb(zlbscjuuid);
                if (GyUtils.isNotNull(lrbyzxDOList)) {
                    cwbbBsLrbYzxVOList = BeanUtils.toBean(lrbyzxDOList, CwbbQykjzzybqyLrbyzxVO.class);
                }

                List<SbCwbbQykjzzybqyXjllbDO> xjllbDOList = sbCwbbQykjzzybqyXjllbMapper.queryXjllb(zlbscjuuid);
                if (GyUtils.isNotNull(xjllbDOList)) {
                    cwbbBsXjllbYzxVOList = BeanUtils.toBean(xjllbDOList, CwbbQykjzzybqyXjllbVO.class);
                }

                List<SbCwbbQykjzzybqySyzqyDO> syzqyDOList = sbCwbbQykjzzybqySyzqyMapper.querySyzqyb(zlbscjuuid);
                if (GyUtils.isNotNull(syzqyDOList)) {
                    cwbbBsSyzqybdbYzxVOList = BeanUtils.toBean(syzqyDOList, CwbbQykjzzybqySyzqyVO.class);
                }
                List<SbZlbsQykjzzfzDO> qykjzzDOList = sbZlbsQykjzzfzMapper.queryQykjzz(zlbscjuuid);
                if (GyUtils.isNotNull(qykjzzDOList)) {
                    cwbbBsQykjzzfzbYzxVO = BeanUtils.toBean(qykjzzDOList.get(0), ZlbsQykjzzfzVO.class);
                }

                qykjzzYbqyYzxVo.setCwbbBsZcfzbYzxVOList(cwbbBsZcfzbYzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsLrbYzxVOList(cwbbBsLrbYzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsXjllbYzxVOList(cwbbBsXjllbYzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsSyzqybdbYzxVOList(cwbbBsSyzqybdbYzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsQykjzzfzbYzxVO(cwbbBsQykjzzfzbYzxVO);
                resVO.setZlbsxlDm(zlbsxlDm);
                resVO.setQykjzzYbqyYzxVo(qykjzzYbqyYzxVo);
                return CommonResult.success(resVO);
            } else {
                return CommonResult.error(-1, "数据不存在");
            }
        } else {
            return CommonResult.error(-1, "传入资料报送小类不存在");
        }
    }

    @Override
    public CommonResult<CwbbSjcxResVO> cwbbDwcxTzsj(CwbbSjcxReqVO reqVO) {
        CwbbSjcxResVO resVO = new CwbbSjcxResVO();
        final String djxh = reqVO.getDjxh();
        final String qydmz = reqVO.getQydmz();
        final String zlbsxlDm = reqVO.getZlbsxlDm();
        final String sszq = reqVO.getSszq();

        if (ZlbsxlDmEnum.QYKJZZYBQY.getZlbsxlDm().equals(zlbsxlDm)) {
            QykjzzYbqyYzxVO qykjzzYbqyYzxVo = new QykjzzYbqyYzxVO();
            List<CwbbQykjzzybqyZcfzbyzxVO> cwbbBsZcfzbYzxVOList = new ArrayList<>();

            //利润表-已执行
            List<CwbbQykjzzybqyLrbyzxVO> cwbbBsLrbYzxVOList = new ArrayList<>();

            List<CwbbQykjzzybqyZcfzbwzxVO> cwbbBsZcfzbWzxVOList = new ArrayList<>();

            //利润表-未执行
            List<CwbbQykjzzybqyLrbwzxVO> cwbbBsLrbWzxVOList = new ArrayList<>();

            //现金流量表-已执行
            List<CwbbQykjzzybqyXjllbVO> cwbbBsXjllbYzxVOList = new ArrayList<>();

            //所有者权益变动表-已执行-列表
            List<CwbbQykjzzybqySyzqyVO> cwbbBsSyzqybdbYzxVOList = new ArrayList<>();

            //企业会计准则附注表-已执行
            ZlbsQykjzzfzVO cwbbBsQykjzzfzbYzxVO = new ZlbsQykjzzfzVO();

            List<ZnsbNssbCqdtfybVO> znsbNssbCqdtfybVOList = new ArrayList<>();

            List<ZnsbNssbGdzcbVO> znsbNssbGdzcbVOList = new ArrayList<>();

            List<ZnsbNssbWxzcbVO> znsbNssbWxzcbVOList = new ArrayList<>();


            ZnsbCwbbZlbscjbDO znsbZbxx = znsbCwbbZlbscjbMapper.queryXxByQydmzDjxh(djxh, qydmz, sszq);
            if (GyUtils.isNotNull(znsbZbxx)) {
                final String zlbscjuuid = znsbZbxx.getZlbscjuuid();
                List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> zcfzbDOList = znsbCwbbQykjzzybqyZcfzbyzxMapper.queryZcfzb(zlbscjuuid);
                if (GyUtils.isNotNull(zcfzbDOList)) {
                    cwbbBsZcfzbYzxVOList = BeanUtils.toBean(zcfzbDOList, CwbbQykjzzybqyZcfzbyzxVO.class);
                }
                List<ZnsbCwbbQykjzzybqyLrbyzxDO> lrbyzxDOList = znsbCwbbQykjzzybqyLrbyzxMapper.queryLrb(zlbscjuuid);
                if (GyUtils.isNotNull(lrbyzxDOList)) {
                    cwbbBsLrbYzxVOList = BeanUtils.toBean(lrbyzxDOList, CwbbQykjzzybqyLrbyzxVO.class);
                }

                List<ZnsbCwbbQykjzzybqyZcfzbwzxDO> zcfzbwzxDOList = znsbCwbbQykjzzybqyZcfzbwzxMapper.queryZcfzb(zlbscjuuid);
                if (GyUtils.isNotNull(zcfzbwzxDOList)) {
                    cwbbBsZcfzbWzxVOList = BeanUtils.toBean(zcfzbwzxDOList, CwbbQykjzzybqyZcfzbwzxVO.class);
                }
                List<ZnsbCwbbQykjzzybqyLrbwzxDO> lrbwzxDOList = znsbCwbbQykjzzybqyLrbwzxMapper.queryLrb(zlbscjuuid);
                if (GyUtils.isNotNull(lrbwzxDOList)) {
                    cwbbBsLrbWzxVOList = BeanUtils.toBean(lrbwzxDOList, CwbbQykjzzybqyLrbwzxVO.class);
                }

                List<ZnsbCwbbQykjzzybqyXjllbDO> xjllbDOList = znsbCwbbQykjzzybqyXjllbMapper.queryXjllb(zlbscjuuid);
                if (GyUtils.isNotNull(xjllbDOList)) {
                    cwbbBsXjllbYzxVOList = BeanUtils.toBean(xjllbDOList, CwbbQykjzzybqyXjllbVO.class);
                }
                List<ZnsbCwbbQykjzzybqySyzqyDO> syzqyDOList = znsbCwbbQykjzzybqySyzqyMapper.querySyzqyb(zlbscjuuid);
                if (GyUtils.isNotNull(syzqyDOList)) {
                    cwbbBsSyzqybdbYzxVOList = BeanUtils.toBean(syzqyDOList, CwbbQykjzzybqySyzqyVO.class);
                }

                List<ZnsbNssbCqdtfybDO> cqdtfybDOList = znsbNssbCqdtfybMapper.queryCqdtfyb(zlbscjuuid);
                if (GyUtils.isNotNull(cqdtfybDOList)) {
                    znsbNssbCqdtfybVOList = BeanUtils.toBean(cqdtfybDOList, ZnsbNssbCqdtfybVO.class);
                }
                List<ZnsbNssbGdzcbDO> gdzcbDOList = znsbNssbGdzcbMapper.queryGdzcb(zlbscjuuid);
                if (GyUtils.isNotNull(gdzcbDOList)) {
                    znsbNssbGdzcbVOList = BeanUtils.toBean(gdzcbDOList, ZnsbNssbGdzcbVO.class);
                }
                List<ZnsbNssbWxzcbDO> wxzcbDOList = znsbNssbWxzcbMapper.queryWxzcb(zlbscjuuid);
                if (GyUtils.isNotNull(wxzcbDOList)) {
                    znsbNssbWxzcbVOList = BeanUtils.toBean(wxzcbDOList, ZnsbNssbWxzcbVO.class);
                }

                qykjzzYbqyYzxVo.setCwbbBsZcfzbYzxVOList(cwbbBsZcfzbYzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsLrbYzxVOList(cwbbBsLrbYzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsZcfzbWzxVOList(cwbbBsZcfzbWzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsLrbWzxVOList(cwbbBsLrbWzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsXjllbYzxVOList(cwbbBsXjllbYzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsSyzqybdbYzxVOList(cwbbBsSyzqybdbYzxVOList);
                qykjzzYbqyYzxVo.setCwbbBsQykjzzfzbYzxVO(cwbbBsQykjzzfzbYzxVO);
                qykjzzYbqyYzxVo.setCqdtfybVOList(znsbNssbCqdtfybVOList);
                qykjzzYbqyYzxVo.setGdzcbVOList(znsbNssbGdzcbVOList);
                qykjzzYbqyYzxVo.setWxzcbVOList(znsbNssbWxzcbVOList);
                resVO.setZlbsxlDm(zlbsxlDm);
                resVO.setQykjzzYbqyYzxVo(qykjzzYbqyYzxVo);
                return CommonResult.success(resVO);
            } else {
                return CommonResult.error(-1, "数据不存在");
            }
        } else {
            return CommonResult.error(-1, "传入资料报送小类不存在");
        }
    }

    private void getInitRespDTO(CwbbBsInitYzxRespDTO cwbbBsInitYzxRespDTO, CwbbBsQykjzzYzxRpaBwDTO
            cwbbBsQykjzzYzxRpaBwDTO) {
        //基本信息表
        CwbbBsJbxxVO cwbbBsJbxxVO = BeanUtils.toBean(cwbbBsQykjzzYzxRpaBwDTO.getHead(), CwbbBsJbxxVO.class);
        cwbbBsInitYzxRespDTO.setCwbbBsJbxxVO(cwbbBsJbxxVO);

        //受理信息表
        CwbbBsSlxxVO cwbbBsSlxxVO = BeanUtils.toBean(cwbbBsQykjzzYzxRpaBwDTO.getBody().getCwbbBsQykjzzYzxRpaZcfzbDTO().getTjxxb(), CwbbBsSlxxVO.class);
        cwbbBsInitYzxRespDTO.setCwbbBsSlxxVO(cwbbBsSlxxVO);

        //资产负债表-已执行
        CwbbBsZcfzbYzxVO cwbbBsZcfzbYzxVO = BeanUtils.toBean(cwbbBsQykjzzYzxRpaBwDTO.getBody().getCwbbBsQykjzzYzxRpaZcfzbDTO().getZcfzbYzx(), CwbbBsZcfzbYzxVO.class);
        cwbbBsInitYzxRespDTO.setCwbbBsZcfzbYzxVO(cwbbBsZcfzbYzxVO);

        //企业会计准则附注表-已执行
        CwbbBsQykjzzfzbYzxVO cwbbBsQykjzzfzbYzxVO = BeanUtils.toBean(cwbbBsQykjzzYzxRpaBwDTO.getBody().getCwbbBsQykjzzYzxRpaQykjzzfzDTO().getQykjzefz(), CwbbBsQykjzzfzbYzxVO.class);
        cwbbBsInitYzxRespDTO.setCwbbBsQykjzzfzbYzxVO(cwbbBsQykjzzfzbYzxVO);

        //利润表-已执行
        CwbbBsLrbYzxVO cwbbBsLrbYzxVO = BeanUtils.toBean(cwbbBsQykjzzYzxRpaBwDTO.getBody().getCwbbBsQykjzzYzxRpaLrbDTO().getLrbYzx(), CwbbBsLrbYzxVO.class);
        cwbbBsInitYzxRespDTO.setCwbbBsLrbYzxVO(cwbbBsLrbYzxVO);

        //现金流量表-已执行
        CwbbBsXjllbYzxVO cwbbBsXjllbYzxVO = BeanUtils.toBean(cwbbBsQykjzzYzxRpaBwDTO.getBody().getCwbbBsQykjzzYzxRpaXjllbDTO().getXjllbYzx(), CwbbBsXjllbYzxVO.class);
        cwbbBsInitYzxRespDTO.setCwbbBsXjllbYzxVO(cwbbBsXjllbYzxVO);

        //所有者权益变动表-已执行-列表
        List<CwbbBsSyzqybdbYzxGridVO> cwbbBsSyzqybdbYzxGrid = BeanUtils.toBean(cwbbBsQykjzzYzxRpaBwDTO.getBody().getCwbbBsQykjzzYzxRpaSyzqybdbDTO().getSyzqybdb(), CwbbBsSyzqybdbYzxGridVO.class);
        cwbbBsInitYzxRespDTO.setCwbbBsSyzqybdbYzxGrid(cwbbBsSyzqybdbYzxGrid);
    }

    private CwbbBsQykjzzYzxRpaBwDTO getRpaBwxx() {
        //TODO 暂时通过mock获取初始化报文  转换用原生写法
        CwbbBsQykjzzYzxRpaBwDTO cwbbBsQykjzzYzxRpaBwDTO = new CwbbBsQykjzzYzxRpaBwDTO();
        try {
            ObjectMapper mapper = new ObjectMapper();
            cwbbBsQykjzzYzxRpaBwDTO = mapper.readValue(new ClassPathResource("/mock/cwbbbsInitJcRpaTest.json").readUtf8Str(), CwbbBsQykjzzYzxRpaBwDTO.class);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return cwbbBsQykjzzYzxRpaBwDTO;
    }

    private CwbbBsQykjzzYzxRpaBwDTO getRpaBwxx2() {
        CwbbBsQykjzzYzxRpaBwDTO cwbbBsQykjzzYzxRpaBwDTO = new CwbbBsQykjzzYzxRpaBwDTO();
        try {
            ObjectMapper mapper = new ObjectMapper();
            cwbbBsQykjzzYzxRpaBwDTO = mapper.readValue(new ClassPathResource("/mock/cwbbbsInitJcRpaTest2.json").readUtf8Str(), CwbbBsQykjzzYzxRpaBwDTO.class);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return cwbbBsQykjzzYzxRpaBwDTO;
    }
}