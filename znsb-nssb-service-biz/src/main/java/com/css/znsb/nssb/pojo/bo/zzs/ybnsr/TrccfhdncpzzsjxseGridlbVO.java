package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 投入产出法核定农产品增值税进项税额Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "trccfhdncpzzsjxseGridlbVO", propOrder = { "ewbhxh", "cpmc1", "hyncpmc", "hdddhsl", "qckcncpsl", "qcpjmj", "dqgjncpsl", "dqmj", "pjgmdj", "dqxshwsl", "kcl", "dqyxdkncpjxse" })
@Getter
@Setter
public class TrccfhdncpzzsjxseGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 产品名称
     */
    @XmlElement(nillable = true, required = true)
    protected String cpmc1;

    /**
     * 耗用农产品名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hyncpmc;

    /**
     * 核定的单耗数量吨
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal hdddhsl;

    /**
     * 期初库存农产品数量
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qckcncpsl;

    /**
     * 期初平均买价
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qcpjmj;

    /**
     * 当期购进农产品数量
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal dqgjncpsl;

    /**
     * 当期买价元/吨
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal dqmj;

    /**
     * 平均购买单价元/吨
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal pjgmdj;

    /**
     * 当期销售货物数量吨
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal dqxshwsl;

    /**
     * 扣除率
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kcl;

    /**
     * 当期允许抵扣农产品进项税额元
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal dqyxdkncpjxse;
}