package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 海关完税凭证抵扣联数据Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hgwspzdklsjGridlbVO", propOrder = { "hgwspzhm", "jkkaDm", "tfrq", "se", "rzjg" })
@Getter
@Setter
public class HgwspzdklsjGridlbVO {
    /**
     * 海关完税凭证号码
     */
    protected String hgwspzhm;

    /**
     * 进口口岸代码
     */
    protected String jkkaDm;

    /**
     * 填发日期
     */
    protected String tfrq;

    /**
     * 税额
     */
    protected BigDecimal se;

    /**
     * 认证结果
     */
    protected String rzjg;
}