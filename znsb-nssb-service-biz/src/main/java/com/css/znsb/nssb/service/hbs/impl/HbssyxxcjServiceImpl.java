package com.css.znsb.nssb.service.hbs.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.constants.BcztConstants;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.gt3invoker.Gt3Invoker;
import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbDjHbsjcxxcjSyxxbMapper;
import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbDjHbsjcxxcjbMapper;
import com.css.znsb.nssb.mapper.zcxx.ZnsbNssbZcxxMapper;
import com.css.znsb.nssb.pojo.domain.csdm.DmGySwjgDO;
import com.css.znsb.nssb.pojo.domain.hbs.ZnsbDjHbsjcxxcjSyxxbDO;
import com.css.znsb.nssb.pojo.domain.hbs.ZnsbDjHbsjcxxcjbDO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.GetWbjhFzsbjsxxDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.HbsJcxxDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.HbssyxsbReqDto;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.SbjsDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.CycssyxxDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.DqswrDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.GfsyxxDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.HbsjcxxcjbDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.PwxscjbDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.SBYjskxxDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.SyxxDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.ZssyxxDTO;
import com.css.znsb.nssb.pojo.dto.nsrxx.DwnsrxxVO;
import com.css.znsb.nssb.pojo.dto.zcxx.ZnsbNssbZcxxDTO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.GetSyxxGridVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HXZGSB10834Request;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HXZGSB10834Response;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HbsCjSyxx;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.gy.CxstysbNsrxx;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.CycsReStrVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.DqsReStrVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.GfReStrVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HXZGSB10813Request;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.QuerySbbzVo;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.ZsReStrVO;
import com.css.znsb.nssb.pojo.vo.hbs.BccjxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.GfcjxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.HbsjcxxcjbVO;
import com.css.znsb.nssb.pojo.vo.hbs.HbssyxxcjSaveReqVo;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.InitSbjsReqVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.InitcjPageReqVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.LoadSsjmxzReqVo;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.LoadZspmReqVo;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.LoadZszmReqVo;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.PzxxReqVo;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.SjsResVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.SlReqVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.WbjhSyppMxVO;
import com.css.znsb.nssb.pojo.vo.hbs.CycssyxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.DqswrVO;
import com.css.znsb.nssb.pojo.vo.hbs.GfsyxxVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.HbsResVo;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.JdxzVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.JwdPljkReqVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.JwdPljkVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.JwdxxJkVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.JwdxxVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.PpxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.SbjsVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.SjsPlJwdjkVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.SsjmxzVO;
import com.css.znsb.nssb.pojo.vo.hbs.SyxxVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.Wrdlpz;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.XtcsReqVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.ZspmVo;
import com.css.znsb.nssb.pojo.vo.hbs.ZssyxxVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx.base.ZszmVo;
import com.css.znsb.nssb.pojo.vo.hbs.ZscjxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.ReStrVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HXZGSB01401Request;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HXZGSB01401Response;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.SBNsrxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.SBSbxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.SBYjskxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HXZGSB10748Request;
import com.css.znsb.nssb.pojo.vo.hbs.HbsJcxxVO;
import com.css.znsb.nssb.pojo.vo.hbs.PwxscjbVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HXZGSB10812Request;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HbssbjsNsrxxVO;
import com.css.znsb.nssb.service.hbs.ZnsbDjHbsjcxxcjSyxxbService;
import com.css.znsb.nssb.service.hbs.ZnsbDjHbsjcxxcjbService;
import com.css.znsb.nssb.service.hbs.ZnsbNssbGtfwwrfzssService;
import com.css.znsb.nssb.service.hbs.ZnsbNssbHbsdqswrjcxxcjbService;
import com.css.znsb.nssb.service.hbs.ZnsbNssbPwxscjbService;
import com.css.znsb.nssb.service.hbs.ZnsbNssbZsjcxxcjbService;
import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.impl.sbzx.HbssyxxcjUtils;
//import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.impl.sbzx.HxzgHbssyxxcjServiceImpl;
import com.css.znsb.nssb.service.hbs.HbssyxxcjService;
import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.sbzx.IHbsQuerySbbzService;
import com.css.znsb.nssb.service.zcxx.ZnsbNssbZcxxService;
import com.css.znsb.nssb.util.SjryUtil;
import com.css.znsb.nssb.utils.CchxwsnssbGyUtils;
import com.css.znsb.nssb.utils.ControllerUtils;
import com.css.znsb.nssb.utils.CxsDateUtils;
import com.css.znsb.nssb.utils.CxsGyUtils;
import com.css.znsb.nssb.utils.FcsCztdsyssycjUtils;
import com.css.znsb.nssb.utils.FtsCxsUtils;
import com.css.znsb.nssb.utils.FtsUtils;
import com.css.znsb.nssb.utils.GYCastUtils;
import com.css.znsb.nssb.utils.GYSbUtils;
import com.css.znsb.nssb.utils.GyQtUtils;
import com.css.znsb.nssb.utils.MathUtils;
import com.css.znsb.nssb.utils.MockUtils;
import com.css.znsb.nssb.utils.NsrglYhUtils;
import com.css.znsb.nssb.utils.cxs.CxsJmxzUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.css.znsb.nssb.constants.CsdmConstants.DM_GY_SWJG;

@Service
@Slf4j
public class HbssyxxcjServiceImpl implements HbssyxxcjService {

    @Resource
    private ZnsbNssbZcxxService znsbNssbZcxxService;
    @Resource
    private SjjhService sjjhService;
    @Resource
    private ZnsbNssbZcxxMapper zcxxMapper;
//    @Resource
//    private HxzgHbssyxxcjServiceImpl hxzgHbsService;
    @Resource
    private IHbsQuerySbbzService hbsQuerySbbzService;

    /**
     * 环保税征收项目代码
     */
    private static final String HBS_ZSXM_DM = "10121";

    /**
     * 成功状态码
     */
    private static final String SUCCESS_CODE = "1";

    /**
     * 失败状态码
     */
    private static final String FAIL_CODE = "9";

    /**
     * 异常状态码
     */
    private static final String ERROR_CODE = "-9";

    /**
     * 噪声污染超1-3
     *
     * @description 噪声污染超1-3
     * @value DM_GY_ZSPM_C1T3FB
     */
    private static final String DM_GY_ZSPM_C1T3FB = "101214101";
    /**
     * 噪声污染超4-5
     *
     * @description 噪声污染超4-5
     * @value DM_GY_ZSPM_C4T6FB
     */
    private static final String DM_GY_ZSPM_C4T6FB = "101214102";
    /**
     * 噪声污染超7-9
     *
     * @description 噪声污染超7-9
     * @value DM_GY_ZSPM_C7T9FB
     */
    private static final String DM_GY_ZSPM_C7T9FB = "101214103";
    /**
     * 噪声污染超10-12
     *
     * @description 噪声污染超10-12
     * @value DM_GY_ZSPM_C10T12FB
     */
    private static final String DM_GY_ZSPM_C10T12FB = "101214104";
    /**
     * 噪声污染超13-15
     *
     * @description 噪声污染超13-15
     * @value DM_GY_ZSPM_C13T15FB
     */
    private static final String DM_GY_ZSPM_C13T15FB = "101214105";
    /**
     * 噪声污染超16
     *
     * @description 噪声污染超16
     * @value DM_GY_ZSPM_C16YS
     */
    private static final String DM_GY_ZSPM_C16YS = "101214106";

    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private ZnsbDjHbsjcxxcjbMapper hbsjcxxcjbMapper;
    @Resource
    private ZnsbDjHbsjcxxcjSyxxbMapper hbsjcxxcjbsyxxMapper;

//    @Value("${sjs.zj-jrkey}")
    private String zjSjsJrkey;
//
//    @Resource
//    private HbsjcxxcjPpxxjgbMapper hbsjcxxcjPpxxjgbMapper;
//
//    @Resource
//    private HbsjcxxcjPpxxwrwmxbMapper hbsjcxxcjPpxxwrwmxbMapper;
//
//    @Resource
//    private HbsjcxxcjSyxxypwxsglbMapper hbsjcxxcjSyxxypwxsglbMapper;
//
//    @Resource
//    private HbssyxxcjFeignClients hbssyxxcjFeignClients;
//
//    @Resource
//    private CssDmcsService cssDmcsService;

    @Resource
    private NsrxxApi nsrxxcxApi;

//    @Resource
//    private HbssyxxcjJ3cxApi hbssyxxcjJ3cxApi;
//
//    @Resource
//    private CxscwgzApi cxscwgzApi;
//
//    @Resource
//    private HbsSjsClient hbsSjsClient;
    @Resource
    private ZnsbDjHbsjcxxcjbService jcxxcjbService;
    @Resource
    private ZnsbDjHbsjcxxcjSyxxbService znsbDjHbsjcxxcjSyxxbService;
    @Resource
    private ZnsbNssbHbsdqswrjcxxcjbService dqswrjcxxcjbService;
    @Resource
    private ZnsbNssbZsjcxxcjbService zsjcxxcjbService;
    @Resource
    private ZnsbNssbGtfwwrfzssService gtfwwrfzssService;
    @Resource
    private ZnsbNssbPwxscjbService pwxscjbService;


    /**
     * 查询纳税人，获取当前纳税人已采集税源信息
     *
     * @param
     * @return 环保税税源基础采集信息
     */
    @Override
    public HbsJcxxVO initcjPage(InitcjPageReqVO initcjPageReqVO) {
        log.info("initcjPage------------------------1--------------initcjPage");
        final String now = DateUtil.format(new Date(), "yyyy-MM-dd");
        final String djxh = initcjPageReqVO.getDjxh();
        final String sbbz = initcjPageReqVO.getSbbz();
        DwnsrxxVO dwnsrxxVO = queryNsrxyxx(djxh);
        log.info("initcjPage------------------------2--------------initcjPage");
        String xzqhszDm = dwnsrxxVO.getScjydzxzqhszDm();
        if (GyUtils.isNull(xzqhszDm)) {
            xzqhszDm = dwnsrxxVO.getZcdzxzqhszDm();
        }
        final String zgswskfjDm = dwnsrxxVO.getZgswskfjDm();
        List<SyxxVO> syxxGridList = new ArrayList<>();
        HbsJcxxVO hbsJcxxVORes = new HbsJcxxVO();
        HbssyxxcjSaveReqVo ywbw = queryHbscjywbw(djxh);
//        try {
//            res = Gt3Invoker.invoke("SWZJ.HXZG.SB.QUERYHBSCJYWBW", expend, req, HXZGSB10748Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.QUERYHBSCJYWBW异常：", e);
//            hbsJcxxDTO.setReturnCode("0");
//            hbsJcxxDTO.setReturnMsg(e.getMessage());
//            return hbsJcxxDTO;
//        }
        log.info("initcjPage------------------------3--------------initcjPage");
        // 正常申报时判断是否逾期
        if (GyUtils.isNull(sbbz)) {
            final Map<String, Object> map = getSkssq();
            String sbqx = "";
            try {
                //获取本上级税务机关
                final List<String> swjgDmList = new ArrayList<>();
                FtsUtils.getSwjgList(swjgDmList, zgswskfjDm, new HashMap<>());
                //获取全量码表数据，并根据税务机关分组
                final List<Map<String, Object>> sbqxwbAll = FtsUtils.getAllCacheData("cs_gy_sbqxwh_sbqx");//申报期限维护表，700条左右
                final List<Map<String, Object>> jjrAll = FtsUtils.getAllCacheData("cs_gy_jjr");//节假日配置表，4000条左右
                final List<Map<String, Object>> zqtzAll = FtsUtils.getAllCacheData("cs_gy_zqtz");//征期调整配置表。50条
                final Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup = sbqxwbAll.stream().filter(f -> !FtsUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
                final Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup = jjrAll.stream().filter(f -> !FtsUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
                final Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup = zqtzAll.stream().filter(f -> !FtsUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
                final Calendar skssqq = CxsDateUtils.parseDate(GYCastUtils.cast2Str(map.get("skssqq")));
                final Calendar skssqz = CxsDateUtils.parseDate(GYCastUtils.cast2Str(map.get("skssqz")));
                sbqx = CchxwsnssbGyUtils.getSbqx(swjgDmList, "04", "08", HBS_ZSXM_DM, skssqq, skssqz, sbqxwbGroup, jjrGroup, zqtzGroup);
            } catch (Exception e) {
                log.info("日期转换异常" + e.getMessage());
            }
            sbqx = new MockUtils().mock(sbqx,"2024-10-30");
            log.info("当前申报期限为" + sbqx);
            if (!GyUtils.isNull(sbqx)
                    && GYCastUtils.cast2Date(sbqx).before(GYCastUtils.cast2Date(sdf.format(new Date())))) {
                hbsJcxxVORes.setReturnCode("3");
                hbsJcxxVORes.setReturnMsg("当前日期已超过申报期限" + sbqx + "，请选择按次申报进行申报计算！");
            }
        }
        log.info("initcjPage------------------------4--------------initcjPage");
        final String wbcxws = this.queryWbcxws(djxh);
        log.info("initcjPage------------------------5--------------initcjPage");
        final String wbShlj = this.queryWbShlj(djxh);
        log.info("initcjPage------------------------6--------------initcjPage");
        //环保税基础信息为空时默认值
        if (GyUtils.isNull(ywbw) || GyUtils.isNull(ywbw.getHbsJcxxVO()) || GyUtils.isNull(ywbw.getHbsJcxxVO().getHbsjcxxcjb())) {
            // 设置初始行政区划
            HbsjcxxcjbVO hbsjcxxcjbVO = new HbsjcxxcjbVO();
            hbsjcxxcjbVO.setXzqhszDm(xzqhszDm);
            hbsjcxxcjbVO.setAcsb("N");
            hbsjcxxcjbVO.setSfczhygc("N");
            if ("Y".equals(wbcxws)) {
                hbsjcxxcjbVO.setSfscxwsjzclcs("Y");
            } else {
                hbsjcxxcjbVO.setSfscxwsjzclcs("N");
            }
            if ("Y".equals(wbShlj)) {
                hbsjcxxcjbVO.setSfsshljjzclcs("Y");
            } else {
                hbsjcxxcjbVO.setSfsshljjzclcs("N");
            }
            hbsJcxxVORes.setHbsjcxxcjb(hbsjcxxcjbVO);
            hbsJcxxVORes.setSyxxGridList(new ArrayList<>());
            return hbsJcxxVORes;
        }
        log.info("initcjPage------------------------7--------------initcjPage");
        // 基础信息
        HbsJcxxVO hbsJcxxVO = ywbw.getHbsJcxxVO();
        // 保存基本信息
        if(GyUtils.isNotNull(hbsJcxxVO.getHbsjcxxcjb())){
            // 多个排污许可证号，数据库pwxkzbh长度不够，核心征管存的zzsyhzcmc
            final String zzsyhzcmc = hbsJcxxVO.getHbsjcxxcjb().getZzsyhzcmc();
            hbsJcxxVO.getHbsjcxxcjb().setPwxkzbh(zzsyhzcmc);
            // 行政区划和纳税人信息行政区划不一致时,直接修改
            if (GyUtils.isNotNull(hbsJcxxVO.getHbsjcxxcjb().getXzqhszDm())
                    && GyUtils.isNotNull(xzqhszDm)
                    && !hbsJcxxVO.getHbsjcxxcjb().getXzqhszDm().equals(xzqhszDm)) {
                hbsJcxxVO.getHbsjcxxcjb().setXzqhszDm(xzqhszDm);
            }
            if (GyUtils.isNull(hbsJcxxVO.getHbsjcxxcjb().getXzqhszDm())) {
                hbsJcxxVO.getHbsjcxxcjb().setXzqhszDm(xzqhszDm);
            }
            if (GyUtils.isNull(hbsJcxxVO.getHbsjcxxcjb().getSfczhygc())) {
                hbsJcxxVO.getHbsjcxxcjb().setSfczhygc("N");
            }
            hbsJcxxVORes.setHbsjcxxcjb(hbsJcxxVO.getHbsjcxxcjb());
        }
        // 环境保护税基础信息税源信息表
        List<SyxxVO> syxxGridLb = new ArrayList<>();
        if(GyUtils.isNotNull(hbsJcxxVO.getSyxxGridList())){
            syxxGridLb = hbsJcxxVO.getSyxxGridList();
        }
        // 环保大气
        List<BccjxxVO> bccjGridLb = ywbw.getBccjList();
        // 固废
        List<GfcjxxVO> gfcjxxGridLb = ywbw.getGfcjxxList();
        //  噪声
        List<ZscjxxVO> zscjxxGridLb = ywbw.getZscjxxList();
        // 排污
        List<PwxscjbVO> pwxscjbGridLb = ywbw.getPwxscjbxxList();
        log.info("initcjPage------------------------8--------------initcjPage");
        // 通过syuuid与syxxGridList对象进行关联
        for (SyxxVO hbsjcxxcjSyxxb : syxxGridLb) {
            SyxxVO syxxVO = BeanUtils.toBean(hbsjcxxcjSyxxb, SyxxVO.class);
            final String zspmDm = syxxVO.getZspmDm();
            final String zszmDm = syxxVO.getZszmDm();
            if (GyUtils.isNotNull(hbsjcxxcjSyxxb.getJd2()) && GyUtils.isNotNull(hbsjcxxcjSyxxb.getWd())) {
                final String jd2 = hbsjcxxcjSyxxb.getJd2();
                final String wd = hbsjcxxcjSyxxb.getWd();
                final String[] jdArr = jd2.split("°|′|″");
                final String[] wdArr = wd.split("°|′|″");
                final Double jdd = Double.valueOf(jdArr[0]);
                final Double jdf = Double.valueOf(jdArr[1]);
                final Double jdm = Double.valueOf(jdArr[2]);
                final Double wdd = Double.valueOf(wdArr[0]);
                final Double wdf = Double.valueOf(wdArr[1]);
                final Double wdm = Double.valueOf(wdArr[2]);
                syxxVO.setJd2D(jdd);
                syxxVO.setJd2F(jdf);
                syxxVO.setJd2M(jdm);
                syxxVO.setWdd(wdd);
                syxxVO.setWdf(wdf);
                syxxVO.setWdm(wdm);
            }
            if (GyUtils.isNotNull(syxxVO.getSyyxqq()) && GyUtils.isNotNull(syxxVO.getSyyxqz())) {
                syxxVO.setSyyxqq(GYCastUtils.cast2Str(DateUtils.parseDate(syxxVO.getSyyxqq(),3)));
                syxxVO.setSyyxqz(GYCastUtils.cast2Str(DateUtils.parseDate(syxxVO.getSyyxqz(),3)));
            }
            // 大气
            if ("W".equals(syxxVO.getZywrwlbDm()) || "A".equals(syxxVO.getZywrwlbDm())) {
                Iterator<BccjxxVO> hbsdqswrjcxxcjbIterator = bccjGridLb.iterator();
                while (hbsdqswrjcxxcjbIterator.hasNext()) {
                    BccjxxVO hbsdqswrjcxxcjb = hbsdqswrjcxxcjbIterator.next();
                    if (syxxVO.getSyuuid().equals(hbsdqswrjcxxcjb.getSyuuid())) {
                        BeanUtils.copyBean(hbsdqswrjcxxcjb, syxxVO);
                        hbsdqswrjcxxcjbIterator.remove();
                        break;
                    }
                }
                // 排污
                if ("4".equals(syxxVO.getWrwpfljsffDm())) {
                    final List<PwxscjbDTO> pwxscjbDTOList = new ArrayList<>();
                    if (!GyUtils.isNull(pwxscjbGridLb)) {
                        for (PwxscjbVO pwxscjb : pwxscjbGridLb) {
                            if (zspmDm.equals(pwxscjb.getZspmDm())) {
                                if (!GyUtils.isNull(zszmDm) && zszmDm.equals(pwxscjb.getZszmDm())) {
                                    pwxscjbDTOList.add(BeanUtils.toBean(pwxscjb, PwxscjbDTO.class));
                                } else if (GyUtils.isNull(zszmDm)) {
                                    pwxscjbDTOList.add(BeanUtils.toBean(pwxscjb, PwxscjbDTO.class));
                                }
                            }
                        }
                        for (PwxscjbDTO pwxscjbDTO : pwxscjbDTOList) {
                            pwxscjbDTO.setZspmDmMc(FtsUtils.getMcByDm("dm_gy_zspm", "zspmmc", pwxscjbDTO.getZspmDm()));
                            pwxscjbDTO.setZszmDmMc(FtsUtils.getMcByDm("dm_gy_zszm", "zszmmc", pwxscjbDTO.getZszmDm()));
                            pwxscjbDTO.setWrwdwDmMc(FtsUtils.getMcByDm("dm_gy_jldw", "jldwmc", pwxscjbDTO.getWrwdwDm()));
                        }
                    }
                    syxxVO.setPwxscjbDTOList(pwxscjbDTOList);
                }
            }
            //固废
            if ("S".equals(syxxVO.getZywrwlbDm())) {
                Iterator<GfcjxxVO> gfjcxxcjbIterator = gfcjxxGridLb.iterator();
                while (gfjcxxcjbIterator.hasNext()) {
                    GfcjxxVO gfjcxxcjb = gfjcxxcjbIterator.next();
                    if (syxxVO.getSyuuid().equals(gfjcxxcjb.getSyuuid())) {
                        BeanUtils.copyBean(gfjcxxcjb, syxxVO);
                        gfjcxxcjbIterator.remove();
                        break;
                    }
                }
            }
            if ("N".equals(syxxVO.getZywrwlbDm())) {
                // 噪声
                Iterator<ZscjxxVO> zsjcxxcjbIterator = zscjxxGridLb.iterator();
                while (zsjcxxcjbIterator.hasNext()) {
                    ZscjxxVO zsjcxxcjb = zsjcxxcjbIterator.next();
                    if (GyUtils.isNotNull(syxxVO.getSyuuid())
                            && syxxVO.getSyuuid().equals(zsjcxxcjb.getSyuuid())) {
                        BeanUtils.copyBean(zsjcxxcjb, syxxVO);
                        zsjcxxcjbIterator.remove();
                        break;
                    }
                }
            }
            // 匹配征收品目和征收子目名称
            if (GyUtils.isNotNull(syxxVO.getZspmDm())) {
                syxxVO.setZspmDmMc(FtsUtils.getMcByDm("dm_gy_zspm", "zspmmc", syxxVO.getZspmDm()));
            }
            if (GyUtils.isNotNull(syxxVO.getZszmDm())) {
                syxxVO.setZszmDmMc(FtsUtils.getMcByDm("dm_gy_zszm", "zszmmc", syxxVO.getZszmDm()));
            }
            if (GyUtils.isNotNull(syxxVO.getJdxzDm())) {
                syxxVO.setJdxzMc(FtsUtils.getMcByDm("dm_gy_jdxz", "jdxzmc", syxxVO.getJdxzDm()));
            }
            syxxGridList.add(syxxVO);
        }
        // 业务统筹组2024011201号增加有效税源和无效税源
        for (SyxxVO syxxVO : syxxGridList) {
            if (!GyUtils.isNull(syxxVO.getSyyxqz()) && GYCastUtils.cast2Date(syxxVO.getSyyxqz()).before(GYCastUtils.cast2Date(now))) {
                syxxVO.setSyxxyxx("N");
            } else {
                syxxVO.setSyxxyxx("Y");
            }
        }
        hbsJcxxVORes.setSyxxGridList(syxxGridList);
        log.info("initcjPage--------------9--------------initcjPage");
        // 循环syxxGridList，入参syuuid，更正传入的税款所属期，调用querySbbzBySyuuid方法，如果返回信息中不存在“sbbz”，则删除该条信息
        if (GyUtils.isNotNull(initcjPageReqVO.getSkssqq())
                && GyUtils.isNotNull(initcjPageReqVO.getSkssqz())) {
            log.info("initcjPage--------------9.1--------------initcjPage");
            // 如果按次申报,按次申报选项置为是
            if (initcjPageReqVO.getSkssqq().equals(initcjPageReqVO.getSkssqz())) {
                hbsJcxxVORes.getHbsjcxxcjb().setAcsb("Y");
            } else {
                hbsJcxxVORes.getHbsjcxxcjb().setAcsb("N");
            }
            log.info("initcjPage--------------9.2--------------initcjPage");
            // 过滤在税源有效期起止是否在属期内
            final Iterator<SyxxVO> syxxDTOIterator = syxxGridList.iterator();
            while (syxxDTOIterator.hasNext()) {
                final SyxxVO syxxDTO = syxxDTOIterator.next();
                if (!GYCastUtils.sqAcross(
                        GYCastUtils.cast2Date(initcjPageReqVO.getSkssqq()),
                        GYCastUtils.cast2Date(initcjPageReqVO.getSkssqz()),
                        GYCastUtils.cast2Date(syxxDTO.getSyyxqq()),
                        GYCastUtils.cast2Date(syxxDTO.getSyyxqz()))) {
                    syxxDTOIterator.remove();
                }
            }
            log.info("initcjPage--------------9.3--------------initcjPage");
        }
        log.info("initcjPage--------------10--------------initcjPage");
        return hbsJcxxVORes;
    }

    private HbssyxxcjSaveReqVo queryHbscjywbw(String djxh) {
        HbssyxxcjSaveReqVo ywbw = new HbssyxxcjSaveReqVo();
        //基础信息
        ywbw.setHbsJcxxVO(jcxxcjbService.queryJcxx(djxh));
        //大气
        ywbw.setBccjList(dqswrjcxxcjbService.queryDqswrjcxxcjb(djxh));
        //固废
        ywbw.setGfcjxxList(gtfwwrfzssService.queryGtfwwrfzss(djxh));
        //噪声
        ywbw.setZscjxxList(zsjcxxcjbService.queryZsjcxxcjb(djxh));
        //排污
        ywbw.setPwxscjbxxList(pwxscjbService.queryPwxscjb(djxh));
        return ywbw;
    }
    /**
     * 环保税税源登记页面初始化时获取下拉数据信息
     */
    @Override
    public Map<String, Object> loadJcxxDictItems(String djxh) {
        final DwnsrxxVO dwnsrxxVO = queryNsrxyxx(djxh);
        final String swjgDm = dwnsrxxVO.getZgswskfjDm();
        String xzqhszDm = dwnsrxxVO.getScjydzxzqhszDm();
        if (GyUtils.isNull(xzqhszDm)) {
            xzqhszDm = dwnsrxxVO.getZcdzxzqhszDm();
        }
        Map<String, Object> jcxxDictItems = new HashMap<>();
        //1.通过缓存表获取cs_dj_hbjgyswjgdzb(环保机构与税务机关对照表),dm_gy_swjg(税务机关代码表)、dm_dj_gjhbjg（各级环保机构代码表）：
        List<Map<String, Object>> hbjgList = getHbjgList(swjgDm);
        // 去重
        hbjgList = hbjgList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(p ->
                        (String) p.get("gjhbjgmc"), Comparator.nullsFirst(String::compareTo)))), ArrayList::new));
        jcxxDictItems.put("hbjgList", hbjgList);
        // 通过缓存表获取DM_GY_ZSPM（征收品目代码）
        // 找出征收品目代码值为“101212100，101212200，101212300，101212400，101212900”的征收品目信息供页面中水污物种类加载下拉数据。
        List<Map<String, Object>> zspmList = loadZspm();
        jcxxDictItems.put("zspmList", zspmList);
        // 通过缓存表获取dm_gy_xzqh（行政区划代码表），通过session中纳税人对应的行政区划数字代码查询该表，找到纳税人行政区划对应的所有本下级行政区划，供页面上生产经营所在区划下拉数据使用
        // 通过swjgdm获取xzqhszdm
        // 如果纳税人信息有xzqhdm只传一条
        List<Map<String, Object>> xzqhList = new ArrayList<>();
        List<Map<String, Object>> xzqhDmList = CacheUtils.getTableData("dm_gy_xzqh");
        if (GyUtils.isNotNull(xzqhszDm)) {
            xzqhList = getChildren(xzqhDmList, "xzqhszDm", "sjxzqhszDm", xzqhszDm, true);
        } else {
            List<Map<String, Object>> xzqhListTmp = FcsCztdsyssycjUtils.getXzqhListBySwjgDm(swjgDm);
            List<Map<String, Object>> children = new ArrayList<>();
            xzqhListTmp.forEach(map -> {
                String xzqh = map.get("value").toString();
                List<Map<String, Object>> childrenTmp = getChildren(xzqhDmList, "xzqhszDm", "sjxzqhszDm", xzqh, true);
                children.addAll(childrenTmp);
            });
            // 去重
            xzqhList = children.stream().distinct().collect(Collectors.toList());
        }
        jcxxDictItems.put("xzqh", xzqhList);
        // 获取缓存表DM_DJ_ZYWRWLB，赋值污染物类别下拉数据
        List<Map<String, Object>> wrwlbList = CacheUtils.getTableData("dm_dj_zywrwlb");
        jcxxDictItems.put("wrwlbList", wrwlbList);
        // 获取缓存表DM_GY_JLDW中取值0208、0209、0210、0211代码值，供污染物单位下拉数据
        List<Map<String, Object>> jldwList = loadJldw();
        jcxxDictItems.put("jldwList", jldwList);
        // 获取缓存表DM_DJ_WRWPFLJSFF，供页面上污染物排放量计算方法下拉数据使用
        List<Map<String, Object>> wrwpfljsffList = CacheUtils.getTableData("dm_dj_wrwpfljsff");
        jcxxDictItems.put("wrwpfljsffList", wrwpfljsffList);
        // 获取缓存表DM_DJ_GTFWCLFS，代码取值0301,0302,0303,0399，供页面上综合利用情况下拉使用
        List<Map<String, Object>> gtfwclfsList = loadGtfwclfs();
        jcxxDictItems.put("gtfwclfsList", gtfwclfsList);
        return jcxxDictItems;
    }

//
    /**
     * 查询环保机构
     *
     * @param swjgDm
     * @return
     */
    private List<Map<String, Object>> getHbjgList(String swjgDm) {
        List<Map<String, Object>> hbjgList = new ArrayList<>();
        Map<String, Object> hbjgyswjgdzbMap = CacheUtils.getTableData("cs_dj_hbjgyswjgdzb", swjgDm);
        if (!GyUtils.isNull(hbjgyswjgdzbMap)) {
            List<Map<String, Object>> gjhbjgList = CacheUtils.getTableData("dm_dj_gjhbjg");
            String[] hbjgdms = hbjgyswjgdzbMap.get("hbjgdms").toString().split(",");
            List<String> hbjgdmList = Stream.of(hbjgdms).collect(Collectors.toList());
            for (String hbjgdm : hbjgdmList) {
                if (hbjgdm.contains("%")) {
                    hbjgdm = hbjgdm.substring(0, hbjgdm.indexOf("%"));
                }
                for (Map<String, Object> map : gjhbjgList) {
                    if (map.get("gjhbjgDm").toString().startsWith(hbjgdm)) {
                        if (GyUtils.isNull(map.get("gjhbjgmc"))) {
                            map.put("gjhbjgmc", GYCastUtils.cast2Str(map.get("xzqmc")));
                        }
                        hbjgList.add(map);
                    }
                }
            }
        } else {
            // 找上级机关
            final DmGySwjgDO data = CacheUtils.getTableData(DM_GY_SWJG, swjgDm, DmGySwjgDO.class);
            if (GyUtils.isNull(data) || GyUtils.isNull(data.getSjswjgDm())) {
                return hbjgList;
            }
            String sjswjgDm = data.getSjswjgDm();
            final DmGySwjgDO sjswjgMap = CacheUtils.getTableData(DM_GY_SWJG, sjswjgDm, DmGySwjgDO.class);
            final String swjgDmNew = sjswjgMap.getSwjgDm();
            hbjgList = getHbjgList(swjgDmNew);
        }
        return hbjgList;
    }
//
//
    /**
     * 环保税税源登记页面获取征收子目下拉数据信息
     */
    @Override
    public List<ZszmVo> loadZszmList(LoadZszmReqVo vo) {
        List<ZszmVo> zszmList = new ArrayList<>();
        String swjgDm = vo.getSwjgDm();
        String zspmDm = vo.getZspmDm();
        String sfczhygc = GyUtils.isNull(vo.getSfczhygc()) ? "N" : vo.getSfczhygc();
        final String cycs = vo.getCycs();
        // 入参cycs、swjgDm、zspmDm、sfczhygc，通过缓存表获取CS_SB_HBSWRDLZTZZPZ
        List<Map<String, Object>> hbswrdlztzzpzList = getHbswrdlztzzpzList(swjgDm);
        if (zspmDm != null && !zspmDm.isEmpty()) {
            hbswrdlztzzpzList = findListInMapList("zspmdm", zspmDm, hbswrdlztzzpzList);
        }
        //如果cycs不为空，则sfcycsbz以cycs的值查询，结果赋值zszmList；
        //如果cycs为空，则sfcycsbz分别以Y和N的值查询配置表，并将结果合并到zszmList中 不过滤
        if (GyUtils.isNotNull(cycs)) {
            hbswrdlztzzpzList = findListInMapList("sfcycsbz", cycs, hbswrdlztzzpzList);
        }
        // 海洋工程选了Y会将配置表中SFHYGC字段为N和Y的都查出来
        if (sfczhygc.equals("N")) {
            hbswrdlztzzpzList = findListInMapList("sfhygc", sfczhygc, hbswrdlztzzpzList);
        }
        if (hbswrdlztzzpzList == null || hbswrdlztzzpzList.isEmpty()) {
            return zszmList;
        }
        for (Map<String, Object> map : hbswrdlztzzpzList) {
            // 过滤不在有效期的数据
            if (!GYCastUtils.sqAcross(new Date(), new Date(),
                    FtsCxsUtils.cast2Date(map.get("yxqq")),
                    FtsCxsUtils.cast2Date(map.get("yxqz")))) {
                continue;
            }
            if (map.get("zszmdm") != null) {
                ZszmVo zszmVo = new ZszmVo();
                String zszmDm = map.get("zszmdm").toString();
                zszmVo.setCode(zszmDm);
                // 根据征收子目代码找征收子目名称
                if (!GyUtils.isNull(map.get("zszmmc"))) {
                    zszmVo.setCaption(GYCastUtils.cast2Str(map.get("zszmmc")));
                    zszmList.add(zszmVo);
                }
            }
        }
        // 去重
        zszmList = zszmList.stream().distinct().collect(Collectors.toList());
        // 如果cjzszm不为空，则过滤zszmList，只保留cjzszm中存在的征收子目信息
        return zszmList;
    }

    /**
     * 环保税税源登记页面获取征收品目下拉数据信息
     */
    @Override
    public List<ZspmVo> loadZspmList(LoadZspmReqVo vo) {
        List<ZspmVo> zspmList = new ArrayList<>();
        final String swjgDm = vo.getSwjgDm();
        final String fzspmDm = vo.getFzspmDm();
        final String sfglbz = vo.getSfglbz();
        final String zywrwlbDm = vo.getZywrwlbDm();
        final List<ZspmVo> zspmListTmp = new ArrayList<>();
        HashMap<String, Object> csMap = new HashMap<>();
        csMap.put("A", "101211");
        csMap.put("W", "101212");
        csMap.put("S", "101213");
        csMap.put("N", "101214");
        //2.通过缓存表获取CS_SB_HBSWRDLZTZZPZ (环保机构与税务机关对照表) , DM_GY_SWJG (税务机关代码表),查询得到征收品目配置信息。
        //A.根据session中的纳税人主管税务机关查询该DM_GY_SWJG，获取税务机关本上级配置信息。
        List<Map<String, Object>> hbswrdlztzzpzList = getHbswrdlztzzpzList(swjgDm);
        if (hbswrdlztzzpzList == null || hbswrdlztzzpzList.isEmpty()) {
            return zspmList;
        }
        hbswrdlztzzpzList.forEach(map -> {
            // 过滤不在有效期的数据
            if (!GYCastUtils.sqAcross(new Date(), new Date(),
                    CxsJmxzUtils.cast2Date(map.get("yxqq")),
                    CxsJmxzUtils.cast2Date(map.get("yxqz")))) {
                return;
            }
            ZspmVo zspmVo = new ZspmVo();
            String zspmDm = map.get("zspmdm").toString();
            zspmVo.setCode(zspmDm);
            // 根据征收品目代码找征收品目名称
            if (!GyUtils.isNull(map.get("zspmmc"))) {
                zspmVo.setCaption(GYCastUtils.cast2Str(map.get("zspmmc")));
                zspmVo.setSjzspmDm(GYCastUtils.cast2Str(map.get("sjzspmdm")));
                zspmListTmp.add(zspmVo);
            }
        });
        // 去重
        zspmList = zspmListTmp.stream().distinct().collect(Collectors.toList());
        //3.如果sfglbz为Y，则对zspmList增加过滤：
        if ("Y".equals(sfglbz)) {
            List<ZspmVo> glZspmList = new ArrayList<>();
            zspmList.forEach(zspmVo -> {
                //（1）循环zspmList，取值SJZSPM_DM（字段值），赋值给sjcode变量；
                String sjcode = zspmVo.getSjzspmDm();
                //（2）在csMap获取zywrwlbDm对应的value，并赋值给变量zspmStart。
                String zspmStart = csMap.get(zywrwlbDm).toString();
                //（3）条件1：sjcode不是以zspmStart开头；
                //（4）条件2：zywrwlbDm等于w并且sjcode不等于fzspmDm；
                //（5）条件1或条件2满足条件，则删除该条信息。
                if (sjcode.startsWith(zspmStart) && !(zywrwlbDm.equals("W") && !sjcode.equals(fzspmDm))) {
                    glZspmList.add(zspmVo);
                }
            });
            return glZspmList;
        }
        return zspmList;
    }

    /**
     * 获取街道乡镇下拉数据信息
     *
     * @param xzqhDm
     * @return
     */
    @Override
    public List<JdxzVO> loadJdxzList(String xzqhDm) {
        // 通过xzqhDm、gdslx_dm=1、yxbz=Y、xybz =Y查询cs_dj_jdxzxzqhdzb，获取jdxz_dm字段
        List<JdxzVO> jdxzList = new ArrayList<>();
        List<Map<String, Object>> jdxzAllList = CacheUtils.getTableListCacheDataByKey("cs_dj_jdxzxzqhdzb", xzqhDm);
        jdxzAllList = GYCastUtils.cast2DefKeyList(jdxzAllList);//将大写下划线Key转换成标准小写Key
        jdxzAllList = findListInMapList("gdslxdm", "1", jdxzAllList);
        jdxzAllList.forEach(map -> {
            // 通过jdxz_dm、yxbz=Y、xybz =Y查询dm_gy_jdxz，获取jdxz_dm 、 jdxzmc，组装jdxzList
            Map<String, Object> jdxzxz = CacheUtils.getTableData("dm_gy_jdxz", map.get("jdxzdm").toString());
            if (jdxzxz != null && !jdxzxz.isEmpty()) {
                JdxzVO jdxzVO = new JdxzVO();
                jdxzVO.setCode(jdxzxz.get("jdxzDm").toString());
                jdxzVO.setCaption(jdxzxz.get("jdxzmc").toString());
                jdxzList.add(jdxzVO);
            }

        });
        return jdxzList;
    }

    /**
     * 获取税收减免性质下拉数据
     *
     * @return
     */
    @Override
    public List<SsjmxzVO> loadSsjmxzDmList(LoadSsjmxzReqVo vo) {
        String djxh = vo.getDjxh();
        String skssqz = vo.getSkssqz();
        String skssqq = vo.getSkssqq();
        String swjgDm = vo.getSwjgDm();
        String zspmDm = vo.getZspmDm();
        String zszmDm = vo.getZszmDm();
        String zsxmDm = "10121";
        final Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("djxh", djxh);
        reqMap.put("zsxmDm", zsxmDm);
        reqMap.put("zspmDm", zspmDm);
        reqMap.put("zszmDm", zszmDm);
        reqMap.put("skssqq", skssqq);
        reqMap.put("skssqz", skssqz);
        reqMap.put("swjgDm", swjgDm);
        List<Map<String, Object>> ssjmxxDmList = new ArrayList<>();
        try {
            ssjmxxDmList = FcsCztdsyssycjUtils.getCxsjmxzListRk(reqMap);
        } catch (Exception e) {
            log.info("获取税收减免性质下拉数据异常" + e.getMessage());
        }
        List<SsjmxzVO> ssjmxzDmList = new ArrayList<>();
        if (!GyUtils.isNull(ssjmxxDmList)) {
            ssjmxxDmList.forEach(map -> {
                SsjmxzVO ssjmxzVO = new SsjmxzVO();
                ssjmxzVO.setCode(map.get("ssjmxzDm").toString());
                ssjmxzVO.setCaption(map.get("ssjmxzDm").toString() + "|" + map.get("ssjmxzmc").toString());
                ssjmxzVO.setJmed(GYCastUtils.cast2Str(map.get("jmed")));
                ssjmxzVO.setJmfd(GYCastUtils.cast2Str(map.get("jmfd")));
                ssjmxzVO.setJmsl(GYCastUtils.cast2Str(map.get("jmsl")));
                ssjmxzVO.setJmzlxDm(GyUtils.isNotNull(map.get("jmzlxDm")) ? map.get("jmzlxDm").toString() : null);
                ssjmxzVO.setSwsxDm(GYCastUtils.cast2Str(map.get("swsxDm")));
                ssjmxzDmList.add(ssjmxzVO);
            });
        }
        return ssjmxzDmList;
    }


    /**
     * 保存环保税基础税源登记信息
     * @param hbssyxxcjSaveReqVo
     * @return
     */
    @Override
    public Map<String, Object> saveJcxx(HbssyxxcjSaveReqVo hbssyxxcjSaveReqVo) {
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("jcxxFlag", "Y");
        returnMap.put("fbFlag", "Y");
        returnMap.put("returnCode", "1");
        HbsJcxxVO jcxxVO = hbssyxxcjSaveReqVo.getHbsJcxxVO();
        // 环保税基础信息采集表
        jcxxVO.getHbsjcxxcjb().setBczt(BcztConstants.DTJ);
        //保存环保税基础信息到本地表
        this.saveHbssyxxcj(jcxxVO);
        final String ywuuid = jcxxVO.getHbsjcxxcjb().getHbsjcxxuuid();
        //保存环保税基础信息到核心
        CommonResult<Object> result = sjjhcl("CSSRPA0003",JsonUtils.toJson(jcxxVO),ywuuid);
        if(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())|| GyUtils.isNull(result.getData())){
            throw ServiceExceptionUtil.exception(500, result.getMsg());
        }
        Map resultMap = JsonUtils.toMap((String) result.getData());
        if (GyUtils.isNull(resultMap.get("hbsjcxxuuid"))) {
            hbssyxxcjSaveReqVo = queryHbscjywbw(ControllerUtils.getDjxh());
            // 保存明细表
            returnMap = this.saveHBSCJYWBW(hbssyxxcjSaveReqVo, returnMap, jcxxVO.getHbsjcxxcjb().getDjxh(), jcxxVO.getHbsjcxxcjb().getZgswskfjDm());
            return returnMap;
        }
        return returnMap;
    }

    /**
     * 保存大气水、固废、噪声、产排污系数等明细信息
     * @param hbssyxxcjSaveReqVo
     * @param returnMap
     * @param djxh
     * @param zgswskfjDm
     * @return
     */
    @Override
    public Map<String, Object> saveHBSCJYWBW(HbssyxxcjSaveReqVo hbssyxxcjSaveReqVo,
                                             Map<String, Object> returnMap, String djxh, String zgswskfjDm) {
        // 保存大气水
        if (GyUtils.isNotNull(hbssyxxcjSaveReqVo.getBccjList())) {
            List<BccjxxVO> bccjList = hbssyxxcjSaveReqVo.getBccjList();
            // 保存大气水时，过滤掉污染物排放量计算方法为3、4、5的税源。
            Iterator<BccjxxVO> iterator = bccjList.iterator();
            while (iterator.hasNext()) {
                BccjxxVO bccjxxVO = iterator.next();
                if ("3".equals(bccjxxVO.getWrwpfljsffDm())
                        || "4".equals(bccjxxVO.getWrwpfljsffDm())
                        || "5".equals(bccjxxVO.getWrwpfljsffDm())) {
                    iterator.remove();
                }
            }
        }

        // 保存排污
        if (GyUtils.isNotNull(hbssyxxcjSaveReqVo.getPwxscjbxxList())) {
            List<PwxscjbVO> pwxscjbxxList = hbssyxxcjSaveReqVo.getPwxscjbxxList();
            final Iterator<PwxscjbVO> pwxscjbDTOIterator = pwxscjbxxList.iterator();
            while (pwxscjbDTOIterator.hasNext()) {
                final PwxscjbVO pwxscjbVO = pwxscjbDTOIterator.next();
                if (GyUtils.isNull(pwxscjbVO.getCwxs()) && GyUtils.isNull(pwxscjbVO.getPwxs())) {
                    pwxscjbDTOIterator.remove();
                }
            }
        }
        //设置uuid
        List<BccjxxVO> bccjxxVOList = new ArrayList<BccjxxVO>();
        List<ZscjxxVO> zscjxxVOList = new ArrayList<ZscjxxVO>();
        List<GfcjxxVO> gfcjxxVOList = new ArrayList<GfcjxxVO>();
        List<PwxscjbVO> pwxscjbxxVOList = new ArrayList<PwxscjbVO>();
        for(SyxxVO syxx:hbssyxxcjSaveReqVo.getHbsJcxxVO().getSyxxGridList()){
            if("A".equals(syxx.getZywrwlbDm()) ||"W".equals(syxx.getZywrwlbDm())){
                bccjxxVOList.add(BeanUtils.toBean(syxx,BccjxxVO.class));
            }
            if("N".equals(syxx.getZywrwlbDm())){
                zscjxxVOList.add(BeanUtils.toBean(syxx,ZscjxxVO.class));
            }
            if("S".equals(syxx.getZywrwlbDm())){
                gfcjxxVOList.add(BeanUtils.toBean(syxx,GfcjxxVO.class));
            }
            if("4".equals(syxx.getWrwpfljsffDm())){
                pwxscjbxxVOList.add(BeanUtils.toBean(syxx,PwxscjbVO.class));
            }
        }
        hbssyxxcjSaveReqVo.setBccjList(bccjxxVOList);
        hbssyxxcjSaveReqVo.setZscjxxList(zscjxxVOList);
        hbssyxxcjSaveReqVo.setGfcjxxList(gfcjxxVOList);
        hbssyxxcjSaveReqVo.setPwxscjbxxList(pwxscjbxxVOList);
        String cjbz = this.saveHbscjYwbw(hbssyxxcjSaveReqVo,djxh);
        CommonResult result = this.sjjhcl("CSSRPA0004",JsonUtils.toJson(hbssyxxcjSaveReqVo), "");
        if(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())){
            throw ServiceExceptionUtil.exception(500, result.getMsg());
        }
        Map resultMap = JsonUtils.toMap((String) result.getData());
        if (!"Y".equals(resultMap.get("cjbz"))) {
            returnMap.put("fbFlag", "N");
            returnMap.put("returnCode", "0");
        }
        return returnMap;
    }

    /**
     * 获取当前纳税人已采集申报计算税源信息
     *
     * @return
     */
    @Override
    public SbjsDTO initSbjsJcxx(InitSbjsReqVO initSbjsReqVO) {
        log.info("initSbjsJcxx------------------------1--------------initSbjsJcxx");
        final SbjsDTO sbjsDTO = new SbjsDTO();
        final String djxh = initSbjsReqVO.getDjxh();
        final String skssqq = initSbjsReqVO.getSkssqq();
        final String skssqz = initSbjsReqVO.getSkssqz();
        final String sbuuid = initSbjsReqVO.getSbuuid();
        final String zgswskfjDm = initSbjsReqVO.getZgswskfjDm();
        String sbbz = initSbjsReqVO.getSbbz();
        String cxstysbuuid = "";
        log.info("initSbjsJcxx------------------------2--------------initSbjsJcxx");
        if (GyUtils.isNotNull(sbuuid)) {
//            sbbz = "cwgz";
//            final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOS = cxscwgzApi.queryCxssbmx(initSbjsReqVO.getPzxh(), "");
//            if (GyUtils.isNull(cchxwsnssbzbmxVOS)) {
//                sbjsDTO.setReturnCode(FAIL_CODE);
//                sbjsDTO.setReturnMsg("当期未查询到有效可更正税源信息，请核实后处理。");
//                return sbjsDTO;
//            }
//            cxstysbuuid = cchxwsnssbzbmxVOS.get(0).getCxstysbuuid();
        }
        log.info("initSbjsJcxx------------------------3--------------initSbjsJcxx");
        // 通过微服务调用核心接口（SWZJ.HXZG.SB.HBSCJQUERYSBJS）获取当前纳税人已采集申报计算税源信息SbjsDTO
        HbssbjsNsrxxVO nsrxxVo = new HbssbjsNsrxxVO();
        nsrxxVo.setDjxh(djxh);
        nsrxxVo.setSkssqq(skssqq);
        nsrxxVo.setSkssqz(skssqz);
        nsrxxVo.setSbsxDm1("11");
//        Map<String, String> expend = new HashMap<>();
//        expend.put("sjjg", zgswskfjDm);
//        expend.put("sjry", SjryUtil.getSjry(zgswskfjDm));
//        HXZGSB10812Response res = null;
//        try {
//            res = Gt3Invoker.invoke("SWZJ.HXZG.SB.HBSCJQUERYSBJS", expend, req, HXZGSB10812Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.HBSCJQUERYSBJS异常：", e);
//            sbjsDTO.setReturnCode("0");
//            sbjsDTO.setReturnMsg(e.getMessage());
//            return sbjsDTO;
//        }
        log.info("initSbjsJcxx------------------------4--------------initSbjsJcxx");
//        SbjsVO sbjsVo = res.getSbjsVo();
        //查询环境保护税申报计算及减免信息
//        nsrxxVo.setDjxh(new MockUtils().mock(nsrxxVo.getDjxh(),"10214406000000002256"));
//        nsrxxVo.setSkssqq(new MockUtils().mock(nsrxxVo.getSkssqq(),"2020-06-01"));
//        nsrxxVo.setSkssqz(new MockUtils().mock(nsrxxVo.getSkssqz(),"2024-08-31"));
        HXZGSB10812Request req = new HXZGSB10812Request();
        req.setNsrxxVo(nsrxxVo);
        CommonResult<Object> result = sjjhcl("CSSRPA0006",JsonUtils.toJson(req),"");
        if(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())|| GyUtils.isNull(result.getData())){
            throw ServiceExceptionUtil.exception(500, result.getMsg());
        }
        Map data = JsonUtils.toMap((String)result.getData());
        SbjsVO sbjsVo = new SbjsVO();
        if(GyUtils.isNotNull(data.get("sbjsVo"))){
            String sbjsVOStr = JsonUtils.toJson(data.get("sbjsVo"));
            com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.sb10813.SbjsVO hxsbjsVo = JsonUtils.toBean(sbjsVOStr, com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.sb10813.SbjsVO.class);
            if(GyUtils.isNotNull(hxsbjsVo.getDqswrGrid())&&GyUtils.isNotNull(hxsbjsVo.getDqswrGrid().getDqswrGridLb())){
                sbjsVo.setDqswrList(BeanUtils.toBean(hxsbjsVo.getDqswrGrid().getDqswrGridLb(),DqswrVO.class));
            }
            if(GyUtils.isNotNull(hxsbjsVo.getGfGrid())&&GyUtils.isNotNull(hxsbjsVo.getGfGrid().getGfGridLb())){
                sbjsVo.setGfxxList(BeanUtils.toBean(hxsbjsVo.getGfGrid().getGfGridLb(),GfsyxxVO.class));
            }
            if(GyUtils.isNotNull(hxsbjsVo.getZsGrid())&&GyUtils.isNotNull(hxsbjsVo.getZsGrid().getZsGridLb())){
                sbjsVo.setZsxxList(BeanUtils.toBean(hxsbjsVo.getZsGrid().getZsGridLb(),ZssyxxVO.class));
            }
            if(GyUtils.isNotNull(hxsbjsVo.getCycsGrid())&&GyUtils.isNotNull(hxsbjsVo.getCycsGrid().getCycsGridLb())){
                sbjsVo.setCycssyxxList(BeanUtils.toBean(hxsbjsVo.getCycsGrid().getCycsGridLb(),CycssyxxVO.class));
            }
        }
        //SbjsVO sbjsVo = this.hxzgHbsService.querySbjsVO(nsrxxVo);
        // 如果skssqq等于skssqz,直接返回
        if (skssqq.equals(skssqz)) {
            // 按次申报过滤已申报
            List<CycssyxxVO> cycsGridLb = sbjsVo.getCycssyxxList();
            List<CycssyxxDTO> cycssyxxDTOList = BeanUtils.toBean(cycsGridLb, CycssyxxDTO.class);
            sbjsDTO.setCycssyxxList(cycssyxxDTOList);
            // 错误更正需要的财行税统一申报uuid
            sbjsDTO.setCxstysbuuid(cxstysbuuid);
            return sbjsDTO;
        }
        log.info("initSbjsJcxx------------------------5--------------initSbjsJcxx");
        // 获取抽样测算采集信息
        List<CycssyxxVO> cycsGridLb = sbjsVo.getCycssyxxList();
        if(GyUtils.isNotNull(cycsGridLb)){
            List<CycssyxxDTO> cycssyxxDTOList = BeanUtils.toBean(cycsGridLb, CycssyxxDTO.class);
            // 方便前端识别抽样
            cycssyxxDTOList.forEach(cycssyxxDTO -> {
                cycssyxxDTO.setWrwpfljsffDm("5");
                cycssyxxDTO.setYf(cycssyxxDTO.getYf().trim());
            });
            sbjsDTO.setCycssyxxList(cycssyxxDTOList.stream()
                    .sorted(Comparator.comparing(CycssyxxDTO::getYf)).collect(Collectors.toList()));
        }

        // 获取大气水污染采集信息
        List<DqswrVO> dqswrGridLb = sbjsVo.getDqswrList();
        List<DqswrDTO> dqswrDTOList = new ArrayList<>();
        if(GyUtils.isNotNull(dqswrGridLb)){
            dqswrDTOList = BeanUtils.toBean(dqswrGridLb, DqswrDTO.class);
            dqswrDTOList.forEach(dqswrDTO -> dqswrDTO.setYf(dqswrDTO.getYf().trim()));
            sbjsDTO.setDqswrList(dqswrDTOList.stream()
                    .sorted(Comparator.comparing(DqswrDTO::getHgbhssybh)
                            .thenComparing(DqswrDTO::getYf)).collect(Collectors.toList()));
        }

        // 获取固废采集信息
        List<GfsyxxVO> gfGridLb = sbjsVo.getGfxxList();
        if(GyUtils.isNotNull(gfGridLb)){
            List<GfsyxxDTO> gfsyxxDTOList = BeanUtils.toBean(gfGridLb, GfsyxxDTO.class);
            gfsyxxDTOList.forEach(gfsyxxDTO -> gfsyxxDTO.setYf(gfsyxxDTO.getYf().trim()));
            sbjsDTO.setGfxxList(gfsyxxDTOList.stream()
                    .sorted(Comparator.comparing(GfsyxxDTO::getHgbhssybh)
                            .thenComparing(GfsyxxDTO::getYf)).collect(Collectors.toList()));
        }
        // 获取噪声采集信息
        List<ZssyxxVO> zsGridLb = sbjsVo.getZsxxList();
        if(GyUtils.isNotNull(zsGridLb)){
            List<ZssyxxDTO> zssyxxDTOList = BeanUtils.toBean(zsGridLb, ZssyxxDTO.class);
            zssyxxDTOList.forEach(zssyxxDTO -> zssyxxDTO.setYf(zssyxxDTO.getYf().trim()));
            zssyxxDTOList.stream().sorted(Comparator.comparing(ZssyxxDTO::getHgbhssybh).thenComparing(ZssyxxDTO::getYf));
            sbjsDTO.setZsxxList(zssyxxDTOList);
        }
        List<CycssyxxDTO> cycssyxxList = sbjsDTO.getCycssyxxList();
        List<DqswrDTO> dqswrList = sbjsDTO.getDqswrList();
        List<GfsyxxDTO> gfxxList = sbjsDTO.getGfxxList();
        List<ZssyxxDTO> zsxxList = sbjsDTO.getZsxxList();
        log.info("initSbjsJcxx------------------------6--------------initSbjsJcxx");
        // 获取环保税登记税源基础信息
        InitcjPageReqVO initcjPageReqVO = new InitcjPageReqVO();
        initcjPageReqVO.setDjxh(djxh);
        initcjPageReqVO.setSbbz(sbbz);
        initcjPageReqVO.setSkssqq(skssqq);
        initcjPageReqVO.setSkssqz(skssqz);
        HbsJcxxVO hbsJcxxVO = initcjPage(initcjPageReqVO);
        log.info("initSbjsJcxx------------------------7--------------initSbjsJcxx");
        if (!GyUtils.isNull(hbsJcxxVO.getSyxxGridList())) {
            List<SyxxVO> syxxGridList = hbsJcxxVO.getSyxxGridList();
            for (SyxxVO syxxDTO : syxxGridList) {
                Date syyxqq = GYCastUtils.cast2Date(syxxDTO.getSyyxqq());
                Date syyxqz = GYCastUtils.cast2Date(syxxDTO.getSyyxqz());
                // 判断所属期
                if (!GYCastUtils.sqAcross(
                        GYCastUtils.cast2Date(skssqq), GYCastUtils.cast2Date(skssqz),
                        syyxqq, syyxqz)) {
                    continue;
                }
                // 一条税源加工成不同月份的三条税源
                Date syyxqqTmp = syyxqq.compareTo(GYCastUtils.cast2Date(skssqq)) >= 0
                        ? syyxqq : GYCastUtils.cast2Date(skssqq);
                Date syyxqzTmp = syyxqz.compareTo(GYCastUtils.cast2Date(skssqz)) < 0
                        ? syyxqz : GYCastUtils.cast2Date(skssqz);
                final List<Map> monthDate = HbssyxxcjUtils.getMonthDate(syyxqqTmp, syyxqzTmp);
                for (Map map : monthDate) {
                    Date date = CxsJmxzUtils.cast2Date(map.get("beginTime"));
                    String yf = Integer.toString(HbssyxxcjUtils.getYfnum(date));
                    // 判断同一月份、污染物名称、征收子目、污染物排放量计算方法、税源编号的税源信息是否在SbjsDTO中存在
                    // 污染物类别
                    final String zywrwlbDm = syxxDTO.getZywrwlbDm();
                    // 污染物名称
                    final String zspmDm = syxxDTO.getZspmDm();
                    // 污染物排放量计算方法
                    final String wrwpfljsffDm = syxxDTO.getWrwpfljsffDm();
                    // 税源编号
                    final String hgbhssybh = syxxDTO.getHgbhssybh();
                    // 税源uuid
                    final String syuuid = syxxDTO.getSyuuid();
                    // 如果污染物类别是W或A，如果污染物排放量计算方法不是3、4、5，并且对应dqswrwjcxxuuid字段为空，则过滤掉该条信息
                    if ("W".equals(zywrwlbDm) || "A".equals(zywrwlbDm)) {
                        if (!"3".equals(wrwpfljsffDm)
                                && !"4".equals(wrwpfljsffDm)
                                && !"5".equals(wrwpfljsffDm)
                                && syxxDTO.getDqswrwjcxxuuid() == null) {
                            continue;
                        }
                    }
                    // 如果污染物类别是N,并且对应的zsjcxxuuid字段为空，则过滤掉该条信息
                    if ("N".equals(zywrwlbDm) && syxxDTO.getZsjcxxuuid() == null) {
                        continue;
                    }
                    // 如果污染物类别是S,并且对应的fzssuuid字段为空，则过滤掉该条信息
                    if ("S".equals(zywrwlbDm) && syxxDTO.getFzssuuid() == null) {
                        continue;
                    }
                    // 判断属于哪个类别
                    // 抽样
                    if (GyUtils.isNotNull(wrwpfljsffDm) && "5".equals(wrwpfljsffDm)) {
                        if (GyUtils.isNotNull(cycssyxxList)) {
                            // 是否存在
                            Boolean czbz = false;
                            // 存在一个税源编号品目相同，税源采集时子目为空，申报计算页面采集了子目的情况，通过syuuid判断
                            for (CycssyxxDTO cycssyxxDTO : cycssyxxList) {
                                if (zspmDm.equals(cycssyxxDTO.getZspmDm())
                                        && yf.equals(cycssyxxDTO.getYf().trim())
                                        && hgbhssybh.equals(cycssyxxDTO.getHgbhssybh())
                                        && syuuid.equals(cycssyxxDTO.getSyuuid())) {
                                    czbz = true;
                                }
                            }
                            // 不存在
                            if (!czbz) {
                                // 新增
                                CycssyxxDTO cycssyxxDTONew = BeanUtils.toBean(syxxDTO, CycssyxxDTO.class);
                                cycssyxxDTONew.setSbbz("N");
                                cycssyxxDTONew.setTzzb(syxxDTO.getZszmDm());
                                cycssyxxDTONew.setYf(yf);
                                cycssyxxDTONew.setSkssqq(GYCastUtils.cast2Str(map.get("beginTime")));
                                cycssyxxDTONew.setSkssqz(GYCastUtils.cast2Str(map.get("endTime")));
                                cycssyxxDTONew.setZbz(0.0);
                                cycssyxxList.add(cycssyxxDTONew);
                            }
                        } else {
                            // 新增
                            CycssyxxDTO cycssyxxDTONew = BeanUtils.toBean(syxxDTO, CycssyxxDTO.class);
                            cycssyxxDTONew.setSbbz("N");
                            cycssyxxDTONew.setTzzb(syxxDTO.getZszmDm());
                            cycssyxxDTONew.setYf(yf);
                            cycssyxxDTONew.setSkssqq(GYCastUtils.cast2Str(map.get("beginTime")));
                            cycssyxxDTONew.setSkssqz(GYCastUtils.cast2Str(map.get("endTime")));
                            cycssyxxDTONew.setZbz(0.0);
                            cycssyxxList = new ArrayList<>();
                            cycssyxxList.add(cycssyxxDTONew);
                        }
                    }
                    // 大气水
                    if (("W".equals(zywrwlbDm)
                            || "A".equals(zywrwlbDm))
                            && !"5".equals(wrwpfljsffDm)) {
                        if (GyUtils.isNotNull(dqswrList)) {
                            // 是否存在
                            Boolean czbz = false;
                            for (DqswrDTO dqswrDTO : dqswrList) {
                                // 存在一个税源编号品目相同，税源采集时子目为空，申报计算页面采集了子目的情况，通过syuuid判断
                                if (zspmDm.equals(dqswrDTO.getZspmDm())
                                        && yf.equals(dqswrDTO.getYf().trim())
                                        && wrwpfljsffDm.equals(dqswrDTO.getWrwpfljsffDm())
                                        && hgbhssybh.equals(dqswrDTO.getHgbhssybh())
                                        && syuuid.equals(dqswrDTO.getSyuuid())) {
                                    czbz = true;
                                }
                            }
                            // 不存在
                            if (!czbz) {
                                // 新增
                                DqswrDTO dqswrDTONew = BeanUtils.toBean(syxxDTO, DqswrDTO.class);
                                dqswrDTONew.setSbbz("N");
                                dqswrDTONew.setYf(yf);
                                dqswrDTONew.setSkssqq(GYCastUtils.cast2Str(map.get("beginTime")));
                                dqswrDTONew.setSkssqz(GYCastUtils.cast2Str(map.get("endTime")));
                                // 如果是排污系数计算方法的置空排污系数等字段
                                if ("4".equals(dqswrDTONew.getWrwpfljsffDm())) {
                                    dqswrDTONew.setPwxs(null);
                                    dqswrDTONew.setCwxs(null);
                                }
                                dqswrList.add(dqswrDTONew);
                            }
                        } else {
                            // 新增
                            DqswrDTO dqswrDTONew = BeanUtils.toBean(syxxDTO, DqswrDTO.class);
                            dqswrDTONew.setSbbz("N");
                            dqswrDTONew.setYf(yf);
                            dqswrDTONew.setSkssqq(GYCastUtils.cast2Str(map.get("beginTime")));
                            dqswrDTONew.setSkssqz(GYCastUtils.cast2Str(map.get("endTime")));
                            // 如果是排污系数计算方法的置空排污系数等字段
                            if ("4".equals(dqswrDTONew.getWrwpfljsffDm())) {
                                dqswrDTONew.setPwxs(null);
                                dqswrDTONew.setCwxs(null);
                            }
                            dqswrList = new ArrayList<>();
                            dqswrList.add(dqswrDTONew);
                        }
                    }
                    // 固废
                    if ("S".equals(zywrwlbDm)) {
                        if (GyUtils.isNotNull(gfxxList)) {
                            // 是否存在
                            Boolean czbz = false;
                            for (GfsyxxDTO gfsyxxDTO : gfxxList) {
                                // 存在一个税源编号品目相同，税源采集时子目为空，申报计算页面采集了子目的情况，通过syuuid判断
                                if (zspmDm.equals(gfsyxxDTO.getZspmDm())
                                        && yf.equals(gfsyxxDTO.getYf().trim())
                                        && hgbhssybh.equals(gfsyxxDTO.getHgbhssybh())
                                        && syuuid.equals(gfsyxxDTO.getSyuuid())) {
                                    czbz = true;
                                }
                            }
                            // 不存在
                            if (!czbz) {
                                // 新增
                                GfsyxxDTO gfsyxxDTONew = BeanUtils.toBean(syxxDTO, GfsyxxDTO.class);
                                gfsyxxDTONew.setSbbz("N");
                                gfsyxxDTONew.setYf(yf);
                                gfsyxxDTONew.setSkssqq(GYCastUtils.cast2Str(map.get("beginTime")));
                                gfsyxxDTONew.setSkssqz(GYCastUtils.cast2Str(map.get("endTime")));
                                gfxxList.add(gfsyxxDTONew);
                            }
                        } else {
                            // 新增
                            GfsyxxDTO gfsyxxDTONew = BeanUtils.toBean(syxxDTO, GfsyxxDTO.class);
                            gfsyxxDTONew.setSbbz("N");
                            gfsyxxDTONew.setYf(yf);
                            gfsyxxDTONew.setSkssqq(GYCastUtils.cast2Str(map.get("beginTime")));
                            gfsyxxDTONew.setSkssqz(GYCastUtils.cast2Str(map.get("endTime")));
                            gfxxList  = new ArrayList<>();
                            gfxxList.add(gfsyxxDTONew);
                        }
                    }
                    // 噪声
                    if ("N".equals(zywrwlbDm)) {
                        if (GyUtils.isNotNull(zsxxList)) {
                            // 是否存在
                            Boolean czbz = false;
                            for (ZssyxxDTO zssyxxDTO : zsxxList) {
                                if (hgbhssybh.equals(zssyxxDTO.getHgbhssybh()) && yf.equals(zssyxxDTO.getYf().trim())) {
                                    zssyxxDTO.setSfzycsbz(syxxDTO.getSfzycsbz());
                                    czbz = true;
                                }
                            }
                            // 不存在
                            if (!czbz) {
                                // 新增
                                ZssyxxDTO zssyxxDTONew = BeanUtils.toBean(syxxDTO, ZssyxxDTO.class);
                                zssyxxDTONew.setSbbz("N");
                                zssyxxDTONew.setYf(yf);
                                zssyxxDTONew.setSkssqq(GYCastUtils.cast2Str(map.get("beginTime")));
                                zssyxxDTONew.setSkssqz(GYCastUtils.cast2Str(map.get("endTime")));
                                zssyxxDTONew.setJcfbs1(0.0);
                                zssyxxDTONew.setCbfbs(0);
                                if ("Y".equals(syxxDTO.getSfzycsbz())) {
                                    // 昼夜产生为是的时候,新增昼夜两条
                                    zssyxxDTONew.setZssdDm("1");
                                    zsxxList.add(zssyxxDTONew);
                                    final ZssyxxDTO zssyxxDTO = new ZssyxxDTO();
                                    BeanUtils.copyBean(zssyxxDTONew, zssyxxDTO);
                                    zssyxxDTO.setZssdDm("2");
                                    zsxxList.add(zssyxxDTO);
                                } else {
                                    zsxxList.add(zssyxxDTONew);
                                }
                            }
                        } else {
                            // 新增
                            ZssyxxDTO zssyxxDTONew = BeanUtils.toBean(syxxDTO, ZssyxxDTO.class);
                            zssyxxDTONew.setSbbz("N");
                            zssyxxDTONew.setYf(yf);
                            zssyxxDTONew.setSkssqq(GYCastUtils.cast2Str(map.get("beginTime")));
                            zssyxxDTONew.setSkssqz(GYCastUtils.cast2Str(map.get("endTime")));
                            zssyxxDTONew.setJcfbs1(0.0);
                            zssyxxDTONew.setCbfbs(0);
                            zsxxList  = new ArrayList<>();
                            if ("Y".equals(syxxDTO.getSfzycsbz())) {
                                // 昼夜产生为是的时候,新增昼夜两条
                                zssyxxDTONew.setZssdDm("1");
                                zsxxList.add(zssyxxDTONew);
                                final ZssyxxDTO zssyxxDTO = new ZssyxxDTO();
                                BeanUtils.copyBean(zssyxxDTONew, zssyxxDTO);
                                zssyxxDTO.setZssdDm("2");
                                zsxxList.add(zssyxxDTO);
                            } else {
                                zsxxList.add(zssyxxDTONew);
                            }
                        }
                    }
                }
            }
        }
        log.info("initSbjsJcxx------------------------8--------------initSbjsJcxx");
        if (!GyUtils.isNull(hbsJcxxVO.getSyxxGridList())) {
            List<SyxxVO> syxxGridList = hbsJcxxVO.getSyxxGridList();
            for (SyxxVO syxxDTO : syxxGridList) {
                if ("4".equals(syxxDTO.getWrwpfljsffDm())) {
                    for (DqswrDTO dqswrDTO : dqswrDTOList) {
                        if ("4".equals(dqswrDTO.getWrwpfljsffDm()) && syxxDTO.getSyuuid().equals(dqswrDTO.getSyuuid())) {
                            if (!GyUtils.isNull(syxxDTO.getPwxscjbDTOList())) {
                                dqswrDTO.setPwxscjbDTOList(syxxDTO.getPwxscjbDTOList());
                            }
                        }
                    }
                }
            }
        }
        log.info("initSbjsJcxx------------------------9--------------initSbjsJcxx");
        // 错误更正需要的财行税统一申报uuid
        sbjsDTO.setCxstysbuuid(cxstysbuuid);
        // 防止存在‘是否昼夜产生‘为‘是’的情况，没有两条的情况
        final Map<String, Object> zssdMap = new HashMap<>();
        final List<ZssyxxDTO> zsxxTmpList = new ArrayList<>();
        if(GyUtils.isNotNull(zsxxList)){
            for (ZssyxxDTO zssyxxDTO : zsxxList) {
                if ("Y".equals(zssyxxDTO.getSfzycsbz())) {
                    String key = zssyxxDTO.getHgbhssybh() + zssyxxDTO.getYf() + zssyxxDTO.getZssdDm();
                    zssdMap.put(key, zssyxxDTO);
                }
            }
            for (ZssyxxDTO zssyxxDTO : zsxxList) {
                if ("Y".equals(zssyxxDTO.getSfzycsbz())) {
                    String key = zssyxxDTO.getHgbhssybh() + zssyxxDTO.getYf();
                    // 昼
                    String key1 = key + "1";
                    if (GyUtils.isNull(zssdMap.get(key1))) {
                        final ZssyxxDTO zssyxxDTONew = new ZssyxxDTO();
                        BeanUtils.copyBean(zssyxxDTO, zssyxxDTONew);
                        zssyxxDTONew.setZssdDm("1");
                        zssyxxDTONew.setUuid(null);
                        zssyxxDTONew.setJcfbs1(0.0);
                        zssyxxDTONew.setCbts(null);
                        zssyxxDTONew.setLcyszscb(null);
                        zssyxxDTONew.setSl1(0.0);
                        zssyxxDTONew.setYnse(0.0);
                        zssyxxDTONew.setYjse(0);
                        zssyxxDTONew.setYnse(0.0);
                        zssyxxDTONew.setYbtse(0.0);
                        zsxxTmpList.add(zssyxxDTONew);
                    }
                    // 夜
                    String key2 = key + "2";
                    if (GyUtils.isNull(zssdMap.get(key2))) {
                        final ZssyxxDTO zssyxxDTONew = new ZssyxxDTO();
                        BeanUtils.copyBean(zssyxxDTO, zssyxxDTONew);
                        zssyxxDTONew.setZssdDm("2");
                        zssyxxDTONew.setUuid(null);
                        zssyxxDTONew.setJcfbs1(0.0);
                        zssyxxDTONew.setCbts(null);
                        zssyxxDTONew.setLcyszscb(null);
                        zssyxxDTONew.setSl1(0.0);
                        zssyxxDTONew.setYnse(0.0);
                        zssyxxDTONew.setYjse(0);
                        zssyxxDTONew.setYnse(0.0);
                        zssyxxDTONew.setYbtse(0.0);
                        zsxxTmpList.add(zssyxxDTONew);
                    }
                }
            }
        }
        if (!GyUtils.isNull(zsxxTmpList)) {
            zsxxList.addAll(zsxxTmpList);
        }
        log.info("initSbjsJcxx------------------------10--------------initSbjsJcxx");
        // 预填申报信息
        // 查询系统参数“DZSWJ00SBZX020065”，判断该省是否启用税源匹配
        final String openSyppVue = CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020065", "N");
        if (!"N".equals(openSyppVue)) {
            final GetWbjhFzsbjsxxDTO getWbjhFzsbjsxxDTO = new GetWbjhFzsbjsxxDTO();
            getWbjhFzsbjsxxDTO.setDjxh(djxh);
            getWbjhFzsbjsxxDTO.setSbbz(sbbz);
            getWbjhFzsbjsxxDTO.setSbjsDTO(sbjsDTO);
            getWbjhFzsbjsxxDTO.setSkssqq(skssqq);
            getWbjhFzsbjsxxDTO.setSkssqz(skssqz);
//            this.getWbjhFzsbjsxx(getWbjhFzsbjsxxDTO);
        }
        log.info("initSbjsJcxx------------------------11--------------initSbjsJcxx");
        // 第一顺序按照“税源编号”排序，第二顺序按照“月份”，第三顺序按照“污染物类别”排序，大气污染物在前水污染物在后。
        if(GyUtils.isNotNull(dqswrList)){
            sbjsDTO.setDqswrList(dqswrList.stream()
                    .sorted(Comparator.comparing(DqswrDTO::getZywrwlbDm)
//                        .thenComparing(DqswrDTO::getHgbhssybh)
                            .thenComparing(DqswrDTO::getYf)).collect(Collectors.toList()));
        }
        if(GyUtils.isNotNull(gfxxList)){
            sbjsDTO.setGfxxList(gfxxList.stream()
                    .sorted(Comparator.comparing(GfsyxxDTO::getHgbhssybh)
                            .thenComparing(GfsyxxDTO::getYf)).collect(Collectors.toList()));
        }
        if(GyUtils.isNotNull(zsxxList)){
            sbjsDTO.setZsxxList(zsxxList.stream()
                    .sorted(Comparator.comparing(ZssyxxDTO::getHgbhssybh)
                            .thenComparing(ZssyxxDTO::getYf)).collect(Collectors.toList()));
        }
        if(GyUtils.isNotNull(cycssyxxList)){
            sbjsDTO.setCycssyxxList(cycssyxxList.stream()
                    .sorted(Comparator.comparing(CycssyxxDTO::getHgbhssybh)
                            .thenComparing(CycssyxxDTO::getYf)).collect(Collectors.toList()));
        }
        // 福建、天津老电局存在大气污染物fzspmDm不为空的情况，导致前端报错，页面不显示
        if(GyUtils.isNotNull(sbjsDTO.getDqswrList())){
            for (DqswrDTO dqswrDTO : sbjsDTO.getDqswrList()) {
                if ("A".equals(dqswrDTO.getZywrwlbDm()) && !GyUtils.isNull(dqswrDTO.getFzspmDm())) {
                    dqswrDTO.setFzspmDm(null);
                }
            }
        }
        log.info("initSbjsJcxx------------------------12--------------initSbjsJcxx");
        return sbjsDTO;
    }

    /**
     * 保存申报计算及减免信息
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, Object> saveSbjsSyxx(HbssyxsbReqDto dto) {
        final String djxh = dto.getDjxh();
        final String zgswskfjDm = dto.getZgswskfjDm();
        final String sbbz = dto.getSbbz();
        final String skssqq = dto.getSkssqq();
        final String skssqz = dto.getSkssqz();
        final String ysquuid = dto.getYsquuid();
        final Map<String, Object> map = new HashMap<>();
        final List<String> uuids = new ArrayList<>();
        // 获取税源uuid
        final HbsJcxxDTO hbsjcxxcjb = getHbsjcxxcjb(djxh, zgswskfjDm);
        final String hbsjcxxuuid = hbsjcxxcjb.getHbsjcxxcjb().getHbsjcxxuuid();
        // 调用核心接口（SWZJ.HXZG.SB.HBSCJSAVESBJS）保存环保税申报计算采集信息
        HXZGSB10813Request req = new HXZGSB10813Request();
        if (GyUtils.isNotNull(dto.getInsert().getSbjsDTO())) {
            SbjsDTO sbjsDTO = dto.getInsert().getSbjsDTO();
            com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO sbjsVO
                    = initSbjsVO(sbjsDTO, hbsjcxxuuid);
            req.setInsert(sbjsVO);
        }
        if (GyUtils.isNotNull(dto.getUpdate().getSbjsDTO())) {
            SbjsDTO sbjsDTO = dto.getUpdate().getSbjsDTO();
            com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO sbjsVO
                    = initSbjsVO(sbjsDTO, hbsjcxxuuid);
            req.setUpdate(sbjsVO);
        }
        if (GyUtils.isNotNull(dto.getDelete().getSbjsDTO())) {
            SbjsDTO sbjsDTO = dto.getDelete().getSbjsDTO();
            com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO sbjsVO
                    = initSbjsVO(sbjsDTO, hbsjcxxuuid);
            req.setDelete(sbjsVO);
        }
        CommonResult<Object> result = sjjhcl("CSSRPA0007",JsonUtils.toJson(req),"");
//        this.hxzgHbsService.saveSbjsVO(req.getInsert(),req.getUpdate(),req.getDelete());
//        Map<String, String> expend = new HashMap<>();
//        expend.put("sjjg", zgswskfjDm);
//        expend.put("sjry", SjryUtil.getSjry(zgswskfjDm));
//        try {
//            Gt3Invoker.invoke("SWZJ.HXZG.SB.HBSCJSAVESBJS", expend, req, HXZGSB10813Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.HBSCJSAVESBJS异常：", e);
//            map.put("code", e.getMessage());
//            return map;
//        }
        // 删掉暂存数据
        if (!GyUtils.isNull(ysquuid)) {
//            this.cancelZcData(ysquuid);
        }
        // 返回给卡片页面uuid
        if (GyUtils.isNotNull(skssqq) && GyUtils.isNotNull(skssqz)) {
            final HXZGSB10834Request hxzgsb10834Request = new HXZGSB10834Request();
            CxstysbNsrxx cxstysbNsrxx = new CxstysbNsrxx();
            cxstysbNsrxx.setDjxh(djxh);
            cxstysbNsrxx.setSkssqq(skssqq);
            cxstysbNsrxx.setSkssqz(skssqz);
            cxstysbNsrxx.setSbsxDm1("11");
            cxstysbNsrxx.setYzpzzlDm("BDA0611148");
            cxstysbNsrxx.setZgswskfjDm(zgswskfjDm);
            if ("cwgz".equals(sbbz)) {
                cxstysbNsrxx.setScenceCs("cwgzbz");
            }
            hxzgsb10834Request.setCxstysbnsrxx(cxstysbNsrxx);
            //查询环保税税源信息
            CommonResult<Object> result8 = sjjhcl("CSSRPA0008",JsonUtils.toJson(hxzgsb10834Request),"");
//            HXZGSB10834Response hxzgsb10834Response = Gt3Invoker.invoke("SWZJ.HXZG.SB.QUERYHBSSYXXFORSB",
//                    expend, hxzgsb10834Request, HXZGSB10834Response.class);
            if(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(result8.getCode())|| GyUtils.isNull(result8.getData())){
                throw ServiceExceptionUtil.exception(500, result.getMsg());
            }
            HXZGSB10834Response hxzgsb10834Response = JsonUtils.toBean((String) result8.getData(),HXZGSB10834Response.class) ;
            List<HbsCjSyxx> hbsCjSyxxGridLb = hxzgsb10834Response.getHbsCjSyxxLb().getHbsCjSyxxGridLb();
            // 按次只返回按次的，按期只返回按期的
            if (!GyUtils.isNull(hbsCjSyxxGridLb)) {
                if (skssqq.equals(skssqz)) {
                    for (HbsCjSyxx hbsCjSyxx : hbsCjSyxxGridLb) {
                        final String skssqq1 = hbsCjSyxx.getSkssqq().substring(0, 10);
                        final String skssqz1 = hbsCjSyxx.getSkssqz().substring(0, 10);
                        if (skssqq1.equals(skssqz1) && skssqq.equals(skssqq1) && skssqz.equals(skssqz1)) {
                            uuids.add(hbsCjSyxx.getUuid());
                        }
                    }
                } else {
                    for (HbsCjSyxx hbsCjSyxx : hbsCjSyxxGridLb) {
                        if (!hbsCjSyxx.getSkssqq().equals(hbsCjSyxx.getSkssqz())) {
                            uuids.add(hbsCjSyxx.getUuid());
                        }
                    }
                }
            }
            map.put("uuids", uuids);
        }
        map.put("code", "Y");
        return map;
    }

    /**
     * 删除暂存数据
     *
     * @param ysquuid
     */
    private void cancelZcData(String ysquuid) {
        ZnsbNssbZcxxDTO znsbNssbZcxxDTO = new ZnsbNssbZcxxDTO();
        znsbNssbZcxxDTO.setUuid(ysquuid);
        znsbNssbZcxxDTO.setYzpzzlDm(YzpzzlEnum.QHJTJCSSSJBS.getDm());
        znsbNssbZcxxDTO.setYxbz("N");
        znsbNssbZcxxService.saveOrUpdate(znsbNssbZcxxDTO);
    }

    /**
     * 初始化申报采集
     *
     * @param sbjsDTO
     * @return
     */
    private com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO initSbjsVO(
            SbjsDTO sbjsDTO, String hbsjcxxuuid) {
        com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO sbjsVO
                = new com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO();
        // 设置大气信息
        com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO.DqswrGrid dqswrGrid
                = new com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO.DqswrGrid();
        if (GyUtils.isNotNull(sbjsDTO.getDqswrList())) {
            final List<DqswrDTO> dqswrList = sbjsDTO.getDqswrList();
            for (DqswrDTO dqswrDTO : dqswrList) {
                dqswrDTO.setSbsxDm1("11");
                if (GyUtils.isNull(dqswrDTO.getWrdls())) {
                    dqswrDTO.setWrdls(0.0);
                }
                // 核心征管先判断减免税额后判断减免性质代码，存在未选择减免性质代码，减免税额为null的情况
                if (GyUtils.isNull(dqswrDTO.getJmse())) {
                    dqswrDTO.setJmse(0.0);
                }
                if (GyUtils.isNull(dqswrDTO.getYnse())) {
                    dqswrDTO.setYnse(0.0);
                }
            }
            dqswrGrid.getDqswrGridLb().addAll(BeanUtils.toBean(dqswrList,
                    com.css.znsb.nssb.pojo.vo.hbs.hxzg.DqsSbjscj.class));
        } else {
            // 防止核心征管报空
            dqswrGrid.getDqswrGridLb().addAll(new ArrayList<>());
        }
        sbjsVO.setDqswrGrid(dqswrGrid);
        // 设置固废信息
        com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO.GfGrid gfGrid
                = new com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO.GfGrid();
        if (GyUtils.isNotNull(sbjsDTO.getGfxxList())) {
            final List<GfsyxxDTO> gfxxList = sbjsDTO.getGfxxList();
            for (GfsyxxDTO gfsyxxDTO : gfxxList) {
                gfsyxxDTO.setSbsxDm1("11");
                if (GyUtils.isNull(gfsyxxDTO.getWrdls())) {
                    gfsyxxDTO.setWrdls(0.0);
                }
                if (GyUtils.isNull(gfsyxxDTO.getWrwpfl())) {
                    gfsyxxDTO.setWrwpfl(0.0);
                }
                // 核心征管先判断减免税额后判断减免性质代码，存在未选择减免性质代码，减免税额为null的情况
                if (GyUtils.isNull(gfsyxxDTO.getJmse())) {
                    gfsyxxDTO.setJmse(0.0);
                }
                if (GyUtils.isNull(gfsyxxDTO.getYnse())) {
                    gfsyxxDTO.setYnse(0.0);
                }
                if (GyUtils.isNull(gfsyxxDTO.getDqzhlyl())) {
                    gfsyxxDTO.setDqzhlyl(0.0);
                }
            }
            gfGrid.getGfGridLb().addAll(BeanUtils.toBean(gfxxList,com.css.znsb.nssb.pojo.vo.hbs.hxzg.GfSbjscj.class));
        } else {
            // 防止核心征管报空
            gfGrid.getGfGridLb().addAll(new ArrayList<>());
        }
        sbjsVO.setGfGrid(gfGrid);
        /// 设置噪声信息
        com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO.ZsGrid zsGrid
                = new com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO.ZsGrid();
        if (GyUtils.isNotNull(sbjsDTO.getZsxxList())) {
            final List<ZssyxxDTO> zsxxList = sbjsDTO.getZsxxList();
            for (ZssyxxDTO zssyxxDTO : zsxxList) {
                zssyxxDTO.setSbsxDm1("11");
                if (GyUtils.isNull(zssyxxDTO.getYnse())) {
                    zssyxxDTO.setYnse(0.0);
                }
            }
            zsGrid.getZsGridLb().addAll(BeanUtils.toBean(zsxxList,
                    com.css.znsb.nssb.pojo.vo.hbs.hxzg.ZsSbjscj.class));
        } else {
            // 防止核心征管报空
            zsGrid.getZsGridLb().addAll(new ArrayList<>());
        }
        sbjsVO.setZsGrid(zsGrid);
        // 设置抽样信息
        com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO.CycsGrid cycsGrid
                = new com.css.znsb.nssb.pojo.vo.hbs.hxzg.SbjsVO.CycsGrid();
        if (GyUtils.isNotNull(sbjsDTO.getCycssyxxList())) {
            final List<CycssyxxDTO> cycssyxxList = sbjsDTO.getCycssyxxList();
            for (CycssyxxDTO cycssyxxDTO : cycssyxxList) {
                // 按次申报时无税源uuid的情况
                if (GyUtils.isNull(cycssyxxDTO.getSyuuid())) {
                    cycssyxxDTO.setSyuuid(hbsjcxxuuid);
                }
                // 按次申报时无月份的情况
                if (GyUtils.isNull(cycssyxxDTO.getYf())
                        || cycssyxxDTO.getYf().isEmpty()) {
                    final Date date = CxsJmxzUtils.cast2Date(cycssyxxDTO.getSkssqq());
                    final int yf = HbssyxxcjUtils.getYfnum(date);
                    cycssyxxDTO.setYf(Integer.toString(yf));
                }
                // 按次申报时没有填写污染当量数，无jsyj防止核心报错
                cycssyxxDTO.setWrdls(GyUtils.isNotNull(cycssyxxDTO.getWrdls())
                        ? cycssyxxDTO.getWrdls() : 0.0);
                cycssyxxDTO.setSbsxDm1("11");
                if (GyUtils.isNull(cycssyxxDTO.getWrdls())) {
                    cycssyxxDTO.setWrdls(0.0);
                }
                // 核心征管先判断减免税额后判断减免性质代码，存在未选择减免性质代码，减免税额为null的情况
                if (GyUtils.isNull(cycssyxxDTO.getJmse())) {
                    cycssyxxDTO.setJmse(0.0);
                }
                if (GyUtils.isNull(cycssyxxDTO.getYnse())) {
                    cycssyxxDTO.setYnse(0.0);
                }
            }
            cycsGrid.getCycsGridLb().addAll(BeanUtils.toBean(cycssyxxList,
                    com.css.znsb.nssb.pojo.vo.hbs.hxzg.CycsSbjscj.class));
        } else {
            // 防止核心征管报空
            cycsGrid.getCycsGridLb().addAll(new ArrayList<>());
        }
        sbjsVO.setCycsGrid(cycsGrid);
        return sbjsVO;
    }

    /**
     * 查询已缴税款信息
     *
     * @param skssqq 税款所属期起
     * @param skssqz 税款所属期止
     * @param djxh   登记序号
     * @return
     */
    @Override
    public List<SBYjskxxDTO> queryYjxx(String skssqq, String skssqz, String djxh) {
        List<SBYjskxxDTO> sBYjskxxList = new ArrayList<>();
        final HXZGSB01401Request req = new HXZGSB01401Request();
        SBNsrxxVO sbNsrxxVO = new SBNsrxxVO();
        sbNsrxxVO.setDjxh(djxh);
        sbNsrxxVO.setSkssqq(skssqq);
        sbNsrxxVO.setSkssqz(skssqz);
        sbNsrxxVO.setSbsxDm1("11");
        req.setSbNsrxxVO(sbNsrxxVO);
        SBSbxxVO sbSbxxVO = new SBSbxxVO();
        sbSbxxVO.setZsxmDm(HBS_ZSXM_DM);
        sbSbxxVO.setSkssqq(skssqq);
        sbSbxxVO.setSkssqz(skssqz);
        sbSbxxVO.setDjxh(djxh);
        HXZGSB01401Request.SbxxVOList sbxxVOList = new HXZGSB01401Request.SbxxVOList();
        List<SBSbxxVO> sbSbxxVOlb = sbxxVOList.getSBSbxxVOlb();
        sbSbxxVOlb.add(sbSbxxVO);
        req.setSbxxVOList(sbxxVOList);
        Map<String, String> expend = new HashMap<>();
        DwnsrxxVO dwnsrxxVO = queryNsrxyxx(djxh);
        final String zgswskfjDm = dwnsrxxVO.getZgswskfjDm();
        expend.put("sjjg", zgswskfjDm);
        expend.put("sjry", SjryUtil.getSjry(zgswskfjDm));
        HXZGSB01401Response res = Gt3Invoker.invoke("SWZJ.HXZG.SB.QUERYJSKXX", expend, req, HXZGSB01401Response.class);
        List<SBYjskxxVO> sbYjskxxVOListlb = res.getSbYjskxxVOList().getSbYjskxxVOListlb();
        if (sbYjskxxVOListlb != null && !sbYjskxxVOListlb.isEmpty()) {
            sbYjskxxVOListlb.forEach(sbYjskxxVO -> {
                SBYjskxxDTO sbYjskxxDTO = BeanUtils.toBean(sbYjskxxVO, SBYjskxxDTO.class);
                sbYjskxxDTO.setYjze(GYCastUtils.cast2Str(sbYjskxxVO.getYjze()));
                sbYjskxxDTO.setYjye1(GYCastUtils.cast2Str(sbYjskxxVO.getYjye()));
                sBYjskxxList.add(sbYjskxxDTO);
            });
        }
        return sBYjskxxList;
    }

//    /**
//     * 获取查询外部城乡污水集中处理场所信息
//     *
//     * @return 城乡污水集中处理场所
//     */
//    @Override
    public String queryWbcxws(String djxh) {

        final DwnsrxxVO dwnsrxxVO = queryNsrxyxx(djxh);
        final String scjydzxzqhszDm = dwnsrxxVO.getScjydzxzqhszDm();
        String csxxbz = "N";
        // 调用税智撑JH_YYZCPT_ XDZSWJ _104接口，获取场所标志csxxbz
        String cxlx = "05";

        // YF ZSPMDM 当查询类型是01,02,03时，该字段为非空字段；
        // 当查询类型是04,05时，该字段可为空；
        Map<String, Object> map = new HashMap<>();
        CommonResult<Object> result = sjjhcl("CSSRPA0001","", "");
        map = JsonUtils.toMap((String)result.getData());
//        try {
//            map = hbssyxxcjFeignClients.cxgmhyzxx(djxh, scjydzxzqhszDm, "", "", cxlx)
//                    .getResponse().getData();
            log.info("调用RPA获取城乡污水集中处理场所结果" + map);
//        } catch (Exception e) {
//            log.error("调用税智撑（JH_YYZCPT_ XDZSWJ _104）查询规模化养殖信息异常：", e.getMessage());
//            return csxxbz;
//        }
        if (GyUtils.isNotNull(map) && GyUtils.isNotNull(map.get("csxxbz"))) {
            csxxbz = map.get("csxxbz").toString();
        }
        return csxxbz;
    }
    /**
     * 查询外部生活垃圾集中处理场所信息
     *
     * @return 生活垃圾集中处理场所
     */
    @Override
    public String queryWbShlj(String djxh) {
        final DwnsrxxVO dwnsrxxVO = queryNsrxyxx(djxh);
        final String scjydzxzqhszDm = dwnsrxxVO.getScjydzxzqhszDm();
        String csxxbz = "N";
        // 调用税智撑JH_YYZCPT_ XDZSWJ _104接口，获取场所标志csxxbz
        String cxlx = "04";
        // YF ZSPMDM 当查询类型是01,02,03时，该字段为非空字段；
        // 当查询类型是04,05时，该字段可为空；
        Map<String, Object> map = new HashMap<>();
        CommonResult<Object> result = sjjhcl("CSSRPA0002","", "");
        map = JsonUtils.toMap((String)result.getData());
        log.info("调用RPA获取生活垃圾集中处理场所结果" + map);
//        try {
//            map = hbssyxxcjFeignClients.cxgmhyzxx(djxh, scjydzxzqhszDm, "", "", cxlx)
//                    .getResponse().getData();
//            log.info("调用税智撑（JH_YYZCPT_ XDZSWJ _104）结果" + map);
//        } catch (Exception e) {
//            log.error("调用税智撑（JH_YYZCPT_ XDZSWJ _104）查询规模化养殖信息异常：", e.getMessage());
//            return csxxbz;
//        }
        if (GyUtils.isNotNull(map) && GyUtils.isNotNull(map.get("csxxbz"))) {
            csxxbz = map.get("csxxbz").toString();
        }
        return csxxbz;
    }

    /**
     * 税源基础登记信息中更正污染物排放量计算方法获取标志
     *
     * @param
     * @return 是否更正污染物排放量计算方法
     */
    @Override
    public String gzSyxxJsff(Map<String, Object> map) {
        String reStr = "";
        if (GyUtils.isNull(map.get("syuuid"))) {
            return reStr;
        }
        final QuerySbbzVo querySbbzVo = new QuerySbbzVo();
        final List<String> dqsSyuuidList = new ArrayList<>();
        final List<String> gfSyuuidList = new ArrayList<>();
        final List<String> zsSyuuidList = new ArrayList<>();
        final List<String> cycsSyuuidList = new ArrayList<>();
        dqsSyuuidList.add(GYCastUtils.cast2Str(map.get("syuuid")));
        gfSyuuidList.add(GYCastUtils.cast2Str(map.get("syuuid")));
        zsSyuuidList.add(GYCastUtils.cast2Str(map.get("syuuid")));
        cycsSyuuidList.add(GYCastUtils.cast2Str(map.get("syuuid")));
        querySbbzVo.setDqsSyuuidList(dqsSyuuidList);
        querySbbzVo.setGfSyuuidList(gfSyuuidList);
        querySbbzVo.setZsSyuuidList(zsSyuuidList);
        querySbbzVo.setCycsSyuuidList(cycsSyuuidList);
        final ReStrVO reStrVO = hbsQuerySbbzService.querySbbzBySyuuid(querySbbzVo);
        if (!GyUtils.isNull(reStrVO)) {
            if (!GyUtils.isNull(reStrVO.getDqsReStr())) {
                reStr = "sbjs";
                // 判断是否存在已申报
                for (DqsReStrVO dqsReStrVO : reStrVO.getDqsReStr()) {
                    if (GyUtils.isNotNull(dqsReStrVO.getCxstysbuuid())) {
                        reStr = "sbjs,sbbz";
                    }
                }
            }
            if (!GyUtils.isNull(reStrVO.getGfReStr())) {
                reStr = "sbjs";
                // 判断是否存在已申报
                for (GfReStrVO gfReStrVO : reStrVO.getGfReStr()) {
                    if (GyUtils.isNotNull(gfReStrVO.getCxstysbuuid())) {
                        reStr = "sbjs,sbbz";
                    }
                }
            }
            if (!GyUtils.isNull(reStrVO.getZsReStr())) {
                reStr = "sbjs";
                // 判断是否存在已申报
                for (ZsReStrVO zsReStrVO : reStrVO.getZsReStr()) {
                    if (GyUtils.isNotNull(zsReStrVO.getCxstysbuuid())) {
                        reStr = "sbjs,sbbz";
                    }
                }
            }
            if (!GyUtils.isNull(reStrVO.getCycsReStr())) {
                reStr = "sbjs";
                // 判断是否存在已申报
                for (CycsReStrVO cycsReStrVO : reStrVO.getCycsReStr()) {
                    if (GyUtils.isNotNull(cycsReStrVO.getCxstysbuuid())) {
                        reStr = "sbjs,sbbz";
                    }
                }
            }
        }
        return reStr;
    }


    /**
     * 税智撑排放接口信息匹配时获取排放口匹配信息
     *
     * @param djxh 登记序号
     * @return 匹配明细VO
     */
    @Override
    @Transactional
    public WbjhSyppMxVO getWbjhSyppMx(String djxh, String swjgdm) {
        final WbjhSyppMxVO resVO = new WbjhSyppMxVO();
        final List<PpxxVO> yppxxVOList = new ArrayList<>();
        final List<PpxxVO> wppxxVOList = new ArrayList<>();
        // 查询系统参数“DZSWJ00SBZX020065”，判断该省是否启用税源匹配
        final String openSyppVue = CxsGyUtils.getXtcs(swjgdm, "DZSWJ00SBZX020065", "N");
        if ("N".equals(openSyppVue)) {
            resVO.setReturnCode(ERROR_CODE);
            resVO.setReturnMsg("未启用税源匹配功能！");
            return resVO;
        }
//        Map<String, Object> skssq = getSkssq();
//        // 调用税智撑（JH_YYZCPT_ XDZSWJ _107），获取纳税人外部环保新增税源数据信息
//        Map<String, Object> wbjhSyppMx = getWbjh(djxh, skssq);
//        // 查询新电局已匹配过的信息
//        final List<HbsjcxxcjPpxxjgbDO> ppxxjgbDOList = hbsjcxxcjPpxxjgbMapper.selectPpxxByDjxh(djxh);
//        final List<HbsjcxxcjPpxxwrwmxbDO> hbsjcxxcjPpxxwrwmxbDOList = hbsjcxxcjPpxxwrwmxbMapper.selectPpxxwrwmxByDjxh(djxh);
//        if (GyUtils.isNull(wbjhSyppMx) && GyUtils.isNull(ppxxjgbDOList)) {
//            resVO.setReturnCode(FAIL_CODE);
//            resVO.setReturnMsg("未获取到有效的外部税源数据！");
//            return resVO;
//        }
//        // 排污许可证信息
//        final List<PpxxVO> wbjhPpxxVOList = new ArrayList<>();
//        final List<PpxxVO> wbjhPwxkzppxxVOList = new ArrayList<>();
//        final List<PpxxVO> wbjhJgjcppxxVOList = new ArrayList<>();
//        final List<PpxxVO> wbjhZdjcppxxVOList = new ArrayList<>();
//        if (GyUtils.isNotNull(wbjhSyppMx.get("pwmxList"))) {
//            for (Map<String, Object> map : (List<Map<String, Object>>) wbjhSyppMx.get("pwmxList")) {
//                PpxxVO ppxxVO = new PpxxVO();
//                try {
//                    ppxxVO = CxsGyUtils.mapToBean(map, PpxxVO.class);
//                    ppxxVO.setPwxkzpfkbh(ppxxVO.getPfkbh());
//                } catch (Exception e) {
//                    log.error("外部数据类转换异常" + e.getMessage());
//                    continue;
//                }
//                // 过滤预填信息，排除掉pwxkzbh、pfkbh 为空的情况
//                if (!GyUtils.isNull(ppxxVO.getPwxkzbh()) && !GyUtils.isNull(ppxxVO.getPfkbh())) {
//                    wbjhPwxkzppxxVOList.add(ppxxVO);
//                }
//            }
//        }
//        // 自动监测信息
//        if (GyUtils.isNotNull(wbjhSyppMx.get("zdjcpfkList"))) {
//            for (Map<String, Object> map : (List<Map<String, Object>>) wbjhSyppMx.get("zdjcpfkList")) {
//                PpxxVO ppxxVO = new PpxxVO();
//                try {
//                    ppxxVO = CxsGyUtils.mapToBean(map, PpxxVO.class);
//                } catch (Exception e) {
//                    log.error("外部数据类转换异常" + e.getMessage());
//                    continue;
//                }
//                wbjhZdjcppxxVOList.add(ppxxVO);
//            }
//        }
//
//        // 监测机构监测信息
//        if (GyUtils.isNotNull(wbjhSyppMx.get("jgjcpfkList"))) {
//            for (Map<String, Object> map : (List<Map<String, Object>>) wbjhSyppMx.get("jgjcpfkList")) {
//                PpxxVO ppxxVO = new PpxxVO();
//                try {
//                    ppxxVO = CxsGyUtils.mapToBean(map, PpxxVO.class);
//                } catch (Exception e) {
//                    log.error("外部数据类转换异常" + e.getMessage());
//                    continue;
//                }
//                wbjhJgjcppxxVOList.add(ppxxVO);
//            }
//        }
//        // 把自动监测和监测机构监测中相同排放口编号的信息合并到排污许可证信息里面
//        if (!GyUtils.isNull(wbjhPwxkzppxxVOList)) {
//            for (PpxxVO ppxxVO : wbjhPwxkzppxxVOList) {
//                if (!GyUtils.isNull(wbjhJgjcppxxVOList)) {
//                    for (int i = wbjhJgjcppxxVOList.size() - 1; i >= 0; i--) {
//                        final PpxxVO jgjcPpxxVO = wbjhJgjcppxxVOList.get(i);
//                        if (ppxxVO.getPwxkzpfkbh().equals(jgjcPpxxVO.getJgjcpfkbh())) {
//                            ppxxVO.setJgjcpfkbh(jgjcPpxxVO.getJgjcpfkbh());
//                            ppxxVO.setJgjcpfkmc(jgjcPpxxVO.getJgjcpfkmc());
//                            wbjhJgjcppxxVOList.remove(i);
//                        }
//                    }
//                }
//                if (!GyUtils.isNull(wbjhZdjcppxxVOList)) {
//                    for (int i = wbjhZdjcppxxVOList.size() - 1; i >= 0; i--) {
//                        final PpxxVO zdjcPpxxVO = wbjhZdjcppxxVOList.get(i);
//                        if (ppxxVO.getPwxkzpfkbh().equals(zdjcPpxxVO.getZdjcpfkbh())) {
//                            ppxxVO.setZdjcpfkbh(zdjcPpxxVO.getZdjcpfkbh());
//                            ppxxVO.setZdjcpfkmc(zdjcPpxxVO.getZdjcpfkmc());
//                            wbjhZdjcppxxVOList.remove(i);
//                        }
//                    }
//                }
//                wbjhPpxxVOList.add(ppxxVO);
//            }
//        }
//        if (!GyUtils.isNull(wbjhJgjcppxxVOList)) {
//            wbjhPpxxVOList.addAll(wbjhJgjcppxxVOList);
//        }
//        if (!GyUtils.isNull(wbjhZdjcppxxVOList)) {
//            wbjhPpxxVOList.addAll(wbjhZdjcppxxVOList);
//        }
//        if (GyUtils.isNotNull(wbjhSyppMx.get("pfklbmxList"))) {
//            final List<PpmxVO> ppmxVOList = BeanUtils.MapsToBeans((List<Map>) wbjhSyppMx.get("pfklbmxList"),
//                    PpmxVO.class);
//            if (GyUtils.isNotNull(wbjhPpxxVOList)) {
//                for (PpxxVO ppxxVO : wbjhPpxxVOList) {
//                    final List<PpmxVO> ppmxDTOListNew = new ArrayList<>();
//                    for (PpmxVO ppmxDTO : ppmxVOList) {
//                        if (!GyUtils.isNull(ppmxDTO.getZspmDm())) {
//                            // 排除zspmDm错误的数据
//                            if (ppmxDTO.getZspmDm().startsWith("10121") && ppxxVO.getUuid().equals(ppmxDTO.getUuid())) {
//                                ppmxDTOListNew.add(ppmxDTO);
//                            }
//                        }
//                    }
//                    ppxxVO.setPpmxVOList(ppmxDTOListNew);
//                }
//            }
//        }
//
//        // 新电局已经匹配过排放口
//        if (!GyUtils.isNull(ppxxjgbDOList)) {
//            for (HbsjcxxcjPpxxjgbDO hbsjcxxcjPpxxjgbDO : ppxxjgbDOList) {
//                final PpxxVO ppxxVO = BeanUtils.toBean(hbsjcxxcjPpxxjgbDO, PpxxVO.class);
//                ppxxVO.setUuid(hbsjcxxcjPpxxjgbDO.getPpxxuuid());
//                ppxxVO.setPpzt(hbsjcxxcjPpxxjgbDO.getPpztbz());
//                final List<HbsjcxxcjPpxxwrwmxbDO> ppxxwrwmxbDOList = hbsjcxxcjPpxxwrwmxbDOList.stream().filter(a -> hbsjcxxcjPpxxjgbDO.getPpxxuuid().equals(a.getPpxxuuid())).collect(Collectors.toList());
//                if (!GyUtils.isNull(ppxxwrwmxbDOList)) {
//                    final List<PpmxVO> ppmxVOList = new ArrayList<>();
//                    for (HbsjcxxcjPpxxwrwmxbDO hbsjcxxcjPpxxwrwmxbDO : ppxxwrwmxbDOList) {
//                        final PpmxVO ppmxVO = new PpmxVO();
//                        ppmxVO.setPpzt("Y");
//                        ppmxVO.setZspmDm(hbsjcxxcjPpxxwrwmxbDO.getZspmDm());
//                        ppmxVO.setUuid(hbsjcxxcjPpxxwrwmxbDO.getPpxxuuid());
//                        ppmxVO.setPfklb(hbsjcxxcjPpxxwrwmxbDO.getPfklbDm());
//                        ppmxVOList.add(ppmxVO);
//                    }
//                    ppxxVO.setPpmxVOList(ppmxVOList);
//                }
//                // 有外部数据
//                if (!GyUtils.isNull(wbjhPpxxVOList)) {
//                    final String pwxkzbh = hbsjcxxcjPpxxjgbDO.getPwxkzbh();
//                    final String pwxkzpfkbh = hbsjcxxcjPpxxjgbDO.getPwxkzpfkbh();
//                    final String jgjcpfkbh = hbsjcxxcjPpxxjgbDO.getJgjcpfkbh();
//                    final String zdjcpfkbh = hbsjcxxcjPpxxjgbDO.getZdjcpfkbh();
//                    for (int i = wbjhPpxxVOList.size() - 1; i >= 0; i--) {
//                        final PpxxVO wbjhPpxxVO = wbjhPpxxVOList.get(i);
//                        // 组装外部数据和已匹配的数据，并判断是否有新增污染物
//                        if (pwxkzbh.equals(wbjhPpxxVO.getPwxkzbh())
//                                && ((!GyUtils.isNull(pwxkzpfkbh) && pwxkzpfkbh.equals(wbjhPpxxVO.getPwxkzpfkbh()))
//                                || (!GyUtils.isNull(jgjcpfkbh) && jgjcpfkbh.equals(wbjhPpxxVO.getJgjcpfkbh()))
//                                || (!GyUtils.isNull(zdjcpfkbh) && zdjcpfkbh.equals(wbjhPpxxVO.getZdjcpfkbh())))) {
//                            if (!GyUtils.isNull(wbjhPpxxVO.getPpmxVOList())) {
//                                if (!GyUtils.isNull(ppxxVO.getPpmxVOList())) {
//                                    final List<String> yppMxList = new ArrayList<>();
//                                    final List<PpmxVO> ppmxVOList = ppxxVO.getPpmxVOList();
//                                    ppmxVOList.forEach(a -> yppMxList.add(a.getZspmDm()));
//                                    for (PpmxVO wbjhppmxVO : wbjhPpxxVO.getPpmxVOList()) {
//                                        final String zspmDm = wbjhppmxVO.getZspmDm();
//                                        if (!yppMxList.contains(zspmDm)) {
//                                            wbjhppmxVO.setPpzt("N");
//                                            ppmxVOList.add(wbjhppmxVO);
//                                        }
//                                    }
//                                    if (yppMxList.size() == ppmxVOList.size()) {
//                                        ppxxVO.setSfxz("N");
//                                    } else {
//                                        ppxxVO.setSfxz("Y");
//                                    }
//                                } else {
//                                    final List<PpmxVO> ppmxVOList = new ArrayList<>();
//                                    ppxxVO.setSfxz("Y");
//                                    for (PpmxVO wbjhppmxVO : wbjhPpxxVO.getPpmxVOList()) {
//                                        wbjhppmxVO.setPpzt("N");
//                                        ppmxVOList.add(wbjhppmxVO);
//                                    }
//                                    ppxxVO.setPpmxVOList(ppmxVOList);
//                                }
//                            }
//                            wbjhPpxxVOList.remove(i);
//                            continue;
//                        }
//                    }
//                }
//                if (!GyUtils.isNull(ppxxVO.getPpmxVOList())) {
//                    final List<PpmxVO> ppmxVOList = ppxxVO.getPpmxVOList();
//                    Integer xh = 1;
//                    for (PpmxVO ppmxVO : ppmxVOList) {
//                        final String zspmDm = ppmxVO.getZspmDm();
//                        // 匹配品目名称
//                        ppmxVO.setZspmDmMc(FtsUtils.getMcByDm("DM_GY_ZSPM", "ZSPMMC", zspmDm));
//                        if (zspmDm.startsWith("101212")) {//水污染物
//                            ppmxVO.setFzspmDm(FtsUtils.getMcByDm("DM_GY_ZSPM", "SJZSPM_DM", zspmDm));
//                        }
//                        ppmxVO.setXh(xh.toString());
//                        xh++;
//                    }
//                }
//                yppxxVOList.add(ppxxVO);
//            }
//        }
//        resVO.setYppxxVOList(yppxxVOList);
//        if (!GyUtils.isNull(wbjhPpxxVOList)) {
//            wppxxVOList.addAll(wbjhPpxxVOList);
//            for (PpxxVO ppxxVO : wppxxVOList) {
//                ppxxVO.setPpzt("N");
//                if (!GyUtils.isNull(ppxxVO.getPpmxVOList())) {
//                    Integer xh = 1;
//                    for (PpmxVO ppmxVO : ppxxVO.getPpmxVOList()) {
//                        final String zspmDm = ppmxVO.getZspmDm();
//                        // 匹配品目名称
//                        ppmxVO.setZspmDmMc(FtsUtils.getMcByDm("DM_GY_ZSPM", "ZSPMMC", zspmDm));
//                        if (zspmDm.startsWith("101212")) {//水污染物
//                            ppmxVO.setFzspmDm(FtsUtils.getMcByDm("DM_GY_ZSPM", "SJZSPM_DM", zspmDm));
//                        }
//                        ppmxVO.setXh(xh.toString());
//                        ppmxVO.setPpzt("N");
//                        xh++;
//                    }
//                }
//            }
//        }
//        resVO.setWppxxVOList(wppxxVOList);
//        resVO.setReturnCode(SUCCESS_CODE);
        return resVO;
    }

    private Map<String, Object> getWbjh(String djxh, Map<String, Object> skssq) {
        Map<String, Object> wbjhMx = new HashMap<>();
//        try {
//            wbjhMx = hbssyxxcjFeignClients.getWbjhSyxzxx(djxh,
//                    GYCastUtils.cast2Str(skssq.get("skssqq")), GYCastUtils.cast2Str(skssq.get("skssqz")))
//                    .getResponse().getData();
//            log.info("调用税智撑（JH_YYZCPT_ XDZSWJ _107）结果" + wbjhMx);
//        } catch (Exception e) {
//            log.error("调用税智撑（JH_YYZCPT_ XDZSWJ _107）获取税源预填信息异常：", e.getMessage());
//            return wbjhMx;
//        }
        // 外部数据测试挡板
        /*final List<Map<String, Object>> pwmxList = new ArrayList<>();
        final List<Map<String, Object>> zdjcmxList = new ArrayList<>();
        final List<Map<String, Object>> jgjcmxList = new ArrayList<>();
        final Map<String, Object> pwmx = new HashMap<>();
        pwmx.put("uuid", "b938446c19094689bbaecea02d5d04d5");
        pwmx.put("pwxkzbh", "无排污许可证排口");
        pwmx.put("jd2", 104.717000);
        pwmx.put("wd", 31.542000);
        pwmx.put("syyxqq", "2023-01-01");
        pwmx.put("syyxqz", "2024-07-31");
        pwmx.put("pwxkzxxuuid", "fb015d13960efeba1d24a931e1609330");
        pwmx.put("lrrq", "2020-07-10");
        pwmx.put("pfkxxuuid", "cbdf182b8bbc4a7d990d5cc563b978a2");
        pwmx.put("pfkbh", "DW001");
        pwmx.put("pwxkzpfkmc", "自动监测1");
        pwmx.put("pfkwybm", "DW001");
        pwmx.put("zywrwlbDm", "A");
        pwmx.put("sqlx", "1");
        pwmxList.add(pwmx);

        final Map<String, Object> pwmx1 = new HashMap<>();
        pwmx1.put("uuid", "b938446c19094689bbaecea02d5d04d6");
        pwmx1.put("pwxkzbh", "无排污许可证排口");
        pwmx1.put("jdxzDm", "440605100");
        pwmx1.put("jd2", 104.717000);
        pwmx1.put("wd", 31.542000);
        pwmx1.put("syyxqq", "2023-01-01");
        pwmx1.put("syyxqz", "2023-07-31");
        pwmx1.put("pfkbh", "DW002");
        pwmx1.put("pwxkzpfkmc", "机构监测1");
        pwmx1.put("pfkwybm", "DW002");
        pwmx1.put("zywrwlbDm", "A");
        pwmx1.put("sqlx", "1");
        pwmx1.put("ppzt", "N");
        pwmxList.add(pwmx1);


        final Map<String, Object> pwmx2 = new HashMap<>();
        pwmx2.put("uuid", "b938446c19094689bbaecea02d5d04d7");
        pwmx2.put("pwxkzbh", "无排污许可证排口");
        pwmx2.put("jdxzDm", "440605100");
        pwmx2.put("jd2", 104.717000);
        pwmx2.put("wd", 31.542000);
        pwmx2.put("syyxqq", "2023-01-01");
        pwmx2.put("syyxqz", "2023-12-31");
        pwmx2.put("pfkbh", "DW003");
        pwmx2.put("pwxkzpfkmc", "自动监测2");
        pwmx2.put("pfkwybm", "DW003");
        pwmx2.put("zywrwlbDm", "W");
        pwmx2.put("sqlx", "1");
        pwmx2.put("ppzt", "N");
        pwmxList.add(pwmx2);
        wbjhMx.put("pwmxList", pwmxList);

        final Map<String, Object> pwmx3 = new HashMap<>();
        pwmx3.put("uuid", "b938446c19094689bbaecea02d5d04d8");
        pwmx3.put("zdjcpfkbh", "DW001");
        pwmx3.put("zdjcpfkmc", "自动监测1");
        zdjcmxList.add(pwmx3);
        wbjhMx.put("zdjcpfkList", zdjcmxList);

        final Map<String, Object> pwmx4 = new HashMap<>();
        pwmx4.put("uuid", "b938446c19094689bbaecea02d5d04d9");
        pwmx4.put("jgjcpfkbh", "FQ001");
        pwmx4.put("jgjcpfkmc", "机构监测2");
        jgjcmxList.add(pwmx4);

        final Map<String, Object> pwmx5 = new HashMap<>();
        pwmx5.put("uuid", "b938446c19094689bbaecea02d5d04d12");
        pwmx5.put("jgjcpfkbh", "DW002");
        pwmx5.put("jgjcpfkmc", "机构监测1");
        jgjcmxList.add(pwmx5);
        wbjhMx.put("jgjcpfkList", jgjcmxList);


        final List<Map<String, Object>> pfklbmxList = new ArrayList<>();
        final Map<String, Object> pfklbmx = new HashMap<>();
        pfklbmx.put("uuid", "b938446c19094689bbaecea02d5d04d5");
        pfklbmx.put("xh", 1);
        pfklbmx.put("pfklb", "01");
        pfklbmx.put("zspmDm", "101211107");
        pfklbmx.put("wrwpfljsffDm", "1");
        pfklbmx.put("zxbz1", "电镀污染物排放标准GB 21900-2008");
        pfklbmx.put("bzndz", "30");
        pfklbmxList.add(pfklbmx);

        final Map<String, Object> pfklbmx1 = new HashMap<>();
        pfklbmx1.put("uuid", "b938446c19094689bbaecea02d5d04d6");
        pfklbmx1.put("xh", 1);
        pfklbmx1.put("pfklb", "02");
        pfklbmx1.put("zspmDm", "101211103");
        pfklbmxList.add(pfklbmx1);

        final Map<String, Object> pfklbmx2 = new HashMap<>();
        pfklbmx2.put("uuid", "b938446c19094689bbaecea02d5d04d7");
        pfklbmx2.put("xh", 1);
        pfklbmx2.put("pfklb", "02");
        pfklbmx2.put("zspmDm", "101212101");
        pfklbmxList.add(pfklbmx2);

        final Map<String, Object> pfklbmx3 = new HashMap<>();
        pfklbmx3.put("uuid", "b938446c19094689bbaecea02d5d04d7");
        pfklbmx3.put("xh", 2);
        pfklbmx3.put("pfklb", "02");
        pfklbmx3.put("zspmDm", "101212104");
        pfklbmxList.add(pfklbmx3);

        final Map<String, Object> pfklbmx4 = new HashMap<>();
        pfklbmx4.put("uuid", "b938446c19094689bbaecea02d5d04d7");
        pfklbmx4.put("xh", 3);
        pfklbmx4.put("pfklb", "02");
        pfklbmx4.put("zspmDm", "101212108");
        pfklbmxList.add(pfklbmx4);

        wbjhMx.put("pfklbmxList", pfklbmxList);*/
        return wbjhMx;
    }
//
//
//    /**
//     * 通过税智撑获取辅助计算申报预填信息
//     *
//     * @param getWbjhFzsbjsxxDTO
//     * @return
//     */
//    @Override
//    public SbjsDTO getWbjhFzsbjsxx(GetWbjhFzsbjsxxDTO getWbjhFzsbjsxxDTO) {
//        final SbjsDTO sbjsDTO = getWbjhFzsbjsxxDTO.getSbjsDTO();
//        final String djxh = getWbjhFzsbjsxxDTO.getDjxh();
//        final String skssqq = getWbjhFzsbjsxxDTO.getSkssqq();
//        final String skssqz = getWbjhFzsbjsxxDTO.getSkssqz();
//        //  调用税智撑（JH_YYZCPT_XDZSWJ_109），排放接口信息匹配时获取排放口匹配信息
//        List<WbjhFzsbjsxxDTO> wbjhFzsbjsxxList = new ArrayList<>();
//        HbsyReqVo hbsyReqVo = new HbsyReqVo(djxh, skssqq, skssqz);
//        List<Map<String, Object>> wbjhFzsbjsxx = new ArrayList<>();
//        try {
//            wbjhFzsbjsxx = hbssyxxcjFeignClients.getWbjhFzsbjsxx(hbsyReqVo).getResponse().getData();
//            log.info("税智撑(JH_YYZCPT_XDZSWJ_109)" + "请求参数：" + hbsyReqVo + ",返回结果：" + wbjhFzsbjsxx);
//        } catch (Exception e) {
//            log.info("税智撑(JH_YYZCPT_XDZSWJ_109)" + e.getMessage());
//            sbjsDTO.setReturnMsg("未查询到外部匹配数据！");
//            return sbjsDTO;
//        }
//        /*final HashMap<String, Object> map1 = new HashMap<>();
//        map1.put("pfkbh", "DW001");
//        map1.put("zspmDm", "101212101");
//        map1.put("yf", "10");
//        map1.put("wrwpfljsffDm", "1");
//        map1.put("zywrwlbDm", "W");
//        map1.put("hgbhssybh", "W440605202300042");
//        map1.put("scldz", 200);
//        wbjhFzsbjsxx.add(map1);
//
//        final HashMap<String, Object> map2 = new HashMap<>();
//        map2.put("pfkbh", "DW001");
//        map2.put("zspmDm", "101212101");
//        map2.put("yf", "11");
//        map2.put("wrwpfljsffDm", "1");
//        map2.put("zywrwlbDm", "W");
//        map2.put("hgbhssybh", "W440605202300042");
//        map2.put("pfl", 20000);
//        map2.put("scldz", 300);
//        wbjhFzsbjsxx.add(map2);
//
//        final HashMap<String, Object> map3 = new HashMap<>();
//        map3.put("pfkbh", "DW001");
//        map3.put("zspmDm", "101212101");
//        map3.put("yf", "12");
//        map3.put("wrwpfljsffDm", "1");
//        map3.put("zywrwlbDm", "W");
//        map3.put("hgbhssybh", "W440605202300042");
//        map3.put("pfl", 30000);
//        map3.put("scldz", 400);
//        wbjhFzsbjsxx.add(map3);*/
//
//        if (GyUtils.isNotNull(wbjhFzsbjsxx)) {
//            wbjhFzsbjsxx.forEach(map -> {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.putAll(map);
//                WbjhFzsbjsxxDTO wbjhFzsbjsxxDTO = jsonObject.toJavaObject(WbjhFzsbjsxxDTO.class);
//                wbjhFzsbjsxxList.add(wbjhFzsbjsxxDTO);
//            });
//        } else {
//            sbjsDTO.setReturnMsg("未查询到外部匹配数据！");
//            return sbjsDTO;
//        }
//        final List<HbsjcxxcjPpxxjgbDO> hbsjcxxcjPpxxjgbDOList = hbsjcxxcjPpxxjgbMapper.selectPpxxByDjxh(djxh);
//        if (GyUtils.isNull(hbsjcxxcjPpxxjgbDOList)) {
//            sbjsDTO.setReturnMsg("未做税源匹配，请前往税源采集页面进行税源匹配！");
//            return sbjsDTO;
//        }
//        final List<HbsjcxxcjPpxxwrwmxbDO> hbsjcxxcjPpxxwrwmxbDOList = hbsjcxxcjPpxxwrwmxbMapper.selectPpxxwrwmxByDjxh(djxh);
//        if (!GyUtils.isNull(sbjsDTO.getDqswrList())) {
//            final List<DqswrDTO> dqswrList = sbjsDTO.getDqswrList();
//            for (HbsjcxxcjPpxxjgbDO hbsjcxxcjPpxxjgbDO : hbsjcxxcjPpxxjgbDOList) {
//                // 匹配成功就预填数据,只匹配大气水自动监测和监测机构监测
//                final String ppztbz = hbsjcxxcjPpxxjgbDO.getPpztbz();
//                if ("Y".equals(ppztbz)) {
//                    final String ppxxuuid = hbsjcxxcjPpxxjgbDO.getPpxxuuid();
//                    final String pwxkzpfkbh = hbsjcxxcjPpxxjgbDO.getPwxkzpfkbh();
//                    final String ycjpfkbh = hbsjcxxcjPpxxjgbDO.getYcjpfkbh();
//                    final List<HbsjcxxcjPpxxwrwmxbDO> ppxxwrwmxbDOList = hbsjcxxcjPpxxwrwmxbDOList.stream().filter(a -> ppxxuuid.equals(a.getPpxxuuid())).collect(Collectors.toList());
//                    if (!GyUtils.isNull(ppxxwrwmxbDOList)) {
//                        final List<String> yppZspmList = new ArrayList<>();
//                        ppxxwrwmxbDOList.forEach(a -> yppZspmList.add(a.getZspmDm()));
//                        // 找到需要匹配的税源
//                        for (DqswrDTO dqswrDTO : dqswrList) {
//                            // 第一次进入的时候预填数据，防止覆盖纳税人修改后的数据
//                            if (!GyUtils.isNull(dqswrDTO.getUuid())) {
//                                continue;
//                            }
//                            // 找到已匹配的品目，江苏青岛传的排放口编号为税源编号
//                            if (yppZspmList.contains(dqswrDTO.getZspmDm()) && (pwxkzpfkbh.equals(dqswrDTO.getPfkbh()) || pwxkzpfkbh.equals(dqswrDTO.getHgbhssybh()) || ycjpfkbh.equals(dqswrDTO.getPfkbh()))) {
//                                for (WbjhFzsbjsxxDTO wbjhFzsbjsxxDTO : wbjhFzsbjsxxList) {
//                                    // 没匹配过的品目不预填
//                                    if (!yppZspmList.contains(wbjhFzsbjsxxDTO.getZspmDm())) {
//                                        continue;
//                                    }
//                                    // 税源编号
//                                    final String hgbhssybh = wbjhFzsbjsxxDTO.getHgbhssybh();
//                                    // 污染物类别
//                                    final String zywrwlbDm = wbjhFzsbjsxxDTO.getZywrwlbDm();
//                                    // 月份
//                                    final String yf = wbjhFzsbjsxxDTO.getYf();
//                                    // 污染物名称
//                                    final String zspmDm = wbjhFzsbjsxxDTO.getZspmDm();
//                                    // 污染物排放量计算方法
//                                    final String wrwpfljsffDm = wbjhFzsbjsxxDTO.getWrwpfljsffDm();
//                                    // 大气水
//                                    if (("W".equals(zywrwlbDm) || "A".equals(zywrwlbDm))
//                                            && ("1".equals(wrwpfljsffDm) || "2".equals(wrwpfljsffDm))) {
//                                        if (dqswrDTO.getHgbhssybh().equals(hgbhssybh)
//                                                && dqswrDTO.getWrwpfljsffDm().equals(wrwpfljsffDm)
//                                                && dqswrDTO.getYf().trim().equals(yf.trim())
//                                                && dqswrDTO.getZspmDm().equals(zspmDm)) {
//                                            dqswrDTO.setPfl(!GyUtils.isNull(wbjhFzsbjsxxDTO.getPfl())
//                                                    ? wbjhFzsbjsxxDTO.getPfl() : 0);
//                                            dqswrDTO.setScldz(!GyUtils.isNull(wbjhFzsbjsxxDTO.getScldz())
//                                                    ? wbjhFzsbjsxxDTO.getScldz() : 0);
//                                            dqswrDTO.setZgscldz(!GyUtils.isNull(wbjhFzsbjsxxDTO.getZgscldz())
//                                                    ? wbjhFzsbjsxxDTO.getZgscldz() : 0);
//                                            dqswrDTO.setDyyxyjld(!GyUtils.isNull(wbjhFzsbjsxxDTO.getDyyxyjld())
//                                                    ? wbjhFzsbjsxxDTO.getDyyxyjld() : 0);
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        sbjsDTO.setReturnCode(SUCCESS_CODE);
//        sbjsDTO.setReturnMsg("匹配成功！");
//        return sbjsDTO;
//    }
//
//    /**
//     * 更新税源信息采集表中的排放口匹配字段信息
//     *
//     * @param updatePpxxVO 匹配明细
//     * @return 更新成功标志
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Map<String, Object> updatePpxx(UpdatePpxxVO updatePpxxVO) {
//        HashMap<String, Object> returnMap = new HashMap<>();
//        //构造必要字段
//        final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        final LocalDateTime now = LocalDateTime.parse(df.format(LocalDateTime.now()), df);
//        final String ywqdDm = updatePpxxVO.getYwqddm();
//        final String sjcsdq = updatePpxxVO.getSjswjgdm();
//        final String sjgsdq = updatePpxxVO.getSjswjgdm();
//        final String lrrsfid = updatePpxxVO.getSjry();
//        final List<PpxxVO> ppxxVOList = updatePpxxVO.getPpxxVOList();
//        if (!GyUtils.isNull(ppxxVOList)) {
//            final String djxh = ppxxVOList.get(0).getDjxh();
//            for (PpxxVO ppxxVO : ppxxVOList) {
//                final String uuid = ppxxVO.getUuid();
//                final HbsjcxxcjPpxxjgbDO hbsjcxxcjPpxxjgbDO = BeanUtils.toBean(ppxxVO, HbsjcxxcjPpxxjgbDO.class);
//                hbsjcxxcjPpxxjgbDO.setPpxxuuid(uuid);
//                hbsjcxxcjPpxxjgbDO.setPpztbz(ppxxVO.getPpzt());
//                if ("N".equals(hbsjcxxcjPpxxjgbDO.getPpztbz())) {
//                    // 删除匹配信息
//                    hbsjcxxcjPpxxjgbMapper.deleteById(hbsjcxxcjPpxxjgbDO);
//                    // 删除污染物明细信息
//                    final QueryWrapper<HbsjcxxcjPpxxwrwmxbDO> queryWrapper = new QueryWrapper<>();
//                    queryWrapper.eq("ppxxuuid", uuid);
//                    queryWrapper.eq("djxh", djxh);
//                    hbsjcxxcjPpxxwrwmxbMapper.delete(queryWrapper);
//                } else {
//                    // 调用HbsPpxxMapper. savePpxx。
//                    // 调用HbsPpMxMapper. savePpmx。
//                    // 判断是否保存过排放口匹配信息
//                    final QueryWrapper<HbsjcxxcjPpxxjgbDO> queryWrapper1 = new QueryWrapper<>();
//                    queryWrapper1.eq("ppxxuuid", uuid);
//                    queryWrapper1.eq("yxbz", "Y");
//                    final List<HbsjcxxcjPpxxjgbDO> hbsjcxxcjPpxxjgbDOList = hbsjcxxcjPpxxjgbMapper.selectList(queryWrapper1);
//                    if (!GyUtils.isNull(hbsjcxxcjPpxxjgbDOList)) {
//                        // 匹配过
//                        // 修改匹配状态
//                        hbsjcxxcjPpxxjgbDO.setXgrq(now);
//                        hbsjcxxcjPpxxjgbDO.setXgrsfid(lrrsfid);
//                        hbsjcxxcjPpxxjgbMapper.updateById(hbsjcxxcjPpxxjgbDO);
//                        // 判断是否有新增品目，若有savePpmx。
//                        if (!GyUtils.isNull(ppxxVO.getPpmxVOList())) {
//                            final List<PpmxVO> ppmxVOList = ppxxVO.getPpmxVOList();
//                            final QueryWrapper<HbsjcxxcjPpxxwrwmxbDO> queryWrapper2 = new QueryWrapper<>();
//                            queryWrapper2.eq("ppxxuuid", uuid);
//                            final List<HbsjcxxcjPpxxwrwmxbDO> hbsjcxxcjPpxxwrwmxbDOList = hbsjcxxcjPpxxwrwmxbMapper.selectList(queryWrapper2);
//                            if (GyUtils.isNull(hbsjcxxcjPpxxwrwmxbDOList)) {
//                                for (int i = 0; i < ppmxVOList.size(); i++) {
//                                    final PpmxVO ppmxVO = ppxxVO.getPpmxVOList().get(i);
//                                    final HbsjcxxcjPpxxwrwmxbDO hbsjcxxcjPpxxwrwmxbDO = new HbsjcxxcjPpxxwrwmxbDO();
//                                    hbsjcxxcjPpxxwrwmxbDO.setPpxxuuid(uuid);
//                                    hbsjcxxcjPpxxwrwmxbDO.setPfkmc(ppxxVO.getPwxkzpfkmc());
//                                    hbsjcxxcjPpxxwrwmxbDO.setPfkbh(ppxxVO.getPwxkzpfkbh());
//                                    hbsjcxxcjPpxxwrwmxbDO.setDjxh(new BigDecimal(djxh));
//                                    hbsjcxxcjPpxxwrwmxbDO.setPfklbDm(ppmxVO.getPfklb());
//                                    hbsjcxxcjPpxxwrwmxbDO.setZspmDm(ppmxVO.getZspmDm());
//                                    hbsjcxxcjPpxxwrwmxbDO.setYwqdDm(ywqdDm);
//                                    hbsjcxxcjPpxxwrwmxbDO.setSjcsdq(sjcsdq);
//                                    hbsjcxxcjPpxxwrwmxbDO.setSjgsdq(sjgsdq);
//                                    hbsjcxxcjPpxxwrwmxbDO.setLrrq(now);
//                                    hbsjcxxcjPpxxwrwmxbDO.setLrrsfid(lrrsfid);
//                                    hbsjcxxcjPpxxwrwmxbMapper.insert(hbsjcxxcjPpxxwrwmxbDO);
//                                }
//                            } else {
//                                final List<String> yppZspmList = new ArrayList<>();
//                                hbsjcxxcjPpxxwrwmxbDOList.forEach(a -> yppZspmList.add(a.getZspmDm()));
//                                for (PpmxVO ppmxVO : ppmxVOList) {
//                                    if (!yppZspmList.contains(ppmxVO.getZspmDm())) {
//                                        final HbsjcxxcjPpxxwrwmxbDO hbsjcxxcjPpxxwrwmxbDO = new HbsjcxxcjPpxxwrwmxbDO();
//                                        hbsjcxxcjPpxxwrwmxbDO.setPpxxuuid(uuid);
//                                        hbsjcxxcjPpxxwrwmxbDO.setDjxh(new BigDecimal(djxh));
//                                        hbsjcxxcjPpxxwrwmxbDO.setPfkbh(ppxxVO.getPwxkzpfkbh());
//                                        hbsjcxxcjPpxxwrwmxbDO.setPfkmc(ppxxVO.getPwxkzpfkmc());
//                                        hbsjcxxcjPpxxwrwmxbDO.setPfklbDm(ppmxVO.getPfklb());
//                                        hbsjcxxcjPpxxwrwmxbDO.setZspmDm(ppmxVO.getZspmDm());
//                                        hbsjcxxcjPpxxwrwmxbDO.setYwqdDm(ywqdDm);
//                                        hbsjcxxcjPpxxwrwmxbDO.setSjcsdq(sjcsdq);
//                                        hbsjcxxcjPpxxwrwmxbDO.setSjgsdq(sjgsdq);
//                                        hbsjcxxcjPpxxwrwmxbDO.setLrrq(now);
//                                        hbsjcxxcjPpxxwrwmxbDO.setLrrsfid(lrrsfid);
//                                        hbsjcxxcjPpxxwrwmxbMapper.insert(hbsjcxxcjPpxxwrwmxbDO);
//                                    }
//                                }
//                            }
//                        }
//                    } else {
//                        // 未匹配过
//                        hbsjcxxcjPpxxjgbDO.setDjxh(new BigDecimal(djxh));
//                        hbsjcxxcjPpxxjgbDO.setSyuuid(uuid);
//                        hbsjcxxcjPpxxjgbDO.setPwxkzpfkbh(ppxxVO.getPwxkzpfkbh());
//                        hbsjcxxcjPpxxjgbDO.setPpztbz("Y");
//                        hbsjcxxcjPpxxjgbDO.setYxbz("Y");
//                        hbsjcxxcjPpxxjgbDO.setYwqdDm(ywqdDm);
//                        hbsjcxxcjPpxxjgbDO.setSjcsdq(sjcsdq);
//                        hbsjcxxcjPpxxjgbDO.setSjgsdq(sjgsdq);
//                        hbsjcxxcjPpxxjgbDO.setLrrq(now);
//                        hbsjcxxcjPpxxjgbDO.setLrrsfid(lrrsfid);
//                        hbsjcxxcjPpxxjgbMapper.insert(hbsjcxxcjPpxxjgbDO);
//                        if (!GyUtils.isNull(ppxxVO.getPpmxVOList())) {
//                            for (int i = 0; i < ppxxVO.getPpmxVOList().size(); i++) {
//                                final PpmxVO ppmxVO = ppxxVO.getPpmxVOList().get(i);
//                                final HbsjcxxcjPpxxwrwmxbDO hbsjcxxcjPpxxwrwmxbDO = new HbsjcxxcjPpxxwrwmxbDO();
//                                hbsjcxxcjPpxxwrwmxbDO.setPpxxuuid(uuid);
//                                hbsjcxxcjPpxxwrwmxbDO.setDjxh(new BigDecimal(djxh));
//                                hbsjcxxcjPpxxwrwmxbDO.setPfkmc(ppxxVO.getPwxkzpfkmc());
//                                hbsjcxxcjPpxxwrwmxbDO.setPfkbh(ppxxVO.getPwxkzpfkbh());
//                                hbsjcxxcjPpxxwrwmxbDO.setPfklbDm(ppmxVO.getPfklb());
//                                hbsjcxxcjPpxxwrwmxbDO.setZspmDm(ppmxVO.getZspmDm());
//                                hbsjcxxcjPpxxwrwmxbDO.setYwqdDm(ywqdDm);
//                                hbsjcxxcjPpxxwrwmxbDO.setSjcsdq(sjcsdq);
//                                hbsjcxxcjPpxxwrwmxbDO.setSjgsdq(sjgsdq);
//                                hbsjcxxcjPpxxwrwmxbDO.setLrrq(now);
//                                hbsjcxxcjPpxxwrwmxbDO.setLrrsfid(lrrsfid);
//                                hbsjcxxcjPpxxwrwmxbMapper.insert(hbsjcxxcjPpxxwrwmxbDO);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        returnMap.put("returnCode", "1");
//        returnMap.put("returnMsg", "匹配信息更新成功！");
//        return returnMap;
//    }
//
//    /**
//     * 申报计算页面初始化时获取下拉数据信息
//     */
//    @Override
//    public Map<String, Object> loadSbjsDictItems() {
//        Map<String, Object> sbjsDictItems = new HashMap<>();
//        // 获取缓存表DM_DJ_ZYWRWLB，赋值污染物类别下拉数据
//        List<Map<String, Object>> wrwlbList = CacheUtils.getTableData("DM_DJ_ZYWRWLB");
//        sbjsDictItems.put("wrwlbList", wrwlbList);
//        // 水污物种类加载下拉数据
//        List<Map<String, Object>> swrwlbList = loadZspm();
//        sbjsDictItems.put("swrwlbList", swrwlbList);
//        // 通过缓存表获取DM_SB_ZSSD，过滤代码值3，赋值噪声时段下拉数据
//        List<Map<String, Object>> zssdList = loadZssd();
//        sbjsDictItems.put("zssdList", zssdList);
//        // code="1" caption="是" 和 code="2" caption="否" 组装超标不足15天下拉
//        List<Map<String, Object>> cbbz15tList = new ArrayList<>();
//        Map<String, Object> map = new HashMap<>();
//        map.put("code", "1");
//        map.put("caption", "是");
//        cbbz15tList.add(map);
//        Map<String, Object> map1 = new HashMap<>();
//        map1.put("code", "2");
//        map1.put("caption", "否");
//        cbbz15tList.add(map1);
//        sbjsDictItems.put("cbbz15tList", cbbz15tList);
//        // code="Y" caption="是"  和 code="N" caption="否" ，组装两处以上噪声超标下拉
//        List<Map<String, Object>> lcyszscbList = new ArrayList<>();
//        Map<String, Object> map3 = new HashMap<>();
//        map3.put("code", "Y");
//        map3.put("caption", "是");
//        lcyszscbList.add(map3);
//        Map<String, Object> map4 = new HashMap<>();
//        map4.put("code", "N");
//        map4.put("caption", "否");
//        lcyszscbList.add(map4);
//        sbjsDictItems.put("lcyszscbList", lcyszscbList);
//        return sbjsDictItems;
//    }
//
//
    /**
     * 获取征收品目、征收子目对应的税率信息
     *
     * @return
     */
    @Override
    public Map<String, Object> getSl(SlReqVO slReqVO) {
        // 调用公共服务获取对应的税率信息。
        final String swjgdm = slReqVO.getSwjgdm();
        final String skssqq = slReqVO.getSkssqq();
        final String skssqz = slReqVO.getSkssqz();
        final String zsxmDm = slReqVO.getZsxmDm();
        final String zspmDm = slReqVO.getZspmDm();
        final String zszmDm = slReqVO.getZszmDm();
        final Map<String, Object> map = GyQtUtils.getSl(swjgdm, zsxmDm, zspmDm, zszmDm, skssqq, skssqz);
        return map;
    }

    /**
     * 获取环保税污染当量值配置信息
     */
    @Override
    public List<Wrdlpz> getPzxx(PzxxReqVo vo) {
        final String zspmDm = vo.getZspmDm();
        final String zszmDm = vo.getZszmDm();
        final String skssqq = vo.getSkssqq();
        final String skssqz = vo.getSkssqz();
        final String swjgDm = vo.getSwjgDm();
        //1.入参传值征收品目、征收子目、税款所属期起止、申报类型
        // session中的djxh和主管税务机关代码，通过缓存表获取CS_SB_HBSWRDLZTZZPZ  (环保机构与税务机关对照表),DM_GY_SWJG (税务机关代码表)。
        List<Wrdlpz> wrdlpzList = new ArrayList<>();
        String sfcycsbz = "Y";
        if (GyUtils.isNull(vo.getSblx()) || !"01".equals(vo.getSblx())) {
            sfcycsbz = "N";
        }
        List<Map<String, Object>> hbswrdlztzzpzList = getHbswrdlztzzpzList(swjgDm);
        if (GyUtils.isNotNull(zszmDm)) {
            // 如果入参征收子目不为空，根据入参征收子目查，不用入参征收品目
            hbswrdlztzzpzList = findListInMapList("zszmdm", zszmDm, hbswrdlztzzpzList);
        } else {
            // 如果入参征收子目为空，根据入参征收品目查，查询的结果中排除掉征收子目字段值为空的数据
            hbswrdlztzzpzList = findListInMapList("zspmdm", zspmDm, hbswrdlztzzpzList);
            final Iterator<Map<String, Object>> mapIterator = hbswrdlztzzpzList.iterator();
            while (mapIterator.hasNext()) {
                final Map<String, Object> map = mapIterator.next();
                if (GyUtils.isNotNull(map.get("zszmdm"))) {
                    mapIterator.remove();
                }
            }
        }
        for (Map<String, Object> map : hbswrdlztzzpzList) {
            // 过滤不在有效期
            if (!GYCastUtils.sqAcross(CxsJmxzUtils.cast2Date(skssqq),
                    CxsJmxzUtils.cast2Date(skssqz),
                    CxsJmxzUtils.cast2Date(map.get("yxqq")),
                    CxsJmxzUtils.cast2Date(map.get("yxqz")))) {
                continue;
            }
            if (!sfcycsbz.equals(GYCastUtils.cast2Str(map.get("sfcycsbz")))) {
                continue;
            }
            final Wrdlpz wrdlpz = new Wrdlpz();
            wrdlpz.setWrdlz(GyUtils.isNotNull(map.get("wrdlz")) ? GYCastUtils.cast2Str(map.get("wrdlz")) : "0.0");
            if (sfcycsbz.equals("Y")) {
                //2.如果申报类型为01, sfcycsbz传值Y查询抽样测算附表所需配置信息,
                // 返回字段wrdlz、tzcwxs、sfcycsbz、tzzjsf、sfczxj、tzzbz1、jsjsdwmc字段值，供页面使用。
                wrdlpz.setTzcwxs(GyUtils.isNotNull(map.get("tzcwxs"))
                        ? map.get("tzcwxs").toString() : "0.0");
                wrdlpz.setSfcycsbz(GyUtils.isNotNull(map.get("sfcycsbz"))
                        ? map.get("sfcycsbz").toString() : null);
                wrdlpz.setTzzjsf(GyUtils.isNotNull(map.get("tzzjsf"))
                        ? map.get("tzzjsf").toString() : null);
                wrdlpz.setSfczxj(GyUtils.isNotNull(map.get("sfczxj"))
                        ? map.get("sfczxj").toString() : null);
                wrdlpz.setTzzbz1(GyUtils.isNotNull(map.get("tzzbz1"))
                        ? map.get("tzzbz1").toString() : null);
                wrdlpz.setJsjsdwmc(GyUtils.isNotNull(map.get("jsjsdwmc"))
                        ? map.get("jsjsdwmc").toString() : null);
                wrdlpzList.add(wrdlpz);
            } else {
                wrdlpz.setJldwDm(GyUtils.isNotNull(map.get("jldwDm"))
                        ? map.get("jldwDm").toString() : null);
                wrdlpzList.add(wrdlpz);
            }
        }
        return wrdlpzList;

    }
//
//    /**
//     * 获取减免性质附属信息
//     *
//     * @param ssjmxzDm
//     */
//    @Override
//    public Map<String, Object> getJmxzmcAndFd(String ssjmxzDm, String swjgDm) {
//        final Map<String, Object> ssjmxzJmfdedslgx = NsrglYhUtils.getSsjmxzJmfdedslgx(ssjmxzDm, swjgDm);
//        return ssjmxzJmfdedslgx;
//    }
//
    /**
     * 通过纳税期限、申报日期获取默认所属期
     */
    @Override
    public Map<String, Object> getSkssq() {
        String nsqxDm = "08";
        //2.通过公共服务根据nsqxDm、申报日期获取上期申报税款属期。
        //3.返回税款所属期起、税款所属期止。
        Map<String, Object> map = CchxwsnssbGyUtils.jsSkssq(nsqxDm, new Date(), "N");
        return map;
    }
//
//    /**
//     * 导入税源登记信息
//     */
//    @Override
//    public Map<String, Object> checkSyjcxx(HbssyxxDrReqVO hbssyxxDrReqVO) {
//        final Map<String, Object> returnMap = new HashMap<>();
//        final StringBuffer msg = new StringBuffer();//错误消息
//        final String zywrwlbsDm = hbssyxxDrReqVO.getZywrwlbsDm();
//        final String now = DateUtil.format(new Date(), "yyyy-MM-dd");
//        final HbsJcxxDTO hbsjcxxcjb = getHbsjcxxcjb(hbssyxxDrReqVO.getDjxh(), hbssyxxDrReqVO.getSjjg());
//        if (GyUtils.isNull(hbsjcxxcjb.getHbsjcxxcjb())) {
//            msg.append("请先保存环境保护税税源明细表表头信息，再进行导入!\n");
//            returnMap.put("code", FAIL_CODE);
//            returnMap.put("msg", msg);
//            return returnMap;
//        }
//        final HbsjcxxcjbDTO hbsjcxxcjbDTO = hbsjcxxcjb.getHbsjcxxcjb();
//        final String hbsjcxxuuid = !GyUtils.isNull(hbsjcxxcjbDTO.getHbsjcxxuuid()) ? hbsjcxxcjbDTO.getHbsjcxxuuid() : "";
//        final List<SyxxDTO> syxxGridList = hbsjcxxcjb.getSyxxGridList();
//        final Map<String, HbsDqsDrVO> dqsfbMap = new HashMap<>();
//        final Map<String, HbsGfDrVO> gffbMap = new HashMap<>();
//        final Map<String, HbsZsDrVO> zsfbMap = new HashMap<>();
//        // 校验表格信息
//        // 基础信息导入时校验计算方法
//        final Map<String, String> jsffMap = new HashMap<>();
//        jsffMap.put("101211111", "12345");//征收品目为一般性粉尘时，计算方法可选5个
//        jsffMap.put("101211120", "12345");//征收品目为烟尘时，计算方法可选5个
//        jsffMap.put("101211101", "12345");//征收品目为二氧化硫时，计算方法可选5个。
//        jsffMap.put("101211102", "12345");//征收品目为氮氧化物时，计算方法可选5个。
//        jsffMap.put("N", "12");//主要污染物类别为噪声计算方法只能选1、2
//        jsffMap.put("101212400", "5");//水污染物种类为“禽畜养殖业、小型企业和第三产业”时，计算方法只能选5
//        jsffMap.put("other", "1234");//其他选1234
//        // 通用校验
//        final List<HbsSyxxDrVO> hbssyxxList = hbssyxxDrReqVO.getHbssyxxList();
//        for (int i = 0; i < hbssyxxList.size(); i++) {
//            int row = i + 5;
//            final HbsSyxxDrVO hbsSyxxDrVO = hbssyxxList.get(i);
//            final String zspm = hbsSyxxDrVO.getZspmDm();
//            final String zspmDm = GyUtils.isNull(zspm) ? "" : zspm.split("\\|")[0];
//            final String zszm = hbsSyxxDrVO.getZszmDm();
//            final String zszmDm = GyUtils.isNull(zszm) ? "" : zszm.split("\\|")[0];
//            final String fzspm = hbsSyxxDrVO.getFzspmDm();
//            final String fzspmDm = GyUtils.isNull(fzspm) ? "" : fzspm.split("\\|")[0];
//            final String jdxz = hbsSyxxDrVO.getJdxzDm();
//            final String jdxzDm = GyUtils.isNull(jdxz) ? "" : jdxz.split("\\|")[0];
//            final String zywrwlb = hbsSyxxDrVO.getZywrwlbDm();
//            final String zywrwlbDm = GyUtils.isNull(zywrwlb) ? "" : zywrwlb.substring(0, 1);
//            final String wrwpfljsffDm = GyUtils.isNull(hbsSyxxDrVO.getWrwpfljsffDm())
//                    ? "" : hbsSyxxDrVO.getWrwpfljsffDm().substring(0, 1);
//            //A.判断主要污染物类别、排污许可证编号、排放口编号、生产经营所在街乡、税源有效期起、税源有效期止是否为空。
//            if (GyUtils.isNull(hbsSyxxDrVO.getZywrwlbDm())
//                    || GyUtils.isNull(hbsSyxxDrVO.getPwxkzbh())
//                    || GyUtils.isNull(hbsSyxxDrVO.getPfkbh())
//                    || GyUtils.isNull(hbsSyxxDrVO.getJdxzDm())
//                    || GyUtils.isNull(hbsSyxxDrVO.getPfkbh())
//                    || GyUtils.isNull(hbsSyxxDrVO.getSyyxqq())
//                    || GyUtils.isNull(hbsSyxxDrVO.getSyyxqz())) {
//                msg.append("税源信息表中第" + row + "行必录字段不能为空!\n");
//            }
//            if ("N".equals(zywrwlbDm) || "W".equals(zywrwlbDm) || "A".equals(zywrwlbDm)) {
//                if (GyUtils.isNull(hbsSyxxDrVO.getWrwpfljsffDm())) {
//                    msg.append("税源信息表中第" + row + "行大气水,噪声的污染物排放量计算方法不能为空!\n");
//                }
//            }
//            if ("S".equals(zywrwlbDm) && !GyUtils.isNull(hbsSyxxDrVO.getWrwpfljsffDm())) {
//                msg.append("税源信息表中第" + row + "行固体废物的污染物排放量计算方法不需要填写!\n");
//            }
//            //B.校验导入信息的污染物类别是否包含在表头选择的“污染物类别”中。
//            if (GyUtils.isNull(zywrwlbsDm) || !zywrwlbsDm.contains(zywrwlbDm)) {
//                msg.append("税源信息表中第" + row + "行填写的污染物类别不包含在表头选择的’污染物类别’中，请确认!\n");
//            }
//            //A.污染物类别‘水污染物’时，‘水污染物种类’不能为空。
//            if ("W".equals(zywrwlbDm) && GyUtils.isNull(hbsSyxxDrVO.getFzspmDm())) {
//                msg.append("税源信息表中第" + row + "行污染物类别‘水污染物’时，‘水污染物种类’不能为空!\n");
//            }
//            if (!"W".equals(zywrwlbDm) && !GyUtils.isNull(hbsSyxxDrVO.getFzspmDm())) {
//                msg.append("税源信息表中第" + row + "行污染物类别不为‘水污染物’时，请勿选择‘水污染物种类’!\n");
//            }
//            // 校验水污染物种类是否和污染物名称对应
//            if ("W".equals(zywrwlbDm)) {
//                final Map<String, Object> zspmMap = FtsUtils.getMapByDm("DM_GY_ZSPM", zspmDm);
//                final String sjzspmDm = (String) zspmMap.get("SJZSPM_DM");
//                if (!fzspmDm.equals(sjzspmDm)) {
//                    msg.append("税源信息表中第" + row + "行‘水污染物种类’与‘污染物名称’不匹配，请核实!\n");
//                }
//            }
//            //C.校验税源有效期起必须为月初，税源有效期止必须为月末，且税源有效期起不能大于税源有效期止。
//            final Date syyxqq = GYCastUtils.cast2Date(hbsSyxxDrVO.getSyyxqq());
//            final Date syyxqz = GYCastUtils.cast2Date(hbsSyxxDrVO.getSyyxqz());
//            if (!HbssyxxcjUtils.isFirstDayOfMonth(syyxqq) || !HbssyxxcjUtils.isLastDayOfMonth(syyxqz)) {
//                msg.append("税源信息表中第" + row + "行税源有效期起必须为月初，税源有效期止必须为月末。\n");
//            }
//            if (syyxqq.after(syyxqz)) {
//                msg.append("税源信息表中第" + row + "行税源有效期起不能晚于税源有效期止。\n");
//            }
//            // 基础信息导入时校验计算方法
//            String key = "other";
//            if (jsffMap.containsKey(zspmDm)) {
//                key = zspmDm;
//            } else if (jsffMap.containsKey(fzspmDm)) {
//                key = fzspmDm;
//            } else if (jsffMap.containsKey(zywrwlbDm)) {
//                key = zywrwlbDm;
//            }
//            if (!jsffMap.get(key).contains(wrwpfljsffDm)) {
//                if ("N".equals(zywrwlbDm)) {
//                    msg.append("税源信息表中第" + row + "行导入的噪声污染物信息的计算方法有误，请修改！\n");
//                } else if (key.equals(fzspmDm)) {
//                    msg.append("税源信息表中第" + row + "行导入信息中水污染物种类代码值为" + fzspmDm + "的计算方法有误，请修改！\n");
//                } else {
//                    msg.append("税源信息表中第" + row + "行导入信息中污染物名称代码值为" + zspmDm + "的计算方法有误，请修改！\n");
//                }
//            }
//            hbsSyxxDrVO.setZspmDm(zspmDm);
//            hbsSyxxDrVO.setZszmDm(zszmDm);
//            hbsSyxxDrVO.setFzspmDm(fzspmDm);
//            hbsSyxxDrVO.setJdxzDm(jdxzDm);
//            hbsSyxxDrVO.setZywrwlbDm(zywrwlbDm);
//            hbsSyxxDrVO.setWrwpfljsffDm(wrwpfljsffDm);
//        }
//        if (msg.length() > 0) {
//            returnMap.put("code", FAIL_CODE);
//            returnMap.put("msg", msg);
//            return returnMap;
//        }
//        // 大气水导入校验
//        if (!hbssyxxDrReqVO.getHbsdqsList().isEmpty()) {
//            final Set<String> qcSet = new HashSet<>();
//            final List<HbsDqsDrVO> hbsdqsList = hbssyxxDrReqVO.getHbsdqsList();
//            for (int i = 0; i < hbsdqsList.size(); i++) {
//                int row = i + 4;
//                final HbsDqsDrVO hbsDqsDrVO = hbsdqsList.get(i);
//                final String hgbhssybh = GyUtils.isNotNull(hbsDqsDrVO.getHgbhssybh())
//                        ? hbsDqsDrVO.getHgbhssybh() : "";
//                final String pwxkzbh = hbsDqsDrVO.getPwxkzbh();
//                final String pfkbh = hbsDqsDrVO.getPfkbh();
//                final String zspm = hbsDqsDrVO.getZspmDm();
//                final String zspmDm = GyUtils.isNull(zspm) ? "" : zspm.split("\\|")[0];
//                final String key = hgbhssybh + zspmDm;
//                if (GyUtils.isNull(zspm) || GyUtils.isNull(pwxkzbh) || GyUtils.isNull(pfkbh)) {
//                    msg.append("大气、水污染采集表中第" + row + "行必录字段不能为空!\n");
//                    continue;
//                }
//                //B.如果导入信息中税源编号不为空，则校验同一税源编号和污染物名称是否存在于已采集税源信息中。
//                if (GyUtils.isNotNull(hbsDqsDrVO.getHgbhssybh())) {
//                    /*for (SyxxDTO syxxDTO : syxxGridList) {
//                        if (syxxDTO.getHgbhssybh().equals(hgbhssybh) && syxxDTO.getZspmDm().equals(zspmDm)) {
//                            msg.append(hgbhssybh + "税源已采集!\n");
//                        }
//                    }*/
//                    //C.校验同一税源编号和污染物名称是否重复。
//                    if (!qcSet.contains(key)) {
//                        qcSet.add(key);
//                    } else {
//                        msg.append("大气、水污染采集表中第" + row + "行存在重复的税源编号+污染物名称代码" + hgbhssybh + "+" + zspmDm + "的信息!\n");
//                    }
//                }
//                hbsDqsDrVO.setZspmDm(zspmDm);
//                String key1 = hbsDqsDrVO.getPfkbh() + hbsDqsDrVO.getPwxkzbh() + hbsDqsDrVO.getZspmDm();
//                if (!GyUtils.isNull(hbsDqsDrVO.getHgbhssybh())) {
//                    key1 = key1 + hbsDqsDrVO.getHgbhssybh();
//                }
//                if (!dqsfbMap.containsKey(key1)) {
//                    dqsfbMap.put(key1, hbsDqsDrVO);
//                } else {
//                    msg.append("大气、水污染采集表中第" + row + "行存在重复的对应排污许可证编号+排放口编号+污染物名称代码" + pwxkzbh + "+" + pfkbh + "+" + zspmDm + "的信息!\n");
//                }
//            }
//        }
//        if (msg.length() > 0) {
//            returnMap.put("code", FAIL_CODE);
//            returnMap.put("msg", msg);
//            return returnMap;
//        }
//        //固废导入校验
//        if (!hbssyxxDrReqVO.getHbsgfList().isEmpty()) {
//            final Set<String> qcSet = new HashSet<>();
//            final List<HbsGfDrVO> hbsgfList = hbssyxxDrReqVO.getHbsgfList();
//            for (int i = 0; i < hbsgfList.size(); i++) {
//                int row = i + 4;
//                final HbsGfDrVO hbsGfDrVO = hbsgfList.get(i);
//                final String hgbhssybh = GyUtils.isNotNull(hbsGfDrVO.getHgbhssybh())
//                        ? hbsGfDrVO.getHgbhssybh() : "";
//                final String pwxkzbh = hbsGfDrVO.getPwxkzbh();
//                final String pfkbh = hbsGfDrVO.getPfkbh();
//                final String zspm = hbsGfDrVO.getZspmDm();
//                final String zspmDm = GyUtils.isNull(zspm) ? "" : zspm.split("\\|")[0];
//                final String key = hgbhssybh + zspmDm;
//                if (GyUtils.isNull(zspm) || GyUtils.isNull(pwxkzbh) || GyUtils.isNull(pfkbh)) {
//                    msg.append("固体废物采集表中第" + row + "行必录字段不能为空!\n");
//                    continue;
//                }
//                //B.如果导入信息中税源编号不为空，则校验同一税源编号和污染物名称是否存在于已采集税源信息中。
//                if (GyUtils.isNotNull(hbsGfDrVO.getHgbhssybh())) {
//                    /*for (SyxxDTO syxxDTO : syxxGridList) {
//                        if (syxxDTO.getHgbhssybh().equals(hgbhssybh) && syxxDTO.getZspmDm().equals(zspmDm)) {
//                            msg.append(hgbhssybh + "税源已采集!\n");
//                        }
//                    }*/
//                    //C.校验同一税源编号和污染物名称是否重复。
//                    if (!qcSet.contains(key)) {
//                        qcSet.add(key);
//                    } else {
//                        msg.append("固体废物采集表中第" + row + "行存在重复的税源编号+污染物名称代码" + hgbhssybh + "+" + zspmDm + "的信息!\n");
//                    }
//                }
//
//                //综合利用情况校验
//                final String zhlyzyfssDms = "0301,0302,0303,0399";
//                final String zhlyzyfssDm = hbsGfDrVO.getZhlyzyfssDm();
//                if (!GyUtils.isNull(zhlyzyfssDm)) {
//                    final List<String> strings = Arrays.asList(zhlyzyfssDm.split(","));
//                    for (String s : strings) {
//                        if (!zhlyzyfssDms.contains(s)) {
//                            msg.append("综合利用情况代码" + s + "不存在。\n");
//                        }
//                    }
//                }
//                hbsGfDrVO.setZspmDm(zspmDm);
//                String key1 = hbsGfDrVO.getPfkbh() + hbsGfDrVO.getPwxkzbh() + hbsGfDrVO.getZspmDm();
//                if (!GyUtils.isNull(hbsGfDrVO.getHgbhssybh())) {
//                    key1 = key1 + hbsGfDrVO.getHgbhssybh();
//                }
//                if (!gffbMap.containsKey(key1)) {
//                    gffbMap.put(key1, hbsGfDrVO);
//                } else {
//                    msg.append("固体废物采集表中第" + row + "行存在重复的对应排污许可证编号+排放口编号+污染物名称代码" + pwxkzbh + "+" + pfkbh + "+" + zspmDm + "的信息!\n");
//                }
//            }
//        }
//        if (msg.length() > 0) {
//            returnMap.put("code", FAIL_CODE);
//            returnMap.put("msg", msg);
//            return returnMap;
//        }
//        //噪声导入校验
//        if (!hbssyxxDrReqVO.getHbszsList().isEmpty()) {
//            final List<HbsZsDrVO> hbszsList = hbssyxxDrReqVO.getHbszsList();
//            for (int i = 0; i < hbszsList.size(); i++) {
//                int row = i + 4;
//                final HbsZsDrVO hbsZsDrVO = hbszsList.get(i);
//                final String pwxkzbh = hbsZsDrVO.getPwxkzbh();
//                final String pfkbh = hbsZsDrVO.getPfkbh();
//                final String sfzycsbz = hbsZsDrVO.getSfzycsbz();
//                final String hgbhssybh = GyUtils.isNotNull(hbsZsDrVO.getHgbhssybh())
//                        ? hbsZsDrVO.getHgbhssybh() : "";
//                if (GyUtils.isNull(pwxkzbh) || GyUtils.isNull(pfkbh)) {
//                    msg.append("噪声采集表中第" + row + "行必录字段不能为空!\n");
//                    continue;
//                }
//                //B.如果导入信息中税源编号不为空，则校验税源编号是否存在于已采集税源信息中。
//                /*if (GyUtils.isNotNull(hbsZsDrVO.getHgbhssybh())) {
//                    for (SyxxDTO syxxDTO : syxxGridList) {
//                        if (syxxDTO.getHgbhssybh().equals(hgbhssybh)) {
//                            msg.append(hgbhssybh + "税源已采集!\n");
//                        }
//                    }
//                }*/
//                //C.是否昼夜产生、标准值——昼间(6时至22时)不能为空。
//                if (GyUtils.isNull(hbsZsDrVO.getSfzycsbz()) || GyUtils.isNull(hbsZsDrVO.getBzzzj())) {
//                    msg.append("噪声采集表中第" + row + "行是否昼夜产生、标准值——昼间(6时至22时)不能为空!\n");
//                }
//                //D.‘是否昼夜产生’为‘是’时，‘标准值——夜间(22时-次日6时)’不能为空。
//                final String csbz = sfzycsbz.substring(0, 1);
//                if ("Y".equals(csbz) && GyUtils.isNull(hbsZsDrVO.getBzzyj())) {
//                    msg.append("噪声采集表中第" + row + "行‘是否昼夜产生’为‘是’时，‘标准值——夜间(22时-次日6时)’不能为空!\n");
//                }
//                hbsZsDrVO.setSfzycsbz(csbz);
//                String key1 = hbsZsDrVO.getPfkbh() + hbsZsDrVO.getPwxkzbh();
//                if (!GyUtils.isNull(hbsZsDrVO.getHgbhssybh())) {
//                    key1 = key1 + hbsZsDrVO.getHgbhssybh();
//                }
//                if (!zsfbMap.containsKey(key1)) {
//                    zsfbMap.put(key1, hbsZsDrVO);
//                } else {
//                    msg.append("噪声采集表中第" + row + "行存在重复的对应排污许可证编号+排放口编号" + pwxkzbh + "+" + pfkbh + "+" + "的信息!\n");
//                }
//            }
//        }
//        //产排污系数导入校验
//        if (!hbssyxxDrReqVO.getHbspwList().isEmpty()) {
//            final List<HbsPwDrVO> hbspwList = hbssyxxDrReqVO.getHbspwList();
//            for (int i = 0; i < hbspwList.size(); i++) {
//                int row = i + 4;
//                final HbsPwDrVO hbsPwDrVO = hbspwList.get(i);
//                final String wrwdwDm = hbsPwDrVO.getWrwdwDm();
//                final String zywrwlbDm = hbsPwDrVO.getZywrwlbDm();
//                final String zspmDm = hbsPwDrVO.getZspmDm();
//                // 计税基数单位、污染物单位、产污系数、排污系数、污染物名称不能为空
//                if (GyUtils.isNull(hbsPwDrVO.getJsjsdw())
//                        || GyUtils.isNull(wrwdwDm)
//                        || GyUtils.isNull(hbsPwDrVO.getCwxs())
//                        || GyUtils.isNull(hbsPwDrVO.getPwxs())
//                        || GyUtils.isNull(zywrwlbDm)
//                        || GyUtils.isNull(zspmDm)) {
//                    msg.append("排污系数采集表中第" + row + "行必录字段不能为空!\n");
//                }
//                final String zszmDm = hbsPwDrVO.getZszmDm();
//                hbsPwDrVO.setZszmDm(GyUtils.isNull(zszmDm) ? "" : zszmDm.split("\\|")[0]);
//                hbsPwDrVO.setZspmDm(zspmDm.split("\\|")[0]);
//                hbsPwDrVO.setZywrwlbDm(zywrwlbDm.substring(0, 1));
//                hbsPwDrVO.setWrwdwDm(wrwdwDm.substring(0, 4));
//                hbsPwDrVO.setPwxs(MathUtils.round(hbsPwDrVO.getPwxs(), 8));
//                hbsPwDrVO.setCwxs(MathUtils.round(hbsPwDrVO.getCwxs(), 8));
//            }
//        }
//        if (msg.length() > 0) {
//            returnMap.put("code", FAIL_CODE);
//            returnMap.put("msg", msg);
//            return returnMap;
//        }
//        // 封装数据
//        for (HbsSyxxDrVO hbsSyxxDrVO : hbssyxxList) {
//            // 度分秒转换
//            final Double jd2d = hbsSyxxDrVO.getJd2d();
//            final Double jd2f = hbsSyxxDrVO.getJd2f();
//            final Double jd2m = hbsSyxxDrVO.getJd2m();
//            if (GyUtils.isNotNull(jd2d)
//                    && GyUtils.isNotNull(jd2f)
//                    && GyUtils.isNotNull(jd2m)) {
//                hbsSyxxDrVO.setJd2(GYCastUtils.cast2Str(jd2d)
//                        + "°" + GYCastUtils.cast2Str(jd2f)
//                        + "′" + GYCastUtils.cast2Str(jd2m) + "″");
//            } else {
//                hbsSyxxDrVO.setJd2("");
//            }
//            // 其他大气污染物和其他水污染物，污染物排放量计算方法自动置为抽样测算
//            if ("101211999".equals(hbsSyxxDrVO.getZspmDm()) || "101212999".equals(hbsSyxxDrVO.getZspmDm())) {
//                hbsSyxxDrVO.setWrwpfljsffDm("5");
//            }
//            String key = hbsSyxxDrVO.getPfkbh() + hbsSyxxDrVO.getPwxkzbh();
//            if (!"N".equals(hbsSyxxDrVO.getZywrwlbDm()) && !GyUtils.isNull(hbsSyxxDrVO.getZspmDm())) {
//                key = key + hbsSyxxDrVO.getZspmDm();
//            }
//            if (!GyUtils.isNull(hbsSyxxDrVO.getHgbhssybh())) {
//                key = key + hbsSyxxDrVO.getHgbhssybh();
//            }
//            // 大气
//            if ("W,A".contains(hbsSyxxDrVO.getZywrwlbDm()) && !GyUtils.isNull(dqsfbMap)) {
//                if (dqsfbMap.containsKey(key)) {
//                    final HbsDqsDrVO hbsDqsDrVO = dqsfbMap.get(key);
//                    hbsSyxxDrVO.setBzndz(hbsDqsDrVO.getBzndz());
//                    hbsSyxxDrVO.setZxbz1(hbsDqsDrVO.getZxbz1());
//                }
//            }
//            //固废
//            if ("S".equals(hbsSyxxDrVO.getZywrwlbDm()) && !GyUtils.isNull(gffbMap)) {
//                if (gffbMap.containsKey(key)) {
//                    final HbsGfDrVO hbsGfDrVO = gffbMap.get(key);
//                    hbsSyxxDrVO.setCshssmc(hbsGfDrVO.getCshssmc());
//                    hbsSyxxDrVO.setCzqk(hbsGfDrVO.getCzqk());
//                    hbsSyxxDrVO.setZhlyzyfssDm(hbsGfDrVO.getZhlyzyfssDm());
//                }
//            }
//            // 噪声
//            if ("N".equals(hbsSyxxDrVO.getZywrwlbDm()) && !GyUtils.isNull(zsfbMap)) {
//                if (zsfbMap.containsKey(key)) {
//                    final HbsZsDrVO hbsZsDrVO = zsfbMap.get(key);
//                    hbsSyxxDrVO.setSfzycsbz(hbsZsDrVO.getSfzycsbz());
//                    hbsSyxxDrVO.setBzzzj(hbsZsDrVO.getBzzzj());
//                    hbsSyxxDrVO.setBzzyj(hbsZsDrVO.getBzzyj());
//                }
//            }
//            // 排污
//            if ("W,A".contains(hbsSyxxDrVO.getZywrwlbDm()) && "4".equals(hbsSyxxDrVO.getWrwpfljsffDm()) && !GyUtils.isNull(hbssyxxDrReqVO.getHbspwList())) {
//                final List<PwxscjbDTO> pwxscjbDTOList = new ArrayList<>();
//                for (HbsPwDrVO hbsPwDrVO : hbssyxxDrReqVO.getHbspwList()) {
//                    if (hbsSyxxDrVO.getZspmDm().equals(hbsPwDrVO.getZspmDm())) {
//                        if (!GyUtils.isNull(hbsSyxxDrVO.getZszmDm()) && hbsSyxxDrVO.getZszmDm().equals(hbsPwDrVO.getZszmDm())) {
//                            pwxscjbDTOList.add(BeanUtils.toBean(hbsPwDrVO, PwxscjbDTO.class));
//                        } else if (GyUtils.isNull(hbsSyxxDrVO.getZszmDm())) {
//                            pwxscjbDTOList.add(BeanUtils.toBean(hbsPwDrVO, PwxscjbDTO.class));
//                        }
//                    }
//                }
//                hbsSyxxDrVO.setPwxscjbDTOList(pwxscjbDTOList);
//            }
//            // 匹配征收品目和征收子目名称
//            hbsSyxxDrVO.setZspmDmMc(FtsUtils.getMcByDm("DM_GY_ZSPM", "ZSPMMC", hbsSyxxDrVO.getZspmDm()));
//            hbsSyxxDrVO.setZszmDmMc(FtsUtils.getMcByDm("DM_GY_ZSZM", "ZSZMMC", hbsSyxxDrVO.getZszmDm()));
//            if (GYCastUtils.cast2Date(hbsSyxxDrVO.getSyyxqz()).before(GYCastUtils.cast2Date(now))) {
//                hbsSyxxDrVO.setSyxxyxx("N");
//            } else {
//                hbsSyxxDrVO.setSyxxyxx("Y");
//            }
//        }
//        if (msg.length() > 0) {
//            returnMap.put("code", FAIL_CODE);
//            returnMap.put("msg", msg);
//            return returnMap;
//        }
//        msg.append("导入成功!");
//        returnMap.put("hbssyxxList", hbssyxxList);
//        returnMap.put("code", "1");
//        returnMap.put("msg", msg);
//        return returnMap;
//    }
//
//    /**
//     * 导入申报计算及减免信息采集信息
//     */
//    @Override
//    public Map<String, Object> importSbjsjjmxx(HbssbjsDrReqVO hbssbjsDrReqVO) {
//        final String djxh = hbssbjsDrReqVO.getDjxh();
//        final String swjgDm = hbssbjsDrReqVO.getSjjg();
//        final Map<String, Object> returnMap = new HashMap<>();
//        final StringBuffer msg = new StringBuffer();//错误消息
//        final HbsJcxxDTO hbsjcxxcjb = getHbsjcxxcjb(djxh, swjgDm);
//        final List<SyxxDTO> syxxGridList = hbsjcxxcjb.getSyxxGridList();
//        final SbjsVO sbjsVO
//                = new SbjsVO();
//        // 获取当前属期
//        final Map<String, Object> skssq = getSkssq();
//        final String skssqq = GYCastUtils.cast2Str(skssq.get("skssqq"));
//        final String skssqz = GYCastUtils.cast2Str(skssq.get("skssqz"));
//        // 已采集的税源信息
//        final Map<String, Object> map = new HashMap<>();
//        // 已采集的产排污系数采集表
//        final QueryPwxsReqVO queryPwxsReqVO = new QueryPwxsReqVO();
//        queryPwxsReqVO.setDjxh(djxh);
//        queryPwxsReqVO.setSwjgDm(swjgDm);
//        final List<PwxscjbDTO> pwxscjbDTOList = this.queryPwxs(queryPwxsReqVO);
//        if (!GyUtils.isNull(syxxGridList)) {
//            for (SyxxDTO syxxDTO : syxxGridList) {
//                //税源编号+征收品目
//                String key = syxxDTO.getHgbhssybh();
//                if (!GyUtils.isNull(syxxDTO.getZspmDm()) && !"N".equals(syxxDTO.getZywrwlbDm())) {
//                    key = syxxDTO.getHgbhssybh() + syxxDTO.getZspmDm();
//                }
//                // 天津存在同一排放口，危险废物，不同子目多条税源
//                if ("101213003".equals(syxxDTO.getZspmDm()) && !GyUtils.isNull(syxxDTO.getZszmDm())) {
//                    key = syxxDTO.getHgbhssybh() + syxxDTO.getZspmDm() + syxxDTO.getZszmDm();
//                }
//                map.put(key, syxxDTO);
//            }
//        }
//        final Map<String, Object> pmSyxxMap = new HashMap<>();
//        for (SyxxDTO syxxDTO : syxxGridList) {
//            final Calendar yxqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(syxxDTO.getSyyxqq()));
//            final Calendar yxqz = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(syxxDTO.getSyyxqz()));
//            final Calendar dskssqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(skssqq));
//            final Calendar dskssqz = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(skssqz));
//            final Calendar start = yxqq.after(dskssqq) ? yxqq : dskssqq;
//            final Calendar end = yxqz.before(dskssqz) ? yxqz : dskssqz;
//            if (end.after(start)) {
//                for (int i = start.get(Calendar.MONTH) + 1; i <= end.get(Calendar.MONTH) + 1; i++) {
//                    pmSyxxMap.put(syxxDTO.getHgbhssybh() + syxxDTO.getZspmDm() + i, syxxDTO);
//                }
//            }
//        }
//        final Map<String, String> jsffMap = new HashMap<>();
//        jsffMap.put("3", "物料衡算");
//        jsffMap.put("4", "排污系数");
//        jsffMap.put("5", "抽样测算");
//
//        // 校验大气水申报计算信息
//        if (GyUtils.isNotNull(hbssbjsDrReqVO.getDqssbList())) {
//            final List<DqswrVO> dqssbList = hbssbjsDrReqVO.getDqssbList();
//            for (int i = 0; i < dqssbList.size(); i++) {
//                final DqswrVO dqswrVO = dqssbList.get(i);
//                final String sybh = dqswrVO.getHgbhssybh();
//                final String zspmDm = this.getDmForDr(dqswrVO.getZspmDm());
//                dqswrVO.setZspmDm(zspmDm);
//                dqswrVO.setZszmDm(this.getDmForDr(dqswrVO.getZszmDm()));
//                final String yf = dqswrVO.getYf();
//                if (GyUtils.isNull(yf)) {
//                    msg.append("大气水污染导入信息中月份不能为空！\n");
//                    continue;
//                }
//                dqswrVO.setSsjmxzDm(this.getDmForDr(dqswrVO.getSsjmxzDm()));
//                final String key = sybh + zspmDm;
//                if (!this.betweenMonth(skssqq, skssqz, yf)) {
//                    msg.append("大气水污染导入信息中月份不在税款所属期之间！\n");
//                    continue;
//                }
//                if (!pmSyxxMap.containsKey(key + dqswrVO.getYf())) {
//                    msg.append(String.format("没有查询到%s(%s月)对应的大气水计算表导入数据！\n", sybh, dqswrVO.getYf()));
//                    continue;
//                }
//                final SyxxDTO syxxDTO = (SyxxDTO) pmSyxxMap.get(key + dqswrVO.getYf());
//                final String syuuid = syxxDTO.getSyuuid();
//                final String wrwpfljsffDm = syxxDTO.getWrwpfljsffDm();
//                if (!map.keySet().contains(key) && "1,2".contains(wrwpfljsffDm)) {
//                    msg.append(String.format("没有查询到%s(%s月)对应的大气水计算表导入数据！\n", sybh, dqswrVO.getYf()));
//                    continue;
//                }
//                if ("3,4,5".contains(wrwpfljsffDm)
//                        && !GyUtils.isNull(dqswrVO.getSsjmxzDm())) {
//                    msg.append(String.format("大气水附表，税源编号为" + sybh + "的导入信息污染物排放量计算方法为"
//                            + jsffMap.get(wrwpfljsffDm)) + "，不允许选择减免，请修改导入信息！\n");
//                    continue;
//                }
//                if ("4".equals(wrwpfljsffDm)) {
//                    //产污系数和排污系数必须录入其中一项，不能都为空，也不能都录
//                    final Double cwxs = GyUtils.isNull(dqswrVO.getCwxs())
//                            ? 0.0 : dqswrVO.getCwxs();
//                    final Double pwxs = GyUtils.isNull(dqswrVO.getPwxs())
//                            ? 0.0 : dqswrVO.getPwxs();
//                    if ((cwxs != 0.0 && pwxs != 0.0) || (cwxs == 0.0 && pwxs == 0.0)) {
//                        msg.append(String.format("税源编号为%s(%s月)的大气水计算表导入数据中必录且只能录入排污系数和产污系数中的一项！\n",
//                                sybh, dqswrVO.getYf()));
//                        continue;
//                    }
//                    if (GyUtils.isNull(dqswrVO.getJsjs1())) {
//                        //排污系数时，计算基数不能为空
//                        msg.append(String.format("税源编号为%s(%s月)的大气水计算表导入数据中计算基数不能为空！\n", sybh, dqswrVO.getYf()));
//                        continue;
//                    }
//                    final List<Double> cwxsList = new ArrayList<>();
//                    final List<Double> pwxsList = new ArrayList<>();
//                    List<PwxscjbDTO> pwxscjbDTONewList = pwxscjbDTOList.stream().filter(a -> zspmDm.equals(a.getZspmDm())).collect(Collectors.toList());
//                    if (!GyUtils.isNull(dqswrVO.getZszmDm())) {
//                        pwxscjbDTONewList = pwxscjbDTONewList.stream().filter(a -> dqswrVO.getZszmDm().equals(a.getZszmDm())).collect(Collectors.toList());
//                    }
//                    if (!GyUtils.isNull(pwxscjbDTONewList)) {
//                        for (PwxscjbDTO pwxscjbDTO : pwxscjbDTONewList) {
//                            cwxsList.add(pwxscjbDTO.getCwxs());
//                            pwxsList.add(pwxscjbDTO.getPwxs());
//                            if ((cwxs != 0.0 && cwxs.equals(pwxscjbDTO.getCwxs())) || (pwxs != 0.0 && pwxs.equals(pwxscjbDTO.getPwxs()))) {
//                                dqswrVO.setWrwdwDm(pwxscjbDTO.getWrwdwDm());
//                            }
//                        }
//                    }
//                    if (cwxs != 0.0 && !cwxsList.contains(cwxs)) {
//                        //导入模板填了产污系数，但是与采集的不同
//                        msg.append(String.format("税源编号为%s(%s月)的大气水计算表导入数据中的产污系数填写有误！\n", sybh, dqswrVO.getYf()));
//                        continue;
//                    } else if (pwxs != 0.0 && !pwxsList.contains(pwxs)) {
//                        //导入模板填了排污系数，但是与采集的不同
//                        msg.append(String.format("税源编号为%s(%s)的大气水计算表导入数据中的排污系数填写有误！\n", sybh, dqswrVO.getYf()));
//                        continue;
//                    }
//                    dqswrVO.setPwxscjbDTOList(pwxscjbDTOList);
//                    //以下四项将值置空
//                    dqswrVO.setPfl(null);
//                    dqswrVO.setScldz(null);
//                    dqswrVO.setDyyxyjld(null);
//                    dqswrVO.setZgscldz(null);
//                } else {
//                    //非排污系数将以下三项置空
//                    dqswrVO.setJsjs1(null);
//                    dqswrVO.setCwxs(null);
//                    dqswrVO.setPwxs(null);
//                }
//                dqswrVO.setSyuuid(syuuid);
//                dqswrVO.setPfkmc(syxxDTO.getPfkmc());
//                //排放口名称字段在大气水附表没有，从税源信息表取
//                dqswrVO.setZywrwlbDm(syxxDTO.getZywrwlbDm());
//                dqswrVO.setFzspmDm(syxxDTO.getFzspmDm());
//                dqswrVO.setWrwpfljsffDm(wrwpfljsffDm);
//                //获取税源属期
//                final Map<String, String> sqMap = this.getSsqByYf(yf, skssqq);
//                dqswrVO.setSkssqq(sqMap.get("sqq"));
//                dqswrVO.setSkssqz(sqMap.get("sqz"));
//            }
//            if (msg.length() == 0) {
//                this.drJs(dqssbList, djxh, swjgDm);
//                sbjsVO.setDqswrList(dqssbList);
//            }
//        }
//
//        // 校验固废申报计算信息
//        if (GyUtils.isNotNull(hbssbjsDrReqVO.getGfsbList())) {
//            final List<GfsyxxVO> gfsbList = hbssbjsDrReqVO.getGfsbList();
//            for (int i = 0; i < gfsbList.size(); i++) {
//                final GfsyxxVO gfsyxxVO = gfsbList.get(i);
//                gfsyxxVO.setSsjmxzDm(this.getDmForDr(gfsyxxVO.getSsjmxzDm()));
//                final String zspmDm = this.getDmForDr(gfsyxxVO.getZspmDm());
//                gfsyxxVO.setZspmDm(zspmDm);
//                gfsyxxVO.setZszmDm(this.getDmForDr(gfsyxxVO.getZszmDm()));
//                final String hgbhssybh = gfsyxxVO.getHgbhssybh();
//                String key = hgbhssybh + zspmDm;
//                // 天津存在同一排放口，危险废物，不同子目多条税源
//                if ("101213003".equals(zspmDm) && !GyUtils.isNull(gfsyxxVO.getZszmDm())) {
//                    key = hgbhssybh + zspmDm + gfsyxxVO.getZszmDm();
//                }
//                if (!map.keySet().contains(key)) {
//                    msg.append("已采集的固体废物采集表中没有查询到对应的固体废物计算表导入数据！税源编号：" + hgbhssybh + "！\n");
//                    continue;
//                }
//                final String yf = gfsyxxVO.getYf();
//                if (GyUtils.isNull(yf)) {
//                    msg.append("固体废物导入信息中月份不能为空！\n");
//                    continue;
//                }
//                if (!this.betweenMonth(skssqq, skssqz, yf)) {
//                    msg.append("固体废物导入信息中月份不在税款所属期之间！\n");
//                    continue;
//                }
//                final BigDecimal dqcsl = GyUtils.isNull(gfsyxxVO.getDqcsl())
//                        ? new BigDecimal(0) : new BigDecimal(gfsyxxVO.getDqcsl());
//                final BigDecimal dqccl = GyUtils.isNull(gfsyxxVO.getDqccl())
//                        ? new BigDecimal(0) : new BigDecimal(gfsyxxVO.getDqccl());
//                final BigDecimal dqczl = GyUtils.isNull(gfsyxxVO.getDqczl())
//                        ? new BigDecimal(0) : new BigDecimal(gfsyxxVO.getDqczl());
//                final BigDecimal dqzhlyl = GyUtils.isNull(gfsyxxVO.getDqzhlyl())
//                        ? new BigDecimal(0) : new BigDecimal(gfsyxxVO.getDqzhlyl());
//                if (dqcsl.compareTo(dqccl) < 0) {
//                    msg.append("固体废物导入信息中“本月固体废物的贮存量”应小于等于“本月固体废物的产生量”!\n");
//                    continue;
//                }
//                if (dqcsl.compareTo(dqczl) < 0) {
//                    msg.append("固体废物导入信息中“本月固体废物的处置量”应小于等于“本月固体废物的产生量”!\n");
//                    continue;
//                }
//                if (dqcsl.compareTo(dqzhlyl) < 0) {
//                    msg.append("固体废物导入信息中“本月固体废物的综合利用量”应小于等于“本月固体废物的产生量”!\n");
//                    continue;
//                }
//                final BigDecimal wrwpflBig = dqcsl.subtract(dqccl).subtract(dqczl);
//                final BigDecimal wrwpfl = wrwpflBig.setScale(6);
//                if (wrwpfl.compareTo(BigDecimal.ZERO) < 0) {
//                    msg.append("固体废物导入信息中“污染物排放量”（“本月固体废物的产生量”-“本月固体废物的贮存量”-“本月固体废物的处置量”）值必须大于等于‘0’!\n");
//                    continue;
//                }
//                if (dqzhlyl.compareTo(wrwpfl) > 0) {
//                    msg.append("固体废物导入信息中“固体废物综合利用量”不能超出“污染物排放量”（“本月固体废物的产生量”-“本月固体废物的贮存量”-“本月固体废物的处置量”）!\n");
//                    continue;
//                }
//                if (dqzhlyl.compareTo(BigDecimal.ZERO) == 1) {
//                    if (GyUtils.isNull((gfsyxxVO.getSsjmxzDm()))) {
//                        msg.append("本月固体废物的综合利用量大于零时，减免性质代码！\n");
//                        continue;
//                    }
//                }
//                gfsyxxVO.setWrwpfl(GYCastUtils.cast2Double(wrwpfl));
//                final SyxxDTO syxxDTO = (SyxxDTO) map.get(key);
//                final String zszmDm = syxxDTO.getZszmDm();
//                if (!GyUtils.isNull(zszmDm) && !zszmDm.equals(gfsyxxVO.getZszmDm())) {
//                    msg.append(String.format("税源编号为%s的固废计算表导入数据中的征收子目与税源信息中的征收子目不一致！\n", hgbhssybh));
//                    continue;
//                }
//                final String syuuid = syxxDTO.getSyuuid();
//                gfsyxxVO.setSyuuid(syuuid);
//                gfsyxxVO.setPfkmc(syxxDTO.getPfkmc());//从税源信息中取排放口名称
//                gfsyxxVO.setZywrwlbDm(syxxDTO.getZywrwlbDm());//从税源信息中取污染物类别
//                //获取税源属期
//                final Map<String, String> sqMap = this.getSsqByYf(yf, skssqq);
//                gfsyxxVO.setSkssqq(sqMap.get("sqq"));
//                gfsyxxVO.setSkssqz(sqMap.get("sqz"));
//            }
//            if (msg.length() == 0) {
//                this.drJs(gfsbList, djxh, swjgDm);
//                sbjsVO.setGfxxList(gfsbList);
//            }
//        }
//
//        // 校验噪声申报计算
//        if (GyUtils.isNotNull(hbssbjsDrReqVO.getZssbList())) {
//            final List<ZssyxxVO> zssbList = hbssbjsDrReqVO.getZssbList();
//            for (int i = 0; i < zssbList.size(); i++) {
//                final ZssyxxVO zssyxxVO = zssbList.get(i);
//                zssyxxVO.setZssdDm(this.getDmForDr(zssyxxVO.getZssdDm()));
//                zssyxxVO.setLcyszscb(this.getDmForDr(zssyxxVO.getLcyszscb()));
//                final String sybh = zssyxxVO.getHgbhssybh();
//                final String key = sybh;
//                if (!map.keySet().contains(sybh)) {
//                    msg.append("已采集的噪声采集表中没有查询到对应的噪声计算表导入数据！\n");
//                    continue;
//                }
//                final String yf = zssyxxVO.getYf();
//                if (GyUtils.isNull(yf)) {
//                    msg.append("噪声导入信息中月份不能为空！\n");
//                    continue;
//                }
//                if (GyUtils.isNull(zssyxxVO.getZssdDm())) {
//                    msg.append("噪声导入信息中噪声时段不能为空！\n");
//                    continue;
//                }
//                if (GyUtils.isNull(zssyxxVO.getJcfbs1())) {
//                    msg.append("噪声导入信息中监测分贝数不能为空！\n");
//                    continue;
//                }
//                if (GyUtils.isNull(zssyxxVO.getCbts())) {
//                    msg.append("噪声导入信息中超标不足15天不能为空！\n");
//                    continue;
//                }
//                if (GyUtils.isNull(zssyxxVO.getLcyszscb())) {
//                    msg.append("噪声导入信息中两处以上噪声超标不能为空！\n");
//                    continue;
//                }
//                if (!this.betweenMonth(skssqq, skssqz, yf)) {
//                    msg.append("噪声导入信息中月份不在税款所属期之间！\n");
//                    continue;
//                }
//                final SyxxDTO syxxDTO = (SyxxDTO) map.get(key);
//                final Double bzz = "1".equals(zssyxxVO.getZssdDm())
//                        ? syxxDTO.getBzzzj() : syxxDTO.getBzzyj();
//                final double cbfbs = new BigDecimal(zssyxxVO.getJcfbs1())
//                        .subtract(new BigDecimal(GyUtils.isNull(bzz) ? 0.0 : bzz)).doubleValue();
//                if (cbfbs < 1) {
//                    msg.append(String.format("超标分贝数不能小于1，即监测分贝数必须大于标准限值%s！\n", bzz));
//                    continue;
//                }
//                final String syuuid = syxxDTO.getSyuuid();
//                zssyxxVO.setSyuuid(syuuid);
//                zssyxxVO.setPfkmc(syxxDTO.getPfkmc());
//                zssyxxVO.setZywrwlbDm(syxxDTO.getZywrwlbDm());
//                //获取税源属期
//                final Map<String, String> sqMap = this.getSsqByYf(yf, skssqq);
//                zssyxxVO.setSkssqq(sqMap.get("sqq"));
//                zssyxxVO.setSkssqz(sqMap.get("sqz"));
//            }
//            if (msg.length() == 0) {
//                this.drJs(zssbList, djxh, swjgDm);
//                sbjsVO.setZsxxList(zssbList);
//            }
//        }
//
//        // 校验抽样测算申报计算
//        if (GyUtils.isNotNull(hbssbjsDrReqVO.getCycssbList())) {
//            final List<CycssyxxVO> cycssbList = hbssbjsDrReqVO.getCycssbList();
//            for (int i = 0; i < cycssbList.size(); i++) {
//                final CycssyxxVO cycssyxxVO = cycssbList.get(i);
//                final String hgbhssybh = cycssyxxVO.getHgbhssybh();
//                final String zspmDm = this.getDmForDr(cycssyxxVO.getZspmDm());
//                cycssyxxVO.setZspmDm(zspmDm);
//                cycssyxxVO.setTzzb(this.getDmForDr(cycssyxxVO.getTzzb()));
//                final String cycsKey = hgbhssybh + zspmDm;
//                if (!map.keySet().contains(cycsKey)) {
//                    msg.append("已采集的税源信息采集表中没有查询到对应的抽样测算计算表导入数据！\n");
//                    continue;
//                }
//                final String yf = cycssyxxVO.getYf();
//                if (GyUtils.isNull(yf)) {
//                    msg.append("抽样测算导入信息中月份不能为空！");
//                    continue;
//                }
//                if (!this.betweenMonth(skssqq, skssqz, yf)) {
//                    msg.append("抽样测算导入信息中月份不在税款所属期之间！\n");
//                    continue;
//                }
//                final SyxxDTO syxxDTO = (SyxxDTO) map.get(cycsKey);
//                final String zszmDm = syxxDTO.getZszmDm();
//                if (!GyUtils.isNull(zszmDm) && !zszmDm.equals(cycssyxxVO.getTzzb())) {
//                    msg.append(String.format("税源编号为%s的抽样测算计算表导入数据中的特征指标与税源信息中的征收子目不一致！\n", hgbhssybh));
//                    continue;
//                }
//                cycssyxxVO.setSyuuid(syxxDTO.getSyuuid());
//                cycssyxxVO.setZywrwlbDm(syxxDTO.getZywrwlbDm());
//                cycssyxxVO.setFzspmDm(syxxDTO.getFzspmDm());
//                //获取税源属期
//                final Map<String, String> sqMap = this.getSsqByYf(yf, skssqq);
//                cycssyxxVO.setSkssqq(sqMap.get("sqq"));
//                cycssyxxVO.setSkssqz(sqMap.get("sqz"));
//            }
//            if (msg.length() == 0) {
//                final String msgCy = this.drJs(cycssbList, djxh, swjgDm);
//                if (!GyUtils.isNull(msgCy)) {
//                    msg.append(msgCy);
//                } else {
//                    sbjsVO.setCycssyxxList(cycssbList);
//                }
//            }
//        }
//        // 校验按次申报计算
//        if (GyUtils.isNotNull(hbssbjsDrReqVO.getAcsbList())) {
//            final List<CycssyxxVO> acsbList = hbssbjsDrReqVO.getAcsbList();
//            for (int i = 0; i < acsbList.size(); i++) {
//                final CycssyxxVO cycssyxxVO = acsbList.get(i);
//                cycssyxxVO.setZywrwlbDm(this.getDmForDr(cycssyxxVO.getZywrwlbDm()).split("_")[0]);
//                cycssyxxVO.setFzspmDm(this.getDmForDr(cycssyxxVO.getFzspmDm()));
//                cycssyxxVO.setSsjmxzDm(this.getDmForDr(cycssyxxVO.getSsjmxzDm()));
//                cycssyxxVO.setZspmDm(this.getDmForDr(cycssyxxVO.getZspmDm()));
//                cycssyxxVO.setTzzb(this.getDmForDr(cycssyxxVO.getTzzb()));
//                // 属期为当天
//                cycssyxxVO.setSkssqq(GYCastUtils.cast2Str(new Date()));
//                cycssyxxVO.setSkssqz(GYCastUtils.cast2Str(new Date()));
//                if (GyUtils.isNull(cycssyxxVO.getZywrwlbDm())
//                        || GyUtils.isNull(cycssyxxVO.getZspmDm())
//                        || GyUtils.isNull(cycssyxxVO.getWrdls())) {
//                    msg.append("请输入必录项！\n");
//                    continue;
//                }
//                if (!"W".equals(cycssyxxVO.getZywrwlbDm())
//                        && GyUtils.isNotNull(cycssyxxVO.getFzspmDm())) {
//                    msg.append("污染物种类不为水污染物，请勿选择水污染物种类！\n");
//                    continue;
//                }
//                if ("W".equals(cycssyxxVO.getZywrwlbDm()) && GyUtils.isNull(cycssyxxVO.getFzspmDm())) {
//                    msg.append("污染物种类为水污染物，请选择水污染物种类！\n");
//                    continue;
//                }
//                if (!GyUtils.isNull(cycssyxxVO.getJmse())
//                        && GYCastUtils.cast2Double(cycssyxxVO.getJmse()) > 0
//                        && GyUtils.isNull(cycssyxxVO.getSsjmxzDm())) {
//                    msg.append("减免性质为空，请选择减免性质代码和项目名称！\n");
//                    continue;
//                }
//                if (!GyUtils.isNull(cycssyxxVO.getJmse())
//                        && GYCastUtils.cast2Double(cycssyxxVO.getJmse()) < 0) {
//                    msg.append("减免税额不能小于0！\n");
//                    continue;
//                }
//                if (GYCastUtils.cast2Double(cycssyxxVO.getYjse()) < 0) {
//                    msg.append("已缴税额不能小于0！\n");
//                    continue;
//                }
//            }
//            if (msg.length() == 0) {
//                this.drJs(acsbList, djxh, swjgDm);
//                sbjsVO.setCycssyxxList(acsbList);
//            }
//        }
//        if (msg.length() > 0) {
//            returnMap.put("code", FAIL_CODE);
//            returnMap.put("msg", msg);
//            return returnMap;
//        }
//        msg.append("导入成功!");
//        returnMap.put("sbjsVO", sbjsVO);
//        returnMap.put("code", "1");
//        returnMap.put("msg", msg);
//        return returnMap;
//    }
//
    /**
     * 获取纳税人信用信息
     * @return
     */
    @Override
    public DwnsrxxVO queryNsrxyxx(String djxh) {
        DwnsrxxVO dwnsrxxVO = null;
        ZnsbMhzcQyjbxxmxReqVO vo = new ZnsbMhzcQyjbxxmxReqVO();
        vo.setDjxh(new MockUtils().mock(djxh,"10213410000000135383"));
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> response = nsrxxcxApi.getNsrxxByDjxh(vo);
        log.error("查询纳税人信息basicInfo"+ response);
        if(!GyUtils.isNull(response) && !GyUtils.isNull(response.getData())){
            ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO = response.getData();
            dwnsrxxVO = BeanUtils.toBean(qyjbxxmxResVO.getJbxxmxsj().get(0), DwnsrxxVO.class);
        }
        dwnsrxxVO.setDjxh(MockUtils.mockString("djxh",dwnsrxxVO.getDjxh()));
        return dwnsrxxVO;
    }
//
//    /**
//     * 作废税源
//     *
//     * @param cancelSyxxReqVo
//     * @return
//     */
//    @Override
//    public Map<String, Object> cancelSyxx(CancelSyxxReqVo cancelSyxxReqVo) {
//        Map<String, Object> returnMap = new HashMap<>();
//        returnMap.put("returnCode", "1");
//        returnMap.put("returnMsg", "作废成功");
//        // 调用核心接口（SWZJ.HXZG.SB.HBSCJZFJCXXSYXX）作废税源
//        final String djxh = cancelSyxxReqVo.getDjxh();
//        final String zgswskfjDm = cancelSyxxReqVo.getZgswskfjDm();
//        // 查核心接口判断是否存在有值
//        HXZGSB10748Request queryReq = new HXZGSB10748Request();
//        queryReq.setDjxh(djxh);
//        Map<String, String> expend = new HashMap<>();
//        expend.put("sjjg", zgswskfjDm);
//        expend.put("sjry", SjryUtil.getSjry(zgswskfjDm));
//        HXZGSB10748Response res = null;
//        try {
//            res = Gt3Invoker.invoke("SWZJ.HXZG.SB.QUERYHBSCJYWBW", expend, queryReq, HXZGSB10748Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.QUERYHBSCJYWBW异常：", e);
//            returnMap.put("returnCode", "0");
//            returnMap.put("returnMsg", e.getMessage());
//            return returnMap;
//        }
//        if (GyUtils.isNull(res)
//                || GyUtils.isNull(res.getHjbhssyxxqzcjYwbw().getHbsJcxxVO())
//                || GyUtils.isNull(res.getHjbhssyxxqzcjYwbw().getHbsJcxxVO().getSyxxGrid())
//                || GyUtils.isNull(res.getHjbhssyxxqzcjYwbw().getHbsJcxxVO().getSyxxGrid().getSyxxGridLb())
//                || res.getHjbhssyxxqzcjYwbw().getHbsJcxxVO().getSyxxGrid().getSyxxGridLb().isEmpty()) {
//            returnMap.put("returnCode", "0");
//            returnMap.put("returnMsg", "作废失败,税源信息为空!");
//            return returnMap;
//        }
//        return returnMap;
//    }
//
//
//    @Override
//    public List<AccjxxVO> queryWqacsbxx(String djxh) {
//        List<AccjxxVO> accjxxVOList = new ArrayList<>();
//        final Date date = new Date();
//        final AccjxxQueryVo accjxxQueryVo = new AccjxxQueryVo();
//        accjxxQueryVo.setDjxh(djxh);
//        accjxxQueryVo.setSkssqq(GYCastUtils.cast2Str(date));
//        accjxxQueryVo.setSkssqz(GYCastUtils.cast2Str(date));
//        accjxxQueryVo.setSbsxDm("11");
//        final List<AccjxxVO> accjxxVOListTmp = hbssyxxcjJ3cxApi.QueryWqAcsbxx(accjxxQueryVo);
//        if (accjxxVOListTmp != null && !accjxxVOListTmp.isEmpty()) {
//            accjxxVOList.addAll(accjxxVOListTmp);
//        }
//        return accjxxVOList;
//
//    }
//
//
    /**
     * 根据key 和上级 key
     */
    private List<Map<String, Object>> getChildren(List<Map<String, Object>> list,
                                                  String parentKey, String key, Object currentValue,
                                                  Boolean containsCurrentValue) {
        int length = Thread.currentThread().getStackTrace().length;
        if (length > 200) {
            // TODO 错误编码需要修改
            try {
                throw new Exception("递归次数过多请检查系统参数是否配置错误");
            } catch (Exception e) {
                log.error("系统异常", e);
                e.printStackTrace();
            }
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> map : list) {
            // 上级代码值
            Object parentValue = map.get(parentKey);
            // 上级代码值登录当前代码值
            if (parentValue != null && parentValue.equals(currentValue)) {
                result.add(map);
                // 下级值
                Object childValue = map.get(key);
                // 对下级值进行递归
                List<Map<String, Object>> children = getChildren(list, parentKey, key, childValue, false);
                result.addAll(children);
            }
            // 如果需要包含起始值
            if (containsCurrentValue) {
                Object value = map.get(key);
                if (value != null && value.equals(currentValue)) {
                    result.add(map);
                }
            }
        }
        return result;
    }
//
    /**
     * 获取缓存表DM_DJ_GTFWCLFS，代码取值0301,0302,0303,0399，供页面上综合利用情况下拉使用
     *
     * @return
     */
    private List<Map<String, Object>> loadGtfwclfs() {
        List<Map<String, Object>> gtfwclfsList = new ArrayList<>();
        gtfwclfsList.add(CacheUtils.getTableData("dm_dj_gtfwclfs", "0301"));
        gtfwclfsList.add(CacheUtils.getTableData("dm_dj_gtfwclfs", "0302"));
        gtfwclfsList.add(CacheUtils.getTableData("dm_dj_gtfwclfs", "0303"));
        gtfwclfsList.add(CacheUtils.getTableData("dm_dj_gtfwclfs", "0399"));
        return gtfwclfsList;
    }
//
    /**
     * 获取缓存表DM_GY_JLDW中取值0208、0209、0210、0211代码值，供污染物单位下拉数据
     *
     * @return
     */
    private List<Map<String, Object>> loadJldw() {
        List<Map<String, Object>> jldwList = new ArrayList<>();
        jldwList.add(CacheUtils.getTableData("dm_gy_jldw", "0208"));
        jldwList.add(CacheUtils.getTableData("dm_gy_jldw", "0209"));
        jldwList.add(CacheUtils.getTableData("dm_gy_jldw", "0210"));
        jldwList.add(CacheUtils.getTableData("dm_gy_jldw", "0211"));
        return jldwList;
    }
//
//    /**
//     * 通过缓存表获取DM_SB_ZSSD，过滤代码值3，赋值噪声时段下拉数据
//     *
//     * @return
//     */
//    private List<Map<String, Object>> loadZssd() {
//        List<Map<String, Object>> zssdList = new ArrayList<>();
//        zssdList.add(CacheUtils.getTableData("DM_SB_ZSSD", "1"));
//        zssdList.add(CacheUtils.getTableData("DM_SB_ZSSD", "2"));
//        return zssdList;
//    }
//
    /**
     * 通过缓存表获取DM_GY_ZSPM（征收品目代码），找出征收品目代码值为“101212100，101212200，101212300，101212400，101212900”的征收品目信息供页面中水污物种类加载下拉数据
     */
    private List<Map<String, Object>> loadZspm() {
        List<Map<String, Object>> swrZspmList = new ArrayList<>();
        swrZspmList.add(CacheUtils.getTableData("dm_gy_zspm", "101212100"));
        swrZspmList.add(CacheUtils.getTableData("dm_gy_zspm", "101212200"));
        swrZspmList.add(CacheUtils.getTableData("dm_gy_zspm", "101212300"));
        swrZspmList.add(CacheUtils.getTableData("dm_gy_zspm", "101212400"));
        swrZspmList.add(CacheUtils.getTableData("dm_gy_zspm", "101212900"));
        return swrZspmList;
    }


    /**
     * 获取环境保护税征收品目子目污染当量值（抽样测算）配置信息
     *
     * @param swjgDm
     * @return
     */
    private List<Map<String, Object>> getHbswrdlztzzpzList(String swjgDm) {
        int length = Thread.currentThread().getStackTrace().length;
        if (length > 200) {
            // TODO 错误编码需要修改
//            throw new GoffException("系统错误", "递归次数过多请检查系统参数是否配置错误");
        }
        List<Map<String, Object>> hbswrdlztzzpzList = new ArrayList<>();
//        swjgDm = new MockUtils().mock(swjgDm,"14406813100");
        hbswrdlztzzpzList = CacheUtils.getTableListCacheDataByKey("cs_sb_hbswrdlztzzpz_mc", swjgDm);
        hbswrdlztzzpzList = GYCastUtils.cast2DefKeyList(hbswrdlztzzpzList);//将大写下划线Key转换成标准小写Key
        if (hbswrdlztzzpzList == null || hbswrdlztzzpzList.isEmpty()) {
            // 找上级税务机关代码
            Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg", swjgDm);
            if (GyUtils.isNull(swjgMap) || GyUtils.isNull(swjgMap.get("sjswjgDm"))) {
                return hbswrdlztzzpzList;
            }
            String sjswjgDm = swjgMap.get("sjswjgDm").toString();
            hbswrdlztzzpzList = getHbswrdlztzzpzList(sjswjgDm);
        }
        return hbswrdlztzzpzList;
    }

    /**
     * 查询税源基本信息
     *
     * @param djxh
     * @return
     */
    public HbsJcxxDTO getHbsjcxxcjb(String djxh, String zgswskfjDm) {
        HbsJcxxDTO hbsJcxxDTO = new HbsJcxxDTO();
        final List<SyxxDTO> syxxDTOList = new ArrayList<>();
        // 调用通过微服务调用核心接口（SWZJ.HXZG.SB.QUERYHBSCJYWBW）
        HXZGSB10748Request req = new HXZGSB10748Request();
        req.setDjxh(djxh);
        Map<String, String> expend = new HashMap<>();
        expend.put("sjjg", zgswskfjDm);
        expend.put("sjry", SjryUtil.getSjry(zgswskfjDm));
        HbssyxxcjSaveReqVo res = queryHbscjywbw(djxh);
//        HXZGSB10748Response res = null;
//        try {
//
//            res = Gt3Invoker.invoke("SWZJ.HXZG.SB.QUERYHBSCJYWBW", expend, req, HXZGSB10748Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.QUERYHBSCJYWBW异常：", e);
//        }
        if (GyUtils.isNull(res) || GyUtils.isNull(res.getHbsJcxxVO())) {
            return hbsJcxxDTO;
        }
        final HbsJcxxVO hbsJcxxVO = res.getHbsJcxxVO();
        // 表头信息
        final HbsjcxxcjbVO hbsjcxxcjb = hbsJcxxVO.getHbsjcxxcjb();
        hbsJcxxDTO.setHbsjcxxcjb(BeanUtils.toBean(hbsjcxxcjb, HbsjcxxcjbDTO.class));
        // 税源信息
        if (GyUtils.isNotNull(hbsJcxxVO.getSyxxGridList()) ) {
            List<SyxxVO> syxxGridLb = hbsJcxxVO.getSyxxGridList();
            final List<BccjxxVO> bccjGridLb = res.getBccjList();
            final List<GfcjxxVO> gfcjxxGridLb = res.getGfcjxxList();
            final List<ZscjxxVO> zscjxxGridLb = res.getZscjxxList();
            final List<PwxscjbVO> pwxscjbGridLb = res.getPwxscjbxxList();
            // 通过syuuid与syxxGridList对象进行关联
            for (SyxxVO hbsjcxxcjSyxxb : syxxGridLb) {
                SyxxDTO syxxDTO = BeanUtils.toBean(hbsjcxxcjSyxxb, SyxxDTO.class);
                if (GyUtils.isNotNull(hbsjcxxcjSyxxb.getJd2())
                        && GyUtils.isNotNull(hbsjcxxcjSyxxb.getWd())) {
                    final String jd2 = hbsjcxxcjSyxxb.getJd2();
                    final String wd = hbsjcxxcjSyxxb.getWd();
                    final String[] jdArr = jd2.split("°|′|″");
                    final String[] wdArr = wd.split("°|′|″");
                    final Double jdd = Double.valueOf(jdArr[0]);
                    final Double jdf = Double.valueOf(jdArr[1]);
                    final Double jdm = Double.valueOf(jdArr[2]);
                    final Double wdd = Double.valueOf(wdArr[0]);
                    final Double wdf = Double.valueOf(wdArr[1]);
                    final Double wdm = Double.valueOf(wdArr[2]);
                    syxxDTO.setJd2D(jdd);
                    syxxDTO.setJd2F(jdf);
                    syxxDTO.setJd2M(jdm);
                    syxxDTO.setWdd(wdd);
                    syxxDTO.setWdf(wdf);
                    syxxDTO.setWdm(wdm);
                }
                if (GyUtils.isNotNull(syxxDTO.getSyyxqq()) && GyUtils.isNotNull(syxxDTO.getSyyxqz())) {
                    syxxDTO.setSyyxqq(GYCastUtils.cast2Str(GYCastUtils.cast2Date(syxxDTO.getSyyxqq())));
                    syxxDTO.setSyyxqz(GYCastUtils.cast2Str(GYCastUtils.cast2Date(syxxDTO.getSyyxqz())));
                }
                // 大气
                if ("W".equals(syxxDTO.getZywrwlbDm()) || "A".equals(syxxDTO.getZywrwlbDm())) {
                    Iterator<BccjxxVO> hbsdqswrjcxxcjbIterator = bccjGridLb.iterator();
                    while (hbsdqswrjcxxcjbIterator.hasNext()) {
                        BccjxxVO hbsdqswrjcxxcjb = hbsdqswrjcxxcjbIterator.next();
                        if (syxxDTO.getSyuuid().equals(hbsdqswrjcxxcjb.getSyuuid())) {
                            BeanUtils.copyBean(hbsdqswrjcxxcjb, syxxDTO);
                            hbsdqswrjcxxcjbIterator.remove();
                            break;
                        }
                    }
                    // 排污
                    for (PwxscjbVO pwxscjb : pwxscjbGridLb) {
                        if (GyUtils.isNotNull(syxxDTO.getZspmDm())
                                && syxxDTO.getZspmDm().equals(pwxscjb.getZspmDm())) {
                            syxxDTO.setCwxs(pwxscjb.getCwxs());
                            syxxDTO.setJsjsdw(pwxscjb.getJsjsdw());
                            syxxDTO.setWrwdwDm(pwxscjb.getWrwdwDm());
                            syxxDTO.setPwxs(pwxscjb.getPwxs());
                            syxxDTO.setPwxsuuid(pwxscjb.getPwxsuuid());
                        }
                    }
                }
                //固废
                if ("S".equals(syxxDTO.getZywrwlbDm())) {
                    Iterator<GfcjxxVO> gfjcxxcjbIterator = gfcjxxGridLb.iterator();
                    while (gfjcxxcjbIterator.hasNext()) {
                        GfcjxxVO gfjcxxcjb = gfjcxxcjbIterator.next();
                        if (syxxDTO.getSyuuid().equals(gfjcxxcjb.getSyuuid())) {
                            BeanUtils.copyBean(gfjcxxcjb, syxxDTO);
                            gfjcxxcjbIterator.remove();
                            break;
                        }
                    }
                }
                if ("N".equals(syxxDTO.getZywrwlbDm())) {
                    // 噪声
                    Iterator<ZscjxxVO> zsjcxxcjbIterator = zscjxxGridLb.iterator();
                    while (zsjcxxcjbIterator.hasNext()) {
                        ZscjxxVO zsjcxxcjb = zsjcxxcjbIterator.next();
                        if (GyUtils.isNotNull(syxxDTO.getSyuuid())
                                && syxxDTO.getSyuuid().equals(zsjcxxcjb.getSyuuid())) {
                            BeanUtils.copyBean(zsjcxxcjb, syxxDTO);
                            zsjcxxcjbIterator.remove();
                            break;
                        }
                    }
                }
                syxxDTOList.add(syxxDTO);
            }
            hbsJcxxDTO.setSyxxGridList(syxxDTOList);
        }
        return hbsJcxxDTO;
    }

//    /**
//     * 导出申报计算信息
//     *
//     * @param sbjsVO
//     * @return
//     */
//    @Override
//    public Map<String, Object> printSbjsxx(SbjsVO sbjsVO) {
//        Map<String, Object> map = new HashMap<>();
//        if (GyUtils.isNotNull(sbjsVO.getDqswrList())) {
//            final List<DqswrVO> dqswrList = sbjsVO.getDqswrList();
//            for (DqswrVO vo : dqswrList) {
//                //处理大气水导出下拉显示
//                vo.setSsjmxzDm(this.getMcForExport(this.getDmForDr(vo.getSsjmxzDm()), "DM_GY_SSJMXZ", "SSJMXZMC"));
//                vo.setZspmDm(this.getMcForExport(this.getDmForDr(vo.getZspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//                vo.setZszmDm(this.getMcForExport(this.getDmForDr(vo.getZszmDm()), "DM_GY_ZSZM", "ZSZMMC"));
//            }
//            map.put("dqswrSbxxGrid", dqswrList);
//        }
//        if (GyUtils.isNotNull(sbjsVO.getGfxxList())) {
//            final List<GfsyxxVO> gfxxList = sbjsVO.getGfxxList();
//            for (GfsyxxVO vo : gfxxList) {
//                //处理固废导出下拉显示
//                vo.setSsjmxzDm(this.getMcForExport(this.getDmForDr(vo.getSsjmxzDm()), "DM_GY_SSJMXZ", "SSJMXZMC"));
//                vo.setZspmDm(this.getMcForExport(this.getDmForDr(vo.getZspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//                vo.setZszmDm(this.getMcForExport(this.getDmForDr(vo.getZszmDm()), "DM_GY_ZSZM", "ZSZMMC"));
//            }
//            map.put("gfSbxxGrid", gfxxList);
//        }
//        if (GyUtils.isNotNull(sbjsVO.getZsxxList())) {
//            final List<ZssyxxVO> zsxxList = sbjsVO.getZsxxList();
//            for (ZssyxxVO vo : zsxxList) {
//                //处理噪声导出下拉显示
//                final String lcys = this.getDmForDr(vo.getLcyszscb());
//                final String zssdDm = this.getDmForDr(vo.getZssdDm());
//                if ("Y".equals(lcys)) {
//                    vo.setLcyszscb(lcys + "|是");
//                } else if ("N".equals(lcys)) {
//                    vo.setLcyszscb(lcys + "|否");
//                }
//                if ("1".equals(zssdDm)) {
//                    vo.setZssdDm(zssdDm + "|昼");
//                } else if ("2".equals(zssdDm)) {
//                    vo.setZssdDm(zssdDm + "|夜");
//                }
//            }
//            map.put("zsSbxxGrid", zsxxList);
//        }
//        if (GyUtils.isNotNull(sbjsVO.getCycssyxxList()) && "N".equals(sbjsVO.getAcsb())) {
//            final List<CycssyxxVO> cycssyxxList = sbjsVO.getCycssyxxList();
//            for (CycssyxxVO vo : cycssyxxList) {
//                vo.setZspmDm(this.getMcForExport(this.getDmForDr(vo.getZspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//                vo.setTzzb(this.getMcForExport(this.getDmForDr(vo.getTzzb()), "DM_GY_ZSZM", "ZSZMMC"));
//            }
//            map.put("cycsSbxxGrid", cycssyxxList);
//        }
//        if (GyUtils.isNotNull(sbjsVO.getCycssyxxList()) && "Y".equals(sbjsVO.getAcsb())) {
//            final List<CycssyxxVO> cycssyxxList = sbjsVO.getCycssyxxList();
//            for (CycssyxxVO vo : cycssyxxList) {
//                vo.setZywrwlbDm(this.getMcForExport(this.getDmForDr(vo.getZywrwlbDm()), "DM_DJ_ZYWRWLB", "ZYWRWLBMC"));
//                vo.setFzspmDm(this.getMcForExport(this.getDmForDr(vo.getFzspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//                vo.setZspmDm(this.getMcForExport(this.getDmForDr(vo.getZspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//                vo.setTzzb(this.getMcForExport(this.getDmForDr(vo.getTzzb()), "DM_GY_ZSZM", "ZSZMMC"));
//                vo.setSsjmxzDm(this.getMcForExport(this.getDmForDr(vo.getSsjmxzDm()), "DM_GY_SSJMXZ", "SSJMXZMC"));
//            }
//            map.put("acSbxxGrid", cycssyxxList);
//        }
//        return map;
//    }


    /**
     * 获取税源信息列表
     *
     * @param getSyxxGridVO
     * @return
     */
    @Override
    public SbjsVO getSyxxGrid(GetSyxxGridVO getSyxxGridVO) {
        final SbjsVO sbjsVO
                = new SbjsVO();
        final String djxh = getSyxxGridVO.getDjxh();
        final String sbbz = getSyxxGridVO.getSbbz();
        final String skssqq = getSyxxGridVO.getSkssqq();
        final String skssqz = getSyxxGridVO.getSkssqz();
        final String zgswskfjDm = getSyxxGridVO.getZgswskfjDm();
        // 获取环保税登记税源基础信息
        InitcjPageReqVO initcjPageReqVO = new InitcjPageReqVO();
        initcjPageReqVO.setDjxh(djxh);
        initcjPageReqVO.setSbbz(sbbz);
        initcjPageReqVO.setSkssqq(skssqq);
        initcjPageReqVO.setSkssqz(skssqz);
        HbsJcxxVO hbsJcxxDTO = this.initcjPage(initcjPageReqVO);
        final List<SyxxVO> syxxGridList = hbsJcxxDTO.getSyxxGridList();
        List<DqswrDTO> dqswrList = new ArrayList<>();
        List<GfsyxxDTO> gfxxList = new ArrayList<>();
        List<ZssyxxDTO> zsxxList = new ArrayList<>();
        List<CycssyxxDTO> cycssyxxList = new ArrayList<>();
        // 添加前台已删除的信息
        if (GyUtils.isNotNull(getSyxxGridVO.getDelete())
                && GyUtils.isNotNull(getSyxxGridVO.getDelete().getSbjsDTO())) {
            final SbjsDTO sbjsDTO2 = getSyxxGridVO.getDelete().getSbjsDTO();
            if (GyUtils.isNotNull(sbjsDTO2.getDqswrList())) {
                for (DqswrDTO dqswrDTO : sbjsDTO2.getDqswrList()) {
                    // 置空历史数据
                    dqswrDTO.setUuid(null);
                    dqswrDTO.setPfl(0.0);
                    dqswrDTO.setDyyxyjld(0.0);
                    dqswrDTO.setScldz(0.0);
                    dqswrDTO.setZgscldz(0.0);
                    dqswrDTO.setJsjs1(0.0);
                    dqswrDTO.setWrwpfl(0.0);
                    dqswrDTO.setWrdls(0.0);
                    dqswrDTO.setSsjmxzDm(null);
                    dqswrDTO.setSwsxDm(null);
                    dqswrDTO.setJmfd(0.0);
                    dqswrDTO.setJmse(0.0);
                    dqswrDTO.setYjse(0.0);
                    dqswrDTO.setYnse(0.0);
                    dqswrDTO.setYbtse(0.0);
                    dqswrList.add(dqswrDTO);
                }
            }
            if (GyUtils.isNotNull(sbjsDTO2.getGfxxList())) {
                for (GfsyxxDTO gfsyxxDTO : sbjsDTO2.getGfxxList()) {
                    // 置空历史数据
                    gfsyxxDTO.setUuid(null);
                    gfsyxxDTO.setDqccl(0.0);
                    gfsyxxDTO.setDqcsl(0.0);
                    gfsyxxDTO.setDqczl(0.0);
                    gfsyxxDTO.setDqzhlyl(0.0);
                    gfsyxxDTO.setWrwpfl(0.0);
                    gfsyxxDTO.setSsjmxzDm(null);
                    gfsyxxDTO.setSwsxDm(null);
                    gfsyxxDTO.setJmse(0.0);
                    gfsyxxDTO.setYjse(0.0);
                    gfsyxxDTO.setYnse(0.0);
                    gfsyxxDTO.setYbtse(0.0);
                    gfxxList.add(gfsyxxDTO);

                }
            }
            if (GyUtils.isNotNull(sbjsDTO2.getZsxxList())) {
                for (ZssyxxDTO zssyxxDTO : sbjsDTO2.getZsxxList()) {
                    // 置空历史数据
                    zssyxxDTO.setUuid(null);
                    zssyxxDTO.setJcfbs1(0.0);
                    zssyxxDTO.setCbts(null);
                    zssyxxDTO.setLcyszscb(null);
                    zssyxxDTO.setSl1(0.0);
                    zssyxxDTO.setYnse(0.0);
                    zssyxxDTO.setYjse(0);
                    zssyxxDTO.setYnse(0.0);
                    zssyxxDTO.setYbtse(0.0);
                    zsxxList.add(zssyxxDTO);
                }
            }
            if (GyUtils.isNotNull(sbjsDTO2.getCycssyxxList())) {
                for (CycssyxxDTO cycssyxxDTO : sbjsDTO2.getCycssyxxList()) {
                    // 置空历史数据
                    cycssyxxDTO.setUuid(null);
                    cycssyxxDTO.setZbz(0.0);
                    cycssyxxDTO.setWrdls(0.0);
                    cycssyxxDTO.setYnse(0.0);
                    cycssyxxDTO.setYjse(0.0);
                    cycssyxxDTO.setYnse(0.0);
                    cycssyxxDTO.setYbtse(0.0);
                    cycssyxxList.add(cycssyxxDTO);
                }
            }
        }
        // 匹配缺少字段
        for (SyxxVO syxxDTO : syxxGridList) {
            if (GyUtils.isNotNull(dqswrList)) {
                for (DqswrDTO dqswrDTO : dqswrList) {
                    if (syxxDTO.getSyuuid().equals(dqswrDTO.getSyuuid())) {
                        BeanUtils.copyBean(syxxDTO, dqswrDTO);
                        // 如果是排污系数计算方法的置空排污系数等字段
                        if ("4".equals(dqswrDTO.getWrwpfljsffDm())) {
                            dqswrDTO.setPwxs(null);
                            dqswrDTO.setCwxs(null);
                        }
                    }
                }
            }
            if (GyUtils.isNotNull(gfxxList)) {
                for (GfsyxxDTO gfsyxxDTO : gfxxList) {
                    if (syxxDTO.getSyuuid().equals(gfsyxxDTO.getSyuuid())) {
                        BeanUtils.copyBean(syxxDTO, gfsyxxDTO);
                    }
                }
            }
            if (GyUtils.isNotNull(zsxxList)) {
                for (ZssyxxDTO zssyxxDTO : zsxxList) {
                    if (syxxDTO.getSyuuid().equals(zssyxxDTO.getSyuuid())) {
                        zssyxxDTO.setPfkbh(syxxDTO.getPfkbh());
                        zssyxxDTO.setPfkmc(syxxDTO.getPfkmc());
                        zssyxxDTO.setBzzyj(syxxDTO.getBzzyj());
                        zssyxxDTO.setBzzzj(syxxDTO.getBzzzj());
                        zssyxxDTO.setSyyxqq(syxxDTO.getSyyxqq());
                        zssyxxDTO.setSyyxqz(syxxDTO.getSyyxqz());
                    }
                }
            }
            if (GyUtils.isNotNull(cycssyxxList)) {
                for (CycssyxxDTO cycssyxxDTO : cycssyxxList) {
                    if (syxxDTO.getSyuuid().equals(cycssyxxDTO.getSyuuid())) {
                        BeanUtils.copyBean(syxxDTO, cycssyxxDTO);
                    }
                }
            }
        }
        final List<DqswrVO> dqswrVOList = BeanUtils.toBean(dqswrList, DqswrVO.class);
        if (GyUtils.isNotNull(dqswrVOList)) {
            this.drJs(dqswrVOList, djxh, zgswskfjDm);
            // 防止0申报报错
            dqswrVOList.forEach(dqswrVO -> {
                dqswrVO.setWrdls(0.0);
                dqswrVO.setYnse(0.0);
                dqswrVO.setJmse(0.0);
                dqswrVO.setYf(dqswrVO.getYf().trim());
                dqswrVO.setSbbz("N");
                dqswrVO.setViewuuid(UUID.randomUUID().toString().replaceAll("-", ""));
            });
        }
        final List<GfsyxxVO> gfsyxxVOList = BeanUtils.toBean(gfxxList, GfsyxxVO.class);
        if (GyUtils.isNotNull(gfsyxxVOList)) {
            this.drJs(gfsyxxVOList, djxh, zgswskfjDm);
            // 防止0申报报错
            gfsyxxVOList.forEach(gfsyxxVO -> {
                gfsyxxVO.setYnse(0.0);
                gfsyxxVO.setJmse(0.0);
                gfsyxxVO.setWrwpfl(0.0);
                gfsyxxVO.setYf(gfsyxxVO.getYf().trim());
                gfsyxxVO.setSbbz("N");
                gfsyxxVO.setViewuuid(UUID.randomUUID().toString().replaceAll("-", ""));
            });
        }
        final List<ZssyxxVO> zssyxxVOList = BeanUtils.toBean(zsxxList, ZssyxxVO.class);
        if (GyUtils.isNotNull(zssyxxVOList)) {
            // 防止0申报报错
            zssyxxVOList.forEach(zssyxxVO -> {
                zssyxxVO.setYnse(0.0);
                zssyxxVO.setJmse(0.0);
                zssyxxVO.setYf(zssyxxVO.getYf().trim());
                zssyxxVO.setSbbz("N");
                zssyxxVO.setViewuuid(UUID.randomUUID().toString().replaceAll("-", ""));
            });
        }
        final List<CycssyxxVO> cycssyxxVOList = BeanUtils.toBean(cycssyxxList, CycssyxxVO.class);
        if (GyUtils.isNotNull(cycssyxxVOList)) {
            this.drJs(cycssyxxVOList, djxh, zgswskfjDm);
            // 防止0申报报错
            cycssyxxVOList.forEach(cycssyxxVO -> {
                cycssyxxVO.setYnse(0.0);
                cycssyxxVO.setJmse(0.0);
                cycssyxxVO.setYf(cycssyxxVO.getYf().trim());
                cycssyxxVO.setSbbz("N");
                cycssyxxVO.setViewuuid(UUID.randomUUID().toString().replaceAll("-", ""));
            });
        }
        sbjsVO.setDqswrList(dqswrVOList.stream()
                .sorted(Comparator.comparing(DqswrVO::getZywrwlbDm)
                        .thenComparing(DqswrVO::getHgbhssybh).thenComparing(DqswrVO::getZspmDm)
                        .thenComparing(DqswrVO::getYf)).collect(Collectors.toList()));
        sbjsVO.setGfxxList(gfsyxxVOList.stream()
                .sorted(Comparator.comparing(GfsyxxVO::getHgbhssybh)
                        .thenComparing(GfsyxxVO::getZspmDm)
                        .thenComparing(GfsyxxVO::getYf)).collect(Collectors.toList()));
        sbjsVO.setZsxxList(zssyxxVOList.stream()
                .sorted(Comparator.comparing(ZssyxxVO::getHgbhssybh)
                        .thenComparing(ZssyxxVO::getYf)).collect(Collectors.toList()));
        sbjsVO.setCycssyxxList(cycssyxxVOList.stream()
                .sorted(Comparator.comparing(CycssyxxVO::getHgbhssybh)
                        .thenComparing(CycssyxxVO::getZspmDm)
                        .thenComparing(CycssyxxVO::getYf)).collect(Collectors.toList()));
        return sbjsVO;
    }
//
//    /**
//     * 获取申报计算下载模板的初始化数据
//     *
//     * @param djxh
//     * @param swjgDm
//     * @param resMap
//     */
//    @Override
//    public void downLoadSbjsDrmb(String djxh, String swjgDm, Map<String, Object> resMap) {
//        // 获取属期
//        final Map<String, Object> skssq = this.getSkssq();
//        final Calendar skssqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(skssq.get("skssqq")));
//        final Calendar skssqz = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(skssq.get("skssqz")));
//        final List<DqsSbjscj> dqsSbjsList = new ArrayList<>();//大气水申报计算
//        final List<GfSbjscj> gfSbjsList = new ArrayList<>();//固废申报计算
//        final List<ZsSbjscj> zsSbjsList = new ArrayList<>();//噪声申报计算
//        final List<CycsSbjscj> cycsSbjscjList = new ArrayList<>();//抽样测算申报计算
//        final Map<String, HbsjcxxcjSyxxb> syxxMap = new HashMap<>();//以syuuid为key存下所有税源信息的map
//
//        // 调用通过微服务调用核心接口（SWZJ.HXZG.SB.QUERYHBSCJYWBW）
//        HXZGSB10748Request req = new HXZGSB10748Request();
//        req.setDjxh(djxh);
//        Map<String, String> expend = new HashMap<>();
//        expend.put("sjjg", swjgDm);
//        expend.put("sjry", SjryUtil.getSjry(swjgDm));
//        HXZGSB10748Response res = null;
//        try {
//            res = Gt3Invoker.invoke("SWZJ.HXZG.SB.QUERYHBSCJYWBW", expend, req, HXZGSB10748Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.QUERYHBSCJYWBW异常：", e);
//            return;
//        }
//        //基础信息表数据
//        final HjbhssyxxqzcjYwbw hjbhssyxxqzcjYwbw = res.getHjbhssyxxqzcjYwbw();
//        if (!GyUtils.isNull(hjbhssyxxqzcjYwbw)
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsJcxxVO())
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsJcxxVO().getSyxxGrid())
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsJcxxVO().getSyxxGrid().getSyxxGridLb())) {
//            for (HbsjcxxcjSyxxb hbsjcxxcjSyxxb : hjbhssyxxqzcjYwbw.getHbsJcxxVO().getSyxxGrid().getSyxxGridLb()) {
//                syxxMap.put(hbsjcxxcjSyxxb.getSyuuid(), hbsjcxxcjSyxxb);
//                final String jsff = hbsjcxxcjSyxxb.getWrwpfljsffDm();
//                final Calendar syyxqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(hbsjcxxcjSyxxb.getSyyxqq()));
//                final Calendar syyxqz = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(hbsjcxxcjSyxxb.getSyyxqz()));
//                final Map<String, Object> syxxbMap = BeanUtils.BeanToMap(hbsjcxxcjSyxxb);
//                //税款所属期税源有效期有交叉则需要带出交叉的月份
//                final boolean sqFlag = !(skssqq.after(syyxqz) || skssqz.before(syyxqq));
//                //计算方法为“排污系数”和“物料衡算”的大气水税源信息不用在附表采集
//                if ("W,A".contains(hbsjcxxcjSyxxb.getZywrwlbDm()) && "3,4".contains(jsff) && sqFlag) {
//                    this.filterPhZszm(syxxbMap);
//                    final List<Map<String, Object>> dqsMaps
//                            = this.fillSbjsDrmbyf(skssqq, skssqz, syyxqq, syyxqz, syxxbMap);
//                    final List<DqsSbjscj> dqsList = new ArrayList<>();
//                    for (Map<String, Object> dqsMap : dqsMaps) {
//                        try {
//                            final DqsSbjscj dqsSbjscj = CxsGyUtils.mapToBean(dqsMap, DqsSbjscj.class);
//                            dqsList.add(dqsSbjscj);
//                        } catch (Exception e) {
//                            log.info("导入模板预填税源信息类转换异常" + e.getMessage());
//                        }
//                    }
//                    if (!GyUtils.isNull(hbsjcxxcjSyxxb.getPfkmc()) && !GyUtils.isNull(dqsList)) {
//                        dqsList.forEach(a -> a.setPfkmc(hbsjcxxcjSyxxb.getPfkmc()));
//                    }
//                    dqsSbjsList.addAll(dqsList);
//                } else if ("5".equals(jsff) && sqFlag) {
//                    //抽样测算填充数据
//                    final List<Map<String, Object>> cycsMaps
//                            = this.fillSbjsDrmbyf(skssqq, skssqz, syyxqq, syyxqz, syxxbMap);
//                    final List<CycsSbjscj> cycsList = new ArrayList<>();
//                    for (Map<String, Object> cycsMap : cycsMaps) {
//                        try {
//                            final CycsSbjscj cycsSbjscj = CxsGyUtils.mapToBean(cycsMap, CycsSbjscj.class);
//                            cycsList.add(cycsSbjscj);
//                        } catch (Exception e) {
//                            log.info("导入模板预填税源信息类转换异常" + e.getMessage());
//                        }
//                    }
//                    if (!GyUtils.isNull(hbsjcxxcjSyxxb.getPfkmc()) && !GyUtils.isNull(cycsList)) {
//                        cycsList.forEach(a -> a.setPfkmc(hbsjcxxcjSyxxb.getPfkmc()));
//                    }
//                    cycsSbjscjList.addAll(cycsList);
//                }
//            }
//        }
//        if (!GyUtils.isNull(cycsSbjscjList)) {
//            resMap.put("cycsSbxxGrid", cycsSbjscjList);
//        }
//
//        //大气水采集附表
//        if (!GyUtils.isNull(hjbhssyxxqzcjYwbw)
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsdqswrVO())
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsdqswrVO().getBccjGrid())
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsdqswrVO().getBccjGrid().getBccjGridLb())) {
//            for (Hbsdqswrjcxxcjb dqscjfb : hjbhssyxxqzcjYwbw.getHbsdqswrVO().getBccjGrid().getBccjGridLb()) {
//                final HbsjcxxcjSyxxb syxx = syxxMap.get(dqscjfb.getSyuuid());
//                final Calendar syyxqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(syxx.getSyyxqq()));
//                final Calendar syyxqz = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(syxx.getSyyxqz()));
//                if (!(skssqq.after(syyxqz) || skssqz.before(syyxqq))) {
//                    final Map<String, Object> dqssyMap = BeanUtils.BeanToMap(dqscjfb);
//                    this.filterPhZszm(dqssyMap);
//                    final List<Map<String, Object>> dqsfbMaps
//                            = this.fillSbjsDrmbyf(skssqq, skssqz, syyxqq, syyxqz, dqssyMap);
//                    final List<DqsSbjscj> list = new ArrayList<>();
//                    for (Map<String, Object> dqsMap : dqsfbMaps) {
//                        try {
//                            final DqsSbjscj dqsSbjscj = CxsGyUtils.mapToBean(dqsMap, DqsSbjscj.class);
//                            list.add(dqsSbjscj);
//                        } catch (Exception e) {
//                            log.info("导入模板预填税源信息类转换异常" + e.getMessage());
//                        }
//                    }
//                    if (!GyUtils.isNull(syxx.getPfkmc()) && !GyUtils.isNull(list)) {
//                        list.forEach(a -> a.setPfkmc(syxx.getPfkmc()));
//                    }
//                    dqsSbjsList.addAll(list);
//                }
//            }
//        }
//        if (!GyUtils.isNull(dqsSbjsList)) {
//            resMap.put("dqswrSbxxGrid", dqsSbjsList);
//        }
//
//        //固废采集附表
//        if (!GyUtils.isNull(hjbhssyxxqzcjYwbw)
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsgfVO())
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsgfVO().getGfcjxxGrid())
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbsgfVO().getGfcjxxGrid().getGfcjxxGridLb())) {
//            for (Gfjcxxcjb gfjcxxcjb : hjbhssyxxqzcjYwbw.getHbsgfVO().getGfcjxxGrid().getGfcjxxGridLb()) {
//                final HbsjcxxcjSyxxb syxx = syxxMap.get(gfjcxxcjb.getSyuuid());
//                final Calendar syyxqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(syxx.getSyyxqq()));
//                final Calendar syyxqz = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(syxx.getSyyxqz()));
//                if (!(skssqq.after(syyxqz) || skssqz.before(syyxqq))) {
//                    final List<Map<String, Object>> gffbMaps
//                            = this.fillSbjsDrmbyf(skssqq, skssqz, syyxqq, syyxqz, BeanUtils.BeanToMap(gfjcxxcjb));
//                    final List<GfSbjscj> list = new ArrayList<>();
//                    for (Map<String, Object> gffbMap : gffbMaps) {
//                        try {
//                            final GfSbjscj gfSbjscj = CxsGyUtils.mapToBean(gffbMap, GfSbjscj.class);
//                            list.add(gfSbjscj);
//                        } catch (Exception e) {
//                            log.info("导入模板预填税源信息类转换异常" + e.getMessage());
//                        }
//                    }
//                    if (!GyUtils.isNull(syxx.getPfkmc()) && !GyUtils.isNull(list)) {
//                        list.forEach(a -> a.setPfkmc(syxx.getPfkmc()));
//                    }
//                    gfSbjsList.addAll(list);
//                }
//            }
//        }
//        if (!GyUtils.isNull(gfSbjsList)) {
//            resMap.put("gfSbxxGrid", gfSbjsList);
//        }
//
//        //噪声采集附表
//        if (!GyUtils.isNull(hjbhssyxxqzcjYwbw)
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbszsVO())
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbszsVO().getZscjxxGrid())
//                && !GyUtils.isNull(hjbhssyxxqzcjYwbw.getHbszsVO().getZscjxxGrid().getZscjxxGridLb())) {
//            for (Zsjcxxcjb zsjcxxcjb : hjbhssyxxqzcjYwbw.getHbszsVO().getZscjxxGrid().getZscjxxGridLb()) {
//                final HbsjcxxcjSyxxb syxx = syxxMap.get(zsjcxxcjb.getSyuuid());
//                final Calendar syyxqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(syxx.getSyyxqq()));
//                final Calendar syyxqz = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(syxx.getSyyxqz()));
//                if (!(skssqq.after(syyxqz) || skssqz.before(syyxqq))) {
//                    final List<Map<String, Object>> zsfbMaps
//                            = this.fillSbjsDrmbyf(skssqq, skssqz, syyxqq, syyxqz, BeanUtils.BeanToMap(zsjcxxcjb));
//                    final List<ZsSbjscj> list = new ArrayList<>();
//                    for (Map<String, Object> zsfbMap : zsfbMaps) {
//                        try {
//                            final ZsSbjscj zsSbjscj = CxsGyUtils.mapToBean(zsfbMap, ZsSbjscj.class);
//                            list.add(zsSbjscj);
//                        } catch (Exception e) {
//                            log.info("导入模板预填税源信息类转换异常" + e.getMessage());
//                        }
//                    }
//                    if (!GyUtils.isNull(syxx.getPfkmc()) && !GyUtils.isNull(list)) {
//                        list.forEach(a -> a.setPfkmc(syxx.getPfkmc()));
//                    }
//                    zsSbjsList.addAll(list);
//                }
//            }
//        }
//        if (!GyUtils.isNull(zsSbjsList)) {
//            resMap.put("zsSbxxGrid", zsSbjsList);
//        }
//    }
//
//    /**
//     * 查询申报标志和申报计算采集标志
//     *
//     * @param querySbbzVo
//     * @return
//     */
//    @Override
//    public List<HbsSbbzDTO> querySbbzBySyuuid(QuerySbbzVo querySbbzVo) {
//        List<HbsSbbzDTO> hbsSbbzDTOList = new ArrayList<>();
//        ReStrVO reStrVO = hbssyxxcjJ3cxApi.QuerySbbzBySyuuid(querySbbzVo);
//        final List<String> dqsSyuuidList = querySbbzVo.getDqsSyuuidList();
//        final List<String> gfSyuuidList = querySbbzVo.getGfSyuuidList();
//        final List<String> zsSyuuidList = querySbbzVo.getZsSyuuidList();
//        final List<String> cycsSyuuidList = querySbbzVo.getCycsSyuuidList();
//        if (!GyUtils.isNull(dqsSyuuidList)) {
//            for (String syuuid : dqsSyuuidList) {
//                final HbsSbbzDTO hbsSbbzDTO = new HbsSbbzDTO();
//                hbsSbbzDTO.setSyuuid(syuuid);
//                if (GyUtils.isNull(reStrVO.getDqsReStr())) {
//                    // 未申报，且没有保存了申报计算信息
//                    hbsSbbzDTO.setSbbz("");
//                } else {
//                    for (DqsReStrVO dqsReStrVO : reStrVO.getDqsReStr()) {
//                        if (syuuid.equals(dqsReStrVO.getSyuuid())) {
//                            // 未申报,但是保存了申报计算信息
//                            if (GyUtils.isNull(dqsReStrVO.getCxstysbuuid())) {
//                                hbsSbbzDTO.setSbbz("sbjs");
//                            } else {
//                                // 已申报
//                                hbsSbbzDTO.setSbbz("sbjs,sbbz");
//                            }
//                        }
//                    }
//                }
//                hbsSbbzDTOList.add(hbsSbbzDTO);
//            }
//        }
//        if (!GyUtils.isNull(gfSyuuidList)) {
//            for (String syuuid : gfSyuuidList) {
//                final HbsSbbzDTO hbsSbbzDTO = new HbsSbbzDTO();
//                hbsSbbzDTO.setSyuuid(syuuid);
//                if (GyUtils.isNull(reStrVO.getGfReStr())) {
//                    // 未申报，且没有保存了申报计算信息
//                    hbsSbbzDTO.setSbbz("");
//                } else {
//                    for (GfReStrVO gfReStrVO : reStrVO.getGfReStr()) {
//                        if (syuuid.equals(gfReStrVO.getSyuuid())) {
//                            // 未申报,但是保存了申报计算信息
//                            if (GyUtils.isNull(gfReStrVO.getCxstysbuuid())) {
//                                hbsSbbzDTO.setSbbz("sbjs");
//                            } else {
//                                // 已申报
//                                hbsSbbzDTO.setSbbz("sbjs,sbbz");
//                            }
//                        }
//                    }
//                }
//                hbsSbbzDTOList.add(hbsSbbzDTO);
//            }
//        }
//        if (!GyUtils.isNull(zsSyuuidList)) {
//            for (String syuuid : zsSyuuidList) {
//                final HbsSbbzDTO hbsSbbzDTO = new HbsSbbzDTO();
//                hbsSbbzDTO.setSyuuid(syuuid);
//                if (GyUtils.isNull(reStrVO.getZsReStr())) {
//                    // 未申报，且没有保存了申报计算信息
//                    hbsSbbzDTO.setSbbz("");
//                } else {
//                    for (ZsReStrVO zsReStrVO : reStrVO.getZsReStr()) {
//                        if (syuuid.equals(zsReStrVO.getSyuuid())) {
//                            // 未申报,但是保存了申报计算信息
//                            if (GyUtils.isNull(zsReStrVO.getCxstysbuuid())) {
//                                hbsSbbzDTO.setSbbz("sbjs");
//                            } else {
//                                // 已申报
//                                hbsSbbzDTO.setSbbz("sbjs,sbbz");
//                            }
//                        }
//                    }
//                }
//                hbsSbbzDTOList.add(hbsSbbzDTO);
//            }
//        }
//        if (!GyUtils.isNull(cycsSyuuidList)) {
//            for (String syuuid : cycsSyuuidList) {
//                final HbsSbbzDTO hbsSbbzDTO = new HbsSbbzDTO();
//                hbsSbbzDTO.setSyuuid(syuuid);
//                if (GyUtils.isNull(reStrVO.getCycsReStr())) {
//                    // 未申报，且没有保存了申报计算信息
//                    hbsSbbzDTO.setSbbz("");
//                } else {
//                    for (CycsReStrVO cycsReStrVO : reStrVO.getCycsReStr()) {
//                        if (syuuid.equals(cycsReStrVO.getSyuuid())) {
//                            // 未申报,但是保存了申报计算信息
//                            if (GyUtils.isNull(cycsReStrVO.getCxstysbuuid())) {
//                                hbsSbbzDTO.setSbbz("sbjs");
//                            } else {
//                                // 已申报
//                                hbsSbbzDTO.setSbbz("sbjs,sbbz");
//                            }
//                        }
//                    }
//                }
//                hbsSbbzDTOList.add(hbsSbbzDTO);
//            }
//        }
//        return hbsSbbzDTOList;
//    }
//
    @Override
    public SbjsVO initSbjsPzxx(SbjsVO sbjsVO) {
        final String djxh = sbjsVO.getDjxh();
        final String zgswskfjDm = sbjsVO.getZgswskfjDm();
        final List<DqswrVO> dqswrList = sbjsVO.getDqswrList();
        if (GyUtils.isNotNull(dqswrList)) {
            this.drJs(dqswrList, djxh, zgswskfjDm);
            sbjsVO.setDqswrList(dqswrList);
        }
        final List<GfsyxxVO> gfxxList = sbjsVO.getGfxxList();
        if (GyUtils.isNotNull(gfxxList)) {
            this.drJs(gfxxList, djxh, zgswskfjDm);
            sbjsVO.setGfxxList(gfxxList);
        }
        final List<ZssyxxVO> zsxxList = sbjsVO.getZsxxList();
        if (GyUtils.isNotNull(zsxxList)) {
            this.drJs(zsxxList, djxh, zgswskfjDm);
            sbjsVO.setZsxxList(zsxxList);
        }
        final List<CycssyxxVO> cycssyxxList = sbjsVO.getCycssyxxList();
        if (GyUtils.isNotNull(cycssyxxList)) {
            this.drJs(cycssyxxList, djxh, zgswskfjDm);
            sbjsVO.setCycssyxxList(cycssyxxList);
        }
        return sbjsVO;
    }

    /**
     * 排放口经纬度出界监控
     *
     * @param jwdxxVO
     * @return
     */
    @Override
    public JwdxxJkVO jwdxxJk(JwdxxVO jwdxxVO) {
        final JwdxxJkVO jwdxxJkVO = new JwdxxJkVO();
        Double jd2 = Double.NaN;
        Double wd = Double.NaN;
        if(GyUtils.isNotNull(jwdxxVO.getJd2())){
            jd2 = Double.valueOf(jwdxxVO.getJd2());
            wd = Double.valueOf(jwdxxVO.getWd());
        }
        if (GyUtils.isNull(jd2) || GyUtils.isNull(wd)) {
            jwdxxJkVO.setReturnCode(SUCCESS_CODE);
            jwdxxJkVO.setReturnMsg("经纬度为空，不做校验！");
            return jwdxxJkVO;
        }
        //如果开关未开，直接返回成功
        String swjgDm = "00000000000";
        String sjxx = FtsUtils.getMcByDm("cs_mh_xtcs", "sjxx", "MH000000000000002");
        if (!FtsUtils.isNull(sjxx)) {
            swjgDm = sjxx;
        }
        final String openGis = CacheUtils.getXtcs("DZSWJ00SBZX020031", "N");
        String jkxx = "未匹配到有效数据。";
        if ("N".equals(openGis)) {
            jwdxxJkVO.setReturnCode(SUCCESS_CODE);
            jwdxxJkVO.setReturnMsg("该税务机关未启用GIS！");
            return jwdxxJkVO;
        }
        // 根据纳税人的行政区划找到所在省市
        String nsrXzqhszDm = jwdxxVO.getNsrXzqhszDm();
        // 根据系统参数判断行政区划是取市级还是区县级还是不判断
        final String jkjb = CacheUtils.getXtcs("DZSWJ00SBZX020062", "3");
        if ("N".equals(jkjb)) {
            jwdxxJkVO.setReturnCode(SUCCESS_CODE);
            jwdxxJkVO.setReturnMsg("该税务机关不启用出界监控！");
            return jwdxxJkVO;
        }
        Map<String, Object> xzqhMap = CacheUtils.getTableData("dm_gy_xzqh", nsrXzqhszDm);
        if (!jkjb.equals(xzqhMap.get("xzqhjc").toString())) {
            nsrXzqhszDm = xzqhMap.get("sjxzqhszDm").toString();
        }
        Map<String, Object> xzqhNewMap = CacheUtils.getTableData("dm_gy_xzqh", nsrXzqhszDm);
        String province = "";
        String city = "";
        String area = "";
        if (!GyUtils.isNull(xzqhNewMap)) {
            if ("1".equals((String) xzqhNewMap.get("xzqhjc"))) {
                province = (String) xzqhNewMap.get("xzqhmc");
                city = (String) xzqhNewMap.get("xzqhmc");
                area = (String) xzqhNewMap.get("xzqhmc");
            }
            if ("2".equals((String) xzqhNewMap.get("xzqhjc"))) {
                province = GyUtils.isNotNull((String) xzqhNewMap.get("ssxzqmc"))
                        ? (String) xzqhNewMap.get("ssxzqmc") : (String) xzqhNewMap.get("xzqhmc");
                city = (String) xzqhNewMap.get("xzqhmc");
                area = (String) xzqhNewMap.get("xzqhmc");
            }
            if ("3".equals((String) xzqhNewMap.get("xzqhjc"))) {
                area = (String) xzqhNewMap.get("xzqhmc");
                Map<String, Object> xzqhMapNew
                        = CacheUtils.getTableData("dm_gy_xzqh", (String) xzqhNewMap.get("sjxzqhszDm"));
                province = GyUtils.isNotNull((String) xzqhMapNew.get("ssxzqmc"))
                        ? (String) xzqhMapNew.get("ssxzqmc") : (String) xzqhMapNew.get("xzqhmc");
                city = (String) xzqhMapNew.get("xzqhmc");
            }
        }
//        SjsJwdjkVO sjsJwdjkVO = null;
//        try {
//            sjsJwdjkVO = hbsSjsClient.jwdxxJk(zjSjsJrkey,
//                    GYCastUtils.cast2Str(jd2),
//                    GYCastUtils.cast2Str(wd),
//                    jwdxxVO.getNsrXzqhszDm());
//        } catch (Exception e) {
//            log.info("调用税即视R_TGIS_XDZSWJ_004 排放口经纬度出界监控异常" + e.getMessage());
//            jwdxxJkVO.setReturnMsg("调用税即视R_TGIS_XDZSWJ_004 排放口经纬度出界监控异常");
//            jwdxxJkVO.setReturnCode(SUCCESS_CODE);
//            return jwdxxJkVO;
//        }
        jwdxxJkVO.setReturnCode(SUCCESS_CODE);
//        if (!GyUtils.isNull(sjsJwdjkVO)
//                && !GyUtils.isNull(sjsJwdjkVO.getData())
//                && !GyUtils.isNull(sjsJwdjkVO.getData().getJkjg())) {
//            final String jkjg = sjsJwdjkVO.getData().getJkjg();
        String jkjg = "";
        jkjg = new MockUtils().mock(jkjg,"0");
        if ("0".equals(jkjg)) {
            jkxx = "未出界！";
        } else if ("1".equals(jkjg)) {
            jkxx = String.format("当前排放口经纬度坐标已出%s界，请核实！", area);
            jwdxxJkVO.setReturnCode(FAIL_CODE);
        } else if ("2".equals(jkjg)) {
            jkxx = String.format("当前排放口经纬度坐标已出%s界，请核实！", city);
            jwdxxJkVO.setReturnCode(FAIL_CODE);
        } else if ("3".equals(jkjg)) {
            jkxx = String.format("当前排放口经纬度坐标已出%s界，请核实！", province);
            jwdxxJkVO.setReturnCode(FAIL_CODE);
        } else if ("4".equals(jkjg)) {
            jkxx = "当前排放口经纬度坐标已出国界，请核实！";
            jwdxxJkVO.setReturnCode(FAIL_CODE);
        } else {
            jkxx = "未找到相应的行政区划！";
        }
//        }
        jwdxxJkVO.setReturnMsg(jkxx);
        return jwdxxJkVO;
    }

    /**
     * 获取税即使key，给前端调用税即使使用
     *
     * @param xtcsReqVO
     * @return
     */
    @Override
    public SjsResVO getSjsJrkey(XtcsReqVO xtcsReqVO) {
        final SjsResVO sjsResVO = new SjsResVO();
        final String zgswskfjDm = xtcsReqVO.getZgswskfjDm();
        final String openGisVue = CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020034", "N");
        sjsResVO.setOpenGisVue(openGisVue);
        if ("Y".equals(openGisVue)) {
            sjsResVO.setSjsJrkey(zjSjsJrkey);
            final String webApiUrl = CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020035", "");
            final String mapUrlKey = CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020041", "");
            final String jwdPljkSize = CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020063", "100");
            final String sjswjgDm = CxsGyUtils.getShengSwjgDmByXtcs();
            sjsResVO.setWebApiUrl(webApiUrl);
            sjsResVO.setMapUrlKey(mapUrlKey);
            sjsResVO.setJwdPljkSize(jwdPljkSize);
            sjsResVO.setSjswjgDm(sjswjgDm);
            return sjsResVO;
        } else {
            sjsResVO.setReturnMsg("未启用税即视！");
            return sjsResVO;
        }
    }
//
//    @Override
//    public List<PwxscjbDTO> queryPwxs(QueryPwxsReqVO queryPwxsReqVO) {
//        final List<PwxscjbDTO> pwxscjbDTOList = new ArrayList<>();
//        final String djxh = queryPwxsReqVO.getDjxh();
//        final String swjgDm = queryPwxsReqVO.getSwjgDm();
//        final String zspmDm = queryPwxsReqVO.getZspmDm();
//        final String zszmDm = queryPwxsReqVO.getZszmDm();
//        // 调用通过微服务调用核心接口（SWZJ.HXZG.DJ.QUERYHBSCJYWBW）
//        final HXZGSB10748Request req = new HXZGSB10748Request();
//        req.setDjxh(djxh);
//        Map<String, String> expend = new HashMap<>();
//        expend.put("sjjg", swjgDm);
//        expend.put("sjry", SjryUtil.getSjry(swjgDm));
//        HXZGSB10748Response res = null;
//        try {
//            res = Gt3Invoker.invoke("SWZJ.HXZG.SB.QUERYHBSCJYWBW", expend, req, HXZGSB10748Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.QUERYHBSCJYWBW异常：", e.getMessage());
//            return pwxscjbDTOList;
//        }
//        if (!GyUtils.isNull(res) && !GyUtils.isNull(res.getHjbhssyxxqzcjYwbw())
//                && !GyUtils.isNull(res.getHjbhssyxxqzcjYwbw().getPwxscjbVO())
//                && !GyUtils.isNull(res.getHjbhssyxxqzcjYwbw().getPwxscjbVO().getPwxscjbGrid())) {
//            List<Pwxscjb> pwxscjbGridLb = res.getHjbhssyxxqzcjYwbw().getPwxscjbVO().getPwxscjbGrid().getPwxscjbGridLb();
//            if (!GyUtils.isNull(zspmDm)) {
//                // 过滤当前征收品目，子目的排污系数
//                pwxscjbGridLb = pwxscjbGridLb.stream().filter(a -> zspmDm.equals(a.getZspmDm())).collect(Collectors.toList());
//                if (!GyUtils.isNull(zszmDm)) {
//                    pwxscjbGridLb = pwxscjbGridLb.stream().filter(a -> zszmDm.equals(a.getZszmDm())).collect(Collectors.toList());
//                }
//            }
//            if (!GyUtils.isNull(pwxscjbGridLb)) {
//                for (Pwxscjb pwxscjb : pwxscjbGridLb) {
//                    final PwxscjbDTO pwxscjbDTO = BeanUtils.toBean(pwxscjb, PwxscjbDTO.class);
//                    pwxscjbDTOList.add(pwxscjbDTO);
//                }
//            }
//
//        }
//        return pwxscjbDTOList;
//    }
//
//    @Override
//    public SavePwxsResVO savePwxs(SavePwxsReqVO savePwxsReqVO) {
//        final SavePwxsResVO savePwxsResVO = new SavePwxsResVO();
//        final LocalDateTime now = LocalDateTime.parse(df.format(LocalDateTime.now()), df);
//        final String djxh = savePwxsReqVO.getDjxh();
//        final String zgswskfjDm = savePwxsReqVO.getZgswskfjDm();
//        final List<PwxscjbDTO> insertList = savePwxsReqVO.getInsertList();
//        final List<PwxscjbDTO> updateList = savePwxsReqVO.getUpdateList();
//        final List<PwxscjbDTO> deleteList = savePwxsReqVO.getDeleteList();
//        final QueryPwxsReqVO queryPwxsReqVO = new QueryPwxsReqVO();
//        queryPwxsReqVO.setDjxh(djxh);
//        queryPwxsReqVO.setSwjgDm(zgswskfjDm);
//        final List<PwxscjbDTO> oldpwxscjbDTOList = this.queryPwxs(queryPwxsReqVO);
//        // 调用通过微服务调用核心接口（SWZJ.HXZG.DJ.SAVEHBSCJYWBW）
//        final HXZGSB10749Request req = new HXZGSB10749Request();
//        final com.css.znsb.nssb.pojo.vo.hbs.hxzg.HjbhssyxxqzcjYwbw hjbhssyxxqzcjYwbw = new cn.gov.chinatax.gt4.cxssb.pojo.vo.gt3vo.hxzg.sb10749.HjbhssyxxqzcjYwbw();
//        cn.gov.chinatax.gt4.cxssb.pojo.vo.gt3vo.hxzg.sb10749.PwxscjbVO pwxscjbVO
//                = new cn.gov.chinatax.gt4.cxssb.pojo.vo.gt3vo.hxzg.sb10749.PwxscjbVO();
//        cn.gov.chinatax.gt4.cxssb.pojo.vo.gt3vo.hxzg.sb10749.PwxscjbVO.PwxscjbGrid pwxscjbGrid
//                = new cn.gov.chinatax.gt4.cxssb.pojo.vo.gt3vo.hxzg.sb10749.PwxscjbVO.PwxscjbGrid();
//        final List<cn.gov.chinatax.gt4.cxssb.pojo.vo.gt3vo.hxzg.sb10749.Pwxscjb> pwxscjbGridLb = pwxscjbGrid.getPwxscjbGridLb();
//        if (!GyUtils.isNull(updateList)) {
//            for (PwxscjbDTO dto : oldpwxscjbDTOList) {
//                for (PwxscjbDTO pwxscjbDTO : updateList) {
//                    if (dto.getPwxsuuid().equals(pwxscjbDTO.getPwxsuuid())) {
//                        BeanUtils.copyBean(pwxscjbDTO, dto);
//                    }
//                }
//            }
//        }
//
//        if (!GyUtils.isNull(deleteList)) {
//            final Iterator<PwxscjbDTO> pwxscjbDTOIterator = oldpwxscjbDTOList.iterator();
//            while (pwxscjbDTOIterator.hasNext()) {
//                final PwxscjbDTO dto = pwxscjbDTOIterator.next();
//                for (PwxscjbDTO pwxscjbDTO : deleteList) {
//                    final String pwxsuuid = pwxscjbDTO.getPwxsuuid();
//                    // 修改新电局关联表中有关这条排污系数的记录为N
//                    final QueryWrapper<HbsjcxxcjSyxxypwxsglbDO> queryWrapper = new QueryWrapper<>();
//                    queryWrapper.eq("djxh", djxh);
//                    queryWrapper.eq("yxbz", "Y");
//                    queryWrapper.eq("pwxsuuid", pwxsuuid);
//                    final List<HbsjcxxcjSyxxypwxsglbDO> syxxypwxsglbDOList = hbsjcxxcjSyxxypwxsglbMapper.selectList(queryWrapper);
//                    if (!GyUtils.isNull(syxxypwxsglbDOList)) {
//                        for (HbsjcxxcjSyxxypwxsglbDO hbsjcxxcjSyxxypwxsglbDO : syxxypwxsglbDOList) {
//                            hbsjcxxcjSyxxypwxsglbDO.setYxbz("N");
//                            hbsjcxxcjSyxxypwxsglbDO.setXgrq(now);
//                            hbsjcxxcjSyxxypwxsglbDO.setXgrsfid(SjryUtil.getSjry(zgswskfjDm));
//                            hbsjcxxcjSyxxypwxsglbMapper.updateById(hbsjcxxcjSyxxypwxsglbDO);
//                        }
//                    }
//                    if (dto.getPwxsuuid().equals(pwxscjbDTO.getPwxsuuid())) {
//                        pwxscjbDTOIterator.remove();
//                    }
//                }
//            }
//
//
//        }
//        if (!GyUtils.isNull(insertList)) {
//            for (PwxscjbDTO pwxscjbDTO : insertList) {
//                oldpwxscjbDTOList.add(pwxscjbDTO);
//            }
//        }
//        if (!GyUtils.isNull(oldpwxscjbDTOList)) {
//            pwxscjbGridLb.addAll(BeanUtils.toBean(oldpwxscjbDTOList, cn.gov.chinatax.gt4.cxssb.pojo.vo.gt3vo.hxzg.sb10749.Pwxscjb.class));
//        }
//        pwxscjbVO.setPwxscjbGrid(pwxscjbGrid);
//        hjbhssyxxqzcjYwbw.setPwxscjbVO(pwxscjbVO);
//        req.setDjxh(djxh);
//        req.setHjbhssyxxqzcjYwbw(hjbhssyxxqzcjYwbw);
//        Map<String, String> expend = new HashMap<>();
//        expend.put("sjjg", zgswskfjDm);
//        expend.put("sjry", SjryUtil.getSjry(zgswskfjDm));
//        HXZGSB10749Response res = null;
//        try {
//            res = Gt3Invoker.invoke("SWZJ.HXZG.SB.SAVEHBSCJYWBW", expend, req, HXZGSB10749Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.SAVEHBSCJYWBW异常：", e.getMessage());
//            savePwxsResVO.setReturnCode(FAIL_CODE);
//            savePwxsResVO.setReturnMsg(e.getMessage());
//            return savePwxsResVO;
//        }
//        if (!"Y".equals(res.getCjbz())) {
//            savePwxsResVO.setReturnCode(FAIL_CODE);
//            return savePwxsResVO;
//        }
//        // 需要再查一次，把uuid返回给前端
//
//        queryPwxsReqVO.setZspmDm(savePwxsReqVO.getZspmDm());
//        queryPwxsReqVO.setZszmDm(savePwxsReqVO.getZszmDm());
//        final List<PwxscjbDTO> pwxscjbDTOList = this.queryPwxs(queryPwxsReqVO);
//        savePwxsResVO.setReturnMsg("保存成功！");
//        savePwxsResVO.setPwxscjbDTOList(pwxscjbDTOList);
//        return savePwxsResVO;
//    }
//
//    /**
//     * 保存排污系数和税源的关联关系(已作废)
//     *
//     * @param savePwxsGlxxReqVO
//     */
//    @Override
//    public HbsResVo savePwxsGlxx(SavePwxsGlxxReqVO savePwxsGlxxReqVO) {
//        final HbsResVo hbsResVo = new HbsResVo();
//        final LocalDateTime now = LocalDateTime.parse(df.format(LocalDateTime.now()), df);
//        final String djxh = savePwxsGlxxReqVO.getDjxh();
//        final String zgswskfjDm = savePwxsGlxxReqVO.getZgswskfjDm();
//        final String ywqddm = savePwxsGlxxReqVO.getYwqddm();
//        final String sjry = savePwxsGlxxReqVO.getSjry();
//        final String sjswjgdm = savePwxsGlxxReqVO.getSjswjgdm();
//        // 排污系数
//        final PwxscjbDTO pwxscjbDTO = savePwxsGlxxReqVO.getPwxscjbDTO();
//        final String zspmDm = pwxscjbDTO.getZspmDm();
//        final String zszmDm = pwxscjbDTO.getZszmDm();
//        // 税源信息
//        final SyxxDTO syxxDTO = savePwxsGlxxReqVO.getSyxxDTO();
//        final String pwxkzbh = syxxDTO.getPwxkzbh();
//        final String pfkbh = syxxDTO.getPfkbh();
//        final String syuuid = syxxDTO.getSyuuid();
//        // 防止疑点数据，保存之前先校验
//        if (GyUtils.isNull(pwxscjbDTO.getPwxsuuid())) {
//            // 先查询一次
//            final QueryPwxsReqVO queryPwxsReqVO = new QueryPwxsReqVO();
//            queryPwxsReqVO.setZspmDm(zspmDm);
//            queryPwxsReqVO.setZszmDm(zszmDm);
//            queryPwxsReqVO.setSwjgDm(zgswskfjDm);
//            queryPwxsReqVO.setDjxh(djxh);
//            final List<PwxscjbDTO> pwxscjbDTOList = this.queryPwxs(queryPwxsReqVO);
//            for (PwxscjbDTO dto : pwxscjbDTOList) {
//                if (dto.getPwxs().equals(pwxscjbDTO.getPwxs())
//                        && dto.getCwxs().equals(pwxscjbDTO.getCwxs())
//                        && dto.getJsjsdw().equals(pwxscjbDTO.getJsjsdw())
//                        && dto.getWrwdwDm().equals(pwxscjbDTO.getWrwdwDm())) {
//                    pwxscjbDTO.setPwxsuuid(dto.getPwxsuuid());
//                }
//            }
//            if (GyUtils.isNull(pwxscjbDTO.getPwxsuuid())) {
//                hbsResVo.setReturnCode(FAIL_CODE);
//                hbsResVo.setReturnMsg("pwxsuuid为空，请核实！");
//                return hbsResVo;
//            }
//        }
//        if (!zspmDm.equals(syxxDTO.getZspmDm())) {
//            hbsResVo.setReturnCode(FAIL_CODE);
//            hbsResVo.setReturnMsg("选择的排污系数采集表污染物名称与税源污染物名称不一致，请核实！");
//            return hbsResVo;
//        }
//        if (!GyUtils.isNull(zszmDm) && !zszmDm.equals(syxxDTO.getZszmDm())) {
//            hbsResVo.setReturnCode(FAIL_CODE);
//            hbsResVo.setReturnMsg("选择的排污系数采集表征收子目与税源征收子目不一致，请核实！");
//            return hbsResVo;
//        }
//        // 新电局关联表
//        // 查询这条税源是否已经关联过,如果关联过了且pwxsuuid发生了变化先作废已关联过的数据，再新增
//        // 先根据syuuid查询
//        if (!GyUtils.isNull(syuuid)) {
//            final List<HbsjcxxcjSyxxypwxsglbDO> hbsjcxxcjSyxxypwxsglbDOList = hbsjcxxcjSyxxypwxsglbMapper.querySyxxypwxsglxxBysyuuid(syuuid);
//            if (!GyUtils.isNull(hbsjcxxcjSyxxypwxsglbDOList)) {
//                // 作废所有
//                for (HbsjcxxcjSyxxypwxsglbDO hbsjcxxcjSyxxypwxsglbDO : hbsjcxxcjSyxxypwxsglbDOList) {
//                    hbsjcxxcjSyxxypwxsglbDO.setYxbz("N");
//                    hbsjcxxcjSyxxypwxsglbDO.setXgrq(now);
//                    hbsjcxxcjSyxxypwxsglbDO.setXgrsfid(SjryUtil.getSjry(zgswskfjDm));
//                    hbsjcxxcjSyxxypwxsglbMapper.updateById(hbsjcxxcjSyxxypwxsglbDO);
//                }
//            }
//        }
//        final List<HbsjcxxcjSyxxypwxsglbDO> hbsjcxxcjSyxxypwxsglbDOList = hbsjcxxcjSyxxypwxsglbMapper.querySyxxypwxsglxxByNosyuuid(djxh, pwxkzbh, pfkbh, zspmDm, zszmDm);
//        if (!GyUtils.isNull(hbsjcxxcjSyxxypwxsglbDOList)) {
//            for (HbsjcxxcjSyxxypwxsglbDO hbsjcxxcjSyxxypwxsglbDO : hbsjcxxcjSyxxypwxsglbDOList) {
//                hbsjcxxcjSyxxypwxsglbDO.setYxbz("N");
//                hbsjcxxcjSyxxypwxsglbDO.setXgrq(now);
//                hbsjcxxcjSyxxypwxsglbDO.setXgrsfid(SjryUtil.getSjry(zgswskfjDm));
//                hbsjcxxcjSyxxypwxsglbMapper.updateById(hbsjcxxcjSyxxypwxsglbDO);
//            }
//        }
//        final HbsjcxxcjSyxxypwxsglbDO hbsjcxxcjSyxxypwxsglbDONew = new HbsjcxxcjSyxxypwxsglbDO();
//        hbsjcxxcjSyxxypwxsglbDONew.setDjxh(new BigDecimal(djxh));
//        hbsjcxxcjSyxxypwxsglbDONew.setPwxsuuid(pwxscjbDTO.getPwxsuuid());
//        if (!GyUtils.isNull(syuuid)) {
//            hbsjcxxcjSyxxypwxsglbDONew.setSyuuid(syuuid);
//        }
//        hbsjcxxcjSyxxypwxsglbDONew.setPwxkzbh(pwxkzbh);
//        hbsjcxxcjSyxxypwxsglbDONew.setPfkbh(pfkbh);
//        hbsjcxxcjSyxxypwxsglbDONew.setZspmDm(zspmDm);
//        hbsjcxxcjSyxxypwxsglbDONew.setZszmDm(zszmDm);
//        hbsjcxxcjSyxxypwxsglbDONew.setYxbz("Y");
//        hbsjcxxcjSyxxypwxsglbDONew.setYwqdDm(ywqddm);
//        hbsjcxxcjSyxxypwxsglbDONew.setLrrq(now);
//        hbsjcxxcjSyxxypwxsglbDONew.setLrrsfid(sjry);
//        hbsjcxxcjSyxxypwxsglbDONew.setSjcsdq(sjswjgdm);
//        hbsjcxxcjSyxxypwxsglbDONew.setSjgsdq(sjswjgdm);
//        hbsjcxxcjSyxxypwxsglbDONew.setSjtbSj(now);
//        final int insert = hbsjcxxcjSyxxypwxsglbMapper.insert(hbsjcxxcjSyxxypwxsglbDONew);
//        if (insert == 0) {
//            hbsResVo.setReturnCode(FAIL_CODE);
//            hbsResVo.setReturnMsg("关联失败，请重试！");
//            return hbsResVo;
//        }
//        return hbsResVo;
//    }
//
    /**
     * 校验税源信息，防止疑点数据
     *
     * @param hbsJcxxDTO
     * @return
     */
    @Override
    public HbsResVo checkSyxx(HbsJcxxDTO hbsJcxxDTO) {
        final HbsResVo resVO = new HbsResVo();
        final StringBuffer msg = new StringBuffer();//错误消息
        final List<SyxxDTO> syxxGridList = hbsJcxxDTO.getSyxxGridList();
        if (!GyUtils.isNull(syxxGridList)) {
            for (int i = 0; i < syxxGridList.size(); i++) {
                final SyxxDTO syxxDTO = syxxGridList.get(i);
                final int rowNum = i + 1;
                final String pwxkzbh = syxxDTO.getPwxkzbh();
                final String pfkbh = syxxDTO.getPwxkzbh();
                final String zywrwlbDm = syxxDTO.getZywrwlbDm();
                final String syyxqq = syxxDTO.getSyyxqq();
                final String syyxqz = syxxDTO.getSyyxqz();
                final String jdxzDm = syxxDTO.getJdxzDm();
                final String wrwpfljsffDm = syxxDTO.getWrwpfljsffDm();
                final String zspmDm = syxxDTO.getZspmDm();
                // 排放口编号、有效期起/止、污染物类别、生产经营所在街乡任一数据未填写
                if (GyUtils.isNull(pwxkzbh)
                        || GyUtils.isNull(pfkbh)
                        || GyUtils.isNull(zywrwlbDm)
                        || GyUtils.isNull(syyxqq)
                        || GyUtils.isNull(syyxqz)
                        || GyUtils.isNull(jdxzDm)) {
                    msg.append("第" + rowNum + "行存在'排污许可证编号'、'排放口编号'、'有效期起/止'、'污染物类别'、'生产经营所在街乡'数据项未填写，请核实！\n");
                    continue;
                }
                // 当排污许可证编号不是“无排污许可证排口”：经度/纬度未填写完整
                if (!"无排污许可证排口".equals(pwxkzbh) && (GyUtils.isNull(syxxDTO.getJd2()) || GyUtils.isNull(syxxDTO.getWd()))) {
                    msg.append("第" + rowNum + "行存在'经度'、'纬度'数据项未填写，请核实！\n");
                    continue;
                }
                // 判断大气水、固体废物是否填写了征收品目
                if (("S".equals(zywrwlbDm) || "W".equals(zywrwlbDm) || "A".equals(zywrwlbDm)) && GyUtils.isNull(zspmDm)) {
                    msg.append("第" + rowNum + "行存在'污染物名称'数据项未填写，请核实！\n");
                    continue;
                }
                // 判断大气水，噪声是否填写了污染物计算方法
                if ("N".equals(zywrwlbDm) || "W".equals(zywrwlbDm) || "A".equals(zywrwlbDm)) {
                    if (GyUtils.isNull(wrwpfljsffDm)) {
                        msg.append("第" + rowNum + "行存在'污染物排放量计算方法'数据项未填写，请核实！\n");
                        continue;
                    }
                }
                // 大气水污染校验
                if (("W".equals(zywrwlbDm) || "A".equals(zywrwlbDm))) {
                    // 当计算方法为'自动监测'或'监测机构监测'时，判断附表是否采集
                    /*if (("1".equals(wrwpfljsffDm) || "2".equals(wrwpfljsffDm))
                            && (GyUtils.isNull(syxxDTO.getBzndz()) || GyUtils.isNull(syxxDTO.getZxbz1()))) {
                        msg.append("第" + rowNum + "行存在'标准浓度值'、'执行标准'数据项未填写，请核实！\n");
                        continue;
                    }*/
                    // 当计算方法为'排污系数'时，判断附表是否采集
                    if ("4".equals(wrwpfljsffDm)
                            && (GyUtils.isNull(syxxDTO.getPwxscjbDTOList()))) {
                        msg.append("第" + rowNum + "行存在'产污系数'、'排污系数'、'污染物单位'、'计税基数单位'数据项未填写，请核实！\n");
                        continue;
                    }
                }
                // 固体废物校验
                /*if ("S".equals(zywrwlbDm) && (GyUtils.isNull(syxxDTO.getCzqk())
                        || GyUtils.isNull(syxxDTO.getCshssmc())
                        || GyUtils.isNull(syxxDTO.getZhlyzyfssDm()))) {
                    msg.append("第" + rowNum + "行存在'贮存情况'、'处置情况'、'综合利用情况'数据项未填写，请核实！\n");
                    continue;
                }*/
                // 噪声校验
                if ("N".equals(zywrwlbDm)) {
                    if ((GyUtils.isNull(syxxDTO.getSfzycsbz()))) {
                        msg.append("第" + rowNum + "行存在'是否昼夜生产'数据项未填写，请核实！\n");
                        continue;
                    }
                    if ("Y".equals(syxxDTO.getSfzycsbz())) {
                        if (GyUtils.isNull(syxxDTO.getBzzyj()) || GyUtils.isNull(syxxDTO.getBzzzj())) {
                            msg.append("第" + rowNum + "行存在'标准值——昼间 (6时至22时)'、'标准值——夜间 (22时至次日6时)'数据项未填写，请核实！\n");
                            continue;
                        }
                    } else {
                        if (GyUtils.isNull(syxxDTO.getBzzzj())) {
                            msg.append("第" + rowNum + "行存在'标准值——昼间 (6时至22时)'数据项未填写，请核实！\n");
                            continue;
                        }
                    }
                }
            }
            if (msg.length() > 0) {
                resVO.setReturnCode(FAIL_CODE);
                resVO.setReturnMsg(msg.toString());
                return resVO;
            }
        }
        resVO.setReturnCode(SUCCESS_CODE);
        return resVO;
    }
//
//    @Override
//    public Map<String, Object> printSyxx(HbssyxxcjSaveReqVo hbssyxxcjSaveReqVo) {
//        final Map<String, Object> map = new HashMap<>();
//        final HbsjcxxcjbVO hbsjcxxcjb = hbssyxxcjSaveReqVo.getHbsJcxxVO().getHbsjcxxcjb();
//        final List<SyxxVO> syxxGridList = hbssyxxcjSaveReqVo.getHbsJcxxVO().getSyxxGridList();
//        final List<BccjxxVO> bccjList = hbssyxxcjSaveReqVo.getBccjList();
//        final List<GfcjxxVO> gfcjxxList = hbssyxxcjSaveReqVo.getGfcjxxList();
//        final List<ZscjxxVO> zscjxxList = hbssyxxcjSaveReqVo.getZscjxxList();
//        final List<base.PwxscjbVO> pwxscjbxxList = hbssyxxcjSaveReqVo.getPwxscjbxxList();
//        // 税源信息sheet页
//        final List<HbsSyxxDrVO> syxxGridNewList = new ArrayList<>();
//        for (SyxxVO syxxVO : syxxGridList) {
//            final HbsSyxxDrVO vo = BeanUtils.toBean(syxxVO, HbsSyxxDrVO.class);
//            vo.setJdxzDm(this.getMcForExport(this.getDmForDr(vo.getJdxzDm()), "DM_GY_JDXZ", "JDXZMC"));
//            vo.setZywrwlbDm(this.getMcForExport(this.getDmForDr(vo.getZywrwlbDm()), "DM_DJ_ZYWRWLB", "ZYWRWLBMC").replace("|", "_"));
//            vo.setZspmDm(this.getMcForExport(this.getDmForDr(vo.getZspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//            vo.setZszmDm(this.getMcForExport(this.getDmForDr(vo.getZszmDm()), "DM_GY_ZSZM", "ZSZMMC"));
//            vo.setFzspmDm(this.getMcForExport(this.getDmForDr(vo.getFzspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//            vo.setWrwpfljsffDm(this.getMcForExport(this.getDmForDr(vo.getWrwpfljsffDm()), "DM_DJ_WRWPFLJSFF", "WRWPFLJSFFMC").replace("|", "_"));
//            vo.setJd2d(syxxVO.getJd2D());
//            vo.setJd2f(syxxVO.getJd2F());
//            vo.setJd2m(syxxVO.getJd2M());
//            if ("Y".equals(vo.getSyxxyxx())) {
//                vo.setSyxxyxx("有效税源");
//            } else if ("N".equals(vo.getSyxxyxx())) {
//                vo.setSyxxyxx("无效税源");
//            } else {
//                vo.setSyxxyxx("");
//            }
//            syxxGridNewList.add(vo);
//        }
//        map.put("syxxGrid", syxxGridNewList);
//        // 大气水附表sheet页
//        final List<BccjxxVO> bccjNewList = new ArrayList<>();
//        for (BccjxxVO vo : bccjList) {
//            if ("1".equals(vo.getWrwpfljsffDm()) || "2".equals(vo.getWrwpfljsffDm())) {
//                vo.setZspmDm(this.getMcForExport(this.getDmForDr(vo.getZspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//                vo.setZszmDm(this.getMcForExport(this.getDmForDr(vo.getZszmDm()), "DM_GY_ZSZM", "ZSZMMC"));
//                if ("Y".equals(vo.getSyxxyxx())) {
//                    vo.setSyxxyxx("有效税源");
//                } else if ("N".equals(vo.getSyxxyxx())) {
//                    vo.setSyxxyxx("无效税源");
//                } else {
//                    vo.setSyxxyxx("");
//                }
//                bccjNewList.add(vo);
//            }
//        }
//        map.put("dqsGrid", bccjNewList);
//        // 固废附表sheet页
//        for (GfcjxxVO vo : gfcjxxList) {
//            vo.setZspmDm(this.getMcForExport(this.getDmForDr(vo.getZspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//            vo.setZszmDm(this.getMcForExport(this.getDmForDr(vo.getZszmDm()), "DM_GY_ZSZM", "ZSZMMC"));
//            if ("Y".equals(vo.getSyxxyxx())) {
//                vo.setSyxxyxx("有效税源");
//            } else if ("N".equals(vo.getSyxxyxx())) {
//                vo.setSyxxyxx("无效税源");
//            } else {
//                vo.setSyxxyxx("");
//            }
//        }
//        map.put("gfGrid", gfcjxxList);
//        // 噪声附表sheet页
//        for (ZscjxxVO vo : zscjxxList) {
//            final String sfzycsbz = vo.getSfzycsbz();
//            if ("Y".equals(sfzycsbz)) {
//                vo.setSfzycsbz(sfzycsbz + "_是");
//            } else if ("N".equals(sfzycsbz)) {
//                vo.setSfzycsbz(sfzycsbz + "_否");
//            } else {
//                vo.setSfzycsbz(null);
//            }
//        }
//        map.put("zsGrid", zscjxxList);
//        // 排污系数附表sheet页
//        // 重新组装排污系数采集附表
//        List<PwxscjbDTO> pwxscjbxxNewList = new ArrayList<>();
//        if (!GyUtils.isNull(pwxscjbxxList)) {
//            for (base.PwxscjbVO pwxscjbVO : pwxscjbxxList) {
//                if (!GyUtils.isNull(pwxscjbVO.getPwxscjbDTOList())) {
//                    pwxscjbxxNewList.addAll(pwxscjbVO.getPwxscjbDTOList());
//                }
//            }
//            // 过滤重复新增的数据
//            final Map<String, PwxscjbDTO> pwxscjbDTOMap = new HashMap<>();
//            for (int i = pwxscjbxxNewList.size() - 1; i >= 0; i--) {
//                final PwxscjbDTO pwxscjbDTO = pwxscjbxxNewList.get(i);
//                String key = pwxscjbDTO.getZspmDm() + pwxscjbDTO.getWrwdwDm() + pwxscjbDTO.getJsjsdw() + pwxscjbDTO.getCwxs() + pwxscjbDTO.getPwxs();
//                if (!GyUtils.isNull(pwxscjbDTO.getPwxsuuid())) {
//                    key = key + pwxscjbDTO.getPwxsuuid();
//                }
//                if (!GyUtils.isNull(pwxscjbDTO.getZszmDm())) {
//                    key = key + pwxscjbDTO.getZszmDm();
//                }
//                if (pwxscjbDTOMap.containsKey(key)) {
//                    pwxscjbxxNewList.remove(i);
//                } else {
//                    pwxscjbDTOMap.put(key, pwxscjbDTO);
//                }
//            }
//        }
//        for (PwxscjbDTO pwxscjbDTO : pwxscjbxxNewList) {
//            pwxscjbDTO.setZywrwlbDm(this.getMcForExport(this.getDmForDr(pwxscjbDTO.getZywrwlbDm()), "DM_DJ_ZYWRWLB", "ZYWRWLBMC").replace("|", "_"));
//            pwxscjbDTO.setZspmDm(this.getMcForExport(this.getDmForDr(pwxscjbDTO.getZspmDm()), "DM_GY_ZSPM", "ZSPMMC"));
//            pwxscjbDTO.setZszmDm(this.getMcForExport(this.getDmForDr(pwxscjbDTO.getZszmDm()), "DM_GY_ZSZM", "ZSZMMC"));
//            pwxscjbDTO.setWrwdwDm(this.getMcForExport(this.getDmForDr(pwxscjbDTO.getWrwdwDm()), "DM_GY_JLDW", "JLDWMC").replace("|", "_"));
//        }
//        map.put("pwxsGrid", pwxscjbxxNewList);
//        // 填充街道乡镇信息
//        List<JdxzVO> jdxzList = this.loadJdxzList(hbsjcxxcjb.getXzqhszDm());
//        final List<Object> jdxzMbList = jdxzList.stream().map(m -> {
//            final String newCode = m.getCode() + "|" + m.getCaption();
//            return newCode;
//        }).collect(Collectors.toList());
//        map.put("jdxzList", jdxzMbList);
//        // 填充排污许可证编号下拉数据
//        final List<Object> pwxkzbhList = new ArrayList<>();
//        String zzsyhzcmc = "";
//        if (GyUtils.isNotNull(hbsjcxxcjb) && GyUtils.isNotNull(hbsjcxxcjb.getZzsyhzcmc())) {
//            zzsyhzcmc = hbsjcxxcjb.getZzsyhzcmc();
//            final String[] split = zzsyhzcmc.split(",");
//            for (String s : split) {
//                pwxkzbhList.add(s);
//            }
//        }
//        if (zzsyhzcmc.contains("无排污许可证编号")) {
//            pwxkzbhList.remove("无排污许可证编号");
//        }
//        if (!zzsyhzcmc.contains("无排污许可证排口")) {
//            pwxkzbhList.add("无排污许可证排口");
//        }
//        map.put("pwxkzbhList", pwxkzbhList);
//        return map;
//    }
//
//
    @Override
    public HbsJcxxVO checkSyxxJwd(HbssyxxcjSaveReqVo hbssyxxcjSaveReqVo) {
        final HbsJcxxVO hbsJcxxVO = hbssyxxcjSaveReqVo.getHbsJcxxVO();
        final String zgswskfjDm = hbssyxxcjSaveReqVo.getZgswskfjDm();
        final List<SyxxVO> syxxGridNewList = new ArrayList<>();
        final String openGis = CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020031", "N");
        if ("N".equals(openGis)) {
            hbssyxxcjSaveReqVo.getHbsJcxxVO().setSyxxGridList(syxxGridNewList);
            return hbsJcxxVO;
        }
        String xzqhszDm = hbssyxxcjSaveReqVo.getHbsJcxxVO().getHbsjcxxcjb().getXzqhszDm();
        // 根据系统参数判断行政区划是取市级还是区县级还是不判断
        String jkjb = CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020062", "3");
        jkjb = new MockUtils().mock(jkjb,"3");
        if ("N".equals(jkjb)) {
            hbssyxxcjSaveReqVo.getHbsJcxxVO().setSyxxGridList(syxxGridNewList);
            return hbsJcxxVO;
        }
        Map<String, Object> xzqhMap = CacheUtils.getTableData("DM_GY_XZQH", xzqhszDm);
        if (GyUtils.isNotNull(xzqhMap)&&!jkjb.equals(xzqhMap.get("XZQHJC").toString())) {
            xzqhszDm = xzqhMap.get("SJXZQHSZ_DM").toString();
        }
        final List<SyxxVO> syxxGridList = hbssyxxcjSaveReqVo.getHbsJcxxVO().getSyxxGridList();
        final Map<String, Object> skssq = this.getSkssq();
        final String skssqq = GYCastUtils.cast2Str(skssq.get("skssqq"));
        final String skssqz = GYCastUtils.cast2Str(skssq.get("skssqz"));
        final Map<String, List<SyxxVO>> jwdMap = new HashMap<>();
        for (SyxxVO syxxVO : syxxGridList) {
            Date syyxqq = GYCastUtils.cast2Date(syxxVO.getSyyxqq());
            Date syyxqz = GYCastUtils.cast2Date(syxxVO.getSyyxqz());
            // 过滤在当前申报期的税源
            if (!GYCastUtils.sqAcross(
                    GYCastUtils.cast2Date(skssqq), GYCastUtils.cast2Date(skssqz),
                    syyxqq, syyxqz)) {
                continue;
            }
            if (GyUtils.isNotNull(syxxVO.getJd2()) && GyUtils.isNotNull(syxxVO.getWd())) {
                final String key = jwdToXsString(syxxVO.getJd2()) + "," + jwdToXsString(syxxVO.getWd());
                if (!jwdMap.containsKey(key)) {
                    jwdMap.put(key, new ArrayList<>());
                    jwdMap.get(key).add(syxxVO);
                } else {
                    jwdMap.get(key).add(syxxVO);
                }
            } else {
                syxxGridNewList.add(syxxVO);
            }
        }
        // 组装税即视批量接口的入参
        String jwdPljkSize = "100";CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020063", "100");
        jkjb = new MockUtils().mock(jkjb,"100");
        final List<List<String>> list = Lists.partition(jwdMap.keySet().stream().collect(Collectors.toList()), GYCastUtils.cast2Integer(jwdPljkSize));
        for (List<String> jwdList : list) {
            final List<JwdPljkVO> plwz = new ArrayList<>();
            for (int i = 0; i < jwdList.size(); i++) {
                final String jwd = jwdList.get(i);
                final JwdPljkVO jwdPljkVO = new JwdPljkVO();
                jwdPljkVO.setLon(jwd.split(",")[0]);
                jwdPljkVO.setLat(jwd.split(",")[1]);
                jwdPljkVO.setId(String.valueOf(i));
                plwz.add(jwdPljkVO);
            }
            // 调用税即视批量出界监控
            final JwdPljkReqVO jwdPljkReqVO = new JwdPljkReqVO();
            jwdPljkReqVO.setKey(zjSjsJrkey);
            jwdPljkReqVO.setXzqhszDm(xzqhszDm);
            jwdPljkReqVO.setPlwz(plwz);
            SjsPlJwdjkVO sjsPlJwdjkVO = new SjsPlJwdjkVO();
            log.info("调用税即视R_TGIS_XDZSWJ_023 批量出界监控入参：" + JsonUtils.toJson(jwdPljkReqVO));
            try {
//                sjsPlJwdjkVO = hbsSjsClient.jwdxxPljk(zjSjsJrkey, jwdPljkReqVO);
            } catch (Exception e) {
                log.info("调用税即视R_TGIS_XDZSWJ_023 批量出界监控异常" + e.getMessage());
                continue;
            }
            log.info("调用税即视R_TGIS_XDZSWJ_023 批量出界监控返回值：" + JsonUtils.toJson(sjsPlJwdjkVO));
            // 根据返回结果拿到出界的税源
            if ("00".equals(sjsPlJwdjkVO.getRtnCode()) && !GyUtils.isNull(sjsPlJwdjkVO.getData())) {
                final Map<String, Object> pljkjg = sjsPlJwdjkVO.getData();
                for (JwdPljkVO jwdPljkVO : plwz) {
                    final String id = jwdPljkVO.getId();
                    if ("0".equals(pljkjg.get(id).toString())) {
                        syxxGridNewList.addAll(jwdMap.get(jwdPljkVO.getLon() + "," + jwdPljkVO.getLat()));
                    }
                }
            }
        }
        hbssyxxcjSaveReqVo.getHbsJcxxVO().setSyxxGridList(syxxGridNewList);
        return hbsJcxxVO;
    }

    /**
     * 修改税源经纬度
     *
     * @param hbssyxxcjSaveReqVo
     * @return
     */
    @Override
    public HbsResVo updateSyxxJwd(HbssyxxcjSaveReqVo hbssyxxcjSaveReqVo) {
        final HbsResVo resVo = new HbsResVo();
        final String zgswskfjDm = hbssyxxcjSaveReqVo.getZgswskfjDm();
        // 调用核心接口（SWZJ.HXZG.SB.HBSCJSAVEJCXX）保存税源基础信息
//        final HXZGSB10753Request req = new HXZGSB10753Request();
        final HbsJcxxVO hbsJcxxVO = BeanUtils.toBean(hbssyxxcjSaveReqVo.getHbsJcxxVO(),
                HbsJcxxVO.class);
        HbsjcxxcjbVO hbsjcxxcjb = hbssyxxcjSaveReqVo.getHbsJcxxVO().getHbsjcxxcjb();
        // 多个排污许可证号，数据库pwxkzbh长度不够
        if (!GyUtils.isNull(hbsjcxxcjb.getPwxkzbh())) {
            hbsjcxxcjb.setPwxkzbh(null);
        }
        if (!GyUtils.isNull(hbsjcxxcjb.getZzsyhzcmc()) && hbsjcxxcjb.getZzsyhzcmc().contains("无排污许可证排口")) {
            hbsjcxxcjb.setZzsyhzcmc(hbsjcxxcjb.getZzsyhzcmc().replace("无排污许可证排口", ""));
        }
        if ("无排污许可证排口".equals(hbsjcxxcjb.getZzsyhzcmc()) || GyUtils.isNull(hbsjcxxcjb.getZzsyhzcmc())) {
            hbsjcxxcjb.setZzsyhzcmc("");
        }
        hbsJcxxVO.setHbsjcxxcjb(hbsjcxxcjb);
        hbsJcxxVO.setSyxxGridList(hbssyxxcjSaveReqVo.getHbsJcxxVO().getSyxxGridList());
//        req.setHbsJcxxVO(hbsJcxxVO);
//        Map<String, String> expend = new HashMap<>();
//        expend.put("sjjg", zgswskfjDm);
//        expend.put("sjry", SjryUtil.getSjry(zgswskfjDm));
//        HXZGSB10753Response res = null;
//        try {
//            res = Gt3Invoker.invoke("SWZJ.HXZG.SB.HBSCJSAVEJCXX", expend, req, HXZGSB10753Response.class);
//        } catch (RuntimeException e) {
//            log.error("调用核心征管接口SWZJ.HXZG.SB.HBSCJSAVEJCXX异常：", e);
//            final String message = e.getMessage();
//            if (!GyUtils.isNull(message) && message.contains("生产经营所在街乡不允许为空")) {
//                resVo.setReturnMsg("生产经营所在街乡不允许为空!");
//            } else {
//                resVo.setReturnMsg(message);
//            }
//            resVo.setReturnCode(FAIL_CODE);
//            return resVo;
//        }
        return resVo;
    }


//    /**
//     * 申报计算导出代码转“代码|名称”
//     *
//     * @param dm
//     * @param tableName
//     * @param mc
//     * @return
//     */
//    private String getMcForExport(String dm, String tableName, String mc) {
//        if (GyUtils.isNull(dm)) {
//            return "";
//        }
//        final Map<String, Object> map = GYSbqxUtils.getMcByDmbAndDm(tableName, dm);
//        final Object v_mc = GyUtils.isNull(map) ? "" : map.get(mc);
//        return dm + "|" + v_mc;
//    }
//
//
//    /**
//     * 导入分割名称代码
//     *
//     * @param dmAndMc
//     * @return
//     */
//    private String getDmForDr(String dmAndMc) {
//        if (GyUtils.isNull(dmAndMc)) {
//            return "";
//        }
//        return dmAndMc.split("\\|")[0];
//    }
//
//    /**
//     * @param skssqq skssqq
//     * @param skssqz skssqz
//     * @param yf     yf
//     * @return boolean
//     * @throws
//     * @name 判断录入月份是否在所属期之间
//     * @description 是：true 否：false
//     * @time 创建时间:2020/11/24 10:14
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private boolean betweenMonth(String skssqq, String skssqz, String yf) {
//        final Calendar ssqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(skssqq));
//        final Calendar ssqz = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(skssqz));
//        final int yfInt = Integer.parseInt(yf);
//        return (ssqq.get(Calendar.MONTH) + 1) <= yfInt && (ssqz.get(Calendar.MONTH) + 1) >= yfInt;
//    }
//
//    /**
//     * @param yf    yf
//     * @param btSqq btSqq
//     * @return java.util.Map<java.lang.String, java.lang.String>
//     * @throws
//     * @name 获取税源的属期
//     * @description 根据表头的ssq和导入的月份获取税源的所属期
//     * @time 创建时间:2020/12/4 10:16
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private Map<String, String> getSsqByYf(String yf, String btSqq) {
//        final Map<String, String> ssqMap = new HashMap<>();
//        final Calendar ssqq = GYCastUtils.cast2Calendar(GYCastUtils.cast2Date(btSqq));
//        ssqq.set(Calendar.MONTH, Integer.valueOf(yf) - 1);
//        ssqq.set(Calendar.DAY_OF_MONTH, 1);
//        final Date ssqz = CchxwsnssbGyUtils.getlastDayofMonth(GYCastUtils.cast2Date(ssqq));
//        ssqMap.put("sqq", GYCastUtils.cast2Str(ssqq));
//        ssqMap.put("sqz", GYCastUtils.cast2Str(ssqz));
//        return ssqMap;
//    }
//
//    /**
//     * 将征收品目为pH值（水）的征收子目置空(核心方法)
//     *
//     * @param syxxbMap
//     */
//    private void filterPhZszm(Map<String, Object> syxxbMap) {
//        if (GyUtils.isNull(syxxbMap)) {
//            return;
//        }
//        final String zspmDm = (String) syxxbMap.get("zspmDm");
//        if ("101212301".equals(zspmDm)) {
//            syxxbMap.put("zszmDm", "");
//        }
//    }
//
//    /**
//     * 根据属期填充grid中的月份和其他数据
//     *
//     * @param skssqq
//     * @param skssqz
//     * @param syyxqq
//     * @param syyxqz
//     * @param cjfb
//     * @return
//     */
//    private List<Map<String, Object>> fillSbjsDrmbyf(Calendar skssqq, Calendar skssqz,
//                                                     Calendar syyxqq, Calendar syyxqz, Map<String, Object> cjfb) {
//        final List<Map<String, Object>> reList = new ArrayList<>();
//        final String sybh = (String) cjfb.get("hgbhssybh");
//        final String zspmDm = (String) cjfb.get("zspmDm");
//        final String wrwpfljsffDm = (String) cjfb.get("wrwpfljsffDm");
//        final String zszmDm = GyUtils.isNull(cjfb.get("zszmDm"))
//                ? (String) cjfb.get("tzzb") : (String) cjfb.get("zszmDm");
//        final String zywrwlbDm = (String) cjfb.get("zywrwlbDm");
//        final String fzspmDm = (String) cjfb.get("fzspmDm");
//        final Calendar start = Calendar.getInstance();
//        start.setTimeInMillis(Math.max(skssqq.getTimeInMillis(), syyxqq.getTimeInMillis()));
//        final Calendar end = Calendar.getInstance();
//        end.setTimeInMillis(Math.min(skssqz.getTimeInMillis(), syyxqz.getTimeInMillis()));
//        final List<Integer> skYfList = this.getYfList(start, end);
//        for (Integer syYf : skYfList) {
//            final Map<String, Object> map = new HashMap<>();
//            map.put("yf", String.valueOf(syYf));
//            map.put("hgbhssybh", sybh);
//            map.put("zspmDm", this.getMcForExport(zspmDm, "DM_GY_ZSPM", "ZSPMMC"));
//            map.put("wrwpfljsffDm", this.getMcForExport(wrwpfljsffDm, "DM_DJ_WRWPFLJSFF", "WRWPFLJSFFMC"));
//            map.put("fzspmDm", this.getMcForExport(fzspmDm, "DM_GY_ZSPM", "ZSPMMC"));
//            map.put("zywrwlbDm", this.getMcForExport(zywrwlbDm, "DM_DJ_ZYWRWLB", "ZYWRWLBMC").replace("|", "_"));
//            if (GyUtils.isNotNull(zszmDm)) {
//                if (!"5".equals(wrwpfljsffDm)) {
//                    map.put("zszmDm", this.getMcForExport(zszmDm, "DM_GY_ZSZM", "ZSZMMC"));
//                } else {
//                    map.put("tzzb", this.getMcForExport(zszmDm, "DM_GY_ZSZM", "ZSZMMC"));
//                }
//            }
//            reList.add(map);
//        }
//        return reList;
//    }
//
//    /**
//     * @param start start
//     * @param end   end
//     * @return java.util.List<java.lang.Integer>
//     * @name 获取两个日期之间的月份
//     * @description 相关说明
//     * @time 创建时间:2021/11/24 14:51
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private List<Integer> getYfList(Calendar start, Calendar end) {
//        final List<Integer> yfList = new ArrayList<Integer>();
//        final Integer startYf = start.get(Calendar.MONTH) + 1;
//        final Integer endYf = end.get(Calendar.MONTH) + 1;
//        if (startYf > endYf) {
//            return yfList;
//        }
//        for (int i = startYf; i <= endYf; i++) {
//            yfList.add(i);
//        }
//        return yfList;
//    }
//
    /**
     * 导入计算
     *
     * @param list
     * @param djxh
     * @param swjgDm
     * @param <T>
     * @return
     * @throws Exception
     */
    private <T> String drJs(List<T> list, String djxh, String swjgDm) {
        String msg = "";
        if (GyUtils.isNull(list)) {
            return msg;
        }
        if (DqswrVO.class.isInstance(list.get(0))) {
            this.drJs((List<DqswrVO>) list, new DqswrVO[0], djxh, swjgDm);
        } else if (GfsyxxVO.class.isInstance(list.get(0))) {
            this.drJs((List<GfsyxxVO>) list, new GfsyxxVO[0], djxh, swjgDm);
        } else if (ZssyxxVO.class.isInstance(list.get(0))) {
            this.drJs((List<ZssyxxVO>) list, new ZssyxxVO[0], djxh, swjgDm);
        } else if (CycssyxxVO.class.isInstance(list.get(0))) {
            msg = this.drJs((List<CycssyxxVO>) list, new CycssyxxVO[0], djxh, swjgDm);
        }
        return msg;
    }

    /**
     * 导入计算大气
     *
     * @param list
     * @param arr
     * @param djxh
     * @param swjgDm
     * @throws Exception
     */
    private void drJs(List<DqswrVO> list, DqswrVO[] arr, String djxh, String swjgDm) {
        SlReqVO slReqVO = new SlReqVO();
        PzxxReqVo pzxxReqVo = new PzxxReqVo();
        LoadZszmReqVo loadZszmReqVo = new LoadZszmReqVo();
        LoadSsjmxzReqVo loadSsjmxzReqVo = new LoadSsjmxzReqVo();
        final Map<Object, Object> slMap = new HashMap<>();
        final Map<Object, Object> zszmMap = new HashMap<>();
        final Map<Object, Wrdlpz> pzxxMap = new HashMap<>();
        List<SsjmxzVO> ssjmxzVOList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            final DqswrVO vo = list.get(i);
            // 新增的无uuid，给前台页面提供viewuuid作为唯一值
            vo.setViewuuid(UUID.randomUUID().toString().replaceAll("-", ""));
            final String zspmDm = vo.getZspmDm();
            final String zszmDm = vo.getZszmDm();
            final String skssqq = vo.getSkssqq();
            final String skssqz = vo.getSkssqz();
            String key = zspmDm;
            if (!zszmMap.containsKey(key)) {
                loadZszmReqVo.setSwjgDm(swjgDm);
                loadZszmReqVo.setZspmDm(zspmDm);
                final List<ZszmVo> zszmList = this.loadZszmList(loadZszmReqVo);
                zszmMap.put(key, zszmList);
            }
            vo.setZszmList((List<ZszmVo>) zszmMap.get(key));
            if (GyUtils.isNotNull(zszmDm)) {
                key = key + zszmDm;
            }
            if (!slMap.containsKey(key)) {
                log.info("获取税率-----------------");
                // 税率
                slReqVO.setSwjgdm(swjgDm);
                slReqVO.setZspmDm(zspmDm);
                slReqVO.setZszmDm(zszmDm);
                slReqVO.setSkssqq(skssqq);
                slReqVO.setSkssqz(skssqz);
                slReqVO.setZsxmDm(HBS_ZSXM_DM);
                final Map<String, Object> sl = this.getSl(slReqVO);
                slMap.put(key, sl.get("fdsl"));
                log.info("获取税率-----------------");
            }
            vo.setSl1(GYCastUtils.cast2Double(GYCastUtils.cast2Str(slMap.get(key))));
            if (!pzxxMap.containsKey(key)) {
                // 污染当量值
                pzxxReqVo.setZspmDm(zspmDm);
                pzxxReqVo.setZszmDm(zszmDm);
                pzxxReqVo.setSkssqq(skssqq);
                pzxxReqVo.setSkssqz(skssqz);
                pzxxReqVo.setSwjgDm(swjgDm);
                log.info("获取污染当量值-----------------");
                final List<Wrdlpz> pzxx = this.getPzxx(pzxxReqVo);
                if (GyUtils.isNotNull(pzxx) && GyUtils.isNotNull(pzxx.get(0))) {
                    pzxxMap.put(key, pzxx.get(0));
                }
                log.info("获取污染当量值-----------------");
            }
            if (!GyUtils.isNull(pzxxMap.get(key))) {
                vo.setWrdlz(GYCastUtils.cast2Double(pzxxMap.get(key).getWrdlz()));
            }
            // 大气水减免性质都一样，获取一次就行
            if (i == 0) {
                loadSsjmxzReqVo.setDjxh(djxh);
                loadSsjmxzReqVo.setSwjgDm(swjgDm);
                loadSsjmxzReqVo.setZspmDm(zspmDm);
                loadSsjmxzReqVo.setZszmDm(zszmDm);
                loadSsjmxzReqVo.setSkssqq(skssqq);
                loadSsjmxzReqVo.setSkssqz(skssqz);
                log.info("获取减免-----------------");
                ssjmxzVOList = this.loadSsjmxzDmList(loadSsjmxzReqVo).stream()
                        .filter(f -> !"0016064004".equals(f.getCode()) && !"0016064005".equals(f.getCode())).collect(Collectors.toList());
                log.info("获取减免-----------------");
            }
            vo.setJmxzOptions(ssjmxzVOList);
        }
    }

    /**
     * 导入计算固废
     *
     * @param list
     * @param arr
     * @param djxh
     * @param swjgDm
     * @throws Exception
     */
    private void drJs(List<GfsyxxVO> list, GfsyxxVO[] arr, String djxh, String swjgDm) {
        SlReqVO slReqVO = new SlReqVO();
        LoadSsjmxzReqVo loadSsjmxzReqVo = new LoadSsjmxzReqVo();
        LoadZszmReqVo loadZszmReqVo = new LoadZszmReqVo();
        final Map<Object, Object> zszmMap = new HashMap<>();
        final Map<Object, Object> slMap = new HashMap<>();
        List<SsjmxzVO> ssjmxzVOList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            final GfsyxxVO vo = list.get(i);
            // 新增的无uuid，给前台页面提供viewuuid作为唯一值
            vo.setViewuuid(UUID.randomUUID().toString().replaceAll("-", ""));
            final String zspmDm = vo.getZspmDm();
            final String zszmDm = vo.getZszmDm();
            final String skssqq = vo.getSkssqq();
            final String skssqz = vo.getSkssqz();
            String key = zspmDm;
            if (!zszmMap.containsKey(key)) {
                loadZszmReqVo.setSwjgDm(swjgDm);
                loadZszmReqVo.setZspmDm(zspmDm);
                final List<ZszmVo> zszmList = this.loadZszmList(loadZszmReqVo);
                zszmMap.put(key, zszmList);
            }
            vo.setZszmList((List<ZszmVo>) zszmMap.get(key));
            if (GyUtils.isNotNull(zszmDm)) {
                key = key + zszmDm;
            }
            if (!slMap.containsKey(key)) {
                log.info("获取税率-----------------");
                // 税率
                slReqVO.setSwjgdm(swjgDm);
                slReqVO.setZspmDm(zspmDm);
                slReqVO.setZszmDm(zszmDm);
                slReqVO.setSkssqq(skssqq);
                slReqVO.setSkssqz(skssqz);
                slReqVO.setZsxmDm(HBS_ZSXM_DM);
                final Map<String, Object> sl = this.getSl(slReqVO);
                slMap.put(key, sl.get("fdsl"));
                log.info("获取税率-----------------");
            }
            vo.setSl1(GYCastUtils.cast2Double(GYCastUtils.cast2Str(slMap.get(key))));
            // 固废减免性质都一样，获取一次就行
            if (i == 0) {
                loadSsjmxzReqVo.setDjxh(djxh);
                loadSsjmxzReqVo.setSwjgDm(swjgDm);
                loadSsjmxzReqVo.setZspmDm(zspmDm);
                loadSsjmxzReqVo.setZszmDm(zszmDm);
                loadSsjmxzReqVo.setSkssqq(skssqq);
                loadSsjmxzReqVo.setSkssqz(skssqz);
                log.info("获取减免-----------------");
                ssjmxzVOList = this.loadSsjmxzDmList(loadSsjmxzReqVo).stream()
                        .filter(f -> "0016064004".equals(f.getCode())).collect(Collectors.toList());
                log.info("获取减免-----------------");
            }
            vo.setJmxzOptions(ssjmxzVOList);
        }
    }

    /**
     * 导入计算噪声
     *
     * @param list
     * @param arr
     * @param djxh
     * @param swjgDm
     */
    private void drJs(List<ZssyxxVO> list, ZssyxxVO[] arr, String djxh, String swjgDm) {
        final HbsJcxxDTO hbsjcxxcjb = getHbsjcxxcjb(djxh, swjgDm);
        final List<SyxxDTO> syxxGridList = hbsjcxxcjb.getSyxxGridList();
        SlReqVO slReqVO = new SlReqVO();
        final Map<Object, Object> slMap = new HashMap<>();
        // 已采集的税源信息
        final Map<String, Map<String, Object>> syxxMap = new HashMap<>();
        if (!GyUtils.isNull(syxxGridList)) {
            for (SyxxDTO syxxDTO : syxxGridList) {
                //税源编号
                String key = syxxDTO.getHgbhssybh();
                syxxMap.put(key, BeanUtils.toMap(syxxDTO));
            }
        }
        for (ZssyxxVO vo : list) {
            // 新增的无uuid，给前台页面提供viewuuid作为唯一值
            vo.setViewuuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (!GyUtils.isNull(vo.getZssdDm())) {
                final Double cbfbs = MathUtils.subtract(MathUtils.round(vo.getJcfbs1(), 0),
                        this.getZsbzz(vo.getHgbhssybh(), vo.getZssdDm(), syxxMap));
                final String zspmDm = this.getZspmDmZs(cbfbs);
                vo.setZspmDm(zspmDm);
                if (!slMap.containsKey(zspmDm)) {
                    log.info("获取税率-----------------");
                    // 税率
                    slReqVO.setSwjgdm(swjgDm);
                    slReqVO.setZspmDm(zspmDm);
                    slReqVO.setZszmDm(null);
                    slReqVO.setSkssqq(vo.getSkssqq());
                    slReqVO.setSkssqz(vo.getSkssqz());
                    slReqVO.setZsxmDm(HBS_ZSXM_DM);
                    final Map<String, Object> sl = this.getSl(slReqVO);
                    slMap.put(zspmDm, sl.get("fdsl"));
                    log.info("获取税率-----------------");
                }
                vo.setSl1(GYCastUtils.cast2Double(GYCastUtils.cast2Str(slMap.get(zspmDm))));
            }
        }
    }

    /**
     * 导入计算抽样
     *
     * @param list
     * @param arr
     * @param djxh
     * @param swjgDm
     * @return
     */
    private String drJs(List<CycssyxxVO> list, CycssyxxVO[] arr, String djxh, String swjgDm) {
        String msg = "";
        SlReqVO slReqVO = new SlReqVO();
        PzxxReqVo pzxxReqVo = new PzxxReqVo();
        final LoadZszmReqVo loadZszmReqVo = new LoadZszmReqVo();
        final LoadSsjmxzReqVo loadSsjmxzReqVo = new LoadSsjmxzReqVo();
        final LoadZspmReqVo loadZspmReqVo = new LoadZspmReqVo();
        final Map<Object, Wrdlpz> pzxxMap = new HashMap<>();
        final Map<Object, Object> acslMap = new HashMap<>();
        final Map<Object, Object> cyslMap = new HashMap<>();
        final Map<Object, Object> zszmMap = new HashMap<>();
        final Map<Object, Object> zszpmMap = new HashMap<>();
        final Map<Object, Object> jmMap = new HashMap<>();
        for (CycssyxxVO vo : list) {
            // 新增的无uuid，给前台页面提供viewuuid作为唯一值
            vo.setViewuuid(UUID.randomUUID().toString().replaceAll("-", ""));
            final String zspmDm = vo.getZspmDm();
            final String zszmDm = vo.getTzzb();
            final String skssqq = vo.getSkssqq();
            final String skssqz = vo.getSkssqz();
            final String zywrwlbDm = vo.getZywrwlbDm();
            final String fzspmDm = vo.getFzspmDm();
            String key = zspmDm;
            if (!zszmMap.containsKey(key)) {
                loadZszmReqVo.setSwjgDm(swjgDm);
                loadZszmReqVo.setZspmDm(zspmDm);
                final List<ZszmVo> zszmList = this.loadZszmList(loadZszmReqVo);
                zszmMap.put(key, zszmList);
            }
            vo.setZszmList((List<ZszmVo>) zszmMap.get(key));
            String wrwKey = zywrwlbDm;
            if (!zszpmMap.containsKey(wrwKey)) {
                loadZspmReqVo.setSwjgDm(swjgDm);
                loadZspmReqVo.setZywrwlbDm(zywrwlbDm);
                loadZspmReqVo.setSfglbz("Y");
                if (!GyUtils.isNull(fzspmDm)) {
                    loadZspmReqVo.setFzspmDm(fzspmDm);
                }
                final List<ZspmVo> zspmList = this.loadZspmList(loadZspmReqVo);
                zszpmMap.put(wrwKey, zspmList);
            }
            vo.setZspmList((List<ZspmVo>) zszpmMap.get(wrwKey));
            if (GyUtils.isNotNull(zszmDm)) {
                key = key + zszmDm;
            }
            final Boolean ac = skssqq.equals(skssqz);
            if (ac) {
                // 返回减免
                // 固废、大气水减免性质都一样，各获取一次就行
                if (!jmMap.containsKey(wrwKey) && !"N".equals(zywrwlbDm)) {
                    loadSsjmxzReqVo.setDjxh(djxh);
                    loadSsjmxzReqVo.setSwjgDm(swjgDm);
                    loadSsjmxzReqVo.setZspmDm(zspmDm);
                    loadSsjmxzReqVo.setZszmDm(zszmDm);
                    loadSsjmxzReqVo.setSkssqq(skssqq);
                    loadSsjmxzReqVo.setSkssqz(skssqz);
                    log.info("获取减免-----------------");
                    List<SsjmxzVO> ssjmxzVOList = this.loadSsjmxzDmList(loadSsjmxzReqVo);
                    if ("S".equals(zywrwlbDm)) {
                        ssjmxzVOList = ssjmxzVOList.stream()
                                .filter(f -> "0016064004".equals(f.getCode())).collect(Collectors.toList());
                    } else {
                        ssjmxzVOList = ssjmxzVOList.stream()
                                .filter(f -> !"0016064004".equals(f.getCode())).collect(Collectors.toList());
                    }
                    log.info("获取减免-----------------");
                    jmMap.put(wrwKey, ssjmxzVOList);
                }
                vo.setJmxzOptions((List<SsjmxzVO>) jmMap.get(wrwKey));
                if (!acslMap.containsKey(key)) {
                    slReqVO.setSwjgdm(swjgDm);
                    slReqVO.setZspmDm(zspmDm);
                    slReqVO.setZszmDm(zszmDm);
                    slReqVO.setSkssqq(skssqq);
                    slReqVO.setSkssqz(skssqz);
                    slReqVO.setZsxmDm(HBS_ZSXM_DM);
                    final Map<String, Object> sl = this.getSl(slReqVO);
                    acslMap.put(key, sl.get("fdsl"));
                }
                vo.setSl1(GYCastUtils.cast2Double(GYCastUtils.cast2Str(acslMap.get(key))));
            } else {
                if (!cyslMap.containsKey(key)) {
                    slReqVO.setSwjgdm(swjgDm);
                    slReqVO.setZspmDm(zspmDm);
                    slReqVO.setZszmDm(zszmDm);
                    slReqVO.setSkssqq(skssqq);
                    slReqVO.setSkssqz(skssqz);
                    slReqVO.setZsxmDm(HBS_ZSXM_DM);
                    final Map<String, Object> sl = this.getSl(slReqVO);
                    cyslMap.put(key, sl.get("fdsl"));
                }
                vo.setSl1(GYCastUtils.cast2Double(GYCastUtils.cast2Str(cyslMap.get(key))));
            }
            if (!ac) {
                if (!pzxxMap.containsKey(key)) {
                    pzxxReqVo.setZspmDm(zspmDm);
                    pzxxReqVo.setZszmDm(zszmDm);
                    pzxxReqVo.setSkssqq(skssqq);
                    pzxxReqVo.setSkssqz(skssqz);
                    pzxxReqVo.setSwjgDm(swjgDm);
                    pzxxReqVo.setSblx("01");
                    final List<Wrdlpz> pzxx = this.getPzxx(pzxxReqVo);
                    if (GyUtils.isNull(pzxx) || !"Y".equals(pzxx.get(0).getSfcycsbz())) {
                        msg = "无特征值配置,所选征收品目为：" + vo.getZspmDm();
                        if (!GyUtils.isNull(vo.getTzzb())) {
                            msg += ",特征指标为：" + vo.getTzzb();
                        }
                        continue;
                    }
                    pzxxMap.put(key, pzxx.get(0));
                }
                final Wrdlpz wrdlpz = pzxxMap.get(key);
                final String jsjsdwmc = wrdlpz.getJsjsdwmc();
                final String tzzbz1 = wrdlpz.getTzzbz1();
                final String tzzjsf = wrdlpz.getTzzjsf();
                final String sfczxj = wrdlpz.getSfczxj();
                final Double tzcwxs = GYCastUtils.cast2Double(wrdlpz.getTzcwxs());
                final Double wrdlz = GYCastUtils.cast2Double(wrdlpz.getWrdlz());
                vo.setJsjsdwmc(jsjsdwmc);
                vo.setTzzjsf(tzzjsf);
                vo.setSfczxj(sfczxj);
                vo.setTzzbz1(tzzbz1);
                vo.setWrdlz(wrdlz);
                if (!GyUtils.isNull(tzzjsf) && "3".equals(tzzjsf)) {
                    if ("N".equals(vo.getSfczxj())) {
                        vo.setTzcwxs(tzcwxs);
                    } else {
                        if (GyUtils.isNull(vo.getTzcwxs())) {
                            vo.setTzcwxs(tzcwxs);
                        }
                    }
                }
            } else if (!GyUtils.isNull(vo.getSsjmxzDm())
                    && (GyUtils.isNull(vo.getJmse()) || vo.getJmse() == 0)) {
                final Map<String, Object> csMap = NsrglYhUtils.getSsjmxzJmfdedslgx(vo.getSsjmxzDm(), swjgDm);
                final Double jmfd = GYCastUtils.cast2Double(csMap.get("jmfd"));
                final Double jmed = GYCastUtils.cast2Double(csMap.get("jmed"));
                final Double jmsl = GYCastUtils.cast2Double(csMap.get("jmsl"));
                final Double wrdls = !GyUtils.isNull(vo.getWrdls()) ? vo.getWrdls() : 0.0;
                vo.setYnse(MathUtils.round(MathUtils.multiple(wrdls.doubleValue(), vo.getSl1()), 2));
                final Double jmse = getMrjmseBySsjmxz(
                        GYSbUtils.getJmzlxDmBySsjmxzDm(vo.getSsjmxzDm()),
                        wrdls.doubleValue(), vo.getYnse(), vo.getSl1(), jmfd, jmed, jmsl);
                vo.setJmse(jmse);
            }
        }
        return msg;
    }
//
    /**
     * 获取噪声标准值
     *
     * @param sybh
     * @param zssdDm
     * @param syxxMap
     * @return
     */
    private Double getZsbzz(String sybh, String zssdDm, Map<String, Map<String, Object>> syxxMap) {
        Double bzz = 0.0;
        final Map<String, Object> map = syxxMap.get(sybh);
        if (!GyUtils.isNull(map)) {
            bzz = (Double) (zssdDm.equals("1") ? map.get("bzzzj") : map.get("bzzyj"));
        }
        return GyUtils.isNull(bzz) ? 0.0 : bzz;
    }

    /**
     * 计算噪声征收品目
     *
     * @param cbfbsD
     * @return
     */
    private String getZspmDmZs(Double cbfbsD) {
        String zspmDm = "";
        if (cbfbsD >= 0 && cbfbsD <= 3) {
            //超1-3分贝
            zspmDm = DM_GY_ZSPM_C1T3FB;
        } else if (cbfbsD >= 4 && cbfbsD <= 6) {
            zspmDm = DM_GY_ZSPM_C4T6FB;
        } else if (cbfbsD >= 7 && cbfbsD <= 9) {
            zspmDm = DM_GY_ZSPM_C7T9FB;
        } else if (cbfbsD >= 10 && cbfbsD <= 12) {
            zspmDm = DM_GY_ZSPM_C10T12FB;
        } else if (cbfbsD >= 13 && cbfbsD <= 15) {
            zspmDm = DM_GY_ZSPM_C13T15FB;
        } else if (cbfbsD >= 16) {
            zspmDm = DM_GY_ZSPM_C16YS;
        }
        return zspmDm;
    }


//    /**
//     * 小数格式经纬度转换为度分秒格式
//     *
//     * @param xs
//     * @return
//     */
//    private String xsStringToJwd(String xs) {
//        String dfmJwd = "";
//        if (!GyUtils.isNull(xs)) {
//            final Double xsNum = Double.valueOf(xs);
//            final int d = xsNum.intValue();
//            final double subtract = MathUtils.subtract(xsNum, d);
//            double fdou = 0.0;
//            if (xsNum >= 0) {
//                fdou = MathUtils.multiple(subtract, 60);
//            } else if (xsNum < 0) {
//                fdou = MathUtils.multiple(subtract, -60);
//            }
//            final int f = (int) fdou;
//            final double m = MathUtils.round(MathUtils.multiple(MathUtils.subtract(fdou, f), 60), 2);
//            dfmJwd = d + "°" + f + "′" + m + "″";
//        }
//        return dfmJwd;
//    }
//
    /**
     * 经纬度转换为小数格式
     *
     * @param jwd
     * @return
     */
    private String jwdToXsString(String jwd) {
        String xsJwd = "";
        if (!GyUtils.isNull(jwd)) {
            final String[] jwdArr = jwd.split("°|′|″");
            final Double d = Double.valueOf(jwdArr[0]);
            final Double f = Double.valueOf(jwdArr[1]);
            final Double m = Double.valueOf(jwdArr[2]);
            if (d >= 0) {
                xsJwd = String.valueOf(d + f / 60 + m / 3600);
            } else if (d < 0) {
                xsJwd = "-" + String.valueOf(Math.abs(d) + f / 60 + m / 3600);
            }
        }
        return xsJwd;
    }

    /**
     * 保存污染物明细信息到本地表
     * @param saveYwbw
     * @param djxh
     * @return
     */
    public String saveHbscjYwbw(HbssyxxcjSaveReqVo saveYwbw, String djxh){
        //查询原基础信息
        final HbsJcxxVO jcxx = jcxxcjbService.queryJcxxFromDao(djxh);
        if (GyUtils.isNull(jcxx) || GyUtils.isNull(jcxx.getHbsjcxxcjb())) {
            throw ServiceExceptionUtil.exception(500, "环境保护税基础信息采集表未采集");
        }
        final Set<String> sySet = new HashSet<String>();  //税源编号
        if (!GyUtils.isNull(jcxx.getSyxxGridList()) && !GyUtils.isNull(jcxx.getSyxxGridList())) {
            for (SyxxVO vo : jcxx.getSyxxGridList()) {
                sySet.add(vo.getHgbhssybh());
            }
        }
        final List<String> errSyList = new ArrayList<String>();
        if (!GyUtils.isNull(saveYwbw)) {
            if (!GyUtils.isNull(saveYwbw.getBccjList())) {//大气水
                for (BccjxxVO vo : saveYwbw.getBccjList()) {
                    if (!sySet.contains(vo.getHgbhssybh())) {
                        errSyList.add(vo.getHgbhssybh());
                    }
                }
                this.dqswrjcxxcjbService.saveDqswrxx(saveYwbw.getBccjList(), djxh);
            }
            if (!GyUtils.isNull(saveYwbw.getZscjxxList()) ) {//噪声
                for (ZscjxxVO vo : saveYwbw.getZscjxxList()) {
                    if (!sySet.contains(vo.getHgbhssybh())) {
                        errSyList.add(vo.getHgbhssybh());
                    }
                }
                this.zsjcxxcjbService.saveZs(saveYwbw.getZscjxxList(), djxh);
            }
            if (!GyUtils.isNull(saveYwbw.getGfcjxxList()) ) {//固废
                for (GfcjxxVO vo : saveYwbw.getGfcjxxList()) {
                    if (!sySet.contains(vo.getHgbhssybh())) {
                        errSyList.add(vo.getHgbhssybh());
                    }
                }
                this.gtfwwrfzssService.saveGf(saveYwbw.getGfcjxxList(), djxh);
            }

            if (!GyUtils.isNull(errSyList)) {
                throw ServiceExceptionUtil.exception(500, "无效税源编号：" + StringUtils.join(errSyList.toArray(new String[0]), ","));
            }
            if (!GyUtils.isNull(saveYwbw.getPwxscjbxxList())) {//产排污
                this.pwxscjbService.saveCpwxs(saveYwbw.getPwxscjbxxList(), djxh);
            }
        }
        final String cjbz = "Y";
        return cjbz;
    }
    /**
     * @name 数据交换处理
     * @description 相关说明
     * @time 创建时间:2024年06月06日下午02:20:08
     * @param sjjhlxDm
     * @param bwnr
     * @param ywuuid
     * @return {@link Object }
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private CommonResult<Object> sjjhcl(String sjjhlxDm, String bwnr, String ywuuid) {
        CommonResult<Object> result = new CommonResult<>();
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm(sjjhlxDm);
        sjjhDTO.setYwbm(YzpzzlEnum.HJBHS.getDm());
        sjjhDTO.setYwuuid(ywuuid);
        String djxh = ControllerUtils.getDjxh();
        djxh = MockUtils.mockString("djxh",djxh);
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(ControllerUtils.getNsrsbh());
        sjjhDTO.setXzqhszDm(ControllerUtils.getXzqhszDm());
        sjjhDTO.setBwnr(bwnr);
        result = sjjhService.saveSjjhJob(sjjhDTO);
        return result;
    }
    @Override
    public String saveHbssyxxcj(HbsJcxxVO jcxx){
        //基础信息VO
        final String djxh = jcxx.getHbsjcxxcjb().getDjxh();
        //查询现有环保税基础信息
        final HbsJcxxVO oldJcxx = jcxxcjbService.queryJcxxFromDao(djxh);
//        this.createHbssybhnew(jcxx);
        final LocalDateTime lrrq = DateUtils.toLocalDateTime(new Date());
        final String swrydm = "xgr";
        final String sjgsdq = ZnsbSessionUtils.getSwjgDm();
        final ZnsbDjHbsjcxxcjbDO cjbDO = new ZnsbDjHbsjcxxcjbDO();
        final List<SyxxVO> insertSyxxList = new ArrayList<SyxxVO>();
        final List<SyxxVO> updateSyxxList = new ArrayList<SyxxVO>();
        final List<SyxxVO> deleteSyxxList = new ArrayList<SyxxVO>();
        String hbsjcxxuuid = "";
        final Map<String, SyxxVO> setMap = new HashMap<String, SyxxVO>();
        //如果基础表存在数据，执行修改操作
        if (GyUtils.isNotNull(oldJcxx)&&GyUtils.isNotNull(oldJcxx.getHbsjcxxcjb())) { //update
            BeanUtil.copyProperties(oldJcxx.getHbsjcxxcjb(), jcxx.getHbsjcxxcjb(), true);
            BeanUtil.copyProperties(jcxx.getHbsjcxxcjb(), cjbDO,  true);
            cjbDO.setXgrDm(swrydm);
            cjbDO.setXgrq(lrrq);
            cjbDO.setSjgsdq(sjgsdq);
            hbsjcxxcjbMapper.update(cjbDO,new QueryWrapper<ZnsbDjHbsjcxxcjbDO>().lambda().eq(ZnsbDjHbsjcxxcjbDO::getHbsjcxxuuid,cjbDO.getHbsjcxxuuid()));

            if (!GyUtils.isNull(oldJcxx.getSyxxGridList())) {
                for (SyxxVO syxx : oldJcxx.getSyxxGridList()) {
                    setMap.put(syxx.getSyuuid(), syxx);
                }
                for (SyxxVO syxx : jcxx.getSyxxGridList()) {
                    if(jcxx.getSyuuids().contains(syxx.getSyuuid())){
                        deleteSyxxList.add(syxx);
                        continue;
                    }
                    if (setMap.keySet().contains(syxx.getSyuuid())) {
                        updateSyxxList.add(syxx);
                        setMap.remove(syxx.getSyuuid());
                    } else {
                        if (GyUtils.isNull(syxx.getSyuuid())) {
                            syxx.setSyuuid(GyUtils.getUuid());
                        }
                        hbsjcxxuuid = oldJcxx.getHbsjcxxcjb().getHbsjcxxuuid();
                        syxx.setHbsjcxxuuid(oldJcxx.getHbsjcxxcjb().getHbsjcxxuuid());
                        insertSyxxList.add(syxx);
                    }
                }
                deleteSyxxList.addAll(setMap.values());
            } else {
                insertSyxxList.addAll(jcxx.getSyxxGridList());
            }
        } else { //insert
            hbsjcxxuuid = GyUtils.getUuid();
            jcxx.getHbsjcxxcjb().setHbsjcxxuuid(hbsjcxxuuid);
            BeanUtil.copyProperties(jcxx.getHbsjcxxcjb(), cjbDO,true);
            cjbDO.setLrrDm(swrydm);
            cjbDO.setLrrq(lrrq);
            cjbDO.setSjgsdq(sjgsdq);
            hbsjcxxcjbMapper.insert(cjbDO);
            insertSyxxList.addAll(jcxx.getSyxxGridList());
        }

        if (!GyUtils.isNull(insertSyxxList)) {
            for (SyxxVO syxx : insertSyxxList) {
                if (GyUtils.isNull(syxx.getSyuuid())) {
                    syxx.setSyuuid(GyUtils.getUuid());
                }

                if (GyUtils.isNull(syxx.getHbsjcxxuuid())) {
                    syxx.setHbsjcxxuuid(jcxx.getHbsjcxxcjb().getHbsjcxxuuid());
                }
                syxx.setLrrDm(swrydm);
                syxx.setSjgsdq(sjgsdq);
                syxx.setLrrq(DateUtils.getSystemCurrentTime(0));
                ZnsbDjHbsjcxxcjSyxxbDO syxxDO = BeanUtils.toBean(syxx, ZnsbDjHbsjcxxcjSyxxbDO.class);
                hbsjcxxcjbsyxxMapper.insert(syxxDO);
            }
        }

        if (!GyUtils.isNull(updateSyxxList)) {
            for (SyxxVO vo : updateSyxxList) {
                vo.setSjgsdq(sjgsdq);
                vo.setXgrDm(swrydm);
                vo.setXgrq(DateUtils.getSystemCurrentTime(0));
                hbsjcxxcjbsyxxMapper.update(BeanUtils.toBean(vo, ZnsbDjHbsjcxxcjSyxxbDO.class),new QueryWrapper<ZnsbDjHbsjcxxcjSyxxbDO>().lambda().eq(ZnsbDjHbsjcxxcjSyxxbDO::getSyuuid,vo.getSyuuid()));
            }
        }
        if (!GyUtils.isNull(deleteSyxxList)) {
            for (SyxxVO vo : deleteSyxxList) {
                vo.setSjgsdq(sjgsdq);
                vo.setXgrDm(swrydm);
                vo.setXgrq(DateUtils.getSystemCurrentTime(0));
                vo.setYxbz("N");
                hbsjcxxcjbsyxxMapper.update(BeanUtils.toBean(vo, ZnsbDjHbsjcxxcjSyxxbDO.class),new QueryWrapper<ZnsbDjHbsjcxxcjSyxxbDO>().lambda().eq(ZnsbDjHbsjcxxcjSyxxbDO::getSyuuid,vo.getSyuuid()));
            }
        }
//        this.delSyxxZc(djxh);//保存基础信息表时清空暂存数据
//        this.delPwxkzbhZc(jcxx.getHbsjcxxcjb());
//        this.updateSbjsSybh(jcxx);//保存基础信息表时如果有已采集的申报计算税源对应的税源编号发生变化，则更新申报计算的对应税源编号
        return hbsjcxxuuid;
    }
    /**
     * @param jcxx jcxx
     * @name 生成税源编号
     * @description 相关说明
     * @time 创建时间:2020-10-30 下午 14:18:25
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void createHbssybhnew(HbsJcxxVO jcxx)  {
        //生成税源编号 一个排口一个税源编号  一个排口可有多条数据
        //部分数据更新排口，变更排口有变化的数据的税源编号
        //更新排口时 如果一个排口全部更新为同一个排口，税源编号不变
        //更新排口，如果所有排口变更，且变更后新排口不相同，第一新排口税源编号不变，其余排口税源编号变更
        final HbsJcxxVO oldJcxx = jcxxcjbService.queryJcxxFromDao(jcxx.getHbsjcxxcjb().getDjxh());
        final Map<String, String> sybhMap = new HashMap<String, String>();
        final Map<String, Integer> pkCount = new HashMap<String, Integer>();
        final Map<String, String> uuidAndpk = new HashMap<String, String>();
        if (!GyUtils.isNull(oldJcxx) && !GyUtils.isNull(oldJcxx.getSyxxGridList())) {
            for (SyxxVO sy : oldJcxx.getSyxxGridList()) {
                //税源编号生成规则添加排污许可证编号
                final String key = sy.getZywrwlbDm() + "_" + sy.getPfkbh() + "_" + sy.getPwxkzbh();
                if (GyUtils.isNull(sy.getHgbhssybh())) {
                    sy.setHgbhssybh(
                            this.createHbssybh(sy.getZywrwlbDm(), jcxx.getHbsjcxxcjb().getXzqhszDm(), String.valueOf(DateUtils.getSystemCurrentTime().get(Calendar.YEAR)), sy.getPwxkzbh()));
                }
                sybhMap.put(key, sy.getHgbhssybh());
                if (!pkCount.containsKey(key)) {
                    pkCount.put(key, 0);
                }
                pkCount.put(key, pkCount.get(key) + 1);
                uuidAndpk.put(sy.getSyuuid(), key);
            }
        }
        if (!GyUtils.isNull(jcxx) && !GyUtils.isNull(jcxx.getSyxxGridList())) {
            final List<SyxxVO> syxxGridLb = jcxx.getSyxxGridList();
            for (SyxxVO sy : syxxGridLb) {
                final String key = sy.getZywrwlbDm() + "_" + sy.getPfkbh() + "_" + sy.getPwxkzbh();
                if (!sybhMap.containsKey(key)) {
                    if (!GyUtils.isNull(sy.getSyuuid())) {//key不在sybhMap中但是有syuuid，说明是已存在的税源修改信息导致key值变化。
                        final String oldPk = uuidAndpk.get(sy.getSyuuid());//通过syuuid找出原来的排口信息
                        pkCount.put(oldPk, pkCount.get(oldPk) - 1);//计数减一
                    }
                }
            }
            final Set<String> update = new HashSet<String>();
            for (SyxxVO sy : syxxGridLb) {
                final String key = sy.getZywrwlbDm() + "_" + sy.getPfkbh() + "_" + sy.getPwxkzbh();
                if (!GyUtils.isNull(sy.getSyuuid()) && pkCount.get(uuidAndpk.get(sy.getSyuuid())) == 0 && !update.contains(uuidAndpk.get(sy.getSyuuid()))) {
                    sybhMap.put(key, sy.getHgbhssybh());
                    update.add(uuidAndpk.get(sy.getSyuuid()));
                } else if (!sybhMap.containsKey(key)) {
                    sybhMap.put(key, this.createHbssybh(sy.getZywrwlbDm(),
                            jcxx.getHbsjcxxcjb().getXzqhszDm(), String.valueOf(DateUtils.getSystemCurrentTime().get(Calendar.YEAR)), sy.getPwxkzbh()));
                }
                sy.setHgbhssybh(sybhMap.get(key));
            }
        }
    }
    /**
     * @param wrwlbDm wrwlbDm
     * @param xzqhDm  xzqhDm
     * @param year    year
     * @param pwxkzbh pwxkzbh
     * @return String
     * @name 生成税源编号
     * @description 相关说明
     * @time 创建时间:2020-08-03 下午 16:14:45
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private String createHbssybh(String wrwlbDm, String xzqhDm, String year, String pwxkzbh){
//        final Map<String, Object> params = new HashMap<String, Object>();
//        params.put("zywrwlbDm", wrwlbDm);
//        params.put("xzqhDm", xzqhDm);
//        params.put("year", year);
//        params.put("pwxkzbh", pwxkzbh);
//        final List<Map<String, Object>> djhlist = HxzgSequenceUtils.getTySequence("HBSSYBH", params, 1);
//        NsrglUtils.checkNullException("获取HBSSYBH单据号为空：请检查：sword_billnone中的单据号：HBSSYBH", djhlist);
//        final String sybh = (String) ((Map) djhlist.get(0)).get("djh");
//        NsrglUtils.checkNullException("获取HBSSYBH单据号为空：请检查：sword_billnone中的单据号：HBSSYBH", sybh);
        String sybh = "111111";
        return sybh;
    }
    private static List<Map<String, Object>> findListInMapList(String key, String value, List<Map<String, Object>> list) {
        final List<Map<String, Object>> reslist = new ArrayList<Map<String, Object>>();
        if (list != null && list.size() > 0) {
            for (Map<String, Object> map : list) {
                if (map != null && !map.isEmpty()) {
                    final String str = (String) map.get(key);
                    if (!GyUtils.isNull(value) && value.equals(str)) {
                        reslist.add(map);
                    }
                }
            }
        }
        return reslist;
    }
    public static double getMrjmseBySsjmxz(String jmzlxDm, double jsyj, double ynse, double fdsl, double jmfd, double jmed, double jmsl) {
        double jmse = 0.00;
        double sljmje = 0.00;
        if ("02".equals(jmzlxDm)) {
            jmse = ynse;
        } else if ("01".equals(jmzlxDm)) {
            if (jmed > 0) {
                jmse = jmed > ynse ? ynse : jmed;
            } else if (jmsl > 0 && fdsl > jmsl) {
                sljmje = MathUtils.multiple(jsyj, MathUtils.subtract(fdsl, jmsl));
                jmse = sljmje > ynse ? ynse : sljmje;
            } else if (jmfd > 0 && jmfd < 1) {
                jmse = MathUtils.multiple(ynse, jmfd);
            }
        }
        return MathUtils.round(jmse, 6);
    }
}
