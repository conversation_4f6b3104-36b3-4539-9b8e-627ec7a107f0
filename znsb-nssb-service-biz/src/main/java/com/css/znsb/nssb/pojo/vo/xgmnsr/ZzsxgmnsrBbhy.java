package com.css.znsb.nssb.pojo.vo.xgmnsr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class ZzsxgmnsrBbhy {

    @JsonProperty("Response")
    private ResponseDTO response;

    @NoArgsConstructor
    @Data
    public static class ResponseDTO {
        @JsonProperty("RequestId")
        private String requestId;
        @JsonProperty("Error")
        private Object error;
        @JsonProperty("Data")
        private DataDTO data;

        @NoArgsConstructor
        @Data
        public static class DataDTO {
            @JsonProperty("Result")
            private ResultDTO result;
            @JsonProperty("Error")
            private Object error;
            @JsonProperty("Success")
            private Boolean success;
            @JsonProperty("ErrParams")
            private Object errParams;

            @NoArgsConstructor
            @Data
            public static class ResultDTO {
                @JsonProperty("pch")
                private String pch;
                @JsonProperty("mxjgList")
                private List<MxjgListDTO> mxjgList;

                @NoArgsConstructor
                @Data
                public static class MxjgListDTO {
                    @JsonProperty("nsrxx")
                    private NsrxxDTO nsrxx;
                    @JsonProperty("sbsx")
                    private SbsxDTO sbsx;
                    @JsonProperty("ytxx")
                    private YtxxDTO ytxx;
                    @JsonProperty("fzxx")
                    private FzxxDTO fzxx;
                    @JsonProperty("qcxx")
                    private QcxxDTO qcxx;

                    @NoArgsConstructor
                    @Data
                    public static class NsrxxDTO {
                        @JsonProperty("@class")
                        private String _$Class115;// 
                        @JsonProperty("djxh")
                        private String djxh;
                        @JsonProperty("nsrsbh")
                        private String nsrsbh;
                        @JsonProperty("shxydm")
                        private String shxydm;
                        @JsonProperty("yxbz")
                        private Object yxbz;
                        @JsonProperty("ynsrsbh")
                        private Object ynsrsbh;
                        @JsonProperty("gdslxDm")
                        private Object gdslxDm;
                        @JsonProperty("ssdabh")
                        private Object ssdabh;
                        @JsonProperty("nsrmc")
                        private String nsrmc;
                        @JsonProperty("djzclxDm")
                        private String djzclxDm;
                        @JsonProperty("djzclxMc")
                        private Object djzclxMc;
                        @JsonProperty("scjydz")
                        private String scjydz;
                        @JsonProperty("scjydzxzqhszDm")
                        private String scjydzxzqhszDm;
                        @JsonProperty("nsrztDm")
                        private String nsrztDm;
                        @JsonProperty("hyDm")
                        private String hyDm;
                        @JsonProperty("hyMc")
                        private Object hyMc;
                        @JsonProperty("zcdz")
                        private String zcdz;
                        @JsonProperty("zcdzxzqhszDm")
                        private Object zcdzxzqhszDm;
                        @JsonProperty("jdxzDm")
                        private String jdxzDm;
                        @JsonProperty("dwlsgxDm")
                        private String dwlsgxDm;
                        @JsonProperty("gdghlxDm")
                        private Object gdghlxDm;
                        @JsonProperty("zgswjDm")
                        private String zgswjDm;
                        @JsonProperty("zgswskfjDm")
                        private String zgswskfjDm;
                        @JsonProperty("ssglyDm")
                        private Object ssglyDm;
                        @JsonProperty("lrrDm")
                        private Object lrrDm;
                        @JsonProperty("lrrq")
                        private Object lrrq;
                        @JsonProperty("xgrDm")
                        private Object xgrDm;
                        @JsonProperty("xgrq")
                        private Object xgrq;
                        @JsonProperty("zzjgDm")
                        private Object zzjgDm;
                        @JsonProperty("sjgsdq")
                        private Object sjgsdq;
                        @JsonProperty("djrq")
                        private String djrq;
                        @JsonProperty("djjgDm")
                        private Object djjgDm;
                        @JsonProperty("nsrZy")
                        private Object nsrZy;
                        @JsonProperty("kzztdjlxDm")
                        private Object kzztdjlxDm;
                        @JsonProperty("fddbrxm")
                        private String fddbrxm;
                        @JsonProperty("fddbrsfzjlxDm")
                        private String fddbrsfzjlxDm;
                        @JsonProperty("fddbrsfzjhm")
                        private String fddbrsfzjhm;
                        @JsonProperty("kqccsztdjbz")
                        private Object kqccsztdjbz;
                        @JsonProperty("fjmqybz")
                        private String fjmqybz;
                        @JsonProperty("swdjblbz")
                        private Object swdjblbz;
                        @JsonProperty("sjtbSj")
                        private Object sjtbSj;
                        @JsonProperty("nsrztlxDm")
                        private Object nsrztlxDm;
                        @JsonProperty("zcdlxdh")
                        private Object zcdlxdh;
                        @JsonProperty("zcdyzbm")
                        private Object zcdyzbm;
                        @JsonProperty("scjydlxdh")
                        private String scjydlxdh;
                        @JsonProperty("scjydyzbm")
                        private Object scjydyzbm;
                        @JsonProperty("hsfsDm")
                        private String hsfsDm;
                        @JsonProperty("cyrs")
                        private Object cyrs;
                        @JsonProperty("wjcyrs")
                        private Object wjcyrs;
                        @JsonProperty("hhrs")
                        private Object hhrs;
                        @JsonProperty("ggrs")
                        private Object ggrs;
                        @JsonProperty("gdgrs")
                        private Object gdgrs;
                        @JsonProperty("zzjglxDm")
                        private Object zzjglxDm;
                        @JsonProperty("wz")
                        private Object wz;
                        @JsonProperty("hyf1Dm")
                        private Object hyf1Dm;
                        @JsonProperty("hyf2Dm")
                        private Object hyf2Dm;
                        @JsonProperty("hyf3Dm")
                        private Object hyf3Dm;
                        @JsonProperty("swdlrlxdh")
                        private Object swdlrlxdh;
                        @JsonProperty("swdlrdzxx")
                        private Object swdlrdzxx;
                        @JsonProperty("zczb")
                        private Object zczb;
                        @JsonProperty("tzze")
                        private Object tzze;
                        @JsonProperty("zrrtzbl")
                        private String zrrtzbl;
                        @JsonProperty("wztzbl")
                        private String wztzbl;
                        @JsonProperty("gytzbl")
                        private String gytzbl;
                        @JsonProperty("gykglxDm")
                        private Object gykglxDm;
                        @JsonProperty("sjgsrq")
                        private Object sjgsrq;
                        @JsonProperty("zfjglxDm")
                        private String zfjglxDm;
                        @JsonProperty("bzfsDm")
                        private Object bzfsDm;
                        @JsonProperty("cwfzrxm")
                        private String cwfzrxm;
                        @JsonProperty("cwfzrsfzjzlDm")
                        private Object cwfzrsfzjzlDm;
                        @JsonProperty("cwfzrsfzjhm")
                        private Object cwfzrsfzjhm;
                        @JsonProperty("cwfzrgddh")
                        private Object cwfzrgddh;
                        @JsonProperty("cwfzryddh")
                        private Object cwfzryddh;
                        @JsonProperty("cwfzrdzxx")
                        private Object cwfzrdzxx;
                        @JsonProperty("bsrxm")
                        private String bsrxm;
                        @JsonProperty("bsrsfzjzlDm")
                        private Object bsrsfzjzlDm;
                        @JsonProperty("bsrsfzjhm")
                        private Object bsrsfzjhm;
                        @JsonProperty("bsrgddh")
                        private Object bsrgddh;
                        @JsonProperty("bsryddh")
                        private String bsryddh;
                        @JsonProperty("bsrdzxx")
                        private Object bsrdzxx;
                        @JsonProperty("swdlrnsrsbh")
                        private Object swdlrnsrsbh;
                        @JsonProperty("swdlrmc")
                        private Object swdlrmc;
                        @JsonProperty("kjywrzgswjgDm")
                        private Object kjywrzgswjgDm;
                        @JsonProperty("kjywrzgswskfjDm")
                        private Object kjywrzgswskfjDm;
                        @JsonProperty("kjywrssglyDm")
                        private Object kjywrssglyDm;
                        @JsonProperty("stfzgswjgDm")
                        private Object stfzgswjgDm;
                        @JsonProperty("stfzgswskfjDm")
                        private Object stfzgswskfjDm;
                        @JsonProperty("stfssglyDm")
                        private Object stfssglyDm;
                        @JsonProperty("fddbrhfzrhyzgddh")
                        private Object fddbrhfzrhyzgddh;
                        @JsonProperty("fddbrhfzrhyzyddh")
                        private Object fddbrhfzrhyzyddh;
                        @JsonProperty("fddbrhfzrhyzdzxx")
                        private Object fddbrhfzrhyzdzxx;
                        @JsonProperty("lsswdjyxqq")
                        private Object lsswdjyxqq;
                        @JsonProperty("lsswdjyxqz")
                        private Object lsswdjyxqz;
                        @JsonProperty("kjzdzzDm")
                        private Object kjzdzzDm;
                        @JsonProperty("hjszd")
                        private Object hjszd;
                        @JsonProperty("jyfw")
                        private String jyfw;
                        @JsonProperty("fddbrgddh")
                        private Object fddbrgddh;
                        @JsonProperty("fddbryddh")
                        private String fddbryddh;
                        @JsonProperty("fddbrdzxx")
                        private Object fddbrdzxx;
                        @JsonProperty("whsyjsfjfxxdjbz")
                        private Object whsyjsfjfxxdjbz;
                        @JsonProperty("zzsjylb")
                        private Object zzsjylb;
                        @JsonProperty("yhsjnfsDm")
                        private Object yhsjnfsDm;
                        @JsonProperty("zsxmcxbzDm")
                        private Object zsxmcxbzDm;
                        @JsonProperty("zzsqylxDm")
                        private Object zzsqylxDm;
                        @JsonProperty("gjhdqszDm")
                        private Object gjhdqszDm;
                        @JsonProperty("ygznsrlxDm")
                        private Object ygznsrlxDm;
                        @JsonProperty("ywzcdz")
                        private Object ywzcdz;
                        @JsonProperty("yhzh")
                        private String yhzh;
                        @JsonProperty("yhhbmc")
                        private String yhhbmc;
                        @JsonProperty("khyhhh")
                        private String khyhhh;
                        @JsonProperty("dlrZjlxDm")
                        private Object dlrZjlxDm;
                        @JsonProperty("dlrZjhm")
                        private Object dlrZjhm;
                        @JsonProperty("dlrXm")
                        private Object dlrXm;
                        @JsonProperty("dlrSjhm")
                        private Object dlrSjhm;
                        @JsonProperty("fddbrwlsfid")
                        private Object fddbrwlsfid;
                        @JsonProperty("qyhxlbDm")
                        private Object qyhxlbDm;
                    }

                    @NoArgsConstructor
                    @Data
                    public static class SbsxDTO {
                        @JsonProperty("@class")
                        private String _$Class192;// 
                        @JsonProperty("sbmsDm")
                        private String sbmsDm;
                        @JsonProperty("rdSbmsDm")
                        private String rdSbmsDm;
                        @JsonProperty("yzpzzlDm")
                        private String yzpzzlDm;
                        @JsonProperty("yzpzzlMc")
                        private String yzpzzlMc;
                        @JsonProperty("skssqq")
                        private String skssqq;
                        @JsonProperty("skssqz")
                        private String skssqz;
                        @JsonProperty("zsxmDm")
                        private String zsxmDm;
                        @JsonProperty("zsxmDmList")
                        private List<String> zsxmDmList;
                        @JsonProperty("zspmDmSet")
                        private Object zspmDmSet;
                        @JsonProperty("zspmDm")
                        private Object zspmDm;
                        @JsonProperty("sbqxDm")
                        private String sbqxDm;
                        @JsonProperty("nsqxDm")
                        private String nsqxDm;
                        @JsonProperty("jkqxDm")
                        private Object jkqxDm;
                        @JsonProperty("sbqx")
                        private Object sbqx;
                        @JsonProperty("jkqx")
                        private Object jkqx;
                        @JsonProperty("sbrq")
                        private String sbrq;
                        @JsonProperty("zsfsDm")
                        private Object zsfsDm;
                        @JsonProperty("jylxDm")
                        private String jylxDm;
                        @JsonProperty("sbsxDm")
                        private Object sbsxDm;
                        @JsonProperty("zsdlfsDm")
                        private Object zsdlfsDm;
                        @JsonProperty("sbfsDm")
                        private Object sbfsDm;
                        @JsonProperty("sbjnfsDm")
                        private Object sbjnfsDm;
                        @JsonProperty("sblsh")
                        private Object sblsh;
                        @JsonProperty("sbxh")
                        private String sbxh;
                        @JsonProperty("sbuuid")
                        private Object sbuuid;
                        @JsonProperty("pzxh")
                        private Object pzxh;
                        @JsonProperty("snapshotId")
                        private String snapshotId;
                        @JsonProperty("dlrxx")
                        private Object dlrxx;
                        @JsonProperty("byhNsrxx")
                        private Object byhNsrxx;
                        @JsonProperty("qqlxEnum")
                        private Object qqlxEnum;
                        @JsonProperty("qcxxMd5")
                        private String qcxxMd5;
                    }

                    @NoArgsConstructor
                    @Data
                    public static class YtxxDTO {
                        @JsonProperty("@class")
                        private String _$Class287;// 
                        @JsonProperty("bqybtsfe")
                        private Object bqybtsfe;
                        @JsonProperty("bqxsqk")
                        private Object bqxsqk;
                        @JsonProperty("sfjsxx")
                        private Object sfjsxx;
                        @JsonProperty("sbZzsXgm")
                        private List<SbZzsXgmDTO> sbZzsXgm;
                        @JsonProperty("sbZzsXgmFbFlzl")
                        private SbZzsXgmFbFlzlDTO sbZzsXgmFbFlzl;
                        @JsonProperty("sbFjsf")
                        private SbFjsfDTO sbFjsf;
                        @JsonProperty("sbZzsFbZzsjmssbmxb")
                        private SbZzsFbZzsjmssbmxbDTO sbZzsFbZzsjmssbmxb;
                        @JsonProperty("sbZzsXgmFbDlqyjxhxx")
                        private Object sbZzsXgmFbDlqyjxhxx;
                        @JsonProperty("tzxx")
                        private TzxxDTO tzxx;

                        @NoArgsConstructor
                        @Data
                        public static class SbZzsXgmFbFlzlDTO {
                            @JsonProperty("qcye")
                            private Double qcye;
                            @JsonProperty("bqfse")
                            private Double bqfse;
                            @JsonProperty("bqkce")
                            private Double bqkce;
                            @JsonProperty("qmye")
                            private Double qmye;
                            @JsonProperty("ysfwxsqbhssr")
                            private Double ysfwxsqbhssr;
                            @JsonProperty("ysfwxshsxse")
                            private Double ysfwxshsxse;
                            @JsonProperty("ysfwxsbhsxse")
                            private Double ysfwxsbhsxse;
                            @JsonProperty("qcye5")
                            private Double qcye5;
                            @JsonProperty("bqfse5")
                            private Double bqfse5;
                            @JsonProperty("bqkce5")
                            private Double bqkce5;
                            @JsonProperty("qmye5")
                            private Double qmye5;
                            @JsonProperty("ysfwxsqbhssr5")
                            private Double ysfwxsqbhssr5;
                            @JsonProperty("ysfwxshsxse5")
                            private Double ysfwxshsxse5;
                            @JsonProperty("ysfwxsbhsxse5")
                            private Double ysfwxsbhsxse5;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SbFjsfDTO {
                            @JsonProperty("sbFjsfMx")
                            private List<SbFjsfMxDTO> sbFjsfMx;
                            @JsonProperty("sbFjsfQtxx")
                            private SbFjsfQtxxDTO sbFjsfQtxx;

                            @NoArgsConstructor
                            @Data
                            public static class SbFjsfQtxxDTO {
                                @JsonProperty("bchssqq")
                                private Object bchssqq;
                                @JsonProperty("bchssqz")
                                private Object bchssqz;
                                @JsonProperty("jsyjxgxz")
                                private Object jsyjxgxz;
                                @JsonProperty("jsyjxgyy")
                                private String jsyjxgyy;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class SbFjsfMxDTO {
                                @JsonProperty("rdpzuuid")
                                private String rdpzuuid;
                                @JsonProperty("zsxmDm")
                                private String zsxmDm;
                                @JsonProperty("zsxmMc")
                                private String zsxmMc;
                                @JsonProperty("zspmDm")
                                private String zspmDm;
                                @JsonProperty("ybzzs")
                                private Double ybzzs;
                                @JsonProperty("zzsxejmje")
                                private Double zzsxejmje;
                                @JsonProperty("zzsmdse")
                                private Object zzsmdse;
                                @JsonProperty("zzsldtse")
                                private Object zzsldtse;
                                @JsonProperty("sl1")
                                private Double sl1;
                                @JsonProperty("bqynsfe")
                                private Double bqynsfe;
                                @JsonProperty("ssjmxzDm")
                                private String ssjmxzDm;
                                @JsonProperty("swsxDm")
                                private String swsxDm;
                                @JsonProperty("jme")
                                private Double jme;
                                @JsonProperty("phjmxzDm")
                                private String phjmxzDm;
                                @JsonProperty("phjmswsxDm")
                                private String phjmswsxDm;
                                @JsonProperty("phjzbl")
                                private Double phjzbl;
                                @JsonProperty("phjmse")
                                private Double phjmse;
                                @JsonProperty("cjrhjmxzDm")
                                private Object cjrhjmxzDm;
                                @JsonProperty("bqcjrhxqydmje")
                                private Object bqcjrhxqydmje;
                                @JsonProperty("bqyjse")
                                private Double bqyjse;
                                @JsonProperty("bqybtse")
                                private Double bqybtse;
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SbZzsFbZzsjmssbmxbDTO {
                            @JsonProperty("sbZzsFbZzsjmssbmxbJsxm")
                            private List<SbZzsFbZzsjmssbmxbJsxmDTO> sbZzsFbZzsjmssbmxbJsxm;
                            @JsonProperty("sbZzsFbZzsjmssbmxbMsxm")
                            private List<SbZzsFbZzsjmssbmxbMsxmDTO> sbZzsFbZzsjmssbmxbMsxm;

                            @NoArgsConstructor
                            @Data
                            public static class SbZzsFbZzsjmssbmxbJsxmDTO {
                                @JsonProperty("ewbhxh")
                                private String ewbhxh;
                                @JsonProperty("hmc")
                                private String hmc;
                                @JsonProperty("swsxDm")
                                private Object swsxDm;
                                @JsonProperty("qcye")
                                private Double qcye;
                                @JsonProperty("bqfse")
                                private Double bqfse;
                                @JsonProperty("bqydjse")
                                private Double bqydjse;
                                @JsonProperty("bqsjdjse")
                                private Double bqsjdjse;
                                @JsonProperty("qmye")
                                private Double qmye;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class SbZzsFbZzsjmssbmxbMsxmDTO {
                                @JsonProperty("ewbhxh")
                                private String ewbhxh;
                                @JsonProperty("ewbhbm")
                                private Object ewbhbm;
                                @JsonProperty("hmc")
                                private String hmc;
                                @JsonProperty("swsxDm")
                                private Object swsxDm;
                                @JsonProperty("mzzzsxmxse")
                                private Double mzzzsxmxse;
                                @JsonProperty("bqsjkcje")
                                private Double bqsjkcje;
                                @JsonProperty("kchmsxse")
                                private Double kchmsxse;
                                @JsonProperty("msxsedyjxse")
                                private Double msxsedyjxse;
                                @JsonProperty("mse")
                                private Double mse;
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class TzxxDTO {
                            @JsonProperty("@class")
                            private String _$Class33;// 
                            @JsonProperty("fpmx")
                            private FpmxDTO fpmx;
                            @JsonProperty("fzsbxx")
                            private FzsbxxDTO fzsbxx;
                            @JsonProperty("jmsxx")
                            private JmsxxDTO jmsxx;

                            @NoArgsConstructor
                            @Data
                            public static class FpmxDTO {
                                @JsonProperty("hwjlw1ZslXse")
                                private Double hwjlw1ZslXse;
                                @JsonProperty("hwjlw1ZslSe")
                                private Double hwjlw1ZslSe;
                                @JsonProperty("hwjlw3ZslXse")
                                private Double hwjlw3ZslXse;
                                @JsonProperty("hwjlw3ZslSe")
                                private Double hwjlw3ZslSe;
                                @JsonProperty("hwjlw5ZslXse")
                                private Double hwjlw5ZslXse;
                                @JsonProperty("hwjlw5ZslSe")
                                private Double hwjlw5ZslSe;
                                @JsonProperty("hwjlwMsXse")
                                private Double hwjlwMsXse;
                                @JsonProperty("hwjlwMsSe")
                                private Double hwjlwMsSe;
                                @JsonProperty("hwjlwCkmsXse")
                                private Double hwjlwCkmsXse;
                                @JsonProperty("hwjlwCkmsSe")
                                private Double hwjlwCkmsSe;
                                @JsonProperty("fwbdchwxzc1ZslXse")
                                private Double fwbdchwxzc1ZslXse;
                                @JsonProperty("fwbdchwxzc1ZslSe")
                                private Double fwbdchwxzc1ZslSe;
                                @JsonProperty("fwbdchwxzc3ZslXse")
                                private Double fwbdchwxzc3ZslXse;
                                @JsonProperty("fwbdchwxzc3ZslSe")
                                private Double fwbdchwxzc3ZslSe;
                                @JsonProperty("fwbdchwxzc3ZslYlfwXse")
                                private Double fwbdchwxzc3ZslYlfwXse;
                                @JsonProperty("fwbdchwxzc3ZslYlfwSe")
                                private Double fwbdchwxzc3ZslYlfwSe;
                                @JsonProperty("fwbdchwxzc3ZslGgfwXse")
                                private Double fwbdchwxzc3ZslGgfwXse;
                                @JsonProperty("fwbdchwxzc3ZslGgfwSe")
                                private Double fwbdchwxzc3ZslGgfwSe;
                                @JsonProperty("fwbdchwxzc5ZslXse")
                                private Double fwbdchwxzc5ZslXse;
                                @JsonProperty("fwbdchwxzc5ZslSe")
                                private Double fwbdchwxzc5ZslSe;
                                @JsonProperty("fwbdchwxzc5ZslXsbdcXse")
                                private Double fwbdchwxzc5ZslXsbdcXse;
                                @JsonProperty("fwbdchwxzc5ZslXsbdcSe")
                                private Double fwbdchwxzc5ZslXsbdcSe;
                                @JsonProperty("fwbdchwxzc5ZslCzfwXse")
                                private Double fwbdchwxzc5ZslCzfwXse;
                                @JsonProperty("fwbdchwxzc5ZslCzfwSe")
                                private Double fwbdchwxzc5ZslCzfwSe;
                                @JsonProperty("fwbdchwxzcMsXse")
                                private Double fwbdchwxzcMsXse;
                                @JsonProperty("fwbdchwxzcMsSe")
                                private Double fwbdchwxzcMsSe;
                                @JsonProperty("fwbdchwxzcMsYlfwXse")
                                private Double fwbdchwxzcMsYlfwXse;
                                @JsonProperty("fwbdchwxzcMsYlfwSe")
                                private Double fwbdchwxzcMsYlfwSe;
                                @JsonProperty("fwbdchwxzcMsGgfwXse")
                                private Double fwbdchwxzcMsGgfwXse;
                                @JsonProperty("fwbdchwxzcMsGgfwSe")
                                private Double fwbdchwxzcMsGgfwSe;
                                @JsonProperty("fwbdchwxzcCkmsXse")
                                private Double fwbdchwxzcCkmsXse;
                                @JsonProperty("fwbdchwxzcCkmsSe")
                                private Double fwbdchwxzcCkmsSe;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class FzsbxxDTO {
                                @JsonProperty("zyfpBdcxse")
                                private Double zyfpBdcxse;
                                @JsonProperty("qtfpBdcxse")
                                private Double qtfpBdcxse;
                                @JsonProperty("zyfpHwjlwxsehj")
                                private Double zyfpHwjlwxsehj;
                                @JsonProperty("qtfpHwjlwxsehj")
                                private Double qtfpHwjlwxsehj;
                                @JsonProperty("zyfpFw3xsehj")
                                private Double zyfpFw3xsehj;
                                @JsonProperty("qtfpFw3xsehj")
                                private Double qtfpFw3xsehj;
                                @JsonProperty("zyfpFw5xsehj")
                                private Double zyfpFw5xsehj;
                                @JsonProperty("qtfpFw5xsehj")
                                private Double qtfpFw5xsehj;
                                @JsonProperty("qzd")
                                private Double qzd;
                                @JsonProperty("dkzpyjzzsse2fjsjsyj")
                                private Double dkzpyjzzsse2fjsjsyj;
                                @JsonProperty("bqysHwYjse")
                                private Double bqysHwYjse;
                                @JsonProperty("bqysFwYjse")
                                private Double bqysFwYjse;
                                @JsonProperty("bqCswhjsfYjse")
                                private Double bqCswhjsfYjse;
                                @JsonProperty("bqJyffjYjse")
                                private Double bqJyffjYjse;
                                @JsonProperty("bqDfjyffjYjse")
                                private Double bqDfjyffjYjse;
                                @JsonProperty("xsbdckce")
                                private Double xsbdckce;
                                @JsonProperty("zyfpHwjlw1xsehj")
                                private Double zyfpHwjlw1xsehj;
                                @JsonProperty("zyfpHwjlw1sehj")
                                private Double zyfpHwjlw1sehj;
                                @JsonProperty("qtfpHwjlw1xsehj")
                                private Double qtfpHwjlw1xsehj;
                                @JsonProperty("qtfpHwjlw1sehj")
                                private Double qtfpHwjlw1sehj;
                                @JsonProperty("zyfpFw1xsehj")
                                private Double zyfpFw1xsehj;
                                @JsonProperty("zyfpFw1sehj")
                                private Double zyfpFw1sehj;
                                @JsonProperty("qtfpFw1xsehj")
                                private Double qtfpFw1xsehj;
                                @JsonProperty("qtfpFw1sehj")
                                private Double qtfpFw1sehj;
                                @JsonProperty("zyfpBdcKce")
                                private Double zyfpBdcKce;
                                @JsonProperty("zyfpKcqBdcxse")
                                private Double zyfpKcqBdcxse;
                                @JsonProperty("zyfpBdcse")
                                private Double zyfpBdcse;
                                @JsonProperty("zyfpQtslBdcxse")
                                private Double zyfpQtslBdcxse;
                                @JsonProperty("zyfpQtslBdcKce")
                                private Double zyfpQtslBdcKce;
                                @JsonProperty("zyfpKcqQtslBdcxse")
                                private Double zyfpKcqQtslBdcxse;
                                @JsonProperty("zyfpQtslBdcse")
                                private Double zyfpQtslBdcse;
                                @JsonProperty("qtfpBdcKce")
                                private Double qtfpBdcKce;
                                @JsonProperty("qtfpKcqBdcKce")
                                private Double qtfpKcqBdcKce;
                                @JsonProperty("qtfpBdcse")
                                private Double qtfpBdcse;
                                @JsonProperty("qtfpKcqBdcxse")
                                private Double qtfpKcqBdcxse;
                                @JsonProperty("qtfpQtslBdcxse")
                                private Double qtfpQtslBdcxse;
                                @JsonProperty("qtfpQtslBdcKce")
                                private Double qtfpQtslBdcKce;
                                @JsonProperty("qtfpKcqQtslBdcKce")
                                private Double qtfpKcqQtslBdcKce;
                                @JsonProperty("qtfpQtslBdcse")
                                private Double qtfpQtslBdcse;
                                @JsonProperty("qtfpBdc0xse")
                                private Double qtfpBdc0xse;
                                @JsonProperty("qtfpBdc0Kce")
                                private Double qtfpBdc0Kce;
                                @JsonProperty("zyfpHwjlw05xsehj")
                                private Double zyfpHwjlw05xsehj;
                                @JsonProperty("zyfpHwjlw05sehj")
                                private Double zyfpHwjlw05sehj;
                                @JsonProperty("qtfpHwjlw05xsehj")
                                private Double qtfpHwjlw05xsehj;
                                @JsonProperty("qtfpHwjlw05sehj")
                                private Double qtfpHwjlw05sehj;
                                @JsonProperty("zyfpHwjlw3xsehj")
                                private Double zyfpHwjlw3xsehj;
                                @JsonProperty("zyfpHwjlw3sehj")
                                private Double zyfpHwjlw3sehj;
                                @JsonProperty("qtfpHwjlw3xsehj")
                                private Double qtfpHwjlw3xsehj;
                                @JsonProperty("qtfpHwjlw3sehj")
                                private Double qtfpHwjlw3sehj;
                                @JsonProperty("qtfpHwjlw0xsehj")
                                private Double qtfpHwjlw0xsehj;
                                @JsonProperty("qtfpHwjlw0sehj")
                                private Double qtfpHwjlw0sehj;
                                @JsonProperty("qtfpHwjlwck0xsehj")
                                private Double qtfpHwjlwck0xsehj;
                                @JsonProperty("zyfpHwjlwQtslxsehj")
                                private Double zyfpHwjlwQtslxsehj;
                                @JsonProperty("zyfpHwjlwQtslsehj")
                                private Double zyfpHwjlwQtslsehj;
                                @JsonProperty("qtfpHwjlwQtslxsehj")
                                private Double qtfpHwjlwQtslxsehj;
                                @JsonProperty("qtfpHwjlwQtslsehj")
                                private Double qtfpHwjlwQtslsehj;
                                @JsonProperty("zyfpFw1Ksehj")
                                private Double zyfpFw1Ksehj;
                                @JsonProperty("zyfpFwKcq1xsehj")
                                private Double zyfpFwKcq1xsehj;
                                @JsonProperty("qtfpFw1Ksehj")
                                private Double qtfpFw1Ksehj;
                                @JsonProperty("qtfpFwKcq1xsehj")
                                private Double qtfpFwKcq1xsehj;
                                @JsonProperty("zyfpFw3Ksehj")
                                private Double zyfpFw3Ksehj;
                                @JsonProperty("zyfpFwKcq3xsehj")
                                private Double zyfpFwKcq3xsehj;
                                @JsonProperty("zyfpFw3sehj")
                                private Double zyfpFw3sehj;
                                @JsonProperty("qtfpFw3Ksehj")
                                private Double qtfpFw3Ksehj;
                                @JsonProperty("qtfpFwKcq3xsehj")
                                private Double qtfpFwKcq3xsehj;
                                @JsonProperty("qtfpFw3sehj")
                                private Double qtfpFw3sehj;
                                @JsonProperty("zyfpFw15xsehj")
                                private Double zyfpFw15xsehj;
                                @JsonProperty("zyfpFw15Ksehj")
                                private Double zyfpFw15Ksehj;
                                @JsonProperty("zyfpFwKcq15xsehj")
                                private Double zyfpFwKcq15xsehj;
                                @JsonProperty("zyfpFw15sehj")
                                private Double zyfpFw15sehj;
                                @JsonProperty("qtfpFw15xsehj")
                                private Double qtfpFw15xsehj;
                                @JsonProperty("qtfpFw15Ksehj")
                                private Double qtfpFw15Ksehj;
                                @JsonProperty("qtfpFwKcq15xsehj")
                                private Double qtfpFwKcq15xsehj;
                                @JsonProperty("qtfpFw15sehj")
                                private Double qtfpFw15sehj;
                                @JsonProperty("zyfpFw5Ksehj")
                                private Double zyfpFw5Ksehj;
                                @JsonProperty("zyfpFwKcq5xsehj")
                                private Double zyfpFwKcq5xsehj;
                                @JsonProperty("zyfpFw5sehj")
                                private Double zyfpFw5sehj;
                                @JsonProperty("qtfpFw5Ksehj")
                                private Double qtfpFw5Ksehj;
                                @JsonProperty("qtfpFwKcq5xsehj")
                                private Double qtfpFwKcq5xsehj;
                                @JsonProperty("qtfpFw5sehj")
                                private Double qtfpFw5sehj;
                                @JsonProperty("zyfpFwKcq0xsehj")
                                private Double zyfpFwKcq0xsehj;
                                @JsonProperty("qtfpFw0xsehj")
                                private Double qtfpFw0xsehj;
                                @JsonProperty("qtfpFw0Ksehj")
                                private Double qtfpFw0Ksehj;
                                @JsonProperty("qtfpFwKcq0xsehj")
                                private Double qtfpFwKcq0xsehj;
                                @JsonProperty("qtfpFw0sehj")
                                private Double qtfpFw0sehj;
                                @JsonProperty("qtfpFwck0xsehj")
                                private Double qtfpFwck0xsehj;
                                @JsonProperty("zyfpFwQtslxsehj")
                                private Double zyfpFwQtslxsehj;
                                @JsonProperty("zyfpFwQtslKsehj")
                                private Double zyfpFwQtslKsehj;
                                @JsonProperty("zyfpFwKcqQtslxsehj")
                                private Double zyfpFwKcqQtslxsehj;
                                @JsonProperty("zyfpFwQtslsehj")
                                private Double zyfpFwQtslsehj;
                                @JsonProperty("qtfpFwQtslxsehj")
                                private Double qtfpFwQtslxsehj;
                                @JsonProperty("qtfpFwQtslKsehj")
                                private Double qtfpFwQtslKsehj;
                                @JsonProperty("qtfpFwKcqQtslxsehj")
                                private Double qtfpFwKcqQtslxsehj;
                                @JsonProperty("qtfpFwQtslsehj")
                                private Double qtfpFwQtslsehj;
                                @JsonProperty("fpFw1Jshj")
                                private Double fpFw1Jshj;
                                @JsonProperty("fpFw3Jshj")
                                private Double fpFw3Jshj;
                                @JsonProperty("fpFw15Jshj")
                                private Double fpFw15Jshj;
                                @JsonProperty("fpFw5Jshj")
                                private Double fpFw5Jshj;
                                @JsonProperty("cswhjssYywxsphjmBz")
                                private String cswhjssYywxsphjmBz;
                                @JsonProperty("jyffjYywxsphjmBz")
                                private String jyffjYywxsphjmBz;
                                @JsonProperty("dfjyfjYywxsphjmBz")
                                private String dfjyfjYywxsphjmBz;
                                @JsonProperty("fpzxseBhbdcxse")
                                private Double fpzxseBhbdcxse;
                                @JsonProperty("fpzxseHbdcxse")
                                private Double fpzxseHbdcxse;
                                @JsonProperty("wpfpHwjlwck0xsehj")
                                private Double wpfpHwjlwck0xsehj;
                                @JsonProperty("wpfpFwck0xsehj")
                                private Double wpfpFwck0xsehj;
                                @JsonProperty("wpfpHwjlw0xsehj")
                                private Double wpfpHwjlw0xsehj;
                                @JsonProperty("wpfpFw0xsehj")
                                private Double wpfpFw0xsehj;
                                @JsonProperty("wpfpHwjlw05xsehj")
                                private Double wpfpHwjlw05xsehj;
                                @JsonProperty("wpfpHwjlw1xsehj")
                                private Double wpfpHwjlw1xsehj;
                                @JsonProperty("wpfpHwjlw1Wjhdxsehj")
                                private Double wpfpHwjlw1Wjhdxsehj;
                                @JsonProperty("wpfpFw1xsehj")
                                private Double wpfpFw1xsehj;
                                @JsonProperty("wpfpFw1Wjhdxsehj")
                                private Double wpfpFw1Wjhdxsehj;
                                @JsonProperty("wpfpHwjlw3xsehj")
                                private Double wpfpHwjlw3xsehj;
                                @JsonProperty("wpfpHwjlw3Wjhdxsehj")
                                private Double wpfpHwjlw3Wjhdxsehj;
                                @JsonProperty("wpfpFw3xsehj")
                                private Double wpfpFw3xsehj;
                                @JsonProperty("wpfpFw3Wjhdxsehj")
                                private Double wpfpFw3Wjhdxsehj;
                                @JsonProperty("wpfpFw5xsehj")
                                private Double wpfpFw5xsehj;
                                @JsonProperty("wpfpBdcxse")
                                private Double wpfpBdcxse;
                                @JsonProperty("wpfpFw15xsehj")
                                private Double wpfpFw15xsehj;
                                @JsonProperty("hwjlwFpXseHj")
                                private Double hwjlwFpXseHj;
                                @JsonProperty("fwFpXseHj")
                                private Double fwFpXseHj;
                                @JsonProperty("hwjlwHdXseHj")
                                private Double hwjlwHdXseHj;
                                @JsonProperty("fwHdXseHj")
                                private Double fwHdXseHj;
                                @JsonProperty("bdcxseHj")
                                private Double bdcxseHj;
                                @JsonProperty("hwjlwXseHj")
                                private Double hwjlwXseHj;
                                @JsonProperty("fwXseHj")
                                private Double fwXseHj;
                                @JsonProperty("bqXseHj")
                                private Double bqXseHj;
                                @JsonProperty("qzdType")
                                private Double qzdType;
                                @JsonProperty("sfyhqj")
                                private String sfyhqj;
                                @JsonProperty("sf3j1yhqj")
                                private String sf3j1yhqj;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class JmsxxDTO {
                                @JsonProperty("sbZzsFbZzsjmssbmxbJsxm")
                                private List<SbZzsFbZzsjmssbmxbJsxmDTO> sbZzsFbZzsjmssbmxbJsxm;
                                @JsonProperty("sbZzsFbZzsjmssbmxbMsxm")
                                private List<SbZzsFbZzsjmssbmxbMsxmDTO> sbZzsFbZzsjmssbmxbMsxm;

                                @NoArgsConstructor
                                @Data
                                public static class SbZzsFbZzsjmssbmxbJsxmDTO {
                                    @JsonProperty("ewbhxh")
                                    private String ewbhxh;
                                    @JsonProperty("hmc")
                                    private String hmc;
                                    @JsonProperty("swsxDm")
                                    private String swsxDm;
                                    @JsonProperty("qcye")
                                    private Double qcye;
                                    @JsonProperty("bqfse")
                                    private Double bqfse;
                                    @JsonProperty("bqydjse")
                                    private Double bqydjse;
                                    @JsonProperty("bqsjdjse")
                                    private Double bqsjdjse;
                                    @JsonProperty("qmye")
                                    private Double qmye;
                                }

                                @NoArgsConstructor
                                @Data
                                public static class SbZzsFbZzsjmssbmxbMsxmDTO {
                                    @JsonProperty("ewbhxh")
                                    private String ewbhxh;
                                    @JsonProperty("hmc")
                                    private String hmc;
                                    @JsonProperty("swsxDm")
                                    private String swsxDm;
                                    @JsonProperty("mzzzsxmxse")
                                    private Double mzzzsxmxse;
                                    @JsonProperty("bqsjkcje")
                                    private Double bqsjkcje;
                                    @JsonProperty("kchmsxse")
                                    private Double kchmsxse;
                                    @JsonProperty("msxsedyjxse")
                                    private Double msxsedyjxse;
                                    @JsonProperty("mse")
                                    private Double mse;
                                }
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SbZzsXgmDTO {
                            @JsonProperty("ewblxh")
                            private String ewblxh;
                            @JsonProperty("ewbhlbm")
                            private String ewbhlbm;
                            @JsonProperty("lmc")
                            private String lmc;
                            @JsonProperty("yzzzsbhsxse")
                            private Double yzzzsbhsxse;
                            @JsonProperty("swjgdkdzzszyfpbhsxse")
                            private Double swjgdkdzzszyfpbhsxse;
                            @JsonProperty("skqjkjdptfpbhsxse")
                            private Double skqjkjdptfpbhsxse;
                            @JsonProperty("xsczbdcbhsxse")
                            private Double xsczbdcbhsxse;
                            @JsonProperty("swjgdkdzzszyfpbhsxse1")
                            private Double swjgdkdzzszyfpbhsxse1;
                            @JsonProperty("skqjkjdptfpbhsxse2")
                            private Double skqjkjdptfpbhsxse2;
                            @JsonProperty("xssygdysgdzcbhsxse")
                            private Double xssygdysgdzcbhsxse;
                            @JsonProperty("skqjkjdptfpbhsxse1")
                            private Double skqjkjdptfpbhsxse1;
                            @JsonProperty("msxse")
                            private Double msxse;
                            @JsonProperty("xwqymsxse")
                            private Double xwqymsxse;
                            @JsonProperty("wdqzdxse")
                            private Double wdqzdxse;
                            @JsonProperty("qtmsxse")
                            private Double qtmsxse;
                            @JsonProperty("ckmsxse")
                            private Double ckmsxse;
                            @JsonProperty("skqjkjdptfpxse1")
                            private Double skqjkjdptfpxse1;
                            @JsonProperty("hdxse")
                            private Double hdxse;
                            @JsonProperty("bqynse")
                            private Double bqynse;
                            @JsonProperty("hdynse")
                            private Double hdynse;
                            @JsonProperty("bqynsejze")
                            private Double bqynsejze;
                            @JsonProperty("bqmse")
                            private Double bqmse;
                            @JsonProperty("xwqymse")
                            private Double xwqymse;
                            @JsonProperty("wdqzdmse")
                            private Double wdqzdmse;
                            @JsonProperty("ynsehj")
                            private Double ynsehj;
                            @JsonProperty("bqyjse1")
                            private Double bqyjse1;
                            @JsonProperty("bqybtse")
                            private Double bqybtse;
                            @JsonProperty("bdcxse")
                            private Double bdcxse;
                            @JsonProperty("bqybtsecjs")
                            private Double bqybtsecjs;
                            @JsonProperty("bqybtsejyfj")
                            private Double bqybtsejyfj;
                            @JsonProperty("bqybtsedfjyfj")
                            private Double bqybtsedfjyfj;
                        }
                    }

                    @NoArgsConstructor
                    @Data
                    public static class FzxxDTO {
                        @JsonProperty("sbbList")
                        private List<SbbListDTO> sbbList;
                        @JsonProperty("tzxx")
                        private TzxxDTO tzxx;
                        @JsonProperty("csxx")
                        private CsxxDTO csxx;
                        @JsonProperty("bqxx")
                        private BqxxDTO bqxx;
                        @JsonProperty("kzxx")
                        private Object kzxx;

                        @NoArgsConstructor
                        @Data
                        public static class TzxxDTO {
                            @JsonProperty("@class")
                            private String _$Class320;// 
                            @JsonProperty("fpmx")
                            private FpmxDTO fpmx;
                            @JsonProperty("fzsbxx")
                            private FzsbxxDTO fzsbxx;
                            @JsonProperty("jmsxx")
                            private JmsxxDTO jmsxx;

                            @NoArgsConstructor
                            @Data
                            public static class FpmxDTO {
                                @JsonProperty("hwjlw1ZslXse")
                                private Double hwjlw1ZslXse;
                                @JsonProperty("hwjlw1ZslSe")
                                private Double hwjlw1ZslSe;
                                @JsonProperty("hwjlw3ZslXse")
                                private Double hwjlw3ZslXse;
                                @JsonProperty("hwjlw3ZslSe")
                                private Double hwjlw3ZslSe;
                                @JsonProperty("hwjlw5ZslXse")
                                private Double hwjlw5ZslXse;
                                @JsonProperty("hwjlw5ZslSe")
                                private Double hwjlw5ZslSe;
                                @JsonProperty("hwjlwMsXse")
                                private Double hwjlwMsXse;
                                @JsonProperty("hwjlwMsSe")
                                private Double hwjlwMsSe;
                                @JsonProperty("hwjlwCkmsXse")
                                private Double hwjlwCkmsXse;
                                @JsonProperty("hwjlwCkmsSe")
                                private Double hwjlwCkmsSe;
                                @JsonProperty("fwbdchwxzc1ZslXse")
                                private Double fwbdchwxzc1ZslXse;
                                @JsonProperty("fwbdchwxzc1ZslSe")
                                private Double fwbdchwxzc1ZslSe;
                                @JsonProperty("fwbdchwxzc3ZslXse")
                                private Double fwbdchwxzc3ZslXse;
                                @JsonProperty("fwbdchwxzc3ZslSe")
                                private Double fwbdchwxzc3ZslSe;
                                @JsonProperty("fwbdchwxzc3ZslYlfwXse")
                                private Double fwbdchwxzc3ZslYlfwXse;
                                @JsonProperty("fwbdchwxzc3ZslYlfwSe")
                                private Double fwbdchwxzc3ZslYlfwSe;
                                @JsonProperty("fwbdchwxzc3ZslGgfwXse")
                                private Double fwbdchwxzc3ZslGgfwXse;
                                @JsonProperty("fwbdchwxzc3ZslGgfwSe")
                                private Double fwbdchwxzc3ZslGgfwSe;
                                @JsonProperty("fwbdchwxzc5ZslXse")
                                private Double fwbdchwxzc5ZslXse;
                                @JsonProperty("fwbdchwxzc5ZslSe")
                                private Double fwbdchwxzc5ZslSe;
                                @JsonProperty("fwbdchwxzc5ZslXsbdcXse")
                                private Double fwbdchwxzc5ZslXsbdcXse;
                                @JsonProperty("fwbdchwxzc5ZslXsbdcSe")
                                private Double fwbdchwxzc5ZslXsbdcSe;
                                @JsonProperty("fwbdchwxzc5ZslCzfwXse")
                                private Double fwbdchwxzc5ZslCzfwXse;
                                @JsonProperty("fwbdchwxzc5ZslCzfwSe")
                                private Double fwbdchwxzc5ZslCzfwSe;
                                @JsonProperty("fwbdchwxzcMsXse")
                                private Double fwbdchwxzcMsXse;
                                @JsonProperty("fwbdchwxzcMsSe")
                                private Double fwbdchwxzcMsSe;
                                @JsonProperty("fwbdchwxzcMsYlfwXse")
                                private Double fwbdchwxzcMsYlfwXse;
                                @JsonProperty("fwbdchwxzcMsYlfwSe")
                                private Double fwbdchwxzcMsYlfwSe;
                                @JsonProperty("fwbdchwxzcMsGgfwXse")
                                private Double fwbdchwxzcMsGgfwXse;
                                @JsonProperty("fwbdchwxzcMsGgfwSe")
                                private Double fwbdchwxzcMsGgfwSe;
                                @JsonProperty("fwbdchwxzcCkmsXse")
                                private Double fwbdchwxzcCkmsXse;
                                @JsonProperty("fwbdchwxzcCkmsSe")
                                private Double fwbdchwxzcCkmsSe;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class FzsbxxDTO {
                                @JsonProperty("zyfpBdcxse")
                                private Double zyfpBdcxse;
                                @JsonProperty("qtfpBdcxse")
                                private Double qtfpBdcxse;
                                @JsonProperty("zyfpHwjlwxsehj")
                                private Double zyfpHwjlwxsehj;
                                @JsonProperty("qtfpHwjlwxsehj")
                                private Double qtfpHwjlwxsehj;
                                @JsonProperty("zyfpFw3xsehj")
                                private Double zyfpFw3xsehj;
                                @JsonProperty("qtfpFw3xsehj")
                                private Double qtfpFw3xsehj;
                                @JsonProperty("zyfpFw5xsehj")
                                private Double zyfpFw5xsehj;
                                @JsonProperty("qtfpFw5xsehj")
                                private Double qtfpFw5xsehj;
                                @JsonProperty("qzd")
                                private Double qzd;
                                @JsonProperty("dkzpyjzzsse2fjsjsyj")
                                private Double dkzpyjzzsse2fjsjsyj;
                                @JsonProperty("bqysHwYjse")
                                private Double bqysHwYjse;
                                @JsonProperty("bqysFwYjse")
                                private Double bqysFwYjse;
                                @JsonProperty("bqCswhjsfYjse")
                                private Double bqCswhjsfYjse;
                                @JsonProperty("bqJyffjYjse")
                                private Double bqJyffjYjse;
                                @JsonProperty("bqDfjyffjYjse")
                                private Double bqDfjyffjYjse;
                                @JsonProperty("xsbdckce")
                                private Double xsbdckce;
                                @JsonProperty("zyfpHwjlw1xsehj")
                                private Double zyfpHwjlw1xsehj;
                                @JsonProperty("zyfpHwjlw1sehj")
                                private Double zyfpHwjlw1sehj;
                                @JsonProperty("qtfpHwjlw1xsehj")
                                private Double qtfpHwjlw1xsehj;
                                @JsonProperty("qtfpHwjlw1sehj")
                                private Double qtfpHwjlw1sehj;
                                @JsonProperty("zyfpFw1xsehj")
                                private Double zyfpFw1xsehj;
                                @JsonProperty("zyfpFw1sehj")
                                private Double zyfpFw1sehj;
                                @JsonProperty("qtfpFw1xsehj")
                                private Double qtfpFw1xsehj;
                                @JsonProperty("qtfpFw1sehj")
                                private Double qtfpFw1sehj;
                                @JsonProperty("zyfpBdcKce")
                                private Double zyfpBdcKce;
                                @JsonProperty("zyfpKcqBdcxse")
                                private Double zyfpKcqBdcxse;
                                @JsonProperty("zyfpBdcse")
                                private Double zyfpBdcse;
                                @JsonProperty("zyfpQtslBdcxse")
                                private Double zyfpQtslBdcxse;
                                @JsonProperty("zyfpQtslBdcKce")
                                private Double zyfpQtslBdcKce;
                                @JsonProperty("zyfpKcqQtslBdcxse")
                                private Double zyfpKcqQtslBdcxse;
                                @JsonProperty("zyfpQtslBdcse")
                                private Double zyfpQtslBdcse;
                                @JsonProperty("qtfpBdcKce")
                                private Double qtfpBdcKce;
                                @JsonProperty("qtfpKcqBdcKce")
                                private Double qtfpKcqBdcKce;
                                @JsonProperty("qtfpBdcse")
                                private Double qtfpBdcse;
                                @JsonProperty("qtfpKcqBdcxse")
                                private Double qtfpKcqBdcxse;
                                @JsonProperty("qtfpQtslBdcxse")
                                private Double qtfpQtslBdcxse;
                                @JsonProperty("qtfpQtslBdcKce")
                                private Double qtfpQtslBdcKce;
                                @JsonProperty("qtfpKcqQtslBdcKce")
                                private Double qtfpKcqQtslBdcKce;
                                @JsonProperty("qtfpQtslBdcse")
                                private Double qtfpQtslBdcse;
                                @JsonProperty("qtfpBdc0xse")
                                private Double qtfpBdc0xse;
                                @JsonProperty("qtfpBdc0Kce")
                                private Double qtfpBdc0Kce;
                                @JsonProperty("zyfpHwjlw05xsehj")
                                private Double zyfpHwjlw05xsehj;
                                @JsonProperty("zyfpHwjlw05sehj")
                                private Double zyfpHwjlw05sehj;
                                @JsonProperty("qtfpHwjlw05xsehj")
                                private Double qtfpHwjlw05xsehj;
                                @JsonProperty("qtfpHwjlw05sehj")
                                private Double qtfpHwjlw05sehj;
                                @JsonProperty("zyfpHwjlw3xsehj")
                                private Double zyfpHwjlw3xsehj;
                                @JsonProperty("zyfpHwjlw3sehj")
                                private Double zyfpHwjlw3sehj;
                                @JsonProperty("qtfpHwjlw3xsehj")
                                private Double qtfpHwjlw3xsehj;
                                @JsonProperty("qtfpHwjlw3sehj")
                                private Double qtfpHwjlw3sehj;
                                @JsonProperty("qtfpHwjlw0xsehj")
                                private Double qtfpHwjlw0xsehj;
                                @JsonProperty("qtfpHwjlw0sehj")
                                private Double qtfpHwjlw0sehj;
                                @JsonProperty("qtfpHwjlwck0xsehj")
                                private Double qtfpHwjlwck0xsehj;
                                @JsonProperty("zyfpHwjlwQtslxsehj")
                                private Double zyfpHwjlwQtslxsehj;
                                @JsonProperty("zyfpHwjlwQtslsehj")
                                private Double zyfpHwjlwQtslsehj;
                                @JsonProperty("qtfpHwjlwQtslxsehj")
                                private Double qtfpHwjlwQtslxsehj;
                                @JsonProperty("qtfpHwjlwQtslsehj")
                                private Double qtfpHwjlwQtslsehj;
                                @JsonProperty("zyfpFw1Ksehj")
                                private Double zyfpFw1Ksehj;
                                @JsonProperty("zyfpFwKcq1xsehj")
                                private Double zyfpFwKcq1xsehj;
                                @JsonProperty("qtfpFw1Ksehj")
                                private Double qtfpFw1Ksehj;
                                @JsonProperty("qtfpFwKcq1xsehj")
                                private Double qtfpFwKcq1xsehj;
                                @JsonProperty("zyfpFw3Ksehj")
                                private Double zyfpFw3Ksehj;
                                @JsonProperty("zyfpFwKcq3xsehj")
                                private Double zyfpFwKcq3xsehj;
                                @JsonProperty("zyfpFw3sehj")
                                private Double zyfpFw3sehj;
                                @JsonProperty("qtfpFw3Ksehj")
                                private Double qtfpFw3Ksehj;
                                @JsonProperty("qtfpFwKcq3xsehj")
                                private Double qtfpFwKcq3xsehj;
                                @JsonProperty("qtfpFw3sehj")
                                private Double qtfpFw3sehj;
                                @JsonProperty("zyfpFw15xsehj")
                                private Double zyfpFw15xsehj;
                                @JsonProperty("zyfpFw15Ksehj")
                                private Double zyfpFw15Ksehj;
                                @JsonProperty("zyfpFwKcq15xsehj")
                                private Double zyfpFwKcq15xsehj;
                                @JsonProperty("zyfpFw15sehj")
                                private Double zyfpFw15sehj;
                                @JsonProperty("qtfpFw15xsehj")
                                private Double qtfpFw15xsehj;
                                @JsonProperty("qtfpFw15Ksehj")
                                private Double qtfpFw15Ksehj;
                                @JsonProperty("qtfpFwKcq15xsehj")
                                private Double qtfpFwKcq15xsehj;
                                @JsonProperty("qtfpFw15sehj")
                                private Double qtfpFw15sehj;
                                @JsonProperty("zyfpFw5Ksehj")
                                private Double zyfpFw5Ksehj;
                                @JsonProperty("zyfpFwKcq5xsehj")
                                private Double zyfpFwKcq5xsehj;
                                @JsonProperty("zyfpFw5sehj")
                                private Double zyfpFw5sehj;
                                @JsonProperty("qtfpFw5Ksehj")
                                private Double qtfpFw5Ksehj;
                                @JsonProperty("qtfpFwKcq5xsehj")
                                private Double qtfpFwKcq5xsehj;
                                @JsonProperty("qtfpFw5sehj")
                                private Double qtfpFw5sehj;
                                @JsonProperty("zyfpFwKcq0xsehj")
                                private Double zyfpFwKcq0xsehj;
                                @JsonProperty("qtfpFw0xsehj")
                                private Double qtfpFw0xsehj;
                                @JsonProperty("qtfpFw0Ksehj")
                                private Double qtfpFw0Ksehj;
                                @JsonProperty("qtfpFwKcq0xsehj")
                                private Double qtfpFwKcq0xsehj;
                                @JsonProperty("qtfpFw0sehj")
                                private Double qtfpFw0sehj;
                                @JsonProperty("qtfpFwck0xsehj")
                                private Double qtfpFwck0xsehj;
                                @JsonProperty("zyfpFwQtslxsehj")
                                private Double zyfpFwQtslxsehj;
                                @JsonProperty("zyfpFwQtslKsehj")
                                private Double zyfpFwQtslKsehj;
                                @JsonProperty("zyfpFwKcqQtslxsehj")
                                private Double zyfpFwKcqQtslxsehj;
                                @JsonProperty("zyfpFwQtslsehj")
                                private Double zyfpFwQtslsehj;
                                @JsonProperty("qtfpFwQtslxsehj")
                                private Double qtfpFwQtslxsehj;
                                @JsonProperty("qtfpFwQtslKsehj")
                                private Double qtfpFwQtslKsehj;
                                @JsonProperty("qtfpFwKcqQtslxsehj")
                                private Double qtfpFwKcqQtslxsehj;
                                @JsonProperty("qtfpFwQtslsehj")
                                private Double qtfpFwQtslsehj;
                                @JsonProperty("fpFw1Jshj")
                                private Double fpFw1Jshj;
                                @JsonProperty("fpFw3Jshj")
                                private Double fpFw3Jshj;
                                @JsonProperty("fpFw15Jshj")
                                private Double fpFw15Jshj;
                                @JsonProperty("fpFw5Jshj")
                                private Double fpFw5Jshj;
                                @JsonProperty("cswhjssYywxsphjmBz")
                                private String cswhjssYywxsphjmBz;
                                @JsonProperty("jyffjYywxsphjmBz")
                                private String jyffjYywxsphjmBz;
                                @JsonProperty("dfjyfjYywxsphjmBz")
                                private String dfjyfjYywxsphjmBz;
                                @JsonProperty("fpzxseBhbdcxse")
                                private Double fpzxseBhbdcxse;
                                @JsonProperty("fpzxseHbdcxse")
                                private Double fpzxseHbdcxse;
                                @JsonProperty("wpfpHwjlwck0xsehj")
                                private Double wpfpHwjlwck0xsehj;
                                @JsonProperty("wpfpFwck0xsehj")
                                private Double wpfpFwck0xsehj;
                                @JsonProperty("wpfpHwjlw0xsehj")
                                private Double wpfpHwjlw0xsehj;
                                @JsonProperty("wpfpFw0xsehj")
                                private Double wpfpFw0xsehj;
                                @JsonProperty("wpfpHwjlw05xsehj")
                                private Double wpfpHwjlw05xsehj;
                                @JsonProperty("wpfpHwjlw1xsehj")
                                private Double wpfpHwjlw1xsehj;
                                @JsonProperty("wpfpHwjlw1Wjhdxsehj")
                                private Double wpfpHwjlw1Wjhdxsehj;
                                @JsonProperty("wpfpFw1xsehj")
                                private Double wpfpFw1xsehj;
                                @JsonProperty("wpfpFw1Wjhdxsehj")
                                private Double wpfpFw1Wjhdxsehj;
                                @JsonProperty("wpfpHwjlw3xsehj")
                                private Double wpfpHwjlw3xsehj;
                                @JsonProperty("wpfpHwjlw3Wjhdxsehj")
                                private Double wpfpHwjlw3Wjhdxsehj;
                                @JsonProperty("wpfpFw3xsehj")
                                private Double wpfpFw3xsehj;
                                @JsonProperty("wpfpFw3Wjhdxsehj")
                                private Double wpfpFw3Wjhdxsehj;
                                @JsonProperty("wpfpFw5xsehj")
                                private Double wpfpFw5xsehj;
                                @JsonProperty("wpfpBdcxse")
                                private Double wpfpBdcxse;
                                @JsonProperty("wpfpFw15xsehj")
                                private Double wpfpFw15xsehj;
                                @JsonProperty("hwjlwFpXseHj")
                                private Double hwjlwFpXseHj;
                                @JsonProperty("fwFpXseHj")
                                private Double fwFpXseHj;
                                @JsonProperty("hwjlwHdXseHj")
                                private Double hwjlwHdXseHj;
                                @JsonProperty("fwHdXseHj")
                                private Double fwHdXseHj;
                                @JsonProperty("bdcxseHj")
                                private Double bdcxseHj;
                                @JsonProperty("hwjlwXseHj")
                                private Double hwjlwXseHj;
                                @JsonProperty("fwXseHj")
                                private Double fwXseHj;
                                @JsonProperty("bqXseHj")
                                private Double bqXseHj;
                                @JsonProperty("qzdType")
                                private Double qzdType;
                                @JsonProperty("sfyhqj")
                                private String sfyhqj;
                                @JsonProperty("sf3j1yhqj")
                                private String sf3j1yhqj;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class JmsxxDTO {
                                @JsonProperty("sbZzsFbZzsjmssbmxbJsxm")
                                private List<SbZzsFbZzsjmssbmxbJsxmDTO> sbZzsFbZzsjmssbmxbJsxm;
                                @JsonProperty("sbZzsFbZzsjmssbmxbMsxm")
                                private List<SbZzsFbZzsjmssbmxbMsxmDTO> sbZzsFbZzsjmssbmxbMsxm;

                                @NoArgsConstructor
                                @Data
                                public static class SbZzsFbZzsjmssbmxbJsxmDTO {
                                    @JsonProperty("ewbhxh")
                                    private String ewbhxh;
                                    @JsonProperty("hmc")
                                    private String hmc;
                                    @JsonProperty("swsxDm")
                                    private String swsxDm;
                                    @JsonProperty("qcye")
                                    private Double qcye;
                                    @JsonProperty("bqfse")
                                    private Double bqfse;
                                    @JsonProperty("bqydjse")
                                    private Double bqydjse;
                                    @JsonProperty("bqsjdjse")
                                    private Double bqsjdjse;
                                    @JsonProperty("qmye")
                                    private Double qmye;
                                }

                                @NoArgsConstructor
                                @Data
                                public static class SbZzsFbZzsjmssbmxbMsxmDTO {
                                    @JsonProperty("ewbhxh")
                                    private String ewbhxh;
                                    @JsonProperty("hmc")
                                    private String hmc;
                                    @JsonProperty("swsxDm")
                                    private String swsxDm;
                                    @JsonProperty("mzzzsxmxse")
                                    private Double mzzzsxmxse;
                                    @JsonProperty("bqsjkcje")
                                    private Double bqsjkcje;
                                    @JsonProperty("kchmsxse")
                                    private Double kchmsxse;
                                    @JsonProperty("msxsedyjxse")
                                    private Double msxsedyjxse;
                                    @JsonProperty("mse")
                                    private Double mse;
                                }
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class CsxxDTO {
                            @JsonProperty("@class")
                            private String _$Class252;// 
                            @JsonProperty("common")
                            private CommonDTO common;
                            @JsonProperty("showFjsfqkbXgyy")
                            private Boolean showFjsfqkbXgyy;
                            @JsonProperty("fjsfqbkXgyyList")
                            private List<FjsfqbkXgyyListDTO> fjsfqbkXgyyList;
                            @JsonProperty("fjsxejmDm")
                            private String fjsxejmDm;
                            @JsonProperty("zzsxejmDm")
                            private String zzsxejmDm;
                            @JsonProperty("yzl")
                            private Double yzl;
                            @JsonProperty("xgmjszcsftsbz")
                            private String xgmjszcsftsbz;
                            @JsonProperty("xgmjszctsxx")
                            private String xgmjszctsxx;

                            @NoArgsConstructor
                            @Data
                            public static class CommonDTO {
                                @JsonProperty("sswrwc")
                                private Double sswrwc;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class FjsfqbkXgyyListDTO {
                                @JsonProperty("value")
                                private String value;
                                @JsonProperty("label")
                                private String label;
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class BqxxDTO {
                            @JsonProperty("@class")
                            private String _$Class91;// 
                            @JsonProperty("fgdlzzshznsfzjg")
                            private String fgdlzzshznsfzjg;
                            @JsonProperty("escjxqy")
                            private String escjxqy;
                            @JsonProperty("gtgsh")
                            private String gtgsh;
                            @JsonProperty("szlbDm")
                            private String szlbDm;
                            @JsonProperty("bczzzssfzrdqy")
                            private String bczzzssfzrdqy;
                            @JsonProperty("dqdehqy")
                            private String dqdehqy;
                            @JsonProperty("yqwsqdjybnsrzgdqy")
                            private Object yqwsqdjybnsrzgdqy;
                            @JsonProperty("hznsqy")
                            private String hznsqy;
                            @JsonProperty("sydjsyxejmqy")
                            private String sydjsyxejmqy;
                            @JsonProperty("sytysbxejmqy")
                            private String sytysbxejmqy;
                            @JsonProperty("sypkrkxejmqy")
                            private String sypkrkxejmqy;
                            @JsonProperty("ylywhsyjsfqy")
                            private Object ylywhsyjsfqy;
                            @JsonProperty("fcekcggywhsyjsfqy")
                            private Object fcekcggywhsyjsfqy;
                            @JsonProperty("cekcggywhsyjsfqy")
                            private Object cekcggywhsyjsfqy;
                            @JsonProperty("whsyjsfGgySfkc")
                            private Object whsyjsfGgySfkc;
                            @JsonProperty("fjyssxfsqy")
                            private Object fjyssxfsqy;
                            @JsonProperty("jqfsgZzscekcdQy")
                            private String jqfsgZzscekcdQy;
                            @JsonProperty("zzsmsQy")
                            private String zzsmsQy;
                            @JsonProperty("czwppddmsfpdQy")
                            private String czwppddmsfpdQy;
                            @JsonProperty("jqfsgCkywdQy")
                            private String jqfsgCkywdQy;
                            @JsonProperty("jqfsgXsc3zslmswqtmsQy")
                            private String jqfsgXsc3zslmswqtmsQy;
                            @JsonProperty("jqfsgXsjhjzzzsywdQy")
                            private String jqfsgXsjhjzzzsywdQy;
                            @JsonProperty("bqfsZfzlywQy")
                            private String bqfsZfzlywQy;
                            @JsonProperty("jqczSkwhfdjQy")
                            private String jqczSkwhfdjQy;
                            @JsonProperty("bqfs1ywdQy")
                            private String bqfs1ywdQy;
                            @JsonProperty("bqwccbsdQy")
                            private String bqwccbsdQy;
                            @JsonProperty("zzzssbtbqxmdndQy")
                            private String zzzssbtbqxmdndQy;
                            @JsonProperty("ggyWhsyjsfSfzrdqy")
                            private Object ggyWhsyjsfSfzrdqy;
                            @JsonProperty("sfz3yhzcsysjfwn")
                            private String sfz3yhzcsysjfwn;
                            @JsonProperty("bqczcwslfpdQy")
                            private String bqczcwslfpdQy;
                            @JsonProperty("bqcz3ptfpdQy")
                            private String bqcz3ptfpdQy;
                            @JsonProperty("bqcz3hcfpdQy")
                            private String bqcz3hcfpdQy;
                            @JsonProperty("bqwkpsjdQy")
                            private String bqwkpsjdQy;
                            @JsonProperty("dqdeQzjkTg")
                            private Object dqdeQzjkTg;
                            @JsonProperty("cgqzdqy")
                            private String cgqzdqy;
                            @JsonProperty("hdcfpcqzdQy")
                            private String hdcfpcqzdQy;
                            @JsonProperty("hdcfpcqzdTsQy")
                            private String hdcfpcqzdTsQy;
                            @JsonProperty("czchmsqjptfpdQy")
                            private String czchmsqjptfpdQy;
                            @JsonProperty("czchfbssqptfpdQy")
                            private String czchfbssqptfpdQy;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SbbListDTO {
                            @JsonProperty("bbid")
                            private String bbid;
                            @JsonProperty("bbmc")
                            private String bbmc;
                            @JsonProperty("bbjdmc")
                            private String bbjdmc;
                            @JsonProperty("bslxDm")
                            private String bslxDm;
                            @JsonProperty("bbms")
                            private String bbms;
                        }
                    }

                    @NoArgsConstructor
                    @Data
                    public static class QcxxDTO {
                        @JsonProperty("@class")
                        private String _$Class177;// 
                        @JsonProperty("bqybtsfe")
                        private BqybtsfeDTO bqybtsfe;
                        @JsonProperty("bqxsqk")
                        private BqxsqkDTO bqxsqk;
                        @JsonProperty("sfjsxx")
                        private SfjsxxDTO sfjsxx;
                        @JsonProperty("sbZzsXgm")
                        private List<SbZzsXgmDTO> sbZzsXgm;
                        @JsonProperty("sbZzsXgmFbFlzl")
                        private SbZzsXgmFbFlzlDTO sbZzsXgmFbFlzl;
                        @JsonProperty("sbFjsf")
                        private SbFjsfDTO sbFjsf;
                        @JsonProperty("sbZzsFbZzsjmssbmxb")
                        private SbZzsFbZzsjmssbmxbDTO sbZzsFbZzsjmssbmxb;
                        @JsonProperty("sbZzsXgmFbDlqyjxhxx")
                        private Object sbZzsXgmFbDlqyjxhxx;
                        @JsonProperty("tzxx")
                        private TzxxDTO tzxx;

                        @NoArgsConstructor
                        @Data
                        public static class BqybtsfeDTO {
                            @JsonProperty("hj")
                            private Double hj;
                            @JsonProperty("zzsjfj")
                            private Double zzsjfj;
                            @JsonProperty("zzs")
                            private Double zzs;
                            @JsonProperty("zzsCswhjss")
                            private Double zzsCswhjss;
                            @JsonProperty("zzsJyffj")
                            private Double zzsJyffj;
                            @JsonProperty("zzsDfjyfj")
                            private Double zzsDfjyfj;
                            @JsonProperty("cswhjss")
                            private Double cswhjss;
                            @JsonProperty("jyffj")
                            private Double jyffj;
                            @JsonProperty("dfjyfj")
                            private Double dfjyfj;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class BqxsqkDTO {
                            @JsonProperty("hwjlwList")
                            private List<HwjlwListDTO> hwjlwList;
                            @JsonProperty("fwbdchwxzcList")
                            private List<FwbdchwxzcListDTO> fwbdchwxzcList;
                            @JsonProperty("fwbdchwxzcHdxse")
                            private Double fwbdchwxzcHdxse;
                            @JsonProperty("fwbdchwxzcSe")
                            private Double fwbdchwxzcSe;
                            @JsonProperty("fwbdchwxzcHdynse")
                            private Double fwbdchwxzcHdynse;
                            @JsonProperty("hwjlwHdynse")
                            private Double hwjlwHdynse;
                            @JsonProperty("fwbdchwxzcXse")
                            private Double fwbdchwxzcXse;
                            @JsonProperty("hwjlwXse")
                            private Double hwjlwXse;
                            @JsonProperty("hwjlwSe")
                            private Double hwjlwSe;
                            @JsonProperty("hwjlwHdxse")
                            private Double hwjlwHdxse;

                            @NoArgsConstructor
                            @Data
                            public static class HwjlwListDTO {
                                @JsonProperty("id")
                                private String id;
                                @JsonProperty("pid")
                                private String pid;
                                @JsonProperty("showDefault")
                                private Boolean showDefault;
                                @JsonProperty("xse")
                                private Double xse;
                                @JsonProperty("qcXse")
                                private Double qcXse;
                                @JsonProperty("lx")
                                private String lx;
                                @JsonProperty("xmmc")
                                private String xmmc;
                                @JsonProperty("xxse")
                                private Double xxse;
                                @JsonProperty("qcXxse")
                                private Double qcXxse;
                                @JsonProperty("xmdm")
                                private String xmdm;
                                @JsonProperty("tips")
                                private Object tips;
                                @JsonProperty("items")
                                private Object items;
                                @JsonProperty("sl")
                                private Double sl;
                                @JsonProperty("xseEnabled")
                                private Boolean xseEnabled;
                                @JsonProperty("xxseEnabled")
                                private Boolean xxseEnabled;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class FwbdchwxzcListDTO {
                                @JsonProperty("id")
                                private String id;
                                @JsonProperty("pid")
                                private String pid;
                                @JsonProperty("showDefault")
                                private Boolean showDefault;
                                @JsonProperty("xse")
                                private Double xse;
                                @JsonProperty("qcXse")
                                private Double qcXse;
                                @JsonProperty("lx")
                                private String lx;
                                @JsonProperty("xmmc")
                                private String xmmc;
                                @JsonProperty("xxse")
                                private Double xxse;
                                @JsonProperty("qcXxse")
                                private Double qcXxse;
                                @JsonProperty("xmdm")
                                private String xmdm;
                                @JsonProperty("tips")
                                private Object tips;
                                @JsonProperty("items")
                                private Object items;
                                @JsonProperty("sl")
                                private Double sl;
                                @JsonProperty("xseEnabled")
                                private Boolean xseEnabled;
                                @JsonProperty("xxseEnabled")
                                private Boolean xxseEnabled;
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SfjsxxDTO {
                            @JsonProperty("zzsYbtse")
                            private Double zzsYbtse;
                            @JsonProperty("zzsYnse")
                            private Double zzsYnse;
                            @JsonProperty("zzsYnsejze")
                            private Double zzsYnsejze;
                            @JsonProperty("zzsMse")
                            private Double zzsMse;
                            @JsonProperty("zzsBqyjse")
                            private Double zzsBqyjse;
                            @JsonProperty("zzsMseTip")
                            private String zzsMseTip;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SbZzsXgmFbFlzlDTO {
                            @JsonProperty("qcye")
                            private Double qcye;
                            @JsonProperty("bqfse")
                            private Double bqfse;
                            @JsonProperty("bqkce")
                            private Double bqkce;
                            @JsonProperty("qmye")
                            private Double qmye;
                            @JsonProperty("ysfwxsqbhssr")
                            private Double ysfwxsqbhssr;
                            @JsonProperty("ysfwxshsxse")
                            private Double ysfwxshsxse;
                            @JsonProperty("ysfwxsbhsxse")
                            private Double ysfwxsbhsxse;
                            @JsonProperty("qcye5")
                            private Double qcye5;
                            @JsonProperty("bqfse5")
                            private Double bqfse5;
                            @JsonProperty("bqkce5")
                            private Double bqkce5;
                            @JsonProperty("qmye5")
                            private Double qmye5;
                            @JsonProperty("ysfwxsqbhssr5")
                            private Double ysfwxsqbhssr5;
                            @JsonProperty("ysfwxshsxse5")
                            private Double ysfwxshsxse5;
                            @JsonProperty("ysfwxsbhsxse5")
                            private Double ysfwxsbhsxse5;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SbFjsfDTO {
                            @JsonProperty("sbFjsfMx")
                            private List<SbFjsfMxDTO> sbFjsfMx;
                            @JsonProperty("sbFjsfQtxx")
                            private SbFjsfQtxxDTO sbFjsfQtxx;

                            @NoArgsConstructor
                            @Data
                            public static class SbFjsfQtxxDTO {
                                @JsonProperty("bchssqq")
                                private Object bchssqq;
                                @JsonProperty("bchssqz")
                                private Object bchssqz;
                                @JsonProperty("jsyjxgxz")
                                private Object jsyjxgxz;
                                @JsonProperty("jsyjxgyy")
                                private String jsyjxgyy;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class SbFjsfMxDTO {
                                @JsonProperty("rdpzuuid")
                                private String rdpzuuid;
                                @JsonProperty("zsxmDm")
                                private String zsxmDm;
                                @JsonProperty("zsxmMc")
                                private String zsxmMc;
                                @JsonProperty("zspmDm")
                                private String zspmDm;
                                @JsonProperty("ybzzs")
                                private Double ybzzs;
                                @JsonProperty("zzsxejmje")
                                private Double zzsxejmje;
                                @JsonProperty("zzsmdse")
                                private Object zzsmdse;
                                @JsonProperty("zzsldtse")
                                private Object zzsldtse;
                                @JsonProperty("sl1")
                                private Double sl1;
                                @JsonProperty("bqynsfe")
                                private Double bqynsfe;
                                @JsonProperty("ssjmxzDm")
                                private String ssjmxzDm;
                                @JsonProperty("swsxDm")
                                private String swsxDm;
                                @JsonProperty("jme")
                                private Double jme;
                                @JsonProperty("phjmxzDm")
                                private String phjmxzDm;
                                @JsonProperty("phjmswsxDm")
                                private String phjmswsxDm;
                                @JsonProperty("phjzbl")
                                private Double phjzbl;
                                @JsonProperty("phjmse")
                                private Double phjmse;
                                @JsonProperty("cjrhjmxzDm")
                                private Object cjrhjmxzDm;
                                @JsonProperty("bqcjrhxqydmje")
                                private Object bqcjrhxqydmje;
                                @JsonProperty("bqyjse")
                                private Double bqyjse;
                                @JsonProperty("bqybtse")
                                private Double bqybtse;
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SbZzsFbZzsjmssbmxbDTO {
                            @JsonProperty("sbZzsFbZzsjmssbmxbJsxm")
                            private List<SbZzsFbZzsjmssbmxbJsxmDTO> sbZzsFbZzsjmssbmxbJsxm;
                            @JsonProperty("sbZzsFbZzsjmssbmxbMsxm")
                            private List<SbZzsFbZzsjmssbmxbMsxmDTO> sbZzsFbZzsjmssbmxbMsxm;

                            @NoArgsConstructor
                            @Data
                            public static class SbZzsFbZzsjmssbmxbJsxmDTO {
                                @JsonProperty("ewbhxh")
                                private String ewbhxh;
                                @JsonProperty("hmc")
                                private String hmc;
                                @JsonProperty("swsxDm")
                                private Object swsxDm;
                                @JsonProperty("qcye")
                                private Double qcye;
                                @JsonProperty("bqfse")
                                private Double bqfse;
                                @JsonProperty("bqydjse")
                                private Double bqydjse;
                                @JsonProperty("bqsjdjse")
                                private Double bqsjdjse;
                                @JsonProperty("qmye")
                                private Double qmye;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class SbZzsFbZzsjmssbmxbMsxmDTO {
                                @JsonProperty("ewbhxh")
                                private String ewbhxh;
                                @JsonProperty("ewbhbm")
                                private Object ewbhbm;
                                @JsonProperty("hmc")
                                private String hmc;
                                @JsonProperty("swsxDm")
                                private Object swsxDm;
                                @JsonProperty("mzzzsxmxse")
                                private Double mzzzsxmxse;
                                @JsonProperty("bqsjkcje")
                                private Double bqsjkcje;
                                @JsonProperty("kchmsxse")
                                private Double kchmsxse;
                                @JsonProperty("msxsedyjxse")
                                private Double msxsedyjxse;
                                @JsonProperty("mse")
                                private Double mse;
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class TzxxDTO {
                            @JsonProperty("@class")
                            private String _$Class222;// 
                            @JsonProperty("fpmx")
                            private FpmxDTO fpmx;
                            @JsonProperty("fzsbxx")
                            private FzsbxxDTO fzsbxx;
                            @JsonProperty("jmsxx")
                            private JmsxxDTO jmsxx;

                            @NoArgsConstructor
                            @Data
                            public static class FpmxDTO {
                                @JsonProperty("hwjlw1ZslXse")
                                private Double hwjlw1ZslXse;
                                @JsonProperty("hwjlw1ZslSe")
                                private Double hwjlw1ZslSe;
                                @JsonProperty("hwjlw3ZslXse")
                                private Double hwjlw3ZslXse;
                                @JsonProperty("hwjlw3ZslSe")
                                private Double hwjlw3ZslSe;
                                @JsonProperty("hwjlw5ZslXse")
                                private Double hwjlw5ZslXse;
                                @JsonProperty("hwjlw5ZslSe")
                                private Double hwjlw5ZslSe;
                                @JsonProperty("hwjlwMsXse")
                                private Double hwjlwMsXse;
                                @JsonProperty("hwjlwMsSe")
                                private Double hwjlwMsSe;
                                @JsonProperty("hwjlwCkmsXse")
                                private Double hwjlwCkmsXse;
                                @JsonProperty("hwjlwCkmsSe")
                                private Double hwjlwCkmsSe;
                                @JsonProperty("fwbdchwxzc1ZslXse")
                                private Double fwbdchwxzc1ZslXse;
                                @JsonProperty("fwbdchwxzc1ZslSe")
                                private Double fwbdchwxzc1ZslSe;
                                @JsonProperty("fwbdchwxzc3ZslXse")
                                private Double fwbdchwxzc3ZslXse;
                                @JsonProperty("fwbdchwxzc3ZslSe")
                                private Double fwbdchwxzc3ZslSe;
                                @JsonProperty("fwbdchwxzc3ZslYlfwXse")
                                private Double fwbdchwxzc3ZslYlfwXse;
                                @JsonProperty("fwbdchwxzc3ZslYlfwSe")
                                private Double fwbdchwxzc3ZslYlfwSe;
                                @JsonProperty("fwbdchwxzc3ZslGgfwXse")
                                private Double fwbdchwxzc3ZslGgfwXse;
                                @JsonProperty("fwbdchwxzc3ZslGgfwSe")
                                private Double fwbdchwxzc3ZslGgfwSe;
                                @JsonProperty("fwbdchwxzc5ZslXse")
                                private Double fwbdchwxzc5ZslXse;
                                @JsonProperty("fwbdchwxzc5ZslSe")
                                private Double fwbdchwxzc5ZslSe;
                                @JsonProperty("fwbdchwxzc5ZslXsbdcXse")
                                private Double fwbdchwxzc5ZslXsbdcXse;
                                @JsonProperty("fwbdchwxzc5ZslXsbdcSe")
                                private Double fwbdchwxzc5ZslXsbdcSe;
                                @JsonProperty("fwbdchwxzc5ZslCzfwXse")
                                private Double fwbdchwxzc5ZslCzfwXse;
                                @JsonProperty("fwbdchwxzc5ZslCzfwSe")
                                private Double fwbdchwxzc5ZslCzfwSe;
                                @JsonProperty("fwbdchwxzcMsXse")
                                private Double fwbdchwxzcMsXse;
                                @JsonProperty("fwbdchwxzcMsSe")
                                private Double fwbdchwxzcMsSe;
                                @JsonProperty("fwbdchwxzcMsYlfwXse")
                                private Double fwbdchwxzcMsYlfwXse;
                                @JsonProperty("fwbdchwxzcMsYlfwSe")
                                private Double fwbdchwxzcMsYlfwSe;
                                @JsonProperty("fwbdchwxzcMsGgfwXse")
                                private Double fwbdchwxzcMsGgfwXse;
                                @JsonProperty("fwbdchwxzcMsGgfwSe")
                                private Double fwbdchwxzcMsGgfwSe;
                                @JsonProperty("fwbdchwxzcCkmsXse")
                                private Double fwbdchwxzcCkmsXse;
                                @JsonProperty("fwbdchwxzcCkmsSe")
                                private Double fwbdchwxzcCkmsSe;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class FzsbxxDTO {
                                @JsonProperty("zyfpBdcxse")
                                private Double zyfpBdcxse;
                                @JsonProperty("qtfpBdcxse")
                                private Double qtfpBdcxse;
                                @JsonProperty("zyfpHwjlwxsehj")
                                private Double zyfpHwjlwxsehj;
                                @JsonProperty("qtfpHwjlwxsehj")
                                private Double qtfpHwjlwxsehj;
                                @JsonProperty("zyfpFw3xsehj")
                                private Double zyfpFw3xsehj;
                                @JsonProperty("qtfpFw3xsehj")
                                private Double qtfpFw3xsehj;
                                @JsonProperty("zyfpFw5xsehj")
                                private Double zyfpFw5xsehj;
                                @JsonProperty("qtfpFw5xsehj")
                                private Double qtfpFw5xsehj;
                                @JsonProperty("qzd")
                                private Double qzd;
                                @JsonProperty("dkzpyjzzsse2fjsjsyj")
                                private Double dkzpyjzzsse2fjsjsyj;
                                @JsonProperty("bqysHwYjse")
                                private Double bqysHwYjse;
                                @JsonProperty("bqysFwYjse")
                                private Double bqysFwYjse;
                                @JsonProperty("bqCswhjsfYjse")
                                private Double bqCswhjsfYjse;
                                @JsonProperty("bqJyffjYjse")
                                private Double bqJyffjYjse;
                                @JsonProperty("bqDfjyffjYjse")
                                private Double bqDfjyffjYjse;
                                @JsonProperty("xsbdckce")
                                private Double xsbdckce;
                                @JsonProperty("zyfpHwjlw1xsehj")
                                private Double zyfpHwjlw1xsehj;
                                @JsonProperty("zyfpHwjlw1sehj")
                                private Double zyfpHwjlw1sehj;
                                @JsonProperty("qtfpHwjlw1xsehj")
                                private Double qtfpHwjlw1xsehj;
                                @JsonProperty("qtfpHwjlw1sehj")
                                private Double qtfpHwjlw1sehj;
                                @JsonProperty("zyfpFw1xsehj")
                                private Double zyfpFw1xsehj;
                                @JsonProperty("zyfpFw1sehj")
                                private Double zyfpFw1sehj;
                                @JsonProperty("qtfpFw1xsehj")
                                private Double qtfpFw1xsehj;
                                @JsonProperty("qtfpFw1sehj")
                                private Double qtfpFw1sehj;
                                @JsonProperty("zyfpBdcKce")
                                private Double zyfpBdcKce;
                                @JsonProperty("zyfpKcqBdcxse")
                                private Double zyfpKcqBdcxse;
                                @JsonProperty("zyfpBdcse")
                                private Double zyfpBdcse;
                                @JsonProperty("zyfpQtslBdcxse")
                                private Double zyfpQtslBdcxse;
                                @JsonProperty("zyfpQtslBdcKce")
                                private Double zyfpQtslBdcKce;
                                @JsonProperty("zyfpKcqQtslBdcxse")
                                private Double zyfpKcqQtslBdcxse;
                                @JsonProperty("zyfpQtslBdcse")
                                private Double zyfpQtslBdcse;
                                @JsonProperty("qtfpBdcKce")
                                private Double qtfpBdcKce;
                                @JsonProperty("qtfpKcqBdcKce")
                                private Double qtfpKcqBdcKce;
                                @JsonProperty("qtfpBdcse")
                                private Double qtfpBdcse;
                                @JsonProperty("qtfpKcqBdcxse")
                                private Double qtfpKcqBdcxse;
                                @JsonProperty("qtfpQtslBdcxse")
                                private Double qtfpQtslBdcxse;
                                @JsonProperty("qtfpQtslBdcKce")
                                private Double qtfpQtslBdcKce;
                                @JsonProperty("qtfpKcqQtslBdcKce")
                                private Double qtfpKcqQtslBdcKce;
                                @JsonProperty("qtfpQtslBdcse")
                                private Double qtfpQtslBdcse;
                                @JsonProperty("qtfpBdc0xse")
                                private Double qtfpBdc0xse;
                                @JsonProperty("qtfpBdc0Kce")
                                private Double qtfpBdc0Kce;
                                @JsonProperty("zyfpHwjlw05xsehj")
                                private Double zyfpHwjlw05xsehj;
                                @JsonProperty("zyfpHwjlw05sehj")
                                private Double zyfpHwjlw05sehj;
                                @JsonProperty("qtfpHwjlw05xsehj")
                                private Double qtfpHwjlw05xsehj;
                                @JsonProperty("qtfpHwjlw05sehj")
                                private Double qtfpHwjlw05sehj;
                                @JsonProperty("zyfpHwjlw3xsehj")
                                private Double zyfpHwjlw3xsehj;
                                @JsonProperty("zyfpHwjlw3sehj")
                                private Double zyfpHwjlw3sehj;
                                @JsonProperty("qtfpHwjlw3xsehj")
                                private Double qtfpHwjlw3xsehj;
                                @JsonProperty("qtfpHwjlw3sehj")
                                private Double qtfpHwjlw3sehj;
                                @JsonProperty("qtfpHwjlw0xsehj")
                                private Double qtfpHwjlw0xsehj;
                                @JsonProperty("qtfpHwjlw0sehj")
                                private Double qtfpHwjlw0sehj;
                                @JsonProperty("qtfpHwjlwck0xsehj")
                                private Double qtfpHwjlwck0xsehj;
                                @JsonProperty("zyfpHwjlwQtslxsehj")
                                private Double zyfpHwjlwQtslxsehj;
                                @JsonProperty("zyfpHwjlwQtslsehj")
                                private Double zyfpHwjlwQtslsehj;
                                @JsonProperty("qtfpHwjlwQtslxsehj")
                                private Double qtfpHwjlwQtslxsehj;
                                @JsonProperty("qtfpHwjlwQtslsehj")
                                private Double qtfpHwjlwQtslsehj;
                                @JsonProperty("zyfpFw1Ksehj")
                                private Double zyfpFw1Ksehj;
                                @JsonProperty("zyfpFwKcq1xsehj")
                                private Double zyfpFwKcq1xsehj;
                                @JsonProperty("qtfpFw1Ksehj")
                                private Double qtfpFw1Ksehj;
                                @JsonProperty("qtfpFwKcq1xsehj")
                                private Double qtfpFwKcq1xsehj;
                                @JsonProperty("zyfpFw3Ksehj")
                                private Double zyfpFw3Ksehj;
                                @JsonProperty("zyfpFwKcq3xsehj")
                                private Double zyfpFwKcq3xsehj;
                                @JsonProperty("zyfpFw3sehj")
                                private Double zyfpFw3sehj;
                                @JsonProperty("qtfpFw3Ksehj")
                                private Double qtfpFw3Ksehj;
                                @JsonProperty("qtfpFwKcq3xsehj")
                                private Double qtfpFwKcq3xsehj;
                                @JsonProperty("qtfpFw3sehj")
                                private Double qtfpFw3sehj;
                                @JsonProperty("zyfpFw15xsehj")
                                private Double zyfpFw15xsehj;
                                @JsonProperty("zyfpFw15Ksehj")
                                private Double zyfpFw15Ksehj;
                                @JsonProperty("zyfpFwKcq15xsehj")
                                private Double zyfpFwKcq15xsehj;
                                @JsonProperty("zyfpFw15sehj")
                                private Double zyfpFw15sehj;
                                @JsonProperty("qtfpFw15xsehj")
                                private Double qtfpFw15xsehj;
                                @JsonProperty("qtfpFw15Ksehj")
                                private Double qtfpFw15Ksehj;
                                @JsonProperty("qtfpFwKcq15xsehj")
                                private Double qtfpFwKcq15xsehj;
                                @JsonProperty("qtfpFw15sehj")
                                private Double qtfpFw15sehj;
                                @JsonProperty("zyfpFw5Ksehj")
                                private Double zyfpFw5Ksehj;
                                @JsonProperty("zyfpFwKcq5xsehj")
                                private Double zyfpFwKcq5xsehj;
                                @JsonProperty("zyfpFw5sehj")
                                private Double zyfpFw5sehj;
                                @JsonProperty("qtfpFw5Ksehj")
                                private Double qtfpFw5Ksehj;
                                @JsonProperty("qtfpFwKcq5xsehj")
                                private Double qtfpFwKcq5xsehj;
                                @JsonProperty("qtfpFw5sehj")
                                private Double qtfpFw5sehj;
                                @JsonProperty("zyfpFwKcq0xsehj")
                                private Double zyfpFwKcq0xsehj;
                                @JsonProperty("qtfpFw0xsehj")
                                private Double qtfpFw0xsehj;
                                @JsonProperty("qtfpFw0Ksehj")
                                private Double qtfpFw0Ksehj;
                                @JsonProperty("qtfpFwKcq0xsehj")
                                private Double qtfpFwKcq0xsehj;
                                @JsonProperty("qtfpFw0sehj")
                                private Double qtfpFw0sehj;
                                @JsonProperty("qtfpFwck0xsehj")
                                private Double qtfpFwck0xsehj;
                                @JsonProperty("zyfpFwQtslxsehj")
                                private Double zyfpFwQtslxsehj;
                                @JsonProperty("zyfpFwQtslKsehj")
                                private Double zyfpFwQtslKsehj;
                                @JsonProperty("zyfpFwKcqQtslxsehj")
                                private Double zyfpFwKcqQtslxsehj;
                                @JsonProperty("zyfpFwQtslsehj")
                                private Double zyfpFwQtslsehj;
                                @JsonProperty("qtfpFwQtslxsehj")
                                private Double qtfpFwQtslxsehj;
                                @JsonProperty("qtfpFwQtslKsehj")
                                private Double qtfpFwQtslKsehj;
                                @JsonProperty("qtfpFwKcqQtslxsehj")
                                private Double qtfpFwKcqQtslxsehj;
                                @JsonProperty("qtfpFwQtslsehj")
                                private Double qtfpFwQtslsehj;
                                @JsonProperty("fpFw1Jshj")
                                private Double fpFw1Jshj;
                                @JsonProperty("fpFw3Jshj")
                                private Double fpFw3Jshj;
                                @JsonProperty("fpFw15Jshj")
                                private Double fpFw15Jshj;
                                @JsonProperty("fpFw5Jshj")
                                private Double fpFw5Jshj;
                                @JsonProperty("cswhjssYywxsphjmBz")
                                private String cswhjssYywxsphjmBz;
                                @JsonProperty("jyffjYywxsphjmBz")
                                private String jyffjYywxsphjmBz;
                                @JsonProperty("dfjyfjYywxsphjmBz")
                                private String dfjyfjYywxsphjmBz;
                                @JsonProperty("fpzxseBhbdcxse")
                                private Double fpzxseBhbdcxse;
                                @JsonProperty("fpzxseHbdcxse")
                                private Double fpzxseHbdcxse;
                                @JsonProperty("wpfpHwjlwck0xsehj")
                                private Double wpfpHwjlwck0xsehj;
                                @JsonProperty("wpfpFwck0xsehj")
                                private Double wpfpFwck0xsehj;
                                @JsonProperty("wpfpHwjlw0xsehj")
                                private Double wpfpHwjlw0xsehj;
                                @JsonProperty("wpfpFw0xsehj")
                                private Double wpfpFw0xsehj;
                                @JsonProperty("wpfpHwjlw05xsehj")
                                private Double wpfpHwjlw05xsehj;
                                @JsonProperty("wpfpHwjlw1xsehj")
                                private Double wpfpHwjlw1xsehj;
                                @JsonProperty("wpfpHwjlw1Wjhdxsehj")
                                private Double wpfpHwjlw1Wjhdxsehj;
                                @JsonProperty("wpfpFw1xsehj")
                                private Double wpfpFw1xsehj;
                                @JsonProperty("wpfpFw1Wjhdxsehj")
                                private Double wpfpFw1Wjhdxsehj;
                                @JsonProperty("wpfpHwjlw3xsehj")
                                private Double wpfpHwjlw3xsehj;
                                @JsonProperty("wpfpHwjlw3Wjhdxsehj")
                                private Double wpfpHwjlw3Wjhdxsehj;
                                @JsonProperty("wpfpFw3xsehj")
                                private Double wpfpFw3xsehj;
                                @JsonProperty("wpfpFw3Wjhdxsehj")
                                private Double wpfpFw3Wjhdxsehj;
                                @JsonProperty("wpfpFw5xsehj")
                                private Double wpfpFw5xsehj;
                                @JsonProperty("wpfpBdcxse")
                                private Double wpfpBdcxse;
                                @JsonProperty("wpfpFw15xsehj")
                                private Double wpfpFw15xsehj;
                                @JsonProperty("hwjlwFpXseHj")
                                private Double hwjlwFpXseHj;
                                @JsonProperty("fwFpXseHj")
                                private Double fwFpXseHj;
                                @JsonProperty("hwjlwHdXseHj")
                                private Double hwjlwHdXseHj;
                                @JsonProperty("fwHdXseHj")
                                private Double fwHdXseHj;
                                @JsonProperty("bdcxseHj")
                                private Double bdcxseHj;
                                @JsonProperty("hwjlwXseHj")
                                private Double hwjlwXseHj;
                                @JsonProperty("fwXseHj")
                                private Double fwXseHj;
                                @JsonProperty("bqXseHj")
                                private Double bqXseHj;
                                @JsonProperty("qzdType")
                                private Double qzdType;
                                @JsonProperty("sfyhqj")
                                private String sfyhqj;
                                @JsonProperty("sf3j1yhqj")
                                private String sf3j1yhqj;
                            }

                            @NoArgsConstructor
                            @Data
                            public static class JmsxxDTO {
                                @JsonProperty("sbZzsFbZzsjmssbmxbJsxm")
                                private List<SbZzsFbZzsjmssbmxbJsxmDTO> sbZzsFbZzsjmssbmxbJsxm;
                                @JsonProperty("sbZzsFbZzsjmssbmxbMsxm")
                                private List<SbZzsFbZzsjmssbmxbMsxmDTO> sbZzsFbZzsjmssbmxbMsxm;

                                @NoArgsConstructor
                                @Data
                                public static class SbZzsFbZzsjmssbmxbJsxmDTO {
                                    @JsonProperty("ewbhxh")
                                    private String ewbhxh;
                                    @JsonProperty("hmc")
                                    private String hmc;
                                    @JsonProperty("swsxDm")
                                    private String swsxDm;
                                    @JsonProperty("qcye")
                                    private Double qcye;
                                    @JsonProperty("bqfse")
                                    private Double bqfse;
                                    @JsonProperty("bqydjse")
                                    private Double bqydjse;
                                    @JsonProperty("bqsjdjse")
                                    private Double bqsjdjse;
                                    @JsonProperty("qmye")
                                    private Double qmye;
                                }

                                @NoArgsConstructor
                                @Data
                                public static class SbZzsFbZzsjmssbmxbMsxmDTO {
                                    @JsonProperty("ewbhxh")
                                    private String ewbhxh;
                                    @JsonProperty("hmc")
                                    private String hmc;
                                    @JsonProperty("swsxDm")
                                    private String swsxDm;
                                    @JsonProperty("mzzzsxmxse")
                                    private Double mzzzsxmxse;
                                    @JsonProperty("bqsjkcje")
                                    private Double bqsjkcje;
                                    @JsonProperty("kchmsxse")
                                    private Double kchmsxse;
                                    @JsonProperty("msxsedyjxse")
                                    private Double msxsedyjxse;
                                    @JsonProperty("mse")
                                    private Double mse;
                                }
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class SbZzsXgmDTO {
                            @JsonProperty("ewblxh")
                            private String ewblxh;
                            @JsonProperty("ewbhlbm")
                            private String ewbhlbm;
                            @JsonProperty("lmc")
                            private String lmc;
                            @JsonProperty("yzzzsbhsxse")
                            private Double yzzzsbhsxse;
                            @JsonProperty("swjgdkdzzszyfpbhsxse")
                            private Double swjgdkdzzszyfpbhsxse;
                            @JsonProperty("skqjkjdptfpbhsxse")
                            private Double skqjkjdptfpbhsxse;
                            @JsonProperty("xsczbdcbhsxse")
                            private Double xsczbdcbhsxse;
                            @JsonProperty("swjgdkdzzszyfpbhsxse1")
                            private Double swjgdkdzzszyfpbhsxse1;
                            @JsonProperty("skqjkjdptfpbhsxse2")
                            private Double skqjkjdptfpbhsxse2;
                            @JsonProperty("xssygdysgdzcbhsxse")
                            private Double xssygdysgdzcbhsxse;
                            @JsonProperty("skqjkjdptfpbhsxse1")
                            private Double skqjkjdptfpbhsxse1;
                            @JsonProperty("msxse")
                            private Double msxse;
                            @JsonProperty("xwqymsxse")
                            private Double xwqymsxse;
                            @JsonProperty("wdqzdxse")
                            private Double wdqzdxse;
                            @JsonProperty("qtmsxse")
                            private Double qtmsxse;
                            @JsonProperty("ckmsxse")
                            private Double ckmsxse;
                            @JsonProperty("skqjkjdptfpxse1")
                            private Double skqjkjdptfpxse1;
                            @JsonProperty("hdxse")
                            private Double hdxse;
                            @JsonProperty("bqynse")
                            private Double bqynse;
                            @JsonProperty("hdynse")
                            private Double hdynse;
                            @JsonProperty("bqynsejze")
                            private Double bqynsejze;
                            @JsonProperty("bqmse")
                            private Double bqmse;
                            @JsonProperty("xwqymse")
                            private Double xwqymse;
                            @JsonProperty("wdqzdmse")
                            private Double wdqzdmse;
                            @JsonProperty("ynsehj")
                            private Double ynsehj;
                            @JsonProperty("bqyjse1")
                            private Double bqyjse1;
                            @JsonProperty("bqybtse")
                            private Double bqybtse;
                            @JsonProperty("bdcxse")
                            private Double bdcxse;
                            @JsonProperty("bqybtsecjs")
                            private Double bqybtsecjs;
                            @JsonProperty("bqybtsejyfj")
                            private Double bqybtsejyfj;
                            @JsonProperty("bqybtsedfjyfj")
                            private Double bqybtsedfjyfj;
                        }
                    }
                }
            }
        }
    }
}
