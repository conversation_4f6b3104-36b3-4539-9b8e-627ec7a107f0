package com.css.znsb.nssb.job.srhy;

import com.css.znsb.nssb.service.srhy.ZnsbNssbSrhyhzbService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ZnsbSrhyhzJob {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ZnsbNssbSrhyhzbService znsbNssbSrhyhzbService;

    /**
     * 收入还原汇总定时任务
     */
    @XxlJob("ZnsbSrhyhzJob")
    public void znsbSrhyhzExecute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("ZnsbSrhyhzJob start");
        String key = formatKey("ZnsbSrhyhzJob:mhzc");
        if (validateCache(key)) {
            try {
                String jobParam = XxlJobHelper.getJobParam();
                znsbNssbSrhyhzbService.clSrhyhzbSj(jobParam);
            } finally {
                delCache(key);
            }
        }
        stopWatch.stop();
        log.info("ZnsbSrhyhzJob end,RT:{}", stopWatch.getTotalTimeSeconds());
    }


    private Boolean validateCache(String key) {
        Boolean hasKey = stringRedisTemplate.hasKey(key);
        if (hasKey) {
            return false;
        } else {
            stringRedisTemplate.opsForValue().set(key, "1", 10, TimeUnit.HOURS);
            return true;
        }
    }

    private void delCache(String key) {
        stringRedisTemplate.delete(key);
    }

    private static String formatKey(String key) {
        return String.format("ZnsbSrhyhzJob:job:%s", key);
    }

}
