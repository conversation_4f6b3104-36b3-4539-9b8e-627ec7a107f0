
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《本期代收代缴税额计算表》（适用于涂料消费税纳税人）业务报文
 * 
 * <p>Java class for bqdsdjsejsbtlywbw complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="bqdsdjsejsbtlywbw">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.chinatax.gov.cn/dataspec/}taxDoc">
 *       &lt;sequence>
 *         &lt;element name="bqdsdjsejsbtl" type="{http://www.chinatax.gov.cn/dataspec/}bqdsdjsejsbtl"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bqdsdjsejsbtlywbw", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "bqdsdjsejsbtl"
})
public class Bqdsdjsejsbtlywbw
    extends TaxDoc
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected Bqdsdjsejsbtl bqdsdjsejsbtl;

    /**
     * Gets the value of the bqdsdjsejsbtl property.
     * 
     * @return
     *     possible object is
     *     {@link Bqdsdjsejsbtl }
     *     
     */
    public Bqdsdjsejsbtl getBqdsdjsejsbtl() {
        return bqdsdjsejsbtl;
    }

    /**
     * Sets the value of the bqdsdjsejsbtl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqdsdjsejsbtl }
     *     
     */
    public void setBqdsdjsejsbtl(Bqdsdjsejsbtl value) {
        this.bqdsdjsejsbtl = value;
    }

}
