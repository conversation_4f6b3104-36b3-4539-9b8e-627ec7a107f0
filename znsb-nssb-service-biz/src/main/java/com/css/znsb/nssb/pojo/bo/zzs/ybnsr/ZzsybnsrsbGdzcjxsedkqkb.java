package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《固定资产进项税额抵扣情况表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_gdzcjxsedkqkb", propOrder = { "sbbheadVO", "gdzcjxsedkqkbformVO" })
@Getter
@Setter
public class ZzsybnsrsbGdzcjxsedkqkb {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 《固定资产进项税额抵扣情况表》
     */
    @XmlElement(nillable = true, required = true)
    protected GdzcjxsedkqkbformVO gdzcjxsedkqkbformVO;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class GdzcjxsedkqkbformVO extends GdzcjxsedkqkbfromVO {}
}