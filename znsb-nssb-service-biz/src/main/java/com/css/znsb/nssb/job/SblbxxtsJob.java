package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SblbxxtsJob {

    @Resource
    private ZnsbNssbSbrwService znsbNssbSbrwService;

    /**
     * 申报漏报消息提醒
     */
    @XxlJob("SblbxxtsJob")
    public void execute() {
        log.info("==========开始执行申报漏报消息提示任务==========");
        znsbNssbSbrwService.sbrwlbtsJob();
        log.info("==========执行申报漏报消息提示任务结束==========");
    }

    /**
     * 缴款漏缴消息提醒
     */
    @XxlJob("JkljxxtsJob")
    public void execute1() {
        log.info("==========开始执行缴款漏缴消息提示任务==========");
        znsbNssbSbrwService.sbrwJkxxtsJob();
        log.info("==========执行缴款漏缴消息提示任务结束==========");
    }
}
