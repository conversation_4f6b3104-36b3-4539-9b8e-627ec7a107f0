package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.SbrwGjReqDTO;
import com.css.znsb.nssb.pojo.vo.jkxxcx.JkxxcxReqVO;
import com.css.znsb.nssb.pojo.vo.lqzlsbjk.nsrckzhbgxx.CxNsrckzhbgxxResVO;
import com.css.znsb.nssb.pojo.vo.lqzlsbjk.nsrckzhbgxx.GetSameLqCkzhzhxxReqVO;
import com.css.znsb.nssb.service.lqzlsbjk.nsrckzhzhxx.INsrckzhzhbgxxService;
import com.css.znsb.nssb.service.skjn.ZnsbSkjnService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 缴款信息归集
 */
@Slf4j
@Component
public class JkxxGjJob {

    @Resource
    private ZnsbSkjnService znsbSkjnService;

    /**
     * 缴款信息归集
     */
    @XxlJob("JkxxGjJob")
    public void execute() {
        log.info("==========开始缴款信息归集==========");
        final String jobParam = XxlJobHelper.getJobParam();
        log.info("入参jobParam{}",jobParam);
        znsbSkjnService.jkxxGj(jobParam);
        log.info("==========缴款信息归集完成========== ");
    }

}
