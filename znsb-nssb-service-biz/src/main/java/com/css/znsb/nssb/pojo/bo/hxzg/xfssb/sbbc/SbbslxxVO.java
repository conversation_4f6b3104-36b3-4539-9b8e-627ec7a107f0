package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;

/**
 * 申报表受理信息VO
 * 
 * <p>Java class for sbbslxxVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="sbbslxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="slrDm" type="{http://www.chinatax.gov.cn/dataspec/}slrDm" minOccurs="0"/>
 *         &lt;element name="slswjgDm" type="{http://www.chinatax.gov.cn/dataspec/}slswjgDm" minOccurs="0"/>
 *         &lt;element name="slrq" type="{http://www.chinatax.gov.cn/dataspec/}slrq"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbslxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = { "slrDm", "slswjgDm", "slrq" })
@XmlSeeAlso( { SlxxForm.class, Sbbslxxkz1VO.class, Sbbslxxkz2VO.class })
public class SbbslxxVO implements Serializable {

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String slrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String slswjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slrq;

    /**
     * Gets the value of the slrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlrDm() {
        return slrDm;
    }

    /**
     * Sets the value of the slrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlrDm(String value) {
        this.slrDm = value;
    }

    /**
     * Gets the value of the slswjgDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlswjgDm() {
        return slswjgDm;
    }

    /**
     * Sets the value of the slswjgDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlswjgDm(String value) {
        this.slswjgDm = value;
    }

    /**
     * Gets the value of the slrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlrq() {
        return slrq;
    }

    /**
     * Sets the value of the slrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlrq(String value) {
        this.slrq = value;
    }

}
