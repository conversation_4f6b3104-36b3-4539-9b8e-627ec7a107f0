package com.css.znsb.nssb.pojo.bo.hxzg.sb000;


import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;
import com.css.znsb.nssb.pojo.bo.hxzg.rd.general.RDNsrzgxxJgbVO;

import java.util.Date;
import java.util.List;

/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.sbzs.sb.sb000
 * @file SBBaseVO.java 创建时间:2014-7-15上午12:17:33
 * @title 申报基础VO（要求能简洁地表达出类的功能和职责）
 * @description 申报基础VO，各个申报VO继承（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class SBBaseVO extends TaxBaseVO {

    /**
     * @description 序列化注释
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 6083859168073724304L;

    /**
     * @description 申报表尾VO
     * @value value:sbbwvo
     */
    private SBSbbwVO sbbwvo;
    /**
     * @description 纳税人主体信息VO对象 
     * @value value:nsrxxsbvo
     */
    private SBNsrxxVO nsrxxsbvo;

    /**
     * @description 纳税人资格信息列表
     * @value value:nsrzgxxVOList
     */
    private List<RDNsrzgxxJgbVO> rdnsrzgxxJgbVOList;

    /**
     * @description 最早税费种认定列表
     * @value value:zzsfzrdVOList
     */
    private List<SBSbxxJyVO> zzsfzrdVOList;
    
    /**
     * @description 申报表信息VO对象集合
     * @value value:sbxxvoList
     */
    private List<SBSbxxVO> sbxxvoList;
    
    /**
     * @description 申报表信息备份对象VO对象集合
     * @value value:sbxxListForYssbxx
     */
    private List<SBSbxxVO> sbxxListForYssbxx;
    /**
     * @description 记录非注册地的应征
     * @value value:sbxxListForSkfjYz
     */
    private List<SBSbxxVO> sbxxListForSkfjYz;
    /**
     * @description 预缴税款信息VO对象集合
     * @value value:sbyjskxxvoList
     */
    private List<SBYjskxxVO> sbyjskxxvoList;

    /**
     * @description 申报减免对象VO信息集合
     * @value value:jmxxList
     */
    private List<SBJmxxVO> jmxxList;
    /**
     * @description 纳税人自查补报通知书UUID
     * @value value:zxbztzsuuid
     */
    private String zxbztzsuuid;
    /**
     * @description 是否回刷页面表头所填所属期标志
     * @value value:initSkssq
     */
    private boolean initBtSkssq = true;
    
    /**
     *分支标记
     */
    private String fzbj="N";

    /**
     * @description 初始化判断是否需要判断重复申报，默认需要控制
     * @value value:initCheckcfsb
     */
    private boolean initCheckcfsb = true;
    /**
     * @description 定义保存成功后返回变量数据信息，默认为0保存成功，为1为保存失败
     * @value value:successFlag
     */
    private String successFlag = "0";

    /**
     * @description 无税费种认定
     * @value value:noSfzrdBz
     */
    private String noSfzrdBz;

    /**
     * @description 重复申报（Y：表示重复申报   N：表示无申报信息）
     * @value value:cfsbBz
     */
    private String cfsbBz;

    /**
     * @description 重复申报反馈信息
     * @value value:checkMes(用于反馈重复申报征收品目名称)
     */
    private String cfsbMes;
    /**
     * @description 是否存在后续申报标志
     * @value value:sfczhxsbBz(Y：表示存在   N：表示不存在)
     */
    private String sfczhxsbBz;

    /**
     * @description 是否存在后续申报信息
     * @value value:sfczhxsbMes(是否存在后续申报信息)
     */
    private String sfczhxsbMes;
    /**
     * @description 登记日期
     * @value value:djrq
     */
    private Date djrq;

    /**
     * @description 上期未申报标记（Y表示上期未申报）
     * @value value:sqwsbBz
     */
    private String sqwsbBz;

    /**
     * @description 逾期申报标志（Y表示存在逾期未申报）
     * @value value:yqsbBz
     */
    private String yqsbBz;

    /**
     * @description 逾期申报信息
     * @value value:yqsbMes
     */
    private String yqsbMes;

    /**
     * @description 纳税评估自查补正通知书
     * @value value:sbNspgzcbbtzsVOList
     */
    private List<SBNspgzcbbtzsVO> sbNspgzcbbtzsVOList;

    /**
     * @description 错误更正返回凭证化vo对象
     * @value value:cwgzPzhvo
     */
    private Object cwgzPzhvo;
    /**
     * @description 凭证化标志,Y/N  Y 取公共的结构化报文、N自己查数据
     * @value value:pzhBz
     */
    private String pzhBz="Y";
    
    /**
     * @description 上期未申报反馈信息
     * @value value:  sqwsbMes
     */
    private String sqwsbMes;
    
    private List<SByqtfpxxVO> yqtfpxxList;
    
    /**
     * @description 是否判断上期未申报标志
     * @value value:  sfpdsqwsbBz
     */
    private String sfpdsqwsbBz = "Y";
    
    private String sfKdqssfxskFlag="N";
    private String channelId;
    
    /**
     * 上期油气田分配list
     */
    private List<SByqtfpxxVO> sqfpList;

    public String getSfpdsqwsbBz() {
        return sfpdsqwsbBz;
    }

    public void setSfpdsqwsbBz(String sfpdsqwsbBz) {
        this.sfpdsqwsbBz = sfpdsqwsbBz;
    }

    public String getSqwsbMes() {
        return sqwsbMes;
    }

    public void setSqwsbMes(String sqwsbMes) {
        this.sqwsbMes = sqwsbMes;
    }

    /**
     *创建时间:2014-9-22下午11:20:43
     *get方法
     * @return the pzhBz
     */
    public String getPzhBz() {
        return pzhBz;
    }

    /**
     *创建时间:2015-9-6上午11:31:12
     *get方法
     * @return the sbxxListForYssbxx
     */
    public List<SBSbxxVO> getSbxxListForYssbxx() {
        return sbxxListForYssbxx;
    }

    /**
     *创建时间:2015-9-6上午11:31:12
     *get方法
     * @return the sbxxListForSkfjYz
     */
    public List<SBSbxxVO> getSbxxListForSkfjYz() {
        return sbxxListForSkfjYz;
    }

    /**
     * 创建时间:2015-9-6上午11:31:12
     * set方法
     * @param sbxxListForYssbxx the sbxxListForYssbxx to set
     */
    public void setSbxxListForYssbxx(List<SBSbxxVO> sbxxListForYssbxx) {
        this.sbxxListForYssbxx = sbxxListForYssbxx;
    }

    /**
     * 创建时间:2015-9-6上午11:31:12
     * set方法
     * @param sbxxListForSkfjYz the sbxxListForSkfjYz to set
     */
    public void setSbxxListForSkfjYz(List<SBSbxxVO> sbxxListForSkfjYz) {
        this.sbxxListForSkfjYz = sbxxListForSkfjYz;
    }

    /**
     * 创建时间:2014-9-22下午11:20:43
     * set方法
     * @param pzhBz the pzhBz to set
     */
    public void setPzhBz(String pzhBz) {
        this.pzhBz = pzhBz;
    }

    /**
     *创建时间:2014-9-10上午10:31:01
     *get方法
     * @return the cwgzPzhvo
     */
    public Object getCwgzPzhvo() {
        return cwgzPzhvo;
    }

    /**
     * 创建时间:2014-9-10上午10:31:01
     * set方法
     * @param cwgzPzhvo the cwgzPzhvo to set
     */
    public void setCwgzPzhvo(Object cwgzPzhvo) {
        this.cwgzPzhvo = cwgzPzhvo;
    }

    /**
     *创建时间:2014-9-10上午10:31:01
     *get方法
     * @return the sbNspgzcbbtzsVOList
     */
    public List<SBNspgzcbbtzsVO> getSbNspgzcbbtzsVOList() {
        return sbNspgzcbbtzsVOList;
    }

    /**
     * 创建时间:2014-9-10上午10:31:01
     * set方法
     * @param sbNspgzcbbtzsVOList the sbNspgzcbbtzsVOList to set
     */
    public void setSbNspgzcbbtzsVOList(List<SBNspgzcbbtzsVO> sbNspgzcbbtzsVOList) {
        this.sbNspgzcbbtzsVOList = sbNspgzcbbtzsVOList;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the nsrxxsbvo
     */
    public SBNsrxxVO getNsrxxsbvo() {
        return nsrxxsbvo;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param nsrxxsbvo the nsrxxsbvo to set
     */
    public void setNsrxxsbvo(SBNsrxxVO nsrxxsbvo) {
        this.nsrxxsbvo = nsrxxsbvo;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the rdnsrzgxxJgbVOList
     */
    public List<RDNsrzgxxJgbVO> getRdnsrzgxxJgbVOList() {
        return rdnsrzgxxJgbVOList;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param rdnsrzgxxJgbVOList the rdnsrzgxxJgbVOList to set
     */
    public void setRdnsrzgxxJgbVOList(List<RDNsrzgxxJgbVO> rdnsrzgxxJgbVOList) {
        this.rdnsrzgxxJgbVOList = rdnsrzgxxJgbVOList;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the sbxxvoList
     */
    public List<SBSbxxVO> getSbxxvoList() {
        return sbxxvoList;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param sbxxvoList the sbxxvoList to set
     */
    public void setSbxxvoList(List<SBSbxxVO> sbxxvoList) {
        this.sbxxvoList = sbxxvoList;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the sbyjskxxvoList
     */
    public List<SBYjskxxVO> getSbyjskxxvoList() {
        return sbyjskxxvoList;
    }

    /**
     *创建时间:2015-9-8下午03:00:41
     *get方法
     * @return the sfKdqssfxskFlag
     */
    public String getSfKdqssfxskFlag() {
        return sfKdqssfxskFlag;
    }

    /**
     * 创建时间:2015-9-8下午03:00:41
     * set方法
     * @param sfKdqssfxskFlag the sfKdqssfxskFlag to set
     */
    public void setSfKdqssfxskFlag(String sfKdqssfxskFlag) {
        this.sfKdqssfxskFlag = sfKdqssfxskFlag;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param sbyjskxxvoList the sbyjskxxvoList to set
     */
    public void setSbyjskxxvoList(List<SBYjskxxVO> sbyjskxxvoList) {
        this.sbyjskxxvoList = sbyjskxxvoList;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the jmxxList
     */
    public List<SBJmxxVO> getJmxxList() {
        return jmxxList;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param jmxxList the jmxxList to set
     */
    public void setJmxxList(List<SBJmxxVO> jmxxList) {
        this.jmxxList = jmxxList;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the zxbztzsuuid
     */
    public String getZxbztzsuuid() {
        return zxbztzsuuid;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param zxbztzsuuid the zxbztzsuuid to set
     */
    public void setZxbztzsuuid(String zxbztzsuuid) {
        this.zxbztzsuuid = zxbztzsuuid;
    }

    /**
     *创建时间:2014-09-21 上午10:13:25
     * @return the initCheckcfsb
     */
    public boolean isInitCheckcfsb() {
        return initCheckcfsb;
    }

    /**
     *创建时间:2014-09-21 上午10:13:25
     *set方法
     * @return the initCheckcfsb
     */
    public void setInitCheckcfsb(boolean initCheckcfsb) {
        this.initCheckcfsb = initCheckcfsb;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the successFlag
     */
    public String getSuccessFlag() {
        return successFlag;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param successFlag the successFlag to set
     */
    public void setSuccessFlag(String successFlag) {
        this.successFlag = successFlag;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the noSfzrdBz
     */
    public String getNoSfzrdBz() {
        return noSfzrdBz;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param noSfzrdBz the noSfzrdBz to set
     */
    public void setNoSfzrdBz(String noSfzrdBz) {
        this.noSfzrdBz = noSfzrdBz;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the cfsbBz
     */
    public String getCfsbBz() {
        return cfsbBz;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param cfsbBz the cfsbBz to set
     */
    public void setCfsbBz(String cfsbBz) {
        this.cfsbBz = cfsbBz;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the cfsbMes
     */
    public String getCfsbMes() {
        return cfsbMes;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param cfsbMes the cfsbMes to set
     */
    public void setCfsbMes(String cfsbMes) {
        this.cfsbMes = cfsbMes;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the djrq
     */
    public Date getDjrq() {
        return djrq;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param djrq the djrq to set
     */
    public void setDjrq(Date djrq) {
        this.djrq = djrq;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the sqwsbBz
     */
    public String getSqwsbBz() {
        return sqwsbBz;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param sqwsbBz the sqwsbBz to set
     */
    public void setSqwsbBz(String sqwsbBz) {
        this.sqwsbBz = sqwsbBz;
    }

    /**
     *创建时间:2014-7-26上午10:13:25
     *get方法
     * @return the yqsbBz
     */
    public String getYqsbBz() {
        return yqsbBz;
    }

    /**
     * 创建时间:2014-7-26上午10:13:25
     * set方法
     * @param yqsbBz the yqsbBz to set
     */
    public void setYqsbBz(String yqsbBz) {
        this.yqsbBz = yqsbBz;
    }

    public boolean isInitBtSkssq() {
        return initBtSkssq;
    }

    public void setInitBtSkssq(boolean initBtSkssq) {
        this.initBtSkssq = initBtSkssq;
    }

    public SBSbbwVO getSbbwvo() {
        return sbbwvo;
    }

    public void setSbbwvo(SBSbbwVO sbbwvo) {
        this.sbbwvo = sbbwvo;
    }

    /**
     * get方法
     * @return the zzsfzrdVOList
     */
    public List<SBSbxxJyVO> getZzsfzrdVOList() {
        return zzsfzrdVOList;
    }

    /**
     * set方法
     * @param zzsfzrdVOList the zzsfzrdVOList to set
     */
    public void setZzsfzrdVOList(List<SBSbxxJyVO> zzsfzrdVOList) {
        this.zzsfzrdVOList = zzsfzrdVOList;
    }

    /**
     * get方法
     * @return the yqsbMes
     */
    public String getYqsbMes() {
        return yqsbMes;
    }

    /**
     * set方法
     * @param yqsbMes the yqsbMes to set
     */
    public void setYqsbMes(String yqsbMes) {
        this.yqsbMes = yqsbMes;
    }

    /**
     *创建时间:2014-9-24上午11:47:52
     *get方法
     * @return the fzbj
     */
    public String getFzbj() {
        return fzbj;
    }

    /**
     * 创建时间:2014-9-24上午11:47:52
     * set方法
     * @param fzbj the fzbj to set
     */
    public void setFzbj(String fzbj) {
        this.fzbj = fzbj;
    }

    /**
     *创建时间:2016-12-22上午11:20:47
     *get方法
     * @return the yqtfpxxList
     */
    public List<SByqtfpxxVO> getYqtfpxxList() {
        return yqtfpxxList;
    }

    /**
     * 创建时间:2016-12-22上午11:20:47
     * set方法
     * @param yqtfpxxList the yqtfpxxList to set
     */
    public void setYqtfpxxList(List<SByqtfpxxVO> yqtfpxxList) {
        this.yqtfpxxList = yqtfpxxList;
    }

    /**
     *创建时间:2017-6-5下午02:00:20
     *get方法
     * @return the sqfpList
     */
    public List<SByqtfpxxVO> getSqfpList() {
        return sqfpList;
    }

    /**
     * 创建时间:2017-6-5下午02:00:20
     * set方法
     * @param sqfpList the sqfpList to set
     */
    public void setSqfpList(List<SByqtfpxxVO> sqfpList) {
        this.sqfpList = sqfpList;
    }
    /**
     *创建时间:2017-11-14下午02:00:20
     *get方法
     * @return the sfczhxsbBz
     */
    public String getSfczhxsbBz() {
        return sfczhxsbBz;
    }
    /**
     * 创建时间:2017-11-14下午02:00:20
     * set方法
     * @param sfczhxsbBz  to set
     */
    public void setSfczhxsbBz(String sfczhxsbBz) {
        this.sfczhxsbBz = sfczhxsbBz;
    }
    /**
     *创建时间:2017-11-14下午02:00:20
     *get方法
     * @return the sfczhxsbMes
     */
    public String getSfczhxsbMes() {
        return sfczhxsbMes;
    }
    /**
     * 创建时间:2017-11-14下午02:00:20
     * set方法
     * @param sfczhxsbMes to set
     */
    public void setSfczhxsbMes(String sfczhxsbMes) {
        this.sfczhxsbMes = sfczhxsbMes;
    }
    /**
     *创建时间:2017-11-14下午02:00:20
     *get方法
     * @return the channelId
     */
    public String getChannelId() {
        return channelId;
    }
    /**
     * 创建时间:2017-11-14下午02:00:20
     * set方法
     * @param channelId to set
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }
}
