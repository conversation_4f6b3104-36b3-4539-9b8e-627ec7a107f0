package com.css.znsb.nssb.job;

import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDOMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDo;
import com.css.znsb.nssb.pojo.dto.cchxwssb.fcscztdsys.*;
import com.css.znsb.nssb.pojo.dto.cchxwssb.fcscztdsys.cztdsys.DataPlatformCztdsysDTO;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.CztdsysTdxxCxVO;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.CztdsysService;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.impl.FtsDataPlatformServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CztdsysCxJob {

    @Resource
    private CztdsysService cztdsysService;
    @Resource
    private CompanyApi companyApi;
    @Resource
    private FtsDataPlatformServiceImpl ftsDataPlatformService;
    @Resource
    private ZnsbNssbCxsTdsyxxcjbDOMapper znsbNssbCxsTdsyxxcjbDOMapper;

    @XxlJob("CztdsysCxJob")
    public void execute() {
        log.info("==========开始房城镇土地使用税归集==========");
        String djxh = XxlJobHelper.getJobParam();
        log.info("入参登记序号djxh{}",djxh);
        final CommonResult<List<CompanyInfoResDTO>> response = companyApi.getAllCompanyInfo();
        final List<CompanyInfoResDTO> companyInfoResDTOS = response.getData();
        for(CompanyInfoResDTO companyInfo : companyInfoResDTOS){
            try {
                if(GyUtils.isNotNull(djxh) && !djxh.equals(companyInfo.getDjxh())){
                    continue;
                }
                final CommonResult<CompanyBasicInfoDTO> resData = companyApi.basicInfo(companyInfo.getDjxh(),companyInfo.getNsrsbh());
                if (GyUtils.isNull(resData) && GyUtils.isNull(resData.getData())){
                    log.info("{}企业基本信息为空",companyInfo.getDjxh());
                    continue;
                }
                final CompanyBasicInfoDTO basicInfoDTO = resData.getData();
                if (GyUtils.isNull(basicInfoDTO)){
                    log.info("{}企业基本信息为空",companyInfo.getDjxh());
                    continue;
                }
                final Map<String,Object> reqMap = new HashMap<>();
                reqMap.put("djxh", basicInfoDTO.getDjxh());
                reqMap.put("pageNum", 1);
                reqMap.put("pageSize", 999);
                final FcsCztdsysResquestDTO req  = new FcsCztdsysResquestDTO();
                req.setDjxh(basicInfoDTO.getDjxh());
                req.setNsrsbh(basicInfoDTO.getNsrsbh());
                req.setYwbw(JsonUtils.toJson(reqMap));
                log.info("归集任务查询城镇土地时入参req{}", JsonUtils.toJson(req));
                final CommonResult<Object> result= cztdsysService.cxCztdsyszdxxbylq(req);
                log.info("归集任务查询城镇土地时入参result{}", JsonUtils.toJson(result));
                if(result.getCode() == 1  && GyUtils.isNotNull(result.getData())) {
                    final CztdsysTdsyzdxxResponseDTO respDTO = JsonUtils.toBean((String)result.getData(),CztdsysTdsyzdxxResponseDTO.class) ;
                    if(GyUtils.isNotNull(respDTO) && GyUtils.isNotNull(respDTO.getSBCxsTdsyxxcjbV0())){
                        final List<SBCxsTdsyxxcjbV0> sbCxsTdsyxxcjbV0List = respDTO.getSBCxsTdsyxxcjbV0();
                        final CztdsysTdxxCxVO cztdsysTdxxCxVO = new CztdsysTdxxCxVO();
                        cztdsysTdxxCxVO.setDjxh(basicInfoDTO.getDjxh());
                        final List<ZnsbNssbCxsTdsyxxcjbDo> doList = znsbNssbCxsTdsyxxcjbDOMapper.queryTdxxList(cztdsysTdxxCxVO);
                        for(ZnsbNssbCxsTdsyxxcjbDo cxsTdsyxxcjbDo : doList){
                            if (GyUtils.isNull(cxsTdsyxxcjbDo.getSyuuid())) {
                                continue;
                            }
                            boolean flag = true;
                            for(SBCxsTdsyxxcjbV0 tdsyxxcjbV0 : sbCxsTdsyxxcjbV0List){
                                if(cxsTdsyxxcjbDo.getSyuuid().equals(tdsyxxcjbV0.getSyuuid())){
                                    flag = false;
                                    break;
                                }
                            }
                            log.info("syuuid{},flag{}", cxsTdsyxxcjbDo.getSyuuid(),flag);
                            if(flag){
                                cxsTdsyxxcjbDo.setYxbz("N");
                                znsbNssbCxsTdsyxxcjbDOMapper.updateById(cxsTdsyxxcjbDo);
                            }
                        }
                        for(SBCxsTdsyxxcjbV0 sbCxsTdsyxxcjbV0:sbCxsTdsyxxcjbV0List){
                            try{
                                cztdsysService.saveCztdsysSyxx(sbCxsTdsyxxcjbV0,basicInfoDTO.getNsrsbh());
                            }catch (Exception e){
                                log.info(sbCxsTdsyxxcjbV0.getTdsybh()+"城镇土地使用税归集异常{}",e);
                            }
                        }
                    }
                }
            }catch (Exception e){
                log.info("城镇土地使用税归集异常{}",e);
            }
        }
        log.info("==========城镇土地使用税归集结束==========");
    }

    @XxlJob("CztdsysDataPlatform")
    public void cztdsysDataPlatform() {
        log.info("==========开始城镇土地使用税数据提取==========");
        final String sxydqypzcsz = CacheUtils.getXtcs("NSSB_FTS_SXYD_QYPZ","N");
        log.info("sxydqypzcsz{}",sxydqypzcsz);
        String nsrsbh = XxlJobHelper.getJobParam();
        log.info("入参纳税人识别号nsrsbh{}",nsrsbh);
        final CommonResult<List<CompanyInfoResDTO>> response = companyApi.getAllCompanyInfo();
        final List<CompanyInfoResDTO> companyInfoResDTOS = response.getData();
        for(CompanyInfoResDTO companyInfo : companyInfoResDTOS){
            try {
                if(GyUtils.isNotNull(nsrsbh) && !nsrsbh.equals(companyInfo.getNsrsbh())){
                    continue;
                }
                if(GyUtils.isNotNull(sxydqypzcsz) && sxydqypzcsz.contains(companyInfo.getNsrsbh())){
                    continue;
                }
                final CompanyBasicInfoDTO basicInfo = companyApi.basicInfo(companyInfo.getDjxh(),companyInfo.getNsrsbh()).getData();
                if(GyUtils.isNotNull(basicInfo) && "3".equals(basicInfo.getQylxz())){
                    continue;
                }
                final List<DataPlatformCztdsysDTO> data = ftsDataPlatformService.handle(companyInfo.getNsrsbh(),DataPlatformCztdsysDTO.class);
                for(DataPlatformCztdsysDTO dataPlatformFcsDTO : data){
                    dataPlatformFcsDTO.setDjxh(companyInfo.getDjxh());
                    dataPlatformFcsDTO.setNsrsbh(companyInfo.getNsrsbh());
                    cztdsysService.saveOrUpdateCztdsys(dataPlatformFcsDTO);
                }
            }catch (Exception e){
                log.error("城镇土地使用税据提取异常",e);
            }
        }
        log.info("==========城镇土地使用税数据提取结束==========");
    }
}
