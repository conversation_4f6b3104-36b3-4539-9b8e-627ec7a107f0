package com.css.znsb.nssb.utils;

import com.css.znsb.nssb.pojo.vo.fcscztdsys.*;
import com.css.znsb.nssb.util.DateUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 房土税数据检验
 */
@Component
public class FtsCheckUtils {

    /**
     * 检验土地信息
     * @param cztdsysTdxxVO 入参
     * @param clbz 1-仅检验土地信息，2-检验土地信息+应税信息
     * @param drbz Y-导入，N-非导入
     * @return 返回值
     */
    public static String checkTdxx(CztdsysTdxxVO cztdsysTdxxVO, String clbz, String drbz) {
        final StringBuffer messageBuffer = new StringBuffer();

        //1.基本字段检验，必录，码值，长度，格式
        //1.1.session字段，仅检查是否为空
        final String djxh = cztdsysTdxxVO.getDjxh();
        if (FtsUtils.isNull(djxh)) {
            messageBuffer.append("登记序号为空，请核实后处理。");
        }
        //1.2.土地税源编号，可为空，检验长度VARCHAR2(75)
        final String tdsybh = cztdsysTdxxVO.getTdsybh();
        final boolean flagTdsybh = FtsUtils.checkMaxLength(tdsybh, 75);
        if (!flagTdsybh) {
            messageBuffer.append("土地编号字段超长，请核实后处理。");
        }
        //1.3.土地名称，可为空，检验长度VARCHAR2(300)
        final String tdmc1 = cztdsysTdxxVO.getTdmc1();
        final boolean flagTdmc1 = FtsUtils.checkMaxLength(tdmc1, 300);
        if (!flagTdmc1) {
            messageBuffer.append("土地名称字段超长，请核实后处理。");
        }
        //1.4.土地用途，不可为空，检验码值(不需检验长度)
        final String tdytDm = cztdsysTdxxVO.getTdytDm();
        if (FtsUtils.isNull(tdytDm)) {
            messageBuffer.append("土地用途为空，请核实后处理。");
        } else {
            final String tdytmc = FtsUtils.getMcByDm("dm_sb_tdyt", "tdytmc", tdytDm);
            if (FtsUtils.isNull(tdytmc)) {
                messageBuffer.append("土地用途码值不正确，请核实后处理。");
            }
        }
        //1.5.土地性质，不可为空，检验码值(不需检验长度)
        final String tdxzDm = cztdsysTdxxVO.getTdxzDm();
        if (FtsUtils.isNull(tdxzDm)) {
            messageBuffer.append("土地性质为空，请核实后处理。");
        } else {
            final String tdxzmc = FtsUtils.getMcByDm("dm_sb_tdxz", "tdxzmc", tdxzDm);
            if (FtsUtils.isNull(tdxzmc)) {
                messageBuffer.append("土地性质码值不正确，请核实后处理。");
            }
        }
        //1.6.土地取得方式，不可为空，码值检验
        final String tdqdfsDm = cztdsysTdxxVO.getTdqdfsDm();
        if (FtsUtils.isNull(tdqdfsDm)) {
            messageBuffer.append("土地取得方式为空，请核实后处理。");
        } else {
            final String tdqdfsmc = FtsUtils.getMcByDm("dm_sb_tdqdfs", "tdqdfsmc", tdqdfsDm);
            if (FtsUtils.isNull(tdqdfsmc)) {
                messageBuffer.append("土地取得方式码值不正确，请核实后处理。");
            }
        }
        //1.7占用土地面积，不可为空，大于0，精度NUMBER(14,2)
        final double zytdmj1 = cztdsysTdxxVO.getZytdmj1();
        if (FtsUtils.isNull(zytdmj1)) {
            messageBuffer.append("占用土地面积为空，请核实后处理。");
        } else {
            final boolean flagZytdmj1 = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(zytdmj1)).toString(), 12, 2);
            if (!flagZytdmj1) {
                messageBuffer.append("占用土地面积数值精度不正确，请核实后处理。");
            }
            if (zytdmj1 <= 0) {
                messageBuffer.append("占用土地面积需大于0，请核实后处理。");
            }
        }
        //1.8.土地地价，可为空，大于等于0，精度NUMBER(18,2)
        final Double tddj = cztdsysTdxxVO.getTddj();
        if (!FtsUtils.isNull(tddj)) {
            final boolean flagTddj = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(tddj)).toString(), 12, 2);
            if (!flagTddj) {
                messageBuffer.append("土地地价数值精度不正确，请核实后处理。");
            }
            if (tddj < 0) {
                messageBuffer.append("土地地价不能为负数，请核实后处理。");
            }
        }
        //1.9.土地取得时间，不可为空，日期类型，不能大于当前日期
        final String csqdsj = cztdsysTdxxVO.getCsqdsj();
        if (FtsUtils.isNull(csqdsj)) {
            messageBuffer.append("土地取得时间为空，请核实后处理。");
        } else {
            final boolean flagCsqdsj = FtsUtils.checkDate(csqdsj, "yyyy-MM-dd");
            if (!flagCsqdsj) {
                messageBuffer.append("土地取得时间日期格式不正确，请核实后处理。");
            }else {
                final Date now = new Date();
                if (now.before(DateUtil.toDate("yyyy-MM-dd", csqdsj))) {
                    messageBuffer.append("土地取得时间不能大于当前日期，请核实后处理。");
                }
            }
        }
        //1.10.宗地号，可为空，检验长度VARCHAR2(300)
        final String dh1 = cztdsysTdxxVO.getDh1();
        final boolean flagDh1 = FtsUtils.checkMaxLength(dh1, 300);
        if (!flagDh1) {
            messageBuffer.append("宗地号字段超长，请核实后处理。");
        }
        //1.11.纳税人类型，不可为空，码值检验
        final String nsrlx = cztdsysTdxxVO.getNsrlx();
        if (FtsUtils.isNull(nsrlx)) {
            //messageBuffer.append("纳税人类型为空，请核实后处理。");
        } else {
            final String nsrlxmc = FtsUtils.getMcByDm("dm_sb_tdnsrlx", "tdnsrlxmc", nsrlx);
            if (FtsUtils.isNull(nsrlxmc)) {
                messageBuffer.append("纳税人类型码值不正确，请核实后处理。");
            }
        }
        //1.12.土地使用权人纳税人识别号，可为空，检验长度VARCHAR2(20)
        final String tdsyqrnsrsbh = cztdsysTdxxVO.getTdsyqrnsrsbh();
        final boolean flagTdsyqrnsrsbh = FtsUtils.checkMaxLength(tdsyqrnsrsbh, 20);
        if (!flagTdsyqrnsrsbh) {
            messageBuffer.append("土地使用权人纳税人识别号字段超长，请核实后处理。");
        }
        //1.13.土地使用权人纳税人名称，可为空，检验长度VARCHAR2(300)
        final String tdsyqrmc = cztdsysTdxxVO.getTdsyqrmc();
        final boolean flagTdsyqrmc = FtsUtils.checkMaxLength(tdsyqrmc, 300);
        if (!flagTdsyqrmc) {
            messageBuffer.append("土地使用权人纳税人名称字段超长，请核实后处理。");
        }
        //1.14.土地使用权人登记序号，可为空，检验长度VARCHAR2(20)
        final String tdsyqrdjxh = cztdsysTdxxVO.getTdsyqrdjxh();
        final boolean flagTdsyqrdjxh = FtsUtils.checkMaxLength(tdsyqrdjxh, 300);
        if (!flagTdsyqrdjxh) {
            messageBuffer.append("土地使用权人纳税人登记序号字段超长，请核实后处理。");
        }
        //1.15.不动产单元代码，可为空，检验长度VARCHAR2(40)
        final String bdcdyh = cztdsysTdxxVO.getBdcdyh();
        final boolean flagBdcdyh = FtsUtils.checkMaxLength(bdcdyh, 40);
        if (!flagBdcdyh) {
            messageBuffer.append("不动产单元号字段超长，请核实后处理。");
        }
        //1.16.不动产权证号，可为空，检验长度VARCHAR2(75)
        final String tdsyzbh = cztdsysTdxxVO.getTdsyzbh();
        final boolean flagTdsyzbh = FtsUtils.checkMaxLength(tdsyzbh, 75);
        if (!flagTdsyzbh) {
            messageBuffer.append("不动产权证书号字段超长，请核实后处理。");
        }
        //1.17.行政区划，不可为空，码值检查
        final String xzqhszDm = cztdsysTdxxVO.getXzqhszDm();
        if (FtsUtils.isNull(xzqhszDm)) {
            messageBuffer.append("土地坐落地址（行政区划）为空，请核实后处理。");
        } else {
            final String mcByDm = FtsUtils.getMcByDm("dm_gy_xzqh", "xzqhmc", xzqhszDm);
            if (FtsUtils.isNull(mcByDm)) {
                messageBuffer.append("土地坐落地址（行政区划）码值不正确，请核实后处理。");
            }
        }
        //1.18.街道乡镇，不可为空，码值检查
        final String jdxzDm = cztdsysTdxxVO.getJdxzDm();
        if (FtsUtils.isNull(jdxzDm)) {
            messageBuffer.append("土地坐落地址（所处街乡）为空，请核实后处理。");
        } else {
            final String mcByDm = FtsUtils.getMcByDm("dm_gy_jdxz", "jdxzmc", jdxzDm);
            if (FtsUtils.isNull(mcByDm)) {
                messageBuffer.append("土地坐落地址（所处街乡）码值不正确，请核实后处理。");
            }
        }
        //1.19.土地坐落地址，不可为空，检验长度VARCHAR2(300)
        final String tdzldz = cztdsysTdxxVO.getTdzldz();
        if (FtsUtils.isNull(tdzldz)) {
            messageBuffer.append("土地坐落地址为空，请核实后处理。");
        } else {
            final boolean flagTdzldz = FtsUtils.checkMaxLength(tdzldz, 300);
            if (!flagTdzldz) {
                messageBuffer.append("土地坐落地址字段长度不正确，请核实后处理。");
            }
        }
        //1.20.房屋所属税务所（科、分局），不可为空，码值检查
        final String zgswskfjDm = cztdsysTdxxVO.getZgswskfjDm();
        if (FtsUtils.isNull(zgswskfjDm)) {
            messageBuffer.append("土地所属主管税务所（科、分局）为空，请核实后处理。");
        } else {
            final String mcByDm = FtsUtils.getMcByDm("dm_gy_swjg", "swjgmc", zgswskfjDm);
            if (FtsUtils.isNull(mcByDm)) {
                messageBuffer.append("土地所属主管税务所（科、分局）码值不正确，请核实后处理。");
            }
        }
        //1.21.此时如果检验异常，返回
        if (!FtsUtils.isNull(messageBuffer.toString())) {
            return messageBuffer.toString();
        }

        //2.基本联动规则检验
        //2.1.有效期起不能为空，且为初始取得时间下月第一天
        final String yxqq = cztdsysTdxxVO.getYxqq();
        if (FtsUtils.isNull(yxqq)) {
            messageBuffer.append("有效期起为空，请核实后处理。");
        } else {
            final boolean flagYxqq = FtsUtils.checkDate(yxqq, "yyyy-MM-dd");
            if (!flagYxqq) {
                messageBuffer.append("有效期起时间日期格式不正确，请核实后处理。");
            } else {
                final Date dateCsqdsj = DateUtil.toDate("yyyy-MM-dd",csqdsj);
                final Date firstCsqdsj = FtsUtils.getFirstDayOfMonth(dateCsqdsj);
                final Date dateYxqq = DateUtil.toDate("yyyy-MM-dd",yxqq);
                final Date firstYxqq = FtsUtils.getFirstDayOfMonth(dateYxqq);
                if (dateYxqq.compareTo(firstYxqq) != 0 || firstCsqdsj.compareTo(firstYxqq) > 0 || FtsUtils.jsYfkd(firstCsqdsj, firstYxqq) != 2) {
                    messageBuffer.append("有效期起应为取得时间的下月一号，请核实后处理。");
                }
            }
        }
        //2.2此时如果检验异常，返回
        if (!FtsUtils.isNull(messageBuffer.toString())) {
            return messageBuffer.toString();
        }

        //3.检验应税信息
        if ("2".equals(clbz)) {
            if (FtsUtils.isNull(cztdsysTdxxVO.getCztdsysYsxxVO()) || FtsUtils.isNull(cztdsysTdxxVO.getCztdsysYsxxVO().getTdsyysxxVOList())) {
                messageBuffer.append("土地应税信息为空，请核实后处理。");
            } else {
                final List<TdsyysxxVO> tdsyysxxVOList = cztdsysTdxxVO.getCztdsysYsxxVO().getTdsyysxxVOList();
                for (TdsyysxxVO tdsyysxxVO : tdsyysxxVOList) {
                    final String ysxxMsg = checkTdYsxx(tdsyysxxVO, drbz ,cztdsysTdxxVO);
                    if (!FtsUtils.isNull(ysxxMsg)){
                        messageBuffer.append(ysxxMsg);
                        break;
                    }

                }
            }
            if (!FtsUtils.isNull(messageBuffer.toString())) {
                return messageBuffer.toString();
            }
        }
        return "";
    }

    /**
     * 土地应税信息检查
     * @param tdsyysxxVO 入参
     * @param drbz Y-导入，N-非导入
     * @param cztdsysTdxxVO 为空为单独保存，不为空为整体保存
     * @return 返回值
     */
    public static String checkTdYsxx(TdsyysxxVO tdsyysxxVO, String drbz ,CztdsysTdxxVO cztdsysTdxxVO) {
        final StringBuffer messageBuffer = new StringBuffer();
        //1.基本字段检验，必录，码值，长度，格式
        //1.1.土地等级，不可为空，码值检验
        final String tddjDm = tdsyysxxVO.getTddjDm();
        if (FtsUtils.isNull(tddjDm)) {
            messageBuffer.append("土地等级为空，请核实后处理。");
        } else {
            final String tdqdfsmc = FtsUtils.getMcByDm("dm_dj_tddj","tddjmc",tddjDm);
            if (FtsUtils.isNull(tdqdfsmc)) {
                messageBuffer.append("土地等级码值不正确，请核实后处理。");
            }
        }
        //1.2.单位税额，不可为空，大于0，精度NUMBER(12,2)
        final double dwse = tdsyysxxVO.getDwse();
        if (FtsUtils.isNull(dwse)) {
            messageBuffer.append("单位税额为空，请核实后处理。");
        } else {
            final boolean flagDwse = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(dwse)).toString(), 12, 2);
            if (!flagDwse) {
                messageBuffer.append("单位税额数值精度不正确，请核实后处理。");
            }
            if (dwse < 0) {
                messageBuffer.append("单位税额不能小于0，请核实后处理。");
            }
        }
        //1.3.占用土地面积，不可为空，大于0，精度NUMBER(14,2)
        final double ystdmj = tdsyysxxVO.getYstdmj();
        if (FtsUtils.isNull(ystdmj)) {
            messageBuffer.append("占用土地面积为空，请核实后处理。");
        } else {
            final boolean flagYstdmj = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(ystdmj)).toString(), 12, 2);
            if (!flagYstdmj) {
                messageBuffer.append("占用土地面积精度不正确，请核实后处理。");
            }
            if (ystdmj <= 0) {
                messageBuffer.append("占用土地面积需大于0，请核实后处理。");
            }
        }
        //1.4.征收品目，不可为空，码值检验
        final String zspmDm = tdsyysxxVO.getZspmDm();
        if (FtsUtils.isNull(zspmDm)) {
            messageBuffer.append("征收品目为空，请核实后处理。");
        } else {
            final String zspmmc = FtsUtils.getMcByDm("dm_gy_zspm","zspmmc",zspmDm);
            if (FtsUtils.isNull(zspmmc)) {
                messageBuffer.append("征收品目码值不正确，请核实后处理。");
            }
        }
        //1.5.有效期起不能为空
        final String yxqq = tdsyysxxVO.getYxqq();
        if (FtsUtils.isNull(yxqq)) {
            messageBuffer.append("有效期起为空，请核实后处理。");
        } else {
            final boolean flagYxqq = FtsUtils.checkDate(yxqq, "yyyy-MM-dd");
            if (!flagYxqq) {
                messageBuffer.append("有效期起时间日期格式不正确，请核实后处理。");
            }
        }
        //1.6.有效期止不能为空
        final String yxqz = tdsyysxxVO.getYxqz();
        if (FtsUtils.isNull(yxqz)) {
            messageBuffer.append("有效期止为空，请核实后处理。");
        } else {
            final boolean flagYxqz = FtsUtils.checkDate(yxqz, "yyyy-MM-dd");
            if (!flagYxqz) {
                messageBuffer.append("有效期止时间日期格式不正确，请核实后处理。");
            }
        }
        //1.6此时如果检验异常，返回
        if (!FtsUtils.isNull(messageBuffer.toString())) {
            return messageBuffer.toString();
        }
        //2.减免信息检验
        if(!FtsUtils.isNull(tdsyysxxVO.getSbCxsYsjmxzxxVOGrid()) && !FtsUtils.isNull(tdsyysxxVO.getSbCxsYsjmxzxxVOGrid().getSbCxsYsjmxzxxVOList())) {
            final List<SBCxsYsjmxzxxVO> sbCxsYsjmxzxxVOList = tdsyysxxVO.getSbCxsYsjmxzxxVOGrid().getSbCxsYsjmxzxxVOList();
            for(SBCxsYsjmxzxxVO sbCxsYsjmxzxxVO :sbCxsYsjmxzxxVOList){
                //2.1减免性质代码，不可为空，码值检验
                final String ssjmxzDm = sbCxsYsjmxzxxVO.getSsjmxzDm();
                if (FtsUtils.isNull(ssjmxzDm)) {
                    messageBuffer.append("减免性质代码为空，请核实后处理。");
                } else {
                    final Map<String, Object> mcByDm = FtsUtils.getIndexData("dm_gy_ssjmxz", ssjmxzDm);
                    if (FtsUtils.isNull(mcByDm)) {
                        messageBuffer.append("减免性质代码码值不正确，请核实后处理。");
                    }
                }
                //2.2减免税土地面积，不可为空，并且大于0，精度NUMBER(14,2)
                final Double jmmj = sbCxsYsjmxzxxVO.getJmmj();
                if (FtsUtils.isNull(jmmj)) {
                    messageBuffer.append("减免税土地面积为空，请核实后处理。");
                } else {
                    final boolean flagJmmj = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(jmmj)).toString(), 12, 2);
                    if (!flagJmmj) {
                        messageBuffer.append("减免税土地面积精度不正确，请核实后处理。");
                    }
                    if (jmmj <= 0) {
                        messageBuffer.append("减免税土地面积必须大于0，请核实后处理。");
                    }
                    if (jmmj > ystdmj) {
                        messageBuffer.append("减免税土地面积不能大于应税的占用土地面积，请核实后处理。");
                    }
                }
                //2.3月减免税金额，不可为空，并且大于0，精度NUMBER(18,2)
                final Double yjmsje1 = sbCxsYsjmxzxxVO.getYjmsje1();
                if (FtsUtils.isNull(yjmsje1)) {
                    messageBuffer.append("月减免税金额为空，请核实后处理。");
                } else {
                    final boolean flagYjmsje1 = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(yjmsje1)).toString(), 12, 2);
                    if (!flagYjmsje1) {
                        messageBuffer.append("月减免税金额精度不正确，请核实后处理。");
                    }
                    if (yjmsje1 <= 0) {
                        messageBuffer.append("月减免税金额必须大于0，请核实后处理。");
                    }
                }
                //1.4.有效期起不能为空
                final String jmYxqq = sbCxsYsjmxzxxVO.getYxqq();
                if (FtsUtils.isNull(jmYxqq)) {
                    messageBuffer.append("减免有效期起为空，请核实后处理。");
                } else {
                    final boolean flagJmYxqq = FtsUtils.checkDate(jmYxqq, "yyyy-MM-dd");
                    if (!flagJmYxqq) {
                        messageBuffer.append("减免有效期起时间日期格式不正确，请核实后处理。");
                    }
                }
                //1.5.有效期起不能为空
                final String jmYxqz = sbCxsYsjmxzxxVO.getYxqz();
                if (FtsUtils.isNull(jmYxqz)) {
                    messageBuffer.append("减免有效期止为空，请核实后处理。");
                } else {
                    final boolean flagJmYxqz = FtsUtils.checkDate(jmYxqz, "yyyy-MM-dd");
                    if (!flagJmYxqz) {
                        messageBuffer.append("减免有效期止时间日期格式不正确，请核实后处理。");
                    }
                }
                //2.6.检验有效期起和止
                if (FtsUtils.isNull(messageBuffer.toString())) {
                    final Date dateQ = DateUtil.toDate("yyyy-MM-dd",jmYxqq);
                    final Date dateZ = DateUtil.toDate("yyyy-MM-dd",jmYxqz);
                    if(dateQ.after(dateZ)){
                        messageBuffer.append("减免有效期起不能大于有效期止，请核实后处理。");
                    }
                    //减免有效期起止小于应税有效期起止
                    final Date yxqqD = DateUtil.toDate("yyyy-MM-dd",yxqq);
                    final Date yxqzD = DateUtil.toDate("yyyy-MM-dd",yxqz);
                    if(dateQ.before(yxqqD) || dateZ.after(yxqzD)){
                        messageBuffer.append("减免有效期起应处于应税有效期(").append(yxqq).append("-").append(yxqz).append(")内，请核实后处理。");
                    }
                }
                //2.4此时如果检验异常，返回
                if (!FtsUtils.isNull(messageBuffer.toString())) {
                    break;
                }
            }
            //2.4此时如果检验异常，返回
            if (!FtsUtils.isNull(messageBuffer.toString())) {
                return messageBuffer.toString();
            }
        }
        //3.和土地信息联动检验
        if(!FtsUtils.isNull(cztdsysTdxxVO)){
            final String zgswskfjDm = cztdsysTdxxVO.getZgswskfjDm();
            //3.1增收品目联动检验，均已检验不可为空
            final List<String> zspmByTddj = FtsUtils.getZspmByTddj(tddjDm, zgswskfjDm);
            if (FtsUtils.isNull(zspmByTddj)) {
                messageBuffer.append("征收品目代码：").append(zspmDm).append(",土地等级代码：").append(tddjDm).append("不匹配，请核实后处理。");
            }else if(zspmByTddj.size() >1){
                messageBuffer.append("根据土地等级和税务机关查询征收品目不唯一，请联系主管机关修改配置信息。");
            }else if(!zspmDm.equals(zspmByTddj.get(0))){
                messageBuffer.append("征收品目代码：").append(zspmDm).append(",土地等级代码：").append(tddjDm).append("不匹配，请核实后处理。");
            }
            //3.2占用土地面积联动检验，均已检验不可为空
            final double zytdmj1 = cztdsysTdxxVO.getZytdmj1();
            if(ystdmj > zytdmj1){
                messageBuffer.append("应税中的占用土地面积不能大于土地采集中的土地占用面积，请核实后处理。");
            }
            //3.3此时如果检验异常，返回
            if (!FtsUtils.isNull(messageBuffer.toString())) {
                return messageBuffer.toString();
            }
        }
        return "";
    }




















//------------------------------房产税数据检验---------------------------------------------------------------
    /**
     * 检验房屋信息
     * @param fcsFwxxVO 入参
     * @param clbz 1-仅检验房屋信息，2-检验房屋信息+应税信息
     * @param drbz Y-导入，N-非导入
     * @return 返回值
     */
    public static String checkFwxx(FcsFwxxVO fcsFwxxVO, String clbz, String drbz) {
        final StringBuffer messageBuffer = new StringBuffer();

        //1.基本字段检验，必录，码值，长度，格式
        //1.1.session字段，仅检查是否为空
        final String djxh = fcsFwxxVO.getDjxh();
        if (FtsUtils.isNull(djxh)) {
            messageBuffer.append("登记序号为空，请核实后处理。");
        }
        //1.2.
        //房源编号，可为空，检验长度VARCHAR2(26)
        final String fybh = fcsFwxxVO.getFybh();
        final boolean flagfybh = FtsUtils.checkMaxLength(fybh, 26);
        if (!flagfybh) {
            messageBuffer.append("房源编号字段超长，请核实后处理。");
        }
        //1.3.房产名称，可为空，检验长度VARCHAR2(300)
        final String fcmc = fcsFwxxVO.getFcmc();
        final boolean flagfcmc = FtsUtils.checkMaxLength(fcmc, 300);
        if (!flagfcmc) {
            messageBuffer.append("房产名称字段超长，请核实后处理。");
        }
        //1.4.房产用途，不可为空，检验码值(不需检验长度)
        final String fcytDm = fcsFwxxVO.getFcytDm();
        if (FtsUtils.isNull(fcytDm)) {
            messageBuffer.append("房产用途为空，请核实后处理。");
        } else {
            final String mcByDm = FtsUtils.getMcByDm("dm_sb_fcyt", "fcytmc", fcytDm);
            if (FtsUtils.isNull(mcByDm)) {
                messageBuffer.append("房产用途码值不正确，请核实后处理。");
            }
        }

        //1.5.房产取得时间，不可为空，日期类型，不能大于当前日期
        final String csqdsj = fcsFwxxVO.getCsqdsj();
        if (FtsUtils.isNull(csqdsj)) {
            messageBuffer.append("房产取得时间为空，请核实后处理。");
        } else {
            final boolean flagCsqdsj = FtsUtils.checkDate(csqdsj, "yyyy-MM-dd");
            if (!flagCsqdsj) {
                messageBuffer.append("房产取得时间日期格式不正确，请核实后处理。");
            } else {
                final Date now = new Date();
                if (now.before(DateUtil.toDate("yyyy-MM-dd", csqdsj))) {
                    messageBuffer.append("房产取得时间不能大于当前日期，请核实后处理。");
                }
            }
        }
        //1.6.建筑面积，不可为空，大于0，精度NUMBER(14,2)
        final Double jzmj = fcsFwxxVO.getJzmj();
        if (FtsUtils.isNull(jzmj)) {
            messageBuffer.append("建筑面积为空，请核实后处理。");
        } else {
            final boolean flagjzmj = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(jzmj)).toString(), 12, 2);
            if (!flagjzmj) {
                messageBuffer.append("建筑面积数值精度不正确，请核实后处理。");
            }
            if (jzmj <= 0) {
                messageBuffer.append("建筑面积需大于0，请核实后处理。");
            }
        }
        //1.7.纳税人类型，不可为空，码值检验
        final String nsrlx = fcsFwxxVO.getFcsnsrlx();
        if (FtsUtils.isNull(nsrlx)) {
            //messageBuffer.append("纳税人类型为空，请核实后处理。");
        } else {
            final String nsrlxmc = FtsUtils.getMcByDm("dm_sb_fcsnsrlx", "fcsnsrlxmc", nsrlx);
            if (FtsUtils.isNull(nsrlxmc)) {
                messageBuffer.append("纳税人类型码值不正确，请核实后处理。");
            }
        }

        //1.8.所有权人纳税人识别号，可为空，检验长度VARCHAR2(20)
        final String qsrnsrsbh = fcsFwxxVO.getQsrnsrsbh();
        final boolean flagqsrnsrsbh = FtsUtils.checkMaxLength(qsrnsrsbh, 20);
        if (!flagqsrnsrsbh) {
            messageBuffer.append("所有权人纳税人识别号字段超长，请核实后处理。");
        }
        //1.9.所有权人名称，可为空，检验长度VARCHAR2(300)
        final String qsrnsrmc = fcsFwxxVO.getQsrnsrmc();
        final boolean flagqsrnsrmc = FtsUtils.checkMaxLength(qsrnsrmc, 300);
        if (!flagqsrnsrmc) {
            messageBuffer.append("所有权人名称字段超长，请核实后处理。");
        }

        //1.10.不动产单元代码，可为空，检验长度VARCHAR2(40)
        final String bdcdyh = fcsFwxxVO.getBdcdyh();
        final boolean flagBdcdyh = FtsUtils.checkMaxLength(bdcdyh, 40);
        if (!flagBdcdyh) {
            messageBuffer.append("不动产单元号字段超长，请核实后处理。");
        }
        //1.11.不动产权证号，可为空，检验长度VARCHAR2(120)
        final String fwcqzsh = fcsFwxxVO.getFwcqzsh();
        final boolean flagfwcqzsh = FtsUtils.checkMaxLength(fwcqzsh, 120);
        if (!flagfwcqzsh) {
            messageBuffer.append("不动产权证书号字段超长，请核实后处理。");
        }
        //1.12.行政区划，不可为空，码值检查
        final String xzqhszDm = fcsFwxxVO.getXzqhszDm();
        if (FtsUtils.isNull(xzqhszDm)) {
            messageBuffer.append("房屋坐落地址（行政区划）为空，请核实后处理。");
        } else {
            final String mcByDm = FtsUtils.getMcByDm("dm_gy_xzqh", "xzqhmc", xzqhszDm);
            if (FtsUtils.isNull(mcByDm)) {
                messageBuffer.append("房屋坐落地址（行政区划）码值不正确，请核实后处理。");
            }
        }
        //1.13.街道乡镇，不可为空，码值检查
        final String jdxzDm = fcsFwxxVO.getJdxzDm();
        if (FtsUtils.isNull(jdxzDm)) {
            messageBuffer.append("房屋坐落地址（所处街乡）为空，请核实后处理。");
        } else {
            final String mcByDm = FtsUtils.getMcByDm("dm_gy_jdxz", "jdxzmc", jdxzDm);
            if (FtsUtils.isNull(mcByDm)) {
                messageBuffer.append("房屋坐落地址（所处街乡）码值不正确，请核实后处理。");
            }
        }
        //1.14.土地坐落地址，不可为空，检验长度VARCHAR2(300)
        final String tdzldz = fcsFwxxVO.getFwzldz();
        if (FtsUtils.isNull(tdzldz)) {
            messageBuffer.append("房屋坐落地址为空，请核实后处理。");
        } else {
            final boolean flagTdzldz = FtsUtils.checkMaxLength(tdzldz, 300);
            if (!flagTdzldz) {
                messageBuffer.append("房屋坐落地址字段长度不正确，请核实后处理。");
            }
        }
        //1.15.房屋所属税务所（科、分局），不可为空，码值检查
        final String zgswskfjDm = fcsFwxxVO.getZgswskfjDm();
        if (FtsUtils.isNull(zgswskfjDm)) {
            messageBuffer.append("房产所属主管税务所（科、分局）为空，请核实后处理。");
        } else {
            final String mcByDm = FtsUtils.getMcByDm("dm_gy_swjg", "swjgmc", zgswskfjDm);
            if (FtsUtils.isNull(mcByDm)) {
                messageBuffer.append("房产所属主管税务所（科、分局）码值不正确，请核实后处理。");
            }
        }
        //1.16.此时如果检验异常，返回
        if (!FtsUtils.isNull(messageBuffer.toString())) {
            return messageBuffer.toString();
        }

        //2.基本联动规则检验
        //2.1.有效期起不能为空，且为初始取得时间下月第一天
        final String yxqq = fcsFwxxVO.getYxqq();
        if (FtsUtils.isNull(yxqq)) {
            messageBuffer.append("有效期起为空，请核实后处理。");
        } else {
            final boolean flagYxqq = FtsUtils.checkDate(yxqq, "yyyy-MM-dd");
            if (!flagYxqq) {
                messageBuffer.append("有效期起时间日期格式不正确，请核实后处理。");
            } else {
                /*final Date dateCsqdsj = DateUtil.toDate(csqdsj, "yyyy-MM-dd");
                final Date firstCsqdsj = FtsUtils.getFirstDayOfMonth(dateCsqdsj);
                final Date dateYxqq = DateUtil.toDate(yxqq, "yyyy-MM-dd");
                final Date firstYxqq = FtsUtils.getFirstDayOfMonth(dateYxqq);
                if (dateYxqq.compareTo(firstYxqq) != 0 || firstCsqdsj.compareTo(firstYxqq) > 0 || FtsUtils.jsYfkd(firstCsqdsj, firstYxqq) != 2) {
                    messageBuffer.append("有效期起应为取得时间的下月一号，请核实后处理。");
                }*/
            }
        }
        //2.2此时如果检验异常，返回
        if (!FtsUtils.isNull(messageBuffer.toString())) {
            return messageBuffer.toString();
        }

        //3.检验应税信息
        if ("2".equals(clbz)) {
            if (FtsUtils.isNull(fcsFwxxVO.getFcsCjYsxxVO()) || FtsUtils.isNull(fcsFwxxVO.getFcsCjYsxxVO().getCjmxVOList())) {
                messageBuffer.append("从价应税信息为空，请核实后处理。");
            } else {
                final List<CjmxVO> cjmxVOList = fcsFwxxVO.getFcsCjYsxxVO().getCjmxVOList();
                for (CjmxVO cjmxVO : cjmxVOList) {//先当一条处理
                    final String ysxxCjMsg = checkFcCjYsxx(cjmxVO, drbz ,fcsFwxxVO);
                    if (!FtsUtils.isNull(ysxxCjMsg)){
                        messageBuffer.append(ysxxCjMsg);
                        break;
                    }
                    final Double czwyz = cjmxVO.getCzwyz();
                    final Double czfcmj = cjmxVO.getCzfcmj();
                    //出租面积大于0，必须填从租应税
                    if(czwyz > 0 || czfcmj > 0){
                        if (FtsUtils.isNull(fcsFwxxVO.getFcsCzYsxxVO()) || FtsUtils.isNull(fcsFwxxVO.getFcsCzYsxxVO().getCzmxVOList())) {
                            messageBuffer.append("从价其中：出租房产原值或出租房产面积大于0，需采集从租应税信息，请核实后处理。");
                            break;
                        }
                    }
                    //出租面积等于0，不能填从租应税
                    if(czwyz == 0 || czfcmj == 0){
                        if (!FtsUtils.isNull(fcsFwxxVO.getFcsCzYsxxVO()) && !FtsUtils.isNull(fcsFwxxVO.getFcsCzYsxxVO().getCzmxVOList())) {
                            messageBuffer.append("从价其中：出租房产原值或出租房产面积等于0，不可采集从租应税信息，请核实后处理。");
                            break;
                        }
                    }
                    if (!FtsUtils.isNull(fcsFwxxVO.getFcsCzYsxxVO()) && !FtsUtils.isNull(fcsFwxxVO.getFcsCzYsxxVO().getCzmxVOList())) {
                        final CzmxVO czmxVO = fcsFwxxVO.getFcsCzYsxxVO().getCzmxVOList().get(0);
                        final String ysxxCzMsg = checkFcCzYsxx(czmxVO, drbz ,cjmxVO);
                        if (!FtsUtils.isNull(ysxxCzMsg)){
                            messageBuffer.append(ysxxCzMsg);
                            break;
                        }
                    }
                }
            }
            if (!FtsUtils.isNull(messageBuffer.toString())) {
                return messageBuffer.toString();
            }
        }
        return "";
    }

    /**
     * 房屋从价应税信息检查
     * @param cjmxVO 入参
     * @param drbz Y-导入，N-非导入
     * @param fcsFwxxVO 为空为单独保存，不为空为整体保存
     * @return 返回值
     */
    public static String checkFcCjYsxx(CjmxVO cjmxVO, String drbz, FcsFwxxVO fcsFwxxVO) {
        final StringBuffer messageBuffer = new StringBuffer();
        //1.基本字段检验，必录，码值，长度，格式
        //1.1.房产原值，不可为空，大于0，NUMBER(14,2)
        final Double fcyz = cjmxVO.getFcyz();
        if (FtsUtils.isNull(fcyz)) {
            messageBuffer.append("从价房产原值为空，请核实后处理。");
        } else {
            final boolean flagfcyz = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(fcyz)).toString(), 12, 2);
            if (!flagfcyz) {
                messageBuffer.append("从价房产原值数值精度不正确，请核实后处理。");
            }
            if (fcyz <= 0) {
                messageBuffer.append("从价房产原值需大于0，请核实后处理。");
            }
        }
        //1.2.其中：出租房产原值，不可为空，大于等于0，NUMBER(14,2)
        final Double czwyz = cjmxVO.getCzwyz();
        if (FtsUtils.isNull(czwyz)) {
            messageBuffer.append("从价其中：出租房产原值为空，请核实后处理。");
        } else {
            final boolean flagczwyz = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(czwyz)).toString(), 12, 2);
            if (!flagczwyz) {
                messageBuffer.append("从价其中：出租房产原值数值精度不正确，请核实后处理。");
            }
            if (czwyz < 0) {
                messageBuffer.append("从价其中：出租房产原值小于0，请核实后处理。");
            }
        }
        //1.3.其中：出租房产面积，不可为空，大于等于0，精度NUMBER(14,2)
        final Double czfcmj = cjmxVO.getCzfcmj();
        if (FtsUtils.isNull(czfcmj)) {
            messageBuffer.append("从价其中：出租房产面积为空，请核实后处理。");
        } else {
            final boolean flagczfcmj = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(czfcmj)).toString(), 12, 2);
            if (!flagczfcmj) {
                messageBuffer.append("从价其中：出租房产面积精度不正确，请核实后处理。");
            }
            if (czfcmj < 0) {
                messageBuffer.append("从价其中：出租房产面积小于0，请核实后处理。");
            }
        }
        //1.3.计税比例，不可为空，大于0，精度NUMBER(4,2)
        final Double jsbl = cjmxVO.getJsbl();
        if (FtsUtils.isNull(jsbl)) {
            messageBuffer.append("从价计税比例为空，请核实后处理。");
        } else {
            final boolean flagcjsbl = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(jsbl)).toString(), 2, 2);
            if (!flagcjsbl) {
                messageBuffer.append("从价计税比例精度不正确，请核实后处理。");
            }
            if (jsbl <= 0) {
                messageBuffer.append("从价计税比例需大于0，请核实后处理。");
            }
        }
        //1.4.征收品目，不可为空，码值检验
        final String zspmDm = cjmxVO.getZspmDm();
        if (FtsUtils.isNull(zspmDm)) {
            messageBuffer.append("从价征收品目为空，请核实后处理。");
        } else {
            if(!"101100700".equals(zspmDm)){
                messageBuffer.append("从价征收品目码值不正确，请核实后处理。");
            }
        }
        //1.5.有效期起不能为空
        final String yxqq = cjmxVO.getYxqq();
        if (FtsUtils.isNull(yxqq)) {
            messageBuffer.append("从价有效期起为空，请核实后处理。");
        } else {
            final boolean flagYxqq = FtsUtils.checkDate(yxqq, "yyyy-MM-dd");
            if (!flagYxqq) {
                messageBuffer.append("从价有效期起时间日期格式不正确，请核实后处理。");
            }
        }
        //1.6.有效期止不能为空
        final String yxqz = cjmxVO.getYxqz();
        if (FtsUtils.isNull(yxqz)) {
            messageBuffer.append("从价有效期止为空，请核实后处理。");
        } else {
            final boolean flagYxqz = FtsUtils.checkDate(yxqz, "yyyy-MM-dd");
            if (!flagYxqz) {
                messageBuffer.append("从价有效期止时间日期格式不正确，请核实后处理。");
            }
        }
        //1.6此时如果检验异常，返回
        if (!FtsUtils.isNull(messageBuffer.toString())) {
            return messageBuffer.toString();
        }
        //1.7出租房产原值，不能大于房产原值
        if(czwyz > fcyz){
            messageBuffer.append("从价出租房产原值不能大于房产原值，请核实后处理。");
        }
        //3.3此时如果检验异常，返回
        if (!FtsUtils.isNull(messageBuffer.toString())) {
            return messageBuffer.toString();
        }

        //2.减免信息检验
        if(!FtsUtils.isNull(cjmxVO.getSbCxsYsjmxzxxVOGrid()) && !FtsUtils.isNull(cjmxVO.getSbCxsYsjmxzxxVOGrid().getSbCxsYsjmxzxxVOList())) {
            final List<SBCxsYsjmxzxxVO> sbCxsYsjmxzxxVOList = cjmxVO.getSbCxsYsjmxzxxVOGrid().getSbCxsYsjmxzxxVOList();
            for(SBCxsYsjmxzxxVO sbCxsYsjmxzxxVO :sbCxsYsjmxzxxVOList) {
                //2.1减免性质代码，不可为空，码值检验
                final String ssjmxzDm = sbCxsYsjmxzxxVO.getSsjmxzDm();
                if (FtsUtils.isNull(ssjmxzDm)) {
                    messageBuffer.append("从价减免性质代码为空，请核实后处理。");
                } else {
                    final Map<String, Object> mcByDm = FtsUtils.getIndexData("dm_gy_ssjmxz", ssjmxzDm);
                    if (FtsUtils.isNull(mcByDm)) {
                        messageBuffer.append("从价减免性质代码码值不正确，请核实后处理。");
                    }
                }
                //2.2.减免税房产原值，不可为空，并且大于0，精度NUMBER(14,2)
                final Double jmsfcyz = sbCxsYsjmxzxxVO.getJmsfcyz();
                if (FtsUtils.isNull(jmsfcyz)) {
                    messageBuffer.append("从价减免税房产原值为空，请核实后处理。");
                } else {
                    final boolean flagjmsfcyz = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(jmsfcyz)).toString(), 12, 2);
                    if (!flagjmsfcyz) {
                        messageBuffer.append("从价减免税房产原值精度不正确，请核实后处理。");
                    }
                    if (jmsfcyz <= 0) {
                        messageBuffer.append("从价减免税房产原值必须大于0，请核实后处理。");
                    }
                    //2022-12-17 需求修改，"减免税房产原值不可大于’房产原值’减除’出租房产原值’的余值"
                    final double subtract = FtsUtils.subtract(fcyz, czwyz);
                    if (jmsfcyz > subtract) {
                        messageBuffer.append("减免税房产原值不可大于’房产原值’减除’出租房产原值’的余值");
                    }
                }
                //2.3.月减免税金额，不可为空，并且大于0，精度NUMBER(14,2)
                final Double yjmsje1 = sbCxsYsjmxzxxVO.getYjmsje1();
                if (FtsUtils.isNull(yjmsje1)) {
                    messageBuffer.append("从价月减免税金额为空，请核实后处理。");
                } else {
                    final boolean flagYjmsje1 = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(yjmsje1)).toString(), 12, 2);
                    if (!flagYjmsje1) {
                        messageBuffer.append("从价月减免税金额精度不正确，请核实后处理。");
                    }
                    if (yjmsje1 <= 0) {
                        messageBuffer.append("从价月减免税金额必须大于0，请核实后处理。");
                    }
                }
                //1.4.有效期起不能为空
                final String jmYxqq = sbCxsYsjmxzxxVO.getYxqq();
                if (FtsUtils.isNull(jmYxqq)) {
                    messageBuffer.append("从价减免有效期起为空，请核实后处理。");
                } else {
                    final boolean flagJmYxqq = FtsUtils.checkDate(jmYxqq, "yyyy-MM-dd");
                    if (!flagJmYxqq) {
                        messageBuffer.append("从价减免有效期起时间日期格式不正确，请核实后处理。");
                    }
                }
                //1.5.有效期起不能为空
                final String jmYxqz = sbCxsYsjmxzxxVO.getYxqz();
                if (FtsUtils.isNull(jmYxqz)) {
                    messageBuffer.append("从价减免有效期止为空，请核实后处理。");
                } else {
                    final boolean flagJmYxqz = FtsUtils.checkDate(jmYxqz, "yyyy-MM-dd");
                    if (!flagJmYxqz) {
                        messageBuffer.append("从价减免有效期止时间日期格式不正确，请核实后处理。");
                    }
                }
                //2.6.检验有效期起和止
                if (FtsUtils.isNull(messageBuffer.toString())) {
                    final Date dateQ = DateUtil.toDate("yyyy-MM-dd", jmYxqq);
                    final Date dateZ = DateUtil.toDate("yyyy-MM-dd", jmYxqz);
                    if(dateQ.after(dateZ)){
                        messageBuffer.append("从价减免有效期起不能大于有效期止，请核实后处理。");
                    }
                    //减免有效期起止小于应税有效期起止
                    final Date yxqqD = DateUtil.toDate("yyyy-MM-dd", yxqq);
                    final Date yxqzD = DateUtil.toDate("yyyy-MM-dd", yxqz);
                    if(dateQ.before(yxqqD) || dateZ.after(yxqzD)){
                        messageBuffer.append("从价减免有效期起应处于应税有效期(").append(yxqq).append("-").append(yxqz).append(")内，请核实后处理。");
                    }
                }

                //2.4此时如果检验异常，返回
                if (!FtsUtils.isNull(messageBuffer.toString())) {
                    continue;
                }
                if("Y".equals(drbz)){
                    //4.2导入减免税额检验
                    final String[] arr = new String[]{"0008011707","0008011708","0008011709","0008011710"};
                    if(Arrays.asList(arr).contains(ssjmxzDm)){
                        messageBuffer.append(ssjmxzDm).append("减免性质代码为从租减免适用，请核实后处理。");
                    }
                    final double jsyj2 = FtsUtils.getJsyj2("CJ", jmsfcyz, jsbl, null, null);
                    final double ynse = FtsUtils.getYnse("CJ", 0.012, jsyj2);
                    final double jmse = FtsUtils.getJmseBySsjmxz(sbCxsYsjmxzxxVO.getJmzlxDm(), jsyj2, ynse, 0.012 ,sbCxsYsjmxzxxVO.getJmfd(), sbCxsYsjmxzxxVO.getJmed(), sbCxsYsjmxzxxVO.getJmsl());
                    if(!FtsUtils.checkDoubelEqual(yjmsje1 , jmse ,2)) {
                        messageBuffer.append("减免性质代码-").append(ssjmxzDm).append("：填入的从价月减免税额(").append(yjmsje1).append(")与计算出的从价月减免税额(").append(jmse).append(")不同，请核实后处理。");
                    }
                }
            }
            //2.4此时如果检验异常，返回
            if (!FtsUtils.isNull(messageBuffer.toString())) {
                return messageBuffer.toString();
            }
        }
        //3.和房屋联动检验
        if(!FtsUtils.isNull(fcsFwxxVO)){
            //3.2出租面积联动检验，均已检验不可为空
            final Double jzmj = fcsFwxxVO.getJzmj();
            if(czfcmj > jzmj){
                messageBuffer.append("从价应税中的出租房产面积不能大于房产采集中的建筑面积，请核实后处理。");
            }
            //3.3此时如果检验异常，返回
            if (!FtsUtils.isNull(messageBuffer.toString())) {
                return messageBuffer.toString();
            }
            //导入检验，此时为整体保存数据都有
            if("Y".equals(drbz)){
                //导入计税比例检验
                final String zgswskfjDm = fcsFwxxVO.getZgswskfjDm();
//                final List<Map<String, Object>> fcyzKclList = FtsUtils.getFcyzKclList(zgswskfjDm, "edit");
//                if (!FtsUtils.isNull(fcyzKclList)) {
//                    final StringBuffer jsblStr = new StringBuffer();
//                    final List<Map<String, Object>> jsblFilter = fcyzKclList.stream().filter(f -> {
//                        if(FtsUtils.checkDoubelEqual(jsbl , GYCastUtils.cast2Double(f.get("value")) ,2)) {
//                            return true;
//                        } else {
//                            if(FtsUtils.isNull(jsblStr.toString())){
//                                jsblStr.append(GYCastUtils.cast2Double(f.get("value")));
//                            }else {
//                                jsblStr.append("、").append(GYCastUtils.cast2Double(f.get("value")));
//                            }
//                            return false;
//                        }
//                    }).collect(Collectors.toList());
//                    if(FtsUtils.isNull(jsblFilter)){
//                        messageBuffer.append("输入的计税比例("+GYCastUtils.cast2Double(jsbl)+")与实际查询到的有效计税比例("+jsblStr+")不匹配，请核实后处理。");
//                    }
//                }else {
//                    messageBuffer.append("根据房屋所属税务机关未查询到有效的计税比例，请核实后处理。");
//                }
                if (!FtsUtils.isNull(messageBuffer.toString())) {
                    return messageBuffer.toString();
                }
            }
        }

        return "";
    }

    /**
     * 房屋从租应税信息检查
     * @param czmxVO 入参
     * @param drbz Y-导入，N-非导入
     * @param cjmxVO 为空为单独保存，不为空为整体保存
     * @return 返回值
     */
    public static String checkFcCzYsxx(CzmxVO czmxVO, String drbz ,CjmxVO cjmxVO) {
        final StringBuffer messageBuffer = new StringBuffer();
        final boolean xgflag = !FtsUtils.isNull(czmxVO.getBgyxqq());
        //1.基本字段检验，必录，码值，长度，格式
        //1.1.出租面积，不可为空，大于0，NUMBER(14,2)
        final Double czmj = czmxVO.getCzmj();
        if (FtsUtils.isNull(czmj)) {
            messageBuffer.append("从租出租面积为空，请核实后处理。");
        } else {
            final boolean flagczmj = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(czmj)).toString(), 12, 2);
            if (!flagczmj) {
                messageBuffer.append("从租出租面积精度不正确，请核实后处理。");
            }
            if (!xgflag && czmj <= 0) {
                messageBuffer.append("从租出租面积需大于0，请核实后处理。");
            }else if(xgflag && czmj < 0){
                messageBuffer.append("从租出租面积不能小于0，请核实后处理。");
            }
        }

        //1.2.承租方纳税人识别号，可为空，检验长度VARCHAR2(20)
        final String czfnsrsbh = czmxVO.getCzfnsrsbh();
        final boolean flagczfnsrsbh = FtsUtils.checkMaxLength(czfnsrsbh, 20);
        if (!flagczfnsrsbh) {
            messageBuffer.append("从租承租方纳税人识别号字段超长，请核实后处理。");
        }
        //1.3.承租方名称，可为空，检验长度VARCHAR2(300)
        final String czfnsrmc = czmxVO.getCzfnsrmc();
        final boolean flagczfnsrmc = FtsUtils.checkMaxLength(czfnsrmc, 300);
        if (!flagczfnsrmc) {
            messageBuffer.append("从租承租方名称字段超长，请核实后处理。");
        }
        //1.4.申报租金收入，不可为空，大于0，NUMBER(14,2)
        final Double sbzjsr = czmxVO.getSbzjsr();
        if (FtsUtils.isNull(sbzjsr)) {
            messageBuffer.append("从租申报租金收入为空，请核实后处理。");
        } else {
            final boolean flagsbzjsr = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(sbzjsr)).toString(), 12, 2);
            if (!flagsbzjsr) {
                messageBuffer.append("从租申报租金收入数值精度不正确，请核实后处理。");
            }
            if (!xgflag && sbzjsr <= 0) {
                messageBuffer.append("从租申报租金收入需大于0，请核实后处理。");
            }else if(xgflag && sbzjsr < 0){
                messageBuffer.append("从租申报租金收入不能小于0，请核实后处理。");
            }
        }
        //1.4.征收品目，不可为空，码值检验
        final String zspmDm = czmxVO.getZspmDm();
        if (FtsUtils.isNull(zspmDm)) {
            messageBuffer.append("从租征收品目为空，请核实后处理。");
        } else {
            if(!"101100800".equals(zspmDm)){
                messageBuffer.append("从租征收品目码值不正确，请核实后处理。");
            }
        }
        //1.5.申报期起不能为空
        final String sbzjsszlqq = czmxVO.getSbzjsszlqq();
        if (FtsUtils.isNull(sbzjsszlqq)) {
            messageBuffer.append("从租申报期起不能为空为空，请核实后处理。");
        } else {
            final boolean flagsbzjsszlqq = FtsUtils.checkDate(sbzjsszlqq, "yyyy-MM-dd");
            if (!flagsbzjsszlqq) {
                messageBuffer.append("从租申报期起不能为空日期格式不正确，请核实后处理。");
            }
        }
        //1.6.申报期止不能为空
        final String sbzjsszlqz = czmxVO.getSbzjsszlqz();
        if (FtsUtils.isNull(sbzjsszlqz)) {
            messageBuffer.append("从租申报期止不能为空为空，请核实后处理。");
        } else {
            final boolean flagsbzjsszlqz = FtsUtils.checkDate(sbzjsszlqz, "yyyy-MM-dd");
            if (!flagsbzjsszlqz) {
                messageBuffer.append("从租申报期止不能为空日期格式不正确，请核实后处理。");
            }
        }
        //1.7申报起不能大于止
        if (FtsUtils.isNull(messageBuffer.toString())) {
            final Date sbqD = DateUtil.toDate("yyyy-MM-dd", sbzjsszlqq);
            final Date sbzD = DateUtil.toDate("yyyy-MM-dd", sbzjsszlqz);
            if(sbqD.after(sbzD)){
                messageBuffer.append("从租申报起起应不能大于申报期止，请核实后处理。");
            }
        }

        //1.7此时如果检验异常，返回
        if (!FtsUtils.isNull(messageBuffer.toString())) {
            return messageBuffer.toString();
        }
        //2.减免信息检验
        if(!FtsUtils.isNull(czmxVO.getSbCxsYsjmxzxxVOGrid()) && !FtsUtils.isNull(czmxVO.getSbCxsYsjmxzxxVOGrid().getSbCxsYsjmxzxxVOList())) {
            final List<SBCxsYsjmxzxxVO> sbCxsYsjmxzxxVOList = czmxVO.getSbCxsYsjmxzxxVOGrid().getSbCxsYsjmxzxxVOList();
            for(SBCxsYsjmxzxxVO sbCxsYsjmxzxxVO :sbCxsYsjmxzxxVOList) {
                //2.1减免性质代码，不可为空，码值检验
                final String ssjmxzDm = sbCxsYsjmxzxxVO.getSsjmxzDm();
                if (FtsUtils.isNull(ssjmxzDm)) {
                    messageBuffer.append("从租减免性质代码为空，请核实后处理。");
                } else {
                    final Map<String, Object> mcByDm = FtsUtils.getIndexData("dm_gy_ssjmxz", ssjmxzDm);
                    if (FtsUtils.isNull(mcByDm)) {
                        messageBuffer.append("从租减免性质代码码值不正确，请核实后处理。");
                    }
                }
                //2.2.减免税房产原值，不可为空，并且大于0，精度NUMBER(14,2)
                final Double jmszjsr =sbCxsYsjmxzxxVO.getJmszjsr();

                if (FtsUtils.isNull(jmszjsr)) {
                    messageBuffer.append("从租减免税房产原值为空，请核实后处理。");
                } else {
                    final boolean flagjmszjsr = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(jmszjsr)).toString(), 12, 2);
                    if (!flagjmszjsr) {
                        messageBuffer.append("从租减免税租金收入精度不正确，请核实后处理。");
                    }
                    if (jmszjsr <= 0) {
                        messageBuffer.append("从租减免税租金收入必须大于0，请核实后处理。");
                    }
                    if (jmszjsr > sbzjsr) {
                        messageBuffer.append("从租减免税租金收入不能大于应税的申报租金收入，请核实后处理。");
                    }
                }
                //2.3.月减免税金额，不可为空，并且大于0，精度NUMBER(14,2)
                final Double yjmsje1 = sbCxsYsjmxzxxVO.getYjmsje1();
                if (FtsUtils.isNull(yjmsje1)) {
                    messageBuffer.append("从租月减免税金额为空，请核实后处理。");
                } else {
                    final boolean flagYjmsje1 = FtsUtils.checkDoubleLength(new BigDecimal(FtsUtils.getStringByDouble(yjmsje1)).toString(), 12, 2);
                    if (!flagYjmsje1) {
                        messageBuffer.append("从租月减免税金额精度不正确，请核实后处理。");
                    }
                    if (yjmsje1 <= 0) {
                        messageBuffer.append("从租月减免税金额必须大于0，请核实后处理。");
                    }
                }
                //2.4.有效期起不能为空
                final String jmYxqq = sbCxsYsjmxzxxVO.getYxqq();
                if (FtsUtils.isNull(jmYxqq)) {
                    messageBuffer.append("从租减免有效期起为空，请核实后处理。");
                } else {
                    final boolean flagJmYxqq = FtsUtils.checkDate(jmYxqq, "yyyy-MM-dd");
                    if (!flagJmYxqq) {
                        messageBuffer.append("从租减免有效期起时间日期格式不正确，请核实后处理。");
                    }
                }
                //2.5.有效期起不能为空
                final String jmYxqz = sbCxsYsjmxzxxVO.getYxqz();
                if (FtsUtils.isNull(jmYxqz)) {
                    messageBuffer.append("从租减免有效期止为空，请核实后处理。");
                } else {
                    final boolean flagJmYxqz = FtsUtils.checkDate(jmYxqz, "yyyy-MM-dd");
                    if (!flagJmYxqz) {
                        messageBuffer.append("从租减免有效期止时间日期格式不正确，请核实后处理。");
                    }
                }
                //2.6.检验有效期起和止
                if (FtsUtils.isNull(messageBuffer.toString())) {
                    final Date dateQ = DateUtil.toDate("yyyy-MM-dd", jmYxqq);
                    final Date dateZ = DateUtil.toDate("yyyy-MM-dd", jmYxqz);
                    if(dateQ.after(dateZ)){
                        messageBuffer.append("从租减免有效期起不能大于有效期止，请核实后处理。");
                    }
                    //减免有效期起止小于应税有效期起止
                    final Date yxqqD = DateUtil.toDate("yyyy-MM-dd", sbzjsszlqq);
                    final Date yxqzD = DateUtil.toDate("yyyy-MM-dd", sbzjsszlqz);
                    if(dateQ.before(yxqqD) || dateZ.after(yxqzD)){
                        messageBuffer.append("从租减免有效期起应处于应税有效期内，请核实后处理。");
                    }
                }

                //2.4此时如果检验异常，返回
                if (!FtsUtils.isNull(messageBuffer.toString())) {
                    continue;
                }
                if("Y".equals(drbz)){
                    //4.2导入减免税额检验
                    final double jsyj2 = FtsUtils.getJsyj2("CZ", jmszjsr, 0.00, DateUtil.toDate( "yyyy-MM-dd", sbzjsszlqq), DateUtil.toDate("yyyy-MM-dd", sbzjsszlqz));
                    final double ynse = FtsUtils.getYnse("CZ", 0.12, jsyj2);
                    final double jmse = FtsUtils.getJmseBySsjmxz(sbCxsYsjmxzxxVO.getJmzlxDm(), jsyj2, ynse, 0.12 ,sbCxsYsjmxzxxVO.getJmfd(), sbCxsYsjmxzxxVO.getJmed(), sbCxsYsjmxzxxVO.getJmsl());
                    if(!FtsUtils.checkDoubelEqual(yjmsje1 , jmse ,2)) {
                        messageBuffer.append("减免性质代码-").append(ssjmxzDm).append("：填入的从租月减免税额(").append(yjmsje1).append(")与计算出的从租月减免税额(").append(jmse).append(")不同，请核实后处理。");
                    }
                }
            }
            //2.4此时如果检验异常，返回
            if (!FtsUtils.isNull(messageBuffer.toString())) {
                return messageBuffer.toString();
            }
        }
        //3.和从价联动检验
        if(!FtsUtils.isNull(cjmxVO)){
            final Double czfcmj = cjmxVO.getCzfcmj();
            //3.2出租面积联动检验，均已检验不可为空
            if(czmj > czfcmj){
                messageBuffer.append("从租应税中的出租房产面积不能大于从价中的出租房产面积，请核实后处理。");
            }
            //有效期检验
            final String cjYxqq = cjmxVO.getYxqq();
            final Date cjYxqqD = DateUtil.toDate( "yyyy-MM-dd", cjYxqq);
            final Date sbzjsszlqqD = DateUtil.toDate("yyyy-MM-dd", sbzjsszlqq);
            if(sbzjsszlqqD.before(cjYxqqD)){
                messageBuffer.append("从租申报租金所属租赁期起应大于等于从价应税有效期起，请核实后处理。");
            }
            //3.3此时如果检验异常，返回
            if (!FtsUtils.isNull(messageBuffer.toString())) {
                return messageBuffer.toString();
            }
        }
        return "";
    }
}
