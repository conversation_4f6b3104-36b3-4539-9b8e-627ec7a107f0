package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 机动车销售统一发票清单
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jdcxstyfpqdGridlbVO", propOrder = { "fpDm", "fphm", "scqymc", "cpxh1", "clsbdm", "jfhjje" })
@Getter
@Setter
public class JdcxstyfpqdGridlbVO {
    /**
     * 发票代码
     */
    protected String fpDm;

    /**
     * 发票号码
     */
    protected String fphm;

    /**
     * 生产企业名称
     */
    protected String scqymc;

    /**
     * 厂牌型号
     */
    protected String cpxh1;

    /**
     * 车辆识别代码（车架号）||车辆识别代码（车架号）
     */
    protected String clsbdm;

    /**
     * 价费合计金额
     */
    protected BigDecimal jfhjje;
}