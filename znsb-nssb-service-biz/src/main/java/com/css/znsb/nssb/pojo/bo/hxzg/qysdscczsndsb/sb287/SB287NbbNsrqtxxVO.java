
package com.css.znsb.nssb.pojo.bo.hxzg.qysdscczsndsb.sb287;

import com.css.znsb.nssb.pojo.bo.hxzg.gy.kxjsfzhq.DoublePrecision2;
import com.css.znsb.nssb.pojo.bo.hxzg.gy.kxjsfzhq.DoublePrecision8;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;

/**
 * 企业所得税查账征收年度申报2017版纳税人其他申报信息
 * 
 * <p>SB287NbbNsrqtxxVO complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SB287NbbNsrqtxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="zfjglb" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="kdsAndkxqFlag" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="jdftfzjgFlag" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="jdyjbl" type="{http://www.chinatax.gov.cn/dataspec/}jdyjbl" minOccurs="0"/>
 *         &lt;element name="sqyjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="kyyjye" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yyyjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="cyrs" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="zcze" type="{http://www.chinatax.gov.cn/dataspec/}zcze" minOccurs="0"/>
 *         &lt;element name="sshyDm" type="{http://www.chinatax.gov.cn/dataspec/}sshyDm" minOccurs="0"/>
 *         &lt;element name="sfkstlbz" type="{http://www.chinatax.gov.cn/dataspec/}sfzcdbz" minOccurs="0"/>
 *         &lt;element name="kdqsssrfpBz" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="zjgFtbl" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="zjgCzjzFtbl" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="fzjgFtbl" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="yjddfjmje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgglwdjase" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgftse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zjgczjzftse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="fzjgftse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="scjydlbmse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zgjyjfzcndkce" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="ljjzyhndkce" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="csgjxzhjz" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="yqsbBz" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="yqsbMes" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="zyjbl" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="xzKdqbz" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="tdywbz" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="qtndkce" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="nsrdjrq" type="{http://www.chinatax.gov.cn/dataspec/}djrq" minOccurs="0"/>
 *         &lt;element name="nsrzxrq" type="{http://www.chinatax.gov.cn/dataspec/}zxrq" minOccurs="0"/>
 *         &lt;element name="dlscjybmFlag" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="initMbkstzLen" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="jshKjzyhndmbdkseNd1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="tsnsrlxDm" type="{http://www.chinatax.gov.cn/dataspec/}tsnsrlxDm" minOccurs="0"/>
 *         &lt;element name="cpdycljzndkjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jzyhnddkgqtzye" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jzyhnddkdtzrye" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="synXclEjFzjg" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="hznsqy" type="{http://www.chinatax.gov.cn/dataspec/}hznsqy" minOccurs="0"/>
 *         &lt;element name="bnkjzyhndkcdjze" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndkjzyhndkcdjze" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendkjzyhndkcdjze" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndszcxbqnd" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="sdsnkszjgBz" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="yjddfjmfd" type="{http://www.chinatax.gov.cn/dataspec/}yhfd" minOccurs="0"/>
 *         &lt;element name="zjgyjddfjmje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jztzQsndKjzyndkcye" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jztzQendKjzyndkcye" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jztzQyndKjzyndkcye" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bxqyLjjzyhndkce" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="snxsgdzchwxzcycxyh" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="zjgyjmdffxbfdje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jdjnfzjgFlag" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="sfxsdfjm" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
*          &lt;element name="zsyshqyFzjgSbseHj" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zsyshqybz" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SB287NbbNsrqtxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "zfjglb",
    "kdsAndkxqFlag",
    "jdftfzjgFlag",
    "jdyjbl",
    "sqyjje",
    "kyyjye",
    "yyyjje",
    "cyrs",
    "zcze",
    "sshyDm",
    "sfkstlbz",
    "kdqsssrfpBz",
    "zjgFtbl",
    "zjgCzjzFtbl",
    "fzjgFtbl",
    "yjddfjmje",
    "zjgglwdjase",
    "zjgftse",
    "zjgczjzftse",
    "fzjgftse",
    "scjydlbmse",
    "zgjyjfzcndkce",
    "ljjzyhndkce",
    "csgjxzhjz",
    "yqsbBz",
    "yqsbMes",
    "zyjbl",
    "xzKdqbz",
    "tdywbz",
    "qtndkce",
    "nsrdjrq",
    "nsrzxrq",
    "dlscjybmFlag",
    "initMbkstzLen",
    "jshKjzyhndmbdkseNd1",
    "tsnsrlxDm",
    "cpdycljzndkjje",
    "jzyhnddkgqtzye",
    "jzyhnddkdtzrye",
    "synXclEjFzjg",
    "hznsqy",
    "bnkjzyhndkcdjze",
    "qyndkjzyhndkcdjze",
    "qendkjzyhndkcdjze",
    "qwndszcxbqnd",
    "sdsnkszjgBz",
    "yjddfjmfd",
    "zjgyjddfjmje",
    "jztzQsndKjzyndkcye",
    "jztzQendKjzyndkcye",
    "jztzQyndKjzyndkcye",
    "bxqyLjjzyhndkce",
    "snxsgdzchwxzcycxyh",
    "zjgyjmdffxbfdje",
    "jdjnfzjgFlag",
    "sfxsdfjm",
    "zsyshqyFzjgSbseHj",
    "zsyshqybz"
})
public class SB287NbbNsrqtxxVO
    implements Serializable
{

    private final static long serialVersionUID = 3736360525945171600L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfjglb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kdsAndkxqFlag;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jdftfzjgFlag;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jdyjbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double sqyjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double kyyjye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double yyyjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String cyrs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double zcze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sshyDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfkstlbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kdqsssrfpBz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zjgFtbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zjgCzjzFtbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String fzjgFtbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double yjddfjmje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double zjgglwdjase;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double zjgftse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double zjgczjzftse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double fzjgftse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double scjydlbmse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double zgjyjfzcndkce;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double ljjzyhndkce;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String csgjxzhjz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqsbBz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yqsbMes;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zyjbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xzKdqbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tdywbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double qtndkce;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nsrdjrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nsrzxrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dlscjybmFlag;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String initMbkstzLen;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double jshKjzyhndmbdkseNd1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tsnsrlxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double cpdycljzndkjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double jzyhnddkgqtzye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double jzyhnddkdtzrye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String synXclEjFzjg;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String hznsqy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double bnkjzyhndkcdjze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double qyndkjzyhndkcdjze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double qendkjzyhndkcdjze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qwndszcxbqnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sdsnkszjgBz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision8.class)
    protected Double yjddfjmfd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double zjgyjddfjmje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double jztzQsndKjzyndkcye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double jztzQendKjzyndkcye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double jztzQyndKjzyndkcye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double bxqyLjjzyhndkce;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String snxsgdzchwxzcycxyh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double zjgyjmdffxbfdje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jdjnfzjgFlag;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfxsdfjm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    @XmlJavaTypeAdapter(DoublePrecision2.class)
    protected Double zsyshqyFzjgSbseHj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsyshqybz;

    /**
     * 获取zfjglb属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfjglb() {
        return zfjglb;
    }

    /**
     * 设置zfjglb属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfjglb(String value) {
        this.zfjglb = value;
    }

    /**
     * 获取kdsAndkxqFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKdsAndkxqFlag() {
        return kdsAndkxqFlag;
    }

    /**
     * 设置kdsAndkxqFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKdsAndkxqFlag(String value) {
        this.kdsAndkxqFlag = value;
    }

    /**
     * 获取jdftfzjgFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJdftfzjgFlag() {
        return jdftfzjgFlag;
    }

    /**
     * 设置jdftfzjgFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJdftfzjgFlag(String value) {
        this.jdftfzjgFlag = value;
    }

    /**
     * 获取jdyjbl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJdyjbl() {
        return jdyjbl;
    }

    /**
     * 设置jdyjbl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJdyjbl(Double value) {
        this.jdyjbl = value;
    }

    /**
     * 获取sqyjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSqyjje() {
        return sqyjje;
    }

    /**
     * 设置sqyjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSqyjje(Double value) {
        this.sqyjje = value;
    }

    /**
     * 获取kyyjye属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKyyjye() {
        return kyyjye;
    }

    /**
     * 设置kyyjye属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKyyjye(Double value) {
        this.kyyjye = value;
    }

    /**
     * 获取yyyjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYyyjje() {
        return yyyjje;
    }

    /**
     * 设置yyyjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYyyjje(Double value) {
        this.yyyjje = value;
    }

    /**
     * 获取cyrs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCyrs() {
        return cyrs;
    }

    /**
     * 设置cyrs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCyrs(String value) {
        this.cyrs = value;
    }

    /**
     * 获取zcze属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZcze() {
        return zcze;
    }

    /**
     * 设置zcze属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZcze(Double value) {
        this.zcze = value;
    }

    /**
     * 获取sshyDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSshyDm() {
        return sshyDm;
    }

    /**
     * 设置sshyDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSshyDm(String value) {
        this.sshyDm = value;
    }

    /**
     * 获取sfkstlbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfkstlbz() {
        return sfkstlbz;
    }

    /**
     * 设置sfkstlbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfkstlbz(String value) {
        this.sfkstlbz = value;
    }

    /**
     * 获取kdqsssrfpBz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKdqsssrfpBz() {
        return kdqsssrfpBz;
    }

    /**
     * 设置kdqsssrfpBz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKdqsssrfpBz(String value) {
        this.kdqsssrfpBz = value;
    }

    /**
     * 获取zjgFtbl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZjgFtbl() {
        return zjgFtbl;
    }

    /**
     * 设置zjgFtbl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZjgFtbl(String value) {
        this.zjgFtbl = value;
    }

    /**
     * 获取zjgCzjzFtbl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZjgCzjzFtbl() {
        return zjgCzjzFtbl;
    }

    /**
     * 设置zjgCzjzFtbl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZjgCzjzFtbl(String value) {
        this.zjgCzjzFtbl = value;
    }

    /**
     * 获取fzjgFtbl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFzjgFtbl() {
        return fzjgFtbl;
    }

    /**
     * 设置fzjgFtbl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFzjgFtbl(String value) {
        this.fzjgFtbl = value;
    }

    /**
     * 获取yjddfjmje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjddfjmje() {
        return yjddfjmje;
    }

    /**
     * 设置yjddfjmje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjddfjmje(Double value) {
        this.yjddfjmje = value;
    }

    /**
     * 获取zjgglwdjase属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgglwdjase() {
        return zjgglwdjase;
    }

    /**
     * 设置zjgglwdjase属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgglwdjase(Double value) {
        this.zjgglwdjase = value;
    }

    /**
     * 获取zjgftse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgftse() {
        return zjgftse;
    }

    /**
     * 设置zjgftse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgftse(Double value) {
        this.zjgftse = value;
    }

    /**
     * 获取zjgczjzftse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgczjzftse() {
        return zjgczjzftse;
    }

    /**
     * 设置zjgczjzftse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgczjzftse(Double value) {
        this.zjgczjzftse = value;
    }

    /**
     * 获取fzjgftse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getFzjgftse() {
        return fzjgftse;
    }

    /**
     * 设置fzjgftse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setFzjgftse(Double value) {
        this.fzjgftse = value;
    }

    /**
     * 获取scjydlbmse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getScjydlbmse() {
        return scjydlbmse;
    }

    /**
     * 设置scjydlbmse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setScjydlbmse(Double value) {
        this.scjydlbmse = value;
    }

    /**
     * 获取zgjyjfzcndkce属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZgjyjfzcndkce() {
        return zgjyjfzcndkce;
    }

    /**
     * 设置zgjyjfzcndkce属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZgjyjfzcndkce(Double value) {
        this.zgjyjfzcndkce = value;
    }

    /**
     * 获取ljjzyhndkce属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getLjjzyhndkce() {
        return ljjzyhndkce;
    }

    /**
     * 设置ljjzyhndkce属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setLjjzyhndkce(Double value) {
        this.ljjzyhndkce = value;
    }

    /**
     * 获取csgjxzhjz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCsgjxzhjz() {
        return csgjxzhjz;
    }

    /**
     * 设置csgjxzhjz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCsgjxzhjz(String value) {
        this.csgjxzhjz = value;
    }

    /**
     * 获取yqsbBz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYqsbBz() {
        return yqsbBz;
    }

    /**
     * 设置yqsbBz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYqsbBz(String value) {
        this.yqsbBz = value;
    }

    /**
     * 获取yqsbMes属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYqsbMes() {
        return yqsbMes;
    }

    /**
     * 设置yqsbMes属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYqsbMes(String value) {
        this.yqsbMes = value;
    }

    /**
     * 获取zyjbl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZyjbl() {
        return zyjbl;
    }

    /**
     * 设置zyjbl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZyjbl(String value) {
        this.zyjbl = value;
    }

    /**
     * 获取xzKdqbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzKdqbz() {
        return xzKdqbz;
    }

    /**
     * 设置xzKdqbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzKdqbz(String value) {
        this.xzKdqbz = value;
    }

    /**
     * 获取tdywbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdywbz() {
        return tdywbz;
    }

    /**
     * 设置tdywbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdywbz(String value) {
        this.tdywbz = value;
    }

    /**
     * 获取qtndkce属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQtndkce() {
        return qtndkce;
    }

    /**
     * 设置qtndkce属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQtndkce(Double value) {
        this.qtndkce = value;
    }

    /**
     * 获取nsrdjrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrdjrq() {
        return nsrdjrq;
    }

    /**
     * 设置nsrdjrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrdjrq(String value) {
        this.nsrdjrq = value;
    }

    /**
     * 获取nsrzxrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrzxrq() {
        return nsrzxrq;
    }

    /**
     * 设置nsrzxrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrzxrq(String value) {
        this.nsrzxrq = value;
    }

    /**
     * 获取dlscjybmFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDlscjybmFlag() {
        return dlscjybmFlag;
    }

    /**
     * 设置dlscjybmFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDlscjybmFlag(String value) {
        this.dlscjybmFlag = value;
    }

    /**
     * 获取initMbkstzLen属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInitMbkstzLen() {
        return initMbkstzLen;
    }

    /**
     * 设置initMbkstzLen属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInitMbkstzLen(String value) {
        this.initMbkstzLen = value;
    }

    /**
     * 获取jshKjzyhndmbdkseNd1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJshKjzyhndmbdkseNd1() {
        return jshKjzyhndmbdkseNd1;
    }

    /**
     * 设置jshKjzyhndmbdkseNd1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJshKjzyhndmbdkseNd1(Double value) {
        this.jshKjzyhndmbdkseNd1 = value;
    }

    /**
     * 获取tsnsrlxDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTsnsrlxDm() {
        return tsnsrlxDm;
    }

    /**
     * 设置tsnsrlxDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTsnsrlxDm(String value) {
        this.tsnsrlxDm = value;
    }

    /**
     * 获取cpdycljzndkjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getCpdycljzndkjje() {
        return cpdycljzndkjje;
    }

    /**
     * 设置cpdycljzndkjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setCpdycljzndkjje(Double value) {
        this.cpdycljzndkjje = value;
    }

    /**
     * 获取jzyhnddkgqtzye属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJzyhnddkgqtzye() {
        return jzyhnddkgqtzye;
    }

    /**
     * 设置jzyhnddkgqtzye属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJzyhnddkgqtzye(Double value) {
        this.jzyhnddkgqtzye = value;
    }

    /**
     * 获取jzyhnddkdtzrye属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJzyhnddkdtzrye() {
        return jzyhnddkdtzrye;
    }

    /**
     * 设置jzyhnddkdtzrye属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJzyhnddkdtzrye(Double value) {
        this.jzyhnddkdtzrye = value;
    }

    /**
     * 获取synXclEjFzjg属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSynXclEjFzjg() {
        return synXclEjFzjg;
    }

    /**
     * 设置synXclEjFzjg属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSynXclEjFzjg(String value) {
        this.synXclEjFzjg = value;
    }

    /**
     * 获取hznsqy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHznsqy() {
        return hznsqy;
    }

    /**
     * 设置hznsqy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHznsqy(String value) {
        this.hznsqy = value;
    }

    /**
     * 获取bnkjzyhndkcdjze属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnkjzyhndkcdjze() {
        return bnkjzyhndkcdjze;
    }

    /**
     * 设置bnkjzyhndkcdjze属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnkjzyhndkcdjze(Double value) {
        this.bnkjzyhndkcdjze = value;
    }

    /**
     * 获取qyndkjzyhndkcdjze属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndkjzyhndkcdjze() {
        return qyndkjzyhndkcdjze;
    }

    /**
     * 设置qyndkjzyhndkcdjze属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndkjzyhndkcdjze(Double value) {
        this.qyndkjzyhndkcdjze = value;
    }

    /**
     * 获取qendkjzyhndkcdjze属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendkjzyhndkcdjze() {
        return qendkjzyhndkcdjze;
    }

    /**
     * 设置qendkjzyhndkcdjze属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendkjzyhndkcdjze(Double value) {
        this.qendkjzyhndkcdjze = value;
    }

    /**
     * 获取qwndszcxbqnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQwndszcxbqnd() {
        return qwndszcxbqnd;
    }

    /**
     * 设置qwndszcxbqnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQwndszcxbqnd(String value) {
        this.qwndszcxbqnd = value;
    }

    /**
     * 获取sdsnkszjgBz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSdsnkszjgBz() {
        return sdsnkszjgBz;
    }

    /**
     * 设置sdsnkszjgBz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSdsnkszjgBz(String value) {
        this.sdsnkszjgBz = value;
    }

    /**
     * 获取yjddfjmfd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjddfjmfd() {
        return yjddfjmfd;
    }

    /**
     * 设置yjddfjmfd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjddfjmfd(Double value) {
        this.yjddfjmfd = value;
    }

    /**
     * 获取zjgyjddfjmje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgyjddfjmje() {
        return zjgyjddfjmje;
    }

    /**
     * 设置zjgyjddfjmje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgyjddfjmje(Double value) {
        this.zjgyjddfjmje = value;
    }

    /**
     * 获取jztzQsndKjzyndkcye属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJztzQsndKjzyndkcye() {
        return jztzQsndKjzyndkcye;
    }

    /**
     * 设置jztzQsndKjzyndkcye属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJztzQsndKjzyndkcye(Double value) {
        this.jztzQsndKjzyndkcye = value;
    }

    /**
     * 获取jztzQendKjzyndkcye属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJztzQendKjzyndkcye() {
        return jztzQendKjzyndkcye;
    }

    /**
     * 设置jztzQendKjzyndkcye属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJztzQendKjzyndkcye(Double value) {
        this.jztzQendKjzyndkcye = value;
    }

    /**
     * 获取jztzQyndKjzyndkcye属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJztzQyndKjzyndkcye() {
        return jztzQyndKjzyndkcye;
    }

    /**
     * 设置jztzQyndKjzyndkcye属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJztzQyndKjzyndkcye(Double value) {
        this.jztzQyndKjzyndkcye = value;
    }

    /**
     * 获取bxqyLjjzyhndkce属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBxqyLjjzyhndkce() {
        return bxqyLjjzyhndkce;
    }

    /**
     * 设置bxqyLjjzyhndkce属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBxqyLjjzyhndkce(Double value) {
        this.bxqyLjjzyhndkce = value;
    }

    /**
     * 获取snxsgdzchwxzcycxyh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSnxsgdzchwxzcycxyh() {
        return snxsgdzchwxzcycxyh;
    }

    /**
     * 设置snxsgdzchwxzcycxyh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSnxsgdzchwxzcycxyh(String value) {
        this.snxsgdzchwxzcycxyh = value;
    }

    /**
     * 获取zjgyjmdffxbfdje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgyjmdffxbfdje() {
        return zjgyjmdffxbfdje;
    }

    /**
     * 设置zjgyjmdffxbfdje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgyjmdffxbfdje(Double value) {
        this.zjgyjmdffxbfdje = value;
    }

    /**
     * 获取jdjnfzjgFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJdjnfzjgFlag() {
        return jdjnfzjgFlag;
    }

    /**
     * 设置jdjnfzjgFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJdjnfzjgFlag(String value) {
        this.jdjnfzjgFlag = value;
    }

    /**
     * 获取sfxsdfjm属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSfxsdfjm() {
        return sfxsdfjm;
    }

    /**
     * 设置sfxsdfjm属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSfxsdfjm(String value) {
        this.sfxsdfjm = value;
    }

    public Double getZsyshqyFzjgSbseHj() {
        return zsyshqyFzjgSbseHj;
    }

    public void setZsyshqyFzjgSbseHj(Double zsyshqyFzjgSbseHj) {
        this.zsyshqyFzjgSbseHj = zsyshqyFzjgSbseHj;
    }

    public String getZsyshqybz() {
        return zsyshqybz;
    }

    public void setZsyshqybz(String zsyshqybz) {
        this.zsyshqybz = zsyshqybz;
    }

}
