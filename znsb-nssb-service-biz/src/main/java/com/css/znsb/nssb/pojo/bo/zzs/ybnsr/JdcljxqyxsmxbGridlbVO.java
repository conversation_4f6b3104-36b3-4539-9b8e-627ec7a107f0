package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 机动车辆经销企业销售明细表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jdcljxqyxsmxbGridlbVO", propOrder = { "jsjg", "clsbdm", "cpxh1", "xsdxmc", "jhqdNsrsbh", "jhqdQymc", "ewbhxh" })
@Getter
@Setter
public class JdcljxqyxsmxbGridlbVO {
    /**
     * 计税价格
     */
    protected BigDecimal jsjg;

    /**
     * 车辆识别代码（车架号）||车辆识别代码（车架号）
     */
    protected String clsbdm;

    /**
     * 厂牌型号
     */
    protected String cpxh1;

    /**
     * 销售对象名称
     */
    protected String xsdxmc;

    /**
     * 进货渠道纳税人识别号
     */
    protected String jhqdNsrsbh;

    /**
     * 进货渠道企业名称
     */
    protected String jhqdQymc;

    /**
     * 二维表行序号
     */
    protected Long ewbhxh;
}