package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《年度航空运输企业年度清算表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_ndhkysqyqsbbw", propOrder = { "zzsybnsrsbNdhkysqyqsb" })
@Getter
@Setter
public class ZzsybnsrsbNdhkysqyqsbbw extends TaxDoc {
    /**
     * 《年度航空运输企业年度清算表》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_ndhkysqyqsb", required = true)
    @JSONField(name = "zzsybnsrsb_ndhkysqyqsb")
    protected ZzsybnsrsbNdhkysqyqsb zzsybnsrsbNdhkysqyqsb;
}