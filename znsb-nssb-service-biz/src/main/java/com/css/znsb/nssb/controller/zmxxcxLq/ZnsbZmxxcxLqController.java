package com.css.znsb.nssb.controller.zmxxcxLq;

import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.file.utils.ZnsbFileUtils;
import com.css.znsb.nssb.mapper.sswszm.ZnsbNssbWszmkjjlMapper;
import com.css.znsb.nssb.pojo.domain.sswszm.ZnsbNssbWszmkjjlDO;
import com.css.znsb.nssb.pojo.vo.zmxxcxLq.ZnsbZmxxcxReqVO;
import com.css.znsb.nssb.pojo.vo.zmxxcxLq.ZnsbZmxxcxVO;
import com.css.znsb.nssb.service.zmxxcxLq.ZmxxcxLqService;
import com.google.gson.Gson;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Tag(name = "证明信息查询")
@RestController
@RequestMapping("/zmxxcxLq")
@Validated
@Slf4j
public class ZnsbZmxxcxLqController {

    @Resource
    private ZmxxcxLqService zmxxcxLqService;

    @Resource
    private ZnsbNssbWszmkjjlMapper wszmkjjlMapper;

    @Operation(summary = "初始化")
    @PostMapping("/v1/initData")
    public CommonResult<List<ZnsbZmxxcxVO>> initData(@RequestBody ZnsbZmxxcxReqVO reqVO) {
        List<ZnsbNssbWszmkjjlDO> pageResult = wszmkjjlMapper.cxWszmkjjl(reqVO);
        List<ZnsbZmxxcxVO> zmxxcxVOList = new ArrayList<>();
        pageResult.forEach(item -> {
            ZnsbZmxxcxVO zmxxcxVO = new ZnsbZmxxcxVO();
            zmxxcxVO.setUuid(item.getUuid());
            zmxxcxVO.setWjmc(item.getWjmc());
            zmxxcxVO.setKjrq(DateUtils.dateToString(item.getKjrq(), 3));
            zmxxcxVO.setKjje(item.getSjje());
            zmxxcxVO.setWszmbh(item.getWszmbh());
            if (GyUtils.isNotNull(item.getZsxmdm())){
                String[] zsxmDm = item.getZsxmdm().split(",");
                StringBuilder zsxmMcBuilder = new StringBuilder();
                for (int i = 0; i < zsxmDm.length; i++) {
                    zsxmMcBuilder.append(CacheUtils.dm2mc("dm_gy_zsxm", zsxmDm[i]));
                    // 只有当不是最后一个元素时才添加逗号
                    if (i < zsxmDm.length - 1) {
                        zsxmMcBuilder.append(",");
                    }
                }
                zmxxcxVO.setZsxmMc(zsxmMcBuilder.toString());
            }
            zmxxcxVO.setKjswjgMc(CacheUtils.dm2mc("dm_gy_swjg", item.getSwjgdm()));
            if ("1".equals(item.getKjfsDm())){
                zmxxcxVO.setKjlxMc("明细");
            }else{
                zmxxcxVO.setKjlxMc("汇总");
            }
            zmxxcxVO.setWjlj(item.getWjlj());
            zmxxcxVOList.add(zmxxcxVO);
        });
        return CommonResult.success(zmxxcxVOList);
    }


    /**
     * @param servletResponse
     * @param reqMap
     * @name 下载税收完税证明
     * @description 相关说明
     * @time 创建时间:2024年06月27日下午04:23:52
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @PostMapping("/v1/downLoadWszm")
    @Operation(summary = "下载税收完税证明")
    public void downLoadWszm(HttpServletResponse servletResponse,
                             @RequestBody Map<String, Object> reqMap) {
        log.info("开具完税证明入参" + new Gson().toJson(reqMap));
        try {
            byte[] content;
            final String wjmc = (String) reqMap.get("wjmc");
            final String wjlj = (String) reqMap.get("wjlj");
            if (GyUtils.isNull(wjlj)){
                content = zmxxcxLqService.downLoadWszmByLq(reqMap);
            }else{
                content = ZnsbFileUtils.getContent(wjlj);
                if (GyUtils.isNull(content)){
                    content = zmxxcxLqService.downLoadWszmByLq(reqMap);
                }
            }

            if (GyUtils.isNull(content)){
                return;
            }

            // 设置响应头
            servletResponse.setContentType("application/octet-stream");
            servletResponse.setCharacterEncoding("utf-8");
            servletResponse.setHeader("Content-Disposition",
                    "attachment; filename=\"" + URLEncoder.encode(wjmc, "UTF-8") + "\"");
            // 获取输出流
            ServletOutputStream outputStream = servletResponse.getOutputStream();
            // 写入字节数组到输出流
            outputStream.write(content);
            // 刷新并关闭输出流
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            // 处理异常，比如记录日志或返回错误信息给前端
            log.error("文件下载失败：", e);
        }
    }

    /**
     * @param servletResponse
     * @param reqMap
     * @name 下载税收完税证明，支持批量下载
     * @description 支持单个文件下载和多个文件打包成ZIP下载
     * @time 创建时间:2024年01月28日
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @PostMapping("/v1/batchDownLoadWszm")
    @Operation(summary = "下载税收完税证明，支持批量下载")
    public void batchDownLoadWszm(HttpServletResponse servletResponse,
                          @RequestBody Map<String, Object> reqMap) {
        log.info("开具税收完税证明显示方法入参：{}", new Gson().toJson(reqMap));
        try {
            byte[] content = zmxxcxLqService.batchDownLoadWszm(reqMap);
            
            if (GyUtils.isNull(content)) {
                log.warn("文件内容为空");
                return;
            }
            
            // 判断是否为批量下载（ZIP格式）
            Object fileListObj = reqMap.get("fileList");
            boolean isBatchDownload = false;
            String fileName = "完税证明.pdf";
            String contentType = "application/pdf";
            
            if (fileListObj instanceof List) {
                List<?> fileList = (List<?>) fileListObj;
                if (fileList.size() > 1) {
                    isBatchDownload = true;
                    fileName = "完税证明批量下载.zip";
                    contentType = "application/zip";
                }
            }
            
            // 如果是单个文件，尝试从请求参数中获取文件名
            if (!isBatchDownload) {
                String wjmc = (String) reqMap.get("wjmc");
                if (GyUtils.isNotNull(wjmc)) {
                    fileName = wjmc;
                }
            }
            
            // 设置响应头
            servletResponse.setContentType(contentType);
            servletResponse.setCharacterEncoding("utf-8");
            servletResponse.setHeader("Content-Disposition",
                    "attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
            
            // 获取输出流
            ServletOutputStream outputStream = servletResponse.getOutputStream();
            // 写入字节数组到输出流
            outputStream.write(content);
            // 刷新并关闭输出流
            outputStream.flush();
            outputStream.close();
            
            log.info("文件下载成功，文件名：{}", fileName);
        } catch (IOException e) {
            // 处理异常，比如记录日志或返回错误信息给前端
            log.error("文件下载失败：", e);
        }
    }

}