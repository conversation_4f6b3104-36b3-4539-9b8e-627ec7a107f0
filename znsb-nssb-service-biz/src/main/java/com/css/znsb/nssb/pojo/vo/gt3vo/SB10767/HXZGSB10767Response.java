
package com.css.znsb.nssb.pojo.vo.gt3vo.SB10767;


import com.css.znsb.nssb.pojo.bo.hxzg.sb.general.SBYjxxJhVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.yhssycj.YhshdVO;
import com.css.znsb.nssb.pojo.vo.gt3vo.gy.sbgy.YhscjCjxxb;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 印花税采集载入数据返回对象业务报文
 * 
 * <p>HXZGSB10767Response complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="HXZGSB10767Response">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.chinatax.gov.cn/dataspec/}taxDoc">
 *       &lt;sequence>
 *         &lt;element name="cjxxGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="cjxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}YhscjCjxxb"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="hdxxGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="hdxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}YhshdVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="yjxxGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="yjxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBYjxxJhVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HXZGSB10767Response", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "cjxxGrid",
    "hdxxGrid",
    "yjxxGrid"
})
public class HXZGSB10767Response
    extends TaxDoc
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected CjxxGrid cjxxGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected HdxxGrid hdxxGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected YjxxGrid yjxxGrid;

    /**
     * 获取cjxxGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link CjxxGrid }
     *     
     */
    public CjxxGrid getCjxxGrid() {
        return cjxxGrid;
    }

    /**
     * 设置cjxxGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link CjxxGrid }
     *     
     */
    public void setCjxxGrid(CjxxGrid value) {
        this.cjxxGrid = value;
    }

    /**
     * 获取hdxxGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link HdxxGrid }
     *     
     */
    public HdxxGrid getHdxxGrid() {
        return hdxxGrid;
    }

    /**
     * 设置hdxxGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link HdxxGrid }
     *     
     */
    public void setHdxxGrid(HdxxGrid value) {
        this.hdxxGrid = value;
    }

    /**
     * 获取yjxxGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link YjxxGrid }
     *     
     */
    public YjxxGrid getYjxxGrid() {
        return yjxxGrid;
    }

    /**
     * 设置yjxxGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link YjxxGrid }
     *     
     */
    public void setYjxxGrid(YjxxGrid value) {
        this.yjxxGrid = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="cjxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}YhscjCjxxb"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "cjxxGridlb"
    })
    public static class CjxxGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<YhscjCjxxb> cjxxGridlb;

        /**
         * Gets the value of the cjxxGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the cjxxGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getCjxxGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link YhscjCjxxb }
         * 
         * 
         */
        public List<YhscjCjxxb> getCjxxGridlb() {
            if (cjxxGridlb == null) {
                cjxxGridlb = new ArrayList<YhscjCjxxb>();
            }
            return this.cjxxGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="hdxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}YhshdVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "hdxxGridlb"
    })
    public static class HdxxGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<YhshdVO> hdxxGridlb;

        /**
         * Gets the value of the hdxxGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the hdxxGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getHdxxGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link YhshdVO }
         * 
         * 
         */
        public List<YhshdVO> getHdxxGridlb() {
            if (hdxxGridlb == null) {
                hdxxGridlb = new ArrayList<YhshdVO>();
            }
            return this.hdxxGridlb;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="yjxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBYjxxJhVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "yjxxGridlb"
    })
    public static class YjxxGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<SBYjxxJhVO> yjxxGridlb;

        /**
         * Gets the value of the yjxxGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the yjxxGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getYjxxGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SBYjxxJhVO }
         * 
         * 
         */
        public List<SBYjxxJhVO> getYjxxGridlb() {
            if (yjxxGridlb == null) {
                yjxxGridlb = new ArrayList<SBYjxxJhVO>();
            }
            return this.yjxxGridlb;
        }

    }

}
