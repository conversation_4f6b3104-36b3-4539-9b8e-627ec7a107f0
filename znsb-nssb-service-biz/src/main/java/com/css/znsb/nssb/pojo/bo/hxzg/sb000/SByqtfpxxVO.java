package com.css.znsb.nssb.pojo.bo.hxzg.sb000;


import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 *  金税三期工程核心征管及应用总集成项目 
 * gov.gt3.vo.sbzs.sb.sb000
 * File: SBYjskxxVO.java 创建时间:2014-6-29上午5:07:05
 * Title: SBYjskxxVO
 * Description: 描述（简要描述类的职责、实现方式、使用注意事项等）
 * Copyright: Copyright (c) 2014 中国软件与技术服务股份有限公司
 * Company: 中国软件与技术服务股份有限公司
 * 模块: 申报
 * <AUTHOR>
 * @reviewer 审核人名字 
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "yqtfpGridlb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "sbuuid",
    "pzxh",
    "skgkDm",
    "yskmDm",
    "ysfpblDm",
    "zszmDm",
    "ybtse",
    "xzqhszDm",
    "zsxmDm",
    "zspmDm",
    "fpbl",
    "glpzmxhx"
})
public class SByqtfpxxVO extends TaxBaseVO {

	private static final long serialVersionUID = -1004456959219359806L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String sbuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String pzxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String skgkDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String yskmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String ysfpblDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String zszmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private Double ybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String xzqhszDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String zsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    private String glpzmxhx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
	private Double fpbl;
    /**
     *创建时间:2016-12-22上午11:20:03
     *get方法
     * @return the xzqhszDm
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }
    /**
     * 创建时间:2016-12-22上午11:20:03
     * set方法
     * @param xzqhszDm the xzqhszDm to set
     */
    public void setXzqhszDm(String xzqhszDm) {
        this.xzqhszDm = xzqhszDm;
    }
    /**
     *创建时间:2016-12-22上午11:20:03
     *get方法
     * @return the zsxmDm
     */
    public String getZsxmDm() {
        return zsxmDm;
    }
    /**
     * 创建时间:2016-12-22上午11:20:03
     * set方法
     * @param zsxmDm the zsxmDm to set
     */
    public void setZsxmDm(String zsxmDm) {
        this.zsxmDm = zsxmDm;
    }
    /**
     *创建时间:2016-12-22上午11:20:03
     *get方法
     * @return the zspmDm
     */
    public String getZspmDm() {
        return zspmDm;
    }
    /**
     * 创建时间:2016-12-22上午11:20:03
     * set方法
     * @param zspmDm the zspmDm to set
     */
    public void setZspmDm(String zspmDm) {
        this.zspmDm = zspmDm;
    }
    /**
     *创建时间:2016-12-22上午11:20:03
     *get方法
     * @return the fpbl
     */
    public Double getFpbl() {
        return fpbl;
    }
    /**
     * 创建时间:2016-12-22上午11:20:03
     * set方法
     * @param fpbl the fpbl to set
     */
    public void setFpbl(Double fpbl) {
        this.fpbl = fpbl;
    }
    /**
     *创建时间:2016-12-23上午11:19:45
     *get方法
     * @return the sbuuid
     */
    public String getSbuuid() {
        return sbuuid;
    }
    /**
     * 创建时间:2016-12-23上午11:19:45
     * set方法
     * @param sbuuid the sbuuid to set
     */
    public void setSbuuid(String sbuuid) {
        this.sbuuid = sbuuid;
    }
    /**
     *创建时间:2016-12-23上午11:19:45
     *get方法
     * @return the pzxh
     */
    public String getPzxh() {
        return pzxh;
    }
    /**
     * 创建时间:2016-12-23上午11:19:45
     * set方法
     * @param pzxh the pzxh to set
     */
    public void setPzxh(String pzxh) {
        this.pzxh = pzxh;
    }
    /**
     *创建时间:2016-12-23上午11:19:45
     *get方法
     * @return the skgkDm
     */
    public String getSkgkDm() {
        return skgkDm;
    }
    /**
     * 创建时间:2016-12-23上午11:19:45
     * set方法
     * @param skgkDm the skgkDm to set
     */
    public void setSkgkDm(String skgkDm) {
        this.skgkDm = skgkDm;
    }
    /**
     *创建时间:2016-12-23上午11:19:45
     *get方法
     * @return the yskmDm
     */
    public String getYskmDm() {
        return yskmDm;
    }
    /**
     * 创建时间:2016-12-23上午11:19:45
     * set方法
     * @param yskmDm the yskmDm to set
     */
    public void setYskmDm(String yskmDm) {
        this.yskmDm = yskmDm;
    }
    /**
     *创建时间:2016-12-23上午11:19:45
     *get方法
     * @return the ysfpblDm
     */
    public String getYsfpblDm() {
        return ysfpblDm;
    }
    /**
     * 创建时间:2016-12-23上午11:19:45
     * set方法
     * @param ysfpblDm the ysfpblDm to set
     */
    public void setYsfpblDm(String ysfpblDm) {
        this.ysfpblDm = ysfpblDm;
    }
    /**
     *创建时间:2016-12-23上午11:19:45
     *get方法
     * @return the zszmDm
     */
    public String getZszmDm() {
        return zszmDm;
    }
    /**
     * 创建时间:2016-12-23上午11:19:45
     * set方法
     * @param zszmDm the zszmDm to set
     */
    public void setZszmDm(String zszmDm) {
        this.zszmDm = zszmDm;
    }
    /**
     *创建时间:2016-12-23上午11:19:45
     *get方法
     * @return the ybtse
     */
    public Double getYbtse() {
        return ybtse;
    }
    /**
     * 创建时间:2016-12-23上午11:19:45
     * set方法
     * @param ybtse the ybtse to set
     */
    public void setYbtse(Double ybtse) {
        this.ybtse = ybtse;
    }
    /**
     *创建时间:2016-12-26上午08:32:49
     *get方法
     * @return the glpzmxxh
     */
    public String getGlpzmxhx() {
        return glpzmxhx;
    }
    /**
     * 创建时间:2016-12-26上午08:32:49
     * set方法
     * @param glpzmxxh the glpzmxxh to set
     */
    public void setGlpzmxhx(String glpzmxhx) {
        this.glpzmxhx = glpzmxhx;
    }
}
