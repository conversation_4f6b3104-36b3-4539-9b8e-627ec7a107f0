package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税运输发票抵扣清单业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_zzsysfpdkqdywbw", propOrder = { "zzssyyybnsrZzsysfpdkqd" })
@Getter
@Setter
public class ZzssyyybnsrZzsysfpdkqdywbw extends TaxDoc {
    /**
     * 增值税运输发票抵扣清单
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_zzsysfpdkqd", required = true)
    @JSONField(name = "zzssyyybnsr_zzsysfpdkqd")
    protected ZzssyyybnsrZzsysfpdkqd zzssyyybnsrZzsysfpdkqd;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrZzsysfpdkqd extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrZzsysfpdkqd {}
}