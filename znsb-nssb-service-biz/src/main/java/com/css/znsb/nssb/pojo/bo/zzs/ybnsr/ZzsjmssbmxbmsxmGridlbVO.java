package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税减免税申报明细表免税项目Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsjmssbmxbmsxmGridlbVO", propOrder = { "ewbhxh", "hmc", "swsxDm", "mzzzsxmxse", "bqsjkcje", "kchmsxse", "msxsedyjxse", "mse" })
@Getter
@Setter
public class ZzsjmssbmxbmsxmGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 税务事项代码
     */
    @XmlElement(nillable = true, required = true)
    protected String swsxDm;

    /**
     * 免征增值税项目销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal mzzzsxmxse;

    /**
     * 本期实际扣除金额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqsjkcje;

    /**
     * 扣除后免税销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kchmsxse;

    /**
     * 免税销售额对应的进项税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal msxsedyjxse;

    /**
     * 免税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal mse;
}