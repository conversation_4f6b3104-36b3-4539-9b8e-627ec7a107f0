package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 汇总纳税企业增值税分配表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hznsqyzzsfpbFormVO", propOrder = { "zjgnsrmc", "fddbrxm", "yydz", "khyh", "yhzh", "lxdh", "zjgnsrsbh", "ybtse", "zfjgxsehj", "zfjgxssr", "zjggdfpbl", "zjgybhwjlwxssr", "zjgybhwjlwjzjtxssr", "zjgysfwxssr", "zjgysfwjzjtxssr", "fpbl", "zjgybhwjlwfpbl", "zjgybhwjlwjzjtfpbl", "zjgysfwfpbl", "zjgysfwjzjtfpbl", "zjgfpse", "zjgybhwjlwfpse", "zjgybhwjlwjzjtfpse", "zjgysfwfpse", "zjgysfwjzjtfpse" })
@Getter
@Setter
public class HznsqyzzsfpbFormVO {
    /**
     * 总机构纳税人名称
     */
    @XmlElement(nillable = true, required = true)
    protected String zjgnsrmc;

    /**
     * 法定代表人姓名
     */
    @XmlElement(nillable = true, required = true)
    protected String fddbrxm;

    /**
     * 营业地址
     */
    @XmlElement(nillable = true, required = true)
    protected String yydz;

    /**
     * 开户银行
     */
    protected String khyh;

    /**
     * 银行账户
     */
    protected String yhzh;

    /**
     * 联系电话
     */
    protected String lxdh;

    /**
     * 总机构纳税人识别号
     */
    protected String zjgnsrsbh;

    /**
     * 应补退税额
     */
    protected BigDecimal ybtse;

    /**
     * 总分机构销售额合计
     */
    protected BigDecimal zfjgxsehj;

    /**
     * 总分机构销售收入
     */
    protected BigDecimal zfjgxssr;

    /**
     * 总机构固定分配比例
     */
    protected BigDecimal zjggdfpbl;

    /**
     * 总机构一般货物及劳务销售收入
     */
    protected BigDecimal zjgybhwjlwxssr;

    /**
     * 总机构一般货物及劳务即征即退销售收入
     */
    protected BigDecimal zjgybhwjlwjzjtxssr;

    /**
     * 总机构应税服务销售收入
     */
    protected BigDecimal zjgysfwxssr;

    /**
     * 总机构应税服务即征即退销售收入
     */
    protected BigDecimal zjgysfwjzjtxssr;

    /**
     * 分配比例
     */
    protected BigDecimal fpbl;

    /**
     * 总机构一般货物及劳务分配比例
     */
    protected BigDecimal zjgybhwjlwfpbl;

    /**
     * 总机构一般货物及劳务即征即退分配比例
     */
    protected BigDecimal zjgybhwjlwjzjtfpbl;

    /**
     * 总机构应税服务分配比例
     */
    protected BigDecimal zjgysfwfpbl;

    /**
     * 总机构应税服务即征即退分配比例
     */
    protected BigDecimal zjgysfwjzjtfpbl;

    /**
     * 总机构分配税额
     */
    protected BigDecimal zjgfpse;

    /**
     * 总机构一般货物及劳务分配税额
     */
    protected BigDecimal zjgybhwjlwfpse;

    /**
     * 总机构一般货物及劳务即征即退分配税额
     */
    protected BigDecimal zjgybhwjlwjzjtfpse;

    /**
     * 总机构应税服务分配税额
     */
    protected BigDecimal zjgysfwfpse;

    /**
     * 总机构应税服务即征即退分配税额
     */
    protected BigDecimal zjgysfwjzjtfpse;
}