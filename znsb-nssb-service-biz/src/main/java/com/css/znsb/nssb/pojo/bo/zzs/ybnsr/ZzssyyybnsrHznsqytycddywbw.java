package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《汇总纳税企业通用传递单》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_hznsqytycddywbw", propOrder = { "zzssyyybnsrHznsqytycdd" })
@Getter
@Setter
public class ZzssyyybnsrHznsqytycddywbw extends TaxDoc {
    /**
     * 汇总纳税企业通用传递单
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_hznsqytycdd", required = true)
    @JSONField(name = "zzssyyybnsr_hznsqytycdd")
    protected ZzssyyybnsrHznsqytycdd zzssyyybnsrHznsqytycdd;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrHznsqytycdd extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrHznsqytycdd {}
}