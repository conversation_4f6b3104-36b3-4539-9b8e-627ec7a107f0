package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.zzsxgmnsrsb.plsb.ZzsxgmYsbsjcjService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ZzsxgmYsbjgCreatJob {

    @Resource
    private ZzsxgmYsbsjcjService ysbsjcjService;

    @XxlJob("zzsxgmYsbjgCreatJob")
    public void execute() {
        log.info("开始执行增值税小规模纳税人申报预申报结果创建定时任务");
        ysbsjcjService.handlerYsbsj();
        log.info("增值税小规模纳税人申报预申报结果创建定时任务执行完成");
    }

}
