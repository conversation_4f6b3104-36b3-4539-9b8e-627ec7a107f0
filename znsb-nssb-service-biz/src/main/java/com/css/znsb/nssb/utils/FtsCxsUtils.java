package com.css.znsb.nssb.utils;


import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.nssb.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import javax.xml.transform.stream.StreamSource;
import java.io.StringReader;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 房土税工具类
 */
@Slf4j
@Component
public class FtsCxsUtils {
    private static ConcurrentHashMap<Class<?>, JAXBContext> jaxbContexts = new ConcurrentHashMap<Class<?>, JAXBContext>();
//    /**
//     * 缓存工具类
//     */
//    private static CssDmcsService cssDmcsService;
//
//    /**
//     * 缓存工具类，构造时使用
//     */
//    @Resource
//    private CssDmcsService cssDmcsServiceNew;

//    /**
//     * 实例化构造缓存工具类对象
//     * @throws Exception 异常
//     */
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        cssDmcsService = cssDmcsServiceNew;
//    }

    /**
     * 判断对象是否为空
     * @param obj 对象
     * @return 结果
     */
    public static boolean isNull(Object obj) {
        boolean isNullFlag = true;
        if (obj != null) {
            if (obj instanceof List<?>) {
                isNullFlag = isNull((List<?>) obj);
            } else if (obj instanceof Set<?>) {
                isNullFlag = isNull((Set<?>) obj);
            } else if (obj instanceof Object[]) {
                isNullFlag = isNull((Object[]) obj);
            } else if (obj instanceof Map) {
                isNullFlag = isNull((Map) obj);
            } else if (obj instanceof String) {
                isNullFlag = isNull((String) obj);
            } else {
                isNullFlag = false;
            }
        }
        return isNullFlag;
    }

    /**
     * 判断list是否为空
     * @param list list集合
     * @return 结果
     */
    public static boolean isNull(List<?> list) {
        return list == null || list.size() == 0;
    }

    /**
     * 判断set是否为空
     * @param set set集合
     * @return 结果
     */
    public static boolean isNull(Set<?> set) {
        return set == null || set.size() == 0;
    }

    /**
     * 判断数组是否为空
     * @param objects 数组
     * @return 结果
     */
    public static boolean isNull(Object[] objects) {
        return objects == null || objects.length == 0;
    }

    /**
     * 判断Map是否为空
     * @param map map集合
     * @return 结果
     */
    public static boolean isNull(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判断字符串是否为空
     * @param str 字符串
     * @return 结果
     */
    public static boolean isNull(String str) {
        return str == null || "".equals(str.trim()) || "null".equalsIgnoreCase(str.trim());
    }

    /**
     * 获取下拉列表或下拉树
     * @param tableName 代码表名
     * @param typeStr 下拉列表类型 tree下拉树
     * @param pcodeName 父节点字段名称（大写）
     * @param codeName code字段名称（大写）
     * @param captionName caption字段名称（大写）
     * @return 返回值
     */
    public static List<Map<String, Object>> getTreeOrSelectList(String tableName, String typeStr, String pcodeName, String codeName, String captionName) {
        final List<Map<String, Object>> resultList = new ArrayList<>();
        if (isNull(tableName)) {
            return resultList;
        }
        final List<Map<String, Object>> allIndexData = FtsCxsUtils.getAllIndexData(tableName.toLowerCase());
        if (isNull(allIndexData)) {
            return resultList;
        }
        for(Map<String, Object> itemMap : allIndexData){
            final Map<String, Object> paramMap = new HashMap<>();
            if ("tree".equals(typeStr)) {
                paramMap.put("pcode", itemMap.get(pcodeName));
                paramMap.put("code", itemMap.get(codeName));
                paramMap.put("caption", itemMap.get(captionName));
            } else {
                paramMap.put("value", itemMap.get(codeName));
                paramMap.put("label", itemMap.get(captionName));
            }
            resultList.add(paramMap);
        }
        return resultList;
    }

    /**
     * 获取上级税务机关list，有序
     * @param result 当前list
     * @param swjgDm 税务机关代码
     * @param resMap 遍历次数Map
     * @return 返回值
     */
    public static List<String> getSwjgList(List<String> result, String swjgDm ,Map<String,Object> resMap){
        String curSwjg = swjgDm;
        if (isNull(swjgDm)) {
            curSwjg = "00000000000";
            result.add(curSwjg);
        }
//        final Map<String, Object> swjgMap = FtsUtils.getIndexData("DM_GY_SWJG", swjgDm);
        final Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg" , swjgDm);
        result.add(swjgDm);
        if (!isNull(swjgMap)) {
            final String sjswjgDm = (String) swjgMap.get("sjswjgDm");
            if(!isNull(sjswjgDm)) {
                if(isNull(resMap.get("number"))){
                    resMap.put("number",1);
                }else if((Integer)resMap.get("number") > 7) {
                    //最多遍历7次
                    return result;
                }else {
                    resMap.put("number",(Integer)resMap.get("number")+1);
                }
                return getSwjgList(result, sjswjgDm ,resMap);
            }
        }
        return result;
    }

//    /**
//     * 通过税务机关代码获取对应的行政区划列表
//     * @param swjgDm 税务机关代码
//     * @return 返回值
//     */
//    public static List<Map<String, Object>> getXzqhListBySwjgDm(String swjgDm)  {
//        final List<Map<String, Object>> mapList = new ArrayList<>();
//        if(!isNull(swjgDm)){
//            String xzqhszDmTemp = "";
//            //直辖市，行政区划为XX0000
//            if(swjgDm.startsWith("111") || swjgDm.startsWith("112") || swjgDm.startsWith("131") || swjgDm.startsWith("150")){
//                xzqhszDmTemp = swjgDm.substring(1,3)+"0000";
//            }else {
//                xzqhszDmTemp = swjgDm.substring(1,5)+"00";
//            }
//            final String xzqhszDmNew = xzqhszDmTemp;
//            final Map<String, Object> xzqhNewMap = FtsCxsUtils.getIndexData("DM_GY_XZQH", xzqhszDmNew);
//            if(!isNull(xzqhNewMap)){
//                final List<Map<String, Object>> xzqhList = FtsCxsUtils.getAllIndexData("DM_GY_XZQH");
//                if(!isNull(xzqhList)){
//                    final List<Map<String, Object>> listFilter = xzqhList.stream().
//                            filter(xzqhMap -> xzqhszDmNew.equals(xzqhMap.get("SJXZQHSZ_DM"))).
//                            map(xzqhMap -> {
//                                final Map<String, Object> xzqhMapNew = new HashMap<>();
//                                xzqhMapNew.put("value",xzqhMap.get("XZQHSZ_DM"));
//                                xzqhMapNew.put("label",xzqhMap.get("XZQHMC"));
//                                return xzqhMapNew;
//                            }).
//                            sorted(Comparator.comparing(o -> ((String) o.get("value")))).
//                            collect(Collectors.toList());
//                    return listFilter;
//                }
//            }
//        }
//        return mapList;
//    }

//    /**
//     * 通过行政区划数字代码获取对应的街道乡镇列表
//     * @param xzqhszDm 行政区划数字代码
//     * @return 返回值
//     */
//    public static List<Map<String, Object>> getJdxzListByXzqhszDm(String xzqhszDm)  {
//        final List<Map<String, Object>> mapList = new ArrayList<>();
//        final List<Map<String, Object>> jdxzList = FtsCxsUtils.getGroupIndexData("CS_DJ_JDXZXZQHDZB", xzqhszDm);
//        if(!isNull(jdxzList)){
//            final List<Map<String, Object>> listFilter = jdxzList.stream()
//                    .filter(f -> "1".equals(f.get("GDSLX_DM")))
//                    .map(m -> {
//                        final Map<String, Object> tempMap = FtsCxsUtils.getIndexData("DM_GY_JDXZ", (String) m.get("JDXZ_DM"));
//                        if (!FtsCxsUtils.isNull(tempMap)) {
//                            m.put("value", FtsCxsUtils.isNull(tempMap.get("JDXZ_DM")) ? "" : tempMap.get("JDXZ_DM"));
//                            m.put("label", FtsCxsUtils.isNull(tempMap.get("JDXZMC")) ? "" : tempMap.get("JDXZMC"));
//                        }
//                        return m;
//                    })
//                    .filter(f -> !FtsCxsUtils.isNull(f.get("label")))
//                    .sorted(Comparator.comparing(o -> ((String) o.get("value"))))
//                    .collect(Collectors.toList());
//            return listFilter;
//        }
//        return mapList;
//        /*final List<Map<String, Object>> mapList = new ArrayList<>();
//        final List<Map<String, Object>> jdxzList = FtsUtils.getGroupIndexData("DM_GY_JDXZ", xzqhszDm);
//        if (!isNull(jdxzList)) {
//            final List<Map<String, Object>> listFilter = jdxzList.stream().
//                    map(jdxzMap -> {
//                        final Map<String, Object> jdxzMapNew = new HashMap<>();
//                        jdxzMapNew.put("value",jdxzMap.get("JDXZ_DM"));
//                        jdxzMapNew.put("label",jdxzMap.get("JDXZMC"));
//                        return jdxzMapNew;
//                    }).
//                    sorted(Comparator.comparing(o -> ((String) o.get("value")))).
//                    collect(Collectors.toList());
//            return listFilter;
//        }
//        return mapList;*/
//    }

    /**
     * 根据swjgDm获取房产计税比例列表
     * @param swjgDm 税务机关代码
     * @param czlx 操作类型
     * @return 返回值
     */
    public static List<Map<String, Object>> getFcyzKclList(String swjgDm, String czlx) {
        final List<Map<String, Object>> fcyzKclList = new ArrayList<>();
        //2023.12.02 陕西计税比例分支，如果系统参数配置了计税比例，新增add时取参数，修改edit时取配置
        boolean flag = true;
        if ("add".equals(czlx)) {
            flag = false;
            final String jsblXtcs = "0";
            if (FtsCxsUtils.isNull(jsblXtcs) || "0".equals(jsblXtcs)) {
                flag = true;
            } else if (jsblXtcs.contains(",")) {
                flag = false;
                final String[] splitArr = jsblXtcs.split(",");
                for (String str : splitArr) {
                    final Map<String, Object> temp = new HashMap<>();
                    temp.put("value", str);
                    temp.put("label", str);
                    fcyzKclList.add(temp);
                }
            } else {
                flag = false;
                final Map<String, Object> temp = new HashMap<>();
                temp.put("value", jsblXtcs);
                temp.put("label", jsblXtcs);
                fcyzKclList.add(temp);
            }
        }
        if (flag) {
            final List<String> resultList = new ArrayList<>();
            getSwjgList(resultList , swjgDm, new HashMap<>());
            if(!isNull(resultList)){
                //swjg有序，从下到上取值
                for(String swjgTemp: resultList){
                    final List<Map<String, Object>> kclList = FtsCxsUtils.getGroupIndexData("CS_DJ_FCYZKCLSZB", swjgTemp);
                    if(!isNull(kclList)){
                        for (Map<String, Object> mapTemp :kclList) {
                            final Map<String,Object> m = new HashMap<>();
                            final Double jsbl = (new BigDecimal(1).subtract(new BigDecimal(mapTemp.get("KCL").toString()))).doubleValue();
                            m.put("value", String.valueOf(jsbl));
                            m.put("label", String.valueOf(jsbl));
                            fcyzKclList.add(m);
                        }
                        break;
                    }
                }
            }
        }
        return fcyzKclList;
    }

    /**
     * 根据土地等级查询征收品目
     * @param tddjDm 土地等级代码
     * @param swjgDm 税务机关代码
     * @return 返回值
     */
    public static List<String> getZspmByTddj(String tddjDm, String swjgDm) {
        //如果取出多个，那么是有问题的，提示前台
        final List<String> zspmDmList = new ArrayList<>();
        /*select cs.tddj_dm, cs.swjg_dm, cs.zspm_dm from cs_dj_tddjzspmdzb cs,
        (select swjg.swjg_dm from dm_gy_swjg swjg connect by prior swjg.sjswjg_dm = swjg.swjg_dm start with swjg.swjg_dm =?) jg
        where cs.swjg_dm = jg.swjg_dm and cs.xybz = 'Y' and cs.yxbz = 'Y' and cs.tddj_dm = ?;*/
        //递归获得所有上级税务机关
        final List<String> resultList = new ArrayList<>();
        getSwjgList(resultList , swjgDm, new HashMap<>());
        if (!isNull(resultList)) {
            //swjg有序，从下到上取值
            for(String swjgTemp: resultList){
                final List<Map<String, Object>> zspmList = FtsCxsUtils.getGroupIndexData("CS_DJ_TDDJZSPMDZB",swjgTemp);
                if(!isNull(zspmList)){
                    final List<Map<String, Object>> listFilter = zspmList.stream().filter(zspmMap -> tddjDm.equals(zspmMap.get("TDDJ_DM"))).collect(Collectors.toList());
                    if(!isNull(listFilter)){
                        listFilter.forEach(f -> zspmDmList.add((String)f.get("ZSPM_DM")));
                    }
                }
            }
        }
        return zspmDmList;
    }

//    /**
//     * 根据行政区划和街道乡镇获取土地等级List
//     * @param xzqhszDm 行政区划数字代码
//     * @param jdxzDm 街道乡镇代码
//     * @return 返回值
//     */
//    public static List<Map<String, Object>> getTddjListByXzqh(String xzqhszDm, String jdxzDm) {
////        select a.dwse, a.tddj_dm, b.tddjmc, b.sjtddj_dm, a.yxqq, a.yxqz
////        from cs_dj_tddwseszb a, dm_dj_tddj b
////        where a.xzqhsz_dm =?
////        and a.yxqq <= a.yxqz
////        and a.xybz = 'Y'
////        and a.yxbz = 'Y'
////        and (a.jdxz_dm =? or a.jdxz_dm is null)
////        and a.tddj_dm = b.tddj_dm
////        and b.xybz = 'Y'
////        and b.yxbz = 'Y'
//        if(isNull(xzqhszDm) || isNull(jdxzDm)){
//            return new ArrayList<>();
//        }
//        final List<Map<String, Object>> jdxzList = new ArrayList<>();
//        final List<Map<String, Object>> dwseList = FtsCxsUtils.getGroupIndexData("CS_DJ_TDDWSESZB",xzqhszDm);
//        if(!isNull(dwseList)){
//            final Map<String,Object> tempMap = new HashMap<>();//过滤map
//            for(Map<String, Object> dwseMap : dwseList){
//                final Map<String,Object> resM = new HashMap<>();
//                final String tddjDmDmb = (String) dwseMap.get("TDDJ_DM");
//                //final String xzqhszDmDmb = (String) dwseMap.get("XZQHSZ_DM");
//                final Date yxqqDt = DateUtil.toDate((String)dwseMap.get("YXQQ"),"yyyy-MM-dd");
//                final Date yxqzDt = DateUtil.toDate((String)dwseMap.get("YXQZ"),"yyyy-MM-dd");
//                // 去掉当前时间检验
//                if(yxqqDt.compareTo(yxqzDt) <=0) {
//                    if(isNull(dwseMap.get("JDXZ_DM")) || jdxzDm.equals(dwseMap.get("JDXZ_DM"))){
//                        final Map<String, Object> tddjMap = FtsCxsUtils.getIndexData("DM_DJ_TDDJ", tddjDmDmb);
//                        if(!isNull(tddjMap)){
//                            final String tddjmc = (String) tddjMap.get("TDDJMC");
//                            final String sjtddjDm = (String) tddjMap.get("SJTDDJ_DM");
//                            resM.put("value",tddjDmDmb);
//                            resM.put("label",tddjmc);
//                            if(!isNull(sjtddjDm)){
//                                final Map<String, Object> sjTddjMap = FtsCxsUtils.getIndexData("DM_DJ_TDDJ", sjtddjDm);
//                                if(!isNull(sjTddjMap)){
//                                    if(tempMap.containsKey(tddjDmDmb)){
//                                        //过滤重复数据
//                                        continue;
//                                    }
//                                    final String sjTddjmc = (String) sjTddjMap.get("TDDJMC");
//                                    resM.put("label",sjTddjmc + "-" + tddjmc);
//                                }
//                            }
//                            tempMap.put(tddjDmDmb,tddjDmDmb);
//                            jdxzList.add(resM);
//                        }
//                    }
//                }
//            }
//        }
//        if(!isNull(jdxzList)){
//            return jdxzList.stream().sorted(Comparator.comparing(o1 -> (String) o1.get("value"))).collect(Collectors.toList());
//        } else {
//            return new ArrayList<>();
//        }
//    }

    /**
     * 获取单位税额List
     * @param tddjDm 土地等级代码
     * @param xzqhszDm 行政区划数字代码
     * @param jdxzDm 街道乡镇代码
     * @param ssqq 所属期起
     * @param ssqz 所属期止
     * @return 返回值
     */
    public static List<Map<String, Object>> getDwseByTddjSsqqz(String tddjDm, String xzqhszDm, String jdxzDm, String ssqq, String ssqz) {
//        SELECT * FROM CS_DJ_TDDWSESZB T
//        WHERE T .XZQHSZ_DM = ?
//        AND (T .JDXZ_DM = ? OR T .JDXZ_DM IS NULL)
//        AND (1 = ? OR (T .YXQQ <= ? AND T .YXQZ >= ?))
//        AND T .YXQQ <= T .YXQZ
//        AND T .YXBZ = 'Y'
//        AND T .XYBZ = 'Y'
//        ORDER BY T .TDDJ_DM,T .YXQQ
        final List<Map<String, Object>> jdxzList = new ArrayList<>();
        final List<Map<String, Object>> nullList = new ArrayList<>();
        final Date ssqqDt = DateUtil.toDate(ssqq,"yyyy-MM-dd");
        final Date ssqzDt = DateUtil.toDate(ssqz,"yyyy-MM-dd");
        final List<Map<String, Object>> dwseList = FtsCxsUtils.getGroupIndexData("CS_DJ_TDDWSESZB",xzqhszDm);
        if(!isNull(dwseList)){
            for(Map<String, Object> dwseMap : dwseList){
                final String tddjDmDmb = (String) dwseMap.get("TDDJ_DM");
                final Date yxqqDt = DateUtil.toDate((String)dwseMap.get("YXQQ"),"yyyy-MM-dd");
                final Date yxqzDt = DateUtil.toDate((String)dwseMap.get("YXQZ"),"yyyy-MM-dd");
                if(yxqqDt.compareTo(yxqzDt) <=0 && tddjDm.equals(tddjDmDmb)){
                    // 取交集
                    if(yxqzDt.before(ssqqDt) || yxqqDt.after(ssqzDt)){
                        continue;
                    }
                    if(isNull(dwseMap.get("JDXZ_DM"))){
                        nullList.add(dwseMap);
                    } else if(jdxzDm.equals(dwseMap.get("JDXZ_DM"))){
                        jdxzList.add(dwseMap);
                    }
                }
            }
        }
        if(!isNull(jdxzList)){
            return jdxzList.stream().sorted(Comparator.comparing(o1 -> (String) o1.get("YXQQ"))).collect(Collectors.toList());
        } else{
            return nullList.stream().sorted(Comparator.comparing(o1 -> (String) o1.get("YXQQ"))).collect(Collectors.toList());
        }
    }

//    /**
//     * 通过街道乡镇查询税务机关参数表信息CS_SB_CXS_JDXZSWJGDZB
//     * @param jdxzDm 街道乡镇代码
//     * @return 返回值
//     */
//    public static List<Map<String, Object>> getZgswjgListByJdxz(String jdxzDm){
//        final List<Map<String,Object>> list = new ArrayList<>();
//        final List<Map<String, Object>> csDjDzb = FtsCxsUtils.getGroupIndexData("CS_GY_JDXZSWJGDZB_SB", jdxzDm);
//        if(!isNull(csDjDzb)){
//            final List<Map<String, Object>> collect = csDjDzb.stream().map(m -> {
//                final Map<String, Object> resMap = new HashMap<>();
//                final Map<String, Object> swjgMap = FtsCxsUtils.getIndexData("DM_GY_SWJG", (String) m.get("ZGSWSKFJ_DM"));
//                if (!isNull(swjgMap)) {
//                    resMap.put("value", swjgMap.get("SWJG_DM"));
//                    resMap.put("label", swjgMap.get("SWJGMC"));
//                }
//                return resMap;
//            }).filter(f -> !FtsCxsUtils.isNull(f.get("value"))).collect(Collectors.toList());
//            list.addAll(collect);
//        }
//        /*List<Map<String,Object>> list = new ArrayList<>();
//        final List<Map<String, Object>> csDjDzb = FtsUtils.getGroupIndexData("CS_DJ_JDXZSWJGDZB_JDXZSWJG", jdxzDm);
//        if(!isNull(csDjDzb)){
//            list = csDjDzb.stream().map(m -> {
//                final Map<String,Object> resMap = new HashMap<>();
//                final Map<String, Object> swjgMap = FtsUtils.getIndexData("DM_GY_SWJG", (String) m.get("SWJG_DM"));
//                if(!isNull(swjgMap)){
//                    resMap.put("value", m.get("SWJG_DM"));
//                    resMap.put("label", swjgMap.get("SWJGMC"));
//                }
//                return resMap;
//            }).filter(f-> !FtsUtils.isNull(f.get("value"))).collect(Collectors.toList());
//        }*/
//        /*else {
//            final List<Map<String, Object>> jdxzswjgdzbList = FtsUtils.getGroupIndexData("CS_SB_CXS_JDXZSWJGDZB", jdxzDm);
//            if(!isNull(jdxzswjgdzbList)){
//                list = jdxzswjgdzbList.stream().map(m -> {
//                    final Map<String,Object> resMap = new HashMap<>();
//                    final Map<String, Object> swjgMap = FtsUtils.getIndexData("DM_GY_SWJG", (String) m.get("SWJG_DM"));
//                    if(!isNull(swjgMap)){
//                        resMap.put("value", m.get("SWJG_DM"));
//                        resMap.put("label", swjgMap.get("SWJGMC"));
//                    }
//                    return resMap;
//                }).collect(Collectors.toList());
//            }
//        }*/
//        return list;
//    }

    /**
     * 日期字符串转换格式，为空赋予空字符串
     * @param <T> 对象
     * @param source 需处理对象
     * @param clazz 对象类型
     * @param arr 需处理字段数组
     * @param format 日期格式
     */
    public static <T> void voDateFormat(T source, Class<T> clazz, String[] arr, String format) {
        for(Field field : clazz.getDeclaredFields()){
            field.setAccessible(true);//设置私有属性可访问
            //final String genericType = field.getGenericType().toString();
            final Class<?> type = field.getType();
            String name = field.getName();
            try {
                if("serialVersionUID".equals(name)){
                    continue;
                }
                if (type == String.class && Arrays.asList(arr).contains(name)){
                    name = name.replaceFirst(name.substring(0, 1), name.substring(0, 1).toUpperCase());
                    final Method methodSet = clazz.getDeclaredMethod("set" + name, type);
                    final Method methodGet = clazz.getDeclaredMethod("get" + name);
                    // 调用setter方法设置属性值
                    final String str = (String) methodGet.invoke(source);
                    if(isNull(str)){
                        methodSet.invoke(source, "");
                    }else {
                        methodSet.invoke(source, DateUtil.toDate(str,format));
                    }
                }
            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                log.error("", e);
            }
        }
    }

    /**
     * 过滤纳税人信息
     * @param nsrxxVOList 入参
     * @param shxydm 社会信用代码
     * @return 返回值
     */
    public static JbxxmxsjVO dofierNsrxx(List<JbxxmxsjVO> nsrxxVOList, String shxydm)  {
        //7.通过跨区标志，纳税人主体过滤纳税人信息
        final List<JbxxmxsjVO> returnkzztList = filterKQNsrxxLogin(nsrxxVOList);
        if (isNull(returnkzztList)) {
            return null;
        }
        //9.纳税人状态过滤基本信息（过滤08非正常户注销，09报验户，10 核销户）
        final List<JbxxmxsjVO> returnList = filterNsrztNsrxx(returnkzztList);
        if (isNull(returnList)) {
            return null;
        }
        //10.如果对方机关只有一条数据，并且为 正常，停业，非正常户，清算状态，直接返回
        if (returnList.size() == 1) {
            final  JbxxmxsjVO djSwdjbxxVO = returnList.get(0);
            final String nrsztdm = djSwdjbxxVO.getNsrztDm();
            if ("03".equals(nrsztdm) || "04".equals(nrsztdm) || "05".equals(nrsztdm) || "06".equals(nrsztdm)|| "07".equals(nrsztdm)) {
                return djSwdjbxxVO;
            }
        }
        //11.如果多条数据的，优先设立税务登记
        final List<JbxxmxsjVO> slswdjnsrNsrList = filerSlswdjnsr(returnList);
        if (!isNull(slswdjnsrNsrList)&&slswdjnsrNsrList.size() == 1) {
            final JbxxmxsjVO djSwdjbxxVO = slswdjnsrNsrList.get(0);
            final String nrsztdm = djSwdjbxxVO.getNsrztDm();
            if ("03".equals(nrsztdm) || "04".equals(nrsztdm) || "05".equals(nrsztdm) || "06".equals(nrsztdm)|| "07".equals(nrsztdm)) {
                return djSwdjbxxVO;
            }
        }
        //12.如果纳税人存在（03，04，05,06,07）纳税人信息，过滤掉07
        final List<JbxxmxsjVO> nsr07ztNsrList = filter07Ztnsr(slswdjnsrNsrList);
        if (nsr07ztNsrList.size() == 1) {
            final JbxxmxsjVO djSwdjbxxVO = nsr07ztNsrList.get(0);
            final String nrsztdm = djSwdjbxxVO.getNsrztDm();
            if ("03".equals(nrsztdm) || "04".equals(nrsztdm) || "05".equals(nrsztdm) || "06".equals(nrsztdm)) {
                return djSwdjbxxVO;
            }
        }
        return null;
    }

    /**
     * 通过跨区标志，纳税人主体过滤纳税人信息
     * @param queryDfnsrxxByssdabhList 入参
     * @return 返回值
     */
    private static List<JbxxmxsjVO> filterKQNsrxxLogin(List<JbxxmxsjVO> queryDfnsrxxByssdabhList)  {
        final List<JbxxmxsjVO> returnList = new ArrayList<>();
        final String kqccsztdjbz = "N";
        for (JbxxmxsjVO djSwdjbxxVO : queryDfnsrxxByssdabhList) {
            if (!isNull(djSwdjbxxVO)) {
//                final String nsrztlxDmTemp = djSwdjbxxVO.getNsrztlxDm();
                final String nsrztlxDmTemp = djSwdjbxxVO.getNsrztDm();
                final String kqccsztdjbzTemp = djSwdjbxxVO.getKqccsztdjbz();
                //纳跨区房产税标志必须为N ，纳税人状态标志为空
                if (kqccsztdjbz.equals(kqccsztdjbzTemp) && isNull(nsrztlxDmTemp)) {
                    returnList.add(djSwdjbxxVO);
                }
            }
        }
        return returnList;
    }

    /**
     * 纳税人状态过滤基本信息（过滤08非正常户注销，09报验户，10 核销户）
     * @param queryDfnsrxxByssdabhList 入参
     * @return 返回值
     */
    private static List<JbxxmxsjVO> filterNsrztNsrxx(List<JbxxmxsjVO> queryDfnsrxxByssdabhList) {
        final List<JbxxmxsjVO> returnList = new ArrayList<>();
        if (isNull(queryDfnsrxxByssdabhList)) {
            return returnList;
        }
        for (JbxxmxsjVO djSwdjbxxVO : queryDfnsrxxByssdabhList) {
            if (!isNull(djSwdjbxxVO)) {
                final String nsrztDm = djSwdjbxxVO.getNsrztDm();
                //过滤掉07注销，08非正常户注销，09报验户，10 核销户
                if ("08".equals(nsrztDm) || "09".equals(nsrztDm) || "10".equals(nsrztDm)) {
                    continue;
                }
                returnList.add(djSwdjbxxVO);
            }

        }
        return returnList;
    }

    /**
     * 如果多条数据的，优先设立税务登记
     * @param querynsrxxByssdabhList 入参
     * @return 返回值
     */
    private static List<JbxxmxsjVO> filerSlswdjnsr(List<JbxxmxsjVO> querynsrxxByssdabhList)  {
        if (isNull(querynsrxxByssdabhList)) {
            return null;
        }
        final List<JbxxmxsjVO> returnList = new ArrayList<>();
        for (JbxxmxsjVO djSwdjbxxVO : querynsrxxByssdabhList) {
            final String kzztlxdm = djSwdjbxxVO.getKzztdjlxDm();
            if (isKzztDm(kzztlxdm)) {//只有一条就可以登陆
                returnList.add(djSwdjbxxVO);
            }
        }
        return returnList;
    }

    /**
     * 如果纳税人存在（03，04，05,06,07）纳税人信息，过滤掉07
     * @param queryDfnsrxxByssdabhList 入参
     * @return 返回值
     */
    private static List<JbxxmxsjVO> filter07Ztnsr(List<JbxxmxsjVO> queryDfnsrxxByssdabhList)  {
        final List<JbxxmxsjVO> returnList = new ArrayList<>();
        for (JbxxmxsjVO djSwdjbxxVO : queryDfnsrxxByssdabhList) {
            if (!isNull(djSwdjbxxVO)) {
                final String nsrztDm = djSwdjbxxVO.getNsrztDm();
                //过滤掉07注销，
                if ("07".equals(nsrztDm)) {
                    continue;
                }
                returnList.add(djSwdjbxxVO);
            }
        }
        return returnList;
    }

    /**
     * 1110，1120，113开头的返回false,其它true
     * @param kzztDm 入参
     * @return 检验结果
     */
    private static boolean isKzztDm(String kzztDm) {
        if (isNull(kzztDm)) {
            return false;
        }
        final String tmep = kzztDm.substring(0, 3);
        if ("1110".equals(kzztDm)) {
            return true;
        }
        if ("1120".equals(kzztDm)) {
            return true;
        }
        if ("113".equals(tmep)) {
            return true;
        }
        return false;
    }

    /**
     * 校验最大长度
     * @param value 值
     * @param maxLength 最大长度
     * @return 检验结果
     */
    public static boolean checkMaxLength(String value, int maxLength){
        if (!isNull(value) && value.getBytes().length > maxLength) {
            return false;
        }
        return true;
    }

//    /**
//     * 根据key,value值转map
//     * @param tableName 表名
//     * @param key 需获取字段
//     * @param value 需转换字段值
//     * @return 返回值
//     */
    /*public static Map<String, Object> getAllCacheDataGroupMap(String tableName,String key,String value){
        final Map<String, Object> resMap = new HashMap<>();
        if(!isNull(tableName)){
            final List<Map<String, Object>> allIndexData = FtsUtils.getAllIndexData(tableName.toUpperCase());
            if(!isNull(allIndexData)){
                final Map<String, List<Map<String, Object>>> collect = allIndexData.stream().collect(Collectors.groupingBy(e -> (String)e.get(key)));
                for(String keyTemp :collect.keySet()){
                    final Object val = collect.get(keyTemp).get(0).get(value);
                    resMap.put(keyTemp,val);
                }
            }
        }
        return resMap;
    }*/

    /**
     * 校验数字长度
     * @param value 值
     * @param zsLength 整数长度
     * @param xsLength 小数长度
     * @return 检验结果
     */
    public static boolean checkDoubleLength(String value, int zsLength, int xsLength) {
        if (!isNull(value)) {
            try {
                Double.parseDouble(value);
            } catch (NumberFormatException e) {
                return false;
            }
            final String[] values = value.split("\\.");
            if (!isNull(values)) {
                if (values.length >= 1 && zsLength < values[0].length()) {
                    return false;
                }
                if (values.length > 1 && (isNull(values[1]) || xsLength < values[1].length())) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 校验日期格式
     * @param value 值
     * @param format 格式
     * @return 检验结果
     */
    public static boolean checkDate(String value, String format) {
        if (!isNull(value)) {
            final DateFormat dateFormat = new SimpleDateFormat(format);
            if("yyyy-MM-dd".equals(format) && value.length() != 10){
                return false;
            }
            Date date = null;
            try {
                date = dateFormat.parse(value);
            } catch (Exception e) {
                return false;
            }
            if (!isNull(date) && !value.equals(dateFormat.format(date))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取月初
     * @param date 日期
     * @return 月初日期
     */
    public static Date getFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        final Calendar c = (Calendar) calendar.clone();
        c.set(Calendar.DAY_OF_MONTH, 1);
        final Date formatDate = c.getTime();
        return formatDate;
    }

    /**
     * 获取月末
     * @param date 日期
     * @return 月末日期
     */
    public static Date getLastDayOfMonthByDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        final Calendar c = (Calendar) calendar.clone();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        final Date formatDate = c.getTime();
        return formatDate;
    }

    /**
     * 计算月份跨度
     * @param qq 期起
     * @param qz 期止
     * @return 月份跨度
     */
    public static int jsYfkd(Date qq,Date qz){
        int yfkd = 1;
        if(!isNull(qq) && !isNull(qz)){
            //期起>期止，非法，跨度计为0
            if(qq.compareTo(qz)>0){
                yfkd =0 ;
                return yfkd;
            }
            final int qqYear = getNdnum(qq);//年
            final int qzYear = getNdnum(qz);//年
            final int qqMonth = getYfnum(qq);//月
            final int qzMonth = getYfnum(qz);//月
            yfkd = (qzYear-qqYear)*12+ qzMonth - qqMonth+1;//年间隔*12个月每年+月间隔+1
        }
        return yfkd;
    }

    /**
     * 计算年度数字
     * @param da 日期
     * @return  年份数字
     */
    public static int getNdnum(Date da){
        int ndnum=0;
        if(!isNull(da)){
            final Calendar daCal = Calendar.getInstance();
            daCal.setTime(da);
            ndnum = daCal.get(Calendar.YEAR); // 年
        }
        return ndnum;
    }

    /**
     * 计算月份数字
     * @param da 日期
     * @return 月份数字
     */
    public static int getYfnum(Date da){
        int yfnum=0;
        if(da!=null){
            final Calendar daCal = Calendar.getInstance();
            daCal.setTime(da);
            yfnum = daCal.get(Calendar.MONTH) + 1; // 月
        }
        return yfnum;
    }

    /**
     * 获取异常堆栈信息
     * @param t 异常
     * @return 堆栈信息
     */
    public static String getTrace(Throwable t){
        if(t == null){
            return "";
        }
        final StackTraceElement[] stackElements = t.getStackTrace();
        if(stackElements == null){
            return "";
        }
        final StringBuilder sb = new StringBuilder("error:");
        sb.append(t);
        for(StackTraceElement stackTraceElement : stackElements){
            sb.append("\n\tat ").append(stackTraceElement);
        }
        return sb.toString();
    }

    /**
     * 科学计算法Double转String
     * @param d 入参
     * @return 返回值
     */
    public static String getStringByDouble(Double d) {
        NumberFormat nf = NumberFormat.getInstance();
        //最多保留小数位
        nf.setMaximumFractionDigits(9);
        // 取消科学计数法
        nf.setGroupingUsed(false);
        //返回结果
        return nf.format(d);
    }

    /**
     * 获取当期日期的下月第一天
     * @param param 支持yyyy-mm-dd,yyyy-mm格式
     * @return 当期日期的下月第一天
     */
    public static String getFirstDayOfNextMonth(String param) {
        final String lastDay = getLastDayOfMonth(param);
        final Calendar cal = Calendar.getInstance();
        cal.setTime(cast2Date(lastDay));
        cal.add(Calendar.DATE, 1);
        final String firstDayNM = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        return firstDayNM;
    }
    public static Date cast2Date(Object obj){
        Date returnVal = null;
        if (obj != null) {
            if (obj instanceof String) {
                if (((String) obj).trim().length() == 0) {
                    returnVal = null;
                } else {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                    // 使用DateTimeFormatter对象的parse方法将字符串转换为LocalDate对象
                    try {
                        returnVal = formatter.parse(String.valueOf(obj));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
            } else if (obj instanceof Calendar) {
                final Calendar ca = (Calendar) obj;
                returnVal = ca.getTime();
            } else if (obj instanceof Date) {
                returnVal = (Date) obj;
            }
        }
        return returnVal;
    }
    /**
     * 获取某年某月的最后一天
     * @param param 支持yyyy-mm-dd,yyyy-mm格式
     * @return 某年某月的最后一天
     */
    public static String getLastDayOfMonth(String param) {
        String lastDay = "";
        if (isNull(param)) {
            return lastDay;
        }
        // 如果字符长度小于了7，即不满足yyyy-mm
        final int strlen = param.length();
        final String[] nyr = param.split("-");
        if (strlen < 7 || nyr.length < 2) {
            return lastDay;
        }
        // 取年
        final int year = Integer.parseInt(nyr[0]);
        // 取月：
        int month = Integer.parseInt(nyr[1]);
        // 月份是从0开始的，比如说如果输入5的话，实际上显示的是4月份的最后一天
        month = month - 1;
        final Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month);
        cal.set(Calendar.DATE, 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DATE));
        lastDay = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        return lastDay;
    }

//    /**
//     * 获得三级级联Map
//     * @param swjgDm 入参
//     * @return 返回值
//     * @throws Exception 异常
//     */
    /*public static Map<String, Map<String, List<String>>> getSjjlHc(String swjgDm) {
        final Map<String, Map<String, List<String>>> jlMap = new HashMap<>();
        final List<Map<String, Object>> xzqhList = FtsUtils.getXzqhListBySwjgDm(swjgDm);//码值为value，label
        final List<Map<String, Object>> swjgListOld = FtsUtils.getAllCacheData("CS_DJ_JDXZSWJGDZB_JDXZSWJG");//码值为大写
        final Map<String, List<Map<String, Object>>> swjgMap = swjgListOld.stream().collect(Collectors.groupingBy(g -> (String)g.get("JDXZ_DM")));
        xzqhList.forEach(m1 ->{
            final String xzqhszDm = (String) m1.get("value");
            final List<Map<String, Object>> jdxzList = FtsUtils.getJdxzListByXzqhszDm((String) m1.get("value"));
            if(!FtsUtils.isNull(jdxzList)){
                final Map<String, List<String>> jdxzMap = new HashMap<>();
                jdxzList.forEach(m2 -> {
                    final String jdxzDm = (String) m2.get("value");
                    final List<Map<String, Object>> swjgListTemp = swjgMap.get(jdxzDm);
                    if(!FtsUtils.isNull(swjgListTemp)){
                        final List<String> swjgFinal = swjgListTemp.stream().map(m3 -> (String) m3.get("SWJG_DM")).collect(Collectors.toList());
                        jdxzMap.put(jdxzDm , swjgFinal);
                    }else {
                        jdxzMap.put(jdxzDm , new ArrayList<>());
                    }
                });
                jlMap.put(xzqhszDm , jdxzMap);
            }else {
                jlMap.put(xzqhszDm , new HashMap<>());
            }
        });
        return jlMap;
    }*/

    /**
     * 判断两个Double是否相等
     * @param a 值A
     * @param b 值B
     * @param scale 保留精度
     * @return 返回值
     */
    public static Boolean checkDoubelEqual(Double a, Double b, int scale){
        if(isNull(a) || isNull(b)){
            return false;
        }
        final BigDecimal aBig = new BigDecimal(GYCastUtils.cast2Str(a));
        final BigDecimal bBig = new BigDecimal(GYCastUtils.cast2Str(b));
        final BigDecimal aBigS = aBig.setScale(scale, RoundingMode.DOWN);
        final BigDecimal bBigS = bBig.setScale(scale, RoundingMode.DOWN);
        return aBigS.compareTo(bBigS) == 0;
    }

    /**
     * 比较A和0
     * @param a 值A
     * @return 0~相等，1~A大于0，-1~A小于0
     */
//    public static int compareAandZero(Double a){
//        //入参不能为空
//        if(isNull(a)){
//            return -1;
//        }
//        final BigDecimal aBig = new BigDecimal(GYCastUtils.cast2Str(a));
//        final BigDecimal zeroBig = new BigDecimal(0);
//        final int returnInt = aBig.compareTo(zeroBig);
//        return returnInt;
//    }

    /**
     * 获取减免月计税依据
     * @param yslx 应税类型 TD土地,CJ房屋从价,CZ房屋从租
     * @param jsyj TD-jmmj减免面积,CJ-jmsfcyz减免税房产原值,CZ-jmszjsr减免税租金收入
     * @param jsbl CJ-计算比率
     * @param sbqq CZ-申报期起
     * @param sbqz CZ-申报期止
     * @return jsyj2 月计税依据
     */
    public static double getJsyj2(String yslx, double jsyj ,double jsbl ,Date sbqq, Date sbqz) {
        double jsyj2 = 0.00;
        if ("TD".equals(yslx)) {
            jsyj2 = divide(jsyj, 12);
        }
        if ("CJ".equals(yslx)) {
            jsyj2 = multiple(divide(jsyj, 12), jsbl);
        }
        if ("CZ".equals(yslx)) {
            final int yfkd = jsYfkd(sbqq, sbqz);
            jsyj2 = divide(jsyj, yfkd);
        }
        return jsyj2;
    }

    /**
     * 获取减免月应纳税额
     * @param yslx 应税类型 TD土地,CJ房屋从价,CZ房屋从租
     * @param fdsl TD-dwse单位税额,CJ-0.012固定值,CZ-0.12固定值
     * @param jsyj2 月计税依据
     * @return ynse 月应纳税额
     */
    public static double getYnse(String yslx, double fdsl , double jsyj2) {
        double ynse = 0.00;
        if ("TD".equals(yslx)) {
            ynse = multiple(jsyj2, fdsl);
        }
        if ("CJ".equals(yslx)) {
            ynse = multiple(jsyj2, fdsl);
        }
        if ("CZ".equals(yslx)) {
            ynse = multiple(jsyj2, fdsl);
        }
        return ynse;
    }

    /**
     * 计算减免税额By税收减免性质
     * @param jmzlxDm 减免征类型
     * @param jsyj 计税依据
     * @param ynse 应纳税额
     * @param fdsl 法定税率
     * @param jmfd 减免幅度
     * @param jmed 减免额度
     * @param jmsl 减免税率
     * @return jmse 减免税额
     */
    public static double getJmseBySsjmxz(String jmzlxDm, double jsyj, double ynse, double fdsl, double jmfd, double jmed, double jmsl) {
        double jmse = 0.00;
        double sljmje = 0.00;
        if ("02".equals(jmzlxDm)) {
            jmse = ynse;
        } else if ("01".equals(jmzlxDm)) {
            if (jmed > 0) {
                jmse = Math.min(jmed, ynse);
            } else if (jmsl > 0 && fdsl > jmsl) {
                sljmje = multiple(jsyj, subtract(fdsl, jmsl));
                jmse = Math.min(sljmje, ynse);
            } else if (jmfd > 0 && jmfd < 1) {
                jmse = multiple(ynse, jmfd);
            }
        }
        return round(jmse, 2);
    }

    /**
     * 加法
     * @param v1 入参
     * @param v2 入参
     * @return 返回值
     */
    public static double add(double v1, double v2){
        BigDecimal decimal = new BigDecimal(Double.toString(v1));
        decimal = decimal.add(new BigDecimal(Double.toString(v2)));
        return decimal.doubleValue();
    }

    /**
     * 减法
     * @param v1 入参
     * @param v2 入参
     * @return 返回值
     */
    public static double subtract(double v1, double v2) {
        BigDecimal decimal = new BigDecimal(Double.toString(v1));
        decimal = decimal.subtract(new BigDecimal(Double.toString(v2)));
        return decimal.doubleValue();
    }

    /**
     * 乘法
     * @param v1 入参
     * @param v2 入参
     * @return 返回值
     */
    public static double multiple(double v1, double v2) {
        BigDecimal decimal = new BigDecimal(Double.toString(v1));
        decimal = decimal.multiply(new BigDecimal(Double.toString(v2)));
        return decimal.doubleValue();
    }

    /**
     * 除法
     * @param v1 入参
     * @param v2 入参
     * @return 返回值
     */
    public static double divide(double v1, double v2) {
        return divide(v1, v2, 10);
    }

    /**
     * 除法
     * @param v1 入参
     * @param v2 入参
     * @param scale 精度
     * @return 返回值
     */
    public static double divide(double v1, double v2, int scale) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 精度
     * @param v 入参
     * @param scale 入参
     * @return 返回值
     */
    public static double round(double v, int scale) {
        BigDecimal b = new BigDecimal(Double.toString(v));
        return b.setScale(scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 获取省级行政区划数字代码By系统参数
     * @return 省级行政区划数字代码 直辖市为110000,120000,310000,500000
     */
    public static String getShengXzqhszDmBySwjgDm(String swjgDm){
        //计划单列市，行政区划为XXXX00，大连，宁波，厦门，青岛，深圳
        final String[] jhdlsXzqhArr = new String[]{"210200","330200","350200","370200","440300"};
        if(!GYObjectUtils.isNull(swjgDm) && swjgDm.length() == 11){
            final String xzqhDmTemp = swjgDm.substring(1, 5) + "00";
            String shengXzshszDm = "";
            if (Arrays.asList(jhdlsXzqhArr).contains(xzqhDmTemp)) {
                shengXzshszDm = swjgDm.substring(1, 5) + "00";
            } else {
                shengXzshszDm = swjgDm.substring(1, 3) + "0000";
            }
            if(!GYObjectUtils.isNull(shengXzshszDm)){
                return shengXzshszDm;
            }
        }
        return "";
    }

//    /**
//     * 获取行政区划树，默认省级开始，计划单列市默认单列
//     * 省市直管乡镇街道暂不考虑
//     * @param shengXzqhszDm 省级行政区划代码
//     * @return 返回值
//     */
//    public static List<Map<String, Object>> getXzqhListByShengXzqhszDm(String shengXzqhszDm)  {
//        final List<Map<String, Object>> returnList = new ArrayList<>();
//        //是否为直辖市
//        boolean zxsFlag = false;
//        //是否为计划单列市
//        boolean jhdlsFlag = false;
//        //是否为包含计划单列市省份
//        boolean bhdlsSfFlag = false;
//        if(!GYObjectUtils.isNull(shengXzqhszDm)){//判空
//            //直辖市，行政区划为XX0000，北京，天津，上海，重庆
//            final String[] zxsXzqhArr = new String[]{"110000","120000","310000","500000"};
//            //判断是否为直辖市
//            if(Arrays.asList(zxsXzqhArr).contains(shengXzqhszDm)){
//                zxsFlag = true;
//            }
//            //计划单列市，行政区划为XXXX00，大连，宁波，厦门，青岛，深圳
//            final String[] jhdlsXzqhArr = new String[]{"210200","330200","350200","370200","440300"};
//            //判断是否为计划单列市
//            if(Arrays.asList(jhdlsXzqhArr).contains(shengXzqhszDm)){
//                jhdlsFlag = true;
//            }
//            //包含计划单列市省份，行政区划为XX0000，辽宁省，浙江省，福建省，山东省，广东省
//            final String[] bhdlsSfXzqhArr = new String[]{"210000","330000","350000","370000","440000"};
//            //判断是否为包含计划单列市省份
//            if(Arrays.asList(bhdlsSfXzqhArr).contains(shengXzqhszDm)){
//                bhdlsSfFlag = true;
//            }
//            //获取当前行政区划信息
//            final Map<String, Object> xzqhNewMap = FtsCxsUtils.getIndexData("DM_GY_XZQH", shengXzqhszDm);
//            //判断是否为省级行政区划，不为空 && （级次为1 || （级次为2 && 计划单列市））
//            if(!GYObjectUtils.isNull(xzqhNewMap) && ("1".equals(xzqhNewMap.get("XZQHJC")) || ("2".equals(xzqhNewMap.get("XZQHJC")) && jhdlsFlag))){
//                //取出全量行政区划，3000多条
//                final List<Map<String, Object>> xzqhListTemp = FtsCxsUtils.getAllIndexData("DM_GY_XZQH");
//                if(!isNull(xzqhListTemp)) {
//                    final Map<String, List<Map<String, Object>>> collect = xzqhListTemp.stream().
//                            filter(f -> !GYObjectUtils.isNull(f.get("SJXZQHSZ_DM")) //移除省级行政区划
//                                    && ("2".equals(f.get("XZQHJC")) || "3".equals(f.get("XZQHJC"))) //保留市区行政区划
//                                    && shengXzqhszDm.substring(0,2).equals(String.valueOf(f.get("SJXZQHSZ_DM")).substring(0,2))). //仅保留该省级行政区划下级代码
//                                    map(m -> {//重组前端所需下拉树结果
//                                final Map<String, Object> xzqhMapTemp = new HashMap<>();
//                                xzqhMapTemp.put("value", m.get("XZQHSZ_DM"));
//                                xzqhMapTemp.put("label", m.get("XZQHMC"));
//                                xzqhMapTemp.put("SJXZQHSZ_DM", m.get("SJXZQHSZ_DM"));
//                                xzqhMapTemp.put("XZQHJC", m.get("XZQHJC"));
//                                xzqhMapTemp.put("XZQHSZ_DM", m.get("XZQHSZ_DM"));
//                                return xzqhMapTemp;
//                            }).
//                            collect(Collectors.groupingBy(g -> (String) g.get("SJXZQHSZ_DM")));//以上级行政区划数字代码分组
//                    if(!GYObjectUtils.isNull(collect)){//判空
//                        //获取省级行政区划下级区划列表
//                        List<Map<String, Object>> xzqhShiList = collect.get(shengXzqhszDm);
//                        if (!GYObjectUtils.isNull(xzqhShiList)) {//判空
//                            if (!zxsFlag && !jhdlsFlag) {//非直辖市，计划单列市
//                                if(bhdlsSfFlag){
//                                    final List<String> jhdlsXzqhList = Arrays.asList(jhdlsXzqhArr);
//                                    xzqhShiList = xzqhShiList.stream().filter(f -> !jhdlsXzqhList.contains((String)f.get("XZQHSZ_DM"))).collect(Collectors.toList());
//                                }
//                                xzqhShiList.forEach(each -> {
//                                    //如果为市级行政区划则不可选，为省级直辖可选，移动逻辑
//                                    /*if("2".equals(each.get("XZQHJC"))){
//                                        each.put("disabled", true);
//                                    }*/
//                                    //获取上级行政区划代码
//                                    final String shiXzqhszDm = (String) each.get("XZQHSZ_DM");
//                                    //获取区县级行政区划列表
//                                    if (!GYObjectUtils.isNull(shiXzqhszDm) && !GYObjectUtils.isNull(collect.get(shiXzqhszDm))) {//判空
//                                        final List<Map<String, Object>> xzqhQuXianList = collect.get(shiXzqhszDm);
//                                        each.put("children", xzqhQuXianList);
//                                        //如果有下级则不可选，否则可选
//                                        each.put("disabled", true);
//                                    }
//                                    /*if("460400".equals(shiXzqhszDm)){
//                                        //海南-儋州市-行政区划级次为2，但无下级，允许选择
//                                        each.put("disabled", false);
//                                    }
//                                    if("441900".equals(shiXzqhszDm)){
//                                        //广东-东莞市-行政区划级次为2，但无下级，允许选择
//                                        each.put("disabled", false);
//                                    }
//                                    if("442000".equals(shiXzqhszDm)){
//                                        //广东-中山市-行政区划级次为2，但无下级，允许选择
//                                        each.put("disabled", false);
//                                    }*/
//                                });
//                                //构造返回值
//                                returnList.addAll(xzqhShiList);
//                            }
//                            if (zxsFlag || jhdlsFlag) {//直辖市或计划单列市
//                                //此时 xzqhShiList 为区县级
//                                final List<Map<String, Object>> xzqhShiListNew = new ArrayList<>();
//                                //构造直辖市行政区划
//                                final Map<String, Object> xzqhZxsMap = new HashMap<>();
//                                xzqhZxsMap.put("value", xzqhNewMap.get("XZQHSZ_DM"));
//                                xzqhZxsMap.put("label", xzqhNewMap.get("XZQHMC"));
//                                xzqhZxsMap.put("disabled", true);
//                                //放入区县级行政区划列表
//                                xzqhZxsMap.put("children", xzqhShiList);
//                                //放入直辖市行政区划
//                                xzqhShiListNew.add(xzqhZxsMap);
//                                //构造返回值
//                                returnList.addAll(xzqhShiListNew);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        return returnList;
//    }

    /**
     * 获取房土异步申报配置数量
     * @param swjgDm 入参
     * @return 返回值
     */
    public static String getFtYbsbNumber(String swjgDm) {
        String ftYbsbNumber = "";
        final List<String> swjgList = new ArrayList<>();
        FtsCxsUtils.getSwjgList(swjgList ,swjgDm ,new HashMap<>());
        for(String dm : swjgList){
            //房土异步申报配置数量
            final List<Map<String,Object>> xtcsList = FtsCxsUtils.getGroupIndexData("CS_GY_XTCS_XDJ","DZSWJ00SBZX020011",dm);
            if(!isNull(xtcsList)){
                ftYbsbNumber = (String) xtcsList.get(0).get("CSZ");
                break;
            }
        }
        // 如果没配置，则默认取网签
        if(isNull(ftYbsbNumber)){
            ftYbsbNumber = "100";
        }
        return ftYbsbNumber;
    }

    /**
     * 代码转名称
     * @param tableName 表名
     * @param key 字段名
     * @param dm 字段值
     * @return 返回值
     */
    public static String getMcByDm(String tableName, String key , String dm){
        if(!isNull(tableName) && !isNull(key) && !isNull(dm)){
            final Map<String, Object> map = FtsCxsUtils.getIndexData(tableName, dm);
            if(!isNull(map)){
                final Object obj = map.get(key);
                if(!isNull(obj)){
                    return String.valueOf(obj);
                }
            }
        }
        return "";
    }

//    /**
//     * 获取码表 -- 增加判空
//     * @param tableName 表名
//     * @param dm 代码
//     * @return 返回值
//     */
//    public static Map<String, Object> getMapByDm(String tableName, String dm){
//        if(!isNull(tableName) && !isNull(dm)){
//            final Map<String, Object> map = FtsCxsUtils.getIndexData(tableName.toUpperCase(), dm);
//            if(!isNull(map)){
//                return map;
//            }
//        }
//        return new HashMap<>();
//    }

    /**
     * 获取全表 -- 增加判空
     * @param tableName 表名
     * @return 返回值
     */
    public static List<Map<String, Object>> getAllCacheData(String tableName){
        if(!isNull(tableName)){
            String table = tableName.toLowerCase();
            List<Map<String, Object>> allIndexData;
            if ("CS_GY_ZQTZ".equals(tableName)) {
                try {
                    allIndexData = CacheUtils.getTableData(table);
                } catch (Exception e){
                    allIndexData = new ArrayList<>();
                }
            } else {
                allIndexData = CacheUtils.getTableData(table);
            }
            if(!isNull(allIndexData)){
                return allIndexData;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取分组缓存数据 -- 增加判空
     * @param tableName 入参
     * @param dm 入参
     * @return 返回值
     */
    public static List<Map<String, Object>> getGroupCacheData(String tableName, String dm) {
        if(!isNull(tableName) && !isNull(dm)){
            final List<Map<String, Object>> list = FtsCxsUtils.getGroupIndexData(tableName.toUpperCase(), dm);
            if(!isNull(list)){
                return list;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取缓存表信息单条
     * @param tableName 表名
     * @param key 关键字
     * @return 返回值
     */
    public static Map<String, Object> getIndexData(String tableName, String key) {
        String table = tableName.toLowerCase();
        String keyvalue = key.toLowerCase();
        return CacheUtils.getTableData(table , keyvalue);
    }

    /**
     * 获取缓存表信息全量
     * @param tableName 表名
     * @return 返回值
     */
    public static List<Map<String, Object>> getAllIndexData(String tableName) {
        return CxsRedisUtils.getAllIndexData(tableName);
    }

    /**
     * 获取缓存表信息分组
     * @param tableName 表名
     * @param keys 关键字
     * @return 返回值
     */
    public static List<Map<String, Object>> getGroupIndexData(String tableName, Object... keys) {
        return CxsRedisUtils.getGroupIndexData(tableName , keys);
    }

    /**
     * 获取缓存表信息全量
     * @param tableName 表名
     * @return 返回值
     */
    public static List<Map<String, Object>> getAllIndexDataOld(String tableName) {
        if(!isNull(tableName)){
            final List<Map<String, Object>> allIndexData = CxsRedisUtils.getAllIndexData(tableName);
            if(!isNull(allIndexData)){
                return allIndexData;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取缓存表信息分组
     * @param tableName 表名
     * @return 返回值
     */
    public static List<Map<String, Object>> getGroupIndexDataOld(String tableName, Object... keys) {
        if(!isNull(tableName)){
            final List<Map<String, Object>> allIndexData = CxsRedisUtils.getGroupIndexData(tableName, keys);
            if(!isNull(allIndexData)){
                return allIndexData;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取纳税人行政区划参数值
     * @param swjgDm 税务机关代码
     * @param yzpzzlDm 应征凭证种类代码
     * @param zsxmDm 征收项目代码
     * @return 返回值
     */
    public static String getNsrXzqhCsz(String swjgDm, String yzpzzlDm, String zsxmDm) {
        String csz = "";
        if (!FtsCxsUtils.isNull(swjgDm) && (!FtsCxsUtils.isNull(yzpzzlDm) || !FtsCxsUtils.isNull(zsxmDm))) {
            final List<Map<String, Object>> pzList = new ArrayList<>();
            final List<Map<String, Object>> allList = FtsCxsUtils.getAllIndexDataOld("CS_SB_CXSXZQHXZFSPZB");
            if (!FtsCxsUtils.isNull(allList)) {
                //根据不同的入参形式获取参数值
                if (!FtsCxsUtils.isNull(yzpzzlDm) && !FtsCxsUtils.isNull(zsxmDm)) {
                    final List<Map<String, Object>> filterList = allList.stream().filter(f -> yzpzzlDm.equals(f.get("YZPZZL_DM")) && zsxmDm.equals(f.get("ZSXM_DM"))).collect(Collectors.toList());
                    pzList.addAll(filterList);
                } else if (!FtsCxsUtils.isNull(yzpzzlDm)) {
                    final List<Map<String, Object>> filterList = allList.stream().filter(f -> yzpzzlDm.equals(f.get("YZPZZL_DM"))).collect(Collectors.toList());
                    pzList.addAll(filterList);
                } else if (!FtsCxsUtils.isNull(zsxmDm)) {
                    final List<Map<String, Object>> filterList = allList.stream().filter(f -> zsxmDm.equals(f.get("ZSXM_DM"))).collect(Collectors.toList());
                    pzList.addAll(filterList);
                }
            }
            if (!FtsCxsUtils.isNull(pzList)) {
                final Map<String, List<Map<String, Object>>> pzGroup = pzList.stream().collect(Collectors.groupingBy(g -> (String) g.get("ZGSWJG_DM")));
                //获取本级及本上级税务机关，有序
                final List<String> swjgList = new ArrayList<>();
                FtsCxsUtils.getSwjgList(swjgList , swjgDm, new HashMap<>());
                for (String jg : swjgList) {
                    final List<Map<String, Object>> pzTemp = pzGroup.get(jg);
                    if (!FtsCxsUtils.isNull(pzTemp)) {
                        //默认取第一条就可以了
                        csz = (String) pzTemp.get(0).get("CXSXZQHXZFS_DM");
                        if ("01".equals(csz) || "02".equals(csz) || "03".equals(csz)) {
                            break;
                        }
                    }
                }
            }
        }
        if (!FtsCxsUtils.isNull(csz)) {
            return csz;
        }
        return "01";
    }

//    /**
//     * 根据机关和对照表获取行政区划列表
//     * @param zgswsfjDm 主管税务所科分局代码
//     * @param xzqhszDmNsr 行政区划数字代码
//     * @return 返回值
//     */
//    public static List<Map<String, Object>> getXzqhByJgAndDzb(String zgswsfjDm, String xzqhszDmNsr) {
//        final List<Map<String, Object>> allList = new ArrayList<>();
//        //默认行政区划
//        final Map<String, Object> xzqhHcbMap = FtsCxsUtils.getMapByDm("DM_GY_XZQH", xzqhszDmNsr);
//        if (!FtsCxsUtils.isNull(xzqhHcbMap)) {
//            final Map<String, Object> xzqhMap = new HashMap<>();
//            xzqhMap.put("value",xzqhszDmNsr);
//            xzqhMap.put("label",xzqhHcbMap.get("XZQHMC"));
//            xzqhMap.put("XZQHSZ_DM", xzqhszDmNsr);
//            xzqhMap.put("XZQHMC", xzqhHcbMap.get("XZQHMC"));
//            allList.add(xzqhMap);
//        }
//        final List<Map<String, Object>> jdxzswjgdzbList = FtsCxsUtils.getGroupCacheData("CS_GY_JDXZSWJGDZB_ZGSWSKFJ", zgswsfjDm);
//        if (!FtsCxsUtils.isNull(jdxzswjgdzbList)) {
//            final Set<String> jdxzSet = jdxzswjgdzbList.stream().map(m -> (String) m.get("JDXZ_DM")).collect(Collectors.toSet());
//            final List<Map<String, Object>> collect = jdxzSet.stream().map(m -> {
//                final Map<String, Object> temp = new HashMap<>();
//                final Map<String, Object> jdxzxzqhdzbMap = FtsCxsUtils.getMapByDm("CS_DJ_JDXZXZQHDZB", m);
//                if (!FtsCxsUtils.isNull(jdxzxzqhdzbMap) && !FtsCxsUtils.isNull(jdxzxzqhdzbMap.get("XZQHSZ_DM")) && "1".equals(jdxzxzqhdzbMap.get("GDSLX_DM"))) {
//                    final String xzqhszDm = (String) jdxzxzqhdzbMap.get("XZQHSZ_DM");
//                    final Map<String, Object> xzqhMap = FtsCxsUtils.getMapByDm("DM_GY_XZQH", xzqhszDm);
//                    if (!FtsCxsUtils.isNull(xzqhMap) && !FtsCxsUtils.isNull(xzqhMap.get("XZQHMC"))) {
//                        final String xzqhmc = (String) xzqhMap.get("XZQHMC");
//                        temp.put("value", xzqhszDm);
//                        temp.put("label", xzqhmc);
//                        temp.put("XZQHSZ_DM", xzqhszDm);
//                        temp.put("XZQHMC", xzqhmc);
//                    }
//                }
//                return temp;
//            }).filter(f -> !FtsCxsUtils.isNull(f.get("value"))).collect(Collectors.toList());
//            if (!FtsCxsUtils.isNull(collect)) {
//                allList.addAll(collect);
//            }
//        }
//        final List<Map<String, Object>> returnList = allList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(qc -> (String) qc.get("value")))), ArrayList::new));
//        return returnList;
//    }

//    /**
//     * 获取车船税征收子目ListMap
//     * @param swjgDm 机关
//     * @return 返回值
//     */
//    public static Map<String, List<Map<String, Object>>> getZszmCcs(String swjgDm) {
//        final Map<String, List<Map<String, Object>>> listMap = new HashMap<>();
//        //final String swjgDm = (String) req.get("swjgDm");//税务机关代码
//        //final String zsxmDm = (String) req.get("zsxmDm");//征收项目代码
//        //final String zspmDm = (String) req.get("zspmDm");//征收品目代码
//        //当前日期
//        final Date nowdate = DateUtil.toDate(DateUtil.doDateFormat(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
//        //税务机关、征收项目、征收品目 不能为空
//        if (FtsCxsUtils.isNull(swjgDm)) {
//            return listMap;
//        }
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = new ArrayList<>();
//        getSwjgList(bjsjSwjgList , swjgDm, new HashMap<>());
//        //获取车船税全量子目
//        final List<Map<String, Object>> allList = FtsCxsUtils.getGroupIndexDataOld("CS_GY_GLB_ZSZM_GXM", "10114");
//        //根据征收品目以及税务机关分组
//        final Map<String, Map<String, List<Map<String, Object>>>> zszmSwjgZspmMap = allList.stream()
//                .filter(f -> !FtsCxsUtils.isNull(f.get("ZSPM_DM")) && !FtsCxsUtils.isNull(f.get("SWJG_DM")) && !FtsCxsUtils.isNull(f.get("ZSZM_DM")))
//                .collect(Collectors.groupingBy(g -> (String) g.get("ZSPM_DM"), Collectors.groupingBy(g -> (String) g.get("SWJG_DM"))));
//        if (!FtsCxsUtils.isNull(zszmSwjgZspmMap)) {
//            //循环遍历征收品目信息
//            for (Map.Entry<String, Map<String, List<Map<String, Object>>>> entry : zszmSwjgZspmMap.entrySet()) {
//                //获取该品目税务机关分组信息
//                final Map<String, List<Map<String, Object>>> zszmSwjgMap = entry.getValue();
//                if (!FtsCxsUtils.isNull(zszmSwjgMap)) {
//                    //返回该品目包含集合-转换后
//                    final List<Map<String, Object>> zszmNewList = new ArrayList<>();
//                    //返回该品目包含集合-缓存中
//                    final List<Map<String, Object>> zszmOldList = new ArrayList<>();
//                    //逐级上查
//                    for (String bjsjSwjgDm : bjsjSwjgList) {
//                        //获取该品目集合
//                        List<Map<String, Object>> zszmKvList = zszmSwjgMap.get(bjsjSwjgDm);
//                        if (!FtsCxsUtils.isNull(zszmKvList)) {
//                            //过滤Set
//                            final Set<String> keySet = new HashSet<>();
//                            //只添加没有重复子目代码的到之前集合：即保证代码优先用下级的
//                            final List<Map<String, Object>> fiterZszmKvList = new ArrayList<>();
//                            for (Map<String, Object> zszmKvMap : zszmKvList) {
//                                final String zszmDmKv = (String) zszmKvMap.get("ZSZM_DM");
//                                final String yxqqStr = (String) zszmKvMap.get("YXQQ");
//                                final String yxqzStr = (String) zszmKvMap.get("YXQZ");
//                                //判断有效期
//                                boolean yxq = false;
//                                if (!FtsCxsUtils.isNull(yxqqStr) && !FtsCxsUtils.isNull(yxqzStr) && !FtsCxsUtils.isNull(nowdate)) {
//                                    final Date yxqq = DateUtil.toDate(yxqqStr, "yyyy-MM-dd");
//                                    final Date yxqz = DateUtil.toDate(yxqzStr, "yyyy-MM-dd");
//                                    if (yxqq.compareTo(nowdate) <=0 && yxqz.compareTo(nowdate) >=0) {
//                                        yxq = true;
//                                    }
//                                }
//                                //只添加未重复子目的，期内有效的
//                                if (!keySet.contains(zszmDmKv) && yxq) {
//                                    fiterZszmKvList.add(zszmKvMap);
//                                    keySet.add(zszmDmKv);
//                                }
//                            }
//                            //把本级税务机关查得的合法结果集添加到list
//                            zszmOldList.addAll(fiterZszmKvList);
//                            //取出第一个
//                            final Map<String, Object> zszmFirstMap = zszmKvList.get(0);
//                            final String dgbz = (String) zszmFirstMap.get("DGBZ");
//                            //如果递归标志为N，不再上溯查找。否则，上溯查找并叠加结果集
//                            if (FtsCxsUtils.isNull(dgbz) || "N".equals(dgbz)) {
//                                break;
//                            }
//                        }
//                    }
//                    //转换部分数据项（针对语义相近的字段，采用宁可多塞的方式）
//                    if (!FtsCxsUtils.isNull(zszmOldList)) {
//                        for (Map<String, Object> map : zszmOldList) {
//                            final Map<String, Object> newMap = new HashMap<>();
//                            newMap.put("zszmDm", map.get("ZSZM_DM"));
//                            newMap.put("swjgDm", map.get("SWJG_DM"));
//                            newMap.put("xzqhszDm", map.get("XZQHSZ_DM"));
//                            newMap.put("uuid", map.get("UUID"));
//                            newMap.put("zsxmDm", map.get("ZSXM_DM"));
//                            newMap.put("zspmDm", map.get("ZSPM_DM"));
//                            newMap.put("sx", map.get("SX"));
//                            newMap.put("xx", map.get("XX"));
//                            newMap.put("jsbz1", map.get("JSBZ_1"));
//                            newMap.put("fdsl", map.get("FDSL"));
//                            newMap.put("yxqq", map.get("YXQQ"));
//                            newMap.put("yxqz", map.get("YXQZ"));
//                            final String zszmmc = FtsCxsUtils.getMcByDm("dm_gy_zszm", "zszmmc", (String) map.get("zszmDm"));
//                            if (isNull(zszmmc)) {
//                                continue;
//                            }
//                            newMap.put("zszmmc", zszmmc);
//                            newMap.put("caption", zszmmc);
//                            newMap.put("label", zszmmc);
//                            newMap.put("code", map.get("ZSZM_DM"));
//                            newMap.put("value", map.get("ZSZM_DM"));
//                            //计税标志
//                            final String jsbz1 = (String) map.get("JSBZ_1");
//                            if ("1".equals(jsbz1)) { //征收率
//                                newMap.put("sl1", map.get("XX"));//税率默认为下限
//                                newMap.put("sl", map.get("XX"));//税率默认为下限
//                                newMap.put("zsl", map.get("XX"));//征收率默认为下限
//                            } else if ("0".equals(jsbz1)) { //税率
//                                newMap.put("sl1", map.get("XX"));//税率默认为下限
//                                newMap.put("sl", map.get("XX"));//税率默认为下限
//                            } else if ("2".equals(jsbz1)) { //应税所得率
//                                newMap.put("yssdl", map.get("XX"));//应税所得率默认为下限
//                                newMap.put("xx", map.get("XX"));//应税所得率-下限
//                                newMap.put("sx", map.get("SX"));//应税所得率-上限
//                            }
//                            zszmNewList.add(newMap);
//                        }
//                    }
//                    //最后放入Map<List>中
//                    if (!FtsCxsUtils.isNull(zszmNewList) && !FtsCxsUtils.isNull(entry.getKey())) {
//                        listMap.put(entry.getKey(), zszmNewList);
//                    }
//                }
//            }
//        }
//        return listMap;
//    }

//    /**
//     * 获取车船税征收子目ListMap
//     * @param swjgDm 机关
//     * @param sksqq 属期起
//     * @param sksqz 属期止
//     * @return 返回值
//     */
//    public static Map<String, List<Map<String, Object>>> getZszmCcs(String swjgDm, Date sksqq, Date sksqz) {
//        final Map<String, List<Map<String, Object>>> listMap = new HashMap<>();
//
//        //税务机关、征收项目、征收品目 不能为空
//        if (FtsCxsUtils.isNull(swjgDm)) {
//            return listMap;
//        }
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = new ArrayList<>();
//        getSwjgList(bjsjSwjgList , swjgDm, new HashMap<>());
//        //获取车船税全量子目
//        final List<Map<String, Object>> allList = FtsCxsUtils.getGroupIndexDataOld("CS_GY_GLB_ZSZM_GXM", "10114");
//        //根据征收品目以及税务机关分组
//        final Map<String, Map<String, List<Map<String, Object>>>> zszmSwjgZspmMap = allList.stream()
//                .filter(f -> !FtsCxsUtils.isNull(f.get("ZSPM_DM")) && !FtsCxsUtils.isNull(f.get("SWJG_DM")) && !FtsCxsUtils.isNull(f.get("ZSZM_DM")))
//                .collect(Collectors.groupingBy(g -> (String) g.get("ZSPM_DM"), Collectors.groupingBy(g -> (String) g.get("SWJG_DM"))));
//        if (!FtsCxsUtils.isNull(zszmSwjgZspmMap)) {
//            //循环遍历征收品目信息
//            for (Map.Entry<String, Map<String, List<Map<String, Object>>>> entry : zszmSwjgZspmMap.entrySet()) {
//                //获取该品目税务机关分组信息
//                final Map<String, List<Map<String, Object>>> zszmSwjgMap = entry.getValue();
//                if (!FtsCxsUtils.isNull(zszmSwjgMap)) {
//                    //返回该品目包含集合-转换后
//                    final List<Map<String, Object>> zszmNewList = new ArrayList<>();
//                    //返回该品目包含集合-缓存中
//                    final List<Map<String, Object>> zszmOldList = new ArrayList<>();
//                    //逐级上查
//                    for (String bjsjSwjgDm : bjsjSwjgList) {
//                        //获取该品目集合
//                        List<Map<String, Object>> zszmKvList = zszmSwjgMap.get(bjsjSwjgDm);
//                        if (!FtsCxsUtils.isNull(zszmKvList)) {
//                            //过滤Set
//                            final Set<String> keySet = new HashSet<>();
//                            //只添加没有重复子目代码的到之前集合：即保证代码优先用下级的
//                            final List<Map<String, Object>> fiterZszmKvList = new ArrayList<>();
//                            for (Map<String, Object> zszmKvMap : zszmKvList) {
//                                final String zszmDmKv = (String) zszmKvMap.get("ZSZM_DM");
//                                final String yxqqStr = (String) zszmKvMap.get("YXQQ");
//                                final String yxqzStr = (String) zszmKvMap.get("YXQZ");
//                                //判断有效期
//                                boolean yxq = true;
//                                if (!FtsCxsUtils.isNull(yxqqStr) && !FtsCxsUtils.isNull(yxqzStr) && !FtsCxsUtils.isNull(sksqq) && !FtsCxsUtils.isNull(sksqz)) {
//                                    final Date yxqq = DateUtil.toDate(yxqqStr, "yyyy-MM-dd");
//                                    final Date yxqz = DateUtil.toDate(yxqzStr, "yyyy-MM-dd");
//                                    if (yxqq.compareTo(sksqz) > 0 || yxqz.compareTo(sksqq) < 0) {
//                                        yxq = false;
//                                    }
//                                }
//                                //只添加未重复子目的，期内有效的
//                                if (!keySet.contains(zszmDmKv) && yxq) {
//                                    fiterZszmKvList.add(zszmKvMap);
//                                    keySet.add(zszmDmKv);
//                                }
//                            }
//                            //把本级税务机关查得的合法结果集添加到list
//                            zszmOldList.addAll(fiterZszmKvList);
//                            //取出第一个
//                            final Map<String, Object> zszmFirstMap = zszmKvList.get(0);
//                            final String dgbz = (String) zszmFirstMap.get("DGBZ");
//                            //如果递归标志为N，不再上溯查找。否则，上溯查找并叠加结果集
//                            if (FtsCxsUtils.isNull(dgbz) || "N".equals(dgbz)) {
//                                break;
//                            }
//                        }
//                    }
//                    //转换部分数据项（针对语义相近的字段，采用宁可多塞的方式）
//                    if (!FtsCxsUtils.isNull(zszmOldList)) {
//                        for (Map<String, Object> map : zszmOldList) {
//                            final Map<String, Object> newMap = new HashMap<>();
//                            newMap.put("zszmDm", map.get("ZSZM_DM"));
//                            newMap.put("swjgDm", map.get("SWJG_DM"));
//                            newMap.put("xzqhszDm", map.get("XZQHSZ_DM"));
//                            newMap.put("uuid", map.get("UUID"));
//                            newMap.put("zsxmDm", map.get("ZSXM_DM"));
//                            newMap.put("zspmDm", map.get("ZSPM_DM"));
//                            newMap.put("sx", map.get("SX"));
//                            newMap.put("xx", map.get("XX"));
//                            newMap.put("jsbz1", map.get("JSBZ_1"));
//                            newMap.put("fdsl", map.get("FDSL"));
//                            newMap.put("yxqq", map.get("YXQQ"));
//                            newMap.put("yxqz", map.get("YXQZ"));
//                            final String zszmmc = FtsCxsUtils.getMcByDm("DM_GY_ZSZM", "ZSZMMC", (String) map.get("ZSZM_DM"));
//                            if (isNull(zszmmc)) {
//                                continue;
//                            }
//                            newMap.put("zszmmc", zszmmc);
//                            newMap.put("caption", zszmmc);
//                            newMap.put("label", zszmmc);
//                            newMap.put("code", map.get("ZSZM_DM"));
//                            newMap.put("value", map.get("ZSZM_DM"));
//                            //计税标志
//                            final String jsbz1 = (String) map.get("JSBZ_1");
//                            if ("1".equals(jsbz1)) { //征收率
//                                newMap.put("sl1", map.get("XX"));//税率默认为下限
//                                newMap.put("sl", map.get("XX"));//税率默认为下限
//                                newMap.put("zsl", map.get("XX"));//征收率默认为下限
//                            } else if ("0".equals(jsbz1)) { //税率
//                                newMap.put("sl1", map.get("XX"));//税率默认为下限
//                                newMap.put("sl", map.get("XX"));//税率默认为下限
//                            } else if ("2".equals(jsbz1)) { //应税所得率
//                                newMap.put("yssdl", map.get("XX"));//应税所得率默认为下限
//                                newMap.put("xx", map.get("XX"));//应税所得率-下限
//                                newMap.put("sx", map.get("SX"));//应税所得率-上限
//                            }
//                            zszmNewList.add(newMap);
//                        }
//                    }
//                    //最后放入Map<List>中
//                    if (!FtsCxsUtils.isNull(zszmNewList) && !FtsCxsUtils.isNull(entry.getKey())) {
//                        listMap.put(entry.getKey(), zszmNewList);
//                    }
//                }
//            }
//        }
//        return listMap;
//    }


//    /**
//     * 获取行政区划取值方式
//     * 财行税行政区划选择方式配置表增加配置：取纳税人登记信息中的生产经营地行政区划预填，在纳税人生产经营地所在地市内的行政区划可由纳税人自行选择。具体规则如下：
//     * 根据生产经营地行政区划获取上一级下的所有行政区划，
//     * 如果生产经营地行政区划不是最下级（为市级或者省级），则直接获取生产经营地下的所有行政区划供纳税人选择，
//     * 对于特殊情况：
//     *     市一级下面没有区县级的只展示当前市级行政区划即可，例如海南省儋州市，只展示儋州市；
//     *     区县级上级直接为省级市则暂时省级下的所有行政区划。
//     * 如果生产经营地行政区划为空则获取本省所有行政区划供纳税人选择。
//     * @param xzqhszDm xzqhszDm
//     * @param zgswskfjDm zgswskfjDm
//     * @return 返回值
//     */
//    public static Map<String, String> getXzqhQzfs(String xzqhszDm, String zgswskfjDm) {
//        final Map<String, String> returnMap = new HashMap<>();
//        //1省级(直辖市，计划单列市)，2市级，其他数据问题用1和省级行政区划兜底
//        String qzfs = "1";
//        //省级行政区划代码
//        String retrunXzqhDm = FtsCxsUtils.getShengXzqhszDmBySwjgDm(zgswskfjDm);
//        //计划单列市直接返回
//        if (PlsbGyUtils.checkIsJhdls(retrunXzqhDm)) {
//            returnMap.put("qzfs", qzfs);
//            returnMap.put("retrunXzqhDm", retrunXzqhDm);
//            return returnMap;
//        }
//        final Map<String, Object> bjXzqhMap = FtsCxsUtils.getMapByDm("DM_GY_XZQH", xzqhszDm);
//        if (!FtsCxsUtils.isNull(xzqhszDm) && !FtsCxsUtils.isNull(bjXzqhMap) && !FtsCxsUtils.isNull(bjXzqhMap.get("XZQHJC"))) {
//            final String xzqhjc = (String) bjXzqhMap.get("XZQHJC");
//            if ("1".equals(xzqhjc)) {
//                //如果生产经营地行政区划不是最下级（为市级或者省级），则直接获取生产经营地下的所有行政区划供纳税人选择，
//                qzfs = "1";
//                retrunXzqhDm = xzqhszDm;
//            } else if ("2".equals(xzqhjc)){
//                //如果生产经营地行政区划不是最下级（为市级或者省级），则直接获取生产经营地下的所有行政区划供纳税人选择，
//                //市一级下面没有区县级的只展示当前市级行政区划即可，例如海南省儋州市，只展示儋州市；
//                qzfs = "2";
//                retrunXzqhDm = xzqhszDm;
//            } else if ("3".equals(xzqhjc)){
//                //根据生产经营地行政区划获取上一级下的所有行政区划
//                final String sjxzqhszDm = (String) bjXzqhMap.get("SJXZQHSZ_DM");
//                final Map<String, Object> sjxzqhMap = FtsCxsUtils.getMapByDm("DM_GY_XZQH", sjxzqhszDm);
//                if (!FtsCxsUtils.isNull(sjxzqhszDm) && !FtsCxsUtils.isNull(sjxzqhMap) && !FtsCxsUtils.isNull(sjxzqhMap.get("XZQHJC"))) {
//                    final String sjxzqhjc = (String) sjxzqhMap.get("XZQHJC");
//                    if ("1".equals(sjxzqhjc)) {
//                        //区县级上级直接为省级市则暂时省级下的所有行政区划，（直辖市）
//                        qzfs = "1";
//                        retrunXzqhDm = sjxzqhszDm;
//                    } else if ("2".equals(sjxzqhjc)) {
//                        //根据生产经营地行政区划获取上一级下的所有行政区划
//                        qzfs = "2";
//                        retrunXzqhDm = sjxzqhszDm;
//                    }
//                }
//            }
//        }
//        returnMap.put("qzfs", qzfs);
//        returnMap.put("retrunXzqhDm", retrunXzqhDm);
//        return returnMap;
//    }

//    /**
//     * 获取行政区划树
//     * @param shiXzqhszDm 市级行政区划代码
//     * @return 返回值
//     */
//    public static List<Map<String, Object>> getXzqhListByShiXzqhszDm(String shiXzqhszDm)  {
//        final List<Map<String, Object>> returnList = new ArrayList<>();
//        //获取当前行政区划信息
//        final Map<String, Object> xzqhNewMap = FtsCxsUtils.getIndexData("DM_GY_XZQH", shiXzqhszDm);
//        //取出全量行政区划，3000多条
//        final List<Map<String, Object>> xzqhListTemp = FtsCxsUtils.getAllIndexData("DM_GY_XZQH");
//        //判空
//        if(!isNull(shiXzqhszDm) && !isNull(xzqhListTemp) && !isNull(xzqhNewMap)){
//            //先放入市级行政区划
//            final Map<String, Object> xzqhMapShi = new HashMap<>();
//            xzqhMapShi.put("value", xzqhNewMap.get("XZQHSZ_DM"));
//            xzqhMapShi.put("label", xzqhNewMap.get("XZQHMC"));
//            returnList.add(xzqhMapShi);
//            //根据上级行政区划分组
//            final Map<String, List<Map<String, Object>>> xzqhGroup = xzqhListTemp.stream()
//                    .filter(f -> !GYObjectUtils.isNull(f.get("SJXZQHSZ_DM"))).collect(Collectors.groupingBy(g -> (String) g.get("SJXZQHSZ_DM")));
//            final List<Map<String, Object>> xzqhQuAll = xzqhGroup.get(shiXzqhszDm);
//            if (!FtsCxsUtils.isNull(xzqhQuAll)) {
//                final List<Map<String, Object>> xzqhQuXianList = xzqhQuAll.stream().map(m -> {
//                    final Map<String, Object> xzqhMapTemp = new HashMap<>();
//                    xzqhMapTemp.put("value", m.get("XZQHSZ_DM"));
//                    xzqhMapTemp.put("label", m.get("XZQHMC"));
//                    return xzqhMapTemp;
//                }).collect(Collectors.toList());
//                xzqhMapShi.put("children", xzqhQuXianList);
//                xzqhMapShi.put("disabled", true);
//            }
//        }
//        return returnList;
//    }

    public static <T> T convertToJavaBean(String xml, Class<T> c) {
        T t = null;
        //        final JAXBContext context = JAXBContext.newInstance(c);
        //        final Unmarshaller unmarshaller = context.createUnmarshaller();
        //        t = (T) unmarshaller.unmarshal(new StringReader(xml));
        try {
            t = xml2Pojo(xml, c);
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return t;
    }
    @SuppressWarnings("unchecked")
    public static <T> T xml2Pojo(String xml, Class<T> clsPojo) throws JAXBException {
        final JAXBContext context = getJaxbContext(clsPojo);
        final Unmarshaller um = context.createUnmarshaller();
        final StreamSource xmlSource = new StreamSource(new StringReader(xml));
        @SuppressWarnings("rawtypes")
        final JAXBElement je1 = um.unmarshal(xmlSource, clsPojo);
        return (T) je1.getValue();
    }

    public static JAXBContext getJaxbContext(Class<?> clazz) throws JAXBException {
        JAXBContext jaxbContext = jaxbContexts.get(clazz);
        if (jaxbContext == null) {
            jaxbContext = JAXBContext.newInstance(clazz);
            final JAXBContext j = jaxbContexts.putIfAbsent(clazz, jaxbContext);
            if (j != null) {
                jaxbContext = j;
            }
        }
        return jaxbContext;
    }
}
