package com.css.znsb.nssb.job;

import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.nssb.service.skjn.ZnsbSkjnService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 缴款信息归集
 */
@Slf4j
@Component
public class JkxxGjByYearJob {

    @Resource
    private ZnsbSkjnService znsbSkjnService;

    /**
     * 缴款信息归集
     */
    @XxlJob("JkxxGjJobByYear")
    public void execute() {
        log.info("==========开始缴款信息归集==========");
        final String jobParam = XxlJobHelper.getJobParam();
        log.info("入参jobParam{}",jobParam);
        znsbSkjnService.JkxxGjJobByYear(jobParam);
        log.info("==========缴款信息归集完成========== ");
    }
}
