package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 购进农产品直接销售核定农产品增值税进项税额Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gjncpzjxshdncpzzsjxseGridlbVO", propOrder = { "xh", "cpmc1", "dqxsncpsl", "shsl1", "ncpgjsl", "shl", "qckcncpsl", "qcpjmj", "dqgjncpsl", "dqmj", "ncppjgmdj", "kcl", "dqyxdkncpjxse" })
@Getter
@Setter
public class GjncpzjxshdncpzzsjxseGridlbVO {
    /**
     * 序号
     */
    protected Integer xh;

    /**
     * 产品名称
     */
    protected String cpmc1;

    /**
     * 当期销售农产品数量吨
     */
    protected BigDecimal dqxsncpsl;

    /**
     * 损耗数量
     */
    protected BigDecimal shsl1;

    /**
     * 农产品购进数量
     */
    protected BigDecimal ncpgjsl;

    /**
     * 损耗率
     */
    protected BigDecimal shl;

    /**
     * 期初库存农产品数量
     */
    protected BigDecimal qckcncpsl;

    /**
     * 期初平均买价
     */
    protected BigDecimal qcpjmj;

    /**
     * 当期购进农产品数量
     */
    protected BigDecimal dqgjncpsl;

    /**
     * 当期买价元/吨
     */
    protected BigDecimal dqmj;

    /**
     * 农产品平均购买单价元/吨
     */
    protected BigDecimal ncppjgmdj;

    /**
     * 扣除率
     */
    protected BigDecimal kcl;

    /**
     * 当期允许抵扣农产品进项税额元
     */
    protected BigDecimal dqyxdkncpjxse;
}