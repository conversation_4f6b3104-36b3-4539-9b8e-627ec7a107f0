package com.css.znsb.nssb.job;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.css.znsb.nssb.service.zzsybnsrsb.plsb.ZzsybnsrYsbsjcjService;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ZzsybnsrYtsjlqJob {

    @Resource
    private ZzsybnsrYsbsjcjService ysbsjcjService;

    @XxlJob("zzsybnsrYtsjlqJob")
    public void execute() {
        log.info("开始执行增值税一般纳税人申报预填数据拉取定时任务");
        ysbsjcjService.retryGetYtsj();
        log.info("增值税一般纳税人申报预填数据拉取定时任务执行完成");
    }
}
