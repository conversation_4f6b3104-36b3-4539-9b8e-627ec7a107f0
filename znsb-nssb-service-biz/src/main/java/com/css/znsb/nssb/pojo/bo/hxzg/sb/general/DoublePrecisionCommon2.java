package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;


import com.css.znsb.nssb.pojo.bo.hxzg.gy.kxjsfzhq.DoublePrecision2;

/**
 * 
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.sbzs.sb.general
 * @file DoublePrecisionCommon2.java 创建时间:2018年7月27日上午9:26:37
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2018 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class DoublePrecisionCommon2 extends DoublePrecision2 {

    /**
     * 
     *@name    重写框架转换方法，当遇到null时直接返回
     *@description 相关说明
     *@time    创建时间:2018年7月27日上午9:34:11
     *@param num 需转换字段
     *@return 调用原方法返回
     *@throws Exception
     * <AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public String marshal(Double num) throws Exception {
        // TODO Auto-generated method stub
        if (num == null) {
            return null;
        }
        return super.marshal(num);
    }
}
