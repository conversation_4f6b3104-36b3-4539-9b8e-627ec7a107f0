package com.css.znsb.nssb.pojo.dto.common;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SbCtItemDTO implements Serializable {
    private static final long serialVersionUID = -4142461737777853336L;

    private String id;

    private String pid;

    private boolean showDefault;

    /**
     * 销售额
     */
    private BigDecimal xse;

    /**
     * 期初销售额
     */
    private BigDecimal qcXse;

    private String lx;

    private String xmmc;

    /**
     * 税额
     */
    private BigDecimal xxse;

    /**
     * 期初税额
     */
    private BigDecimal qcXxse;

    private String xmdm;

    /**
     * 提示信息
     */
    private String tips;

    /**
     * 下级项目列表
     */
    private List<SbCtItemDTO> items;

    /**
     * 税率
     */
    private BigDecimal sl;

    /**
     * 销售额有效标志
     */
    private boolean xseEnabled;

    /**
     * 税额有效标志
     */
    private boolean xxseEnabled;

    public SbCtItemDTO() {
    }

    public SbCtItemDTO(String id, String pid, String lx, String xmdm, String xmmc, boolean showDefault, BigDecimal xse,
                       BigDecimal xxse, BigDecimal sl, boolean xseEnabled, boolean xxseEnabled) {
        this.id = id;
        this.pid = pid;
        this.lx = lx;
        this.xmdm = xmdm;
        this.xmmc = xmmc;
        this.showDefault = showDefault;
        this.xse = xse;
        this.qcXse = xse;
        this.xxse = xxse;
        this.qcXxse = xxse;
        this.sl = sl;
        this.xseEnabled = xseEnabled;
        this.xxseEnabled = xxseEnabled;
    }
}
