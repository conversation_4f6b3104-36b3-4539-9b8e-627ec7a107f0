
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《消费税》酒类申报
 * 
 * <p>Java class for xfsJlsb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfsJlsb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="xfssb3" type="{http://www.chinatax.gov.cn/dataspec/}xfssb_jjjj"/>
 *         &lt;element name="xfssb3_fb1" type="{http://www.chinatax.gov.cn/dataspec/}bqzydjsejsbjjjj" minOccurs="0"/>
 *         &lt;element name="xfssb3_fb2" type="{http://www.chinatax.gov.cn/dataspec/}bqdsdjsejsbjjjj" minOccurs="0"/>
 *         &lt;element name="xfssb3_fb3" type="{http://www.chinatax.gov.cn/dataspec/}scjyqkbjjjj" minOccurs="0"/>
 *         &lt;element name="xfssb3_fb4" type="{http://www.chinatax.gov.cn/dataspec/}yhdzdjsjgbjqd" minOccurs="0"/>
 *         &lt;element name="xfssb3_fb5" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsemxb" minOccurs="0"/>
 *         &lt;element name="xfssb3_fb6" type="{http://www.chinatax.gov.cn/dataspec/}hznsqyxfsfpb" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfsJlsb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "xfssb3",
    "xfssb3Fb1",
    "xfssb3Fb2",
    "xfssb3Fb3",
    "xfssb3Fb4",
    "xfssb3Fb5",
    "xfssb3Fb6"
})
public class XfsJlsb
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected XfssbJjjj xfssb3;
    @XmlElement(name = "xfssb3_fb1", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqzydjsejsbjjjj xfssb3Fb1;
    @XmlElement(name = "xfssb3_fb2", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqdsdjsejsbjjjj xfssb3Fb2;
    @XmlElement(name = "xfssb3_fb3", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Scjyqkbjjjj xfssb3Fb3;
    @XmlElement(name = "xfssb3_fb4", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Yhdzdjsjgbjqd xfssb3Fb4;
    @XmlElement(name = "xfssb3_fb5", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqjmsemxb xfssb3Fb5;
    @XmlElement(name = "xfssb3_fb6", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Hznsqyxfsfpb xfssb3Fb6;

    /**
     * Gets the value of the xfssb3 property.
     * 
     * @return
     *     possible object is
     *     {@link XfssbJjjj }
     *     
     */
    public XfssbJjjj getXfssb3() {
        return xfssb3;
    }

    /**
     * Sets the value of the xfssb3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfssbJjjj }
     *     
     */
    public void setXfssb3(XfssbJjjj value) {
        this.xfssb3 = value;
    }

    /**
     * Gets the value of the xfssb3Fb1 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqzydjsejsbjjjj }
     *     
     */
    public Bqzydjsejsbjjjj getXfssb3Fb1() {
        return xfssb3Fb1;
    }

    /**
     * Sets the value of the xfssb3Fb1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqzydjsejsbjjjj }
     *     
     */
    public void setXfssb3Fb1(Bqzydjsejsbjjjj value) {
        this.xfssb3Fb1 = value;
    }

    /**
     * Gets the value of the xfssb3Fb2 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqdsdjsejsbjjjj }
     *     
     */
    public Bqdsdjsejsbjjjj getXfssb3Fb2() {
        return xfssb3Fb2;
    }

    /**
     * Sets the value of the xfssb3Fb2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqdsdjsejsbjjjj }
     *     
     */
    public void setXfssb3Fb2(Bqdsdjsejsbjjjj value) {
        this.xfssb3Fb2 = value;
    }

    /**
     * Gets the value of the xfssb3Fb3 property.
     * 
     * @return
     *     possible object is
     *     {@link Scjyqkbjjjj }
     *     
     */
    public Scjyqkbjjjj getXfssb3Fb3() {
        return xfssb3Fb3;
    }

    /**
     * Sets the value of the xfssb3Fb3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Scjyqkbjjjj }
     *     
     */
    public void setXfssb3Fb3(Scjyqkbjjjj value) {
        this.xfssb3Fb3 = value;
    }

    /**
     * Gets the value of the xfssb3Fb4 property.
     * 
     * @return
     *     possible object is
     *     {@link Yhdzdjsjgbjqd }
     *     
     */
    public Yhdzdjsjgbjqd getXfssb3Fb4() {
        return xfssb3Fb4;
    }

    /**
     * Sets the value of the xfssb3Fb4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Yhdzdjsjgbjqd }
     *     
     */
    public void setXfssb3Fb4(Yhdzdjsjgbjqd value) {
        this.xfssb3Fb4 = value;
    }

    /**
     * Gets the value of the xfssb3Fb5 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqjmsemxb }
     *     
     */
    public Bqjmsemxb getXfssb3Fb5() {
        return xfssb3Fb5;
    }

    /**
     * Sets the value of the xfssb3Fb5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqjmsemxb }
     *     
     */
    public void setXfssb3Fb5(Bqjmsemxb value) {
        this.xfssb3Fb5 = value;
    }

    /**
     * Gets the value of the xfssb3Fb6 property.
     * 
     * @return
     *     possible object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public Hznsqyxfsfpb getXfssb3Fb6() {
        return xfssb3Fb6;
    }

    /**
     * Sets the value of the xfssb3Fb6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public void setXfssb3Fb6(Hznsqyxfsfpb value) {
        this.xfssb3Fb6 = value;
    }

}
