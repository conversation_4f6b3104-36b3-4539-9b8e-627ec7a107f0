package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《汇总纳税企业通用传递单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_hznsqytycdd", propOrder = { "sbbhead", "hznsqytycddxxGrid", "hznsqytycddjxGrid", "hznsqytycddForm", "slxx" })
@XmlSeeAlso({ ZzssyyybnsrHznsqytycddywbw.ZzssyyybnsrHznsqytycdd.class })
@Getter
@Setter
public class ZzssyyybnsrHznsqytycdd {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    @XmlElement(nillable = true, required = true)
    protected HznsqytycddxxGrid hznsqytycddxxGrid;

    @XmlElement(nillable = true, required = true)
    protected HznsqytycddjxGrid hznsqytycddjxGrid;

    /**
     * 汇总纳税企业通用传递单
     */
    @XmlElement(nillable = true, required = true)
    protected HznsqytycddFormVO hznsqytycddForm;

    /**
     * 受理信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbslxxVO slxx;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "hznsqytycddjxGridlbVO" })
    @Getter
    @Setter
    public static class HznsqytycddjxGrid {
        @XmlElement(nillable = true, required = true)
        protected List<HznsqytycddjxGridlbVO> hznsqytycddjxGridlbVO;

        /**
         * Gets the value of the hznsqytycddjxGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the hznsqytycddjxGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getHznsqytycddjxGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link HznsqytycddjxGridlbVO}
         */
        public List<HznsqytycddjxGridlbVO> getHznsqytycddjxGridlbVO() {
            if (hznsqytycddjxGridlbVO == null) {
                hznsqytycddjxGridlbVO = new ArrayList<HznsqytycddjxGridlbVO>();
            }
            return this.hznsqytycddjxGridlbVO;
        }
    }

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "hznsqytycddxxGridlbVO" })
    @Getter
    @Setter
    public static class HznsqytycddxxGrid {
        @XmlElement(nillable = true, required = true)
        protected List<HznsqytycddxxGridlbVO> hznsqytycddxxGridlbVO;

        /**
         * Gets the value of the hznsqytycddxxGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the hznsqytycddxxGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getHznsqytycddxxGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link HznsqytycddxxGridlbVO}
         */
        public List<HznsqytycddxxGridlbVO> getHznsqytycddxxGridlbVO() {
            if (hznsqytycddxxGridlbVO == null) {
                hznsqytycddxxGridlbVO = new ArrayList<HznsqytycddxxGridlbVO>();
            }
            return this.hznsqytycddxxGridlbVO;
        }
    }
}