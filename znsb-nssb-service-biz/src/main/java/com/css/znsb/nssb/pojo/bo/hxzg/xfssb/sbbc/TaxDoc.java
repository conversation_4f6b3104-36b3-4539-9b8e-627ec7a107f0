
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for taxDoc complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="taxDoc">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;attribute name="bbh" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="xmlbh" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="xmlmc" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "taxDoc", namespace = "http://www.chinatax.gov.cn/dataspec/")
@XmlSeeAlso({
    Xfssbjjjjywbw.class,
    Bqzykcsejsbyywbw.class,
    Scjyqkbjjjjywbw.class,
    Scjyqkbxqcywbw.class,
    Bqjmsejsbdcywbw.class,
    Yxfjsczztrcclljtjbywbw.class,
    Xfssbylywbw.class,
    Bqdsdjsejsbdcywbw.class,
    Xfssbqtywbw.class,
    Xfssbtlywbw.class,
    Scqyddzgsnyrlykjptbzzszyfpmxbywbw.class,
    Yhdzdjsjgbjqdywbw.class,
    Bqzykcsejsbcpyywbw.class,
    Bqdsdjsejsbyywbw.class,
    Bqdsdjsejsbqtywbw.class,
    Bqjmsejsbtlywbw.class,
    Gphggjyxfsjsjgywbw.class,
    Bqjmsejsbcpyywbw.class,
    Bqdsdjsejsbjjjjywbw.class,
    Xfskspzmxbcpyywbw.class,
    Dksktzywbw.class,
    Xfssbxqcywbw.class,
    Xfssbcpyywbw.class,
    Xfssbylpfywbw.class,
    Dsdjskbgbcpyywbw.class,
    Xfssbywbw.class,
    Bqjmsemxbywbw.class,
    Bqzykcsejsbqtywbw.class,
    Zykcxfspzmxbqtywbw.class,
    Snyrlyscwghykcydtjbywbw.class,
    Syqywgsnyrlypzmxbywbw.class,
    Bqdsdjsejsbxqcywbw.class,
    Bqzydjsejsbjjjjywbw.class,
    Jypfqyyfxsmxqdywbw.class,
    Cpyxsmxbywbw.class,
    Bqdsdjsejsbtlywbw.class,
    Cpykcbgywbw.class,
    Scjyqkbqtywbw.class,
    Xfssbdcywbw.class,
    Scqyxshssnyrlywsqkmxbywbw.class,
    Jyscqyndxsmxbywbw.class
})
public abstract class TaxDoc
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlAttribute(required = true)
    protected String bbh;
    @XmlAttribute(required = true)
    protected String xmlbh;
    @XmlAttribute(required = true)
    protected String xmlmc;

    /**
     * Gets the value of the bbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBbh() {
        return bbh;
    }

    /**
     * Sets the value of the bbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBbh(String value) {
        this.bbh = value;
    }

    /**
     * Gets the value of the xmlbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXmlbh() {
        return xmlbh;
    }

    /**
     * Sets the value of the xmlbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXmlbh(String value) {
        this.xmlbh = value;
    }

    /**
     * Gets the value of the xmlmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXmlmc() {
        return xmlmc;
    }

    /**
     * Sets the value of the xmlmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXmlmc(String value) {
        this.xmlmc = value;
    }

}
