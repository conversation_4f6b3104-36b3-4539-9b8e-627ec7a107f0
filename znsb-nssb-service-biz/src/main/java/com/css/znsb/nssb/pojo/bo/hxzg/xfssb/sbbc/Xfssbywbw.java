
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《消费税申报》业务报文
 * 
 * <p>Java class for xfssbywbw complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfssbywbw">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.chinatax.gov.cn/dataspec/}taxDoc">
 *       &lt;sequence>
 *         &lt;element name="xfssbblxDm" type="{http://www.chinatax.gov.cn/dataspec/}xfssbblxDm"/>
 *         &lt;element name="xfsYlsb" type="{http://www.chinatax.gov.cn/dataspec/}xfsYlsb" minOccurs="0"/>
 *         &lt;element name="xfsYlpfsb" type="{http://www.chinatax.gov.cn/dataspec/}xfsYlpfsb" minOccurs="0"/>
 *         &lt;element name="xfsJlsb" type="{http://www.chinatax.gov.cn/dataspec/}xfsJlsb" minOccurs="0"/>
 *         &lt;element name="xfsCpylsb" type="{http://www.chinatax.gov.cn/dataspec/}xfsCpylsb" minOccurs="0"/>
 *         &lt;element name="xfsClsb" type="{http://www.chinatax.gov.cn/dataspec/}xfsClsb" minOccurs="0"/>
 *         &lt;element name="xfsQtsb" type="{http://www.chinatax.gov.cn/dataspec/}xfsQtsb" minOccurs="0"/>
 *         &lt;element name="xfsDcsb" type="{http://www.chinatax.gov.cn/dataspec/}xfsDcsb" minOccurs="0"/>
 *         &lt;element name="xfsTlsb" type="{http://www.chinatax.gov.cn/dataspec/}xfsTlsb" minOccurs="0"/>
 *         &lt;element name="xfsFjs" type="{http://www.chinatax.gov.cn/dataspec/}xfsfjssb" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfssbywbw", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "xfssbblxDm",
    "xfsYlsb",
    "xfsYlpfsb",
    "xfsJlsb",
    "xfsCpylsb",
    "xfsClsb",
    "xfsQtsb",
    "xfsDcsb",
    "xfsTlsb",
    "xfsFjs"
})
public class Xfssbywbw
    extends TaxDoc
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xfssbblxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected XfsYlsb xfsYlsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected XfsYlpfsb xfsYlpfsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected XfsJlsb xfsJlsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected XfsCpylsb xfsCpylsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected XfsClsb xfsClsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected XfsQtsb xfsQtsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected XfsDcsb xfsDcsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected XfsTlsb xfsTlsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Xfsfjssb xfsFjs;

    /**
     * Gets the value of the xfssbblxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXfssbblxDm() {
        return xfssbblxDm;
    }

    /**
     * Sets the value of the xfssbblxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXfssbblxDm(String value) {
        this.xfssbblxDm = value;
    }

    /**
     * Gets the value of the xfsYlsb property.
     * 
     * @return
     *     possible object is
     *     {@link XfsYlsb }
     *     
     */
    public XfsYlsb getXfsYlsb() {
        return xfsYlsb;
    }

    /**
     * Sets the value of the xfsYlsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfsYlsb }
     *     
     */
    public void setXfsYlsb(XfsYlsb value) {
        this.xfsYlsb = value;
    }

    /**
     * Gets the value of the xfsYlpfsb property.
     * 
     * @return
     *     possible object is
     *     {@link XfsYlpfsb }
     *     
     */
    public XfsYlpfsb getXfsYlpfsb() {
        return xfsYlpfsb;
    }

    /**
     * Sets the value of the xfsYlpfsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfsYlpfsb }
     *     
     */
    public void setXfsYlpfsb(XfsYlpfsb value) {
        this.xfsYlpfsb = value;
    }

    /**
     * Gets the value of the xfsJlsb property.
     * 
     * @return
     *     possible object is
     *     {@link XfsJlsb }
     *     
     */
    public XfsJlsb getXfsJlsb() {
        return xfsJlsb;
    }

    /**
     * Sets the value of the xfsJlsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfsJlsb }
     *     
     */
    public void setXfsJlsb(XfsJlsb value) {
        this.xfsJlsb = value;
    }

    /**
     * Gets the value of the xfsCpylsb property.
     * 
     * @return
     *     possible object is
     *     {@link XfsCpylsb }
     *     
     */
    public XfsCpylsb getXfsCpylsb() {
        return xfsCpylsb;
    }

    /**
     * Sets the value of the xfsCpylsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfsCpylsb }
     *     
     */
    public void setXfsCpylsb(XfsCpylsb value) {
        this.xfsCpylsb = value;
    }

    /**
     * Gets the value of the xfsClsb property.
     * 
     * @return
     *     possible object is
     *     {@link XfsClsb }
     *     
     */
    public XfsClsb getXfsClsb() {
        return xfsClsb;
    }

    /**
     * Sets the value of the xfsClsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfsClsb }
     *     
     */
    public void setXfsClsb(XfsClsb value) {
        this.xfsClsb = value;
    }

    /**
     * Gets the value of the xfsQtsb property.
     * 
     * @return
     *     possible object is
     *     {@link XfsQtsb }
     *     
     */
    public XfsQtsb getXfsQtsb() {
        return xfsQtsb;
    }

    /**
     * Sets the value of the xfsQtsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfsQtsb }
     *     
     */
    public void setXfsQtsb(XfsQtsb value) {
        this.xfsQtsb = value;
    }

    /**
     * Gets the value of the xfsDcsb property.
     * 
     * @return
     *     possible object is
     *     {@link XfsDcsb }
     *     
     */
    public XfsDcsb getXfsDcsb() {
        return xfsDcsb;
    }

    /**
     * Sets the value of the xfsDcsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfsDcsb }
     *     
     */
    public void setXfsDcsb(XfsDcsb value) {
        this.xfsDcsb = value;
    }

    /**
     * Gets the value of the xfsTlsb property.
     * 
     * @return
     *     possible object is
     *     {@link XfsTlsb }
     *     
     */
    public XfsTlsb getXfsTlsb() {
        return xfsTlsb;
    }

    /**
     * Sets the value of the xfsTlsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfsTlsb }
     *     
     */
    public void setXfsTlsb(XfsTlsb value) {
        this.xfsTlsb = value;
    }

    /**
     * Gets the value of the xfsFjs property.
     * 
     * @return
     *     possible object is
     *     {@link Xfsfjssb }
     *     
     */
    public Xfsfjssb getXfsFjs() {
        return xfsFjs;
    }

    /**
     * Sets the value of the xfsFjs property.
     * 
     * @param value
     *     allowed object is
     *     {@link Xfsfjssb }
     *     
     */
    public void setXfsFjs(Xfsfjssb value) {
        this.xfsFjs = value;
    }

}
