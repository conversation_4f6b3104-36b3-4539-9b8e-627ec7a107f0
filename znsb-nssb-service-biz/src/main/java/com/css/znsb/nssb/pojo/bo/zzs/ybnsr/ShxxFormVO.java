package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

/**
 * 审核信息
 *
 * <p>shxxFormVO complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="shxxFormVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence minOccurs="0">
 *         &lt;element name="shswry" type="{http://www.chinatax.gov.cn/dataspec/}swryxm"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "shxxFormVO", propOrder = { "shswry" })
public class ShxxFormVO {
    /**
     * 审核税务人员
     */
    protected String shswry;

    /**
     * 获取shswry属性的值。
     * <p>
     * 审核税务人员
     */
    public String getShswry() {
        return shswry;
    }

    /**
     * 设置shswry属性的值。
     */
    public void setShswry(String value) {
        this.shswry = value;
    }
}