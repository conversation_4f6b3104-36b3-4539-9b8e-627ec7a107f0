package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 成品油购销存情况明细表
 *
 * <p>cpygxcqkmxbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="cpygxcqkmxbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="cpygxcqkmxbGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}cpygxcqkmxbGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "cpygxcqkmxbGrid", propOrder = { "cpygxcqkmxbGridlbVO" })
public class CpygxcqkmxbGrid {
    @XmlElement(nillable = true, required = true)
    protected List<CpygxcqkmxbGridlbVO> cpygxcqkmxbGridlbVO;

    /**
     * Gets the value of the cpygxcqkmxbGridlbVO property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the cpygxcqkmxbGridlbVO property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getCpygxcqkmxbGridlbVO().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CpygxcqkmxbGridlbVO}
     */
    public List<CpygxcqkmxbGridlbVO> getCpygxcqkmxbGridlbVO() {
        if (cpygxcqkmxbGridlbVO == null) {
            cpygxcqkmxbGridlbVO = new ArrayList<CpygxcqkmxbGridlbVO>();
        }
        return this.cpygxcqkmxbGridlbVO;
    }
}