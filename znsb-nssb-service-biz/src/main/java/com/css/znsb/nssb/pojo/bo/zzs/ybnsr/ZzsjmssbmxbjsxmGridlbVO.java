package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税减免税申报明细表减税项目Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsjmssbmxbjsxmGridlbVO", propOrder = { "ewbhxh", "hmc", "swsxDm", "qcye", "bqfse", "bqydjse", "bqsjdjse", "qmye" })
@Getter
@Setter
public class ZzsjmssbmxbjsxmGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 税务事项代码
     */
    @XmlElement(nillable = true, required = true)
    protected String swsxDm;

    /**
     * 期初余额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qcye;

    /**
     * 本期发生额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqfse;

    /**
     * 本期应抵减税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqydjse;

    /**
     * 本期实际抵减税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqsjdjse;

    /**
     * 期末余额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmye;
}