package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《油气田企业增值税分配表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_yqtqyzzsfpbywbw", propOrder = { "zzssyyybnsrYqtqyzzsfpb" })
@Getter
@Setter
public class ZzssyyybnsrYqtqyzzsfpbywbw extends TaxDoc {
    /**
     * 油气田企业增值税分配表
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_yqtqyzzsfpb", required = true)
    @JSONField(name = "zzssyyybnsr_yqtqyzzsfpb")
    protected ZzssyyybnsrYqtqyzzsfpb zzssyyybnsrYqtqyzzsfpb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrYqtqyzzsfpb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrYqtqyzzsfpb {}
}