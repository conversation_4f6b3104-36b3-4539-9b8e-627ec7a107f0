package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 抵扣清单Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dkqdGridlbVO", propOrder = { "fpzlDm", "fphm", "kprq", "ysdwMc", "ysdwNsrsbh", "ysdwZgdfswjmc", "ysdwZgdfswjDm", "yfje", "jsdkYfje", "jsdkdjxse" })
@Getter
@Setter
public class DkqdGridlbVO {
    /**
     * 发票种类代码
     */
    protected String fpzlDm;

    /**
     * 发票号码
     */
    protected String fphm;

    /**
     * 开票日期
     */
    protected String kprq;

    /**
     * 运输单位名称
     */
    protected String ysdwMc;

    /**
     * 运输单位纳税人识别号
     */
    protected String ysdwNsrsbh;

    /**
     * 运输单位主管地方税务局名称
     */
    protected String ysdwZgdfswjmc;

    /**
     * 运输单位主管地方税务局代码
     */
    protected String ysdwZgdfswjDm;

    /**
     * 运费金额
     */
    protected BigDecimal yfje;

    /**
     * 允许计算抵扣运费金额
     */
    protected BigDecimal jsdkYfje;

    /**
     * 计算抵扣的进项税额
     */
    protected BigDecimal jsdkdjxse;
}