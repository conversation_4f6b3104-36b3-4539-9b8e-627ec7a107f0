package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《成本法核定农产品增值税进项税额计算表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbwGridlbVO", propOrder = { "dqyxdkncpjxse", "ncphyl", "dqzyywcb", "kcl", "cpmc1", "xh" })
@Getter
@Setter
public class ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO {
    /**
     * 当期允许抵扣农产品进项税额元
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal dqyxdkncpjxse;

    /**
     * 农产品耗用率
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ncphyl;

    /**
     * 当期主营业务成本元
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal dqzyywcb;

    /**
     * 扣除率
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kcl;

    /**
     * 产品名称
     */
    @XmlElement(nillable = true, required = true)
    protected String cpmc1;

    /**
     * 序号
     */
    @XmlElement(nillable = true, required = true)
    protected Integer xh;
}