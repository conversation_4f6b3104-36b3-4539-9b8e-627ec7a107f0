
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 生产经营情况表（其他）
 * 
 * <p>Java class for scjyqkbqt complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="scjyqkbqt">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="scjyqkbqtGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="scjyqkbqtGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBXfsscjyqkbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "scjyqkbqt", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "scjyqkbqtGrid"
})
public class Scjyqkbqt
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected ScjyqkbqtGrid scjyqkbqtGrid;

    /**
     * Gets the value of the scjyqkbqtGrid property.
     * 
     * @return
     *     possible object is
     *     {@link ScjyqkbqtGrid }
     *     
     */
    public ScjyqkbqtGrid getScjyqkbqtGrid() {
        return scjyqkbqtGrid;
    }

    /**
     * Sets the value of the scjyqkbqtGrid property.
     * 
     * @param value
     *     allowed object is
     *     {@link ScjyqkbqtGrid }
     *     
     */
    public void setScjyqkbqtGrid(ScjyqkbqtGrid value) {
        this.scjyqkbqtGrid = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="scjyqkbqtGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBXfsscjyqkbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "scjyqkbqtGridlb"
    })
    public static class ScjyqkbqtGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<SBXfsscjyqkbVO> scjyqkbqtGridlb;

        /**
         * Gets the value of the scjyqkbqtGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the scjyqkbqtGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getScjyqkbqtGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SBXfsscjyqkbVO }
         * 
         * 
         */
        public List<SBXfsscjyqkbVO> getScjyqkbqtGridlb() {
            if (scjyqkbqtGridlb == null) {
                scjyqkbqtGridlb = new ArrayList<SBXfsscjyqkbVO>();
            }
            return this.scjyqkbqtGridlb;
        }

    }

}
