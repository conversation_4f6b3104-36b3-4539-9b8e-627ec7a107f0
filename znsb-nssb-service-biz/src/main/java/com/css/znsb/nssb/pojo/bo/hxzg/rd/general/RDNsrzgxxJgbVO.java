
package com.css.znsb.nssb.pojo.bo.hxzg.rd.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 纳税人资格信息结果表
 * 
 * <p>Java class for RDNsrzgxxJgbVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="RDNsrzgxxJgbVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="rdpzuuid" type="{http://www.chinatax.gov.cn/dataspec/}rdpzuuid"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="lcslid" type="{http://www.chinatax.gov.cn/dataspec/}lcslid" minOccurs="0"/>
 *         &lt;element name="nsrzglxDm" type="{http://www.chinatax.gov.cn/dataspec/}nsrzglxDm"/>
 *         &lt;element name="yxqq" type="{http://www.chinatax.gov.cn/dataspec/}yxqq" minOccurs="0"/>
 *         &lt;element name="yxqz" type="{http://www.chinatax.gov.cn/dataspec/}yxqz" minOccurs="0"/>
 *         &lt;element name="sjzzrq" type="{http://www.chinatax.gov.cn/dataspec/}sjzzrq" minOccurs="0"/>
 *         &lt;element name="qxbz" type="{http://www.chinatax.gov.cn/dataspec/}qxbz" minOccurs="0"/>
 *         &lt;element name="wszg" type="{http://www.chinatax.gov.cn/dataspec/}wszg" minOccurs="0"/>
 *         &lt;element name="zfbz1" type="{http://www.chinatax.gov.cn/dataspec/}zfbz1"/>
 *         &lt;element name="zfrDm" type="{http://www.chinatax.gov.cn/dataspec/}zfrDm" minOccurs="0"/>
 *         &lt;element name="zfrq1" type="{http://www.chinatax.gov.cn/dataspec/}zfrq1" minOccurs="0"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq" minOccurs="0"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RDNsrzgxxJgbVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "rdpzuuid",
    "djxh",
    "lcslid",
    "nsrzglxDm",
    "yxqq",
    "yxqz",
    "sjzzrq",
    "qxbz",
    "wszg",
    "zfbz1",
    "zfrDm",
    "zfrq1",
    "lrrDm",
    "lrrq",
    "xgrDm",
    "xgrq",
    "sjgsdq"
})
public class RDNsrzgxxJgbVO
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String rdpzuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String lcslid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String nsrzglxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yxqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yxqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sjzzrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String wszg;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zfbz1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfrq1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;

    /**
     * Gets the value of the rdpzuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdpzuuid() {
        return rdpzuuid;
    }

    /**
     * Sets the value of the rdpzuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdpzuuid(String value) {
        this.rdpzuuid = value;
    }

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the lcslid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLcslid() {
        return lcslid;
    }

    /**
     * Sets the value of the lcslid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLcslid(String value) {
        this.lcslid = value;
    }

    /**
     * Gets the value of the nsrzglxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrzglxDm() {
        return nsrzglxDm;
    }

    /**
     * Sets the value of the nsrzglxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrzglxDm(String value) {
        this.nsrzglxDm = value;
    }

    /**
     * Gets the value of the yxqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqq() {
        return yxqq;
    }

    /**
     * Sets the value of the yxqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqq(String value) {
        this.yxqq = value;
    }

    /**
     * Gets the value of the yxqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqz() {
        return yxqz;
    }

    /**
     * Sets the value of the yxqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqz(String value) {
        this.yxqz = value;
    }

    /**
     * Gets the value of the sjzzrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjzzrq() {
        return sjzzrq;
    }

    /**
     * Sets the value of the sjzzrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjzzrq(String value) {
        this.sjzzrq = value;
    }

    /**
     * Gets the value of the qxbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQxbz() {
        return qxbz;
    }

    /**
     * Sets the value of the qxbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQxbz(String value) {
        this.qxbz = value;
    }

    /**
     * Gets the value of the wszg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWszg() {
        return wszg;
    }

    /**
     * Sets the value of the wszg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWszg(String value) {
        this.wszg = value;
    }

    /**
     * Gets the value of the zfbz1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfbz1() {
        return zfbz1;
    }

    /**
     * Sets the value of the zfbz1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfbz1(String value) {
        this.zfbz1 = value;
    }

    /**
     * Gets the value of the zfrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfrDm() {
        return zfrDm;
    }

    /**
     * Sets the value of the zfrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfrDm(String value) {
        this.zfrDm = value;
    }

    /**
     * Gets the value of the zfrq1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfrq1() {
        return zfrq1;
    }

    /**
     * Sets the value of the zfrq1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfrq1(String value) {
        this.zfrq1 = value;
    }

    /**
     * Gets the value of the lrrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * Sets the value of the lrrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * Gets the value of the lrrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * Sets the value of the lrrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * Gets the value of the xgrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * Sets the value of the xgrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * Gets the value of the xgrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * Sets the value of the xgrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * Gets the value of the sjgsdq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * Sets the value of the sjgsdq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

}
