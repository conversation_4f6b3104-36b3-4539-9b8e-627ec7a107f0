package com.css.znsb.nssb.service.sswszmLq.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.css.framework.dlfw.pojo.invoke.DlfwFileDownLoadDataDTO;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.Base64Utils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.nssb.mapper.sswszm.ZnsbNssbWszmkjjlMapper;
import com.css.znsb.nssb.pojo.domain.sswszm.ZnsbNssbJnskxxDO;
import com.css.znsb.nssb.pojo.domain.sswszm.ZnsbNssbSapjlbDO;
import com.css.znsb.nssb.pojo.domain.sswszm.ZnsbNssbWszmkjjlDO;
import com.css.znsb.nssb.pojo.dto.sap.SapHspzReqDTO;
import com.css.znsb.nssb.pojo.dto.sap.SapHspzResDTO;
import com.css.znsb.nssb.pojo.vo.dzzl.XzdzzlResVO;
import com.css.znsb.nssb.pojo.vo.sswszm.*;
import com.css.znsb.nssb.service.sswszmLq.*;
import com.css.znsb.nssb.utils.GYCastUtils;
import com.css.znsb.nssb.utils.GYObjectUtils;
import com.css.znsb.tzzx.feign.SapApi;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.Calendar.MINUTE;

/**
* <AUTHOR>
* @description 针对表【znsb_lzs_sstjdcqc(税收统计调查清册)】的数据库操作Service实现
* @createDate 2024-08-01 11:10:20
*/
@Slf4j
@Service
public class SswszmLqServiceImpl  implements SswszmLqService {

    @Resource
    private SjjhService sjjhService;

    @Resource
    private ZnsbNssbWszmkjjlService wszmkjjlService;

    @Resource
    private ZnsbNssbWszmkjjlMapper wszmkjjlMapper;

    // 新增注入：缴纳税款信息表 Service 与 企业信息 API
    @Resource
    private ZnsbNssbJnskxxService jnskxxService;

    @Resource
    private CompanyApi companyApi;

    // 新增注入：缴款书明细服务，用于查询征收代理方式代码
    @Resource
    private com.css.znsb.nssb.service.skjn.ZnsbNssbJksmxService jksmxService;

    // 新增注入：SAP调用记录服务
    @Resource
    private ZnsbNssbSapjlbService sapJlbService;

    @Resource
    private SapApi sapApi;

    @Override
    public List<JkxxVO> cxNsrJnskxxWss(SswszmKjRequestVO reqVO) {
        try {
            ZmWszjnskcxtjVO zmWszjnskcxtjVO = new ZmWszjnskcxtjVO();
            zmWszjnskcxtjVO.setDjxh(reqVO.getDjxh());
            zmWszjnskcxtjVO.setNsrsbh(reqVO.getNsrsbh());
            zmWszjnskcxtjVO.setSkssrqq(reqVO.getCxrqq());
            zmWszjnskcxtjVO.setSkssrqz(reqVO.getCxrqz());

            // 将List<String>转换为用英文逗号','分隔的字符串
            if (GyUtils.isNotNull(reqVO.getZsxmDm())) {
                String zsxmDmStr = Optional.ofNullable(reqVO.getZsxmDm())
                        .map(list -> String.join(",", list))
                        .orElse("");
                zmWszjnskcxtjVO.setZsxmdmj(zsxmDmStr);
            }

            // 组装请求参数
            WszjnskcxtjRequestVO wszjnskcxtjRequestVO = new WszjnskcxtjRequestVO();
            wszjnskcxtjRequestVO.setZmWszjnskcxtjVO(zmWszjnskcxtjVO);

            // 调用乐企接口--文书式完税证查询纳税人缴纳税款信息
            final SjjhDTO sjjhDTO = new SjjhDTO();
            sjjhDTO.setSjjhlxDm("CX00000001");
            sjjhDTO.setYwbm("LQWSSWSZM");
            sjjhDTO.setDjxh(String.valueOf(reqVO.getDjxh()));
            sjjhDTO.setNsrsbh(reqVO.getNsrsbh());
            sjjhDTO.setXzqhszDm(reqVO.getXzqhszDm());
            sjjhDTO.setYwuuid(GyUtils.getUuid());
            sjjhDTO.setBwnr(JsonUtils.toJson(wszjnskcxtjRequestVO));
            log.info("乐企缴款接口-乐企请求报文：{}", JsonUtils.toJson(sjjhDTO));

            CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);

            if (sjjhResult != null && sjjhResult.getData() != null) {
                WszjnskcxtjResponseVO wszjnskcxtjResponseVO = JsonUtils.toBean((String) sjjhResult.getData(), WszjnskcxtjResponseVO.class);
                if ("00".equals(wszjnskcxtjResponseVO.getReturncode())) {
                    ZmWszcxjnskxxVOGrid zmWszcxjnskxxVOGrid = wszjnskcxtjResponseVO.getZmWszcxjnskxxVOGrid();
                    List<ZmWszcxjnskxxVO> zmWszcxjnskxxVOList = Optional.ofNullable(zmWszcxjnskxxVOGrid)
                            .map(ZmWszcxjnskxxVOGrid::getZmWszcxjnskxxVOGridlb)
                            .orElse(Collections.emptyList());

                    if (!zmWszcxjnskxxVOList.isEmpty()) {
                        return groupAndAssembleJkxxVOList(zmWszcxjnskxxVOList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理纳税人缴纳税款信息时发生错误", e);
        }

        return Collections.emptyList();
    }

    /**
     * 将zmWszcxjnskxxVOList根据dzsphm分组，组装成jkxxVOList
     *
     * @param zmWszcxjnskxxVOList 原始数据列表
     * @return 分组后的jkxxVOList
     */
    public List<JkxxVO> groupAndAssembleJkxxVOList(List<ZmWszcxjnskxxVO> zmWszcxjnskxxVOList) {
        if (zmWszcxjnskxxVOList == null || zmWszcxjnskxxVOList.isEmpty()) {
            return Collections.emptyList();
        }

        // 根据dzsphm分组
        return zmWszcxjnskxxVOList.stream()
                .collect(Collectors.groupingBy(ZmWszcxjnskxxVO::getDzsphm))
                .entrySet().stream()
                .map(entry -> {
                    String dzsphm = entry.getKey();
                    List<ZmWszcxjnskxxVO> groupList = entry.getValue();

                    JkxxVO jkxxVO = new JkxxVO();
                    jkxxVO.setDzsphm(dzsphm);


                    if (GyUtils.isNotNull(groupList)) {
                        ZmWszcxjnskxxVO firstItem = groupList.get(0);
                        jkxxVO.setSkssqq(firstItem.getSkssqq());
                        jkxxVO.setSkssqz(firstItem.getSkssqz());
                        jkxxVO.setRkrq(firstItem.getRkrq());

                        // 计算实缴金额总和
                        BigDecimal totalSjje = groupList.stream()
                                .map(ZmWszcxjnskxxVO::getSjje)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        jkxxVO.setSjje(totalSjje);

                        // 转换并设置wszmList
                        List<WszmVO> wszmVOList = groupList.stream().map(zmWszcxjnskxxVO -> {
                            WszmVO wszmVO = new WszmVO();
                            // 映射相同字段
                            wszmVO.setDzsphm(zmWszcxjnskxxVO.getDzsphm());
                            wszmVO.setSksxDm(zmWszcxjnskxxVO.getSksxDm());
                            wszmVO.setZsuuid(zmWszcxjnskxxVO.getZsuuid());
                            wszmVO.setZsxmDm(zmWszcxjnskxxVO.getZsxmDm());
                            wszmVO.setZsxmmc(CacheUtils.dm2mc("dm_gy_zsxm", zmWszcxjnskxxVO.getZsxmDm()));
                            wszmVO.setZspmmc(CacheUtils.dm2mc("dm_gy_zspm", zmWszcxjnskxxVO.getZspmDm()));
                            wszmVO.setZspmDm(zmWszcxjnskxxVO.getZspmDm());
                            wszmVO.setSkssqq(zmWszcxjnskxxVO.getSkssqq());
                            wszmVO.setSkssqz(zmWszcxjnskxxVO.getSkssqz());
                            wszmVO.setSjje(zmWszcxjnskxxVO.getSjje());

                            return wszmVO;
                        }).collect(Collectors.toList());

                        jkxxVO.setWszmList(wszmVOList);
                    }

                    return jkxxVO;
                })
                .collect(Collectors.toList());
    }


    /**
     * 文书式完税证明开具
     * 1) 调用前：根据 djxh、skssqq、skssqz、zsxmdm、yxbz=Y 查询 znsb_nssb_wszmkjjl 表；
     * 若存在且 bz1=3，则将请求参数 wsscxtjVO.bz1 置为 "3"，否则置空。
     * 2) 调用后：将返回数据保存/更新至 znsb_nssb_wszmkjjl 表；
     * 若返回 bz1 为 "3"，则 cgbz 置为 "Y"，否则置为 "N"。
     * 3) 新增记录时：重试次数 cscs+1，下次执行时间 xczxsj=当前时间+1分钟，同时设置 yxbz="Y"。
     *
     * @param reqVO 开具请求参数
     * @return 返回包括 wssBh、fileKey、bz、bz1 的结果 Map
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> wssWszmKj(SswszmKjRequestVO reqVO) {
        Map<String, Object> resp = new HashMap<>();
        resp.put("code", "01");

        try {
            final WsscxtjVO wsscxtjVO = convertToWsscxtjVO(reqVO);

            // 计算合计金额
            BigDecimal hjje = BigDecimal.ZERO;
            if (reqVO.getWszmList() != null) {
                hjje = reqVO.getWszmList().stream()
                        .filter(jnskxxVO -> jnskxxVO.getSjje() != null)
                        .map(NsrJnskxxVO::getSjje)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 1. 调用前：根据djxh、skssqq、skssqz、zsxmdm、yxbz=Y查询是否存在记录，若存在且bz1=3，则将请求bz1置为3，否则置空
            handleBz1BeforeRequest(wsscxtjVO);

            // 调用乐企接口--文书式完税证开具
            CommonResult<Object> sjjhResult = callLqWssWszmKjInterface(reqVO, wsscxtjVO);

            if (isSjjhResultValid(sjjhResult)) {
                WsswszmKjResponseVO resVO = JsonUtils.toBean((String) sjjhResult.getData(), WsswszmKjResponseVO.class);
                log.info("文书式完税证明开具-乐企响应：code={}, message={}, wssBh={}, bz1={}",
                        Optional.ofNullable(resVO).map(WsswszmKjResponseVO::getReturncode).orElse(null),
                        Optional.ofNullable(resVO).map(WsswszmKjResponseVO::getReturnmsg).orElse(null),
                        Optional.ofNullable(resVO).map(WsswszmKjResponseVO::getWssBh).orElse(null),
                        Optional.ofNullable(resVO).map(WsswszmKjResponseVO::getBz1).orElse(null));

                if (resVO != null && "00".equals(resVO.getReturncode())) {
                    // 2. 调用后：保存或更新完税证明开具记录
                    String uuid = saveOrUpdateWszmKjjlRecord(wsscxtjVO, resVO, hjje);
                    if ("3".equals(resVO.getBz1())) {
                        resp.put("code", "00");
                        resp.put("wssBh", resVO.getWssBh());
                        resp.put("fileKey", resVO.getFileKey());
                        resp.put("bz", resVO.getBz());
                        resp.put("bz1", resVO.getBz1());
                        resp.put("uuid", uuid);
                        resp.put("hjje", hjje);
                        resp.put("zmkjsj", new Date());
                        resp.put("zmwjsl", 1);
                    } else {
                        resp.put("message", "完税证明开具失败");
                    }
                } else {
                    resp.put("message", "完税证明开具失败");
                }
            } else {
                log.error("完税证明开具失败,乐企接口返回空");
                resp.put("message", "完税证明开具失败,乐企接口返回空");
            }
        } catch (Exception e) {
            log.error("处理完税证明开具时发生错误", e);
            resp.put("message", "完税证明开具失败");
        }

        return resp;
    }

    /**
     * 处理请求前的bz1逻辑
     *
     * @param wsscxtjVO 请求参数
     */
    private void handleBz1BeforeRequest(WsscxtjVO wsscxtjVO) {
        final ZnsbNssbWszmkjjlDO queryDO = wszmkjjlMapper.queryWszmKjjl(wsscxtjVO);
        if (GyUtils.isNotNull(queryDO) && "3".equals(queryDO.getBz1())) {
            wsscxtjVO.setBz1("3");
        } else {
            wsscxtjVO.setBz1(null);
        }
    }

    /**
     * 调用乐企文书式完税证明开具接口
     *
     * @param reqVO     原始请求参数
     * @param wsscxtjVO 转换后的请求参数
     * @return 乐企接口返回结果
     */
    private CommonResult<Object> callLqWssWszmKjInterface(SswszmKjRequestVO reqVO, WsscxtjVO wsscxtjVO) {
        WsswszmKjRequestVO wsswszmKjRequestVO = new WsswszmKjRequestVO();
        wsswszmKjRequestVO.setWsscxtjVO(wsscxtjVO);
        final String reqJson = JsonUtils.toJson(wsswszmKjRequestVO);

        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("**********");
        sjjhDTO.setYwbm("LQWSSWSZM");
        sjjhDTO.setDjxh(String.valueOf(reqVO.getDjxh()));
        sjjhDTO.setNsrsbh(reqVO.getNsrsbh());
        sjjhDTO.setXzqhszDm(reqVO.getXzqhszDm());
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setBwnr(reqJson);
        log.info("文书式完税证明开具-请求报文：{}", JsonUtils.toJson(sjjhDTO));

        return sjjhService.saveSjjhJob(sjjhDTO);
    }

    /**
     * 验证数据交互结果是否有效
     *
     * @param sjjhResult 数据交互结果
     * @return 是否有效
     */
    private boolean isSjjhResultValid(CommonResult<Object> sjjhResult) {
        return GyUtils.isNotNull(sjjhResult) && GyUtils.isNotNull(sjjhResult.getData());
    }

    /**
     * 保存或更新完税证明开具记录
     *
     * @param wsscxtjVO 转换后的请求参数
     * @param resVO     响应结果
     * @param hjje      合计金额
     * @return 记录表的UUID
     */
    private String saveOrUpdateWszmKjjlRecord(WsscxtjVO wsscxtjVO, WsswszmKjResponseVO resVO, BigDecimal hjje) {
        final ZnsbNssbWszmkjjlDO queryDO = wszmkjjlMapper.queryWszmKjjl(wsscxtjVO);
        boolean exist = GyUtils.isNotNull(queryDO);

        ZnsbNssbWszmkjjlDO wszmkjjlDO = exist ? queryDO : buildNewWszmkjjlDO(wsscxtjVO);
        updateWszmkjjlDOFields(wszmkjjlDO, wsscxtjVO, resVO, hjje, exist);

        if (exist) {
            wszmkjjlService.updateById(wszmkjjlDO);
        } else {
            wszmkjjlService.save(wszmkjjlDO);
        }

        return wszmkjjlDO.getUuid();
    }

    /**
     * 构建新的完税证明开具记录
     *
     * @param wsscxtjVO 转换后的请求参数
     * @return 新的完税证明开具记录
     */
    private ZnsbNssbWszmkjjlDO buildNewWszmkjjlDO(WsscxtjVO wsscxtjVO) {
        ZnsbNssbWszmkjjlDO wszmkjjlDO = new ZnsbNssbWszmkjjlDO();
        wszmkjjlDO.setUuid(GyUtils.getUuid());
        wszmkjjlDO.setDjxh(wsscxtjVO.getDjxh());
        wszmkjjlDO.setSkssqq(wsscxtjVO.getSkssrqq());
        wszmkjjlDO.setSkssqz(wsscxtjVO.getSkssrqz());
        wszmkjjlDO.setZsxmdm(wsscxtjVO.getZsxmdmj());
        // 初始化重试次数为1
        wszmkjjlDO.setCscs(1);
        // 下次执行时间=当前时间+1分钟(因为重试次数为1，小于5)
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MINUTE, 1);
        wszmkjjlDO.setXczxsj(cal.getTime());
        wszmkjjlDO.setYxbz("Y");
        wszmkjjlDO.setKjrq(new Date());
        wszmkjjlDO.setKjfsDm(wsscxtjVO.getDygs());
        wszmkjjlDO.setSwsxdm("ws");
        return wszmkjjlDO;
    }

    /**
     * 更新完税证明开具记录字段
     *
     * @param wszmkjjlDO 完税证明开具记录
     * @param wsscxtjVO  转换后的请求参数
     * @param resVO      响应结果
     * @param hjje       合计金额
     * @param exist      是否已存在记录
     */
    private void updateWszmkjjlDOFields(ZnsbNssbWszmkjjlDO wszmkjjlDO, WsscxtjVO wsscxtjVO, WsswszmKjResponseVO resVO, BigDecimal hjje, boolean exist) {
        wszmkjjlDO.setWszmbh(resVO.getWssBh());
        wszmkjjlDO.setFilekey(resVO.getFileKey());
        wszmkjjlDO.setBz(resVO.getBz());
        wszmkjjlDO.setBz1(resVO.getBz1());
        wszmkjjlDO.setQqbw(JsonUtils.toJson(wsscxtjVO));
        // 设置实缴金额为合计金额
        wszmkjjlDO.setSjje(hjje);
        // 根据返回bz1确定成功标志
        final boolean success = "3".equals(resVO.getBz1());
        wszmkjjlDO.setCgbz(success ? "Y" : "N");

        // 如果是已存在的记录，重试次数+1
        if (exist) {
            int cscs = wszmkjjlDO.getCscs() + 1;
            wszmkjjlDO.setCscs(cscs);
            // 更新下次执行时间
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            // 如果重试次数大于5次，则下次执行时间增加半小时，否则增加一分钟
            if (cscs > 5) {
                cal.add(Calendar.MINUTE, 30);
            } else {
                cal.add(Calendar.MINUTE, 1);
            }
            wszmkjjlDO.setXczxsj(cal.getTime());

            // 如果当前cscs大于10次且bz1不为3则yxbz改为N
            if (cscs > 10 && !success) {
                wszmkjjlDO.setYxbz("N");
            }
        }
    }

    /**
     * 将SswszmKjRequestVO转换为WsscxtjVO
     *
     * @param reqVO 请求参数
     * @return 转换后的WsscxtjVO对象
     */
    public WsscxtjVO convertToWsscxtjVO(SswszmKjRequestVO reqVO) {
        WsscxtjVO wsscxtjVO = new WsscxtjVO();

        // 复制基本属性
        wsscxtjVO.setDjxh(reqVO.getDjxh());
        wsscxtjVO.setDygs(reqVO.getKjlx());
        wsscxtjVO.setKjsswszmyyDm("40");

        // 处理wszmList中的skssqq和skssrqz，获取最大最小值
        List<NsrJnskxxVO> wszmList = reqVO.getWszmList();
        if (GyUtils.isNotNull(wszmList) && !wszmList.isEmpty()) {
            // 获取skssqq的最小值和skssqz的最大值
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            List<Date> skssqqDates = new ArrayList<>();
            List<Date> skssqzDates = new ArrayList<>();

            for (NsrJnskxxVO nsrJnskxxVO : wszmList) {
                if (GyUtils.isNotNull(nsrJnskxxVO.getSkssqq())) {
                    skssqqDates.add(nsrJnskxxVO.getSkssqq());
                }
                if (GyUtils.isNotNull(nsrJnskxxVO.getSkssqz())) {
                    skssqzDates.add(nsrJnskxxVO.getSkssqz());
                }
            }

            // 设置最小的skssqq
            if (!skssqqDates.isEmpty()) {
                Date minSkssqq = Collections.min(skssqqDates);
                wsscxtjVO.setSkssrqq(minSkssqq);
            }

            // 设置最大的skssqz
            if (!skssqzDates.isEmpty()) {
                Date maxSkssqz = Collections.max(skssqzDates);
                wsscxtjVO.setSkssrqz(maxSkssqz);
            }

            // 收集所有不重复的zsxmDm，用英文','连接
            Set<String> zsxmDmSet = wszmList.stream()
                    .map(NsrJnskxxVO::getZsxmDm)
                    .filter(Objects::nonNull)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toSet());

            // 对zsxmDmSet进行排序，确保与查询逻辑一致
            List<String> sortedZsxmDmList = zsxmDmSet.stream()
                    .sorted()
                    .collect(Collectors.toList());
            String zsxmdmj = String.join(",", sortedZsxmDmList);
            wsscxtjVO.setZsxmdmj(zsxmdmj);
        }

        return wsscxtjVO;
    }


    /**
     * 重试完税证明开具
     * 根据djxh、skssqq、skssqz、zsxmdm、yxbz=Y查询znsb_nssb_wszmkjjl表
     * 如果bz1为3则为成功，无需重试，并将cgbz置为Y。
     * 如果bz1为1或2则进行重试，重新调用乐企接口查询纳税人缴纳税款信息，并修改原记录的重试次数cscs+1
     * 下次执行时间xczxsj在前5次为当前时间增加1分钟，后5次增加30分钟，重试最多执行10次
     * 如果当前cscs大于10次且bz1不为3则yxbz改为N
     *
     * @param djxh   登记序号
     * @param skssqq 税款所属期起
     * @param skssqz 税款所属期止
     * @param zsxmdm 征收项目代码
     * @return 重试结果
     */
    @Override
    public Map<String, Object> retryWssWszmKj(BigDecimal djxh, Date skssqq, Date skssqz, String zsxmdm) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", "01");

        try {
            // 构造查询条件
            LambdaQueryWrapper<ZnsbNssbWszmkjjlDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ZnsbNssbWszmkjjlDO::getDjxh, djxh)
                    .eq(ZnsbNssbWszmkjjlDO::getSkssqq, skssqq)
                    .eq(ZnsbNssbWszmkjjlDO::getSkssqz, skssqz)
                    .eq(ZnsbNssbWszmkjjlDO::getZsxmdm, zsxmdm)
                    .eq(ZnsbNssbWszmkjjlDO::getYxbz, "Y");

            ZnsbNssbWszmkjjlDO record = wszmkjjlService.getOne(queryWrapper);

            if (record == null) {
                result.put("message", "未找到对应的完税证明开具记录");
                return result;
            }

            // 如果bz1为3则为成功，无需重试，并将cgbz置为Y
            if ("3".equals(record.getBz1())) {
                record.setCgbz("Y");
                wszmkjjlService.updateById(record);
                result.put("code", "00");
                result.put("message", "记录已成功，无需重试");
                return result;
            }

            // 如果bz1为1或2则进行重试
            if ("1".equals(record.getBz1()) || "2".equals(record.getBz1())) {
                // 重新调用乐企接口查询纳税人缴纳税款信息
                // 这里需要根据业务逻辑构造请求参数
                // 由于缺少具体的请求参数构造逻辑，暂不实现实际的接口调用

                // 修改原记录的重试次数cscs+1
                int cscs = record.getCscs() + 1;
                record.setCscs(cscs);

                // 更新下次执行时间
                Calendar cal = Calendar.getInstance();
                cal.setTime(new Date());
                // 前5次为当前时间增加1分钟，后5次增加30分钟
                if (cscs <= 5) {
                    cal.add(MINUTE, 1);
                } else {
                    cal.add(MINUTE, 30);
                }
                record.setXczxsj(cal.getTime());

                // 如果当前cscs大于10次且bz1不为3则yxbz改为N
                if (cscs > 10 && !"3".equals(record.getBz1())) {
                    record.setYxbz("N");
                }

                // 更新记录
                wszmkjjlService.updateById(record);

                result.put("code", "00");
                result.put("message", "重试成功，重试次数:" + cscs);
                return result;
            }

            result.put("message", "bz1状态不支持重试");
        } catch (Exception e) {
            log.error("重试完税证明开具时发生错误", e);
            result.put("message", "重试失败");
        }

        return result;
    }

    public Map<String, Object> downLoadWszm(Map<String, Object> reqMap) {
        Map<String, Object> resMap = new HashMap<>();
        String fileName = "sswszm.pdf";

        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CX00000004");
        sjjhDTO.setYwbm("SSWSZMKL0001");
        sjjhDTO.setDjxh(GYCastUtils.cast2Str(reqMap.get("djxh")));
        sjjhDTO.setNsrsbh(GYCastUtils.cast2Str(reqMap.get("nsrsbh")));
        sjjhDTO.setXzqhszDm(GYCastUtils.cast2Str(reqMap.get("xzqhszDm")));
        sjjhDTO.setBwnr(GYObjectUtils.capitalizeJsonKeys(new Gson().toJson(reqMap)));
        CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
        String downloadStr = GYCastUtils.cast2Str(sjjhResult.getData());
        DlfwFileDownLoadDataDTO downLoadDataDTO = new DlfwFileDownLoadDataDTO();
        try {
            ObjectMapper mapper = new ObjectMapper();
            downLoadDataDTO = mapper.readValue(downloadStr, DlfwFileDownLoadDataDTO.class);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        int num = countSemicolons(GYCastUtils.cast2Str(reqMap.get("rediskey")));
        if (num >= 1) {
            fileName = "sswszm.zip";
        }
        byte[] bytes = Base64Utils.decodeBytes(downLoadDataDTO.getContent());

        resMap.put("fileName", fileName);
        resMap.put("fileBytes", bytes);
        return resMap;
    }

    public static int countSemicolons(String input) {
        Pattern pattern = Pattern.compile(";");
        Matcher matcher = pattern.matcher(input);

        int count = 0;
        while (matcher.find()) {
            count++;
        }

        return count;
    }

    @Override
    public void updateWszmRecordFilePath(String djxh, String fileKey, String filePath, String wjm, XzdzzlResVO resVO) {
        try {
            // 根据djxh和fileKey查找对应的完税证明记录
            ZnsbNssbWszmkjjlDO wszmRecord = wszmkjjlMapper.queryWszmKjjlByFileKey(
                    new BigDecimal(djxh), fileKey);

            if (GyUtils.isNotNull(wszmRecord)) {
                // 更新文件路径和文件名称
                wszmRecord.setWjlj(filePath);
                wszmRecord.setWjmc(wjm);
                wszmRecord.setSwjgdm(resVO.getSwjgDm());
                wszmkjjlService.updateById(wszmRecord);
                log.info("更新完税证明记录成功，登记序号: {}, fileKey: {}, 文件路径: {}, 文件名: {}", djxh, fileKey, filePath, wjm);
            } else {
                log.info("未找到对应的完税证明记录，登记序号: {}, fileKey: {}", djxh, fileKey);
            }
        } catch (Exception e) {
            log.error("更新完税证明记录文件路径失败，登记序号: {}, fileKey: {}, 文件路径: {}, 文件名: {}", djxh, fileKey, filePath, wjm, e);
        }
    }

    @Override
    public void hspzToSap() {
        log.info("开始执行SAP划税凭证处理");
        
        try {
            // 获取当月时间范围（用于过滤rkrq）
            Date[] currentMonth = currentMonthFirstLast();
            Date currentMonthStart = DateUtils.toDate("2022-01-01","yyyy-MM-dd"); //currentMonth[0];
            Date currentMonthEnd = DateUtils.toDate("2022-12-31","yyyy-MM-dd");//currentMonth[1];



            // 1. 获取企业列表和配置数据
            List<Map<String, Object>> kmbmglbList = CacheUtils.getTableData("dm_znsb_kmbmglb");
            List<Map<String, Object>> zsxmKmdzList = CacheUtils.getTableData("dm_znsb_zsxmkmdzb");
            
            if (kmbmglbList == null || kmbmglbList.isEmpty()) {
                log.info("企业列表为空，无法处理SAP划税凭证");
                return;
            }
            
            if (zsxmKmdzList == null || zsxmKmdzList.isEmpty()) {
                log.info("征收项目科目对照表为空，无法处理SAP划税凭证");
                return;
            }
            
            // 获取企业列表中的nsrsbh
            Set<String> nsrsbhSet = kmbmglbList.stream()
                .map(qy -> GYCastUtils.cast2Str(qy.get("nsrsbh")))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
            
            log.info("获取到{}家企业需要处理SAP划税凭证", nsrsbhSet.size());
            
            // 2. 根据nsrsbh查询缴纳税款信息
            LambdaQueryWrapper<ZnsbNssbJnskxxDO> jnskxxQuery = new LambdaQueryWrapper<>();
            jnskxxQuery.in(ZnsbNssbJnskxxDO::getNsrsbh, nsrsbhSet)
                    .ge(ZnsbNssbJnskxxDO::getRkrq, currentMonthStart)
                    .le(ZnsbNssbJnskxxDO::getRkrq, currentMonthEnd)
                    .eq(ZnsbNssbJnskxxDO::getSfXrSaphspzbz, "N")
                    .isNotNull(ZnsbNssbJnskxxDO::getZsdlfsDm);
            
            List<ZnsbNssbJnskxxDO> allJnskxxList = jnskxxService.list(jnskxxQuery);
            log.info("查询到{}条未处理的缴纳税款信息", allJnskxxList.size());
            
            if (allJnskxxList.isEmpty()) {
                log.info("没有需要处理的缴纳税款信息");
                return;
            }
            
            // 3. 按nsrsbh分组
            Map<String, List<ZnsbNssbJnskxxDO>> nsrsbhGroupMap = allJnskxxList.stream()
                .collect(Collectors.groupingBy(ZnsbNssbJnskxxDO::getNsrsbh));
            
            log.info("按nsrsbh分组后共{}个企业组", nsrsbhGroupMap.size());
            
            // 4. 循环处理每个企业的数据
            int processedCompanyCount = 0;
            int totalProcessedRecords = 0;
            
            for (Map.Entry<String, List<ZnsbNssbJnskxxDO>> nsrsbhEntry : nsrsbhGroupMap.entrySet()) {
                String nsrsbh = nsrsbhEntry.getKey();
                List<ZnsbNssbJnskxxDO> companyJnskxxList = nsrsbhEntry.getValue();
                
                try {
                    log.info("开始处理企业[{}]的{}条缴纳税款信息", nsrsbh, companyJnskxxList.size());
                    
                    // 按skssqq和skssqz分组
                    Map<String, List<ZnsbNssbJnskxxDO>> periodGroupMap = companyJnskxxList.stream()
                        .collect(Collectors.groupingBy(jnskxx -> {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            String skssqq = jnskxx.getSkssqq() != null ? sdf.format(jnskxx.getSkssqq()) : "";
                            String skssqz = jnskxx.getSkssqz() != null ? sdf.format(jnskxx.getSkssqz()) : "";
                            return skssqq + "_" + skssqz;
                        }));
                    
                    log.info("企业[{}]按税款所属期分组后共{}个期间组", nsrsbh, periodGroupMap.size());
                    
                    // 处理每个期间组
                    for (Map.Entry<String, List<ZnsbNssbJnskxxDO>> periodEntry : periodGroupMap.entrySet()) {
                        String periodKey = periodEntry.getKey();
                        List<ZnsbNssbJnskxxDO> periodJnskxxList = periodEntry.getValue();
                        
                        try {
                            log.info("处理企业[{}]期间[{}]的{}条记录", nsrsbh, periodKey, periodJnskxxList.size());
                            
                            // 调用分组处理方法
                            processGroupedWszmToHspz(periodJnskxxList, kmbmglbList, zsxmKmdzList);
                            totalProcessedRecords += periodJnskxxList.size();
                            
                        } catch (Exception e) {
                            log.error("处理企业[{}]期间[{}]的SAP划税凭证失败", nsrsbh, periodKey, e);
                        }
                    }
                    
                    processedCompanyCount++;
                    
                } catch (Exception e) {
                    log.error("处理企业[{}]的SAP划税凭证失败", nsrsbh, e);
                }
            }
            
            log.info("SAP划税凭证处理完成，处理企业{}家，处理记录{}条", processedCompanyCount, totalProcessedRecords);
            
        } catch (Exception e) {
            log.error("执行SAP划税凭证处理失败", e);
        }
    }

    /**
     * 文书式完税证-缴纳税款信息批量查询并落库
     * 入参格式：
     * - "YYYY-YYYY;djxh"：按年度区间逐年查询；
     * - "YYYY;djxh"：按单个年度查询；
     * - "" 或 ";djxh"：按当前月份查询；
     * - 若 djxh 为空，则对全量企业循环调用。
     * 实现步骤：
     * 1) 解析年度/区间生成查询起止日期；
     * 2) 根据是否传入 djxh 获取企业基本信息（nsrsbh、xzqhszDm、djxh）；
     * 3) 调用乐企接口“文书式完税证查询纳税人缴纳税款信息”；
     * 4) 将返回的明细数据落库到 znsb_nssb_jnskxx。
     * 返回：code、savedCount、companyCount、periodCount。
     *
     * @return 处理结果
     */
    @Override
    public Map<String, Object> monthlyCollectWszmInfo() {
        Map<String, Object> result = new HashMap<>();
        int totalProcessed = 0;
        int totalCreated = 0;
        int totalDownloaded = 0;
        
        try {
            log.info("开始执行每月归集完税证明信息任务");

            // 获取当前年份
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            String currentYearStr = String.valueOf(currentYear);

            // 第一步：调用现有的syncWssJnskxxAndSave方法，查询本年缴纳税款信息并存入znsb_nssb_jnskxx表
            log.info("步骤1：开始归集本年缴纳税款信息，年份：{}", currentYearStr);
            Map<String, Object> syncResult = syncWssJnskxxAndSave(currentYearStr);

            if (!"00".equals(syncResult.get("code"))) {
                result.put("code", "01");
                result.put("message", "归集缴纳税款信息失败：" + syncResult.get("message"));
                return result;
            }

            log.info("步骤1完成，归集缴纳税款信息：{}", syncResult.get("savedCount"));

            // 第二步：查询本年的缴纳税款信息，并过滤出rkrq在本月范围内的记录，为每条记录检查或创建完税证明记录
            log.info("步骤2：开始检查并创建完税证明记录");

            // 获取当月时间范围（用于过滤rkrq）
            Date[] currentMonth = currentMonthFirstLast();
            Date currentMonthStart = currentMonth[0];
            Date currentMonthEnd = currentMonth[1];

            // 获取本年时间范围（用于查询skssqq和skssqz）
            Date yearStart = firstDayOfYear(currentYear);
            Date yearEnd = lastDayOfYear(currentYear);

            // 查询本年的缴纳税款信息
            LambdaQueryWrapper<ZnsbNssbJnskxxDO> jnskxxQuery = new LambdaQueryWrapper<>();
            jnskxxQuery.ge(ZnsbNssbJnskxxDO::getSkssqq, yearStart)
                .le(ZnsbNssbJnskxxDO::getSkssqz, yearEnd);

            List<ZnsbNssbJnskxxDO> allJnskxxList = jnskxxService.list(jnskxxQuery);
            log.info("查询到本年缴纳税款信息{}条", allJnskxxList.size());

            // 过滤出rkrq在本月范围内的记录
            List<ZnsbNssbJnskxxDO> jnskxxList = allJnskxxList.stream()
                .filter(jnskxx -> jnskxx.getRkrq() != null &&
                    !jnskxx.getRkrq().before(currentMonthStart) &&
                    !jnskxx.getRkrq().after(currentMonthEnd))
                .collect(Collectors.toList());
            log.info("过滤出rkrq在本月范围内的缴纳税款信息{}条", jnskxxList.size());
            
            // 为每条缴纳税款信息检查或创建完税证明记录
            for (ZnsbNssbJnskxxDO jnskxx : jnskxxList) {
                try {
                    totalProcessed++;
                    
                    // 检查是否已存在完税证明记录
                     LambdaQueryWrapper<ZnsbNssbWszmkjjlDO> wszmQuery = new LambdaQueryWrapper<>();
                     wszmQuery.eq(ZnsbNssbWszmkjjlDO::getDjxh, jnskxx.getDjxh())
                             .eq(ZnsbNssbWszmkjjlDO::getSkssqq, jnskxx.getSkssqq())
                             .eq(ZnsbNssbWszmkjjlDO::getSkssqz, jnskxx.getSkssqz())
                             .eq(ZnsbNssbWszmkjjlDO::getZsxmdm, jnskxx.getZsxmDm())
                             .eq(ZnsbNssbWszmkjjlDO::getBz1, "3")
                             .eq(ZnsbNssbWszmkjjlDO::getYxbz, "Y");
                    
                    ZnsbNssbWszmkjjlDO existRecord = wszmkjjlMapper.selectOne(wszmQuery);
                    
                    if (existRecord == null) {
                        // 不存在记录，调用开具接口
                         log.info("为zsuuid:{}创建完税证明记录", jnskxx.getZsuuid());
                         
                         SswszmKjRequestVO reqVO = new SswszmKjRequestVO();
                         reqVO.setDjxh(new BigDecimal(jnskxx.getZsuuid()));
                         
                         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                         reqVO.setCxrqq(sdf.format(jnskxx.getSkssqq()));
                         reqVO.setCxrqz(sdf.format(jnskxx.getSkssqz()));
                         reqVO.setZsxmDm(Arrays.asList(jnskxx.getZsxmDm()));
                        
                        Map<String, Object> kjResult = wssWszmKj(reqVO);
                        if ("00".equals(kjResult.get("code"))) {
                            totalCreated++;
                            existRecord = wszmkjjlMapper.selectOne(wszmQuery); // 重新查询创建的记录
                        }
                    }
                    
                    // 第三步：检查文件路径，如果为空则下载文件
                    if (existRecord != null && (GyUtils.isNull(existRecord.getWjlj()) || existRecord.getWjlj().trim().isEmpty())) {
                        log.info("为zsuuid:{}下载完税证明文件", jnskxx.getZsuuid());
                         
                         Map<String, Object> reqMap = new HashMap<>();
                         reqMap.put("djxh", jnskxx.getZsuuid());
                         reqMap.put("filekey", existRecord.getFilekey());
                        
                        Map<String, Object> downloadResult = downLoadWszm(reqMap);
                        if ("00".equals(downloadResult.get("code"))) {
                            totalDownloaded++;
                        }
                    }
                    
                } catch (Exception e) {
                     log.error("处理zsuuid:{}的完税证明记录失败", jnskxx.getZsuuid(), e);
                 }
            }
            
            result.put("code", "00");
            result.put("message", "每月归集完税证明信息完成");
            result.put("jnskxxSavedCount", syncResult.get("savedCount"));
            result.put("totalProcessed", totalProcessed);
            result.put("wszmCreated", totalCreated);
            result.put("fileDownloaded", totalDownloaded);

//            // 新增：批量处理SAP划税凭证（处理之前可能遗漏的记录）
//            log.info("步骤4：开始批量处理SAP划税凭证");
//            try {
//                wszmHspzService.batchProcessWszmToHspz();
//                log.info("批量处理SAP划税凭证完成");
//            } catch (Exception sapBatchEx) {
//                log.error("批量处理SAP划税凭证失败", sapBatchEx);
//                // SAP批量处理失败不影响主流程
//            }

            log.info("每月归集完税证明信息任务完成，处理{}条记录，创建{}个完税证明，下载{}个文件",
                    totalProcessed, totalCreated, totalDownloaded);

        } catch (Exception e) {
            log.error("每月归集完税证明信息任务执行失败", e);
            result.put("code", "01");
            result.put("message", "每月归集完税证明信息失败：" + e.getMessage());
            result.put("totalProcessed", totalProcessed);
            result.put("wszmCreated", totalCreated);
            result.put("fileDownloaded", totalDownloaded);
        }
        
        return result;
    }



    @Override
    public Map<String, Object> syncWssJnskxxAndSave(String yearDjxhParam) {
        Map<String, Object> resp = new HashMap<>();
        resp.put("code", "01");
        int savedCount = 0;
        int companyCount = 0;
        int periodCount = 0;
        try {
            String yearsPart = null;
            String djxhPart = null;
            if (GyUtils.isNotNull(yearDjxhParam)) {
                String[] parts = yearDjxhParam.split(";");
                if (parts.length >= 1 && GyUtils.isNotNull(parts[0])) {
                    yearsPart = parts[0].trim();
                }
                if (parts.length >= 2 && GyUtils.isNotNull(parts[1])) {
                    djxhPart = parts[1].trim();
                }
            }

            List<Date[]> periods = resolvePeriods(yearsPart);
            periodCount = periods.size();

            List<CompanyBasicInfoDTO> companies = new ArrayList<>();
            if (GyUtils.isNull(djxhPart)) {
                CommonResult<List<CompanyInfoResDTO>> allCompany = companyApi.getAllCompanyInfo();
                if (allCompany != null && allCompany.getData() != null) {
                    companies = allCompany.getData().stream().map(ci -> {
                        CompanyBasicInfoDTO dto = new CompanyBasicInfoDTO();
                        dto.setDjxh(ci.getDjxh());
                        dto.setNsrsbh(ci.getNsrsbh());
                        dto.setXzqhszDm(ci.getXzqhszDm());
                        return dto;
                    }).collect(Collectors.toList());
                }
            } else {
                CommonResult<CompanyBasicInfoDTO> base = companyApi.basicInfo(djxhPart, djxhPart);
                if (base != null && base.getData() != null) {
                    companies = Collections.singletonList(base.getData());
                }
            }
            companyCount = companies.size();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (CompanyBasicInfoDTO company : companies) {
                if (GyUtils.isNull(company) || GyUtils.isNull(company.getDjxh()) || GyUtils.isNull(company.getNsrsbh())) {
                    continue;
                }
                for (Date[] p : periods) {
                    SswszmKjRequestVO reqVO = new SswszmKjRequestVO();
                    reqVO.setDjxh(new BigDecimal(company.getDjxh()));
                    reqVO.setNsrsbh(company.getNsrsbh());
                    reqVO.setXzqhszDm(company.getXzqhszDm());
                    reqVO.setCxrqq(sdf.format(p[0]));
                    reqVO.setCxrqz(sdf.format(p[1]));

                    List<ZmWszcxjnskxxVO> detailList = callWssJnskxx(reqVO);
                    savedCount += saveJnskxxList(detailList,reqVO);
                }
            }

            resp.put("code", "00");
            resp.put("savedCount", savedCount);
            resp.put("companyCount", companyCount);
            resp.put("periodCount", periodCount);
        } catch (Exception e) {
            log.error("文书式缴款信息查询并落库失败", e);
            resp.put("message", "执行失败");
        }
        return resp;
    }

    /**
     * 解析年度字符串，生成查询起止日期列表
     *
     * @param yearsPart 年份字符串：YYYY、YYYY-YYYY，或为空
     * @return 每个元素为 [起, 止] 的 Date 数组
     */
    private List<Date[]> resolvePeriods(String yearsPart) {
        List<Date[]> list = new ArrayList<>();
        if (GyUtils.isNull(yearsPart)) {
            list.add(currentMonthFirstLast());
            return list;
        }
        if (yearsPart.contains("-")) {
            String[] arr = yearsPart.split("-");
            try {
                int start = Integer.parseInt(arr[0].trim());
                int end = Integer.parseInt(arr[1].trim());
                if (start > end) {
                    int t = start;
                    start = end;
                    end = t;
                }
                for (int y = start; y <= end; y++) {
                    list.add(new Date[]{firstDayOfYear(y), lastDayOfYear(y)});
                }
            } catch (Exception e) {
                log.info("年份区间解析失败：{}", yearsPart, e);
                list.add(currentMonthFirstLast());
            }
        } else {
            try {
                int year = Integer.parseInt(yearsPart.trim());
                list.add(new Date[]{firstDayOfYear(year), lastDayOfYear(year)});
            } catch (Exception e) {
                log.info("年份解析失败：{}", yearsPart, e);
                list.add(currentMonthFirstLast());
            }
        }
        return list;
    }

    /**
     * 当月第一天与最后一天
     */
    private Date[] currentMonthFirstLast() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, 1);
        setTimeToStart(c);
        Date start = c.getTime();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        setTimeToEnd(c);
        Date end = c.getTime();
        return new Date[]{start, end};
    }

    /**
     * 指定年份第一天
     */
    private Date firstDayOfYear(int year) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, Calendar.JANUARY);
        c.set(Calendar.DAY_OF_MONTH, 1);
        setTimeToStart(c);
        return c.getTime();
    }

    /**
     * 指定年份最后一天
     */
    private Date lastDayOfYear(int year) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, Calendar.DECEMBER);
        c.set(Calendar.DAY_OF_MONTH, 31);
        setTimeToEnd(c);
        return c.getTime();
    }

    private void setTimeToStart(Calendar c) {
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
    }

    private void setTimeToEnd(Calendar c) {
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
    }

    /**
     * 调用乐企接口-文书式完税证查询纳税人缴纳税款信息，返回明细列表
     * 优化：调用两次接口，一次DbtsBz为N，一次为Y，并将DbtsBz标志添加到返回结果中
     *
     * @param reqVO 查询参数
     * @return 明细列表
     */
    private List<ZmWszcxjnskxxVO> callWssJnskxx(SswszmKjRequestVO reqVO) {
        List<ZmWszcxjnskxxVO> allResults = new ArrayList<>();

        // 调用两次接口：一次DbtsBz为N，一次为Y
        String[] dbtsBzValues = {"N", "Y"};

        for (String dbtsBz : dbtsBzValues) {
            try {
                log.info("开始调用文书式缴款信息查询接口，DbtsBz: {}", dbtsBz);

                List<ZmWszcxjnskxxVO> results = callWssJnskxxWithDbtsBz(reqVO, dbtsBz);

                // 为每个结果设置DbtsBz标志
                results.forEach(vo -> vo.setDbtsBz(dbtsBz));

                allResults.addAll(results);

                log.info("DbtsBz={}的查询完成，获取到{}条记录", dbtsBz, results.size());

            } catch (Exception e) {
                log.error("调用文书式缴款信息查询异常，DbtsBz: {}", dbtsBz, e);
            }
        }

        log.info("文书式缴款信息查询总计完成，共获取{}条记录", allResults.size());
        return allResults;
    }

    /**
     * 调用乐企接口-文书式完税证查询纳税人缴纳税款信息（指定DbtsBz值）
     *
     * @param reqVO 查询参数
     * @param dbtsBz 代办退税标志
     * @return 明细列表
     */
    private List<ZmWszcxjnskxxVO> callWssJnskxxWithDbtsBz(SswszmKjRequestVO reqVO, String dbtsBz) {
        try {
            ZmWszjnskcxtjVO zmWszjnskcxtjVO = new ZmWszjnskcxtjVO();
            zmWszjnskcxtjVO.setDjxh(reqVO.getDjxh());
            zmWszjnskcxtjVO.setNsrsbh(reqVO.getNsrsbh());
            zmWszjnskcxtjVO.setSkssrqq(reqVO.getCxrqq());
            zmWszjnskcxtjVO.setSkssrqz(reqVO.getCxrqz());
            zmWszjnskcxtjVO.setDbtsBz(dbtsBz);
            if ("Y".equals(dbtsBz)) {
                zmWszjnskcxtjVO.setCktsskssqq(reqVO.getCxrqq());
                zmWszjnskcxtjVO.setCktsskssqz(reqVO.getCxrqz());
            }
            if (GyUtils.isNotNull(reqVO.getZsxmDm())) {
                String zsxmDmStr = Optional.ofNullable(reqVO.getZsxmDm()).map(list -> String.join(",", list)).orElse("");
                zmWszjnskcxtjVO.setZsxmdmj(zsxmDmStr);
            }
            WszjnskcxtjRequestVO requestVO = new WszjnskcxtjRequestVO();
            requestVO.setZmWszjnskcxtjVO(zmWszjnskcxtjVO);

            final SjjhDTO sjjhDTO = new SjjhDTO();
            sjjhDTO.setSjjhlxDm("CX00000001");
            sjjhDTO.setYwbm("LQWSSWSZM");
            sjjhDTO.setDjxh(String.valueOf(reqVO.getDjxh()));
            sjjhDTO.setNsrsbh(reqVO.getNsrsbh());
            sjjhDTO.setXzqhszDm(reqVO.getXzqhszDm());
            sjjhDTO.setYwuuid(GyUtils.getUuid());
            sjjhDTO.setBwnr(JsonUtils.toJson(requestVO));
            log.info("文书式缴款信息查询-乐企请求报文(DbtsBz={})：{}", dbtsBz, JsonUtils.toJson(sjjhDTO));

            CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
            if (sjjhResult != null && sjjhResult.getData() != null) {
                WszjnskcxtjResponseVO resVO = JsonUtils.toBean((String) sjjhResult.getData(), WszjnskcxtjResponseVO.class);
                if (resVO != null && "00".equals(resVO.getReturncode())) {
                    ZmWszcxjnskxxVOGrid grid = resVO.getZmWszcxjnskxxVOGrid();
                    List<ZmWszcxjnskxxVO> results = Optional.ofNullable(grid)
                        .map(ZmWszcxjnskxxVOGrid::getZmWszcxjnskxxVOGridlb)
                        .orElse(Collections.emptyList());

                    log.info("DbtsBz={}的接口调用成功，返回{}条记录", dbtsBz, results.size());
                    return results;
                } else {
                    log.info("DbtsBz={}的接口调用失败，返回码: {}, 返回信息: {}",
                        dbtsBz,
                        resVO != null ? resVO.getReturncode() : "null",
                        resVO != null ? resVO.getReturnmsg() : "null");
                }
            } else {
                log.info("DbtsBz={}的接口调用返回空结果", dbtsBz);
            }
        } catch (Exception e) {
            log.error("调用文书式缴款信息查询异常，DbtsBz: {}", dbtsBz, e);
        }
        return Collections.emptyList();
    }

    /**
     * 将查询返回的明细列表映射并批量落库
     * 优化：添加DbtsBz字段保存和统计信息
     *
     * @param voList 明细列表
     * @param reqVO
     * @return 成功保存的条数
     */
    private int saveJnskxxList(List<ZmWszcxjnskxxVO> voList, SswszmKjRequestVO reqVO) {
        if (voList == null || voList.isEmpty()) {
            return 0;
        }

        // 统计不同DbtsBz的记录数量
        Map<String, Long> dbtsBzStats = voList.stream()
            .collect(Collectors.groupingBy(
                vo -> GyUtils.isNotNull(vo.getDbtsBz()) ? vo.getDbtsBz() : "未知",
                Collectors.counting()
            ));

        log.info("准备保存缴纳税款信息，总计{}条记录，DbtsBz统计: {}", voList.size(), dbtsBzStats);

        // 批量查询zsdlfs_dm
        List<String> zsuuidList = voList.stream()
                .map(ZmWszcxjnskxxVO::getZsuuid)
                .filter(GyUtils::isNotNull)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> zsdlfsDmMap = new HashMap<>();
        if (!zsuuidList.isEmpty()) {
            try {
                zsdlfsDmMap = jksmxService.getZsdlfsDmByZsuuidBatch(zsuuidList);
                log.info("批量查询征收代理方式代码完成，查询{}个zsuuid，获得{}条映射关系",
                        zsuuidList.size(), zsdlfsDmMap.size());
            } catch (Exception e) {
                log.error("批量查询征收代理方式代码失败", e);
            }
        }

        List<ZnsbNssbJnskxxDO> saveList = new ArrayList<>(voList.size());
        for (ZmWszcxjnskxxVO vo : voList) {
            ZnsbNssbJnskxxDO d = new ZnsbNssbJnskxxDO();
            d.setUuid(GyUtils.getUuid());
            d.setSkssqq(vo.getSkssqq());
            d.setSkssqz(vo.getSkssqz());
            d.setSjje(vo.getSjje());
            d.setZgswskfjDm(vo.getZgswskfjDm());
            d.setZsuuid(vo.getZsuuid());
            d.setZsxmDm(vo.getZsxmDm());
            d.setZspmDm(vo.getZspmDm());
            d.setZszmDm(vo.getZszmDm());
            d.setZsswjgDm(vo.getZsswjgDm());
            d.setDjzclxDm(vo.getDjzclxDm());
            d.setHyDm(vo.getHyDm());
            d.setSksxDm(vo.getSksxDm());
            try {
                if (GyUtils.isNotNull(vo.getDzsphm())) {
                    d.setDzsphm(new BigDecimal(vo.getDzsphm()).longValue());
                }
            } catch (Exception ex) {
                d.setDzsphm(null);
            }
            d.setRkrq(vo.getRkrq());
            d.setSbrq1(vo.getSbrq1());
            d.setBz(vo.getBz());
            d.setDjxh(reqVO.getDjxh());
            d.setNsrsbh(reqVO.getNsrsbh());
            // 设置代办退税标志
            d.setDbtsbz(vo.getDbtsBz());
            // 设置是否写入SAP划税凭证标记默认值为N（未写入划税凭证）
            d.setSfXrSaphspzbz("N");

            // 通过zsuuid关联查询并设置征收代理方式代码
            String zsdlfsDm = zsdlfsDmMap.get(vo.getZsuuid());
            d.setZsdlfsDm(zsdlfsDm);

            saveList.add(d);
        }

        boolean ok = jnskxxService.saveBatch(saveList);
        int savedCount = ok ? saveList.size() : 0;

        if (ok) {
            // 统计zsdlfs_dm的设置情况
            long zsdlfsDmSetCount = saveList.stream()
                    .filter(d -> GyUtils.isNotNull(d.getZsdlfsDm()))
                    .count();

            log.info("缴纳税款信息保存成功，共保存{}条记录，其中{}条记录设置了zsdlfs_dm",
                    savedCount, zsdlfsDmSetCount);
        } else {
            log.error("缴纳税款信息保存失败，尝试保存{}条记录", saveList.size());
        }

        return savedCount;
    }

    /**
     * 分组处理完税证明SAP划税凭证
     * 按照新的匹配规则：先按fzbz分组，然后处理不同的匹配场景
     *
     * @param periodJnskxxList 同一期间的缴纳税款信息列表
     * @param qyList 企业列表
     * @param zsxmKmdzList 征收项目科目对照表
     */
    private void processGroupedWszmToHspz(List<ZnsbNssbJnskxxDO> periodJnskxxList, 
                                          List<Map<String, Object>> qyList,
                                          List<Map<String, Object>> zsxmKmdzList) {
        if (periodJnskxxList == null || periodJnskxxList.isEmpty()) {
            return;
        }
        
        try {
            log.info("开始处理{}条分组数据的SAP划税凭证", periodJnskxxList.size());
            
            // 获取第一条记录的基本信息
            ZnsbNssbJnskxxDO firstRecord = periodJnskxxList.get(0);
            String nsrsbh = firstRecord.getNsrsbh();
            
            // 1. 先将dm_znsb_zsxmkmdzb用fzbz分组
            Map<String, List<Map<String, Object>>> fzbzGroupMap = zsxmKmdzList.stream()
                .collect(Collectors.groupingBy(config -> {
                    String fzbz = GYCastUtils.cast2Str(config.get("fzbz"));
                    return GyUtils.isNotNull(fzbz) ? fzbz : "0";
                }));
            
            log.info("征收项目科目对照表按fzbz分组后共{}个分组", fzbzGroupMap.size());
            
            // 存储所有BSCHL=40的数据
            List<Map<String, Object>> allDebitEntries = new ArrayList<>();
            
            // 遍历每个fzbz分组
            for (Map.Entry<String, List<Map<String, Object>>> fzbzEntry : fzbzGroupMap.entrySet()) {
                String fzbz = fzbzEntry.getKey();
                List<Map<String, Object>> fzbzConfigs = fzbzEntry.getValue();
                
                log.info("处理fzbz分组[{}]，包含{}条配置", fzbz, fzbzConfigs.size());
                
                // 2-3. 根据zsxm_dm和zsdlfs_dm进一步分组，处理不同匹配场景
                List<Map<String, Object>> debitEntries = processMatchingRules(periodJnskxxList, fzbzConfigs, fzbz);
                allDebitEntries.addAll(debitEntries);
            }
            
            if (allDebitEntries.isEmpty()) {
                log.info("未生成任何借方分录，无法创建SAP凭证");
                return;
            }
            
            // 4-5. 根据fzbz分组BSCHL=40数据，生成对应的BSCHL=50数据
            List<Map<String, Object>> creditEntries = generateCreditEntries(allDebitEntries, qyList, zsxmKmdzList, nsrsbh, firstRecord);
            
            // 验证匹配结果的完整性
            validateMatchingCompleteness(periodJnskxxList, allDebitEntries, nsrsbh);

            // 生成完整的SAP凭证并发送
            Map<String, Object> sapVoucher = buildSapVoucher(allDebitEntries, creditEntries, firstRecord, qyList);
            sendSapVoucher(sapVoucher, periodJnskxxList);

            BigDecimal totalAmount = allDebitEntries.stream()
                .map(entry -> new BigDecimal(entry.get("WRBTR").toString()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("成功处理{}条记录的SAP划税凭证，总金额: {}，借方分录{}条，贷方分录{}条",
                periodJnskxxList.size(), totalAmount, allDebitEntries.size(), creditEntries.size());
            
        } catch (Exception e) {
            log.error("处理分组SAP划税凭证失败", e);
            throw new RuntimeException("处理分组SAP划税凭证失败", e);
        }
    }
    
    /**
     * 根据新的匹配规则处理jnsk数据和配置数据的匹配
     * 包括两种场景：只有1条zspm_dm的和有2条zspm_dm的（一条有值一条为空）
     *
     * @param periodJnskxxList 同一期间的缴纳税款信息列表
     * @param fzbzConfigs 同一fzbz的配置列表
     * @param fzbz 当前fzbz值
     * @return BSCHL=40的借方分录列表
     */
    private List<Map<String, Object>> processMatchingRules(List<ZnsbNssbJnskxxDO> periodJnskxxList,
                                                           List<Map<String, Object>> fzbzConfigs,
                                                           String fzbz) {
        List<Map<String, Object>> debitEntries = new ArrayList<>();

        if (periodJnskxxList == null || periodJnskxxList.isEmpty()) {
            log.info("fzbz[{}]的缴纳税款信息列表为空，跳过处理", fzbz);
            return debitEntries;
        }

        if (fzbzConfigs == null || fzbzConfigs.isEmpty()) {
            log.info("fzbz[{}]的配置列表为空，跳过处理", fzbz);
            return debitEntries;
        }

        // 优化：预先建立jnsk数据的索引，提高匹配性能
        Map<String, List<ZnsbNssbJnskxxDO>> jnskIndexMap = buildJnskIndexMap(periodJnskxxList);

        // 按zsxm_dm和zsdlfs_dm分组
        Map<String, List<Map<String, Object>>> zsxmZsdlfsGroupMap = fzbzConfigs.stream()
            .collect(Collectors.groupingBy(config -> {
                String zsxmDm = GYCastUtils.cast2Str(config.get("zsxmDm"));
                String zsdlfsDm = GYCastUtils.cast2Str(config.get("zsdlfsDm"));
                return (zsxmDm != null ? zsxmDm : "") + "_" + (zsdlfsDm != null ? zsdlfsDm : "");
            }));

        log.info("在fzbz[{}]中，按zsxm_dm和zsdlfs_dm分组后共{}个子组", fzbz, zsxmZsdlfsGroupMap.size());

        // 处理每个子组
        for (Map.Entry<String, List<Map<String, Object>>> subGroupEntry : zsxmZsdlfsGroupMap.entrySet()) {
            String subGroupKey = subGroupEntry.getKey();
            List<Map<String, Object>> subGroupConfigs = subGroupEntry.getValue();

            log.info("处理子组[{}]，包含{}条配置", subGroupKey, subGroupConfigs.size());

            try {
                if (subGroupConfigs.size() == 1) {
                    // 2. 只有1条zspm_dm的情况
                    Map<String, Object> config = subGroupConfigs.get(0);
                    List<Map<String, Object>> matchedEntries = processSingleZspmConfigOptimized(jnskIndexMap, config, fzbz);
                    debitEntries.addAll(matchedEntries);

                } else if (subGroupConfigs.size() == 2) {
                    // 3. 有2条zspm_dm的情况（一条有值一条为空）
                    List<Map<String, Object>> matchedEntries = processDualZspmConfigOptimized(jnskIndexMap, subGroupConfigs, fzbz);
                    debitEntries.addAll(matchedEntries);

                } else {
                    log.info("子组[{}]包含{}条配置，超出预期的数量，跳过处理", subGroupKey, subGroupConfigs.size());
                }
            } catch (Exception e) {
                log.error("处理子组[{}]时发生异常，跳过该组", subGroupKey, e);
            }
        }

        log.info("fzbz[{}]匹配完成，生成{}条借方分录", fzbz, debitEntries.size());
        return debitEntries;
    }
    
    /**
     * 构建jnsk数据索引，提高匹配性能
     * 索引格式：zsxm_dm + "_" + zsdlfs_dm + "_" + zspm_dm
     * 优化：添加空值处理和日志记录
     */
    private Map<String, List<ZnsbNssbJnskxxDO>> buildJnskIndexMap(List<ZnsbNssbJnskxxDO> periodJnskxxList) {
        if (periodJnskxxList == null || periodJnskxxList.isEmpty()) {
            log.info("构建jnsk索引时，输入数据为空");
            return new HashMap<>();
        }

        Map<String, List<ZnsbNssbJnskxxDO>> indexMap = periodJnskxxList.stream()
            .collect(Collectors.groupingBy(jnsk -> {
                String zsxmDm = GyUtils.isNotNull(jnsk.getZsxmDm()) ? jnsk.getZsxmDm().trim() : "";
                String zsdlfsDm = GyUtils.isNotNull(jnsk.getZsdlfsDm()) ? jnsk.getZsdlfsDm().trim() : "";
                String zspmDm = GyUtils.isNotNull(jnsk.getZspmDm()) ? jnsk.getZspmDm().trim() : "";
                return zsxmDm + "_" + zsdlfsDm + "_" + zspmDm;
            }));

        log.info("构建jnsk索引完成，共{}条记录，生成{}个索引键", periodJnskxxList.size(), indexMap.size());
        return indexMap;
    }

    /**
     * 优化版：处理只有1条zspm_dm配置的情况
     * 优化：增强数据验证、错误处理和性能监控
     */
    private List<Map<String, Object>> processSingleZspmConfigOptimized(Map<String, List<ZnsbNssbJnskxxDO>> jnskIndexMap,
                                                                       Map<String, Object> config,
                                                                       String fzbz) {
        List<Map<String, Object>> debitEntries = new ArrayList<>();

        // 提取配置参数并进行数据清洗
        String zsxmDm = GYCastUtils.cast2Str(config.get("zsxmDm"));
        String zspmDm = GYCastUtils.cast2Str(config.get("zspmDm"));
        String zsdlfsDm = GYCastUtils.cast2Str(config.get("zsdlfsDm"));

        // 清理空白字符
        zsxmDm = GyUtils.isNotNull(zsxmDm) ? zsxmDm.trim() : null;
        zspmDm = GyUtils.isNotNull(zspmDm) ? zspmDm.trim() : null;
        zsdlfsDm = GyUtils.isNotNull(zsdlfsDm) ? zsdlfsDm.trim() : null;

        // 数据验证
        if (GyUtils.isNull(zsxmDm) || zsxmDm.isEmpty()) {
            log.info("fzbz[{}]配置中zsxm_dm为空，跳过处理", fzbz);
            return debitEntries;
        }

        // 构建索引键进行精确匹配
        String indexKey = zsxmDm + "_" + (zsdlfsDm != null ? zsdlfsDm : "") + "_" + (zspmDm != null ? zspmDm : "");
        List<ZnsbNssbJnskxxDO> matchedJnskList = jnskIndexMap.get(indexKey);

        if (matchedJnskList != null && !matchedJnskList.isEmpty()) {
            // 合并为一条jnsk数据，sjje合并计算作为WRBTR
            BigDecimal totalAmount = matchedJnskList.stream()
                .map(ZnsbNssbJnskxxDO::getSjje)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                Map<String, Object> debitEntry = buildDebitEntryFromConfig(matchedJnskList.get(0), config, totalAmount);
                // 记录匹配的记录数量，用于调试
                debitEntry.put("matched_records_count", matchedJnskList.size());
                debitEntries.add(debitEntry);

                log.info("fzbz[{}]单一zspm匹配成功，zsxm_dm: {}, zsdlfs_dm: {}, zspm_dm: {}, 匹配{}条记录，总金额: {}",
                    fzbz, zsxmDm, zsdlfsDm, zspmDm, matchedJnskList.size(), totalAmount);
            } else {
                log.info("fzbz[{}]单一zspm匹配到记录但总金额为0，zsxm_dm: {}, zsdlfs_dm: {}, zspm_dm: {}, 记录数: {}",
                    fzbz, zsxmDm, zsdlfsDm, zspmDm, matchedJnskList.size());
            }
        } else {
            log.debug("fzbz[{}]单一zspm未匹配到记录，索引键: {}", fzbz, indexKey);
        }

        return debitEntries;
    }
    
    /**
     * 优化版：处理有2条zspm_dm配置的情况（一条有值一条为空）
     */
    private List<Map<String, Object>> processDualZspmConfigOptimized(Map<String, List<ZnsbNssbJnskxxDO>> jnskIndexMap,
                                                                     List<Map<String, Object>> configs,
                                                                     String fzbz) {
        List<Map<String, Object>> debitEntries = new ArrayList<>();

        // 区分有值和为空的配置，增强数据验证
        Map<String, Object> valueConfig = null;
        Map<String, Object> emptyConfig = null;

        for (Map<String, Object> config : configs) {
            String zspmDm = GYCastUtils.cast2Str(config.get("zspmDm"));
            if (GyUtils.isNotNull(zspmDm) && !zspmDm.trim().isEmpty()) {
                if (valueConfig != null) {
                    log.info("fzbz[{}]双zspm配置中发现多个有值配置，使用第一个", fzbz);
                } else {
                    valueConfig = config;
                }
            } else {
                if (emptyConfig != null) {
                    log.info("fzbz[{}]双zspm配置中发现多个空值配置，使用第一个", fzbz);
                } else {
                    emptyConfig = config;
                }
            }
        }

        if (valueConfig == null || emptyConfig == null) {
            log.info("fzbz[{}]双zspm配置中未找到有值和空值的对应配置，有值配置: {}, 空值配置: {}",
                fzbz, valueConfig != null, emptyConfig != null);
            return debitEntries;
        }

        // 提取并清理配置参数
        String zsxmDm = GYCastUtils.cast2Str(valueConfig.get("zsxmDm"));
        String zsdlfsDm = GYCastUtils.cast2Str(valueConfig.get("zsdlfsDm"));
        String zspmDmValue = GYCastUtils.cast2Str(valueConfig.get("zspmDm"));

        zsxmDm = GyUtils.isNotNull(zsxmDm) ? zsxmDm.trim() : null;
        zsdlfsDm = GyUtils.isNotNull(zsdlfsDm) ? zsdlfsDm.trim() : null;
        zspmDmValue = GyUtils.isNotNull(zspmDmValue) ? zspmDmValue.trim() : null;

        // 数据验证
        if (GyUtils.isNull(zsxmDm) || zsxmDm.isEmpty()) {
            log.info("fzbz[{}]配置中zsxm_dm为空，跳过处理", fzbz);
            return debitEntries;
        }

        // 处理zspm_dm有值的匹配 - 使用索引精确匹配
        String valueIndexKey = zsxmDm + "_" + (zsdlfsDm != null ? zsdlfsDm : "") + "_" + zspmDmValue;
        List<ZnsbNssbJnskxxDO> valueMatchedList = jnskIndexMap.get(valueIndexKey);

        if (valueMatchedList != null && !valueMatchedList.isEmpty()) {
            BigDecimal valueAmount = valueMatchedList.stream()
                .map(ZnsbNssbJnskxxDO::getSjje)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (valueAmount.compareTo(BigDecimal.ZERO) > 0) {
                Map<String, Object> debitEntry = buildDebitEntryFromConfig(valueMatchedList.get(0), valueConfig, valueAmount);
                debitEntries.add(debitEntry);

                log.info("fzbz[{}]双zspm有值匹配成功，zsxm_dm: {}, zsdlfs_dm: {}, zspm_dm: {}, 匹配{}条记录，金额: {}",
                    fzbz, zsxmDm, zsdlfsDm, zspmDmValue, valueMatchedList.size(), valueAmount);
            }
        }

        // 处理zspm_dm为空的匹配：匹配 zsxm_dm、zsdlfs_dm 相同，但 zspm_dm 不等于另一条有值的 zspm_dm
        // 优化：使用更精确的匹配逻辑和性能监控
        List<ZnsbNssbJnskxxDO> emptyMatchedList = new ArrayList<>();
        String zsxmZsdlfsPrefix = zsxmDm + "_" + (zsdlfsDm != null ? zsdlfsDm : "") + "_";
        int excludedKeys = 0;

        for (Map.Entry<String, List<ZnsbNssbJnskxxDO>> entry : jnskIndexMap.entrySet()) {
            String indexKey = entry.getKey();
            if (indexKey.startsWith(zsxmZsdlfsPrefix) && !indexKey.equals(valueIndexKey)) {
                emptyMatchedList.addAll(entry.getValue());
            } else if (indexKey.equals(valueIndexKey)) {
                excludedKeys++;
            }
        }

        log.debug("fzbz[{}]双zspm空值匹配，前缀: {}, 排除键: {}, 找到记录: {}条",
            fzbz, zsxmZsdlfsPrefix, excludedKeys, emptyMatchedList.size());

        if (!emptyMatchedList.isEmpty()) {
            BigDecimal emptyAmount = emptyMatchedList.stream()
                .map(ZnsbNssbJnskxxDO::getSjje)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (emptyAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 使用空配置，但zspm_dm设为空
                Map<String, Object> debitEntry = buildDebitEntryFromConfig(emptyMatchedList.get(0), emptyConfig, emptyAmount);
                debitEntry.put("zspm_dm", ""); // 显式设为空
                debitEntry.put("matched_records_count", emptyMatchedList.size()); // 记录匹配数量
                debitEntries.add(debitEntry);

                log.info("fzbz[{}]双zspm空值匹配成功，zsxm_dm: {}, zsdlfs_dm: {}, 匹配{}条记录（排除zspm_dm={}），金额: {}",
                    fzbz, zsxmDm, zsdlfsDm, emptyMatchedList.size(), zspmDmValue, emptyAmount);
            } else {
                log.info("fzbz[{}]双zspm空值匹配到记录但总金额为0，记录数: {}", fzbz, emptyMatchedList.size());
            }
        } else {
            log.debug("fzbz[{}]双zspm空值未匹配到记录，前缀: {}", fzbz, zsxmZsdlfsPrefix);
        }

        return debitEntries;
    }

    /**
     * 验证匹配结果的完整性
     * 检查是否有未匹配的数据，用于调试和监控
     */
    private void validateMatchingCompleteness(List<ZnsbNssbJnskxxDO> periodJnskxxList,
                                            List<Map<String, Object>> allDebitEntries,
                                            String nsrsbh) {
        try {
            // 计算原始数据总金额
            BigDecimal originalTotalAmount = periodJnskxxList.stream()
                .map(ZnsbNssbJnskxxDO::getSjje)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算匹配后的总金额
            BigDecimal matchedTotalAmount = allDebitEntries.stream()
                .map(entry -> new BigDecimal(entry.get("WRBTR").toString()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算匹配的记录数
            int matchedRecordsCount = allDebitEntries.stream()
                .mapToInt(entry -> (Integer) entry.getOrDefault("matched_records_count", 0))
                .sum();

            log.info("匹配完整性验证 - 企业[{}]: 原始记录{}条/金额{}, 匹配记录{}条/金额{}, 匹配率: {:.2f}%",
                nsrsbh, periodJnskxxList.size(), originalTotalAmount,
                matchedRecordsCount, matchedTotalAmount,
                periodJnskxxList.size() > 0 ? (matchedRecordsCount * 100.0 / periodJnskxxList.size()) : 0);

            // 如果匹配率过低，记录警告
            if (periodJnskxxList.size() > 0 && matchedRecordsCount < periodJnskxxList.size() * 0.8) {
                log.info("企业[{}]匹配率较低，可能存在配置问题或数据异常", nsrsbh);
            }

        } catch (Exception e) {
            log.error("验证匹配完整性时发生异常", e);
        }
    }

    /**
     * 根据配置构建借方分录
     */
    private Map<String, Object> buildDebitEntryFromConfig(ZnsbNssbJnskxxDO jnskxx,
                                                          Map<String, Object> config,
                                                          BigDecimal amount) {
        Map<String, Object> debitEntry = new HashMap<>();

        // 数据验证
        if (jnskxx == null || config == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("构建借方分录时参数无效，jnskxx: {}, config: {}, amount: {}",
                jnskxx != null ? jnskxx.getUuid() : null,
                config != null ? config.get("zsxmDm") : null,
                amount);
            return debitEntry;
        }

        debitEntry.put("BSCHL", "40"); // 借方过账码
        debitEntry.put("HKONT", GYCastUtils.cast2Str(config.get("kmdm"))); // 税金科目
        debitEntry.put("WRBTR", amount.toString()); // 金额
        debitEntry.put("DMBTR", amount.toString()); // 本位币金额
        debitEntry.put("RSTGR", GYCastUtils.cast2Str(config.get("yydm"))); // 原因代码
        debitEntry.put("fzbz", GYCastUtils.cast2Str(config.get("fzbz"))); // 保存fzbz供后续分组使用

        // 保存匹配信息供调试使用
        debitEntry.put("matched_zsxm_dm", GYCastUtils.cast2Str(config.get("zsxmDm")));
        debitEntry.put("matched_zsdlfs_dm", GYCastUtils.cast2Str(config.get("zsdlfsDm")));
        debitEntry.put("matched_zspm_dm", GYCastUtils.cast2Str(config.get("zspmDm")));
        debitEntry.put("matched_records_count", 1); // 这个值会在调用处更新

        // 生成ZUONR（分配描述）
        String zuonr = buildZuonrFromConfig(config);
        debitEntry.put("ZUONR", zuonr);

        // 生成SGTXT（文本）
        String sgtxt = buildSgtxtFromConfig(jnskxx, config);
        debitEntry.put("SGTXT", sgtxt);

        return debitEntry;
    }
    
    /**
     * 根据fzbz分组BSCHL=40数据，生成对应的BSCHL=50数据
     */
    private List<Map<String, Object>> generateCreditEntries(List<Map<String, Object>> allDebitEntries, 
                                                            List<Map<String, Object>> qyList,
                                                            List<Map<String, Object>> zsxmKmdzList,
                                                            String nsrsbh,
                                                            ZnsbNssbJnskxxDO firstRecord) {
        List<Map<String, Object>> creditEntries = new ArrayList<>();
        
        // 按fzbz分组借方分录
        Map<String, List<Map<String, Object>>> fzbzDebitGroupMap = allDebitEntries.stream()
            .collect(Collectors.groupingBy(entry -> {
                String fzbz = GYCastUtils.cast2Str(entry.get("fzbz"));
                return GyUtils.isNotNull(fzbz) ? fzbz : "0";
            }));
        
        // 查找企业银行账户配置
        Map<String, Object> qyConfig = qyList.stream()
            .filter(qy -> nsrsbh.equals(GYCastUtils.cast2Str(qy.get("nsrsbh"))))
            .findFirst()
            .orElse(null);
        
        if (qyConfig == null) {
            log.info("未找到企业[{}]的银行账户配置", nsrsbh);
            return creditEntries;
        }
        
        // 为每个fzbz组生成一条BSCHL=50的贷方分录
        for (Map.Entry<String, List<Map<String, Object>>> fzbzEntry : fzbzDebitGroupMap.entrySet()) {
            String fzbz = fzbzEntry.getKey();
            List<Map<String, Object>> fzbzDebitEntries = fzbzEntry.getValue();
            
            // 计算该fzbz组的总金额
            BigDecimal totalAmount = fzbzDebitEntries.stream()
                .map(entry -> new BigDecimal(entry.get("WRBTR").toString()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            
            // 生成贷方分录
            Map<String, Object> creditEntry = new HashMap<>();
            creditEntry.put("BSCHL", "50"); // 贷方过账码
            creditEntry.put("HKONT", GYCastUtils.cast2Str(qyConfig.get("kmbm"))); // 银行科目
            creditEntry.put("WRBTR", totalAmount.toString()); // 总金额
            creditEntry.put("DMBTR", totalAmount.toString()); // 本位币金额
            creditEntry.put("RSTGR", "350"); // 默认原因代码
            creditEntry.put("fzbz", fzbz); // 保存fzbz
            
            // ZUONR使用银行名称
            creditEntry.put("ZUONR", GYCastUtils.cast2Str(qyConfig.get("yhmc")));
            
            // SGTXT通过dm_znsb_zsxmkmdzb中szmc生成
            String sgtxt = buildCreditSgtxtFromFzbz(firstRecord, zsxmKmdzList, fzbz);
            creditEntry.put("SGTXT", sgtxt);
            
            creditEntries.add(creditEntry);
            
            log.info("生成fzbz[{}]的贷方分录，对应{}条借方分录，总金额: {}", fzbz, fzbzDebitEntries.size(), totalAmount);
        }
        
        return creditEntries;
    }
    
    /**
     * 根据配置构建分配描述（ZUONR）
     */
    private String buildZuonrFromConfig(Map<String, Object> config) {
        String zsxmMc = GYCastUtils.cast2Str(config.get("zsxmmc"));
        String zspmMc = GYCastUtils.cast2Str(config.get("zspmmc"));
        
        if (GyUtils.isNotNull(zspmMc) && !"滞纳金".equals(zspmMc)) {
            return "应交税金-" + zsxmMc + "-" + zspmMc;
        } else if ("滞纳金".equals(zspmMc)) {
            return "缴税-" + zsxmMc + "滞纳金";
        } else {
            return "应交税金-" + zsxmMc;
        }
    }
    
    /**
     * 根据配置构建借方文本描述（SGTXT）
     */
    private String buildSgtxtFromConfig(ZnsbNssbJnskxxDO jnskxx, Map<String, Object> config) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月");
        String dateStr = sdf.format(jnskxx.getSkssqq() != null ? jnskxx.getSkssqq() : new Date());
        
        String zsxmMc = GYCastUtils.cast2Str(config.get("zsxmmc"));
        String zspmMc = GYCastUtils.cast2Str(config.get("zspmmc"));
        String fzmc = GYCastUtils.cast2Str(config.get("fzmc"));
        
        String prefix = "代扣代缴".equals(fzmc) ? "代扣代缴" : "缴税";
        String taxName;
        
        if (GyUtils.isNotNull(zspmMc) && !"滞纳金".equals(zspmMc)) {
            taxName = zspmMc; // 使用征收品目名称
        } else if ("滞纳金".equals(zspmMc)) {
            taxName = zsxmMc + "滞纳金";
        } else {
            taxName = zsxmMc; // 使用征收项目名称
        }
        
        return "付" + dateStr + prefix + taxName;
    }
    
    /**
     * 根据fzbz构建贷方文本描述（SGTXT）
     */
    private String buildCreditSgtxtFromFzbz(ZnsbNssbJnskxxDO firstRecord, 
                                            List<Map<String, Object>> zsxmKmdzList, 
                                            String fzbz) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月");
        String dateStr = sdf.format(firstRecord.getSkssqq() != null ? firstRecord.getSkssqq() : new Date());
        
        // 查找该fzbz对应的szmc
        String szmc = zsxmKmdzList.stream()
            .filter(config -> fzbz.equals(GYCastUtils.cast2Str(config.get("fzbz"))))
            .map(config -> GYCastUtils.cast2Str(config.get("szmc")))
            .filter(Objects::nonNull)
            .findFirst()
            .orElse("税费"); // 默认值
        
        return "付" + dateStr + szmc;
    }
    
    /**
     * 查找匹配的征收项目科目配置
     */
    private Map<String, Object> findMatchingZsxmConfig(ZnsbNssbJnskxxDO jnskxx, 
                                                        List<Map<String, Object>> zsxmKmdzList) {
        String zsxmDm = GyUtils.isNotNull(jnskxx.getZsxmDm()) ? jnskxx.getZsxmDm() : "";
        String zspmDm = GyUtils.isNotNull(jnskxx.getZspmDm()) ? jnskxx.getZspmDm() : "";
        String zsdlfsDm = GyUtils.isNotNull(jnskxx.getZsdlfsDm()) ? jnskxx.getZsdlfsDm() : "0";
        
        return zsxmKmdzList.stream()
            .filter(config -> {
                String configZsxmDm = GYCastUtils.cast2Str(config.get("zsxmDm"));
                String configZspmDm = GYCastUtils.cast2Str(config.get("zspmDm"));
                String configZsdlfsDm = GYCastUtils.cast2Str(config.get("zsdlfsDm"));
                
                // 征收项目代码必须匹配
                if (!zsxmDm.equals(configZsxmDm)) {
                    return false;
                }
                
                // 征收代理方式代码必须匹配
                if (!zsdlfsDm.equals(configZsdlfsDm)) {
                    return false;
                }
                
                // 征收品目代码匹配逻辑：空的不等于非空的
                if (GyUtils.isNull(configZspmDm) || configZspmDm.trim().isEmpty()) {
                    // 配置中征收品目为空，只匹配数据中征收品目也为空的
                    return GyUtils.isNull(zspmDm) || zspmDm.trim().isEmpty();
                } else {
                    // 配置中征收品目不为空，精确匹配
                    return configZspmDm.equals(zspmDm);
                }
            })
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 构建借方分录(BSCHL=40)
     */
    private Map<String, Object> buildDebitEntry(List<ZnsbNssbJnskxxDO> groupRecords,
                                                Map<String, Object> zsxmConfig,
                                                BigDecimal groupAmount) {
        Map<String, Object> debitEntry = new HashMap<>();
        ZnsbNssbJnskxxDO firstRecord = groupRecords.get(0);

        // 注意：UUID将在buildSapVoucher方法中统一添加
        debitEntry.put("BSCHL", "40"); // 借方过账码
        debitEntry.put("HKONT", GYCastUtils.cast2Str(zsxmConfig.get("kmdm"))); // 税金科目
        debitEntry.put("WRBTR", String.format("%.2f", groupAmount)); // 金额，保留两位小数
        debitEntry.put("DMBTR", String.format("%.2f", groupAmount)); // 本位币金额，保留两位小数

        // 原因代码处理，如果为空则设置为空字符串
        String rstgr = GYCastUtils.cast2Str(zsxmConfig.get("yydm"));
        debitEntry.put("RSTGR", GyUtils.isNotNull(rstgr) ? rstgr : "");

        // 生成ZUONR（分配描述）
        String zuonr = buildZuonr(zsxmConfig);
        debitEntry.put("ZUONR", zuonr);

        // 生成SGTXT（文本）
        String sgtxt = buildSgtxt(firstRecord, zsxmConfig);
        debitEntry.put("SGTXT", sgtxt);

        return debitEntry;
    }
    
    /**
     * 构建贷方分录(BSCHL=50)
     */
    private Map<String, Object> buildCreditEntry(String nsrsbh,
                                                 List<Map<String, Object>> qyList,
                                                 List<Map<String, Object>> zsxmKmdzList,
                                                 BigDecimal totalAmount,
                                                 ZnsbNssbJnskxxDO firstRecord) {
        // 查找企业银行账户配置
        Map<String, Object> qyConfig = qyList.stream()
            .filter(qy -> nsrsbh.equals(GYCastUtils.cast2Str(qy.get("nsrsbh"))))
            .findFirst()
            .orElse(null);

        if (qyConfig == null) {
            return null;
        }

        Map<String, Object> creditEntry = new HashMap<>();
        // 注意：UUID将在buildSapVoucher方法中统一添加
        creditEntry.put("BSCHL", "50"); // 贷方过账码
        creditEntry.put("HKONT", GYCastUtils.cast2Str(qyConfig.get("kmbm"))); // 银行科目
        creditEntry.put("WRBTR", String.format("%.2f", totalAmount)); // 总金额，保留两位小数
        creditEntry.put("DMBTR", String.format("%.2f", totalAmount)); // 本位币金额，保留两位小数
        creditEntry.put("RSTGR", "200"); // 原因代码，与目标结构保持一致

        // ZUONR使用银行名称
        String yhmc = GYCastUtils.cast2Str(qyConfig.get("yhmc"));
        creditEntry.put("ZUONR", GyUtils.isNotNull(yhmc) ? yhmc : "银行存款-工行沙支");

        // SGTXT通过dm_znsb_zsxmkmdzb中szmc生成
        String sgtxt = buildCreditSgtxt(firstRecord, zsxmKmdzList);
        creditEntry.put("SGTXT", sgtxt);

        return creditEntry;
    }
    
    /**
     * 构建分配描述（ZUONR）
     */
    private String buildZuonr(Map<String, Object> zsxmConfig) {
        String zsxmMc = GYCastUtils.cast2Str(zsxmConfig.get("zsxmmc"));
        String zspmMc = GYCastUtils.cast2Str(zsxmConfig.get("zspmmc"));
        
        if (GyUtils.isNotNull(zspmMc) && !"滞纳金".equals(zspmMc)) {
            return "应交税金-" + zsxmMc + "-" + zspmMc;
        } else if ("滞纳金".equals(zspmMc)) {
            return "缴税-" + zsxmMc + "滞纳金";
        } else {
            return "应交税金-" + zsxmMc;
        }
    }
    
    /**
     * 构建借方文本描述（SGTXT）
     */
    private String buildSgtxt(ZnsbNssbJnskxxDO jnskxx, Map<String, Object> zsxmConfig) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月");
        String dateStr = sdf.format(jnskxx.getSkssqq() != null ? jnskxx.getSkssqq() : new Date());
        
        String zsxmMc = GYCastUtils.cast2Str(zsxmConfig.get("zsxmmc"));
        String zspmMc = GYCastUtils.cast2Str(zsxmConfig.get("zspmmc"));
        String fzmc = GYCastUtils.cast2Str(zsxmConfig.get("fzmc"));
        
        String prefix = "代扣代缴".equals(fzmc) ? "代扣代缴" : "缴税";
        String taxName;
        
        if (GyUtils.isNotNull(zspmMc) && !"滞纳金".equals(zspmMc)) {
            taxName = zspmMc; // 使用征收品目名称
        } else if ("滞纳金".equals(zspmMc)) {
            taxName = zsxmMc + "滞纳金";
        } else {
            taxName = zsxmMc; // 使用征收项目名称
        }
        
        return "付" + dateStr + prefix + taxName;
    }
    
    /**
     * 构建贷方文本描述（SGTXT）
     */
    private String buildCreditSgtxt(ZnsbNssbJnskxxDO firstRecord, List<Map<String, Object>> zsxmKmdzList) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月");
        String dateStr = sdf.format(firstRecord.getSkssqq() != null ? firstRecord.getSkssqq() : new Date());
        
        // 查找对应的税种名称
        Map<String, Object> zsxmConfig = findMatchingZsxmConfig(firstRecord, zsxmKmdzList);
        String szmc = "税费"; // 默认值
        if (zsxmConfig != null) {
            szmc = GYCastUtils.cast2Str(zsxmConfig.get("szmc"));
            if (GyUtils.isNull(szmc)) {
                szmc = GYCastUtils.cast2Str(zsxmConfig.get("zsxmmc"));
            }
        }
        
        return "付" + dateStr + szmc;
    }
    
    /**
     * 构建完整的SAP凭证（支持多个贷方分录）
     */
    private Map<String, Object> buildSapVoucher(List<Map<String, Object>> debitEntries,
                                                List<Map<String, Object>> creditEntries,
                                                ZnsbNssbJnskxxDO firstRecord,
                                                List<Map<String, Object>> qyList) {
        Map<String, Object> sapVoucher = new HashMap<>();

        // 生成业务UUID，用于抬头和行项目
        String businessUuid = GyUtils.getUuid();//generateBusinessUuid(firstRecord);

        // 根据nsrsbh查找企业配置
        String nsrsbh = firstRecord.getNsrsbh();
        Map<String, Object> qyConfig = findQyConfigByNsrsbh(nsrsbh, qyList);

        // 从企业配置中获取qydm和bsy
        String qydm = qyConfig != null ? GYCastUtils.cast2Str(qyConfig.get("qydm")) : "A280";
        String bsy = qyConfig != null ? GYCastUtils.cast2Str(qyConfig.get("bsy")) : "完税证明划税凭证接口";

        // 如果qydm为空，使用默认值
        if (GyUtils.isNull(qydm)) {
            qydm = "A280";
        }

        // 如果bsy为空，使用默认值
        if (GyUtils.isNull(bsy)) {
            bsy = "完税证明划税凭证接口";
        }

        log.info("企业[{}]配置信息 - qydm: {}, bsy: {}", nsrsbh, qydm, bsy);

        // 根节点信息
        sapVoucher.put("GUID", GyUtils.getUuid());
        sapVoucher.put("SENDER", "SFP");
        sapVoucher.put("RECEIVER", "SAP");
        sapVoucher.put("TIMESTAMP", new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));

        // TD_ACC_HEADER 抬头信息
        Map<String, Object> tdAccHeader = new HashMap<>();
        List<Map<String, Object>> headerItems = new ArrayList<>();
        Map<String, Object> headerItem = new HashMap<>();

        headerItem.put("UUID", businessUuid);
        headerItem.put("BLART", "SA"); // 凭证类型改为SA
        headerItem.put("BLDAT", new SimpleDateFormat("yyyyMMdd").format(new Date())); // 凭证日期
        headerItem.put("BUDAT", new SimpleDateFormat("yyyyMMdd").format(new Date())); // 过账日期
        headerItem.put("BKTXT", "F#1"); // 凭证抬头文本
        headerItem.put("BUKRS", qydm); // 公司代码，从企业配置中获取
        headerItem.put("GSBER", qydm); // 业务范围，从企业配置中获取
        headerItem.put("WAERS", "RMB"); // 货币
        headerItem.put("UNAME", "HOUJC"); // 用户名
        headerItem.put("XBLNR", bsy); // 参考凭证号，从企业配置中获取bsy字段

        headerItems.add(headerItem);
        tdAccHeader.put("ITEM", headerItems);
        sapVoucher.put("TD_ACC_HEADER", tdAccHeader);

        // TD_ACC_ITEM 行项目信息
        Map<String, Object> tdAccItem = new HashMap<>();
        List<Map<String, Object>> allEntries = new ArrayList<>();

        // 为所有分录添加UUID
        for (Map<String, Object> entry : debitEntries) {
            entry.put("UUID", businessUuid);
            allEntries.add(entry);
        }

        for (Map<String, Object> entry : creditEntries) {
            entry.put("UUID", businessUuid);
            allEntries.add(entry);
        }

        tdAccItem.put("ITEM", allEntries);
        sapVoucher.put("TD_ACC_ITEM", tdAccItem);

        return sapVoucher;
    }

    /**
     * 生成业务UUID
     */
    private String generateBusinessUuid(ZnsbNssbJnskxxDO firstRecord) {
        // 使用登记序号生成业务UUID，格式：LQ + yyyyMMdd + 序号
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String djxhStr = String.format("%04d", firstRecord.getDjxh().intValue());
        return "LQ" + dateStr + djxhStr;
    }

    /**
     * 根据纳税人识别号查找企业代码
     */
    private String findQydmByNsrsbh(String nsrsbh, List<Map<String, Object>> qyList) {
        if (qyList == null || qyList.isEmpty() || GyUtils.isNull(nsrsbh)) {
            log.info("企业列表为空或纳税人识别号为空，使用默认企业代码A280");
            return "A280";
        }

        Map<String, Object> qyConfig = qyList.stream()
            .filter(qy -> nsrsbh.equals(GYCastUtils.cast2Str(qy.get("nsrsbh"))))
            .findFirst()
            .orElse(null);

        if (qyConfig == null) {
            log.info("未找到纳税人识别号[{}]对应的企业配置，使用默认企业代码A280", nsrsbh);
            return "A280";
        }

        String qydm = GYCastUtils.cast2Str(qyConfig.get("qydm"));
        if (GyUtils.isNull(qydm)) {
            log.info("纳税人识别号[{}]对应的企业代码为空，使用默认企业代码A280", nsrsbh);
            return "A280";
        }

        log.info("纳税人识别号[{}]对应的企业代码为[{}]", nsrsbh, qydm);
        return qydm;
    }

    /**
     * 根据纳税人识别号查找企业配置
     */
    private Map<String, Object> findQyConfigByNsrsbh(String nsrsbh, List<Map<String, Object>> qyList) {
        if (qyList == null || qyList.isEmpty() || GyUtils.isNull(nsrsbh)) {
            log.warn("企业列表为空或纳税人识别号为空，无法查找企业配置");
            return null;
        }

        Map<String, Object> qyConfig = qyList.stream()
            .filter(qy -> nsrsbh.equals(GYCastUtils.cast2Str(qy.get("nsrsbh"))))
            .findFirst()
            .orElse(null);

        if (qyConfig == null) {
            log.warn("未找到纳税人识别号[{}]对应的企业配置", nsrsbh);
        } else {
            log.info("找到纳税人识别号[{}]对应的企业配置", nsrsbh);
        }

        return qyConfig;
    }
    
    /**
     * 从SAP报文中提取UUID
     */
    private String extractUuidFromSapVoucher(Map<String, Object> sapVoucher) {
        try {
            Map<String, Object> tdAccHeader = (Map<String, Object>) sapVoucher.get("TD_ACC_HEADER");
            if (tdAccHeader != null) {
                List<Map<String, Object>> items = (List<Map<String, Object>>) tdAccHeader.get("ITEM");
                if (items != null && !items.isEmpty()) {
                    Map<String, Object> firstItem = items.get(0);
                    String uuid = GYCastUtils.cast2Str(firstItem.get("UUID"));
                    if (GyUtils.isNotNull(uuid)) {
                        return uuid;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("从SAP报文中提取UUID失败", e);
        }

        // 如果提取失败，生成一个新的UUID
        log.warn("无法从SAP报文中提取UUID，将生成新的UUID");
        return GyUtils.getUuid();
    }

    /**
     * 发送SAP凭证
     */
    private void sendSapVoucher(Map<String, Object> sapVoucher, List<ZnsbNssbJnskxxDO> periodJnskxxList) {
        long startTime = System.currentTimeMillis();
        String requestParams = JsonUtils.toJson(sapVoucher);
        String businessNo = null;
        String operator = null;

        // 从SAP报文中提取UUID
        String sapUuid = extractUuidFromSapVoucher(sapVoucher);
        log.info("从SAP报文中提取的UUID: {}", sapUuid);

        // 获取业务单据号和操作人员信息
        if (!periodJnskxxList.isEmpty()) {
            ZnsbNssbJnskxxDO firstRecord = periodJnskxxList.get(0);
            businessNo = firstRecord.getNsrsbh(); // 使用纳税人识别号作为业务单据号
        }

        try {
            SapHspzReqDTO sapHspzReqDTO = BeanUtils.toBean(sapVoucher, SapHspzReqDTO.class);
            String responseResult = sapApi.sendSapFwzcxx(sapHspzReqDTO);
            SapHspzResDTO resDTO = BeanUtils.toBean(responseResult, SapHspzResDTO.class);
            if (GyUtils.isNull(resDTO) || GyUtils.isNull(resDTO.getTD_RETURN())
                    || GyUtils.isNull(resDTO.getTD_RETURN().getITEM())){
                throw new RuntimeException("sap返回为空");
            }

            final List<SapHspzResDTO.ReturnItem> ITEM = resDTO.getTD_RETURN().getITEM();
            String cgbz = "N";
            String sapPzxh = "";
            if ("1".equals(ITEM.get(0).getCODE())) {
                cgbz = "Y";
                sapPzxh = ITEM.get(0).getBELNR();
            }

            // 直接保存SAP调用成功记录，使用SAP报文中的UUID
            ZnsbNssbSapjlbDO successRecord = new ZnsbNssbSapjlbDO();
            successRecord.setLq_uuid(sapUuid); // 使用从SAP报文中提取的UUID
            successRecord.setNsrsbh(businessNo);
            successRecord.setSap_bw(requestParams);
            successRecord.setSap_code(ITEM.get(0).getCODE());
            successRecord.setSap_msg(responseResult);
            successRecord.setCscs(1L);
            successRecord.setCgbz(cgbz);
            sapJlbService.save(successRecord);

            // 更新处理状态，同时设置lq_uuid
            for (ZnsbNssbJnskxxDO jnskxx : periodJnskxxList) {
                jnskxx.setSfXrSaphspzbz("Y");
                jnskxx.setLqUuid(sapUuid); // 设置lq_uuid为SAP报文中的UUID
                jnskxx.setSapPzbh(sapPzxh);
//                jnskxx.setSapCode("1");
//                jnskxx.setSapMsg("SAP凭证发送成功");
//                jnskxx.setSapBw(JsonUtils.toJson(sapVoucher));
                jnskxxService.updateById(jnskxx);
            }

        } catch (Exception e) {
            log.error("发送SAP凭证失败", e);
            String errorMessage = "SAP凭证发送失败: " + e.getMessage();

            // 直接保存SAP调用失败记录，使用SAP报文中的UUID
            ZnsbNssbSapjlbDO failedRecord = new ZnsbNssbSapjlbDO();
            failedRecord.setLq_uuid(sapUuid); // 使用从SAP报文中提取的UUID
            failedRecord.setNsrsbh(businessNo);
            failedRecord.setSap_bw(requestParams);
            failedRecord.setSap_code("ERROR");
            failedRecord.setSap_msg(errorMessage);
            failedRecord.setCscs(1L);
            failedRecord.setCgbz("N");

            // 设置下次执行时间为当前时间+5分钟
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MINUTE, 5);
            failedRecord.setXczxsj(cal.getTime());
            sapJlbService.save(failedRecord);

            // 更新失败状态，同时设置lq_uuid
            for (ZnsbNssbJnskxxDO jnskxx : periodJnskxxList) {
                jnskxx.setSfXrSaphspzbz("N");
                jnskxx.setLqUuid(sapUuid); // 设置lq_uuid为SAP报文中的UUID
//                jnskxx.setSapCode("ERROR");
//                jnskxx.setSapMsg("SAP凭证发送失败: " + e.getMessage());
                jnskxxService.updateById(jnskxx);
            }

            throw new RuntimeException("发送SAP凭证失败", e);
        }
    }
}