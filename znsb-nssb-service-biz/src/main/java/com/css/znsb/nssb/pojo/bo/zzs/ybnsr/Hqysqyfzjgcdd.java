package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《航空运输企业分支机构传递单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hqysqyfzjgcdd", propOrder = { "sbbhead", "yjnzzsqkGrid", "qdjxseqkGrid" })
@XmlSeeAlso({ Zzssyyybnsr11Hqysqyfzjgcddywbw.Hqysqyfzjgcdd.class })
@Getter
@Setter
public class Hqysqyfzjgcdd {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    @XmlElement(nillable = true, required = true)
    protected YjnzzsqkGrid yjnzzsqkGrid;

    @XmlElement(nillable = true, required = true)
    protected QdjxseqkGrid qdjxseqkGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "qdjxseqkGridlbVO" })
    @Getter
    @Setter
    public static class QdjxseqkGrid {
        @XmlElement(nillable = true, required = true)
        protected List<QdjxseqkGridlbVO> qdjxseqkGridlbVO;

        /**
         * Gets the value of the qdjxseqkGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the qdjxseqkGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getQdjxseqkGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link QdjxseqkGridlbVO}
         */
        public List<QdjxseqkGridlbVO> getQdjxseqkGridlbVO() {
            if (qdjxseqkGridlbVO == null) {
                qdjxseqkGridlbVO = new ArrayList<QdjxseqkGridlbVO>();
            }
            return this.qdjxseqkGridlbVO;
        }
    }

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "yjnzzsqkGridlbVO" })
    @Getter
    @Setter
    public static class YjnzzsqkGrid {
        @XmlElement(nillable = true, required = true)
        protected List<YjnzzsqkGridlbVO> yjnzzsqkGridlbVO;

        /**
         * Gets the value of the yjnzzsqkGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the yjnzzsqkGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getYjnzzsqkGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link YjnzzsqkGridlbVO}
         */
        public List<YjnzzsqkGridlbVO> getYjnzzsqkGridlbVO() {
            if (yjnzzsqkGridlbVO == null) {
                yjnzzsqkGridlbVO = new ArrayList<YjnzzsqkGridlbVO>();
            }
            return this.yjnzzsqkGridlbVO;
        }
    }
}