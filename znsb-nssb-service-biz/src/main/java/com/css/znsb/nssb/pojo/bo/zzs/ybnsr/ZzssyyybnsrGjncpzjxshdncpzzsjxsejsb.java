package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《购进农产品直接销售核定农产品增值税进项税额计算表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb", propOrder = { "sbbhead", "gjncpzjxshdncpzzsjxseGrid" })
@XmlSeeAlso({ ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw.ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb.class })
@Getter
@Setter
public class ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    @XmlElement(nillable = true, required = true)
    protected GjncpzjxshdncpzzsjxseGrid gjncpzjxshdncpzzsjxseGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "gjncpzjxshdncpzzsjxseGridlbVO" })
    @Getter
    @Setter
    public static class GjncpzjxshdncpzzsjxseGrid {
        @XmlElement(nillable = true, required = true)
        protected List<GjncpzjxshdncpzzsjxseGridlbVO> gjncpzjxshdncpzzsjxseGridlbVO;

        /**
         * Gets the value of the gjncpzjxshdncpzzsjxseGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the gjncpzjxshdncpzzsjxseGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getGjncpzjxshdncpzzsjxseGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GjncpzjxshdncpzzsjxseGridlbVO}
         */
        public List<GjncpzjxshdncpzzsjxseGridlbVO> getGjncpzjxshdncpzzsjxseGridlbVO() {
            if (gjncpzjxshdncpzzsjxseGridlbVO == null) {
                gjncpzjxshdncpzzsjxseGridlbVO = new ArrayList<GjncpzjxshdncpzzsjxseGridlbVO>();
            }
            return this.gjncpzjxshdncpzzsjxseGridlbVO;
        }
    }
}