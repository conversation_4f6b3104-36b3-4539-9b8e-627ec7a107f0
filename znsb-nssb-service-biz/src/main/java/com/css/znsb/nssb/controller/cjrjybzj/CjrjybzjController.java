package com.css.znsb.nssb.controller.cjrjybzj;

import com.css.znsb.framework.common.exception.ServiceException;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.nssb.constants.SbrwztConstants;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.domain.cjrjybzj.ZnsbCjrjybzjDO;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.dto.sbrw.ZnsbNssbSbrwDTO;
import com.css.znsb.nssb.pojo.vo.common.ResponseVO;
import com.css.znsb.nssb.pojo.vo.common.SbDyVO;
import com.css.znsb.nssb.service.cjrjybzj.CjrjybzjService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.css.znsb.nssb.utils.GYCastUtils;
import com.css.znsb.nssb.utils.SbPrintUtils;
import com.google.gson.Gson;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.css.znsb.framework.common.pojo.CommonResult.success;

@Slf4j
@RestController
@RequestMapping("/cjrjybzj/v1")
public class CjrjybzjController {

    @Resource
    private SjjhService sjjhService;
    @Resource
    private ZnsbNssbSbrwService znsbNssbSbrwService;
    @Resource
    private CjrjybzjService cjrjybzjService;

    @Autowired
    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;
    @PostMapping("/initData")
    //@ApiOperation(value = "残疾人保障金初始化")
    public CommonResult<Object> initData(@RequestBody Map req) {
        log.error("/cjrjybzj/v1/initData入参"+ JsonUtils.toJson(req));
//        Map result = new HashMap();
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CX00000001");
        sjjhDTO.setYwbm(YzpzzlEnum.CJRJYBZJ.getDm());
        sjjhDTO.setDjxh(GYCastUtils.cast2Str(req.get("djxh")));
        sjjhDTO.setNsrsbh(GYCastUtils.cast2Str(req.get("nsrsbh")));
        sjjhDTO.setXzqhszDm(GYCastUtils.cast2Str(req.get("xzqhszDm")));
        sjjhDTO.setBwnr(new Gson().toJson(req));
        CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
        return sjjhResult;
//        String dataStr = (String) sjjhResult.getData();
//        Map<String, Object> data = JsonUtils.toMap(dataStr);
//        if(data.containsKey("Ysqxxid")){
//            result.put("Ysqxxid",data.get("Ysqxxid"));
//        }
//        String bodyStr = "";
//        if(data.containsKey("Body")){
//            bodyStr = (String)data.get("Body");
//        }
//        Map<String, Object> body = JsonUtils.toMap(bodyStr);
//        if(body.containsKey("fq_")){
//            Map fq_ = (Map)body.get("fq_");
//            if(fq_.containsKey("nsrjbxx")){
//                Map nsrjbxx = (Map)fq_.get("nsrjbxx");
//                result.put("nsrjbxx", nsrjbxx);
//            }
//            if(fq_.containsKey("cjrjybzjInitData")){
//                Map cjrjybzjInitData = (Map)fq_.get("cjrjybzjInitData");
//                result.put("cjrjybzjInitData", cjrjybzjInitData);
//            }
//            if(fq_.containsKey("jmxxlist")){
//                Map jmxxlist = (Map)fq_.get("jmxxlist");
//                result.put("jmxxlist", jmxxlist);
//            }
//        }
//        return CommonResult.success(result);
    }

    @PostMapping("/submitData")
    //@ApiOperation(value = "残疾人保障金提交申报")
    public CommonResult<Object> submitData(@RequestBody Map req) {
        log.error("/cjrjybzj/v1/submitData入参"+ JsonUtils.toJson(req));
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("BC00000001");
        sjjhDTO.setYwbm(YzpzzlEnum.CJRJYBZJ.getDm());
        sjjhDTO.setDjxh(GYCastUtils.cast2Str(req.get("djxh")));
        sjjhDTO.setNsrsbh(GYCastUtils.cast2Str(req.get("nsrsbh")));
        sjjhDTO.setXzqhszDm(GYCastUtils.cast2Str(req.get("xzqhszDm")));
        sjjhDTO.setBwnr(new Gson().toJson(req));
        CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
        Map data = JsonUtils.toMap((String) sjjhResult.getData());
        if(data.containsKey("BizCode")){
            String bizcode = (String) data.get("BizCode");
            if("00".equals(bizcode)){
                ZnsbCjrjybzjDO cjrjybzjDO = map2cbjdo(req);
                List<ZnsbCjrjybzjDO> localCbj = this.cjrjybzjService.queryLocalCbj(cjrjybzjDO);
                if(GyUtils.isNull(localCbj)){
                    cjrjybzjDO.setUuid(GyUtils.getUuid());
                }else {
                    cjrjybzjDO.setUuid(localCbj.get(0).getUuid());
                }
                this.cjrjybzjService.saveOrUpdateCjrjybzj(cjrjybzjDO);
                clSbrw((String)req.get("sbrwuuid"));
            }else {
                return CommonResult.error(data.get("BizMsg"));
            }
        }
        return sjjhResult;
    }

    private ZnsbCjrjybzjDO map2cbjdo(Map req) {
        ZnsbCjrjybzjDO cjrjybzjDO = new ZnsbCjrjybzjDO();
        cjrjybzjDO.setNsrmc((String) req.get("Sbuuid"));
        cjrjybzjDO.setPzxh(GYCastUtils.cast2BigDecimal(req.get("pzxh")));
        cjrjybzjDO.setSkssqq(LocalDate.parse((String) req.get("SssqQ")));
        cjrjybzjDO.setSkssqz(LocalDate.parse((String) req.get("SssqZ")));
        cjrjybzjDO.setNsrsbh((String) req.get("nsrsbh"));
        cjrjybzjDO.setDjxh(GYCastUtils.cast2BigDecimal(req.get("djxh")));
        if(req.containsKey("Zcbw")){
            Map zcbw = JsonUtils.toMap((String) req.get("Zcbw"));
            if(zcbw.containsKey("ht_")){
                Map ht_ = (Map) zcbw.get("ht_");
                if(ht_.containsKey("cjrvoList")){
                    Map<String, Object> cjrvoList = (Map<String, Object>) ht_.get("cjrvoList");
                    if(cjrvoList.containsKey("cjrvoListlb")){
                        List<Map<String, Object>> cjrvoListlb = (List<Map<String, Object>>) cjrvoList.get("cjrvoListlb");
                        Map cjrvo = cjrvoListlb.get(0);
                        cjrjybzjDO.setJbrsfzjhm(GYCastUtils.cast2Str(cjrvo.get("blrysfzjhm")));
                        cjrjybzjDO.setNsrmc(GYCastUtils.cast2Str(cjrvo.get("nsrmc")));
                        cjrjybzjDO.setScjydz(GYCastUtils.cast2Str(cjrvo.get("scjydz")));
                        cjrjybzjDO.setZcdlxdh(GYCastUtils.cast2Str(cjrvo.get("zcdlxdh")));
                        cjrjybzjDO.setSnzzzggzze(GYCastUtils.cast2BigDecimal(cjrvo.get("snzzzggzze")));
                        cjrjybzjDO.setSnzzzgrs(GYCastUtils.cast2BigDecimal(cjrvo.get("snzzzgrs")));
                        cjrjybzjDO.setYapcjrjybl(GYCastUtils.cast2BigDecimal(cjrvo.get("yapcjrjybl")));
                        cjrjybzjDO.setSnzzzgnpjgz(GYCastUtils.cast2BigDecimal(cjrvo.get("snzzzgnpjgz")));
                        cjrjybzjDO.setBqynse(GYCastUtils.cast2BigDecimal(cjrvo.get("t_bqynse")));
                        cjrjybzjDO.setBqjmse(GYCastUtils.cast2BigDecimal(cjrvo.get("t_bqjmse")));
                        cjrjybzjDO.setBqyjse1(GYCastUtils.cast2BigDecimal(cjrvo.get("bqyjse1")));
                        cjrjybzjDO.setBqybtse(GYCastUtils.cast2BigDecimal(cjrvo.get("bqybtse")));
                        cjrjybzjDO.setSbrq(LocalDate.parse((String)cjrvo.get("sbrq")));
                        cjrjybzjDO.setJbr(GYCastUtils.cast2Str(cjrvo.get("jbr")));
                        cjrjybzjDO.setXzqhsf(GyUtils.cast2Str(cjrvo.get("xzqhsf")));
                        cjrjybzjDO.setXzqhds(GyUtils.cast2Str(cjrvo.get("xzqhds")));
                        cjrjybzjDO.setXzqhqx(GyUtils.cast2Str(cjrvo.get("xzqhqx")));
                        cjrjybzjDO.setJdxzDm(GyUtils.cast2Str(cjrvo.get("jdxzDm")));
                        cjrjybzjDO.setRdpzuuid(GYCastUtils.cast2Str(cjrvo.get("rdpzuuid")));
                        cjrjybzjDO.setSsjmxzDm(GYCastUtils.cast2Str(cjrvo.get("ssjmxzDm")));
                        cjrjybzjDO.setSlswjgDm(GYCastUtils.cast2Str(cjrvo.get("slswjgDm")));
                        cjrjybzjDO.setSlrDm(GYCastUtils.cast2Str(cjrvo.get("slrDm")));
                        cjrjybzjDO.setSlrq(LocalDate.parse(GYCastUtils.cast2Str(cjrvo.get("slrq"))));
                        cjrjybzjDO.setSnsjapcjrjyrs(GYCastUtils.cast2BigDecimal(cjrvo.get("snsjapcjrjyrs")));
                    }
                }
            }
        }
        return cjrjybzjDO;
    }

    private void clSbrw(String sbrwuuid) {
        ZnsbNssbSbrwDTO sbrwDto = znsbNssbSbrwService.getSbrwBySbrwuuid(sbrwuuid);
        if(GyUtils.isNotNull(sbrwDto)){
            sbrwDto.setRwztDm(SbrwztConstants.RWZT_WSB_DM);
            sbrwDto.setNsrsbztDm(SbrwztConstants.SBZT_SBZ_DM);
            znsbNssbSbrwMapper.updateById(BeanUtils.toBean(sbrwDto, ZnsbNssbSbrwDO.class));
        }
    }

    @PostMapping("/getSbResult")
    //@ApiOperation(value = "查询申报结果")
    public CommonResult<Object> getSbResult(@RequestBody Map req) {
        log.error("/cjrjybzj/v1/getSbResult入参"+ JsonUtils.toJson(req));
        CommonResult<Object> result = cjrjybzjService.getSbResult(req);
        if("1".equals(String.valueOf(result.getCode()))){
            Map<String, Object> data = (Map<String, Object>) result.getData();
            if(data.containsKey("BizCode")){
                String bizcode = (String) data.get("BizCode");
                if("01".equals(bizcode)){
                    cjrjybzjService.saveRzb(req);
                }
            }
        }
        return result;
    }
    @Operation(summary = "申报作废")
    @PostMapping("/sbzf")
    public CommonResult<Object> sbzf(@RequestBody @Valid Map<String, Object> reqVO) {
        CommonResult<Object> result = cjrjybzjService.sbZf(reqVO);
        return result;
    }
    @PostMapping("/exportPdf")
    @Operation(summary = "下载pdf")
    public CommonResult<ResponseVO> exportPdf(HttpServletResponse response, @RequestBody Map req) {
        ResponseVO responseVO = new ResponseVO();
        final String nsrsbh = (String) req.get("nsrsbh");
        final LocalDate skssqq = LocalDate.parse((String) req.get("skssqq"));
        final LocalDate skssqz = LocalDate.parse((String) req.get("skssqz"));
        String byte64 = this.printPdf(nsrsbh, skssqq, skssqz);
        if (GyUtils.isNotNull(byte64)){
            try {
                SbPrintUtils.downloadFile("残疾人就业保障金申报表", SbPrintUtils.decode(byte64), response);
            } catch (Exception e) {
                log.error("ErrorResponseException", e);
            }
            responseVO.setReturnCode("0");
            responseVO.setReturnMsg("成功");
            responseVO.setData(byte64);
            return success(responseVO);
        }else{
            throw new ServiceException(2_001_001_001, "下载失败，部分申报表无数据，请同步报表后操作。");
        }
    }
    private String printPdf(String nsrsbh, LocalDate skssqq, LocalDate skssqz){
        List<SbDyVO> dyxxList = cjrjybzjService.getCbjDyxx(nsrsbh, skssqq, skssqz);
        // 打印模板列表
        final List<String> mbljList = new ArrayList<>();
        // 打印参数列表
        final List<Map<String, Object>> paramList = new ArrayList<>();

        if(GyUtils.isNotNull(dyxxList)){
            for (SbDyVO vo : dyxxList) {
                mbljList.add(vo.getMblj());
                paramList.add(vo.getDyxx());
            }
        }
        // 输出到一个PDF文件中
        final byte[] bytes = SbPrintUtils.makePDFList(mbljList, paramList);
        // TODO 如果需要加签，在此处对bytes进行处理
        String printstr = SbPrintUtils.encode(bytes);
        return printstr;
    }
}
