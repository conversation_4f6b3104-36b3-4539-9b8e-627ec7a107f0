package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 《机动车辆经销企业销售明细表》
 *
 * <p>jdcljxqyxsmxbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="jdcljxqyxsmxbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="jdcljxqyxsmxbGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}jdcljxqyxsmxbGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jdcljxqyxsmxbGrid", propOrder = { "jdcljxqyxsmxbGridlbVO" })
public class JdcljxqyxsmxbGrid {
    @XmlElement(nillable = true, required = true)
    protected List<JdcljxqyxsmxbGridlbVO> jdcljxqyxsmxbGridlbVO;

    /**
     * Gets the value of the jdcljxqyxsmxbGridlbVO property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the jdcljxqyxsmxbGridlbVO property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getJdcljxqyxsmxbGridlbVO().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link JdcljxqyxsmxbGridlbVO}
     */
    public List<JdcljxqyxsmxbGridlbVO> getJdcljxqyxsmxbGridlbVO() {
        if (jdcljxqyxsmxbGridlbVO == null) {
            jdcljxqyxsmxbGridlbVO = new ArrayList<JdcljxqyxsmxbGridlbVO>();
        }
        return this.jdcljxqyxsmxbGridlbVO;
    }
}