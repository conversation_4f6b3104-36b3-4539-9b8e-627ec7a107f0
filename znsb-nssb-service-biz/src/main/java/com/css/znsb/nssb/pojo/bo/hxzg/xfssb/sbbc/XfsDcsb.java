
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《消费税》电池申报
 * 
 * <p>Java class for xfsDcsb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfsDcsb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="xfssb7" type="{http://www.chinatax.gov.cn/dataspec/}xfssb_dc"/>
 *         &lt;element name="xfssb7_fb1" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsejsbdc" minOccurs="0"/>
 *         &lt;element name="xfssb7_fb2" type="{http://www.chinatax.gov.cn/dataspec/}bqdsdjsejsbdc" minOccurs="0"/>
 *         &lt;element name="xfssb7_fb3" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsemxb" minOccurs="0"/>
 *         &lt;element name="xfssb7_fb4" type="{http://www.chinatax.gov.cn/dataspec/}dctlskdktz" minOccurs="0"/>
 *         &lt;element name="xfssb7_fb5" type="{http://www.chinatax.gov.cn/dataspec/}hznsqyxfsfpb" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfsDcsb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "xfssb7",
    "xfssb7Fb1",
    "xfssb7Fb2",
    "xfssb7Fb3",
    "xfssb7Fb4",
    "xfssb7Fb5"
})
public class XfsDcsb
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected XfssbDc xfssb7;
    @XmlElement(name = "xfssb7_fb1", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqjmsejsbdc xfssb7Fb1;
    @XmlElement(name = "xfssb7_fb2", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqdsdjsejsbdc xfssb7Fb2;
    @XmlElement(name = "xfssb7_fb3", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqjmsemxb xfssb7Fb3;
    @XmlElement(name = "xfssb7_fb4", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Dctlskdktz xfssb7Fb4;
    @XmlElement(name = "xfssb7_fb5", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Hznsqyxfsfpb xfssb7Fb5;

    /**
     * Gets the value of the xfssb7 property.
     * 
     * @return
     *     possible object is
     *     {@link XfssbDc }
     *     
     */
    public XfssbDc getXfssb7() {
        return xfssb7;
    }

    /**
     * Sets the value of the xfssb7 property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfssbDc }
     *     
     */
    public void setXfssb7(XfssbDc value) {
        this.xfssb7 = value;
    }

    /**
     * Gets the value of the xfssb7Fb1 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqjmsejsbdc }
     *     
     */
    public Bqjmsejsbdc getXfssb7Fb1() {
        return xfssb7Fb1;
    }

    /**
     * Sets the value of the xfssb7Fb1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqjmsejsbdc }
     *     
     */
    public void setXfssb7Fb1(Bqjmsejsbdc value) {
        this.xfssb7Fb1 = value;
    }

    /**
     * Gets the value of the xfssb7Fb2 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqdsdjsejsbdc }
     *     
     */
    public Bqdsdjsejsbdc getXfssb7Fb2() {
        return xfssb7Fb2;
    }

    /**
     * Sets the value of the xfssb7Fb2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqdsdjsejsbdc }
     *     
     */
    public void setXfssb7Fb2(Bqdsdjsejsbdc value) {
        this.xfssb7Fb2 = value;
    }

    /**
     * Gets the value of the xfssb7Fb3 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqjmsemxb }
     *     
     */
    public Bqjmsemxb getXfssb7Fb3() {
        return xfssb7Fb3;
    }

    /**
     * Sets the value of the xfssb7Fb3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqjmsemxb }
     *     
     */
    public void setXfssb7Fb3(Bqjmsemxb value) {
        this.xfssb7Fb3 = value;
    }

    /**
     * Gets the value of the xfssb7Fb4 property.
     * 
     * @return
     *     possible object is
     *     {@link Dctlskdktz }
     *     
     */
    public Dctlskdktz getXfssb7Fb4() {
        return xfssb7Fb4;
    }

    /**
     * Sets the value of the xfssb7Fb4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Dctlskdktz }
     *     
     */
    public void setXfssb7Fb4(Dctlskdktz value) {
        this.xfssb7Fb4 = value;
    }

    /**
     * Gets the value of the xfssb7Fb5 property.
     * 
     * @return
     *     possible object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public Hznsqyxfsfpb getXfssb7Fb5() {
        return xfssb7Fb5;
    }

    /**
     * Sets the value of the xfssb7Fb5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public void setXfssb7Fb5(Hznsqyxfsfpb value) {
        this.xfssb7Fb5 = value;
    }

}
