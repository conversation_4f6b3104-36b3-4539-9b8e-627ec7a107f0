package com.css.znsb.nssb.pojo.dto.xfs.lqfzpt.sbbc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * @description 消费税申报保存业务报文
 * @package com.css.znsb.nssb.pojo.dto.xfs.lqfzpt.sbbc
 * @file XfsSbbcYwbwVO 创建时间:2024/7/29 14:10
 * @copyright Copyright (c) 2022 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Data
@Schema(description = "消费税申报保存业务报文")
public class XfsSbbcYwbwVO implements Serializable {

    private static final long serialVersionUID = 5693254239589598769L;

    @Schema(description = "税务事项代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String swsxDm;

    @Schema(description = "登记序号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String djxh;

    @Schema(description = "税务机关代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String swjgDm;

    @Schema(description = "税款所属期起", requiredMode = Schema.RequiredMode.REQUIRED)
    private String skssqq;

    @Schema(description = "税款所属期止", requiredMode = Schema.RequiredMode.REQUIRED)
    private String skssqz;

    @Schema(description = "交易类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String jylxDm;

    @Schema(description = "税源操作标志，消费税固定传null")
    private String syczbz;

    @Schema(description = "申报数据，base64字符串", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sbsj;

    @Schema(description = "事项明细ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mxid;

}
