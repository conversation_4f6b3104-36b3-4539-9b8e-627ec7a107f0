package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO", propOrder = { "ewbhxh", "cpmc1", "hyncpmc", "dqhyncpsl", "qckcncpsl", "qcpjmj", "dqgjncpsl", "dqmj", "ncppjgmdj", "kcl", "dqyxdkncpjxse" })
@Getter
@Setter
public class GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 产品名称
     */
    protected String cpmc1;

    /**
     * 耗用农产品名称
     */
    protected String hyncpmc;

    /**
     * 当期耗用农产品数量吨
     */
    protected BigDecimal dqhyncpsl;

    /**
     * 期初库存农产品数量
     */
    protected BigDecimal qckcncpsl;

    /**
     * 期初平均买价
     */
    protected BigDecimal qcpjmj;

    /**
     * 当期购进农产品数量
     */
    protected BigDecimal dqgjncpsl;

    /**
     * 当期买价元/吨
     */
    protected BigDecimal dqmj;

    /**
     * 农产品平均购买单价元/吨
     */
    protected BigDecimal ncppjgmdj;

    /**
     * 扣除率
     */
    protected BigDecimal kcl;

    /**
     * 当期允许抵扣农产品进项税额元
     */
    protected BigDecimal dqyxdkncpjxse;
}