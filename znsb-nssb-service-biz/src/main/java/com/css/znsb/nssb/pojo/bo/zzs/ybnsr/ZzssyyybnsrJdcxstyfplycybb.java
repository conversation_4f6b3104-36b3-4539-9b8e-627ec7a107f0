package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车销售统一发票领用存月报表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdcxstyfplycybb", propOrder = { "jdcxstyfplycybbGrid" })
@Getter
@Setter
public class ZzssyyybnsrJdcxstyfplycybb {
    /**
     * 机动车销售统一发票领用存月报表
     */
    @XmlElement(nillable = true, required = true)
    protected JdcxstyfplycybbGrid jdcxstyfplycybbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "jdcxstyfplycybbGridlbVO" })
    @Getter
    @Setter
    public static class JdcxstyfplycybbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<JdcxstyfplycybbGridlbVO> jdcxstyfplycybbGridlbVO;

        /**
         * Gets the value of the jdcxstyfplycybbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jdcxstyfplycybbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getJdcxstyfplycybbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link JdcxstyfplycybbGridlbVO}
         */
        public List<JdcxstyfplycybbGridlbVO> getJdcxstyfplycybbGridlbVO() {
            if (jdcxstyfplycybbGridlbVO == null) {
                jdcxstyfplycybbGridlbVO = new ArrayList<JdcxstyfplycybbGridlbVO>();
            }
            return this.jdcxstyfplycybbGridlbVO;
        }
    }
}