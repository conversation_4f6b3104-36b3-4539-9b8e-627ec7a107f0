
package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 企业所得税汇总纳税分支机构分配表总机构情况信息
 * 
 * <p>Java class for SBQysdszjgxxVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBQysdszjgxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="fzjgfpbzjguuid" type="{http://www.chinatax.gov.cn/dataspec/}fzjgfpbzjguuid"/>
 *         &lt;element name="pzxh" type="{http://www.chinatax.gov.cn/dataspec/}pzxh"/>
 *         &lt;element name="sbuuid" type="{http://www.chinatax.gov.cn/dataspec/}sbuuid"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq" minOccurs="0"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz" minOccurs="0"/>
 *         &lt;element name="zjgnsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}zjgnsrsbh" minOccurs="0"/>
 *         &lt;element name="zjgdjxh" type="{http://www.chinatax.gov.cn/dataspec/}zjgdjxh" minOccurs="0"/>
 *         &lt;element name="ynsdse" type="{http://www.chinatax.gov.cn/dataspec/}ynsdse" minOccurs="0"/>
 *         &lt;element name="zjgftsdse" type="{http://www.chinatax.gov.cn/dataspec/}zjgftsdse" minOccurs="0"/>
 *         &lt;element name="zjgczjzfpsdse" type="{http://www.chinatax.gov.cn/dataspec/}zjgczjzfpsdse" minOccurs="0"/>
 *         &lt;element name="fzjgftdsdse" type="{http://www.chinatax.gov.cn/dataspec/}fzjgftdsdse" minOccurs="0"/>
 *         &lt;element name="sfxsdfjm" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="xsdfjmfd" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBQysdszjgxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "fzjgfpbzjguuid",
    "pzxh",
    "sbuuid",
    "skssqq",
    "skssqz",
    "zjgnsrsbh",
    "zjgdjxh",
    "ynsdse",
    "zjgftsdse",
    "zjgczjzfpsdse",
    "fzjgftdsdse",
    "sfxsdfjm",
    "xsdfjmfd",
    "btsldqbs"
})
public class SBQysdszjgxxVO
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fzjgfpbzjguuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String pzxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sbuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zjgnsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zjgdjxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ynsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgftsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zjgczjzfpsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double fzjgftdsdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sfxsdfjm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double xsdfjmfd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String btsldqbs;

    /**
     *创建时间:2016-3-19下午02:23:30
     *get方法
     * @return the btsldqbs
     */
    public String getBtsldqbs() {
        return btsldqbs;
    }

    /**
     * 创建时间:2016-3-19下午02:23:31
     * set方法
     * @param btsldqbs the btsldqbs to set
     */
    public void setBtsldqbs(String btsldqbs) {
        this.btsldqbs = btsldqbs;
    }

    /**
     * Gets the value of the fzjgfpbzjguuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFzjgfpbzjguuid() {
        return fzjgfpbzjguuid;
    }

    /**
     * Sets the value of the fzjgfpbzjguuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFzjgfpbzjguuid(String value) {
        this.fzjgfpbzjguuid = value;
    }

    /**
     * Gets the value of the pzxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPzxh() {
        return pzxh;
    }

    /**
     * Sets the value of the pzxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPzxh(String value) {
        this.pzxh = value;
    }

    /**
     * Gets the value of the sbuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbuuid() {
        return sbuuid;
    }

    /**
     * Sets the value of the sbuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbuuid(String value) {
        this.sbuuid = value;
    }

    /**
     * Gets the value of the skssqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * Sets the value of the skssqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * Gets the value of the skssqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * Sets the value of the skssqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * Gets the value of the zjgnsrsbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZjgnsrsbh() {
        return zjgnsrsbh;
    }

    /**
     * Sets the value of the zjgnsrsbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZjgnsrsbh(String value) {
        this.zjgnsrsbh = value;
    }

    /**
     * Gets the value of the zjgdjxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZjgdjxh() {
        return zjgdjxh;
    }

    /**
     * Sets the value of the zjgdjxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZjgdjxh(String value) {
        this.zjgdjxh = value;
    }

    /**
     * Gets the value of the ynsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYnsdse() {
        return ynsdse;
    }

    /**
     * Sets the value of the ynsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYnsdse(Double value) {
        this.ynsdse = value;
    }

    /**
     * Gets the value of the zjgftsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgftsdse() {
        return zjgftsdse;
    }

    /**
     * Sets the value of the zjgftsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgftsdse(Double value) {
        this.zjgftsdse = value;
    }

    /**
     * Gets the value of the zjgczjzfpsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZjgczjzfpsdse() {
        return zjgczjzfpsdse;
    }

    /**
     * Sets the value of the zjgczjzfpsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZjgczjzfpsdse(Double value) {
        this.zjgczjzfpsdse = value;
    }

    /**
     * Gets the value of the fzjgftdsdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getFzjgftdsdse() {
        return fzjgftdsdse;
    }

    /**
     * Sets the value of the fzjgftdsdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setFzjgftdsdse(Double value) {
        this.fzjgftdsdse = value;
    }

    /**
     * Gets the value of the sfxsdfjm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfxsdfjm() {
        return sfxsdfjm;
    }

    /**
     * Sets the value of the sfxsdfjm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfxsdfjm(String value) {
        this.sfxsdfjm = value;
    }

    /**
     * Gets the value of the xsdfjmfd property.
     * 
     */
    public double getXsdfjmfd() {
        return xsdfjmfd;
    }

    /**
     * Sets the value of the xsdfjmfd property.
     * 
     */
    public void setXsdfjmfd(double value) {
        this.xsdfjmfd = value;
    }

}
