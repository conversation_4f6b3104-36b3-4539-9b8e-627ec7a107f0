package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.service.cchxwssb.ZnsbNssbCxsshgjsyxxjlbService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CxsShgjSyxxJob {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ZnsbNssbCxsshgjsyxxjlbService cxsshgjsyxxjlbService;

    /**
     * 销项发票明细汇总任务
     */
    @XxlJob("cxsShgjSyxxJob")
    public void cxsShgjSyxxExecute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("cxsShgjSyxxJob start");
        String key = formatKey("cxsShgjSyxx:syxx");
        if (validateCache(key)) {
            try {
                String cs = XxlJobHelper.getJobParam();
                int zxcs = 10;
                if (!GyUtils.isNull(cs)) {
                    zxcs = Integer.parseInt(cs);
                }
                cxsshgjsyxxjlbService.clShgjsyxxHandle(zxcs);
            } finally {
                delCache(key);
            }
        }
        stopWatch.stop();
        log.info("cxsShgjSyxxJob end,RT:{}", stopWatch.getTotalTimeSeconds());
    }


    private Boolean validateCache(String key) {
        Boolean hasKey = stringRedisTemplate.hasKey(key);
        if (hasKey) {
            return false;
        } else {
            stringRedisTemplate.opsForValue().set(key, "1", 300, TimeUnit.MINUTES);
            return true;
        }
    }

    private void delCache(String key) {
        stringRedisTemplate.delete(key);
    }

    private static String formatKey(String key) {
        return String.format("cxsShgjSyxxJob:job:%s", key);
    }

}
