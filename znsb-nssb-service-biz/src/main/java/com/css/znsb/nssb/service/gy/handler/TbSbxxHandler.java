package com.css.znsb.nssb.service.gy.handler;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.domain.ZnsbNssbSjjhDO;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhHandlerDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhHandler;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbmxxxDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbqkcxRespDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbxxDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.TbSbxxSjjhDTO;
import com.css.znsb.nssb.service.sbqkcx.SbqkcxSbxxService;
import com.css.znsb.nssb.service.sbqkcx.SbqkcxSbxxmxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service(value = "TbSbxxHandler")
public class TbSbxxHandler implements SjjhHandler {
    @Resource
    private SbqkcxSbxxService sbbService;

    @Resource
    private SbqkcxSbxxmxService sbxxService;

    @Resource
    private SjjhService sjjhService;

    @Override
    public void after(SjjhHandlerDTO sjjhHandlerDTO){

    }

    @Override
    public void successAfter(ZnsbNssbSjjhDO sjjhDO, Object obj){
        log.info("回调成功事后采集-申报情况查询服务");
        TbSbxxSjjhDTO tbSbxxSjjhDTO = JsonUtils.toBean(sjjhDO.getBwnr(), TbSbxxSjjhDTO.class);
        List<String> pzxhList = new ArrayList<>();
        pzxhList.add(tbSbxxSjjhDTO.getPzxh());
        SbqkcxRespDTO sbqkcxRespDTO = (SbqkcxRespDTO)obj;
        if ("00".equals(sbqkcxRespDTO.getReturncode())){
            final List<SbxxDTO> sbxxList = sbqkcxRespDTO.getSbxxList();
            final List<SbmxxxDTO> sbmxxxList = sbqkcxRespDTO.getSbmxxxList();
            boolean sbbResult = sbbService.handlerSbbData(pzxhList, sbxxList);
            if (sbbResult){
                boolean sbxxResult = sbxxService.handlerSbxxData(pzxhList, sbmxxxList);
                if (sbxxResult){
                    List<SjjhDTO> sjjhDTOList = new ArrayList<>();
                    for(SbxxDTO sbxxDTO : sbxxList){
                        // 发起申报明细数据查询
                        final SjjhDTO sjjhDTO = new SjjhDTO();
                        sjjhDTO.setYwuuid(GyUtils.getUuid());
                        sjjhDTO.setYwbm(sbxxDTO.getYzpzzlDm());
                        sjjhDTO.setSjjhlxDm("SHSJGJ0001");
                        sjjhDTO.setNsrsbh(sbxxDTO.getNsrsbh());
                        sjjhDTO.setDjxh(sbxxDTO.getDjxh());
                        sjjhDTO.setXzqhszDm(sjjhDO.getXzqhszDm());
                        sjjhDTO.setBwnr(JsonUtils.toJson(sbqkcxRespDTO));
                        sjjhDTOList.add(sjjhDTO);
                    }
                    sjjhService.saveSjjhJobList(sjjhDTOList);
                }
            }
        }
    }

    @Override
    public void failAfter(ZnsbNssbSjjhDO sjjhDO, Object obj){

    }
}
