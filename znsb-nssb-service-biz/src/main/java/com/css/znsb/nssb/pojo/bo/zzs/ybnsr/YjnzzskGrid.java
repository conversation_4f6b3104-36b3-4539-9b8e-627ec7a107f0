package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 已缴纳增值税情况
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "yjnzzskGrid", propOrder = { "yjnzzskGridlb" })
@Getter
@Setter
public class YjnzzskGrid {
    @XmlElement(nillable = true, required = true)
    protected List<YjnzzskGridlb> yjnzzskGridlb;

    /**
     * Gets the value of the yjnzzskGridlb property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the yjnzzskGridlb property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getYjnzzskGridlb().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link YjnzzskGridlb}
     */
    public List<YjnzzskGridlb> getYjnzzskGridlb() {
        if (yjnzzskGridlb == null) {
            yjnzzskGridlb = new ArrayList<YjnzzskGridlb>();
        }
        return this.yjnzzskGridlb;
    }
}