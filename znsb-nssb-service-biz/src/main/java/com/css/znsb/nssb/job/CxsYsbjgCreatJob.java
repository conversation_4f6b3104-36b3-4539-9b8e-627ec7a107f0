package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.cchxwssb.plsb.CxsYsbsjcjService;
import com.css.znsb.nssb.service.zzsybnsrsb.plsb.ZzsybnsrYsbsjcjService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 增值税一般纳税人预申报结果创建定时任务
 */
@Slf4j
@Component
public class CxsYsbjgCreatJob {

    @Resource
    private CxsYsbsjcjService cxsYsbsjcjService;

    @XxlJob("CxsYsbjgCreatJob")
    public void execute() {
        log.info("开始执行增值税一般纳税人申报预申报结果创建定时任务");
        cxsYsbsjcjService.handlerYsbsj();
        log.info("增值税一般纳税人申报预申报结果创建定时任务执行完成");
    }
}
