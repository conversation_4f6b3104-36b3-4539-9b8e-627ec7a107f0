package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 进项信息
 *
 * <p>jxGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="jxGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *         &lt;element name="jxGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}jxGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jxGrid", propOrder = { "jxGridlbVO" })
public class JxGrid {
    protected List<JxGridlbVO> jxGridlbVO;

    /**
     * Gets the value of the jxGridlbVO property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the jxGridlbVO property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getJxGridlbVO().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link JxGridlbVO}
     */
    public List<JxGridlbVO> getJxGridlbVO() {
        if (jxGridlbVO == null) {
            jxGridlbVO = new ArrayList<JxGridlbVO>();
        }
        return this.jxGridlbVO;
    }
}