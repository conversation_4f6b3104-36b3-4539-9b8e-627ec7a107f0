package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 汇总纳税企业通用传递单Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hznsqytycddxxGridlbVO", propOrder = { "ewbhxh", "yjsk", "fpsk", "ynse", "yzl", "ysxssr", "ysxmmc", "ysxmDm" })
@Getter
@Setter
public class HznsqytycddxxGridlbVO {
    /**
     * 二维表行序号
     */
    protected String ewbhxh;

    /**
     * 已缴税款
     */
    protected BigDecimal yjsk;

    /**
     * 分配税款
     */
    protected BigDecimal fpsk;

    /**
     * 应纳税额
     */
    protected BigDecimal ynse;

    /**
     * 预征率
     */
    protected BigDecimal yzl;

    /**
     * 应税销售收入
     */
    protected BigDecimal ysxssr;

    /**
     * 应税项目
     */
    protected String ysxmmc;

    /**
     * 应税项目_代码
     */
    protected String ysxmDm;
}