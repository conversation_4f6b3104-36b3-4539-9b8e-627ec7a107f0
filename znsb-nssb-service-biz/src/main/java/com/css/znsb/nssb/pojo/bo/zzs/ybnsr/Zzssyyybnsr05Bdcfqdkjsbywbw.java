package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税纳税申报表附列资料五（不动产分期抵扣计算表）业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr05_bdcfqdkjsbywbw", propOrder = { "zzssyyybnsr05Bdcfqdkjsb" })
@Getter
@Setter
public class Zzssyyybnsr05Bdcfqdkjsbywbw extends TaxDoc {
    /**
     * 增值税纳税申报表附列资料五（不动产分期抵扣计算表）
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr05_bdcfqdkjsb", required = true)
    @JSONField(name = "zzssyyybnsr05_bdcfqdkjsb")
    protected Zzssyyybnsr05Bdcfqdkjsb zzssyyybnsr05Bdcfqdkjsb;
}