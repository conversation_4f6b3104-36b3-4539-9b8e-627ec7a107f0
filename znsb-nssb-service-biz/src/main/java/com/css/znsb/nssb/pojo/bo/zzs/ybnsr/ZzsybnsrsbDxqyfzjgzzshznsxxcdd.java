package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《电信企业分支机构增值税汇总纳税信息传递单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_dxqyfzjgzzshznsxxcdd", propOrder = { "sbbheadVO", "dxyjnzzsqkGrid", "qdjxseqkGrid" })
@Getter
@Setter
public class ZzsybnsrsbDxqyfzjgzzshznsxxcdd {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 已缴纳增值税情况
     */
    @XmlElement(nillable = true, required = true)
    protected DxyjnzzsqkGrid dxyjnzzsqkGrid;

    /**
     * 取得进项税额情况
     */
    @XmlElement(nillable = true, required = true)
    protected QdjxseqkGrid qdjxseqkGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "dxyjnzzsqkGridlbVO" })
    @Getter
    @Setter
    public static class DxyjnzzsqkGrid {
        @XmlElement(nillable = true, required = true)
        protected List<DxyjnzzsqkGridlbVO> dxyjnzzsqkGridlbVO;

        /**
         * Gets the value of the dxyjnzzsqkGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the dxyjnzzsqkGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getDxyjnzzsqkGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link DxyjnzzsqkGridlbVO}
         */
        public List<DxyjnzzsqkGridlbVO> getDxyjnzzsqkGridlbVO() {
            if (dxyjnzzsqkGridlbVO == null) {
                dxyjnzzsqkGridlbVO = new ArrayList<DxyjnzzsqkGridlbVO>();
            }
            return this.dxyjnzzsqkGridlbVO;
        }
    }

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "qdjxseGridlbVO" })
    @Getter
    @Setter
    public static class QdjxseqkGrid {
        @XmlElement(nillable = true, required = true)
        protected List<QdjxseGridlbVO> qdjxseGridlbVO;

        /**
         * Gets the value of the qdjxseGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the qdjxseGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getQdjxseGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link QdjxseGridlbVO}
         */
        public List<QdjxseGridlbVO> getQdjxseGridlbVO() {
            if (qdjxseGridlbVO == null) {
                qdjxseGridlbVO = new ArrayList<QdjxseGridlbVO>();
            }
            return this.qdjxseGridlbVO;
        }
    }
}