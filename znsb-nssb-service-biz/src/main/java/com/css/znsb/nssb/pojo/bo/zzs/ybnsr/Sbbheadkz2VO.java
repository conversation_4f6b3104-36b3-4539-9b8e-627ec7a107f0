package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 通用申报表表头扩展2，增加申报日期
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbheadkz2VO", propOrder = { "sbrq" })
@Getter
@Setter
public class Sbbheadkz2VO extends SbbheadVO {
    /**
     * 申报日期
     */
    @XmlElement(nillable = true, required = true)
    protected String sbrq;
}