package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《加油站月份加油信息明细表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jyzyfjyxxmxb", propOrder = { "sbbhead", "jyzyfjyxxmxGrid", "slxx" })
@XmlSeeAlso({ ZzssyyybnsrJyzyfjyxxmxbywbw.ZzssyyybnsrJyzyfjyxxmxb.class })
@Getter
@Setter
public class ZzssyyybnsrJyzyfjyxxmxb {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    @XmlElement(nillable = true, required = true)
    protected JyzyfjyxxmxGrid jyzyfjyxxmxGrid;

    @XmlElement(nillable = true, required = true)
    protected SbbslxxVO slxx;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "jyzyfjyxxmxGridlbVO" })
    @Getter
    @Setter
    public static class JyzyfjyxxmxGrid {
        @XmlElement(nillable = true, required = true)
        protected List<JyzyfjyxxmxGridlbVO> jyzyfjyxxmxGridlbVO;

        /**
         * Gets the value of the jyzyfjyxxmxGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jyzyfjyxxmxGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getJyzyfjyxxmxGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link JyzyfjyxxmxGridlbVO}
         */
        public List<JyzyfjyxxmxGridlbVO> getJyzyfjyxxmxGridlbVO() {
            if (jyzyfjyxxmxGridlbVO == null) {
                jyzyfjyxxmxGridlbVO = new ArrayList<JyzyfjyxxmxGridlbVO>();
            }
            return this.jyzyfjyxxmxGridlbVO;
        }
    }
}