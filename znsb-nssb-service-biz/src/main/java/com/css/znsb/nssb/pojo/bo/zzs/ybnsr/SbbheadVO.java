package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 通用申报表表头
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbheadVO", propOrder = { "nsrsbh", "nsrmc", "skssqq", "skssqz" })
@XmlSeeAlso({ Sbbheadkz1VO.class, Sbbheadkz3VO.class, Sbbheadkz2VO.class })
@Getter
@Setter
public class SbbheadVO {
    /**
     * 纳税人识别号
     */
    @XmlElement(nillable = true, required = true)
    protected String nsrsbh;

    /**
     * 纳税人名称
     */
    @XmlElement(nillable = true, required = true)
    protected String nsrmc;

    /**
     * 税款所属期起
     */
    @XmlElement(nillable = true, required = true)
    protected String skssqq;

    /**
     * 税款所属期止
     */
    @XmlElement(nillable = true, required = true)
    protected String skssqz;
}