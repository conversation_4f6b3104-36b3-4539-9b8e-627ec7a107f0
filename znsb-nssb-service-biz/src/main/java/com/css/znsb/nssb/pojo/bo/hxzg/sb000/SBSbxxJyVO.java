package com.css.znsb.nssb.pojo.bo.hxzg.sb000;

import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;

import java.math.BigDecimal;
import java.util.Date;


/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.sbzs.sb.sb000
 * @file SBSbxxJyVO.java 创建时间:2014-7-26上午01:52:56
 * @title 申报信息简要VO
 * @description 只包含最常用字段，用于后台之间传输及复用
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class SBSbxxJyVO extends TaxBaseVO {

    /**
     * @description 序列ID
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -7413398122308618184L;
    
    /**
     * @description 登记序号
     * @value value:djxh
     */
    private BigDecimal djxh;
    
    /**
     * @description 征收项目代码
     * @value value:zsxmDm
     */
    private String zsxmDm;
    
    /**
     * @description 征收品目代码
     * @value value:zspmDm
     */
    private String zspmDm;
    
    /**
     * @description 税款所属期起
     * @value value:skssqq
     */
    private Date skssqq;
    
    /**
     * @description 税款所属期止
     * @value value:skssqz
     */
    private Date skssqz;
    
    /**
     * @description 征收子目代码
     * @value value:zszmDm
     */
    private String zszmDm;
    
    /**
     * @description 税源编号
     * @value value:sybh1
     */
    private String sybh1;
    
    /**
     * @description 应征凭证种类代码
     * @value value:yzpzzlDm
     */
    private String yzpzzlDm;

    public BigDecimal getDjxh() {
        return djxh;
    }

    public void setDjxh(BigDecimal djxh) {
        this.djxh = djxh;
    }

    public String getZsxmDm() {
        return zsxmDm;
    }

    public void setZsxmDm(String zsxmDm) {
        this.zsxmDm = zsxmDm;
    }

    public String getZspmDm() {
        return zspmDm;
    }

    public void setZspmDm(String zspmDm) {
        this.zspmDm = zspmDm;
    }

    public Date getSkssqq() {
        return skssqq;
    }

    public void setSkssqq(Date skssqq) {
        this.skssqq = skssqq;
    }

    public Date getSkssqz() {
        return skssqz;
    }

    public void setSkssqz(Date skssqz) {
        this.skssqz = skssqz;
    }

    public String getZszmDm() {
        return zszmDm;
    }

    public void setZszmDm(String zszmDm) {
        this.zszmDm = zszmDm;
    }

    public String getSybh1() {
        return sybh1;
    }

    public void setSybh1(String sybh1) {
        this.sybh1 = sybh1;
    }

    public String getYzpzzlDm() {
        return yzpzzlDm;
    }

    public void setYzpzzlDm(String yzpzzlDm) {
        this.yzpzzlDm = yzpzzlDm;
    }
    


}
