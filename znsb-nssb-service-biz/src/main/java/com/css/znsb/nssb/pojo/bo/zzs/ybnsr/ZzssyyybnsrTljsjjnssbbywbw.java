package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《铁路建设基金纳税申报表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_tljsjjnssbbywbw", propOrder = { "zzssyyybnsrTljsjjnssbb" })
@Getter
@Setter
public class ZzssyyybnsrTljsjjnssbbywbw extends TaxDoc {
    /**
     * 《铁路建设基金纳税申报表》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_tljsjjnssbb", required = true)
    @JSONField(name = "zzssyyybnsr_tljsjjnssbb")
    protected ZzssyyybnsrTljsjjnssbb zzssyyybnsrTljsjjnssbb;
}