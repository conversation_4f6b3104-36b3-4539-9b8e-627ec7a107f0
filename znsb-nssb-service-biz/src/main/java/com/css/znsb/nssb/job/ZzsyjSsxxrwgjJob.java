package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.zzsyjsb.SjcjrwJobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;


/**
 * 数据采集定时任务
 */
@Slf4j
@Component
public class ZzsyjSsxxrwgjJob {

    @Resource
    private SjcjrwJobService sjcjrwJobService;
    /**
     * 查询乐企增值税预缴算税相关信息
     */
    @XxlJob("zzsyjssxxgjJob")
    public void execute() {
        log.info("==========开始查询乐企增值税预缴算税相关信息数据采集任务==========");
        //根据最新需求 不再需要定时任务
        //this.sjcjrwJobService.cxLqZzsyjssxxProcess();
        log.info("==========查询乐企增值税预缴算税相关信息数据采集任务结束==========");
    }

}
