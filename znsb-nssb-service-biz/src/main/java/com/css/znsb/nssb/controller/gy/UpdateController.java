package com.css.znsb.nssb.controller.gy;

import com.css.znsb.nssb.service.gy.DataUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/update")
public class UpdateController {

    @Autowired
    private DataUpdateService dataUpdateService;

    @PostMapping("/v1/updateBczt")
    public int updateBczt(@RequestParam String zsxmDm, @RequestParam String bczt, @RequestParam String syuuid,@RequestParam String ysbbz) {
        return dataUpdateService.updateData(zsxmDm, bczt, syuuid,ysbbz);
    }
}
