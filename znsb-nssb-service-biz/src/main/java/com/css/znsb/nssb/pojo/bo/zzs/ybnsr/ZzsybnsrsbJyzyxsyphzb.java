package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《加油站月销售油品汇总表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_jyzyxsyphzb", propOrder = { "sbbheadVO", "jyzyxsyphzbGrid" })
@Getter
@Setter
public class ZzsybnsrsbJyzyxsyphzb {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 《加油站月销售油品汇总表》业务报文
     */
    @XmlElement(nillable = true, required = true)
    protected JyzyxsyphzbGrid jyzyxsyphzbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "jyzyxsyphzbGridlbVO" })
    @Getter
    @Setter
    public static class JyzyxsyphzbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<JyzyxsyphzbGridlbVO> jyzyxsyphzbGridlbVO;

        /**
         * Gets the value of the jyzyxsyphzbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jyzyxsyphzbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getJyzyxsyphzbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link JyzyxsyphzbGridlbVO}
         */
        public List<JyzyxsyphzbGridlbVO> getJyzyxsyphzbGridlbVO() {
            if (jyzyxsyphzbGridlbVO == null) {
                jyzyxsyphzbGridlbVO = new ArrayList<JyzyxsyphzbGridlbVO>();
            }
            return this.jyzyxsyphzbGridlbVO;
        }
    }
}