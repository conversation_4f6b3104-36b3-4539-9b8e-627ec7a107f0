package com.css.znsb.nssb.service.ssyc.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.ServiceException;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.pojo.PageResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.redis.utils.RedisUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.NsrbqxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.SfzrdmxxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.constants.SsycEnum;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.constants.enums.ZsxmDmEnum;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjbMapper;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDOMapper;
import com.css.znsb.nssb.mapper.fcscztdsyssycj.SbCxsFtdcjglbMapper;
import com.css.znsb.nssb.mapper.ssyc.SsycBpcMapper;
import com.css.znsb.nssb.mapper.ssyc.SsycKmyeMapper;
import com.css.znsb.nssb.mapper.ssyc.SsycMapper;
import com.css.znsb.nssb.mapper.ssyc.ZnsbNssbSsycHzbMapper;
import com.css.znsb.nssb.mapper.ssyc.ZnsbNssbSsycQysdsMapper;
import com.css.znsb.nssb.mapper.ssyc.ZnsbNssbSsycZzsMapper;
import com.css.znsb.nssb.pojo.bo.ssyc.SsycBO;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjb;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDo;
import com.css.znsb.nssb.pojo.domain.sds.ZnsbNssbQysdsbaxxDO;
import com.css.znsb.nssb.pojo.domain.ssyc.ZnsbNssbSsycHzbDO;
import com.css.znsb.nssb.pojo.domain.ssyc.ZnsbNssbSsycQysdsDO;
import com.css.znsb.nssb.pojo.domain.ssyc.ZnsbNssbSsycZzsDO;
import com.css.znsb.nssb.pojo.dto.ssyc.SsycCtsSyxxDTO;
import com.css.znsb.nssb.pojo.dto.ssyc.SsycFcsSyxxDTO;
import com.css.znsb.nssb.pojo.dto.ssyc.SsycFtsxxDTO;
import com.css.znsb.nssb.pojo.dto.ssyc.SsycQyjcxxDTO;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.CjmxVO;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.CztdsysTdxxCxVO;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.CztdsysYsxxVO;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.FcsFwxxCxVO;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.FcsYsxxVO;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.TdsyysxxVO;
import com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CzjzsymxbGridlbVO;
import com.css.znsb.nssb.pojo.vo.ssyc.SsycHzbExcelVO;
import com.css.znsb.nssb.pojo.vo.ssyc.SsycPageResVO;
import com.css.znsb.nssb.pojo.vo.ssyc.SsycQysdsExcelVO;
import com.css.znsb.nssb.pojo.vo.ssyc.SsycUpdateTzjeReqVO;
import com.css.znsb.nssb.pojo.vo.ssyc.SsycUpdateTzjeResVO;
import com.css.znsb.nssb.pojo.vo.ssyc.SsycZzsExcelVO;
import com.css.znsb.nssb.pojo.vo.ssyc.SsyccxReqVO;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.CztdsysService;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.FcsService;
import com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.qysdsbaxx.ZnsbNssbQysdsbaxxService;
import com.css.znsb.nssb.service.ssyc.SsycService;
import com.css.znsb.nssb.service.zfjg.ZfjgxxService;
import com.css.znsb.nssb.utils.CchxwsnssbGyUtils;
import com.css.znsb.nssb.utils.FtsCxsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.hutool.core.util.NumberUtil.add;
import static cn.hutool.core.util.NumberUtil.div;
import static cn.hutool.core.util.NumberUtil.mul;
import static cn.hutool.core.util.NumberUtil.nullToZero;

@Slf4j
@Service
public class SsycServiceImpl implements SsycService {

    @Resource
    private SsycKmyeMapper kmyeMapper;

    @Resource
    private SsycBpcMapper bpcMapper;

    @Resource
    private ZnsbNssbSsycHzbMapper ssycHzbMapper;

    @Resource
    private ZnsbNssbSsycZzsMapper ssycZzsMapper;

    @Resource
    private ZnsbNssbSsycQysdsMapper ssycQysdsMapper;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private SsycMapper ssycMapper;

    @Resource
    private ZnsbNssbQysdsbaxxService qysdsbaxxService;

    @Resource
    private CztdsysService cztdsysService;

    @Resource
    private FcsService fcsService;

    @Resource
    private SbCxsFtdcjglbMapper sbCxsFtdcjglbMapper;

    @Resource
    private ZfjgxxService zfjgxxService;

    @Resource
    private ZnsbCxsFyxxcjbMapper fyxxcjbMapper;

    @Resource
    private ZnsbNssbCxsTdsyxxcjbDOMapper tdsyxxcjbDOMapper;

    @Override
    public void ssyc(String sszq, List<String> qydmzList, boolean cxycbz) {
        log.info("开始进行税收预测");

        log.info("开始计算当前日期");
        LocalDate currentDate = getCurrentDate(sszq);
        log.info("计算出当前日期：{}", currentDate);

        log.info("开始计算所属期");
        String ssq = currentDate.minusMonths(1).format(DateTimeFormatter.ofPattern("yyyyMM"));
        log.info("所属期为：{}", ssq);

        log.info("开始查询企业基础信息");
        List<SsycQyjcxxDTO> qyjcxxList = ssycMapper.queryQyjcxx();
        if (GyUtils.isNull(qyjcxxList)) {
            log.info("无企业基础信息，结束执行");
            return;
        }

        if (GyUtils.isNotNull(qydmzList)) {
            qyjcxxList = qyjcxxList.stream()
                    .filter(t -> qydmzList.contains(t.getQydmz()))
                    .collect(Collectors.toList());
        }
        log.info("查询到企业基础信息：{}", qyjcxxList);

        List<SsycQyjcxxDTO> czkqhQyxxList = new ArrayList<>();
        for (SsycQyjcxxDTO qyjcxxDTO : qyjcxxList) {
            String nsrlx = qyjcxxDTO.getNsrlx();
            String qydmz = qyjcxxDTO.getQydmz();
            String zzuuid = qyjcxxDTO.getZzuuid();
            String nsrsbh = qyjcxxDTO.getNsrsbh();
            String djxh = qyjcxxDTO.getDjxh();
            if (GyUtils.isNull(qydmz) || GyUtils.isNull(zzuuid)) {
                log.info("企业代码值或组织为空，跳过此数据");
                continue;
            }
            String ydspbz = qyjcxxDTO.getYdspbz();
            if ("Y".equals(ydspbz)) {
                czkqhQyxxList.add(qyjcxxDTO);
            }

            log.info("开始创建汇总表数据");
            ZnsbNssbSsycHzbDO hzbDO = BeanUtils.toBean(qyjcxxDTO, ZnsbNssbSsycHzbDO.class);
            hzbDO.setSsq(ssq);

            log.info("开始创建增值税数据");
            ZnsbNssbSsycZzsDO zzsDO = BeanUtils.toBean(qyjcxxDTO, ZnsbNssbSsycZzsDO.class);
            zzsDO.setSsq(ssq);

            log.info("开始创建企业所得税数据");
            ZnsbNssbSsycQysdsDO qysdsDO = BeanUtils.toBean(qyjcxxDTO, ZnsbNssbSsycQysdsDO.class);
            qysdsDO.setSsq(ssq);

            log.info("开始创建计算bo");
            SsycBO ssycBO = new SsycBO();
            ssycBO.setFjsslList(CollectionUtil.newArrayList(qyjcxxDTO.getCjssl(), qyjcxxDTO.getJyfsl(), qyjcxxDTO.getDfjyfsl()));
            ssycBO.setNsrlx(nsrlx);
            ssycBO.setLslfjmbz(getLslfjmbz(qyjcxxDTO.getDjxh(), qyjcxxDTO.getNsrsbh(), qyjcxxDTO.getNsrlx()));

            log.info("保留数据库中的调整金额");
            BigDecimal tzje2 = ssycZzsMapper.getTzje2(qydmz, ssq);
            ssycBO.setTzje2(tzje2);

            if ("1".equals(nsrlx)) {
                // 增值税一般纳税人
                log.info("开始查询增值税一般纳税人数据");

                List<SsycEnum> zzsybnsrCxtjList = SsycEnum.getSsycpzList("10101-1");
                zzsybnsrCxtjList.forEach(t -> handleDataSource(ssycBO, t.getFiledName(), t.getYcly(), qydmz, ssq, ssq, t));
                ssycBO.setZzsyssr(nullToZero(kmyeMapper.queryZzsYssr(qydmz, ssq, ssq)));
            }

            log.info("开始校验企业所得税是否为就地预缴");
            if (!checkQysdsBaxx(qyjcxxDTO.getDjxh(), currentDate)) {
                log.info("就地预缴企业，开始查询企业所得税数据");

                ZnsbNssbSsycQysdsDO blsjx = ssycQysdsMapper.getBlsjx(qydmz, ssq);
                BigDecimal lrtzje = BigDecimal.ZERO;
                BigDecimal qtse = BigDecimal.ZERO;
                BigDecimal yfjjkc = BigDecimal.ZERO;
                if (GyUtils.isNotNull(blsjx)) {
                    lrtzje = blsjx.getLrtzje();
                    qtse = blsjx.getQtse();
                    yfjjkc = blsjx.getYfjjkc();
                }

                log.info("保留数据库中的利润调整金额");
                ssycBO.setLrtzje(lrtzje);

                log.info("保留数据库中的其他税额");
                ssycBO.setQtse(qtse);

                log.info("保留数据库中的研发加计扣除");
                ssycBO.setYfjjkc(yfjjkc);

                log.info("查询企业所得税已预缴税额");
                String qysdsSkssqq = LocalDate.of(currentDate.minusMonths(1).getYear(), 1, 1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                String qysdsSkssqz = LocalDate.of(currentDate.minusMonths(1).getYear(), 12, 31).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                BigDecimal yyjse = ssycMapper.queryQysdsYyjse(nsrsbh, qysdsSkssqq, qysdsSkssqz);
                ssycBO.setYyjse(yyjse);

                List<SsycEnum> qysdsCxtjList = SsycEnum.getSsycpzList("10104");
                qysdsCxtjList.forEach(t -> handleDataSource(ssycBO, t.getFiledName(), t.getYcly(), qydmz, ssq, ssq, t));
            }

            // 个税数据处理
            log.info("开始查询个人所得税数据");
            List<SsycEnum> grsdsCxtjList = SsycEnum.getSsycpzList("10106");
            grsdsCxtjList.forEach(t -> handleDataSource(ssycBO, t.getFiledName(), t.getYcly(), qydmz, ssq, ssq, t));

            if (isJdsbq(currentDate)) {
                log.info("当前月份为季度申报期");
                // 处理企业所得税、印花税数据
                String sszqq = currentDate.minusMonths(4)
                        .with(TemporalAdjusters.firstDayOfMonth())
                        .format(DateTimeFormatter.ofPattern("yyyyMM"));
                String sszqz = currentDate.minusMonths(1)
                        .with(TemporalAdjusters.lastDayOfMonth())
                        .format(DateTimeFormatter.ofPattern("yyyyMM"));

                log.info("开始查询印花税数据");
                List<SsycEnum> yhsCxtjList = SsycEnum.getSsycpzList("10111");
                yhsCxtjList.forEach(t -> handleDataSource(ssycBO, t.getFiledName(), t.getYcly(), qydmz, sszqq, sszqz, t));

                log.info("开始查询增值税小规模数据");
                if ("2".equals(nsrlx)) {
                    // 增值税小规模，需要查询收入台账
                    BigDecimal xsse = ssycMapper.queryZzsxgmXxse(qydmz, sszqq, sszqz);
                    ssycBO.setZzsxxse(xsse);
                    ssycBO.setZzsyssr(nullToZero(kmyeMapper.queryZzsYssr(qydmz, sszqq, sszqz)));
                }
            }

            ssycBO.setFcsse(setFcsse(qyjcxxDTO.getNsrsbh(), CollectionUtil.newArrayList(qyjcxxDTO.getDjxh()), currentDate));

            ssycBO.setTdsysse(setTdsysse(qyjcxxDTO.getNsrsbh(), CollectionUtil.newArrayList(qyjcxxDTO.getDjxh()), currentDate));

            BeanUtils.copyBean(ssycBO, hzbDO);
            BeanUtils.copyBean(ssycBO, zzsDO);
            BeanUtils.copyBean(ssycBO, qysdsDO);

            log.info("汇总表数据：{}", hzbDO);
            log.info("增值税数据：{}", zzsDO);
            log.info("企业所得税数据：{}", qysdsDO);

            ssycHzbMapper.addOrUpdate(hzbDO);
            ssycZzsMapper.addOrUpdate(zzsDO);
            ssycQysdsMapper.addOrUpdate(qysdsDO);
        }

        if (GyUtils.isNotNull(czkqhQyxxList)) {
            List<SsycQyjcxxDTO> kqhQyxxList = new ArrayList<>();
            for (SsycQyjcxxDTO qyjcxxDTO : czkqhQyxxList) {
                String djxh = qyjcxxDTO.getDjxh();
                String nsrsbh = qyjcxxDTO.getNsrsbh();

                ZnsbMhzcQyjbxxmxReqVO qyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                qyjbxxmxReqVO.setNsrsbh(nsrsbh);
                CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByNsrsbh(qyjbxxmxReqVO);

                if (nsrxxResult.isSuccess() && GyUtils.isNotNull(nsrxxResult.getData())) {
                    ZnsbMhzcQyjbxxmxResVO nsrxx = nsrxxResult.getData();
                    List<JbxxmxsjVO> jbxxList = CollectionUtil.emptyIfNull(nsrxx.getJbxxmxsj()).stream()
                            .filter(t -> !djxh.equals(t.getDjxh()))
                            .collect(Collectors.toList());
                    if (GyUtils.isNotNull(jbxxList)) {
                        for (JbxxmxsjVO jbxxmxsjVO : jbxxList) {
                            SsycQyjcxxDTO kqhQyxx = new SsycQyjcxxDTO();
                            BeanUtils.copyBean(qyjcxxDTO, kqhQyxx);
                            kqhQyxx.setDjxh(jbxxmxsjVO.getDjxh());
                            kqhQyxx.setNsrmc(kqhQyxx.getNsrmc() + "（异地商铺）");
                            BigDecimal cjssl = CollectionUtil.emptyIfNull(nsrxx.getSfzrdmxxx()).stream()
                                    .filter(t -> jbxxmxsjVO.getDjxh().equals(t.getDjxh()) && ZsxmDmEnum.CSWHJSS.getCode().equals(t.getZsxmDm()))
                                    .map(SfzrdmxxxVO::getSlhdwse)
                                    .findFirst()
                                    .orElse(BigDecimal.ZERO);
                            BigDecimal jyfsl = CollectionUtil.emptyIfNull(nsrxx.getSfzrdmxxx()).stream()
                                    .filter(t -> jbxxmxsjVO.getDjxh().equals(t.getDjxh()) && ZsxmDmEnum.JYFFJ.getCode().equals(t.getZsxmDm()))
                                    .map(SfzrdmxxxVO::getSlhdwse)
                                    .findFirst()
                                    .orElse(BigDecimal.ZERO);
                            BigDecimal dfjyfsl = CollectionUtil.emptyIfNull(nsrxx.getSfzrdmxxx()).stream()
                                    .filter(t -> jbxxmxsjVO.getDjxh().equals(t.getDjxh()) && ZsxmDmEnum.DFJYFJ.getCode().equals(t.getZsxmDm()))
                                    .map(SfzrdmxxxVO::getSlhdwse)
                                    .findFirst()
                                    .orElse(BigDecimal.ZERO);
                            kqhQyxx.setCjssl(cjssl);
                            kqhQyxx.setJyfsl(jyfsl);
                            kqhQyxx.setDfjyfsl(dfjyfsl);
                            kqhQyxxList.add(kqhQyxx);
                        }
                    }
                }
            }

            ssycHzbMapper.insertBatch(crateYdspSj(kqhQyxxList, currentDate));
        }

        if (cxycbz) {
            // 重新预测完成，删除重新预测
            RedisUtils.delete("ssyc:cxyc:cxycuuid");
        }
    }

    private List<ZnsbNssbSsycHzbDO> crateYdspSj(List<SsycQyjcxxDTO> qyjcxxList, LocalDate currentDate) {
        List<ZnsbNssbSsycHzbDO> hzbList = new ArrayList<>();
        List<List<SsycQyjcxxDTO>> qyxxListGroupByNsrsbh = CollectionUtil.groupByField(qyjcxxList, "nsrsbh");
        for (List<SsycQyjcxxDTO> qyxxList : qyxxListGroupByNsrsbh) {
            String nsrsbh = qyxxList.get(0).getNsrsbh();
            String djxh = qyxxList.get(0).getDjxh();
            String nsrlx = qyxxList.get(0).getNsrlx();
            String lslfjmbz = getLslfjmbz(djxh, nsrsbh, nsrlx);
            ZnsbNssbSsycHzbDO hzbDO = new ZnsbNssbSsycHzbDO();
            BeanUtils.copyBean(qyxxList.get(0), hzbDO);
            List<String> djxhList = qyxxList.stream()
                    .map(SsycQyjcxxDTO::getDjxh)
                    .collect(Collectors.toList());

            String skssqq = "";
            String skssqz = "";
            if ("1".equals(nsrlx)) {
                skssqq = currentDate.minusMonths(1).withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                skssqz = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                if (isJdsbq(currentDate)) {
                    skssqq = currentDate.minusMonths(4).withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    skssqz = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                }
            }

            BigDecimal zzsyjseTotal = BigDecimal.ZERO;
            BigDecimal zzsyjfjsseTotal = BigDecimal.ZERO;
            if (GyUtils.isNotNull(skssqq) && GyUtils.isNotNull(skssqz)) {
                List<Map<String, Object>> zzsyjseList = ssycMapper.queryZzsyjse(djxhList, skssqq, skssqz);
                if (GyUtils.isNotNull(zzsyjseList)) {
                    for (Map<String, Object> zzsyjseMap : zzsyjseList) {
                        String yjdjxh = Convert.toStr(zzsyjseMap.get("djxh"));
                        BigDecimal zzsyjse = Convert.toBigDecimal(zzsyjseMap.get("ybtse"));
                        SsycQyjcxxDTO qyjcxx = qyxxList.stream()
                                .filter(t -> yjdjxh.equals(t.getDjxh()))
                                .findFirst()
                                .orElse(null);
                        BigDecimal zzsyjfjsse = BigDecimal.ZERO;
                        if (GyUtils.isNotNull(qyjcxx)) {
                            if ("Y".equals(lslfjmbz)) {
                                zzsyjfjsse = add(mul(zzsyjse, nullToZero(qyjcxx.getCjssl()), new BigDecimal("0.5")),
                                        mul(zzsyjse, nullToZero(qyjcxx.getJyfsl()), new BigDecimal("0.5")),
                                        mul(zzsyjse, nullToZero(qyjcxx.getDfjyfsl()), new BigDecimal("0.5")));
                            } else {
                                zzsyjfjsse = add(mul(zzsyjse, nullToZero(qyjcxx.getCjssl())),
                                        mul(zzsyjse, nullToZero(qyjcxx.getJyfsl())),
                                        mul(zzsyjse, nullToZero(qyjcxx.getDfjyfsl())));
                            }
                        }
                        zzsyjseTotal = add(zzsyjseTotal, zzsyjse);
                        zzsyjfjsseTotal = add(zzsyjfjsseTotal, zzsyjfjsse);
                    }
                }
            }

            hzbDO.setUuid(GyUtils.getUuid());
            hzbDO.setSsq(currentDate.minusMonths(1).format(DateTimeFormatter.ofPattern("yyyyMM")));
            hzbDO.setZzsse(zzsyjseTotal);
            hzbDO.setZzsfjse(zzsyjfjsseTotal);

            hzbDO.setFcsse(setFcsse(nsrsbh, djxhList, currentDate));
            hzbDO.setTdsysse(setTdsysse(nsrsbh, djxhList, currentDate));

            hzbDO.setSfhj(add(hzbDO.getZzsse(), hzbDO.getZzsfjse(), hzbDO.getFcsse(), hzbDO.getTdsysse()));
            hzbList.add(hzbDO);
        }
        return hzbList;
    }

    private String getLslfjmbz(String djxh, String nsrsbh, String nsrlx) {
        if ("2".equals(nsrlx)) {
            return "Y";
        } else {
            ZnsbMhzcQyjbxxmxReqVO qyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            qyjbxxmxReqVO.setDjxh(djxh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByDjxh(qyjbxxmxReqVO);
            ZnsbMhzcQyjbxxmxResVO nsrxx = nsrxxResult.getData();
            if (nsrxxResult.isSuccess() && GyUtils.isNotNull(nsrxx)) {
                List<NsrbqxxVO> bqxxList = nsrxx.getNsrbqxx();
                NsrbqxxVO gtgshbqxx = CollectionUtil.findOneByField(bqxxList, "bqmc", "gtgsh");
                String gtgsh = GyUtils.isNull(gtgshbqxx) ? "N" : gtgshbqxx.getYxbz();
                String xwqybq = zfjgxxService.getZjgXwqybq(nsrsbh, djxh);
                log.info("登记序号：{}，个体工商户标签：{}，小微企业标签：{}", djxh, gtgsh, xwqybq);
                if ("Y".equals(gtgsh) || "Y".equals(xwqybq)) {
                    return "Y";
                } else {
                    return "N";
                }
            } else {
                return "N";
            }
        }
    }

    private BigDecimal setFcsse(String nsrsbh, List<String> djxhList, LocalDate currentDate) {
        FcsFwxxCxVO fwxxCxVO = new FcsFwxxCxVO();
        fwxxCxVO.setTzcxbz("Y");
        fwxxCxVO.setDjxhList(djxhList);
        List<ZnsbCxsFyxxcjb> fyxxList = fyxxcjbMapper.queryFwxxList(fwxxCxVO);
        if (GyUtils.isNull(fyxxList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal fcsse = BigDecimal.ZERO;
        List<SsycFtsxxDTO> fcssbxxList = new ArrayList<>();
        for (ZnsbCxsFyxxcjb fyxxcjb : fyxxList) {
            SsycFcsSyxxDTO fcsSyxxDTO = new SsycFcsSyxxDTO();
            fcsSyxxDTO.setNsrsbh(nsrsbh);
            fcsSyxxDTO.setDjxh(fyxxcjb.getDjxh());
            fcsSyxxDTO.setYxbz(fyxxcjb.getYxbz());
            fcsSyxxDTO.setFybh(fyxxcjb.getFybh());
            fcsSyxxDTO.setUuid(fyxxcjb.getUuid());
            fcsSyxxDTO.setFyxxuuid(fyxxcjb.getFyxxuuid());
            fcsSyxxDTO.setZgswskfjDm(fyxxcjb.getZgswskfjDm());
            if (GyUtils.isNotNull(fyxxcjb.getNsywzzsj())) {
                fcsSyxxDTO.setNsywzzsj(Date.from(fyxxcjb.getNsywzzsj().atZone(ZoneId.systemDefault()).toInstant()));
            }
            List<SsycFtsxxDTO> ftsSbxx = createFtsSbxx(null, fcsSyxxDTO, currentDate);
            if (GyUtils.isNotNull(ftsSbxx)) {
                fcssbxxList.addAll(ftsSbxx);
            }
        }

        if (GyUtils.isNotNull(fcssbxxList)) {
            fcsse = ssycMapper.queryFcsse(fcssbxxList);
        }
        return fcsse;
    }

    private BigDecimal setTdsysse(String nsrsbh, List<String> djxhList, LocalDate currentDate) {
        CztdsysTdxxCxVO tdxxCxVO = new CztdsysTdxxCxVO();
        tdxxCxVO.setTzcxbz("Y");
        tdxxCxVO.setDjxhList(djxhList);
        tdxxCxVO.setNsrsbh(nsrsbh);
        List<ZnsbNssbCxsTdsyxxcjbDo> tdxxList = tdsyxxcjbDOMapper.queryTdxxList(tdxxCxVO);
        if (GyUtils.isNull(tdxxList)) {
            return BigDecimal.ZERO;
        }
        List<SsycFtsxxDTO> ctssbxxList = new ArrayList<>();
        for (ZnsbNssbCxsTdsyxxcjbDo tdsyxxcjbDo : tdxxList) {
            SsycCtsSyxxDTO ctsSyxxDTO = new SsycCtsSyxxDTO();
            ctsSyxxDTO.setDjxh(tdsyxxcjbDo.getDjxh());
            ctsSyxxDTO.setYxbz(tdsyxxcjbDo.getYxbz());
            ctsSyxxDTO.setUuid(tdsyxxcjbDo.getUuid());
            ctsSyxxDTO.setNsrsbh(nsrsbh);
            ctsSyxxDTO.setZgswskfjDm(tdsyxxcjbDo.getZgswskfjDm());
            ctsSyxxDTO.setSyuuid(tdsyxxcjbDo.getSyuuid());
            ctsSyxxDTO.setTdsybh(tdsyxxcjbDo.getTdsybh());
            if (GyUtils.isNotNull(tdsyxxcjbDo.getZxrq())) {
                ctsSyxxDTO.setZxrq(Date.from(tdsyxxcjbDo.getZxrq().atZone(ZoneId.systemDefault()).toInstant()));
            }
            List<SsycFtsxxDTO> ftsSbxx = createFtsSbxx(ctsSyxxDTO, null, currentDate);
            if (GyUtils.isNotNull(ftsSbxx)) {
                ctssbxxList.addAll(ftsSbxx);
            }
        }

        BigDecimal ctsse = CollectionUtil.emptyIfNull(ctssbxxList).stream()
                .map(t -> div(mul(t.getYstdmj(), t.getDwse()), new BigDecimal("12"), 2))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return ctsse;
    }

    private boolean isJdsbq(LocalDate currentDate) {
        int monthValue = currentDate.getMonthValue();
        if (1 == monthValue || 4 == monthValue || 7 == monthValue || 10 == monthValue) {
            return true;
        }
        return false;
    }

    private boolean checkQysdsBaxx(String djxh, LocalDate currentDate) {
        String skssqq = currentDate.minusMonths(4)
                .with(TemporalAdjusters.firstDayOfMonth())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String skssqz = currentDate.minusMonths(1)
                .with(TemporalAdjusters.lastDayOfMonth())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<ZnsbNssbQysdsbaxxDO> baxxList = qysdsbaxxService.queryQysdsbaxx(djxh,skssqq,skssqz);
        if (GyUtils.isNull(baxxList)){
            return false;
        }
        //就地缴纳标识为N不生成申报任务(存在为空的情况，如果为空默认为N同样不生成申报任务)
        return GyUtils.isNull(baxxList.get(0).getJdjnbs()) || "N".equals(baxxList.get(0).getJdjnbs());
    }

    private void handleDataSource(SsycBO ssycBO, String fieldName, String ycly,
                                  String qydmz, String sszqq, String sszqz, SsycEnum ssycEnum) {
        if ("SAP".equals(ycly)) {
            BigDecimal se = kmyeMapper.querySeByKmbm(qydmz, sszqq, sszqz, ssycEnum);
            ReflectUtil.setFieldValue(ssycBO, fieldName, se);
        } else if ("BPC".equals(ycly)) {
            BigDecimal total = calculateBpcData(qydmz, sszqq, sszqz, ssycEnum);
            ReflectUtil.setFieldValue(ssycBO, fieldName, total);
        }
    }

    private BigDecimal calculateBpcData(String qydmz, String sszqq, String sszqz, SsycEnum ssycEnum) {
        String gslxDm = getGsxl(qydmz);
        List<String> bpcDataList = new ArrayList<>();
        if (GyUtils.isNotNull(gslxDm)) {
            bpcDataList = Optional.ofNullable(
                    bpcMapper.querySeByBbmc(qydmz, sszqq, sszqz, gslxDm, ssycEnum)
            ).orElse(Collections.emptyList());
        }
        return bpcDataList.stream()
                .map(JsonUtils::toMapList)
                .map(list -> CollectionUtil.emptyIfNull(list).stream()
                        .filter(t -> ssycEnum.getSjxmc().equals(Convert.toStr(t.get("RGLID"))))
                        .map(t -> nullToZero(Convert.toBigDecimal(t.get("SIGNEDDATA"))))
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private LocalDate getCurrentDate(String sszq) {
        LocalDate currentDate = LocalDate.now();
        if (GyUtils.isNotNull(sszq)) {
            currentDate = LocalDate.parse(sszq + "25", DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        if (currentDate.getDayOfMonth() >= 25) {
            currentDate = currentDate.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        } else {
            currentDate = currentDate.with(TemporalAdjusters.firstDayOfMonth());
        }
        return currentDate;
    }

    public String getGsxl(String qydmz) {
        String gslx = null;
        if (GyUtils.isNull(qydmz)) {
            return gslx;
        } else {
            final String gshtb = qydmz.substring(0, 2);
            final String gshwb = qydmz.substring(qydmz.length() - 2, qydmz.length());
            if ("00".equals(gshwb)) {
                List<Map<String, Object>> zhxxList = CacheUtils.getTableData("dlfw_lq_zhxx");
                if (GyUtils.isNotNull(zhxxList)) {
                    List<Map<String, Object>> qydmList = zhxxList.stream()
                            .filter(o -> {
                                final String qydm = String.valueOf(o.get("qydm"));
                                return GyUtils.isNotNull(qydm) && gshtb.equals(qydm.substring(0, 2));
                            })
                            .collect(Collectors.toList());
                    if (!GyUtils.isNull(qydmList)) {
                        if (qydmList.size() > 1) {
                            gslx = "GH";
                        } else {
                            gslx = "E";
                        }
                    } else {
                        gslx = null;
                    }
                } else {
                    gslx = null;
                }

            } else {
                gslx = "S";
            }
        }
        return gslx;
    }

    private List<SsycFtsxxDTO> createFtsSbxx(SsycCtsSyxxDTO ctsSyxx, SsycFcsSyxxDTO fcsSyxx, LocalDate currentDate) {
        List<SsycFtsxxDTO> ftsxxList = new ArrayList<>();
        String dqrqStr = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Date dqrqDate = DateUtil.parseDate(dqrqStr);
        String djxh = "";
        String nsrsbh = "";
        String zsxmDm = "";
        String zspmDm = "";
        String swjgDm = "";
        String syuuid = "";
        String sybh = "";
        BigDecimal ystdmj = BigDecimal.ZERO;
        BigDecimal tddj = BigDecimal.ZERO;
        BigDecimal dwse = BigDecimal.ZERO;
        String yzpzzlDm = YzpzzlEnum.CXS.getDm();
        Boolean czzt = true;
        List<CzjzsymxbGridlbVO> czlist = new ArrayList<>();
        Date zxrq = null;
        if (GyUtils.isNotNull(ctsSyxx)) {
            djxh = ctsSyxx.getDjxh();
            Map<String, Object> pzbMap = CacheUtils.getTableData("cs_znsb_ftssbpzb",djxh);
            if("N".equals(ctsSyxx.getYxbz())){
                return null;
            }
            if(GyUtils.isNotNull(pzbMap)) {
                List<Map<String, Object>> pzbList = JsonUtils.toMapList((String) pzbMap.get("dataList"));
                pzbList = pzbList.stream().filter(vo-> "10112".equals(vo.get("zsxmDm"))).collect(Collectors.toList());
                if(GyUtils.isNotNull(pzbList)){
                    Calendar calendar = Calendar.getInstance();
                    String sbyfstr = (String)pzbList.get(0).get("sbyf");
                    if(GyUtils.isNotNull(sbyfstr)){
                        boolean flag = false;
                        String sbyfArr[] = sbyfstr.split(",");
                        int dqyf = calendar.get(Calendar.MONTH) + 1;
                        for(int i = 0; i < sbyfArr.length; i++){
                            int sbyf = Integer.parseInt(sbyfArr[i]);
                            if(dqyf == sbyf ){
                                flag = true;
                                break;
                            }
                        }
                        if(!flag){
                            log.info("当前月份不是申报月份{}",sbyfstr,"月");
                            return null;
                        }
                    }
                }
            }
            CztdsysYsxxVO cztdsysYsxxVO = cztdsysService.queryCztdsysYsxxbyuuid(ctsSyxx.getUuid());
            if(GyUtils.isNotNull(cztdsysYsxxVO) && GyUtils.isNotNull(cztdsysYsxxVO.getTdsyysxxVOList())){
                TdsyysxxVO tdsyysxxVO =  cztdsysYsxxVO.getTdsyysxxVOList().get(0);
                if (dqrqDate.after(DateUtil.parse(tdsyysxxVO.getYxqz(), "yyyy-MM-dd"))) {
                    log.info("当前时间大于土地应税有效期止{}",JsonUtils.toJson(tdsyysxxVO));
                    return null;
                } else {
                    zspmDm = tdsyysxxVO.getZspmDm();
                    ystdmj = new BigDecimal(Double.toString(tdsyysxxVO.getYstdmj()));
                    String tddjDm = tdsyysxxVO.getTddjDm();
                    tddj = new BigDecimal(tddjDm.substring(tddjDm.length() - 1));
                    dwse = new BigDecimal(Double.toString(tdsyysxxVO.getDwse()));
                }
            } else {
                return null;
            }
            zxrq = ctsSyxx.getZxrq();
            nsrsbh = ctsSyxx.getNsrsbh();
            zsxmDm = "10112";
            swjgDm = ctsSyxx.getZgswskfjDm();
            syuuid = ctsSyxx.getSyuuid();
            sybh = ctsSyxx.getTdsybh();
        } else if (GyUtils.isNotNull(fcsSyxx)) {
            djxh = fcsSyxx.getDjxh();
            Map<String, Object> pzbMap = CacheUtils.getTableData("cs_znsb_ftssbpzb",djxh);
            if(GyUtils.isNull(pzbMap)){
                return null;
            }
            if("N".equals(fcsSyxx.getYxbz())){
                return null;
            }
            FcsYsxxVO fcsYsxxVO = fcsService.queryFcsYsxxbyuuid(fcsSyxx.getUuid(),"");
            if(GyUtils.isNotNull(fcsYsxxVO) && GyUtils.isNotNull(fcsYsxxVO.getFcsCjYsxxVO())&& GyUtils.isNotNull(fcsYsxxVO.getFcsCjYsxxVO().getCjmxVOList())){
                final CjmxVO cjmxVO =  fcsYsxxVO.getFcsCjYsxxVO().getCjmxVOList().get(0);
                if (dqrqDate.after(DateUtil.parse(cjmxVO.getYxqz(), "yyyy-MM-dd"))) {
                    log.info("当前时间大于从价有效期止{}",JsonUtils.toJson(cjmxVO));
                    return null;
                } else if (GyUtils.isNotNull(cjmxVO.getFcyz()) && cjmxVO.getFcyz().equals(0.00)){
                    log.info("从价房产原值为0{}",JsonUtils.toJson(cjmxVO));
                    return null;
                }
            }
            zxrq = fcsSyxx.getNsywzzsj();
            nsrsbh = fcsSyxx.getNsrsbh();
            zsxmDm = "10110";
            swjgDm = fcsSyxx.getZgswskfjDm();
            syuuid = fcsSyxx.getFyxxuuid();
            sybh = fcsSyxx.getFybh();
            czlist = sbCxsFtdcjglbMapper.queryCzList(fcsSyxx.getUuid());
            if(GyUtils.isNotNull(czlist) && czlist.size() > 0){
                czzt = true;
            }
        }
        List<String> swjgDmList = new ArrayList<>();
        FtsCxsUtils.getSwjgList(swjgDmList , swjgDm, new HashMap<>());
        List<Map<String, Object>> sbqxwbAll = FtsCxsUtils.getAllCacheData("cs_gy_sbqxwh_sbqx");//申报期限维护表，700条左右
        List<Map<String, Object>> jjrAll = FtsCxsUtils.getAllCacheData("cs_gy_jjr");//节假日配置表，4000条左右
        List<Map<String, Object>> zqtzAll = FtsCxsUtils.getAllCacheData("cs_gy_zqtz");//征期调整配置表。50条
        Map<Object, Map<Object,List<Map<String, Object>>>> sbqxwbGroup = sbqxwbAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        Map<Object, Map<Object,List<Map<String, Object>>>> jjrGroup = jjrAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        Map<Object, Map<Object,List<Map<String, Object>>>> zqtzGroup = zqtzAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        List<SfzrdmxxxVO> sfzrdxxList = this.checkSfzrdxx(djxh, nsrsbh, zsxmDm, zspmDm, czzt);
        if(GyUtils.isNull(sfzrdxxList)){
            sfzrdxxList = this.checkFtsmrqxgz(zsxmDm, czzt,true, dqrqStr, swjgDmList, sbqxwbGroup, jjrGroup, zqtzGroup);
        } else {
            if(sfzrdxxList.size() == 1 && GyUtils.isNotNull(sfzrdxxList.get(0).getZspmDm())){
                if("101100700".equals(sfzrdxxList.get(0).getZspmDm())){
                    sfzrdxxList.addAll(this.checkFtsmrqxgz(zsxmDm, czzt,false, dqrqStr, swjgDmList, sbqxwbGroup, jjrGroup, zqtzGroup));
                } else if ("101100800".equals(sfzrdxxList.get(0).getZspmDm())) {
                    sfzrdxxList.addAll(this.checkFtsmrqxgz(zsxmDm, false, true,dqrqStr, swjgDmList, sbqxwbGroup, jjrGroup, zqtzGroup));
                }
                log.info("房土生成申报任务房土车sfzrdxxList3{}", JsonUtils.toJson(sfzrdxxList));
                if(sfzrdxxList.size() > 1){
                    final SfzrdmxxxVO sfzrdmxxxVO1 = sfzrdxxList.get(0);
                    final SfzrdmxxxVO sfzrdmxxxVO2 = sfzrdxxList.get(1);
                    if(sfzrdmxxxVO1.getNsqxDm().equals(sfzrdmxxxVO2.getNsqxDm())
                            && sfzrdmxxxVO1.getSbqxDm().equals(sfzrdmxxxVO2.getSbqxDm())){
                        sfzrdmxxxVO1.setZspmDm("");
                        sfzrdxxList = new ArrayList<>();
                        sfzrdxxList.add(sfzrdmxxxVO1);
                    }
                    log.info("房土生成申报任务房土车sfzrdxxList4{}", JsonUtils.toJson(sfzrdxxList));
                }
            }
        }
        for(SfzrdmxxxVO sfzrdmxxxVO : sfzrdxxList){
            String nsqxDm = sfzrdmxxxVO.getNsqxDm();
            String sbqxDm = sfzrdmxxxVO.getSbqxDm();
            Map<String,Object> skssqmap = CchxwsnssbGyUtils.jsSkssq(nsqxDm, dqrqDate, CchxwsnssbGyUtils.checkIsQnsb(sbqxDm));
            String skssqq = DateUtil.format((Date) skssqmap.get("skssqq"), "yyyy-MM-dd");
            String skssqz = DateUtil.format((Date) skssqmap.get("skssqz"), "yyyy-MM-dd");
            if(GyUtils.isNotNull(zxrq) && zxrq.before(DateUtils.toDate(skssqq, "yyyy-MM-dd" ))){
                continue;
            }
            Calendar calendarMrskssqq = Calendar.getInstance();
            calendarMrskssqq.setTime(DateUtils.toDate(skssqq,"yyyy-MM-dd"));
            Calendar calendarMrskssqz = Calendar.getInstance();
            calendarMrskssqz.setTime(DateUtils.toDate(skssqz,"yyyy-MM-dd"));
            String sbqxq = CchxwsnssbGyUtils.getSbqxrqq(sbqxDm, nsqxDm, zsxmDm,
                    calendarMrskssqq, calendarMrskssqz);
            String sbqxz = CchxwsnssbGyUtils.getSbqx(swjgDmList, sbqxDm, nsqxDm, zsxmDm,
                    calendarMrskssqq, calendarMrskssqz,
                    sbqxwbGroup, jjrGroup, zqtzGroup);
            if(!dqrqDate.before(DateUtil.parse(sbqxq, "yyyy-MM-dd")) && !dqrqDate.after(DateUtil.parse(sbqxz, "yyyy-MM-dd"))){
                SsycFtsxxDTO ftsxx = new SsycFtsxxDTO();
                ftsxx.setDjxh(djxh);
                ftsxx.setSybh(sybh);
                ftsxx.setSkssqq(skssqq);
                ftsxx.setSkssqz(skssqz);
                ftsxx.setYstdmj(ystdmj);
                ftsxx.setTddj(tddj);
                ftsxx.setDwse(dwse);
                ftsxxList.add(ftsxx);
            }
        }
        return ftsxxList;
    }

    private List<SfzrdmxxxVO> checkFtsmrqxgz(String zsxmDm, Boolean czzt, Boolean cjzt, String dqrqStr,
                                             List<String> swjgDmList,
                                             Map<Object, Map<Object,List<Map<String, Object>>>> sbqxwbGroup,
                                             Map<Object, Map<Object,List<Map<String, Object>>>> jjrGroup,
                                             Map<Object, Map<Object,List<Map<String, Object>>>> zqtzGroup){
        final List<SfzrdmxxxVO> returnList = new ArrayList<>();
        swjgDmList.remove("00000000000");
        final Date dqrq = DateUtil.parse(dqrqStr, "yyyy-MM-dd");
        final List<Map<String, Object>> ftcGroupAll = FtsCxsUtils.getAllCacheData("cxs_cs_sb_ftc_mrqxgz");//房土车配置，10条
        final Map<Object, Map<Object, List<Map<String, Object>>>> ftcGroup = ftcGroupAll.stream().collect(Collectors.groupingBy(g1 -> g1.get("zsxmDm"), Collectors.groupingBy(g2 -> g2.get("swjgDm"))));
        final List<Map<String, Object>>  ftcList = CchxwsnssbGyUtils.getnsqxftcByZsxmDmAndSwjgDm(zsxmDm, swjgDmList, ftcGroup, "0", false, false, dqrq,
                sbqxwbGroup, jjrGroup, zqtzGroup);
        log.info("校验房土车查询ftcList{}", ftcList);
        log.info("校验房土车查询czzt{},cjzt{}", czzt, cjzt);
        if(!GyUtils.isNull(ftcList)){
            //城镇土地
            if ("10112".equals(zsxmDm)) {
                final SfzrdmxxxVO sfzrdmxxxVO = new SfzrdmxxxVO();
                sfzrdmxxxVO.setNsqxDm((String)ftcList.get(0).get("NSQX_DM"));
                sfzrdmxxxVO.setSbqxDm((String)ftcList.get(0).get("SBQX_DM"));
                returnList.add(sfzrdmxxxVO);
            }
            //房产税
            if ("10110".equals(zsxmDm)) {
                SfzrdmxxxVO cjRdxx = null;
                SfzrdmxxxVO czRdxx = null;
                for (Map<String, Object> sfzrdxxVO : ftcList) {
                    if("".equals(sfzrdxxVO.get("ZSPM_DM"))){
                        cjRdxx =  new SfzrdmxxxVO();
                        cjRdxx.setNsqxDm((String)sfzrdxxVO.get("NSQX_DM"));
                        cjRdxx.setSbqxDm((String)sfzrdxxVO.get("SBQX_DM"));
                        break;
                    }
                    if("101100700".equals(sfzrdxxVO.get("ZSPM_DM"))){
                        cjRdxx =  new SfzrdmxxxVO();
                        cjRdxx.setNsqxDm((String)sfzrdxxVO.get("NSQX_DM"));
                        cjRdxx.setSbqxDm((String)sfzrdxxVO.get("SBQX_DM"));
                        cjRdxx.setZspmDm("101100700");
                    }
                    if("101100800".equals(sfzrdxxVO.get("ZSPM_DM"))){
                        czRdxx =  new SfzrdmxxxVO();
                        czRdxx.setNsqxDm((String)sfzrdxxVO.get("NSQX_DM"));
                        czRdxx.setSbqxDm((String)sfzrdxxVO.get("SBQX_DM"));
                        czRdxx.setZspmDm("101100800");
                    }
                }
                log.info("cjRdxx{}",JsonUtils.toJson(cjRdxx));
                log.info("czRdxx{}",JsonUtils.toJson(czRdxx));
                if (GyUtils.isNotNull(cjRdxx)){
                    if(cjzt){
                        if(czzt){
                            if(GyUtils.isNotNull(czRdxx)){
                                if (!czRdxx.getNsqxDm().equals(cjRdxx.getNsqxDm()) || !czRdxx.getSbqxDm().equals(cjRdxx.getSbqxDm())) {
                                    returnList.add(czRdxx);
                                }else{
                                    cjRdxx.setZspmDm("");
                                }
                            }
                            returnList.add(cjRdxx);
                        }else {
                            cjRdxx.setZspmDm("101100700");
                            returnList.add(cjRdxx);
                        }
                    }else {
                        if(czzt){
                            if(GyUtils.isNotNull(czRdxx)){
                                returnList.add(czRdxx);
                            }else if("".equals(cjRdxx.getZspmDm())){
                                cjRdxx.setZspmDm("101100800");
                                returnList.add(cjRdxx);
                            }
                        }
                    }
                }else{
                    if (GyUtils.isNotNull(czRdxx) && czzt) {
                        returnList.add(czRdxx);
                    }
                }
            }
        }
        return returnList;
    }

    private List<SfzrdmxxxVO> checkSfzrdxx(String djxh, String nsrsbh, String zsxmDm, String zspmDm, Boolean czzt){
        final List<SfzrdmxxxVO> returnList = new ArrayList<>();
        final Date dqrq = new Date();
        //先查询税费种认定信息
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> cxSfzrdxxReqVO = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        log.info("校验种认定查询cxSfzrdxxReqVO{}", JsonUtils.toJson(cxSfzrdxxReqVO));
        if (!GyUtils.isNull(cxSfzrdxxReqVO) && !GyUtils.isNull(cxSfzrdxxReqVO.getData()) && !GyUtils.isNull(cxSfzrdxxReqVO.getData().getSfzrdmxxx())) {
            final List<SfzrdmxxxVO> sfzrdxxList = cxSfzrdxxReqVO.getData().getSfzrdmxxx();
            //城镇土地
            if ("10112".equals(zsxmDm)) {
                //根据税种、品目、认定有效期过滤
                final List<SfzrdmxxxVO> cztdsysSfzrdxxList = sfzrdxxList.stream()
                        .filter(f -> "10112".equals(f.getZsxmDm()) && !dqrq.before(f.getRdyxqq())
                                && !dqrq.after(f.getRdyxqz()) && zspmDm.equals(f.getZspmDm()))
                        .collect(Collectors.toList());
                if(GyUtils.isNotNull(cztdsysSfzrdxxList)){
                    returnList.add(cztdsysSfzrdxxList.get(0));
                }
            }
            //房产
            if ("10110".equals(zsxmDm)) {
                //先查从价的
                final List<SfzrdmxxxVO> cjfzrdxxList = sfzrdxxList.stream()
                        .filter(f -> "10110".equals(f.getZsxmDm()) && !dqrq.before(f.getRdyxqq())
                                && !dqrq.after(f.getRdyxqz()) && "101100700".equals(f.getZspmDm()))
                        .collect(Collectors.toList());
                //if有从租
                if (czzt) {
                    if(GyUtils.isNotNull(cjfzrdxxList)){
                        final SfzrdmxxxVO cjsfrdxx = cjfzrdxxList.get(0);

                        final List<SfzrdmxxxVO> czfzrdxxList = sfzrdxxList.stream()
                                .filter(f -> "10110".equals(f.getZsxmDm()) && !dqrq.before(f.getRdyxqq())
                                        && !dqrq.after(f.getRdyxqz()) && "101100800".equals(f.getZspmDm()))
                                .collect(Collectors.toList());
                        //从租
                        if(GyUtils.isNotNull(czfzrdxxList)){
                            if(!cjsfrdxx.getNsqxDm().equals(czfzrdxxList.get(0).getNsqxDm()) || !cjsfrdxx.getSbqxDm().equals(czfzrdxxList.get(0).getSbqxDm())){
                                returnList.add(czfzrdxxList.get(0));
                            }else{
                                cjsfrdxx.setZspmDm("");
                            }
                        }
                        returnList.add(cjsfrdxx);
                    }else{
                        final List<SfzrdmxxxVO> czfzrdxxList = sfzrdxxList.stream()
                                .filter(f -> "10110".equals(f.getZsxmDm()) && !dqrq.before(f.getRdyxqq())
                                        && !dqrq.after(f.getRdyxqz()) && "101100800".equals(f.getZspmDm()))
                                .collect(Collectors.toList());
                        if(GyUtils.isNotNull(czfzrdxxList)){
                            returnList.add(czfzrdxxList.get(0));
                        }
                    }
                }else {
                    if(GyUtils.isNotNull(cjfzrdxxList)){
                        returnList.add(cjfzrdxxList.get(0));
                    }
                }
            }
        }
        return returnList;
    }

    @Override
    public SsycPageResVO pageListSsyc(SsyccxReqVO reqVO) {
        SsycPageResVO result = new SsycPageResVO();
        String tabVal = reqVO.getTabVal();
        if ("0".equals(tabVal)) {
            // 汇总表
            PageResult<ZnsbNssbSsycHzbDO> pageResult = ssycHzbMapper.queryHzbPageList(reqVO);
            ZnsbNssbSsycHzbDO hj = ssycHzbMapper.queryHzbHj(reqVO);
            result.setPageResult(pageResult);
            result.setHj(hj);
        } else if ("1".equals(tabVal)) {
            // 增值税
            PageResult<ZnsbNssbSsycZzsDO> pageResult = ssycZzsMapper.queryZzsPageList(reqVO);
            ZnsbNssbSsycZzsDO hj = ssycZzsMapper.queryZzsHj(reqVO);
            result.setPageResult(pageResult);
            result.setHj(hj);
        } else if ("2".equals(tabVal)) {
            // 企业所得税
            PageResult<ZnsbNssbSsycQysdsDO> pageResult = ssycQysdsMapper.queryQysdsPageList(reqVO);
            ZnsbNssbSsycQysdsDO hj = ssycQysdsMapper.queryQysdsHj(reqVO);
            result.setPageResult(pageResult);
            result.setHj(hj);
        }
        return result;
    }

    @Override
    public SsycUpdateTzjeResVO updateTzje(SsycUpdateTzjeReqVO reqVO) {
        SsycUpdateTzjeResVO resVO = new SsycUpdateTzjeResVO();
        String tabVal = reqVO.getTabVal();
        if ("1".equals(tabVal)) {
            ZnsbNssbSsycZzsDO zzsDO = ssycZzsMapper.selectById(reqVO.getUuid());
            if (GyUtils.isNull(zzsDO)) {
                throw new ServiceException(-1, "更新失败，数据不存在，请重新查询数据");
            }
            String qydmz = zzsDO.getQydmz();
            String ssq = zzsDO.getSsq();
            ZnsbNssbSsycHzbDO hzb = ssycHzbMapper.getHzbByQydmzAndSsq(qydmz, ssq);
            if (GyUtils.isNull(hzb)) {
                hzb = new ZnsbNssbSsycHzbDO();
                hzb.setQydmz(qydmz);
                hzb.setSsq(ssq);
            }
            SsycBO ssycBO = new SsycBO();
            BeanUtils.copyBean(hzb, ssycBO);
            BeanUtils.copyBean(zzsDO, ssycBO);
            ZnsbMhzcQyjbxxmxReqVO qyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            qyjbxxmxReqVO.setQydmz(qydmz);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByQydmz(qyjbxxmxReqVO);
            ZnsbMhzcQyjbxxmxResVO nsrxxData = nsrxxResult.getData();
            List<BigDecimal> fjsslList = CollectionUtil.emptyIfNull(nsrxxData.getSfzrdmxxx()).stream()
                    .filter(t -> "10109".equals(t.getZsxmDm())
                            || "30203".equals(t.getZsxmDm())
                            || "30216".equals(t.getZsxmDm()))
                    .filter(t -> !"11".equals(t.getNsqxDm()))
                    .map(SfzrdmxxxVO::getSlhdwse)
                    .collect(Collectors.toList());
            ssycBO.setFjsslList(fjsslList);
            ssycBO.setNsrlx(nsrxxData.getNsrzgxx().get(0).getNsrlx());
            ssycBO.setTzje2(NumberUtil.nullToZero(reqVO.getTzje()));

            String nsrsbh = nsrxxData.getJbxxmxsj().get(0).getNsrsbh();
            CommonResult<List<CompanyDTO>> jgxxResult = companyApi.getjgxxsByNsrsbh(nsrsbh);
            String djxh = CollectionUtil.emptyIfNull(jgxxResult.getData()).stream()
                    .filter(t -> "1".equals(t.getQylxz()))
                    .map(CompanyDTO::getDjxh)
                    .findFirst()
                    .orElse(null);
            ssycBO.setLslfjmbz(getLslfjmbz(djxh, nsrsbh, ssycBO.getNsrlx()));
            BeanUtils.copyBean(ssycBO, zzsDO);
            ssycZzsMapper.updateById(zzsDO);
            BeanUtils.copyBean(ssycBO, hzb);
            ssycHzbMapper.addOrUpdate(hzb);
            BeanUtils.copyBean(ssycBO, resVO);
            ZnsbNssbSsycZzsDO hj = ssycZzsMapper.queryZzsHj(reqVO.getCxtj());
            resVO.setHj(hj);
        } else if ("2".equals(tabVal)) {
            ZnsbNssbSsycQysdsDO qysdsDO = ssycQysdsMapper.selectById(reqVO.getUuid());
            if (GyUtils.isNull(qysdsDO)) {
                throw new ServiceException(-1, "更新失败，数据不存在，请重新查询数据");
            }
            String qydmz = qysdsDO.getQydmz();
            String ssq = qysdsDO.getSsq();
            String lxh = reqVO.getLxh();
            ZnsbNssbSsycHzbDO hzb = ssycHzbMapper.getHzbByQydmzAndSsq(qydmz, ssq);
            if (GyUtils.isNull(hzb)) {
                hzb = new ZnsbNssbSsycHzbDO();
                hzb.setQydmz(qydmz);
                hzb.setSsq(ssq);
            }
            SsycBO ssycBO = new SsycBO();
            BeanUtils.copyBean(hzb, ssycBO);
            BeanUtils.copyBean(qysdsDO, ssycBO);
            ZnsbMhzcQyjbxxmxReqVO qyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            qyjbxxmxReqVO.setQydmz(qydmz);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByQydmz(qyjbxxmxReqVO);
            ZnsbMhzcQyjbxxmxResVO nsrxxData = nsrxxResult.getData();
            List<BigDecimal> fjsslList = CollectionUtil.emptyIfNull(nsrxxData.getSfzrdmxxx()).stream()
                    .filter(t -> "10109".equals(t.getZsxmDm())
                            || "30203".equals(t.getZsxmDm())
                            || "30216".equals(t.getZsxmDm()))
                    .filter(t -> !"11".equals(t.getNsqxDm()))
                    .map(SfzrdmxxxVO::getSlhdwse)
                    .collect(Collectors.toList());
            ssycBO.setFjsslList(fjsslList);
            ssycBO.setNsrlx(nsrxxData.getNsrzgxx().get(0).getNsrlx());
            if ("3".equals(lxh)) {
                ssycBO.setLrtzje(NumberUtil.nullToZero(reqVO.getTzje()));
            } else if ("14".equals(lxh)) {
                ssycBO.setQtse(NumberUtil.nullToZero(reqVO.getTzje()));
            } else if ("13".equals(lxh)) {
                ssycBO.setYfjjkc(NumberUtil.nullToZero(reqVO.getTzje()));
            }

            String nsrsbh = nsrxxData.getJbxxmxsj().get(0).getNsrsbh();
            CommonResult<List<CompanyDTO>> jgxxResult = companyApi.getjgxxsByNsrsbh(nsrsbh);
            String djxh = CollectionUtil.emptyIfNull(jgxxResult.getData()).stream()
                    .filter(t -> "1".equals(t.getQylxz()))
                    .map(CompanyDTO::getDjxh)
                    .findFirst()
                    .orElse(null);
            ssycBO.setLslfjmbz(getLslfjmbz(djxh, nsrsbh, ssycBO.getNsrlx()));

            BeanUtils.copyBean(ssycBO, qysdsDO);
            ssycQysdsMapper.updateById(qysdsDO);
            BeanUtils.copyBean(ssycBO, hzb);
            ssycHzbMapper.addOrUpdate(hzb);
            BeanUtils.copyBean(ssycBO, resVO);
            ZnsbNssbSsycQysdsDO hj = ssycQysdsMapper.queryQysdsHj(reqVO.getCxtj());
            resVO.setHj(hj);
        }
        return resVO;
    }

    @Override
    public void exportSsycData(SsyccxReqVO reqVO, HttpServletResponse response) {
        String tabVal = reqVO.getTabVal();
        String templatePath = File.separator + "templates" + File.separator + "ssyc" + File.separator;
        String fileName = "";
        if ("0".equals(tabVal)) {
            templatePath = templatePath + "ssyc_hzb_export_template.xlsx";
            fileName = "税收预测汇总表";
        } else if ("1".equals(tabVal)) {
            templatePath = templatePath + "ssyc_zzs_export_template.xlsx";
            fileName = "税收预测增值税";
        } else if ("2".equals(tabVal)) {
            templatePath = templatePath + "ssyc_qysds_export_template.xlsx";
            fileName = "税收预测企业所得税";
        }

        ClassPathResource classPathResource = new ClassPathResource(templatePath);
        InputStream inputStream = classPathResource.getStream();

        SsycPageResVO exportData = pageListSsyc(reqVO);
        try {
            ServletOutputStream out = response.getOutputStream();
            //设置文件类型
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            //设置编码格式
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
            //创建excel
            ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(inputStream).build();
            // 创建sheet
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            if ("0".equals(tabVal)) {
                List<SsycHzbExcelVO> hzbExcelDataList = BeanUtils.toBean(exportData.getPageResult().getList(), SsycHzbExcelVO.class);
                for (int i = 1; i <= hzbExcelDataList.size(); i++) {
                    hzbExcelDataList.get(i - 1).setXh(i + "");
                }
                SsycHzbExcelVO hj = BeanUtils.toBean(exportData.getHj(), SsycHzbExcelVO.class);
                hj.setQydmz("合计");
                hzbExcelDataList.add(hzbExcelDataList.size(), hj);
                excelWriter.write(hzbExcelDataList, writeSheet);
            } else if ("1".equals(tabVal)) {
                List<SsycZzsExcelVO> zzsExcelDataList = BeanUtils.toBean(exportData.getPageResult().getList(), SsycZzsExcelVO.class);
                for (int i = 1; i <= zzsExcelDataList.size(); i++) {
                    zzsExcelDataList.get(i - 1).setXh(i + "");
                }
                SsycZzsExcelVO hj = BeanUtils.toBean(exportData.getHj(), SsycZzsExcelVO.class);
                hj.setQydmz("合计");
                zzsExcelDataList.add(zzsExcelDataList.size(), hj);
                excelWriter.write(zzsExcelDataList, writeSheet);
            } else if ("2".equals(tabVal)) {
                List<SsycQysdsExcelVO> qysdsExcelDataList = BeanUtils.toBean(exportData.getPageResult().getList(), SsycQysdsExcelVO.class);
                for (int i = 1; i <= qysdsExcelDataList.size(); i++) {
                    qysdsExcelDataList.get(i - 1).setXh(i + "");
                }
                SsycQysdsExcelVO hj = BeanUtils.toBean(exportData.getHj(), SsycQysdsExcelVO.class);
                hj.setQydmz("合计");
                qysdsExcelDataList.add(qysdsExcelDataList.size(), hj);
                excelWriter.write(qysdsExcelDataList, writeSheet);
            }
            //填充完成
            excelWriter.finish();
            out.flush();
        } catch (Exception e) {
            log.info("导出失败", e);
        }
    }
}
