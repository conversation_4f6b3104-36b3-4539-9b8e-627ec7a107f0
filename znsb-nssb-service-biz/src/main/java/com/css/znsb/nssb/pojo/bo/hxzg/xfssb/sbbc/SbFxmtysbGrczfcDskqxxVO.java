
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 个人出租房产代收情况VO
 * 
 * <p>Java class for sbFxmtysbGrczfcDskqxxVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="sbFxmtysbGrczfcDskqxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="uuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid" minOccurs="0"/>
 *         &lt;element name="dssbuuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid" minOccurs="0"/>
 *         &lt;element name="bdssbuuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid" minOccurs="0"/>
 *         &lt;element name="acczrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq" minOccurs="0"/>
 *         &lt;element name="czyear" type="{http://www.chinatax.gov.cn/dataspec/}hyDm" minOccurs="0"/>
 *         &lt;element name="czmonth" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dsje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh" minOccurs="0"/>
 *         &lt;element name="zsxmDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmDm" minOccurs="0"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm" minOccurs="0"/>
 *         &lt;element name="zszmDm" type="{http://www.chinatax.gov.cn/dataspec/}zszmDm" minOccurs="0"/>
 *         &lt;element name="zfbz1" type="{http://www.chinatax.gov.cn/dataspec/}zfbz1" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbFxmtysbGrczfcDskqxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "uuid",
    "dssbuuid",
    "bdssbuuid",
    "acczrq",
    "czyear",
    "czmonth",
    "dsje",
    "djxh",
    "zsxmDm",
    "zspmDm",
    "zszmDm",
    "zfbz1"
})
public class SbFxmtysbGrczfcDskqxxVO
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String uuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dssbuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bdssbuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String acczrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String czyear;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String czmonth;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dsje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zszmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfbz1;

    /**
     * Gets the value of the uuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * Sets the value of the uuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUuid(String value) {
        this.uuid = value;
    }

    /**
     * Gets the value of the dssbuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDssbuuid() {
        return dssbuuid;
    }

    /**
     * Sets the value of the dssbuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDssbuuid(String value) {
        this.dssbuuid = value;
    }

    /**
     * Gets the value of the bdssbuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBdssbuuid() {
        return bdssbuuid;
    }

    /**
     * Sets the value of the bdssbuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBdssbuuid(String value) {
        this.bdssbuuid = value;
    }

    /**
     * Gets the value of the acczrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcczrq() {
        return acczrq;
    }

    /**
     * Sets the value of the acczrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcczrq(String value) {
        this.acczrq = value;
    }

    /**
     * Gets the value of the czyear property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzyear() {
        return czyear;
    }

    /**
     * Sets the value of the czyear property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzyear(String value) {
        this.czyear = value;
    }

    /**
     * Gets the value of the czmonth property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzmonth() {
        return czmonth;
    }

    /**
     * Sets the value of the czmonth property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzmonth(String value) {
        this.czmonth = value;
    }

    /**
     * Gets the value of the dsje property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDsje() {
        return dsje;
    }

    /**
     * Sets the value of the dsje property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDsje(Double value) {
        this.dsje = value;
    }

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the zsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmDm() {
        return zsxmDm;
    }

    /**
     * Sets the value of the zsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmDm(String value) {
        this.zsxmDm = value;
    }

    /**
     * Gets the value of the zspmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * Sets the value of the zspmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * Gets the value of the zszmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZszmDm() {
        return zszmDm;
    }

    /**
     * Sets the value of the zszmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZszmDm(String value) {
        this.zszmDm = value;
    }

    /**
     * Gets the value of the zfbz1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfbz1() {
        return zfbz1;
    }

    /**
     * Sets the value of the zfbz1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfbz1(String value) {
        this.zfbz1 = value;
    }

}
