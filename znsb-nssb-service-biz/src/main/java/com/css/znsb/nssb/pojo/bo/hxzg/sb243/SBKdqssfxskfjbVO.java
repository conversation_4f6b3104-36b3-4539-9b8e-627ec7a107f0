
package com.css.znsb.nssb.pojo.bo.hxzg.sb243;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 跨地区税收分享税款分解表
 * 
 * <p>Java class for SBKdqssfxskfjbVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBKdqssfxskfjbVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="uuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid"/>
 *         &lt;element name="pzxh" type="{http://www.chinatax.gov.cn/dataspec/}pzxh"/>
 *         &lt;element name="sbuuid" type="{http://www.chinatax.gov.cn/dataspec/}sbuuid"/>
 *         &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm"/>
 *         &lt;element name="zgswjgDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswjgDm"/>
 *         &lt;element name="zsxmDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmDm"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm" minOccurs="0"/>
 *         &lt;element name="fpbl" type="{http://www.chinatax.gov.cn/dataspec/}fjbl"/>
 *         &lt;element name="bqynse" type="{http://www.chinatax.gov.cn/dataspec/}bqynse"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="jzjtskbz" type="{http://www.chinatax.gov.cn/dataspec/}jzjtskbz" minOccurs="0"/>
 *         &lt;element name="sfzcdbz" type="{http://www.chinatax.gov.cn/dataspec/}sfzcdbz"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBKdqssfxskfjbVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "uuid",
    "pzxh",
    "sbuuid",
    "xzqhszDm",
    "zgswjgDm",
    "zsxmDm",
    "zspmDm",
    "fpbl",
    "bqynse",
    "lrrDm",
    "lrrq",
    "xgrDm",
    "xgrq",
    "sjgsdq",
    "jzjtskbz",
    "sfzcdbz",
    "zgswjgmc",
    "sbxxuuid",
    "kjdjxh",
    "kjnsrmc"
})
public class SBKdqssfxskfjbVO implements Serializable{

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 6610112085796291973L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String uuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String pzxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sbuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xzqhszDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zgswjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fpbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bqynse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jzjtskbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sfzcdbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zgswjgmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbxxuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjdjxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjnsrmc;

    /**
     *创建时间:2015-9-10下午11:49:08
     *get方法
     * @return the sbxxuuid
     */
    public String getSbxxuuid() {
        return sbxxuuid;
    }

    /**
     * 创建时间:2015-9-10下午11:49:08
     * set方法
     * @param sbxxuuid the sbxxuuid to set
     */
    public void setSbxxuuid(String sbxxuuid) {
        this.sbxxuuid = sbxxuuid;
    }

    /**
     * Gets the value of the uuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * Sets the value of the uuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUuid(String value) {
        this.uuid = value;
    }

    /**
     * Gets the value of the pzxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPzxh() {
        return pzxh;
    }

    /**
     * Sets the value of the pzxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPzxh(String value) {
        this.pzxh = value;
    }

    /**
     * Gets the value of the sbuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbuuid() {
        return sbuuid;
    }

    /**
     * Sets the value of the sbuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbuuid(String value) {
        this.sbuuid = value;
    }

    /**
     * Gets the value of the xzqhszDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }

    /**
     * Sets the value of the xzqhszDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhszDm(String value) {
        this.xzqhszDm = value;
    }

    /**
     * Gets the value of the zgswjgDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswjgDm() {
        return zgswjgDm;
    }

    /**
     * Sets the value of the zgswjgDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswjgDm(String value) {
        this.zgswjgDm = value;
    }

    /**
     * Gets the value of the zsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmDm() {
        return zsxmDm;
    }

    /**
     * Sets the value of the zsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmDm(String value) {
        this.zsxmDm = value;
    }

    /**
     * Gets the value of the zspmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * Sets the value of the zspmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * Gets the value of the fpbl property.
     * 
     */
    public double getFpbl() {
        return fpbl;
    }

    /**
     * Sets the value of the fpbl property.
     * 
     */
    public void setFpbl(double value) {
        this.fpbl = value;
    }

    /**
     * Gets the value of the bqynse property.
     * 
     */
    public double getBqynse() {
        return bqynse;
    }

    /**
     * Sets the value of the bqynse property.
     * 
     */
    public void setBqynse(double value) {
        this.bqynse = value;
    }

    /**
     * Gets the value of the lrrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * Sets the value of the lrrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * Gets the value of the lrrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * Sets the value of the lrrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * Gets the value of the xgrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * Sets the value of the xgrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * Gets the value of the xgrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * Sets the value of the xgrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * Gets the value of the sjgsdq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * Sets the value of the sjgsdq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * Gets the value of the jzjtskbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJzjtskbz() {
        return jzjtskbz;
    }

    /**
     * Sets the value of the jzjtskbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJzjtskbz(String value) {
        this.jzjtskbz = value;
    }

    /**
     * Gets the value of the sfzcdbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfzcdbz() {
        return sfzcdbz;
    }

    /**
     * Sets the value of the sfzcdbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfzcdbz(String value) {
        this.sfzcdbz = value;
    }

    /**
     * Gets the value of the zgswjgmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswjgmc() {
        return zgswjgmc;
    }

    /**
     * Sets the value of the zgswjgmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswjgmc(String zgswjgmc) {
        this.zgswjgmc = zgswjgmc;
    }

    /**
     * Gets the value of the kjdjxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKjdjxh() {
        return kjdjxh;
    }

    /**
     * Sets the value of the kjdjxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKjdjxh(String kjdjxh) {
        this.kjdjxh = kjdjxh;
    }

    /**
     * Gets the value of the kjnsrmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKjnsrmc() {
        return kjnsrmc;
    }

    /**
     * Sets the value of the kjnsrmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKjnsrmc(String kjnsrmc) {
        this.kjnsrmc = kjnsrmc;
    }
    
}
