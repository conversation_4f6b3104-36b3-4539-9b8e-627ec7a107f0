package com.css.znsb.nssb.pojo.domain.xfssb;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 委托加工收回的应税消费品领用存情况
 * @TableName sb_xfs_fb_wtjghsdysxfplyqk
 */
@TableName(value ="sb_xfs_fb_wtjghsdysxfplyqk")
@Data
public class SbXfsFbWtjghsdysxfplyqkDO implements Serializable {
    /**
     * UUID||uuid
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 申报UUID
     */
    @TableField(value = "sbuuid")
    private String sbuuid;

    /**
     * 凭证序号
     */
    @TableField(value = "pzxh")
    private String pzxh;

    /**
     * 应税消费品名称
     */
    @TableField(value = "ysxfpmc")
    private String ysxfpmc;

    /**
     * 商品和服务税收分类编码
     */
    @TableField(value = "sphfwssflbm")
    private String sphfwssflbm;

    /**
     * 适用税率
     */
    @TableField(value = "sysl")
    private BigDecimal sysl;

    /**
     * 上期扣除数量
     */
    @TableField(value = "sqkcsl")
    private BigDecimal sqkcsl;

    /**
     * 本期委托加工收回数量
     */
    @TableField(value = "bqwtjgshsl")
    private BigDecimal bqwtjgshsl;

    /**
     * 本期委托加工收回直接销售数量
     */
    @TableField(value = "bqwtjgshzjjgsl")
    private BigDecimal bqwtjgshzjjgsl;

    /**
     * 本期委托加工收回用于连续生产数量
     */
    @TableField(value = "bqwtjgshyylxscsl")
    private BigDecimal bqwtjgshyylxscsl;

    /**
     * 本期结存数量||本期结存数量
     */
    @TableField(value = "bqjcsl")
    private Long bqjcsl;

    /**
     * 征收品目代码
     */
    @TableField(value = "zspm_dm")
    private String zspmDm;

    /**
     * 本期结存数量
     */
    @TableField(value = "bqjcsl_1")
    private BigDecimal bqjcsl1;

    /**
     * 数据剥离标志||仅限于数据剥离时使用
     */
    @TableField(value = "sjblbz")
    private Integer sjblbz;

    /**
     * 业务渠道代码
     */
    @TableField(value = "ywqd_dm")
    private String ywqdDm;

    /**
     * 录入日期
     */
    @TableField(value = "lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField(value = "xgrq")
    private Date xgrq;

    /**
     * 数据产生地区
     */
    @TableField(value = "sjcsdq")
    private String sjcsdq;

    /**
     * 数据归属地区
     */
    @TableField(value = "sjgsdq")
    private String sjgsdq;

    /**
     * 修改人身份id
     */
    @TableField(value = "xgrsfid")
    private String xgrsfid;

    /**
     * 录入人身份id
     */
    @TableField(value = "lrrsfid")
    private String lrrsfid;

    /**
     * 数据同步时间
     */
    @TableField(value = "sjtb_sj")
    private Date sjtbSj;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SbXfsFbWtjghsdysxfplyqkDO other = (SbXfsFbWtjghsdysxfplyqkDO) that;
        return (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
            && (this.getSbuuid() == null ? other.getSbuuid() == null : this.getSbuuid().equals(other.getSbuuid()))
            && (this.getPzxh() == null ? other.getPzxh() == null : this.getPzxh().equals(other.getPzxh()))
            && (this.getYsxfpmc() == null ? other.getYsxfpmc() == null : this.getYsxfpmc().equals(other.getYsxfpmc()))
            && (this.getSphfwssflbm() == null ? other.getSphfwssflbm() == null : this.getSphfwssflbm().equals(other.getSphfwssflbm()))
            && (this.getSysl() == null ? other.getSysl() == null : this.getSysl().equals(other.getSysl()))
            && (this.getSqkcsl() == null ? other.getSqkcsl() == null : this.getSqkcsl().equals(other.getSqkcsl()))
            && (this.getBqwtjgshsl() == null ? other.getBqwtjgshsl() == null : this.getBqwtjgshsl().equals(other.getBqwtjgshsl()))
            && (this.getBqwtjgshzjjgsl() == null ? other.getBqwtjgshzjjgsl() == null : this.getBqwtjgshzjjgsl().equals(other.getBqwtjgshzjjgsl()))
            && (this.getBqwtjgshyylxscsl() == null ? other.getBqwtjgshyylxscsl() == null : this.getBqwtjgshyylxscsl().equals(other.getBqwtjgshyylxscsl()))
            && (this.getBqjcsl() == null ? other.getBqjcsl() == null : this.getBqjcsl().equals(other.getBqjcsl()))
            && (this.getZspmDm() == null ? other.getZspmDm() == null : this.getZspmDm().equals(other.getZspmDm()))
            && (this.getBqjcsl1() == null ? other.getBqjcsl1() == null : this.getBqjcsl1().equals(other.getBqjcsl1()))
            && (this.getSjblbz() == null ? other.getSjblbz() == null : this.getSjblbz().equals(other.getSjblbz()))
            && (this.getYwqdDm() == null ? other.getYwqdDm() == null : this.getYwqdDm().equals(other.getYwqdDm()))
            && (this.getLrrq() == null ? other.getLrrq() == null : this.getLrrq().equals(other.getLrrq()))
            && (this.getXgrq() == null ? other.getXgrq() == null : this.getXgrq().equals(other.getXgrq()))
            && (this.getSjcsdq() == null ? other.getSjcsdq() == null : this.getSjcsdq().equals(other.getSjcsdq()))
            && (this.getSjgsdq() == null ? other.getSjgsdq() == null : this.getSjgsdq().equals(other.getSjgsdq()))
            && (this.getXgrsfid() == null ? other.getXgrsfid() == null : this.getXgrsfid().equals(other.getXgrsfid()))
            && (this.getLrrsfid() == null ? other.getLrrsfid() == null : this.getLrrsfid().equals(other.getLrrsfid()))
            && (this.getSjtbSj() == null ? other.getSjtbSj() == null : this.getSjtbSj().equals(other.getSjtbSj()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getSbuuid() == null) ? 0 : getSbuuid().hashCode());
        result = prime * result + ((getPzxh() == null) ? 0 : getPzxh().hashCode());
        result = prime * result + ((getYsxfpmc() == null) ? 0 : getYsxfpmc().hashCode());
        result = prime * result + ((getSphfwssflbm() == null) ? 0 : getSphfwssflbm().hashCode());
        result = prime * result + ((getSysl() == null) ? 0 : getSysl().hashCode());
        result = prime * result + ((getSqkcsl() == null) ? 0 : getSqkcsl().hashCode());
        result = prime * result + ((getBqwtjgshsl() == null) ? 0 : getBqwtjgshsl().hashCode());
        result = prime * result + ((getBqwtjgshzjjgsl() == null) ? 0 : getBqwtjgshzjjgsl().hashCode());
        result = prime * result + ((getBqwtjgshyylxscsl() == null) ? 0 : getBqwtjgshyylxscsl().hashCode());
        result = prime * result + ((getBqjcsl() == null) ? 0 : getBqjcsl().hashCode());
        result = prime * result + ((getZspmDm() == null) ? 0 : getZspmDm().hashCode());
        result = prime * result + ((getBqjcsl1() == null) ? 0 : getBqjcsl1().hashCode());
        result = prime * result + ((getSjblbz() == null) ? 0 : getSjblbz().hashCode());
        result = prime * result + ((getYwqdDm() == null) ? 0 : getYwqdDm().hashCode());
        result = prime * result + ((getLrrq() == null) ? 0 : getLrrq().hashCode());
        result = prime * result + ((getXgrq() == null) ? 0 : getXgrq().hashCode());
        result = prime * result + ((getSjcsdq() == null) ? 0 : getSjcsdq().hashCode());
        result = prime * result + ((getSjgsdq() == null) ? 0 : getSjgsdq().hashCode());
        result = prime * result + ((getXgrsfid() == null) ? 0 : getXgrsfid().hashCode());
        result = prime * result + ((getLrrsfid() == null) ? 0 : getLrrsfid().hashCode());
        result = prime * result + ((getSjtbSj() == null) ? 0 : getSjtbSj().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", uuid=").append(uuid);
        sb.append(", sbuuid=").append(sbuuid);
        sb.append(", pzxh=").append(pzxh);
        sb.append(", ysxfpmc=").append(ysxfpmc);
        sb.append(", sphfwssflbm=").append(sphfwssflbm);
        sb.append(", sysl=").append(sysl);
        sb.append(", sqkcsl=").append(sqkcsl);
        sb.append(", bqwtjgshsl=").append(bqwtjgshsl);
        sb.append(", bqwtjgshzjjgsl=").append(bqwtjgshzjjgsl);
        sb.append(", bqwtjgshyylxscsl=").append(bqwtjgshyylxscsl);
        sb.append(", bqjcsl=").append(bqjcsl);
        sb.append(", zspmDm=").append(zspmDm);
        sb.append(", bqjcsl1=").append(bqjcsl1);
        sb.append(", sjblbz=").append(sjblbz);
        sb.append(", ywqdDm=").append(ywqdDm);
        sb.append(", lrrq=").append(lrrq);
        sb.append(", xgrq=").append(xgrq);
        sb.append(", sjcsdq=").append(sjcsdq);
        sb.append(", sjgsdq=").append(sjgsdq);
        sb.append(", xgrsfid=").append(xgrsfid);
        sb.append(", lrrsfid=").append(lrrsfid);
        sb.append(", sjtbSj=").append(sjtbSj);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}