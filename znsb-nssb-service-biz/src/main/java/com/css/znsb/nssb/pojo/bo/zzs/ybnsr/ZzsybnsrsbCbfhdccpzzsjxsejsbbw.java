package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《成本法核定农产品增值税进项税额计算表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbw", propOrder = { "zzsybnsrsbCbfhdccpzzsjxsejsb" })
@Getter
@Setter
public class ZzsybnsrsbCbfhdccpzzsjxsejsbbw extends TaxDoc {
    /**
     * 《成本法核定农产品增值税进项税额计算表》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_cbfhdccpzzsjxsejsb", required = true)
    @JSONField(name = "zzsybnsrsb_cbfhdccpzzsjxsejsb")
    protected ZzsybnsrsbCbfhdccpzzsjxsejsb zzsybnsrsbCbfhdccpzzsjxsejsb;
}