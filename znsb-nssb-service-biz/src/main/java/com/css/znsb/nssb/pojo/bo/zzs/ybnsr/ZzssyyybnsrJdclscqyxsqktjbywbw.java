package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车辆生产企业销售情况统计表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdclscqyxsqktjbywbw", propOrder = { "zzssyyybnsrJdclscqyxsqktjb" })
@Getter
@Setter
public class ZzssyyybnsrJdclscqyxsqktjbywbw extends TaxDoc {
    /**
     * 《机动车辆生产企业销售情况统计表》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_jdclscqyxsqktjb", required = true)
    @JSONField(name = "zzssyyybnsr_jdclscqyxsqktjb")
    protected ZzssyyybnsrJdclscqyxsqktjb zzssyyybnsrJdclscqyxsqktjb;
}