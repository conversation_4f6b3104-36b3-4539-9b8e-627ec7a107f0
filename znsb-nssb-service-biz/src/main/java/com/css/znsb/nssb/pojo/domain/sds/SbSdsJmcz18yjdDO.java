package com.css.znsb.nssb.pojo.domain.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 居民企业（查账征收）企业所得税月（季）度申报2018版
 * @TableName sb_sds_jmcz_18yjd
 */
@TableName(value ="sb_sds_jmcz_18yjd")
@Data
public class SbSdsJmcz18yjdDO implements Serializable {
    /**
     * UUID||uuid
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 凭证序号
     */
    @TableField(value = "pzxh")
    private String pzxh;

    /**
     * 申报UUID
     */
    @TableField(value = "sbuuid")
    private String sbuuid;

    /**
     * 期末从业人数
     */
    @TableField(value = "qmcyrs")
    private String qmcyrs;

    /**
     * 是否属于小型微利企业
     */
    @TableField(value = "sfsyxxwlqy")
    private String sfsyxxwlqy;

    /**
     * 是否科技型中小企业
     */
    @TableField(value = "sfkjxzxqy")
    private String sfkjxzxqy;

    /**
     * 是否高新技术企业
     */
    @TableField(value = "sfgxjsqy")
    private String sfgxjsqy;

    /**
     * 是否发生技术入股递延纳税事项
     */
    @TableField(value = "sffsjsrgdynssx")
    private String sffsjsrgdynssx;

    /**
     * 营业收入累计
     */
    @TableField(value = "yysr_lj")
    private BigDecimal yysrLj;

    /**
     * 营业成本累计
     */
    @TableField(value = "yycb_lj")
    private BigDecimal yycbLj;

    /**
     * 利润总额累计
     */
    @TableField(value = "lrze_lj")
    private BigDecimal lrzeLj;

    /**
     * 特定业务计算的应纳税所得额累计
     */
    @TableField(value = "tdywjsdynssde_lj")
    private BigDecimal tdywjsdynssdeLj;

    /**
     * 不征税收入累计
     */
    @TableField(value = "bzssr_lj")
    private BigDecimal bzssrLj;

    /**
     * 免税收入累计
     */
    @TableField(value = "mssr_lj")
    private BigDecimal mssrLj;

    /**
     * 固定资产加速折旧（扣除）调减额累计金额
     */
    @TableField(value = "gdzcjszjkctje_lj")
    private BigDecimal gdzcjszjkctjeLj;

    /**
     * 弥补以前年度亏损累计
     */
    @TableField(value = "mbyqndks_lj")
    private BigDecimal mbyqndksLj;

    /**
     * 实际利润额累计
     */
    @TableField(value = "sjlre_lj")
    private BigDecimal sjlreLj;

    /**
     * 税率累计
     */
    @TableField(value = "sl_lj")
    private BigDecimal slLj;

    /**
     * 应纳所得税额累计
     */
    @TableField(value = "ynsdse_lj")
    private BigDecimal ynsdseLj;

    /**
     * 减免所得税额累计
     */
    @TableField(value = "jmsdse_lj")
    private BigDecimal jmsdseLj;

    /**
     * 实际已缴所得税额累计
     */
    @TableField(value = "sjyyjsdse_lj")
    private BigDecimal sjyyjsdseLj;

    /**
     * 特定业务预缴（征）所得税额累计
     */
    @TableField(value = "tdywyjzsdse_lj")
    private BigDecimal tdywyjzsdseLj;

    /**
     * 应补（退）所得税额累计
     */
    @TableField(value = "ybtsdse_lj")
    private BigDecimal ybtsdseLj;

    /**
     * 总机构本期分摊应补（退）所得税额
     */
    @TableField(value = "zjgybtsdse_bq")
    private BigDecimal zjgybtsdseBq;

    /**
     * 总机构应分摊所得税额本期
     */
    @TableField(value = "zjgyftsdse_bq")
    private BigDecimal zjgyftsdseBq;

    /**
     * 总机构独立生产经营部门应分摊所得税额本期
     */
    @TableField(value = "zjgdlscjybmyftsdse_bq")
    private BigDecimal zjgdlscjybmyftsdseBq;

    /**
     * 分配比例
     */
    @TableField(value = "fpbl")
    private BigDecimal fpbl;

    /**
     * 分支机构分配所得税额本期
     */
    @TableField(value = "fzjgfpsdse_bq")
    private BigDecimal fzjgfpsdseBq;

    /**
     * 录入日期
     */
    @TableField(value = "lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField(value = "xgrq")
    private Date xgrq;

    /**
     * 数据归属地区
     */
    @TableField(value = "sjgsdq")
    private String sjgsdq;

    /**
     * 法定代表人（负责人）
     */
    @TableField(value = "fddbr")
    private String fddbr;

    /**
     * 法定代表人签字日期
     */
    @TableField(value = "fddbrqzrq")
    private Date fddbrqzrq;

    /**
     * 会计主管
     */
    @TableField(value = "kjzg")
    private String kjzg;

    /**
     * 代理申报中介机构
     */
    @TableField(value = "dlsbzjjg")
    private String dlsbzjjg;

    /**
     * 代理申报日期
     */
    @TableField(value = "dlsbrq")
    private Date dlsbrq;

    /**
     * 经办人
     */
    @TableField(value = "jbr")
    private String jbr;

    /**
     * 经办人执业证件号码
     */
    @TableField(value = "jbrzyzjhm")
    private String jbrzyzjhm;

    /**
     * 受理人姓名
     */
    @TableField(value = "slr")
    private String slr;

    /**
     * 受理日期
     */
    @TableField(value = "slrq")
    private Date slrq;

    /**
     * 主管税务机关||主管税务机关
     */
    @TableField(value = "zgswjg")
    private String zgswjg;

    /**
     * 数据同步时间
     */
    @TableField(value = "sjtb_sj")
    private Date sjtbSj;

    /**
     * 预缴方式||1按照实际利润额预缴|2按照上一纳税年度应纳税所得额平均额预缴|3按照税务机关确定的其他方法预缴
     */
    @TableField(value = "yjfs")
    private String yjfs;

    /**
     * 企业类型||0一般企业|1跨地区经营汇总纳税企业总机构|2跨地区经营汇总纳税企业分支机构
     */
    @TableField(value = "sbqylx")
    private String sbqylx;

    /**
     * 总机构分摊比例||总机构分摊比例
     */
    @TableField(value = "zjgftbl")
    private BigDecimal zjgftbl;

    /**
     * 分支机构分摊比例||分支机构分摊比例
     */
    @TableField(value = "fzjgftbl")
    private BigDecimal fzjgftbl;

    /**
     * 总机构财政集中分摊比例||总机构财政集中分摊比例
     */
    @TableField(value = "zjgczjzftbl")
    private BigDecimal zjgczjzftbl;

    /**
     * 总机构具有主体生产经营职能部门分摊比例
     */
    @TableField(value = "dlscjybmftbl")
    private BigDecimal dlscjybmftbl;

    /**
     * 填表日期
     */
    @TableField(value = "tbrq_1")
    private Date tbrq1;

    /**
     * 是否代理申报
     */
    @TableField(value = "sfdlsb")
    private String sfdlsb;

    /**
     * 办理人
     */
    @TableField(value = "blr")
    private String blr;

    /**
     * 办理人员身份证件类型
     */
    @TableField(value = "blrysfzjlx_dm")
    private String blrysfzjlxDm;

    /**
     * 办理人员身份证件号码
     */
    @TableField(value = "blrysfzjhm")
    private String blrysfzjhm;

    /**
     * 财政集中分配所得税额本期
     */
    @TableField(value = "czjzfpsdse_bq")
    private BigDecimal czjzfpsdseBq;

    /**
     * 分支机构应分摊所得税额本期
     */
    @TableField(value = "fzjgyftsdse_bq")
    private BigDecimal fzjgyftsdseBq;

    /**
     * 行政区划数字代码
     */
    @TableField(value = "xzqhsz_dm")
    private String xzqhszDm;

    /**
     * 数据剥离标志||仅限于数据剥离时使用
     */
    @TableField(value = "sjblbz")
    private Integer sjblbz;

    /**
     * 期初资产总额（季初资产总额）||期初资产总额（季初资产总额）
     */
    @TableField(value = "qczcze")
    private BigDecimal qczcze;

    /**
     * 期末资产总额（季末资产总额）||期末资产总额（季末资产总额）
     */
    @TableField(value = "qmzcze")
    private BigDecimal qmzcze;

    /**
     * 期初从业人数（季初从业人数）||期初从业人数（季初从业人数）
     */
    @TableField(value = "qccyrs")
    private String qccyrs;

    /**
     * 国家限制和禁止行业
     */
    @TableField(value = "gjxzhjzhy")
    private String gjxzhjzhy;

    /**
     * 代理机构统一社会信用代码||代理机构统一社会信用代码
     */
    @TableField(value = "dljgtyshxydm")
    private String dljgtyshxydm;

    /**
     * 一季度起从业人数
     */
    @TableField(value = "qccyrs1")
    private String qccyrs1;

    /**
     * 一季度期末从业人数
     */
    @TableField(value = "qmcyrs1")
    private String qmcyrs1;

    /**
     * 二季度期初从业人数
     */
    @TableField(value = "qccyrs2")
    private String qccyrs2;

    /**
     * 二季度期末从业人数
     */
    @TableField(value = "qmcyrs2")
    private String qmcyrs2;

    /**
     * 三季度期初从业人数
     */
    @TableField(value = "qccyrs3")
    private String qccyrs3;

    /**
     * 三季度期末从业人数
     */
    @TableField(value = "qmcyrs3")
    private String qmcyrs3;

    /**
     * 四季度期初从业人数
     */
    @TableField(value = "qccyrs4")
    private String qccyrs4;

    /**
     * 四季度期末从业人数
     */
    @TableField(value = "qmcyrs4")
    private String qmcyrs4;

    /**
     * 企业从业人数全年平均人数
     */
    @TableField(value = "qycyrs_qnpjrs")
    private BigDecimal qycyrsQnpjrs;

    /**
     * 一季度期初资产总额
     */
    @TableField(value = "qczcze1")
    private BigDecimal qczcze1;

    /**
     * 一季度期末资产总额
     */
    @TableField(value = "qmzcze1")
    private BigDecimal qmzcze1;

    /**
     * 二季度期初资产总额
     */
    @TableField(value = "qczcze2")
    private BigDecimal qczcze2;

    /**
     * 二季度资产总额
     */
    @TableField(value = "qmzcze2")
    private BigDecimal qmzcze2;

    /**
     * 三季度期初资产总额
     */
    @TableField(value = "qczcze3")
    private BigDecimal qczcze3;

    /**
     * 三季度资产总额
     */
    @TableField(value = "qmzcze3")
    private BigDecimal qmzcze3;

    /**
     * 四季度期初资产总额
     */
    @TableField(value = "qczcze4")
    private BigDecimal qczcze4;

    /**
     * 四季度期末资产总额
     */
    @TableField(value = "qmzcze4")
    private BigDecimal qmzcze4;

    /**
     * 资产总额全年平均数
     */
    @TableField(value = "zcze_qnpjs")
    private BigDecimal zczeQnpjs;

    /**
     * 是否延缓缴纳所得税
     */
    @TableField(value = "yhjnsds")
    private String yhjnsds;

    /**
     * 符合条件的小型微利企业延缓缴纳所得税额
     */
    @TableField(value = "fhtjxwqyyhjzsdse_lj")
    private BigDecimal fhtjxwqyyhjzsdseLj;

    /**
     * 不缓征说明
     */
    @TableField(value = "bhzsm")
    private String bhzsm;

    /**
     * 所得减免累计金额
     */
    @TableField(value = "sdjm_lj")
    private BigDecimal sdjmLj;

    /**
     * 中央级收入实际应纳税额[本期]
     */
    @TableField(value = "zyjsrsjynse_bq")
    private BigDecimal zyjsrsjynseBq;

    /**
     * 地方级收入应纳税额[本期]
     */
    @TableField(value = "dfjsrynse_bq")
    private BigDecimal dfjsrynseBq;

    /**
     * 减征免征类型||减征免征类型1减征2免征
     */
    @TableField(value = "jzmzlx")
    private String jzmzlx;

    /**
     * 减征幅度
     */
    @TableField(value = "jzfd")
    private BigDecimal jzfd;

    /**
     * 民族自治地区本期实际减免金额
     */
    @TableField(value = "mzzzdqdfjm_bq")
    private BigDecimal mzzzdqdfjmBq;

    /**
     * 民族自治地区本年累计应减免金额
     */
    @TableField(value = "mzzzdqdfjm_lj")
    private BigDecimal mzzzdqdfjmLj;

    /**
     * 民族自治地区地方减免总分机构减免合计累计值
     */
    @TableField(value = "mzzzdqdfjmzfjghj_lj")
    private BigDecimal mzzzdqdfjmzfjghjLj;

    /**
     * 地方级收入实际应纳税额[本期]
     */
    @TableField(value = "dfjsrsjynse_bq")
    private BigDecimal dfjsrsjynseBq;

    /**
     * 实际应补（退）所得税额[本期]
     */
    @TableField(value = "sjybtsdse_bq")
    private BigDecimal sjybtsdseBq;

    /**
     * 减征幅度（8位精度）
     */
    @TableField(value = "jzfd_8wjd")
    private BigDecimal jzfd8wjd;

    /**
     * 分支机构全年累计分配金额
     */
    @TableField(value = "fzjgqnljfpje")
    private BigDecimal fzjgqnljfpje;

    /**
     * 分支机构全年累计应享受民族地方优惠金额
     */
    @TableField(value = "fzjgqnljyxsmzdfyhje")
    private BigDecimal fzjgqnljyxsmzdfyhje;

    /**
     * 分支机构全年累计已享受民族地方优惠金额
     */
    @TableField(value = "fzjgqnljyxsmzdfyhje_1")
    private BigDecimal fzjgqnljyxsmzdfyhje1;

    /**
     * 不享受小微优惠理由
     */
    @TableField(value = "bxsxwyhly")
    private String bxsxwyhly;

    /**
     * 企业所得税预缴申报模式
     */
    @TableField(value = "qysdsyjsbms_dm")
    private String qysdsyjsbmsDm;

    /**
     * 录入人身份id
     */
    @TableField(value = "lrrsfid")
    private String lrrsfid;

    /**
     * 修改人身份id
     */
    @TableField(value = "xgrsfid")
    private String xgrsfid;

    /**
     * 业务渠道代码
     */
    @TableField(value = "ywqd_dm")
    private String ywqdDm;

    /**
     * 数据产生地区
     */
    @TableField(value = "sjcsdq")
    private String sjcsdq;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}