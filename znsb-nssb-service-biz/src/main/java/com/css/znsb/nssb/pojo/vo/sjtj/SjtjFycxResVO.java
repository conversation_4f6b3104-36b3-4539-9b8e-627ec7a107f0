package com.css.znsb.nssb.pojo.vo.sjtj;

import com.css.znsb.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "数据统计分页查询响应")
public class SjtjFycxResVO implements Serializable {
    private static final long serialVersionUID = -6156864847716822057L;

    @Schema(description = "合计")
    private Object hj;

    @Schema(description = "分页数据")
    private PageResult<?> pageResult;
}
