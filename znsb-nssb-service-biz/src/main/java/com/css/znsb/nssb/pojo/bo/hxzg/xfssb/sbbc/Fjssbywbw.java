
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 附加税申报业务报文
 * 
 * <p>Java class for fjssbywbw complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="fjssbywbw">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.chinatax.gov.cn/dataspec/}taxDoc">
 *       &lt;sequence>
 *         &lt;element name="fjssb" type="{http://www.chinatax.gov.cn/dataspec/}xfsfjssb"/>
 *         &lt;element name="fzjgfjssb" type="{http://www.chinatax.gov.cn/dataspec/}fzjgxfsfjssb"/>
 *         &lt;element name="jsyjxgxz" type="{http://www.chinatax.gov.cn/dataspec/}jsyjxgxz" minOccurs="0"/>
 *         &lt;element name="jsyjxgyy" type="{http://www.chinatax.gov.cn/dataspec/}jsyjxgyy" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "fjssbywbw", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "fjssb",
    "fzjgfjssb",
    "jsyjxgxz",
    "jsyjxgyy",
    "bchssqq",
    "bchssqz",
    "bqsfsyxgmyhzc",
    "jzzcsyztDm",
    "syxgmjzzcqssj",
    "syxgmjzzczzsj"
})
public class Fjssbywbw
    extends TaxDoc
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected Xfsfjssb fjssb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected Fzjgxfsfjssb fzjgfjssb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jsyjxgxz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jsyjxgyy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bchssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bchssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bqsfsyxgmyhzc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jzzcsyztDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String syxgmjzzcqssj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String syxgmjzzczzsj;

    public String getBchssqq() {
        return bchssqq;
    }

    public void setBchssqq(String bchssqq) {
        this.bchssqq = bchssqq;
    }

    public String getBchssqz() {
        return bchssqz;
    }

    public void setBchssqz(String bchssqz) {
        this.bchssqz = bchssqz;
    }

    public String getBqsfsyxgmyhzc() {
        return bqsfsyxgmyhzc;
    }

    public void setBqsfsyxgmyhzc(String bqsfsyxgmyhzc) {
        this.bqsfsyxgmyhzc = bqsfsyxgmyhzc;
    }

    public String getJzzcsyztDm() {
        return jzzcsyztDm;
    }

    public void setJzzcsyztDm(String jzzcsyztDm) {
        this.jzzcsyztDm = jzzcsyztDm;
    }

    public String getSyxgmjzzcqssj() {
        return syxgmjzzcqssj;
    }

    public void setSyxgmjzzcqssj(String syxgmjzzcqssj) {
        this.syxgmjzzcqssj = syxgmjzzcqssj;
    }

    public String getSyxgmjzzczzsj() {
        return syxgmjzzczzsj;
    }

    public void setSyxgmjzzczzsj(String syxgmjzzczzsj) {
        this.syxgmjzzczzsj = syxgmjzzczzsj;
    }

    /**
     * Gets the value of the fjssb property.
     *
     * @return
     *     possible object is
     *     {@link Xfsfjssb }
     *
     */
    public Xfsfjssb getFjssb() {
        return fjssb;
    }

    /**
     * Sets the value of the fjssb property.
     *
     * @param value
     *     allowed object is
     *     {@link Xfsfjssb }
     *
     */
    public void setFjssb(Xfsfjssb value) {
        this.fjssb = value;
    }

    /**
     * Gets the value of the fzjgfjssb property.
     *
     * @return
     *     possible object is
     *     {@link Fzjgxfsfjssb }
     *
     */
    public Fzjgxfsfjssb getFzjgfjssb() {
        return fzjgfjssb;
    }

    /**
     * Sets the value of the fzjgfjssb property.
     *
     * @param value
     *     allowed object is
     *     {@link Fzjgxfsfjssb }
     *
     */
    public void setFzjgfjssb(Fzjgxfsfjssb value) {
        this.fzjgfjssb = value;
    }

    /**
     * Gets the value of the jsyjxgxz property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getJsyjxgxz() {
        return jsyjxgxz;
    }

    /**
     * Sets the value of the jsyjxgxz property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setJsyjxgxz(String value) {
        this.jsyjxgxz = value;
    }

    /**
     * Gets the value of the jsyjxgyy property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getJsyjxgyy() {
        return jsyjxgyy;
    }

    /**
     * Sets the value of the jsyjxgyy property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setJsyjxgyy(String value) {
        this.jsyjxgyy = value;
    }

}
