
package com.css.znsb.nssb.pojo.bo.hxzg.bssb00000;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 企业所得税查账征收年度申报弥补亏损台账数据信息
 * 
 * <p>Java class for SBQysdsczzsndsbmbkstzxxVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBQysdsczzsndsbmbkstzxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nd1" type="{http://www.chinatax.gov.cn/dataspec/}sbnd" minOccurs="0"/>
 *         &lt;element name="ylehkse1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hbflqyzrkmbkse1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="dnkmbdsde1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqsnd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqsannd1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqernd1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqyind1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqsnd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqsannd1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqernd1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqyind1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/> 
 *         &lt;element name="hj1" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="nd2" type="{http://www.chinatax.gov.cn/dataspec/}sbnd" minOccurs="0"/>
 *         &lt;element name="ylehkse2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hbflqyzrkmbkse2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="dnkmbdsde2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqsannd2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqernd2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqyind2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqsannd2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqernd2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqyind2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/> 
 *         &lt;element name="hj2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="kjzyhndmbdkse2" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="nd3" type="{http://www.chinatax.gov.cn/dataspec/}sbnd" minOccurs="0"/>
 *         &lt;element name="ylehkse3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hbflqyzrkmbkse3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="dnkmbdsde3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqernd3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqyind3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqernd3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqyind3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/> 
 *         &lt;element name="hj3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="kjzyhndmbdkse3" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="nd4" type="{http://www.chinatax.gov.cn/dataspec/}sbnd" minOccurs="0"/>
 *         &lt;element name="ylehkse4" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hbflqyzrkmbkse4" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="dnkmbdsde4" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yqndksmbeqyind4" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jwyqndksmbeqyind4" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/> 
 *         &lt;element name="hj4" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="kjzyhndmbdkse4" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="nd5" type="{http://www.chinatax.gov.cn/dataspec/}sbnd" minOccurs="0"/>
 *         &lt;element name="ylehkse5" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hbflqyzrkmbkse5" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="dnkmbdsde5" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="kjzyhndmbdkse5" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="nd6" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="xn_kjzyhndmbdkse_nd1" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="xn_kjzyhndmbdkse_nd2" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="xn_kjzyhndmbdkse_nd3" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="xn_kjzyhndmbdkse_nd4" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="xn_kjzyhndmbdkse_nd5" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="kjzyhndmbdksehj" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="kjzyhndmbdksehjSBDR_hxzg" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="initMbkstzLen" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="ndStr" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="bndsjmbdyqndkse1" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bndsjmbdyqndkse2" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bndsjmbdyqndkse3" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bndsjmbdyqndkse4" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bndsjmbdyqndkse5" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="ylehkse6" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="hbflqyzrkmbkse6" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="dnkmbdsde6" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bndsjmbdyqndkse6" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="kjzyhndmbdkse6" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBQysdsczzsndsbmbkstzxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "nd1",
    "ylehkse1",
    "hbflqyzrkmbkse1",
    "dnkmbdsde1",
    "yqndksmbeqsnd",
    "yqndksmbeqsannd1",
    "yqndksmbeqernd1",
    "yqndksmbeqyind1",
    "jwyqndksmbeqsnd",
    "jwyqndksmbeqsannd1",
    "jwyqndksmbeqernd1",
    "jwyqndksmbeqyind1",    
    "hj1",
    "nd2",
    "ylehkse2",
    "hbflqyzrkmbkse2",
    "dnkmbdsde2",
    "yqndksmbeqsannd2",
    "yqndksmbeqernd2",
    "yqndksmbeqyind2",
    "jwyqndksmbeqsannd2",
    "jwyqndksmbeqernd2",
    "jwyqndksmbeqyind2",    
    "hj2",
    "kjzyhndmbdkse2",
    "nd3",
    "ylehkse3",
    "hbflqyzrkmbkse3",
    "dnkmbdsde3",
    "yqndksmbeqernd3",
    "yqndksmbeqyind3",
    "jwyqndksmbeqernd3",
    "jwyqndksmbeqyind3",    
    "hj3",
    "kjzyhndmbdkse3",
    "nd4",
    "ylehkse4",
    "hbflqyzrkmbkse4",
    "dnkmbdsde4",
    "yqndksmbeqyind4",
    "jwyqndksmbeqyind4",    
    "hj4",
    "kjzyhndmbdkse4",
    "nd5",
    "ylehkse5",
    "hbflqyzrkmbkse5",
    "dnkmbdsde5",
    "kjzyhndmbdkse5",
    "nd6",
    "xnKjzyhndmbdkseNd1",
    "xnKjzyhndmbdkseNd2",
    "xnKjzyhndmbdkseNd3",
    "xnKjzyhndmbdkseNd4",
    "xnKjzyhndmbdkseNd5",
    "kjzyhndmbdksehj",
    "kjzyhndmbdksehjSBDRHxzg",
    "initMbkstzLen",
    "ndStr",
    "bndsjmbdyqndkse1",
    "bndsjmbdyqndkse2",
    "bndsjmbdyqndkse3",
    "bndsjmbdyqndkse4",
    "bndsjmbdyqndkse5",
    "ylehkse6",
    "hbflqyzrkmbkse6",
    "dnkmbdsde6",
    "bndsjmbdyqndkse6",
    "kjzyhndmbdkse6",
    "bnjwsdmbyqndkse6",
    "bnjwsdmbyqndkse5",
    "bnjwsdmbyqndkse4",
    "bnjwsdmbyqndkse3",
    "bnjwsdmbyqndkse2",
    "bnjwsdmbyqndkse1"
})
public class SBQysdsczzsndsbmbkstzxxVO
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nd1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ylehkse1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hbflqyzrkmbkse1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dnkmbdsde1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqsnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqsannd1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqernd1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqyind1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqsnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqsannd1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqernd1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqyind1;    
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hj1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nd2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ylehkse2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hbflqyzrkmbkse2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dnkmbdsde2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqsannd2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqernd2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqyind2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqsannd2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqernd2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Object jwyqndksmbeqyind2;    
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hj2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double kjzyhndmbdkse2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nd3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ylehkse3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hbflqyzrkmbkse3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dnkmbdsde3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqernd3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqyind3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqernd3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqyind3;    
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hj3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double kjzyhndmbdkse3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nd4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ylehkse4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hbflqyzrkmbkse4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dnkmbdsde4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yqndksmbeqyind4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jwyqndksmbeqyind4;    
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hj4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double kjzyhndmbdkse4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nd5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ylehkse5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hbflqyzrkmbkse5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dnkmbdsde5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double kjzyhndmbdkse5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nd6;
    @XmlElement(name = "xn_kjzyhndmbdkse_nd1", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xnKjzyhndmbdkseNd1;
    @XmlElement(name = "xn_kjzyhndmbdkse_nd2", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xnKjzyhndmbdkseNd2;
    @XmlElement(name = "xn_kjzyhndmbdkse_nd3", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xnKjzyhndmbdkseNd3;
    @XmlElement(name = "xn_kjzyhndmbdkse_nd4", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xnKjzyhndmbdkseNd4;
    @XmlElement(name = "xn_kjzyhndmbdkse_nd5", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xnKjzyhndmbdkseNd5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjzyhndmbdksehj;
    @XmlElement(name = "kjzyhndmbdksehjSBDR_hxzg", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjzyhndmbdksehjSBDRHxzg;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String initMbkstzLen;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ndStr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bndsjmbdyqndkse1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bndsjmbdyqndkse2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bndsjmbdyqndkse3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bndsjmbdyqndkse4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bndsjmbdyqndkse5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double ylehkse6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double hbflqyzrkmbkse6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double dnkmbdsde6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bndsjmbdyqndkse6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double kjzyhndmbdkse6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bnjwsdmbyqndkse6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bnjwsdmbyqndkse5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bnjwsdmbyqndkse4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bnjwsdmbyqndkse3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bnjwsdmbyqndkse2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double bnjwsdmbyqndkse1;

    /**
     * Gets the value of the nd1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNd1() {
        return nd1;
    }

    /**
     * Sets the value of the nd1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNd1(String value) {
        this.nd1 = value;
    }

    /**
     * Gets the value of the ylehkse1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYlehkse1() {
        return ylehkse1;
    }

    /**
     * Sets the value of the ylehkse1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYlehkse1(Double value) {
        this.ylehkse1 = value;
    }

    /**
     * Gets the value of the hbflqyzrkmbkse1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHbflqyzrkmbkse1() {
        return hbflqyzrkmbkse1;
    }

    /**
     * Sets the value of the hbflqyzrkmbkse1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHbflqyzrkmbkse1(Double value) {
        this.hbflqyzrkmbkse1 = value;
    }

    /**
     * Gets the value of the dnkmbdsde1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDnkmbdsde1() {
        return dnkmbdsde1;
    }

    /**
     * Sets the value of the dnkmbdsde1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDnkmbdsde1(Double value) {
        this.dnkmbdsde1 = value;
    }

    /**
     * Gets the value of the yqndksmbeqsnd property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqsnd() {
        return yqndksmbeqsnd;
    }

    /**
     * Sets the value of the yqndksmbeqsnd property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqsnd(Double value) {
        this.yqndksmbeqsnd = value;
    }

    /**
     * Gets the value of the yqndksmbeqsannd1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqsannd1() {
        return yqndksmbeqsannd1;
    }

    /**
     * Sets the value of the yqndksmbeqsannd1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqsannd1(Double value) {
        this.yqndksmbeqsannd1 = value;
    }

    /**
     * Gets the value of the yqndksmbeqernd1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqernd1() {
        return yqndksmbeqernd1;
    }

    /**
     * Sets the value of the yqndksmbeqernd1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqernd1(Double value) {
        this.yqndksmbeqernd1 = value;
    }

    /**
     * Gets the value of the yqndksmbeqyind1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqyind1() {
        return yqndksmbeqyind1;
    }

    /**
     * Sets the value of the yqndksmbeqyind1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqyind1(Double value) {
        this.yqndksmbeqyind1 = value;
    }
    
    /**
     * 获取jwyqndksmbeqsnd属性的值。
     * 
     */
    public double getJwyqndksmbeqsnd() {
        return jwyqndksmbeqsnd;
    }

    /**
     * 设置jwyqndksmbeqsnd属性的值。
     * 
     */
    public void setJwyqndksmbeqsnd(double value) {
        this.jwyqndksmbeqsnd = value;
    }

    /**
     * 获取jwyqndksmbeqsannd1属性的值。
     * 
     */
    public double getJwyqndksmbeqsannd1() {
        return jwyqndksmbeqsannd1;
    }

    /**
     * 设置jwyqndksmbeqsannd1属性的值。
     * 
     */
    public void setJwyqndksmbeqsannd1(double value) {
        this.jwyqndksmbeqsannd1 = value;
    }

    /**
     * 获取jwyqndksmbeqernd1属性的值。
     * 
     */
    public double getJwyqndksmbeqernd1() {
        return jwyqndksmbeqernd1;
    }

    /**
     * 设置jwyqndksmbeqernd1属性的值。
     * 
     */
    public void setJwyqndksmbeqernd1(double value) {
        this.jwyqndksmbeqernd1 = value;
    }

    /**
     * 获取jwyqndksmbeqyind1属性的值。
     * 
     */
    public double getJwyqndksmbeqyind1() {
        return jwyqndksmbeqyind1;
    }

    /**
     * 设置jwyqndksmbeqyind1属性的值。
     * 
     */
    public void setJwyqndksmbeqyind1(double value) {
        this.jwyqndksmbeqyind1 = value;
    }    

    /**
     * Gets the value of the hj1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHj1() {
        return hj1;
    }

    /**
     * Sets the value of the hj1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHj1(Double value) {
        this.hj1 = value;
    }

    /**
     * Gets the value of the nd2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNd2() {
        return nd2;
    }

    /**
     * Sets the value of the nd2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNd2(String value) {
        this.nd2 = value;
    }

    /**
     * Gets the value of the ylehkse2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYlehkse2() {
        return ylehkse2;
    }

    /**
     * Sets the value of the ylehkse2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYlehkse2(Double value) {
        this.ylehkse2 = value;
    }

    /**
     * Gets the value of the hbflqyzrkmbkse2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHbflqyzrkmbkse2() {
        return hbflqyzrkmbkse2;
    }

    /**
     * Sets the value of the hbflqyzrkmbkse2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHbflqyzrkmbkse2(Double value) {
        this.hbflqyzrkmbkse2 = value;
    }

    /**
     * Gets the value of the dnkmbdsde2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDnkmbdsde2() {
        return dnkmbdsde2;
    }

    /**
     * Sets the value of the dnkmbdsde2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDnkmbdsde2(Double value) {
        this.dnkmbdsde2 = value;
    }

    /**
     * Gets the value of the yqndksmbeqsannd2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqsannd2() {
        return yqndksmbeqsannd2;
    }

    /**
     * Sets the value of the yqndksmbeqsannd2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqsannd2(Double value) {
        this.yqndksmbeqsannd2 = value;
    }

    /**
     * Gets the value of the yqndksmbeqernd2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqernd2() {
        return yqndksmbeqernd2;
    }

    /**
     * Sets the value of the yqndksmbeqernd2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqernd2(Double value) {
        this.yqndksmbeqernd2 = value;
    }

    /**
     * Gets the value of the yqndksmbeqyind2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqyind2() {
        return yqndksmbeqyind2;
    }

    /**
     * Sets the value of the yqndksmbeqyind2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqyind2(Double value) {
        this.yqndksmbeqyind2 = value;
    }

    /**
     * 获取jwyqndksmbeqsannd2属性的值。
     * 
     */
    public double getJwyqndksmbeqsannd2() {
        return jwyqndksmbeqsannd2;
    }

    /**
     * 设置jwyqndksmbeqsannd2属性的值。
     * 
     */
    public void setJwyqndksmbeqsannd2(double value) {
        this.jwyqndksmbeqsannd2 = value;
    }

    /**
     * 获取jwyqndksmbeqernd2属性的值。
     * 
     */
    public double getJwyqndksmbeqernd2() {
        return jwyqndksmbeqernd2;
    }

    /**
     * 设置jwyqndksmbeqernd2属性的值。
     * 
     */
    public void setJwyqndksmbeqernd2(double value) {
        this.jwyqndksmbeqernd2 = value;
    }

    /**
     * 获取jwyqndksmbeqyind2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Object }
     *     
     */
    public Object getJwyqndksmbeqyind2() {
        return jwyqndksmbeqyind2;
    }

    /**
     * 设置jwyqndksmbeqyind2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Object }
     *     
     */
    public void setJwyqndksmbeqyind2(Object value) {
        this.jwyqndksmbeqyind2 = value;
    }    

    /**
     * Gets the value of the hj2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHj2() {
        return hj2;
    }

    /**
     * Sets the value of the hj2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHj2(Double value) {
        this.hj2 = value;
    }

    /**
     * Gets the value of the kjzyhndmbdkse2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKjzyhndmbdkse2() {
        return kjzyhndmbdkse2;
    }

    /**
     * Sets the value of the kjzyhndmbdkse2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKjzyhndmbdkse2(Double value) {
        this.kjzyhndmbdkse2 = value;
    }

    /**
     * Gets the value of the nd3 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNd3() {
        return nd3;
    }

    /**
     * Sets the value of the nd3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNd3(String value) {
        this.nd3 = value;
    }

    /**
     * Gets the value of the ylehkse3 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYlehkse3() {
        return ylehkse3;
    }

    /**
     * Sets the value of the ylehkse3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYlehkse3(Double value) {
        this.ylehkse3 = value;
    }

    /**
     * Gets the value of the hbflqyzrkmbkse3 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHbflqyzrkmbkse3() {
        return hbflqyzrkmbkse3;
    }

    /**
     * Sets the value of the hbflqyzrkmbkse3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHbflqyzrkmbkse3(Double value) {
        this.hbflqyzrkmbkse3 = value;
    }

    /**
     * Gets the value of the dnkmbdsde3 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDnkmbdsde3() {
        return dnkmbdsde3;
    }

    /**
     * Sets the value of the dnkmbdsde3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDnkmbdsde3(Double value) {
        this.dnkmbdsde3 = value;
    }

    /**
     * Gets the value of the yqndksmbeqernd3 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqernd3() {
        return yqndksmbeqernd3;
    }

    /**
     * Sets the value of the yqndksmbeqernd3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqernd3(Double value) {
        this.yqndksmbeqernd3 = value;
    }

    /**
     * Gets the value of the yqndksmbeqyind3 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqyind3() {
        return yqndksmbeqyind3;
    }

    /**
     * Sets the value of the yqndksmbeqyind3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqyind3(Double value) {
        this.yqndksmbeqyind3 = value;
    }

    /**
     * 获取jwyqndksmbeqernd3属性的值。
     * 
     */
    public double getJwyqndksmbeqernd3() {
        return jwyqndksmbeqernd3;
    }

    /**
     * 设置jwyqndksmbeqernd3属性的值。
     * 
     */
    public void setJwyqndksmbeqernd3(double value) {
        this.jwyqndksmbeqernd3 = value;
    }

    /**
     * 获取jwyqndksmbeqyind3属性的值。
     * 
     */
    public double getJwyqndksmbeqyind3() {
        return jwyqndksmbeqyind3;
    }

    /**
     * 设置jwyqndksmbeqyind3属性的值。
     * 
     */
    public void setJwyqndksmbeqyind3(double value) {
        this.jwyqndksmbeqyind3 = value;
    }
    
    /**
     * Gets the value of the hj3 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHj3() {
        return hj3;
    }

    /**
     * Sets the value of the hj3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHj3(Double value) {
        this.hj3 = value;
    }

    /**
     * Gets the value of the kjzyhndmbdkse3 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKjzyhndmbdkse3() {
        return kjzyhndmbdkse3;
    }

    /**
     * Sets the value of the kjzyhndmbdkse3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKjzyhndmbdkse3(Double value) {
        this.kjzyhndmbdkse3 = value;
    }

    /**
     * Gets the value of the nd4 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNd4() {
        return nd4;
    }

    /**
     * Sets the value of the nd4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNd4(String value) {
        this.nd4 = value;
    }

    /**
     * Gets the value of the ylehkse4 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYlehkse4() {
        return ylehkse4;
    }

    /**
     * Sets the value of the ylehkse4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYlehkse4(Double value) {
        this.ylehkse4 = value;
    }

    /**
     * Gets the value of the hbflqyzrkmbkse4 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHbflqyzrkmbkse4() {
        return hbflqyzrkmbkse4;
    }

    /**
     * Sets the value of the hbflqyzrkmbkse4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHbflqyzrkmbkse4(Double value) {
        this.hbflqyzrkmbkse4 = value;
    }

    /**
     * Gets the value of the dnkmbdsde4 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDnkmbdsde4() {
        return dnkmbdsde4;
    }

    /**
     * Sets the value of the dnkmbdsde4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDnkmbdsde4(Double value) {
        this.dnkmbdsde4 = value;
    }

    /**
     * Gets the value of the yqndksmbeqyind4 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYqndksmbeqyind4() {
        return yqndksmbeqyind4;
    }

    /**
     * Sets the value of the yqndksmbeqyind4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYqndksmbeqyind4(Double value) {
        this.yqndksmbeqyind4 = value;
    }

    /**
     * 获取jwyqndksmbeqyind4属性的值。
     * 
     */
    public double getJwyqndksmbeqyind4() {
        return jwyqndksmbeqyind4;
    }

    /**
     * 设置jwyqndksmbeqyind4属性的值。
     * 
     */
    public void setJwyqndksmbeqyind4(double value) {
        this.jwyqndksmbeqyind4 = value;
    }    

    /**
     * Gets the value of the hj4 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHj4() {
        return hj4;
    }

    /**
     * Sets the value of the hj4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHj4(Double value) {
        this.hj4 = value;
    }

    /**
     * Gets the value of the kjzyhndmbdkse4 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKjzyhndmbdkse4() {
        return kjzyhndmbdkse4;
    }

    /**
     * Sets the value of the kjzyhndmbdkse4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKjzyhndmbdkse4(Double value) {
        this.kjzyhndmbdkse4 = value;
    }

    /**
     * Gets the value of the nd5 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNd5() {
        return nd5;
    }

    /**
     * Sets the value of the nd5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNd5(String value) {
        this.nd5 = value;
    }

    /**
     * Gets the value of the ylehkse5 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYlehkse5() {
        return ylehkse5;
    }

    /**
     * Sets the value of the ylehkse5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYlehkse5(Double value) {
        this.ylehkse5 = value;
    }

    /**
     * Gets the value of the hbflqyzrkmbkse5 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHbflqyzrkmbkse5() {
        return hbflqyzrkmbkse5;
    }

    /**
     * Sets the value of the hbflqyzrkmbkse5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHbflqyzrkmbkse5(Double value) {
        this.hbflqyzrkmbkse5 = value;
    }

    /**
     * Gets the value of the dnkmbdsde5 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDnkmbdsde5() {
        return dnkmbdsde5;
    }

    /**
     * Sets the value of the dnkmbdsde5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDnkmbdsde5(Double value) {
        this.dnkmbdsde5 = value;
    }

    /**
     * Gets the value of the kjzyhndmbdkse5 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKjzyhndmbdkse5() {
        return kjzyhndmbdkse5;
    }

    /**
     * Sets the value of the kjzyhndmbdkse5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKjzyhndmbdkse5(Double value) {
        this.kjzyhndmbdkse5 = value;
    }

    /**
     * Gets the value of the nd6 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNd6() {
        return nd6;
    }

    /**
     * Sets the value of the nd6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNd6(String value) {
        this.nd6 = value;
    }

    /**
     * Gets the value of the xnKjzyhndmbdkseNd1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXnKjzyhndmbdkseNd1() {
        return xnKjzyhndmbdkseNd1;
    }

    /**
     * Sets the value of the xnKjzyhndmbdkseNd1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXnKjzyhndmbdkseNd1(String value) {
        this.xnKjzyhndmbdkseNd1 = value;
    }

    /**
     * Gets the value of the xnKjzyhndmbdkseNd2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXnKjzyhndmbdkseNd2() {
        return xnKjzyhndmbdkseNd2;
    }

    /**
     * Sets the value of the xnKjzyhndmbdkseNd2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXnKjzyhndmbdkseNd2(String value) {
        this.xnKjzyhndmbdkseNd2 = value;
    }

    /**
     * Gets the value of the xnKjzyhndmbdkseNd3 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXnKjzyhndmbdkseNd3() {
        return xnKjzyhndmbdkseNd3;
    }

    /**
     * Sets the value of the xnKjzyhndmbdkseNd3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXnKjzyhndmbdkseNd3(String value) {
        this.xnKjzyhndmbdkseNd3 = value;
    }

    /**
     * Gets the value of the xnKjzyhndmbdkseNd4 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXnKjzyhndmbdkseNd4() {
        return xnKjzyhndmbdkseNd4;
    }

    /**
     * Sets the value of the xnKjzyhndmbdkseNd4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXnKjzyhndmbdkseNd4(String value) {
        this.xnKjzyhndmbdkseNd4 = value;
    }

    /**
     * Gets the value of the xnKjzyhndmbdkseNd5 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXnKjzyhndmbdkseNd5() {
        return xnKjzyhndmbdkseNd5;
    }

    /**
     * Sets the value of the xnKjzyhndmbdkseNd5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXnKjzyhndmbdkseNd5(String value) {
        this.xnKjzyhndmbdkseNd5 = value;
    }

    /**
     * Gets the value of the kjzyhndmbdksehj property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKjzyhndmbdksehj() {
        return kjzyhndmbdksehj;
    }

    /**
     * Sets the value of the kjzyhndmbdksehj property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKjzyhndmbdksehj(String value) {
        this.kjzyhndmbdksehj = value;
    }

    /**
     * Gets the value of the kjzyhndmbdksehjSBDRHxzg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKjzyhndmbdksehjSBDRHxzg() {
        return kjzyhndmbdksehjSBDRHxzg;
    }

    /**
     * Sets the value of the kjzyhndmbdksehjSBDRHxzg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKjzyhndmbdksehjSBDRHxzg(String value) {
        this.kjzyhndmbdksehjSBDRHxzg = value;
    }

    /**
     * Gets the value of the initMbkstzLen property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInitMbkstzLen() {
        return initMbkstzLen;
    }

    /**
     * Sets the value of the initMbkstzLen property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInitMbkstzLen(String value) {
        this.initMbkstzLen = value;
    }

    /**
     * Gets the value of the ndStr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNdStr() {
        return ndStr;
    }

    /**
     * Sets the value of the ndStr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNdStr(String value) {
        this.ndStr = value;
    }

    /**
     * Gets the value of the bndsjmbdyqndkse1 property.
     * 
     */
    public double getBndsjmbdyqndkse1() {
        return bndsjmbdyqndkse1;
    }

    /**
     * Sets the value of the bndsjmbdyqndkse1 property.
     * 
     */
    public void setBndsjmbdyqndkse1(double value) {
        this.bndsjmbdyqndkse1 = value;
    }

    /**
     * Gets the value of the bndsjmbdyqndkse2 property.
     * 
     */
    public double getBndsjmbdyqndkse2() {
        return bndsjmbdyqndkse2;
    }

    /**
     * Sets the value of the bndsjmbdyqndkse2 property.
     * 
     */
    public void setBndsjmbdyqndkse2(double value) {
        this.bndsjmbdyqndkse2 = value;
    }

    /**
     * Gets the value of the bndsjmbdyqndkse3 property.
     * 
     */
    public double getBndsjmbdyqndkse3() {
        return bndsjmbdyqndkse3;
    }

    /**
     * Sets the value of the bndsjmbdyqndkse3 property.
     * 
     */
    public void setBndsjmbdyqndkse3(double value) {
        this.bndsjmbdyqndkse3 = value;
    }

    /**
     * Gets the value of the bndsjmbdyqndkse4 property.
     * 
     */
    public double getBndsjmbdyqndkse4() {
        return bndsjmbdyqndkse4;
    }

    /**
     * Sets the value of the bndsjmbdyqndkse4 property.
     * 
     */
    public void setBndsjmbdyqndkse4(double value) {
        this.bndsjmbdyqndkse4 = value;
    }

    /**
     * Gets the value of the bndsjmbdyqndkse5 property.
     * 
     */
    public double getBndsjmbdyqndkse5() {
        return bndsjmbdyqndkse5;
    }

    /**
     * Sets the value of the bndsjmbdyqndkse5 property.
     * 
     */
    public void setBndsjmbdyqndkse5(double value) {
        this.bndsjmbdyqndkse5 = value;
    }

    /**
     * Gets the value of the ylehkse6 property.
     * 
     */
    public double getYlehkse6() {
        return ylehkse6;
    }

    /**
     * Sets the value of the ylehkse6 property.
     * 
     */
    public void setYlehkse6(double value) {
        this.ylehkse6 = value;
    }

    /**
     * Gets the value of the hbflqyzrkmbkse6 property.
     * 
     */
    public double getHbflqyzrkmbkse6() {
        return hbflqyzrkmbkse6;
    }

    /**
     * Sets the value of the hbflqyzrkmbkse6 property.
     * 
     */
    public void setHbflqyzrkmbkse6(double value) {
        this.hbflqyzrkmbkse6 = value;
    }

    /**
     * Gets the value of the dnkmbdsde6 property.
     * 
     */
    public double getDnkmbdsde6() {
        return dnkmbdsde6;
    }

    /**
     * Sets the value of the dnkmbdsde6 property.
     * 
     */
    public void setDnkmbdsde6(double value) {
        this.dnkmbdsde6 = value;
    }

    /**
     * Gets the value of the bndsjmbdyqndkse6 property.
     * 
     */
    public double getBndsjmbdyqndkse6() {
        return bndsjmbdyqndkse6;
    }

    /**
     * Sets the value of the bndsjmbdyqndkse6 property.
     * 
     */
    public void setBndsjmbdyqndkse6(double value) {
        this.bndsjmbdyqndkse6 = value;
    }

    /**
     * Gets the value of the kjzyhndmbdkse6 property.
     * 
     */
    public double getKjzyhndmbdkse6() {
        return kjzyhndmbdkse6;
    }

    /**
     * Sets the value of the kjzyhndmbdkse6 property.
     * 
     */
    public void setKjzyhndmbdkse6(double value) {
        this.kjzyhndmbdkse6 = value;
    }
    /**
     * Gets the value of the bnjwsdmbyqndkse6 property.
     * 
     */
    public double getBnjwsdmbyqndkse6() {
        return bnjwsdmbyqndkse6;
    }
    
    /**
     * Sets the value of the bnjwsdmbyqndkse6 property.
     * 
     */
    public void setBnjwsdmbyqndkse6(double value) {
        this.bnjwsdmbyqndkse6 = value;
    }
    /**
     * Gets the value of the bnjwsdmbyqndkse5 property.
     * 
     */
    public double getBnjwsdmbyqndkse5() {
        return bnjwsdmbyqndkse5;
    }
    
    /**
     * Sets the value of the bnjwsdmbyqndkse5 property.
     * 
     */
    public void setBnjwsdmbyqndkse5(double value) {
        this.bnjwsdmbyqndkse5 = value;
    }
    /**
     * Gets the value of the bnjwsdmbyqndkse4 property.
     * 
     */
    public double getBnjwsdmbyqndkse4() {
        return bnjwsdmbyqndkse4;
    }
    
    /**
     * Sets the value of the bnjwsdmbyqndkse4 property.
     * 
     */
    public void setBnjwsdmbyqndkse4(double value) {
        this.bnjwsdmbyqndkse4 = value;
    }
    /**
     * Gets the value of the bnjwsdmbyqndkse3 property.
     * 
     */
    public double getBnjwsdmbyqndkse3() {
        return bnjwsdmbyqndkse3;
    }
    
    /**
     * Sets the value of the bnjwsdmbyqndkse3 property.
     * 
     */
    public void setBnjwsdmbyqndkse3(double value) {
        this.bnjwsdmbyqndkse3 = value;
    }
    /**
     * Gets the value of the bnjwsdmbyqndkse2 property.
     * 
     */
    public double getBnjwsdmbyqndkse2() {
        return bnjwsdmbyqndkse2;
    }
    
    /**
     * Sets the value of the bnjwsdmbyqndkse3 property.
     * 
     */
    public void setBnjwsdmbyqndkse2(double value) {
        this.bnjwsdmbyqndkse2 = value;
    }
    /**
     * Gets the value of the bnjwsdmbyqndkse1 property.
     * 
     */
    public double getBnjwsdmbyqndkse1() {
        return bnjwsdmbyqndkse1;
    }
    
    /**
     * Sets the value of the bnjwsdmbyqndkse1 property.
     * 
     */
    public void setBnjwsdmbyqndkse1(double value) {
        this.bnjwsdmbyqndkse1 = value;
    }

}
