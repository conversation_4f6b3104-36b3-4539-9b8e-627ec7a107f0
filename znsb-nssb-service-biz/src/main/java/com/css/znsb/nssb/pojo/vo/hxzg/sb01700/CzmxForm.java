
package com.css.znsb.nssb.pojo.vo.hxzg.sb01700;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 房屋应税信息（从租）
 * 
 * <p>czmxForm complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="czmxForm">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="czsyuuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid"/>
 *         &lt;element name="fyxxuuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid"/>
 *         &lt;element name="czfnsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="czfdjxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh" minOccurs="0"/>
 *         &lt;element name="czfnsrmc" type="{http://www.chinatax.gov.cn/dataspec/}nsrmc"/>
 *         &lt;element name="czmj" type="{http://www.chinatax.gov.cn/dataspec/}czmj"/>
 *         &lt;element name="htzjzsr" type="{http://www.chinatax.gov.cn/dataspec/}htzjzsr"/>
 *         &lt;element name="htydzlqq" type="{http://www.chinatax.gov.cn/dataspec/}htydzlqq"/>
 *         &lt;element name="htydzlqz" type="{http://www.chinatax.gov.cn/dataspec/}htydzlqz"/>
 *         &lt;element name="sbzjsr" type="{http://www.chinatax.gov.cn/dataspec/}sbzjsr"/>
 *         &lt;element name="sbzjsszlqq" type="{http://www.chinatax.gov.cn/dataspec/}sbzjsszlqq"/>
 *         &lt;element name="sbzjsszlqz" type="{http://www.chinatax.gov.cn/dataspec/}sbzjsszlqz"/>
 *         &lt;element name="sl1" type="{http://www.chinatax.gov.cn/dataspec/}sl1"/>
 *         &lt;element name="yxqq" type="{http://www.chinatax.gov.cn/dataspec/}yxqq"/>
 *         &lt;element name="yxqz" type="{http://www.chinatax.gov.cn/dataspec/}yxqz"/>
 *         &lt;element name="yczsyuuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz"/>
 *         &lt;element name="bgbm" type="{http://www.chinatax.gov.cn/dataspec/}bz1"/>
 *         &lt;element name="pkbz" type="{http://www.chinatax.gov.cn/dataspec/}pkbz"/>
 *         &lt;element name="jmxzdmxx" type="{http://www.chinatax.gov.cn/dataspec/}jmxzdmxx"/>
 *         &lt;element name="jmshj" type="{http://www.chinatax.gov.cn/dataspec/}jmshj"/>
 *         &lt;element name="bgyxqq" type="{http://www.chinatax.gov.cn/dataspec/}yxqq"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "czmxForm", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "czsyuuid",
    "fyxxuuid",
    "czfnsrsbh",
    "czfdjxh",
    "czfnsrmc",
    "czmj",
    "htzjzsr",
    "htydzlqq",
    "htydzlqz",
    "sbzjsr",
    "sbzjsszlqq",
    "sbzjsszlqz",
    "sl1",
    "yxqq",
    "yxqz",
    "yczsyuuid",
    "zspmDm",
    "yxbz",
    "bgbm",
    "pkbz",
    "jmxzdmxx",
    "jmshj",
    "bgyxqq"
})
public class CzmxForm
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String czsyuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String fyxxuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String czfnsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String czfdjxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String czfnsrmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double czmj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double htzjzsr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String htydzlqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String htydzlqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double sbzjsr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String sbzjsszlqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String sbzjsszlqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double sl1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String yxqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String yxqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String yczsyuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String yxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String bgbm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String pkbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String jmxzdmxx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double jmshj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String bgyxqq;

    /**
     * 获取czsyuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzsyuuid() {
        return czsyuuid;
    }

    /**
     * 设置czsyuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzsyuuid(String value) {
        this.czsyuuid = value;
    }

    /**
     * 获取fyxxuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFyxxuuid() {
        return fyxxuuid;
    }

    /**
     * 设置fyxxuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFyxxuuid(String value) {
        this.fyxxuuid = value;
    }

    /**
     * 获取czfnsrsbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzfnsrsbh() {
        return czfnsrsbh;
    }

    /**
     * 设置czfnsrsbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzfnsrsbh(String value) {
        this.czfnsrsbh = value;
    }

    /**
     * 获取czfdjxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzfdjxh() {
        return czfdjxh;
    }

    /**
     * 设置czfdjxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzfdjxh(String value) {
        this.czfdjxh = value;
    }

    /**
     * 获取czfnsrmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzfnsrmc() {
        return czfnsrmc;
    }

    /**
     * 设置czfnsrmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzfnsrmc(String value) {
        this.czfnsrmc = value;
    }

    /**
     * 获取czmj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getCzmj() {
        return czmj;
    }

    /**
     * 设置czmj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setCzmj(Double value) {
        this.czmj = value;
    }

    /**
     * 获取htzjzsr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHtzjzsr() {
        return htzjzsr;
    }

    /**
     * 设置htzjzsr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHtzjzsr(Double value) {
        this.htzjzsr = value;
    }

    /**
     * 获取htydzlqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHtydzlqq() {
        return htydzlqq;
    }

    /**
     * 设置htydzlqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHtydzlqq(String value) {
        this.htydzlqq = value;
    }

    /**
     * 获取htydzlqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHtydzlqz() {
        return htydzlqz;
    }

    /**
     * 设置htydzlqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHtydzlqz(String value) {
        this.htydzlqz = value;
    }

    /**
     * 获取sbzjsr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSbzjsr() {
        return sbzjsr;
    }

    /**
     * 设置sbzjsr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSbzjsr(Double value) {
        this.sbzjsr = value;
    }

    /**
     * 获取sbzjsszlqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbzjsszlqq() {
        return sbzjsszlqq;
    }

    /**
     * 设置sbzjsszlqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbzjsszlqq(String value) {
        this.sbzjsszlqq = value;
    }

    /**
     * 获取sbzjsszlqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbzjsszlqz() {
        return sbzjsszlqz;
    }

    /**
     * 设置sbzjsszlqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbzjsszlqz(String value) {
        this.sbzjsszlqz = value;
    }

    /**
     * 获取sl1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSl1() {
        return sl1;
    }

    /**
     * 设置sl1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSl1(Double value) {
        this.sl1 = value;
    }

    /**
     * 获取yxqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqq() {
        return yxqq;
    }

    /**
     * 设置yxqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqq(String value) {
        this.yxqq = value;
    }

    /**
     * 获取yxqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqz() {
        return yxqz;
    }

    /**
     * 设置yxqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqz(String value) {
        this.yxqz = value;
    }

    /**
     * 获取yczsyuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYczsyuuid() {
        return yczsyuuid;
    }

    /**
     * 设置yczsyuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYczsyuuid(String value) {
        this.yczsyuuid = value;
    }

    /**
     * 获取zspmDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * 设置zspmDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * 获取yxbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxbz() {
        return yxbz;
    }

    /**
     * 设置yxbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxbz(String value) {
        this.yxbz = value;
    }

    /**
     * 获取bgbm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBgbm() {
        return bgbm;
    }

    /**
     * 设置bgbm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBgbm(String value) {
        this.bgbm = value;
    }

    /**
     * 获取pkbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPkbz() {
        return pkbz;
    }

    /**
     * 设置pkbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPkbz(String value) {
        this.pkbz = value;
    }

    /**
     * 获取jmxzdmxx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmxzdmxx() {
        return jmxzdmxx;
    }

    /**
     * 设置jmxzdmxx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmxzdmxx(String value) {
        this.jmxzdmxx = value;
    }

    /**
     * 获取jmshj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJmshj() {
        return jmshj;
    }

    /**
     * 设置jmshj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJmshj(Double value) {
        this.jmshj = value;
    }

    /**
     * 获取bgyxqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBgyxqq() {
        return bgyxqq;
    }

    /**
     * 设置bgyxqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBgyxqq(String value) {
        this.bgyxqq = value;
    }

}
