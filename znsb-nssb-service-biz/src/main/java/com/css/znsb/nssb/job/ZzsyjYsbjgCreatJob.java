package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.zzsyjsb.plsb.ZzsyjYsbsjcjService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 增值税预缴预申报结果创建定时任务
 */
@Slf4j
@Component
public class ZzsyjYsbjgCreatJob {

    @Resource
    private ZzsyjYsbsjcjService ysbsjcjService;

    @XxlJob("zzsyjYsbjgCreatJob")
    public void execute() {
        log.info("开始执行增值税预缴申报预申报结果创建定时任务");
        ysbsjcjService.handlerYsbsj();
        log.info("增值税预缴申报预申报结果创建定时任务执行完成");
    }
}
