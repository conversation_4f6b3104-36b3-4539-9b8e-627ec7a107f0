
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 总机构
 * 
 * <p>Java class for zjgForm complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="zjgForm">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="khyh" type="{http://www.chinatax.gov.cn/dataspec/}khyh"/>
 *         &lt;element name="yhzh" type="{http://www.chinatax.gov.cn/dataspec/}yhzh"/>
 *         &lt;element name="bsrymc" type="{http://www.chinatax.gov.cn/dataspec/}mc"/>
 *         &lt;element name="dhhm" type="{http://www.chinatax.gov.cn/dataspec/}dhhm"/>
 *         &lt;element name="zfjgxsehj" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="zjgxssr" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="zjgfpbl" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="zjgfpse" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bsdw" type="{http://www.chinatax.gov.cn/dataspec/}mc"/>
 *         &lt;element name="zgswjgshyj" type="{http://www.chinatax.gov.cn/dataspec/}mc"/>
 *         &lt;element name="slry" type="{http://www.chinatax.gov.cn/dataspec/}mc"/>
 *         &lt;element name="zgswjgmc" type="{http://www.chinatax.gov.cn/dataspec/}mc"/>
 *         &lt;element name="slrq" type="{http://www.chinatax.gov.cn/dataspec/}slrq"/>
 *         &lt;element name="zjgnsrmc" type="{http://www.chinatax.gov.cn/dataspec/}nsrmc"/>
 *         &lt;element name="fddbrxm" type="{http://www.chinatax.gov.cn/dataspec/}fddbr"/>
 *         &lt;element name="yydz" type="{http://www.chinatax.gov.cn/dataspec/}yydz"/>
 *         &lt;element name="lxdh" type="{http://www.chinatax.gov.cn/dataspec/}lxdh"/>
 *         &lt;element name="zjgnsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="ybtse" type="{http://www.chinatax.gov.cn/dataspec/}ybtse"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zjgForm", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "khyh",
    "yhzh",
    "bsrymc",
    "dhhm",
    "zfjgxsehj",
    "zjgxssr",
    "zjgfpbl",
    "zjgfpse",
    "bsdw",
    "zgswjgshyj",
    "slry",
    "zgswjgmc",
    "slrq",
    "zjgnsrmc",
    "fddbrxm",
    "yydz",
    "lxdh",
    "zjgnsrsbh",
    "ybtse"
})
public class ZjgForm
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String khyh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yhzh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String bsrymc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String dhhm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double zfjgxsehj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double zjgxssr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double zjgfpbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double zjgfpse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String bsdw;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zgswjgshyj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slry;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zgswjgmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zjgnsrmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fddbrxm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yydz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lxdh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zjgnsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double ybtse;

    /**
     * Gets the value of the khyh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKhyh() {
        return khyh;
    }

    /**
     * Sets the value of the khyh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKhyh(String value) {
        this.khyh = value;
    }

    /**
     * Gets the value of the yhzh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYhzh() {
        return yhzh;
    }

    /**
     * Sets the value of the yhzh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYhzh(String value) {
        this.yhzh = value;
    }

    /**
     * Gets the value of the bsrymc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBsrymc() {
        return bsrymc;
    }

    /**
     * Sets the value of the bsrymc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBsrymc(String value) {
        this.bsrymc = value;
    }

    /**
     * Gets the value of the dhhm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDhhm() {
        return dhhm;
    }

    /**
     * Sets the value of the dhhm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDhhm(String value) {
        this.dhhm = value;
    }

    /**
     * Gets the value of the zfjgxsehj property.
     * 
     */
    public double getZfjgxsehj() {
        return zfjgxsehj;
    }

    /**
     * Sets the value of the zfjgxsehj property.
     * 
     */
    public void setZfjgxsehj(double value) {
        this.zfjgxsehj = value;
    }

    /**
     * Gets the value of the zjgxssr property.
     * 
     */
    public double getZjgxssr() {
        return zjgxssr;
    }

    /**
     * Sets the value of the zjgxssr property.
     * 
     */
    public void setZjgxssr(double value) {
        this.zjgxssr = value;
    }

    /**
     * Gets the value of the zjgfpbl property.
     * 
     */
    public double getZjgfpbl() {
        return zjgfpbl;
    }

    /**
     * Sets the value of the zjgfpbl property.
     * 
     */
    public void setZjgfpbl(double value) {
        this.zjgfpbl = value;
    }

    /**
     * Gets the value of the zjgfpse property.
     * 
     */
    public double getZjgfpse() {
        return zjgfpse;
    }

    /**
     * Sets the value of the zjgfpse property.
     * 
     */
    public void setZjgfpse(double value) {
        this.zjgfpse = value;
    }

    /**
     * Gets the value of the bsdw property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBsdw() {
        return bsdw;
    }

    /**
     * Sets the value of the bsdw property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBsdw(String value) {
        this.bsdw = value;
    }

    /**
     * Gets the value of the zgswjgshyj property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswjgshyj() {
        return zgswjgshyj;
    }

    /**
     * Sets the value of the zgswjgshyj property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswjgshyj(String value) {
        this.zgswjgshyj = value;
    }

    /**
     * Gets the value of the slry property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlry() {
        return slry;
    }

    /**
     * Sets the value of the slry property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlry(String value) {
        this.slry = value;
    }

    /**
     * Gets the value of the zgswjgmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswjgmc() {
        return zgswjgmc;
    }

    /**
     * Sets the value of the zgswjgmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswjgmc(String value) {
        this.zgswjgmc = value;
    }

    /**
     * Gets the value of the slrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlrq() {
        return slrq;
    }

    /**
     * Sets the value of the slrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlrq(String value) {
        this.slrq = value;
    }

    /**
     * Gets the value of the zjgnsrmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZjgnsrmc() {
        return zjgnsrmc;
    }

    /**
     * Sets the value of the zjgnsrmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZjgnsrmc(String value) {
        this.zjgnsrmc = value;
    }

    /**
     * Gets the value of the fddbrxm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFddbrxm() {
        return fddbrxm;
    }

    /**
     * Sets the value of the fddbrxm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFddbrxm(String value) {
        this.fddbrxm = value;
    }

    /**
     * Gets the value of the yydz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYydz() {
        return yydz;
    }

    /**
     * Sets the value of the yydz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYydz(String value) {
        this.yydz = value;
    }

    /**
     * Gets the value of the lxdh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLxdh() {
        return lxdh;
    }

    /**
     * Sets the value of the lxdh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLxdh(String value) {
        this.lxdh = value;
    }

    /**
     * Gets the value of the zjgnsrsbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZjgnsrsbh() {
        return zjgnsrsbh;
    }

    /**
     * Sets the value of the zjgnsrsbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZjgnsrsbh(String value) {
        this.zjgnsrsbh = value;
    }

    /**
     * Gets the value of the ybtse property.
     * 
     */
    public double getYbtse() {
        return ybtse;
    }

    /**
     * Sets the value of the ybtse property.
     * 
     */
    public void setYbtse(double value) {
        this.ybtse = value;
    }

}
