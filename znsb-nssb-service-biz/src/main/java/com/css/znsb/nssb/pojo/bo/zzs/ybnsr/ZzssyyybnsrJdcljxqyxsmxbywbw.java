package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车辆经销企业销售明细表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdcljxqyxsmxbywbw", propOrder = { "zzssyyybnsrJdcljxqyxsmxb" })
@Getter
@Setter
public class ZzssyyybnsrJdcljxqyxsmxbywbw extends TaxDoc {
    /**
     * 《机动车辆经销企业销售明细表》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_jdcljxqyxsmxb", required = true)
    @JSONField(name = "zzssyyybnsr_jdcljxqyxsmxb")
    protected ZzssyyybnsrJdcljxqyxsmxb zzssyyybnsrJdcljxqyxsmxb;
}