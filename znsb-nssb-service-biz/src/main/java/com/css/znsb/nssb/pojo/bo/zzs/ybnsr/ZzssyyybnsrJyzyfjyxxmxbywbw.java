package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《加油站月份加油信息明细表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jyzyfjyxxmxbywbw", propOrder = { "zzssyyybnsrJyzyfjyxxmxb" })
@Getter
@Setter
public class ZzssyyybnsrJyzyfjyxxmxbywbw extends TaxDoc {
    /**
     * 加油站月份加油信息明细
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_jyzyfjyxxmxb", required = true)
    @JSONField(name = "zzssyyybnsr_jyzyfjyxxmxb")
    protected ZzssyyybnsrJyzyfjyxxmxb zzssyyybnsrJyzyfjyxxmxb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrJyzyfjyxxmxb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrJyzyfjyxxmxb {}
}