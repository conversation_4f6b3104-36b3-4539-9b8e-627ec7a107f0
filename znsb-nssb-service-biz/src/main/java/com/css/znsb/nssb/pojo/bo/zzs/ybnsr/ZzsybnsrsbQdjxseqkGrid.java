package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《取得进项税额情况》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_qdjxseqkGrid")
@XmlSeeAlso({ ZzsybnsrsbYzqyfzjgzzshznsxxcdd.ZzsybnsrsbQdjxseqkGrid.class })
@Getter
@Setter
public class ZzsybnsrsbQdjxseqkGrid {}