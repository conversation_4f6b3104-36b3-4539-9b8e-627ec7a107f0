package com.css.znsb.nssb.pojo.vo.cchxwssb.cchxws.sbzx;

import com.css.znsb.nssb.pojo.dto.cchxwssb.sbzx.base.PwxscjbDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HbsSyxxDrVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 税源编号
     */
    //@ApiModelProperty("税源编号")
    @JsonProperty(value="hgbhssybh")
    private String hgbhssybh;

    /**
     * 排污许可证编号
     */
    //@ApiModelProperty("排污许可证编号")
    @JsonProperty(value="pwxkzbh")
    private String pwxkzbh;

    /**
     * 排放口编号
     */
    //@ApiModelProperty("排放口编号")
    @JsonProperty(value="pfkbh")
    private String pfkbh;

    /**
     * 排放口名称
     */
    //@ApiModelProperty("排放口名称")
    @JsonProperty(value="pfkmc")
    private String pfkmc;

    /**
     * 街道乡镇代码
     */
    //@ApiModelProperty("街道乡镇代码")
    @JsonProperty(value="jdxzDm")
    private String jdxzDm;

    /**
     * 经度
     */
    //@ApiModelProperty("经度")
    @JsonProperty(value="jd2")
    private String jd2;

    /**
     * 经度-度
     */
    //@ApiModelProperty("经度-度")
    @JsonProperty(value="jd2d")
    private Double jd2d;

    /**
     * 经度-分
     */
    //@ApiModelProperty("经度-分")
    @JsonProperty(value="jd2f")
    private Double jd2f;

    /**
     * 经度-秒
     */
    //@ApiModelProperty("经度-秒")
    @JsonProperty(value="jd2m")
    private Double jd2m;

    /**
     * 纬度
     */
    //@ApiModelProperty("纬度")
    @JsonProperty(value="wd")
    private String wd;

    /**
     * 纬度-度
     */
    //@ApiModelProperty("纬度-度")
    @JsonProperty(value="wdd")
    private Double wdd;

    /**
     * 纬度-分
     */
    //@ApiModelProperty("纬度-分")
    @JsonProperty(value="wdf")
    private Double wdf;

    /**
     * 纬度-秒
     */
    //@ApiModelProperty("纬度-秒")
    @JsonProperty(value="wdm")
    private Double wdm;

    /**
     * 税源有效期起
     */
    //@ApiModelProperty("税源有效期起")
    @JsonProperty(value="syyxqq")
    private String syyxqq;

    /**
     * 税源有效期止
     */
    //@ApiModelProperty("税源有效期止")
    @JsonProperty(value="syyxqz")
    private String syyxqz;

    /**
     * 污染物类别代码
     */
    //@ApiModelProperty("污染物类别代码")
    @JsonProperty(value="zywrwlbDm")
    private String zywrwlbDm;

    /**
     * 水污染物种类代码
     */
    //@ApiModelProperty("水污染物种类代码")
    @JsonProperty(value="fzspmDm")
    private String fzspmDm;

    /**
     * 征收品目
     */
    //@ApiModelProperty("征收品目")
    @JsonProperty(value="zspmDm")
    private String zspmDm;

    /**
     * 征收子目代码
     */
    //@ApiModelProperty("征收子目代码")
    @JsonProperty(value="zszmDm")
    private String zszmDm;

    /**
     * 污染物排放量计算方法
     */
    //@ApiModelProperty("污染物排放量计算方法")
    @JsonProperty(value="wrwpfljsffDm")
    private String wrwpfljsffDm;


    /**
     * 税源uuid
     */
    //@ApiModelProperty("税源uuid")
    @JsonProperty(value="syuuid")
    private String syuuid;

    /**
     * 大气污染物排放口类别代码
     */
    //@ApiModelProperty("大气污染物排放口类别代码")
    @JsonProperty(value="dqwrwpfklbDm")
    private String dqwrwpfklbDm;

    /**
     * 标准浓度值
     */
    //@ApiModelProperty("标准浓度值")
    @JsonProperty(value="bzndz")
    private Double bzndz;

    /**
     * 执行标准
     */
    //@ApiModelProperty("执行标准")
    @JsonProperty(value="zxbz1")
    private String zxbz1;

    /**
     * 标准值——夜间(22时-次日6时)
     */
    //@ApiModelProperty("标准值——夜间(22时-次日6时)")
    @JsonProperty(value="bzzyj")
    private Double bzzyj;

    /**
     * 是否昼夜生产
     */
    //@ApiModelProperty("是否昼夜生产")
    @JsonProperty(value="sfzycsbz")
    private String sfzycsbz;

    /**
     * 标准值——昼间(6时-22时)
     */
    //@ApiModelProperty("标准值——昼间(6时-22时)")
    @JsonProperty(value="bzzzj")
    private Double bzzzj;


    /**
     * 综合利用、处置、贮存容量能力（吨）
     */
    //@ApiModelProperty("综合利用、处置、贮存容量能力（吨）")
    @JsonProperty(value="zhlyhczhccrlnl")
    private Double zhlyhczhccrlnl;

    /**
     * 场所（设施）名称
     */
    //@ApiModelProperty("场所（设施）名称")
    @JsonProperty(value="cshssmc")
    private String cshssmc;

    /**
     * 综合利用产物
     */
    //@ApiModelProperty("综合利用产物")
    @JsonProperty(value="zhlycw")
    private String zhlycw;

    /**
     * 综合利用（处置）方式
     */
    //@ApiModelProperty("综合利用（处置）方式")
    @JsonProperty(value="zhlyzyfssDm")
    private String zhlyzyfssDm;

    /**
     * 处置情况
     */
    //@ApiModelProperty("处置情况")
    @JsonProperty(value="czqk")
    private String czqk;

    /**
     * 产污系数
     */
    //@ApiModelProperty("产污系数")
    @JsonProperty(value="cwxs")
    private Double cwxs;


    /**
     * 排污系数
     */
    //@ApiModelProperty("排污系数")
    @JsonProperty(value="pwxs")
    private Double pwxs;

    /**
     * 计税基数单位名称
     */
    //@ApiModelProperty("计税基数单位名称")
    @JsonProperty(value="jsjsdw")
    private String jsjsdw;

    /**
     * 污染物单位代码
     */
    //@ApiModelProperty("污染物单位代码")
    @JsonProperty(value="wrwdwDm")
    private String wrwdwDm;

    /**
     *  税源信息有效性
     */
    //@ApiModelProperty("税源信息有效性")
    @JsonProperty(value="syxxyxx")
    private String syxxyxx;

    /**
     *  排污系数附表集合
     */
    //@ApiModelProperty("排污系数附表集合")
    @JsonProperty(value="pwxscjbDTOList")
    private List<PwxscjbDTO> pwxscjbDTOList;
}
