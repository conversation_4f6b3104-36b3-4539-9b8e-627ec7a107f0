package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>taxList complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="taxList">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nsrsbh" type="{http://www.w3.org/2001/XMLSchema}anyType"/>
 *         &lt;element name="nsrmc" type="{http://www.chinatax.gov.cn/dataspec/}taxDoc"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "taxList", propOrder = { "nsrsbh", "nsrmc" })
public class TaxList {
    @XmlElement(nillable = true, required = true)
    protected Object nsrsbh;

    @XmlElement(nillable = true, required = true)
    protected TaxDoc nsrmc;

    /**
     * 获取nsrsbh属性的值。
     */
    public Object getNsrsbh() {
        return nsrsbh;
    }

    /**
     * 设置nsrsbh属性的值。
     */
    public void setNsrsbh(Object value) {
        this.nsrsbh = value;
    }

    /**
     * 获取nsrmc属性的值。
     */
    public TaxDoc getNsrmc() {
        return nsrmc;
    }

    /**
     * 设置nsrmc属性的值。
     */
    public void setNsrmc(TaxDoc value) {
        this.nsrmc = value;
    }
}