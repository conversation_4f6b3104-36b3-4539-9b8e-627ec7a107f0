package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 已缴纳增值税情况Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "yjnzzsqkGridlbVO", propOrder = { "zsxm", "xse", "yzl", "yjse1", "bjse", "bz" })
@Getter
@Setter
public class YjnzzsqkGridlbVO {
    /**
     * 征税项目
     */
    protected String zsxm;

    /**
     * 销售额
     */
    protected BigDecimal xse;

    /**
     * 预征率
     */
    protected BigDecimal yzl;

    /**
     * 预缴税额
     */
    protected BigDecimal yjse1;

    /**
     * 补缴税额
     */
    protected BigDecimal bjse;

    /**
     * 备注
     */
    protected String bz;
}