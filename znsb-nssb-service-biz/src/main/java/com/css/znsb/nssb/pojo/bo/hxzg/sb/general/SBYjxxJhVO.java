package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.sbzs.sb.general
 * @file SBYjxxJhVO.java 创建时间:2014-7-22上午12:02:51
 * @title 申报纳税人预缴数据信息对象
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 核心征管-申报
 * <AUTHOR> 刘巍
 * @reviewer 审核人 邓文辉
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBYjxxJhVO", propOrder = {
    "yjskuuid",
    "zsxmDm",
    "zspmDm",
    "djxh",
    "skssqq",
    "skssqz",
    "yjze",
    "yjye1",
    "sksxDm",
    "zgswskfjDm",
    "sjgsdq",
    "hyDm"
})
public class SBYjxxJhVO implements Serializable{

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -8535451108637622570L;
    @XmlElement(required = true, nillable = true)
    protected String yjskuuid;
    @XmlElement(required = true, nillable = true)
    protected String zsxmDm;
    @XmlElement(required = true, nillable = true)
    protected String zspmDm;
    @XmlElement(required = true, nillable = true)
    protected String djxh;
    @XmlElement(required = true, nillable = true)
    protected String skssqq;
    @XmlElement(required = true, nillable = true)
    protected String skssqz;
    @XmlElement(required = true, type = Double.class, nillable = true)
    protected Double yjze;
    @XmlElement(required = true, type = Double.class, nillable = true)
    protected Double yjye1;
    @XmlElement(required = true, nillable = true)
    protected String sksxDm;
    @XmlElement(required = true, nillable = true)
    protected String zgswskfjDm;
    @XmlElement(required = true, nillable = true)
    protected String sjgsdq;
    @XmlElement(required = true, nillable = true)
    protected String hyDm;

    /**
     * Gets the value of the yjskuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYjskuuid() {
        return yjskuuid;
    }

    /**
     * Sets the value of the yjskuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYjskuuid(String value) {
        this.yjskuuid = value;
    }

    /**
     * Gets the value of the zsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmDm() {
        return zsxmDm;
    }

    /**
     * Sets the value of the zsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmDm(String value) {
        this.zsxmDm = value;
    }

    /**
     * Gets the value of the zspmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * Sets the value of the zspmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the skssqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * Sets the value of the skssqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * Gets the value of the skssqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * Sets the value of the skssqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * Gets the value of the yjze property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjze() {
        return yjze;
    }

    /**
     * Sets the value of the yjze property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjze(Double value) {
        this.yjze = value;
    }

    /**
     * Gets the value of the yjye property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjye1() {
        return yjye1;
    }

    /**
     * Sets the value of the yjye property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjye1(Double value) {
        this.yjye1 = value;
    }

    /**
     * Gets the value of the sksxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSksxDm() {
        return sksxDm;
    }

    /**
     * Sets the value of the sksxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSksxDm(String value) {
        this.sksxDm = value;
    }

    /**
     * Gets the value of the zgswskfjDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    /**
     * Sets the value of the zgswskfjDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswskfjDm(String value) {
        this.zgswskfjDm = value;
    }

    /**
     * Gets the value of the sjgsdq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * Sets the value of the sjgsdq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * Gets the value of the hyDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHyDm() {
        return hyDm;
    }

    /**
     * Sets the value of the hyDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHyDm(String value) {
        this.hyDm = value;
    }

}
