package com.css.znsb.nssb.controller.cchxwssb.yyssycj;


import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.CxsQuerymxInitDTO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.HXZGSB10735Response;
import com.css.znsb.nssb.pojo.vo.cchxwssb.gy.ResVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.yyssycj.*;
import com.css.znsb.nssb.service.cchxwssb.yyssycj.YyssycjService;
import com.css.znsb.nssb.utils.GYObjectUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.css.znsb.framework.common.pojo.CommonResult.success;

/**
 *
 * @project 金四新电子税务局
 * @file YyssycjController.java 创建时间:2022年10月08日上午10:25:43
 * @title 烟叶税税源采集ctrl
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2022 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Slf4j
@RestController
@RequestMapping("/yyssycj")
@Tag(name = "烟叶税采集")
public class YyssycjController {

    @Resource
    private YyssycjService yyssycjService;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CompanyApi companyApi;

//    private static final String djxh = "10215100000000514667";
//    private static final String nsrsbh = "91511622762319341H";
//    private static final String shxydm = "91511622762319341H";
//    private static final String nsrmc = "四川省任公司";
//    private static final String yhlx_dm = "2";
//    private static final String zgswjgdm = "15116220800";
//    private static final String swjgdm = "15116220800";
//    private static final String nsrztdm = "03";
//    private static final String userId = "5100c7fcdb6c61c54ee49d4d96b3114d3d07";
//    private static final String userName = "田成";
//    private static final String nsrzjhm = "210106199010170051";

    @PostMapping("/v1/cxData")
    //本地解决跨域问题
    //@CrossOrigin
    @Operation(summary = "cxData查询初始化数据")
    public CommonResult<YysnssbbVO> cxData(@RequestBody CxInitReqVO reqVO){
        String djxh = reqVO.getDjxh();
        if(GYObjectUtils.isNull(djxh)){
            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
        }
/*        String zgswjgdm = reqVO.getZgswjgdm();
        if(GYObjectUtils.isNull(zgswjgdm)){
            return CommonResult.error(-1,"纳税人主管税务机关不能为空，请核实!");
        }*/
        String skssqq = reqVO.getSkssqq();
        String skssqz = reqVO.getSkssqz();
        String pzxh = reqVO.getPzxh();
        if(GYObjectUtils.isNull(skssqq)||GYObjectUtils.isNull(skssqz)){
            return CommonResult.error(-1,"税款所属期起止不能为空，请核实!");
        }
        YysnssbbVO yysnssbbVO = yyssycjService.cxData(reqVO);
        return success(yysnssbbVO);
    }

    @PostMapping("/v1/queryNsrxyxx")
    public CommonResult<ZnsbMhzcQyjbxxmxResVO> queryNsrxyxx(@RequestBody CxInitReqVO reqVO){
        final String djxh = reqVO.getDjxh();
        final String nsrsbh = reqVO.getNsrsbh();
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
        return nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
    }

    @PostMapping("/v1/queryNsrXzqh")
    public CommonResult<CompanyBasicInfoDTO> queryNsrXzqh(@RequestBody CxInitReqVO reqVO){
        final String djxh = reqVO.getDjxh();
        final String nsrsbh = reqVO.getNsrsbh();
        return companyApi.basicInfo(djxh,nsrsbh);
    }

    @PostMapping("/v1/queryYysgjkze")
    public CommonResult<GetYysgjeResponse> queryYysgjkze(@RequestBody CxInitReqVO cxInitReqVO){
        String nsrsbh = cxInitReqVO.getNsrsbh();
        if(GYObjectUtils.isNull(nsrsbh)){
            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
        }
        String skssqq = cxInitReqVO.getSkssqq();
        String skssqz = cxInitReqVO.getSkssqz();
        if(GYObjectUtils.isNull(skssqq)||GYObjectUtils.isNull(skssqz)){
            return CommonResult.error(-1,"税款所属期起止不能为空，请核实!");
        }
        //CommonResult<Object>  rpaResult = new CommonResult<>();//yyssycjService.queryYysgjkze(cxInitReqVO);
        final GetYysgjeResponse getYysgjeResponse = new GetYysgjeResponse();
        getYysgjeResponse.setYysgje(0D);
        return success(getYysgjeResponse);
    }

    @PostMapping("/v1/saveFsgqfxtx")
    public CommonResult<ResVO> saveFsgqfxtx(@RequestBody FsgqsycjfxtxVo fsgqsycjfxtxVo){
        String djxh = fsgqsycjfxtxVo.getDjxh();
        if(GYObjectUtils.isNull(djxh)){
            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
        }
        String skssqq = fsgqsycjfxtxVo.getSkssqq();
        String skssqz = fsgqsycjfxtxVo.getSkssqz();
        if(GYObjectUtils.isNull(skssqq)||GYObjectUtils.isNull(skssqz)){
            return CommonResult.error(-1,"税款所属期起止不能为空，请核实!");
        }
        ResVO resVO = new ResVO();
        resVO.setReturnCode("1");
        resVO.setReturnMsg("保存成功！");
//        Map<String, Object> mapCommonResult = yyssycjFeignClients.saveFsgqfxtx(fsgqfxtx,djxh,skssqq,skssqz);
        return success(resVO);
    }

    @PostMapping("/v1/saveFpfxtx")
    public CommonResult<ResVO> saveFpfxtx(@RequestBody YysgfpfxtxVo yysgfpfxtxVo){
        String djxh = yysgfpfxtxVo.getDjxh();
        if(GYObjectUtils.isNull(djxh)){
            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
        }
        String skssqq = yysgfpfxtxVo.getSkssqq();
        String skssqz = yysgfpfxtxVo.getSkssqz();
        if(GYObjectUtils.isNull(skssqq)||GYObjectUtils.isNull(skssqz)){
            return CommonResult.error(-1,"税款所属期起止不能为空，请核实!");
        }
        ResVO resVO = new ResVO();
        resVO.setReturnCode("1");
        resVO.setReturnMsg("保存成功！");
//        Map<String, Object> mapCommonResult = yyssycjFeignClients.saveFpfxtx(fpsgje,yysgjkze,djxh,skssqq,skssqz);
        return success(resVO);
    }

    @PostMapping("/v1/deleteYyssymx")
    public CommonResult<YyssycjResVo> deleteYyssymx(@RequestParam(value ="uuid")String uuid,@RequestParam(value ="zgswjgdm")String zgswjgdm){
//        String zgswjgdm = cxssbSessionUtils.getZgswskfjDm();
        if(GYObjectUtils.isNull(zgswjgdm)){
            return CommonResult.error(-1,"纳税人主管税务机关不能为空，请核实!");
        }
/*        YyssycjResVo responseVO = yyssycjFeignClients.deleteYyssymx(uuid,zgswjgdm);
        final Map<String,String> reqMap = new HashMap<>();
        reqMap.put("uuid",uuid);
        reqMap.put("zgswjgdm",zgswjgdm);
        final String params = JacksonUtils.toJson(reqMap);
        final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/yyssycj/v1/deleteYyssymx",
                "cxssb-service","post", InvokeGt4Constants.XTDM.XDJ,"",params);
        CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
        final YyssycjResVo responseVO = JsonUtils.toBean(data.getData(), YyssycjResVo.class);
        */
        YyssycjResVo yyssycjResVo = this.yyssycjService.deleteYyssymx(uuid,zgswjgdm);
        return success(yyssycjResVo);
    }

    @PostMapping("/v1/saveYyssycj")
    public CommonResult<YyssycjResVo> saveYyssycj(@RequestBody YysnssbbVO yysnssbbVO){
        String djxh = yysnssbbVO.getDjxh();
        if(GYObjectUtils.isNull(djxh)){
            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
        }
        String zgswjgdm = yysnssbbVO.getZgswjgdm();
        if(GYObjectUtils.isNull(zgswjgdm)){
            return CommonResult.error(-1,"纳税人主管税务机关不能为空，请核实!");
        }
/*        YyssycjResVo responseVO = yyssycjFeignClients.saveYyssycj(yysnssbbVO);
        final String json = JacksonUtils.toJson(yysnssbbVO);
        final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/yyssycj/v1/saveYyssycj",
                "cxssb-service","post", InvokeGt4Constants.XTDM.XDJ,json,"");
        CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
        YyssycjResVo responseVO = JsonUtils.toBean(data.getData(), YyssycjResVo.class);*/
        YyssycjResVo responseVO = yyssycjService.saveYyssycj(yysnssbbVO);
        return success(responseVO);
    }

    @PostMapping("/v1/querymxInitData")
    public CommonResult<HXZGSB10735Response> querymxInitData(@RequestBody CxsQuerymxInitDTO cxsQuerymxInitDTO){
        final List<String> syuuidList =  cxsQuerymxInitDTO.getSyuuidList();
        if(GYObjectUtils.isNull(syuuidList)||syuuidList.size()==0){
            return CommonResult.error(-1,"税源主键不能为空，请核实税源主键");
        }
        HXZGSB10735Response res = this.yyssycjService.querymxInitData(cxsQuerymxInitDTO);
        log.info("查询明细初始化数据成功，返回结果为{}",res);
        return CommonResult.success(res);
    }
}
