package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 申报表受理信息VO扩展2，增加办税人相关信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbslxxkz2VO", propOrder = { "bsy" })
@Getter
@Setter
public class Sbbslxxkz2VO extends SbbslxxVO {
    /**
     * 办税人
     */
    @XmlElement(nillable = true, required = true)
    protected String bsy;
}