
package com.css.znsb.nssb.pojo.bo.hxzg.sb243;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 跨地区税收收入分配比例情况表
 * 
 * <p>Java class for SBKdqsssrfpblqkbVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBKdqsssrfpblqkbVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="uuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="zsxmDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmDm"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm" minOccurs="0"/>
 *         &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm"/>
 *         &lt;element name="yxqq" type="{http://www.chinatax.gov.cn/dataspec/}yxqq"/>
 *         &lt;element name="yxqz" type="{http://www.chinatax.gov.cn/dataspec/}yxqz"/>
 *         &lt;element name="sfzcdbz" type="{http://www.chinatax.gov.cn/dataspec/}sfzcdbz"/>
 *         &lt;element name="fpbl" type="{http://www.chinatax.gov.cn/dataspec/}fpbl"/>
 *         &lt;element name="jzjtfpbl" type="{http://www.chinatax.gov.cn/dataspec/}jzjtfpbl" minOccurs="0"/>
 *         &lt;element name="skssswjgDm" type="{http://www.chinatax.gov.cn/dataspec/}skssswjgDm"/>
 *         &lt;element name="sjtbbz" type="{http://www.chinatax.gov.cn/dataspec/}sjtbbz"/>
 *         &lt;element name="bz" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz" minOccurs="0"/>
 *         &lt;element name="sjswjgDm" type="{http://www.chinatax.gov.cn/dataspec/}sjswjgDm" minOccurs="0"/>
 *         &lt;element name="kshztlbz" type="{http://www.chinatax.gov.cn/dataspec/}kshztlbz" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBKdqsssrfpblqkbVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "uuid",
    "djxh",
    "zsxmDm",
    "zspmDm",
    "xzqhszDm",
    "yxqq",
    "yxqz",
    "sfzcdbz",
    "fpbl",
    "jzjtfpbl",
    "skssswjgDm",
    "sjtbbz",
    "bz",
    "lrrDm",
    "lrrq",
    "xgrDm",
    "xgrq",
    "sjgsdq",
    "yxbz",
    "sjswjgDm",
    "kshztlbz",
    "swjgmc",
    "zsdlfsDm",
    "bkjdjxh",
    "bkjnsrsbh"
})
public class SBKdqsssrfpblqkbVO implements Serializable{

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -4746282560211452588L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String uuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xzqhszDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yxqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yxqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sfzcdbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fpbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jzjtfpbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String skssswjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjtbbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sjswjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kshztlbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String swjgmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsdlfsDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bkjdjxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bkjnsrsbh;

    /**
     * Gets the value of the uuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * Sets the value of the uuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUuid(String value) {
        this.uuid = value;
    }

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the zsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmDm() {
        return zsxmDm;
    }

    /**
     * Sets the value of the zsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmDm(String value) {
        this.zsxmDm = value;
    }

    /**
     * Gets the value of the zspmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * Sets the value of the zspmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * Gets the value of the xzqhszDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }

    /**
     * Sets the value of the xzqhszDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhszDm(String value) {
        this.xzqhszDm = value;
    }

    /**
     * Gets the value of the yxqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqq() {
        return yxqq;
    }

    /**
     * Sets the value of the yxqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqq(String value) {
        this.yxqq = value;
    }

    /**
     * Gets the value of the yxqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqz() {
        return yxqz;
    }

    /**
     * Sets the value of the yxqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqz(String value) {
        this.yxqz = value;
    }

    /**
     * Gets the value of the sfzcdbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfzcdbz() {
        return sfzcdbz;
    }

    /**
     * Sets the value of the sfzcdbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfzcdbz(String value) {
        this.sfzcdbz = value;
    }

    /**
     * Gets the value of the fpbl property.
     * 
     */
    public double getFpbl() {
        return fpbl;
    }

    /**
     * Sets the value of the fpbl property.
     * 
     */
    public void setFpbl(double value) {
        this.fpbl = value;
    }

    /**
     * Gets the value of the jzjtfpbl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJzjtfpbl() {
        return jzjtfpbl;
    }

    /**
     * Sets the value of the jzjtfpbl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJzjtfpbl(Double value) {
        this.jzjtfpbl = value;
    }

    /**
     * Gets the value of the skssswjgDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssswjgDm() {
        return skssswjgDm;
    }

    /**
     * Sets the value of the skssswjgDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssswjgDm(String value) {
        this.skssswjgDm = value;
    }

    /**
     * Gets the value of the sjtbbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjtbbz() {
        return sjtbbz;
    }

    /**
     * Sets the value of the sjtbbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjtbbz(String value) {
        this.sjtbbz = value;
    }

    /**
     * Gets the value of the bz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBz() {
        return bz;
    }

    /**
     * Sets the value of the bz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBz(String value) {
        this.bz = value;
    }

    /**
     * Gets the value of the lrrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * Sets the value of the lrrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * Gets the value of the lrrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * Sets the value of the lrrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * Gets the value of the xgrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * Sets the value of the xgrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * Gets the value of the xgrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * Sets the value of the xgrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * Gets the value of the sjgsdq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * Sets the value of the sjgsdq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * Gets the value of the yxbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxbz() {
        return yxbz;
    }

    /**
     * Sets the value of the yxbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxbz(String value) {
        this.yxbz = value;
    }

    /**
     * Gets the value of the sjswjgDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjswjgDm() {
        return sjswjgDm;
    }

    /**
     * Sets the value of the sjswjgDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjswjgDm(String value) {
        this.sjswjgDm = value;
    }

    /**
     * Gets the value of the kshztlbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKshztlbz() {
        return kshztlbz;
    }

    /**
     * Sets the value of the kshztlbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKshztlbz(String value) {
        this.kshztlbz = value;
    }

    /**
     * Gets the value of the swjgmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSwjgmc() {
        return swjgmc;
    }

    /**
     * Sets the value of the swjgmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSwjgmc(String swjgmc) {
        this.swjgmc = swjgmc;
    }

    /**
     * Gets the value of the zsdlfsDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsdlfsDm() {
        return zsdlfsDm;
    }

    /**
     * Sets the value of the zsdlfsDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsdlfsDm(String zsdlfsDm) {
        this.zsdlfsDm = zsdlfsDm;
    }

    /**
     * Gets the value of the bkjdjxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBkjdjxh() {
        return bkjdjxh;
    }

    /**
     * Sets the value of the bkjdjxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBkjdjxh(String bkjdjxh) {
        this.bkjdjxh = bkjdjxh;
    }

    /**
     * Gets the value of the bkjnsrsbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBkjnsrsbh() {
        return bkjnsrsbh;
    }

    /**
     * Sets the value of the bkjnsrsbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBkjnsrsbh(String bkjnsrsbh) {
        this.bkjnsrsbh = bkjnsrsbh;
    }
    
}
