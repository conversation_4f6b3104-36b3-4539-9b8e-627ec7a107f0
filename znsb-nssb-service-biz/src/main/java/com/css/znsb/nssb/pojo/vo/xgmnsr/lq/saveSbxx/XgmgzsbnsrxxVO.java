package com.css.znsb.nssb.pojo.vo.xgmnsr.lq.saveSbxx;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "小规模更正保存纳税人信息VO")
@Data
public class XgmgzsbnsrxxVO {

    @Schema(description = "凭证序号")
    @JsonProperty(value = "pzxh")
    private String pzxh;

    @Schema(description = "申报uuid")
    @JsonProperty(value = "sbuuid")
    private String sbuuid;

    @Schema(description = "场景参数")
    @JsonProperty(value = "scenceCs")
    private String scenceCs;

    @Schema(description = "登记序号")
    @JsonProperty(value = "djxh")
    private String djxh;

    @Schema(description = "申报日期")
    @JsonProperty(value = "sbrq1")
    private String sbrq1;

    @Schema(description = "应征凭证种类代码")
    @JsonProperty(value = "yzpzzlDm")
    private String yzpzzlDm;

    @Schema(description = "申报类型代码")
    @JsonProperty(value = "sbsxDm1")
    private String sbsxDm1;

}
