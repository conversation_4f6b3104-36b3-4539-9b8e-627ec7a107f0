package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《加油站月销售油品汇总表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jyzyxsyphzbGridlbVO", propOrder = { "xsjeBtgjyjLjs", "xsjeBtgjyjBys", "xsjeTgjyjLjs", "xsjeTgjyjBys", "ysxsslLjs", "ysxsslBys", "ykcylJcLjs", "ykcylDcLjs", "ykcylDkLjs", "ykcylZyLjs", "ykcylJcBys", "ykcylDcBys", "ykcylDkBys", "ykcylZyBys", "cyslLjs", "cyslBys" })
@Getter
@Setter
public class JyzyxsyphzbGridlbVO {
    /**
     * 销售金额不通过加油机累计数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal xsjeBtgjyjLjs;

    /**
     * 销售金额不通过加油机本月数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal xsjeBtgjyjBys;

    /**
     * 销售金额通过加油机累计数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal xsjeTgjyjLjs;

    /**
     * 销售金额通过加油机本月数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal xsjeTgjyjBys;

    /**
     * 应税销售数量累计数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ysxsslLjs;

    /**
     * 应税销售数量本月数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ysxsslBys;

    /**
     * 应扣除油量累计数检测
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ykcylJcLjs;

    /**
     * 应扣除油量累计数代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ykcylDcLjs;

    /**
     * 应扣除油量累计数倒库
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ykcylDkLjs;

    /**
     * 应扣除油量累计数自用
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ykcylZyLjs;

    /**
     * 应扣除油量本月数检测
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ykcylJcBys;

    /**
     * 应扣除油量本月数代储
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ykcylDcBys;

    /**
     * 应扣除油量本月数倒库
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ykcylDkBys;

    /**
     * 应扣除油量本月数自用
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ykcylZyBys;

    /**
     * 出油数量累计数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal cyslLjs;

    /**
     * 出油数量本月数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal cyslBys;
}