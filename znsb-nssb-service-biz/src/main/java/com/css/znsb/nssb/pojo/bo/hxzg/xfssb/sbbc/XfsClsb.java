
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《消费税》车类申报
 * 
 * <p>Java class for xfsClsb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfsClsb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="xfssb5" type="{http://www.chinatax.gov.cn/dataspec/}xfssb_xqc"/>
 *         &lt;element name="xfssb5_fb1" type="{http://www.chinatax.gov.cn/dataspec/}bqdsdjsejsbxqc" minOccurs="0"/>
 *         &lt;element name="xfssb5_fb2" type="{http://www.chinatax.gov.cn/dataspec/}scjyqkbxqc" minOccurs="0"/>
 *         &lt;element name="xfssb5_fb3" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsemxb" minOccurs="0"/>
 *         &lt;element name="xfssb5_fb4" type="{http://www.chinatax.gov.cn/dataspec/}hznsqyxfsfpb" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfsClsb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "xfssb5",
    "xfssb5Fb1",
    "xfssb5Fb2",
    "xfssb5Fb3",
    "xfssb5Fb4"
})
public class XfsClsb
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected XfssbXqc xfssb5;
    @XmlElement(name = "xfssb5_fb1", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqdsdjsejsbxqc xfssb5Fb1;
    @XmlElement(name = "xfssb5_fb2", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Scjyqkbxqc xfssb5Fb2;
    @XmlElement(name = "xfssb5_fb3", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqjmsemxb xfssb5Fb3;
    @XmlElement(name = "xfssb5_fb4", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Hznsqyxfsfpb xfssb5Fb4;

    /**
     * Gets the value of the xfssb5 property.
     * 
     * @return
     *     possible object is
     *     {@link XfssbXqc }
     *     
     */
    public XfssbXqc getXfssb5() {
        return xfssb5;
    }

    /**
     * Sets the value of the xfssb5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfssbXqc }
     *     
     */
    public void setXfssb5(XfssbXqc value) {
        this.xfssb5 = value;
    }

    /**
     * Gets the value of the xfssb5Fb1 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqdsdjsejsbxqc }
     *     
     */
    public Bqdsdjsejsbxqc getXfssb5Fb1() {
        return xfssb5Fb1;
    }

    /**
     * Sets the value of the xfssb5Fb1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqdsdjsejsbxqc }
     *     
     */
    public void setXfssb5Fb1(Bqdsdjsejsbxqc value) {
        this.xfssb5Fb1 = value;
    }

    /**
     * Gets the value of the xfssb5Fb2 property.
     * 
     * @return
     *     possible object is
     *     {@link Scjyqkbxqc }
     *     
     */
    public Scjyqkbxqc getXfssb5Fb2() {
        return xfssb5Fb2;
    }

    /**
     * Sets the value of the xfssb5Fb2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Scjyqkbxqc }
     *     
     */
    public void setXfssb5Fb2(Scjyqkbxqc value) {
        this.xfssb5Fb2 = value;
    }

    /**
     * Gets the value of the xfssb5Fb3 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqjmsemxb }
     *     
     */
    public Bqjmsemxb getXfssb5Fb3() {
        return xfssb5Fb3;
    }

    /**
     * Sets the value of the xfssb5Fb3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqjmsemxb }
     *     
     */
    public void setXfssb5Fb3(Bqjmsemxb value) {
        this.xfssb5Fb3 = value;
    }

    /**
     * Gets the value of the xfssb5Fb4 property.
     * 
     * @return
     *     possible object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public Hznsqyxfsfpb getXfssb5Fb4() {
        return xfssb5Fb4;
    }

    /**
     * Sets the value of the xfssb5Fb4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public void setXfssb5Fb4(Hznsqyxfsfpb value) {
        this.xfssb5Fb4 = value;
    }

}
