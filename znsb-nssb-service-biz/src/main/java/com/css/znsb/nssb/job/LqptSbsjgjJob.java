package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbqkcxRespDTO;
import com.css.znsb.nssb.utils.LqptSbsjgjUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class LqptSbsjgjJob {

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private SjjhService sjjhService;

    @XxlJob("LqptSbsjgjJob")
    public void execute() {
        log.info("开始从乐企平台归集申报数据");
        String nsrsbhJsonArray = XxlJobHelper.getJobParam();
        log.info("获取到纳税人识别号数组：{}", nsrsbhJsonArray);
        boolean wqsjgjbz = false;
        List<String> nsrsbhList = new ArrayList<>();
        if (GyUtils.isNotNull(nsrsbhJsonArray)) {
            wqsjgjbz = true;
            nsrsbhList = JsonUtils.toList(nsrsbhJsonArray, String.class);
        }
        log.info("往期归集标志：{}", wqsjgjbz);
        List<Map<String, String>> nsrxxList = getNsrxxList(nsrsbhList);
        log.info("获取到纳税人信息：{}", nsrxxList);

        List<SjjhDTO> sjjhList = LqptSbsjgjUtils.buildSjjhList(nsrxxList, wqsjgjbz);
        log.info("组装数据交换数据：{}", sjjhList);
        for (SjjhDTO sjjhDTO : sjjhList) {
            CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
            int i = 0;
            while (!handlerCxlq(result) && i < 10) {
                result = sjjhService.saveSjjhJob(sjjhDTO);
                i++;
            }
        }
    }

    private static boolean handlerCxlq(CommonResult<Object> result) {
        if (result.isSuccess()) {
            SbqkcxRespDTO sbqk = (SbqkcxRespDTO) result.getData();
            if (!"00".equals(sbqk.getReturncode())) {
                return false;
            } else if (GyUtils.isNotNull(sbqk.getSbxxList()) && GyUtils.isNull(sbqk.getSbmxxxList())) {
                return false;
            } else if (GyUtils.isNull(sbqk.getSbxxList()) && GyUtils.isNotNull(sbqk.getSbmxxxList())) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    private List<Map<String, String>> getNsrxxList(List<String> nsrsbhList) {
        List<Map<String, String>> nsrxxList = new ArrayList<>();
        if (GyUtils.isNotNull(nsrsbhList)) {
            for (String nsrsbh : nsrsbhList) {
                ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
                reqVO.setNsrsbh(nsrsbh);
                // 调用接口获取纳税人信息
                CommonResult<ZnsbMhzcQyjbxxmxResVO> result = nsrxxApi.getNsrxxByNsrsbh(reqVO);
                if (result.isSuccess() && GyUtils.isNotNull(result.getData())
                        && GyUtils.isNotNull(result.getData().getJbxxmxsj())) {
                    List<JbxxmxsjVO> jbxxmxList = result.getData().getJbxxmxsj();
                    for (JbxxmxsjVO jbxxmxsj : jbxxmxList) {
                        Map<String, String> nsrxxMap = new HashMap<>();
                        String djxh = jbxxmxsj.getDjxh();
                        nsrxxMap.put("nsrsbh", jbxxmxsj.getNsrsbh());
                        nsrxxMap.put("djxh", djxh);

                        CommonResult<CompanyBasicInfoDTO> basicInfoResult =
                                companyApi.basicInfo(djxh, jbxxmxsj.getNsrsbh());
                        if (basicInfoResult.isSuccess() && GyUtils.isNotNull(basicInfoResult.getData())) {
                            nsrxxMap.put("xzqhszDm", basicInfoResult.getData().getXzqhszDm());
                            nsrxxList.add(nsrxxMap);
                        }
                    }
                }
            }
        } else {
            CommonResult<List<String>> djxhsResult = companyApi.getAllDjxh();
            if (djxhsResult.isSuccess() && GyUtils.isNotNull(djxhsResult.getData())) {
                for (String djxh : djxhsResult.getData()) {
                    CommonResult<CompanyBasicInfoDTO> basicInfoResult = companyApi.basicInfo(djxh, "");
                    if (basicInfoResult.isSuccess() && GyUtils.isNotNull(basicInfoResult.getData())) {
                        Map<String, String> nsrxxMap = new HashMap<>();
                        CompanyBasicInfoDTO basicInfo = basicInfoResult.getData();
                        nsrxxMap.put("nsrsbh", basicInfo.getNsrsbh());
                        nsrxxMap.put("djxh", djxh);
                        nsrxxMap.put("xzqhszDm", basicInfo.getXzqhszDm());
                        nsrxxList.add(nsrxxMap);
                    }
                }
            }
        }
        return nsrxxList;
    }
}
