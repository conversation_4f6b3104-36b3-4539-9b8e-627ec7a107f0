package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税纳税申报表（一般纳税人适用）》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_zbbw", propOrder = { "zzssyyybnsrZb" })
@Getter
@Setter
public class ZzssyyybnsrZbbw extends TaxDoc {
    /**
     * 《增值税纳税申报表（一般纳税人适用）》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_zb", required = true)
    @JSONField(name = "zzssyyybnsr_zb")
    protected ZzssyyybnsrZb zzssyyybnsrZb;
}