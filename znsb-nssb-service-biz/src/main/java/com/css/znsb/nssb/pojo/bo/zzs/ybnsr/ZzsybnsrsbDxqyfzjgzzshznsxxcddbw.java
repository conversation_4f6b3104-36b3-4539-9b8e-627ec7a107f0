package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《电信企业分支机构增值税汇总纳税信息传递单》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_dxqyfzjgzzshznsxxcddbw", propOrder = { "zzsybnsrsbDxqyfzjgzzshznsxxcdd" })
@Getter
@Setter
public class ZzsybnsrsbDxqyfzjgzzshznsxxcddbw extends TaxDoc {
    /**
     * 《电信企业分支机构增值税汇总纳税信息传递单》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_dxqyfzjgzzshznsxxcdd", required = true)
    @JSONField(name = "zzsybnsrsb_dxqyfzjgzzshznsxxcdd")
    protected ZzsybnsrsbDxqyfzjgzzshznsxxcdd zzsybnsrsbDxqyfzjgzzshznsxxcdd;
}