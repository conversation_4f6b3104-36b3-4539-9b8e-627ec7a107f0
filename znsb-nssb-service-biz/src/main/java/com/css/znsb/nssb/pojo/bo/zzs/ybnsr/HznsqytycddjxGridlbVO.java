package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 汇总纳税企业通用传递单Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hznsqytycddjxGridlbVO", propOrder = { "ewbhxh", "bqfsjx", "mshwyJxsezc", "fzcssJxsezc", "qtJxsezc", "xjJxsezc", "bqsjkce" })
@Getter
@Setter
public class HznsqytycddjxGridlbVO {
    /**
     * 二维表行序号
     */
    protected String ewbhxh;

    /**
     * 本期发生进项
     */
    protected BigDecimal bqfsjx;

    /**
     * 免税货物用进项税额转出
     */
    protected BigDecimal mshwyJxsezc;

    /**
     * 非正常损失进项税额转出
     */
    protected BigDecimal fzcssJxsezc;

    /**
     * 其他进项税额转出
     */
    protected BigDecimal qtJxsezc;

    /**
     * 小计进项税额转出
     */
    protected BigDecimal xjJxsezc;

    /**
     * 本期实际扣除额
     */
    protected BigDecimal bqsjkce;
}