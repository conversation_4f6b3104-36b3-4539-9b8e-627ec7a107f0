package com.css.znsb.nssb.controller.sfxyqd;

import cn.hutool.core.io.resource.ClassPathResource;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00038.HXZGZS00038Request;
import com.css.znsb.nssb.pojo.vo.sfxyqd.*;
import com.css.znsb.nssb.pojo.vo.sfxyqd.lqbw.LqSfxyReqVO;
import com.css.znsb.nssb.service.sfxyqg.SfxyqdService;
import com.css.znsb.nssb.util.SjryUtil;
import com.css.znsb.nssb.utils.GYCastUtils;
import com.css.znsb.tzzx.pojo.tzzx.gy.ExportTzVO;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/yssfhjxy")
public class SfxyqdController {

    @Resource
    SfxyqdService yssfhjxyService;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private SjjhService sjjhService;


    /**
     * @param zSsfxyVO
     * @return {@link CommonResult<Object> }
     * @name 生成三方协议号，并且成功之后发起保存流程
     * @description 相关说明
     * @time 创建时间:2024年08月20日下午07:35:19
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @PostMapping("/v1/createSfxyhm")
    public CommonResult<Object> createSfxyhm(@RequestBody ZSSfxyVO zSsfxyVO, @RequestParam(value = "Djxh") String djxh,@RequestParam(value = "Nsrsbh") String nsrsbh,@RequestParam(value = "XzqhszDm") String xzqhszDm,@RequestParam("SwjgDm") String swjgDm) {
        //入参较验，如果不为空，并且长度为11位数字，验证通过，流程继续；
        // Map<String, Object> map;
        if (StringUtils.isEmpty(swjgDm) && swjgDm.length() != 11) {
            return CommonResult.error(-1, "入参较验错误，参数只能为11位数字，业务阻断。");
        } else {
            // 由于是异步查询，导致不能实时获取三方协议号返回给页面，然后进行保存流程
            // 所以修改前端，如果三方协议号为空，那就生成三方协议号之后，发起保存流程

            HXZGZS00038Request hxzgzs00038Request = new HXZGZS00038Request();
            hxzgzs00038Request.setSkssswjgDm(swjgDm);

            final Map<String,Object> reqMap = new HashMap<>();
            reqMap.put("bwnr",hxzgzs00038Request);
            reqMap.put("saveSfhjxy",zSsfxyVO);

            final SjjhDTO sjjhDTO = new SjjhDTO();
            sjjhDTO.setSjjhlxDm("BC00000001");
            sjjhDTO.setYwbm("SFXYQD001");
            sjjhDTO.setDjxh(djxh);
            sjjhDTO.setNsrsbh(nsrsbh);
            sjjhDTO.setXzqhszDm(xzqhszDm);
            sjjhDTO.setBwnr(new Gson().toJson(reqMap));
            return sjjhService.saveSjjhJob(sjjhDTO);
        }
    }

    /**
     * 报验户时，查询核心征管已验证+税智撑省外三方协议
     *
     * @param
     * @return map
     */
    @GetMapping("/v1/listSfhjxyByh")
    public CommonResult<SfxyxxVo> listSfhjxyByh(@RequestParam("Djxh") String djxh, @RequestParam("ZgswjDm") String zgswjDm, @RequestParam("nsrsbh") String nsrsbh, @RequestParam("xzqhszDm") String xzqhszDm) {
        final Map<String,Object> reqMap = new HashMap<>();
        reqMap.put("djxh",djxh);
        reqMap.put("nsrsbh",nsrsbh);
        reqMap.put("xzqhszDm",xzqhszDm);
        //入参较验，判断djxh是否为空，如果djxh为空
        SfxyxxVo sfxyxxVo = new SfxyxxVo();
        if (StringUtils.isEmpty(djxh)) {
            return CommonResult.error(-1, "注册地登记序号不能为空，流程阻断");
        }

        log.info("================省内mbdDjxh的值==============："+ djxh);
        log.info("================省内mbdzgswjgDm的值================："+ zgswjDm);
        //调用service层listSfhjxy方法，查询三方协议信息
        SfxyxxDTO sfxyxxDTO = yssfhjxyService.listSfhjxyJ3cx(reqMap);
        List<SfhjxyxxDTO> sfhjxyxxList = null;
        if(!GyUtils.isNull(sfxyxxDTO.getSfhjxyxxList())){
            sfhjxyxxList = sfxyxxDTO.getSfhjxyxxList().stream().filter(obj -> "02".equals(obj.getSfxyztDm())).collect(Collectors.toList());
            log.info("================省内listSfhjxy值================："+ JsonUtils.toJson(sfhjxyxxList));
        }else{
            //企业端取消查询省外数据
//            //根据税智撑要求，传上级税务机关代码（取不到跨省机关上级，需求反馈后4位改成0）
//            if(!GyUtils.isNull(zgswjDm)){
//                zgswjDm = zgswjDm.substring(0,zgswjDm.length()-4) +  "0000";
//            }
//            log.info("================省外mbdDjxh的值================："+ djxh);
//            log.info("================省外mbdzgswjgDm的值================："+ zgswjDm);
//            //查询税智撑省外三方协议
//            sfhjxyxxList = this.hqyssfwthjxy(djxh, zgswjDm, sfhjxyxxList);
//            log.info("================省外listSfhjxy值================："+ JsonUtils.toJson(sfhjxyxxList));
        }
        if(GyUtils.isNull(sfhjxyxxList)){
            sfhjxyxxList = new ArrayList<>();
        }
        sfxyxxVo.setSfhjxyxxList(sfhjxyxxList);
        sfxyxxVo.setReturnCode(sfxyxxDTO.getReturnCode());
        sfxyxxVo.setReturnMsg(sfxyxxDTO.getReturnMsg());
        return CommonResult.success(sfxyxxVo);
    }


    public CommonResult<SfxyxxVo> listSfhjxyHd(Map<String,Object> reqMap) {
        //入参较验，判断djxh是否为空，如果djxh为空
        SfxyxxVo sfxyxxVo = new SfxyxxVo();
        if (GyUtils.isNull(reqMap)) {
            return CommonResult.error(-1, "入参错误，必录项不能为空，流程阻断");
        }
        //调用service层listSfhjxy方法，查询三方协议信息
        SfxyxxDTO sfxyxxDTO = yssfhjxyService.listSfhjxyJ3cx(reqMap);
        sfxyxxVo.setSfhjxyxxList(sfxyxxDTO.getSfhjxyxxList());
        sfxyxxVo.setReturnCode(sfxyxxDTO.getReturnCode());
        sfxyxxVo.setReturnMsg(sfxyxxDTO.getReturnMsg());
        sfxyxxVo.setIsdjhgl("N");
        return CommonResult.success(sfxyxxVo);
    }

    @GetMapping("/v1/listCkzhxx")
    public CommonResult<CkzhxxVO> listCkzhxx(@RequestParam("Djxh") String djxh) {
        //入参较验，判断djxh是否为空，如果djxh为空，
        // 则返回结果编码,结果消息=入参错误，必录项不能为空，流程阻断；否则流程继续
        CkzhxxVO ckzhxxVO = new CkzhxxVO();
        CkzhxxDTO ckzhxxDTO = yssfhjxyService.listCkzhxx(djxh);
        ckzhxxVO.setCkzhxxList(ckzhxxDTO.getCkzhxxList());
        ckzhxxVO.setReturnCode(ckzhxxDTO.getReturnCode());
        ckzhxxVO.setReturnMsg(ckzhxxDTO.getReturnMsg());
        return CommonResult.success(ckzhxxVO);
    }

    /**
     * 保存新增
     * 该接口调整时，发邮件给唐文敏、曾凯、赵继平、抄送赵凯鹏、嵇云梅、以及各个组长
     * @param zSsfxyVO
     * @return sfxyxxResDTO
     */
    public CommonResult<Map<String, Object>> saveSfhjxy(@RequestBody ZSSfxyVO zSsfxyVO,Map<String,Object> sjjhMap) {
        // 入参较验，如果较验不通过，返回结果编码;结果消息=入参错误，必录项不能为空，流程阻断。
        //2. 调用service层saveSfhjxy方法，保存三方协议信息。
        //3. 接收service层返回的结果编码与结果消息并同步返回给调用方
        Map<String, Object> map;
        if (zSsfxyVO == null) {
            return CommonResult.error(-1, "入参错误，必录项不能为空");
        }
        String sjry=zSsfxyVO.getSjry();
        if (StringUtils.isEmpty(sjry)) {
            sjry= SjryUtil.getSjry(zSsfxyVO.getSkssswjgDm());
        }
        zSsfxyVO.setLrrDm(sjry);
        map = yssfhjxyService.saveSfhjxy(zSsfxyVO, zSsfxyVO.getSglrbj(),sjjhMap);
        return CommonResult.success(map);
    }

    /**
     * 验证三方协议信息
     *
     * @param
     * @return sfxyxxResDTO
     */
    @Transactional
    @PostMapping("/v1/checkSfhjxy")
    public CommonResult<Object> checkSfhjxy(@RequestParam("Djxh") String djxh, @RequestParam("Nsrsbh") String nsrsbh, @RequestParam("XzqhszDm") String xzqhszDm, @RequestParam("Sfxydjuuid") String sfxydjuuid) {
        final Map<String,Object> sjjhMap = new HashMap<>();
        sjjhMap.put("djxh",djxh);
        sjjhMap.put("nsrsbh",nsrsbh);
        sjjhMap.put("xzqhszDm",xzqhszDm);
        log.info("乐企-验签三方协议-入参："+ JsonUtils.toJson(sjjhMap));
        final SfhjxyxxDTO req = new SfhjxyxxDTO();
        req.setDjxh(djxh);
        req.setSfxydjuuid(sfxydjuuid);
        log.info("乐企-验签三方协议-查询本地三方协议入参："+ JsonUtils.toJson(req));
        final List<SfhjxyxxDTO> list = yssfhjxyService.querySfhjxyxxListLocalData(req);
        log.info("乐企-验签三方协议-查询本地三方协议出参："+ JsonUtils.toJson(list));
        if (GyUtils.isNotNull(list)){
            SfhjxyxxDTO sfhjxyxxDTO = list.get(0);
            com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO zssfxyvoParam = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00041.ZSSfxyVO();
            BeanUtils.copyProperties(sfhjxyxxDTO, zssfxyvoParam);
            zssfxyvoParam.setSfxydjuuid(sfxydjuuid);
            //日期格式转换，否则为英文
            zssfxyvoParam.setSfxyyztgRq(DateUtils.toDateStrByFormatIndex(sfhjxyxxDTO.getSfxyyztgRq(),3));
            return yssfhjxyService.checkSfhjxy(zssfxyvoParam,sjjhMap);
        }else {
            return CommonResult.error(-1,"未查询到三方协议信息");
        }
    }

    /**
     * 更新三方协议
     *
     * @param
     * @return
     */
    @Transactional
    @PostMapping("/v1/updateSfhjxy")
    public CommonResult<Object> updateSfhjxy(@RequestBody ZSSfxyVO zsSfxyVO, @RequestParam(value = "Djxh") String djxh,@RequestParam(value = "Nsrsbh") String nsrsbh,@RequestParam(value = "XzqhszDm") String xzqhszDm) {
        final LqSfxyReqVO lqSfxyReqVO = new LqSfxyReqVO();
        lqSfxyReqVO.setDjxh(djxh);
        lqSfxyReqVO.setNsrsbh(nsrsbh);
        lqSfxyReqVO.setXzqhszDm(xzqhszDm);
        //以上lqSfxyReqVO实际没用，不知为啥传值，若后期确实无用，考虑删除
        zsSfxyVO.setDjxh(djxh);
        zsSfxyVO.setNsrsbh(nsrsbh);
        zsSfxyVO.setXzqhszDm(xzqhszDm);
        if (StringUtils.isBlank(zsSfxyVO.getSfxyuuid()) && StringUtils.isBlank(zsSfxyVO.getSfxydjuuid())) {
            return CommonResult.error(-1, "入参错误，必录项不能为空");
        }
        //todo 没有明白此处为啥要查询本地数据再赋值，那不是把修改内容替换了嘛，故注释，待后期确认无用直接删除
//        if (StringUtil.isEmpty(zsSfxyVO.getSfxyh()) && StringUtil.isEmpty(zsSfxyVO.getSfxyztDm())){
//            final SfhjxyxxDTO req = new SfhjxyxxDTO();
//            req.setDjxh(zsSfxyVO.getDjxh());
//            req.setSfxydjuuid(zsSfxyVO.getSfxyuuid());
//            final List<SfhjxyxxDTO> list = yssfhjxyService.querySfhjxyxxListLocalData(req);
//            if (GyUtils.isNotNull(list)){
//                SfhjxyxxDTO sfhjxyxxDTO = list.get(0);
//                sfhjxyxxDTO.setPkbz(zsSfxyVO.getPkbz());
//                BeanUtils.copyProperties(sfhjxyxxDTO,zsSfxyVO);
//                if (StringUtils.isBlank(zsSfxyVO.getSfxyuuid())){
//                    zsSfxyVO.setSfxyuuid(zsSfxyVO.getSfxydjuuid());
//                }
//            }
//        }
        //业务渠道修改：分web和app，依赖注入的sjry
        //zsSfxyVO.setXgrDm(SjryUtil.getSjry(zsSfxyVO.getSkssswjgDm()));
        String sjry=zsSfxyVO.getSjry();
        if (StringUtils.isEmpty(sjry)) {
            sjry=SjryUtil.getSjry(SjryUtil.getSjry(zsSfxyVO.getSkssswjgDm()));
        }
        zsSfxyVO.setXgrDm(sjry);
        //调用service层updateSfhjxy方法，更新三方协议信息。
        //3. 接收service层返回的结果编码 与结果消息，并返回给调用方。
        return CommonResult.success(yssfhjxyService.updateSfhjxy(zsSfxyVO,lqSfxyReqVO));
    }

    /**
     * 作废
     *
     * @param sfxyuuid
     * @return sfxyxxResDTO
     */
    @Transactional
    @PostMapping("/v1/cancelSfhjxy")
    public CommonResult<Object> cancelSfhjxy(@RequestParam("Djxh") String djxh, @RequestParam("Nsrsbh") String nsrsbh, @RequestParam("XzqhszDm") String xzqhszDm, @RequestParam("Sfxyuuid") String sfxyuuid) {
        if (StringUtils.isBlank(sfxyuuid)) {
            return CommonResult.error(-1, "入参错误，必录项不能为空");
        }else{
            return CommonResult.success(yssfhjxyService.cancelSfhjxy(djxh,nsrsbh,xzqhszDm,sfxyuuid));
        }
//        sfxyxxResDTO sfxyxxResDTO = yssfhjxyService.cancelSfhjxy(sfxyuuid,swjgDm);
//        return CommonResult.success(sfxyxxResDTO);
//        HXZGZS00040Request hxzgzs00040Request = new HXZGZS00040Request();
//        hxzgzs00040Request.setSfxydjuuid(sfxyuuid);
//        hxzgzs00040Request.setZfrDm("qyd");
//
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("BC00000004");
//        sjjhDTO.setYwbm("SFXYQD001");
//        sjjhDTO.setDjxh(djxh);
//        sjjhDTO.setNsrsbh(nsrsbh);
//        sjjhDTO.setXzqhszDm(xzqhszDm);
//        sjjhDTO.setBwnr(yssfhjxyService.createRequestXml("03","SXN022011401","zfsfxyxx",djxh,nsrsbh,xzqhszDm,hxzgzs00040Request));
//        return yssfhjxyService.saveSjjhJob(sjjhDTO);
//            yssfhjxyService.cancelSfhjxy(djxh,nsrsbh,xzqhszDm,sfxyuuid);
    }

    /**
     * @param zsSfxyVO
     * @return {@link CommonResult<Object> }
     * @name 中止
     * @description 相关说明
     * @time 创建时间:2024年08月09日下午03:38:38
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Transactional
    @PostMapping("/v1/stopSfhjxy")
    public CommonResult<Object> stopSfhjxy(@RequestBody ZSSfxyVO zsSfxyVO, @RequestParam(value = "Djxh") String djxh,@RequestParam(value = "Nsrsbh") String nsrsbh,@RequestParam(value = "XzqhszDm") String xzqhszDm) {
        log.info(" stopSfhjxy 注销三方协议 zsSfxyVO ："+JsonUtils.toJson(zsSfxyVO)+" djxh:"+djxh+" nsrsbh:"+nsrsbh+" xzqhszDm:"+xzqhszDm);
        if (StringUtils.isBlank(zsSfxyVO.getSfxyuuid()) && StringUtils.isBlank(zsSfxyVO.getSfxydjuuid())) {
            return CommonResult.error(-1, "入参错误，必录项不能为空");
        }else{
            final SfhjxyxxDTO req = new SfhjxyxxDTO();
            req.setDjxh(djxh);
            req.setSfxydjuuid(zsSfxyVO.getSfxyuuid());
            final List<SfhjxyxxDTO> list = yssfhjxyService.querySfhjxyxxListLocalData(req);
            if (GyUtils.isNotNull(list)){
                SfhjxyxxDTO sfhjxyxxDTO = list.get(0);
                BeanUtils.copyProperties(sfhjxyxxDTO,zsSfxyVO);
                if (StringUtils.isBlank(zsSfxyVO.getSfxyuuid())){
                    zsSfxyVO.setSfxyuuid(zsSfxyVO.getSfxydjuuid());
                }
            }
        }

        //业务渠道修改：分web和app，依赖注入的sjry
        String sjry=zsSfxyVO.getSjry();
        if (StringUtils.isEmpty(sjry)) {
            sjry=SjryUtil.getSjry(zsSfxyVO.getSkssswjgDm());
        }
        zsSfxyVO.setXgrDm(sjry);
        com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00042.ZSSfxyVO hxzgZSSfxyVO = new com.css.znsb.nssb.pojo.vo.hxzg.hxzgzs00042.ZSSfxyVO();
        BeanUtils.copyProperties(zsSfxyVO, hxzgZSSfxyVO);
        if (StringUtils.isNotBlank(zsSfxyVO.getSfxyuuid())) {
            hxzgZSSfxyVO.setSfxydjuuid(zsSfxyVO.getSfxyuuid());
        }
//        hxzgZSSfxyVO.setSfxyztDm("04");     //04终止
        return yssfhjxyService.stopSfhjxy(hxzgZSSfxyVO,djxh,nsrsbh,xzqhszDm);

    }

    @GetMapping("/v1/getDictList")
    public CommonResult<Map<String, List<Map<String, Object>>>> getDictList(@RequestParam("dictKeys") String dictKeys) {
        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
        if (StringUtils.isNotEmpty(dictKeys)) {
            String[] dictKeyList = dictKeys.split(",");
            for (String dictKey : dictKeyList) {
                
                List<Map<String, Object>> dictList = CacheUtils.getTableData(dictKey.toLowerCase(Locale.ROOT));
                if (GyUtils.isNotNull(dictList)) {
                    if ("DM_GY_YHHB".equals(dictKey)) {
                        dictList = this.orderByYhhb(dictList);
                    }
                }
                resultMap.put(dictKey, dictList);
            }
        }
        return CommonResult.success(resultMap);
    }


    public CommonResult<Map<String, List<Map<String, Object>>>> getPkbzListHd(@RequestParam("djxh") String djxh, @RequestParam("swjgDm") String swjgDm, @RequestParam(value = "sfxyuuid", required = false) String sfxyuuid) {
        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
        String pkbzVo = null;
        if (StringUtils.isNotBlank(sfxyuuid)) {
            SfhjxyxxDTO sfhjxyxxDTO = yssfhjxyService.getSfxyxxByUuid(djxh, sfxyuuid);
            if ("02".equals(sfhjxyxxDTO.getSfxyztDm())) {
                pkbzVo = sfhjxyxxDTO.getPkbz();
            }
        }

        List<Map<String, Object>> existsPkbzlist = this.getPkztList(djxh, swjgDm, sfxyuuid, pkbzVo);
        Map<String, String> existPkztMap = new HashMap<>();
        existsPkbzlist.forEach(obj -> {
            existPkztMap.put(obj.get("SFXYZT_DM").toString(), obj.get("SFXYZT_DM").toString());
        });
        List<Map<String, Object>> pkbzList = yssfhjxyService.getPkbzList();
        Map<String, Map<String, Object>> pkbzMap = new HashMap<>();
        pkbzList.forEach(map -> {
            pkbzMap.put(map.get("SFXYZT_DM").toString(), map);
        });
        if (StringUtils.isBlank(existPkztMap.get(pkbzVo))) {
            Map<String, Object> map = pkbzMap.get(pkbzVo);
            if (GyUtils.isNotNull(map)) {
                existsPkbzlist.add(map);
            }
        }
        Collections.sort(existsPkbzlist, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                if (Integer.parseInt(o1.get("SFXYZT_DM").toString()) > Integer.parseInt(o2.get("SFXYZT_DM").toString())) {
                    return 1;
                }
                return -1;
            }
        });
        resultMap.put("DM_GY_PKBZ", existsPkbzlist);
        return CommonResult.success(resultMap);
    }

    @GetMapping("/v1/checkZqyh")
    public CommonResult<Map<String, String>> checkZqyh(@RequestParam("yhyywdDm") String yhhbDm) {
        return CommonResult.success(yssfhjxyService.checkZqyh(yhhbDm));
    }


    public List<Map<String, Object>> getPkztList(String djxh, String swjgDm, String sfxyuuid, String pkbzVo) {
        String pkbzCs = yssfhjxyService.getPkbzCs(swjgDm);
        //新增不调用getSfxyAllPkbz，只修改调用
        //String pkbz = yssfhjxyService.getSfxyAllPkbz(djxh, sfxyuuid);
        String pkbz="";
        if(StringUtils.isNotBlank(sfxyuuid)){
            pkbz = yssfhjxyService.getSfxyAllPkbz(djxh, sfxyuuid);
        }
        List<Map<String, Object>> resultList = yssfhjxyService.getPkbzList();
        if (StringUtils.isBlank(pkbz)) {//首次或修改
            if ("N".equals(pkbzCs)) {
                resultList.remove(5);
                resultList.remove(4);
                resultList.remove(3);
                resultList.remove(2);
            } else { //批扣标志为Y
            }
        } else {  //非首次
            if ("N".equals(pkbzCs)) {
                if (StringUtils.isNotBlank(pkbz) && pkbz.contains("1")) {
                    resultList.remove(5);
                    resultList.remove(4);
                    resultList.remove(3);
                    resultList.remove(2);
                    resultList.remove(1);
                } else {
                    resultList.remove(5);
                    resultList.remove(4);
                    resultList.remove(3);
                    resultList.remove(2);
                }
            } else {
                if (StringUtils.isNotBlank(pkbz)) {
                    if ("1".equals(pkbzVo) || ("2".equals(pkbzVo) && !pkbz.contains("2")) || ("3".equals(pkbzVo) && !pkbz.contains("3"))) {
                        //当前修改的数据为所有批扣，带出所有状态
                    } else if (pkbz.contains("1")) {
                        resultList.remove(3);
                        resultList.remove(2);
                        resultList.remove(1);
                    } else if (pkbz.contains("2") && pkbz.contains("3")) {
                        resultList.remove(3);
                        resultList.remove(2);
                        resultList.remove(1);
                    } else if (pkbz.contains("2") && !pkbz.contains("3")) {
                        resultList.remove(2);
                        resultList.remove(1);
                    } else if (!pkbz.contains("2") && pkbz.contains("3")) {
                        resultList.remove(3);
                        resultList.remove(1);
                    }
                }
            }
        }
        return resultList;
    }

    private List<Map<String, Object>> orderByYhhb(List<Map<String, Object>> yhhbDOList) {
        List<Map<String, Object>> result = new ArrayList<>(yhhbDOList.size());
        List<Map<String, Object>> other = new ArrayList<>();
        List<Map<String, Object>> order = new ArrayList<>();
        Map<String, Object> mm = new HashMap<>();
        if (CollectionUtils.isNotEmpty(yhhbDOList)) {
            for (Map<String, Object> map : yhhbDOList) {
                // 201,202,203,326 放后面
                if ("201,202,203,326".contains(map.get("yhhbDm").toString())) {
                    other.add(map);
                } else if ("317,402".contains(map.get("yhhbDm").toString())) {
                    order.add(map);
                } else {
                    if ("314".equals(map.get("yhhbDm").toString())) {
                        mm = map;
                    }
                    if (isStartWithNum(map.get("yhhbDm").toString())) {
                        result.add(map);
                    } else {
                        other.add(map);
                    }
                }

            }
        }
        // 对result排序
        if (CollectionUtils.isNotEmpty(result)) {
            Map<String, Object> temp;
            for (int i = 0; i < result.size() - 1; i++) {
                for (int j = 0; j < result.size() - 1 - i; j++) {
                    String dm1 = result.get(j).get("yhhbDm").toString();
                    String dm2 = result.get(j + 1).get("yhhbDm").toString();
                    if (Integer.parseInt(dm1) > Integer.parseInt(dm2)) {
                        temp = result.get(j);
                        result.set(j, result.get(j + 1));
                        result.set(j + 1, temp);
                    }
                }
            }
            //农村合作银行
            if (!mm.isEmpty() && CollectionUtils.isNotEmpty(result)) {
                int res = result.indexOf(mm);
                result.addAll(res + 1, order);
            }
            result.addAll(other);
        }
        return result;
    }

    private boolean isStartWithNum(String str) {
        Pattern pattern = Pattern.compile("[1-9]*");
        Matcher isNum = pattern.matcher(str.charAt(0) + "");
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    //根据三方协议号查询三方协议信息
    @PostMapping(value = "/v1/getSfxyxxByDjxhUuid")
    public CommonResult<SfhjxyxxDTO> getSfxyxxByUuid(@RequestParam("Djxh") String djxh, @RequestParam("Sfxyuuid") String sfxyuuid) {
        return CommonResult.success(yssfhjxyService.getSfxyxxByUuid(djxh, sfxyuuid));
    }

    //根据报验户登记序号查询纳税主体的登记序号
    public Map<String,String> getYdjxhByMdbdjxh(String ydDjxh, String zgswjgDm, String nsrsbh){
        Map<String,String> mbdDjxhAndSwjgDm= new HashMap<>();
        String mbdDjxh="",mbdzgswjgDm="";
//        CommonResult<SfhjxyxxDTO> response = new CommonResult<>();
//        String ss = "{\"sfxydjuuid\":\"123\",\"sfxyh\":\"123\",\"djxh\":\"123\",\"yhhbDm\":\"104\",\"yhhbMc\":\"1\",\"jkzhmc\":\"1\",\"jkzh\":\"12345678\",\"khyhhh\":\"1\",\"qsyhhh\":\"1\",\"sfxyztDm\":\"02\",\"xyztmc\":\"\",\"skssswjgDm\":\"\",\"skssswjgMc\":\"1\",\"swjgmc\":\"1\",\"pkbz\":\"1\",\"pkbzmc\":\"1\",\"sfzjhm\":\"1\",\"slrq\":\"2004-07-05\",\"xzqhszDm\":\"340000\",\"yhyywdDm\":\"1045001092\",\"yhyywdMc\":\"1\",\"nsrsbh\":\"1\",\"nsrmc\":\"1\",\"zqyhbz\":\"1\",\"returnCode\":\"1\",\"returnMsg\":\"1\"}";
//        SfhjxyxxDTO invoke = JsonUtils.toBean(ss, SfhjxyxxDTO.class);
//        //CommonResult<SfhjxyxxDTO> response = yssfhjxyApi.getYdjxhByMdbdjxh(ydDjxh, zgswjgDm);
//        response.setData(invoke);
//        if(!GyUtils.isNull(response)){
//            mbdDjxh = response.getData().getDjxh();
//            mbdzgswjgDm=response.getData().getSkssswjgDm();
//        }
//
//        mbdDjxhAndSwjgDm.put("mbdDjxh",mbdDjxh);
//        mbdDjxhAndSwjgDm.put("mbdzgswjgDm",mbdzgswjgDm);
//        return mbdDjxhAndSwjgDm;

        ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
        znsbmhzcqyjbxxmxreqvo.setNsrsbh(nsrsbh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVo = nsrxxApi.getNsrxxByDjxh(znsbmhzcqyjbxxmxreqvo);
        List<JbxxmxsjVO> jbxxmxsj = nsrxxVo.getData().getJbxxmxsj();
        if (GyUtils.isNotNull(jbxxmxsj)){
            for (JbxxmxsjVO jbxxmxsjVO : jbxxmxsj) {
                if("03".equals(jbxxmxsjVO.getNsrztDm())&&"N".equals(jbxxmxsjVO.getKqccsztdjbz())&&"1110".equals(jbxxmxsjVO.getKzztdjlxDm())){
                    mbdDjxh = jbxxmxsjVO.getDjxh();
                    mbdzgswjgDm=jbxxmxsjVO.getZgswjDm();
                }
            }
        }
        mbdDjxhAndSwjgDm.put("mbdDjxh",mbdDjxh);
        mbdDjxhAndSwjgDm.put("mbdzgswjgDm",mbdzgswjgDm);
        return mbdDjxhAndSwjgDm;
    }

    public CommonResult<Map<String, String>> getHsjgdmByZgswskfjDm(@RequestParam("ZgswskfjDm") String zgswskfjDm){
        Map<String, String> result = new HashMap<>();
        String hsjgDm = zgswskfjDm;
        String hsjgMc = "";
        //TODO
        final Map<String,Object> qbsj= new HashMap<>();
        //final Map<String,Object> qbsj= cssDmcsService.getIndexData("CS_MH_XTCS","MH000000000000002");
        final String swjgDm = (String) qbsj.get("SJXX");
        final String provincemc = CacheUtils.dm2mc("dm_gy_swjg",swjgDm);
        //TODO
        Map<String, Object> map = new HashMap<>();
        //Map<String, Object> map = cssDmcsService.getIndexData("DM_QX_ZNJGDZB", zgswskfjDm);
        if(GyUtils.isNotNull(map)){
            hsjgDm = map.get("HSJG_DM").toString();
        }
        Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg", hsjgDm);
        if(GyUtils.isNotNull(swjgMap) && swjgMap.containsKey("swjgmc")){
            hsjgMc = swjgMap.get("swjgmc").toString();
        }
        result.put("swjgDm", hsjgDm);
        result.put("zgswjMc", hsjgMc);
        result.put("provincemc", provincemc);
        return CommonResult.success(result);
    }

    public Map<String, String> getNsrmcAndDqdexx(@RequestParam("Djxh") String djxh){
        Map<String, String> result = new HashMap<>();
        String nsrmc="";
        //个体双定
        String gtsdhBz="N";

        ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
        znsbmhzcqyjbxxmxreqvo.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVo = nsrxxApi.getNsrxxByDjxh(znsbmhzcqyjbxxmxreqvo);
        List<JbxxmxsjVO> jbxxmxsj = nsrxxVo.getData().getJbxxmxsj();
        nsrmc=jbxxmxsj.get(0).getNsrmc();
        if("1120".equals(jbxxmxsj.get(0).getKzztdjlxDm())){
            //企业算组长确认，取消个体双定户，默认是N
            //Integer dqdeCnt = yssfhjxyService.getDqdexx(djxh);
            //gtsdhBz= (dqdeCnt>0 ?"Y":"N");
        }
        result.put("nsrmc",nsrmc);
        result.put("gtsdhBz",gtsdhBz);
        return result;
    }

    @GetMapping("/v1/getYhyywdByYhyywdDm")
    public CommonResult<List<Map<String,Object>>> getYhyywdByYhyywdDm(@RequestParam(value = "YhyywdDm")String yhyywdDm){
        List<Map<String,Object>> result= new ArrayList<>();
        Map<String,Object> map = CacheUtils.getTableData("dm_gy_yhyywd", yhyywdDm);
        if(GyUtils.isNotNull(map)){
            result.add(map);
        }
        return CommonResult.success(result);
    }


    @GetMapping("/v1/getXzqhTreeData")
    public CommonResult<List<Map<String, Object>>> getXzqhTreeData(){
        return CommonResult.success(yssfhjxyService.getXzqhTreeData());
    }


    @PostMapping("/v1/listYhyywdByQzqhAndYhhb")
    public CommonResult<List<Map<String, Object>>> listYhyywdByQzqhAndYhhb(@RequestBody Map<String,Object> reqMap){
        final String xzqhszDm = GYCastUtils.cast2Str(reqMap.get("XzqhDm"));
        final String yhhbDm = GYCastUtils.cast2Str(reqMap.get("YhhbDm"));
        final List<Map<String, Object>> yhyywdList = CacheUtils.getTableData("dm_gy_yhyywd");
        final List<Map<String, Object>> xzqhList = CacheUtils.getTableData("dm_gy_xzqh");
        final Set<String> set = new HashSet<>();
        set.add(xzqhszDm);
        for (Map<String, Object> map : xzqhList) {
            if(xzqhszDm.equals(GYCastUtils.cast2Str(map.get("sjxzqhszDm")))){
                set.add(GYCastUtils.cast2Str(map.get("xzqhszDm")));
            }
        }
        final List<Map<String, Object>> yhyywdListNew = new ArrayList<>();
        Iterator<Map<String, Object>> iterator = yhyywdList.iterator();
        while (iterator.hasNext()){
            Map<String, Object> map = iterator.next();
            if(set.contains(GYCastUtils.cast2Str(map.get("xzqhszDm")))&&yhhbDm.equals(GYCastUtils.cast2Str(map.get("yhhbDm")))){
                final Map<String,Object> newMap = new HashMap<>();
                newMap.put("yHYYWD_DM",map.get("yhyywdDm"));
                newMap.put("yHYYWDMC",map.get("yhyywdmc"));
                yhyywdListNew.add(newMap);
            }
        }
        return CommonResult.success(yhyywdListNew);
    }


    /**
     * 获取行政区划下拉TREE
     * @param xzqhDm
     * @return
     */
    @PostMapping("/v1/getXzqhList")
    public CommonResult<List<XzqhVO>> getXzqhTreeList(@RequestParam(value = "XzqhDm")String xzqhDm) {
        List<XzqhVO> xzqhTreeList = yssfhjxyService.getXzqhTreeList(xzqhDm);
        return CommonResult.success(xzqhTreeList);
    }

    @PostMapping("/v1/listXzqh")
    public CommonResult<List<XzqhVO>> listXzqh(@RequestParam(value = "XzqhDm") String xzqhDm){
        List<XzqhVO> xzqhDmDTOList = yssfhjxyService.queryXzqhList(xzqhDm);
        return CommonResult.success(xzqhDmDTOList);
    }




    /*************************************以下是新电局的中转层代码****************************************/

    @GetMapping("/v1/listSfhjxy")
    public CommonResult<SfxyxxWithFzjgVO> listSfhjxy(@RequestParam(value = "djxh") String djxh,@RequestParam(value = "nsrsbh") String nsrsbh,@RequestParam(value = "xzqhszDm") String xzqhszDm) {
        final Map<String,Object> reqMap = new HashMap<>();
        reqMap.put("djxh",djxh);
        reqMap.put("nsrsbh",nsrsbh);
        reqMap.put("xzqhszDm",xzqhszDm);

        ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
        znsbmhzcqyjbxxmxreqvo.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVo = nsrxxApi.getNsrxxByDjxh(znsbmhzcqyjbxxmxreqvo);
        log.info(" listSfhjxy "+JsonUtils.toJson(nsrxxVo));
        //返回结果为空，返回错误信息 begin
        if(GyUtils.isNull(nsrxxVo.getData())){
            SfxyxxWithFzjgVO sfxyxxWithFzjgVO = new SfxyxxWithFzjgVO();
            sfxyxxWithFzjgVO.setReturnCode("99");
            sfxyxxWithFzjgVO.setReturnMsg("未获取到纳税人信息，请您确认信息是否有误。");
            return CommonResult.success(sfxyxxWithFzjgVO);
        }
        //返回结果为空，返回错误信息 end
        List<JbxxmxsjVO> jbxxmxsj = nsrxxVo.getData().getJbxxmxsj();
        new JbxxmxsjVO();
        JbxxmxsjVO jbxxmxsjVO;
        if (GyUtils.isNotNull(jbxxmxsj)){
            jbxxmxsjVO = jbxxmxsj.get(0);
        }else{
            return CommonResult.error(new SfxyxxWithFzjgVO());
        }

        String zgswskfjDm = GyUtils.isNull(jbxxmxsjVO.getZgswskfjDm()) ? jbxxmxsjVO.getZgswjDm() : jbxxmxsjVO.getZgswskfjDm();
        String nsrmc = jbxxmxsjVO.getNsrmc();
        String zgswjDm = jbxxmxsjVO.getZgswjDm();
        
        CommonResult<SfxyxxVo> result;
        String qydllx = "";
        if("09".equals(jbxxmxsjVO.getNsrztDm())||"10".equals(jbxxmxsjVO.getNsrztDm())){
            qydllx = "1";
        }else{
            qydllx = "0";
        }
        if ("1".equals(qydllx) || "2".equals(qydllx)) {
            Map<String, String> response = null;
            try {
                //当是报验户时，查询核心征管已验证+税智撑省外三方协议
                response = getYdjxhByMdbdjxh(djxh, zgswjDm,nsrsbh);
                if (GyUtils.isNull(response)){
                    return CommonResult.error(new SfxyxxWithFzjgVO());
                }
                //return CommonResult.fail(response.getResponse().getError().getCode(), response.getResponse().getError().getMessage());
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            String mbdDjxh = "";
            String mbdzgswjgDm = "";
            if (!GyUtils.isNull(response)) {
                mbdDjxh = response.get("mbdDjxh");
                mbdzgswjgDm = response.get("mbdzgswjgDm");
            }

            if (GyUtils.isNull(mbdDjxh)) {
                SfxyxxWithFzjgVO sfxyxxWithFzjgVO = new SfxyxxWithFzjgVO();
                sfxyxxWithFzjgVO.setReturnCode("-1");
                sfxyxxWithFzjgVO.setReturnMsg("未获取注册地主体信息，请核实后处理。");
                return CommonResult.success(sfxyxxWithFzjgVO);
            }
            result = listSfhjxyByh(mbdDjxh, mbdzgswjgDm,nsrsbh,xzqhszDm);
        } else {
            //普通用户，查询所有核心征管所有三方协议信息
            result = listSfhjxyHd(reqMap);
        }

        SfxyxxVo sfxyxxVo = result.getData();
        //57774：网页端、三方协议允许注销状态且有“跨省迁出”的登记户标识的纳税人办理此业务
        String nsrztDm=jbxxmxsjVO.getNsrztDm();
        if("07".equals(nsrztDm) && !sfxyxxVo.getIsdjhgl().equals("Y")){
            SfxyxxWithFzjgVO sfxyxxWithFzjgVO = new SfxyxxWithFzjgVO();
            sfxyxxWithFzjgVO.setReturnCode("-1");
            sfxyxxWithFzjgVO.setReturnMsg("您当前为注销状态，如需继续办理，请重新办理税务登记。详情请点击【征纳互动】图标沟通或拨打12366咨询。");
            return CommonResult.success(sfxyxxWithFzjgVO);
        }

        Map<String, String> swjgData;
        CommonResult<Map<String, String>> swjgMap = getHsjgdmByZgswskfjDm(zgswskfjDm);
        swjgData = swjgMap.getData();
        sfxyxxVo.setNsrsbh(nsrsbh);
        sfxyxxVo.setNsrmc(nsrmc);
        sfxyxxVo.setSkssswjgDm(swjgData.get("swjgDm"));
        sfxyxxVo.setZgswjMc(swjgData.get("zgswjMc"));
        sfxyxxVo.setQydllx(qydllx);
        if (GyUtils.isNotNull(sfxyxxVo.getSfhjxyxxList())) {
            List<SfhjxyxxDTO> sfhjxyxxList = new ArrayList<>();
            String finalNsrsbh = nsrsbh;
            String finalNsrmc = nsrmc;
            sfxyxxVo.getSfhjxyxxList().forEach(obj -> {
                if(GyUtils.isNull(obj.getNsrsbh())){
                    obj.setNsrsbh(finalNsrsbh);
                }
                obj.setNsrmc(finalNsrmc);
                sfhjxyxxList.add(obj);
            });
        }

        //是否为分支机构（2）
        boolean sffzjg = false;
        sffzjg = "2".equals(jbxxmxsjVO.getZfjglxDm());
        SfxyxxWithFzjgVO sfxyxxWithFzjgVO = new SfxyxxWithFzjgVO();
        sfxyxxWithFzjgVO.setSfhjxyxxList(sfxyxxVo.getSfhjxyxxList());
        sfxyxxWithFzjgVO.setNsrsbh(sfxyxxVo.getNsrsbh());
        sfxyxxWithFzjgVO.setNsrmc(sfxyxxVo.getNsrmc());
        sfxyxxWithFzjgVO.setSkssswjgDm(sfxyxxVo.getSkssswjgDm());
        sfxyxxWithFzjgVO.setZgswjMc(sfxyxxVo.getZgswjMc());
        sfxyxxWithFzjgVO.setQydllx(sfxyxxVo.getQydllx());
        sfxyxxWithFzjgVO.setReturnCode(sfxyxxVo.getReturnCode());
        sfxyxxWithFzjgVO.setReturnMsg(sfxyxxVo.getReturnMsg());
        sfxyxxWithFzjgVO.setSffzjg(sffzjg);
        //用户类型：0 税务人 1 自然人 2 企业身份
        String userType = "2";
        sfxyxxWithFzjgVO.setUserType(userType);
        return CommonResult.success(sfxyxxWithFzjgVO);
    }

    @GetMapping("/v1/getPkbzList")
    public CommonResult<Map<String, List<Map<String, Object>>>> getPkbzList(@RequestParam(value = "sfxyuuid", required = false) String sfxyuuid, @RequestParam(value = "djxh") String djxh,@RequestParam(value = "nsrsbh") String nsrsbh,@RequestParam(value = "xzqhszDm") String xzqhszDm) {
        ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
        znsbmhzcqyjbxxmxreqvo.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVo = nsrxxApi.getNsrxxByDjxh(znsbmhzcqyjbxxmxreqvo);
        List<JbxxmxsjVO> jbxxmxsj = nsrxxVo.getData().getJbxxmxsj();
        JbxxmxsjVO jbxxmxsjVO;
        if (GyUtils.isNotNull(jbxxmxsj)){
            jbxxmxsjVO = jbxxmxsj.get(0);
        }else{
            return CommonResult.error(new HashMap<>());
        }
        String zgswskfjDm = GyUtils.isNull(jbxxmxsjVO.getZgswskfjDm()) ? jbxxmxsjVO.getZgswjDm() : jbxxmxsjVO.getZgswskfjDm();
        CommonResult<Map<String, String>> res = getHsjgdmByZgswskfjDm(zgswskfjDm);
        String swjgDm = res.getData().get("swjgDm");
        return getPkbzListHd(djxh, swjgDm, sfxyuuid);
    }

    @GetMapping("/v1/createInit")
    public CommonResult<Map<String, Object>> createInit(@RequestParam(value = "djxh") String djxh,@RequestParam(value = "nsrsbh") String nsrsbh,@RequestParam(value = "xzqhszDm") String xzqhszDm) {
        ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
        znsbmhzcqyjbxxmxreqvo.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVo = nsrxxApi.getNsrxxByDjxh(znsbmhzcqyjbxxmxreqvo);
        List<JbxxmxsjVO> jbxxmxsj = nsrxxVo.getData().getJbxxmxsj();
        JbxxmxsjVO jbxxmxsjVO;
        if (GyUtils.isNotNull(jbxxmxsj)){
            jbxxmxsjVO = jbxxmxsj.get(0);
        }else{
            return CommonResult.error(new HashMap<>());
        }

        String nsrmc;
        String dqbm = "";
        String qydllx = ""; //报验户（1、2）
        //是否个体双定户
        String gtsdhBz = "";
        
        //dqbm = sessionUtils.getDqbm();
        String zgswskfjDm = GyUtils.isNull(jbxxmxsjVO.getZgswskfjDm()) ? jbxxmxsjVO.getZgswjDm() : jbxxmxsjVO.getZgswskfjDm();

        if("09".equals(jbxxmxsjVO.getNsrztDm())||"10".equals(jbxxmxsjVO.getNsrztDm())){
            //新电局的报验户（1、2），用纳税人状态代码赋值
            qydllx = "1";
        }
        Map<String, String> response = getNsrmcAndDqdexx(djxh);
        if (GyUtils.isNull(response)){
            return CommonResult.error(new HashMap<>());
        }else{
            nsrmc = response.get("nsrmc");
            gtsdhBz = response.get("gtsdhBz");
        }

        Map<String, String> swjgData;
        CommonResult<Map<String, String>> swjgMap = getHsjgdmByZgswskfjDm(zgswskfjDm);
        swjgData = swjgMap.getData();

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("djxh", djxh);
        resultMap.put("nsrsbh", nsrsbh);
        resultMap.put("nsrmc", nsrmc);
        resultMap.put("dqbm", dqbm);
        resultMap.put("qydllx", qydllx);
        //个体双定户标志
        resultMap.put("gtsdhBz", gtsdhBz);
        resultMap.putAll(swjgData);
        return CommonResult.success(resultMap);
    }

    

    @PostMapping("/v1/saveSfhjxy")
    public CommonResult<Map<String, Object>> saveSfhjxy(@RequestBody ZSSfxyVO zSsfxyVO, @RequestParam(value = "Djxh") String djxh,@RequestParam(value = "Nsrsbh") String nsrsbh,@RequestParam(value = "XzqhszDm") String xzqhszDm) {
        log.info("/v1/saveSfhjxy 入参：{}", JsonUtils.toJson(zSsfxyVO));
        log.info("/v1/saveSfhjxy 入参：djxh: ", djxh+" nsrsbh: ", nsrsbh+" xzqhszDm: ", xzqhszDm);
        final Map<String,Object> sjjhMap = new HashMap<>();
        sjjhMap.put("djxh",djxh);
        sjjhMap.put("nsrsbh",nsrsbh);
        sjjhMap.put("xzqhszDm",xzqhszDm);

        ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
        znsbmhzcqyjbxxmxreqvo.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVo = nsrxxApi.getNsrxxByDjxh(znsbmhzcqyjbxxmxreqvo);
        List<JbxxmxsjVO> jbxxmxsj = nsrxxVo.getData().getJbxxmxsj();
        JbxxmxsjVO jbxxmxsjVO;
        if (GyUtils.isNotNull(jbxxmxsj)){
            jbxxmxsjVO = jbxxmxsj.get(0);
        }else{
            return CommonResult.error(new HashMap<>());
        }

        String qydllx = "";
        if("09".equals(jbxxmxsjVO.getNsrztDm())||"10".equals(jbxxmxsjVO.getNsrztDm())){
            //新电局的报验户（1、2），用纳税人状态代码赋值
            qydllx = "1";
        }

        zSsfxyVO.setDjxh(djxh);
        if (StringUtils.isBlank(zSsfxyVO.getSfzjhm())) {
            zSsfxyVO.setSfzjhm(zSsfxyVO.getSfzjhm());
            zSsfxyVO.setSfzjlxDm(zSsfxyVO.getSfzjlxDm());
        }

        zSsfxyVO.setNsrsbh(nsrsbh);
        String zgswskfjDm = GyUtils.isNull(jbxxmxsjVO.getZgswskfjDm()) ? jbxxmxsjVO.getZgswjDm() : jbxxmxsjVO.getZgswskfjDm();
        if (StringUtils.isNotEmpty(zgswskfjDm)) {
            CommonResult<Map<String, String>> res = getHsjgdmByZgswskfjDm(zgswskfjDm);
            zSsfxyVO.setSkssswjgDm(res.getData().get("swjgDm"));
        }
        if (StringUtils.isBlank(qydllx)) {
            qydllx = "1";
        }
        zSsfxyVO.setQydllx(qydllx);

        CommonResult<Map<String, Object>> response = saveSfhjxy(zSsfxyVO,sjjhMap);

        Map<String, Object> map = null;
        if (!GyUtils.isNull(response))
            map = response.getData();
        String zgswjMc = jbxxmxsjVO.getSwjgmc();
        map.put("zgswjMc", zgswjMc);
        return CommonResult.success(map);
    }



    @GetMapping("/v1/getQsyhhhKhyhhh")
    public CommonResult<List<Map<String, Object>>> getQsyhhhKhyhhh(@RequestParam("yhyywdDm") String yhyywdDm,@RequestParam(value = "djxh") String djxh) {
        ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
        znsbmhzcqyjbxxmxreqvo.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVo = nsrxxApi.getNsrxxByDjxh(znsbmhzcqyjbxxmxreqvo);
        List<JbxxmxsjVO> jbxxmxsj = nsrxxVo.getData().getJbxxmxsj();
        JbxxmxsjVO jbxxmxsjVO;
        if (GyUtils.isNotNull(jbxxmxsj)){
            jbxxmxsjVO = jbxxmxsj.get(0);
        }else{
            return CommonResult.error(-1,"未查询到纳税人信息");
        }

        List<Map<String, Object>> response= yssfhjxyService.getQsyhhhKhyhhh(yhyywdDm);

        log.info("=======================getQsyhhhKhyhhh开始==========================");
        String zgswjDm= jbxxmxsjVO.getZgswjDm();
        log.info("=================zhaohy的zgswjDm值==================："+zgswjDm);
        //57665:跨区报验户、跨区税源户、分支机构签订三方协议的时候，用银行营业网点代码加税务机关代码
        List<Map<String, Object>> yhyywdDmList = null;
        if (!GyUtils.isNull(response)) {
            log.info("=================zhaohy的list值==================："+ JSON.toJSONString(response));
            List<Map<String, Object>> swjgTree = yssfhjxyService.getBjAndSjSwjgList(zgswjDm);
                    //cssDmcsService.getTreeCacheData("DM_GY_SWJG_TREE", zgswjDm, "02", true);
            log.info("=================zhaohy的swjgTree值==================："+JSON.toJSONString(swjgTree));
            if(!GyUtils.isNull(swjgTree)){
                List<String> swjgList = swjgTree.stream().map(obj -> obj.get("swjgDm").toString()).collect(Collectors.toList());
                Collections.sort(swjgList, Collections.reverseOrder());
                log.info("=================zhaohy的swjgList值==================："+JSON.toJSONString(swjgList));
                for (String str:swjgList){
                    yhyywdDmList = response.stream().filter(swjgMap -> str.equals(swjgMap.get("swjgDm"))).collect(Collectors.toList());
                    if(!GyUtils.isNull(yhyywdDmList)) break;
                }
            }
        }

        if(!GyUtils.isNull(yhyywdDmList)){
            log.info("=======================getQsyhhhKhyhhh结束1=========================="+JSON.toJSONString(yhyywdDmList));
            return CommonResult.success(yhyywdDmList);
        }else{
            log.info("=======================getQsyhhhKhyhhh结束2==========================");
            return CommonResult.success(response);
        }
    }

    @PostMapping("/export")
    public void exportTz(@RequestBody ExportTzVO exportParam, HttpServletResponse response) {
        this.exportTzExcelByTemplete(exportParam, response);
    }


    private void exportTzExcelByTemplete(ExportTzVO exportParam, HttpServletResponse response) {
        //获取参数
        final String fileName = exportParam.getFileName();
        final Map<String, Object> cxParam = exportParam.getCxParam();
        final List<Map<String,Object>> reqVOList = (List<Map<String,Object>>) cxParam.get("reqVOList");
        if (GyUtils.isNull(reqVOList)) {
            return;
        }
        //查询需要导出的数据
        Map<String, Object> exportFieldData = new HashMap<>();
        List<SfxyExportDTO> exportTableData = new ArrayList<>();

        for(Map<String,Object> map : reqVOList){
            AtomicInteger xh = new AtomicInteger(1);
            CommonResult<SfxyxxWithFzjgVO> result = this.listSfhjxy((String)map.get("djxh"),(String)map.get("nsrsbh"),(String)map.get("xzqhszDm"));
            SfxyxxWithFzjgVO sfxyxxWithFzjgVO = result.getData();
            if(GyUtils.isNotNull(sfxyxxWithFzjgVO) && GyUtils.isNotNull(sfxyxxWithFzjgVO.getSfhjxyxxList())){
                List<SfhjxyxxDTO> sfhjxyxxList = sfxyxxWithFzjgVO.getSfhjxyxxList();
                sfhjxyxxList.forEach(sfhjxyxxDTO -> {
                    final SfxyExportDTO exportDTO = com.css.znsb.framework.common.util.object.BeanUtils.toBean(sfhjxyxxDTO,SfxyExportDTO.class);
                    exportDTO.setXh(String.valueOf(xh));
                    exportDTO.setNsrsbh(sfxyxxWithFzjgVO.getNsrsbh());
                    exportDTO.setNsrmc(sfxyxxWithFzjgVO.getNsrmc());
                    if("01".equals(sfhjxyxxDTO.getSfxyztDm())){
                        exportDTO.setSfxyztmc("未验证");
                    } else if ("02".equals(sfhjxyxxDTO.getSfxyztDm())) {
                        exportDTO.setSfxyztmc("验证通过");
                    } else if ("03".equals(sfhjxyxxDTO.getSfxyztDm())) {
                        exportDTO.setSfxyztmc("验证失败");
                    } else if ("04".equals(sfhjxyxxDTO.getSfxyztDm())) {
                        exportDTO.setSfxyztmc("终止协议");
                    }
                    exportTableData.add(exportDTO);
                    xh.set(xh.get() + 1);
                });
            }
        }

        //模板
        final String templatePath = File.separator + "templates" + File.separator + "sfxy"
                + File.separator + "sfxy.xlsx";

        //设置要下载的文件的名称
        ClassPathResource classPathResource = new ClassPathResource(templatePath);
        InputStream inputStream = classPathResource.getStream();
        try {
            ServletOutputStream out = response.getOutputStream();
            //设置文件类型
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            //设置编码格式
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
            //创建excel
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inputStream).build();
            // 创建sheet
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(exportTableData, fillConfig, writeSheet);
            excelWriter.fill(exportFieldData, writeSheet);
            //填充完成
            excelWriter.finish();
            out.flush();
        } catch (IOException e) {
            log.error("",e);
        }
    }
}
