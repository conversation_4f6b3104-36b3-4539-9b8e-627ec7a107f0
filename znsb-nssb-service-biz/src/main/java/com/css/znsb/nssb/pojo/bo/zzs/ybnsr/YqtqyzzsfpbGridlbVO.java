package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 油气田企业增值税分配表Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "yqtqyzzsfpbGridlbVO", propOrder = { "djxh", "ewbhxh", "xzqhszDm", "skrkgk", "ysfpbl", "yycl", "yybl", "trqcl", "trqbl", "zhfpbl", "fpse" })
@Getter
@Setter
public class YqtqyzzsfpbGridlbVO {
    /**
     * 登记序号
     */
    protected String djxh;

    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 行政区划数字代码
     */
    protected String xzqhszDm;

    /**
     * 税款入库国库
     */
    protected String skrkgk;

    /**
     * 预算分配比例
     */
    protected BigDecimal ysfpbl;

    /**
     * 原油产量
     */
    protected BigDecimal yycl;

    /**
     * 原油比例
     */
    protected BigDecimal yybl;

    /**
     * 天然气产量
     */
    protected BigDecimal trqcl;

    /**
     * 天然气比例
     */
    protected BigDecimal trqbl;

    /**
     * 综合分配比例
     */
    protected BigDecimal zhfpbl;

    /**
     * 分配所得税额
     */
    protected BigDecimal fpse;
}