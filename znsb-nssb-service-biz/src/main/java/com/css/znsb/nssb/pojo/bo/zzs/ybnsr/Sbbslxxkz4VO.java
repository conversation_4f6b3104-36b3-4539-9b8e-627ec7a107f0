package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 * @Description: 申报表受理信息VO扩展4,增加申报日期
 * @date 2023年11月30日 9:52
 * @company 税友软件集团有限公司
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbslxxkz4VO", propOrder = { "sbrq1" })
@Getter
@Setter
public class Sbbslxxkz4VO extends Sbbslxxkz1VO{

    @XmlElement(nillable = true, required = true)
    protected String sbrq1;

}
