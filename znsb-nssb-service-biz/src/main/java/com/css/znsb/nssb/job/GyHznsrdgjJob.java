package com.css.znsb.nssb.job;

import cn.hutool.core.date.DateUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("GyHznsrdgjJob")
public class GyHznsrdgjJob {

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private SjjhService sjjhService;

    @XxlJob("hznsrdgjJob")
    public void execute() {
        log.info("汇总纳税认定归集任务开始");
        String nsrsbhJsonArray = XxlJobHelper.getJobParam();
        List<String> nsrsbhList = new ArrayList<>();
        if (GyUtils.isNotNull(nsrsbhJsonArray)) {
            nsrsbhList = JsonUtils.toList(nsrsbhJsonArray, String.class);
        }
        List<Map<String, String>> nsrxxList = getNsrxxList(nsrsbhList);
        log.info("获取到纳税人信息：{}", nsrxxList);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        for (Map<String, String> nsrxxMap : nsrxxList) {
            SjjhDTO sjjhDTO = new SjjhDTO();
            sjjhDTO.setYwbm("GY00000006");
            sjjhDTO.setSjjhlxDm("CXHZNSBAXX");
            sjjhDTO.setYwuuid(GyUtils.getUuid());
            sjjhDTO.setNsrsbh(nsrxxMap.get("nsrsbh"));
            sjjhDTO.setXzqhszDm(nsrxxMap.get("xzqhszDm"));
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("djxh", nsrxxMap.get("djxh"));
            paramMap.put("skssqq", DateUtil.format(DateUtil.beginOfMonth(calendar.getTime()), "yyyy-MM-dd"));
            paramMap.put("skssqz", DateUtil.format(DateUtil.endOfMonth(calendar.getTime()), "yyyy-MM-dd"));
            sjjhDTO.setBwnr(JsonUtils.toJson(paramMap));
            sjjhService.saveSjjhJob(sjjhDTO);
        }
    }

    private List<Map<String, String>> getNsrxxList(List<String> nsrsbhList) {
        List<Map<String, String>> nsrxxList = new ArrayList<>();
        if (GyUtils.isNotNull(nsrsbhList)) {
            for (String nsrsbh : nsrsbhList) {
                ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
                reqVO.setNsrsbh(nsrsbh);
                // 调用接口获取纳税人信息
                CommonResult<ZnsbMhzcQyjbxxmxResVO> result = nsrxxApi.getNsrxxByNsrsbh(reqVO);
                if (result.isSuccess() && GyUtils.isNotNull(result.getData())
                        && GyUtils.isNotNull(result.getData().getJbxxmxsj())) {
                    List<JbxxmxsjVO> jbxxmxList = result.getData().getJbxxmxsj();
                    for (JbxxmxsjVO jbxxmxsj : jbxxmxList) {
                        Map<String, String> nsrxxMap = new HashMap<>();
                        String djxh = jbxxmxsj.getDjxh();
                        nsrxxMap.put("nsrsbh", jbxxmxsj.getNsrsbh());
                        nsrxxMap.put("djxh", djxh);

                        CommonResult<CompanyBasicInfoDTO> basicInfoResult =
                                companyApi.basicInfo(djxh, jbxxmxsj.getNsrsbh());
                        if (basicInfoResult.isSuccess() && GyUtils.isNotNull(basicInfoResult.getData())) {
                            nsrxxMap.put("xzqhszDm", basicInfoResult.getData().getXzqhszDm());
                            nsrxxList.add(nsrxxMap);
                        }
                    }
                }
            }
        } else {
            CommonResult<List<String>> djxhsResult = companyApi.getAllDjxh();
            if (djxhsResult.isSuccess() && GyUtils.isNotNull(djxhsResult.getData())) {
                for (String djxh : djxhsResult.getData()) {
                    CommonResult<CompanyBasicInfoDTO> basicInfoResult = companyApi.basicInfo(djxh, "");
                    if (basicInfoResult.isSuccess() && GyUtils.isNotNull(basicInfoResult.getData())) {
                        Map<String, String> nsrxxMap = new HashMap<>();
                        CompanyBasicInfoDTO basicInfo = basicInfoResult.getData();
                        nsrxxMap.put("nsrsbh", basicInfo.getNsrsbh());
                        nsrxxMap.put("djxh", djxh);
                        nsrxxMap.put("xzqhszDm", basicInfo.getXzqhszDm());
                        nsrxxList.add(nsrxxMap);
                    }
                }
            }
        }
        return nsrxxList;
    }
}
