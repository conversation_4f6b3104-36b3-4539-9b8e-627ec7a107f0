package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《固定资产进项税额抵扣情况表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_gdzcjxsedkqkbbw", propOrder = { "zzsybnsrsbGdzcjxsedkqkb" })
@Getter
@Setter
public class ZzsybnsrsbGdzcjxsedkqkbbw extends TaxDoc {
    /**
     * 《固定资产进项税额抵扣情况表》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_gdzcjxsedkqkb", required = true)
    @JSONField(name = "zzsybnsrsb_gdzcjxsedkqkb")
    protected ZzsybnsrsbGdzcjxsedkqkb zzsybnsrsbGdzcjxsedkqkb;
}