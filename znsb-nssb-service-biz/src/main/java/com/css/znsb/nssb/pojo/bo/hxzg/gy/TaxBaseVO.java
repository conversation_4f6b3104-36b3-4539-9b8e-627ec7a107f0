package com.css.znsb.nssb.pojo.bo.hxzg.gy;

import java.io.Serializable;

/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.zh
 * @file TaxBaseVO.java 创建时间:2014-7-5上午12:30:19
 * @title 税务基本VO（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class TaxBaseVO implements Serializable {

    private static final long serialVersionUID = -8720762420527276032L;

}