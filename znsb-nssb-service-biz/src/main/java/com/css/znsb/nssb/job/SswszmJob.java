package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.sbrw.SbrwgjService;
import com.css.znsb.nssb.service.sswszmLq.SswszmLqService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 申报任务归集定时任务
 */
@Slf4j
@Component
public class SswszmJob {
    
    @Resource
    private SswszmLqService swszmLqService;

    /**
     * 归集完税证明信息
     */
    @XxlJob("wszmGjByYear")
    public void wszmGjByYear() {
        final String jobParam = XxlJobHelper.getJobParam();
        swszmLqService.syncWssJnskxxAndSave(jobParam);
    }

    /**
     * 每月归集完税证明信息
     */
    @XxlJob("wszmGjByMonth")
    public void wszmGjByMonth() {
        swszmLqService.monthlyCollectWszmInfo();
    }

    /**
     * 划税凭证发送给sap
     */
    @XxlJob("hspzToSap")
    public void hspzToSap() {
        swszmLqService.hspzToSap();
    }
}
