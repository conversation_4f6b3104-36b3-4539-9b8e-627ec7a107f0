
package com.css.znsb.nssb.pojo.vo.cwbb.lqbs.sb099;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 申报信息
 *
 * <p>Java class for ybqyzcfzbzbGridlbVO complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="ybqyzcfzbzbGridlbVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ewbhxh" type="{http://www.chinatax.gov.cn/dataspec/}ewbhxh" minOccurs="0"/>
 *         &lt;element name="zcxmmc" type="{http://www.chinatax.gov.cn/dataspec/}zcxmmc" minOccurs="0"/>
 *         &lt;element name="qmyeZc" type="{http://www.chinatax.gov.cn/dataspec/}qmyeZc" minOccurs="0"/>
 *         &lt;element name="ncyeZc" type="{http://www.chinatax.gov.cn/dataspec/}ncyeZc" minOccurs="0"/>
 *         &lt;element name="qyxmmc" type="{http://www.chinatax.gov.cn/dataspec/}qyxmmc" minOccurs="0"/>
 *         &lt;element name="qmyeQy" type="{http://www.chinatax.gov.cn/dataspec/}qmyeQy" minOccurs="0"/>
 *         &lt;element name="ncyeQy" type="{http://www.chinatax.gov.cn/dataspec/}ncyeQy" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ybqyzcfzbzbGridlbVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
        "ewbhxh",
        "zcxmmc",
        "qmyeZc",
        "ncyeZc",
        "qyxmmc",
        "qmyeQy",
        "ncyeQy"
})
public class YbqyzcfzbzbGridlbVO
        implements Serializable {

    private final static long serialVersionUID = 1468566763656402744L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Long ewbhxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zcxmmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qmyeZc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ncyeZc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qyxmmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qmyeQy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ncyeQy;

    /**
     * Gets the value of the ewbhxh property.
     *
     * @return possible object is
     * {@link Long }
     */
    public Long getEwbhxh() {
        return ewbhxh;
    }

    /**
     * Sets the value of the ewbhxh property.
     *
     * @param value allowed object is
     *              {@link Long }
     */
    public void setEwbhxh(Long value) {
        this.ewbhxh = value;
    }

    /**
     * Gets the value of the zcxmmc property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getZcxmmc() {
        return zcxmmc;
    }

    /**
     * Sets the value of the zcxmmc property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setZcxmmc(String value) {
        this.zcxmmc = value;
    }

    /**
     * Gets the value of the qmyeZc property.
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getQmyeZc() {
        return qmyeZc;
    }

    /**
     * Sets the value of the qmyeZc property.
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setQmyeZc(Double value) {
        this.qmyeZc = value;
    }

    /**
     * Gets the value of the ncyeZc property.
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getNcyeZc() {
        return ncyeZc;
    }

    /**
     * Sets the value of the ncyeZc property.
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setNcyeZc(Double value) {
        this.ncyeZc = value;
    }

    /**
     * Gets the value of the qyxmmc property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getQyxmmc() {
        return qyxmmc;
    }

    /**
     * Sets the value of the qyxmmc property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setQyxmmc(String value) {
        this.qyxmmc = value;
    }

    /**
     * Gets the value of the qmyeQy property.
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getQmyeQy() {
        return qmyeQy;
    }

    /**
     * Sets the value of the qmyeQy property.
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setQmyeQy(Double value) {
        this.qmyeQy = value;
    }

    /**
     * Gets the value of the ncyeQy property.
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getNcyeQy() {
        return ncyeQy;
    }

    /**
     * Sets the value of the ncyeQy property.
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setNcyeQy(Double value) {
        this.ncyeQy = value;
    }

}
