package com.css.znsb.nssb.pojo.bo.hxzg.sb442;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SBSdsJwmbkstzVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBSdsJwmbkstzVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="dqnx0" type="{http://www.chinatax.gov.cn/dataspec/}dqnx0" minOccurs="0"/>
 *         &lt;element name="dqnx1" type="{http://www.chinatax.gov.cn/dataspec/}dqnx1" minOccurs="0"/>
 *         &lt;element name="dqnx2" type="{http://www.chinatax.gov.cn/dataspec/}dqnx2" minOccurs="0"/>
 *         &lt;element name="dqnx3" type="{http://www.chinatax.gov.cn/dataspec/}dqnx3" minOccurs="0"/>
 *         &lt;element name="dqnx4" type="{http://www.chinatax.gov.cn/dataspec/}dqnx4" minOccurs="0"/>
 *         &lt;element name="dqnx5" type="{http://www.chinatax.gov.cn/dataspec/}dqnx5" minOccurs="0"/>
 *         &lt;element name="dqnx6" type="{http://www.chinatax.gov.cn/dataspec/}dqnx6" minOccurs="0"/>
 *         &lt;element name="dqnx7" type="{http://www.chinatax.gov.cn/dataspec/}dqnx7" minOccurs="0"/>
 *         &lt;element name="dqnx8" type="{http://www.chinatax.gov.cn/dataspec/}dqnx8" minOccurs="0"/>
 *         &lt;element name="dqnx9" type="{http://www.chinatax.gov.cn/dataspec/}dqnx9" minOccurs="0"/>
 *         &lt;element name="gjdq" type="{http://www.chinatax.gov.cn/dataspec/}gjdq"/>
 *         &lt;element name="jzyhndmbdfsjkse" type="{http://www.chinatax.gov.cn/dataspec/}jzyhndmbdfsjkse" minOccurs="0"/>
 *         &lt;element name="jzyhndmbdsjkse" type="{http://www.chinatax.gov.cn/dataspec/}jzyhndmbdsjkse" minOccurs="0"/>
 *         &lt;element name="ksje0" type="{http://www.chinatax.gov.cn/dataspec/}ksje0" minOccurs="0"/>
 *         &lt;element name="ksje1" type="{http://www.chinatax.gov.cn/dataspec/}ksje1" minOccurs="0"/>
 *         &lt;element name="ksje2" type="{http://www.chinatax.gov.cn/dataspec/}ksje2" minOccurs="0"/>
 *         &lt;element name="ksje3" type="{http://www.chinatax.gov.cn/dataspec/}ksje3" minOccurs="0"/>
 *         &lt;element name="ksje4" type="{http://www.chinatax.gov.cn/dataspec/}ksje4" minOccurs="0"/>
 *         &lt;element name="ksje5" type="{http://www.chinatax.gov.cn/dataspec/}ksje5" minOccurs="0"/>
 *         &lt;element name="ksje6" type="{http://www.chinatax.gov.cn/dataspec/}ksje6" minOccurs="0"/>
 *         &lt;element name="ksje7" type="{http://www.chinatax.gov.cn/dataspec/}ksje7" minOccurs="0"/>
 *         &lt;element name="ksje8" type="{http://www.chinatax.gov.cn/dataspec/}ksje8" minOccurs="0"/>
 *         &lt;element name="ksje9" type="{http://www.chinatax.gov.cn/dataspec/}ksje9" minOccurs="0"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="ssnd" type="{http://www.chinatax.gov.cn/dataspec/}ssnd"/>
 *         &lt;element name="uuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq" minOccurs="0"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz" minOccurs="0"/>
 *         &lt;element name="zfbz1" type="{http://www.chinatax.gov.cn/dataspec/}zfbz1" minOccurs="0"/>
 *         &lt;element name="zfrq1" type="{http://www.chinatax.gov.cn/dataspec/}zfrq1" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBSdsJwmbkstzVO", propOrder = {
    "djxh",
    "dqnx0",
    "dqnx1",
    "dqnx2",
    "dqnx3",
    "dqnx4",
    "dqnx5",
    "dqnx6",
    "dqnx7",
    "dqnx8",
    "dqnx9",
    "dqnx10",
    "gjdq",
    "jzyhndmbdfsjkse",
    "jzyhndmbdsjkse",
    "ksje0",
    "ksje1",
    "ksje2",
    "ksje3",
    "ksje4",
    "ksje5",
    "ksje6",
    "ksje7",
    "ksje8",
    "ksje9",
    "ksje10",
    "mbqksje0",
    "mbqksje1",
    "mbqksje2",
    "mbqksje3",
    "mbqksje4",
    "mbqksje5",
    "mbqksje6",
    "mbqksje7",
    "mbqksje8",
    "mbqksje9",
    "mbqksje10",
    "lrrDm",
    "lrrq",
    "sjgsdq",
    "ssnd",
    "uuid",
    "xgrDm",
    "xgrq",
    "zfbz1",
    "zfrq1",
    "mbkstzuuid"
})
public class SBSdsJwmbkstzVO implements Serializable {

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx0;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx7;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx8;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx9;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqnx10;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String gjdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jzyhndmbdfsjkse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jzyhndmbdsjkse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje0;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje7;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje8;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje9;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ksje10;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje0;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje4;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje5;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje7;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje8;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje9;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double mbqksje10;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String uuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfbz1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfrq1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String mbkstzuuid;
    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the dqnx0 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx0() {
        return dqnx0;
    }

    /**
     * Sets the value of the dqnx0 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx0(String value) {
        this.dqnx0 = value;
    }

    /**
     * Gets the value of the dqnx1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx1() {
        return dqnx1;
    }

    /**
     * Sets the value of the dqnx1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx1(String value) {
        this.dqnx1 = value;
    }

    /**
     * Gets the value of the dqnx2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx2() {
        return dqnx2;
    }

    /**
     * Sets the value of the dqnx2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx2(String value) {
        this.dqnx2 = value;
    }

    /**
     * Gets the value of the dqnx3 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx3() {
        return dqnx3;
    }

    /**
     * Sets the value of the dqnx3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx3(String value) {
        this.dqnx3 = value;
    }

    /**
     * Gets the value of the dqnx4 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx4() {
        return dqnx4;
    }

    /**
     * Sets the value of the dqnx4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx4(String value) {
        this.dqnx4 = value;
    }

    /**
     * Gets the value of the dqnx5 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx5() {
        return dqnx5;
    }

    /**
     * Sets the value of the dqnx5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx5(String value) {
        this.dqnx5 = value;
    }

    /**
     * Gets the value of the dqnx6 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx6() {
        return dqnx6;
    }

    /**
     * Sets the value of the dqnx6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx6(String value) {
        this.dqnx6 = value;
    }

    /**
     * Gets the value of the dqnx7 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx7() {
        return dqnx7;
    }

    /**
     * Sets the value of the dqnx7 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx7(String value) {
        this.dqnx7 = value;
    }

    /**
     * Gets the value of the dqnx8 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx8() {
        return dqnx8;
    }

    /**
     * Sets the value of the dqnx8 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx8(String value) {
        this.dqnx8 = value;
    }

    /**
     * Gets the value of the dqnx9 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqnx9() {
        return dqnx9;
    }

    /**
     * Sets the value of the dqnx9 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqnx9(String value) {
        this.dqnx9 = value;
    }

    /**
     * Gets the value of the gjdq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGjdq() {
        return gjdq;
    }

    /**
     * Sets the value of the gjdq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGjdq(String value) {
        this.gjdq = value;
    }

    /**
     * Gets the value of the jzyhndmbdfsjkse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJzyhndmbdfsjkse() {
        return jzyhndmbdfsjkse;
    }

    /**
     * Sets the value of the jzyhndmbdfsjkse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJzyhndmbdfsjkse(Double value) {
        this.jzyhndmbdfsjkse = value;
    }

    /**
     * Gets the value of the jzyhndmbdsjkse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJzyhndmbdsjkse() {
        return jzyhndmbdsjkse;
    }

    /**
     * Sets the value of the jzyhndmbdsjkse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJzyhndmbdsjkse(Double value) {
        this.jzyhndmbdsjkse = value;
    }

    /**
     * Gets the value of the ksje0 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje0() {
        return ksje0;
    }

    /**
     * Sets the value of the ksje0 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje0(Double value) {
        this.ksje0 = value;
    }

    /**
     * Gets the value of the ksje1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje1() {
        return ksje1;
    }

    /**
     * Sets the value of the ksje1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje1(Double value) {
        this.ksje1 = value;
    }

    /**
     * Gets the value of the ksje2 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje2() {
        return ksje2;
    }

    /**
     * Sets the value of the ksje2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje2(Double value) {
        this.ksje2 = value;
    }

    /**
     * Gets the value of the ksje3 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje3() {
        return ksje3;
    }

    /**
     * Sets the value of the ksje3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje3(Double value) {
        this.ksje3 = value;
    }

    /**
     * Gets the value of the ksje4 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje4() {
        return ksje4;
    }

    /**
     * Sets the value of the ksje4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje4(Double value) {
        this.ksje4 = value;
    }

    /**
     * Gets the value of the ksje5 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje5() {
        return ksje5;
    }

    /**
     * Sets the value of the ksje5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje5(Double value) {
        this.ksje5 = value;
    }

    /**
     * Gets the value of the ksje6 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje6() {
        return ksje6;
    }

    /**
     * Sets the value of the ksje6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje6(Double value) {
        this.ksje6 = value;
    }

    /**
     * Gets the value of the ksje7 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje7() {
        return ksje7;
    }

    /**
     * Sets the value of the ksje7 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje7(Double value) {
        this.ksje7 = value;
    }

    /**
     * Gets the value of the ksje8 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje8() {
        return ksje8;
    }

    /**
     * Sets the value of the ksje8 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje8(Double value) {
        this.ksje8 = value;
    }

    /**
     * Gets the value of the ksje9 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getKsje9() {
        return ksje9;
    }

    /**
     * Sets the value of the ksje9 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setKsje9(Double value) {
        this.ksje9 = value;
    }

    /**
     * Gets the value of the lrrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * Sets the value of the lrrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * Gets the value of the lrrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * Sets the value of the lrrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * Gets the value of the sjgsdq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * Sets the value of the sjgsdq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * Gets the value of the ssnd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsnd() {
        return ssnd;
    }

    /**
     * Sets the value of the ssnd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsnd(String value) {
        this.ssnd = value;
    }

    /**
     * Gets the value of the uuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * Sets the value of the uuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUuid(String value) {
        this.uuid = value;
    }

    /**
     * Gets the value of the xgrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * Sets the value of the xgrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * Gets the value of the xgrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * Sets the value of the xgrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * Gets the value of the zfbz1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfbz1() {
        return zfbz1;
    }

    /**
     * Sets the value of the zfbz1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfbz1(String value) {
        this.zfbz1 = value;
    }

    /**
     * Gets the value of the zfrq1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfrq1() {
        return zfrq1;
    }

    /**
     * Sets the value of the zfrq1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfrq1(String value) {
        this.zfrq1 = value;
    }

    /**
     *创建时间:2018-12-11下午01:38:17
     *get方法
     * @return the mbkstzuuid
     */
    public String getMbkstzuuid() {
        return mbkstzuuid;
    }

    /**
     * 创建时间:2018-12-11下午01:38:17
     * set方法
     * @param mbkstzuuid the mbkstzuuid to set
     */
    public void setMbkstzuuid(String mbkstzuuid) {
        this.mbkstzuuid = mbkstzuuid;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje0
     */
    public Double getMbqksje0() {
        return mbqksje0;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje0 the mbqksje0 to set
     */
    public void setMbqksje0(Double mbqksje0) {
        this.mbqksje0 = mbqksje0;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje1
     */
    public Double getMbqksje1() {
        return mbqksje1;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje1 the mbqksje1 to set
     */
    public void setMbqksje1(Double mbqksje1) {
        this.mbqksje1 = mbqksje1;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje2
     */
    public Double getMbqksje2() {
        return mbqksje2;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje2 the mbqksje2 to set
     */
    public void setMbqksje2(Double mbqksje2) {
        this.mbqksje2 = mbqksje2;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje3
     */
    public Double getMbqksje3() {
        return mbqksje3;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje3 the mbqksje3 to set
     */
    public void setMbqksje3(Double mbqksje3) {
        this.mbqksje3 = mbqksje3;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje4
     */
    public Double getMbqksje4() {
        return mbqksje4;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje4 the mbqksje4 to set
     */
    public void setMbqksje4(Double mbqksje4) {
        this.mbqksje4 = mbqksje4;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje5
     */
    public Double getMbqksje5() {
        return mbqksje5;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje5 the mbqksje5 to set
     */
    public void setMbqksje5(Double mbqksje5) {
        this.mbqksje5 = mbqksje5;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje6
     */
    public Double getMbqksje6() {
        return mbqksje6;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje6 the mbqksje6 to set
     */
    public void setMbqksje6(Double mbqksje6) {
        this.mbqksje6 = mbqksje6;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje7
     */
    public Double getMbqksje7() {
        return mbqksje7;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje7 the mbqksje7 to set
     */
    public void setMbqksje7(Double mbqksje7) {
        this.mbqksje7 = mbqksje7;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje8
     */
    public Double getMbqksje8() {
        return mbqksje8;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje8 the mbqksje8 to set
     */
    public void setMbqksje8(Double mbqksje8) {
        this.mbqksje8 = mbqksje8;
    }

    /**
     *创建时间:2018-12-11下午04:15:03
     *get方法
     * @return the mbqksje9
     */
    public Double getMbqksje9() {
        return mbqksje9;
    }

    /**
     * 创建时间:2018-12-11下午04:15:03
     * set方法
     * @param mbqksje9 the mbqksje9 to set
     */
    public void setMbqksje9(Double mbqksje9) {
        this.mbqksje9 = mbqksje9;
    }

    /**
     *创建时间:2018-12-11下午04:33:44
     *get方法
     * @return the dqnx10
     */
    public String getDqnx10() {
        return dqnx10;
    }

    /**
     * 创建时间:2018-12-11下午04:33:44
     * set方法
     * @param dqnx10 the dqnx10 to set
     */
    public void setDqnx10(String dqnx10) {
        this.dqnx10 = dqnx10;
    }

    /**
     *创建时间:2018-12-11下午04:33:44
     *get方法
     * @return the ksje10
     */
    public Double getKsje10() {
        return ksje10;
    }

    /**
     * 创建时间:2018-12-11下午04:33:44
     * set方法
     * @param ksje10 the ksje10 to set
     */
    public void setKsje10(Double ksje10) {
        this.ksje10 = ksje10;
    }

    /**
     *创建时间:2018-12-13上午09:54:25
     *get方法
     * @return the mbqksje10
     */
    public Double getMbqksje10() {
        return mbqksje10;
    }

    /**
     * 创建时间:2018-12-13上午09:54:25
     * set方法
     * @param mbqksje10 the mbqksje10 to set
     */
    public void setMbqksje10(Double mbqksje10) {
        this.mbqksje10 = mbqksje10;
    }

}
