package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 油气田企业增值税分配表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "yqtqyzzsfpbFormVO", propOrder = { "djxh", "dqynsze", "bcyfpskze", "sjfpse", "yyzcl", "trqzcl", "yytrqxxbl" })
@Getter
@Setter
public class YqtqyzzsfpbFormVO {
    /**
     * 登记序号
     */
    protected String djxh;

    /**
     * 当期应纳税总额
     */
    protected BigDecimal dqynsze;

    /**
     * 不参与分配税款总额
     */
    protected BigDecimal bcyfpskze;

    /**
     * 实际分配税额
     */
    protected BigDecimal sjfpse;

    /**
     * 原油总产量
     */
    protected BigDecimal yyzcl;

    /**
     * 天然气总产量
     */
    protected BigDecimal trqzcl;

    /**
     * 原油天然气销项比例
     */
    protected BigDecimal yytrqxxbl;
}