package com.css.znsb.nssb.service.zzsybnsrsb.converter;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhHandlerDTO;
import com.css.znsb.framework.sjjh.service.sjjh.fw.converter.SjjhDataConverter;
import com.css.znsb.nssb.constants.SbrwztConstants;
import com.css.znsb.nssb.constants.YclxConstants;
import com.css.znsb.nssb.pojo.dto.sbbc.SbbcGyRespDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.sbbc.ZzsybnsrSbDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.sbbc.ZzsybnsrSbbcReqDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsrywbw.lqfzpt.sbbc.ZzsybnsrSbbcRespDTO;
import com.css.znsb.nssb.service.zzsybnsrsb.ZzsYbnsrSbbcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;

import static com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants.ERROR;
import static com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

@Slf4j
@Service(value = "ZzsybnsrSbbcConverter")
public class ZzsybnsrSbbcConverter implements SjjhDataConverter {
    
    @Resource
    private ZzsYbnsrSbbcService sbbcService;
    
    @Override
    public String convertReq(SjjhHandlerDTO sjjhHandlerDTO) {
        final String bwnr = sjjhHandlerDTO.getSjjhDTO().getBwnr();
        final ZzsybnsrSbDTO sbDTO = JsonUtils.toBean(bwnr, ZzsybnsrSbDTO.class);
        final ZzsybnsrSbbcReqDTO reqDTO = sbbcService.sbbc(sbDTO);
        return JsonUtils.toJson(reqDTO);
    }

    @Override
    public CommonResult<Object> convertResp(String resp) {
        final SbbcGyRespDTO sbbcGyRespDTO = new SbbcGyRespDTO();
        String yclxDm;
        String ycxx;
        String sbyysm;
        try {
            if (GyUtils.isNull(resp)) {
                yclxDm = YclxConstants.QYDYWYC_YCLX_DM;
                ycxx = "乐企申报返回数据为空";
                sbyysm = "乐企申报返回数据为空，请联系系统运维人员。";
                sbbcGyRespDTO.setSbztDm(SbrwztConstants.SBZT_SBSB_DM);
                sbbcGyRespDTO.setYclxDm(yclxDm);
                sbbcGyRespDTO.setYcxx(ycxx);
                sbbcGyRespDTO.setSbyysm(sbyysm);
                return CommonResult.error(ERROR.getCode(), sbyysm, sbbcGyRespDTO);
            }
            final ZzsybnsrSbbcRespDTO sbbcRespDTO = JsonUtils.toBean(resp, ZzsybnsrSbbcRespDTO.class);
            if (GyUtils.isNull(sbbcRespDTO)) {
                yclxDm = YclxConstants.QYDYWYC_YCLX_DM;
                ycxx = "乐企申报返回报文转换失败";
                sbyysm = "乐企申报返回报文转换失败，请联系系统运维人员。";
                sbbcGyRespDTO.setSbztDm(SbrwztConstants.SBZT_SBSB_DM);
                sbbcGyRespDTO.setYclxDm(yclxDm);
                sbbcGyRespDTO.setYcxx(ycxx);
                sbbcGyRespDTO.setSbyysm(sbyysm);
                return CommonResult.error(ERROR.getCode(), sbyysm, sbbcGyRespDTO);
            }
            
            if (!"00".equals(sbbcRespDTO.getReturncode()) && !"99".equals(sbbcRespDTO.getReturncode())) {
                yclxDm = YclxConstants.LQYWYC_YCLX_DM;
                ycxx = sbbcRespDTO.getReturnmsg();
                sbyysm = sbbcRespDTO.getReturnmsg();
                sbbcGyRespDTO.setSbztDm(SbrwztConstants.SBZT_SBSB_DM);
                sbbcGyRespDTO.setYclxDm(yclxDm);
                sbbcGyRespDTO.setYcxx(ycxx);
                sbbcGyRespDTO.setSbyysm(sbyysm);
                return CommonResult.error(ERROR.getCode(), sbyysm, sbbcGyRespDTO);
            } else if ("99".equals(sbbcRespDTO.getReturncode())) {
                yclxDm = YclxConstants.LQXTYC_YCLX_DM;
                ycxx = sbbcRespDTO.getReturnmsg();
                sbyysm = sbbcRespDTO.getReturnmsg();
                sbbcGyRespDTO.setSbztDm(SbrwztConstants.SBZT_SBSB_DM);
                sbbcGyRespDTO.setYclxDm(yclxDm);
                sbbcGyRespDTO.setYcxx(ycxx);
                sbbcGyRespDTO.setSbyysm(sbyysm);
                return CommonResult.error(ERROR.getCode(), sbyysm, sbbcGyRespDTO);
            } else {
                sbbcGyRespDTO.setSbztDm(SbrwztConstants.SBZT_SBZ_DM);
                sbbcGyRespDTO.setPclsh(sbbcRespDTO.getPclsh());
                return CommonResult.success(sbbcGyRespDTO);
            }
        } catch (Exception e) {
            log.error("乐企增值税一般纳税人申报返回报文解析失败，失败原因：", e);
            final StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer));
            yclxDm = YclxConstants.QYDXTYC_YCLX_DM;
            ycxx = writer.toString();
            sbyysm = e.getMessage();
            sbbcGyRespDTO.setSbyysm(sbyysm);
            sbbcGyRespDTO.setYclxDm(yclxDm);
            sbbcGyRespDTO.setYcxx(ycxx);
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), sbyysm, sbbcGyRespDTO);
        }
    }
}
