package com.css.znsb.nssb.controller.cchxwssb.cchxws;

import cn.hutool.core.io.IoUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.css.framework.xxzx.api.xxtx.XxtxAPI;
import com.css.framework.xxzx.pojo.vo.xxtx.*;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.ServiceException;
import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.object.TypeUtils;
import com.css.znsb.framework.common.util.object.keyconverter.KeyCoverters;
import com.css.znsb.framework.common.util.string.StrUtils;
import com.css.znsb.framework.common.util.template.TemplateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.file.utils.ZnsbFileUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.framework.sjjh.constants.SjcjConstants;
import com.css.znsb.framework.sjjh.pojo.domain.ZnsbNssbSjjhDO;
import com.css.znsb.framework.sjjh.service.sjcj.IZnsbSjcjService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.NsrbqxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.constants.GyConstants;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.CxsCxsjcljgResponseDTO;
import com.css.znsb.nssb.pojo.dto.gy.gzlspnr.GzlGySbSaveReqDTO;
import com.css.znsb.nssb.pojo.dto.yhssycj.ywcx.YhscjNsrxxDTO;
import com.css.znsb.nssb.pojo.dto.zcxx.ZnsbNssbZcxxDTO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.*;
import com.css.znsb.nssb.pojo.vo.common.sbxxcx.SbxxcxTbbbReqVO;
import com.css.znsb.nssb.pojo.vo.gy.sbcx.SbxxcxPdfZipVO;
import com.css.znsb.nssb.pojo.vo.sswszm.bszs10283.HXZGZS10283Response;
import com.css.znsb.nssb.pojo.vo.sswszm.bszs10283.JkswzkxxVO;
import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.CchxwsnssbService;
import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.CxsSaveService;
import com.css.znsb.nssb.service.cchxwssb.plsb.CxsYsbsjcjService;
import com.css.znsb.nssb.service.cchxwssb.yhssjcj.YhssycjNewService;
import com.css.znsb.nssb.service.gy.ISbxxCxService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.css.znsb.nssb.service.skjn.ZnsbSkjnService;
import com.css.znsb.nssb.service.zcxx.ZnsbNssbZcxxService;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.nssb.utils.DbUtils;
import com.css.znsb.nssb.utils.SbPrintUtils;
import com.css.znsb.xxzx.api.znsbwf.ZnsbWfAPI;
import com.css.znsb.xxzx.pojo.znsbwf.ZnsbInitProcDataVO;
import com.css.znsb.xxzx.pojo.znsbwf.ZnsbWfDataVO;
import io.swagger.v3.core.util.Json;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.css.znsb.nssb.mapper.skjn.ZnsbNssbJkyzmxMapper;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.css.znsb.framework.common.pojo.CommonResult.success;

/**
 * 财产和行为税纳税申报
 */
@Tag(name = "财产和行为税纳税申报")
@RestController
@RequestMapping("/cchxwsnssb")
@Validated
@Slf4j
public class CchxwsnssbController {

    /**
     * 用户类型_自然人
     */
    private static final String YHLX_ZRR = "1";

    @Resource
    NsrxxApi nsrxxApi;
    @Resource
    CchxwsnssbService cchxwsnssbService;
    @Resource
    CompanyApi companyApi;
    @Resource
    private ZnsbWfAPI znsbWfAPI;
    @Resource
    ZnsbNssbZcxxService znsbNssbZcxxService;
    @Resource
    private IZnsbSjcjService sjcjService;
    @Resource
    private XxtxAPI xxtxAPI;
    @Resource
    private ISbxxCxService sbxxCxService;

    @Resource
    private ZnsbNssbSbrwService znsbNssbSbrwService;

    @Resource
    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;

    @Resource
    private ZnsbNssbJkyzmxMapper znsbNssbJkyzmxMapper;
    @Resource
    private CxsYsbsjcjService cxsYsbsjcjService;
    @Resource
    CxsSaveService cxsSaveService;
    @Resource
    ZnsbSkjnService znsbSkjnService;

    @Resource
    YhssycjNewService yhssycjNewService;

    /**
     * 财产和行为税申报初始化
     * @param cchxwscsReqVO 入参
     * @return 返回值
     */
    @PostMapping("/v1/cxsInit")
    public CommonResult<CchxwsnssbInitResVO> cchxwsnssbInit(@RequestBody CchxwscsReqVO cchxwscsReqVO) {
        //从当前登录人信息中取出登记序号，纳税人识别号和纳税人名称
        log.info("财行税初始化入参djxh:{},nsrsbh:{}",cchxwscsReqVO.getDjxh(),cchxwscsReqVO.getNsrsbh());
        String djxh = cchxwscsReqVO.getDjxh();
        String nsrsbh = cchxwscsReqVO.getNsrsbh();
        if (GyUtils.isNull(djxh) || GyUtils.isNull(nsrsbh)) {
            djxh = ZnsbSessionUtils.getDjxh();
            nsrsbh = ZnsbSessionUtils.getNsrsbh();
        }
        if (GyUtils.isNull(djxh) || GyUtils.isNull(nsrsbh)) {
            return CommonResult.error(-1, "纳税人登记不能为空，请核实!");
        }
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> znsbMhzcQyjbxxmxResVOCommonResult = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        List<JbxxmxsjVO> jbxxmxsj;
        String nsrmc;
        if (GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData()) && GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj())) {
            jbxxmxsj = znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj();
            nsrmc = jbxxmxsj.get(0).getNsrmc();
        }else {
            return CommonResult.error(-1, "纳税人登记不能为空，请核实!");
        }
        //用户类型 0 税务人 1 自然人2 企业身份
        String yhlx = "2";//默认企业身份
        String shxydm = null;
        CommonResult<CompanyBasicInfoDTO> companyInfo = companyApi.basicInfo(djxh,nsrsbh);
        if(companyInfo.getCode()==1 && GyUtils.isNotNull(companyInfo.getData()) && GyUtils.isNotNull(companyInfo.getData().getShxydm())){
            shxydm = companyInfo.getData().getShxydm();
        }

        //自然人取省级税务机关，纳税人主管税务所科分局代码
        final String zgswjgdm = jbxxmxsj.get(0).getZgswskfjDm();
        //纳税人状态代码
        final String nsrztdm = jbxxmxsj.get(0).getNsrztDm();

        //填充入参
        final CchxwsnssbInitReqVO cchxwsnssbInitReqVO = new CchxwsnssbInitReqVO();
        cchxwsnssbInitReqVO.setCsskssqq(cchxwscsReqVO.getCsskssqq());
        cchxwsnssbInitReqVO.setCsskssqz(cchxwscsReqVO.getCsskssqz());
        cchxwsnssbInitReqVO.setDqrq(cchxwscsReqVO.getDqrq());
        cchxwsnssbInitReqVO.setDjxh(djxh);
        cchxwsnssbInitReqVO.setNsrsbh(GyUtils.isNull(shxydm)?shxydm:nsrsbh);
        cchxwsnssbInitReqVO.setNsrmc(nsrmc);
        cchxwsnssbInitReqVO.setZgswskfj_dm(zgswjgdm);
        cchxwsnssbInitReqVO.setYhlx(yhlx);
        cchxwsnssbInitReqVO.setShxydm(shxydm);
        cchxwsnssbInitReqVO.setFtbz(cchxwscsReqVO.getFtbz());
        cchxwsnssbInitReqVO.setNsrztDm(nsrztdm);
        //调用后台接口
        CchxwsnssbInitResVO cchxwsnssbInitResVO = cchxwsnssbService.cxsInit(cchxwsnssbInitReqVO);
        return CommonResult.success(cchxwsnssbInitResVO);
    }



    /**
     * 查询申报表信息
     * @param cchxwsnssbquerysbbxxReqVO 入参
     * @return 返回值
     */
    @PostMapping("/v1/querySbbxx")
    public CommonResult<CchxwsnssbbxxVO> querycchxwsnssbSbbxx(@RequestBody CchxwsnssbquerysbbxxReqVO cchxwsnssbquerysbbxxReqVO) throws Exception {
        log.info("财行税查询申报信息入参djxh:{},nsrsbh:{}，shxydm:{}",cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getDjxh(),cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getNsrsbh(),cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getShxydm());
        String djxh = cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getDjxh();
        String nsrsbh = cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getNsrsbh();
        if (GyUtils.isNull(djxh) || GyUtils.isNull(nsrsbh)) {
            return CommonResult.error(-1, "纳税人登记不能为空，请核实!");
        }
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> znsbMhzcQyjbxxmxResVOCommonResult = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        List<JbxxmxsjVO> jbxxmxsj;
        String nsrmc = null;
        if (GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData())
                && GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj())) {
            jbxxmxsj = znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj();
            if (GyUtils.isNotNull(jbxxmxsj.get(0).getNsrmc())) {
                nsrmc = jbxxmxsj.get(0).getNsrmc();
            }
        }else {
            return CommonResult.error(-1, "纳税人登记不能为空，请核实!");
        }
        String shxydm = null;
        CommonResult<CompanyBasicInfoDTO> companyInfo = companyApi.basicInfo(djxh,nsrsbh);
        if(companyInfo.getCode()==1 && GyUtils.isNotNull(companyInfo.getData()) && GyUtils.isNotNull(companyInfo.getData().getShxydm())){
            shxydm = companyInfo.getData().getShxydm();
        }
        //用户类型 0 税务人 1 自然人2 企业身份
        String yhlx = "2";//默认企业身份
        //自然人取前台传递税务机关，纳税人主管税务所科分局代码
        String swjgdm = jbxxmxsj.get(0).getZgswskfjDm();
        cchxwsnssbquerysbbxxReqVO.getNsrxxVO().setDjxh(djxh);
        cchxwsnssbquerysbbxxReqVO.getNsrxxVO().setNsrsbh(GyUtils.isNull(nsrsbh)?shxydm:nsrsbh);
        cchxwsnssbquerysbbxxReqVO.getNsrxxVO().setNsrmc(nsrmc);
        cchxwsnssbquerysbbxxReqVO.getNsrxxVO().setZgswskfj_dm(swjgdm);
        cchxwsnssbquerysbbxxReqVO.getNsrxxVO().setYhlx(yhlx);
        final CchxwsnssbbxxVO cchxwsnssbbxxVO = cchxwsnssbService.querySbbmx(cchxwsnssbquerysbbxxReqVO);
        return CommonResult.success(cchxwsnssbbxxVO);
    }

    /**
     * 查询申报表信息
     * @param cchxwsnssbquerysbbxxReqVO 入参
     * @return 返回值
     */
//    public CchxwsnssbbxxVO cxSbbxx(CchxwsnssbquerysbbxxReqVO cchxwsnssbquerysbbxxReqVO) throws Exception {
//        CchxwsnssbbxxVO cchxwsnssbbxxVO = new CchxwsnssbbxxVO();
//        final String yhlx = cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getYhlx();
//        if(!GyUtils.isNull(cchxwsnssbquerysbbxxReqVO.getInitResMxVOList())){
//            final CchxwsnssbInitResMxVO initResMxVO = cchxwsnssbquerysbbxxReqVO.getInitResMxVOList().get(0);
//            log.info(cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getDjxh() + initResMxVO.getMrskssqq() + initResMxVO.getMrskssqz()
//                    + ":yqsbbz=" + initResMxVO.getYqsbbz() + "-tqsbbz=" + initResMxVO.getTqsbbz());
//            if(!YHLX_ZRR.equals(yhlx)){
//                if("Y".equals(initResMxVO.getYqsbbz()) || "Y".equals(initResMxVO.getTqsbbz())){
//                    //逾期申报分支、提前申报分支
//                    cchxwsnssbbxxVO = cchxwsnssbService.queryYqsbbmx(cchxwsnssbquerysbbxxReqVO);
//                }else {
//                    //正常申报分支
//                    cchxwsnssbbxxVO = cchxwsnssbService.querySbbmx(cchxwsnssbquerysbbxxReqVO);
//                }
//            }else {
//                if ("Y".equals(initResMxVO.getYqsbbz()) || "Y".equals(initResMxVO.getTqsbbz())) {
//                    //自然人逾期申报分支、提前申报分支
//                    cchxwsnssbbxxVO = cchxwsnssbService.queryYqsbbmx(cchxwsnssbquerysbbxxReqVO);
//                } else {
//                    //自然人正常申报分支
//                    cchxwsnssbbxxVO = cchxwsnssbService.queryZrrSbbmx(cchxwsnssbquerysbbxxReqVO);
//                }
//            }
//        }
//        return cchxwsnssbbxxVO;
//    }

    /**
     * 保存财行税申报数据
     * @param cchxwsnssbbxxVO 入参
     * @return 返回值
     */
    @PostMapping("/v1/saveSbbxx")
    @SneakyThrows
    public CommonResult<Map<String,Object>> savecchxwsnssbSbbxx(@RequestBody CchxwsnssbbxxVO cchxwsnssbbxxVO){
        log.info("财行税提交保存申报信息入参djxh:{},nsrsbh:{}，shxydm:{}",cchxwsnssbbxxVO.getCchxwsnssbzbVO().getDjxh(),cchxwsnssbbxxVO.getCchxwsnssbzbVO().getNsrsbh(),cchxwsnssbbxxVO.getCchxwsnssbzbVO().getShxydm());
        log.info("财行税申报任务uuid:{}",cchxwsnssbbxxVO.getCchxwsnssbzbVO().getSbrwuuid());
        String djxh = cchxwsnssbbxxVO.getCchxwsnssbzbVO().getDjxh();
        String nsrsbh = cchxwsnssbbxxVO.getCchxwsnssbzbVO().getNsrsbh();
        if (GyUtils.isNull(djxh) || GyUtils.isNull(nsrsbh)) {
            return CommonResult.error(-1, "纳税人登记不能为空，请核实!");
        }
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> znsbMhzcQyjbxxmxResVOCommonResult = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        List<JbxxmxsjVO> jbxxmxsj = new ArrayList<>();
        String nsrmc = null;
        if (GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData())
                && GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj())) {
            jbxxmxsj = znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj();
            if (GyUtils.isNotNull(jbxxmxsj.get(0).getNsrmc())) {
                nsrmc = jbxxmxsj.get(0).getNsrmc();
            }
        }
        String shxydm = null;
        CommonResult<CompanyBasicInfoDTO> companyInfo = companyApi.basicInfo(djxh,nsrsbh);
        if(companyInfo.getCode()==1 && GyUtils.isNotNull(companyInfo.getData()) && GyUtils.isNotNull(companyInfo.getData().getShxydm())){
            shxydm = companyInfo.getData().getShxydm();
        }
        //自然人取省级税务机关，纳税人主管税务所科分局代码
        final String zgswjgdm = jbxxmxsj.get(0).getZgswskfjDm();
//        //用户类型 0 税务人 1 自然人2 企业身份
        String yhlx = "2";//默认企业身份
//        if(GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData())
//                &&GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData().getNsrzgxx())){
//            List<NsrzgxxVO> nsrzgxx = znsbMhzcQyjbxxmxResVOCommonResult.getData().getNsrzgxx();
//            yhlx = nsrzgxx.get(0).getNsrlx();
//        }
//        //自然人网络id
//        final String userId = //ZnsbSessionUtils.getUserId();
        final CxstysbNsrxxVO cxstysbNsrxxVO = new CxstysbNsrxxVO();
        cxstysbNsrxxVO.setDjxh(djxh);
        cxstysbNsrxxVO.setNsrsbh(GyUtils.isNull(nsrsbh)?shxydm:nsrsbh);
        cxstysbNsrxxVO.setNsrmc(nsrmc);
        cxstysbNsrxxVO.setZgswskfj_dm(zgswjgdm);
        cxstysbNsrxxVO.setYhlx(yhlx);
//        cxstysbNsrxxVO.setUserId(userId);
        cchxwsnssbbxxVO.setCxstysbNsrxxVO(cxstysbNsrxxVO);
        //从当前登录人信息中取出登记序号，纳税人识别号和纳税人名称
        //组装受理信息
        final CchxwsnssbSlxxVO slxxVO = new CchxwsnssbSlxxVO();
//        slxxVO.setJbr(userName);//ZnsbSessionUtils.getUserName());
//        slxxVO.setJbrsfzjhm(nsrzjhm);//ZnsbSessionUtils.getNsrzjhm());
        cchxwsnssbbxxVO.setCchxwsnssbSlxxVO(slxxVO);
        final Map<String, Object> returnMap = saveSbbxx(cchxwsnssbbxxVO,companyInfo.getData());
        return CommonResult.success(returnMap);
    }

    /**
     * 财产和行为税申报初始化
     * @param cchxwsnssbInitReqVO 入参
     * @return 返回值
     */
//    @PostMapping("/v1/cxsInit")
//    public CommonResult<CchxwsnssbInitResVO> cxsInit(@RequestBody CchxwsnssbInitReqVO cchxwsnssbInitReqVO) {
//        final CchxwsnssbInitResVO cchxwsnssbInitResVO = cchxwsnssbService.cxsInit(cchxwsnssbInitReqVO);
//        return CommonResult.success(cchxwsnssbInitResVO);
//    }

    /**
     * 查询申报表信息
     * @param cchxwsnssbquerysbbxxReqVO 入参
     * @return 返回值
     */
    public CchxwsnssbbxxVO querySbbxx(@RequestBody CchxwsnssbquerysbbxxReqVO cchxwsnssbquerysbbxxReqVO) throws Exception {
        CchxwsnssbbxxVO cchxwsnssbbxxVO = new CchxwsnssbbxxVO();
        if(!GyUtils.isNull(cchxwsnssbquerysbbxxReqVO.getInitResMxVOList())){
            final CchxwsnssbInitResMxVO initResMxVO = cchxwsnssbquerysbbxxReqVO.getInitResMxVOList().get(0);
            log.info(cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getDjxh() + initResMxVO.getMrskssqq() + initResMxVO.getMrskssqz()
                    + ":yqsbbz=" + initResMxVO.getYqsbbz() + "-tqsbbz=" + initResMxVO.getTqsbbz());
            //正常申报分支
            cchxwsnssbbxxVO = cchxwsnssbService.querySbbmx(cchxwsnssbquerysbbxxReqVO);
        }
        return cchxwsnssbbxxVO;
    }

    /**
     * 财行税工作流审核成功后调用的接口
     * @param gysbSaveReqDTO 入参
     * @return 返回值
     */
    @PostMapping("/v1/saveCxsGzlSbbxx")
    public CommonResult<String> saveCxsGzlSbbxx(@RequestBody GzlGySbSaveReqDTO gysbSaveReqDTO){
        if(GyUtils.isNull(gysbSaveReqDTO) || GyUtils.isNull(gysbSaveReqDTO.getLcslid())){
            throw ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, "财行税提交申报信息异常");
        }
        cchxwsnssbService.gzlsaveSbbxx(gysbSaveReqDTO.getLcslid());
        return CommonResult.success("成功");
    }

    /**
     * 财行税工作流审核不通过后调用的接口
     * @param gysbSaveReqDTO 入参
     * @return 返回值
     */
    @PostMapping("/v1/shsbCxsGzlSbbxx")
    public CommonResult<String> shsbCxsGzlSbbxx(@RequestBody GzlGySbSaveReqDTO gysbSaveReqDTO){
        log.info("------财行税工作流审核不通过后调用的接口------------");
        if(GyUtils.isNull(gysbSaveReqDTO) || GyUtils.isNull(gysbSaveReqDTO.getLcslid())){
            throw ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, "财行税提交申报信息异常");
        }
        cchxwsnssbService.shsbCxsGzlSbbxx(gysbSaveReqDTO.getLcslid());
        return CommonResult.success("成功");
    }

    /**
     * 财行税查询暂存表
     * @param cchxwsnssbGzlVO 入参
     * @return 返回值
     */
    @PostMapping("/v1/querycxszcxx")
    public CommonResult<CchxwsnssbGzlVO> querycxszcxx(@RequestBody CchxwsnssbGzlVO cchxwsnssbGzlVO){
        CchxwsnssbGzlVO resZcxxVO;
        if(GyUtils.isNotNull(cchxwsnssbGzlVO.getSbrwCkbz()) && GyUtils.isNotNull(cchxwsnssbGzlVO.getSbrwuuid())){
            ZnsbNssbZcxxDTO znsbNssbZcxxDTO = znsbNssbZcxxService.queryZcxxBySbrwuuid(cchxwsnssbGzlVO.getSbrwuuid());
            if (GyUtils.isNotNull(znsbNssbZcxxDTO)){
                resZcxxVO = JacksonUtils.toObj(znsbNssbZcxxDTO.getBusinessclob(),CchxwsnssbGzlVO.class);
            }else{
                resZcxxVO = cchxwsnssbService.handleZcxxVO(cchxwsnssbGzlVO);
            }

        }else{
            if(GyUtils.isNull(cchxwsnssbGzlVO.getLcslid())){
                return CommonResult.error(500,"流程实例ID为空");
            }
            ZnsbNssbZcxxDTO znsbNssbZcxxDTO = znsbNssbZcxxService.queryZcxxByLcsjid(cchxwsnssbGzlVO.getLcslid());
            resZcxxVO = JacksonUtils.toObj(znsbNssbZcxxDTO.getBusinessclob(),CchxwsnssbGzlVO.class);
        }
        return CommonResult.success(resZcxxVO);
    }

    /**
     * 财行税合并申报保存
     * @param cchxwsnssbbxxVO 入参
     * @return 返回值
     */
//    @PostMapping("/v1/saveSbbxx")
    public Map<String,Object> saveSbbxx(@RequestBody CchxwsnssbbxxVO cchxwsnssbbxxVO,CompanyBasicInfoDTO companyBasicInfoDTO) throws Exception {
        Map<String,Object> map = new HashMap<>();
        //增加异步 by zwj 正常流程应该先写入mongo 再发消息，先测试消息队列
//        final String ybkg = CxsGyUtils.getXtcs(cchxwsnssbbxxVO.getCxstysbNsrxxVO().getZgswskfj_dm() , "DZSWJ00SBZX020009" , "N");
        final String ybkg = "N";
//        log.error("@cchxwsnssb/v1/saveSbbxx@-------> 异步申报开关" + ybkg);
        final String yhlx = cchxwsnssbbxxVO.getCxstysbNsrxxVO().getYhlx();
//        if("Y".equals(ybkg)){
//            //异步申报分支
//            String id = CxsGyUtils.buildUuid();//贯穿异步流程的id
//            try{
//                //业务VO补充流水表的ID
//                cchxwsnssbbxxVO.getCxstysbNsrxxVO().setYsquuid(id);
//                //写流水表
//                log.error("@/v1/saveSbbxx@-------> 开始写入流水表");
//                final Map<String , Object> sblsmap = new HashMap<>();
//                sblsmap.put("ysqxxid" ,id);
//                sblsmap.put("djxh" , cchxwsnssbbxxVO.getCxstysbNsrxxVO().getDjxh());
//                sblsmap.put("sssqq" , CchxwsnssbGyUtils.firstDayofYearBySsnd(CxsGyUtils.getRealStr(CxsGyUtils.getNdnum(new Date()))));
//                sblsmap.put("sssqz" , CchxwsnssbGyUtils.lastDayofYearBySsnd(CxsGyUtils.getRealStr(CxsGyUtils.getNdnum(new Date()))));
//                sblsmap.put("lrrsfid" , cchxwsnssbbxxVO.getCxstysbNsrxxVO().getUserId());
//                sblsmap.put("swjgDm" , cchxwsnssbbxxVO.getCxstysbNsrxxVO().getZgswskfj_dm());
//                sblsmap.put("ywbm" , CxsSxztbmConstants.getYwbmCxssb());
//                cchxwsnssbAsyncService.insertSbls(sblsmap);
//                log.error("@/v1/saveSbbxx@-------> 流水表写入成功");
//                //写mongo
//                log.error("@/v1/saveSbbxx@-------> 开始写入mongo");
//                MongoVO mongovo = new MongoVO();
//                mongovo.setUuid(id);
//                mongovo.setText(CssJsonUtils.toJson(cchxwsnssbbxxVO));
//                mongovo.setLrrq(new Date());
//                MongoUtils.save(mongovo);
//                log.error("@/v1/saveSbbxx@-------> mongo写入成功");
//                //写消息队列
//                log.error("@/v1/saveSbbxx@-------> 开始写入TDMQ");
//                TdmqCxsMessage tdmqMessage = new TdmqCxsMessage();
//                tdmqMessage.setMongoid(id);
//                tdmqMessage.setParam(JacksonUtils.toJson(cchxwsnssbbxxVO));
//                tdmqMessage.setYhlx(yhlx);
//                TdmqUtils.sendTdmqMessage(tdmqMessage);
//                log.error("@/v1/saveSbbxx@-------> TDMQ写入成功");
//                map.put("ysquuid" , id);
//                map.put("ybsbReturnCode" , "0");
//                map.put("ybsbReturnMsg" , "异步申报提交成功");
//            } catch (Exception e){
//                log.error("@cchxwsnssb/v1/saveSbbxx@-------> 异步申报失败，原因" + FtsUtils.getTrace(e));
//                map.put("ysquuid" , id);
//                map.put("ybsbReturnCode" , "1");
//                map.put("ybsbReturnMsg" , FtsUtils.getTrace(e));
//            }
//        } else {
        //正常申报分支
        if(!YHLX_ZRR.equals(yhlx)){
            ZnsbWfDataVO znsbWfDataVO = new ZnsbWfDataVO();
            znsbWfDataVO.setYwsxdm(YzpzzlEnum.CXS.getDm());
            znsbWfDataVO.setZzuuid(ZnsbSessionUtils.getZzuuid());
//            znsbWfDataVO.setZzuuid("50151bb947034fed83ed74dc1a0a6a4c");
            CommonResult<ZnsbInitProcDataVO> commonResult = znsbWfAPI.initProcess(znsbWfDataVO);
            log.info("------commonResult-------{}",commonResult);
            String sfkqgzl = "N";
            // 当sfqdlc为1的时候启用工作流,其他情况视为不启用工作流
            if(GyUtils.isNotNull(commonResult.getData()) && GyUtils.isNotNull(commonResult.getData().getSfqdlc()) && "1".equals(commonResult.getData().getSfqdlc())){
                // 启用工作流提交申报的分支处理接口
                sfkqgzl = "Y";
                log.info("------sfkqgzl-------{}",sfkqgzl);
                map = cchxwsnssbService.saveSbbxx(cchxwsnssbbxxVO,commonResult.getData(),companyBasicInfoDTO,znsbWfDataVO,sfkqgzl);
                // 走工作流
                map.put("clbz","audit");
            }else{
                //未启用工作流的正常申报处理分支
                //纳税人分支
                sfkqgzl = "N";
                log.info("------sfkqgzl-------{}",sfkqgzl);
                map = cchxwsnssbService.saveSbbxx(cchxwsnssbbxxVO,commonResult.getData(),companyBasicInfoDTO,znsbWfDataVO,sfkqgzl);
                // 不走工作流
                map.put("clbz","success");
            }
        }
//            else {
//                //自然人分支
//                map = cchxwsnssbService.saveZrrSbbxx(cchxwsnssbbxxVO);
//            }

//        }
        //给前端返回异步开关
        map.put("ybkg" , ybkg);
        return map;
    }

    /**
     * 财产和行为税申报初始化 - 错误更正
     * @param cchxwsnssbCwgzInitReqVO 入参
     * @return 返回值
     */
    @PostMapping("/v1/cxsCwgzInit")
    public CommonResult<CchxwsnssbbxxVO> cxsCwgzInit(@RequestBody CchxwsnssbCwgzInitReqVO cchxwsnssbCwgzInitReqVO){
//        final String djxh = //cxssbSessionUtils.getDjxh();
//        final String shxydm = //cxssbSessionUtils.getShxydm();
//        final String nsrsbh = //cxssbSessionUtils.getNsrsbh();
//        final String nsrmc = //cxssbSessionUtils.getNsrmc();
        final String djxh = cchxwsnssbCwgzInitReqVO.getDjxh();
        final String nsrsbh = cchxwsnssbCwgzInitReqVO.getNsrsbh();
        if(GyUtils.isNull(djxh)||GyUtils.isNull(nsrsbh)){
            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
        }
        //自然人取省级税务机关，纳税人主管税务所科分局代码
//        String zgswjgdm = //cxssbSessionUtils.getFtsZgswskfjDm();
        //用户类型 0 税务人 1 自然人2 企业身份
        final String yhlx = "2";
        //如果为自然人，检验税务机关，其他以后补检验入参数据
        /*if("1".equals(yhlx)){
            final String zgswskfjDmGz = cchxwsnssbCwgzInitReqVO.getCxscwgzInitReqVO().getZgswskfjDm();
            if(GyUtils.isNull(zgswskfjDmGz)){
                final CchxwsnssbbxxVO failVO = new CchxwsnssbbxxVO();
                failVO.setReturnCode("-1");
                failVO.setReturnMsg("更正信息中主管税务所科分局代码为空，请核实!");
                return CommonResult.success(failVO);
            }else {
                zgswjgdm = zgswskfjDmGz;
            }
        }*/
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> znsbMhzcQyjbxxmxResVOCommonResult = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        List<JbxxmxsjVO> jbxxmxsj = new ArrayList<>();
        String nsrmc = null;
        if (GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData())
                && GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj())) {
            jbxxmxsj = znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj();
            nsrmc = jbxxmxsj.get(0).getNsrmc();
        }else {
            log.info("财产和行为税参数初始化无纳税人基本信息djxh:{},nsrsbh:{}",GyUtils.isNull(djxh)?"登记序号为空":djxh,GyUtils.isNull(nsrsbh)?"纳税人识别号为空":nsrsbh);
            return CommonResult.error(-1, "纳税人登记不能为空，请核实!");
        }
        final String zgswjgdm = jbxxmxsj.get(0).getZgswskfjDm();
        String shxydm = null;
        CommonResult<CompanyBasicInfoDTO> companyInfo = companyApi.basicInfo(djxh,nsrsbh);
        if(companyInfo.getCode()==1 && GyUtils.isNotNull(companyInfo.getData()) && GyUtils.isNotNull(companyInfo.getData().getShxydm())){
            shxydm = companyInfo.getData().getShxydm();
        }
//        cchxwsnssbCwgzInitReqVO.setCxscwgzInitReqVO(new CxscwgzInitReqVO());
        cchxwsnssbCwgzInitReqVO.getCxscwgzInitReqVO().setDjxh(djxh);
        cchxwsnssbCwgzInitReqVO.getCxscwgzInitReqVO().setNsrsbh(GyUtils.isNull(nsrsbh)?shxydm:nsrsbh);
        cchxwsnssbCwgzInitReqVO.getCxscwgzInitReqVO().setNsrmc(nsrmc);
        cchxwsnssbCwgzInitReqVO.getCxscwgzInitReqVO().setZgswskfjDm(zgswjgdm);
        cchxwsnssbCwgzInitReqVO.getCxscwgzInitReqVO().setYhlx(yhlx);
        final CchxwsnssbbxxVO cchxwsnssbbxxVO = cchxwsnssbService.cxsCwgzInit(cchxwsnssbCwgzInitReqVO);
        return CommonResult.success(cchxwsnssbbxxVO);
    }

    /**
     * 保存财行税申报数据 - 错误更正
     * @param cchxwsnssbbxxVO 入参
     * @return 返回值
     */
    @PostMapping("/v1/saveCwgzSbbxx")
    public CommonResult<Map<String,Object>> saveCwgzSbbxx(@RequestBody CchxwsnssbbxxVO cchxwsnssbbxxVO){
        final String djxh = cchxwsnssbbxxVO.getDjxh();
        final String nsrsbh = cchxwsnssbbxxVO.getNsrsbh();
        if(GyUtils.isNull(djxh)||GyUtils.isNull(nsrsbh)){
            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
        }
        //检验传值信息
        final CxscwgzInitReqVO cxscwgzInitReqVO = cchxwsnssbbxxVO.getCxscwgzInitReqVO();
        if(GyUtils.isNull(cxscwgzInitReqVO.getSbuuid())
                || GyUtils.isNull(cxscwgzInitReqVO.getSkssqq()) || GyUtils.isNull(cxscwgzInitReqVO.getSkssqz())){
            final Map<String,Object> failMap = new HashMap<>();
            cchxwsnssbbxxVO.setReturnCode("-9");
            cchxwsnssbbxxVO.setReturnMsg("更正申报入参不能为空，请核实!");
            failMap.put("cchxwsnssbbxxVO", cchxwsnssbbxxVO);
            return CommonResult.success(failMap);
        }
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> znsbMhzcQyjbxxmxResVOCommonResult = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        List<JbxxmxsjVO> jbxxmxsj = new ArrayList<>();
        String nsrmc = null;
        if (GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData())
                && GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj())) {
            jbxxmxsj = znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj();
            nsrmc = jbxxmxsj.get(0).getNsrmc();
        }else {
            log.info("财产和行为税参数初始化无纳税人基本信息djxh:{},nsrsbh:{}",GyUtils.isNull(djxh)?"登记序号为空":djxh,GyUtils.isNull(nsrsbh)?"纳税人识别号为空":nsrsbh);
            return CommonResult.error(-1, "纳税人登记不能为空，请核实!");
        }
        CommonResult<CompanyBasicInfoDTO> companyInfo = companyApi.basicInfo(djxh,nsrsbh);
        CompanyBasicInfoDTO companyBasicInfoDTO = new CompanyBasicInfoDTO();
        if(companyInfo.getCode()==1 && GyUtils.isNotNull(companyInfo.getData())){
            companyBasicInfoDTO = companyInfo.getData();
        }
        final String zgswjgdm = jbxxmxsj.get(0).getZgswskfjDm();
        //自然人取省级税务机关，纳税人主管税务所科分局代码
//        String zgswjgdm = //cxssbSessionUtils.getFtsZgswskfjDm();
        //用户类型 0 税务人 1 自然人2 企业身份
        final String yhlx = "2";
        //如果为自然人，检验税务机关，暂未传，自己查询
        /*if("1".equals(yhlx)){
            final String zgswskfjDmGz = cchxwsnssbbxxVO.getCxscwgzInitReqVO().getZgswskfjDm();
            if(GyUtils.isNull(zgswskfjDmGz)){
                final Map<String, Object> failVO = new HashMap<>();
                failVO.put("returnCode", "-1");
                failVO.put("returnMsg", "更正信息中主管税务所科分局代码为空，请核实!");
                return CommonResult.success(failVO);
            }else {
                zgswjgdm = zgswskfjDmGz;
            }
        }*/
        //自然人网络id
//        final String userId = ;//cxssbSessionUtils.getUserId();

        cchxwsnssbbxxVO.getCxscwgzInitReqVO().setDjxh(djxh);
        cchxwsnssbbxxVO.getCxscwgzInitReqVO().setNsrsbh(nsrsbh);
        cchxwsnssbbxxVO.getCxscwgzInitReqVO().setNsrmc(nsrmc);
        cchxwsnssbbxxVO.getCxscwgzInitReqVO().setZgswskfjDm(zgswjgdm);
        cchxwsnssbbxxVO.getCxscwgzInitReqVO().setYhlx(yhlx);
        cchxwsnssbbxxVO.getCxscwgzInitReqVO().setUserId("");
        //组装受理信息
        final CchxwsnssbSlxxVO slxxVO = new CchxwsnssbSlxxVO();
//        slxxVO.setJbr(userName);//cxssbSessionUtils.getUserName());
//        slxxVO.setJbrsfzjhm(nsrzjhm);//cxssbSessionUtils.getNsrzjhm());
        cchxwsnssbbxxVO.setCchxwsnssbSlxxVO(slxxVO);
        final String ybkg = "N";
//        log.error("@cchxwsnssb/v1/saveSbbxx@-------> 异步申报开关" + ybkg);
        ZnsbWfDataVO znsbWfDataVO = new ZnsbWfDataVO();
        znsbWfDataVO.setYwsxdm(YzpzzlEnum.CXS.getDm());
        znsbWfDataVO.setZzuuid(ZnsbSessionUtils.getZzuuid());
//            znsbWfDataVO.setZzuuid("50151bb947034fed83ed74dc1a0a6a4c");
        CommonResult<ZnsbInitProcDataVO> commonResult = znsbWfAPI.initProcess(znsbWfDataVO);
        String sfkqgzl = "N";
        Map<String,Object> map = new HashMap<>();
        // 当sfqdlc为1的时候启用工作流,其他情况视为不启用工作流
        if(GyUtils.isNotNull(commonResult.getData()) && GyUtils.isNotNull(commonResult.getData().getSfqdlc()) && "1".equals(commonResult.getData().getSfqdlc())){
            // 启用工作流提交申报的分支处理接口
            sfkqgzl = "Y";
            map = cchxwsnssbService.saveCwgzSbbxx(cchxwsnssbbxxVO,commonResult.getData(),companyBasicInfoDTO,znsbWfDataVO,sfkqgzl);
            // 走工作流
            map.put("clbz","audit");
        }else{
            //未启用工作流的正常申报处理分支
            //纳税人分支
            sfkqgzl = "N";
            map = cchxwsnssbService.saveCwgzSbbxx(cchxwsnssbbxxVO,commonResult.getData(),companyBasicInfoDTO,znsbWfDataVO,sfkqgzl);
            // 不走工作流
            map.put("clbz","success");
        }
        if(GyUtils.isNull(map)){
            return CommonResult.success(null);
        }
        map.put("ybkg" , ybkg);
        return CommonResult.success(map);
    }

    /**
     * 财产和行为税申报初始化 - 逾期申报
     * @param cxsYqwsbInitReqVo 入参
     * @return 返回值
     */
    @PostMapping("/v1/cxsYqsbInit")
    public CommonResult<List<CchxwsYqwsbVo>> cxsYqsbInit(@RequestBody CxsYqwsbInitReqVo cxsYqwsbInitReqVo){
        //从当前登录人信息中取出登记序号，纳税人识别号和纳税人名称
        String djxh = cxsYqwsbInitReqVo.getDjxh();
        String nsrsbh = cxsYqwsbInitReqVo.getNsrsbh();
        if (GyUtils.isNull(djxh) || GyUtils.isNull(nsrsbh)) {
            djxh = ZnsbSessionUtils.getDjxh();
            nsrsbh = ZnsbSessionUtils.getNsrsbh();
        }
//        final String nsrmc = ZnsbSessionUtils.getNsrmc();
        if (GyUtils.isNull(djxh) || GyUtils.isNull(nsrsbh)) {
            return CommonResult.error(-1, "纳税人登记不能为空，请核实!");
        }
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> znsbMhzcQyjbxxmxResVOCommonResult = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        List<JbxxmxsjVO> jbxxmxsj = new ArrayList<>();
        if (GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData())
                && GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj())) {
            jbxxmxsj = znsbMhzcQyjbxxmxResVOCommonResult.getData().getJbxxmxsj();
        }

        //自然人取省级税务机关，纳税人主管税务所科分局代码
        final String zgswjgdm = jbxxmxsj.get(0).getZgswskfjDm();
//        //用户类型 0 税务人 1 自然人2 企业身份
        String yhlx = "2";//默认企业身份
//        if(GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData())
//                &&GyUtils.isNotNull(znsbMhzcQyjbxxmxResVOCommonResult.getData().getNsrzgxx())){
//            List<NsrzgxxVO> nsrzgxx = znsbMhzcQyjbxxmxResVOCommonResult.getData().getNsrzgxx();
//            yhlx = nsrzgxx.get(0).getNsrlx();
//        }
        try {
//            final List<CchxwsYqwsbVo> cchxwsYqwsbVoList = cchxwsnssbFeignClients.cxsYqsbInit(nsrsbh, djxh, zgswjgdm, yhlx);
            final Map<String,String> reqMap = new HashMap<>();
            reqMap.put("nsrsbh",nsrsbh);
            reqMap.put("djxh",djxh);
            reqMap.put("zgswjgdm",zgswjgdm);
            reqMap.put("yhlx",yhlx);
//            final String params = JacksonUtils.toJson(reqMap);
//            final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/cchxwsnssb/v1/cxsYqsbInit",
//                    "cxssb-service","post", InvokeGt4Constants.XTDM.XDJ,"",params);
//            final CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
            final String data = DbUtils.getData("cxsYqsbInit");
//            final List<CchxwsYqwsbVo> cchxwsYqwsbVoList = JsonUtils.toList(data, CchxwsYqwsbVo.class);
//            final List<CchxwsYqwsbVo> cchxwsYqwsbVoList = this.cxsYqsbInit(nsrsbh, djxh, zgswjgdm, yhlx);
            final List<CchxwsYqwsbVo> cchxwsYqwsbVoList = new ArrayList<>();
            return success(cchxwsYqwsbVoList);
        } catch (Exception e){
            return CommonResult.error(-1,e.getMessage());
        }
    }

//    /**
//     * 财行税初始化 - 逾期申报
//     * @param nsrsbh 社会信用代码
//     * @param djxh 登记序号
//     * @param zgswjgdm 税务机关
//     * @return 返回值
//     */
////    public List<CchxwsYqwsbVo> cxsYqsbInit(String nsrsbh, String djxh,String zgswjgdm, String yhlx) {
////        final List<CchxwsYqwsbVo> returnList = new ArrayList<>();
////        // 逾期申报分支
////        final List<CchxwsYqwsbVo> cchxwsYqwsbVoList = cchxwsnssbService.cxsYqsbInit(nsrsbh,djxh,zgswjgdm ,yhlx);
////        if (!GyUtils.isNull(cchxwsYqwsbVoList)) {
////            returnList.addAll(cchxwsYqwsbVoList);
////        }
////        // 延期申报分支，自然人不考虑
////        if(!YHLX_ZRR.equals(yhlx)){
////            log.info("cxsYqsbInit_dateA1_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
////            final List<CchxwsYqwsbVo> yqsbVoList = cchxwsnssbService.getYanQiList(djxh);
////            if (!GyUtils.isNull(yqsbVoList)) {
////                returnList.addAll(yqsbVoList);
////            }
////            log.info("cxsYqsbInit_dateA2_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
////        }
////        return returnList;
////    }
//
//    /**
//     * 查询申报表信息 - 逾期申报
//     * @param cchxwsnssbquerysbbxxReqVO 入参
//     * @return 返回值
//     */
//    @PostMapping("/v1/queryYqsbbxx")
//    public CommonResult<CchxwsnssbbxxVO> queryYqsbbxx(@RequestBody CchxwsnssbquerysbbxxReqVO cchxwsnssbquerysbbxxReqVO){
////        final String djxh = //cxssbSessionUtils.getDjxh();
////        final String shxydm = //cxssbSessionUtils.getShxydm();
////        final String nsrsbh = //cxssbSessionUtils.getNsrsbh();
////        final String nsrmc = //cxssbSessionUtils.getNsrmc();
//        if(GyUtils.isNull(djxh)||GyUtils.isNull(nsrsbh)||GyUtils.isNull(nsrmc)){
//            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
//        }
//
//        final String yhlx = yhlx_dm;//cxssbSessionUtils.getYhlx();
//        //自然人取前台传递机关，纳税人主管税务所科分局代码
//        String zgswjgdm = swjgdm;//cxssbSessionUtils.getFtsZgswskfjDm();
//        if(YHLX_ZRR.equals(yhlx)) {
//            if(!GyUtils.isNull(cchxwsnssbquerysbbxxReqVO.getCchxwsnssbOtherVO()) && !GyUtils.isNull(cchxwsnssbquerysbbxxReqVO.getCchxwsnssbOtherVO().getSwjgDm())) {
//                zgswjgdm = cchxwsnssbquerysbbxxReqVO.getCchxwsnssbOtherVO().getSwjgDm();
//            }else {
//                final CchxwsnssbbxxVO failVO = new CchxwsnssbbxxVO();
//                failVO.setReturnCode("-1");
//                failVO.setReturnMsg("选择机关信息缺失，请刷新页面重试!");
//                return success(failVO);
//            }
//        }
//        final CxstysbNsrxxVO nsrxxVO = new CxstysbNsrxxVO();
//        nsrxxVO.setDjxh(djxh);
//        nsrxxVO.setNsrsbh(GyUtils.isNull(shxydm)?nsrsbh:shxydm);
//        nsrxxVO.setNsrmc(nsrmc);
//        nsrxxVO.setZgswskfj_dm(zgswjgdm);
//        nsrxxVO.setYhlx(yhlx);
//        cchxwsnssbquerysbbxxReqVO.setNsrxxVO(nsrxxVO);
////        final CchxwsnssbbxxVO cchxwsnssbbxxVO = cchxwsnssbFeignClients.querySbbxx(cchxwsnssbquerysbbxxReqVO);
//        final String json = JacksonUtils.toJson(cchxwsnssbquerysbbxxReqVO);
//        final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/cchxwsnssb/v1/queryYqsbbxx",
//                "cxssb-service","post", InvokeGt4Constants.XTDM.XDJ,json,"");
//        CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
//        final CchxwsnssbbxxVO cchxwsnssbbxxVO = JsonUtils.toBean(data.getData(), CchxwsnssbbxxVO.class);
//        return success(cchxwsnssbbxxVO);
//    }
//
//    /**
//     * 查询已申报信息
//     * @param cchxwsYsbxxReqVo 入参
//     * @return 返回值
//     */
//    @PostMapping("/v1/queryYsbxx")
//    public CommonResult<List<CchxwsYsbxxVo>> queryYsbxx(@RequestBody CchxwsYsbxxReqVo cchxwsYsbxxReqVo){
//        //从当前登录人信息中取出登记序号，纳税人识别号和纳税人名称
////        final String djxh = //cxssbSessionUtils.getDjxh();
////        final String nsrsbh = //cxssbSessionUtils.getNsrsbh();
////        final String nsrmc = //cxssbSessionUtils.getNsrmc();
//        if(GyUtils.isNull(djxh)||GyUtils.isNull(nsrsbh)||GyUtils.isNull(nsrmc)){
//            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
//        }
//        //自然人取省级税务机关，纳税人主管税务所科分局代码
////        final String zgswjgdm = //cxssbSessionUtils.getFtsZgswskfjDm();
//
//        cchxwsYsbxxReqVo.setDjxh(djxh);
//        cchxwsYsbxxReqVo.setZgswskfjDm(zgswjgdm);
////        final List<CchxwsYsbxxVo> cchxwsYsbxxVoList = cchxwsnssbFeignClients.queryYsbxx(cchxwsYsbxxReqVo);
//        final String json = JacksonUtils.toJson(cchxwsYsbxxReqVo);
//        final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/cchxwsnssb/v1/queryYsbxx",
//                "cxssb-service","post", InvokeGt4Constants.XTDM.XDJ,json,"");
//        CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
//        final List<CchxwsYsbxxVo> cchxwsYsbxxVoList = JsonUtils.toList(data.getData(), CchxwsYsbxxVo.class);
//        return success(cchxwsYsbxxVoList);
//    }
//
//    /**
//     * 保存房土异步申报
//     * @param cchxwsnssbbxxVO 入参
//     * @return 返回值
//     */
//    @PostMapping("/v1/ftsaveSbbxx")
//    public CommonResult<Map<String,Object>> ftsaveSbbxx(@RequestBody CchxwsnssbbxxVO cchxwsnssbbxxVO){
//        //从当前登录人信息中取出登记序号，纳税人识别号和纳税人名称
////        final String djxh = //cxssbSessionUtils.getDjxh();
////        final String shxydm = //cxssbSessionUtils.getShxydm();
////        final String nsrsbh = //cxssbSessionUtils.getNsrsbh();
////        final String nsrmc = //cxssbSessionUtils.getNsrmc();
//        if(GyUtils.isNull(djxh)||GyUtils.isNull(nsrsbh)||GyUtils.isNull(nsrmc)){
//            return CommonResult.error(-1,"纳税人登记不能为空，请核实!");
//        }
//        //自然人取省级税务机关，纳税人主管税务所科分局代码
////        final String zgswjgdm = //cxssbSessionUtils.getFtsZgswskfjDm();
////        //用户类型 0 税务人 1 自然人2 企业身份
//        final String yhlx = yhlx_dm;//cxssbSessionUtils.getYhlx();
//
//        final CxstysbNsrxxVO cxstysbNsrxxVO = new CxstysbNsrxxVO();
//        cxstysbNsrxxVO.setDjxh(djxh);
//        cxstysbNsrxxVO.setNsrsbh(GyUtils.isNull(shxydm)?nsrsbh:shxydm);
//        cxstysbNsrxxVO.setNsrmc(nsrmc);
//        cxstysbNsrxxVO.setZgswskfj_dm(zgswjgdm);
//        cxstysbNsrxxVO.setYhlx(yhlx);
//        cchxwsnssbbxxVO.setCxstysbNsrxxVO(cxstysbNsrxxVO);
//        //组装受理信息
//        final CchxwsnssbSlxxVO slxxVO = new CchxwsnssbSlxxVO();
//        slxxVO.setJbr(userName);//cxssbSessionUtils.getUserName());
//        slxxVO.setJbrsfzjhm(nsrzjhm);//cxssbSessionUtils.getNsrzjhm());
//        cchxwsnssbbxxVO.setCchxwsnssbSlxxVO(slxxVO);
////        final Map<String,Object> returnMap = cchxwsnssbFeignClients.ftsaveSbbxx(cchxwsnssbbxxVO);
//        final String json = JacksonUtils.toJson(cchxwsnssbbxxVO);
//        final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/cchxwsnssb/v1/ftsaveSbbxx",
//                "cxssb-service","post", InvokeGt4Constants.XTDM.XDJ,json,"");
//        CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
//        final Map<String,Object> returnMap = JsonUtils.toBean(data.getData(), Map.class);
//        return success(returnMap);
//    }
//
//    /**
//     * 获取简易处罚任务状态
//     * @param getjycfRwztReqVo 入参
//     * @return 返回值
//     */
//    @PostMapping("/v1/getjycfRwzt")
//    public CommonResult<ResponseDTO> getjycfRwzt(@RequestBody GetjycfRwztReqVo getjycfRwztReqVo){
////        final ResponseDTO responseDTO =  cchxwsnssbFeignClients.getjycfRwzt(getjycfRwztReqVo);
//        final String json = JacksonUtils.toJson(getjycfRwztReqVo);
//        final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/cchxwsnssb/v1/getjycfRwzt",
//                "cxssb-service","post", InvokeGt4Constants.XTDM.XDJ,json,"");
//        CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
//        final ResponseDTO responseDTO = JsonUtils.toBean(data.getData(), ResponseDTO.class);
//        return success(responseDTO);
//    }
//
    /**
     * 获取纳税人认定资格
     * @return 返回值
     */
    @PostMapping("/v1/getNsrRdzg")
    public CommonResult<String> getNsrRdzg(){
//        final String djxh = djxh;//cxssbSessionUtils.getDjxh();
//        final String params = JacksonUtils.toJson(djxh);
//        final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/cchxwsnssb/v1/getNsrRdzg",
//                "cxssb-service","get", InvokeGt4Constants.XTDM.XDJ,"",params);
//        CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
        return success("Y");
    }

    /**
     * 获取财行税税源明细
     * @param cxsSymxReqVO cxsSymxReqVO
     * @return 返回值
     */
    @PostMapping("/v1/getCxsSymx")
    public CommonResult<CxsSymxResVO> getCxsSymx(@RequestBody CxsSymxReqVO cxsSymxReqVO){
        final String djxh = ZnsbSessionUtils.getDjxh();
        cxsSymxReqVO.setDjxh(djxh);
//        final String json = JacksonUtils.toJson(cxsSymxReqVO);
//        final InvokeGt4RequestDTO requestDTO = InvokeGt4Utils.createDTO("/cxssbService/cchxwsnssb/v1/getCxsSymx",
//                "cxssb-service","post", InvokeGt4Constants.XTDM.XDJ,json,"");
//        CommonResult<String> data = invokeGt4ServiceApi.invokeGt4Service(requestDTO);
////        return cchxwsnssbFeignClients.getCxsSymx(cxsSymxReqVO);
//        CxsSymxResVO result = JsonUtils.toBean(data.getData(), CxsSymxResVO.class);

        //入参检验
        final StringBuffer messageBuffer = new StringBuffer();
        if (GyUtils.isNull(cxsSymxReqVO.getZsxmDm())) {
            messageBuffer.append("入参征收项目不能为空，请核实后处理。");
        }
        if (GyUtils.isNull(cxsSymxReqVO.getCxsSySbxxVOList())) {
            messageBuffer.append("入参申报信息不能为空，请核实后处理。");
        }
        if (GyConstants.getDmGyZsxmCztdsys().equals(cxsSymxReqVO.getZsxmDm())) {
            if (GyUtils.isNull(cxsSymxReqVO.getCxsSySkxxVOList())) {
                messageBuffer.append("入参税款信息不能为空，请核实后处理。");
            }
        }
        if (GyConstants.getDmGyZsxmFcs().equals(cxsSymxReqVO.getZsxmDm())) {
            if (GyUtils.isNull(cxsSymxReqVO.getCxsSyCjxxVOList()) && GyUtils.isNull(cxsSymxReqVO.getCxsSyCzxxVOList())) {
                messageBuffer.append("入参税款信息不能为空，请核实后处理。");
            }
        }
        final String message = messageBuffer.toString();
        if(!GyUtils.isNull(message)){
            final CxsSymxResVO failResVO = new CxsSymxResVO();
            failResVO.setReturnCode("9");
            failResVO.setReturnMsg(message);
            return CommonResult.success(failResVO);
        }
        final CxsSymxResVO cxsSymxResVO = cchxwsnssbService.getCxsSymx(cxsSymxReqVO);
        return CommonResult.success(cxsSymxResVO);
    }


    @PostMapping("/v1/startDataCollectionByID")
    public CommonResult<String> startDataCollectionByID(String id){
        sjcjService.startDataCollectionByID(id);
        return success("Y");
    }


    @PostMapping("/v1/startDataCollectionTableByName")
    public CommonResult<String> startDataCollectionTableByName(String tableName){
        sjcjService.startDataCollectionTableByName(tableName);
        return success("Y");
    }

    @PostMapping("/v1/dmbSql")
    public String dmbSql(String json,String tableName) {
        final Map<String, Object> dataMap = CacheUtils.getTableData(SjcjConstants.CACHE_SJCJ_RW_CSDMB, tableName);
        String extendInfo = (String) dataMap.get("extendInfo");
        Map<String, Object>  extendInfoMap = JsonUtils.toMap(extendInfo);
        final List<Map<String, Object>> dataList = getDataList(extendInfoMap, json.replace("'",""));

        String insertSqlTemp = (String) extendInfoMap.get("insertSql");
        List<String> sqlList = new ArrayList<>();
        for (final Map<String, Object> dataMap1 : dataList) {
            // 替换uuid键的值
            if (dataMap1.containsKey("uuid")) {
                dataMap1.put("uuid", GyUtils.getUuid());
            }
            final String sql = TemplateUtils.parseContent(insertSqlTemp, dataMap1);
            sqlList.add(sql);
        }

        return StringUtils.join(sqlList, ";\n");
    }

    private static List<Map<String, Object>> getDataList(Map<String, Object> extendInfoDTO, String msg) {
        final List<Map<String, Object>> dataList = JsonUtils.toMapList(msg);

        TypeUtils.convertKey(dataList, true, KeyCoverters.LOWER_TRIM_CONVERTER);

        if (GyUtils.isNull(dataList)) {
            return null;
        }

        if (GyUtils.isNotNull(extendInfoDTO.get("insertSql"))) {
            return dataList;
        }

        assert dataList != null;

        final String tableColumns = extendInfoDTO.get("dataColumns").toString();
        if (GyUtils.isNull(tableColumns)) {
            return dataList;
        }
        final String dataColumns = extendInfoDTO.get("tableColumns").toString();

        if (GyUtils.isNotNull(dataColumns)) {
            // 整理插入数据
            final String[] tableColumnArr = StrUtils.split(tableColumns, ",");
            final String[] dataColumnArr = StrUtils.split(dataColumns, ",");
            for (final Map<String, Object> dataMap : dataList) {
                processData(dataMap, tableColumnArr, dataColumnArr);
            }
        }

        return dataList;
    }

    private static void processData(Map<String, Object> dataMap, String[] tableColumns, String[] dataColumns) {
        if (GyUtils.isNull(tableColumns) || GyUtils.isNull(dataColumns)) {
            return;
        }

        for (int i = 0; i < tableColumns.length; i++) {
            final String name = tableColumns[i].trim();
            final String dataColumn = dataColumns[i].trim();
            String value = null;
            try {
                value = TemplateUtils.parseContent(dataColumn, dataMap);
            } catch (Exception ignore) {
                log.info(ignore.getMessage());
            }
            dataMap.put(name, value);
        }
    }

    @Operation(summary = "印花税生成预填")
    @PostMapping("/v1/yhsPlsb")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Object> yhsPlsb(@RequestBody YhscjNsrxxDTO nsrxxFormVO) {
        //查询申报表信息
        List<ZnsbNssbSbrwDO> znsbNssbSbrwDOList = znsbNssbSbrwMapper.getZnsbNssbSbrwFromYhs(nsrxxFormVO);
        cxsYsbsjcjService.saveOrUpdateYsbjgb(znsbNssbSbrwDOList.get(0));
        return CommonResult.success("1");
    }

    @Operation(summary = "cxsSuccessAfter")
    @PostMapping("/v1/cxsSuccessAfter")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Object> cxsSuccessAfter(@RequestBody Object obj) {
        ZnsbNssbSjjhDO sjjhDO = new ZnsbNssbSjjhDO();
        sjjhDO.setYwuuid("13cafb1ba9f7432aa9c2b3389e971bd6");
        CxsCxsjcljgResponseDTO responseDTO = BeanUtils.toBean(obj,CxsCxsjcljgResponseDTO.class);
        cxsSaveService.cxsSuccessAfter(sjjhDO,responseDTO);
        return CommonResult.success("1");
    }

    @Operation(summary = "发消息")
    @PostMapping("/v1/sendXx")
    public CommonResult<Object> sendXx(@RequestBody String json){
        final HXZGZS10283Response respDTO = JsonUtils.toBean(json, HXZGZS10283Response.class);

        final String djxh = "10115113000069417805";
        final String nsrsbh = "91511302791806982U";
        final String pzxh = "10014425000071661003";
        if (GyUtils.isNotNull(respDTO) && "00".equals(respDTO.getReturncode())) {
            HXZGZS10283Response.WzkjksGrid jksGrid = respDTO.getjksGrid();
            List<JkswzkxxVO> jksmxGrid= jksGrid.getJksGridlb();
            // 使用Stream API根据dzsphm分组jksmxGrid
            Map<String, List<JkswzkxxVO>> jksmxGridGroupByDzsphm = jksmxGrid.stream()
                    .collect(java.util.stream.Collectors.groupingBy(JkswzkxxVO::getDzsphm));

            for (Map.Entry<String, List<JkswzkxxVO>> entry : jksmxGridGroupByDzsphm.entrySet()) {
                // 查询是否已经存在缴款记录
                final List<JkswzkxxVO> jkxxVOList = new ArrayList<>();
                final List<JkswzkxxVO> jkswzkxxVOList = entry.getValue();
//                for(JkswzkxxVO jksmxVo:jkswzkxxVOList){
//                    // 查询是否已经存在缴款记录
//                    List<ZnsbNssbJkyzmxDO> jkyzmxDOList = znsbNssbJkyzmxMapper.queryRwmxList(djxh, jksmxVo.getYzpzxh());
//                    if (GyUtils.isNull(jkyzmxDOList)){
//                        continue;
//                    }
//                    if (jkyzmxDOList.get(0).getClztDm().equals("01")){
//                        continue;
//                    }
//                    jkxxVOList.add(jksmxVo);
//                }
                // 发送消息
                if (GyUtils.isNotNull(jkswzkxxVOList)){
                    this.sendXtxx(djxh, nsrsbh, jkswzkxxVOList);
                }
//                for(JkswzkxxVO jksmxVo:jkswzkxxVOList){
//                    log.info("SkjnBcclKafkaConsumer 补偿处理 根据缴款成功结果修改企业端应征 jksmxVo：" + JsonUtils.toJson(jksmxVo));
//                    final ZnsbNssbJkyzmxDO znsbnssbjkyzmxdo = new ZnsbNssbJkyzmxDO();
//                    BeanUtils.copyBean(jksmxVo, znsbnssbjkyzmxdo);
//                    znsbnssbjkyzmxdo.setDjxh(djxh);
//                    znsbnssbjkyzmxdo.setNssbrq(DateUtils.toDate(jksmxVo.getSbrq(), "yyyy-MM-dd"));
//                    znsbnssbjkyzmxdo.setSjje(GyUtils.cast2Str(jksmxVo.getSjse()));
//                    znsbnssbjkyzmxdo.setKjrq(DateUtils.toDate(jksmxVo.getJkrq(), "yyyy-MM-dd"));
//                    log.info("SkjnBcclKafkaConsumer 补偿处理 根据缴款成功结果修改企业端应征 znsbnssbjkyzmxdo ：" + JsonUtils.toJson(znsbnssbjkyzmxdo));
//                    znsbNssbJkyzmxMapper.updateJkyzmxByBccl(znsbnssbjkyzmxdo);
//                }
            }
        }
//        UpdateSbrwJkztReqDTO updateSbrwJkztReqDTO = new UpdateSbrwJkztReqDTO();
//        updateSbrwJkztReqDTO.setDjxh(djxh);
//        updateSbrwJkztReqDTO.setPzxh(pzxh);
//        log.info("SkjnBcclKafkaConsumer 修改申报任务 入参 登记序号：" + djxh + " 凭证序号：" + pzxh);
//        UpdateSbrwJkztRespDTO resSbrwDTO = znsbNssbSbrwService.updateSbrwJkzt(updateSbrwJkztReqDTO);
//        log.info("SkjnBcclKafkaConsumer 修改申报任务 成功" + JsonUtils.toJson(resSbrwDTO));

        return CommonResult.success("1");
    }


    private CommonResult<CommonResult<SaveXtxxResVO>> sendXtxx(String djxh, String nsrsbh, List<JkswzkxxVO> jksmxGrid) {
        //获取企业信息
        CommonResult<CompanyBasicInfoDTO> qyxxRes = companyApi.basicInfo(djxh, nsrsbh);
        List<String> jguuidList = new ArrayList<>();
        if (!GyUtils.isNull(qyxxRes.getData())) {
            log.info("获取到的企业信息 qyxxRes：" + JsonUtils.toJson(qyxxRes));
            jguuidList.add(qyxxRes.getData().getJguuid());
        } else {
            throw ServiceExceptionUtil.exception(500, "未查询到当前企业机构信息!");
        }
        // 消息主体
        SaveXtxxReqVO saveXtxxReqVO = new SaveXtxxReqVO();
        saveXtxxReqVO.setYwxtid("LXPT_ZNSB");
        saveXtxxReqVO.setYwxtfwkl("1980D5F999384736E063990C170AE6F4");
        saveXtxxReqVO.setLrrsfid("SYSTEM");
        saveXtxxReqVO.setXxmbbh("00000000000_00000026");
        saveXtxxReqVO.setXxTbMbbh("00000000000_00000024");
        final String ywzj = GyUtils.getUuid();
        // 消息头部信息
        List<XtxxCSVO> xxTbCsVOs = new ArrayList<>();
        XtxxCSVO xxHeadCsVO = new XtxxCSVO();
        xxHeadCsVO.setYwzj(ywzj);
        xxHeadCsVO.setJguuidList(jguuidList);
        //消息发送  begin  btxx,qydm,nsrmc,jkrq,sjje,skssqq,skssqz,zsxm
        final Map<String, Object> headMap = new HashMap<>();
        headMap.put("btxx", "缴款成功提醒");
        headMap.put("qydm", qyxxRes.getData().getQydmz());
        headMap.put("nsrmc", qyxxRes.getData().getNsrmc());
        headMap.put("jkrq", jksmxGrid.get(0).getJkrq());
        headMap.put("qyxxbt", "缴款成功提醒");
        headMap.put("qclurl", "/znsb/view/nssb/sbrwmx");
        xxHeadCsVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
        xxHeadCsVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)), 0));
        xxHeadCsVO.setXxyxj("9");

        BigDecimal hjjeNum = new BigDecimal("0");//合计数
        List<XtxxCSVO> xtxxCSVOList = new ArrayList<>();
        for(JkswzkxxVO jksmxVo : jksmxGrid){
            hjjeNum = hjjeNum.add(BigDecimal.valueOf(jksmxVo.getSjse()));
            XtxxCSVO xtxxCSVO = new XtxxCSVO();
            xtxxCSVO.setYwzj(ywzj);
            xtxxCSVO.setJguuidList(jguuidList);
            //消息发送  begin  btxx,qydm,nsrmc,jkrq,sjje,skssqq,skssqz,zsxm
            final Map<String, Object> jsonmap = new HashMap<>();
            jsonmap.put("skssqq", jksmxVo.getSkssqq());
            jsonmap.put("skssqz", jksmxVo.getSkssqz());
            jsonmap.put("zsxm", CacheUtils.dm2mc("dm_gy_zsxm", jksmxVo.getZsxmDm()));
            jsonmap.put("sjje", jksmxVo.getYbtse());
            xtxxCSVO.setXxcs(JsonUtils.toJson(jsonmap));
            xtxxCSVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
            xtxxCSVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)), 0));
            xtxxCSVO.setXxyxj("9");
            xtxxCSVOList.add(xtxxCSVO);
        }
        saveXtxxReqVO.setSfhb("Y");
        saveXtxxReqVO.setXtxxCsVOs(xtxxCSVOList);
        headMap.put("sjje", hjjeNum);
        xxHeadCsVO.setXxcs(JsonUtils.toJson(headMap));
        xxTbCsVOs.add(xxHeadCsVO);
        saveXtxxReqVO.setXxTbCsVOs(xxTbCsVOs);
        CommonResult<SaveXtxxResVO> result = xxtxAPI.saveXtxx(saveXtxxReqVO);
        return CommonResult.success(result);
    }

    @Operation(summary = "jkxxGj")
    @PostMapping("/v1/jkxxGj")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Object> jkxxGj() {
        znsbSkjnService.jkxxGj("10013101001120017369;2025-01-01;2025-04-30");
        return CommonResult.success("1");
    }

    @Operation(summary = "scAcSbrw")
    @PostMapping("/v1/scAcSbrw")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Object> scAcSbrw() {
        yhssycjNewService.scAcSbrw("Y");
        return CommonResult.success("1");
    }

    @Operation(summary = "用工号发消息")
    @PostMapping("/v1/sendXxByGh")
    public CommonResult<Object> sendXxByGh(){
        StringBuffer xxnr = new StringBuffer();
        xxnr.append("\r\n");
        xxnr.append("计税方式:");
        String jsfsmc = CacheUtils.dm2mc("dm_tzzx_jsfs","01");
        xxnr.append(jsfsmc);
        xxnr.append("，征税项目：");
        String zsxmmc = CacheUtils.dm2mc("dm_tzzx_zsxm1","01");
        xxnr.append(zsxmmc);
        xxnr.append("，税率:");
        xxnr.append("0%");
        xxnr.append("，未开具发票销售额为");
        xxnr.append("0");
        xxnr.append("元，小于等于");
        xxnr.append("10");
        xxnr.append("元。");
        xxnr.append("\r\n");
        SaveXtxxReqVO saveXtxxReqVO = new SaveXtxxReqVO();
        saveXtxxReqVO.setYwxtid("LXPT_ZNSB");
        saveXtxxReqVO.setYwxtfwkl("1980D5F999384736E063990C170AE6F4");
        saveXtxxReqVO.setLrrsfid("SYSTEM");
        saveXtxxReqVO.setXxmbbh("00000000000_00000011");
        List<XtxxCSVO> xtxxCSVOList = new ArrayList<>();
        XtxxCSVO xtxxCSVO = new XtxxCSVO();
        xtxxCSVO.setYwzj(GyUtils.getUuid());

        List<String> ghList = new ArrayList<>();
        ghList.add("lps001");
        ghList.add("wc001");
        xtxxCSVO.setGhList(ghList);

        CommonResult<CompanyBasicInfoDTO> qyxxRes = companyApi.basicInfo("10013101001120017369","91310112773731630K");
        if (!GyUtils.isNull(qyxxRes.getData())){
            List<String> jguuidList = new ArrayList<>();
            jguuidList.add(qyxxRes.getData().getJguuid());
            xtxxCSVO.setJguuidList(jguuidList);
        }else{
            throw ServiceExceptionUtil.exception(500, "未查询到当前企业机构信息!");
        }

        //组装消息模板报文参数
        final Map<String,Object> jsonmap = new HashMap<>();
        jsonmap.put("wkptxbt","增值税一般人申报销项台账未开票收入疑点提醒");
        jsonmap.put("nsrmc", "1111");
        Calendar tsDate = Calendar.getInstance();
        tsDate.add(Calendar.DAY_OF_MONTH,-10);
        String sszq = DateUtil.doDateFormat(tsDate.getTime(),"yyyy-MM");
        jsonmap.put("sszq", sszq);
        jsonmap.put("xxnr", xxnr);
        String gnyrl = "/znsb/view/tzzx/zzstz?jguuid=f7cbe421265240648183ed507868fe9d";
        jsonmap.put("gnurl",gnyrl);
        xtxxCSVO.setXxcs(JsonUtils.toJson(jsonmap));
        xtxxCSVO.setXxyxqq(DateUtils.getSystemCurrentTime(0));
        xtxxCSVO.setXxyxqz(DateUtils.toDateStrByFormatIndex(DateUtils.toDate(LocalDate.now().plusDays(1)),0));
        xtxxCSVO.setXxyxj("9");
        xtxxCSVOList.add(xtxxCSVO);
        saveXtxxReqVO.setXtxxCsVOs(xtxxCSVOList);
        CommonResult<SaveXtxxResVO> result = xxtxAPI.saveXtxx(saveXtxxReqVO);
        return CommonResult.success(result);
    }


    @Operation(summary = "用工号发消息")
    @PostMapping("/v1/sendXxByGh1")
    public CommonResult<Object> sendXxByGh1(@RequestBody String json){
//        StringBuffer xxnr = new StringBuffer();
//        xxnr.append("\r\n");
//        xxnr.append("计税方式:");
//        String jsfsmc = CacheUtils.dm2mc("dm_tzzx_jsfs","01");
//        xxnr.append(jsfsmc);
//        xxnr.append("，征税项目：");
//        String zsxmmc = CacheUtils.dm2mc("dm_tzzx_zsxm1","01");
//        xxnr.append(zsxmmc);
//        xxnr.append("，税率:");
//        xxnr.append("0%");
//        xxnr.append("，未开具发票销售额为");
//        xxnr.append("0");
//        xxnr.append("元，小于等于");
//        xxnr.append("10");
//        xxnr.append("元。");
//        xxnr.append("\r\n");
//
//        List<String> ghList = new ArrayList<>();
//        ghList.add("lps001");
//        ghList.add("wc001");
//
        SendDdxxReqVO sendDdxxReqVO = new SendDdxxReqVO();
//        sendDdxxReqVO.setYwxtid("FPXT");
//        sendDdxxReqVO.setLrrsfid("SYSTEM");
//
//        List<XxVO> xxVOList = new ArrayList<>();
//        XxVO xxVO = new XxVO();
//        xxVO.setXxnr(xxnr.toString());
//        xxVO.setGhList(ghList);
//        xxVOList.add(xxVO);
        sendDdxxReqVO = BeanUtils.toBean(json, SendDdxxReqVO.class);
        CommonResult<SendDdxxResVO> result = xxtxAPI.sendDdxxByGh(sendDdxxReqVO);
        return CommonResult.success(result);
    }

    @Operation(summary = "缴款补偿")
    @PostMapping("/v1/jkbc")
    public CommonResult<Object> jkbc(){
        znsbSkjnService.jkbc("");
        return CommonResult.success("1");
    }

    @Operation(summary = "getNsrxxByNsrsbh")
    @PostMapping("/v1/getNsrxxByNsrsbh")
    public CommonResult<Object> getNsrxxByNsrsbh(@RequestParam("djxh") String djxh,@RequestParam("nsrsnh") String nsrsnh){
        CommonResult<ZnsbMhzcQyjbxxmxResVO> qyjbxxmx;
        ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsnh);
        qyjbxxmx = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        return CommonResult.success(qyjbxxmx);
    }

    @PostMapping("/v1/downloadSbbFile")
    @Operation(summary = "申报信息报表下载")
    public void downloadSbbFile(@RequestParam("sbuuid")  String sbuuid,@RequestParam("yzpzzlDm")  String yzpzzlDm, HttpServletResponse response)
            throws IOException {
        List<SbxxcxPdfZipVO> pdfZipList = new ArrayList<>();
        String pdfFileNameInZip = "test.pdf";
        byte[] wjnr;
        wjnr = makePdf(sbuuid,yzpzzlDm);
        if (GyUtils.isNull(wjnr)){
            throw new ServiceException(2_001_001_001, "下载失败，部分申报表无数据，请同步报表后操作。");
        }
        ZnsbFileUtils.upload(wjnr, sbuuid, "application/pdf");
        pdfZipList.add(new SbxxcxPdfZipVO(pdfFileNameInZip, wjnr));
        log.info("查询到minio文件二进制数组：是否为空{}", GyUtils.isNull(wjnr));

        if (GyUtils.isNotNull(pdfZipList)){
            String zipFileName = "申报表.zip";
            // 设置文件类型
            response.setContentType("application/zip");
            // 设置编码格式
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            ZipOutputStream zos = new ZipOutputStream(outputStream);
            for (SbxxcxPdfZipVO pdfZipVO : pdfZipList){
                zos.putNextEntry(new ZipEntry(pdfZipVO.getPdfName()));
                IoUtil.write(zos, false, pdfZipVO.getWjnr());
                zos.closeEntry();
            }
            zos.flush();
            zos.close();
            outputStream.close();
        }else {
            throw new ServiceException(2_001_001_001, "下载失败，部分申报表无数据，请同步报表后操作。");
        }
    }

    @Operation(summary = "财行税生成预填")
    @PostMapping("/v1/handlerYsbsj")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Object> handlerYsbsj(@RequestParam("sbrwuuid") String sbrwuuid) {
        //查询申报表信息
        ZnsbNssbSbrwDO sbrwDO = znsbNssbSbrwMapper.querySbrwBySbrwuuid(sbrwuuid);
        cxsYsbsjcjService.saveOrUpdateYsbjgb(sbrwDO);
        return CommonResult.success("1");
    }

    private byte[] makePdf(String sbuuid, String yzpzzlDm) {
        try {
            final String byte64 = sbxxCxService.printPdfBbxz(sbuuid, yzpzzlDm);
            if (GyUtils.isNotNull(byte64)){
                return SbPrintUtils.decode(byte64);
            }else {
                return null;
            }
        }catch (Exception e){
            throw new ServiceException(2_001_001_001, "下载失败，部分申报表无数据，请同步报表后操作。");
        }
    }



}
