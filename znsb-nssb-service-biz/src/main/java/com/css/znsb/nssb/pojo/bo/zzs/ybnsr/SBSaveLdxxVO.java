package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 申报保存留底数据信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBSaveLdxxVO", propOrder = { "sqldpzxh" })
@Getter
@Setter
public class SBSaveLdxxVO {
    /**
     * 上期留抵凭证序号
     */
    protected String sqldpzxh;
}