package com.css.znsb.nssb.pojo.bo.hxzg.sb000;


import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;

/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.sbzs.sb.sb000
 * @file SBSbbwVO.java 创建时间:2014-8-9下午11:23:46
 * @title 申报表尾VO
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class SBSbbwVO extends TaxBaseVO {
    
    
    /**
     * @description 序列ID
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 6809215230466987924L;
    
    private String bsrxm;//办税人姓名
    private String dljbrzyzjhm;//代理经办人执业证件号码
    private String cwfzrxm;//财务负责人姓名
    private String fddbrxm;//法定代表人姓名
    private String swdlrdz;//税务代理人地址
    private String swdlrlxdh;//税务代理人联系电话
    public String getBsrxm() {
        return bsrxm;
    }
    public void setBsrxm(String bsrxm) {
        this.bsrxm = bsrxm;
    }
    public String getDljbrzyzjhm() {
        return dljbrzyzjhm;
    }
    public void setDljbrzyzjhm(String dljbrzyzjhm) {
        this.dljbrzyzjhm = dljbrzyzjhm;
    }
    public String getCwfzrxm() {
        return cwfzrxm;
    }
    public void setCwfzrxm(String cwfzrxm) {
        this.cwfzrxm = cwfzrxm;
    }
    public String getFddbrxm() {
        return fddbrxm;
    }
    public void setFddbrxm(String fddbrxm) {
        this.fddbrxm = fddbrxm;
    }
    public String getSwdlrdz() {
        return swdlrdz;
    }
    public void setSwdlrdz(String swdlrdz) {
        this.swdlrdz = swdlrdz;
    }
    public String getSwdlrlxdh() {
        return swdlrlxdh;
    }
    public void setSwdlrlxdh(String swdlrlxdh) {
        this.swdlrlxdh = swdlrlxdh;
    }
    
    
}
