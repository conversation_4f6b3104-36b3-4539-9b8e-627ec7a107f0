package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《铁路运输企业分支机构增值税汇总纳税信息传递单》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_tlysqyfzjgzzshznsxxcddbw", propOrder = { "zzsybnsrsbTlysqyfzjgzzshznsxxcdd" })
@Getter
@Setter
public class ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw extends TaxDoc {
    /**
     * 《铁路运输企业分支机构增值税汇总纳税信息传递单》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_tlysqyfzjgzzshznsxxcdd", required = true)
    @JSONField(name = "zzsybnsrsb_tlysqyfzjgzzshznsxxcdd")
    protected ZzsybnsrsbTlysqyfzjgzzshznsxxcdd zzsybnsrsbTlysqyfzjgzzshznsxxcdd;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzsybnsrsbTlysqyfzjgzzshznsxxcdd extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzsybnsrsbTlysqyfzjgzzshznsxxcdd {}
}