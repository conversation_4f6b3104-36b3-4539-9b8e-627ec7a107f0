//package com.css.znsb.nssb.dao.hxzg.hbs;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.css.znsb.framework.common.util.date.DateUtils;
//import com.css.znsb.framework.common.util.object.BeanUtils;
//import com.css.znsb.framework.common.util.znsb.GyUtils;
//import com.css.znsb.framework.session.ZnsbSessionUtils;
//import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxstysbxxMapper;
//import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjCycsMapper;
//import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjDqsMapper;
//import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjGfMapper;
//import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbSbCxsHbsSbjscjZsMapper;
//import com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxstysbxxDO;
//import com.css.znsb.nssb.pojo.domain.hbs.ZnsbSbCxsHbsSbjscjCycsDO;
//import com.css.znsb.nssb.pojo.domain.hbs.ZnsbSbCxsHbsSbjscjDqsDO;
//import com.css.znsb.nssb.pojo.domain.hbs.ZnsbSbCxsHbsSbjscjGfDO;
//import com.css.znsb.nssb.pojo.domain.hbs.ZnsbSbCxsHbsSbjscjZsDO;
//import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.SbxxGridlbVO;
//import com.css.znsb.nssb.pojo.vo.hxzg.sb10812.CycsSbjscj;
//import com.css.znsb.nssb.pojo.vo.hxzg.sb10812.DqsSbjscj;
//import com.css.znsb.nssb.pojo.vo.hxzg.sb10812.GfSbjscj;
//import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HbssbjsNsrxxVO;
//import com.css.znsb.nssb.pojo.vo.hxzg.sb10812.SbjsVO;
//import com.css.znsb.nssb.pojo.vo.hxzg.sb10812.ZsSbjscj;
//import com.css.znsb.nssb.utils.GYCastUtils;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.HashSet;
//import java.util.List;
//
//@Component(value = "SB665HjbhjssyxxqzcjDao")
//public class SB665HjbhjssyxxqzcjDao {
//    @Resource
//    ZnsbSbCxsHbsSbjscjDqsMapper sbjscjDqsMapper;
//    @Resource
//    ZnsbSbCxsHbsSbjscjGfMapper sbjscjGfMapper;
//    @Resource
//    ZnsbSbCxsHbsSbjscjZsMapper sbjscjZsMapper;
//    @Resource
//    ZnsbSbCxsHbsSbjscjCycsMapper sbjscjCycsMapper;
//    @Resource
//    ZnsbNssbCxstysbxxMapper cxstysbxxMapper;
//
//    /**
//     * @param nsrxxVO nsrxxVO
//     * @return SbjsVO
//     * @name 查询申报计算及减免信息
//     * @description 相关说明
//     * @time 创建时间:2020-08-19 上午 10:14:19
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public SbjsVO querySbjsVO(HbssbjsNsrxxVO nsrxxVO){
//        final SbjsVO sbjsVO = new SbjsVO();
//        final String djxh = nsrxxVO.getDjxh();
//        final String sbsxDm1 = nsrxxVO.getSbsxDm1();
//        final Date skssqq = DateUtils.parseDate(nsrxxVO.getSkssqq(), 3);
//        final Date skssqz = DateUtils.parseDate(nsrxxVO.getSkssqz(), 3);
//        final List<DqsSbjscj> dqsSbjscjs = this.sbjscjDqsMapper.querydqssbjscj(djxh,"Y","Y",sbsxDm1,skssqq,skssqz);
//        sbjsVO.setDqswrGrid(new SbjsVO.DqswrGrid());
//        sbjsVO.getDqswrGrid().getDqswrGridLb().addAll(dqsSbjscjs);
//        final List<GfSbjscj> gfSbjscjs = this.sbjscjGfMapper.querygfsbjscj(djxh,"Y","Y",sbsxDm1,skssqq,skssqz);
//        sbjsVO.setGfGrid(new SbjsVO.GfGrid());
//        sbjsVO.getGfGrid().getGfGridLb().addAll(gfSbjscjs);
//        final List<ZsSbjscj> zsSbjscjs = this.sbjscjZsMapper.queryzssbjscj(djxh,"Y",sbsxDm1,skssqq,skssqz);
//        sbjsVO.setZsGrid(new SbjsVO.ZsGrid());
//        sbjsVO.getZsGrid().getZsGridLb().addAll(zsSbjscjs);
//        final List<CycsSbjscj> cycsSbjscjs = this.sbjscjCycsMapper.querycycssbjscj(djxh,"Y","Y",sbsxDm1,skssqq,skssqz);
//        sbjsVO.setCycsGrid(new SbjsVO.CycsGrid());
//        sbjsVO.getCycsGrid().getCycsGridLb().addAll(cycsSbjscjs);
//        if (skssqq.equals(skssqz)) {
//            final List<CycsSbjscj> acSbjscjs = this.sbjscjCycsMapper.queryhbssbjsac(djxh,"Y","N",nsrxxVO.getSbsxDm1(),DateUtils.parseDate(nsrxxVO.getSkssqq(), 3),DateUtils.parseDate(nsrxxVO.getSkssqz(), 3));
//            sbjsVO.getCycsGrid().getCycsGridLb().addAll(acSbjscjs);
//        }
//        return sbjsVO;
//    }
//    /**
//     * @param sbjsUuidSet sbjsUuidSet
//     * @param nsrxxVO     nsrxxVO
//     * @return java.util.List<gov.gt3.vo.sbzs.sb.sb666.SbxxGridlbVO>
//     * @name 查询申报信息
//     * @description 根据syuuid查询财行税通用申报信息表
//     * @time 创建时间:2020/10/28 17:09
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public List<SbxxGridlbVO> queryCxsSbxx(HashSet<String> sbjsUuidSet, HbssbjsNsrxxVO nsrxxVO){
//        if (GyUtils.isNull(sbjsUuidSet)) {
//            return new ArrayList<SbxxGridlbVO>();
//        }
//        final List<ZnsbNssbCxstysbxxDO> sbxxGridlbVOS = this.cxstysbxxMapper.queryListBySkssq(GYCastUtils.cast2Date(nsrxxVO.getSkssqq()),GYCastUtils.cast2Date(nsrxxVO.getSkssqz()),"N");
//
//        return BeanUtils.toBean(sbxxGridlbVOS,SbxxGridlbVO.class);
//    }
//    /**
//     * @param insertSbjsVO insertSbjsVO
//     * @param updateSbjsVO updateSbjsVO
//     * @param deleteSbjsVO deleteSbjsVO
//     * @return String
//     * @name 保存申报计算及减免信息
//     * @description 相关说明
//     * @time 创建时间:2020-08-19 上午 10:14:19
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public String saveSbjsVO(com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.sb10813.SbjsVO insertSbjsVO, com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.sb10813.SbjsVO updateSbjsVO, com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.sb10813.SbjsVO deleteSbjsVO){
//        if (!GyUtils.isNull(insertSbjsVO)) {
//            saveInsertSbjs(insertSbjsVO);//insert
//        }
//        if (!GyUtils.isNull(updateSbjsVO)) {
//            saveUpdateSbjs(updateSbjsVO);//update
//        }
//        if (!GyUtils.isNull(deleteSbjsVO)) {
//            saveDeleteSbjs(deleteSbjsVO);//zuofei
//        }
//        return null;
//    }
//    /**
//     * @param insertSbjsVO insertSbjsVO
//     * @name 新增申报计算及减免信息
//     * @description 相关说明
//     * @time 创建时间:2020-08-19 上午 10:14:19
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private void saveInsertSbjs(com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.sb10813.SbjsVO insertSbjsVO){
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.DqsSbjscj> insertDqs = !GyUtils.isNull(insertSbjsVO.getDqswrGrid()) ? insertSbjsVO.getDqswrGrid().getDqswrGridLb() : new ArrayList<com.css.znsb.nssb.pojo.vo.hbs.hxzg.DqsSbjscj>();//防止服务报文测试时报空指针异常
//        if (!GyUtils.isNull(insertDqs)) {
//            List<ZnsbSbCxsHbsSbjscjDqsDO> idqsPO= BeanUtils.toBean(insertDqs,ZnsbSbCxsHbsSbjscjDqsDO.class);
//            for (ZnsbSbCxsHbsSbjscjDqsDO po : idqsPO) {
//                po.setLrrDm("lrr");
//                po.setLrrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                po.setUuid(GyUtils.getUuid());
//                po.setYxbz("Y");
//                this.sbjscjDqsMapper.insert(po);
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.GfSbjscj> insertGf = !GyUtils.isNull(insertSbjsVO.getGfGrid()) ? insertSbjsVO.getGfGrid().getGfGridLb() : new ArrayList<com.css.znsb.nssb.pojo.vo.hbs.hxzg.GfSbjscj>();
//        if (!GyUtils.isNull(insertGf)) {
//            final List<ZnsbSbCxsHbsSbjscjGfDO> igfPO = BeanUtils.toBean(insertGf, ZnsbSbCxsHbsSbjscjGfDO.class);
//            for (ZnsbSbCxsHbsSbjscjGfDO po : igfPO) {
//                po.setLrrDm("lrr");
//                po.setLrrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                po.setUuid(GyUtils.getUuid());
//                po.setYxbz("Y");
//                this.sbjscjGfMapper.insert(po);
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.ZsSbjscj> insertZs = !GyUtils.isNull(insertSbjsVO.getZsGrid()) ? insertSbjsVO.getZsGrid().getZsGridLb() : new ArrayList<com.css.znsb.nssb.pojo.vo.hbs.hxzg.ZsSbjscj>();
//        if (!GyUtils.isNull(insertZs)) {
//            final List<ZnsbSbCxsHbsSbjscjZsDO> izsPO = BeanUtils.toBean(insertZs, ZnsbSbCxsHbsSbjscjZsDO.class);
//            for (ZnsbSbCxsHbsSbjscjZsDO po : izsPO) {
//                po.setLrrDm("lrr");
//                po.setLrrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                po.setUuid(GyUtils.getUuid());
//                po.setYxbz("Y");
//                this.sbjscjZsMapper.insert(po);
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.CycsSbjscj> insertCycs = !GyUtils.isNull(insertSbjsVO.getCycsGrid()) ? insertSbjsVO.getCycsGrid().getCycsGridLb() : new ArrayList<com.css.znsb.nssb.pojo.vo.hbs.hxzg.CycsSbjscj>();
//        if (!GyUtils.isNull(insertCycs)) {
//            final List<ZnsbSbCxsHbsSbjscjCycsDO> icycsPO = BeanUtils.toBean(insertCycs, ZnsbSbCxsHbsSbjscjCycsDO.class);
//            for (ZnsbSbCxsHbsSbjscjCycsDO po : icycsPO) {
//                po.setLrrDm("lrr");
//                po.setLrrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                po.setUuid(GyUtils.getUuid());
//                po.setYxbz("Y");
//                this.sbjscjCycsMapper.insert(po);
//            }
//        }
//    }
//    /**
//     * @param updateSbjsVO updateSbjsVO
//     * @name 更新申报计算及减免信息
//     * @description 相关说明
//     * @time 创建时间:2020-08-19 上午 10:14:19
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private void saveUpdateSbjs(com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.sb10813.SbjsVO updateSbjsVO){
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.DqsSbjscj> updateDqs = updateSbjsVO.getDqswrGrid().getDqswrGridLb();
//        if (!GyUtils.isNull(updateDqs)) {
//            List<ZnsbSbCxsHbsSbjscjDqsDO> udqsPO= BeanUtils.toBean(updateDqs,ZnsbSbCxsHbsSbjscjDqsDO.class);
//            for (ZnsbSbCxsHbsSbjscjDqsDO po : udqsPO) {
//                po.setXgrDm("xgr");
//                po.setXgrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                this.sbjscjDqsMapper.update(po,new QueryWrapper<ZnsbSbCxsHbsSbjscjDqsDO>().lambda().eq(ZnsbSbCxsHbsSbjscjDqsDO::getUuid,po.getUuid()));
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.GfSbjscj> updateGf = updateSbjsVO.getGfGrid().getGfGridLb();
//        if (!GyUtils.isNull(updateGf)) {
//            final List<ZnsbSbCxsHbsSbjscjGfDO> ugfPO = BeanUtils.toBean(updateGf, ZnsbSbCxsHbsSbjscjGfDO.class);
//            for (ZnsbSbCxsHbsSbjscjGfDO po : ugfPO) {
//                po.setXgrDm("xgr");
//                po.setXgrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                this.sbjscjGfMapper.update(po,new QueryWrapper<ZnsbSbCxsHbsSbjscjGfDO>().lambda().eq(ZnsbSbCxsHbsSbjscjGfDO::getUuid,po.getUuid()));
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.ZsSbjscj> updateZs = updateSbjsVO.getZsGrid().getZsGridLb();
//        if (!GyUtils.isNull(updateZs)) {
//            final List<ZnsbSbCxsHbsSbjscjZsDO> ugfPO = BeanUtils.toBean(updateZs, ZnsbSbCxsHbsSbjscjZsDO.class);
//            for (ZnsbSbCxsHbsSbjscjZsDO po : ugfPO) {
//                po.setXgrDm("xgr");
//                po.setXgrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                this.sbjscjZsMapper.update(po,new QueryWrapper<ZnsbSbCxsHbsSbjscjZsDO>().lambda().eq(ZnsbSbCxsHbsSbjscjZsDO::getUuid,po.getUuid()));
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.CycsSbjscj> updateCycs = updateSbjsVO.getCycsGrid().getCycsGridLb();
//        if (!GyUtils.isNull(updateCycs)) {
//            final List<ZnsbSbCxsHbsSbjscjCycsDO> ugfPO = BeanUtils.toBean(updateCycs, ZnsbSbCxsHbsSbjscjCycsDO.class);
//            for (ZnsbSbCxsHbsSbjscjCycsDO po : ugfPO) {
//                po.setXgrDm("xgr");
//                po.setXgrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                this.sbjscjCycsMapper.update(po,new QueryWrapper<ZnsbSbCxsHbsSbjscjCycsDO>().lambda().eq(ZnsbSbCxsHbsSbjscjCycsDO::getUuid,po.getUuid()));
//            }
//        }
//    }
//    /**
//     * @param deleteSbjsVO deleteSbjsVO
//     * @name 作废申报计算及减免信息
//     * @description 相关说明
//     * @time 创建时间:2020-08-19 上午 10:14:19
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private void saveDeleteSbjs(com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.sb10813.SbjsVO deleteSbjsVO) {
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.DqsSbjscj> updateDqs = deleteSbjsVO.getDqswrGrid().getDqswrGridLb();
//        if (!GyUtils.isNull(updateDqs)) {
//            final List<ZnsbSbCxsHbsSbjscjDqsDO> udqsPO = BeanUtils.toBean(updateDqs, ZnsbSbCxsHbsSbjscjDqsDO.class);
//            for (ZnsbSbCxsHbsSbjscjDqsDO po : udqsPO) {
//                po.setXgrDm("xgr");
//                po.setXgrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                po.setYxbz("N");
//                this.sbjscjDqsMapper.update(po,new QueryWrapper<ZnsbSbCxsHbsSbjscjDqsDO>().lambda().eq(ZnsbSbCxsHbsSbjscjDqsDO::getUuid,po.getUuid()));
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.GfSbjscj> updateGf = deleteSbjsVO.getGfGrid().getGfGridLb();
//        if (!GyUtils.isNull(updateGf)) {
//            final List<ZnsbSbCxsHbsSbjscjGfDO> ugfPO = BeanUtils.toBean(updateGf, ZnsbSbCxsHbsSbjscjGfDO.class);
//            for (ZnsbSbCxsHbsSbjscjGfDO po : ugfPO) {
//                po.setXgrDm("xgr");
//                po.setXgrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                po.setYxbz("N");
//                this.sbjscjGfMapper.update(po,new QueryWrapper<ZnsbSbCxsHbsSbjscjGfDO>().lambda().eq(ZnsbSbCxsHbsSbjscjGfDO::getUuid,po.getUuid()));
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.ZsSbjscj> updateZs = deleteSbjsVO.getZsGrid().getZsGridLb();
//        if (!GyUtils.isNull(updateZs)) {
//            final List<ZnsbSbCxsHbsSbjscjZsDO> uzsPO = BeanUtils.toBean(updateZs, ZnsbSbCxsHbsSbjscjZsDO.class);
//            for (ZnsbSbCxsHbsSbjscjZsDO po : uzsPO) {
//                po.setXgrDm("xgr");
//                po.setXgrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                po.setYxbz("N");
//                this.sbjscjZsMapper.update(po,new QueryWrapper<ZnsbSbCxsHbsSbjscjZsDO>().lambda().eq(ZnsbSbCxsHbsSbjscjZsDO::getUuid,po.getUuid()));
//            }
//        }
//        final List<com.css.znsb.nssb.pojo.vo.hbs.hxzg.CycsSbjscj> updateCycs = deleteSbjsVO.getCycsGrid().getCycsGridLb();
//        if (!GyUtils.isNull(updateCycs)) {
//            final List<ZnsbSbCxsHbsSbjscjCycsDO> ucycsPO = BeanUtils.toBean(updateCycs, ZnsbSbCxsHbsSbjscjCycsDO.class);
//            for (ZnsbSbCxsHbsSbjscjCycsDO po : ucycsPO) {
//                po.setXgrDm("xgr");
//                po.setXgrq(LocalDateTime.now());
//                po.setSjgsdq(ZnsbSessionUtils.getSwjgDm());
//                po.setYxbz("N");
//                this.sbjscjCycsMapper.update(po,new QueryWrapper<ZnsbSbCxsHbsSbjscjCycsDO>().lambda().eq(ZnsbSbCxsHbsSbjscjCycsDO::getUuid,po.getUuid()));
//            }
//        }
//    }
//}
