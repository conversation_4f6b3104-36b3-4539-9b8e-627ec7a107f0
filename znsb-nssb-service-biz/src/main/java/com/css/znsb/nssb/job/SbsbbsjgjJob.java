package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.api.sbrw.SbrwApi;
import com.css.znsb.nssb.pojo.dto.RefreshSbrwZtReqDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.ZnsbNssbSbrwDTO;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 申报数据归集定时任务
 */
@Slf4j
@Component
public class SbsbbsjgjJob {

    @Resource
    private SbrwApi sbrwApi;

    @Resource
    private ZnsbNssbSbrwService znsbNssbSbrwService;

    /**
     * 申报数据归集
     */
    @XxlJob("sbsbbsjgjJob")
    public void execute() {
        log.info("==========开始进行申报数据归集==========");
        //扫描当月全量申报任务，对每条申报任务进行刷新状态操作
        List<ZnsbNssbSbrwDTO> sbrwDTOList = znsbNssbSbrwService.querySbrwAllBySbny(DateUtils.getSystemCurrentTime(17));
        if (!GyUtils.isNull(sbrwDTOList)){
            sbrwDTOList.forEach(sbrwDO -> {
                RefreshSbrwZtReqDTO refreshSbrwZtReqDTO = BeanUtils.toBean(sbrwDO,RefreshSbrwZtReqDTO.class);
                //date类型转换,防止日期格式转成字符串格式不统一
                refreshSbrwZtReqDTO.setSkssqq(DateUtils.dateToString(sbrwDO.getSkssqq(),3));
                refreshSbrwZtReqDTO.setSkssqz(DateUtils.dateToString(sbrwDO.getSkssqz(),3));
                sbrwApi.refreshSbrwZt(refreshSbrwZtReqDTO);
            });
        }
        log.info("==========申报数据归集完成==========");
    }
}
