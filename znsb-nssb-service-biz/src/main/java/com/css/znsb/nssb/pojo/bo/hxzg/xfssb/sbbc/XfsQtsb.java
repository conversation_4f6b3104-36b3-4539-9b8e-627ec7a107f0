
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《消费税》其它申报
 * 
 * <p>Java class for xfsQtsb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfsQtsb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="xfssb6" type="{http://www.chinatax.gov.cn/dataspec/}xfssb_qt"/>
 *         &lt;element name="xfssb6_fb1" type="{http://www.chinatax.gov.cn/dataspec/}bqzykcsejsbqt" minOccurs="0"/>
 *         &lt;element name="xfssb6_fb2" type="{http://www.chinatax.gov.cn/dataspec/}zykcxfspzmxbqt" minOccurs="0"/>
 *         &lt;element name="xfssb6_fb3" type="{http://www.chinatax.gov.cn/dataspec/}bqdsdjsejsbqt" minOccurs="0"/>
 *         &lt;element name="xfssb6_fb4" type="{http://www.chinatax.gov.cn/dataspec/}scjyqkbqt" minOccurs="0"/>
 *         &lt;element name="xfssb6_fb5" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsemxb" minOccurs="0"/>
 *         &lt;element name="xfssb6_fb6" type="{http://www.chinatax.gov.cn/dataspec/}hznsqyxfsfpb" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfsQtsb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "xfssb6",
    "xfssb6Fb1",
    "xfssb6Fb2",
    "xfssb6Fb3",
    "xfssb6Fb4",
    "xfssb6Fb5",
    "xfssb6Fb6"
})
public class XfsQtsb
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected XfssbQt xfssb6;
    @XmlElement(name = "xfssb6_fb1", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqzykcsejsbqt xfssb6Fb1;
    @XmlElement(name = "xfssb6_fb2", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Zykcxfspzmxbqt xfssb6Fb2;
    @XmlElement(name = "xfssb6_fb3", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqdsdjsejsbqt xfssb6Fb3;
    @XmlElement(name = "xfssb6_fb4", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Scjyqkbqt xfssb6Fb4;
    @XmlElement(name = "xfssb6_fb5", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqjmsemxb xfssb6Fb5;
    @XmlElement(name = "xfssb6_fb6", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Hznsqyxfsfpb xfssb6Fb6;

    /**
     * Gets the value of the xfssb6 property.
     * 
     * @return
     *     possible object is
     *     {@link XfssbQt }
     *     
     */
    public XfssbQt getXfssb6() {
        return xfssb6;
    }

    /**
     * Sets the value of the xfssb6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfssbQt }
     *     
     */
    public void setXfssb6(XfssbQt value) {
        this.xfssb6 = value;
    }

    /**
     * Gets the value of the xfssb6Fb1 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqzykcsejsbqt }
     *     
     */
    public Bqzykcsejsbqt getXfssb6Fb1() {
        return xfssb6Fb1;
    }

    /**
     * Sets the value of the xfssb6Fb1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqzykcsejsbqt }
     *     
     */
    public void setXfssb6Fb1(Bqzykcsejsbqt value) {
        this.xfssb6Fb1 = value;
    }

    /**
     * Gets the value of the xfssb6Fb2 property.
     * 
     * @return
     *     possible object is
     *     {@link Zykcxfspzmxbqt }
     *     
     */
    public Zykcxfspzmxbqt getXfssb6Fb2() {
        return xfssb6Fb2;
    }

    /**
     * Sets the value of the xfssb6Fb2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Zykcxfspzmxbqt }
     *     
     */
    public void setXfssb6Fb2(Zykcxfspzmxbqt value) {
        this.xfssb6Fb2 = value;
    }

    /**
     * Gets the value of the xfssb6Fb3 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqdsdjsejsbqt }
     *     
     */
    public Bqdsdjsejsbqt getXfssb6Fb3() {
        return xfssb6Fb3;
    }

    /**
     * Sets the value of the xfssb6Fb3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqdsdjsejsbqt }
     *     
     */
    public void setXfssb6Fb3(Bqdsdjsejsbqt value) {
        this.xfssb6Fb3 = value;
    }

    /**
     * Gets the value of the xfssb6Fb4 property.
     * 
     * @return
     *     possible object is
     *     {@link Scjyqkbqt }
     *     
     */
    public Scjyqkbqt getXfssb6Fb4() {
        return xfssb6Fb4;
    }

    /**
     * Sets the value of the xfssb6Fb4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Scjyqkbqt }
     *     
     */
    public void setXfssb6Fb4(Scjyqkbqt value) {
        this.xfssb6Fb4 = value;
    }

    /**
     * Gets the value of the xfssb6Fb5 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqjmsemxb }
     *     
     */
    public Bqjmsemxb getXfssb6Fb5() {
        return xfssb6Fb5;
    }

    /**
     * Sets the value of the xfssb6Fb5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqjmsemxb }
     *     
     */
    public void setXfssb6Fb5(Bqjmsemxb value) {
        this.xfssb6Fb5 = value;
    }

    /**
     * Gets the value of the xfssb6Fb6 property.
     * 
     * @return
     *     possible object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public Hznsqyxfsfpb getXfssb6Fb6() {
        return xfssb6Fb6;
    }

    /**
     * Sets the value of the xfssb6Fb6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public void setXfssb6Fb6(Hznsqyxfsfpb value) {
        this.xfssb6Fb6 = value;
    }

}
