package com.css.znsb.nssb.job;

import cn.hutool.core.util.StrUtil;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.service.sbrw.cwbbbsrw.ZnsbNssbCwkjzdbaxxService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 财务会计制度备案信息归集任务
 */
@Slf4j
@Component
public class CwkjzdbaxxGjJob {

    @Resource
    private ZnsbNssbCwkjzdbaxxService znsbNssbCwkjzdbaxxService;

    /**
     * 财务会计制度备案信息归集任务
     */
    @XxlJob("cwkjzdbaxxGjJob")
    public void execute() {
        log.info("==========开始进行财务会计制度备案信息归集任务==========");
        znsbNssbCwkjzdbaxxService.gjJobInit();
        log.info("==========财务会计制度备案信息归集任务完成==========");
    }

    /**
     * 财务会计制度备案信息归集任务(历史数据归集)
     */
    @XxlJob("cwkjzdbaxxGjLssjJob")
    public void execute1() {
        log.info("==========开始进行财务会计制度备案信息历史数据归集任务==========");
        String jobParam = XxlJobHelper.getJobParam();
        Map<String,Object> jobParamMap = JsonUtils.toMap(jobParam);
        if (GyUtils.isNull(jobParamMap) || GyUtils.isNull(jobParamMap.get("kssj"))){
            log.info("==========财务会计制度备案信息历史数据归集任务未获取到配置参数，执行结束==========");
            return;
        }
        String djxh = StrUtil.EMPTY;
        if (!GyUtils.isNull(jobParamMap.get("djxh"))){
            djxh = String.valueOf(jobParamMap.get("djxh"));
        }
        znsbNssbCwkjzdbaxxService.gjlsrwJobInit(String.valueOf(jobParamMap.get("kssj")),djxh);
        log.info("==========财务会计制度备案信息历史数据归集任务完成==========");
    }

}
