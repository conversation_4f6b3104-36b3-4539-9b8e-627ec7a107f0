package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《铁路运输企业分支机构增值税汇总纳税信息传递单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_tlysqyfzjgzzshznsxxcdd", propOrder = { "sbbheadVO", "yjnzzsqkGrid", "qdjxseqkGrid" })
@XmlSeeAlso({ ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw.ZzsybnsrsbTlysqyfzjgzzshznsxxcdd.class })
@Getter
@Setter
public class ZzsybnsrsbTlysqyfzjgzzshznsxxcdd {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 已缴纳增值税情况
     */
    @XmlElement(nillable = true, required = true)
    protected YjnzzskGrid yjnzzsqkGrid;

    /**
     * 取得进项税额情况
     */
    @XmlElement(nillable = true, required = true)
    protected QdjxsekGrid qdjxseqkGrid;
}