package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税一般纳税人本期应纳税额按预算科目分解表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_zzsybnsrbqynseayskmfjbywbw", propOrder = { "zzssyyybnsrZzsybnsrbqynseayskmfjb" })
@Getter
@Setter
public class ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw extends TaxDoc {
    /**
     * 增值税一般纳税人本期应纳税额按预算科目分解表
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_zzsybnsrbqynseayskmfjb", required = true)
    @JSONField(name = "zzssyyybnsr_zzsybnsrbqynseayskmfjb")
    protected ZzssyyybnsrZzsybnsrbqynseayskmfjb zzssyyybnsrZzsybnsrbqynseayskmfjb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrZzsybnsrbqynseayskmfjb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrZzsybnsrbqynseayskmfjb {}
}