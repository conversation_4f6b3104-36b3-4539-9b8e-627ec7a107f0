package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《代扣代缴税收通用缴款书抵扣清单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_dkdjsstyjksdkqd", propOrder = { "sbbheadVO", "dkdjsstyjksdkqdGrid" })
@Getter
@Setter
public class ZzsybnsrsbDkdjsstyjksdkqd {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 《固定资产进项税额抵扣情况表》
     */
    @XmlElement(nillable = true, required = true)
    protected DkdjsstyjksdkqdGrid dkdjsstyjksdkqdGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "dkdjsstyjksdkqdGridlb" })
    @Getter
    @Setter
    public static class DkdjsstyjksdkqdGrid {
        @XmlElement(nillable = true, required = true)
        protected List<DkdjsstyjksdkqdGridlbVO> dkdjsstyjksdkqdGridlb;

        /**
         * Gets the value of the dkdjsstyjksdkqdGridlb property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the dkdjsstyjksdkqdGridlb property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getDkdjsstyjksdkqdGridlb().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link DkdjsstyjksdkqdGridlbVO}
         */
        public List<DkdjsstyjksdkqdGridlbVO> getDkdjsstyjksdkqdGridlb() {
            if (dkdjsstyjksdkqdGridlb == null) {
                dkdjsstyjksdkqdGridlb = new ArrayList<DkdjsstyjksdkqdGridlbVO>();
            }
            return this.dkdjsstyjksdkqdGridlb;
        }
    }
}