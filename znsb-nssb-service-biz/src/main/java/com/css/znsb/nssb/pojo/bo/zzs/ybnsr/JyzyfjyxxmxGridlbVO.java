package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 加油站月份加油信息明细Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jyzyfjyxxmxGridlbVO", propOrder = { "fddbr", "lxdh", "zqs", "jyqccbh", "ypxh", "yljjyl", "yljje", "yynzzse", "tbr" })
@Getter
@Setter
public class JyzyfjyxxmxGridlbVO {
    /**
     * 法定代表人（负责人）
     */
    @XmlElement(nillable = true, required = true)
    protected String fddbr;

    /**
     * 联系电话
     */
    @XmlElement(nillable = true, required = true)
    protected String lxdh;

    /**
     * 总枪数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal zqs;

    /**
     * 加油枪出厂编号
     */
    @XmlElement(nillable = true, required = true)
    protected String jyqccbh;

    /**
     * 油品型号
     */
    @XmlElement(nillable = true, required = true)
    protected String ypxh;

    /**
     * 月累计加油量
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal yljjyl;

    /**
     * 月累计金额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal yljje;

    /**
     * 月应纳增值税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal yynzzse;

    /**
     * 填表人
     */
    @XmlElement(nillable = true, required = true)
    protected String tbr;
}