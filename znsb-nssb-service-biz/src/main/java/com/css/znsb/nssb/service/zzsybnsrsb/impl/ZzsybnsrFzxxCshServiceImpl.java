package com.css.znsb.nssb.service.zzsybnsrsb.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.nssb.constants.SfEnum;
import com.css.znsb.nssb.constants.ZzsybnsrSjjhConstants;
import com.css.znsb.nssb.constants.enums.JylxDmEnum;
import com.css.znsb.nssb.constants.enums.JzzcsyztEnum;
import com.css.znsb.nssb.constants.enums.YbnsrEwbHlbmEnum;
import com.css.znsb.nssb.constants.enums.YesOrNoEnum;
import com.css.znsb.nssb.constants.enums.ZsxmDmEnum;
import com.css.znsb.nssb.constants.enums.ZzsJjdjEnum;
import com.css.znsb.nssb.pojo.domain.xfssb.DjDjhglglDO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsJcdlqywgxpTzDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.qcxx.tzxx.YbnsrTzxxJjdjLjfseDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.bqfselj.ZzsybnsrBqfseljcxReqDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.bqfselj.ZzsybnsrBqfseljcxRespDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.csh.ZzsybnsrCktsXtztReqDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.csh.ZzsybnsrCktsXtztRespDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.csh.ZzsybnsrCshQtqcsjDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.csh.zcsb.ZzsybnsrCshJjdjXzjgDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.jjdjbqfse.ZzsybnsrJjdjbqfsecxReqDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.jjdjbqfse.ZzsybnsrJjdjbqfsecxRespDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.wqwcjwgxp.ZzsybnsrWqwcjWgxpcxReqDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.lqfzpt.wqwcjwgxp.ZzsybnsrWqwcjWgxpcxRespDTO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrBqxxVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrQcCsxxVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrQcKzxxVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrQcNsrxxVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrQcxxItemVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrSbsxVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrTzxxVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrYtxxVO;
import com.css.znsb.nssb.service.gy.IGyCsdmService;
import com.css.znsb.nssb.service.phjm.PhjmService;
import com.css.znsb.nssb.service.xfssb.DjDjhglglService;
import com.css.znsb.nssb.service.zzs.yjyh.ZzsyjyhService;
import com.css.znsb.nssb.service.zzsybnsrsb.ZzsybnsrFzxxCshService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static cn.hutool.core.date.DatePattern.NORM_DATE_FORMAT;
import static cn.hutool.core.text.CharSequenceUtil.EMPTY;
import static com.css.znsb.nssb.constants.Gt3HxzgXtcsConstants.CS_FYGZNSRTXBZ_CSBM;
import static com.css.znsb.nssb.constants.ZzsybnsrSjjhConstants.ZZSYBNSR_CXCKSCQYMDTSBSHZT_SJJHLXDM;
import static com.css.znsb.nssb.constants.ZzsybnsrSjjhConstants.ZZSYBNSR_SJJH_YWBM;

/**
 * 增值税一般纳税人辅助信息初始化service
 */
@Slf4j
@Service
public class ZzsybnsrFzxxCshServiceImpl implements ZzsybnsrFzxxCshService {
    
    @Resource
    private ZzsyjyhService zzsyjyhService;

    @Resource
    private PhjmService phjmService;

    @Resource
    private IGyCsdmService csdmService;
    
    @Resource
    private DjDjhglglService djhglglService;
    
    @Resource
    private CompanyApi companyApi;
    
    @Resource
    private SjjhService sjjhService;

    @Override
    public void buildKzxx(ZzsybnsrCshQtqcsjDTO qtxx, YbnsrQcxxItemVO qcxxItem) {
        YbnsrQcKzxxVO kzxx = new YbnsrQcKzxxVO();
        Optional.ofNullable(qcxxItem.getFzxx().getKzxx()).ifPresent(t -> BeanUtil.copyProperties(t, kzxx));
        final Date maxSkssqz = this.queryMaxZstySkssqzByDjxh(qcxxItem.getNsrxx().getDjxh());
        kzxx.setYzl(qtxx.getYzl());
        kzxx.setSfczYxdZdxLdtssq(GyUtils.isNull(maxSkssqz) ? "N" : "Y");
        kzxx.setYtsSqzZdz(Optional.ofNullable(maxSkssqz)
                .map(t -> DateUtil.format(maxSkssqz, NORM_DATE_FORMAT)).orElse(null));
        // IPS-87084 【金四】【金三补丁：1204.03】【20006_增值税及附加税费申报（一般纳税人适用）】调整附表一13a、13b、13c栏次可编辑性-开始
        kzxx.setYzlAKbjbz(YesOrNoEnum.N.getCode());
        kzxx.setYzlBKbjbz(YesOrNoEnum.N.getCode());
        kzxx.setYzlCKbjbz(YesOrNoEnum.N.getCode());
        // 是否启用增值税预缴优化：增值税一般纳税人申报税款所属期止大于增值税预缴数据优化系统参数A0000001062023013配置的参数值，则为启用，未配置则为未启用
        final String djxh = qcxxItem.getNsrxx().getDjxh();
        final String zgswjDm = qcxxItem.getNsrxx().getZgswjDm();
        final Date skssqq = qcxxItem.getSbsx().getSkssqq();
        final Date skssqz = qcxxItem.getSbsx().getSkssqz();

        if (zzsyjyhService.checkZzsyjyhEnable(djxh, skssqq, skssqz, zgswjDm)) {
            // 启用增值税预缴优化的预征率处理
            ybnsrFb1YzlYjyhcl(kzxx, qcxxItem);
//            final YbnsrBqxxVO bqxx = qcxxItem.getFzxx().getBqxx();
//            if (!StrUtil.equals(JylxDmEnum.GZSB.getCode(), qcxxItem.getSbsx().getJylxDm())
//                    && needChooseYjskclfs(bqxx, qcxxItem.getNsrxx().getDjxh(), skssqq, skssqz)) {
//                kzxxDTO.setYjskclfs(YesOrNoEnum.Y.getCode());
//            }
            if (!JylxDmEnum.GZSB.getCode().equals(qcxxItem.getSbsx().getJylxDm())) {
                // TODO 如果总机构没有选择预缴税款处理方式的话，核心会抛出异常，那就是能执行到这里那肯定是已经选择了
                kzxx.setYjskclfs(YesOrNoEnum.N.getCode());
            }
        } else {
            // 2、未启用增值税预缴优化时：（1）附列资料一第13a、b、c行，附列资料一第13a、b、c行，可编辑性按通用规则处理。
            ybnsrFb1YzlTygzcl(kzxx, qcxxItem);
        }
        // IPS-87084 【金四】【金三补丁：1204.03】【20006_增值税及附加税费申报（一般纳税人适用）】调整附表一13a、13b、13c栏次可编辑性-结束

        // 原小微企业六税两费减免性质名称校验中心写死，现后端返回
        kzxx.setXwqyLslfJmxzList(phjmService.getXwqyLslfJmxz(
                Arrays.asList(ZsxmDmEnum.CSWHJSS.getCode(), ZsxmDmEnum.JYFFJ.getCode(), ZsxmDmEnum.DFJYFJ.getCode()),
                Arrays.asList(JzzcsyztEnum.XXWLQY.getCode(), JzzcsyztEnum.GTGSH.getCode()),
                DateUtil.format(skssqq, NORM_DATE_FORMAT),
                DateUtil.format(skssqz, NORM_DATE_FORMAT),
                zgswjDm));
        kzxx.setJzzcsyztDmSzc(qcxxItem.getYtxx().getSbFjsf().getSbFjsfQtxx().getJzzcsyztDm());
        kzxx.setGzsbSfytbCkts(YesOrNoEnum.N.getCode());
//        if (sfGzsb) {
//            String ckts = ybnsrGzsbCktsSftbBusiness.getCktsXtzt(qcxxItemBO.getNsrxx().getDjxh(), skssqq, skssqz, qcxxItemBO.getSbsx().getSbuuid(), qcxxItemBO.getSbsx().getPzxh());
//            if (!StringUtils.isBlank(ckts) && "01".equals(ckts)) {
//                kzxxDTO.setGzsbSfytbCkts(YesOrNoEnum.Y.getCode());
//            } else if ("02".equals(ckts)) {
//                throw new DzswjBizException(SbzxErrorEnum.BIZ_ERROR.getCode(), new String[]{"获取出口退税系统状态异常，请稍后再试。"});
//            }
//        }
        // TODO 更正申报并且具有出口退税资格时
        if (JylxDmEnum.GZSB.getCode().equals(qcxxItem.getSbsx().getJylxDm()) && "Y".equals(qtxx.getCktsqyBz())) {
            final String ckts = this.getCktsXtzt(qcxxItem);
            if (GyUtils.isNotNull(ckts) && "01".equals(ckts)) {
                kzxx.setGzsbSfytbCkts(YesOrNoEnum.N.getCode());
            }
        }
        qcxxItem.getFzxx().setKzxx(kzxx);
    }

    /**
     * 一般纳税人附表一预征率预缴优化处理
     * @param kzxx 扩展信息
     * @param qcxxItem 期初信息
     */
    private void ybnsrFb1YzlYjyhcl(YbnsrQcKzxxVO kzxx, YbnsrQcxxItemVO qcxxItem) {
        ybnsrFb1YzlTygzcl(kzxx, qcxxItem);
    }

    /**
     * 一般纳税人附表一预征率通用规则处理
     * @param kzxx 扩展信息
     * @param qcxxItem 期初信息
     */
    private void ybnsrFb1YzlTygzcl(YbnsrQcKzxxVO kzxx, YbnsrQcxxItemVO qcxxItem) {
        // 13a行：纳税人为增值税“按固定预征率或定额税率预缴”分支机构，可编辑，否则只读；
        // 增值税“按固定预征率或定额税率预缴”分支机构：命中“增值税预征率预征分支机构”标签的纳税人。
        YbnsrBqxxVO bqxx = qcxxItem.getFzxx().getBqxx();
        if (StrUtil.equals(bqxx.getZzsyzfzjg(), YesOrNoEnum.Y.getCode())) {
            kzxx.setYzlAKbjbz(YesOrNoEnum.Y.getCode());
        }
        // 13b行：税费种认定了铁路运输【101016105】的征收品目，可编辑，否则只读。
        if (StrUtil.equals(bqxx.getTlysqy(), YesOrNoEnum.Y.getCode())) {
            kzxx.setYzlBKbjbz(YesOrNoEnum.Y.getCode());
        }
        // 13c行：纳税人为跨省合资铁路企业，可编辑，否则只读。
        if (StrUtil.equals(bqxx.getKshztlbz(), YesOrNoEnum.Y.getCode())) {
            kzxx.setYzlCKbjbz(YesOrNoEnum.Y.getCode());
        }
    }

    /**
     * 组织加计抵减扩展信息
     * @param qtxx 其他信息
     * @param qcxxItem 期初数据
     */
    @Override
    public void organizeJjdjKzxx(ZzsybnsrCshQtqcsjDTO qtxx, ZzsybnsrCshJjdjXzjgDTO jjdjXzjg, YbnsrQcxxItemVO qcxxItem) {
        final YbnsrBqxxVO bqxx = qcxxItem.getFzxx().getBqxx();
        final YbnsrQcKzxxVO kzxx =  qcxxItem.getFzxx().getKzxx();
        if (GyUtils.isNull(qcxxItem.getFzxx().getTzxx())) {
            qcxxItem.getFzxx().setTzxx(new YbnsrTzxxVO());
        }
        if (GyUtils.isNull(qcxxItem.getFzxx().getCsxx())) {
            qcxxItem.getFzxx().setCsxx(new YbnsrQcCsxxVO());
        }
        final YbnsrQcCsxxVO csxx = qcxxItem.getFzxx().getCsxx();
        final YbnsrYtxxVO ytxx = qcxxItem.getYtxx();
        kzxx.setSfSctbJcdlJjdjBz(bqxx.getJcdljjdjqy());
        kzxx.setSfSctbGymjJjdjBz(bqxx.getGymjjjdjqy());
        kzxx.setSfSctbXjzzyJjdjBz(bqxx.getXjzzyjjdjqy());
        kzxx.setSfsscsbqsqbz(YesOrNoEnum.N.getCode());
        Date defaultDate = DateUtil.parse("2023-01-01 00:00:00");
        kzxx.setNrJcdlQymdGlsj(defaultDate);
        kzxx.setNrGymjQymdGlsj(defaultDate);
        kzxx.setNrXjzzyQymdGlsj(defaultDate);
        YbnsrQcNsrxxVO nsrxxVO = qcxxItem.getNsrxx();
        //生产生活政策有效期值
        String scshZcyxqzNd = csdmService.getGt3XtcsByZgswjgDmUp(ZzsJjdjEnum.SCSH.getGlZzCsDm(), nsrxxVO.getZgswjDm());
        if (GyUtils.isNull(scshZcyxqzNd)) {
            scshZcyxqzNd = ZzsJjdjEnum.SCSH.getMrNd();
        }
        kzxx.setJjdjZcZznd(scshZcyxqzNd);
        String zcjg = "";
        if (GyUtils.isNotNull(qtxx) && GyUtils.isNotNull(jjdjXzjg)) {
            if (JylxDmEnum.GZSB.getCode().equals(qcxxItem.getSbsx().getJylxDm())) {
                zcjg = qtxx.getXzjg();
            } else {
                zcjg = jjdjXzjg.getXzjg();
            }
        }
        kzxx.setJcdlbz(qtxx.getJcdlbz());
        kzxx.setGymjbz(qtxx.getGymjbz());
        kzxx.setXjzzybz(qtxx.getXjzzybz());
        kzxx.setXzjg(zcjg);
        kzxx.setJjdjbz(qtxx.getJjdjBz());
        if (YesOrNoEnum.Y.getCode().equals(bqxx.getJcdljjdjqy())
                || YesOrNoEnum.Y.getCode().equals(bqxx.getGymjjjdjqy())
                || YesOrNoEnum.Y.getCode().equals(bqxx.getXjzzyjjdjqy())) {
            //错误更正不是最近一次申报标志
            if (JylxDmEnum.GZSB.getCode().equals(qcxxItem.getSbsx().getJylxDm())) {
                if (YesOrNoEnum.N.getCode().equals(qtxx.getSfszhycsb())) {
                    kzxx.setCwgzBsZjyc(YesOrNoEnum.Y.getCode());
                } else {
                    kzxx.setCwgzBsZjyc(YesOrNoEnum.N.getCode());
                }
            } else {
                kzxx.setCwgzBsZjyc(YesOrNoEnum.N.getCode());
            }
            //加计抵减是否申报过
            if (GyUtils.isNotNull(zcjg)) {
                if (YesOrNoEnum.Y.getCode().equals(bqxx.getJcdljjdjqy())) {
                    //是否首次填报集成电路加计抵减
                    if ("S".equals(qtxx.getJcdlbz())) {
                        kzxx.setSfSctbJcdlJjdjBz(YesOrNoEnum.Y.getCode());
                    } else {
                        kzxx.setSfSctbJcdlJjdjBz(YesOrNoEnum.N.getCode());
                    }
                }
                if (YesOrNoEnum.Y.getCode().equals(bqxx.getGymjjjdjqy())) {
                    //是否首次填报工业母机加计抵减
                    if ("S".equals(qtxx.getGymjbz())) {
                        kzxx.setSfSctbGymjJjdjBz(YesOrNoEnum.Y.getCode());
                    } else {
                        kzxx.setSfSctbGymjJjdjBz(YesOrNoEnum.N.getCode());
                    }
                }
                if (YesOrNoEnum.Y.getCode().equals(bqxx.getXjzzyjjdjqy())) {
                    //是否首次填报先进制造业加计抵减
                    if ("S".equals(qtxx.getXjzzybz())) {
                        kzxx.setSfSctbXjzzyJjdjBz(YesOrNoEnum.Y.getCode());
                    } else {
                        kzxx.setSfSctbXjzzyJjdjBz(YesOrNoEnum.N.getCode());
                    }
                }
            }
            //适用加计抵减率
            setJcdlSyjjdjl(csxx, nsrxxVO, qcxxItem.getSbsx().getSkssqq(), qcxxItem.getSbsx().getSkssqz());
            Date finalGlDate = DateUtil.parse(scshZcyxqzNd + "-12-31");
            final String djxh = qcxxItem.getNsrxx().getDjxh();
            final Date glyxqq = DateUtil.parse("1970-01-01", NORM_DATE_FORMAT);
            final Date glyxqz = DateUtil.parse("9999-12-31", NORM_DATE_FORMAT);
            //加计抵减政策归类时间
            if (YesOrNoEnum.Y.getCode().equals(bqxx.getJcdljjdjqy())) {
                // TODO 没法查归类信息，暂时拿本地消费税的dj_djhgl表处理
                final List<DjDjhglglDO> jcdlJjdjQyGlList = djhglglService.queryDjglXx(djxh, glyxqq, glyxqz, 
                        ZzsJjdjEnum.JCDL.getLbDm());
                if (GyUtils.isNotNull(jcdlJjdjQyGlList)) {
                    final DjDjhglglDO jcdlJjdjQyGl = jcdlJjdjQyGlList.stream().min((o1, o2) -> 
                            DateUtil.compare(o1.getYxqz(), o2.getYxqz())).orElse(new DjDjhglglDO());
                    kzxx.setNrJcdlQymdGlsj(jcdlJjdjQyGl.getYxqq());
                    
                    final Date yxqz = jcdlJjdjQyGlList.stream().max((o1, o2) -> DateUtil.compare(o1.getYxqz(), 
                            o2.getYxqz())).orElse(new DjDjhglglDO()).getYxqz();

                    if (DateUtil.compare(yxqz, finalGlDate) > 0) {
                        finalGlDate = jcdlJjdjQyGl.getYxqz();
                        String jcdlNd = csdmService.getGt3XtcsByZgswjgDmUp(ZzsJjdjEnum.JCDL.getGlZzCsDm(), nsrxxVO.getZgswjDm());
                        if (GyUtils.isNull(jcdlNd)) {
                            jcdlNd = ZzsJjdjEnum.JCDL.getMrNd();
                        }
                        kzxx.setJjdjZcZznd(jcdlNd);
                    }
                }
                //调用核心征管11083查询历史申报记录
                getWgxpTzList(qtxx, qcxxItem);
                //首次填报集成电路加计抵减税款所属期起
//                if (!YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbJcdlJjdjBz())) {
//                    kzxx.setSctbJcdlJjdjSkssqq(zcjgList.stream()
//                            .filter(o -> ZzsJjdjEnum.JCDL.getXzjg().equals(o.getXzjg()))
//                            .map(SbZzsXzjjdjzcjgVO::getSkssqq)
//                            .min(DateUtil::compareDate)
//                            .map(o -> DateUtils.doDateFormat(o, DateUtil.DATE_PATTERN))
//                            .orElse(null));
//                    kzxx.setSctbJcdlJjdjSkssqq(DateUtil.format(zcjg.getSkssqq(), NORM_DATE_FORMAT));
//                }
            }
            if (YesOrNoEnum.Y.getCode().equals(bqxx.getGymjjjdjqy())) {
                //加计抵减政策归类时间
                final List<DjDjhglglDO> gymjJjdjQyGlList = djhglglService.queryDjglXx(djxh, glyxqq, glyxqz,
                        ZzsJjdjEnum.GYMJ.getLbDm());
                if (GyUtils.isNotNull(gymjJjdjQyGlList)) {
                    final DjDjhglglDO gymjJjdjQyGl = gymjJjdjQyGlList.stream().min((o1, o2) -> 
                            DateUtil.compare(o1.getYxqz(), o2.getYxqz())).orElse(new DjDjhglglDO());
                    kzxx.setNrGymjQymdGlsj(gymjJjdjQyGl.getYxqq());

                    final Date yxqz = gymjJjdjQyGlList.stream().max((o1, o2) -> DateUtil.compare(o1.getYxqz(),
                            o2.getYxqz())).orElse(new DjDjhglglDO()).getYxqz();

                    if (DateUtil.compare(yxqz, finalGlDate) > 0) {
                        finalGlDate = gymjJjdjQyGl.getYxqz();
                        String gymjNd = csdmService.getGt3XtcsByZgswjgDmUp(ZzsJjdjEnum.GYMJ.getGlZzCsDm(), nsrxxVO.getZgswjDm());
                        if (GyUtils.isNull(gymjNd)) {
                            gymjNd = ZzsJjdjEnum.GYMJ.getMrNd();
                        }
                        kzxx.setJjdjZcZznd(gymjNd);
                    }
                }
                //首次填报工业母机加计抵减税款所属期起
//                if (!YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbGymjJjdjBz())) {
//                    kzxx.setSctbGymjJjdjSkssqq(zcjgList.stream()
//                            .filter(o -> ZzsJjdjEnum.GYMJ.getXzjg().equals(o.getXzjg()))
//                            .map(SbZzsXzjjdjzcjgVO::getSkssqq)
//                            .min(DateUtil::compareDate)
//                            .map(o -> DateUtils.doDateFormat(o, DateUtil.DATE_PATTERN))
//                            .orElse(null));
//                    kzxx.setSctbGymjJjdjSkssqq(DateUtil.format(zcjg.getSkssqq(), NORM_DATE_FORMAT));
//                }
                //工业母机更正时本期发生额是否可填写
                if (JylxDmEnum.GZSB.getCode().equals(qcxxItem.getSbsx().getJylxDm())) {
                    if (YesOrNoEnum.Y.getCode().equals(kzxx.getCwgzBsZjyc())
                            && ZzsJjdjEnum.GYMJ.getXzjg().equals(zcjg)) {
                        kzxx.setGymjGzBqfseSfkbj(YesOrNoEnum.N.getCode());
                    } else {
                        kzxx.setGymjGzBqfseSfkbj(YesOrNoEnum.Y.getCode());
                    }
                } else {
                    kzxx.setGymjGzBqfseSfkbj(YesOrNoEnum.Y.getCode());
                }
            } else {
                kzxx.setGymjGzBqfseSfkbj(YesOrNoEnum.N.getCode());
            }
            if (YesOrNoEnum.Y.getCode().equals(bqxx.getXjzzyjjdjqy())) {
                //加计抵减政策归类时间
                final List<DjDjhglglDO> xjzzyJjdjQyGlList = djhglglService.queryDjglXx(djxh, glyxqq, glyxqz,
                        ZzsJjdjEnum.XJZZY.getLbDm());
                if (GyUtils.isNotNull(xjzzyJjdjQyGlList)) {
                    final DjDjhglglDO xjzzyJjdjQyGl = xjzzyJjdjQyGlList.stream().min((o1, o2) ->
                            DateUtil.compare(o1.getYxqz(), o2.getYxqz())).orElse(new DjDjhglglDO());
                    kzxx.setNrXjzzyQymdGlsj(xjzzyJjdjQyGl.getYxqq());
                    
                    final Date yxqz = xjzzyJjdjQyGlList.stream().max((o1, o2) -> DateUtil.compare(o1.getYxqz(),
                            o2.getYxqz())).orElse(new DjDjhglglDO()).getYxqz();

                    if (DateUtil.compare(yxqz, finalGlDate) > 0) {
                        String xjzzyNd = csdmService.getGt3XtcsByZgswjgDmUp(ZzsJjdjEnum.XJZZY.getGlZzCsDm(), nsrxxVO.getZgswjDm());
                        if (GyUtils.isNull(xjzzyNd)) {
                            xjzzyNd = ZzsJjdjEnum.XJZZY.getMrNd();
                        }
                        kzxx.setJjdjZcZznd(xjzzyNd);
                    }
                }
                //首次填报先进制造业加计抵减税款所属期起
//                if (!YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbXjzzyJjdjBz())) {
//                    kzxxDTO.setSctbXjzzyJjdjSkssqq(zcjgList.stream()
//                            .filter(o -> ZzsJjdjEnum.XJZZY.getXzjg().equals(o.getXzjg()))
//                            .map(SbZzsXzjjdjzcjgVO::getSkssqq)
//                            .min(DateUtil::compareDate)
//                            .map(o -> DateUtils.doDateFormat(o, DateUtil.DATE_PATTERN))
//                            .orElse(null));
//                    kzxx.setSctbXjzzyJjdjSkssqq(DateUtil.format(zcjg.getSkssqq(), NORM_DATE_FORMAT));
//                }
                //先进制造业更正时本期发生额是否可填写
                if (JylxDmEnum.GZSB.getCode().equals(qcxxItem.getSbsx().getJylxDm())) {
                    if (YesOrNoEnum.Y.getCode().equals(kzxx.getCwgzBsZjyc())
                            && ZzsJjdjEnum.XJZZY.getXzjg().equals(zcjg)) {
                        kzxx.setXjzzyGzBqfseSfkbj(YesOrNoEnum.N.getCode());
                    } else {
                        kzxx.setXjzzyGzBqfseSfkbj(YesOrNoEnum.Y.getCode());
                    }
                } else {
                    kzxx.setXjzzyGzBqfseSfkbj(YesOrNoEnum.Y.getCode());
                }
            } else {
                kzxx.setXjzzyGzBqfseSfkbj(YesOrNoEnum.N.getCode());
            }
            //加计抵减如果小于生效日期，期初余额强制赋0
            if (Integer.parseInt(kzxx.getJjdjZcZznd()) < Integer.parseInt(DateUtil.format(qcxxItem.getSbsx().getSkssqz(), "yyyy"))) {
                if (!CollectionUtil.isEmpty(ytxx.getSbZzsYbnsrFb4Sedjqk())) {
                    ytxx.getSbZzsYbnsrFb4Sedjqk().stream().filter(item ->
                                    StrUtil.equalsAny(item.getEwbhxh()
                                            , YbnsrEwbHlbmEnum.SB_ZZS_YBNSR_FB4_SEDJQK_JJDJQK_YBXMJJDJEJS.getEwbHlxh()
                                            , YbnsrEwbHlbmEnum.SB_ZZS_YBNSR_FB4_SEDJQK_JJDJQK_JZJTXMJJDJEJS.getEwbHlxh()))
                            .forEach(item -> item.setQcye(BigDecimal.ZERO));
                }
            }
            //首次申报之前的属期更正
            kzxx.setSfsscsbqsqbz(qtxx.getSfsscsbqsqbz());
            //调用核心征管11905查询本期发生额
            getJjdjBqfseList(qtxx, qcxxItem);
            //更正申报加计抵减政策选择结果
            if (JylxDmEnum.GZSB.getCode().equals(qcxxItem.getSbsx().getJylxDm())) {
                kzxx.setJjdjzcXzjg(zcjg);
            } else {
                kzxx.setJjdjzcXzjg(EMPTY);
            }
            //处理台账数据
            dealJjdjLjyjts(qcxxItem);
            //处理提示语
            getJjdjzcts(qcxxItem);
        }
    }

    /**
     * 设置加计抵减率
     * @param csxx 参数信息
     * @param nsrxxVO 纳税人信息
     * @param skssqqString 税款所属期起
     * @param skssqzString 税款所属期止
     */
    private void setJcdlSyjjdjl(YbnsrQcCsxxVO csxx, YbnsrQcNsrxxVO nsrxxVO, Date skssqqString, Date skssqzString) {
        String jcdlJjdjl = getDjl(csdmService.getGt3XtcsByZgswjgDmUp(ZzsJjdjEnum.JCDL.getSlCsDm(), nsrxxVO.getZgswjDm()), skssqqString, skssqzString);
        String gymjJjdjl = getDjl(csdmService.getGt3XtcsByZgswjgDmUp(ZzsJjdjEnum.GYMJ.getSlCsDm(), nsrxxVO.getZgswjDm()), skssqqString, skssqzString);
        String xjzzyJjdjl = getDjl(csdmService.getGt3XtcsByZgswjgDmUp(ZzsJjdjEnum.XJZZY.getSlCsDm(), nsrxxVO.getZgswjDm()), skssqqString, skssqzString);
        csxx.setJcdlSyjjdjl(StrUtil.isBlank(jcdlJjdjl) ? ZzsJjdjEnum.JCDL.getMrZsl() : jcdlJjdjl);
        csxx.setGymjSyjjdjl(StrUtil.isBlank(gymjJjdjl) ? ZzsJjdjEnum.GYMJ.getMrZsl() : gymjJjdjl);
        csxx.setXjzzySyjjdjl(StrUtil.isBlank(xjzzyJjdjl) ? ZzsJjdjEnum.XJZZY.getMrZsl() : xjzzyJjdjl);
    }

    /**
     * 获取抵减率
     * @param csz 参数值
     * @param skssqqString 税款所属期起
     * @param skssqzString 税款所属期止
     * @return 抵减率
     */
    private String getDjl(String csz, Date skssqqString, Date skssqzString) {
        if (StrUtil.contains(csz, ",")) {
            String[] split = csz.split(",");
            if (split.length == 3) {
                String yxqq = StrUtil.trim(split[0]);
                String yxqz = StrUtil.trim(split[1]);
                String sl = StrUtil.trim(split[2]);
                if (DateUtil.compare(DateUtil.parse(yxqq), skssqqString) <= 0
                        && DateUtil.compare(DateUtil.parse(yxqz), skssqzString) >= 0) {
                    return sl;
                }
                return BigDecimal.ZERO.toPlainString();
            }
        }
        return csz;
    }

    /**
     * 获取外购芯片台账信息
     * @param qtxx 其他数据
     * @param qcxxItem 期初数据
     */
    private void getWgxpTzList(ZzsybnsrCshQtqcsjDTO qtxx, YbnsrQcxxItemVO qcxxItem) {
        final String djxh = qcxxItem.getNsrxx().getDjxh();
        final String skssqq = DateUtil.format(qcxxItem.getSbsx().getSkssqq(), NORM_DATE_FORMAT);
        final String skssqz = DateUtil.format(qcxxItem.getSbsx().getSkssqz(), NORM_DATE_FORMAT);
        final String jzjtbz = qtxx.getJzjtBz();
        final String sfscsb = qtxx.getJcdlbz();
        final String jylxDm = qcxxItem.getSbsx().getJylxDm();
        final String cwgzbz = JylxDmEnum.GZSB.getCode().equals(jylxDm) ? "cwgzbz" : "";
        final String nsrsbh = qcxxItem.getNsrxx().getNsrsbh();
        
        final ZzsybnsrWqwcjWgxpcxReqDTO reqDTO = new ZzsybnsrWqwcjWgxpcxReqDTO();
        reqDTO.setDjxh(djxh);
        reqDTO.setSkssqq(skssqq);
        reqDTO.setSkssqz(skssqz);
        reqDTO.setJzjtbz(jzjtbz);
        reqDTO.setSfscsb(sfscsb);
        reqDTO.setCwgzbz(cwgzbz);
        
//        final CommonResult<CompanyBasicInfoDTO> result = companyApi.basicInfo(djxh, nsrsbh);
//        String xzqhszDm = qcxxItem.getNsrxx().getZcdzxzqhszDm();
//        if (result.isSuccess() && GyUtils.isNotNull(result.getData())) {
//            xzqhszDm = result.getData().getXzqhszDm();
//        }
        
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setDjxh(djxh);
//        sjjhDTO.setNsrsbh(nsrsbh);
//        sjjhDTO.setSjjhlxDm(ZZSYBNSR_WQWCJWGXP_SJJHLXDM);
//        sjjhDTO.setYwbm(ZZSYBNSR_SJJH_YWBM);
//        sjjhDTO.setYwuuid(GyUtils.getUuid());
//        sjjhDTO.setXzqhszDm(xzqhszDm);
//        sjjhDTO.setBwnr(JSONUtil.toJsonStr(reqDTO));
        
//        final CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
//        if (sjjhResult.isSuccess() && GyUtils.isNotNull(sjjhResult.getData())) {
//            final String resJson = (String) sjjhResult.getData();
//            final ZzsybnsrWqwcjWgxpcxRespDTO respDTO = JSONUtil.toBean(resJson, ZzsybnsrWqwcjWgxpcxRespDTO.class);
//            final List<SbZzsJcdlqywgxpTzDTO> wgxpTzDTOList = respDTO.getWgxptzmxList();
//            wgxpTzDTOList.forEach(item -> {
//                if (item.getSyjjdjl() == null || BigDecimal.ZERO.compareTo(item.getSyjjdjl()) == 0) {
//                    item.setSyjjdjl(new BigDecimal(qcxxItem.getFzxx().getCsxx().getJcdlSyjjdjl()));
//                    item.setSfsctb(qcxxItem.getFzxx().getKzxx().getSfSctbJcdlJjdjBz());
//                }
//            });
//            qcxxItem.getFzxx().getTzxx().setSbZzsJcdlqyWgxpTzList(wgxpTzDTOList);
//            //当期数据自行组织
//            if (!JylxDmEnum.GZSB.getCode().equals(jylxDm) && YesOrNoEnum.Y.getCode().equals(
//                    qcxxItem.getFzxx().getKzxx().getSfSctbJcdlJjdjBz())) {
//                SbZzsJcdlqywgxpTzDTO dqWgxp = initWgxpTz(djxh, skssqq, skssqz, 
//                        qcxxItem.getFzxx().getKzxx().getSfSctbJcdlJjdjBz(), 
//                        qcxxItem.getFzxx().getCsxx().getJcdlSyjjdjl());
//                qcxxItem.getFzxx().getTzxx().getSbZzsJcdlqyWgxpTzList().add(dqWgxp);
//            }
//            qcxxItem.getFzxx().getKzxx().setJcdlZxsqq(qcxxItem.getFzxx().getTzxx().getSbZzsJcdlqyWgxpTzList().stream()
//                    .map(SbZzsJcdlqywgxpTzDTO::getSkssqq).min(Date::compareTo).orElse(null));
//            qcxxItem.getFzxx().getKzxx().setJcdlZdsqz(qcxxItem.getFzxx().getTzxx().getSbZzsJcdlqyWgxpTzList().stream()
//                    .map(SbZzsJcdlqywgxpTzDTO::getSkssqz).max(Date::compareTo).orElse(null));
//            if (JylxDmEnum.GZSB.getCode().equals(jylxDm)
//                    && (YesOrNoEnum.Y.getCode().equals(qcxxItem.getFzxx().getKzxx().getCwgzSfsc())
//                    || YesOrNoEnum.Y.getCode().equals(qcxxItem.getFzxx().getKzxx().getScJjdjYsb()))) {
//                qcxxItem.getYtxx().setSbZzsJcdlqywgxpTz(qcxxItem.getFzxx().getTzxx().getSbZzsJcdlqyWgxpTzList());
//            }
//        }
    }

    /**
     * 初始化外购芯片台账
     * @param djxh 登记序号
     * @param skssqq 税款所属期起
     * @param skssqz 税款所属期止
     * @param sfSctbJcdlJjdjBz 是否首次填报集成电路加计抵减标志
     * @param jcdlSyjjdjl 集成电路适用加计抵减率
     * @return 外购芯片台账信息
     */
    private SbZzsJcdlqywgxpTzDTO initWgxpTz(String djxh, String skssqq, String skssqz, String sfSctbJcdlJjdjBz, 
                                            String jcdlSyjjdjl) {
        SbZzsJcdlqywgxpTzDTO dqWgxp = new SbZzsJcdlqywgxpTzDTO();
        dqWgxp.setUuid("");
        dqWgxp.setSbuuid(GyUtils.getUuid());
        dqWgxp.setDjxh(djxh);
        dqWgxp.setSkssqq(DateUtil.parse(skssqq, NORM_DATE_FORMAT));
        dqWgxp.setSkssqz(DateUtil.parse(skssqz, NORM_DATE_FORMAT));
        dqWgxp.setJxse(BigDecimal.ZERO);
        dqWgxp.setYbxmjxse(BigDecimal.ZERO);
        dqWgxp.setYbxmwgxpjxse(BigDecimal.ZERO);
        dqWgxp.setJzjtjxse(BigDecimal.ZERO);
        dqWgxp.setJzjtwgxpjxse(BigDecimal.ZERO);
        dqWgxp.setLrrDm("");
        dqWgxp.setLrrq(new Date());
        dqWgxp.setXgrDm("");
        dqWgxp.setXgrq(new Date());
        dqWgxp.setZfbz1("");
        dqWgxp.setSfsctb(sfSctbJcdlJjdjBz);
        dqWgxp.setSyjjdjl(new BigDecimal(jcdlSyjjdjl));
        return dqWgxp;
    }

    /**
     * 获取加计抵减本期发生额列表
     * @param qtxx 其他数据
     * @param qcxxItem 期初数据
     */
    private void getJjdjBqfseList(ZzsybnsrCshQtqcsjDTO qtxx, YbnsrQcxxItemVO qcxxItem) {
        final String djxh = qcxxItem.getNsrxx().getDjxh();
        final String skssqq = DateUtil.format(qcxxItem.getSbsx().getSkssqq(), NORM_DATE_FORMAT);
        final String skssqz = DateUtil.format(qcxxItem.getSbsx().getSkssqz(), NORM_DATE_FORMAT);
        final String jzjtbz = qtxx.getJzjtBz();
        final String jylxDm = qcxxItem.getSbsx().getJylxDm();
        final String cwgzbz = JylxDmEnum.GZSB.getCode().equals(jylxDm) ? "cwgzbz" : "";
        final String nsrsbh = qcxxItem.getNsrxx().getNsrsbh();
        
//        final CommonResult<CompanyBasicInfoDTO> result = companyApi.basicInfo(djxh, nsrsbh);
//        String xzqhszDm = qcxxItem.getNsrxx().getZcdzxzqhszDm();
//        if (result.isSuccess() && GyUtils.isNotNull(result.getData())) {
//            xzqhszDm = result.getData().getXzqhszDm();
//        }
//        
//        final List<ZzsJjdjEnum> enumList = new ArrayList<>();
//        final YbnsrBqxxVO bqxx = qcxxItem.getFzxx().getBqxx();
//        List<YbnsrTzxxJjdjLjfseDTO> ybnsrTzxxJjdjLjfseList = new ArrayList<>();
//        if (YesOrNoEnum.Y.getCode().equals(bqxx.getJcdljjdjqy())) {
//            enumList.add(ZzsJjdjEnum.JCDL);
//        }
//        if (YesOrNoEnum.Y.getCode().equals(bqxx.getGymjjjdjqy())) {
//            enumList.add(ZzsJjdjEnum.GYMJ);
//        }
//        if (YesOrNoEnum.Y.getCode().equals(bqxx.getXjzzyjjdjqy())) {
//            enumList.add(ZzsJjdjEnum.XJZZY);
//        }
//        for (ZzsJjdjEnum jjdjEnum : enumList) {
//            
//            final ZzsybnsrJjdjbqfsecxReqDTO reqDTO = new ZzsybnsrJjdjbqfsecxReqDTO();
//            reqDTO.setDjxh(djxh);
//            reqDTO.setSkssqq(skssqq);
//            reqDTO.setSkssqz(skssqz);
//            reqDTO.setJzjtbz(jzjtbz);
//            reqDTO.setCwgzbz(cwgzbz);
//            reqDTO.setLbDm(jjdjEnum.getLbDm());
//
//            final SjjhDTO sjjhDTO = new SjjhDTO();
//            sjjhDTO.setYwuuid(GyUtils.getUuid());
//            sjjhDTO.setDjxh(djxh);
//            sjjhDTO.setSjjhlxDm(ZZSYBNSR_JJDJBQFSELJ_SJJHLXDM);
//            sjjhDTO.setYwbm(ZZSYBNSR_SJJH_YWBM);
//            sjjhDTO.setNsrsbh(nsrsbh);
//            sjjhDTO.setXzqhszDm(xzqhszDm);
//            sjjhDTO.setBwnr(JSONUtil.toJsonStr(reqDTO));
//            
//            final CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
//            if (sjjhResult.isSuccess() && GyUtils.isNotNull(sjjhResult.getData())) {
//                final String resJson = (String) sjjhResult.getData();
//                final ZzsybnsrJjdjbqfsecxRespDTO respDTO = JSONUtil.toBean(resJson, ZzsybnsrJjdjbqfsecxRespDTO.class);
//                final String code = respDTO.getCode();
//                if (!"00".equals(code)) {
//                    throw exception(JJDJBQFSE_FAIL);
//                }
//                final YbnsrTzxxJjdjLjfseDTO jjdjbqfseLjfseDTO = BeanUtils.toBean(respDTO, YbnsrTzxxJjdjLjfseDTO.class);
//                // 按照业务需求，ybxmjxselj取值逻辑调整 即征即退企业取ybxmjxselj，非即征即退企业取fb2jxselj
//                if (StrUtil.equals(bqxx.getZzsjzjtqy(), "Y")) {
//                    jjdjbqfseLjfseDTO.setYbxmjxselj(respDTO.getYbxmjxselj());
//                } else {
//                    jjdjbqfseLjfseDTO.setYbxmjxselj(respDTO.getFb2jxselj());
//                }
//                jjdjbqfseLjfseDTO.setJjdjzcXzjg(jjdjEnum.getXzjg());
//                ybnsrTzxxJjdjLjfseList.add(jjdjbqfseLjfseDTO);
//            }
//        }
//        qcxxItem.getFzxx().getTzxx().setYbnsrTzxxJjdjLjfseList(ybnsrTzxxJjdjLjfseList);
    }

    /**
     * 处理加计抵减累计已计提数
     * @param qcxxItem 期初数据
     */
    private void dealJjdjLjyjts(YbnsrQcxxItemVO qcxxItem) {
        final String djxh = qcxxItem.getNsrxx().getDjxh();
        final String ssqq = DateUtil.format(qcxxItem.getSbsx().getSkssqq(), NORM_DATE_FORMAT);
        final String nsrsbh = qcxxItem.getNsrxx().getNsrsbh();
        
//        String xzqhszDm = "";
//        final CommonResult<CompanyBasicInfoDTO> jgxxResult = companyApi.basicInfo(djxh, nsrsbh);
//        if (jgxxResult.isSuccess() && GyUtils.isNotNull(jgxxResult.getData())) {
//            xzqhszDm = jgxxResult.getData().getXzqhszDm();
//        }
//        
//        final ZzsybnsrBqfseljcxReqDTO reqDTO = new ZzsybnsrBqfseljcxReqDTO();
//        reqDTO.setDjxh(djxh);
//        reqDTO.setSsqq(ssqq);
//        
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setYwuuid(GyUtils.getUuid());
//        sjjhDTO.setSjjhlxDm(ZZSYBNSR_BQFSELJ_SJJHLXDM);
//        sjjhDTO.setYwbm(ZZSYBNSR_SJJH_YWBM);
//        sjjhDTO.setDjxh(djxh);
//        sjjhDTO.setNsrsbh(nsrsbh);
//        sjjhDTO.setXzqhszDm(xzqhszDm);
//        sjjhDTO.setBwnr(JSONUtil.toJsonStr(reqDTO));
//        
//        final CommonResult<Object> sjjhResult = sjjhService.saveSjjhJob(sjjhDTO);
//        if (sjjhResult.isSuccess() && GyUtils.isNotNull(sjjhResult.getData())) {
//            final String resJson = (String) sjjhResult.getData();
//            final ZzsybnsrBqfseljcxRespDTO respDTO = JSONUtil.toBean(resJson, ZzsybnsrBqfseljcxRespDTO.class);
//            if ("00".equals(respDTO.getCode())) {
//                qcxxItem.getFzxx().getTzxx().setJjdjLjyjts(respDTO.getWqyjtelj().toPlainString());
//                qcxxItem.getFzxx().getTzxx().setJjdjLjtje(respDTO.getWqtjelj());
//            } else {
//                qcxxItem.getFzxx().getTzxx().setJjdjLjyjts(BigDecimal.ZERO.toPlainString());
//                qcxxItem.getFzxx().getTzxx().setJjdjLjtje(BigDecimal.ZERO);
//            }
//        } else {
//            qcxxItem.getFzxx().getTzxx().setJjdjLjyjts(BigDecimal.ZERO.toPlainString());
//            qcxxItem.getFzxx().getTzxx().setJjdjLjtje(BigDecimal.ZERO);
//        }
    }

    /**
     * 获取加计抵减政策提示
     * @param qcxxItem 期初数据
     */
    private void getJjdjzcts(YbnsrQcxxItemVO qcxxItem) {
        final YbnsrBqxxVO bqxx = qcxxItem.getFzxx().getBqxx();
        if ("Y".equals(bqxx.getJcdljjdjqy()) && !"Y".equals(bqxx.getGymjjjdjqy()) && !"Y".equals(bqxx.getXjzzyjjdjqy())) {
            qcxxItem.getFzxx().getKzxx().setJjdjzctsNr("<p class=\"indent-2em\">根据《财政部 税务总局关于集成电路企业增值税加计抵减政策的通知》，自2023年1月1日至2027年12月31日，对集成电路设计、生产、封测、装备、材料企业，允许按照当期可抵扣进项税额加计15%，抵减应纳税额。</p>" +
                    "<p class=\"indent-2em\">经检测，您可以享受上述政策，请仔细对照政策文件，准确填报本期加计抵减相关栏次。</p>");
        } else if (!"Y".equals(bqxx.getJcdljjdjqy()) && "Y".equals(bqxx.getGymjjjdjqy()) && !"Y".equals(bqxx.getXjzzyjjdjqy())) {
            qcxxItem.getFzxx().getKzxx().setJjdjzctsNr("<p class=\"indent-2em\">根据《财政部 税务总局关于工业母机企业增值税加计抵减政策的通知》，自2023年1月1日至2027年12月31日，对生产销售先进工业母机产品的纳税人，允许按照当期可抵扣进项税额加计15%抵减企业应纳增值税税额。</p>" +
                    "<p class=\"indent-2em\">经检测，您可以享受上述政策，请仔细对照政策文件，准确填报本期加计抵减相关栏次。</p>");
        } else if (!"Y".equals(bqxx.getJcdljjdjqy()) && !"Y".equals(bqxx.getGymjjjdjqy()) && "Y".equals(bqxx.getXjzzyjjdjqy())) {
            qcxxItem.getFzxx().getKzxx().setJjdjzctsNr("<p class=\"indent-2em\">根据《财政部 税务总局关于先进制造业企业增值税加计抵减政策的公告》，自2023年1月1日至2027年12月31日，允许先进制造业企业按照当期可抵扣进项税额加计5%抵减应纳增值税税额。</p>" +
                    "<p class=\"indent-2em\">经检测，您可以享受上述政策，请仔细对照政策文件，准确填报本期加计抵减相关栏次。</p>");
        }
    }

    /**
     * 取增值税期末留抵退税额申请表数据中的最大所属期
     * @param djxh 登记序号
     * @return 最大所属期
     */
    private Date queryMaxZstySkssqzByDjxh(String djxh) {
        // TODO 取不到，先写死，默认为空
        log.info("取增值税期末留抵退税额申请表数据中的最大所属期，登记序号：" + djxh);
        /*
          select max(t.skssqz)
                  from hx_zgxt.zs_zzsqmldsets_sqb_2019 t
                  where t.zstybz = 1
                    and nvl(t.zfbz_1, 'N') = 'N'
                    and t.skssqz is not null
                    and t.djxh = #{djxh, jdbcType=NUMERIC}
         */
        return null;
    }

    /**
     * 组织参数信息
     * @param qcxxItem 期初信息
     */
    @Override
    public void organizeCsxx(YbnsrQcxxItemVO qcxxItem) {
        //纳税人的“增值税加计抵减企业”标签为N的时候才有可能提示，Y直接不提示
//        Date tsQssj = DateUtil.stringToDate(XtcsUtil.getWithDefaultValue(LzsXtcsBmConstant.SB_ZZS_JJDJTS_QSSJ, "2023-01-01"), DateUtil.DATE_PATTERN);
        Date tsQssj = DateUtil.parse("2023-01-01");
//        Date tsZzsj = DateUtil.stringToDate(XtcsUtil.getWithDefaultValue(LzsXtcsBmConstant.SB_ZZS_JJDJTS_ZZSJ, "2027-12-31"), DateUtil.DATE_PATTERN);
        Date tsZzsj = DateUtil.parse("2027-12-31");
        Date ssqq = qcxxItem.getSbsx().getSkssqq();
        Date ssqz = qcxxItem.getSbsx().getSkssqz();
        YbnsrQcKzxxVO kzxx = qcxxItem.getFzxx().getKzxx();
        YbnsrBqxxVO bqxx = qcxxItem.getFzxx().getBqxx();
        final YbnsrQcCsxxVO csxx = qcxxItem.getFzxx().getCsxx();
        if (DateUtil.compare(tsQssj, ssqq) <= 0 && DateUtil.compare(tsZzsj, ssqz) >= 0) {
            // 系统参数长度不够，这里先写死，后续改长度
            String tsnr = "";
            if (!YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbJcdlJjdjBz())
                    && !YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbGymjJjdjBz())
                    && !YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbXjzzyJjdjBz())
            ) {
                if ("N".equals(bqxx.getZzsjjdjqy())) {
                    tsnr = "<p style=\"text-indent: 2em\">\n" +
                            "        按照政策规定，自2019年4月1日至2023年12月31日，对生产、生活性服务业纳税人，可以适用加计抵减政策。\n" +
                            "        <b>2023年，生产性服务业纳税人加计抵减率为5%，生活性服务业纳税人加计抵减率为10%。</b>\n" +
                            "      </p>\n" +
                            "<p style=\"text-indent: 2em\">\n" +
                            "        生产、生活性服务业纳税人，是指提供邮政服务、电信服务、现代服务、生活服务（以下称四项服务）取得的销售额占全部销售额的比重超过50%的纳税人。四项服务的具体范围按照《销售服务、无形资产、不动产注释》（财税〔2016〕36号印发）执行。\n" +
                            "      </p>\n" +
                            "<p style=\"text-indent: 2em\">\n" +
                            "        如果您单位符合上述政策规定，可以通过填写《增值税适用加计抵减政策声明》，来确认适用加计抵减政策。\n" +
                            "      </p>";
                }
            } else {
                if (YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbJcdlJjdjBz()) && !YesOrNoEnum.Y.getCode().equals(kzxx.getCwgzBsZjyc())) {
                    tsnr += "<p style=\"text-indent: 2em\">\n" +
                            "自2023年1月1日至2027年12月31日，对集成电路设计、生产、封测、装备、材料企业，允许按照当期可抵扣进项税额加计15%，抵减应纳税额。企业外购芯片对应进项税额及按现行规定不得抵扣的进项税额，不得计提加计抵减额。您可以享受集成电路相关政策，请按照提示填写2023年1月至今相关进项税额和加计抵减额\n" +
                            "</p>\n";
                }
                if (YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbGymjJjdjBz()) && YesOrNoEnum.Y.getCode().equals(kzxx.getGymjGzBqfseSfkbj())) {
                    tsnr += "<p style=\"text-indent: 2em\">\n" +
                            "自2023年1月1日至2027年12月31日，对生产销售先进工业母机主机、关键功能部件、数控系统的增值税一般纳税人，允许按照当期可抵扣进项税额加计15%，抵减应纳税额。您可以享受工业母机加计抵减相关政策，请按照提示填写2023年1月至今相关进项税额和加计抵减额。\n" +
                            "</p>\n";
                }
                if (YesOrNoEnum.Y.getCode().equals(kzxx.getSfSctbXjzzyJjdjBz()) && YesOrNoEnum.Y.getCode().equals(kzxx.getXjzzyGzBqfseSfkbj())) {
                    tsnr += "<p style=\"text-indent: 2em\">\n" +
                            "自2023年1月1日至2027年12月31日，允许先进制造业企业按照当期可抵扣进项税额加计5%抵减应纳增值税税额。您可以享受该项政策，请按照提示填写2023年1月至今相关进项税额和加计抵减额。\n" +
                            "</p>";
                }
            }
            if (StrUtil.isNotBlank(tsnr)) {
                csxx.setJjdjSfts(YesOrNoEnum.Y.getCode());
            } else {
                csxx.setJjdjSfts(YesOrNoEnum.N.getCode());
            }
//            csxx.setJjdjTsnr(XtcsUtil.getWithDefaultValue(LzsXtcsBmConstant.LZS_ZZS_JJDJTS_TSNR, tsnr));
            csxx.setJjdjTsnr(tsnr);
            // TODO 没法获取系统参数做跳转
//            csxx.setJjdjTzdz(XtcsUtil.get(LzsXtcsBmConstant.LZS_ZZS_JJDJTS_TZDZ));
            csxx.setJjdjTzdz(null);
        } else {
            csxx.setJjdjSfts(YesOrNoEnum.N.getCode());
        }
    }
    
    private String getCktsXtzt(YbnsrQcxxItemVO qcxxItemVO) {
        final YbnsrQcNsrxxVO nsrxxVO = qcxxItemVO.getNsrxx();
        final YbnsrSbsxVO sbsxVO = qcxxItemVO.getSbsx();
        final YbnsrYtxxVO ytxxVO = qcxxItemVO.getYtxx();
        
        final String djxh = nsrxxVO.getDjxh();
        final String nsrsbh = nsrxxVO.getNsrsbh();
        final String skssqq = DateUtil.format(sbsxVO.getSkssqq(), DatePattern.NORM_DATE_PATTERN);
        final String skssqz = DateUtil.format(sbsxVO.getSkssqz(), DatePattern.NORM_DATE_PATTERN);
        
        String xzqhszDm = nsrxxVO.getZcdzxzqhszDm();
        
        final ZzsybnsrCktsXtztReqDTO reqDTO = new ZzsybnsrCktsXtztReqDTO();
        reqDTO.setDjxh(djxh);
        reqDTO.setSkssqq(skssqq);
        reqDTO.setSkssqz(skssqz);
        
        final CommonResult<CompanyBasicInfoDTO> basicInfo = companyApi.basicInfo(djxh, nsrsbh);
        if (basicInfo.isSuccess() && GyUtils.isNotNull(basicInfo.getData())) {
            xzqhszDm = basicInfo.getData().getXzqhszDm();
        }
        xzqhszDm = SfEnum.getSsjXzqhszDmByXzqhszDm(xzqhszDm);
        
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setYwbm(ZZSYBNSR_SJJH_YWBM);
        sjjhDTO.setSjjhlxDm(ZZSYBNSR_CXCKSCQYMDTSBSHZT_SJJHLXDM);
        sjjhDTO.setDjxh(djxh);
        sjjhDTO.setNsrsbh(nsrsbh);
        sjjhDTO.setXzqhszDm(xzqhszDm);
        sjjhDTO.setBwnr(JsonUtils.toJson(reqDTO));
        
        String shzt = null;
        try {
            final CommonResult<Object> sjjhJob = sjjhService.saveSjjhJob(sjjhDTO);
            if (!sjjhJob.isSuccess()) {
                shzt = "02";
            } else {
                final String resp = (String) sjjhJob.getData();
                final ZzsybnsrCktsXtztRespDTO respDTO = JsonUtils.toBean(resp, ZzsybnsrCktsXtztRespDTO.class);
                if ("00".equals(respDTO.getReturncode()) && "1".equals(respDTO.getSbshzt())) {
                    // 计算期末留抵税额
                    BigDecimal qmldse = BigDecimal.ZERO;
                    if (GyUtils.isNotNull(ytxxVO) && GyUtils.isNotNull(ytxxVO.getSbZzsYbnsr())) {
                        final SbZzsYbnsrDTO zb_1 = CollectionUtil.findOneByField(ytxxVO.getSbZzsYbnsr(), 
                                "ewblxh", "1");
                        final SbZzsYbnsrDTO zb_3 = CollectionUtil.findOneByField(ytxxVO.getSbZzsYbnsr(), 
                                "ewblxh", "3");
                        qmldse = NumberUtil.add(NumberUtil.nullToZero(zb_1.getQmldse()), 
                                NumberUtil.nullToZero(zb_3.getQmldse()));
                    }
                    if (qmldse.compareTo(BigDecimal.ZERO) != 0) {
                        shzt = "01";
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取出口生产企业免抵退税申报审核状态失败，失败原因：", e);
            shzt = "02";
        }
        return shzt;
    }

}
