package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税纳税申报表（一般纳税人适用）
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zbGridlbVO", propOrder = { "ewblxh", "lmc", "asysljsxse", "yshwxse", "yslwxse", "syslNsjctzxse", "ajybfjsxse", "jybfNsjctzxse", "mdtbfckxse", "msxse", "mshwxse", "mslwxse", "xxse", "jxse", "sqldse", "jxsezc", "mdtytse", "syslNsjcybjse", "ydksehj", "sjdkse", "ynse", "qmldse", "jybfYnse", "jybfNsjcybjse", "ynsejze", "ynsehj", "qcwjse", "ssckkjzyjkstse", "bqyjse", "fcyjse", "ckkjzyjksyjse", "bqjnsqynse", "bqjnqjse", "qmwjse", "qmwjseQjse", "bqybtse", "jzjtsjtse", "qcwjcbse", "bqrkcbse", "qmwjcbse", "sqr", "smrxm", "slr", "bqybtsecjs", "bqybtsejyfj", "bqybtsedfjyfj" })
@Getter
@Setter
public class ZbGridlbVO {
    /**
     * 二维表列序号
     */
    protected long ewblxh;

    /**
     * 列名称
     */
    @XmlElement(nillable = true, required = true)
    protected String lmc;

    /**
     * 按适用税率计税销售额
     */
    @XmlElement(nillable=true)
    protected BigDecimal asysljsxse;

    /**
     * 应税货物销售额
     */
    @XmlElement(nillable=true)
    protected BigDecimal yshwxse;

    /**
     * 应税劳务销售额
     */
    @XmlElement(nillable=true)
    protected BigDecimal yslwxse;

    /**
     * 纳税检查调整的销售额_适用税率
     */
    @XmlElement(nillable=true)
    protected BigDecimal syslNsjctzxse;

    /**
     * 按简易办法计税销售额
     */
    @XmlElement(nillable=true)
    protected BigDecimal ajybfjsxse;

    /**
     * 纳税检查调整的销售额_简易办法
     */
    @XmlElement(nillable=true)
    protected BigDecimal jybfNsjctzxse;

    /**
     * 免抵退办法出口销售额
     */
    @XmlElement(nillable=true)
    protected BigDecimal mdtbfckxse;

    /**
     * 免税销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal msxse;

    /**
     * 免税货物销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal mshwxse;

    /**
     * 免税劳务销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal mslwxse;

    /**
     * 销项税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal xxse;

    /**
     * 进项税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal jxse;

    /**
     * 上期留抵税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal sqldse;

    /**
     * 进项税额转出
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal jxsezc;

    /**
     * 免、抵、退应退税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal mdtytse;

    /**
     * 按适用税率计算的纳税检查应补缴税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal syslNsjcybjse;

    /**
     * 应抵扣税额合计
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ydksehj;

    /**
     * 实际抵扣税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal sjdkse;

    /**
     * 应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ynse;

    /**
     * 期末留抵税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmldse;

    /**
     * 简易计税办法计算的应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal jybfYnse;

    /**
     * 按简易计税办法计算的纳税检查应补缴税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal jybfNsjcybjse;

    /**
     * 应纳税额减征额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ynsejze;

    /**
     * 应纳税额合计
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ynsehj;

    /**
     * 期初未缴税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qcwjse;

    /**
     * 实收出口开具专用缴款书退税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ssckkjzyjkstse;

    /**
     * 本期预缴税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqyjse;

    /**
     * 分次预缴税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal fcyjse;

    /**
     * 出口开具专用缴款书预缴税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ckkjzyjksyjse;

    /**
     * 本期缴纳上期应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqjnsqynse;

    /**
     * 本期缴纳欠缴税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqjnqjse;

    /**
     * 期末未缴税额（多缴为负数）
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmwjse;

    /**
     * 其中:欠缴税额≥0
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmwjseQjse;

    /**
     * 本期应补退税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqybtse;

    /**
     * 即征即退实际退税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal jzjtsjtse;

    /**
     * 期初未缴查补税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qcwjcbse;

    /**
     * 本期入库查补税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqrkcbse;

    /**
     * 期末未缴款补税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmwjcbse;

    /**
     * 授权人
     */
    @XmlElement(nillable = true, required = true)
    protected String sqr;

    /**
     * 声明人
     */
    @XmlElement(nillable = true, required = true)
    protected String smrxm;

    /**
     * 受理人
     */
    @XmlElement(nillable = true, required = true)
    protected String slr;

    /**
     * 城市维护建设税本期应补（退）税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqybtsecjs;

    /**
     * 教育费附加本期应补（退）费额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqybtsejyfj;

    /**
     * 地方教育附加本期应补（退）费额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqybtsedfjyfj;
}