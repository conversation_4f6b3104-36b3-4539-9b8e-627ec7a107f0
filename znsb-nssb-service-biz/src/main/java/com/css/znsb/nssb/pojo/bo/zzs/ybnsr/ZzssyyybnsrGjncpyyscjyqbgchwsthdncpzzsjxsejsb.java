package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb", propOrder = { "sbbhead", "gjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid" })
@XmlSeeAlso({ ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw.ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb.class })
@Getter
@Setter
public class ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    @XmlElement(nillable = true, required = true)
    protected GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid gjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO" })
    @Getter
    @Setter
    public static class GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO> gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO;

        /**
         * Gets the value of the gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getGjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO}
         */
        public List<GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO> getGjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO() {
            if (gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO == null) {
                gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO = new ArrayList<GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO>();
            }
            return this.gjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO;
        }
    }
}