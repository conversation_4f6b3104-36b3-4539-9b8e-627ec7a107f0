package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《电力企业增值税销项税额和进项税额传递单》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsnssbb_dlqyzzsxxsehjxsecddywbw", propOrder = { "dlqyzzsxxsehjxsecdd" })
@Getter
@Setter
public class ZzsnssbbDlqyzzsxxsehjxsecddywbw extends TaxDoc {
    @XmlElement(nillable = true, required = true)
    protected Dlqyzzsxxsehjxsecdd dlqyzzsxxsehjxsecdd;
}