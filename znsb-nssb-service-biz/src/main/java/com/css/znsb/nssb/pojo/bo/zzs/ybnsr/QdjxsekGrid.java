package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 取得进项税额情况
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "qdjxsekGrid", propOrder = { "qdjxseqkGridlb" })
@Getter
@Setter
public class QdjxsekGrid {
    @XmlElement(nillable = true, required = true)
    protected List<QdjxsekGridlb> qdjxseqkGridlb;

    /**
     * Gets the value of the qdjxseqkGridlb property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the qdjxseqkGridlb property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getQdjxseqkGridlb().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link QdjxsekGridlb}
     */
    public List<QdjxsekGridlb> getQdjxseqkGridlb() {
        if (qdjxseqkGridlb == null) {
            qdjxseqkGridlb = new ArrayList<QdjxsekGridlb>();
        }
        return this.qdjxseqkGridlb;
    }
}