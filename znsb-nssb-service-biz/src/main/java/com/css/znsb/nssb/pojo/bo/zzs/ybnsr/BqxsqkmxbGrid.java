package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

/**
 * 《增值税纳税申报表附表一（本期销售情况明细表）》业务报文
 *
 * <p>bqxsqkmxbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="bqxsqkmxbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ewbhxh" type="{http://www.chinatax.gov.cn/dataspec/}ewbhxh" minOccurs="0"/>
 *         &lt;element name="kjskzzszyfpXse" type="{http://www.chinatax.gov.cn/dataspec/}kjskzzszyfpXse" minOccurs="0"/>
 *         &lt;element name="kjskzzszyfpXxynse" type="{http://www.chinatax.gov.cn/dataspec/}kjskzzszyfpXxynse" minOccurs="0"/>
 *         &lt;element name="kjqtfpXse" type="{http://www.chinatax.gov.cn/dataspec/}kjqtfpXse" minOccurs="0"/>
 *         &lt;element name="kjqtfpXxynse" type="{http://www.chinatax.gov.cn/dataspec/}kjqtfpXxynse" minOccurs="0"/>
 *         &lt;element name="wkjfpXse" type="{http://www.chinatax.gov.cn/dataspec/}wkjfpXse" minOccurs="0"/>
 *         &lt;element name="wkjfpXxynse" type="{http://www.chinatax.gov.cn/dataspec/}wkjfpXxynse" minOccurs="0"/>
 *         &lt;element name="nsjctzdxse" type="{http://www.chinatax.gov.cn/dataspec/}nsjctzdxse" minOccurs="0"/>
 *         &lt;element name="nsjctzXxynse" type="{http://www.chinatax.gov.cn/dataspec/}nsjctzXxynse" minOccurs="0"/>
 *         &lt;element name="xse" type="{http://www.chinatax.gov.cn/dataspec/}xse" minOccurs="0"/>
 *         &lt;element name="hjXxynse" type="{http://www.chinatax.gov.cn/dataspec/}hjXxynse" minOccurs="0"/>
 *         &lt;element name="jshj" type="{http://www.chinatax.gov.cn/dataspec/}jshj" minOccurs="0"/>
 *         &lt;element name="ysfwkcxmbqsjkcje" type="{http://www.chinatax.gov.cn/dataspec/}ysfwkcxmbqsjkcje" minOccurs="0"/>
 *         &lt;element name="kchHsmsxse" type="{http://www.chinatax.gov.cn/dataspec/}kchHsmsxse" minOccurs="0"/>
 *         &lt;element name="kchXxynse" type="{http://www.chinatax.gov.cn/dataspec/}kchXxynse" minOccurs="0"/>
 *         &lt;element name="hmc" type="{http://www.chinatax.gov.cn/dataspec/}hmc" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bqxsqkmxbGrid", propOrder = { "ewbhxh", "kjskzzszyfpXse", "kjskzzszyfpXxynse", "kjqtfpXse", "kjqtfpXxynse", "wkjfpXse", "wkjfpXxynse", "nsjctzdxse", "nsjctzXxynse", "xse", "hjXxynse", "jshj", "ysfwkcxmbqsjkcje", "kchHsmsxse", "kchXxynse", "hmc" })
public class BqxsqkmxbGrid {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 开具税控增值税专用发票_销售额
     */
    protected BigDecimal kjskzzszyfpXse;

    /**
     * 开具税控增值税专用发票_销项应纳税额
     */
    protected BigDecimal kjskzzszyfpXxynse;

    /**
     * 开具其他发票_销售额
     */
    protected BigDecimal kjqtfpXse;

    /**
     * 开具其他发票_销项应纳税额
     */
    protected BigDecimal kjqtfpXxynse;

    /**
     * 未开具发票_销售额
     */
    protected BigDecimal wkjfpXse;

    /**
     * 未开具发票_销项应纳税额
     */
    protected BigDecimal wkjfpXxynse;

    /**
     * 纳税检查调整的销售额
     */
    protected BigDecimal nsjctzdxse;

    /**
     * 纳税检查调整_销项应纳税额
     */
    protected BigDecimal nsjctzXxynse;

    /**
     * 销售额
     */
    protected BigDecimal xse;

    /**
     * 合计_销项应纳税额
     */
    protected BigDecimal hjXxynse;

    /**
     * 价税合计
     */
    protected BigDecimal jshj;

    /**
     * 应税服务扣除项目本期实际扣除金额
     */
    protected BigDecimal ysfwkcxmbqsjkcje;

    /**
     * 扣除后_含税免税销售额
     */
    protected BigDecimal kchHsmsxse;

    /**
     * 扣除后_销项应纳税额
     */
    protected BigDecimal kchXxynse;

    /**
     * 行名称
     */
    protected String hmc;

    /**
     * 获取ewbhxh属性的值。
     * <p>
     * 二维表行序号
     */
    public Long getEwbhxh() {
        return ewbhxh;
    }

    /**
     * 设置ewbhxh属性的值。
     */
    public void setEwbhxh(Long value) {
        this.ewbhxh = value;
    }

    /**
     * 获取kjskzzszyfpXse属性的值。
     * <p>
     * 开具税控增值税专用发票_销售额
     */
    public BigDecimal getKjskzzszyfpXse() {
        return kjskzzszyfpXse;
    }

    /**
     * 设置kjskzzszyfpXse属性的值。
     */
    public void setKjskzzszyfpXse(BigDecimal value) {
        this.kjskzzszyfpXse = value;
    }

    /**
     * 获取kjskzzszyfpXxynse属性的值。
     * <p>
     * 开具税控增值税专用发票_销项应纳税额
     */
    public BigDecimal getKjskzzszyfpXxynse() {
        return kjskzzszyfpXxynse;
    }

    /**
     * 设置kjskzzszyfpXxynse属性的值。
     */
    public void setKjskzzszyfpXxynse(BigDecimal value) {
        this.kjskzzszyfpXxynse = value;
    }

    /**
     * 获取kjqtfpXse属性的值。
     * <p>
     * 开具其他发票_销售额
     */
    public BigDecimal getKjqtfpXse() {
        return kjqtfpXse;
    }

    /**
     * 设置kjqtfpXse属性的值。
     */
    public void setKjqtfpXse(BigDecimal value) {
        this.kjqtfpXse = value;
    }

    /**
     * 获取kjqtfpXxynse属性的值。
     * <p>
     * 开具其他发票_销项应纳税额
     */
    public BigDecimal getKjqtfpXxynse() {
        return kjqtfpXxynse;
    }

    /**
     * 设置kjqtfpXxynse属性的值。
     */
    public void setKjqtfpXxynse(BigDecimal value) {
        this.kjqtfpXxynse = value;
    }

    /**
     * 获取wkjfpXse属性的值。
     * <p>
     * 未开具发票_销售额
     */
    public BigDecimal getWkjfpXse() {
        return wkjfpXse;
    }

    /**
     * 设置wkjfpXse属性的值。
     */
    public void setWkjfpXse(BigDecimal value) {
        this.wkjfpXse = value;
    }

    /**
     * 获取wkjfpXxynse属性的值。
     * <p>
     * 未开具发票_销项应纳税额
     */
    public BigDecimal getWkjfpXxynse() {
        return wkjfpXxynse;
    }

    /**
     * 设置wkjfpXxynse属性的值。
     */
    public void setWkjfpXxynse(BigDecimal value) {
        this.wkjfpXxynse = value;
    }

    /**
     * 获取nsjctzdxse属性的值。
     * <p>
     * 纳税检查调整的销售额
     */
    public BigDecimal getNsjctzdxse() {
        return nsjctzdxse;
    }

    /**
     * 设置nsjctzdxse属性的值。
     */
    public void setNsjctzdxse(BigDecimal value) {
        this.nsjctzdxse = value;
    }

    /**
     * 获取nsjctzXxynse属性的值。
     * <p>
     * 纳税检查调整_销项应纳税额
     */
    public BigDecimal getNsjctzXxynse() {
        return nsjctzXxynse;
    }

    /**
     * 设置nsjctzXxynse属性的值。
     */
    public void setNsjctzXxynse(BigDecimal value) {
        this.nsjctzXxynse = value;
    }

    /**
     * 获取xse属性的值。
     * <p>
     * 销售额
     */
    public BigDecimal getXse() {
        return xse;
    }

    /**
     * 设置xse属性的值。
     */
    public void setXse(BigDecimal value) {
        this.xse = value;
    }

    /**
     * 获取hjXxynse属性的值。
     * <p>
     * 合计_销项应纳税额
     */
    public BigDecimal getHjXxynse() {
        return hjXxynse;
    }

    /**
     * 设置hjXxynse属性的值。
     */
    public void setHjXxynse(BigDecimal value) {
        this.hjXxynse = value;
    }

    /**
     * 获取jshj属性的值。
     * <p>
     * 价税合计
     */
    public BigDecimal getJshj() {
        return jshj;
    }

    /**
     * 设置jshj属性的值。
     */
    public void setJshj(BigDecimal value) {
        this.jshj = value;
    }

    /**
     * 获取ysfwkcxmbqsjkcje属性的值。
     * <p>
     * 应税服务扣除项目本期实际扣除金额
     */
    public BigDecimal getYsfwkcxmbqsjkcje() {
        return ysfwkcxmbqsjkcje;
    }

    /**
     * 设置ysfwkcxmbqsjkcje属性的值。
     */
    public void setYsfwkcxmbqsjkcje(BigDecimal value) {
        this.ysfwkcxmbqsjkcje = value;
    }

    /**
     * 获取kchHsmsxse属性的值。
     * <p>
     * 扣除后_含税免税销售额
     */
    public BigDecimal getKchHsmsxse() {
        return kchHsmsxse;
    }

    /**
     * 设置kchHsmsxse属性的值。
     */
    public void setKchHsmsxse(BigDecimal value) {
        this.kchHsmsxse = value;
    }

    /**
     * 获取kchXxynse属性的值。
     * <p>
     * 扣除后_销项应纳税额
     */
    public BigDecimal getKchXxynse() {
        return kchXxynse;
    }

    /**
     * 设置kchXxynse属性的值。
     */
    public void setKchXxynse(BigDecimal value) {
        this.kchXxynse = value;
    }

    /**
     * 获取hmc属性的值。
     * <p>
     * 行名称
     */
    public String getHmc() {
        return hmc;
    }

    /**
     * 设置hmc属性的值。
     */
    public void setHmc(String value) {
        this.hmc = value;
    }
}