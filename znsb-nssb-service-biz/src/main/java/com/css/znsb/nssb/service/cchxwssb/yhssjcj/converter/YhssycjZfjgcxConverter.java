package com.css.znsb.nssb.service.cchxwssb.yhssjcj.converter;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhHandlerDTO;
import com.css.znsb.framework.sjjh.service.sjjh.fw.converter.SjjhDataConverter;
import com.css.znsb.nssb.pojo.dto.sbbc.SbbcGyRespDTO;
import com.css.znsb.nssb.pojo.dto.sbjgcx.LqfzptSbjgcxReqDTO;
import com.css.znsb.nssb.pojo.dto.yhssycj.jggy.LqBcYhsJgCxResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants.ERROR;

@Slf4j
@Service(value = "YhssycjZfjgcxConverter")
public class YhssycjZfjgcxConverter implements SjjhDataConverter {
    /**
     * 转换
     *
     * @param req 请求
     * @return
     */
    @Override
    public String convertReq(SjjhHandlerDTO req) {
        log.debug("=====乐企辅助平台作废结果查询转换请求开始=====");
        log.debug("待转换的入参：" + JsonUtils.toJson(req));
        final LqfzptSbjgcxReqDTO sbcxReq = new LqfzptSbjgcxReqDTO();
        final SbbcGyRespDTO respDTO = (SbbcGyRespDTO) req.getScResult();
        sbcxReq.setPclsh(respDTO.getPclsh());
        final String resJson = JsonUtils.toJson(sbcxReq);
        log.debug("转换后的出参：" + resJson);
        log.debug("=====乐企辅助平台作废结果查询转换请求结束=====");
        return resJson;
    }

    @Override
    public CommonResult<Object> convertResp(String resp) {
        log.debug("=====乐企辅助平台作废结果查询转换响应开始=====");
        log.debug("待转换的入参：" + resp);
        CommonResult<Object> result = new CommonResult<>();
        //返回报文包含 data 则为乐企返回报文，否则为代理服务返回的成功报文，不做处理
        final LqBcYhsJgCxResponseDTO sbcxResp = JsonUtils.toBean(resp, LqBcYhsJgCxResponseDTO.class);
        final String returncode = sbcxResp.getReturncode();
        boolean success = false;
        if ("00".equals(returncode)) {
            // 成功状态
            success = true;
        }
        if (success) {
            result = CommonResult.success(sbcxResp);
        } else {
            result = CommonResult.error(ERROR.getCode(), ERROR.getMsg(), sbcxResp);
        }
        log.debug("转换后的出参：" + JsonUtils.toJson(result));
        log.debug("=====乐企辅助平台作废结果查询转换响应结束=====");
        return result;
    }
}
