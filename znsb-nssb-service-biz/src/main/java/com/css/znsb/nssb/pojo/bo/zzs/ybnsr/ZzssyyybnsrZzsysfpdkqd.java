package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税运输发票抵扣清单
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_zzsysfpdkqd", propOrder = { "sbbhead", "dkqdGrid" })
@XmlSeeAlso({ ZzssyyybnsrZzsysfpdkqdywbw.ZzssyyybnsrZzsysfpdkqd.class })
@Getter
@Setter
public class ZzssyyybnsrZzsysfpdkqd {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 抵扣清单
     */
    @XmlElement(nillable = true, required = true)
    protected DkqdGrid dkqdGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "dkqdGridlbVO" })
    @Getter
    @Setter
    public static class DkqdGrid {
        @XmlElement(nillable = true, required = true)
        protected List<DkqdGridlbVO> dkqdGridlbVO;

        /**
         * Gets the value of the dkqdGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the dkqdGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getDkqdGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link DkqdGridlbVO}
         */
        public List<DkqdGridlbVO> getDkqdGridlbVO() {
            if (dkqdGridlbVO == null) {
                dkqdGridlbVO = new ArrayList<DkqdGridlbVO>();
            }
            return this.dkqdGridlbVO;
        }
    }
}