package com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.Base64Utils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.nsrxx.*;
import com.css.znsb.nssb.api.ssjg.SsjgApi;
import com.css.znsb.nssb.constants.GyConstants;
import com.css.znsb.nssb.constants.SfEnum;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.gt3invoker.Gt3Invoker;
import com.css.znsb.nssb.mapper.cchxwsnssb.CxsSsjgMapper;
import com.css.znsb.nssb.mapper.cchxwsnssb.ZnsbNssbCxstysbxxMapper;
import com.css.znsb.nssb.mapper.fcscztdsyssycj.SbCxsFtdcjglbMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxstyjmxxDO;
import com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxstysbxxDO;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjb;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.*;
import com.css.znsb.nssb.pojo.dto.cchxwssb.ccssycj.CxstysbNsrxxDTO;
import com.css.znsb.nssb.pojo.dto.gy.lqpt.jbrxx.GyJbrxxDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.ZnsbNssbSbrwDTO;
import com.css.znsb.nssb.pojo.dto.ssjg.SsjgReqDTO;
import com.css.znsb.nssb.pojo.dto.yhssycj.jyss.YhsTysbbYtReqDTO;
import com.css.znsb.nssb.pojo.dto.zcxx.ZnsbNssbZcxxDTO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.*;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.*;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.sbxxgrid.SymxGridlbVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10736.HXZGSB10736Request;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10736.SbxxGridlb10736VO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10736.SymxGridlb10736VO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10745.HXZGSB10745Request;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10745.SbxxGridlb745VO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10745.SymxGridlb745VO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10774.HXZGSB10774Request;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10774.YhssbsyGridlbVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB11056.HXZGSB11056Request;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB11056.HXZGSB11056Response;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.ZS00377.HXZGZS00377Request;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.ZS00377.HXZGZS00377Response;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.gy.CxstysbNsrxx;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.gy.CxstysbSaveReturnVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.gy.SBSaveReturnVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.gy.SbCxstysbSlxx;
import com.css.znsb.nssb.pojo.vo.cchxwssb.gy.ResVO;
import com.css.znsb.nssb.pojo.vo.hbs.hxzg.HbsCjSyxx;
import com.css.znsb.nssb.pojo.vo.hxzg.sb01700.CzjzsymxbGridlbVO;
import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.*;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.CztdsysService;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.FcsCztdsysService;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.FcsService;
import com.css.znsb.nssb.service.cchxwssb.yhssjcj.YhssycjNewService;
import com.css.znsb.nssb.service.cchxwssb.yhssjcj.YhssycjService;
import com.css.znsb.nssb.service.cchxwssb.yhssjcj.ZnsbJyssSsSsjgYhsService;
import com.css.znsb.nssb.service.cchxwssb.yyssycj.YyssycjService;
import com.css.znsb.nssb.service.gy.DataUpdateService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.css.znsb.nssb.service.zcxx.ZnsbNssbZcxxService;
import com.css.znsb.nssb.service.zfjg.ZfjgxxService;
import com.css.znsb.nssb.util.SjryUtil;
import com.css.znsb.nssb.utils.*;
import com.css.znsb.xxzx.api.znsbwf.ZnsbWfAPI;
import com.css.znsb.xxzx.pojo.znsbwf.ZnsbInitProcDataVO;
import com.css.znsb.xxzx.pojo.znsbwf.ZnsbWfDataResVO;
import com.css.znsb.xxzx.pojo.znsbwf.ZnsbWfDataVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.css.znsb.nssb.constants.BcztConstants.*;
import static com.css.znsb.nssb.constants.SbrwztConstants.*;
import static com.css.znsb.nssb.constants.enums.ZsxmDmEnum.*;


/**
 * 财产和行为税纳税申报
 */
@Slf4j
@Service
public class CchxwsnssbServiceImpl implements CchxwsnssbService {

    /**
     * 简易处罚任务类型
     */
    private static final String JYCFRZL = "jycfrw";

    /**
     * 用户类型_自然人
     */
    private static final String YHLX_ZRR = "1";

    /**
     * 用户类型_纳税人
     */
    private static final String YHLX_NSR = "2";

    @Resource
    NsrxxApi nsrxxApi;
    @Resource
    FcsService fcsService;
    @Resource
    CztdsysService cztdsysService;
    @Resource
    SjjhService sjjhService;
    @Resource
    YhssycjService yhssycjService;
    @Resource
    ZnsbJyssSsSsjgYhsService znsbJyssSsSsjgYhsService;
    @Resource
    ZnsbNssbSbrwMapper znsbNssbSbrwMapper;
    @Resource
    ZnsbNssbSbrwService znsbNssbSbrwService;
    @Resource
    ZnsbWfAPI znsbWfAPI;
    @Resource
    private ZnsbNssbZcxxService znsbNssbZcxxService;
    @Resource
    CxsGyUtils cxsGyUtils;
    @Resource
    DataUpdateService dataUpdateService;
    @Resource
    CxsSsjgMapper cxsSsjgMapper;
    @Resource
    YyssycjService yyssycjService;
    @Resource
    CompanyApi companyApi;
    @Resource
    CchxwsnssbService cchxwsnssbService;
    @Resource
    SbCxsFtdcjglbMapper sbCxsFtdcjglbMapper;
    @Resource
    ZnsbNssbCxstysbxxMapper znsbNssbCxstysbxxMapper;
    @Resource
    YhssycjNewService yhssycjNewService;
    @Resource
    FcsCztdsysService fcsCztdsysService;
    @Resource
    ZnsbNssbCxstysbzbService znsbNssbCxstysbzbService;
    @Resource
    ZnsbNssbCxstysbxxService znsbNssbCxstysbxxServic;
    @Resource
    ZnsbNssbCxstyjmxxService znsbNssbCxstyjmxxService;
    @Resource
    SsjgApi ssjgApi;
    @Resource
    CchxwshbsbService cchxwshbsbService;

    /**
     * 查询财产和行为税税款所属期
     *
     * @param cchxwsnssbInitReqVO 入参
     * @return 返回值
     */
    @Override
    @SneakyThrows
    public CchxwsnssbInitResVO cxsInit(CchxwsnssbInitReqVO cchxwsnssbInitReqVO) {
        log.info("----------------cxsInit----------------------");
        //通过修改dqrq开放测试口供测试使用
        //增加系统参数控制测试入口
        String dqrqStr = DateUtil.format(new Date(), "yyyy-MM-dd");
        final Date dqrq = DateUtil.parse(dqrqStr, "yyyy-MM-dd");
        final String djxh = cchxwsnssbInitReqVO.getDjxh();//登记序号
        final String yhlx = cchxwsnssbInitReqVO.getYhlx();//用户类型
        final String ftbz = cchxwsnssbInitReqVO.getFtbz();//房土标志-税源单用
        final String zgswjgdm = cchxwsnssbInitReqVO.getZgswskfj_dm();//主管税务机关所科分局代码，自然人为省级机关

        //返回值
        final CchxwsnssbInitResVO initResVO = new CchxwsnssbInitResVO();
        //返回值：公共信息
        final CchxwsnssbInitResZbVO initResZbVO = new CchxwsnssbInitResZbVO();
        initResZbVO.setDjxh(djxh);
        initResZbVO.setNsrsbh(cchxwsnssbInitReqVO.getNsrsbh());
        initResZbVO.setNsrmc(cchxwsnssbInitReqVO.getNsrmc());
        initResZbVO.setZgswskfjDm(cchxwsnssbInitReqVO.getZgswskfj_dm());
        initResZbVO.setYhlx(cchxwsnssbInitReqVO.getYhlx());
        initResZbVO.setNsrztDm(cchxwsnssbInitReqVO.getNsrztDm());
        initResZbVO.setShxydm(cchxwsnssbInitReqVO.getShxydm());

        //卡片数据
        final List<CchxwsnssbInitResMxVO> initResMxVOList = new ArrayList<>();
        //当前需初始化加载项目
        final StringBuilder cxsXMStr = new StringBuilder();
        //自然人需初始化加载项目返回值
        final List<String> zsxmDmListRes = new ArrayList<>();
         if ("10110,10112".equals(ftbz) || "10111".equals(ftbz)) {
            if ("10110,10112".equals(ftbz)) {
                //房土初始化分支
                CchxwsnssbInitResMxVO mp1 = new CchxwsnssbInitResMxVO();
                CchxwsnssbInitResMxVO mp2 = new CchxwsnssbInitResMxVO();
                mp1.setZsxmdm("10110");
                mp1.setSzacsbbj("N");
                mp2.setZsxmdm("10112");
                mp2.setSzacsbbj("N");
                cxsXMStr.append("10110,10112");
                initResMxVOList.add(mp1);
                initResMxVOList.add(mp2);
            } else {
                CchxwsnssbInitResMxVO mp1 = new CchxwsnssbInitResMxVO();
                mp1.setZsxmdm(ftbz);
                mp1.setSzacsbbj("Y");
                cxsXMStr.append(ftbz);
                initResMxVOList.add(mp1);
            }
        } else {
            //纳税人分支
            for (CxsSbbEnum cxsSbbEnum : CxsSbbEnum.values()) {
                CchxwsnssbInitResMxVO mp = new CchxwsnssbInitResMxVO();
                mp.setZsxmdm(cxsSbbEnum.zsxmDm);
                mp.setSzacsbbj(cxsSbbEnum.szacsbbj);
                cxsXMStr.append(",").append(cxsSbbEnum.zsxmDm);
                initResMxVOList.add(mp);
            }
        }

        //2024.03.07，前置获取
        //获取本上级税务机关
        final List<String> swjgDmList = new ArrayList<>();
        FtsCxsUtils.getSwjgList(swjgDmList, zgswjgdm, new HashMap<>());

        log.info("cxsInit_dateB1_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        //获取全量码表数据，并根据税务机关分组
        final List<Map<String, Object>> sbqxwbAll = FtsCxsUtils.getAllCacheData("cs_gy_sbqxwh_sbqx");//申报期限维护表，700条左右
        final List<Map<String, Object>> jjrAll = FtsCxsUtils.getAllCacheData("cs_gy_jjr");//节假日配置表，4000条左右
        final List<Map<String, Object>> zqtzAll = FtsCxsUtils.getAllCacheData("cs_gy_zqtz");//征期调整配置表。50条
        final Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup = sbqxwbAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        final Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup = jjrAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        final Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup = zqtzAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        log.info("cxsInit_dateB2_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        //税费种认定信息
        final List<SfzrdmxxxVO> aqRdList = new ArrayList<>();//除房产税的九税按期
        final List<SfzrdmxxxVO> acRdList = new ArrayList<>();//印花、环保、耕占、契税、土增按次
        final List<SfzrdmxxxVO> zysRdList = new ArrayList<>();//按次、按期资源
        final List<SfzrdmxxxVO> fcsRdList = new ArrayList<>();//按期房产
        //查询税费种认定信息
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(cchxwsnssbInitReqVO.getNsrsbh());
        znsbMhzcQyjbxxmxReqVO.setDjxh(cchxwsnssbInitReqVO.getDjxh());
        CommonResult<ZnsbMhzcQyjbxxmxResVO> cxSfzrdxxReqVO = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        if (!GyUtils.isNull(cxSfzrdxxReqVO) && !GyUtils.isNull(cxSfzrdxxReqVO.getData()) && !GyUtils.isNull(cxSfzrdxxReqVO.getData().getSfzrdmxxx())) {
            final List<SfzrdmxxxVO> sfzrdxxList = cxSfzrdxxReqVO.getData().getSfzrdmxxx();
            //过滤税费种认定信息
            this.filterSfzrdxx(cxsXMStr.toString(), dqrq, sfzrdxxList, aqRdList, acRdList, zysRdList, fcsRdList,
                    sbqxwbGroup, jjrGroup, zqtzGroup, swjgDmList);
        }
        //资源税税费种认定有效数量
        final int zysRdNum = zysRdList.size();
        //按次税费种认定项目List
        final List<String> acRdZsxmList = acRdList.stream().map(SfzrdmxxxVO::getZsxmDm).collect(Collectors.toList());
        //全量认定信息
        final List<SfzrdmxxxVO> allRdList = new ArrayList<>();
        allRdList.addAll(aqRdList);
        allRdList.addAll(acRdList);
        allRdList.addAll(fcsRdList);
        //房产税品目认定标志
        boolean cjFlag = false;
        boolean czFlag = false;
        //返回值：税种明细信息
        log.info("cxsInit_dateA1_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        final List<CchxwsnssbInitResMxVO> newmxList = new ArrayList<>();
        //获取全量码表数据，并根据征收项目分组
        final List<Map<String, Object>> qgpzAll = FtsCxsUtils.getAllCacheData("cxs_cs_gy_zsxmpmqxgzb_qg");//全国配置，200条左右
        final List<Map<String, Object>> gszdyAll = FtsCxsUtils.getAllCacheData("cxs_cs_gy_zsxmpmqxgzb_zdy");//各省自定义配置，30条左右
        final List<Map<String, Object>> ftcGroupAll = FtsCxsUtils.getAllCacheData("cxs_cs_sb_ftc_mrqxgz");//房土车配置，10条
        final List<Map<String, Object>> mrpzAll = FtsCxsUtils.getAllCacheData("cs_dj_mrqxgz_zsxm");//默认配置，70条
        final Map<Object, List<Map<String, Object>>> qgpzGroup = qgpzAll.stream().collect(Collectors.groupingBy(g -> g.get("zsxmDm")));
        final Map<Object, Map<Object, List<Map<String, Object>>>> gszdyGroup = gszdyAll.stream().collect(Collectors.groupingBy(g1 -> g1.get("zsxmDm"), Collectors.groupingBy(g2 -> g2.get("swjgDm"))));
        final Map<Object, Map<Object, List<Map<String, Object>>>> ftcGroup = ftcGroupAll.stream().collect(Collectors.groupingBy(g1 -> g1.get("zsxmDm"), Collectors.groupingBy(g2 -> g2.get("swjgDm"))));
        final Map<Object, Map<Object, List<Map<String, Object>>>> mrpzGroup = mrpzAll.stream().collect(Collectors.groupingBy(g1 -> g1.get("zsxmDm"), Collectors.groupingBy(g2 -> g2.get("swjgDm"))));
        for (CchxwsnssbInitResMxVO initResMxVO : initResMxVOList) {
            final String zsxmDm = initResMxVO.getZsxmdm();
            //如果有按次认定，则只获取按期配置
            final boolean filterAc = !GyUtils.isNull(acRdZsxmList) && acRdZsxmList.contains(zsxmDm);
            //根据税费种认定信息判断
            if (!GyUtils.isNull(allRdList)) {
                for (SfzrdmxxxVO sfzrdxxVO : allRdList) {
                    if (zsxmDm.equals(sfzrdxxVO.getZsxmDm())) {
                        //原电局代码就注掉了
                        //用于判断是否获取到有效认定信息，只有耕占、契税按次的放入，其余按次不放入，需求未输入
                        /*if ("11".equals(sfzrdxxVO.getNsqxDm())) {
                            if ("10118".equals(zsxmDm) || "10119".equals(zsxmDm)) {
                                initResMxVO.setNsqxdm(sfzrdxxVO.getNsqxDm());
                            }
                        } else {
                            initResMxVO.setNsqxdm(sfzrdxxVO.getNsqxDm());
                        }*/
                        initResMxVO.setNsqxdm(sfzrdxxVO.getNsqxDm());
                        //房产税判断从价从租认定详情
                        if ("10110".equals(zsxmDm)) {
                            //过滤后的认定信息只有从价从租或者品目为空
                            if ("101100700".equals(sfzrdxxVO.getZspmDm())) {
                                cjFlag = true;
                            } else if ("101100800".equals(sfzrdxxVO.getZspmDm())) {
                                czFlag = true;
                            } else {
                                cjFlag = true;
                                czFlag = true;
                            }
                        }
                        final CchxwsnssbInitResMxVO newMxVO = new CchxwsnssbInitResMxVO();
                        newMxVO.setDcjbz("Y");//存在认定信息标志
                        newMxVO.setZsxmdm(initResMxVO.getZsxmdm());
                        newMxVO.setSzacsbbj(initResMxVO.getSzacsbbj());
                        newMxVO.setNsqxdm(sfzrdxxVO.getNsqxDm());
                        newMxVO.setSbqxdm(sfzrdxxVO.getSbqxDm());
                        newMxVO.setJkqxdm(sfzrdxxVO.getJkqxDm());
                        newMxVO.setZspmdm(sfzrdxxVO.getZspmDm());
                        final Map<String, Object> skssqmap = CchxwsnssbGyUtils.jsSkssq(sfzrdxxVO.getNsqxDm(), dqrq, CchxwsnssbGyUtils.checkIsQnsb(sfzrdxxVO.getSbqxDm()));
                        String skssqq = "";
                        String skssqz = "";
                        if ("11".equals(sfzrdxxVO.getNsqxDm())) {
                            //按次申报分支，公共方法计算出来为前一天，这里重新赋值
                            skssqq = dqrqStr;
                            skssqz = dqrqStr;
                        } else {
                            skssqq = DateUtil.format((Date) skssqmap.get("skssqq"), "yyyy-MM-dd");
                            skssqz = DateUtil.format((Date) skssqmap.get("skssqz"), "yyyy-MM-dd");
                        }
                        newMxVO.setMrskssqq(skssqq);
                        newMxVO.setMrskssqz(skssqz);
                        newmxList.add(newMxVO);
                    }
                }
                if (!GyUtils.isNull(initResMxVO.getNsqxdm())) {
                    //对于房产税，需要精确到品目
                    if ("10110".equals(zsxmDm)) {
                        if (cjFlag && czFlag) {
                            continue;
                        }
                    } else {
                        continue;
                    }
                }
            }
            //认定没有，取全国配置（10107,10111,10120,10121），（税务机关均为11个0），（房土车土增无申报期限）
            final List<Map<String, Object>> qgList = CchxwsnssbGyUtils.getnsqxqgByZsxmDmAndSwjgDm(zsxmDm, swjgDmList, qgpzGroup, dqrq,
                    sbqxwbGroup, jjrGroup, zqtzGroup);
            if (!GyUtils.isNull(qgList) && !"10110,10112,10114".contains(zsxmDm)) {
                for (Map<String, Object> sfzrdxxVO : qgList) {
                    if ("10107".equals(zsxmDm) && zysRdNum > 0) {
                        initResMxVO.setDcjbz("Y");//存在认定信息标志
                    }
                    //不为按次，或者，没有认定按次
                    if (!"11".equals(sfzrdxxVO.get("NSQX_DM")) || !filterAc) {
                        //如果有按次认定，此时不取按次配置
                        final List<CchxwsnssbInitResMxVO> newMxListTemp = this.setKpxx(initResMxVO, sfzrdxxVO, dqrq);
                        newmxList.addAll(newMxListTemp);
                    }
                }
                if (!GyUtils.isNull(initResMxVO.getNsqxdm())) {
                    continue;
                }
            }
            //全国配置没有，取各省自定义（10113）
            final List<Map<String, Object>> zdyList = CchxwsnssbGyUtils.getnsqxzdyByZsxmDmAndSwjgDm(zsxmDm, swjgDmList, gszdyGroup, dqrq,
                    sbqxwbGroup, jjrGroup, zqtzGroup);
            if (!GyUtils.isNull(zdyList) && !"10110,10112,10114,10118,10119".contains(zsxmDm)) {
                for (Map<String, Object> sfzrdxxVO : zdyList) {
                    if ("10107".equals(zsxmDm) && zysRdNum > 0) {
                        initResMxVO.setDcjbz("Y");//存在认定信息标志
                    }
                    //不为按次，或者，没有认定按次
                    if (!"11".equals(sfzrdxxVO.get("NSQX_DM")) || !filterAc) {
                        final List<CchxwsnssbInitResMxVO> newMxListTemp = this.setKpxx(initResMxVO, sfzrdxxVO, dqrq);
                        newmxList.addAll(newMxListTemp);
                    }
                }
                if (!GyUtils.isNull(initResMxVO.getNsqxdm())) {
                    continue;
                }
            }
            //自定义没有，取房土车和默认配置表（10110,10112,10114）
            if ("10110,10112,10114".contains(zsxmDm)) {
                //纳税人类型||0 纳税人、1 自然人、2 所有人
                String nsrlxCS = "0";
                if (YHLX_ZRR.equals(yhlx)) {
                    nsrlxCS = "1";
                }
                //匹配到房土车项目，调用房土车配置表，其他取值公用配置表
                final List<Map<String, Object>> ftcList = CchxwsnssbGyUtils.getnsqxftcByZsxmDmAndSwjgDm(zsxmDm, swjgDmList, ftcGroup, nsrlxCS, cjFlag, czFlag, dqrq,
                        sbqxwbGroup, jjrGroup, zqtzGroup);
                if (!GyUtils.isNull(ftcList)) {
                    for (Map<String, Object> sfzrdxxVO : ftcList) {
                        final List<CchxwsnssbInitResMxVO> newMxListTemp = this.setKpxx(initResMxVO, sfzrdxxVO, dqrq);
                        newmxList.addAll(newMxListTemp);
                    }
                }
            } else {
                //默认配置表（10113,10118,10119）
                final List<Map<String, Object>> mrgzList = CchxwsnssbGyUtils.getnsqxmrgzByZsxmDmAndSwjgDm(zsxmDm, swjgDmList, mrpzGroup);
                if (!GyUtils.isNull(mrgzList)) {
                    for (Map<String, Object> sfzrdxxVO : mrgzList) {
                        if ("10107".equals(zsxmDm) && zysRdNum > 0) {
                            initResMxVO.setDcjbz("Y");//存在认定信息标志
                        }
                        //不为按次，或者，没有认定按次
                        if (!"11".equals(sfzrdxxVO.get("NSQX_DM")) || !filterAc) {
                            final List<CchxwsnssbInitResMxVO> newMxListTemp = this.setKpxx(initResMxVO, sfzrdxxVO, dqrq);
                            newmxList.addAll(newMxListTemp);
                        }
                    }
                }
            }
        }
        log.info("cxsInit_dateA2_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));

        //是否需要补充按次税源
        final List<String> filterAcEndList = newmxList.stream().filter(f -> "11".equals(f.getNsqxdm())).map(CchxwsnssbInitResMxVO::getZsxmdm).collect(Collectors.toList());
        //组装按次申报税种明细
        if (GyUtils.isNull(filterAcEndList) || !filterAcEndList.contains("10107")) {
            final CchxwsnssbInitResMxVO initResMxVO_zys = new CchxwsnssbInitResMxVO(dqrqStr, dqrqStr, "10107", "11", "04", "04", "Y");
            newmxList.add(initResMxVO_zys);
        }
        if (GyUtils.isNull(filterAcEndList) || !filterAcEndList.contains("10111")) {
            final CchxwsnssbInitResMxVO initResMxVO_yhs = new CchxwsnssbInitResMxVO(dqrqStr, dqrqStr, "10111", "11", "04", "04", "Y");
            newmxList.add(initResMxVO_yhs);
        }
        if (GyUtils.isNull(filterAcEndList) || !filterAcEndList.contains("10121")) {
            //自然人无环保税
            if (!YHLX_ZRR.equals(cchxwsnssbInitReqVO.getYhlx())) {
                final CchxwsnssbInitResMxVO initResMxVO_hbs = new CchxwsnssbInitResMxVO(dqrqStr, dqrqStr, "10121", "11", "04", "04", "Y");
                newmxList.add(initResMxVO_hbs);
            }
        }
        if (GyUtils.isNull(filterAcEndList) || !filterAcEndList.contains("10118")) {
            final CchxwsnssbInitResMxVO initResMxVO_gdzys = new CchxwsnssbInitResMxVO(dqrqStr, dqrqStr, "10118", "11", "04", "04", "Y");
            newmxList.add(initResMxVO_gdzys);
        }
        if (GyUtils.isNull(filterAcEndList) || !filterAcEndList.contains("10119")) {
            final CchxwsnssbInitResMxVO initResMxVO_qs = new CchxwsnssbInitResMxVO(dqrqStr, dqrqStr, "10119", "11", "04", "04", "Y");
            newmxList.add(initResMxVO_qs);
        }
        if (GyUtils.isNull(filterAcEndList) || !filterAcEndList.contains("10113")) {
            final CchxwsnssbInitResMxVO initResMxVO_tdzzs = new CchxwsnssbInitResMxVO(dqrqStr, dqrqStr, "10113", "11", "04", "04", "Y");
            newmxList.add(initResMxVO_tdzzs);
        }

        //房产单独去重：根据mx -> mx.getMrskssqq()+";"+mx.getMrskssqz()+";"+mx.getNsqxdm()+";"+mx.getZsxmdm()+";"+mx.getZspmdm()+";"+mx.getDqsfyx()
        final List<CchxwsnssbInitResMxVO> aList = newmxList.stream()
                .filter(f -> "10110".equals(f.getZsxmdm()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator
                        .comparing(mx -> mx.getMrskssqq() + ";" + mx.getMrskssqz() + ";" + mx.getNsqxdm() + ";" + mx.getZsxmdm() + ";" + mx.getZspmdm() + ";" + mx.getDqsfyx()))), ArrayList::new));

        //去重：根据mx.getMrskssqq()+";"+mx.getMrskssqz()+";"+mx.getNsqxdm()+";"+mx.getZsxmdm()+";"+mx.getDqsfyx()
        final List<CchxwsnssbInitResMxVO> bLsit = newmxList.stream()
                .filter(f -> !"10110".equals(f.getZsxmdm()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator
                        .comparing(mx -> mx.getMrskssqq() + ";" + mx.getMrskssqz() + ";" + mx.getNsqxdm() + ";" + mx.getZsxmdm() + ";" + mx.getDqsfyx()))), ArrayList::new));

        final List<CchxwsnssbInitResMxVO> newinitResMxVOList = new ArrayList<>();
        newinitResMxVOList.addAll(aList);
        newinitResMxVOList.addAll(bLsit);

        log.info("cxsInit_dateC1_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        //判断当期是否有效
        for (CchxwsnssbInitResMxVO initResMxVO : newinitResMxVOList) {
            final String sbqxDm = initResMxVO.getSbqxdm();
            Calendar calendarMrskssqq = Calendar.getInstance();
            calendarMrskssqq.setTime(DateUtils.parseDate(initResMxVO.getMrskssqq(), "yyyy-MM-dd"));
            Calendar calendarMrskssqz = Calendar.getInstance();
            calendarMrskssqz.setTime(DateUtils.parseDate(initResMxVO.getMrskssqz(), "yyyy-MM-dd"));
            final String sbrqqStr = CchxwsnssbGyUtils.getSbqxrqq(sbqxDm, initResMxVO.getNsqxdm(), initResMxVO.getZsxmdm(),
                    calendarMrskssqq, calendarMrskssqz);
            final String sbqxStr = CchxwsnssbGyUtils.getSbqx(swjgDmList, sbqxDm, initResMxVO.getNsqxdm(), initResMxVO.getZsxmdm(),
                    calendarMrskssqq, calendarMrskssqz,
                    sbqxwbGroup, jjrGroup, zqtzGroup);
            final Date sbrqqDate = com.css.znsb.nssb.util.DateUtil.toDate("yyyy-MM-dd", sbrqqStr);
            final Date sbqxDate = com.css.znsb.nssb.util.DateUtil.toDate("yyyy-MM-dd", sbqxStr);
            initResMxVO.setSbrqq(sbrqqStr);
            initResMxVO.setSbrqz(sbqxStr);
            if (!sbqxDate.before(dqrq) && !sbrqqDate.after(dqrq)) {
                initResMxVO.setDqsfyx("Y");
            } else {
                initResMxVO.setDqsfyx("N");
            }
        }
        log.info("cxsInit_dateC2_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));

        //土地增值税增加系统参数控制，默认为Y，弃用参数
        initResZbVO.setTdzzssbkg("Y");
        initResVO.setInitResZbVO(initResZbVO);
        initResVO.setInitResMxVOList(newinitResMxVOList);
        initResVO.setZsxmDmList(zsxmDmListRes);
        //initResVO.setOriginalList(newinitResMxVOList);
        return initResVO;
    }

    /**
     * 财行税初始化 - 逾期申报
     * @param nsrsbh 社会信用代码
     * @param djxh 登记序号
     * @param zgswjgdm 税务机关
     * @return 返回值
     */
//    @Override
//    public List<CchxwsYqwsbVo> cxsYqsbInit(String nsrsbh, String djxh, String zgswjgdm, String yhlx){
//        List<CchxwsYqwsbVo> cchxwsYqwsbVoList = new ArrayList<>();
//        //逾期申报开关，Y旧版swsj_gt4_szc_sbzszcfw_sffw_cxcchxwswqwsbjl，X新版，N不开启
//        final String cxsyqsbkg = CxsGyUtils.getXtcs(zgswjgdm, "DZSWJ00SBZX020017" , "N");
//        if("Y".equals(cxsyqsbkg)){
//            final ServerResponse<Map<String, Object>> response = cchxwsYqwsbFeignClients.cxsYqsbInit(nsrsbh, djxh);
//            if(!GyUtils.isNull(response) && !GyUtils.isNull(response.getResponse()) &&!GyUtils.isNull(response.getResponse().getData()) &&
//                    !GyUtils.isNull(response.getResponse().getData().get("data"))){
//                cchxwsYqwsbVoList = BeanUtils.MapsToBeans((List<Map>) response.getResponse().getData().get("data"), CchxwsYqwsbVo.class);
//            }
//        } else if ("X".equals(cxsyqsbkg)) {
//            //电子表证单书种类代码 - BDA0611148 - 《财产和行为税纳税申报表》
//            final ServerResponse<Map<String, Object>> responseNew = cchxwsQueryYqwsbFeignClients.queryYqwsbxx(djxh, "BDA0611148");
//            if(!GyUtils.isNull(responseNew) && !GyUtils.isNull(responseNew.getResponse()) &&!GyUtils.isNull(responseNew.getResponse().getData()) &&
//                    !GyUtils.isNull (responseNew.getResponse().getData().get("data"))){
//                final List<Map<String, Object>> yqxxList = (List<Map<String, Object>>) responseNew.getResponse().getData().get("data");
//                log.info(djxh + ":" + yqxxList);
//                this.dealWithCxsYqxxNew(yqxxList, cchxwsYqwsbVoList);
//            }
//        } else {
//            return new ArrayList<>();
//        }
//
//        String[] finalZsxmFilter = new String[]{"10110","10112","10114","10111","10107","10120","10121"};
//        if(YHLX_ZRR.equals(yhlx)){
//            finalZsxmFilter = new String[]{"10110","10112","10111","10107"};
//        }
//        final List<String> strings = Arrays.asList(finalZsxmFilter);
//
//        //数据过滤并去重，仅保存未申报数据
//        final List<CchxwsYqwsbVo> filterList = cchxwsYqwsbVoList.stream().filter((x) -> !"Y".equals(x.getYsbbz()) && strings.contains(x.getZsxmDm()))
//                .collect(Collectors.collectingAndThen(Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(mx -> mx.getZsxmDm()+";"+mx.getSkssqq()+";"+mx.getSkssqz()))),ArrayList::new));
//        //数据过滤并去重 - 土增分支
//        if(!YHLX_ZRR.equals(yhlx)){
//            final List<CchxwsYqwsbVo> filterListTdzzs = cchxwsYqwsbVoList.stream().filter((x) -> !"Y".equals(x.getYsbbz()) && "10113".equals(x.getZsxmDm()))
//                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(mx -> mx.getZsxmDm() + ";" + mx.getSkssqq() + ";" + mx.getSkssqz() + ";" + mx.getXmbh() + ";" + mx.getZspmDm()))), ArrayList::new));
//            filterList.addAll(filterListTdzzs);
//        }
//        //返回值
//        List<CchxwsYqwsbVo> yqwsbVoList = new ArrayList<>();
//        if(!GyUtils.isNull(filterList)){
//            //数据处理，并排序
//            yqwsbVoList = filterList.stream().map(m -> {
//                final String zsxmmc = GyUtils.getMcByDm("DM_GY_ZSXM", "ZSXMMC", m.getZsxmDm());
//                m.setZsxmmc(zsxmmc);
//                m.setSbqx(DateUtils.formatToStr(m.getSbqx(), "yyyy-MM-dd"));
//                m.setSkssqq(DateUtil.formatToStr(m.getSkssqq(), "yyyy-MM-dd"));
//                m.setSkssqz(DateUtil.formatToStr(m.getSkssqz(), "yyyy-MM-dd"));
//                m.setSbms("YUQI");
//                return m;
//            }).sorted(Comparator.comparing(CchxwsYqwsbVo::getSkssqz).reversed().thenComparing(CchxwsYqwsbVo::getSkssqq)).collect(Collectors.toList());
//        }
//        return yqwsbVoList;
//    }


//    /**
//     * 处理财行税逾期信息新接口
//     * @param yqxxList 逾期信息
//     * @param cchxwsYqwsbVoList 待填充数据
//     */
//    private void dealWithCxsYqxxNew(List<Map<String, Object>> yqxxList, List<CchxwsYqwsbVo> cchxwsYqwsbVoList) {
//        if (!GyUtils.isNull(yqxxList)) {
//            for (Map<String, Object> yqxxMap : yqxxList) {
//                final String skssqq = (String) yqxxMap.get("skssqq");
//                final String skssqz = (String) yqxxMap.get("skssqz");
//                final String sbqx = (String) yqxxMap.get("sbqx");
//                final String yzpzzlDm = (String) yqxxMap.get("yzpzzlDm");
//                String sybh = "";
//                if (!GyUtils.isNull(yqxxMap.get("sybh"))) {
//                    sybh = (String) yqxxMap.get("sybh");
//                }
//                final String zsxmDmTemp = (String) yqxxMap.get("zsxmDm");
//                String zsxmDm = "";
//                String zspmDm = "";
//                if (!GyUtils.isNull(zsxmDmTemp) && !"10113".equals(zsxmDmTemp)) {
//                    zsxmDm = zsxmDmTemp;
//                } else {
//                    final Map<String, Object> retrunMap = CchxwsnssbGyUtils.getZsxmDmByYzpzzlDm(yzpzzlDm, sybh);
//                    if (!GyUtils.isNull(retrunMap)) {
//                        if (!GyUtils.isNull(retrunMap.get("zsxmDm"))) {
//                            zsxmDm = (String) retrunMap.get("zsxmDm");
//                        }
//                        if (!GyUtils.isNull(retrunMap.get("zspmDm"))) {
//                            zspmDm = (String) retrunMap.get("zspmDm");
//                        }
//                    }
//                }
//                if (!GyUtils.isNull(skssqq) && !GyUtils.isNull(skssqz) && !GyUtils.isNull(sbqx) &&
//                        !GyUtils.isNull(yzpzzlDm) && !GyUtils.isNull(zsxmDm)) {
//                    if ("10113".equals(zsxmDm) && (GyUtils.isNull(zspmDm))) {
//                        continue;
//                    }
//                    final CchxwsYqwsbVo cxsVO = new CchxwsYqwsbVo();
//                    cxsVO.setZsxmDm(zsxmDm);
//                    cxsVO.setZspmDm(zspmDm);
//                    cxsVO.setSybh1(sybh);
//                    cxsVO.setXmbh(sybh);
//                    cxsVO.setSkssqq(DateUtil.formatToStr(skssqq, "yyyy-MM-dd"));
//                    cxsVO.setSkssqz(DateUtil.formatToStr(skssqz, "yyyy-MM-dd"));
//                    cxsVO.setSbqx(DateUtil.formatToStr(sbqx, "yyyy-MM-dd"));
//                    //无用字段:zlbsxDm
//                    cxsVO.setDjxh(GYCastUtils.cast2Str(yqxxMap.get("djxh")));
//                    cxsVO.setZgswjDm((String) yqxxMap.get("zgswjDm"));
//                    cxsVO.setZgswskfjDm((String) yqxxMap.get("zgswskfjDm"));
//                    cchxwsYqwsbVoList.add(cxsVO);
//                }
//            }
//        }
//    }

    /**
     * 过滤税费种认定信息
     *
     * @param cxsXMStr    财行税征收项目字符串
     * @param dqrq        当前日期
     * @param sfzrdxxList 原始税费种认定信息
     * @param aqRdList    过滤后按期认定信息
     * @param acRdList    过滤后按次认定信息
     * @param zysRdList   过滤后资源税认定信息
     * @param fcsRdList   过滤后房产税认定信息
     * @param sbqxwbGroup 申报期限维护表group
     * @param jjrGroup    节假日group
     * @param zqtzGroup   征期调整group
     * @param swjgDmList  本上级税务机关
     */
    private void filterSfzrdxx(String cxsXMStr, Date dqrq,
                               List<SfzrdmxxxVO> sfzrdxxList, List<SfzrdmxxxVO> aqRdList,
                               List<SfzrdmxxxVO> acRdList, List<SfzrdmxxxVO> zysRdList, List<SfzrdmxxxVO> fcsRdList,
                               Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup,
                               Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup,
                               Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup,
                               List<String> swjgDmList) {
        if (GyUtils.isNull(cxsXMStr) || GyUtils.isNull(dqrq) || GyUtils.isNull(sfzrdxxList)) {
            return;
        }
        //循环分组
        for (SfzrdmxxxVO rdVO : sfzrdxxList) {
            final String zsxmDm = rdVO.getZsxmDm();
            final String zspmDm = !GyUtils.isNull(rdVO.getZspmDm()) ? rdVO.getZspmDm() : "";
            rdVO.setZspmDm(zspmDm);
            final String nsqxDm = rdVO.getNsqxDm();
            final String sbqxDm = rdVO.getSbqxDm();
            final String jkqxDm = rdVO.getJkqxDm();
            final Date rdyxqq = rdVO.getRdyxqq();
            final Date rdyxqz = rdVO.getRdyxqz();
            //过滤掉不完整认定
            if (GyUtils.isNull(zsxmDm) || GyUtils.isNull(nsqxDm) || GyUtils.isNull(sbqxDm) || GyUtils.isNull(jkqxDm)
                    || GyUtils.isNull(rdyxqq) || GyUtils.isNull(rdyxqz)) {
                continue;
            }
            //过滤掉非十税税种
            if (!cxsXMStr.contains(zsxmDm)) {
                continue;
            }
            //过滤掉水资源认定
            if ("10107".equals(zsxmDm) && !GyUtils.isNull(zspmDm) && zspmDm.startsWith("1010708")) {
                continue;
            }
            //过滤掉房产税非从价从租认定
            if ("10110".equals(zsxmDm) && !GyUtils.isNull(zspmDm) && !"101100700".equals(zspmDm) && !"101100800".equals(zspmDm)) {
                continue;
            }
            //过滤掉非有效和非自行申报认定
//            if(!"Y".equals(rdVO.getYxbz()) || !"0".equals(rdVO.getZsdlfsDm()) ){
            //乐企接口没传YXBZ
            if (!"0".equals(rdVO.getZsdlfsDm())) {
                continue;
            }
            //检验认定配置日期是否有效
            if (!CchxwsnssbGyUtils.checkRdpzRq(nsqxDm, sbqxDm, rdyxqq, rdyxqz, dqrq, sbqxwbGroup, jjrGroup, zqtzGroup, swjgDmList, zsxmDm)) {
                continue;
            }
            //资源税单独分堆
            if ("10107".equals(zsxmDm)) {
                zysRdList.add(rdVO);
                continue;
            }
            //按次单独分堆
            if ("11".equals(nsqxDm)) {
                //房土车烟不支持按次认定，如存在按错误数据处理
                if ("10110".equals(zsxmDm) || "10112".equals(zsxmDm) || "10114".equals(zsxmDm) || "10120".equals(zsxmDm)) {
                    continue;
                } else {
                    acRdList.add(rdVO);
                    continue;
                }
            }
            //房产税
            if ("10110".equals(zsxmDm)) {
                fcsRdList.add(rdVO);
            } else {
                aqRdList.add(rdVO);
            }
        }
    }

    /**
     * 设置卡票信息方法
     *
     * @param initResMxVO 税种信息
     * @param sfzrdxxVO   税费种认定信息
     * @param dqrq        当前日期
     * @return 返回值
     */
    private List<CchxwsnssbInitResMxVO> setKpxx(CchxwsnssbInitResMxVO initResMxVO, Map<String, Object> sfzrdxxVO, Date dqrq) {
        final String dqrqStr = DateUtil.format(dqrq, "yyyy-MM-dd");
        //用于判断是否获取到有效认定信息
        initResMxVO.setNsqxdm(sfzrdxxVO.get("NSQX_DM").toString());
        //返回值
        final List<CchxwsnssbInitResMxVO> initResMxVOList = new ArrayList<>();
        final CchxwsnssbInitResMxVO newMxVO = new CchxwsnssbInitResMxVO();
        newMxVO.setZsxmdm(initResMxVO.getZsxmdm());
        newMxVO.setSzacsbbj(initResMxVO.getSzacsbbj());
        newMxVO.setDcjbz(initResMxVO.getDcjbz());
        //暂时只有房产需要品目
        newMxVO.setZspmdm((String) sfzrdxxVO.get("ZSPM_DM"));
        newMxVO.setNsqxdm((String) sfzrdxxVO.get("NSQX_DM"));
        newMxVO.setSbqxdm((String) sfzrdxxVO.get("SBQX_DM"));
        newMxVO.setJkqxdm((String) sfzrdxxVO.get("JKQX_DM"));
        //计算税款所属期
        final Map<String, Object> skssqmap = CchxwsnssbGyUtils.jsSkssq(sfzrdxxVO.get("NSQX_DM").toString(), dqrq, CchxwsnssbGyUtils.checkIsQnsb(sfzrdxxVO.get("SBQX_DM").toString()));
        String skssqq = "";
        String skssqz = "";
        if ("11".equals(sfzrdxxVO.get("NSQX_DM").toString())) {
            //按次申报分支，公共方法计算出来为前一天，这里重新赋值
            skssqq = dqrqStr;
            skssqz = dqrqStr;
        } else {
            skssqq = DateUtil.format((Date) skssqmap.get("skssqq"), "yyyy-MM-dd");
            skssqz = DateUtil.format((Date) skssqmap.get("skssqz"), "yyyy-MM-dd");
        }
        newMxVO.setMrskssqq(skssqq);
        newMxVO.setMrskssqz(skssqz);
        //资源税过滤掉半年报和年报，某个需求，（里边是水资源的申报期，需要过滤掉，前面已经过滤掉，代码暂时保留）
        if (("10107".equals(initResMxVO.getZsxmdm()) && "09".equals(initResMxVO.getNsqxdm())) || ("10107".equals(initResMxVO.getZsxmdm()) && "10".equals(initResMxVO.getNsqxdm()))) {
            return initResMxVOList;
        } else {
            initResMxVOList.add(newMxVO);
            return initResMxVOList;
        }
    }

    /**
     * 查询申报表信息
     *
     * @param cchxwsnssbquerysbbxxReqVO 入参
     * @return 返回值
     */
    @Override
    public CchxwsnssbbxxVO querySbbmx(CchxwsnssbquerysbbxxReqVO cchxwsnssbquerysbbxxReqVO) throws Exception {
        //返回值
        CchxwsnssbbxxVO cchxwsnssbbxxVO = new CchxwsnssbbxxVO();
        //获取有效的税源认定和配置信息，并以税款所属期起排序从小到大排序，因为按次的为当前日期，税款所属期起永远最大
        final List<CchxwsnssbInitResMxVO> initResMxVOList = cchxwsnssbquerysbbxxReqVO.getInitResMxVOList().stream().filter((x) -> x.getDqsfyx().equals("Y"))
                .sorted(Comparator.comparing(CchxwsnssbInitResMxVO::getMrskssqq))
                .collect(Collectors.toList());
        //入参检验
        if (GyUtils.isNull(initResMxVOList)) {
            cchxwsnssbbxxVO.setReturnCode("0");
            cchxwsnssbbxxVO.setReturnMsg("当期没有需要申报的有效税源信息");
            return cchxwsnssbbxxVO;
        }
        //更正标志
        final String gzbz = cchxwsnssbquerysbbxxReqVO.getGzbz();
        //取出入参数据
        final CxstysbNsrxxVO cxstysbNsrxxVO = cchxwsnssbquerysbbxxReqVO.getNsrxxVO();
        final String yhlx = cxstysbNsrxxVO.getYhlx();
        final String zgswjgdm = cxstysbNsrxxVO.getZgswskfj_dm();
        //勾选税源信息
        final List<SbxxVO> sbxxVOList = cchxwsnssbquerysbbxxReqVO.getSbxxVOList();
        //获取纳税人基本信息
        JbxxmxsjVO dwnsrxxVO = null;
        //获取纳纳税人资格信息
        NsrzgxxVO nsrzgxxVO = null;
        //获取纳税人标签信息
        List<NsrbqxxVO> nsrbqxxVOList = new ArrayList<>();
        CommonResult<ZnsbMhzcQyjbxxmxResVO> qyjbxxmx = new CommonResult<>();
        if (!GyUtils.isNull(cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getDjxh())) {
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getDjxh());
            znsbMhzcQyjbxxmxReqVO.setNsrsbh(cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getNsrsbh());
            qyjbxxmx = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        }
//        final CxNsrjbxxAndFyhyxxResVO data = response.getData();
        final ZnsbMhzcQyjbxxmxResVO data = qyjbxxmx.getData();
        if (!GyUtils.isNull(data) && !GyUtils.isNull(data.getJbxxmxsj())) {
            final List<JbxxmxsjVO> nsrxxList = data.getJbxxmxsj();
            if (!GyUtils.isNull(cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getDjxh())) {
                dwnsrxxVO = nsrxxList.get(0);
            } else {
                dwnsrxxVO = FtsCxsUtils.dofierNsrxx(nsrxxList, cchxwsnssbquerysbbxxReqVO.getNsrxxVO().getNsrsbh());
            }
        }
        if (!GyUtils.isNull(data) && !GyUtils.isNull(data.getNsrzgxx())) {
            nsrzgxxVO = data.getNsrzgxx().get(0);
        }
        if (!GyUtils.isNull(data) && !GyUtils.isNull(data.getNsrbqxx())) {
            nsrbqxxVOList = data.getNsrbqxx();
        }
        String hydm = "";
        String xzqhszdm = "";
        String jdxzdm = "";
        if (!GyUtils.isNull(dwnsrxxVO)) {
            hydm = dwnsrxxVO.getHyDm();
            xzqhszdm = dwnsrxxVO.getScjydzxzqhszDm();
            jdxzdm = dwnsrxxVO.getJdxzDm();
        }
        //税源信息，入参为单税源，默认取第一条数据，税款所属期范围最大（以排序）
        final String nsqxDm = cxstysbNsrxxVO.getNsqxDm();
//        final CchxwsnssbInitResMxVO initResMxVO = initResMxVOList.get(0);
        List<CchxwsnssbInitResMxVO> initMxList = new  ArrayList<>();
        if (GyUtils.isNotNull(nsqxDm)){
            initMxList = initResMxVOList.stream().filter(vo -> nsqxDm.equals(vo.getNsqxdm())).collect(Collectors.toList());
        }
        if (GyUtils.isNull(initMxList)) {
            cchxwsnssbbxxVO.setReturnCode("0");
            cchxwsnssbbxxVO.setReturnMsg("没有对应纳税期限的有效认定信息");
            return cchxwsnssbbxxVO;
        }
        final CchxwsnssbInitResMxVO initResMxVO = initMxList.get(0);
        //组装公用查询条件
        final CxstysbNsrxx cxstysbNsrxx = new CxstysbNsrxx();
        //先塞入默认属期，后续逻辑重新处理，此处大概率无用代码
        cxstysbNsrxx.setSkssqq(initResMxVO.getMrskssqq());
        cxstysbNsrxx.setSkssqz(initResMxVO.getMrskssqz());
        cxstysbNsrxx.setDjxh(cxstysbNsrxxVO.getDjxh());
        cxstysbNsrxx.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
        cxstysbNsrxx.setNsrmc(cxstysbNsrxxVO.getNsrmc());
        //申报属性代码 - 11 - 正常申报
        cxstysbNsrxx.setSbsxDm1("11");
        cxstysbNsrxx.setSbrq1(CchxwsnssbGyUtils.getDayofNow());
        cxstysbNsrxx.setHyDm(hydm);
        cxstysbNsrxx.setJdxzDm(jdxzdm);
        cxstysbNsrxx.setXzqhszDm(xzqhszdm);
        //电子表证单书种类代码 - BDA0611148 - 《财产和行为税纳税申报表》
        cxstysbNsrxx.setYzpzzlDm("BDA0611148");
        cxstysbNsrxx.setZgswskfjDm(cxstysbNsrxxVO.getZgswskfj_dm());
        //先塞入六税两费信息，后续逻辑重新处理，此处大概率无用代码
        cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
        cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
        cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());

        //耕地占用税未批先占参数值，为Y显示提示，并判断gdzysLslfMrz
        final String gdzysWpxzCsz = CxsGyUtils.getXtcs(zgswjgdm, "DZSWJ00SBZX020051", "N");
        //费耕地占用税六税两默认值，为空前端不做修改，为N默认为不选中且不可修改
        String gdzysLslfMrz = "";

        //内部返回值
        final List<CchxwsnssbInitResMxVO> mxVOList = new ArrayList<>();
        final List<SbxxGridlbVO> sbxxGridlb = new ArrayList<>();
        final List<Bqjmsemxbywbw> bqjmsemxbywbwList = new ArrayList<>();
        final List<SymxVO> symxVOList_save = new ArrayList<>();
        //税源明细ReqVO
        final CxsSymxReqVO cxsSymxReqVO = new CxsSymxReqVO();
        //环保税排序使用
        final List<HbsCjSyxx> hbsSyxxBaseSort = new ArrayList<>();

        //为年度情况
        if (GyUtils.isNull(cxstysbNsrxxVO.getSsnd())) {
            //每年一月份，仅查询去年按次信息；如仅有按期认定，则年度为上一年；如仅有按次认定，则年度会变为本年（正常不应该为本年，耕地、契税仅有按次，所以需要特殊处理为去年）；
            final String nd = initResMxVO.getMrskssqz().substring(0, 4);
            //取整一年属期
            cxstysbNsrxx.setSkssqq(CchxwsnssbGyUtils.firstDayofYearBySsnd(nd));
            cxstysbNsrxx.setSkssqz(CchxwsnssbGyUtils.lastDayofYearBySsnd(nd));
            //耕地占用税，契税处理，修复为1月份仅显示上年按次问题（仅有按次认定）
            if ("10118,10119".contains(initResMxVO.getZsxmdm()) && "Y".equals(initResMxVO.getDqsfyx()) && "01".equals(initResMxVO.getMrskssqz().substring(5, 7))) {
                final int ndd = Integer.parseInt(nd) - 1;
                cxstysbNsrxx.setSkssqq(CchxwsnssbGyUtils.firstDayofYearBySsnd(Integer.toString(ndd)));
                cxstysbNsrxx.setSkssqz(CchxwsnssbGyUtils.lastDayofYearBySsnd(Integer.toString(ndd)));
            }
        } else {
            //选择年度情况，1.如果未勾选税源情况仅查询按次税源，2.如果有勾选，直接会走勾选税源分支，3.车船税只会走勾选税源分支和逾期申报分支
            cxstysbNsrxx.setSkssqq(CchxwsnssbGyUtils.firstDayofYearBySsnd(cxstysbNsrxxVO.getSsnd()));
            cxstysbNsrxx.setSkssqz(CchxwsnssbGyUtils.lastDayofYearBySsnd(cxstysbNsrxxVO.getSsnd()));
        }

        if (GyUtils.isNull(sbxxVOList)) {
            //印花税分支
            if (initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmYhs())) {
                if (GyUtils.isNotNull(cchxwsnssbquerysbbxxReqVO.getSbrwuuid())) {
                    final String skssqq_init = DateUtil.format(CchxwsnssbGyUtils.getFirstDayofMonth(GyUtils.cast2Date(cxstysbNsrxxVO.getSkssqq())), "yyyy-MM-dd");
                    final String skssqz_init = DateUtil.format(CchxwsnssbGyUtils.getlastDayofMonth(GyUtils.cast2Date(cxstysbNsrxxVO.getSkssqz())), "yyyy-MM-dd");
                    // initResMxVO已更新，可直接使用
                    if (!GyUtils.isNull(skssqq_init) && !GyUtils.isNull(skssqz_init)) {
                        // 乐企不提供接口，此处处理重写，原电局接口注掉
                        this.getPhjmzgnew(initResMxVO, skssqq_init, skssqz_init, nsrzgxxVO, nsrbqxxVOList);
                    }
                    //组装六税两费信息
                    cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
                    cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());
                    cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
                    cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
                    mxVOList.add(initResMxVO);
                    //申报状态
                    List<String> sbztList = Arrays.asList(SBZT_SBSB_DM, SBZT_SHBTG_DM, SBZT_WSB_DM,SBZT_ZF_ZFCG_DM);
                    //根据更正标志组装申报状态
                    if ("Y".equals(gzbz)){
                        sbztList = Collections.singletonList(SBZT_SBCG_DM);
                    }
                    LambdaQueryWrapper<ZnsbNssbSbrwDO> znsbNssbSbrwDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    znsbNssbSbrwDOLambdaQueryWrapper.eq(ZnsbNssbSbrwDO::getSbrwuuid, cchxwsnssbquerysbbxxReqVO.getSbrwuuid())
                            .in(ZnsbNssbSbrwDO::getNsrsbztDm, sbztList);
                    ZnsbNssbSbrwDO znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(znsbNssbSbrwDOLambdaQueryWrapper);
                    if (GyUtils.isNotNull(znsbNssbSbrwDO) && GyUtils.isNotNull(znsbNssbSbrwDO.getSyuuid1())) {
                        //组装syuuid
                        String[] syuuidList = znsbNssbSbrwDO.getSyuuid1().substring(1, znsbNssbSbrwDO.getSyuuid1().length() - 1).split(",");
                        final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
                        for (String syuuid : syuuidList) {
                            final SymxVO symxVO = new SymxVO();
                            symxVO.setSyuuid(syuuid);
                            symxVO.setSybzDm1("");
                            symxVOList_save.add(symxVO);
                            final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
                            symxGridlbVO.setSyuuid(syuuid);
                            symxGridlbVO.setSybzDm1("");
                            symxGridlb.add(symxGridlbVO);
                        }
                        if (GyUtils.isNotNull(symxGridlb)) {
                            //调用财行税申报核心初始化接口
                            this.initCxstysbHX(GyConstants.getDmGyZsxmYhs(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO, initResMxVO, cchxwsnssbquerysbbxxReqVO);
                        }
                    }
                }
                //调用税源查询服务
                //申报保存时使用的税源信息

//                final List<YhssbsyGridlbVO> yhssbsyGridlb = this.queryForSb("SWZJ.HXZG.SB.QUERYYHSSYXXFORSB", cxstysbNsrxx, cxstysbNsrxxVO,zgswjgdm, yhlx, YhssbsyGridlbVO.class);
//                if(!GyUtils.isNull(yhssbsyGridlb)){
//                     final List<String> list_skssqq_yhs = new ArrayList<>();
//                     final List<String> list_skssqz_yhs = new ArrayList<>();
//                     final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//                     for (YhssbsyGridlbVO yhssbsyGridlbVO : yhssbsyGridlb) {
//                        //对查询出的税源信息处理，按次+多种属期
////                        final Date yhsSkssqq = com.css.znsb.nssb.util.DateUtil.toDate(yhssbsyGridlbVO.getSkssqq(), "yyyy-MM-dd");
////                        final Date yhsSkssqz = com.css.znsb.nssb.util.DateUtil.toDate(yhssbsyGridlbVO.getSkssqz(), "yyyy-MM-dd");
//                         final Date yhsSkssqq = sdf.parse(yhssbsyGridlbVO.getSkssqq());
//                         final Date yhsSkssqz = sdf.parse(yhssbsyGridlbVO.getSkssqz());
//                         //判断税源是否有效
//                         final boolean syflag = this.filterSyxxBySkssq(mrskssqq, mrskssqz, yhsSkssqq, yhsSkssqz, cxstysbNsrxxVO.getSsnd());
//                         if (syflag) {
//                             if(GyUtils.isNotNull(syuuidList) && !syuuidList.contains(yhssbsyGridlbVO.getSyuuid())){
//                                 continue;
//                             }
//                             //核心初始化接口入参
//                             final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                             symxGridlbVO.setSyuuid(yhssbsyGridlbVO.getSyuuid());
//                             symxGridlbVO.setSybzDm1("");
//                             symxGridlb.add(symxGridlbVO);
//                             //申报保存时使用的税源信息
//                             final SymxVO symxVO = new SymxVO();
//                             symxVO.setSyuuid(yhssbsyGridlbVO.getSyuuid());
//                             symxVO.setSybzDm1("");
//                             symxVOList_save.add(symxVO);
//                             //计算六税两费信息使用
//                             list_skssqq_yhs.add(yhssbsyGridlbVO.getSkssqq());
//                             list_skssqz_yhs.add(yhssbsyGridlbVO.getSkssqz());
//                         }
//                     }
//                    //获取最小税款所属期起，并取月初第一天；获取最大税款所属期止，并取月末最后一天
//                    final Date skssqq_yhs = CchxwsnssbGyUtils.minmaxDate(list_skssqq_yhs, "min");
//                    final Date skssqz_yhs = CchxwsnssbGyUtils.minmaxDate(list_skssqz_yhs, "max");
//                    String skssqq_init = null;
//                    String skssqz_init = null;
//                    if(!GyUtils.isNull(skssqq_yhs) && !GyUtils.isNull(skssqz_yhs)){
//                        skssqq_init = DateUtil.format(CchxwsnssbGyUtils.getFirstDayofMonth(GyUtils.cast2Date(cxstysbNsrxxVO.getSkssqq())), "yyyy-MM-dd");
//                        skssqz_init = DateUtil.format(CchxwsnssbGyUtils.getlastDayofMonth(GyUtils.cast2Date(cxstysbNsrxxVO.getSkssqz())), "yyyy-MM-dd");
//                    }
//                    // initResMxVO已更新，可直接使用
//                    if(!GyUtils.isNull(skssqq_init) && !GyUtils.isNull(skssqz_init)) {
//                        //此时initResMxVO已在方法里处理，返回值为同一个对象，不用接收重新赋值
////                        this.getPhjmzg(cxstysbNsrxx, initResMxVO, skssqq_init, skssqz_init,initResMxVO.getZsxmdm());
//                        // 乐企不提供接口，此处处理重写，原电局接口注掉
//                        this.getPhjmzgnew(initResMxVO,skssqq_init,skssqz_init,nsrzgxxVO,nsrbqxxVO);
//                    }
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                    //组装六税两费信息
//                    cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
//                    cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());
//                    cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
//                    cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
//
//                    if(!GyUtils.isNull(symxGridlb)){
//                        //调用财行税申报核心初始化接口
//                        this.initCxstysbHX(GyConstants.getDmGyZsxmYhs(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO,initResMxVO,cchxwsnssbquerysbbxxReqVO);
//                        if (GyUtils.isNotNull(sbxxGridlb)){
//
//                        }
//
//                    }
////                    cchxwsnssbbxxVO = JacksonUtils.toObj(DbUtils.getData("syxxquery"),CchxwsnssbbxxVO.class);
////                    return cchxwsnssbbxxVO;
//                }else {
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                }
            }
//            else if(initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmZys())){//资源税分支
//                //调用税源查询服务
//                final List<ZyssbsyGridlbVO> zyssbsyGridlb = this.queryForSb("SWZJ.HXZG.SB.QUERYZYSSYXXFORSB", cxstysbNsrxx, zgswjgdm, yhlx, ZyssbsyGridlbVO.class);
//                if (!GyUtils.isNull(zyssbsyGridlb)) {
//                    final List<String> list_skssqq_zys = new ArrayList<>();
//                    final List<String> list_skssqz_zys = new ArrayList<>();
//                    final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//                    for (ZyssbsyGridlbVO zyssbsyGridlbVO : zyssbsyGridlb) {
//                        //对查询出的税源信息处理，按次+多种属期
//                        final Date zysSkssqq = DateUtil.toDate(zyssbsyGridlbVO.getSkssqq(), "yyyy-MM-dd");
//                        final Date zysSkssqz = DateUtil.toDate(zyssbsyGridlbVO.getSkssqz(), "yyyy-MM-dd");
//                        //判断税源是否有效
//                        final boolean syflag = this.filterSyxxBySkssq(mrskssqq, mrskssqz, zysSkssqq, zysSkssqz, cxstysbNsrxxVO.getSsnd());
//                        //判断税源是否有效
//                        if (syflag) {
//                            //核心初始化接口入参
//                            final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                            symxGridlbVO.setSyuuid(zyssbsyGridlbVO.getSyuuid());
//                            symxGridlbVO.setSybzDm1("");
//                            symxGridlb.add(symxGridlbVO);
//                            //申报保存时使用的税源信息
//                            final SymxVO symxVO = new SymxVO();
//                            symxVO.setSyuuid(zyssbsyGridlbVO.getSyuuid());
//                            symxVO.setSybzDm1("");
//                            symxVOList_save.add(symxVO);
//                            //计算六税两费信息使用
//                            list_skssqq_zys.add(zyssbsyGridlbVO.getSkssqq());
//                            list_skssqz_zys.add(zyssbsyGridlbVO.getSkssqz());
//                        }
//                    }
//                    //获取最小税款所属期起，并取月初第一天；获取最大税款所属期止，并取月末最后一天
//                    final Date skssqq_zys = CchxwsnssbGyUtils.minmaxDate(list_skssqq_zys, "min");
//                    final Date skssqz_zys = CchxwsnssbGyUtils.minmaxDate(list_skssqz_zys, "max");
//                    String skssqq_init = null;
//                    String skssqz_init = null;
//                    if(!GyUtils.isNull(skssqq_zys)&&!GyUtils.isNull(skssqz_zys)){
//                        skssqq_init = DateUtil.format(CchxwsnssbGyUtils.getFirstDayofMonth(skssqq_zys), "yyyy-MM-dd");
//                        skssqz_init = DateUtil.format(CchxwsnssbGyUtils.getlastDayofMonth(skssqz_zys), "yyyy-MM-dd");
//                    }
//                    // initResMxVO已更新，可直接使用
//                    if(!GyUtils.isNull(skssqq_init) && !GyUtils.isNull(skssqz_init)) {
//                        //此时initResMxVO已在方法里处理，返回值为同一个对象，不用接收重新赋值
//                        this.getPhjmzg(cxstysbNsrxxVO, initResMxVO, skssqq_init, skssqz_init);
//                    }
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                    //组装六税两费信息
//                    cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
//                    cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());
//                    cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
//                    cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
//                    if(!GyUtils.isNull(symxGridlb)) {
//                        //调用财行税申报核心初始化接口
//                        try {
//                            this.initCxstysbHX(GyConstants.getDmGyZsxmZys(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO);
//                        } catch (Exception e) {
//                            this.getInitCxstysbError(e, GyConstants.getDmGyZsxmZys(), cchxwsnssbbxxVO);
//                            return cchxwsnssbbxxVO;
//                        }
//                    }
//                }else {
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                }
//            }
//            else if(initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmGdzys())){//耕地占用税分支
//                //调用税源查询服务
//                final List<SBCxsGdzysSyxxVO> sbCxsGdzysSyxxGridlb = this.queryForSb("SWZJ.HXZG.SB.QUERYGDZYSSYXXFORSB", cxstysbNsrxx, zgswjgdm, yhlx, SBCxsGdzysSyxxVO.class);
//                if (!GyUtils.isNull(sbCxsGdzysSyxxGridlb)) {
//                    final List<String> list_skssqq_gdzys = new ArrayList<>();
//                    final List<String> list_skssqz_gdzys = new ArrayList<>();
//                    final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//                    for (SBCxsGdzysSyxxVO gdzysSyxxVO : sbCxsGdzysSyxxGridlb) {
//                        //对查询出的税源信息处理，按次
//                        final Date gzsSkssqq = DateUtil.toDate(gdzysSyxxVO.getSkssqq(), "yyyy-MM-dd");
//                        final Date gzsSkssqz = DateUtil.toDate(gdzysSyxxVO.getSkssqz(), "yyyy-MM-dd");
//                        //判断税源是否有效
//                        final boolean syflag = this.filterSyxxBySkssq(mrskssqq, mrskssqz, gzsSkssqq, gzsSkssqz, cxstysbNsrxxVO.getSsnd());
//                        if (syflag) {
//                            //核心初始化接口入参
//                            SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                            symxGridlbVO.setSyuuid(gdzysSyxxVO.getSyuuid());
//                            symxGridlbVO.setSybzDm1("");
//                            symxGridlb.add(symxGridlbVO);
//                            //申报保存时使用的税源信息
//                            final SymxVO symxVO = new SymxVO();
//                            symxVO.setSyuuid(gdzysSyxxVO.getSyuuid());
//                            symxVO.setSybzDm1("");
//                            //2023-12-21 禅道48830 占地方式-未批先占-分支
//                            symxVO.setZdfsDm(gdzysSyxxVO.getZdfs1Dm());
//                            symxVOList_save.add(symxVO);
//                            //计算六税两费信息使用
//                            list_skssqq_gdzys.add(gdzysSyxxVO.getSkssqq());
//                            list_skssqz_gdzys.add(gdzysSyxxVO.getSkssqz());
//                        }
//                    }
//                    //获取最小税款所属期起，并取月初第一天；获取最大税款所属期止，并取月末最后一天
//                    Date skssqq_gdzys = CchxwsnssbGyUtils.minmaxDate(list_skssqq_gdzys, "min");
//                    Date skssqz_gdzys = CchxwsnssbGyUtils.minmaxDate(list_skssqz_gdzys, "max");
//                    String skssqq_init = null;
//                    String skssqz_init = null;
//                    if(!GyUtils.isNull(skssqq_gdzys)&&!GyUtils.isNull(skssqz_gdzys)){
//                        skssqq_init = DateUtil.format(CchxwsnssbGyUtils.getFirstDayofMonth(skssqq_gdzys), "yyyy-MM-dd");
//                        skssqz_init = DateUtil.format(CchxwsnssbGyUtils.getlastDayofMonth(skssqz_gdzys), "yyyy-MM-dd");
//                    }
//                    // initResMxVO已更新，可直接使用
//                    if(!GyUtils.isNull(skssqq_init) && !GyUtils.isNull(skssqz_init)) {
//                        //此时initResMxVO已在方法里处理，返回值为同一个对象，不用接收重新赋值
//                        this.getPhjmzg(cxstysbNsrxxVO, initResMxVO, skssqq_init, skssqz_init);
//                    }
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                    //组装六税两费信息
//                    cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
//                    cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());
//                    cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
//                    cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
//                    if(!GyUtils.isNull(symxGridlb)) {
//                        if ("Y".equals(gdzysWpxzCsz)) {
//                            //Y为开启区分，N为原逻辑
//                            final List<SymxGridlbVO> wpxzList = new ArrayList<>();
//                            final List<SymxGridlbVO> otherList = new ArrayList<>();
//                            final List<Map<String, Object>> symxListAll = this.getGdzysWpxzSyList(symxVOList_save, new ArrayList<>(), yhlx);
//                            gdzysLslfMrz = this.clGdzysWpxz(symxListAll, symxGridlb, wpxzList, otherList);
//                            if (!GyUtils.isNull(wpxzList)) {
//                                final CxstysbNsrxx bxsCopy = this.getBxsNsrVO(cxstysbNsrxx);
//                                this.initCxstysbHX(GyConstants.getDmGyZsxmGdzys(), bxsCopy, wpxzList, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO);
//                            }
//                            if (!GyUtils.isNull(otherList)) {
//                                this.initCxstysbHX(GyConstants.getDmGyZsxmGdzys(), cxstysbNsrxx, otherList, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO);
//                            }
//                        } else {
//                            //调用财行税申报核心初始化接口
//                            this.initCxstysbHX(GyConstants.getDmGyZsxmGdzys(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO);
//                        }
//                    }
//                }else{
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                }
//            }
//            else if(initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmQs())){//契税分支
//                //调用税源查询服务
//                final List<SBCxsQssyxxVO> sbCxsQssyxxVO = this.queryForSb("SWZJ.HXZG.SB.QUERYQSSYXXFORSB", cxstysbNsrxx, zgswjgdm, yhlx, SBCxsQssyxxVO.class);
//                if (!GyUtils.isNull(sbCxsQssyxxVO)) {
//                    final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//                    for (SBCxsQssyxxVO qssyxxVO : sbCxsQssyxxVO) {
//                        //对查询出的税源信息处理，按次
//                        final Date qsSkssqq = DateUtil.toDate(qssyxxVO.getSkssqq(), "yyyy-MM-dd");
//                        final Date qsSkssqz = DateUtil.toDate(qssyxxVO.getSkssqz(), "yyyy-MM-dd");
//                        //判断税源是否有效
//                        final boolean syflag = this.filterSyxxBySkssq(mrskssqq, mrskssqz, qsSkssqq, qsSkssqz, cxstysbNsrxxVO.getSsnd());
//                        if (syflag) {
//                            //核心初始化接口入参
//                            final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                            symxGridlbVO.setSyuuid(qssyxxVO.getSyuuid());
//                            symxGridlbVO.setSybzDm1(qssyxxVO.getSybzDm1());
//                            symxGridlb.add(symxGridlbVO);
//                            //申报保存时使用的税源信息
//                            final SymxVO symxVO = new SymxVO();
//                            symxVO.setSyuuid(qssyxxVO.getSyuuid());
//                            symxVO.setSybzDm1(qssyxxVO.getSybzDm1());
//                            symxVOList_save.add(symxVO);
//                        }
//                    }
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                    if(!GyUtils.isNull(symxGridlb)) {
//                        //调用财行税申报核心初始化接口
//                        this.initCxstysbHX(GyConstants.getDmGyZsxmQs(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO);
//                    }
//                }else{
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                }
//            }
//            else if(initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmHbs())){//环保税分支
//                final List<HbsCjSyxx> hbsCjSyxxGridLb = this.queryForSb("SWZJ.HXZG.SB.QUERYHBSSYXXFORSB", cxstysbNsrxx, zgswjgdm, yhlx, HbsCjSyxx.class);
//                if (!GyUtils.isNull(hbsCjSyxxGridLb)) {
//                    final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//                    for (HbsCjSyxx hbsCjSyxx : hbsCjSyxxGridLb) {
//                        //对查询出的税源信息处理，按次+多种属期
//                        final Date hbsSkssqq = DateUtil.toDate(hbsCjSyxx.getSkssqq(), "yyyy-MM-dd");
//                        final Date hbsSkssqz = DateUtil.toDate(hbsCjSyxx.getSkssqz(), "yyyy-MM-dd");
//                        //判断税源是否有效
//                        boolean syflag = this.filterSyxxBySkssq(mrskssqq, mrskssqz, hbsSkssqq, hbsSkssqz, cxstysbNsrxxVO.getSsnd());
//                        //2023-05-04，环保税有包含月的按季税源，需要特殊处理，不选年度分支：例如默认属期2023-01~2023-03，需要带出2023-01,2023-02,2023-03,2023-01~2023-03
//                        if(GyUtils.isNull(cxstysbNsrxxVO.getSsnd()) && !syflag){
//                            if(mrskssqq.compareTo(mrskssqz) != 0 && mrskssqq.compareTo(hbsSkssqq) <= 0 && mrskssqz.compareTo(hbsSkssqz) >= 0){
//                                syflag = true;
//                            }
//                        }
//                        if (syflag) {
//                            //核心初始化接口入参
//                            final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                            symxGridlbVO.setSyuuid(hbsCjSyxx.getSyuuid());
//                            symxGridlbVO.setSybzDm1("");
//                            symxGridlb.add(symxGridlbVO);
//                            //申报保存时使用的税源信息
//                            final SymxVO symxVO = new SymxVO();
//                            symxVO.setSyuuid(hbsCjSyxx.getSyuuid());
//                            symxVO.setSybzDm1("");
//                            symxVOList_save.add(symxVO);
//                            //环保税排序使用
//                            hbsSyxxBaseSort.add(hbsCjSyxx);
//                        }
//                    }
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                    if(!GyUtils.isNull(symxGridlb)) {
//                        //调用财行税申报核心初始化接口
//                        try {
//                            this.initCxstysbHX(GyConstants.getDmGyZsxmHbs(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO);
//                        } catch (Exception e) {
//                            this.getInitCxstysbError(e, GyConstants.getDmGyZsxmHbs(), cchxwsnssbbxxVO);
//                            return cchxwsnssbbxxVO;
//                        }
//                    }
//                }else{
//                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                    mxVOList.add(initResMxVO);
//                }
//            }
//            else if(initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmCcs())){
//                //车船税分支，因为车船税总是会有勾选，所以此分支大概率进不来，车船税只会走勾选税源分支和逾期申报分支(选往年的情况)
//                if(GyUtils.isNull(cxstysbNsrxxVO.getSsnd())){
//                    //车船税为单属期税种，如果未选择年度，此时取默认期限，一般为本年度
//                    cxstysbNsrxx.setSkssqq(initResMxVO.getMrskssqq());
//                    cxstysbNsrxx.setSkssqz(initResMxVO.getMrskssqz());
//                }
//                initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                mxVOList.add(initResMxVO);
//                //调用财行税申报核心初始化接口
//                final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//                this.initCxstysbHX(GyConstants.getDmGyZsxmCcs(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO);
//            }
            else if (initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmFcs()) && GyUtils.isNull(cxstysbNsrxxVO.getSsnd())) {
                //房产税分支，选择年度不走此分支
                //配置房土税源数量
                //判断是否启用异步申报
                initResMxVO.setFctdsmust("");
                initResMxVO.setFttip("N");
                log.info("---initResMxVO.getBqsfsyzzsxgmnsrjzzc()----{}",initResMxVO.getBqsfsyzzsxgmnsrjzzc());
                //如果进此分支，证明为初始化，否则前台勾选，initResMxVO已更新，可直接使用
                if (GyUtils.isNull(initResMxVO.getBqsfsyzzsxgmnsrjzzc())) {
                    //此时initResMxVO已在方法里处理，返回值为同一个对象，不用接收重新赋值
                    this.getPhjmzgnew(initResMxVO, initResMxVO.getMrskssqq(), initResMxVO.getMrskssqz(), nsrzgxxVO, nsrbqxxVOList);
                }
                log.info("---initResMxVO----{}",initResMxVO);
                initResMxVO.setSkssqq(initResMxVO.getMrskssqq());
                initResMxVO.setSkssqz(initResMxVO.getMrskssqz());
                mxVOList.add(initResMxVO);
                //组装六税两费信息
                cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
                cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());
                cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
                cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
                // 存在可申报税源信息，调用初始化接口!!
                //房产税为单属期税种，此时取默认期限
                cxstysbNsrxx.setSkssqq(initResMxVO.getMrskssqq());
                cxstysbNsrxx.setSkssqz(initResMxVO.getMrskssqz());
                //调用财行税申报核心初始化接口
                final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
                this.initCxstysbHX(initResMxVO.getZsxmdm(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO, initResMxVO, cchxwsnssbquerysbbxxReqVO);
            } else if (initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmCztdsys()) && GyUtils.isNull(cxstysbNsrxxVO.getSsnd())) {
                //城镇土地使用税分支，选择年度不走此分支
                initResMxVO.setFttip("N");
                //如果进此分支，证明为初始化，否则前台勾选，initResMxVO已更新，可直接使用
                if (GyUtils.isNull(initResMxVO.getBqsfsyzzsxgmnsrjzzc())) {
                    //此时initResMxVO已在方法里处理，返回值为同一个对象，不用接收重新赋值
                    this.getPhjmzgnew(initResMxVO, initResMxVO.getMrskssqq(), initResMxVO.getMrskssqz(), nsrzgxxVO, nsrbqxxVOList);
                }
                initResMxVO.setSkssqq(initResMxVO.getMrskssqq());
                initResMxVO.setSkssqz(initResMxVO.getMrskssqz());
                mxVOList.add(initResMxVO);
                //组装六税两费信息
                cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
                cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());
                cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
                cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
                //城镇土地使用税为单属期税种，此时取默认期限
                cxstysbNsrxx.setSkssqq(initResMxVO.getMrskssqq());
                cxstysbNsrxx.setSkssqz(initResMxVO.getMrskssqz());
                //调用财行税申报核心初始化接口
                final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
                this.initCxstysbHX(initResMxVO.getZsxmdm(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO, initResMxVO, cchxwsnssbquerysbbxxReqVO);
            } else if (initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmYys()) && GyUtils.isNull(cxstysbNsrxxVO.getSsnd())) {
                //烟叶税分支，选择年度不走此分支
                initResMxVO.setSkssqq(initResMxVO.getMrskssqq());
                initResMxVO.setSkssqz(initResMxVO.getMrskssqz());
                mxVOList.add(initResMxVO);
                //烟叶税税为按期申报税种，此时取默认期限
                cxstysbNsrxx.setSkssqq(initResMxVO.getMrskssqq());
                cxstysbNsrxx.setSkssqz(initResMxVO.getMrskssqz());
                try {
                    LambdaQueryWrapper<ZnsbNssbSbrwDO> znsbNssbSbrwDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    znsbNssbSbrwDOLambdaQueryWrapper.eq(ZnsbNssbSbrwDO::getSbrwuuid, cchxwsnssbquerysbbxxReqVO.getSbrwuuid())
                            .in(ZnsbNssbSbrwDO::getNsrsbztDm, SBZT_SBSB_DM, SBZT_SHBTG_DM, SBZT_WSB_DM,SBZT_ZF_ZFCG_DM);
                    ZnsbNssbSbrwDO znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(znsbNssbSbrwDOLambdaQueryWrapper);
                    if (GyUtils.isNotNull(znsbNssbSbrwDO) && GyUtils.isNotNull(znsbNssbSbrwDO.getSyuuid1())) {
                        //组装syuuid
                        List<String> syuuidList = Arrays.asList(znsbNssbSbrwDO.getSyuuid1().substring(1, znsbNssbSbrwDO.getSyuuid1().length() - 1).split(","));
                        final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
                        for (String syuuid : syuuidList) {
                            final SymxVO symxVO = new SymxVO();
                            symxVO.setSyuuid(syuuid);
                            symxVO.setSybzDm1("");
                            symxVOList_save.add(symxVO);
                            final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
                            symxGridlbVO.setSyuuid(syuuid);
                            symxGridlbVO.setSybzDm1("");
                            symxGridlb.add(symxGridlbVO);
                        }
                        if (GyUtils.isNotNull(symxGridlb)) {
                            //调用财行税申报核心初始化接口
                            this.initCxstysbHX(GyConstants.getDmGyZsxmYys(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO, initResMxVO, cchxwsnssbquerysbbxxReqVO);
                        }
                    }
                } catch (Exception e) {
                    this.getInitCxstysbError(e, initResMxVO.getZsxmdm(), cchxwsnssbbxxVO);
                    return cchxwsnssbbxxVO;
                }
            }
//            else if(initResMxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmTdzzs())){//土地增值税分支
//                final Date qqInit = DateUtil.toDate(cxstysbNsrxx.getSkssqq(), "yyyy-MM-dd");
//                final Date qzInit = DateUtil.toDate(cxstysbNsrxx.getSkssqz(), "yyyy-MM-dd");
//                initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                mxVOList.add(initResMxVO);
//                //预征、尾盘获取有效税款所属期起止和申报日期起止Map，如果为空则未获取到
//                final Map<String, Date> tdzzsSfzrdxx = getTdzzsSfzrdxx(cxstysbNsrxxVO.getDjxh(), zgswjgdm);
//                //土增有跨年情况，先查出全量信息
//                cxstysbNsrxx.setSkssqq("");
//                cxstysbNsrxx.setSkssqz("");
//                //调用税源查询服务
//                final List<TdzzssbsyGridlbVO> tdzzssbsyGridlb = this.queryForSb("SWZJ.HXZG.SB.QUERYTDZZSSYXXFORSB", cxstysbNsrxx, zgswjgdm, yhlx, TdzzssbsyGridlbVO.class);
//                if (!GyUtils.isNull(tdzzssbsyGridlb)) {
//                    final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//                    for (TdzzssbsyGridlbVO tdzzssbsyGridlbVO : tdzzssbsyGridlb) {
//                        //对查询出的税源信息处理
//                        final Date nowDate = DateUtil.toDate(DateUtil.format(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
//                        final Date tzSkssqq = DateUtil.toDate(tdzzssbsyGridlbVO.getSkssqq(), "yyyy-MM-dd");
//                        final Date tzSkssqz = DateUtil.toDate(tdzzssbsyGridlbVO.getSkssqz(), "yyyy-MM-dd");
//                        if(!GyUtils.isNull(tdzzsSfzrdxx)) {
//                            final Date skssqqYz = tdzzsSfzrdxx.get("skssqqYz");
//                            final Date skssqzYz = tdzzsSfzrdxx.get("skssqzYz");
//                            final Date sbrqqYz = tdzzsSfzrdxx.get("sbrqqYz");
//                            final Date sbrqzYz = tdzzsSfzrdxx.get("sbrqzYz");
//                            final Date skssqqWp = tdzzsSfzrdxx.get("skssqqWp");
//                            final Date skssqzWp = tdzzsSfzrdxx.get("skssqzWp");
//                            final Date sbrqqWp = tdzzsSfzrdxx.get("sbrqqWp");
//                            final Date sbrqzWp = tdzzsSfzrdxx.get("sbrqzWp");
//                            //预征分支
//                            if("1".equals(tdzzssbsyGridlbVO.getTdzzssbblx()) &&
//                                    !GyUtils.isNull(skssqqYz) && !GyUtils.isNull(skssqzYz) &&
//                                    !GyUtils.isNull(sbrqqYz) && !GyUtils.isNull(sbrqzYz)){
//                                //税源属期和计算后属期相同，并且当前日期在申报期内则有效，存疑？
//                                if(tzSkssqq.compareTo(skssqqYz)==0 && tzSkssqz.compareTo(skssqzYz)==0 && !sbrqzYz.before(nowDate) && !sbrqqYz.after(nowDate)){
//                                    //选择年度不带处按期税源
//                                    if(GyUtils.isNull(cxstysbNsrxxVO.getSsnd())){
//                                        //核心初始化接口入参
//                                        final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                                        symxGridlbVO.setSyuuid(tdzzssbsyGridlbVO.getSyuuid());
//                                        symxGridlbVO.setSybzDm1(tdzzssbsyGridlbVO.getSybzDm1());
//                                        symxGridlb.add(symxGridlbVO);
//                                        //申报保存时使用的税源信息
//                                        final SymxVO symxVO = new SymxVO();
//                                        symxVO.setSyuuid(tdzzssbsyGridlbVO.getSyuuid());
//                                        symxVO.setSybzDm1(tdzzssbsyGridlbVO.getSybzDm1());
//                                        symxVOList_save.add(symxVO);
//                                    }
//                                }
//                                continue;
//                            }
//                            //尾盘分支
//                            if("4".equals(tdzzssbsyGridlbVO.getTdzzssbblx()) &&
//                                    !GyUtils.isNull(skssqqWp) && !GyUtils.isNull(skssqzWp) &&
//                                    !GyUtils.isNull(sbrqqWp) && !GyUtils.isNull(sbrqzWp)) {
//                                //税源属期和计算后属期相同，并且当前日期在申报期内则有效，存疑？
//                                if(tzSkssqq.compareTo(skssqqWp)==0 && tzSkssqz.compareTo(skssqzWp)==0 && !sbrqzWp.before(nowDate) && !sbrqqWp.after(nowDate)){
//                                    //选择年度不带处按期税源
//                                    if(GyUtils.isNull(cxstysbNsrxxVO.getSsnd())){
//                                        //核心初始化接口入参
//                                        final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                                        symxGridlbVO.setSyuuid(tdzzssbsyGridlbVO.getSyuuid());
//                                        symxGridlbVO.setSybzDm1(tdzzssbsyGridlbVO.getSybzDm1());
//                                        symxGridlb.add(symxGridlbVO);
//                                        //申报保存时使用的税源信息
//                                        final SymxVO symxVO = new SymxVO();
//                                        symxVO.setSyuuid(tdzzssbsyGridlbVO.getSyuuid());
//                                        symxVO.setSybzDm1(tdzzssbsyGridlbVO.getSybzDm1());
//                                        symxVOList_save.add(symxVO);
//                                    }
//                                }
//                                continue;
//                            }
//                        }
//                        //其他分支，正常此时只应该带出按次税源，但是是否现在会带出无效按期税源，存疑？
//                        if(!tzSkssqz.before(qqInit) && !tzSkssqz.after(qzInit)){
//                            //核心初始化接口入参
//                            final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                            symxGridlbVO.setSyuuid(tdzzssbsyGridlbVO.getSyuuid());
//                            symxGridlbVO.setSybzDm1(tdzzssbsyGridlbVO.getSybzDm1());
//                            symxGridlb.add(symxGridlbVO);
//                            //申报保存时使用的税源信息
//                            final SymxVO symxVO = new SymxVO();
//                            symxVO.setSyuuid(tdzzssbsyGridlbVO.getSyuuid());
//                            symxVO.setSybzDm1(tdzzssbsyGridlbVO.getSybzDm1());
//                            symxVOList_save.add(symxVO);
//                        }
//                    }
//                    if(!GyUtils.isNull(symxGridlb)){
//                        try {
//                            //调用财行税申报核心初始化接口
//                            this.initCxstysbHX(GyConstants.getDmGyZsxmTdzzs(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO);
//                        } catch (Exception e) {
//                            this.getInitCxstysbError(e, GyConstants.getDmGyZsxmTdzzs(), cchxwsnssbbxxVO);
//                            return cchxwsnssbbxxVO;
//                        }
//                    }
//                }
//            }
        } else {
            //勾选税源情况
            //未选择年度情况，或者不是房土，印花、资源、耕占、烟叶税无勾选税源，不会进此分支
            if (GyUtils.isNull(cxstysbNsrxxVO.getSsnd()) || !"10110,10112,10120".contains(initResMxVO.getZsxmdm())) {
                if (GyUtils.isNull(cxstysbNsrxxVO.getSsnd())) {//未选择年度情况
                    if (GyUtils.isNull(initResMxVO.getBqsfsyzzsxgmnsrjzzc())) {
                        //如果进此分支，证明为初始化，否则前台勾选，initResMxVO已更新，可直接使用
                        if ("Y".equals(initResMxVO.getSzacsbbj())) {//印花、资源、耕地、契税、环保、土增
                            //此时initResMxVO已在方法里处理，返回值为同一个对象，不用接收重新赋值
                            this.getPhjmzgnew(initResMxVO, initResMxVO.getMrskssqq(), initResMxVO.getMrskssqz(), nsrzgxxVO, nsrbqxxVOList);
                            initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
                            initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
                            mxVOList.add(initResMxVO);
                        } else if ("N".equals(initResMxVO.getSzacsbbj())) {//房、土、车、烟叶
                            //按期申报税源，需要使用默认属期，否则范围会变大
                            this.getPhjmzgnew(initResMxVO, initResMxVO.getMrskssqq(), initResMxVO.getMrskssqz(), nsrzgxxVO, nsrbqxxVOList);
                            initResMxVO.setSkssqq(initResMxVO.getMrskssqq());
                            initResMxVO.setSkssqz(initResMxVO.getMrskssqz());
                            mxVOList.add(initResMxVO);
                            cxstysbNsrxx.setSkssqq(initResMxVO.getSkssqq());
                            cxstysbNsrxx.setSkssqz(initResMxVO.getSkssqz());
                        }
                    } else {
                        //此时是前台勾选是否减免，直接取initResMxVO信息
                        if ("Y".equals(initResMxVO.getSzacsbbj())) {//印花、资源、耕地
                            initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
                            initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
                            mxVOList.add(initResMxVO);
                        } else if ("N".equals(initResMxVO.getSzacsbbj())) {//房、土
                            //按期申报税源，需要使用默认属期，否则范围会变大，初始化时，skss 和 mrskss已经赋值同数
                            initResMxVO.setSkssqq(initResMxVO.getMrskssqq());
                            initResMxVO.setSkssqz(initResMxVO.getMrskssqz());
                            mxVOList.add(initResMxVO);
                            cxstysbNsrxx.setSkssqq(initResMxVO.getSkssqq());
                            cxstysbNsrxx.setSkssqz(initResMxVO.getSkssqz());
                        }
                    }
                } else {
                    //选择年度情况，只处理含有按次税源，车船税选往年会走逾期，选本后年会走这里，正常本后年不应该让添加车船税
                    initResMxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
                    initResMxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
                    mxVOList.add(initResMxVO);
                }
                cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
                cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());
                cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
                cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
                //遍历勾选税源信息
                final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
                for (SbxxVO sbxxVO : sbxxVOList) {
                    //勾选的税源可能只有征收项目，此时为查询全量数据
                    if (!GyUtils.isNull(sbxxVO.getSymxList())) {
                        for (SymxVO symxVO : sbxxVO.getSymxList()) {
                            if (!GyUtils.isNull(symxVO.getSyuuid())) {
                                //核心初始化接口入参
                                final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
                                symxGridlbVO.setSyuuid(symxVO.getSyuuid());
                                symxGridlbVO.setSybzDm1(symxVO.getSybzDm1());
                                symxGridlb.add(symxGridlbVO);
                                //申报保存时使用的税源信息
                                symxVOList_save.add(symxVO);
                            }
                        }
                    }
                }
                //调用财行税申报核心初始化接口
                try {
                    if ("10118".equals(initResMxVO.getZsxmdm()) && "Y".equals(gdzysWpxzCsz)) {
                        //Y为开启区分，N为原逻辑
                        final List<SymxGridlbVO> wpxzList = new ArrayList<>();
                        final List<SymxGridlbVO> otherList = new ArrayList<>();
                        final List<Map<String, Object>> symxListAll = this.getGdzysWpxzSyList(symxVOList_save, new ArrayList<>(), yhlx);
                        gdzysLslfMrz = this.clGdzysWpxz(symxListAll, symxGridlb, wpxzList, otherList);
                        if (!GyUtils.isNull(wpxzList)) {
                            final CxstysbNsrxx bxsCopy = this.getBxsNsrVO(cxstysbNsrxx);
                            this.initCxstysbHX(initResMxVO.getZsxmdm(), bxsCopy, wpxzList, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO, initResMxVO, cchxwsnssbquerysbbxxReqVO);
                        }
                        if (!GyUtils.isNull(otherList)) {
                            this.initCxstysbHX(initResMxVO.getZsxmdm(), cxstysbNsrxx, otherList, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO, initResMxVO, cchxwsnssbquerysbbxxReqVO);
                        }
                    } else {
                        this.initCxstysbHX(initResMxVO.getZsxmdm(), cxstysbNsrxx, symxGridlb, zgswjgdm, sbxxGridlb, bqjmsemxbywbwList, cxsSymxReqVO, initResMxVO, cchxwsnssbquerysbbxxReqVO);
                    }
                } catch (Exception e) {
                    this.getInitCxstysbError(e, initResMxVO.getZsxmdm(), cchxwsnssbbxxVO);
                    return cchxwsnssbbxxVO;
                }
            }
        }

        //填充内部返回值
        final List<CchxwsnssbSaveQueryVO> cchxwsnssbSaveQueryVOList = new ArrayList<>();
        final CchxwsnssbSaveQueryVO cchxwsnssbSaveQueryVO = new CchxwsnssbSaveQueryVO();
        cchxwsnssbSaveQueryVO.setZsxmDm(initResMxVO.getZsxmdm());
        cchxwsnssbSaveQueryVO.setSymxVOList(symxVOList_save);
        cchxwsnssbSaveQueryVOList.add(cchxwsnssbSaveQueryVO);

        //主表VO
        final CchxwsnssbzbVO cchxwsnssbzbVO = new CchxwsnssbzbVO();
        cchxwsnssbzbVO.setDjxh(cxstysbNsrxxVO.getDjxh());
        cchxwsnssbzbVO.setNsrmc(cxstysbNsrxxVO.getNsrmc());
        cchxwsnssbzbVO.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
        //征收表税款属性代码0101为一般申报，此处可能有问题，但是没用到这个字段
        cchxwsnssbzbVO.setSksxDm("11");
        cchxwsnssbzbVO.setSbrq(CchxwsnssbGyUtils.getNowTime());
        cchxwsnssbzbVO.setHyDm(hydm);
        cchxwsnssbzbVO.setZgswskfjDm(zgswjgdm);
        cchxwsnssbzbVO.setCxstysbuuid("");
        cchxwsnssbzbVO.setPzxh("");
        cchxwsnssbzbVO.setSbuuid("");
        cchxwsnssbzbVO.setYzpzzlDm("BDA0611148");

//        //针对环保税对主表明细增加税源编号
//        if(GyConstants.getDmGyZsxmHbs().equals(initResMxVO.getZsxmdm())
//                && !GyUtils.isNull(symxVOList_save) && !GyUtils.isNull(sbxxGridlb)){
//            this.addSybhZbbmxByHbs(hbsSyxxBaseSort, symxVOList_save, sbxxGridlb, cxstysbNsrxx, zgswjgdm ,yhlx);
//        }

//        //汇总申报明细VOList，根据zsxmDm，zspmDm，skssqq，skssqz，sl1分组汇总，资源、环保特殊处理
//        final List<SbxxGridlbVO> hzzbList = CchxwsnssbGyUtils.hzsbxx(sbxxGridlb, new HashMap<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new String[]{"zsxmDm", "zspmDm", "skssqq", "skssqz", "sl1"}, true);
//        //处理汇总明细VOList
//        final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList = this.dealWithZbmxList(hzzbList, yhlx, cxstysbNsrxxVO.getDjxh());
//        List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList_sort = new ArrayList<>();
//        if (!GyUtils.isNull(cchxwsnssbzbmxVOList)) {
//            cchxwsnssbzbmxVOList_sort = cchxwsnssbzbmxVOList.stream().sorted(Comparator.comparing(CchxwsnssbzbmxVO::getZspmDm, Comparator.nullsLast(Comparator.naturalOrder()))
//                    .thenComparing(Comparator.comparing(CchxwsnssbzbmxVO::getSkssqz).reversed())).collect(Collectors.toList());
//        }
//
//        //附表减免明细VOList，分组汇总，正常为zsxmDm，skssqq，skssqz，sybh分组，资源、印花、环保特殊处理
//        final List<BqjmsemxbGridlbVO> hzFbJmxxList = this.hzFbJmxx(bqjmsemxbywbwList);
//        //处理附表明细VOList
//        final List<CchxwsnssbfbmxVO> cchxwsnssbfbmxVOList = this.dealWithFbmxList(hzFbJmxxList);
//
//        //2023.06.15 禅道37799，对环保税主附表明细增加排序
//        if (GyConstants.getDmGyZsxmHbs().equals(initResMxVO.getZsxmdm()) && !GyUtils.isNull(cchxwsnssbzbmxVOList_sort)) {
//            this.sortZbbmxByHbs(cchxwsnssbzbmxVOList_sort, cchxwsnssbfbmxVOList);
//        }
//        //2023.09.11 房土添加税源明细信息
//        if (GyConstants.getDmGyZsxmCztdsys().equals(initResMxVO.getZsxmdm()) || GyConstants.getDmGyZsxmFcs().equals(initResMxVO.getZsxmdm())) {
//            cxsSymxReqVO.setZsxmDm(initResMxVO.getZsxmdm());
//            cchxwsnssbbxxVO.setCxsSymxReqVO(cxsSymxReqVO);
//        }
//        //处理卡片税款信息VOList
//        final List<CchxwsnssbKpVO> kpVOList = this.dealWithKpList(cchxwsnssbzbmxVOList_sort, initResMxVO.getZsxmdm(), "Y");
//
//        //处理返回值
//        final List<CchxwsnssbKpVO> kpmcVOList = CchxwsnssbGyUtils.getZsxmMcKp(kpVOList);
//        final List<CchxwsnssbzbmxVO> zbmxmcVOList = CchxwsnssbGyUtils.getZsxmMcZbmx(cchxwsnssbzbmxVOList_sort);
//        final List<CchxwsnssbfbmxVO> fbmxmcVOList = CchxwsnssbGyUtils.getZsxmMcFbmx(cchxwsnssbfbmxVOList);

        // 处理主附表明细信息
        final CchxwsnssbbxxVO sbbxxVO = this.handleZfbmxList(sbxxGridlb, bqjmsemxbywbwList, initResMxVO.getZsxmdm());

        final List<CchxwsnssbKpVO> kpmcVOList = new ArrayList<>();
        final List<CchxwsnssbzbmxVO> zbmxmcVOList = sbbxxVO.getCchxwsnssbzbmxVOList();
        final List<CchxwsnssbfbmxVO> fbmxmcVOList = sbbxxVO.getCchxwsnssbfbmxVOList();

        log.info("----cchxwsnssbzbmxVOList-----{}",zbmxmcVOList);
        log.info("----cchxwsnssbfbmxVOList-----{}",fbmxmcVOList);
        log.info("----initResMxVO-----{}",initResMxVO);

        //处理六税两费减免
        this.clLslfJmxx(zbmxmcVOList,fbmxmcVOList);

        cchxwsnssbbxxVO.setCchxwsnssbzbVO(cchxwsnssbzbVO);
        cchxwsnssbbxxVO.setCchxwsnssbzbmxVOList(zbmxmcVOList);
        cchxwsnssbbxxVO.setCchxwsnssbfbmxVOList(fbmxmcVOList);
        cchxwsnssbbxxVO.setCchxwsnssbKpVOList(kpmcVOList);
        cchxwsnssbbxxVO.setCchxwsnssbSaveQueryVOList(cchxwsnssbSaveQueryVOList);
        cchxwsnssbbxxVO.setInitResMxVOList(mxVOList);
        this.clGzMxVO(mxVOList, gdzysWpxzCsz, gdzysLslfMrz);
        return cchxwsnssbbxxVO;
    }

    @Override
    public CchxwsnssbbxxVO handleZfbmxList(List<SbxxGridlbVO> sbxxGridlb, List<Bqjmsemxbywbw> bqjmsemxbywbwList, String zsxmDm) throws Exception {
        final CchxwsnssbbxxVO cchxwsnssbbxxVO = new CchxwsnssbbxxVO();

        //汇总申报明细VOList，根据zsxmDm，zspmDm，skssqq，skssqz，sl1分组汇总，资源、环保特殊处理
        final List<SbxxGridlbVO> hzzbList = CchxwsnssbGyUtils.hzsbxx(sbxxGridlb, new HashMap<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new String[]{"zsxmDm", "zspmDm", "skssqq", "skssqz", "sl1"}, true);
        //处理汇总明细VOList
        final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList = this.dealWithZbmxList(hzzbList);
        List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList_sort = new ArrayList<>();
        if (!GyUtils.isNull(cchxwsnssbzbmxVOList)) {
            cchxwsnssbzbmxVOList_sort = cchxwsnssbzbmxVOList.stream().sorted(Comparator.comparing(CchxwsnssbzbmxVO::getZspmDm, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(Comparator.comparing(CchxwsnssbzbmxVO::getSkssqz).reversed())).collect(Collectors.toList());
        }

        //附表减免明细VOList，分组汇总，正常为zsxmDm，skssqq，skssqz，sybh分组，资源、印花、环保特殊处理
        final List<BqjmsemxbGridlbVO> hzFbJmxxList = this.hzFbJmxx(bqjmsemxbywbwList);
        //处理附表明细VOList
        final List<CchxwsnssbfbmxVO> cchxwsnssbfbmxVOList = this.dealWithFbmxList(hzFbJmxxList);

        //2023.06.15 禅道37799，对环保税主附表明细增加排序
        if (GyConstants.getDmGyZsxmHbs().equals(zsxmDm) && !GyUtils.isNull(cchxwsnssbzbmxVOList_sort)) {
            this.sortZbbmxByHbs(cchxwsnssbzbmxVOList_sort, cchxwsnssbfbmxVOList);
        }

        //2023.09.11 房土添加税源明细信息
        if (GyConstants.getDmGyZsxmCztdsys().equals(zsxmDm) || GyConstants.getDmGyZsxmFcs().equals(zsxmDm)) {
            final CxsSymxReqVO cxsSymxReqVO = new CxsSymxReqVO();
            cxsSymxReqVO.setZsxmDm(zsxmDm);
            cchxwsnssbbxxVO.setCxsSymxReqVO(cxsSymxReqVO);
        }

        final List<CchxwsnssbzbmxVO> zbmxmcVOList = CchxwsnssbGyUtils.getZsxmMcZbmx(cchxwsnssbzbmxVOList_sort);
        final List<CchxwsnssbfbmxVO> fbmxmcVOList = CchxwsnssbGyUtils.getZsxmMcFbmx(cchxwsnssbfbmxVOList);
        cchxwsnssbbxxVO.setCchxwsnssbzbmxVOList(zbmxmcVOList);
        cchxwsnssbbxxVO.setCchxwsnssbfbmxVOList(fbmxmcVOList);

        return cchxwsnssbbxxVO;
    }

    /**
     * 处理六税两费减免信息
     *
     * @param zbmxmcVOList  接口名称
     * @param fbmxmcVOList 入参
     */
    private void clLslfJmxx(List<CchxwsnssbzbmxVO> zbmxmcVOList, List<CchxwsnssbfbmxVO> fbmxmcVOList) {
        if (GyUtils.isNull(zbmxmcVOList) || GyUtils.isNull(fbmxmcVOList)){
            return;
        }
        //判断是否为房产税、城镇土地使用税
        final String zsxmDm = zbmxmcVOList.get(0).getZsxmDm();
        if (!FCS.getCode().equals(zsxmDm) && !CZTDSYS.getCode().equals(zsxmDm)){
            // 按照 zspm, zsxm, skssqq, skssqz, ynse 分组并求和 fbmxmcVOList 中的 jmse
            Map<String, Double> fbmxGroupSums = fbmxmcVOList.stream()
                    .collect(Collectors.groupingBy(
                            vo -> vo.getZsxmDm() + "|" + vo.getZspmDm() + "|" + vo.getSkssqq() + "|" + vo.getSkssqz(),
                            Collectors.summingDouble(CchxwsnssbfbmxVO::getJmse)
                    ));

            // 更新 zbmxmcVOList 中对应的 jmse
            zbmxmcVOList.forEach(vo -> {
                String groupKey = vo.getZsxmDm() + "|" + vo.getZspmDm() + "|" + vo.getSkssqq() + "|" + vo.getSkssqz();
                Double jmse = fbmxGroupSums.get(groupKey);
                vo.setJmse(roundToTwoDecimalPlaces(jmse)); // 设置 zbmxmcVOList 的 jmse 为 fbmxmcVOList 对应分组的 jmse 总和，并保留两位小数

                // 计算 ybtse = ynse - jmse，并保留两位小数
                Double ybtse = vo.getYnse() - vo.getJmse();
                vo.setYbtse(roundToTwoDecimalPlaces(ybtse)); // 保留两位小数
            });
        }
//        if (FCS.getCode().equals(zsxmDm) || CZTDSYS.getCode().equals(zsxmDm)){
//            // 按照 bdcbh 分组并求和 fbmxmcVOList 中的 jmse
//            Map<String, Double> fbmxGroupSums = fbmxmcVOList.stream()
//                    .collect(Collectors.groupingBy(
//                            CchxwsnssbfbmxVO::getSybh,
//                            Collectors.summingDouble(CchxwsnssbfbmxVO::getJmse)
//                    ));
//
//            // 更新 zbmxmcVOList 中对应的 jmse
//            zbmxmcVOList.forEach(vo -> {
//                Double jmse = fbmxGroupSums.get(vo.getSyuuid());
//                vo.setJmse(roundToTwoDecimalPlaces(jmse)); // 设置 zbmxmcVOList 的 jmse 为 fbmxmcVOList 对应分组的 jmse 总和，并保留两位小数
//
//                // 计算 ybtse = ynse - jmse，并保留两位小数
//                Double ybtse = vo.getYnse() - vo.getJmse();
//                vo.setYbtse(roundToTwoDecimalPlaces(ybtse)); // 保留两位小数
//            });
//        }
    }

    // 保留两位小数
    private static Double roundToTwoDecimalPlaces(Double value) {
        if (value == null) return 0.0;
        BigDecimal bigDecimal = new BigDecimal(value);
        return bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 调用ForSb接口
     *
     * @param serviceName  接口名称
     * @param cxstysbNsrxx 入参
     * @param sjjg         数据机关
     * @param <T>          返回值泛型
     * @return 返回值
     */
    private <T> List<T> queryForSb(String serviceName, CxstysbNsrxx cxstysbNsrxx, CxstysbNsrxxVO cxstysbNsrxxVO, String sjjg, String yhlx, Class<T> clazz) {
        final Map<String, String> expand = new HashMap<>();
        expand.put("sjjg", sjjg);
        //final String initService = CxsSbbEnum.getCxsSbbEnumByZsxm(zsxmDm).initService;
        if ("SWZJ.HXZG.SB.QUERYYHSSYXXFORSB".equals(serviceName)) {//印花税
            //印花税分支
            final HXZGSB10774Request hxzgsb10774Request = new HXZGSB10774Request();
            final HXZGSB10774Request.SymxGrid symxGrid1 = new HXZGSB10774Request.SymxGrid();
            hxzgsb10774Request.setCxstysbnsrxx(cxstysbNsrxx);
            hxzgsb10774Request.setSymxGrid(symxGrid1);
//            final HXZGSB10774Response hxzgsb10774Response = Gt3Invoker.invoke(serviceName, expand, hxzgsb10774Request, HXZGSB10774Response.class);
//            if(!GyUtils.isNull(hxzgsb10774Response) && !GyUtils.isNull(hxzgsb10774Response.getYhssbsyGrid()) && !GyUtils.isNull(hxzgsb10774Response.getYhssbsyGrid().getYhssbsyGridlb())){
//                final List<YhssbsyGridlbVO> yhssbsyGridlb = hxzgsb10774Response.getYhssbsyGrid().getYhssbsyGridlb();
//                return BeanCopierUtil.copyList(yhssbsyGridlb, clazz);
//            }
            CxstysbNsrxxDTO cxstysbNsrxxDTO = BeanUtil.copyProperties(cxstysbNsrxx, CxstysbNsrxxDTO.class);
            cxstysbNsrxxDTO.setNsqxDm(GyUtils.isNotNull(cxstysbNsrxxVO.getNsqxDm()) ? cxstysbNsrxxVO.getNsqxDm() : "");
            if (!GyUtils.isNull(cxstysbNsrxxVO.getNsqxDm()) && !cxstysbNsrxxVO.getNsqxDm().equals("11")) {
                cxstysbNsrxxDTO.setSkssqq(cxstysbNsrxxVO.getSkssqq());
                cxstysbNsrxxDTO.setSkssqz(cxstysbNsrxxVO.getSkssqz());
            }
            List<Map<String, Object>> maps = yhssycjService.queryYhsSyxxForSb(cxstysbNsrxxDTO, new ArrayList<>());
            maps = maps.stream().peek(e -> {
                e.put("skssqq", DateUtil.format((Date) e.get("skssqq"), "yyyy-MM-dd"));
                e.put("skssqz", DateUtil.format((Date) e.get("skssqz"), "yyyy-MM-dd"));
            }).collect(Collectors.toList());
//            String yhssyxx = DbUtils.getData("SWZJ.HXZG.SB.QUERYYHSSYXXFORSB");
            List<YhssbsyGridlbVO> yhssbsyGridlb = BeanUtil.copyToList(maps, YhssbsyGridlbVO.class);
            return BeanUtil.copyToList(yhssbsyGridlb, clazz);
        }
//        else if ("SWZJ.HXZG.SB.QUERYZYSSYXXFORSB".equals(serviceName)){//资源税
//            final HXZGSB10779Request hxzgsb10779Request = new HXZGSB10779Request();
//            final HXZGSB10779Request.SymxGrid symxGrid = new HXZGSB10779Request.SymxGrid();
//            hxzgsb10779Request.setCxstysbnsrxx(cxstysbNsrxx);
//            hxzgsb10779Request.setSymxGrid(symxGrid);
//            final HXZGSB10779Response hxzgsb10779Response = Gt3Invoker.invoke(serviceName, expand, hxzgsb10779Request, HXZGSB10779Response.class);
//            if(!GyUtils.isNull(hxzgsb10779Response) && !GyUtils.isNull(hxzgsb10779Response.getZyssbsyGrid()) && !GyUtils.isNull(hxzgsb10779Response.getZyssbsyGrid().getZyssbsyGridlb())){
//                final List<ZyssbsyGridlbVO> zyssbsyGridlb = hxzgsb10779Response.getZyssbsyGrid().getZyssbsyGridlb();
//                return BeanCopierUtil.copyList(zyssbsyGridlb, clazz);
//            }
//        }
//        else if ("SWZJ.HXZG.SB.QUERYGDZYSSYXXFORSB".equals(serviceName)){//耕地占用税，无grid
//            //耕地占用税分支
//            final HXZGSB10808Request hxzgsb10808Request = new HXZGSB10808Request();
//            hxzgsb10808Request.setCxNsrxxVO(cxstysbNsrxx);
//            final HXZGSB10808Response hxzgsb10808Response = Gt3Invoker.invoke(serviceName, expand, hxzgsb10808Request, HXZGSB10808Response.class);
//            if(!GyUtils.isNull(hxzgsb10808Response) && !GyUtils.isNull(hxzgsb10808Response.getSbCxsGdzysSyxxGridlb())){
//                final List<SBCxsGdzysSyxxVO> sbCxsGdzysSyxxGridlb = hxzgsb10808Response.getSbCxsGdzysSyxxGridlb();
//                return BeanCopierUtil.copyList(sbCxsGdzysSyxxGridlb, clazz);
//            }
//        }
//        else if ("SWZJ.HXZG.SB.QUERYQSSYXXFORSB".equals(serviceName)){//契税，无grid
//            final HXZGSB10798Request hxzgsb10798Request = new HXZGSB10798Request();
//            hxzgsb10798Request.setCxNsrxxVO(cxstysbNsrxx);
//            final HXZGSB10797Response hxzgsb10797Response = Gt3Invoker.invoke(serviceName, expand, hxzgsb10798Request, HXZGSB10797Response.class);
//            if(!GyUtils.isNull(hxzgsb10797Response) && !GyUtils.isNull(hxzgsb10797Response.getSbCxsQssyxxVO())){
//                final List<SBCxsQssyxxVO> sbCxsQssyxxVO = hxzgsb10797Response.getSbCxsQssyxxVO();
//                return BeanCopierUtil.copyList(sbCxsQssyxxVO, clazz);
//            }
//        }
//        else if ("SWZJ.HXZG.SB.QUERYCCSSYXXFORSB".equals(serviceName)){//车船税，核心清册无此接口，此分支弃用
//            final HXZGSB10968Request hxzgsb10968Request = new HXZGSB10968Request();
//            final HXZGSB10968Request.SymxGrid symxGrid = new HXZGSB10968Request.SymxGrid();
//            hxzgsb10968Request.setCxstysbnsrxx(cxstysbNsrxx);
//            hxzgsb10968Request.setSymxGrid(symxGrid);
//            final HXZGSB10968Response hxzgsb10968Response = Gt3Invoker.invoke(serviceName, expand, hxzgsb10968Request, HXZGSB10968Response.class);
//            if(!GyUtils.isNull(hxzgsb10968Response) && !GyUtils.isNull(hxzgsb10968Response.getCcssbsyGrid()) && !GyUtils.isNull(hxzgsb10968Response.getCcssbsyGrid().getCcssbsyGridlb())){
//                final List<CcssbsyGridlbVO> ccssbsyGridlb = hxzgsb10968Response.getCcssbsyGrid().getCcssbsyGridlb();
//                return BeanCopierUtil.copyList(ccssbsyGridlb, clazz);
//            }
//        }
//        else if ("SWZJ.HXZG.SB.QUERYHBSSYXXFORSB".equals(serviceName)){//环保税
//            final HXZGSB10834Request hxzgsb10834Request = new HXZGSB10834Request();
//            hxzgsb10834Request.setCxstysbnsrxx(cxstysbNsrxx);
//            final HXZGSB10834Response hxzgsb10834Response = Gt3Invoker.invoke(serviceName, expand, hxzgsb10834Request, HXZGSB10834Response.class);
//            if(!GyUtils.isNull(hxzgsb10834Response) && !GyUtils.isNull(hxzgsb10834Response.getHbsCjSyxxLb()) && !GyUtils.isNull(hxzgsb10834Response.getHbsCjSyxxLb().getHbsCjSyxxGridLb())){
//                final List<HbsCjSyxx> hbsCjSyxxGridLb = hxzgsb10834Response.getHbsCjSyxxLb().getHbsCjSyxxGridLb();
//                return BeanCopierUtil.copyList(hbsCjSyxxGridLb, clazz);
//            }
//        }
//        else if ("SWZJ.HXZG.SB.QUERYTDZZSSYXXFORSB".equals(serviceName)){//土增
//            if(!YHLX_ZRR.equals(yhlx)){
//                final HXZGSB10754Request hxzgsb10754Request = new HXZGSB10754Request();
//                HXZGSB10754Request.SymxGrid symxGrid = new HXZGSB10754Request.SymxGrid();
//                hxzgsb10754Request.setCxstysbnsrxx(cxstysbNsrxx);
//                hxzgsb10754Request.setSymxGrid(symxGrid);
//                final HXZGSB10754Response hxzgsb10754Response = Gt3Invoker.invoke(serviceName, expand, hxzgsb10754Request, HXZGSB10754Response.class);
//                if(!GyUtils.isNull(hxzgsb10754Response) && !GyUtils.isNull(hxzgsb10754Response.getTdzzssbsyGrid()) && !GyUtils.isNull(hxzgsb10754Response.getTdzzssbsyGrid().getTdzzssbsyGridlb())){
//                    final List<TdzzssbsyGridlbVO> tdzzssbsyGridlb = hxzgsb10754Response.getTdzzssbsyGrid().getTdzzssbsyGridlb();
//                    return BeanCopierUtil.copyList(tdzzssbsyGridlb, clazz);
//                }
//            } else {
//                final TdzzsSbxxReqVO reqVO = new TdzzsSbxxReqVO();
//                final cn.gov.chinatax.dzswj.j3cx.api.cxssb.tdzzs.vo.CxstysbNsrxxVO nsrxxVO = new cn.gov.chinatax.dzswj.j3cx.api.cxssb.tdzzs.vo.CxstysbNsrxxVO();
//                nsrxxVO.setDjxh(cxstysbNsrxx.getDjxh());
//                nsrxxVO.setSbsxDm1("11");
//                if(!GyUtils.isNull(cxstysbNsrxx.getSkssqq())){
//                    nsrxxVO.setSkssqq(cxstysbNsrxx.getSkssqq());
//                }
//                if(!GyUtils.isNull(cxstysbNsrxx.getSkssqz())){
//                    nsrxxVO.setSkssqz(cxstysbNsrxx.getSkssqz());
//                }
//                reqVO.setCxstysbNsrxxVO(nsrxxVO);
//                final ServerResponse<List<cn.gov.chinatax.dzswj.j3cx.api.cxssb.tdzzs.vo.TdzzssbsyGridlbVO>> responseJ3cx = tdzzsJ3cxApi.queryTdzzsSyxxForZrrSb(reqVO);
//                if(!GyUtils.isNull(responseJ3cx) && !GyUtils.isNull(responseJ3cx.getResponse()) && !GyUtils.isNull(responseJ3cx.getResponse().getData())){
//                    final List<cn.gov.chinatax.dzswj.j3cx.api.cxssb.tdzzs.vo.TdzzssbsyGridlbVO> responseDate = responseJ3cx.getResponse().getData();
//                    return BeanCopierUtil.copyList(responseDate, clazz);
//                }
//            }
//        }

        return null;
    }

    /**
     * 过滤税款所属期
     *
     * @param mrskssqq 默认税款所属期起
     * @param mrskssqz 默认税款所属期止
     * @param skssqq   税款所属期起
     * @param skssqz   税款所属期止
     * @return 是否有效
     */
    private boolean filterSyxxBySkssq(Date mrskssqq, Date mrskssqz, Date skssqq, Date skssqz, String nd) {
        //是否为按次税源
        final boolean isAcSy = skssqq.compareTo(skssqz) == 0;
        if (isAcSy) {
            //如果是按次税源直接返回有效
            return true;
        }
        //选择年度情况，此时不是按次税源，直接返回false
        if (!GyUtils.isNull(nd)) {
            return false;
        }
        //默认期限非按次 && 并且止相同
        return mrskssqq.compareTo(mrskssqz) != 0 && mrskssqz.compareTo(skssqz) == 0;
    }

    private void getPhjmzgnew(CchxwsnssbInitResMxVO initResMxVO, String skssqq, String skssqz, NsrzgxxVO nsrzgxxVO,
                              List<NsrbqxxVO> nsrbqxxVOList) {
        if (GyUtils.isNull(nsrzgxxVO)) {
            return;
        }
        final CxsLslfJmxxDTO cxsLslfJmxxDTO = cchxwshbsbService.getLslfJmxx(skssqq, skssqz, nsrzgxxVO, nsrbqxxVOList);

        initResMxVO.setXgmjzzcqssj(cxsLslfJmxxDTO.getXgmjzzcqssj());
        initResMxVO.setXgmjzzczzsj(cxsLslfJmxxDTO.getXgmjzzczzsj());
        initResMxVO.setJzzcsyztDm(cxsLslfJmxxDTO.getJzzcsyztDm());
        initResMxVO.setBqsfsyzzsxgmnsrjzzc(cxsLslfJmxxDTO.getBqsfsyzzsxgmnsrjzzc());
        initResMxVO.setCheckbz(cxsLslfJmxxDTO.getBqsfsyzzsxgmnsrjzzc());
    }

    /**
     * 查询六税两费公共方法
     *
     * @param cxstysbNsrxxVO 纳税人信息
     * @param mxVO           税种信息
     * @param skssqq         税款所属期起
     * @param skssqz         税款所属期止
     */
    @SneakyThrows
    private void getPhjmzg(CxstysbNsrxx cxstysbNsrxxVO, CchxwsnssbInitResMxVO mxVO, String skssqq, String skssqz, String zsxmDm) {
        //核心入参
        final HXZGSB11056Request hxzgsb11056Request = new HXZGSB11056Request();
        hxzgsb11056Request.setDjxh(cxstysbNsrxxVO.getDjxh());
        hxzgsb11056Request.setSkssqq(skssqq);
        hxzgsb11056Request.setSkssqz(skssqz);
        //扩展节点
        final Map<String, String> expand = new HashMap<>();
        expand.put("sjjg", cxstysbNsrxxVO.getZgswskfjDm());
//        final HXZGSB11056Response hxzgsb11056Response = Gt3Invoker.invoke("SWZJ.HXZG.SB.CXLSLFJMZG", expand, hxzgsb11056Request, HXZGSB11056Response.class);
//        final HXZGSB11056Response hxzgsb11056Response = JacksonUtils.toObj(DbUtils.getData("SWZJ.HXZG.SB.CXLSLFJMZG"),HXZGSB11056Response.class);
        final String ywbw = JsonUtils.toJson(hxzgsb11056Request);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CXS_JMZG");
        sjjhDTO.setYwbm(YzpzzlEnum.CXS.getDm());
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setDjxh(cxstysbNsrxxVO.getDjxh());
        sjjhDTO.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
        sjjhDTO.setXzqhszDm(cxstysbNsrxxVO.getXzqhszDm());
        sjjhDTO.setBwnr(ywbw);
        CommonResult<Object> commonResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (commonResult.getCode() == -1) {
            throw ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, "财行税调用乐企接口查询六税两费减免资格失败djxh:{},nsrsbh:{},zsxmDm:{}", cxstysbNsrxxVO.getDjxh(), cxstysbNsrxxVO.getNsrsbh(), zsxmDm);
        }
        final HXZGSB11056Response hxzgsb11056Response = (HXZGSB11056Response) commonResult.getData();
        if (!GyUtils.isNull(hxzgsb11056Response)) {
            //取出结果，小微企业标志如果上半年不为X，则默认为下半年也不为X，如果不对则让纳税人先做企业所得税年报
            final String hasPhjmzg = hxzgsb11056Response.getHasphjmzg();
            final String phjmssqq = hxzgsb11056Response.getPhjmssqq();
            final String phjmssqz = hxzgsb11056Response.getPhjmssqz();
            final String phjmssqmin = hxzgsb11056Response.getPhjmssqmin();
            final String phjmssqmax = hxzgsb11056Response.getPhjmssqmax();
            String bqsfsyzzsxgmnsrjzzc = "";
            String jzzcsyztDm = "";
            String xgmjzzcqssj = "";
            String xgmjzzczzsj = "";
            //六税两费资格，Y:小规模，N：不能享受六税两费，X：季中转一般人，XX：查不到纳税人，GT：个体工商户，XWY：小微企业，XWN：非小微企业
            if ("10110,10112,10111,10107,10118".contains(mxVO.getZsxmdm())) {
                if ("N".equals(hasPhjmzg)) {
                    bqsfsyzzsxgmnsrjzzc = "N";
                    jzzcsyztDm = "";
                    xgmjzzcqssj = "";
                    xgmjzzczzsj = "";
                } else if ("Y".equals(hasPhjmzg)) {
                    bqsfsyzzsxgmnsrjzzc = "Y";
                    jzzcsyztDm = hxzgsb11056Response.getJzzcsyztDm();
                    xgmjzzcqssj = phjmssqq;
                    xgmjzzczzsj = phjmssqz;
                } else if ("GT".equals(hasPhjmzg)) {
                    bqsfsyzzsxgmnsrjzzc = "Y";
                    jzzcsyztDm = hxzgsb11056Response.getJzzcsyztDm();
                    xgmjzzcqssj = phjmssqq;
                    xgmjzzczzsj = phjmssqz;
                } else if ("X".equals(hasPhjmzg)) {
                    //获取跨半年情况
                    final Map<String, String> kbnMap = new HashMap<>();
                    final boolean kbnFlag = this.getKbnFlag(skssqq, skssqz, kbnMap);
                    //获取转换情况  一般人转小规模1，小规模转一般人-1，中间属期0
                    final Map<String, String> sfczMap = new HashMap<>();
                    final String zhqk = this.getZhqk(skssqq, skssqz, phjmssqmin, phjmssqmax, sfczMap);
                    if ("Y".equals(hxzgsb11056Response.getSfxwqy())) {
                        //默认勾选，主体为小微企业
                        bqsfsyzzsxgmnsrjzzc = "Y";
                        jzzcsyztDm = "21";
                        //小微企业，如果跨半年，可能（上半年为X或N，下半年为Y）。或者（上半年为Y，下半年为X或N）
                        String qNew = skssqq;
                        String zNew = skssqz;
                        if (kbnFlag) {
                            if ("1".equals(zhqk)) {
                                //查下半年
                                final String sfxwqyNew = this.getPhjmzgAgain(hxzgsb11056Request, expand, kbnMap.get("xbnDstr"), skssqz, cxstysbNsrxxVO, zsxmDm);
                                if ("Y".equals(sfxwqyNew) || "X".equals(sfxwqyNew)) {
                                    //下半年为Y，则属期内均为小微企业，置为入参属期
                                    qNew = skssqq;
                                    zNew = skssqz;
                                } else {
                                    //下半年为N，则判断最大时间和6月30号关系，小于则置为6月30日，否者使用返回时间
                                    if ("Y".equals(sfczMap.get("isCz"))) {
                                        qNew = skssqq;
                                        zNew = sfczMap.get("sjNew");
                                    } else {
                                        qNew = skssqq;
                                        zNew = phjmssqmax;
                                    }
                                }
                            } else if ("-1".equals(zhqk)) {
                                //查上半年
                                final String sfxwqyNew = this.getPhjmzgAgain(hxzgsb11056Request, expand, skssqq, kbnMap.get("sbnDstr"), cxstysbNsrxxVO, zsxmDm);
                                if ("Y".equals(sfxwqyNew) || "X".equals(sfxwqyNew)) {
                                    //上半年为Y或X，则属期内均为小微企业，置为入参属期
                                    qNew = skssqq;
                                    zNew = skssqz;
                                } else {
                                    //上半年为N，则判断最小时间和7月1号关系，大于则置为7月1日，否者使用返回时间
                                    if ("Y".equals(sfczMap.get("isCz"))) {
                                        qNew = sfczMap.get("sjNew");
                                        zNew = skssqz;
                                    } else {
                                        qNew = phjmssqmin;
                                        zNew = skssqz;
                                    }
                                }
                            } else {
                                //起止都不相同（过于极端，默认逻辑同不跨半年）或都相等（核心处理过，默认逻辑同不跨半年）
                                qNew = skssqq;
                                zNew = skssqz;
                            }
                        } else {
                            //如果入参属期不跨半年，则属期内均为小微企业，置为入参属期，默认勾选，主体为小微企业
                            qNew = skssqq;
                            zNew = skssqz;
                        }
                        //重置时间
                        xgmjzzcqssj = qNew;
                        xgmjzzczzsj = zNew;
                    } else if ("X".equals(hxzgsb11056Response.getSfxwqy())) {
                        //默认放开勾选，主体为小微企业
                        bqsfsyzzsxgmnsrjzzc = "XWN";
                        jzzcsyztDm = "21";
                        //无法判断是否为小微企业，如果跨半年，可能上半年为X，下半年为N，所以需要判断下半年是否为X
                        String qNew = skssqq;
                        String zNew = skssqz;
                        //查下半年
                        if (kbnFlag) {
                            //跨半年
                            final String sfxwqyNew = this.getPhjmzgAgain(hxzgsb11056Request, expand, kbnMap.get("xbnDstr"), skssqz, cxstysbNsrxxVO, zsxmDm);
                            if ("X".equals(sfxwqyNew)) {
                                //下半年为X，则全年无法判断是否为小微企业，此时置为入参属期，放开勾选，主体为小微企业，并置为XWN
                                qNew = skssqq;
                                zNew = skssqz;
                            } else {
                                //下半年为N，则看返回的最大最小时间
                                if ("1".equals(zhqk)) {
                                    //判断最大时间和6月30号关系，小于则置为6月30日，否者使用返回时间
                                    if ("Y".equals(sfczMap.get("isCz"))) {
                                        qNew = skssqq;
                                        zNew = sfczMap.get("sjNew");
                                    } else {
                                        //此时应用小规模性质，下半年不是小微，所以置到小规模时间
                                        bqsfsyzzsxgmnsrjzzc = "Y";
                                        jzzcsyztDm = "11";
                                        qNew = skssqq;
                                        zNew = phjmssqmax;
                                    }
                                } else if ("-1".equals(zhqk)) {
                                    //返回的最大时间和入参止相等，则证明为小规模转一般人
                                    //此时上半年是无法判断，下半年是则置为入参时间
                                    qNew = skssqq;
                                    zNew = skssqz;
                                } else {
                                    //起止都不相同（过于极端，默认逻辑同不跨半年）
                                    qNew = skssqq;
                                    zNew = skssqz;
                                }
                            }
                        } else {
                            //如果入参属期不跨半年，则属期内无法判断是否为小微企业，置为入参属期，放开勾选，主体为小微企业，置为XWN
                            qNew = skssqq;
                            zNew = skssqz;
                        }
                        xgmjzzcqssj = qNew;
                        xgmjzzczzsj = zNew;
                    } else {
                        //小微企业标志为N，则全年为非小微，则取返回最大最小时间，主体为小规模
                        bqsfsyzzsxgmnsrjzzc = "Y";
                        jzzcsyztDm = hxzgsb11056Response.getJzzcsyztDm();
                        xgmjzzcqssj = phjmssqmin;
                        xgmjzzczzsj = phjmssqmax;
                    }
                } else if ("XWY".equals(hasPhjmzg)) {
                    //可能会出现上半年无法判断，下班年小微的情况，此时最小时间是7月1日，需要查上半年时什么情况
                    bqsfsyzzsxgmnsrjzzc = "Y";
                    jzzcsyztDm = hxzgsb11056Response.getJzzcsyztDm();
                    //获取跨半年情况
                    final Map<String, String> kbnMap = new HashMap<>();
                    final boolean kbnFlag = this.getKbnFlag(skssqq, skssqz, kbnMap);
                    if (kbnFlag) {
                        //跨半年，需要判断上半年时什么情况，看是否延长，如果上半年X，延长，为N不延长
                        final Calendar phjmssqminTemp = Calendar.getInstance();
                        phjmssqminTemp.setTime(DateUtils.parseDate(phjmssqmin, "yyyy-MM-dd"));
                        final int phjmssqminMonth = phjmssqminTemp.get(Calendar.MONTH) + 1;
                        if (phjmssqminMonth == 7) {
                            final String sfxwqyNew = this.getPhjmzgAgain(hxzgsb11056Request, expand, skssqq, kbnMap.get("sbnDstr"), cxstysbNsrxxVO, zsxmDm);
                            //取出结果
                            if ("X".equals(sfxwqyNew)) {
                                //如果为X直接扩大范围
                                xgmjzzcqssj = phjmssqq;
                                xgmjzzczzsj = phjmssqz;
                            } else {
                                xgmjzzcqssj = phjmssqmin;
                                xgmjzzczzsj = phjmssqmax;
                            }
                        } else {
                            xgmjzzcqssj = phjmssqmin;
                            xgmjzzczzsj = phjmssqmax;
                        }
                    } else {
                        xgmjzzcqssj = phjmssqmin;
                        xgmjzzczzsj = phjmssqmax;
                    }
                } else if ("XWN".equals(hasPhjmzg)) {
                    if ("N".equals(hxzgsb11056Response.getSfxwqy())) {
                        bqsfsyzzsxgmnsrjzzc = "N";
                    } else {
                        bqsfsyzzsxgmnsrjzzc = "XWN";
                    }
                    jzzcsyztDm = "21";
                    xgmjzzcqssj = phjmssqq;
                    xgmjzzczzsj = phjmssqz;
                } else {
                    bqsfsyzzsxgmnsrjzzc = "N";
                    jzzcsyztDm = "";
                    xgmjzzcqssj = "";
                    xgmjzzczzsj = "";
                }
            }
            mxVO.setCheckbz(bqsfsyzzsxgmnsrjzzc);
            mxVO.setJzzcsyztDm(jzzcsyztDm);
            mxVO.setXgmjzzcqssj(xgmjzzcqssj);
            mxVO.setXgmjzzczzsj(xgmjzzczzsj);
            //2023.07.07 所有六税两费均重新调用，对数据进行处理
            if ("10110,10112,10111,10107,10118".contains(mxVO.getZsxmdm()) &&
                    "XWN".equals(bqsfsyzzsxgmnsrjzzc) && "XWN".equals(mxVO.getOldSwitchPhjmzg())) {
                //可勾选税源则以前台为准，为Y时检验减免时间是否存在
                String tempStr = "";
                if ("XWN".equals(mxVO.getBqsfsyzzsxgmnsrjzzc())) {
                    tempStr = "XWN";
                } else if ("N".equals(mxVO.getBqsfsyzzsxgmnsrjzzc())) {
                    tempStr = "N";
                } else if ("Y".equals(mxVO.getBqsfsyzzsxgmnsrjzzc())) {
                    if (!GyUtils.isNull(mxVO.getXgmjzzcqssj()) && !GyUtils.isNull(mxVO.getXgmjzzczzsj())) {
                        tempStr = "Y";
                    } else {
                        tempStr = "N";
                    }
                } else {
                    tempStr = "N";
                }
                mxVO.setBqsfsyzzsxgmnsrjzzc(tempStr);
            } else {
                mxVO.setBqsfsyzzsxgmnsrjzzc(bqsfsyzzsxgmnsrjzzc);
            }
            //检查用
            mxVO.setOldSwitchPhjmzg(bqsfsyzzsxgmnsrjzzc);
            mxVO.setOldSfxwqy(hxzgsb11056Response.getSfxwqy());
            mxVO.setOldHasPhjmzg(hasPhjmzg);
            mxVO.setOldPhjmssqq(phjmssqq);
            mxVO.setOldPhjmssqz(phjmssqz);
            mxVO.setOldPhjmssqmin(phjmssqmin);
            mxVO.setOldPhjmssqmax(phjmssqmax);
        }
    }

    /**
     * 获取跨半年情况
     *
     * @param skssqq 入参时间
     * @param skssqz 入参时间
     * @param kbnMap 新入参
     * @return 跨半年情况
     */
    @SneakyThrows
    private boolean getKbnFlag(String skssqq, String skssqz, Map<String, String> kbnMap) {
        if (GyUtils.isNull(skssqq) || GyUtils.isNull(skssqz)) {
            return false;
        }
        Calendar calendarskssqqc = Calendar.getInstance();
        calendarskssqqc.setTime(DateUtils.parseDate(skssqq, "yyyy-MM-dd"));
        Calendar calendarskssqzc = Calendar.getInstance();
        calendarskssqzc.setTime(DateUtils.parseDate(skssqz, "yyyy-MM-dd"));
        final int skssqqYear = calendarskssqqc.get(Calendar.YEAR);
        final int skssqzYear = calendarskssqzc.get(Calendar.YEAR);
        final int skssqqMonth = calendarskssqqc.get(Calendar.MONTH) + 1;
        final int skssqzMonth = calendarskssqzc.get(Calendar.MONTH) + 1;
        //跨半年情况
        boolean kbnFlag = false;
        if (skssqqYear == skssqzYear && skssqqMonth <= 6 && skssqzMonth >= 7) {
            kbnFlag = true;
            final String sbnDstr = skssqqYear + "-06-30";
            final String xbnDstr = skssqzYear + "-07-01";
            kbnMap.put("sbnDstr", sbnDstr);
            kbnMap.put("xbnDstr", xbnDstr);
        }
        return kbnFlag;
    }

    /**
     * 获取转换情况
     *
     * @param skssqq     入参属期
     * @param skssqz     入参属期
     * @param phjmssqmin 返回属期
     * @param phjmssqmax 返回属期
     * @param sfczMap    是否重置属期
     * @return 转换情况
     */
    @SneakyThrows
    private String getZhqk(String skssqq, String skssqz, String phjmssqmin, String phjmssqmax, Map<String, String> sfczMap) {
        if (GyUtils.isNull(skssqq) || GyUtils.isNull(skssqz) || GyUtils.isNull(phjmssqmin) || GyUtils.isNull(phjmssqmax)) {
            return "0";
        }
        Calendar phjmssqminC = Calendar.getInstance();
        phjmssqminC.setTime(DateUtils.parseDate(phjmssqmin, "yyyy-MM-dd"));
        Calendar phjmssqmaxC = Calendar.getInstance();
        phjmssqmaxC.setTime(DateUtils.parseDate(phjmssqmax, "yyyy-MM-dd"));
//        final Calendar phjmssqminC = DateUtil.parseDate(phjmssqmin, "yyyy-MM-dd");
//        final Calendar phjmssqmaxC = DateUtil.parseDate(phjmssqmax, "yyyy-MM-dd");
        final int phjmssqminYear = phjmssqminC.get(Calendar.YEAR);
        final int phjmssqmaxYear = phjmssqmaxC.get(Calendar.YEAR);
        final int phjmssqminMonth = phjmssqminC.get(Calendar.MONTH) + 1;
        final int phjmssqmaxMonth = phjmssqmaxC.get(Calendar.MONTH) + 1;
        //判断属期是一般人转小规模1，还是小规模转一般人-1，还是中间属期0，如果全相等证明核心处理了，扩大到入参属期，并为小微企业，和0是一样的
        String zhqk = "";//转换情况
        if (skssqq.equals(phjmssqmin) && !skssqz.equals(phjmssqmax)) {
            zhqk = "1";
            //此时判断最大时间
            if (phjmssqmaxMonth < 6) {
                sfczMap.put("isCz", "Y");
                sfczMap.put("sjNew", phjmssqmaxYear + "-06-30");
            }
        } else if (!skssqq.equals(phjmssqmin) && skssqz.equals(phjmssqmax)) {
            zhqk = "-1";
            //此时判断最小时间
            if (phjmssqminMonth > 7) {
                sfczMap.put("isCz", "Y");
                sfczMap.put("sjNew", phjmssqminYear + "-07-01");
            }
        } else {
            zhqk = "0";
        }
        return zhqk;
    }

    private String getPhjmzgAgain(HXZGSB11056Request hxzgsb11056Request, Map<String, String> expand, String skssqq, String skssqz, CxstysbNsrxx cxstysbNsrxxVO, String zsxmDm) {
        hxzgsb11056Request.setSkssqq(skssqq);
        hxzgsb11056Request.setSkssqz(skssqz);
//        final HXZGSB11056Response responseNew = Gt3Invoker.invoke("SWZJ.HXZG.SB.CXLSLFJMZG", expand, hxzgsb11056Request, HXZGSB11056Response.class);
//        final HXZGSB11056Response responseNew = JacksonUtils.toObj(DbUtils.getData("SWZJ.HXZG.SB.CXLSLFJMZG"),HXZGSB11056Response.class);

        final String ywbw = JsonUtils.toJson(hxzgsb11056Request);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CXS_JMZG");
        sjjhDTO.setYwbm(YzpzzlEnum.CXS.getDm());
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setDjxh(cxstysbNsrxxVO.getDjxh());
        sjjhDTO.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
        sjjhDTO.setXzqhszDm(cxstysbNsrxxVO.getXzqhszDm());
        sjjhDTO.setBwnr(ywbw);
        CommonResult<Object> commonResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (commonResult.getCode() == -1) {
            throw ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, "财行税调用乐企接口查询六税两费减免资格失败djxh:{},nsrsbh:{},zsxmDm:{}", cxstysbNsrxxVO.getDjxh(), cxstysbNsrxxVO.getNsrsbh(), zsxmDm);
        }
        final HXZGSB11056Response responseNew = (HXZGSB11056Response) commonResult.getData();
        if (!GyUtils.isNull(responseNew)) {
            final String sfxwqy = responseNew.getSfxwqy();
            return sfxwqy;
        }
        return "";
    }

    private String cskg() {
        String cskg = "N";
        List<Map<String, Object>> xtcsList = CacheUtils.getTableData("cs_znsb_xtcs");
        if (GyUtils.isNotNull(xtcsList)) {
            for (Map map : xtcsList) {
                if (map.get("csbm").equals("CCHXWSNSSB00001")) {
                    cskg = (String) map.get("csz");
                    break;
                }
            }
        }
        return cskg;
    }

    /**
     * 核心财行税申报初始化接口
     *
     * @param zsxmDm            征收项目
     * @param cxstysbNsrxx      纳税人信息
     * @param symxGridlb        税源明细
     * @param sjjg              数据机关
     * @param sbxxGridlb        申报信息list
     * @param bqjmsemxbywbwList 减免信息list
     * @param cxsSymxReqVO      税源信息ReqVO
     */
    private void initCxstysbHX(String zsxmDm, CxstysbNsrxx cxstysbNsrxx, List<SymxGridlbVO> symxGridlb, String sjjg,
                               List<SbxxGridlbVO> sbxxGridlb, List<Bqjmsemxbywbw> bqjmsemxbywbwList,
                               CxsSymxReqVO cxsSymxReqVO, CchxwsnssbInitResMxVO initResMxVO, CchxwsnssbquerysbbxxReqVO cchxwsnssbquerysbbxxReqVO) {
        final CxsQuerymxInitDTO cxsQuerymxInitDTO = new CxsQuerymxInitDTO();
        this.cxsInitRequsetBW(cxsQuerymxInitDTO, cxstysbNsrxx, symxGridlb, initResMxVO, cchxwsnssbquerysbbxxReqVO);
        log.info("---cxsQuerymxInitDTO------{}",cxsQuerymxInitDTO);
        //使用本地数据测试开关
        HXZGSB10735Response hxzgsb10735Response = null;
        if (YHS.getCode().equals(zsxmDm)) {
            YhsTysbbYtReqDTO yhsTysbbYtReqDTO = BeanUtil.toBean(cxsQuerymxInitDTO, YhsTysbbYtReqDTO.class);
            yhsTysbbYtReqDTO.setSyuuid(cxsQuerymxInitDTO.getSyuuidList());
            final String yskg = CacheUtils.getXtcs("CCHXWSNSSB00001");
            if ("Y".equals(yskg)) {
                hxzgsb10735Response = znsbJyssSsSsjgYhsService.getCxxx1(yhsTysbbYtReqDTO);
            } else {
                hxzgsb10735Response = znsbJyssSsSsjgYhsService.getCxxx(yhsTysbbYtReqDTO);
            }
        } else if (CZTDSYS.getCode().equals(zsxmDm)) {
            hxzgsb10735Response = cztdsysService.cxsInit(cxsQuerymxInitDTO);
        } else if (FCS.getCode().equals(zsxmDm)) {
            hxzgsb10735Response = fcsService.cxsInit(cxsQuerymxInitDTO);
        } else if (YYS.getCode().equals(zsxmDm)) {
            hxzgsb10735Response = yyssycjService.querymxInitData(cxsQuerymxInitDTO);
        }
        if (GyUtils.isNotNull(hxzgsb10735Response)) {
            if (!GyUtils.isNull(hxzgsb10735Response)) {
                if (!GyUtils.isNull(hxzgsb10735Response.getSbxxGrid()) && !GyUtils.isNull(hxzgsb10735Response.getSbxxGrid().getSbxxGridlb())) {
                    sbxxGridlb.addAll(hxzgsb10735Response.getSbxxGrid().getSbxxGridlb());
                    if (GyConstants.getDmGyZsxmFcs().equals(zsxmDm) || GyConstants.getDmGyZsxmCztdsys().equals(zsxmDm)) {
                        final List<CxsSySbxxVO> cxsSySbxxVOS = new ArrayList<>();
                        if (GyConstants.getDmGyZsxmCztdsys().equals(zsxmDm) && !GyUtils.isNull(hxzgsb10735Response.getTdsyxxGrid()) && !GyUtils.isNull(hxzgsb10735Response.getTdsyxxGrid().getTdsyxxGridlb())) {
                            final Map<String, List<SBCxsTdsyxxcjbVO>> tdsyMap = hxzgsb10735Response.getTdsyxxGrid().getTdsyxxGridlb().stream().filter(e -> GyUtils.isNotNull(e.getSyuuid())).collect(Collectors.groupingBy(SBCxsTdsyxxcjbVO::getSyuuid));
                            final List<CxsSySbxxVO> collect = hxzgsb10735Response.getSbxxGrid().getSbxxGridlb().stream().map(m -> {
                                final CxsSySbxxVO copy = BeanUtil.copyProperties(m, CxsSySbxxVO.class);
                                if (!GyUtils.isNull(tdsyMap.get(copy.getSyuuid()))) {
                                    copy.setSybh(tdsyMap.get(copy.getSyuuid()).get(0).getTdsybh());
                                }
                                return copy;
                            }).collect(Collectors.toList());
                            cxsSySbxxVOS.addAll(collect);
                        } else {
                            cxsSySbxxVOS.addAll(BeanUtil.copyToList(hxzgsb10735Response.getSbxxGrid().getSbxxGridlb(), CxsSySbxxVO.class));
                        }
                        cxsSymxReqVO.setCxsSySbxxVOList(cxsSySbxxVOS);
                    }
                }

                // 处理减免信息
                this.processJmxxGrid(hxzgsb10735Response, cxstysbNsrxx, bqjmsemxbywbwList);

                //补充税源明细信息
                if (!GyUtils.isNull(hxzgsb10735Response.getDqyjskxxGrid()) && !GyUtils.isNull(hxzgsb10735Response.getDqyjskxxGrid().getDqyjskxxGridlb())) {
                    cxsSymxReqVO.setCxsSySkxxVOList(BeanUtil.copyToList(hxzgsb10735Response.getDqyjskxxGrid().getDqyjskxxGridlb(), CxsSySkxxVO.class));
                }
                if (!GyUtils.isNull(hxzgsb10735Response.getDqcjjzfcsyjskxxGrid()) && !GyUtils.isNull(hxzgsb10735Response.getDqcjjzfcsyjskxxGrid().getDqcjjzfcsyjskxxGridlb())) {
                    cxsSymxReqVO.setCxsSyCjxxVOList(BeanUtil.copyToList(hxzgsb10735Response.getDqcjjzfcsyjskxxGrid().getDqcjjzfcsyjskxxGridlb(), CxsSyFcsskVO.class));
                }
                if (!GyUtils.isNull(hxzgsb10735Response.getDqczjzfcsyjskxxGrid()) && !GyUtils.isNull(hxzgsb10735Response.getDqczjzfcsyjskxxGrid().getDqczjzfcsyjskxxGridlb())) {
                    cxsSymxReqVO.setCxsSyCzxxVOList(BeanUtil.copyToList(hxzgsb10735Response.getDqczjzfcsyjskxxGrid().getDqczjzfcsyjskxxGridlb(), CxsSyFcsskVO.class));
                }
            }
        }
    }

    public void processJmxxGrid(HXZGSB10735Response hxzgsb10735Response, CxstysbNsrxx cxstysbNsrxx, List<Bqjmsemxbywbw> bqjmsemxbywbwList) {
        Optional.ofNullable(hxzgsb10735Response.getJmxxGrid())
                .map(HXZGSB10735Response.JmxxGrid::getJmxxGridlb)
                .ifPresent(gridList -> {
                    gridList.forEach(bqjmsemxbywbw -> processBqjmsemxbywbw(bqjmsemxbywbw, cxstysbNsrxx));
                    bqjmsemxbywbwList.addAll(gridList);
                });
    }

    private void processBqjmsemxbywbw(Bqjmsemxbywbw bqjmsemxbywbw, CxstysbNsrxx cxstysbNsrxx) {
        Optional.ofNullable(bqjmsemxbywbw)
                .map(Bqjmsemxbywbw::getBqjmsemxb)
                .map(Bqjmsemxbywbw.Bqjmsemxb::getBqjmsemxbGridlb)
                .ifPresent(gridlb -> gridlb.forEach(bqjmsemxbGridlbVO -> updateSsjmxzDm(bqjmsemxbGridlbVO, cxstysbNsrxx)));
    }

    private void updateSsjmxzDm(BqjmsemxbGridlbVO bqjmsemxbGridlbVO, CxstysbNsrxx cxstysbNsrxx) {
        if (GyUtils.isNull(bqjmsemxbGridlbVO.getSsjmxzDm()) && "Y".equals(cxstysbNsrxx.getBqsfsyzzsxgmnsrjzzc())) {
            String jzzcsyztDm = cxstysbNsrxx.getJzzcsyztDm();
            switch (jzzcsyztDm) {
                case "11":
                    bqjmsemxbGridlbVO.setSsjmxzDm("0009049901"); // 小规模
                    break;
                case "21":
                    bqjmsemxbGridlbVO.setSsjmxzDm("0009049903"); // 小微企业
                    break;
                case "22":
                    bqjmsemxbGridlbVO.setSsjmxzDm("0009049902"); // 个体工商户
                    break;
            }
            bqjmsemxbGridlbVO.setSsjmxzmc(FtsUtils.getMcByDm("dm_gy_ssjmxz","ssjmxzmc",bqjmsemxbGridlbVO.getSsjmxzDm()));
        }
    }


    /**
     * 获取统一耕占未批先占税源List
     *
     * @param symxListNsr 返回暂存listNsr
     * @param symxListZrr 返回暂存listZrr
     * @param yhlx        用户类型
     * @return 返回值
     */
    private List<Map<String, Object>> getGdzysWpxzSyList(List<SymxVO> symxListNsr, List<ZrrSymxVO> symxListZrr, String yhlx) {
        final List<Map<String, Object>> symxListAll = new ArrayList<>();
        if (!YHLX_ZRR.equals(yhlx)) {
            if (!GyUtils.isNull(symxListNsr)) {
                final List<Map<String, Object>> rtmList = symxListNsr.stream().map(m -> {
                    final Map<String, Object> rtm = new HashMap<>();
                    rtm.put("zdfsDm", m.getZdfsDm());
                    rtm.put("syuuid", m.getSyuuid());
                    return rtm;
                }).collect(Collectors.toList());
                symxListAll.addAll(rtmList);
            }
        } else {
            if (!GyUtils.isNull(symxListZrr)) {
                final List<Map<String, Object>> rtmList = symxListZrr.stream().map(m -> {
                    final Map<String, Object> rtm = new HashMap<>();
                    rtm.put("zdfsDm", m.getZdfsDm());
                    rtm.put("syuuid", m.getSyuuid());
                    return rtm;
                }).collect(Collectors.toList());
                symxListAll.addAll(rtmList);
            }
        }
        return symxListAll;
    }

    /**
     * 处理耕地占用税未批先占普惠减免情况
     *
     * @param symxListAll 返回暂存list
     * @param symxGridlb  原list
     * @param wpxzList    未批先占List
     * @param otherList   其余List
     * @return 返回值
     */
    private String clGdzysWpxz(List<Map<String, Object>> symxListAll, List<SymxGridlbVO> symxGridlb, List<SymxGridlbVO> wpxzList, List<SymxGridlbVO> otherList) {
        //由于各省市对耕地占用税中，若小微企业存在占地方式属于“未批先占”的情况，能否享受普惠减免的政策规定不一致，因此需要兼顾多种情况。
        //小微企业存在占地方式属于“未批先占”的情况，由各省自行配置，
        //（1）若允许享受，则勾选“普惠减免”，不可修改；
        //（2）若不允许享受，当只有“未批先占”税源，则不勾选“普惠减免”，不可修改；
        // 当既有“未批先占”税源，又有其他方式税源，则勾选“普惠减免”，不可修改，
        // 后台区分税源申报，“未批先占”不享受普惠减免，其他占地方式可以享受。
        if (!GyUtils.isNull(symxGridlb)) {
            if (!GyUtils.isNull(symxListAll)) {
                final Map<String, List<Map<String, Object>>> gdzysGroup = symxListAll.stream().filter(f -> !GyUtils.isNull(f.get("zdfsDm")) && f.get("zdfsDm").toString().contains("06"))
                        .collect(Collectors.groupingBy(g -> (String) g.get("syuuid")));
                for (SymxGridlbVO temoVO : symxGridlb) {
                    final List<Map<String, Object>> symxVOS = gdzysGroup.get(temoVO.getSyuuid());
                    if (!GyUtils.isNull(symxVOS)) {
                        wpxzList.add(temoVO);
                    } else {
                        otherList.add(temoVO);
                    }
                }
            } else {
                otherList.addAll(symxGridlb);
            }
            if (wpxzList.size() == symxGridlb.size()) {
                //费耕地占用税六税两默认值
                final String gdzysLslfMrz = "N";
                return gdzysLslfMrz;
            }
        }
        return "";
    }

    /**
     * 获取不享受六税两费VO
     *
     * @param cxstysbNsrxx 纳税人信息VO
     * @return 返回值
     */
    private CxstysbNsrxx getBxsNsrVO(CxstysbNsrxx cxstysbNsrxx) {
        final CxstysbNsrxx bxsCopy = BeanUtil.copyProperties(cxstysbNsrxx, CxstysbNsrxx.class);
        bxsCopy.setBqsfsyzzsxgmnsrjzzc("N");
        bxsCopy.setJzzcsyztDm("");
        bxsCopy.setXgmjzzcqssj("");
        bxsCopy.setXgmjzzczzsj("");
        return bxsCopy;
    }

    /**
     * 核心财行税申报初始化接口异常信息处理
     *
     * @param e               异常
     * @param zsxmDm          征收项目代码
     * @param cchxwsnssbbxxVO 返回值
     */
    private void getInitCxstysbError(Exception e, String zsxmDm, CchxwsnssbbxxVO cchxwsnssbbxxVO) {
        //获取申报表名称
        final CxsSbbEnum sbbEnum = CxsSbbEnum.getCxsSbbEnumByZsxm(zsxmDm);
        String sbbmc = zsxmDm;
        if (sbbEnum != null) {
            sbbmc = sbbEnum.sbbmc;
        }
        log.info("InitCxstysbError" + zsxmDm + FtsCxsUtils.getTrace(e));
        final String message = e.getMessage();
        cchxwsnssbbxxVO.setReturnCode("-9");
        if (!GyUtils.isNull(message)) {
            if (message.contains("1010010097000045") && message.contains("本期减免税额不能大于本期应纳税额！")) {
                //资源税
                cchxwsnssbbxxVO.setReturnMsg(sbbmc + "：本期减免税额不能大于本期应纳税额！");
            } else if (message.contains("1010010097000045") && message.contains("维护征收项目")) {
                //环保税
                cchxwsnssbbxxVO.setReturnMsg(sbbmc + "：需要通过参数表:CS_SB_FTC_MRQXGZ维护征收项目:" + sbbmc + "的默认期限规则,请核对");
            } else if (message.contains("1010010097000045") && message.contains("纳税人该属期已申报烟叶税，如需更正需通过申报错误更正业务处理")) {
                //烟叶税
                cchxwsnssbbxxVO.setReturnMsg(sbbmc + "：纳税人该属期已申报烟叶税，如需更正需通过申报错误更正业务处理。");
            } else if (message.contains("1010330006000004") && message.contains("当前纳税人申报属期内不存在有符合条件的税（费）种认定信息，请通过税（费）种认定功能进行查阅")) {
                //土地增值税
                cchxwsnssbbxxVO.setReturnMsg(sbbmc + "：当前纳税人申报属期内土地增值税不存在有符合条件的税（费）种认定信息，请通过税（费）种认定功能进行查阅");
            } else {
                cchxwsnssbbxxVO.setReturnMsg(message);
            }
        } else {
            cchxwsnssbbxxVO.setReturnMsg("查询失败，请稍候再试");
        }
    }

//    /**
//     * 增加税源编号
//     * @param hbsSyxxBaseSort 环保税排序基础信息
//     * @param symxVOList_save 应申报syuuid明细
//     * @param sbxxGridlb 申报表明细List
//     * @param cxstysbNsrxx 纳税人信息
//     * @param zgswjgdm 机关
//     * @param yhlx 用户类型
//     */
//    private void addSybhZbbmxByHbs(List<HbsCjSyxx> hbsSyxxBaseSort, List<SymxVO> symxVOList_save,
//                                   List<SbxxGridlbVO> sbxxGridlb,
//                                   CxstysbNsrxx cxstysbNsrxx, String zgswjgdm, String yhlx) {
//        //过滤后的税源明细信息
//        final List<HbsCjSyxx> filterSyxx = new ArrayList<>();
//        if(GyUtils.isNull(hbsSyxxBaseSort)){
//            //如果hbsSyxxBaseSort为空，证明为勾选税源或者更正，此时需要重新调用forsb接口获取税源信息
//            final List<String> syuuidList = symxVOList_save.stream().map(SymxVO::getSyuuid).filter(syuuid -> !GyUtils.isNull(syuuid)).collect(Collectors.toList());
//            final List<HbsCjSyxx> hbsCjSyxxGridLb = this.queryForSb("SWZJ.HXZG.SB.QUERYHBSSYXXFORSB", cxstysbNsrxx, zgswjgdm, yhlx, HbsCjSyxx.class);
//            if(!GyUtils.isNull(hbsCjSyxxGridLb)){
//                final List<HbsCjSyxx> filterSyxxTemp = hbsCjSyxxGridLb.stream().filter(f -> !GyUtils.isNull(f.getSyuuid()) && !GyUtils.isNull(f.getHgbhssybh()) && syuuidList.contains(f.getSyuuid())).collect(Collectors.toList());
//                filterSyxx.addAll(filterSyxxTemp);
//            }
//        } else {
//            final List<HbsCjSyxx> filterSyxxTemp = hbsSyxxBaseSort.stream().filter(f -> !GyUtils.isNull(f.getSyuuid()) && !GyUtils.isNull(f.getHgbhssybh())).collect(Collectors.toList());
//            filterSyxx.addAll(filterSyxxTemp);
//        }
//        if(!GyUtils.isNull(filterSyxx)){
//            final Map<String, HbsCjSyxx> syuuidMap = filterSyxx.stream().collect(Collectors.toMap(HbsCjSyxx::getSyuuid, Function.identity(), (key1, key2) -> key1));
//            //同一排放口编号指税源编号相同 —— forsb接口 - hgbhssybh
//            for(SbxxGridlbVO tempVO : sbxxGridlb){
//                final String syuuid = tempVO.getSyuuid();
//                if(!GyUtils.isNull(syuuid) && !GyUtils.isNull(syuuidMap.get(syuuid))){
//                    tempVO.setSybh(syuuidMap.get(syuuid).getHgbhssybh());
//                }
//            }
//        }
//    }

    /**
     * 处理主表明细list
     *
     * @param hzzbList 待处理list
     * @return 返回值
     */
    private List<CchxwsnssbzbmxVO> dealWithZbmxList(List<SbxxGridlbVO> hzzbList) {
        final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList = new ArrayList<>();
        if (!GyUtils.isNull(hzzbList)) {
            for (SbxxGridlbVO sbCxssybtxxVO : hzzbList) {
                final CchxwsnssbzbmxVO cchxwsnssbzbmxVO = new CchxwsnssbzbmxVO();
                cchxwsnssbzbmxVO.setZsxmDm(sbCxssybtxxVO.getZsxmDm());
                //纳税人烟叶税分支
                /*if(!YHLX_ZRR.equals(yhlx) && sbCxssybtxxVO.getZsxmDm().equals(GyConstants.getDmGyZsxmYys()) && GyUtils.isNull(sbCxssybtxxVO.getZspmDm())){
                    final CxSfzrdxxReqVO cxSfzrdxxReqVO = new CxSfzrdxxReqVO();
                    cxSfzrdxxReqVO.setDjxh(djxh);
                    cxSfzrdxxReqVO.setZsxmDm(sbCxssybtxxVO.getZsxmDm());
                    cxSfzrdxxReqVO.setSkssqq(sbCxssybtxxVO.getSkssqq());
                    cxSfzrdxxReqVO.setSkssqz(sbCxssybtxxVO.getSkssqz());
                    final ServerResponse<CxSfzrdxxResVO> sfzrdxxResVO = nsrxxcxApi.querySfzrdxxBySql(cxSfzrdxxReqVO);
                    final List<SfzrdxxVO> sfzrdxxList = sfzrdxxResVO.getResponse().getData().getSfzrdxxList();
                    cchxwsnssbzbmxVO.setZspmDm(!GyUtils.isNull(sfzrdxxList) ? sfzrdxxList.get(0).getZspmDm() : "101200100");
                }else{
                    cchxwsnssbzbmxVO.setZspmDm(sbCxssybtxxVO.getZspmDm());
                }*/
                cchxwsnssbzbmxVO.setZspmDm(sbCxssybtxxVO.getZspmDm());
                cchxwsnssbzbmxVO.setZszmDm(sbCxssybtxxVO.getZszmDm());
                cchxwsnssbzbmxVO.setSkssqq(sbCxssybtxxVO.getSkssqq());
                cchxwsnssbzbmxVO.setSkssqz(sbCxssybtxxVO.getSkssqz());
                cchxwsnssbzbmxVO.setJsyj(CchxwsnssbGyUtils.formatDouble(sbCxssybtxxVO.getJsyj()));
                cchxwsnssbzbmxVO.setSl1(sbCxssybtxxVO.getSl1());
                cchxwsnssbzbmxVO.setYnse(sbCxssybtxxVO.getYnse());
                cchxwsnssbzbmxVO.setJmse(sbCxssybtxxVO.getJmse());
                cchxwsnssbzbmxVO.setYjse(sbCxssybtxxVO.getYjse());
                cchxwsnssbzbmxVO.setYbtse(sbCxssybtxxVO.getYbtse());
                cchxwsnssbzbmxVO.setSyuuid(sbCxssybtxxVO.getSyuuid());
                cchxwsnssbzbmxVO.setYzpzzlDm(sbCxssybtxxVO.getYzpzzlDm());
                cchxwsnssbzbmxVO.setYqsbbz(sbCxssybtxxVO.getYqsbbz());
                cchxwsnssbzbmxVO.setYqts(sbCxssybtxxVO.getYqts());
                cchxwsnssbzbmxVO.setSybh(sbCxssybtxxVO.getSybh());
                cchxwsnssbzbmxVOList.add(cchxwsnssbzbmxVO);
            }
        }
        return cchxwsnssbzbmxVOList;
    }

    /**
     * 汇总附表减免信息
     *
     * @param bqjmsemxbywbwList 入参
     * @return 返回值
     */
    private List<BqjmsemxbGridlbVO> hzFbJmxx(List<Bqjmsemxbywbw> bqjmsemxbywbwList) {
        final List<BqjmsemxbGridlbVO> hzFbJmxxList = new ArrayList<>();
        if (GyUtils.isNull(bqjmsemxbywbwList)){
            return hzFbJmxxList;
        }
        for (Bqjmsemxbywbw bqjmsemxbywbw : bqjmsemxbywbwList) {
            final String zsxm_dm = bqjmsemxbywbw.getZsxmDm();
            //减免信息分组map
            final Map<String, BqjmsemxbGridlbVO> jmhzMap = new HashMap<>();
            final List<BqjmsemxbGridlbVO> jmmx = bqjmsemxbywbw.getBqjmsemxb().getBqjmsemxbGridlb();
            for (BqjmsemxbGridlbVO vo : jmmx) {
                //获取分组信息
                final String sybh = vo.getSybh();
                final String zspmDm = vo.getZspmDm();
                final String zszmDm = vo.getZszmDm();
                final String wrwlbDm = vo.getZywrwlbDm();
                final String skssqq = vo.getSkssqq();
                final String skssqz = vo.getSkssqz();
                final String ssjmxzDm = vo.getSsjmxzDm();
                final Double jmse = vo.getJmse();
                String key = skssqq + skssqz + ssjmxzDm;
                if ("10121".equals(zsxm_dm)) {
                    key = key + sybh + wrwlbDm + zspmDm;
                } else if ("10107".equals(zsxm_dm)) {
                    key = key + zspmDm + zszmDm;
                } else if ("10111".equals(zsxm_dm)) {
                    key = key + zspmDm;
                }
//                else if ("10110".equals(zsxm_dm)) {
//                    continue;
//                }
                else {
                    key = key + sybh;
                }
                //汇总减免金额
                BqjmsemxbGridlbVO jmvo = jmhzMap.get(key);
                if (jmvo == null) {
                    jmvo = new BqjmsemxbGridlbVO();
                    BeanUtil.copyProperties(vo, jmvo);
                } else {
                    jmvo.setJmse(CchxwsnssbGyUtils.addHj(jmvo.getJmse(), jmse));
                }
                jmhzMap.put(key, jmvo);
            }
            final List<BqjmsemxbGridlbVO> jmmxList = new ArrayList<>(jmhzMap.values());
            hzFbJmxxList.addAll(jmmxList);
        }
        return hzFbJmxxList;
    }

    /**
     * 处理附表明细list
     *
     * @param hzFbJmxxList 待处理list
     * @return 返回值
     */
    @SneakyThrows
    private List<CchxwsnssbfbmxVO> dealWithFbmxList(List<BqjmsemxbGridlbVO> hzFbJmxxList) {
        final List<CchxwsnssbfbmxVO> cchxwsnssbfbmxVOList = new ArrayList<>();
        if (GyUtils.isNull(hzFbJmxxList)){
            return cchxwsnssbfbmxVOList;
        }
        //处理汇总减免明细VOList
        for (BqjmsemxbGridlbVO jm : hzFbJmxxList) {
            final CchxwsnssbfbmxVO cchxwsnssbfbmxVO = new CchxwsnssbfbmxVO();
            cchxwsnssbfbmxVO.setZsxmDm(jm.getZsxmDm());
            cchxwsnssbfbmxVO.setZspmDm(jm.getZspmDm());
            cchxwsnssbfbmxVO.setZszmDm(jm.getZszmDm());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date jmskssqq = sdf.parse(jm.getSkssqq());
            Date jmskssqz = sdf.parse(jm.getSkssqz());
            cchxwsnssbfbmxVO.setSkssqq(DateUtil.format(jmskssqq, "yyyy-MM-dd"));
            cchxwsnssbfbmxVO.setZywrwlbdm(jm.getZywrwlbDm());
            cchxwsnssbfbmxVO.setSkssqz(DateUtil.format(jmskssqz, "yyyy-MM-dd"));
            cchxwsnssbfbmxVO.setSsjmxzDm(jm.getSsjmxzDm());
            cchxwsnssbfbmxVO.setSwsxDm(jm.getSwsxDm());
            cchxwsnssbfbmxVO.setJmse(CchxwsnssbGyUtils.formatDouble(jm.getJmse()));
            cchxwsnssbfbmxVO.setSybh(jm.getSybh());
            cchxwsnssbfbmxVO.setUuid(jm.getUuid());
            cchxwsnssbfbmxVOList.add(cchxwsnssbfbmxVO);
        }
        return cchxwsnssbfbmxVOList;
    }

    /**
     * 处理主附表明细
     * 请按需求实现大气/水污染物系统自动判断前三或前五的污染物生成申报表和减免表：核心FORSB接口已实现
     * 一、同一排放口编号下的大气污染物，按照污染当量数从大到小排序,对前三项污染物征收环境保护税（减免税同步判断），生成最终申报表。
     * 二、同一排放口编号下的应税水污染物，区分第一类水污染物和其他类水污染物，按照污染当量数从大到小排序，
     * 对第一类水污染物按照前五项征收环境保护税，对其他类水污染物按照前三项征收环境保护税（减免税同步判断），生成最终申报表。
     *
     * @param zbList 申报表明细List
     * @param fbList 附表明细List
     */
    private void sortZbbmxByHbs(List<CchxwsnssbzbmxVO> zbList, List<CchxwsnssbfbmxVO> fbList) {
        //同一排放口编号指税源编号相同 —— forsb接口 - hgbhssybh
        //污染当量数wrdls —— init接口 - jsyj
        //水污染物种类代码 zywrwlbDm W水污染-A大气
        Map<String, Integer> sortMap = new HashMap<>();
        if (!GyUtils.isNull(zbList)) {
            //对税源分组
            final List<CchxwsnssbzbmxVO> acZbList = new ArrayList<>();//按次
            final List<CchxwsnssbzbmxVO> ajZbList = new ArrayList<>();//按季
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (CchxwsnssbzbmxVO tempVO : zbList) {
                final String sybh = tempVO.getSybh();
                final String zspmDm = tempVO.getZspmDm();
                final String skssqq = sdf.format(tempVO.getSkssqq());
                final String skssqz = sdf.format(tempVO.getSkssqz());
                tempVO.setSkssqq(skssqq);
                tempVO.setSkssqz(skssqz);
                //环保税为按季按次申报，所以有两类属期，分两类(无税源编号、无征收品目的都放一起)
                tempVO.setSortInt(9);
                if ((!GyUtils.isNull(skssqq) && skssqq.equals(skssqz)) || GyUtils.isNull(zspmDm) || GyUtils.isNull(sybh)) {
                    acZbList.add(tempVO);
                } else {
                    if (zspmDm.startsWith("101211")) {
                        //101211***大气污染物
                        tempVO.setSortInt(10);
                    } else if (zspmDm.startsWith("101212")) {
                        //101212***水污染物
                        if (zspmDm.startsWith("1012121")) {
                            //1012121**第一类水污染物
                            tempVO.setSortInt(21);
                        } else {
                            //1012122**,1012123**,1012124**,1012129**
                            tempVO.setSortInt(29);
                        }
                    } else {
                        tempVO.setSortInt(99);
                    }
                    ajZbList.add(tempVO);
                }
            }
            //清空
            zbList.clear();
            //因为上一步已经排过序，此时只需要重排大气水
            if (!GyUtils.isNull(ajZbList)) {
                //1.税源编号(同一排放口编号)，2.大气、水一类、水其他、其他，3.污染当量数(计税依据)，此时税源编号、品目不为空，当量数需要判空
                final List<CchxwsnssbzbmxVO> sortListTemp = ajZbList.stream()
                        .sorted(Comparator.comparing(CchxwsnssbzbmxVO::getSybh, Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(CchxwsnssbzbmxVO::getSortInt, Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(CchxwsnssbzbmxVO::getJsyj, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                for (int i = 0; i < sortListTemp.size(); i++) {
                    sortListTemp.get(i).setSortInt(i + 1);
                }
                sortMap = sortListTemp.stream().collect(Collectors.toMap(o -> o.getSybh() + o.getZspmDm(), CchxwsnssbzbmxVO::getSortInt, (k1, k2) -> k1));
                zbList.addAll(sortListTemp);
            }
            //放入按次
            zbList.addAll(acZbList);
        }

        if (!GyUtils.isNull(fbList) && !GyUtils.isNull(sortMap)) {
            //对税源分组
            final List<CchxwsnssbfbmxVO> acFbList = new ArrayList<>();//按次
            final List<CchxwsnssbfbmxVO> ajFbList = new ArrayList<>();//按季
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (CchxwsnssbfbmxVO tempVO : fbList) {
                final String sybh = tempVO.getSybh();
                final String zspmDm = tempVO.getZspmDm();
                final String skssqq = sdf.format(tempVO.getSkssqq());
                final String skssqz = sdf.format(tempVO.getSkssqz());
                tempVO.setSkssqq(skssqq);
                tempVO.setSkssqz(skssqz);
                //环保税为按季按次申报，所以有两类属期，分两类(无税源编号、无征收品目的都放一起)
                if ((!GyUtils.isNull(skssqq) && skssqq.equals(skssqz)) || GyUtils.isNull(zspmDm) || GyUtils.isNull(sybh)) {
                    acFbList.add(tempVO);
                } else {
                    ajFbList.add(tempVO);
                }
            }
            //清空
            fbList.clear();
            //附表暂时只排大气水，减免是按月的，需要按月倒序
            if (!GyUtils.isNull(ajFbList)) {
                for (CchxwsnssbfbmxVO sortTemp : ajFbList) {
                    final String fbKey = sortTemp.getSybh() + sortTemp.getZspmDm();
                    sortTemp.setSortInt(0);
                    if (!GyUtils.isNull(sortMap.get(fbKey))) {
                        sortTemp.setSortInt(sortMap.get(fbKey));
                    }
                }
                final List<CchxwsnssbfbmxVO> sortListTemp = ajFbList.stream()
                        .sorted(Comparator.comparing(CchxwsnssbfbmxVO::getSortInt, Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(CchxwsnssbfbmxVO::getSkssqz, Comparator.nullsLast(Comparator.naturalOrder())))
                        .collect(Collectors.toList());
                fbList.addAll(sortListTemp);
            }
            //放入按次
            fbList.addAll(acFbList);
        }
    }

    /**
     * 处理卡片税款信息VOList
     *
     * @param cchxwsnssbzbmxVOList 待处理list
     * @param zsxmDm               征收项目代码
     * @param clbz                 处理标志
     * @return 返回值
     */
    private List<CchxwsnssbKpVO> dealWithKpList(List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList, String zsxmDm, String clbz) {
        final List<CchxwsnssbKpVO> kpVOList = new ArrayList<>();
        //计算最大最小所属期使用
        final List<String> list_skssqq = new ArrayList<>();
        final List<String> list_skssqz = new ArrayList<>();
        //处理后的数据
        final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOS = new ArrayList<>();
        if ("Y".equals(clbz)) {
            //这个过滤感觉也没什么用，都是单税源调用
            final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOSTemp = cchxwsnssbzbmxVOList.stream().filter(x -> x.getZsxmDm().equals(zsxmDm)).collect(Collectors.toList());
            if (zsxmDm.equals(GyConstants.getDmGyZsxmZys())) {
                //资源税分支，再次分组后合并，但是感觉没啥用，反正都是循环计算
                cchxwsnssbzbmxVOSTemp.parallelStream().collect(Collectors.groupingBy(x -> x.getZsxmDm() + "#" + x.getSkssqq() + "#" + x.getSkssqz(), Collectors.toList()))
                        .forEach((zsxm, zbmxvo) -> {
                            zbmxvo.stream().reduce((a, b) -> new CchxwsnssbzbmxVO(a.getZsxmDm(), a.getSkssqq(), a.getSkssqz(), a.getYnse() + b.getYnse(), a.getJmse() + b.getJmse(), a.getYbtse() + b.getYbtse())).ifPresent(cchxwsnssbzbmxVOS::add);
                        });
            } else {
                cchxwsnssbzbmxVOS.addAll(cchxwsnssbzbmxVOSTemp);
            }
        } else {
            cchxwsnssbzbmxVOS.addAll(cchxwsnssbzbmxVOList);
        }

        //计算总合并金额
        BigDecimal total_ybtse = BigDecimal.ZERO;
        BigDecimal total_ynse = BigDecimal.ZERO;
        BigDecimal total_jmse = BigDecimal.ZERO;
        final CchxwsnssbKpVO cchxwsnssbKpVO = new CchxwsnssbKpVO();
        for (CchxwsnssbzbmxVO cxs : cchxwsnssbzbmxVOS) {
            total_ybtse = total_ybtse.add(BigDecimal.valueOf(cxs.getYbtse()));
            total_ynse = total_ynse.add(BigDecimal.valueOf(cxs.getYnse()));
            total_jmse = total_jmse.add(BigDecimal.valueOf(cxs.getJmse()));
            cchxwsnssbKpVO.setZsxmDm(cxs.getZsxmDm());
            cchxwsnssbKpVO.setYbtseHj(total_ybtse);
            cchxwsnssbKpVO.setYnseHj(total_ynse);
            cchxwsnssbKpVO.setJmseHj(total_jmse);
            list_skssqq.add(cxs.getSkssqq());
            list_skssqz.add(cxs.getSkssqz());
        }
        //计算最大最小所属期
        final Date skssqq = CchxwsnssbGyUtils.minmaxDate(list_skssqq, "min");
        final Date skssqz = CchxwsnssbGyUtils.minmaxDate(list_skssqz, "max");
        if (!GyUtils.isNull(skssqq) && !GyUtils.isNull(skssqz)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            cchxwsnssbKpVO.setSkssqq(sdf.format(skssqq));
            cchxwsnssbKpVO.setSkssqz(sdf.format(skssqz));
            kpVOList.add(cchxwsnssbKpVO);
        }
        return kpVOList;
    }

    /**
     * 组装VO信息
     *
     * @param mxVOList     mxVOList
     * @param gdzysWpxzCsz 参数值
     * @param gdzysLslfMrz 默认值
     */
    private void clGzMxVO(List<CchxwsnssbInitResMxVO> mxVOList, String gdzysWpxzCsz, String gdzysLslfMrz) {
        if (!GyUtils.isNull(mxVOList)) {
            final String gdzysWpxzCszFinal = gdzysWpxzCsz;
            final String gdzysLslfMrzFinal = gdzysLslfMrz;
            mxVOList.stream().filter(f -> "10118".equals(f.getZsxmdm())).forEach(p -> {
                p.setGdzysWpxzCsz(gdzysWpxzCszFinal);
                p.setGdzysLslfMrz(gdzysLslfMrzFinal);
            });
        }
    }

    @SneakyThrows
    private ZnsbNssbSbrwDO acSbrwInsert(String sbrwuuid, CxstysbNsrxx cxstysbNsrxx, List<CchxwsnssbzbmxVO> nssbzbmxlist, String xzqhszdm, String zsxmDm, CchxwsnssbInitResMxVO initresmxvo) {
        ZnsbNssbSbrwDO znsbNssbSbrwDO = new ZnsbNssbSbrwDO();
        znsbNssbSbrwDO.setSbrwuuid(sbrwuuid);
        znsbNssbSbrwDO.setDjxh(cxstysbNsrxx.getDjxh());
        znsbNssbSbrwDO.setNsrsbh(cxstysbNsrxx.getNsrsbh());
        znsbNssbSbrwDO.setNsrmc(cxstysbNsrxx.getNsrmc());
        znsbNssbSbrwDO.setYzpzzlDm(cxstysbNsrxx.getYzpzzlDm());
        znsbNssbSbrwDO.setNsqxDm("11");
        znsbNssbSbrwDO.setSbny(cxstysbNsrxx.getSbrq1().replace("-", "").substring(0, 6));
        if (zsxmDm.equals("10110") || zsxmDm.equals("10112")) {
            znsbNssbSbrwDO.setSkssqq(DateUtils.parseDate(initresmxvo.getSkssqq()));
            znsbNssbSbrwDO.setSkssqz(DateUtils.parseDate(initresmxvo.getSkssqz()));
        } else {
            znsbNssbSbrwDO.setSkssqq(DateUtils.parseDate(nssbzbmxlist.get(0).getSkssqq()));
            znsbNssbSbrwDO.setSkssqz(DateUtils.parseDate(nssbzbmxlist.get(0).getSkssqz()));
        }
        znsbNssbSbrwDO.setXzqhszDm(cxstysbNsrxx.getXzqhszDm());
//        znsbNssbSbrwDO.setSbqx();
        znsbNssbSbrwDO.setNsrsbztDm(SBZT_WSB_DM);
        znsbNssbSbrwDO.setYwqdDm("lq-qyd");
        znsbNssbSbrwDO.setRwztDm(RWZT_WSB_DM);
        znsbNssbSbrwDO.setRwlxDm(RWLX_CXS_DM);
        LocalDateTime newtime = LocalDateTime.now();
//        znsbNssbSbrwDO.setLrrq(newtime);
//        znsbNssbSbrwDO.setXgrq(newtime);
        znsbNssbSbrwDO.setSjcsdq(xzqhszdm);
        znsbNssbSbrwDO.setSjgsdq(xzqhszdm);
        znsbNssbSbrwDO.setSjtbSj(newtime);
        znsbNssbSbrwDO.setZsxmDm(zsxmDm);
//        znsbNssbSbrwMapper.insert(znsbNssbSbrwDO);

        return znsbNssbSbrwDO;
    }


    /**
     * 财行税合并申报保存
     *
     * @param cchxwsnssbbxxVO 入参
     * @return 返回值
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveSbbxx(CchxwsnssbbxxVO cchxwsnssbbxxVO, ZnsbInitProcDataVO znsbInitProcDataVO, CompanyBasicInfoDTO companyBasicInfoDTO, ZnsbWfDataVO znsbWfDataVO, String sfkqgzl) throws Exception {
        String sbrwuuid = cchxwsnssbbxxVO.getCchxwsnssbzbVO().getSbrwuuid();
        log.info("------sbrwuuid-------{}", sbrwuuid);
        //获取基础数据信息
        final List<CchxwsnssbInitResMxVO> initResMxVOList = cchxwsnssbbxxVO.getInitResMxVOList().stream().filter((x) -> x.getDqsfyx().equals("Y"))
                .sorted(Comparator.comparing(CchxwsnssbInitResMxVO::getMrskssqq))
                .collect(Collectors.toList());
        final List<CchxwsnssbSaveQueryVO> cchxwsnssbSaveQueryVOList = cchxwsnssbbxxVO.getCchxwsnssbSaveQueryVOList();
        //根据征收项目代码分组
        Map<String, List<CchxwsnssbSaveQueryVO>> saveQueryVOListGroup = new HashMap<>();
        if (!GyUtils.isNull(cchxwsnssbSaveQueryVOList)) {
            saveQueryVOListGroup = cchxwsnssbSaveQueryVOList.stream().collect(Collectors.groupingBy(CchxwsnssbSaveQueryVO::getZsxmDm));
        }
        final CxstysbNsrxxVO cxstysbNsrxxVO = cchxwsnssbbxxVO.getCxstysbNsrxxVO();
        final String zgswskfjDm = cxstysbNsrxxVO.getZgswskfj_dm();
        final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList = cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList();
        final List<CchxwsnssbfbmxVO> cchxwsnssbfbmxVOList = cchxwsnssbbxxVO.getCchxwsnssbfbmxVOList();
        //获取纳税人基本信息
//        final JbxxmxsjVO dwnsrxxVO = cchxwsnssbGyUtils.queryNsrxxByApi(cchxwsnssbbxxVO.getCxstysbNsrxxVO().getDjxh(), cchxwsnssbbxxVO.getCxstysbNsrxxVO().getNsrsbh());
        JbxxmxsjVO jbxxmxsjVO = null;
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrjbxxresponse = new CommonResult<>();
        if (!GyUtils.isNull(cxstysbNsrxxVO.getDjxh())) {
//            response = nsrxxcxApi.queryNsrxxBySql(djxh,"");
//        }else {
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
            nsrjbxxresponse = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        }
//        final CxNsrjbxxAndFyhyxxResVO data = response.getData();
        final ZnsbMhzcQyjbxxmxResVO data = nsrjbxxresponse.getData();
        if (!GyUtils.isNull(data) && !GyUtils.isNull(data.getJbxxmxsj())) {
            final List<JbxxmxsjVO> nsrxxList = data.getJbxxmxsj();
            if (!GyUtils.isNull(cxstysbNsrxxVO.getDjxh())) {
                jbxxmxsjVO = nsrxxList.get(0);
            } else {
                jbxxmxsjVO = FtsCxsUtils.dofierNsrxx(nsrxxList, cxstysbNsrxxVO.getNsrsbh());
            }
        }
        log.info("------jbxxmxsjVO-------{}", jbxxmxsjVO);
        String hydm = "";
        String xzqhszdm = "";
        String jdxzdm = "";
        String djzclxdm = "";
        String bsrxm = "";
        String bsrsfzjhm = "";
        String kzztlxDm = "";
        if (!GyUtils.isNull(jbxxmxsjVO)) {
            hydm = jbxxmxsjVO.getHyDm();
            xzqhszdm = jbxxmxsjVO.getScjydzxzqhszDm();
            jdxzdm = jbxxmxsjVO.getJdxzDm();
            djzclxdm = jbxxmxsjVO.getDjzclxDm();
            bsrxm = jbxxmxsjVO.getBsrxm();
            bsrsfzjhm = jbxxmxsjVO.getBsrsfzjhm();
            kzztlxDm = jbxxmxsjVO.getKzztdjlxDm();
        }
        final String djxh = cxstysbNsrxxVO.getDjxh();
        final String nsrsbh = cxstysbNsrxxVO.getNsrsbh();
        final CommonResult<CompanyBasicInfoDTO> jgxxResult = companyApi.basicInfo(djxh, nsrsbh);
        if (jgxxResult.isSuccess() && GyUtils.isNotNull(jgxxResult.getData())) {
            xzqhszdm = jgxxResult.getData().getXzqhszDm();
        }
        xzqhszdm = SfEnum.getSsjXzqhszDmByXzqhszDm(xzqhszdm);
        //纳税人信息
        final CxstysbNsrxx cxstysbNsrxx = new CxstysbNsrxx();
        cxstysbNsrxx.setDjxh(cxstysbNsrxxVO.getDjxh());
        cxstysbNsrxx.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
        cxstysbNsrxx.setNsrmc(cxstysbNsrxxVO.getNsrmc());
        cxstysbNsrxx.setSbsxDm1("11");
        cxstysbNsrxx.setSbrq1(CchxwsnssbGyUtils.getDayofNow());
        cxstysbNsrxx.setHyDm(hydm);
        cxstysbNsrxx.setJdxzDm(jdxzdm);
        cxstysbNsrxx.setYzpzzlDm("BDA0611148");
        cxstysbNsrxx.setXzqhszDm(xzqhszdm);
        cxstysbNsrxx.setZgswskfjDm(cchxwsnssbbxxVO.getCxstysbNsrxxVO().getZgswskfj_dm());
        cxstysbNsrxx.setKzztlxDm(kzztlxDm);
        cxstysbNsrxx.setDjzclxDm(djzclxdm);
        log.info("------cxstysbNsrxx-------{}", cxstysbNsrxx);
        //受理信息
        final GyJbrxxDTO jbrxxDTO = GyJbrxxUtils.getJbrxx(cxstysbNsrxxVO.getNsrsbh(),cxstysbNsrxxVO.getDjxh());
        final SbCxstysbSlxx sbCxstysbSlxx = new SbCxstysbSlxx();
        sbCxstysbSlxx.setJbr(!GyUtils.isNull(jbrxxDTO.getJbr()) ? jbrxxDTO.getJbr() : bsrxm);
        sbCxstysbSlxx.setJbrsfzjhm(jbrxxDTO.getJbrsfzjhm());

        //返回值
        final List<CxstysbSaveReturnVO> cxstysbSaveReturnVOList = new ArrayList<>();
        final List<ResVO> cwtipsList = new ArrayList<>();
        final Map<String, String> jycfReturn = new HashMap<>();
        final StringBuffer filterYqsbReturn = new StringBuffer();

        //循环可申报税源
        for (CchxwsnssbInitResMxVO mxVO : initResMxVOList) {
            log.info(cxstysbNsrxxVO.getDjxh() + mxVO.getSkssqq() + mxVO.getSkssqz() + mxVO.getZsxmdm()
                    + ":yqsbbz=" + mxVO.getYqsbbz() + "-tqsbbz=" + mxVO.getTqsbbz());
            //如果未选择是否适用减免，此时置为N
            if ("XWN".equals(mxVO.getBqsfsyzzsxgmnsrjzzc())) {
                mxVO.setBqsfsyzzsxgmnsrjzzc("N");
            }
            if (!GyUtils.isNull(cchxwsnssbSaveQueryVOList)) {
                //获取税源信息
                final List<CchxwsnssbSaveQueryVO> queryVOListTemp = saveQueryVOListGroup.get(mxVO.getZsxmdm());
                if (!GyUtils.isNull(queryVOListTemp)) {
                    //一一对应，直接取第一条
                    final CchxwsnssbSaveQueryVO queryVO = queryVOListTemp.get(0);
                    //分为两类税源，是否含按次，6+4一样的代码，暂时保留
                    if (mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmYhs())) {//印花税分支
                        Map<String, List<CchxwsnssbzbmxVO>> syuuidMap = cchxwsnssbzbmxVOList.stream().collect(Collectors.groupingBy(CchxwsnssbzbmxVO::getSyuuid));
                        Map<String, List<CchxwsnssbfbmxVO>> fbmxListMap = cchxwsnssbfbmxVOList.stream().collect(Collectors.groupingBy(CchxwsnssbfbmxVO::getSybh));
                        for (String str : syuuidMap.keySet()) {
//                            String rwuuid = GyUtils.isNotNull(sbrwuuid)?sbrwuuid:"";
//                            List<CchxwsnssbzbmxVO> nssbzbmxlist = syuuidMap.get(str);
//                            String skssqq = nssbzbmxlist.get(0).getSkssqq();
//                            String skssqz = nssbzbmxlist.get(0).getSkssqz();
//                            if(skssqq.equals(skssqz)){
//                                LambdaQueryWrapper<ZnsbNssbSbrwDO> sbrwrapper = new LambdaQueryWrapper<>();
//                                sbrwrapper.eq(ZnsbNssbSbrwDO::getDjxh,cxstysbNsrxxVO.getDjxh()).eq(ZnsbNssbSbrwDO::getNsrsbh,cxstysbNsrxxVO.getNsrsbh())
//                                        .eq(ZnsbNssbSbrwDO::getSkssqq,skssqq).eq(ZnsbNssbSbrwDO::getSkssqz,skssqz).eq(ZnsbNssbSbrwDO::getSyuuid,nssbzbmxlist.get(0).getSyuuid())
//                                        .in(ZnsbNssbSbrwDO::getNsrsbztDm,SBZT_WSB_DM,SBZT_SBSB_DM,SBZT_SHBTG_DM)
//                                        .eq(ZnsbNssbSbrwDO::getYzpzzlDm,"BDA0611148").eq(ZnsbNssbSbrwDO::getZsxmDm,GyConstants.getDmGyZsxmYhs());
//                                ZnsbNssbSbrwDO znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(sbrwrapper);
//                                if(GyUtils.isNotNull(znsbNssbSbrwDO)&&GyUtils.isNotNull(znsbNssbSbrwDO.getSbrwuuid())){
//                                    rwuuid = znsbNssbSbrwDO.getSbrwuuid();
//                                }else{
//                                    // 按次要发反写申报任务表
//                                    rwuuid = GyUtils.getUuid();
//                                    this.acSbrwInsert(rwuuid,cxstysbNsrxx,nssbzbmxlist,xzqhszdm,mxVO.getZsxmdm());
//                                }
//                            } else {
//                                // 非按次要判断是不是从菜单进,sbrwuuid为空是从菜单进入,需查询sbrw表获取sbrwuuid
//                                if(GyUtils.isNull(sbrwuuid)){
//                                    LambdaQueryWrapper<ZnsbNssbSbrwDO> sbrwrapper = new LambdaQueryWrapper<>();
//                                    sbrwrapper.eq(ZnsbNssbSbrwDO::getDjxh,cxstysbNsrxxVO.getDjxh()).eq(ZnsbNssbSbrwDO::getNsrsbh,cxstysbNsrxxVO.getNsrsbh())
//                                            .eq(ZnsbNssbSbrwDO::getSkssqq,skssqq).eq(ZnsbNssbSbrwDO::getSkssqz,skssqz)
//                                            .eq(ZnsbNssbSbrwDO::getNsrsbztDm,SBZT_WSB_DM).eq(ZnsbNssbSbrwDO::getYzpzzlDm,"BDA0611148");
//                                    ZnsbNssbSbrwDO znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(sbrwrapper);
//                                    rwuuid = znsbNssbSbrwDO.getSbrwuuid();
//                                }
//                            }
//                            List<String> syuuidList = nssbzbmxlist.stream().map(CchxwsnssbzbmxVO::getSyuuid).distinct().collect(Collectors.toList());
//
//                            //组装六税两费信息
//                            cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
//                            //根据不同登记注册类型填入不同减征类型特殊处理
//                            this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
//                            cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
//                            cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                            cxstysbNsrxx.setSkssqq(nssbzbmxlist.get(0).getSkssqq());
//                            cxstysbNsrxx.setSkssqz(nssbzbmxlist.get(0).getSkssqz());
//                            //组装税源信息
//                            final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
//                            if(!GyUtils.isNull(queryVO.getSymxVOList())){
//                                for(SymxVO symxVO : queryVO.getSymxVOList()){
//                                    if(!syuuidList.contains(symxVO.getSyuuid())){
//                                        continue;
//                                    }
//                                    final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
//                                    symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
//                                    symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
//                                    symxGridlb.add(symxGridlb10736VO);
//                                }
//                            }
//                            final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO,rwuuid);
//                            log.info("saveCxstysbHX_isOk_"+ queryVO.getZsxmDm() + "_" +isOk);

                            //封装 zc表报文
                            CchxwsnssbGzlVO cchxwsnssbGzlVO = new CchxwsnssbGzlVO();
//                            String rwuuid = GyUtils.isNotNull(sbrwuuid)?sbrwuuid:"";
                            String rwuuid = "";
                            List<CchxwsnssbzbmxVO> nssbzbmxlist = syuuidMap.get(str);
                            List<CchxwsnssbfbmxVO> nssbfbmxlist = fbmxListMap.get(str);
                            String skssqq = nssbzbmxlist.get(0).getSkssqq();
                            String skssqz = nssbzbmxlist.get(0).getSkssqz();
                            List<String> syuuidList = nssbzbmxlist.stream().map(CchxwsnssbzbmxVO::getSyuuid).distinct().collect(Collectors.toList());
                            log.info("------syuuidList-------{}", syuuidList);
                            //组装税源信息
                            final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
                            log.info("------queryVO.getSymxVOList()-------{}", queryVO.getSymxVOList());
                            if (!GyUtils.isNull(queryVO.getSymxVOList())) {
                                for (SymxVO symxVO : queryVO.getSymxVOList()) {
                                    if (!syuuidList.contains(symxVO.getSyuuid())) {
                                        continue;
                                    }
                                    final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
                                    symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
                                    symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
                                    symxGridlb.add(symxGridlb10736VO);
                                    cchxwsnssbGzlVO.setCchxwsnssbzbmxVOList(nssbzbmxlist);
                                    cchxwsnssbGzlVO.setCchxwsnssbfbmxVOList(nssbfbmxlist);
                                }
                            }
                            log.info("------symxGridlb-------{}", symxGridlb);
                            if (GyUtils.isNotNull(symxGridlb)) {
                                ZnsbNssbSbrwDO znsbNssbSbrwDO = new ZnsbNssbSbrwDO();
                                if (skssqq.equals(skssqz)) {
                                    LambdaQueryWrapper<ZnsbNssbSbrwDO> sbrwrapper = new LambdaQueryWrapper<>();
                                    sbrwrapper.eq(ZnsbNssbSbrwDO::getDjxh, cxstysbNsrxxVO.getDjxh()).eq(ZnsbNssbSbrwDO::getNsrsbh, cxstysbNsrxxVO.getNsrsbh())
                                            .eq(ZnsbNssbSbrwDO::getSkssqq, DateUtils.parseDate(skssqq,"yyyy-MM-dd")).eq(ZnsbNssbSbrwDO::getSkssqz, DateUtils.parseDate(skssqz,"yyyy-MM-dd")).like(ZnsbNssbSbrwDO::getSyuuid1, nssbzbmxlist.get(0).getSyuuid())
                                            .in(ZnsbNssbSbrwDO::getNsrsbztDm, SBZT_WSB_DM, SBZT_SBSB_DM, SBZT_SHBTG_DM,SBZT_ZF_ZFCG_DM)
                                            .eq(ZnsbNssbSbrwDO::getYzpzzlDm, "BDA0611148").eq(ZnsbNssbSbrwDO::getZsxmDm, GyConstants.getDmGyZsxmYhs());
                                    znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(sbrwrapper);
                                    log.info("------znsbNssbSbrwDO-------{}", znsbNssbSbrwDO);
                                    if (GyUtils.isNotNull(znsbNssbSbrwDO) && GyUtils.isNotNull(znsbNssbSbrwDO.getSbrwuuid())) {
                                        rwuuid = znsbNssbSbrwDO.getSbrwuuid();
                                    } else {
                                        continue;
                                    }
//                                    else{
//                                        // 按次要发反写申报任务表
//                                        rwuuid = GyUtils.getUuid();
//                                        znsbNssbSbrwDO = this.acSbrwInsert(rwuuid,cxstysbNsrxx,nssbzbmxlist,xzqhszdm,mxVO.getZsxmdm(),mxVO);
//                                    }
                                } else {
                                    if (GyUtils.isNull(sbrwuuid)) {
                                        LambdaQueryWrapper<ZnsbNssbSbrwDO> sbrwrapper = new LambdaQueryWrapper<>();
                                        sbrwrapper.eq(ZnsbNssbSbrwDO::getDjxh, cxstysbNsrxxVO.getDjxh()).eq(ZnsbNssbSbrwDO::getNsrsbh, cxstysbNsrxxVO.getNsrsbh())
                                                .eq(ZnsbNssbSbrwDO::getSkssqq, DateUtils.parseDate(skssqq,"yyyy-MM-dd")).eq(ZnsbNssbSbrwDO::getSkssqz, DateUtils.parseDate(skssqz,"yyyy-MM-dd"))
                                                .in(ZnsbNssbSbrwDO::getNsrsbztDm, SBZT_WSB_DM, SBZT_SBSB_DM, SBZT_SHBTG_DM,SBZT_ZF_ZFCG_DM)
                                                .eq(ZnsbNssbSbrwDO::getYzpzzlDm, "BDA0611148").eq(ZnsbNssbSbrwDO::getZsxmDm, GyConstants.getDmGyZsxmYhs());
                                        znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(sbrwrapper);
                                    } else {
                                        znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(new LambdaQueryWrapper<ZnsbNssbSbrwDO>()
                                                .eq(ZnsbNssbSbrwDO::getSbrwuuid, sbrwuuid));
                                    }
                                    log.info("------znsbNssbSbrwDO-------{}", znsbNssbSbrwDO);
                                    if (GyUtils.isNull(znsbNssbSbrwDO)) {
                                        continue;
                                    }
                                    rwuuid = znsbNssbSbrwDO.getSbrwuuid();
                                }
                                cchxwsnssbGzlVO.setDjxh(cxstysbNsrxxVO.getDjxh());
                                cchxwsnssbGzlVO.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
                                cchxwsnssbGzlVO.setNsrmc(cxstysbNsrxxVO.getNsrmc());
                                cchxwsnssbGzlVO.setSbrwuuid(rwuuid);
                                cchxwsnssbGzlVO.setZsxmDm(mxVO.getZsxmdm());
                                cchxwsnssbGzlVO.setSkssqq(nssbzbmxlist.get(0).getSkssqq());
                                cchxwsnssbGzlVO.setSkssqz(nssbzbmxlist.get(0).getSkssqz());
                                cchxwsnssbGzlVO.setXzqhszDm(xzqhszdm);
                                List<CchxwsnssbInitResMxVO> resmxlist = new ArrayList<>();
                                resmxlist.add(mxVO);
                                cchxwsnssbGzlVO.setInitResMxVOList(resmxlist);
                                cchxwsnssbGzlVO.setSyuuid(syuuidList);
                                //组装六税两费信息
                                cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
                                //根据不同登记注册类型填入不同减征类型特殊处理
                                this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
                                cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
                                cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                                cxstysbNsrxx.setSkssqq(mxVO.getSkssqq());
//                                cxstysbNsrxx.setSkssqz(mxVO.getSkssqz());
                                cxstysbNsrxx.setSkssqq(skssqq);
                                cxstysbNsrxx.setSkssqz(skssqz);
                                log.info("------cxstysbNsrxx-------{}", cxstysbNsrxx);
                                log.info("------cchxwsnssbGzlVO-------{}", cchxwsnssbGzlVO);
                                cxsGyUtils.savesbcl(sfkqgzl, znsbNssbSbrwDO, queryVO, cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, cchxwsnssbGzlVO, znsbInitProcDataVO, companyBasicInfoDTO,
                                        mxVO, znsbWfDataVO, rwuuid, syuuidList, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, jbxxmxsjVO);
                            }
                        }
                    }
//                    else if(mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmZys())){//资源税分支
//                        //组装六税两费信息
//                        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
//                        //根据不同登记注册类型填入不同减征类型特殊处理
//                        this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
//                        cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
//                        cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                        cxstysbNsrxx.setSkssqq(mxVO.getSkssqq());
//                        cxstysbNsrxx.setSkssqz(mxVO.getSkssqz());
//                        //组装税源信息
//                        final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
//                        if(!GyUtils.isNull(queryVO.getSymxVOList())){
//                            for(SymxVO symxVO : queryVO.getSymxVOList()){
//                                final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
//                                symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
//                                symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
//                                symxGridlb.add(symxGridlb10736VO);
//                            }
//                        }
//                        final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO);
//                        log.info("saveCxstysbHX_isOk_"+ queryVO.getZsxmDm() + "_" +isOk);
//
//                    }
//                    else if (mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmGdzys())){//耕地占用税分支
//                        //组装六税两费信息
//                        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
//                        //根据不同登记注册类型填入不同减征类型特殊处理
//                        this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
//                        cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
//                        cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                        cxstysbNsrxx.setSkssqq(mxVO.getSkssqq());
//                        cxstysbNsrxx.setSkssqz(mxVO.getSkssqz());
//                        //组装税源信息
//                        final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
//                        if(!GyUtils.isNull(queryVO.getSymxVOList())){
//                            for(SymxVO symxVO : queryVO.getSymxVOList()){
//                                final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
//                                symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
//                                symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
//                                symxGridlb.add(symxGridlb10736VO);
//                            }
//                        }
//                        //保存检验是否重复申报
//                        final boolean isRepeatFlag = this.checkRepeatSave(queryVO.getZsxmDm(), cxstysbNsrxx, symxGridlb, zgswskfjDm, YHLX_NSR, cwtipsList);
//                        if (!isRepeatFlag) {
//                            if ("Y".equals(mxVO.getGdzysWpxzCsz())) {
//                                //Y为开启区分，N为原逻辑
//                                final List<SymxGridlb10736VO> wpxzList = new ArrayList<>();
//                                final List<SymxGridlb10736VO> otherList = new ArrayList<>();
//                                final List<Map<String, Object>> symxListAll = this.getGdzysWpxzSyList(queryVO.getSymxVOList(), new ArrayList<>(), YHLX_NSR);
//                                this.clGdzysWpxzSB(symxListAll, symxGridlb, wpxzList, otherList);
//                                if (!GyUtils.isNull(wpxzList)) {
//                                    final CxstysbNsrxx bxsCopy = this.getBxsNsrVO(cxstysbNsrxx);
//                                    final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), bxsCopy, sbCxstysbSlxx, wpxzList, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO);
//                                    log.info("saveCxstysbHX_isOk_CF1" + queryVO.getZsxmDm() + "_" + isOk);
//                                }
//                                if (!GyUtils.isNull(otherList)) {
//                                    final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, otherList, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO);
//                                    log.info("saveCxstysbHX_isOk_CF2" + queryVO.getZsxmDm() + "_" + isOk);
//                                }
//                            } else {
//                                final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO);
//                                log.info("saveCxstysbHX_isOk_" + queryVO.getZsxmDm() + "_" + isOk);
//                            }
//                        }
//
//                    }
//                    else if(mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmQs())){//契税分支
//                        //组装六税两费信息
//                        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
//                        //根据不同登记注册类型填入不同减征类型特殊处理
//                        this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
//                        cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
//                        cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                        cxstysbNsrxx.setSkssqq(mxVO.getSkssqq());
//                        cxstysbNsrxx.setSkssqz(mxVO.getSkssqz());
//                        //组装税源信息
//                        final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
//                        if(!GyUtils.isNull(queryVO.getSymxVOList())){
//                            for(SymxVO symxVO : queryVO.getSymxVOList()){
//                                final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
//                                symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
//                                symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
//                                symxGridlb.add(symxGridlb10736VO);
//                            }
//                        }
//                        final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO);
//                        log.info("saveCxstysbHX_isOk_"+ queryVO.getZsxmDm() + "_" +isOk);
//
//                    }
//                    else if(mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmHbs())){//环境保护税分支
//                        //组装六税两费信息
//                        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
//                        //根据不同登记注册类型填入不同减征类型特殊处理
//                        this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
//                        cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
//                        cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                        cxstysbNsrxx.setSkssqq(mxVO.getSkssqq());
//                        cxstysbNsrxx.setSkssqz(mxVO.getSkssqz());
//                        //组装税源信息
//                        final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
//                        if(!GyUtils.isNull(queryVO.getSymxVOList())){
//                            for(SymxVO symxVO : queryVO.getSymxVOList()){
//                                final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
//                                symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
//                                symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
//                                symxGridlb.add(symxGridlb10736VO);
//                            }
//                        }
//                        final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO);
//                        log.info("saveCxstysbHX_isOk_"+ queryVO.getZsxmDm() + "_" +isOk);
//
//                    }
//                    else if (mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmTdzzs())) {//土地增值税分支
//                        //组装六税两费信息
//                        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
//                        //根据不同登记注册类型填入不同减征类型特殊处理
//                        this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
//                        cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
//                        cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                        cxstysbNsrxx.setSkssqq(mxVO.getSkssqq());
//                        cxstysbNsrxx.setSkssqz(mxVO.getSkssqz());
//                        //组装税源信息
//                        final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
//                        if(!GyUtils.isNull(queryVO.getSymxVOList())){
//                            for(SymxVO symxVO : queryVO.getSymxVOList()){
//                                final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
//                                symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
//                                symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
//                                symxGridlb.add(symxGridlb10736VO);
//                            }
//                        }
//                        final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO);
//                        log.info("saveCxstysbHX_isOk_"+ queryVO.getZsxmDm() + "_" +isOk);
//
//                    }
//                    else if(mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmCcs())){//车船税分支
//                        //无税源uuid明细，则证明为纯粹按期申报，传默认属期
//                        cxstysbNsrxx.setSkssqq(mxVO.getMrskssqq());
//                        cxstysbNsrxx.setSkssqz(mxVO.getMrskssqz());
//                        //组装六税两费信息
//                        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
//                        //根据不同登记注册类型填入不同减征类型特殊处理
//                        this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
//                        cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
//                        cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                        //组装税源信息
//                        final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
//                        if(!GyUtils.isNull(queryVO.getSymxVOList())){
//                            for(SymxVO symxVO : queryVO.getSymxVOList()){
//                                final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
//                                symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
//                                symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
//                                symxGridlb.add(symxGridlb10736VO);
//                            }
//                        }
//                        final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO);
//                        log.info("saveCxstysbHX_isOk_"+ queryVO.getZsxmDm() + "_" +isOk);
//
//                    }
                    else if (mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmCztdsys())) {
                        //城镇土地使用税分支
                        // 过滤出城镇土地部分
                        List<CchxwsnssbzbmxVO> cztdzbmxList = cchxwsnssbzbmxVOList.stream().filter(x -> x.getZsxmDm().equals(GyConstants.getDmGyZsxmCztdsys()))
                                .collect(Collectors.toList());
                        if (GyUtils.isNull(cztdzbmxList)) {
                            continue;
                        }
                        Map<String, List<CchxwsnssbzbmxVO>> syuuidMap = cztdzbmxList.stream().collect(Collectors.groupingBy(CchxwsnssbzbmxVO::getSyuuid));
                        Map<String, List<CchxwsnssbfbmxVO>> fbmxListMap = cchxwsnssbfbmxVOList.stream().collect(Collectors.groupingBy(CchxwsnssbfbmxVO::getSybh));
                        for (String str : syuuidMap.keySet()) {
                            List<String> syuuidList = syuuidMap.get(str).stream().map(CchxwsnssbzbmxVO::getSyuuid).distinct().collect(Collectors.toList());
                            LambdaQueryWrapper<ZnsbNssbSbrwDO> sbrwrapper = new LambdaQueryWrapper<>();
                            sbrwrapper.eq(ZnsbNssbSbrwDO::getDjxh, cxstysbNsrxxVO.getDjxh()).eq(ZnsbNssbSbrwDO::getNsrsbh, cxstysbNsrxxVO.getNsrsbh())
                                    .in(ZnsbNssbSbrwDO::getNsrsbztDm, SBZT_WSB_DM, SBZT_SBSB_DM, SBZT_SHBTG_DM,SBZT_ZF_ZFCG_DM)
                                    .eq(ZnsbNssbSbrwDO::getYzpzzlDm, "BDA0611148").eq(ZnsbNssbSbrwDO::getZsxmDm, GyConstants.getDmGyZsxmCztdsys())
                                    .like(ZnsbNssbSbrwDO::getSyuuid1, str)
                                    .orderByDesc(ZnsbNssbSbrwDO::getSjtbSj);
                            List<ZnsbNssbSbrwDO> znsbNssbSbrwDOlist = znsbNssbSbrwMapper.selectList(sbrwrapper);
                            //
                            if (GyUtils.isNull(znsbNssbSbrwDOlist)) {
                                continue;
                            }
                            ZnsbNssbSbrwDO znsbNssbSbrwDO = znsbNssbSbrwDOlist.get(0);
                            //无税源uuid明细，则证明为纯粹按期申报，传默认属期
                            cxstysbNsrxx.setSkssqq(mxVO.getMrskssqq());
                            cxstysbNsrxx.setSkssqz(mxVO.getMrskssqz());
                            //组装六税两费信息
                            cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
                            //根据不同登记注册类型填入不同减征类型特殊处理
                            this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
                            cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
                            cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
                            //组装税源信息
                            final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
                            final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
                            symxGridlb10736VO.setSyuuid(str);
//                            symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
                            symxGridlb.add(symxGridlb10736VO);
                            //封装 zc表报文
                            CchxwsnssbGzlVO cchxwsnssbGzlVO = new CchxwsnssbGzlVO();
                            cchxwsnssbGzlVO.setCchxwsnssbzbmxVOList(syuuidMap.get(str));
                            cchxwsnssbGzlVO.setCchxwsnssbfbmxVOList(fbmxListMap.get(str));
                            cchxwsnssbGzlVO.setDjxh(cxstysbNsrxxVO.getDjxh());
                            cchxwsnssbGzlVO.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
                            cchxwsnssbGzlVO.setNsrmc(cxstysbNsrxxVO.getNsrmc());
                            cchxwsnssbGzlVO.setSbrwuuid(znsbNssbSbrwDO.getSbrwuuid());
                            cchxwsnssbGzlVO.setZsxmDm(mxVO.getZsxmdm());
                            cchxwsnssbGzlVO.setSkssqq(mxVO.getMrskssqq());
                            cchxwsnssbGzlVO.setSkssqz(mxVO.getMrskssqz());
                            cchxwsnssbGzlVO.setXzqhszDm(xzqhszdm);
                            List<CchxwsnssbInitResMxVO> resmxlist = new ArrayList<>();
                            resmxlist.add(mxVO);
                            cchxwsnssbGzlVO.setInitResMxVOList(resmxlist);
                            cchxwsnssbGzlVO.setSyuuid(syuuidList);
                            cxsGyUtils.savesbcl(sfkqgzl, znsbNssbSbrwDO, queryVO, cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, cchxwsnssbGzlVO, znsbInitProcDataVO, companyBasicInfoDTO,
                                    mxVO, znsbWfDataVO, znsbNssbSbrwDO.getSbrwuuid(), syuuidList, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, jbxxmxsjVO);
                        }
                    } else if (mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmFcs())) {
                        //房产税分支
                        // 过滤出房产税部分
                        List<CchxwsnssbzbmxVO> fcszbmxList = cchxwsnssbzbmxVOList.stream().filter(x -> GyConstants.getDmGyZsxmFcs().equals(x.getZsxmDm()))
                                .collect(Collectors.toList());
                        if (GyUtils.isNull(fcszbmxList)) {
                            continue;
                        }

                        Map<String, List<CchxwsnssbzbmxVO>> syuuidMap = fcszbmxList.stream().collect(Collectors.groupingBy(CchxwsnssbzbmxVO::getSyuuid));
                        Map<String, List<CchxwsnssbfbmxVO>> fbmxListMap = cchxwsnssbfbmxVOList.stream().collect(Collectors.groupingBy(CchxwsnssbfbmxVO::getSybh));
                        for (String str : syuuidMap.keySet()) {
                            final String skssqq = mxVO.getSkssqq();
                            final String skssqz = mxVO.getSkssqz();
                            List<String> syuuidList = syuuidMap.get(str).stream().map(CchxwsnssbzbmxVO::getSyuuid).distinct().collect(Collectors.toList());
                            LambdaQueryWrapper<ZnsbNssbSbrwDO> sbrwrapper = new LambdaQueryWrapper<>();
                            sbrwrapper.eq(ZnsbNssbSbrwDO::getDjxh, cxstysbNsrxxVO.getDjxh()).eq(ZnsbNssbSbrwDO::getNsrsbh, cxstysbNsrxxVO.getNsrsbh())
                                    .in(ZnsbNssbSbrwDO::getNsrsbztDm, SBZT_WSB_DM, SBZT_SBSB_DM, SBZT_SHBTG_DM,SBZT_ZF_ZFCG_DM)
                                    .eq(ZnsbNssbSbrwDO::getYzpzzlDm, "BDA0611148").eq(ZnsbNssbSbrwDO::getZsxmDm, GyConstants.getDmGyZsxmFcs())
                                    .like(ZnsbNssbSbrwDO::getSyuuid1, str)
                                    .eq(ZnsbNssbSbrwDO::getSkssqq,DateUtils.parseDate(skssqq))
                                    .eq(ZnsbNssbSbrwDO::getSkssqz,DateUtils.parseDate(skssqz))
                                    .orderByDesc(ZnsbNssbSbrwDO::getSjtbSj);
                            List<ZnsbNssbSbrwDO> znsbNssbSbrwDOlist = znsbNssbSbrwMapper.selectList(sbrwrapper);
                            //
                            if (GyUtils.isNull(znsbNssbSbrwDOlist)) {
                                continue;
                            }
                            ZnsbNssbSbrwDO znsbNssbSbrwDO = znsbNssbSbrwDOlist.get(0);

                            //取申报任务中属期
                            cxstysbNsrxx.setSkssqq(DateUtils.format(znsbNssbSbrwDO.getSkssqq(),"yyyy-MM-dd"));
                            cxstysbNsrxx.setSkssqz(DateUtils.format(znsbNssbSbrwDO.getSkssqz(),"yyyy-MM-dd"));
                            // 获取征收品目（从租还是从价还是一起报）
                            if (GyUtils.isNotNull(znsbNssbSbrwDO.getZspmDm())){
                                cxstysbNsrxx.setZspmDm(znsbNssbSbrwDO.getZspmDm());
                            }
                            //组装六税两费信息
                            cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
                            //根据不同登记注册类型填入不同减征类型特殊处理
                            this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
                            cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
                            cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
                            //组装税源信息
                            final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
                            final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
                            symxGridlb10736VO.setSyuuid(str);
//                            symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
                            symxGridlb.add(symxGridlb10736VO);
                            //封装 zc表报文
                            CchxwsnssbGzlVO cchxwsnssbGzlVO = new CchxwsnssbGzlVO();
                            cchxwsnssbGzlVO.setCchxwsnssbzbmxVOList(syuuidMap.get(str));
                            cchxwsnssbGzlVO.setCchxwsnssbfbmxVOList(fbmxListMap.get(str));
                            cchxwsnssbGzlVO.setDjxh(cxstysbNsrxxVO.getDjxh());
                            cchxwsnssbGzlVO.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
                            cchxwsnssbGzlVO.setNsrmc(cxstysbNsrxxVO.getNsrmc());
                            cchxwsnssbGzlVO.setSbrwuuid(znsbNssbSbrwDO.getSbrwuuid());
                            cchxwsnssbGzlVO.setZsxmDm(mxVO.getZsxmdm());
                            cchxwsnssbGzlVO.setSkssqq(DateUtils.format(znsbNssbSbrwDO.getSkssqq(),"yyyy-MM-dd"));
                            cchxwsnssbGzlVO.setSkssqz(DateUtils.format(znsbNssbSbrwDO.getSkssqz(),"yyyy-MM-dd"));
                            cchxwsnssbGzlVO.setXzqhszDm(xzqhszdm);
                            List<CchxwsnssbInitResMxVO> resmxlist = new ArrayList<>();
                            resmxlist.add(mxVO);
                            cchxwsnssbGzlVO.setInitResMxVOList(resmxlist);
                            cchxwsnssbGzlVO.setSyuuid(syuuidList);
                            cxsGyUtils.savesbcl(sfkqgzl, znsbNssbSbrwDO, queryVO, cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, cchxwsnssbGzlVO, znsbInitProcDataVO, companyBasicInfoDTO,
                                    mxVO, znsbWfDataVO, znsbNssbSbrwDO.getSbrwuuid(), syuuidList, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, jbxxmxsjVO);
                        }
                    } else if (mxVO.getZsxmdm().equals(GyConstants.getDmGyZsxmYys())) {//烟叶税分支
                        Map<String, List<CchxwsnssbzbmxVO>> syuuidMap = cchxwsnssbzbmxVOList.stream().collect(Collectors.groupingBy(CchxwsnssbzbmxVO::getSyuuid));
                        Map<String, List<CchxwsnssbfbmxVO>> fbmxListMap = cchxwsnssbfbmxVOList.stream().collect(Collectors.groupingBy(CchxwsnssbfbmxVO::getSybh));
                        for (String str : syuuidMap.keySet()) {
                            //封装 zc表报文
                            CchxwsnssbGzlVO cchxwsnssbGzlVO = new CchxwsnssbGzlVO();
//                            String rwuuid = GyUtils.isNotNull(sbrwuuid)?sbrwuuid:"";
                            String rwuuid = "";
                            List<CchxwsnssbzbmxVO> nssbzbmxlist = syuuidMap.get(str);
                            List<CchxwsnssbfbmxVO> nssbfbmxlist = fbmxListMap.get(str);
                            String skssqq = nssbzbmxlist.get(0).getSkssqq();
                            String skssqz = nssbzbmxlist.get(0).getSkssqz();
                            List<String> syuuidList = nssbzbmxlist.stream().map(CchxwsnssbzbmxVO::getSyuuid).distinct().collect(Collectors.toList());
                            log.info("------syuuidList-------{}", syuuidList);
                            //组装税源信息
                            final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
                            log.info("------queryVO.getSymxVOList()-------{}", queryVO.getSymxVOList());
                            if (!GyUtils.isNull(queryVO.getSymxVOList())) {
                                for (SymxVO symxVO : queryVO.getSymxVOList()) {
                                    if (!syuuidList.contains(symxVO.getSyuuid())) {
                                        continue;
                                    }
                                    final SymxGridlb10736VO symxGridlb10736VO = new SymxGridlb10736VO();
                                    symxGridlb10736VO.setSyuuid(symxVO.getSyuuid());
                                    symxGridlb10736VO.setSybzDm1(symxVO.getSybzDm1());
                                    symxGridlb.add(symxGridlb10736VO);
                                    cchxwsnssbGzlVO.setCchxwsnssbzbmxVOList(nssbzbmxlist);
                                    cchxwsnssbGzlVO.setCchxwsnssbfbmxVOList(nssbfbmxlist);
                                }
                            }
                            log.info("------symxGridlb-------{}", symxGridlb);
                            if (GyUtils.isNotNull(symxGridlb)) {
                                ZnsbNssbSbrwDO znsbNssbSbrwDO;
                                if (GyUtils.isNull(sbrwuuid)) {
                                    LambdaQueryWrapper<ZnsbNssbSbrwDO> sbrwrapper = new LambdaQueryWrapper<>();
                                    sbrwrapper.eq(ZnsbNssbSbrwDO::getDjxh, cxstysbNsrxxVO.getDjxh()).eq(ZnsbNssbSbrwDO::getNsrsbh, cxstysbNsrxxVO.getNsrsbh())
                                            .eq(ZnsbNssbSbrwDO::getSkssqq, DateUtils.parseDate(skssqq,"yyyy-MM-dd")).eq(ZnsbNssbSbrwDO::getSkssqz, DateUtils.parseDate(skssqz,"yyyy-MM-dd"))
                                            .in(ZnsbNssbSbrwDO::getNsrsbztDm, SBZT_WSB_DM, SBZT_SBSB_DM, SBZT_SHBTG_DM,SBZT_ZF_ZFCG_DM)
                                            .eq(ZnsbNssbSbrwDO::getYzpzzlDm, "BDA0611148").eq(ZnsbNssbSbrwDO::getZsxmDm, GyConstants.getDmGyZsxmYys());
                                    znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(sbrwrapper);
                                } else {
                                    znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(new LambdaQueryWrapper<ZnsbNssbSbrwDO>()
                                            .eq(ZnsbNssbSbrwDO::getSbrwuuid, sbrwuuid));
                                }
                                log.info("------znsbNssbSbrwDO-------{}", znsbNssbSbrwDO);
                                if (GyUtils.isNull(znsbNssbSbrwDO)) {
                                    continue;
                                }
                                rwuuid = znsbNssbSbrwDO.getSbrwuuid();
                                cchxwsnssbGzlVO.setDjxh(cxstysbNsrxxVO.getDjxh());
                                cchxwsnssbGzlVO.setNsrsbh(cxstysbNsrxxVO.getNsrsbh());
                                cchxwsnssbGzlVO.setNsrmc(cxstysbNsrxxVO.getNsrmc());
                                cchxwsnssbGzlVO.setSbrwuuid(rwuuid);
                                cchxwsnssbGzlVO.setZsxmDm(mxVO.getZsxmdm());
                                cchxwsnssbGzlVO.setSkssqq(nssbzbmxlist.get(0).getSkssqq());
                                cchxwsnssbGzlVO.setSkssqz(nssbzbmxlist.get(0).getSkssqz());
                                cchxwsnssbGzlVO.setXzqhszDm(xzqhszdm);
                                List<CchxwsnssbInitResMxVO> resmxlist = new ArrayList<>();
                                resmxlist.add(mxVO);
                                cchxwsnssbGzlVO.setInitResMxVOList(resmxlist);
                                cchxwsnssbGzlVO.setSyuuid(syuuidList);
                                //组装六税两费信息
                                cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
                                //根据不同登记注册类型填入不同减征类型特殊处理
                                this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
                                cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
                                cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
                                cxstysbNsrxx.setSkssqq(skssqq);
                                cxstysbNsrxx.setSkssqz(skssqz);
                                log.info("------cxstysbNsrxx-------{}", cxstysbNsrxx);
                                log.info("------cchxwsnssbGzlVO-------{}", cchxwsnssbGzlVO);
                                cxsGyUtils.savesbcl(sfkqgzl, znsbNssbSbrwDO, queryVO, cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, cchxwsnssbGzlVO, znsbInitProcDataVO, companyBasicInfoDTO,
                                        mxVO, znsbWfDataVO, rwuuid, syuuidList, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, jbxxmxsjVO);
                            }
                        }
                    }
                }
            }
//            else{//此分支是否会进入，待确认
//                //无税源uuid明细，则证明为纯粹按期申报，传默认属期
//                cxstysbNsrxx.setSkssqq(mxVO.getMrskssqq());
//                cxstysbNsrxx.setSkssqz(mxVO.getMrskssqz());
//                //组装六税两费信息
//                cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(mxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : mxVO.getBqsfsyzzsxgmnsrjzzc());
//                //根据不同登记注册类型填入不同减征类型特殊处理
//                this.dealWithJzzcsyztDm(mxVO, cxstysbNsrxx, djzclxdm);
//                cxstysbNsrxx.setXgmjzzcqssj(mxVO.getXgmjzzcqssj());
//                cxstysbNsrxx.setXgmjzzczzsj(mxVO.getXgmjzzczzsj());
//                //调用核心接口
//                final List<SymxGridlb10736VO> symxGridlb = new ArrayList<>();
//                final boolean isOk = this.saveCxstysbHX(mxVO.getZsxmdm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO,sbrwuuid);
//                log.info("saveCxstysbHX_isOk_"+ mxVO.getZsxmdm() + "_" +isOk);
//            }
        }

//        //凭证序号集合
//        final List<String> stringList = new ArrayList<>();
//        //处理保存结果信息
//        if (!GyUtils.isNull(cxstysbSaveReturnVOList)) {
//            for (CxstysbSaveReturnVO saveReturnVO : cxstysbSaveReturnVOList) {
//                List<SBSaveReturnVO> savereturnGridlb = saveReturnVO.getSavereturnGrid().getSavereturnGridlb();
//                for (SBSaveReturnVO sbSaveReturnVO : savereturnGridlb) {
//                    stringList.add(sbSaveReturnVO.getPzxh());
//                }
//            }
//        }
//
//        //查询纳税人未清缴(欠税费)信息
//        final List<CchxwsnssbjfxxVO> jfxxmcVOList = new ArrayList<>();
//        this.queryYjsfHX(cxstysbNsrxxVO.getDjxh(), new HashSet<>(), zgswskfjDm, stringList, cwtipsList, jfxxmcVOList);
//
//        //对无需缴费申报信息进行组装
//        final List<CchxwsnssbjfxxVO> sfxxAllVOListTemp = new ArrayList<>();
//        this.dealWithYjsf(jfxxmcVOList, cxstysbSaveReturnVOList, cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList(), sfxxAllVOListTemp);
//        //放入应缴税费并排序
//        sfxxAllVOListTemp.addAll(jfxxmcVOList);
//        final List<CchxwsnssbjfxxVO> sfxxAllVOList = sfxxAllVOListTemp.stream().sorted(Comparator.comparing(CchxwsnssbjfxxVO::getZsxmDm)).collect(Collectors.toList());

        //添加接口返回值
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("cxstysbSaveReturnVO", cxstysbSaveReturnVOList);
        returnMap.put("cchxwsnssbbxxVO", cchxwsnssbbxxVO);
        returnMap.put("cwtipsMap", cwtipsList);
        returnMap.put("jycfReturn", jycfReturn);
        returnMap.put("filterYqsbReturn", filterYqsbReturn);

//        returnMap.put("jfxxmcVOList",jfxxmcVOList);
//        returnMap.put("sfxxAllVOList", sfxxAllVOList);
//        //保存异步流程业务数据  只有异步流程有这个值
//        if(!CxsGyUtils.isNull(cchxwsnssbbxxVO.getCxstysbNsrxxVO().getYsquuid())){
//            this.updateSbls(cchxwsnssbbxxVO.getCxstysbNsrxxVO().getYsquuid() , returnMap , CxsSxztbmConstants.getAcceptSuccess());
//        }
        //好差评接入
//        this.sendHcpMessage(cxstysbSaveReturnVOList, cxstysbNsrxxVO, cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList());
//        returnMap = JacksonUtils.toObj(DbUtils.getData("savesuccess"),Map.class);
        return returnMap;
    }
//    @Transactional(rollbackFor = Exception.class)
//    void savesbcl(String sfkqgzl,ZnsbNssbSbrwDO znsbNssbSbrwDO,CchxwsnssbSaveQueryVO queryVO,CxstysbNsrxx cxstysbNsrxx,
//                  SbCxstysbSlxx sbCxstysbSlxx,List<SymxGridlb10736VO> symxGridlb,CchxwsnssbGzlVO cchxwsnssbGzlVO,
//                  ZnsbInitProcDataVO znsbInitProcDataVO,CompanyBasicInfoDTO companyBasicInfoDTO,CchxwsnssbInitResMxVO mxVO,
//                  ZnsbWfDataVO znsbWfDataVO,String rwuuid,List<String> syuuidList,String zgswskfjDm,List<ResVO> cwtipsList,
//                  List<CxstysbSaveReturnVO> cxstysbSaveReturnVOList,Map<String, String> jycfReturn,StringBuffer filterYqsbReturn){
//        Long count = znsbNssbSbrwMapper.selectCount(new LambdaQueryWrapper<ZnsbNssbSbrwDO>().eq(ZnsbNssbSbrwDO::getSbrwuuid,znsbNssbSbrwDO.getSbrwuuid()));
//        if(count == 0){
//            znsbNssbSbrwMapper.insert(znsbNssbSbrwDO);
//        }
//        final String lcslid = GyUtils.getUuid();
//        this.saveGzlzcb(cchxwsnssbGzlVO,lcslid);
//        if(sfkqgzl.equals("Y")){
//            this.saveCxsGzl(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, cchxwsnssbGzlVO);
//            // 开启工作流,写暂存表
//            this.gzlcl(znsbInitProcDataVO, companyBasicInfoDTO, DateUtils.format(znsbNssbSbrwDO.getSkssqq(),"yyyy-MM-dd"), DateUtils.format(znsbNssbSbrwDO.getSkssqz(),"yyyy-MM-dd"), znsbWfDataVO, lcslid);
//            // 修改申报任务表状态为审核中
//            this.updateSbrw(rwuuid, syuuidList);
//        } else {
//            final boolean isOk = this.saveCxstysbHX(queryVO.getZsxmDm(), cxstysbNsrxx, sbCxstysbSlxx, symxGridlb, zgswskfjDm, cwtipsList, cxstysbSaveReturnVOList, jycfReturn, filterYqsbReturn, mxVO,rwuuid);
//            log.info("saveCxstysbHX_isOk_"+ queryVO.getZsxmDm() + "_" +isOk);
//        }
//    }

    public void updateSbrw(String sbrwuuid, List<String> syuuidList, String zsdmDm, BigDecimal ybtsje) {
        LambdaUpdateWrapper<ZnsbNssbSbrwDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(ZnsbNssbSbrwDO::getNsrsbztDm, SBZT_SHZ_DM)
                .set(ZnsbNssbSbrwDO::getYbtse, ybtsje)
                .eq(ZnsbNssbSbrwDO::getSbrwuuid, sbrwuuid);
        znsbNssbSbrwMapper.update(lambdaUpdateWrapper);
        //修改税源状态
        this.updateSybczt(zsdmDm, SBZT_SHZ_DM, syuuidList, sbrwuuid);
    }

    public void updateSybczt(String zsdmDm, String bczt, List<String> syuuidList, String sbrwuuid) {
        log.info("-----zsdmDm-----{}", zsdmDm);
        log.info("-----bczt-----{}", bczt);
        log.info("-----syuuidList-----{}", syuuidList);
        if (GyUtils.isNull(syuuidList)) {
            return;
        }

        // 查询syuuid是否还有未申报的申报任务，有则不更新税源状态
        List<ZnsbNssbSbrwDO> znsbNssbSbrwDOList = znsbNssbSbrwMapper.queryWsbBySyuuidList(syuuidList,sbrwuuid);
        log.info("-----znsbNssbSbrwDOList-----{}", znsbNssbSbrwDOList);
        List<String> filteredList = new ArrayList<>();
        if (GyUtils.isNotNull(znsbNssbSbrwDOList)){
            // 提取 znsbNssbSbrwDOList 中所有的 syuuid 到一个 Set 中（用于高效查找）
            Set<String> existingUuids = znsbNssbSbrwDOList.stream()
                    .map(ZnsbNssbSbrwDO::getSyuuid)
                    .collect(Collectors.toSet());
            log.info("-----existingUuids-----{}", existingUuids);
            // 过滤 syuuidList，排除在 znsbNssbSbrwDOList 中已存在的 syuuid
           filteredList = syuuidList.stream()
                    .filter(uuid -> !existingUuids.contains(uuid))
                    .collect(Collectors.toList());

        }else {
            filteredList = syuuidList;
        }

        log.info("-----filteredList-----{}", filteredList);
        String ysbbz = "";
        if (zsdmDm.equals("10111")) {
            ysbbz = bczt.equals(SBZT_SBCG_DM) ? "Y" : "";
        }
        // 税源保存状态，30处理成功 50已申报
        String sybczt = CLCG;
        switch (bczt) {
            case SBZT_SHZ_DM:
            case SBZT_SBZ_DM:
                sybczt = SBZ;
                break;
            case SBZT_SBCG_DM:
                sybczt = SBCG;
                break;
            case SBZT_SBSB_DM:
                sybczt = SBSB;
                break;
            case SBZT_SHBTG_DM:
                sybczt = CLCG;
                break;
            default:
                break;
        }
        log.info("-----sybczt-----{}", bczt);
        for (String syuuid : filteredList) {
            dataUpdateService.updateData(zsdmDm, sybczt, syuuid, ysbbz);
        }
    }

    public void gzlcl(ZnsbInitProcDataVO znsbInitProcDataVO, CompanyBasicInfoDTO companyBasicInfoDTO, String skssqq, String skssqz, ZnsbWfDataVO znsbWfDataVO, String lcslid) {
        // znsbWfDataVO该部分在接口入口controller处定义
//        ZnsbWfDataVO znsbWfDataVO = new ZnsbWfDataVO();
//        znsbWfDataVO.setYwsxdm(YzpzzlEnum.CXS.getDm());
//        znsbWfDataVO.setZzuuid(ZnsbSessionUtils.getZzuuid());
        if (!GyUtils.isNull(companyBasicInfoDTO)) {
            znsbWfDataVO.setJguuid(companyBasicInfoDTO.getJguuid());
            znsbWfDataVO.setJgmc(companyBasicInfoDTO.getJgmc());
        } else {
            throw ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, "未查询到当前企业机构信息!");
        }
        //每次新启动流程创建lcslid流程实例id
        log.info("------lcslid--------{}", lcslid);
        znsbWfDataVO.setLcslid(lcslid);
        znsbWfDataVO.setUfRwzt(YzpzzlEnum.CXS.getMc() + "申报（" + skssqq + "-" + skssqz + "）申请");
        log.info("------ZnsbSessionUtils.getYhUuid()--------{}", ZnsbSessionUtils.getYhUuid());
        znsbWfDataVO.setYhuuid(ZnsbSessionUtils.getYhUuid());
        znsbWfDataVO.setZzuuid(ZnsbSessionUtils.getZzuuid());

//        znsbWfDataVO.setYhuuid("d1af53fe3d4e45008e03222d74a801dc");
//        znsbWfDataVO.setZzuuid("50151bb947034fed83ed74dc1a0a6a4c");

        //获取配置表url
        znsbWfDataVO.setUfFormUrl(String.valueOf(CacheUtils.getTableData("cs_znsb_gzl_shnrurl", YzpzzlEnum.CXS.getDm()).get("pageUrl")));
        //获取公用申报流程url
        znsbWfDataVO.setNextTaskUfFormUrl(String.valueOf(CacheUtils.dm2mc("ZDY_DM_GZL_XYHJURL", "GYSBLC")));
        //传参初始化接口返回时获取到的流程事项代码
        znsbWfDataVO.setLcsxDm(znsbInitProcDataVO.getLcsxDm());
        log.info("------znsbWfDataVO--------{}", znsbWfDataVO);
        CommonResult<ZnsbWfDataResVO> startResult = znsbWfAPI.startOneProc(znsbWfDataVO);
        if (startResult.getCode() != 1) {
            throw ServiceExceptionUtil.exception(500, startResult.getMsg());
        }
    }

    @Override
    public void gzlsaveSbbxx(String lcslid) {
        ZnsbNssbZcxxDTO znsbNssbZcxxDTO = znsbNssbZcxxService.queryZcxxByLcsjid(lcslid);
        CchxwsnssbGzlVO cchxwsnssbGzlVO = JacksonUtils.toObj(znsbNssbZcxxDTO.getBusinessclob(), CchxwsnssbGzlVO.class);
        CxsRequestDTO cxsRequestDTO = cchxwsnssbGzlVO.getCxsRequestDTO();
        final String ywbw = JsonUtils.toJson(cxsRequestDTO);
        log.info("工作流财行税提交申报保存请求:{}", ywbw);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setYwbm(YzpzzlEnum.CXS.getDm());
        sjjhDTO.setYwuuid(cchxwsnssbGzlVO.getSbrwuuid());
        sjjhDTO.setDjxh(cchxwsnssbGzlVO.getDjxh());
        sjjhDTO.setNsrsbh(cchxwsnssbGzlVO.getNsrsbh());
        sjjhDTO.setXzqhszDm(cchxwsnssbGzlVO.getXzqhszDm());
        sjjhDTO.setBwnr(ywbw);
        switch (cchxwsnssbGzlVO.getNssblcbz()) {
            case "zcsb":
                sjjhDTO.setSjjhlxDm("BC00000001");
                break;
            case "cwgz":
                sjjhDTO.setSjjhlxDm("CXS0000002");
                break;
            default:
                break;
        }
        CommonResult<Object> commonResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (commonResult.getCode() != 1) {
            throw ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, commonResult.getMsg());
        } else {
            CxsBcResponseDTO cxsBcResponseDTO = (CxsBcResponseDTO) commonResult.getData();
            if (!"00".equals(cxsBcResponseDTO.getReturncode())) {
                throw ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, commonResult.getMsg());
            }
        }
    }

    @Override
    public void shsbCxsGzlSbbxx(String lcslid) {
        log.info("-----lcslid-----{}", lcslid);
        ZnsbNssbZcxxDTO znsbNssbZcxxDTO = znsbNssbZcxxService.queryZcxxByLcsjid(lcslid);
        log.info("-----znsbNssbZcxxDTO-----{}", znsbNssbZcxxDTO);
        CchxwsnssbGzlVO cchxwsnssbGzlVO = JacksonUtils.toObj(znsbNssbZcxxDTO.getBusinessclob(), CchxwsnssbGzlVO.class);
        switch (cchxwsnssbGzlVO.getNssblcbz()) {
            case "zcsb":
                ZnsbNssbSbrwDTO znsbNssbSbrwDTO = znsbNssbSbrwService.getSbrwBySbrwuuid(znsbNssbZcxxDTO.getSbrwuuid());
                log.info("-----znsbNssbSbrwDTO-----{}", znsbNssbSbrwDTO);
                //修改税源状态
//                List<String> syuuidList = Arrays.asList(znsbNssbSbrwDTO.getSyuuid1().substring(1,znsbNssbSbrwDTO.getSyuuid1().length()-1).split(","));
                List<String> syuuidList = Arrays.asList(znsbNssbSbrwDTO.getSyuuid());
                log.info("-----syuuidList-----{}", syuuidList);
                this.updateSybczt(znsbNssbSbrwDTO.getZsxmDm(), SBZT_SHBTG_DM, syuuidList, znsbNssbZcxxDTO.getSbrwuuid());
                break;
            case "cwgz":
                break;
            default:
                break;
        }
    }

    @SneakyThrows
    public void saveGzlzcb(CchxwsnssbGzlVO cchxwsnssbGzlVO, String lcslid) {
        //保存暂存表
        ZnsbNssbZcxxDTO znsbNssbZcxxDTO = new ZnsbNssbZcxxDTO();
        znsbNssbZcxxDTO.setUuid(GyUtils.getUuid());
        znsbNssbZcxxDTO.setDjxh(cchxwsnssbGzlVO.getDjxh());
        znsbNssbZcxxDTO.setYzpzzlDm(YzpzzlEnum.CXS.getDm());
        znsbNssbZcxxDTO.setSkssqq(DateUtils.parseDate(cchxwsnssbGzlVO.getSkssqq()));
        znsbNssbZcxxDTO.setSkssqz(DateUtils.parseDate(cchxwsnssbGzlVO.getSkssqz()));
        znsbNssbZcxxDTO.setBusinessclob(JsonUtils.toJson(cchxwsnssbGzlVO));
        znsbNssbZcxxDTO.setYxbz("Y");
        znsbNssbZcxxDTO.setSbrwuuid(cchxwsnssbGzlVO.getSbrwuuid());
        znsbNssbZcxxDTO.setLcslid(lcslid);
        znsbNssbZcxxService.saveOrUpdate(znsbNssbZcxxDTO);
    }

    /**
     * 处理减征政策适用主体代码，减征类型为空的情况
     *
     * @param initResMxVO  普惠减免信息
     * @param cxstysbNsrxx 纳税人信息
     */
    private void dealWithJzzcsyztDm(CchxwsnssbInitResMxVO initResMxVO, CxstysbNsrxx cxstysbNsrxx, String djzclxdm) {
        if ("Y".equals(initResMxVO.getBqsfsyzzsxgmnsrjzzc()) && GyUtils.isNull(initResMxVO.getJzzcsyztDm()) &&
                "XWN".equals(initResMxVO.getOldHasPhjmzg()) && "X".equals(initResMxVO.getOldSfxwqy())) {
            //21-小型微利企业(增值税一般纳税人)
            cxstysbNsrxx.setJzzcsyztDm("21");
            initResMxVO.setJzzcsyztDm("21");
            return;
        }
        if ("Y".equals(initResMxVO.getBqsfsyzzsxgmnsrjzzc()) && GyUtils.isNull(initResMxVO.getJzzcsyztDm()) && "171,172,420,421,422,423".contains(djzclxdm)) {
            //11-增值税小规模纳税人
            cxstysbNsrxx.setJzzcsyztDm("11");
            initResMxVO.setJzzcsyztDm("11");
        } else if ("Y".equals(initResMxVO.getBqsfsyzzsxgmnsrjzzc()) && GyUtils.isNull(initResMxVO.getJzzcsyztDm()) && djzclxdm.startsWith("1")) {
            cxstysbNsrxx.setJzzcsyztDm("11");
            initResMxVO.setJzzcsyztDm("11");
        } else if ("Y".equals(initResMxVO.getBqsfsyzzsxgmnsrjzzc()) && GyUtils.isNull(initResMxVO.getJzzcsyztDm()) && djzclxdm.startsWith("4")) {
            //22-个体工商户(增值税一般纳税人)
            cxstysbNsrxx.setJzzcsyztDm("22");
            initResMxVO.setJzzcsyztDm("22");
        } else {
            cxstysbNsrxx.setJzzcsyztDm(initResMxVO.getJzzcsyztDm());
        }
    }

    @SneakyThrows
    private void cxsInitRequsetBW(CxsQuerymxInitDTO cxsQuerymxInitDTO, CxstysbNsrxx cxstysbNsrxx, List<SymxGridlbVO> symxGridlb, CchxwsnssbInitResMxVO initResMxVO, CchxwsnssbquerysbbxxReqVO cchxwsnssbquerysbbxxReqVO) {
        CxstysbNsrxxVO cxstysbNsrxxVO = cchxwsnssbquerysbbxxReqVO.getNsrxxVO();
        // 财行税通用申报初始化
        cxsQuerymxInitDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsQuerymxInitDTO.setSkssqq(DateUtils.parseDate(cxstysbNsrxxVO.getSkssqq(), "yyyy-MM-dd"));
        cxsQuerymxInitDTO.setSkssqz(DateUtils.parseDate(cxstysbNsrxxVO.getSkssqz(), "yyyy-MM-dd"));
        cxsQuerymxInitDTO.setNsqxDm(cxstysbNsrxxVO.getNsqxDm());
        cxsQuerymxInitDTO.setBssfsylslfjmzc(GyUtils.isNotNull(initResMxVO.getBqsfsyzzsxgmnsrjzzc())? initResMxVO.getBqsfsyzzsxgmnsrjzzc() : "N");
        cxsQuerymxInitDTO.setJmsyzt(initResMxVO.getJzzcsyztDm());
        if (GyUtils.isNotNull(initResMxVO.getXgmjzzcqssj())) {
            cxsQuerymxInitDTO.setSyjzzcq(DateUtils.parseDate(initResMxVO.getXgmjzzcqssj(), "yyyy-MM-dd"));
        }
        if (GyUtils.isNotNull(initResMxVO.getXgmjzzcqssj())) {
            cxsQuerymxInitDTO.setSyjzzcz(DateUtils.parseDate(initResMxVO.getXgmjzzczzsj(), "yyyy-MM-dd"));
        }
        if (GyUtils.isNotNull(symxGridlb)) {
            cxsQuerymxInitDTO.setSyuuidList(symxGridlb.stream().map(SymxGridlbVO::getSyuuid).collect(Collectors.toList()));
        }
        cxsQuerymxInitDTO.setGzbz(cchxwsnssbquerysbbxxReqVO.getGzbz());
    }

    private void jcbw(CxsRequestDTO cxsRequestDTO, JbxxmxsjVO jbxxmxsjVO) {
        final GyJbrxxDTO jbrxxDTO = GyJbrxxUtils.getJbrxx(jbxxmxsjVO.getNsrsbh(),jbxxmxsjVO.getDjxh());
        CxsjcbwDTO cxsjcbwDTO = new CxsjcbwDTO();
        cxsjcbwDTO.setJbr(jbrxxDTO.getJbr());
        cxsjcbwDTO.setJbrsfzjhm(jbrxxDTO.getJbrsfzjhm());
        cxsjcbwDTO.setJbrsfzjlxDm(jbrxxDTO.getJbrsfzjlxDm());
        cxsRequestDTO.setJcbw(cxsjcbwDTO);
    }

    @SneakyThrows
    private void ywbw(String zsxmDm, CxsRequestDTO cxsRequestDTO, CxstysbNsrxx cxstysbNsrxx, HXZGSB10736Request.SbxxGrid sbxxGrid, SbCxstysbSlxx sbCxstysbSlxx, String mxid) {
        CxsywbwDTO cxsywbwDTO = new CxsywbwDTO();
        // SXA061041005
        cxsywbwDTO.setSwsxDm("SXA061041005");
        cxsywbwDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsywbwDTO.setSwjgDm(cxstysbNsrxx.getZgswskfjDm().substring(0, 3) + "00000000");
        cxsywbwDTO.setSkssqq(DateUtils.parseDate(cxstysbNsrxx.getSkssqq(), "yyyy-MM-dd"));
        cxsywbwDTO.setSkssqz(DateUtils.parseDate(cxstysbNsrxx.getSkssqz(), "yyyy-MM-dd"));
        cxsywbwDTO.setGzbz("N");
//        cxsywbwDTO.setGzbz("Y");
        cxsywbwDTO.setMxid(mxid);
        // 处理申报数据
        CxsYwbwSbsjDTO cxsYwbwSbsjDTO = this.sbsj(zsxmDm, cxstysbNsrxx, sbxxGrid, sbCxstysbSlxx);
        cxsywbwDTO.setSbsj(Base64Utils.encode(JacksonUtils.toJson(cxsYwbwSbsjDTO)));
        List<CxsywbwDTO> cxsywbwDTOS = new ArrayList<>();
        cxsywbwDTOS.add(cxsywbwDTO);
        cxsRequestDTO.setYwbw(cxsywbwDTOS);
    }

    private String getYzpzzlDmByZsxmDm(String zsxmDm) {
        return YzpzzlEnum.getYzpzzlDmByZsxmDm(zsxmDm,null);
    }


    @SneakyThrows
    private CxsYwbwSbsjDTO sbsj(String zsxmDm, CxstysbNsrxx cxstysbNsrxx, HXZGSB10736Request.SbxxGrid sbxxGrid, SbCxstysbSlxx sbCxstysbSlxx) {
        CxsYwbwSbsjDTO cxsYwbwSbsjDTO = new CxsYwbwSbsjDTO();
        //保存财行税通用申报业务报文
        CxstysbnsrxxDTO cxstysbnsrxxDTO = BeanUtil.copyProperties(cxstysbNsrxx, CxstysbnsrxxDTO.class);
        //测试使用
        cxstysbnsrxxDTO.setSkssqq(GyUtils.isNotNull(cxstysbNsrxx.getSkssqq()) ? DateUtils.parseDate(cxstysbNsrxx.getSkssqq(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setSkssqz(GyUtils.isNotNull(cxstysbNsrxx.getSkssqz()) ? DateUtils.parseDate(cxstysbNsrxx.getSkssqz(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setSbrq1(GyUtils.isNotNull(cxstysbNsrxx.getSbrq1()) ? DateUtils.parseDate(cxstysbNsrxx.getSbrq1(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setXgmjzzcqssj(GyUtils.isNotNull(cxstysbNsrxx.getXgmjzzcqssj()) ? DateUtils.parseDate(cxstysbNsrxx.getXgmjzzcqssj(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setXgmjzzczzsj(GyUtils.isNotNull(cxstysbNsrxx.getXgmjzzczzsj()) ? DateUtils.parseDate(cxstysbNsrxx.getXgmjzzczzsj(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setYzpzzlDm(YzpzzlEnum.CXS.getDm());
        if (GyUtils.isNull(cxstysbnsrxxDTO.getBqsfsyzzsxgmnsrjzzc()) || !"Y".equals(cxstysbnsrxxDTO.getBqsfsyzzsxgmnsrjzzc())){
            cxstysbnsrxxDTO.setBqsfsyzzsxgmnsrjzzc("N");
//            cxstysbnsrxxDTO.setXgmjzzcqssj(DateUtils.parseDate("2024-10-18", "yyyy-MM-dd"));
//            cxstysbnsrxxDTO.setXgmjzzczzsj(DateUtils.parseDate("2024-10-18", "yyyy-MM-dd"));
//            cxstysbnsrxxDTO.setJzzcsyztDm("11");
        }
//        cxstysbnsrxxDTO.setScenceCs("zcsb");
//        cxstysbnsrxxDTO.setScenceCs("cwgzbz");
        // 保存税种列表
        CxssbxxGridDTO cxssbxxGridDTO = new CxssbxxGridDTO();
        List<CxssbxxGridlbDTO> sbxxGridlb = sbxxGrid.getSbxxGridlb().stream().map(e -> {
            CxssbxxGridlbDTO cxssbxxGridlbDTO = new CxssbxxGridlbDTO();
            cxssbxxGridlbDTO.setZsxmDm(e.getZsxmDm());
            String yzpzzlDm = getYzpzzlDmByZsxmDm(e.getZsxmDm());
//            if (YHS.getCode().equals(e.getZsxmDm())){
//                yzpzzlDm = YzpzzlEnum.YHS.getDm();
//            } else if (FCS.getCode().equals(e.getZsxmDm()) || CZTDSYS.getCode().equals(e.getZsxmDm())) {
//                yzpzzlDm = YzpzzlEnum.CZTD_FCS.getDm();
//            }
            cxssbxxGridlbDTO.setYzpzzlDm(yzpzzlDm);
//            cxssbxxGridlbDTO.setPzxh("10014424000000005476");
//            cxssbxxGridlbDTO.setSbuuid("54FCCCCFEF56A0E6F4599983D1DBFDD0");
//            cxssbxxGridlbDTO.setSkssqq(DateUtil.parse("2024-07-23","yyyy-MM-dd"));
//            cxssbxxGridlbDTO.setSkssqz(DateUtil.parse("2024-07-23","yyyy-MM-dd"));

            List<CxssymxDTO> cxssymxDTOS = BeanUtil.copyToList(e.getSymxGrid().getSymxGridlb(), CxssymxDTO.class);
            CxssymxGridDTO cxssymxGridDTO = new CxssymxGridDTO();
            cxssymxGridDTO.setSymxGridlb(cxssymxDTOS);
            cxssbxxGridlbDTO.setSymxGrid(cxssymxGridDTO);
            return cxssbxxGridlbDTO;
        }).collect(Collectors.toList());
        cxssbxxGridDTO.setSbxxGridlb(sbxxGridlb);
        // 受理信息
        CxstysbslxxDTO cxstysbslxxDTO = BeanUtil.copyProperties(sbCxstysbSlxx, CxstysbslxxDTO.class);
        cxstysbslxxDTO.setSlrq(GyUtils.isNotNull(sbCxstysbSlxx.getSlrq()) ? DateUtils.parseDate(sbCxstysbSlxx.getSlrq(), "yyyy-MM-dd") : null);
        cxsYwbwSbsjDTO.setCxstysbnsrxx(cxstysbnsrxxDTO);
        cxsYwbwSbsjDTO.setSbxxGrid(cxssbxxGridDTO);
        cxsYwbwSbsjDTO.setCxstysbslxx(cxstysbslxxDTO);
        return cxsYwbwSbsjDTO;
    }

    private void ssjg(CxsRequestDTO cxsRequestDTO, CxstysbNsrxx cxstysbNsrxx, String zsxmDm, String syuuid1, String mxid) {
        CxsssjgDTO cxsssjgDTO = new CxsssjgDTO();
        String syuuid = Arrays.asList(syuuid1.substring(1, syuuid1.length() - 1).split(",")).get(0);
        switch (zsxmDm) {
            case "10110":
                cchxwsnssbService.fcsssjg(cxsssjgDTO, null, null, cxstysbNsrxx, syuuid,mxid);
                break;
            case "10111":
                cchxwsnssbService.yhsssjg(cxsssjgDTO, null, null, cxstysbNsrxx,mxid);
                break;
            case "10112":
                cchxwsnssbService.cztdsysssjg(cxsssjgDTO, null, null, cxstysbNsrxx, syuuid,mxid);
                break;
            default:
                log.error("---ssjg----zsxmDm为空--");
                break;
        }
        cxsRequestDTO.setSsjg(cxsssjgDTO);
    }

    //保存财行税入参印花税算税结果
    @Override
    public void yhsssjg(CxsssjgDTO cxsssjgDTO, BigDecimal pzxh, String sbuuid, CxstysbNsrxx cxstysbNsrxx, String mxid) {
        Date skssqq = GyUtils.cast2Date(cxstysbNsrxx.getSkssqq());
        Date skssqz = GyUtils.cast2Date(cxstysbNsrxx.getSkssqz());
        String djxh = cxstysbNsrxx.getDjxh();

        List<Map<String, Object>> ssjgMapList = this.cxSssjg(YHS.getCode(),djxh, skssqq, skssqz, "", "");
        if (GyUtils.isNull(ssjgMapList)) {
            return;
        }
        List<YhsssjgxxDTO> yhsssjgxxDTOS = BeanUtils.toBean(ssjgMapList, YhsssjgxxDTO.class);

        // 处理 yhsssjgxxDTOS 中的 BigDecimal 字段，保留小数点后6位
        for (YhsssjgxxDTO dto : yhsssjgxxDTOS) {
            if (dto.getSl1() != null) {
                dto.setSl1(dto.getSl1().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
            if (dto.getSsjezj() != null) {
                dto.setSsjezj(dto.getSsjezj().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
            if (dto.getSsjejs() != null) {
                dto.setSsjejs(dto.getSsjejs().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
            if (dto.getSsjeye() != null) {
                dto.setSsjeye(dto.getSsjeye().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
        }
        CxsYhsssjgDTO cxsYhsssjgDTO = new CxsYhsssjgDTO();
        if (GyUtils.isNotNull(pzxh)) {
            cxsYhsssjgDTO.setPzxh(pzxh);
        }
        if (GyUtils.isNotNull(sbuuid)) {
            cxsYhsssjgDTO.setSbuuid(sbuuid);
        }
        cxsYhsssjgDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsYhsssjgDTO.setSkssqq(GyUtils.cast2Date(cxstysbNsrxx.getSkssqq()));
        cxsYhsssjgDTO.setSkssqz(GyUtils.cast2Date(cxstysbNsrxx.getSkssqz()));
        cxsYhsssjgDTO.setMxid(mxid);
        //还未给算税接口，待完善
        cxsYhsssjgDTO.setYhsssjgxxGrid(yhsssjgxxDTOS);
//        cxsYhsssjgDTO.setSsgcpclsh();
//        cxsYhsssjgDTO.setFbzlList();
        cxsssjgDTO.setYhsssjg(cxsYhsssjgDTO);
    }

    //保存财行税入参房产算税结果处理
    @Override
    public void fcsssjg(CxsssjgDTO cxsssjgDTO, BigDecimal pzxh, String sbuuid, CxstysbNsrxx cxstysbNsrxx, String syuuid, String mxid) {
        Date skssqq = GyUtils.cast2Date(cxstysbNsrxx.getSkssqq());
        Date skssqz = GyUtils.cast2Date(cxstysbNsrxx.getSkssqz());
        Date newtime = new Date();
        List<Map<String, Object>> ssjgMapList = this.cxSssjg(FCS.getCode(),cxstysbNsrxx.getDjxh(), skssqq, skssqz, syuuid,cxstysbNsrxx.getZspmDm());
        if (GyUtils.isNull(ssjgMapList)) {
            return;
        }
        List<FcsssjgxxDTO> fcsssjgxxDTOS = BeanUtils.toBean(ssjgMapList, FcsssjgxxDTO.class);

        // 处理 fcsssjgxxDTOS 中的 BigDecimal 字段，保留小数点后6位
        for (FcsssjgxxDTO dto : fcsssjgxxDTOS) {
            if (dto.getSsjezj() != null) {
                dto.setSsjezj(dto.getSsjezj().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
            if (dto.getSsjejs() != null) {
                dto.setSsjejs(dto.getSsjejs().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
            if (dto.getSsjeye() != null) {
                dto.setSsjeye(dto.getSsjeye().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
        }
        //查询房产税从租表，如果有数据则为从租否则为从价
        LambdaQueryWrapperX<ZnsbCxsFyxxcjb> fcQw = new LambdaQueryWrapperX<>();
        fcQw.eq(ZnsbCxsFyxxcjb::getFyxxuuid,syuuid);
        final List<CzjzsymxbGridlbVO> czjzsymxbGridlbVOList = sbCxsFtdcjglbMapper.queryCzList(syuuid);
        final boolean czbz = GyUtils.isNotNull(czjzsymxbGridlbVOList);
        for(FcsssjgxxDTO fcsssjgxx : fcsssjgxxDTOS){
            fcsssjgxx.setYwfssj(newtime);
            fcsssjgxx.setHyDm(cxstysbNsrxx.getHyDm());
            fcsssjgxx.setDjzclxDm(cxstysbNsrxx.getDjzclxDm());
            fcsssjgxx.setKzztlxDm(cxstysbNsrxx.getKzztlxDm());
            //1、当算税规则为‘从价计税’时，原始凭证类型为：BLPZ034,业务行为分类为：BL035
            //2、当算税规则为‘从租计税’时，原始凭证类型为：BLPZ035,业务行为分类为：BL036
            if (czbz){
                fcsssjgxx.setYspzlxDm("BLPZ035");
                fcsssjgxx.setYwxwflDm("BL036");
            }else {
                fcsssjgxx.setYspzlxDm("BLPZ034");
                fcsssjgxx.setYwxwflDm("BL035");
            }
        }
        CxsFcsssjgDTO cxsFcsssjgDTO = new CxsFcsssjgDTO();
        if (GyUtils.isNotNull(pzxh)) {
            cxsFcsssjgDTO.setPzxh(pzxh);
        }
        if (GyUtils.isNotNull(sbuuid)) {
            cxsFcsssjgDTO.setSbuuid(sbuuid);
        }
        cxsFcsssjgDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsFcsssjgDTO.setSkssqq(GyUtils.cast2Date(cxstysbNsrxx.getSkssqq()));
        cxsFcsssjgDTO.setSkssqz(GyUtils.cast2Date(cxstysbNsrxx.getSkssqz()));
        cxsFcsssjgDTO.setMxid(mxid);
        //还未给算税接口，待完善
        cxsFcsssjgDTO.setFcsssjgxxGrid(fcsssjgxxDTOS);
//        cxsYhsssjgDTO.setSsgcpclsh();
//        cxsYhsssjgDTO.setFbzlList();
        cxsssjgDTO.setFcsssjg(cxsFcsssjgDTO);
    }

    //保存财行税入参城镇土地算税结果处理
    @Override
    public void cztdsysssjg(CxsssjgDTO cxsssjgDTO, BigDecimal pzxh, String sbuuid, CxstysbNsrxx cxstysbNsrxx, String syuuid, String mxid) {
        Date skssqq = GyUtils.cast2Date(cxstysbNsrxx.getSkssqq());
        Date skssqz = GyUtils.cast2Date(cxstysbNsrxx.getSkssqz());
        List<Map<String, Object>> ssjgMapList = this.cxSssjg(CZTDSYS.getCode(),cxstysbNsrxx.getDjxh(), skssqq, skssqz, syuuid, "");
        if (GyUtils.isNull(ssjgMapList)) {
            return;
        }
        List<CztdsysssjgxxDTO> cztdsysssjgxxDTOS = BeanUtils.toBean(ssjgMapList, CztdsysssjgxxDTO.class);

        // 处理 cztdsysssjgxxDTOS 中的 BigDecimal 字段，保留小数点后6位
        for (CztdsysssjgxxDTO dto : cztdsysssjgxxDTOS) {
            if (dto.getSsjezj() != null) {
                dto.setSsjezj(dto.getSsjezj().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
            if (dto.getSsjejs() != null) {
                dto.setSsjejs(dto.getSsjejs().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
            if (dto.getSsjeye() != null) {
                dto.setSsjeye(dto.getSsjeye().setScale(6, BigDecimal.ROUND_HALF_UP));
            }
        }
        CxsCztdssjgDTO cxsCztdssjgDTO = new CxsCztdssjgDTO();
        if (GyUtils.isNotNull(pzxh)) {
            cxsCztdssjgDTO.setPzxh(pzxh);
        }
        if (GyUtils.isNotNull(sbuuid)) {
            cxsCztdssjgDTO.setSbuuid(sbuuid);
        }
        cxsCztdssjgDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsCztdssjgDTO.setSkssqq(GyUtils.cast2Date(cxstysbNsrxx.getSkssqq()));
        cxsCztdssjgDTO.setSkssqz(GyUtils.cast2Date(cxstysbNsrxx.getSkssqz()));
        cxsCztdssjgDTO.setMxid(mxid);
        //还未给算税接口，待完善
        cxsCztdssjgDTO.setCztdsysssjgxxGrid(cztdsysssjgxxDTOS);
//        cxsYhsssjgDTO.setSsgcpclsh();
//        cxsYhsssjgDTO.setFbzlList();
        cxsssjgDTO.setCztdsysssjg(cxsCztdssjgDTO);
    }

    public List<Map<String, Object>> cxSssjg(String zsxmDm, String djxh, Date skssqq, Date skssqz, String syuuid, String zspmDm) {
        List<Map<String, Object>> resMapList = new ArrayList<>();
        SsjgReqDTO ssjgReqDTO = new SsjgReqDTO();
        ssjgReqDTO.setDjxh(djxh);
        ssjgReqDTO.setZsxmDm(zsxmDm);
        ssjgReqDTO.setSkssqq(skssqq);
        ssjgReqDTO.setSkssqz(skssqz);
        ssjgReqDTO.setSyuuid(syuuid);
        ssjgReqDTO.setZspmDm(zspmDm);
        CommonResult<List<Map<String, Object>>> commonResult = ssjgApi.cxSssjg(ssjgReqDTO);
        if (GyUtils.isNotNull(commonResult)){
            resMapList = commonResult.getData();
        }
        return resMapList;
    }

    @Override
    @DSTransactional
    public List<Map<String, Object>> cxSssjg(SsjgReqDTO ssjgReqDTO) {
        List<Map<String, Object>> resMapList = new ArrayList<>();
        if (YHS.getCode().equals(ssjgReqDTO.getZsxmDm())){
            List<YhsssjgxxDTO> yhsssjgxxDTOList = cxsSsjgMapper.yhsSsjgList(ssjgReqDTO.getDjxh(), ssjgReqDTO.getSkssqq(),
                    ssjgReqDTO.getSkssqz());
            resMapList = processYhsssjgxxDTOList(yhsssjgxxDTOList);
        }
        if (FCS.getCode().equals(ssjgReqDTO.getZsxmDm())){
            List<FcsssjgxxDTO> fcsssjgxxDTOList = cxsSsjgMapper.fcsSsjgList(ssjgReqDTO.getDjxh(), ssjgReqDTO.getSkssqq(),
                    ssjgReqDTO.getSkssqz(), ssjgReqDTO.getSyuuid(),ssjgReqDTO.getZspmDm());
            resMapList = processFcsssjgxxDTOList(fcsssjgxxDTOList);
        }
        if (CZTDSYS.getCode().equals(ssjgReqDTO.getZsxmDm())){
            List<CztdsysssjgxxDTO> cztdsysssjgxxDTOList = cxsSsjgMapper.cztdsysSsjgList(ssjgReqDTO.getDjxh(),
                    ssjgReqDTO.getSkssqq(), ssjgReqDTO.getSkssqz(), ssjgReqDTO.getSyuuid());
            resMapList = processCztdsysssjgxxDTOList(cztdsysssjgxxDTOList);
        }

        return resMapList;
    }

    public List<Map<String, Object>> processYhsssjgxxDTOList(List<YhsssjgxxDTO> yhsssjgxxDTOList) {
        // 遍历 yhsssjgxxDTOList，对 BigDecimal 字段进行格式化
        List<Map<String, Object>> resMapList = yhsssjgxxDTOList.stream()
                .map(dto -> {
                    Map<String, Object> map = BeanUtils.toMap(dto);
                    // 处理 ssjezj 字段
                    if (map.containsKey("ssjezj") && map.get("ssjezj") instanceof BigDecimal) {
                        BigDecimal ssjezj = (BigDecimal) map.get("ssjezj");
                        // 设置小数位最多为 6 位
                        ssjezj = ssjezj.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjezj", ssjezj);
                    }
                    // 处理 ssjejs 字段
                    if (map.containsKey("ssjejs") && map.get("ssjejs") instanceof BigDecimal) {
                        BigDecimal ssjejs = (BigDecimal) map.get("ssjejs");
                        // 设置小数位最多为 6 位
                        ssjejs = ssjejs.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjejs", ssjejs);
                    }
                    // 处理 ssjeye 字段
                    if (map.containsKey("ssjeye") && map.get("ssjeye") instanceof BigDecimal) {
                        BigDecimal ssjeye = (BigDecimal) map.get("ssjeye");
                        // 设置小数位最多为 6 位
                        ssjeye = ssjeye.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjeye", ssjeye);
                    }
                    return map;
                })
                .collect(Collectors.toList());

        return resMapList;
    }

    public List<Map<String, Object>> processFcsssjgxxDTOList(List<FcsssjgxxDTO> fcsssjgxxDTOList) {
        // 遍历 fcsssjgxxDTOList，对 BigDecimal 字段进行格式化
        List<Map<String, Object>> resMapList = fcsssjgxxDTOList.stream()
                .map(dto -> {
                    Map<String, Object> map = BeanUtils.toMap(dto);
                    // 处理 ssjezj 字段
                    if (map.containsKey("ssjezj") && map.get("ssjezj") instanceof BigDecimal) {
                        BigDecimal ssjezj = (BigDecimal) map.get("ssjezj");
                        // 设置小数位最多为 6 位
                        ssjezj = ssjezj.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjezj", ssjezj);
                    }
                    // 处理 ssjejs 字段
                    if (map.containsKey("ssjejs") && map.get("ssjejs") instanceof BigDecimal) {
                        BigDecimal ssjejs = (BigDecimal) map.get("ssjejs");
                        // 设置小数位最多为 6 位
                        ssjejs = ssjejs.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjejs", ssjejs);
                    }
                    // 处理 ssjeye 字段
                    if (map.containsKey("ssjeye") && map.get("ssjeye") instanceof BigDecimal) {
                        BigDecimal ssjeye = (BigDecimal) map.get("ssjeye");
                        // 设置小数位最多为 6 位
                        ssjeye = ssjeye.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjeye", ssjeye);
                    }
                    return map;
                })
                .collect(Collectors.toList());

        return resMapList;
    }

    public List<Map<String, Object>> processCztdsysssjgxxDTOList(List<CztdsysssjgxxDTO> cztdsysssjgxxDTOList) {
        // 遍历 cztdsysssjgxxDTOList，对 BigDecimal 字段进行格式化
        List<Map<String, Object>> resMapList = cztdsysssjgxxDTOList.stream()
                .map(dto -> {
                    Map<String, Object> map = BeanUtils.toMap(dto);
                    // 处理 ssjezj 字段
                    if (map.containsKey("ssjezj") && map.get("ssjezj") instanceof BigDecimal) {
                        BigDecimal ssjezj = (BigDecimal) map.get("ssjezj");
                        // 设置小数位最多为 6 位
                        ssjezj = ssjezj.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjezj", ssjezj);
                    }
                    // 处理 ssjejs 字段
                    if (map.containsKey("ssjejs") && map.get("ssjejs") instanceof BigDecimal) {
                        BigDecimal ssjejs = (BigDecimal) map.get("ssjejs");
                        // 设置小数位最多为 6 位
                        ssjejs = ssjejs.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjejs", ssjejs);
                    }
                    // 处理 ssjeye 字段
                    if (map.containsKey("ssjeye") && map.get("ssjeye") instanceof BigDecimal) {
                        BigDecimal ssjeye = (BigDecimal) map.get("ssjeye");
                        // 设置小数位最多为 6 位
                        ssjeye = ssjeye.setScale(6, BigDecimal.ROUND_HALF_UP);
                        map.put("ssjeye", ssjeye);
                    }
                    return map;
                })
                .collect(Collectors.toList());

        return resMapList;
    }


    private void cxsSaveRequsetBW(String zsxmDm, CxsRequestDTO cxsRequestDTO, CxstysbNsrxx cxstysbNsrxx, HXZGSB10736Request.SbxxGrid sbxxGrid, SbCxstysbSlxx sbCxstysbSlxx, ZnsbNssbSbrwDO znsbNssbSbrwDO, JbxxmxsjVO jbxxmxsjVO) {
        cxsRequestDTO.setRequestId(GyUtils.getUuid());
        if (GyUtils.isNotNull(znsbNssbSbrwDO.getPclsh())) {
            cxsRequestDTO.setPclsh(znsbNssbSbrwDO.getPclsh());
        }
        final String mxid = GyUtils.getUuid();
        //jcbw基础报文处理
        jcbw(cxsRequestDTO, jbxxmxsjVO);
        //ywbw业务报文处理
        ywbw(zsxmDm, cxsRequestDTO, cxstysbNsrxx, sbxxGrid, sbCxstysbSlxx,mxid);
        //ssjg业务报文处理
        ssjg(cxsRequestDTO, cxstysbNsrxx, zsxmDm, znsbNssbSbrwDO.getSyuuid1(),mxid);
    }

    @SneakyThrows
    public void saveCxsGzl(String zsxmDm, CxstysbNsrxx cxstysbNsrxx, SbCxstysbSlxx sbCxstysbSlxx, List<SymxGridlb10736VO> symxGridlb, CchxwsnssbGzlVO cchxwsnssbGzlVO, ZnsbNssbSbrwDO znsbNssbSbrwDO, JbxxmxsjVO jbxxmxsjVO) {
        //核心申报接口入参
        final HXZGSB10736Request hxzgsb10736Request = new HXZGSB10736Request();
        final HXZGSB10736Request.SbxxGrid sbxxGrid = new HXZGSB10736Request.SbxxGrid();
        final SbxxGridlb10736VO sbxxGridlb10736VO = new SbxxGridlb10736VO();
        final SbxxGridlb10736VO.SymxGrid symxGrid = new SbxxGridlb10736VO.SymxGrid();
        symxGrid.getSymxGridlb().addAll(symxGridlb);
        sbxxGridlb10736VO.setZsxmDm(zsxmDm);
        sbxxGridlb10736VO.setSymxGrid(symxGrid);
        sbxxGrid.getSbxxGridlb().add(sbxxGridlb10736VO);
        //2023.10.18 如果不为Y则清空信息
        if (!"Y".equals(cxstysbNsrxx.getBqsfsyzzsxgmnsrjzzc())) {
            cxstysbNsrxx.setJzzcsyztDm("");
            cxstysbNsrxx.setXgmjzzcqssj("");
            cxstysbNsrxx.setXgmjzzczzsj("");
        }
        hxzgsb10736Request.setCxstysbnsrxx(cxstysbNsrxx);
        hxzgsb10736Request.setSbxxGrid(sbxxGrid);
        hxzgsb10736Request.setCxstysbslxx(sbCxstysbSlxx);
        // 报文格式转换
        CxsRequestDTO cxsRequestDTO = new CxsRequestDTO();
        this.cxsSaveRequsetBW(zsxmDm, cxsRequestDTO, cxstysbNsrxx, sbxxGrid, sbCxstysbSlxx, znsbNssbSbrwDO, jbxxmxsjVO);
        cchxwsnssbGzlVO.setCxsRequestDTO(cxsRequestDTO);
        //正常申报
        cchxwsnssbGzlVO.setNssblcbz("zcsb");
        log.info("----cchxwsnssbGzlVO------{}", cchxwsnssbGzlVO);
    }


    /**
     * 核心财行税申报保存接口
     *
     * @param zsxmDm                  征收项目
     * @param cxstysbNsrxx            纳税人信息
     * @param sbCxstysbSlxx           受理信息
     * @param symxGridlb              税源明细
     * @param sjjg                    数据机关
     * @param cwtipsList              错误提示list
     * @param cxstysbSaveReturnVOList 保存信息返回值
     * @param jycfReturn              简易处罚返回值
     * @param filterYqsbReturn        逾期申报mongo标志
     * @param mxVO                    应申报税源信息
     * @return 返回值
     */
    @SneakyThrows
    public boolean saveCxstysbHX(String zsxmDm, CxstysbNsrxx cxstysbNsrxx, SbCxstysbSlxx sbCxstysbSlxx, List<SymxGridlb10736VO> symxGridlb, String sjjg,
                                 List<ResVO> cwtipsList, List<CxstysbSaveReturnVO> cxstysbSaveReturnVOList, Map<String, String> jycfReturn,
                                 StringBuffer filterYqsbReturn, CchxwsnssbInitResMxVO mxVO, String sbrwuuid, ZnsbNssbSbrwDO znsbNssbSbrwDO, JbxxmxsjVO jbxxmxsjVO) {
        //核心申报接口入参
        final HXZGSB10736Request hxzgsb10736Request = new HXZGSB10736Request();
        final HXZGSB10736Request.SbxxGrid sbxxGrid = new HXZGSB10736Request.SbxxGrid();
        final SbxxGridlb10736VO sbxxGridlb10736VO = new SbxxGridlb10736VO();
        final SbxxGridlb10736VO.SymxGrid symxGrid = new SbxxGridlb10736VO.SymxGrid();
        symxGrid.getSymxGridlb().addAll(symxGridlb);
        sbxxGridlb10736VO.setZsxmDm(zsxmDm);
        sbxxGridlb10736VO.setSymxGrid(symxGrid);
        sbxxGrid.getSbxxGridlb().add(sbxxGridlb10736VO);
        //2023.10.18 如果不为Y则清空信息
        if (!"Y".equals(cxstysbNsrxx.getBqsfsyzzsxgmnsrjzzc())) {
            cxstysbNsrxx.setJzzcsyztDm("");
            cxstysbNsrxx.setXgmjzzcqssj("");
            cxstysbNsrxx.setXgmjzzczzsj("");
        }
        hxzgsb10736Request.setCxstysbnsrxx(cxstysbNsrxx);
        hxzgsb10736Request.setSbxxGrid(sbxxGrid);
        hxzgsb10736Request.setCxstysbslxx(sbCxstysbSlxx);
        // 报文格式转换
        CxsRequestDTO cxsRequestDTO = new CxsRequestDTO();
        this.cxsSaveRequsetBW(zsxmDm, cxsRequestDTO, cxstysbNsrxx, sbxxGrid, sbCxstysbSlxx, znsbNssbSbrwDO, jbxxmxsjVO);

        // 判断是否为演示环境
        String csz = CacheUtils.getXtcs("TBDG-YS", "N");
        log.info("特变电工演示环境参数值：{}", csz);
        if ("Y".equals(csz)){
            //调用使用本地税源信息保存申报表和保存应缴税费信息接口
            this.yshjSb(znsbNssbSbrwDO);

            List<CxstysbSaveReturnVO> cxstysbSaveReturnVOS = new ArrayList<>();
            CxstysbSaveReturnVO cxstysbSaveReturnVO = new CxstysbSaveReturnVO();
            cxstysbSaveReturnVO.setZsxmDm(zsxmDm);
            cxstysbSaveReturnVOS.add(cxstysbSaveReturnVO);
            cxstysbSaveReturnVOList.addAll(cxstysbSaveReturnVOS);
            return true;
        }

        final String ywbw = JsonUtils.toJson(cxsRequestDTO);
        log.info("财行税提交申报保存请求:{}", ywbw);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("BC00000001");
        sjjhDTO.setYwbm(YzpzzlEnum.CXS.getDm());
        sjjhDTO.setYwuuid(sbrwuuid);
        sjjhDTO.setDjxh(cxstysbNsrxx.getDjxh());
        sjjhDTO.setNsrsbh(cxstysbNsrxx.getNsrsbh());
        sjjhDTO.setXzqhszDm(cxstysbNsrxx.getXzqhszDm());
        sjjhDTO.setBwnr(ywbw);
        CommonResult<Object> commonResult = sjjhService.saveSjjhJob(sjjhDTO);
        log.info("财行税提交申报保存返回报文:{}", commonResult);
        final ResVO cwtipsResVo = new ResVO();
        if (commonResult.getCode() != 1) {
            cwtipsResVo.setReturnCode(zsxmDm);
            cwtipsResVo.setReturnMsg("调用代理服务失败");
            cwtipsList.add(cwtipsResVo);
            return false;
        } else {
            CxsBcResponseDTO cxsBcResponseDTO = (CxsBcResponseDTO) commonResult.getData();
            if (!"00".equals(cxsBcResponseDTO.getReturncode())) {
                cwtipsResVo.setReturnCode(zsxmDm);
                cwtipsResVo.setReturnMsg("调用乐企接口失败");
                cwtipsList.add(cwtipsResVo);
                return false;
            }
        }
        List<CxstysbSaveReturnVO> cxstysbSaveReturnVOS = new ArrayList<>();
        CxstysbSaveReturnVO cxstysbSaveReturnVO = new CxstysbSaveReturnVO();
        cxstysbSaveReturnVO.setZsxmDm(zsxmDm);
        cxstysbSaveReturnVOS.add(cxstysbSaveReturnVO);
        cxstysbSaveReturnVOList.addAll(cxstysbSaveReturnVOS);
        return true;
    }


    /**
     * 演示环境申报
     * @param znsbNssbSbrwDO
     */
    private void yshjSb(ZnsbNssbSbrwDO znsbNssbSbrwDO) {
        znsbNssbSbrwDO.setPzxh(getYzpzxh());
        log.info("pzxh:{}",znsbNssbSbrwDO.getPzxh());
        znsbNssbSbrwDO.setSbuuid(GyUtils.getUuid());
        //调用保存申报表接口
        if(YHS.getCode().equals(znsbNssbSbrwDO.getZsxmDm())){
            yhssycjNewService.yhsBcTysbbBySyuuid(znsbNssbSbrwDO);
        } else if (CZTDSYS.getCode().equals(znsbNssbSbrwDO.getZsxmDm())) {
            fcsCztdsysService.ftsSbxxSave(znsbNssbSbrwDO);
        } else if (FCS.getCode().equals(znsbNssbSbrwDO.getZsxmDm())) {
            fcsCztdsysService.ftsSbxxSave(znsbNssbSbrwDO);
        }

    }

    private String getYzpzxh() {
        UUID uuid = UUID.randomUUID();
        long significantBits = Math.abs(uuid.getMostSignificantBits()); // 取绝对值
        long leastSignificantBits = Math.abs(uuid.getLeastSignificantBits()); // 取绝对值
        String id = significantBits + String.valueOf(leastSignificantBits);
        return id.substring(0, 20);
    }

    /**
     * 查询应交税费核心接口
     *
     * @param djxh         登记序号
     * @param sjjgSet      税务机关set - 自然人使用
     * @param swjgDm       税务机关 - 纳税人使用
     * @param stringList   pzxh合集
     * @param cwtipsList   错误信息
     * @param jfxxmcVOList 缴费信息
     */
    private void queryYjsfHX(String djxh, Set<String> sjjgSet, String swjgDm, List<String> stringList, List<ResVO> cwtipsList, List<CchxwsnssbjfxxVO> jfxxmcVOList) {
        final HXZGZS00377Request hxzgzs00377Request = new HXZGZS00377Request();
        hxzgzs00377Request.setDjxh(djxh);
        final List<CchxwsnssbjfxxVO> jfxxVOList = new ArrayList<>();
        //根据机关循环调用
        if (GyUtils.isNull(sjjgSet)) {
            sjjgSet.add(swjgDm);
        }
        //自然人循环调用，纳税人调用一次
        for (String sjjg : sjjgSet) {
            final Map<String, String> expand = new HashMap<>();
            expand.put("sjjg", sjjg);
            HXZGZS00377Response hxzgzs00377Response = null;
            try {
                hxzgzs00377Response = Gt3Invoker.invoke("SWZJ.HXZG.ZS.CXNSRWQJQSFXX", expand, hxzgzs00377Request, HXZGZS00377Response.class);
//                hxzgzs00377Response = JacksonUtils.toObj(DbUtils.getData("SWZJ.HXZG.ZS.CXNSRWQJQSFXX"),HXZGZS00377Response.class);
            } catch (Exception e) {
                log.info("hhhhhhhhhhhhhhhhhhhhhinit" + FcsCztdsyssycjUtils.getTrace(e));
                final String message = e.getMessage();
                final ResVO cwtipsResVo = new ResVO();
                cwtipsResVo.setReturnCode("cxjfxx_code");
                cwtipsResVo.setReturnMsg(sjjg + "+" + message);
                cwtipsList.add(cwtipsResVo);
            }
            if (hxzgzs00377Response != null && !GyUtils.isNull(hxzgzs00377Response.getNsrwqjxxGrid()) && !GyUtils.isNull(hxzgzs00377Response.getNsrwqjxxGrid().getNsrwqjxxGridlb())) {
                final List<CchxwsnssbjfxxVO> tempList = BeanUtil.copyToList(hxzgzs00377Response.getNsrwqjxxGrid().getNsrwqjxxGridlb(), CchxwsnssbjfxxVO.class);
                jfxxVOList.addAll(tempList);
            }
        }
        //过滤数据
        final List<CchxwsnssbjfxxVO> jfxx = jfxxVOList.stream().filter(f -> stringList.contains(f.getYzpzxh())).collect(Collectors.toList());
        if (!GyUtils.isNull(jfxx)) {
            //填充名称
            jfxxmcVOList.addAll(CchxwsnssbGyUtils.getZsxmmcJfxx(jfxx));
        }
    }

    /**
     * 处理应缴税费信息
     *
     * @param jfxxmcVOList            应缴税费信息
     * @param cxstysbSaveReturnVOList 保存接口返回信息
     * @param cchxwsnssbzbmxVOList    主表明细信息
     */
    private void dealWithYjsf(List<CchxwsnssbjfxxVO> jfxxmcVOList, List<CxstysbSaveReturnVO> cxstysbSaveReturnVOList, List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList, List<CchxwsnssbjfxxVO> sfxxAllVOList) {
        log.info("dealWithYjsf_dateA1_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        //以征收项目分组-应缴税费信息
        Map<String, List<CchxwsnssbjfxxVO>> yjsfZxsmMap = new HashMap<>();
        Map<String, List<CchxwsnssbjfxxVO>> yjsfZxsmSkQzMapTemp = new HashMap<>();
        Set<String> yjsfPzxhSet = new HashSet<>();
        if (!GyUtils.isNull(jfxxmcVOList)) {
            yjsfZxsmMap = jfxxmcVOList.stream().collect(Collectors.groupingBy(CchxwsnssbjfxxVO::getZsxmDm));
            yjsfZxsmSkQzMapTemp = jfxxmcVOList.stream()
                    .filter(f -> !GyUtils.isNull(f.getZsxmDm()) && !GyUtils.isNull(f.getSkssqq()) && !GyUtils.isNull(f.getSkssqz()))
                    .map(m -> {
                        m.setSkssqq(com.css.znsb.nssb.util.DateUtil.dateAutoFormatList(m.getSkssqq(), "yyyy-MM-dd"));
                        m.setSkssqz(com.css.znsb.nssb.util.DateUtil.dateAutoFormatList(m.getSkssqz(), "yyyy-MM-dd"));
                        return m;
                    })
                    .collect(Collectors.groupingBy(g -> g.getZsxmDm() + "-" + g.getSkssqq() + "-" + g.getSkssqz()));
            yjsfPzxhSet = jfxxmcVOList.stream().map(CchxwsnssbjfxxVO::getYzpzxh).collect(Collectors.toSet());
        }
        final Map<String, List<CchxwsnssbjfxxVO>> yjsfZxsmSkQzMap = yjsfZxsmSkQzMapTemp;
        //以征收项目分组-主表明细信息
        Map<String, List<CchxwsnssbzbmxVO>> zbmxZxsmMap = new HashMap<>();
        if (!GyUtils.isNull(cchxwsnssbzbmxVOList)) {
            zbmxZxsmMap = cchxwsnssbzbmxVOList.stream()
                    .filter(f -> !GyUtils.isNull(f.getYbtse()) && !GyUtils.isNull(f.getZsxmDm()) && !GyUtils.isNull(f.getSkssqq()) && !GyUtils.isNull(f.getSkssqz()))
                    .map(m -> {
                        m.setSkssqq(com.css.znsb.nssb.util.DateUtil.dateAutoFormatList(m.getSkssqq(), "yyyy-MM-dd"));
                        m.setSkssqz(com.css.znsb.nssb.util.DateUtil.dateAutoFormatList(m.getSkssqz(), "yyyy-MM-dd"));
                        return m;
                    }).collect(Collectors.groupingBy(CchxwsnssbzbmxVO::getZsxmDm));
        }
        //对申报返回信息遍历处理
        if (!GyUtils.isNull(cxstysbSaveReturnVOList)) {
            for (CxstysbSaveReturnVO saveReturnVO : cxstysbSaveReturnVOList) {
                //对每个申报的税种分别处理
                final String zsxmDm = saveReturnVO.getZsxmDm();
                final List<SBSaveReturnVO> saveVOList = saveReturnVO.getSavereturnGrid().getSavereturnGridlb();//获取当前征收项目保存接口信息
                final List<CchxwsnssbzbmxVO> zbmxVOList = zbmxZxsmMap.get(zsxmDm);//获取当前征收项目主表明细
                final List<CchxwsnssbjfxxVO> yjsfZsxmList = yjsfZxsmMap.get(zsxmDm);//判断当前征收项目是否返回应缴税费
                //记录当前征收项目拥有税费信息
                boolean hasYjsf = !GyUtils.isNull(yjsfZsxmList);
                //如果保存接口和主表信息无数据则不处理
                if (!GyUtils.isNull(saveVOList) && !GyUtils.isNull(zbmxVOList)) {
                    if (hasYjsf) {
                        final Set<String> ybtseSet = new HashSet<>();
                        for (SBSaveReturnVO saveVO : saveVOList) {
                            final String pzxh = saveVO.getPzxh();
                            final Double ybtse = saveVO.getYbtse();
                            //如果应缴税费查询出来了，则过滤掉此条数据
                            if (!yjsfPzxhSet.contains(pzxh) && !GyUtils.isNull(ybtse) && ybtse <= 1) {
                                final String ybtseStr = GYCastUtils.cast2Str(FtsCxsUtils.round(ybtse, 2));
                                if (!ybtseSet.contains(ybtseStr)) {
                                    ybtseSet.add(ybtseStr);
                                    final List<CchxwsnssbjfxxVO> collect = zbmxVOList.stream()
                                            .filter(f -> FtsCxsUtils.checkDoubelEqual(ybtse, f.getYbtse(), 2) && GyUtils.isNull(yjsfZxsmSkQzMap.get(f.getZsxmDm() + "-" + f.getSkssqq() + "-" + f.getSkssqz())))
                                            .map(m -> {
                                                final CchxwsnssbjfxxVO sfxxVO = BeanUtil.copyProperties(saveVO, CchxwsnssbjfxxVO.class);
                                                sfxxVO.setSkssqq(m.getSkssqq());
                                                sfxxVO.setSkssqz(m.getSkssqz());
                                                sfxxVO.setZsxmDm(m.getZsxmDm());
                                                sfxxVO.setZsxmmc(m.getZsxmmc());
                                                sfxxVO.setYzpzxh(saveVO.getPzxh());
                                                sfxxVO.setYbtse(saveVO.getYbtse());
                                                return sfxxVO;
                                            }).collect(Collectors.toList());
                                    sfxxAllVOList.addAll(collect);
                                }
                            }
                        }
                    } else {
                        //如果没有应缴税费，直接拿主表明细构建信息
                        final List<CchxwsnssbjfxxVO> collect = zbmxVOList.stream()
                                .filter(f -> !GyUtils.isNull(f.getYbtse()) && f.getYbtse() <= 1)
                                .map(m -> {
                                    final CchxwsnssbjfxxVO sfxxVO = BeanUtil.copyProperties(m, CchxwsnssbjfxxVO.class);
                                    sfxxVO.setYbtse(m.getYbtse());
                                    return sfxxVO;
                                }).collect(Collectors.toList());
                        sfxxAllVOList.addAll(collect);
                    }
                }
            }
        }
        log.info("dealWithYjsf_dateA2_" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
    }

    /**
     * 获取财行税税源明细
     *
     * @param cxsSymxReqVO cxsSymxReqVO
     * @return 返回值
     */
    @Override
    public CxsSymxResVO getCxsSymx(CxsSymxReqVO cxsSymxReqVO) {
        final CxsSymxResVO cxsSymxResVO = new CxsSymxResVO();
        if (GyConstants.getDmGyZsxmCztdsys().equals(cxsSymxReqVO.getZsxmDm())) {
            final List<CztdsysZspmGroupVO> cztdsysZspmGroupVOList = new ArrayList<>();
            CchxwsnssbGyUtils.addSyxxByCztdsys(cxsSymxReqVO.getCxsSySbxxVOList(), cxsSymxReqVO.getCxsSySkxxVOList(), cztdsysZspmGroupVOList);
            if (!GyUtils.isNull(cztdsysZspmGroupVOList)) {
                cxsSymxResVO.setReturnCode("1");
                cxsSymxResVO.setCztdsysZspmGroupVOList(cztdsysZspmGroupVOList);
            } else {
                cxsSymxResVO.setReturnMsg("9");
                cxsSymxResVO.setReturnMsg("未获取有效税源明细信息");
            }
        }
        if (GyConstants.getDmGyZsxmFcs().equals(cxsSymxReqVO.getZsxmDm())) {
            final List<FcsZspmGroupVO> fcsZspmGroupVOList = new ArrayList<>();
            if (!GyUtils.isNull(cxsSymxReqVO.getCxsSyCjxxVOList())) {
                CchxwsnssbGyUtils.addSyxxByFcs(cxsSymxReqVO.getCxsSySbxxVOList(), cxsSymxReqVO.getCxsSyCjxxVOList(), "cj", fcsZspmGroupVOList);
            }
            if (!GyUtils.isNull(cxsSymxReqVO.getCxsSyCzxxVOList())) {
                CchxwsnssbGyUtils.addSyxxByFcs(cxsSymxReqVO.getCxsSySbxxVOList(), cxsSymxReqVO.getCxsSyCzxxVOList(), "cz", fcsZspmGroupVOList);
            }
            if (!GyUtils.isNull(fcsZspmGroupVOList)) {
                cxsSymxResVO.setReturnCode("1");
                cxsSymxResVO.setFcsZspmGroupVOList(fcsZspmGroupVOList);
            } else {
                cxsSymxResVO.setReturnMsg("9");
                cxsSymxResVO.setReturnMsg("未获取有效税源明细信息");
            }
        }
        return cxsSymxResVO;
    }

    @Override
    public CchxwsnssbGzlVO handleZcxxVO(CchxwsnssbGzlVO cchxwsnssbGzlVO) {
        CchxwsnssbGzlVO resZcxxVO = new CchxwsnssbGzlVO();

        // 根据申报任务uuid查询pzxh
        final ZnsbNssbSbrwDTO sbrwDTO = znsbNssbSbrwService.getSbrwBySbrwuuid(cchxwsnssbGzlVO.getSbrwuuid());
        final String pzxh = sbrwDTO.getPzxh();
        if (GyUtils.isNull(pzxh)) {
            return resZcxxVO;
        }

        // 组装主体数据
        resZcxxVO.setDjxh(sbrwDTO.getDjxh());
        resZcxxVO.setNsrsbh(sbrwDTO.getNsrsbh());
        resZcxxVO.setNsrmc(sbrwDTO.getNsrmc());
        resZcxxVO.setZsxmDm(sbrwDTO.getZsxmDm());
        resZcxxVO.setSbrwuuid(sbrwDTO.getSbrwuuid());
        resZcxxVO.setSkssqq(DateUtils.format(sbrwDTO.getSkssqq(), "yyyy-MM-dd"));
        resZcxxVO.setSkssqz(DateUtils.format(sbrwDTO.getSkssqz(), "yyyy-MM-dd"));
        resZcxxVO.setXzqhszDm(sbrwDTO.getXzqhszDm());

        //获取纳纳税人资格信息
        NsrzgxxVO nsrzgxxVO = new NsrzgxxVO();
        //获取纳税人标签信息
        List<NsrbqxxVO> nsrbqxxVOList = new ArrayList<>();
        ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setDjxh(sbrwDTO.getDjxh());
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(sbrwDTO.getNsrsbh());
        CommonResult<ZnsbMhzcQyjbxxmxResVO> qyjbxxmx = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        final ZnsbMhzcQyjbxxmxResVO data = qyjbxxmx.getData();
        if (!GyUtils.isNull(data) && !GyUtils.isNull(data.getNsrzgxx())) {
            nsrzgxxVO = data.getNsrzgxx().get(0);
        }
        if (!GyUtils.isNull(data) && !GyUtils.isNull(data.getNsrbqxx())) {
            nsrbqxxVOList = data.getNsrbqxx();
        }
        List<CchxwsnssbInitResMxVO> initResMxVOList = new ArrayList<>();
        CchxwsnssbInitResMxVO initResMxVO = new CchxwsnssbInitResMxVO();
        this.getPhjmzgnew(initResMxVO, resZcxxVO.getSkssqq(), resZcxxVO.getSkssqz(), nsrzgxxVO, nsrbqxxVOList);
        initResMxVOList.add(initResMxVO);
        resZcxxVO.setInitResMxVOList(initResMxVOList);

        // 根据pzxh查询znsb_nssb_cxstysbxx
        List<ZnsbNssbCxstysbxxDO> cxstysbxxList = znsbNssbCxstysbxxServic.queryByPzxh(pzxh);
        if (GyUtils.isNull(cxstysbxxList)) {
            return resZcxxVO;
        }
        final String cxstysbuuid = cxstysbxxList.get(0).getCxstysbuuid();

//        // 根据cxstysbuuid查询znsb_nssb_cxstysbzb
//        final ZnsbNssbCxstysbzbDO cxstysbzbDO = znsbNssbCxstysbzbService.queryByCxstysbuuid(cxstysbuuid);
//        if (GyUtils.isNull(cxstysbzbDO)) {
//            return resZcxxVO;
//        }

        // 根据cxstysbuuid查询znsb_nssb_cxstyjmxx
        final List<ZnsbNssbCxstyjmxxDO> cxstyjmxxDOList = znsbNssbCxstyjmxxService.queryByCxstysbuuid(cxstysbuuid);

        // 组装申报主表数据
        List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList = new ArrayList<>();
        for (ZnsbNssbCxstysbxxDO cxstysbxxDO : cxstysbxxList){
            final CchxwsnssbzbmxVO cchxwsnssbzbmxVO = BeanUtils.toBean(cxstysbxxDO, CchxwsnssbzbmxVO.class);
            cchxwsnssbzbmxVO.setZsxmmc(CacheUtils.dm2mc("dm_gy_zsxm",cchxwsnssbzbmxVO.getZsxmDm()));
            cchxwsnssbzbmxVO.setZspmmc(CacheUtils.dm2mc("dm_gy_zspm",cchxwsnssbzbmxVO.getZspmDm()));
            cchxwsnssbzbmxVO.setZszmmc(CacheUtils.dm2mc("dm_gy_zszm",cchxwsnssbzbmxVO.getZszmDm()));
            cchxwsnssbzbmxVO.setYzpzzlmc(YzpzzlEnum.getYzpzzlByDm(cchxwsnssbzbmxVO.getYzpzzlDm()).getMc());
            cchxwsnssbzbmxVOList.add(cchxwsnssbzbmxVO);
        }
        resZcxxVO.setCchxwsnssbzbmxVOList(cchxwsnssbzbmxVOList);

        // 组装申报副表数据
        List<CchxwsnssbfbmxVO> cchxwsnssbfbmxVOList = new ArrayList<>();
        for (ZnsbNssbCxstyjmxxDO cxstyjmxxDO : cxstyjmxxDOList){
            final CchxwsnssbfbmxVO cchxwsnssbfbmxVO = BeanUtils.toBean(cxstyjmxxDO, CchxwsnssbfbmxVO.class);
            cchxwsnssbfbmxVO.setZsxmmc(CacheUtils.dm2mc("dm_gy_zsxm",cchxwsnssbfbmxVO.getZsxmDm()));
            cchxwsnssbfbmxVO.setZspmmc(CacheUtils.dm2mc("dm_gy_zspm",cchxwsnssbfbmxVO.getZspmDm()));
            cchxwsnssbfbmxVO.setZszmmc(CacheUtils.dm2mc("dm_gy_zszm",cchxwsnssbfbmxVO.getZszmDm()));
            cchxwsnssbfbmxVO.setSsjmxzmc(CacheUtils.dm2mc("dm_gy_ssjmxz",cchxwsnssbfbmxVO.getSsjmxzDm()));
            cchxwsnssbfbmxVOList.add(cchxwsnssbfbmxVO);
        }
        resZcxxVO.setCchxwsnssbfbmxVOList(cchxwsnssbfbmxVOList);

        return resZcxxVO;
    }

    /**
     * 财产和行为税申报初始化 - 错误更正
     *
     * @param cchxwsnssbCwgzInitReqVO 入参
     * @return 返回值
     */
    @Override
    @SneakyThrows
    public CchxwsnssbbxxVO cxsCwgzInit(CchxwsnssbCwgzInitReqVO cchxwsnssbCwgzInitReqVO) {
        //返回值
        final CchxwsnssbbxxVO cchxwsnssbbxxVO = new CchxwsnssbbxxVO();
        //取出入参数据
        final CxscwgzInitReqVO cxscwgzInitReqVO = cchxwsnssbCwgzInitReqVO.getCxscwgzInitReqVO();
        //税源初始化信息
        CchxwsnssbInitResMxVO initResMxVORequest = new CchxwsnssbInitResMxVO();
        if (!GyUtils.isNull(cchxwsnssbCwgzInitReqVO.getInitResMxVOList())) {
            initResMxVORequest = cchxwsnssbCwgzInitReqVO.getInitResMxVOList().get(0);
        }
        //纳税人为主管税务所科分局，自然人机关取更正信息
        String zgswjgdm = cxscwgzInitReqVO.getZgswskfjDm();
        final String yhlx = cxscwgzInitReqVO.getYhlx();
        //获取纳税人信息
        String hydm = "";
        String xzqhszdm = "";
        String jdxzdm = "";
        String djzclxdm = "";
        NsrzgxxVO nsrzgxxVO = null;
        List<NsrbqxxVO> nsrbqxxVOList = new ArrayList<>();
//        if(!YHLX_ZRR.equals(yhlx)){
//            final DwnsrxxVO dwnsrxxVO = CchxwsnssbGyUtils.queryNsrxxByApi(cxscwgzInitReqVO.getDjxh(), cxscwgzInitReqVO.getNsrsbh());
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(cxscwgzInitReqVO.getNsrsbh());
        znsbMhzcQyjbxxmxReqVO.setDjxh(cxscwgzInitReqVO.getDjxh());
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsr = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        if (!GyUtils.isNull(nsr.getData()) && !GyUtils.isNull(nsr.getData().getJbxxmxsj())) {
            JbxxmxsjVO jbxxmxsjVO = nsr.getData().getJbxxmxsj().get(0);
            hydm = GyUtils.isNull(jbxxmxsjVO.getHyDm()) ? "" : jbxxmxsjVO.getHyDm();
//            xzqhszdm = GyUtils.isNull(jbxxmxsjVO.getScjydzxzqhszDm()) ? "" : jbxxmxsjVO.getScjydzxzqhszDm();
            jdxzdm = GyUtils.isNull(jbxxmxsjVO.getJdxzDm()) ? "" : jbxxmxsjVO.getJdxzDm();
            djzclxdm = GyUtils.isNull(jbxxmxsjVO.getDjzclxDm()) ? "" : jbxxmxsjVO.getDjzclxDm();
        }
        if (!GyUtils.isNull(nsr.getData()) && !GyUtils.isNull(nsr.getData().getNsrzgxx())) {
            nsrzgxxVO = nsr.getData().getNsrzgxx().get(0);
        }
        if (!GyUtils.isNull(nsr.getData()) && !GyUtils.isNull(nsr.getData().getNsrbqxx())) {
            nsrbqxxVOList = nsr.getData().getNsrbqxx();
        }

        final CommonResult<CompanyBasicInfoDTO> jgxxResult = companyApi.basicInfo(cxscwgzInitReqVO.getDjxh(), cxscwgzInitReqVO.getNsrsbh());
        if (jgxxResult.isSuccess() && GyUtils.isNotNull(jgxxResult.getData())) {
            xzqhszdm = jgxxResult.getData().getXzqhszDm();
        }
//        } else {
//            //自然人机关更正链接获取，暂时未提供，通关查询sb_sbb获取
//            zgswjgdm = getCwgzzgswskfjDm(cxscwgzInitReqVO.getPzxh(), cxscwgzInitReqVO.getDjxh());
//            if(GyUtils.isNull(zgswjgdm)){
//                cchxwsnssbbxxVO.setReturnCode("-1");
//                cchxwsnssbbxxVO.setReturnMsg("未查询有效税务机关代码。");
//                return cchxwsnssbbxxVO;
//            }
//        }
        //组装公用查询条件
        final CxstysbNsrxx cxstysbNsrxx = new CxstysbNsrxx();
        cxstysbNsrxx.setDjxh(cxscwgzInitReqVO.getDjxh());
        cxstysbNsrxx.setNsrsbh(cxscwgzInitReqVO.getNsrsbh());
        cxstysbNsrxx.setNsrmc(cxscwgzInitReqVO.getNsrmc());
        cxstysbNsrxx.setSbsxDm1("11");
        cxstysbNsrxx.setSbrq1(CchxwsnssbGyUtils.getDayofNow());
        cxstysbNsrxx.setHyDm(hydm);
        cxstysbNsrxx.setJdxzDm(jdxzdm);
        cxstysbNsrxx.setYzpzzlDm(cxscwgzInitReqVO.getYzpzzlDm());//有值
        cxstysbNsrxx.setXzqhszDm(xzqhszdm);
        cxstysbNsrxx.setZgswskfjDm(zgswjgdm);
        cxstysbNsrxx.setSkssqq(cxscwgzInitReqVO.getSkssqq());//有值
        cxstysbNsrxx.setSkssqz(cxscwgzInitReqVO.getSkssqz());//有值
        //更正标志
        cxstysbNsrxx.setScenceCs("cwgzbz");
        cxstysbNsrxx.setCxstysbuuid(cxscwgzInitReqVO.getCxstysbuuid());//有值

        //耕地占用税未批先占参数值，为Y显示提示，并判断gdzysLslfMrz
        final String gdzysWpxzCsz = CxsGyUtils.getXtcs(zgswjgdm, "DZSWJ00SBZX020051", "N");
        //费耕地占用税六税两默认值，为空前端不做修改，为N默认为不选中且不可修改
        String gdzysLslfMrz = "";

        //内部返回值
        //此返回值不作为申报基础信息依据，仅为存储六税两费信息，所以可为空
        final List<CchxwsnssbInitResMxVO> initResMxVOList = new ArrayList<>();
        final CchxwsnssbInitResMxVO initResMxVO = new CchxwsnssbInitResMxVO();
        initResMxVO.setZsxmdm(cxscwgzInitReqVO.getZsxmDm());
        final List<SbxxGridlbVO> sbxxGridlb = new ArrayList<>();
        final List<Bqjmsemxbywbw> bqjmsemxbywbwList = new ArrayList<>();
        //税源明细ReqVO
        final CxsSymxReqVO cxsSymxReqVO = new CxsSymxReqVO();

        //组装六税两费信息
        if (!YHLX_ZRR.equals(yhlx)) {
            //如果此节点为空，证明为税源跳转，更正仅作为普惠减免属性，可为空
            if (GyUtils.isNull(cchxwsnssbCwgzInitReqVO.getInitResMxVOList())) {
                //含六税两费税种(房、土、印花、资源、耕占)
                if ("10110,10112,10111,10107,10118".contains(cxscwgzInitReqVO.getZsxmDm())) {
                    final CxstysbNsrxxVO cxstysbNsrxxVO = new CxstysbNsrxxVO();
                    cxstysbNsrxxVO.setDjxh(cxscwgzInitReqVO.getDjxh());
                    cxstysbNsrxxVO.setZgswskfj_dm(zgswjgdm);
//                    this.getPhjmzg(cxstysbNsrxxVO, initResMxVO, cxstysbNsrxx.getSkssqq(), cxstysbNsrxx.getSkssqz());
                    this.getPhjmzgnew(initResMxVO, cxstysbNsrxx.getSkssqq(), cxstysbNsrxx.getSkssqz(), nsrzgxxVO, nsrbqxxVOList);
                    if ("XWN".equals(initResMxVO.getBqsfsyzzsxgmnsrjzzc())) {
                        //第一次调用初始化接口默认不享受
                        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc("N");
                    } else {
                        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVO.getBqsfsyzzsxgmnsrjzzc());
                    }
                    //根据不同登记注册类型填入不同减征类型特殊处理
                    this.dealWithJzzcsyztDm(initResMxVO, cxstysbNsrxx, djzclxdm);
                    //组装其他信息
                    cxstysbNsrxx.setXgmjzzcqssj(initResMxVO.getXgmjzzcqssj());
                    cxstysbNsrxx.setXgmjzzczzsj(initResMxVO.getXgmjzzczzsj());
                    initResMxVOList.add(initResMxVO);
                }
            } else {
                //此时证明为税源为主动修改减免信息，填入勾选情况
                if ("XWN".equals(initResMxVORequest.getBqsfsyzzsxgmnsrjzzc()) || "".equals(initResMxVORequest.getBqsfsyzzsxgmnsrjzzc())) {
                    cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc("N");
                } else {
                    cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(initResMxVORequest.getBqsfsyzzsxgmnsrjzzc());
                }
                cxstysbNsrxx.setJzzcsyztDm(initResMxVORequest.getJzzcsyztDm());
                cxstysbNsrxx.setXgmjzzcqssj(initResMxVORequest.getXgmjzzcqssj());
                cxstysbNsrxx.setXgmjzzczzsj(initResMxVORequest.getXgmjzzczzsj());
                initResMxVO.setBqsfsyzzsxgmnsrjzzc(initResMxVORequest.getBqsfsyzzsxgmnsrjzzc());
                initResMxVO.setJzzcsyztDm(initResMxVORequest.getJzzcsyztDm());
                initResMxVO.setXgmjzzcqssj(initResMxVORequest.getXgmjzzcqssj());
                initResMxVO.setXgmjzzczzsj(initResMxVORequest.getXgmjzzczzsj());
                initResMxVO.setCheckbz(initResMxVORequest.getCheckbz());
                initResMxVOList.add(initResMxVO);
            }
        }
//        else {
//            //自然人默认六税两费(房、土、印花、资源、耕占)
//            if("10110,10112,10111,10107,10118".contains(initResMxVO.getZsxmdm())){
//                //2023.09.11 普惠减免日期开始于2019.01.01
//                if (!GyUtils.isNull(cxscwgzInitReqVO.getSkssqq()) && DateUtil.toDate(cxscwgzInitReqVO.getSkssqq(),"yyyy-MM-dd").before(DateUtil.toDate("2019-01-01", "yyyy-MM-dd"))) {
//                    initResMxVO.setBqsfsyzzsxgmnsrjzzc("N");
//                    initResMxVO.setCheckbz("N");
//                    initResMxVO.setJzzcsyztDm("");
//                    initResMxVO.setXgmjzzcqssj("");
//                    initResMxVO.setXgmjzzczzsj("");
//                    cxstysbNsrxx.setXgmjzzcqssj("");
//                    cxstysbNsrxx.setXgmjzzczzsj("");
//                    cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc("N");
//                    cxstysbNsrxx.setJzzcsyztDm("");
//                } else {
//                    initResMxVO.setBqsfsyzzsxgmnsrjzzc("Y");
//                    initResMxVO.setCheckbz("Y");
//                    initResMxVO.setJzzcsyztDm("11");
//                    //更正减免属期默认为属期
//                    initResMxVO.setXgmjzzcqssj(cxscwgzInitReqVO.getSkssqq());
//                    initResMxVO.setXgmjzzczzsj(cxscwgzInitReqVO.getSkssqz());
//                    cxstysbNsrxx.setXgmjzzcqssj(cxscwgzInitReqVO.getSkssqq());
//                    cxstysbNsrxx.setXgmjzzczzsj(cxscwgzInitReqVO.getSkssqz());
//                    cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc("Y");
//                    cxstysbNsrxx.setJzzcsyztDm("11");
//                }
//                initResMxVOList.add(initResMxVO);
//            }
//        }

//        if(!GyUtils.isNull(cchxwsnssbCwgzInitReqVO.getSymxVOList())){
        if (!GyUtils.isNull(cxscwgzInitReqVO.getPzxh())) {
//            //扩展节点
//            final Map<String, String> expand = new HashMap<>();
//            expand.put("sjjg", zgswjgdm);
            //组装入参
//            final HXZGSB10735Request hxzgsb10735Request = new HXZGSB10735Request();
//            final HXZGSB10735Request.SbxxGrid sbxxGrid = new HXZGSB10735Request.SbxxGrid();
//            final sbxxReqGridlbVO sbxxGridlbVO = new sbxxReqGridlbVO();
//            final sbxxReqGridlbVO.SymxGrid symxGrid = new sbxxReqGridlbVO.SymxGrid();
//            final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//            for (SymxVO symxVO : cchxwsnssbCwgzInitReqVO.getSymxVOList()) {
//                final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//                symxGridlbVO.setSyuuid(symxVO.getSyuuid());
//                symxGridlbVO.setSybzDm1(symxVO.getSybzDm1());
//                symxGridlb.add(symxGridlbVO);
//            }
//            symxGrid.getSymxGridlb().addAll(symxGridlb);
//            sbxxGridlbVO.setZsxmDm(cxscwgzInitReqVO.getZsxmDm());
//            sbxxGridlbVO.setSbuuid(cxscwgzInitReqVO.getSbuuid());
//            sbxxGridlbVO.setPzxh(cxscwgzInitReqVO.getPzxh());
//            sbxxGridlbVO.setSkssqq(cxscwgzInitReqVO.getSkssqq());
//            sbxxGridlbVO.setSkssqz(cxscwgzInitReqVO.getSkssqz());
//            sbxxGridlbVO.setSymxGrid(symxGrid);
//            sbxxGrid.getSbxxGridlb().add(sbxxGridlbVO);
//            //耕占&&系统参数为Y&&占用方式不为空&&占用方式包含06或未批先占&&税源均为未批先占
//            if ("10118".equals(initResMxVO.getZsxmdm()) && "Y".equals(gdzysWpxzCsz)) {
//                final List<SymxVO> wpxzList = cchxwsnssbCwgzInitReqVO.getSymxVOList().stream().filter(f -> !GyUtils.isNull(f.getZdfsDm()) && (f.getZdfsDm().contains("06") || f.getZdfsDm().contains("未批先占"))).collect(Collectors.toList());
//                if (!GyUtils.isNull(wpxzList) && wpxzList.size() == cchxwsnssbCwgzInitReqVO.getSymxVOList().size()) {
//                    gdzysLslfMrz = "N";
//                }
//            }
//            //如果默认为N，则直接使用
//            if ("N".equals(gdzysLslfMrz)) {
//                final CxstysbNsrxx bxsNsrVO = this.getBxsNsrVO(cxstysbNsrxx);
//                hxzgsb10735Request.setCxstysbnsrxx(bxsNsrVO);
//            } else {
//                hxzgsb10735Request.setCxstysbnsrxx(cxstysbNsrxx);
//            }
//            hxzgsb10735Request.setSbxxGrid(sbxxGrid);

            if ("Y".equals(cchxwsnssbCwgzInitReqVO.getTqgzsjbz())){
                //根据本地税源信息生成申报信息
                this.cwgzInitByLocal(cxstysbNsrxx,cxscwgzInitReqVO,sbxxGridlb,bqjmsemxbywbwList);
            }else {
                //根据乐企错误更正初始化接口生成申报信息
                this.cwgzInitByLq(cxstysbNsrxx,cxscwgzInitReqVO,sbxxGridlb,bqjmsemxbywbwList);
            }
        } else {
            cchxwsnssbbxxVO.setReturnCode("-1");
            cchxwsnssbbxxVO.setReturnMsg("没有传入税源信息！");
        }

        //主表VO
        final CchxwsnssbzbVO cchxwsnssbzbVO = new CchxwsnssbzbVO();
        cchxwsnssbzbVO.setDjxh(cxstysbNsrxx.getDjxh());
        cchxwsnssbzbVO.setNsrmc(cxstysbNsrxx.getNsrmc());
        cchxwsnssbzbVO.setNsrsbh(cxstysbNsrxx.getNsrsbh());
        //征收表税款属性代码0101为一般申报，此处可能有问题，但是没用到这个字段
        cchxwsnssbzbVO.setSksxDm("11");
        cchxwsnssbzbVO.setSbrq(CchxwsnssbGyUtils.getNowTime());
        cchxwsnssbzbVO.setHyDm(hydm);
        cchxwsnssbzbVO.setZgswskfjDm(cxstysbNsrxx.getZgswskfjDm());
        cchxwsnssbzbVO.setCxstysbuuid(cxscwgzInitReqVO.getCxstysbuuid());
        cchxwsnssbzbVO.setPzxh(cxscwgzInitReqVO.getPzxh());
        cchxwsnssbzbVO.setSbuuid(cxscwgzInitReqVO.getSbuuid());
        cchxwsnssbzbVO.setYzpzzlDm(cxscwgzInitReqVO.getYzpzzlDm());

        //环保税排序使用
        final List<HbsCjSyxx> hbsSyxxBaseSort = new ArrayList<>();
//        //针对环保税对主表明细增加税源编号
//        if(GyConstants.getDmGyZsxmHbs().equals(initResMxVO.getZsxmdm())
//                && !GyUtils.isNull(cchxwsnssbCwgzInitReqVO.getSymxVOList()) && !GyUtils.isNull(sbxxGridlb)){
//            this.addSybhZbbmxByHbs(hbsSyxxBaseSort, cchxwsnssbCwgzInitReqVO.getSymxVOList(), sbxxGridlb, cxstysbNsrxx, zgswjgdm ,yhlx);
//        }

        //汇总申报明细VOList，根据zsxmDm，zspmDm，skssqq，skssqz，sl1分组汇总，资源、环保特殊处理
        final List<SbxxGridlbVO> hzzbList = CchxwsnssbGyUtils.hzsbxx(sbxxGridlb, new HashMap<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new String[]{"zsxmDm", "zspmDm", "skssqq", "skssqz", "sl1"}, true);
        //处理汇总明细VOList
        final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList = this.dealWithZbmxList(hzzbList);
        List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList_sort = new ArrayList<>();
        if (!GyUtils.isNull(cchxwsnssbzbmxVOList)) {
            cchxwsnssbzbmxVOList_sort = cchxwsnssbzbmxVOList.stream().sorted(Comparator.comparing(CchxwsnssbzbmxVO::getZspmDm, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(Comparator.comparing(CchxwsnssbzbmxVO::getSkssqz).reversed())).collect(Collectors.toList());
        }

        //附表减免明细VOList，分组汇总，正常为zsxmDm，skssqq，skssqz，sybh分组，资源、印花、环保特殊处理
        final List<BqjmsemxbGridlbVO> hzFbJmxxList = this.hzFbJmxx(bqjmsemxbywbwList);
        //处理附表明细VOList
        final List<CchxwsnssbfbmxVO> cchxwsnssbfbmxVOList = this.dealWithFbmxList(hzFbJmxxList);

        //2023.06.15 禅道37799，对环保税主附表明细增加排序
        if (GyConstants.getDmGyZsxmHbs().equals(initResMxVO.getZsxmdm()) && !GyUtils.isNull(cchxwsnssbzbmxVOList_sort)) {
            this.sortZbbmxByHbs(cchxwsnssbzbmxVOList_sort, cchxwsnssbfbmxVOList);
        }
        //2023.09.11 房土添加税源明细信息
        if (GyConstants.getDmGyZsxmCztdsys().equals(initResMxVO.getZsxmdm()) || GyConstants.getDmGyZsxmFcs().equals(initResMxVO.getZsxmdm())) {
            cxsSymxReqVO.setZsxmDm(initResMxVO.getZsxmdm());
            cchxwsnssbbxxVO.setCxsSymxReqVO(cxsSymxReqVO);
        }

        //处理卡片税款信息VOList
        final List<CchxwsnssbKpVO> kpVOList = this.dealWithKpList(cchxwsnssbzbmxVOList, initResMxVO.getZsxmdm(), "N");

        //处理返回值
        final List<CchxwsnssbKpVO> kpmcVOList = CchxwsnssbGyUtils.getZsxmMcKp(kpVOList);
        final List<CchxwsnssbzbmxVO> zbmxmcVOList = CchxwsnssbGyUtils.getZsxmMcZbmx(cchxwsnssbzbmxVOList_sort);
        final List<CchxwsnssbfbmxVO> fbmxmcVOList = CchxwsnssbGyUtils.getZsxmMcFbmx(cchxwsnssbfbmxVOList);

        //处理六税两费减免
        this.clLslfJmxx(zbmxmcVOList,fbmxmcVOList);

        List<SymxVO> symxVOList = zbmxmcVOList.stream().map(e -> {
            SymxVO symxVO = new SymxVO();
            symxVO.setSyuuid(e.getSyuuid());
            return symxVO;
        }).collect(Collectors.toList());
        cchxwsnssbbxxVO.setSymxVOList(symxVOList);
        cchxwsnssbbxxVO.setCchxwsnssbzbVO(cchxwsnssbzbVO);
        cchxwsnssbbxxVO.setCchxwsnssbzbmxVOList(zbmxmcVOList);
        cchxwsnssbbxxVO.setCchxwsnssbfbmxVOList(fbmxmcVOList);
        cchxwsnssbbxxVO.setInitResMxVOList(initResMxVOList);
        cchxwsnssbbxxVO.setCchxwsnssbKpVOList(kpmcVOList);
        this.clGzMxVO(initResMxVOList, gdzysWpxzCsz, gdzysLslfMrz);
        return cchxwsnssbbxxVO;
    }

    private void cwgzInitByLocal(CxstysbNsrxx cxstysbNsrxx, CxscwgzInitReqVO cxscwgzInitReqVO, List<SbxxGridlbVO> sbxxGridlb, List<Bqjmsemxbywbw> bqjmsemxbywbwList) {
        //根据凭证序号查询申报任务
        LambdaQueryWrapper<ZnsbNssbSbrwDO> znsbNssbSbrwDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        znsbNssbSbrwDOLambdaQueryWrapper.eq(ZnsbNssbSbrwDO::getPzxh, cxscwgzInitReqVO.getPzxh())
                .in(ZnsbNssbSbrwDO::getNsrsbztDm, Arrays.asList(SBZT_SBCG_DM, SBZT_CWGZ_GZSB_DM,SBZT_CWGZ_GZCG_DM));
        ZnsbNssbSbrwDO znsbNssbSbrwDO = znsbNssbSbrwMapper.selectOne(znsbNssbSbrwDOLambdaQueryWrapper);
        //组装syuuid
        String[] syuuidList = znsbNssbSbrwDO.getSyuuid1().substring(1, znsbNssbSbrwDO.getSyuuid1().length() - 1).split(",");
        final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
        for (String syuuid : syuuidList) {
            final SymxVO symxVO = new SymxVO();
            symxVO.setSyuuid(syuuid);
            symxVO.setSybzDm1("");
            final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
            symxGridlbVO.setSyuuid(syuuid);
            symxGridlbVO.setSybzDm1("");
            symxGridlb.add(symxGridlbVO);
        }
        this.initCxsCwgztysbHX(GyConstants.getDmGyZsxmYhs(), cxstysbNsrxx,cxscwgzInitReqVO, symxGridlb, sbxxGridlb, bqjmsemxbywbwList);
    }


    private void initCxsCwgztysbHX(String zsxmDm,CxstysbNsrxx cxstysbNsrxx, CxscwgzInitReqVO cxscwgzInitReqVO, List<SymxGridlbVO> symxGridlb, List<SbxxGridlbVO> sbxxGridlb, List<Bqjmsemxbywbw> bqjmsemxbywbwList) {
        CxsQuerymxInitDTO cxsQuerymxInitDTO = this.cxsCwgzInitRequsetBW(cxstysbNsrxx, cxscwgzInitReqVO,symxGridlb);
        log.info("---cxsQuerymxInitDTO------{}",cxsQuerymxInitDTO);
        HXZGSB10735Response hxzgsb10735Response = null;
        if (YHS.getCode().equals(zsxmDm)) {
            YhsTysbbYtReqDTO yhsTysbbYtReqDTO = BeanUtil.toBean(cxsQuerymxInitDTO, YhsTysbbYtReqDTO.class);
            yhsTysbbYtReqDTO.setSyuuid(cxsQuerymxInitDTO.getSyuuidList());
            final String yskg = CacheUtils.getXtcs("CCHXWSNSSB00001");
            if ("Y".equals(yskg)) {
                hxzgsb10735Response = znsbJyssSsSsjgYhsService.getCxxx1(yhsTysbbYtReqDTO);
            } else {
                hxzgsb10735Response = znsbJyssSsSsjgYhsService.getCxxx(yhsTysbbYtReqDTO);
            }
        } else if (CZTDSYS.getCode().equals(zsxmDm)) {
            hxzgsb10735Response = cztdsysService.cxsInit(cxsQuerymxInitDTO);
        } else if (FCS.getCode().equals(zsxmDm)) {
            hxzgsb10735Response = fcsService.cxsInit(cxsQuerymxInitDTO);
        } else if (YYS.getCode().equals(zsxmDm)) {
            hxzgsb10735Response = yyssycjService.querymxInitData(cxsQuerymxInitDTO);
        }
        if (GyUtils.isNotNull(hxzgsb10735Response)) {
            if (!GyUtils.isNull(hxzgsb10735Response)) {
                if (!GyUtils.isNull(hxzgsb10735Response.getSbxxGrid()) && !GyUtils.isNull(hxzgsb10735Response.getSbxxGrid().getSbxxGridlb())) {
                    sbxxGridlb.addAll(hxzgsb10735Response.getSbxxGrid().getSbxxGridlb());
                    if (GyConstants.getDmGyZsxmFcs().equals(zsxmDm) || GyConstants.getDmGyZsxmCztdsys().equals(zsxmDm)) {
                        final List<SbxxGridlbVO> cxsSySbxxVOS = new ArrayList<>();
                        if (GyConstants.getDmGyZsxmCztdsys().equals(zsxmDm) && !GyUtils.isNull(hxzgsb10735Response.getTdsyxxGrid()) && !GyUtils.isNull(hxzgsb10735Response.getTdsyxxGrid().getTdsyxxGridlb())) {
                            final Map<String, List<SBCxsTdsyxxcjbVO>> tdsyMap = hxzgsb10735Response.getTdsyxxGrid().getTdsyxxGridlb().stream().filter(e -> GyUtils.isNotNull(e.getSyuuid())).collect(Collectors.groupingBy(SBCxsTdsyxxcjbVO::getSyuuid));
                            final List<SbxxGridlbVO> collect = hxzgsb10735Response.getSbxxGrid().getSbxxGridlb().stream().map(m -> {
                                final SbxxGridlbVO copy = BeanUtil.copyProperties(m, SbxxGridlbVO.class);
                                if (!GyUtils.isNull(tdsyMap.get(copy.getSyuuid()))) {
                                    copy.setSybh(tdsyMap.get(copy.getSyuuid()).get(0).getTdsybh());
                                }
                                return copy;
                            }).collect(Collectors.toList());
                            cxsSySbxxVOS.addAll(collect);
                        } else {
                            cxsSySbxxVOS.addAll(BeanUtil.copyToList(hxzgsb10735Response.getSbxxGrid().getSbxxGridlb(), SbxxGridlbVO.class));
                        }
                        sbxxGridlb.addAll(cxsSySbxxVOS);
                    }
                }
                if (!GyUtils.isNull(hxzgsb10735Response.getJmxxGrid()) && !GyUtils.isNull(hxzgsb10735Response.getJmxxGrid().getJmxxGridlb())) {
                    bqjmsemxbywbwList.addAll(hxzgsb10735Response.getJmxxGrid().getJmxxGridlb());
                }
            }
        }
    }

    @SneakyThrows
    private CxsQuerymxInitDTO cxsCwgzInitRequsetBW(CxstysbNsrxx cxstysbNsrxx, CxscwgzInitReqVO cxscwgzInitReqVO, List<SymxGridlbVO> symxGridlb) {
        // 财行税通用申报初始化
        CxsQuerymxInitDTO cxsQuerymxInitDTO = new CxsQuerymxInitDTO();
        cxsQuerymxInitDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsQuerymxInitDTO.setSkssqq(DateUtils.parseDate(cxstysbNsrxx.getSkssqq(), "yyyy-MM-dd"));
        cxsQuerymxInitDTO.setSkssqz(DateUtils.parseDate(cxstysbNsrxx.getSkssqz(), "yyyy-MM-dd"));
        cxsQuerymxInitDTO.setNsqxDm(cxscwgzInitReqVO.getNsqxDm());
        cxsQuerymxInitDTO.setBssfsylslfjmzc(cxstysbNsrxx.getBqsfsyzzsxgmnsrjzzc());
        cxsQuerymxInitDTO.setJmsyzt(cxstysbNsrxx.getJzzcsyztDm());
        if (GyUtils.isNotNull(cxstysbNsrxx.getXgmjzzcqssj())) {
            cxsQuerymxInitDTO.setSyjzzcq(DateUtils.parseDate(cxstysbNsrxx.getXgmjzzcqssj(), "yyyy-MM-dd"));
        }
        if (GyUtils.isNotNull(cxstysbNsrxx.getXgmjzzcqssj())) {
            cxsQuerymxInitDTO.setSyjzzcz(DateUtils.parseDate(cxstysbNsrxx.getXgmjzzczzsj(), "yyyy-MM-dd"));
        }
        if (GyUtils.isNotNull(symxGridlb)) {
            cxsQuerymxInitDTO.setSyuuidList(symxGridlb.stream().map(SymxGridlbVO::getSyuuid).collect(Collectors.toList()));
        }
        cxsQuerymxInitDTO.setGzbz("Y");
        return cxsQuerymxInitDTO;
    }

    private void cwgzInitByLq(CxstysbNsrxx cxstysbNsrxx, CxscwgzInitReqVO cxscwgzInitReqVO, List<SbxxGridlbVO> sbxxGridlb, List<Bqjmsemxbywbw> bqjmsemxbywbwList) {
        final CxsCwgzInitReqDTO cxsCwgzInitReqDTO = new CxsCwgzInitReqDTO();
        cxsCwgzInitReqDTO.setDjxh(cxscwgzInitReqVO.getDjxh());
        final CxsCwgzPzxhGridDTO cxsCwgzpzxhGridDTO = new CxsCwgzPzxhGridDTO();
        cxsCwgzpzxhGridDTO.setPzxh(Collections.singletonList(cxscwgzInitReqVO.getPzxh()));
        cxsCwgzInitReqDTO.setPzxhGrid(cxsCwgzpzxhGridDTO);
        final String ywbw = JsonUtils.toJson(cxsCwgzInitReqDTO);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CXS0000003");
        sjjhDTO.setYwbm(YzpzzlEnum.CXS.getDm());
        sjjhDTO.setYwuuid(GyUtils.getUuid());
        sjjhDTO.setDjxh(cxstysbNsrxx.getDjxh());
        sjjhDTO.setNsrsbh(cxstysbNsrxx.getNsrsbh());
        sjjhDTO.setXzqhszDm(cxstysbNsrxx.getXzqhszDm());
        sjjhDTO.setBwnr(ywbw);
        CommonResult<Object> commonResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (GyUtils.isNotNull(commonResult) && GyUtils.isNotNull(commonResult.getData())) {
            if (commonResult.getCode() == -1) {
                throw ServiceExceptionUtil.exception(INTERNAL_SERVER_ERROR, "财行税调用乐企财行税错误更正初始化接口失败djxh:{},nsrsbh:{},zsxmDm:{}", cxstysbNsrxx.getDjxh(), cxstysbNsrxx.getNsrsbh(), cxscwgzInitReqVO.getZsxmDm());
            }
            final CxsCwgzinitResponseDTO cxsCwgzinitResponseDTO = (CxsCwgzinitResponseDTO) commonResult.getData();
            if (!GyUtils.isNull(cxsCwgzinitResponseDTO.getSbxxGrid()) && !GyUtils.isNull(cxsCwgzinitResponseDTO.getSbxxGrid())) {
                List<CxssbxxGridlbDTO> cxssbxxGridlbDTOList = cxsCwgzinitResponseDTO.getSbxxGrid().getSbxxGridlb();
                List<SbxxGridlbVO> sbxxGridlbVOS = cxssbxxGridlbDTOList.stream().map(e -> {
                    SbxxGridlbVO sbxxGridlbVO = BeanUtil.toBean(e, SbxxGridlbVO.class);
                    sbxxGridlbVO.setSkssqq(DateUtil.format(e.getSkssqq(), "yyyy-MM-dd"));
                    sbxxGridlbVO.setSkssqz(DateUtil.format(e.getSkssqz(), "yyyy-MM-dd"));
                    return sbxxGridlbVO;
                }).collect(Collectors.toList());
                sbxxGridlb.addAll(sbxxGridlbVOS);
            }
            if (GyUtils.isNotNull(cxsCwgzinitResponseDTO.getJmxxGrid().getJmxxGridlb())
                    && !cxsCwgzinitResponseDTO.getJmxxGrid().getJmxxGridlb().isEmpty()) {
                List<BqjmsemxbGridlbVO> bqjmsemxbGridlbVOS = cxsCwgzinitResponseDTO.getJmxxGrid().getJmxxGridlb().get(0)
                        .getBqjmsemxb().getBqjmsemxbGridlb().stream().map(e -> {
                    BqjmsemxbGridlbVO bqjmsemxbGridlbVO = BeanUtil.toBean(e, BqjmsemxbGridlbVO.class);
                    bqjmsemxbGridlbVO.setJmse(GyUtils.isNull(e.getJmxse()) ? 0 : e.getJmxse().doubleValue());
                    bqjmsemxbGridlbVO.setSkssqq(DateUtil.format(e.getSkssqq(), "yyyy-MM-dd"));
                    bqjmsemxbGridlbVO.setSkssqz(DateUtil.format(e.getSkssqz(), "yyyy-MM-dd"));
                    return bqjmsemxbGridlbVO;
                }).collect(Collectors.toList());
                List<Bqjmsemxbywbw> bqjmsemxbywbws = new ArrayList<>();
                Bqjmsemxbywbw bqjmsemxbywbw = new Bqjmsemxbywbw();
                bqjmsemxbywbw.setZsxmDm(cxscwgzInitReqVO.getZsxmDm());
                Bqjmsemxbywbw.Bqjmsemxb bqjmsemxb = new Bqjmsemxbywbw.Bqjmsemxb();
                bqjmsemxb.setBqjmsemxbGridlb(bqjmsemxbGridlbVOS);
                bqjmsemxbywbw.setBqjmsemxb(bqjmsemxb);
                bqjmsemxbywbws.add(bqjmsemxbywbw);
                bqjmsemxbywbwList.addAll(bqjmsemxbywbws);
            }
        }
    }


//    public CxsCwgzinitResponseDTO querySbxxGrid(CxscwgzInitReqVO cxscwgzInitReqVO){
//        CxsCwgzinitResponseDTO cxsCwgzinitResponseDTO = new CxsCwgzinitResponseDTO();
//        final String djxh = cxscwgzInitReqVO.getDjxh();
//        final String zsxmDm = cxscwgzInitReqVO.getZsxmDm();
//        QueryWrapper<SbSbxxDO> sbxxDOQueryWrapper = new QueryWrapper<>();
//        sbxxDOQueryWrapper.lambda().eq(SbSbxxDO::getPzxh,cxscwgzInitReqVO.getPzxh());
//        List<SbSbxxDO> sbSbxxDOList = sbSbxxMapper.selectList(sbxxDOQueryWrapper);
//        if (GyUtils.isNull(sbSbxxDOList)) {
//            return null;
//        }
//        final String sbuuid = sbSbxxDOList.get(0).getSbuuid();
//        QueryWrapper<ZnsbNssbSbrwDO> sbrwDOQueryWrapper = new QueryWrapper<>();
//        sbrwDOQueryWrapper.lambda().eq(ZnsbNssbSbrwDO::getSbuuid,sbuuid)
//                .eq(ZnsbNssbSbrwDO::getDjxh,djxh);
//        List<ZnsbNssbSbrwDO> sbrwDOList = znsbNssbSbrwMapper.selectList(sbrwDOQueryWrapper);
//        if (GyUtils.isNull(sbrwDOList)){
//            return null;
//        }
//        final String[] syuuidList = new String[]{sbrwDOList.get(0).getSyuuid()};
//        final List<SymxGridlbVO> symxGridlb = new ArrayList<>();
//        for (String syuuid : syuuidList) {
//            final SymxVO symxVO = new SymxVO();
//            symxVO.setSyuuid(syuuid);
//            symxVO.setSybzDm1("");
//            final SymxGridlbVO symxGridlbVO = new SymxGridlbVO();
//            symxGridlbVO.setSyuuid(syuuid);
//            symxGridlbVO.setSybzDm1("");
//            symxGridlb.add(symxGridlbVO);
//        }
//        if (GyUtils.isNotNull(symxGridlb)) {
//            //调用财行税申报核心初始化接口
//            CxsQuerymxInitDTO cxsQuerymxInitDTO = new CxsQuerymxInitDTO();
//            // 财行税通用申报初始化
//            cxsQuerymxInitDTO.setDjxh(djxh);
//            cxsQuerymxInitDTO.setSkssqq(GyUtils.cast2Date(cxscwgzInitReqVO.getSkssqq()));
//            cxsQuerymxInitDTO.setSkssqz(GyUtils.cast2Date(cxscwgzInitReqVO.getSkssqz()));
//            cxsQuerymxInitDTO.setNsqxDm(cxscwgzInitReqVO.getNsqxDm());
//            cxsQuerymxInitDTO.setBssfsylslfjmzc(cxscwgzInitReqVO.getBqsfsyzzsxgmnsrjzzc());
//            cxsQuerymxInitDTO.setJmsyzt(initResMxVO.getJzzcsyztDm());
//            if (GyUtils.isNotNull(cxscwgzInitReqVO.getXgmjzzcqssj())) {
//                cxsQuerymxInitDTO.setSyjzzcq(DateUtils.parseDate(cxscwgzInitReqVO.getXgmjzzcqssj(), "yyyy-MM-dd"));
//            }
//            if (GyUtils.isNotNull(cxscwgzInitReqVO.getXgmjzzcqssj())) {
//                cxsQuerymxInitDTO.setSyjzzcz(DateUtils.parseDate(cxscwgzInitReqVO.getXgmjzzczzsj(), "yyyy-MM-dd"));
//            }
//            if (GyUtils.isNotNull(symxGridlb)) {
//                cxsQuerymxInitDTO.setSyuuidList(symxGridlb.stream().map(SymxGridlbVO::getSyuuid).collect(Collectors.toList()));
//            }
//            //使用本地数据测试开关
//            HXZGSB10735Response hxzgsb10735Response = null;
//            if (YHS.getCode().equals(zsxmDm)) {
//                YhsTysbbYtReqDTO yhsTysbbYtReqDTO = BeanUtil.toBean(cxsQuerymxInitDTO, YhsTysbbYtReqDTO.class);
//                final String yskg = CacheUtils.getXtcs("CCHXWSNSSB00002");
//                if ("Y".equals(yskg)) {
//                    hxzgsb10735Response = znsbJyssSsSsjgYhsService.getCxxx1(yhsTysbbYtReqDTO);
//                } else {
//                    hxzgsb10735Response = znsbJyssSsSsjgYhsService.getCxxx(yhsTysbbYtReqDTO);
//                }
//            } else if (CZTDSYS.getCode().equals(zsxmDm)) {
//                hxzgsb10735Response = cztdsysService.cxsInit(cxsQuerymxInitDTO);
//            } else if (FCS.getCode().equals(zsxmDm)) {
//                hxzgsb10735Response = fcsService.cxsInit(cxsQuerymxInitDTO);
//            } else if (YYS.getCode().equals(zsxmDm)) {
//                hxzgsb10735Response = yyssycjService.querymxInitData(cxsQuerymxInitDTO);
//            }
//            if (GyUtils.isNull(hxzgsb10735Response)){
//                return null;
//            }
//
//        }
//        return cxsCwgzinitResponseDTO;
//    }

    /**
     * 保存财行税申报数据 - 错误更正
     *
     * @param cchxwsnssbbxxVO 入参
     * @return 返回值
     */
    @Override
    public Map<String, Object> saveCwgzSbbxx(CchxwsnssbbxxVO cchxwsnssbbxxVO, ZnsbInitProcDataVO znsbInitProcDataVO, CompanyBasicInfoDTO companyBasicInfoDTO, ZnsbWfDataVO znsbWfDataVO, String sfkqgzl) {
        final Map<String, Object> returnMap = new HashMap<>();
        //税源初始化信息，更正只需要提供减免信息，可为空
        CchxwsnssbInitResMxVO initResMxVO = new CchxwsnssbInitResMxVO();
        if (!FtsUtils.isNull(cchxwsnssbbxxVO.getInitResMxVOList())) {
            initResMxVO = cchxwsnssbbxxVO.getInitResMxVOList().get(0);
        }
        //取出入参数据
        final CxscwgzInitReqVO cxscwgzInitReqVO = cchxwsnssbbxxVO.getCxscwgzInitReqVO();
        final String yhlx = cxscwgzInitReqVO.getYhlx();
        String zgswskfjDm = cxscwgzInitReqVO.getZgswskfjDm();
        String hydm = "";
        String xzqhszdm = "";
        String jdxzdm = "";
        String bsrxm = "";
        String bsrsfzjhm = "";
        String djzclxdm = "";
//        if(!YHLX_ZRR.equals(yhlx)){
//        final DwnsrxxVO dwnsrxxVO = CchxwsnssbGyUtils.queryNsrxxByApi(cchxwsnssbbxxVO.getCxscwgzInitReqVO().getDjxh(), cchxwsnssbbxxVO.getCxscwgzInitReqVO().getNsrsbh());
        final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(cxscwgzInitReqVO.getNsrsbh());
        znsbMhzcQyjbxxmxReqVO.setDjxh(cxscwgzInitReqVO.getDjxh());
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsr = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        JbxxmxsjVO jbxxmxsjVO = new JbxxmxsjVO();
        if (!GyUtils.isNull(nsr.getData()) && !GyUtils.isNull(nsr.getData().getJbxxmxsj())) {
//        if(!GyUtils.isNull(dwnsrxxVO)){
            jbxxmxsjVO = nsr.getData().getJbxxmxsj().get(0);
            hydm = GyUtils.isNull(jbxxmxsjVO.getHyDm()) ? "" : jbxxmxsjVO.getHyDm();
            xzqhszdm = GyUtils.isNull(jbxxmxsjVO.getScjydzxzqhszDm()) ? "" : jbxxmxsjVO.getScjydzxzqhszDm();
            jdxzdm = GyUtils.isNull(jbxxmxsjVO.getJdxzDm()) ? "" : jbxxmxsjVO.getJdxzDm();
            bsrxm = GyUtils.isNull(jbxxmxsjVO.getBsrxm()) ? "" : jbxxmxsjVO.getBsrxm();
            bsrsfzjhm = GyUtils.isNull(jbxxmxsjVO.getBsrsfzjhm()) ? "" : jbxxmxsjVO.getBsrsfzjhm();
            djzclxdm = GyUtils.isNull(jbxxmxsjVO.getDjzclxDm()) ? "" : jbxxmxsjVO.getDjzclxDm();
        }
        xzqhszdm = SfEnum.getSsjXzqhszDmByXzqhszDm(xzqhszdm);
//        }
//        else {
//            hydm = CxsGyUtils.getXtcs(zgswskfjDm, "DZSWJ00SBZX020022" , "9519");
//            //自然人机关更正链接获取，暂时未提供，通关查询sb_sbb获取
//            zgswskfjDm = getCwgzzgswskfjDm(cxscwgzInitReqVO.getPzxh(), cxscwgzInitReqVO.getDjxh());
//            if(FtsUtils.isNull(zgswskfjDm)){
//                cchxwsnssbbxxVO.setReturnCode("-9");
//                cchxwsnssbbxxVO.setReturnCode("未查询有效税务机关代码。");
//                returnMap.put("cchxwsnssbbxxVO", cchxwsnssbbxxVO);
//                //保存异步流程业务数据  只有异步流程有这个值
//                if(!CxsGyUtils.isNull(cchxwsnssbbxxVO.getCxscwgzInitReqVO().getYsquuid())){
//                    this.updateSbls(cchxwsnssbbxxVO.getCxscwgzInitReqVO().getYsquuid() , returnMap , CxsSxztbmConstants.getAcceptFail());
//                }
//                return returnMap;
//            }
//        }
        //纳税人信息
        final CxstysbNsrxx cxstysbNsrxx = new CxstysbNsrxx();
        cxstysbNsrxx.setDjxh(cxscwgzInitReqVO.getDjxh());
        cxstysbNsrxx.setNsrsbh(cxscwgzInitReqVO.getNsrsbh());
        cxstysbNsrxx.setNsrmc(cxscwgzInitReqVO.getNsrmc());
        cxstysbNsrxx.setSbsxDm1("11");
        cxstysbNsrxx.setSbrq1(CchxwsnssbGyUtils.getDayofNow());
        cxstysbNsrxx.setHyDm(hydm);
        cxstysbNsrxx.setJdxzDm(jdxzdm);
        cxstysbNsrxx.setYzpzzlDm("BDA0611148");
        cxstysbNsrxx.setXzqhszDm(xzqhszdm);
        cxstysbNsrxx.setZgswskfjDm(zgswskfjDm);
        cxstysbNsrxx.setSkssqq(cxscwgzInitReqVO.getSkssqq());
        cxstysbNsrxx.setSkssqz(cxscwgzInitReqVO.getSkssqz());
        //错误更正标志
        cxstysbNsrxx.setScenceCs("cwgzbz");
        //根据pzxh查询cxstysbuuid
        List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList = znsbNssbCxstysbxxMapper.queryZbmxList(cxscwgzInitReqVO.getPzxh(),"");
        cxstysbNsrxx.setCxstysbuuid(cchxwsnssbzbmxVOList.get(0).getCxstysbuuid());
        cxstysbNsrxx.setBqsfsyzzsxgmnsrjzzc(GyUtils.isNull(initResMxVO.getBqsfsyzzsxgmnsrjzzc()) ? "N" : initResMxVO.getBqsfsyzzsxgmnsrjzzc());
        //根据不同登记注册类型填入不同减征类型特殊处理
        this.dealWithJzzcsyztDm(initResMxVO, cxstysbNsrxx, djzclxdm);
        //2023.10.18 如果不为Y则清空信息
        if (!"Y".equals(cxstysbNsrxx.getBqsfsyzzsxgmnsrjzzc())) {
            cxstysbNsrxx.setJzzcsyztDm("");
            cxstysbNsrxx.setXgmjzzcqssj("");
            cxstysbNsrxx.setXgmjzzczzsj("");
        } else {
            cxstysbNsrxx.setXgmjzzcqssj(GyUtils.isNull(initResMxVO.getXgmjzzcqssj()) ? "" : initResMxVO.getXgmjzzcqssj());
            cxstysbNsrxx.setXgmjzzczzsj(GyUtils.isNull(initResMxVO.getXgmjzzczzsj()) ? "" : initResMxVO.getXgmjzzczzsj());
        }
        //受理信息
        String jbr = "";
        String jbrsfzjhm = "";
        if (!GyUtils.isNull(cchxwsnssbbxxVO.getCchxwsnssbSlxxVO())) {
            jbr = cchxwsnssbbxxVO.getCchxwsnssbSlxxVO().getJbr();
            jbrsfzjhm = cchxwsnssbbxxVO.getCchxwsnssbSlxxVO().getJbrsfzjhm();
        }
        final SbCxstysbSlxx sbCxstysbSlxx = new SbCxstysbSlxx();
        sbCxstysbSlxx.setDljgqz("");
        sbCxstysbSlxx.setJbr(!GyUtils.isNull(jbr) ? jbr : bsrxm);
        sbCxstysbSlxx.setSlr(SjryUtil.getSjry(zgswskfjDm));
        sbCxstysbSlxx.setSlrq(CchxwsnssbGyUtils.getDayofNow());
        sbCxstysbSlxx.setSlswjg(zgswskfjDm);
        sbCxstysbSlxx.setJbrsfzjhm(!GyUtils.isNull(jbrsfzjhm) ? jbrsfzjhm : bsrsfzjhm);
        sbCxstysbSlxx.setDljgtyshxydm("");

        //扩展节点
        final Map<String, String> expand = new HashMap<>();
        expand.put("sjjg", zgswskfjDm);
        //核心接口入参
        final HXZGSB10745Request hxzgsb10745Request = new HXZGSB10745Request();
        final HXZGSB10745Request.SbxxGrid sbxxGrid = new HXZGSB10745Request.SbxxGrid();
        final SbxxGridlb745VO.SymxGrid symxGrid = new SbxxGridlb745VO.SymxGrid();
        //循环税源信息，不可为空
        final List<SymxGridlb745VO> symxGridlb745VOList = new ArrayList<>();
        for (SymxVO symxVO : cchxwsnssbbxxVO.getSymxVOList()) {
            final SymxGridlb745VO symxGridlb745VO = new SymxGridlb745VO();
            symxGridlb745VO.setSyuuid(symxVO.getSyuuid());
            symxGridlb745VO.setSybzDm1(symxVO.getSybzDm1());
            symxGridlb745VOList.add(symxGridlb745VO);
        }
        symxGrid.getSymxGridlb().addAll(symxGridlb745VOList);
        //更正基本信息
        final SbxxGridlb745VO sbxxGridlb745VO = new SbxxGridlb745VO();
        sbxxGridlb745VO.setZsxmDm(cxscwgzInitReqVO.getZsxmDm());
        sbxxGridlb745VO.setSkssqq(cxscwgzInitReqVO.getSkssqq());
        sbxxGridlb745VO.setSkssqz(cxscwgzInitReqVO.getSkssqz());
        sbxxGridlb745VO.setSbuuid(cxscwgzInitReqVO.getSbuuid());
        sbxxGridlb745VO.setPzxh(cxscwgzInitReqVO.getPzxh());
        sbxxGridlb745VO.setYzpzzlDm(getYzpzzlDmByZsxmDm(cxscwgzInitReqVO.getZsxmDm()));
        sbxxGridlb745VO.setSymxGrid(symxGrid);
        sbxxGrid.getSbxxGridlb().add(sbxxGridlb745VO);
        //如果默认为N，则直接使用
        if ("10118".equals(cxscwgzInitReqVO.getZsxmDm()) && "Y".equals(initResMxVO.getGdzysWpxzCsz()) && "N".equals(initResMxVO.getGdzysLslfMrz())) {
            final CxstysbNsrxx bxsNsrVO = this.getBxsNsrVO(cxstysbNsrxx);
            hxzgsb10745Request.setCxstysbnsrxx(bxsNsrVO);
        } else {
            hxzgsb10745Request.setCxstysbnsrxx(cxstysbNsrxx);
        }
        hxzgsb10745Request.setCxstysbnsrxx(cxstysbNsrxx);
        hxzgsb10745Request.setSbxxGrid(sbxxGrid);
        hxzgsb10745Request.setCxstysbslxx(sbCxstysbSlxx);

        ZnsbNssbSbrwDO sbrwdo = znsbNssbSbrwService.getSbrwBySbuuid(cxscwgzInitReqVO.getSbuuid());
        //工作流信息
        CchxwsnssbGzlVO cchxwsnssbGzlVO = new CchxwsnssbGzlVO();
        cchxwsnssbGzlVO.setCchxwsnssbzbmxVOList(cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList());
        cchxwsnssbGzlVO.setCchxwsnssbfbmxVOList(cchxwsnssbbxxVO.getCchxwsnssbfbmxVOList());
        cchxwsnssbGzlVO.setDjxh(cxstysbNsrxx.getDjxh());
        cchxwsnssbGzlVO.setNsrsbh(cxstysbNsrxx.getNsrsbh());
        cchxwsnssbGzlVO.setNsrmc(cxstysbNsrxx.getNsrmc());
        cchxwsnssbGzlVO.setSbrwuuid(sbrwdo.getSbrwuuid());
        cchxwsnssbGzlVO.setZsxmDm(cxscwgzInitReqVO.getZsxmDm());
        cchxwsnssbGzlVO.setSkssqq(cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList().get(0).getSkssqq());
        cchxwsnssbGzlVO.setSkssqz(cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList().get(0).getSkssqz());
        cchxwsnssbGzlVO.setXzqhszDm(xzqhszdm);
        List<CchxwsnssbInitResMxVO> resmxlist = new ArrayList<>();
        resmxlist.add(initResMxVO);
        cchxwsnssbGzlVO.setInitResMxVOList(resmxlist);
        List<String> syuuidList = cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList().stream().map(CchxwsnssbzbmxVO::getSyuuid).distinct().collect(Collectors.toList());
        cchxwsnssbGzlVO.setSyuuid(syuuidList);
        cchxwsnssbGzlVO.setNssblcbz("cwgz");

        //返回值
        final List<CxstysbSaveReturnVO> cxstysbSaveReturnVOList = new ArrayList<>();
        CxsRequestDTO cxsRequestDTO = new CxsRequestDTO();
        this.cxsCwgzSaveRequsetBW(cxscwgzInitReqVO.getZsxmDm(), cxsRequestDTO, cxstysbNsrxx, sbxxGrid, sbCxstysbSlxx, sbrwdo, jbxxmxsjVO);
        cchxwsnssbGzlVO.setCxsRequestDTO(cxsRequestDTO);
        cxsGyUtils.savecwgzcl(sfkqgzl, cxstysbNsrxx, sbxxGrid, sbCxstysbSlxx, cxscwgzInitReqVO, cchxwsnssbGzlVO,
                znsbInitProcDataVO, companyBasicInfoDTO, sbrwdo, syuuidList, initResMxVO, znsbWfDataVO);
//
//
//        final String ywbw = JsonUtils.toJson(cxsRequestDTO);
//        log.info("财行税提交申报保存请求:{}",ywbw);
//        final SjjhDTO sjjhDTO = new SjjhDTO();
//        sjjhDTO.setSjjhlxDm("CXS0000002");
//        sjjhDTO.setYwbm(YzpzzlEnum.CXS.getDm());
//        sjjhDTO.setYwuuid(sbrwdo.getSbrwuuid());
//        sjjhDTO.setDjxh(cxstysbNsrxx.getDjxh());
//        sjjhDTO.setNsrsbh(cxstysbNsrxx.getNsrsbh());
//        sjjhDTO.setXzqhszDm(cxstysbNsrxx.getXzqhszDm());
//        sjjhDTO.setBwnr(ywbw);
//        CommonResult<Object> commonResult = sjjhService.saveSjjhJob(sjjhDTO);
//        if(commonResult.getCode() != 1){
//            return returnMap;
//        }else {
//            CxsBcResponseDTO cxsBcResponseDTO = (CxsBcResponseDTO) commonResult.getData();
//            if(!"00".equals(cxsBcResponseDTO.getReturncode())){
//                return returnMap;
//            }
//        }
//        try{
//            final HXZGSB10745Response hxzgsb10745Response = Gt3Invoker.invoke("SWZJ.HXZG.SB.CWGZSAVECXSTYSB", expand, hxzgsb10745Request, HXZGSB10745Response.class);
//            if(!FtsUtils.isNull(hxzgsb10745Response) && !FtsUtils.isNull(hxzgsb10745Response.getReturnGrid()) && !FtsUtils.isNull(hxzgsb10745Response.getReturnGrid().getReturnGridlb())){
//                cxstysbSaveReturnVOList.addAll(hxzgsb10745Response.getReturnGrid().getReturnGridlb());
//            }
//        } catch (Exception e) {
//            log.info("hhhhhhhhhhhhhhhhhhhhhinit"+ FcsCztdsyssycjUtils.getTrace(e));
//            final String message = e.getMessage();
//            cchxwsnssbbxxVO.setReturnCode("-9");
//            if(!FcsCztdsyssycjUtils.isNull(message) && message.contains("税款状态为扣款锁定，不能进行申报错误更正") && message.contains("1010010097000045")){
//                cchxwsnssbbxxVO.setReturnMsg("税款状态为扣款锁定，不能进行申报错误更正！");
//            } else if(!FcsCztdsyssycjUtils.isNull(message) && message.contains("该笔申报信息已经作废,无需更正") && message.contains("1010410006000045")){
//                cchxwsnssbbxxVO.setReturnMsg("该笔申报信息已经作废,无需更正！");
//            } else {
//                cchxwsnssbbxxVO.setReturnMsg(message);
//            }
//        }
//
//        //凭证序号集合
//        final List<String> stringList = new ArrayList<>();
//        //处理保存结果信息
//        if (!GyUtils.isNull(cxstysbSaveReturnVOList)) {
//            for (CxstysbSaveReturnVO saveReturnVO : cxstysbSaveReturnVOList) {
//                final List<SBSaveReturnVO> savereturnGridlb = saveReturnVO.getSavereturnGrid().getSavereturnGridlb();
//                for (SBSaveReturnVO sbSaveReturnVO : savereturnGridlb) {
//                    stringList.add(sbSaveReturnVO.getPzxh());
//                }
//            }
//        }

        //查询纳税人未清缴(欠税费)信息
        final List<CchxwsnssbjfxxVO> jfxxmcVOList = new ArrayList<>();
//        this.queryYjsfHX(cxscwgzInitReqVO.getDjxh(), new HashSet<>(), zgswskfjDm, stringList, new ArrayList<>(), jfxxmcVOList);

        //对无需缴费申报信息进行组装
        final List<CchxwsnssbjfxxVO> sfxxAllVOListTemp = new ArrayList<>();
        this.dealWithYjsf(jfxxmcVOList, cxstysbSaveReturnVOList, cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList(), sfxxAllVOListTemp);
        //放入应缴税费并排序
        sfxxAllVOListTemp.addAll(jfxxmcVOList);
        final List<CchxwsnssbjfxxVO> sfxxAllVOList = sfxxAllVOListTemp.stream().sorted(Comparator.comparing(CchxwsnssbjfxxVO::getZsxmDm)).collect(Collectors.toList());

        //组装返回值
        returnMap.put("cxstysbSaveReturnVO", cxstysbSaveReturnVOList);
        returnMap.put("cchxwsnssbbxxVO", cchxwsnssbbxxVO);
        returnMap.put("jfxxmcVOList", jfxxmcVOList);
        returnMap.put("sfxxAllVOList", sfxxAllVOList);

//        //保存异步流程业务数据  只有异步流程有这个值
//        if(!CxsGyUtils.isNull(cchxwsnssbbxxVO.getCxscwgzInitReqVO().getYsquuid())){
//            this.updateSbls(cchxwsnssbbxxVO.getCxscwgzInitReqVO().getYsquuid() , returnMap,CxsSxztbmConstants.getAcceptSuccess());
//        }

//        //好差评接入
//        final CxstysbNsrxxVO cxstysbNsrxxVO = new CxstysbNsrxxVO();
//        cxstysbNsrxxVO.setDjxh(cxscwgzInitReqVO.getDjxh());
//        cxstysbNsrxxVO.setNsrsbh(cxscwgzInitReqVO.getNsrsbh());
//        cxstysbNsrxxVO.setUserId(cxscwgzInitReqVO.getUserId());//录入人身份ID
//        cxstysbNsrxxVO.setZgswskfj_dm(cxscwgzInitReqVO.getZgswskfjDm());//受理税务机关代码
//        this.sendHcpMessage(cxstysbSaveReturnVOList, cxstysbNsrxxVO, cchxwsnssbbxxVO.getCchxwsnssbzbmxVOList());

        return returnMap;
    }

    public boolean saveCxstysbCwgz(CxstysbNsrxx cxstysbNsrxx, HXZGSB10745Request.SbxxGrid sbxxGrid, SbCxstysbSlxx sbCxstysbSlxx, CxscwgzInitReqVO cxscwgzInitReqVO, CchxwsnssbGzlVO cchxwsnssbGzlVO) {
//        CxsRequestDTO cxsRequestDTO = new CxsRequestDTO();
//        this.cxsCwgzSaveRequsetBW(cxsRequestDTO,cxstysbNsrxx,sbxxGrid,sbCxstysbSlxx);
        final String ywbw = JsonUtils.toJson(cchxwsnssbGzlVO.getCxsRequestDTO());
        log.info("财行税提交申报保存请求:{}", ywbw);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        sjjhDTO.setSjjhlxDm("CXS0000002");
        sjjhDTO.setYwbm(YzpzzlEnum.CXS.getDm());
        ZnsbNssbSbrwDO sbrwdo = znsbNssbSbrwService.getSbrwBySbuuid(cxscwgzInitReqVO.getSbuuid());
        sjjhDTO.setYwuuid(sbrwdo.getSbrwuuid());
        sjjhDTO.setDjxh(cxstysbNsrxx.getDjxh());
        sjjhDTO.setNsrsbh(cxstysbNsrxx.getNsrsbh());
        sjjhDTO.setXzqhszDm(cxstysbNsrxx.getXzqhszDm());
        sjjhDTO.setBwnr(ywbw);
        CommonResult<Object> commonResult = sjjhService.saveSjjhJob(sjjhDTO);
        if (commonResult.getCode() != 1) {
            return false;
        } else {
            CxsBcResponseDTO cxsBcResponseDTO = (CxsBcResponseDTO) commonResult.getData();
            if (!"00".equals(cxsBcResponseDTO.getReturncode())) {
                return false;
            }
        }
        return true;
    }

    @SneakyThrows
    private void cwgzywbw(String zsxmDm, CxsRequestDTO cxsRequestDTO, CxstysbNsrxx cxstysbNsrxx, HXZGSB10745Request.SbxxGrid sbxxGrid, SbCxstysbSlxx sbCxstysbSlxx, String mxid) {
        CxsywbwDTO cxsywbwDTO = new CxsywbwDTO();
        // SXA061041005
        cxsywbwDTO.setSwsxDm("SXA061041005");
        cxsywbwDTO.setDjxh(cxstysbNsrxx.getDjxh());
        cxsywbwDTO.setSwjgDm(cxstysbNsrxx.getZgswskfjDm().substring(0, 3) + "00000000");
        cxsywbwDTO.setSkssqq(DateUtils.parseDate(cxstysbNsrxx.getSkssqq(), "yyyy-MM-dd"));
        cxsywbwDTO.setSkssqz(DateUtils.parseDate(cxstysbNsrxx.getSkssqz(), "yyyy-MM-dd"));
        cxsywbwDTO.setGzbz("Y");
        cxsywbwDTO.setMxid(mxid);
        // 处理申报数据
        CxsYwbwSbsjDTO cxsYwbwSbsjDTO = this.cwgzsbsj(zsxmDm, cxstysbNsrxx, sbxxGrid, sbCxstysbSlxx);
        cxsywbwDTO.setSbsj(Base64Utils.encode(JacksonUtils.toJson(cxsYwbwSbsjDTO)));
        List<CxsywbwDTO> cxsywbwDTOS = new ArrayList<>();
        cxsywbwDTOS.add(cxsywbwDTO);
        cxsRequestDTO.setYwbw(cxsywbwDTOS);
    }

    @SneakyThrows
    private CxsYwbwSbsjDTO cwgzsbsj(String zsxmDm, CxstysbNsrxx cxstysbNsrxx, HXZGSB10745Request.SbxxGrid sbxxGrid, SbCxstysbSlxx sbCxstysbSlxx) {
        CxsYwbwSbsjDTO cxsYwbwSbsjDTO = new CxsYwbwSbsjDTO();
        //保存财行税通用申报业务报文
        CxstysbnsrxxDTO cxstysbnsrxxDTO = BeanUtil.copyProperties(cxstysbNsrxx, CxstysbnsrxxDTO.class);
        cxstysbnsrxxDTO.setSkssqq(GyUtils.isNotNull(cxstysbNsrxx.getSkssqq()) ? DateUtils.parseDate(cxstysbNsrxx.getSkssqq(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setSkssqz(GyUtils.isNotNull(cxstysbNsrxx.getSkssqz()) ? DateUtils.parseDate(cxstysbNsrxx.getSkssqz(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setSbrq1(GyUtils.isNotNull(cxstysbNsrxx.getSbrq1()) ? DateUtils.parseDate(cxstysbNsrxx.getSbrq1(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setXgmjzzcqssj(GyUtils.isNotNull(cxstysbNsrxx.getXgmjzzcqssj()) ? DateUtils.parseDate(cxstysbNsrxx.getXgmjzzcqssj(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setXgmjzzczzsj(GyUtils.isNotNull(cxstysbNsrxx.getXgmjzzczzsj()) ? DateUtils.parseDate(cxstysbNsrxx.getXgmjzzczzsj(), "yyyy-MM-dd") : null);
        cxstysbnsrxxDTO.setYzpzzlDm(getYzpzzlDmByZsxmDm(zsxmDm));
        cxstysbnsrxxDTO.setScenceCs("cwgzbz");
        // 保存税种列表
        CxssbxxGridDTO cxssbxxGridDTO = new CxssbxxGridDTO();
        List<CxssbxxGridlbDTO> sbxxGridlb = sbxxGrid.getSbxxGridlb().stream().map(e -> {
            CxssbxxGridlbDTO cxssbxxGridlbDTO = new CxssbxxGridlbDTO();
            cxssbxxGridlbDTO.setZsxmDm(e.getZsxmDm());
            cxssbxxGridlbDTO.setPzxh(e.getPzxh());
            cxssbxxGridlbDTO.setSbuuid(e.getSbuuid());
            cxssbxxGridlbDTO.setSkssqq(DateUtil.parse(e.getSkssqq(), "yyyy-MM-dd"));
            cxssbxxGridlbDTO.setSkssqz(DateUtil.parse(e.getSkssqz(), "yyyy-MM-dd"));
            cxssbxxGridlbDTO.setYzpzzlDm(getYzpzzlDmByZsxmDm(zsxmDm));
            List<CxssymxDTO> cxssymxDTOS = BeanUtil.copyToList(e.getSymxGrid().getSymxGridlb(), CxssymxDTO.class);
            CxssymxGridDTO cxssymxGridDTO = new CxssymxGridDTO();
            cxssymxGridDTO.setSymxGridlb(cxssymxDTOS);
            cxssbxxGridlbDTO.setSymxGrid(cxssymxGridDTO);
            return cxssbxxGridlbDTO;
        }).collect(Collectors.toList());
        cxssbxxGridDTO.setSbxxGridlb(sbxxGridlb);
        // 受理信息
        CxstysbslxxDTO cxstysbslxxDTO = BeanUtil.copyProperties(sbCxstysbSlxx, CxstysbslxxDTO.class);
        cxstysbslxxDTO.setSlrq(GyUtils.isNotNull(sbCxstysbSlxx.getSlrq()) ? DateUtils.parseDate(sbCxstysbSlxx.getSlrq(), "yyyy-MM-dd") : null);
        cxsYwbwSbsjDTO.setCxstysbnsrxx(cxstysbnsrxxDTO);
        cxsYwbwSbsjDTO.setSbxxGrid(cxssbxxGridDTO);
        cxsYwbwSbsjDTO.setCxstysbslxx(cxstysbslxxDTO);
        return cxsYwbwSbsjDTO;
    }

    @SneakyThrows
    private void cxsCwgzSaveRequsetBW(String zsxmDm, CxsRequestDTO cxsRequestDTO, CxstysbNsrxx cxstysbNsrxx, HXZGSB10745Request.SbxxGrid sbxxGrid, SbCxstysbSlxx sbCxstysbSlxx,
                                      ZnsbNssbSbrwDO znsbNssbSbrwDO, JbxxmxsjVO jbxxmxsjVO) {
        cxsRequestDTO.setRequestId(GyUtils.getUuid());
        if (GyUtils.isNotNull(znsbNssbSbrwDO.getPclsh())) {
            cxsRequestDTO.setPclsh(znsbNssbSbrwDO.getPclsh());
        }
        final String mxid = GyUtils.getUuid();
        //jcbw基础报文处理
        jcbw(cxsRequestDTO, jbxxmxsjVO);
        //ywbw业务报文处理
        cwgzywbw(zsxmDm, cxsRequestDTO, cxstysbNsrxx, sbxxGrid, sbCxstysbSlxx, mxid);
        //ssjg业务报文处理
        ssjg(cxsRequestDTO, cxstysbNsrxx, zsxmDm, znsbNssbSbrwDO.getSyuuid1(), mxid);
    }
}