package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税一般纳税人本期应纳税额按预算科目分解表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrbqynseayskmfjbFormVO", propOrder = { "ybzsxmBqybtse", "ybzsxmZzsFjbl", "ybzsxmZzsBqybtse", "ybzsxmGzzzsFjbl", "ybzsxmGzzzsBqybtse", "jzjtzsxmBqybtse", "jzjtzsxmZzsFjbl", "jzjtzsxmZzsBqybtse", "jzjtzsxmGzzzsFjbl", "jzjtzsxmGzzzsBqybtse" })
@Getter
@Setter
public class ZzsybnsrbqynseayskmfjbFormVO {
    /**
     * 一般征税项目本期应补退税额
     */
    protected BigDecimal ybzsxmBqybtse;

    /**
     * 一般征税项目增值税分解比例
     */
    protected BigDecimal ybzsxmZzsFjbl;

    /**
     * 一般征税项目增值税本期应补退税额
     */
    protected BigDecimal ybzsxmZzsBqybtse;

    /**
     * 一般征税项目改征增值税分解比例
     */
    protected BigDecimal ybzsxmGzzzsFjbl;

    /**
     * 一般征税项目改征增值税本期应补退税额
     */
    protected BigDecimal ybzsxmGzzzsBqybtse;

    /**
     * 即征即退征税项目本期应补退税额
     */
    protected BigDecimal jzjtzsxmBqybtse;

    /**
     * 即征即退征税项目增值税分解比例
     */
    protected BigDecimal jzjtzsxmZzsFjbl;

    /**
     * 即征即退征税项目增值税本期应补退税额
     */
    protected BigDecimal jzjtzsxmZzsBqybtse;

    /**
     * 即征即退征税项目改征增值税分解比例
     */
    protected BigDecimal jzjtzsxmGzzzsFjbl;

    /**
     * 即征即退征税项目改征增值税本期应补退税额
     */
    protected BigDecimal jzjtzsxmGzzzsBqybtse;
}