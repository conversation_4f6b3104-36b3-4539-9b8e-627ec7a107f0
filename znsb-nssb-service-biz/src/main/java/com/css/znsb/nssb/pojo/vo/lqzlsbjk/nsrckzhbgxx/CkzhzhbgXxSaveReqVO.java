package com.css.znsb.nssb.pojo.vo.lqzlsbjk.nsrckzhbgxx;

import com.css.znsb.nssb.pojo.vo.lqzlsbjk.xmlVO.DJjwskyhzhxxVO;
import com.css.znsb.nssb.pojo.vo.lqzlsbjk.JbxxVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 纳税人存款账户账号报告表
 *
 * <AUTHOR>
 * @title:
 * @description
 * @createDate 2024/7/22 17:18
 */
@NoArgsConstructor
@Data
public class CkzhzhbgXxSaveReqVO implements Serializable {
    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    //    @ApiModelProperty("纳税人基本信息")
//    @JsonProperty(value="JbxxVO")
    private JbxxVO jbxxVO;

    //    @ApiModelProperty("银行账户信息")
//    @JsonProperty(value="YhzhxxVOList")
    private List<YhzhxxVO> yhzhxxVOList;

    //    @ApiModelProperty("境外收款银行账户信息")
//    @JsonProperty(value="jwskyhzhxxVOList")
    private List<DJjwskyhzhxxVO> jwskyhzhxxVOList;

    /**
     * 1显示“同步注册地银行账户信息”按钮； 2显示“同步总机构账户信息”按钮； 3显示“同步银行账户信息”按钮
     */
//    @ApiModelProperty("填写方式")
//    @JsonProperty(value = "TxfsDm")
    private String txfsDm;

//    @JsonProperty(value = "sjswjgdm")
    private String sjswjgdm;

//    @JsonProperty(value = "ywqddm")
    private String ywqddm;

//    @JsonProperty(value = "sjry")
    private String sjry;
}
