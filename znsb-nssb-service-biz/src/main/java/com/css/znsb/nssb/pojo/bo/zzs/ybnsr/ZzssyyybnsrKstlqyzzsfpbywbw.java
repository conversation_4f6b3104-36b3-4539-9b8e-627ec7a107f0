package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《跨省铁路企业增值税分配表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_kstlqyzzsfpbywbw", propOrder = { "zzssyyybnsrKstlqyzzsfpb" })
@Getter
@Setter
public class ZzssyyybnsrKstlqyzzsfpbywbw extends TaxDoc {
    /**
     * 跨省铁路企业增值税分配表
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_kstlqyzzsfpb", required = true)
    @JSONField(name = "zzssyyybnsr_kstlqyzzsfpb")
    protected ZzssyyybnsrKstlqyzzsfpb zzssyyybnsrKstlqyzzsfpb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrKstlqyzzsfpb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrKstlqyzzsfpb {}
}