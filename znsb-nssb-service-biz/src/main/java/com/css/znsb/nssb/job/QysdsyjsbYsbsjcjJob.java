package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.plsb.QysdsyjYsbsjcjService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class QysdsyjsbYsbsjcjJob {

    @Resource
    private QysdsyjYsbsjcjService qysdsyjYsbsjcjService;

    @XxlJob("qysdsyjsbYsbsjcjJob")
    public void execute() {
        log.info("开始执行企业所得税预缴申报预填预申报数据采集任务");
        qysdsyjYsbsjcjService.handlerYsbsj();
        log.info("企业所得税预缴申报预填预申报数据采集任务执行完成");
    }
}
