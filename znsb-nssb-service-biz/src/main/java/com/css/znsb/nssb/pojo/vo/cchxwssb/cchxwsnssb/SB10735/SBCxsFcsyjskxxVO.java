
package com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 房产税应缴税款信息表VO
 * 
 * <p>SBCxsFcsyjskxxVO complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SBCxsFcsyjskxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="jsbl" type="{http://www.chinatax.gov.cn/dataspec/}jsbl"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz"/>
 *         &lt;element name="sl1" type="{http://www.chinatax.gov.cn/dataspec/}sl1"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="jzlx" type="{http://www.chinatax.gov.cn/dataspec/}jzlx"/>
 *         &lt;element name="sjtbSj" type="{http://www.chinatax.gov.cn/dataspec/}sjtbSj"/>
 *         &lt;element name="jmmj3" type="{http://www.chinatax.gov.cn/dataspec/}jmmj3"/>
 *         &lt;element name="jmsxmmc3" type="{http://www.chinatax.gov.cn/dataspec/}jmsxmmc3"/>
 *         &lt;element name="ssjmxzdm1" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzdm1"/>
 *         &lt;element name="fyxxuuid" type="{http://www.chinatax.gov.cn/dataspec/}fyxxuuid"/>
 *         &lt;element name="ysuuid" type="{http://www.chinatax.gov.cn/dataspec/}ysuuid"/>
 *         &lt;element name="jmmj1" type="{http://www.chinatax.gov.cn/dataspec/}jmmj1"/>
 *         &lt;element name="czwyz" type="{http://www.chinatax.gov.cn/dataspec/}czwyz"/>
 *         &lt;element name="fybh" type="{http://www.chinatax.gov.cn/dataspec/}fybh"/>
 *         &lt;element name="yjmsje3" type="{http://www.chinatax.gov.cn/dataspec/}yjmsje3"/>
 *         &lt;element name="ssjmxzdm3" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzdm3"/>
 *         &lt;element name="jmmj2" type="{http://www.chinatax.gov.cn/dataspec/}jmmj2"/>
 *         &lt;element name="jmsxmmc2" type="{http://www.chinatax.gov.cn/dataspec/}jmsxmmc2"/>
 *         &lt;element name="yjmsje1" type="{http://www.chinatax.gov.cn/dataspec/}yjmsje1"/>
 *         &lt;element name="ybtse" type="{http://www.chinatax.gov.cn/dataspec/}ybtse"/>
 *         &lt;element name="yjse" type="{http://www.chinatax.gov.cn/dataspec/}yjse"/>
 *         &lt;element name="fcyz" type="{http://www.chinatax.gov.cn/dataspec/}fcyz"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="yjmsje2" type="{http://www.chinatax.gov.cn/dataspec/}yjmsje2"/>
 *         &lt;element name="jmse" type="{http://www.chinatax.gov.cn/dataspec/}jmse"/>
 *         &lt;element name="phjmse" type="{http://www.chinatax.gov.cn/dataspec/}phjmse" minOccurs="0"/>
 *         &lt;element name="ynse" type="{http://www.chinatax.gov.cn/dataspec/}ynse"/>
 *         &lt;element name="sbuuid" type="{http://www.chinatax.gov.cn/dataspec/}sbuuid"/>
 *         &lt;element name="skuuid" type="{http://www.chinatax.gov.cn/dataspec/}skuuid"/>
 *         &lt;element name="sbzjsr" type="{http://www.chinatax.gov.cn/dataspec/}sbzjsr"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm"/>
 *         &lt;element name="ssjmxzdm2" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzdm2"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz"/>
 *         &lt;element name="ssny" type="{http://www.chinatax.gov.cn/dataspec/}ssny"/>
 *         &lt;element name="sysyuuid" type="{http://www.chinatax.gov.cn/dataspec/}sysyuuid"/>
 *         &lt;element name="jmsxmmc1" type="{http://www.chinatax.gov.cn/dataspec/}jmsxmmc1"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq"/>
 *         &lt;element name="jsyj" type="{http://www.chinatax.gov.cn/dataspec/}jsyj"/>
 *         &lt;element name="phjzbl" type="{http://www.chinatax.gov.cn/dataspec/}phjzbl" minOccurs="0"/>
 *         &lt;element name="phjmxzDm" type="{http://www.chinatax.gov.cn/dataspec/}phjmxzDm" minOccurs="0"/>
 *         &lt;element name="phjmswsxDm" type="{http://www.chinatax.gov.cn/dataspec/}phjmswsxDm" minOccurs="0"/>
 *         &lt;element name="jmxxmxGrid" minOccurs="0">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="jmxxmxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBCxsSbjmxxmxVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBCxsFcsyjskxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "jsbl",
    "skssqz",
    "sl1",
    "djxh",
    "jzlx",
    "sjtbSj",
    "jmmj3",
    "jmsxmmc3",
    "ssjmxzdm1",
    "fyxxuuid",
    "ysuuid",
    "jmmj1",
    "czwyz",
    "fybh",
    "yjmsje3",
    "ssjmxzdm3",
    "jmmj2",
    "jmsxmmc2",
    "yjmsje1",
    "ybtse",
    "yjse",
    "fcyz",
    "lrrDm",
    "yjmsje2",
    "jmse",
    "phjmse",
    "ynse",
    "sbuuid",
    "skuuid",
    "sbzjsr",
    "sjgsdq",
    "xgrDm",
    "ssjmxzdm2",
    "yxbz",
    "ssny",
    "sysyuuid",
    "jmsxmmc1",
    "skssqq",
    "jsyj",
    "phjzbl",
    "phjmxzDm",
    "phjmswsxDm",
    "jmxxmxGrid"
})
public class SBCxsFcsyjskxxVO
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double jsbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String skssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double sl1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String jzlx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String sjtbSj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double jmmj3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String jmsxmmc3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String ssjmxzdm1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String fyxxuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String ysuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double jmmj1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double czwyz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String fybh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double yjmsje3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String ssjmxzdm3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double jmmj2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String jmsxmmc2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double yjmsje1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double ybtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double yjse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double fcyz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double yjmsje2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double jmse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double phjmse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double ynse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String sbuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String skuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double sbzjsr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String ssjmxzdm2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String yxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String ssny;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String sysyuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String jmsxmmc1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String skssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double jsyj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double phjzbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String phjmxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String phjmswsxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected JmxxmxGrid jmxxmxGrid;

    /**
     * 获取jsbl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJsbl() {
        return jsbl;
    }

    /**
     * 设置jsbl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJsbl(Double value) {
        this.jsbl = value;
    }

    /**
     * 获取skssqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * 设置skssqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * 获取sl1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSl1() {
        return sl1;
    }

    /**
     * 设置sl1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSl1(Double value) {
        this.sl1 = value;
    }

    /**
     * 获取djxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * 设置djxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * 获取jzlx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJzlx() {
        return jzlx;
    }

    /**
     * 设置jzlx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJzlx(String value) {
        this.jzlx = value;
    }

    /**
     * 获取sjtbSj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjtbSj() {
        return sjtbSj;
    }

    /**
     * 设置sjtbSj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjtbSj(String value) {
        this.sjtbSj = value;
    }

    /**
     * 获取jmmj3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJmmj3() {
        return jmmj3;
    }

    /**
     * 设置jmmj3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJmmj3(Double value) {
        this.jmmj3 = value;
    }

    /**
     * 获取jmsxmmc3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmsxmmc3() {
        return jmsxmmc3;
    }

    /**
     * 设置jmsxmmc3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmsxmmc3(String value) {
        this.jmsxmmc3 = value;
    }

    /**
     * 获取ssjmxzdm1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmxzdm1() {
        return ssjmxzdm1;
    }

    /**
     * 设置ssjmxzdm1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmxzdm1(String value) {
        this.ssjmxzdm1 = value;
    }

    /**
     * 获取fyxxuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFyxxuuid() {
        return fyxxuuid;
    }

    /**
     * 设置fyxxuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFyxxuuid(String value) {
        this.fyxxuuid = value;
    }

    /**
     * 获取ysuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYsuuid() {
        return ysuuid;
    }

    /**
     * 设置ysuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYsuuid(String value) {
        this.ysuuid = value;
    }

    /**
     * 获取jmmj1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJmmj1() {
        return jmmj1;
    }

    /**
     * 设置jmmj1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJmmj1(Double value) {
        this.jmmj1 = value;
    }

    /**
     * 获取czwyz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getCzwyz() {
        return czwyz;
    }

    /**
     * 设置czwyz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setCzwyz(Double value) {
        this.czwyz = value;
    }

    /**
     * 获取fybh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFybh() {
        return fybh;
    }

    /**
     * 设置fybh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFybh(String value) {
        this.fybh = value;
    }

    /**
     * 获取yjmsje3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjmsje3() {
        return yjmsje3;
    }

    /**
     * 设置yjmsje3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjmsje3(Double value) {
        this.yjmsje3 = value;
    }

    /**
     * 获取ssjmxzdm3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmxzdm3() {
        return ssjmxzdm3;
    }

    /**
     * 设置ssjmxzdm3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmxzdm3(String value) {
        this.ssjmxzdm3 = value;
    }

    /**
     * 获取jmmj2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJmmj2() {
        return jmmj2;
    }

    /**
     * 设置jmmj2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJmmj2(Double value) {
        this.jmmj2 = value;
    }

    /**
     * 获取jmsxmmc2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmsxmmc2() {
        return jmsxmmc2;
    }

    /**
     * 设置jmsxmmc2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmsxmmc2(String value) {
        this.jmsxmmc2 = value;
    }

    /**
     * 获取yjmsje1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjmsje1() {
        return yjmsje1;
    }

    /**
     * 设置yjmsje1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjmsje1(Double value) {
        this.yjmsje1 = value;
    }

    /**
     * 获取ybtse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYbtse() {
        return ybtse;
    }

    /**
     * 设置ybtse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYbtse(Double value) {
        this.ybtse = value;
    }

    /**
     * 获取yjse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjse() {
        return yjse;
    }

    /**
     * 设置yjse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjse(Double value) {
        this.yjse = value;
    }

    /**
     * 获取fcyz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getFcyz() {
        return fcyz;
    }

    /**
     * 设置fcyz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setFcyz(Double value) {
        this.fcyz = value;
    }

    /**
     * 获取lrrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * 设置lrrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * 获取yjmsje2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjmsje2() {
        return yjmsje2;
    }

    /**
     * 设置yjmsje2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjmsje2(Double value) {
        this.yjmsje2 = value;
    }

    /**
     * 获取jmse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJmse() {
        return jmse;
    }

    /**
     * 设置jmse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJmse(Double value) {
        this.jmse = value;
    }

    /**
     * 获取phjmse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getPhjmse() {
        return phjmse;
    }

    /**
     * 设置phjmse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setPhjmse(Double value) {
        this.phjmse = value;
    }

    /**
     * 获取ynse属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYnse() {
        return ynse;
    }

    /**
     * 设置ynse属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYnse(Double value) {
        this.ynse = value;
    }

    /**
     * 获取sbuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbuuid() {
        return sbuuid;
    }

    /**
     * 设置sbuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbuuid(String value) {
        this.sbuuid = value;
    }

    /**
     * 获取skuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkuuid() {
        return skuuid;
    }

    /**
     * 设置skuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkuuid(String value) {
        this.skuuid = value;
    }

    /**
     * 获取sbzjsr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSbzjsr() {
        return sbzjsr;
    }

    /**
     * 设置sbzjsr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSbzjsr(Double value) {
        this.sbzjsr = value;
    }

    /**
     * 获取sjgsdq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * 设置sjgsdq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * 获取xgrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * 设置xgrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * 获取ssjmxzdm2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmxzdm2() {
        return ssjmxzdm2;
    }

    /**
     * 设置ssjmxzdm2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmxzdm2(String value) {
        this.ssjmxzdm2 = value;
    }

    /**
     * 获取yxbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxbz() {
        return yxbz;
    }

    /**
     * 设置yxbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxbz(String value) {
        this.yxbz = value;
    }

    /**
     * 获取ssny属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsny() {
        return ssny;
    }

    /**
     * 设置ssny属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsny(String value) {
        this.ssny = value;
    }

    /**
     * 获取sysyuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSysyuuid() {
        return sysyuuid;
    }

    /**
     * 设置sysyuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSysyuuid(String value) {
        this.sysyuuid = value;
    }

    /**
     * 获取jmsxmmc1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJmsxmmc1() {
        return jmsxmmc1;
    }

    /**
     * 设置jmsxmmc1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJmsxmmc1(String value) {
        this.jmsxmmc1 = value;
    }

    /**
     * 获取skssqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * 设置skssqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * 获取jsyj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJsyj() {
        return jsyj;
    }

    /**
     * 设置jsyj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJsyj(Double value) {
        this.jsyj = value;
    }

    /**
     * 获取phjzbl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getPhjzbl() {
        return phjzbl;
    }

    /**
     * 设置phjzbl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setPhjzbl(Double value) {
        this.phjzbl = value;
    }

    /**
     * 获取phjmxzDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPhjmxzDm() {
        return phjmxzDm;
    }

    /**
     * 设置phjmxzDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPhjmxzDm(String value) {
        this.phjmxzDm = value;
    }

    /**
     * 获取phjmswsxDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPhjmswsxDm() {
        return phjmswsxDm;
    }

    /**
     * 设置phjmswsxDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPhjmswsxDm(String value) {
        this.phjmswsxDm = value;
    }

    /**
     * 获取jmxxmxGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JmxxmxGrid }
     *     
     */
    public JmxxmxGrid getJmxxmxGrid() {
        return jmxxmxGrid;
    }

    /**
     * 设置jmxxmxGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JmxxmxGrid }
     *     
     */
    public void setJmxxmxGrid(JmxxmxGrid value) {
        this.jmxxmxGrid = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="jmxxmxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBCxsSbjmxxmxVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "jmxxmxGridlb"
    })
    public static class JmxxmxGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        private List<SBCxsSbjmxxmxVO> jmxxmxGridlb;

        /**
         * Gets the value of the jmxxmxGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jmxxmxGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getJmxxmxGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SBCxsSbjmxxmxVO }
         * 
         * 
         */
        public List<SBCxsSbjmxxmxVO> getJmxxmxGridlb() {
            if (jmxxmxGridlb == null) {
                jmxxmxGridlb = new ArrayList<SBCxsSbjmxxmxVO>();
            }
            return this.jmxxmxGridlb;
        }

    }

}
