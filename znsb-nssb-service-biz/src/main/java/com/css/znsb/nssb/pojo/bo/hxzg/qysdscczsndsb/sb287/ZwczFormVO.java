
package com.css.znsb.nssb.pojo.bo.hxzg.qysdscczsndsb.sb287;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 企业重组所得税特殊性税务处理报告表(债务重组)_主表
 * 
 * <p>zwczFormVO complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="zwczFormVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="sbqymc" type="{http://www.chinatax.gov.cn/dataspec/}nsrmc" minOccurs="0"/>
 *         &lt;element name="zwczfs" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="zwzmjz" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zwrczywjsjc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yfzkjsjc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qtyfkjsjc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jkjsjc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qtzwjsjc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zcgyjz" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="xjin" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="yhck" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="fhbzc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qtjsjc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zwrzwczsd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="zwrbndynssde" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="sdebz" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="gqzzwrqbgqbl" type="{http://www.chinatax.gov.cn/dataspec/}bl" minOccurs="0"/>
 *         &lt;element name="gyjz" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jsjc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bqrzwczsd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zwczFormVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "sbqymc",
    "zwczfs",
    "zwzmjz",
    "zwrczywjsjc",
    "yfzkjsjc",
    "qtyfkjsjc",
    "jkjsjc",
    "qtzwjsjc",
    "zcgyjz",
    "xjin",
    "yhck",
    "fhbzc",
    "qtjsjc",
    "zwrzwczsd",
    "zwrbndynssde",
    "sdebz",
    "gqzzwrqbgqbl",
    "gyjz",
    "jsjc",
    "bqrzwczsd"
})
public class ZwczFormVO implements Serializable {

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -3185569838253289231L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbqymc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zwczfs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zwzmjz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zwrczywjsjc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yfzkjsjc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qtyfkjsjc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jkjsjc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qtzwjsjc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zcgyjz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double xjin;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yhck;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double fhbzc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qtjsjc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zwrzwczsd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zwrbndynssde;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double sdebz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double gqzzwrqbgqbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double gyjz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jsjc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bqrzwczsd;

    /**
     * 获取sbqymc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbqymc() {
        return sbqymc;
    }

    /**
     * 设置sbqymc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbqymc(String value) {
        this.sbqymc = value;
    }

    /**
     * 获取zwczfs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZwczfs() {
        return zwczfs;
    }

    /**
     * 设置zwczfs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZwczfs(String value) {
        this.zwczfs = value;
    }

    /**
     * 获取zwzmjz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZwzmjz() {
        return zwzmjz;
    }

    /**
     * 设置zwzmjz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZwzmjz(Double value) {
        this.zwzmjz = value;
    }

    /**
     * 获取zwrczywjsjc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZwrczywjsjc() {
        return zwrczywjsjc;
    }

    /**
     * 设置zwrczywjsjc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZwrczywjsjc(Double value) {
        this.zwrczywjsjc = value;
    }

    /**
     * 获取yfzkjsjc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYfzkjsjc() {
        return yfzkjsjc;
    }

    /**
     * 设置yfzkjsjc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYfzkjsjc(Double value) {
        this.yfzkjsjc = value;
    }

    /**
     * 获取qtyfkjsjc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQtyfkjsjc() {
        return qtyfkjsjc;
    }

    /**
     * 设置qtyfkjsjc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQtyfkjsjc(Double value) {
        this.qtyfkjsjc = value;
    }

    /**
     * 获取jkjsjc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJkjsjc() {
        return jkjsjc;
    }

    /**
     * 设置jkjsjc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJkjsjc(Double value) {
        this.jkjsjc = value;
    }

    /**
     * 获取qtzwjsjc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQtzwjsjc() {
        return qtzwjsjc;
    }

    /**
     * 设置qtzwjsjc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQtzwjsjc(Double value) {
        this.qtzwjsjc = value;
    }

    /**
     * 获取zcgyjz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZcgyjz() {
        return zcgyjz;
    }

    /**
     * 设置zcgyjz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZcgyjz(Double value) {
        this.zcgyjz = value;
    }

    /**
     * 获取xjin属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getXjin() {
        return xjin;
    }

    /**
     * 设置xjin属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setXjin(Double value) {
        this.xjin = value;
    }

    /**
     * 获取yhck属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYhck() {
        return yhck;
    }

    /**
     * 设置yhck属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYhck(Double value) {
        this.yhck = value;
    }

    /**
     * 获取fhbzc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getFhbzc() {
        return fhbzc;
    }

    /**
     * 设置fhbzc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setFhbzc(Double value) {
        this.fhbzc = value;
    }

    /**
     * 获取qtjsjc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQtjsjc() {
        return qtjsjc;
    }

    /**
     * 设置qtjsjc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQtjsjc(Double value) {
        this.qtjsjc = value;
    }

    /**
     * 获取zwrzwczsd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZwrzwczsd() {
        return zwrzwczsd;
    }

    /**
     * 设置zwrzwczsd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZwrzwczsd(Double value) {
        this.zwrzwczsd = value;
    }

    /**
     * 获取zwrbndynssde属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZwrbndynssde() {
        return zwrbndynssde;
    }

    /**
     * 设置zwrbndynssde属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZwrbndynssde(Double value) {
        this.zwrbndynssde = value;
    }

    /**
     * 获取sdebz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSdebz() {
        return sdebz;
    }

    /**
     * 设置sdebz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSdebz(Double value) {
        this.sdebz = value;
    }

    /**
     * 获取gqzzwrqbgqbl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getGqzzwrqbgqbl() {
        return gqzzwrqbgqbl;
    }

    /**
     * 设置gqzzwrqbgqbl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setGqzzwrqbgqbl(Double value) {
        this.gqzzwrqbgqbl = value;
    }

    /**
     * 获取gyjz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getGyjz() {
        return gyjz;
    }

    /**
     * 设置gyjz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setGyjz(Double value) {
        this.gyjz = value;
    }

    /**
     * 获取jsjc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJsjc() {
        return jsjc;
    }

    /**
     * 设置jsjc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJsjc(Double value) {
        this.jsjc = value;
    }

    /**
     * 获取bqrzwczsd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBqrzwczsd() {
        return bqrzwczsd;
    }

    /**
     * 设置bqrzwczsd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBqrzwczsd(Double value) {
        this.bqrzwczsd = value;
    }

}
