package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《年度航空运输企业年度清算表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_ndhkysqyqsb", propOrder = { "sbbheadVO", "ndhkysqyqsbGrid" })
@Getter
@Setter
public class ZzsybnsrsbNdhkysqyqsb {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbVO sbbheadVO;

    /**
     * 《年度航空运输企业年度清算表》
     */
    @XmlElement(nillable = true, required = true)
    protected NdhkysqyqsbGrid ndhkysqyqsbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "ndhkysqyqsbGridlb" })
    @Getter
    @Setter
    public static class NdhkysqyqsbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<NdhkysqyqsbGridlb> ndhkysqyqsbGridlb;

        /**
         * Gets the value of the ndhkysqyqsbGridlb property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the ndhkysqyqsbGridlb property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getNdhkysqyqsbGridlb().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link NdhkysqyqsbGridlb}
         */
        public List<NdhkysqyqsbGridlb> getNdhkysqyqsbGridlb() {
            if (ndhkysqyqsbGridlb == null) {
                ndhkysqyqsbGridlb = new ArrayList<NdhkysqyqsbGridlb>();
            }
            return this.ndhkysqyqsbGridlb;
        }

        /**
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "")
        @Getter
        @Setter
        public static class NdhkysqyqsbGridlb extends NdhkysqyqsbGridlbVO {}
    }
}