package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《跨省铁路企业增值税分配表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_kstlqyzzsfpb", propOrder = { "kstlqyzzsfpbGrid", "kstlqyzzsfpbForm" })
@XmlSeeAlso({ ZzssyyybnsrKstlqyzzsfpbywbw.ZzssyyybnsrKstlqyzzsfpb.class })
@Getter
@Setter
public class ZzssyyybnsrKstlqyzzsfpb {
    @XmlElement(nillable = true, required = true)
    protected KstlqyzzsfpbGrid kstlqyzzsfpbGrid;

    /**
     * 跨省铁路企业增值税分配表
     */
    @XmlElement(nillable = true, required = true)
    protected KstlqyzzsfpbFormVO kstlqyzzsfpbForm;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "kstlqyzzsfpbGridlbVO" })
    @Getter
    @Setter
    public static class KstlqyzzsfpbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<KstlqyzzsfpbGridlbVO> kstlqyzzsfpbGridlbVO;

        /**
         * Gets the value of the kstlqyzzsfpbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the kstlqyzzsfpbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getKstlqyzzsfpbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link KstlqyzzsfpbGridlbVO}
         */
        public List<KstlqyzzsfpbGridlbVO> getKstlqyzzsfpbGridlbVO() {
            if (kstlqyzzsfpbGridlbVO == null) {
                kstlqyzzsfpbGridlbVO = new ArrayList<KstlqyzzsfpbGridlbVO>();
            }
            return this.kstlqyzzsfpbGridlbVO;
        }
    }
}