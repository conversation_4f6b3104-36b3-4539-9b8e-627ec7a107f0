package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.SbrwGjReqDTO;
import com.css.znsb.nssb.pojo.vo.lqzlsbjk.nsrckzhbgxx.CxNsrckzhbgxxResVO;
import com.css.znsb.nssb.pojo.vo.lqzlsbjk.nsrckzhbgxx.GetSameLqCkzhzhxxReqVO;
import com.css.znsb.nssb.service.lqzlsbjk.nsrckzhzhxx.INsrckzhzhbgxxService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 数据采集定时任务
 */
@Slf4j
@Component
public class CkzhzhbgJob {


    @Resource
    private CompanyApi companyApi;

    @Resource
    private INsrckzhzhbgxxService nsrckzhzhbgxxService;


//    private String djxh;
//    private String nsrsbh;
//    private String xzqhszDm;
    /**
     * 申报任务采集
     */
    @XxlJob("CkzhzhbgJob")
    public void execute() {
        final CommonResult<List<CompanyInfoResDTO>> result = companyApi.getAllCompanyInfo();
        log.info("==========开始同步存款账户========== ");
        log.info("获取到全部企业信息：" + JsonUtils.toJson(result.getData()));
        if (GyUtils.isNull(result.getData())) {
            log.debug("没有配置企业信息，归集任务结束");
            return;
        }
        for (CompanyInfoResDTO companyInfo : result.getData()) {
            final SbrwGjReqDTO sbrwgjReq = BeanUtils.toBean(companyInfo, SbrwGjReqDTO.class);
            final String djxh = sbrwgjReq.getDjxh();
            final String nsrsbh = sbrwgjReq.getNsrsbh();
            final String xzqhszDm = sbrwgjReq.getXzqhszDm();
            log.info("执行定时任务 djxh:"+djxh+" nsrsbh:"+nsrsbh+" xzqhszDm:"+xzqhszDm);
            GetSameLqCkzhzhxxReqVO getSameLqCkzhzhxxReqVO = new GetSameLqCkzhzhxxReqVO();
            getSameLqCkzhzhxxReqVO.setDjxh(djxh);
            getSameLqCkzhzhxxReqVO.setNsrsbh(nsrsbh);
            getSameLqCkzhzhxxReqVO.setXzqhszDm(xzqhszDm);
            CxNsrckzhbgxxResVO resVo = nsrckzhzhbgxxService.queryNsrckzhzhbgxx(getSameLqCkzhzhxxReqVO);
            log.info("执行定时任务 resVo:"+JsonUtils.toJson(resVo));
        }
        log.info("==========同步存款账户完成========== ");
    }
}
