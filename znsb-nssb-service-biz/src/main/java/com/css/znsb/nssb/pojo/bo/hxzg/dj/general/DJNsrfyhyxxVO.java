
package com.css.znsb.nssb.pojo.bo.hxzg.dj.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 纳税人附营行业信息
 * 
 * <p>Java class for DJNsrfyhyxxVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DJNsrfyhyxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="fyhyuuid" type="{http://www.chinatax.gov.cn/dataspec/}fyhyuuid"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="xh" type="{http://www.chinatax.gov.cn/dataspec/}xh"/>
 *         &lt;element name="hyDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq" minOccurs="0"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DJNsrfyhyxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "fyhyuuid",
    "djxh",
    "xh",
    "hyDm",
    "yxbz",
    "lrrDm",
    "lrrq",
    "xgrDm",
    "xgrq",
    "sjgsdq"
})
public class DJNsrfyhyxxVO
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fyhyuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected int xh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hyDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;

    /**
     * Gets the value of the fyhyuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFyhyuuid() {
        return fyhyuuid;
    }

    /**
     * Sets the value of the fyhyuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFyhyuuid(String value) {
        this.fyhyuuid = value;
    }

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the xh property.
     * 
     */
    public int getXh() {
        return xh;
    }

    /**
     * Sets the value of the xh property.
     * 
     */
    public void setXh(int value) {
        this.xh = value;
    }

    /**
     * Gets the value of the hyDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHyDm() {
        return hyDm;
    }

    /**
     * Sets the value of the hyDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHyDm(String value) {
        this.hyDm = value;
    }

    /**
     * Gets the value of the yxbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxbz() {
        return yxbz;
    }

    /**
     * Sets the value of the yxbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxbz(String value) {
        this.yxbz = value;
    }

    /**
     * Gets the value of the lrrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * Sets the value of the lrrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * Gets the value of the lrrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * Sets the value of the lrrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * Gets the value of the xgrDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * Sets the value of the xgrDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * Gets the value of the xgrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * Sets the value of the xgrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * Gets the value of the sjgsdq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * Sets the value of the sjgsdq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

}
