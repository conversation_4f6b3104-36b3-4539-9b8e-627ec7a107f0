package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车销售统一发票清单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdcxstyfpqd", propOrder = { "sbbhead", "jdcxstyfpqdGrid" })
@XmlSeeAlso({ ZzssyyybnsrJdcxstyfpqdywbw.ZzssyyybnsrJdcxstyfpqd.class })
@Getter
@Setter
public class ZzssyyybnsrJdcxstyfpqd {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 机动车销售统一发票清单
     */
    @XmlElement(nillable = true, required = true)
    protected JdcxstyfpqdGrid jdcxstyfpqdGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "jdcxstyfpqdGridlbVO" })
    @Getter
    @Setter
    public static class JdcxstyfpqdGrid {
        @XmlElement(nillable = true, required = true)
        protected List<JdcxstyfpqdGridlbVO> jdcxstyfpqdGridlbVO;

        /**
         * Gets the value of the jdcxstyfpqdGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jdcxstyfpqdGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getJdcxstyfpqdGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link JdcxstyfpqdGridlbVO}
         */
        public List<JdcxstyfpqdGridlbVO> getJdcxstyfpqdGridlbVO() {
            if (jdcxstyfpqdGridlbVO == null) {
                jdcxstyfpqdGridlbVO = new ArrayList<JdcxstyfpqdGridlbVO>();
            }
            return this.jdcxstyfpqdGridlbVO;
        }
    }
}