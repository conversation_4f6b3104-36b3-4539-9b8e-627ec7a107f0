package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《农产品核定扣除增值税进项税额计算表（汇总表）》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ncphdkczzsjxsejsbGridlbVO", propOrder = { "scjyBz", "scjyJxse", "zjxsBz", "zjxsJxse", "schwCbfBz", "schwCbfJxse", "schwTrccfBz", "schwTrccfJxse" })
@Getter
@Setter
public class NcphdkczzsjxsejsbGridlbVO {
    /**
     * 购进农产品用于生产经营且不构成货物实体备注
     */
    @XmlElement(nillable = true, required = true)
    protected String scjyBz;

    /**
     * 购进农产品用于生产经营且不构成货物实体当期允许抵扣农产品增值税进项税额元
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal scjyJxse;

    /**
     * 购进农产品直接销售备注
     */
    @XmlElement(nillable = true, required = true)
    protected String zjxsBz;

    /**
     * 购进农产品直接销售当期允许抵扣农产品增值税进项税额元
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal zjxsJxse;

    /**
     * 以购进农产品为原料生产货物成本法备注
     */
    @XmlElement(nillable = true, required = true)
    protected String schwCbfBz;

    /**
     * 以购进农产品为原料生产货物成本法当期允许抵扣农产品增值税进项税额元
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal schwCbfJxse;

    /**
     * 以购进农产品为原料生产货物投入产出法备注
     */
    @XmlElement(nillable = true, required = true)
    protected String schwTrccfBz;

    /**
     * 以购进农产品为原料生产货物投入产出法当期允许抵扣农产品增值税进项税额元
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal schwTrccfJxse;
}