package com.css.znsb.nssb.pojo.vo.cchxwssb.tdzzs.wp.save;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TdzzsSyWpSbxxVO {

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSyuuid() {
        return syuuid;
    }

    public void setSyuuid(String syuuid) {
        this.syuuid = syuuid;
    }

    public String getDzbzdszlDm() {
        return dzbzdszlDm;
    }

    public void setDzbzdszlDm(String dzbzdszlDm) {
        this.dzbzdszlDm = dzbzdszlDm;
    }

    public String getDjxh() {
        return djxh;
    }

    public void setDjxh(String djxh) {
        this.djxh = djxh;
    }

    public String getZrfdcsrzePtzz() {
        return zrfdcsrzePtzz;
    }

    public void setZrfdcsrzePtzz(String zrfdcsrzePtzz) {
        this.zrfdcsrzePtzz = zrfdcsrzePtzz;
    }

    public String getZrfdcsrzeFptzz() {
        return zrfdcsrzeFptzz;
    }

    public void setZrfdcsrzeFptzz(String zrfdcsrzeFptzz) {
        this.zrfdcsrzeFptzz = zrfdcsrzeFptzz;
    }

    public String getZrfdcsrzeQtfclx() {
        return zrfdcsrzeQtfclx;
    }

    public void setZrfdcsrzeQtfclx(String zrfdcsrzeQtfclx) {
        this.zrfdcsrzeQtfclx = zrfdcsrzeQtfclx;
    }

    public String getZrfdcsrzeHj() {
        return zrfdcsrzeHj;
    }

    public void setZrfdcsrzeHj(String zrfdcsrzeHj) {
        this.zrfdcsrzeHj = zrfdcsrzeHj;
    }

    public String getHbsrPtzz() {
        return hbsrPtzz;
    }

    public void setHbsrPtzz(String hbsrPtzz) {
        this.hbsrPtzz = hbsrPtzz;
    }

    public String getHbsrFptzz() {
        return hbsrFptzz;
    }

    public void setHbsrFptzz(String hbsrFptzz) {
        this.hbsrFptzz = hbsrFptzz;
    }

    public String getHbsrQtfclx() {
        return hbsrQtfclx;
    }

    public void setHbsrQtfclx(String hbsrQtfclx) {
        this.hbsrQtfclx = hbsrQtfclx;
    }

    public String getHbsrHj() {
        return hbsrHj;
    }

    public void setHbsrHj(String hbsrHj) {
        this.hbsrHj = hbsrHj;
    }

    public String getSwsrjqtsrPtzz() {
        return swsrjqtsrPtzz;
    }

    public void setSwsrjqtsrPtzz(String swsrjqtsrPtzz) {
        this.swsrjqtsrPtzz = swsrjqtsrPtzz;
    }

    public String getSwsrjqtsrFptzz() {
        return swsrjqtsrFptzz;
    }

    public void setSwsrjqtsrFptzz(String swsrjqtsrFptzz) {
        this.swsrjqtsrFptzz = swsrjqtsrFptzz;
    }

    public String getSwsrjqtsrQtfclx() {
        return swsrjqtsrQtfclx;
    }

    public void setSwsrjqtsrQtfclx(String swsrjqtsrQtfclx) {
        this.swsrjqtsrQtfclx = swsrjqtsrQtfclx;
    }

    public String getSwsrjqtsrHj() {
        return swsrjqtsrHj;
    }

    public void setSwsrjqtsrHj(String swsrjqtsrHj) {
        this.swsrjqtsrHj = swsrjqtsrHj;
    }

    public String getStxssrPtzz() {
        return stxssrPtzz;
    }

    public void setStxssrPtzz(String stxssrPtzz) {
        this.stxssrPtzz = stxssrPtzz;
    }

    public String getStxssrFptzz() {
        return stxssrFptzz;
    }

    public void setStxssrFptzz(String stxssrFptzz) {
        this.stxssrFptzz = stxssrFptzz;
    }

    public String getStxssrQtfclx() {
        return stxssrQtfclx;
    }

    public void setStxssrQtfclx(String stxssrQtfclx) {
        this.stxssrQtfclx = stxssrQtfclx;
    }

    public String getStxssrHj() {
        return stxssrHj;
    }

    public void setStxssrHj(String stxssrHj) {
        this.stxssrHj = stxssrHj;
    }

    public String getKcxmjehjPtzz() {
        return kcxmjehjPtzz;
    }

    public void setKcxmjehjPtzz(String kcxmjehjPtzz) {
        this.kcxmjehjPtzz = kcxmjehjPtzz;
    }

    public String getKcxmjehjFptzz() {
        return kcxmjehjFptzz;
    }

    public void setKcxmjehjFptzz(String kcxmjehjFptzz) {
        this.kcxmjehjFptzz = kcxmjehjFptzz;
    }

    public String getKcxmjehjQtfclx() {
        return kcxmjehjQtfclx;
    }

    public void setKcxmjehjQtfclx(String kcxmjehjQtfclx) {
        this.kcxmjehjQtfclx = kcxmjehjQtfclx;
    }

    public String getKcxmjehj() {
        return kcxmjehj;
    }

    public void setKcxmjehj(String kcxmjehj) {
        this.kcxmjehj = kcxmjehj;
    }

    public String getXsmjPtzz() {
        return xsmjPtzz;
    }

    public void setXsmjPtzz(String xsmjPtzz) {
        this.xsmjPtzz = xsmjPtzz;
    }

    public String getXsmjFptzz() {
        return xsmjFptzz;
    }

    public void setXsmjFptzz(String xsmjFptzz) {
        this.xsmjFptzz = xsmjFptzz;
    }

    public String getXsmjQtfclx() {
        return xsmjQtfclx;
    }

    public void setXsmjQtfclx(String xsmjQtfclx) {
        this.xsmjQtfclx = xsmjQtfclx;
    }

    public String getXsmjHj() {
        return xsmjHj;
    }

    public void setXsmjHj(String xsmjHj) {
        this.xsmjHj = xsmjHj;
    }

    public String getDwcbfyPtzz() {
        return dwcbfyPtzz;
    }

    public void setDwcbfyPtzz(String dwcbfyPtzz) {
        this.dwcbfyPtzz = dwcbfyPtzz;
    }

    public String getDwcbfyFptzz() {
        return dwcbfyFptzz;
    }

    public void setDwcbfyFptzz(String dwcbfyFptzz) {
        this.dwcbfyFptzz = dwcbfyFptzz;
    }

    public String getDwcbfyQtfclx() {
        return dwcbfyQtfclx;
    }

    public void setDwcbfyQtfclx(String dwcbfyQtfclx) {
        this.dwcbfyQtfclx = dwcbfyQtfclx;
    }

    public String getDwcbfyHj() {
        return dwcbfyHj;
    }

    public void setDwcbfyHj(String dwcbfyHj) {
        this.dwcbfyHj = dwcbfyHj;
    }

    public String getBcyzrfdcygdsjPtzz() {
        return bcyzrfdcygdsjPtzz;
    }

    public void setBcyzrfdcygdsjPtzz(String bcyzrfdcygdsjPtzz) {
        this.bcyzrfdcygdsjPtzz = bcyzrfdcygdsjPtzz;
    }

    public String getBcyzrfdcygdsjFptzz() {
        return bcyzrfdcygdsjFptzz;
    }

    public void setBcyzrfdcygdsjFptzz(String bcyzrfdcygdsjFptzz) {
        this.bcyzrfdcygdsjFptzz = bcyzrfdcygdsjFptzz;
    }

    public String getBcyzrfdcygdsjQtfclx() {
        return bcyzrfdcygdsjQtfclx;
    }

    public void setBcyzrfdcygdsjQtfclx(String bcyzrfdcygdsjQtfclx) {
        this.bcyzrfdcygdsjQtfclx = bcyzrfdcygdsjQtfclx;
    }

    public String getBcyzrfdcygdsjHj() {
        return bcyzrfdcygdsjHj;
    }

    public void setBcyzrfdcygdsjHj(String bcyzrfdcygdsjHj) {
        this.bcyzrfdcygdsjHj = bcyzrfdcygdsjHj;
    }

    public String getYysPtzz() {
        return yysPtzz;
    }

    public void setYysPtzz(String yysPtzz) {
        this.yysPtzz = yysPtzz;
    }

    public String getYysFptzz() {
        return yysFptzz;
    }

    public void setYysFptzz(String yysFptzz) {
        this.yysFptzz = yysFptzz;
    }

    public String getYysQtfclx() {
        return yysQtfclx;
    }

    public void setYysQtfclx(String yysQtfclx) {
        this.yysQtfclx = yysQtfclx;
    }

    public String getYysHj() {
        return yysHj;
    }

    public void setYysHj(String yysHj) {
        this.yysHj = yysHj;
    }

    public String getCswhjssPtzz() {
        return cswhjssPtzz;
    }

    public void setCswhjssPtzz(String cswhjssPtzz) {
        this.cswhjssPtzz = cswhjssPtzz;
    }

    public String getCswhjssFptzz() {
        return cswhjssFptzz;
    }

    public void setCswhjssFptzz(String cswhjssFptzz) {
        this.cswhjssFptzz = cswhjssFptzz;
    }

    public String getCswhjssQtfclx() {
        return cswhjssQtfclx;
    }

    public void setCswhjssQtfclx(String cswhjssQtfclx) {
        this.cswhjssQtfclx = cswhjssQtfclx;
    }

    public String getCswhjssHj() {
        return cswhjssHj;
    }

    public void setCswhjssHj(String cswhjssHj) {
        this.cswhjssHj = cswhjssHj;
    }

    public String getJyffjPtzz() {
        return jyffjPtzz;
    }

    public void setJyffjPtzz(String jyffjPtzz) {
        this.jyffjPtzz = jyffjPtzz;
    }

    public String getJyffjFptzz() {
        return jyffjFptzz;
    }

    public void setJyffjFptzz(String jyffjFptzz) {
        this.jyffjFptzz = jyffjFptzz;
    }

    public String getJyffjQtfclx() {
        return jyffjQtfclx;
    }

    public void setJyffjQtfclx(String jyffjQtfclx) {
        this.jyffjQtfclx = jyffjQtfclx;
    }

    public String getJyffjHj() {
        return jyffjHj;
    }

    public void setJyffjHj(String jyffjHj) {
        this.jyffjHj = jyffjHj;
    }

    public String getZzePtzz() {
        return zzePtzz;
    }

    public void setZzePtzz(String zzePtzz) {
        this.zzePtzz = zzePtzz;
    }

    public String getZzeFptzz() {
        return zzeFptzz;
    }

    public void setZzeFptzz(String zzeFptzz) {
        this.zzeFptzz = zzeFptzz;
    }

    public String getZzeQtfclx() {
        return zzeQtfclx;
    }

    public void setZzeQtfclx(String zzeQtfclx) {
        this.zzeQtfclx = zzeQtfclx;
    }

    public String getZzeHj() {
        return zzeHj;
    }

    public void setZzeHj(String zzeHj) {
        this.zzeHj = zzeHj;
    }

    public String getZzeykcxmjezbPtzz() {
        return zzeykcxmjezbPtzz;
    }

    public void setZzeykcxmjezbPtzz(String zzeykcxmjezbPtzz) {
        this.zzeykcxmjezbPtzz = zzeykcxmjezbPtzz;
    }

    public String getZzeykcxmjezbFptzz() {
        return zzeykcxmjezbFptzz;
    }

    public void setZzeykcxmjezbFptzz(String zzeykcxmjezbFptzz) {
        this.zzeykcxmjezbFptzz = zzeykcxmjezbFptzz;
    }

    public String getZzeykcxmjezbQtfclx() {
        return zzeykcxmjezbQtfclx;
    }

    public void setZzeykcxmjezbQtfclx(String zzeykcxmjezbQtfclx) {
        this.zzeykcxmjezbQtfclx = zzeykcxmjezbQtfclx;
    }

    public String getSyslPtzz() {
        return syslPtzz;
    }

    public void setSyslPtzz(String syslPtzz) {
        this.syslPtzz = syslPtzz;
    }

    public String getSyslFptzz() {
        return syslFptzz;
    }

    public void setSyslFptzz(String syslFptzz) {
        this.syslFptzz = syslFptzz;
    }

    public String getSyslQtfclx() {
        return syslQtfclx;
    }

    public void setSyslQtfclx(String syslQtfclx) {
        this.syslQtfclx = syslQtfclx;
    }

    public String getSskcxsPtzz() {
        return sskcxsPtzz;
    }

    public void setSskcxsPtzz(String sskcxsPtzz) {
        this.sskcxsPtzz = sskcxsPtzz;
    }

    public String getSskcxsFptzz() {
        return sskcxsFptzz;
    }

    public void setSskcxsFptzz(String sskcxsFptzz) {
        this.sskcxsFptzz = sskcxsFptzz;
    }

    public String getSskcxsQtfclx() {
        return sskcxsQtfclx;
    }

    public void setSskcxsQtfclx(String sskcxsQtfclx) {
        this.sskcxsQtfclx = sskcxsQtfclx;
    }

    public String getYjtdzzssePtzz() {
        return yjtdzzssePtzz;
    }

    public void setYjtdzzssePtzz(String yjtdzzssePtzz) {
        this.yjtdzzssePtzz = yjtdzzssePtzz;
    }

    public String getYjtdzzsseFptzz() {
        return yjtdzzsseFptzz;
    }

    public void setYjtdzzsseFptzz(String yjtdzzsseFptzz) {
        this.yjtdzzsseFptzz = yjtdzzsseFptzz;
    }

    public String getYjtdzzsseQtfclx() {
        return yjtdzzsseQtfclx;
    }

    public void setYjtdzzsseQtfclx(String yjtdzzsseQtfclx) {
        this.yjtdzzsseQtfclx = yjtdzzsseQtfclx;
    }

    public String getYjtdzzsseHj() {
        return yjtdzzsseHj;
    }

    public void setYjtdzzsseHj(String yjtdzzsseHj) {
        this.yjtdzzsseHj = yjtdzzsseHj;
    }

    public String getJmsePtzz() {
        return jmsePtzz;
    }

    public void setJmsePtzz(String jmsePtzz) {
        this.jmsePtzz = jmsePtzz;
    }

    public String getJmseFptzz() {
        return jmseFptzz;
    }

    public void setJmseFptzz(String jmseFptzz) {
        this.jmseFptzz = jmseFptzz;
    }

    public String getJmseQtfclx() {
        return jmseQtfclx;
    }

    public void setJmseQtfclx(String jmseQtfclx) {
        this.jmseQtfclx = jmseQtfclx;
    }

    public String getJmseHj() {
        return jmseHj;
    }

    public void setJmseHj(String jmseHj) {
        this.jmseHj = jmseHj;
    }

    public String getJmxzdmmcPtzz() {
        return jmxzdmmcPtzz;
    }

    public void setJmxzdmmcPtzz(String jmxzdmmcPtzz) {
        this.jmxzdmmcPtzz = jmxzdmmcPtzz;
    }

    public String getJmxzdmmcFptzz() {
        return jmxzdmmcFptzz;
    }

    public void setJmxzdmmcFptzz(String jmxzdmmcFptzz) {
        this.jmxzdmmcFptzz = jmxzdmmcFptzz;
    }

    public String getJmxzdmmcQtfclx() {
        return jmxzdmmcQtfclx;
    }

    public void setJmxzdmmcQtfclx(String jmxzdmmcQtfclx) {
        this.jmxzdmmcQtfclx = jmxzdmmcQtfclx;
    }

    public String getYijtdzzssePtzz() {
        return yijtdzzssePtzz;
    }

    public void setYijtdzzssePtzz(String yijtdzzssePtzz) {
        this.yijtdzzssePtzz = yijtdzzssePtzz;
    }

    public String getYijtdzzsseFptzz() {
        return yijtdzzsseFptzz;
    }

    public void setYijtdzzsseFptzz(String yijtdzzsseFptzz) {
        this.yijtdzzsseFptzz = yijtdzzsseFptzz;
    }

    public String getYijtdzzsseQtfclx() {
        return yijtdzzsseQtfclx;
    }

    public void setYijtdzzsseQtfclx(String yijtdzzsseQtfclx) {
        this.yijtdzzsseQtfclx = yijtdzzsseQtfclx;
    }

    public String getYijtdzzsseHj() {
        return yijtdzzsseHj;
    }

    public void setYijtdzzsseHj(String yijtdzzsseHj) {
        this.yijtdzzsseHj = yijtdzzsseHj;
    }

    public String getYbtdzzssePtzz() {
        return ybtdzzssePtzz;
    }

    public void setYbtdzzssePtzz(String ybtdzzssePtzz) {
        this.ybtdzzssePtzz = ybtdzzssePtzz;
    }

    public String getYbtdzzsseFptzz() {
        return ybtdzzsseFptzz;
    }

    public void setYbtdzzsseFptzz(String ybtdzzsseFptzz) {
        this.ybtdzzsseFptzz = ybtdzzsseFptzz;
    }

    public String getYbtdzzsseQtfclx() {
        return ybtdzzsseQtfclx;
    }

    public void setYbtdzzsseQtfclx(String ybtdzzsseQtfclx) {
        this.ybtdzzsseQtfclx = ybtdzzsseQtfclx;
    }

    public String getYbtdzzsseHj() {
        return ybtdzzsseHj;
    }

    public void setYbtdzzsseHj(String ybtdzzsseHj) {
        this.ybtdzzsseHj = ybtdzzsseHj;
    }

    public String getPtzzUuid() {
        return ptzzUuid;
    }

    public void setPtzzUuid(String ptzzUuid) {
        this.ptzzUuid = ptzzUuid;
    }

    public String getFptzzUuid() {
        return fptzzUuid;
    }

    public void setFptzzUuid(String fptzzUuid) {
        this.fptzzUuid = fptzzUuid;
    }

    public String getQtfclxUuid() {
        return qtfclxUuid;
    }

    public void setQtfclxUuid(String qtfclxUuid) {
        this.qtfclxUuid = qtfclxUuid;
    }

    public String getZzeykcxmjezbPtzz3() {
        return zzeykcxmjezbPtzz3;
    }

    public void setZzeykcxmjezbPtzz3(String zzeykcxmjezbPtzz3) {
        this.zzeykcxmjezbPtzz3 = zzeykcxmjezbPtzz3;
    }

    public String getSyslPtzz3() {
        return syslPtzz3;
    }

    public void setSyslPtzz3(String syslPtzz3) {
        this.syslPtzz3 = syslPtzz3;
    }

    public String getSskcxsPtzz3() {
        return sskcxsPtzz3;
    }

    public void setSskcxsPtzz3(String sskcxsPtzz3) {
        this.sskcxsPtzz3 = sskcxsPtzz3;
    }

    public String getZzeykcxmjezbFptzz3() {
        return zzeykcxmjezbFptzz3;
    }

    public void setZzeykcxmjezbFptzz3(String zzeykcxmjezbFptzz3) {
        this.zzeykcxmjezbFptzz3 = zzeykcxmjezbFptzz3;
    }

    public String getSyslFptzz3() {
        return syslFptzz3;
    }

    public void setSyslFptzz3(String syslFptzz3) {
        this.syslFptzz3 = syslFptzz3;
    }

    public String getSskcxsFptzz3() {
        return sskcxsFptzz3;
    }

    public void setSskcxsFptzz3(String sskcxsFptzz3) {
        this.sskcxsFptzz3 = sskcxsFptzz3;
    }

    public String getZzeykcxmjezbQtfclx3() {
        return zzeykcxmjezbQtfclx3;
    }

    public void setZzeykcxmjezbQtfclx3(String zzeykcxmjezbQtfclx3) {
        this.zzeykcxmjezbQtfclx3 = zzeykcxmjezbQtfclx3;
    }

    public String getSyslQtfclx3() {
        return syslQtfclx3;
    }

    public void setSyslQtfclx3(String syslQtfclx3) {
        this.syslQtfclx3 = syslQtfclx3;
    }

    public String getSskcxsQtfclx3() {
        return sskcxsQtfclx3;
    }

    public void setSskcxsQtfclx3(String sskcxsQtfclx3) {
        this.sskcxsQtfclx3 = sskcxsQtfclx3;
    }

    public String getSyslPtzz2() {
        return syslPtzz2;
    }

    public void setSyslPtzz2(String syslPtzz2) {
        this.syslPtzz2 = syslPtzz2;
    }

    public String getSyslFptzz2() {
        return syslFptzz2;
    }

    public void setSyslFptzz2(String syslFptzz2) {
        this.syslFptzz2 = syslFptzz2;
    }

    public String getSyslQtfclx2() {
        return syslQtfclx2;
    }

    public void setSyslQtfclx2(String syslQtfclx2) {
        this.syslQtfclx2 = syslQtfclx2;
    }

    /**
     * uuid
     */
    @JsonProperty(value = "uuid")
    private String uuid;

    /**
     * 税源uuid
     */
    @JsonProperty(value = "syuuid")
    private String syuuid;

    /**
     * dzbzdszldm
     */
    @JsonProperty(value = "dzbzdszlDm")
    private String dzbzdszlDm;

    /**
     * 登记序号
     */
    @JsonProperty(value = "djxh")
    private String djxh;

    /**
     * 转让房地产收入总额清算数
     */
    @JsonProperty(value = "zrfdcsrzePtzz")
    private String zrfdcsrzePtzz;

    /**
     * 转让房地产收入总额清算数非普通住宅
     */
    @JsonProperty(value = "zrfdcsrzeFptzz")
    private String zrfdcsrzeFptzz;

    /**
     *转让房地产收入总额清算数其他房产类型
     */
    @JsonProperty(value = "zrfdcsrzeQtfclx")
    private String zrfdcsrzeQtfclx;

    /**
     *转让房地产收入总额清算数合计
     */
    @JsonProperty(value = "zrfdcsrzeHj")
    private String zrfdcsrzeHj;

    /**
     * 货币收入
     */
    @JsonProperty(value = "hbsrPtzz")
    private String hbsrPtzz;

    /**
     * 货币收入
     */
    @JsonProperty(value = "hbsrFptzz")
    private String hbsrFptzz;

    /**
     * 货币收入
     */
    @JsonProperty(value = "hbsrQtfclx")
    private String hbsrQtfclx;

    /**
     * 货币收入合计
     */
    @JsonProperty(value = "hbsrHj")
    private String hbsrHj;

    /**
     * 实物收入及其他收入
     */
    @JsonProperty(value = "swsrjqtsrPtzz")
    private String swsrjqtsrPtzz;

    /**
     * 实物收入及其他收入
     */
    @JsonProperty(value = "swsrjqtsrFptzz")
    private String swsrjqtsrFptzz;

    /**
     * 实物收入及其他收入
     */
    @JsonProperty(value = "swsrjqtsrQtfclx")
    private String swsrjqtsrQtfclx;

    /**
     * 实物收入及其他收入合计
     */
    @JsonProperty(value = "swsrjqtsrHj")
    private String swsrjqtsrHj;

    /**
     * 视同销售收入
     */
    @JsonProperty(value = "stxssrPtzz")
    private String stxssrPtzz;

    /**
     * 视同销售收入
     */
    @JsonProperty(value = "stxssrFptzz")
    private String stxssrFptzz;

    /**
     * 视同销售收入
     */
    @JsonProperty(value = "stxssrQtfclx")
    private String stxssrQtfclx;

    /**
     * 视同销售收入
     */
    @JsonProperty(value = "stxssrHj")
    private String stxssrHj;

    /**
     * 扣除项目金额合计
     */
    @JsonProperty(value = "kcxmjehjPtzz")
    private String kcxmjehjPtzz;

    /**
     * 扣除项目金额合计
     */
    @JsonProperty(value = "kcxmjehjFptzz")
    private String kcxmjehjFptzz;

    /**
     * 扣除项目金额合计
     */
    @JsonProperty(value = "kcxmjehjQtfclx")
    private String kcxmjehjQtfclx;

    /**
     * 扣除项目金额合计
     */
    @JsonProperty(value = "kcxmjehj")
    private String kcxmjehj;

    /**
     * 销售面积
     */
    @JsonProperty(value = "xsmjPtzz")
    private String xsmjPtzz;

    /**
     * 销售面积
     */
    @JsonProperty(value = "xsmjFptzz")
    private String xsmjFptzz;

    /**
     * 销售面积
     */
    @JsonProperty(value = "xsmjQtfclx")
    private String xsmjQtfclx;

    /**
     * 销售面积 合计
     */
    @JsonProperty(value = "xsmjHj")
    private String xsmjHj;

    /**
     *
     */
    @JsonProperty(value = "dwcbfyPtzz")
    private String dwcbfyPtzz;

    /**
     * 单位成本费用
     */
    @JsonProperty(value = "dwcbfyFptzz")
    private String dwcbfyFptzz;

    /**
     * 单位成本费用
     */
    @JsonProperty(value = "dwcbfyQtfclx")
    private String dwcbfyQtfclx;

    /**
     * 单位成本费用合计
     */
    @JsonProperty(value = "dwcbfyHj")
    private String dwcbfyHj;

    /**
     * 本次与转让房地产有关的税金
     */
    @JsonProperty(value = "bcyzrfdcygdsjPtzz")
    private String bcyzrfdcygdsjPtzz;

    /**
     * 本次与转让房地产有关的税金
     */
    @JsonProperty(value = "bcyzrfdcygdsjFptzz")
    private String bcyzrfdcygdsjFptzz;

    /**
     * 本次与转让房地产有关的税金
     */
    @JsonProperty(value = "bcyzrfdcygdsjQtfclx")
    private String bcyzrfdcygdsjQtfclx;

    /**
     * 本次与转让房地产有关的税金
     */
    @JsonProperty(value = "bcyzrfdcygdsjHj")
    private String bcyzrfdcygdsjHj;

    /**
     * 营业税
     */
    @JsonProperty(value = "yysPtzz")
    private String yysPtzz;

    /**
     * 营业税
     */
    @JsonProperty(value = "yysFptzz")
    private String yysFptzz;

    /**
     * 营业税
     */
    @JsonProperty(value = "yysQtfclx")
    private String yysQtfclx;

    /**
     * 营业税
     */
    @JsonProperty(value = "yysHj")
    private String yysHj;

    /**
     * 城市维护建设税
     */
    @JsonProperty(value = "cswhjssPtzz")
    private String cswhjssPtzz;

    /**
     * 城市维护建设税
     */
    @JsonProperty(value = "cswhjssFptzz")
    private String cswhjssFptzz;

    /**
     * 城市维护建设税
     */
    @JsonProperty(value = "cswhjssQtfclx")
    private String cswhjssQtfclx;

    /**
     * 城市维护建设税
     */
    @JsonProperty(value = "cswhjssHj")
    private String cswhjssHj;

    /**
     *
     */
    @JsonProperty(value = "jyffjPtzz")
    private String jyffjPtzz;

    /**
     * 普通住宅教育费附加
     */
    @JsonProperty(value = "jyffjFptzz")
    private String jyffjFptzz;

    /**
     * 普通住宅教育费附加
     */
    @JsonProperty(value = "jyffjQtfclx")
    private String jyffjQtfclx;

    /**
     * 普通住宅教育费附加
     */
    @JsonProperty(value = "jyffjHj")
    private String jyffjHj;

    /**
     * 增值额
     */
    @JsonProperty(value = "zzePtzz")
    private String zzePtzz;

    /**
     * 增值额
     */
    @JsonProperty(value = "zzeFptzz")
    private String zzeFptzz;

    /**
     * 增值额
     */
    @JsonProperty(value = "zzeQtfclx")
    private String zzeQtfclx;

    /**
     * 增值额
     */
    @JsonProperty(value = "zzeHj")
    private String zzeHj;

    /**
     * 增值额与扣除项目金额之比
     */
    @JsonProperty(value = "zzeykcxmjezbPtzz")
    private String zzeykcxmjezbPtzz;

    /**
     * 增值额与扣除项目金额之比
     */
    @JsonProperty(value = "zzeykcxmjezbFptzz")
    private String zzeykcxmjezbFptzz;

    /**
     * 增值额与扣除项目金额之比
     */
    @JsonProperty(value = "zzeykcxmjezbQtfclx")
    private String zzeykcxmjezbQtfclx;

    /**
     * 税源税率
     */
    @JsonProperty(value = "syslPtzz")
    private String syslPtzz;

    /**
     * 税源税率
     */
    @JsonProperty(value = "syslFptzz")
    private String syslFptzz;

    /**
     * 税源税率
     */
    @JsonProperty(value = "syslQtfclx")
    private String syslQtfclx;

    /**
     * 速算扣除系数
     */
    @JsonProperty(value = "sskcxsPtzz")
    private String sskcxsPtzz;

    /**
     * 速算扣除系数
     */
    @JsonProperty(value = "sskcxsFptzz")
    private String sskcxsFptzz;

    /**
     * 速算扣除系数
     */
    @JsonProperty(value = "sskcxsQtfclx")
    private String sskcxsQtfclx;

    /**
     * 应缴土地增值税税额
     */
    @JsonProperty(value = "yjtdzzssePtzz")
    private String yjtdzzssePtzz;

    /**
     * 应缴土地增值税税额
     */
    @JsonProperty(value = "yjtdzzsseFptzz")
    private String yjtdzzsseFptzz;

    /**
     * 应缴土地增值税税额
     */
    @JsonProperty(value = "yjtdzzsseQtfclx")
    private String yjtdzzsseQtfclx;

    /**
     * 应缴土地增值税税额
     */
    @JsonProperty(value = "yjtdzzsseHj")
    private String yjtdzzsseHj;

    /**
     * 减免税额
     */
    @JsonProperty(value = "jmsePtzz")
    private String jmsePtzz;

    /**
     * 减免税额
     */
    @JsonProperty(value = "jmseFptzz")
    private String jmseFptzz;

    /**
     * 减免税额
     */
    @JsonProperty(value = "jmseQtfclx")
    private String jmseQtfclx;

    /**
     * 减免税额
     */
    @JsonProperty(value = "jmseHj")
    private String jmseHj;

    /**
     * 税收减免性质代码
     */
    @JsonProperty(value = "jmxzdmmcPtzz")
    private String jmxzdmmcPtzz;

    /**
     * 税收减免性质代码
     */
    @JsonProperty(value = "jmxzdmmcFptzz")
    private String jmxzdmmcFptzz;

    /**
     * 税收减免性质代码
     */
    @JsonProperty(value = "jmxzdmmcQtfclx")
    private String jmxzdmmcQtfclx;

    /**
     * 已缴土地增值税税额
     */
    @JsonProperty(value = "yijtdzzssePtzz")
    private String yijtdzzssePtzz;

    /**
     * 已缴土地增值税税额
     */
    @JsonProperty(value = "yijtdzzsseFptzz")
    private String yijtdzzsseFptzz;

    /**
     * 已缴土地增值税税额
     */
    @JsonProperty(value = "yijtdzzsseQtfclx")
    private String yijtdzzsseQtfclx;

    /**
     * 已缴土地增值税税额
     */
    @JsonProperty(value = "yijtdzzsseHj")
    private String yijtdzzsseHj;

    /**
     * 应补（退）土地增值税税额
     */
    @JsonProperty(value = "ybtdzzssePtzz")
    private String ybtdzzssePtzz;

    /**
     * 应补（退）土地增值税税额
     */
    @JsonProperty(value = "ybtdzzsseFptzz")
    private String ybtdzzsseFptzz;

    /**
     * 应补（退）土地增值税税额
     */
    @JsonProperty(value = "ybtdzzsseQtfclx")
    private String ybtdzzsseQtfclx;

    /**
     * 应补（退）土地增值税税额
     */
    @JsonProperty(value = "ybtdzzsseHj")
    private String ybtdzzsseHj;

    /**
     * uuid
     */
    @JsonProperty(value = "ptzzUuid")
    private String ptzzUuid;

    /**
     * uuid
     */
    @JsonProperty(value = "fptzzUuid")
    private String fptzzUuid;

    /**
     *
     */
    @JsonProperty(value = "qtfclxUuid")
    private String qtfclxUuid;

    /**
     * 增值额与扣除项目金额之比审定数
     */
    @JsonProperty(value = "zzeykcxmjezbPtzz3")
    private String zzeykcxmjezbPtzz3;

    /**
     *  税率
     */
    @JsonProperty(value = "syslPtzz3")
    private String syslPtzz3;

    /**
     * 速算扣除系数
     */
    @JsonProperty(value = "sskcxsPtzz3")
    private String sskcxsPtzz3;

    /**
     * 增值额与扣除项目金额之比(%)清算数
     */
    @JsonProperty(value = "zzeykcxmjezbFptzz3")
    private String zzeykcxmjezbFptzz3;

    /**
     * 税率
     */
    @JsonProperty(value = "syslFptzz3")
    private String syslFptzz3;

    /**
     * 速算扣除系数清算数
     */
    @JsonProperty(value = "sskcxsFptzz3")
    private String sskcxsFptzz3;

    /**
     * 增值额与扣除项目金额之比审定数
     */
    @JsonProperty(value = "zzeykcxmjezbQtfclx3")
    private String zzeykcxmjezbQtfclx3;

    /**
     * 税率
     */
    @JsonProperty(value = "syslQtfclx3")
    private String syslQtfclx3;

    /**
     * 速算扣除系数调整数
     */
    @JsonProperty(value = "sskcxsQtfclx3")
    private String sskcxsQtfclx3;

    /**
     * 速算扣除系数清算数
     */
    @JsonProperty(value = "syslPtzz2")
    private String syslPtzz2;

    /**
     * 税率
     */
    @JsonProperty(value = "syslFptzz2")
    private String syslFptzz2;

    /**
     * 税率
     */
    @JsonProperty(value = "syslQtfclx2")
    private String syslQtfclx2;


}
