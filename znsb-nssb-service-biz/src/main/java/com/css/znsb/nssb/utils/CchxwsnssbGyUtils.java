package com.css.znsb.nssb.utils;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.util.DateUtils;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.ServiceException;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.NsrbqxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.NsrzgxxVO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.LslfDTO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.*;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.SbxxGridlbVO;
import com.css.znsb.nssb.util.DateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 财产和行为税纳税申报工具类
 */
@Slf4j
@Component
public class CchxwsnssbGyUtils {

    @Resource
    NsrxxApi nsrxxApi;

    /**
     * 纳税人信息查询Api
     */
//    @Resource
//    private  NsrxxApi nsrxxApi;

//    /**
//     * 注入纳税人信息查询Api
//     * @param nsrxxcxApi nsrxxcxApi
//     */
//    @Autowired
//    public void setNsrxxcxApi(NsrxxcxApi nsrxxcxApi) {
//        CchxwsnssbGyUtils.nsrxxcxApi = nsrxxcxApi;
//    }

    /**
     * 获取纳税期限(全国配置)
     * @param zsxmDm 征收项目
     * @param swjgDmList 本上级税务机关
     * @param pzbListMap 配置表Group
     * @param dqrq 当前日期
     * @param sbqxwbGroup 申报期限维护表group
     * @param jjrGroup 节假日group
     * @param zqtzGroup 征期调整group
     * @return 返回值
     */
    @SneakyThrows
    public static List<Map<String, Object>> getnsqxqgByZsxmDmAndSwjgDm(String zsxmDm, List<String> swjgDmList,
           Map<Object, List<Map<String, Object>>> pzbListMap, Date dqrq,
           Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup,
           Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup,
           Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup) {
        if (GyUtils.isNull(zsxmDm) || GyUtils.isNull(swjgDmList) || GyUtils.isNull(pzbListMap)) {
            return null;
        }
        final List<Map<String, Object>> nsqxList = new ArrayList<>();
        //根据征收项目获取配置表信息
        final List<Map<String, Object>> nsqxcsList = pzbListMap.get(zsxmDm);
        if (!GyUtils.isNull(nsqxcsList)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (final Map<String, Object> nsqxcsMap : nsqxcsList) {
                final String xybz = (String) nsqxcsMap.get("xybz");
                final String yxbz = (String) nsqxcsMap.get("yxbz");
                //final Date yxqq = CxsJmxzUtils.cast2Date(nsqxcsMap.get("YXQQ"));
                //final Date yxqz = CxsJmxzUtils.cast2Date(nsqxcsMap.get("YXQZ"));
                final Date yxqq = DateUtil.toDate("yyyy-MM-dd",(String) nsqxcsMap.get("yxqq"));
                final Date yxqz = DateUtil.toDate("yyyy-MM-dd",(String) nsqxcsMap.get("yxqz"));
                final String comSwjgDm = (String) nsqxcsMap.get("swjgDm");
                //只返回选用标志和有效标志为Y且税务机关代码为当前税务机关本机及本上级的数据
                if ("Y".equals(xybz) && "Y".equals(yxbz) && !GyUtils.isNull(yxqq) && !GyUtils.isNull(yxqz) && yxqq.compareTo(yxqz) <= 0 && swjgDmList.contains(comSwjgDm)) {
                    //如果是资源税，过滤掉水资源
                    final String zspmDmTemp = (String) nsqxcsMap.get("zspmDm");
                    if ("10107".equals(zsxmDm) && !GyUtils.isNull(zspmDmTemp) && zspmDmTemp.startsWith("1010708")) {
                        continue;
                    }
                    final String nsqx = (String) nsqxcsMap.get("nsqxDm");
                    final String sbqx = (String) nsqxcsMap.get("sbqxDm");
                    final String jkqx = (String) nsqxcsMap.get("jkqxDm");
                    //过滤掉无纳税、申报、缴款期限数据
                    if (!GyUtils.isNull(nsqx) && !GyUtils.isNull(sbqx) && !GyUtils.isNull(jkqx)) {
                        //检验认定配置日期是否有效
                        if (CchxwsnssbGyUtils.checkRdpzRq(nsqx, sbqx, yxqq, yxqz, dqrq, sbqxwbGroup, jjrGroup, zqtzGroup, swjgDmList, zsxmDm)) {
                            final Map<String, Object> jmxzMap = new HashMap<>();
                            jmxzMap.put("NSQX_DM", nsqx);
                            jmxzMap.put("SBQX_DM", sbqx);
                            jmxzMap.put("JKQX_DM", jkqx);
                            jmxzMap.put("ZSXM_DM", zsxmDm);
                            nsqxList.add(jmxzMap);
                        }
                    }
                }
            }
        }
        return nsqxList ;
    }

    /**
     * 获取纳税期限(各省自定义配置)
     * @param zsxmDm 征收项目
     * @param swjgDmList 本上级税务机关
     * @param gszdyGroup 配置表Group
     * @param dqrq 当前日期
     * @param sbqxwbGroup 申报期限维护表group
     * @param jjrGroup 节假日group
     * @param zqtzGroup 征期调整group
     * @return 返回值
     */
    public static List<Map<String, Object>> getnsqxzdyByZsxmDmAndSwjgDm(String zsxmDm, List<String> swjgDmList,
            Map<Object, Map<Object, List<Map<String, Object>>>> gszdyGroup, Date dqrq,
            Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup,
            Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup,
            Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup) {
        if (GyUtils.isNull(zsxmDm) || GyUtils.isNull(swjgDmList) || GyUtils.isNull(gszdyGroup)) {
            return null;
        }
        final List<Map<String, Object>> nsqxList = new ArrayList<>();
        //根据征收项目获取配置表信息
        final Map<Object, List<Map<String, Object>>> zsxmGroup = gszdyGroup.get(zsxmDm);
        if (!GyUtils.isNull(zsxmGroup)) {
            for (String swjgDm : swjgDmList) {
                if (!GyUtils.isNull(nsqxList)) {
                    break;
                }
                final List<Map<String, Object>> nsqxcsList = zsxmGroup.get(swjgDm);
                if (GyUtils.isNull(nsqxcsList)) {
                    continue;
                }
                for (final Map<String, Object> nsqxcsMap : nsqxcsList) {
                    final String xybz = (String) nsqxcsMap.get("xybz");
                    final String yxbz = (String) nsqxcsMap.get("yxbz");
                    //final Date yxqq = CxsJmxzUtils.cast2Date(nsqxcsMap.get("YXQQ"));
                    //final Date yxqz = CxsJmxzUtils.cast2Date(nsqxcsMap.get("YXQZ"));
                    final Date yxqq = DateUtil.toDate("yyyy-MM-dd",(String) nsqxcsMap.get("yxqq"));
                    final Date yxqz = DateUtil.toDate("yyyy-MM-dd",(String) nsqxcsMap.get("yxqz"));
                    //final String comSwjgDm = (String) nsqxcsMap.get("SWJG_DM");
                    //只返回选用标志和有效标志为Y且税务机关代码为当前税务机关本机及本上级的数据
                    if ("Y".equals(xybz) && "Y".equals(yxbz) && !GyUtils.isNull(yxqq) && !GyUtils.isNull(yxqz) && yxqq.compareTo(yxqz) <= 0) {
                        final String nsqx = (String) nsqxcsMap.get("nsqxDm");
                        final String sbqx = (String) nsqxcsMap.get("sbqxDm");
                        final String jkqx = (String) nsqxcsMap.get("jkqxDm");
                        //过滤掉无纳税、申报、缴款期限数据
                        if (!GyUtils.isNull(nsqx) && !GyUtils.isNull(sbqx) && !GyUtils.isNull(jkqx)) {
                            //检验认定配置日期是否有效
                            if (CchxwsnssbGyUtils.checkRdpzRq(nsqx, sbqx, yxqq, yxqz, dqrq, sbqxwbGroup, jjrGroup, zqtzGroup, swjgDmList, zsxmDm)) {
                                final Map<String, Object> jmxzMap = new HashMap<>();
                                jmxzMap.put("NSQX_DM", nsqx);
                                jmxzMap.put("SBQX_DM", sbqx);
                                jmxzMap.put("JKQX_DM", jkqx);
                                jmxzMap.put("ZSXM_DM", zsxmDm);
                                nsqxList.add(jmxzMap);
                            }
                        }
                    }
                }
            }
        }
        return nsqxList ;
    }

    /**
     * 获取纳税期限(房土车配置)
     * @param zsxmDm 征收项目
     * @param swjgDmList 本上级税务机关
     * @param ftcGroup 配置表Group
     * @param nsrlxCS 用户类型
     * @param dqrq 当前日期
     * @param sbqxwbGroup 申报期限维护表group
     * @param jjrGroup 节假日group
     * @param zqtzGroup 征期调整group
     * @return 返回值
     */
    public static List<Map<String, Object>> getnsqxftcByZsxmDmAndSwjgDm(String zsxmDm, List<String> swjgDmList,
            Map<Object, Map<Object, List<Map<String, Object>>>> ftcGroup, String nsrlxCS, boolean cjZt, boolean czZt, Date dqrq,
            Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup,
            Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup,
            Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup) {
        if (GyUtils.isNull(zsxmDm) || GyUtils.isNull(swjgDmList) || GyUtils.isNull(ftcGroup)) {
            return null;
        }
        final List<Map<String, Object>> nsqxList = new ArrayList<>();
        //根据征收项目获取配置表信息
        final Map<Object, List<Map<String, Object>>> zsxmGroup = ftcGroup.get(zsxmDm);
        if (!GyUtils.isNull(zsxmGroup)) {
            //对于房产税10110，需要取出从价和从租配置，true为已经获取，false为待获取，此时标记总体状态
            boolean cjFlag = cjZt;
            boolean czFlag = czZt;
            for (String swjgDm : swjgDmList) {
                if (!GyUtils.isNull(nsqxList)) {
                    if ("10110".equals(zsxmDm)) {
                        if (cjFlag && czFlag) {
                            break;
                        }
                    } else {
                        break;
                    }
                }
                final List<Map<String, Object>> nsqxcsList = zsxmGroup.get(swjgDm);
                if (GyUtils.isNull(nsqxcsList)) {
                    continue;
                }
                //对于房产税10110，需要取出从价和从租配置，已经取到的配置不继续获取，此时标记本级状态需要获取的品目
                boolean cjContinue = !cjFlag;
                boolean czContinue = !czFlag;
                for (final Map<String, Object> nsqxcsMap : nsqxcsList) {
                    final String xybz = (String) nsqxcsMap.get("xybz");
                    final String yxbz = (String) nsqxcsMap.get("yxbz");
                    //final Date yxqq = CxsJmxzUtils.cast2Date(nsqxcsMap.get("YXQQ"));
                    //final Date yxqz = CxsJmxzUtils.cast2Date(nsqxcsMap.get("YXQZ"));
                    final Date yxqq = DateUtil.toDate("yyyy-MM-dd",(String) nsqxcsMap.get("yxqq"));
                    final Date yxqz = DateUtil.toDate("yyyy-MM-dd",(String) nsqxcsMap.get("yxqz"));
                    //final String comSwjgDm = (String) nsqxcsMap.get("SWJG_DM");
                    //只返回选用标志和有效标志为Y且税务机关代码为当前税务机关本机及本上级的数据
                    if ("Y".equals(xybz) && "Y".equals(yxbz) && !GyUtils.isNull(yxqq) && !GyUtils.isNull(yxqz) && yxqq.compareTo(yxqz) <= 0) {
                        final String nsqx = (String) nsqxcsMap.get("nsqxDm");
                        final String sbqx = (String) nsqxcsMap.get("sbqxDm");
                        final String jkqx = (String) nsqxcsMap.get("jkqxDm");
                        final String nsrlxDB = (String) nsqxcsMap.get("nsrlx");
                        final String zspmDmDB = (String) nsqxcsMap.get("zspmDm");
                        //过滤掉无纳税、申报、缴款期限数据
                        if (!GyUtils.isNull(nsqx) && !GyUtils.isNull(sbqx) && !GyUtils.isNull(jkqx)) {
                            //检验认定配置日期是否有效
                            if (CchxwsnssbGyUtils.checkRdpzRq(nsqx, sbqx, yxqq, yxqz, dqrq, sbqxwbGroup, jjrGroup, zqtzGroup, swjgDmList, zsxmDm)) {
                                final Map<String, Object> jmxzMap = new HashMap<>();
                                jmxzMap.put("NSQX_DM", nsqx);
                                jmxzMap.put("SBQX_DM", sbqx);
                                jmxzMap.put("JKQX_DM", jkqx);
                                jmxzMap.put("ZSXM_DM", zsxmDm);
                                jmxzMap.put("NSRLX", nsrlxDB);
                                final boolean nsrlxFalg = CchxwsnssbGyUtils.judgeNsrlx(nsrlxDB, nsrlxCS);
                                if (nsrlxFalg) {
                                    if ("10110".equals(zsxmDm)) {
                                        //对于房产税10110，需要取出从价和从租配置区分征收品目%%%%%%%%%，10110%%%%，1011007%%，1011008%%，101100700，101100800
                                        final boolean f700 = CchxwsnssbGyUtils.judegeZspmDm(zspmDmDB, "101100700");
                                        final boolean f800 = CchxwsnssbGyUtils.judegeZspmDm(zspmDmDB, "101100800");
                                        if (f700 && f800) {
                                            cjFlag = true;
                                            czFlag = true;
                                            jmxzMap.put("ZSPM_DM", "");
                                            nsqxList.add(jmxzMap);
                                        } else if (f700) {
                                            if (cjContinue) {
                                                cjFlag = true;
                                                jmxzMap.put("ZSPM_DM", "101100700");
                                                nsqxList.add(jmxzMap);
                                            }
                                        } else if (f800) {
                                            if (czContinue) {
                                                czFlag = true;
                                                jmxzMap.put("ZSPM_DM", "101100800");
                                                nsqxList.add(jmxzMap);
                                            }
                                        }
                                    } else {
                                        nsqxList.add(jmxzMap);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return nsqxList;
    }

    /**
     * 获取纳税期限(默认配置)
     * @param zsxmDm 征收项目
     * @param swjgDmList 本上级税务机关
     * @param mrpzGroup 配置表Group
     * @return 返回值
     */
    public static List<Map<String, Object>> getnsqxmrgzByZsxmDmAndSwjgDm(String zsxmDm, List<String> swjgDmList,
            Map<Object, Map<Object, List<Map<String, Object>>>> mrpzGroup) {
        if (GyUtils.isNull(zsxmDm) || GyUtils.isNull(swjgDmList) || GyUtils.isNull(mrpzGroup)) {
            return null;
        }
        final List<Map<String, Object>> nsqxList = new ArrayList<>();
        //根据征收项目获取配置表信息
        final Map<Object, List<Map<String, Object>>> zsxmGroup = mrpzGroup.get(zsxmDm);
        if (!GyUtils.isNull(zsxmGroup)) {
            for (String swjgDm : swjgDmList) {
                if (!GyUtils.isNull(nsqxList)) {
                    break;
                }
                final List<Map<String, Object>> nsqxcsList = zsxmGroup.get(swjgDm);
                if (GyUtils.isNull(nsqxcsList)) {
                    continue;
                }
                for (final Map<String, Object> nsqxcsMap : nsqxcsList) {
                    final String xybz = (String) nsqxcsMap.get("xybz");
                    final String yxbz = (String) nsqxcsMap.get("yxbz");
                    //final String comSwjgDm = (String) nsqxcsMap.get("SWJG_DM");
                    //只返回选用标志和有效标志为Y且税务机关代码为当前税务机关本机及本上级的数据
                    if ("Y".equals(xybz) && "Y".equals(yxbz)) {
                        final String nsqx = (String) nsqxcsMap.get("nsqxDm");
                        final String sbqx = (String) nsqxcsMap.get("sbqxDm");
                        final String jkqx = (String) nsqxcsMap.get("jkqxDm");
                        //过滤掉无纳税、申报、缴款期限数据
                        if (!GyUtils.isNull(nsqx) && !GyUtils.isNull(sbqx) && !GyUtils.isNull(jkqx)) {
                            final Map<String, Object> jmxzMap = new HashMap<>();
                            jmxzMap.put("NSQX_DM", nsqx);
                            jmxzMap.put("SBQX_DM", sbqx);
                            jmxzMap.put("JKQX_DM", jkqx);
                            jmxzMap.put("ZSXM_DM", zsxmDm);
                            nsqxList.add(jmxzMap);
                        }
                    }
                }
            }
        }
        return nsqxList ;
    }

//-------------------------------------------获取申报期限start-------------------------------------------------------------

    /**
     * 当期标志
     * @param sbqxDm 申报期限代码
     * @return 返回值
     */
    public static String checkIsQnsb(String sbqxDm){
        if (GyUtils.isNull(sbqxDm)) {
            return "N";
        } else if("09".equals(sbqxDm)
                || "20".equals(sbqxDm) || "21".equals(sbqxDm) || "22".equals(sbqxDm) || "23".equals(sbqxDm) || "24".equals(sbqxDm) || "25".equals(sbqxDm) || "26".equals(sbqxDm) || "27".equals(sbqxDm)
                || "30".equals(sbqxDm) || "31".equals(sbqxDm) || "32".equals(sbqxDm) || "33".equals(sbqxDm) || "34".equals(sbqxDm) || "35".equals(sbqxDm) || "37".equals(sbqxDm) || "38".equals(sbqxDm) || "39".equals(sbqxDm)
                || "40".equals(sbqxDm) || "49".equals(sbqxDm) || "53".equals(sbqxDm)
                || "54".equals(sbqxDm) || "55".equals(sbqxDm) || "56".equals(sbqxDm) || "59".equals(sbqxDm)
                || "64".equals(sbqxDm) || "65".equals(sbqxDm) || "66".equals(sbqxDm)
                || "72".equals(sbqxDm) || "73".equals(sbqxDm) || "75".equals(sbqxDm) || "76".equals(sbqxDm) || "77".equals(sbqxDm) || "78".equals(sbqxDm) || "79".equals(sbqxDm)
                || "80".equals(sbqxDm) || "81".equals(sbqxDm) || "82".equals(sbqxDm) || "86".equals(sbqxDm) || "87".equals(sbqxDm) || "88".equals(sbqxDm)){
            return "Y";
        }
        return "N";
    }

    /**
     * 计算税款属期
     * @param nsqxDm 纳税期限代码
     * @param jzrq 基准日期（申报日期、或所属期起、或其它日期，用来作为计算属期的基准）
     * @param dqbzIn 当期标志（是推算上个属期、还是当前属期）
     * @return 返回值
     */
    public static Map<String, Object> jsSkssq(String nsqxDm, Date jzrq, String dqbzIn){
        final Map<String, Object> rs = new HashMap<>();
        if (GyUtils.isNull(nsqxDm) || GyUtils.isNull(jzrq)) {
            return rs;
        }
        //默认属期为基准日期的上期，即不为当期
        String dqbz = dqbzIn;
        if (GyUtils.isNull(dqbz)) {
            dqbz = "N";
        }
        //月份，季度最后一月
        final int m3 = 3;
        final int m6 = 6;
        final int m9 = 9;
        final int m12 = 12;
        //30日，31日
        final int d30 = 30;
        final int d31 = 31;

        //先判断当期还是上期，如上期，先将基准日期根据纳税期限往前推
        Calendar jzrqCal = Calendar.getInstance();
        jzrqCal.setTime(jzrq);
        if ("N".equals(dqbz)) {
            switch (nsqxDm) {
                //月报
                case "06":
                    jzrqCal.add(Calendar.MONTH, -1);
                    break;
                //季报
                case "08":
                    jzrqCal.add(Calendar.MONTH, -3);
                    break;
                //半年报
                case "09":
                    jzrqCal.add(Calendar.MONTH, -6);
                    break;
                //年报
                case "10":
                    jzrqCal.add(Calendar.MONTH, -12);
                    break;
                //按次（默认当天）
                case "11":
                    jzrqCal.add(Calendar.DAY_OF_MONTH, -1);
                    break;
                //六十天（默认基准日期落在前一个月，如落在后一个月，需另写实现）
                case "07":
                    jzrqCal.add(Calendar.MONTH, -2);
                    break;
                //按周
                case "13":
                    jzrqCal.add(Calendar.DATE, -7);
                    break;
                //其它默认也按月处理
                default:
                    jzrqCal.add(Calendar.MONTH, -1);
                    break;
            }
        }
        //根据基准日期赋值期起、期止，为后面推算做准备
        Calendar skssqqCal = (Calendar) jzrqCal.clone();
        Calendar skssqzCal = (Calendar) jzrqCal.clone();
        //获取月份，从0开始需加1
        final int intmonth = jzrqCal.get(Calendar.MONTH) + 1;

        //推算期起、期止
        switch (nsqxDm) {
            //月报
            case "06":
                skssqqCal.set(Calendar.DAY_OF_MONTH, 1);
                skssqzCal.add(Calendar.MONTH, 1);
                skssqzCal.set(Calendar.DAY_OF_MONTH, 1);
                skssqzCal.add(Calendar.DAY_OF_MONTH, -1);
                break;
            //季报
            case "08":
                if (intmonth <= m3) {
                    //1季度
                    skssqqCal.set(Calendar.DATE, 1);
                    skssqqCal.set(Calendar.MONTH, Calendar.JANUARY);
                    skssqzCal.set(Calendar.DATE, d31);
                    skssqzCal.set(Calendar.MONTH, Calendar.MARCH);
                } else if (intmonth <= m6) {
                    //2季度
                    skssqqCal.set(Calendar.DATE, 1);
                    skssqqCal.set(Calendar.MONTH, Calendar.APRIL);
                    skssqzCal.set(Calendar.DATE, d30);
                    skssqzCal.set(Calendar.MONTH, Calendar.JUNE);
                } else if (intmonth <= m9) {
                    //3季度
                    skssqqCal.set(Calendar.DATE, 1);
                    skssqqCal.set(Calendar.MONTH, Calendar.JULY);
                    skssqzCal.set(Calendar.DATE, d30);
                    skssqzCal.set(Calendar.MONTH, Calendar.SEPTEMBER);
                } else if (intmonth <= m12) {
                    //4季度
                    skssqqCal.set(Calendar.DAY_OF_MONTH, 1);
                    skssqqCal.set(Calendar.MONTH, Calendar.OCTOBER);
                    skssqzCal.set(Calendar.DAY_OF_MONTH, d31);
                    skssqzCal.set(Calendar.MONTH, Calendar.DECEMBER);
                }
                break;
            //半年报
            case "09":
                if (intmonth <= m6) {
                    //上半年
                    skssqqCal.set(Calendar.MONTH, Calendar.JANUARY);
                    skssqqCal.set(Calendar.DATE, 1);
                    skssqzCal.set(Calendar.MONTH, Calendar.JUNE);
                    skssqzCal.set(Calendar.DATE, d30);
                } else {
                    //下半年
                    skssqqCal.set(Calendar.MONTH, Calendar.JULY);
                    skssqqCal.set(Calendar.DATE, 1);
                    skssqzCal.set(Calendar.MONTH, Calendar.DECEMBER);
                    skssqzCal.set(Calendar.DATE, d31);
                }
                break;
            //年报
            case "10":
                skssqqCal.set(Calendar.DATE, 1);
                skssqqCal.set(Calendar.MONTH, Calendar.JANUARY);
                skssqzCal.set(Calendar.DATE, d31);
                skssqzCal.set(Calendar.MONTH, Calendar.DECEMBER);
                break;
            //按次（默认当天）
            case "11":
                skssqqCal = jzrqCal;
                skssqzCal = jzrqCal;
                break;
            //六十天（默认基准日期落在前一个月，如落在后一个月，需另写实现）
            case "07":
                skssqqCal.set(Calendar.DAY_OF_MONTH, 1);//设为月第一天
                skssqzCal.set(Calendar.DAY_OF_MONTH, 1);//先设为月第一天
                skssqzCal.add(Calendar.MONTH, 2);//加两个月
                skssqzCal.add(Calendar.DAY_OF_MONTH, -1);//减一天到上月月底
                break;
            //按周
            case "13":
                skssqqCal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                skssqzCal.set(skssqqCal.get(Calendar.YEAR), skssqqCal.get(Calendar.MONTH), skssqqCal.get(Calendar.DATE));
                skssqzCal.add(Calendar.DATE, 6);
                if (skssqqCal.getTime().after(jzrq)) {
                    skssqqCal.add(Calendar.DATE, -7);
                    skssqzCal.add(Calendar.DATE, -7);
                }
                break;
            //其它默认也按月处理
            default:
                skssqqCal.set(Calendar.DAY_OF_MONTH, 1);
                skssqzCal.add(Calendar.MONTH, 1);
                skssqzCal.set(Calendar.DAY_OF_MONTH, 1);
                skssqzCal.add(Calendar.DAY_OF_MONTH, -1);
                break;
        }

        rs.put("skssqq", skssqqCal.getTime());
        rs.put("skssqz", skssqzCal.getTime());
        return rs;
    }

    /**
     * 获取申报期限日期起
     * @param sbqxDmIn 申报期限代码
     * @param nsqxDm 纳税期限代码
     * @param zsxmDm 征收项目代码
     * @param skssqq 税款所属期起
     * @param skssqz 税款所属期止
     * @return 返回值
     */
    public static String getSbqxrqq(String sbqxDmIn, String nsqxDm, String zsxmDm, Calendar skssqq, Calendar skssqz) {
        String sbpzbSbqx = null;
        if("Y".equals(checkIsQnsb(sbqxDmIn))){
            sbpzbSbqx = DateUtil.doDateFormat(skssqq.getTime(), "yyyy-MM-dd");
            final String sbpzbSbqxNd = String.valueOf(skssqq.get(Calendar.YEAR));
            int skssqqMonth = skssqq.get(Calendar.MONTH) + 1;//获取月份，从0开始需加1
            int skssqzMonth = skssqz.get(Calendar.MONTH) + 1;//获取月份，从0开始需加1
            //09-期止当月月底-DY0031，取税款所属期止：月初->月底
            if ("09".equals(sbqxDmIn)) {
                final Date firstDayofMonth = getFirstDayofMonth(skssqz.getTime());
                sbpzbSbqx = DateUtil.doDateFormat(firstDayofMonth, "yyyy-MM-dd");
            } else if ("66".equals(sbqxDmIn)) {
                //66-当年10月1日至12月31日-DN1231
                sbpzbSbqx = sbpzbSbqxNd + "-10-01";
            } else if ("78".equals(sbqxDmIn) || "80".equals(sbqxDmIn)) {
                //78-当年5月20日或当年11月20日-BNDQ05201120
                //80-当年5月31日或当年11月30日-BNDQ05311130
                if(skssqqMonth == 1 && skssqzMonth == 6) {
                    sbpzbSbqx = sbpzbSbqxNd + "-05-01";
                }
                if(skssqqMonth == 7 && skssqzMonth == 12) {
                    sbpzbSbqx = sbpzbSbqxNd + "-11-01";
                }
            } else if ("75".equals(sbqxDmIn) || "82".equals(sbqxDmIn)){
                //75-当年6月15日或当年12月15日-BNDQ06151215
                //82-当年6月20日或当年12月20日-BNDQ06201220
                if(skssqqMonth == 1 && skssqzMonth == 6) {
                    sbpzbSbqx = sbpzbSbqxNd + "-06-01";
                }
                if(skssqqMonth == 7 && skssqzMonth == 12) {
                    sbpzbSbqx = sbpzbSbqxNd + "-12-01";
                }
            } else if ("81".equals(sbqxDmIn) || "87".equals(sbqxDmIn)) {
                //81-当年4月30日或当年10月31日-BNDQ04301031
                //87-当年4月15日或当年10月15日-BNDQ04151015
                if(skssqqMonth == 1 && skssqzMonth == 6) {
                    sbpzbSbqx = sbpzbSbqxNd + "-04-01";
                }
                if(skssqqMonth == 7 && skssqzMonth == 12) {
                    sbpzbSbqx = sbpzbSbqxNd + "-10-01";
                }
            } else if ("72".equals(sbqxDmIn)) {
                //72-当年1月31日或当年7月31日-BNDQ01310731
                if(skssqqMonth == 1 && skssqzMonth == 6) {
                    sbpzbSbqx = sbpzbSbqxNd + "-01-01";
                }
                if(skssqqMonth == 7 && skssqzMonth == 12) {
                    sbpzbSbqx = sbpzbSbqxNd + "-07-01";
                }
            } else if ("88".equals(sbqxDmIn)) {
                //88-当年3月15日或当年9月15日-BNDQ03150915
                if(skssqqMonth == 1 && skssqzMonth == 6) {
                    sbpzbSbqx = sbpzbSbqxNd + "-03-01";
                }
                if(skssqqMonth == 7 && skssqzMonth == 12) {
                    sbpzbSbqx = sbpzbSbqxNd + "-09-01";
                }
            } else if ("64".equals(sbqxDmIn)) {
                //64-当年2月、5月、8月、11月15日-JB0015
                if(skssqqMonth == 1 && skssqzMonth == 3) {
                    sbpzbSbqx = sbpzbSbqxNd + "-02-01";
                }
                if(skssqqMonth == 4 && skssqzMonth == 6) {
                    sbpzbSbqx = sbpzbSbqxNd + "-05-01";
                }
                if(skssqqMonth == 7 && skssqzMonth == 9) {
                    sbpzbSbqx = sbpzbSbqxNd + "-08-01";
                }
                if(skssqqMonth == 10 && skssqzMonth == 12) {
                    sbpzbSbqx = sbpzbSbqxNd + "-11-01";
                }
            } else if ("76".equals(sbqxDmIn)) {
                //76-当年3月、6月、9月、12月15-JB0015
                if(skssqqMonth == 1 && skssqzMonth == 3) {
                    sbpzbSbqx = sbpzbSbqxNd + "-03-01";
                }
                if(skssqqMonth == 4 && skssqzMonth == 6) {
                    sbpzbSbqx = sbpzbSbqxNd + "-06-01";
                }
                if(skssqqMonth == 7 && skssqzMonth == 9) {
                    sbpzbSbqx = sbpzbSbqxNd + "-09-01";
                }
                if(skssqqMonth == 10 && skssqzMonth == 12) {
                    sbpzbSbqx = sbpzbSbqxNd + "-12-01";
                }
            }
        }else{
            sbpzbSbqx = getFirstDayofNextMonth(DateUtil.doDateFormat(skssqz.getTime(), "yyyy-MM-dd"));
        }
        //按次申报分支
        if("11".equals(nsqxDm)) {
            sbpzbSbqx = "2020-01-01";
        }
        return sbpzbSbqx;
    }

    /**
     * 获取申报期限日期
     * @param swjgList 本上级税务机关
     * @param sbqxDmIn 申报期限代码
     * @param nsqxDm 纳税期限代码
     * @param zsxmDm 征收项目代码
     * @param skssqq 税款所属期起
     * @param skssqz 税款所属期止
     * @param sbqxwbGroup 申报期限维护表group
     * @param jjrGroup 节假日group
     * @param zqtzGroup 征期调整group
     * @return 返回值
     */
    public static String getSbqx(List<String> swjgList, String sbqxDmIn, String nsqxDm, String zsxmDm, Calendar skssqq, Calendar skssqz,
                                 Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup,
                                 Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup,
                                 Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup) {
        //申报期限代码为68（征期结束后3天），需先按照期后15天（sbqx_dm为04）计算征期，然后在加3天
        final String sbqxDm = "68".equals(sbqxDmIn) ? "04" : sbqxDmIn;
        //当前日期
        final Calendar curCal = Calendar.getInstance();
        //按照业务规则计算出正常征期的申报期限
        Calendar sbpzbSbqx = getSbqxWithSbqxDm(sbqxDm, skssqq, skssqz, curCal, curCal, "sbqxjs", null, null);

        //目前应用月取自申报期限 ，不在使用申报日期
        String yyyf = DateUtil.doDateFormat(sbpzbSbqx.getTime(), "yyyyMM");
        //若无延期申报数据，查询申报期限维护表，查找是否存在当月的申报期限记录，有则取出，终止算法，返回前台；
        Date sbqxwhDate = getSbqxJlInCache(zsxmDm, null, sbqxDmIn, swjgList, "1", null, yyyf, nsqxDm, sbqxwbGroup);
        if (sbqxwhDate != null) {
            if ("68".equals(sbqxDmIn)) {
                final Calendar sbqxwhCal = CxsDateUtils.convUtilDateToUtilCalendar(sbqxwhDate);
                sbqxwhCal.add(Calendar.DATE, 3);
                sbqxwhDate = CxsDateUtils.convertCalendarToDate(sbqxwhCal);
            }
            return DateUtil.doDateFormat(sbqxwhDate, "yyyy-MM-dd");
        }

        //查询节假日配置表查询节假日，有，则根据申报期限、节假日算法计算申报期限，无则继续流程；
        if (checkIsJjrsy(swjgList.get(0), zsxmDm, sbqxDm)) {
            getSbqxWithJJRInCache(swjgList, yyyf, sbpzbSbqx, jjrGroup);
        }

        //计算完节假日后，重新计算一下yyyf，因为可能存在跨月的情况，导致yyyf发生变化
        yyyf = DateUtil.doDateFormat(sbpzbSbqx.getTime(), "yyyyMM");
        //查询征期调整配置表，有，则根据申报期限、征期调整算法计算申报期限，无则继续流程；
        getSbqxWithZQTZInCache(zsxmDm, swjgList,null, nsqxDm, yyyf, sbpzbSbqx , zqtzGroup);

        //重新计算yyyf
        yyyf = DateUtil.doDateFormat(sbpzbSbqx.getTime(), "yyyyMM");
        //查询节假日配置表查询周末休息，有，则根据申报期限、周末顺延算法计算申报期限，无则继续流程
        if (checkIsJjrsy(swjgList.get(0), zsxmDm, sbqxDm)) {
            getSbqxWithZMInCache(swjgList, yyyf, sbpzbSbqx, jjrGroup);
        }

        //按照期后15天计算得到征期后，再往后加3天
        if ("68".equals(sbqxDmIn)) {
            sbpzbSbqx.add(Calendar.DATE, 3);
        }
        //按次申报分支
//        if("11".equals(nsqxDm)){
//            return "9999-12-31";
//        }

        return DateUtil.doDateFormat(sbpzbSbqx.getTime(), "yyyy-MM-dd");
    }

    /**
     * 若无申报期限维护数据，根据申报期限代码，按照业务规则计算出正常征期的申报期限
     * @param sbqxDm 申报期限代码
     * @param skssqqCalendar 税款属期起cal
     * @param skssqzCalendar 税款属期止cal
     * @param sbrqCalendar 申报日期cal
     * @param tbrqCalendar 填表日期cal
     * @param jsqxbj 计算标记
     * @param sbqxCalendar 申报期限cal
     * @param syqdsj 税源取得时间
     * @return 返回值
     */
    public static Calendar getSbqxWithSbqxDm(String sbqxDm, Calendar skssqqCalendar, Calendar skssqzCalendar,
                                             Calendar sbrqCalendar, Calendar tbrqCalendar, String jsqxbj, Calendar sbqxCalendar, Calendar syqdsj) {
        //返回值
        Calendar sbpzbSbqx = null;
        //常量值
        final int THIRTY_ONE = 31;
        //用来封装执行完申报期限代码配置表后所得到的申报期限
        Calendar skssqqCalendarClone = (Calendar) skssqqCalendar.clone();
        final Calendar skssqzCalendarClone = (Calendar) skssqzCalendar.clone();
        final Calendar sbrqCalendarClone = (Calendar) sbrqCalendar.clone();
        final Calendar tbrqCalendarClone = (Calendar) tbrqCalendar.clone();
        Calendar sbqxCalendarClone = null;
        if (!GyUtils.isNull(sbqxCalendar)) {
            sbqxCalendarClone = (Calendar) sbqxCalendar.clone();
        }
        //特殊规则部分（固定写算法）：jsqxbj（申报期限计算或者缴款计算标志）分别调用申报期限算法或者缴款期限算法。
        if (!GyUtils.isNull(jsqxbj) && "sbqxjs".equals(jsqxbj)) {
            //特殊的申报期限计算
            sbpzbSbqx = getGdSbqxJs(sbqxDm, skssqqCalendarClone, skssqzCalendarClone, sbrqCalendarClone);
        }
        //缴款期限计算暂不支持
        /*else if (!GyUtils.isNull(jsqxbj) && "jkqxjs".equals(jsqxbj)) {
            sbpzbSbqx = getGdJkqxJs(sbqxDm, skssqqCalendarClone, skssqzCalendarClone, sbrqCalendarClone);
        }*/
        if (GyUtils.isNull(sbpzbSbqx)) {
            //标准规则部分 :根据规则码写算法
            final Map<String, Object> pzbsbqxMap = getSbqxGzWithSbqxDm(sbqxDm, jsqxbj);
            if (!GyUtils.isNull(pzbsbqxMap)) {
                final String sbqxrqStr = (String) pzbsbqxMap.get("sbqxgz");
                if (!GyUtils.isNull(sbqxrqStr)) {
                    //标志位
                    boolean Bzw = sbqxrqStr.startsWith("BNDQ");
                    //得到规则后进行分离
                    final String firstStr = sbqxrqStr.substring(0, 2);//XX
                    final String secondStr = sbqxrqStr.substring(2, 4);//YY
                    final String thirdStr = sbqxrqStr.substring(4);//ZZ

                    final int tmpYear;
                    if (firstStr.equals("QQ")) {
                        //期内
                        skssqqCalendarClone.add(Calendar.DAY_OF_MONTH, Integer.parseInt(thirdStr));
                    } else if (firstStr.equals("QZ")) {
                        //期后
                        skssqzCalendarClone.add(Calendar.DAY_OF_MONTH, Integer.parseInt(secondStr.concat(thirdStr)));
                        skssqqCalendarClone = skssqzCalendarClone;
                    } else if (firstStr.endsWith("QD")) {
                        //固定的申报期限（全国统一的） 所属期起当年
                        skssqqCalendarClone.set(Calendar.MONTH, Integer.parseInt(secondStr) - 1);
                        skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, Integer.parseInt(thirdStr));
                    } else if (firstStr.endsWith("DN")) { // 当年
                        skssqqCalendarClone.set(Calendar.MONTH, Integer.parseInt(secondStr) - 1);
                        skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, Integer.parseInt(thirdStr));
                        //申报期限代码为65（当年2月28日或2月29日），需要判断是否闰年
                        if("65".equals(sbqxDm) && skssqqCalendarClone.get(Calendar.YEAR) % 4 == 0){
                            skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, 29);
                        }
                    } else if (firstStr.endsWith("CN")) {
                        //次年
                        tmpYear = skssqqCalendarClone.get(Calendar.YEAR);
                        skssqqCalendarClone.set(Calendar.YEAR, tmpYear + 1);
                        skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, Integer.parseInt(thirdStr));
                        skssqqCalendarClone.set(Calendar.MONTH, Integer.parseInt(secondStr) - 1);
                        //申报期限代码为95（当年2月28日或2月29日），需要判断是否闰年
                        if("95".equals(sbqxDm) && skssqqCalendarClone.get(Calendar.YEAR) % 4 == 0){
                            skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, 29);
                        }
                    } else if (firstStr.endsWith("CC")) {
                        //次次年
                        tmpYear = skssqqCalendarClone.get(Calendar.YEAR);
                        skssqqCalendarClone.set(Calendar.YEAR, tmpYear + 2);
                        skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, Integer.parseInt(thirdStr));
                        skssqqCalendarClone.set(Calendar.MONTH, Integer.parseInt(secondStr) - 1);
                    } else if (firstStr.endsWith("DT")) {
                        //当天
                        sbrqCalendarClone.add(Calendar.DAY_OF_MONTH, Integer.parseInt(thirdStr));
                        skssqqCalendarClone = sbrqCalendarClone;
                    } else if (firstStr.endsWith("DD")) {
                        //以系统时间为基准时间
                        tbrqCalendarClone.add(Calendar.DAY_OF_MONTH, Integer.parseInt(thirdStr));
                        skssqqCalendarClone = tbrqCalendarClone;
                    } else if (firstStr.endsWith("DY")) {
                        //如社保费属期为2012-07-01到2012-07-31，那么申报期限，缴款期限均为当月最后一天，即为2012-07-31
                        skssqzCalendarClone.set(Calendar.DAY_OF_MONTH, 1);
                        skssqzCalendarClone.add(Calendar.MONTH, 1);
                        skssqzCalendarClone.add(Calendar.DAY_OF_MONTH, -1);
                        skssqqCalendarClone = skssqzCalendarClone;
                    } else if (firstStr.endsWith("ND")) {
                        //当年最后一天
                        skssqzCalendarClone.set(Calendar.MONTH, Calendar.DECEMBER);
                        skssqzCalendarClone.set(Calendar.DAY_OF_MONTH, THIRTY_ONE);
                        skssqqCalendarClone = skssqzCalendarClone;
                    } else if (sbqxCalendarClone != null && firstStr.endsWith("QX")) {
                        //不知道
                        sbqxCalendarClone.add(Calendar.DAY_OF_MONTH, Integer.parseInt(thirdStr));
                        skssqqCalendarClone = sbqxCalendarClone;
                    } else if (firstStr.endsWith("QM")) {
                        //期满之日后月数
                        skssqqCalendarClone = Calendar.getInstance();
                        skssqqCalendarClone.setTimeInMillis(skssqzCalendarClone.getTimeInMillis());
                        final int month = skssqqCalendarClone.get(Calendar.MONTH) + Integer.parseInt(secondStr) + 1;
                        skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, 1);
                        skssqqCalendarClone.set(Calendar.MONTH, month);
                        skssqqCalendarClone.add(Calendar.DAY_OF_MONTH, -1);
                    } else if (firstStr.endsWith("JH")) {
                        //季后
                        final Map<String, Object> skssqMap = jsSkssq("08", skssqzCalendar.getTime(), "Y");
                        final Calendar skssqz = DateUtil.parseDate(DateUtil.doDateFormat((Date) skssqMap.get("skssqz"), "yyyy-MM-dd"));
                        skssqz.add(Calendar.DAY_OF_MONTH, Integer.parseInt(secondStr.concat(thirdStr)));
                        skssqqCalendarClone = skssqz;
                    }else if (Bzw && sbqxrqStr.startsWith("BNDQ")) {
                        //上半年日期
                        final String sbn =sbqxrqStr.substring(4, 8);
                        final int sbnByYY = Integer.parseInt(sbn.substring(0,2));
                        final int sbnByTT = Integer.parseInt(sbn.substring(2,4));
                        //下半年日期
                        final String xbn = sbqxrqStr.substring(8, 12);
                        final int xbnByYY = Integer.parseInt(xbn.substring(0,2));
                        final int xbnByTT = Integer.parseInt(xbn.substring(2,4));
                        sbpzbSbqx = Calendar.getInstance();
                        sbpzbSbqx.setTimeInMillis(skssqqCalendarClone.getTimeInMillis());
                        final int month = sbpzbSbqx.get(Calendar.MONTH) + 1;
                        if (month < 7) {
                            skssqqCalendarClone.set(Calendar.MONTH, sbnByYY-1);
                            skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, sbnByTT);
                            final Calendar checkCal = (Calendar) skssqqCalendarClone.clone();
                            //判断前需要将该申报期限改为对应月份的月初
                            checkCal.set(Calendar.DAY_OF_MONTH, 1);
                            //若首次申报上半年属期税款，且税源取得时间在申报期限之后，则取下半年的申报期限，房土可能需要
                            if(!GyUtils.isNull(syqdsj) && checkCal.get(Calendar.YEAR) == syqdsj.get(Calendar.YEAR) && syqdsj.compareTo(checkCal) >=0){
                                skssqqCalendarClone.set(Calendar.MONTH, xbnByYY-1);
                                skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, xbnByTT);
                            }
                        } else {
                            skssqqCalendarClone.set(Calendar.MONTH, xbnByYY-1);
                            skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, xbnByTT);
                            final Calendar checkCal = (Calendar)skssqqCalendarClone.clone();
                            //判断前需要将该申报期限改为对应月份的月初
                            checkCal.set(Calendar.DAY_OF_MONTH, 1);
                            //若首次申报下半年属期税款，且税源取得时间在申报期限之后，则取次年上半年的申报期限，房土可能需要
                            if(!GyUtils.isNull(syqdsj) && checkCal.get(Calendar.YEAR) == syqdsj.get(Calendar.YEAR) && syqdsj.compareTo(checkCal) >=0){
                                skssqqCalendarClone.add(Calendar.YEAR, 1);
                                skssqqCalendarClone.set(Calendar.MONTH, sbnByYY-1);
                                skssqqCalendarClone.set(Calendar.DAY_OF_MONTH, sbnByTT);
                            }
                        }
                    } else if (firstStr.endsWith("ZD")) {
                        //获取指定时间
                        final String zdsj = sbqxrqStr.substring(2);
                        skssqqCalendarClone = DateUtil.parseDate(zdsj);

                    }
                    //取不到的情况下，默认期起
                    sbpzbSbqx = skssqqCalendarClone;
                }
            }
        }
        return sbpzbSbqx;
    }

    /**
     * 申报期限计算规则
     * @param sbqxDm 申报期限代码
     * @param skssqqCalendar 税款所属期起
     * @param skssqzCalendar 税款所属期止
     * @param sbrqCalendar 申报日期
     * @return 返回值
     */
    private static Calendar getGdSbqxJs(String sbqxDm, Calendar skssqqCalendar, Calendar skssqzCalendar, Calendar sbrqCalendar) {
        //返回值
        Calendar sbpzbSbqx = null;
        //常量值
        final int THIRTY_ONE = 31;
        final int SEVEN = 7;
        final int TEN = 10;
        final int FOUR = 4;
        final int ONE = 1;
        final int FIVE = 5;
        final int ELEVEN = 11;
        final int Three=3;
        final int Six=6;
        final int Nine=9;
        final int Twelve=12;
        final int tmpYear;
        //用来封装执行完申报期限代码配置表后所得到的申报期限
        final Calendar skssqzCalendarClone = (Calendar) skssqzCalendar.clone();
        final Calendar sbrqCalendarClone = (Calendar) sbrqCalendar.clone();
        //几乎用户到以下分支
        if ("54".equals(sbqxDm)) {
            //4月30日或10月31日
            sbpzbSbqx = Calendar.getInstance();
            sbpzbSbqx.setTimeInMillis(skssqzCalendarClone.getTimeInMillis());
            final int month = sbpzbSbqx.get(Calendar.MONTH) + 1;
            if (month < SEVEN) {
                //上半年赋值成10月31号
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
                sbpzbSbqx.set(Calendar.MONTH, TEN);
                sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -1);
            } else {
                //下半年赋值成4月30号
                tmpYear = skssqzCalendarClone.get(Calendar.YEAR);
                sbpzbSbqx.set(Calendar.YEAR, tmpYear + 1);
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
                sbpzbSbqx.set(Calendar.MONTH, FOUR);
                sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -1);
            }
        } else if ("10".equals(sbqxDm)) {
            //申报日后下月月底：例如申报日期为2013-05-14，则申报期限=2013-06-30
            sbpzbSbqx = Calendar.getInstance();
            sbpzbSbqx.setTimeInMillis(sbrqCalendarClone.getTimeInMillis());
            final int month = sbpzbSbqx.get(Calendar.MONTH) + 2;
            sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
            sbpzbSbqx.set(Calendar.MONTH, month);
            sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -1);
        } else if ("55".equals(sbqxDm)) {
            //1月31日或7月31日
            sbpzbSbqx = Calendar.getInstance();
            sbpzbSbqx.setTimeInMillis(skssqzCalendarClone.getTimeInMillis());
            final int month = sbpzbSbqx.get(Calendar.MONTH) + 1;
            if (month < SEVEN) {
                //赋值成7月31号
                sbpzbSbqx.set(Calendar.MONTH, Calendar.JULY);
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, THIRTY_ONE);
            } else {
                //赋值成1月31号
                tmpYear = skssqzCalendarClone.get(Calendar.YEAR);
                sbpzbSbqx.set(Calendar.YEAR, tmpYear + 1);
                sbpzbSbqx.set(Calendar.MONTH, Calendar.JANUARY);
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, THIRTY_ONE);
            }
        } else if ("25".equals(sbqxDm)) {
            //1月31日或7月15日
            sbpzbSbqx = Calendar.getInstance();
            sbpzbSbqx.setTimeInMillis(skssqzCalendarClone.getTimeInMillis());
            final int month = sbpzbSbqx.get(Calendar.MONTH) + 1;
            if (month < SEVEN) {
                //赋值成7月15日
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
                sbpzbSbqx.set(Calendar.MONTH, SEVEN);
                sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -17);
            } else {
                //赋值成1月31日
                tmpYear = skssqzCalendarClone.get(Calendar.YEAR);
                sbpzbSbqx.set(Calendar.YEAR, tmpYear + 1);
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
                sbpzbSbqx.set(Calendar.MONTH, ONE);
                sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -1);
            }
        } else if ("26".equals(sbqxDm)) {
            //4月15日或10月15日
            sbpzbSbqx = Calendar.getInstance();
            sbpzbSbqx.setTimeInMillis(skssqzCalendarClone.getTimeInMillis());
            final int month = sbpzbSbqx.get(Calendar.MONTH) + 1;
            if (month < SEVEN) {
                //赋值成10月15日
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
                sbpzbSbqx.set(Calendar.MONTH, TEN);
                sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -17);
            } else {
                //赋值成4月15日
                tmpYear = skssqzCalendarClone.get(Calendar.YEAR);
                sbpzbSbqx.set(Calendar.YEAR, tmpYear + 1);
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
                sbpzbSbqx.set(Calendar.MONTH, FOUR);
                sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -16);
            }
        } else if ("27".equals(sbqxDm)) {
            //5月15日或11月15日
            sbpzbSbqx = Calendar.getInstance();
            sbpzbSbqx.setTimeInMillis(skssqzCalendarClone.getTimeInMillis());
            final int month = sbpzbSbqx.get(Calendar.MONTH) + 1;
            if (month < SEVEN) {
                //赋值成11月15日
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
                sbpzbSbqx.set(Calendar.MONTH, ELEVEN);
                sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -16);
            } else {
                //赋值5月15日成
                tmpYear = skssqzCalendarClone.get(Calendar.YEAR);
                sbpzbSbqx.set(Calendar.YEAR, tmpYear + 1);
                sbpzbSbqx.set(Calendar.DAY_OF_MONTH, 1);
                sbpzbSbqx.set(Calendar.MONTH, FIVE);
                sbpzbSbqx.add(Calendar.DAY_OF_MONTH, -17);
            }
        }else if ("59".equals(sbqxDm)){
            //当年3月31日或当年9月30日
            sbpzbSbqx = Calendar.getInstance();
            sbpzbSbqx.setTimeInMillis(skssqzCalendarClone.getTimeInMillis());
            final int month = sbpzbSbqx.get(Calendar.MONTH) + 1;
            if (month < SEVEN) {
                //赋值成3月31
                skssqzCalendarClone.set(Calendar.MONTH, 2);
                skssqzCalendarClone.set(Calendar.DAY_OF_MONTH, 31);
                sbpzbSbqx=skssqzCalendarClone;
            } else {
                //赋值9月30日
                skssqzCalendarClone.set(Calendar.MONTH, 8);
                skssqzCalendarClone.set(Calendar.DAY_OF_MONTH, 30);
                sbpzbSbqx=skssqzCalendarClone;
            }
        }else if ("76".equals(sbqxDm) || "64".equals(sbqxDm)){
            //当年3月、6月、9月、12月15 || 当年2月、5月、8月、11月15日
            sbpzbSbqx = Calendar.getInstance();
            sbpzbSbqx.setTimeInMillis(skssqzCalendarClone.getTimeInMillis());
            final int month = sbpzbSbqx.get(Calendar.MONTH) + 1;
            if (month <= Three) {
                if("76".equals(sbqxDm)){
                    //赋值成3月15
                    skssqzCalendarClone.set(Calendar.MONTH, 2);
                }else{
                    //赋值成2月15
                    skssqzCalendarClone.set(Calendar.MONTH, 1);
                }
                skssqzCalendarClone.set(Calendar.DAY_OF_MONTH, 15);
                sbpzbSbqx=skssqzCalendarClone;
            }else if(month<=Six){
                if("76".equals(sbqxDm)){
                    //赋值6月15日
                    skssqzCalendarClone.set(Calendar.MONTH, 5);
                }else{
                    //赋值5月15日
                    skssqzCalendarClone.set(Calendar.MONTH, 4);
                }
                skssqzCalendarClone.set(Calendar.DAY_OF_MONTH, 15);
                sbpzbSbqx=skssqzCalendarClone;
            }else if(month<=Nine){
                if("76".equals(sbqxDm)){
                    //赋值9月15日
                    skssqzCalendarClone.set(Calendar.MONTH, 8);
                }else{
                    //赋值8月15日
                    skssqzCalendarClone.set(Calendar.MONTH, 7);
                }
                skssqzCalendarClone.set(Calendar.DAY_OF_MONTH, 15);
                sbpzbSbqx=skssqzCalendarClone;
            }else if(month<=Twelve){
                if("76".equals(sbqxDm)){
                    //赋值12月15日
                    skssqzCalendarClone.set(Calendar.MONTH, 11);
                }else{
                    //赋值11月15日
                    skssqzCalendarClone.set(Calendar.MONTH, 10);
                }
                skssqzCalendarClone.set(Calendar.DAY_OF_MONTH, 15);
                sbpzbSbqx=skssqzCalendarClone;
            }
        }else if ("85".equals(sbqxDm)){
            //合同签订之日起730
            sbpzbSbqx =sbrqCalendar;
            sbpzbSbqx.add(Calendar.DAY_OF_MONTH, Integer.parseInt("730"));
        } else if ("47".equals(sbqxDm)) {
            //次年5月15日或次年9月15日(残保金专用)，税款属期为上半年时期限获取为次年5月15日，税款属期为下半年时期限获取为次年9月15日；当纳税期限为“年”时，期限必须是次年5月15日
            if (skssqqCalendar.get(Calendar.MONTH) == Calendar.JANUARY && (skssqzCalendar.get(Calendar.MONTH) == Calendar.JUNE || skssqzCalendar.get(Calendar.MONTH) == Calendar.DECEMBER)) {
                //上半年或整年的话是次年5月15日
                sbpzbSbqx = DateUtil.parseDate((skssqqCalendar.get(Calendar.YEAR) + 1) + "-05-15");
            } else {
                //否则认为是下半年，返回次年9月15日
                sbpzbSbqx = DateUtil.parseDate((skssqqCalendar.get(Calendar.YEAR) + 1) + "-09-15");
            }
        }
        return sbpzbSbqx;
    }

    /**
     * 查询申报期限规则
     * @param sbqxDm 申报期限代码
     * @param jsqxbj 计算标记
     * @return 返回值
     */
    public static Map<String, Object> getSbqxGzWithSbqxDm(String sbqxDm, String jsqxbj){
        String tableName = "dm_gy_sbqx";
        //缴款期限计算暂不支持
        if("jkqxjs".equals(jsqxbj)){
            tableName = "dm_gy_jkqx";
        }
//        final Map<String, Object> resMap = FtsCxsUtils.getIndexData(tableName, sbqxDm);
        final Map<String, Object> resMap = CacheUtils.getTableData(tableName , sbqxDm);
        String qxgz = null;
        if(!GyUtils.isNull(resMap)){
            qxgz = (String) resMap.get("sbqxgz");
            //缴款期限计算暂不支持
            if("jkqxjs".equals(jsqxbj)){
                qxgz = (String) resMap.get("jkqxgz");
            }
        }
        final Map<String, Object> pzbsbqxMap = new HashMap<>();
        pzbsbqxMap.put("sbqxgz", qxgz);
        return pzbsbqxMap;
    }

    /**
     * 获取下个月第一天
     * @param datestr 日期
     * @return 返回值
     */
    public static String getFirstDayofNextMonth(String datestr){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = sdf.parse(datestr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.DAY_OF_MONTH,1);
            calendar.add(Calendar.MONTH,1);
            return sdf.format(calendar.getTime());
        } catch (ParseException e) {
            log.error("",e);
        }
        return null;
    }

    /**
     * 查询申报期限维护表，查找是否存在当月的申报期限记录，有则取出，终止算法，返回前台；
     * @param zsxmDm 征收项目代码
     * @param zspmDm 征收品目代码
     * @param sbqxDm 申报期限代码
     * @param swjgList 本上级税务机关
     * @param hsbj 汇算标记
     * @param zclxlb 注册类型分类
     * @param yyyf 年月
     * @param nsqxDm 纳税期限代码
     * @param sbqxwbGroup 申报期限维护Group
     * @return 返回值
     */
    public static Date getSbqxJlInCache(String zsxmDm, String zspmDm, String sbqxDm, List<String> swjgList, String hsbj, String zclxlb, String yyyf, String nsqxDm, Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup) {
        Date sbqxwh = null;
        for (String swjgDm : swjgList) {
            final Map<Object, List<Map<String, Object>>> qxwhKvListMap = sbqxwbGroup.get(swjgDm);
            if (!GyUtils.isNull(qxwhKvListMap)) {
                final List<Map<String, Object>> qxwhKvList = qxwhKvListMap.get(yyyf);
                if (!GyUtils.isNull(qxwhKvList)) {
                    for (Map<String, Object> mp : qxwhKvList) {
                        //只添加期内有效的
                        final String yyyfTmp = (String) mp.get("YYYF");
                        final String zsxmDmTmp = (String) mp.get("ZSXM_DM");
                        final String zspmDmTmp = (String) mp.get("ZSPM_DM");
                        final String sbqxDmTemp = (String) mp.get("SBQX_DM");
                        final String zclxflTmp = (String) mp.get("ZCLXFL");
                        final String hsbzTmp = (String) mp.get("HSBZ");
                        final String nsqxDMTmp = (String) mp.get("NSQX_DM");
                        if (yyyf.equals(yyyfTmp)
                                && (GyUtils.isNull(nsqxDm) || GyUtils.isNull(nsqxDMTmp) || nsqxDm.equals(nsqxDMTmp))
                                && (GyUtils.isNull(zsxmDmTmp) || zsxmDmTmp.equals(zsxmDm))
                                && (GyUtils.isNull(zspmDm) || GyUtils.isNull(zspmDmTmp) || zspmDm.equals(zspmDmTmp))
                                && (GyUtils.isNull(sbqxDmTemp) || sbqxDmTemp.equals(sbqxDm))
                                && (GyUtils.isNull(zclxflTmp) || zclxflTmp.equals(zclxlb))
                                && (GyUtils.isNull(hsbzTmp) || hsbzTmp.equals(hsbj))) {
                            final Date sbqxwhTmp = DateUtil.toDate("yyyy-MM-dd",(String) mp.get("SBQX"));
                            if (sbqxwh == null || sbqxwh.before(sbqxwhTmp)) {
                                sbqxwh = sbqxwhTmp;
                            }
                        }
                    }
                    if (!GyUtils.isNull(sbqxwh)) {
                        //找到第一个有设置的税务机关，即终止，不再上溯
                        break;
                    }
                }
            }
        }
        return sbqxwh;
    }

    /**
     * 查询节假日配置表查询节假日，有，则根据申报期限、节假日算法计算申报期限，无则继续流程
     * @param swjgList 本上级税务机关
     * @param yyyf 年月
     * @param sbqx 申报期限
     * @param jjrGroup 节假日group
     */
    @SneakyThrows
    public static void getSbqxWithJJRInCache(List<String> swjgList, String yyyf, Calendar sbqx, Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup) {
        //规则如下
        //根据税务机关、月份、节假日标志从节假日表中筛选可使用的记录；
        //取得记录，延长天数=节假日结束日期 -节假日起始日期
        //如果延长天数>=3，则：申报期限=申报期限+延长天数，否则，不做任何处理。
        //如果未查询到记录，则查询上级税务机关有无节假日，直至总局，若未查询到可使用记录，结束节假日计算，继续下一步。
        //节假日表中配置数据时，按天进行配置，即不存在连续的两天或以上的配置；
        //得到节假日结果集，然后计算需要延长的天数
        //放假天数
        double fjts = 0.0;
        //期限内放假天数，比如申报期限为15日，放假时间是1-3,14-20，计算出的期限内放假天数应该为3+2（14,15）
        double qxnfjts = 0.0;
        Calendar sbpzbSbqx = (Calendar) sbqx.clone();
        final Date sbpzbSbqxDate = sbpzbSbqx.getTime();
        final List<Map<String, Object>> jjrResultTemp = new ArrayList<>();
        for (String swjgDm : swjgList) {
            final Map<Object, List<Map<String, Object>>> jjrGroupMap = jjrGroup.get(swjgDm);
            if (!GyUtils.isNull(jjrGroupMap)) {
                final List<Map<String, Object>> jjrKvList = jjrGroupMap.get(yyyf);
                if (!GyUtils.isNull(jjrKvList)) {
                    for (Map<String, Object> mp : jjrKvList) {
                        //只添加期内有效的
                        final String yyyfTmp = (String) mp.get("yyyf");
                        final String jjrbz = (String) mp.get("jjrbz");
                        final Date jjrqsrq = DateUtil.toDate("yyyy-MM-dd",(String) mp.get("jjrqsrq"));
                        if (yyyf.equals(yyyfTmp) && jjrqsrq.compareTo(sbpzbSbqxDate) <= 0 && "Y".equals(jjrbz)) {
                            Map<String,Object> mmp = new HashMap<>();
                            mmp.put("jjrzzrq", DateUtil.toDate("yyyy-MM-dd",(String) mp.get("jjrzzrq")));
                            mmp.put("jjrqsrq", jjrqsrq);
                            jjrResultTemp.add(mmp);
                        }
                    }
                    if (!GyUtils.isNull(jjrResultTemp)) {
                        //找到第一个有设置的税务机关，即终止，不再上溯
                        break;
                    }
                }
            }
        }
        //获取指定年月缓存表数据
        List<Map<String, Object>> yyyfListFilter = getYyyfList(swjgList, yyyf, jjrGroup);
        //处理数据
        if (!GyUtils.isNull(jjrResultTemp)) {
            //获取新的list，不改变原数据
            final List<Map<String, Object>> jjrResult = jjrResultTemp.stream().map(m -> {
                final Map<String, Object> map = new HashMap<>();
                map.put("jjrzzrq", m.get("jjrzzrq"));
                map.put("jjrqsrq", m.get("jjrqsrq"));
                return map;
            }).sorted(Comparator.comparing(s -> (Date) s.get("jjrzzrq"))).collect(Collectors.toList());
            //对于单独配置天数，连续的三天，然后进行一天一天的配置的情况，先进行合并
            for (int i = jjrResult.size() - 1; i >= 0; i--) {
                final Date jjrzzrqDateZ = (Date) jjrResult.get(i).get("jjrzzrq");
                final Date jjrzzrqDateQ = (Date) jjrResult.get(i).get("jjrqsrq");
                if (jjrzzrqDateZ == null || jjrzzrqDateQ == null) {
                    continue;
                }
                if(i-1<0){
                    break;
                }
                final Date jjrqsrqDate = (Date) jjrResult.get(i-1).get("jjrqsrq");
                final Date jjrzzrqDate = (Date) jjrResult.get(i-1).get("jjrzzrq");
                if (!GyUtils.isNull(jjrqsrqDate) && !GyUtils.isNull(jjrzzrqDate)) {
                    if(CxsDateUtils.calcDays(jjrzzrqDateQ,jjrzzrqDate)==1){
                        jjrResult.get(i-1).put("jjrzzrq",jjrzzrqDateZ);
                        jjrResult.remove(i);
                    }
                }
            }
            //计算放假天数，期限内放假天数
            for (Map<String, Object> singleResult : jjrResult) {
                final Date jjrqsrqDate = (Date) singleResult.get("jjrqsrq");
                final Date jjrzzrqDate = (Date) singleResult.get("jjrzzrq");
                if (jjrqsrqDate == null || jjrzzrqDate == null) {
                    continue;
                }
                //计算放假天数
                final int jsfjts = Math.abs(CxsDateUtils.calcDays(jjrzzrqDate, jjrqsrqDate));
                fjts = FtsCxsUtils.add(fjts, jsfjts);
                fjts++;
                //计算期限内放假天数
                if(sbpzbSbqxDate.after(jjrzzrqDate)){
                    final int qxnfjtsTemp = Math.abs(CxsDateUtils.calcDays(jjrzzrqDate, jjrqsrqDate));
                    qxnfjts= FtsCxsUtils.add(qxnfjts, qxnfjtsTemp);
                } else if(sbpzbSbqxDate.compareTo(jjrzzrqDate)<=0 && sbpzbSbqxDate.compareTo(jjrqsrqDate)>=0){//期限在节假日区间内
                    final int qxnfjtsTemp = Math.abs(CxsDateUtils.calcDays(sbpzbSbqxDate, jjrqsrqDate));
                    qxnfjts= FtsCxsUtils.add(qxnfjts, qxnfjtsTemp);
                }
                qxnfjts++;
            }
            //重新获取年月数据
            final String yyyfNew1 = DateUtil.doDateFormat(sbpzbSbqx.getTime(), "yyyyMM");
            if(!yyyf.equals(yyyfNew1)){
                yyyfListFilter = getYyyfList(swjgList, yyyfNew1, jjrGroup);
            }
            if(qxnfjts<=2 && !checkIsGzr(sbpzbSbqx, yyyfListFilter)){
                //期限内的放假天数小于3天，如果申报期限不是工作日，顺延到工作日
                while(!checkIsGzr(sbpzbSbqx, yyyfListFilter)){
                    sbpzbSbqx.add(Calendar.DATE, 1);
                }
            } else if(qxnfjts >= 3){
                //期限内的放假天数大于3天才进行顺延
                final Calendar sbpzbSbqxTemp = (Calendar) sbpzbSbqx.clone();
                int tsNew = 0;
                while(tsNew < qxnfjts){
                    sbpzbSbqx.add(Calendar.DATE, 1);
                    //重新获取年月数据
                    final String yyyfNew2 = DateUtil.doDateFormat(sbpzbSbqx.getTime(), "yyyyMM");
                    if(!yyyfNew1.equals(yyyfNew2)){
                        yyyfListFilter = getYyyfList(swjgList, yyyfNew2, jjrGroup);
                    }
                    //判断是否为工作日，若不为工作日，则跳过该日，继续往后顺延
                    if(!checkIsGzr(sbpzbSbqx, yyyfListFilter)){
                        continue;
                    }
                    tsNew++;
                }
                //节假日顺延后的申报期限不能超过当月的29号（2月不超过27号）- 现在逻辑是这个
                //需求沟通，顺延之前期限已经大于当月的29号（2月27号）时，继续顺延
                final Calendar maxSbqx = CxsDateUtils.convUtilDateToUtilCalendar(sbpzbSbqxDate);//最大申报期限
                if(maxSbqx.get(Calendar.MONTH) == Calendar.FEBRUARY){
                    //2月
                    maxSbqx.set(Calendar.DAY_OF_MONTH, 27);
                }else{
                    //其他月份
                    maxSbqx.set(Calendar.DAY_OF_MONTH, 29);
                }
                if(sbpzbSbqxTemp.compareTo(maxSbqx) <= 0 && sbpzbSbqx.compareTo(maxSbqx) > 0){
                    sbpzbSbqx = (Calendar) maxSbqx.clone();
                }
            }
        } else {
            //重新获取年月数据
            final String yyyfNew3 = DateUtil.doDateFormat(sbpzbSbqx.getTime(), "yyyyMM");
            if(!yyyf.equals(yyyfNew3)){
                yyyfListFilter = getYyyfList(swjgList, yyyfNew3, jjrGroup);
            }
            if(!checkIsGzr(sbpzbSbqx, yyyfListFilter)){
                while(!checkIsGzr(sbpzbSbqx, yyyfListFilter)){
                    sbpzbSbqx.add(Calendar.DATE, 1);
                }
            }
        }
        sbqx.setTime(sbpzbSbqx.getTime());

        sbpzbSbqx.getTime();
    }

    /**
     * 获取指定年月数据
     * @param swjgList 本上级税务机关
     * @param yyyf 年月
     * @param jjrGroup 节假日group
     * @return 返回值
     */
    public static List<Map<String, Object>> getYyyfList(List<String> swjgList, String yyyf, Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup){
        final List<Map<String, Object>> jjrResult = new ArrayList<>();
        for (String swjgDm : swjgList) {
            final Map<Object, List<Map<String, Object>>> jjrGroupMap = jjrGroup.get(swjgDm);
            if (!GyUtils.isNull(jjrGroupMap)) {
                final List<Map<String, Object>> jjrKvList = jjrGroupMap.get(yyyf);
                if (!GyUtils.isNull(jjrKvList)) {
                    //找到第一个有设置的税务机关，即终止，不再上溯
                    jjrResult.addAll(jjrKvList);
                    break;
                }
            }
        }
        return jjrResult;
    }

    /**
     * 判断是否为工作日
     * @param baseCal 基准日期
     * @param jjrResult 过滤年月后的节假日list
     * @return 返回值
     */
    public static boolean checkIsGzr(Calendar baseCal, List<Map<String, Object>> jjrResult){
        //判断是否为工作日，即非周末节假日
        boolean flag = true;
        final Date baseDate = baseCal.getTime();
        if (!GyUtils.isNull(jjrResult)) {
            for (Map<String, Object> map : jjrResult) {
                final Date jjrqsrqDate = DateUtil.toDate("yyyy-MM-dd",(String) map.get("jjrqsrq"));
                final Date jjrzzrqDate = DateUtil.toDate("yyyy-MM-dd",(String) map.get("jjrzzrq"));
                if (!jjrqsrqDate.after(baseDate) && !jjrzzrqDate.before(baseDate)) {
                    flag = false;
                    break;
                }
            }
        }

        return flag;
    }

    /**
     * 判断是否需要节假日顺延
     * @param swjgDm 税务机关代码
     * @param zsxmDm 征收项目代码
     * @param sbqxDm 申报期限代码
     * @return 返回值
     */
    public static boolean checkIsJjrsy(String swjgDm, String zsxmDm, String sbqxDm) {
        //针对山西的房产税10110、城镇土地使用税10112、车船税10114、残疾人就业保障金30218四种税费不需要进行节假日和周末的顺延
        final boolean a = !(swjgDm.startsWith("114") || swjgDm.startsWith("214"))
                || !("10110".equals(zsxmDm) || "10112".equals(zsxmDm) || "10114".equals(zsxmDm));
        //针对安徽特有的两个申报期限83（次年12月31日）和84（申报日后30天）不进行节假日和周末的顺延
        final boolean b = !(swjgDm.startsWith("134") || swjgDm.startsWith("234"))
                || !("83".equals(sbqxDm) || "84".equals(sbqxDm));
        //针对厦门对于房产税、城镇土地使用税，当申报期限、缴款期限代码为55（1月31日或7月31日），不进行假节日以及周末顺延。
        final boolean c = !(swjgDm.startsWith("13502")) || !("10110".equals(zsxmDm) || "10112".equals(zsxmDm)) || !("55".equals(sbqxDm));
        //针对西藏，期限为83（次年12月31日）时不进行节假日和周末的顺延
        final boolean d = !(swjgDm.startsWith("154") && "83".equals(sbqxDm));
        //申报期限为当年或次年某月月底的，不进行节假日顺延
        final boolean e = !("56".equals(sbqxDm) || "18".equals(sbqxDm) || "20".equals(sbqxDm) || "21".equals(sbqxDm) || "22".equals(sbqxDm)
                || "23".equals(sbqxDm) || "24".equals(sbqxDm) || "28".equals(sbqxDm) || "29".equals(sbqxDm) || "31".equals(sbqxDm)
                || "32".equals(sbqxDm) || "34".equals(sbqxDm) || "35".equals(sbqxDm) || "74".equals(sbqxDm) || "66".equals(sbqxDm)
                || "16".equals(sbqxDm) || "17".equals(sbqxDm) || "72".equals(sbqxDm) || "80".equals(sbqxDm) || "81".equals(sbqxDm)
                || "90".equals(sbqxDm) || "65".equals(sbqxDm) || "69".equals(sbqxDm) || "09".equals(sbqxDm) || "98".equals(sbqxDm)
                || "95".equals(sbqxDm) || "93".equals(sbqxDm));
        return a && b && c && d && e;
    }

    /**
     * 查询征期调整配置表，有，则根据申报期限、征期调整算法计算申报期限，无则继续流程；
     * @param zsxmDm 征收项目代码
     * @param swjgList 本上级税务机关
     * @param yzpzzlDm 应征凭证种类代码
     * @param nsqxDm 纳税期限代码
     * @param yyyf 年月
     * @param sbpzbSbqx 申报期限
     * @param zqtzGroup 整齐调整group
     */
    public static void getSbqxWithZQTZInCache(String zsxmDm, List<String> swjgList, String yzpzzlDm, String nsqxDm, String yyyf, Calendar sbpzbSbqx, Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup) {
        //查询征期调整配置表，有，则根据申报期限、征期调整算法计算申报期限，无则继续流程；
        //根据税务机关、月份、征收项目、纳税期限代码、凭证种类代码（可空）、有效日期起、有效日期止从征期调整表中筛选可使用的记录；
        //取得记录，然后取记录的顺延天数+申报期限
        //如果未查询到记录，则查询上级税务机关有无征期调整，直至总局，若未查询到可使用记录，则执行下一步。
        //其中征收项目代码与纳税期限代码是联合使用，当有征收项目代码时，必须配置相应的纳税期限代码
        Double syts = null;
        for (String swjgDm : swjgList) {
            final Map<Object, List<Map<String, Object>>> zqtzGroupMap = zqtzGroup.get(swjgDm);
            if (!GyUtils.isNull(zqtzGroupMap)) {
                final List<Map<String, Object>> jjrKvList = zqtzGroupMap.get(yyyf);
                if (!GyUtils.isNull(jjrKvList)) {
                    for (Map<String, Object> mp : jjrKvList) {
                        //只添加期内有效的
                        final String yyyfTmp = (String) mp.get("yyyf");
                        final String zsxmDmTmp = (String) mp.get("zsxmDm");
                        final String yzpzzlDmTmp = (String) mp.get("yzpzzlDm");
                        final String nsqxDmTmp = (String) mp.get("nsqxDm");
                        final Date yxqsrq = DateUtil.toDate("yyyy-MM-dd",(String) mp.get("yxqsrq"));
                        final Date yxzzrq = DateUtil.toDate("yyyy-MM-dd",(String) mp.get("txzzrq"));
                        if(yyyf.equals(yyyfTmp)
                                && (GyUtils.isNull(zsxmDmTmp) || zsxmDmTmp.equals(zsxmDm))
                                && (GyUtils.isNull(yzpzzlDmTmp) || yzpzzlDmTmp.equals(yzpzzlDm))
                                && (GyUtils.isNull(nsqxDmTmp) || nsqxDm.equals(nsqxDmTmp))
                                && sbpzbSbqx.getTime().compareTo(yxqsrq)>=0
                                && sbpzbSbqx.getTime().compareTo(yxzzrq)<=0){
                            final double sytsTmp = Double.parseDouble((String) mp.get("syts"));
                            if(syts == null || syts < sytsTmp){
                                syts = sytsTmp;
                            }
                        }
                    }
                    if (!GyUtils.isNull(syts)) {
                        //找到第一个有设置的税务机关，即终止，不再上溯
                        break;
                    }
                }
            }
        }
        if (syts != null) {
            //如果查出了结果
            sbpzbSbqx.add(Calendar.DAY_OF_MONTH, (int) (syts.longValue()));
        }
    }

    /**
     * 查询节假日配置表查询周末休息，有，则根据申报期限、周末顺延算法计算申报期限，无则继续流程
     * @param swjgList 本上级税务机关
     * @param yyyf 年月
     * @param sbpzbSbqx 申报期限
     * @param jjrGroup 节假日group
     */
    public static void getSbqxWithZMInCache(List<String> swjgList, String yyyf, Calendar sbpzbSbqx, Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup) {
        //查询节假日配置表查询周末休息，有，则根据申报期限、周末顺延算法计算申报期限，无则继续流程
        //根据税务机关、月份、节假日标志从节假日表中筛选可使用的记录，
        //取得记录，申报期限=节假日结束日期+1
        //如果未查询到记录，则查询上级税务机关有无节假日，直至总局，若未查询到可使用记录，退出计算。
        //将最终计算得出的申报期限返回前台。
        Calendar sbqCal = sbpzbSbqx;
        Date sbpzbSbqxDate = sbpzbSbqx.getTime();
        final List<Map<String, Object>> jjrResultTemp = new ArrayList<>();
        for (String swjgDm : swjgList) {
            final Map<Object, List<Map<String, Object>>> jjrGroupMap = jjrGroup.get(swjgDm);
            if (!GyUtils.isNull(jjrGroupMap)) {
                final List<Map<String, Object>> jjrKvList = jjrGroupMap.get(yyyf);
                if (!GyUtils.isNull(jjrKvList)) {
                    for (Map<String, Object> mp : jjrKvList) {
                        //只添加期内有效的
                        final String yyyfTmp = (String) mp.get("yyyf");
                        final String jjrbz = (String) mp.get("jjrbz");
                        if (yyyf.equals(yyyfTmp) && "N".equals(jjrbz)) {
                            Date jjrzzrq = DateUtil.toDate("yyyy-MM-dd",(String) mp.get("jjrzzrq"));
                            Date jjrqsrq = DateUtil.toDate("yyyy-MM-dd",(String) mp.get("jjrqsrq"));
                            Map<String,Object> mmp = new HashMap<>();
                            mmp.put("jjrzzrq", jjrzzrq);
                            mmp.put("jjrqsrq", jjrqsrq);
                            jjrResultTemp.add(mmp);
                        }
                    }
                    if (!GyUtils.isNull(jjrResultTemp)) {
                        //找到第一个有设置的税务机关，即终止，不再上溯
                        break;
                    }
                }
            }
        }

        if (!GyUtils.isNull(jjrResultTemp)) {
            //获取新的list，不改变原数据
            final List<Map<String, Object>> jjrResult = jjrResultTemp.stream().map(m -> {
                final Map<String, Object> map = new HashMap<>();
                map.put("jjrzzrq", m.get("jjrzzrq"));
                map.put("jjrqsrq", m.get("jjrqsrq"));
                return map;
            }).sorted(Comparator.comparing(s -> (Date) s.get("jjrzzrq"))).collect(Collectors.toList());
            for (Map<String, Object> singleResult : jjrResult) {
                final Date jjrqsrqDate = (Date) singleResult.get("jjrqsrq");
                final Date jjrzzrqDate = (Date) singleResult.get("jjrzzrq");
                if (jjrqsrqDate == null || jjrzzrqDate == null) {
                    continue;
                }
                //计算周末天数
                if (!jjrqsrqDate.after(sbpzbSbqxDate) && !jjrzzrqDate.before(sbpzbSbqxDate)) {
                    final Calendar jjrzzrqCalendarClone = Calendar.getInstance();
                    jjrzzrqCalendarClone.setTime(jjrzzrqDate);
                    jjrzzrqCalendarClone.add(Calendar.DAY_OF_MONTH, 1);
                    sbpzbSbqxDate = jjrzzrqCalendarClone.getTime();
                    sbqCal = jjrzzrqCalendarClone;
                }
            }
        }
        //同步赋值sbpzbSbqx
        sbpzbSbqx.setTime(sbqCal.getTime());
    }

//-------------------------------------------获取申报期限end-------------------------------------------------------------

    /**
     * 获取当前日期Date
     * @return 返回值
     */
    public static Date getNowTime(){
        Date date = new Date();
        return date;
    }

    /**
     * 获取当前日期Str
     * @return 返回值
     */
    public static String getDayofNow(){
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date());
    }

    /**
     * 获取最小最大日期
     * @param list 待处理list
     * @param flag min/max标志
     * @return 返回值
     */
    @SneakyThrows
    public static Date minmaxDate(List<String> list, String flag) {
        final List<Date> tempList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (!GyUtils.isNull(list) && "min".equals(flag)) {
            for (String dateStr : list) {
                final Date dt = sdf.parse(dateStr);
//                final Date dt = DateUtil.toDate(dtString, "yyyy-MM-dd");
                tempList.add(dt);
            }
            Date MinDate = null;
            if(GyUtils.isNotNull(tempList)){
                MinDate = Collections.min(tempList);
            }
            return MinDate;
        } else if (!GyUtils.isNull(list) && "max".equals(flag)) {
            for (String dateStr : list) {
                final Date dt = sdf.parse(dateStr);
//                final Date dt = DateUtil.toDate(dateStr, "yyyy-MM-dd");
                tempList.add(dt);
            }
            final Date MaxDate = Collections.max(tempList);
            return MaxDate;
        } else {
            return null;
        }
    }

    /**
     * Double四舍五入2位小数
     * @param value 入参
     * @return 返回值
     */
    public static Double formatDouble(Double value) {
        if(GyUtils.isNull(value)){
            return null;
        }
        BigDecimal bigDecimal = new BigDecimal(value);
        bigDecimal = bigDecimal.setScale(2, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    /**
     * 计算合计(简单加法)
     * @param a 数字a
     * @param b 数字b
     * @return 返回值
     */
    public static Double addHj(Double a, Double b) {
        return a + b;
    }

    /**
     * obj转换字符串
     * @param obj 入参
     * @return 返回值
     */
    public static String cast2StrNew(Object obj) {
        String returnVal = null;
        if (obj != null) {
            if (obj instanceof String) {
                returnVal = (String) obj;
            } else if (obj instanceof BigDecimal || obj instanceof Integer || obj instanceof Long) {
                final BigDecimal dec = new BigDecimal(obj.toString());
                returnVal = dec.stripTrailingZeros().toPlainString();//把科学计数法格式成正常格式, 去掉尾数的0
            }
            //先转成BigDecimal，再转成String，可以避免 转型出现 3.0E-4 这样的科学计数法字符串
            else if (obj instanceof Double) {
                final BigDecimal dec = new BigDecimal(obj.toString());
                returnVal = dec.stripTrailingZeros().toPlainString();//把科学计数法格式成正常格式, 去掉尾数的0
            } else if (obj instanceof Date) {
                final Date da = (Date) obj;
                returnVal = CxsDateUtils.dateToString(da);
            } else if (obj instanceof Calendar) {
                final Calendar ca = (Calendar) obj;
                returnVal = CxsDateUtils.dateToString(CxsDateUtils.convertCalendarToDate(ca));
            } else if (obj instanceof Boolean) {
                final Boolean objBooleanValue = ((Boolean) obj).booleanValue();
                if (objBooleanValue) {
                    returnVal = "Y";
                } else if (!objBooleanValue) {
                    returnVal = "N";
                }
            }
            //默认toString，需注意某些特定类型的转型
            else {
                returnVal = obj.toString();
            }
        }
        return returnVal;
    }

    /**
     * obj转换double
     * @param obj 入参
     * @return 返回值
     */
    public static Double cDouble(Object obj) {
        Double returnVal = 0.00;
        if (obj != null) {
            if (obj instanceof Double) {
                returnVal = (Double) obj;
            } else if (obj instanceof BigDecimal) {
                returnVal = ((BigDecimal) obj).doubleValue();
            } else if (obj instanceof Integer) {
                returnVal = ((Integer) obj).doubleValue();
            } else if (obj instanceof Long) {
                returnVal = ((Long) obj).doubleValue();
            } else if (obj instanceof String) {
                if (((String) obj).trim().length() == 0) {
                    returnVal = 0.00;
                } else {
                    returnVal = Double.parseDouble((String) obj);
                }
            }
        }
        return returnVal;
    }

    /**
     * 获取年度1月1日
     * @param ssnd 年度
     * @return 返回值
     */
    public static String firstDayofYearBySsnd(String ssnd){
        final String firstday = ssnd+"-01-01";
        return firstday;
    }

    /**
     * 获取年度12月31日
     * @param ssnd 年度
     * @return 返回值
     */
    public static String lastDayofYearBySsnd(String ssnd){
        final String lastday = ssnd+"-12-31";
        return lastday;
    }

    /**
     * 获取传入月份第一天
     * @param date 日期
     * @return 返回值
     */
    public static Date getFirstDayofMonth(Date date){
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH,1);
        return calendar.getTime();
    }

    /**
     * 获取传入月份最后一天
     * @param date 日期
     * @return 返回值
     */
    public static Date getlastDayofMonth(Date date){
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH,calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    /**
     * 日期转字符串yyyy-MM-dd格式
     * @param date 日期
     * @return 返回值
     */
    public static String dateToStr(Date date){
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    /**
     * 码值转换-卡片信息
     * @param list 入参
     * @return 返回值
     */
    public static List<CchxwsnssbKpVO> getZsxmMcKp(List<CchxwsnssbKpVO> list) {
        final List<CchxwsnssbKpVO> kpVOList = new ArrayList<>();
        for (CchxwsnssbKpVO kpVO : list) {
//            final Map<String, Object> dmGySwjgMap = FtsCxsUtils.getIndexData("DM_GY_ZSXM", kpVO.getZsxmDm());
            final Map<String, Object> dmGySwjgMap = CacheUtils.getTableData("dm_gy_zsxm", kpVO.getZsxmDm());
            kpVO.setZsxmmc(GyUtils.isNull(dmGySwjgMap) ? "" : (String) dmGySwjgMap.get("zsxmmc"));
            kpVOList.add(kpVO);
        }
        return kpVOList;
    }

    /**
     * 码值转换-主表明细
     * @param list 入参
     * @return 返回值
     */
    public static List<CchxwsnssbzbmxVO> getZsxmMcZbmx(List<CchxwsnssbzbmxVO> list) {
        final List<CchxwsnssbzbmxVO> cchxwsnssbzbmxVOList = new ArrayList<>();
        for (CchxwsnssbzbmxVO cchxwsnssbzbmxVO : list) {
            final Map<String, Object> zsxmMap = CacheUtils.getTableData("dm_gy_zsxm", cchxwsnssbzbmxVO.getZsxmDm());
            final Map<String, Object> zspmMap = CacheUtils.getTableData("dm_gy_zspm", cchxwsnssbzbmxVO.getZspmDm());
            final Map<String, Object> zszmMap = CacheUtils.getTableData("dm_gy_zszm", cchxwsnssbzbmxVO.getZszmDm());
            final Map<String, Object> yzpzzlMap = CacheUtils.getTableData("dm_gy_dzbzdszl", cchxwsnssbzbmxVO.getYzpzzlDm());
            cchxwsnssbzbmxVO.setZsxmmc(GyUtils.isNull(zsxmMap) ? "" : (String) zsxmMap.get("zsxmmc"));
            cchxwsnssbzbmxVO.setZspmmc(GyUtils.isNull(zspmMap) ? "" : (String) zspmMap.get("zspmmc"));
            cchxwsnssbzbmxVO.setZszmmc(GyUtils.isNull(zszmMap) ? "" : (String) zszmMap.get("zszmmc"));
            cchxwsnssbzbmxVO.setYzpzzlmc(GyUtils.isNull(yzpzzlMap) ? "" : (String) yzpzzlMap.get("dzbzdszlmc"));
            cchxwsnssbzbmxVOList.add(cchxwsnssbzbmxVO);
        }
        return cchxwsnssbzbmxVOList;
    }

    /**
     * 码值转换-附表明细
     * @param list 入参
     * @return 返回值
     */
    public static List<CchxwsnssbfbmxVO> getZsxmMcFbmx(List<CchxwsnssbfbmxVO> list) {
        final List<CchxwsnssbfbmxVO> cchxwsnssbfbmxVOList = new ArrayList<>();
        for (CchxwsnssbfbmxVO cchxwsnssbfbmxVO : list) {
            final Map<String, Object> zsxmMap = CacheUtils.getTableData("dm_gy_zsxm", cchxwsnssbfbmxVO.getZsxmDm());
            final Map<String, Object> zspmMap = CacheUtils.getTableData("dm_gy_zspm", cchxwsnssbfbmxVO.getZspmDm());
            final Map<String, Object> zszmMap = CacheUtils.getTableData("dm_gy_zszm", cchxwsnssbfbmxVO.getZszmDm());
            final Map<String, Object> ssjmxzMap = CacheUtils.getTableData("dm_gy_ssjmxz", cchxwsnssbfbmxVO.getSsjmxzDm());
            final Map<String, Object> swsxMap = CacheUtils.getTableData("dm_gy_swsx", cchxwsnssbfbmxVO.getSwsxDm());
            final Map<String, Object> zywrwlbMap = CacheUtils.getTableData("dm_dj_zywrwlb", cchxwsnssbfbmxVO.getZywrwlbdm());
            cchxwsnssbfbmxVO.setZsxmmc(GyUtils.isNull(zsxmMap) ? "" : (String) zsxmMap.get("zsxmmc"));
            cchxwsnssbfbmxVO.setZspmmc(GyUtils.isNull(zspmMap) ? "" : (String) zspmMap.get("zspmmc"));
            cchxwsnssbfbmxVO.setZszmmc(GyUtils.isNull(zszmMap) ? "" : (String) zszmMap.get("zszmmc"));
            cchxwsnssbfbmxVO.setSsjmxzmc(GyUtils.isNull(ssjmxzMap) ? "" : (String) ssjmxzMap.get("ssjmxzmc"));
            cchxwsnssbfbmxVO.setSwsxmc(GyUtils.isNull(swsxMap) ? "" : (String) swsxMap.get("swsxmc"));
            cchxwsnssbfbmxVO.setZywrwlbmc(GyUtils.isNull(zywrwlbMap) ? "" : (String) zywrwlbMap.get("zywrwlbmc"));
            cchxwsnssbfbmxVOList.add(cchxwsnssbfbmxVO);
        }
        return cchxwsnssbfbmxVOList;
    }

    /**
     * 获取征收项目名称by缴费
     * @param list 缴费信息list
     * @return 返回值
     */
    public static List<CchxwsnssbjfxxVO> getZsxmmcJfxx(List<CchxwsnssbjfxxVO> list){
        if(GyUtils.isNull(list)){
            return list;
        }
        final List<CchxwsnssbjfxxVO> kpVOList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (CchxwsnssbjfxxVO kpVO : list) {
            final Map<String, Object> dmGySwjgMap = FtsCxsUtils.getIndexData("dm_gy_zsxm", kpVO.getZsxmDm());
            kpVO.setZsxmmc(!GyUtils.isNull(dmGySwjgMap) ? (String) dmGySwjgMap.get("zsxmmc") : "");
//            kpVO.setJkqx(!FtsCxsUtils.isNull(kpVO.getJkqx()) ? DateUtil.doDateFormat(kpVO.getJkqx(), "yyyy-MM-dd") : "");
//            kpVO.setSkssqq(!FtsCxsUtils.isNull(kpVO.getSkssqq()) ? DateUtil.doDateFormat(kpVO.getSkssqq(), "yyyy-MM-dd") : "");
//            kpVO.setSkssqz(!FtsCxsUtils.isNull(kpVO.getSkssqz()) ? DateUtil.doDateFormat(kpVO.getSkssqz(), "yyyy-MM-dd") : "");
            kpVO.setJkqx(!GyUtils.isNull(kpVO.getJkqx()) ? sdf.format(kpVO.getJkqx()) : "");
            kpVO.setSkssqq(!GyUtils.isNull(kpVO.getSkssqq()) ? sdf.format(kpVO.getSkssqq()) : "");
            kpVO.setSkssqz(!GyUtils.isNull(kpVO.getSkssqz()) ? sdf.format(kpVO.getSkssqz()) : "");
            kpVOList.add(kpVO);
        }
        return kpVOList;
    }

    /**
     * 处理汇总申报信息
     * @param rtList 申报信息list
     * @param syMap 汇总后的税源
     * @param zsxmList 征收项目list
     * @param zspmList 征收品目list
     * @param zszmList 征收子目list
     * @param hzkey 汇总的主键
     * @param sfsswr 是否需要四舍五入
     * @return 返回值
     * @throws Exception 异常
     */
    public static List<SbxxGridlbVO> hzsbxx(List<SbxxGridlbVO> rtList, Map<String, List<Map<String, Object>>> syMap, List<Map<String, Object>> zsxmList,
                                            List<Map<String, Object>> zspmList, List<Map<String, Object>> zszmList, String[] hzkey, boolean sfsswr) throws Exception {
        final Map<String, SbxxGridlbVO> hzMap = new HashMap<>();
        if (!GyUtils.isNull(rtList)) {
            for (SbxxGridlbVO vo : rtList) {
                final String zsxmDm = vo.getZsxmDm();
                //获取分组key
                final StringBuilder key = new StringBuilder();
                boolean flag = false;
                try {
                    for (String s : hzkey) {
                        if (("10121".equals(zsxmDm) || "10107".equals(zsxmDm)) && "sl1".equals(s)) {
                            //资源税、环保税处理，不拼接sl1
                            flag = true;
                            continue;
                        }
                        if ("10112".equals(zsxmDm)){
                            continue;
                        }
                        if ("10110".equals(zsxmDm) && !"zspmDm".equals(s)){
                            continue;
                        }
                        //反射获取属性值
                        final Field field = vo.getClass().getDeclaredField(s);
                        field.setAccessible(true);
                        final String val = cast2StrNew(field.get(vo));
                        key.append(val);
                    }
                    //环保税增加税源编号(排放口编号)分组key，仅处理按季申报(即拥有税源编号)大气水两类品目 101211***大气污染物 101212***水污染物
                    if ("10121".equals(zsxmDm) && !GyUtils.isNull(vo.getSybh()) && !GyUtils.isNull(vo.getZspmDm()) &&
                            (vo.getZspmDm().startsWith("101211") || vo.getZspmDm().startsWith("101212"))){
                        key.append(vo.getSybh());
                    }
                    if ("10112".equals(zsxmDm) || "10110".equals(zsxmDm)){
                        key.append(vo.getSyuuid());
                    }
                } catch (Exception e) {
                    FtsCxsUtils.getTrace(e);
                }
                //--以下没用到，开始
                final String pzxh = vo.getPzxh();
                //获取税源pzxhMap，初始化一般pzxh均为空，此处syMap拥有一个null值key
                List<Map<String, Object>> syList = syMap.get(pzxh);
                if (GyUtils.isNull(syList)) {
                    syList = new ArrayList<>();
                    syMap.put(pzxh, syList);
                }
                //放入税源信息
                final Map<String, Object> symxMap = new HashMap<>();
                final String zspmDm = vo.getZspmDm();
                //final String zszmDm = vo.getZszmDm();
                symxMap.put("syuuid", vo.getSyuuid());
                if ("10114".equals(zsxmDm)) {
                    //车船税分支处理
                    log.info("==========ccszspm=========="+zspmDm);
                    if(!GyUtils.isNull(zspmDm)){
                        if (zspmDm.startsWith("1011406")) {
                            symxMap.put("sybzDm1", "DM_SB_SYBZ_CB");
                        } else {
                            symxMap.put("sybzDm1", "DM_SB_SYBZ_CL");
                        }
                    }
                }
                syList.add(symxMap);
                //--以下没用到，结束

                //根据分组key，汇总数据
                if (!GyUtils.isNull(hzMap.get(key.toString()))) {
                    final SbxxGridlbVO sbvo = hzMap.get(key.toString());
                    //合计数据
                    sbvo.setJsyj(addHj(cDouble(sbvo.getJsyj()), cDouble(vo.getJsyj())));
                    sbvo.setYnse(addHj(cDouble(sbvo.getYnse()), cDouble(vo.getYnse())));
                    sbvo.setJmse(addHj(cDouble(sbvo.getJmse()), cDouble(vo.getJmse())));
                    sbvo.setYjse(addHj(cDouble(sbvo.getYjse()), cDouble(vo.getYjse())));
                    sbvo.setYbtse(addHj(cDouble(sbvo.getYbtse()), cDouble(vo.getYbtse())));
                    //取最大税款所属期起止
//                    final Calendar skssqq = CxsDateUtils.parseDate(sbvo.getSkssqq());
//                    final Calendar skssqq_ = CxsDateUtils.parseDate(vo.getSkssqq());
                    final Calendar skssqq = CxsDateUtils.parseDate(sbvo.getSkssqq());
                    final Calendar skssqq1 = CxsDateUtils.parseDate(vo.getSkssqq());
                    if (skssqq.compareTo(skssqq1) > 0) {
                        sbvo.setSkssqq(CxsDateUtils.toDateTimeStr(skssqq1));
                    }
                    final Calendar skssqz = CxsDateUtils.parseDate(sbvo.getSkssqz());
                    final Calendar skssqz1 = CxsDateUtils.parseDate(vo.getSkssqz());
                    if (skssqz.compareTo(skssqz1) < 0) {
                        sbvo.setSkssqz(CxsDateUtils.toDateTimeStr(skssqz1));
                    }
                    //如果是资源、环保，并且税源税率不一致，移除计税依据，存疑
                    if (flag && !vo.getSl1().equals(sbvo.getSl1())) {
                        sbvo.setSl1(null);
                        sbvo.setJsyj(null);
                    }
                } else {
                    //放入汇总信息
                    final SbxxGridlbVO sbvo = new SbxxGridlbVO();
                    BeanUtil.copyProperties(vo, sbvo);
                    hzMap.put(key.toString(), sbvo);
                }
            }
        }
        //获取内部所有分组数据
        final List<SbxxGridlbVO> skxxmxList = new ArrayList<>(hzMap.values());
        for (SbxxGridlbVO vo : skxxmxList) {
            //处理double数据
            if (!GyUtils.isNull(vo.getJsyj())) {
                vo.setJsyj(FtsCxsUtils.round(!GyUtils.isNull(vo.getJsyj()) ? vo.getJsyj() : new Double("0"), 2));
            }
            if (sfsswr) {
                vo.setYnse(FtsCxsUtils.round(!GyUtils.isNull(vo.getYnse()) ? vo.getYnse() : new Double("0"), 2));
                vo.setJmse(FtsCxsUtils.round(!GyUtils.isNull(vo.getJmse()) ? vo.getJmse() : new Double("0"), 2));
                vo.setYjse(FtsCxsUtils.round(!GyUtils.isNull(vo.getYjse()) ? vo.getYjse() : new Double("0"), 2));
                vo.setYbtse(FtsCxsUtils.round(!GyUtils.isNull(vo.getYbtse()) ? vo.getYbtse() : new Double("0"), 2));
            }
            //核心说的是2022-07-01号立法之前处理，现在分支永远进不去，先不管
            if ("yhs".equals(vo.getZsxmDm())) {
                //印花税应补退税额特殊处理
                Double ybtse = vo.getYbtse();
                if ("101110105".equals(vo.getZspmDm())) {
                    if (ybtse < 1D && ybtse >= 0.1D) {
                        ybtse = 1D;
                    } else if (ybtse < 0.1D && ybtse > 0D) {
                        ybtse = 0D;
                    }
                } else {
                    if (ybtse < 0.1D && ybtse > 0D) {
                        ybtse = 0D;
                    }
                }
                vo.setYbtse(FtsCxsUtils.round(ybtse, 1));
            }
        }
        return skxxmxList;
    }

//    /**
//     * 查询纳税人信息
//     * @param djxh 登记序号
//     * @param nsrsbh 纳税人识别号
//     * @return 返回值
//     */
//    public  JbxxmxsjVO queryNsrxxByApi(String djxh, String nsrsbh){
//        JbxxmxsjVO jbxxmxsjVO = null;
////        ServerResponse<CxNsrjbxxAndFyhyxxResVO> response = new ServerResponse<>();
//        CommonResult<ZnsbMhzcQyjbxxmxResVO> response = new CommonResult<>();
//        if(!FtsCxsUtils.isNull(djxh)){
////            response = nsrxxcxApi.queryNsrxxBySql(djxh,"");
////        }else {
//            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
//            znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
//            response = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
//        }
////        final CxNsrjbxxAndFyhyxxResVO data = response.getData();
//        final ZnsbMhzcQyjbxxmxResVO data = response.getData();
//        if (!FtsCxsUtils.isNull(data) && !FtsCxsUtils.isNull(data.getJbxxmxsj())) {
//            final List<JbxxmxsjVO> nsrxxList = data.getJbxxmxsj();
//            if(!FtsCxsUtils.isNull(djxh)){
//                jbxxmxsjVO = nsrxxList.get(0);
//            }else {
//                jbxxmxsjVO = FtsCxsUtils.dofierNsrxx(nsrxxList, nsrsbh);
//            }
//        }
//        return jbxxmxsjVO;
//    }
//
//    /**
//     * 保存逾期申报信息-mongo
//     * @param djxh 登记序号
//     * @param zsxmdm 征收项目代码
//     * @param skssqq 税款所属期起
//     * @param skssqz 税款所属期止
//     * @return 返回值
//     */
//    public static String saveFilterYqsb(String djxh,String zsxmdm,String skssqq,String skssqz) {
//        //保存结果
//        String bcFlag = "";
//        //是否不为为房土税源
//        boolean filterFt = !"10110".equals(zsxmdm) && !"10112".equals(zsxmdm);
//        //分组key
//        String textStr = zsxmdm+","+skssqq+","+skssqz;
//        //查询数据
//        MongoTemporaryVO mongoTemporaryVO = MongoUtils.queryTemporary(djxh);
//        MongoTemporaryVO mongo = new MongoTemporaryVO();
//        if(!FtsCxsUtils.isNull(mongoTemporaryVO) && filterFt){
//            //增量更新
//            mongo.setDjxh(djxh);
//            mongo.setText(mongoTemporaryVO.getText() + ";" + textStr);
//            MongoUtils.updateTemporary(mongo);
//            bcFlag = "updateSuccess";
//        }else {
//            if(filterFt){
//                //新增
//                mongo.setDjxh(djxh);
//                mongo.setText(textStr);
//                mongo.setLrrq(new Date());
//                MongoUtils.saveTemporary(mongo);
//                bcFlag="saveSuccess";
//            }
//        }
//        return bcFlag;
//    }

    /**
     * 增加税源信息By城镇土地使用税
     * 实际循环次数为cxsSySkxxVOList大小
     * @param cxsSySbxxVOList 税源申报信息列表
     * @param cxsSySkxxVOList 税源税款信息列表
     * @param zspmGroupVOList 返回值
     */
    public static void addSyxxByCztdsys(List<CxsSySbxxVO> cxsSySbxxVOList, List<CxsSySkxxVO> cxsSySkxxVOList, List<CztdsysZspmGroupVO> zspmGroupVOList) {
        log.info("addSyxxByCztdsys_dateA1_" + DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        int i = 0;
        if (!GyUtils.isNull(cxsSySbxxVOList) && !GyUtils.isNull(cxsSySkxxVOList)) {
            //根据税源UUID+tddjDm+skssqq+skssqz+sl1分组、应税UUID二次分组
            final Map<String, Map<String, List<CxsSySkxxVO>>> collect = cxsSySkxxVOList.stream()
                    .filter(f -> !GyUtils.isNull(f.getSyuuid()) && !GyUtils.isNull(f.getYsuuid()) && !GyUtils.isNull(f.getSsny()))
                    .peek(m -> {
                        //m.setSkssqq(DateUtil.formatToStr(m.getSkssqq(), "yyyy-MM-dd"));
                        //m.setSkssqz(DateUtil.formatToStr(m.getSkssqz(), "yyyy-MM-dd"));
                        m.setSl1(GyUtils.isNull(m.getSl1()) ? 0d : FtsCxsUtils.round(m.getSl1(), 2));
                    })
                    .sorted(Comparator.comparing(CxsSySkxxVO::getSsny, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(g ->
                                    g.getSyuuid() + "-" +
                                            g.getTddjDm() + "-" +
                                            g.getSkssqq() + "-" +
                                            g.getSkssqz() + "-" +
                                            g.getSl1(),
                            Collectors.groupingBy(CxsSySkxxVO::getYsuuid)));
            log.info("addSyxxByCztdsys_dateA2_" + DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
            //根据zsxmDm，zspmDm，skssqq，skssqz，sl1分组
            final Map<String, List<CxsSySbxxVO>> zspmKeyMap = cxsSySbxxVOList.stream()
                    .filter(f -> !GyUtils.isNull(f.getZspmDm()) && f.getZspmDm().length() == 9)
                    .peek(m -> {
                        //m.setSkssqq(DateUtil.formatToStr(m.getSkssqq(), "yyyy-MM-dd"));
                        //m.setSkssqz(DateUtil.formatToStr(m.getSkssqz(), "yyyy-MM-dd"));
                        m.setSl1(GyUtils.isNull(m.getSl1()) ? 0d : FtsCxsUtils.round(m.getSl1(), 2));
                    })
                    .collect(Collectors.groupingBy(g ->
                            g.getZsxmDm() + "-" +
                                    g.getZspmDm() + "-" +
                                    g.getSkssqq() + "-" +
                                    g.getSkssqz() + "-" +
                                    g.getSl1()));
            log.info("addSyxxByCztdsys_dateA3_" + DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
            for (Map.Entry<String, List<CxsSySbxxVO>> entry : zspmKeyMap.entrySet()) {
                //一层内部VO
                final CztdsysZspmGroupVO zspmGroupVO = new CztdsysZspmGroupVO();
                //征收品目合计数据
                double zspmJsyj = 0d;
                double zspmYnse = 0d;
                double zspmJmse = 0d;
                double zspmYjse = 0d;
                double zspmYbtse = 0d;
                //二层List
                final List<CztdsysSyuuidGroupVO> syuuidGroupVOList = new ArrayList<>();
                //循环申报信息，单税源
                for (CxsSySbxxVO sbxxVO : entry.getValue()) {
                    //合计数据
                    zspmJsyj = FtsCxsUtils.add(zspmJsyj, GyUtils.isNull(sbxxVO.getJsyj()) ? 0d : sbxxVO.getJsyj());
                    zspmYnse = FtsCxsUtils.add(zspmYnse, GyUtils.isNull(sbxxVO.getYnse()) ? 0d : sbxxVO.getYnse());
                    zspmJmse = FtsCxsUtils.add(zspmJmse, GyUtils.isNull(sbxxVO.getJmse()) ? 0d : sbxxVO.getJmse());
                    zspmYjse = FtsCxsUtils.add(zspmYjse, GyUtils.isNull(sbxxVO.getYjse()) ? 0d : sbxxVO.getYjse());
                    zspmYbtse = FtsCxsUtils.add(zspmYbtse, GyUtils.isNull(sbxxVO.getYbtse()) ? 0d : sbxxVO.getYbtse());
                    //基础数据
                    zspmGroupVO.setZsxmDm(sbxxVO.getZsxmDm());
                    zspmGroupVO.setZspmDm(sbxxVO.getZspmDm());
                    zspmGroupVO.setSkssqq(sbxxVO.getSkssqq());
                    zspmGroupVO.setSkssqz(sbxxVO.getSkssqz());
                    zspmGroupVO.setSl1(sbxxVO.getSl1());
                    zspmGroupVO.setSbuuid(sbxxVO.getSbuuid());
                    zspmGroupVO.setPzxh(sbxxVO.getPzxh());
                    zspmGroupVO.setCxstysbuuid(sbxxVO.getCxstysbuuid());
                    //获取同税源同品目税款信息
                    final String keyTemp = sbxxVO.getSyuuid() + "-" +
                            sbxxVO.getZspmDm().substring(6) + "-" +
                            sbxxVO.getSkssqq() + "-" +
                            sbxxVO.getSkssqz() + "-" +
                            sbxxVO.getSl1();
                    final Map<String, List<CxsSySkxxVO>> syuuidColloct = collect.get(keyTemp);
                    //三层List
                    final List<CztdsysYsuuidGroupVO> ysuuidGroupVOList = new ArrayList<>();
                    if (!GyUtils.isNull(syuuidColloct)) {
                        //循环税款信息，单税源多应税
                        for (Map.Entry<String, List<CxsSySkxxVO>> entryTemp : syuuidColloct.entrySet()) {
                            //三层内部VO
                            final CztdsysYsuuidGroupVO ysuuidGroupVO = new CztdsysYsuuidGroupVO();
                            double ysuuidJsyj = 0d;
                            double ysuuidYnse = 0d;
                            double ysuuidJmse = 0d;
                            double ysuuidPhjmse = 0d;
                            double ysuuidYjse = 0d;
                            double ysuuidYbtse = 0d;
                            String ssnyQ = "";
                            String ssnyZ = "";
                            //循环应税信息，单应税多年月
                            for (CxsSySkxxVO skTemp : entryTemp.getValue()) {
                                i++;
                                //合计数据
                                ysuuidJsyj = FtsCxsUtils.add(ysuuidJsyj, GyUtils.isNull(skTemp.getJsyj()) ? 0d : skTemp.getJsyj());
                                ysuuidYnse = FtsCxsUtils.add(ysuuidYnse, GyUtils.isNull(skTemp.getYnse()) ? 0d : skTemp.getYnse());
                                ysuuidJmse = FtsCxsUtils.add(ysuuidJmse, GyUtils.isNull(skTemp.getJmse()) ? 0d : skTemp.getJmse());
                                ysuuidPhjmse = FtsCxsUtils.add(ysuuidPhjmse, GyUtils.isNull(skTemp.getPhjmse()) ? 0d : skTemp.getPhjmse());
                                ysuuidYjse = FtsCxsUtils.add(ysuuidYjse, GyUtils.isNull(skTemp.getYjse()) ? 0d : skTemp.getYjse());
                                ysuuidYbtse = FtsCxsUtils.add(ysuuidYbtse, GyUtils.isNull(skTemp.getYbtse()) ? 0d : skTemp.getYbtse());
                                //获取最大最小年月
                                if (!GyUtils.isNull(skTemp.getSsny())) {
                                    if (GyUtils.isNull(ssnyQ) || ssnyQ.compareTo(skTemp.getSsny()) > 0) {
                                        ssnyQ = skTemp.getSsny();
                                    }
                                    if (GyUtils.isNull(ssnyZ) || ssnyZ.compareTo(skTemp.getSsny()) < 0) {
                                        ssnyZ = skTemp.getSsny();
                                    }
                                }
                                //基础数据
                                ysuuidGroupVO.setSyuuid(skTemp.getSyuuid());
                                ysuuidGroupVO.setYsuuid(skTemp.getYsuuid());
                                ysuuidGroupVO.setSl1(skTemp.getSl1());
                                ysuuidGroupVO.setTddjDm(skTemp.getTddjDm());
                            }
                            //数值格式化
                            ysuuidGroupVO.setJsyj(FtsCxsUtils.round(ysuuidJsyj, 2));
                            ysuuidGroupVO.setYnse(FtsCxsUtils.round(ysuuidYnse, 2));
                            ysuuidGroupVO.setJmse(FtsCxsUtils.round(ysuuidJmse, 2));
                            ysuuidGroupVO.setPhjmse(FtsCxsUtils.round(ysuuidPhjmse, 2));
                            ysuuidGroupVO.setJmseHj(FtsCxsUtils.add(ysuuidGroupVO.getJmse(), ysuuidGroupVO.getPhjmse()));
                            ysuuidGroupVO.setYjse(FtsCxsUtils.round(ysuuidYjse, 2));
                            ysuuidGroupVO.setYbtse(FtsCxsUtils.round(ysuuidYbtse, 2));
                            //根据最大最小年月获取税款所属期起止
                            if (!GyUtils.isNull(ssnyQ) && ssnyQ.length() == 6) {
                                final String ssnyrQ = ssnyQ.substring(0,4) + "-" + ssnyQ.substring(4) + "-" + "01";
                                ysuuidGroupVO.setSkssqq(ssnyrQ);
                            }
                            if (!GyUtils.isNull(ssnyZ) && ssnyZ.length() == 6) {
                                final String ssnyrZ = ssnyZ.substring(0,4) + "-" + ssnyZ.substring(4);
                                ysuuidGroupVO.setSkssqz(FtsCxsUtils.getLastDayOfMonth(ssnyrZ));
                            }
                            ysuuidGroupVOList.add(ysuuidGroupVO);
                        }
                    }
                    //二层内部VO
                    final CztdsysSyuuidGroupVO syuuidGroupVO = new CztdsysSyuuidGroupVO();
                    syuuidGroupVO.setSyuuid(sbxxVO.getSyuuid());
                    syuuidGroupVO.setSybh(sbxxVO.getSybh());
                    syuuidGroupVO.setSkssqq(sbxxVO.getSkssqq());
                    syuuidGroupVO.setSkssqz(sbxxVO.getSkssqz());
                    syuuidGroupVO.setYbtse(FtsCxsUtils.round(GyUtils.isNull(sbxxVO.getYbtse()) ? 0d : sbxxVO.getYbtse(), 2));
                    syuuidGroupVO.setCztdsysYsuuidGroupVO(ysuuidGroupVOList);
                    syuuidGroupVOList.add(syuuidGroupVO);
                }
                //数值格式化
                zspmGroupVO.setJsyj(FtsCxsUtils.round(zspmJsyj, 2));
                zspmGroupVO.setYnse(FtsCxsUtils.round(zspmYnse, 2));
                zspmGroupVO.setJmse(FtsCxsUtils.round(zspmJmse, 2));
                zspmGroupVO.setYjse(FtsCxsUtils.round(zspmYjse, 2));
                zspmGroupVO.setYbtse(FtsCxsUtils.round(zspmYbtse, 2));
                zspmGroupVO.setZspmmc(FtsCxsUtils.getMcByDm("dm_gy_zspm", "zspmmc", zspmGroupVO.getZspmDm()));
                zspmGroupVO.setCztdsysSyuuidGroupList(syuuidGroupVOList);
                zspmGroupVOList.add(zspmGroupVO);
            }
        }
        log.info("addSyxxByCztdsys_dateA4_" + DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        log.info("addSyxxByCztdsys_dateA5_i=" + i);
    }

    /**
     * 增加税源信息By房产税
     * @param cxsSySbxxVOList 税源申报信息列表
     * @param fcsskVOList 税源信息列表
     * @param cjczBz 从价从租标志
     * @param zspmGroupVOList 返回值
     */
    public static void addSyxxByFcs(List<CxsSySbxxVO> cxsSySbxxVOList, List<CxsSyFcsskVO> fcsskVOList, String cjczBz, List<FcsZspmGroupVO> zspmGroupVOList) {
        String zspmDmTemp = "";
        if ("cj".equals(cjczBz)) {
            zspmDmTemp = "101100700";
        } else {
            zspmDmTemp = "101100800";
        }
        final String zspmDmNew = zspmDmTemp;
        final String zspmmcNew = FtsCxsUtils.getMcByDm("dm_gy_zspm", "zspmmc", zspmDmNew);
        log.info("addSyxxByFcs_dateB1_" + DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        int i = 0;
        if (!GyUtils.isNull(cxsSySbxxVOList) && !GyUtils.isNull(fcsskVOList)) {
            //根据税源UUID+skssqq+skssqz+sl1分组、应税UUID二次分组
            final Map<String, Map<String, List<CxsSyFcsskVO>>> collect = fcsskVOList.stream()
                    .filter(f -> !GyUtils.isNull(f.getFyxxuuid()) && !GyUtils.isNull(f.getYsuuid()) && !GyUtils.isNull(f.getSsny()))
                    .peek(m -> {
                        //m.setSkssqq(DateUtil.formatToStr(m.getSkssqq(), "yyyy-MM-dd"));
                        //m.setSkssqz(DateUtil.formatToStr(m.getSkssqz(), "yyyy-MM-dd"));
                        m.setSl1(GyUtils.isNull(m.getSl1()) ? 0d : FtsCxsUtils.round(m.getSl1(), 3));
                    })
                    .sorted(Comparator.comparing(CxsSyFcsskVO::getSsny, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(g ->
                                    g.getFyxxuuid() + "-" +
                                            g.getSkssqq() + "-" +
                                            g.getSkssqz() + "-" +
                                            g.getSl1(),
                            Collectors.groupingBy(CxsSyFcsskVO::getYsuuid)));
            log.info("addSyxxByFcs_dateB2_" + DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
            //根据zsxmDm，zspmDm，skssqq，skssqz，sl1分组
            final Map<String, List<CxsSySbxxVO>> zspmKeyMap = cxsSySbxxVOList.stream()
                    .filter(f -> !GyUtils.isNull(f.getZspmDm()) && f.getZspmDm().length() == 9 && zspmDmNew.equals(f.getZspmDm()))
                    .peek(m -> {
                        //m.setSkssqq(DateUtil.formatToStr(m.getSkssqq(), "yyyy-MM-dd"));
                        //m.setSkssqz(DateUtil.formatToStr(m.getSkssqz(), "yyyy-MM-dd"));
                        m.setSl1(GyUtils.isNull(m.getSl1()) ? 0d : FtsCxsUtils.round(m.getSl1(), 3));
                    })
                    .collect(Collectors.groupingBy(g ->
                            g.getZsxmDm() + "-" +
                                    g.getZspmDm() + "-" +
                                    g.getSkssqq() + "-" +
                                    g.getSkssqz() + "-" +
                                    g.getSl1()));
            log.info("addSyxxByFcs_dateB3_" + DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
            for (Map.Entry<String, List<CxsSySbxxVO>> entry : zspmKeyMap.entrySet()) {
                //一层内部VO
                final FcsZspmGroupVO zspmGroupVO = new FcsZspmGroupVO();
                //征收品目合计数据
                double zspmJsyj = 0d;
                double zspmYnse = 0d;
                double zspmJmse = 0d;
                double zspmYjse = 0d;
                double zspmYbtse = 0d;
                //二层List
                final List<FcsSyuuidGroupVO> syuuidGroupVOList = new ArrayList<>();
                //循环申报信息，单税源
                for (CxsSySbxxVO sbxxVO : entry.getValue()) {
                    //合计数据
                    zspmJsyj = FtsCxsUtils.add(zspmJsyj, GyUtils.isNull(sbxxVO.getJsyj()) ? 0d : sbxxVO.getJsyj());
                    zspmYnse = FtsCxsUtils.add(zspmYnse, GyUtils.isNull(sbxxVO.getYnse()) ? 0d : sbxxVO.getYnse());
                    zspmJmse = FtsCxsUtils.add(zspmJmse, GyUtils.isNull(sbxxVO.getJmse()) ? 0d : sbxxVO.getJmse());
                    zspmYjse = FtsCxsUtils.add(zspmYjse, GyUtils.isNull(sbxxVO.getYjse()) ? 0d : sbxxVO.getYjse());
                    zspmYbtse = FtsCxsUtils.add(zspmYbtse, GyUtils.isNull(sbxxVO.getYbtse()) ? 0d : sbxxVO.getYbtse());
                    //基础数据
                    zspmGroupVO.setZsxmDm(sbxxVO.getZsxmDm());
                    zspmGroupVO.setZspmDm(sbxxVO.getZspmDm());
                    zspmGroupVO.setSkssqq(sbxxVO.getSkssqq());
                    zspmGroupVO.setSkssqz(sbxxVO.getSkssqz());
                    zspmGroupVO.setSl1(sbxxVO.getSl1());
                    zspmGroupVO.setSbuuid(sbxxVO.getSbuuid());
                    zspmGroupVO.setPzxh(sbxxVO.getPzxh());
                    zspmGroupVO.setCxstysbuuid(sbxxVO.getCxstysbuuid());
                    //获取同税源同品目税款信息
                    final String keyTemp = sbxxVO.getSyuuid() + "-" +
                            sbxxVO.getSkssqq() + "-" +
                            sbxxVO.getSkssqz() + "-" +
                            sbxxVO.getSl1();
                    final Map<String, List<CxsSyFcsskVO>> syuuidColloct = collect.get(keyTemp);
                    //三层List
                    final List<FcsYsuuidGroupVO> ysuuidGroupVOList = new ArrayList<>();
                    if (!FtsCxsUtils.isNull(syuuidColloct)) {
                        //循环税款信息，单税源多应税
                        for (Map.Entry<String, List<CxsSyFcsskVO>> entryTemp : syuuidColloct.entrySet()) {
                            //三层内部VO
                            final FcsYsuuidGroupVO ysuuidGroupVO = new FcsYsuuidGroupVO();
                            double ysuuidJsyj = 0d;
                            double ysuuidYnse = 0d;
                            double ysuuidJmse = 0d;
                            double ysuuidPhjmse = 0d;
                            double ysuuidYjse = 0d;
                            double ysuuidYbtse = 0d;
                            double ysuuidSbzjsr = 0d;
                            String ssnyQ = "";
                            String ssnyZ = "";
                            //循环应税信息，单应税多年月
                            for (CxsSyFcsskVO skTemp : entryTemp.getValue()) {
                                i++;
                                //合计数据
                                ysuuidJsyj = FtsCxsUtils.add(ysuuidJsyj, GyUtils.isNull(skTemp.getJsyj()) ? 0d : skTemp.getJsyj());
                                ysuuidYnse = FtsCxsUtils.add(ysuuidYnse, GyUtils.isNull(skTemp.getYnse()) ? 0d : skTemp.getYnse());
                                ysuuidJmse = FtsCxsUtils.add(ysuuidJmse, GyUtils.isNull(skTemp.getJmse()) ? 0d : skTemp.getJmse());
                                ysuuidPhjmse = FtsCxsUtils.add(ysuuidPhjmse, GyUtils.isNull(skTemp.getPhjmse()) ? 0d : skTemp.getPhjmse());
                                ysuuidYjse = FtsCxsUtils.add(ysuuidYjse, GyUtils.isNull(skTemp.getYjse()) ? 0d : skTemp.getYjse());
                                ysuuidYbtse = FtsCxsUtils.add(ysuuidYbtse, GyUtils.isNull(skTemp.getYbtse()) ? 0d : skTemp.getYbtse());
                                ysuuidSbzjsr = FtsCxsUtils.add(ysuuidSbzjsr, GyUtils.isNull(skTemp.getSbzjsr()) ? 0d : skTemp.getSbzjsr());
                                //获取最大最小年月
                                if (!FtsCxsUtils.isNull(skTemp.getSsny())) {
                                    if (FtsCxsUtils.isNull(ssnyQ) || ssnyQ.compareTo(skTemp.getSsny()) > 0) {
                                        ssnyQ = skTemp.getSsny();
                                    }
                                    if (FtsCxsUtils.isNull(ssnyZ) || ssnyZ.compareTo(skTemp.getSsny()) < 0) {
                                        ssnyZ = skTemp.getSsny();
                                    }
                                }
                                //基础数据
                                ysuuidGroupVO.setSyuuid(skTemp.getFyxxuuid());
                                ysuuidGroupVO.setYsuuid(skTemp.getYsuuid());
                                ysuuidGroupVO.setSl1(skTemp.getSl1());
                                if ("cj".equals(cjczBz)) {
                                    ysuuidGroupVO.setJsbl(FtsCxsUtils.isNull(skTemp.getJsbl()) ? 0d : skTemp.getJsbl());
                                    ysuuidGroupVO.setFcyz(FtsCxsUtils.round(FtsCxsUtils.isNull(skTemp.getFcyz()) ? 0d : skTemp.getFcyz(), 2));
                                    ysuuidGroupVO.setCzfcyz(FtsCxsUtils.round(FtsCxsUtils.isNull(skTemp.getCzwyz()) ? 0d : skTemp.getCzwyz(), 2));
                                }
                            }
                            //数值格式化
                            if ("cz".equals(cjczBz)) {
                                ysuuidGroupVO.setSbzjsr(FtsCxsUtils.round(ysuuidSbzjsr, 2));
                            }
                            ysuuidGroupVO.setJsyj(FtsCxsUtils.round(ysuuidJsyj, 2));
                            ysuuidGroupVO.setYnse(FtsCxsUtils.round(ysuuidYnse, 2));
                            ysuuidGroupVO.setJmse(FtsCxsUtils.round(ysuuidJmse, 2));
                            ysuuidGroupVO.setPhjmse(FtsCxsUtils.round(ysuuidPhjmse, 2));
                            ysuuidGroupVO.setJmseHj(FtsCxsUtils.add(ysuuidGroupVO.getJmse(), ysuuidGroupVO.getPhjmse()));
                            ysuuidGroupVO.setYjse(FtsCxsUtils.round(ysuuidYjse, 2));
                            ysuuidGroupVO.setYbtse(FtsCxsUtils.round(ysuuidYbtse, 2));
                            //根据最大最小年月获取税款所属期起止
                            if (!FtsCxsUtils.isNull(ssnyQ) && ssnyQ.length() == 6) {
                                final String ssnyrQ = ssnyQ.substring(0,4) + "-" + ssnyQ.substring(4) + "-" + "01";
                                ysuuidGroupVO.setSkssqq(ssnyrQ);
                            }
                            if (!FtsCxsUtils.isNull(ssnyZ) && ssnyZ.length() == 6) {
                                final String ssnyrZ = ssnyZ.substring(0,4) + "-" + ssnyZ.substring(4);
                                ysuuidGroupVO.setSkssqz(FtsCxsUtils.getLastDayOfMonth(ssnyrZ));
                            }
                            ysuuidGroupVOList.add(ysuuidGroupVO);
                        }
                    }
                    //二层内部VO
                    final FcsSyuuidGroupVO syuuidGroupVO = new FcsSyuuidGroupVO();
                    syuuidGroupVO.setSyuuid(sbxxVO.getSyuuid());
                    syuuidGroupVO.setSybh(sbxxVO.getSybh());
                    syuuidGroupVO.setSkssqq(sbxxVO.getSkssqq());
                    syuuidGroupVO.setSkssqz(sbxxVO.getSkssqz());
                    syuuidGroupVO.setYbtse(FtsCxsUtils.round(FtsCxsUtils.isNull(sbxxVO.getYbtse()) ? 0d : sbxxVO.getYbtse(), 2));
                    syuuidGroupVO.setFcsYsuuidGroupVO(ysuuidGroupVOList);
                    syuuidGroupVOList.add(syuuidGroupVO);
                }
                //数值格式化
                zspmGroupVO.setJsyj(FtsCxsUtils.round(zspmJsyj, 2));
                zspmGroupVO.setYnse(FtsCxsUtils.round(zspmYnse, 2));
                zspmGroupVO.setJmse(FtsCxsUtils.round(zspmJmse, 2));
                zspmGroupVO.setYjse(FtsCxsUtils.round(zspmYjse, 2));
                zspmGroupVO.setYbtse(FtsCxsUtils.round(zspmYbtse, 2));
                zspmGroupVO.setZspmmc(zspmmcNew);
                zspmGroupVO.setFcsSyuuidGroupList(syuuidGroupVOList);
                zspmGroupVOList.add(zspmGroupVO);
            }
        }
        log.info("addSyxxByFcs_dateB4_" + DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        log.info("addSyxxByFcs_dateB5_i=" + i);
    }

//    /**
//     * 获取金四税务事项By征收项目
//     * @param zsxmDm 征收项目代码
//     * @param yzpzzlDm 应征凭证种类代码
//     * @return 返回值
//     */
//    public static String getGt4SwsxByZsxm(String zsxmDm, String yzpzzlDm) {
//        String swsxDm = "";
//        if (!FtsCxsUtils.isNull(zsxmDm)) {
//            switch (zsxmDm) {
//                case "10107":
//                    swsxDm = "SXN022007401";//资源税申报
//                    break;
//                case "10110":
//                    swsxDm = "SXN022006001";//
//                    break;
//                case "10112":
//                    swsxDm = "SXN022006001";//房产税、城镇土地使用税申报
//                    break;
//                case "10111":
//                    swsxDm = "SXN022006701";//印花税申报
//                    break;
//                case "10114":
//                    swsxDm = "SXN022007001";//车船税自行申报
//                    break;
//                case "10118":
//                    swsxDm = "SXN022007301";//耕地占用税申报
//                    break;
//                case "10119":
//                    swsxDm = "SXN022006101";//契税申报
//                    break;
//                case "10120":
//                    swsxDm = "SXN022007201";//烟叶税申报
//                    break;
//                case "10121":
//                    swsxDm = "SXN022007701";//环境保护税申报
//                    break;
//                case "10113":
//                    if ("BDA0610683".equals(yzpzzlDm)) {
//                        swsxDm = "SXN022006301";//土地增值税预征申报
//                    } else if ("BDA0610681".equals(yzpzzlDm) || "BDA0610784".equals(yzpzzlDm) || "BDA0610785".equals(yzpzzlDm)) {
//                        swsxDm = "SXN022006401";//土地增值税清算申报
//                    } else if ("BDA0610783".equals(yzpzzlDm)) {
//                        swsxDm = "SXN022006501";//土地增值税尾盘申报
//                    } else if ("BDA0610682".equals(yzpzzlDm) || "BDA0610786".equals(yzpzzlDm)) {
//                        swsxDm = "SXN022006601";//其他情况土地增值税申报
//                    } else {
//                        swsxDm = "SXN022006601";
//                    }
//                    break;
//                default:
//                    break;
//            }
//        }
//        return swsxDm;
//    }
//
    /**
     * 判断征收品目是否匹配
     * @param zspmDmDB 来源于参数表的征收品目
     * @param zspmDmCS 来源于传入的参数品目
     * @return true :匹配  false：不匹配
     */
    private static boolean judegeZspmDm(String zspmDmDB, String zspmDmCS){
        String zspmDmTemp = zspmDmCS;

        //如果传入的参数是空，返回true
        if(GyUtils.isNull(zspmDmCS) || GyUtils.isNull(zspmDmDB)){
            return true;
        }
        //数据库参数全是%%%号组成，能通配，返回true
        if(zspmDmDB.equals("%%%%%%%%%")){
            return true;
        }
        //第一次全匹配征收品目代码
        if (zspmDmDB.equals(zspmDmTemp)) {
            return true;
        }
        //第二次匹配前七位
        zspmDmTemp = zspmDmCS.substring(0, 7).concat("%%");
        if (zspmDmDB.equals(zspmDmTemp)) {
            return true;
        }
        //第三次匹配前五位
        zspmDmTemp = zspmDmCS.substring(0, 5).concat("%%%%");
        if (zspmDmDB.equals(zspmDmTemp)) {
            return true;
        }
        return false;
    }

    /**
     * 匹配纳税人类型
     * @param nsrlxDB 来自数据库的纳税人类型
     * @param nsrlxCS 来自入参的纳税人类型
     * @return true :匹配  false：不匹配
     */
    private static boolean judgeNsrlx(String nsrlxDB, String nsrlxCS) {
        if (GyUtils.isNull(nsrlxCS) || GyUtils.isNull(nsrlxDB)) {
            return true;
        }
        if ("2".equals(nsrlxDB) || nsrlxDB.equals(nsrlxCS)) {
            return true;
        }
        return false;
    }
//
//    /**
//     * 匹配行业代码
//     * @param hyDmDB 来自数据库的行业代码
//     * @param hyDmCS 来自入参的行业代码
//     * @return true :匹配  false：不匹配
//     */
//    private static boolean judgeHyDm(String hyDmDB, String hyDmCS) {
//        if (FtsCxsUtils.isNull(hyDmCS) || FtsCxsUtils.isNull(hyDmDB)) {
//            return true;
//        }
//        if (hyDmDB.equals("%%%%")) {
//            return true;
//        }
//        String hyDmTemp = hyDmCS;
//        //一次全匹配行业代码
//        if (hyDmDB.equals(hyDmTemp)) {
//            return true;
//        }
//        //二次匹配行业代码前两位
//        hyDmTemp = hyDmCS.substring(0, 2).concat("%%");
//        if (hyDmDB.equals(hyDmTemp)) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 获取征收项目代码By应征凭证种类代码
//     * @param yzpzzlDm 应征凭证种类代码
//     * @param sybh 税源编号
//     * @return 返回值
//     */
//    public static Map<String, Object> getZsxmDmByYzpzzlDm(String yzpzzlDm, String sybh) {
//        String zsxmDm = "";
//        String zspmDm = "";
//        if (!FtsCxsUtils.isNull(yzpzzlDm)) {
//            switch (yzpzzlDm) {
//                case "BDA0611116":
//                    if (FtsCxsUtils.isNull(sybh)) {
//                        zsxmDm = "10110";//随缘吧
//                    } else if (sybh.startsWith("F")) {
//                        zsxmDm = "10110";//房产税
//                    } else if (sybh.startsWith("T")){
//                        zsxmDm = "10112";//城镇土地使用税申报
//                    } else {
//                        zsxmDm = "10112";//随缘吧
//                    }
//                    break;
//                case "BDA0611147"://资源税申报
//                    zsxmDm = "10107";
//                    break;
//                case "BDA0610794":
//                    zsxmDm = "10111";//印花税申报
//                    break;
//                case "BDA0610088":
//                    zsxmDm = "10120";//烟叶税申报
//                    break;
//                case "BDA0610980":
//                case "BDA0610986":
//                    zsxmDm = "10121";//环境保护税申报
//                    break;
//                case "BDA0610750":
//                    zsxmDm = "10114";//车船税自行申报
//                    break;
//                case "BDA0610007":
//                    zsxmDm = "10118";//耕地占用税申报
//                    break;
//                case "BDA0610108":
//                    zsxmDm = "10119";//契税申报
//                    break;
//                case "BDA0610683":
//                    zsxmDm = "10113";//《土地增值税纳税申报表（一）（从事房地产开发的纳税人预征适用）》
//                    zspmDm = "101130501";//《土地增值税纳税申报表（一）（从事房地产开发的纳税人预征适用）》
//                    break;
//                case "BDA0610783":
//                    zsxmDm = "10113";//《土地增值税纳税申报表（四）（从事房地产开发的纳税人清算后尾盘销售适用）》
//                    zspmDm = "101130801";//《土地增值税纳税申报表（一）（从事房地产开发的纳税人预征适用）》
//                    break;
//                default:
//                    break;
//            }
//        }
//        final Map<String, Object> returnMap = new HashMap<>();
//        returnMap.put("zsxmDm", zsxmDm);
//        returnMap.put("zspmDm", zspmDm);
//        return returnMap;
//    }

    /**
     * 检验认定配置日期是否有效
     * @param nsqxDm 纳税期限
     * @param sbqxDm 申报期限
     * @param rdyxqq 认定有效期起
     * @param rdyxqz 认定有效期止
     * @param dqrq 当前日期
     * @param sbqxwbGroup 申报期限维护表group
     * @param jjrGroup 节假日group
     * @param zqtzGroup 征期调整group
     * @param swjgDmList 本上级税务机关
     * @param zsxmDm 征收项目代码
     */
    public static boolean checkRdpzRq(String nsqxDm, String sbqxDm, Date rdyxqq, Date rdyxqz, Date dqrq,
                                      Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup,
                                      Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup,
                                      Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup,
                                      List<String> swjgDmList, String zsxmDm) {
        if (GyUtils.isNull(nsqxDm) || GyUtils.isNull(sbqxDm) || GyUtils.isNull(rdyxqq) || GyUtils.isNull(rdyxqz) || GyUtils.isNull(dqrq)) {
            return false;
        }
        boolean sfyxFlag = true;
        //过滤掉过期认定
        if ("11".equals(nsqxDm)) {
            //按次认定根当前日期比较
            if(rdyxqq.compareTo(dqrq) > 0 || rdyxqz.compareTo(dqrq) < 0 ){
                sfyxFlag = false;
            }
        } else {
            //非按次认定先计算税款所属期，然后全包含比较 20231201先用止判断看看情况
            final Map<String, Object> skssqmap = CchxwsnssbGyUtils.jsSkssq(nsqxDm, dqrq, CchxwsnssbGyUtils.checkIsQnsb(sbqxDm));
            if (!GyUtils.isNull(skssqmap) && !GyUtils.isNull(skssqmap.get("skssqq")) && !GyUtils.isNull(skssqmap.get("skssqz"))) {
                final Date skssqqTemp = (Date) skssqmap.get("skssqq");
                final Date skssqzTemp = (Date) skssqmap.get("skssqz");
                if(rdyxqq.compareTo(skssqzTemp) > 0 || rdyxqz.compareTo(skssqzTemp) < 0 ){
                    sfyxFlag = false;
                } else {
                    //对于最后一期有效数据，需要额外判断是否过申报期限，即1.认定有效期止==税款所属期止(期后报);2.当前日期>认定有效期止;3.当前日期>税款所属期止
                    if (rdyxqz.compareTo(skssqzTemp) == 0 && dqrq.compareTo(rdyxqz) > 0 && dqrq.compareTo(skssqzTemp) > 0) {
                        final String sbqxStr = CchxwsnssbGyUtils.getSbqx(swjgDmList, sbqxDm, nsqxDm, zsxmDm,
                                DateUtil.parseDate(DateUtil.doDateFormat(skssqqTemp, "yyyy-MM-dd")), DateUtil.parseDate(DateUtil.doDateFormat(skssqzTemp, "yyyy-MM-dd")),
                                sbqxwbGroup, jjrGroup, zqtzGroup);
                        //当前日期>申报日期止
                        if (!GyUtils.isNull(sbqxStr) && dqrq.compareTo(DateUtil.toDate("yyyy-MM-dd",sbqxStr)) > 0) {
                            sfyxFlag = false;
                        }
                    }
                }
            } else {
                sfyxFlag = false;
            }
        }
        return sfyxFlag;
    }


    public static boolean pdsfZsbqx(Date skssqq, Date skssqz, String nsqxDm, String sbqxDm, String zsxmDm, String zgswjgdm) {
        final Date dqrq = getCurrentDateWithNoTime();
        final Map<String, Object> skssqmap = CchxwsnssbGyUtils.jsSkssq(nsqxDm, dqrq, CchxwsnssbGyUtils.checkIsQnsb(sbqxDm));
        final String sfzrdskssqq = cn.hutool.core.date.DateUtil.format((Date) skssqmap.get("skssqq"), "yyyy-MM-dd");
        final String sfzrdskssqz = cn.hutool.core.date.DateUtil.format((Date) skssqmap.get("skssqz"), "yyyy-MM-dd");
        final List<String> swjgDmList = new ArrayList<>();
        FtsCxsUtils.getSwjgList(swjgDmList, zgswjgdm, new HashMap<>());

        //获取全量码表数据，并根据税务机关分组
        final List<Map<String, Object>> sbqxwbAll = FtsCxsUtils.getAllCacheData("cs_gy_sbqxwh_sbqx");//申报期限维护表，700条左右
        final List<Map<String, Object>> jjrAll = FtsCxsUtils.getAllCacheData("cs_gy_jjr");//节假日配置表，4000条左右
        final List<Map<String, Object>> zqtzAll = FtsCxsUtils.getAllCacheData("cs_gy_zqtz");//征期调整配置表。50条
        final Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup = sbqxwbAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        final Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup = jjrAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        final Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup = zqtzAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf"))).collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));

        Calendar calendarMrskssqq = Calendar.getInstance();
        try {
            calendarMrskssqq.setTime(com.alibaba.excel.util.DateUtils.parseDate(sfzrdskssqq, "yyyy-MM-dd"));
        } catch (ParseException e) {
            throw new ServiceException(9999, "日期转化失败");
        }
        Calendar calendarMrskssqz = Calendar.getInstance();
        try {
            calendarMrskssqz.setTime(com.alibaba.excel.util.DateUtils.parseDate(sfzrdskssqz, "yyyy-MM-dd"));
        } catch (ParseException e) {
            throw new ServiceException(9999, "日期转化失败");
        }
        final String sbrqqStr = CchxwsnssbGyUtils.getSbqxrqq(sbqxDm, nsqxDm, zsxmDm,
                calendarMrskssqq, calendarMrskssqz);
        final String sbqxStr = CchxwsnssbGyUtils.getSbqx(swjgDmList, sbqxDm, nsqxDm, zsxmDm,
                calendarMrskssqq, calendarMrskssqz,
                sbqxwbGroup, jjrGroup, zqtzGroup);
        final Date sbrqqDate = com.css.znsb.nssb.util.DateUtil.toDate("yyyy-MM-dd", sbrqqStr);
        final Date sbqxDate = com.css.znsb.nssb.util.DateUtil.toDate("yyyy-MM-dd", sbqxStr);
        if (!sbqxDate.before(dqrq) && !sbrqqDate.after(dqrq)) {
            return true;
        }
        return false;
    }

    public static Date getCurrentDateWithNoTime() {
        // 获取当前日期和时间
        Calendar calendar = Calendar.getInstance();

        // 获取当前时间
        calendar.setTime(new Date());

        // 设置时、分、秒和毫秒为 0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 返回没有时分秒的当前日期
        return calendar.getTime();
    }

    private static LslfDTO getPhjmzgnew(String skssqq, String skssqz, NsrzgxxVO nsrzgxxVO, List<NsrbqxxVO> nsrbqxxVOList) {
        final LslfDTO lslfDTO = new LslfDTO();
        if (GyUtils.isNull(nsrzgxxVO)) {
            return lslfDTO;
        }
        Date yxqq = nsrzgxxVO.getYxqq();
        Date yxqz = nsrzgxxVO.getYxqz();
        Date dateSkssqq = cn.hutool.core.date.DateUtil.parse(skssqq, "yyyy-MM-dd");
        Date dateSkssqz = cn.hutool.core.date.DateUtil.parse(skssqz, "yyyy-MM-dd");
        String jzzcsyzt = "";
        /*
          1：一般纳税人
          2：小规模纳税人
          3：辅导期一般纳税人
          4：逾期未认定一般纳税人
          5：其它
          **/
        if ("1".equals(nsrzgxxVO.getNsrlx())) {
            for (NsrbqxxVO nsrbqxxVO : nsrbqxxVOList) {
                if (GyUtils.isNotNull(nsrbqxxVO) && GyUtils.isNotNull(nsrbqxxVO.getBqmc()) && nsrbqxxVO.getBqmc().equals("xwqy")) {
                    yxqq = nsrbqxxVO.getYxqq();
                    yxqz = nsrbqxxVO.getYxqz();
                    jzzcsyzt = "21";
                    break;
                } else if (GyUtils.isNotNull(nsrbqxxVO) && GyUtils.isNotNull(nsrbqxxVO.getBqmc()) && nsrbqxxVO.getBqmc().equals("gtgsh")) {
                    yxqq = nsrbqxxVO.getYxqq();
                    yxqz = nsrbqxxVO.getYxqz();
                    jzzcsyzt = "22";
                }
            }
        } else if ("2".equals(nsrzgxxVO.getNsrlx())) {
            jzzcsyzt = "11";
        }
        if (GyUtils.isNull(jzzcsyzt)) {
            lslfDTO.setBqsfsyzzsxgmnsrjzzc("N");
            return lslfDTO;
        }
        if (yxqq.compareTo(dateSkssqq) >= 0 && yxqz.compareTo(dateSkssqz) <= 0) {
            /*  skssqq______yxqq__yxqz______skssqz  **/
            lslfDTO.setXgmjzzcqssj(cn.hutool.core.date.DateUtil.format(yxqq, "yyyy-MM-dd"));
            lslfDTO.setXgmjzzczzsj(cn.hutool.core.date.DateUtil.format(yxqz, "yyyy-MM-dd"));
            lslfDTO.setJzzcsyztDm(jzzcsyzt);
            lslfDTO.setBqsfsyzzsxgmnsrjzzc("Y");
        } else if (yxqq.compareTo(dateSkssqq) < 0 && yxqz.compareTo(dateSkssqz) < 0) {
            /*  yxqq______skssqq__yxqz______skssqz  **/
            lslfDTO.setXgmjzzcqssj(cn.hutool.core.date.DateUtil.format(dateSkssqq, "yyyy-MM-dd"));
            lslfDTO.setXgmjzzczzsj(cn.hutool.core.date.DateUtil.format(yxqz, "yyyy-MM-dd"));
            lslfDTO.setJzzcsyztDm(jzzcsyzt);
            lslfDTO.setBqsfsyzzsxgmnsrjzzc("Y");
        } else if (yxqq.compareTo(dateSkssqq) < 0 && yxqz.compareTo(dateSkssqz) >= 0) {
            /*  yxqq______skssqq__skssqz______yxqz  **/
            lslfDTO.setXgmjzzcqssj(cn.hutool.core.date.DateUtil.format(dateSkssqq, "yyyy-MM-dd"));
            lslfDTO.setXgmjzzczzsj(cn.hutool.core.date.DateUtil.format(dateSkssqz, "yyyy-MM-dd"));
            lslfDTO.setJzzcsyztDm(jzzcsyzt);
            lslfDTO.setBqsfsyzzsxgmnsrjzzc("Y");
        } else if (yxqq.compareTo(dateSkssqq) > 0 && yxqz.compareTo(dateSkssqz) >= 0 && yxqq.compareTo(dateSkssqz) <= 0) {
            /*  skssqq______yxqq__skssqz______yxqz  **/
            lslfDTO.setXgmjzzcqssj(cn.hutool.core.date.DateUtil.format(yxqq, "yyyy-MM-dd"));
            lslfDTO.setXgmjzzczzsj(cn.hutool.core.date.DateUtil.format(dateSkssqz, "yyyy-MM-dd"));
            lslfDTO.setJzzcsyztDm(jzzcsyzt);
            lslfDTO.setBqsfsyzzsxgmnsrjzzc("Y");
        } else {
            lslfDTO.setBqsfsyzzsxgmnsrjzzc("N");
        }
        return lslfDTO;
    }

    public static void getPhjmzgnew(CchxwsnssbInitResMxVO initResMxVO, String skssqq, String skssqz, NsrzgxxVO nsrzgxxVO, List<NsrbqxxVO> nsrbqxxVOList) {
        if (GyUtils.isNull(nsrzgxxVO)) {
            return;
        }
        Date yxqq = nsrzgxxVO.getYxqq();
        Date yxqz = nsrzgxxVO.getYxqz();
        Date dateSkssqq = cn.hutool.core.date.DateUtil.parse(skssqq, "yyyy-MM-dd");
        Date dateSkssqz = cn.hutool.core.date.DateUtil.parse(skssqz, "yyyy-MM-dd");
        String jzzcsyzt = "";
        /*
          1：一般纳税人
          2：小规模纳税人
          3：辅导期一般纳税人
          4：逾期未认定一般纳税人
          5：其它
          **/
        if ("1".equals(nsrzgxxVO.getNsrlx())) {
            for (NsrbqxxVO nsrbqxxVO : nsrbqxxVOList) {
                if (GyUtils.isNotNull(nsrbqxxVO) && GyUtils.isNotNull(nsrbqxxVO.getBqmc()) && nsrbqxxVO.getBqmc().equals("xwqy")) {
                    yxqq = nsrbqxxVO.getYxqq();
                    yxqz = nsrbqxxVO.getYxqz();
                    jzzcsyzt = "21";
                    break;
                } else if (GyUtils.isNotNull(nsrbqxxVO) && GyUtils.isNotNull(nsrbqxxVO.getBqmc()) && nsrbqxxVO.getBqmc().equals("gtgsh")) {
                    yxqq = nsrbqxxVO.getYxqq();
                    yxqz = nsrbqxxVO.getYxqz();
                    jzzcsyzt = "22";
                }
            }
        } else if ("2".equals(nsrzgxxVO.getNsrlx())) {
            jzzcsyzt = "11";
        }
        if (GyUtils.isNull(jzzcsyzt)) {
            initResMxVO.setBqsfsyzzsxgmnsrjzzc("N");
            initResMxVO.setCheckbz("N");
            return;
        }

        //判断税款所属期止是否在有效期前
        if (dateSkssqq.before(yxqq) && !dateSkssqq.equals(yxqq)){
            initResMxVO.setBqsfsyzzsxgmnsrjzzc("N");
            initResMxVO.setCheckbz("N");
            return;
        }

        Date xgmjzzcqssj = getLatestDate(dateSkssqq,yxqq);
        Date xgmjzzczssj = getLatestDate(dateSkssqz,yxqz);
        initResMxVO.setXgmjzzcqssj(cn.hutool.core.date.DateUtil.format(xgmjzzcqssj, "yyyy-MM-dd"));
        initResMxVO.setXgmjzzczzsj(cn.hutool.core.date.DateUtil.format(xgmjzzczssj, "yyyy-MM-dd"));
        initResMxVO.setJzzcsyztDm(jzzcsyzt);
        initResMxVO.setBqsfsyzzsxgmnsrjzzc("Y");
        initResMxVO.setCheckbz("Y");


    }

    // 获取两个日期中的较小值（即较早的日期）
    private static Date getEarliestDate(Date date1, Date date2) {
        return date1.before(date2) ? date1 : date2;
    }

    // 获取两个日期中的较大值（即较晚的日期）
    private static Date getLatestDate(Date date1, Date date2) {
        return date1.after(date2) ? date1 : date2;
    }

    public static String getJzzcsyzt(NsrzgxxVO nsrzgxxVO, List<NsrbqxxVO> nsrbqxxVOList) {
        String jzzcsyzt = "";

        //查询课征主体类型
        if (GyUtils.isNull(nsrzgxxVO) || GyUtils.isNull(nsrbqxxVOList)){
            return jzzcsyzt;
        }
        /*
          1：一般纳税人
          2：小规模纳税人
          3：辅导期一般纳税人
          4：逾期未认定一般纳税人
          5：其它
          **/
        if ("1".equals(nsrzgxxVO.getNsrlx())) {
            for (NsrbqxxVO nsrbqxxVO : nsrbqxxVOList) {
                if (GyUtils.isNotNull(nsrbqxxVO) && GyUtils.isNotNull(nsrbqxxVO.getBqmc()) && nsrbqxxVO.getBqmc().equals("xwqy")) {
                    jzzcsyzt = "21";
                    break;
                } else if (GyUtils.isNotNull(nsrbqxxVO) && GyUtils.isNotNull(nsrbqxxVO.getBqmc()) && nsrbqxxVO.getBqmc().equals("gtgsh")) {
                    jzzcsyzt = "22";
                }
            }
        } else if ("2".equals(nsrzgxxVO.getNsrlx())) {
            jzzcsyzt = "11";
        }
        return jzzcsyzt;
    }

    /**
     * 获取上一个季度的起止日期（Date 类型）
     *
     * @return 返回包含上个季度开始日期和结束日期的数组 [startDate, endDate]
     */
    public static Date[] getPreviousQuarterDatesAsDate() {
        LocalDate currentDate = LocalDate.now();
        YearMonth currentYearMonth = YearMonth.from(currentDate);

        int currentMonthValue = currentYearMonth.getMonthValue();

        // 计算当前属于哪个季度
        int currentQuarter = (currentMonthValue - 1) / 3 + 1;

        // 上个季度
        int previousQuarter = currentQuarter == 1 ? 4 : currentQuarter - 1;

        // 根据上个季度计算起始月份
        int startMonthValue;
        switch (previousQuarter) {
            case 1:
                startMonthValue = Month.JANUARY.getValue();
                break;
            case 2:
                startMonthValue = Month.APRIL.getValue();
                break;
            case 3:
                startMonthValue = Month.JULY.getValue();
                break;
            case 4:
                startMonthValue = Month.OCTOBER.getValue();
                break;
            default:
                throw new IllegalArgumentException("Invalid quarter");
        }

        YearMonth startOfQuarter = YearMonth.of(currentYearMonth.getYear(), startMonthValue);
        LocalDate startDate = startOfQuarter.atDay(1); // 季度第一天
        LocalDate endDate = startDate.plusMonths(2).withDayOfMonth(startDate.plusMonths(2).lengthOfMonth()); // 季度最后一天

        return new Date[]{Date.from(startDate.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant()),
                Date.from(endDate.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant())};
    }

    public static boolean isStartOfQuarter(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int month = cal.get(Calendar.MONTH); // 0-based
        return month % 3 == 0;
    }


}
