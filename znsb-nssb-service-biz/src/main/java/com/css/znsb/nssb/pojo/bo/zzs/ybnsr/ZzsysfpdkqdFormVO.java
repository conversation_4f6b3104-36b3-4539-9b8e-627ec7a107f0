package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import com.alibaba.fastjson.annotation.JSONField;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

/**
 * 增值税（适用于增值税一般纳税人）-纳税人信息
 *
 * <p>zzsysfpdkqdFormVO complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="zzsysfpdkqdFormVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="yfje_tl" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yxjsdkyfje_tl" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="jsdkdjxse_tl" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yfje_hk" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yxjsdkyfje_hk" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="jsdkdjxse_hk" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yfje_gd" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yxjsdkyfje_gd" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="jsdkdjxse_gd" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yfje_hy" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="yxjsdkyfje_hy" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="jsdkdjxse_hy" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsysfpdkqdFormVO", propOrder = { "yfjeTl", "yxjsdkyfjeTl", "jsdkdjxseTl", "yfjeHk", "yxjsdkyfjeHk", "jsdkdjxseHk", "yfjeGd", "yxjsdkyfjeGd", "jsdkdjxseGd", "yfjeHy", "yxjsdkyfjeHy", "jsdkdjxseHy" })
public class ZzsysfpdkqdFormVO {
    /**
     * 铁路运输运费金额
     */
    @XmlElement(name = "yfje_tl")
    @JSONField(name = "yfje_tl")
    protected BigDecimal yfjeTl;

    /**
     * 铁路运输允许计算抵扣运费金额
     */
    @XmlElement(name = "yxjsdkyfje_tl")
    @JSONField(name = "yxjsdkyfje_tl")
    protected BigDecimal yxjsdkyfjeTl;

    /**
     * 铁路运输计算抵扣的进项税额
     */
    @XmlElement(name = "jsdkdjxse_tl")
    @JSONField(name = "jsdkdjxse_tl")
    protected BigDecimal jsdkdjxseTl;

    /**
     * 航空运输运费金额
     */
    @XmlElement(name = "yfje_hk")
    @JSONField(name = "yfje_hk")
    protected BigDecimal yfjeHk;

    /**
     * 航空运输允许计算抵扣运费金额
     */
    @XmlElement(name = "yxjsdkyfje_hk")
    @JSONField(name = "yxjsdkyfje_hk")
    protected BigDecimal yxjsdkyfjeHk;

    /**
     * 航空运输计算抵扣的进项税额
     */
    @XmlElement(name = "jsdkdjxse_hk")
    @JSONField(name = "jsdkdjxse_hk")
    protected BigDecimal jsdkdjxseHk;

    /**
     * 管道运输运费金额
     */
    @XmlElement(name = "yfje_gd")
    @JSONField(name = "yfje_gd")
    protected BigDecimal yfjeGd;

    /**
     * 管道运输允许计算抵扣运费金额
     */
    @XmlElement(name = "yxjsdkyfje_gd")
    @JSONField(name = "yxjsdkyfje_gd")
    protected BigDecimal yxjsdkyfjeGd;

    /**
     * 管道运输计算抵扣的进项税额
     */
    @XmlElement(name = "jsdkdjxse_gd")
    @JSONField(name = "jsdkdjxse_gd")
    protected BigDecimal jsdkdjxseGd;

    /**
     * 海洋运输运费金额
     */
    @XmlElement(name = "yfje_hy")
    @JSONField(name = "yfje_hy")
    protected BigDecimal yfjeHy;

    /**
     * 海洋运输允许计算抵扣运费金额
     */
    @XmlElement(name = "yxjsdkyfje_hy")
    @JSONField(name = "yxjsdkyfje_hy")
    protected BigDecimal yxjsdkyfjeHy;

    /**
     * 海洋运输计算抵扣的进项税额
     */
    @XmlElement(name = "jsdkdjxse_hy")
    @JSONField(name = "jsdkdjxse_hy")
    protected BigDecimal jsdkdjxseHy;

    /**
     * 获取yfjeTl属性的值。
     * <p>
     * 铁路运输运费金额
     */
    public BigDecimal getYfjeTl() {
        return yfjeTl;
    }

    /**
     * 设置yfjeTl属性的值。
     */
    public void setYfjeTl(BigDecimal value) {
        this.yfjeTl = value;
    }

    /**
     * 获取yxjsdkyfjeTl属性的值。
     * <p>
     * 铁路运输允许计算抵扣运费金额
     */
    public BigDecimal getYxjsdkyfjeTl() {
        return yxjsdkyfjeTl;
    }

    /**
     * 设置yxjsdkyfjeTl属性的值。
     */
    public void setYxjsdkyfjeTl(BigDecimal value) {
        this.yxjsdkyfjeTl = value;
    }

    /**
     * 获取jsdkdjxseTl属性的值。
     * <p>
     * 铁路运输计算抵扣的进项税额
     */
    public BigDecimal getJsdkdjxseTl() {
        return jsdkdjxseTl;
    }

    /**
     * 设置jsdkdjxseTl属性的值。
     */
    public void setJsdkdjxseTl(BigDecimal value) {
        this.jsdkdjxseTl = value;
    }

    /**
     * 获取yfjeHk属性的值。
     * <p>
     * 航空运输运费金额
     */
    public BigDecimal getYfjeHk() {
        return yfjeHk;
    }

    /**
     * 设置yfjeHk属性的值。
     */
    public void setYfjeHk(BigDecimal value) {
        this.yfjeHk = value;
    }

    /**
     * 获取yxjsdkyfjeHk属性的值。
     * <p>
     * 航空运输允许计算抵扣运费金额
     */
    public BigDecimal getYxjsdkyfjeHk() {
        return yxjsdkyfjeHk;
    }

    /**
     * 设置yxjsdkyfjeHk属性的值。
     */
    public void setYxjsdkyfjeHk(BigDecimal value) {
        this.yxjsdkyfjeHk = value;
    }

    /**
     * 获取jsdkdjxseHk属性的值。
     * <p>
     * 航空运输计算抵扣的进项税额
     */
    public BigDecimal getJsdkdjxseHk() {
        return jsdkdjxseHk;
    }

    /**
     * 设置jsdkdjxseHk属性的值。
     */
    public void setJsdkdjxseHk(BigDecimal value) {
        this.jsdkdjxseHk = value;
    }

    /**
     * 获取yfjeGd属性的值。
     * <p>
     * 管道运输运费金额
     */
    public BigDecimal getYfjeGd() {
        return yfjeGd;
    }

    /**
     * 设置yfjeGd属性的值。
     */
    public void setYfjeGd(BigDecimal value) {
        this.yfjeGd = value;
    }

    /**
     * 获取yxjsdkyfjeGd属性的值。
     * <p>
     * 管道运输允许计算抵扣运费金额
     */
    public BigDecimal getYxjsdkyfjeGd() {
        return yxjsdkyfjeGd;
    }

    /**
     * 设置yxjsdkyfjeGd属性的值。
     */
    public void setYxjsdkyfjeGd(BigDecimal value) {
        this.yxjsdkyfjeGd = value;
    }

    /**
     * 获取jsdkdjxseGd属性的值。
     * <p>
     * 管道运输计算抵扣的进项税额
     */
    public BigDecimal getJsdkdjxseGd() {
        return jsdkdjxseGd;
    }

    /**
     * 设置jsdkdjxseGd属性的值。
     */
    public void setJsdkdjxseGd(BigDecimal value) {
        this.jsdkdjxseGd = value;
    }

    /**
     * 获取yfjeHy属性的值。
     * <p>
     * 海洋运输运费金额
     */
    public BigDecimal getYfjeHy() {
        return yfjeHy;
    }

    /**
     * 设置yfjeHy属性的值。
     */
    public void setYfjeHy(BigDecimal value) {
        this.yfjeHy = value;
    }

    /**
     * 获取yxjsdkyfjeHy属性的值。
     * <p>
     * 海洋运输允许计算抵扣运费金额
     */
    public BigDecimal getYxjsdkyfjeHy() {
        return yxjsdkyfjeHy;
    }

    /**
     * 设置yxjsdkyfjeHy属性的值。
     */
    public void setYxjsdkyfjeHy(BigDecimal value) {
        this.yxjsdkyfjeHy = value;
    }

    /**
     * 获取jsdkdjxseHy属性的值。
     * <p>
     * 海洋运输计算抵扣的进项税额
     */
    public BigDecimal getJsdkdjxseHy() {
        return jsdkdjxseHy;
    }

    /**
     * 设置jsdkdjxseHy属性的值。
     */
    public void setJsdkdjxseHy(BigDecimal value) {
        this.jsdkdjxseHy = value;
    }
}