
package com.css.znsb.nssb.pojo.vo.hxzg.sb01730;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 土地税源信息采集表VO
 * 
 * <p>SBCxsTdsyxxcjbVO complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SBCxsTdsyxxcjbVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="djzclxDm" type="{http://www.chinatax.gov.cn/dataspec/}djzclxDm"/>
 *         &lt;element name="zytdmj1" type="{http://www.chinatax.gov.cn/dataspec/}zytdmj1"/>
 *         &lt;element name="hyDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm"/>
 *         &lt;element name="tdytDm" type="{http://www.chinatax.gov.cn/dataspec/}tdytDm"/>
 *         &lt;element name="tdqdfsDm" type="{http://www.chinatax.gov.cn/dataspec/}tdqdfsDm"/>
 *         &lt;element name="czrsj" type="{http://www.chinatax.gov.cn/dataspec/}czrsj"/>
 *         &lt;element name="sjtbSj" type="{http://www.chinatax.gov.cn/dataspec/}sjtbSj"/>
 *         &lt;element name="tbrq" type="{http://www.chinatax.gov.cn/dataspec/}tbrq"/>
 *         &lt;element name="dlrsfzjhm1" type="{http://www.chinatax.gov.cn/dataspec/}dlrsfzjhm1"/>
 *         &lt;element name="lsgx" type="{http://www.chinatax.gov.cn/dataspec/}lsgx"/>
 *         &lt;element name="qzqdtdsyqzfje" type="{http://www.chinatax.gov.cn/dataspec/}qzqdtdsyqzfje"/>
 *         &lt;element name="jdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}jdxzDm"/>
 *         &lt;element name="nsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="zxrq" type="{http://www.chinatax.gov.cn/dataspec/}zxrq"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz"/>
 *         &lt;element name="slswjg" type="{http://www.chinatax.gov.cn/dataspec/}slswjg"/>
 *         &lt;element name="nsywzzsj" type="{http://www.chinatax.gov.cn/dataspec/}nsywzzsj"/>
 *         &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm"/>
 *         &lt;element name="tdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}tdxzDm"/>
 *         &lt;element name="tdsybh" type="{http://www.chinatax.gov.cn/dataspec/}tdsybh"/>
 *         &lt;element name="qztdkfcb" type="{http://www.chinatax.gov.cn/dataspec/}qztdkfcb"/>
 *         &lt;element name="tdsyqrnsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}tdsyqrnsrsbh"/>
 *         &lt;element name="dh1" type="{http://www.chinatax.gov.cn/dataspec/}dh1"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="syuuid" type="{http://www.chinatax.gov.cn/dataspec/}syuuid"/>
 *         &lt;element name="tdsyqrdjxh" type="{http://www.chinatax.gov.cn/dataspec/}tdsyqrdjxh"/>
 *         &lt;element name="tdsyzbh" type="{http://www.chinatax.gov.cn/dataspec/}tdsyzbh"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="slrq" type="{http://www.chinatax.gov.cn/dataspec/}slrq"/>
 *         &lt;element name="slr" type="{http://www.chinatax.gov.cn/dataspec/}slr"/>
 *         &lt;element name="yxqq" type="{http://www.chinatax.gov.cn/dataspec/}yxqq"/>
 *         &lt;element name="csqdsj" type="{http://www.chinatax.gov.cn/dataspec/}csqdsj"/>
 *         &lt;element name="tddj" type="{http://www.chinatax.gov.cn/dataspec/}tddj"/>
 *         &lt;element name="nsrlx" type="{http://www.chinatax.gov.cn/dataspec/}nsrlx"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm"/>
 *         &lt;element name="yxqz" type="{http://www.chinatax.gov.cn/dataspec/}yxqz"/>
 *         &lt;element name="tdzldz" type="{http://www.chinatax.gov.cn/dataspec/}tdzldz"/>
 *         &lt;element name="zgswskfjDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswskfjDm"/>
 *         &lt;element name="tdsyqrmc" type="{http://www.chinatax.gov.cn/dataspec/}tdsyqrmc"/>
 *         &lt;element name="tdmc1" type="{http://www.chinatax.gov.cn/dataspec/}tdmc1"/>
 *         &lt;element name="dlrsfzjzlDm1" type="{http://www.chinatax.gov.cn/dataspec/}dlrsfzjzlDm1" minOccurs="0"/>
 *         &lt;element name="dlr" type="{http://www.chinatax.gov.cn/dataspec/}dlr" minOccurs="0"/>
 *         &lt;element name="tbr" type="{http://www.chinatax.gov.cn/dataspec/}tbr"/>
 *         &lt;element name="bdcdyh" type="{http://www.chinatax.gov.cn/dataspec/}bdcdyh" minOccurs="0"/>
 *         &lt;element name="dljgqz" type="{http://www.chinatax.gov.cn/dataspec/}dljgqz" minOccurs="0"/>
 *         &lt;element name="jbrsfzjzlDm" type="{http://www.chinatax.gov.cn/dataspec/}jbrsfzjzlDm" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBCxsTdsyxxcjbVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "djzclxDm",
    "zytdmj1",
    "hyDm",
    "tdytDm",
    "tdqdfsDm",
    "czrsj",
    "sjtbSj",
    "tbrq",
    "dlrsfzjhm1",
    "lsgx",
    "qzqdtdsyqzfje",
    "jdxzDm",
    "nsrsbh",
    "zxrq",
    "yxbz",
    "slswjg",
    "nsywzzsj",
    "xzqhszDm",
    "tdxzDm",
    "tdsybh",
    "qztdkfcb",
    "tdsyqrnsrsbh",
    "dh1",
    "djxh",
    "syuuid",
    "tdsyqrdjxh",
    "tdsyzbh",
    "lrrDm",
    "slrq",
    "slr",
    "yxqq",
    "csqdsj",
    "tddj",
    "nsrlx",
    "sjgsdq",
    "xgrDm",
    "yxqz",
    "tdzldz",
    "zgswskfjDm",
    "tdsyqrmc",
    "tdmc1",
    "dlrsfzjzlDm1",
    "dlr",
    "tbr",
    "bdcdyh",
    "dljgqz",
    "jbrsfzjzlDm"
})
public class SBCxsTdsyxxcjbVO
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String djzclxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double zytdmj1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String hyDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String tdytDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String tdqdfsDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String czrsj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String sjtbSj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String tbrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String dlrsfzjhm1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String lsgx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double qzqdtdsyqzfje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String jdxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String nsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String zxrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slswjg;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String nsywzzsj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xzqhszDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String tdxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String tdsybh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double qztdkfcb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String tdsyqrnsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String dh1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String syuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String tdsyqrdjxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String tdsyzbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yxqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String csqdsj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, type = Double.class, nillable = true)
    protected Double tddj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String nsrlx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yxqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String tdzldz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zgswskfjDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String tdsyqrmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String tdmc1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dlrsfzjzlDm1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dlr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String tbr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bdcdyh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dljgqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jbrsfzjzlDm;

    /**
     * 获取djzclxDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjzclxDm() {
        return djzclxDm;
    }

    /**
     * 设置djzclxDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjzclxDm(String value) {
        this.djzclxDm = value;
    }

    /**
     * 获取zytdmj1属性的值。
     * 
     */
    public double getZytdmj1() {
        return zytdmj1;
    }

    /**
     * 设置zytdmj1属性的值。
     * 
     */
    public void setZytdmj1(double value) {
        this.zytdmj1 = value;
    }

    /**
     * 获取hyDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHyDm() {
        return hyDm;
    }

    /**
     * 设置hyDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHyDm(String value) {
        this.hyDm = value;
    }

    /**
     * 获取tdytDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdytDm() {
        return tdytDm;
    }

    /**
     * 设置tdytDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdytDm(String value) {
        this.tdytDm = value;
    }

    /**
     * 获取tdqdfsDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdqdfsDm() {
        return tdqdfsDm;
    }

    /**
     * 设置tdqdfsDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdqdfsDm(String value) {
        this.tdqdfsDm = value;
    }

    /**
     * 获取czrsj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzrsj() {
        return czrsj;
    }

    /**
     * 设置czrsj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzrsj(String value) {
        this.czrsj = value;
    }

    /**
     * 获取sjtbSj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjtbSj() {
        return sjtbSj;
    }

    /**
     * 设置sjtbSj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjtbSj(String value) {
        this.sjtbSj = value;
    }

    /**
     * 获取tbrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTbrq() {
        return tbrq;
    }

    /**
     * 设置tbrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTbrq(String value) {
        this.tbrq = value;
    }

    /**
     * 获取dlrsfzjhm1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDlrsfzjhm1() {
        return dlrsfzjhm1;
    }

    /**
     * 设置dlrsfzjhm1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDlrsfzjhm1(String value) {
        this.dlrsfzjhm1 = value;
    }

    /**
     * 获取lsgx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLsgx() {
        return lsgx;
    }

    /**
     * 设置lsgx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLsgx(String value) {
        this.lsgx = value;
    }

    /**
     * 获取qzqdtdsyqzfje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQzqdtdsyqzfje() {
        return qzqdtdsyqzfje;
    }

    /**
     * 设置qzqdtdsyqzfje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQzqdtdsyqzfje(Double value) {
        this.qzqdtdsyqzfje = value;
    }

    /**
     * 获取jdxzDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJdxzDm() {
        return jdxzDm;
    }

    /**
     * 设置jdxzDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJdxzDm(String value) {
        this.jdxzDm = value;
    }

    /**
     * 获取nsrsbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrsbh() {
        return nsrsbh;
    }

    /**
     * 设置nsrsbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrsbh(String value) {
        this.nsrsbh = value;
    }

    /**
     * 获取zxrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZxrq() {
        return zxrq;
    }

    /**
     * 设置zxrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZxrq(String value) {
        this.zxrq = value;
    }

    /**
     * 获取yxbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxbz() {
        return yxbz;
    }

    /**
     * 设置yxbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxbz(String value) {
        this.yxbz = value;
    }

    /**
     * 获取slswjg属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlswjg() {
        return slswjg;
    }

    /**
     * 设置slswjg属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlswjg(String value) {
        this.slswjg = value;
    }

    /**
     * 获取nsywzzsj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsywzzsj() {
        return nsywzzsj;
    }

    /**
     * 设置nsywzzsj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsywzzsj(String value) {
        this.nsywzzsj = value;
    }

    /**
     * 获取xzqhszDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }

    /**
     * 设置xzqhszDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhszDm(String value) {
        this.xzqhszDm = value;
    }

    /**
     * 获取tdxzDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdxzDm() {
        return tdxzDm;
    }

    /**
     * 设置tdxzDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdxzDm(String value) {
        this.tdxzDm = value;
    }

    /**
     * 获取tdsybh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdsybh() {
        return tdsybh;
    }

    /**
     * 设置tdsybh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdsybh(String value) {
        this.tdsybh = value;
    }

    /**
     * 获取qztdkfcb属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQztdkfcb() {
        return qztdkfcb;
    }

    /**
     * 设置qztdkfcb属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQztdkfcb(Double value) {
        this.qztdkfcb = value;
    }

    /**
     * 获取tdsyqrnsrsbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdsyqrnsrsbh() {
        return tdsyqrnsrsbh;
    }

    /**
     * 设置tdsyqrnsrsbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdsyqrnsrsbh(String value) {
        this.tdsyqrnsrsbh = value;
    }

    /**
     * 获取dh1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDh1() {
        return dh1;
    }

    /**
     * 设置dh1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDh1(String value) {
        this.dh1 = value;
    }

    /**
     * 获取djxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * 设置djxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * 获取syuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSyuuid() {
        return syuuid;
    }

    /**
     * 设置syuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSyuuid(String value) {
        this.syuuid = value;
    }

    /**
     * 获取tdsyqrdjxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdsyqrdjxh() {
        return tdsyqrdjxh;
    }

    /**
     * 设置tdsyqrdjxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdsyqrdjxh(String value) {
        this.tdsyqrdjxh = value;
    }

    /**
     * 获取tdsyzbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdsyzbh() {
        return tdsyzbh;
    }

    /**
     * 设置tdsyzbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdsyzbh(String value) {
        this.tdsyzbh = value;
    }

    /**
     * 获取lrrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * 设置lrrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * 获取slrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlrq() {
        return slrq;
    }

    /**
     * 设置slrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlrq(String value) {
        this.slrq = value;
    }

    /**
     * 获取slr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlr() {
        return slr;
    }

    /**
     * 设置slr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlr(String value) {
        this.slr = value;
    }

    /**
     * 获取yxqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqq() {
        return yxqq;
    }

    /**
     * 设置yxqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqq(String value) {
        this.yxqq = value;
    }

    /**
     * 获取csqdsj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCsqdsj() {
        return csqdsj;
    }

    /**
     * 设置csqdsj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCsqdsj(String value) {
        this.csqdsj = value;
    }

    /**
     * 获取tddj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getTddj() {
        return tddj;
    }

    /**
     * 设置tddj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setTddj(Double value) {
        this.tddj = value;
    }

    /**
     * 获取nsrlx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrlx() {
        return nsrlx;
    }

    /**
     * 设置nsrlx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrlx(String value) {
        this.nsrlx = value;
    }

    /**
     * 获取sjgsdq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * 设置sjgsdq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * 获取xgrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * 设置xgrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * 获取yxqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqz() {
        return yxqz;
    }

    /**
     * 设置yxqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqz(String value) {
        this.yxqz = value;
    }

    /**
     * 获取tdzldz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdzldz() {
        return tdzldz;
    }

    /**
     * 设置tdzldz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdzldz(String value) {
        this.tdzldz = value;
    }

    /**
     * 获取zgswskfjDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    /**
     * 设置zgswskfjDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswskfjDm(String value) {
        this.zgswskfjDm = value;
    }

    /**
     * 获取tdsyqrmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdsyqrmc() {
        return tdsyqrmc;
    }

    /**
     * 设置tdsyqrmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdsyqrmc(String value) {
        this.tdsyqrmc = value;
    }

    /**
     * 获取tdmc1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTdmc1() {
        return tdmc1;
    }

    /**
     * 设置tdmc1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTdmc1(String value) {
        this.tdmc1 = value;
    }

    /**
     * 获取dlrsfzjzlDm1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDlrsfzjzlDm1() {
        return dlrsfzjzlDm1;
    }

    /**
     * 设置dlrsfzjzlDm1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDlrsfzjzlDm1(String value) {
        this.dlrsfzjzlDm1 = value;
    }

    /**
     * 获取dlr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDlr() {
        return dlr;
    }

    /**
     * 设置dlr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDlr(String value) {
        this.dlr = value;
    }

    /**
     * 获取tbr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTbr() {
        return tbr;
    }

    /**
     * 设置tbr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTbr(String value) {
        this.tbr = value;
    }

    /**
     * 获取bdcdyh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBdcdyh() {
        return bdcdyh;
    }

    /**
     * 设置bdcdyh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBdcdyh(String value) {
        this.bdcdyh = value;
    }

    /**
     * 获取dljgqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDljgqz() {
        return dljgqz;
    }

    /**
     * 设置dljgqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDljgqz(String value) {
        this.dljgqz = value;
    }

    /**
     * 获取jbrsfzjzlDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJbrsfzjzlDm() {
        return jbrsfzjzlDm;
    }

    /**
     * 设置jbrsfzjzlDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJbrsfzjzlDm(String value) {
        this.jbrsfzjzlDm = value;
    }

}
