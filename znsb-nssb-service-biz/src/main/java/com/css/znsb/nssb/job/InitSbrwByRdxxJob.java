package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class InitSbrwByRdxxJob {

    @Resource
    private ZnsbNssbSbrwService znsbNssbSbrwService;

    /**
     * 根据认定信息生成申报任务
     */
    @XxlJob("initSbrwByRdxxJob")
    public void execute() {
        log.info("==========开始根据认定信息生成申报任务数据任务==========");
        znsbNssbSbrwService.initSbrwByRdxx();
        log.info("==========根据认定信息生成申报任务数据任务结束==========");
    }

}
