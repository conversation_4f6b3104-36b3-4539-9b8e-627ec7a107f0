
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 分支机构
 * 
 * <p>Java class for fzjgGrid complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="fzjgGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *         &lt;element name="fzjgGridlb" type="{http://www.chinatax.gov.cn/dataspec/}fzjgGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "fzjgGrid", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "fzjgGridlb"
})
public class FzjgGrid
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected List<FzjgGridlbVO> fzjgGridlb;

    /**
     * Gets the value of the fzjgGridlb property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the fzjgGridlb property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getFzjgGridlb().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link FzjgGridlbVO }
     * 
     * 
     */
    public List<FzjgGridlbVO> getFzjgGridlb() {
        if (fzjgGridlb == null) {
            fzjgGridlb = new ArrayList<FzjgGridlbVO>();
        }
        return this.fzjgGridlb;
    }

}
