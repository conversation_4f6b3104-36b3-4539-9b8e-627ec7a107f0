package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 汇总纳税企业增值税分配表Grid列表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hznsqyzzsfpbGridlbVO", propOrder = { "fzjgnsrsbh", "fzjgmc", "fzjgxssr", "fzjgybhwjlwxssr", "fzjgybhwjlwjzjtxssr", "fzjgysfwxssr", "fzjgysfwjzjtxssr", "fzjgFpbl", "fzjgybhwjlwfpbl", "fzjgybhwjlwjzjtfpbl", "fzjgysfwfpbl", "fzjgysfwjzjtfpbl", "fzjgfpse", "fzjgybhwjlwfpse", "fzjgybhwjlwjzjtfpse", "fzjgysfwfpse", "fzjgysfwjzjtfpse" })
@Getter
@Setter
public class HznsqyzzsfpbGridlbVO {
    /**
     * 分支机构纳税人识别号
     */
    protected String fzjgnsrsbh;

    /**
     * 分支机构名称
     */
    protected String fzjgmc;

    /**
     * 分支机构销售收入
     */
    protected BigDecimal fzjgxssr;

    /**
     * 分支机构一般货物及劳务销售收入
     */
    protected BigDecimal fzjgybhwjlwxssr;

    /**
     * 分支机构一般货物及劳务即征即退销售收入
     */
    protected BigDecimal fzjgybhwjlwjzjtxssr;

    /**
     * 分支机构应税服务销售收入
     */
    protected BigDecimal fzjgysfwxssr;

    /**
     * 分支机构应税服务即征即退销售收入
     */
    protected BigDecimal fzjgysfwjzjtxssr;

    /**
     * 分支机构分配比例
     */
    protected BigDecimal fzjgFpbl;

    /**
     * 分支机构一般货物及劳务分配比例
     */
    protected BigDecimal fzjgybhwjlwfpbl;

    /**
     * 分支机构一般货物及劳务即征即退分配比例
     */
    protected BigDecimal fzjgybhwjlwjzjtfpbl;

    /**
     * 分支机构应税服务分配比例
     */
    protected BigDecimal fzjgysfwfpbl;

    /**
     * 分支机构应税服务即征即退分配比例
     */
    protected BigDecimal fzjgysfwjzjtfpbl;

    /**
     * 分支机构分配税额
     */
    protected BigDecimal fzjgfpse;

    /**
     * 分支机构一般货物及劳务分配税额
     */
    protected BigDecimal fzjgybhwjlwfpse;

    /**
     * 分支机构一般货物及劳务即征即退分配税额
     */
    protected BigDecimal fzjgybhwjlwjzjtfpse;

    /**
     * 分支机构应税服务分配税额
     */
    protected BigDecimal fzjgysfwfpse;

    /**
     * 分支机构应税服务即征即退分配税额
     */
    protected BigDecimal fzjgysfwjzjtfpse;
}