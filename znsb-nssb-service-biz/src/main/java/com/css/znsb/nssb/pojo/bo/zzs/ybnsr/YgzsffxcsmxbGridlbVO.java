package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ygzsffxcsmxbGridlbVO", propOrder = { "ewbhxh", "hmc", "ysxmdmjmc", "zzsslhzsl", "yyssl", "zzsbhsxse", "zzsxxynse", "zzsjshj", "zzsbqsjkcje", "zzskchhsxse", "zzskchxxynse", "zzsynse", "yysqcye", "yysbqfse", "yysbqykcje", "yysbqsjkcje", "yysqmye", "yysysyye", "yysynse" })
@Getter
@Setter
public class YgzsffxcsmxbGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 应税项目代码及名称
     */
    protected String ysxmdmjmc;

    /**
     * 增值税税率或征收率
     */
    protected BigDecimal zzsslhzsl;

    /**
     * 营业税税率
     */
    protected BigDecimal yyssl;

    /**
     * 增值税不含税销售额
     */
    protected BigDecimal zzsbhsxse;

    /**
     * 增值税销项应纳税额
     */
    protected BigDecimal zzsxxynse;

    /**
     * 增值税价税合计
     */
    protected BigDecimal zzsjshj;

    /**
     * 增值税本期实际扣除金额
     */
    protected BigDecimal zzsbqsjkcje;

    /**
     * 增值税扣除后含税销售额
     */
    protected BigDecimal zzskchhsxse;

    /**
     * 增值税扣除后销项应纳税额
     */
    protected BigDecimal zzskchxxynse;

    /**
     * 增值税应纳税额
     */
    protected BigDecimal zzsynse;

    /**
     * 营业税期初余额
     */
    protected BigDecimal yysqcye;

    /**
     * 营业税本期发生额
     */
    protected BigDecimal yysbqfse;

    /**
     * 营业税本期应扣除金额
     */
    protected BigDecimal yysbqykcje;

    /**
     * 营业税本期实际扣除金额
     */
    protected BigDecimal yysbqsjkcje;

    /**
     * 营业税期末余额
     */
    protected BigDecimal yysqmye;

    /**
     * 营业税应税营业额
     */
    protected BigDecimal yysysyye;

    /**
     * 营业税应纳税额
     */
    protected BigDecimal yysynse;
}