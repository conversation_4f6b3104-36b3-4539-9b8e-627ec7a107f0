package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 《代扣代缴税收通用缴款书抵扣清单》
 *
 * <p>dkdjsstyjksdkqdGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="dkdjsstyjksdkqdGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="dkdjsstyjksdkqdGridlb" type="{http://www.chinatax.gov.cn/dataspec/}dkdjsstyjksdkqdGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dkdjsstyjksdkqdGrid", propOrder = { "dkdjsstyjksdkqdGridlb" })
public class DkdjsstyjksdkqdGrid {
    @XmlElement(nillable = true, required = true)
    protected DkdjsstyjksdkqdGridlbVO dkdjsstyjksdkqdGridlb;

    /**
     * 获取dkdjsstyjksdkqdGridlb属性的值。
     */
    public DkdjsstyjksdkqdGridlbVO getDkdjsstyjksdkqdGridlb() {
        return dkdjsstyjksdkqdGridlb;
    }

    /**
     * 设置dkdjsstyjksdkqdGridlb属性的值。
     */
    public void setDkdjsstyjksdkqdGridlb(DkdjsstyjksdkqdGridlbVO value) {
        this.dkdjsstyjksdkqdGridlb = value;
    }
}