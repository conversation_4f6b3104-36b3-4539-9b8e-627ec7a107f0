package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 附加税申报
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "fjsxxGridlb", propOrder = { "sybh", "rdpzuuid", "zsxmDm", "zspmDm", "jsyj", "sl1", "ynse", "ssjmxzDm", "jmse", "yjse1", "ybtse", "rdyxqq", "rdyxqz", "swsxDm", "cjrhjmxzDm", "bqcjrhxqydmje", "zzsxejmje", "zzsmdse", "zzsldtse" })
@Getter
@Setter
public class FjsxxGridlb {
    /**
     * 税源编号
     */
    @XmlElement(nillable = true, required = true)
    protected String sybh;

    /**
     * 认定凭证UUID
     */
    @XmlElement(nillable = true, required = true)
    protected String rdpzuuid;

    /**
     * 征收项目
     */
    @XmlElement(nillable = true, required = true)
    protected String zsxmDm;

    /**
     * 征收品目
     */
    @XmlElement(nillable = true, required = true)
    protected String zspmDm;

    /**
     * 计税依据
     */
    protected BigDecimal jsyj;

    /**
     * 税率
     */
    protected BigDecimal sl1;

    /**
     * 本期应纳税额
     */
    protected BigDecimal ynse;

    /**
     * 税收减免性质代码
     */
    @XmlElement(nillable = true, required = true)
    protected String ssjmxzDm;

    /**
     * 减免税额
     */
    protected BigDecimal jmse;

    /**
     * 已缴税额
     */
    protected BigDecimal yjse1;

    /**
     * 本期应补（退）税额
     */
    protected BigDecimal ybtse;

    /**
     * 认定有效期起
     */
    @XmlElement(nillable = true, required = true)
    protected String rdyxqq;

    /**
     * 认定有效期止
     */
    @XmlElement(nillable = true, required = true)
    protected String rdyxqz;

    /**
     * 税务事项代码
     */
    @XmlElement(nillable = true, required = true)
    protected String swsxDm;

    /**
     * 产教融合减免性质代码
     */
    @XmlElement(nillable = true, required = true)
    protected String cjrhjmxzDm;

    /**
     * 本期产教融合抵免金额
     */
    protected BigDecimal bqcjrhxqydmje;

    /**
     * 增值税限额减免金额
     */
    protected BigDecimal zzsxejmje;

    /**
     * 增值税免抵税额
     */
    protected BigDecimal zzsmdse;

    /**
     * 本期扣减留抵退税额
     */
    protected BigDecimal zzsldtse;

    /**
     * 税收减免性质代码
     */
    protected String phjmxzDm;
    /**
     * 税收减免性质代码
     */
    protected String phjmswsxDm;

    protected BigDecimal phjzbl;
    protected BigDecimal phjmse;
}