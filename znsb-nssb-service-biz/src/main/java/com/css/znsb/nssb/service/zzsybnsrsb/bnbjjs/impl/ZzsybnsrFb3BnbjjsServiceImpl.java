package com.css.znsb.nssb.service.zzsybnsrsb.bnbjjs.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFb1BqxsqkmxDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFb3YsfwkcxmDTO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrQcxxItemVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.qcxx.YbnsrYtxxVO;
import com.css.znsb.nssb.service.zzsybnsrsb.bnbjjs.ZzsybnsrFb3BnbjjsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

import static cn.hutool.core.util.NumberUtil.max;
import static cn.hutool.core.util.NumberUtil.min;
import static cn.hutool.core.util.NumberUtil.nullToZero;
import static cn.hutool.core.util.NumberUtil.sub;
import static com.css.znsb.nssb.utils.BigDecimalUtils.add;
import static com.css.znsb.nssb.utils.MathUtils.round;

@Slf4j
@Service
public class ZzsybnsrFb3BnbjjsServiceImpl implements ZzsybnsrFb3BnbjjsService {

    /**
     * 附表三表内表间计算
     */
    @Override
    public void bnbjjs(YbnsrQcxxItemVO qcxxItemVO) {
        if (GyUtils.isNull(qcxxItemVO) || GyUtils.isNull(qcxxItemVO.getYtxx())) {
            return;
        }
        final String zzsyzlfzjg = qcxxItemVO.getFzxx().getBqxx().getZzsyzfzjg();
        final YbnsrYtxxVO ytxxVO = qcxxItemVO.getYtxx();
        final List<SbZzsYbnsrFb3YsfwkcxmDTO> fb3YtxxList = ytxxVO.getSbZzsYbnsrFb3Ysfwkcxm();
        fb3YtxxList.sort(Comparator.comparingInt(o -> Integer.parseInt(o.getEwbhxh())));
        for (SbZzsYbnsrFb3YsfwkcxmDTO fb3YsfwkcxmDTO : fb3YtxxList) {
            this.calcMsxse(fb3YsfwkcxmDTO, zzsyzlfzjg, ytxxVO);
            this.calcBqykcje(fb3YsfwkcxmDTO);
            this.calcBqsjkcje(fb3YsfwkcxmDTO);
            this.calcQmye(fb3YsfwkcxmDTO);
        }
    }

    private void calcMsxse(SbZzsYbnsrFb3YsfwkcxmDTO fb3YsfwkcxmDTO, String zzsyzlfzjg, YbnsrYtxxVO ytxxVO) {
        String ewbhxh = fb3YsfwkcxmDTO.getEwbhxh();
        String fb1Ewbhxh = "";
        switch (ewbhxh) {
            case "1":
                fb1Ewbhxh = "2";
                break;
            case "2":
                fb1Ewbhxh = "4";
                break;
            case "5":
                fb1Ewbhxh = "22";
                break;
            case "6":
                fb1Ewbhxh = "12";
                break;
            case "7":
                fb1Ewbhxh = "17";
                break;
            case "8":
                fb1Ewbhxh = "19";
                break;
            default:
                break;
        }
        if (GyUtils.isNotNull(fb1Ewbhxh)) {
            final SbZzsYbnsrFb1BqxsqkmxDTO fb1 = CollectionUtil.findOneByField(ytxxVO.getSbZzsYbnsrFb1Bqxsqkmx(),
                    "ewbhxh", fb1Ewbhxh);
            fb3YsfwkcxmDTO.setMsxse(GyUtils.isNull(fb1) ? BigDecimal.ZERO : fb1.getJshj());
        }
    }

    /**
     * 计算本期已扣除金额
     * 第2栏+第3栏
     * @param fb3YsfwkcxmDTO
     */
    private void calcBqykcje(SbZzsYbnsrFb3YsfwkcxmDTO fb3YsfwkcxmDTO) {
        final BigDecimal qcye = fb3YsfwkcxmDTO.getQcye();
        final BigDecimal bqfse = fb3YsfwkcxmDTO.getBqfse();
        final BigDecimal bqykcje = add(nullToZero(qcye), nullToZero(bqfse));
        fb3YsfwkcxmDTO.setBqykcje(round(bqykcje, 2));
    }

    /**
     * 计算本期实际扣除金额
     * min(第一栏， 第四栏)
     * @param fb3YsfwkcxmDTO
     */
    private void calcBqsjkcje(SbZzsYbnsrFb3YsfwkcxmDTO fb3YsfwkcxmDTO) {
        final BigDecimal msxse = fb3YsfwkcxmDTO.getMsxse();
        final BigDecimal bqykcje = fb3YsfwkcxmDTO.getBqykcje();
        final BigDecimal bqfse = fb3YsfwkcxmDTO.getBqfse();
        BigDecimal bqsjkcje = BigDecimal.ZERO;
        if (bqfse.compareTo(BigDecimal.ZERO) != 0) {
            bqsjkcje = max(min(msxse, bqykcje), BigDecimal.ZERO);
        }
        fb3YsfwkcxmDTO.setBqsjkcje(round(bqsjkcje, 2));
    }

    /**
     * 计算期末余额
     * 第5栏-第6栏
     * @param fb3YsfwkcxmDTO
     */
    private void calcQmye(SbZzsYbnsrFb3YsfwkcxmDTO fb3YsfwkcxmDTO) {
        final BigDecimal bqykcje = fb3YsfwkcxmDTO.getBqykcje();
        final BigDecimal bqsjkcje = fb3YsfwkcxmDTO.getBqsjkcje();
        final BigDecimal qmye = sub(bqykcje, bqsjkcje);
        fb3YsfwkcxmDTO.setQmye(round(qmye, 2));
    }
}
