
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 《消费税》烟类申报
 * 
 * <p>Java class for xfsYlsb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfsYlsb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="xfssb1" type="{http://www.chinatax.gov.cn/dataspec/}xfssb_yl"/>
 *         &lt;element name="xfssb1_fb1" type="{http://www.chinatax.gov.cn/dataspec/}bqzykcsejsby" minOccurs="0"/>
 *         &lt;element name="xfssb1_fb2" type="{http://www.chinatax.gov.cn/dataspec/}bqdsdjsejsby" minOccurs="0"/>
 *         &lt;element name="xfssb1_fb3" type="{http://www.chinatax.gov.cn/dataspec/}jyscqyndxsmxb" minOccurs="0"/>
 *         &lt;element name="xfssb1_fb4" type="{http://www.chinatax.gov.cn/dataspec/}gphggjyxfsjsjg" minOccurs="0"/>
 *         &lt;element name="xfssb1_fb5" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsemxb" minOccurs="0"/>
 *         &lt;element name="xfssb1_fb6" type="{http://www.chinatax.gov.cn/dataspec/}hznsqyxfsfpb" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfsYlsb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "xfssb1",
    "xfssb1Fb1",
    "xfssb1Fb2",
    "xfssb1Fb3",
    "xfssb1Fb4",
    "xfssb1Fb5",
    "xfssb1Fb6",
    "jyscqyhzscjyxfsqkbgbywbw"
})
public class XfsYlsb
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected XfssbYl xfssb1;
    @XmlElement(name = "xfssb1_fb1", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqzykcsejsby xfssb1Fb1;
    @XmlElement(name = "xfssb1_fb2", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqdsdjsejsby xfssb1Fb2;
    @XmlElement(name = "xfssb1_fb3", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Jyscqyndxsmxb xfssb1Fb3;
    @XmlElement(name = "xfssb1_fb4", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Gphggjyxfsjsjg xfssb1Fb4;
    @XmlElement(name = "xfssb1_fb5", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqjmsemxb xfssb1Fb5;
    @XmlElement(name = "xfssb1_fb6", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Hznsqyxfsfpb xfssb1Fb6;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Jyscqyhzscjyxfsqkbgbywbw jyscqyhzscjyxfsqkbgbywbw;
    /**
     * Gets the value of the xfssb1 property.
     * 
     * @return
     *     possible object is
     *     {@link XfssbYl }
     *     
     */
    public XfssbYl getXfssb1() {
        return xfssb1;
    }

    /**
     * Sets the value of the xfssb1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfssbYl }
     *     
     */
    public void setXfssb1(XfssbYl value) {
        this.xfssb1 = value;
    }

    /**
     * Gets the value of the xfssb1Fb1 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqzykcsejsby }
     *     
     */
    public Bqzykcsejsby getXfssb1Fb1() {
        return xfssb1Fb1;
    }

    /**
     * Sets the value of the xfssb1Fb1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqzykcsejsby }
     *     
     */
    public void setXfssb1Fb1(Bqzykcsejsby value) {
        this.xfssb1Fb1 = value;
    }

    /**
     * Gets the value of the xfssb1Fb2 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqdsdjsejsby }
     *     
     */
    public Bqdsdjsejsby getXfssb1Fb2() {
        return xfssb1Fb2;
    }

    /**
     * Sets the value of the xfssb1Fb2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqdsdjsejsby }
     *     
     */
    public void setXfssb1Fb2(Bqdsdjsejsby value) {
        this.xfssb1Fb2 = value;
    }

    /**
     * Gets the value of the xfssb1Fb3 property.
     * 
     * @return
     *     possible object is
     *     {@link Jyscqyndxsmxb }
     *     
     */
    public Jyscqyndxsmxb getXfssb1Fb3() {
        return xfssb1Fb3;
    }

    /**
     * Sets the value of the xfssb1Fb3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Jyscqyndxsmxb }
     *     
     */
    public void setXfssb1Fb3(Jyscqyndxsmxb value) {
        this.xfssb1Fb3 = value;
    }

    /**
     * Gets the value of the xfssb1Fb4 property.
     * 
     * @return
     *     possible object is
     *     {@link Gphggjyxfsjsjg }
     *     
     */
    public Gphggjyxfsjsjg getXfssb1Fb4() {
        return xfssb1Fb4;
    }

    /**
     * Sets the value of the xfssb1Fb4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Gphggjyxfsjsjg }
     *     
     */
    public void setXfssb1Fb4(Gphggjyxfsjsjg value) {
        this.xfssb1Fb4 = value;
    }

    /**
     * Gets the value of the xfssb1Fb5 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqjmsemxb }
     *     
     */
    public Bqjmsemxb getXfssb1Fb5() {
        return xfssb1Fb5;
    }

    /**
     * Sets the value of the xfssb1Fb5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqjmsemxb }
     *     
     */
    public void setXfssb1Fb5(Bqjmsemxb value) {
        this.xfssb1Fb5 = value;
    }

    /**
     * Gets the value of the xfssb1Fb6 property.
     * 
     * @return
     *     possible object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public Hznsqyxfsfpb getXfssb1Fb6() {
        return xfssb1Fb6;
    }

    /**
     * Sets the value of the xfssb1Fb6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public void setXfssb1Fb6(Hznsqyxfsfpb value) {
        this.xfssb1Fb6 = value;
    }

    public Jyscqyhzscjyxfsqkbgbywbw getJyscqyhzscjyxfsqkbgbywbw() {
        return jyscqyhzscjyxfsqkbgbywbw;
    }

    public void setJyscqyhzscjyxfsqkbgbywbw(Jyscqyhzscjyxfsqkbgbywbw jyscqyhzscjyxfsqkbgbywbw) {
        this.jyscqyhzscjyxfsqkbgbywbw = jyscqyhzscjyxfsqkbgbywbw;
    }
}
