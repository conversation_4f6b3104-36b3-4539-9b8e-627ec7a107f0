
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 申报表信息
 * 
 * <p>Java class for sbxxGridlbVo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="sbxxGridlbVo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="yssdl" type="{http://www.chinatax.gov.cn/dataspec/}yssdl" minOccurs="0"/>
 *         &lt;element name="xh" type="{http://www.chinatax.gov.cn/dataspec/}xh" minOccurs="0"/>
 *         &lt;element name="sybzDm1" type="{http://www.chinatax.gov.cn/dataspec/}sybzDm1" minOccurs="0"/>
 *         &lt;element name="sybh1" type="{http://www.chinatax.gov.cn/dataspec/}sybh1" minOccurs="0"/>
 *         &lt;element name="syzldz" type="{http://www.chinatax.gov.cn/dataspec/}syzldz" minOccurs="0"/>
 *         &lt;element name="zsxmDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmDm" minOccurs="0"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm" minOccurs="0"/>
 *         &lt;element name="zszmDm" type="{http://www.chinatax.gov.cn/dataspec/}zszmDm" minOccurs="0"/>
 *         &lt;element name="sfkssqq" type="{http://www.chinatax.gov.cn/dataspec/}sfkssqq" minOccurs="0"/>
 *         &lt;element name="sfkssqz" type="{http://www.chinatax.gov.cn/dataspec/}sfkssqz" minOccurs="0"/>
 *         &lt;element name="ysx" type="{http://www.chinatax.gov.cn/dataspec/}ysx" minOccurs="0"/>
 *         &lt;element name="jcx" type="{http://www.chinatax.gov.cn/dataspec/}jcx" minOccurs="0"/>
 *         &lt;element name="jsfyj" type="{http://www.chinatax.gov.cn/dataspec/}jsfyj" minOccurs="0"/>
 *         &lt;element name="sflhdwse" type="{http://www.chinatax.gov.cn/dataspec/}sflhdwse" minOccurs="0"/>
 *         &lt;element name="sskcs" type="{http://www.chinatax.gov.cn/dataspec/}sskcs" minOccurs="0"/>
 *         &lt;element name="bqynsfe" type="{http://www.chinatax.gov.cn/dataspec/}bqynsfe" minOccurs="0"/>
 *         &lt;element name="bqjmsfe" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsfe" minOccurs="0"/>
 *         &lt;element name="bqyjsfe" type="{http://www.chinatax.gov.cn/dataspec/}bqyjsfe" minOccurs="0"/>
 *         &lt;element name="bqybtsfe" type="{http://www.chinatax.gov.cn/dataspec/}bqybtsfe" minOccurs="0"/>
 *         &lt;element name="wdqzdbz" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="rdpzuuid" type="{http://www.chinatax.gov.cn/dataspec/}rdpzuuid" minOccurs="0"/>
 *         &lt;element name="rdzsuuid" type="{http://www.chinatax.gov.cn/dataspec/}rdzsuuid" minOccurs="0"/>
 *         &lt;element name="ysbse" type="{http://www.chinatax.gov.cn/dataspec/}ysbse" minOccurs="0"/>
 *         &lt;element name="ssjmxzDm" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzDm" minOccurs="0"/>
 *         &lt;element name="phjmswsxDm" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzDm" minOccurs="0"/>
 *         &lt;element name="phjmxzDm" type="{http://www.chinatax.gov.cn/dataspec/}ssjmxzDm" minOccurs="0"/>
 *         &lt;element name="phjzbl" type="{http://www.chinatax.gov.cn/dataspec/}bqynsfe" minOccurs="0"/>
 *         &lt;element name="phjmse" type="{http://www.chinatax.gov.cn/dataspec/}bqynsfe" minOccurs="0"/>
 *         lt;element name="zzsldtse" type="{http://www.chinatax.gov.cn/dataspec/}zzsldtse" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbxxGridlbVo", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "yssdl",
    "xh",
    "sybzDm1",
    "sybh1",
    "syzldz",
    "zsxmDm",
    "zspmDm",
    "zszmDm",
    "sfkssqq",
    "sfkssqz",
    "ysx",
    "jcx",
    "jsfyj",
    "sflhdwse",
    "sskcs",
    "bqynsfe",
    "bqjmsfe",
    "bqyjsfe",
    "bqybtsfe",
    "wdqzdbz",
    "rdpzuuid",
    "rdzsuuid",
    "ysbse",
    "ssjmxzDm",
    "qtgrczbdczjsfft",
    "qtgrczbdczlqq",
    "qtgrczbdczlqz",
    "qtgrczbdcfthyzjsr",
    "phjmswsxDm",
    "phjmxzDm",
    "phjzbl",
    "phjmse",
    "zzsldtse",
    "bqsfsyxgmyhzc",
    "jzzcsyztDm",
    "bz1"
})
public class SbxxGridlbVo
    implements Serializable
{

    private final static long serialVersionUID = -8720762420527276032L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yssdl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Integer xh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sybzDm1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sybh1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String syzldz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zszmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfkssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfkssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ysx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jcx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jsfyj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double sflhdwse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double sskcs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bqynsfe;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bqjmsfe;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bqyjsfe;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bqybtsfe;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String wdqzdbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String rdpzuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String rdzsuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ysbse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssjmxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qtgrczbdczjsfft;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qtgrczbdczlqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qtgrczbdczlqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qtgrczbdcfthyzjsr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String phjmswsxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String phjmxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double phjzbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double phjmse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzsldtse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bqsfsyxgmyhzc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jzzcsyztDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bz1;

    public String getBz1() {
        return bz1;
    }

    public void setBz1(String bz1) {
        this.bz1 = bz1;
    }

    public String getBqsfsyxgmyhzc() {
        return bqsfsyxgmyhzc;
    }

    public void setBqsfsyxgmyhzc(String bqsfsyxgmyhzc) {
        this.bqsfsyxgmyhzc = bqsfsyxgmyhzc;
    }

    public String getJzzcsyztDm() {
        return jzzcsyztDm;
    }

    public void setJzzcsyztDm(String jzzcsyztDm) {
        this.jzzcsyztDm = jzzcsyztDm;
    }

    /**
     * Gets the value of the yssdl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYssdl() {
        return yssdl;
    }

    /**
     * Sets the value of the yssdl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYssdl(Double value) {
        this.yssdl = value;
    }

    /**
     * Gets the value of the xh property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getXh() {
        return xh;
    }

    /**
     * Sets the value of the xh property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setXh(Integer value) {
        this.xh = value;
    }

    /**
     * Gets the value of the sybzDm1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSybzDm1() {
        return sybzDm1;
    }

    /**
     * Sets the value of the sybzDm1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSybzDm1(String value) {
        this.sybzDm1 = value;
    }

    /**
     * Gets the value of the sybh1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSybh1() {
        return sybh1;
    }

    /**
     * Sets the value of the sybh1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSybh1(String value) {
        this.sybh1 = value;
    }

    /**
     * Gets the value of the syzldz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSyzldz() {
        return syzldz;
    }

    /**
     * Sets the value of the syzldz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSyzldz(String value) {
        this.syzldz = value;
    }

    /**
     * Gets the value of the zsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmDm() {
        return zsxmDm;
    }

    /**
     * Sets the value of the zsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmDm(String value) {
        this.zsxmDm = value;
    }

    /**
     * Gets the value of the zspmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * Sets the value of the zspmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * Gets the value of the zszmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZszmDm() {
        return zszmDm;
    }

    /**
     * Sets the value of the zszmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZszmDm(String value) {
        this.zszmDm = value;
    }

    /**
     * Gets the value of the sfkssqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfkssqq() {
        return sfkssqq;
    }

    /**
     * Sets the value of the sfkssqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfkssqq(String value) {
        this.sfkssqq = value;
    }

    /**
     * Gets the value of the sfkssqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfkssqz() {
        return sfkssqz;
    }

    /**
     * Sets the value of the sfkssqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfkssqz(String value) {
        this.sfkssqz = value;
    }

    /**
     * Gets the value of the ysx property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYsx() {
        return ysx;
    }

    /**
     * Sets the value of the ysx property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYsx(Double value) {
        this.ysx = value;
    }

    /**
     * Gets the value of the jcx property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJcx() {
        return jcx;
    }

    /**
     * Sets the value of the jcx property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJcx(Double value) {
        this.jcx = value;
    }

    /**
     * Gets the value of the jsfyj property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJsfyj() {
        return jsfyj;
    }

    /**
     * Sets the value of the jsfyj property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJsfyj(Double value) {
        this.jsfyj = value;
    }

    /**
     * Gets the value of the sflhdwse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSflhdwse() {
        return sflhdwse;
    }

    /**
     * Sets the value of the sflhdwse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSflhdwse(Double value) {
        this.sflhdwse = value;
    }

    /**
     * Gets the value of the sskcs property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSskcs() {
        return sskcs;
    }

    /**
     * Sets the value of the sskcs property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSskcs(Double value) {
        this.sskcs = value;
    }

    /**
     * Gets the value of the bqynsfe property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBqynsfe() {
        return bqynsfe;
    }

    /**
     * Sets the value of the bqynsfe property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBqynsfe(Double value) {
        this.bqynsfe = value;
    }

    /**
     * Gets the value of the bqjmsfe property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBqjmsfe() {
        return bqjmsfe;
    }

    /**
     * Sets the value of the bqjmsfe property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBqjmsfe(Double value) {
        this.bqjmsfe = value;
    }

    /**
     * Gets the value of the bqyjsfe property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBqyjsfe() {
        return bqyjsfe;
    }

    /**
     * Sets the value of the bqyjsfe property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBqyjsfe(Double value) {
        this.bqyjsfe = value;
    }

    /**
     * Gets the value of the bqybtsfe property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBqybtsfe() {
        return bqybtsfe;
    }

    /**
     * Sets the value of the bqybtsfe property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBqybtsfe(Double value) {
        this.bqybtsfe = value;
    }

    /**
     * Gets the value of the wdqzdbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWdqzdbz() {
        return wdqzdbz;
    }

    /**
     * Sets the value of the wdqzdbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWdqzdbz(String value) {
        this.wdqzdbz = value;
    }

    /**
     * Gets the value of the rdpzuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdpzuuid() {
        return rdpzuuid;
    }

    /**
     * Sets the value of the rdpzuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdpzuuid(String value) {
        this.rdpzuuid = value;
    }

    /**
     * Gets the value of the rdzsuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdzsuuid() {
        return rdzsuuid;
    }

    /**
     * Sets the value of the rdzsuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdzsuuid(String value) {
        this.rdzsuuid = value;
    }

    /**
     * Gets the value of the ysbse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYsbse() {
        return ysbse;
    }

    /**
     * Sets the value of the ysbse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYsbse(Double value) {
        this.ysbse = value;
    }

    /**
     * Gets the value of the ssjmxzDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsjmxzDm() {
        return ssjmxzDm;
    }

    /**
     * Sets the value of the ssjmxzDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsjmxzDm(String value) {
        this.ssjmxzDm = value;
    }

    /**
     *创建时间:2016-5-25下午04:18:16
     *get方法
     * @return the qtgrczbdczjsfft
     */
    public String getQtgrczbdczjsfft() {
        return qtgrczbdczjsfft;
    }

    /**
     * 创建时间:2016-5-25下午04:18:16
     * set方法
     * @param qtgrczbdczjsfft the qtgrczbdczjsfft to set
     */
    public void setQtgrczbdczjsfft(String qtgrczbdczjsfft) {
        this.qtgrczbdczjsfft = qtgrczbdczjsfft;
    }

    /**
     *创建时间:2016-5-25下午04:18:16
     *get方法
     * @return the qtgrczbdczlqq
     */
    public String getQtgrczbdczlqq() {
        return qtgrczbdczlqq;
    }

    /**
     * 创建时间:2016-5-25下午04:18:16
     * set方法
     * @param qtgrczbdczlqq the qtgrczbdczlqq to set
     */
    public void setQtgrczbdczlqq(String qtgrczbdczlqq) {
        this.qtgrczbdczlqq = qtgrczbdczlqq;
    }

    /**
     *创建时间:2016-5-25下午04:18:16
     *get方法
     * @return the qtgrczbdczlqz
     */
    public String getQtgrczbdczlqz() {
        return qtgrczbdczlqz;
    }

    /**
     * 创建时间:2016-5-25下午04:18:16
     * set方法
     * @param qtgrczbdczlqz the qtgrczbdczlqz to set
     */
    public void setQtgrczbdczlqz(String qtgrczbdczlqz) {
        this.qtgrczbdczlqz = qtgrczbdczlqz;
    }

    /**
     *创建时间:2016-5-25下午04:18:16
     *get方法
     * @return the qtgrczbdcfthyzjsr
     */
    public Double getQtgrczbdcfthyzjsr() {
        return qtgrczbdcfthyzjsr;
    }

    /**
     * 创建时间:2016-5-25下午04:18:16
     * set方法
     * @param qtgrczbdcfthyzjsr the qtgrczbdcfthyzjsr to set
     */
    public void setQtgrczbdcfthyzjsr(Double qtgrczbdcfthyzjsr) {
        this.qtgrczbdcfthyzjsr = qtgrczbdcfthyzjsr;
    }
    /**
     *创建时间:2019-1-20 18:06:39
     *get方法
     * @return the phjmxzDm
     */
    public String getPhjmxzDm() {
        return phjmxzDm;
    }
    /**
     * 创建时间:2016-5-25下午04:18:16
     * set方法
     * @param phjmxzDm the phjmxzDm to set
     */
    public void setPhjmxzDm(String phjmxzDm) {
        this.phjmxzDm = phjmxzDm;
    }
    /**
     *创建时间:2019-1-20 18:06:39
     *get方法
     * @return the phjzbl
     */
    public Double getPhjzbl() {
        return phjzbl;
    }
    /**
     * 创建时间:2016-5-25下午04:18:16
     * set方法
     */
    public void setPhjzbl(Double phjzbl) {
        this.phjzbl = phjzbl;
    }
    /**
     *创建时间:2019-1-20 18:06:39
     *get方法
     * @return the phjmse
     */
    public Double getPhjmse() {
        return phjmse;
    }
    /**
     * 创建时间:2016-5-25下午04:18:16
     * set方法
     * @param phjmse the phjmse to set
     */
    public void setPhjmse(Double phjmse) {
        this.phjmse = phjmse;
    }

    public String getPhjmswsxDm() {
        return phjmswsxDm;
    }

    public void setPhjmswsxDm(String phjmswsxDm) {
        this.phjmswsxDm = phjmswsxDm;
    }

    public Double getZzsldtse() {
        return zzsldtse;
    }

    public void setZzsldtse(Double zzsldtse) {
        this.zzsldtse = zzsldtse;
    }

}
