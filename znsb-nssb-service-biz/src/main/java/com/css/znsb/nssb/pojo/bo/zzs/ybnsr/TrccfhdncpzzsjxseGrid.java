package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 投入产出法核定农产品增值税进项税额Grid
 *
 * <p>trccfhdncpzzsjxseGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="trccfhdncpzzsjxseGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="trccfhdncpzzsjxseGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}trccfhdncpzzsjxseGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "trccfhdncpzzsjxseGrid", propOrder = { "trccfhdncpzzsjxseGridlbVO" })
public class TrccfhdncpzzsjxseGrid {
    @XmlElement(nillable = true, required = true)
    protected List<TrccfhdncpzzsjxseGridlbVO> trccfhdncpzzsjxseGridlbVO;

    /**
     * Gets the value of the trccfhdncpzzsjxseGridlbVO property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the trccfhdncpzzsjxseGridlbVO property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getTrccfhdncpzzsjxseGridlbVO().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TrccfhdncpzzsjxseGridlbVO}
     */
    public List<TrccfhdncpzzsjxseGridlbVO> getTrccfhdncpzzsjxseGridlbVO() {
        if (trccfhdncpzzsjxseGridlbVO == null) {
            trccfhdncpzzsjxseGridlbVO = new ArrayList<TrccfhdncpzzsjxseGridlbVO>();
        }
        return this.trccfhdncpzzsjxseGridlbVO;
    }
}