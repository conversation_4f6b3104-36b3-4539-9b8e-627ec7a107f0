package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.qcxx.QcxxService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 申报期初信息归集job
 */
@Slf4j
@Component
public class SbQcxxgjJob {

    @Resource
    private QcxxService qcxxService;

    /**
     * 申报期初信息归集job
     */
    @XxlJob("sbQcxxgjJob")
    public void execute() {
        log.info("==========开始进行申报期初信息归集==========");
        String sbrwuuid = XxlJobHelper.getJobParam();
        qcxxService.sbQcxxgj(sbrwuuid);
        log.info("==========申报期初信息归集结束==========");
    }
}
