package com.css.znsb.nssb.pojo.bo.hxzg.sb000;


import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;

/**
 *  金税三期工程核心征管及应用总集成项目 
 * gov.gt3.vo.sbzs.sb.sb000
 * File: SBYjskxxVO.java 创建时间:2014-6-29上午5:07:05
 * Title: SBYjskxxVO
 * Description: 描述（简要描述类的职责、实现方式、使用注意事项等）
 * Copyright: Copyright (c) 2014 中国软件与技术服务股份有限公司
 * Company: 中国软件与技术服务股份有限公司
 * 模块: 申报
 * <AUTHOR>
 * @reviewer 审核人名字 
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */
public class SBYjskxxVO extends TaxBaseVO {

	private static final long serialVersionUID = -1004456959219359806L;

	/**
	 * @description 预缴税款UUID
	 * @value value:yjskuuid
	 */
	private String yjskuuid;
	
	//申报信息id，用于记录哪些申报明细与此相关
	private String sbxxuuid;
	
	// 预缴纳税款序号
	private String yjskxh;
	
	// 征收项目代码
	private String zsxmDm;
	
	// 征收品目代码
	private String zspmDm;

    // 征收子目代码
    private String zszmDm;
	
	// 登记序号
	private String djxh;
	
	// 税款所属期起
	private String skssqq;
	
	// 税款所属期止
	private String skssqz;
	
	// 预缴总额
	private Double yjze;
	
	// 预缴余额
	private Double yjye1;
	
	// 本次使用金额
	private Double bcsyje;
	
	// 税款属性代码
	private String sksxDm;
	
	// 主管税务所（科、分局）代码
	private String zgswskfjDm;
	
	// 数据归属地区
	private String sjgsdq;
	
	// 行业代码
	private String hyDm;
	
	// 认定凭证UUID（税费种认定）
	private String rdpzuuid;
	/**
	 * 凭证序号
	 */
	private String pzxh;
	/**
	 * 税票号码
	 */
	private String sphm;
	/**
	 * 票证种类代码
	 */
	private String pzzlDm;
	/**
	 * 票证字轨代码
	 */
	private String pzzgDm;
	/**
	 * 票证号码
	 */
	private String pzhm;
	/**
	 * 录入人代码
	 */
	private String lrrDm;

    /**
     * 录入日期
     */
    private String lrrq;

    /**
     * 增值税预缴台帐uuid
     */
    private String zzsyjtzuuid;

    /**
     * 预缴数据来源:1 报验户，2 跨区税源登记主体，3 异地代开增值税专用发票主体,4 分支机构
     */
    private String yjsjly;

    /**
     * 预缴数据来源地登记序号
     */
    private String yjsjlyDjxh;

    /**
     * 预缴数据来源地税务机关
     */
    private String yjsjlySwjg;

    /**
     * 预缴数据类型:1 划断前,2 划断后,3 划断前补录,4 划断后补录,5 申报附表四
     */
    private String yjsjlx;

    /**
     * 应征凭证种类代码
     */
    private String yzpzzlDm;

	/**
	 * @description 申报临时明细ID
	 * @value value:sbxxlsmxid
	 */
	private String sbxxlsmxid;
	private Integer yzpzmxxh;


    /**
     *创建时间:2016-4-22下午02:39:40
     *get方法
     * @return the yzpzmxxh
     */
    public Integer getYzpzmxxh() {
        return yzpzmxxh;
    }

    /**
     * 创建时间:2016-4-22下午02:39:40
     * set方法
     * @param yzpzmxxh the yzpzmxxh to set
     */
    public void setYzpzmxxh(Integer yzpzmxxh) {
        this.yzpzmxxh = yzpzmxxh;
    }

    public String getSbxxlsmxid() {
        return sbxxlsmxid;
    }

    public void setSbxxlsmxid(String sbxxlsmxid) {
        this.sbxxlsmxid = sbxxlsmxid;
    }

    public String getYjskuuid() {
        return yjskuuid;
    }

    public void setYjskuuid(String yjskuuid) {
        this.yjskuuid = yjskuuid;
    }

    public String getYjskxh() {
        return yjskxh;
    }

    public void setYjskxh(String yjskxh) {
        this.yjskxh = yjskxh;
    }

    public String getZsxmDm() {
        return zsxmDm;
    }

    public void setZsxmDm(String zsxmDm) {
        this.zsxmDm = zsxmDm;
    }

    public String getZspmDm() {
        return zspmDm;
    }

    public void setZspmDm(String zspmDm) {
        this.zspmDm = zspmDm;
    }

    public String getDjxh() {
        return djxh;
    }

    public void setDjxh(String djxh) {
        this.djxh = djxh;
    }

    public String getSkssqq() {
        return skssqq;
    }

    public void setSkssqq(String skssqq) {
        this.skssqq = skssqq;
    }

    public String getSkssqz() {
        return skssqz;
    }

    public void setSkssqz(String skssqz) {
        this.skssqz = skssqz;
    }

    public Double getYjze() {
        return yjze;
    }

    public void setYjze(Double yjze) {
        this.yjze = yjze;
    }

    public Double getYjye1() {
        return yjye1;
    }

    public void setYjye1(Double yjye1) {
        this.yjye1 = yjye1;
    }

    public String getSksxDm() {
        return sksxDm;
    }

    public void setSksxDm(String sksxDm) {
        this.sksxDm = sksxDm;
    }

    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    public void setZgswskfjDm(String zgswskfjDm) {
        this.zgswskfjDm = zgswskfjDm;
    }

    public String getSjgsdq() {
        return sjgsdq;
    }

    public void setSjgsdq(String sjgsdq) {
        this.sjgsdq = sjgsdq;
    }

    public String getHyDm() {
        return hyDm;
    }

    public void setHyDm(String hyDm) {
        this.hyDm = hyDm;
    }

    public String getRdpzuuid() {
        return rdpzuuid;
    }

    public void setRdpzuuid(String rdpzuuid) {
        this.rdpzuuid = rdpzuuid;
    }

    /**
     *创建时间:2014-8-28下午09:17:51
     *get the pzxh方法
     * @return the pzxh
     */
    public String getPzxh() {
        return pzxh;
    }

    /**
     * 创建时间:2014-8-28下午09:17:51
     * set the pzxh方法
     * @param pzxh the pzxh to set
     */
    public void setPzxh(String pzxh) {
        this.pzxh = pzxh;
    }

    /**
     *创建时间:2014-8-28下午09:17:51
     *get the sphm方法
     * @return the sphm
     */
    public String getSphm() {
        return sphm;
    }

    /**
     * 创建时间:2014-8-28下午09:17:51
     * set the sphm方法
     * @param sphm the sphm to set
     */
    public void setSphm(String sphm) {
        this.sphm = sphm;
    }

    /**
     *创建时间:2014-8-28下午09:17:51
     *get the pzzlDm方法
     * @return the pzzlDm
     */
    public String getPzzlDm() {
        return pzzlDm;
    }

    /**
     * 创建时间:2014-8-28下午09:17:51
     * set the pzzlDm方法
     * @param pzzlDm the pzzlDm to set
     */
    public void setPzzlDm(String pzzlDm) {
        this.pzzlDm = pzzlDm;
    }

    /**
     *创建时间:2014-8-28下午09:17:51
     *get the pzzgDm方法
     * @return the pzzgDm
     */
    public String getPzzgDm() {
        return pzzgDm;
    }

    /**
     * 创建时间:2014-8-28下午09:17:51
     * set the pzzgDm方法
     * @param pzzgDm the pzzgDm to set
     */
    public void setPzzgDm(String pzzgDm) {
        this.pzzgDm = pzzgDm;
    }

    /**
     *创建时间:2014-8-28下午09:17:51
     *get the pzhm方法
     * @return the pzhm
     */
    public String getPzhm() {
        return pzhm;
    }

    /**
     * 创建时间:2014-8-28下午09:17:51
     * set the pzhm方法
     * @param pzhm the pzhm to set
     */
    public void setPzhm(String pzhm) {
        this.pzhm = pzhm;
    }

    /**
     *创建时间:2014-8-28下午09:17:51
     *get the lrrDm方法
     * @return the lrrDm
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * 创建时间:2014-8-28下午09:17:51
     * set the lrrDm方法
     * @param lrrDm the lrrDm to set
     */
    public void setLrrDm(String lrrDm) {
        this.lrrDm = lrrDm;
    }

    public Double getBcsyje() {
        return bcsyje;
    }

    public void setBcsyje(Double bcsyje) {
        this.bcsyje = bcsyje;
    }

    public String getSbxxuuid() {
        return sbxxuuid;
    }

    public void setSbxxuuid(String sbxxuuid) {
        this.sbxxuuid = sbxxuuid;
    }

    public String getZszmDm() {
        return zszmDm;
    }

    public void setZszmDm(String zszmDm) {
        this.zszmDm = zszmDm;
    }

    public String getZzsyjtzuuid() {
        return zzsyjtzuuid;
    }

    public void setZzsyjtzuuid(String zzsyjtzuuid) {
        this.zzsyjtzuuid = zzsyjtzuuid;
    }

    public String getYjsjly() {
        return yjsjly;
    }

    public void setYjsjly(String yjsjly) {
        this.yjsjly = yjsjly;
    }

    public String getYjsjlyDjxh() {
        return yjsjlyDjxh;
    }

    public void setYjsjlyDjxh(String yjsjlyDjxh) {
        this.yjsjlyDjxh = yjsjlyDjxh;
    }

    public String getYjsjlySwjg() {
        return yjsjlySwjg;
    }

    public void setYjsjlySwjg(String yjsjlySwjg) {
        this.yjsjlySwjg = yjsjlySwjg;
    }

    public String getYjsjlx() {
        return yjsjlx;
    }

    public void setYjsjlx(String yjsjlx) {
        this.yjsjlx = yjsjlx;
    }

    public String getYzpzzlDm() {
        return yzpzzlDm;
    }

    public void setYzpzzlDm(String yzpzzlDm) {
        this.yzpzzlDm = yzpzzlDm;
    }

    public String getLrrq() {
        return lrrq;
    }

    public void setLrrq(String lrrq) {
        this.lrrq = lrrq;
    }
}
