
package com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc;

import com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc.general.SBZzsYbnsrLdtsbhqkVO;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 《增值税（一般纳税人）》业务报文
 * 
 * <p>Java class for ZzsybsbSbbdxxVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ZzsybsbSbbdxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="zzssyyybnsr_zb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_zb"/>
 *         &lt;element name="zzssyyybnsr01_bqxsqkmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr01_bqxsqkmxb"/>
 *         &lt;element name="zzssyyybnsr02_bqjxsemxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr02_bqjxsemxb"/>
 *         &lt;element name="zzssyyybnsr03_ysfwkcxmmx" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr03_ysfwkcxmmx"/>
 *         &lt;element name="zzssyyybnsr04_bqjxsemxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr04_bqjxsemxb"/>
 *         &lt;element name="zzssyyybnsr05_bdcfqdkjsb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr05_bdcfqdkjsb"/>
 *         &lt;element name="zzssyyybnsr_zzsybnsrbqynseayskmfjb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_zzsybnsrbqynseayskmfjb" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_gdzcjxsedkqkb" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_gdzcjxsedkqkb" minOccurs="0"/>
 *         &lt;element name="hqysqyfzjgcdd" type="{http://www.chinatax.gov.cn/dataspec/}hqysqyfzjgcdd" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_ndhkysqyqsb" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_ndhkysqyqsb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_cpygxcqkmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_cpygxcqkmxb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_cpygxcslmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_cpygxcslmxb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_jdclscqyxsmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_jdclscqyxsmxb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_jdclscqyxsqktjb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_jdclscqyxsqktjb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_jdcxstyfplycybb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_jdcxstyfplycybb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_jdcxstyfpqd" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_jdcxstyfpqd" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_jdcljxqyxsmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_jdcljxqyxsmxb" minOccurs="0"/>
 *         &lt;element name="dlqyzzsxxsehjxsecdd" type="{http://www.chinatax.gov.cn/dataspec/}dlqyzzsxxsehjxsecdd" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_zzsysfpdkqd" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_zzsysfpdkqd" minOccurs="0"/>
 *         &lt;element name="hgwspzdklsjcjb" type="{http://www.chinatax.gov.cn/dataspec/}hgwspzdklsjcjb" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_ncphdkczzsjxsejsb" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_ncphdkczzsjxsejsb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_hqysqyfzjgcdd" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_hqysqyfzjgcdd" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_cbfhdccpzzsjxsejsb" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_cbfhdccpzzsjxsejsb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_jyzyxsyphzb" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_jyzyxsyphzb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_jyzyfjyxxmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_jyzyfjyxxmxb" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_dkdjsstyjksdkqd" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_dkdjsstyjksdkqd" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_hznsqytycdd" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_hznsqytycdd" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_hznsqyzzsfpb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_hznsqyzzsfpb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_kstlqyzzsfpb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_kstlqyzzsfpb" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_yzqyfzjgzzshznsxxcdd" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_yzqyfzjgzzshznsxxcdd" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_tlysqyfzjgzzshznsxxcdd" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_tlysqyfzjgzzshznsxxcdd" minOccurs="0"/>
 *         &lt;element name="zzsybnsrsb_dxqyfzjgzzshznsxxcdd" type="{http://www.chinatax.gov.cn/dataspec/}zzsybnsrsb_dxqyfzjgzzshznsxxcdd" minOccurs="0"/>
 *         &lt;element name="zzsfjssb" type="{http://www.chinatax.gov.cn/dataspec/}zzsfjssb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_bfcpxstjb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_bfcpxstjb" minOccurs="0"/>
 *         &lt;element name="zzsjmssbmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzsjmssbmxb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_yqtqyzzsfpb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_yqtqyzzsfpb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_bqdkjxsejgmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_bqdkjxsejgmxb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_tljsjjnssbb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_tljsjjnssbb" minOccurs="0"/>
 *         &lt;element name="zzssyyybnsr_ygzsffxcsmxb" type="{http://www.chinatax.gov.cn/dataspec/}zzssyyybnsr_ygzsffxcsmxb" minOccurs="0"/>
 *         &lt;element name="sbZzsYbnsrLdtsbhqkVO" type="{http://www.chinatax.gov.cn/dataspec/}SBZzsYbnsrLdtsbhqkVO" minOccurs="0"/>
 *         &lt;element name="sbZzsJcdlqyWgxpTzMxb" type="{http://www.chinatax.gov.cn/dataspec/}sbZzsJcdlqyWgxpTzMxb" minOccurs="0"/>
 *         &lt;element name="sbZzsXzjjdjzcJg" type="{http://www.chinatax.gov.cn/dataspec/}SBZzsXzjjdjzcJgVO" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ZzsybsbSbbdxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "zzssyyybnsrZb",
    "zzssyyybnsr01Bqxsqkmxb",
    "zzssyyybnsr02Bqjxsemxb",
    "zzssyyybnsr03Ysfwkcxmmx",
    "zzssyyybnsr04Bqjxsemxb",
    "zzssyyybnsr05Bdcfqdkjsb",
    "zzssyyybnsrZzsybnsrbqynseayskmfjb",
    "zzsybnsrsbGdzcjxsedkqkb",
    "hqysqyfzjgcdd",
    "zzsybnsrsbNdhkysqyqsb",
    "zzssyyybnsrCpygxcqkmxb",
    "zzssyyybnsrCpygxcslmxb",
    "zzssyyybnsrJdclscqyxsmxb",
    "zzssyyybnsrJdclscqyxsqktjb",
    "zzssyyybnsrJdcxstyfplycybb",
    "zzssyyybnsrJdcxstyfpqd",
    "zzssyyybnsrJdcljxqyxsmxb",
    "dlqyzzsxxsehjxsecdd",
    "zzssyyybnsrZzsysfpdkqd",
    "hgwspzdklsjcjb",
    "zzsybnsrsbNcphdkczzsjxsejsb",
    "zzssyyybnsrHqysqyfzjgcdd",
    "zzsybnsrsbCbfhdccpzzsjxsejsb",
    "zzssyyybnsrGjncpzjxshdncpzzsjxsejsb",
    "zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb",
    "zzsybnsrsbJyzyxsyphzb",
    "zzssyyybnsrJyzyfjyxxmxb",
    "zzsybnsrsbDkdjsstyjksdkqd",
    "zzssyyybnsrHznsqytycdd",
    "zzssyyybnsrHznsqyzzsfpb",
    "zzssyyybnsrKstlqyzzsfpb",
    "zzsybnsrsbYzqyfzjgzzshznsxxcdd",
    "zzsybnsrsbTlysqyfzjgzzshznsxxcdd",
    "zzsybnsrsbDxqyfzjgzzshznsxxcdd",
    "zzsfjssb",
    "zzssyyybnsrBfcpxstjb",
    "zzsjmssbmxb",
    "zzssyyybnsrYqtqyzzsfpb",
    "zzssyyybnsrBqdkjxsejgmxb",
    "zzssyyybnsrTljsjjnssbb",
    "zzssyyybnsrYgzsffxcsmxb",
    "sbZzsYbnsrLdtsbhqkVO",
    "sbZzsJcdlqyWgxpTzMxb",
    "sbZzsXzjjdjzcJg"
})
public class ZzsybsbSbbdxxVO
    implements Serializable
{

    private final static long serialVersionUID = 1468566763656402744L;
    @XmlElement(name = "zzssyyybnsr_zb", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    @JsonProperty(value = "zzssyyybnsr_zb")
    protected ZzssyyybnsrZb zzssyyybnsrZb;
    @XmlElement(name = "zzssyyybnsr01_bqxsqkmxb", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    @JsonProperty(value = "zzssyyybnsr01_bqxsqkmxb")
    protected Zzssyyybnsr01Bqxsqkmxb zzssyyybnsr01Bqxsqkmxb;
    @XmlElement(name = "zzssyyybnsr02_bqjxsemxb", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    @JsonProperty(value = "zzssyyybnsr02_bqjxsemxb")
    protected Zzssyyybnsr02Bqjxsemxb zzssyyybnsr02Bqjxsemxb;
    @XmlElement(name = "zzssyyybnsr03_ysfwkcxmmx", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    @JsonProperty(value = "zzssyyybnsr03_ysfwkcxmmx")
    protected Zzssyyybnsr03Ysfwkcxmmx zzssyyybnsr03Ysfwkcxmmx;
    @XmlElement(name = "zzssyyybnsr04_bqjxsemxb", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    @JsonProperty(value = "zzssyyybnsr04_bqjxsemxb")
    protected Zzssyyybnsr04Bqjxsemxb zzssyyybnsr04Bqjxsemxb;
    @XmlElement(name = "zzssyyybnsr05_bdcfqdkjsb", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    @JsonProperty(value = "zzssyyybnsr05_bdcfqdkjsb")
    protected Zzssyyybnsr05Bdcfqdkjsb zzssyyybnsr05Bdcfqdkjsb;
    @XmlElement(name = "zzssyyybnsr_zzsybnsrbqynseayskmfjb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_zzsybnsrbqynseayskmfjb")
    protected ZzssyyybnsrZzsybnsrbqynseayskmfjb zzssyyybnsrZzsybnsrbqynseayskmfjb;
    @XmlElement(name = "zzsybnsrsb_gdzcjxsedkqkb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_gdzcjxsedkqkb")
    protected ZzsybnsrsbGdzcjxsedkqkb zzsybnsrsbGdzcjxsedkqkb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Hqysqyfzjgcdd hqysqyfzjgcdd;
    @XmlElement(name = "zzsybnsrsb_ndhkysqyqsb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_ndhkysqyqsb")
    protected ZzsybnsrsbNdhkysqyqsb zzsybnsrsbNdhkysqyqsb;
    @XmlElement(name = "zzssyyybnsr_cpygxcqkmxb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_cpygxcqkmxb")
    protected ZzssyyybnsrCpygxcqkmxb zzssyyybnsrCpygxcqkmxb;
    @XmlElement(name = "zzssyyybnsr_cpygxcslmxb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_cpygxcslmxb")
    protected ZzssyyybnsrCpygxcslmxb zzssyyybnsrCpygxcslmxb;
    @XmlElement(name = "zzssyyybnsr_jdclscqyxsmxb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_jdclscqyxsmxb")
    protected ZzssyyybnsrJdclscqyxsmxb zzssyyybnsrJdclscqyxsmxb;
    @XmlElement(name = "zzssyyybnsr_jdclscqyxsqktjb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_jdclscqyxsqktjb")
    protected ZzssyyybnsrJdclscqyxsqktjb zzssyyybnsrJdclscqyxsqktjb;
    @XmlElement(name = "zzssyyybnsr_jdcxstyfplycybb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_jdcxstyfplycybb")
    protected ZzssyyybnsrJdcxstyfplycybb zzssyyybnsrJdcxstyfplycybb;
    @XmlElement(name = "zzssyyybnsr_jdcxstyfpqd", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_jdcxstyfpqd")
    protected ZzssyyybnsrJdcxstyfpqd zzssyyybnsrJdcxstyfpqd;
    @XmlElement(name = "zzssyyybnsr_jdcljxqyxsmxb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_jdcljxqyxsmxb")
    protected ZzssyyybnsrJdcljxqyxsmxb zzssyyybnsrJdcljxqyxsmxb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Dlqyzzsxxsehjxsecdd dlqyzzsxxsehjxsecdd;
    @XmlElement(name = "zzssyyybnsr_zzsysfpdkqd", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_zzsysfpdkqd")
    protected ZzssyyybnsrZzsysfpdkqd zzssyyybnsrZzsysfpdkqd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Hgwspzdklsjcjb hgwspzdklsjcjb;
    @XmlElement(name = "zzsybnsrsb_ncphdkczzsjxsejsb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_ncphdkczzsjxsejsb")
    protected ZzsybnsrsbNcphdkczzsjxsejsb zzsybnsrsbNcphdkczzsjxsejsb;
    @XmlElement(name = "zzssyyybnsr_hqysqyfzjgcdd", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_hqysqyfzjgcdd")
    protected ZzssyyybnsrHqysqyfzjgcdd zzssyyybnsrHqysqyfzjgcdd;
    @XmlElement(name = "zzsybnsrsb_cbfhdccpzzsjxsejsb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_cbfhdccpzzsjxsejsb")
    protected ZzsybnsrsbCbfhdccpzzsjxsejsb zzsybnsrsbCbfhdccpzzsjxsejsb;
    @XmlElement(name = "zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb")
    protected ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb zzssyyybnsrGjncpzjxshdncpzzsjxsejsb;
    @XmlElement(name = "zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb")
    protected ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb;
    @XmlElement(name = "zzsybnsrsb_jyzyxsyphzb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_jyzyxsyphzb")
    protected ZzsybnsrsbJyzyxsyphzb zzsybnsrsbJyzyxsyphzb;
    @XmlElement(name = "zzssyyybnsr_jyzyfjyxxmxb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_jyzyfjyxxmxb")
    protected ZzssyyybnsrJyzyfjyxxmxb zzssyyybnsrJyzyfjyxxmxb;
    @XmlElement(name = "zzsybnsrsb_dkdjsstyjksdkqd", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_dkdjsstyjksdkqd")
    protected ZzsybnsrsbDkdjsstyjksdkqd zzsybnsrsbDkdjsstyjksdkqd;
    @XmlElement(name = "zzssyyybnsr_hznsqytycdd", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_hznsqytycdd")
    protected ZzssyyybnsrHznsqytycdd zzssyyybnsrHznsqytycdd;
    @XmlElement(name = "zzssyyybnsr_hznsqyzzsfpb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_hznsqyzzsfpb")
    protected ZzssyyybnsrHznsqyzzsfpb zzssyyybnsrHznsqyzzsfpb;
    @XmlElement(name = "zzssyyybnsr_kstlqyzzsfpb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_kstlqyzzsfpb")
    protected ZzssyyybnsrKstlqyzzsfpb zzssyyybnsrKstlqyzzsfpb;
    @XmlElement(name = "zzsybnsrsb_yzqyfzjgzzshznsxxcdd", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_yzqyfzjgzzshznsxxcdd")
    protected ZzsybnsrsbYzqyfzjgzzshznsxxcdd zzsybnsrsbYzqyfzjgzzshznsxxcdd;
    @XmlElement(name = "zzsybnsrsb_tlysqyfzjgzzshznsxxcdd", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_tlysqyfzjgzzshznsxxcdd")
    protected ZzsybnsrsbTlysqyfzjgzzshznsxxcdd zzsybnsrsbTlysqyfzjgzzshznsxxcdd;
    @XmlElement(name = "zzsybnsrsb_dxqyfzjgzzshznsxxcdd", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzsybnsrsb_dxqyfzjgzzshznsxxcdd")
    protected ZzsybnsrsbDxqyfzjgzzshznsxxcdd zzsybnsrsbDxqyfzjgzzshznsxxcdd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Zzsfjssb zzsfjssb;
    @XmlElement(name = "zzssyyybnsr_bfcpxstjb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_bfcpxstjb")
    protected ZzssyyybnsrBfcpxstjb zzssyyybnsrBfcpxstjb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Zzsjmssbmxb zzsjmssbmxb;
    @XmlElement(name = "zzssyyybnsr_yqtqyzzsfpb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_yqtqyzzsfpb")
    protected ZzssyyybnsrYqtqyzzsfpb zzssyyybnsrYqtqyzzsfpb;
    @XmlElement(name = "zzssyyybnsr_bqdkjxsejgmxb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_bqdkjxsejgmxb")
    protected ZzssyyybnsrBqdkjxsejgmxb zzssyyybnsrBqdkjxsejgmxb;
    @XmlElement(name = "zzssyyybnsr_tljsjjnssbb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_tljsjjnssbb")
    protected ZzssyyybnsrTljsjjnssbb zzssyyybnsrTljsjjnssbb;
    @XmlElement(name = "zzssyyybnsr_ygzsffxcsmxb", namespace = "http://www.chinatax.gov.cn/dataspec/")
    @JsonProperty(value = "zzssyyybnsr_ygzsffxcsmxb")
    protected ZzssyyybnsrYgzsffxcsmxb zzssyyybnsrYgzsffxcsmxb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected SBZzsYbnsrLdtsbhqkVO sbZzsYbnsrLdtsbhqkVO;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected SbZzsJcdlqyWgxpTzMxb sbZzsJcdlqyWgxpTzMxb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected SBZzsXzjjdjzcJgVO sbZzsXzjjdjzcJg;

    /**
     * Gets the value of the zzssyyybnsrZb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrZb }
     *     
     */
    public ZzssyyybnsrZb getZzssyyybnsrZb() {
        return zzssyyybnsrZb;
    }

    /**
     * Sets the value of the zzssyyybnsrZb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrZb }
     *     
     */
    public void setZzssyyybnsrZb(ZzssyyybnsrZb value) {
        this.zzssyyybnsrZb = value;
    }

    /**
     * Gets the value of the zzssyyybnsr01Bqxsqkmxb property.
     * 
     * @return
     *     possible object is
     *     {@link Zzssyyybnsr01Bqxsqkmxb }
     *     
     */
    public Zzssyyybnsr01Bqxsqkmxb getZzssyyybnsr01Bqxsqkmxb() {
        return zzssyyybnsr01Bqxsqkmxb;
    }

    /**
     * Sets the value of the zzssyyybnsr01Bqxsqkmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link Zzssyyybnsr01Bqxsqkmxb }
     *     
     */
    public void setZzssyyybnsr01Bqxsqkmxb(Zzssyyybnsr01Bqxsqkmxb value) {
        this.zzssyyybnsr01Bqxsqkmxb = value;
    }

    /**
     * Gets the value of the zzssyyybnsr02Bqjxsemxb property.
     * 
     * @return
     *     possible object is
     *     {@link Zzssyyybnsr02Bqjxsemxb }
     *     
     */
    public Zzssyyybnsr02Bqjxsemxb getZzssyyybnsr02Bqjxsemxb() {
        return zzssyyybnsr02Bqjxsemxb;
    }

    /**
     * Sets the value of the zzssyyybnsr02Bqjxsemxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link Zzssyyybnsr02Bqjxsemxb }
     *     
     */
    public void setZzssyyybnsr02Bqjxsemxb(Zzssyyybnsr02Bqjxsemxb value) {
        this.zzssyyybnsr02Bqjxsemxb = value;
    }

    /**
     * Gets the value of the zzssyyybnsr03Ysfwkcxmmx property.
     * 
     * @return
     *     possible object is
     *     {@link Zzssyyybnsr03Ysfwkcxmmx }
     *     
     */
    public Zzssyyybnsr03Ysfwkcxmmx getZzssyyybnsr03Ysfwkcxmmx() {
        return zzssyyybnsr03Ysfwkcxmmx;
    }

    /**
     * Sets the value of the zzssyyybnsr03Ysfwkcxmmx property.
     * 
     * @param value
     *     allowed object is
     *     {@link Zzssyyybnsr03Ysfwkcxmmx }
     *     
     */
    public void setZzssyyybnsr03Ysfwkcxmmx(Zzssyyybnsr03Ysfwkcxmmx value) {
        this.zzssyyybnsr03Ysfwkcxmmx = value;
    }

    /**
     * Gets the value of the zzssyyybnsr04Bqjxsemxb property.
     * 
     * @return
     *     possible object is
     *     {@link Zzssyyybnsr04Bqjxsemxb }
     *     
     */
    public Zzssyyybnsr04Bqjxsemxb getZzssyyybnsr04Bqjxsemxb() {
        return zzssyyybnsr04Bqjxsemxb;
    }

    /**
     * Sets the value of the zzssyyybnsr04Bqjxsemxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link Zzssyyybnsr04Bqjxsemxb }
     *     
     */
    public void setZzssyyybnsr04Bqjxsemxb(Zzssyyybnsr04Bqjxsemxb value) {
        this.zzssyyybnsr04Bqjxsemxb = value;
    }

    /**
     * Gets the value of the zzssyyybnsr05Bdcfqdkjsb property.
     * 
     * @return
     *     possible object is
     *     {@link Zzssyyybnsr05Bdcfqdkjsb }
     *     
     */
    public Zzssyyybnsr05Bdcfqdkjsb getZzssyyybnsr05Bdcfqdkjsb() {
        return zzssyyybnsr05Bdcfqdkjsb;
    }

    /**
     * Sets the value of the zzssyyybnsr05Bdcfqdkjsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link Zzssyyybnsr05Bdcfqdkjsb }
     *     
     */
    public void setZzssyyybnsr05Bdcfqdkjsb(Zzssyyybnsr05Bdcfqdkjsb value) {
        this.zzssyyybnsr05Bdcfqdkjsb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrZzsybnsrbqynseayskmfjb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrZzsybnsrbqynseayskmfjb }
     *     
     */
    public ZzssyyybnsrZzsybnsrbqynseayskmfjb getZzssyyybnsrZzsybnsrbqynseayskmfjb() {
        return zzssyyybnsrZzsybnsrbqynseayskmfjb;
    }

    /**
     * Sets the value of the zzssyyybnsrZzsybnsrbqynseayskmfjb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrZzsybnsrbqynseayskmfjb }
     *     
     */
    public void setZzssyyybnsrZzsybnsrbqynseayskmfjb(ZzssyyybnsrZzsybnsrbqynseayskmfjb value) {
        this.zzssyyybnsrZzsybnsrbqynseayskmfjb = value;
    }

    /**
     * Gets the value of the zzsybnsrsbGdzcjxsedkqkb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbGdzcjxsedkqkb }
     *     
     */
    public ZzsybnsrsbGdzcjxsedkqkb getZzsybnsrsbGdzcjxsedkqkb() {
        return zzsybnsrsbGdzcjxsedkqkb;
    }

    /**
     * Sets the value of the zzsybnsrsbGdzcjxsedkqkb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbGdzcjxsedkqkb }
     *     
     */
    public void setZzsybnsrsbGdzcjxsedkqkb(ZzsybnsrsbGdzcjxsedkqkb value) {
        this.zzsybnsrsbGdzcjxsedkqkb = value;
    }

    /**
     * Gets the value of the hqysqyfzjgcdd property.
     * 
     * @return
     *     possible object is
     *     {@link Hqysqyfzjgcdd }
     *     
     */
    public Hqysqyfzjgcdd getHqysqyfzjgcdd() {
        return hqysqyfzjgcdd;
    }

    /**
     * Sets the value of the hqysqyfzjgcdd property.
     * 
     * @param value
     *     allowed object is
     *     {@link Hqysqyfzjgcdd }
     *     
     */
    public void setHqysqyfzjgcdd(Hqysqyfzjgcdd value) {
        this.hqysqyfzjgcdd = value;
    }

    /**
     * Gets the value of the zzsybnsrsbNdhkysqyqsb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbNdhkysqyqsb }
     *     
     */
    public ZzsybnsrsbNdhkysqyqsb getZzsybnsrsbNdhkysqyqsb() {
        return zzsybnsrsbNdhkysqyqsb;
    }

    /**
     * Sets the value of the zzsybnsrsbNdhkysqyqsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbNdhkysqyqsb }
     *     
     */
    public void setZzsybnsrsbNdhkysqyqsb(ZzsybnsrsbNdhkysqyqsb value) {
        this.zzsybnsrsbNdhkysqyqsb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrCpygxcqkmxb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrCpygxcqkmxb }
     *     
     */
    public ZzssyyybnsrCpygxcqkmxb getZzssyyybnsrCpygxcqkmxb() {
        return zzssyyybnsrCpygxcqkmxb;
    }

    /**
     * Sets the value of the zzssyyybnsrCpygxcqkmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrCpygxcqkmxb }
     *     
     */
    public void setZzssyyybnsrCpygxcqkmxb(ZzssyyybnsrCpygxcqkmxb value) {
        this.zzssyyybnsrCpygxcqkmxb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrCpygxcslmxb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrCpygxcslmxb }
     *     
     */
    public ZzssyyybnsrCpygxcslmxb getZzssyyybnsrCpygxcslmxb() {
        return zzssyyybnsrCpygxcslmxb;
    }

    /**
     * Sets the value of the zzssyyybnsrCpygxcslmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrCpygxcslmxb }
     *     
     */
    public void setZzssyyybnsrCpygxcslmxb(ZzssyyybnsrCpygxcslmxb value) {
        this.zzssyyybnsrCpygxcslmxb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrJdclscqyxsmxb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrJdclscqyxsmxb }
     *     
     */
    public ZzssyyybnsrJdclscqyxsmxb getZzssyyybnsrJdclscqyxsmxb() {
        return zzssyyybnsrJdclscqyxsmxb;
    }

    /**
     * Sets the value of the zzssyyybnsrJdclscqyxsmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrJdclscqyxsmxb }
     *     
     */
    public void setZzssyyybnsrJdclscqyxsmxb(ZzssyyybnsrJdclscqyxsmxb value) {
        this.zzssyyybnsrJdclscqyxsmxb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrJdclscqyxsqktjb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrJdclscqyxsqktjb }
     *     
     */
    public ZzssyyybnsrJdclscqyxsqktjb getZzssyyybnsrJdclscqyxsqktjb() {
        return zzssyyybnsrJdclscqyxsqktjb;
    }

    /**
     * Sets the value of the zzssyyybnsrJdclscqyxsqktjb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrJdclscqyxsqktjb }
     *     
     */
    public void setZzssyyybnsrJdclscqyxsqktjb(ZzssyyybnsrJdclscqyxsqktjb value) {
        this.zzssyyybnsrJdclscqyxsqktjb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrJdcxstyfplycybb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrJdcxstyfplycybb }
     *     
     */
    public ZzssyyybnsrJdcxstyfplycybb getZzssyyybnsrJdcxstyfplycybb() {
        return zzssyyybnsrJdcxstyfplycybb;
    }

    /**
     * Sets the value of the zzssyyybnsrJdcxstyfplycybb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrJdcxstyfplycybb }
     *     
     */
    public void setZzssyyybnsrJdcxstyfplycybb(ZzssyyybnsrJdcxstyfplycybb value) {
        this.zzssyyybnsrJdcxstyfplycybb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrJdcxstyfpqd property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrJdcxstyfpqd }
     *     
     */
    public ZzssyyybnsrJdcxstyfpqd getZzssyyybnsrJdcxstyfpqd() {
        return zzssyyybnsrJdcxstyfpqd;
    }

    /**
     * Sets the value of the zzssyyybnsrJdcxstyfpqd property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrJdcxstyfpqd }
     *     
     */
    public void setZzssyyybnsrJdcxstyfpqd(ZzssyyybnsrJdcxstyfpqd value) {
        this.zzssyyybnsrJdcxstyfpqd = value;
    }

    /**
     * Gets the value of the zzssyyybnsrJdcljxqyxsmxb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrJdcljxqyxsmxb }
     *     
     */
    public ZzssyyybnsrJdcljxqyxsmxb getZzssyyybnsrJdcljxqyxsmxb() {
        return zzssyyybnsrJdcljxqyxsmxb;
    }

    /**
     * Sets the value of the zzssyyybnsrJdcljxqyxsmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrJdcljxqyxsmxb }
     *     
     */
    public void setZzssyyybnsrJdcljxqyxsmxb(ZzssyyybnsrJdcljxqyxsmxb value) {
        this.zzssyyybnsrJdcljxqyxsmxb = value;
    }

    /**
     * Gets the value of the dlqyzzsxxsehjxsecdd property.
     * 
     * @return
     *     possible object is
     *     {@link Dlqyzzsxxsehjxsecdd }
     *     
     */
    public Dlqyzzsxxsehjxsecdd getDlqyzzsxxsehjxsecdd() {
        return dlqyzzsxxsehjxsecdd;
    }

    /**
     * Sets the value of the dlqyzzsxxsehjxsecdd property.
     * 
     * @param value
     *     allowed object is
     *     {@link Dlqyzzsxxsehjxsecdd }
     *     
     */
    public void setDlqyzzsxxsehjxsecdd(Dlqyzzsxxsehjxsecdd value) {
        this.dlqyzzsxxsehjxsecdd = value;
    }

    /**
     * Gets the value of the zzssyyybnsrZzsysfpdkqd property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrZzsysfpdkqd }
     *     
     */
    public ZzssyyybnsrZzsysfpdkqd getZzssyyybnsrZzsysfpdkqd() {
        return zzssyyybnsrZzsysfpdkqd;
    }

    /**
     * Sets the value of the zzssyyybnsrZzsysfpdkqd property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrZzsysfpdkqd }
     *     
     */
    public void setZzssyyybnsrZzsysfpdkqd(ZzssyyybnsrZzsysfpdkqd value) {
        this.zzssyyybnsrZzsysfpdkqd = value;
    }

    /**
     * Gets the value of the hgwspzdklsjcjb property.
     * 
     * @return
     *     possible object is
     *     {@link Hgwspzdklsjcjb }
     *     
     */
    public Hgwspzdklsjcjb getHgwspzdklsjcjb() {
        return hgwspzdklsjcjb;
    }

    /**
     * Sets the value of the hgwspzdklsjcjb property.
     * 
     * @param value
     *     allowed object is
     *     {@link Hgwspzdklsjcjb }
     *     
     */
    public void setHgwspzdklsjcjb(Hgwspzdklsjcjb value) {
        this.hgwspzdklsjcjb = value;
    }

    /**
     * Gets the value of the zzsybnsrsbNcphdkczzsjxsejsb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbNcphdkczzsjxsejsb }
     *     
     */
    public ZzsybnsrsbNcphdkczzsjxsejsb getZzsybnsrsbNcphdkczzsjxsejsb() {
        return zzsybnsrsbNcphdkczzsjxsejsb;
    }

    /**
     * Sets the value of the zzsybnsrsbNcphdkczzsjxsejsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbNcphdkczzsjxsejsb }
     *     
     */
    public void setZzsybnsrsbNcphdkczzsjxsejsb(ZzsybnsrsbNcphdkczzsjxsejsb value) {
        this.zzsybnsrsbNcphdkczzsjxsejsb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrHqysqyfzjgcdd property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrHqysqyfzjgcdd }
     *     
     */
    public ZzssyyybnsrHqysqyfzjgcdd getZzssyyybnsrHqysqyfzjgcdd() {
        return zzssyyybnsrHqysqyfzjgcdd;
    }

    /**
     * Sets the value of the zzssyyybnsrHqysqyfzjgcdd property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrHqysqyfzjgcdd }
     *     
     */
    public void setZzssyyybnsrHqysqyfzjgcdd(ZzssyyybnsrHqysqyfzjgcdd value) {
        this.zzssyyybnsrHqysqyfzjgcdd = value;
    }

    /**
     * Gets the value of the zzsybnsrsbCbfhdccpzzsjxsejsb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbCbfhdccpzzsjxsejsb }
     *     
     */
    public ZzsybnsrsbCbfhdccpzzsjxsejsb getZzsybnsrsbCbfhdccpzzsjxsejsb() {
        return zzsybnsrsbCbfhdccpzzsjxsejsb;
    }

    /**
     * Sets the value of the zzsybnsrsbCbfhdccpzzsjxsejsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbCbfhdccpzzsjxsejsb }
     *     
     */
    public void setZzsybnsrsbCbfhdccpzzsjxsejsb(ZzsybnsrsbCbfhdccpzzsjxsejsb value) {
        this.zzsybnsrsbCbfhdccpzzsjxsejsb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrGjncpzjxshdncpzzsjxsejsb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb }
     *     
     */
    public ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb getZzssyyybnsrGjncpzjxshdncpzzsjxsejsb() {
        return zzssyyybnsrGjncpzjxshdncpzzsjxsejsb;
    }

    /**
     * Sets the value of the zzssyyybnsrGjncpzjxshdncpzzsjxsejsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb }
     *     
     */
    public void setZzssyyybnsrGjncpzjxshdncpzzsjxsejsb(ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb value) {
        this.zzssyyybnsrGjncpzjxshdncpzzsjxsejsb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb }
     *     
     */
    public ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb getZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb() {
        return zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb;
    }

    /**
     * Sets the value of the zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb }
     *     
     */
    public void setZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb(ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb value) {
        this.zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb = value;
    }

    /**
     * Gets the value of the zzsybnsrsbJyzyxsyphzb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbJyzyxsyphzb }
     *     
     */
    public ZzsybnsrsbJyzyxsyphzb getZzsybnsrsbJyzyxsyphzb() {
        return zzsybnsrsbJyzyxsyphzb;
    }

    /**
     * Sets the value of the zzsybnsrsbJyzyxsyphzb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbJyzyxsyphzb }
     *     
     */
    public void setZzsybnsrsbJyzyxsyphzb(ZzsybnsrsbJyzyxsyphzb value) {
        this.zzsybnsrsbJyzyxsyphzb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrJyzyfjyxxmxb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrJyzyfjyxxmxb }
     *     
     */
    public ZzssyyybnsrJyzyfjyxxmxb getZzssyyybnsrJyzyfjyxxmxb() {
        return zzssyyybnsrJyzyfjyxxmxb;
    }

    /**
     * Sets the value of the zzssyyybnsrJyzyfjyxxmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrJyzyfjyxxmxb }
     *     
     */
    public void setZzssyyybnsrJyzyfjyxxmxb(ZzssyyybnsrJyzyfjyxxmxb value) {
        this.zzssyyybnsrJyzyfjyxxmxb = value;
    }

    /**
     * Gets the value of the zzsybnsrsbDkdjsstyjksdkqd property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbDkdjsstyjksdkqd }
     *     
     */
    public ZzsybnsrsbDkdjsstyjksdkqd getZzsybnsrsbDkdjsstyjksdkqd() {
        return zzsybnsrsbDkdjsstyjksdkqd;
    }

    /**
     * Sets the value of the zzsybnsrsbDkdjsstyjksdkqd property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbDkdjsstyjksdkqd }
     *     
     */
    public void setZzsybnsrsbDkdjsstyjksdkqd(ZzsybnsrsbDkdjsstyjksdkqd value) {
        this.zzsybnsrsbDkdjsstyjksdkqd = value;
    }

    /**
     * Gets the value of the zzssyyybnsrHznsqytycdd property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrHznsqytycdd }
     *     
     */
    public ZzssyyybnsrHznsqytycdd getZzssyyybnsrHznsqytycdd() {
        return zzssyyybnsrHznsqytycdd;
    }

    /**
     * Sets the value of the zzssyyybnsrHznsqytycdd property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrHznsqytycdd }
     *     
     */
    public void setZzssyyybnsrHznsqytycdd(ZzssyyybnsrHznsqytycdd value) {
        this.zzssyyybnsrHznsqytycdd = value;
    }

    /**
     * Gets the value of the zzssyyybnsrHznsqyzzsfpb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrHznsqyzzsfpb }
     *     
     */
    public ZzssyyybnsrHznsqyzzsfpb getZzssyyybnsrHznsqyzzsfpb() {
        return zzssyyybnsrHznsqyzzsfpb;
    }

    /**
     * Sets the value of the zzssyyybnsrHznsqyzzsfpb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrHznsqyzzsfpb }
     *     
     */
    public void setZzssyyybnsrHznsqyzzsfpb(ZzssyyybnsrHznsqyzzsfpb value) {
        this.zzssyyybnsrHznsqyzzsfpb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrKstlqyzzsfpb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrKstlqyzzsfpb }
     *     
     */
    public ZzssyyybnsrKstlqyzzsfpb getZzssyyybnsrKstlqyzzsfpb() {
        return zzssyyybnsrKstlqyzzsfpb;
    }

    /**
     * Sets the value of the zzssyyybnsrKstlqyzzsfpb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrKstlqyzzsfpb }
     *     
     */
    public void setZzssyyybnsrKstlqyzzsfpb(ZzssyyybnsrKstlqyzzsfpb value) {
        this.zzssyyybnsrKstlqyzzsfpb = value;
    }

    /**
     * Gets the value of the zzsybnsrsbYzqyfzjgzzshznsxxcdd property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbYzqyfzjgzzshznsxxcdd }
     *     
     */
    public ZzsybnsrsbYzqyfzjgzzshznsxxcdd getZzsybnsrsbYzqyfzjgzzshznsxxcdd() {
        return zzsybnsrsbYzqyfzjgzzshznsxxcdd;
    }

    /**
     * Sets the value of the zzsybnsrsbYzqyfzjgzzshznsxxcdd property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbYzqyfzjgzzshznsxxcdd }
     *     
     */
    public void setZzsybnsrsbYzqyfzjgzzshznsxxcdd(ZzsybnsrsbYzqyfzjgzzshznsxxcdd value) {
        this.zzsybnsrsbYzqyfzjgzzshznsxxcdd = value;
    }

    /**
     * Gets the value of the zzsybnsrsbTlysqyfzjgzzshznsxxcdd property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbTlysqyfzjgzzshznsxxcdd }
     *     
     */
    public ZzsybnsrsbTlysqyfzjgzzshznsxxcdd getZzsybnsrsbTlysqyfzjgzzshznsxxcdd() {
        return zzsybnsrsbTlysqyfzjgzzshznsxxcdd;
    }

    /**
     * Sets the value of the zzsybnsrsbTlysqyfzjgzzshznsxxcdd property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbTlysqyfzjgzzshznsxxcdd }
     *     
     */
    public void setZzsybnsrsbTlysqyfzjgzzshznsxxcdd(ZzsybnsrsbTlysqyfzjgzzshznsxxcdd value) {
        this.zzsybnsrsbTlysqyfzjgzzshznsxxcdd = value;
    }

    /**
     * Gets the value of the zzsybnsrsbDxqyfzjgzzshznsxxcdd property.
     * 
     * @return
     *     possible object is
     *     {@link ZzsybnsrsbDxqyfzjgzzshznsxxcdd }
     *     
     */
    public ZzsybnsrsbDxqyfzjgzzshznsxxcdd getZzsybnsrsbDxqyfzjgzzshznsxxcdd() {
        return zzsybnsrsbDxqyfzjgzzshznsxxcdd;
    }

    /**
     * Sets the value of the zzsybnsrsbDxqyfzjgzzshznsxxcdd property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzsybnsrsbDxqyfzjgzzshznsxxcdd }
     *     
     */
    public void setZzsybnsrsbDxqyfzjgzzshznsxxcdd(ZzsybnsrsbDxqyfzjgzzshznsxxcdd value) {
        this.zzsybnsrsbDxqyfzjgzzshznsxxcdd = value;
    }

    /**
     * Gets the value of the zzsfjssb property.
     * 
     * @return
     *     possible object is
     *     {@link Zzsfjssb }
     *     
     */
    public Zzsfjssb getZzsfjssb() {
        return zzsfjssb;
    }

    /**
     * Sets the value of the zzsfjssb property.
     * 
     * @param value
     *     allowed object is
     *     {@link Zzsfjssb }
     *     
     */
    public void setZzsfjssb(Zzsfjssb value) {
        this.zzsfjssb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrBfcpxstjb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrBfcpxstjb }
     *     
     */
    public ZzssyyybnsrBfcpxstjb getZzssyyybnsrBfcpxstjb() {
        return zzssyyybnsrBfcpxstjb;
    }

    /**
     * Sets the value of the zzssyyybnsrBfcpxstjb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrBfcpxstjb }
     *     
     */
    public void setZzssyyybnsrBfcpxstjb(ZzssyyybnsrBfcpxstjb value) {
        this.zzssyyybnsrBfcpxstjb = value;
    }

    /**
     * Gets the value of the zzsjmssbmxb property.
     * 
     * @return
     *     possible object is
     *     {@link Zzsjmssbmxb }
     *     
     */
    public Zzsjmssbmxb getZzsjmssbmxb() {
        return zzsjmssbmxb;
    }

    /**
     * Sets the value of the zzsjmssbmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link Zzsjmssbmxb }
     *     
     */
    public void setZzsjmssbmxb(Zzsjmssbmxb value) {
        this.zzsjmssbmxb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrYqtqyzzsfpb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrYqtqyzzsfpb }
     *     
     */
    public ZzssyyybnsrYqtqyzzsfpb getZzssyyybnsrYqtqyzzsfpb() {
        return zzssyyybnsrYqtqyzzsfpb;
    }

    /**
     * Sets the value of the zzssyyybnsrYqtqyzzsfpb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrYqtqyzzsfpb }
     *     
     */
    public void setZzssyyybnsrYqtqyzzsfpb(ZzssyyybnsrYqtqyzzsfpb value) {
        this.zzssyyybnsrYqtqyzzsfpb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrBqdkjxsejgmxb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrBqdkjxsejgmxb }
     *     
     */
    public ZzssyyybnsrBqdkjxsejgmxb getZzssyyybnsrBqdkjxsejgmxb() {
        return zzssyyybnsrBqdkjxsejgmxb;
    }

    /**
     * Sets the value of the zzssyyybnsrBqdkjxsejgmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrBqdkjxsejgmxb }
     *     
     */
    public void setZzssyyybnsrBqdkjxsejgmxb(ZzssyyybnsrBqdkjxsejgmxb value) {
        this.zzssyyybnsrBqdkjxsejgmxb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrTljsjjnssbb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrTljsjjnssbb }
     *     
     */
    public ZzssyyybnsrTljsjjnssbb getZzssyyybnsrTljsjjnssbb() {
        return zzssyyybnsrTljsjjnssbb;
    }

    /**
     * Sets the value of the zzssyyybnsrTljsjjnssbb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrTljsjjnssbb }
     *     
     */
    public void setZzssyyybnsrTljsjjnssbb(ZzssyyybnsrTljsjjnssbb value) {
        this.zzssyyybnsrTljsjjnssbb = value;
    }

    /**
     * Gets the value of the zzssyyybnsrYgzsffxcsmxb property.
     * 
     * @return
     *     possible object is
     *     {@link ZzssyyybnsrYgzsffxcsmxb }
     *     
     */
    public ZzssyyybnsrYgzsffxcsmxb getZzssyyybnsrYgzsffxcsmxb() {
        return zzssyyybnsrYgzsffxcsmxb;
    }

    /**
     * Sets the value of the zzssyyybnsrYgzsffxcsmxb property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZzssyyybnsrYgzsffxcsmxb }
     *     
     */
    public void setZzssyyybnsrYgzsffxcsmxb(ZzssyyybnsrYgzsffxcsmxb value) {
        this.zzssyyybnsrYgzsffxcsmxb = value;
    }

    /**
     * Gets the value of the sbZzsYbnsrLdtsbhqkVO property.
     *
     * @return
     *     possible object is
     *     {@link SBZzsYbnsrLdtsbhqkVO }
     *
     */
    public SBZzsYbnsrLdtsbhqkVO getSbZzsYbnsrLdtsbhqkVO() {
        return sbZzsYbnsrLdtsbhqkVO;
    }

    /**
     * Sets the value of the sbZzsYbnsrLdtsbhqkVO property.
     *
     * @param value
     *     allowed object is
     *     {@link SBZzsYbnsrLdtsbhqkVO }
     *
     */
    public void setSbZzsYbnsrLdtsbhqkVO(SBZzsYbnsrLdtsbhqkVO value) {
        this.sbZzsYbnsrLdtsbhqkVO = value;
    }

    /**
     * Gets the value of the sbZzsJcdlqyWgxpTzMxb property.
     *
     * @return
     *     possible object is
     *     {@link SbZzsJcdlqyWgxpTzMxb }
     *
     */
    public SbZzsJcdlqyWgxpTzMxb getSbZzsJcdlqyWgxpTzMxb() {
        return sbZzsJcdlqyWgxpTzMxb;
    }

    /**
     * Sets the value of the sbZzsJcdlqyWgxpTzMxb property.
     *
     * @param value
     *     allowed object is
     *     {@link SbZzsJcdlqyWgxpTzMxb }
     *
     */
    public void setSbZzsJcdlqyWgxpTzMxb(SbZzsJcdlqyWgxpTzMxb value) {
        this.sbZzsJcdlqyWgxpTzMxb = value;
    }

    /**
     * Gets the value of the sbZzsXzjjdjzcJg property.
     *
     * @return
     *     possible object is
     *     {@link SBZzsXzjjdjzcJgVO }
     *
     */
    public SBZzsXzjjdjzcJgVO getSbZzsXzjjdjzcJg() {
        return sbZzsXzjjdjzcJg;
    }

    /**
     * Sets the value of the sbZzsXzjjdjzcJg property.
     *
     * @param value
     *     allowed object is
     *     {@link SBZzsXzjjdjzcJgVO }
     *
     */
    public void setSbZzsXzjjdjzcJg(SBZzsXzjjdjzcJgVO value) {
        this.sbZzsXzjjdjzcJg = value;
    }
}