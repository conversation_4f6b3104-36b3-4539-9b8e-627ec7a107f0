
package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 申报纳税人申报数据信息对象
 * 
 * <p>Java class for SBSbxxJhVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBSbxxJhVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="zsxmDm" type="{http://www.chinatax.gov.cn/dataspec/}zsxmDm" minOccurs="0"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm" minOccurs="0"/>
 *         &lt;element name="zszmDm" type="{http://www.chinatax.gov.cn/dataspec/}zszmDm" minOccurs="0"/>
 *         &lt;element name="hyDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm" minOccurs="0"/>
 *         &lt;element name="nsqxDm" type="{http://www.chinatax.gov.cn/dataspec/}nsqxDm" minOccurs="0"/>
 *         &lt;element name="zsdlfsDm" type="{http://www.chinatax.gov.cn/dataspec/}zsdlfsDm" minOccurs="0"/>
 *         &lt;element name="jkqxDm" type="{http://www.chinatax.gov.cn/dataspec/}jkqxDm" minOccurs="0"/>
 *         &lt;element name="sbqxDm" type="{http://www.chinatax.gov.cn/dataspec/}sbqxDm" minOccurs="0"/>
 *         &lt;element name="jkqx" type="{http://www.chinatax.gov.cn/dataspec/}jkqx" minOccurs="0"/>
 *         &lt;element name="ssyhlxDm" type="{http://www.chinatax.gov.cn/dataspec/}ssyhlxDm" minOccurs="0"/>
 *         &lt;element name="yjze" type="{http://www.chinatax.gov.cn/dataspec/}yjze" minOccurs="0"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq" minOccurs="0"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz" minOccurs="0"/>
 *         &lt;element name="djzclxDm" type="{http://www.chinatax.gov.cn/dataspec/}djzclxDm" minOccurs="0"/>
 *         &lt;element name="dwlsgxDm" type="{http://www.chinatax.gov.cn/dataspec/}dwlsgxDm" minOccurs="0"/>
 *         &lt;element name="zsfsDm" type="{http://www.chinatax.gov.cn/dataspec/}zsfsDm" minOccurs="0"/>
 *         &lt;element name="xssr" type="{http://www.chinatax.gov.cn/dataspec/}xssr" minOccurs="0"/>
 *         &lt;element name="yxssr" type="{http://www.chinatax.gov.cn/dataspec/}xssr" minOccurs="0"/>
 *         &lt;element name="jsyj" type="{http://www.chinatax.gov.cn/dataspec/}jsyj" minOccurs="0"/>
 *         &lt;element name="sbqx" type="{http://www.chinatax.gov.cn/dataspec/}sbqx" minOccurs="0"/>
 *         &lt;element name="sl1" type="{http://www.chinatax.gov.cn/dataspec/}sl1" minOccurs="0"/>
 *         &lt;element name="zsl" type="{http://www.chinatax.gov.cn/dataspec/}zsl" minOccurs="0"/>
 *         &lt;element name="se" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="ssglyDm" type="{http://www.chinatax.gov.cn/dataspec/}ssglyDm" minOccurs="0"/>
 *         &lt;element name="skssswjgDm" type="{http://www.chinatax.gov.cn/dataspec/}skssswjgDm" minOccurs="0"/>
 *         &lt;element name="zgswskfjDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswskfjDm" minOccurs="0"/>
 *         &lt;element name="swjgDm" type="{http://www.chinatax.gov.cn/dataspec/}swjgDm" minOccurs="0"/>
 *         &lt;element name="yjse1" type="{http://www.chinatax.gov.cn/dataspec/}yjse1" minOccurs="0"/>
 *         &lt;element name="jmse" type="{http://www.chinatax.gov.cn/dataspec/}jmse" minOccurs="0"/>
 *         &lt;element name="yjse" type="{http://www.chinatax.gov.cn/dataspec/}yjse" minOccurs="0"/>
 *         &lt;element name="ynse" type="{http://www.chinatax.gov.cn/dataspec/}ynse" minOccurs="0"/>
 *         &lt;element name="hdynsjye" type="{http://www.chinatax.gov.cn/dataspec/}hdynsjye" minOccurs="0"/>
 *         &lt;element name="yhdynsjye" type="{http://www.chinatax.gov.cn/dataspec/}hdynsjye" minOccurs="0"/>
 *         &lt;element name="hztykcjye" type="{http://www.chinatax.gov.cn/dataspec/}hztykcjye" minOccurs="0"/>
 *         &lt;element name="sybh" type="{http://www.chinatax.gov.cn/dataspec/}sybh" minOccurs="0"/>
 *         &lt;element name="kjdjxh" type="{http://www.chinatax.gov.cn/dataspec/}kjdjxh" minOccurs="0"/>
 *         &lt;element name="rdzsuuid" type="{http://www.chinatax.gov.cn/dataspec/}rdzsuuid" minOccurs="0"/>
 *         &lt;element name="rdpzuuid" type="{http://www.chinatax.gov.cn/dataspec/}rdpzuuid" minOccurs="0"/>
 *         &lt;element name="zfsbz" type="{http://www.chinatax.gov.cn/dataspec/}zfsbz" minOccurs="0"/>
 *         &lt;element name="yslwqzd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="xshwqzd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}jdxzDm" minOccurs="0"/>
 *         &lt;element name="hdbl" type="{http://www.chinatax.gov.cn/dataspec/}hdbl" minOccurs="0"/>
 *         &lt;element name="hdse" type="{http://www.chinatax.gov.cn/dataspec/}hdse" minOccurs="0"/>
 *         &lt;element name="zhjzl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="ysbje" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="bqkdkse" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="wdqzdbz" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="jsbz1" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="sfysb" type="{http://www.chinatax.gov.cn/dataspec/}bz"/>
 *         &lt;element name="rdyxqq" type="{http://www.chinatax.gov.cn/dataspec/}rdyxqq" minOccurs="0"/>
 *         &lt;element name="rdyxqz" type="{http://www.chinatax.gov.cn/dataspec/}rdyxqz" minOccurs="0"/>
 *         &lt;element name="dexmDm" type="{http://www.chinatax.gov.cn/dataspec/}dexmDm" minOccurs="0"/>
 *         &lt;element name="wzsyhdjsyj" type="{http://www.chinatax.gov.cn/dataspec/}jsyj" minOccurs="0"/>
 *         &lt;element name="qzd" type="{http://www.chinatax.gov.cn/dataspec/}qzd" minOccurs="0"/>
 *         &lt;element name="lsbbz" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="dfpskje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh" minOccurs="0"/>
 *         &lt;element name="yzpzzlDm" type="{http://www.chinatax.gov.cn/dataspec/}yzpzzlDm" minOccurs="0"/>
 *         &lt;element name="jzjtskbz" type="{http://www.chinatax.gov.cn/dataspec/}jzjtskbz" minOccurs="0"/>
 *         &lt;element name="zcdsyyjskje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bz" type="{http://www.chinatax.gov.cn/dataspec/}bz" minOccurs="0"/>
 *         &lt;element name="wzsyjsyj" type="{http://www.chinatax.gov.cn/dataspec/}jsyj" minOccurs="0"/>
 *         &lt;element name="wzsyhdse" type="{http://www.chinatax.gov.cn/dataspec/}jsyj" minOccurs="0"/>
 *         &lt;element name="zzsldtse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="bqmdse" type="{http://www.chinatax.gov.cn/dataspec/}mdse" minOccurs="0"/>
 *         &lt;element name="zzsmdse" type="{http://www.chinatax.gov.cn/dataspec/}mdse" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBSbxxJhVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "zsxmDm",
    "zspmDm",
    "zszmDm",
    "hyDm",
    "nsqxDm",
    "zsdlfsDm",
    "jkqxDm",
    "sbqxDm",
    "jkqx",
    "ssyhlxDm",
    "yjze",
    "skssqq",
    "skssqz",
    "djzclxDm",
    "dwlsgxDm",
    "zsfsDm",
    "xssr",
        "yxssr",
    "jsyj",
    "sbqx",
    "sl1",
    "zsl",
    "se",
    "ssglyDm",
    "skssswjgDm",
    "zgswskfjDm",
    "swjgDm",
    "yjse1",
    "jmse",
    "yjse",
    "ynse",
    "hdynsjye",
        "yhdynsjye",
    "hztykcjye",
    "sybh",
    "kjdjxh",
    "rdzsuuid",
    "rdpzuuid",
    "zfsbz",
    "yslwqzd",
    "xshwqzd",
    "jdxzDm",
    "hdbl",
    "hdse",
    "zhjzl",
    "ysbje",
    "bqkdkse",
    "wdqzdbz",
    "jsbz1",
    "yssdl",
    "kce",
    "sfysb",
    "rdyxqq",
    "rdyxqz",
    "dexmDm",
    "wzsyhdjsyj",
    "qzd",
    "lsbbz",
    "dfpskje",
    "djxh",
    "yzpzzlDm",
    "jzjtskbz",
    "zcdsyyjskje",
    "bz",
    "wzsyjsyj",
    "wzsyhdse",
    "zzsldtse",
    "zzsmdse",
        "bqmdse",
        "zsXssr"
})
public class SBSbxxJhVO
    implements Serializable
{

    private final static long serialVersionUID = 1468566763656402744L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsxmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zszmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String hyDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nsqxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsdlfsDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jkqxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbqxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jkqx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssyhlxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yjze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String djzclxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dwlsgxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zsfsDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double xssr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jsyj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbqx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double sl1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zsl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double se;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ssglyDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssswjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zgswskfjDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String swjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yjse1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jmse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yjse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double ynse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hdynsjye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hztykcjye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sybh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjdjxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String rdzsuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String rdpzuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfsbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yslwqzd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double xshwqzd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jdxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hdbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zhjzl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double ysbje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bqkdkse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String wdqzdbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jsbz1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yssdl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double kce;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String sfysb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String rdyxqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String rdyxqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dexmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double wzsyhdjsyj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double qzd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String lsbbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double dfpskje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yzpzzlDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jzjtskbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zcdsyyjskje;   
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double wzsyjsyj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double wzsyhdse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzsldtse;

    protected Double bqmdse;

    protected Double zzsmdse;
    protected Double zsXssr;


    /**
     * @description 应征信息备注用以保存其他或者过度信息
     * @value value:bz
     */
    private String bz;


    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yhdynsjye;

    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yxssr;

    public Double getYxssr() {
        return yxssr;
    }

    public void setYxssr(Double yxssr) {
        this.yxssr = yxssr;
    }


    public Double getYhdynsjye() {
        return yhdynsjye;
    }

    public void setYhdynsjye(Double yhdynsjye) {
        this.yhdynsjye = yhdynsjye;
    }
    public Double getZzsldtse() {
        return zzsldtse;
    }

    public void setZzsldtse(Double zzsldtse) {
        this.zzsldtse = zzsldtse;
    }

    public Double getKce() {
        return kce;
    }

    public void setKce(Double kce) {
        this.kce = kce;
    }

    public Double getYssdl() {
        return yssdl;
    }

    public void setYssdl(Double yssdl) {
        this.yssdl = yssdl;
    }

    /**
     * Gets the value of the zsxmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsxmDm() {
        return zsxmDm;
    }

    /**
     * Sets the value of the zsxmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsxmDm(String value) {
        this.zsxmDm = value;
    }

    /**
     * Gets the value of the zspmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * Sets the value of the zspmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * Gets the value of the zszmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZszmDm() {
        return zszmDm;
    }

    /**
     * Sets the value of the zszmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZszmDm(String value) {
        this.zszmDm = value;
    }

    /**
     * Gets the value of the hyDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHyDm() {
        return hyDm;
    }

    /**
     * Sets the value of the hyDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHyDm(String value) {
        this.hyDm = value;
    }

    /**
     * Gets the value of the nsqxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsqxDm() {
        return nsqxDm;
    }

    /**
     * Sets the value of the nsqxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsqxDm(String value) {
        this.nsqxDm = value;
    }

    /**
     * Gets the value of the zsdlfsDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsdlfsDm() {
        return zsdlfsDm;
    }

    /**
     * Sets the value of the zsdlfsDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsdlfsDm(String value) {
        this.zsdlfsDm = value;
    }

    /**
     * Gets the value of the jkqxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJkqxDm() {
        return jkqxDm;
    }

    /**
     * Sets the value of the jkqxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJkqxDm(String value) {
        this.jkqxDm = value;
    }

    /**
     * Gets the value of the sbqxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbqxDm() {
        return sbqxDm;
    }

    /**
     * Sets the value of the sbqxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbqxDm(String value) {
        this.sbqxDm = value;
    }

    /**
     * Gets the value of the jkqx property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJkqx() {
        return jkqx;
    }

    /**
     * Sets the value of the jkqx property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJkqx(String value) {
        this.jkqx = value;
    }

    /**
     * Gets the value of the ssyhlxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsyhlxDm() {
        return ssyhlxDm;
    }

    /**
     * Sets the value of the ssyhlxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsyhlxDm(String value) {
        this.ssyhlxDm = value;
    }

    /**
     * Gets the value of the yjze property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjze() {
        return yjze;
    }

    /**
     * Sets the value of the yjze property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjze(Double value) {
        this.yjze = value;
    }

    /**
     * Gets the value of the skssqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * Sets the value of the skssqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * Gets the value of the skssqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * Sets the value of the skssqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * Gets the value of the djzclxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjzclxDm() {
        return djzclxDm;
    }

    /**
     * Sets the value of the djzclxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjzclxDm(String value) {
        this.djzclxDm = value;
    }

    /**
     * Gets the value of the dwlsgxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDwlsgxDm() {
        return dwlsgxDm;
    }

    /**
     * Sets the value of the dwlsgxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDwlsgxDm(String value) {
        this.dwlsgxDm = value;
    }

    /**
     * Gets the value of the zsfsDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZsfsDm() {
        return zsfsDm;
    }

    /**
     * Sets the value of the zsfsDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZsfsDm(String value) {
        this.zsfsDm = value;
    }

    /**
     * Gets the value of the xssr property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getXssr() {
        return xssr;
    }

    /**
     * Sets the value of the xssr property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setXssr(Double value) {
        this.xssr = value;
    }

    /**
     * Gets the value of the jsyj property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJsyj() {
        return jsyj;
    }

    /**
     * Sets the value of the jsyj property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJsyj(Double value) {
        this.jsyj = value;
    }

    /**
     * Gets the value of the sbqx property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbqx() {
        return sbqx;
    }

    /**
     * Sets the value of the sbqx property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbqx(String value) {
        this.sbqx = value;
    }

    /**
     * Gets the value of the sl1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSl1() {
        return sl1;
    }

    /**
     * Sets the value of the sl1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSl1(Double value) {
        this.sl1 = value;
    }

    /**
     * Gets the value of the zsl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZsl() {
        return zsl;
    }

    /**
     * Sets the value of the zsl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZsl(Double value) {
        this.zsl = value;
    }

    /**
     * Gets the value of the se property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSe() {
        return se;
    }

    /**
     * Sets the value of the se property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSe(Double value) {
        this.se = value;
    }

    /**
     * Gets the value of the ssglyDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsglyDm() {
        return ssglyDm;
    }

    /**
     * Sets the value of the ssglyDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsglyDm(String value) {
        this.ssglyDm = value;
    }

    /**
     * Gets the value of the skssswjgDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssswjgDm() {
        return skssswjgDm;
    }

    /**
     * Sets the value of the skssswjgDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssswjgDm(String value) {
        this.skssswjgDm = value;
    }

    /**
     * Gets the value of the zgswskfjDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    /**
     * Sets the value of the zgswskfjDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswskfjDm(String value) {
        this.zgswskfjDm = value;
    }

    /**
     * Gets the value of the swjgDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSwjgDm() {
        return swjgDm;
    }

    /**
     * Sets the value of the swjgDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSwjgDm(String value) {
        this.swjgDm = value;
    }

    /**
     * Gets the value of the yjse1 property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjse1() {
        return yjse1;
    }

    /**
     * Sets the value of the yjse1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjse1(Double value) {
        this.yjse1 = value;
    }

    /**
     * Gets the value of the jmse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJmse() {
        return jmse;
    }

    /**
     * Sets the value of the jmse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJmse(Double value) {
        this.jmse = value;
    }

    /**
     * Gets the value of the yjse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYjse() {
        return yjse;
    }

    /**
     * Sets the value of the yjse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYjse(Double value) {
        this.yjse = value;
    }

    /**
     * Gets the value of the ynse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYnse() {
        return ynse;
    }

    /**
     * Sets the value of the ynse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYnse(Double value) {
        this.ynse = value;
    }

    /**
     * Gets the value of the hdynsjye property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHdynsjye() {
        return hdynsjye;
    }

    /**
     * Sets the value of the hdynsjye property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHdynsjye(Double value) {
        this.hdynsjye = value;
    }

    /**
     * Gets the value of the hztykcjye property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHztykcjye() {
        return hztykcjye;
    }

    /**
     * Sets the value of the hztykcjye property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHztykcjye(Double value) {
        this.hztykcjye = value;
    }

    /**
     * Gets the value of the sybh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSybh() {
        return sybh;
    }

    /**
     * Sets the value of the sybh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSybh(String value) {
        this.sybh = value;
    }

    /**
     * Gets the value of the kjdjxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKjdjxh() {
        return kjdjxh;
    }

    /**
     * Sets the value of the kjdjxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKjdjxh(String value) {
        this.kjdjxh = value;
    }

    /**
     * Gets the value of the rdzsuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdzsuuid() {
        return rdzsuuid;
    }

    /**
     * Sets the value of the rdzsuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdzsuuid(String value) {
        this.rdzsuuid = value;
    }

    /**
     * Gets the value of the rdpzuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdpzuuid() {
        return rdpzuuid;
    }

    /**
     * Sets the value of the rdpzuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdpzuuid(String value) {
        this.rdpzuuid = value;
    }

    /**
     * Gets the value of the zfsbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfsbz() {
        return zfsbz;
    }

    /**
     * Sets the value of the zfsbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfsbz(String value) {
        this.zfsbz = value;
    }

    /**
     * Gets the value of the yslwqzd property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYslwqzd() {
        return yslwqzd;
    }

    /**
     * Sets the value of the yslwqzd property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYslwqzd(Double value) {
        this.yslwqzd = value;
    }

    /**
     * Gets the value of the xshwqzd property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getXshwqzd() {
        return xshwqzd;
    }

    /**
     * Sets the value of the xshwqzd property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setXshwqzd(Double value) {
        this.xshwqzd = value;
    }

    /**
     * Gets the value of the jdxzDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJdxzDm() {
        return jdxzDm;
    }

    /**
     * Sets the value of the jdxzDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJdxzDm(String value) {
        this.jdxzDm = value;
    }

    /**
     * Gets the value of the hdbl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHdbl() {
        return hdbl;
    }

    /**
     * Sets the value of the hdbl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHdbl(Double value) {
        this.hdbl = value;
    }

    /**
     * Gets the value of the hdse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHdse() {
        return hdse;
    }

    /**
     * Sets the value of the hdse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHdse(Double value) {
        this.hdse = value;
    }

    /**
     * Gets the value of the zhjzl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZhjzl() {
        return zhjzl;
    }

    /**
     * Sets the value of the zhjzl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZhjzl(Double value) {
        this.zhjzl = value;
    }

    /**
     * Gets the value of the ysbje property.
     * 
     */
    public double getYsbje() {
        return ysbje;
    }

    /**
     * Sets the value of the ysbje property.
     * 
     */
    public void setYsbje(double value) {
        this.ysbje = value;
    }

    /**
     * Gets the value of the bqkdkse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBqkdkse() {
        return bqkdkse;
    }

    /**
     * Sets the value of the bqkdkse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBqkdkse(Double value) {
        this.bqkdkse = value;
    }

    /**
     * Gets the value of the wdqzdbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWdqzdbz() {
        return wdqzdbz;
    }

    /**
     * Sets the value of the wdqzdbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWdqzdbz(String value) {
        this.wdqzdbz = value;
    }

    /**
     * Gets the value of the jsbz1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJsbz1() {
        return jsbz1;
    }

    /**
     * Sets the value of the jsbz1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJsbz1(String value) {
        this.jsbz1 = value;
    }
    
    /**
     * Gets the value of the sfysb property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfysb() {
        return sfysb;
    }

    /**
     * Sets the value of the sfysb property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfysb(String value) {
        this.sfysb = value;
    }

    /**
     * Gets the value of the rdyxqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdyxqq() {
        return rdyxqq;
    }

    /**
     * Sets the value of the rdyxqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdyxqq(String value) {
        this.rdyxqq = value;
    }    
    
    /**
     * Gets the value of the rdyxqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRdyxqz() {
        return rdyxqz;
    }

    /**
     * Sets the value of the rdyxqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRdyxqz(String value) {
        this.rdyxqz = value;
    }
    
    /**
     * Gets the value of the dexmDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDexmDm() {
        return dexmDm;
    }

    /**
     * Sets the value of the dexmDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDexmDm(String value) {
        this.dexmDm = value;
    }
    
    /**
     * Gets the value of the wzsyhdjsyj property.
     * 
     * @return
     *     possible object is
     *     {@link double }
     *     
     */
    public double getWzsyhdjsyj() {
        return wzsyhdjsyj;
    }

    /**
     * Sets the value of the wzsyhdjsyj property.
     * 
     * @param value
     *     allowed object is
     *     {@link double }
     *     
     */
    public void setWzsyhdjsyj(double value) {
        this.wzsyhdjsyj = value;
    }
    
    /**
     * Gets the value of the qzd property.
     * 
     * @return
     *     possible object is
     *     {@link double }
     *     
     */
    public double getQzd() {
        return qzd;
    }

    /**
     * Sets the value of the qzd property.
     * 
     * @param value
     *     allowed object is
     *     {@link double }
     *     
     */
    public void setQzd(double value) {
        this.qzd = value;
    }

    public String getLsbbz() {
        return lsbbz;
    }

    public void setLsbbz(String lsbbz) {
        this.lsbbz = lsbbz;
    }
    
    /**
     * 获取dfpskje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getDfpskje() {
        return dfpskje;
    }

    /**
     * 设置dfpskje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setDfpskje(Double value) {
        this.dfpskje = value;
    }

    /**
     * 获取djxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * 设置djxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * 获取yzpzzlDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYzpzzlDm() {
        return yzpzzlDm;
    }

    /**
     * 设置yzpzzlDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYzpzzlDm(String value) {
        this.yzpzzlDm = value;
    }

    /**
     * 获取jzjtskbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJzjtskbz() {
        return jzjtskbz;
    }

    /**
     * 设置jzjtskbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJzjtskbz(String value) {
        this.jzjtskbz = value;
    }
    
    /**
     * 获取zcdsyyjskje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZcdsyyjskje() {
        return zcdsyyjskje;
    }

    /**
     * 设置zcdsyyjskje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZcdsyyjskje(Double value) {
        this.zcdsyyjskje = value;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public double getWzsyjsyj() {
        return wzsyjsyj;
    }

    public void setWzsyjsyj(double wzsyjsyj) {
        this.wzsyjsyj = wzsyjsyj;
    }

    public double getWzsyhdse() {
        return wzsyhdse;
    }

    public void setWzsyhdse(double wzsyhdse) {
        this.wzsyhdse = wzsyhdse;
    }


    public Double getBqmdse() {
        return bqmdse;
    }

    public void setBqmdse(Double bqmdse) {
        this.bqmdse = bqmdse;
    }

    /**
     * Gets the value of the zzsmdse property.
     *
     * @return
     *     possible object is
     *     {@link Double }
     *
     */
    public Double getZzsmdse() {
        return zzsmdse;
    }

    /**
     * Sets the value of the zzsmdse property.
     *
     * @param value
     *     allowed object is
     *     {@link Double }
     *
     */
    public void setZzsmdse(Double value) {
        this.zzsmdse = value;
    }

    public Double getZsXssr() {
        return zsXssr;
    }

    public void setZsXssr(Double zsXssr) {
        this.zsXssr = zsXssr;
    }
}
