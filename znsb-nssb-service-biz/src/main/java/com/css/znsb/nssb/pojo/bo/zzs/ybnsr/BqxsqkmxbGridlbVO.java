package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import lombok.Getter;
import lombok.Setter;

/**
 * 《增值税纳税申报表附表一（本期销售情况明细表）》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bqxsqkmxbGridlbVO", propOrder = {"ewbhxh", "hmc", "kjskzzszyfpXse", "kjskzzszyfpXxynse", "kjqtfpXse", "kjqtfpXxynse", "wkjfpXse", "wkjfpXxynse", "nsjctzdxse", "nsjctzXxynse", "xse", "hjXxynse", "jshj", "ysfwkcxmbqsjkcje", "kchHsmsxse", "kchXxynse"})
@Getter
@Setter
public class BqxsqkmxbGridlbVO {
    /**
     * 二维表行序号(特殊：13a(原13行)填写13,13b行填写20,13c行填写21,营改增新增9b行填写22,2017-5-31修改4b（原4行）填写4，4a行填写23行)
     */
    protected Long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 开具税控增值税专用发票_销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kjskzzszyfpXse;

    /**
     * 开具税控增值税专用发票_销项应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kjskzzszyfpXxynse;

    /**
     * 开具其他发票_销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kjqtfpXse;

    /**
     * 开具其他发票_销项应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kjqtfpXxynse;

    /**
     * 未开具发票_销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal wkjfpXse;

    /**
     * 未开具发票_销项应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal wkjfpXxynse;

    /**
     * 纳税检查调整的销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal nsjctzdxse;

    /**
     * 纳税检查调整_销项应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal nsjctzXxynse;

    /**
     * 销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal xse;

    /**
     * 合计_销项应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal hjXxynse;

    /**
     * 价税合计
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal jshj;

    /**
     * 应税服务扣除项目本期实际扣除金额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal ysfwkcxmbqsjkcje;

    /**
     * 扣除后_含税免税销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kchHsmsxse;

    /**
     * 扣除后_销项应纳税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal kchXxynse;
}