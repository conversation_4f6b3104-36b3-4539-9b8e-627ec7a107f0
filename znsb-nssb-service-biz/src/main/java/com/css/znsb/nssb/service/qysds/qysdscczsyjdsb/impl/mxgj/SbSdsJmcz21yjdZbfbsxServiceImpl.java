package com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.impl.mxgj;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZbfbsxMapper;
import com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.sb228.JmCzzsYjd2021FbsxVO;
import com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.sb228.SBJmQysdsCzzsYjd2021SqxxVO;
import com.css.znsb.nssb.pojo.domain.sds.SbSdsJmcz21yjdZbfbsxDO;
import com.css.znsb.nssb.pojo.dto.qysds.czzsyjd.sbmxcx.QysdsCzzsYjdSbmxcxRespDTO;
import com.css.znsb.nssb.pojo.dto.qysds.czzsyjd.sbmxcx.lqmxVO.JmqyczzsqysdsyjdsbzbfbsxGrid;
import com.css.znsb.nssb.pojo.vo.qysds.czzsyjd.csh.QysdsCzzsYjdQcxxItemVO;
import com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.QysdsczzsYjdSbMxgjService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sb_sds_jmcz_21yjd_zbfbsx(居民企业（查账征收）企业所得税月（季）度申报（2021）主表附报事项)】的数据库操作Service实现
* @createDate 2024-12-10 19:35:36
*/
@Service
public class SbSdsJmcz21yjdZbfbsxServiceImpl extends ServiceImpl<SbSdsJmcz21yjdZbfbsxMapper, SbSdsJmcz21yjdZbfbsxDO>
    implements QysdsczzsYjdSbMxgjService {

    @Override
    public void saveData(String sbuuid, String pzxh, String qd, String xzqhDm, QysdsCzzsYjdSbmxcxRespDTO sbsjcxRespDTO) {
        this.getBaseMapper().delete(new LambdaQueryWrapperX<SbSdsJmcz21yjdZbfbsxDO>()
                .eq(SbSdsJmcz21yjdZbfbsxDO::getSbuuid,sbuuid));
        if (GyUtils.isNull(sbsjcxRespDTO) || GyUtils.isNull(sbsjcxRespDTO.getJmqyczzsqysdsyjdalsbmx())
        || GyUtils.isNull(sbsjcxRespDTO.getJmqyczzsqysdsyjdalsbmx().getJmqyczzsqysdsyjdsbzbfbsxGrid())){
            return;
        }
        final List<JmqyczzsqysdsyjdsbzbfbsxGrid> fbsxVOList = sbsjcxRespDTO.getJmqyczzsqysdsyjdalsbmx().getJmqyczzsqysdsyjdsbzbfbsxGrid();
        final List<SbSdsJmcz21yjdZbfbsxDO> fbsxList = BeanUtils.toBean(fbsxVOList, SbSdsJmcz21yjdZbfbsxDO.class);
        fbsxList.forEach(t -> {
            t.setUuid(GyUtils.getUuid());
            t.setSbuuid(sbuuid);
            t.setPzxh(pzxh);
            t.setLrrq(new Date());
            t.setXgrq(new Date());
            t.setYwqdDm(qd);
            t.setSjcsdq(xzqhDm);
            t.setSjgsdq(xzqhDm);
        });
        this.getBaseMapper().insertBatch(fbsxList);
    }

    @Override
    public void sbbck(List<QysdsCzzsYjdQcxxItemVO> qcxxItemVOList, String sbuuid) {
        List<SbSdsJmcz21yjdZbfbsxDO> fbsxList = this.getBaseMapper().selectList(new LambdaQueryWrapperX<SbSdsJmcz21yjdZbfbsxDO>()
                .eq(SbSdsJmcz21yjdZbfbsxDO::getSbuuid,sbuuid));
        if (!GyUtils.isNull(fbsxList)){
            SBJmQysdsCzzsYjd2021SqxxVO.FbsxGrid fbsxGrid = new SBJmQysdsCzzsYjd2021SqxxVO.FbsxGrid();
            fbsxGrid.setFbsxGridlb(BeanUtils.toBean(fbsxList,JmCzzsYjd2021FbsxVO.class));
            qcxxItemVOList.get(0).getSqsbxx().setFbsxGrid(fbsxGrid);
        }
    }
}




