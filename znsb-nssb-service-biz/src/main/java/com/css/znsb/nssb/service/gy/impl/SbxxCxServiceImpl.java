package com.css.znsb.nssb.service.gy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.pojo.PageResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.mapper.gy.SbSlxxMapper;
import com.css.znsb.nssb.mapper.gy.ZnsbMhzcQyjbxxmxMapper;
import com.css.znsb.nssb.mapper.gy.ZnsbMhzcSfzrdxxMapper;
import com.css.znsb.nssb.mapper.qysds.SbSdsFzjgfpbFzjgMapper;
import com.css.znsb.nssb.mapper.qysds.SbSdsFzjgfpbZjgMapper;
import com.css.znsb.nssb.mapper.qysds.SbSdsJmcz18yjdMapper;
import com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZbfbsxMapper;
import com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZbjmxxMapper;
import com.css.znsb.nssb.mapper.qysds.SbSdsJmcz21yjdZczjmxbMapper;
import com.css.znsb.nssb.mapper.qysds.SbSdsJmczYjdDynsbabMapper;
import com.css.znsb.nssb.mapper.sbrw.SbSbbDOMapper;
import com.css.znsb.nssb.mapper.sbrw.SbSbxxDOMapper;
import com.css.znsb.nssb.mapper.zzsxgmsb.SbZzsXgmFbFlzlMapper;
import com.css.znsb.nssb.mapper.zzsxgmsb.SbZzsXgmMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbFjsfMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsFbZzsjmssbmxbJsxmMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsFbZzsjmssbmxbMsxmMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb1BqxsqkmxMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb2BqjxsemxMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb3YsfwkcxmMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFb4SedjqkMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbCpygxcqkMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbCpygxcslMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbDlqyxxhjxMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHkysQdjxMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.SbZzsYbnsrFbHkysYjnMapper;
import com.css.znsb.nssb.mapper.zzsybnsrsb.ZzsYbnsrSbmxGjMapper;
import com.css.znsb.nssb.mapper.zzsyjsb.SbZzsyjnssbSjbMapper;
import com.css.znsb.nssb.mapper.zzsyjsb.SbZzsyjnssbYwbMapper;
import com.css.znsb.nssb.pojo.domain.sbrw.SbSbbDO;
import com.css.znsb.nssb.pojo.domain.sbrw.SbSbxxDO;
import com.css.znsb.nssb.pojo.domain.zzsxgmsb.SbZzsXgmDO;
import com.css.znsb.nssb.pojo.domain.zzsxgmsb.SbZzsXgmFbFlzlDO;
import com.css.znsb.nssb.pojo.domain.zzsyjsb.SbZzsyjnssbSjbDO;
import com.css.znsb.nssb.pojo.domain.zzsyjsb.SbZzsyjnssbYwbDO;
import com.css.znsb.nssb.pojo.dto.common.SbSlxxDTO;
import com.css.znsb.nssb.pojo.dto.common.ZnsbMhzcQyjbxxmxDTO;
import com.css.znsb.nssb.pojo.dto.common.ZnsbMhzcSfzrdxxDTO;
import com.css.znsb.nssb.pojo.dto.gy.csdm.JmxxDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbFjsfSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsFbZzsjmssbmxbJsxmSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsFbZzsjmssbmxbMsxmSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFb1BqxsqkmxSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFb2BqjxsemxSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFb3YsfwkcxmSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFb4SedjqkSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbCpygxcqkSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbCpygxcslSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbDlqyxxhjxSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbHkysQdjxSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrFbHkysYjnSjgjDTO;
import com.css.znsb.nssb.pojo.dto.zzs.ybnsr.sjgj.SbZzsYbnsrSjgjDTO;
import com.css.znsb.nssb.pojo.vo.common.BbxzQueryVO;
import com.css.znsb.nssb.pojo.vo.common.SbDyVO;
import com.css.znsb.nssb.pojo.vo.common.SbxxCxQueryVO;
import com.css.znsb.nssb.pojo.vo.common.SbxxCxResVO;
import com.css.znsb.nssb.pojo.vo.common.sbxxcx.SbxxcxTbbbReqVO;
import com.css.znsb.nssb.pojo.vo.xfs.XfssbxxGridlbVO;
import com.css.znsb.nssb.pojo.vo.ybnsr.jmxx.ZzsJmxxReqVO;
import com.css.znsb.nssb.service.cjrjybzj.CjrjybzjService;
import com.css.znsb.nssb.service.gy.ISbxxCxService;
import com.css.znsb.nssb.service.zzsybnsrsb.ZzsYbnsrService;
import com.css.znsb.nssb.utils.GYCastUtils;
import com.css.znsb.nssb.utils.GYSbSdsUtils;
import com.css.znsb.nssb.utils.SbPrintUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SbxxCxServiceImpl implements ISbxxCxService {
    @Resource
    private SbSbbDOMapper sbSbbDOMapper;
    @Resource
    private ZnsbMhzcQyjbxxmxMapper znsbMhzcQyjbxxmxMapper;
    @Resource
    private SbSlxxMapper sbSlxxMapper;
    @Resource
    private ZzsYbnsrSbmxGjMapper zzsYbnsrSbmxGjMapper;
    @Resource
    private SbZzsYbnsrFb1BqxsqkmxMapper sbZzsYbnsrFb1BqxsqkmxMapper;
    @Resource
    private SbZzsYbnsrFb2BqjxsemxMapper sbZzsYbnsrFb2BqjxsemxMapper;
    @Resource
    private SbZzsYbnsrFb3YsfwkcxmMapper sbZzsYbnsrFb3YsfwkcxmMapper;
    @Resource
    private SbZzsYbnsrFb4SedjqkMapper sbZzsYbnsrFb4SedjqkMapper;
    @Resource
    private SbZzsYbnsrFbHkysQdjxMapper sbZzsYbnsrFbHkysQdjxMapper;
    @Resource
    private SbZzsYbnsrFbHkysYjnMapper sbZzsYbnsrFbHkysYjnMapper;
    @Resource
    private SbZzsFbZzsjmssbmxbJsxmMapper sbZzsFbZzsjmssbmxbJsxmMapper;
    @Resource
    private SbZzsFbZzsjmssbmxbMsxmMapper sbZzsFbZzsjmssbmxbMsxmMapper;
    @Resource
    private SbZzsYbnsrFbCpygxcqkMapper sbZzsYbnsrFbCpygxcqkMapper;
    @Resource
    private SbZzsYbnsrFbCpygxcslMapper sbZzsYbnsrFbCpygxcslMapper;
    @Resource
    private SbZzsYbnsrFbDlqyxxhjxMapper sbZzsYbnsrFbDlqyxxhjxMapper;
    @Resource
    private ZnsbMhzcSfzrdxxMapper znsbMhzcSfzrdxxMapper;
    @Resource
    private ZzsYbnsrService zzsYbnsrService;
    @Resource
    private SbZzsyjnssbYwbMapper zzsyjywbMapper;
    @Resource
    private SbZzsyjnssbSjbMapper zzsyjsjbMapper;
    @Resource
    private SbFjsfMapper fjsfMapper;
    @Resource
    private SbZzsXgmMapper sbZzsXgmMapper;
    @Resource
    private SbZzsXgmFbFlzlMapper sbZzsXgmfb1Mapper;
    @Resource
    private SbSdsJmcz18yjdMapper sbSdsJmcz18yjdMapper;
    @Resource
    private SbSdsJmcz21yjdZbfbsxMapper sbSdsJmcz21yjdZbfbsxMapper;
    @Resource
    private SbSdsJmcz21yjdZbjmxxMapper sbSdsJmcz21yjdZbjmxxMapper;
    @Resource
    private SbSdsJmcz21yjdZczjmxbMapper sbSdsJmcz21yjdZczjmxbMapper;
    @Resource
    private SbSdsFzjgfpbZjgMapper sbSdsFzjgfpbZjgMapper;
    @Resource
    private SbSdsFzjgfpbFzjgMapper sbSdsFzjgfpbFzjgMapper;
    @Resource
    private CompanyApi companyApi;
    @Resource
    private SbSdsJmczYjdDynsbabMapper sbSdsJmczYjdDynsbabMapper;
    @Resource
    private SbSbxxDOMapper sbSbxxDOMapper;
    @Resource
    private NsrxxApi nsrxxApi;
    @Resource
    private CjrjybzjService cjrjybzjService;

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public PageResult<SbxxCxResVO> querySbxxcx(SbxxCxQueryVO vo) {
        PageResult<SbxxCxResVO> pageResult = sbSbbDOMapper.querySbxxcx(vo);
        if (GyUtils.isNotNull(pageResult.getList())) {
            // 处理企业代码值
            for (SbxxCxResVO resVO : pageResult.getList()) {
                String nsrsbh = resVO.getNsrsbh();
                String djxh = resVO.getDjxh();
                CommonResult<CompanyBasicInfoDTO> basicInfoResult = companyApi.basicInfo(djxh, nsrsbh);
                if (basicInfoResult.isSuccess() && GyUtils.isNotNull(basicInfoResult.getData())) {
                    resVO.setQydm(basicInfoResult.getData().getQydmz());
                }
                String yzpzzlDm = resVO.getYzpzzlDm();
                if ("BDA0611116".equals(yzpzzlDm)) {
                    List<SbSbxxDO> sbxx= sbSbxxDOMapper.querySbxxBySbuuid(resVO.getSbuuid());
                    if (GyUtils.isNotNull(sbxx)) {
                        String zsxmDm = sbxx.get(0).getZsxmDm();
                        if (zsxmDm.equals("10110")){
                            resVO.setYzpzzlmc("《房产税纳税申报表》");
                        }else if (zsxmDm.equals("10112")){
                            resVO.setYzpzzlmc("《城镇土地使用税纳税申报表》");
                        }
                    }
                }else {
                    resVO.setYzpzzlmc(CacheUtils.dm2mc("dm_gy_dzbzdszl", yzpzzlDm));
                }
            }
        }
        return pageResult;
    }

    /**
     *
     * @name 打印增值税一般人申报表（PDF格式）
     * @description BDA0610606
     * @time 创建时间:2019-1-3下午03:44:26
     * @param sbuuid 申报uuid
     * @return String BASE64编码的二进制PDF流
     * <AUTHOR>
     * @throws Exception
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public String printPdfBbxz(String sbuuid, String dzbzdszlDm) {
        String printstr = null;
        // 获取打印数据信息
        List<SbDyVO> dyxxList = null;

        if ("BDA0610606".equals(dzbzdszlDm)) {// 增值税一般人
            dyxxList = getBDA0610606bySbuuid(sbuuid);
        } else if ("BDA0611111".equals(dzbzdszlDm)) {// 《消费税及附加税费申报表》
            dyxxList = getBDA0611111bySbuuid(sbuuid);
        } else if ("BDA0611148".equals(dzbzdszlDm) || "BDA0610794".equals(dzbzdszlDm)
            || "BDA0611116".equals(dzbzdszlDm)) {// 财产和行为税纳税申报表
            SbSbbDO sbbDO = sbSbbDOMapper.querySbbBySbuuid(sbuuid);
            final List<Map<String, Object>> cxsList = zzsYbnsrSbmxGjMapper.queryCxsxxByPzxh(sbbDO.getPzxh());
            if (GyUtils.isNotNull(cxsList)) {
                final String cxstysbuuid = (String)cxsList.get(0).get("cxstysbuuid");
                dyxxList = getBDA0611148bySbuuid(sbuuid, cxstysbuuid);
            }
        } else if("BDA0610865".equals(dzbzdszlDm)) {// 增值税预缴
            dyxxList = getBDA0610865bySbuuid(sbuuid);
        } else if("BDA0610611".equals(dzbzdszlDm)){// 增值税小规模
            dyxxList = getBDA0610611bySbuuid(sbuuid);
        } else if("BDA0611159".equals(dzbzdszlDm)){//企业所得税预缴
            dyxxList = getBDA0611159bySbuuid(sbuuid);
        } else if(YzpzzlEnum.CJRJYBZJ.getDm().equals(dzbzdszlDm)){
            // 获取申报表基本信息
            LambdaQueryWrapper<SbSbbDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SbSbbDO::getSbuuid, sbuuid);
            SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapper);
            dyxxList = cjrjybzjService.getCbjDyxx(sbbDO.getNsrsbh(),sbbDO.getSkssqq().toLocalDate(),sbbDO.getSkssqz().toLocalDate());
        }
        // final List<SbDyVO> dyxxList = getTest(sbuuid);
        if (GyUtils.isNotNull(dyxxList)) {
            // 打印模板列表
            final List<String> mbljList = new ArrayList<>();
            // 打印参数列表
            final List<Map<String, Object>> paramList = new ArrayList<>();
            for (SbDyVO vo : dyxxList) {
                mbljList.add(vo.getMblj());
                paramList.add(vo.getDyxx());
            }
            // 输出到一个PDF文件中
            final byte[] bytes = SbPrintUtils.makePDFList(mbljList, paramList);
            // TODO 如果需要加签，在此处对bytes进行处理
            // 最后转换为Base64编码的字符串（后期可考虑做Zip压缩，减少网络传输消耗）
            // SbPrintUtils.makePdfForLocal(bytes);
            printstr = SbPrintUtils.encode(bytes);
        }
        return printstr;
    }

    /**
     *
     * @name 打印财行税申报表（PDF格式）
     * @description BDA0610606
     * @time 创建时间:2019-1-3下午03:44:26
     * @param sbuuid 申报uuid
     * @return String BASE64编码的二进制PDF流
     * <AUTHOR>
     * @throws Exception
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public String printPdfBbxz(String sbuuid, String cxstysbuuid, String dzbzdszlDm) {
        String printstr = null;
        // 获取打印数据信息
        List<SbDyVO> dyxxList = getBDA0611148bySbuuid(sbuuid, cxstysbuuid);
        if (GyUtils.isNotNull(dyxxList)) {
            // 打印模板列表
            final List<String> mbljList = new ArrayList<>();
            // 打印参数列表
            final List<Map<String, Object>> paramList = new ArrayList<>();
            for (SbDyVO vo : dyxxList) {
                mbljList.add(vo.getMblj());
                paramList.add(vo.getDyxx());
            }
            // 输出到一个PDF文件中
            final byte[] bytes = SbPrintUtils.makePDFList(mbljList, paramList);
            // TODO 如果需要加签，在此处对bytes进行处理
            // 最后转换为Base64编码的字符串（后期可考虑做Zip压缩，减少网络传输消耗）
            // SbPrintUtils.makePdfForLocal(bytes);
            printstr = SbPrintUtils.encode(bytes);
        }
        return printstr;
    }

    /**
     * @param sbuuid 申报UUID
     * @param cxstysbuuid 财行税通用申报UUID
     * @return {@link List<SbDyVO> }
     * @name 获取财行税申报表打印数据（主表+附表）
     * @description 财行税PDF下载打印数据获取
     * @time 创建时间:2024年05月20日下午05:20:35
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<SbDyVO> getBDA0611148bySbuuid(String sbuuid, String cxstysbuuid) {
        List<SbDyVO> resultList = new ArrayList<>();
        
        try {
            // 获取主表数据（财行税申报主表）
            SbDyVO mainTable = getBDA0611148(sbuuid, cxstysbuuid);
            if (GYCastUtils.notNull(mainTable)) {
                resultList.add(mainTable);
            }
            
            // 获取附表数据（财行税减免明细表）
            SbDyVO attachmentTable = getBDA0611148FB(sbuuid, cxstysbuuid);
            if (GYCastUtils.notNull(attachmentTable)) {
                resultList.add(attachmentTable);
            }
            
            // 处理打印参数的数值格式化
            if (!resultList.isEmpty()) {
                Map<String, String> formatMap = new HashMap<>();
                resultList = dealwithDyxxList(resultList, formatMap);
            }
            
        } catch (Exception e) {
            log.error("获取财行税申报表打印数据异常，sbuuid: {}, cxstysbuuid: {}", sbuuid, cxstysbuuid, e);
            throw new RuntimeException("获取财行税申报表打印数据失败", e);
        }
        
        return resultList;
    }

    /**
     * 获取财行税申报主表打印数据
     * 
     * @param sbuuid 申报UUID
     * @param cxstysbuuid 财行税通用申报UUID
     * @return 主表打印数据
     */
    private SbDyVO getBDA0611148(String sbuuid, String cxstysbuuid) {
        log.debug("开始构建财行税申报主表数据，sbuuid: {}, cxstysbuuid: {}", sbuuid, cxstysbuuid);
        
        try {
            // 获取基础数据
            Map<String, Object> baseData = buildBaseData(sbuuid, cxstysbuuid);
            if (GyUtils.isNull(baseData)) {
                log.debug("基础数据为空，跳过主表构建");
                return null;
            }
            
            // 构建主表数据
            return buildMainTableData(baseData, cxstysbuuid);
            
        } catch (Exception e) {
            log.error("构建财行税申报主表数据异常", e);
            throw new RuntimeException("构建财行税申报主表数据失败", e);
        }
    }
    
    /**
     * 构建基础数据（申报表信息和受理信息）
     * 
     * @param sbuuid 申报UUID
     * @param cxstysbuuid 财行税通用申报UUID
     * @return 基础数据Map
     */
    private Map<String, Object> buildBaseData(String sbuuid, String cxstysbuuid) {
        Map<String, Object> baseData = new HashMap<>();
        
        // 获取申报表基本信息
        LambdaQueryWrapper<SbSbbDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapper);
        
        // 获取财行税申报表信息
        Map<String, Object> sbbMap = zzsYbnsrSbmxGjMapper.queryCxssbbxx(cxstysbuuid);
        if (GyUtils.isNull(sbbMap)) {
            return null;
        }
        
        // 补充经办人信息
        if (GyUtils.isNotNull(sbbDO)) {
            sbbMap.put("jbr", sbbDO.getJbrxm());
            sbbMap.put("dlrsfzjhm1", sbbDO.getDljbrzyzjhm());
        }
        
        // 获取受理信息
        Map<String, Object> slxxMap = zzsYbnsrSbmxGjMapper.querySlxx(sbuuid);
        if (GyUtils.isNotNull(slxxMap)) {
            GYCastUtils.formatRq(slxxMap, "slrq");
            sbbMap.putAll(slxxMap);
        }
        
        // 获取财行税数据
        List<Map<String, Object>> cxsList = zzsYbnsrSbmxGjMapper.queryCxsxx(cxstysbuuid);
        
        baseData.put("sbbMap", sbbMap);
        baseData.put("cxsList", cxsList);
        
        return baseData;
    }
    
    /**
     * 构建主表数据
     * 
     * @param baseData 基础数据
     * @param cxstysbuuid 财行税通用申报UUID
     * @return 主表打印数据
     */
    private SbDyVO buildMainTableData(Map<String, Object> baseData, String cxstysbuuid) {
        Map<String, Object> sbbMap = (Map<String, Object>) baseData.get("sbbMap");
        List<Map<String, Object>> cxsList = (List<Map<String, Object>>) baseData.get("cxsList");
        
        SbDyVO mainTable = new SbDyVO();
        Map<String, Object> fieldMap = new HashMap<>();
        Map<String, Object> tablesMap = new HashMap<>();
        
        // 设置基础字段信息
        fieldMap.putAll(sbbMap);
        
        // 处理财行税数据
        if (GYCastUtils.notNull(cxsList)) {
            processMainTableCxsData(fieldMap, tablesMap, cxsList);
        }
        
        // 处理六税两费相关信息
        processLslfInfo(fieldMap, sbbMap);
        
        // 格式化日期
        GYCastUtils.formatRq(fieldMap, "slrq");
        
        // 设置打印模板路径
        String templatePath = determinePrintTemplatePath(sbbMap, "main");
        
        // 构建打印数据
        Map<String, Object> dyxxMap = new HashMap<>();
        dyxxMap.put("field", fieldMap);
        dyxxMap.put("tables", tablesMap);
        
        mainTable.setMblj(templatePath);
        mainTable.setDyxx(dyxxMap);
        
        log.debug("财行税申报主表数据构建完成");
        return mainTable;
    }
    
    /**
     * 处理主表财行税数据
     * 
     * @param fieldMap 字段映射
     * @param tablesMap 表格映射
     * @param cxsList 财行税数据列表
     */
    private void processMainTableCxsData(Map<String, Object> fieldMap, Map<String, Object> tablesMap, 
                                        List<Map<String, Object>> cxsList) {
        
        // 使用分组合并方法处理数据
        List<Map<String, Object>> processedList = groupAndMergeCxsList(cxsList);
        
        // 初始化合计变量
        double ynseHJ = 0.0;
        double jmseHJ = 0.0;
        double yjseHJ = 0.0;
        double ybtseHJ = 0.0;
        
        // 处理每行数据
        for (int i = 0; i < processedList.size(); i++) {
            Map<String, Object> rowData = processedList.get(i);
            
            // 设置序号
            rowData.put("xh", i + 1);
            
            // 转换代码为名称
            rowData.put("zsxmDm", CacheUtils.dm2mc("dm_gy_zsxm", 
                GYSbSdsUtils.cast2StrNew(rowData.get("zsxmDm"))));
            rowData.put("zspmDm", CacheUtils.dm2mc("dm_gy_zspm", 
                GYSbSdsUtils.cast2StrNew(rowData.get("zspmDm"))));
            
            // 格式化日期
            GYCastUtils.formatRq(rowData, "skssqq", "skssqz");
            
            // 累加金额
            double ynse = GYCastUtils.isNull(rowData.get("ynse")) ? 0.0 : GYCastUtils.cast2Double(rowData.get("ynse"));
            double jmse = GYCastUtils.isNull(rowData.get("jmse")) ? 0.0 : GYCastUtils.cast2Double(rowData.get("jmse"));
            double yjse = GYCastUtils.isNull(rowData.get("yjse")) ? 0.0 : GYCastUtils.cast2Double(rowData.get("yjse"));
            double ybtse = GYCastUtils.isNull(rowData.get("ybtse")) ? 0.0 : GYCastUtils.cast2Double(rowData.get("ybtse"));
            
            ynseHJ = GYSbSdsUtils.add(ynseHJ, ynse);
            jmseHJ = GYSbSdsUtils.add(jmseHJ, jmse);
            yjseHJ = GYSbSdsUtils.add(yjseHJ, yjse);
            ybtseHJ = GYSbSdsUtils.add(ybtseHJ, ybtse);
        }
        
        // 设置表格数据
        tablesMap.put("cxsGrid", processedList);
        
        // 设置合计字段（避免科学计数法）
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        
        fieldMap.put("ynseHJ", nf.format(ynseHJ));
        fieldMap.put("jmseHJ", nf.format(jmseHJ));
        fieldMap.put("yjseHJ", nf.format(yjseHJ));
        fieldMap.put("ybtseHJ", nf.format(ybtseHJ));
    }
    
    /**
     * 处理六税两费相关信息
     * 
     * @param fieldMap 字段映射
     * @param sbbMap 申报表信息
     */
    private void processLslfInfo(Map<String, Object> fieldMap, Map<String, Object> sbbMap) {
        String sfcjrhqy = GYSbSdsUtils.cast2StrNew(sbbMap.get("bqsfsyzzsxgmnsrjzzc"));
        
        if ("Y".equals(sfcjrhqy)) {
            fieldMap.put("sfsy", "√是 □否");
        } else {
            fieldMap.put("sfsy", "□是 √否");
        }
        
        // 处理日期字段
        fieldMap.put("xgmjzzcqssj", GyUtils.isNotNull(sbbMap.get("xgmjzzcqssj")) ?
            GYCastUtils.cast2Str(sbbMap.get("xgmjzzcqssj")).substring(0, 10) : "");
        fieldMap.put("xgmjzzczzsj", GyUtils.isNotNull(sbbMap.get("xgmjzzczzsj")) ?
            GYCastUtils.cast2Str(sbbMap.get("xgmjzzczzsj")).substring(0, 10) : "");
        
        // 设置减征状态
        fieldMap.put("jzzt", CacheUtils.dm2mc("cs_znsb_sb_jzzcsyzt", 
            GYSbSdsUtils.cast2StrNew(sbbMap.get("jzzcsyztDm"))));
    }
    
    /**
     * 确定打印模板路径
     * 
     * @param sbbMap 申报表信息
     * @param tableType 表格类型（main/attachment）
     * @return 模板路径
     */
    private String determinePrintTemplatePath(Map<String, Object> sbbMap, String tableType) {
        String xtcsz = CacheUtils.getXtcs("Z0000099Z02000259", "N");
        boolean isLslf = "Y".equals(xtcsz);
        
        if ("main".equals(tableType)) {
            return isLslf ? "/templates/sbdy/BDA0611148/sb666_cxszb_lslf_print.doc" 
                         : "/templates/sbdy/BDA0611148/sb666_cxszb_print.doc";
        } else {
            return isLslf ? "/templates/sbdy/BDA0611148/sb666_cxsjmmxb_lslf_print.doc" 
                         : "/templates/sbdy/BDA0611148/sb666_cxsjmmxb_print.doc";
        }
    }

    /**
     * 获取财行税申报附表（减免明细表）打印数据
     * 
     * @param sbuuid 申报UUID
     * @param cxstysbuuid 财行税通用申报UUID
     * @return 附表打印数据
     */
    private SbDyVO getBDA0611148FB(String sbuuid, String cxstysbuuid) {
        log.debug("开始构建财行税申报附表数据，sbuuid: {}, cxstysbuuid: {}", sbuuid, cxstysbuuid);
        
        try {
            // 获取基础数据
            Map<String, Object> baseData = buildAttachmentBaseData(sbuuid, cxstysbuuid);
            if (GyUtils.isNull(baseData)) {
                log.debug("附表基础数据为空，跳过附表构建");
                return null;
            }
            
            // 构建附表数据
            return buildAttachmentTableData(baseData, cxstysbuuid);
            
        } catch (Exception e) {
            log.error("构建财行税申报附表数据异常", e);
            throw new RuntimeException("构建财行税申报附表数据失败", e);
        }
    }
    
    /**
     * 构建附表基础数据
     * 
     * @param sbuuid 申报UUID
     * @param cxstysbuuid 财行税通用申报UUID
     * @return 基础数据Map
     */
    private Map<String, Object> buildAttachmentBaseData(String sbuuid, String cxstysbuuid) {
        Map<String, Object> baseData = new HashMap<>();
        
        // 获取申报表信息
        Map<String, Object> sbbMap = zzsYbnsrSbmxGjMapper.queryCxssbbxx(cxstysbuuid);
        if (GyUtils.isNull(sbbMap)) {
            return null;
        }
        
        // 获取受理信息
        Map<String, Object> slxxMap = zzsYbnsrSbmxGjMapper.querySlxx(sbuuid);
        if (GyUtils.isNotNull(slxxMap)) {
            GYCastUtils.formatRq(slxxMap, "slrq");
            sbbMap.putAll(slxxMap);
        }
        
        // 获取减免明细数据
        List<Map<String, Object>> cxsjmxxList = zzsYbnsrSbmxGjMapper.queryCxsjmxxlist(cxstysbuuid);
        
        baseData.put("sbbMap", sbbMap);
        baseData.put("cxsjmxxList", cxsjmxxList);
        
        return baseData;
    }
    
    /**
     * 构建附表数据
     * 
     * @param baseData 基础数据
     * @param cxstysbuuid 财行税通用申报UUID
     * @return 附表打印数据
     */
    private SbDyVO buildAttachmentTableData(Map<String, Object> baseData, String cxstysbuuid) {
        Map<String, Object> sbbMap = (Map<String, Object>) baseData.get("sbbMap");
        List<Map<String, Object>> cxsjmxxList = (List<Map<String, Object>>) baseData.get("cxsjmxxList");
        
        SbDyVO attachmentTable = new SbDyVO();
        Map<String, Object> fieldMap = new HashMap<>();
        Map<String, Object> tablesMap = new HashMap<>();
        
        // 设置基础字段信息
        fieldMap.putAll(sbbMap);
        
        // 处理六税两费相关信息
        processLslfInfo(fieldMap, sbbMap);
        
        // 处理减免明细数据
        if (GYCastUtils.notNull(cxsjmxxList)) {
            processAttachmentCxsjmxxData(fieldMap, tablesMap, cxsjmxxList);
        }
        
        // 格式化日期
        GYCastUtils.formatRq(fieldMap, "slrq", "xgmjzzcqssj", "xgmjzzczzsj");
        
        // 设置打印模板路径
        String templatePath = determinePrintTemplatePath(sbbMap, "attachment");
        
        // 构建打印数据
        Map<String, Object> dyxxMap = new HashMap<>();
        dyxxMap.put("field", fieldMap);
        dyxxMap.put("tables", tablesMap);
        
        attachmentTable.setMblj(templatePath);
        attachmentTable.setDyxx(dyxxMap);
        
        log.debug("财行税申报附表数据构建完成");
        return attachmentTable;
    }
    
    /**
     * 处理附表减免明细数据
     * 
     * @param fieldMap 字段映射
     * @param tablesMap 表格映射
     * @param cxsjmxxList 减免明细数据列表
     */
    private void processAttachmentCxsjmxxData(Map<String, Object> fieldMap, Map<String, Object> tablesMap, 
                                             List<Map<String, Object>> cxsjmxxList) {
        
        // 初始化各税种网格列表
        Map<String, List<Map<String, Object>>> taxGrids = initializeTaxGrids();
        
        // 初始化合计变量
        Map<String, Double> taxTotals = initializeTaxTotals();
        
        // 使用分组合并方法处理数据
        List<Map<String, Object>> mergedList = groupAndMergeCxsjmxxList(cxsjmxxList);
        
        // 按税种分类处理数据
        classifyTaxData(mergedList, taxGrids, taxTotals);
        
        // 设置表格数据
        setTaxGridsToTablesMap(tablesMap, taxGrids);
        
        // 处理表格数据格式
        processGridDataFormat(tablesMap);
        
        // 设置合计字段
        setTaxTotalsToFieldMap(fieldMap, taxTotals);
    }
    
    /**
     * 初始化各税种网格列表
     * 
     * @return 税种网格映射
     */
    private Map<String, List<Map<String, Object>>> initializeTaxGrids() {
        Map<String, List<Map<String, Object>>> taxGrids = new HashMap<>();
        taxGrids.put("fcsGrid", new ArrayList<>());
        taxGrids.put("cztdsysGrid", new ArrayList<>());
        taxGrids.put("ccsGrid", new ArrayList<>());
        taxGrids.put("yhsGrid", new ArrayList<>());
        taxGrids.put("zysGrid", new ArrayList<>());
        taxGrids.put("gdzysGrid", new ArrayList<>());
        taxGrids.put("qsGrid", new ArrayList<>());
        taxGrids.put("tdzzsGrid", new ArrayList<>());
        taxGrids.put("hjbhsGrid", new ArrayList<>());
        taxGrids.put("yysGrid", new ArrayList<>());
        return taxGrids;
    }
    
    /**
     * 初始化税种合计变量
     * 
     * @return 税种合计映射
     */
    private Map<String, Double> initializeTaxTotals() {
        Map<String, Double> taxTotals = new HashMap<>();
        taxTotals.put("jmseHJ", 0.0);
        taxTotals.put("fcsHJ", 0.0);
        taxTotals.put("cztdsHJ", 0.0);
        taxTotals.put("ccsHJ", 0.0);
        taxTotals.put("yhsHJ", 0.0);
        taxTotals.put("zysHJ", 0.0);
        taxTotals.put("gdzysHJ", 0.0);
        taxTotals.put("qsHJ", 0.0);
        taxTotals.put("tdzzsHJ", 0.0);
        taxTotals.put("hjbhsHJ", 0.0);
        taxTotals.put("yysHJ", 0.0);
        return taxTotals;
    }
    
    /**
     * 按税种分类处理数据
     * 
     * @param mergedList 合并后的数据列表
     * @param taxGrids 税种网格映射
     * @param taxTotals 税种合计映射
     */
    private void classifyTaxData(List<Map<String, Object>> mergedList, 
                                Map<String, List<Map<String, Object>>> taxGrids, 
                                Map<String, Double> taxTotals) {

        for (Map<String, Object> rowData : mergedList) {
            Double jmse = GYCastUtils.isNull(rowData.get("jmse")) ? 0.0 : GYCastUtils.cast2Double(rowData.get("jmse"));
            rowData.put("jmse", jmse);
            
            String zsxmDm = GYSbSdsUtils.cast2StrNew(rowData.get("zsxmDm"));
            
            // 根据税种代码分类处理
            switch (zsxmDm) {
                case "10110": // 房产税
                    taxTotals.put("fcsHJ", GYSbSdsUtils.add(taxTotals.get("fcsHJ"), jmse));
                    taxGrids.get("fcsGrid").add(rowData);
                    break;
                case "10112": // 城镇土地使用税
                    taxTotals.put("cztdsHJ", GYSbSdsUtils.add(taxTotals.get("cztdsHJ"), jmse));
                    taxGrids.get("cztdsysGrid").add(rowData);
                    break;
                case "10114": // 车船税
                    taxTotals.put("ccsHJ", GYSbSdsUtils.add(taxTotals.get("ccsHJ"), jmse));
                    taxGrids.get("ccsGrid").add(rowData);
                    break;
                case "10111": // 印花税
                    taxTotals.put("yhsHJ", GYSbSdsUtils.add(taxTotals.get("yhsHJ"), jmse));
                    taxGrids.get("yhsGrid").add(rowData);
                    break;
                case "10107": // 资源税
                    taxTotals.put("zysHJ", GYSbSdsUtils.add(taxTotals.get("zysHJ"), jmse));
                    rowData.put("zszmDm", CacheUtils.dm2mc("dm_gy_zszm", GYSbSdsUtils.cast2StrNew(rowData.get("zszmDm"))));
                    taxGrids.get("zysGrid").add(rowData);
                    break;
                case "10118": // 耕地占用税
                    taxTotals.put("gdzysHJ", GYSbSdsUtils.add(taxTotals.get("gdzysHJ"), jmse));
                    taxGrids.get("gdzysGrid").add(rowData);
                    break;
                case "10119": // 契税
                    taxTotals.put("qsHJ", GYSbSdsUtils.add(taxTotals.get("qsHJ"), jmse));
                    taxGrids.get("qsGrid").add(rowData);
                    break;
                case "10113": // 土地增值税
                    taxTotals.put("tdzzsHJ", GYSbSdsUtils.add(taxTotals.get("tdzzsHJ"), jmse));
                    taxGrids.get("tdzzsGrid").add(rowData);
                    break;
                case "10121": // 环境保护税
                    taxTotals.put("hjbhsHJ", GYSbSdsUtils.add(taxTotals.get("hjbhsHJ"), jmse));
                    rowData.put("zywrwlbDm", CacheUtils.dm2mc("dm_dj_zywrwlb", GYSbSdsUtils.cast2StrNew(rowData.get("zywrwlbDm"))));
                    taxGrids.get("hjbhsGrid").add(rowData);
                    break;
                case "10120": // 烟叶税
                    taxTotals.put("yysHJ", GYSbSdsUtils.add(taxTotals.get("yysHJ"), jmse));
                    taxGrids.get("yysGrid").add(rowData);
                    break;
                default:
                    break;
            }
        }
        
        // 计算总合计
        double totalJmse = taxTotals.values().stream().mapToDouble(Double::doubleValue).sum();
        taxTotals.put("jmseHJ", totalJmse);
    }
    
    /**
     * 设置税种网格数据到表格映射
     * 
     * @param tablesMap 表格映射
     * @param taxGrids 税种网格映射
     */
    private void setTaxGridsToTablesMap(Map<String, Object> tablesMap, Map<String, List<Map<String, Object>>> taxGrids) {
        tablesMap.putAll(taxGrids);
    }
    
    /**
     * 处理表格数据格式
     * 
     * @param tablesMap 表格映射
     */
    private void processGridDataFormat(Map<String, Object> tablesMap) {
        for (Map.Entry<String, Object> entry : tablesMap.entrySet()) {
            List<Map<String, Object>> gridList = (List<Map<String, Object>>) entry.getValue();
            addcsxgridxx(gridList);
        }
    }
    
    /**
     * 设置税种合计到字段映射
     * 
     * @param fieldMap 字段映射
     * @param taxTotals 税种合计映射
     */
    private void setTaxTotalsToFieldMap(Map<String, Object> fieldMap, Map<String, Double> taxTotals) {
        // 避免科学计数法显示
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        
        for (Map.Entry<String, Double> entry : taxTotals.entrySet()) {
            fieldMap.put(entry.getKey(), nf.format(entry.getValue()));
        }
    }

    /**
     *
     * @name 添加信息，转码等
     * @description 相关说明
     * @time 创建时间:2021年5月27日上午11:48:22
     * @param grid grid
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void addcsxgridxx(List<Map<String, Object>> grid) {
        if (GYCastUtils.notNull(grid)) {
            for (int i = 0; i < grid.size(); i++) {
                final Map<String, Object> map = grid.get(i);
                map.put("xh", i + 1);
                GYCastUtils.formatRq(map, "skssqq", "skssqz", "xgmjzzcqssj", "xgmjzzczzsj");
                map.put("zsxmDm", CacheUtils.dm2mc("dm_gy_zsxm", GYSbSdsUtils.cast2StrNew(map.get("zsxmDm"))));
                // GYCastUtils.getMcByDm("DM_GY_ZSXM", GYCastUtils.cast2StrNew(map.get("zspxDm")), "ZSXMMC"));
                map.put("zspmDm", CacheUtils.dm2mc("dm_gy_zspm", GYSbSdsUtils.cast2StrNew(map.get("zspmDm"))));
                // GYCastUtils.getMcByDm("DM_GY_ZSPM", GYCastUtils.cast2StrNew(map.get("zspmDm")), "ZSPMMC"));
                final String ssjmxzmc = changeJmxz(map);
                map.put("ssjmxzDm", ssjmxzmc);
            }
        }
    }

    /**
     * @name 减免性质名称（根据所属期判断减免性质名称）
     * @description 相关说明
     * @time 创建时间:2024年1月17日上午10:14:52
     * @param resMap resMap
     * @return String String
     * @throws Exception
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private String changeJmxz(Map<String, Object> resMap) {
        final String skssqq = GYSbSdsUtils.cast2StrNew(resMap.get("skssqq"));
        final String skssqz = GYSbSdsUtils.cast2StrNew(resMap.get("skssqz"));
        final String ssjmxzDm = GYSbSdsUtils.cast2StrNew(resMap.get("ssjmxzDm"));
        String ssjmxzmc = "";
        if (GYCastUtils.notNull(resMap)) {
            final Map<String, Object> param = new HashMap<>();
            param.put("SSJMXZ_DM", ssjmxzDm);
            final List<Map<String, Object>> tmpList1 = CacheUtils.getTableData("cs_yh_ssjmxzdmmcdz");
            final List<Map<String, Object>> tmpList =
                tmpList1.stream().filter(m -> ssjmxzDm.equals(m.get("ssjmxzDm"))).collect(Collectors.toList());
            // HxzgCacheUtils.getHcbData("CS_YH_SSJMXZDMMCDZ", param);
            if (!GyUtils.isNull(tmpList)) {
                for (Map<String, Object> map11 : tmpList) {
                    final String yxqq = GYSbSdsUtils.cast2StrNew(map11.get("yxqq"));
                    final String yxqz = GYSbSdsUtils.cast2StrNew(map11.get("yxqz"));
                    // 小于0源日期早于比较日期，大于0源日期大于比较日期
                    if (GYSbSdsUtils.compareRq(skssqq, yxqq) >= 0 && GYSbSdsUtils.compareRq(skssqz, yxqz) <= 0) {
                        ssjmxzmc = ssjmxzDm + "|" + GYSbSdsUtils.cast2StrNew(map11.get("ssjmxzmc"));
                    }
                }
            } else {
                final List<Map<String, Object>> tmpDmList1 = CacheUtils.getTableData("dm_gy_ssjmxz");
                final List<Map<String, Object>> tmpDmList =
                    tmpDmList1.stream().filter(m -> ssjmxzDm.equals(m.get("ssjmxzDm"))).collect(Collectors.toList());
                // HxzgCacheUtils.getHcbData("DM_GY_SSJMXZ", param);
                for (Map<String, Object> map11 : tmpDmList) {
                    final String yxqq = GYSbSdsUtils.cast2StrNew(map11.get("yxqq"));
                    final String yxqz = GYSbSdsUtils.cast2StrNew(map11.get("yxqz"));
                    // 小于0源日期早于比较日期，大于0源日期大于比较日期
                    if (GYSbSdsUtils.compareRq(skssqq, yxqq) >= 0 && GYSbSdsUtils.compareRq(skssqz, yxqz) <= 0) {
                        ssjmxzmc = ssjmxzDm + "|" + GYSbSdsUtils.cast2StrNew(map11.get("ssjmxzmc"));
                    }
                }
            }

        }
        return ssjmxzmc;
    }
    /**
     * @name 中文名称
     * @description 测试PDF打印类
     * @time 创建时间:2024年05月14日上午11:17:18
     * @param sbuuid
     * @return {@link List<SbDyVO> }
     * @throws Exception
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    /*public List<SbDyVO> getTest(String sbuuid) throws Exception {
        List<SbDyVO> list = new ArrayList<>();
        //获取主表的数据
        Map<String, Object> res = getBDA0610606FB00(sbuuid);
        final SbSbbDO sbbDO = (SbSbbDO) res.get("sbbxx");
        final SbDyVO zb = (SbDyVO) res.get("sbdyvo");
        if (GyUtils.isNotNull(zb)) {
            list.add(zb);
        }
        return list;
    }*/

    /**
     * @name 中文名称
     * @description 组装获取增值税一般人PDF打印信息
     * @time 创建时间:2024年05月14日上午11:16:27
     * @param sbuuid
     * @return {@link List<SbDyVO> }
     * @throws Exception
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<SbDyVO> getBDA0610606bySbuuid(String sbuuid) {
        List<SbDyVO> list = new ArrayList<>();
        // 获取主表的数据
        Map<String, Object> res = getBDA0610606FB00(sbuuid);
        final SbSbbDO sbbDO = (SbSbbDO)res.get("sbbxx");
        // final ZnsbMhzcQyjbxxmxDTO znsbMhzcQyjbxxmxDTO = (ZnsbMhzcQyjbxxmxDTO) res.get("nsrxx");
        // final SbSlxxDTO slxxDTO = (SbSlxxDTO) res.get("slxx");
        final SbDyVO zb = (SbDyVO)res.get("sbdyvo");
        if (GyUtils.isNotNull(zb)) {
            list.add(zb);
        }

        // 获取附表1的数据
        final SbDyVO fb1 = getBDA0610606FB01(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb1)) {
            list.add(fb1);
        }

        // 获取附表2的数据
        final SbDyVO fb2 = getBDA0610606FB02(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb2)) {
            list.add(fb2);
        }

        // 获取附表3的数据
        final SbDyVO fb3 = getBDA0610606FB03(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb3)) {
            list.add(fb3);
        }

        // 获取附表4的数据
        final SbDyVO fb4 = getBDA0610606FB04(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb4)) {
            list.add(fb4);
        }

        // 乐企接口没有 SB_ZZS_YBNSR_FB_GDZCJXDK
        // 获取附表05的数据--固定资产（不含不动产）进项税额抵扣情况表
        /*final SbDyVO fb05 = getBDA0610606FB05(sbuuid);
        if (GyUtils.isNotNull(fb05)) {
            list.add(fb05);
        }*/

        // 获取附表06的数据--航空运输企业分支机构传递单
        final SbDyVO fb06 = getBDA0610606FB06(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb06)) {
            list.add(fb06);
        }

        // 获取附表08的数据--成品油购销存情况明细表
        final SbDyVO fb08 = getBDA0610606FB08(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb08)) {
            list.add(fb08);
        }

        // 获取附表09的数据--增值税一般纳税人适用申报表成品油购销存数量明细表
        final SbDyVO fb09 = getBDA0610606FB09(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb09)) {
            list.add(fb09);
        }
        // 乐企接口没此表Sb_Zzs_Ybnsr_Fb_Jdcscxsmx
        // 获取附表10的数据--增值税一般纳税人适用申报表--机动车辆生产企业销售明细表
        /*final SbDyVO fb10 = getBDA0610606FB10(sbuuid);
        if (GyUtils.isNotNull(fb10)) {
            list.add(fb10);
        }*/

        // 乐企没有此表 Sb_Zzs_Ybnsr_Fb_Jdcxstj
        // 获取附表11的数据--增值税一般纳税人适用申报表--机动车辆生产企业销售情况统计表
        /*final SbDyVO fb11 = getBDA0610606FB11(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb11)) {
            list.add(fb11);
        }*/

        // 乐企没有此表 Sb_Zzs_Ybnsr_Fb_Jdcxsfpyb
        // 获取附表12的数据--机动车销售统一发票领用存月报表
        /*final SbDyVO fb12 = getBDA0610606FB12(sbuuid);
        if (GyUtils.isNotNull(fb12)) {
            list.add(fb12);
        }*/

        // 乐企没有此表 Sb_Zzs_Ybnsr_Fb_Jdcxsfpqd
        // 获取附表13的数据--机动车销售统一发票清单
        /*final SbDyVO fb13 = getBDA0610606FB13(sbuuid);
        if (GyUtils.isNotNull(fb13)) {
            list.add(fb13);
        }*/

        // 乐企没有此表 Sb_Zzs_Ybnsr_Fb_Jdcjxxsmx
        // 获取附表14的数据--机动车辆经销企业销售明细表
        /*final SbDyVO fb14 = getBDA0610606FB14(sbuuid);
        if (GyUtils.isNotNull(fb14)) {
            list.add(fb14);
        }*/

        // 获取附表15的数据--电力企业增值税销项税额和进项税额传递单
        final SbDyVO fb15 = getBDA0610606FB15(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb15)) {
            list.add(fb15);
        }

        // 乐企没有此表 Sb_Zzs_Ybnsr_Fb_Ysfpdkqd
        // 获取附表16的数据--增值税运输发票抵扣清单
        /*final SbDyVO fb16 = getBDA0610606FB16(sbuuid);
        if (GyUtils.isNotNull(fb16)) {
            list.add(fb16);
        }*/

        // 获取附表18的数据--农产品核定扣除增值税进项税额计算表（汇总表） Sb_Zzs_Ybnsr_Fb_Ncphdkchz
        final SbDyVO fb18 = getBDA0610606FB18(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb18)) {
            list.add(fb18);
        }

        // 获取附表19的数据--投入产出法核定农产品增值税进项税额计算表 SB_ZZS_YBNSR_FB_HDNCPJX
        final SbDyVO fb19 = getBDA0610606FB19(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb19)) {
            list.add(fb19);
        }

        // 获取附表20的数据--成本法核定农产品增值税进项税额计算表//SB_ZZS_YBNSR_FB_CBFHDNCP《成本法核定农产品增值税进项税额计算表》
        final SbDyVO fb20 = getBDA0610606FB20(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb20)) {
            list.add(fb20);
        }

        // 获取附表21的数据--购进农产品直接销售核定农产品增值税进项税额计算表SB_ZZS_YBNSR_FB_NCPZJXS
        final SbDyVO fb21 = getBDA0610606FB21(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb21)) {
            list.add(fb21);
        }

        // 获取附表21的数据--SB_ZZS_YBNSR_FB_NCPYYSCJY《购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表》
        final SbDyVO fb22 = getBDA0610606FB22(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb22)) {
            list.add(fb22);
        }
        // 获取附表23的数据--加油站月销售油品汇总表SB_ZZS_YBNSR_FB_JYZYXSHZ 增值税一般纳税人适用申报表加油站月销售油品汇总表
        final SbDyVO fb23 = getBDA0610606FB23(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb23)) {
            list.add(fb23);
        }
        // 获取附表24的数据--加油站月销售油品汇总表 SB_ZZS_YBNSR_FB_JYZYFJYXX 增值税一般纳税人适用申报表加油站月份加油信息明细表
        final SbDyVO fb24 = getBDA0610606FB24(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb24)) {
            list.add(fb24);
        }
        // 获取附表25的数据--代扣代缴税收通用缴款书抵扣清单 SB_ZZS_YBNSR_FB_DKDJJKSDK 代扣代缴税收通用缴款书抵扣清单
        final SbDyVO fb25 = getBDA0610606FB25(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb25)) {
            list.add(fb25);
        }
        // 获取附表26的数据--汇总纳税企业通用传递单 《汇总纳税企业通用传递单》 SB_ZZS_YBNSR_FB_HZTYCDD_XX SB_ZZS_YBNSR_FB_HZTYCDD_JX
        final SbDyVO fb26 = getBDA0610606FB26(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb26)) {
            list.add(fb26);
        }
        // 《邮政企业分支机构增值税汇总纳税信息传递单》SB_ZZS_YBNSR_FB_YZQY_YJN
        // 《邮政企业分支机构增值税汇总纳税信息传递单》SB_ZZS_YBNSR_FB_YZQY_QDJX
        final SbDyVO fb27 = getBDA0610606FB27(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb27)) {
            list.add(fb27);
        }
        // 《铁路运输企业分支机构增值税汇总纳税信息传递单》SB_ZZS_YBNSR_FB_TLYS_YJN SB_ZZS_YBNSR_FB_TLYS_QDJX
        final SbDyVO fb28 = getBDA0610606FB28(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb28)) {
            list.add(fb28);
        }
        // 《电信企业分支机构增值税汇总纳税信息传递单》 SB_ZZS_YBNSR_FB_DXQY_YJN SB_ZZS_YBNSR_FB_DXQY_QDJX
        final SbDyVO fb29 = getBDA0610606FB29(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb29)) {
            list.add(fb29);
        }
        // SB_ZZS_YBNSR_FB_HZNSFP 增值税一般纳税人适用申报表汇总纳税企业增值税分配表
        // SB_ZZS_YBNSR_FB_HZNSFP_ZB 增值税一般纳税人适用申报表汇总纳税企业增值税分配表_子表
        final SbDyVO fb30 = getBDA0610606FB30(sbuuid);
        if (GyUtils.isNotNull(fb30)) {
            list.add(fb30);
        }
        // 乐企没有此表 Sb_Zzs_Ybnsr_Fb_Bfcpxstjb 输出部分产品销售统计表打印数据
        /*final SbDyVO fb32 = getBDA0610606FB32(sbuuid);
        if (GyUtils.isNotNull(fb32)) {
            list.add(fb32);
        }*/

        // 获取附表34的数据（同增值税小规模BDA0610611的附表7，完全一样）
        // 增值税减免税申报明细表
        // SB_ZZS_FB_ZZSJMSSBMXB_JSXM 增值税减免税申报明细表-减税项目
        // SB_ZZS_FB_ZZSJMSSBMXB_MSXM 增值税减免税申报明细表-免税项目
        final SbDyVO fb34 = getBDA0610611FB07(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb34)) {
            list.add(fb34);
        }

        // 乐企没有此表数据sb_zzs_ybnsr_fb5_bdcfqdkb 增值税一般纳税人适用申报表附列资料五（不动产分期抵扣计算表）
        // 获取附表36的数据
        /*final SbDyVO fb36 = getBDA0610606FB36(sbuuid);
        if (GyUtils.isNotNull(fb36)) {
            list.add(fb36);
        }*/

        // 获取附表37的数据--增值税一般纳税人适用申报表--本期抵扣进项税额结构明细表
        // 乐企没有此表数据 Sb_Zzs_Ybnsr_Fb_Jxsejgmxb 增值税一般纳税人适用申报表本期抵扣进项税额结构明细表
        /*final SbDyVO fb37 = getBDA0610606FB37(sbuuid);
        if (GyUtils.isNotNull(fb37)) {
            list.add(fb37);
        }*/

        // 获取附表38的数据--增值税一般纳税人适用申报表--铁路建设基金纳税申报表 SB_ZZS_YBNSR_FB_TLJSJJ 铁路建设基金纳税申报表
        final SbDyVO fb38 = getBDA0610606FB38(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb38)) {
            list.add(fb38);
        }

        // 获取附表39的数据--增值税一般纳税人适用申报表--附加税费情况表 SB_FJSF SB_FJSF_QTXX
        final SbDyVO fb39 = getBDA0610606FB39(sbuuid, sbbDO);
        if (GyUtils.isNotNull(fb39)) {
            list.add(fb39);
        }

        // 获取附表40的数据--增值税一般纳税人适用申报表--汇总纳税附加税费分配表 汇总纳税附加税费分配表 SB_FJSF_HZNSFJSFFPB
        final SbDyVO fb40 = getBDA0610606FB40(sbuuid);
        if (GyUtils.isNotNull(fb40)) {
            list.add(fb40);
        }
        // 精度参数Map，本例中精度都默认保留小数点后2位，所以为空，其他用例有特殊要求的话自己构造 by:张俊，2019年1月23日12:36:51
        final Map<String, String> csMap = new HashMap<>();
        if (GyUtils.isNotNull(fb08)) {
            csMap.put("qckcZgsl", "##############0.0000");
        }

        if (!GyUtils.isNull(fb30)) {
            csMap.put("zjggdfpbl", "###0.0000000000");
            csMap.put("zjgybhwjlwfpbl", "###0.0000000000");
            csMap.put("zjgybhwjlwjzjtfpbl", "###0.0000000000");
            csMap.put("zjgysfwfpbl", "###0.0000000000");
            csMap.put("zjgysfwjzjtfpbl", "###0.0000000000");
            csMap.put("fzjgybhwjlwfpbl", "###0.0000000000");
            csMap.put("fzjgybhwjlwjzjtfpbl", "###0.0000000000");
            csMap.put("fzjgysfwfpbl", "###0.0000000000");
            csMap.put("fzjgysfwjzjtfpbl", "###0.0000000000");
        }
        // 对List中打印参数的数值进行处理 by:张俊，2019年1月22日16:31:29
        list = dealwithDyxxList(list, csMap);
        return list;
    }

    /**
     * @name 中文名称
     * @description 组装消费税及附加税费申报表PDF打印数据
     * @time 创建时间:2024年05月14日上午11:20:47
     * @param sbuuid
     * @return {@link List<SbDyVO> }
     * @throws Exception
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public List<SbDyVO> getBDA0611111bySbuuid(String sbuuid) {

        List<SbDyVO> list = new ArrayList<>();
        // sb_xfs sb_xfs_zb
        Map<String, Object> res = getBDA0611111Zb(sbuuid);
        final SbSbbDO sbbDO = (SbSbbDO)res.get("sbbxx");
        final SbDyVO xfszb = (SbDyVO)res.get("sbdyvo");
        // 获取主表的数据F100
        if (GYCastUtils.notNull(xfszb)) {
            list.add(xfszb);
        }
        // Sb_Xfs_Fb_Bqzykcsejsb 本期准予扣除税额计算表
        final SbDyVO fb1 = getBDA0611111FB1bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb1)) {
            list.add(fb1);
        }
        // Sb_Xfs_Fb_Dksejkcjs_Cpy抵扣税额及库存计算 、Sb_Xfs_Fb_Bxrlyclyqk_Cpy变性燃料乙醇领用存情况
        final SbDyVO fb2 = getBDA0611111FB2bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb2)) {
            list.add(fb2);
        }
        // Sb_Xfs_Fb_Bqjmsemxb 本期减（免）税额明细表
        final SbDyVO fb3 = getBDA0611111FB3bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb3)) {
            list.add(fb3);
        }
        // Sb_Xfs_Fb_Stfdkdjskqk 本期委托加工情况报告表 、Sb_Xfs_Fb_Wtjghsdysxfplyqk 委托加工收回的应税消费品领用存情况
        final SbDyVO fb4 = getBDA0611111FB4bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb4)) {
            list.add(fb4);
        }
        // Sb_Xfs_Fb_Snyrlyydtjb 消费税附表石脑油、燃料油生产、外购、耗用、库存月度统计表
        // Sb_Xfs_Fb_Snyrlyydtjb_Ddzgjh 消费税附表石脑油、燃料油生产、外购、耗用、库存月度统计表_定点直供计划情况
        // Sb_Xfs_Fb_Snyrlyydtjb_Kchyqk 消费税附表石脑油、燃料油生产、外购、耗用、库存月度统计表_库存耗用情况
        final SbDyVO fb5 = getBDA0611111FB5bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb5)) {
            list.add(fb5);
        }
        // 乙烯、芳烃生产装置投入产出流量计统计表 Sb_Xfs_Fb_Yxftsczztj
        final SbDyVO fb6 = getBDA0611111FB6bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb6)) {
            list.add(fb6);
        }
        // 使用企业外购石脑油、燃料油凭证明细表 Sb_Xfs_Fb_Wgpzmx Sb_Xfs_Fb_Wgpzmx_Zb
        final SbDyVO fb7 = getBDA0611111FB7bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb7)) {
            list.add(fb7);
        }
        // 消费税附表生产企业定点直供石脑油、燃料油开具普通版增值税专用发票明细表 Sb_Xfs_Fb_Zyfpmx
        final SbDyVO fb8 = getBDA0611111FB8bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb8)) {
            list.add(fb8);
        }
        // 生产企业销售含税石脑油、燃料油完税情况明细表 Sb_Xfs_Fb_Xswsqkmx_Zb Sb_Xfs_Fb_Xswsqkmx
        final SbDyVO fb9 = getBDA0611111FB9bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb9)) {
            list.add(fb9);
        }
        // 汇总纳税企业消费税分配表（总机构） Sb_Xfs_Fb_Hznsqyxfsfpb_Zjg 、Sb_Xfs_Fb_Hznsqyxfsfpb
        final SbDyVO fb10 = getBDA0611111FB10bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb10)) {
            list.add(fb10);
        }
        // Sb_Xfs_Fb_Gdsbgzsszbxsmxb 高档手表、贵重首饰及珠宝玉石销售明细表
        final SbDyVO fb11 = getBDA0611111FB11bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb11)) {
            list.add(fb11);
        }
        // SB_XFS_FB_YHDZDJSJGBJQD 消费税附表已核定最低计税价格白酒清单
        final SbDyVO fb12 = getBDA0611111FB12bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb12)) {
            list.add(fb12);
        }
        // SB_XFS_FB_JYPFQYYFXSMX 消费税附表卷烟批发企业月份销售明细清单
        final SbDyVO fb13 = getBDA0611111FB13bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb13)) {
            list.add(fb13);
        }
        // Sb_Xfs_Fb_Hzscjyxfsqkbgb 卷烟生产企业合作生产卷烟消费税情况报告表
        final SbDyVO fb14 = getBDA0611111FB14bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb14)) {
            list.add(fb14);
        }
        // Sb_Fjsf 附加税费情况表
        final SbDyVO fb15 = getBDA0611111FB15bySbuuid(sbuuid, sbbDO);
        if (GYCastUtils.notNull(fb15)) {
            list.add(fb15);
        }
        final Map<String, String> csMap = new HashMap<>();

        // 对List中打印参数的数值进行处理 by:张俊，2019年1月22日16:31:29
        list = dealwithDyxxList(list, csMap);
        return list;
    }

    private Map<String, Object> getBDA0611111Zb(String sbuuid) {
        String djxh = null;
        String skssqq = null;
        String skssqz = null;
        final Map<String, Object> reS = new HashMap<>();
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // sb_sbb
        final LambdaQueryWrapper<SbSbbDO> wrapperSbSbb = new LambdaQueryWrapper<>();
        wrapperSbSbb.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapperSbSbb);
        if (GyUtils.isNotNull(sbbDO)) {
            reS.put("sbbxx", sbbDO);
            Map<String, Object> sbbMap = getSbbMap(sbbDO);
            djxh = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformDjxh"));
            skssqq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqq"));
            skssqz = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqz"));
            fieldMap.putAll(sbbMap);
            // 受理信息
            final LambdaQueryWrapper<SbSlxxDTO> slxxWrapper = new LambdaQueryWrapper<>();
            slxxWrapper.eq(SbSlxxDTO::getSbuuid, sbuuid);
            List<SbSlxxDTO> slxxDTO = sbSlxxMapper.selectList(slxxWrapper);
            if (GYCastUtils.notNull(slxxDTO)) {
                final String blrysfzjlxDm = slxxDTO.get(0).getBlrysfzjlxDm();
                final String blrysfzjlxMc = CacheUtils.dm2mc("dm_gy_sfzjlx", blrysfzjlxDm);
                sbbMap.put("blrysfzjlxDm", blrysfzjlxMc);
                fieldMap.putAll(sbbMap);
            }
        }

        // 然后查询业务表主表 sb_xfs
        final Map<String, Object> zbMap = zzsYbnsrSbmxGjMapper.queryXfszb(sbuuid);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000458_getbda0610090zb01", param);
        if (GYCastUtils.notNull(zbMap)) {
            // 本期应扣除税额
            final Double bqykcse =
                GYSbSdsUtils.add(GYCastUtils.getJe(zbMap, "bqzykcse"), GYCastUtils.getJe(zbMap, "qcldse"));
            zbMap.put("bqykcse", bqykcse);
            // 主表中的Grid信息 sb_xfs_zb
            final List<Map<String, Object>> zbmxbGrid = zzsYbnsrSbmxGjMapper.queryXfszbmx(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000459_getbda0610090zb01", param);
            if (GYCastUtils.notNull(zbmxbGrid)) {
                Double ynsehj = 0.0;
                for (Map<String, Object> tempmap : zbmxbGrid) {
                    ynsehj = GYSbSdsUtils.add(ynsehj,
                        GYCastUtils.notNull(tempmap.get("ynse")) ? GYSbSdsUtils.cDouble(tempmap.get("ynse")) : 0.0);
                    // 适用税率
                    final String zspmDm = GYSbSdsUtils.cast2StrNew(tempmap.get("zspmDm"));
                    final String zspmMc = CacheUtils.dm2mc("dm_gy_zspm", zspmDm);
                    tempmap.put("zspmDm", zspmMc);
                    final Map<String, Object> res = getBDA0611111ZbSl(zspmDm, skssqq, skssqz, djxh);
                    if (!GyUtils.isNull(res)) {
                        tempmap.put("desl", GYCastUtils.notNull(res.get("desl")) ? res.get("desl") : "——");
                        tempmap.put("blsl", GYCastUtils.notNull(res.get("blsl")) ? res.get("blsl") : "——");
                        tempmap.put("jldwmc", GYCastUtils.notNull(res.get("jldwmc")) ? res.get("jldwmc") : "——");
                    }
                }
                // 将明细表中的明细数据填入主表中
                tablesMap.put("zbmxbGrid", zbmxbGrid);
                zbMap.put("ynsehj", ynsehj);
            }
            fieldMap.putAll(zbMap);
            for (String keytemp : fieldMap.keySet()) {
                fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
            }
            dyxx.put("field", fieldMap);
            dyxx.put("tables", tablesMap);
        }

        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "/templates/sbdy/BDA0611111/sb001_xfsnssb2019zbprint.doc";

        // sb702dao.getBDA0611111Zb(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            final boolean sfQyFjsfb = sfQyFjsfb(skssqz);

            if (sfQyFjsfb) {
                mblj = "/templates/sbdy/BDA0611111/sb001_xfsnssb2019zb2print.doc";
            }
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        reS.put("sbdyvo", vo);
        return reS;
    }

    /**
     *
     * @name 获取税率
     * @description 获取税率
     * @time 创建时间:2019年7月22日下午4:45:48
     * @param zspmDm zspmDm
     * @param skssqq skssqq
     * @param skssqz skssqz
     * @param djxh djxh
     * @return res 税率
     * @throws Exception 通用异常
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public Map<String, Object> getBDA0611111ZbSl(String zspmDm, String skssqq, String skssqz, String djxh) {
        final LambdaQueryWrapper<ZnsbMhzcSfzrdxxDTO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ZnsbMhzcSfzrdxxDTO::getDjxh, djxh);
        wrapper.eq(ZnsbMhzcSfzrdxxDTO::getZspmDm, zspmDm);
        wrapper.eq(ZnsbMhzcSfzrdxxDTO::getZsxmDm, "10102");
        wrapper.eq(ZnsbMhzcSfzrdxxDTO::getZsdlfsDm, "0");
        wrapper.ge(ZnsbMhzcSfzrdxxDTO::getRdyxqz, GYSbSdsUtils.cast2Date(skssqz));
        wrapper.le(ZnsbMhzcSfzrdxxDTO::getRdyxqq, GYSbSdsUtils.cast2Date(skssqq));
        List<ZnsbMhzcSfzrdxxDTO> sfzrdxxList = znsbMhzcSfzrdxxMapper.selectList(wrapper);
        final String zsdlfsDm = "0";
        if (!GyUtils.isNull(sfzrdxxList)) {
            for (int i = sfzrdxxList.size() - 1; i >= 0; i--) {
                if (!GyUtils.isNull(zsdlfsDm)) {
                    if (sfzrdxxList.get(i).getZsdlfsDm() != null
                        && !zsdlfsDm.equals(sfzrdxxList.get(i).getZsdlfsDm())) {
                        sfzrdxxList.remove(i);
                    }
                }
            }
        }
        Double sl1 = GYSbSdsUtils.cDouble(sfzrdxxList.get(0).getSlhdwse());
        final XfssbxxGridlbVO sbvo = new XfssbxxGridlbVO();

        Double desl;// 定额税率
        String jldwmc;// 计量单位名称

        if ("101020104".equals(zspmDm) || "101020105".equals(zspmDm) || "101020109".equals(zspmDm)) {
            // 如果关联表里配置的税率单位是元/支，转换为元/万支
            if (sl1 < 1) {
                sl1 = GYSbSdsUtils.multiple(sl1, 10000);
            }
        }

        final Map<String, Object> pzb = CacheUtils.getTableData("cs_sb_xfsslpzb", zspmDm);
        if (!GyUtils.isNull(pzb)) {
            jldwmc = GYCastUtils.cast2Str(pzb.get("jldwmc"));
            desl = GYSbSdsUtils.cDouble(pzb.get("desl"));
            if (!GyUtils.isNull(desl) && desl > 0) {
                sbvo.setDesl(sl1);
            } else {
                sbvo.setBlsl(sl1);
            }
            sbvo.setJldwmc(jldwmc);
        } else {
            final Map<String, Object> zspmMp = CacheUtils.getTableData("dm_gy_zspm", zspmDm);
            if (!GyUtils.isNull(zspmMp)) {
                final String sjzspmDm = GYSbSdsUtils.cast2StrNew(zspmMp.get("SJZSPM_DM"));
                final Map<String, Object> sjpzb = CacheUtils.getTableData("cs_sb_xfsslpzb", sjzspmDm);
                if (!GyUtils.isNull(sjpzb)) {
                    jldwmc = GYCastUtils.cast2Str(sjpzb.get("jldwmc"));
                    desl = GYSbSdsUtils.cDouble(sjpzb.get("desl"));
                    if (!GyUtils.isNull(desl) && desl > 0) {
                        sbvo.setDesl(sl1);
                    } else {
                        sbvo.setBlsl(sl1);
                    }
                    sbvo.setJldwmc(jldwmc);
                }
            }
        }
        final Map<String, Object> res = new HashMap<>();
        res.put("desl", sbvo.getDesl());
        res.put("blsl", sbvo.getBlsl());
        res.put("jldwmc", sbvo.getJldwmc());
        return res;
    }

    private SbDyVO getBDA0611111FB1bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // 主表中的Grid信息 Sb_Xfs_Fb_Bqzykcsejsb
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbBqzykcsejsb(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000865_queryxfsnssbfbbqzykcsejsbxxbysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                Double dqzykcwtjgynsehj = 0.0;
                Double dqzykcwgynsehj = 0.0;
                Double bqzykcwgysynskhj = 0.0;
                Double bqzykcsehjhj = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    final String zspmDm = GYSbSdsUtils.cast2StrNew(tempmap.get("zspmDm"));
                    final String zspmMc = CacheUtils.dm2mc("dm_gy_zspm", zspmDm);
                    tempmap.put("zspmDm", GYCastUtils.notNull(zspmMc) ? zspmMc : zspmDm);

                    dqzykcwtjgynsehj =
                        GYSbSdsUtils.add(dqzykcwtjgynsehj, GYCastUtils.notNull(tempmap.get("dqzykcwtjgynse"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("dqzykcwtjgynse")) : 0.0);
                    dqzykcwgynsehj = GYSbSdsUtils.add(dqzykcwgynsehj, GYCastUtils.notNull(tempmap.get("dqzykcwgynse"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("dqzykcwgynse")) : 0.0);
                    bqzykcwgysynskhj =
                        GYSbSdsUtils.add(bqzykcwgysynskhj, GYCastUtils.notNull(tempmap.get("bqzykcwgysynsk"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("bqzykcwgysynsk")) : 0.0);
                    bqzykcsehjhj = GYSbSdsUtils.add(bqzykcsehjhj, GYCastUtils.notNull(tempmap.get("bqzykcsehj"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("bqzykcsehj")) : 0.0);
                }
                tablesMap.put("mxbGrid", mxbGrid);
                hjMap.put("dqzykcwtjgynsehj", dqzykcwtjgynsehj);
                hjMap.put("dqzykcwgynsehj", dqzykcwgynsehj);
                hjMap.put("bqzykcwgysynskhj", bqzykcwgysynskhj);
                hjMap.put("bqzykcsehjhj", bqzykcsehjhj);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbkcsejsbprint.doc";

        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            // ycjbrzjhm(dyxx,"jbrsfzjhm");
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB2bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            // 无此表
            // Sb_Xfs_Fb_Zykcmx_Cpy成品油准予扣除明细
            /*final List<Map<String, Object>> mxbGrid1 = ps.queryMapListByKey("hdxt_dzswj_sbjs000529_getbda0610738fb01for2018", param);
            if(GYCastUtils.notNull(mxbGrid1)){
                for(String keytemp : fieldMap.keySet()){
                    fieldMap.put(keytemp, GYCastUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }*/

            // Sb_Xfs_Fb_Dksejkcjs_Cpy抵扣税额及库存计算
            final List<Map<String, Object>> mxbGrid2 = zzsYbnsrSbmxGjMapper.queryXfsFbDksejkcjsCpy(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000530_getbda0610738fb01for2018", param);
            if (GYCastUtils.notNull(mxbGrid2)) {
                for (Map<String, Object> tempmap : mxbGrid2) {
                    final String ewbhxh = GYSbSdsUtils.cast2StrNew(tempmap.get("ewbhxh"));
                    tempmap.put("ewbhxh", ewbhxh.equals("1") ? "汽油" : tempmap.get("ewbhxh"));
                    tempmap.put("ewbhxh", ewbhxh.equals("2") ? "柴油" : tempmap.get("ewbhxh"));
                    tempmap.put("ewbhxh", ewbhxh.equals("3") ? "石脑油" : tempmap.get("ewbhxh"));
                    tempmap.put("ewbhxh", ewbhxh.equals("4") ? "润滑油" : tempmap.get("ewbhxh"));
                    tempmap.put("ewbhxh", ewbhxh.equals("5") ? "燃料油" : tempmap.get("ewbhxh"));
                    tempmap.put("ewbhxh", ewbhxh.equals("6") ? "合计" : tempmap.get("ewbhxh"));
                }
                tablesMap.put("mxbGrid2", mxbGrid2);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }

            // Sb_Xfs_Fb_Bxrlyclyqk_Cpy变性燃料乙醇领用存情况
            final List<Map<String, Object>> mxbGrid3 = zzsYbnsrSbmxGjMapper.queryXfsFbBxrlyclyqkCpy(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000531_getbda0610738fb01for2018", param);
            if (GYCastUtils.notNull(mxbGrid3)) {
                for (Map<String, Object> tempmap : mxbGrid3) {
                    final String ewbhxh = GYSbSdsUtils.cast2StrNew(tempmap.get("ewbhxh"));
                    tempmap.put("ewbhxh", ewbhxh.equals("1") ? "润滑油基础油（废矿物油）" : tempmap.get("ewbhxh"));
                    tempmap.put("ewbhxh", ewbhxh.equals("2") ? "变性燃料乙醇" : tempmap.get("ewbhxh"));
                }
                tablesMap.put("mxbGrid3", mxbGrid3);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbkcsejsbcpyprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB3bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // Sb_Xfs_Fb_Bqjmsemxb
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbBqjmsemxb(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000463_getbda0610090fb05", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                Double bqjmsehj = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    final String zspmDm = GYSbSdsUtils.cast2StrNew(tempmap.get("zspmDm"));
                    final String zspmMc = CacheUtils.dm2mc("dm_gy_zspm", zspmDm);
                    tempmap.put("zspmDm", GYCastUtils.notNull(zspmMc) ? zspmMc : "出口免税");
                    final String ssjmxzDm = GYSbSdsUtils.cast2StrNew(tempmap.get("ssjmxzDm"));
                    final String ssjmxzMc = CacheUtils.dm2mc("dm_gy_ssjmxz", ssjmxzDm);
                    tempmap.put("ssjmxzDm", GYCastUtils.notNull(ssjmxzMc) ? ssjmxzMc : "");
                    final String swsxDm = GYSbSdsUtils.cast2StrNew(tempmap.get("swsxDm"));
                    final String swsxMc = CacheUtils.dm2mc("dm_gy_swsx", swsxDm);
                    tempmap.put("swsxDm", GYCastUtils.notNull(swsxMc) ? swsxMc : "");
                    bqjmsehj = GYSbSdsUtils.add(bqjmsehj,
                        GYCastUtils.notNull(tempmap.get("bqjmse")) ? GYSbSdsUtils.cDouble(tempmap.get("bqjmse")) : 0);
                }
                tablesMap.put("mxbGrid", mxbGrid);
                hjMap.put("bqjmsehj", bqjmsehj);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbkcsemxbprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB4bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // Sb_Xfs_Fb_Stfdkdjskqk 委托加工受托方代扣代缴税款情况
            final List<Map<String, Object>> mxbGrid1 = zzsYbnsrSbmxGjMapper.queryXfsFbStfdkdjskqk(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000533_getbda0610738fb02for2018", param);
            if (GYCastUtils.notNull(mxbGrid1)) {
                Double wtjgshslhj = 0.0;
                Double wtjgshysxfpjsjghj = 0.0;
                Double dsdjskhj = 0.0;
                for (Map<String, Object> tempmap : mxbGrid1) {
                    // 处理日期
                    final String ssjkskjrq = ((LocalDateTime)tempmap.get("ssjkskjrq")).format(formatter);
                    tempmap.put("ssjkskjrq", ssjkskjrq);

                    wtjgshslhj = GYSbSdsUtils.add(wtjgshslhj, GYCastUtils.notNull(tempmap.get("wtjgshslhj"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("wtjgshslhj")) : 0);
                    wtjgshysxfpjsjghj =
                        GYSbSdsUtils.add(wtjgshysxfpjsjghj, GYCastUtils.notNull(tempmap.get("wtjgshysxfpjsjg"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("wtjgshysxfpjsjg")) : 0);
                    dsdjskhj = GYSbSdsUtils.add(dsdjskhj,
                        GYCastUtils.notNull(tempmap.get("dsdjsk")) ? GYSbSdsUtils.cDouble(tempmap.get("dsdjsk")) : 0);
                }
                tablesMap.put("mxbGrid1", mxbGrid1);
                hjMap.put("wtjgshslhj", wtjgshslhj);
                hjMap.put("wtjgshysxfpjsjghj", wtjgshysxfpjsjghj);
                hjMap.put("dsdjskhj", dsdjskhj);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
            // Sb_Xfs_Fb_Wtjghsdysxfplyqk 委托加工收回的应税消费品领用存情况
            final List<Map<String, Object>> mxbGrid2 = zzsYbnsrSbmxGjMapper.queryXfsFbWtjghsdysxfplyqk(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000534_getbda0610738fb02for2018", param);
            if (GYCastUtils.notNull(mxbGrid2)) {
                Double sqkcslhj = 0.0;
                Double bqwtjgshslhj = 0.0;
                Double bqwtjgshzjjgslhj = 0.0;
                Double bqwtjgshyylxscslhj = 0.0;
                Double bqjcsl1hj = 0.0;
                for (Map<String, Object> tempmap : mxbGrid2) {
                    sqkcslhj = GYSbSdsUtils.add(sqkcslhj,
                        GYCastUtils.notNull(tempmap.get("sqkcsl")) ? GYSbSdsUtils.cDouble(tempmap.get("sqkcsl")) : 0);
                    bqwtjgshslhj = GYSbSdsUtils.add(bqwtjgshslhj, GYCastUtils.notNull(tempmap.get("bqwtjgshsl"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("bqwtjgshsl")) : 0);
                    bqwtjgshzjjgslhj =
                        GYSbSdsUtils.add(bqwtjgshzjjgslhj, GYCastUtils.notNull(tempmap.get("bqwtjgshzjjgsl"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("bqwtjgshzjjgsl")) : 0);
                    bqwtjgshyylxscslhj =
                        GYSbSdsUtils.add(bqwtjgshyylxscslhj, GYCastUtils.notNull(tempmap.get("bqwtjgshyylxscsl"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("bqwtjgshyylxscsl")) : 0);
                    bqjcsl1hj = GYSbSdsUtils.add(bqjcsl1hj,
                        GYCastUtils.notNull(tempmap.get("bqjcsl1")) ? GYSbSdsUtils.cDouble(tempmap.get("bqjcsl1")) : 0);
                }
                tablesMap.put("mxbGrid2", mxbGrid2);
                hjMap.put("sqkcslhj", sqkcslhj);
                hjMap.put("bqwtjgshslhj", bqwtjgshslhj);
                hjMap.put("bqwtjgshzjjgslhj", bqwtjgshzjjgslhj);
                hjMap.put("bqwtjgshyylxscslhj", bqwtjgshyylxscslhj);
                hjMap.put("bqjcsl1hj", bqjcsl1hj);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbbqwtjgqkbgbprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB5bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            // Sb_Xfs_Fb_Snyrlyydtjb产品情况
            final List<Map<String, Object>> mxbGrid1 = zzsYbnsrSbmxGjMapper.queryXfsFbSnyrlyydtjb(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000536_getbda0610738fb06", param);
            if (GYCastUtils.notNull(mxbGrid1)) {
                final Map<String, Object> cpqkMap = new HashMap<>();
                for (Map<String, Object> tempmap : mxbGrid1) {
                    final String ewbhxh = GYSbSdsUtils.cast2StrNew(tempmap.get("ewbhxh"));
                    cpqkMap.put("cl" + ewbhxh, tempmap.get("cl"));
                    cpqkMap.put("cpycpbl" + ewbhxh, tempmap.get("cpycpbl") + "%");
                }
                fieldMap.putAll(cpqkMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
            }
            // Sb_Xfs_Fb_Snyrlyydtjb_Ddzgjh定点直供计划情况
            final List<Map<String, Object>> mxbGrid2 = zzsYbnsrSbmxGjMapper.queryXfsFbSnyrlyydtjbDdzgjh(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000525_getbda0610738fb06", param);
            if (GYCastUtils.notNull(mxbGrid2)) {
                final Map<String, Object> ddzgjhMap = new HashMap<>();
                for (Map<String, Object> tempmap : mxbGrid2) {
                    final String ewbhxh = GYSbSdsUtils.cast2StrNew(tempmap.get("ewbhxh"));
                    ddzgjhMap.put("gcrly" + ewbhxh, tempmap.get("gcRly"));
                    ddzgjhMap.put("gcsny" + ewbhxh, tempmap.get("gcSny"));
                    ddzgjhMap.put("qygrrly" + ewbhxh, tempmap.get("qygrRly"));
                    ddzgjhMap.put("qygrsny" + ewbhxh, tempmap.get("qygrSny"));
                }
                fieldMap.putAll(ddzgjhMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
            }
            // Sb_Xfs_Fb_Snyrlyydtjb_Kchyqk库存耗用情况
            final List<Map<String, Object>> mxbGrid3 = zzsYbnsrSbmxGjMapper.queryXfsFbSnyrlyydtjbKchyqk(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000526_getbda0610738fb06", param);
            if (GYCastUtils.notNull(mxbGrid3)) {
                final Map<String, Object> kchyqkMap = new HashMap<>();
                for (Map<String, Object> tempmap : mxbGrid3) {
                    final String ewblxh = GYSbSdsUtils.cast2StrNew(tempmap.get("ewblxh"));
                    for (String key : tempmap.keySet()) {
                        kchyqkMap.put(key + ewblxh, tempmap.get(key));
                    }
                }
                fieldMap.putAll(kchyqkMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbsnyrlyscwghykcydtjbprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB6bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // Sb_Xfs_Fb_Yxftsczztj
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbYxftsczztj(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000504_queryxfscpyfb7bysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                Double yltrqkLljgsHJ = 0.0;
                Double yltrLltjSnyslHJ = 0.0;
                Double yltrLltjRnlslHJ = 0.0;
                Double yltrCwhsSnyslHJ = 0.0;
                Double yltrCwhsRnlslHJ = 0.0;
                Double cpccLltjgsHJ = 0.0;
                Double cpccLltjYxslHJ = 0.0;
                Double cpccLltjFtslHJ = 0.0;
                Double cpccCwhsYxslHJ = 0.0;
                Double cpccCwhsFtslHJ = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    yltrqkLljgsHJ = GYSbSdsUtils.add(yltrqkLljgsHJ, GYCastUtils.notNull(tempmap.get("yltrqkLljgs"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("yltrqkLljgs")) : 0);
                    yltrLltjSnyslHJ =
                        GYSbSdsUtils.add(yltrLltjSnyslHJ, GYCastUtils.notNull(tempmap.get("yltrLltjSnysl"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("yltrLltjSnysl")) : 0);
                    yltrLltjRnlslHJ =
                        GYSbSdsUtils.add(yltrLltjRnlslHJ, GYCastUtils.notNull(tempmap.get("yltrLltjRnlsl"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("yltrLltjRnlsl")) : 0);
                    yltrCwhsSnyslHJ =
                        GYSbSdsUtils.add(yltrCwhsSnyslHJ, GYCastUtils.notNull(tempmap.get("yltrCwhsSnysl"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("yltrCwhsSnysl")) : 0);
                    yltrCwhsRnlslHJ =
                        GYSbSdsUtils.add(yltrCwhsRnlslHJ, GYCastUtils.notNull(tempmap.get("yltrCwhsRnlsl"))
                            ? GYSbSdsUtils.cDouble(tempmap.get("yltrCwhsRnlsl")) : 0);
                    cpccLltjgsHJ = GYSbSdsUtils.add(cpccLltjgsHJ, GYCastUtils.notNull(tempmap.get("cpccLltjgs"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("cpccLltjgs")) : 0);
                    cpccLltjYxslHJ = GYSbSdsUtils.add(cpccLltjYxslHJ, GYCastUtils.notNull(tempmap.get("cpccLltjYxsl"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("cpccLltjYxsl")) : 0);
                    cpccLltjFtslHJ = GYSbSdsUtils.add(cpccLltjFtslHJ, GYCastUtils.notNull(tempmap.get("cpccLltjFtsl"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("cpccLltjFtsl")) : 0);
                    cpccCwhsYxslHJ = GYSbSdsUtils.add(cpccCwhsYxslHJ, GYCastUtils.notNull(tempmap.get("cpccCwhsYxsl"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("cpccCwhsYxsl")) : 0);
                    cpccCwhsFtslHJ = GYSbSdsUtils.add(cpccCwhsFtslHJ, GYCastUtils.notNull(tempmap.get("cpccCwhsFtsl"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("cpccCwhsFtsl")) : 0);
                }
                tablesMap.put("mxbGrid", mxbGrid);
                hjMap.put("yltrqkLljgsHJ", yltrqkLljgsHJ);
                hjMap.put("yltrLltjSnyslHJ", yltrLltjSnyslHJ);
                hjMap.put("yltrLltjRnlslHJ", yltrLltjRnlslHJ);
                hjMap.put("yltrCwhsSnyslHJ", yltrCwhsSnyslHJ);
                hjMap.put("yltrCwhsRnlslHJ", yltrCwhsRnlslHJ);
                hjMap.put("cpccLltjgsHJ", cpccLltjgsHJ);
                hjMap.put("cpccLltjYxslHJ", cpccLltjYxslHJ);
                hjMap.put("cpccLltjFtslHJ", cpccLltjFtslHJ);
                hjMap.put("cpccCwhsYxslHJ", cpccCwhsYxslHJ);
                hjMap.put("cpccCwhsFtslHJ", cpccCwhsFtslHJ);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbyxftsczztjprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB7bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            Double hssnyslHJ = 0.0;// 含税石脑油
            Double hsrlyslHJ = 0.0;// 含税燃料油
            // Sb_Xfs_Fb_Wgpzmx
            final List<Map<String, Object>> mxbGrid1 = zzsYbnsrSbmxGjMapper.queryXfsFbWgpzmx(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000505_queryxfscpyfb8bysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid1)) {
                final List<Map<String, Object>> msfwGrid = new ArrayList<>();
                final List<Map<String, Object>> msptGrid = new ArrayList<>();
                final List<Map<String, Object>> hsfwGrid = new ArrayList<>();
                final List<Map<String, Object>> hsptGrid = new ArrayList<>();

                Double msfwsnyslHJ = 0.0;// 免税防伪石脑油
                Double msfwrlyslHJ = 0.0;// 免税防伪燃料油
                Double msptsnyslHJ = 0.0;// 免税普通石脑油
                Double msptrlyslHJ = 0.0;// 免税普通燃料油
                Double mssnyslHJ = 0.0;// 免税石脑油
                Double msrlyslHJ = 0.0;// 免税燃料油

                Double hsfwsnyslHJ = 0.0;// 含税防伪石脑油
                Double hsfwrlyslHJ = 0.0;// 含税防伪燃料油
                Double hsptsnyslHJ = 0.0;// 含税普通石脑油
                Double hsptrlyslHJ = 0.0;// 含税普通燃料油
                Double gcsnyslHJ = 0.0;// 国产含税石脑油
                Double gcrlyslHJ = 0.0;// 国产含税燃料油
                for (Map<String, Object> tempmap : mxbGrid1) {
                    // 外购免税油品,外购含税油品
                    final String qywgyplx = GYSbSdsUtils.cast2StrNew(tempmap.get("qywgyplx"));
                    // 普通版增值税专用发票,汉字防伪版增值税专用发票
                    final String fpzlmc = GYSbSdsUtils.cast2StrNew(tempmap.get("fpzlmc"));
                    if ("外购免税油品".equals(qywgyplx) && "汉字防伪版增值税专用发票".equals(fpzlmc)) {
                        msfwGrid.add(tempmap);
                        msfwsnyslHJ = GYSbSdsUtils.add(msfwsnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        msfwrlyslHJ = GYSbSdsUtils.add(msfwrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                        mssnyslHJ = GYSbSdsUtils.add(mssnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        msrlyslHJ = GYSbSdsUtils.add(msrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                    }
                    if ("外购免税油品".equals(qywgyplx) && "普通版增值税专用发票".equals(fpzlmc)) {
                        msptGrid.add(tempmap);
                        msptsnyslHJ = GYSbSdsUtils.add(msptsnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        msptrlyslHJ = GYSbSdsUtils.add(msptrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                        mssnyslHJ = GYSbSdsUtils.add(mssnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        msrlyslHJ = GYSbSdsUtils.add(msrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                    }
                    if ("外购含税油品".equals(qywgyplx) && "汉字防伪版增值税专用发票".equals(fpzlmc)) {
                        hsfwGrid.add(tempmap);
                        hsfwsnyslHJ = GYSbSdsUtils.add(hsfwsnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        hsfwrlyslHJ = GYSbSdsUtils.add(hsfwrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                        gcsnyslHJ = GYSbSdsUtils.add(gcsnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        gcrlyslHJ = GYSbSdsUtils.add(gcrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                        hssnyslHJ = GYSbSdsUtils.add(hssnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        hsrlyslHJ = GYSbSdsUtils.add(hsrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                    }
                    if ("外购含税油品".equals(qywgyplx) && "普通版增值税专用发票".equals(fpzlmc)) {
                        hsptGrid.add(tempmap);
                        hsptsnyslHJ = GYSbSdsUtils.add(hsptsnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        hsptrlyslHJ = GYSbSdsUtils.add(hsptrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                        gcsnyslHJ = GYSbSdsUtils.add(gcsnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        gcrlyslHJ = GYSbSdsUtils.add(gcrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                        hssnyslHJ = GYSbSdsUtils.add(hssnyslHJ,
                            GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                        hsrlyslHJ = GYSbSdsUtils.add(hsrlyslHJ,
                            GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                    }
                }
                hjMap.put("msfwsnyslHJ", msfwsnyslHJ);
                hjMap.put("msfwrlyslHJ", msfwrlyslHJ);
                hjMap.put("msptsnyslHJ", msptsnyslHJ);
                hjMap.put("msptrlyslHJ", msptrlyslHJ);
                hjMap.put("mssnyslHJ", mssnyslHJ);
                hjMap.put("msrlyslHJ", msrlyslHJ);

                hjMap.put("hsfwsnyslHJ", hsfwsnyslHJ);
                hjMap.put("hsfwrlyslHJ", hsfwrlyslHJ);
                hjMap.put("hsptsnyslHJ", hsptsnyslHJ);
                hjMap.put("hsptrlyslHJ", hsptrlyslHJ);
                hjMap.put("gcsnyslHJ", gcsnyslHJ);
                hjMap.put("gcrlyslHJ", gcrlyslHJ);
                hjMap.put("hssnyslHJ", hssnyslHJ);
                hjMap.put("hsrlyslHJ", hsrlyslHJ);

                tablesMap.put("msfwGrid", msfwGrid);
                tablesMap.put("msptGrid", msptGrid);
                tablesMap.put("hsfwGrid", hsfwGrid);
                tablesMap.put("hsptGrid", hsptGrid);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
            // Sb_Xfs_Fb_Wgpzmx_Zb 子表
            final List<Map<String, Object>> mxbGrid2 = zzsYbnsrSbmxGjMapper.queryXfsFbWgpzmxZb(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000863_queryxfsnssbfbwgpzmx_zbxxbysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid2)) {
                Double jksnyslHJ = 0.0;// 进口含税石脑油
                Double jkrlyslHJ = 0.0;// 进口含税燃料油
                for (Map<String, Object> tempmap : mxbGrid1) {
                    jksnyslHJ = GYSbSdsUtils.add(jksnyslHJ,
                        GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                    jkrlyslHJ = GYSbSdsUtils.add(jkrlyslHJ,
                        GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                    hssnyslHJ = GYSbSdsUtils.add(hssnyslHJ,
                        GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                    hsrlyslHJ = GYSbSdsUtils.add(hsrlyslHJ,
                        GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                }
                tablesMap.put("mxbGrid2", mxbGrid2);
                hjMap.put("jksnyslHJ", jksnyslHJ);
                hjMap.put("jkrlyslHJ", jkrlyslHJ);
                hjMap.put("hssnyslHJ", hssnyslHJ);
                hjMap.put("hsrlyslHJ", hsrlyslHJ);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbwgpzmxprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB8bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // Sb_Xfs_Fb_Zyfpmx
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbZyfpmx(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000864_queryxfsnssbfbzyfpmxxxbysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                final List<Map<String, Object>> snyGrid = new ArrayList<>();
                final List<Map<String, Object>> rlyGrid = new ArrayList<>();
                Double snyslhj = 0.0;
                Double rlyslhj = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    // 处理日期
                    final String kprq = ((LocalDateTime)tempmap.get("kprq")).format(formatter);
                    tempmap.put("kprq", kprq);
                    final String hwmc = GYSbSdsUtils.cast2StrNew(tempmap.get("hwmc"));
                    if ("石脑油".equals(hwmc)) {
                        snyslhj = GYSbSdsUtils.add(snyslhj,
                            GYCastUtils.notNull(tempmap.get("sl")) ? GYSbSdsUtils.cDouble(tempmap.get("sl")) : 0);
                        snyGrid.add(tempmap);
                    }
                    if ("燃料油".equals(hwmc)) {
                        rlyslhj = GYSbSdsUtils.add(rlyslhj,
                            GYCastUtils.notNull(tempmap.get("sl")) ? GYSbSdsUtils.cDouble(tempmap.get("sl")) : 0);
                        rlyGrid.add(tempmap);
                    }
                }
                final Double slhj = snyslhj + rlyslhj;
                hjMap.put("snyslhj", snyslhj);
                hjMap.put("rlyslhj", rlyslhj);
                hjMap.put("slhj", slhj);
                tablesMap.put("snyGrid", snyGrid);
                tablesMap.put("rlyGrid", rlyGrid);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbzyfpmxprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB9bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // Sb_Xfs_Fb_Xswsqkmx_Zb
            final Map<String, Object> mxbmap = zzsYbnsrSbmxGjMapper.queryXfsFbXswsqkmxZb(sbuuid);
            // ps.queryMapByKey("hdxt_dzswj_sbjs000508_queryxfscpyfb10bysbuuid", param);
            if (GYCastUtils.notNull(mxbmap)) {
                fieldMap.putAll(mxbmap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
            // Sb_Xfs_Fb_Xswsqkmx
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbXswsqkmx(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000508_queryxfscpyfb10bysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                Double snyslHJ = 0.0;
                Double rlyslHJ = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    snyslHJ = GYSbSdsUtils.add(snyslHJ,
                        GYCastUtils.notNull(tempmap.get("snysl")) ? GYSbSdsUtils.cDouble(tempmap.get("snysl")) : 0);
                    rlyslHJ = GYSbSdsUtils.add(rlyslHJ,
                        GYCastUtils.notNull(tempmap.get("rlysl")) ? GYSbSdsUtils.cDouble(tempmap.get("rlysl")) : 0);
                }
                tablesMap.put("mxbGrid", mxbGrid);
                hjMap.put("snyslHJ", snyslHJ);
                hjMap.put("rlyslHJ", rlyslHJ);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbxswsqkmxprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB10bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // Sb_Xfs_Fb_Hznsqyxfsfpb_Zjg 总机构信息
            final Map<String, Object> mxbMap = zzsYbnsrSbmxGjMapper.queryXfsFbHznsqyxfsfpbZjg(sbuuid);
            // ps.queryMapByKey("hdxt_dzswj_sbjs000467_getbda0610090fb06", param);
            if (GYCastUtils.notNull(mxbMap)) {
                // 先调用api接口
                ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
                znsbmhzcqyjbxxmxreqvo.setDjxh(GYCastUtils.cast2Str(sbbDO.getDjxh()));
                znsbmhzcqyjbxxmxreqvo.setNsrsbh(sbbDO.getNsrsbh());
                znsbmhzcqyjbxxmxreqvo.setFhNsrJbxx(true);
                CommonResult<ZnsbMhzcQyjbxxmxResVO> resQyjbxx = nsrxxApi.getNsrxxByNsrsbh(znsbmhzcqyjbxxmxreqvo);
                List<JbxxmxsjVO> list = resQyjbxx.getData().getJbxxmxsj();
                // List<JbxxmxsjVO> list = new ArrayList<JbxxmxsjVO>();
                log.debug("纳税人信息" + list);
                if (!GyUtils.isNull(list)) {
                    JbxxmxsjVO jbxxmxsjVO = list.get(0);
                    mxbMap.put("fddbrxm", jbxxmxsjVO.getFddbrxm());
                    mxbMap.put("yydz", jbxxmxsjVO.getScjydz());
                    mxbMap.put("lxdh", jbxxmxsjVO.getZcdlxdh());
                    mxbMap.put("zjgnsrsbh", jbxxmxsjVO.getNsrsbh());
                    mxbMap.put("zjgnsrmc", jbxxmxsjVO.getNsrmc());
                    mxbMap.put("ybtse", sbbMap.get("ybtse"));
                } else {
                    // znsb_mhzc_qyjbxxmx
                    final LambdaQueryWrapper<ZnsbMhzcQyjbxxmxDTO> qyjbxxWrapper = new LambdaQueryWrapper<>();
                    qyjbxxWrapper.eq(ZnsbMhzcQyjbxxmxDTO::getDjxh, sbbDO.getDjxh());
                    ZnsbMhzcQyjbxxmxDTO znsbMhzcQyjbxxmxDTO = znsbMhzcQyjbxxmxMapper.selectOne(qyjbxxWrapper);

                    mxbMap.put("fddbrxm", znsbMhzcQyjbxxmxDTO.getFddbrxm());
                    mxbMap.put("yydz", znsbMhzcQyjbxxmxDTO.getScjydz());
                    mxbMap.put("lxdh", znsbMhzcQyjbxxmxDTO.getZcdlxdh());
                    mxbMap.put("zjgnsrsbh", znsbMhzcQyjbxxmxDTO.getNsrsbh());
                    mxbMap.put("zjgnsrmc", znsbMhzcQyjbxxmxDTO.getNsrmc());
                    mxbMap.put("ybtse", sbbMap.get("ybtse"));
                }

                // 处理日期
                final String slrq = ((LocalDateTime)mxbMap.get("slrq")).format(formatter);
                mxbMap.put("slrq", slrq);
                fieldMap.putAll(mxbMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
            // Sb_Xfs_Fb_Hznsqyxfsfpb
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbHznsqyxfsfpb(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000466_getbda0610090fb06", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                Double fzjgxssrhj = 0.0;
                Double fzjgfpblhj = 0.0;
                Double fzjgfpsehj = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    fzjgxssrhj = GYSbSdsUtils.add(fzjgxssrhj, GYCastUtils.notNull(tempmap.get("fzjgxssr"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("fzjgxssr")) : 0);
                    fzjgfpblhj = GYSbSdsUtils.add(fzjgfpblhj, GYCastUtils.notNull(tempmap.get("fzjgfpbl"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("fzjgfpbl")) : 0);
                    fzjgfpsehj = GYSbSdsUtils.add(fzjgfpsehj, GYCastUtils.notNull(tempmap.get("fzjgfpse"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("fzjgfpse")) : 0);
                }
                tablesMap.put("mxbGrid", mxbGrid);
                hjMap.put("fzjgxssrhj", fzjgxssrhj);
                hjMap.put("fzjgfpblhj", fzjgfpblhj);
                hjMap.put("fzjgfpsehj", fzjgfpsehj);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbzfjgprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB11bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            // Sb_Xfs_Fb_Gdsbgzsszbxsmxb
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbGdsbgzsszbxsmxb(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000861_queryxfsnssbfbgdsbgzsszbxsmxbxxbysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                for (Map<String, Object> tempmap : mxbGrid) {
                    final String ewbhxh = GYSbSdsUtils.cast2StrNew(tempmap.get("ewbhxh"));
                    final String sntqxssl = GYSbSdsUtils.cast2StrNew(tempmap.get("sntqxssl"));
                    tempmap.put("sntqxssl", GYCastUtils.notNull(sntqxssl) ? sntqxssl : 0);
                    if ("1".equals(ewbhxh)) {
                        tempmap.put("pm", "高档手表");
                    }
                    if ("2".equals(ewbhxh)) {
                        tempmap.put("pm", "贵重首饰及珠宝玉石");
                    }
                }
                tablesMap.put("mxbGrid", mxbGrid);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbgdsbgzssxsmxprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB12bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            // Sb_Xfs_Fb_Yhdzdjsjgbjqd
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbYhdzdjsjgbjqd(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000414_getbda0610744fb04", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                tablesMap.put("mxbGrid", mxbGrid);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbyhdzdjsjgbjqdprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB13bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            // Sb_Xfs_Fb_Jypfqyyfxsmx
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbJypfqyyfxsmx(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000384_getbda0610017fb01", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                for (Map<String, Object> tempmap : mxbGrid) {
                    final String jylbDm1 = GYSbSdsUtils.cast2StrNew(tempmap.get("jylbDm1"));
                    final String jylbMc1 = CacheUtils.dm2mc("dm_sb_jylb", jylbDm1);
                    tempmap.put("jylbDm1", jylbMc1);
                    final String jylxDm1 = GYSbSdsUtils.cast2StrNew(tempmap.get("jylxDm1"));
                    if (jylxDm1.equals("1")) {// 国产卷烟
                        tempmap.put("jylxDm1", "国产卷烟");
                    } else if (jylxDm1.equals("2")) {
                        tempmap.put("jylxDm1", "进口卷烟");
                    } else if (jylxDm1.equals("3")) {
                        tempmap.put("jylxDm1", "罚没卷烟");
                    } else if (jylxDm1.equals("9")) {
                        tempmap.put("jylxDm1", "其他");
                    }
                    // final String jylxMc1 = GYCastUtils.getMcByDm("DM_SB_JYLX", jylxDm1, "JYLXMC_1");
                    // tempmap.put("jylxDm1", jylxMc1);
                }
                tablesMap.put("mxbGrid", mxbGrid);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbjypfqyyfxsmxqdprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB14bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // Sb_Xfs_Fb_Hzscjyxfsqkbgb
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryXfsFbHzscjyxfsqkbgb(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs000862_queryxfsnssbfbhzscjyxfsqkbgbxxbysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                Double xsslHJ = 0.0;
                Double xsjeHJ = 0.0;
                Double ppsrfyjnskHJ = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    xsslHJ = GYSbSdsUtils.add(xsslHJ,
                        GYCastUtils.notNull(tempmap.get("xssl")) ? GYSbSdsUtils.cDouble(tempmap.get("xssl")) : 0);
                    xsjeHJ = GYSbSdsUtils.add(xsjeHJ,
                        GYCastUtils.notNull(tempmap.get("xsje")) ? GYSbSdsUtils.cDouble(tempmap.get("xsje")) : 0);
                    ppsrfyjnskHJ = GYSbSdsUtils.add(ppsrfyjnskHJ, GYCastUtils.notNull(tempmap.get("ppsrfyjnsk"))
                        ? GYSbSdsUtils.cDouble(tempmap.get("ppsrfyjnsk")) : 0);
                }
                tablesMap.put("mxbGrid", mxbGrid);
                hjMap.put("xsslHJ", xsslHJ);
                hjMap.put("xsjeHJ", xsjeHJ);
                hjMap.put("ppsrfyjnskHJ", ppsrfyjnskHJ);
                fieldMap.putAll(hjMap);
                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0611111/sb001_xfs2019fbjyscqyhzscjyxfsqkbgprint.doc";
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private SbDyVO getBDA0611111FB15bySbuuid(String sbuuid, SbSbbDO sbbDO) {
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // 首先查询SB_SBB
        final Map<String, Object> sbbMap = getSbbMap(sbbDO);
        // ps.queryMapByKey("hdxt_dzswj_sbjs000192_querysbsbbbysbuuid", param);
        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // Sb_Fjsf
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryListFjsf(sbuuid);
            // ps.queryMapListByKey("hdxt_dzswj_sbjs001028_queryfjsfxxbysbuuid", param);
            if (GYCastUtils.notNull(mxbGrid)) {
                // 获取当期附加税中的普惠减免数据
                // 1.判断是否有普惠减免资格
                // final String djxh = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformDjxh"));
                // final String skssqq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqq"));
                // final String skssqz = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqz"));
                /*final Map<String, Object> retMap = GYPhjmUtil.hasPhjmzg(djxh, skssqq, skssqz);
                final String hasPhjmzg = GYSbSdsUtils.cast2StrNew(retMap.get("hasPhjmzg"));
                if ("Y".equals(hasPhjmzg)) {
                    fieldMap.put("sfph", "√是 □否");
                    for (Map<String, Object> tempmap : mxbGrid) {
                        final String zsxmDm = GYSbSdsUtils.cast2StrNew(tempmap.get("zsxmDm"));
                        tempmap.put("sfz", CacheUtils.dm2mc("dm_gy_zsxm", zsxmDm));
                
                        //2.获取普惠减免信息
                        final Map<String, Object> phjmMap = GYPhjmUtil.getPhjmxzJmfdSwsx("", zsxmDm, skssqq, skssqz);
                        final String phjmxzDm = GYSbSdsUtils.cast2StrNew(phjmMap.get("ssjmxzDm"));
                        final String phjmswsxDm = GYSbSdsUtils.cast2StrNew(phjmMap.get("swsxDm"));
                        final String phjzbl = GYSbSdsUtils.cast2StrNew(phjmMap.get("phjzbl"));
                        tempmap.put("phjmxzDm", phjmxzDm);
                        tempmap.put("phjmswsxDm", phjmswsxDm);
                        tempmap.put("phjzbl", phjzbl);
                    }
                } else {
                    fieldMap.put("sfph", "□是 √否");
                    for (Map<String, Object> tempmap : mxbGrid) {
                        final String zsxmDm = GYSbSdsUtils.cast2StrNew(tempmap.get("zsxmDm"));
                        tempmap.put("sfz", CacheUtils.dm2mc("dm_gy_zsxm", zsxmDm));
                    }
                }*/

                Double ybzzsHJ = 0.0;
                Double xfsHJ = 0.0;
                Double bqynsfeHJ = 0.0;
                Double jmeHJ = 0.0;
                Double bqyjseHJ = 0.0;
                Double bqybtseHJ = 0.0;
                Double phjmseHJ = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    final Double bqynsfe =
                        GYCastUtils.notNull(tempmap.get("bqynsfe")) ? GYSbSdsUtils.cDouble(tempmap.get("bqynsfe")) : 0;
                    final Double jme =
                        GYCastUtils.notNull(tempmap.get("jme")) ? GYSbSdsUtils.cDouble(tempmap.get("jme")) : 0;
                    final Double phjzbl =
                        GYCastUtils.notNull(tempmap.get("phjzbl")) ? GYSbSdsUtils.cDouble(tempmap.get("phjzbl")) : 0;
                    // 减征额 7=（3-5）×6
                    Double phjmse;
                    final Double temp1 = GYSbSdsUtils.subtract(bqynsfe, jme);
                    final Double temp2 = GYSbSdsUtils.multiple(temp1, phjzbl);
                    final Double bfs = 0.01;
                    phjmse = GYSbSdsUtils.multiple(temp2, bfs);
                    tempmap.put("phjmse", phjmse);
                    tempmap.put("ssjmxzDm",
                        CacheUtils.dm2mc("dm_gy_ssjmxz", GYSbSdsUtils.cast2StrNew(tempmap.get("ssjmxzDm"))));
                    // 计算合计值
                    ybzzsHJ = GYSbSdsUtils.add(ybzzsHJ,
                        GYCastUtils.notNull(tempmap.get("ybzzs")) ? GYSbSdsUtils.cDouble(tempmap.get("ybzzs")) : 0);
                    xfsHJ = GYSbSdsUtils.add(xfsHJ,
                        GYCastUtils.notNull(tempmap.get("xfs")) ? GYSbSdsUtils.cDouble(tempmap.get("xfs")) : 0);
                    bqynsfeHJ = GYSbSdsUtils.add(bqynsfeHJ,
                        GYCastUtils.notNull(tempmap.get("bqynsfe")) ? GYSbSdsUtils.cDouble(tempmap.get("bqynsfe")) : 0);
                    jmeHJ = GYSbSdsUtils.add(jmeHJ,
                        GYCastUtils.notNull(tempmap.get("jme")) ? GYSbSdsUtils.cDouble(tempmap.get("jme")) : 0);
                    bqyjseHJ = GYSbSdsUtils.add(bqyjseHJ,
                        GYCastUtils.notNull(tempmap.get("bqyjse")) ? GYSbSdsUtils.cDouble(tempmap.get("bqyjse")) : 0);
                    bqybtseHJ = GYSbSdsUtils.add(bqybtseHJ,
                        GYCastUtils.notNull(tempmap.get("bqybtse")) ? GYSbSdsUtils.cDouble(tempmap.get("bqybtse")) : 0);
                    phjmseHJ = GYSbSdsUtils.add(phjmseHJ,
                        GYCastUtils.notNull(tempmap.get("phjmse")) ? GYSbSdsUtils.cDouble(tempmap.get("phjmse")) : 0);
                    fieldMap.putAll(tempmap);
                }
                tablesMap.put("mxbGrid", mxbGrid);
                hjMap.put("ybzzsHJ", ybzzsHJ);
                hjMap.put("xfsHJ", xfsHJ);
                hjMap.put("bqynsfeHJ", bqynsfeHJ);
                hjMap.put("jmeHJ", jmeHJ);
                hjMap.put("bqyjseHJ", bqyjseHJ);
                hjMap.put("bqybtseHJ", bqybtseHJ);
                hjMap.put("phjmseHJ", phjmseHJ);
                fieldMap.putAll(hjMap);

                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                // 六税两费
                final Map<String, Object> lslfMap = new HashMap<>();
                // ps.queryMapByKey("hdxt_dzswj_sbjs001356_queryzzsjzzt", param);
                // 附加税费情况表 SB_FJSF_QTXX
                final Map<String, Object> fjsfqtxx = zzsYbnsrSbmxGjMapper.queryFjsfqtxx(sbuuid);
                if (GYCastUtils.notNull(fjsfqtxx)) {
                    lslfMap.put("bqsfsyxgmyhzc", fjsfqtxx.get("bqsfsyxgmyhzc"));
                    lslfMap.put("syxgmjzzcqssj", GYCastUtils.cast2Str(fjsfqtxx.get("syxgmjzzcqssj")).substring(0, 10));
                    lslfMap.put("syxgmjzzczzsj", GYCastUtils.cast2Str(fjsfqtxx.get("syxgmjzzczzsj")).substring(0, 10));
                    lslfMap.put("jzzcsyztDm", fjsfqtxx.get("jzzcsyztDm"));
                    if (!GyUtils.isNull(
                        CacheUtils.dm2mc("cs_sb_jzzcsyzt", GYSbSdsUtils.cast2StrNew(fjsfqtxx.get("jzzcsyztDm"))))) {
                        lslfMap.put("jzzcsyztmc",
                            CacheUtils.dm2mc("cs_sb_jzzcsyzt", GYSbSdsUtils.cast2StrNew(fjsfqtxx.get("jzzcsyztDm"))));
                    }
                }

                if (GYCastUtils.notNull(lslfMap)) {
                    lslf(lslfMap);
                    GYCastUtils.formatRq(lslfMap, "syxgmjzzcqssj");
                    GYCastUtils.formatRq(lslfMap, "syxgmjzzczzsj");
                    fieldMap.putAll(lslfMap);
                } else {
                    fieldMap.put("sfsy", "否");
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "/templates/sbdy/BDA0611111/sb001_xfsnssb2019fbfjsprint.doc";
        // 六税两费分支
        final String xtcsz = "N";
        // HxzgGyUtils.getXtcs("Z0000099Z02000259", WssbSessionUtils.getSwjgDm());
        if ("Y".equals(xtcsz)) {
            mblj = "/templates/sbdy/BDA0611111/sb001_xfsnssb2019fbfjsprint_lslf.doc";
        }
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 处理打印参数值中的科学计数法
     * @description 相关说明
     * @time 创建时间:2019-1-22下午04:31:34
     * @param list oldList
     * @param csMap 精度参数Map
     * @return list newList
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private List<SbDyVO> dealwithDyxxList(List<SbDyVO> list, Map<String, String> csMap) {
        final List<SbDyVO> newList = new ArrayList<>();
        if (GyUtils.isNotNull(list)) {
            for (SbDyVO vo : list) {
                final SbDyVO newVO = new SbDyVO();
                newVO.setMblj(vo.getMblj());
                newVO.setDyxx(filterMap(vo.getDyxx(), csMap));
                newList.add(newVO);
            }
        }
        return newList;
    }

    /**
     *
     * @name 递归处理Map数据
     * @description 递归处理Map数据
     * @time 创建时间:2019-1-22下午05:09:27
     * @param oldMap 旧Map
     * @param csMap 精度参数Map
     * @return newMap 新Map
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> filterMap(Map<String, Object> oldMap, Map<String, String> csMap) {
        final HashMap<String, Object> newMap = new HashMap<>();
        if (oldMap != null && !oldMap.isEmpty()) {
            for (String key : oldMap.keySet()) {
                final Object val = oldMap.get(key);
                if (val != null) {
                    if (val instanceof Double) {
                        // 如果是双精度且为科学计数法表示，尝试进行格式化 by:张俊，2019年1月23日12:27:46
                        // if (val.toString().indexOf("E") > -1) {
                        if (csMap.get(key) != null) {
                            // 如果能从精度参数Map中取到值，则使用参数Map中的配置进行格式化
                            newMap.put(key, (new DecimalFormat(csMap.get(key))).format(val));
                        } else {
                            // 否则默认保留2位小数
                            newMap.put(key, (new DecimalFormat("###,###,###,###,##0.00")).format(val));
                        }
                        /* } else {
                            if (csMap.get(key) != null) {
                                //如果能从精度参数Map中取到值，则使用参数Map中的配置进行格式化
                                newMap.put(key, (new DecimalFormat(csMap.get(key))).format(val));
                            } else {
                                newMap.put(key, (new DecimalFormat("###,###,###,###,##0.00")).format(val));
                            }
                        }*/
                    } else if (val instanceof BigDecimal) {
                        // if (val.toString().indexOf("E") > -1) {
                        if (csMap.get(key) != null) {
                            // 如果能从精度参数Map中取到值，则使用参数Map中的配置进行格式化
                            newMap.put(key, (new DecimalFormat(csMap.get(key))).format(val));
                        } else {
                            // 否则默认保留2位小数
                            newMap.put(key, (new DecimalFormat("###,###,###,###,##0.00")).format(val));
                        }
                        /*} else {
                            if (csMap.get(key) != null) {
                                //如果能从精度参数Map中取到值，则使用参数Map中的配置进行格式化
                                newMap.put(key, (new DecimalFormat(csMap.get(key))).format(val));
                            } else {
                                newMap.put(key, (new DecimalFormat("###,###,###,###,##0.00")).format(val));
                            }
                        }*/
                    } else if (val instanceof Map) {
                        // 如果是Map，递归调用
                        newMap.put(key, filterMap((Map<String, Object>)val, csMap));
                    } else if (val instanceof List) {
                        // 如果是List，循环递归调用
                        final List<Map<String, Object>> newList = new ArrayList<>();
                        if (((List<Map<String, Object>>)val).size() > 0) {
                            for (Map<String, Object> map : (List<Map<String, Object>>)val) {
                                newList.add(filterMap(map, csMap));
                            }
                        }
                        newMap.put(key, newList);
                    } else {
                        newMap.put(key, val);
                    }
                }
            }
        }
        return newMap;
    }

    /**
     *
     * @name 输出增值税一般人申报表主表打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getBDA0610606FB00(String sbuuid) {
        final Map<String, Object> res = new HashMap<>();
        SbDyVO vo = null;
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // sb_sbb
        final LambdaQueryWrapper<SbSbbDO> wrapperSbSbb = new LambdaQueryWrapper<>();
        wrapperSbSbb.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapperSbSbb);
        if (GyUtils.isNotNull(sbbDO)) {
            final Map<String, Object> sbbMap = new HashMap<>();
            sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
            sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
            sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
            sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
            sbbMap.put("nsrxxformTbrq1", GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter)
                : sbbDO.getTbrq1().format(formatter));
            sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
            sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
            sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
            sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
            sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
            sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
            sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
            res.put("sbbxx", sbbDO);
            // 先调用api接口
            ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo = new ZnsbMhzcQyjbxxmxReqVO();
            znsbmhzcqyjbxxmxreqvo.setDjxh(GYCastUtils.cast2Str(sbbDO.getDjxh()));
            znsbmhzcqyjbxxmxreqvo.setNsrsbh(sbbDO.getNsrsbh());
            znsbmhzcqyjbxxmxreqvo.setFhNsrJbxx(true);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> resQyjbxx = nsrxxApi.getNsrxxByNsrsbh(znsbmhzcqyjbxxmxreqvo);
            List<JbxxmxsjVO> list = resQyjbxx.getData().getJbxxmxsj();
            // List<JbxxmxsjVO> list = new ArrayList<JbxxmxsjVO>();
            log.debug("纳税人信息" + list);
            if (!GyUtils.isNull(list)) {
                JbxxmxsjVO jbxxmxsjVO = list.get(0);
                sbbMap.put("nsrxxformHymc", jbxxmxsjVO.getHymc());
                sbbMap.put("nsrxxformZcdz", jbxxmxsjVO.getZcdz());
                sbbMap.put("nsrxxformScjydz", jbxxmxsjVO.getScjydz());
                sbbMap.put("nsrxxformDjzclx", jbxxmxsjVO.getDjzclxmc());
            } else {
                // znsb_mhzc_qyjbxxmx
                final LambdaQueryWrapper<ZnsbMhzcQyjbxxmxDTO> qyjbxxWrapper = new LambdaQueryWrapper<>();
                qyjbxxWrapper.eq(ZnsbMhzcQyjbxxmxDTO::getDjxh, sbbDO.getDjxh());
                ZnsbMhzcQyjbxxmxDTO znsbMhzcQyjbxxmxDTO = znsbMhzcQyjbxxmxMapper.selectOne(qyjbxxWrapper);

                sbbMap.put("nsrxxformHymc", znsbMhzcQyjbxxmxDTO.getHymc());
                sbbMap.put("nsrxxformZcdz", znsbMhzcQyjbxxmxDTO.getZcdz());
                sbbMap.put("nsrxxformScjydz", znsbMhzcQyjbxxmxDTO.getScjydz());
                sbbMap.put("nsrxxformDjzclx", znsbMhzcQyjbxxmxDTO.getDjzclxmc());
            }
            // res.put("nsrxx", znsbMhzcQyjbxxmxDTO);
            // SB_SLXX
            final LambdaQueryWrapper<SbSlxxDTO> slxxWrapper = new LambdaQueryWrapper<>();
            slxxWrapper.eq(SbSlxxDTO::getSbuuid, sbuuid);
            List<SbSlxxDTO> slxxDTO = sbSlxxMapper.selectList(slxxWrapper);
            if (GyUtils.isNotNull(slxxDTO)) {
                sbbMap.put("slr", slxxDTO.get(0).getSlrxm());
                sbbMap.put("slxxformSlrq", slxxDTO.get(0).getSlrq());
                sbbMap.put("slswjg", slxxDTO.get(0).getSlswjgmc());
            }
            // sres.put("slxx", slxxDTO);
            // SB_ZZS_YBNSR
            final LambdaQueryWrapper<SbZzsYbnsrSjgjDTO> sbzzsybnsrWrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrWrapper.eq(SbZzsYbnsrSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrSjgjDTO> zzsList = zzsYbnsrSbmxGjMapper.selectList(sbzzsybnsrWrapper);
            if (GyUtils.isNotNull(zzsList)) {
                for (SbZzsYbnsrSjgjDTO dto : zzsList) {
                    final Long ewblxh = dto.getEwblxh();
                    final Map<String, Object> map = BeanUtils.toBean(dto, Map.class);
                    for (String key : map.keySet()) {
                        // 以key+二维表列序号的方式组合到打印信息中，例如key为msxse，那么第二列该项的名称为msxse2
                        fieldMap.put(key + ewblxh.toString(), map.get(key));
                    }
                }
                fieldMap.putAll(sbbMap);
            }
            /*for (String keytemp : fieldMap.keySet()) {
                String tempv = GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp));
                if ("0.0".equals(tempv)) {
                    tempv = "0.00";
                }
                fieldMap.put(keytemp, tempv);
            }*/
            dyxx.put("field", fieldMap);
            dyxx.put("tables", tablesMap);
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb00_print_zzbd.doc";
        if (GyUtils.isNotNull(dyxx)) {
            final String skssqz = GYSbSdsUtils.cast2StrNew(fieldMap.get("nsrxxformSkssqz"));
            final boolean sfQyFjsfb = sfQyFjsfb(skssqz);
            if (sfQyFjsfb) {
                mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb002_print_zzbd.doc";
            }
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        res.put("sbdyvo", vo);
        return res;
    }

    @Override
    public List<SbSbbDO> querySbSbbGroupByDjxhSkssq(List<SbxxcxTbbbReqVO> reqVO) {
        List<BigDecimal> djxhList = new ArrayList<>();
        List<String> skssqqList = new ArrayList<>();
        List<String> skssqzList = new ArrayList<>();
        for (SbxxcxTbbbReqVO t : reqVO) {
            if (!djxhList.contains(t.getDjxh())) {
                djxhList.add(new BigDecimal(t.getDjxh()));
            }
            if (!skssqqList.contains(t.getSkssqq())) {
                skssqqList.add(t.getSkssqq());
            }
            if (!skssqzList.contains(t.getSkssqz())) {
                skssqzList.add(t.getSkssqz());
            }
        }
        List<SbSbbDO> sbSbbDOS = sbSbbDOMapper.selectGroupByDjxhSkssq(djxhList, skssqqList, skssqzList);
        return sbSbbDOS;
    }

    @Override
    public List<SbDyVO> getBDA0611148bySbuuid(String sbuuid) {
        List<SbDyVO> dyxxList = new ArrayList<>();
        SbSbbDO sbbDO = sbSbbDOMapper.querySbbBySbuuid(sbuuid);
        final List<Map<String, Object>> cxsList = zzsYbnsrSbmxGjMapper.queryCxsxxByPzxh(sbbDO.getPzxh());
        if (GyUtils.isNotNull(cxsList)) {
            final String cxstysbuuid = (String)cxsList.get(0).get("cxstysbuuid");
            dyxxList = getBDA0611148bySbuuid(sbuuid, cxstysbuuid);
        }
        return dyxxList;
    }

    @Override
    public void exportSbmxxx(BbxzQueryVO bbxzQueryVO, HttpServletResponse response) {
        String dzbzdszlDm = bbxzQueryVO.getDzbzdszlDm();
        if ("BDA0611148".equals(dzbzdszlDm) || "BDA0610794".equals(dzbzdszlDm)
                || "BDA0611116".equals(dzbzdszlDm)) {// 财产和行为税纳税申报表
            exportBDA0611148Sbmxxx(bbxzQueryVO,response);
        } else if("BDA0610865".equals(dzbzdszlDm)) {// 增值税预缴
            exportBDA0610865Sbmxxx(bbxzQueryVO,response);
        }
    }

    public void exportBDA0611148Sbmxxx(BbxzQueryVO bbxzQueryVO, HttpServletResponse response) {
        final List<SbDyVO> list = this.getBDA0611148bySbuuid(bbxzQueryVO.getSbuuid());
        final Map<String, Object> dyxx = list.get(0).getDyxx();
        final Map<String, Object> exportFieldData = (Map<String, Object>) dyxx.get("field");
        if(GyUtils.isNotNull(exportFieldData.get("sbrq1"))){
            final LocalDateTime sbrq1 = (LocalDateTime) exportFieldData.get("sbrq1");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            exportFieldData.put("sbrq1",sbrq1.format(formatter));
        }
        final Map<String, Object> tables = (Map<String, Object>) dyxx.get("tables");
        final List<Map<String, Object>> exportTableData = (List<Map<String, Object>>) tables.get("cxsGrid");

        final Map<String, Object> jmDyxx = list.get(1).getDyxx();
        final Map<String, Object> jmExportFieldData = (Map<String, Object>) jmDyxx.get("field");
        exportFieldData.put("cztdsHJ",jmExportFieldData.get("cztdsHJ"));
        exportFieldData.put("fcsHJ",jmExportFieldData.get("fcsHJ"));
        exportFieldData.put("yhsHJ",jmExportFieldData.get("yhsHJ"));
        exportFieldData.put("hjbhsHJ",jmExportFieldData.get("hjbhsHJ"));
        final Map<String, Object> jmTables = (Map<String, Object>) jmDyxx.get("tables");
        final List<Map<String, Object>> jmExportTableData = new ArrayList<>();
        if(GyUtils.isNotNull(jmTables.get("cztdsysGrid"))){
            final List<Map<String, Object>> cztdsysGrid =  (List<Map<String, Object>>) jmTables.get("cztdsysGrid");
            for(Map<String, Object> map : cztdsysGrid){
                Map<String, Object> jmMap = new HashMap<>();
                jmMap.put("tdxh",map.get("xh"));
                jmMap.put("tdbh",map.get("sybh"));
                jmMap.put("tdskssqq",map.get("skssqq"));
                jmMap.put("tdskssqz",map.get("skssqz"));
                jmMap.put("tdssjmxzDm",map.get("ssjmxzDm"));
                jmMap.put("tdjmse",map.get("jmse"));
                jmExportTableData.add(jmMap);
            }
        } else if (GyUtils.isNotNull(jmTables.get("fcsGrid"))){
            final List<Map<String, Object>> fcsGrid =  (List<Map<String, Object>>) jmTables.get("fcsGrid");
            for(Map<String, Object> map : fcsGrid){
                Map<String, Object> jmMap = new HashMap<>();
                jmMap.put("fyxh",map.get("xh"));
                jmMap.put("fybh",map.get("sybh"));
                jmMap.put("fyskssqq",map.get("skssqq"));
                jmMap.put("fyskssqz",map.get("skssqz"));
                jmMap.put("fyssjmxzDm",map.get("ssjmxzDm"));
                jmMap.put("fyjmse",map.get("jmse"));
                jmExportTableData.add(jmMap);
            }
        } else if (GyUtils.isNotNull(jmTables.get("yhsGrid"))){
            final List<Map<String, Object>> yhsGrid =  (List<Map<String, Object>>) jmTables.get("yhsGrid");
            for(Map<String, Object> map : yhsGrid){
                Map<String, Object> jmMap = new HashMap<>();
                jmMap.put("yhxh",map.get("xh"));
                jmMap.put("yszspmDm",map.get("zspmDm"));
                jmMap.put("ysskssqq",map.get("skssqq"));
                jmMap.put("ysskssqz",map.get("skssqz"));
                jmMap.put("ysssjmxzDm",map.get("ssjmxzDm"));
                jmMap.put("ysjmse",map.get("jmse"));
                jmExportTableData.add(jmMap);
            }
        } else if (GyUtils.isNotNull(jmTables.get("hjbhsGrid"))){
            final List<Map<String, Object>> hjbhsGrid =  (List<Map<String, Object>>) jmTables.get("hjbhsGrid");
            for(Map<String, Object> map : hjbhsGrid){
                Map<String, Object> jmMap = new HashMap<>();
                jmMap.put("hbxh",map.get("xh"));
                jmMap.put("hbsybh",map.get("sybh"));
                jmMap.put("zywrwlbDm",map.get("zywrwlbDm"));
                jmMap.put("hbzspmDm",map.get("zspmDm"));
                jmMap.put("hbskssqq",map.get("skssqq"));
                jmMap.put("hbskssqz",map.get("skssqz"));
                jmMap.put("hnssjmxzDm",map.get("ssjmxzDm"));
                jmMap.put("hbjmse",map.get("jmse"));
                jmExportTableData.add(jmMap);
            }
        }


        final String mblj = File.separator + "templates" + File.separator + "sbxxcx"
                + File.separator + "cxsnssbb" + ".xlsx";

        try {
            ServletOutputStream out = response.getOutputStream();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            //设置编码格式
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("申报信息", "UTF-8") + ".xlsx");
            InputStream inputStreamTemp = new ClassPathResource(mblj).getInputStream();
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inputStreamTemp).build();
            //申报表
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(exportTableData, fillConfig, writeSheet);
            excelWriter.fill(exportFieldData, writeSheet);
            //减免明细
            WriteSheet writeSheet2 = EasyExcel.writerSheet(1).build();
            FillConfig fillConfig2 = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(jmExportTableData, fillConfig2, writeSheet2);
            excelWriter.fill(exportFieldData, writeSheet2);

            excelWriter.finish();
            out.flush();
        } catch (IOException e) {
            log.error("导出失败，", e);
        }
    }

    public void exportBDA0610865Sbmxxx(BbxzQueryVO bbxzQueryVO, HttpServletResponse response) {
        final List<SbDyVO> list = this.getBDA0610865bySbuuid(bbxzQueryVO.getSbuuid());
        final Map<String, Object> dyxx = list.get(0).getDyxx();
        final Map<String, Object> exportFieldData = (Map<String, Object>) dyxx.get("field");
        final Map<String, Object> tables = (Map<String, Object>) dyxx.get("tables");
        final List<Map<String, Object>> exportTableData = new ArrayList<>();
        if(GyUtils.isNotNull(tables.get("yzxmGrid"))){
            final List<Map<String, Object>> yzxmGrid = (List<Map<String, Object>>) tables.get("yzxmGrid");
            for(Map<String, Object> map : yzxmGrid){
                Map<String, Object> jmMap = new HashMap<>();
                jmMap.put("kcje",map.get("kcje"));
                jmMap.put("yzxmDm",map.get("yzxmDm"));
                jmMap.put("lc",map.get("lc"));
                jmMap.put("yzl",map.get("yzl"));
                jmMap.put("xse",map.get("xse"));
                jmMap.put("yzse",map.get("yzse"));
                exportTableData.add(jmMap);
            }
        }

        final Map<String, Object> mxDyxx = list.get(1).getDyxx();
        final Map<String, Object> mxExportFieldData = (Map<String, Object>) mxDyxx.get("field");
        final Map<String, Object> mxTables = (Map<String, Object>) mxDyxx.get("tables");
        final List<Map<String, Object>> mxExportTableData = new ArrayList<>();
        if(GyUtils.isNotNull(mxTables.get("mxbGrid"))){
            final List<Map<String, Object>> mxbGrid =  (List<Map<String, Object>>) mxTables.get("mxbGrid");
            for(Map<String, Object> map : mxbGrid){
                Map<String, Object> jmMap = new HashMap<>();
                jmMap.put("jme",map.get("jme"));
                jmMap.put("bqybtse",map.get("bqybtse"));
                jmMap.put("phjmse",map.get("phjmse"));
                jmMap.put("phjzbl",map.get("phjzbl"));
                jmMap.put("sl1",map.get("sl1"));
                jmMap.put("zsxmdm",map.get("zsxmdm"));
                jmMap.put("ybzzs",map.get("ybzzs"));
                jmMap.put("bqynsfe",map.get("bqynsfe"));
                jmMap.put("ssjmxzdm", "");
                if(GyUtils.isNotNull(map.get("ssjmxzdm")))
                    jmMap.put("ssjmxzdm",map.get("ssjmxzdm"));
                mxExportTableData.add(jmMap);
            }
        }

        final String mblj = File.separator + "templates" + File.separator + "sbdy"
                + File.separator + "BDA0610865" + File.separator + "sb207_zzsyjnssb_print" + ".xlsx";

        try {
            ServletOutputStream out = response.getOutputStream();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            //设置编码格式
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("申报信息", "UTF-8") + ".xlsx");
            InputStream inputStreamTemp = new ClassPathResource(mblj).getInputStream();
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inputStreamTemp).build();
            //申报表
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(exportTableData, fillConfig, writeSheet);
            excelWriter.fill(exportFieldData, writeSheet);
            //明细
            WriteSheet writeSheet2 = EasyExcel.writerSheet(1).build();
            FillConfig fillConfig2 = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(mxExportTableData, fillConfig2, writeSheet2);
            excelWriter.fill(mxExportFieldData, writeSheet2);

            excelWriter.finish();
            out.flush();
        } catch (IOException e) {
            log.error("导出失败，", e);
        }
    }

    private Map<String, Object> getSbbMap(SbSbbDO sbbDO) {
        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        return sbbMap;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表1打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    private SbDyVO getBDA0610606FB01(String sbuuid, @NotNull SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb01_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB01(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        final String skssqz = GYSbSdsUtils.cast2StrNew(sbbDO.getSkssqz().format(formatter));
        if (GYCastUtils.notNull(sbbMap)) {
            // sb_zzs_ybnsr_fb1_bqxsqkmx
            final LambdaQueryWrapper<SbZzsYbnsrFb1BqxsqkmxSjgjDTO> sbzzsybnsrfb1Wrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrfb1Wrapper.eq(SbZzsYbnsrFb1BqxsqkmxSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrFb1BqxsqkmxSjgjDTO> zzsfb1List =
                sbZzsYbnsrFb1BqxsqkmxMapper.selectList(sbzzsybnsrfb1Wrapper);
            if (GyUtils.isNotNull(zzsfb1List)) {
                for (SbZzsYbnsrFb1BqxsqkmxSjgjDTO dto : zzsfb1List) {
                    final Long ewblxh = dto.getEwbhxh();
                    final Map<String, Object> map = BeanUtils.toBean(dto, Map.class);
                    for (String key : map.keySet()) {
                        // 以key+二维表列序号的方式组合到打印信息中，例如key为msxse，那么第二列该项的名称为msxse2
                        fieldMap.put(key + ewblxh.toString(), map.get(key));
                    }
                }
                if (skssqz.compareTo("2019-04-01") >= 0) {
                    fieldMap.put("hmc1", "13%税率的货物及加工修理修配劳务");
                    fieldMap.put("hmc2", "13%税率的服务、不动产和无形资产");
                    fieldMap.put("hmc23", "9%税率的货物及加工修理修配劳务");
                    fieldMap.put("hmc4", "9%税率的服务、不动产和无形资产");
                } else if (skssqz.compareTo("2018-05-01") >= 0) {
                    fieldMap.put("hmc1", "16%税率的货物及加工修理修配劳务");
                    fieldMap.put("hmc2", "16%税率的服务、不动产和无形资产");
                    fieldMap.put("hmc23", "10%税率的货物及加工修理修配劳务");
                    fieldMap.put("hmc4", "10%税率的服务、不动产和无形资产");
                } else {
                    fieldMap.put("hmc1", "17%税率的货物及加工修理修配劳务");
                    fieldMap.put("hmc2", "17%税率的服务、不动产和无形资产");
                    fieldMap.put("hmc23", "11%税率的货物及加工修理修配劳务");
                    fieldMap.put("hmc4", "11%税率的服务、不动产和无形资产");
                }
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        if (GyUtils.isNotNull(dyxx)) {
            // 如果属期是2019年以前的，那么使用旧模板输出
            if (skssqz.compareTo("2019-04-01") >= 0) {
                mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb01_print_zzbdNew.doc";
            }
            final boolean sfQyFjsfb = sfQyFjsfb(skssqz);
            if (sfQyFjsfb) {
                mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb012_print_zzbdNew.doc";
            }
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表2打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    private SbDyVO getBDA0610606FB02(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        final String skssqz = GYSbSdsUtils.cast2StrNew(sbbDO.getSkssqz().format(formatter));

        if (GYCastUtils.notNull(sbbMap)) {
            // sb_zzs_ybnsr_fb2_bqjxsemx
            final LambdaQueryWrapper<SbZzsYbnsrFb2BqjxsemxSjgjDTO> sbzzsybnsrfb2Wrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrfb2Wrapper.eq(SbZzsYbnsrFb2BqjxsemxSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrFb2BqjxsemxSjgjDTO> zzsList = sbZzsYbnsrFb2BqjxsemxMapper.selectList(sbzzsybnsrfb2Wrapper);

            if (GyUtils.isNotNull(zzsList)) {
                for (SbZzsYbnsrFb2BqjxsemxSjgjDTO dto : zzsList) {
                    final Long ewblxh = dto.getEwbhxh();
                    final Map<String, Object> map = BeanUtils.toBean(dto, Map.class);
                    for (String key : map.keySet()) {
                        if ("fs".equals(key)) {
                            final Double fs =
                                GYCastUtils.cast2Double(GYCastUtils.notNull(map.get(key)) ? map.get(key) : 0);
                            map.put(key, GYSbSdsUtils.cast2StrNew(fs.intValue()));
                        }
                        // 以key+二维表列序号的方式组合到打印信息中，例如key为msxse，那么第二列该项的名称为msxse2
                        fieldMap.put(key + ewblxh.toString(), map.get(key));
                    }
                }
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb02_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB02(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            if (skssqz.compareTo("2019-04-01") >= 0) {
                mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb02_print_zzbdnew.doc";
            }
            final boolean sfQyFjsfb = sfQyFjsfb(skssqz);
            if (sfQyFjsfb) {
                mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb022_print_zzbdnew.doc";
            }
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表3打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    private SbDyVO getBDA0610606FB03(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        final String skssqz = GYSbSdsUtils.cast2StrNew(sbbDO.getSkssqz().format(formatter));

        if (GYCastUtils.notNull(sbbMap)) {
            // sb_zzs_ybnsr_fb3_ysfwkcxm
            final LambdaQueryWrapper<SbZzsYbnsrFb3YsfwkcxmSjgjDTO> sbzzsybnsrWrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrWrapper.eq(SbZzsYbnsrFb3YsfwkcxmSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrFb3YsfwkcxmSjgjDTO> zzsList = sbZzsYbnsrFb3YsfwkcxmMapper.selectList(sbzzsybnsrWrapper);

            if (GyUtils.isNotNull(zzsList)) {
                for (SbZzsYbnsrFb3YsfwkcxmSjgjDTO dto : zzsList) {
                    final Long ewblxh = dto.getEwbhxh();
                    final Map<String, Object> map = BeanUtils.toBean(dto, Map.class);
                    for (String key : map.keySet()) {
                        // 以key+二维表列序号的方式组合到打印信息中，例如key为msxse，那么第二列该项的名称为msxse2
                        fieldMap.put(key + ewblxh.toString(), map.get(key));
                    }
                }
                if (skssqz.compareTo("2019-04-01") >= 0) {
                    fieldMap.put("hmc1", "13%税率的项目");
                    fieldMap.put("hmc2", "9%税率的项目");
                } else if (skssqz.compareTo("2018-05-01") >= 0) {
                    fieldMap.put("hmc1", "16%税率的项目");
                    fieldMap.put("hmc2", "10%税率的项目");
                } else {
                    fieldMap.put("hmc1", "17%税率的项目");
                    fieldMap.put("hmc2", "11%税率的项目");
                }
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb03_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB03(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            final boolean sfQyFjsfb = sfQyFjsfb(skssqz);
            if (sfQyFjsfb) {
                mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb032_print_zzbd.doc";
            }
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表4打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    private SbDyVO getBDA0610606FB04(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        final String skssqz = GYSbSdsUtils.cast2StrNew(sbbDO.getSkssqz().format(formatter));

        if (GYCastUtils.notNull(sbbMap)) {
            // sb_zzs_ybnsr_fb4_sedjqk
            final LambdaQueryWrapper<SbZzsYbnsrFb4SedjqkSjgjDTO> sbzzsybnsrWrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrWrapper.eq(SbZzsYbnsrFb4SedjqkSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrFb4SedjqkSjgjDTO> zzsList = sbZzsYbnsrFb4SedjqkMapper.selectList(sbzzsybnsrWrapper);
            if (GyUtils.isNotNull(zzsList)) {
                for (SbZzsYbnsrFb4SedjqkSjgjDTO dto : zzsList) {
                    final Long ewblxh = dto.getEwbhxh();
                    final Map<String, Object> map = BeanUtils.toBean(dto, Map.class);
                    for (String key : map.keySet()) {
                        // 以key+二维表列序号的方式组合到打印信息中，例如key为msxse，那么第二列该项的名称为msxse2
                        fieldMap.put(key + ewblxh.toString(), map.get(key));
                    }
                }
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb04_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB04(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            if (skssqz.compareTo("2019-04-01") >= 0) {
                mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb04_print_zzbd_2019.doc";
            }
            final boolean sfQyFjsfb = sfQyFjsfb(skssqz);
            if (sfQyFjsfb) {
                mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb042_print_zzbd_2019.doc";
            }
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表05 固定资产（不含不动产）进项税额抵扣情况表 打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB05(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb05_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB05(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表06 航空运输企业分支机构传递单 打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB06(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        // final String skssqz = GYSbSdsUtils.cast2StrNew(sbbDO.getSkssqz().format(formatter));

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_HKYS_YJN
            final LambdaQueryWrapper<SbZzsYbnsrFbHkysYjnSjgjDTO> sbzzsybnsrYjnWrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrYjnWrapper.eq(SbZzsYbnsrFbHkysYjnSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrFbHkysYjnSjgjDTO> zzsListYjn = sbZzsYbnsrFbHkysYjnMapper.selectList(sbzzsybnsrYjnWrapper);
            if (GyUtils.isNotNull(zzsListYjn)) {
                for (int j = 0; j < zzsListYjn.size(); j++) {
                    final String zsxm = zzsListYjn.get(j).getZsxm();
                    if (zsxm.equals("其他")) {
                        final SbZzsYbnsrFbHkysYjnSjgjDTO yjnzsxmQt = zzsListYjn.get(j);
                        fieldMap.put("qtxse", yjnzsxmQt.getXse());
                        fieldMap.put("qtyzl", yjnzsxmQt.getYzl());
                        fieldMap.put("qtyjse", yjnzsxmQt.getYjse1());
                        fieldMap.put("qtbz", yjnzsxmQt.getBz());
                    } else if (zsxm.equals("有形")) {
                        final SbZzsYbnsrFbHkysYjnSjgjDTO yjnzsxmYx = zzsListYjn.get(j);
                        fieldMap.put("yxxse", yjnzsxmYx.getXse());
                        fieldMap.put("yxyzl", yjnzsxmYx.getYzl());
                        fieldMap.put("yxyjse", yjnzsxmYx.getYjse1());
                        fieldMap.put("yxbz", yjnzsxmYx.getBz());
                    } else if (zsxm.equals("交通")) {
                        final SbZzsYbnsrFbHkysYjnSjgjDTO yjnzsxmJt = zzsListYjn.get(j);
                        fieldMap.put("jtxse", yjnzsxmJt.getXse());
                        fieldMap.put("jtyzl", yjnzsxmJt.getYzl());
                        fieldMap.put("jtyjse", yjnzsxmJt.getYjse1());
                        fieldMap.put("jtbz", yjnzsxmJt.getBz());
                    } else if (zsxm.equals("小计")) {
                        final SbZzsYbnsrFbHkysYjnSjgjDTO yjnzsxmXj = zzsListYjn.get(j);
                        fieldMap.put("xsexj", yjnzsxmXj.getXse());
                        fieldMap.put("yjsexj", yjnzsxmXj.getYjse1());
                        fieldMap.put("bjsexj", yjnzsxmXj.getBjse());
                    }
                }
            }
            // SB_ZZS_YBNSR_FB_HKYS_QDJX
            final LambdaQueryWrapper<SbZzsYbnsrFbHkysQdjxSjgjDTO> sbzzsybnsrWrapperQdjx = new LambdaQueryWrapper<>();
            sbzzsybnsrWrapperQdjx.eq(SbZzsYbnsrFbHkysQdjxSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrFbHkysQdjxSjgjDTO> zzsListQdjx =
                sbZzsYbnsrFbHkysQdjxMapper.selectList(sbzzsybnsrWrapperQdjx);
            List<String> json = JsonUtils.toJsonList(zzsListQdjx);
            if (GyUtils.isNotNull(zzsListQdjx)) {
                final List<Map<String, Object>> fsddqQdjxseqkGrid = JsonUtils.toList(json);
                tablesMap.put("fsddqQdjxseqkGrid", fsddqQdjxseqkGrid);
                dyxx.put("tables", tablesMap);
            }
            if (GyUtils.isNotNull(zzsListQdjx) || GyUtils.isNotNull(zzsListYjn)) {
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb06_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB06(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税小规模申报表附表7打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610611FB07(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_FB_ZZSJMSSBMXB_JSXM
            final LambdaQueryWrapper<SbZzsFbZzsjmssbmxbJsxmSjgjDTO> sbzzsybnsrJsxmWrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrJsxmWrapper.eq(SbZzsFbZzsjmssbmxbJsxmSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsFbZzsjmssbmxbJsxmSjgjDTO> zzsListJsxm =
                sbZzsFbZzsjmssbmxbJsxmMapper.selectList(sbzzsybnsrJsxmWrapper);
            if (GyUtils.isNotNull(zzsListJsxm)) {
                final List<Map<String, Object>> zzsjmssbmxbjsxmGrid = new ArrayList<>();
                for (SbZzsFbZzsjmssbmxbJsxmSjgjDTO dto : zzsListJsxm) {
                    final Long ewbhxh = dto.getEwbhxh();
                    final String hmc = dto.getHmc();
                    if (ewbhxh == 1 || "合计".equals(hmc)) {
                        fieldMap.put("qcyeHJ", dto.getQcye());
                        fieldMap.put("bqfseHJ", dto.getBqfse());
                        fieldMap.put("bqydjseHJ", dto.getBqydjse());
                        fieldMap.put("bqsjdjseHJ", dto.getBqsjdjse());
                        fieldMap.put("qmyeHJ", dto.getQmye());
                    } else {
                        final Map<String, Object> jsMap = BeanUtils.toBean(dto, Map.class);
                        String skssqq = sbbDO.getSkssqq().format(formatter);
                        String skssqz = sbbDO.getSkssqz().format(formatter);
                        String jmzlxDm = "01";
                        String ssjmxzDm = dto.getHmc();
                        String swsxDm = dto.getSwsxDm();
                        jsMap.put("ssjmxzDm", getSsjmxzmc(skssqq, skssqz, jmzlxDm, ssjmxzDm, swsxDm));
                        jsMap.put("lc", ewbhxh - 1);

                        zzsjmssbmxbjsxmGrid.add(jsMap);
                    }
                }
                tablesMap.put("zzsjmssbmxbjsxmGrid", zzsjmssbmxbjsxmGrid);
            }
            // SB_ZZS_FB_ZZSJMSSBMXB_MSXM
            final LambdaQueryWrapper<SbZzsFbZzsjmssbmxbMsxmSjgjDTO> sbzzsybnsrMsxmWrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrMsxmWrapper.eq(SbZzsFbZzsjmssbmxbMsxmSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsFbZzsjmssbmxbMsxmSjgjDTO> zzsMsxmList =
                sbZzsFbZzsjmssbmxbMsxmMapper.selectList(sbzzsybnsrMsxmWrapper);
            if (GyUtils.isNotNull(zzsMsxmList)) {
                final List<Map<String, Object>> zzsjmssbmxbmsxmGrid = new ArrayList<>();
                for (SbZzsFbZzsjmssbmxbMsxmSjgjDTO dto : zzsMsxmList) {
                    final Long ewbhxh = dto.getEwbhxh();
                    final String hmc = dto.getHmc();
                    if (ewbhxh == 1 || "合计".equals(hmc)) {
                        fieldMap.put("mzzzsxmxseHJ", dto.getMzzzsxmxse());
                        fieldMap.put("bqsjkcjeHJ", dto.getBqsjkcje());
                        fieldMap.put("kchmsxseHJ", dto.getKchmsxse());
                        fieldMap.put("msxsedyjxseHJ", dto.getMsxsedyjxse());
                        fieldMap.put("mseHJ", dto.getMse());
                    } else if (ewbhxh == 2 || "出口免税".equals(hmc)) {
                        fieldMap.put("ckms", dto.getMzzzsxmxse());
                    } else if (ewbhxh == 3 || "其中:跨境服务".equals(hmc)) {
                        fieldMap.put("qzKjfw", dto.getMzzzsxmxse());
                    } else {
                        final Map<String, Object> msMap = BeanUtils.toBean(dto, Map.class);
                        String skssqq = sbbDO.getSkssqq().format(formatter);
                        String skssqz = sbbDO.getSkssqz().format(formatter);
                        String jmzlxDm = "02";
                        String ssjmxzDm = dto.getHmc();
                        String swsxDm = dto.getSwsxDm();
                        msMap.put("ssjmxzDm", getSsjmxzmc(skssqq, skssqz, jmzlxDm, ssjmxzDm, swsxDm));
                        msMap.put("lc", ewbhxh - 3);
                        zzsjmssbmxbmsxmGrid.add(msMap);
                    }
                }
                tablesMap.put("zzsjmssbmxbmsxmGrid", zzsjmssbmxbmsxmGrid);
            }
            if (GYCastUtils.notNull(fieldMap)) {
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610611/sb2031_zzsxgmnsrsbygz07_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610611FB07(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    private String getSsjmxzmc(String skssqq, String skssqz, String jmzlxDm, String ssjmxzDm, String swsxDm) {
        ZzsJmxxReqVO reqVO = new ZzsJmxxReqVO();
        reqVO.setSkssqq(skssqq);
        reqVO.setSkssqz(skssqz);
        reqVO.setJmlxDm(jmzlxDm);
        List<JmxxDTO> jmxxList = zzsYbnsrService.getZzsJmxx(reqVO);
        if (GyUtils.isNotNull(jmxxList)) {
            List<JmxxDTO> glhJmxxList = jmxxList.stream()
                .filter(
                    t -> t.getLabel().contains(ssjmxzDm) && (GyUtils.isNull(swsxDm) || t.getLabel().contains(swsxDm)))
                .collect(Collectors.toList());
            if (GyUtils.isNotNull(glhJmxxList)) {
                return glhJmxxList.get(0).getLabel();
            }
        }
        return null;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表08 成品油购销存情况明细表 打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB08(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // SbZzsYbnsrFbCpygxcqkMapper 查询表Sb_Zzs_Ybnsr_Fb_Cpygxcqk 增值税一般纳税人--成品油购销存情况明细表
            final LambdaQueryWrapper<SbZzsYbnsrFbCpygxcqkSjgjDTO> sbzzsybnsrWrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrWrapper.eq(SbZzsYbnsrFbCpygxcqkSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrFbCpygxcqkSjgjDTO> zzsList = sbZzsYbnsrFbCpygxcqkMapper.selectList(sbzzsybnsrWrapper);
            if (GyUtils.isNotNull(zzsList)) {
                final List<Map<String, Object>> cpygxcqkmxbGrid = new ArrayList<>();
                Double qckcZgjeHj = 0.0;
                Double bqrkZgjeHj = 0.0;
                Double bqckYsbfjeHj = 0.0;
                Double bqckFysbfZyjeHj = 0.0;
                Double qmkcZgjeHj = 0.0;
                // 计算销售价格合计
                for (SbZzsYbnsrFbCpygxcqkSjgjDTO dto : zzsList) {
                    final String ypxh = CacheUtils.dm2mc("dm_sb_cpycpzl", dto.getYpxh());
                    dto.setYpxh(ypxh);
                    final Double qckcZgje = GyUtils.isNull(dto.getQckcZgje()) ? 0.0 : dto.getQckcZgje();
                    final Double bqrkZgje = GyUtils.isNull(dto.getBqrkZgje()) ? 0.0 : dto.getBqrkZgje();
                    final Double bqckYsbfje = GyUtils.isNull(dto.getBqckYsbfje()) ? 0.0 : dto.getBqckYsbfje();
                    final Double bqckFysbfZyje = GyUtils.isNull(dto.getBqckFysbfZyje()) ? 0.0 : dto.getBqckFysbfZyje();
                    final Double qmkcZgje = GyUtils.isNull(dto.getQmkcZgje()) ? 0.0 : dto.getQmkcZgje();
                    qckcZgjeHj = GYCastUtils.add(qckcZgjeHj, qckcZgje);
                    bqrkZgjeHj = GYCastUtils.add(bqrkZgjeHj, bqrkZgje);
                    bqckYsbfjeHj = GYCastUtils.add(bqckYsbfjeHj, bqckYsbfje);
                    bqckFysbfZyjeHj = GYCastUtils.add(bqckFysbfZyjeHj, bqckFysbfZyje);
                    qmkcZgjeHj = GYCastUtils.add(qmkcZgjeHj, qmkcZgje);
                    Map<String, Object> map = BeanUtils.toBean(dto, Map.class);
                    cpygxcqkmxbGrid.add(map);
                }
                tablesMap.put("cpygxcqkmxbGrid", cpygxcqkmxbGrid);
                fieldMap.put("qckc_zgjeHJ", qckcZgjeHj);
                fieldMap.put("bqrk_zgjeHJ", bqrkZgjeHj);
                fieldMap.put("bqck_ysbfjeHJ", bqckYsbfjeHj);
                fieldMap.put("bqck_fysbf_zyjeHJ", bqckFysbfZyjeHj);
                fieldMap.put("qmkc_zgjeHJ", qmkcZgjeHj);
                dyxx.put("tables", tablesMap);
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
            }

        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb08_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB08(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表09 成品油购销存数量明细表 打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB09(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            final List<Map<String, Object>> cpygxcslmxbGrid = new ArrayList<>();
            // SbZzsYbnsrFbCpygxcslMapper 查询表Sb_Zzs_Ybnsr_Fb_Cpygxcsl 获取结构化表的数据，如果没数据，就不需要给dyxxMap设置值了
            final LambdaQueryWrapper<SbZzsYbnsrFbCpygxcslSjgjDTO> sbzzsybnsrWrapper = new LambdaQueryWrapper<>();
            sbzzsybnsrWrapper.eq(SbZzsYbnsrFbCpygxcslSjgjDTO::getSbuuid, sbuuid);
            List<SbZzsYbnsrFbCpygxcslSjgjDTO> zzsList = sbZzsYbnsrFbCpygxcslMapper.selectList(sbzzsybnsrWrapper);
            if (GyUtils.isNotNull(zzsList)) {
                for (SbZzsYbnsrFbCpygxcslSjgjDTO dto : zzsList) {
                    final String ylxh = dto.getYlxh();
                    final String ylxhmc = CacheUtils.dm2mc("dm_sb_cpycpzl", ylxh);
                    dto.setYlxh(ylxhmc);
                    Map<String, Object> map = BeanUtils.toBean(dto, Map.class);
                    cpygxcslmxbGrid.add(map);
                }
                tablesMap.put("cpygxcslmxbGrid", cpygxcslmxbGrid);
                dyxx.put("tables", tablesMap);
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb09_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB09(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表10机动车辆生产企业销售明细表 打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB10(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb10_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB10(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表11 机动车辆生产企业销售情况统计表 打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB11(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb11_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB11(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 机动车销售统一发票领用存月报表
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB12(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb12_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB12(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 机动车销售统一发票清单
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB13(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb13_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB13(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表机动车辆经销企业销售明细表打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB14(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb14_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB14(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表电力企业增值税销项税额和进项税额传递单打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB15(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        // SB_ZZS_YBNSR_FB_DLQYXXHJX
        // SbZzsYbnsrFbDlqyxxhjxMapper SbZzsYbnsrFbDlqyxxjxJxMapper SbZzsYbnsrFbDlqyxxjxXxMapper
        final LambdaQueryWrapper<SbZzsYbnsrFbDlqyxxhjxSjgjDTO> sbzzsybnsrDlqyxxhjxWrapper = new LambdaQueryWrapper<>();
        sbzzsybnsrDlqyxxhjxWrapper.eq(SbZzsYbnsrFbDlqyxxhjxSjgjDTO::getSbuuid, sbuuid);
        SbZzsYbnsrFbDlqyxxhjxSjgjDTO sbzzsybnsrDlqyxxhjxDTO =
            sbZzsYbnsrFbDlqyxxhjxMapper.selectOne(sbzzsybnsrDlqyxxhjxWrapper);
        Map<String, Object> sbbMap1 = BeanUtils.toBean(sbzzsybnsrDlqyxxhjxDTO, Map.class);

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_DLQYXXJX_XX
            final List<Map<String, Object>> xxGrid = zzsYbnsrSbmxGjMapper.queryListDlqyXx(sbuuid);
            // SB_ZZS_YBNSR_FB_DLQYXXJX_JX
            final List<Map<String, Object>> jxGrid = zzsYbnsrSbmxGjMapper.queryListDlqyXx(sbuuid);
            if (GYCastUtils.notNull(jxGrid)) {
                tablesMap.put("jxGrid", jxGrid);
            }
            if (GYCastUtils.notNull(xxGrid)) {
                tablesMap.put("xxGrid", xxGrid);
                fieldMap.putAll(sbbMap);
                fieldMap.putAll(sbbMap1);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb15_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB15(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表增值税运输发票抵扣清单打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB16(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb16_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB16(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表农产品核定扣除增值税进项税额计算表（汇总表）打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB18(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // Sb_Zzs_Ybnsr_Fb_Ncphdkchz
            final Map<String, Object> sbbMap1 = zzsYbnsrSbmxGjMapper.queryNcphdkchz(sbuuid);
            if (GYCastUtils.notNull(sbbMap1)) {
                Double jxsehj = 0.0;

                for (Map.Entry<String, Object> entry : sbbMap1.entrySet()) {
                    final String key = entry.getKey();
                    if ("schwTrccfJxse".equals(key) || "schwCbfJxse".equals(key) || "zjxsJxse".equals(key)
                        || "scjyJxse".equals(key)) {
                        final Double value =
                            GyUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                        jxsehj = GYCastUtils.add(jxsehj, value);
                    }

                }
                fieldMap.put("jxsehj", jxsehj);
                fieldMap.putAll(sbbMap);
                fieldMap.putAll(sbbMap1);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb18_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB18(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 《投入产出法核定农产品增值税进项税额计算表》
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB19(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        // final String skssqz = GYSbSdsUtils.cast2StrNew(sbbDO.getSkssqz().format(formatter));

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_HDNCPJX 《投入产出法核定农产品增值税进项税额计算表》
            final List<Map<String, Object>> fb3List = zzsYbnsrSbmxGjMapper.queryListHdncpjx(sbuuid);
            if (GYCastUtils.notNull(fb3List)) {
                Double hjl1 = 0.0;
                Double hjl2 = 0.0;
                Double hjl3 = 0.0;
                Double hjl4 = 0.0;
                Double hjl5 = 0.0;
                Double hjl6 = 0.0;
                Double hjl7 = 0.0;
                Double hjl9 = 0.0;
                for (Map<String, Object> map : fb3List) {
                    if (!GyUtils
                        .isNull(CacheUtils.dm2mc("dm_sb_ncpkcbzcplx", GYSbSdsUtils.cast2StrNew(map.get("cpmc1"))))) {
                        map.put("cpmc1",
                            CacheUtils.dm2mc("dm_sb_ncpkcbzcplx", GYSbSdsUtils.cast2StrNew(map.get("cpmc1"))));
                    }

                    final Double hdddhsl =
                        GyUtils.isNull(map.get("hdddhsl")) ? 0.0 : GYCastUtils.cast2Double(map.get("hdddhsl"));
                    final Double qckcncpsl =
                        GyUtils.isNull(map.get("qckcncpsl")) ? 0.0 : GYCastUtils.cast2Double(map.get("qckcncpsl"));
                    final Double qcpjmj =
                        GyUtils.isNull(map.get("qcpjmj")) ? 0.0 : Double.valueOf(String.valueOf(map.get("qcpjmj")));
                    final Double dqgjncpsl =
                        GyUtils.isNull(map.get("dqgjncpsl")) ? 0.0 : GYCastUtils.cast2Double(map.get("dqgjncpsl"));
                    final Double dqmj =
                        GyUtils.isNull(map.get("dqmj")) ? 0.0 : Double.valueOf(String.valueOf(map.get("dqmj")));
                    final Double pjgmdj =
                        GyUtils.isNull(map.get("pjgmdj")) ? 0.0 : Double.valueOf(String.valueOf(map.get("pjgmdj")));
                    final Double dqxshwsl =
                        GyUtils.isNull(map.get("dqxshwsl")) ? 0.0 : GYCastUtils.cast2Double(map.get("dqxshwsl"));
                    final Double dqxydkncpjxse = GyUtils.isNull(map.get("dqyxdkncpjxse")) ? 0.0
                        : GYCastUtils.cast2Double(map.get("dqyxdkncpjxse"));
                    hjl1 = GYCastUtils.add(hjl1, hdddhsl);
                    hjl2 = GYCastUtils.add(hjl2, qckcncpsl);
                    hjl3 = GYCastUtils.add(hjl3, qcpjmj);
                    hjl4 = GYCastUtils.add(hjl4, dqgjncpsl);
                    hjl5 = GYCastUtils.add(hjl5, dqmj);
                    hjl6 = GYCastUtils.add(hjl6, pjgmdj);
                    hjl7 = GYCastUtils.add(hjl7, dqxshwsl);
                    hjl9 = GYCastUtils.add(hjl9, dqxydkncpjxse);
                }
                fieldMap.put("hjl1", hjl1);
                fieldMap.put("hjl2", hjl2);
                fieldMap.put("hjl3", hjl3);
                fieldMap.put("hjl4", hjl4);
                fieldMap.put("hjl5", hjl5);
                fieldMap.put("hjl6", hjl6);
                fieldMap.put("hjl7", hjl7);
                fieldMap.put("hjl9", hjl9);
                fieldMap.putAll(sbbMap);
                tablesMap.put("xxGrid", fb3List);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb19_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB19(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表成本法核定农产品增值税进项税额计算表打印数据SB_ZZS_YBNSR_FB_CBFHDNCP《成本法核定农产品增值税进项税额计算表》
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB20(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_CBFHDNCP《成本法核定农产品增值税进项税额计算表》
            final List<Map<String, Object>> fb3List = zzsYbnsrSbmxGjMapper.queryListCbfhdncp(sbuuid);
            if (GYCastUtils.notNull(fb3List)) {
                Double jehj = 0.0;
                // 计算销售价格合计
                for (Map<String, Object> map : fb3List) {
                    if (!GyUtils
                        .isNull(CacheUtils.dm2mc("dm_sb_ncpkcbzcplx", GYSbSdsUtils.cast2StrNew(map.get("cpmc1"))))) {
                        map.put("cpmc1",
                            CacheUtils.dm2mc("dm_sb_ncpkcbzcplx", GYSbSdsUtils.cast2StrNew(map.get("cpmc1"))));
                    }

                    final Double dqyxdkncpjxse = GyUtils.isNull(map.get("dqyxdkncpjxse")) ? 0.0
                        : GYCastUtils.cast2Double(map.get("dqyxdkncpjxse"));
                    jehj = GYCastUtils.add(jehj, dqyxdkncpjxse);
                }
                tablesMap.put("xxGrid", fb3List);
                fieldMap.put("hjje", jehj);
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb20_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB20(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表购进农产品直接销售核定农产品增值税进项税额计算表打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB21(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_NCPZJXS《购进农产品直接销售核定农产品增值税进项税额计算表》
            final List<Map<String, Object>> fb3List = zzsYbnsrSbmxGjMapper.queryListNcpzjxs(sbuuid);
            if (GYCastUtils.notNull(fb3List)) {
                Double hjl9 = 0.0;
                // 计算销售价格合计
                for (Map<String, Object> map : fb3List) {
                    final Double ncppjgmdj =
                        GyUtils.isNull(map.get("ncppjgmdj")) ? 0.0 : GYCastUtils.cast2Double(map.get("ncppjgmdj"));
                    hjl9 = GYCastUtils.add(hjl9, ncppjgmdj);
                }
                tablesMap.put("xxGrid", fb3List);
                fieldMap.put("hjl9", hjl9);
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb21_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB21(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表22 购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB22(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_NCPYYSCJY《购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表》
            final List<Map<String, Object>> zzsybrfb22List = zzsYbnsrSbmxGjMapper.queryListNcpyyscjy(sbuuid);
            if (GYCastUtils.notNull(zzsybrfb22List)) {
                for (int i = 0; i < zzsybrfb22List.size(); i++) {
                    final Map<String, Object> map22 = zzsybrfb22List.get(i);
                    map22.put("cpmc1",
                        CacheUtils.dm2mc("dm_sb_ncpkcbzcplx", GYSbSdsUtils.cast2StrNew(map22.get("cpmc1"))));
                    map22.put("xh", i + 1);
                }
                tablesMap.put("gjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid", zzsybrfb22List);
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb22_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB22(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表23 打印数据
     * @description 加油站月销售油品汇总表
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB23(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_JYZYXSHZ 增值税一般纳税人适用申报表加油站月销售油品汇总表
            final List<Map<String, Object>> zzsybrfb23List = zzsYbnsrSbmxGjMapper.queryListJyzyxshz(sbuuid);
            if (GYCastUtils.notNull(zzsybrfb23List)) {
                tablesMap.put("jyzyxsyphzbGrid", zzsybrfb23List);
                // 计算合计
                double cyslLjsHJ = 0.0;
                double ykcylZyBysHJ = 0.0;
                double ykcylDkBysHJ = 0.0;
                double ykcylDcBysHJ = 0.0;
                double ykcylJcBysHJ = 0.0;
                double ykcylZyLjsHJ = 0.0;
                double ykcylDkLjsHJ = 0.0;
                double ykcylDcLjsHJ = 0.0;
                double ykcylJcLjsHJ = 0.0;
                double ysxsslBysHJ = 0.0;
                double ysxsslLjsHJ = 0.0;
                double xsjeTgjyjBysHJ = 0.0;
                double xsjeTgjyjLjsHJ = 0.0;
                double xsjeBtgjyjBysHJ = 0.0;
                double xsjeBtgjyjLjsHJ = 0.0;
                for (Map<String, Object> map23 : zzsybrfb23List) {
                    final Double cyslLj =
                        GyUtils.isNull(map23.get("cyslLjs")) ? 0.0 : GYCastUtils.cast2Double(map23.get("cyslLjs"));
                    final Double ykcylZyBys = GyUtils.isNull(map23.get("ykcylZyBys")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("ykcylZyBys"));
                    final Double ykcylDkBys = GyUtils.isNull(map23.get("ykcylDkBys")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("ykcylDkBys"));
                    final Double ykcylDcBys = GyUtils.isNull(map23.get("ykcylDcBys")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("ykcylDcBys"));
                    final Double ykcylJcBys = GyUtils.isNull(map23.get("ykcylJcBys")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("ykcylJcBys"));
                    final Double ykcylZyLjs = GyUtils.isNull(map23.get("ykcylZyLjs")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("ykcylZyLjs"));
                    final Double ykcylDkLjs = GyUtils.isNull(map23.get("ykcylDkLjs")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("ykcylDkLjs"));
                    final Double ykcylDcLjs = GyUtils.isNull(map23.get("ykcylDcLjs")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("ykcylDcLjs"));
                    final Double ykcylJcLjs = GyUtils.isNull(map23.get("ykcylJcLjs")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("ykcylJcLjs"));
                    final Double ysxsslBys =
                        GyUtils.isNull(map23.get("ysxsslBys")) ? 0.0 : GYCastUtils.cast2Double(map23.get("ysxsslBys"));
                    final Double ysxsslLjs =
                        GyUtils.isNull(map23.get("ysxsslLjs")) ? 0.0 : GYCastUtils.cast2Double(map23.get("ysxsslLjs"));
                    final Double xsjeTgjyjBys = GyUtils.isNull(map23.get("xsjeTgjyjBys")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("xsjeTgjyjBys"));
                    final Double xsjeTgjyjLjs = GyUtils.isNull(map23.get("xsjeTgjyjLjs")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("xsjeTgjyjLjs"));
                    final Double xsjeBtgjyjBys = GyUtils.isNull(map23.get("xsjeBtgjyjBys")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("xsjeBtgjyjBys"));
                    final Double xsjeBtgjyjLjs = GyUtils.isNull(map23.get("xsjeBtgjyjLjs")) ? 0.0
                        : GYCastUtils.cast2Double(map23.get("xsjeBtgjyjLjs"));
                    cyslLjsHJ = GYCastUtils.add(cyslLjsHJ, cyslLj);
                    ykcylZyBysHJ = GYCastUtils.add(ykcylZyBysHJ, ykcylZyBys);
                    ykcylDkBysHJ = GYCastUtils.add(ykcylDkBysHJ, ykcylDkBys);
                    ykcylDcBysHJ = GYCastUtils.add(ykcylDcBysHJ, ykcylDcBys);
                    ykcylJcBysHJ = GYCastUtils.add(ykcylJcBysHJ, ykcylJcBys);

                    ykcylZyLjsHJ = GYCastUtils.add(ykcylZyLjsHJ, ykcylZyLjs);
                    ykcylDkLjsHJ = GYCastUtils.add(ykcylDkLjsHJ, ykcylDkLjs);
                    ykcylDcLjsHJ = GYCastUtils.add(ykcylDcLjsHJ, ykcylDcLjs);
                    ykcylJcLjsHJ = GYCastUtils.add(ykcylJcLjsHJ, ykcylJcLjs);
                    ysxsslBysHJ = GYCastUtils.add(ysxsslBysHJ, ysxsslBys);

                    ysxsslLjsHJ = GYCastUtils.add(ysxsslLjsHJ, ysxsslLjs);
                    xsjeTgjyjBysHJ = GYCastUtils.add(xsjeTgjyjBysHJ, xsjeTgjyjBys);
                    xsjeTgjyjLjsHJ = GYCastUtils.add(xsjeTgjyjLjsHJ, xsjeTgjyjLjs);
                    xsjeBtgjyjBysHJ = GYCastUtils.add(xsjeBtgjyjBysHJ, xsjeBtgjyjBys);
                    xsjeBtgjyjLjsHJ = GYCastUtils.add(xsjeBtgjyjLjsHJ, xsjeBtgjyjLjs);
                }
                fieldMap.put("cyslLjsHJ", cyslLjsHJ);
                fieldMap.put("ykcylZyBysHJ", ykcylZyBysHJ);
                fieldMap.put("ykcylDkBysHJ", ykcylDkBysHJ);
                fieldMap.put("ykcylDcBysHJ", ykcylDcBysHJ);
                fieldMap.put("ykcylJcBysHJ", ykcylJcBysHJ);

                fieldMap.put("ykcylZyLjsHJ", ykcylZyLjsHJ);
                fieldMap.put("ykcylDkLjsHJ", ykcylDkLjsHJ);
                fieldMap.put("ykcylDcLjsHJ", ykcylDcLjsHJ);
                fieldMap.put("ysxsslBysHJ", ysxsslBysHJ);
                fieldMap.put("ykcylJcLjsHJ", ykcylJcLjsHJ);

                fieldMap.put("ysxsslLjsHJ", ysxsslLjsHJ);
                fieldMap.put("xsjeTgjyjBysHJ", xsjeTgjyjBysHJ);
                fieldMap.put("xsjeTgjyjLjsHJ", xsjeTgjyjLjsHJ);
                fieldMap.put("xsjeBtgjyjBysHJ", xsjeBtgjyjBysHJ);
                fieldMap.put("xsjeBtgjyjLjsHJ", xsjeBtgjyjLjsHJ);

                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb23_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB23(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表24 打印数据
     * @description 加油站 月份加油信息明细表
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB24(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_JYZYFJYXX 增值税一般纳税人适用申报表加油站月份加油信息明细表
            final List<Map<String, Object>> zzsybrfb24List = zzsYbnsrSbmxGjMapper.queryListJyzyfjyxx(sbuuid);
            if (GYCastUtils.notNull(zzsybrfb24List)) {
                tablesMap.put("jyzyfjyxxmxbGrid", zzsybrfb24List);
                final Map<String, Object> map24A1 = zzsybrfb24List.get(0);
                final Double zqs = GYCastUtils.cast2Double(map24A1.get("zqs"));
                final String lxdh = GYSbSdsUtils.cast2StrNew(map24A1.get("lxdh"));
                final String fddbr = GYSbSdsUtils.cast2StrNew(map24A1.get("fddbr"));
                // 计算合计
                double yljjylHJ = 0.0;
                double yljjeHJ = 0.0;
                double yynzzseHJ = 0.0;
                for (Map<String, Object> map24 : zzsybrfb24List) {
                    final Double yljjyl =
                        GyUtils.isNull(map24.get("yljjyl")) ? 0.0 : GYCastUtils.cast2Double(map24.get("yljjyl"));
                    final Double yljje =
                        GyUtils.isNull(map24.get("yljje")) ? 0.0 : GYCastUtils.cast2Double(map24.get("yljje"));
                    final Double yynzzse =
                        GyUtils.isNull(map24.get("yynzzse")) ? 0.0 : GYCastUtils.cast2Double(map24.get("yynzzse"));

                    yljjylHJ = GYCastUtils.add(yljjylHJ, yljjyl);
                    yljjeHJ = GYCastUtils.add(yljjeHJ, yljje);
                    yynzzseHJ = GYCastUtils.add(yynzzseHJ, yynzzse);

                }
                fieldMap.put("zqs", zqs);
                fieldMap.put("lxdh", lxdh);
                fieldMap.put("fddbr", fddbr);
                fieldMap.put("yljjylHJ", yljjylHJ);
                fieldMap.put("yljjeHJ", yljjeHJ);
                fieldMap.put("yynzzseHJ", yynzzseHJ);
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb24_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB24(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表25 代扣代缴税收通用缴款书抵扣清单 打印数据
     * @description 代扣代缴税收通用缴款书抵扣清单
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB25(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_DKDJJKSDK 代扣代缴税收通用缴款书抵扣清单
            final List<Map<String, Object>> zzsybrfb25List = zzsYbnsrSbmxGjMapper.queryListDkdjjksdk(sbuuid);
            if (GYCastUtils.notNull(zzsybrfb25List)) {
                tablesMap.put("dkdjsstyjksdkqdGrid", zzsybrfb25List);
                // 计算合计
                double seHJ = 0.0;
                for (Map<String, Object> map25 : zzsybrfb25List) {
                    final Double se = GyUtils.isNull(map25.get("se")) ? 0.0 : GYCastUtils.cast2Double(map25.get("se"));
                    seHJ = GYCastUtils.add(seHJ, se);

                }
                fieldMap.put("seHJ", seHJ);
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb25_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB25(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表26 打印数据
     * @description 相关说明 ：汇总纳税企业通用传递单
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB26(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // 《汇总纳税企业通用传递单》 SB_ZZS_YBNSR_FB_HZTYCDD_XX
            final List<Map<String, Object>> zzsybrfb26List1 = zzsYbnsrSbmxGjMapper.queryListHztycddXx(sbuuid);
            // 《汇总纳税企业通用传递单》 SB_ZZS_YBNSR_FB_HZTYCDD_JX
            final List<Map<String, Object>> zzsybrfb26List2 = zzsYbnsrSbmxGjMapper.queryListHztycddJx(sbuuid);
            if (GYCastUtils.notNull(zzsybrfb26List1)) {
                tablesMap.put("hznsqytycddXxGrid", zzsybrfb26List1);
            }
            if (GYCastUtils.notNull(zzsybrfb26List2)) {
                tablesMap.put("hznsqytycddJxGrid", zzsybrfb26List2);
            }
            if (GYCastUtils.notNull(zzsybrfb26List1) || GYCastUtils.notNull(zzsybrfb26List2)) {
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 《汇总纳税企业通用传递单》 SB_ZZS_YBNSR_FB_HZTYCDD_XX SB_ZZS_YBNSR_FB_HZTYCDD_JX
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb26_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB26(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出邮政企业分支机构增值税汇总纳税信息传递单打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB27(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        // 《邮政企业分支机构增值税汇总纳税信息传递单》SB_ZZS_YBNSR_FB_YZQY_YJN
        final Map<String, Object> sbbMap1 = zzsYbnsrSbmxGjMapper.queryYzqyyjn(sbuuid);
        if (GYCastUtils.notNull(sbbMap1)) {
            Double ysxm1 = 0.0;
            Double ysxm2 = 0.0;
            Double ysxm3 = 0.0;
            Double ysxm4 = 0.0;
            Double ysxm5 = 0.0;
            Double ysxm6 = 0.0;
            Double ysxm7 = 0.0;
            // String ysxm8 = "";
            StringBuilder ysxm8 = new StringBuilder();
            Double msxm1 = 0.0;
            Double msxm2 = 0.0;
            Double msxm3 = 0.0;
            Double msxm4 = 0.0;
            Double msxm5 = 0.0;
            Double msxm6 = 0.0;
            Double msxm7 = 0.0;
            // String msxm8 = "";
            StringBuilder msxm8 = new StringBuilder();
            for (Map.Entry<String, Object> entry : sbbMap1.entrySet()) {
                final String key = entry.getKey();
                if ("ypxsXse".equals(key) || "qtXse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    ysxm1 = GYCastUtils.add(ysxm1, value);
                }
                if ("ypxsYdk".equals(key) || "qtYdk".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    ysxm2 = GYCastUtils.add(ysxm2, value);
                }
                if ("ypxsYzl".equals(key) || "qtYzl".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    ysxm3 = GYCastUtils.add(ysxm3, value);
                }
                if ("ypxsYjse".equals(key) || "qtYjse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    ysxm4 = GYCastUtils.add(ysxm4, value);
                }
                if ("ypxsCbxse".equals(key) || "qtCbxse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    ysxm5 = GYCastUtils.add(ysxm5, value);
                }
                if ("ypxsSysl".equals(key) || "qtSysl".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    ysxm6 = GYCastUtils.add(ysxm6, value);
                }
                if ("ypxsCbse".equals(key) || "qtCbse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    ysxm7 = GYCastUtils.add(ysxm7, value);
                }
                if ("ypxsBz".equals(key) || "qtBz".equals(key)) {
                    final String value =
                        GYCastUtils.isNull(entry.getValue()) ? "" : GYCastUtils.cast2Str(entry.getValue());
                    // ysxm8 = ysxm8 +" "+ value;
                    ysxm8.append(value);
                }

            }
            for (Map.Entry<String, Object> entry : sbbMap1.entrySet()) {
                final String key = entry.getKey();
                if ("yzpbfwXse".equals(key) || "yztsfwXse".equals(key) || "qtyzfwDljrbxywXse".equals(key)
                    || "qtyzfwDlsdwllywXse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    msxm1 = GYCastUtils.add(msxm1, value);
                }
                if ("yzpbfwYdk".equals(key) || "yztsfwYdk".equals(key) || "qtyzfwDljrbxywYdk".equals(key)
                    || "qtyzfwDlsdwllywYdk".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    msxm2 = GYCastUtils.add(msxm2, value);
                }
                if ("yzpbfwYzl".equals(key) || "yztsfwYzl".equals(key) || "qtyzfwDljrbxywYzl".equals(key)
                    || "qtyzfwDlsdwllywYzl".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    msxm3 = GYCastUtils.add(msxm3, value);
                }
                if ("yzpbfwYjse".equals(key) || "yztsfwYjse".equals(key) || "qtyzfwDljrbxywYjse".equals(key)
                    || "qtyzfwDlsdwllywYjse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    msxm4 = GYCastUtils.add(msxm4, value);
                }
                if ("yzpbfwCbxse".equals(key) || "yztsfwCbxse".equals(key) || "qtyzfwDljrbxywCbxse".equals(key)
                    || "qtyzfwDlsdwllywCbxse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    msxm5 = GYCastUtils.add(msxm5, value);
                }
                if ("yzpbfwSysl".equals(key) || "yztsfwSysl".equals(key) || "qtyzfwDljrbxywSysl".equals(key)
                    || "qtyzfwDlsdwllywSysl".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    msxm6 = GYCastUtils.add(msxm6, value);
                }
                if ("yzpbfwCbse".equals(key) || "yztsfwCbse".equals(key) || "qtyzfwDljrbxywCbse".equals(key)
                    || "qtyzfwDlsdwllywCbse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    msxm7 = GYCastUtils.add(msxm7, value);
                }
                if ("yzpbfwBz".equals(key) || "yztsfwBz".equals(key) || "qtyzfwDljrbxywBz".equals(key)
                    || "qtyzfwDlsdwllywBz".equals(key)) {
                    final String value =
                        GYCastUtils.isNull(entry.getValue()) ? "" : GYCastUtils.cast2Str(entry.getValue());
                    // msxm8 = msxm8 +" "+ value;
                    msxm8.append(value);
                }
            }
            fieldMap.put("ysxm1", ysxm1);
            fieldMap.put("ysxm2", ysxm2);
            fieldMap.put("ysxm3", ysxm3);
            fieldMap.put("ysxm4", ysxm4);
            fieldMap.put("ysxm5", ysxm5);
            fieldMap.put("ysxm6", ysxm6);
            fieldMap.put("ysxm7", ysxm7);
            fieldMap.put("ysxm8", ysxm8.toString());
            fieldMap.put("msxm1", msxm1);
            fieldMap.put("msxm2", msxm2);
            fieldMap.put("msxm3", msxm3);
            fieldMap.put("msxm4", msxm4);
            fieldMap.put("msxm5", msxm5);
            fieldMap.put("msxm6", msxm6);
            fieldMap.put("msxm7", msxm7);
            fieldMap.put("msxm8", msxm8.toString());
        }
        if (GYCastUtils.notNull(sbbMap)) {
            // 《邮政企业分支机构增值税汇总纳税信息传递单》SB_ZZS_YBNSR_FB_YZQY_QDJX
            final List<Map<String, Object>> fb3List = zzsYbnsrSbmxGjMapper.queryListYzqyqdjx(sbuuid);
            if (GYCastUtils.notNull(fb3List)) {
                Double jehj = 0.0;
                Double jxsehj = 0.0;
                // 计算销售价格合计
                for (Map<String, Object> map : fb3List) {
                    final Double je = GyUtils.isNull(map.get("je")) ? 0.0 : GYCastUtils.cast2Double(map.get("je"));
                    final Double jxse =
                        GyUtils.isNull(map.get("jxse")) ? 0.0 : GYCastUtils.cast2Double(map.get("jxse"));
                    jehj = GYCastUtils.add(jehj, je);
                    jxsehj = GYCastUtils.add(jxsehj, jxse);
                }
                tablesMap.put("xxGrid", fb3List);
                fieldMap.putAll(sbbMap);
                fieldMap.putAll(sbbMap1);
                fieldMap.put("jehj", jehj);
                fieldMap.put("jxsehj", jxsehj);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb27_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB27(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出铁路运输企业分支机构增值税汇总纳税信息传递单打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB28(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());
        // 《铁路运输企业分支机构增值税汇总纳税信息传递单》SB_ZZS_YBNSR_FB_TLYS_YJN
        final Map<String, Object> sbbMap1 = zzsYbnsrSbmxGjMapper.queryTlysyjn(sbuuid);
        if (GYCastUtils.notNull(sbbMap1)) {
            Double xsehj = 0.0;
            Double yjsehj = 0.0;
            Double cbxsehj = 0.0;
            Double cbsehj = 0.0;
            Double tljsjjhj = 0.0;
            for (Map.Entry<String, Object> entry : sbbMap1.entrySet()) {
                final String key = entry.getKey();
                if ("jtysfwXse".equals(key) || "wlfzfwXse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    xsehj = GYCastUtils.add(xsehj, value);
                }
                if ("jtysfwYjse".equals(key) || "wlfzfwYjse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    yjsehj = GYCastUtils.add(yjsehj, value);
                }
                if ("jtysfwCbxse".equals(key) || "wlfzfwCbxse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    cbxsehj = GYCastUtils.add(cbxsehj, value);
                }
                if ("jtysfwCbse".equals(key) || "wlfzfwCbse".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    cbsehj = GYCastUtils.add(cbsehj, value);
                }
                if ("jtysfwTljsjj".equals(key) || "wlfzfwTljsjj".equals(key)) {
                    final Double value =
                        GYCastUtils.isNull(entry.getValue()) ? 0.0 : GYCastUtils.cast2Double(entry.getValue());
                    tljsjjhj = GYCastUtils.add(tljsjjhj, value);
                }
            }
            fieldMap.put("xsehj", xsehj);
            fieldMap.put("yjsehj", yjsehj);
            fieldMap.put("cbxsehj", cbxsehj);
            fieldMap.put("cbsehj", cbsehj);
            fieldMap.put("tljsjjhj", tljsjjhj);
        }
        if (GYCastUtils.notNull(sbbMap)) {
            // 《铁路运输企业分支机构增值税汇总纳税信息传递单》 SB_ZZS_YBNSR_FB_TLYS_QDJX
            final List<Map<String, Object>> fb3List = zzsYbnsrSbmxGjMapper.queryListTlysqdjx(sbuuid);
            if (GYCastUtils.notNull(fb3List)) {
                tablesMap.put("xxGrid", fb3List);
                fieldMap.putAll(sbbMap);
                fieldMap.putAll(sbbMap1);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb28_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB28(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出电信企业分支机构增值税汇总纳税信息传递单打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB29(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // 《电信企业分支机构增值税汇总纳税信息传递单》 SB_ZZS_YBNSR_FB_DXQY_YJN
            final List<Map<String, Object>> fb3List1 = zzsYbnsrSbmxGjMapper.queryListDxqyyjn(sbuuid);
            if (GYCastUtils.notNull(fb3List1)) {
                for (Map<String, Object> map : fb3List1) {
                    final String ewbhxh = GYCastUtils.isNull(map.get("ewbhxh")) ? "" : map.get("ewbhxh").toString();
                    for (Map.Entry<String, Object> entry : map.entrySet()) {
                        final String key = entry.getKey();
                        if ("xse".equals(key) || "yskje".equals(key) || "yzl".equals(key) || "yjse1".equals(key)
                            || "cbxse".equals(key) || "sysl".equals(key) || "cbse".equals(key) || "bz".equals(key)) {
                            fieldMap.put(key + ewbhxh, entry.getValue());
                        }
                    }
                }
            }
            // 《电信企业分支机构增值税汇总纳税信息传递单》 SB_ZZS_YBNSR_FB_DXQY_QDJX
            final List<Map<String, Object>> fb3List = zzsYbnsrSbmxGjMapper.queryListDxqyqdjx(sbuuid);
            if (GYCastUtils.notNull(fb3List)) {
                Double jehj = 0.0;
                Double jxsehj = 0.0;
                Double sksbjjsfwfhj = 0.0;
                // 计算销售价格合计
                for (Map<String, Object> map : fb3List) {
                    final Double je = GYCastUtils.isNull(map.get("je")) ? 0.0 : GYCastUtils.cast2Double(map.get("je"));
                    final Double jxse =
                        GYCastUtils.isNull(map.get("jxse")) ? 0.0 : GYCastUtils.cast2Double(map.get("jxse"));
                    final Double sksbjjsfwf = GYCastUtils.isNull(map.get("sksbjjsfwf")) ? 0.0
                        : GYCastUtils.cast2Double(map.get("sksbjjsfwf"));
                    jehj = GYCastUtils.add(jehj, je);
                    jxsehj = GYCastUtils.add(jxsehj, jxse);
                    sksbjjsfwfhj = GYCastUtils.add(sksbjjsfwfhj, sksbjjsfwf);
                }
                tablesMap.put("xxGrid", fb3List);
                fieldMap.putAll(sbbMap);
                fieldMap.put("jehj", jehj);
                fieldMap.put("sksbjjsfwfhj", sksbjjsfwfhj);
                fieldMap.put("jxsehj", jxsehj);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }

        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb29_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB32(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出汇总纳税企业增值税分配表打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB30(String sbuuid) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // SB_ZZS_YBNSR_FB_HZNSFP 增值税一般纳税人适用申报表汇总纳税企业增值税分配表
        final Map<String, Object> sbbMap = zzsYbnsrSbmxGjMapper.queryHznsfp(sbuuid);
        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_HZNSFP_ZB 增值税一般纳税人适用申报表汇总纳税企业增值税分配表_子表
            final List<Map<String, Object>> fb3List = zzsYbnsrSbmxGjMapper.queryHznsfpZb(sbuuid);
            fieldMap.putAll(sbbMap);
            if (GYCastUtils.notNull(fb3List)) {
                tablesMap.put("xxGrid", fb3List);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb30_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB30(sbuuid);
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出部分产品销售统计表打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB32(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb32_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB32(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表36打印数据
     * @description 相关说明
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB36(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb36_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB36(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GYCastUtils.notNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表37 打印数据
     * @description 相关说明 ：本期抵扣进项税额结构明细表
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB37(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb37_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB37(sbuuid);
        final Map<String, Object> dyxx = new HashMap<>();
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表38 打印数据
     * @description 铁路建设基金纳税申报表
     * @time 创建时间:2019-1-7下午02:42:46
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB38(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxformNsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxformSkssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxformSkssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            // SB_ZZS_YBNSR_FB_TLJSJJ 铁路建设基金纳税申报表
            final List<Map<String, Object>> fb38List = zzsYbnsrSbmxGjMapper.queryListTljsjj(sbuuid);
            if (GYCastUtils.notNull(fb38List)) {
                for (Map<String, Object> map : fb38List) {
                    final Long ewbhxh = (Long)map.get("ewbhxh");
                    fieldMap.put("bys" + ewbhxh.toString(), map.get("bys"));
                    fieldMap.put("bnlj" + ewbhxh.toString(), map.get("bnlj"));
                }
                fieldMap.putAll(sbbMap);
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);
            }
        }
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb38_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB38(sbuuid);
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     *
     * @name 输出增值税一般人申报表附表39 打印数据
     * @description 附加税费情况表
     * @time 创建时间:2020年11月16日20点08分
     * @param sbuuid sbuuid
     * @return vo 打印数据
     * <AUTHOR>
     * @ @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB39(String sbuuid, SbSbbDO sbbDO) {
        SbDyVO vo = null;
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();

        final Map<String, Object> sbbMap = new HashMap<>();
        sbbMap.put("nsrxxformDjxh", sbbDO.getDjxh());
        sbbMap.put("nsrxxformNsrsbh", sbbDO.getNsrsbh());
        sbbMap.put("nsrxxFormnsrmc", sbbDO.getNsrmc());
        sbbMap.put("nsrxxFormskssqq", sbbDO.getSkssqq().format(formatter));
        sbbMap.put("nsrxxFormskssqz", sbbDO.getSkssqz().format(formatter));
        sbbMap.put("nsrxxformTbrq1",
            GyUtils.isNull(sbbDO.getTbrq1()) ? sbbDO.getSbrq1().format(formatter) : sbbDO.getTbrq1().format(formatter));
        sbbMap.put("slxxformBsrxm", sbbDO.getBsrxm());
        sbbMap.put("slxxformCwfzrxm", sbbDO.getCwfzrxm());
        sbbMap.put("slxxformFddbrxm", sbbDO.getFddbrxm());
        sbbMap.put("slxxformDlrmc", sbbDO.getDlrmc());
        sbbMap.put("slxxformJbrxm", sbbDO.getJbrxm());
        sbbMap.put("slxxformJbrlxdh", sbbDO.getJbrlxdh());
        sbbMap.put("slxxformZgswskfjDm", sbbDO.getZgswskfjDm());
        sbbMap.put("slrq", sbbDO.getSlrq());
        sbbMap.put("slrDm", sbbDO.getSlrDm());
        sbbMap.put("slswjgDm", sbbDO.getSlswjgDm());
        sbbMap.put("ybtse", sbbDO.getYbtse());

        if (GYCastUtils.notNull(sbbMap)) {
            fieldMap.putAll(sbbMap);
            final Map<String, Object> hjMap = new HashMap<>();
            // 附加税费情况表 SB_FJSF
            final List<Map<String, Object>> mxbGrid = zzsYbnsrSbmxGjMapper.queryListFjsf(sbuuid);
            if (GYCastUtils.notNull(mxbGrid)) {
                // select t.* from SB_FJSF_QTXX t where t.sbuuid = '20889AA5B6AACD1045646D22216CA6BA'
                // 2.获取产教信息
                // 附加税费情况表 SB_FJSF_QTXX
                final Map<String, Object> fjsfqtxx = zzsYbnsrSbmxGjMapper.queryFjsfqtxx(sbuuid);
                // 1.判断是否有产教融合资格
                final String sfcjrhqy = GYSbSdsUtils.cast2StrNew(fjsfqtxx.get("bqsfsycjyhxqyjzzc"));
                if ("Y".equals(sfcjrhqy)) {
                    fieldMap.put("sfcjrh", "■是 □否");
                } else {
                    fieldMap.put("sfcjrh", "□是 ■否");
                }
                if (GYCastUtils.notNull(fjsfqtxx)) {
                    fieldMap.putAll(fjsfqtxx);
                }
                Double bqynsfeHJ = 0.0;
                Double jmeHJ = 0.0;
                Double bqyjseHJ = 0.0;
                Double bqybtseHJ = 0.0;
                Double bqcjrhxqydmjeHJ = 0.0;
                Double phjmseHJ = 0.0;
                for (Map<String, Object> tempmap : mxbGrid) {
                    final String zsxmDm = GYSbSdsUtils.cast2StrNew(tempmap.get("zsxmDm"));
                    if ("10109".equals(zsxmDm)) {
                        tempmap.put("sfz", "城市维护建设税");
                        tempmap.put("ewbhxh", "1");
                        tempmap.put("cjrhjmxzDm", "——");
                        tempmap.put("bqcjrhxqydmje", "——");
                    }
                    if ("30203".equals(zsxmDm)) {
                        tempmap.put("sfz", "教育费附加");
                        tempmap.put("ewbhxh", "2");
                    }
                    if ("30216".equals(zsxmDm)) {
                        tempmap.put("sfz", "地方教育附加");
                        tempmap.put("ewbhxh", "3");
                    }
                    Double ybzzs;
                    final double zzsxejmjeAdd = GYCastUtils.notNull(tempmap.get("zzsxejmje"))
                        ? GYCastUtils.cast2Double(tempmap.get("zzsxejmje")) : 0;
                    final double ybzzsAdd =
                        GYCastUtils.notNull(tempmap.get("ybzzs")) ? GYCastUtils.cast2Double(tempmap.get("ybzzs")) : 0;
                    ybzzs = GYCastUtils.add(zzsxejmjeAdd, ybzzsAdd);
                    tempmap.put("ybzzs", ybzzs);
                    tempmap.put("cjrhjmxzDm",
                        CacheUtils.dm2mc("dm_gy_ssjmxz", GYSbSdsUtils.cast2StrNew(tempmap.get("cjrhjmxzDm"))));
                    tempmap.put("ssjmxzDm",
                        CacheUtils.dm2mc("dm_gy_ssjmxz", GYSbSdsUtils.cast2StrNew(tempmap.get("ssjmxzDm"))));
                    // 计算合计值
                    bqynsfeHJ = GYCastUtils.add(bqynsfeHJ, GYCastUtils.notNull(tempmap.get("bqynsfe"))
                        ? GYCastUtils.cast2Double(tempmap.get("bqynsfe")) : 0);
                    jmeHJ = GYCastUtils.add(jmeHJ,
                        GYCastUtils.notNull(tempmap.get("jme")) ? GYCastUtils.cast2Double(tempmap.get("jme")) : 0);
                    bqyjseHJ = GYCastUtils.add(bqyjseHJ, GYCastUtils.notNull(tempmap.get("bqyjse"))
                        ? GYCastUtils.cast2Double(tempmap.get("bqyjse")) : 0);
                    bqybtseHJ = GYCastUtils.add(bqybtseHJ, GYCastUtils.notNull(tempmap.get("bqybtse"))
                        ? GYCastUtils.cast2Double(tempmap.get("bqybtse")) : 0);
                    phjmseHJ = GYCastUtils.add(phjmseHJ, GYCastUtils.notNull(tempmap.get("phjmse"))
                        ? GYCastUtils.cast2Double(tempmap.get("phjmse")) : 0);
                    final String bqcjrhxqydmje = GYSbSdsUtils.cast2StrNew(tempmap.get("bqcjrhxqydmje"));

                    final boolean tempflag = GYCastUtils.notNull(tempmap.get("bqcjrhxqydmje"))
                        && (!("——".equals(tempmap.get("bqcjrhxqydmje"))));
                    bqcjrhxqydmjeHJ = GYCastUtils.add(bqcjrhxqydmjeHJ, tempflag ? Double.valueOf(bqcjrhxqydmje) : 0);

                    fieldMap.putAll(tempmap);
                }
                mxbGrid.sort(Comparator.comparingInt(t -> Convert.toInt(t.get("ewbhxh"))));
                tablesMap.put("mxbGrid", mxbGrid);
                hjMap.put("bqynsfeHJ", bqynsfeHJ);
                hjMap.put("jmeHJ", jmeHJ);
                hjMap.put("bqyjseHJ", bqyjseHJ);
                hjMap.put("bqybtseHJ", bqybtseHJ);
                hjMap.put("phjmseHJ", phjmseHJ);
                fieldMap.putAll(hjMap);

                for (String keytemp : fieldMap.keySet()) {
                    fieldMap.put(keytemp, GYSbSdsUtils.cast2StrNew(fieldMap.get(keytemp)));
                }
                // 六税两费
                final Map<String, Object> lslfMap = new HashMap<>();
                if (GYCastUtils.notNull(fjsfqtxx)) {
                    lslfMap.put("bqsfsyxgmyhzc", fjsfqtxx.get("bqsfsyxgmyhzc"));
                    Date jzzcqssj = Convert.toDate(fjsfqtxx.get("syxgmjzzcqssj"));
                    Date jzzczzsj = Convert.toDate(fjsfqtxx.get("syxgmjzzczzsj"));
                    if (GyUtils.isNotNull(fjsfqtxx.get("syxgmjzzcqssj"))) {
                        lslfMap.put("syxgmjzzcqssj", DateUtil.format(jzzcqssj, DatePattern.CHINESE_DATE_PATTERN));
                    } else {
                        lslfMap.put("syxgmjzzcqssj", " 年 月 日");
                    }
                    if (GyUtils.isNotNull(fjsfqtxx.get("syxgmjzzczzsj"))) {
                        lslfMap.put("syxgmjzzczzsj", DateUtil.format(jzzczzsj, DatePattern.CHINESE_DATE_PATTERN));
                    } else {
                        lslfMap.put("syxgmjzzczzsj", " 年 月 日");
                    }
                    String jzzcsyztDm = GYCastUtils.cast2Str(fjsfqtxx.get("jzzcsyztDm"));
                    String jzzcsyztmc = "";
                    if ("21".equals(jzzcsyztDm)) {
                        jzzcsyztmc = "□ 个体工商户 ■ 小型微利企业";
                    } else if ("22".equals(jzzcsyztDm)) {
                        jzzcsyztmc = "■ 个体工商户 □ 小型微利企业";
                    } else {
                        jzzcsyztmc = "□ 个体工商户 □ 小型微利企业";
                    }
                    lslfMap.put("jzzcsyztDm", jzzcsyztDm);
                    lslfMap.put("jzzcsyztmc", jzzcsyztmc);
                }
                // ps.queryMapByKey("hdxt_dzswj_sbjs001356_queryzzsjzzt", param);
                if (GYCastUtils.notNull(lslfMap)) {
                    lslf(lslfMap);
                    fieldMap.putAll(lslfMap);
                } else {
                    fieldMap.put("sfsy", "否");
                }
                dyxx.put("field", fieldMap);
                dyxx.put("tables", tablesMap);

            }
        }
// □ 是 ■ 否
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb39_lslf_print_zzbd.doc";
        // final Map<String, Object> dyxx = sb702dao.getBDA0610606FB39(sbuuid);
        // 六税两费分支
        /* final String xtcsz = HxzgGyUtils.getXtcs("Z0000099Z02000259", WssbSessionUtils.getSwjgDm());
        if("Y".equals(xtcsz)) {
            mblj = "templates/sbdy/BDA0610606/sb2021_zzsybnsrsb39_lslf_print_zzbd.doc";
        }*/
        if (GyUtils.isNotNull(dyxx)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        return vo;
    }

    /**
     * @name 增值税一般纳税人适用申报表--汇总纳税附加税费分配表
     * @description 相关说明
     * @time 创建时间:2021年8月12日上午10:25:35
     * @param sbuuid sbuuid
     * @return vo vo
     * @ <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private SbDyVO getBDA0610606FB40(String sbuuid) {
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "/templates/sbdy/BDA0610606/sb2021_zzsybnsrsb40_print_zzbd.doc";
        // final List<Map<String, Object>> hznsfpbList = sb702dao.getBDA0610606FB40(sbuuid);
        // 汇总纳税附加税费分配表 SB_FJSF_HZNSFJSFFPB
        final List<Map<String, Object>> hznsfpbList = zzsYbnsrSbmxGjMapper.queryListHznsfjsffpb(sbuuid);
        if (GyUtils.isNotNull(hznsfpbList)) {
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            final Map<String, Object> ybtseHJMap = new HashMap<>();
            final Map<String, Object> tableMap = new HashMap<>();
            Double ybtseHJ = 0.0;
            final DecimalFormat je = new DecimalFormat("#0.00");
            for (Map<String, Object> map : hznsfpbList) {
                final String zsxmMc = CacheUtils.dm2mc("dm_gy_zsxm", GYSbSdsUtils.cast2StrNew(map.get("zsxmDm")));
                map.put("zsxmDm", zsxmMc);
                final String zspmMc = CacheUtils.dm2mc("dm_gy_zspm", GYSbSdsUtils.cast2StrNew(map.get("zspmDm")));
                map.put("zspmDm", zspmMc);
                // 预征税额
                final Double ybtse = GYCastUtils.getJe(map, "ybtse", je);
                ybtseHJ = GYCastUtils.add(ybtseHJ, ybtse);
            }

            ybtseHJMap.put("ybtseHJ", je.format(ybtseHJ));
            tableMap.put("hznsFjsffpbGrid", hznsfpbList);
            final Map<String, Object> dyMap = new HashMap<>();
            dyMap.put("field", ybtseHJMap);
            dyMap.put("tables", tableMap);
            // vo.setMblj(SbPrintUtils.getPath(mblj));
            vo.setMblj(mblj);
            vo.setDyxx(dyMap);
        }
        return vo;
    }

    // 是否启用附加税作为附表
    public static boolean sfQyFjsfb(String skssqz) {
        String qyrq = CacheUtils.getXtcs("A0000001062019006");
        if (GyUtils.isNull(qyrq)) {
            qyrq = "9999-12-31";
        }
        return GYSbSdsUtils.compareRq(skssqz, qyrq) >= 0;
    }

    /**
     * @name 六税两费数据格式化
     * @description 相关说明
     * @time 创建时间:2022年3月29日上午10:08:29
     * @param lslfMap lslfMap
     * @return map map
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private Map<String, Object> lslf(Map<String, Object> lslfMap) {
        // final String xtcsz = HxzgGyUtils.getXtcs("Z0000099Z02000259", WssbSessionUtils.getSwjgDm());
        if (GYCastUtils.notNull(lslfMap)) {
            // 财行税
            final String bqsfsyzzsxgmnsrjzzc = GYSbSdsUtils.cast2StrNew(lslfMap.get("bqsfsyzzsxgmnsrjzzc"));
            // 增值税一般人
            final String bqsfsyxgmyhzc = GYSbSdsUtils.cast2StrNew(lslfMap.get("bqsfsyxgmyhzc"));
            if ("Y".equals(bqsfsyzzsxgmnsrjzzc) || "Y".equals(bqsfsyxgmyhzc)) {
                // 适用小微企业“六税两费”减征收政策
                lslfMap.put("sfsy", "■ 是 □ 否");
                lslfMap.put("jzzt", GYSbSdsUtils.cast2StrNew(lslfMap.get("jzzcsyztmc")));
            } else {
                lslfMap.put("sfsy", "□ 是 ■ 否");
            }
        }
        return lslfMap;
    }

    @Schema(description = "增值税预缴申报表打印")
    private List<SbDyVO> getBDA0610865bySbuuid(String sbuuid) {
        List<SbDyVO> list = new ArrayList<>();
        // sb_zzsyjnssb_ywb sb_zzsyjnssb_sjb
        Map<String, Object> res = getBDA0610865Zb(sbuuid);
        final SbDyVO yjzb = (SbDyVO)res.get("sbdyvo");
        // 获取主表的数据F100
        if (GYCastUtils.notNull(yjzb)) {
            list.add(yjzb);
        }

        // sb_fjsf
        Map<String, Object> resfb = getBDA0610865fb01(sbuuid);
        final SbDyVO fjb = (SbDyVO)resfb.get("sbdyvo");
        // 获取附加税表的数据
        if (GYCastUtils.notNull(fjb)) {
            list.add(fjb);
        }
        final Map<String, String> csMap = new HashMap<>();
        list = dealwithDyxxList(list, csMap);
        return list;
    }

    @Schema(description = "增值税预缴申报表主表打印")
    private Map<String, Object> getBDA0610865Zb(String sbuuid) {
        final Map<String, Object> reS = new HashMap<>();
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // sb_sbb
        final LambdaQueryWrapper<SbSbbDO> wrapperSbSbb = new LambdaQueryWrapper<>();
        wrapperSbSbb.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapperSbSbb);
        if (GyUtils.isNotNull(sbbDO)) {
            Map<String, Object> sbbMap = getSbbMap(sbbDO);
            String skssqq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqq"));
            String skssqz = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqz"));
            String nsrsbh = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformNsrsbh"));
            String nsrmc = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformNsrmc"));
            String slrq = GYSbSdsUtils.cast2StrNew(sbbMap.get("slrq"));
            String slswjgDm = GYSbSdsUtils.cast2StrNew(sbbMap.get("slswjgDm"));

            fieldMap.put("skssqq",skssqq);
            fieldMap.put("skssqz",skssqz);
            fieldMap.put("nsrsbh",nsrsbh);
            fieldMap.put("nsrmc",nsrmc);
            fieldMap.put("slr","");
            fieldMap.put("slswjg",CacheUtils.dm2mc("dm_gy_swjg", slswjgDm));
            if(GyUtils.isNotNull(slrq)){
                fieldMap.put("slrq",slrq.substring(0,10));
            }
            fieldMap.put("sbsxDm1","预缴申报");
            // 受理信息 增值税预缴没存 目前先空着 已单独从上面赋值
            /*final LambdaQueryWrapper<SbSlxxDTO> slxxWrapper = new LambdaQueryWrapper<>();
            slxxWrapper.eq(SbSlxxDTO::getSbuuid, sbuuid);
            List<SbSlxxDTO> slxxDTO = sbSlxxMapper.selectList(slxxWrapper);
            if (GYCastUtils.notNull(slxxDTO)) {
                final String blrysfzjlxDm = slxxDTO.get(0).getBlrysfzjlxDm();
                final String blrysfzjlxMc = CacheUtils.dm2mc("dm_gy_sfzjlx", blrysfzjlxDm);
                sbbMap.put("blrysfzjlxDm", blrysfzjlxMc);
                fieldMap.putAll(sbbMap);
            }*/
        }
        // sb_zzsyjnssb_ywb
        final LambdaQueryWrapper<SbZzsyjnssbYwbDO> wrapperywb = new LambdaQueryWrapper<>();
        wrapperywb.eq(SbZzsyjnssbYwbDO::getSbuuid, sbuuid);
        log.info("增值税预缴sbuuid" + sbuuid);
        SbZzsyjnssbYwbDO ywbDO = zzsyjywbMapper.selectOne(wrapperywb);
        if (GyUtils.isNotNull(ywbDO)) {
            fieldMap.put("xmbh",ywbDO.getXmbh());
            fieldMap.put("xmmc",ywbDO.getXmmc());
            fieldMap.put("xmdz",ywbDO.getXmdz());
            fieldMap.put("bqybtsecjs",ywbDO.getBqybtsecjs());
            fieldMap.put("bqybtsejyfj",ywbDO.getBqybtsejyfj());
            fieldMap.put("bqybtsedfjyfj",ywbDO.getBqybtsedfjyfj());

            fieldMap.put("dlr",ywbDO.getDlr());
            fieldMap.put("dlrsfzjhm1",ywbDO.getDlrdz());
            fieldMap.put("syybjsff",  "Y".equals(ywbDO.getSyybjsff()) ? "是 √   否 □" : "是 □   否 √");
            fieldMap.put("ybrbz",  "N".equals(ywbDO.getYbrbz()) ? "是 □   否 √" : "是 √   否 □");
            log.info("增值税预缴fieldMap" + fieldMap);
        }else{
            log.info("增值税预缴ywbDO未查到");
        }
        // sb_zzsyjnssb_sjb
        final LambdaQueryWrapper<SbZzsyjnssbSjbDO> wrappersjb = new LambdaQueryWrapper<>();
        wrappersjb.eq(SbZzsyjnssbSjbDO::getSbuuid, sbuuid);
        List<SbZzsyjnssbSjbDO> sjbDOS = zzsyjsjbMapper.selectList(wrappersjb);
        if (GyUtils.isNotNull(sjbDOS)) {
            int i = 1;
            BigDecimal xseHJ = BigDecimal.ZERO;
            BigDecimal kcjeHJ = BigDecimal.ZERO;
            BigDecimal yzseHJ = BigDecimal.ZERO;
            final List<Map<String, Object>> yjxxList = CollectionUtil.newArrayList();
            for(SbZzsyjnssbSjbDO sjbDO:sjbDOS){
                final  Map<String, Object> yjxxmap = new HashMap<String, Object>();
                String yzxmDm = sjbDO.getYzxmDm();
                if(yzxmDm.equals("101016600")){
                    yzxmDm = "出租不动产";
                }else if(yzxmDm.equals("101017700")){
                    yzxmDm = "销售不动产";
                }else if(yzxmDm.equals("101017100")){
                    yzxmDm = "建筑服务";
                }
                yjxxmap.put("yzxmDm",yzxmDm);
                yjxxmap.put("lc",i++);
                yjxxmap.put("xse",sjbDO.getXse());
                yjxxmap.put("kcje",sjbDO.getKcje());
                yjxxmap.put("yzl",sjbDO.getYzl());

                //预征税额 等于 含税金额除以（1+税率） 乘以预征率
                BigDecimal sl1 = NumberUtil.add(NumberUtil.toBigDecimal("1"),sjbDO.getSl1());//相加
                BigDecimal hsje= NumberUtil.sub(sjbDO.getXse(),sjbDO.getKcje());
                BigDecimal bhsje = NumberUtil.div(hsje,sl1);//除以
                BigDecimal yzl = sjbDO.getYzl();
                BigDecimal yzse = NumberUtil.round(NumberUtil.mul(bhsje,yzl),2);//乘以并保留两位
                yjxxmap.put("yzse",yzse);
                yjxxList.add(yjxxmap);
                //销售额
                xseHJ = NumberUtil.add(xseHJ,sjbDO.getXse());
                //扣除金额
                kcjeHJ = NumberUtil.add(kcjeHJ,sjbDO.getKcje());
                //预征税额
                yzseHJ = NumberUtil.add(yzseHJ, yzse);
            }
            tablesMap.put("yzxmGrid", yjxxList);
            fieldMap.put("xseHJ",xseHJ);
            fieldMap.put("kcjeHJ",kcjeHJ);
            fieldMap.put("yzseHJ",yzseHJ);
        }
        dyxx.put("field", fieldMap);
        dyxx.put("tables", tablesMap);
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        if (GYCastUtils.notNull(dyxx)) {
            String mblj = "/templates/sbdy/BDA0610865/sb207_zzsyjnssb2_print.doc";
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        reS.put("sbdyvo", vo);
        return reS;
    }

    @Schema(description = "增值税预缴申报表附加税表打印")
    private Map<String, Object> getBDA0610865fb01(String sbuuid) {
        final Map<String, Object> reS = new HashMap<>();
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // sb_sbb
        final LambdaQueryWrapper<SbSbbDO> wrapperSbSbb = new LambdaQueryWrapper<>();
        wrapperSbSbb.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapperSbSbb);
        if (GyUtils.isNotNull(sbbDO)) {
            Map<String, Object> sbbMap = getSbbMap(sbbDO);
            String skssqq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqq"));
            String skssqz = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqz"));
            String nsrmc = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformNsrmc"));
            fieldMap.put("skssqq",skssqq);
            fieldMap.put("skssqz",skssqz);
            fieldMap.put("nsrmc",nsrmc);
        }
        // sb_sbb
        final LambdaQueryWrapper<SbFjsfSjgjDTO> wrapperfjb = new LambdaQueryWrapper<>();
        wrapperfjb.eq(SbFjsfSjgjDTO::getSbuuid, sbuuid);
        List<SbFjsfSjgjDTO> fjbDOS = fjsfMapper.selectList(wrapperfjb);
        if (GyUtils.isNotNull(fjbDOS)) {

            BigDecimal bqynsfeHJ = BigDecimal.ZERO;
            BigDecimal jmeHJ = BigDecimal.ZERO;
            BigDecimal phjmseHJ = BigDecimal.ZERO;
            BigDecimal bqybtseHJ = BigDecimal.ZERO;
            fieldMap.put("sfsy","否");//森马是否 后续有企业在改
            /*fieldMap.put("jzzt","");
            fieldMap.put("syxgmjzzcqssj","");
            fieldMap.put("syxgmjzzczzsj","");*/

            /*if("Y".equals(sfsy)){
                nsrxxAndSlrxxMap.put("sfcjrh","√是 □否");
            }else{
                nsrxxAndSlrxxMap.put("sfcjrh","□是 √否");
            }*/
            fieldMap.put("sfcjrh","□是 √否");
            final List<Map<String, Object>> yjxxList = CollectionUtil.newArrayList();
            for(SbFjsfSjgjDTO fjbDO:fjbDOS){
                if(!"10101".equals(fjbDO.getZsxmDm())){
                    final  Map<String, Object> yjxxmap = new HashMap<String, Object>();
                    yjxxmap.put("zsxmdm",CacheUtils.dm2mc("dm_gy_zsxm", GYSbSdsUtils.cast2StrNew(fjbDO.getZsxmDm())));
                    yjxxmap.put("ssjmxzdm",CacheUtils.dm2mc("dm_gy_ssjmxz", GYSbSdsUtils.cast2StrNew(fjbDO.getSsjmxzDm())));
                    yjxxmap.put("ybzzs",fjbDO.getYbzzs());
                    yjxxmap.put("sl1",fjbDO.getSl1());
                    yjxxmap.put("bqynsfe",fjbDO.getBqynsfe());
                    yjxxmap.put("jme",fjbDO.getJme());
                    yjxxmap.put("phjzbl",fjbDO.getPhjzbl());
                    yjxxmap.put("phjmse",fjbDO.getPhjmse());
                    yjxxmap.put("bqybtse",fjbDO.getBqybtse());
                    yjxxList.add(yjxxmap);
                    bqynsfeHJ = NumberUtil.add(bqynsfeHJ,fjbDO.getBqynsfe());
                    jmeHJ = NumberUtil.add(jmeHJ,fjbDO.getJme());
                    phjmseHJ = NumberUtil.add(phjmseHJ, fjbDO.getPhjmse());
                    bqybtseHJ = NumberUtil.add(bqybtseHJ, fjbDO.getBqybtse());
                }
            }
            tablesMap.put("mxbGrid", yjxxList);
            fieldMap.put("bqynsfeHJ",bqynsfeHJ);
            fieldMap.put("jmeHJ",jmeHJ);
            fieldMap.put("phjmseHJ",phjmseHJ);
            fieldMap.put("bqybtseHJ",bqybtseHJ);
        }
        dyxx.put("field", fieldMap);
        dyxx.put("tables", tablesMap);
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        if (GYCastUtils.notNull(dyxx)) {
            String mblj = "/templates/sbdy/BDA0610865/sb207_zzsyjnssb01new_lslf_print.doc";
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        reS.put("sbdyvo", vo);
        return reS;
    }

    @Schema(description = "增值税小规模申报表打印")
    private List<SbDyVO> getBDA0610611bySbuuid(String sbuuid) {
        List<SbDyVO> list = new ArrayList<>();
        // SB_ZZS_XGM 主表
        Map<String, Object> res = getBDA0610611Zb(sbuuid);
        final SbDyVO xgmzb = (SbDyVO)res.get("sbdyvo");
        // 获取主表的数据
        if (GYCastUtils.notNull(xgmzb)) {
            list.add(xgmzb);
        }

        //SB_ZZS_XGM_FB_FLZL 附表一
        Map<String, Object> resfb = getBDA0610611fb01(sbuuid);
        final SbDyVO xgmfb1 = (SbDyVO)resfb.get("sbdyvo");
        // 获取附加税表的数据
        if (GYCastUtils.notNull(xgmfb1)) {
            list.add(xgmfb1);
        }

        //sb_fjsf 附表二
        Map<String, Object> resfjsb = getBDA0610611fb02(sbuuid);
        final SbDyVO xgmfb2 = (SbDyVO)resfjsb.get("sbdyvo");
        // 获取附加税表的数据
        if (GYCastUtils.notNull(xgmfb2)) {
            list.add(xgmfb2);
        }

        // sb_zzs_fb_zzsjmssbmxb_jsxm sb_zzs_fb_zzsjmssbmxb_msxm 减免税表
        Map<String, Object> resJsmsb = getBDA0610611jmsb(sbuuid);
        final SbDyVO xgmjsmsb = (SbDyVO)resJsmsb.get("sbdyvo");
        // 获取附加税表的数据
        if (GYCastUtils.notNull(xgmjsmsb)) {
            list.add(xgmjsmsb);
        }

        final Map<String, String> csMap = new HashMap<>();
        list = dealwithDyxxList(list, csMap);
        return list;
    }

    @Schema(description = "增值税小规模申报表主表打印")
    private Map<String, Object> getBDA0610611Zb(String sbuuid) {
        final Map<String, Object> reS = new HashMap<>();
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // sb_sbb
        final LambdaQueryWrapper<SbSbbDO> wrapperSbSbb = new LambdaQueryWrapper<>();
        wrapperSbSbb.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapperSbSbb);
        if (GyUtils.isNotNull(sbbDO)) {
            Map<String, Object> sbbMap = getSbbMap(sbbDO);
            String skssqq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqq"));
            String skssqz = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqz"));
            String nsrsbh = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformNsrsbh"));
            String nsrmc = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformNsrmc"));
            String tbrq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformTbrq1"));
            fieldMap.put("nsrxxformSkssqq",skssqq);
            fieldMap.put("nsrxxformSkssqz",skssqz);
            fieldMap.put("nsrxxformNsrsbh",nsrsbh);
            fieldMap.put("nsrxxformNsrmc",nsrmc);
            fieldMap.put("nsrxxformTbrq1",tbrq);
        }
        // SB_SLXX
        final LambdaQueryWrapper<SbSlxxDTO> slxxWrapper = new LambdaQueryWrapper<>();
        slxxWrapper.eq(SbSlxxDTO::getSbuuid, sbuuid);
        List<SbSlxxDTO> slxxDTO = sbSlxxMapper.selectList(slxxWrapper);
        if (GyUtils.isNotNull(slxxDTO)) {
            fieldMap.put("dlr", slxxDTO.get(0).getJbr());
            fieldMap.put("blrysfzjhm", slxxDTO.get(0).getJbrsfzjhm());
            fieldMap.put("slr", slxxDTO.get(0).getSlrxm());
            Date slrqdata = slxxDTO.get(0).getSlrq();
            if(GyUtils.isNotNull(slrqdata)){
                final String slrqStr = DateUtils.dateToString(slrqdata, 3);
                fieldMap.put("slxxformSlrq", slrqStr);
            }
            fieldMap.put("slswjg", slxxDTO.get(0).getSlswjgmc());
        }
        // sb_zzs_xgm
        final LambdaQueryWrapper<SbZzsXgmDO> zbxxWrapper = new LambdaQueryWrapper<>();
        zbxxWrapper.eq(SbZzsXgmDO::getSbuuid, sbuuid);
        List<SbZzsXgmDO> zbxxDTO = sbZzsXgmMapper.selectList(zbxxWrapper);
        List<Map<String, Object>> zbxxlistmap = BeanListTomapList(zbxxDTO);
        if (GyUtils.isNotNull(zbxxlistmap)) {
            for(Map<String, Object> zbmap : zbxxlistmap){
                final Long ewblxh = (Long) zbmap.get("ewblxh");
                for (String key : zbmap.keySet()) {
                    //以key+二维表列序号的方式组合到打印信息中，例如key为msxse，那么第二列该项的名称为msxse2
                    fieldMap.put(key + ewblxh.toString(), zbmap.get(key));
                }
            }
        }
        dyxx.put("field", fieldMap);
        dyxx.put("tables", tablesMap);
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        if (GYCastUtils.notNull(dyxx)) {
            String mblj = "/templates/sbdy/BDA0610611/sb2031_zzsxgmnsrsbygz002_print_zzbd.doc";
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        reS.put("sbdyvo", vo);
        return reS;
    }

    @Schema(description = "增值税小规模申报表附表一打印")
    private Map<String, Object> getBDA0610611fb01(String sbuuid) {
        final Map<String, Object> reS = new HashMap<>();
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // sb_sbb
        final LambdaQueryWrapper<SbSbbDO> wrapperSbSbb = new LambdaQueryWrapper<>();
        wrapperSbSbb.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapperSbSbb);
        if (GyUtils.isNotNull(sbbDO)) {
            Map<String, Object> sbbMap = getSbbMap(sbbDO);
            String skssqq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqq"));
            String skssqz = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqz"));
            String nsrmc = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformNsrmc"));
            String tbrq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformTbrq1"));
            fieldMap.put("nsrxxformSkssqq",skssqq);
            fieldMap.put("nsrxxformSkssqz",skssqz);
            fieldMap.put("nsrxxformNsrmc",nsrmc);
            fieldMap.put("nsrxxformTbrq1",tbrq);
        }
        //SB_ZZS_XGM_FB_FLZL
        final LambdaQueryWrapper<SbZzsXgmFbFlzlDO> wrapperxgmfb1 = new LambdaQueryWrapper<>();
        wrapperxgmfb1.eq(SbZzsXgmFbFlzlDO::getSbuuid, sbuuid);
        SbZzsXgmFbFlzlDO fb1sbbDO = sbZzsXgmfb1Mapper.selectOne(wrapperxgmfb1);
        if (GyUtils.isNotNull(fb1sbbDO)) {
            fieldMap.put("qcye",fb1sbbDO.getQcye());
            fieldMap.put("bqfse",fb1sbbDO.getBqfse());
            fieldMap.put("bqkce",fb1sbbDO.getBqkce());
            fieldMap.put("qmye",fb1sbbDO.getQmye());

            fieldMap.put("ysfwxsqbhssr",fb1sbbDO.getYsfwxsqbhssr());
            fieldMap.put("ysfwxshsxse",fb1sbbDO.getYsfwxshsxse());
            fieldMap.put("ysfwxsbhsxse",fb1sbbDO.getYsfwxsbhsxse());

            fieldMap.put("qcye5",fb1sbbDO.getQcye5());
            fieldMap.put("bqfse5",fb1sbbDO.getBqfse5());
            fieldMap.put("bqkce5",fb1sbbDO.getBqkce5());
            fieldMap.put("qmye5",fb1sbbDO.getQmye5());

            fieldMap.put("ysfwxsqbhssr5",fb1sbbDO.getYsfwxsqbhssr5());
            fieldMap.put("ysfwxshsxse5",fb1sbbDO.getYsfwxsbhsxse5());
            fieldMap.put("ysfwxsbhsxse5",fb1sbbDO.getYsfwxsbhsxse5());

        }
        dyxx.put("field", fieldMap);
        dyxx.put("tables", tablesMap);
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        if (GYCastUtils.notNull(dyxx)) {
            String mblj = "/templates/sbdy/BDA0610611/sb2031_zzsxgmnsrsbygz012_print_zzbd.doc";
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        reS.put("sbdyvo", vo);
        return reS;
    }

    @Schema(description = "增值税小规模申报表附表二打印")
    private Map<String, Object> getBDA0610611fb02(String sbuuid) {
        final Map<String, Object> reS = new HashMap<>();
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // sb_sbb
        final LambdaQueryWrapper<SbSbbDO> wrapperSbSbb = new LambdaQueryWrapper<>();
        wrapperSbSbb.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapperSbSbb);
        if (GyUtils.isNotNull(sbbDO)) {
            Map<String, Object> sbbMap = getSbbMap(sbbDO);
            String skssqq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqq"));
            String skssqz = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqz"));
            String nsrmc = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformNsrmc"));
            fieldMap.put("nsrxxformSkssqq",skssqq);
            fieldMap.put("nsrxxformSkssqz",skssqz);
            fieldMap.put("nsrxxformNsrmc",nsrmc);
        }

        // sb_fjsf
        final LambdaQueryWrapper<SbFjsfSjgjDTO> wrapperfjb = new LambdaQueryWrapper<>();
        wrapperfjb.eq(SbFjsfSjgjDTO::getSbuuid, sbuuid);
        List<SbFjsfSjgjDTO> fjbDOS = fjsfMapper.selectList(wrapperfjb);
        if (GyUtils.isNotNull(fjbDOS)) {

            BigDecimal ybzzsHJ = BigDecimal.ZERO;
            BigDecimal bqynsfeHJ = BigDecimal.ZERO;
            BigDecimal jmeHJ = BigDecimal.ZERO;
            BigDecimal phjmseHJ = BigDecimal.ZERO;
            BigDecimal bqyjseHJ = BigDecimal.ZERO;
            BigDecimal bqybtseHJ = BigDecimal.ZERO;

            final List<Map<String, Object>> yjxxList = CollectionUtil.newArrayList();
            for(SbFjsfSjgjDTO fjbDO:fjbDOS){
                if(!"10101".equals(fjbDO.getZsxmDm())){
                    final  Map<String, Object> yjxxmap = new HashMap<String, Object>();
                    yjxxmap.put("sfz",CacheUtils.dm2mc("dm_gy_zsxm", GYSbSdsUtils.cast2StrNew(fjbDO.getZsxmDm())));
                    yjxxmap.put("ssjmxzDm",CacheUtils.dm2mc("dm_gy_ssjmxz", GYSbSdsUtils.cast2StrNew(fjbDO.getSsjmxzDm())));
                    yjxxmap.put("ybzzs",fjbDO.getYbzzs());
                    yjxxmap.put("sl1",fjbDO.getSl1());
                    yjxxmap.put("bqynsfe",fjbDO.getBqynsfe());
                    yjxxmap.put("jme",fjbDO.getJme());
                    yjxxmap.put("phjzbl",fjbDO.getPhjzbl());
                    yjxxmap.put("phjmse",fjbDO.getPhjmse());
                    yjxxmap.put("bqyjse",fjbDO.getBqyjse());
                    yjxxmap.put("bqybtse",fjbDO.getBqybtse());
                    yjxxList.add(yjxxmap);

                    ybzzsHJ = NumberUtil.add(ybzzsHJ,fjbDO.getYbzzs());
                    bqynsfeHJ = NumberUtil.add(bqynsfeHJ,fjbDO.getBqynsfe());
                    jmeHJ = NumberUtil.add(jmeHJ,fjbDO.getJme());
                    phjmseHJ = NumberUtil.add(phjmseHJ, fjbDO.getPhjmse());
                    bqyjseHJ = NumberUtil.add(bqyjseHJ, fjbDO.getBqyjse());
                    bqybtseHJ = NumberUtil.add(bqybtseHJ, fjbDO.getBqybtse());
                }
            }
            tablesMap.put("mxbGrid", yjxxList);
            fieldMap.put("ybzzsHJ",ybzzsHJ);
            fieldMap.put("bqynsfeHJ",bqynsfeHJ);
            fieldMap.put("jmeHJ",jmeHJ);
            fieldMap.put("phjmseHJ",phjmseHJ);
            fieldMap.put("bqyjseHJ",bqyjseHJ);
            fieldMap.put("bqybtseHJ",bqybtseHJ);
        }
        dyxx.put("field", fieldMap);
        dyxx.put("tables", tablesMap);
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        if (GYCastUtils.notNull(dyxx)) {
            String mblj = "/templates/sbdy/BDA0610611/sb2031_zzsxgmnsrsbygz08_print_zzbd.doc";
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        reS.put("sbdyvo", vo);
        return reS;
    }

    @Schema(description = "增值税小规模申报表减免税表打印")
    private Map<String, Object> getBDA0610611jmsb(String sbuuid) {
        final Map<String, Object> reS = new HashMap<>();
        // 最终返回的打印信息Map
        final Map<String, Object> dyxx = new HashMap<>();
        final Map<String, Object> fieldMap = new HashMap<>();
        final Map<String, Object> tablesMap = new HashMap<>();
        // sb_sbb
        final LambdaQueryWrapper<SbSbbDO> wrapperSbSbb = new LambdaQueryWrapper<>();
        wrapperSbSbb.eq(SbSbbDO::getSbuuid, sbuuid);
        SbSbbDO sbbDO = sbSbbDOMapper.selectOne(wrapperSbSbb);
        if (GyUtils.isNotNull(sbbDO)) {
            Map<String, Object> sbbMap = getSbbMap(sbbDO);
            String skssqq = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqq"));
            String skssqz = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformSkssqz"));
            String nsrmc = GYSbSdsUtils.cast2StrNew(sbbMap.get("nsrxxformNsrmc"));
            fieldMap.put("nsrxxformSkssqq",skssqq);
            fieldMap.put("nsrxxformSkssqz",skssqz);
            fieldMap.put("nsrxxformNsrmc",nsrmc);
        }

        // sb_zzs_fb_zzsjmssbmxb_jsxm
        final LambdaQueryWrapper<SbZzsFbZzsjmssbmxbJsxmSjgjDTO> wrapperjsb = new LambdaQueryWrapper<>();
        wrapperjsb.eq(SbZzsFbZzsjmssbmxbJsxmSjgjDTO::getSbuuid, sbuuid);
        List<SbZzsFbZzsjmssbmxbJsxmSjgjDTO> jsmxDOS = sbZzsFbZzsjmssbmxbJsxmMapper.selectList(wrapperjsb);
        List<Map<String, Object>> jsxxlistmap = BeanListTomapList(jsmxDOS);
        if (GyUtils.isNotNull(jsxxlistmap)) {
            final List<Map<String, Object>> zzsjmssbmxbjsxmGrid = new ArrayList<Map<String, Object>>();
            for(Map<String, Object> zbmap : jsxxlistmap){
                final Long ewbhxh = (Long) zbmap.get("ewblxh");
                final String hmc = GYCastUtils.cast2Str(zbmap.get("hmc"));
                if (ewbhxh == 1 || "合计".equals(hmc)) {
                    fieldMap.put("qcyeHJ", zbmap.get("qcye"));
                    fieldMap.put("bqfseHJ", zbmap.get("bqfse"));
                    fieldMap.put("bqydjseHJ", zbmap.get("bqydjse"));
                    fieldMap.put("bqsjdjseHJ", zbmap.get("bqsjdjse"));
                    fieldMap.put("qmyeHJ", zbmap.get("qmye"));
                } else {
                    final Map<String, Object> jsMap = new HashMap<String, Object>(zbmap);
                    jsMap.put("lc", ewbhxh - 1);
                    zzsjmssbmxbjsxmGrid.add(jsMap);
                }
            }
            tablesMap.put("zzsjmssbmxbjsxmGrid", zzsjmssbmxbjsxmGrid);
        }

        // sb_zzs_fb_zzsjmssbmxb_msxm
        final LambdaQueryWrapper<SbZzsFbZzsjmssbmxbMsxmSjgjDTO> wrappermsb = new LambdaQueryWrapper<>();
        wrappermsb.eq(SbZzsFbZzsjmssbmxbMsxmSjgjDTO::getSbuuid, sbuuid);
        List<SbZzsFbZzsjmssbmxbMsxmSjgjDTO> msmxDOS = sbZzsFbZzsjmssbmxbMsxmMapper.selectList(wrappermsb);
        List<Map<String, Object>> msxxlistmap = BeanListTomapList(msmxDOS);
        if (GyUtils.isNotNull(msxxlistmap)) {
            final List<Map<String, Object>> zzsjmssbmxbmsxmGrid = new ArrayList<Map<String, Object>>();
            for(Map<String, Object> zbmap : msxxlistmap){
                final Long ewbhxh = (Long) zbmap.get("ewblxh");
                final String hmc = GYCastUtils.cast2Str(zbmap.get("hmc"));
                if (ewbhxh == 1 || "合计".equals(hmc)) {
                    fieldMap.put("mzzzsxmxseHJ", zbmap.get("mzzzsxmxse"));
                    fieldMap.put("bqsjkcjeHJ", zbmap.get("bqsjkcje"));
                    fieldMap.put("kchmsxseHJ", zbmap.get("kchmsxse"));
                    fieldMap.put("msxsedyjxseHJ", zbmap.get("msxsedyjxse"));
                    fieldMap.put("mseHJ", zbmap.get("mse"));
                } else if (ewbhxh == 2 || "出口免税".equals(hmc)) {
                    fieldMap.put("ckms", zbmap.get("mzzzsxmxse"));
                } else if (ewbhxh == 3 || "其中:跨境服务".equals(hmc)) {
                    fieldMap.put("qzKjfw", zbmap.get("mzzzsxmxse"));
                } else {
                    final Map<String, Object> msMap = new HashMap<String, Object>(zbmap);
                    msMap.put("lc", ewbhxh - 3);
                    zzsjmssbmxbmsxmGrid.add(msMap);
                }
            }
            tablesMap.put("zzsjmssbmxbmsxmGrid", zzsjmssbmxbmsxmGrid);
        }
        dyxx.put("field", fieldMap);
        dyxx.put("tables", tablesMap);
        SbDyVO vo = null;
        // 表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        if (GYCastUtils.notNull(dyxx)) {
            String mblj = "/templates/sbdy/BDA0610611/sb2031_zzsxgmnsrsbygz07_print_zzbd.doc";
            // 查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxx);
        }
        reS.put("sbdyvo", vo);
        return reS;

    }

    @Schema(description = "BeanListTomapList")
    public List<Map<String, Object>> BeanListTomapList(List<?> sources) {
        if (sources == null) {
            return null;
        } else {
            List<Map<String, Object>> targets = new ArrayList(sources.size());
            if (!sources.isEmpty()) {
                sources.forEach((source) -> {
                    targets.add(BeanToMap(source));
                });
            }

            return targets;
        }
    }

    @Schema(description = "BeanToMap")
    public static <T> Map BeanToMap(Object bean) {
        ObjectMapper objectMapper = new ObjectMapper();
        //防止包含空的字段
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(bean, Map.class);
    }

    private List<SbDyVO> getBDA0611159bySbuuid(String sbuuid){
        List<SbDyVO> list = new ArrayList<SbDyVO>();
        //获取主表的数据
        final SbDyVO zb = getBDA0611159A200000(sbuuid);
        if (GYCastUtils.notNull(zb)) {
            list.add(zb);
        }

        //获取A201020表资产加速折旧、摊销(扣除)优惠明细表
        final SbDyVO A201020 = getBDA0611159A201020(sbuuid);
        if (GYCastUtils.notNull(A201020)) {
            list.add(A201020);
        }

        //获取A202000企业所得税汇总纳税分支机构所得税分配表
        final SbDyVO A202000 = getBDA0611033A202000(sbuuid);
        if (GYCastUtils.notNull(A202000)) {
            list.add(A202000);
        }

        //获取技术成果投资入股企业所得税递延纳税备案表
        final SbDyVO dynsbab = getBDA0611033Dynsbab(sbuuid);
        if (GYCastUtils.notNull(dynsbab)) {
            list.add(dynsbab);
        }
        //精度参数Map，本例中精度都默认保留小数点后2位，所以为空，其他用例有特殊要求的话自己构造
        final Map<String, String> csMap = new HashMap<String, String>();
        csMap.put("fpbl", "########0.0000000000");//--分支机构分配表中分配比例
        //对List中打印参数的数值进行处理
        list = dealwithDyxxList(list, csMap);
        log.info("企业所得税预缴申报信息查询组装返回列表为：{}",JsonUtils.toJson(list));
        return list;
    }

    private SbDyVO getBDA0611159A200000(String sbuuid){
        SbDyVO vo = null;
        //表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "templates/sbdy/BDA0611159/sb228_A200000_2021_print.doc";
        //最终返回的打印信息Map
        final Map<String, Object> dyxxMap = new HashMap<String, Object>();
        final Map<String, Object> fieldMap = new HashMap<String, Object>();
        final Map<String, Object> tableMap = new HashMap<String, Object>();
        //首先查询SB_SBB获取表头表位信息，如果这个查不着，都不需要再查询结构化表了
        final Map<String, Object> sbbMap = sbSbbDOMapper.queryBDA0611159A200000(sbuuid);
        log.info("企业所得税预缴申报打印生成时查询sbsbb返回数据：{}",JsonUtils.toJson(sbbMap));
        if (GYCastUtils.notNull(sbbMap)) {
            final String skssqq = MapUtil.getStr(sbbMap,"nsrxxformSkssqq");
            final String nsrxxformNsrsbh = sbbMap.get("nsrxxformNsrsbh").toString();
            sbbMap.put("nsrxxformNsrsbh", nsrxxformNsrsbh);
            final String skssqqStr = DateUtils.dateToString(DateUtils.toDate(skssqq,"yyyy-MM-dd"), 7);
            sbbMap.put("nsrxxformSkssqq", skssqqStr);
            final String skssqz = MapUtil.getStr(sbbMap,"nsrxxformSkssqz");
            final String skssqzStr = DateUtils.dateToString(DateUtils.toDate(skssqz,"yyyy-MM-dd"), 7);
            sbbMap.put("nsrxxformSkssqz", skssqzStr);
            final String nsrxxformNsrmc = sbbMap.get("nsrxxformNsrmc").toString();
            sbbMap.put("nsrxxformNsrmc", nsrxxformNsrmc);
            fieldMap.putAll(sbbMap);
            //然后查询sb_sds_jmcz_18yjd结构化表，获取结构化表的数据，如果没数据，就不需要给dyxxMap设置值了
            final Map<String, Object> jmcz18yjd = sbSdsJmcz18yjdMapper.queryBDA0611159A200000(sbuuid);
            if (GYCastUtils.notNull(jmcz18yjd)) {
                fieldMap.putAll(jmcz18yjd);
                fieldMap.put("gjxzhjzhy",MapUtil.getStr(fieldMap,"gjxzhjzhy1"));
                dyxxMap.put("field", fieldMap);
            }
            //然后查询SB_SDS_JMCZ_21YJD_ZBFBSX结构化表，获取主表附报事项数据，如果没数据，就不需要给dyxxMap设置值了
            final List<Map<String, Object>> fbsxlist = sbSdsJmcz21yjdZbfbsxMapper.queryBDA0611159A200000(sbuuid);
            final Map<String, Object> fbsxmap = new HashMap<String, Object>();
            String sx1 = "0.0";
            String sx2 = "0.0";
            String sx3 = "";

            if (GYCastUtils.notNull(fbsxlist)) {
                for(Map<String, Object> tempfbsxmap : fbsxlist) {
                    final String jehxxz = GYCastUtils.notNull(tempfbsxmap.get("jehxxz"))?tempfbsxmap.get("jehxxz").toString():"";
                    final String ewbhgjz = GYCastUtils.notNull(tempfbsxmap.get("ewbhgjz"))?tempfbsxmap.get("ewbhgjz").toString():"";
                    if("K01001".equals(ewbhgjz)) {
                        sx1 = jehxxz;
                    }
                    if("K01002".equals(ewbhgjz)) {
                        sx2 = jehxxz;
                    }
                    if("Y01001".equals(ewbhgjz)) {
                        sx3 = "0".equals(jehxxz)?"原政策":"";
                        sx3 = "1".equals(jehxxz)?"新政策":"";
                    }
                }
                fbsxmap.put("sx1", sx1);
                fbsxmap.put("sx2", sx2);
                fbsxmap.put("sx3", sx3);

                fieldMap.putAll(fbsxmap);
            }
            final String slrq = DateUtils.dateToString(MapUtil.getDate(fieldMap,"slrq"),3);
            final String slrqStr = DateUtils.dateToString(DateUtils.toDate(slrq,"yyyy-MM-dd"), 7);
            fieldMap.put("slrq", slrqStr);
            //然后查询SB_SDS_JMCZ_21YJD_ZBJMXX结构化表，获取主表减免信息表(主表7、8、13行）的数据，如果没数据，就不需要给dyxxMap设置值了
            final List<Map<String, Object>> jmxxlist = sbSdsJmcz21yjdZbjmxxMapper.queryBDA0611159A200000(sbuuid);
            if (GYCastUtils.notNull(jmxxlist)) {
                final List<Map<String, Object>> mssrGrid = new ArrayList<Map<String,Object>>();
                final List<Map<String, Object>> sdjmGrid = new ArrayList<Map<String,Object>>();
                final List<Map<String, Object>> jmsdGrid = new ArrayList<Map<String,Object>>();
                for(Map<String, Object> tempjmxx : jmxxlist) {
                    final String ewbhgjz = GYCastUtils.notNull(tempjmxx.get("ewbhgjz"))?tempjmxx.get("ewbhgjz").toString():"";
                    final String ewbhxh = GYCastUtils.notNull(tempjmxx.get("ewbhxh"))?tempjmxx.get("ewbhxh").toString():"";

                    final String yhswsxDm = GYCastUtils.notNull(tempjmxx.get("yhswsx"))?tempjmxx.get("yhswsx").toString().trim():"";
                    String yhswsxmc = "";
                    final List<Map<String,Object>> jmxzdzList = CacheUtils.getTableData("cs_sb_sdsyhsxpzb");
                    for (Map<String, Object> mxMap : jmxzdzList) {
                        final String sdsyhswsx = MapUtil.getStr(mxMap,"sdsyhswsx");
                        final Date yxqqDate = DateUtils.stringToDate(String.valueOf(mxMap.get("yxqq")));
                        final Date yxqzDate = DateUtils.stringToDate(String.valueOf(mxMap.get("yxqz")));
                        final Date skssqqDate = DateUtils.toDate(skssqq,"yyyy-MM-dd");
                        final Date skssqzDate = DateUtils.toDate(skssqz,"yyyy-MM-dd");
                        if ((sdsyhswsx.equals(yhswsxDm)) && (yxqqDate.before(skssqqDate) || yxqqDate.equals(skssqqDate)) && (yxqzDate.after(skssqzDate) || yxqzDate.equals(skssqzDate))) {
                            yhswsxmc = (String) mxMap.get("sdsyhswsxmc");
                            break;
                        }
                    }
                    //final String yhswsxmc = GYCastUtils.getMcByDm("CS_SB_SDSYHSXPZB_PRINT", yhswsxDm, "SDSYHSWSXMC");
                    tempjmxx.put("ssjmxzmc", yhswsxmc);
                    if("mssrGrid".equals(ewbhgjz)) {
                        tempjmxx.put("ewbhxh", "7."+ewbhxh);
                        mssrGrid.add(tempjmxx);
                        continue;
                    }
                    if("sdjmGrid".equals(ewbhgjz)) {
                        tempjmxx.put("ewbhxh", "8."+ewbhxh);
                        sdjmGrid.add(tempjmxx);
                        continue;
                    }
                    if("jmsdGrid".equals(ewbhgjz)) {
                        final double yhjmje = GYCastUtils.cast2Double(GYCastUtils.notNull(tempjmxx.get("yhjmje"))?tempjmxx.get("yhjmje").toString():"0.00");
                        if(yhjmje>0&&GyUtils.isNull(yhswsxmc)){//如果减免金额大于0并且结构化表中没有对应减免性质，则默认是符合条件的小型微利企业减免企业所得税
                            tempjmxx.put("ewbhxh", "13."+ewbhxh);
                            tempjmxx.put("ssjmxzmc", "符合条件的小型微利企业减免企业所得税");
                            jmsdGrid.add(tempjmxx);
                            continue;
                        }else{
                            tempjmxx.put("ewbhxh", "13."+ewbhxh);
                            jmsdGrid.add(tempjmxx);
                            continue;
                        }
                    }
                }
                //为空则设置为null
                if (!GyUtils.isNull(mssrGrid)){
                    tableMap.put("mssrGrid", mssrGrid);
                }else{
                    tableMap.put("mssrGrid", null);
                }
                if (!GyUtils.isNull(sdjmGrid)){
                    tableMap.put("sdjmGrid", sdjmGrid);
                }else{
                    tableMap.put("sdjmGrid", null);
                }
                if (!GyUtils.isNull(jmsdGrid)){
                    tableMap.put("jmsdGrid", jmsdGrid);
                }else{
                    tableMap.put("jmsdGrid", null);
                }
            }
            dyxxMap.put("tables", tableMap);
        }

        if (GYCastUtils.notNull(dyxxMap)) {
            //查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            //ycjbrzjhm(dyxx,"jbrsfzjhm");
            vo.setMblj(mblj);
            vo.setDyxx(dyxxMap);
        }
        return vo;
    }

    private SbDyVO getBDA0611159A201020(String sbuuid){
        SbDyVO vo = null;
        //表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "templates/sbdy/BDA0611159/sb228_A201020_2021_print.doc";
        //根据企业所得税税款所属期止判断企业所得税查账征收表所使用的模板版本 end
        //最终返回的打印信息Map
        final Map<String, Object> dyxxMap = new HashMap<String, Object>();
        final Map<String, Object> fieldMap = new HashMap<String, Object>();
        final Map<String, Object> tableMap = new HashMap<String, Object>();
        //然后查询SB_SDS_JMCZ_21YJD_ZCZJMXB结构化表，获取结构化表的数据，如果没数据，就不需要给dyxxMap设置值了
        final List<Map<String, Object>> zczjmx = sbSdsJmcz21yjdZczjmxbMapper.queryBDA0611159A201020(sbuuid);
        if (GYCastUtils.notNull(zczjmx)) {
            final List<Map<String, Object>> jszjGrid = new ArrayList<Map<String,Object>>();
            final List<Map<String, Object>> yckcGrid = new ArrayList<Map<String,Object>>();

            for(Map<String, Object> tempzczj : zczjmx) {
                final String ewbhgjz = GYCastUtils.notNull(tempzczj.get("ewbhgjz"))?tempzczj.get("ewbhgjz").toString():"";
                final String ewbhxh = GYCastUtils.notNull(tempzczj.get("ewbhxh"))?tempzczj.get("ewbhxh").toString():"";

                if("HJ".equals(ewbhgjz)) {
                    fieldMap.putAll(tempzczj);
                    continue;
                }
                final String yhswsx = GYCastUtils.notNull(tempzczj.get("yhswsx"))?tempzczj.get("yhswsx").toString().trim():"";
                //final String ssjmxzDm = GYCastUtils.notNull(tempzczj.get("ssjmxzDm"))?tempzczj.get("ssjmxzDm").toString():"";
                //final String ssjmxzmc = GYCastUtils.getMcByDm("DM_GY_SSJMXZ", ssjmxzDm, "SSJMXZMC");
                final String yhswsxmc = CacheUtils.dm2mc("cs_sb_sdsyhsxpzb_print", yhswsx);
                tempzczj.put("xmmc", yhswsxmc);
                if("JSZJ".equals(ewbhgjz)) {
                    if("0".equals(ewbhxh)) {
                        final Map<String, Object> temphjmap = new HashMap<String, Object>();
                        for(String tempkey:tempzczj.keySet()) {
                            temphjmap.put("jszj"+tempkey, tempzczj.get(tempkey));
                        }
                        fieldMap.putAll(temphjmap);
                    }else {
                        tempzczj.put("ewbhxh", "1."+ewbhxh);
                        jszjGrid.add(tempzczj);
                    }
                    continue;
                }
                if("YCKC".equals(ewbhgjz)) {
                    if("0".equals(ewbhxh)) {
                        final Map<String, Object> temphjmap = new HashMap<String, Object>();
                        for(String tempkey:tempzczj.keySet()) {
                            temphjmap.put("yckc"+tempkey, tempzczj.get(tempkey));
                        }
                        fieldMap.putAll(temphjmap);
                    }else {
                        tempzczj.put("ewbhxh", "2."+ewbhxh);
                        yckcGrid.add(tempzczj);
                    }
                    continue;
                }
            }
            tableMap.put("jszjGrid", jszjGrid);
            tableMap.put("yckcGrid", yckcGrid);
            dyxxMap.put("tables", tableMap);
            dyxxMap.put("field", fieldMap);
        }
        if (GYCastUtils.notNull(dyxxMap)) {
            //查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxxMap);
        }
        return vo;
    }

    private SbDyVO getBDA0611033A202000(String sbuuid) {
        SbDyVO vo = null;
        //表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        String mblj = "templates/sbdy/BDA0611159/sb222_A202000_2019_print.doc";
        //根据企业所得税税款所属期止判断企业所得税查账征收表所使用的模板版本 begin
        /*if(!"".equals(sdsVersion)){//202006新版本表样并且新冠疫情减免版本
            mblj = "template/sbdy/BDA0611033/sb222_A202000_2020_print.doc";
        }*/
        //根据企业所得税税款所属期止判断企业所得税查账征收表所使用的模板版本 end
        //最终返回的打印信息Map
        final Map<String, Object> dyxxMap = new HashMap<String, Object>();
        final Map<String, Object> fieldMap = new HashMap<String, Object>();
        final Map<String, Object> tableMap = new HashMap<String, Object>();
        //然后查询sb_sds_fzjgfpb_zjg结构化表，获取结构化表的数据，如果没数据，就不需要给dyxxMap设置值了
        final Map<String, Object> jmcz18yjdfzjgfpbzjg = sbSdsFzjgfpbZjgMapper.queryBDA0611033A202000(sbuuid);
        if (GYCastUtils.notNull(jmcz18yjdfzjgfpbzjg)) {
            //查询纳税人信息获取机构名称zjgmc
            final String zjgdjxh = MapUtil.getStr(jmcz18yjdfzjgfpbzjg,"zjgdjxh");
            final String zjgnsrsbh = MapUtil.getStr(jmcz18yjdfzjgfpbzjg,"zjgnsrsbh");
            CommonResult<CompanyBasicInfoDTO> result = companyApi.basicInfo(zjgdjxh,zjgnsrsbh);
            if (!GyUtils.isNull(result.getData())){
                jmcz18yjdfzjgfpbzjg.put("zjgmc",result.getData().getNsrmc());
            }
            fieldMap.putAll(jmcz18yjdfzjgfpbzjg);
            //查询分支机构分配信息
            final List<Map<String, Object>> fzjgList = sbSdsFzjgfpbFzjgMapper.queryBDA0611033A202000(sbuuid);
            if (GYCastUtils.notNull(fzjgList)) {
                tableMap.put("fzjgGrid", fzjgList);
                //计算合计值
                double fzjgsrzeHJ = 0.0;
                double fzjggzzeHJ = 0.0;
                double fzjgzczeHJ = 0.0;
                double fpblHJ = 0.0;
                double xsdfjmjeHJ = 0.0;
                double fpseHJ = 0.0;
                for (Map<String, Object> map : fzjgList) {
                    final BigDecimal fzjgsrze = GYCastUtils.isNull(map.get("fzjgsrze")) ? new BigDecimal(0) : (BigDecimal) map.get("fzjgsrze");
                    final BigDecimal fzjggzze = GYCastUtils.isNull(map.get("fzjggzze")) ? new BigDecimal(0) : (BigDecimal) map.get("fzjggzze");
                    final BigDecimal fzjgzcze = GYCastUtils.isNull(map.get("fzjgzcze")) ? new BigDecimal(0) : (BigDecimal) map.get("fzjgzcze");
                    final BigDecimal fpbl = GYCastUtils.isNull(map.get("fpbl")) ? new BigDecimal(0) : (BigDecimal) map.get("fpbl");
                    final BigDecimal xsdfjmje = GYCastUtils.isNull(map.get("xsdfjmje")) ? new BigDecimal(0) : (BigDecimal) map.get("xsdfjmje");
                    final BigDecimal fpse = GYCastUtils.isNull(map.get("fzjgfpse")) ? new BigDecimal(0) : (BigDecimal) map.get("fzjgfpse");
                    fzjgsrzeHJ = new BigDecimal(fzjgsrzeHJ).add(fzjgsrze).doubleValue();
                    fzjggzzeHJ = new BigDecimal(fzjggzzeHJ).add(fzjggzze).doubleValue();
                    fzjgzczeHJ = new BigDecimal(fzjgzczeHJ).add(fzjgzcze).doubleValue();
                    fpblHJ = new BigDecimal(fpblHJ).add(fpbl).doubleValue();
                    xsdfjmjeHJ = new BigDecimal(xsdfjmjeHJ).add(xsdfjmje).doubleValue();
                    fpseHJ = new BigDecimal(fpseHJ).add(fpse).doubleValue();
                }
                fieldMap.put("fzjgsrzeHJ", fzjgsrzeHJ);
                fieldMap.put("fzjggzzeHJ", fzjggzzeHJ);
                fieldMap.put("fzjgzczeHJ", fzjgzczeHJ);
                fieldMap.put("fpblHJ", fpblHJ);
                fieldMap.put("xsdfjmjeHJ", xsdfjmjeHJ);
                fieldMap.put("fpseHJ", fpseHJ);
                dyxxMap.put("tables", tableMap);
            }
            dyxxMap.put("field", fieldMap);
        }
        log.info("企业所得税预缴总分机构分配表组装信息为：{}",JsonUtils.toJson(dyxxMap));
        if (GYCastUtils.notNull(dyxxMap)) {
            //查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxxMap);
        }
        return vo;
    }

    private SbDyVO getBDA0611033Dynsbab(String sbuuid) {
        SbDyVO vo = null;
        //表单模板路径直接写死，毕竟这个一旦确定了基本不会再变了
        final String mblj = "templates/sbdy/BDA0611159/sb222_dynsbab_2019_print.doc";
        //最终返回的打印信息Map
        final Map<String, Object> dyxxMap = new HashMap<String, Object>();
        final Map<String, Object> fieldMap = new HashMap<String, Object>();
        final Map<String, Object> tableMap = new HashMap<String, Object>();
        //首先查询SB_SBB获取表头表位信息，如果这个查不着，都不需要再查询结构化表了
        final Map<String, Object> sbbMap = sbSbbDOMapper.queryBDA0611033Dynsbab(sbuuid);
        if (GYCastUtils.notNull(sbbMap)) {
            final String skssqq = MapUtil.getStr(sbbMap,"nsrxxformSkssqq");
            final String nsrxxformNsrsbh = sbbMap.get("nsrxxformNsrsbh").toString();
            sbbMap.put("nsrsbh", nsrxxformNsrsbh);
            final String skssqqStr = DateUtils.dateToString(DateUtils.toDate(skssqq,"yyyy-MM-dd"), 4).substring(0, 4);
            sbbMap.put("ssnd", skssqqStr);
            final String nsrxxformNsrmc = sbbMap.get("nsrxxformNsrmc").toString();
            sbbMap.put("nsrmc", nsrxxformNsrmc);
            fieldMap.putAll(sbbMap);
            //然后查询sb_sds_jmcz_yjd_dynsbab结构化表，获取结构化表的数据，如果没数据，就不需要给dyxxMap设置值了
            final List<Map<String, Object>> dynsbabList = sbSdsJmczYjdDynsbabMapper.queryBDA0611033Dynsbab(sbuuid);
            if (GYCastUtils.notNull(dynsbabList)) {
                tableMap.put("qyxxGrid", dynsbabList);
                //计算合计值
                double gyjzHJ = 0.0;
                double jsjcHJ = 0.0;
                double dysdHJ = 0.0;
                for (Map<String, Object> map : dynsbabList) {
                    final BigDecimal gyjz = GYCastUtils.isNull(map.get("gyjz")) ? new BigDecimal(0) : (BigDecimal) map.get("gyjz");
                    final BigDecimal jsjc = GYCastUtils.isNull(map.get("jsjc")) ? new BigDecimal(0) : (BigDecimal) map.get("jsjc");
                    final BigDecimal dysd = GYCastUtils.isNull(map.get("dysd")) ? new BigDecimal(0) : (BigDecimal) map.get("dysd");
                    gyjzHJ = new BigDecimal(gyjzHJ).add(gyjz).doubleValue();
                    jsjcHJ = new BigDecimal(jsjcHJ).add(jsjc).doubleValue();
                    dysdHJ = new BigDecimal(dysdHJ).add(dysd).doubleValue();
                    final String gqqdsj = DateUtils.dateToString(DateUtils.toDate(MapUtil.getStr(map,"gqqdsj"),"yyyy-MM-dd"), 4);
                    map.put("gqqdsj", gqqdsj);
                }
                fieldMap.put("gyjzHJ", gyjzHJ);
                fieldMap.put("jsjcHJ", jsjcHJ);
                fieldMap.put("dysdHJ", dysdHJ);
                dyxxMap.put("tables", tableMap);
                dyxxMap.put("field", fieldMap);
            }
        }
        if (GYCastUtils.notNull(dyxxMap)) {
            //查询到打印信息的情况下，才构造打印信息VO
            vo = new SbDyVO();
            vo.setMblj(mblj);
            vo.setDyxx(dyxxMap);
        }
        return vo;
    }

    /**
     * 根据zspmDm和zszmDm对cxsList进行分组合并
     * 合并相同分组的jsyj、ynse、jmse、yjse、ybtse字段
     *
     * @param cxsList 原始财行税列表数据
     * @return 分组合并后的列表
     */
    private List<Map<String, Object>> groupAndMergeCxsList(List<Map<String, Object>> cxsList) {
        if (GYCastUtils.isNull(cxsList)) {
            return new ArrayList<>();
        }

        // 用于存储分组合并结果的Map，key为分组标识，value为合并后的数据
        final Map<String, Map<String, Object>> groupedMap = new HashMap<>();

        for (Map<String, Object> map : cxsList) {
            // 处理税率字段
            final String sl1 = GYCastUtils.isNull(map.get("sl1")) ? "0.0" : GYSbSdsUtils.cast2StrNew(map.get("sl1"));
            map.put("sl1", new BigDecimal(sl1).toPlainString());

            // 构建分组key：zspmDm + zszmDm
            final String zspmDm = String.valueOf(map.get("zspmDm"));
            final String zszmDm = String.valueOf(map.get("zszmDm"));
            final String groupKey = zspmDm + "_" + zszmDm;

            // 获取需要合并的金额字段
            double jsyj = GYCastUtils.isNull(map.get("jsyj")) ? 0.0 : GYCastUtils.cast2Double(map.get("jsyj"));
            double ynse = GYCastUtils.isNull(map.get("ynse")) ? 0.0 : GYCastUtils.cast2Double(map.get("ynse"));
            double jmse = GYCastUtils.isNull(map.get("jmse")) ? 0.0 : GYCastUtils.cast2Double(map.get("jmse"));
            double yjse = GYCastUtils.isNull(map.get("yjse")) ? 0.0 : GYCastUtils.cast2Double(map.get("yjse"));
            double ybtse = GYCastUtils.isNull(map.get("ybtse")) ? 0.0 : GYCastUtils.cast2Double(map.get("ybtse"));

            if (groupedMap.containsKey(groupKey)) {
                // 如果已存在相同分组，则累加金额字段
                Map<String, Object> existingMap = groupedMap.get(groupKey);
                double existingJsyj = GYCastUtils.cast2Double(existingMap.get("jsyj"));
                double existingYnse = GYCastUtils.cast2Double(existingMap.get("ynse"));
                double existingJmse = GYCastUtils.cast2Double(existingMap.get("jmse"));
                double existingYjse = GYCastUtils.cast2Double(existingMap.get("yjse"));
                double existingYbtse = GYCastUtils.cast2Double(existingMap.get("ybtse"));

                existingMap.put("jsyj", jsyj + existingJsyj);
                existingMap.put("ynse", ynse + existingYnse);
                existingMap.put("jmse", jmse + existingJmse);
                existingMap.put("yjse", yjse + existingYjse);
                existingMap.put("ybtse", ybtse + existingYbtse);
            } else {
                // 如果是新分组，则直接添加
                Map<String, Object> newMap = new HashMap<>(map);
                newMap.put("jsyj", jsyj);
                newMap.put("ynse", ynse);
                newMap.put("jmse", jmse);
                newMap.put("yjse", yjse);
                newMap.put("ybtse", ybtse);
                groupedMap.put(groupKey, newMap);
            }
        }

        // 将分组合并结果转换为List
        List<Map<String, Object>> resultList = new ArrayList<>(groupedMap.values());

        // 按照zspmDm进行排序
        Comparator<Map<String, Object>> comparator = (o1, o2) -> {
            String value1 = (String) o1.get("zspmDm");
            String value2 = (String) o2.get("zspmDm");
            return value1.compareTo(value2);
        };
        Collections.sort(resultList, comparator);

        return resultList;
    }

    /**
     * 根据zspmDm和zszmDm对cxsjmxxList进行分组合并
     * 合并相同分组的jmse字段
     *
     * @param cxsjmxxList 原始财行税减免信息列表数据
     * @return 分组合并后的列表
     */
    private List<Map<String, Object>> groupAndMergeCxsjmxxList(List<Map<String, Object>> cxsjmxxList) {
        if (GYCastUtils.isNull(cxsjmxxList)) {
            return new ArrayList<>();
        }

        // 用于存储分组合并结果的Map，key为分组标识，value为合并后的数据
        final Map<String, Map<String, Object>> groupedMap = new HashMap<>();

        for (Map<String, Object> map : cxsjmxxList) {
            // 构建分组key：zspmDm + zszmDm
            final String zspmDm = String.valueOf(map.get("zspmDm"));
            final String zszmDm = String.valueOf(map.get("zszmDm"));
            final String groupKey = zspmDm + "_" + zszmDm;

            // 获取需要合并的减免税额字段
            double jmse = GYCastUtils.isNull(map.get("jmse")) ? 0.0 : GYCastUtils.cast2Double(map.get("jmse"));

            if (groupedMap.containsKey(groupKey)) {
                // 如果已存在相同分组，则累加jmse字段
                Map<String, Object> existingMap = groupedMap.get(groupKey);
                double existingJmse = GYCastUtils.cast2Double(existingMap.get("jmse"));
                existingMap.put("jmse", jmse + existingJmse);
            } else {
                // 如果是新分组，则直接添加
                Map<String, Object> newMap = new HashMap<>(map);
                newMap.put("jmse", jmse);
                groupedMap.put(groupKey, newMap);
            }
        }

        // 将分组合并结果转换为List
        List<Map<String, Object>> resultList = new ArrayList<>(groupedMap.values());

        // 按照zspmDm进行排序
        Comparator<Map<String, Object>> comparator = (o1, o2) -> {
            String value1 = (String) o1.get("zspmDm");
            String value2 = (String) o2.get("zspmDm");
            return value1.compareTo(value2);
        };
        Collections.sort(resultList, comparator);

        return resultList;
    }

}
