package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 固定资产进项税额抵扣情况表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gdzcjxsedkqkbfromVO", propOrder = { "hgjkzzszyjksJxselj", "zzszyfpJxselj", "hgjkzzszyjksJxse", "zzszyfpJxse" })
@XmlSeeAlso({ ZzsybnsrsbGdzcjxsedkqkb.GdzcjxsedkqkbformVO.class })
@Getter
@Setter
public class GdzcjxsedkqkbfromVO {
    /**
     * 海关进口增值税专用缴款书当期申报抵扣的固定资产进项税额累计
     */
    protected BigDecimal hgjkzzszyjksJxselj;

    /**
     * 增值税专用发票当期申报抵扣的固定资产进项税额累计
     */
    protected BigDecimal zzszyfpJxselj;

    /**
     * 海关进口增值税专用缴款书当期申报抵扣的固定资产进项税额
     */
    protected BigDecimal hgjkzzszyjksJxse;

    /**
     * 增值税专用发票当期申报抵扣的固定资产进项税额
     */
    protected BigDecimal zzszyfpJxse;
}