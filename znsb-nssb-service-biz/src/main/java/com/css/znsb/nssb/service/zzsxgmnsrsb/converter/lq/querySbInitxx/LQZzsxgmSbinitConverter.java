package com.css.znsb.nssb.service.zzsxgmnsrsb.converter.lq.querySbInitxx;

import com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhHandlerDTO;
import com.css.znsb.framework.sjjh.service.sjjh.fw.converter.SjjhDataConverter;
import com.css.znsb.nssb.pojo.vo.xgmnsr.lq.init.ZzsxgmInitResVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service(value = "LQZzsxgmSbinitConverter")
public class LQZzsxgmSbinitConverter  implements SjjhDataConverter {

    @Override
    public String convertReq(SjjhHandlerDTO req) {
        log.info("增值税小规模申报初始化待返回转换LQZzsxgmSbinitConverter的convertReq入参req{}:", JsonUtils.toJson(req));
        return JsonUtils.toJson(req.getSjjhDTO().getBwnr());
    }

    @Override
    public CommonResult<Object> convertResp(String req) {
        if (GyUtils.isNull(req)) {
            log.info("查询结果为空");
            return CommonResult.error(GlobalErrorCodeConstants.ERROR.getCode(), "查询结果为空", req);
        }
        log.info("增值税小规模申报乐企申报初始化待返回转换LQZzsxgmSbinitConverter的convertResp入参req{}:",JsonUtils.toJson(req));
        final ZzsxgmInitResVO resVO = JsonUtils.toBean(req, ZzsxgmInitResVO.class);
        log.info("增值税小规模申报乐企申报初始化待返回转换LQZzsxgmSbinitConverter的convertResp出参res{}:",JsonUtils.toJson(resVO));
        final String returncode = resVO.getReturncode();
        log.info("增值税小规模申报乐企申报初始化待返回转换LQZzsxgmSbinitConverter的convertResp出参returncode:"+returncode);
        if ("00".equals(returncode)){
            log.info("增值税小规模申报乐企申报初始化待返回转换LQZzsxgmSbinitConverter的convertResp成功");
            return CommonResult.success(resVO);
        }else{
            log.info("增值税小规模申报乐企申报初始化待返回转换LQZzsxgmSbinitConverter的convertResp失败");
            return CommonResult.error(Integer.parseInt(resVO.getReturncode()),resVO.getReturnmsg());
        }
    }

}
