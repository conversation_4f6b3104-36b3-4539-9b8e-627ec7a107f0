package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《取得进项税额情况》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_qdjxseqkGridlb", propOrder = { "ewbhxh", "pzzlDm1", "je", "jxse", "rzhjhyf", "bz" })
@Getter
@Setter
public class ZzsybnsrsbQdjxseqkGridlb {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 凭证种类代码
     */
    protected String pzzlDm1;

    /**
     * 金额
     */
    protected BigDecimal je;

    /**
     * 进项税额
     */
    protected BigDecimal jxse;

    /**
     * 认证/稽核月份
     */
    protected String rzhjhyf;

    /**
     * 备注
     */
    protected String bz;
}