package com.css.znsb.nssb.service.srhy.impl;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.common.util.znsb.MD5Utils;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.mapper.srhy.ZnsbNssbSrhyBzbMapper;
import com.css.znsb.nssb.mapper.srhy.ZnsbNssbSrhyhzbMapper;
import com.css.znsb.nssb.pojo.domain.srhy.ZnsbNssbSrhyBzbDO;
import com.css.znsb.nssb.pojo.domain.srhy.ZnsbNssbSrhyhzbDO;
import com.css.znsb.nssb.pojo.dto.srhy.ZnsbNssbSrmxbDTO;
import com.css.znsb.nssb.pojo.dto.srhyb.SrhybSjcxReqVO;
import com.css.znsb.nssb.pojo.dto.srhyb.SrhybxxVO;
import com.css.znsb.nssb.pojo.vo.srhy.*;
import com.css.znsb.nssb.service.srhy.ZnsbNssbSrhyBzbService;
import com.css.znsb.nssb.service.srhy.ZnsbNssbSrhyhzbService;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.tzzx.mapper.PzFlMxMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzbMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzmxbMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.css.znsb.nssb.constants.srhy.SrhyConstants.*;

/**
 * <AUTHOR>
 * @description 针对表【znsb_nssb_srhyhzb(收入还原汇总表)】的数据库操作Service实现
 * @createDate 2025-02-13 09:26:29
 */
@Slf4j
@Service
public class ZnsbNssbSrhyhzbServiceImpl extends ServiceImpl<ZnsbNssbSrhyhzbMapper, ZnsbNssbSrhyhzbDO>
        implements ZnsbNssbSrhyhzbService {

    @Resource
    private ZnsbTzzxSrzzmxbMapper znsbTzzxSrzzmxbMapper;
    @Resource
    private ZnsbNssbSrhyhzbMapper znsbNssbSrhyhzbMapper;
    @Resource
    private NsrxxApi nsrxxApi;
    @Resource
    private PzFlMxMapper pzFlMxMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ZnsbTzzxSrzzbMapper znsbTzzxSrzzbMapper;
    @Resource
    private ZnsbNssbSrhyBzbService znsbNssbSrhyBzbService;
    @Resource
    private ZnsbNssbSrhyBzbMapper znsbNssbSrhyBzbMapper;

    @Override
    public void clSrhyhzbSj(String cs) {
        int sszq;
        if (GyUtils.isNull(cs)) {
            sszq = Integer.parseInt(cn.hutool.core.date.DateUtil.format(DateUtils.stringToDate(DateUtil.addMonths(DateUtils.dateToString(new Date()), -1)), DatePattern.SIMPLE_MONTH_PATTERN));
        } else {
            sszq = Integer.parseInt(cs);
        }

        List<ZnsbNssbSrmxbDTO> yspzFz1List = pzFlMxMapper.hqMxxxFz1(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> fz1CxkMap = new HashMap<>();
        if (GyUtils.isNotNull(yspzFz1List)) {
            fz1CxkMap = yspzFz1List.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getNsrsbh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }

        List<ZnsbNssbSrmxbDTO> zyywsrFzList = pzFlMxMapper.hqZyywsrFz(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> zyywsrfzCxkMap = new HashMap<>();
        if (GyUtils.isNotNull(zyywsrFzList)) {
            zyywsrfzCxkMap = zyywsrFzList.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getNsrsbh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }

        List<ZnsbNssbSrmxbDTO> cyhFzList = znsbTzzxSrzzbMapper.queryZyqtAndStxsBySszq(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> cyhFzMap = new HashMap<>();
        if (GyUtils.isNotNull(cyhFzList)) {
            cyhFzMap = cyhFzList.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getDjxh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }

        List<ZnsbNssbSrmxbDTO> srmxbFz1Lsit = znsbTzzxSrzzmxbMapper.hqMxxxFz1(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> fz1Map = new HashMap<>();
        if (GyUtils.isNotNull(srmxbFz1Lsit)) {
            fz1Map = srmxbFz1Lsit.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getDjxh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }
        List<ZnsbNssbSrmxbDTO> srmxbFz2Lsit = znsbTzzxSrzzmxbMapper.hqMxxxFz2(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> fz2Map = new HashMap<>();
        if (GyUtils.isNotNull(srmxbFz2Lsit)) {
            fz2Map = srmxbFz2Lsit.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getDjxh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }

//        List<ZnsbNssbSrmxbDTO> srmxbFz3Lsit = znsbTzzxSrzzmxbMapper.hqMxxxFz3(sszq);
//        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> fz3Map = new HashMap<>();
//        if (GyUtils.isNotNull(srmxbFz3Lsit)) {
//            fz3Map = srmxbFz3Lsit.stream().collect(Collectors.groupingBy(
//                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getDjxh(), ZnsbNssbSrmxbDTO.getLrzx()),
//                    Collectors.toList()
//            ));
//        }

        List<ZnsbNssbSrmxbDTO> srmxbFz4Lsit = znsbTzzxSrzzmxbMapper.hqMxxxFz4(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> fz4Map = new HashMap<>();
        if (GyUtils.isNotNull(srmxbFz4Lsit)) {
            fz4Map = srmxbFz4Lsit.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getDjxh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }

//        List<ZnsbNssbSrmxbDTO> srmxbFz5Lsit = znsbTzzxSrzzmxbMapper.hqMxxxFz5(sszq);
//        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> fz5Map = new HashMap<>();
//        if (GyUtils.isNotNull(srmxbFz5Lsit)) {
//            fz5Map = srmxbFz5Lsit.stream().collect(Collectors.groupingBy(
//                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getDjxh(), ZnsbNssbSrmxbDTO.getLrzx()),
//                    Collectors.toList()
//            ));
//        }

        List<ZnsbNssbSrmxbDTO> srmxbFz6Lsit = znsbTzzxSrzzmxbMapper.hqMxxxFz6(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> fz6Map = new HashMap<>();
        if (GyUtils.isNotNull(srmxbFz6Lsit)) {
            fz6Map = srmxbFz6Lsit.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getDjxh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }

        List<ZnsbNssbSrmxbDTO> srmxbFz7Lsit = znsbTzzxSrzzmxbMapper.hqMxxxFz7(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> fz7Map = new HashMap<>();
        if (GyUtils.isNotNull(srmxbFz7Lsit)) {
            fz7Map = srmxbFz7Lsit.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getDjxh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }

        List<ZnsbNssbSrmxbDTO> srmxb0SlFzLsit = pzFlMxMapper.hqMxxxFzQt0sltz(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> tz0SlFzMap = new HashMap<>();
        if (GyUtils.isNotNull(srmxb0SlFzLsit)) {
            tz0SlFzMap = srmxb0SlFzLsit.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getNsrsbh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }

        List<ZnsbNssbSrmxbDTO> srmxb0SlFz1Lsit = pzFlMxMapper.hqMxxxFzQt0sltz1(sszq);
        Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> tz0SlFz1Map = new HashMap<>();
        if (GyUtils.isNotNull(srmxb0SlFz1Lsit)) {
            tz0SlFz1Map = srmxb0SlFz1Lsit.stream().collect(Collectors.groupingBy(
                    ZnsbNssbSrmxbDTO -> new AbstractMap.SimpleEntry<>(ZnsbNssbSrmxbDTO.getNsrsbh(), ZnsbNssbSrmxbDTO.getLrzx()),
                    Collectors.toList()
            ));
        }


        List<Map<String, String>> qylrzxFz = znsbTzzxSrzzmxbMapper.qylrzxFz(sszq);
        if (GyUtils.isNotNull(qylrzxFz)) {
            Map<String, List<Map<String, String>>> lrzxFzByqy = qylrzxFz.stream().collect(Collectors.groupingBy(a -> String.valueOf(a.get("djxh"))));
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz1Cxk = fz1CxkMap;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalZyywsrFzCxk = zyywsrfzCxkMap;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalcyhFz = cyhFzMap;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz1 = fz1Map;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz2 = fz2Map;
//            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz3 = fz3Map;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz4 = fz4Map;
//            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz5 = fz5Map;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz6 = fz6Map;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz7 = fz7Map;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> final0SlFz = tz0SlFzMap;
            Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> final0Sl1Fz = tz0SlFz1Map;

            lrzxFzByqy.forEach((djxh, values) -> {
                try {
                    clSrmxBydjxh(sszq, finalFz1Cxk, finalZyywsrFzCxk, finalFz1, finalFz2, finalFz4, finalFz6, finalFz7, finalcyhFz, final0SlFz, final0Sl1Fz, djxh, values);
                } catch (Exception e) {
                    log.error("处理收入汇总表报错，djxh为--->{},报错信息--->{}", djxh, e.getMessage());
                    log.error("异常堆栈为--->{}", JsonUtils.toJson(e.getStackTrace()));
                }
            });
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void clSrmxBydjxh(int sszq, Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz1Cxk, Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalZcyywsrFzCxk,
                             Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz1,
                             Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz2, Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz4,
                             Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz6, Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalFz7,
                             Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> finalcyhFz, Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> final0SlFz,
                             Map<Map.Entry<String, String>, List<ZnsbNssbSrmxbDTO>> final0Sl1Fz, String djxh, List<Map<String, String>> values) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        /*List<ZnsbNssbSrhyhzbDO> srhyhzbSrList = new ArrayList<>();
        List<ZnsbNssbSrhyhzbDO> srhyhzbXxList = new ArrayList<>();*/


        final ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
        reqVO.setDjxh(djxh);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVO = nsrxxApi.getNsrxxByDjxh(reqVO);
        String nsrsbh = "";
        String nsrmc = "";
        if (GyUtils.isNotNull(nsrxxVO) && GyUtils.isNotNull(nsrxxVO.getData())) {
            final List<JbxxmxsjVO> qyjbxxList = nsrxxVO.getData().getJbxxmxsj();
            final JbxxmxsjVO qyjbxx = qyjbxxList.get(0);
            nsrsbh = qyjbxx.getNsrsbh();
            nsrmc = qyjbxx.getNsrmc();
        }

        for (Map<String, String> lrzx : values) {
            ZnsbNssbSrhyhzbDO srhyhzbSr = new ZnsbNssbSrhyhzbDO();
            ZnsbNssbSrhyhzbDO srhyhzbXx = new ZnsbNssbSrhyhzbDO();
            final String lrzx1 = lrzx.get("lrzx");
            final String qydmz = lrzx.get("qydmz");
            srhyhzbSr.setDjxh(djxh);
            srhyhzbSr.setNsrsbh(nsrsbh);
            srhyhzbSr.setSsny(sszq);
            srhyhzbSr.setNsrmc(nsrmc);
            srhyhzbSr.setLrzx(lrzx1);
            srhyhzbSr.setUuid(MD5Utils.md5(djxh + lrzx1 + sszq + "0"));
            srhyhzbSr.setLrrq(new Date());
            srhyhzbSr.setYxbz("Y");
            srhyhzbSr.setSjlx("0");

            srhyhzbXx.setDjxh(djxh);
            srhyhzbXx.setNsrsbh(nsrsbh);
            srhyhzbXx.setSsny(sszq);
            srhyhzbXx.setNsrmc(nsrmc);
            srhyhzbXx.setLrzx(lrzx1);
            srhyhzbXx.setUuid(MD5Utils.md5(djxh + lrzx1 + sszq + "1"));
            srhyhzbXx.setLrrq(new Date());
            srhyhzbXx.setYxbz("Y");
            srhyhzbXx.setSjlx("1");


            Map.Entry<String, String> tj = new AbstractMap.SimpleEntry<>(srhyhzbSr.getDjxh(), srhyhzbSr.getLrzx());
            Map.Entry<String, String> cxktj = new AbstractMap.SimpleEntry<>(srhyhzbSr.getNsrsbh(), srhyhzbSr.getLrzx());

            if (GyUtils.isNotNull(finalZcyywsrFzCxk)) {
                List<ZnsbNssbSrmxbDTO> zcyywsrFzList = finalZcyywsrFzCxk.get(cxktj);
                if (GyUtils.isNotNull(zcyywsrFzList)) {
                    for (ZnsbNssbSrmxbDTO zcyywsr : zcyywsrFzList) {
                        final BigDecimal je = zcyywsr.getJe();
                        srhyhzbSr.setZyywsr(srhyhzbSr.getZyywsr().add(je));
                    }
                }
            }

            if (GyUtils.isNotNull(finalFz1)) {
                List<ZnsbNssbSrmxbDTO> fz1List = finalFz1.get(tj);
                if (GyUtils.isNotNull(fz1List)) {
                    for (ZnsbNssbSrmxbDTO fz1 : fz1List) {
                        final String kmdm = fz1.getKmdm();
                        final BigDecimal sl = fz1.getSl().setScale(2);
                        final BigDecimal je = fz1.getJe();


//                        if (KMDM.equals(kmdm.substring(0, 4))) {
//                            srhyhzbSr.setZyywsr(srhyhzbSr.getZyywsr().add(je));
//                        }
                        if (KMDM1.equals(kmdm.substring(0, 6))) {
                            if (SL13.equals(sl)) {
                                srhyhzbSr.setZycpxs(srhyhzbSr.getZycpxs().add(je));
                            } else if (SL6.equals(sl)) {
                                srhyhzbSr.setZycpxs1(srhyhzbSr.getZycpxs1().add(je));
                            } else if (SL0.compareTo(sl) == 0 && "10113101010000359094".equals(fz1.getDjxh())) {
                                srhyhzbSr.setZymssr(srhyhzbSr.getZymssr().add(je));
                            }
                        }
                        if (KMDM2.equals(kmdm.substring(0, 6)) && SL6.equals(sl)) {
                            srhyhzbSr.setZycckd(srhyhzbSr.getZycckd().add(je));
                        }
                        if (KMDM3.contains(kmdm) && SL6.equals(sl)) {
                            srhyhzbSr.setZyspfw(je);
                        }
                        if (KMDM4.equals(kmdm)) {
                            if (SL0.compareTo(sl) == 0) {
                                srhyhzbSr.setZyjwfy(je);
                            } else if (SL13.equals(sl)) {
                                srhyhzbSr.setZzsyszyjwfy(je);
                            }

                        }
                        if (KMDM8.equals(kmdm.substring(0, 4))) {
                            srhyhzbSr.setQtywsr(srhyhzbSr.getQtywsr().add(je));
                        }
                        if (KMDM9.contains(kmdm.substring(0, 6))) {
                            if (SL13.equals(sl)) {
                                srhyhzbSr.setQtgf(je.add(srhyhzbSr.getQtgf()));
                            } else if (SL0.compareTo(sl) == 0 && "10113101010000359094".equals(fz1.getDjxh())) {
                                srhyhzbSr.setQtms(je.add(srhyhzbSr.getQtms()));
                            }

                        }
                        if (KMDM22.contains(kmdm) && SL9.equals(sl)) {
                            srhyhzbSr.setQtzlsr(je.add(srhyhzbSr.getQtzlsr()));
                        }

                        if (KMDM10.equals(kmdm)) {
                            if (SL13.equals(sl)) {
                                srhyhzbSr.setQtsbzj(je);
                            } else if (SL5.equals(sl)) {
                                srhyhzbSr.setQtzlsr1(je);
                            }

                        }
                        if (KMDM11.equals(kmdm) && SL13.equals(sl)) {
                            srhyhzbSr.setQtkmjbyf(je);
                        }

                        if (KMDM23.contains(kmdm) && SL13.equals(sl)) {
                            srhyhzbSr.setQtdswl(je.add(srhyhzbSr.getQtdswl()));
                        }

                        if (KMDM12.equals(kmdm) && SL9.equals(sl)) {
                            srhyhzbSr.setQtdswl1(je);
                        }
                        if (KMDM13.equals(kmdm) && SL6.equals(sl)) {
                            srhyhzbSr.setQtppsq(je);
                        }
                        if (KMDM14.equals(kmdm) && SL6.equals(sl)) {
                            srhyhzbSr.setQtccfw(je);
                        }
                        if (KMDM15.equals(kmdm) && SL6.equals(sl)) {
                            srhyhzbSr.setQto2ofw(je);
                        }
                        if (KMDM16.contains(kmdm) && SL6.equals(sl)) {
                            srhyhzbSr.setQtgffwf(je.add(srhyhzbSr.getQtgffwf()));
                        }
                        if (KMDM17.contains(kmdm) && SL0.compareTo(sl) == 0) {
                            srhyhzbSr.setQtzgfwf(je.add(srhyhzbSr.getQtzgfwf()));
                        }
                        if (KMDM18.equals(kmdm) && SL13.equals(sl)) {
                            srhyhzbSr.setZzsyszyxszkzr(je);
                        }
                        if (KMDM19.contains(kmdm) && SL13.equals(sl)) {
                            srhyhzbSr.setZzsyszyqjfykd(je.add(srhyhzbSr.getZzsyszyqjfykd()));
                        }
                    }
                }
            }

            //2025-08-14 增加其他0税率调整 2115 10113101010000359094
            if (GyUtils.isNotNull(final0SlFz)) {
                List<ZnsbNssbSrmxbDTO> qt0SlFzList = final0SlFz.get(cxktj);
                if (GyUtils.isNotNull(qt0SlFzList)) {
                    for (ZnsbNssbSrmxbDTO qt0Sl : qt0SlFzList) {
                        if (!"91310112MA1GBWCK07".equals(qt0Sl.getNsrsbh())) {
                            srhyhzbSr.setQt0sltz(qt0Sl.getJe());
                        }
                    }
                }
            }
            if (GyUtils.isNotNull(final0Sl1Fz)) {
                List<ZnsbNssbSrmxbDTO> qt0Sl1FzList = final0Sl1Fz.get(cxktj);
                if (GyUtils.isNotNull(qt0Sl1FzList)) {
                    for (ZnsbNssbSrmxbDTO qt0Sl1 : qt0Sl1FzList) {
                        if (!"91310112MA1GBWCK07".equals(qt0Sl1.getNsrsbh())) {
                            srhyhzbSr.setQt0sltz1(qt0Sl1.getJe());
                        }
                    }
                }
            }

            // 2025-04-15 修改这三个数得取值来源 1264 1255 1253
            if (GyUtils.isNotNull(finalFz1Cxk)) {
                List<ZnsbNssbSrmxbDTO> fz1CxkList = finalFz1Cxk.get(cxktj);
                if (GyUtils.isNotNull(fz1CxkList)) {
                    for (ZnsbNssbSrmxbDTO fz1Cxk : fz1CxkList) {
                        final String kmdm = fz1Cxk.getKmdm();
                        final BigDecimal sl = fz1Cxk.getSl().setScale(2);
                        final BigDecimal je = fz1Cxk.getJe();
                        if (KMDM5.equals(kmdm) && SL0.compareTo(sl) == 0) {
                            srhyhzbSr.setZyqhth(je);
//                            srhyhzbSr.setZyywsr(srhyhzbSr.getZyywsr().add(srhyhzbSr.getZyqhth()));
                        }
                        if (KMDM6.equals(kmdm) && SL0.compareTo(sl) == 0) {
                            srhyhzbSr.setZyyjfl(je);
//                            srhyhzbSr.setZyywsr(srhyhzbSr.getZyywsr().add(srhyhzbSr.getZyyjfl()));
                        }
                        if (KMDM10.equals(kmdm) && SL0.compareTo(sl) == 0) {
                            srhyhzbSr.setQtspyqzj(je);
                            srhyhzbSr.setQtywsr(srhyhzbSr.getQtywsr().add(srhyhzbSr.getQtspyqzj()));
                        }
                        if (KMDM7.equals(kmdm) && SL0.compareTo(sl) == 0) {
                            srhyhzbSr.setZyhyjf(je);
//                            srhyhzbSr.setZyywsr(srhyhzbSr.getZyywsr().add(srhyhzbSr.getZyhyjf()));
                        }
                    }
                }
            }

            zhjs(srhyhzbSr, qk1);

            srhyhzbSr.setZysrce(srhyhzbSr.getZyywsr().subtract(srhyhzbSr.getZysrce()));
            srhyhzbSr.setQtsrce(srhyhzbSr.getQtywsr().subtract(srhyhzbSr.getQtsrce()));

            srhyhzbSr.setZzsyszyxssr(srhyhzbSr.getZycpxs());
            srhyhzbSr.setZzsyszycpxs1(srhyhzbSr.getZycpxs1());
            srhyhzbSr.setZzsyszycckd(srhyhzbSr.getZycckd());
            srhyhzbSr.setZzsyszyspfw(srhyhzbSr.getZyspfw());
            srhyhzbSr.setZzsstxssrzlsr(srhyhzbSr.getQtzlsr1());
            srhyhzbSr.setZzsstxssrqtywsr(srhyhzbSr.getZzsstxssrzlsr());
            srhyhzbSr.setZzsqtgf(srhyhzbSr.getQtgf());
            srhyhzbSr.setZzsqtsbzj(srhyhzbSr.getQtsbzj());
            srhyhzbSr.setZzsqtkmjbyf(srhyhzbSr.getQtkmjbyf());
            srhyhzbSr.setZzsqtdswl(srhyhzbSr.getQtdswl());
            srhyhzbSr.setZzsqtzlsr(srhyhzbSr.getQtzlsr());
            srhyhzbSr.setZzsqtdswl1(srhyhzbSr.getQtdswl1());
            srhyhzbSr.setZzsqtppsq(srhyhzbSr.getQtppsq());
            srhyhzbSr.setZzsqtccfw(srhyhzbSr.getQtccfw());
            srhyhzbSr.setZzsqto2ofw(srhyhzbSr.getQto2ofw());
            srhyhzbSr.setZzsqtgf1(srhyhzbSr.getQtgffwf());

            srhyhzbSr.setZzsyszycpxs(srhyhzbSr.getZzsyszyxssr().add(srhyhzbSr.getZzsyszyxszkzr()).add(srhyhzbSr.getZzsyszyqjfykd()));


            if (GyUtils.isNotNull(finalFz2)) {
                List<ZnsbNssbSrmxbDTO> fz2Lsit = finalFz2.get(tj);
                BigDecimal qtsaptz = BigDecimal.ZERO;
                BigDecimal qtsaptz1 = BigDecimal.ZERO;
                BigDecimal qtsaptz2 = BigDecimal.ZERO;
                BigDecimal qtsaptz3 = BigDecimal.ZERO;
                BigDecimal qtsaptz4 = BigDecimal.ZERO;
                if (GyUtils.isNotNull(fz2Lsit)) {
                    for (ZnsbNssbSrmxbDTO fz2 : fz2Lsit) {
                        final BigDecimal sl2 = fz2.getSl().setScale(2);
                        if (SRLX.contains(fz2.getSrlxdm()) && SL13.equals(sl2)) {
                            qtsaptz = qtsaptz.add(fz2.getJe());
                        }
                        if (SL9.equals(sl2)) {
                            qtsaptz1 = qtsaptz1.add(fz2.getJe());
                        }
                        if (SL6.equals(sl2)) {
                            qtsaptz2 = qtsaptz2.add(fz2.getJe());
                        }
                        if (SL5.equals(sl2)) {
                            qtsaptz3 = qtsaptz3.add(fz2.getJe());
                        }
                        if (SL3.equals(sl2)) {
                            qtsaptz4 = qtsaptz4.add(fz2.getJe());
                        }
                    }
                }
                srhyhzbSr.setQtsaptz(qtsaptz);
                srhyhzbSr.setQtsaptz1(qtsaptz1);
                srhyhzbSr.setQtsaptz2(qtsaptz2);
                srhyhzbSr.setQtsaptz3(qtsaptz3);
                srhyhzbSr.setQtsaptz4(qtsaptz4);
            }
            zhjs(srhyhzbSr, qk2);
            BigDecimal xxZzsqtcyh = BigDecimal.ZERO;
            BigDecimal xxZzsqtcyh1 = BigDecimal.ZERO;
            BigDecimal xxZzsqtcyh2 = BigDecimal.ZERO;
            BigDecimal xxZzsqtcyh3 = BigDecimal.ZERO;

            srhyhzbSr.setZzsqtcyh(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsqtcyh()));
            srhyhzbSr.setZzsqtcyh1(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsqtcyh1()));
            srhyhzbSr.setZzsqtcyh2(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsqtcyh2()));
            srhyhzbSr.setZzsqtcyh3(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsqtcyh3()));
            if (GyUtils.isNotNull(finalcyhFz)) {
                List<ZnsbNssbSrmxbDTO> qtCyhFzLsit = finalcyhFz.get(tj);
                if (GyUtils.isNotNull(qtCyhFzLsit)) {
                    List<ZnsbNssbSrmxbDTO> zyjqtList = qtCyhFzLsit.stream().filter(o -> "180".equals(o.getSrlxdm())).collect(Collectors.toList());
                    if (GyUtils.isNotNull(zyjqtList)) {
                        for (ZnsbNssbSrmxbDTO zyjqt : zyjqtList) {
                            final BigDecimal sl = zyjqt.getSl().setScale(2);
                            final BigDecimal je = zyjqt.getJe();
                            final BigDecimal xxse = zyjqt.getXxse();
                            if (SL13.equals(sl)) {
                                srhyhzbSr.setZzsqtcyh(je.add(srhyhzbSr.getZzsqtcyh()));
                                xxZzsqtcyh = xxse;
                            }
                            if (SL9.equals(sl)) {
                                srhyhzbSr.setZzsqtcyh1(je.add(srhyhzbSr.getZzsqtcyh1()));
                                xxZzsqtcyh1 = xxse;
                            }
                            if (SL6.equals(sl)) {
                                srhyhzbSr.setZzsqtcyh2(je.add(srhyhzbSr.getZzsqtcyh2()));
                                xxZzsqtcyh2 = xxse;
                            }
                            if (SL0.compareTo(sl) == 0) {
                                srhyhzbSr.setZzsqtcyh3(je.add(srhyhzbSr.getZzsqtcyh3()));
                                xxZzsqtcyh3 = xxse;
                            }
                        }
                    }
                }
            }

//            if (GyUtils.isNotNull(finalFz3)) {
//                BigDecimal zzsqtcyhTzbf = BigDecimal.ZERO;
//                BigDecimal zzsqtcyh1Tzbf = BigDecimal.ZERO;
//                BigDecimal zzsqtcyh2Tzbf = BigDecimal.ZERO;
//                BigDecimal zzsqtcyh3Tzbf = BigDecimal.ZERO;
//
//                List<ZnsbNssbSrmxbDTO> fz3Lsit = finalFz3.get(tj);
//                if (GyUtils.isNotNull(fz3Lsit)) {
//                    for (ZnsbNssbSrmxbDTO fz3 : fz3Lsit) {
//                        final BigDecimal sl = fz3.getSl().setScale(2);
//                        final BigDecimal je = fz3.getJe();
//                        if (SL13.equals(sl)) {
//                            zzsqtcyhTzbf = je;
//                        }
//                        if (SL9.equals(sl)) {
//                            zzsqtcyh1Tzbf = je;
//                        }
//                        if (SL6.equals(sl)) {
//                            zzsqtcyh2Tzbf = je;
//                        }
//                        if (SL0.compareTo(sl) == 0) {
//                            zzsqtcyh3Tzbf = je;
//                        }
//                    }
//                }
//                srhyhzbSr.setZzsqtcyh(srhyhzbSr.getZzsqtcyh().subtract(zzsqtcyhTzbf));
//                srhyhzbSr.setZzsqtcyh1(srhyhzbSr.getZzsqtcyh1().subtract(zzsqtcyh1Tzbf));
//                srhyhzbSr.setZzsqtcyh2(srhyhzbSr.getZzsqtcyh2().subtract(zzsqtcyh2Tzbf));
//                srhyhzbSr.setZzsqtcyh3(srhyhzbSr.getZzsqtcyh3().subtract(zzsqtcyh3Tzbf));
//            }

            if (GyUtils.isNotNull(finalFz4)) {
                List<ZnsbNssbSrmxbDTO> fz4Lsit = finalFz4.get(tj);
                if (GyUtils.isNotNull(fz4Lsit)) {
                    for (ZnsbNssbSrmxbDTO fz4 : fz4Lsit) {
                        final String kjfp = fz4.getKjfp();
                        final BigDecimal sl = fz4.getSl().setScale(2);
                        final BigDecimal je = fz4.getJe();
                        final String kmdm = fz4.getKmdm();
                        if (KMDM20.equals(kmdm) && KJFP.equals(kjfp) && SL13.equals(sl)) {
                            srhyhzbXx.setZzsstxssrjmslx(je);
                            srhyhzbSr.setZzsstxssrjmslx(je.divide(SL13, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP1.contains(kjfp) && SL13.equals(sl)) {
                            srhyhzbXx.setZzsstxssrzydf(je.add(srhyhzbXx.getZzsstxssrzydf()));
                            srhyhzbSr.setZzsstxssrzydf(srhyhzbXx.getZzsstxssrzydf().divide(SL13, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP2.equals(kjfp) && SL13.equals(sl)) {
                            srhyhzbXx.setZzsstxssrjzsr(je);
                            srhyhzbSr.setZzsstxssrjzsr(je.divide(SL13, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP12.equals(kjfp) && SL13.equals(sl)) {
                            srhyhzbXx.setZzsstxssrdxst(je);
                            srhyhzbSr.setZzsstxssrdxst(je.divide(SL13, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP3.equals(kjfp) && SL9.equals(sl)) {
                            srhyhzbXx.setZzsstxssrsfsr(je);
                            srhyhzbSr.setZzsstxssrsfsr(je.divide(SL9, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP4.equals(kjfp) && SL9.equals(sl)) {
                            srhyhzbXx.setZzsstxssrbdyqzj(je);
                            srhyhzbSr.setZzsstxssrbdyqzj(je.divide(SL9, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP5.equals(kjfp) && SL6.equals(sl)) {
                            srhyhzbXx.setZzsstxssrdklx(je);
                            srhyhzbSr.setZzsstxssrdklx(je.divide(SL6, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP6.equals(kjfp) && SL6.equals(sl)) {
                            srhyhzbXx.setZzsstxssrbblc(je);
                            srhyhzbSr.setZzsstxssrbblc(je.divide(SL6, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP7.equals(kjfp) && SL6.equals(sl)) {
                            srhyhzbXx.setZzsstxssrwyf(je);
                            srhyhzbSr.setZzsstxssrwyf(je.divide(SL6, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP8.equals(kjfp) && SL6.equals(sl)) {
                            srhyhzbXx.setZzsstxssrgssxffh(je);
                            srhyhzbSr.setZzsstxssrgssxffh(je.divide(SL6, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM21.contains(kmdm) && KJFP9.contains(kjfp) && SL5.equals(sl)) {
                            srhyhzbXx.setZzsstxssrbdspyqzj(je.add(srhyhzbXx.getZzsstxssrbdspyqzj()));
                            srhyhzbSr.setZzsstxssrbdspyqzj(srhyhzbXx.getZzsstxssrbdspyqzj().divide(SL5, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM21.contains(kmdm) && KJFP10.equals(kjfp) && SL5.equals(sl)) {
                            srhyhzbXx.setZzsstxssrwdspzj(je.add(srhyhzbXx.getZzsstxssrwdspzj()));
                            srhyhzbSr.setZzsstxssrwdspzj(srhyhzbXx.getZzsstxssrwdspzj().divide(SL5, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM21.contains(kmdm) && KJFP11.equals(kjfp) && SL3.equals(sl)) {
                            srhyhzbXx.setZzsstxssrgdzccl(je.add(srhyhzbXx.getZzsstxssrgdzccl()));
                            srhyhzbSr.setZzsstxssrgdzccl(srhyhzbXx.getZzsstxssrgdzccl().divide(SL3, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP13.equals(kjfp) && SL9.equals(sl)) {
                            srhyhzbXx.setZzsstxssrwyjst(je);
                            srhyhzbSr.setZzsstxssrwyjst(je.divide(SL9, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP13.equals(kjfp) && SL6.equals(sl)) {
                            srhyhzbXx.setZzsstxssrwyjst6(je);
                            srhyhzbSr.setZzsstxssrwyjst6(je.divide(SL6, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP13.equals(kjfp) && SL5.equals(sl)) {
                            srhyhzbXx.setZzsstxssrwyjst5(je);
                            srhyhzbSr.setZzsstxssrwyjst5(je.divide(SL5, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP14.equals(kjfp) && SL13.equals(sl)) {
                            srhyhzbXx.setZzsstxssrlstscj13(je);
                            srhyhzbSr.setZzsstxssrlstscj13(je.divide(SL13, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP14.equals(kjfp) && SL9.equals(sl)) {
                            srhyhzbXx.setZzsstxssrlstscj9(je);
                            srhyhzbSr.setZzsstxssrlstscj9(je.divide(SL9, 2, RoundingMode.HALF_UP));
                        }
                        if (KMDM20.equals(kmdm) && KJFP14.equals(kjfp) && SL6.equals(sl)) {
                            srhyhzbXx.setZzsstxssrlstscj6(je);
                            srhyhzbSr.setZzsstxssrlstscj6(je.divide(SL6, 2, RoundingMode.HALF_UP));
                        }
                    }
                }

            }
            zhjs(srhyhzbSr, qk4);

            BigDecimal xxZzsstxssrcyh = BigDecimal.ZERO;
            BigDecimal xxZzsstxssrcyh1 = BigDecimal.ZERO;
            BigDecimal xxZzsstxssrcyh2 = BigDecimal.ZERO;
            BigDecimal xxZzsstxssrcyh3 = BigDecimal.ZERO;
            BigDecimal xxZzsstxssrcyh4 = BigDecimal.ZERO;
            srhyhzbSr.setZzsstxssrcyh(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsstxssrcyh()));
            srhyhzbSr.setZzsstxssrcyh1(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsstxssrcyh1()));
            srhyhzbSr.setZzsstxssrcyh2(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsstxssrcyh2()));
            srhyhzbSr.setZzsstxssrcyh3(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsstxssrcyh3()));
            srhyhzbSr.setZzsstxssrcyh4(BigDecimal.ZERO.subtract(srhyhzbSr.getZzsstxssrcyh4()));
            if (GyUtils.isNotNull(finalcyhFz)) {
                List<ZnsbNssbSrmxbDTO> stxsCyhFzLsit = finalcyhFz.get(tj);
                if (GyUtils.isNotNull(stxsCyhFzLsit)) {
                    List<ZnsbNssbSrmxbDTO> stxsList = stxsCyhFzLsit.stream().filter(o -> "130".equals(o.getSrlxdm())).collect(Collectors.toList());
                    if (GyUtils.isNotNull(stxsList)) {
                        for (ZnsbNssbSrmxbDTO stxs : stxsList) {
                            final BigDecimal sl = stxs.getSl().setScale(2);
                            final BigDecimal je = stxs.getJe();
                            final BigDecimal xxse = stxs.getXxse();
                            if (SL13.equals(sl)) {
                                srhyhzbSr.setZzsstxssrcyh(je.add(srhyhzbSr.getZzsstxssrcyh()));
                                xxZzsstxssrcyh = xxse;
                            }
                            if (SL9.equals(sl)) {
                                srhyhzbSr.setZzsstxssrcyh1(je.add(srhyhzbSr.getZzsstxssrcyh1()));
                                xxZzsstxssrcyh1 = xxse;
                            }
                            if (SL6.equals(sl)) {
                                srhyhzbSr.setZzsstxssrcyh2(je.add(srhyhzbSr.getZzsstxssrcyh2()));
                                xxZzsstxssrcyh2 = xxse;
                            }
                            if (SL5.equals(sl)) {
                                srhyhzbSr.setZzsstxssrcyh3(je.add(srhyhzbSr.getZzsstxssrcyh3()));
                                xxZzsstxssrcyh3 = xxse;
                            }
                            if (SL3.equals(sl)) {
                                srhyhzbSr.setZzsstxssrcyh4(je.add(srhyhzbSr.getZzsstxssrcyh4()));
                                xxZzsstxssrcyh4 = xxse;
                            }
                        }
                    }
                }

            }

//            if (GyUtils.isNotNull(finalFz5)) {
//                List<ZnsbNssbSrmxbDTO> fz5Lsit = finalFz5.get(tj);
//                if (GyUtils.isNotNull(fz5Lsit)) {
//                    for (ZnsbNssbSrmxbDTO fz5 : fz5Lsit) {
//                        final BigDecimal sl = fz5.getSl().setScale(2);
//                        final BigDecimal je = fz5.getJe();
//                        if (SL13.equals(sl)) {
//                            srhyhzbSr.setZzsstxssrcyh(srhyhzbSr.getZzsstxssrcyh().subtract(je.divide(SL13, 2, RoundingMode.HALF_UP)));
//                        }
//                        if (SL9.equals(sl)) {
//                            srhyhzbSr.setZzsstxssrcyh1(srhyhzbSr.getZzsstxssrcyh1().subtract(je.divide(SL9, 2, RoundingMode.HALF_UP)));
//                        }
//                        if (SL6.equals(sl)) {
//                            srhyhzbSr.setZzsstxssrcyh2(srhyhzbSr.getZzsstxssrcyh2().subtract(je.divide(SL6, 2, RoundingMode.HALF_UP)));
//                        }
//                        if (SL5.equals(sl)) {
//                            srhyhzbSr.setZzsstxssrcyh3(srhyhzbSr.getZzsstxssrcyh3().subtract(je.divide(SL5, 2, RoundingMode.HALF_UP)));
//                        }
//                        if (SL3.equals(sl)) {
//                            srhyhzbSr.setZzsstxssrcyh4(srhyhzbSr.getZzsstxssrcyh4().subtract(je.divide(SL3, 2, RoundingMode.HALF_UP)));
//                        }
//                    }
//                }
//
//            }

            zhjs(srhyhzbSr, qk6);
            if (GyUtils.isNotNull(finalFz6)) {
                List<ZnsbNssbSrmxbDTO> fz6List = finalFz6.get(tj);
                BigDecimal lqtzpz = BigDecimal.ZERO;
                BigDecimal lqtzpz1 = BigDecimal.ZERO;
                BigDecimal lqtzpz2 = BigDecimal.ZERO;
                BigDecimal lqtzpz3 = BigDecimal.ZERO;
                BigDecimal lqtzpz4 = BigDecimal.ZERO;
                BigDecimal lqtzpz5 = BigDecimal.ZERO;
                if (GyUtils.isNotNull(fz6List)) {
                    for (ZnsbNssbSrmxbDTO fz6 : fz6List) {
                        final BigDecimal sl = fz6.getSl().setScale(2);
                        final String srlx = fz6.getSrlxdm();
                        final BigDecimal je = fz6.getJe();
                        if (SL13.equals(sl)) {
                            if (SRLX.contains(srlx)) {
                                lqtzpz = lqtzpz.add(je);
                            }
                            if (SRLX1.equals(srlx)) {
                                lqtzpz = lqtzpz.add(je.divide(SL13, 2, RoundingMode.HALF_UP));
                            }
                        }
                        if (SL9.equals(sl)) {
                            if (SRLX.contains(srlx)) {
                                lqtzpz1 = lqtzpz1.add(je);
                            }
                            if (SRLX1.equals(srlx)) {
                                lqtzpz1 = lqtzpz1.add(je.divide(SL9, 2, RoundingMode.HALF_UP));
                            }
                        }
                        if (SL6.equals(sl)) {
                            if (SRLX.contains(srlx)) {
                                lqtzpz2 = lqtzpz2.add(je);
                            }
                            if (SRLX1.equals(srlx)) {
                                lqtzpz2 = lqtzpz2.add(je.divide(SL6, 2, RoundingMode.HALF_UP));
                            }
                        }
                        if (SL5.equals(sl)) {
                            if (SRLX.contains(srlx)) {
                                lqtzpz3 = lqtzpz3.add(je);
                            }
                            if (SRLX1.equals(srlx)) {
                                lqtzpz3 = lqtzpz3.add(je.divide(SL5, 2, RoundingMode.HALF_UP));
                            }
                        }
                        if (SL3.equals(sl)) {
                            if (SRLX.contains(srlx)) {
                                lqtzpz4 = lqtzpz4.add(je);
                            }
                            if (SRLX1.equals(srlx)) {
                                lqtzpz4 = lqtzpz4.add(je.divide(SL3, 2, RoundingMode.HALF_UP));
                            }
                        }
                        if (SL1.equals(sl)) {
                            if (SRLX.contains(srlx)) {
                                lqtzpz5 = lqtzpz5.add(je);
                            }
                            if (SRLX1.equals(srlx)) {
                                lqtzpz5 = lqtzpz5.add(je.divide(SL1, 2, RoundingMode.HALF_UP));
                            }
                        }
                    }
                }

                srhyhzbSr.setZzsstxssrlqtzpz(lqtzpz);
                srhyhzbSr.setZzsstxssrlqtzpz1(lqtzpz1);
                srhyhzbSr.setZzsstxssrlqtzpz2(lqtzpz2);
                srhyhzbSr.setZzsstxssrlqtzpz3(lqtzpz3);
                srhyhzbSr.setZzsstxssrlqtzpz4(lqtzpz4);
                srhyhzbSr.setZzsstxssrlqtzpz5(lqtzpz5);

            }

            if (GyUtils.isNotNull(finalFz7)) {
                List<ZnsbNssbSrmxbDTO> fz7List = finalFz7.get(tj);
                if (GyUtils.isNotNull(fz7List)) {
                    for (ZnsbNssbSrmxbDTO fz7 : fz7List) {
                        final BigDecimal sl = fz7.getSl().setScale(2);
                        final BigDecimal je = fz7.getJe();
                        if (SL13.equals(sl)) {
                            srhyhzbSr.setZzsstxssrlqwxpz(je);
                        }
                        if (SL9.equals(sl)) {
                            srhyhzbSr.setZzsstxssrlqwxpz1(je);
                        }
                        if (SL6.equals(sl)) {
                            srhyhzbSr.setZzsstxssrlqwxpz2(je);
                        }
                        if (SL5.equals(sl)) {
                            srhyhzbSr.setZzsstxssrlqwxpz3(je);
                        }
                        if (SL3.equals(sl)) {
                            srhyhzbSr.setZzsstxssrlqwxpz4(je);
                        }
                        if (SL1.equals(sl)) {
                            srhyhzbSr.setZzsstxssrlqwxpz5(je);
                        }
                    }
                }

            }
            zhjs(srhyhzbSr, qk7);
            srhyhzbXx.setQtdswl(srhyhzbSr.getQtdswl().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setQtdswl1(srhyhzbSr.getQtdswl1().multiply(SL9).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setQtsaptz(srhyhzbSr.getQtsaptz().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsyszycpxs(srhyhzbSr.getZzsyszycpxs().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsyszyxssr(srhyhzbSr.getZzsyszyxssr().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsyszyxszkzr(srhyhzbSr.getZzsyszyxszkzr().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsyszyqjfykd(srhyhzbSr.getZzsyszyqjfykd().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsyszyjwfy(srhyhzbSr.getZzsyszyjwfy().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsyszycpxs1(srhyhzbSr.getZzsyszycpxs1().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsyszycckd(srhyhzbSr.getZzsyszycckd().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsyszyspfw(srhyhzbSr.getZzsyszyspfw().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtgf(srhyhzbSr.getZzsqtgf().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtsbzj(srhyhzbSr.getZzsqtsbzj().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtkmjbyf(srhyhzbSr.getZzsqtkmjbyf().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtdswl(srhyhzbSr.getZzsqtdswl().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtzlsr(srhyhzbSr.getZzsqtzlsr().multiply(SL9).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtdswl1(srhyhzbSr.getZzsqtdswl1().multiply(SL9).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtppsq(srhyhzbSr.getZzsqtppsq().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtccfw(srhyhzbSr.getZzsqtccfw().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqto2ofw(srhyhzbSr.getZzsqto2ofw().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsqtgf1(srhyhzbSr.getZzsqtgf1().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
//            srhyhzbXx.setZzsqtcyh(srhyhzbSr.getZzsqtcyh().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
//            srhyhzbXx.setZzsqtcyh1(srhyhzbSr.getZzsqtcyh1().multiply(SL9).setScale(2, RoundingMode.HALF_UP));
//            srhyhzbXx.setZzsqtcyh2(srhyhzbSr.getZzsqtcyh2().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
//            srhyhzbXx.setZzsqtcyh3(BigDecimal.ZERO);
//
//            srhyhzbXx.setZzsstxssrcyh(srhyhzbSr.getZzsstxssrcyh().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
//            srhyhzbXx.setZzsstxssrcyh1(srhyhzbSr.getZzsstxssrcyh1().multiply(SL9).setScale(2, RoundingMode.HALF_UP));
//            srhyhzbXx.setZzsstxssrcyh2(srhyhzbSr.getZzsstxssrcyh2().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
//            srhyhzbXx.setZzsstxssrcyh3(srhyhzbSr.getZzsstxssrcyh3().multiply(SL5).setScale(2, RoundingMode.HALF_UP));
//            srhyhzbXx.setZzsstxssrcyh4(srhyhzbSr.getZzsstxssrcyh4().multiply(SL3).setScale(2, RoundingMode.HALF_UP));

            srhyhzbXx.setZzsstxssrzlsr(srhyhzbSr.getZzsstxssrzlsr().multiply(SL5).setScale(2, RoundingMode.HALF_UP));

            srhyhzbXx.setZzsstxssrlqtzpz(srhyhzbSr.getZzsstxssrlqtzpz().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqtzpz1(srhyhzbSr.getZzsstxssrlqtzpz1().multiply(SL9).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqtzpz2(srhyhzbSr.getZzsstxssrlqtzpz2().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqtzpz3(srhyhzbSr.getZzsstxssrlqtzpz3().multiply(SL5).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqtzpz4(srhyhzbSr.getZzsstxssrlqtzpz4().multiply(SL3).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqtzpz5(srhyhzbSr.getZzsstxssrlqtzpz5().multiply(SL1).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqwxpz(srhyhzbSr.getZzsstxssrlqwxpz().multiply(SL13).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqwxpz1(srhyhzbSr.getZzsstxssrlqwxpz1().multiply(SL9).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqwxpz2(srhyhzbSr.getZzsstxssrlqwxpz2().multiply(SL6).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqwxpz3(srhyhzbSr.getZzsstxssrlqwxpz3().multiply(SL5).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqwxpz4(srhyhzbSr.getZzsstxssrlqwxpz4().multiply(SL3).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrlqwxpz5(srhyhzbSr.getZzsstxssrlqwxpz5().multiply(SL1).setScale(2, RoundingMode.HALF_UP));
            srhyhzbXx.setZzsstxssrqtywsr(srhyhzbXx.getZzsstxssrzlsr());
            zhXxjs(srhyhzbXx, qk11);
            srhyhzbXx.setZzsqtcyh(xxZzsqtcyh.subtract(srhyhzbXx.getZzsqtcyh()));
            srhyhzbXx.setZzsqtcyh1(xxZzsqtcyh1.subtract(srhyhzbXx.getZzsqtcyh1()));
            srhyhzbXx.setZzsqtcyh2(xxZzsqtcyh2.subtract(srhyhzbXx.getZzsqtcyh2()));
            srhyhzbXx.setZzsqtcyh3(xxZzsqtcyh3.subtract(srhyhzbXx.getZzsqtcyh3()));
            zhXxjs(srhyhzbXx, qk12);
            srhyhzbXx.setZzsstxssrcyh(xxZzsstxssrcyh.subtract(srhyhzbXx.getZzsstxssrcyh()));
            srhyhzbXx.setZzsstxssrcyh1(xxZzsstxssrcyh1.subtract(srhyhzbXx.getZzsstxssrcyh1()));
            srhyhzbXx.setZzsstxssrcyh2(xxZzsstxssrcyh2.subtract(srhyhzbXx.getZzsstxssrcyh2()));
            srhyhzbXx.setZzsstxssrcyh3(xxZzsstxssrcyh3.subtract(srhyhzbXx.getZzsstxssrcyh3()));
            srhyhzbXx.setZzsstxssrcyh4(xxZzsstxssrcyh4.subtract(srhyhzbXx.getZzsstxssrcyh4()));
            zhXxjs(srhyhzbXx, qk10);

            List<ZnsbNssbSrhyBzbDO> bzbDOList = JsonUtils.toList(JSONSTR, ZnsbNssbSrhyBzbDO.class);
            Date date = new Date();
            for (ZnsbNssbSrhyBzbDO bzbDO : bzbDOList) {
                bzbDO.setUuid(MD5Utils.md5(djxh + lrzx1 + sszq + bzbDO.getYwmc()));
                bzbDO.setDjxh(djxh);
                bzbDO.setNsrsbh(srhyhzbSr.getNsrsbh());
                bzbDO.setNsrmc(srhyhzbSr.getNsrmc());
                bzbDO.setQydmz(qydmz);
                bzbDO.setLrzx(srhyhzbSr.getLrzx());
                bzbDO.setSsny(srhyhzbSr.getSsny());

                bzbDO.setJe(new BigDecimal(BeanUtils.getProperty(srhyhzbSr, bzbDO.getYwmc())));
                bzbDO.setSe(new BigDecimal(BeanUtils.getProperty(srhyhzbXx, bzbDO.getYwmc())));
                bzbDO.setLrrq(date);
                bzbDO.setLrrsfid("SJJG");
            }
            znsbNssbSrhyBzbService.saveOrUpdateBatch(bzbDOList);
//            srhyhzbSrList.add(srhyhzbSr);
//            srhyhzbXxList.add(srhyhzbXx);
        }

//        this.saveOrUpdateBatch(srhyhzbSrList);
//        this.saveOrUpdateBatch(srhyhzbXxList);
    }

    private void zhjs(ZnsbNssbSrhyhzbDO srhyhzbSr, LinkedHashMap<String, List<String>> qk) {
        qk.keySet().stream()
                .map(s -> {
                    BigDecimal hj = BigDecimal.ZERO;
                    for (String prop : qk.get(s)) {

                        BigDecimal property = BigDecimal.ZERO;
                        try {
                            property = new BigDecimal(BeanUtils.getProperty(srhyhzbSr, prop));
                        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                            e.printStackTrace();
                        }
                        hj = hj.add(property);
                    }
                    try {
                        BeanUtils.setProperty(srhyhzbSr, s, hj);
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        e.printStackTrace();
                        log.info("报错--->{}", e.getMessage());
                    }
                    return hj;
                }).collect(Collectors.toList());
    }

    private void zhXxjs(ZnsbNssbSrhyhzbDO srhyhzbXx, LinkedHashMap<String, List<String>> qk) {
        qk.keySet().stream()
                .map(s -> {
                    BigDecimal hj = BigDecimal.ZERO;
                    for (String prop : qk.get(s)) {

                        BigDecimal property = BigDecimal.ZERO;
                        try {
                            property = new BigDecimal(BeanUtils.getProperty(srhyhzbXx, prop));
                        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                            e.printStackTrace();
                            log.error("报错--->{}", e.getMessage());
                        }
                        hj = hj.add(property);
                    }
                    try {
                        BeanUtils.setProperty(srhyhzbXx, s, hj);
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        e.printStackTrace();
                        log.error("报错--->{}", e.getMessage());
                    }
                    return hj;
                }).collect(Collectors.toList());
    }

    @Override
    public ZnsbNssbSrhybResVO hqSrxx(ZnsbNssbSrhybReqVO reqVO) {
        ZnsbNssbSrhybResVO resVO = new ZnsbNssbSrhybResVO();
        List<ZnsbNssbSrhyhzbVO> hjList = new ArrayList<>();
        Map<String, List<ZnsbNssbSrhyhzbVO>> srhzmxMap = new HashMap<>();
        final String nsrmc = reqVO.getNsrmc();
        final String djxh = reqVO.getDjxh();
        final int ssny = Integer.parseInt(reqVO.getSsny().replace("-", ""));
        List<Map<String, Object>> hzxxList = znsbNssbSrhyBzbMapper.queryHzxx(djxh, ssny);
        if (GyUtils.isNotNull(hzxxList)) {
            ZnsbNssbSrhyhzbVO hzxxJeVo = new ZnsbNssbSrhyhzbVO();
            hzxxJeVo.setSjlx("0");
            ZnsbNssbSrhyhzbVO hzxxSeVo = new ZnsbNssbSrhyhzbVO();
            hzxxSeVo.setSjlx("1");
            for (Map<String, Object> hzxx : hzxxList) {

                try {
                    BeanUtils.setProperty(hzxxJeVo, String.valueOf(hzxx.get("ywmc")), hzxx.get("je"));
                    BeanUtils.setProperty(hzxxSeVo, String.valueOf(hzxx.get("ywmc")), hzxx.get("se"));
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }

            }
            hjList.add(hzxxJeVo);
            hjList.add(hzxxSeVo);
        }

//        List<ZnsbNssbSrhyhzbDO> hzxxList = znsbNssbSrhyhzbMapper.queryHzxx(djxh, ssny);
//        hjList = com.css.znsb.framework.common.util.object.BeanUtils.toBean(hzxxList, ZnsbNssbSrhyhzbVO.class);
//        List<ZnsbNssbSrhyhzbDO> mxxxList = znsbNssbSrhyhzbMapper.queryMxxx(djxh, ssny);
        List<ZnsbNssbSrhyhzbVO> mxxx = new ArrayList<>();
        List<Map<String, Object>> mxxxListMap = znsbNssbSrhyBzbMapper.queryMxxx(djxh, ssny);
        if (GyUtils.isNotNull(mxxxListMap)) {
            Map<String, List<Map<String, Object>>> mxxxGroup = mxxxListMap.stream().collect(Collectors.groupingBy(o -> String.valueOf(o.get("lrzx"))));
            mxxxGroup.forEach((key, values) -> {
                List<ZnsbNssbSrhyBzbDO> bzbDOList = znsbNssbSrhyBzbMapper.queryAll(djxh, ssny);
                final Date lrrq = bzbDOList.get(0).getLrrq();
                final String lrzx = key;
                ZnsbNssbSrhyhzbVO mxxxJe = new ZnsbNssbSrhyhzbVO();
                mxxxJe.setLrzx(lrzx);
                mxxxJe.setSjlx("0");
                mxxxJe.setLrrq(lrrq);
                ZnsbNssbSrhyhzbVO mxxxSe = new ZnsbNssbSrhyhzbVO();
                mxxxSe.setLrzx(lrzx);
                mxxxSe.setSjlx("1");
                mxxxSe.setLrrq(lrrq);
                for (Map<String, Object> mxxxMap : values) {
                    try {
                        BeanUtils.setProperty(mxxxJe, String.valueOf(mxxxMap.get("ywmc")), mxxxMap.get("je"));
                        BeanUtils.setProperty(mxxxSe, String.valueOf(mxxxMap.get("ywmc")), mxxxMap.get("se"));
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
                mxxx.add(mxxxJe);
                mxxx.add(mxxxSe);
            });
//            List<ZnsbNssbSrhyhzbVO> mxxx = com.css.znsb.framework.common.util.object.BeanUtils.toBean(mxxxList, ZnsbNssbSrhyhzbVO.class);
            List<Map.Entry<String, List<ZnsbNssbSrhyhzbVO>>> entryList = new ArrayList<>(mxxx.stream().collect(Collectors.groupingBy(ZnsbNssbSrhyhzbVO::getLrzx)).entrySet());
            entryList.sort(Map.Entry.comparingByKey());
            srhzmxMap = entryList.stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new
                    ));
        }
        resVO.setDjxh(djxh);
        resVO.setNsrmc(nsrmc);
        resVO.setHjList(hjList);
        resVO.setSrhzmxMap(srhzmxMap);
        return resVO;
    }

    @Async
    @Override
    public void tqsj(ZnsbNssbSrhybTqsjReqVO reqVO, String key) {
        try {
            ZnsbNssbSrhybTqsjResVO resVO = new ZnsbNssbSrhybTqsjResVO();
            final String ssny = reqVO.getSsny().replace("-", "");
            this.clSrhyhzbSj(ssny);
        } catch (Exception e) {
            log.error("收入还原汇总表提起数据异常报错信息为---->{}", e.getMessage());
        } finally {
            stringRedisTemplate.delete(key);
        }

    }

    @Override
    public List<SrhybxxVO> srhybxxDwcx(SrhybSjcxReqVO reqVO) {
        List<SrhybxxVO> srhybxxVOList = new ArrayList<>();
        final String nsrsbh = reqVO.getNsrsbh();
        final String qydm = reqVO.getQydm();
        final String ssny = reqVO.getSsny();
        List<ZnsbNssbSrhyBzbDO> srhyBzbDOList = znsbNssbSrhyBzbMapper.querySrhybxx(nsrsbh, qydm, Integer.valueOf(ssny));
        if (GyUtils.isNotNull(srhyBzbDOList)) {
            srhybxxVOList = srhyBzbDOList.stream().map(o -> {
                SrhybxxVO srhybxxVO = new SrhybxxVO();
                srhybxxVO.setSsny(String.valueOf(o.getSsny()));
                srhybxxVO.setQydm(o.getQydmz());
                srhybxxVO.setNsrsbh(o.getNsrsbh());
                srhybxxVO.setQymc(o.getNsrmc());
                srhybxxVO.setLrzx(o.getLrzx());
                srhybxxVO.setXzbm(o.getXzbm());
                srhybxxVO.setXmbm(o.getXmbm());
                srhybxxVO.setXmmc(o.getHmc());
                srhybxxVO.setSl(o.getSlmc());
                DecimalFormat df = new DecimalFormat("0.00"); // 去掉 #,## 如果不需要千分位
                df.setRoundingMode(RoundingMode.HALF_UP);
                srhybxxVO.setSrhjs(df.format(o.getJe()));
                srhybxxVO.setXxhjs(df.format(o.getSe()));
                return srhybxxVO;
            }).collect(Collectors.toList());
        }
        return srhybxxVOList;
    }
}




