package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 申报表受理信息VO扩展3
 *
 * <p>sbbslxxkz3VO complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="sbbslxxkz3VO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="jbr" type="{http://www.chinatax.gov.cn/dataspec/}jbr"/>
 *         &lt;element name="cwfzr" type="{http://www.chinatax.gov.cn/dataspec/}cwfzr"/>
 *         &lt;element name="lxdh" type="{http://www.chinatax.gov.cn/dataspec/}swdlrlxdh"/>
 *         &lt;element name="swdlrmc" type="{http://www.chinatax.gov.cn/dataspec/}swdlrmc"/>
 *         &lt;element name="swdlrdz" type="{http://www.chinatax.gov.cn/dataspec/}swdlrdz"/>
 *         &lt;element name="sqr" type="{http://www.chinatax.gov.cn/dataspec/}bsy"/>
 *         &lt;element name="blrysfzjlxDm" type="{http://www.chinatax.gov.cn/dataspec/}blrysfzjlxDm"/>
 *         &lt;element name="blrysfzjhm" type="{http://www.chinatax.gov.cn/dataspec/}blrysfzjhm"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbslxxkz3VO", propOrder = { "jbr", "cwfzr", "lxdh", "swdlrmc", "swdlrdz", "sqr", "blrysfzjlxDm", "blrysfzjhm" })
public class Sbbslxxkz3VO {
    /**
     * 经办人
     */
    @XmlElement(nillable = true, required = true)
    protected String jbr;

    /**
     * 财务负责人
     */
    @XmlElement(nillable = true, required = true)
    protected String cwfzr;

    /**
     * 税务代理人联系电话
     */
    @XmlElement(nillable = true, required = true)
    protected String lxdh;

    /**
     * 税务代理人名称
     */
    @XmlElement(nillable = true, required = true)
    protected String swdlrmc;

    /**
     * 税务代理人地址
     */
    @XmlElement(nillable = true, required = true)
    protected String swdlrdz;

    /**
     * 授权人
     */
    @XmlElement(nillable = true, required = true)
    protected String sqr;

    /**
     * 办理人身份证件类型代码
     */
    @XmlElement(nillable = true, required = true)
    protected String blrysfzjlxDm;

    /**
     * 办理人身份证件号码
     */
    @XmlElement(nillable = true, required = true)
    protected String blrysfzjhm;

    /**
     * 获取jbr属性的值。
     * <p>
     * 经办人
     */
    public String getJbr() {
        return jbr;
    }

    /**
     * 设置jbr属性的值。
     */
    public void setJbr(String value) {
        this.jbr = value;
    }

    /**
     * 获取cwfzr属性的值。
     * <p>
     * 财务负责人
     */
    public String getCwfzr() {
        return cwfzr;
    }

    /**
     * 设置cwfzr属性的值。
     */
    public void setCwfzr(String value) {
        this.cwfzr = value;
    }

    /**
     * 获取lxdh属性的值。
     * <p>
     * 税务代理人联系电话
     */
    public String getLxdh() {
        return lxdh;
    }

    /**
     * 设置lxdh属性的值。
     */
    public void setLxdh(String value) {
        this.lxdh = value;
    }

    /**
     * 获取swdlrmc属性的值。
     * <p>
     * 税务代理人名称
     */
    public String getSwdlrmc() {
        return swdlrmc;
    }

    /**
     * 设置swdlrmc属性的值。
     */
    public void setSwdlrmc(String value) {
        this.swdlrmc = value;
    }

    /**
     * 获取swdlrdz属性的值。
     * <p>
     * 税务代理人地址
     */
    public String getSwdlrdz() {
        return swdlrdz;
    }

    /**
     * 设置swdlrdz属性的值。
     */
    public void setSwdlrdz(String value) {
        this.swdlrdz = value;
    }

    /**
     * 获取sqr属性的值。
     * <p>
     * 授权人
     */
    public String getSqr() {
        return sqr;
    }

    /**
     * 设置sqr属性的值。
     */
    public void setSqr(String value) {
        this.sqr = value;
    }

    /**
     * 获取blrysfzjlxDm属性的值。
     * <p>
     * 办理人身份证件类型代码
     */
    public String getBlrysfzjlxDm() {
        return blrysfzjlxDm;
    }

    /**
     * 设置blrysfzjlxDm属性的值。
     */
    public void setBlrysfzjlxDm(String value) {
        this.blrysfzjlxDm = value;
    }

    /**
     * 获取blrysfzjhm属性的值。
     * <p>
     * 办理人身份证件号码
     */
    public String getBlrysfzjhm() {
        return blrysfzjhm;
    }

    /**
     * 设置blrysfzjhm属性的值。
     */
    public void setBlrysfzjhm(String value) {
        this.blrysfzjhm = value;
    }
}