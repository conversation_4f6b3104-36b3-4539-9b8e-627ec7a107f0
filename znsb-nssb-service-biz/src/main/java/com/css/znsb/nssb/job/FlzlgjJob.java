package com.css.znsb.nssb.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.css.znsb.cxtj.api.SimpleQueryApi;
import com.css.znsb.cxtj.pojo.SimpleQueryReq;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.nssb.mapper.gy.ZnsbNssbCxfbzlscqdMapper;
import com.css.znsb.nssb.pojo.dto.gy.flzl.FhsjDTO;
import com.css.znsb.nssb.pojo.dto.gy.flzl.LqFbzlscCxRequestDTO;
import com.css.znsb.nssb.pojo.dto.gy.flzl.LqFbzlscCxResponseDTO;
import com.css.znsb.nssb.pojo.dto.gy.flzl.ZnsbNssbCxfbzlscqdDTO;
import com.css.znsb.nssb.service.gy.ZnsbNssbCxfbzlscqdService;
import com.css.znsb.nssb.utils.ControllerUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 附列资料归集job
 */
@Slf4j
@Component
public class FlzlgjJob {
    @Resource
    private SimpleQueryApi queryApi;
    @Resource
    private SjjhService sjjhService;
    @Resource
    private ZnsbNssbCxfbzlscqdService znsbNssbCxfbzlscqdService;
    @Resource
    private ZnsbNssbCxfbzlscqdMapper znsbNssbCxfbzlscqdMapper;
    
    @XxlJob(value = "flzlgjJob")
    public void execute() {
        //查询全量企业信息进行归集  企业端归集应申报统计数据
        final SimpleQueryReq simpleQueryReq = new SimpleQueryReq();
        simpleQueryReq.setPath("/sb/sbrw//qydgjysbtjsj");
        final List<Map<String, Object>> resultList = queryApi.queryList(simpleQueryReq);
        log.info("magicapi获取的纳税人基本信息：", resultList);
        if (GyUtils.isNotNull(resultList)) {

            final String nsrsbh = (String) resultList.get(0).get("nsrsbh");
            final String djxh = (String) resultList.get(0).get("djxh");
            final String xzqhszDm = (String) resultList.get(0).get("xzqhszDm");


            LambdaQueryWrapper<ZnsbNssbCxfbzlscqdDTO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ZnsbNssbCxfbzlscqdDTO::getSwsxDm, "SXA061001034");
            znsbNssbCxfbzlscqdMapper.delete(wrapper);

            final LqFbzlscCxRequestDTO lqFbzlscCxRequestDTO = new LqFbzlscCxRequestDTO();
            //增值税一般人申报
            lqFbzlscCxRequestDTO.setSwsxDm("SXA061001034");

            final String ywbw = JsonUtils.toJson(lqFbzlscCxRequestDTO);
            final SjjhDTO sjjhDTO = new SjjhDTO();
            sjjhDTO.setSjjhlxDm("FLZL000001");
            sjjhDTO.setYwbm("BDA0611155");
            sjjhDTO.setDjxh(djxh);
            sjjhDTO.setNsrsbh(nsrsbh);
            sjjhDTO.setXzqhszDm(xzqhszDm);
            sjjhDTO.setBwnr(ywbw);
            log.info("乐企查询附报资料上传清单接口报文入参{}", sjjhDTO);
            final CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);

            LqFbzlscCxResponseDTO lqFbzlscCxResponseDTO = new LqFbzlscCxResponseDTO();

            if (GyUtils.isNotNull(result) && GyUtils.isNotNull(result.getData())) {
                log.info("乐企返回查询附报资料上传清单接口报文{}", result);
                lqFbzlscCxResponseDTO = JsonUtils.toBean((String) result.getData(), LqFbzlscCxResponseDTO.class);
            }
            if ("00".equals(lqFbzlscCxResponseDTO.getReturncode())) {
                List<FhsjDTO> fhsj = lqFbzlscCxResponseDTO.getFhsj();
                List<ZnsbNssbCxfbzlscqdDTO> list = new ArrayList<>();

                fhsj.forEach(t -> {
                    ZnsbNssbCxfbzlscqdDTO dto = new ZnsbNssbCxfbzlscqdDTO();
                    dto.setUuid(GyUtils.getUuid());
                    dto.setBsfs(t.getBsfs());
                    dto.setBsnsrlx(t.getBsnsrlx());
                    dto.setDzbzdszlDm(t.getDzbzdszlDm());
                    dto.setFlzlDm(t.getFlzlDm());
                    dto.setFlzlbslxDm(t.getFlzlbslxDm());
                    dto.setFlzlbslxmc(t.getFlzlbslxmc());
                    dto.setFlzlbz(t.getFlzlbz());
                    dto.setFlzlcllxDm(t.getFlzlcllxDm());
                    dto.setFlzlcllxmc(t.getFlzlcllxmc());
                    dto.setFlzlmc(t.getFlzlmc());
                    dto.setFlzluuid(t.getFlzluuid());
                    dto.setLcswsxDm(t.getLcswsxDm());
                    dto.setSwjgDm(t.getSwjgDm());
                    dto.setSwsxDm(t.getSwsxDm());
                    dto.setXh(t.getXh());
                    dto.setYxbz(t.getYxbz());
                    dto.setLrrq(new Date());
                    dto.setXgrq(new Date());
                    dto.setSjcsdq(t.getSwjgDm());
                    dto.setYwqdDm("LQ");
                    dto.setSjgsdq(t.getSjgsdq());
                    list.add(dto);
                });
                znsbNssbCxfbzlscqdService.saveBatch(list);
            }
        }
    }
}
