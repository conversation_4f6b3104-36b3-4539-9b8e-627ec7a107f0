package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《成本法核定农产品增值税进项税额计算表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_cbfhdccpzzsjxsejsb", propOrder = { "sbbheadVO", "zzsybnsrsbCbfhdccpzzsjxsejsbbwGrid" })
@Getter
@Setter
public class ZzsybnsrsbCbfhdccpzzsjxsejsb {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 《成本法核定农产品增值税进项税额计算表》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbwGrid", required = true)
    @JSONField(name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbwGrid")
    protected ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid zzsybnsrsbCbfhdccpzzsjxsejsbbwGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO" })
    @Getter
    @Setter
    public static class ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid {
        @XmlElement(nillable = true, name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbwGridlbVO", required = true)
        @JSONField(name = "zzsybnsrsb_cbfhdccpzzsjxsejsbbwGridlbVO")
        protected List<ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO> zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO;

        /**
         * Gets the value of the zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO}
         */
        public List<ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO> getZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO() {
            if (zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO == null) {
                zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO = new ArrayList<ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO>();
            }
            return this.zzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO;
        }
    }
}