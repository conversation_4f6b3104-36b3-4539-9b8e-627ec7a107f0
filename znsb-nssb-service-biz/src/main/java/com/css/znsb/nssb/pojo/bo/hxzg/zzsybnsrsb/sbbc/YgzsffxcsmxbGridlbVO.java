
package com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ygzsffxcsmxbGridlbVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ygzsffxcsmxbGridlbVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ewbhxh" type="{http://www.chinatax.gov.cn/dataspec/}ewbhxh" minOccurs="0"/>
 *         &lt;element name="hmc" type="{http://www.chinatax.gov.cn/dataspec/}hmc"/>
 *         &lt;element name="ysxmdmjmc" type="{http://www.chinatax.gov.cn/dataspec/}ywmc" minOccurs="0"/>
 *         &lt;element name="zzsslhzsl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="yyssl" type="{http://www.chinatax.gov.cn/dataspec/}sl" minOccurs="0"/>
 *         &lt;element name="zzsbhsxse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzsxxynse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzsjshj" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzsbqsjkcje" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzskchhsxse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzskchxxynse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="zzsynse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysqcye" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysbqfse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysbqykcje" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysbqsjkcje" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysqmye" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysysyye" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *         &lt;element name="yysynse" type="{http://www.chinatax.gov.cn/dataspec/}se" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ygzsffxcsmxbGridlbVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "ewbhxh",
    "hmc",
    "ysxmdmjmc",
    "zzsslhzsl",
    "yyssl",
    "zzsbhsxse",
    "zzsxxynse",
    "zzsjshj",
    "zzsbqsjkcje",
    "zzskchhsxse",
    "zzskchxxynse",
    "zzsynse",
    "yysqcye",
    "yysbqfse",
    "yysbqykcje",
    "yysbqsjkcje",
    "yysqmye",
    "yysysyye",
    "yysynse"
})
public class YgzsffxcsmxbGridlbVO
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Long ewbhxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String ysxmdmjmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzsslhzsl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yyssl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzsbhsxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzsxxynse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzsjshj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzsbqsjkcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzskchhsxse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzskchxxynse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double zzsynse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yysqcye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yysbqfse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yysbqykcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yysbqsjkcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yysqmye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yysysyye;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double yysynse;

    /**
     * Gets the value of the ewbhxh property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getEwbhxh() {
        return ewbhxh;
    }

    /**
     * Sets the value of the ewbhxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setEwbhxh(Long value) {
        this.ewbhxh = value;
    }

    /**
     * Gets the value of the hmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHmc() {
        return hmc;
    }

    /**
     * Sets the value of the hmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHmc(String value) {
        this.hmc = value;
    }

    /**
     * Gets the value of the ysxmdmjmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYsxmdmjmc() {
        return ysxmdmjmc;
    }

    /**
     * Sets the value of the ysxmdmjmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYsxmdmjmc(String value) {
        this.ysxmdmjmc = value;
    }

    /**
     * Gets the value of the zzsslhzsl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZzsslhzsl() {
        return zzsslhzsl;
    }

    /**
     * Sets the value of the zzsslhzsl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZzsslhzsl(Double value) {
        this.zzsslhzsl = value;
    }

    /**
     * Gets the value of the yyssl property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYyssl() {
        return yyssl;
    }

    /**
     * Sets the value of the yyssl property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYyssl(Double value) {
        this.yyssl = value;
    }

    /**
     * Gets the value of the zzsbhsxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZzsbhsxse() {
        return zzsbhsxse;
    }

    /**
     * Sets the value of the zzsbhsxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZzsbhsxse(Double value) {
        this.zzsbhsxse = value;
    }

    /**
     * Gets the value of the zzsxxynse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZzsxxynse() {
        return zzsxxynse;
    }

    /**
     * Sets the value of the zzsxxynse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZzsxxynse(Double value) {
        this.zzsxxynse = value;
    }

    /**
     * Gets the value of the zzsjshj property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZzsjshj() {
        return zzsjshj;
    }

    /**
     * Sets the value of the zzsjshj property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZzsjshj(Double value) {
        this.zzsjshj = value;
    }

    /**
     * Gets the value of the zzsbqsjkcje property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZzsbqsjkcje() {
        return zzsbqsjkcje;
    }

    /**
     * Sets the value of the zzsbqsjkcje property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZzsbqsjkcje(Double value) {
        this.zzsbqsjkcje = value;
    }

    /**
     * Gets the value of the zzskchhsxse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZzskchhsxse() {
        return zzskchhsxse;
    }

    /**
     * Sets the value of the zzskchhsxse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZzskchhsxse(Double value) {
        this.zzskchhsxse = value;
    }

    /**
     * Gets the value of the zzskchxxynse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZzskchxxynse() {
        return zzskchxxynse;
    }

    /**
     * Sets the value of the zzskchxxynse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZzskchxxynse(Double value) {
        this.zzskchxxynse = value;
    }

    /**
     * Gets the value of the zzsynse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getZzsynse() {
        return zzsynse;
    }

    /**
     * Sets the value of the zzsynse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setZzsynse(Double value) {
        this.zzsynse = value;
    }

    /**
     * Gets the value of the yysqcye property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYysqcye() {
        return yysqcye;
    }

    /**
     * Sets the value of the yysqcye property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYysqcye(Double value) {
        this.yysqcye = value;
    }

    /**
     * Gets the value of the yysbqfse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYysbqfse() {
        return yysbqfse;
    }

    /**
     * Sets the value of the yysbqfse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYysbqfse(Double value) {
        this.yysbqfse = value;
    }

    /**
     * Gets the value of the yysbqykcje property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYysbqykcje() {
        return yysbqykcje;
    }

    /**
     * Sets the value of the yysbqykcje property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYysbqykcje(Double value) {
        this.yysbqykcje = value;
    }

    /**
     * Gets the value of the yysbqsjkcje property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYysbqsjkcje() {
        return yysbqsjkcje;
    }

    /**
     * Sets the value of the yysbqsjkcje property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYysbqsjkcje(Double value) {
        this.yysbqsjkcje = value;
    }

    /**
     * Gets the value of the yysqmye property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYysqmye() {
        return yysqmye;
    }

    /**
     * Sets the value of the yysqmye property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYysqmye(Double value) {
        this.yysqmye = value;
    }

    /**
     * Gets the value of the yysysyye property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYysysyye() {
        return yysysyye;
    }

    /**
     * Sets the value of the yysysyye property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYysysyye(Double value) {
        this.yysysyye = value;
    }

    /**
     * Gets the value of the yysynse property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getYysynse() {
        return yysynse;
    }

    /**
     * Sets the value of the yysynse property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setYysynse(Double value) {
        this.yysynse = value;
    }

}
