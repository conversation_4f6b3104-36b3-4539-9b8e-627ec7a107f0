package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 申报表信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbVO", propOrder = { "nsrsbh", "hkysqymc", "zjghzdndxse", "zjghzdndynse" })
@Getter
@Setter
public class SbbVO {
    /**
     * 纳税人识别号
     */
    @XmlElement(nillable = true, required = true)
    protected String nsrsbh;

    /**
     * 航空运输企业名称
     */
    protected String hkysqymc;

    /**
     * 总机构汇总的年度销售额
     */
    protected BigDecimal zjghzdndxse;

    /**
     * 总机构汇总的年度应纳税额
     */
    protected BigDecimal zjghzdndynse;
}