package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bqjxsemxbGridlbVO", propOrder = { "ewbhxh", "hmc", "xmmc", "lc", "fs", "je", "se" })
@Getter
@Setter
public class BqjxsemxbGridlbVO {
    /**
     * 二维表行序号（2017-5-31修改，8b（原8行）填写8，8a填写37行）（2020-10-26修改,23b(原23行)填写23，23a填写38行）
     */
    protected Long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 项目名称
     */
    @XmlElement(nillable = true, required = true)
    protected String xmmc;

    /**
     * 栏次
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal lc;

    /**
     * 份数
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal fs;

    /**
     * 金额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal je;

    /**
     * 税额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal se;
}