package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税减免税申报明细表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsjmssbmxbywbw", propOrder = { "zzsjmssbmxb" })
@Getter
@Setter
public class Zzsjmssbmxbywbw extends TaxDoc {
    /**
     * 增值税减免税申报明细表
     */
    @XmlElement(nillable = true, required = true)
    protected Zzsjmssbmxb zzsjmssbmxb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class Zzsjmssbmxb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.Zzsjmssbmxb {}
}