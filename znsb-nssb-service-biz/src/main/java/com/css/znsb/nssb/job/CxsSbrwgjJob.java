package com.css.znsb.nssb.job;

import com.css.znsb.nssb.service.sbrw.SbrwgjService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CxsSbrwgjJob {

    @Resource
    private SbrwgjService sbrwgjService;

    /**
     * 财行税申报任务归集
     */
    @XxlJob("cxssbrwgj")
    public void execute() {
        log.info("==========开始进行财行税申报任务归集==========");
        sbrwgjService.cxssbrwgj();
        log.info("==========财行税申报任务归集结束==========");
    }
}
