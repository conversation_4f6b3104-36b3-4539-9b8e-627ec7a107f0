
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 抵扣税款台账
 * 
 * <p>Java class for dksktz complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="dksktz">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="dksktzGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="dksktzGridlb" type="{http://www.chinatax.gov.cn/dataspec/}dksktzGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dksktz", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "dksktzGrid"
})
public class Dksktz
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected DksktzGrid dksktzGrid;

    /**
     * Gets the value of the dksktzGrid property.
     * 
     * @return
     *     possible object is
     *     {@link DksktzGrid }
     *     
     */
    public DksktzGrid getDksktzGrid() {
        return dksktzGrid;
    }

    /**
     * Sets the value of the dksktzGrid property.
     * 
     * @param value
     *     allowed object is
     *     {@link DksktzGrid }
     *     
     */
    public void setDksktzGrid(DksktzGrid value) {
        this.dksktzGrid = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="dksktzGridlb" type="{http://www.chinatax.gov.cn/dataspec/}dksktzGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "dksktzGridlb"
    })
    public static class DksktzGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected List<DksktzGridlbVO> dksktzGridlb;

        /**
         * Gets the value of the dksktzGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the dksktzGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getDksktzGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link DksktzGridlbVO }
         * 
         * 
         */
        public List<DksktzGridlbVO> getDksktzGridlb() {
            if (dksktzGridlb == null) {
                dksktzGridlb = new ArrayList<DksktzGridlbVO>();
            }
            return this.dksktzGridlb;
        }

    }

}
