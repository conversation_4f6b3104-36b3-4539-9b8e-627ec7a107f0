
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 啤酒（增值税专用发票）
 * 
 * <p>Java class for pjGrid complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="pjGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *         &lt;element name="pjGridlb" type="{http://www.chinatax.gov.cn/dataspec/}pjGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "pjGrid", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "pjGridlb"
})
public class PjGrid
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected List<PjGridlbVO> pjGridlb;

    /**
     * Gets the value of the pjGridlb property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the pjGridlb property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPjGridlb().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PjGridlbVO }
     * 
     * 
     */
    public List<PjGridlbVO> getPjGridlb() {
        if (pjGridlb == null) {
            pjGridlb = new ArrayList<PjGridlbVO>();
        }
        return this.pjGridlb;
    }

}
