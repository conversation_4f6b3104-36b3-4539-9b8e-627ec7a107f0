package com.css.znsb.nssb.job.cwbb;

import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.service.cwbb.CwbbBsService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CwbbSjlqJob {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private CwbbBsService cwbbBsService;

    /**
     * 财务报表数据拉取
     */
    @XxlJob("cwbbSjlqJob")
    public void cwbbSjlqJobExecute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("cwbbSjlqJob start");
        String key = formatKey("cwbbSjlqJob:sjlq");
        if (validateCache(key)) {
            try {
                String cs = XxlJobHelper.getJobParam();
                String sbny = DateUtils.dateToString(new Date(), 10).substring(0, 6);
                if (!GyUtils.isNull(cs)) {
                    sbny = cs;
                }
                cwbbBsService.sjlqFromLq(sbny, "");
            } finally {
                delCache(key);
            }
        }
        stopWatch.stop();
        log.info("cwbbSjlqJob end,RT:{}", stopWatch.getTotalTimeSeconds());
    }


    private Boolean validateCache(String key) {
        Boolean hasKey = stringRedisTemplate.hasKey(key);
        if (hasKey) {
            return false;
        } else {
            stringRedisTemplate.opsForValue().set(key, "1", 300, TimeUnit.MINUTES);
            return true;
        }
    }

    private void delCache(String key) {
        stringRedisTemplate.delete(key);
    }

    private static String formatKey(String key) {
        return String.format("cwbbSjlqJob:job:%s", key);
    }

}
