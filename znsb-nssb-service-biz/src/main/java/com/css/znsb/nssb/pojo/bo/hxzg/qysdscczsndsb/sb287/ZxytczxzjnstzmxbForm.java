
package com.css.znsb.nssb.pojo.bo.hxzg.qysdscczsndsb.sb287;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 *  *********《专项用途财政性资金纳税调整明细表》（A105040）申报信息
 * 
 * <p>zxytczxzjnstzmxbForm complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="zxytczxzjnstzmxbForm">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="qwndqdnd" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="qwndczxzj" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndfhbzczxzjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndfhbzczxzjjrbndje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndqwnd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndqsind" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndqsand" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndqend" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndqynd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndbnzcqkzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndbnzcqkfyhzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndbnjyjyje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndbnjysjczje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qwndbnjyyjrbnje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindqdnd" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="qsindczxzj" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindfhbzczxzjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindfhbzczxzjjrbndje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindqsind" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindqsand" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindqend" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindqynd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindbnzcqkzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindbnzcqkfyhzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindbnjyjyje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindbnjysjczje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsindbnjyyjrbnje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandqdnd" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="qsandczxzj" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandfhbzczxzjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandfhbzczxzjjrbndje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandqsand" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsanndqend" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandqynd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandbnzcqkzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandbnzcqkfyhzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandbnjyjyje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsnndbnjysjczje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qsandbnjyyjrbnje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendqdnd" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="qendczxzj" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendfhbzczxzjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendfhbzczxzjjrbndje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendqend" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendqynd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendbnzcqkzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendbnzcqkfyhje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendbnjyjyje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendbnjysjczje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qendbnjyyjrbnje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndqdnd" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="qyndczxzj" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndfhbzczxzjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndfhbzczxzjjrbnje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndqynd" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndbnzcqkzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndbnzcqkfyhzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndbnjyjyje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndbnjysjczje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="qyndbnjyyjrbnje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnqdnd" type="{http://www.chinatax.gov.cn/dataspec/}nd" minOccurs="0"/>
 *         &lt;element name="bnczxje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnfhbzczxzjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnfhbzczxzjjrbndje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnbnzcqkzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnbnzcqkfyhzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnbnjyjyje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnbnjysjczje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="bnbnjyyjrbnje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hjczxje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hjfhbzczxzjje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hjfhbzczxzjjrbndje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hjbnzcqkzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hjbnzcqkfyhzcje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hjbnjyjyje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hjbnsjczje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="hjbnjyyjrbnje" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zxytczxzjnstzmxbForm", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "qwndqdnd",
    "qwndczxzj",
    "qwndfhbzczxzjje",
    "qwndfhbzczxzjjrbndje",
    "qwndqwnd",
    "qwndqsind",
    "qwndqsand",
    "qwndqend",
    "qwndqynd",
    "qwndbnzcqkzcje",
    "qwndbnzcqkfyhzcje",
    "qwndbnjyjyje",
    "qwndbnjysjczje",
    "qwndbnjyyjrbnje",
    "qsindqdnd",
    "qsindczxzj",
    "qsindfhbzczxzjje",
    "qsindfhbzczxzjjrbndje",
    "qsindqsind",
    "qsindqsand",
    "qsindqend",
    "qsindqynd",
    "qsindbnzcqkzcje",
    "qsindbnzcqkfyhzcje",
    "qsindbnjyjyje",
    "qsindbnjysjczje",
    "qsindbnjyyjrbnje",
    "qsandqdnd",
    "qsandczxzj",
    "qsandfhbzczxzjje",
    "qsandfhbzczxzjjrbndje",
    "qsandqsand",
    "qsanndqend",
    "qsandqynd",
    "qsandbnzcqkzcje",
    "qsandbnzcqkfyhzcje",
    "qsandbnjyjyje",
    "qsnndbnjysjczje",
    "qsandbnjyyjrbnje",
    "qendqdnd",
    "qendczxzj",
    "qendfhbzczxzjje",
    "qendfhbzczxzjjrbndje",
    "qendqend",
    "qendqynd",
    "qendbnzcqkzcje",
    "qendbnzcqkfyhje",
    "qendbnjyjyje",
    "qendbnjysjczje",
    "qendbnjyyjrbnje",
    "qyndqdnd",
    "qyndczxzj",
    "qyndfhbzczxzjje",
    "qyndfhbzczxzjjrbnje",
    "qyndqynd",
    "qyndbnzcqkzcje",
    "qyndbnzcqkfyhzcje",
    "qyndbnjyjyje",
    "qyndbnjysjczje",
    "qyndbnjyyjrbnje",
    "bnqdnd",
    "bnczxje",
    "bnfhbzczxzjje",
    "bnfhbzczxzjjrbndje",
    "bnbnzcqkzcje",
    "bnbnzcqkfyhzcje",
    "bnbnjyjyje",
    "bnbnjysjczje",
    "bnbnjyyjrbnje",
    "hjczxje",
    "hjfhbzczxzjje",
    "hjfhbzczxzjjrbndje",
    "hjbnzcqkzcje",
    "hjbnzcqkfyhzcje",
    "hjbnjyjyje",
    "hjbnsjczje",
    "hjbnjyyjrbnje"
})
public class ZxytczxzjnstzmxbForm implements Serializable {

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -6788987031951452797L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qwndqdnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndczxzj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndfhbzczxzjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndfhbzczxzjjrbndje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndqwnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndqsind;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndqsand;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndqend;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndqynd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndbnzcqkzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndbnzcqkfyhzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndbnjyjyje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndbnjysjczje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qwndbnjyyjrbnje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qsindqdnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindczxzj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindfhbzczxzjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindfhbzczxzjjrbndje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindqsind;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindqsand;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindqend;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindqynd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindbnzcqkzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindbnzcqkfyhzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindbnjyjyje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindbnjysjczje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsindbnjyyjrbnje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qsandqdnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandczxzj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandfhbzczxzjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandfhbzczxzjjrbndje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandqsand;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsanndqend;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandqynd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandbnzcqkzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandbnzcqkfyhzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandbnjyjyje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsnndbnjysjczje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qsandbnjyyjrbnje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qendqdnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendczxzj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendfhbzczxzjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendfhbzczxzjjrbndje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendqend;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendqynd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendbnzcqkzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendbnzcqkfyhje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendbnjyjyje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendbnjysjczje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qendbnjyyjrbnje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qyndqdnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndczxzj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndfhbzczxzjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndfhbzczxzjjrbnje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndqynd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndbnzcqkzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndbnzcqkfyhzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndbnjyjyje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndbnjysjczje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double qyndbnjyyjrbnje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bnqdnd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnczxje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnfhbzczxzjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnfhbzczxzjjrbndje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnbnzcqkzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnbnzcqkfyhzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnbnjyjyje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnbnjysjczje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double bnbnjyyjrbnje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hjczxje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hjfhbzczxzjje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hjfhbzczxzjjrbndje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hjbnzcqkzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hjbnzcqkfyhzcje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hjbnjyjyje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hjbnsjczje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double hjbnjyyjrbnje;

    /**
     * 获取qwndqdnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQwndqdnd() {
        return qwndqdnd;
    }

    /**
     * 设置qwndqdnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQwndqdnd(String value) {
        this.qwndqdnd = value;
    }

    /**
     * 获取qwndczxzj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndczxzj() {
        return qwndczxzj;
    }

    /**
     * 设置qwndczxzj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndczxzj(Double value) {
        this.qwndczxzj = value;
    }

    /**
     * 获取qwndfhbzczxzjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndfhbzczxzjje() {
        return qwndfhbzczxzjje;
    }

    /**
     * 设置qwndfhbzczxzjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndfhbzczxzjje(Double value) {
        this.qwndfhbzczxzjje = value;
    }

    /**
     * 获取qwndfhbzczxzjjrbndje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndfhbzczxzjjrbndje() {
        return qwndfhbzczxzjjrbndje;
    }

    /**
     * 设置qwndfhbzczxzjjrbndje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndfhbzczxzjjrbndje(Double value) {
        this.qwndfhbzczxzjjrbndje = value;
    }

    /**
     * 获取qwndqwnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndqwnd() {
        return qwndqwnd;
    }

    /**
     * 设置qwndqwnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndqwnd(Double value) {
        this.qwndqwnd = value;
    }

    /**
     * 获取qwndqsind属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndqsind() {
        return qwndqsind;
    }

    /**
     * 设置qwndqsind属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndqsind(Double value) {
        this.qwndqsind = value;
    }

    /**
     * 获取qwndqsand属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndqsand() {
        return qwndqsand;
    }

    /**
     * 设置qwndqsand属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndqsand(Double value) {
        this.qwndqsand = value;
    }

    /**
     * 获取qwndqend属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndqend() {
        return qwndqend;
    }

    /**
     * 设置qwndqend属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndqend(Double value) {
        this.qwndqend = value;
    }

    /**
     * 获取qwndqynd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndqynd() {
        return qwndqynd;
    }

    /**
     * 设置qwndqynd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndqynd(Double value) {
        this.qwndqynd = value;
    }

    /**
     * 获取qwndbnzcqkzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndbnzcqkzcje() {
        return qwndbnzcqkzcje;
    }

    /**
     * 设置qwndbnzcqkzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndbnzcqkzcje(Double value) {
        this.qwndbnzcqkzcje = value;
    }

    /**
     * 获取qwndbnzcqkfyhzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndbnzcqkfyhzcje() {
        return qwndbnzcqkfyhzcje;
    }

    /**
     * 设置qwndbnzcqkfyhzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndbnzcqkfyhzcje(Double value) {
        this.qwndbnzcqkfyhzcje = value;
    }

    /**
     * 获取qwndbnjyjyje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndbnjyjyje() {
        return qwndbnjyjyje;
    }

    /**
     * 设置qwndbnjyjyje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndbnjyjyje(Double value) {
        this.qwndbnjyjyje = value;
    }

    /**
     * 获取qwndbnjysjczje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndbnjysjczje() {
        return qwndbnjysjczje;
    }

    /**
     * 设置qwndbnjysjczje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndbnjysjczje(Double value) {
        this.qwndbnjysjczje = value;
    }

    /**
     * 获取qwndbnjyyjrbnje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQwndbnjyyjrbnje() {
        return qwndbnjyyjrbnje;
    }

    /**
     * 设置qwndbnjyyjrbnje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQwndbnjyyjrbnje(Double value) {
        this.qwndbnjyyjrbnje = value;
    }

    /**
     * 获取qsindqdnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQsindqdnd() {
        return qsindqdnd;
    }

    /**
     * 设置qsindqdnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQsindqdnd(String value) {
        this.qsindqdnd = value;
    }

    /**
     * 获取qsindczxzj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindczxzj() {
        return qsindczxzj;
    }

    /**
     * 设置qsindczxzj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindczxzj(Double value) {
        this.qsindczxzj = value;
    }

    /**
     * 获取qsindfhbzczxzjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindfhbzczxzjje() {
        return qsindfhbzczxzjje;
    }

    /**
     * 设置qsindfhbzczxzjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindfhbzczxzjje(Double value) {
        this.qsindfhbzczxzjje = value;
    }

    /**
     * 获取qsindfhbzczxzjjrbndje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindfhbzczxzjjrbndje() {
        return qsindfhbzczxzjjrbndje;
    }

    /**
     * 设置qsindfhbzczxzjjrbndje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindfhbzczxzjjrbndje(Double value) {
        this.qsindfhbzczxzjjrbndje = value;
    }

    /**
     * 获取qsindqsind属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindqsind() {
        return qsindqsind;
    }

    /**
     * 设置qsindqsind属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindqsind(Double value) {
        this.qsindqsind = value;
    }

    /**
     * 获取qsindqsand属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindqsand() {
        return qsindqsand;
    }

    /**
     * 设置qsindqsand属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindqsand(Double value) {
        this.qsindqsand = value;
    }

    /**
     * 获取qsindqend属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindqend() {
        return qsindqend;
    }

    /**
     * 设置qsindqend属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindqend(Double value) {
        this.qsindqend = value;
    }

    /**
     * 获取qsindqynd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindqynd() {
        return qsindqynd;
    }

    /**
     * 设置qsindqynd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindqynd(Double value) {
        this.qsindqynd = value;
    }

    /**
     * 获取qsindbnzcqkzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindbnzcqkzcje() {
        return qsindbnzcqkzcje;
    }

    /**
     * 设置qsindbnzcqkzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindbnzcqkzcje(Double value) {
        this.qsindbnzcqkzcje = value;
    }

    /**
     * 获取qsindbnzcqkfyhzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindbnzcqkfyhzcje() {
        return qsindbnzcqkfyhzcje;
    }

    /**
     * 设置qsindbnzcqkfyhzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindbnzcqkfyhzcje(Double value) {
        this.qsindbnzcqkfyhzcje = value;
    }

    /**
     * 获取qsindbnjyjyje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindbnjyjyje() {
        return qsindbnjyjyje;
    }

    /**
     * 设置qsindbnjyjyje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindbnjyjyje(Double value) {
        this.qsindbnjyjyje = value;
    }

    /**
     * 获取qsindbnjysjczje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindbnjysjczje() {
        return qsindbnjysjczje;
    }

    /**
     * 设置qsindbnjysjczje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindbnjysjczje(Double value) {
        this.qsindbnjysjczje = value;
    }

    /**
     * 获取qsindbnjyyjrbnje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsindbnjyyjrbnje() {
        return qsindbnjyyjrbnje;
    }

    /**
     * 设置qsindbnjyyjrbnje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsindbnjyyjrbnje(Double value) {
        this.qsindbnjyyjrbnje = value;
    }

    /**
     * 获取qsandqdnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQsandqdnd() {
        return qsandqdnd;
    }

    /**
     * 设置qsandqdnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQsandqdnd(String value) {
        this.qsandqdnd = value;
    }

    /**
     * 获取qsandczxzj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandczxzj() {
        return qsandczxzj;
    }

    /**
     * 设置qsandczxzj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandczxzj(Double value) {
        this.qsandczxzj = value;
    }

    /**
     * 获取qsandfhbzczxzjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandfhbzczxzjje() {
        return qsandfhbzczxzjje;
    }

    /**
     * 设置qsandfhbzczxzjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandfhbzczxzjje(Double value) {
        this.qsandfhbzczxzjje = value;
    }

    /**
     * 获取qsandfhbzczxzjjrbndje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandfhbzczxzjjrbndje() {
        return qsandfhbzczxzjjrbndje;
    }

    /**
     * 设置qsandfhbzczxzjjrbndje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandfhbzczxzjjrbndje(Double value) {
        this.qsandfhbzczxzjjrbndje = value;
    }

    /**
     * 获取qsandqsand属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandqsand() {
        return qsandqsand;
    }

    /**
     * 设置qsandqsand属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandqsand(Double value) {
        this.qsandqsand = value;
    }

    /**
     * 获取qsanndqend属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsanndqend() {
        return qsanndqend;
    }

    /**
     * 设置qsanndqend属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsanndqend(Double value) {
        this.qsanndqend = value;
    }

    /**
     * 获取qsandqynd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandqynd() {
        return qsandqynd;
    }

    /**
     * 设置qsandqynd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandqynd(Double value) {
        this.qsandqynd = value;
    }

    /**
     * 获取qsandbnzcqkzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandbnzcqkzcje() {
        return qsandbnzcqkzcje;
    }

    /**
     * 设置qsandbnzcqkzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandbnzcqkzcje(Double value) {
        this.qsandbnzcqkzcje = value;
    }

    /**
     * 获取qsandbnzcqkfyhzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandbnzcqkfyhzcje() {
        return qsandbnzcqkfyhzcje;
    }

    /**
     * 设置qsandbnzcqkfyhzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandbnzcqkfyhzcje(Double value) {
        this.qsandbnzcqkfyhzcje = value;
    }

    /**
     * 获取qsandbnjyjyje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandbnjyjyje() {
        return qsandbnjyjyje;
    }

    /**
     * 设置qsandbnjyjyje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandbnjyjyje(Double value) {
        this.qsandbnjyjyje = value;
    }

    /**
     * 获取qsnndbnjysjczje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsnndbnjysjczje() {
        return qsnndbnjysjczje;
    }

    /**
     * 设置qsnndbnjysjczje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsnndbnjysjczje(Double value) {
        this.qsnndbnjysjczje = value;
    }

    /**
     * 获取qsandbnjyyjrbnje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQsandbnjyyjrbnje() {
        return qsandbnjyyjrbnje;
    }

    /**
     * 设置qsandbnjyyjrbnje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQsandbnjyyjrbnje(Double value) {
        this.qsandbnjyyjrbnje = value;
    }

    /**
     * 获取qendqdnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQendqdnd() {
        return qendqdnd;
    }

    /**
     * 设置qendqdnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQendqdnd(String value) {
        this.qendqdnd = value;
    }

    /**
     * 获取qendczxzj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendczxzj() {
        return qendczxzj;
    }

    /**
     * 设置qendczxzj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendczxzj(Double value) {
        this.qendczxzj = value;
    }

    /**
     * 获取qendfhbzczxzjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendfhbzczxzjje() {
        return qendfhbzczxzjje;
    }

    /**
     * 设置qendfhbzczxzjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendfhbzczxzjje(Double value) {
        this.qendfhbzczxzjje = value;
    }

    /**
     * 获取qendfhbzczxzjjrbndje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendfhbzczxzjjrbndje() {
        return qendfhbzczxzjjrbndje;
    }

    /**
     * 设置qendfhbzczxzjjrbndje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendfhbzczxzjjrbndje(Double value) {
        this.qendfhbzczxzjjrbndje = value;
    }

    /**
     * 获取qendqend属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendqend() {
        return qendqend;
    }

    /**
     * 设置qendqend属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendqend(Double value) {
        this.qendqend = value;
    }

    /**
     * 获取qendqynd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendqynd() {
        return qendqynd;
    }

    /**
     * 设置qendqynd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendqynd(Double value) {
        this.qendqynd = value;
    }

    /**
     * 获取qendbnzcqkzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendbnzcqkzcje() {
        return qendbnzcqkzcje;
    }

    /**
     * 设置qendbnzcqkzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendbnzcqkzcje(Double value) {
        this.qendbnzcqkzcje = value;
    }

    /**
     * 获取qendbnzcqkfyhje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendbnzcqkfyhje() {
        return qendbnzcqkfyhje;
    }

    /**
     * 设置qendbnzcqkfyhje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendbnzcqkfyhje(Double value) {
        this.qendbnzcqkfyhje = value;
    }

    /**
     * 获取qendbnjyjyje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendbnjyjyje() {
        return qendbnjyjyje;
    }

    /**
     * 设置qendbnjyjyje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendbnjyjyje(Double value) {
        this.qendbnjyjyje = value;
    }

    /**
     * 获取qendbnjysjczje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendbnjysjczje() {
        return qendbnjysjczje;
    }

    /**
     * 设置qendbnjysjczje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendbnjysjczje(Double value) {
        this.qendbnjysjczje = value;
    }

    /**
     * 获取qendbnjyyjrbnje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQendbnjyyjrbnje() {
        return qendbnjyyjrbnje;
    }

    /**
     * 设置qendbnjyyjrbnje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQendbnjyyjrbnje(Double value) {
        this.qendbnjyyjrbnje = value;
    }

    /**
     * 获取qyndqdnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQyndqdnd() {
        return qyndqdnd;
    }

    /**
     * 设置qyndqdnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQyndqdnd(String value) {
        this.qyndqdnd = value;
    }

    /**
     * 获取qyndczxzj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndczxzj() {
        return qyndczxzj;
    }

    /**
     * 设置qyndczxzj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndczxzj(Double value) {
        this.qyndczxzj = value;
    }

    /**
     * 获取qyndfhbzczxzjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndfhbzczxzjje() {
        return qyndfhbzczxzjje;
    }

    /**
     * 设置qyndfhbzczxzjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndfhbzczxzjje(Double value) {
        this.qyndfhbzczxzjje = value;
    }

    /**
     * 获取qyndfhbzczxzjjrbnje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndfhbzczxzjjrbnje() {
        return qyndfhbzczxzjjrbnje;
    }

    /**
     * 设置qyndfhbzczxzjjrbnje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndfhbzczxzjjrbnje(Double value) {
        this.qyndfhbzczxzjjrbnje = value;
    }

    /**
     * 获取qyndqynd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndqynd() {
        return qyndqynd;
    }

    /**
     * 设置qyndqynd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndqynd(Double value) {
        this.qyndqynd = value;
    }

    /**
     * 获取qyndbnzcqkzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndbnzcqkzcje() {
        return qyndbnzcqkzcje;
    }

    /**
     * 设置qyndbnzcqkzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndbnzcqkzcje(Double value) {
        this.qyndbnzcqkzcje = value;
    }

    /**
     * 获取qyndbnzcqkfyhzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndbnzcqkfyhzcje() {
        return qyndbnzcqkfyhzcje;
    }

    /**
     * 设置qyndbnzcqkfyhzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndbnzcqkfyhzcje(Double value) {
        this.qyndbnzcqkfyhzcje = value;
    }

    /**
     * 获取qyndbnjyjyje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndbnjyjyje() {
        return qyndbnjyjyje;
    }

    /**
     * 设置qyndbnjyjyje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndbnjyjyje(Double value) {
        this.qyndbnjyjyje = value;
    }

    /**
     * 获取qyndbnjysjczje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndbnjysjczje() {
        return qyndbnjysjczje;
    }

    /**
     * 设置qyndbnjysjczje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndbnjysjczje(Double value) {
        this.qyndbnjysjczje = value;
    }

    /**
     * 获取qyndbnjyyjrbnje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getQyndbnjyyjrbnje() {
        return qyndbnjyyjrbnje;
    }

    /**
     * 设置qyndbnjyyjrbnje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setQyndbnjyyjrbnje(Double value) {
        this.qyndbnjyyjrbnje = value;
    }

    /**
     * 获取bnqdnd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBnqdnd() {
        return bnqdnd;
    }

    /**
     * 设置bnqdnd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBnqdnd(String value) {
        this.bnqdnd = value;
    }

    /**
     * 获取bnczxje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnczxje() {
        return bnczxje;
    }

    /**
     * 设置bnczxje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnczxje(Double value) {
        this.bnczxje = value;
    }

    /**
     * 获取bnfhbzczxzjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnfhbzczxzjje() {
        return bnfhbzczxzjje;
    }

    /**
     * 设置bnfhbzczxzjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnfhbzczxzjje(Double value) {
        this.bnfhbzczxzjje = value;
    }

    /**
     * 获取bnfhbzczxzjjrbndje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnfhbzczxzjjrbndje() {
        return bnfhbzczxzjjrbndje;
    }

    /**
     * 设置bnfhbzczxzjjrbndje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnfhbzczxzjjrbndje(Double value) {
        this.bnfhbzczxzjjrbndje = value;
    }

    /**
     * 获取bnbnzcqkzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnbnzcqkzcje() {
        return bnbnzcqkzcje;
    }

    /**
     * 设置bnbnzcqkzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnbnzcqkzcje(Double value) {
        this.bnbnzcqkzcje = value;
    }

    /**
     * 获取bnbnzcqkfyhzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnbnzcqkfyhzcje() {
        return bnbnzcqkfyhzcje;
    }

    /**
     * 设置bnbnzcqkfyhzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnbnzcqkfyhzcje(Double value) {
        this.bnbnzcqkfyhzcje = value;
    }

    /**
     * 获取bnbnjyjyje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnbnjyjyje() {
        return bnbnjyjyje;
    }

    /**
     * 设置bnbnjyjyje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnbnjyjyje(Double value) {
        this.bnbnjyjyje = value;
    }

    /**
     * 获取bnbnjysjczje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnbnjysjczje() {
        return bnbnjysjczje;
    }

    /**
     * 设置bnbnjysjczje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnbnjysjczje(Double value) {
        this.bnbnjysjczje = value;
    }

    /**
     * 获取bnbnjyyjrbnje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getBnbnjyyjrbnje() {
        return bnbnjyyjrbnje;
    }

    /**
     * 设置bnbnjyyjrbnje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setBnbnjyyjrbnje(Double value) {
        this.bnbnjyyjrbnje = value;
    }

    /**
     * 获取hjczxje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHjczxje() {
        return hjczxje;
    }

    /**
     * 设置hjczxje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHjczxje(Double value) {
        this.hjczxje = value;
    }

    /**
     * 获取hjfhbzczxzjje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHjfhbzczxzjje() {
        return hjfhbzczxzjje;
    }

    /**
     * 设置hjfhbzczxzjje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHjfhbzczxzjje(Double value) {
        this.hjfhbzczxzjje = value;
    }

    /**
     * 获取hjfhbzczxzjjrbndje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHjfhbzczxzjjrbndje() {
        return hjfhbzczxzjjrbndje;
    }

    /**
     * 设置hjfhbzczxzjjrbndje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHjfhbzczxzjjrbndje(Double value) {
        this.hjfhbzczxzjjrbndje = value;
    }

    /**
     * 获取hjbnzcqkzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHjbnzcqkzcje() {
        return hjbnzcqkzcje;
    }

    /**
     * 设置hjbnzcqkzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHjbnzcqkzcje(Double value) {
        this.hjbnzcqkzcje = value;
    }

    /**
     * 获取hjbnzcqkfyhzcje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHjbnzcqkfyhzcje() {
        return hjbnzcqkfyhzcje;
    }

    /**
     * 设置hjbnzcqkfyhzcje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHjbnzcqkfyhzcje(Double value) {
        this.hjbnzcqkfyhzcje = value;
    }

    /**
     * 获取hjbnjyjyje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHjbnjyjyje() {
        return hjbnjyjyje;
    }

    /**
     * 设置hjbnjyjyje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHjbnjyjyje(Double value) {
        this.hjbnjyjyje = value;
    }

    /**
     * 获取hjbnsjczje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHjbnsjczje() {
        return hjbnsjczje;
    }

    /**
     * 设置hjbnsjczje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHjbnsjczje(Double value) {
        this.hjbnsjczje = value;
    }

    /**
     * 获取hjbnjyyjrbnje属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getHjbnjyyjrbnje() {
        return hjbnjyyjrbnje;
    }

    /**
     * 设置hjbnjyyjrbnje属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setHjbnjyyjrbnje(Double value) {
        this.hjbnjyyjrbnje = value;
    }

}
