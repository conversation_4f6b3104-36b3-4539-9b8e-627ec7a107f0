package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《邮政企业分支机构增值税汇总纳税信息传递单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_yzqyfzjgzzshznsxxcdd", propOrder = { "sbbheadVO", "zzsybnsrsbYjnzzsqkForm", "zzsybnsrsbQdjxseqkGrid" })
@Getter
@Setter
public class ZzsybnsrsbYzqyfzjgzzshznsxxcdd {
    /**
     * 申报表头信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 《已缴纳增值税情况》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_yjnzzsqkForm", required = true)
    @JSONField(name = "zzsybnsrsb_yjnzzsqkForm")
    protected ZzsybnsrsbYjnzzsqkForm zzsybnsrsbYjnzzsqkForm;

    /**
     * 《取得进项税额情况》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_qdjxseqkGrid", required = true)
    @JSONField(name = "zzsybnsrsb_qdjxseqkGrid")
    protected ZzsybnsrsbQdjxseqkGrid zzsybnsrsbQdjxseqkGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "zzsybnsrsbQdjxseqkGridlb" })
    @Getter
    @Setter
    public static class ZzsybnsrsbQdjxseqkGrid extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzsybnsrsbQdjxseqkGrid {
        @XmlElement(nillable = true, name = "zzsybnsrsb_qdjxseqkGridlb", required = true)
        @JSONField(name = "zzsybnsrsb_qdjxseqkGridlb")
        protected List<ZzsybnsrsbQdjxseqkGridlb> zzsybnsrsbQdjxseqkGridlb;

        /**
         * Gets the value of the zzsybnsrsbQdjxseqkGridlb property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the zzsybnsrsbQdjxseqkGridlb property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getZzsybnsrsbQdjxseqkGridlb().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ZzsybnsrsbQdjxseqkGridlb}
         */
        public List<ZzsybnsrsbQdjxseqkGridlb> getZzsybnsrsbQdjxseqkGridlb() {
            if (zzsybnsrsbQdjxseqkGridlb == null) {
                zzsybnsrsbQdjxseqkGridlb = new ArrayList<ZzsybnsrsbQdjxseqkGridlb>();
            }
            return this.zzsybnsrsbQdjxseqkGridlb;
        }
    }
}