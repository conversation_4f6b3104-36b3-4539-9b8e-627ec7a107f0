package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 机动车辆生产企业销售明细表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jdclscqyxsmxbGridlbVO", propOrder = { "ewbhxh", "xsqkClsbdm", "xsqkCpxh", "ghfnsrrbh", "xsdxmc", "xsqkXsjg" })
@Getter
@Setter
public class JdclscqyxsmxbGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 销售情况车辆识别代码车架号码
     */
    protected String xsqkClsbdm;

    /**
     * 销售情况厂牌型号
     */
    protected String xsqkCpxh;

    /**
     * 购货方纳税人认别号
     */
    protected String ghfnsrrbh;

    /**
     * 销售对象名称
     */
    protected String xsdxmc;

    /**
     * 销售情况销售价格
     */
    protected BigDecimal xsqkXsjg;
}