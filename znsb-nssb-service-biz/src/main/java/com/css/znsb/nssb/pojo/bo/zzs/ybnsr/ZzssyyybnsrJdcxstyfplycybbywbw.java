package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车销售统一发票领用存月报表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdcxstyfplycybbywbw", propOrder = { "zzssyyybnsrJdcxstyfplycybb" })
@Getter
@Setter
public class ZzssyyybnsrJdcxstyfplycybbywbw extends TaxDoc {
    /**
     * 《机动车销售统一发票领用存月报表》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_jdcxstyfplycybb", required = true)
    @JSONField(name = "zzssyyybnsr_jdcxstyfplycybb")
    protected ZzssyyybnsrJdcxstyfplycybb zzssyyybnsrJdcxstyfplycybb;
}