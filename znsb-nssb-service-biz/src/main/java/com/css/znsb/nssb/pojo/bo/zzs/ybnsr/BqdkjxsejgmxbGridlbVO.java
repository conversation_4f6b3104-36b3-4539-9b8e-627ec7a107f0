package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bqdkjxsejgmxbGridlbVO", propOrder = { "ewbhxh", "hmc", "je", "se" })
@Getter
@Setter
public class BqdkjxsejgmxbGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 金额
     */
    protected BigDecimal je;

    /**
     * 税额
     */
    protected BigDecimal se;
}