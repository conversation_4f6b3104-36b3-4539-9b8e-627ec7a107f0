
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 《消费税》涂料申报
 * 
 * <p>Java class for xfsTlsb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="xfsTlsb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="xfssb8" type="{http://www.chinatax.gov.cn/dataspec/}xfssb_qt"/>
 *         &lt;element name="xfssb8_fb1" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsejsbtl" minOccurs="0"/>
 *         &lt;element name="xfssb8_fb2" type="{http://www.chinatax.gov.cn/dataspec/}bqdsdjsejsbtl" minOccurs="0"/>
 *         &lt;element name="xfssb8_fb3" type="{http://www.chinatax.gov.cn/dataspec/}bqjmsemxb" minOccurs="0"/>
 *         &lt;element name="xfssb8_fb4" type="{http://www.chinatax.gov.cn/dataspec/}dctlskdktz" minOccurs="0"/>
 *         &lt;element name="xfssb8_fb5" type="{http://www.chinatax.gov.cn/dataspec/}hznsqyxfsfpb" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xfsTlsb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "xfssb8",
    "xfssb8Fb1",
    "xfssb8Fb2",
    "xfssb8Fb3",
    "xfssb8Fb4",
    "xfssb8Fb5"
})
public class XfsTlsb
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected XfssbQt xfssb8;
    @XmlElement(name = "xfssb8_fb1", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqjmsejsbtl xfssb8Fb1;
    @XmlElement(name = "xfssb8_fb2", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqdsdjsejsbtl xfssb8Fb2;
    @XmlElement(name = "xfssb8_fb3", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Bqjmsemxb xfssb8Fb3;
    @XmlElement(name = "xfssb8_fb4", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Dctlskdktz xfssb8Fb4;
    @XmlElement(name = "xfssb8_fb5", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Hznsqyxfsfpb xfssb8Fb5;

    /**
     * Gets the value of the xfssb8 property.
     * 
     * @return
     *     possible object is
     *     {@link XfssbQt }
     *     
     */
    public XfssbQt getXfssb8() {
        return xfssb8;
    }

    /**
     * Sets the value of the xfssb8 property.
     * 
     * @param value
     *     allowed object is
     *     {@link XfssbQt }
     *     
     */
    public void setXfssb8(XfssbQt value) {
        this.xfssb8 = value;
    }

    /**
     * Gets the value of the xfssb8Fb1 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqjmsejsbtl }
     *     
     */
    public Bqjmsejsbtl getXfssb8Fb1() {
        return xfssb8Fb1;
    }

    /**
     * Sets the value of the xfssb8Fb1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqjmsejsbtl }
     *     
     */
    public void setXfssb8Fb1(Bqjmsejsbtl value) {
        this.xfssb8Fb1 = value;
    }

    /**
     * Gets the value of the xfssb8Fb2 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqdsdjsejsbtl }
     *     
     */
    public Bqdsdjsejsbtl getXfssb8Fb2() {
        return xfssb8Fb2;
    }

    /**
     * Sets the value of the xfssb8Fb2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqdsdjsejsbtl }
     *     
     */
    public void setXfssb8Fb2(Bqdsdjsejsbtl value) {
        this.xfssb8Fb2 = value;
    }

    /**
     * Gets the value of the xfssb8Fb3 property.
     * 
     * @return
     *     possible object is
     *     {@link Bqjmsemxb }
     *     
     */
    public Bqjmsemxb getXfssb8Fb3() {
        return xfssb8Fb3;
    }

    /**
     * Sets the value of the xfssb8Fb3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Bqjmsemxb }
     *     
     */
    public void setXfssb8Fb3(Bqjmsemxb value) {
        this.xfssb8Fb3 = value;
    }

    /**
     * Gets the value of the xfssb8Fb4 property.
     * 
     * @return
     *     possible object is
     *     {@link Dctlskdktz }
     *     
     */
    public Dctlskdktz getXfssb8Fb4() {
        return xfssb8Fb4;
    }

    /**
     * Sets the value of the xfssb8Fb4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Dctlskdktz }
     *     
     */
    public void setXfssb8Fb4(Dctlskdktz value) {
        this.xfssb8Fb4 = value;
    }

    /**
     * Gets the value of the xfssb8Fb5 property.
     * 
     * @return
     *     possible object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public Hznsqyxfsfpb getXfssb8Fb5() {
        return xfssb8Fb5;
    }

    /**
     * Sets the value of the xfssb8Fb5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Hznsqyxfsfpb }
     *     
     */
    public void setXfssb8Fb5(Hznsqyxfsfpb value) {
        this.xfssb8Fb5 = value;
    }

}
