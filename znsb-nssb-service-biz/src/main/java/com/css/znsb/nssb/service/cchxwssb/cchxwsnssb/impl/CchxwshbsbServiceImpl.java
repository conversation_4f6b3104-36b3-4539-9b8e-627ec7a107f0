package com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.company.CompanyDTO;
import com.css.znsb.mhzc.pojo.company.DjxhReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.*;
import com.css.znsb.mhzc.pojo.yhxx.RyxxVO;
import com.css.znsb.nssb.api.gy.SbGyGzlAPI;
import com.css.znsb.nssb.constants.SfEnum;
import com.css.znsb.nssb.constants.enums.JylxDmEnum;
import com.css.znsb.nssb.constants.enums.YesOrNoEnum;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.CchxwsSbDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.CxsCxsjcljgResponseDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.CxsLslfJmxxDTO;
import com.css.znsb.nssb.pojo.dto.cchxwssb.cchxws.CxsQuerymxInitDTO;
import com.css.znsb.nssb.pojo.dto.gy.GySbGzlStartReqDTO;
import com.css.znsb.nssb.pojo.dto.gy.GySbGzlStartRespDTO;
import com.css.znsb.nssb.pojo.dto.sbbc.SbbcGyRespDTO;
import com.css.znsb.nssb.pojo.dto.yhssycj.jyss.YhsTysbbYtReqDTO;
import com.css.znsb.nssb.pojo.dto.zcxx.ZnsbNssbZcxxDTO;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.zcxx.ZzsybnsrZcxxYwbwDTO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.CchxwsnssbInitResMxVO;
import com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.SB10735.HXZGSB10735Response;
import com.css.znsb.nssb.pojo.vo.common.sbtj.SbtjResponseVO;
import com.css.znsb.nssb.service.cchxwssb.cchxwsnssb.CchxwshbsbService;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.CztdsysService;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.FcsService;
import com.css.znsb.nssb.service.cchxwssb.yhssjcj.ZnsbJyssSsSsjgYhsService;
import com.css.znsb.nssb.service.cchxwssb.yyssycj.YyssycjService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import com.css.znsb.nssb.service.zcxx.ZnsbNssbZcxxService;
import com.css.znsb.nssb.service.zfjg.ZfjgxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.css.znsb.nssb.constants.CchxwsSjjhConstants.CXS_CWGZBCTJ_SJJHLXDM;
import static com.css.znsb.nssb.constants.CchxwsSjjhConstants.CXS_ZCSBBCTJ_SJJHLXDM;
import static com.css.znsb.nssb.constants.enums.NssbErrorCodeConstants.ZCXX_NOT_EXISTS;
import static com.css.znsb.nssb.constants.enums.ZsxmDmEnum.*;


/**
 * 财产和行为税纳税申报
 */
@Slf4j
@Service
public class CchxwshbsbServiceImpl implements CchxwshbsbService {

    @Resource
    NsrxxApi nsrxxApi;
    @Resource
    FcsService fcsService;
    @Resource
    CztdsysService cztdsysService;
    @Resource
    SjjhService sjjhService;
    @Resource
    ZnsbJyssSsSsjgYhsService znsbJyssSsSsjgYhsService;
    @Resource
    ZnsbNssbSbrwMapper znsbNssbSbrwMapper;
    @Resource
    ZnsbNssbSbrwService sbrwService;
    @Resource
    private ZnsbNssbZcxxService zcxxService;
    @Resource
    private SbGyGzlAPI sbGyGzlAPI;
    @Resource
    YyssycjService yyssycjService;
    @Resource
    CompanyApi companyApi;
    @Resource
    ZfjgxxService zfjgxxService;

    /**
     * 获取财行税预填数据
     *
     * @param sbrwDO
     * @param cxsQuerymxInitDTO
     * @return
     */
    @Override
    public HXZGSB10735Response getCxsSbYtsj(ZnsbNssbSbrwDO sbrwDO, CxsQuerymxInitDTO cxsQuerymxInitDTO) {
        final String zsxmDm = sbrwDO.getZsxmDm();

        // 创建一个 Map 来存储不同 zsxmDm 对应的处理逻辑
        Map<String, Supplier<HXZGSB10735Response>> zsxmDmHandlerMap = new HashMap<>();

        // YHS 处理逻辑
        zsxmDmHandlerMap.put(YHS.getCode(), () -> {
            YhsTysbbYtReqDTO yhsTysbbYtReqDTO = BeanUtil.toBean(cxsQuerymxInitDTO, YhsTysbbYtReqDTO.class);
            yhsTysbbYtReqDTO.setSyuuid(cxsQuerymxInitDTO.getSyuuidList());
            String yskg = CacheUtils.getXtcs("CCHXWSNSSB00001");
            if ("Y".equals(yskg)) {
                return znsbJyssSsSsjgYhsService.getCxxx1(yhsTysbbYtReqDTO);
            } else {
                return znsbJyssSsSsjgYhsService.getCxxx(yhsTysbbYtReqDTO);
            }
        });

        // CZTDSYS 处理逻辑
        zsxmDmHandlerMap.put(CZTDSYS.getCode(), () -> cztdsysService.cxsInit(cxsQuerymxInitDTO));

        // FCS 处理逻辑
        zsxmDmHandlerMap.put(FCS.getCode(), () -> fcsService.cxsInit(cxsQuerymxInitDTO));

        // YYS 处理逻辑
        zsxmDmHandlerMap.put(YYS.getCode(), () -> yyssycjService.querymxInitData(cxsQuerymxInitDTO));

        // 查找对应的处理逻辑，如果没有找到返回默认空响应
        Supplier<HXZGSB10735Response> handler = zsxmDmHandlerMap.get(zsxmDm);

        if (handler != null) {
            return handler.get();  // 执行相应的服务方法并返回结果
        } else {
            return new HXZGSB10735Response();  // 如果没有对应的处理逻辑，返回默认空响应
        }
    }

    /**
     * 申报提交
     *
     * @param cchxwsSbDTO 申报提交
     * @return {@link SbtjResponseVO}
     */
    @Override
    public CommonResult<SbtjResponseVO> sbtj(CchxwsSbDTO cchxwsSbDTO) {
        // 返回信息
        final SbtjResponseVO responseVO = new SbtjResponseVO();
        final String sbrwuuid = cchxwsSbDTO.getSbrwuuid();

        // 查询暂存信息
        final ZnsbNssbZcxxDTO zcxxDTO = this.queryZcxx(null, sbrwuuid);
        if (GyUtils.isNull(zcxxDTO)) {
            return CommonResult.error(ZCXX_NOT_EXISTS);
        }

//        final RyxxVO ryxxVO = this.getRyxx(sbrwuuid);
//        String bsrxm = null;
//        String sfzjlxDm = null;
//        String sfzjhm = null;
//        if (GyUtils.isNotNull(ryxxVO)) {
//            bsrxm = ryxxVO.getZsxm1();
//            sfzjlxDm = ryxxVO.getSfzjlx();
//            sfzjhm = ryxxVO.getSfzjhm();
//        }
//        // 更新申报任务中的办税人姓名
//        sbrwService.updateBsrxm(sbrwuuid, bsrxm);

        // 推送工作流
        final String sfqygzl = this.pushGzl(cchxwsSbDTO, zcxxDTO);
        responseVO.setSfqyGzl(sfqygzl);
        if ("N".equals(sfqygzl)) {
            // 不启用工作流，直接申报
            return this.saveSbb(cchxwsSbDTO);
        }
        return CommonResult.success(responseVO);
    }

    /**
     * 保存财行税申报信息
     *
     * 此方法根据提供的申报信息DTO（sbDTO）来保存申报信息它首先构建一个数据交换DTO（sjjhDTO），
     * 设置相应的数据交换类型代码和其他相关信息，然后调用数据交换服务来保存申报信息
     *
     * @param sbDTO 申报信息的数据传输对象，包含申报所需的各项信息
     * @return 返回一个包含响应信息的通用结果对象，其中包含是否成功以及响应数据
     */
    private CommonResult<SbtjResponseVO> saveSbb(CchxwsSbDTO sbDTO) {
        final SbtjResponseVO responseVO = new SbtjResponseVO();
        final String ywbw = sbDTO.getYwbw();
        log.info("财行税提交申报保存请求:{}", ywbw);
        final SjjhDTO sjjhDTO = new SjjhDTO();
        if (JylxDmEnum.GZSB.getCode().equals(sbDTO.getJylxDm())) {
            // 更正申报
            sjjhDTO.setSjjhlxDm(CXS_CWGZBCTJ_SJJHLXDM);
        } else {
            // 正常申报
            sjjhDTO.setSjjhlxDm(CXS_ZCSBBCTJ_SJJHLXDM);
        }
        sjjhDTO.setYwbm(YzpzzlEnum.CXS.getDm());
        sjjhDTO.setYwuuid(sbDTO.getSbrwuuid());
        sjjhDTO.setDjxh(sbDTO.getDjxh());
        sjjhDTO.setNsrsbh(sbDTO.getNsrsbh());
        sjjhDTO.setXzqhszDm(this.getXzqhszDm(sbDTO.getNsrsbh(), sbDTO.getDjxh()));
        sjjhDTO.setBwnr(ywbw);
        CommonResult<Object> sbbcRes = sjjhService.saveSjjhJob(sjjhDTO);
        log.info("财行税提交申报保存返回报文:{}", sbbcRes);
        if (!sbbcRes.isSuccess()) {
            return handleErrorResponse(sbbcRes, responseVO);
        } else {
            // 更新申报任务中的应补退税额
            this.updateYbtse(sbDTO);

            // 直接申报
            CxsCxsjcljgResponseDTO respDTO = BeanUtils.toBean(sbbcRes.getData(),CxsCxsjcljgResponseDTO.class);
            responseVO.setPch(respDTO.getPclsh());
            return CommonResult.success(responseVO);
        }
    }

    /**
     * 更新申报任务中的应补退税额
     *
     * @param sbDTO 申报信息的数据传输对象，包含申报所需的各项信息
     */
    private void updateYbtse(CchxwsSbDTO sbDTO) {
        LambdaUpdateWrapper<ZnsbNssbSbrwDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(ZnsbNssbSbrwDO::getYbtse,sbDTO.getYbtse())
                .eq(ZnsbNssbSbrwDO::getSbrwuuid,sbDTO.getSbrwuuid());
        znsbNssbSbrwMapper.update(lambdaUpdateWrapper);
    }

    /**
     * 处理错误响应
     *
     * 该方法旨在根据传入的错误响应类型，构建并返回一个包含错误信息的CommonResult对象
     * 它通过检查响应数据的类型，来决定如何填充错误信息到响应VO中
     *
     * @param sbbcRes 包含原始错误响应数据的CommonResult对象
     * @param responseVO 响应视图对象，用于携带错误信息
     * @return 包含错误信息的CommonResult对象
     */
    private CommonResult<SbtjResponseVO> handleErrorResponse(CommonResult<Object> sbbcRes, SbtjResponseVO responseVO) {
        Object data = sbbcRes.getData();
        if (data instanceof Exception) {
            final Exception e = (Exception) data;
            responseVO.setCode(StrUtil.toString(GlobalErrorCodeConstants.ERROR.getCode()));
            responseVO.setMsg(e.getMessage());
            return CommonResult.error(GlobalErrorCodeConstants.ERROR.getCode(), e.getMessage(), responseVO);
        } else if (data instanceof SbbcGyRespDTO) {
            final SbbcGyRespDTO respDTO = (SbbcGyRespDTO) data;
            responseVO.setCode(StrUtil.toString(GlobalErrorCodeConstants.ERROR.getCode()));
            responseVO.setMsg(respDTO.getSbyysm());
            return CommonResult.error(GlobalErrorCodeConstants.ERROR.getCode(), respDTO.getSbyysm(), responseVO);
        } else {
            log.error("未知类型的响应数据: {}", data);
            responseVO.setCode(StrUtil.toString(GlobalErrorCodeConstants.ERROR.getCode()));
            responseVO.setMsg("未知错误");
            return CommonResult.error(GlobalErrorCodeConstants.ERROR.getCode(), "未知错误", responseVO);
        }
    }

    /**
     * 根据申报任务UUID获取人员信息
     * 当批量申报时，由于异步线程可能导致session丢失，此时通过申报任务UUID查询相关信息
     *
     * @param sbrwuuid 申报任务UUID
     * @return 返回人员信息对象，如果没有找到相关信息，则返回null
     */
    private RyxxVO getRyxx(String sbrwuuid) {
        RyxxVO ryxxVO = null;
        String bsrxm = ZnsbSessionUtils.getZsxm();
        String sfzjlxDm = ZnsbSessionUtils.getSfzjlx();
        String sfzjhm = ZnsbSessionUtils.getSfzjhm();
        if (GyUtils.isNull(bsrxm) || GyUtils.isNull(sfzjlxDm) || GyUtils.isNull(sfzjhm)) {
            // 批量申报，异步线程导致session丢失，通过申报任务uuid查询
            ZnsbNssbSbrwDO sbrw = sbrwService.querySbrwBySbrwuuid(sbrwuuid);
            if (GyUtils.isNotNull(sbrw)) {
                String djxh = sbrw.getDjxh();
                DjxhReqVO reqVO = new DjxhReqVO();
                reqVO.setDjxh(djxh);
                CommonResult<List<RyxxVO>> result = companyApi.getBsyByQy(reqVO);
                if (result.isSuccess() || GyUtils.isNotNull(result.getData())) {
                    if (GyUtils.isNotNull(sbrw.getBsy())) {
                        ryxxVO = CollectionUtil.findOneByField(result.getData(), "zsxm1", sbrw.getBsy());
                    }
                    if (GyUtils.isNull(ryxxVO)) {
                        ryxxVO = result.getData().get(0);
                    }
                }
            }
        } else {
            ryxxVO = new RyxxVO();
            ryxxVO.setZsxm1(bsrxm);
            ryxxVO.setSfzjlx(sfzjlxDm);
            ryxxVO.setSfzjhm(sfzjhm);
        }
        return ryxxVO;
    }

    /**
     * 获取行政区划数字代码
     *
     * @param nsrsbh 纳税人识别号
     * @param djxh 登记序号
     * @return {@link String}
     */
    private String getXzqhszDm(String nsrsbh, String djxh) {
        CommonResult<CompanyBasicInfoDTO> result = companyApi.basicInfo(djxh, nsrsbh);
        if (result.isSuccess() && GyUtils.isNotNull(result.getData())) {
            return result.getData().getXzqhszDm();
        }
        final ZnsbMhzcQyjbxxmxReqVO nsrreqVO = new ZnsbMhzcQyjbxxmxReqVO();
        nsrreqVO.setNsrsbh(nsrsbh);
        nsrreqVO.setDjxh(djxh);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByNsrsbh(nsrreqVO);
        final JbxxmxsjVO jbxxmxsjVO = nsrxxRes.getData().getJbxxmxsj().get(0);
        String xzqhszDm = jbxxmxsjVO.getScjydzxzqhszDm();
        if (GyUtils.isNull(xzqhszDm)) {
            xzqhszDm = jbxxmxsjVO.getZcdzxzqhszDm();
        }
        xzqhszDm = SfEnum.getSsjXzqhszDmByXzqhszDm(xzqhszDm);
        return xzqhszDm;
    }

    /**
     * 查询暂存信息
     *
     * @param lcslid 流程实例ID
     * @param sbrwuuid 申报任务UUID
     * @return {@link ZzsybnsrZcxxYwbwDTO}
     */
    private ZnsbNssbZcxxDTO queryZcxx(String lcslid, String sbrwuuid) {
        ZnsbNssbZcxxDTO zcxxDTO = null;
        if (GyUtils.isNotNull(lcslid)) {
            zcxxDTO = zcxxService.queryZcxxByLcsjid(lcslid);
        } else if (GyUtils.isNotNull(sbrwuuid)) {
            zcxxDTO = zcxxService.queryZcxxBySbrwuuid(sbrwuuid);
        }
        if (GyUtils.isNotNull(zcxxDTO) && GyUtils.isNotNull(zcxxDTO.getBusinessclob())) {
            return zcxxDTO;
        } else {
            return null;
        }
    }

    /**
     * 推送工作流
     *
     * @param cchxwsSbDTO 申报提交请求
     * @param zcxxYwbwDTO 暂存信息业务报文
     * @return {@link String}
     */
    private String pushGzl(CchxwsSbDTO cchxwsSbDTO, ZnsbNssbZcxxDTO zcxxYwbwDTO) {
        final GySbGzlStartReqDTO gzlStartReqDTO = new GySbGzlStartReqDTO();
        gzlStartReqDTO.setDjxh(cchxwsSbDTO.getDjxh());
        gzlStartReqDTO.setNsrsbh(cchxwsSbDTO.getNsrsbh());
        final DateTime skssqq = DateUtil.parse(cchxwsSbDTO.getSkssqq(), DatePattern.NORM_DATE_PATTERN);
        gzlStartReqDTO.setSkssqq(skssqq);
        final DateTime skssqz = DateUtil.parse(cchxwsSbDTO.getSkssqz(), DatePattern.NORM_DATE_PATTERN);
        gzlStartReqDTO.setSkssqz(skssqz);
        gzlStartReqDTO.setBusinessclob(JsonUtils.toJson(zcxxYwbwDTO.getBusinessclob()));
        gzlStartReqDTO.setSbrwuuid(cchxwsSbDTO.getSbrwuuid());
        gzlStartReqDTO.setAddSbrwBz(YesOrNoEnum.N.getCode());
        gzlStartReqDTO.setYzpzzlDm(YzpzzlEnum.CXS.getDm());
        gzlStartReqDTO.setUfRwzt(YzpzzlEnum.CXS.getMc() + "申报（" + com.css.znsb.framework.common.util.date.DateUtils.dateToString(skssqq, 10) + "-"
                + DateUtils.dateToString(skssqz, 10) + "）申请");
        String sfqygzl = "N";
        try {
            final CommonResult<GySbGzlStartRespDTO> gySbGzlStartRespDTOCommonResult =
                    sbGyGzlAPI.startGzl(gzlStartReqDTO);
            if (gySbGzlStartRespDTOCommonResult.isSuccess()
                    && GyUtils.isNotNull(gySbGzlStartRespDTOCommonResult.getData())) {
                sfqygzl = gySbGzlStartRespDTOCommonResult.getData().getSfqyGzl();
            }
        } catch (Exception e) {
            log.error("工作流启动报错，报错信息：", e);
            log.error("直接进行申报");
        }
        // 记录是否启用工作流
        return sfqygzl;
    }

    /**
     * 获取六税两费减免信息
     * @param skssqq
     * @param skssqz
     * @param nsrzgxxVO
     * @param nsrbqxxVOList
     */
    @Override
    public CxsLslfJmxxDTO getLslfJmxx(String skssqq, String skssqz, NsrzgxxVO nsrzgxxVO, List<NsrbqxxVO> nsrbqxxVOList) {

        // 判断是否为跨区税源户,跨区税源户取主户的标签信息和纳税人资格信息
        final CompanyBasicInfoDTO basicInfoDTO = companyApi.basicInfo(nsrzgxxVO.getDjxh(),nsrzgxxVO.getNsrsbh()).getData();
        if ("3".equals(basicInfoDTO.getQylxz())){
            final List<CompanyDTO> companyDTOList = companyApi.getjgxxsByNsrsbh(nsrzgxxVO.getNsrsbh()).getData();
            // 筛选出 qylxz 为 1 的主户数据
            CompanyDTO zhCompanyDTO = companyDTOList.stream()
                    .filter(company -> "1".equals(company.getQylxz()))
                    .collect(Collectors.toList()).get(0);
            
            final ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(zhCompanyDTO.getDjxh());
            ZnsbMhzcQyjbxxmxResVO qyjbxxmxResVO = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO).getData();
            nsrzgxxVO = qyjbxxmxResVO.getNsrzgxx().get(0);
            nsrbqxxVOList = qyjbxxmxResVO.getNsrbqxx();
        }

        final CxsLslfJmxxDTO cxsLslfJmxxDTO = new CxsLslfJmxxDTO();
        Date yxqq = nsrzgxxVO.getYxqq();
        Date yxqz = nsrzgxxVO.getYxqz();
        Date dateSkssqq = DateUtil.parse(skssqq, "yyyy-MM-dd");
        Date dateSkssqz = DateUtil.parse(skssqz, "yyyy-MM-dd");
        String jzzcsyzt = "";
        Date xgmjzzcqssj = null;
        Date xgmjzzczssj = null;

        /*
          1：一般纳税人
          2：小规模纳税人
          3：辅导期一般纳税人
          4：逾期未认定一般纳税人
          5：其它
          **/
        log.info("六税两费减免----登记序号: {}, 纳税人类型: {}",nsrzgxxVO.getDjxh(),nsrzgxxVO.getNsrlx());
        if ("1".equals(nsrzgxxVO.getNsrlx())) {
            for (NsrbqxxVO nsrbqxxVO : nsrbqxxVOList) {
                if (GyUtils.isNotNull(nsrbqxxVO) && GyUtils.isNotNull(nsrbqxxVO.getBqmc()) && nsrbqxxVO.getBqmc().equals("xwqy")) {
                    yxqq = nsrbqxxVO.getYxqq();
                    yxqz = nsrbqxxVO.getYxqz();
                    jzzcsyzt = "21";
                    break;
                } else if (GyUtils.isNotNull(nsrbqxxVO) && GyUtils.isNotNull(nsrbqxxVO.getBqmc()) && nsrbqxxVO.getBqmc().equals("gtgsh")) {
                    yxqq = nsrbqxxVO.getYxqq();
                    yxqz = nsrbqxxVO.getYxqz();
                    jzzcsyzt = "22";
                }
            }

            if (GyUtils.isNull(jzzcsyzt)) {
                // 分支机构查询总机构是否为小微企业
                Map<String,String> xwqyMap = zfjgxxService.getZjgXwqy(nsrzgxxVO.getNsrsbh(),nsrzgxxVO.getDjxh());
                String xwqybz = xwqyMap.get("xwqy");
                if ("Y".equals(xwqybz)){
                    jzzcsyzt = "21";
                    try {
                        yxqq = com.alibaba.excel.util.DateUtils.parseDate(xwqyMap.get("yxqq"), "yyyy-MM-dd");
                        yxqz = com.alibaba.excel.util.DateUtils.parseDate(xwqyMap.get("yxqz"), "yyyy-MM-dd");
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

            //判断税款所属期止是否在有效期前
            if (dateSkssqz.before(yxqq) && !dateSkssqq.equals(yxqq)){
                cxsLslfJmxxDTO.setBqsfsyzzsxgmnsrjzzc("N");
                return cxsLslfJmxxDTO;
            }

            if (GyUtils.isNotNull(jzzcsyzt)) {
                xgmjzzcqssj = getLatestDate(dateSkssqq,yxqq);
                xgmjzzczssj = getEarliestDate(dateSkssqz,yxqz);
            }
        } else if ("2".equals(nsrzgxxVO.getNsrlx())) {
            jzzcsyzt = "11";
        }

        if (GyUtils.isNull(jzzcsyzt)) {
            cxsLslfJmxxDTO.setBqsfsyzzsxgmnsrjzzc("N");
            return cxsLslfJmxxDTO;
        }

        cxsLslfJmxxDTO.setXgmjzzcqssj(GyUtils.isNotNull(xgmjzzcqssj)?DateUtil.format(xgmjzzcqssj, "yyyy-MM-dd") : null);
        cxsLslfJmxxDTO.setXgmjzzczzsj(GyUtils.isNotNull(xgmjzzczssj)?DateUtil.format(xgmjzzczssj, "yyyy-MM-dd") : null);
        cxsLslfJmxxDTO.setJzzcsyztDm(jzzcsyzt);
        cxsLslfJmxxDTO.setBqsfsyzzsxgmnsrjzzc("Y");
        return cxsLslfJmxxDTO;
    }

    // 获取两个日期中的较小值（即较早的日期）
    private static Date getEarliestDate(Date date1, Date date2) {
        return date1.before(date2) ? date1 : date2;
    }

    // 获取两个日期中的较大值（即较晚的日期）
    private static Date getLatestDate(Date date1, Date date2) {
        return date1.after(date2) ? date1 : date2;
    }
}