package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
/**
 * 《增值税（一般纳税人）》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ZzsybsbSbbdxxVO", propOrder = { "zzssyyybnsrZb", "zzssyyybnsr01Bqxsqkmxb", "zzssyyybnsr02Bqjxsemxb", "zzssyyybnsr03Ysfwkcxmmx", "zzssyyybnsr04Bqjxsemxb", "zzssyyybnsr05Bdcfqdkjsb", "zzssyyybnsrZzsybnsrbqynseayskmfjb", "zzsybnsrsbGdzcjxsedkqkb", "hqysqyfzjgcdd", "zzsybnsrsbNdhkysqyqsb", "zzssyyybnsrCpygxcqkmxb", "zzssyyybnsrCpygxcslmxb", "zzssyyybnsrJdclscqyxsmxb", "zzssyyybnsrJdclscqyxsqktjb", "zzssyyybnsrJdcxstyfplycybb", "zzssyyybnsrJdcxstyfpqd", "zzssyyybnsrJdcljxqyxsmxb", "dlqyzzsxxsehjxsecdd", "zzssyyybnsrZzsysfpdkqd", "hgwspzdklsjcjb", "zzsybnsrsbNcphdkczzsjxsejsb", "zzssyyybnsrHqysqyfzjgcdd", "zzsybnsrsbCbfhdccpzzsjxsejsb", "zzssyyybnsrGjncpzjxshdncpzzsjxsejsb", "zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb", "zzsybnsrsbJyzyxsyphzb", "zzssyyybnsrJyzyfjyxxmxb", "zzsybnsrsbDkdjsstyjksdkqd", "zzssyyybnsrHznsqytycdd", "zzssyyybnsrHznsqyzzsfpb", "zzssyyybnsrKstlqyzzsfpb", "zzsybnsrsbYzqyfzjgzzshznsxxcdd", "zzsybnsrsbTlysqyfzjgzzshznsxxcdd", "zzsybnsrsbDxqyfzjgzzshznsxxcdd", "zzsfjssb", "zzssyyybnsrBfcpxstjb", "zzsjmssbmxb", "zzssyyybnsrYqtqyzzsfpb", "zzssyyybnsrBqdkjxsejgmxb", "zzssyyybnsrTljsjjnssbb", "zzssyyybnsrYgzsffxcsmxb" })
@Getter
@Setter
public class ZzsybsbSbbdxxVO {
    /**
     * 增值税纳税申报表（一般纳税人适用）
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_zb", required = true)
    @JSONField(name = "zzssyyybnsr_zb")
    protected ZzssyyybnsrZb zzssyyybnsrZb;

    /**
     * 增值税纳税申报表附列资料一（本期销售情况明细）》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr01_bqxsqkmxb", required = true)
    @JSONField(name = "zzssyyybnsr01_bqxsqkmxb")
    protected Zzssyyybnsr01Bqxsqkmxb zzssyyybnsr01Bqxsqkmxb;

    /**
     * 增值税纳税申报表附列资料二（本期进项税额明细）
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr02_bqjxsemxb", required = true)
    @JSONField(name = "zzssyyybnsr02_bqjxsemxb")
    protected Zzssyyybnsr02Bqjxsemxb zzssyyybnsr02Bqjxsemxb;

    /**
     * 增值税纳税申报表附列资料三（应税服务扣除项目明细）
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr03_ysfwkcxmmx", required = true)
    @JSONField(name = "zzssyyybnsr03_ysfwkcxmmx")
    protected Zzssyyybnsr03Ysfwkcxmmx zzssyyybnsr03Ysfwkcxmmx;

    /**
     * 增值税纳税申报表附列资料四（税额抵减情况表）
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr04_bqjxsemxb", required = true)
    @JSONField(name = "zzssyyybnsr04_bqjxsemxb")
    protected Zzssyyybnsr04Bqjxsemxb zzssyyybnsr04Bqjxsemxb;

    /**
     * 增值税纳税申报表附列资料五（不动产分期抵扣计算表）
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr05_bdcfqdkjsb", required = true)
    @JSONField(name = "zzssyyybnsr05_bdcfqdkjsb")
    protected Zzssyyybnsr05Bdcfqdkjsb zzssyyybnsr05Bdcfqdkjsb;

    /**
     * 增值税一般纳税人本期应纳税额按预算科目分解表
     */
    @XmlElement(name = "zzssyyybnsr_zzsybnsrbqynseayskmfjb")
    @JSONField(name = "zzssyyybnsr_zzsybnsrbqynseayskmfjb")
    protected ZzssyyybnsrZzsybnsrbqynseayskmfjb zzssyyybnsrZzsybnsrbqynseayskmfjb;

    /**
     * 固定资产进项税额抵扣情况表
     */
    @XmlElement(name = "zzsybnsrsb_gdzcjxsedkqkb")
    @JSONField(name = "zzsybnsrsb_gdzcjxsedkqkb")
    protected ZzsybnsrsbGdzcjxsedkqkb zzsybnsrsbGdzcjxsedkqkb;

    /**
     * 航空运输企业分支机构传递单
     */
    protected Hqysqyfzjgcdd hqysqyfzjgcdd;

    /**
     * 年度航空运输企业年度清算表
     */
    @XmlElement(name = "zzsybnsrsb_ndhkysqyqsb")
    @JSONField(name = "zzsybnsrsb_ndhkysqyqsb")
    protected ZzsybnsrsbNdhkysqyqsb zzsybnsrsbNdhkysqyqsb;

    /**
     * 成品油购销存情况明细表
     */
    @XmlElement(name = "zzssyyybnsr_cpygxcqkmxb")
    @JSONField(name = "zzssyyybnsr_cpygxcqkmxb")
    protected ZzssyyybnsrCpygxcqkmxb zzssyyybnsrCpygxcqkmxb;

    /**
     * 成品油购销存数量明细表
     */
    @XmlElement(name = "zzssyyybnsr_cpygxcslmxb")
    @JSONField(name = "zzssyyybnsr_cpygxcslmxb")
    protected ZzssyyybnsrCpygxcslmxb zzssyyybnsrCpygxcslmxb;

    /**
     * 机动车辆生产企业销售明细表
     */
    @XmlElement(name = "zzssyyybnsr_jdclscqyxsmxb")
    @JSONField(name = "zzssyyybnsr_jdclscqyxsmxb")
    protected ZzssyyybnsrJdclscqyxsmxb zzssyyybnsrJdclscqyxsmxb;

    /**
     * 机动车辆生产企业销售情况统计表
     */
    @XmlElement(name = "zzssyyybnsr_jdclscqyxsqktjb")
    @JSONField(name = "zzssyyybnsr_jdclscqyxsqktjb")
    protected ZzssyyybnsrJdclscqyxsqktjb zzssyyybnsrJdclscqyxsqktjb;

    /**
     * 机动车销售统一发票领用存月报表
     */
    @XmlElement(name = "zzssyyybnsr_jdcxstyfplycybb")
    @JSONField(name = "zzssyyybnsr_jdcxstyfplycybb")
    protected ZzssyyybnsrJdcxstyfplycybb zzssyyybnsrJdcxstyfplycybb;

    /**
     * 机动车销售统一发票清单
     */
    @XmlElement(name = "zzssyyybnsr_jdcxstyfpqd")
    @JSONField(name = "zzssyyybnsr_jdcxstyfpqd")
    protected ZzssyyybnsrJdcxstyfpqd zzssyyybnsrJdcxstyfpqd;

    /**
     * 机动车辆经销企业销售明细表
     */
    @XmlElement(name = "zzssyyybnsr_jdcljxqyxsmxb")
    @JSONField(name = "zzssyyybnsr_jdcljxqyxsmxb")
    protected ZzssyyybnsrJdcljxqyxsmxb zzssyyybnsrJdcljxqyxsmxb;

    /**
     * 电力企业增值税销项税额和进项税额传递单
     */
    protected Dlqyzzsxxsehjxsecdd dlqyzzsxxsehjxsecdd;

    /**
     * 增值税运输发票抵扣清单
     */
    @XmlElement(name = "zzssyyybnsr_zzsysfpdkqd")
    @JSONField(name = "zzssyyybnsr_zzsysfpdkqd")
    protected ZzssyyybnsrZzsysfpdkqd zzssyyybnsrZzsysfpdkqd;

    /**
     * 海关完税凭证抵扣联数据采集申请单
     */
    protected Hgwspzdklsjcjb hgwspzdklsjcjb;

    /**
     * 农产品核定扣除增值税进项税额计算表（汇总表）
     */
    @XmlElement(name = "zzsybnsrsb_ncphdkczzsjxsejsb")
    @JSONField(name = "zzsybnsrsb_ncphdkczzsjxsejsb")
    protected ZzsybnsrsbNcphdkczzsjxsejsb zzsybnsrsbNcphdkczzsjxsejsb;

    /**
     * 投入产出法核定农产品增值税进项税额计算表
     */
    @XmlElement(name = "zzssyyybnsr_hqysqyfzjgcdd")
    @JSONField(name = "zzssyyybnsr_hqysqyfzjgcdd")
    protected ZzssyyybnsrHqysqyfzjgcdd zzssyyybnsrHqysqyfzjgcdd;

    /**
     * 成本法核定农产品增值税进项税额计算表
     */
    @XmlElement(name = "zzsybnsrsb_cbfhdccpzzsjxsejsb")
    @JSONField(name = "zzsybnsrsb_cbfhdccpzzsjxsejsb")
    protected ZzsybnsrsbCbfhdccpzzsjxsejsb zzsybnsrsbCbfhdccpzzsjxsejsb;

    /**
     * 购进农产品直接销售核定农产品增值税进项税额计算表
     */
    @XmlElement(name = "zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb")
    @JSONField(name = "zzssyyybnsr_gjncpzjxshdncpzzsjxsejsb")
    protected ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb zzssyyybnsrGjncpzjxshdncpzzsjxsejsb;

    /**
     * 购进农产品用于生产经营且不构成货物实体核定农产品增值税进项税额计算表
     */
    @XmlElement(name = "zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb")
    @JSONField(name = "zzssyyybnsr_gjncpyyscjyqbgchwsthdncpzzsjxsejsb")
    protected ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb zzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb;

    /**
     * 加油站月销售油品汇总表
     */
    @XmlElement(name = "zzsybnsrsb_jyzyxsyphzb")
    @JSONField(name = "zzsybnsrsb_jyzyxsyphzb")
    protected ZzsybnsrsbJyzyxsyphzb zzsybnsrsbJyzyxsyphzb;

    /**
     * 加油站月份加油信息明细表
     */
    @XmlElement(name = "zzssyyybnsr_jyzyfjyxxmxb")
    @JSONField(name = "zzssyyybnsr_jyzyfjyxxmxb")
    protected ZzssyyybnsrJyzyfjyxxmxb zzssyyybnsrJyzyfjyxxmxb;

    /**
     * 代扣代缴税收通用缴款书抵扣清单
     */
    @XmlElement(name = "zzsybnsrsb_dkdjsstyjksdkqd")
    @JSONField(name = "zzsybnsrsb_dkdjsstyjksdkqd")
    protected ZzsybnsrsbDkdjsstyjksdkqd zzsybnsrsbDkdjsstyjksdkqd;

    /**
     * 汇总纳税企业通用传递单
     */
    @XmlElement(name = "zzssyyybnsr_hznsqytycdd")
    @JSONField(name = "zzssyyybnsr_hznsqytycdd")
    protected ZzssyyybnsrHznsqytycdd zzssyyybnsrHznsqytycdd;

    /**
     * 汇总纳税企业增值税分配表
     */
    @XmlElement(name = "zzssyyybnsr_hznsqyzzsfpb")
    @JSONField(name = "zzssyyybnsr_hznsqyzzsfpb")
    protected ZzssyyybnsrHznsqyzzsfpb zzssyyybnsrHznsqyzzsfpb;

    /**
     * 跨省铁路企业增值税分配表
     */
    @XmlElement(name = "zzssyyybnsr_kstlqyzzsfpb")
    @JSONField(name = "zzssyyybnsr_kstlqyzzsfpb")
    protected ZzssyyybnsrKstlqyzzsfpb zzssyyybnsrKstlqyzzsfpb;

    /**
     * 邮政企业分支机构增值税汇总纳税信息传递单
     */
    @XmlElement(name = "zzsybnsrsb_yzqyfzjgzzshznsxxcdd")
    @JSONField(name = "zzsybnsrsb_yzqyfzjgzzshznsxxcdd")
    protected ZzsybnsrsbYzqyfzjgzzshznsxxcdd zzsybnsrsbYzqyfzjgzzshznsxxcdd;

    /**
     * 铁路运输企业分支机构增值税汇总纳税信息传递单
     */
    @XmlElement(name = "zzsybnsrsb_tlysqyfzjgzzshznsxxcdd")
    @JSONField(name = "zzsybnsrsb_tlysqyfzjgzzshznsxxcdd")
    protected ZzsybnsrsbTlysqyfzjgzzshznsxxcdd zzsybnsrsbTlysqyfzjgzzshznsxxcdd;

    /**
     * 电信企业分支机构增值税汇总纳税信息传递单
     */
    @XmlElement(name = "zzsybnsrsb_dxqyfzjgzzshznsxxcdd")
    @JSONField(name = "zzsybnsrsb_dxqyfzjgzzshznsxxcdd")
    protected ZzsybnsrsbDxqyfzjgzzshznsxxcdd zzsybnsrsbDxqyfzjgzzshznsxxcdd;

    /**
     * 附加税申报纳税人申报表
     */
    protected Zzsfjssb zzsfjssb;

    /**
     * 部分产品销售统计表
     */
    @XmlElement(name = "zzssyyybnsr_bfcpxstjb")
    @JSONField(name = "zzssyyybnsr_bfcpxstjb")
    protected ZzssyyybnsrBfcpxstjb zzssyyybnsrBfcpxstjb;

    /**
     * 增值税减免税申报明细表
     */
    @XmlElement(name = "zzsjmssbmxb")
    @JSONField(name = "zzsjmssbmxb")
    protected Zzsjmssbmxb zzsjmssbmxb;

    /**
     * 油气田企业增值税分配表
     */
    @XmlElement(name = "zzssyyybnsr_yqtqyzzsfpb")
    @JSONField(name = "zzssyyybnsr_yqtqyzzsfpb")
    protected ZzssyyybnsrYqtqyzzsfpb zzssyyybnsrYqtqyzzsfpb;

    /**
     * 本期抵扣进项税额结构明细表
     */
    @XmlElement(name = "zzssyyybnsr_bqdkjxsejgmxb")
    @JSONField(name = "zzssyyybnsr_bqdkjxsejgmxb")
    protected ZzssyyybnsrBqdkjxsejgmxb zzssyyybnsrBqdkjxsejgmxb;

    /**
     * 铁路建设基金纳税申报表
     */
    @XmlElement(name = "zzssyyybnsr_tljsjjnssbb")
    @JSONField(name = "zzssyyybnsr_tljsjjnssbb")
    protected ZzssyyybnsrTljsjjnssbb zzssyyybnsrTljsjjnssbb;

    /**
     * 营改增税负分析测算明细表
     */
    @XmlElement(name = "zzssyyybnsr_ygzsffxcsmxb")
    @JSONField(name = "zzssyyybnsr_ygzsffxcsmxb")
    protected ZzssyyybnsrYgzsffxcsmxb zzssyyybnsrYgzsffxcsmxb;
}