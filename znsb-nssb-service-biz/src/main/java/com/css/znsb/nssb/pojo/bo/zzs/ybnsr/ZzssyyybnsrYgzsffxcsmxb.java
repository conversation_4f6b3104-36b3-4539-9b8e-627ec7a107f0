package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《营改增税负分析测算明细表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_ygzsffxcsmxb", propOrder = { "sbbhead", "ygzsffxcsmxbGrid" })
@Getter
@Setter
public class ZzssyyybnsrYgzsffxcsmxb {
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 营改增税负分析测算明细表
     */
    @XmlElement(nillable = true, required = true)
    protected YgzsffxcsmxbGrid ygzsffxcsmxbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "ygzsffxcsmxbGridlbVO" })
    @Getter
    @Setter
    public static class YgzsffxcsmxbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<YgzsffxcsmxbGridlbVO> ygzsffxcsmxbGridlbVO;

        /**
         * Gets the value of the ygzsffxcsmxbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the ygzsffxcsmxbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getYgzsffxcsmxbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link YgzsffxcsmxbGridlbVO}
         */
        public List<YgzsffxcsmxbGridlbVO> getYgzsffxcsmxbGridlbVO() {
            if (ygzsffxcsmxbGridlbVO == null) {
                ygzsffxcsmxbGridlbVO = new ArrayList<YgzsffxcsmxbGridlbVO>();
            }
            return this.ygzsffxcsmxbGridlbVO;
        }
    }
}