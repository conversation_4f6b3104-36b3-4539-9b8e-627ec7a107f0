package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 申报纳税人申报保存提交其他信息对象
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBSBbcTjqtxxVO", propOrder = { "djxh", "zxbztzsuuid", "qzdbz", "scenceCs", "hyDm", "xzqhszDm", "jdxzDm", "zgswskfjDm", "sfyczb" })
@Getter
@Setter
public class SBSBbcTjqtxxVO {
    /**
     * 登记序号
     */
    @XmlElement(nillable = true, required = true)
    protected String djxh;

    /**
     * 自查补报通知书uuid
     */
    protected String zxbztzsuuid;

    /**
     * 起征点标志（Y未达起征点、N达到起征点，用于需前端判断起征点的场景）
     */
    protected String qzdbz;

    /**
     * 业务跳转口径（正常申报zcsb、申报错误更正sbcwgz、申报作废sbzf、申报导入sbdr、修改申报属期xgsq、修改申报类型xgsblx、网上申报wssb）
     */
    protected String scenceCs;

    /**
     * 行业代码，自然人时必须传入
     */
    protected String hyDm;

    /**
     * 行政区划数字代码，自然人时传入，用于过滤街道乡镇
     */
    protected String xzqhszDm;

    /**
     * 街道乡镇代码，自然人时传入
     */
    protected String jdxzDm;

    /**
     * 主管税务科所分局代码
     */
    protected String zgswskfjDm;

    /**
     * 是否异常转办(S 仅有提示性错误继续保存  Y 票表比对有强制错误的保存并启动异常转办 T 只比对不保存申报表)
     */
    protected String sfyczb;
}