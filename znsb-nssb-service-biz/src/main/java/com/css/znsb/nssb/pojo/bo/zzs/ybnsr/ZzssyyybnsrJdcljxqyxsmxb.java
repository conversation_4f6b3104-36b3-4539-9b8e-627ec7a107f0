package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车辆经销企业销售明细表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdcljxqyxsmxb", propOrder = { "sbbheadVO", "jdcljxqyxsmxbGrid" })
@Getter
@Setter
public class ZzssyyybnsrJdcljxqyxsmxb {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 机动车辆经销企业销售明细表
     */
    @XmlElement(nillable = true, required = true)
    protected JdcljxqyxsmxbGrid jdcljxqyxsmxbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "jdcljxqyxsmxbGridlbVO" })
    @Getter
    @Setter
    public static class JdcljxqyxsmxbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<JdcljxqyxsmxbGridlbVO> jdcljxqyxsmxbGridlbVO;

        /**
         * Gets the value of the jdcljxqyxsmxbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jdcljxqyxsmxbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getJdcljxqyxsmxbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link JdcljxqyxsmxbGridlbVO}
         */
        public List<JdcljxqyxsmxbGridlbVO> getJdcljxqyxsmxbGridlbVO() {
            if (jdcljxqyxsmxbGridlbVO == null) {
                jdcljxqyxsmxbGridlbVO = new ArrayList<JdcljxqyxsmxbGridlbVO>();
            }
            return this.jdcljxqyxsmxbGridlbVO;
        }
    }
}