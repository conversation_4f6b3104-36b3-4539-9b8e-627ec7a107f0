package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税纳税申报表附列资料四（税额抵减情况表）业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr04_bqjxsemxbywbw", propOrder = { "zzssyyybnsr04Bqjxsemxb" })
@Getter
@Setter
public class Zzssyyybnsr04Bqjxsemxbywbw extends TaxDoc {
    /**
     * 增值税纳税申报表附列资料四（税额抵减情况表）
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr04_bqjxsemxb", required = true)
    @JSONField(name = "zzssyyybnsr04_bqjxsemxb")
    protected Zzssyyybnsr04Bqjxsemxb zzssyyybnsr04Bqjxsemxb;
}