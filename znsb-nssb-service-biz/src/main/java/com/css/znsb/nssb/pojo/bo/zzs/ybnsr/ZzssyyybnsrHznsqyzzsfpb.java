package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《汇总纳税企业增值税分配表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_hznsqyzzsfpb", propOrder = { "sbbhead", "hznsqyzzsfpbGrid", "hznsqyzzsfpbForm", "slxx" })
@XmlSeeAlso({ ZzssyyybnsrHznsqyzzsfpbywbw.ZzssyyybnsrHznsqyzzsfpb.class })
@Getter
@Setter
public class ZzssyyybnsrHznsqyzzsfpb {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    @XmlElement(nillable = true, required = true)
    protected HznsqyzzsfpbGrid hznsqyzzsfpbGrid;

    /**
     * 汇总纳税企业增值税分配表
     */
    @XmlElement(nillable = true, required = true)
    protected HznsqyzzsfpbFormVO hznsqyzzsfpbForm;

    /**
     * 受理信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbslxxVO slxx;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "hznsqyzzsfpbGridlbVO" })
    @Getter
    @Setter
    public static class HznsqyzzsfpbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<HznsqyzzsfpbGridlbVO> hznsqyzzsfpbGridlbVO;

        /**
         * Gets the value of the hznsqyzzsfpbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the hznsqyzzsfpbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getHznsqyzzsfpbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link HznsqyzzsfpbGridlbVO}
         */
        public List<HznsqyzzsfpbGridlbVO> getHznsqyzzsfpbGridlbVO() {
            if (hznsqyzzsfpbGridlbVO == null) {
                hznsqyzzsfpbGridlbVO = new ArrayList<HznsqyzzsfpbGridlbVO>();
            }
            return this.hznsqyzzsfpbGridlbVO;
        }
    }
}