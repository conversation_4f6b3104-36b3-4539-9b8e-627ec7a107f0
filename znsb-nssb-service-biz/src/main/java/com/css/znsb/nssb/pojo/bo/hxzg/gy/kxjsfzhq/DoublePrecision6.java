package com.css.znsb.nssb.pojo.bo.hxzg.gy.kxjsfzhq;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.text.DecimalFormat;

/**
*
* @project 金税三期工程核心征管及应用总集成项目
* @package gov.gt3.vo.gy.kxjsfzhq
* @file DoublePrecision6.java 创建时间:2016-6-15下午08:23:50
* @title 外部服务VO对象Double返回值科学计数法转换器保留六位小数
* @description 外部服务VO返回值科学计数法转换器保留六位小数
* @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
* @company 中国软件与技术服务股份有限公司
* @module 模块: 模块名称
* <AUTHOR>
* @reviewer 审核人
* @version 1.0
* @history 修订历史（历次修订内容、修订人、修订时间等）
*
*/

public class DoublePrecision6 extends XmlAdapter<String, Double> {

	@Override
	public Double unmarshal(String str) throws Exception {
		return new Double(str);
	}

	@Override
	public String marshal(Double num) throws Exception {
		final DecimalFormat df = new DecimalFormat("##############0.000000");
		return df.format(num);
	}

}
