package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.*;

import lombok.Getter;
import lombok.Setter;
/**
 * 《成品油购销存情况明细表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_cpygxcqkmxb", propOrder = { "sbbheadVO", "cpygxcqkmxbGrid" })
@XmlSeeAlso({ ZzssyyybnsrCpygxcqkmxbywbw.ZzssyyybnsrCpygxcqkmxb.class })
@Getter
@Setter
public class ZzssyyybnsrCpygxcqkmxb {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 成品油购销存情况明细表
     */
    @XmlElement(nillable = true, required = true)
    protected CpygxcqkmxbGrid cpygxcqkmxbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "cpygxcqkmxbGridlbVO" })
    @Getter
    @Setter
    public static class CpygxcqkmxbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<CpygxcqkmxbGridlbVO> cpygxcqkmxbGridlbVO;

        /**
         * Gets the value of the cpygxcqkmxbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the cpygxcqkmxbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getCpygxcqkmxbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link CpygxcqkmxbGridlbVO}
         */
        public List<CpygxcqkmxbGridlbVO> getCpygxcqkmxbGridlbVO() {
            if (cpygxcqkmxbGridlbVO == null) {
                cpygxcqkmxbGridlbVO = new ArrayList<CpygxcqkmxbGridlbVO>();
            }
            return this.cpygxcqkmxbGridlbVO;
        }
    }
}