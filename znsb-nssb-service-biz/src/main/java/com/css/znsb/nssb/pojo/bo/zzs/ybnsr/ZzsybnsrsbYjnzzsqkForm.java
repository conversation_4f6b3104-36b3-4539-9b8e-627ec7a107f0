package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《已缴纳增值税情况》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_yjnzzsqkForm", propOrder = { "dbsdwllywcjjrqdsrje", "qtyzfwDlsdwllywBz", "qtyzfwDlsdwllywYdk", "qtyzfwDlsdwllywXse", "qtyzfwDlsdwllywYzl", "qtyzfwDlsdwllywYjse", "qtyzfwDlsdwllywCbxse", "qtyzfwDlsdwllywSysl", "qtyzfwDlsdwllywCbse", "qtyzfwDljrbxywBz", "qtyzfwDljrbxywYdk", "qtyzfwDljrbxywXse", "qtyzfwDljrbxywYzl", "qtyzfwDljrbxywYjse", "qtyzfwDljrbxywCbxse", "qtyzfwDljrbxywSysl", "qtyzfwDljrbxywCbse", "yztsfwBz", "yztsfwYdk", "yztsfwXse", "yztsfwYzl", "yztsfwYjse", "yztsfwCbxse", "yztsfwSysl", "yztsfwCbse", "yzpbfwBz", "yzpbfwYdk", "yzpbfwXse", "yzpbfwYzl", "yzpbfwYjse", "yzpbfwCbxse", "yzpbfwSysl", "yzpbfwCbse", "qtBz", "qtCbse", "qtSysl", "qtCbxse", "qtYjse", "qtYzl", "qtYdk", "qtXse", "ypxsBz", "ypxsCbse", "ypxsSysl", "ypxsCbxse", "ypxsYjse", "ypxsYzl", "ypxsYdk", "ypxsXse", "zgswjgDm" })
@Getter
@Setter
public class ZzsybnsrsbYjnzzsqkForm {
    /**
     * 代办速递物流类业务从寄件人取得的收入金额
     */
    protected BigDecimal dbsdwllywcjjrqdsrje;

    /**
     * 其他邮政服务—代理速递物流类业务备注
     */
    protected String qtyzfwDlsdwllywBz;

    /**
     * 其他邮政服务—代理速递物流类业务预订款
     */
    protected BigDecimal qtyzfwDlsdwllywYdk;

    /**
     * 其他邮政服务—代理速递物流类业务销售额
     */
    protected BigDecimal qtyzfwDlsdwllywXse;

    /**
     * 其他邮政服务—代理速递物流类业务预征率
     */
    protected BigDecimal qtyzfwDlsdwllywYzl;

    /**
     * 其他邮政服务—代理速递物流类业务预缴税额
     */
    protected BigDecimal qtyzfwDlsdwllywYjse;

    /**
     * 其他邮政服务—代理速递物流类业务查补销售额
     */
    protected BigDecimal qtyzfwDlsdwllywCbxse;

    /**
     * 其他邮政服务—代理速递物流类业务适用税率
     */
    protected BigDecimal qtyzfwDlsdwllywSysl;

    /**
     * 其他邮政服务—代理速递物流类业务查补税额
     */
    protected BigDecimal qtyzfwDlsdwllywCbse;

    /**
     * 其他邮政服务—代理金融保险业务备注
     */
    protected String qtyzfwDljrbxywBz;

    /**
     * 其他邮政服务—代理金融保险业务预订款
     */
    protected BigDecimal qtyzfwDljrbxywYdk;

    /**
     * 其他邮政服务—代理金融保险业务销售额
     */
    protected BigDecimal qtyzfwDljrbxywXse;

    /**
     * 其他邮政服务—代理金融保险业务z预征率
     */
    protected BigDecimal qtyzfwDljrbxywYzl;

    /**
     * 其他邮政服务—代理金融保险业务预缴税额
     */
    protected BigDecimal qtyzfwDljrbxywYjse;

    /**
     * 其他邮政服务—代理金融保险业务查补销售额
     */
    protected BigDecimal qtyzfwDljrbxywCbxse;

    /**
     * 其他邮政服务—代理金融保险业务适用税率
     */
    protected BigDecimal qtyzfwDljrbxywSysl;

    /**
     * 其他邮政服务—代理金融保险业务查补税额
     */
    protected BigDecimal qtyzfwDljrbxywCbse;

    /**
     * 邮政特殊服务备注
     */
    protected String yztsfwBz;

    /**
     * 邮政特殊服务预订款
     */
    protected BigDecimal yztsfwYdk;

    /**
     * 邮政特殊服务销售额
     */
    protected BigDecimal yztsfwXse;

    /**
     * 邮政特殊服务预征率
     */
    protected BigDecimal yztsfwYzl;

    /**
     * 邮政特殊服务预缴税额
     */
    protected BigDecimal yztsfwYjse;

    /**
     * 邮政特殊服务查补销售额
     */
    protected BigDecimal yztsfwCbxse;

    /**
     * 邮政特殊服务适用税率
     */
    protected BigDecimal yztsfwSysl;

    /**
     * 邮政特殊服务查补税额
     */
    protected BigDecimal yztsfwCbse;

    /**
     * 邮政普遍服务备注
     */
    protected String yzpbfwBz;

    /**
     * 邮政普遍服务预订款
     */
    protected BigDecimal yzpbfwYdk;

    /**
     * 邮政普遍服务销售额
     */
    protected BigDecimal yzpbfwXse;

    /**
     * 邮政普遍服务预征率
     */
    protected BigDecimal yzpbfwYzl;

    /**
     * 邮政普遍服务预缴税额
     */
    protected BigDecimal yzpbfwYjse;

    /**
     * 邮政普遍服务查补销售额
     */
    protected BigDecimal yzpbfwCbxse;

    /**
     * 邮政普遍服务适用税率
     */
    protected BigDecimal yzpbfwSysl;

    /**
     * 邮政普遍服务查补税额
     */
    protected BigDecimal yzpbfwCbse;

    /**
     * 其他备注
     */
    protected String qtBz;

    /**
     * 其他查补税额
     */
    protected BigDecimal qtCbse;

    /**
     * 其他适用税率%
     */
    protected BigDecimal qtSysl;

    /**
     * 其他查补销售额
     */
    protected BigDecimal qtCbxse;

    /**
     * 其他预缴税额
     */
    protected BigDecimal qtYjse;

    /**
     * 其他预征率%
     */
    protected BigDecimal qtYzl;

    /**
     * 其他预订款
     */
    protected BigDecimal qtYdk;

    /**
     * 其他销售额
     */
    protected BigDecimal qtXse;

    /**
     * 邮品销售备注
     */
    protected String ypxsBz;

    /**
     * 邮品销售查补税额
     */
    protected BigDecimal ypxsCbse;

    /**
     * 邮品销售适用税率%
     */
    protected BigDecimal ypxsSysl;

    /**
     * 邮品销售查补销售额
     */
    protected BigDecimal ypxsCbxse;

    /**
     * 邮品销售预缴税额
     */
    protected BigDecimal ypxsYjse;

    /**
     * 邮品销售预征率%
     */
    protected BigDecimal ypxsYzl;

    /**
     * 邮品销售预订款
     */
    protected BigDecimal ypxsYdk;

    /**
     * 邮品销售销售额
     */
    protected BigDecimal ypxsXse;

    /**
     * 主管税务机关代码
     */
    protected String zgswjgDm;
}