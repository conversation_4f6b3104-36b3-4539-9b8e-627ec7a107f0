package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《电力企业增值税销项税额和进项税额传递单》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dlqyzzsxxsehjxsecdd", propOrder = { "sbbhead", "xxGrid", "jxGrid", "skzsForm" })
@Getter
@Setter
public class Dlqyzzsxxsehjxsecdd {
    /**
     * 申报表表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 销项信息
     */
    @XmlElement(nillable = true, required = true)
    protected XxGrid xxGrid;

    /**
     * 进项信息
     */
    @XmlElement(nillable = true, required = true)
    protected JxGrid jxGrid;

    /**
     * 税款征收信息
     */
    @XmlElement(nillable = true, required = true)
    protected SkzsFormVO skzsForm;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "jxGridlbVO" })
    @Getter
    @Setter
    public static class JxGrid {
        @XmlElement(nillable = true, required = true)
        protected List<JxGridlbVO> jxGridlbVO;

        /**
         * Gets the value of the jxGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jxGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getJxGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link JxGridlbVO}
         */
        public List<JxGridlbVO> getJxGridlbVO() {
            if (jxGridlbVO == null) {
                jxGridlbVO = new ArrayList<JxGridlbVO>();
            }
            return this.jxGridlbVO;
        }
    }

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "xxGridlbVO" })
    @Getter
    @Setter
    public static class XxGrid {
        @XmlElement(nillable = true, required = true)
        protected List<XxGridlbVO> xxGridlbVO;

        /**
         * Gets the value of the xxGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the xxGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getXxGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link XxGridlbVO}
         */
        public List<XxGridlbVO> getXxGridlbVO() {
            if (xxGridlbVO == null) {
                xxGridlbVO = new ArrayList<XxGridlbVO>();
            }
            return this.xxGridlbVO;
        }
    }
}