package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 申报纳税人申报保存提交其他信息对象
 * 
 * <p>Java class for SBSBbcTjqtxxVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBSBbcTjqtxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="zxbztzsuuid" type="{http://www.chinatax.gov.cn/dataspec/}zxbztzsuuid" minOccurs="0"/>
 *         &lt;element name="qzdbz" type="{http://www.chinatax.gov.cn/dataspec/}bz1" minOccurs="0"/>
 *         &lt;element name="scenceCs" type="{http://www.chinatax.gov.cn/dataspec/}csbm" minOccurs="0"/>
 *         &lt;element name="hyDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm" minOccurs="0"/>
 *         &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm" minOccurs="0"/>
 *         &lt;element name="jdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm" minOccurs="0"/>
 *         &lt;element name="zgswskfjDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswskfjDm" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBSBbcTjqtxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "djxh",
    "zxbztzsuuid",
    "qzdbz",
    "scenceCs",
    "hyDm",
    "xzqhszDm",
    "jdxzDm",
    "zgswskfjDm",
    "sfyczb",
    "glqrResult",
    "xzjg",
    "jcdlbz",
    "gymjbz",
    "jjdjbz",
    "xjzzybz"
})
public class SBSBbcTjqtxxVO
    implements Serializable
{

    private final static long serialVersionUID = -8720762420527276032L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zxbztzsuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qzdbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String scenceCs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String hyDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xzqhszDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jdxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zgswskfjDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfyczb;
    /*@XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String glqrResult;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xzjg;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jcdlbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String gymjbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jjdjbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xjzzybz;*/

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the zxbztzsuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZxbztzsuuid() {
        return zxbztzsuuid;
    }

    /**
     * Sets the value of the zxbztzsuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZxbztzsuuid(String value) {
        this.zxbztzsuuid = value;
    }

    /**
     * Gets the value of the qzdbz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQzdbz() {
        return qzdbz;
    }

    /**
     * Sets the value of the qzdbz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQzdbz(String value) {
        this.qzdbz = value;
    }

    /**
     * Gets the value of the scenceCs property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getScenceCs() {
        return scenceCs;
    }

    /**
     * Sets the value of the scenceCs property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setScenceCs(String value) {
        this.scenceCs = value;
    }

    /**
     * Gets the value of the hyDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHyDm() {
        return hyDm;
    }

    /**
     * Sets the value of the hyDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHyDm(String value) {
        this.hyDm = value;
    }

    /**
     * Gets the value of the xzqhszDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }

    /**
     * Sets the value of the xzqhszDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhszDm(String value) {
        this.xzqhszDm = value;
    }

    /**
     * Gets the value of the jdxzDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJdxzDm() {
        return jdxzDm;
    }

    /**
     * Sets the value of the jdxzDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJdxzDm(String value) {
        this.jdxzDm = value;
    }

    /**
     * Gets the value of the zgswskfjDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    /**
     * Sets the value of the zgswskfjDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswskfjDm(String value) {
        this.zgswskfjDm = value;
    }

    /**
     * Gets the value of the sfyczb property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSfyczb() {
        return sfyczb;
    }
    
    /**
     * Sets the value of the sfyczb property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSfyczb(String value) {
        this.sfyczb = value;
    }

    /*public String getGlqrResult() {
        return glqrResult;
    }

    public void setGlqrResult(String glqrResult) {
        this.glqrResult = glqrResult;
    }

    public String getXzjg() {
        return xzjg;
    }

    public void setXzjg(String xzjg) {
        this.xzjg = xzjg;
    }

    public String getJcdlbz() {
        return jcdlbz;
    }

    public void setJcdlbz(String jcdlbz) {
        this.jcdlbz = jcdlbz;
    }

    public String getGymjbz() {
        return gymjbz;
    }

    public void setGymjbz(String gymjbz) {
        this.gymjbz = gymjbz;
    }

    public String getJjdjbz() {
        return jjdjbz;
    }

    public void setJjdjbz(String jjdjbz) {
        this.jjdjbz = jjdjbz;
    }

    public String getXjzzybz() {
        return xjzzybz;
    }

    public void setXjzzybz(String xjzzybz) {
        this.xjzzybz = xjzzybz;
    }*/
}

