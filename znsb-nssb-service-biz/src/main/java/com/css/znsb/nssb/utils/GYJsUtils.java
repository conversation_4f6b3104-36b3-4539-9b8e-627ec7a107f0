package com.css.znsb.nssb.utils;

import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.common.gy.util
 * @file GYJsUtils.java 创建时间:2014-8-9上午10:35:17
 * @title 公用计税工具类
 * @description 税率/征收率/应税所得率
 * 及征收项目、品目、子目列表等的过滤
 * 基本计税公式计算
 * 起征点计算、减免计算等
 * 取缓存等公共方法
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Slf4j
@Component
public class GYJsUtils {
//
////    /**
////     * @description cs_gy_glb_zsxm cs_gy_glb_zspm cs_gy_glb_zszm等按税务机关再存放一次的缓存对象（因缓存key大写转驼峰式小写 反复频繁调用耗性能而增加）
////     * @value value:swjgCsCache
////     */
////    private static final SwordMultiKeyMap<Object[]> swjgCsCache = new SwordLRUMap<Object[]>(1000).synchronizedMap();
//
//    //  RDSfzrdxxUtils.getZsxmListWithKtshgs
//    //  getZspmListWithKtshgs
//    //  ZSKtjyUtils.getYskmYsfpblSkgk
//
//    /**-----------------------------------------------------**/
//    /**----------方法区--------------------------------**/
//    /**-----------------------------------------------------**/
////    /**
////     *@name    获取征收项目列表
////     *@description 1、从cs_gy_glb_zsxm中查找，2、从会计公式中查找
////     *如只有参数表，代码表中无对应项，不显示名称
////     *@time    创建时间:2014-8-9上午11:07:30
////     *@param req 输入参数map
////     *@param useKtshgs 是否使用会统审核公式
////     *@return List<Map<String, Object>> 项目列表
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    @SuppressWarnings("unchecked")
////    public static List<Map<String, Object>> getZsxm(Map<String, Object> req, String useKtshgs) {
////        List<Map<String, Object>> rsList = new ArrayList<Map<String, Object>>();
////        //必要入参
////        final String swjgDm = (String) req.get("swjgDm");//税务机关代码
////        //以下为可选入参
////        Date skssqq = GYCastUtils.cast2Date(req.get(GYJsConstants.getcSkssqq()));//所属期起
////        Date skssqz = GYCastUtils.cast2Date(req.get(GYJsConstants.getcSkssqz()));//所属期止
////        final String djzclxDm = (String) req.get("djzclxDm");//登记注册类型代码
////        final String fxclbcBz = (String) req.get("fxclbcBz");//风险处理补偿标识
////        //当前日期
////        final Date nowdate = DateUtil.toDate(DateUtil.doDateFormat(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
////        //税务机关不能为空
////        if (swjgDm == null || swjgDm.trim().length() == 0) {
////            return rsList;
////        }
////        //期起期止如为空，默认为当前日期
////        if (skssqq == null || skssqz == null) {
////            skssqq = nowdate;
////            skssqz = nowdate;
////        }
////        //获取本级上级税务机关
////        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
////
////        //逐级上查，找到某级税务机关 有记录，则返回该级数据
////        List<Map<String, Object>> zsxmList = new ArrayList<Map<String, Object>>();
////        for (String bjsjSwjgDm : bjsjSwjgList) {
////            final List<Map<String, Object>> zsxmKvList = findListInCacheBySwjg(bjsjSwjgDm, GYJsConstants.gettCsZsxm());
////            if (zsxmKvList != null && zsxmKvList.size() > 0) {
////                //注意，缓存里面只过滤了 yxbz有效标志，没过滤选用标志
////
////                for (Map<String, Object> zsxmKvMap : zsxmKvList) {
////                    final Date yxqq = (Date) zsxmKvMap.get("yxqq");
////                    final Date yxqz = (Date) zsxmKvMap.get("yxqz");
////                    //判断有效期
////                    boolean yxq = false;
////                    //未传入所属期，或属期与有效期有交叉，则视为有效
////                    if (skssqq == null || skssqz == null) {
////                        yxq = true;
////                    } else if (GYCastUtils.sqAcross(skssqq, skssqz, yxqq, yxqz)) {
////                        yxq = true;
////                    }
////                    //只添加期内有效的
////                    if (yxq) {
////                        zsxmList.add(zsxmKvMap);
////                    }
////                }
////                if (GYCastUtils.notNull(zsxmList)) {
////                    break;//找到第一个有设置征收项目的税务机关，即终止，不再上溯
////                }
////            }
////        }
////        //再做一次基本过滤，选用标志等
////        //之所以要分两次过滤，是因为第一次要根据本级是否有记录来判断是否逐级上查。兼容本级全设选用标志=N而不逐级上查的情况
////        List<Map<String, Object>> tList = new ArrayList<Map<String, Object>>();
////        for (Map<String, Object> zsxmMap : zsxmList) {
////            if (zsxmMap != null && !zsxmMap.isEmpty()) {
////                final String yxbz = (String) zsxmMap.get("yxbz");
////                final String xybz = (String) zsxmMap.get("xybz");
////                if ("Y".equals(yxbz) && "Y".equals(xybz)) {
////                    tList.add(zsxmMap);
////                }
////            }
////        }
////        zsxmList = tList;
////        //根据会计公式再过滤出最终的征收项目列表
////        //传入前作数据转换
////        //是否使用会统审核公式
////        final String ktshgsKg = getXtcs("A0000001061001100", swjgDm);
////        if ("Y".equals(useKtshgs) && "Y".equals(ktshgsKg) && notNull(djzclxDm) && notNull(zsxmList)) {
////            List<Map<String, Object>> ktlist = new ArrayList<Map<String, Object>>();
////            for (Map<String, Object> zsxmMap : zsxmList) {
////                final Map<String, Object> codemap = new HashMap<String, Object>();
////                codemap.put("code", zsxmMap.get("zsxmDm"));
////                ktlist.add(codemap);
////            }
////            //调用纳税人管理组提供会统审核公式
////            ktlist = GYKtshgsUtils.getZsxmListWithKtshgs(djzclxDm, swjgDm, ktlist, skssqq, skssqz);
////            if (ktlist == null) {
////                ktlist = new ArrayList<Map<String, Object>>();
////            }
////            //用会统审核公式过滤的list对原list进行过滤
////            tList = new ArrayList<Map<String, Object>>();
////            for (Map<String, Object> zsxmMap : zsxmList) {
////                final String zsxmDm = (String) zsxmMap.get("zsxmDm");
////                for (Map<String, Object> ktmap : ktlist) {
////                    final String code = (String) ktmap.get("code");
////                    if (GYCastUtils.equal(zsxmDm, code)) {
////                        tList.add(zsxmMap);
////                        break;
////                    }
////                }
////            }
////            zsxmList = tList;
////        }
////
////        //排序，最终赋值
////        final SwordSortUtils.SortDescription description2 = new SwordSortUtils.SortDescription("zsxmDm", OrderType.ASC);
////        zsxmList = SwordSortUtils.sortMapList(zsxmList, description2);
////
////        //塞入名称
////        rsList = new ArrayList<Map<String, Object>>();
////        if (notNull(zsxmList)) {
////            if(GyUtils.isNull(fxclbcBz)){
////                for (Map<String, Object> newMap : zsxmList) {
////                    final Map<String, Object> zsxmmcMap = (Map<String, Object>) SwordCacheUtils.getFromKV("DM_GY_ZSXM", new Object[] { newMap.get("zsxmDm") });
////                    if (notNull(zsxmmcMap)) {
////                        newMap.put("code", newMap.get("zsxmDm"));
////                        newMap.put("caption", zsxmmcMap.get("ZSXMMC"));
////                        rsList.add(newMap);
////                    }
////                }
////            }
////            else{
////                List<Map<String, Object>> zsxmFxclList = FxGyUtils.getAllZsxmForFxclBc();
////                zsxmFxclList = SwordSortUtils.sortMapList(zsxmFxclList, description2);
////                for (Map<String, Object> newMap : zsxmList) {
////                    final String zsxmDm = (String) newMap.get("zsxmDm");
////                    for(Map<String, Object> newMapCp : zsxmFxclList) {
////                        final String zsxmDmCp = (String) newMapCp.get("zsxmDm");
////                        final String yxbz = (String)newMapCp.get("yxbz");
////                        final String xybz = (String)newMapCp.get("xybz");
////                        final String zsxmmc = (String)newMapCp.get("zsxmmc");
////                        if(zsxmDmCp.equalsIgnoreCase(zsxmDm)){
////                            newMap.put("code", newMap.get("zsxmDm"));
////                            if("N".equalsIgnoreCase(yxbz) || "N".equalsIgnoreCase(xybz)){
////                                newMap.put("caption", zsxmmc+"(失效)");
////                            }
////                            else{
////                                newMap.put("caption", zsxmmc);
////                            }
////                            rsList.add(newMap);
////                            break;
////                        }
////                    }
////                }
////            }
////        }
////        return rsList;
////    }
//
////    /**
////     *@name    获取征收品目列表
////     *@description 如只有参数表，代码表中无对应项，不显示名称
////     *@time    创建时间:2014-8-12下午08:11:23
////     *@param req 输入Map
////     *@param useKtshgs 是否使用会统审核公式
////     *@return 品目列表
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    @SuppressWarnings({ "unused", "unchecked" })
////
////    public static List<Map<String, Object>> getZspm(Map<String, Object> req, String useKtshgs) {
////        List<Map<String, Object>> rsList = new ArrayList<Map<String, Object>>();
////        final String swjgDm = (String) req.get("swjgDm");//税务机关代码
////        final String zsxmDm = (String) req.get("zsxmDm");//征收项目代码
////        //以下为可选入参
////        Date skssqq = GYCastUtils.cast2Date(req.get(GYJsConstants.getcSkssqq()));//所属期起
////        Date skssqz = GYCastUtils.cast2Date(req.get(GYJsConstants.getcSkssqz()));//所属期止
////
////        /*增加入参过滤方式，取值为：
////        0采用原过滤方式，CS_GY_GLB_ZSPM有效期与税费种认定有效期有交叉，即返回该品目 ,
////        1税费种认定有效期限在CS_GY_GLB_ZSPM有效期限内，返回该品目  jiyunmei 2017-6-6*/
////        final String glfs = (String)req.get("glfs");
////
////        final String djzclxDm = (String) req.get("djzclxDm");//登记注册类型代码
////        final String hyDm = (String) req.get("hyDm");//行业代码
////        final List<String> hyList = (List<String>) req.get("hyList");//行业列表
////        final String zzsybrZg = (String) req.get(GYJsConstants.getkZzsybrzg());//增值税一般纳税人资格
////        final String fxclbcBz = (String) req.get("fxclbcBz");//风险处理补偿标识
////        //final String zzsxgmnsrZg = (String)req.get("zzsxgmnsrZg");//增值税小规模纳税人资格
////        //当前日期
////        final Date nowdate = DateUtil.toDate(DateUtil.doDateFormat(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
////        //税务机关、征收项目 不能为空
////        if (swjgDm == null || swjgDm.trim().length() == 0 || zsxmDm == null || zsxmDm.trim().length() == 0) {
////            return rsList;
////        }
////        //期起期止如为空，默认为当前日期
////        if (skssqq == null || skssqz == null) {
////            skssqq = nowdate;
////            skssqz = nowdate;
////        }
////        //获取本级上级税务机关
////        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
////
////        //逐级上查，找到某级税务机关 有记录，则返回该级数据
////        List<Map<String, Object>> zspmList = new ArrayList<Map<String, Object>>();
////        for (String bjsjSwjgDm : bjsjSwjgList) {
////            List<Map<String, Object>> zspmKvList = findListInCacheBySwjg(bjsjSwjgDm, GYJsConstants.gettCsZspm());//根据税务机关过滤品目参数表
////            zspmKvList = findListInMapList("zsxmDm", zsxmDm, zspmKvList);//根据征收项目再次过滤
////            if (zspmKvList != null && zspmKvList.size() > 0) {
////                //注意，缓存里面只过滤了 yxbz有效标志，没过滤选用标志
////                //只添加没有重复品目代码的到之前集合：即保证代码优先用下级的
////                final List<Map<String, Object>> fiterZspmKvList = new ArrayList<Map<String, Object>>();
////                for (Map<String, Object> zspmKvMap : zspmKvList) {
////                    final String zspmDm = (String) zspmKvMap.get("zspmDm");
////                    final Date yxqq = (Date) zspmKvMap.get("yxqq");
////                    final Date yxqz = (Date) zspmKvMap.get("yxqz");
////                    //判断有效期
////                    boolean yxq = false;
////                    //未传入所属期，或属期与有效期有交叉，则视为有效
////                    if (skssqq == null || skssqz == null) {
////                        yxq = true;
////                    } else if ("1".equals(glfs)&&GYCastUtils.compareYxqx(skssqq, skssqz, yxqq, yxqz)){
////                        yxq = true;
////                    }else if((GyUtils.isNull(glfs)||"0".equals(glfs))&&GYCastUtils.sqAcross(skssqq, skssqz, yxqq, yxqz)) {
////                        yxq = true;
////                    }
////                    //只添加未重复品目的，期内有效的
////                    if (!findKeyInMapList("zspmDm", zspmDm, zspmList) && yxq) {
////                        fiterZspmKvList.add(zspmKvMap);
////                    }
////                }
////                zspmList.addAll(fiterZspmKvList);//把本级税务机关查得的合法结果集添加到list
////
////                final Map<String, Object> zspmFirstMap = zspmKvList.get(0);
////                final String dgbz = (String) zspmFirstMap.get("dgbz");
////                //如果递归标志为N，不再上溯查找。否则，上溯查找并叠加结果集
////                if (dgbz == null || "N".equals(dgbz)) {
////                    break;
////                }
////            }
////        }
////        List<Map<String, Object>> tList = new ArrayList<Map<String, Object>>();
////        //最后校验选用标志
////        //之所以要分两次过滤，是因为第一次要根据本级是否有记录来判断是否逐级上查。兼容本级全设选用标志=N而不逐级上查的情况
////        for (Map<String, Object> zspmMap : zspmList) {
////            if (zspmMap != null && !zspmMap.isEmpty()) {
////                final String yxbz = (String) zspmMap.get("yxbz");
////                final String xybz = (String) zspmMap.get("xybz");
////                if ("Y".equals(yxbz) && "Y".equals(xybz)) {
////                    tList.add(zspmMap);
////                }
////            }
////        }
////        zspmList = tList;
////        //根据会计公式再过滤出最终的征收品目列表 TODO
////        //传入前作数据转换
////        //是否使用会统审核公式
////        final String ktshgsKg = getXtcs("A0000001061001100", swjgDm);
////        if ("Y".equals(useKtshgs) && "Y".equals(ktshgsKg) && notNull(hyDm) && notNull(zspmList)) {
////            List<Map<String, Object>> ktlist = new ArrayList<Map<String, Object>>();
////            for (Map<String, Object> zspmMap : zspmList) {
////                final Map<String, Object> codemap = new HashMap<String, Object>();
////                codemap.put("code", zspmMap.get("zspmDm"));
////                ktlist.add(codemap);
////            }
////            //调用纳税人管理组提供会统审核公式
////            ktlist = GYKtshgsUtils.getZspmListWithKtshgs(req, ktlist);
////            if (ktlist == null) {
////                ktlist = new ArrayList<Map<String, Object>>();
////            }
////            //用会统审核公式过滤的list对原list进行过滤
////            tList = new ArrayList<Map<String, Object>>();
////            for (Map<String, Object> zspmMap : zspmList) {
////                final String zspmDm = (String) zspmMap.get("zspmDm");
////                for (Map<String, Object> ktmap : ktlist) {
////                    final String code = (String) ktmap.get("code");
////                    if (GYCastUtils.equal(zspmDm, code)) {
////                        tList.add(zspmMap);
////                        break;
////                    }
////                }
////            }
////            zspmList = tList;
////        }
////
////        //取得合集后开始排序
////        final SwordSortUtils.SortDescription description2 = new SwordSortUtils.SortDescription("zspmDm", OrderType.ASC);
////        zspmList = SwordSortUtils.sortMapList(zspmList, description2);
////        //转换部分数据项（针对语义相近的字段，采用宁可多塞的方式）
////        rsList = new ArrayList<Map<String, Object>>();
////        if (GYCastUtils.notNull(zspmList)) {
////            if(GyUtils.isNull(fxclbcBz)){
////                for (Map<String, Object> map : zspmList) {
////                    final Map<String, Object> newMap = new HashMap<String, Object>();
////                    newMap.put("zspmDm", map.get("zspmDm"));
////                    newMap.put("swjgDm", map.get("swjgDm"));
////                    newMap.put("sl1", map.get("sl1"));
////                    newMap.put("sl", map.get("sl1"));
////                    newMap.put("fdsl", map.get("sl1"));
////                    newMap.put("zsl", map.get("sl1"));
////                    //add at 2018-5-11 10:35:59增加返回征收率值
////                    newMap.put("zsl1", map.get("zsl"));
////                    newMap.put("xzqhszDm", map.get("xzqhszDm"));
////                    newMap.put("uuid", map.get("uuid"));
////                    newMap.put("zsxmDm", map.get("zsxmDm"));
////                    //ZOG00_202211150024 征收品目弃用101190101、101190201。但已申报的数据要带出。需单独处理
////                    if (map.get("zspmDm").equals("101190101")){
////                        newMap.put("caption", "国有土地使用权出让契税");
////                        newMap.put("code", "101190101");
////                        newMap.put("zspmmc", "国有土地使用权出让契税");
////                        newMap.put("sfygzzspmbz", "N");
////                        newMap.put("sf2016ygzbz", "");
////                        newMap.put("sjzspmDm", "101190100");
////                        rsList.add(newMap);
////                    } else if (map.get("zspmDm").equals("101190201")){
////                        newMap.put("caption", "土地使用权转让(出售、赠与和交换)契税");
////                        newMap.put("code", "101190201");
////                        newMap.put("zspmmc", "土地使用权转让(出售、赠与和交换)契税");
////                        newMap.put("sfygzzspmbz", "N");
////                        newMap.put("sf2016ygzbz", "");
////                        newMap.put("sjzspmDm", "101190200");
////                        rsList.add(newMap);
////                    } else {
////                        final Map<String, Object> zspmmcMap = (Map<String, Object>) SwordCacheUtils.getFromKV("DM_GY_ZSPM", new Object[]{map.get("zspmDm")});
////                        if (notNull(zspmmcMap)) {
////                            newMap.put("code", map.get("zspmDm"));
////                            newMap.put("sjzspmDm", zspmmcMap.get("SJZSPM_DM"));
////                            newMap.put("zspmmc", zspmmcMap.get("ZSPMMC"));
////                            newMap.put("caption", zspmmcMap.get("ZSPMMC"));
////                            newMap.put("sfygzzspmbz", zspmmcMap.get("SFYGZZSPMBZ"));
////                            newMap.put("sf2016ygzbz", zspmmcMap.get("SF2016YGZBZ"));
////                            rsList.add(newMap);
////                        }
////                    }
////                }
////            }
////            else{
////                List<Map<String, Object>> zspmFxclList = FxGyUtils.getAllZspmForFxclBc(zsxmDm);
////                zspmFxclList = SwordSortUtils.sortMapList(zspmFxclList, description2);
////
////                for (Map<String, Object> map : zspmList) {
////                    final Map<String, Object> newMap = new HashMap<String, Object>();
////                    final String zspmDm = (String)map.get("zspmDm");
////                    newMap.put("zspmDm", map.get("zspmDm"));
////                    newMap.put("swjgDm", map.get("swjgDm"));
////                    newMap.put("sl1", map.get("sl1"));
////                    newMap.put("sl", map.get("sl1"));
////                    newMap.put("fdsl", map.get("sl1"));
////                    newMap.put("zsl", map.get("sl1"));
////                    //add at 2018-5-11 10:35:59增加返回征收率值
////                    newMap.put("zsl1", map.get("zsl"));
////                    newMap.put("xzqhszDm", map.get("xzqhszDm"));
////                    newMap.put("uuid", map.get("uuid"));
////                    newMap.put("zsxmDm", map.get("zsxmDm"));
////
////                    for(Map<String, Object> mapCp : zspmFxclList){
////                        final String zspmDmCp = (String) mapCp.get("zspmDm");
////                        final String yxbz = (String)mapCp.get("yxbz");
////                        final String xybz = (String)mapCp.get("xybz");
////                        final String zspmmc = (String)mapCp.get("zspmmc");
////                        if(zspmDm.equalsIgnoreCase(zspmDmCp)){
////                            newMap.put("code", zspmDm);
////                            newMap.put("sjzspmDm", mapCp.get("sjzspmDm"));
////                            newMap.put("zspmmc", zspmmc);
////                            if("N".equalsIgnoreCase(yxbz) || "N".equalsIgnoreCase(xybz)){
////                                newMap.put("caption", zspmmc+"(失效)");
////                            }
////                            else{
////                                newMap.put("caption", zspmmc);
////                            }
////                            newMap.put("sfygzzspmbz", mapCp.get("sfygzzspmbz"));
////                            newMap.put("sf2016ygzbz", mapCp.get("sf2016ygzbz"));
////                            rsList.add(newMap);
////                            break;
////                        }
////                    }
////                }
////            }
////        }
////        return rsList;
////    }
//
////    /**
////     *@name    获取征收项目列表
////     *@description 不使用会统审核公式入口
////     *@time    创建时间:2014-8-9上午11:07:30
////     *@param req 输入参数map
////     *@return List<Map<String, Object>> 项目列表
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    public static List<Map<String, Object>> getZsxm(Map<String, Object> req) throws SwordBaseCheckedException {
////        return getZsxm(req, "N");
////    }
//
////    /**
////     *@name    获取征收项目列表
////     *@description 使用会统审核公式入口
////     *@time    创建时间:2014-8-9上午11:07:30
////     *@param req 输入参数map
////     *@return List<Map<String, Object>> 项目列表
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    public static List<Map<String, Object>> getZsxmKtshgs(Map<String, Object> req) throws SwordBaseCheckedException {
////        return getZsxm(req, "Y");
////    }
//
////    /**
////     *@name    获取征收品目列表
////     *@description 不使用会统审核公式入口
////     *@time    创建时间:2014-8-12下午08:11:23
////     *@param req 输入Map
////     *@return 品目列表
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    public static List<Map<String, Object>> getZspm(Map<String, Object> req) {
////        return getZspm(req, "N");
////    }
//
////    /**
////     *@name  对上面的方法做一个封装，封装成返回List<SBSbxxVO>，其他方法里面可直接调用了
////     *@description 相关说明
////     *@time    创建时间:2014年12月5日下午5:23:27
////     *@param swjgDm swjgDm
////     *@param djzclxDm djzclxDm
////     *@param zsxmDm zsxmDm
////     *@param djxh djxh
////     *@param zspmDm 如果不为空，只返回这个品目的Sbxx
////     *@return List<SBSbxxVO>
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    public static List<SBSbxxVO> makeSbxxFromZspmGlb(String swjgDm, String djzclxDm, String zsxmDm, String djxh, String zspmDm) throws SwordBaseCheckedException {
////        final Map<String, Object> reqpm = new HashMap<String, Object>();
////        reqpm.put("swjgDm", swjgDm);
////        reqpm.put("djzclxDm", djzclxDm);
////        reqpm.put("zsxmDm", zsxmDm);
////        final List<Map<String, Object>> cxzspmList = getZspm(reqpm);
////        final List<SBSbxxVO> sbxxList = new ArrayList<SBSbxxVO>();
////        final Set<String> tmpSet = new HashSet<String>();
////        if (cxzspmList != null) {
////            for (Map<String, Object> zspmMap : cxzspmList) {
////                final SBSbxxVO sbsbxxVO = SwordTypeUtils.mapToBean(zspmMap, SBSbxxVO.class);
////                if (!GyUtils.isNull(zspmDm) && !sbsbxxVO.getZspmDm().equals(zspmDm)) {
////                    continue;
////                }
////                //过滤掉重复的
////                if (tmpSet.contains(sbsbxxVO.getZspmDm())) {
////                    continue;
////                }
////                tmpSet.add(sbsbxxVO.getZspmDm());
////                GYJsSfzVO row = new GYJsSfzVO();
////                row.setZsxmDm(GYDm01Constants.getDmGyZsxmTdzzs());
////                row.setZspmDm(sbsbxxVO.getZspmDm());
////                final Map<String, Object> reqqx = new HashMap<String, Object>();
////                reqqx.put("swjgDm", ZnsbSessionUtils.getswjgdm());
////                row = GYJsUtils.getMrqx(row, reqqx); //获取默认期限
////                sbsbxxVO.setSbqxDm(row.getSbqxDm());
////                sbsbxxVO.setNsqxDm(row.getNsqxDm());
////                sbsbxxVO.setJkqxDm(row.getJkqxDm());
////                sbsbxxVO.setDjxh(djxh);
////                sbxxList.add(sbsbxxVO);
////            }
////        }
////        return sbxxList;
////    }
//
////    /**
////     *@name    获取征收品目列表
////     *@description 使用会统审核公式入口
////     *@time    创建时间:2014-8-12下午08:11:23
////     *@param req 输入Map
////     *@return 品目列表
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    public static List<Map<String, Object>> getZspmKtshgs(Map<String, Object> req) throws SwordBaseCheckedException {
////        return getZspm(req, "Y");
////    }
//
////    /**
////     *@name    获取子目列表
////     *@description 如只有参数表，代码表中无对应项，过滤掉
////     *@time    创建时间:2014-8-12下午08:13:02
////     *@param req 输入Map
////     *@return List<Map<String, Object>>
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    @SuppressWarnings({ "unchecked", "unused" })
////    public static List<Map<String, Object>> getZszm(Map<String, Object> req) throws SwordBaseCheckedException {
////        //TODO 要区分 计税标志的不同 而赋值不同出参
////        final List<Map<String, Object>> resList = new ArrayList<Map<String, Object>>();
////        final String swjgDm = (String) req.get("swjgDm");//税务机关代码
////        final String zsxmDm = (String) req.get("zsxmDm");//征收项目代码
////        final String zspmDm = (String) req.get("zspmDm");//征收品目代码
////        final String glfs = (String)req.get("glfs");
////        //以下为可选入参
////        Date skssqq = GYCastUtils.cast2Date(req.get(GYJsConstants.getcSkssqq()));//所属期起
////        Date skssqz = GYCastUtils.cast2Date(req.get(GYJsConstants.getcSkssqz()));//所属期止
////        final String xzqhszDm = (String) req.get("xzqhszDm");//行政区划数字代码
////        final String zdsljsfsDm = (String) req.get("sljsfsDm");//指定 税率计算方式代码
////        final String fxclbcBz = (String) req.get("fxclbcBz");//风险处理补偿标识
////        //当前日期
////        final Date nowdate = SwordDateUtils.StringToDate(SwordDateUtils.toDateStrByFormatIndex(SwordDateUtils.getSystemCurrentTime(), 3));
////        //税务机关、征收项目、征收品目 不能为空
////        if (swjgDm == null || swjgDm.trim().length() == 0 || zsxmDm == null || zsxmDm.trim().length() == 0 || zspmDm == null || zspmDm.trim().length() == 0) {
////            return resList;
////        }
////        //期起期止如为空，默认为当前日期
////        if (skssqq == null || skssqz == null) {
////            skssqq = nowdate;
////            skssqz = nowdate;
////        }
////        //获取本级上级税务机关
////        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
////
////        //逐级上查，找到某级税务机关 有记录，则返回该级数据
////        List<Map<String, Object>> zszmList = new ArrayList<Map<String, Object>>();
////        for (String bjsjSwjgDm : bjsjSwjgList) {
////            List<Map<String, Object>> zszmKvList = findListInCacheBySwjg(bjsjSwjgDm, GYJsConstants.gettCsZszm());//根据税务机关过滤子目参数表
////            zszmKvList = findListByLikeInMapList("zspmDm", zspmDm, zszmKvList);//根据征收品目再次过滤（模糊匹配）
////            if (zszmKvList != null && zszmKvList.size() > 0) {
////                //注意，缓存里面只过滤了 yxbz有效标志，没过滤选用标志
////                //只添加没有重复子目代码的到之前集合：即保证代码优先用下级的
////                final List<Map<String, Object>> fiterZszmKvList = new ArrayList<Map<String, Object>>();
////                for (Map<String, Object> zszmKvMap : zszmKvList) {
////                    final String zszmDmKv = (String) zszmKvMap.get("zszmDm");
////                    final Date yxqq = (Date) zszmKvMap.get("yxqq");
////                    final Date yxqz = (Date) zszmKvMap.get("yxqz");
////                    //判断有效期
////                    boolean yxq = false;
////                    //未传入所属期，或属期与有效期有交叉，则视为有效
////                    if (skssqq == null || skssqz == null) {
////                        yxq = true;
////                    } else if ("1".equals(glfs)&&GYCastUtils.compareYxqx(skssqq, skssqz, yxqq, yxqz)){
////                        yxq = true;
////                    }else if((GyUtils.isNull(glfs)||"0".equals(glfs))&&GYCastUtils.sqAcross(skssqq, skssqz, yxqq, yxqz)) {
////                        yxq = true;
////                    }
////
////                    //只添加未重复子目的，期内有效的
////                    if (!findKeyInMapList("zszmDm", zszmDmKv, zszmList) && yxq) {
////                        fiterZszmKvList.add(zszmKvMap);
////                    }
////                }
////                zszmList.addAll(fiterZszmKvList);//把本级税务机关查得的合法结果集添加到list
////
////                final Map<String, Object> zszmFirstMap = zszmKvList.get(0);
////                final String dgbz = (String) zszmFirstMap.get("dgbz");
////                //如果递归标志为N，不再上溯查找。否则，上溯查找并叠加结果集
////                if (dgbz == null || "N".equals(dgbz)) {
////                    break;
////                }
////            }
////        }
////
////        //再做一次基本过滤：标志、并按 征收子目/经营项目 分场景过滤
////        //之所以要分两次过滤，是因为第一次要根据本级是否有记录来判断是否逐级上查。兼容本级全设选用标志=N而不逐级上查的情况
////        final List<Map<String, Object>> tList = new ArrayList<Map<String, Object>>();
////        for (Map<String, Object> zszmMap : zszmList) {
////            if (zszmMap != null && !zszmMap.isEmpty()) {
////                final String yxbz = (String) zszmMap.get("yxbz");
////                final String xybz = (String) zszmMap.get("xybz");
////                final Date yxqq = (Date) zszmMap.get("yxqq");
////                final Date yxqz = (Date) zszmMap.get("yxqz");
////                final String zszmDm = (String) zszmMap.get("zszmDm");
////                final Map<String, Object> zszmDmMap = (Map<String, Object>) SwordCacheUtils.getFromKV("DM_GY_ZSZM", zszmDm);
////                if (isNull(zszmDmMap)) {
////                    continue;//如果为空，跳过，继续后面循环
////                }
////                final String sljsfsDm = (String) zszmDmMap.get("SLJSFS_DM");
////                boolean jsfs = false;
////
////                //未指定税率计算方式
////                if (zdsljsfsDm == null || zdsljsfsDm.trim().length() == 0) {
////                    jsfs = true;
////                }
////                //经营项目、征收子目分场景匹配
////                else if (GYJsConstants.getSljsfsJyxm().equals(zdsljsfsDm) || GYJsConstants.getSljsfsQt().equals(zdsljsfsDm)) {
////                    if (zdsljsfsDm.equals(sljsfsDm)) {
////                        jsfs = true;
////                    }
////                }
////                if ("Y".equals(yxbz) && "Y".equals(xybz) && jsfs) {
////                    tList.add(zszmMap);
////                }
////            }
////        }
////        zszmList = tList;
////
////        //土地等级的，需添加获取土地等级列表的util（需转调纳税人管理组）
////
////        //取得合集后开始排序
////        final SwordSortUtils.SortDescription description2 = new SwordSortUtils.SortDescription("zszmDm", OrderType.ASC);
////        zszmList = SwordSortUtils.sortMapList(zszmList, description2);
////        //转换部分数据项（针对语义相近的字段，采用宁可多塞的方式）
////        if (GYCastUtils.notNull(zszmList)) {
////            if(GyUtils.isNull(fxclbcBz)){
////                for (Map<String, Object> map : zszmList) {
////                    final Map<String, Object> newMap = new HashMap<String, Object>();
////                    newMap.put("zszmDm", map.get("zszmDm"));
////                    newMap.put("swjgDm", map.get("swjgDm"));
////                    newMap.put("xzqhszDm", map.get("xzqhszDm"));
////                    newMap.put("uuid", map.get("uuid"));
////                    newMap.put("zsxmDm", map.get("zsxmDm"));
////                    newMap.put("zspmDm", map.get("zspmDm"));
////                    newMap.put("sx", map.get("sx"));
////                    newMap.put("xx", map.get("xx"));
////                    newMap.put("jsbz1", map.get("jsbz1"));
////                    newMap.put("fdsl", map.get("fdsl"));
////                    newMap.put("yxqq", map.get("yxqq"));
////                    newMap.put("yxqz", map.get("yxqz"));
////                    final Map<String, Object> zszmmcMap = (Map<String, Object>) SwordCacheUtils.getFromKV("DM_GY_ZSZM", new Object[] { map.get("zszmDm") });
////                    if (isNull(zszmmcMap)) {
////                        continue;
////                    }
////                    newMap.put("zszmmc", zszmmcMap.get("ZSZMMC"));
////                    newMap.put("caption", zszmmcMap.get("ZSZMMC"));
////                    newMap.put("code", map.get("zszmDm"));
////                    //计税标志
////                    final String jsbz1 = (String) map.get("jsbz1");
////                    //征收率
////                    if (GYJsConstants.getJsbzZsl().equals(jsbz1)) {
////                        newMap.put("sl1", map.get("xx"));//税率默认为下限
////                        newMap.put("sl", map.get("xx"));//税率默认为下限
////                        newMap.put("zsl", map.get("xx"));//征收率默认为下限
////                    }
////                    //税率
////                    else if (GYJsConstants.getJsbzSl().equals(jsbz1)) {
////                        newMap.put("sl1", map.get("xx"));//税率默认为下限
////                        newMap.put("sl", map.get("xx"));//税率默认为下限
////                    }
////                    //应税所得率
////                    else if (GYJsConstants.getJsbzYssdl().equals(jsbz1)) {
////                        newMap.put("yssdl", map.get("xx"));//应税所得率默认为下限
////                        newMap.put("xx", map.get("xx"));//应税所得率-下限
////                        newMap.put("sx", map.get("sx"));//应税所得率-上限
////                    }
////                    resList.add(newMap);
////                }
////            }
////            else{
////                List<Map<String, Object>> zszmFxclList = FxGyUtils.getAllZszmForFxclBc(zspmDm);
////                zszmFxclList = SwordSortUtils.sortMapList(zszmFxclList, description2);
////
////                for (Map<String, Object> map : zszmList) {
////                    final Map<String, Object> newMap = new HashMap<String, Object>();
////                    final String zszmDm = (String)map.get("zszmDm");
////                    newMap.put("zszmDm", map.get("zszmDm"));
////                    newMap.put("swjgDm", map.get("swjgDm"));
////                    newMap.put("xzqhszDm", map.get("xzqhszDm"));
////                    newMap.put("uuid", map.get("uuid"));
////                    newMap.put("zsxmDm", map.get("zsxmDm"));
////                    newMap.put("zspmDm", map.get("zspmDm"));
////                    newMap.put("sx", map.get("sx"));
////                    newMap.put("xx", map.get("xx"));
////                    newMap.put("jsbz1", map.get("jsbz1"));
////                    newMap.put("fdsl", map.get("fdsl"));
////
////                    for(Map<String, Object> mapCp : zszmFxclList){
////                        final String zszmDmCp = (String)mapCp.get("zszmDm");
////                        final String yxbz = (String)mapCp.get("yxbz");
////                        final String xybz = (String)mapCp.get("xybz");
////                        final String zszmmc = (String)mapCp.get("zszmmc");
////                        if(zszmDm.equalsIgnoreCase(zszmDmCp)){
////                            newMap.put("zszmmc", zszmmc);
////                            if("N".equalsIgnoreCase(yxbz) || "N".equalsIgnoreCase(xybz)){
////                                newMap.put("caption", zszmmc+"(失效)");
////                            }
////                            else{
////                                newMap.put("caption", zszmmc);
////                            }
////                            newMap.put("code", map.get("zszmDm"));
////                            //计税标志
////                            final String jsbz1 = (String) map.get("jsbz1");
////                            //征收率
////                            if (GYJsConstants.getJsbzZsl().equals(jsbz1)) {
////                                newMap.put("sl1", map.get("xx"));//税率默认为下限
////                                newMap.put("sl", map.get("xx"));//税率默认为下限
////                                newMap.put("zsl", map.get("xx"));//征收率默认为下限
////                            }
////                            //税率
////                            else if (GYJsConstants.getJsbzSl().equals(jsbz1)) {
////                                newMap.put("sl1", map.get("xx"));//税率默认为下限
////                                newMap.put("sl", map.get("xx"));//税率默认为下限
////                            }
////                            //应税所得率
////                            else if (GYJsConstants.getJsbzYssdl().equals(jsbz1)) {
////                                newMap.put("yssdl", map.get("xx"));//应税所得率默认为下限
////                                newMap.put("xx", map.get("xx"));//应税所得率-下限
////                                newMap.put("sx", map.get("sx"));//应税所得率-上限
////                            }
////                            resList.add(newMap);
////                            break;
////                        }
////                    }
////                }
////            }
////        }
////
////        return resList;
////    }
//
////    /**
////     *@name    根据品目获取行业列表
////     *@description 相关说明
////     *@time    创建时间:2014-8-12下午08:13:12
////     *@param req 输入Map
////     *@return List<Map<String, Object>>
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    @SuppressWarnings("unchecked")
////    public static List<Map<String, Object>> getHyByPm(Map<String, Object> req) throws SwordBaseCheckedException {
////        List<Map<String, Object>> resList = new ArrayList<Map<String, Object>>();
////        //先约定入参
////        /*String swjgDm = (String)req.get("swjgDm");
////        String zsxmDm = (String)req.get("zsxmDm");
////        String zspmDm = (String)req.get("zspmDm");*/
////        //先写死取全缓存
////        final Collection co = SwordCacheUtils.getAllDataFromKV("DM_GY_HY");
////        final Iterator it = co.iterator();
////        while (it.hasNext()) {
////            final Map<String, Object> dataKvMap = (Map<String, Object>) it.next();
////            resList.add(dataKvMap);
////        }
////        resList = cast2DefKeyList(resList);//将大写下划线Key转换成标准小写Key
////        return resList;
////    }
//
////    /**
////     *@name    根据确定的征收项目/品目/子目 获取税率/征收率/应税所得率等
////     *@description 老方法，仅供房产交易申报调用，其它不推荐用此老方法
////     *额外增加了联动调用个税累计税率的方法，供通用申报、房产交易申报用
////     *@time    创建时间:2014-8-13上午10:25:00
////     *@param row 输入Map
////     *@param csMap 参数Map
////     *@return Map<String,Object>
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    public static Map<String, Object> jsSlzsl(Map<String, Object> row, Map<String, Object> csMap) throws SwordBaseCheckedException {
////        Map<String, Object> rs = new HashMap<String, Object>();
////        final String swjgDm = (String) row.get("swjgDm");//税务机关代码
////        //类型转换：转成标准VO
////        GYJsSfzVO sfzvo = turnSfzVO(row);
////        csMap.put("swjgDm", swjgDm);
////        sfzvo = getSlzsl(sfzvo, csMap);//调用新方法
////
////        //3、个人所得税累进税率
////        //个税，无子目或为应税所得率的情况，取累进税率
////        if (GYDm01Constants.getDmGyZsxmGrsds().equals(sfzvo.getZsxmDm())) {
////            //每次清空当前标志，重新判断
////            if (GYJsConstants.getTsjsbzGslj().equals(sfzvo.getTsjsbz())) {
////                GYJsUtils.jsGsLjsl(sfzvo, csMap);//联动调用个税累计计算
////            }
////        }
////        //类型转换：转回map
////        rs = turnSfzMap(sfzvo);
////        return rs;
////    }
//
////    /**
////     *@name    根据确定的征收项目/品目/子目 获取税率/征收率/应税所得率等
////     *@description 新方法，换成标准vo
////     *只获取固定税率、征收率、应税所得率（累进税率等需根据应税项、计税依据确定的，不在此处理）
////     *@time    创建时间:2014-8-13上午10:25:00
////     *@param row 输入Map
////     *@param csMap 参数Map
////     *@return Map<String,Object>
////     *<AUTHOR>
////     *@history 修订历史（历次修订内容、修订人、修订时间等）
////     */
////    @SuppressWarnings("unused")
////    public static GYJsSfzVO getSlzsl(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
////        GYJsSfzVO rs = row;
////        final String swjgDm = (String) csMap.get("swjgDm");//税务机关代码
////        final String zsxmDm = row.getZsxmDm();//征收项目代码
////        final String zspmDm = row.getZspmDm();//征收品目代码
////        //以下为可选入参
////        String zszmDm = row.getZszmDm();//征收子目代码
////        if (notNull(zszmDm)) {
////            zszmDm = zszmDm.trim();//考虑到土地等级 不足9位 可能有空格的问题
////        }
////        Date skssqq = GYCastUtils.cast2Date(row.getSkssqq());//所属期起
////        Date skssqz = GYCastUtils.cast2Date(row.getSkssqz());//所属期止
////        final String zdSljsfsDm = (String) csMap.get("sljsfsDm");//指定税率计算方式代码（非必传，如传入则按传入方式进行计税）
////
////        final String sljsfsDm = zdSljsfsDm;//税率计算方式 TODO暂不用
////        final String zzsybrZg = (String) csMap.get(GYJsConstants.getkZzsybrzg());//增值税一般纳税人资格
////        //final String zzsxgmnsrZg = (String)csMap.get("zzsxgmnsrZg");//增值税小规模资格
////        //当前日期
////        final Date nowdate = SwordDateUtils.StringToDate(SwordDateUtils.toDateStrByFormatIndex(SwordDateUtils.getSystemCurrentTime(), 3));
////        //必传参数校验
////        if (GYCastUtils.isNull(swjgDm) || GYCastUtils.isNull(zsxmDm) || GYCastUtils.isNull(zspmDm)) {
////            return rs;
////        }
////        //期起期止如为空，默认为当前日期
////        if (skssqq == null || skssqz == null) {
////            skssqq = nowdate;
////            skssqz = nowdate;
////        }
////        //是否有子目标志
////        boolean zszmBz = false;
////        //子目为应税所得率标志
////        boolean zmYssdlBz = false;
////        if (zszmDm != null && zszmDm.trim().length() >= 9) {
////            zszmBz = true;
////        }
////        //add by yangdl 针对于城镇土地使用税，选择了土地等级，且税率不为空的情况，税率不需要重新取数。
////        if (zsxmDm.equals(GYDm01Constants.getDmGyZsxmCztdsys()) && GYCastUtils.notNull(row.getSl()) && GYCastUtils.notNull(row.getTddjDm())) {
////            row.setFdsl(row.getSl());
////            return row;
////        }
////        //csmap中增加发票代开西藏模式标识,重置 add by luoxingpan 2015/8/11
////        csMap.put("fpdkXzms", "0");
////        //品目或子目map
////        Map<String, Object> zspmMap = new HashMap<String, Object>();
////        Map<String, Object> zszmMap = new HashMap<String, Object>();
////        //获取本级上级税务机关
////        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
////        //1、如是有子目的情况
////        if (zszmBz) {
////            //上溯查找
////            for (String bjsjSwjgDm : bjsjSwjgList) {
////                final List<Map<String, Object>> zszmList = findListInCacheBySwjg(bjsjSwjgDm, GYJsConstants.gettCsZszm());//根据税务机关查找
////                zszmMap = findSingleInMapListByYxq("zszmDm", zszmDm, skssqq, skssqz, zszmList);//根据征收子目再次查找唯一项
////                if (zszmMap != null && !zszmMap.isEmpty()) {
////                    //需要过滤 xybz选用标志
////                    final String xybz = (String) zszmMap.get("xybz");
////                    if ("N".equals(xybz)) {
////                        zszmMap = new HashMap<String, Object>();
////                    }
////                    break;//找到即不再上溯
////                }
////            }
////            //计税标志
////            final String jsbz1 = (String) zszmMap.get("jsbz1");
////            row.setJsbz1(jsbz1);//计税标志
////            //征收率
////            if (GYJsConstants.getJsbzZsl().equals(jsbz1)) {
////                row.setSl(cast2Str(zszmMap.get("xx")));//征收率默认为下限
////                row.setFdsl(cast2Str(zszmMap.get("fdsl")));//法定税率
////            }
////            //税率
////            else if (GYJsConstants.getJsbzSl().equals(jsbz1)) {
////                final String ssjmxzDm = cast2Str(zszmMap.get("ssjmxzDm"));
////                if (!GyUtils.isNull(ssjmxzDm)) {
////                    row.setSsjmxzDm(ssjmxzDm);
////                }
////                row.setSl(cast2Str(zszmMap.get("xx")));//税率默认为下限
////                row.setFdsl(cast2Str(zszmMap.get("fdsl")));//法定税率
////            }
////            //应税所得率、所得率
////            else if (GYJsConstants.getJsbzYssdl().equals(jsbz1) || GYJsConstants.getJsbzSdl().equals(jsbz1)) {
////                zmYssdlBz = true;
////                row.setYssdl(cast2Str(zszmMap.get("xx")));//应税所得率默认为下限
////                row.setXx(cast2Str(zszmMap.get("xx")));//应税所得率-下限
////                row.setSx(cast2Str(zszmMap.get("sx")));//应税所得率-上限
////            }
////            //子目_累进税率
////            else if (GYJsConstants.getJsbzLjsl().equals(jsbz1)) {
////                row.setSl(cast2Str(zszmMap.get("xx")));//税率默认为下限
////                row.setFdsl(cast2Str(zszmMap.get("fdsl")));//法定税率
////                jsZmLjsl(row, csMap);//计算累进税率，CS_GY_ZMLJSL，如有累进税率，覆盖之前的税率
////            }
////            //子目_西藏模式 add by luoxingpan 2015/8/11
////            else if (GYJsConstants.getJsbzXzms().equals(jsbz1)) {
////                row.setSl(cast2Str(zszmMap.get("xx")));//征收率默认为下限
////                row.setFdsl(cast2Str(zszmMap.get("fdsl")));//法定税率
////                row.setYssdl(null);//应税所得率默认为下限
////                row.setTsjsbz(null);
////                //csmap中增加发票代开西藏模式标识
////                csMap.put("fpdkXzms", "1");
////            }
////        }
////        //2、无子目，或者有子目但为应税所得率的情况，需从品目取税率
////        if (!zszmBz || (zszmBz && zmYssdlBz)) {
////            //上溯查找
////            for (String bjsjSwjgDm : bjsjSwjgList) {
////                final List<Map<String, Object>> zspmList = findListInCacheBySwjg(bjsjSwjgDm, GYJsConstants.gettCsZspm());//根据税务机关查找
////                zspmMap = findSingleInMapListByYxq("zspmDm", zspmDm, skssqq, skssqz, zspmList);//根据征收品目再次查找唯一项
////                if (zspmMap != null && !zspmMap.isEmpty()) {
////                    //需要过滤 xybz 选用标志
////                    final String xybz = (String) zspmMap.get("xybz");
////                    if ("N".equals(xybz)) {
////                        zspmMap = new HashMap<String, Object>();
////                    }
////                    row.setSl(cast2Str(zspmMap.get("sl1")));//税率
////                    row.setZsl(cast2Str(zspmMap.get("zsl")));//征收率
////                    row.setFdsl(cast2Str(zspmMap.get("sl1")));//法定税率
////                    break;//找到即不再上溯
////                }
////            }
////        }
////        //3、个人所得税累进税率
////        //个税，无子目或为应税所得率的情况，取累进税率
////        if (GYDm01Constants.getDmGyZsxmGrsds().equals(zsxmDm)) {
////            //每次清空当前标志，重新判断
////            if (GYJsConstants.getTsjsbzGslj().equals(row.getTsjsbz())) {
////                row.setTsjsbz(null);
////            }
////            if (!zszmBz || zmYssdlBz) {
////                //计算个税累进税率、速算扣除数、免税收入等
////                row.setTsjsbz(GYJsConstants.getTsjsbzGslj());
////                //jsGsLjsl(row, csMap);//TODO 暂屏蔽计算，改为具体单元格计算
////            }
////
////        }
////        //4、土地增值税累进税率
////        //土地增值税，无子目的情况，取累进税率
////        if (GYDm01Constants.getDmGyZsxmTdzzs().equals(zsxmDm)) {
////            //每次清空当前标志，重新判断
////            if (GYJsConstants.getTsjsbzTdzzslj().equals(row.getTsjsbz())) {
////                row.setTsjsbz(null);
////            }
////            if (!zszmBz) {
////                //计算土地增值税累进税率、速算扣除系数、速算扣除数等
////                row.setTsjsbz(GYJsConstants.getTsjsbzTdzzslj());
////                //jsTdzzsLjsl(row, csMap);//TODO 暂屏蔽计算，改为具体单元格计算
////            }
////        }
////
////        //5、增值税小规模纳税人，未选子目的情况下，取固定征收率。
////        if (GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm) && "N".equals(zzsybrZg) && isNull(zszmDm)) {
////            //商业(2%) 品目 征收率 2%
////            if (GYDm01Constants.getDmGyZspmSy1().equals(zspmDm)) {
////                row.setSl("0.02");//征收率0.02
////                row.setFdsl("0.02");//征收率0.02
////            }
////            //常规情况下，默认3%征收率
////            else {
////                row.setSl("0.03");//征收率0.03
////                row.setFdsl("0.03");//征收率0.03
////            }
////
////            //增值税专、普票代开，征收率不为空的情况下，取征收率
////            final String lybz = cast2Str(csMap.get("lybz"));
////            if(notNull(lybz) && "FPDK".equals(lybz)){
////                final String fpdklbDm = cast2Str(csMap.get("fpdklbDm"));//代开发票类别
////                Double zsl = FPdkUtils.castToDouble(row.getZsl());//征收率
////                //征收率不为空，增值税专、普票代开
////                if(zsl.compareTo(0D) > 0 && notNull(fpdklbDm) && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
////                        || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", ""))){
////                    final String yqjs = cast2Str(csMap.get("yqjs"));
//////                    final Map<String,Object> xgmmzMap=FPdkUtils.xgmMzxx(null);
//////                    final String xgmmzbz =!GyUtils.isNull(xgmmzMap)?GYJsUtils.cast2Str(xgmmzMap.get("xgmmzbz")):"";//小规模免征是否在有效期内
////                    if("Y".equals(yqjs) && FPdkUtils.yqjs(null)
////                            && zsl.compareTo(0.03D)==0){
////                        zsl = 0.01D;
////                    }
////                    final String zrrbz = (String) csMap.get("zrrbz");//自然人标志
////                    final String djzclxDm = (String) csMap.get("djzclxDm");//登记注册类型
////                    final String zfzljsxz = (String) csMap.get("zfzljsxz");//住房租赁计税选择项
////                    final String zfzlqybs = (String) csMap.get("zfzlqybs");//住房租赁企业标识
////                    final String sffwcz = (String) csMap.get("sffwcz");//是否住房出租
////
////                    //前置条件  参数1开启  住房租赁企业标识 是住房出租    5%
////                    if( FpglFPZdyConstants.getY().equals(sffwcz) && zspmDm.startsWith("1010166") && zsl.compareTo(0.05)==0){
////                        //参数1开启且住房租赁企业标识为Y
////                        //1.非个体工商户和自然人  按1.5%计税
////                        //2.个体工商户或自然人根据选择是否按照1.5%或者减按1.5%,且默认按照1.5%计税
////                        if(FPdkUtils.getZfzlqyQy()&&FpglFPZdyConstants.getY().equals(zfzlqybs)){
////                            if(!FpglFPZdyConstants.getY().equals(zfzljsxz)){
////                                zsl = 0.015D;
////                            }
////                        }
////                        //参数2开启
////                        if(FPdkUtils.getGrczfwSlXz()){
////                            if(!FpglFPZdyConstants.getY().equals(zfzlqybs) && (FpglFPZdyConstants.getY().equals(zrrbz)
////                                    ||FpdkJsUtils.isGtgsh(zrrbz,djzclxDm))){
////                                zsl = 0.015D;
////                            }
////                        }
////                    }
////                    row.setSl(cast2Str(zsl));//征收率
////                    row.setFdsl(cast2Str(zsl));//征收率
////                }
////            }
////        }
////        //6、城镇土地使用税，根据土地等级取税率
////        //TODO
////        //因级联关系多，需根据行政区划 获得 土地等级列表，土地等级列表获得率列表，所以只能根据当时列表带率到界面上，不再回刷
////        FpdkJsUtils01.setFpdkxCztdsysSl(row, csMap);
////        //TODO 如增值税，如税率变动，需根据税率再反算发票代开的收入额（不含税）
////
////        //企业所得税，优先取值企业所得税核定结果中的应税所得率，如果取不到则取值cs_gy_glb_zszm中的下限
////        getYssdlFromQysdsHd(row, csMap);
////        //“个人所得税”且征收品目为“个体户生产经营所得”，若税务机关对纳税人核定了税额，取其征收子目、税率，不可修改，税率为核定的税率。
////        dealGthscjysd(row, csMap);
////        //设置发票代开营改增税率
////        setFpdkYgzSl(row, csMap);
////        rs = row;
////        return rs;
////    }
//
//    /**
//     *@name    整个表格：根据确定的征收项目/品目/子目 获取税率/征收率/应税所得率等
//     *@description 相关说明
//     *@time    创建时间:2014-8-20下午07:13:03
//     *@param grid 表格
//     *@param csMap 附加参数Map
//     *@return grid 表格

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> getGridSlzsl(List<GYJsSfzVO> grid, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final List<GYJsSfzVO> rsGrid = grid;
//        if (!notNull(rsGrid)) {
//            return rsGrid;
//        }
//        for (GYJsSfzVO row : grid) {
//            getSlzsl(row, csMap);
//        }
//        return rsGrid;
//    }
//
//    /**
//     *@name    获取特殊计税标志
//     *@description 描述
//     *@time    创建时间:2014-9-4下午08:10:22
//     *@param zsxmDm 征收项目
//     *@param zspmDm 征收品目
//     *@param zszmDm 征收子目
//     *@param csMap 参数map
//     *@return 特殊计税标志
//     *@throws SwordBaseCheckedException   .
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getTsjsbz(String zsxmDm, String zspmDm, String zszmDm, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final GYJsSfzVO row = new GYJsSfzVO();
//        row.setZsxmDm(zsxmDm);
//        row.setZspmDm(zspmDm);
//        row.setZszmDm(zszmDm);
//        getSlzsl(row, csMap);
//        return row.getTsjsbz();
//    }
//
//    /**
//     *@name    根据法定税率、税率差计算减免额
//     *@description 相关说明
//     *@time    创建时间:2014-8-13上午11:50:40
//     *@param row 输入Map
//     *@return Map<String, Object>

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO calcFdJmse(GYJsSfzVO row) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        double fdJmse = 0;
//        final Double jsyj = cast2Double(row.getJsyj());// 计税依据
//        final Double fdsl = cast2Double(row.getFdsl());// 法定税率
//        final Double sx = cast2Double(row.getSx());// 上限
//        final Double xx = cast2Double(row.getXx());// 下限
//        Double sl = cast2Double(row.getSl());// 税率
//        //如无税率，则再取下限、上限
//        if (sl == null) {
//            if (xx != null && xx.compareTo(new Double("0")) > 0) {
//                sl = xx;
//            }
//            if (sx != null && sx.compareTo(new Double("0")) > 0) {
//                sl = sx;
//            }
//        }
//        // 判断计税依据 不为空
//        if (jsyj != null && jsyj.compareTo(new Double("0")) > 0) {
//            // 判断 法定税率、税率 不为空，为正常数据，且两率有差额
//            if (fdsl != null && sl != null && fdsl.compareTo(new Double("0")) > 0 && fdsl.compareTo(sl) != 0) {
//                fdJmse = SwordMathUtils.multiple(jsyj.doubleValue(), SwordMathUtils.subtract(fdsl.doubleValue(), sl.doubleValue()));
//            }
//        }
//        row.setFdJmse(cast2Str(fdJmse));
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    获取默认纳税期限/申报期限/缴款期限代码
//     *@description 相关说明
//     *@time    创建时间:2014-8-13下午02:24:32
//     *@param row 输入GYJsSfzVO
//     *@param csMap 输入参数map
//     *@return 行vo
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unused")
//    public static GYJsSfzVO getMrqx(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final GYJsSfzVO rs = row;
//        final String zsxmDm = row.getZsxmDm();
//        final String skssqq = row.getSkssqq();
//        final String swjgDm = (String) csMap.get("swjgDm");//税务机关代码
//        //税务机关、征收项目 不能为空
//        if (swjgDm == null || swjgDm.trim().length() == 0 || zsxmDm == null || zsxmDm.trim().length() == 0) {
//            return rs;
//        }
//        return getMrqxFromZsxmpmqxgzb(rs, swjgDm);
//    }
//
//    /**
//     *@name    获取CS_GY_MRQXGZ默认纳税期限/申报期限/缴款期限代码
//     *@description 相关说明
//     *@time    创建时间:2014-8-13下午02:24:32
//     *@param row 输入GYJsSfzVO
//     *@param swjgDm swjgDm
//     *@return 行vo
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO getMrqxFromMrqxgz(GYJsSfzVO row, String swjgDm) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        final String zsxmDm = row.getZsxmDm();//征收项目代码
//        //以下条件为可选
//
//        final String zspmDm = row.getZspmDm();//征收品目代码  todo暂不使用此参数
//        //税务机关、征收项目 不能为空
//        if (swjgDm == null || swjgDm.trim().length() == 0 || zsxmDm == null || zsxmDm.trim().length() == 0) {
//            return rs;
//        }
//        //输出参数变量
//        String nsqxDm = new String();
//        String sbqxDm = new String();
//        String jkqxDm = new String();
//
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//
//        //逐级上查，找到某级税务机关 有记录，则返回该级数据
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            final List<Map<String, Object>> mrqxKvList = findListInCache(bjsjSwjgDm, "SWJG_DM", "CS_DJ_MRQXGZ");//根据税务机关过滤
//            final List<Map<String,Object>> resList = new ArrayList<Map<String,Object>>();
//            resList.addAll(findListInMapList("zspmDm", zspmDm, mrqxKvList));//根据征收品目再次查找
//            resList.addAll(findListInMapListNew("zsxmDm","zspmDm",zsxmDm,"%%%%%%%%%",mrqxKvList));//根据征收品目通配再次查找
//            resList.addAll(findListInMapList("zsxmDm", zsxmDm, mrqxKvList));//根据征收项目随机品目再次查找
//            if (resList != null && resList.size() > 0) {
//                final Map<String, Object> mrqxKvMap = resList.get(0);
//                if (mrqxKvMap != null && !mrqxKvMap.isEmpty()) {
//                    final String yxbz = (String) mrqxKvMap.get("yxbz");
//                    final String xybz = (String) mrqxKvMap.get("xybz");
//                    if ("Y".equals(yxbz) && "Y".equals(xybz)) {
//                        nsqxDm = (String) mrqxKvMap.get("nsqxDm");//纳税期限
//                        sbqxDm = (String) mrqxKvMap.get("sbqxDm");//申报期限
//                        jkqxDm = (String) mrqxKvMap.get("jkqxDm");//缴款期限
//                        break;//找到该征收项目的期限即终止
//                    }
//                }
//            }
//        }
//        row.setNsqxDm(GyUtils.isNull(row.getNsqxDm()) ? nsqxDm : row.getNsqxDm());
//        row.setSbqxDm(sbqxDm);
//        row.setJkqxDm(jkqxDm);
//        rs = row;
//        return rs;
//    }
//
//    /**
//     * @name 判断日期包含
//     * @description 相关说明
//     * @time 创建时间:2022-10-03 上午 09:55:18
//     * @param current current
//     * @param start start
//     * @param end end
//     * @throws SwordBaseCheckedException 通用架构异常
//     * @return boolean
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static boolean between(Date current, Date start, Date end) throws SwordBaseCheckedException {
//        return !(current.before(start) || current.after(end));
//    }
//
//    /**
//     * @name List取一条值
//     * @description 相关说明
//     * @time 创建时间:2022-10-03 上午 10:00:32
//     * @param list list
//     * @param predicate predicate
//     * @param <T> T
//     * @throws SwordBaseCheckedException 通用架构异常
//     * @return T
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static <T> T getOneData(List<T> list, Predicate<T> predicate) throws SwordBaseCheckedException {
//        if (GyUtils.isNull(list)) {
//            return null;
//        }
//        for (T t : list) {
//            if (predicate.apply(t)) {
//                return t;
//            }
//        }
//        return null;
//    }
//    /**
//     * @name 通过有效期取缓存数据
//     * @description 相关说明
//     * @time 创建时间:2022-10-03 上午 10:05:20
//     * @param list list
//     * @param yxqq yxqq
//     * @param yxqz yxqz
//     * @throws SwordBaseCheckedException 通用架构异常
//     * @return Object>
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> getOneCacheDataByYxq(List<Map<String, Object>> list, final Date yxqq, final Date yxqz) throws SwordBaseCheckedException {
//        return getOneData(list, new Predicate<Map<String, Object>>() {
//            @Override
//            public boolean apply(Map<String, Object> map) {
//                try {
//                    final Date start = GYCastUtils.cast2Date(map.get("YXQQ"));
//                    final Date end = GYCastUtils.cast2Date(map.get("YXQZ"));
//                    return GYJsUtils.between(yxqq, start, end) && GYJsUtils.between(yxqz, start, end);
//                } catch (SwordBaseCheckedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        });
//    }
//
//    /**
//     * @name 取CS_GY_ZSXMPMQXGZB配置
//     * @description 相关说明
//     * @time 创建时间:2022-10-03 上午 10:14:05
//     * @param swjgDm swjgDm
//     * @param zsxmDm zsxmDm
//     * @param zspmDm zspmDm
//     * @param nsqxDm nsqxDm
//     * @param skssqq skssqq
//     * @param skssqz skssqz
//     * @throws SwordBaseCheckedException 通用架构异常
//     * @return Object>
//     * <AUTHOR>
//     * @history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static Map<String, Object> getCacheZpxmPmQxPzb(String swjgDm, String zsxmDm, String zspmDm,
//                                                          String nsqxDm, String skssqq, String skssqz) throws SwordBaseCheckedException {
//        final Date now = SwordDateUtils.getSystemCurrentTime().getTime();
//        final Date tempSkssqq = GyUtils.isNull(skssqq) ? now : SwordDateUtils.parseDate(skssqq, 3);
//        final Date tempSkssqz = GyUtils.isNull(skssqz) ? now : SwordDateUtils.parseDate(skssqz, 3);
//        final List<Map<String, Object>> list = (List<Map<String, Object>>) SwordCacheUtils.getFromKV("CS_GY_ZSXMPMQXGZB", swjgDm, zsxmDm, zspmDm, nsqxDm);
//        return getOneCacheDataByYxq(list, tempSkssqq, tempSkssqz);
//    }
//
//    /**
//     *@name    获取CS_GY_ZSXMPMQXGZB默认纳税期限/申报期限/缴款期限代码
//     *@description 相关说明
//     *@time    创建时间:2014-8-13下午02:24:32
//     *@param row 输入GYJsSfzVO
//     *@param swjgDm swjgDm
//     *@return 行vo
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static GYJsSfzVO getMrqxFromZsxmpmqxgzb(GYJsSfzVO row, String swjgDm) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        final String zsxmDm = row.getZsxmDm();
//        final String zspmDm = row.getZspmDm();
//        String nsqxDm = !GyUtils.isNull(row.getNsqxDm()) ? row.getNsqxDm() : "";
//
//        Boolean found = Boolean.FALSE;
//        if (zsxmDm.equals(GYDm01Constants.getDmGyZsxmYhs()) && !GyUtils.isNull(row.getSkssqq())
//                && SwordDateUtils.parseDate(row.getSkssqq()).compareTo(SwordDateUtils.parseDate("2022-07-01")) < 0) {
//            //印花税2022-07-01 以前直接查询查询CS_DJ_MRQXGZ
//            return getMrqxFromMrqxgz(rs, swjgDm);
//        }
//        if (GyUtils.isNull(nsqxDm) && !GyUtils.isNull(row.getSkssqq()) && !GyUtils.isNull(row.getSkssqz())) {
//            nsqxDm = GYSbCxsUtils.calcNsqxBySsq(row.getSkssqq(), row.getSkssqz());
//        }
//        Map<String, Object> mrqxgzMap = null;
//        if (!GyUtils.isNull(nsqxDm)) {
//            for (String temp : bjsjSwjgList) {
//                mrqxgzMap = getCacheZpxmPmQxPzb(temp, zsxmDm, zspmDm, nsqxDm, row.getSkssqq(), row.getSkssqz());
//                if (GyUtils.isNull(mrqxgzMap)) {
//                    mrqxgzMap = getCacheZpxmPmQxPzb(temp, zsxmDm, "%%%%%%%%%", nsqxDm, row.getSkssqq(), row.getSkssqz());
//
//                }
//                if (!GyUtils.isNull(mrqxgzMap)) {
//                    rs.setNsqxDm((String) mrqxgzMap.get("NSQX_DM"));
//                    rs.setSbqxDm((String) mrqxgzMap.get("SBQX_DM"));
//                    rs.setJkqxDm((String) mrqxgzMap.get("JKQX_DM"));
//                    found = Boolean.TRUE;
//                    if (GyUtils.isNull(mrqxgzMap.get("SBQX_DM")) || GyUtils.isNull(mrqxgzMap.get("JKQX_DM"))) {
//                        found = Boolean.FALSE;
//                    }
//                    break;
//                }
//            }
//        }
//
//        if (!GyUtils.isNull(nsqxDm) && !"11".equals(nsqxDm) && GyUtils.isNull(mrqxgzMap)) {
//            final Set<String> zsxmSet = GYSbUtils.getZsxmSetFromZsxmpmqxgzbQG();
//            GYSbSdsUtils.throwEx(zsxmSet.contains(zsxmDm), "征收项目:" + zsxmDm + ",纳税期限代码:" + nsqxDm + "在CS_GY_ZSXMPMQXGZB_QG表中不支持，请检查");
//        }
//
//        if (!found) {  //CS_GY_ZSXMPMQXGZB未获取到 查询CS_DJ_MRQXGZ
//            rs = getMrqxFromMrqxgz(rs, swjgDm);
//        }
//        return rs;
//    }
//    /**
//     *@name    获取房土车默认纳税期限/申报期限/缴款期限代码
//     *@description 相关说明
//     *@time    创建时间:2015年9月28日下午3:26:20
//     *@param swjgDm swjgDm
//     *@param zsxmDm swjgDm
//     *@param zspmDm swjgDm
//     *@param nsrlx 0 纳税人  1自然人
//     *@param hyDm hyDm
//     *@return 期限map
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static Map<String, Object> getFtcMrqx(String swjgDm, String zsxmDm, String zspmDm, String nsrlx, String hyDm) throws SwordBaseCheckedException {
//        String swjgsrDm = swjgDm;
//        Boolean flag = true;
//        Map<String, Object> mrzMap = new HashMap<String, Object>();
//        while (flag) {
//            boolean hyFlag = true;
//            String srhyDm = hyDm;
//            while (hyFlag) {
//                mrzMap = (Map<String, Object>) SwordCacheUtils.getFromKV("CS_SB_FTC_MRQXGZ", new Object[] { swjgsrDm, zsxmDm, zspmDm, nsrlx, srhyDm });
//                if (mrzMap == null || mrzMap.isEmpty()) {
//                    mrzMap = (Map<String, Object>) SwordCacheUtils.getFromKV("CS_SB_FTC_MRQXGZ", new Object[] { swjgsrDm, zsxmDm, "%%%%%%%%%", nsrlx, srhyDm });
//                }
//                //要考虑行业的统配,行业统配分两种情况：1、征收品目不统配 2、征收品目统配
//                if (mrzMap == null || mrzMap.isEmpty()) {
//                    mrzMap = (Map<String, Object>) SwordCacheUtils.getFromKV("CS_SB_FTC_MRQXGZ", new Object[] { swjgsrDm, zsxmDm, zspmDm, nsrlx, "%%%%" });
//                }
//                if (mrzMap == null || mrzMap.isEmpty()) {
//                    mrzMap = (Map<String, Object>) SwordCacheUtils.getFromKV("CS_SB_FTC_MRQXGZ", new Object[] { swjgsrDm, zsxmDm, "%%%%%%%%%", nsrlx, "%%%%" });
//                }
//                if (mrzMap == null || mrzMap.isEmpty()) {
//                    final Map<String, Object> sjhyMap = HxzgQxUtils.getMcByDmbAndDm("DM_GY_HY", srhyDm);
//                    if (sjhyMap != null && !sjhyMap.isEmpty()) {
//                        final String sjhyDm = (String) sjhyMap.get("SJHY_DM");
//                        if (sjhyDm == null || "".equals(sjhyDm)) {
//                            break;//退出 行业代码级别的循环
//                        }
//                        srhyDm = sjhyDm;
//                    } else {
//                        break;
//                    }
//                } else {
//                    hyFlag = false;
//                    flag = false;
//                }
//            }
//            if (mrzMap == null || mrzMap.isEmpty()) {
//                final Map<String, Object> sjswjgMap = HxzgQxUtils.getMcByDmbAndDm("DM_GY_SWJG", swjgsrDm);
//                if (sjswjgMap != null && !sjswjgMap.isEmpty()) {
//                    final String sjswjgDm = (String) sjswjgMap.get("SJSWJG_DM");
//                    if (sjswjgDm == null || "".equals(sjswjgDm)) {
//                        return null;
//                    }
//                    swjgsrDm = sjswjgDm;
//                } else {
//                    return null;
//                }
//            } else {
//                flag = false;
//            }
//        }
//        return mrzMap;
//    }
//
//    /**
//     *@name    获取税务机关下所有可能的附税
//     *@description 描述
//     *@time    创建时间:2014-9-16下午04:58:21
//     *@param csMap 参数
//     *@return mapList，内含 zsxmDm,zspmDm

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static List<Map<String, Object>> getAllFsBySwjg(Map<String, Object> csMap) throws SwordBaseCheckedException {
//        List<Map<String, Object>> fsList = new ArrayList<Map<String, Object>>();
//        final String swjgDm = (String) csMap.get("swjgDm");
//        //校验必传参数
//        if (GYCastUtils.isNull(swjgDm)) {
//            return fsList;
//        }
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            final List<Map<String, Object>> fsKvList = findListInCache(bjsjSwjgDm, "SWJG_DM", "CS_RD_ZFSDZB");//根据税务机关过滤出结果
//            if (notNull(fsKvList)) {
//                for (Map<String, Object> fskvMap : fsKvList) {
//                    final Map<String, Object> fsMap = new HashMap<String, Object>();
//                    String fszsxmDm = GYCastUtils.cast2Str(fskvMap.get("fszsxmDm"));//附税征收项目
//                    String fszspmDm = GYCastUtils.cast2Str(fskvMap.get("fszspmDm"));//附税征收品目
//                    if (fszsxmDm == null) {
//                        fszsxmDm = new String();
//                    }
//                    if (fszspmDm == null) {
//                        fszspmDm = new String();
//                    }
//                    fsMap.put("zsxmDm", fszsxmDm);
//                    fsMap.put("zspmDm", fszspmDm);
//                    fsList.add(fsMap);
//                }
//            }
//        }
//        // 针对指定字段去重
//        final String[] paramArray = { "zsxmDm", "zspmDm" };
//        final SwordMultiKeyMap sMap = new SwordMultiKeyMap();
//        sMap.loadDataFromMapList(paramArray, fsList);
//        fsList = new ArrayList(sMap.values());
//        return fsList;
//    }
//
//    /**
//     *@name    主税品目确定，或点计算附加税
//     *@description 各类场景均通过pzMap配置特定参数实现
//     *场景1：添加附税行并计税
//     *场景2：仅计税，不添加附税行，用于附税行已存在的场景
//     *场景3：带出默认期限的情况 并计算属期、期限的场景
//     *@time    创建时间:2014-8-14上午12:42:29
//     *@param zsRow 主税行map
//     *@param csMap 配置map
//     *@return grid
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> jsGridByZs(GYJsSfzVO zsRow, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final List<GYJsSfzVO> grid = new ArrayList<GYJsSfzVO>();
//        final String isMrqx = (String) csMap.get("isMrqx");//是否默认期限
//        final String isJsAll = (String) csMap.get("isJsAll");//是否全部计算：应税所得率、税率、征收率等
//        //String isJsSlzsl =(String)pzMap.get("isJsSlzsl");//是否计算：税率、征收率等
//        //String jsJsYssdl =(String)pzMap.get("jsJsYssdl");//是否计算：应税所得率
//        //String isOnlyJs =(String)pzMap.get("isOnlyJs");//是否仅计税，不带附税 TODO
//        //添加主税行
//        grid.add(zsRow);
//        //带出附税行
//        final List<GYJsSfzVO> fsList = getFsByZs(zsRow, csMap);
//        //添加附税行
//        grid.addAll(fsList);
//        //取率再计算
//        if ("Y".equals(isJsAll)) {
//            for (GYJsSfzVO row : grid) {
//                getSlzsl(row, csMap);//取得各类率
//            }
//        }
//        //取得默认期限代码
//        if ("Y".equals(isMrqx)) {
//            for (GYJsSfzVO row : grid) {
//                getMrqx(row, csMap);//取得默认期限代码
//            }
//        }
//        //基本公式计算
//        jsGridJsyjLd(grid, csMap);
//        //计算起征点
//        jsWdqzd(grid, csMap);
//        return grid;
//    }
//
//    /**
//     *@name    根据主税获取附税
//     *@description 仅限于附税，如需一并带出 附带征税 需另写方法实现
//     *@time    创建时间:2014-8-13下午03:17:55
//     *@param zsRow 输入GYJsSfzVO
//     *@param csMap 参数Map
//     *@return List<Map<String,Object>>
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> getFsByZs(GYJsSfzVO zsRow, Map<String, Object> csMap) throws SwordBaseCheckedException {
//
//        final List<GYJsSfzVO> fsList = new ArrayList<GYJsSfzVO>();
//        if (zsRow == null) {
//            return fsList;
//        }
//        createRow(zsRow);//赋值ID
//        final String swjgDm = (String) csMap.get("swjgDm");
//        final String zsZsxmDm = zsRow.getZsxmDm();
//        final String zsZspmDm = zsRow.getZspmDm();
//        //以下为可选项
//        final String zsZszmDm = zsRow.getZszmDm();
//        final String zdXzqhqyxzDm = (String) csMap.get("xzqhqyxzDm");//行政区划区域性质代码（指定行政区划区域性质代码）
//        final String xzqhszDm = (String) csMap.get("xzqhszDm");//行政区划数字代码（用于确定城建税具体品目）
//        final Date zsSkssqq = cast2Date(zsRow.getSkssqq());//税款所属期起
//        final Date zsSkssqz = cast2Date(zsRow.getSkssqz());//税款所属期止
//        final String zsNsqxDm = zsRow.getNsqxDm();//纳税期限代码
//        final String zsSbqxDm = zsRow.getSbqxDm();//申报期限代码
//        final String zsJkqxDm = zsRow.getJkqxDm();//缴款期限代码
//        final Date zsSbqx = cast2Date(zsRow.getSbqx());//申报期限
//        final Date zsJkqx = cast2Date(zsRow.getJkqx());//缴款期限
//        final Double zsYsx = cast2Double(zsRow.getYsx());//应税项或收入额
//        if (zsZsxmDm == null || zsZsxmDm.length() == 0 || zsZspmDm == null || zsZspmDm.length() == 0) {
//            return fsList;
//        }
//        //根据行政区划、参数表获取行政区划性质
//        String xzqhqyxzDm = "1";//默认为市区
//        //如果指定了行政区划区域性质代码
//        if (GYCastUtils.notNull(zdXzqhqyxzDm)) {
//            xzqhqyxzDm = zdXzqhqyxzDm;
//        }
//        //否则，根据行政区划代码 查找 行政区划区域性质代码
//        else if (xzqhszDm != null && xzqhszDm.length() > 0) {
//            final String hcXzqhqyxzDm = GYCastUtils.cast2Str(SwordCacheUtils.getDefaultValueFromKV("CS_RD_XZQHQYXZDZB", xzqhszDm));
//            if (GYCastUtils.notNull(hcXzqhqyxzDm)) {
//                xzqhqyxzDm = hcXzqhqyxzDm;
//            }
//            /*
//            //此种效率查缓存较低
//            final Map<String, Object> xzqhqyxzDmMap = findSingleInCache(xzqhszDm, "XZQHSZ_DM", "CS_RD_XZQHQYXZDZB");
//            if (xzqhqyxzDmMap != null && !xzqhqyxzDmMap.isEmpty()) {
//                xzqhqyxzDm = (String) xzqhqyxzDmMap.get("xzqhqyxzDm");
//            }
//            */
//        }
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        //根据主税带出附税及附带征税征收项目、征收品目
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            List<Map<String, Object>> fsKvList = findListInCache(bjsjSwjgDm, "SWJG_DM", "CS_RD_ZFSDZB");//根据税务机关过滤出结果
//            fsKvList = findListInMapList("zsxmDm", zsZsxmDm, fsKvList);//根据征收项目再次过滤
//            if (fsKvList != null && fsKvList.size() > 0) {
//                for (Map<String, Object> fsKvMap : fsKvList) {
//                    final String zspmDm = (String) fsKvMap.get("zspmDm");
//                    final String fsZsxmDm = (String) fsKvMap.get("fszsxmDm");
//                    final String fsZspmDm = (String) fsKvMap.get("fszspmDm");
//                    final String yxbz = (String) fsKvMap.get("yxbz");
//                    final String hzbz = (String) fsKvMap.get("hzbz");
//                    //附税场景
//                    //000000000表示品目通配，其它只要前几位匹配即可
//                    if ("Y".equals(yxbz) && (zsZspmDm.startsWith(zspmDm.trim()) || "000000000".equals(zspmDm))) {
//                        final Map<String, Object> fscsMap = new HashMap<String, Object>();
//                        fscsMap.put("zsxmDm", fsZsxmDm);
//                        fscsMap.put("zspmDm", fsZspmDm);
//                        fscsMap.put("hzbz", hzbz);
//                        collectFjs(fscsMap, fsList, xzqhqyxzDm, zsRow);//根据各场景填装附税列表，并在此创建附税行
//                    }
//                }
//            }
//        }
//        //将附税行填上 主税项目、品目、子目 以便主附税关联
//        for (GYJsSfzVO fsRow : fsList) {
//            fsRow.setZsZsxmDm(zsZsxmDm);//主税征收项目
//            fsRow.setZsZspmDm(zsZspmDm);//主税征收品目
//            fsRow.setZsZszmDm(zsZszmDm);//主税征收子目
//            if (isNull(fsRow.getZsxxid())) {
//                fsRow.setZsxxid(zsRow.getXxid());//主税信息ID
//            }
//        }
//        //根据主税刷新附税税款所属期
//        if (zsSkssqq != null && zsSkssqz != null) {
//            for (GYJsSfzVO fsRow : fsList) {
//                fsRow.setSkssqq(cast2Str(zsSkssqq));
//                fsRow.setSkssqz(cast2Str(zsSkssqz));
//            }
//        }
//        //申报期限、缴款期限
//        if (zsSbqx != null && zsJkqx != null) {
//            for (GYJsSfzVO fsRow : fsList) {
//                fsRow.setSbqx(cast2Str(zsSbqx));
//                fsRow.setJkqx(cast2Str(zsJkqx));
//            }
//        }
//        //纳税期限代码、申报期限代码、缴款期限代码
//        if (GYCastUtils.notNull(zsNsqxDm) && GYCastUtils.notNull(zsSbqxDm) && GYCastUtils.notNull(zsJkqxDm)) {
//            for (GYJsSfzVO fsRow : fsList) {
//                fsRow.setNsqxDm(zsNsqxDm);
//                fsRow.setSbqxDm(zsSbqxDm);
//                fsRow.setJkqxDm(zsJkqxDm);
//            }
//        }
//        //应税项、收入
//        if (zsYsx != null) {
//            for (GYJsSfzVO fsRow : fsList) {
//                fsRow.setYsx(cast2Str(zsYsx));
//            }
//        }
//        //附税 税率、征收率
//        for (GYJsSfzVO fsRow : fsList) {
//            //如果附税非税费种认定数据，则获取各类率
//            if (GYCastUtils.isNull(fsRow.getRdpzuuid())) {
//                GYJsUtils.getSlzsl(fsRow, csMap);
//            }
//        }
//
//        //如果主税非税费种认定数据，则获取各类率
//        if (GYCastUtils.isNull(zsRow.getRdpzuuid())) {
//            GYJsUtils.getSlzsl(zsRow, csMap);
//        }
//        //如是附税，不直接更新收入。其它都根据应税项-减除项 更新计税依据（附税计税依据由主税触发计算）
//        //且有品目的情况下，开始联动刷新后续计税
//        if (GYCastUtils.isNull(zsRow.getZsxxid()) && GYCastUtils.notNull(zsRow.getZspmDm())) {
//            GYJsUtils.jsRowJcxLd(zsRow, csMap);//调用减除项联动公式，计算减除项后的所有单元格
//        }
//        //根据主税计算附税
//        final Map<String, Object> inMap = new HashMap<String, Object>();
//        inMap.put("swjgDm", swjgDm);
//        jsFsByZs(fsList, zsRow, inMap);
//
//        return fsList;
//    }
//
//    /**
//     *@name    根据主税计算附税
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午08:32:37
//     *@param grid 表格
//     *@param zsRow 主税行
//     *@param csMap 参数map
//     *@return 表格

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> jsFsByZs(final List<GYJsSfzVO> grid, GYJsSfzVO zsRow, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String swjgDm = (String) csMap.get(GYJsConstants.getkSwjgdm());//税务机关代码
//        //必要参数判空
//        if (isNull(grid) || isNull(zsRow) || isNull(swjgDm)) {
//            return grid;
//        }
//        createRow(zsRow);//赋值xxid
//        final String zsXxid = zsRow.getXxid();
//        final Date zsSkssqq = cast2Date(zsRow.getSkssqq());//税款所属期起
//        final Date zsSkssqz = cast2Date(zsRow.getSkssqz());//税款所属期止
//        final String zsNsqxDm = zsRow.getNsqxDm();//纳税期限代码
//        final String zsSbqxDm = zsRow.getSbqxDm();//申报期限代码
//        final String zsJkqxDm = zsRow.getJkqxDm();//缴款期限代码
//        final Date zsSbqx = cast2Date(zsRow.getSbqx());//申报期限
//        final Date zsJkqx = cast2Date(zsRow.getJkqx());//缴款期限
//        //根据主税对附税进行计税
//
//        for (GYJsSfzVO fsRow : grid) {
//            final String fsZsxxid = fsRow.getZsxxid();//附税行的主税id
//            //如果附税行的主税id与主税行id匹配，进行联动计算
//            if (GYCastUtils.equal(zsXxid, fsZsxxid)) {
//                //根据主税刷新附税税款所属期
//                if (zsSkssqq != null && zsSkssqz != null) {
//                    fsRow.setSkssqq(cast2Str(zsSkssqq));
//                    fsRow.setSkssqz(cast2Str(zsSkssqz));
//                }
//                //申报期限、缴款期限
//                if (zsSbqx != null && zsJkqx != null) {
//                    fsRow.setSbqx(cast2Str(zsSbqx));
//                    fsRow.setJkqx(cast2Str(zsJkqx));
//                }
//                //纳税期限代码、申报期限代码、缴款期限代码
//                if (GYCastUtils.notNull(zsNsqxDm) && GYCastUtils.notNull(zsSbqxDm) && GYCastUtils.notNull(zsJkqxDm)) {
//                    fsRow.setNsqxDm(zsNsqxDm);
//                    fsRow.setSbqxDm(zsSbqxDm);
//                    fsRow.setJkqxDm(zsJkqxDm);
//                }
//                //调用单行计算附税
//                jsFsByZsSingle(zsRow, fsRow, csMap);
//
//                //联动计算附税行的 应纳后面的值
//                jsRowYnseLd(fsRow, csMap);
//            }
//        }
//        return grid;
//    }
//
//    /**
//     *@name    根据主税计算附税（Grid整体计算）
//     *@description 其中可能有多个主税，之前需调用relateZfsInSfzrd或relateZfsByZszspm关联主附税
//     *@time    创建时间:2014-9-2上午07:40:43
//     *@param grid 表格（含主附税）
//     *@param csMap 参数map
//     *@return 表格（含主附税）
//     *@throws SwordBaseCheckedException   .
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> jsFsByZsGrid(final List<GYJsSfzVO> grid, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String swjgDm = (String) csMap.get(GYJsConstants.getkSwjgdm());//税务机关代码
//        //可选
//        final String zfRelateType = (String) csMap.get("zfRelateType");//主附关联方式
//        final String outgridType = (String) csMap.get("outgridType");//返回grid形式
//        //必要参数判空
//        if (isNull(grid) || isNull(swjgDm)) {
//            return grid;
//        }
//        //如果是税费种认定关联方式
//        if (zfRelateType.equals("sfzrd")) {
//            relateZfsInSfzrd(grid, csMap);
//        }
//        //如果是zsZsxmDm、zsZspmDm关联方式
//        else if (zfRelateType.equals("zsZspm")) {
//            relateZfsByZszspm(grid, csMap);
//        }
//        //其它，默认为之前已处理好主附税关联
//        else {
//            LOG.debug("");
//        }
//        for (GYJsSfzVO zsrow : grid) {
//            //三大主税，且特殊计税标志 不为 往期、同期前次
//            if ((GYDm01Constants.getDmGyZsxmZzs().equals(zsrow.getZsxmDm()) || GYDm01Constants.getDmGyZsxmXfs().equals(zsrow.getZsxmDm()) || GYDm01Constants.getDmGyZsxmYys1()
//                    .equals(zsrow.getZsxmDm())) && (!GYJsConstants.getTsjsbzTqqc().equals(zsrow.getTsjsbz()) && !GYJsConstants.getTsjsbzWq().equals(zsrow.getTsjsbz()))) {
//                jsFsByZs(grid, zsrow, csMap);//针对每个主税计算附税
//            }
//        }
//        //如果要求输出无主税
//        if ("nozs".equals(outgridType)) {
//            for (int i = grid.size() - 1; i >= 0; i--) {
//                final GYJsSfzVO zsrow = grid.get(i);
//                if (GYDm01Constants.getDmGyZsxmZzs().equals(zsrow.getZsxmDm()) || GYDm01Constants.getDmGyZsxmXfs().equals(zsrow.getZsxmDm())
//                        || GYDm01Constants.getDmGyZsxmYys1().equals(zsrow.getZsxmDm())) {
//                    grid.remove(zsrow);
//                }
//            }
//        }
//        return grid;
//    }
//
//    /**
//     *@name    单行的计算附税
//     *@description 描述
//     *@time    创建时间:2014-8-28下午08:13:42
//     *@param zsRow 主税行
//     *@param fsRow 附税行
//     *@param csMap 参数map
//     *@throws SwordBaseCheckedException   .
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void jsFsByZsSingle(GYJsSfzVO zsRow, GYJsSfzVO fsRow, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String swjgDm = (String) csMap.get("swjgDm");
//        final String fsZsxmDm = fsRow.getZsxmDm();
//        final String fsZspmDm = fsRow.getZspmDm();
//        final String lybz = cast2Str(csMap.get("lybz"));//来源标识
//        //判断附税是随主税计税依据 还是随主税应纳
//        final String fsszBz = getFsszbz(swjgDm, fsZsxmDm, fsZspmDm);
//        //如果随主税计税依据
//        Double fsJsyj = new Double("0");
//        if (GYJsConstants.getFsSzbzJsyj().equals(fsszBz)) {
//            final String fsJsyjTemp = fsRow.getJsyj();//临时记录附件税计税依据
//            //主税计税依据=附税计税依据
//            fsJsyj = cast2GsNum(zsRow.getJsyj());
//
//            //add by luoxp，只处理当前需要处理的税款，不处理未达起征点数据
//            if(!GYCastUtils.isNull(lybz) && "FPDK".equals(lybz)){
//                final String gldksquuid = cast2Str(fsRow.getGldksquuid());//关联代开申请UUID
//                if(!GYCastUtils.isNull(gldksquuid)){
//                    fsJsyj = cast2GsNum(fsJsyjTemp);
//                }
//            }
//        }
//        //其它按主税应纳税额-非特定减免
//        else {
//            //主税应纳税额-减免+特定减免=附税计税依据
//            fsJsyj = SwordMathUtils.add(SwordMathUtils.subtract(cast2GsNum(zsRow.getYnse()), cast2GsNum(zsRow.getJmse())), cast2GsNum(zsRow.getTdjmse()));
//        }
//        //根据问题IM150002018071200005修改增值税附加文化事业建设费的计税依据
//        if(GYDm01Constants.getDmGyZsxmWhsyjsf().equals(fsZsxmDm)&&GYDm01Constants.getDmGyZsxmZzs().equals(zsRow.getZsxmDm())){
//            if(!GyUtils.isNull(zsRow.getWhsyjsfjsyj())){
//                fsJsyj = cast2GsNum(zsRow.getWhsyjsfjsyj());
//            }
//        }
//        //如果是国税代征的文化事业建设费，文化事业建设费的“实缴税额”应该是含税销售额×0.03
//        final String gdslxDm = cast2Str(csMap.get("gdslxDm"));
//        if (FpglFPZdyConstants.getDmFpGdslxGs().equals(gdslxDm) && "FPDK".equals(lybz)) {
//            final String gldksquuid = cast2Str(fsRow.getGldksquuid());//关联代开申请UUID
//            final String csz = getXtcs("A0000002053000100", swjgDm);
//            final String fpdklbDm = cast2Str(csMap.get("fpdklbDm"));
//            if (GYCastUtils.notNull(csz) && csz.indexOf(fsZsxmDm) >= 0 && "02".equals(fpdklbDm)) {
//                //add by luoxp，只处理当前需要处理的税款，不处理未达起征点数据
//                if(GYCastUtils.isNull(gldksquuid)){
//                    final Double kpje = cast2GsNum(csMap.get("kpje"));
//                    fsJsyj = kpje;
//                }
//                //fsRow.setYsx(cast2Str(csMap.get("kpje")));
//            } else if (GYCastUtils.notNull(csz) && csz.indexOf(fsZsxmDm) >= 0 && !"02".equals(fpdklbDm)) {
//                //add by luoxp，只处理当前需要处理的税款，不处理未达起征点数据
//                if(GYCastUtils.isNull(gldksquuid)){
//                    Double sl = new Double("0.03");//增值税默认为0.03的征收率（大多数情况为小规模纳税人）
//                    if(GYCastUtils.notNull(zsRow.getFdsl())){
//                        sl = GYCastUtils.cast2Double(zsRow.getFdsl());
//                    }
//                    fsJsyj = SwordMathUtils.round(SwordMathUtils.multiple(cast2GsNum(zsRow.getJsyj()), SwordMathUtils.add(new Double("1"), sl)), 2);
//                }
//                //fsRow.setYsx(cast2Str(fsJsyj));
//            }
//
//        }
//        fsRow.setJsyj(cast2Str(fsJsyj));
//    }
//
//    /**
//     *@name    根据税费种认定id匹配主附税关系
//     *@description 描述
//     *@time    创建时间:2014-9-1下午11:48:46
//     *@param grid 表格
//     *@param csMap 参数map
//     *@return 主附税表格
//     *@throws SwordBaseCheckedException   .
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> relateZfsInSfzrd(final List<GYJsSfzVO> grid, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        if (GYCastUtils.isNull(grid)) {
//            return grid;
//        }
//        for (GYJsSfzVO row : grid) {
//            createRow(row);//赋值xxid
//        }
//        for (GYJsSfzVO fsrow : grid) {
//            for (GYJsSfzVO zsrow : grid) {
//                //根据标准的税费种认定Rdpzuuid、Rdzsuuid匹配父子关系
//                if (GYCastUtils.equal(zsrow.getRdpzuuid(), fsrow.getRdzsuuid())) {
//                    fsrow.setZsxxid(zsrow.getXxid());
//                    if (zsrow.getSybh() != null && !"".equals(zsrow.getSybh())) {
//                        fsrow.setSybh(zsrow.getSybh());
//                        fsrow.setSkssswjgDm(zsrow.getSkssswjgDm());
//                        fsrow.setJdxzdm(zsrow.getJdxzdm());
//                    }
//                }
//            }
//        }
//        return grid;
//    }
//
//    /**
//     *@name    根据附税中的主税征收品目、子目代码匹配主附税关系
//     *@description 描述
//     *@time    创建时间:2014-9-1下午11:50:35
//     *@param grid 表格
//     *@param csMap 参数map
//     *@return 主附税表格
//     *@throws SwordBaseCheckedException   .
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> relateZfsByZszspm(final List<GYJsSfzVO> grid, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        if (GYCastUtils.isNull(grid)) {
//            return grid;
//        }
//        for (GYJsSfzVO row : grid) {
//            createRow(row);//赋值xxid
//        }
//        for (GYJsSfzVO fsrow : grid) {
//            for (GYJsSfzVO zsrow : grid) {
//                boolean find = false;
//
//                //如果都有子目，子目相等，匹配父子关系
//                if (GYCastUtils.equal(zsrow.getZszmDm(), fsrow.getZsZszmDm())) {
//                    find = true;
//                }
//                //否则，如品目相等，匹配父子关系
//                else if (GYCastUtils.equal(zsrow.getZspmDm(), fsrow.getZsZspmDm())) {
//                    find = true;
//                }
//                if (find) {
//                    fsrow.setZsxxid(zsrow.getXxid());
//                }
//            }
//        }
//        return grid;
//    }
//
//    /**
//     *@name    附税赋值税源编号
//     *@description 描述
//     *@time    创建时间:2014-11-4上午12:32:02
//     *@param grid 表格
//     *@param csMap 参数map
//     *@return 表格
//     *@throws SwordBaseCheckedException  通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> relateZfsSy(final List<GYJsSfzVO> grid, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        if (GYCastUtils.isNull(grid)) {
//            return grid;
//        }
//        for (GYJsSfzVO fsrow : grid) {
//            for (GYJsSfzVO zsrow : grid) {
//                //如找到父亲，赋值税源编号
//                if (GYCastUtils.equal(zsrow.getXxid(), fsrow.getZsxxid())) {
//                    if (GYCastUtils.isNull(fsrow.getSybh())) {
//                        fsrow.setSybh(zsrow.getSybh());
//                    }
//                    break;
//                }
//            }
//        }
//        return grid;
//    }
//
//    /**
//     *@name    关联主附税、并赋值附税税源编号
//     *@description 描述
//     *@time    创建时间:2014-11-4上午12:32:33
//     *@param grid 表格
//     *@param csMap 参数map
//     *@return 表格

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> relateZfsInSfzrdAndSy(final List<GYJsSfzVO> grid, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        if (GYCastUtils.isNull(grid)) {
//            return grid;
//        }
//        relateZfsInSfzrd(grid, csMap);//关联主附税
//        relateZfsSy(grid, csMap);//给附税赋值主税的税源编号
//        return grid;
//    }
//
//    /**-----------------------------------------------**/
//    /**-----------------基本公式---------------------**/
//    /**-----------------------------------------------**/
//    /**
//     *@name    整体公式计算
//     *@description 相关说明
//     *@time    创建时间:2014-8-14上午12:30:13
//     *@param grid 整个表格数据
//     *@param csMap 参数map
//     *@return grid
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> jsGridJsyjLd(List<GYJsSfzVO> grid, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        if (grid != null && grid.size() > 0) {
//            for (GYJsSfzVO row : grid) {
//                jsRowJsyjLd(row, csMap, grid);
//            }
//        }
//        return grid;
//    }
//
//    /**
//     *@name    行计算：计算减除项-联动
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午07:20:25
//     *@param row 行
//     *@param csMap 参数map
//     *@param grid 表格
//     *@return 行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJcxLd(GYJsSfzVO row, Map<String, Object> csMap, List<GYJsSfzVO> grid) throws SwordBaseCheckedException {
//        jsRowJcx(row, csMap);//计算减除项
//        jsRowJsyj(row, csMap, grid);//计税依据=应税项或收入额*应税所得率-减除项（但实际 应税所得率、减除项不会同时存在）
//        jsRowSl(row, csMap);//计算税率
//        jsRowYnse(row, csMap);//应纳税额=计税依据*税率-速算扣除数
//        jsRowJmxz(row, csMap);//自动带出减免性质
//        jsRowJmse(row, csMap);//计算减免税额
//        jsRowYbtse(row, csMap);//应补退税额=应纳税额-减免税额-已缴税额 -纳税期内已缴税额
//        //TODO 单行计算需手动触发计算起征点
//        return row;
//    }
//
//    /**
//     *@name    行计算：计算减除项-联动
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午07:20:25
//     *@param row 行
//     *@param csMap 参数map
//     *@return 行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJcxLd(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        return jsRowJcxLd(row, csMap, null);
//    }
//
//    /**
//     *@name    行计算：计算计税依据-联动
//     *@description 相关说明
//     *@time    创建时间:2014-8-14上午12:29:04
//     *@param row 行数据
//     *@param csMap 参数map
//     *@param grid 表格
//     *@return row 行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJsyjLd(GYJsSfzVO row, Map<String, Object> csMap, List<GYJsSfzVO> grid) throws SwordBaseCheckedException {
//        //TODO 针对土地增值税、个人所得税，如有累进特殊计税标志，需屏蔽某些公式
//        jsRowJsyj(row, csMap, grid);//计税依据=应税项或收入额*应税所得率-减除项（但实际 应税所得率、减除项不会同时存在）
//        jsRowSl(row, csMap);//计算税率
//        jsRowYnse(row, csMap);//应纳税额=计税依据*税率-速算扣除数
//        jsRowJmxz(row, csMap);//自动带出减免性质
//        jsRowJmse(row, csMap);//计算减免税额
//        jsRowYbtse(row, csMap);//应补退税额=应纳税额-减免税额-已缴税额
//        //TODO 单行计算需手动触发计算起征点
//        return row;
//    }
//
//    /**
//     *@name    行计算：计算计税依据-联动
//     *@description 相关说明
//     *@time    创建时间:2014-8-14上午12:29:04
//     *@param row 行数据
//     *@param csMap 参数map
//     *@return row 行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJsyjLd(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        return jsRowJsyjLd(row, csMap, null);
//    }
//
//    /**
//     *@name    行计算：计算应纳税额-联动
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午08:24:13
//     *@param row 行数据
//     *@param csMap 参数map
//     *@return 行数据

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowYnseLd(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        jsRowYnse(row, csMap);//应纳税额=计税依据*税率-速算扣除数
//        jsRowJmxz(row, csMap);//自动带出减免性质
//        jsRowJmse(row, csMap);//计算减免税额
//        jsRowYbtse(row, csMap);//应补退税额=应纳税额-减免税额-已缴税额
//        return row;
//    }
//
//    /**
//     *@name    行计算：计算减免税额-联动
//     *@description 相关说明
//     *@time    创建时间:2014-8-25下午11:09:28
//     *@param row 行
//     *@param csMap 参数map
//     *@return 行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJmseLd(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        jsRowJmse(row, csMap);//计算减免税额
//        jsRowYbtse(row, csMap);//应补退税额=应纳税额-减免税额-已缴税额
//        return row;
//    }
//
//    /**
//     *@name    行计算：计算减除项
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午06:02:31
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return 行GYJsSfzVO

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJcx(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm())) {
//            final String zsxmDm = row.getZsxmDm();
//            //个税累进的情况
//            if (GYDm01Constants.getDmGyZsxmGrsds().equals(zsxmDm) && GYJsConstants.getTsjsbzGslj().equals(row.getTsjsbz())) {
//                LOG.debug("");
//                //jsGsLjslMssr(row, csMap); //TODO暂不调
//            }
//            //其它普通情况
//            else {
//                LOG.debug("");
//            }
//        }
//        return row;
//    }
//
//    /**
//     *@name    基本公式：计算计税依据
//     *@description 全场景：考虑附税根据主税计算计税依据的场景。需传入整个grid
//     *@time    创建时间:2014-8-14上午12:14:07
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@param grid 表格
//     *@return GYJsSfzVO
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJsyj(GYJsSfzVO row, Map<String, Object> csMap, List<GYJsSfzVO> grid) throws SwordBaseCheckedException {
//        final String swjgDm = (String) csMap.get("swjgDm");//税务机关代码
//        final String zrrbz = (String) csMap.get(GYJsConstants.getkZrrbz());//自然人标志
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm()) && notNull(row.getYsx())) {
//            final String zsxmDm = row.getZsxmDm();//征收项目
//            final Double ysx = cast2GsNum(row.getYsx());//应税项或收入额
//            final Double yssdl = cast2GsL(row.getYssdl());//应税所得率（所得率）
//            final Double jcx = cast2GsNum(row.getJcx());//减除项
//            final String jsbz1 = row.getJsbz1();//计税标志
//
//            //附税，根据主税信息计算计税依据
//            //boolean findZs = false;
//            if (GYCastUtils.notNull(grid) && GYCastUtils.notNull(row.getZsxxid())) {
//                final String zsxxid = row.getZsxxid();
//                for (GYJsSfzVO zsrow : grid) {
//                    if (zsxxid.equals(zsrow.getXxid())) {
//                        //findZs = true;//找到主税
//                        jsFsByZsSingle(zsrow, row, csMap);
//                        //如附税，直接返回结果即可
//                        return row;
//                    }
//                }
//            }
//            //计税依据=应税项或收入额*应税所得率-减除项（但实际 应税所得率、减除项不会同时存在）
//            final Double ljysx = SwordMathUtils.add(cast2GsNum(row.getGsljkpje()), ysx);
//            Double jsyj = SwordMathUtils.multiple(ljysx, yssdl);//应税项或收入额*应税所得率(所得率)
//            //jsyj=SwordMathUtils.add(cast2GsNum(row.getGsljkpje()), jsyj);
//            //如果是非自然人，个税，且是所得率模式，要-3500扣除额
//            if (!"Y".equals(zrrbz) && GYDm01Constants.getDmGyZsxmGrsds().equals(zsxmDm) && GYJsConstants.getJsbzSdl().equals(jsbz1)) {
//                /*Date sbrq= GYCastUtils.cast2Date(csMap.get("sbrq"));
//                if(GyUtils.isNull(sbrq)){
//                    sbrq=SwordDateUtils.getSystemCurrentTime().getTime();
//                }
//                String kce =  getKcje(sbrq);*/
//                String kce = GYJsUtils.getXtcs(SbzsSBZdyConstants.getDmGyXtcsbmGrsdsasdlkcjecsbm(), swjgDm);//取扣除额3500
//                if (GYCastUtils.isNull(kce)) {//取不到的情况，默认为0
//                    kce = "0.00";
//                }
//                final int yfkd = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(row.getSkssqq()), GYCastUtils.cast2Date(row.getSkssqz()));//根据属期 推算 跨度月份
//                jsyj = SwordMathUtils.subtract(jsyj, SwordMathUtils.multiple(GYCastUtils.cast2Double(kce), GYCastUtils.cast2Double(yfkd)));//-3500扣除额*月份跨度
//                if (jsyj != null && jsyj.doubleValue() < 0) {
//                    jsyj = new Double(0);//如果为负数，变0
//                }
//            }
//            jsyj = SwordMathUtils.subtract(jsyj, jcx);//计税依据:-减除项（允许扣除额）
//            if (ysx > 0 && jsyj != null && jsyj.doubleValue() < 0) {
//                jsyj = new Double(0);//如果为负数，变0
//            }
//            row.setJsyj(cast2Str(jsyj));
//            //土地增值税累进的情况
//            if (GYDm01Constants.getDmGyZsxmTdzzs().equals(zsxmDm) && GYJsConstants.getTsjsbzTdzzslj().equals(row.getTsjsbz())) {
//                //土地增值税累进-计算增值额（计税依据）
//                jsTdzzsLjslZze(row, csMap);
//            }
//            //其它普通情况
//            else {
//                LOG.debug("");
//            }
//            //发票代开按次征收个人所得税品目计税依据*70%
//            dealAczsGszspm(row, csMap);
//        }
//        return row;
//    }
//
//    /**
//     *@name    基本公式：计算计税依据
//     *@description 普通的，不考虑附税根据主税计算的场景
//     *@time    创建时间:2014-8-14上午12:14:07
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return GYJsSfzVO
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJsyj(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        return jsRowJsyj(row, csMap, null);
//    }
//
//    /**
//     *@name    行计算：计算税率
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午06:02:24
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return 行GYJsSfzVO

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowSl(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm())) {
//            final String zsxmDm = row.getZsxmDm();
//            //个税累进的情况
//            if (GYDm01Constants.getDmGyZsxmGrsds().equals(zsxmDm) && GYJsConstants.getTsjsbzGslj().equals(row.getTsjsbz())) {
//                //查 sb_cs_sskcs 速算扣除数表，并作匹配
//                jsGsLjslSskcs(row, csMap);
//            }
//            //土地增值税累进的情况
//            if (GYDm01Constants.getDmGyZsxmTdzzs().equals(zsxmDm) && GYJsConstants.getTsjsbzTdzzslj().equals(row.getTsjsbz())) {
//                //土地增值税累进-计算适用税率、速算扣除系数、速算扣除数
//                jsTdzzsLjslSlsskcs(row, csMap);
//            }
//            //其它普通情况
//            else {
//                LOG.debug("");
//                //发票代开，如果存在子目，且子目为累进税率，那么获取累计税率
//                final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//                if(notNull(lybz) && "FPDK".equals(lybz)){
//                    final String jsbz1 = row.getJsbz1();//计税标识
//                    final String zszmDm = row.getZszmDm();//征收子目
//                    //子目_累进税率
//                    if (GYJsConstants.getJsbzLjsl().equals(jsbz1) && notNull(zszmDm) && zszmDm.trim().length() >= 9) {
//                        jsZmLjsl(row, csMap);//计算累进税率，CS_GY_ZMLJSL，如有累进税率，覆盖之前的税率
//                    }
//                }
//            }
//        }
//        return row;
//    }
//
//    /**
//     *@name    行计算：计算应纳
//     *@description 相关说明
//     *@time    创建时间:2014-8-14上午12:13:54
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return GYJsSfzVO

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowYnse(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm()) && notNull(row.getJsyj()) && notNull(row.getFdsl())) {
//            final String zsxmDm = row.getZsxmDm();
//            //土地增值税累进的情况
//            if (GYDm01Constants.getDmGyZsxmTdzzs().equals(zsxmDm) && GYJsConstants.getTsjsbzTdzzslj().equals(row.getTsjsbz())) {
//                //土地增值税累进-计算应缴土地增值税税额（应纳税额）
//                jsTdzzsLjslYjtdzzsse(row, csMap);
//            }
//            //其它普通情况
//            else {
//                final Double jsyj = cast2GsNum(row.getJsyj());//计税依据
//                final Double fdsl = cast2GsNum(row.getFdsl());//法定税率
//                final Double sskcs = cast2GsNum(row.getSskcs());//速算扣除数
//                //应纳税额=计税依据*法定税率-速算扣除数
//                Double ynse = SwordMathUtils.subtract(SwordMathUtils.multiple(jsyj, fdsl), sskcs);//应纳税额
//                /*//印花税，1角以下四舍五入
//                if (GYDm01Constants.getDmGyZsxmYhs().equals(zsxmDm)) {
//                    ynse = SwordMathUtils.round(ynse, 1);
//                }
//                //其它正常保留两位小数
//                else {
//                    ynse = SwordMathUtils.round(ynse, 2);
//                }*/
//                final String qdDm=cast2Str(csMap.get("qdDm"));
//                if(notNull(qdDm) && "hzdkfp".equals(qdDm)){
//                    ynse = SwordMathUtils.round(ynse, 4);//新汇总代开保留四位小数
//                }else{
//                    ynse = SwordMathUtils.round(ynse, 2);//统一成两位小数（含印花税）
//                }
//                row.setYnse(cast2Str(ynse));
//            }
//            //联动计算附税 暂不在此直接联动，改为在外层判断联动
//        }
//        return row;
//    }
//
//    /**
//     *@name    行计算：计算已缴
//     *@description 相关说明
//     *@time    创建时间:2014-8-25下午11:17:42
//     *@param row 行
//     *@param csMap 参数map
//     *@return 行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowYjse(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm())) {
//            LOG.debug("");
//        }
//        return row;
//    }
//
//    /**
//     *@name    行计算：计算抵扣
//     *@description 相关说明
//     *@time    创建时间:2014-8-26上午11:15:24
//     *@param row 行
//     *@param csMap 参数map
//     *@return 行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowDksfe(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm())) {
//            LOG.debug("");
//        }
//        return row;
//    }
//
//    /**
//     *@name    行计算：计算减免税额
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午06:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return 行GYJsSfzVO

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJmse(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm()) && notNull(row.getYnse())) {
//            //TODO 减免比例、幅度等需 根据前面单元格联动计算的部分
//            final String jmbz = row.getJmbz();//减免标志
//            final Double ynse = cast2Double(row.getYnse());
//            final Double yjse = cast2GsNum(row.getYjse());
//            final Double nsqnyjse = cast2GsNum(row.getNsqnyjse());//纳税期内已缴税额
//            final Double qmJmse = SwordMathUtils.subtract(SwordMathUtils.subtract(ynse, yjse), nsqnyjse);
//            //免征、未达起征点的情况
//            if (GYJsConstants.getJmbzMz().equals(jmbz) || GYJsConstants.getJmbzWdqzd().equals(jmbz)) {
//                row.setJmse(cast2Str(qmJmse));
//            }
//            //通过减免性质获取减免税额
//            FpdkJsUtils01.setJmseByJmxz(row, csMap);
//            //设置减免
//            setJmseFwcz(row, csMap);
//            //设置减免租金分摊
//            setJmseZjft(row, csMap);
//            //设置发票代开普惠减免
//            setJmsePh(row, csMap);
//        }
//        return row;
//    }
//
//    /**
//     *@name    行计算:计算应补退税额
//     *@description 相关说明
//     *@time    创建时间:2014-8-14上午12:11:01
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return GYJsSfzVO

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowYbtse(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm()) && notNull(row.getYnse())) {
//            final String zsxmDm = row.getZsxmDm();
//            //土地增值税累进的情况
//            if (GYDm01Constants.getDmGyZsxmTdzzs().equals(zsxmDm) && GYJsConstants.getTsjsbzTdzzslj().equals(row.getTsjsbz())) {
//                //土地增值税累进-计算应补退税额（应补退税额）
//                jsTdzzsLjslYbtdzzsse(row, csMap);
//            }
//            //其它普通情况
//            else {
//                final Double ynse = cast2GsNum(row.getYnse());//应纳税额
//                final Double yjse = cast2GsNum(row.getYjse());//已缴税额
//                final Double nsqnyjse = cast2GsNum(row.getNsqnyjse());//纳税期内已缴税额
//                final Double jmse = cast2GsNum(row.getJmse());//减免税额
//                final Double dksfe = cast2GsNum(row.getDksfe());//已缴税额
//                final Double jmed = cast2GsNum(row.getPhjmse());//减免额度
//
//                //应补退税额=应纳税额-已缴税额-纳税期内已缴税额-减免税额-抵扣税额-减免额度
//                //Double ybtse = SwordMathUtils.subtract(SwordMathUtils.subtract(SwordMathUtils.subtract(SwordMathUtils.subtract(ynse, yjse), jmse), dksfe), nsqnyjse);//应补退税额
//                Double ybtse = SwordMathUtils.subtract(ynse, yjse, jmse, dksfe, nsqnyjse, jmed);
//                /*//印花税，1角以下四舍五入
//                if (GYDm01Constants.getDmGyZsxmYhs().equals(zsxmDm)) {
//                    ybtse = SwordMathUtils.round(ybtse, 1);
//                }
//                //其它正常保留两位小数
//                else {
//                    ybtse = SwordMathUtils.round(ybtse, 2);
//                }*/
//                final String qdDm=cast2Str(csMap.get("qdDm"));
//                if(notNull(qdDm) && "hzdkfp".equals(qdDm)){
//                    ybtse = SwordMathUtils.round(ybtse, 4);//新汇总代开四位小数
//                }else{
//                    ybtse = SwordMathUtils.round(ybtse, 2);//统一成两位小数（含印花税）
//                }
//                //发票代开，印花税，1角以下四舍五入
//                final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//                if(notNull(lybz) && "FPDK".equals(lybz) && FPdkUtils.yhsxsw()
//                        && GYDm01Constants.getDmGyZsxmYhs().equals(zsxmDm)){
//                    if(row.getZspmDm().equals("101110105")){
//                        //20190517
//                        ybtse = ybtse.compareTo(0.1D)<0?0:ybtse.compareTo(1.0D)<0?1:ybtse;
//                    }
//                    ybtse = SwordMathUtils.round(ybtse, 1);
//                    //解决因为四舍五入导致的负一毛钱的问题
//                    ybtse = ybtse.compareTo(-0.1D)==0?0:ybtse;
//                }
//                row.setYbtse(cast2Str(ybtse));
//            }
//        }
//        return row;
//    }
//
//    /**-----------------------------------------------**/
//    /**-----------------小业务方法---------------------**/
//    /**-----------------------------------------------**/
//    /**
//     *@name    根据xxid获得行
//     *@description 相关说明
//     *@time    创建时间:2014-8-25上午09:56:24
//     *@param grid 表格
//     *@param xxid 信息id
//     *@return 行
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO findRowById(List<GYJsSfzVO> grid, String xxid) {
//        GYJsSfzVO rs = new GYJsSfzVO();
//        if (isNull(grid) || isNull(xxid)) {
//            return rs;
//        }
//        for (GYJsSfzVO row : grid) {
//            if (xxid.equals(row.getXxid())) {
//                rs = row;
//                break;
//            }
//        }
//        return rs;
//    }
//
//    /**
//     *@name    计算子目累进税率
//     *@description 暂适用于子目，后续视情况扩展到品目
//     *@time    创建时间:2015-3-2下午02:58:50
//     *@param row 行对象
//     *@param csMap 额外的参数map
//     *@return 行对象

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unused")
//    public static GYJsSfzVO jsZmLjsl(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final GYJsSfzVO rs = row;
//        final String zsxmDm = row.getZsxmDm();//征收项目代码
//        final String zspmDm = row.getZspmDm();//征收品目代码
//        String zszmDm = row.getZszmDm();//征收子目代码
//        final Double jsyj = cast2Double(row.getJsyj());//计税依据
//        Double jsyj1 = jsyj;
//        if (notNull(zszmDm)) {
//            zszmDm = zszmDm.trim();//考虑到土地等级 不足9位 可能有空格的问题
//        }
//        final Date skssqq = cast2Date(row.getSkssqq());//所属期起
//        final Date skssqz = cast2Date(row.getSkssqz());//所属期止
//        final String swjgDm = (String) csMap.get("swjgDm");//税务机关代码
//        int zys = 12;//默认一年中发生经营行为是12个月
//        if (csMap.containsKey("zys") && csMap.get("zys") != null && !csMap.get("zys").equals("")) {
//            zys = Integer.parseInt(csMap.get("zys").toString());
//        }
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String nsqxDm = GYCastUtils.cast2Str(csMap.get("nsqxDm"));//纳税期限
//        //发票代开，纳税期限为季
//        if (GYCastUtils.notNull(lybz) && GYCastUtils.notNull(nsqxDm) && "FPDK".equals(lybz) && "2".equals(nsqxDm)) {
//            zys = 4;
//        }
//        final int yfkd = GYCastUtils.jsYfkd(skssqq, skssqz);//根据属期 推算 跨度月份
//        if (jsyj == null || GYCastUtils.isNull(swjgDm)) {
//            return rs;//必要参数没有，原样返回
//        }
//        //默认按月收入判断累进区间
//        jsyj1 = SwordMathUtils.divide(jsyj, yfkd, 2);//有些按年计收入的品目
//
//        final double yssr = cast2Double(jsyj1.toString());// 应税收入=计税依据
//
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//
//        //上溯查找
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            boolean findLjsl = false;//是否找到累进税率
//            List<Map<String, Object>> zmljslList = findListInCacheBySwjg(bjsjSwjgDm, "CS_GY_ZMLJSL");//根据税务机关查找
//            zmljslList = findListInMapList("zsxmDm", zsxmDm, zmljslList);//根据征收项目过滤
//            zmljslList = findListByLikeInMapList("zszmDm", zszmDm, zmljslList);//根据征收子目模糊匹配
//            if (zmljslList != null && zmljslList.size() > 0) {
//                //取 list中参数表 选用标志="Y" 的记录，且传入属期在参数表数据有效期范围内的
//                for (Map<String, Object> zmljslMap : zmljslList) {
//                    final String yxbz = (String) zmljslMap.get("yxbz");
//                    final String xybz = (String) zmljslMap.get("xybz");
//                    int qqts = 1;// 默认通配，赋值1
//                    int qzts = 1;// 默认通配，赋值1
//                    final Date yxqq = (Date) zmljslMap.get("yxqq");
//                    final Date yxqz = (Date) zmljslMap.get("yxqz");
//                    if (skssqq != null) {
//                        qqts = SwordDateUtils.calcDays(skssqq, yxqq);
//                    }
//                    if (skssqz != null) {
//                        qzts = SwordDateUtils.calcDays(yxqz, skssqz);
//                    }
//                    //有效，且属期在有效期内
//                    if ("Y".equals(xybz) && qqts >= 0 && qzts >= 0) {
//                        findLjsl = true;//本税务机关已找到累进税率
//                        final Double ljbzq = (Double) zmljslMap.get("ljbzq");
//                        final Double ljbzz = (Double) zmljslMap.get("ljbzz");
//                        // 累进区间比较：下包含，上不包含
//                        //20141014修改为：下不含，上含处理
//                        if ("Y".equals(yxbz) && ((ljbzq.doubleValue() < yssr && yssr <= ljbzz.doubleValue()) || (yssr == 0 && ljbzq == 0))) {
//                            final Double sskcs = cast2Double(zmljslMap.get("sskcs"));
//                            final String sysl = cast2Str(zmljslMap.get("sysl"));
//                            //适用税率不为空才赋值
//                            if (GYCastUtils.notNull(sysl)) {
//                                row.setSskcs(cast2Str(sskcs));// 速算扣除数
//                                row.setSl(cast2Str(zmljslMap.get("sysl")));// 适用税率
//                                row.setFdsl(cast2Str(zmljslMap.get("sysl")));
//                            }
//                            break;
//                        }
//                    }
//                }
//            }
//            //如果已找到累进税率，不再继续循环本上级税务机关
//            if (findLjsl) {
//                break;
//            }
//        }
//        return rs;
//    }
//
//    /**
//     *@name    查询房土附加税配置表
//     *@description 查询房土附加税配置表
//     *@time    创建时间:2015-5-19下午02:58:50
//     *@param csMap 额外的参数map
//     *@return 房土附加税配置表 信息

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unused")
//    public static List<Map<String, Object>> queryFtfjspzb(Map<String, Object> csMap) throws SwordBaseCheckedException {
//
//        final Date skssqq = cast2Date(csMap.get("skssqq"));//所属期起
//        final Date skssqz = cast2Date(csMap.get("skssqz"));//所属期止
//        final String swjgDm = (String) csMap.get("swjgDm");//税务机关代码
//        final String zsxmDm = (String) csMap.get("zsxmDm");//征收项目代码
//
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        List<Map<String, Object>> fjsList = new ArrayList<Map<String, Object>>();
//        //上溯查找
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            fjsList = findListInCacheBySwjg(bjsjSwjgDm, "CS_SB_FTPKFJSPZB");//根据税务机关查找
//            fjsList = findListInMapList("zsxmDm", zsxmDm, fjsList);//根据征收项目过滤
//            boolean findFlag = false;
//            if (fjsList != null && fjsList.size() > 0) {
//                //取 list中参数表 选用标志="Y" 的记录，且传入属期在参数表数据有效期范围内的
//                for (int i = fjsList.size()-1;i>=0;i--){
//                    final Map<String, Object> zmljslMap = fjsList.get(i);
//                    final String yxbz = (String) zmljslMap.get("yxbz");
//                    final String xybz = (String) zmljslMap.get("xybz");
//                    int qqts = 1;// 默认通配，赋值1
//                    int qzts = 1;// 默认通配，赋值1
//                    final Date yxqq = (Date) zmljslMap.get("yxqq");
//                    final Date yxqz = (Date) zmljslMap.get("yxqz");
//                    if (skssqq != null) {
//                        qqts = SwordDateUtils.calcDays(skssqq, yxqq);
//                    }
//                    if (skssqz != null) {
//                        qzts = SwordDateUtils.calcDays(yxqz, skssqz);
//                    }
//                    //有效，且属期在有效期内
//                    if ("Y".equals(xybz) && qqts >= 0 && qzts >= 0) {
//                        findFlag = true;
//                    }else{
//                        fjsList.remove(i);
//                    }
//                }
//            }
//            //如果已找到累进税率，不再继续循环本上级税务机关
//            if (findFlag) {
//                break;
//            }
//        }
//        return fjsList;
//    }
//
//    /**
//     *@name    计算个税累进税率
//     *@description 相关说明
//     *@time    创建时间:2014-8-14下午08:39:11
//     *@param row 行map
//     *@param csMap 参数map
//     *@return 行map

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsGsLjsl(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = new GYJsSfzVO();
//        //其它额外参数 csMap
//        //查sb_cs_mssr 免税收入，并作匹配
//        //jsGsLjslMssr(row,csMap);//获取免税收入  TODO 已实现，但暂不启用
//        jsRowJsyj(row, csMap);//触发计算：计税依据=应税项或收入额*应税所得率-减除项（但实际 应税所得率、减除项不会同时存在）
//        // 查 sb_cs_sskcs 速算扣除数表，并作匹配
//        jsGsLjslSskcs(row, csMap);
//        jsRowYnse(row, csMap);//触发计算：应纳税额=计税依据*税率-速算扣除数
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    计算个税累进税率-免税收入、起征点等
//     *@description 相关说明
//     *@time    创建时间:2014-8-14下午08:39:02
//     *@param row 行map
//     *@param csMap 参数map
//     *@return 行map

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unused")
//    public static GYJsSfzVO jsGsLjslMssr(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        final String swjgDm = (String) csMap.get("swjgDm");//税务机关代码
//        final String zsxmDm = row.getZsxmDm();//征收项目代码
//        final String zspmDm = row.getZspmDm();//征收品目代码
//        final Double ysx = cast2Double(row.getYsx());//应税项
//        //final Double jsyj = castToDouble(row.get(C_JSYJ));//计税依据
//        //以下为可选入参
//        String zszmDm = row.getZsZszmDm();//征收子目代码
//        if (notNull(zszmDm)) {
//            zszmDm = zszmDm.trim();//考虑到土地等级 不足9位 可能有空格的问题
//        }
//        final Date skssqq = GYCastUtils.cast2Date(row.getSkssqq());//所属期起
//        final Date skssqz = GYCastUtils.cast2Date(row.getSkssqz());//所属期止
//        //TODO 如实现，需考虑录入的收入额需折算成全年的情况
//        int zys = 12;//默认一年中发生经营行为是12个月
//        if (csMap.containsKey("zys") && csMap.get("zys") != null && !csMap.get("zys").equals("")) {
//            zys = Integer.parseInt(csMap.get("zys").toString());
//        }
//        final int yfkd = GYCastUtils.jsYfkd(skssqq, skssqz);//根据属期 推算 跨度月份
//        if (ysx == null) {
//            return rs;
//        }
//        final double sre = cast2Double(ysx.toString());// 原收入额=应税项
//        List<Map<String, Object>> mssrList = findListInCache("00000000000", "SWJG_DM", "CS_SB_MSSR");//TODO 确认有无缓存
//        mssrList = findListInMapList("zsxmDm", zsxmDm, mssrList);//根据征收项目过滤
//        mssrList = findListByLikeInMapList("zspmDm", zspmDm, mssrList);//根据征收品目模糊匹配
//        if (zszmDm != null && zszmDm.length() > 0) {
//            mssrList = findListInMapList("zszmDm", zszmDm, mssrList);//如有子目，根据征收子目过滤
//        }
//        for (Map<String, Object> mssrMap : mssrList) {
//            final String xybz = (String) mssrMap.get("xybz");
//            int qqts = 1;// 默认通配，赋值1
//            int qzts = 1;// 默认通配，赋值1
//            final Date yxqq = (Date) mssrMap.get("yxqq");
//            final Date yxqz = (Date) mssrMap.get("yxqz");
//            if (skssqq != null) {
//                qqts = SwordDateUtils.calcDays(skssqq, yxqq);
//            }
//            if (skssqz != null) {
//                qzts = SwordDateUtils.calcDays(yxqz, skssqz);
//            }
//            if (xybz.equals("Y") && qqts >= 0 && qzts >= 0) {
//                final Double ljbzq = (Double) mssrMap.get("ljbzq");
//                final Double ljbzz = (Double) mssrMap.get("ljbzz");
//                // 累进区间比较：下包含，上不包含
//                if ((ljbzq == null || ljbzq.doubleValue() <= sre) && (ljbzz == null || sre < ljbzz.doubleValue())) {
//                    if (mssrMap.get("mssr") != null) {
//                        row.setJcx(cast2Str(mssrMap.get("mssr")));//减除项=免税收入
//                    }
//                    break;
//                }
//            }
//        }
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    计算个税累进税率-阶梯税率、速算扣除数
//     *@description 相关说明
//     *@time    创建时间:2014-8-14下午08:39:05
//     *@param row 行map
//     *@param csMap 附加参数map
//     *@return 行map

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsGsLjslSskcs(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        final String zsxmDm = row.getZsxmDm();//征收项目代码
//        final String zspmDm = row.getZspmDm();//征收品目代码
//        final Double jsyj = cast2Double(row.getJsyj());//计税依据
//        Double jsyj1 = jsyj;
//        //以下为可选入参
//        String zszmDm = row.getZszmDm();//征收子目代码
//        if (notNull(zszmDm)) {
//            zszmDm = zszmDm.trim();//考虑到土地等级 不足9位 可能有空格的问题
//        }
//        final Date skssqq = cast2Date(row.getSkssqq());//所属期起
//        final Date skssqz = cast2Date(row.getSkssqz());//所属期止
//        int zys = 12;//默认一年中发生经营行为是12个月
//        if (csMap.containsKey("zys") && csMap.get("zys") != null && !csMap.get("zys").equals("")) {
//            zys = Integer.parseInt(csMap.get("zys").toString());
//        }
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String nsqxDm = GYCastUtils.cast2Str(csMap.get("nsqxDm"));//纳税期限
//        //发票代开，纳税期限为季
//        if (GYCastUtils.notNull(lybz) && GYCastUtils.notNull(nsqxDm) && "FPDK".equals(lybz) && "2".equals(nsqxDm)) {
//            zys = 4;
//        }
//        final int yfkd = GYCastUtils.jsYfkd(skssqq, skssqz);//根据属期 推算 跨度月份
//        if (jsyj == null) {
//            return rs;
//        }
//        //如果是 企事业承包承租经营所得 或 个体生产经营所得，按年套用累进区间
//        if (gsAnjs(zspmDm)) {
//            final String cdesbbz = GYCastUtils.cast2Str(csMap.get("cdesbbz"));
//            if ("Y".equals(cdesbbz)) {//超定额申报情况
//                //超定额当月前的累计所得额+超定额当月的实际所得额+超定额当月后的核定所得额
//                final double jsyjBeforehj = GyUtils.isNull(csMap.get("jsyjBeforehj")) ? 0d : GYJsUtils.cast2Double(csMap.get("jsyjBeforehj"));
//                final double jsyjAfterhj = GyUtils.isNull(csMap.get("jsyjAfterhj")) ? 0d : GYJsUtils.cast2Double(csMap.get("jsyjAfterhj"));
//                jsyj1 = SwordMathUtils.add(jsyjBeforehj, jsyj, jsyjAfterhj);
//            }else{
//                jsyj1 = SwordMathUtils.divide(SwordMathUtils.multiple(jsyj, zys), yfkd, 2);//有些按年计收入的品目
//            }
//        }
//        final double yssr = cast2Double(jsyj1.toString());// 应税收入=计税依据
//        List<Map<String, Object>> sskcsList = findListInCache("00000000000", "SWJG_DM", "CS_SB_SSKCS");//TODO 确认有无缓存
//        sskcsList = findListInMapList("zsxmDm", zsxmDm, sskcsList);//根据征收项目过滤
//        sskcsList = findListByLikeInMapList("zspmDm", zspmDm, sskcsList);//根据征收品目模糊匹配
//        //TODO CS_SB_SSKCS 暂不考虑按子目过滤的情况
//        //如有子目，且CS_SB_SSKCS中有子目，根据征收子目过滤
//        if (sskcsList != null && sskcsList.size() > 0) {
//            // 取 list中参数表 选用标志="Y" 的记录，且传入属期在参数表数据有效期范围内的
//            for (Map<String, Object> sskcsMap : sskcsList) {
//                final String xybz = (String) sskcsMap.get("xybz");
//                int qqts = 1;// 默认通配，赋值1
//                int qzts = 1;// 默认通配，赋值1
//                final Date yxqq = (Date) sskcsMap.get("yxqq");
//                final Date yxqz = (Date) sskcsMap.get("yxqz");
//                if (skssqq != null) {
//                    qqts = SwordDateUtils.calcDays(skssqq, yxqq);
//                }
//                if (skssqz != null) {
//                    qzts = SwordDateUtils.calcDays(yxqz, skssqz);
//                }
//                if (xybz.equals("Y") && qqts >= 0 && qzts >= 0) {
//                    final Double ljbzq = (Double) sskcsMap.get("ljbzq");
//                    final Double ljbzz = (Double) sskcsMap.get("ljbzz");
//                    // 累进区间比较：下包含，上不包含
//                    //20141014修改为：下不含，上含处理
//                    if ((ljbzq.doubleValue() < yssr) && (yssr <= ljbzz.doubleValue()) || (yssr == 0 && ljbzq == 0)) {
//                        Double sskcs = cast2Double(sskcsMap.get("sskcs"));
//                        //如果是 企事业承包承租经营所得 或 个体生产经营所得，按年套用累进区间
//                        if (sskcs != null && gsAnjs(zspmDm)) {
//                            final String cdesbbz = GYCastUtils.cast2Str(csMap.get("cdesbbz"));
//                            if (!"Y".equals(cdesbbz)) {//非超定额申报情况
//                                sskcs = SwordMathUtils.divide(SwordMathUtils.multiple(sskcs, yfkd), zys, 2);//有些按年计收入的品目
//                            }
//                        }
//                        row.setSskcs(cast2Str(sskcs));// 速算扣除数
//                        row.setSl(cast2Str(sskcsMap.get("sysl")));// 适用税率
//                        row.setFdsl(cast2Str(sskcsMap.get("sysl")));
//                        break;
//                    }
//                }
//            }
//        }
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    按照累计收入计算个税累进税率-阶梯税率、速算扣除数、应补退税额
//     *@description 相关说明
//     *@time    创建时间:2014-8-14下午08:39:05
//     *@param row 当前申报信息
//     *@param csMap csMap
//     *@return 行map

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsGsLjslSskcsLjjs(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//
//        GYJsSfzVO rs = row;
//        final String zsxmDm = row.getZsxmDm();//征收项目代码
//        final String zspmDm = row.getZspmDm();//征收品目代码
//        final Double jsyj = cast2Double(row.getJsyj());//计税依据
//        final Double jsyj1 = jsyj;
//        //以下为可选入参
//        String zszmDm = row.getZszmDm();//征收子目代码
//        if (notNull(zszmDm)) {
//            zszmDm = zszmDm.trim();//考虑到土地等级 不足9位 可能有空格的问题
//        }
//        final Date skssqq = cast2Date(row.getSkssqq());//所属期起
//        final Date skssqz = cast2Date(row.getSkssqz());//所属期止
//
//        final double yssr = cast2Double(jsyj1.toString());// 应税收入=计税依据
//        List<Map<String, Object>> sskcsList = findListInCache("00000000000", "SWJG_DM", "CS_SB_SSKCS");//TODO 确认有无缓存
//        sskcsList = findListInMapList("zsxmDm", zsxmDm, sskcsList);//根据征收项目过滤
//        sskcsList = findListByLikeInMapList("zspmDm", zspmDm, sskcsList);//根据征收品目模糊匹配
//        //TODO CS_SB_SSKCS 暂不考虑按子目过滤的情况
//        //如有子目，且CS_SB_SSKCS中有子目，根据征收子目过滤
//        if (sskcsList != null && sskcsList.size() > 0) {
//            // 取 list中参数表 选用标志="Y" 的记录，且传入属期在参数表数据有效期范围内的
//            for (Map<String, Object> sskcsMap : sskcsList) {
//                final String xybz = (String) sskcsMap.get("xybz");
//                int qqts = 1;// 默认通配，赋值1
//                int qzts = 1;// 默认通配，赋值1
//                final Date yxqq = (Date) sskcsMap.get("yxqq");
//                final Date yxqz = (Date) sskcsMap.get("yxqz");
//                if (skssqq != null) {
//                    qqts = SwordDateUtils.calcDays(skssqq, yxqq);
//                }
//                if (skssqz != null) {
//                    qzts = SwordDateUtils.calcDays(yxqz, skssqz);
//                }
//                if (xybz.equals("Y") && qqts >= 0 && qzts >= 0) {
//                    final Double ljbzq = (Double) sskcsMap.get("ljbzq");
//                    final Double ljbzz = (Double) sskcsMap.get("ljbzz");
//                    // 累进区间比较：下包含，上不包含
//                    //20141014修改为：下不含，上含处理
//                    if ((ljbzq.doubleValue() < yssr) && (yssr <= ljbzz.doubleValue()) || (yssr == 0 && ljbzq == 0)) {
//                        final Double sskcs = cast2Double(sskcsMap.get("sskcs"));
//                        //如果是 企事业承包承租经营所得 或 个体生产经营所得，按年套用累进区间
//                        /* if (sskcs != null && gsAnjs(zspmDm)) {
//                             sskcs = SwordMathUtils.divide(SwordMathUtils.multiple(sskcs, yfkd), zys, 2);//有些按年计收入的品目
//                         }*/
//                        row.setSskcs(cast2Str(sskcs));// 速算扣除数
//                        row.setSl(cast2Str(sskcsMap.get("sysl")));// 适用税率
//                        row.setFdsl(cast2Str(sskcsMap.get("sysl")));
//                        break;
//                    }
//                }
//            }
//        }
//        rs = row;
//        return rs;
//
//    }
//
//    /**
//     *@name    计算土地增值税累进税率
//     *@description 相关说明
//     *@time    创建时间:2014-8-14下午08:08:19
//     *@param row 输入vo
//     *@param csMap 附加参数map
//     *@return 输出vo
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsTdzzsLjsl(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        final String tsbz = (String) csMap.get("tsbz");//特殊标志
//        /*（一）增值额未超过扣除项目金额50%土地增值税税额=增值额×30%
//                    （二）增值额超过扣除项目金额50%，未超过100%的土地增值税税额=增值额×40%－扣除项目金额×5%
//                    （三）增值额超过扣除项目金额100%，未超过200%的土地增值税税额=增值额×50%－扣除项目金额×15%
//                    （四）增值额超过扣除项目金额200%土地增值税税额=增值额×60%－扣除项目金额×35%公式中的5%，15%，35%为速算扣除系数。*/
//        //zrfdcsrze=ysx
//        //kcxmjehj=jcx
//        //zze=jsyj
//        //sl=sl
//        //yjtdzzsse=ynse
//        //ybtdzzsse=ybtse
//        //对于不同参数入口：使用GYCastUtils.chooseValue方法，用来在有可能传两种参数的情况下，一个取不到，接着取另一个，兼容多口径
//        //对于不同参数出口：采取冗余方式，两种参数都塞
//
//        //转让房地产收入总额、增值额 不允许都为空
//        if (row.getYsx() == null && row.getZrfdcsrze() == null && row.getZze() == null && row.getJsyj() == null) {
//            return row;
//        }
//        //减除项不允许为空，否则不进行计算
//        if (row.getJcx() == null && row.getKcxmjehj() == null) {
//            return row;
//        }
//        //转让房地产收入总额1
//        //扣除项目金额合计4
//        //增值额 21=转让房地产收入总额1-扣除项目金额合计4
//        //某些特定场景无需此公式，直接给 增值额、扣除项目金额
//        if (!GYJsConstants.getTsbzFcjysb().equals(tsbz)) {
//            jsTdzzsLjslZze(row, csMap);
//        }
//
//        //增值额与扣除项目金额之比 22=增值额 21/扣除项目金额合计4
//        //适用税率23 （默认值）
//        //速算扣除系数24（默认值）
//        //根据 增值额与扣除项目金额之比 来匹配区间 适用税率 、速算扣除系数
//        jsTdzzsLjslSlsskcs(row, csMap);
//
//        //应缴土地增值税税额 25=(21*23)-(4*24)
//        jsTdzzsLjslYjtdzzsse(row, csMap);
//
//        //已缴土地增值税税额26
//        //应补退税额27=应缴土地增值税税额25-已缴土地增值税税额26
//        jsTdzzsLjslYbtdzzsse(row, csMap);
//
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    土地增值税累进-计算增值额（计税依据）
//     *@description 相关说明
//     *@time    创建时间:2014-8-20下午05:23:56
//     *@param row 行vo
//     *@param csMap 附加参数map
//     *@return 行vo

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsTdzzsLjslZze(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        //获取收入总额、扣除项目金额，如任一为空，不触发计算
//        final Object zrfdcsrzeObj = GYCastUtils.chooseValue(row.getZrfdcsrze(), row.getYsx());
//        final Object kcxmjehjObj = GYCastUtils.chooseValue(row.getKcxmjehj(), row.getJcx());
//        if (isNull(zrfdcsrzeObj) || isNull(kcxmjehjObj)) {
//            return row;
//        }
//        //转让房地产收入总额1
//        final Double zrfdcsrze = cast2GsNum(zrfdcsrzeObj);
//        //扣除项目金额合计4
//        final Double kcxmjehj = cast2GsNum(kcxmjehjObj);
//        //增值额 21=转让房地产收入总额1-扣除项目金额合计4
//        final Double zze = SwordMathUtils.subtract(zrfdcsrze, kcxmjehj);
//        row.setZze(cast2Str(zze));//增值额
//        row.setJsyj(cast2Str(zze));//计税依据=增值额
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    土地增值税累进-计算适用税率、速算扣除系数、速算扣除数
//     *@description 相关说明
//     *@time    创建时间:2014-8-20下午05:24:43
//     *@param row 行vo
//     *@param csMap 附加参数map
//     *@return 行vo

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsTdzzsLjslSlsskcs(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        //扣除项目金额、增值额，如任一为空，不触发计算
//        final Object kcxmjehjObj = GYCastUtils.chooseValue(row.getKcxmjehj(), row.getJcx());
//        final Object zzeObj = GYCastUtils.chooseValue(row.getZze(), row.getJsyj());
//        if (isNull(kcxmjehjObj) || isNull(zzeObj)) {
//            return row;
//        }
//        //扣除项目金额合计4
//        final Double kcxmjehj = cast2GsNum(kcxmjehjObj);
//        //增值额 21=转让房地产收入总额1-扣除项目金额合计4
//        final Double zze = cast2GsNum(zzeObj);
//        //增值额与扣除项目金额之比 22=增值额 21/扣除项目金额合计4
//        Double zzeykcxmjezb;
//        if(GYSbCxsUtils.cDouble(kcxmjehjObj) == 0d /*&& GYJsConstants.getTsbzFcjysb().equals(csMap.get("tsbz"))*/){
//            //ZOG00_202306250027 需求反馈，土增的税率不应该为0，对于这种增值额为0，导致增值额与扣除项目之比为0的情况，税率应该是最低档税率，计税依据应该为0。最终的应补退税额应该为0 。
//            zzeykcxmjezb = 0d;
//        }else{
//            zzeykcxmjezb = SwordMathUtils.divide(zze, kcxmjehj, 6);//保留6位小数
//        }
//        //适用税率23 （默认值）
//        Double sl = new Double("1");
//        //速算扣除系数24（默认值）
//        Double sskcxs = new Double("0");
//        //根据 增值额与扣除项目金额之比 来匹配区间 适用税率 、速算扣除系数
//        final Double level0 = new Double("0");
//        final Double level1 = new Double("0.5");
//        final Double level2 = new Double("1");
//        final Double level3 = new Double("2");
//        //0<=增值额与扣除项目金额之比<=0.5
//        if (zzeykcxmjezb.compareTo(level0) >= 0 && zzeykcxmjezb.compareTo(level1) <= 0) {
//            sl = new Double("0.3");//适用税率0.3
//            sskcxs = new Double("0");//速算扣除系数0
//        }
//        //0.5<增值额与扣除项目金额之比<=1
//        else if (zzeykcxmjezb.compareTo(level1) > 0 && zzeykcxmjezb.compareTo(level2) <= 0) {
//            sl = new Double("0.4");//适用税率0.4
//            sskcxs = new Double("0.05");//速算扣除系数0.05
//        }
//        //1<增值额与扣除项目金额之比<=2
//        else if (zzeykcxmjezb.compareTo(level2) > 0 && zzeykcxmjezb.compareTo(level3) <= 0) {
//            sl = new Double("0.5");//适用税率0.5
//            sskcxs = new Double("0.15");//速算扣除系数0.15
//        }
//        //2<增值额与扣除项目金额之比
//        else if (zzeykcxmjezb.compareTo(level3) > 0) {
//            sl = new Double("0.6");//适用税率0.6
//            sskcxs = new Double("0.35");//速算扣除系数0.35
//        }
//        Double sskcs = SwordMathUtils.multiple(kcxmjehj, sskcxs);//速算扣除数（表单上没有，某些特定场合用）
//        sskcs = SwordMathUtils.round(sskcs, 2);//正常保留两位小数
//        row.setZzeykcxmjezb(cast2Str(zzeykcxmjezb));//增值额与扣除项目金额之比
//        row.setSl(cast2Str(sl));//适用税率
//        row.setFdsl(cast2Str(sl));//适用税率
//        row.setSskcxs(cast2Str(sskcxs));//速算扣除系数
//        row.setSskcs(cast2Str(sskcs));//速算扣除数
//
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    土地增值税累进-计算应缴土地增值税税额（应纳税额）
//     *@description 相关说明
//     *@time    创建时间:2014-8-20下午05:24:20
//     *@param row 行vo
//     *@param csMap 附加参数map
//     *@return 行vo

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsTdzzsLjslYjtdzzsse(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        //扣除项目金额合计4
//        final Double kcxmjehj = cast2GsNum(GYCastUtils.chooseValue(row.getKcxmjehj(), row.getJcx()));
//        //增值额 21=转让房地产收入总额1-扣除项目金额合计4
//        final Double zze = cast2GsNum(GYCastUtils.chooseValue(row.getZze(), row.getJsyj()));
//        //税率
//        final Double fdsl = cast2GsNum(row.getFdsl());
//        //速算扣除系数24
//        final Double sskcxs = cast2GsNum(row.getSskcxs());
//        //速算扣除数
//        final Double sskcs = cast2GsNum(row.getSskcs());
//
//        //应缴土地增值税税额 25=(21*23)-(4*24)
//        Double yjtdzzsse = new Double("0");
//        //正常情况下套用标准公式25=(21*23)-(4*24)
//        if (sskcxs != null) {
//            yjtdzzsse = SwordMathUtils.subtract(SwordMathUtils.multiple(zze, fdsl), SwordMathUtils.multiple(kcxmjehj, sskcxs));
//        }
//        //如果有速算扣除数，直接-速算扣除数
//        else if (sskcs != null) {
//            yjtdzzsse = SwordMathUtils.subtract(SwordMathUtils.multiple(zze, fdsl), sskcs);
//        }
//        yjtdzzsse = SwordMathUtils.round(yjtdzzsse, 2);//正常保留两位小数
//        row.setYjtdzzsse(cast2Str(yjtdzzsse));//应缴土地增值税税额
//        row.setYnse(cast2Str(yjtdzzsse));//应纳税额
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    土地增值税累进-计算应补退税额（应补退税额）
//     *@description 相关说明
//     *@time    创建时间:2014-8-20下午05:25:38
//     *@param row 行vo
//     *@param csMap 附加参数map
//     *@return 行vo

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsTdzzsLjslYbtdzzsse(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        //应缴土地增值税税额 25
//        final Double yjtdzzsse = cast2GsNum(row.getYjtdzzsse());
//        //已缴土地增值税税额26
//        final Double yijtdzzsse = cast2GsNum(GYCastUtils.chooseValue(row.getYijtdzzsse(), row.getYjse()));
//        //应补退税额27=应缴土地增值税税额25-已缴土地增值税税额26
//        final Double ybtdzzsse = SwordMathUtils.subtract(yjtdzzsse, yijtdzzsse);
//        row.setYbtdzzsse(cast2Str(ybtdzzsse));//应补退税额
//        row.setYbtse(cast2Str(ybtdzzsse));//应补退税额
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *@name    汇总、填塞附加税列表
//     *@description 相关说明
//     *@time    创建时间:2014-8-13下午09:13:47
//     *@param fscsMap 附税参数Map
//     *@param fsList 将要填装的附税list
//     *@param xzqhqyxzDm 行政区划区县性质代码
//     *@param zsRow 主税行
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static void collectFjs(Map<String, Object> fscsMap, List<GYJsSfzVO> fsList, String xzqhqyxzDm, GYJsSfzVO zsRow) throws SwordBaseCheckedException {
//        if (fscsMap == null || fscsMap.isEmpty() || fsList == null) {
//            return;
//        }
//        //当前附税行数据
//        final String csZsxmDm = (String) fscsMap.get("zsxmDm");
//        final String csZspmDm = (String) fscsMap.get("zspmDm");
//        final String hzbz = (String) fscsMap.get("hzbz");
//        boolean isadd = true;//是否添加
//        //先校验是否list中已有此项目或品目
//        for (GYJsSfzVO fsRow : fsList) {
//            final String zsxmDm = fsRow.getZsxmDm();
//            final String zspmDm = fsRow.getZspmDm();
//            //如果项目、品目均匹配，说明重复，不添加
//            if (zsxmDm.equals(csZsxmDm) && zspmDm.equals(csZspmDm)) {
//                isadd = false;
//                return;
//            }
//            //如果仅项目匹配
//            else if (zsxmDm.equals(csZsxmDm) && !zspmDm.equals(csZspmDm)) {
//                //如果合并标志为N，跳出，直接添加
//                if ("N".equals(hzbz)) {
//                    isadd = true;
//                    break;
//                }
//                //如果合并标志为Y
//                else if ("Y".equals(hzbz)) {
//                    //如为城建税，根据行政区划区县性质代码确认默认品目
//                    if (csZsxmDm.equals(GYDm01Constants.getDmGyZsxmCswhjss())) {
//                        if (csZspmDm.substring(5, 7).equals("0".concat(xzqhqyxzDm))) {
//                            isadd = true;
//                            break;
//                        }
//                    }
//                    //其它如有合并标志为Y的，取首条
//                    else {
//                        //再找一下list中是否有该项目
//                        boolean findZsxm = false;
//                        for (GYJsSfzVO afsRow : fsList) {
//                            final String azsxmDm = afsRow.getZsxmDm();
//                            if (csZsxmDm.equals(azsxmDm)) {
//                                findZsxm = true;
//                                break;
//                            }
//                        }
//                        //如按项目未找到，添加
//                        if (!findZsxm) {
//                            isadd = true;
//                            break;
//                        }
//                    }
//                }
//
//            }
//        }
//        //如果城建税，即便之前list没有此项目，品目，再根据行政区划行政代码校验一下 该品目
//        if (isadd) {
//            if (csZsxmDm.equals(GYDm01Constants.getDmGyZsxmCswhjss()) && "Y".equals(hzbz)) {
//                if (csZspmDm.substring(5, 7).equals("0".concat(xzqhqyxzDm))) {
//                    isadd = true;
//                } else {
//                    isadd = false;
//                }
//            }
//        }
//        if (isadd) {
//            final GYJsSfzVO fsRow = createRow();//创建附税行
//            fsRow.setZsxmDm(csZsxmDm);
//            fsRow.setZspmDm(csZspmDm);
//            fsRow.setZsxxid(zsRow.getXxid());
//            fsList.add(fsRow);
//        }
//    }
//
//    /**
//     *@name    获取系统参数
//     *@description 上溯查找，逐级上查
//     *@time    创建时间:2014-7-29下午10:36:50
//     *@param csbm 参数编码
//     *@param inswjgdm 税务机关代码
//     *@return 系统参数值

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getXtcs(String csbm, String inswjgdm) throws SwordBaseCheckedException {
//        return GYCastUtils.getXtcs(csbm, inswjgdm);
//    }
//
//    /**
//     *@name    获取附税随征标志
//     *@description 相关说明
//     *@time    创建时间:2014-8-13下午10:59:46
//     *@param swjgDm 税务机关代码
//     *@param zsxmDm 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@return 附税随征标志
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getFsszbz(String swjgDm, String zsxmDm, String zspmDm) throws SwordBaseCheckedException {
//        String fsszBz = GYJsConstants.getFsSzbzYn();//默认：附税随主税应纳
//        final String fjsxx = getXtcs("A0000001061001006", swjgDm);//附税随征方式标志信息串
//        fsszBz = parseFjsxx(zsxmDm, zspmDm, fjsxx);
//        return fsszBz;
//    }
//    /**
//     *@name    解析附加信息串，判断是随主税何种计算方式
//     *@description 相关说明
//     *@time    创建时间:2014-8-13下午10:55:19
//     *@param zsxmDm 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@param fjsxx 附加税信息串
//     *@return fsszBz 附税随征标志
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String parseFjsxx(String zsxmDm, String zspmDm, String fjsxx) {
//        String fsszBz = GYJsConstants.getFsSzbzYn();//默认：附税随主税应纳
//        final String[] fjsxxs = fjsxx.split(",");
//        final int length = fjsxxs.length;
//        for (int i = 0; i < length; i++) {
//            final String singleFjsxx = fjsxxs[i];
//            if (singleFjsxx.indexOf("@") >= 0) {
//                if (singleFjsxx.split("@")[0].equals(zsxmDm) && singleFjsxx.indexOf(zspmDm) >= 0) {
//                    fsszBz = singleFjsxx.split(":")[1];//附税计算方式：1=随计应纳税额，0=随计税依据
//                    break;
//                }
//            } else {
//                if (singleFjsxx.split(":")[0].equals(zsxmDm)) {
//                    fsszBz = singleFjsxx.split(":")[1];//附税计算方式：1=随计应纳税额，0=随计税依据
//                    break;
//                }
//            }
//        }
//        return fsszBz;
//    }
//    /**
//     *@name    获取配置规费标志
//     *@description 相关说明
//     *@time    创建时间:2015-8-13下午10:59:46
//     *@param swjgDm 税务机关代码
//     *@param zsxmDm 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@return 规费标志
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getGfbz(String swjgDm, String zsxmDm, String zspmDm) throws SwordBaseCheckedException {
//        String gfBz = "0";//0为非规费
//        final String gfxx = getXtcs("A0000001061001401", swjgDm);//附税随征方式标志信息串
//        if (!GyUtils.isNull(gfxx)){
//            gfBz = parseGfxx(zsxmDm, zspmDm, gfxx);
//        }
//        return gfBz;
//    }
//
//    /**
//     *@name    解析规费信息
//     *@description 相关说明
//     *@time    创建时间:2014-8-13下午10:55:19
//     *@param zsxmDm 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@param gfxx 附加税信息串
//     *@return czBz 存在标志 1 存在 0 不存在
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String parseGfxx(String zsxmDm, String zspmDm, String gfxx) {
//        String czBz = "0";//默认：0 为不存在
//        final String[] gfxxs = gfxx.split(",");
//        final int length = gfxxs.length;
//        for (int i = 0; i < length; i++) {
//            final String singleGfxx = gfxxs[i];
//            if (singleGfxx.indexOf("@") >= 0) {
//                if (singleGfxx.split("@")[0].equals(zsxmDm) && singleGfxx.indexOf(zspmDm) >= 0) {
//                    czBz = "1";
//                    break;
//                }
//            } else {
//                if (singleGfxx.equals(zsxmDm)) {
//                    czBz = "1";
//                    break;
//                }
//            }
//        }
//        return czBz;
//    }
//
//    /**
//     *@name    计算未达起征点
//     *@description 按表格计算，最简单的场景
//     *@time    创建时间:2014-8-18下午03:15:03
//     *@param csMap 输入map
//     *@param grid 表格
//     *@return 表格

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> jsWdqzd(List<GYJsSfzVO> grid, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        List<GYJsSfzVO> rsGrid = grid;
//        if (!notNull(rsGrid)) {
//            return rsGrid;
//        }
//        //确定一个主税征收项目
//        final String zsZsxmDmAll = qdOnlyQzdzsZsxm(grid);
//        //统计指定主税应税项
//        final Double ysxHj = sumZdZsysx(grid, zsZsxmDmAll);
//        final String djzclxDm = GYCastUtils.cast2Str(csMap.get("djzclxDm"));
//
//        final String swjgDm = GYCastUtils.cast2Str(csMap.get("swjgDm"));
//        final String acBz = GYCastUtils.cast2Str(csMap.get("acBz"));//按次标志
//        String ayBz = GYCastUtils.cast2Str(csMap.get("ayBz"));//按月标志
//        //默认可享受按月未达起征点
//        if (GYCastUtils.isNull(ayBz)) {
//            ayBz = "Y";
//        }
//        //按次、按月互斥
//        if ("Y".equals(acBz)) {
//            ayBz = "N";
//        }
//        //final String wdqzdZg = GYCastUtils.cast2Str(csMap.get("wdqzdZg"));
//        final String tsbz = GYCastUtils.cast2Str(csMap.get("tsbz"));//特殊标志，用于某些特定分支
//
//        //起征点
//        final Double qzd = GYCastUtils.cast2Double(getQzd(swjgDm, zsZsxmDmAll, acBz));
//
//        boolean wdqzd = false;
//        //按次
//        if ("Y".equals(acBz)) {
//            wdqzd = pdWdqzd(ysxHj, qzd, false);
//        }
//        //按月
//        else if ("Y".equals(ayBz)) {
//            final Map<String, Object> inMap = new HashMap<String, Object>();
//            inMap.put("ysx", ysxHj);
//            inMap.put("qzd", qzd);
//            inMap.put("djzclxDm", djzclxDm);
//            inMap.put("wdqzdZg", ayBz);
//            inMap.put("tsbz", tsbz);
//            wdqzd = pdAyWdqzd(inMap);
//        }
//
//        //按如未达起征点，刷新减免标志，并联动调用公式处理 减免额=应纳税额-已缴税额
//        //先统一清理 减免标志=未达起征点，再进行之后的判断和赋值
//        //个税是否要考虑随之免征
//        refreshWdqzd(grid, csMap, wdqzd);
//
//        rsGrid = grid;
//        return rsGrid;
//    }
//
//    /**
//     *@name    刷新未达起征点相关项
//     *@description 刷新各行未达起征点标志，及联动计算减免、应补退税额
//     *@time    创建时间:2014-8-26上午12:24:40
//     *@param grid 表格
//     *@param csMap 参数map
//     *@param wdqzd 未达起征点标志
//     *@return 表格

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> refreshWdqzd(List<GYJsSfzVO> grid, Map<String, Object> csMap, boolean wdqzd) throws SwordBaseCheckedException {
//        return refreshWdqzdOrmz(grid, csMap, wdqzd, GYJsConstants.getJmbzWdqzd());
//        /*
//        List<GYJsSfzVO> rsGrid = grid;
//        if (!notNull(rsGrid)) {
//            return rsGrid;
//        }
//        final String swjgDm = GYCastUtils.cast2Str(csMap.get("swjgDm"));
//
//        //按如未达起征点，刷新减免标志，并联动调用公式处理 减免额=应纳税额-已缴税额
//        //先统一清理 减免标志=未达起征点，再进行之后的判断和赋值
//        //个税是否要考虑随之免征
//        for (GYJsSfzVO row : grid) {
//            final String zsxmDm = row.getZsxmDm();//征收项目代码
//            final String zsxxid = row.getZsxxid();//主税信息id
//            final String tsjsbz = row.getTsjsbz();//特殊计税标志
//            final String jmbz = row.getJmbz();//减免标志
//            //先统一清理 减免标志=未达起征点，再进行之后的判断和赋值
//            //之前减免标志=未达起征点，且非不计税的行
//            if (GYJsConstants.getJmbzWdqzd().equals(jmbz) && !bjsTsjsbz(tsjsbz)) {
//                row.setJmbz(null);
//                row.setJmse(null);
//            }
//            final GYJsSfzVO zsrow = findRowById(grid, zsxxid);//主税行
//            final String jmbzWdqzd = GYJsConstants.getJmbzWdqzd();
//            //如是往期等情况，不参与未达起征点计算及刷新
//            if (bjsTsjsbz(tsjsbz)) {
//                continue;
//            }
//            //主税（增值税、营业税） （消费税暂无未达起征点概念）
//            if (GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm) || GYDm01Constants.getDmGyZsxmYys1().equals(zsxmDm)) {
//                if (wdqzd) {
//                    row.setJmbz(jmbzWdqzd);
//                }
//            }
//            //主税（增值税、营业税） 对应的附税
//            if (notNull(zsrow) && (GYDm01Constants.getDmGyZsxmZzs().equals(zsrow.getZsxmDm()) || GYDm01Constants.getDmGyZsxmYys1().equals(zsrow.getZsxmDm()))) {
//                if (wdqzd) {
//                    row.setJmbz(jmbzWdqzd);
//                }
//            }
//            //个税（分场景设参数，随主税免征的情况）
//            final String gsSzsqzdCl = GYCastUtils.getXtcs("A0000001061001042", swjgDm);//个税是否随主税起征点处理
//            if ("1".equals(gsSzsqzdCl) && GYDm01Constants.getDmGyZsxmGrsds().equals(zsxmDm)) {
//                if (wdqzd) {
//                    row.setJmbz(jmbzWdqzd);
//                }
//            }
//
//            //联动计算 减免税额格 之后的项
//            jsRowJmseLd(row, csMap);
//        }
//
//        rsGrid = grid;
//        return rsGrid;*/
//    }
//
//    /**
//     *@name    刷新未达起征点及免征相关项
//     *@description 刷新各行未达起征点及免征标志，及联动计算减免、应补退税额
//     *@time    创建时间:2014-12-9上午01:22:19
//     *@param grid 表格
//     *@param csMap 参数map
//     *@param wdqzdOrmz 未达起征点或免征标志
//     *@param jmbzAll 全体待刷新的减免标志，也即表头选择减免税类型确定的减免标志
//     *@return 表格
//     *@throws SwordBaseCheckedException  通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<GYJsSfzVO> refreshWdqzdOrmz(List<GYJsSfzVO> grid, Map<String, Object> csMap, boolean wdqzdOrmz, String jmbzAll) throws SwordBaseCheckedException {
//        List<GYJsSfzVO> rsGrid = grid;
//        if (!notNull(rsGrid)) {
//            return rsGrid;
//        }
//        final String swjgDm = GYCastUtils.cast2Str(csMap.get("swjgDm"));
//
//        //按如未达起征点，刷新减免标志，并联动调用公式处理 减免额=应纳税额-已缴税额
//        //先统一清理 减免标志=未达起征点、免征，再进行之后的判断和赋值
//        //个税是否要考虑随之免征
//        for (GYJsSfzVO row : grid) {
//            final String zsxmDm = row.getZsxmDm();//征收项目代码
//            final String zsxxid = row.getZsxxid();//主税信息id
//            final String tsjsbz = row.getTsjsbz();//特殊计税标志
//            final String jmbz = row.getJmbz();//减免标志
//            //先统一清理 减免标志=未达起征点、免征，再进行之后的判断和赋值
//            //之前减免标志=未达起征点、免征，且非不计税的行
//            if ((GYJsConstants.getJmbzWdqzd().equals(jmbz) || GYJsConstants.getJmbzMz().equals(jmbz)) && !bjsTsjsbz(tsjsbz)) {
//                row.setJmbz(null);
//                row.setJmse(null);
//            }
//            final GYJsSfzVO zsrow = findRowById(grid, zsxxid);//主税行
//
//            //final String jmbzWdqzd = GYJsConstants.getJmbzWdqzd();
//            //如是往期等情况，不参与未达起征点计算及刷新
//            if (bjsTsjsbz(tsjsbz)) {
//                continue;
//            }
//
//            //发票代开，当本次减免税类型为 免税货物、免税劳务、免税服务时，加收的同期前次，不免征
//            final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//            if(GYCastUtils.notNull(lybz) && "FPDK".equals(lybz)){
//                final String jmslxDm = GYCastUtils.cast2Str(csMap.get("jmslxDm"));//减免税类型
//                boolean mz = false;
//                //如 减免税类型 选择 免税货物、免税劳务、免税服务，认为是免征，自动带出减免额
//                if(GYCastUtils.notNull(jmslxDm) && (FpglFPDm01Constants.getDmFpDkfpjmslxMshw().equals(jmslxDm)
//                        ||FpglFPDm01Constants.getDmFpDkfpjmslxMslw().equals(jmslxDm)
//                        ||FpglFPDm01Constants.getDmFpDkfpjmslxMsfw().equals(jmslxDm))){
//                    mz = true;
//                }
//                if (GYCastUtils.notNull(tsjsbz) && GYJsConstants.getTsjsbzTqqc().equals(tsjsbz) && mz) {
//                    continue;
//                }
//            }
//
//            //主税（增值税、营业税、消费税）
//            if (GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm) || GYDm01Constants.getDmGyZsxmYys1().equals(zsxmDm) || GYDm01Constants.getDmGyZsxmXfs().equals(zsxmDm)) {
//                if (wdqzdOrmz) {
//                    row.setJmbz(jmbzAll);
//                }
//            }
//            //主税（增值税、营业税） 对应的附税
//            if (notNull(zsrow) && (GYDm01Constants.getDmGyZsxmZzs().equals(zsrow.getZsxmDm()) || GYDm01Constants.getDmGyZsxmYys1().equals(zsrow.getZsxmDm()))) {
//                if (wdqzdOrmz) {
//                    row.setJmbz(jmbzAll);
//                }
//                //根据2021/4/9按照《附加税费起征点相关计税逻辑_修订20210408》调整，主税附征调整为根据计税依据自动计算
//                if(GYCastUtils.notNull(lybz) && "FPDK".equals(lybz)){
//                    row.setJmbz(null);
//                    row.setJmse(null);
//                }
//            }
//            //个税（分场景设参数，随主税免征的情况）
//            final String gsSzsqzdCl = GYCastUtils.getXtcs("A0000001061001042", swjgDm);//个税是否随主税起征点处理
//            if ("1".equals(gsSzsqzdCl) && GYDm01Constants.getDmGyZsxmGrsds().equals(zsxmDm)) {
//                if (wdqzdOrmz) {
//                    row.setJmbz(jmbzAll);
//                }
//            }
//
//            //联动计算 减免税额格 之后的项
//            jsRowJmseLd(row, csMap);
//        }
//
//        rsGrid = grid;
//        return rsGrid;
//    }
//
//    /**
//     *@name    统计指定主税应税项合计
//     *@description 相关说明
//     *@time    创建时间:2014-8-25下午11:47:55
//     *@param grid 表格
//     *@param zsZsxmDm 主税征收项目
//     *@return 应税项合计

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Double sumZdZsysx(List<GYJsSfzVO> grid, String zsZsxmDm) throws SwordBaseCheckedException {
//        Double ysxHj = new Double("0");
//        //表格中 主税应税项合计，并取一个 主税项目去取起征点
//        for (GYJsSfzVO row : grid) {
//            Double ysx = GYCastUtils.cast2Double(row.getYsx());
//            if (ysx == null) {
//                ysx = new Double("0");
//            }
//            final String zsxmDm = row.getZsxmDm();
//            if (zsZsxmDm.equals(zsxmDm) || zsZsxmDm.equals(zsxmDm)) {
//                ysxHj = SwordMathUtils.add(ysxHj, ysx);
//            }
//        }
//        return ysxHj;
//    }
//
//    /**
//     *@name    获取主税起征点
//     *@description 增值税、营业税销售额
//     *@time    创建时间:2014-8-18下午01:12:35
//     *@param swjgDm 税务机关代码
//     *@param zsxmDm 征收项目
//     *@param acBz 是否按次
//     *@return 起征点串
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getQzd(String swjgDm, String zsxmDm, String acBz) throws SwordBaseCheckedException {
//        String qzd = new String();
//        //按次/日
//        if ("Y".equals(acBz)) {
//            //增值税
//            if (GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm)) {
//                qzd = getXtcs("A0000001021011009", swjgDm).trim();
//            }
//            //营业税
//            else if (GYDm01Constants.getDmGyZsxmYys1().equals(zsxmDm)) {
//                qzd = getXtcs("A0000001021011004", swjgDm).trim();
//            }
//        }
//        //按月
//        else {
//            //增值税
//            if (GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm)) {
//                qzd = getXtcs("A0000001021011008", swjgDm).trim();
//            }
//            //营业税
//            else if (GYDm01Constants.getDmGyZsxmYys1().equals(zsxmDm)) {
//                qzd = getXtcs("A0000001021011003", swjgDm).trim();
//            }
//        }
//        return qzd;
//    }
//
//    /**
//     *@name    判断未达起征点
//     *@description 相关说明
//     *@time    创建时间:2014-8-18下午01:18:04
//     *@param ysx 应税项，销售收入
//     *@param qzd 起征点
//     *@param bhqzdBz 包含起征点标志
//     *@return 是否未达起征点
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean pdWdqzd(Double ysx, Double qzd, boolean bhqzdBz) {
//        boolean wdqzd = false;
//        if (notNull(ysx) && notNull(qzd)) {
//            if (ysx.compareTo(qzd) < 0 || (bhqzdBz && ysx.compareTo(qzd) == 0)) {
//                wdqzd = true;
//            }
//        }
//        return wdqzd;
//    }
//
//    /**
//     *@name    判断未达起征点
//     *@description 默认不包含起征点，即刚好等于起征点时不视为未达起征点，要缴税
//     *@time    创建时间:2014-8-18下午01:18:04
//     *@param ysx 应税项，销售收入
//     *@param qzd 起征点
//     *@return 是否未达起征点
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean pdWdqzd(Double ysx, Double qzd) {
//        return pdWdqzd(ysx, qzd, true);
//    }
//
//    /**
//     *@name    判断按月未达起征点
//     *@description 相关说明
//     *@time    创建时间:2014-8-18下午02:18:56
//     *@param req 输入map
//     *@return 是否未达起征点

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean pdAyWdqzd(Map<String, Object> req) throws SwordBaseCheckedException {
//        boolean wdqzd = false;
//        final Double ysx = GYCastUtils.cast2Double(req.get("ysx"));
//        final Double qzd = GYCastUtils.cast2Double(req.get("qzd"));
//        final String djzclxDm = GYCastUtils.cast2Str(req.get("djzclxDm"));
//        final String wdqzdZg = GYCastUtils.cast2Str(req.get("wdqzdZg"));
//        final String tsbz = GYCastUtils.cast2Str(req.get(GYJsConstants.getTsbz()));//特殊标志，用于某些特定分支
//        //if ("Y".equals(wdqzdZg) && notNull(djzclxDm)) {
//        if ("Y".equals(wdqzdZg)) {//为了支持自然人也可以进行按月减免
//            //个体
//            if (djzclxDm.startsWith("41")) {
//                wdqzd = pdWdqzd(ysx, qzd, true);
//            }
//            //企业
//            else if (!djzclxDm.startsWith("4") && !djzclxDm.startsWith("9")) {
//                //发票代开不享受未达起征点
//                if ("fpdk".equals(tsbz)) {
//                    wdqzd = false;
//                }
//                //其它普通场景
//                else {
//                    wdqzd = pdWdqzd(ysx, qzd, true);
//                }
//            }
//        }
//        return wdqzd;
//    }
//
//    /**
//     *@name    个税按年计税
//     *@description 个体生产经营所得、企事业承包
//     *按年套用累进区间
//     *@time    创建时间:2014-8-16上午02:42:26
//     *@param zspmDm 征收品目
//     *@return 是否
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean gsAnjs(String zspmDm) {
//        if (zspmDm.startsWith("1010602") || zspmDm.startsWith("1010603")) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     *@name    判断特殊计税标志中的不计税
//     *@description 完全不计税，未达起征点都不参与
//     *@time    创建时间:2014-8-25上午11:07:52
//     *@param tsjsbz 特殊计税标志
//     *@return 是否不计税
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean bjsTsjsbz(String tsjsbz) {
//        //特殊计税标志=往期，不计税（未达起征点刷新、计税依据之后的公式计算等）
//        if (GYJsConstants.getTsjsbzWq().equals(tsjsbz)) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     *@name    从表格中确定一个有起征点的主税行
//     *@description 描述
//     *@time    创建时间:2014-8-30下午09:10:23
//     *@param grid 表格
//     *@return   行
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO qdOnlyQzdzsrow(List<GYJsSfzVO> grid) {
//        final GYJsSfzVO zsrow = new GYJsSfzVO();
//        if (GYCastUtils.notNull(grid)) {
//            for (GYJsSfzVO row : grid) {
//                final String zsxmDm = row.getZsxmDm();
//                //TODO 需过滤 往期抵扣 和当期抵扣 或已缴主税的情况
//                if (GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm) || GYDm01Constants.getDmGyZsxmYys1().equals(zsxmDm)) {
//                    return row;
//                }
//            }
//        }
//        return zsrow;
//    }
//
//    /**
//     *@name    从表格中确定一个有起征点的主税征收项目
//     *@description 增值税、营业税
//     *@time    创建时间:2014-8-24上午01:30:43
//     *@param grid 表格
//     *@return 征收项目代码
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String qdOnlyQzdzsZsxm(List<GYJsSfzVO> grid) {
//        String zsZsxmDm = qdOnlyQzdzsrow(grid).getZsxmDm();
//        if (isNull(zsZsxmDm)) {
//            zsZsxmDm = new String();
//        }
//        return zsZsxmDm;
//    }
//
//    /**
//     *@name    从表格中查找有起征点的主税征收项目
//     *@description 增值税、营业税
//     *@time    创建时间:2014-8-28下午09:15:53
//     *@param grid 表格
//     *@return   征收品目代码
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String findQzdzsZspm(List<GYJsSfzVO> grid) {
//        String zsZspmDm = qdOnlyQzdzsrow(grid).getZspmDm();
//        if (isNull(zsZspmDm)) {
//            zsZspmDm = new String();
//        }
//        return zsZspmDm;
//    }
//
//    /**
//     *@name    从表格中确定一个有起征点的主税征收项目
//     *@description 增值税、营业税、消费税
//     *@time    创建时间:2014-8-24上午01:30:43
//     *@param grid 表格
//     *@return 征收项目代码
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String qdOnlyZsZsxm(List<GYJsSfzVO> grid) {
//        String zsZsxmDm = new String();
//        if (GYCastUtils.notNull(grid)) {
//            for (GYJsSfzVO row : grid) {
//                final String zsxmDm = row.getZsxmDm();
//                if (GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm) || GYDm01Constants.getDmGyZsxmXfs().equals(zsxmDm) || GYDm01Constants.getDmGyZsxmYys1().equals(zsxmDm)) {
//                    zsZsxmDm = zsxmDm;
//                    break;
//                }
//            }
//        }
//        return zsZsxmDm;
//    }
//
//    /**-----------------------------------------------**/
//    /**-----------------工具类方法---------------------**/
//    /**-----------------------------------------------**/
//
//    /**
//     *@name    更新表格中指定行、列的单元格
//     *@description 此方法暂废弃
//     *@time    创建时间:2014-8-24上午12:02:23
//     *@param sfzvoList 税费种volist
//     *@param dqxxid 当前行id
//     *@param colname 当前列名
//     *@param val 值
//     *@return 税费种voList
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unused")
//    private static List<GYJsSfzVO> updateFieldInGrid(List<GYJsSfzVO> sfzvoList, String dqxxid, String colname, String val) {
//        final List<GYJsSfzVO> rsList = sfzvoList;
//        if (notNull(sfzvoList) && notNull(dqxxid)) {
//            for (GYJsSfzVO sfzvo : sfzvoList) {
//                final String xxid = sfzvo.getXxid();
//                if (dqxxid.equals(xxid)) {
//                    LOG.debug("");
//                    //updateFieldInRow(sfzvo, colname, val);
//                }
//            }
//        }
//        return rsList;
//    }
//
//    /**
//     *@name    根据税务机关、缓存表名获得list
//     *@description 相关说明
//     *@time    创建时间:2014-8-23下午11:17:11
//     *@param req 参数map
//     *@return 缓存list

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> getHclistBySwjg(Map<String, Object> req) throws SwordBaseCheckedException {
//        List<Map<String, Object>> rsList = new ArrayList<Map<String, Object>>();
//        //税务机关不能为空
//        final String swjgDm = (String) req.get(GYJsConstants.getkSwjgdm());
//        final String tabname = (String) req.get(GYJsConstants.getkTabname());
//        if (!notNull(swjgDm) || !notNull(tabname)) {
//            return rsList;
//        }
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//
//        //逐级上查，找到某级税务机关 有记录，则返回该级数据
//        List<Map<String, Object>> kvList = new ArrayList<Map<String, Object>>();
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            kvList = findListInCacheBySwjg(bjsjSwjgDm, tabname);//根据税务机关过滤品目参数表
//            if (notNull(kvList)) {
//                rsList = kvList;
//                break;
//            }
//        }
//        return rsList;
//    }
//
//
//    /**
//     *@name    根据税务机关、缓存表名获得list
//     *@description 相关说明
//     *@time    创建时间:2014-8-23下午11:17:11
//     *@param req 参数map
//     *@return 缓存list

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> getHclistBySwjgAndZsxmDm(Map<String, Object> req) throws SwordBaseCheckedException {
//        List<Map<String, Object>> rsList = new ArrayList<Map<String, Object>>();
//        //税务机关不能为空
//        final String swjgDm = (String) req.get(GYJsConstants.getkSwjgdm());
//        final String tabname = (String) req.get(GYJsConstants.getkTabname());
//        if (!notNull(swjgDm) || !notNull(tabname)) {
//            return rsList;
//        }
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//
//        final String zsxmDm = (String) req.get("zsxmDm");
//
//        //逐级上查，找到某级税务机关 有记录，则返回该级数据
//        List<Map<String, Object>> kvList = new ArrayList<Map<String, Object>>();
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            kvList = findListInCacheBySwjg(bjsjSwjgDm, tabname);//根据税务机关过滤品目参数表
//            if (notNull(kvList) && notNull(zsxmDm)) {//通过征收项目过滤
//                for (int i = kvList.size() - 1; i >= 0; i--) {
//                    final Map<String, Object> resMap = kvList.get(i);
//                    final String zsxmDmTmp = GYCastUtils.cast2Str(resMap.get("zsxmDm"));
//                    if (!zsxmDmTmp.equals(zsxmDm)) {
//                        kvList.remove(i);
//                    }
//                }
//            }
//            if (notNull(kvList)) {
//                rsList = kvList;
//                break;
//            }
//        }
//        return rsList;
//    }
//
//    /**
//     *@name    将List中大写的key转成标准的小写驼峰式key
//     *@description 相关说明
//     *@time    创建时间:2014-8-13上午01:30:55
//     *@param inlist 输入list
//     *@return List<Map<String,Object>>
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> cast2DefKeyList(List<Map<String, Object>> inlist) {
//        return GYCastUtils.cast2DefKeyList(inlist);
//    }
//
    /**
     *@name    把Map中大写的key转成标准的小写驼峰式key
     *@description 相关说明
     *@time    创建时间:2014-8-13上午01:31:17
     *@param req 输入Map
     *@return Map<String,Object>
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static Map<String, Object> cast2DefKeyMap(Map<String, Object> req) {
        return GYCastUtils.cast2DefKeyMap(req);
    }
//
//    /**
//     *@name    把大写的key字符转成标准的小写驼峰式key字符
//     *@description 相关说明
//     *@time    创建时间:2014-8-13上午12:16:21
//     *@param oldkey 原key
//     *@return newKey
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String cast2DefKey(String oldkey) {
//        return GYCastUtils.cast2DefKey(oldkey);
//    }
//
//    /**
//     *@name    在list查找单笔有效，且期内有效的记录
//     *@description 如：查找品目、子目参数表
//     *@time    创建时间:2014-12-6下午11:40:20
//     *@param key 键，如:zspmDm
//     *@param value 值，如101060200
//     *@param skssqq 税款所属期起，可空
//     *@param skssqz 税款所属期止，可空
//     *@param list 缓存数据列表
//     *@return   找到的数据map
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> findSingleInMapListByYxq(String key, String value, Date skssqq, Date skssqz, List<Map<String, Object>> list) {
//        Map<String, Object> resMap = new HashMap<String, Object>();
//        if (list != null && list.size() > 0) {
//            for (Map<String, Object> map : list) {
//                if (map != null && !map.isEmpty()) {
//                    final String str = (String) map.get(key);
//                    boolean yxq = false;//默认期内有效
//                    final Date yxqq = (Date) map.get("yxqq");
//                    final Date yxqz = (Date) map.get("yxqz");
//                    //如果不传属期 或者 配置表中无有效期，则认为不按有效期适配
//                    if (skssqq == null || skssqz == null || yxqq == null || yxqz == null) {
//                        yxq = true;
//                    }
//                    //否则，判断属期 和有效期是否有交叉，有则视为期内有效
//                    else if (GYCastUtils.sqAcross(yxqq, yxqz, skssqq, skssqz)) {
//                        yxq = true;
//                    }
//                    //代码吻合且 期内有效，视为找到
//                    if (value.equals(str) && yxq) {
//                        resMap = map;
//                        break;
//                    }
//                }
//            }
//        }
//        return resMap;
//    }
//
//    /**
//     *@name    查找特殊职能机关对照map
//     *@description 描述
//     *@time    创建时间:2014-12-18上午01:46:17
//     *@param inGljgDm 传入的管理机关代码
//     *@param inZsxmDm 征收项目代码
//     *@param inZspmDm 征收品目代码
//     *@param skssqq 税款所属期起（可空）
//     *@param skssqz 税款所属期止（可空）
//     *@param list CS_GY_TSZNJGDZ列表
//     *@return  找到的map(CS_GY_TSZNJGDZ)
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> findTsznjgdzMap(String inGljgDm, String inZsxmDm, String inZspmDm, Date skssqq, Date skssqz, List<Map<String, Object>> list) {
//        Map<String, Object> rsMap = new HashMap<String, Object>();
//        if (GYCastUtils.isNull(list)) {
//            return rsMap;
//        }
//        //1:先按品目匹配
//        for (Map<String, Object> map : list) {
//            if (map != null && !map.isEmpty()) {
//                final String gljgDm = (String) map.get("gljgDm");
//                boolean yxq = false;//默认期内有效
//                final Date yxqq = (Date) map.get("yxqq");
//                final Date yxqz = (Date) map.get("yxqz");
//                final String zsxmDm = (String) map.get("zsxmDm");
//                final String zspmdmc = (String) map.get("zspmdmc");
//                //如果不传属期 或者 配置表中无有效期，则认为不按有效期适配
//                if (skssqq == null || skssqz == null || yxqq == null || yxqz == null) {
//                    yxq = true;
//                }
//                //否则，判断属期 和有效期是否有交叉，有则视为期内有效
//                else if (GYCastUtils.sqAcross(yxqq, yxqz, skssqq, skssqz)) {
//                    yxq = true;
//                }
//                //管理机构、项目、品目符合， 期内有效，视为找到（品目模糊匹配模式）
//                if (gljgDm.equals(inGljgDm) && GYCastUtils.notNull(zsxmDm) && zsxmDm.equals(inZsxmDm) && GYCastUtils.notNull(zspmdmc) && GYCastUtils.notNull(inZspmDm) && inZspmDm.startsWith(zspmdmc)
//                        && yxq) {
//                    rsMap = map;
//                    return rsMap;
//                }
//            }
//        }
//        //2:如1未找到，再按征收项目匹配
//        for (Map<String, Object> map : list) {
//            if (map != null && !map.isEmpty()) {
//                final String gljgDm = (String) map.get("gljgDm");
//                boolean yxq = false;//默认期内有效
//                final Date yxqq = (Date) map.get("yxqq");
//                final Date yxqz = (Date) map.get("yxqz");
//                final String zsxmDm = (String) map.get("zsxmDm");
//                final String zspmdmc = (String) map.get("zspmdmc");
//                //如果不传属期 或者 配置表中无有效期，则认为不按有效期适配
//                if (skssqq == null || skssqz == null || yxqq == null || yxqz == null) {
//                    yxq = true;
//                }
//                //否则，判断属期 和有效期是否有交叉，有则视为期内有效
//                else if (GYCastUtils.sqAcross(yxqq, yxqz, skssqq, skssqz)) {
//                    yxq = true;
//                }
//                //管理机构、征收项目符合， 期内有效，且参数表中品目为空，视为找到（征收项目匹配模式）
//                if (gljgDm.equals(inGljgDm) && GYCastUtils.notNull(zsxmDm) && zsxmDm.equals(inZsxmDm) && GYCastUtils.isNull(zspmdmc) && yxq) {
//                    rsMap = map;
//                    return rsMap;
//                }
//            }
//        }
//        //3:如2未找到，再按管理机关通配
//        for (Map<String, Object> map : list) {
//            if (map != null && !map.isEmpty()) {
//                final String gljgDm = (String) map.get("gljgDm");
//                boolean yxq = false;//默认期内有效
//                final Date yxqq = (Date) map.get("yxqq");
//                final Date yxqz = (Date) map.get("yxqz");
//                final String zsxmDm = (String) map.get("zsxmDm");
//                final String zspmdmc = (String) map.get("zspmdmc");
//                //如果不传属期 或者 配置表中无有效期，则认为不按有效期适配
//                if (skssqq == null || skssqz == null || yxqq == null || yxqz == null) {
//                    yxq = true;
//                }
//                //否则，判断属期 和有效期是否有交叉，有则视为期内有效
//                else if (GYCastUtils.sqAcross(yxqq, yxqz, skssqq, skssqz)) {
//                    yxq = true;
//                }
//                //管理机构吻合且 期内有效，且参数表中征收项目、品目为空，视为找到（税务机关通配模式）
//                if (gljgDm.equals(inGljgDm) && GYCastUtils.isNull(zsxmDm) && GYCastUtils.isNull(zspmdmc) && yxq) {
//                    rsMap = map;
//                    break;
//                }
//            }
//        }
//
//        return rsMap;
//    }
//
//    /**
//     *@name    查找特殊职能机关对照map
//     *@description 税款所属期不传入的方法入口，转调上一方法
//     *@time    创建时间:2014-12-18上午01:46:17
//     *@param inGljgDm 传入的管理机关代码
//     *@param inZsxmDm 征收项目代码
//     *@param inZspmDm 征收品目代码
//     *@param list CS_GY_TSZNJGDZ列表
//     *@return  找到的map(CS_GY_TSZNJGDZ)
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> findTsznjgdzMap(String inGljgDm, String inZsxmDm, String inZspmDm, List<Map<String, Object>> list) {
//        return findTsznjgdzMap(inGljgDm, inZsxmDm, inZspmDm, null, null, list);
//    }
//
//    /**
//     *@name    在list中查找指定项，看对应key的value是否和传入的一致
//     *@description 相关说明
//     *@time    创建时间:2014-8-12下午11:17:04
//     *@param key 键值
//     *@param value 值
//     *@param list 待查列表
//     *@return 是否找到
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static boolean findKeyInMapList(String key, String value, List<Map<String, Object>> list) {
//        boolean find = false;
//        if (list != null && list.size() > 0) {
//            for (Map<String, Object> map : list) {
//                if (map != null && !map.isEmpty()) {
//                    final String str = (String) map.get(key);
//                    if (value.equals(str)) {
//                        find = true;
//                        break;
//                    }
//                }
//            }
//        }
//        return find;
//    }
//
//    /**
//     *@name    根据指定项在list中查找数据
//     *@description 相关说明
//     *@time    创建时间:2014-8-12下午11:34:02
//     *@param key1 键值
//     *@param key2 键值
//     *@param value1 值
//     *@param value2 值
//     *@param list 待查列表
//     *@return 结果列表
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> findListInMapListNew(String key1,String key2, String value1,String value2, List<Map<String, Object>> list) {
//        final List<Map<String, Object>> reslist = new ArrayList<Map<String, Object>>();
//        if (list != null && list.size() > 0) {
//            for (Map<String, Object> map : list) {
//                if (map != null && !map.isEmpty()) {
//                    final String str1 = (String) map.get(key1);
//                    final String str2 = (String) map.get(key2);
//                    if (!GyUtils.isNull(value1) && !GyUtils.isNull(value2) && value1.equals(str1) && value2.equals(str2)) {
//                        reslist.add(map);
//                    }
//                }
//            }
//        }
//        return reslist;
//    }
//
//    /**
//     *@name    根据指定项在list中查找数据
//     *@description 相关说明
//     *@time    创建时间:2014-8-12下午11:34:02
//     *@param key 键值
//     *@param value 值
//     *@param list 待查列表
//     *@return 结果列表
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> findListInMapList(String key, String value, List<Map<String, Object>> list) {
//        final List<Map<String, Object>> reslist = new ArrayList<Map<String, Object>>();
//        if (list != null && list.size() > 0) {
//            for (Map<String, Object> map : list) {
//                if (map != null && !map.isEmpty()) {
//                    final String str = (String) map.get(key);
//                    if (!GyUtils.isNull(value) && value.equals(str)) {
//                        reslist.add(map);
//                    }
//                }
//            }
//        }
//        return reslist;
//    }
//
//    /**
//     *@name    根据指定项在list中查找数据
//     *@description 模糊匹配场景，即trim() like %
//     *@time    创建时间:2014-8-14下午08:49:26
//     *@param key 键值
//     *@param value 值
//     *@param list 待查列表
//     *@return 结果列表
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> findListByLikeInMapList(String key, String value, List<Map<String, Object>> list) {
//        final List<Map<String, Object>> reslist = new ArrayList<Map<String, Object>>();
//        if (notNull(list) && notNull(value)) {
//            for (Map<String, Object> map : list) {
//                if (map != null && !map.isEmpty()) {
//                    final String str = (String) map.get(key);
//                    //模糊匹配
//                    if (notNull(str) && value.startsWith(str.trim())) {
//                        reslist.add(map);
//                    }
//                }
//            }
//        }
//        return reslist;
//    }
//
//    /**
//     *@name    根据指定项在list中查找单行数据
//     *@description 相关说明
//     *@time    创建时间:2014-8-14下午03:46:08
//     *@param key 键值
//     *@param value 值
//     *@param list 待查列表
//     *@return 结果列表
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> findSingleInMapList(String key, String value, List<Map<String, Object>> list) {
//        Map<String, Object> resMap = new HashMap<String, Object>();
//        if (list != null && list.size() > 0) {
//            for (Map<String, Object> map : list) {
//                if (map != null && !map.isEmpty()) {
//                    final String str = (String) map.get(key);
//                    if (value.equals(str)) {
//                        resMap = map;
//                        break;
//                    }
//                }
//            }
//        }
//        return resMap;
//    }
//
//    /**
//     *@name    根据指定项在list中查找单行数据
//     *@description 相关说明
//     *@time    创建时间:2014-8-14下午03:46:08
//     *@param key 键值
//     *@param value 值
//     *@param list 待查列表
//     *@return 结果列表
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static List<Map<String, Object>> findMapListInMapList(String key, String value, List<Map<String, Object>> list) {
//        final List<Map<String, Object>> resList = new ArrayList<Map<String, Object>>();
//        if (list != null && list.size() > 0) {
//            for (Map<String, Object> map : list) {
//                if (map != null && !map.isEmpty()) {
//                    final String str = (String) map.get(key);
//                    if (value.equals(str)) {
//                        resList.add(map);
//                    }
//                }
//            }
//        }
//        return resList;
//    }
//
//    /**
//     *@name    根据税务机关筛选数据列表  暂不用
//     *@description List<Map<String, Object>>需含key为swjgDm 暂不用
//     *@time    创建时间:2014-8-9下午02:38:50
//     *@param swjgDm 税务机关
//     *@param dataList 数据列表
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unused")
//    private static void fiterListBySwjg(String swjgDm, List<Map<String, Object>> dataList) {
//        if (dataList == null || dataList.size() == 0 || swjgDm == null) {
//            return;
//        }
//        //从list中筛选出该税务机关下的记录
//        for (int i = dataList.size() - 1; i >= 0; i--) {
//            final Map<String, Object> dataMap = dataList.get(i);
//            final String rowSwjgDm = (String) dataMap.get("swjgDm");
//            if (rowSwjgDm == null || !rowSwjgDm.equals(swjgDm)) {
//                dataList.remove(dataMap);
//            }
//        }
//    }
//
    /**
     *@name    查找本级上级税务机关list
     *@description 相关说明
     *@time    创建时间:2014-8-12上午09:59:54
     *@param swjgDm 本级税务机关代码
     *@return bjsjSwjgDmList 本级上级税务机关list
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static List<String> findBjsjSwjgDmInCache(String swjgDm) {
        final List<String> bjsjSwjgDmList = new ArrayList<String>();
        bjsjSwjgDmList.add(swjgDm);
        String bjswjgDm = swjgDm;//本级税务机关
        String sjswjgDm = findSjswjgInCache(bjswjgDm);//上级税务机关
        //上溯，直到找不到上级机关为止
        while (notNull(sjswjgDm) && !bjswjgDm.equals("00000000000") && !sjswjgDm.equals(swjgDm)) {
            bjsjSwjgDmList.add(sjswjgDm);//同时添加上级税务机关
            //上溯一层机关
            bjswjgDm = sjswjgDm;//本级税务机关
            sjswjgDm = findSjswjgInCache(bjswjgDm);//上级税务机关
        }
        return bjsjSwjgDmList;
    }
//
//    /**
//     *@name    查找默认行政区划区域性质
//     *@description 描述
//     *@time    创建时间:2015-1-7上午05:44:19
//     *@param jdxzDm 街道乡镇
//     *@param swjgDm 税务机关
//     *@param ssqq 所属期起
//     *@param ssqz 所属期止
//     *@return 行政区划区域性质
//     *@throws SwordBaseCheckedException  通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String findDefaultXzqhqyxzDm(String jdxzDm, String swjgDm, Date ssqq, Date ssqz) throws SwordBaseCheckedException {
//        String defaultXzqhqyxzDm = new String();
//        List<Map<String, Object>> hcMapList = new ArrayList<Map<String, Object>>();
//        //先按jdxzDm 查找 CS_GY_XZQHQYXZ_JDXZ，如找到，返回
//        if (GYCastUtils.notNull(jdxzDm)) {
//            hcMapList = findListInCacheByGroupcol(jdxzDm, "CS_GY_XZQHQYXZ_JDXZ");
//            //默认取首条，原则上一个街道乡镇也只对应一条
//            final String val = findYxstrInHcList("xzqhqyxzDm", ssqq, ssqz, hcMapList);
//            if (GYCastUtils.notNull(val)) {
//                defaultXzqhqyxzDm = val;
//                return defaultXzqhqyxzDm;
//            }
//        }
//        //再按swjgDm 查找CS_GY_XZQHQYXZ_SWJG，按税务机关逐级上溯查找，如找到，返回
//        if (GYCastUtils.notNull(swjgDm)) {
//            //获取本级上级税务机关
//            final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//            for (String bjsjSwjgDm : bjsjSwjgList) {
//                hcMapList = findListInCacheByGroupcol(bjsjSwjgDm, "CS_GY_XZQHQYXZ_SWJG");
//                //默认取首条，原则上一个税务机关也只对应一条
//                final String val = findYxstrInHcList("xzqhqyxzDm", ssqq, ssqz, hcMapList);
//                if (GYCastUtils.notNull(val)) {
//                    defaultXzqhqyxzDm = val;
//                    return defaultXzqhqyxzDm;
//                }
//            }
//        }
//        return defaultXzqhqyxzDm;
//    }
//
//    /**
//     *@name    查找默认行政区划区域性质
//     *@description 同上一方法，所属期可不传
//     *@time    创建时间:2015-1-7上午05:44:19
//     *@param jdxzDm 街道乡镇
//     *@param swjgDm 税务机关
//     *@return 行政区划区域性质
//     *@throws SwordBaseCheckedException  通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String findDefaultXzqhqyxzDm(String jdxzDm, String swjgDm) throws SwordBaseCheckedException {
//        return findDefaultXzqhqyxzDm(jdxzDm, swjgDm, null, null);
//    }
//
//    /**
//     *@name    在缓存中找到首条有效记录
//     *@description 仅限于标准的有 xybz,yxqq,yxqz的表
//     *@time    创建时间:2015-1-7上午05:39:24
//     *@param key 值列key
//     *@param ssqq 所属期起
//     *@param ssqz 所属期止
//     *@param hclist 缓存list
//     *@return 值str
//     *@throws SwordBaseCheckedException   通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String findYxstrInHcList(String key, Date ssqq, Date ssqz, List<Map<String, Object>> hclist) throws SwordBaseCheckedException {
//        String yxstr = new String();
//        if (GYCastUtils.isNull(hclist) || GYCastUtils.isNull(key)) {
//            return yxstr;
//        }
//        for (Map<String, Object> hcmap : hclist) {
//            final String xybz = GYCastUtils.cast2Str(hcmap.get("xybz"));
//            final Date yxqq = GYCastUtils.cast2Date(hcmap.get("yxqq"));
//            final Date yxqz = GYCastUtils.cast2Date(hcmap.get("yxqz"));
//            boolean yxq = false;
//            //任意日期为空，即不限制日期
//            if (ssqq == null || ssqz == null || yxqq == null || yxqz == null) {
//                yxq = true;
//            }
//            //否则，日期交叉，即视为有效期内
//            else if (GYCastUtils.sqAcross(ssqq, ssqz, yxqq, yxqz)) {
//                yxq = true;
//            }
//            //期内有效，且选用
//            if (yxq && "Y".equals(xybz)) {
//                final String val = GYCastUtils.cast2Str(hcmap.get(key));
//                //有值则返回，只要首条
//                if (GYCastUtils.notNull(val)) {
//                    yxstr = val;
//                    return yxstr;
//                }
//            }
//        }
//        return yxstr;
//    }
//
//    /**
//     *@name    根据税务机关查找参数表
//     *@description 暂用于项目、品目、子目三张参数表
//     *参数表中SWJG_DM需要为【分组索引列】
//     *因涉及 key 大写转驼峰式小写 在反复频繁调用的情况下有开销，针对此再做一个缓存 by 张久旭
//     *@time    创建时间:2014-8-16下午10:35:00
//     *@param swjgDm 税务机关代码
//     *@param indbTab 表名
//     *@return MapList

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> findListInCacheBySwjg(String swjgDm, String indbTab) throws SwordBaseCheckedException {
//        return findListInCacheByGroupcol(swjgDm, indbTab);
//    }
//
//    /**
//     *@name    根据缓存索引列查找参数表
//     *@description 暂用于 分组索引列
//     *参数表中 需要为【分组索引列】
//     *因涉及 key 大写转驼峰式小写 在反复频繁调用的情况下有开销，针对此再做一个缓存 by 张久旭
//     *@time    创建时间:2014-8-16下午10:35:00
//     *@param indexcol 缓存索引列名
//     *@param indbTab 表名
//     *@return MapList

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static List<Map<String, Object>> findListInCacheByGroupcol(String indexcol, String indbTab) throws SwordBaseCheckedException {
//        final List<Map<String, Object>> value;
//        final String key = indexcol + "_" + indbTab;
//        Object[] objects = swjgCsCache.get(key);
//        String cacheCurrentVersion = SwordCacheUtils.getVersion(indbTab);
//        if (objects != null && objects[0].equals(cacheCurrentVersion)) {
//            value = (List<Map<String, Object>>) objects[1];
//        } else {
//            synchronized (key.intern()) {
//                objects = swjgCsCache.get(key);
//                if (objects == null) {
//                    value = innerFindListInCacheByGroupcol(indexcol, indbTab);
//                    swjgCsCache.put(key, new Object[] { cacheCurrentVersion, value });
//                } else {
//                    cacheCurrentVersion = SwordCacheUtils.getVersion(indbTab);
//                    if (objects[0].equals(cacheCurrentVersion)) {
//                        value = (List<Map<String, Object>>) objects[1];
//                    } else {
//                        value = innerFindListInCacheByGroupcol(indexcol, indbTab);
//                        swjgCsCache.put(key, new Object[] { cacheCurrentVersion, value });
//                    }
//                }
//            }
//        }
//        if (GYCastUtils.isNull(value)) {
//            return value;
//        }else {
//            return new ArrayList<Map<String, Object>>(value);
//        }
//    }
//
//    /**
//     *@name    根据缓存索引列查找参数表（内置方法）
//     *@description 暂用于 分组索引列
//     *参数表中 需要为【分组索引列】
//     *@time    创建时间:2014-8-16下午10:35:00
//     *@param indexcol 缓存索引列名
//     *@param indbTab 表名
//     *@return MapList

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    private static List<Map<String, Object>> innerFindListInCacheByGroupcol(String indexcol, String indbTab) throws SwordBaseCheckedException {
//        List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
//        if (indexcol == null || indbTab == null) {
//            return dataList;
//        }
//        final String dbTab = indbTab.toUpperCase();//确保大写
//        //swjgDm须为索引字段或者 分组索引字段
//        final List<Map<String, Object>> kvList = (List<Map<String, Object>>) SwordCacheUtils.getFromKV(dbTab, indexcol);
//        dataList = cast2DefKeyList(kvList);//将大写下划线Key转换成标准小写Key
//        return dataList;
//    }
//
//
//    /**
//     *@name    根据缓存索引列查找参数表
//     *@description 暂用于 分组索引列
//     *参数表中 需要为【分组索引列】，且适用于多个索引列的情况
//     *因涉及 key 大写转驼峰式小写 在反复频繁调用的情况下有开销，针对此再做一个缓存 by 张久旭
//     *@time    创建时间:2014-8-16下午10:35:00
//     *@param indexcol 缓存索引列名
//     *@param indbTab 表名
//     *@return MapList

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static List<Map<String, Object>> findListInCacheByMultiGroupcol(String[] indexcol, String indbTab) throws SwordBaseCheckedException {
//        final List<Map<String, Object>> value;
//        //索引列名未串，直接返回空列表
//        if(indexcol==null||indexcol.length==0){
//            return new ArrayList<Map<String, Object>>();
//        }
//
//        String key = new String();
//        //将多个索引列+表名 拼成key
//        for(int i=0;i<indexcol.length;i++){
//            key = key + indexcol[i] + "_";
//        }
//        key = key + indbTab;
//        Object[] objects = swjgCsCache.get(key);
//        String cacheCurrentVersion = SwordCacheUtils.getVersion(indbTab);
//        if (objects != null && objects[0].equals(cacheCurrentVersion)) {
//            value = (List<Map<String, Object>>) objects[1];
//        } else {
//            synchronized (key.intern()) {
//                objects = swjgCsCache.get(key);
//                if (objects == null) {
//                    value = innerFindListInCacheByMultiGroupcol(indexcol, indbTab);
//                    swjgCsCache.put(key, new Object[] { cacheCurrentVersion, value });
//                } else {
//                    cacheCurrentVersion = SwordCacheUtils.getVersion(indbTab);
//                    if (objects[0].equals(cacheCurrentVersion)) {
//                        value = (List<Map<String, Object>>) objects[1];
//                    } else {
//                        value = innerFindListInCacheByMultiGroupcol(indexcol, indbTab);
//                        swjgCsCache.put(key, new Object[] { cacheCurrentVersion, value });
//                    }
//                }
//            }
//        }
//
//        if (GYCastUtils.isNull(value)) {
//            return value;
//        }else {
//            return new ArrayList<Map<String, Object>>(value);
//        }
//    }
//
//    /**
//     *@name    根据缓存索引列查找参数表（内置方法）
//     *@description 暂用于 分组索引列
//     *参数表中 需要为【分组索引列】，且适用于多个索引列的情况
//     *@time    创建时间:2014-8-16下午10:35:00
//     *@param indexcol 缓存索引列名
//     *@param indbTab 表名
//     *@return MapList

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    private static List<Map<String, Object>> innerFindListInCacheByMultiGroupcol(Object[] indexcol, String indbTab) throws SwordBaseCheckedException {
//        List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
//        if (indexcol == null || indexcol.length==0 || indbTab == null) {
//            return dataList;
//        }
//        final String dbTab = indbTab.toUpperCase();//确保大写
//        //swjgDm须为索引字段或者 分组索引字段
//        final List<Map<String, Object>> kvList = (List<Map<String, Object>>) SwordCacheUtils.getFromKV(dbTab,indexcol);
//        dataList = cast2DefKeyList(kvList);//将大写下划线Key转换成标准小写Key
//        return dataList;
//    }
//
//    /**
//     *@name    缓存中根据代码找单行信息
//     *@description 【尽量不用这个方法】改用SwordCacheUtils.getFromKV用索引列或者分组索引列取缓存效率更高
//     *@time    创建时间:2014-8-12下午03:30:53
//     *@param code 输入代码
//     *@param indbCol 数据库列名
//     *@param indbTab 数据库表名
//     *@return Map<String,Object>

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static Map<String, Object> findSingleInCache(String code, String indbCol, String indbTab) throws SwordBaseCheckedException {
//        Map<String, Object> dataMap = new HashMap<String, Object>();
//        if (code == null || indbCol == null || indbTab == null) {
//            return dataMap;
//        }
//        final String dbCol = indbCol.toUpperCase();
//        final String dbTab = indbTab.toUpperCase();
//        final Collection co = SwordCacheUtils.getAllDataFromKV(dbTab);
//        final Iterator it = co.iterator();
//        while (it.hasNext()) {
//            final Map<String, Object> dataKvMap = (Map<String, Object>) it.next();
//            final String eachCode = (String) dataKvMap.get(dbCol);
//            if (code.equals(eachCode)) {
//                dataMap = dataKvMap;
//                break;
//            }
//        }
//        dataMap = cast2DefKeyMap(dataMap);//将大写下划线Key转换成标准小写Key
//        return dataMap;
//    }
//
//    /**
//     *@name    缓存中根据代码找多行信息
//     *@description 【尽量不用这个方法】改用SwordCacheUtils.getFromKV用索引列或者分组索引列取缓存效率更高
//     *@time    创建时间:2014-8-12下午03:31:15
//     *@param code 输入代码
//     *@param indbCol 数据库列名
//     *@param indbTab 数据库表名
//     *@return List<Map<String,Object>>

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static List<Map<String, Object>> findListInCache(String code, String indbCol, String indbTab) throws SwordBaseCheckedException {
//        List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
//        if (code == null || indbCol == null || indbTab == null) {
//            return dataList;
//        }
//        final String dbCol = indbCol.toUpperCase();
//        final String dbTab = indbTab.toUpperCase();
//        final Collection co = SwordCacheUtils.getAllDataFromKV(dbTab);
//        final Iterator it = co.iterator();
//        while (it.hasNext()) {
//            final Map<String, Object> dataKvMap = (Map<String, Object>) it.next();
//            final String eachCode = (String) dataKvMap.get(dbCol);
//            if (code.equals(eachCode)) {
//                dataList.add(dataKvMap);
//            }
//        }
//        dataList = cast2DefKeyList(dataList);//将大写下划线Key转换成标准小写Key
//        return dataList;
//    }
//
    /**
     *@name    查找税务机关对应的上级税务机关
     *@description 相关说明
     *@time    创建时间:2014-8-12下午02:50:13
     *@param swjgDm 税务机关代码
     *@return 税务机关代码
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @SuppressWarnings("unchecked")
    public static String findSjswjgInCache(String swjgDm) {
        String sjswjgDm = new String();
        Map<String, Object> swjgMap = (Map<String, Object>) CacheUtils.getTableData(GYJsConstants.gettDmSwjg(), swjgDm);
        swjgMap = cast2DefKeyMap(swjgMap);
        //final Map<String,Object> swjgMap = findSingleInCache(swjgDm,"SWJG_DM",T_DM_SWJG);
        if (swjgMap != null && !swjgMap.isEmpty()) {
            final String eachSjswjgDm = (String) swjgMap.get("sjswjgDm");
            if (eachSjswjgDm != null) {
                sjswjgDm = eachSjswjgDm;
            }
        }
        return sjswjgDm;
    }
//
//    /**
//     *@name    转换成公式率
//     *@description 主要对率为null或0的处理，默认成1
//     *@time    创建时间:2014-8-14上午12:05:51
//     *@param obj Object
//     *@return Double
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Double cast2GsL(Object obj) throws SwordBaseCheckedException {
//        return GYCastUtils.cast2GsL(obj);
//    }
//
//    /**
//     *@name    转换成公式数字
//     *@description 主要对null的处理
//     *@time    创建时间:2014-8-14上午12:05:51
//     *@param obj Object
//     *@return Double
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Double cast2GsNum(Object obj) throws SwordBaseCheckedException {
//        return GYCastUtils.cast2GsNum(obj);
//    }
//
//    /**
//     *@name    转型成String
//     *@description 相关说明
//     *@time    创建时间:2014-7-10下午10:49:00
//     *@param obj 对象
//     *@return String
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String cast2Str(Object obj) {
//        return GYCastUtils.cast2Str(obj);
//    }
//
//    /**
//     *@name    转型成日期
//     *@description 相关说明
//     *@time    创建时间:2014-7-10下午10:49:49
//     *@param obj 对象
//     *@return Date
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Date cast2Date(Object obj) throws SwordBaseCheckedException {
//        return GYCastUtils.cast2Date(obj);
//    }
//
//    /**
//     *@name    转型成Double
//     *@description 相关说明
//     *@time    创建时间:2014-7-10下午10:49:53
//     *@param obj 对象
//     *@return Double
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Double cast2Double(Object obj) throws SwordBaseCheckedException {
//        return GYCastUtils.cast2Double(obj);
//    }
//
//    /**
//     *@name    转型成BigDecimal
//     *@description 相关说明
//     *@time    创建时间:2014-7-10下午10:49:57
//     *@param obj 对象
//     *@return BigDecimal
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static BigDecimal cast2BigDecimal(Object obj) throws SwordBaseCheckedException {
//        return GYCastUtils.cast2BigDecimal(obj);
//    }
//
//    /**
//     *@name    转型成Long
//     *@description 相关说明
//     *@time    创建时间:2014-7-10下午10:49:53
//     *@param obj 对象
//     *@return Long
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Long cast2Long(Object obj) throws SwordBaseCheckedException {
//        return GYCastUtils.cast2Long(obj);
//    }
//
    /**
     *@name    判断常用对象非空
     *@description 相关说明
     *@time    创建时间:2014-8-15上午03:04:26
     *@param obj 对象
     *@return 是否非空
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static boolean notNull(Object obj) {
        return GYCastUtils.notNull(obj);
    }
//
//    /**
//     *@name    判断常用对象为空
//     *@description 相关说明
//     *@time    创建时间:2014-8-15上午03:04:26
//     *@param obj 对象
//     *@return 是否非空
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean isNull(Object obj) {
//        return !GYCastUtils.notNull(obj);
//    }
//
//    /**
//     *@name    判断增值税-应税服务品目
//     *@description 即营改增品目
//     *@time    创建时间:2014-10-30上午10:44:27
//     *@param zspmDm 征收品目代码
//     *@return   是否标志
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String isYsfwPm(String zspmDm) {
//        if (zspmDm.startsWith("101016") || zspmDm.startsWith("101017")) {
//            return "Y";
//        }
//        return "N";
//    }
//
//    /**-----------------------------------------------------**/
//    /**----------VO处理部分--------------------------------**/
//    /**-----------------------------------------------------**/
//
//    /**
//     *@name    创建计税vo行
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午05:21:08
//     *@return 新创建的行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO createRow() throws SwordBaseCheckedException {
//        final GYJsSfzVO row = new GYJsSfzVO();
//        row.setXxid(SwordSequenceUtils.generateRandomString());//赋值ID供后续处理用
//        return row;
//    }
//
//    /**
//     *@name    创建计税vo行
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午05:24:11
//     *@param row 原行数据
//     *@return 新创建的行

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO createRow(GYJsSfzVO row) throws SwordBaseCheckedException {
//        if (row != null && (row.getXxid() == null || row.getXxid().trim().length() == 0)) {
//            row.setXxid(SwordSequenceUtils.generateRandomString());//赋值ID供后续处理用
//        }
//        return row;
//    }
//
//    /**
//     *@name    清除行中金额项及隐藏列值
//     *@description 保留主附税关系及自身ID等
//     *@time    创建时间:2014-8-28下午07:37:42
//     *@param row 行
//     *@return   .
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO clearRowJe(GYJsSfzVO row) {
//        row.setYsx(null);
//        row.setYssdl(null);
//        row.setJcx(null);
//        row.setJsyj(null);
//        row.setSl(null);
//        row.setSskcs(null);
//        row.setYnse(null);
//        row.setYjse(null);
//        row.setNsqnyjse(null);//纳税期内已缴税额
//        row.setJmse(null);
//        row.setYbtse(null);
//        row.setNsqxDm(null);
//        row.setSbqxDm(null);//纳税期限代码
//        row.setJkqxDm(null);//缴款期限代码
//        row.setSbqx(null);//申报期限
//        row.setJkqx(null);//缴款期限
//        row.setFdsl(null);//法定税率
//        row.setSx(null);//上限（率）
//        row.setXx(null);//下限（率）
//        row.setTdjmse(null);//特定减免税额
//        row.setJmbz(null);//减免标志
//        row.setJsbz1(null);//计税标志
//        row.setFdJmse(null);//法定减免税额（如fdsl和sl不一致，此fdJmse指率差算出的差额）
//        row.setZrfdcsrze(null);//转让房地产收入总额1=ysx
//        row.setKcxmjehj(null);//扣除项目金额合计4=jcx
//        row.setZze(null);//增值额 21=转让房地产收入总额1-扣除项目金额合计4=jsyj
//        row.setZzeykcxmjezb(null);//增值额与扣除项目金额之比 22=增值额 21/扣除项目金额合计4
//        row.setSskcxs(null);//速算扣除系数24
//        row.setYjtdzzsse(null);//应缴土地增值税税额 25=(21*23)-(4*24)=ynse
//        row.setYijtdzzsse(null);//已缴土地增值税税额26=yjse
//        row.setYbtdzzsse(null);//应补退税额27=应缴土地增值税税额25-已缴土地增值税税额26=ybtse
//        row.setRdzsuuid(null);//税费种主税UUID
//        row.setJmspjguuid(null);//减免UUID
//        row.setGldksquuid(null);//关联代开申请UUID
//        row.setGlZszsxmDm(null);//关联主税征收项目代码 TODO
//        row.setGlZszspmDm(null);//关联主税征收品目代码 TODO
//        row.setDksfe(null);//抵扣税额
//        row.setRdpzuuid(null);//税费种认定uuid
//        row.setBjsbz(null);//不计税标志
//        row.setTsjsbz(null);//特殊计算标志
//        return row;
//    }
//
//    /**-----------------------------------------------------**/
//    /**----------VO-map转换部分--------------------------------**/
//    /**-----------------------------------------------------**/
//
//    /**
//     *@name    VO转换成Map
//     *@description 相关说明
//     *@time    创建时间:2014-8-20上午12:20:20
//     *@param sfzvo 税费种vo
//     *@return 税费种map
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> turnSfzMap(GYJsSfzVO sfzvo) {
//        final Map<String, Object> sfzmap = new HashMap<String, Object>();
//        sfzmap.put(GYJsConstants.getcSkssqq(), sfzvo.getSkssqq());
//        sfzmap.put(GYJsConstants.getcSkssqz(), sfzvo.getSkssqz());
//        sfzmap.put(GYJsConstants.getcHydm(), sfzvo.getHyDm());
//        sfzmap.put(GYJsConstants.getcZsxmdm(), sfzvo.getZsxmDm());
//        sfzmap.put(GYJsConstants.getcZspmdm(), sfzvo.getZspmDm());
//        sfzmap.put(GYJsConstants.getcZszmdm(), sfzvo.getZszmDm());
//        sfzmap.put(GYJsConstants.getcYsx(), sfzvo.getYsx());
//        sfzmap.put(GYJsConstants.getcYssdl(), sfzvo.getYssdl());
//        sfzmap.put(GYJsConstants.getcJcx(), sfzvo.getJcx());
//        sfzmap.put(GYJsConstants.getcJsyj(), sfzvo.getJsyj());
//        sfzmap.put(GYJsConstants.getcSl(), sfzvo.getSl());
//        sfzmap.put(GYJsConstants.getcSskcs(), sfzvo.getSskcs());
//        sfzmap.put(GYJsConstants.getcYnse(), sfzvo.getYnse());
//        sfzmap.put(GYJsConstants.getcYjse(), sfzvo.getYjse());
//        sfzmap.put(GYJsConstants.getcNsqnyjse(), sfzvo.getNsqnyjse());
//        sfzmap.put(GYJsConstants.getcJmse(), sfzvo.getJmse());
//        sfzmap.put(GYJsConstants.getcYbtse(), sfzvo.getYbtse());
//        sfzmap.put(GYJsConstants.getcNsqxdm(), sfzvo.getNsqxDm());
//        sfzmap.put(GYJsConstants.getcSbqxdm(), sfzvo.getSbqxDm());
//        sfzmap.put(GYJsConstants.getcJkqxdm(), sfzvo.getJkqxDm());
//        sfzmap.put(GYJsConstants.getcSbqx(), sfzvo.getSbqx());
//        sfzmap.put(GYJsConstants.getcJkqx(), sfzvo.getJkqx());
//        sfzmap.put(GYJsConstants.getcFdsl(), sfzvo.getFdsl());
//        sfzmap.put(GYJsConstants.getcSx(), sfzvo.getSx());
//        sfzmap.put(GYJsConstants.getcXx(), sfzvo.getXx());
//        sfzmap.put(GYJsConstants.getcTdjmse(), sfzvo.getTdjmse());
//        sfzmap.put(GYJsConstants.getcJmbz(), sfzvo.getJmbz());
//        sfzmap.put(GYJsConstants.getcXxid(), sfzvo.getXxid());
//        sfzmap.put(GYJsConstants.getcZsxxid(), sfzvo.getZsxxid());
//        sfzmap.put(GYJsConstants.getcZszsxmdm(), sfzvo.getZsZsxmDm());
//        sfzmap.put(GYJsConstants.getcZszspmdm(), sfzvo.getZsZspmDm());
//        sfzmap.put(GYJsConstants.getcZszszmdm(), sfzvo.getZsZszmDm());
//        sfzmap.put(GYJsConstants.getcJsbz1(), sfzvo.getJsbz1());
//        sfzmap.put(GYJsConstants.getcZrfdcsrze(), sfzvo.getZrfdcsrze());
//        sfzmap.put(GYJsConstants.getcKcxmjehj(), sfzvo.getKcxmjehj());
//        sfzmap.put(GYJsConstants.getcZze(), sfzvo.getZze());
//        sfzmap.put(GYJsConstants.getcZzeykcxmjezb(), sfzvo.getZzeykcxmjezb());
//        sfzmap.put(GYJsConstants.getcSskcxs(), sfzvo.getSskcxs());
//        sfzmap.put(GYJsConstants.getcYjtdzzsse(), sfzvo.getYjtdzzsse());
//        sfzmap.put(GYJsConstants.getcYijtdzzsse(), sfzvo.getYijtdzzsse());
//        sfzmap.put(GYJsConstants.getcYbtdzzsse(), sfzvo.getYbtdzzsse());
//        sfzmap.put(GYJsConstants.getcTsjsbz(), sfzvo.getTsjsbz());
//        sfzmap.put(GYJsConstants.getcDksfe(), sfzvo.getDksfe());
//        sfzmap.put(GYJsConstants.getcRdpzuuid(), sfzvo.getRdpzuuid());
//        sfzmap.put(GYJsConstants.getcBjsbz(), sfzvo.getBjsbz());
//        sfzmap.put(GYJsConstants.getcRdzsuuid(), sfzvo.getRdzsuuid());
//        sfzmap.put(GYJsConstants.getcJmspjguuid(), sfzvo.getJmspjguuid());
//        sfzmap.put(GYJsConstants.getcSsjmxzdm(), sfzvo.getSsjmxzDm());
//        sfzmap.put("zsl", sfzvo.getZsl());
//        return sfzmap;
//    }
//
//    /**
//     *@name    map转换成VO
//     *@description 相关说明
//     *@time    创建时间:2014-8-20上午12:20:05
//     *@param sfzmap 税费种map
//     *@return 税费种vo

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO turnSfzVO(Map<String, Object> sfzmap) throws SwordBaseCheckedException {
//        final GYJsSfzVO sfzvo = new GYJsSfzVO();
//        sfzvo.setSkssqq(cast2Str(sfzmap.get(GYJsConstants.getcSkssqq())));
//        sfzvo.setSkssqz(cast2Str(sfzmap.get(GYJsConstants.getcSkssqz())));
//
//        sfzvo.setHyDm(cast2Str(sfzmap.get(GYJsConstants.getcHydm())));
//        sfzvo.setZsxmDm(cast2Str(sfzmap.get(GYJsConstants.getcZsxmdm())));
//        sfzvo.setZspmDm(cast2Str(sfzmap.get(GYJsConstants.getcZspmdm())));
//        sfzvo.setZszmDm(cast2Str(sfzmap.get(GYJsConstants.getcZszmdm())));
//
//        sfzvo.setYsx(cast2Str(sfzmap.get(GYJsConstants.getcYsx())));
//        sfzvo.setYssdl(cast2Str(sfzmap.get(GYJsConstants.getcYssdl())));
//        sfzvo.setJcx(cast2Str(sfzmap.get(GYJsConstants.getcJcx())));
//        sfzvo.setJsyj(cast2Str(sfzmap.get(GYJsConstants.getcJsyj())));
//        sfzvo.setSl(cast2Str(sfzmap.get(GYJsConstants.getcSl())));
//        sfzvo.setSskcs(cast2Str(sfzmap.get(GYJsConstants.getcSskcs())));
//
//        sfzvo.setYnse(cast2Str(sfzmap.get(GYJsConstants.getcYnse())));
//        sfzvo.setYjse(cast2Str(sfzmap.get(GYJsConstants.getcYjse())));
//        sfzvo.setNsqnyjse(cast2Str(sfzmap.get(GYJsConstants.getcNsqnyjse())));
//        sfzvo.setJmse(cast2Str(sfzmap.get(GYJsConstants.getcJmse())));
//        sfzvo.setYbtse(cast2Str(sfzmap.get(GYJsConstants.getcYbtse())));
//
//        sfzvo.setNsqxDm(cast2Str(sfzmap.get(GYJsConstants.getcNsqxdm())));
//        sfzvo.setSbqxDm(cast2Str(sfzmap.get(GYJsConstants.getcSbqxdm())));
//        sfzvo.setJkqxDm(cast2Str(sfzmap.get(GYJsConstants.getcJkqxdm())));
//
//        sfzvo.setSbqx(cast2Str(sfzmap.get(GYJsConstants.getcSbqx())));
//        sfzvo.setJkqx(cast2Str(sfzmap.get(GYJsConstants.getcJkqx())));
//
//        sfzvo.setFdsl(cast2Str(sfzmap.get(GYJsConstants.getcFdsl())));
//        sfzvo.setSx(cast2Str(sfzmap.get(GYJsConstants.getcSx())));
//        sfzvo.setXx(cast2Str(sfzmap.get(GYJsConstants.getcXx())));
//        sfzvo.setTdjmse(cast2Str(sfzmap.get(GYJsConstants.getcTdjmse())));
//
//        sfzvo.setJmbz(cast2Str(sfzmap.get(GYJsConstants.getcJmbz())));
//        sfzvo.setXxid(cast2Str(sfzmap.get(GYJsConstants.getcXxid())));
//        sfzvo.setZsxxid(cast2Str(sfzmap.get(GYJsConstants.getcZsxxid())));
//        sfzvo.setZsZsxmDm(cast2Str(sfzmap.get(GYJsConstants.getcZszsxmdm())));
//        sfzvo.setZsZspmDm(cast2Str(sfzmap.get(GYJsConstants.getcZszspmdm())));
//        sfzvo.setZsZszmDm(cast2Str(sfzmap.get(GYJsConstants.getcZszszmdm())));
//        sfzvo.setJsbz1(cast2Str(sfzmap.get(GYJsConstants.getcJsbz1())));
//
//        sfzvo.setZrfdcsrze(cast2Str(sfzmap.get(GYJsConstants.getcZrfdcsrze())));
//        sfzvo.setKcxmjehj(cast2Str(sfzmap.get(GYJsConstants.getcKcxmjehj())));
//        sfzvo.setZze(cast2Str(sfzmap.get(GYJsConstants.getcZze())));
//        sfzvo.setZzeykcxmjezb(cast2Str(sfzmap.get(GYJsConstants.getcZzeykcxmjezb())));
//        sfzvo.setSskcxs(cast2Str(sfzmap.get(GYJsConstants.getcSskcxs())));
//        sfzvo.setYjtdzzsse(cast2Str(sfzmap.get(GYJsConstants.getcYjtdzzsse())));
//        sfzvo.setYijtdzzsse(cast2Str(sfzmap.get(GYJsConstants.getcYbtdzzsse())));
//        sfzvo.setYbtdzzsse(cast2Str(sfzmap.get(GYJsConstants.getcYbtdzzsse())));
//        sfzvo.setTsjsbz(cast2Str(sfzmap.get(GYJsConstants.getcTsjsbz())));
//        sfzvo.setDksfe(cast2Str(sfzmap.get(GYJsConstants.getcDksfe())));
//        sfzvo.setRdpzuuid(cast2Str(sfzmap.get(GYJsConstants.getcRdpzuuid())));
//        sfzvo.setBjsbz(cast2Str(sfzmap.get(GYJsConstants.getcBjsbz())));
//        sfzvo.setRdzsuuid(cast2Str(sfzmap.get(GYJsConstants.getcRdzsuuid())));
//        sfzvo.setJmspjguuid(cast2Str(sfzmap.get(GYJsConstants.getcJmspjguuid())));
//        return sfzvo;
//    }
//
//    /**
//     *@name    获取自然人默认纳税期限/申报期限/缴款期限代码
//     *@description 相关说明
//     *@time    创建时间:2014-9-21下午02:24:32
//     *@param row 输入GYJsSfzVO
//     *@param csMap 输入参数map
//     *@return 行vo
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException 通用异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unused")
//    public static GYJsSfzVO getzrrMrqx(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        GYJsSfzVO rs = row;
//        final String swjgDm = (String) csMap.get("swjgDm");//税务机关代码
//        final String zsxmDm = row.getZsxmDm();//征收项目代码
//        //以下条件为可选
//
//        //输出参数变量
//        final String nsqxDm = new String();
//        final String sbqxDm = new String();
//        final String jkqxDm = new String();
//        //预留接口，通过和需求沟通，默认申报期限为07(期满之日起3个月内) 缴款期限 11(申报日后3天) 纳税期限：11(次)
//        row.setNsqxDm(GYDm01Constants.getDmGyNsqxC());
//        row.setSbqxDm(GYDm01Constants.getDmGySbqxSbrht());
//        row.setJkqxDm(GYDm01Constants.getDmGyJkqxSbrht1());
//        rs = row;
//        return rs;
//    }
//
//    /**
//     *
//     *@name    根据参数表查询起征点的计税信息
//     *@description 相关说明
//     *@time    创建时间:2014-10-23下午8:09:51
//     *@param swjgDm 税务机关代码
//     *@param qzdlx 起征点类型 01 增值税 02 营业税
//     *@param ssqq 所属期起
//     *@param ssqz 所属期止
//     *@return 起征点金额合计

//     *<AUTHOR> 曹保攀
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Double getqzdjexx(String swjgDm, String qzdlx, String ssqq, String ssqz) throws SwordBaseCheckedException {
//        Double je = 0.0;
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        List<Map<String, Object>> qzdlxList = new ArrayList<Map<String, Object>>();
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            final List<Map<String, Object>> zszmList = findListInCacheBySwjg(bjsjSwjgDm, "CS_SB_GLB_QZD");//根据税务机关查找
//            qzdlxList = findMapListInMapList("qzdlx", qzdlx, zszmList);//根据起征点类型判断
//            if (qzdlxList != null && !qzdlxList.isEmpty()) {
//                break;//找到即不再上溯
//            }
//        }
//        final String bdssqq = ssqq;
//        final String bdssqz = ssqz;
//        //先按照所属期进行排序
//        final SwordSortUtils.SortDescription description1 = new SwordSortUtils.SortDescription("yxqq", OrderType.ASC);
//        SwordSortUtils.sortMapList(qzdlxList, description1);
//        //通过税款所属期再进行
//        for (Map<String, Object> qzdMap : qzdlxList) {
//            final String yxqq = GYCastUtils.cast2Datestr(qzdMap.get("yxqq"));
//            final String yxqz = GYCastUtils.cast2Datestr(qzdMap.get("yxqz"));
//            final Double yje = (Double) qzdMap.get("je");
//            //开始判断所属期的条件是否满足
//            //场景1 开始日期不再范围中 截止日期在范围中
//            if (bdssqq.compareTo(yxqq) < 0 && bdssqz.compareTo(yxqq) > 0 && bdssqz.compareTo(yxqz) < 0) {
//                //通过yxqq,bdssqz计算月数量
//                final int ys = GYCastUtils.jsYfkd(SwordDateUtils.StringToDate(yxqq), SwordDateUtils.StringToDate(bdssqz));
//                je += ys * yje;
//            } else if (bdssqq.compareTo(yxqq) >= 0 && bdssqz.compareTo(yxqz) <= 0) {//两个都在范围中
//                //通过比对bdssqz,bdssqq计算月数
//                final int ys = GYCastUtils.jsYfkd(SwordDateUtils.StringToDate(bdssqq), SwordDateUtils.StringToDate(bdssqz));
//                je += ys * yje;
//            } else if (bdssqq.compareTo(yxqz) < 0 && bdssqz.compareTo(yxqz) > 0) {//开始在范围，截止日期不再范围
//                //通过yxqz.bdssqq计算月数
//                final int ys = GYCastUtils.jsYfkd(SwordDateUtils.StringToDate(bdssqq), SwordDateUtils.StringToDate(yxqz));
//                je += ys * yje;
//            } else if (bdssqq.compareTo(yxqq) < 0 && bdssqz.compareTo(yxqz) > 0) {
//                final int ys = GYCastUtils.jsYfkd(SwordDateUtils.StringToDate(yxqq), SwordDateUtils.StringToDate(yxqz));
//                je += ys * yje;
//            }
//
//        }
//        return SwordMathUtils.round(je, 2);
//    }
//
//    /**
//     *
//     *@name    获取纳税人起征点
//     *@description 获取纳税人起征点
//     *@time    创建时间:2014-11-7下午09:23:04
//     *@param swjgDm 税务机关代码
//     *@param zsxmDmIn 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@param skssqq 税款所属期起
//     *@param skssqz 税款所属期止
//     *@param djzclxDm 登记注册类型代码
//     *@return resultMap 起征点map
//     *@throws SwordBaseCheckedException   通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> getQzdxxWithZspm(String swjgDm, String zsxmDmIn, String zspmDm, Date skssqq, Date skssqz, String djzclxDm) throws SwordBaseCheckedException {
//        String zsxmDm = zsxmDmIn;
//        String xtcs = "";
//        String csz = "0.0";
//        String qzdBz = "Y";
//        //首先判断纳税人的类型，一般纳税人或者小规模纳税人（企业或企业性）针对企业和非企业单位（登记注册类型为1、2、3、5、42开头的，包括171私营独资企业、172私营合伙企业等企业）
//        String nsrlx = "3";//01 一般纳税人 02 为 小规模纳税人 03 为不享受优惠纳税人
//        if (djzclxDm.startsWith("1") || djzclxDm.startsWith("2") || djzclxDm.startsWith("3") || djzclxDm.startsWith("5") || djzclxDm.startsWith("42") || djzclxDm.startsWith("9")) {
//            nsrlx = "1";
//        }
//        if (djzclxDm.startsWith("41") || djzclxDm.startsWith("43")) { //针对个体经营（登记注册类型为41和43开头的），适用于原起征点政策，即营业税20000属于已达起征点，收取营业税，增值税20000属于已达起征点
//            nsrlx = "0";
//        }
//        //判断所属期类型
//        String ssqlx = "04";//01 月 02 季度 03 次 05 半年  04什么都不是
//        if (skssqq.compareTo(skssqz) == 0) {
//            ssqlx = "03";
//        } else {
//            //先通过月判断，当前是否是月
//            final Map<String, Object> rs = GYSbqxUtils.jsSkssq(GYDm01Constants.getDmGyNsqxY(), skssqq, "Y");
//            final Date yssqq = (Date) rs.get("skssqq");
//            final Date yssqz = (Date) rs.get("skssqz");
//            final Map<String, Object> rs1 = GYSbqxUtils.jsSkssq(GYDm01Constants.getDmGyNsqxJ(), skssqq, "Y");
//            final Date jssqq = (Date) rs1.get("skssqq");
//            final Date jssqz = (Date) rs1.get("skssqz");
//            final Map<String, Object> rs2 = GYSbqxUtils.jsSkssq(GYDm01Constants.getDmGyNsqxBn(), skssqq, "Y");
//            final Date bnssqq = (Date) rs2.get("skssqq");
//            final Date bnssqz = (Date) rs2.get("skssqz");
//            if (skssqq.compareTo(yssqq) == 0 && skssqz.compareTo(yssqz) == 0) {
//                ssqlx = "01";
//            } else if (skssqq.compareTo(jssqq) == 0 && skssqz.compareTo(jssqz) == 0) {
//                ssqlx = "02";
//            } else if (skssqq.compareTo(bnssqq) == 0 && skssqz.compareTo(bnssqz) == 0) {
//                ssqlx = "05";
//            } else {
//                ssqlx = "04";
//            }
//        }
//
//        if (ssqlx.equals("03")) {
//            //按次申报时使用的系统参数
//            if (zsxmDm.equals(GYDm01Constants.getDmGyZsxmYys1())) {
//                xtcs = GYDm06Constants.getDmGyXtcsbmAcnsdqzd();
//            } else if (zsxmDm.equals(GYDm01Constants.getDmGyZsxmZzs())) {
//                xtcs = GYDm06Constants.getDmGyXtcsbmAcnszzsqzd();
//            }
//        } else {
//            //个人所得税计税处理
//            String grsdsSfzsskBz = "Y";//个人所得税未达起征点是否征收税款：Y 征收 N 不征收
//            if (zsxmDm.equals(GYDm01Constants.getDmGyZsxmGrsds())) {
//                grsdsSfzsskBz = GYJsUtils.getXtcs(SbzsZSZdyConstants.getXtcsbmGrsdsqzdyxse(), swjgDm);
//                if (grsdsSfzsskBz != null && grsdsSfzsskBz.equals("N")) {
//                    zsxmDm = GYDm01Constants.getDmGyZsxmYys1();
//                } else {
//                    qzdBz = "N";//不按照起征点计税
//                }
//            }
//            //final String dfsjjsjjDm = GYSbzsUtils.changeoldDmtonewDm(GYDm01Constants.getDmGyZsxmDfsljsjj(), GYZdyConstants.getZsxmdmBm(), skssqqStr, skssqzStr, ZnsbSessionUtils.getswjgdm());
//            //final String whsyjsfDm = GYSbzsUtils.changeoldDmtonewDm(GYDm01Constants.getDmGyZsxmWhsyjsf(), GYZdyConstants.getZsxmdmBm(), skssqqStr, skssqzStr, ZnsbSessionUtils.getswjgdm());
//            if ((nsrlx.equals("0")||nsrlx.equals("1")) && !ssqlx.equals("04")) {//一般纳税人
//               /* if(dfsjjsjjDm.equals(zsxmDm)){//地方水利基金按照营业税起征点计算
//                    zsxmDm = GYDm01Constants.getDmGyZsxmYys1();
//                }*/
//                //查询配置表CS_SB_QZD_LXPZ，获取起征点类型
//                final String qzdlx = GYJsUtils.getQzdlx(swjgDm, zsxmDm, nsrlx);
//                if(!GyUtils.isNull(qzdlx)){
//                    final Double je = GYJsUtils.getqzdjexx(swjgDm,qzdlx, GYCastUtils.cast2Datestr(skssqq), GYCastUtils.cast2Datestr(skssqz));
//                    csz = GYCastUtils.cast2Str(je);
//                }else{
//                    qzdBz = "N";//不按照起征点计税
//                }
//            } else {//不享受优惠
//                xtcs = "";
//                csz = "0.0";
//                qzdBz = "N";
//            }
//
//        }
//        if (!xtcs.equals("")) {
//            csz = getXtcs(xtcs, swjgDm);
//        }
//
//        final Map<String, Object> resultMap = new HashMap<String, Object>();
//        resultMap.put("qzd", csz);
//        resultMap.put("qzdBz", qzdBz);
//
//        return resultMap;
//    }
//
//    /**
//     *
//     *@name    根据参数表查询起征点类型
//     *@description 相关说明
//     *@time    创建时间:2014-10-23下午8:09:51
//     *@param swjgDm 税务机关代码
//     *@param zsxmDm 征收项目
//     *@param nsrlx 纳税人类型
//     *@return 起征点类型

//     *<AUTHOR> 曹保攀
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getQzdlx(String swjgDm, String zsxmDm,String nsrlx) throws SwordBaseCheckedException {
//        String qzdlx = "";
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        List<Map<String, Object>> qzdlxList = new ArrayList<Map<String, Object>>();
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            final List<Map<String, Object>> zszmList = findListInCacheBySwjg(bjsjSwjgDm, "CS_SB_QZD_LXPZ");//根据税务机关查找
//            qzdlxList = findMapListInMapList("zsxmDm", zsxmDm, zszmList);//根据起征点类型过滤
//            if (qzdlxList != null && !qzdlxList.isEmpty()) {
//                break;//找到即不再上溯
//            }
//        }
//        for (Map<String, Object> qzdMap : qzdlxList) {
//            final String nsrlx2 = GYCastUtils.cast2Str(qzdMap.get("nsrlxDm"));
//            if(GyUtils.isNull(nsrlx2)||nsrlx.equals(nsrlx2)){
//                qzdlx = GYCastUtils.cast2Str(qzdMap.get("qzdlx"));
//            }
//
//        }
//        return qzdlx;
//    }
//
//    /**
//     *
//     *@name    中文名称  获取配置起征点的征收项目字符串
//     *@description 相关说明
//     *@time    创建时间:2015-9-14下午06:27:18
//     *@param swjgDm 税务机关代码
//     *@return  起征点征收项目字符串
//     *@throws SwordBaseCheckedException
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getQzdzsxm(String swjgDm) throws SwordBaseCheckedException {
//        String zsxmStr = "";
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        List<Map<String, Object>> qzdlxList = new ArrayList<Map<String, Object>>();
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            qzdlxList = findListInCacheBySwjg(bjsjSwjgDm, "CS_SB_QZD_LXPZ");//根据税务机关查找
//            if (qzdlxList != null && !qzdlxList.isEmpty()) {
//                break;//找到即不再上溯
//            }
//        }
//        for (Map<String, Object> qzdMap : qzdlxList) {
//            final String zsxmDM = GYCastUtils.cast2Str(qzdMap.get("zsxmDm"));
//            zsxmStr = zsxmStr +"、"+zsxmDM;
//        }
//        zsxmStr = zsxmStr +"、"+GYDm01Constants.getDmGyZsxmGrsds();//增加个人所得税
//        return zsxmStr;
//    }
//
//    /**
//     *@name    在获取起征点时根据登记注册类型获取纳税人类型
//     *@description 相关说明
//     *@time    创建时间:2017年4月20日下午1:53:41
//     *@param djzclxDm 登记注册类型代码
//     *@return 纳税人类型
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getNsrlxForQzd(final String djzclxDm) {
//        //首先判断纳税人的类型，一般纳税人或者小规模纳税人（企业或企业性）针对企业和非企业单位（登记注册类型为1、2、3、5开头的，包括171私营独资企业、172私营合伙企业等企业）
//        String nsrlx = "3";//01 一般纳税人 02 为 小规模纳税人 03 为不享受优惠纳税人
//        if (djzclxDm.startsWith("1") || djzclxDm.startsWith("2") || djzclxDm.startsWith("3") || djzclxDm.startsWith("5")) {
//            nsrlx = "1";
//        }
//        if (djzclxDm.startsWith("41")) { //针对个体经营（登记注册类型为41开头的），适用于原起征点政策，即营业税20000属于已达起征点，收取营业税，增值税20000属于已达起征点
//            nsrlx = "0";
//        }
//        //add by duwei 支持自然人享受未到起征点减免 2015-08-10
//        if (djzclxDm.startsWith("43")) {
//            nsrlx = "1";
//        }
//
//        return nsrlx;
//    }
//
//    /**
//     *@name    获取纳税人起征点
//     *@description 获取纳税人起征点
//     *@time    创建时间:2014-7-23下午10:05:53
//     *@param swjgDm swjgDm
//     *@param zsxmDm zsxmDm
//     *@param skssqq skssqq
//     *@param skssqz skssqz
//     *@param djzclxDm djzclxDm
//     *@param xwbz xwbz
//     *@return resultMap
//     *@throws SwordBaseCheckedException SwordBaseCheckedException
//     *<AUTHOR>   曹保攀
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> getQzdxxWithZsxmnew(String swjgDm, String zsxmDm, Date skssqq, Date skssqz, String djzclxDm, Boolean xwbz) throws SwordBaseCheckedException {
//        String xtcs = "";
//        String csz = "";
//        /// TODO-dm测试
//        final String nsrlx = GYJsUtils.getNsrlxForQzd(djzclxDm);
////        //首先判断纳税人的类型，一般纳税人或者小规模纳税人（企业或企业性）针对企业和非企业单位（登记注册类型为1、2、3、5开头的，包括171私营独资企业、172私营合伙企业等企业）
////        String nsrlx = "3";//01 一般纳税人 02 为 小规模纳税人 03 为不享受优惠纳税人
////        if (djzclxDm.startsWith("1") || djzclxDm.startsWith("2") || djzclxDm.startsWith("3") || djzclxDm.startsWith("5")) {
////            nsrlx = "1";
////        }
////        if (djzclxDm.startsWith("41")) { //针对个体经营（登记注册类型为41开头的），适用于原起征点政策，即营业税20000属于已达起征点，收取营业税，增值税20000属于已达起征点
////            nsrlx = "0";
////        }
////        //add by duwei 支持自然人享受未到起征点减免 2015-08-10
////        if (djzclxDm.startsWith("43")) {
////            nsrlx = "1";
////        }
//        // END
//        //判断所属期类型
//        String ssqlx = "04";//01 月 02 季度 03 次 04什么都不是
//        if (skssqq.compareTo(skssqz) == 0) {
//            ssqlx = "03";
//        } else {
//            //先通过月判断，当前是否是月
//            final Map<String, Object> rs = GYSbqxUtils.jsSkssq(GYDm01Constants.getDmGyNsqxY(), skssqq, "Y");
//            final Date yssqq = (Date) rs.get("skssqq");
//            final Date yssqz = (Date) rs.get("skssqz");
//            final Map<String, Object> rs1 = GYSbqxUtils.jsSkssq(GYDm01Constants.getDmGyNsqxJ(), skssqq, "Y");
//            final Date jssqq = (Date) rs1.get("skssqq");
//            final Date jssqz = (Date) rs1.get("skssqz");
//            if (skssqq.compareTo(yssqq) == 0 && skssqz.compareTo(yssqz) == 0) {
//                ssqlx = "01";
//            } else if (skssqq.compareTo(jssqq) == 0 && skssqz.compareTo(jssqz) == 0) {
//                ssqlx = "02";
//            } else {
//                ssqlx = "04";
//            }
//        }
//        if (ssqlx.equals("03")) {
//            //按次申报时使用的系统参数
//            if (zsxmDm.equals(GYDm01Constants.getDmGyZsxmYys1())) {
//                xtcs = GYDm06Constants.getDmGyXtcsbmAcnsdqzd();
//            } else if (zsxmDm.equals(GYDm01Constants.getDmGyZsxmZzs())) {
//                xtcs = GYDm06Constants.getDmGyXtcsbmAcnszzsqzd();
//            }
//        } else {
//            if ((nsrlx.equals("0") || nsrlx.equals("1")) && !ssqlx.equals("04")) {//一般纳税人
//                /* if(dfsjjsjjDm.equals(zsxmDm)){//地方水利基金按照营业税起征点计算
//                     zsxmDm = GYDm01Constants.getDmGyZsxmYys1();
//                 }*/
//                //查询配置表CS_SB_QZD_LXPZ，获取起征点类型
//                final String qzdlx = GYJsUtils.getQzdlx(swjgDm, zsxmDm, nsrlx);
//                if (!GyUtils.isNull(qzdlx)) {
//                    final Double je = GYJsUtils.getqzdjexx(swjgDm, qzdlx, GYCastUtils.cast2Datestr(skssqq), GYCastUtils.cast2Datestr(skssqz));
//                    csz = GYCastUtils.cast2Str(je);
//                }
//            } else {//不享受优惠
//                xtcs = "";
//                csz = "0.0";
//            }
//            //获取系统参数
//        }
//        if (!xtcs.equals("")) {
//            csz = getXtcs(xtcs, swjgDm);
//        }
//        final Map<String, Object> resultMap = new HashMap<String, Object>();
//        resultMap.put("qzd", csz);
//        resultMap.put("qzdBz", "T");
//
//        return resultMap;
//    }
//
//    /**
//     *@name    中文名称
//     *@description 相关说明
//     *@time    创建时间:2014-7-24下午08:00:43
//     *@param zsxmDm zsxmDm
//     *@param skssqq skssqq
//     *@param skssqz skssqz
//     *@param djzclxDm djzclxDm
//     *@param xwbz xwbz
//     *@param csz csz
//     *@return resultMap
//     *@throws NumberFormatException NumberFormatException
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unused")
//    private static Map<String, Object> getQzdxxWithZsxm(String zsxmDm, Date skssqq, Date skssqz, String djzclxDm, Boolean xwbz, String csz) throws NumberFormatException {
//        //获取因子
//        final BigDecimal multiple = getMultiple(skssqq, skssqz, djzclxDm, xwbz);
//
//        //获取最后的乘积
//        final Map<String, Object> resultMap = getMutipleCsz(multiple, csz, djzclxDm);
//        return resultMap;
//    }
//
//    /**
//     *@name    获取因子
//     *@description 相关说明
//     *@time    创建时间:2014-7-24下午08:00:55
//     *@param skssqq skssqq
//     *@param skssqz skssqz
//     *@param djzclxDm djzclxDm
//     *@param xwbz xwbz
//     *@return multiple multiple
//     *<AUTHOR>   赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static BigDecimal getMultiple(Date skssqq, Date skssqz, String djzclxDm, Boolean xwbz) {
//        final Calendar date0831Calendar = Calendar.getInstance();
//        date0831Calendar.set(2013, Calendar.AUGUST, 1);
//
//        final Calendar skssqqCal = Calendar.getInstance();
//        skssqqCal.setTime(skssqq);
//
//        final Calendar skssqzCal = Calendar.getInstance();
//        skssqzCal.setTime(skssqz);
//
//        BigDecimal multiple = new BigDecimal("0.0");
//
//        final int compareWithSkssqq = skssqqCal.compareTo(date0831Calendar);
//        final int compareWithSkssqz = skssqzCal.compareTo(date0831Calendar);
//
//        if (compareWithSkssqq >= 0) {//date<skssqq<skssqz
//            @SuppressWarnings("unused")
//            final Double skssqqMonth = skssqqCal.get(Calendar.MONTH) * 1.0;
//            @SuppressWarnings("unused")
//            final Double skssqzMonth = skssqzCal.get(Calendar.MONTH) * 1.0;
//            final Double multipleD = 1.0;
//
//            multiple = new BigDecimal(multipleD);
//        } else {
//            //默认和0801在同一年里面
//            if (compareWithSkssqz >= 0) {//skssqq<date<skssqz
//                @SuppressWarnings("unused")
//                final Double fixedMonth = date0831Calendar.get(Calendar.MONTH) * 1.0;
//                @SuppressWarnings("unused")
//                final Double skssqzMonth = skssqzCal.get(Calendar.MONTH) * 1.0;
//                final Double multipleD = 1.0;
//
//                multiple = new BigDecimal(multipleD);
//            } else {//skssqq<skssqz<date
//                //老规则
//                if (xwbz != null && xwbz) {
//                    multiple = new BigDecimal("1.00");
//                    return multiple;//有一个判断完就结束
//                } else {
//                    multiple = new BigDecimal("0.00");
//                }
//                if (djzclxDm != null && (djzclxDm.startsWith("41") || djzclxDm.startsWith("43"))) {
//                    multiple = new BigDecimal("1.00");
//                } else {
//                    multiple = new BigDecimal("0.00");
//                }
//            }
//        }
//        return multiple;
//    }
//
//    /**
//     *@name    获取最后的乘积
//     *@description 相关说明
//     *@time    创建时间:2014-7-24下午08:02:26
//     *@param multiple1 multiple
//     *@param csz1 csz
//     *@param djzclxDm djzclxDm
//     *@return resultMap resultMap
//     *@throws NumberFormatException NumberFormatException
//     *<AUTHOR>   赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static Map<String, Object> getMutipleCsz(final BigDecimal multiple1, final String csz1, String djzclxDm) throws NumberFormatException {
//        BigDecimal multiple = multiple1;
//        String csz = csz1;
//        final Map<String, Object> resultMap = new HashMap<String, Object>();
//        final BigDecimal big0 = new BigDecimal("0.0");
//        if (csz == null || "".equals(csz)) {
//            csz = "0.0";
//        }
//        Double cszDouble = Double.valueOf(csz);
//        cszDouble = SwordMathUtils.multiple(cszDouble, Double.valueOf(multiple.toString()));
//
//        if (djzclxDm == null || "".equals(djzclxDm) || djzclxDm.startsWith("900")) {
//            multiple = new BigDecimal("0");
//        } else {
//
//            if (djzclxDm.startsWith("4")) {//个人开头的
//                cszDouble = SwordMathUtils.subtract(cszDouble, 0.001);
//                resultMap.put("qzdGrQy", "GR");
//            } else {//企业
//                resultMap.put("qzdGrQy", "QY");
//            }
//        }
//
//        if (multiple.compareTo(big0) == 0) {
//            resultMap.put("qzdBz", "N");
//            resultMap.put("qzd", "0.0");
//        } else {
//            resultMap.put("qzdBz", "Y");
//            resultMap.put("qzd", String.valueOf(cszDouble));
//        }
//        return resultMap;
//    }
//
//    /**
//     *@name    获取税务机关下所有城建税的方法
//     *@description 描述
//     *@time    创建时间:2014-11-6下午22:00:21
//     *@param csMap 参数
//     *@return mapList，内含 zsxmDm,zspmDm

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings({ "unchecked" })
//    public static List<Map<String, Object>> getAllCjsBySwjg(Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String swjgDm = (String) csMap.get("swjgDm");
//        String xzqhszDm = (String) csMap.get("xzqhszDm");
//        //城建税类型，01为增值税 02 为消费税 03 为营业税
//        String cjslx = (String) csMap.get("cjslx");
//        if (GyUtils.isNull(cjslx)) {
//            cjslx = "03";
//        }
//        //增值城建税品目Str
//        //消费税城建税品目Str
//        //营业税城建税品目Str
//        final String zzscswhjsspmStr = SbzsSBZdyConstants.getCsZzsCswhjssStr();
//        final String yyscswhjsspmStr = SbzsSBZdyConstants.getCsYysCswhjssStr();
//        final String xfscswhjsspmStr = SbzsSBZdyConstants.getCsXfsCswhjssStr();
//        List<Map<String, Object>> allzspmList = getAllFsBySwjg(csMap);
//        final List<Map<String, Object>> cjsLsit = new ArrayList<Map<String, Object>>();
//        if (GyUtils.isNull(allzspmList)) {
//            allzspmList = new ArrayList<Map<String, Object>>();
//        }
//        for (Map<String, Object> szMap : allzspmList) {
//            final String zsxmDm = (String) szMap.get("zsxmDm");
//            final String zspmDm = (String) szMap.get("zspmDm");
//            if (zsxmDm.equals(GYDm01Constants.getDmGyZsxmCswhjss())) {
//                if (cjslx.equals("01") && zzscswhjsspmStr.indexOf(zspmDm) >= 0) {
//                    cjsLsit.add(szMap);
//                } else if (cjslx.equals("03") && yyscswhjsspmStr.indexOf(zspmDm) >= 0) {
//                    cjsLsit.add(szMap);
//                } else if (cjslx.equals("02") && xfscswhjsspmStr.indexOf(zspmDm) >= 0) {
//                    cjsLsit.add(szMap);
//                }
//            }
//        }
//        //通过税务机关获取当前税务机关的行政区划代码
//        if (GyUtils.isNull(xzqhszDm)) {
//            final Map<String, Object> swjgMap = (Map<String, Object>) SwordCacheUtils.getFromKV("DM_GY_SWJG", new Object[] { swjgDm });
//            xzqhszDm = (String) swjgMap.get("XZQHSZ_DM");
//        }
//        List<Map<String, Object>> fsList = new ArrayList<Map<String, Object>>();
//        //通过行政区划代码获取税务机关代码
//        //根据行政区划、参数表获取行政区划性质
//        String xzqhqyxzDm = "1,2,3";//默认为市区 1 市区 2 县城 3 农村
//        if (xzqhszDm != null && xzqhszDm.length() > 0) {
//            final Map<String, Object> xzqhqyxzDmMap = new HashMap<String, Object>();
//            if (xzqhqyxzDmMap != null && !xzqhqyxzDmMap.isEmpty()) {
//                xzqhqyxzDm = (String) xzqhqyxzDmMap.get("xzqhqyxzDm");
//            }
//        }
//        //分割信息
//        final String[] s_vo = xzqhqyxzDm.split(",");
//        for (int jj = 0; jj < s_vo.length; jj++) {
//            final String lx = s_vo[jj];
//            for (Map<String, Object> szMap : cjsLsit) {
//                final String zspmDm = (String) szMap.get("zspmDm");
//                if (zspmDm.substring(5, 7).equals("0".concat(lx))) {
//                    fsList.add(szMap);
//                    //                    continue;
//                }
//            }
//        }
//        final String[] paramArray = { "zsxmDm", "zspmDm" };
//        final SwordMultiKeyMap sMap = new SwordMultiKeyMap();
//        sMap.loadDataFromMapList(paramArray, fsList);
//        fsList = new ArrayList(sMap.values());
//        return fsList;
//    }
//
//    /**
//     *@name    减免信息校验
//     *@description 减免信息校验
//     *@time    创建时间:2014-12-24上午10:50:54
//     *@param jmxxVOList jmxxVOList
//     *@param sbxxVOList sbxxVOList
//     *@param ywsbSbxxList ywsbSbxxList
//     *@return map
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> getJmxxCheck(List<SBJmxxVO> jmxxVOList, List<SBSbxxVO> sbxxVOList, List<SBSbxxVO> ywsbSbxxList) throws SwordBaseCheckedException {
//
//        //返回参数
//        final Map<String, Object> messMap = new HashMap<String, Object>();
//
//        //返回消息
//        String message = "校验不通过：";
//
//        //组装申报信息LIST将相同征收品目的计税依据、应纳税额、减免税额合计放入mapList
//        final List<Map<String, Object>> sbxxMapList = assembleSbxxList(sbxxVOList);
//
//        //组织特定减免信息LIST
//        final List<SBJmxxVO> tdjmList = new ArrayList<SBJmxxVO>();
//
//        //组装申报信息LIST将相同征收品目的计税依据、应纳税额、减免税额合计放入mapList
//        List<Map<String, Object>> ywsbxxMap = new ArrayList<Map<String, Object>>();
//        if (!GyUtils.isNull(ywsbSbxxList)) {
//            ywsbxxMap = assembleSbxxList(ywsbSbxxList);
//        }
//        for (Map<String, Object> map : sbxxMapList) {
//            final String zsxmDm = (String) map.get("zsxmDm");
//            if (!GyUtils.isNull(map.get("jmse")) && GYCastUtils.cast2Double(map.get("jmse")) > 0) {
//                for (SBJmxxVO sbJmxxVO : jmxxVOList) {
//                    if (zsxmDm.equals(sbJmxxVO.getZsxmDm()) && GyUtils.isNull(sbJmxxVO.getTdjmBz())) {
//                        //减征金额计算、特定减计算
//                        message = getJmxxJs(sbJmxxVO, map, message, ywsbxxMap);
//                    } else if (!GyUtils.isNull(sbJmxxVO.getTdjmBz())) {
//                        tdjmList.add(sbJmxxVO);
//                        map.put("tdjmBz", "Y");
//                    }
//                }
//            }
//        }
//        if (!GyUtils.isNull(tdjmList)) {
//            final Map<String, Object> messageMap = getTdjmJmxx(sbxxVOList, tdjmList, message, ywsbxxMap);
//            if ("N".equals(messageMap.get("jmBz"))) {
//                message = (String) messageMap.get("news");
//            }
//        }
//        messMap.put("message", message);
//        messMap.put("mapList", sbxxMapList);
//        return messMap;
//    }
//
//    /**
//     *@name    根据减征类型优先取 未达起征点、免征、减幅、减率、减额、特定减免
//     *@description 根据减征类型优先取 未达起征点、免征、减幅、减率、减额、特定减免
//     *@time    创建时间:2014-11-19下午05:24:30
//     *@param sbxxvo sbxxvo
//     *@param jmxxList jmxxList
//     *@param news news
//     *@param qdBz qdBz
//     *@param jlsyjmje jlsyjmje
//     *@return map
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> getJzlxJmxx(SBSbxxVO sbxxvo, List<SBJmxxVO> jmxxList, String news, Double jlsyjmje, String qdBz) throws SwordBaseCheckedException {
//        final List<SBJmxxVO> jzfdjmList = new ArrayList<SBJmxxVO>();
//        final List<SBJmxxVO> jzslList = new ArrayList<SBJmxxVO>();
//        final List<SBJmxxVO> jzedList = new ArrayList<SBJmxxVO>();
//        Map<String, Object> map = new HashMap<String, Object>();
//        Double ysjmje = jlsyjmje;
//        for (SBJmxxVO sbxxJmxxVO : jmxxList) {
//            if (sbxxJmxxVO.getZsxmDm().equals(sbxxvo.getZsxmDm())) {
//                if (sbxxJmxxVO.getJmzlxDm().equals("01")) {
//                    //  减征
//                    final Double jzfd = sbxxJmxxVO.getJzfd();
//                    final Double jzsl = sbxxJmxxVO.getJzsl();
//                    final Double jzed = sbxxJmxxVO.getJzed();
//                    if (jzfd != null && !jzfd.equals("") && jzfd > 0) {
//                        jzfdjmList.add(sbxxJmxxVO);
//                    } else if (jzsl != null && !jzsl.equals("") && jzsl > 0) {
//                        jzslList.add(sbxxJmxxVO);
//                    } else if (jzed != null && !jzed.equals("") && jzed > 0 && (sbxxJmxxVO.getTdjmBz() == null || !sbxxJmxxVO.getTdjmBz().equals("Y"))) {
//                        jzedList.add(sbxxJmxxVO);
//                    }
//                } else if (sbxxJmxxVO.getJmzlxDm().equals("02")) {
//                    map = getmzJmxx(sbxxvo, jmxxList, news);
//                    return map;
//                }
//            }
//
//        }
//        //以下判定不可颠倒。根据（未达起征点、免征、减幅、减率、减额、特定减免顺序依次取）
//
//        //减幅
//        if (jzfdjmList.size() > 0) {
//            final SBJmxxVO jmxxvo = jzfdjmList.get(0);
//            final Double jzfd = jmxxvo.getJzfd();
//            final Double ynse = sbxxvo.getYnse();
//            final Double jzfdje = SwordMathUtils.multiple(ynse, jzfd);
//            if (jzfdje >= sbxxvo.getJmse()) {
//                map.put("jmBz", "Y");
//            } else {
//                final String zsxmmc = dmOrMc(sbxxvo.getZsxmDm());
//                map.put("news", news + "征收项目：" + zsxmmc + " 减免金额不能大于：" + jzfdje + "   ");
//                map.put("jmBz", "N");
//            }
//        }
//        //减率
//        if (jzslList.size() > 0) {
//            final SBJmxxVO jmxxvo = jzslList.get(0);
//            final Double jzsl = jmxxvo.getJzsl();
//            final Double jsyj = GYCastUtils.cast2Double(sbxxvo.getJsyj());
//            final Double jzslje = SwordMathUtils.multiple(jsyj, jzsl);
//            if (jzslje >= sbxxvo.getJmse()) {
//                map.put("jmBz", "Y");
//            } else {
//                final String zsxmmc = dmOrMc(sbxxvo.getZsxmDm());
//                map.put("news", news + "征收项目：" + zsxmmc + "减免金额不能大于：" + jzslje + "   ");
//                map.put("jmBz", "N");
//            }
//        }
//        //减额
//        if (jzedList.size() > 0) {
//            final SBJmxxVO jmxxvo = jzedList.get(0);
//            final Double jzed = jmxxvo.getJzed();
//            if (jzed >= (sbxxvo.getJmse() + ysjmje)) {
//                ysjmje = sbxxvo.getJmse() + ysjmje;
//                map.put("jlsyjmje", ysjmje);
//                map.put("news", news);
//                map.put("jmBz", "Y");
//            } else {
//                final String zsxmmc = dmOrMc(sbxxvo.getZsxmDm());
//                map.put("news", news + "征收项目：" + zsxmmc + "减免金额不能大于：" + jzed + "   ");
//                map.put("jmBz", "N");
//            }
//        }
//        return map;
//    }
//
//    /**
//     *@name     获取免征税款金额
//     *@description  获取免征税款金额
//     *@time    创建时间:2014-11-19下午05:26:27
//     *@param sbxxvo sbxxvo
//     *@param jmxxList jmxxList
//     *@param news news
//     *@return map
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> getmzJmxx(SBSbxxVO sbxxvo, List<SBJmxxVO> jmxxList, String news) throws SwordBaseCheckedException {
//        final Map<String, Object> map = new HashMap<String, Object>();
//        for (SBJmxxVO sbjmxxvo : jmxxList) {
//            if (sbjmxxvo.getZsxmDm().equals(sbxxvo.getZsxmDm())) {
//                if (sbjmxxvo.getJmzlxDm().equals("02")) {
//                    if (sbxxvo.getYnse() >= sbxxvo.getJmse()) {
//                        map.put("jmBz", "Y");
//                        return map;
//                    } else {
//                        final String zsxmmc = dmOrMc(sbxxvo.getZsxmDm());
//                        map.put("news", news + "征收项目：" + zsxmmc + "减免金额不能大于：" + sbxxvo.getYnse() + "   ");
//                        map.put("jmBz", "N");
//                        return map;
//                    }
//                }
//            }
//        }
//        return map;
//    }
//
//    /**
//     *@name    特定减免处理
//     *@description 特定减免处理
//     *@time    创建时间:2014-11-19下午05:27:08
//     *@param sbxxList sbxxList
//     *@param jmxxList jmxxList
//     *@param news news
//     *@param ywsbxxMap ywsbxxMap
//     *@return map
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> getTdjmJmxx(List<SBSbxxVO> sbxxList, List<SBJmxxVO> jmxxList, String news, List<Map<String, Object>> ywsbxxMap) throws SwordBaseCheckedException {
//        final String news_new = news + "特定减免金额";
//        Map<String, Object> xtmap = null;
//        String[] cszArray = null;
//        //先计算特定减免
//        //事先组装特定减免的征收项目map
//        final Map<String, Object> tdZsxmInJgbMap = new HashMap<String, Object>();
//        for (SBJmxxVO sbxxjmxxvo : jmxxList) {
//            final String zsxmDm = sbxxjmxxvo.getZsxmDm();//征收项目
//            final String jmsspsxDm = sbxxjmxxvo.getJmsspsxDm();// 得到减免税务事项
//            final String ssyhbz = sbxxjmxxvo.getTdjmBz();//税收优惠标志SSYHBZ  0：个体   1：企业   2：非特定减免
//            if (jmsspsxDm != null && "Y".equals(ssyhbz)) {
//                xtmap = HxzgQxUtils.getXtcs(ZnsbSessionUtils.getswjgdm(), "A0000001061001032");
//            } else {
//                xtmap = HxzgQxUtils.getXtcs(ZnsbSessionUtils.getswjgdm(), "A0000001061001034");
//            }
//            if (xtmap != null && !xtmap.isEmpty()) {
//                final String csz = (String) xtmap.get("csz");
//                cszArray = csz.split(",");
//            }
//            tdZsxmInJgbMap.put(zsxmDm, "");
//        }
//        final Map<String, Object> newsMap = new HashMap<String, Object>();
//        if (cszArray != null && !cszArray.equals("")) {
//            final int cszLength = cszArray.length;
//            for (SBJmxxVO sbxxjmxxvo : jmxxList) {
//                // 特定减免
//                final String jmsspsxDm = sbxxjmxxvo.getJmsspsxDm();// 得到减免税务事项
//                final String ssyhbz = sbxxjmxxvo.getTdjmBz();//税收优惠标志SSYHBZ  0：个体   1：企业   2：非特定减免
//                if (jmsspsxDm != null && ("0".equals(ssyhbz) || "1".equals(ssyhbz))) {
//
//                    // 判断税务事项是否在Swsx的数组中，如果在的话就是特定减免
//                    Double jzed = sbxxjmxxvo.getJzed() == null ? 0 : sbxxjmxxvo.getJzed();
//
//                    //用于超出金额时提示最原始金额
//                    final Double jzed_new = jzed;
//
//                    //特定减免的特定顺序值，先把特定顺序值的给处理了，然后再处理特定减免中的非特定顺序值，循环每个参数值看看有没有匹配的，
//                    final Map<String, Object> newsMap_Sort = getTdjmOrSort(cszLength, cszArray, jzed, sbxxList, tdZsxmInJgbMap, news_new, jzed_new, jmsspsxDm, jmxxList, ywsbxxMap);
//                    if (newsMap_Sort != null && !newsMap_Sort.isEmpty()) {
//                        if ("Y".equals(newsMap_Sort.get("jmBz"))) {
//                            jzed = SwordMathUtils.subtract(jzed, GYCastUtils.cast2Double(newsMap_Sort.get("jzed")));
//                        } else if ("N".equals(newsMap_Sort.get("jmBz"))) {
//                            newsMap.put("news", newsMap_Sort.get("news"));
//                            newsMap.put("jmBz", newsMap_Sort.get("jmBz"));
//                            return newsMap;
//                        }
//                    }
//                    //特定的顺序值处理完毕之后，就要处理特定减免中的非特定值，这个可以自动过滤掉已经有减免的数值，如果又见面那么就说明ynse一定是0了，因为一次都是扣完的，如果不是零就说明jzed是0了
//                    //减征额度大于0，但不是特定顺序中的征收项目或者征收品目
//                    //且该条申报 征收项目 应在该户审批结果表中项目范围内
//                    final Map<String, Object> newsMap_Tdjm = getTdjm(jzed, sbxxList, tdZsxmInJgbMap, news_new, jzed_new, jmsspsxDm, jmxxList, ywsbxxMap);
//                    if (newsMap_Tdjm != null && !newsMap_Tdjm.isEmpty()) {
//                        if ("Y".equals(newsMap_Tdjm.get("jmBz"))) {
//                            jzed = SwordMathUtils.subtract(jzed, GYCastUtils.cast2Double(newsMap_Tdjm.get("jzed")));
//                        } else if ("N".equals(newsMap_Tdjm.get("jmBz"))) {
//                            newsMap.put("news", newsMap_Tdjm.get("news"));
//                            newsMap.put("jmBz", newsMap_Tdjm.get("jmBz"));
//                            return newsMap;
//                        }
//                    }
//                }
//            }
//        }
//        newsMap.put("news", news);
//        newsMap.put("jmBz", "Y");
//        return newsMap;
//    }
//
//    /**
//     *@name    特定减免的特定顺序值，先把特定顺序值的给处理了，然后再处理特定减免中的非特定顺序值，循环每个参数值看看有没有匹配的
//     *@description  特定减免的特定顺序值，先把特定顺序值的给处理了，然后再处理特定减免中的非特定顺序值，循环每个参数值看看有没有匹配的
//     *@time    创建时间:2014-11-19下午08:43:02
//     *@param cszLength cszLength
//     *@param cszArray cszArray
//     *@param jzed jzed
//     *@param sbxxList sbxxList
//     *@param tdZsxmInJgbMap tdZsxmInJgbMap
//     *@param news news
//     *@param jzednew jzednew
//     *@param jmsspsxDm jmsspsxDm
//     *@param jmxxList jmxxList
//     *@param ywsbxxMap ywsbxxMap
//     *@return map
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static Map<String, Object> getTdjmOrSort(int cszLength, String[] cszArray, Double jzed, List<SBSbxxVO> sbxxList, Map<String, Object> tdZsxmInJgbMap, String news, Double jzednew,
//                                                     String jmsspsxDm, List<SBJmxxVO> jmxxList, List<Map<String, Object>> ywsbxxMap) throws SwordBaseCheckedException {
//        final Map<String, Object> newsMap = new HashMap<String, Object>();
//        Double jzed_new = jzed;
//        String news_new = news;
//        for (int i = 0; i < cszLength; i++) {
//            final String singleCsz = cszArray[i];
//            final int singleLength = singleCsz.length();
//            boolean zspmFlag = false;
//
//            //如果参数值是征收品目的话就是true
//            if (singleLength > 5) {
//                zspmFlag = true;
//            }
//            for (SBSbxxVO sbxxvo : sbxxList) {
//                //用来判断是不是特定顺序值，如果是的话就置成true
//                if (sbxxvo.getJmse() <= 0) {
//                    continue;
//                }
//                final boolean tdFlag = whetherTd(singleCsz, zspmFlag, sbxxvo);
//                final String zsxmDm = sbxxvo.getZsxmDm();
//                //且该条申报 征收项目 应在该户审批结果表中项目范围内
//                if (jzed > 0 && tdFlag && tdZsxmInJgbMap.containsKey(zsxmDm)) {
//                    //不但减征额度大于0而且是特定顺序中的征收项目或者征收品目     减额
//                    double jzedTmp = jzed;
//                    if (jzedTmp < 0) {
//                        jzedTmp = 0;
//                    }
//                    jzedTmp = getqueryZsxmByJmse(zsxmDm, jzedTmp, ywsbxxMap);
//                    if ((jzedTmp - sbxxvo.getJmse()) <= 0) {
//                        //字符串拼接
//                        news_new += "" + jzednew + "已超出金额";
//                        news_new = messPz(news, jmsspsxDm, jmxxList);
//                        newsMap.put("news", news_new);
//                        newsMap.put("jmBz", "N");
//                        return newsMap;
//                    }
//                    Double jmse = 0.00;
//                    if (sbxxvo.getJmse() != null) {
//                        jmse = sbxxvo.getJmse();
//                    }
//                    jzed_new = SwordMathUtils.subtract(jzed_new, jmse);
//                    newsMap.put("jmBz", "Y");
//                    newsMap.put("jzed", jzed_new);
//                }
//            }
//        }
//        newsMap.put("jmBz", "Y");
//        newsMap.put("jzed", 0.00);
//        return newsMap;
//    }
//
//    /**
//     *@name    特定的顺序值处理完毕之后，就要处理特定减免中的非特定值，这个可以自动过滤掉已经有减免的数值，如果又见面那么就说明ynse一定是0了，因为一次都是扣完的，如果不是零就说明jzed是0了
//     *@description 特定的顺序值处理完毕之后，就要处理特定减免中的非特定值，这个可以自动过滤掉已经有减免的数值，如果又见面那么就说明ynse一定是0了，因为一次都是扣完的，如果不是零就说明jzed是0了
//     *@time    创建时间:2014-12-24上午10:40:40
//     *@param jzed jzed
//     *@param sbxxList sbxxList
//     *@param tdZsxmInJgbMap tdZsxmInJgbMap
//     *@param newsnew newsnew
//     *@param jzednew jzednew
//     *@param jmsspsxDm jmsspsxDm
//     *@param jmxxList jmxxList
//     *@param ywsbxxMap ywsbxxMap
//     *@return map

//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static Map<String, Object> getTdjm(Double jzed, List<SBSbxxVO> sbxxList, Map<String, Object> tdZsxmInJgbMap, String newsnew, Double jzednew, String jmsspsxDm, List<SBJmxxVO> jmxxList,
//                                               List<Map<String, Object>> ywsbxxMap) throws SwordBaseCheckedException {
//        final Map<String, Object> newsMap = new HashMap<String, Object>();
//        Double jzed_new = jzed;
//        String news = newsnew;
//        for (SBSbxxVO sbxxvo : sbxxList) {
//            final String zsxmDm = sbxxvo.getZsxmDm();
//            if (sbxxvo.getJmse() <= 0) {
//                continue;
//            }
//            if (tdZsxmInJgbMap.containsKey(zsxmDm)) {
//                //不但减征额度大于0而且是特定顺序中的征收项目或者征收品目     减额
//                jzed_new = getqueryZsxmByJmse(zsxmDm, jzed_new, ywsbxxMap);
//                if ((jzed_new - sbxxvo.getJmse()) < 0) {
//                    //字符串拼接
//                    news += "" + jzed_new + "已超出金额";
//                    news = messPz(news, jmsspsxDm, jmxxList);
//                    newsMap.put("news", news);
//                    newsMap.put("jmBz", "N");
//                    return newsMap;
//                }
//                Double jmse = 0.00;
//                if (sbxxvo.getJmse() != null) {
//                    jmse = sbxxvo.getJmse();
//                }
//                jzed_new = SwordMathUtils.subtract(jzed_new, jmse);
//            }
//        }
//        newsMap.put("jzed", jzed_new);
//        newsMap.put("jmBz", "Y");
//        return newsMap;
//    }
//
//    /**
//     *@name   用来判断是不是特定顺序值，如果是的话就置成true
//     *@description  用来判断是不是特定顺序值，如果是的话就置成true
//     *@time    创建时间:2014-11-19下午05:29:11
//     *@param singleCsz singleCsz
//     *@param zspmFlag zspmFlag
//     *@param sbxxvo sbxxvo
//     *@return boolean
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static boolean whetherTd(String singleCsz, boolean zspmFlag, SBSbxxVO sbxxvo) {
//        boolean tdFlag = false;//特定，如果是特定的就置成true
//        if (zspmFlag) {//如果参数值是征收品目的话
//            final String zspmDm = sbxxvo.getZspmDm();
//            if (singleCsz.equals(zspmDm)) {
//                tdFlag = true;
//            }
//        } else {
//            final String zsxmDm = sbxxvo.getZsxmDm();
//            if (singleCsz.equals(zsxmDm)) {
//                tdFlag = true;
//            }
//        }
//        return tdFlag;
//    }
//
//    /**
//     *@name    拼接异常码
//     *@description 拼接异常码
//     *@time    创建时间:2014-11-19下午05:29:51
//     *@param news news
//     *@param jmsspsxDm jmsspsxDm
//     *@param jmxxList jmxxList
//     *@return String
//     *@throws SwordBaseCheckedException
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static String messPz(String news, String jmsspsxDm, List<SBJmxxVO> jmxxList) throws SwordBaseCheckedException {
//        String news_new = news;
//        for (SBJmxxVO sbjmxxvo : jmxxList) {
//            if (jmsspsxDm.equals(sbjmxxvo.getJmsspsxDm())) {
//                news_new += "征收项目：" + dmOrMc(sbjmxxvo.getZsxmDm()) + "、";
//            }
//        }
//        return news_new;
//    }
//
//    /**
//     *@name    代码转名称
//     *@description 代码转名称
//     *@time    创建时间:2014-11-14下午05:01:58
//     *@param zsxmDm zsxmDm
//     *@return String
//     *@throws SwordBaseCheckedException
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String dmOrMc(String zsxmDm) throws SwordBaseCheckedException {
//        String zsxmmc = null;
//        final Map<String, Object> xzqhMap = HxzgQxUtils.getMcByDmbAndDm("DM_GY_ZSXM", zsxmDm);
//        if (xzqhMap != null && xzqhMap.size() > 0) {
//            zsxmmc = (String) xzqhMap.get("ZSXMMC");
//        }
//        return zsxmmc;
//    }
//
//    /**
//     *
//     *@name    查询申报限额
//     *@description  查询申报限额
//     *@time    创建时间:2014-11-22下午02:55:34
//     *@param swjgDm 税务机关代码
//     *@param zsxmDm 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@param ssqq 所属期起
//     *@param ssqz 所属期止
//     *@return 限额
//     *@throws SwordBaseCheckedException 系统通用架构异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Double getSbXe(String swjgDm, String zsxmDm, String zspmDm, String ssqq, String ssqz) throws SwordBaseCheckedException {
//        Double je = 0.0;
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        List<Map<String, Object>> xeList = new ArrayList<Map<String, Object>>();
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            final List<Map<String, Object>> xeLists = findListInCacheBySwjg(bjsjSwjgDm, "CS_SB_GLB_XE");//根据税务机关查找
//            xeList = findMapListInMapList("zsxmDm", zsxmDm, xeLists);//过滤征收项目
//            xeList = findMapListInMapList("zspmDm", zspmDm, xeLists);//过滤征收项目
//            if (xeList != null && !xeList.isEmpty()) {
//                break;//找到即不再上溯
//            }
//        }
//        final String bdssqq = ssqq;
//        final String bdssqz = ssqz;
//        //先按照所属期进行排序
//        final SwordSortUtils.SortDescription description1 = new SwordSortUtils.SortDescription("yxqq", OrderType.ASC);
//        SwordSortUtils.sortMapList(xeList, description1);
//        //通过税款所属期再进行
//        for (Map<String, Object> qzdMap : xeList) {
//            final String yxqq = GYCastUtils.cast2Datestr(qzdMap.get("yxqq"));
//            final String yxqz = GYCastUtils.cast2Datestr(qzdMap.get("yxqz"));
//            final Double yje = (Double) qzdMap.get("je");
//            //开始判断所属期的条件是否满足
//            if (bdssqq.compareTo(yxqq) >= 0 && bdssqz.compareTo(yxqz) <= 0) {
//                je = yje;
//            }
//        }
//        return je;
//    }
//
//    /**
//     *
//     *@name    根据纳税期限和申报属期判断是否为上线前申报
//     *@description 根据纳税期限和申报属期判断是否为上线前申报
//     *@time    创建时间:2014-12-16上午10:26:03
//     *@param nsqxDm  纳税期限代码
//     *@param skssqzIn 税款所属期止
//     *@return sxqsbFlag
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException  系统通用架构异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean getSfsxqsbByNsqxAndSQZ(String nsqxDm, String skssqzIn) throws SwordBaseCheckedException {
//        String nsqxDmNew = nsqxDm;
//        //获取上线时间,修改为获取公用的方法
//        String sxsj = GyUtils.getSjjzrqFromZgswkfjDmandgndm(ZnsbSessionUtils.getswjgdm(), "A0000001A0600030", "A06");
//
//        if (GyUtils.isNull(sxsj)) {//如果查询不到，默认2014-01-01为第一批次上线时间
//            sxsj = "2014-12-31";
//        }
//
//        //根据纳税期限和上线时间推算上线前属期止
//        if (GYCastUtils.isNull(nsqxDmNew)) {
//            nsqxDmNew = GYDm01Constants.getDmGyNsqxY();
//        }
//        final Map<String, Object> skssqMap = GYSbqxUtils.jsSkssq(nsqxDmNew, SwordDateUtils.parseDate(sxsj, 3), "Y");//获取上线时间的当期属期
//        final Date skssqz = (Date) skssqMap.get("skssqz");
//        boolean sxqsbFlag = false;
//        if (SwordDateUtils.parseDate(skssqzIn, 3).compareTo(skssqz) < 0) {
//            sxqsbFlag = true;
//        }
//        return sxqsbFlag;
//
//    }
//
//    /**
//     *
//     *@name    根据申报属期判断是否为上线当前年申报
//     *@description 根据申报属期判断是否为上线当前年申报
//     *@time    创建时间:2014-12-16上午10:26:03
//     *@param skssqzIn 税款所属期止
//     *@return sxqsbFlag
//     *<AUTHOR>
//     * @throws SwordBaseCheckedException  系统通用架构异常
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean getSfsxnsbByNsqxAndSQZ(String skssqzIn) throws SwordBaseCheckedException {
//        //获取上线时间
//        String sxsj = GYJsUtils.getXtcs(SbzsSBZdyConstants.getDmGyXtcsbmSjyhsxsjcsbm(), ZnsbSessionUtils.getswjgdm());
//
//        if (GyUtils.isNull(sxsj)) {//如果查询不到，默认2014-01-01为第一批次上线时间
//            sxsj = "2014-12-31";
//        }
//        final String sxnf = sxsj.substring(0, 4);
//        sxsj = sxnf + "-12-31";
//        final Date skssqz = SwordDateUtils.parseDate(sxsj, 3);
//        boolean sxqsbFlag = false;
//        if (SwordDateUtils.parseDate(skssqzIn, 3).compareTo(skssqz) <= 0) {
//            sxqsbFlag = true;
//        }
//        return sxqsbFlag;
//
//    }
//
//    /**
//     *@name    获取核算机关（税款所属机关）
//     *@description 要考虑集中征收、常规职能机关对照等多种场景
//     *@time    创建时间:2014-12-18上午02:39:53
//     *@param gljgDm 管理机关代码
//     *@param zsxmDm 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@param skssqq 税款所属期起（可空）
//     *@param skssqz 税款所属期止（可空）
//     *@return 核算机关
//     *@throws SwordBaseCheckedException  通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getHsjg(String gljgDm, String zsxmDm, String zspmDm, Date skssqq, Date skssqz) throws SwordBaseCheckedException {
//        //先按照特殊职能机关对照查找核算机关
//        String skssswjgDm = getHsjgByTsgz(gljgDm, zsxmDm, zspmDm, skssqq, skssqz);
//        //如没有，再按常规方法（职能机关对照代码表）取核算机关
//        if (GYCastUtils.isNull(skssswjgDm)) {
//            skssswjgDm = getHsjgCommon(gljgDm);
//        }
//        return skssswjgDm;
//    }
//
//    /**
//     *@name    获取核算机关（税款所属机关）
//     *@description 描述
//     *@time    创建时间:2014-12-18上午02:39:53
//     *@param gljgDm 管理机关代码
//     *@param zsxmDm 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@return 核算机关
//     *@throws SwordBaseCheckedException  通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getHsjg(String gljgDm, String zsxmDm, String zspmDm) throws SwordBaseCheckedException {
//        return getHsjg(gljgDm, zsxmDm, zspmDm, null, null);
//    }
//
//    /**
//     *@name    根据特殊职能机关对照查找核算机关
//     *@description 考虑集中征收而设
//     *@time    创建时间:2014-12-18上午02:31:34
//     *@param gljgDm 管理机关
//     *@param zsxmDm 征收项目
//     *@param zspmDm 征收品目
//     *@param skssqq 税款所属期起
//     *@param skssqz 税款所属期止
//     *@return 核算机关
//     *@throws SwordBaseCheckedException  通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getHsjgByTsgz(String gljgDm, String zsxmDm, String zspmDm, Date skssqq, Date skssqz) throws SwordBaseCheckedException {
//        String skssswjgDm = null;
//        final String blswjgDm = ZnsbSessionUtils.getswjgdm();//办税税务机关
//        //1、优先按办理税务机关+管理机关  查找 特殊职能对照表，管理机关本上级逐级上查
//        //办理机关判空
//        if (GYCastUtils.notNull(blswjgDm)) {
//            //获取主管机关的本级上级税务机关
//            final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(gljgDm);
//            //上溯查找 主管税务机关的本上级
//            for (String bjsjSwjgDm : bjsjSwjgList) {
//                //按办理税务机关+管理机关 查找 特殊职能对照表
//                final String[] swjgparams = new String[2];
//                swjgparams[0]=blswjgDm;
//                swjgparams[1]=bjsjSwjgDm;
//                final List<Map<String, Object>> tsznjgdzList = findListInCacheByMultiGroupcol(swjgparams, "CS_GY_TSZNJGDZ_BLJG");//注意：这里与下面查的不是同一个缓存
//                final Map<String, Object> tsznjgdzMap = findTsznjgdzMap(bjsjSwjgDm, zsxmDm, zspmDm, skssqq, skssqz, tsznjgdzList);
//                //如果找到有效记录，表明本级有配置，即不再上溯查找
//                if (GYCastUtils.notNull(tsznjgdzMap)) {
//                    skssswjgDm = (String) tsznjgdzMap.get("skssswjgDm");
//                    break;
//                }
//            }
//            //如按办理机关在特殊职能对照表已找到核算机关，则终止查找，返回
//            if (GYCastUtils.notNull(skssswjgDm)) {
//                return skssswjgDm;
//            }
//        }
//
//        //2、按管理机关 查找特殊职能对照表，管理机关本上级逐级上查
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(gljgDm);
//        //上溯查找
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            final List<Map<String, Object>> tsznjgdzList = findListInCacheBySwjg(bjsjSwjgDm, "CS_GY_TSZNJGDZ");//根据税务机关查找
//            final Map<String, Object> tsznjgdzMap = findTsznjgdzMap(bjsjSwjgDm, zsxmDm, zspmDm, skssqq, skssqz, tsznjgdzList);
//            //如果找到有效记录，表明本级有配置，即不再上溯查找
//            if (GYCastUtils.notNull(tsznjgdzMap)) {
//                skssswjgDm = (String) tsznjgdzMap.get("skssswjgDm");
//                break;
//            }
//        }
//        return skssswjgDm;
//    }
//
//    /**
//     *@name    根据特殊职能机关对照查找征收机关
//     *@description 考虑集中征收而设
//     *@time    创建时间:2014-12-18上午02:31:25
//     *@param gljgDm 管理机关
//     *@param zsxmDm 征收项目
//     *@param zspmDm 征收品目
//     *@param skssqq 税款所属期起
//     *@param skssqz 税款所属期止
//     *@return 征收机关

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getZsjgByTsgz(String gljgDm, String zsxmDm, String zspmDm, Date skssqq, Date skssqz) throws SwordBaseCheckedException {
//        String zsjgDm = null;
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(gljgDm);
//        //上溯查找
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            final List<Map<String, Object>> tsznjgdzList = findListInCacheBySwjg(bjsjSwjgDm, "CS_GY_TSZNJGDZ");//根据税务机关查找
//            final Map<String, Object> tsznjgdzMap = findTsznjgdzMap(bjsjSwjgDm, zsxmDm, zspmDm, skssqq, skssqz, tsznjgdzList);
//            //如果找到有效记录，表明本级有配置，即不再上溯查找
//            if (GYCastUtils.notNull(tsznjgdzMap)) {
//                zsjgDm = (String) tsznjgdzMap.get("zsjgDm");
//                break;
//            }
//        }
//        return zsjgDm;
//    }
//
//    /**
//     *
//     *@name    获取核算机关
//     *@description 查询职能机关对照表获取核算机关
//     *@time    创建时间:2014-7-14上午10:01:23
//     *@param gljgDm  管理机关代码
//     *@return String
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getHsjgCommon(String gljgDm) throws SwordBaseCheckedException {
//        String skssswjgDm = null;
//        final List<String> result = HxzgQxUtils.getjgDmByGljgDm(gljgDm, ZSJConstants.getDM_GY_ZN_HSZN());
//        if (result != null && !result.isEmpty() && result.size() > 0) {
//            final String hsjgDm = result.get(0);
//            if (hsjgDm == null || "".equals(hsjgDm)) {
//                //抛出找不到核算机关异常
//                throwHsjgException(gljgDm);
//            } else {
//                skssswjgDm = hsjgDm;
//            }
//        } else {
//            //抛出找不到核算机关异常
//            throwHsjgException(gljgDm);
//        }
//        return skssswjgDm;
//    }
//
//    /**
//     *
//     *@name    获取核算机关
//     *@description 查询职能机关对照表获取核算机关
//     *@time    创建时间:2014-7-14上午10:01:23
//     *@param swjgDm  管理机关代码
//     *@param zndm 职能代码
//     *@return String
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getHsjgBySwjgAndZn(String swjgDm, String zndm) throws SwordBaseCheckedException {
//        String skssswjgDm = "";
//        if (!GyUtils.isNull(swjgDm)) {
//            final List<String> result = HxzgQxUtils.getjgDmByGljgDm(swjgDm, ZSJConstants.getDM_GY_ZN_HSZN());
//            if (result != null && !result.isEmpty() && result.size() > 0) {
//                skssswjgDm = result.get(0);
//                return skssswjgDm;
//            }
//        }
//        if (GyUtils.isNull(skssswjgDm)) {
//            if ("02".equals(zndm)) { //稽查职能
//                final String dmTableName = "DM_QX_ZNJGDZBKZ_JCJG";
//                final Map<String, Object> paramMap = new HashMap<String, Object>();
//                paramMap.put("jcjg_dm", swjgDm);
//                List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
//                list = HxzgCacheUtils.getHcbData(dmTableName, paramMap);
//                if (!GyUtils.isNull(list) && list.size() > 0) {
//                    final String gljgDm = (String) (list.get(0).get("GLJG_DM"));
//                    skssswjgDm = getHsjgCommon(gljgDm);
//                }
//            }
//        }
//        return skssswjgDm;
//    }
//
//    /**
//     *@name    抛出找不到核算机关异常
//     *@description 相关说明
//     *@time    创建时间:2014-7-14上午10:02:38
//     *@param gljgDm  管理机关代码
//     *@throws SwordBizCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void throwHsjgException(String gljgDm) throws SwordBizCheckedException {
//        //final Map<String, Object> exceptionMap = new HashMap<String, Object>();
//        //exceptionMap.put("param", gljgDm);
//        //throw new SwordBizCheckedException("1010410006000014", exceptionMap);
//        throw GYSbCxsUtils.getCommonExceptionMessage("未查询到管理机关对应的核算机关配置信息，请到dm_qx_znjgdzb表中配置，管理机关代码为:" + gljgDm);
//    }
//
//    /**
//     *@name    组织申报信息LIST
//     *@description  组织申报信息LIST将同项目的信息累加起来用于计算
//     *@time    创建时间:2014-12-19下午11:26:10
//     *@param sbxxVOList sbxxVOList
//     *@return List<Map<String, Object>>
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> assembleSbxxList(List<SBSbxxVO> sbxxVOList) throws SwordBaseCheckedException {
//        final ArrayList<SBSbxxVO> sbxxList1 = new ArrayList<SBSbxxVO>();
//        final ArrayList<SBSbxxVO> sbxxList2 = new ArrayList<SBSbxxVO>();
//        sbxxList1.addAll(sbxxVOList);
//        sbxxList2.addAll(sbxxVOList);
//        final Map<String, Object> a = new HashMap<String, Object>();
//        final List<Map<String, Object>> listmap = new ArrayList<Map<String, Object>>();
//        for (SBSbxxVO sbSbxxVO1 : sbxxList1) {
//            if (!a.containsKey(sbSbxxVO1.getZsxmDm())) {
//                a.put(sbSbxxVO1.getZsxmDm(), sbSbxxVO1.getZsxmDm());
//                Double ynse = 0.0;
//                Double jmse = 0.0;
//                Double jsyj = 0.0;
//                String zsxmDm = null;
//
//                for (SBSbxxVO sbSbxxVO2 : sbxxList2) {
//                    if (sbSbxxVO1.getZsxmDm().equals(sbSbxxVO2.getZsxmDm())) {
//                        ynse = SwordMathUtils.add(ynse, sbSbxxVO2.getYnse());
//                        jmse = SwordMathUtils.add(jmse, sbSbxxVO2.getJmse());
//                        jsyj = SwordMathUtils.add(jsyj, GYCastUtils.cast2Double(sbSbxxVO2.getJsyj()));
//                        zsxmDm = sbSbxxVO2.getZsxmDm();
//                    }
//                }
//                final Map<String, Object> map = new HashMap<String, Object>();
//                map.put("jsyj", jsyj);
//                map.put("jmse", jmse);
//                map.put("ynse", ynse);
//                map.put("zsxmDm", zsxmDm);
//
//                listmap.add(map);
//            }
//        }
//
//        return listmap;
//    }
//
//    /**
//     *@name    根据减征类型优先取 未达起征点、免征、减幅、减率、减额、特定减免
//     *@description 据减征类型优先取 未达起征点、免征、减幅、减率、减额、特定减免
//     *@time    创建时间:2014-12-19下午10:58:12
//     *@param sbJmxxVO sbJmxxVO
//     *@param sbxxMap sbxxMap
//     *@param message message
//     **@param ywsbxxMap ywsbxxMap
//     *@return String
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getJmxxJs(SBJmxxVO sbJmxxVO, Map<String, Object> sbxxMap, String message, List<Map<String, Object>> ywsbxxMap) throws SwordBaseCheckedException {
//        String mess = message;
//        final String jmzlx = sbJmxxVO.getJmzlxDm();
//        if (!GyUtils.isNull(jmzlx) && "01".equals(jmzlx)) {
//            final Double jzfd = sbJmxxVO.getJzfd();
//            final Double jzsl = sbJmxxVO.getJzsl();
//            Double jzed = sbJmxxVO.getJzed();
//            //  减征幅度
//            if (!GyUtils.isNull(jzfd) && jzfd > 0) {
//                final Double ynse = GYCastUtils.cast2Double(sbxxMap.get("ynse"));
//                final Double jzfdje = SwordMathUtils.multiple(ynse, jzfd);
//                if (jzfdje < GYCastUtils.cast2Double(sbxxMap.get("jmse"))) {
//                    mess = "征收品目：" + getMcByDmFromCacheData("DM_GY_ZSXM", "ZSXMMC", "ZSXM_DM", (String) sbxxMap.get("zsxmDm")) + "减免金额合计不能大于" + jzfdje + "、";
//                }
//            } else if (!GyUtils.isNull(jzsl) && jzsl > 0) {
//                //减征税率
//                final Double jsyj = GYCastUtils.cast2Double(sbxxMap.get("jsyj"));
//                final Double jzslje = SwordMathUtils.multiple(jsyj, jzsl);
//                if (jzslje < GYCastUtils.cast2Double(sbxxMap.get("jmse"))) {
//                    mess = "征收品目：" + getMcByDmFromCacheData("DM_GY_ZSXM", "ZSXMMC", "ZSXM_DM", (String) sbxxMap.get("zsxmDm")) + "减免金额合计不能大于" + jzslje + "、";
//                }
//            } else if (!GyUtils.isNull(jzed) && (GyUtils.isNull(sbJmxxVO.getTdjmBz())) && jzed >= 0) {
//                //减额
//                sbxxMap.put("tdjmBz", "N");
//                sbxxMap.put("swsxdm", sbJmxxVO.getJmsspsxDm());
//                jzed = getqueryZsxmByJmse((String) sbxxMap.get("zsxmDm"), jzed, ywsbxxMap);
//                if (jzed < GYCastUtils.cast2Double(sbxxMap.get("jmse"))) {
//                    mess = "征收品目：" + getMcByDmFromCacheData("DM_GY_ZSXM", "ZSXMMC", "ZSXM_DM", (String) sbxxMap.get("zsxmDm")) + "减免金额合计不能大于" + jzed + "、";
//                }
//            }
//        } else if (!GyUtils.isNull(jmzlx) && "02".equals(jmzlx)) {
//            //免征
//            if (!GyUtils.isNull(sbxxMap.get("ynse")) && !GyUtils.isNull(sbxxMap.get("jmse"))) {
//                if (GYCastUtils.cast2Double(sbxxMap.get("jmse")) > GYCastUtils.cast2Double(sbxxMap.get("ynse"))) {
//                    mess = "征收品目：" + getMcByDmFromCacheData("DM_GY_ZSXM", "ZSXMMC", "ZSXM_DM", (String) sbxxMap.get("zsxmDm")) + "免征金额不能大于应纳金额" + GYCastUtils.cast2Double(sbxxMap.get("ynse")) + "、";
//                }
//            }
//        }
//        return mess;
//    }
//
//    /**
//     *@name    根据优惠反馈减免额度加上以往申报减免金额等于本次申报错误更正时可用减免额度
//     *@description 根据优惠反馈减免额度加上以往申报减免金额等于本次申报错误更正时可用减免额度
//     *@time    创建时间:2014-12-24上午10:42:18
//     *@param zsxmDm zsxmDm
//     *@param jzed jzed
//     *@param ywsbxxMap ywsbxxMap
//     *@return Double
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static Double getqueryZsxmByJmse(String zsxmDm, Double jzed, List<Map<String, Object>> ywsbxxMap) throws SwordBaseCheckedException {
//        Double jmse = jzed;
//        for (Map<String, Object> sbxxMap : ywsbxxMap) {
//            if (!GyUtils.isNull(zsxmDm) && !GyUtils.isNull(sbxxMap.get("zsxmDm")) && zsxmDm.equals(sbxxMap.get("zsxmDm"))) {
//                if (!GyUtils.isNull(sbxxMap.get("jmse"))) {
//                    jmse = SwordMathUtils.add(jmse, GYJsUtils.cast2Double(sbxxMap.get("jmse")));
//                } else {
//                    jmse = SwordMathUtils.add(jmse, 0.00);
//                }
//            }
//        }
//        return jmse;
//    }
//
//    /**
//     *@name    中文名称
//     *@description 相关说明
//     *@time    创建时间:2014-7-25下午02:43:17
//     *@param tableName 缓存代码表名称
//     *@param mcName 代码表名称字段名
//     *@param dmName 代码表代码字段名
//     *@param dm 代码
//     *@return 代码对应的名称
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getMcByDmFromCacheData(String tableName, String mcName, String dmName, String dm) throws SwordBaseCheckedException {
//        String mc = dm;
//        //        Map<String,Object> paramMap = new HashMap<String,Object>();
//        //        paramMap.put(dmName, dm);
//        //        paramMap.put("YXBZ", "Y");
//        //        paramMap.put("XYBZ", "Y");
//        //        List<Map<String,Object>> cacheDataList = HxzgCacheUtils.getHcbData(tableName, paramMap);
//        //        if(cacheDataList!=null&& cacheDataList.size()>0) {
//        //            mc = (String) cacheDataList.get(0).get(mcName);
//        //        }
//        final Map<String, Object> cacheMap = HxzgQxUtils.getMcByDmbAndDm(tableName, dm);
//        if (cacheMap != null) {
//            mc = (String) cacheMap.get(mcName);
//        }
//        return mc;
//    }
//
//    /**
//     *
//     *@name    发票代开获取应税所得率从企业所得税核定结果表
//     *@description 发票代开获取应税所得率从企业所得税核定结果表
//     *@time    创建时间:2014-12-25下午07:54:08
//     *@param row 操作当前行
//     *@param csMap 参数信息
//     *@throws SwordBaseCheckedException ex
//     *<AUTHOR> 杨大林
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void getYssdlFromQysdsHd(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        //企业所得税，优先取值企业所得税核定结果中的应税所得率，如果取不到则取值cs_gy_glb_zszm中的下限
//        final String zsxmDm = row.getZsxmDm();//征收项目代码
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));
//        if (GYDm01Constants.getDmGyZsxmQysds().equals(zsxmDm) && "FPDK".equals(lybz)) {
//            //edit by luoxp,AHD34_201511190008按照所得税计算规则，应税所得率和征收率应该二选一，现在选择征收子目为征税率计税，应税所得率应该清空。
//            final String zszmDm = cast2Str(row.getZszmDm());//征收子目
//            if(GYCastUtils.notNull(zszmDm)){
//                return;
//            }
//            final String djxh = GYCastUtils.cast2Str(csMap.get("djxh"));
//            final List<Map<String, Object>> list = ZSHdUtils.getqysdshdjgxx(djxh, null, null, null);
//            for (Map<String, Object> map : list) {
//                if (GYCastUtils.notNull(map.get("yssdl"))) {
//                    row.setYssdl(cast2Str(map.get("yssdl")));//应税所得率默认为下限
//                    row.setXx(cast2Str(map.get("yssdl")));//应税所得率-下限
//                    row.setSx(cast2Str(map.get("yssdl")));//应税所得率-上限
//                    return;
//                }
//            }
//
//        }
//    }
//
//    /**
//     *@name    校验税款所属期录入是否正确
//     *@description 如果纳税人存在多种纳税期限情况下。需要进行校验，如果录入税款所属期满足其中一个纳税期限则默认录入日期为合法
//     *@time    创建时间:2014-12-29上午02:01:01
//     *@param skssqq skssqq
//     *@param skssqz skssqz
//     *@param sbxxList sbxxList
//     *@return map
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> checkSkssq(String skssqq, String skssqz, List<SBSbxxVO> sbxxList) throws SwordBaseCheckedException {
//        final Map<String, Object> map = new HashMap<String, Object>();
//        boolean skssqBz = false;
//        String message = "录入税款所属期不正确：本次申报录入所属期必须为：";
//        final List<Map<String, Object>> sbxxmapList = assembleNsqxSbxxList(sbxxList);
//        String nsqxDm = null;
//        for (Map<String, Object> sbxxMap : sbxxmapList) {
//            nsqxDm = (String) sbxxMap.get("nsqxDm");
//            if (!GYCastUtils.isNull(nsqxDm) && !GYCastUtils.isNull(skssqq) && !GYCastUtils.isNull(skssqz)) {
//                final boolean check = GYSbqxUtils.checkSkssq(GYJsUtils.cast2Date(skssqq), GYJsUtils.cast2Date(skssqz), nsqxDm);
//                if (check) {
//                    skssqBz = true;
//                    break;
//                }
//            }
//            message += GYJsUtils.getMcByDmFromCacheData("DM_GY_NSQX", "NSQXMC", "NSQX_DM", nsqxDm) + "或者";
//        }
//        if (!skssqBz) {
//            throw new SwordBizCheckedException(message.substring(0, message.length() - 2));
//        }
//        map.put("nsqxDm", nsqxDm);
//        map.put("skssqBz", skssqBz);
//        map.put("message", message.substring(0, message.length() - 2));
//        return map;
//    }
//
//    /**
//     *@name    组织申报信息LIST
//     *@description  组织申报信息LIST将同项目的信息累加起来用于计算
//     *@time    创建时间:2014-12-19下午11:26:10
//     *@param sbxxVOList sbxxVOList
//     *@return List<Map<String, Object>>
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 赵国良
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static List<Map<String, Object>> assembleNsqxSbxxList(List<SBSbxxVO> sbxxVOList) throws SwordBaseCheckedException {
//        final ArrayList<SBSbxxVO> sbxxList1 = new ArrayList<SBSbxxVO>();
//        final ArrayList<SBSbxxVO> sbxxList2 = new ArrayList<SBSbxxVO>();
//        sbxxList1.addAll(sbxxVOList);
//        sbxxList2.addAll(sbxxVOList);
//        final Map<String, Object> a = new HashMap<String, Object>();
//        final List<Map<String, Object>> listmap = new ArrayList<Map<String, Object>>();
//        for (SBSbxxVO sbSbxxVO1 : sbxxList1) {
//            if (!a.containsKey(sbSbxxVO1.getNsqxDm())) {
//                a.put(sbSbxxVO1.getNsqxDm(), sbSbxxVO1.getNsqxDm());
//                String nsqxDm = null;
//                for (SBSbxxVO sbSbxxVO2 : sbxxList2) {
//                    if (sbSbxxVO1.getNsqxDm().equals(sbSbxxVO2.getNsqxDm())) {
//                        nsqxDm = sbSbxxVO2.getNsqxDm();
//                    }
//                }
//                final Map<String, Object> map = new HashMap<String, Object>();
//                map.put("nsqxDm", nsqxDm);
//                listmap.add(map);
//            }
//        }
//
//        return listmap;
//    }
//
//    /**
//     *@name    计算简并征期：实现记录的复制，涉及属期、期限、主附税关系的填装等
//     *@description 注意：需按每户处理，否则可能会乱
//     *@time    创建时间:2015-1-15上午05:47:59
//     *@param sbxxList 原申报信息list
//     *@param jbzqNsqxDm 简并征期纳税期限代码
//     *@param yyDate 应用日期，通常为当天
//     *@param skssqqJbzq 简并征期的长所属期起
//     *@param skssqzJbzq 简并征期的长所属期止
//     *@return 简并征期处理后的申报信息list
//     *@throws SwordBaseCheckedException  通用异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<SBSbxxVO> jsJbzq(List<SBSbxxVO> sbxxList, String jbzqNsqxDm, final Date yyDate, final Date skssqqJbzq, final Date skssqzJbzq) throws SwordBaseCheckedException {
//        final List<SBSbxxVO> jbzqSbxxList = new ArrayList<SBSbxxVO>();
//        //为空则终止
//        if (GYCastUtils.isNull(sbxxList)) {
//            return sbxxList;
//        }
//        //如非本月份可申报的简并征期期限，终止
//        if (!GYSbqxUtils.judgeDqksbNsqx(yyDate, jbzqNsqxDm)) {
//            return sbxxList;
//        }
//
//        //如果纳税期限 非按月，直接添加到新list，不做简并征期处理
//        for (SBSbxxVO sbxxvo : sbxxList) {
//            if (!GYDm01Constants.getDmGyNsqxY().equals(sbxxvo.getNsqxDm())) {
//                jbzqSbxxList.add(sbxxvo);
//                continue;
//            }
//        }
//
//        final Calendar skssqqJbCal = SwordDateUtils.convUtilDateToUtilCalendar(skssqqJbzq);
//        final int monthQqJb = skssqqJbCal.get(Calendar.MONTH) + 1;
//        final Calendar skssqzJbCal = SwordDateUtils.convUtilDateToUtilCalendar(skssqzJbzq);
//        final int monthQzJb = skssqzJbCal.get(Calendar.MONTH) + 1;
//        //属期所在年
//        final String yearQqJb = String.valueOf(skssqqJbCal.get(Calendar.YEAR));
//        //按简并征期后的长属期跨度 按月循环
//        for (int i = monthQqJb; i <= monthQzJb; i++) {
//            //月份补成标准的两位
//            String mm = String.valueOf(i);
//            if (mm.length() == 1) {
//                mm = "0".concat(mm);
//            }
//            //属期所在年-循环到的月份-01
//            final String qqstr = yearQqJb.concat("-").concat(mm).concat("-").concat("01");
//            final Date skssqq = SwordDateUtils.parseDate(qqstr, 3);//每月月初
//            final Date skssqz = SwordDateUtils.lastDay(qqstr).getTime();//每月月末
//            //每个月份需新建该idmap，用于主附税之间关联，不能各月份之间混用，因为仅每月分开才能保证每次rdpzuuid唯一
//            final Map<String, Object> idMap = new HashMap<String, Object>();
//            //先循环每个月份的主税，并记录新生成的主税id
//            for (SBSbxxVO sbxxvo : sbxxList) {
//                //只有按月的才简并征期，不按月的，跳过
//                if (!GYDm01Constants.getDmGyNsqxY().equals(sbxxvo.getNsqxDm())) {
//                    continue;
//                }
//                //zfsbz!=1视为主税
//                final String zsfbz = sbxxvo.getZfsbz();//主附税标志
//                if (GYCastUtils.isNull(zsfbz) || !"1".equals(zsfbz)) {
//                    final String rdpzuuid = sbxxvo.getRdpzuuid();//认定凭证uuid
//                    final String newid = SwordSequenceUtils.generateRandomString();//新生成主键
//                    //判空rdpzuuid
//                    if (GYCastUtils.notNull(rdpzuuid)) {
//                        idMap.put(rdpzuuid, newid);//填装到idmap中方便附税查找
//                    }
//                    SBSbxxVO newvo = new SBSbxxVO();
//                    //复制sbxxvo
//                    newvo = SwordTypeUtils.copyBean(sbxxvo, newvo);
//                    newvo.setSkssqq(GYCastUtils.cast2Str(skssqq));//分每月的属期
//                    newvo.setSkssqz(GYCastUtils.cast2Str(skssqz));
//                    newvo.setLsmxuuid(newid);//新生成临时主键
//                    newvo.setSbxxuuid(newid);//新生成主键
//                    jbzqSbxxList.add(newvo);//添加新vo到简并征期list中
//
//                }
//            }
//            //再循环每个月份的附税，并关联主附税关系
//            for (SBSbxxVO sbxxvo : sbxxList) {
//                //只有按月的才简并征期，不按月的，跳过
//                if (!GYDm01Constants.getDmGyNsqxY().equals(sbxxvo.getNsqxDm())) {
//                    continue;
//                }
//                //String rdpzuuid = sbxxvo.getRdpzuuid();//认定凭证uuid
//                final String rdzsuuid = sbxxvo.getRdzsuuid();//认定主税uuid
//                final String zfsbz = sbxxvo.getZfsbz();//主附税标志
//                ////判别附税：主附税标志=1,rdzsuuid不为空，不为0，且能找到主税
//                if ("1".equals(zfsbz) && GYCastUtils.notNull(rdzsuuid) && !"0".equals(rdzsuuid) && idMap.containsKey(rdzsuuid)) {
//                    final String zsxxid = (String) idMap.get(rdzsuuid);
//                    final String newid = SwordSequenceUtils.generateRandomString();//新生成主键
//                    SBSbxxVO newvo = new SBSbxxVO();
//                    //复制sbxxvo
//                    newvo = SwordTypeUtils.copyBean(sbxxvo, newvo);
//                    newvo.setSkssqq(GYCastUtils.cast2Str(skssqq));//分每月的属期
//                    newvo.setSkssqz(GYCastUtils.cast2Str(skssqz));
//                    newvo.setLsmxuuid(newid);//新生成临时主键
//                    newvo.setSbxxuuid(newid);//新生成主键
//                    newvo.setZsxxid(zsxxid);//主税uuid
//                    jbzqSbxxList.add(newvo);//添加新vo到简并征期list中
//                }
//            }
//        }
//        return jbzqSbxxList;
//    }
//
//    /**
//     *
//     *@name    获取计税月份数
//     *@description 相关说明,获取计税月份数
//     *@time    创建时间:2015-4-27下午04:29:23
//     *@param nsqxDm 纳税期限代码
//     *@param skssqq 税款所属期起
//     *@param skssqz 税款所属期止
//     *@param yxqq1   有效期起
//     *@param yxqz1 有效期止
//     **@param zjyfs 增加月份数,从当月计税，0，对于下月计税的，1
//     *@return  jsyfs 计税月份数
//     *@throws SwordBaseCheckedException  系统通用架构异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static int getJsyfs(String nsqxDm, String skssqq, String skssqz, String yxqq1, String yxqz1, int zjyfs) throws SwordBaseCheckedException {
//        int jsyfs = 0;// 默认1
//        String yxqz = yxqz1;
//        String yxqq = yxqq1;
//        if (GyUtils.isNull(nsqxDm)) {
//            return jsyfs;
//        }
//        if (GyUtils.isNull(skssqq)) {
//            return jsyfs;
//        }
//        if (GyUtils.isNull(skssqz)) {
//            return jsyfs;
//        }
//        if (GyUtils.isNull(yxqq1)) {
//            return jsyfs;
//        }
//        if (GyUtils.isNull(yxqz1)) {
//            yxqz = "9999-12-31";
//        }
//        if (yxqq1.length() > 10) {
//            yxqq = yxqq1.substring(0, 10);
//        }
//        if (yxqz.length() > 10) {
//            yxqz = yxqz.substring(0, 10);
//        }
//        /*      if(zjyfs==1){
//                  final Calendar lastDay =SwordDateUtils.lastDay(yxqq1);//获取土地或房产初始取得日期当月最后一天
//                  final Calendar calendar = new GregorianCalendar();
//                  calendar.setTime(lastDay.getTime());
//                  calendar.add(Calendar.DATE, 1);//加一天，
//                  yxqq = SwordDateUtils.dateToString(calendar.getTime());
//              }*/
//
//        //如果税款所属期起止和纳税期限不一致,则返回计税月份数为0,不能汇总计税
//        /* final Map<String, Object> skMap = GYSbqxUtils.jsSkssq(nsqxDm, GYCastUtils.cast2Date(skssqq), "N");
//         final String skssqqNsqx = GYCastUtils.cast2Str(skMap.get("skssqq"));
//         final String skssqzNsqx = GYCastUtils.cast2Str(skMap.get("skssqz"));
//
//         if(skssqqNsqx.equals(skssqq)||skssqzNsqx.equals(skssqz)){
//             return jsyfs;
//         }*/
//
//        // 税款所属期在有效期内 正常算法取期数
//        if (yxqq.compareTo(skssqq) < 0 && skssqz.compareTo(yxqz) <= 0) {
//            /*   if (nsqxDm == "06") {
//                   jsyfs = 1;
//               } else if (nsqxDm == "08") {
//                   jsyfs = 3;
//               } else if (nsqxDm == "09") {
//                   jsyfs = 6;
//               } else if (nsqxDm == "10") {
//                   jsyfs = 12;
//               }*/
//            jsyfs = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(skssqq), GYCastUtils.cast2Date(skssqz));
//            // 税款所属期起在有效期起之前，取有效期起与税款所属期止的月份差+1
//        } else if (yxqq.compareTo(skssqq) >= 0 && skssqz.compareTo(yxqz) <= 0) {
//            jsyfs = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(yxqq), GYCastUtils.cast2Date(skssqz)) - zjyfs;
//            // 税款所属期止在有效期止之后，取税款所属期起与有效期止的月份差+1
//        } else if (yxqq.compareTo(skssqq) < 0 && skssqz.compareTo(yxqz) > 0) {
//            jsyfs = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(skssqq), GYCastUtils.cast2Date(yxqz));
//            // 有效期在税款所属期之内，取有效期起止的月份差+1
//        } else if (yxqq.compareTo(skssqq) >= 0 && skssqz.compareTo(yxqz) > 0) {
//            jsyfs = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(yxqq), GYCastUtils.cast2Date(yxqz)) - zjyfs;
//        }
//
//        if (jsyfs < 0) {
//            jsyfs = 0;
//        }
//        return jsyfs;
//    }
//    /**
//     *
//     *@name    获取计税月份数
//     *@description 相关说明,获取计税月份数
//     *@time    创建时间:2015-4-27下午04:29:23
//     *@param skssqq 税款所属期起
//     *@param skssqz 税款所属期止
//     *@param yxqq1   有效期起
//     *@param yxqz1 有效期止
//     *@return  jsyfs 计税月份数
//     *@throws SwordBaseCheckedException  系统通用架构异常
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static int getJsyfsforccs(String skssqq, String skssqz, String yxqq1, String yxqz1) throws SwordBaseCheckedException {
//        int jsyfs = 0;// 默认1
//        String yxqz = yxqz1;
//        String yxqq = yxqq1;
//        if (GyUtils.isNull(skssqq)) {
//            return jsyfs;
//        }
//        if (GyUtils.isNull(skssqz)) {
//            return jsyfs;
//        }
//        if (GyUtils.isNull(yxqq1)) {
//            return jsyfs;
//        }
//        //计算应补退税额和期数
//        int zjyfs = 0;
//        if (GyUtils.isNull(yxqz1)) {
//            yxqz = "9999-12-31";
//        }else{
//            zjyfs = 1;
//        }
//        if (yxqq1.length() > 10) {
//            yxqq = yxqq1.substring(0, 10);
//        }
//        if (yxqz.length() > 10) {
//            yxqz = yxqz.substring(0, 10);
//        }
//
//        // 税款所属期在有效期内 正常算法取期数
//        if (yxqq.compareTo(skssqq) < 0 && skssqz.compareTo(yxqz) <= 0) {
//            jsyfs = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(skssqq), GYCastUtils.cast2Date(skssqz));
//            // 税款所属期起在有效期起之前，取有效期起与税款所属期止的月份差+1
//        } else if (yxqq.compareTo(skssqq) >= 0 && skssqz.compareTo(yxqz) <= 0) {
//            jsyfs = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(yxqq), GYCastUtils.cast2Date(skssqz));
//            // 税款所属期止在有效期止之后，取税款所属期起与有效期止的月份差+1
//        } else if (yxqq.compareTo(skssqq) < 0 && skssqz.compareTo(yxqz) > 0) {
//            jsyfs = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(skssqq), GYCastUtils.cast2Date(yxqz))  - zjyfs;
//            // 有效期在税款所属期之内，取有效期起止的月份差+1
//        } else if (yxqq.compareTo(skssqq) >= 0 && skssqz.compareTo(yxqz) > 0) {
//            jsyfs = GYCastUtils.jsYfkd(GYCastUtils.cast2Date(yxqq), GYCastUtils.cast2Date(yxqz)) - zjyfs;
//        }
//        //车船税含头不含尾,如果yxqz在skssqz内,则月份数-1
//        if(skssqz.compareTo(yxqz)==0){
//            jsyfs--;
//        }
//        if (jsyfs < 0) {
//            jsyfs = 0;
//        }
//        return jsyfs;
//    }
//    /**
//     *@name 对于征收项目为“个人所得税”且征收品目为“个体户生产经营所得”，若税务机关对纳税人核定了税额，取其征收子目、税率，不可修改，税率为核定的税率。
//     *@description 对于征收项目为“个人所得税”且征收品目为“个体户生产经营所得”，若税务机关对纳税人核定了税额，取其征收子目、税率，不可修改，税率为核定的税率。
//     *@time    创建时间:2014-12-25下午07:54:08
//     *@param row 操作当前行
//     *@param csMap 参数信息
//     *@return 已核定标识
//     *@throws SwordBaseCheckedException ex
//     *<AUTHOR> 罗兴攀
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static boolean dealGthscjysd(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String zsxmDm = row.getZsxmDm();//征收项目代码
//        final String zspmDm = row.getZspmDm();//征收项目代码
//        //来源标识为空，或不是来自于发票代开，征收项目不为个人所得税，征收品目不为个体户生产经营所得，个人所得税核定信息为空返回
//        if (GYCastUtils.isNull(lybz) || !"FPDK".equals(lybz) || !GYDm01Constants.getDmGyZsxmGrsds().equals(zsxmDm) || !GYDm01Constants.getDmGyZspmGthscjysd().equals(zspmDm)
//                || GYCastUtils.isNull(csMap.get("grsdsHdList"))) {
//            return false;
//        }
//        final List<Map<String, Object>> list = (List<Map<String, Object>>) (csMap.get("grsdsHdList"));//个人所得税核定信息
//        //个人所得税核定信息为空返回
//        if (GYCastUtils.isNull(list)) {
//            return false;
//        }
//        boolean flag = false;//未核定
//        for (Map<String, Object> map : list) {
//            final String zspmDmTemp = GYCastUtils.cast2Str(map.get("zspmDm"));//征收品目
//            if (GYCastUtils.notNull(zspmDmTemp) && zspmDmTemp.equals(zspmDm)) {
//                final String zszmDm = GYCastUtils.cast2Str(map.get("jyxmDm"));//征收子目
//                final String zszmDmTemp = GYCastUtils.cast2Str(row.getZszmDm());//页面征收子目
//                /**
//                 * 1.双定户做代开时如果核定到子目的，系统带出子目为默认值，允许修改为配置的子目列表。
//                 * 2.双定户代开时如果仅核定到品目的（包括迁移的情况），系统默认不带出子目，但可选择对应配置的子目列表。(暂不放开)
//                 * edit by luoxp 2015/8/10 最新需求
//                 */
//                boolean hdSfl = false;//是否使用核定税费率
//                //双定户做代开时如果核定到子目的，系统带出子目为默认值。
//                if (GYCastUtils.isNull(zszmDmTemp) && GYCastUtils.notNull(zszmDm)) {
//                    row.setZszmDm(zszmDm);
//                    hdSfl = true;
//                }
//                //选择的子目与核定的子目相同时取核定信息。
//                if (GYCastUtils.notNull(zszmDmTemp) && GYCastUtils.notNull(zszmDm) && zszmDmTemp.equals(zszmDm)) {
//                    row.setZszmDm(zszmDm);
//                    hdSfl = true;
//                }
//                //双定户代开时如果仅核定到品目的（包括迁移的情况），系统默认不带出子目。
//                if(GYCastUtils.isNull(zszmDm)){
//                    row.setZszmDm(null);
//                    hdSfl = true;
//                }
//                //如果使用核定税费率
//                if (hdSfl) {
//                    final String jsbz1 = GYCastUtils.cast2Str(map.get("jsbz1"));//计税标志
//                    row.setJsbz1(jsbz1);//计税标志
//                    //征收率
//                    if (GYJsConstants.getJsbzZsl().equals(jsbz1)) {
//                        row.setSl(GYCastUtils.cast2Str(map.get("sl1")));//征收率默认为下限
//                        row.setFdsl(GYCastUtils.cast2Str(map.get("sl1")));//法定税率
//                        row.setYssdl(null);//应税所得率默认为下限
//                        row.setTsjsbz(null);
//                    }
//                    //税率
//                    else if (GYJsConstants.getJsbzSl().equals(jsbz1)) {
//                        row.setSl(GYCastUtils.cast2Str(map.get("sl1")));//税率默认为下限
//                        row.setFdsl(GYCastUtils.cast2Str(map.get("sl1")));//法定税率
//                        row.setYssdl(null);//应税所得率默认为下限
//                        row.setTsjsbz(null);
//                    }
//                    //应税所得率、所得率
//                    else if (GYJsConstants.getJsbzYssdl().equals(jsbz1) || GYJsConstants.getJsbzSdl().equals(jsbz1)) {
//                        row.setYssdl(GYCastUtils.cast2Str(map.get("yssdl")));//应税所得率默认为下限
//                        row.setXx(GYCastUtils.cast2Str(map.get("yssdl")));//应税所得率-下限
//                        row.setSx(GYCastUtils.cast2Str(map.get("yssdl")));//应税所得率-上限
//                        row.setTsjsbz(GYJsConstants.getTsjsbzGslj());
//                    }
//                    //子目_累进税率
//                    else if (GYJsConstants.getJsbzLjsl().equals(jsbz1)) {
//                        row.setSl(GYCastUtils.cast2Str(map.get("sl1")));//税率默认为下限
//                        row.setFdsl(GYCastUtils.cast2Str(map.get("sl1")));//法定税率
//                        row.setTsjsbz(GYJsConstants.getTsjsbzGslj());
//                    }
//                    //子目_西藏模式
//                    else if (GYJsConstants.getJsbzXzms().equals(jsbz1)) {
//                        row.setSl(GYCastUtils.cast2Str(map.get("sl1")));//税率默认为下限
//                        row.setFdsl(GYCastUtils.cast2Str(map.get("sl1")));//法定税率
//                        row.setYssdl(null);//应税所得率默认为下限
//                        row.setTsjsbz(null);
//                        //csmap中增加发票代开西藏模式标识
//                        csMap.put("fpdkXzms", "1");
//                    }
//                    flag = true;
//                    break;
//                }
//            }
//        }
//        return flag;
//    }
//
//    /**
//     *@name    营业税申报校验税款所属期录入是否正确(在原有判断基础上，如果不满足月、季度、半年、年的情况，则归为按次)
//     *@description 如果纳税人存在多种纳税期限情况下。需要进行校验，如果录入税款所属期满足其中一个纳税期限则默认录入日期为合法
//     *@time    创建时间:2015-8-22下午03:32:35
//     *@param skssqq skssqq
//     *@param skssqz skssqz
//     *@param sbxxList sbxxList
//     *@return nsqxDmstr 纳税期限集合
//     *@throws SwordBaseCheckedException
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String checkSkssqForYys(String skssqq, String skssqz, List<SBSbxxVO> sbxxList) throws SwordBaseCheckedException {
//        String nsqxDmstr = "";
//        final List<Map<String, Object>> sbxxmapList = assembleNsqxSbxxList(sbxxList);
//        String nsqxDm = null;
//        for (Map<String, Object> sbxxMap : sbxxmapList) {
//            nsqxDm = (String) sbxxMap.get("nsqxDm");
//            if (!GYCastUtils.isNull(nsqxDm) && !GYCastUtils.isNull(skssqq) && !GYCastUtils.isNull(skssqz)) {
//                final boolean check = GYSbqxUtils.checkSkssqForYys(GYJsUtils.cast2Date(skssqq), GYJsUtils.cast2Date(skssqz), nsqxDm);
//                if (check) {
//                    nsqxDmstr += nsqxDm+",";
//                }
//            }
//        }
//        return nsqxDmstr;
//    }
//
//    /**
//     *@name    判斷征收品目是否是個稅的按次征收品目，如是，计税依据*70%
//     *@description 判斷征收品目是否是個稅的按次征收品目，如是，计税依据*70%
//     *@time    创建时间:2014-12-28下午04:39:32
//     *@param row 计税行
//     *@param csMap 参数
//     *@throws SwordBaseCheckedException ex
//     *<AUTHOR> 罗兴攀
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void dealAczsGszspm(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String zsxmDm = GYCastUtils.cast2Str(row.getZsxmDm());//征收项目
//        //来源标识为发票代开，征收项目为个人所得税
//        if (GYCastUtils.notNull(lybz) && "FPDK".equals(lybz) && GYDm01Constants.getDmGyZsxmGrsds().equals(zsxmDm)){
//            final String zspmDm = GYCastUtils.cast2Str(row.getZspmDm());//征收项目
//            final String xyszzspm = GYJsUtils.getXtcs("A0000002053000099", ZnsbSessionUtils.getswjgdm());//按次征收品目
//            //只针对征收品目为“稿酬所得”
//            final String gcsd = GYDm01Constants.getDmGyZspmGcsd();
//            if(GYCastUtils.notNull(zspmDm) && zspmDm.equals(gcsd) && xyszzspm.indexOf(zspmDm) != -1){
//                Double jsyj = GYCastUtils.cast2Double(row.getJsyj());
//                if(GYCastUtils.notNull(jsyj)){
//                    jsyj = SwordMathUtils.multiple(jsyj, 0.7);//计税依据*70%
//                    row.setJsyj(GYCastUtils.cast2Str(jsyj));
//                }
//            }
//
//        }
//    }
//    /**
//     *@name    解析房产一体化主税税种配置信息串
//     *@description 相关说明
//     *@time    创建时间:2014-8-13下午10:55:19
//     *@param zsxmDm 征收项目代码
//     *@param zspmDm 征收品目代码
//     *@param zsxx 主税信息串
//     *@return zsszBz 主税标志 1主税 0 未配置
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String parseZsxx(String zsxmDm, String zspmDm, String zsxx) {
//        String zsszBz ="0";//默认：没有配置
//        if(GyUtils.isNull(zsxx)){
//            return zsszBz;
//        }
//        final String[] zsxxs = zsxx.split(",");
//        final int length = zsxxs.length;
//        for (int i = 0; i < length; i++) {
//            final String singleZsxx = zsxxs[i];
//            if (singleZsxx.indexOf("@") >= 0) {
//                if (singleZsxx.split("@")[0].equals(zsxmDm) && singleZsxx.indexOf(zspmDm) >= 0) {
//                    zsszBz = "1";
//                    break;
//                }
//            } else {
//                if (singleZsxx.equals(zsxmDm)) {
//                    zsszBz ="1";
//                    break;
//                }
//            }
//        }
//        return zsszBz;
//    }
//    /**
//     *
//     *@name    过滤纳税期限房土车优化版
//     *@description 过滤纳税期限房土车优化版
//     *@time    创建时间:2015-12-9下午06:20:43
//     *@param nsqxcsList 纳税期限信息
//     *@param zspmDm 征收品目代码
//     *@param nsrlx 纳税人类型
//     *@param hyDm 行业代码
//     *@param cxrq 查询日期
//     *@return 纳税期限时间
//     *@throws SwordBaseCheckedException ex
//     *<AUTHOR> yangdalin
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO getCxsFtcMrqx(List<Map<String, Object>> nsqxcsList,String zspmDm, String nsrlx, String hyDm,Date cxrq) throws SwordBaseCheckedException{
//        final GYJsSfzVO sfzVO=new GYJsSfzVO();
//        for (Map<String, Object> mrqxKvMap : nsqxcsList) {
//            if (mrqxKvMap != null && !mrqxKvMap.isEmpty()) {
//                if(!judgeYxq(cxrq,mrqxKvMap)){
//                    continue;
//                }
//                final String zspmDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("zspmDm"));
//                final String nsrlx_ = GYCastUtils.cast2Str(mrqxKvMap.get("nsrlx"));
//                final String hyDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("hyDm"));
//                //如果征收品目没有匹配上则跳出当次循环
//                if (!judegeZspmDm(zspmDm_,zspmDm)) {
//                    continue;
//                }
//                //如果纳税人类型没有匹配上则跳出当次循环
//                if(!judgeNsrlx(nsrlx_,nsrlx)){
//                    continue;
//                }
//                if (judgeHyDm(hyDm_,hyDm)) {
//                    //如果行业匹配通过，则添加到返回信息当中
//                    sfzVO.setNsqxDm((String)mrqxKvMap.get("nsqxDm"));
//                    sfzVO.setJkqx((String)mrqxKvMap.get("jkqxDm"));
//                    sfzVO.setJkqx((String)mrqxKvMap.get("sbqxDm"));
//                    return sfzVO;
//                }
//            }
//        }
//        return sfzVO;
//    }
//
//    /**
//     *@name    获取房土车默认纳税期限/申报期限/缴款期限代码
//     *@description 相关说明
//     *@time    创建时间:2015年9月28日下午3:26:20
//     *@param swjgDm swjgDm
//     *@param zsxmDm swjgDm
//     *@param zspmDm swjgDm
//     *@param nsrlx 0 纳税人  1自然人
//     *@param hyDm hyDm
//     *@return 期限map
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR> yangdalin
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> getCxsFtcMrqx(String swjgDm, String zsxmDm, String zspmDm, String nsrlx, String hyDm) throws SwordBaseCheckedException {
//        String swjg=swjgDm;
//        if (GyUtils.isNull(swjgDm)) {
//            swjg = ZnsbSessionUtils.getswjgdm();
//        }
//        //返回list
//        List<Map<String, Object>> reList = new ArrayList<Map<String, Object>>();
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = GYJsUtils.findBjsjSwjgDmInCache(swjg);
//
//        //逐级上查，找到某级税务机关 有记录，则返回该级数据
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            List<Map<String, Object>> mrqxKvList = findListInCache(bjsjSwjgDm, "SWJG_DM", "CS_SB_FTC_MRQXGZ");//根据税务机关过滤
//            mrqxKvList = GYJsUtils.findListInMapList("zsxmDm", zsxmDm, mrqxKvList);//根据征收项目再次查找
//            if (mrqxKvList != null && mrqxKvList.size() > 0) {
//                for (Map<String, Object> mrqxKvMap : mrqxKvList) {
//                    if (mrqxKvMap != null && !mrqxKvMap.isEmpty()) {
//                        final String zspmDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("zspmDm"));
//                        final String nsrlx_ = GYCastUtils.cast2Str(mrqxKvMap.get("nsrlx"));
//                        final String hyDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("hyDm"));
//
//                        //如果征收品目没有匹配上则跳出当次循环
//                        if (!judegeZspmDm(zspmDm_,zspmDm)) {
//                            continue;
//                        }
//                        //如果纳税人类型没有匹配上则跳出当次循环
//                        if(!judgeNsrlx(nsrlx_,nsrlx)){
//                            continue;
//                        }
//                        if (judgeHyDm(hyDm_,hyDm)) {
//                            //如果行业匹配通过，则添加到返回信息当中
//                            reList.add(mrqxKvMap);
//                        }
//                    }
//                }
//
//            }
//
//        }
//        if(!GyUtils.isNull(reList)&&reList.size()>0){
//            final SwordSortUtils.SortDescription description1 = new SwordSortUtils.SortDescription(
//                    "swjgDm", SwordSortUtils.OrderType.DESC);
//            reList= SwordSortUtils.sortMapList(reList, description1);
//        }
//        return reList;
//    }
//
//    /**
//     *
//     *@name    判断数据的有效期是否有效
//     *@description 判断数据的有效期是否有效
//     *@time    创建时间:2015-12-9下午08:48:38
//     *@param yxqcs 有效期参数
//     *@param mrqxKvMap 数据库参数
//     *@return true :匹配  false：不匹配
//     *@throws SwordBaseCheckedException
//     *<AUTHOR> 杨大林
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static boolean judgeYxq(Date yxqcs, Map<String, Object> mrqxKvMap) throws SwordBaseCheckedException {
//        final String yxqqTemp = "1900-01-01";
//        final String yxqzTemp = "2999-12-31";
//        //IM131002020063000004 去掉时分秒，避免判断错误
//        final Date yxqcs2 = GYCastUtils.cast2Date(GYCastUtils.cast2StrNew(yxqcs));
//        final Date yxqq = GyUtils.isNull(mrqxKvMap.get("yxqq")) ? SwordDateUtils.StringToDate(yxqqTemp) : (Date) mrqxKvMap.get("yxqq");
//        final Date yxqz = GyUtils.isNull(mrqxKvMap.get("yxqz")) ? SwordDateUtils.StringToDate(yxqzTemp) : (Date) mrqxKvMap.get("yxqz");
//        if (yxqcs2.before(yxqq) || yxqcs2.after(yxqz)) {
//            return false;
//        }
//        return true;
//    }
//    /**
//     *
//     *@name    匹配纳税人类型
//     *@description 匹配纳税人类型
//     *@time    创建时间:2015-12-9下午08:36:36
//     *@param nsrlxDB 来自数据库的纳税人类型
//     *@param nsrlxCS 来自入参的纳税人类型
//     *@return true :匹配  false：不匹配
//     *<AUTHOR> 杨大林
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static boolean judgeNsrlx(String nsrlxDB, String nsrlxCS) {
//
//        if (GyUtils.isNull(nsrlxCS) || "2".equals(nsrlxDB) || nsrlxDB.equals(nsrlxCS)) {
//            return true;
//        }
//        return false;
//    }
//    /**
//     *
//     *@name    匹配行业代码
//     *@description 匹配行业代码
//     *@time    创建时间:2015-12-9下午08:23:46
//     *@param hyDmDB 来自数据库的行业代码
//     *@param hyDmCS 来自入参的行业代码
//     *@return true :匹配  false：不匹配
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static boolean judgeHyDm(String hyDmDB, String hyDmCS) {
//
//        if (GyUtils.isNull(hyDmCS) || hyDmDB.equals("%%%%")) {
//            return true;
//        }
//        String hyDmTemp = hyDmCS;
//        //一次全匹配行业代码
//        if (hyDmDB.equals(hyDmTemp)) {
//            return true;
//        }
//        //二次匹配行业代码前两位
//        hyDmTemp = hyDmCS.substring(0, 2).concat("%%");
//        if (hyDmDB.equals(hyDmTemp)) {
//            return true;
//        }
//
//        return false;
//    }
//    /**
//     *
//     *@name    判断征收品目是否匹配
//     *@description 判断征收品目是否匹配
//     *@time    创建时间:2015-12-9下午08:00:48
//     *@param zspmDmDB 来源于参数表的征收品目，
//     *@param zspmDmCS 来源于传入的参数品目
//     *@return true :匹配  false：不匹配
//     *<AUTHOR> 杨大林
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    private static boolean judegeZspmDm(String zspmDmDB,String zspmDmCS){
//        String zspmDmTemp = zspmDmCS;
//
//        //如果传入的参数是空，或者数据库参数全是%%%号组成，能通配，返回true
//        if(zspmDmDB.equals("%%%%%%%%%")|| GyUtils.isNull(zspmDmCS)){
//            return true;
//        }
//        //第一次全匹配征收品目代码
//        if (zspmDmDB.equals(zspmDmTemp)) {
//            return true;
//        }
//        //第二次匹配前七位
//        zspmDmTemp = zspmDmCS.substring(0, 7).concat("%%");
//        if (zspmDmDB.equals(zspmDmTemp)) {
//            return true;
//        }
//        //第三次匹配前五位
//        zspmDmTemp = zspmDmCS.substring(0, 5).concat("%%%%");
//        if (zspmDmDB.equals(zspmDmTemp)) {
//            return true;
//        }
//        return false;
//
//    }
//    /**
//     *@name    获取单条房土车默认纳税期限/申报期限/缴款期限代码
//     *@description 相关说明
//     *@time    创建时间:2015年9月28日下午3:26:20
//     *@param swjgDm swjgDm
//     *@param zsxmDm swjgDm
//     *@param zspmDm swjgDm
//     *@param nsrlx 0 纳税人  1自然人
//     *@param hyDm hyDm
//     *@param cxrq cxrq 查询时间
//     *@return 期限map
//     *<AUTHOR> yangdalin
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String, Object> getSingleCxsFtcMrqx(String swjgDm, String zsxmDm, String zspmDm, String nsrlx, String hyDm,Date cxrq) {
//        String swjg=swjgDm;
//        if (GyUtils.isNull(swjgDm)) {
//            MockUtils.mockSession();
//            swjg = ZnsbSessionUtils.getSwjgDm();
//        }
//        Date cxrqTemp = cxrq;
//        if(GyUtils.isNull(cxrq)){
//            cxrqTemp = DateUtils.getSystemCurrentTime().getTime();
//        }
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = GYJsUtils.findBjsjSwjgDmInCache(swjg);
//
//        //逐级上查，找到某级税务机关 有记录，则返回该级数据
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            List<Map<String, Object>> mrqxKvList = findListInCache(bjsjSwjgDm, "SWJG_DM", "CS_SB_FTC_MRQXGZ");//根据税务机关过滤
//            mrqxKvList = GYJsUtils.findListInMapList("zsxmDm", zsxmDm, mrqxKvList);//根据征收项目再次查找
//            if (mrqxKvList != null && mrqxKvList.size() > 0) {
//                for (Map<String, Object> mrqxKvMap : mrqxKvList) {
//                    if (mrqxKvMap != null && !mrqxKvMap.isEmpty()) {
//
//                        if(!judgeYxq(cxrqTemp,mrqxKvMap)){
//                            continue;
//                        }
//                        final String zspmDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("zspmDm"));
//                        final String nsrlx_ = GYCastUtils.cast2Str(mrqxKvMap.get("nsrlx"));
//                        final String hyDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("hyDm"));
//                        //如果征收品目没有匹配上则跳出当次循环
//                        if (!judegeZspmDm(zspmDm_,zspmDm)) {
//                            continue;
//                        }
//                        //如果纳税人类型没有匹配上则跳出当次循环
//                        if(!judgeNsrlx(nsrlx_,nsrlx)){
//                            continue;
//                        }
//                        if (judgeHyDm(hyDm_,hyDm)) {
//                            //如果行业匹配通过，则添加到返回信息当中
//                            return mrqxKvMap;
//                        }
//                    }
//                }
//
//            }
//
//        }
//        return null;
//    }
//    /**
//     *@name    获取房土车默认纳税期限/申报期限/缴款期限代码
//     *@description 相关说明
//     *@time    创建时间:2015年9月28日下午3:26:20
//     *@param swjgDm swjgDm
//     *@param zsxmDm swjgDm
//     *@param zspmDm swjgDm
//     *@param nsrlx 0 纳税人  1自然人
//     *@param hyDm hyDm
//     *@param cxrq 查询日期
//     *@return 期限map
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR> yangdalin
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static List<Map<String, Object>> getCxsFtcMrqx(String swjgDm, String zsxmDm, String zspmDm, String nsrlx, String hyDm,Date cxrq) throws SwordBaseCheckedException {
//        String swjg=swjgDm;
//        if (GyUtils.isNull(swjgDm)) {
//            swjg = ZnsbSessionUtils.getswjgdm();
//        }
//        //返回list
//        List<Map<String, Object>> reList = new ArrayList<Map<String, Object>>();
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = GYJsUtils.findBjsjSwjgDmInCache(swjg);
//
//        //逐级上查，找到某级税务机关 有记录，则返回该级数据
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            List<Map<String, Object>> mrqxKvList = findListInCache(bjsjSwjgDm, "SWJG_DM", "CS_SB_FTC_MRQXGZ");//根据税务机关过滤
//            mrqxKvList = GYJsUtils.findListInMapList("zsxmDm", zsxmDm, mrqxKvList);//根据征收项目再次查找
//            if (mrqxKvList != null && mrqxKvList.size() > 0) {
//                for (Map<String, Object> mrqxKvMap : mrqxKvList) {
//                    if (mrqxKvMap != null && !mrqxKvMap.isEmpty()) {
//                        if(!judgeYxq(cxrq,mrqxKvMap)){
//                            continue;
//                        }
//                        final String zspmDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("zspmDm"));
//                        final String nsrlx_ = GYCastUtils.cast2Str(mrqxKvMap.get("nsrlx"));
//                        final String hyDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("hyDm"));
//
//                        //如果征收品目没有匹配上则跳出当次循环
//                        if (!judegeZspmDm(zspmDm_,zspmDm)) {
//                            continue;
//                        }
//                        //如果纳税人类型没有匹配上则跳出当次循环
//                        if(!judgeNsrlx(nsrlx_,nsrlx)){
//                            continue;
//                        }
//                        if (judgeHyDm(hyDm_,hyDm)) {
//                            //如果行业匹配通过，则添加到返回信息当中
//                            reList.add(mrqxKvMap);
//                        }
//                    }
//                }
//
//            }
//
//        }
//        if(!GyUtils.isNull(reList)&&reList.size()>0){
//            final SwordSortUtils.SortDescription description1 = new SwordSortUtils.SortDescription(
//                    "swjgDm", SwordSortUtils.OrderType.DESC);
//            reList= SwordSortUtils.sortMapList(reList, description1);
//        }
//        return reList;
//    }
//
//    /**
//     *@name    获取纳税期限值【批扣使用】
//     *@description 获取纳税期限值【批扣使用】
//     *@time    创建时间:2015年9月28日下午3:26:20
//     *@param zspmDm zspmDm
//     *@param nsqxcsList nsqxcsList
//     *@param nsrxx nsrxx
//     *@return 期限map
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR> yangdalin
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static Map<String,Object> getNsqxDmForPK(String zspmDm,List<Map<String, Object>> nsqxcsList,Map<String, Object> nsrxx) throws SwordBaseCheckedException {
//        if(!GyUtils.isNull(nsrxx.get("nsqxDm"))){
//            return nsrxx;
//        }
//        final String djxh=GYCastUtils.cast2Str(nsrxx.get("djxh"));
//        String nsrlx="0";
//        String hyDm="";
//        //如果是自然人
//        if ("N".equals(NsrglUtils.isHxzgNsr(djxh))) {
//            nsrlx="1";
//        }
//        hyDm=GYCastUtils.cast2Str(nsrxx.get("hyDm"));
//        for (Map<String, Object> mrqxKvMap : nsqxcsList) {
//            if (mrqxKvMap != null && !mrqxKvMap.isEmpty()) {
//                final String zspmDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("zspmDm"));
//                final String nsrlx_ = GYCastUtils.cast2Str(mrqxKvMap.get("nsrlx"));
//                final String hyDm_ = GYCastUtils.cast2Str(mrqxKvMap.get("hyDm"));
//                //如果征收品目没有匹配上则跳出当次循环
//                if (!judegeZspmDm(zspmDm_,zspmDm)) {
//                    continue;
//                }
//                //如果纳税人类型没有匹配上则跳出当次循环
//                if(!judgeNsrlx(nsrlx_,nsrlx)){
//                    continue;
//                }
//                if (judgeHyDm(hyDm_,hyDm)) {
//                    //如果行业匹配通过，则添加到返回信息当中
//                    return mrqxKvMap;
//                }
//            }
//        }
//        return null;
//    }
//
//    /**
//     *@name    判断是否发票代开，且代开类别为增值税专票或增值税普票
//     *@description 营改增，国税代开增值税专票或者增值税普票时，税率来自配置表cs_gy_glb_zspm，不固定0.03
//     *@time    创建时间:2015年9月28日下午3:26:20
//     *@param csMap 参数
//     *@return true取固定率，false取配置表
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR> 罗兴攀
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean isDkzzs(Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        //来源标识为发票代开，征收项目为个人所得税
//        if (GYCastUtils.notNull(lybz) && "FPDK".equals(lybz)){
//            final String fpdklbDm = GYCastUtils.cast2Str(csMap.get("fpdklbDm"));//代开类别
//            if(FPdkUtils.cssl1() && GYCastUtils.notNull(fpdklbDm) && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
//                    || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", ""))){
//                return false;
//            }
//        }
//        return true;
//    }
//
//    /**
//     *@name    发票代开营改增取税率
//     *@description 规则
//     * 营改增品目
//     *  101017100 建筑服务
//     *  101017700 销售不动产
//     *
//     *1.地税开具专票和普票：(地税只能开具营改增销售不动产发票，如地税开具非营改增发票一般人取参数表，其他人税率默认0.03)
//     *  1）房产交易，（简易计税）税率5%
//     *  2）个人房屋出租（通过前台获取标识），（简易计税）税率5%
//     *  3）个人房屋出租（通过前台获取标识），个人、个体工商户税率1.5%
//     *2.国税开具专票和普票：
//     *  1）非营改增，小规模（简易计税）税率0.03
//     *  2）营改增，小规模（简易计税）税率
//     *      建筑0.03
//     *      房屋0.05
//     *2.国税开具普票
//     *  1）非营改增，一般人（一般计税）税率取税率参数表
//     *  2）营改增，一般人（一般计税）税率取税率参数表
//     *  3）建筑，报验登记一般人（一般计税）税率0.02
//     *@time    创建时间:2015年9月28日下午3:26:20
//     *@param row 计税行
//     *@param csMap 参数
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR> 罗兴攀
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void setFpdkYgzSl(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String fpdklbDm = cast2Str(csMap.get("fpdklbDm"));//代开类别
//        final String zsxmDm = cast2Str(row.getZsxmDm());//征收项目代码
//        final String zspmDm = cast2Str(row.getZspmDm());//征收品目代码
//        final String zszmDm = cast2Str(row.getZszmDm());//征收子目代码
//
//        //来源标识为发票代开,增值税专用发票和增值税普通发票，增值税，营改增(101017100 建筑服务 101017700 销售不动产  101016600  租赁服务)，且征收子目为空
//        if(!(notNull(lybz) && "FPDK".equals(lybz)
//                && notNull(fpdklbDm) && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
//                || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", ""))
//                && notNull(zsxmDm) && GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm)
//                && notNull(zspmDm) && (zspmDm.startsWith("1010171") || zspmDm.startsWith("1010177") || zspmDm.startsWith("1010166"))
//                && !zspmDm.equals("101016601") && !zspmDm.equals("101016602")
//                && isNull(zszmDm))){
//            return;
//        }
//        final Double zsl = FPdkUtils.castToDouble(row.getZsl());
//        //final String zsl = GYCastUtils.cast2Str(row.getZsl());//征收率
//
//        //重新判断取固定税率
//        final String zrrbz = cast2Str(csMap.get("zrrbz"));//自然人标识
//        final String djzclxDm = cast2Str(csMap.get("djzclxDm"));//登记注册类型
//        //101017700 销售不动产  101016600  租赁服务
//        if(zspmDm.startsWith("1010177") || zspmDm.startsWith("1010166")){
//
//            /*final String sfybjs = cast2Str(csMap.get("sfybjs"));//是否适用一般计税方法
//            if(notNull(sfybjs) && sfybjs.equals("Y")){//一般计税税率为11%
//                row.setSl("0.11");
//                row.setFdsl("0.11");
//                return;
//            }*/
//
//            //个人、个体工商户税率简易征收5%
//            if((notNull(zrrbz) && zrrbz.equals("Y"))
//                    || FpdkJsUtils.isGtgsh(zrrbz,djzclxDm)){
//                //征收率为空时
//                if(zsl.compareTo(0D) == 0){
//                    row.setSl("0.05");
//                    row.setFdsl("0.05");
//                }
//                return;
//            }
//            //纳税人
//            if(notNull(zrrbz) && zrrbz.equals("N")){//纳税人
//                final String zzsybrZg = (String) csMap.get(GYJsConstants.getkZzsybrzg());//增值税一般纳税人资格
//                //小规模（简易计税）税率5%
//                if("N".equals(zzsybrZg) && zsl.compareTo(0D) == 0){
//                    row.setSl("0.05");
//                    row.setFdsl("0.05");
//                    return;
//                }
//                //（简易计税）税率5%
//                final String jyjsbz = cast2Str(csMap.get("jyjsbz"));//简易计税标识
//                if(notNull(jyjsbz) && jyjsbz.equals("N") && zsl.compareTo(0D) == 0){//N表示简易计税， 征收率为空时
//                    row.setSl("0.05");
//                    row.setFdsl("0.05");
//                }
//            }
//            return;
//        }
//        //101017100 建筑服务
//        if(zspmDm.startsWith("1010171")){
//            //纳税人
//            if(notNull(zrrbz) && zrrbz.equals("N")){//纳税人
//                final String zzsybrZg = (String) csMap.get(GYJsConstants.getkZzsybrzg());//增值税一般纳税人资格
//                //小规模（简易计税）税率3%， 征收率为空时
//                if("N".equals(zzsybrZg) && zsl.compareTo(0D) == 0){
//                    row.setSl("0.03");
//                    row.setFdsl("0.03");
//                    return;
//                }
//                //（简易计税）税率3%， 征收率为空时
//                final String jyjsbz = cast2Str(csMap.get("jyjsbz"));//简易计税标识
//                if(notNull(jyjsbz) && jyjsbz.equals("N") && zsl.compareTo(0D) == 0){//N表示简易计税
//                    row.setSl("0.03");
//                    row.setFdsl("0.03");
//                }
//            }
//            //自然人税率为0.03，征收率为空时
//            if(notNull(zrrbz) && zrrbz.equals("Y") && zsl.compareTo(0D) == 0){//自然人
//                row.setSl("0.03");
//                row.setFdsl("0.03");
//            }
//        }
//    }
//
//    /**
//     *@name    判断是否简易计税
//     *@description 规则
//     *RdNsrzgUtils.queryAllNsrRdzgxx()，返回纳税人资格类型包含203则为简易计税，否则为一般计税
//     *@time    创建时间:2015年9月28日下午3:26:20
//     *@param djxh 登记序号
//     *@return true简易计税
//     *@throws SwordBaseCheckedException 异常
//     *<AUTHOR> 罗兴攀
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static boolean fpdkJyjsfs(String djxh) throws SwordBaseCheckedException {
//        final List<RDNsrzgxxJgbVO> zgList = RdNsrzgUtils.queryAllNsrRdzgxx(djxh);//根据登记序号 查询纳税人所有认定资格
//        if(notNull(zgList)){
//            for(RDNsrzgxxJgbVO zg :zgList){
//                final String nsrzglxDm = cast2Str(zg.getNsrzglxDm());//纳税人资格类型代码
//                if(notNull(nsrzglxDm) && nsrzglxDm.equals(NsrglRdDm01Constants.getDmRdNsrzglxJybfzsybnsr())){
//                    return true;
//                }
//            }
//        }
//        return false;
//    }
//
//    /**
//     *@name    房屋出租设置减免税额
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午06:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void setJmseFwcz(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String fpdklbDm = cast2Str(csMap.get("fpdklbDm"));//代开类别
//        final String zsxmDm = cast2Str(row.getZsxmDm());//征收项目代码
//        final String zspmDm = cast2Str(row.getZspmDm());//征收品目代码
//        final String zszmDm = cast2Str(row.getZszmDm());//征收子目代码
//
//        //房屋出租，房产税，个人所得税带出全免金额，add by zouyu 2017-08-08
//        if(notNull(lybz) && "FPDK".equals(lybz)
//                && notNull(fpdklbDm) && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
//                || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", ""))
//                && notNull(zsxmDm) && !GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm)
//                && FPdkUtils.fwczJmzsxm().indexOf(zsxmDm) > -1){
//
//            final String zrrbz = cast2Str(csMap.get("zrrbz"));//自然人标识
//            final String djzclxDm = cast2Str(csMap.get("djzclxDm"));//登记注册类型
//
//            if((notNull(zrrbz) && zrrbz.equals("Y"))
//                    || FpdkJsUtils.isGtgsh(zrrbz,djzclxDm)){
//                final String sffwcz = cast2Str(csMap.get("sffwcz"));//是否房屋出租
//                if(notNull(sffwcz) && sffwcz.equals("Y")) {
//                    final Double ynse = cast2Double(row.getYnse());
//                    final Double yjse = cast2GsNum(row.getYjse());
//                    final Double nsqnyjse = cast2GsNum(row.getNsqnyjse());//纳税期内已缴税额
//                    final Double qmJmse = SwordMathUtils.subtract(SwordMathUtils.subtract(ynse, yjse), nsqnyjse);
//                    row.setJmse(cast2Str(qmJmse));
//                }
//            }
//
//        }
//        // 房屋出租，房产税，个人所得税带出全免金额  end 2017-08-08
//
//        //来源标识为发票代开,增值税专用发票和增值税普通发票，增值税，营改增(101017100 建筑服务 101017700 销售不动产  101016600  租赁服务)，且征收子目为空
//        if(!(notNull(lybz) && "FPDK".equals(lybz)
//                && notNull(fpdklbDm) && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
//                || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", ""))
//                && notNull(zsxmDm) && GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm)
//                && notNull(zspmDm) && zspmDm.startsWith("1010166")
//                && !zspmDm.equals("101016601") && !zspmDm.equals("101016602")
//                && isNull(zszmDm))){
//            return;
//        }
//
//        final String zrrbz = cast2Str(csMap.get("zrrbz"));//自然人标识
//        final String djzclxDm = cast2Str(csMap.get("djzclxDm"));//登记注册类型
//        //101017700 销售不动产  101016600  租赁服务
//        if((notNull(zrrbz) && zrrbz.equals("Y"))
//                || FpdkJsUtils.isGtgsh(zrrbz,djzclxDm)){
//            final String sffwcz = cast2Str(csMap.get("sffwcz"));//是否房屋出租
//            //房屋出租个人、个体工商户税率1.5%   101016600  租赁服务
//            if(notNull(sffwcz) && sffwcz.equals("Y") && notNull(row.getFdsl())&& "0.05".equals(row.getFdsl())) {
//                final String jmbz = row.getJmbz();//减免标志
//                final Double ynse = cast2Double(row.getYnse());
//                final Double yjse = cast2GsNum(row.getYjse());
//                final Double nsqnyjse = cast2GsNum(row.getNsqnyjse());//纳税期内已缴税额
//                final Double qmJmse = SwordMathUtils.subtract(SwordMathUtils.subtract(ynse, yjse), nsqnyjse);
//                //免征、未达起征点的情况
//                if (GYJsConstants.getJmbzMz().equals(jmbz) || GYJsConstants.getJmbzWdqzd().equals(jmbz)) {
//                    row.setJmse(cast2Str(qmJmse));
//                }else{
//                    final String ksyskuuid = cast2Str(row.getKsyskuuid());//可使用税款UUID
//                    final String gldksquuid = cast2Str(row.getGldksquuid());//关联代开申请UUID
//                    //抵扣的税款不处理   加收的未达起征点数据不处理
//                    if(notNull(ksyskuuid) || notNull(gldksquuid)){
//                        return;
//                    }
//                    //减免金额=计税依据*3.5%
//                    final Double sjyj = cast2Double(row.getJsyj());
//                    final Double fwJmse = SwordMathUtils.round(SwordMathUtils.multiple(sjyj, 0.035), 2);
//                    row.setJmse(cast2Str(fwJmse));
//                    //房屋减免金额大于能够减免的金额，填能够减免的金额
//                    if(fwJmse.compareTo(qmJmse) > 0){
//                        row.setJmse(cast2Str(qmJmse));
//                    }
//                }
//            }
//        }
//    }
//
//    /**
//     *@name    租金分摊设置减免税额
//     *@description 相关说明
//     *@time    创建时间:2014-8-24下午06:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void setJmseZjft(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String fpdklbDm = cast2Str(csMap.get("fpdklbDm"));//代开类别
//        final String zsxmDm = cast2Str(row.getZsxmDm());//征收项目代码
//        final String zspmDm = cast2Str(row.getZspmDm());//征收品目代码
//
//        //来源标识为发票代开,增值税专用发票和增值税普通发票，增值税，营改增(101017100 建筑服务 101017700 销售不动产  101016600  租赁服务)，且征收子目为空
//        if(!(notNull(lybz) && "FPDK".equals(lybz)
//                && notNull(fpdklbDm) && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
//                || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", ""))
//                && notNull(zsxmDm) && GYDm01Constants.getDmGyZsxmZzs().equals(zsxmDm)
//                && notNull(zspmDm) && zspmDm.startsWith("1010166")
//                && !zspmDm.equals("101016601") && !zspmDm.equals("101016602"))){
//            return;
//        }
//
//        final String zrrbz = cast2Str(csMap.get("zrrbz"));//自然人标识
//        final String djzclxDm = cast2Str(csMap.get("djzclxDm"));//登记注册类型
//        //101017700 销售不动产  101016600  租赁服务
//        if((notNull(zrrbz) && zrrbz.equals("Y"))
//                || FpdkJsUtils.isGtgsh(zrrbz,djzclxDm)){
//
//            final String zjsrsfft = cast2Str(csMap.get("zjsrsfft"));//租金收入是否分摊
//            final String zlyfq = cast2Str(csMap.get("zlyfq"));//租赁月份起
//            final String zlyfz = cast2Str(csMap.get("zlyfz"));//租赁月份止
//            //租金分摊
//            if(notNull(zjsrsfft) && zjsrsfft.equals("Y") && notNull(zlyfq) && notNull(zlyfz)) {
//                final String ksyskuuid = cast2Str(row.getKsyskuuid());//可使用税款UUID
//                final String gldksquuid = cast2Str(row.getGldksquuid());//关联代开申请UUID
//                //抵扣的税款不处理   加收的未达起征点数据不处理
//                if(notNull(ksyskuuid) || notNull(gldksquuid)){
//                    return;
//                }
//                //final String jmbz = row.getJmbz();//减免标志
//                final Double ynse = cast2Double(row.getYnse());
//                final Double yjse = cast2GsNum(row.getYjse());
//                final Double nsqnyjse = cast2GsNum(row.getNsqnyjse());//纳税期内已缴税额
//                final Double qmJmse = SwordMathUtils.subtract(SwordMathUtils.subtract(ynse, yjse), nsqnyjse);//全免金额
//                //租金分摊减免，不区分是否免征标识，
//                Double zjJmje = FPdkUtils.castToDouble(csMap.get("zjJmse"));//租金减免金额
//                if(zjJmje.compareTo(0D) > 0){
//                    zjJmje = SwordMathUtils.round(zjJmje, 2);//保留两位，这样处理可能会使减免金额合计大于原金额
//                    Double fwJmse = 0D;//租金减免税额
//                    final String sffwcz = cast2Str(csMap.get("sffwcz"));//是否房屋出租
//                    //房屋出租个人、个体工商户税率1.5%   101016600  租赁服务
//                    if(notNull(sffwcz) && sffwcz.equals("Y") && notNull(row.getFdsl())&& "0.05".equals(row.getFdsl()) ) {
//                        //减免金额=计税依据*3.5%
//                        final Double jsyj = cast2Double(row.getJsyj());//计税依据
//                        zjJmje = zjJmje.compareTo(jsyj)>0?jsyj:zjJmje;//防止减免金额大于计税金额
//                        final Double syJsyj = SwordMathUtils.subtract(jsyj, zjJmje);//剩余计税依据
//                        fwJmse = SwordMathUtils.round(SwordMathUtils.multiple(syJsyj, 0.035), 2);
//                    }
//                    final Double sfl = cast2Double(row.getFdsl());//税率
//                    final Double zjJmse = SwordMathUtils.round(SwordMathUtils.multiple(zjJmje, sfl), 2);//减免税额
//                    final Double jmseHj = SwordMathUtils.round(SwordMathUtils.add(fwJmse, zjJmse), 2);//减免税额合计
//                    if(jmseHj.compareTo(0D) > 0 && jmseHj.compareTo(qmJmse) <= 0){
//                        row.setJmje(GYCastUtils.cast2Str(zjJmje));//设置减免金额
//                        row.setJmse(cast2Str(jmseHj));
//                    }
//                }
//            }
//        }
//    }
//
//    /**
//     *@name    根据税务机关代码查询工作时间区间
//     *@description 相关说明
//     *@time    创建时间:2016-10-12上午08:50:52
//     *@param swjgDm 税务机关代码
//     *@param xtgnDm 系统功能代码
//     *@return 工作时间区间字符串
//     *@throws SwordBaseCheckedException
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static String getGzsjqjBySwjgDm(String swjgDm, String xtgnDm) throws SwordBaseCheckedException {
//        String gzsj = "08:30:00-17:30:00";//默认工作时间区间
//        String csz = "";
//
//        //从CS_GY_GZSJPZ取参数值
//        final List<Map<String, Object>> gzsjpzList = new ArrayList<Map<String, Object>>((Collection) SwordCacheUtils.getAllDataFromKV("CS_GY_GZSJPZ"));
//        final List<Map<String, Object>> cszList = new ArrayList<Map<String, Object>>();
//        if (GYCastUtils.notNull(gzsjpzList)) {
//            //取系统功能代码对应的参数值
//            for (Map<String, Object> gzsjpzMap : gzsjpzList) {
//                if (xtgnDm.equals(gzsjpzMap.get("XTGN_DM"))) {
//                    cszList.add(gzsjpzMap);
//                }
//            }
//        }
//        if (GYCastUtils.notNull(cszList)) {
//            //获取本上级税务机关的参数值
//            final List<String> bjsjSwjgList = GYJsUtils.findBjsjSwjgDmInCache(swjgDm);
//            for (String bjsjSwjgDm : bjsjSwjgList) {
//                for (Map<String, Object> cszMap : cszList) {
//                    if (cszMap.get("SWJG_DM").equals(bjsjSwjgDm)) {
//                        csz = cszMap.get("CSZ_1") + "";
//                        if (csz.matches("\\d{2}:\\d{2}:\\d{2}-\\d{2}:\\d{2}:\\d{2}") && csz.split("-")[1].compareTo(csz.split("-")[0]) >= 0) {
//                            gzsj = csz;
//                            break;
//                        } else {
//                            csz = "";
//                        }
//                    }
//                }
//                if (!"".equals(csz)) {
//                    break;
//                }
//            }
//        }
//
//        //如果CS_GY_GZSJPZ没取到合法的值，那么读取系统参数A0000001061001444
//        if ("".equals(csz)) {
//            final Map<String, Object> csMap = HxzgQxUtils.getXtcs(swjgDm, "A0000001061001444");
//            if (GYCastUtils.notNull(csMap)) {
//                csz = csMap.get("csz") + "";
//                //判断是否为合法的时间区间
//                if (csz.matches("\\d{2}:\\d{2}:\\d{2}-\\d{2}:\\d{2}:\\d{2}") && csz.split("-")[1].compareTo(csz.split("-")[0]) >= 0) {
//                    gzsj = csz;
//                }
//            }
//        }
//
//        //如果是法定假日，时间返回 00:00:00-00:00:00
//        final Calendar curDate = SwordDateUtils.getSystemCurrentTime();
//        final String date = SwordDateUtils.toDateStr(curDate);
//        final String yyyf = date.replace("-", "").substring(0, 6);
//        final SQLParam sql = new SQLParam();
//        sql.addParam(swjgDm);
//        sql.addParam(date);
//        sql.addParam(date);
//        sql.addParam(yyyf);
//        final List<Map<String, Object>> jjrList = SwordPersistenceUtils.getPersistenceService().queryMapListByKey("common_common000150_queryjjrxx", sql);
//        if (GYCastUtils.notNull(jjrList)) {
//            gzsj = "00:00:00-00:00:00";
//        }
//
//        return gzsj;
//    }
//
//    /**
//     *@name    检查是否是工作时间，根据系统参数 A0000001061001444配置。
//     *@description 相关说明
//     *@time    创建时间:2016-10-12上午10:07:39
//     *@param gnDm 功能代码
//     *@return String[] 1:是否工作时间范围内Y/N, 2：工作时间范围
//     *@throws SwordBaseCheckedException 异常信息
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String[] checkGzsj(String gnDm) throws SwordBaseCheckedException {
//        String swjgDm = ZnsbSessionUtils.getswjgdm();
//        swjgDm = GyUtils.isNull(swjgDm) ? "00000000000" : swjgDm;
//        final String gzsj = getGzsjqjBySwjgDm(swjgDm, gnDm);
//        final String[] gzsjArry = gzsj.split("-");//不会为空
//        String startTime = gzsjArry[0];
//        String endTime = gzsjArry[1];
//        final Date currentTime = SwordDateUtils.getSystemCurrentTime().getTime();
//        final String nowDateString = SwordDateUtils.toDateStrByFormatIndex(currentTime, 3);
//        startTime = nowDateString + " " + startTime;
//        endTime = nowDateString + " " + endTime;
//        final Date startDate = SwordDateUtils.parseDate(startTime, 0);//带时分秒的日期
//        final Date endDate = SwordDateUtils.parseDate(endTime, 0);//带时分秒的日期
//        String isGzsj = "Y";
//        if (currentTime.before(startDate) || currentTime.after(endDate)) {
//            isGzsj = "N";
//        }
//        final String[] gzsjRetArray = new String[2];
//        gzsjRetArray[0] = isGzsj;
//        gzsjRetArray[1] = gzsj;
//        return gzsjRetArray;
//    }
//
//    /**
//     *@name    获取品目关联表有效期校验未通过品目信息
//     *@description 相关说明
//     *@time    创建时间:2018-03-02 02:25:04
//     *@param req req
//     *@return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
//     *@throws SwordBaseCheckedException 框架异常信息
//     *<AUTHOR> 贾蜀川
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static List<Map<String, String>> checkNotPassZspm(Map<String, Object> req) throws SwordBaseCheckedException {
//        final List<Map<String, String>> rsList = new ArrayList<Map<String, String>>();
//        final String swjgDm = (String) req.get("swjgDm");//税务机关代码
//        final String zsxmDm = (String) req.get("zsxmDm");//征收项目代码
//        final String zspmDm = (String) req.get("zspmDm");//征收品目代码
//        final Date skssqq = GYCastUtils.cast2Date(req.get(GYJsConstants.getcSkssqq()));//所属期起
//        final Date skssqz = GYCastUtils.cast2Date(req.get(GYJsConstants.getcSkssqz()));//所属期止
//        //获取本级上级税务机关
//        final List<String> bjsjSwjgList = findBjsjSwjgDmInCache(swjgDm);
//        //逐级上查，找到某级税务机关 有记录，则返回该级数据
//        List<Map<String, Object>> zspmList = new ArrayList<Map<String, Object>>();
//        for (String bjsjSwjgDm : bjsjSwjgList) {
//            List<Map<String, Object>> zspmKvList = findListInCacheBySwjg(bjsjSwjgDm, GYJsConstants.gettCsZspm());//根据税务机关过滤品目参数表
//            zspmKvList = findListInMapList("zsxmDm", zsxmDm, zspmKvList);//根据征收项目再次过滤
//            zspmKvList = findListInMapList("zspmDm", zspmDm, zspmKvList);//根据征收品目再次过滤
//            if (zspmKvList != null && zspmKvList.size() > 0) {
//                //注意，缓存里面只过滤了 yxbz有效标志，没过滤选用标志
//                //只添加没有重复品目代码的到之前集合：即保证代码优先用下级的
//                final List<Map<String, Object>> fiterZspmKvList = new ArrayList<Map<String, Object>>();
//                for (Map<String, Object> zspmKvMap : zspmKvList) {
//                    final Date yxqq = (Date) zspmKvMap.get("yxqq");
//                    final Date yxqz = (Date) zspmKvMap.get("yxqz");
//                    //判断有效期
//                    boolean yxq = false;
//                    if (GYCastUtils.compareYxqx(skssqq, skssqz, yxqq, yxqz)){
//                        yxq = true;
//                    }
//                    if (!yxq) {
//                        fiterZspmKvList.add(zspmKvMap);
//                    }
//                }
//                zspmList.addAll(fiterZspmKvList);//把本级税务机关查得的合法结果集添加到list
//                final Map<String, Object> zspmFirstMap = zspmKvList.get(0);
//                final String dgbz = (String) zspmFirstMap.get("dgbz");
//                //如果递归标志为N，不再上溯查找。否则，上溯查找并叠加结果集
//                if (dgbz == null || "N".equals(dgbz)) {
//                    break;
//                }
//            }
//        }
//        final List<Map<String, Object>> tList = new ArrayList<Map<String, Object>>();
//        //最后校验选用标志
//        //之所以要分两次过滤，是因为第一次要根据本级是否有记录来判断是否逐级上查。兼容本级全设选用标志=N而不逐级上查的情况
//        for (Map<String, Object> zspmMap : zspmList) {
//            if (zspmMap != null && !zspmMap.isEmpty()) {
//                final String yxbz = (String) zspmMap.get("yxbz");
//                final String xybz = (String) zspmMap.get("xybz");
//                if ("Y".equals(yxbz) && "Y".equals(xybz)) {
//                    tList.add(zspmMap);
//                }
//            }
//        }
//        zspmList = tList;
//        if (GYCastUtils.notNull(zspmList)) {
//            for (Map<String, Object> map : zspmList) {
//                final Map<String, String> newMap = new HashMap<String, String>();
//                newMap.put("zspmDm", (String) map.get("zspmDm"));
//                newMap.put("swjgDm", (String) map.get("swjgDm"));
//                newMap.put("zsxmDm", (String) map.get("zsxmDm"));
//                newMap.put("skssqq", (String) req.get("skssqq"));
//                newMap.put("skssqz", (String) req.get("skssqz"));
//                newMap.put("yxqq", SwordDateUtils.toDateStr(SwordDateUtils.convSqlTimestampToUtilCalendar((Timestamp)map.get("yxqq"))));
//                newMap.put("yxqz", SwordDateUtils.toDateStr(SwordDateUtils.convSqlTimestampToUtilCalendar((Timestamp)map.get("yxqz"))));
//                final Map<String, Object> zsxmmcMap = (Map<String, Object>) SwordCacheUtils.getFromKV("DM_GY_ZSXM", new Object[] { map.get("zsxmDm") });
//                final Map<String, Object> zspmmcMap = (Map<String, Object>) SwordCacheUtils.getFromKV("DM_GY_ZSPM", new Object[] { map.get("zspmDm") });
//                final Map<String,Object> swjgmcMap = (Map<String, Object>) SwordCacheUtils.getFromKV("DM_GY_SWJG",  new Object[] { map.get("swjgDm") });
//                if (notNull(zsxmmcMap)) {
//                    newMap.put("zsxmmc", (String) zsxmmcMap.get("ZSXMMC"));
//                }
//                if (notNull(zspmmcMap)) {
//                    newMap.put("zspmmc", (String) zspmmcMap.get("ZSPMMC"));
//                }
//                if (notNull(swjgmcMap)) {
//                    newMap.put("swjgmc", (String) swjgmcMap.get("SWJGMC"));
//                }
//                rsList.add(newMap);
//            }
//        }
//        return rsList;
//    }
//    /**
//     *
//     *@name    获取个人所得税按照所得率申报所得额扣除金额
//     *@description 获取个人所得税按照所得率申报所得额扣除金额
//     *@time    创建时间:2018-9-22下午01:53:54
//     *@param rq 传入日期
//     *@return  kcje
//     *@throws SwordBaseCheckedException ex
//     *<AUTHOR> yangdalin
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    @SuppressWarnings("unchecked")
//    public static String getKcje(Date rq) throws SwordBaseCheckedException{
//        final String kcje="0.00";
//        //从CS_GY_GRSDSSDLKCJEPZB取参数值
//        final List<Map<String, Object>> pzList = new ArrayList<Map<String, Object>>((Collection) SwordCacheUtils.getAllDataFromKV("CS_GY_GRSDSSDLKCJEPZB"));
//
//        if(!GyUtils.isNull(pzList)){
//            for(Map<String, Object> map:pzList){
//                final Date yxqq = (Date) map.get("YXQQ");
//                final Date yxqz = (Date) map.get("YXQZ");
//                if (!(rq.before(yxqq) || rq.after(yxqz))) {
//                    return GYCastUtils.cast2Str(map.get("KCJE"));
//                }
//            }
//        }
//        return kcje;
//    }
//    /**
//     *
//     *@name    計算屬期內的扣除金額
//     *@description 計算屬期內的扣除金額
//     *@time    创建时间:2018-9-25下午01:33:52
//     *@param skssqq 稅款所屬期起
//     *@param skssqz 稅款所屬期止
//     *@return 扣除金額
//     *@throws SwordBaseCheckedException ex
//     *<AUTHOR> yangdalin
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static double getKcje(Date skssqq,Date skssqz) throws SwordBaseCheckedException{
//        double kpje=0d;
//        if(GyUtils.isNull(skssqq)||GyUtils.isNull(skssqz)){
//            Date date=GyUtils.isNull(skssqq)?skssqz:skssqq;
//            date=GyUtils.isNull(date)?SwordDateUtils.getSystemCurrentTime().getTime():date;
//            return GYSbCxsUtils.cDouble(getKcje(date));
//        }
//        final Calendar calStartDate = SwordDateUtils.convUtilDateToUtilCalendar(skssqq);
//        while (calStartDate.getTime().before(skssqz)) {
//            kpje=SwordMathUtils.add(kpje, GYSbCxsUtils.cDouble(getKcje(calStartDate.getTime())));
//            calStartDate.add(Calendar.MONTH, 1);//进行当前日期月份加1
//        }
//        return kpje;
//    }
//
//    /**
//     *@name 查询服务名称
//     *@description 相关说明
//     *@time 创建时间：
//     *@param lcswsxDm 流程税务事项代码
//     *@return 受理税务事项信息
//     *@throws SwordBaseCheckedException
//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String getNextStepFwmcByLcswsxDm(String lcswsxDm) throws SwordBaseCheckedException {
//        String fwmc = "";
//        final SQLParam param = new SQLParam();
//        param.addParam(lcswsxDm);
//        final Map<String, Object> fwmcMap = (Map<String, Object>) SwordPersistenceUtils.getPersistenceService().queryMapByKey("common_common000065_tysl_getnextstepfwmcbylcswsxdm", param);
//        if (!GyUtils.isNull(fwmcMap)) {
//            fwmc = (String) fwmcMap.get("fwmc");
//        }
//        if (GyUtils.isNull(fwmc)) {
//            fwmc = WfClient.getWfClient().getFirstActServiceName(lcswsxDm); //获取流程图配置第一步的路径
//        }
//        if (GyUtils.isNull(fwmc)) {
//            throw new SwordBizCheckedException("CS_GY_TYSLDYFWPZ表中没有流程税务事项代码为" + lcswsxDm + "的记录，或者流程图第一个节点未正确配置表单路径");
//        }
//        return fwmc;
//    }
//
//    /**
//     *@name    普惠政策设置减免税额
//     *@description 普惠政策设置减免税额
//     *@time    创建时间:2014-8-24下午06:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void setJmsePh(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String fpdklbDm = cast2Str(csMap.get("fpdklbDm"));//代开类别
//        final String yqwrdybnsrbz = GYCastUtils.cast2Str(csMap.get("yqwrdybnsrbz"));//是否逾期未认定增值税一般纳税人
//        //来源标识为发票代开,增值税专用发票和增值税普通发票
//        if(!(notNull(lybz) && "FPDK".equals(lybz)
//                && notNull(fpdklbDm) && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
//                || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", "")
//                || fpdklbDm.equals(FpglFPZdyConstants.getDmFpDkfplbTyjdfp()))) || "Y".equals(yqwrdybnsrbz)){
//            return;
//        }
//        final Double ynse = cast2GsNum(row.getYnse());//应纳税额
//        final Double jmse = cast2GsNum(row.getJmse());//减免税额
//        final Double nsqnyjse = cast2GsNum(row.getNsqnyjse());//纳税期内已缴
//        final String zsxmDm = cast2Str(row.getZsxmDm());//征收项目代码
//        row.setPhjzbl("");
//        row.setPhjmse("");
//        row.setPhjmxzDm("");
//        Double phjmse = 0D;
//        final String nowdate = SwordDateUtils.toDateStrByFormatIndex(SwordDateUtils.getSystemCurrentTime(), 3);
//        final Map<String, Object> jmfdMap = GYPhjmUtil.getPhjmByZsxm(zsxmDm, nowdate, nowdate, null, null);
//        final Double jmfd = FPdkUtils.castToDouble(jmfdMap.get("jmfd"));//减免幅度
//        final String ssjmxzDm = cast2Str(jmfdMap.get("ssjmxzDm"));//普惠减免性质
//        if(jmfd.compareTo(0D) > 0){
//            row.setPhjzbl(cast2Str(jmfd));
//            //增值税小规模纳税人减征额=（本期应纳税额-本期减免税额）×增值税小规模纳税人享受减征比例
//            final String qdDm=cast2Str(csMap.get("qdDm"));
//            if(notNull(qdDm) && "hzdkfp".equals(qdDm)){//新汇总代开保存四位小数
//                phjmse = SwordMathUtils.round(SwordMathUtils.multiple(SwordMathUtils.subtract(ynse, jmse), jmfd), 4);
//            }else{
//                phjmse = SwordMathUtils.round(SwordMathUtils.multiple(SwordMathUtils.subtract(ynse, jmse), jmfd), 2);
//            }
//
//            final Double kjmse = SwordMathUtils.subtract(ynse, jmse, nsqnyjse);//可减免税额
//            if(phjmse.compareTo(0D) > 0 && kjmse.compareTo(phjmse) >= 0){
//                row.setPhjmse(cast2Str(phjmse));
//                row.setPhjmxzDm(ssjmxzDm);
//            }
//        }
//    }
//    /**
//     *@name    行计算：计算减免性质
//     *@description 相关说明
//     *@time    创建时间:2021-3-11下午06:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return 行GYJsSfzVO

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static GYJsSfzVO jsRowJmxz(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String fpdklbDm = cast2Str(csMap.get("fpdklbDm"));//代开类别
//        final String zsxmDm = FPdkUtils.castToStr(row.getZsxmDm());//征收项目代码
//        final String zspmDm = FPdkUtils.castToStr(row.getZspmDm());//征收品目代码
//        //来源标识为发票代开,增值税专用发票和增值税普通发票
//        if(!(notNull(lybz) && "FPDK".equals(lybz) && notNull(fpdklbDm)
//                && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
//                || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", "")))){
//            return row;
//        }
//        row.setSsjmxzDm("");//清空
//        row.setSsjmxzmc("");//清空
//        String ssjmxzDm="";
////        if(FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", "")&& zsxmDm.equals(GYDm01Constants.getDmGyZsxmZzs())){
////            ssjmxzDm=FpdkJsUtils01.setJmxzXgmmzZzs(row, csMap);//小规模免征减免性质设置
////        } else
//        if(zsxmDm.equals(GYDm01Constants.getDmGyZsxmFcs())){
//            row.setJmse(null);
//            ssjmxzDm=setJmxzFcs(row, csMap);//房产税减免性质设置
//        } else if(zsxmDm.equals(GYDm01Constants.getDmGyZsxmJyffj())||zsxmDm.equals(GYDm01Constants.getDmGyZsxmDfjyfj())
//                ||zsxmDm.equals(GYDm01Constants.getDmGyZsxmSljszxsr())){
//            ssjmxzDm=setJmxzFjs(row, csMap);//附加税减免性质设置
//        }else if(GYDm01Constants.getDmGyZsxmYhs().equals(zsxmDm)&&GYDm01Constants.getDmGyZspmZlht().equals(zspmDm)){
//            ssjmxzDm=setJmxzYhs(row, csMap);//印花税减免性质设置
//        }
//        if(!GyUtils.isNull(ssjmxzDm)){
//            //组装减免性质代码
//            final String djxh = FPdkUtils.castToStr(csMap.get("djxh"));//登记序号
//            final String skssqq=FPdkUtils.castToStr(row.getSkssqq());//税款所属期起
//            final String skssqz=FPdkUtils.castToStr(row.getSkssqz());//税款所属期止
//            final String swjgDm=GyUtils.isNull(ZnsbSessionUtils.getgwssswjg())?ZnsbSessionUtils.getswjgdm():ZnsbSessionUtils.getgwssswjg();
//            final List<Map<String, Object>> jmxmdmList = FpdkJsUtils.getSsjmxxDmListByZsxmDmAndSkssq(swjgDm,
//                    djxh, zsxmDm, zspmDm, skssqq, skssqz, "", null);
//            if(!GyUtils.isNull(jmxmdmList)) {
//                for (Map<String, Object> map : jmxmdmList) {
//                    final String ssjmxzDm_=FPdkUtils.castToStr(map.get("code"));
//                    final String ssjmxzmc_=FPdkUtils.castToStr(map.get("caption"));
//                    if(ssjmxzDm.equals(ssjmxzDm_)&&!GyUtils.isNull(ssjmxzmc_)){
//                        row.setSsjmxzDm(ssjmxzDm_);
//                        row.setSsjmxzmc(ssjmxzmc_);
//                        break;
//                    }
//                }
//            }
//        }
//        return row;
//    }
//    /**
//     *@name    房产税政策设置减免性质
//     *@description 房产税政策设置减免性质
//     *@time    创建时间:2021-1-24下午02:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return 减免性质

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String setJmxzFcs(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        if(FPdkUtils.gbjmxzdcbz()){//关闭减免性质带出标志为true时，返回
//            return "";
//        }
//        final String djxh = FPdkUtils.castToStr(csMap.get("djxh"));//登记序号
//        final String djzclxDm = FPdkUtils.castToStr(csMap.get("djzclxDm"));//登记注册类型
//        final String fkfdjzclxDm = FPdkUtils.castToStr(csMap.get("fkfdjzclxDm"));//付款方登记注册类型
//        final String fkfdjxh = FPdkUtils.castToStr(csMap.get("fkfdjxh"));//付款方归类户类型
//        final String sffwcz = FPdkUtils.castToStr(csMap.get("sffwcz"));//是否房屋出租
//        final String zsxmDm = FPdkUtils.castToStr(row.getZsxmDm());//征收项目代码
//        final String zlyfq = cast2Str(csMap.get("zlyfq"));//租赁月份起
//        final String zlyfz = cast2Str(csMap.get("zlyfz"));//租赁月份止
//        String ssjmxzDm="";
//        if(notNull(zlyfq)&&notNull(zlyfz)&&notNull(sffwcz)&&zsxmDm.equals(GYDm01Constants.getDmGyZsxmFcs())&& FP01Utils.checkDjzclxGy(djzclxDm,fkfdjzclxDm)){
//            final String fkfglhlx = FP01Utils.getGlhlx(fkfdjxh, djxh,ssjmxzDm,zlyfq,zlyfz);
//            if((fkfdjzclxDm.startsWith("1") || fkfdjzclxDm.startsWith("2") || fkfdjzclxDm.startsWith("3"))
//                    &&"Y".equals(sffwcz)&&"Y".equals(fkfglhlx)){
//                // 承租方为企业（登记注册类型为“100内资企业”“200港澳台商投资企业”“300外商投资企业” 下所有细类，下同），
//                // 且“收款方基本信息”模块中“是否出租住房”选为“是”：
//                // 自动带出减免性质代码08011707
//                ssjmxzDm="0008011707";
//            }else if((fkfdjzclxDm.startsWith("41") || fkfdjzclxDm.startsWith("43"))
//                    &&"Y".equals(sffwcz)) {
//                // 承租方为个人（登记注册类型为“410个体工商户”、“430个人”下所有细类，下同），且“收款方基本信息”模块中“是否出租住房”选为“是”：
//                // 自动带出减免性质代码08011708
//                ssjmxzDm="0008011708";
//            }
//        }
//        return ssjmxzDm;
//    }
//    /**
//     *@name    房产税政策设置减免税额
//     *@description 房产税政策设置减免税额
//     *@time    创建时间:2021-1-24下午02:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static void setJmseFcs(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String lybz = GYCastUtils.cast2Str(csMap.get("lybz"));//来源标识
//        final String fpdklbDm = cast2Str(csMap.get("fpdklbDm"));//代开类别
//        final String djzclxDm = cast2Str(csMap.get("djzclxDm"));//登记注册类型
//        final String fkfdjzclxDm = cast2Str(csMap.get("fkfdjzclxDm"));//付款方登记注册类型
//        final String sffwcz = cast2Str(csMap.get("sffwcz"));//是否房屋出租
//        final String zsxmDm = cast2Str(row.getZsxmDm());//征收项目代码
//        final String ssjmxzDm = cast2Str(row.getSsjmxzDm());//税收减免性质代码
//
//        //来源标识为发票代开,增值税专用发票和增值税普通发票
//        if(!(notNull(lybz) && "FPDK".equals(lybz)&& notNull(sffwcz)
//                && notNull(fpdklbDm) && (FpdkJsUtils.checkSfzzsZp(fpdklbDm, "", "")
//                || FpdkJsUtils.checkSfzzsPp(fpdklbDm, "", ""))
//                &&zsxmDm.equals(GYDm01Constants.getDmGyZsxmFcs())&& FP01Utils.checkDjzclxGy(djzclxDm,fkfdjzclxDm))){
//            return;
//        }
//        final String zlyfq = cast2Str(csMap.get("zlyfq"));//租赁月份起
//        final String zlyfz = cast2Str(csMap.get("zlyfz"));//租赁月份止
//        //承租方为特定标识企业或4个减免性质自动带出房产税减免税额
//        if(!GyUtils.isNull(zlyfq)&&!GyUtils.isNull(zlyfz)&&("0008011707".equals(ssjmxzDm)
//                ||"0008011708".equals(ssjmxzDm) ||"0008011709".equals(ssjmxzDm)||"0008011710".equals(ssjmxzDm))){
//            final int zlyfs = GYCastUtils.jsYfkd(SwordDateUtils.StringToDate(zlyfq), SwordDateUtils.StringToDate(zlyfz));//租赁月份间隔数
//            int jmyfs=0;//减免月份数
//            if("0008011708".equals(ssjmxzDm)){
//                jmyfs=zlyfs;//减免性质为08011708时，减免时间为整个租赁期。
//            }else{
//                final Map<String, Object> JmyfMap=FP01Utils.getJmyfsFcs(ssjmxzDm,zlyfq,zlyfz);//获取租赁期内减免性质有效期
//                jmyfs =Integer.parseInt(cast2Str(JmyfMap.get("jmyfs")));//租赁期内减免月份数
//            }
//            Double fcsJmse = 0D;//房产税减免税额
//            final Double jsyj= cast2GsNum(row.getJsyj());//计税依据
//            final Double fdsl= cast2GsNum(row.getFdsl());//法定税率
//            final Double ynse = cast2GsNum(row.getYnse());//应纳税额
//            final Double yjse = cast2GsNum(row.getYjse());
//            final Double nsqnyjse = cast2GsNum(row.getNsqnyjse());//纳税期内已缴
//            //add by 房产税减免税额=计税依据*（法定税率-减免税率）*减免月份数/租赁月份数 0429需求
//            fcsJmse=SwordMathUtils.round(SwordMathUtils.divide(SwordMathUtils.multiple(
//                    FpdkJsUtils01.getjmseBySsjmxz(ssjmxzDm,jsyj, ynse,fdsl),jmyfs),zlyfs),2);
//            final Double qmJmse = SwordMathUtils.subtract(SwordMathUtils.subtract(ynse, yjse), nsqnyjse);//全免税额
//            if(fcsJmse.compareTo(0D) > 0 && qmJmse.compareTo(fcsJmse) >= 0){
//                row.setJmse(cast2Str(fcsJmse));
//            }
//        }
//    }
//    /**
//     *@name    附加税设置减免性质
//     *@description 附加税设置减免性质
//     *@time    创建时间:2021-6-15下午02:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return 减免性质

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String setJmxzFjs(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        String ssjmxzDm="";
//        //判断有品目才触发后面的计税
//        if (notNull(row.getZspmDm()) && notNull(row.getYnse())) {
//            final String zsxmDm = FPdkUtils.castToStr(row.getZsxmDm());//征收项目代码
//            final String jmbz = row.getJmbz();//减免标志
//            final Double ynse = cast2Double(row.getYnse());
//            final Double yjse = cast2GsNum(row.getYjse());
//            final Double nsqnyjse = cast2GsNum(row.getNsqnyjse());//纳税期内已缴税额
//            final Double qmJmse = SwordMathUtils.subtract(SwordMathUtils.subtract(ynse, yjse), nsqnyjse);
//            //免征、未达起征点的情况
//            if ((GYJsConstants.getJmbzMz().equals(jmbz) || GYJsConstants.getJmbzWdqzd().equals(jmbz))
//                    && qmJmse.compareTo(0D) > 0) {
//                if(zsxmDm.equals(GYDm01Constants.getDmGyZsxmJyffj())){
//                    ssjmxzDm="0061042802";//教育附加减免性质
//                }else if(zsxmDm.equals(GYDm01Constants.getDmGyZsxmDfjyfj())){
//                    ssjmxzDm="0099042802";//地方教育附加减免性质
//                }else if(zsxmDm.equals(GYDm01Constants.getDmGyZsxmSljszxsr())){
//                    ssjmxzDm="0099129901";//水利建设专项收入减免性质
//                }
//            }
//        }
//        return ssjmxzDm;
//    }
//    /**
//     *@name    印花税政策设置减免性质
//     *@description 印花税政策设置减免性质
//     *@time    创建时间:2023-6-12下午02:01:57
//     *@param row 行GYJsSfzVO
//     *@param csMap 参数map
//     *@return 减免性质

//     *<AUTHOR>
//     *@history 修订历史（历次修订内容、修订人、修订时间等）
//     */
//    public static String setJmxzYhs(GYJsSfzVO row, Map<String, Object> csMap) throws SwordBaseCheckedException {
//        final String djzclxDm = FPdkUtils.castToStr(csMap.get("djzclxDm"));//登记注册类型
//        final String fkfdjzclxDm = FPdkUtils.castToStr(csMap.get("fkfdjzclxDm"));//付款方登记注册类型
//        final String sffwcz = FPdkUtils.castToStr(csMap.get("sffwcz"));//是否房屋出租
//        final String zspmDm = FPdkUtils.castToStr(row.getZspmDm());//征收品目代码
//        final String zrrbz = FPdkUtils.castToStr(csMap.get("zrrbz"));//自然人标志
//        String ssjmxzDm="";
//        if(GYDm01Constants.getDmGyZspmZlht().equals(zspmDm) && "Y".equals(sffwcz) &&
//                (FpdkJsUtils.isGtgshZrr(zrrbz,djzclxDm)||FpdkJsUtils.isGtgshZrr("",fkfdjzclxDm))){
//            ssjmxzDm="0009011707";//免征个人出租承租住房签订的租赁合同印花税
//        }
//        return ssjmxzDm;
//    }
}