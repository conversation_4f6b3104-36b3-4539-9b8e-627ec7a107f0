package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车辆生产企业销售明细表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdclscqyxsmxbywbw", propOrder = { "zzssyyybnsrJdclscqyxsmxb" })
@Getter
@Setter
public class ZzssyyybnsrJdclscqyxsmxbywbw extends TaxDoc {
    /**
     * 《机动车辆生产企业销售明细表》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_jdclscqyxsmxb", required = true)
    @JSONField(name = "zzssyyybnsr_jdclscqyxsmxb")
    protected ZzssyyybnsrJdclscqyxsmxb zzssyyybnsrJdclscqyxsmxb;
}