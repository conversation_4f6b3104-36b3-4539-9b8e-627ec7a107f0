package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "taxDoc")
@XmlSeeAlso({ Zzsjmssbmxbywbw.class, ZzssyyybnsrHgwspzdklsjcjbywbw.class, Zzssyyybnsr11Hqysqyfzjgcddywbw.class, ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw.class, ZzssyyybnsrYgzsffxcsmxbywbw.class, ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw.class, ZzssyyybnsrTljsjjnssbbywbw.class, ZzssyyybnsrBfcpxstjbywbw.class, Zzssyyybnsr03Ysfwkcxmmxywbw.class, HXZGSB00041Request.class, Fjssbywbw.class, ZzsybnsrsbGdzcjxsedkqkbbw.class, ZzsybnsrsbNcphdkczzsjxsejsbbw.class, ZzsnssbbDlqyzzsxxsehjxsecddywbw.class, ZzssyyybnsrYqtqyzzsfpbywbw.class, ZzssyyybnsrJdclscqyxsqktjbywbw.class, ZzssyyybnsrJdcxstyfplycybbywbw.class, Zzssyyybnsr02Bqjxsemxbywbw.class, ZzssyyybnsrCpygxcslmxbywbw.class, ZzssyyybnsrJdclscqyxsmxbywbw.class, ZzssyyybnsrJdcxstyfpqdywbw.class, ZzssyyybnsrHznsqytycddywbw.class, ZzssyyybnsrCpygxcqkmxbywbw.class, ZzssyyybnsrHqysqyfzjgcddywbw.class, ZzssyyybnsrZzsysfpdkqdywbw.class, ZzssyyybnsrJdcljxqyxsmxbywbw.class, Zzssyyybnsr05Bdcfqdkjsbywbw.class, ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw.class, ZzsybnsrsbDxqyfzjgzzshznsxxcddbw.class, ZzsybnsrsbJyzyxsyphzbbw.class, ZzssyyybnsrJyzyfjyxxmxbywbw.class, Zzssyyybnsr01Bqxsqkmxbywbw.class, ZzssyyybnsrZbbw.class, ZzssyyybnsrKstlqyzzsfpbywbw.class, Zzsywbw.class, ZzsybnsrsbDkdjsstyjksdkqdbw.class, ZzsybnsrsbYzqyfzjgzzshznsxxcddbw.class, ZzssyyybnsrHznsqyzzsfpbywbw.class, Zzssyyybnsr04Bqjxsemxbywbw.class, ZzsybnsrsbNdhkysqyqsbbw.class, ZzssyyybnsrBqdkjxsejgmxbywbw.class, ZzsybnsrsbCbfhdccpzzsjxsejsbbw.class, ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw.class })
@Getter
@Setter
public abstract class TaxDoc {
    protected String bbh;

    protected String xmlbh;

    protected String xmlmc;
}