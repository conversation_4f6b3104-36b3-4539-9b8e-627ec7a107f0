package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 进项信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jxGridlbVO", propOrder = { "ewbhxh", "bqfsjx", "mshwyjxse", "fzcssjxse", "zrjxse", "bqsjdkjxse" })
@Getter
@Setter
public class JxGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 进项-本期发生进项
     */
    protected BigDecimal bqfsjx;

    /**
     * 进项-免税货物用
     */
    protected BigDecimal mshwyjxse;

    /**
     * 进项-非正常损失
     */
    protected BigDecimal fzcssjxse;

    /**
     * 进项-折让
     */
    protected BigDecimal zrjxse;

    /**
     * 进项-本期实际抵扣进项
     */
    protected BigDecimal bqsjdkjxse;
}