package com.css.znsb.nssb.job;

import cn.hutool.core.convert.Convert;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.service.ssyc.SsycService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SsycJob {

    @Resource
    private SsycService ssycService;

    @XxlJob("ssycjob")
    public void execute() {
        String paramsJson = XxlJobHelper.getJobParam();
        String sszq = null;
        List<String> qydmzList = null;
        if (GyUtils.isNotNull(paramsJson)) {
            Map<String, Object> params = JsonUtils.toMap(paramsJson);
            sszq = Convert.toStr(params.get("sszq"));
            qydmzList = Convert.toList(String.class, params.get("qydmzList"));
        }
        log.info("开始进行税收预测");
        ssycService.ssyc(sszq, qydmzList, false);
        log.info("税收预测完成");
    }

}
