package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 增值税纳税申报表附列资料（附表三）
 *
 * <p>ysfwkcxmmxGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="ysfwkcxmmxGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ysfwkcxmmxGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}ysfwkcxmmxGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ysfwkcxmmxGrid", propOrder = { "ysfwkcxmmxGridlbVO" })
public class YsfwkcxmmxGrid {
    @XmlElement(nillable = true, required = true)
    protected YsfwkcxmmxGridlbVO ysfwkcxmmxGridlbVO;

    /**
     * 获取ysfwkcxmmxGridlbVO属性的值。
     */
    public YsfwkcxmmxGridlbVO getYsfwkcxmmxGridlbVO() {
        return ysfwkcxmmxGridlbVO;
    }

    /**
     * 设置ysfwkcxmmxGridlbVO属性的值。
     */
    public void setYsfwkcxmmxGridlbVO(YsfwkcxmmxGridlbVO value) {
        this.ysfwkcxmmxGridlbVO = value;
    }
}