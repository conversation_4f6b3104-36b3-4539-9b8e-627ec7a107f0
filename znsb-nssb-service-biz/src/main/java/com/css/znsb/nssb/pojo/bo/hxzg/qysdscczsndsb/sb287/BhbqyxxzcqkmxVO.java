
package com.css.znsb.nssb.pojo.bo.hxzg.qysdscczsndsb.sb287;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * 被合并企业股东取得股权和其他资产情况
 * 
 * <p>bhbqyxxzcqkmxVO complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="bhbqyxxzcqkmxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="gdmc" type="{http://www.chinatax.gov.cn/dataspec/}gdmc" minOccurs="0"/>
 *         &lt;element name="xmmc" type="{http://www.chinatax.gov.cn/dataspec/}xmmc" minOccurs="0"/>
 *         &lt;element name="gyjz" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *         &lt;element name="jsjc" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bhbqyxxzcqkmxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "gdmc",
    "xmmc",
    "gyjz",
    "jsjc"
})
public class BhbqyxxzcqkmxVO implements Serializable {

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -6122768813222670139L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String gdmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xmmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double gyjz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jsjc;

    /**
     * 获取gdmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdmc() {
        return gdmc;
    }

    /**
     * 设置gdmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdmc(String value) {
        this.gdmc = value;
    }

    /**
     * 获取xmmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXmmc() {
        return xmmc;
    }

    /**
     * 设置xmmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXmmc(String value) {
        this.xmmc = value;
    }

    /**
     * 获取gyjz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getGyjz() {
        return gyjz;
    }

    /**
     * 设置gyjz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setGyjz(Double value) {
        this.gyjz = value;
    }

    /**
     * 获取jsjc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJsjc() {
        return jsjc;
    }

    /**
     * 设置jsjc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJsjc(Double value) {
        this.jsjc = value;
    }

}
