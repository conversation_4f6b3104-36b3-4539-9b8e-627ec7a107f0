package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 销项信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xxGridlbVO", propOrder = { "ewbhxh", "xsdl", "dj2", "sdsr", "bzsdjwfy", "ysjwfy", "deslhyzl" })
@Getter
@Setter
public class XxGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 销售电量上网电量
     */
    protected BigDecimal xsdl;

    /**
     * 电价
     */
    protected BigDecimal dj2;

    /**
     * 销项-售电收入
     */
    protected BigDecimal sdsr;

    /**
     * 销项-其中不征税的价外费用
     */
    protected BigDecimal bzsdjwfy;

    /**
     * 销项-其中应税价外费用
     */
    protected BigDecimal ysjwfy;

    /**
     * 定额税率或预征率
     */
    protected BigDecimal deslhyzl;
}