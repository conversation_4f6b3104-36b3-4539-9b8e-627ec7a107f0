package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 《机动车辆生产企业销售明细表》
 *
 * <p>jdclscqyxsmxbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="jdclscqyxsmxbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="jdcljxqyxsmxbGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}jdclscqyxsmxbGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jdclscqyxsmxbGrid", propOrder = { "jdcljxqyxsmxbGridlbVO" })
public class JdclscqyxsmxbGrid {
    @XmlElement(nillable = true, required = true)
    protected List<JdclscqyxsmxbGridlbVO> jdcljxqyxsmxbGridlbVO;

    /**
     * Gets the value of the jdcljxqyxsmxbGridlbVO property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the jdcljxqyxsmxbGridlbVO property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getJdcljxqyxsmxbGridlbVO().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link JdclscqyxsmxbGridlbVO}
     */
    public List<JdclscqyxsmxbGridlbVO> getJdcljxqyxsmxbGridlbVO() {
        if (jdcljxqyxsmxbGridlbVO == null) {
            jdcljxqyxsmxbGridlbVO = new ArrayList<JdclscqyxsmxbGridlbVO>();
        }
        return this.jdcljxqyxsmxbGridlbVO;
    }
}