
package com.css.znsb.nssb.pojo.vo.cchxwssb.cchxwsnssb.gy;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 申报比对结果VO
 * 
 * <p>SBSbbdBdjgVO complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SBSbbdBdjgVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="zfbz1" type="{http://www.chinatax.gov.cn/dataspec/}zfbz1" minOccurs="0"/>
 *         &lt;element name="nsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh" minOccurs="0"/>
 *         &lt;element name="bmdbz" type="{http://www.chinatax.gov.cn/dataspec/}bmdbz" minOccurs="0"/>
 *         &lt;element name="yjkzcsuuid" type="{http://www.chinatax.gov.cn/dataspec/}yjkzcsuuid" minOccurs="0"/>
 *         &lt;element name="pzxh" type="{http://www.chinatax.gov.cn/dataspec/}pzxh" minOccurs="0"/>
 *         &lt;element name="bdwcbz" type="{http://www.chinatax.gov.cn/dataspec/}bdwcbz" minOccurs="0"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh" minOccurs="0"/>
 *         &lt;element name="bdjg" type="{http://www.chinatax.gov.cn/dataspec/}bdjg" minOccurs="0"/>
 *         &lt;element name="uuid" type="{http://www.chinatax.gov.cn/dataspec/}uuid" minOccurs="0"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq" minOccurs="0"/>
 *         &lt;element name="sbuuid" type="{http://www.chinatax.gov.cn/dataspec/}sbuuid" minOccurs="0"/>
 *         &lt;element name="nsrlx3" type="{http://www.chinatax.gov.cn/dataspec/}nsrlx3" minOccurs="0"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz" minOccurs="0"/>
 *         &lt;element name="sfcbs" type="{http://www.chinatax.gov.cn/dataspec/}sfcbs" minOccurs="0"/>
 *         &lt;element name="shbdbz" type="{http://www.chinatax.gov.cn/dataspec/}shbdbz" minOccurs="0"/>
 *         &lt;element name="jsbz2" type="{http://www.chinatax.gov.cn/dataspec/}jsbz2" minOccurs="0"/>
 *         &lt;element name="sfyczb" type="{http://www.chinatax.gov.cn/dataspec/}sfyczb" minOccurs="0"/>
 *         &lt;element name="yxce" type="{http://www.chinatax.gov.cn/dataspec/}yxce" minOccurs="0"/>
 *         &lt;element name="tsxx" type="{http://www.chinatax.gov.cn/dataspec/}tsxx" minOccurs="0"/>
 *         &lt;element name="bdjgmxGrid" minOccurs="0">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded" minOccurs="0">
 *                   &lt;element name="bdjgmxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBSbbdBdjgmxVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBSbbdBdjgVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "zfbz1",
    "nsrsbh",
    "bmdbz",
    "yjkzcsuuid",
    "pzxh",
    "bdwcbz",
    "djxh",
    "bdjg",
    "uuid",
    "skssqq",
    "sbuuid",
    "nsrlx3",
    "skssqz",
    "sfcbs",
    "shbdbz",
    "jsbz2",
    "sfyczb",
    "yxce",
    "tsxx",
    "bdjgmxGrid"
})
public class SBSbbdBdjgVO
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfbz1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bmdbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yjkzcsuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pzxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bdwcbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String bdjg;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String uuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nsrlx3;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfcbs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String shbdbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jsbz2;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfyczb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yxce;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tsxx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected BdjgmxGrid bdjgmxGrid;

    /**
     * 获取zfbz1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfbz1() {
        return zfbz1;
    }

    /**
     * 设置zfbz1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfbz1(String value) {
        this.zfbz1 = value;
    }

    /**
     * 获取nsrsbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrsbh() {
        return nsrsbh;
    }

    /**
     * 设置nsrsbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrsbh(String value) {
        this.nsrsbh = value;
    }

    /**
     * 获取bmdbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBmdbz() {
        return bmdbz;
    }

    /**
     * 设置bmdbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBmdbz(String value) {
        this.bmdbz = value;
    }

    /**
     * 获取yjkzcsuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYjkzcsuuid() {
        return yjkzcsuuid;
    }

    /**
     * 设置yjkzcsuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYjkzcsuuid(String value) {
        this.yjkzcsuuid = value;
    }

    /**
     * 获取pzxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPzxh() {
        return pzxh;
    }

    /**
     * 设置pzxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPzxh(String value) {
        this.pzxh = value;
    }

    /**
     * 获取bdwcbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBdwcbz() {
        return bdwcbz;
    }

    /**
     * 设置bdwcbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBdwcbz(String value) {
        this.bdwcbz = value;
    }

    /**
     * 获取djxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * 设置djxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * 获取bdjg属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBdjg() {
        return bdjg;
    }

    /**
     * 设置bdjg属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBdjg(String value) {
        this.bdjg = value;
    }

    /**
     * 获取uuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * 设置uuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUuid(String value) {
        this.uuid = value;
    }

    /**
     * 获取skssqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * 设置skssqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * 获取sbuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbuuid() {
        return sbuuid;
    }

    /**
     * 设置sbuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbuuid(String value) {
        this.sbuuid = value;
    }

    /**
     * 获取nsrlx3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrlx3() {
        return nsrlx3;
    }

    /**
     * 设置nsrlx3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrlx3(String value) {
        this.nsrlx3 = value;
    }

    /**
     * 获取skssqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * 设置skssqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * 获取sfcbs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfcbs() {
        return sfcbs;
    }

    /**
     * 设置sfcbs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfcbs(String value) {
        this.sfcbs = value;
    }

    /**
     * 获取shbdbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShbdbz() {
        return shbdbz;
    }

    /**
     * 设置shbdbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShbdbz(String value) {
        this.shbdbz = value;
    }

    /**
     * 获取jsbz2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJsbz2() {
        return jsbz2;
    }

    /**
     * 设置jsbz2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJsbz2(String value) {
        this.jsbz2 = value;
    }

    /**
     * 获取sfyczb属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfyczb() {
        return sfyczb;
    }

    /**
     * 设置sfyczb属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfyczb(String value) {
        this.sfyczb = value;
    }

    /**
     * 获取yxce属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxce() {
        return yxce;
    }

    /**
     * 设置yxce属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxce(String value) {
        this.yxce = value;
    }

    /**
     * 获取tsxx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTsxx() {
        return tsxx;
    }

    /**
     * 设置tsxx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTsxx(String value) {
        this.tsxx = value;
    }

    /**
     * 获取bdjgmxGrid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BdjgmxGrid }
     *     
     */
    public BdjgmxGrid getBdjgmxGrid() {
        return bdjgmxGrid;
    }

    /**
     * 设置bdjgmxGrid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BdjgmxGrid }
     *     
     */
    public void setBdjgmxGrid(BdjgmxGrid value) {
        this.bdjgmxGrid = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded" minOccurs="0">
     *         &lt;element name="bdjgmxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}SBSbbdBdjgmxVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "bdjgmxGridlb"
    })
    public static class BdjgmxGrid
        implements Serializable
    {

        private final static long serialVersionUID = 5102454195654018577L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        private List<SBSbbdBdjgmxVO> bdjgmxGridlb;

        /**
         * Gets the value of the bdjgmxGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the bdjgmxGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getBdjgmxGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SBSbbdBdjgmxVO }
         * 
         * 
         */
        public List<SBSbbdBdjgmxVO> getBdjgmxGridlb() {
            if (bdjgmxGridlb == null) {
                bdjgmxGridlb = new ArrayList<SBSbbdBdjgmxVO>();
            }
            return this.bdjgmxGridlb;
        }

    }

}
