package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 部分产品销售统计表业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_bfcpxstjbywbw", propOrder = { "zzssyyybnsrBfcpxstjb" })
@Getter
@Setter
public class ZzssyyybnsrBfcpxstjbywbw extends TaxDoc {
    /**
     * 部分产品销售统计表
     */
    @XmlElement(nillable = true, name = "Zzssyyybnsr_bfcpxstjb", required = true)
    @JSONField(name = "Zzssyyybnsr_bfcpxstjb")
    protected ZzssyyybnsrBfcpxstjb zzssyyybnsrBfcpxstjb;
}