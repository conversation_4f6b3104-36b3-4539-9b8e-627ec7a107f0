package com.css.znsb.nssb.pojo.vo.sswszm;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WszmVO {

    /**
     * 开具金额
     */
    @JsonProperty("kjje")
    private BigDecimal kjje;
    /**
     * 税款属性代码
     */
    @JsonProperty("sksxDm")
    private String sksxDm;
    /**
     * 征收uuid
     */
    @JsonProperty("zsuuid")
    private String zsuuid;
    /**
     * 征收项目代码
     */
    @JsonProperty("zsxmDm")
    private String zsxmDm;
    /**
     * 征收项目名称
     */
    @JsonProperty("zsxmmc")
    private String zsxmmc;
    /**
     * 征收品目名称
     */
    @JsonProperty("zspmmc")
    private String zspmmc;
    /**
     * 税款种类代码
     */
    @JsonProperty("skzlDm")
    private String skzlDm;
    /**
     * 时间日期1
     */
    @JsonProperty("sjrq1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", shape = JsonFormat.Shape.STRING)
    private Date sjrq1;
    /**
     * 征收品目代码
     */
    @JsonProperty("zspmDm")
    private String zspmDm;
    /**
     * 税款所属期起
     */
    @JsonProperty("skssqq")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", shape = JsonFormat.Shape.STRING)
    private Date skssqq;
    /**
     * 税款所属期止
     */
    @JsonProperty("skssqz")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", shape = JsonFormat.Shape.STRING)
    private Date skssqz;
    /**
     * 银行货币代码
     */
    @JsonProperty("yhhbDm")
    private String yhhbDm;
    /**
     * 银行营业网点代码
     */
    @JsonProperty("yhyywdDm")
    private String yhyywdDm;
    /**
     * 实缴金额
     */
    @JsonProperty("sjje")
    private BigDecimal sjje;
    /**
     * 征收机关代码
     */
    @JsonProperty("zsjgDm")
    private String zsjgDm;
    /**
     * 电子税票号码
     */
    @JsonProperty("dzsphm")
    private String dzsphm;
    /**
     * 税票uuid
     */
    @JsonProperty("spuuid")
    private String spuuid;
    /**
     * 税款缴纳方式代码
     */
    @JsonProperty("skjnfsDm")
    private String skjnfsDm;
    /**
     * 开具日期
     */
    @JsonProperty("kjrq")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", shape = JsonFormat.Shape.STRING)
    private Date kjrq;
    /**
     * 录入人代码
     */
    @JsonProperty("lrrDm")
    private String lrrDm;
    /**
     * 税款归口代码
     */
    @JsonProperty("skgkDm")
    private String skgkDm;
    /**
     * 印制凭证序号
     */
    @JsonProperty("yzpzxh")
    private String yzpzxh;
    /**
     * 税款所属税务机关代码
     */
    @JsonProperty("skssswjgDm")
    private String skssswjgDm;
    /**
     * 开具税务师事务所证明原因
     */
    @JsonProperty("kjsswszmyy")
    private String kjsswszmyy;
    /**
     * 是否电子收入退还书
     */
    @JsonProperty("sfdzsrths")
    private String sfdzsrths;
    /**
     * 印制凭证种类代码
     */
    @JsonProperty("yzpzzlDm")
    private String yzpzzlDm;

}