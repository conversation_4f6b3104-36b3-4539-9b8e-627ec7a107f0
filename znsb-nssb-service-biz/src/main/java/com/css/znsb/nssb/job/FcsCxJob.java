package com.css.znsb.nssb.job;

import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjbMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjb;
import com.css.znsb.nssb.pojo.dto.cchxwssb.fcscztdsys.*;
import com.css.znsb.nssb.pojo.dto.cchxwssb.fcscztdsys.fcs.DataPlatformFcsDTO;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.*;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.FcsService;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.impl.FtsDataPlatformServiceImpl;
import com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxFcssyfcdzbMapper;
import com.css.znsb.tzzx.pojo.domain.tzzx.fcs.ZnsbTzzxFcssyfcdzbDO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class FcsCxJob {

    @Resource
    private FcsService fcsService;
    @Resource
    private CompanyApi companyApi;
    @Resource
    private ZnsbCxsFyxxcjbMapper znsbCxsFyxxcjbMapper;
    @Resource
    private ZnsbTzzxFcssyfcdzbMapper znsbTzzxFcssyfcdzbMapper;
    @Resource
    private FtsDataPlatformServiceImpl ftsDataPlatformService;


    /**
     * 财行税申报任务归集
     */
    @XxlJob("FcsCxJob")
    public void execute() {
        log.info("==========开始房产税归集==========");
        String djxh = XxlJobHelper.getJobParam();
        log.info("入参登记序号djxh{}",djxh);
        final CommonResult<List<CompanyInfoResDTO>> response = companyApi.getAllCompanyInfo();
        final List<CompanyInfoResDTO> companyInfoResDTOS = response.getData();
        for(CompanyInfoResDTO companyInfo : companyInfoResDTOS){
            try {
                if(GyUtils.isNotNull(djxh) && !djxh.equals(companyInfo.getDjxh())){
                    continue;
                }
                final CommonResult<CompanyBasicInfoDTO> resData = companyApi.basicInfo(companyInfo.getDjxh(),companyInfo.getNsrsbh());
                if (GyUtils.isNull(resData) && GyUtils.isNull(resData.getData())){
                    log.info("{}企业基本信息为空",companyInfo.getDjxh());
                    continue;
                }
                final CompanyBasicInfoDTO basicInfoDTO = resData.getData();
                if (GyUtils.isNull(basicInfoDTO)){
                    log.info("{}企业基本信息为空",companyInfo.getDjxh());
                    continue;
                }
                final FyxxFormDTO fyxxFormDTO = new FyxxFormDTO();
                fyxxFormDTO.setDjxh(basicInfoDTO.getDjxh());
                fyxxFormDTO.setNsrsbh(basicInfoDTO.getNsrsbh());
                fyxxFormDTO.setZgswskfjDm(basicInfoDTO.getZgswskfjDm());
                fyxxFormDTO.setUuid(GyUtils.getUuid());
                log.info("归集任务查询房产时入参fyxxFormDTO{}", JsonUtils.toJson(fyxxFormDTO));
                final CommonResult<Object> result= fcsService.selectFcsfyxx(fyxxFormDTO);
                log.info("归集任务查询房产时出参result{}", JsonUtils.toJson(result));
                if(result.getCode() == 1  && GyUtils.isNotNull(result.getData())) {
                    final FcsCxFyxxResponseDTO respDTO = (FcsCxFyxxResponseDTO) result.getData();
                    final List<FyxxFormDTO> fwxxGridlb = respDTO.getFwxxGridlb();
                    final FcsFwxxCxVO fcsFwxxCxVO = new FcsFwxxCxVO();
                    fcsFwxxCxVO.setDjxh(basicInfoDTO.getDjxh());
                    final List<ZnsbCxsFyxxcjb> znsbCxsFyxxcjbs = znsbCxsFyxxcjbMapper.queryFwxxList(fcsFwxxCxVO);
                    for(ZnsbCxsFyxxcjb znsbCxsFyxxcjb : znsbCxsFyxxcjbs){
                        if(GyUtils.isNull(znsbCxsFyxxcjb.getFyxxuuid())){
                            continue;
                        }
                        Boolean flag = true;
                        for (FyxxFormDTO fyxxForm : fwxxGridlb) {
                            if(znsbCxsFyxxcjb.getFyxxuuid().equals(fyxxForm.getFyxxuuid())){
                                flag = false;
                                break;
                            }
                        }
                        log.info("fyxxuuid{},flag{}", znsbCxsFyxxcjb.getFyxxuuid(),flag);
                        if(flag){
                            znsbCxsFyxxcjb.setYxbz("N");
                            znsbCxsFyxxcjbMapper.updateById(znsbCxsFyxxcjb);
                            final List<ZnsbTzzxFcssyfcdzbDO> fcsdzbList = znsbTzzxFcssyfcdzbMapper.selectDzbxxByBdFybh(znsbCxsFyxxcjb.getFybh());
                            if(GyUtils.isNotNull(fcsdzbList)){
                                fcsdzbList.forEach(znsbTzzxFcssyfcdzbDO -> {
                                    znsbTzzxFcssyfcdzbDO.setYxbz("N");
                                    znsbTzzxFcssyfcdzbDO.setXybz("N");
                                    znsbTzzxFcssyfcdzbMapper.updateById(znsbTzzxFcssyfcdzbDO);
                                });
                            }
                        }
                    }
                    for (FyxxFormDTO fyxxForm : fwxxGridlb) {
                        try{
                            fyxxForm.setDjxh(basicInfoDTO.getDjxh());
                            fyxxForm.setNsrsbh(basicInfoDTO.getNsrsbh());
                            fcsService.sqgjFcssyxx(fyxxForm);
                        }catch (Exception e){
                            log.error(fyxxForm.getFybh()+"房产归集异常",e);
                        }
                    }
                }
            }catch (Exception e){
                log.error("房产归集异常",e);
            }
        }
        log.info("==========房产税归集结束==========");
    }

    @XxlJob("saveFcscjjzxx")
    public void saveFcscjjzxx() {
        log.info("==========开始生成房产税从价信息==========");
        String param = XxlJobHelper.getJobParam();
        log.info("入参param{}",param);
        String paramArr[] = param.split(",");
        String fyxxuuid = paramArr[0];
        String cjsyuuid = paramArr[1];
        String djxh = paramArr[2];
        String nsrsbh = paramArr[3];
        String fcyz = paramArr[4];
        String czfcyz = paramArr[5];
        String czfcmj = paramArr[6];
        String bgyxqq = paramArr[7];
        FcsYsxxVO fcsYsxxVO = fcsService.queryFcsYsxxbyuuid(fyxxuuid,"");
        List<CjmxVO> cjmxVOList = fcsYsxxVO.getFcsCjYsxxVO().getCjmxVOList();
        CjmxVO cjmxVO = new CjmxVO();
        for(CjmxVO cjmxVO1 : cjmxVOList){
            if(cjmxVO1.getCjsyuuid().equals(cjsyuuid)){
                cjmxVO = cjmxVO1;
                cjmxVO.setDjxh(djxh);
                cjmxVO.setNsrsbh(nsrsbh);
                cjmxVO.setFcyz(Double.parseDouble(fcyz));
                cjmxVO.setCzwyz(Double.parseDouble(czfcyz));
                cjmxVO.setCzfcmj(Double.parseDouble(czfcmj));
                cjmxVO.setBgyxqq(bgyxqq);
                break;
            }
        }
        FcsCjYsxxVO fcsCjYsxxVO = new FcsCjYsxxVO();
        fcsCjYsxxVO.getCjmxVOList().add(cjmxVO);
        fcsService.saveFcsCjYsxx(fcsCjYsxxVO);
        log.info("==========生成房产税从价信息结束==========");
    }

    @XxlJob("FcsDataPlatform")
    public void fcsDataPlatform() {
        log.info("==========开始房产税数据提取==========");
        final String sxydqypzcsz = CacheUtils.getXtcs("NSSB_FTS_SXYD_QYPZ","N");
        log.info("sxydqypzcsz{}",sxydqypzcsz);
        String nsrsbh = XxlJobHelper.getJobParam();
        log.info("入参纳税人识别号nsrsbh{}",nsrsbh);
        final CommonResult<List<CompanyInfoResDTO>> response = companyApi.getAllCompanyInfo();
        final List<CompanyInfoResDTO> companyInfoResDTOS = response.getData();
        for(CompanyInfoResDTO companyInfo : companyInfoResDTOS){
            try {
                if(GyUtils.isNotNull(nsrsbh) && !nsrsbh.equals(companyInfo.getNsrsbh())){
                    continue;
                }
                if(GyUtils.isNotNull(sxydqypzcsz) && sxydqypzcsz.contains(companyInfo.getNsrsbh())){
                    continue;
                }
                final CompanyBasicInfoDTO basicInfo = companyApi.basicInfo(companyInfo.getDjxh(),companyInfo.getNsrsbh()).getData();
                if(GyUtils.isNotNull(basicInfo) && "3".equals(basicInfo.getQylxz())){
                    continue;
                }
                final List<DataPlatformFcsDTO> data = ftsDataPlatformService.handle(companyInfo.getNsrsbh(),DataPlatformFcsDTO.class);
                for(DataPlatformFcsDTO dataPlatformFcsDTO : data){
                    dataPlatformFcsDTO.setNsrsbh(companyInfo.getNsrsbh());
                    dataPlatformFcsDTO.setDjxh(companyInfo.getDjxh());
                    fcsService.saveOrUpdateFwxx(dataPlatformFcsDTO);
                }
            }catch (Exception e){
                log.error("房产税数据提取结束",e);
            }
        }
        log.info("==========房产税数据提取结束==========");
    }
}
