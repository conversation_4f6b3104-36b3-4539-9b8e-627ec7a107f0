package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税一般纳税人本期应纳税额按预算科目分解表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_zzsybnsrbqynseayskmfjb", propOrder = { "zzsybnsrbqynseayskmfjbFormVO" })
@XmlSeeAlso({ ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw.ZzssyyybnsrZzsybnsrbqynseayskmfjb.class })
@Getter
@Setter
public class ZzssyyybnsrZzsybnsrbqynseayskmfjb {
    @XmlElement(nillable = true, required = true)
    protected ZzsybnsrbqynseayskmfjbFormVO zzsybnsrbqynseayskmfjbFormVO;
}