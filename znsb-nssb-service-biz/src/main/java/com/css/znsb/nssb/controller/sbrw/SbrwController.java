package com.css.znsb.nssb.controller.sbrw;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.map.MapUtil;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.zqrl.ZqrlApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.company.DjxhReqVO;
import com.css.znsb.mhzc.pojo.xzj.GszqxxVO;
import com.css.znsb.mhzc.pojo.yhxx.RyxxVO;
import com.css.znsb.nssb.api.sbrw.SbrwApi;
import com.css.znsb.nssb.constants.SbrwztConstants;
import com.css.znsb.nssb.constants.dto.sbrw.QuerySbrwxxByYfReqDTO;
import com.css.znsb.nssb.constants.dto.sbrw.QuerySbrwxxByYfRespDTO;
import com.css.znsb.nssb.constants.enums.CwbbZlxlEnum;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.constants.enums.ZsxmDmEnum;
import com.css.znsb.nssb.mapper.sbrw.SbSbbDOMapper;
import com.css.znsb.nssb.mapper.sbrw.SbSbxxDOMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc.general.SBSbbdBdjgmxVO;
import com.css.znsb.nssb.pojo.domain.sbrw.*;
import com.css.znsb.nssb.pojo.dto.RefreshSbrwZtReqDTO;
import com.css.znsb.nssb.pojo.dto.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjBsqcDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbmxxxDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbqkcxNewReqDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbqkcxRespDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbxxDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.*;
import com.css.znsb.nssb.service.qhjtjcsssjbs.ZnsbNssbQhjtjcsssjService;
import com.css.znsb.nssb.service.sbqkcx.SbqkcxSbxxService;
import com.css.znsb.nssb.service.sbqkcx.SbqkcxSbxxmxService;
import com.css.znsb.nssb.service.sbrw.*;
import com.css.znsb.nssb.service.sbrw.cwbbbsrw.ZnsbNssbCwkjzdbaxxService;
import com.css.znsb.nssb.service.skjn.ZnsbSkjnService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.css.znsb.nssb.constants.SbrwztConstants.JKZT_WXJK_DM;
import static com.css.znsb.nssb.constants.enums.ZsxmDmEnum.JSXZSYXSFSR;
import static com.css.znsb.nssb.constants.enums.ZsxmDmEnum.SLJSJJ;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project qyd-znsb
 * @package com.css.znsb.nssb.controller.sbrw
 * @file SbglController.java 创建时间:2024/4/5 11:12
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 职能申报-申报管理
 * @reviewer 审核人
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */
@Tag(name = "申报任务管理")
@RestController
@RequestMapping("/sbrw/v1")
@Validated
@Slf4j
public class SbrwController implements SbrwApi {

    @Resource
    private ISbrwMxService rwmxService;

    @Resource
    private ISbrwXxService rwxxService;

    @Resource
    private ZnsbNssbSbrwmxDOService znsbNssbSbrwmxDOService;

    @Resource
    private ZnsbNssbSbrwService znsbNssbSbrwService;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private SjjhService sjjhService;

    @Resource
    private SbSbbDOMapper sbSbbDOMapper;

    @Resource
    private SbSbxxDOMapper sbSbxxDOMapper;

    @Resource
    private ZnsbNssbBdjgService bdjgService;

    @Resource
    private ZqrlApi zqrlApi;

    @Resource
    private ISbrwResetService sbrwResetService;

    @Resource
    private ZnsbNssbQhjtjcsssjService znsbNssbQhjtjcsssjService;

    @Resource
    private SbqkcxSbxxService sbqkcxSbxxService;

    @Resource
    private SbqkcxSbxxmxService sbqkcxSbxxmxService;

    @Resource
    private ZnsbNssbSbrwCwbbService znsbNssbSbrwCwbbService;

    @Resource
    private ZnsbSkjnService znsbSkjnService;

    @Resource
    private SbrwRefreshZtService sbrwRefreshZtService;

    private static final ZoneId systemDefaultZoneId = ZoneId.systemDefault();

    @Resource
    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;

    @Resource
    private ZnsbNssbCwkjzdbaxxService znsbNssbCwkjzdbaxxService;

    @PostMapping("/queryNsrGlList")
    @Operation(summary = "")
    public CommonResult<List<SbrwQyxxDTO>> queryNsrGlList() {
        log.info("查询纳税人管理列表");
        final String userID = ZnsbSessionUtils.getYhUuid();
        return CommonResult.success(this.getGlqyList(userID));
    }


    @PostMapping("/queryRwTjList")
    @Operation(summary = "")
    public CommonResult<List<ZnsbSbrwXxDO>> queryRwTjList(@RequestBody SbrwTjQueryDTO req) {
        log.info("查询申报任务统计信息");
        final String sbny = this.getCurrentSbny();
        if (GyUtils.isNull(req.getSbny())) {
            req.setSbny(sbny);
        }

        if (GyUtils.isNull(req.getNsrsbhList())) {
            final String userID = ZnsbSessionUtils.getYhUuid();
            final List<SbrwQyxxDTO> glqyList = this.getGlqyList(userID);
            final Collection<String> nsrsbhList = GyUtils.getValueByKey(glqyList, "nsrsbh");
            req.setNsrsbhList(nsrsbhList);
        }


        return CommonResult.success(this.rwxxService.queryRwxxList(req));
    }

    @PostMapping("/queryRwmxList")
    @Operation(summary = "")
    public CommonResult<List<ZnsbSbrwMxDO>> queryRwmxList(@RequestBody SbrwmxQueryDTO req) {
        log.info("查询申报任务列表");
        return CommonResult.success(this.rwmxService.queryRwmxList(req));
    }

    private String getCurrentSbny() {
        return DateUtils.getSystemCurrentTime(17);
    }

    private List<SbrwQyxxDTO> getGlqyList(String userID) {
        //TODO 获取管理企业信息 暂时使用mock数据
        final String qyxxJson = new ClassPathResource("/mock/ghxx.json").readUtf8Str();
        final List<SbrwQyxxDTO> qyList = JsonUtils.toList(qyxxJson, SbrwQyxxDTO.class);
        return qyList;
    }

    @PostMapping("/querySbrwxxByYf")
    @Operation(summary = "查询某月申报任务信息")
    public CommonResult<QuerySbrwxxByYfRespDTO> querySbrwxxByYf(@RequestBody QuerySbrwxxByYfReqDTO querySbrwxxByYfReqDTO) {
        log.info("查询某月申报任务信息");
        return CommonResult.success(znsbNssbSbrwmxDOService.querySbrwxxByYf(querySbrwxxByYfReqDTO));
    }

    @PostMapping("/refreshSbrwZt")
    @Operation(summary = "申报任务明细-税费申报刷新申报任务状态")
    public CommonResult<String> refreshSbrwZt(@RequestBody RefreshSbrwZtReqDTO refreshSbrwZtReqDTO) {
        //判断是否乐企企业和是否启用rpa
        if ("Y".equals(CacheUtils.getXtcs("QYD_RPA"))) {
            //根据任务类型代码判断调用哪个rpa，资料报送类单独调用rpa处理
            if (SbrwztConstants.RWLX_ZLBS_DM.equals(refreshSbrwZtReqDTO.getRwlxDm())) {
                //TODO 资料报送类需要单独区分调用rpa

                //千户集团
                if (YzpzzlEnum.QHJTJCSSSJBS.getDm().equals(refreshSbrwZtReqDTO.getYzpzzlDm())) {
                    //组装千户集团报送清册查询的请求报文
                    final Map<String, String> reqMap = new HashMap<>();
                    reqMap.put("BsqxQ", DateUtils.dateToString(DateUtils.nowYearFirstDay()));
                    reqMap.put("BsqxZ", DateUtils.dateToString(DateUtils.nowYearLastDay()));
                    reqMap.put("BsssqQ", refreshSbrwZtReqDTO.getSkssqq());
                    reqMap.put("BsssqZ", refreshSbrwZtReqDTO.getSkssqz());

                    //发起数据交换
                    SjjhDTO sjjhDTO = new SjjhDTO();
                    sjjhDTO.setSjjhlxDm("QHJTGETQC");
                    sjjhDTO.setYwbm("QHJTBS0001");
                    sjjhDTO.setYwuuid(refreshSbrwZtReqDTO.getSbrwuuid());
                    sjjhDTO.setDjxh(refreshSbrwZtReqDTO.getDjxh());
                    sjjhDTO.setNsrsbh(refreshSbrwZtReqDTO.getNsrsbh());
                    sjjhDTO.setXzqhszDm(refreshSbrwZtReqDTO.getXzqhszDm());
                    sjjhDTO.setBwnr(JsonUtils.toJson(reqMap));
                    CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
                    if (result.getCode() != 1){
                        return CommonResult.error(500, result.getMsg());
                    }
                    log.info("税费申报调用千户集团清册查询接口返回报文为：{}", JsonUtils.toJson(result));
                    if (!GyUtils.isNull(result.getData())) {
                        List<ZnsbNssbQhjtjcsssjBsqcDTO> qhjtList = JsonUtils.toList(String.valueOf(result.getData()), ZnsbNssbQhjtjcsssjBsqcDTO.class);
                        //有数据需要触发业务内归集
                        qhjtList.get(0).setNsrsbh(refreshSbrwZtReqDTO.getNsrsbh());
                        qhjtList.get(0).setXzqhszDm(refreshSbrwZtReqDTO.getXzqhszDm());
                        znsbNssbQhjtjcsssjService.updateBsqc(qhjtList.get(0));
                        //报送状态为1代表报送成功
                        if ("1".equals(qhjtList.get(0).getBszt())) {
                            znsbNssbSbrwService.refreshSbrwZt(refreshSbrwZtReqDTO.getSbrwuuid(),
                                    null,
                                    null,
                                    SbrwztConstants.SBZT_ZLBS_BSCG_DM,
                                    DateUtils.toDate(qhjtList.get(0).getBsrq(), "yyyy-MM-dd hh:mm:ss"),
                                    null,
                                    SbrwztConstants.RWZT_YSB_DM);
                        }
                    }
                }
            } else {
                //启用RPA优先调用RPA
                SjjhDTO sjjhDTO = new SjjhDTO();
                sjjhDTO.setSjjhlxDm("SBRWZT0001");
                sjjhDTO.setYwbm("SBRWZT-RPA");
                sjjhDTO.setYwuuid(refreshSbrwZtReqDTO.getSbrwuuid());
                sjjhDTO.setDjxh(refreshSbrwZtReqDTO.getDjxh());
                sjjhDTO.setNsrsbh(refreshSbrwZtReqDTO.getNsrsbh());
                sjjhDTO.setXzqhszDm(refreshSbrwZtReqDTO.getXzqhszDm());
                //组装rpa报文
                QuerySbxxRpaReqDTO querySbxxRpaReqDTO = new QuerySbxxRpaReqDTO();
                querySbxxRpaReqDTO.setSbrqq(null);
                querySbxxRpaReqDTO.setSbrqz(null);
                querySbxxRpaReqDTO.setSkssqq(DateUtils.toDate(refreshSbrwZtReqDTO.getSkssqq(), "yyyy-MM-dd"));
                querySbxxRpaReqDTO.setSkssqz(DateUtils.toDate(refreshSbrwZtReqDTO.getSkssqz(), "yyyy-MM-dd"));
                querySbxxRpaReqDTO.setYzpzzlDm(refreshSbrwZtReqDTO.getYzpzzlDm());
                sjjhDTO.setBwnr(JsonUtils.toJson(querySbxxRpaReqDTO));
                CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
                if (result.getCode() != 1){
                    return CommonResult.error(500, result.getMsg());
                }
                QuerySbxxRpaRespDTO querySbxxRpaRespDTO = JsonUtils.toBean(String.valueOf(result.getData()), QuerySbxxRpaRespDTO.class);
                if ("0".equals(querySbxxRpaRespDTO.getCode())) {
                    //0为查询成功
                    //判断sbb是否返回数据
                    if (!GyUtils.isNull(querySbxxRpaRespDTO.getSbbList())) {
                        this.saveSbbAndUpdateSbrw(BeanUtils.toBean(querySbxxRpaRespDTO.getSbbList(), SbSbbDO.class),
                                BeanUtils.toBean(querySbxxRpaRespDTO.getSbxxList(), SbSbxxDO.class), refreshSbrwZtReqDTO);
                    }
                } else {
                    return CommonResult.error(500, querySbxxRpaRespDTO.getMessage());
                }
            }
        } else if ("Y".equals(CacheUtils.getXtcs("QYD_LQQY"))) {
            //未启用RPA判断是否乐企企业来调用乐企接口
            //财务报表单独走逻辑刷新状态
            if (YzpzzlEnum.CWBBBS.getDm().equals(refreshSbrwZtReqDTO.getYzpzzlDm())){
                ZnsbNssbSbrwDO sbrw = znsbNssbSbrwMapper.selectById(refreshSbrwZtReqDTO.getSbrwuuid());
                if (!GyUtils.isNull(sbrw)) {
                    znsbNssbCwkjzdbaxxService.cwbbbsrwjgcx(sbrw);
                }
            }else{
                //获取乐企支持税种,为空则不处理
                Map<String, Object> lqzcszMap = this.getLqsz(refreshSbrwZtReqDTO);
                if (!GyUtils.isNull(lqzcszMap)) {
                    SjjhDTO sjjhDTO = new SjjhDTO();
                    sjjhDTO.setSjjhlxDm("SBRWZT0002");
                    sjjhDTO.setYwbm("SBRWZT-LQ");
                    sjjhDTO.setYwuuid(refreshSbrwZtReqDTO.getSbrwuuid());
                    sjjhDTO.setDjxh(refreshSbrwZtReqDTO.getDjxh());
                    sjjhDTO.setNsrsbh(refreshSbrwZtReqDTO.getNsrsbh());
                    sjjhDTO.setXzqhszDm(refreshSbrwZtReqDTO.getXzqhszDm());
                    //组装乐企报文
                    SbqkcxNewReqDTO sbqkcxReqDTO = new SbqkcxNewReqDTO();
                    sbqkcxReqDTO.setDjxh(refreshSbrwZtReqDTO.getDjxh());
                    sbqkcxReqDTO.setSkssqq(refreshSbrwZtReqDTO.getSkssqq());
                    sbqkcxReqDTO.setSkssqz(refreshSbrwZtReqDTO.getSkssqz());
                    //乐企传参时不传，接到所有数据后过滤
                    //只有财行税的传这两个参数
                    if (YzpzzlEnum.CXS.getDm().equals(refreshSbrwZtReqDTO.getYzpzzlDm())) {
                        sbqkcxReqDTO.setYzpzzlDm(MapUtil.getStr(lqzcszMap,"yzpzzlDm"));
                        sbqkcxReqDTO.setZsxmDm(MapUtil.getStr(lqzcszMap,"zsxmDm"));
                    }else{
                        sbqkcxReqDTO.setYzpzzlDm("");
                        sbqkcxReqDTO.setZsxmDm("");
                    }
                    sjjhDTO.setBwnr(JsonUtils.toJson(sbqkcxReqDTO));
                    CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
                    if (result.getCode() != 1){
                        return CommonResult.error(500, result.getMsg());
                    }
                    SbqkcxRespDTO sbqkcxRespDTO = JsonUtils.toBean(String.valueOf(result.getData()), SbqkcxRespDTO.class);
                    if ("00".equals(sbqkcxRespDTO.getReturncode())) {
                        //00为成功
                        //判断sbb是否返回数据
                        if (!GyUtils.isNull(sbqkcxRespDTO.getSbxxList())) {
                            //根据当前传入yzpzzlDm筛选数据
                            this.lqsxByYzpzzlDm(sbqkcxRespDTO,lqzcszMap);
                            //根据传入的属期起止比较返回数据的属期起止过滤，因为乐企接口是属期起止范围查询，存在按期申报的查询口径会将按次申报的申报信息返回。
                            this.getSbbBySkssq(sbqkcxRespDTO,refreshSbrwZtReqDTO);
                            //再次判空防止过滤后无数据
                            if (!GyUtils.isNull(sbqkcxRespDTO.getSbxxList())){
                                List<SbSbbDO> sbSbbDOList = new ArrayList<>();
                                for (SbxxDTO sbbxDTO : sbqkcxRespDTO.getSbxxList()) {
                                    SbSbbDO sbSbbDO = BeanUtils.toBean(sbbxDTO, SbSbbDO.class);
                                    sbSbbDO.setSbsxDm1(sbbxDTO.getSbsxDm());
                                    sbSbbDOList.add(sbSbbDO);
                                }
                                this.saveSbbAndUpdateSbrw(sbSbbDOList,
                                        BeanUtils.toBean(sbqkcxRespDTO.getSbmxxxList(), SbSbxxDO.class), refreshSbrwZtReqDTO);
                            }else{
                                log.info("税费申报刷新状态调用乐企申报情况未获取到数据，sbrwuuid为：{}",refreshSbrwZtReqDTO.getSbrwuuid());
                            }
                        }
                    } else {
                        return CommonResult.error(500, sbqkcxRespDTO.getReturnmsg());
                    }
                }
            }
        }
        return CommonResult.success("成功");
    }

    private void getSbbBySkssq(SbqkcxRespDTO sbqkcxRespDTO, RefreshSbrwZtReqDTO refreshSbrwZtReqDTO) {
        if (!GyUtils.isNull(sbqkcxRespDTO.getSbxxList())) {
            List<SbxxDTO> sbxxList = new ArrayList<>();
            List<SbmxxxDTO> sbmxxxList = new ArrayList<>();
            sbqkcxRespDTO.getSbxxList().forEach(sbxxDTO -> {
                if (refreshSbrwZtReqDTO.getSkssqq().equals(DateUtils.dateToString(sbxxDTO.getSkssqq(),3))
                        && refreshSbrwZtReqDTO.getSkssqz().equals(DateUtils.dateToString(sbxxDTO.getSkssqz(),3))){
                    sbxxList.add(sbxxDTO);
                    sbqkcxRespDTO.getSbmxxxList().forEach(sbmxxxDTO -> {
                        if (sbxxDTO.getSbuuid().equals(sbmxxxDTO.getSbuuid())){
                            sbmxxxList.add(sbmxxxDTO);
                        }
                    });
                }
            });
            sbqkcxRespDTO.setSbxxList(sbxxList);
            sbqkcxRespDTO.setSbmxxxList(sbmxxxList);
        }
    }

    private void lqsxByYzpzzlDm(SbqkcxRespDTO sbqkcxRespDTO, Map<String, Object> lqzcszMap) {
        if (!GyUtils.isNull(sbqkcxRespDTO.getSbxxList())){
            List<SbxxDTO> sbxxList = new ArrayList<>();
            List<SbmxxxDTO> sbmxxxList = new ArrayList<>();
            sbqkcxRespDTO.getSbxxList().forEach(sbxxDTO -> {
                if (lqzcszMap.get("yzpzzlDm").equals(sbxxDTO.getYzpzzlDm())){
                    sbxxList.add(sbxxDTO);
                    sbqkcxRespDTO.getSbmxxxList().forEach(sbmxxxDTO -> {
                        if (sbxxDTO.getSbuuid().equals(sbmxxxDTO.getSbuuid())){
                            sbmxxxList.add(sbmxxxDTO);
                        }
                    });
                }
            });
            sbqkcxRespDTO.setSbxxList(sbxxList);
            sbqkcxRespDTO.setSbmxxxList(sbmxxxList);
        }
    }

    private Map<String, Object> getLqsz(RefreshSbrwZtReqDTO refreshSbrwZtReqDTO) {
        //判断是否乐企支持税种，如果不是则不处理
        List<Map<String, Object>> lqzcszList = CacheUtils.getTableData("cs_sb_lqjrszpzb");
        if (!GyUtils.isNull(lqzcszList)) {
            Map<String, Object> lqzcszMap = lqzcszList.stream().filter(map ->
                    String.valueOf(map.get("qydyzpzzlDm")).equals(refreshSbrwZtReqDTO.getYzpzzlDm())
                            && String.valueOf(map.get("zsxmDm")).equals(refreshSbrwZtReqDTO.getZsxmDm())
            ).findAny().orElse(null);
            if (!GyUtils.isNull(lqzcszMap)) {
                return lqzcszMap;
            }
        }
        return null;
    }

    private void saveSbbAndUpdateSbrw(List<SbSbbDO> sbbList, List<SbSbxxDO> sbxxList, RefreshSbrwZtReqDTO refreshSbrwZtReqDTO) {
        //先全量归集sbb和sbxx表数据到本地
        this.saveSbxx(sbbList, sbxxList);
        //优先用申报uuid来比对,有sbuuid的情况下不用考虑申报成功的状态
        /*if (!GyUtils.isNull(refreshSbrwZtReqDTO.getSbuuid())) {
            //组装此条申报任务对应的sbb信息
            final List<SbSbbDO> sbbNewList = new ArrayList<>();
            final List<SbSbxxDO> sbxxNewList = new ArrayList<>();
            sbbList.forEach(sbbDO -> {
                if (refreshSbrwZtReqDTO.getSbuuid().equals(sbbDO.getSbuuid())) {
                    sbbList.forEach(sbbNewDO -> {
                        if (sbbDO.getPzxh().equals(sbbNewDO.getPzxh())) {
                            sbbNewList.add(sbbNewDO);
                        }
                    });
                }
            });
            sbxxList.forEach(sbxxDO -> {
                if (refreshSbrwZtReqDTO.getSbuuid().equals(sbxxDO.getSbuuid())) {
                    sbxxList.forEach(sbxxNewDO -> {
                        if (sbxxDO.getPzxh().equals(sbxxNewDO.getPzxh())) {
                            sbxxNewList.add(sbxxNewDO);
                        }
                    });
                }
            });
            if (!GyUtils.isNull(sbbNewList) && !GyUtils.isNull(sbxxNewList)) {
                //判断一条是否作废
                if (sbbNewList.size() == 1) {
                    if ("1".equals(sbbNewList.get(0).getGzlxDm1()) && "Y".equals(sbbNewList.get(0).getZfbz1())){
                        //更新为作废
                        this.updateSbrwzt("2", sbbNewList.get(0), refreshSbrwZtReqDTO);
                    }else{
                        //更新为申报成功
                        this.updateSbrwzt("1", sbbList.get(0), refreshSbrwZtReqDTO);
                    }

                } else {
                    //多于1条的情况下，需要判断作废情况和更正情况
                    this.checkGzOrZf(sbbNewList, refreshSbrwZtReqDTO);
                }
            }
        } else {

        }*/

        //没有申报uuid的情况，需要考虑申报成功，作废和更正状态
        //申报成功（sbblist只有一条gzlxDm为1的数据）
        if (sbbList.size() == 1 && "1".equals(sbbList.get(0).getGzlxDm1())) {
            //乐企只会返回一条数据，不会返回作废的数据
            //只区分更正成功和申报成功即可
            if ("2".equals(sbbList.get(0).getGzlxDm1()) || "4".equals(sbbList.get(0).getGzlxDm1()) || "5".equals(sbbList.get(0).getGzlxDm1())){
                //更正成功
                //更新为更正
                this.updateSbrwzt("3", sbbList.get(0), refreshSbrwZtReqDTO, sbxxList);
            }
            if ("1".equals(sbbList.get(0).getGzlxDm1()) || "3".equals(sbbList.get(0).getGzlxDm1())){
                //申报成功
                this.updateSbrwzt("1", sbbList.get(0), refreshSbrwZtReqDTO, sbxxList);
            }
            //只需判断是否作废即可
            /*if ("Y".equals(sbbList.get(0).getZfbz1())) {
                //更新为作废
                this.updateSbrwzt("2", sbbList.get(0), refreshSbrwZtReqDTO, sbxxList);
            } else {
                //更新为申报成功
                this.updateSbrwzt("1", sbbList.get(0), refreshSbrwZtReqDTO, sbxxList);
            }*/
        } else {
            //多于1条的情况下，需要判断作废情况和更正情况
            this.checkGzOrZf(sbbList, refreshSbrwZtReqDTO, sbxxList);
        }

    }

    private void checkGzOrZf(List<SbSbbDO> sbbList, RefreshSbrwZtReqDTO refreshSbrwZtReqDTO, List<SbSbxxDO> sbSbxxDOList) {
        SbSbbDO sbbDOOne = sbbList.stream().filter(t -> "5".equals(t.getGzlxDm1())).findAny().orElse(null);
        if (!GyUtils.isNull(sbbDOOne)) {
            if ("Y".equals(sbbDOOne.getZfbz1())) {
                //更新为作废
                this.updateSbrwzt("2", sbbDOOne, refreshSbrwZtReqDTO, sbSbxxDOList);
            } else {
                //更新为更正
                this.updateSbrwzt("3", sbbDOOne, refreshSbrwZtReqDTO, sbSbxxDOList);
            }
        }
    }

    private void updateSbrwzt(String s, SbSbbDO sbSbbDO, RefreshSbrwZtReqDTO refreshSbrwZtReqDTO, List<SbSbxxDO> sbSbxxDOList) {
        if ("1".equals(s)) {
            //申报成功
            znsbNssbSbrwService.refreshSbrwZt(refreshSbrwZtReqDTO.getSbrwuuid(),
                    sbSbbDO.getYbtse(),
                    sbSbbDO.getSbuuid(),
                    SbrwztConstants.SBZT_SBCG_DM,
                    DateUtils.toDate(sbSbbDO.getSbrq1()),
                    String.valueOf(sbSbbDO.getPzxh()),
                    SbrwztConstants.RWZT_YSB_DM);
            //更新缴款状态
            znsbSkjnService.jkxxTb(refreshSbrwZtReqDTO.getDjxh(),sbSbbDO.getPzxh());
            this.getsbmxxx(sbSbbDO, refreshSbrwZtReqDTO, sbSbxxDOList);

        }
        if ("2".equals(s)) {
            //作废
            znsbNssbSbrwService.refreshSbrwZt(refreshSbrwZtReqDTO.getSbrwuuid(),
                    null,
                    "",
                    SbrwztConstants.SBZT_ZF_ZFCG_DM,
                    null,
                    "",
                    SbrwztConstants.RWZT_WSB_DM);
        }
        if ("3".equals(s)) {
            //更正
            znsbNssbSbrwService.refreshSbrwZt(refreshSbrwZtReqDTO.getSbrwuuid(),
                    sbSbbDO.getYbtse(),
                    sbSbbDO.getSbuuid(),
                    SbrwztConstants.SBZT_CWGZ_GZCG_DM,
                    DateUtils.toDate(sbSbbDO.getSbrq1()),
                    String.valueOf(sbSbbDO.getPzxh()),
                    SbrwztConstants.RWZT_YSB_DM);
            //更新缴款状态
            znsbSkjnService.jkxxTb(refreshSbrwZtReqDTO.getDjxh(),sbSbbDO.getPzxh());
            this.getsbmxxx(sbSbbDO, refreshSbrwZtReqDTO, sbSbxxDOList);

        }
    }

    private void getsbmxxx(SbSbbDO sbSbbDO, RefreshSbrwZtReqDTO refreshSbrwZtReqDTO, List<SbSbxxDO> sbSbxxDOList) {
        //是否乐企企业
        if ("Y".equals(CacheUtils.getXtcs("QYD_LQQY"))) {
            //归集sbb数据后归集申报明细数据，目前只支持归集乐企提供的税种，取配置发起数据交换
            Map<String, Object> lqszMap = this.getLqsz(refreshSbrwZtReqDTO);
            //如果不是乐企支持税种不处理
            if (!GyUtils.isNull(lqszMap)) {
                sbrwRefreshZtService.refreshSbrwztGetsbmxxx(sbSbbDO,refreshSbrwZtReqDTO,sbSbxxDOList,lqszMap);
            }
        }

    }

    private void saveSbxx(List<SbSbbDO> sbbList, List<SbSbxxDO> sbxxList) {
        List<SbxxDTO> sbb = BeanUtils.toBean(sbbList, SbxxDTO.class);
        List<SbmxxxDTO> sbmx = BeanUtils.toBean(sbxxList, SbmxxxDTO.class);
        List<String> pzxhList = new ArrayList<>();
        sbbList.forEach(sbbDO -> {
            pzxhList.add(sbbDO.getPzxh());
            sbb.forEach(sbbDTO -> {
                if (sbbDO.getSbuuid().equals(sbbDTO.getSbuuid())) {
                    sbbDTO.setSbsxDm(sbbDO.getSbsxDm1());
                }
            });
        });
        sbqkcxSbxxService.handlerSbbData(pzxhList,sbb);
        sbqkcxSbxxmxService.handlerSbxxData(pzxhList,sbmx);
    }

    @PostMapping("/deleteSbrw")
    @Operation(summary = "申报任务明细-税费申报删除申报任务")
    public CommonResult<String> deleteSbrw(@RequestBody DeleteSbrwReqDTO deleteSbrwReqDTO) {
        znsbNssbSbrwService.deleteBySbrwuuid(deleteSbrwReqDTO.getSbrwuuid());
        return CommonResult.success("成功");
    }

    @PostMapping("/addSbrwSave")
    @Operation(summary = "申报任务明细-税费申报添加报表保存")
    public CommonResult<String> addSbrwSave(@RequestBody AddSbrwSaveReqDTO addSbrwSaveReqDTO) {
        //先根据djxh和nsrsbh查询纳税人信息
        CommonResult<CompanyBasicInfoDTO> qyxxRes = companyApi.basicInfo(addSbrwSaveReqDTO.getDjxh(), addSbrwSaveReqDTO.getNsrsbh());
        // 调用办税员列表获取办税员
        String bsymc = StringUtils.EMPTY;
        DjxhReqVO bsyreqVO = new DjxhReqVO();
        bsyreqVO.setDjxh(addSbrwSaveReqDTO.getDjxh());
        CommonResult<List<RyxxVO>> ryxxResult = companyApi.getBsyByQy(bsyreqVO);
        if (!GyUtils.isNull(ryxxResult.getData())) {
            List<RyxxVO> ryxxList = ryxxResult.getData();
            if (!GyUtils.isNull(ryxxList)) {
                bsymc = ryxxList.get(0).getZsxm1();
            }
        }
        //肯定能查到，所以不判空了
        final String nsrmc = qyxxRes.getData().getNsrmc();
        final String xzqhszDm = qyxxRes.getData().getXzqhszDm();
        ZnsbNssbSbrwDO znsbNssbSbrwDO = new ZnsbNssbSbrwDO();
        znsbNssbSbrwDO.setSbrwuuid(GyUtils.getUuid());
        znsbNssbSbrwDO.setDjxh(addSbrwSaveReqDTO.getDjxh());
        znsbNssbSbrwDO.setNsrsbh(addSbrwSaveReqDTO.getNsrsbh());
        znsbNssbSbrwDO.setNsrmc(nsrmc);
        znsbNssbSbrwDO.setYzpzzlDm(addSbrwSaveReqDTO.getYzpzzlDm());
        znsbNssbSbrwDO.setSkssqq(DateUtils.toDate(addSbrwSaveReqDTO.getSkssqq(), "yyyy-MM-dd"));
        znsbNssbSbrwDO.setSkssqz(DateUtils.toDate(addSbrwSaveReqDTO.getSkssqz(), "yyyy-MM-dd"));
        znsbNssbSbrwDO.setNsqxDm(addSbrwSaveReqDTO.getNsqxDm());
        znsbNssbSbrwDO.setSbny(DateUtils.getSystemCurrentTime(17));
        znsbNssbSbrwDO.setNsrsbztDm(SbrwztConstants.SBZT_WSB_DM);
        znsbNssbSbrwDO.setRwztDm(SbrwztConstants.RWZT_WSB_DM);
        znsbNssbSbrwDO.setRwlxDm(addSbrwSaveReqDTO.getRwlxDm());
        znsbNssbSbrwDO.setXzqhszDm(xzqhszDm);
        znsbNssbSbrwDO.setZdyBz("1");
        znsbNssbSbrwDO.setBsy(bsymc);
        if (!GyUtils.isNull(addSbrwSaveReqDTO.getZsxmDm())){
            znsbNssbSbrwDO.setZsxmDm(addSbrwSaveReqDTO.getZsxmDm());
        }else{
            znsbNssbSbrwDO.setZsxmDm(ZsxmDmEnum.matchZsxmDmByYzpzzlDm(addSbrwSaveReqDTO.getYzpzzlDm()));
        }
        znsbNssbSbrwDO.setZspmDm(addSbrwSaveReqDTO.getZspmDm());
        znsbNssbSbrwService.save(znsbNssbSbrwDO);
        return CommonResult.success("成功");
    }

    @PostMapping("/addSbrwInit")
    @Operation(summary = "申报任务明细-税费申报添加报表初始化")
    public CommonResult<AddSbrwInitRespDTO> addSbrwInit(@RequestBody AddSbrwInitReqDTO addSbrwInitReqDTO) {
        AddSbrwInitRespDTO addSbrwInitRespDTO = new AddSbrwInitRespDTO();
        log.info("申报任务明细-税费申报添加报表初始化");
        //查缓存加载下拉选内容
        //任务类型代码
        List<Map<String, Object>> rwlxDmResList = new ArrayList<>();
        List<Map<String, Object>> rwlxDmList = CacheUtils.getTableData("ZDY_DM_SBRW_RWLX");
        if (!GyUtils.isNull(rwlxDmList)) {
            rwlxDmList.forEach(map -> {
                Map<String, Object> resMap = new HashMap<>();
                resMap.put("rwlxDm", map.get("code"));
                resMap.put("rwlxmc", map.get("value"));
                rwlxDmResList.add(resMap);
            });
        }
        addSbrwInitRespDTO.setRwlxDmList(rwlxDmResList);

        List<Map<String, Object>> yzpzzlResList = new ArrayList<>();
        //根据是否传入rwlxDm获取缓存表数据
        if (GyUtils.isNull(addSbrwInitReqDTO.getRwlxDm())) {
            //未传入则全量获取
            List<Map<String, Object>> resListMap = CacheUtils.getTableData("cs_znsb_addsbrw_zdy");
            if (!GyUtils.isNull(resListMap)) {
                resListMap.forEach(map -> {
                    List<Map<String, Object>> dataList = JsonUtils.toMapList(String.valueOf(map.get("dataList")));
                    if (!GyUtils.isNull(dataList)) {
                        dataList.forEach(dataMap -> {
                            Map<String, Object> resMap = new HashMap<>();
                            resMap.put("yzpzzlDm", dataMap.get("yzpzzlDm"));
                            resMap.put("yzpzzlmc", dataMap.get("yzpzzlmc"));
                            yzpzzlResList.add(resMap);
                        });
                    }
                });
            }
        } else {
            //根据rwlxDm获取
            Map<String, Object> resCodeMap = CacheUtils.getTableData("cs_znsb_addsbrw_zdy", addSbrwInitReqDTO.getRwlxDm());
            if (!GyUtils.isNull(resCodeMap)) {
                if (!GyUtils.isNull(resCodeMap.get("dataList"))) {
                    List<Map<String, Object>> yzpzzlList = JsonUtils.toMapList(String.valueOf(resCodeMap.get("dataList")));
                    if (!GyUtils.isNull(yzpzzlList)) {
                        yzpzzlList.forEach(map -> {
                            Map<String, Object> resMap = new HashMap<>();
                            resMap.put("yzpzzlDm", map.get("yzpzzlDm"));
                            resMap.put("yzpzzlmc", map.get("yzpzzlmc"));
                            yzpzzlResList.add(resMap);
                        });
                    }
                }
            }

        }
        addSbrwInitRespDTO.setYzpzzlDmList(yzpzzlResList);
        return CommonResult.success(addSbrwInitRespDTO);
    }

    @PostMapping("/getRwlxDmByYzpzzlDm")
    @Operation(summary = "申报任务明细-税费申报添加报表获取任务类型代码")
    public CommonResult<AddSbrwGetRwlxDmDTO> getRwlxDmByYzpzzlDm(@RequestBody AddSbrwGetRwlxDmDTO addSbrwGetRwlxDmDTO) {
        //根据yzpzzlDm获取对应rwlxDm
        List<Map<String, Object>> resListMap = CacheUtils.getTableData("cs_znsb_addsbrw_zdy");
        if (!GyUtils.isNull(resListMap)) {
            resListMap.forEach(map -> {
                List<Map<String, Object>> dataList = JsonUtils.toMapList(String.valueOf(map.get("dataList")));
                String code = String.valueOf(map.get("code"));
                if (!GyUtils.isNull(dataList)) {
                    dataList.forEach(dataMap -> {
                        if (addSbrwGetRwlxDmDTO.getYzpzzlDm().equals(dataMap.get("yzpzzlDm"))){
                            addSbrwGetRwlxDmDTO.setRwlxDm(code);
                        }
                    });
                }
            });
        }
        return CommonResult.success(addSbrwGetRwlxDmDTO);
    }

    @PostMapping("/cwbbbsrwInit")
    @Operation(summary = "申报任务明细-财务报表报送任务初始化")
    public CommonResult<List<SbrwmxInitRespDTO>> cwbbbsrwInit(@RequestBody SbrwmxInitReqDTO sbrwmxInitReqDTO){
        if (!GyUtils.isNull(sbrwmxInitReqDTO.getSbnyList())){
            List<String> sbnyNewList = new ArrayList<>();
            sbnyNewList.add(sbrwmxInitReqDTO.getSbnyList().get(0).replace("-", ""));
            sbnyNewList.add(sbrwmxInitReqDTO.getSbnyList().get(1).replace("-", ""));
            sbrwmxInitReqDTO.setSbnyList(sbnyNewList);
        }
        List<SbrwmxInitRespDTO> respList = BeanUtils.toBean(znsbNssbSbrwCwbbService.cwbbbsrwInit(sbrwmxInitReqDTO), SbrwmxInitRespDTO.class);
        if (!GyUtils.isNull(respList)) {
            this.setCwbbbsrwInitResp(respList);
        }
        return CommonResult.success(respList);
    }

    private void setCwbbbsrwInitResp(List<SbrwmxInitRespDTO> respList) {

        int xh = 1;
        for (SbrwmxInitRespDTO dto : respList) {
            //代码转名称
            //任务类型代码获取任务类型名称
            dto.setBbfl(CacheUtils.dm2mc("ZDY_DM_SBRW_RWLX", dto.getRwlxDm()));
            //应征凭证种类代码获取报表名称
            //获取资料小类代码对应的名称，组装报表名称
            dto.setBbmc("财务报表报送("+ CwbbZlxlEnum.getCwbbZlxlByDm(dto.getZlxlDm()).getMc() + ")");
            //任务状态名称
            dto.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", dto.getRwztDm()));
            //纳税人申报状态名称ZDY_DM_SBRW_SBZT
            dto.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", dto.getNsrsbztDm()));

            //取配置去申报按钮跳转路径  系统异常重新提交的路径
            Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", dto.getYzpzzlDm());
            if (GyUtils.isNull(qsbMap)) {
                dto.setActionQsb("");
                dto.setActionError("");
            } else {
                dto.setActionQsb(String.valueOf(qsbMap.get("caption")));
                dto.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
            }

            dto.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

            dto.setXh(xh);
            xh++;
        }
    }

    @PostMapping("/lbljrwInit")
    @Operation(summary = "申报任务明细-漏报漏缴任务初始化")
    public CommonResult<List<SbrwmxInitRespDTO>> lbljrwInit(@RequestBody SbrwmxInitReqDTO sbrwmxInitReqDTO) {
        log.info("申报任务明细-漏报漏缴任务初始化");
        //根据纳税人识别号取全部申报年月为当前年月的申报任务数据
        List<SbrwmxInitRespDTO> resplist = BeanUtils.toBean(znsbNssbSbrwService.lbljrwInit(sbrwmxInitReqDTO), SbrwmxInitRespDTO.class);
        if (!GyUtils.isNull(resplist)) {
            //获取征期列表
            CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
            log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
            //获取征期时间
            List<GszqxxVO> zqList = zqRes.getData();
            this.setSbrwmxInitResp(resplist, zqList);
        }
        //排序
        if (!GyUtils.isNull(resplist)){
            resplist = resplist.stream()
                    .sorted(Comparator.comparing(SbrwmxInitRespDTO::getRwlxDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getXh)
                            .thenComparing(SbrwmxInitRespDTO::getZsxmDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getYzpzzlDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getRwztDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getNsqxDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getSkssqq, Comparator.nullsLast(Date::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getFybh, Comparator.nullsLast(String::compareTo))
                    )
                    .collect(Collectors.toList());
        }
        return CommonResult.success(resplist);
    }

    @PostMapping("/sbrwmxInit")
    @Operation(summary = "申报任务明细-税费申报初始化")
    public CommonResult<List<SbrwmxInitRespDTO>> sbrwmxInit(@RequestBody SbrwmxInitReqDTO sbrwmxInitReqDTO) {
        log.info("申报任务明细-税费申报初始化");
        //根据纳税人识别号取全部申报年月为当前年月的申报任务数据
        List<SbrwmxInitRespDTO> resplist = BeanUtils.toBean(znsbNssbSbrwService.sbrwmxInit(sbrwmxInitReqDTO), SbrwmxInitRespDTO.class);
        if (!GyUtils.isNull(resplist)) {
            //获取征期列表
            CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
            log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
            //获取征期时间
            List<GszqxxVO> zqList = zqRes.getData();
            this.setSbrwmxInitResp(resplist, zqList);
        }
        //排序
        if (!GyUtils.isNull(resplist)){
            resplist = resplist.stream()
                    .sorted(Comparator.comparing(SbrwmxInitRespDTO::getRwlxDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getXh)
                            .thenComparing(SbrwmxInitRespDTO::getZsxmDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getYzpzzlDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getRwztDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getNsqxDm, Comparator.nullsLast(String::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getSkssqq, Comparator.nullsLast(Date::compareTo))
                            .thenComparing(SbrwmxInitRespDTO::getFybh, Comparator.nullsLast(String::compareTo))
                    )
                    .collect(Collectors.toList());
        }
        return CommonResult.success(resplist);
    }

    private void setSbrwmxInitResp(List<SbrwmxInitRespDTO> resplist, List<GszqxxVO> zqList) {
        for (SbrwmxInitRespDTO dto : resplist) {
            //代码转名称
            //任务类型代码获取任务类型名称
            dto.setBbfl(CacheUtils.dm2mc("ZDY_DM_SBRW_RWLX", dto.getRwlxDm()));
            //应征凭证种类代码获取报表名称
            dto.setBbmc(GyUtils.isNull(YzpzzlEnum.getYzpzzlByDm(dto.getYzpzzlDm())) ? "" : YzpzzlEnum.getYzpzzlByDm(dto.getYzpzzlDm()).getMc());
            //任务状态名称
            dto.setRwztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_RWZT", dto.getRwztDm()));
            //纳税人申报状态名称ZDY_DM_SBRW_SBZT
            dto.setNsrsbztMc(CacheUtils.dm2mc("ZDY_DM_SBRW_SBZT", dto.getNsrsbztDm()));

            //取配置去申报按钮跳转路径  系统异常重新提交的路径
            Map<String, Object> qsbMap = CacheUtils.getTableData("cs_znsb_sbbtzljdzb", dto.getYzpzzlDm());
            if (GyUtils.isNull(qsbMap)) {
                dto.setActionQsb("");
                dto.setActionError("");
            } else {
                dto.setActionQsb(String.valueOf(qsbMap.get("caption")));
                dto.setActionError(String.valueOf(qsbMap.get("cxtjlj")));
            }

            //取自定义代码表的去理税按钮跳转路径，根据征收项目代码来获取
            dto.setActionQls(CacheUtils.dm2mc("ZDY_DM_SFSB_QLSTZURL", dto.getYzpzzlDm()+dto.getZsxmDm()));

            //增值税预缴需要根据sjcsdq获取税务机关名称(简称)
            Map<String, Object> swjgMap = CacheUtils.getTableData("dm_gy_swjg", dto.getSjcsdq());
            if(!GyUtils.isNull(swjgMap)){
                dto.setSwjgmc(this.getswjgmc(swjgMap));
            }else {
                dto.setSwjgmc("");
            }

            //财行税需要按照征收项目代码组装名称
            if (YzpzzlEnum.CXS.getDm().equals(dto.getYzpzzlDm())) {
                ZsxmDmEnum zsxmDmEnum = ZsxmDmEnum.getZsxmDm(dto.getZsxmDm());
                String zsxmMc = "";
                if (!GyUtils.isNull(zsxmDmEnum)) {
                    zsxmMc = zsxmDmEnum.getName();
                }
                dto.setBbmc(dto.getBbmc() + "（" + zsxmMc + "）");

                //根据纳税期限代码组装名称后缀
                //this.getBbmcByNsqxDm(dto);
            }


            //财务报表需要重新定义名称，根据纳税期限代码将名称加后缀
            if (YzpzzlEnum.CWBB_QYKJZD.getDm().equals(dto.getYzpzzlDm())) {
                dto.setBbmc("财务报表报送与信息采集");
                //根据纳税期限代码组装名称后缀
                //this.getBbmcByNsqxDm(dto);
            }

            //企业所得税月季报重定义名称
            if (YzpzzlEnum.QYSDSCZZS.getDm().equals(dto.getYzpzzlDm())) {
                dto.setBbmc("居民企业（查账征收）企业所得税月（季）度申报");
            }

            //企业所得税年报重定义名称
            if (YzpzzlEnum.QYSDS_NDA.getDm().equals(dto.getYzpzzlDm())) {
                dto.setBbmc("居民企业（查账征收）企业所得税年度申报");
            }

            //通用申报根据征收项目代码组装报表名称
            if (YzpzzlEnum.FSSR_TYSB.getDm().equals(dto.getYzpzzlDm())) {
                if (JSXZSYXSFSR.getCode().equals(dto.getZsxmDm())){
                    dto.setBbmc("非税通用申报（城镇垃圾处理费）");
                }
                if (SLJSJJ.getCode().equals(dto.getZsxmDm())){
                    dto.setBbmc("非税通用申报（水利建设基金）");
                }
            }

            //通用申报
            if (YzpzzlEnum.TYSB.getDm().equals(dto.getYzpzzlDm())) {
                dto.setBbmc("通用申报");
                ZsxmDmEnum zsxmDmEnum = ZsxmDmEnum.getZsxmDm(dto.getZsxmDm());
                String zsxmMc = "";
                if (!GyUtils.isNull(zsxmDmEnum)) {
                    zsxmMc = zsxmDmEnum.getName();
                }
                dto.setBbmc(dto.getBbmc() + "（" + zsxmMc + "）");
            }


            //组装征期时间，资料报送类的不需要
            if (!GyUtils.isNull(zqList) && !SbrwztConstants.RWLX_ZLBS_DM.equals(dto.getRwlxDm())) {
                dto.setZqsj(this.getzqsj(dto,zqList));
            }

            dto.setNowDate(DateUtils.toDate(DateUtils.getSystemCurrentTime(3), "yyyy-MM-dd"));

            dto.setXh(this.getSbrwXh(dto));

            //判断缴款状态，应补退税额小于等于1时设置展示状态为无需缴款
            if (!GyUtils.isNull(dto.getYbtse())){
                if (dto.getYbtse().compareTo(new BigDecimal("1")) <= 0){
                    dto.setSbjkztDm(JKZT_WXJK_DM);
                }
            }
        }

    }

    private String getswjgmc(Map<String, Object> swjgMap) {
        if (GyUtils.isNull(swjgMap)){
            return "";
        }
        //获取此条机关对应的行政区划
        final String xzqhszDm = MapUtil.getStr(swjgMap,"xzqhszDm");
        if (GyUtils.isNull(xzqhszDm)){
            return MapUtil.getStr(swjgMap,"swjgjc");
        }
        //根据行政区划数字代码查询代码表数据
        Map<String, Object> xzqhMap = CacheUtils.getTableData("dm_gy_xzqh", xzqhszDm);
        if (GyUtils.isNull(xzqhMap)){
            return MapUtil.getStr(swjgMap,"swjgjc");
        }
        return MapUtil.getStr(xzqhMap,"ssxzqmc") + "/" + MapUtil.getStr(swjgMap,"swjgjc");
    }

    private int getSbrwXh(SbrwmxInitRespDTO dto) {
        //默认返回值为100  特殊情况从0开始排
        //企业所得税预缴
        if (YzpzzlEnum.QYSDSCZZS.getDm().equals(dto.getYzpzzlDm())){
            return 0;
        }
        //财行税印花税
        if (YzpzzlEnum.CXS.getDm().equals(dto.getYzpzzlDm()) && ZsxmDmEnum.YHS.getCode().equals(dto.getZsxmDm())){
            return 0;
        }
        return 100;
    }

    private Date getzqsj(SbrwmxInitRespDTO dto, List<GszqxxVO> zqList) {
        String xzqhszDm = dto.getXzqhszDm();
        if (GyUtils.isNull(xzqhszDm)){
            //根据djxh和nsrsbh获取
            CommonResult<CompanyBasicInfoDTO> result = companyApi.basicInfo(dto.getDjxh(),dto.getNsrsbh());
            if (!GyUtils.isNull(result.getData())){
                xzqhszDm = result.getData().getXzqhszDm();
            }else{
                return DateUtils.toDate(DateUtils.getSystemCurrentTime(0), "yyyy-MM-dd HH:mm:ss");
            }
        }
        final String finalXzqhszDm = xzqhszDm;
        GszqxxVO zqvo = zqList.stream().filter(vo -> vo.getXzqhszDm().contains(finalXzqhszDm)).findAny().orElse(null);
        //判断税费申报征期控制参数
        final String zqcs = CacheUtils.getXtcs("SBRW-ZQCS");
        if (!GyUtils.isNull(zqvo)) {
            log.info("税费申报获取到的当前征期为：{}", JsonUtils.toJson(zqvo));
            if ("Y".equals(zqcs)){
                return DateUtils.toDate(zqvo.getZqsj(), "yyyy-MM-dd hh:mm:ss");
            }else{
                //返回本月最后一天作为征期
                return this.nowMonthLastDay();
            }

        } else {
            log.info("税费申报初始化未获取到有效的征期参数，传入参数sbny，xzqhszDm为：{}，{}", DateUtils.getSystemCurrentTime(17), finalXzqhszDm);
        }
        return DateUtils.toDate(DateUtils.getSystemCurrentTime(0), "yyyy-MM-dd HH:mm:ss");
    }

    private Date nowMonthLastDay() {
        // 获取当前年份和月份
        YearMonth yearMonth = YearMonth.now();

        // 使用TemporalAdjusters获取当前月的最后一天
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();
        return Date.from(lastDayOfMonth.atStartOfDay().atZone(systemDefaultZoneId).toInstant());
    }

    private void getBbmcByNsqxDm(SbrwmxInitRespDTO dto) {
        if ("06".equals(dto.getNsqxDm())) {
            dto.setBbmc(dto.getBbmc() + "（月报）");
        }
        if ("08".equals(dto.getNsqxDm())) {
            dto.setBbmc(dto.getBbmc() + "（季报）");
        }
        if ("10".equals(dto.getNsqxDm())) {
            dto.setBbmc(dto.getBbmc() + "（年报）");
        }
        if ("11".equals(dto.getNsqxDm())) {
            dto.setBbmc(dto.getBbmc() + "（次报）");
        }
    }

    @PostMapping("/sbrwmxUpdateHlzt")
    @Operation(summary = "申报任务明细-更新忽略状态")
    public CommonResult<String> sbrwmxUpdateHlzt(@RequestBody SbrwmxUpdateHlztReqDTO sbrwmxUpdateHlztReqDTO) {
        log.info("申报任务明细-更新忽略状态");
        znsbNssbSbrwService.sbrwmxUpdateHlzt(sbrwmxUpdateHlztReqDTO);
        return CommonResult.success("成功");
    }

    @PostMapping("/getSession")
    @Operation(summary = "申报任务明细-获取session")
    public CommonResult<SbrwGetSessionRespDTO> getSession() {
        log.info("申报任务明细-获取session");
        SbrwGetSessionRespDTO sbrwGetSessionRespDTO = new SbrwGetSessionRespDTO();
        if (!GyUtils.isNull(ZnsbSessionUtils.getNsrsbh())) {
            sbrwGetSessionRespDTO.setNsrsbh(ZnsbSessionUtils.getNsrsbh());
            sbrwGetSessionRespDTO.setJguuid(ZnsbSessionUtils.getJguuid());
        } else {
            //环境上的session不会为空，所以此设置用于本地测试
            sbrwGetSessionRespDTO.setNsrsbh("91310000MA1FL70BCS");
            sbrwGetSessionRespDTO.setJguuid("5f5dd426d8ce411db5ccfb99a2490e0c");
        }
        return CommonResult.success(sbrwGetSessionRespDTO);
    }

    @PostMapping("/queryBdjgmx")
    @Operation(summary = "申报任务明细-获取比对结果明细")
    public CommonResult<List<SBSbbdBdjgmxVO>> queryBdjgmx(@Validated @RequestBody SbBdjgmxReqDTO sbBdjgmxReqDTO) {
        log.info("申报任务明细-获取比对结果明细");
        return CommonResult.success(bdjgService.queryBdjgmx(sbBdjgmxReqDTO));
    }

    @Schema(description = "重置申报任务")
    @PostMapping("/restSbrw")
    public CommonResult<Object> restSbrw() {
        boolean result = sbrwResetService.resetSbrw();
        return CommonResult.success(result);
    }

    @GetMapping("/getCybdbz")
    @Operation(summary = "获取差异比对标志")
    public CommonResult<String> getCybdbz() {
        log.info("获取差异比对标志");
        String cybdbz = CacheUtils.getXtcs("PLSB_TZCYBDBZ", "Y");
        return CommonResult.success(cybdbz);
    }

    @PostMapping("/queryZzsybnsrSbrwmx")
    @Operation(summary = "增值税一般纳税人-申报任务明细-查询")
    public CommonResult<ZzsybnsrSbrwmxInitRespDTO> queryZzsybnsrSbrwmx(@Validated @RequestBody ZzsybnsrSbrwmxInitReqDTO zzsybnsrSbrwmxInitReqDTO) {
        ZzsybnsrSbrwmxInitRespDTO zzsybnsrSbrwmxInitRespDTO = new ZzsybnsrSbrwmxInitRespDTO();
        //增值税一般纳税人
        if (YzpzzlEnum.ZZS_YBNSR.getDm().equals(zzsybnsrSbrwmxInitReqDTO.getYzpzzlDm())
        && ZsxmDmEnum.ZZS.getCode().equals(zzsybnsrSbrwmxInitReqDTO.getZsxmDm())){
            zzsybnsrSbrwmxInitRespDTO = znsbNssbSbrwService.queryZzsybnsrSbrwmx(zzsybnsrSbrwmxInitReqDTO);
        }
        //增值税小规模
        if (YzpzzlEnum.ZZS_XGMNSR.getDm().equals(zzsybnsrSbrwmxInitReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.ZZS.getCode().equals(zzsybnsrSbrwmxInitReqDTO.getZsxmDm())){
            zzsybnsrSbrwmxInitRespDTO = znsbNssbSbrwService.queryZzsxgmnsrSbrwmx(zzsybnsrSbrwmxInitReqDTO);
        }
        //增值税预缴
        if (YzpzzlEnum.ZZS_YJSB.getDm().equals(zzsybnsrSbrwmxInitReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.ZZS.getCode().equals(zzsybnsrSbrwmxInitReqDTO.getZsxmDm())){
            zzsybnsrSbrwmxInitRespDTO = znsbNssbSbrwService.queryZzsyjSbrwmx(zzsybnsrSbrwmxInitReqDTO);
        }
        //印花税
        if (YzpzzlEnum.CXS.getDm().equals(zzsybnsrSbrwmxInitReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.YHS.getCode().equals(zzsybnsrSbrwmxInitReqDTO.getZsxmDm())){
            zzsybnsrSbrwmxInitRespDTO = znsbNssbSbrwService.queryYhsSbrwmx(zzsybnsrSbrwmxInitReqDTO);
        }
        //房产税
        if (YzpzzlEnum.CXS.getDm().equals(zzsybnsrSbrwmxInitReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.FCS.getCode().equals(zzsybnsrSbrwmxInitReqDTO.getZsxmDm())){
            zzsybnsrSbrwmxInitRespDTO = znsbNssbSbrwService.queryFcsSbrwmx(zzsybnsrSbrwmxInitReqDTO);
        }
        //城镇土地使用税
        if (YzpzzlEnum.CXS.getDm().equals(zzsybnsrSbrwmxInitReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.CZTDSYS.getCode().equals(zzsybnsrSbrwmxInitReqDTO.getZsxmDm())){
            zzsybnsrSbrwmxInitRespDTO = znsbNssbSbrwService.queryCztdsysSbrwmx(zzsybnsrSbrwmxInitReqDTO);
        }
        //企业所得税预缴
        if (YzpzzlEnum.QYSDSCZZS.getDm().equals(zzsybnsrSbrwmxInitReqDTO.getYzpzzlDm())
                && ZsxmDmEnum.QYSDS.getCode().equals(zzsybnsrSbrwmxInitReqDTO.getZsxmDm())){
            zzsybnsrSbrwmxInitRespDTO = znsbNssbSbrwService.queryQysdsyjSbrwmx(zzsybnsrSbrwmxInitReqDTO);
        }
        //财务报表
        if (YzpzzlEnum.CWBBBS.getDm().equals(zzsybnsrSbrwmxInitReqDTO.getYzpzzlDm())
                && "00000".equals(zzsybnsrSbrwmxInitReqDTO.getZsxmDm())){
            zzsybnsrSbrwmxInitRespDTO = znsbNssbSbrwService.queryCwbbSbrwmx(zzsybnsrSbrwmxInitReqDTO);
        }
        return CommonResult.success(zzsybnsrSbrwmxInitRespDTO);
    }

    @PostMapping("/sbrwmxAbbPlsb")
    @Operation(summary = "申报任务明细-按报表-批量申报")
    public CommonResult<SbrwmxAbbPlsbRespDTO> sbrwmxAbbPlsb(@Validated @RequestBody SbrwmxAbbPlsbReqDTO sbrwmxAbbPlsbReqDTO) {
        return CommonResult.success(znsbNssbSbrwService.sbrwmxAbbPlsb(sbrwmxAbbPlsbReqDTO));
    }

    @PostMapping("/initYsSbrw")
    @Operation(summary = "初始化演示申报任务")
    public CommonResult<InitYsSbrwRespDTO> initYsSbrw(@Validated @RequestBody InitYsSbrwReqDTO initYsSbrwReqDTO) {
        return CommonResult.success(znsbNssbSbrwService.initYsSbrw(initYsSbrwReqDTO));
    }

    @PostMapping("/updateJkzt")
    @Operation(summary = "更新申报任务缴款状态")
    public CommonResult<UpdateSbrwJkztRespDTO> updateJkzt(@Validated @RequestBody UpdateSbrwJkztReqDTO updateSbrwJkztReqDTO) {
        return CommonResult.success(znsbNssbSbrwService.updateSbrwJkzt(updateSbrwJkztReqDTO));
    }

    @PostMapping("/initQcxxBySbrwuuid")
    @Operation(summary = "期初信息归BY申报任务uuid单条")
    public CommonResult<String> initQcxxBySbrwuuid(@Validated @RequestBody InitQcxxBySbrwuuidReqDTO initQcxxBySbrwuuidReqDTO) {
        return CommonResult.success(znsbNssbSbrwService.initQcxxBySbrwuuid(initQcxxBySbrwuuidReqDTO));
    }

    @PostMapping("/plyyjk")
    @Operation(summary = "批量预约缴款")
    public CommonResult<Map<String, Object>> plyyjk(@RequestBody SbrwmxAbbPlsbReqDTO sbrwmxAbbPlsbReqDTO) {
        return CommonResult.success(znsbNssbSbrwService.plyyjk(sbrwmxAbbPlsbReqDTO));
    }

}
