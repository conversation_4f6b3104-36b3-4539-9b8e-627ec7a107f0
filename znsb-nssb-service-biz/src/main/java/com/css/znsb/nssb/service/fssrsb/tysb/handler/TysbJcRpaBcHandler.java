package com.css.znsb.nssb.service.fssrsb.tysb.handler;

import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.sjjh.pojo.domain.ZnsbNssbSjjhDO;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhHandlerDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhHandler;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.dto.jcyRpa.JcyrpaSbSaveQueryDataRespDTO;
import com.css.znsb.nssb.pojo.dto.jcyRpa.JcyrpaSbSaveQueryRespDTO;
import com.css.znsb.nssb.pojo.vo.fssrsb.tysb.TysbBcReqVO;
import com.css.znsb.nssb.pojo.vo.fssrsb.tysb.TysbBdhjxxVO;
import com.css.znsb.nssb.service.fssrsb.tysb.TysbService;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

import static com.css.znsb.nssb.constants.SbrwztConstants.*;

@Slf4j
@Service(value = "TysbJcRpaBcHandler")
public class TysbJcRpaBcHandler implements SjjhHandler {

    /**
     * 申报任务
     */
    @Resource
    private ZnsbNssbSbrwService sbrwService;

    @Resource
    private TysbService tysbService;

    @Override
    public void before(SjjhHandlerDTO sjjhHandlerDTO){

    }

    @Override
    @Transactional
    public void after(SjjhHandlerDTO sjjhHandlerDTO) {
        // 更新申报任务表状态
        final String sbrwuuid = sjjhHandlerDTO.getSjjhDTO().getYwuuid();
        final TysbBcReqVO bcReqVO = JsonUtils.toBean(sjjhHandlerDTO.getSjjhDTO().getBwnr(), TysbBcReqVO.class);
        final TysbBdhjxxVO tysbbHj = bcReqVO.getTysbbHj();
        // 查询申报任务
        final ZnsbNssbSbrwDO sbrw = sbrwService.getById(sbrwuuid);
        // 更新申报状态
        sbrw.setNsrsbztDm(SBZT_SBZ_DM);
        sbrw.setYbtse(BigDecimal.valueOf(tysbbHj.getBqynseHj()));
        sbrwService.updateById(sbrw);
    }

    @Override
    @Transactional
    public void successAfter(ZnsbNssbSjjhDO sjjhDO, Object obj) {
        // 更新申报任务表状态
        final String sbrwuuid = sjjhDO.getYwuuid();
        // 查询申报任务
        final ZnsbNssbSbrwDO sbrw = sbrwService.getById(sbrwuuid);
        // 更新申报状态
        sbrw.setNsrsbztDm(SBZT_SBCG_DM);
        sbrw.setRwztDm(RWZT_YSB_DM);
        sbrwService.updateById(sbrw);

        tysbService.tysbcssbjg(sjjhDO, obj);
        sbrwService.sendSbcgxxzx(sbrwuuid);
    }

    @Override
    @Transactional
    public void failAfter(ZnsbNssbSjjhDO sjjhDO, Object obj) {
        // 更新申报任务表状态
        final String sbrwuuid = sjjhDO.getYwuuid();
        JcyrpaSbSaveQueryRespDTO jcyrpaSbSaveQueryRespDTO = (JcyrpaSbSaveQueryRespDTO) obj;
        JcyrpaSbSaveQueryDataRespDTO data = jcyrpaSbSaveQueryRespDTO.getData();

        // 查询申报任务
        final ZnsbNssbSbrwDO sbrw = sbrwService.getById(sbrwuuid);
        // 更新申报状态
        sbrw.setNsrsbztDm(SBZT_SBSB_DM);
        sbrw.setSbyysm(data.getStatusMsg());
        sbrwService.updateById(sbrw);
        sbrwService.sendSbsbxxzx(sbrwuuid);
    }
}
