package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 海关完税凭证抵扣联数据Grid
 *
 * <p>hgwspzdklsjGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="hgwspzdklsjGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="hgwspzdklsjGridlb" type="{http://www.chinatax.gov.cn/dataspec/}hgwspzdklsjGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hgwspzdklsjGrid", propOrder = { "hgwspzdklsjGridlb" })
public class HgwspzdklsjGrid {
    @XmlElement(nillable = true, required = true)
    protected HgwspzdklsjGridlbVO hgwspzdklsjGridlb;

    /**
     * 获取hgwspzdklsjGridlb属性的值。
     */
    public HgwspzdklsjGridlbVO getHgwspzdklsjGridlb() {
        return hgwspzdklsjGridlb;
    }

    /**
     * 设置hgwspzdklsjGridlb属性的值。
     */
    public void setHgwspzdklsjGridlb(HgwspzdklsjGridlbVO value) {
        this.hgwspzdklsjGridlb = value;
    }
}