package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《本期抵扣进项税额结构明细表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_bqdkjxsejgmxb", propOrder = { "sbbhead", "bqdkjxsejgmxbGrid" })
@Getter
@Setter
public class ZzssyyybnsrBqdkjxsejgmxb {
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 本期抵扣进项税额结构明细表
     */
    @XmlElement(nillable = true, required = true)
    protected BqdkjxsejgmxbGrid bqdkjxsejgmxbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "bqdkjxsejgmxbGridlbVO" })
    @Getter
    @Setter
    public static class BqdkjxsejgmxbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<BqdkjxsejgmxbGridlbVO> bqdkjxsejgmxbGridlbVO;

        /**
         * Gets the value of the bqdkjxsejgmxbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the bqdkjxsejgmxbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getBqdkjxsejgmxbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link BqdkjxsejgmxbGridlbVO}
         */
        public List<BqdkjxsejgmxbGridlbVO> getBqdkjxsejgmxbGridlbVO() {
            if (bqdkjxsejgmxbGridlbVO == null) {
                bqdkjxsejgmxbGridlbVO = new ArrayList<BqdkjxsejgmxbGridlbVO>();
            }
            return this.bqdkjxsejgmxbGridlbVO;
        }
    }
}