package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 取得进项税额情况
 *
 * <p>qdjxseGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="qdjxseGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="qdjxseqkGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}qdjxseGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "qdjxseGrid", propOrder = { "qdjxseqkGridlbVO" })
public class QdjxseGrid {
    /**
     * 取得进项税额情况
     */
    @XmlElement(nillable = true, required = true)
    protected QdjxseGridlbVO qdjxseqkGridlbVO;

    /**
     * 获取qdjxseqkGridlbVO属性的值。
     * <p>
     * 取得进项税额情况
     */
    public QdjxseGridlbVO getQdjxseqkGridlbVO() {
        return qdjxseqkGridlbVO;
    }

    /**
     * 设置qdjxseqkGridlbVO属性的值。
     */
    public void setQdjxseqkGridlbVO(QdjxseGridlbVO value) {
        this.qdjxseqkGridlbVO = value;
    }
}