package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 已缴纳增值税情况
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dxyjnzzsqkGridlbVO", propOrder = { "ewbhxh", "xm1", "xse", "yskje", "yzl", "yjse1", "cbxse", "sysl", "cbse", "bz" })
@Getter
@Setter
public class DxyjnzzsqkGridlbVO {
    /**
     * 二维表行序号
     */
    protected Long ewbhxh;

    /**
     * 项目
     */
    protected String xm1;

    /**
     * 销售额
     */
    protected BigDecimal xse;

    /**
     * 预收款金额
     */
    protected BigDecimal yskje;

    /**
     * 预征率
     */
    protected BigDecimal yzl;

    /**
     * 预缴税额
     */
    protected BigDecimal yjse1;

    /**
     * 查补销售额
     */
    protected BigDecimal cbxse;

    /**
     * 适用税率
     */
    protected BigDecimal sysl;

    /**
     * 查补税额
     */
    protected BigDecimal cbse;

    /**
     * 备注
     */
    protected String bz;
}