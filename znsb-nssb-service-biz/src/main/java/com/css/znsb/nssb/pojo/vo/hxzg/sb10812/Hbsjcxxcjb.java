
package com.css.znsb.nssb.pojo.vo.hxzg.sb10812;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 环境保护税基础信息采集表
 * 
 * <p>Hbsjcxxcjb complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="Hbsjcxxcjb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="gfzxbz" type="{http://www.chinatax.gov.cn/dataspec/}gfzxbz" minOccurs="0"/>
 *         &lt;element name="tbrxm" type="{http://www.chinatax.gov.cn/dataspec/}tbrxm" minOccurs="0"/>
 *         &lt;element name="zfrq1" type="{http://www.chinatax.gov.cn/dataspec/}zfrq1" minOccurs="0"/>
 *         &lt;element name="pwxkzbh" type="{http://www.chinatax.gov.cn/dataspec/}pwxkzbh" minOccurs="0"/>
 *         &lt;element name="gjhbjgDm" type="{http://www.chinatax.gov.cn/dataspec/}gjhbjgDm" minOccurs="0"/>
 *         &lt;element name="zfrDm" type="{http://www.chinatax.gov.cn/dataspec/}zfrDm" minOccurs="0"/>
 *         &lt;element name="zfbz1" type="{http://www.chinatax.gov.cn/dataspec/}zfbz1" minOccurs="0"/>
 *         &lt;element name="lxdh" type="{http://www.chinatax.gov.cn/dataspec/}lxdh" minOccurs="0"/>
 *         &lt;element name="acsb" type="{http://www.chinatax.gov.cn/dataspec/}acsb" minOccurs="0"/>
 *         &lt;element name="jdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}jdxzDm" minOccurs="0"/>
 *         &lt;element name="zgswjgDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswjgDm" minOccurs="0"/>
 *         &lt;element name="sfcycycsfjsbz" type="{http://www.chinatax.gov.cn/dataspec/}sfcycycsfjsbz" minOccurs="0"/>
 *         &lt;element name="sfsshljjzclcs" type="{http://www.chinatax.gov.cn/dataspec/}sfsshljjzclcs" minOccurs="0"/>
 *         &lt;element name="sfqdpwxkzbz" type="{http://www.chinatax.gov.cn/dataspec/}sfqdpwxkzbz" minOccurs="0"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="sfscxwsjzclcs" type="{http://www.chinatax.gov.cn/dataspec/}sfscxwsjzclcs" minOccurs="0"/>
 *         &lt;element name="hbsjcxxuuid" type="{http://www.chinatax.gov.cn/dataspec/}hbsjcxxuuid"/>
 *         &lt;element name="zgswskfjDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswskfjDm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq" minOccurs="0"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm" minOccurs="0"/>
 *         &lt;element name="sfczhygc" type="{http://www.chinatax.gov.cn/dataspec/}sfczhygc" minOccurs="0"/>
 *         &lt;element name="jbr" type="{http://www.chinatax.gov.cn/dataspec/}jbr" minOccurs="0"/>
 *         &lt;element name="sjtbSj" type="{http://www.chinatax.gov.cn/dataspec/}sjtbSj" minOccurs="0"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="tbrq" type="{http://www.chinatax.gov.cn/dataspec/}tbrq" minOccurs="0"/>
 *         &lt;element name="hblxr" type="{http://www.chinatax.gov.cn/dataspec/}hblxr" minOccurs="0"/>
 *         &lt;element name="zywrwlbsDm" type="{http://www.chinatax.gov.cn/dataspec/}zywrwlbsDm" minOccurs="0"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="zzsyhzcmc" type="{http://www.chinatax.gov.cn/dataspec/}zzsyhzcmc"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Hbsjcxxcjb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "gfzxbz",
    "tbrxm",
    "zfrq1",
    "pwxkzbh",
    "gjhbjgDm",
    "zfrDm",
    "zfbz1",
    "lxdh",
    "acsb",
    "jdxzDm",
    "zgswjgDm",
    "sfcycycsfjsbz",
    "sfsshljjzclcs",
    "sfqdpwxkzbz",
    "djxh",
    "sfscxwsjzclcs",
    "hbsjcxxuuid",
    "zgswskfjDm",
    "xgrq",
    "lrrDm",
    "xzqhszDm",
    "sfczhygc",
    "jbr",
    "sjtbSj",
    "sjgsdq",
    "tbrq",
    "hblxr",
    "zywrwlbsDm",
    "xgrDm",
    "lrrq",
    "zzsyhzcmc"
})
public class Hbsjcxxcjb
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String gfzxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tbrxm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfrq1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pwxkzbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String gjhbjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zfbz1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String lxdh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String acsb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jdxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zgswjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfcycycsfjsbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfsshljjzclcs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfqdpwxkzbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfscxwsjzclcs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbsjcxxuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zgswskfjDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xzqhszDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sfczhygc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jbr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sjtbSj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tbrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String hblxr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zywrwlbsDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zzsyhzcmc;

    /**
     * 获取gfzxbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGfzxbz() {
        return gfzxbz;
    }

    /**
     * 设置gfzxbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGfzxbz(String value) {
        this.gfzxbz = value;
    }

    /**
     * 获取tbrxm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTbrxm() {
        return tbrxm;
    }

    /**
     * 设置tbrxm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTbrxm(String value) {
        this.tbrxm = value;
    }

    /**
     * 获取zfrq1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfrq1() {
        return zfrq1;
    }

    /**
     * 设置zfrq1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfrq1(String value) {
        this.zfrq1 = value;
    }

    /**
     * 获取pwxkzbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPwxkzbh() {
        return pwxkzbh;
    }

    /**
     * 设置pwxkzbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPwxkzbh(String value) {
        this.pwxkzbh = value;
    }

    /**
     * 获取gjhbjgDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGjhbjgDm() {
        return gjhbjgDm;
    }

    /**
     * 设置gjhbjgDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGjhbjgDm(String value) {
        this.gjhbjgDm = value;
    }

    /**
     * 获取zfrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfrDm() {
        return zfrDm;
    }

    /**
     * 设置zfrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfrDm(String value) {
        this.zfrDm = value;
    }

    /**
     * 获取zfbz1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZfbz1() {
        return zfbz1;
    }

    /**
     * 设置zfbz1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZfbz1(String value) {
        this.zfbz1 = value;
    }

    /**
     * 获取lxdh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLxdh() {
        return lxdh;
    }

    /**
     * 设置lxdh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLxdh(String value) {
        this.lxdh = value;
    }

    /**
     * 获取acsb属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcsb() {
        return acsb;
    }

    /**
     * 设置acsb属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcsb(String value) {
        this.acsb = value;
    }

    /**
     * 获取jdxzDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJdxzDm() {
        return jdxzDm;
    }

    /**
     * 设置jdxzDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJdxzDm(String value) {
        this.jdxzDm = value;
    }

    /**
     * 获取zgswjgDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswjgDm() {
        return zgswjgDm;
    }

    /**
     * 设置zgswjgDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswjgDm(String value) {
        this.zgswjgDm = value;
    }

    /**
     * 获取sfcycycsfjsbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfcycycsfjsbz() {
        return sfcycycsfjsbz;
    }

    /**
     * 设置sfcycycsfjsbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfcycycsfjsbz(String value) {
        this.sfcycycsfjsbz = value;
    }

    /**
     * 获取sfsshljjzclcs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfsshljjzclcs() {
        return sfsshljjzclcs;
    }

    /**
     * 设置sfsshljjzclcs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfsshljjzclcs(String value) {
        this.sfsshljjzclcs = value;
    }

    /**
     * 获取sfqdpwxkzbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfqdpwxkzbz() {
        return sfqdpwxkzbz;
    }

    /**
     * 设置sfqdpwxkzbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfqdpwxkzbz(String value) {
        this.sfqdpwxkzbz = value;
    }

    /**
     * 获取djxh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * 设置djxh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * 获取sfscxwsjzclcs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfscxwsjzclcs() {
        return sfscxwsjzclcs;
    }

    /**
     * 设置sfscxwsjzclcs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfscxwsjzclcs(String value) {
        this.sfscxwsjzclcs = value;
    }

    /**
     * 获取hbsjcxxuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHbsjcxxuuid() {
        return hbsjcxxuuid;
    }

    /**
     * 设置hbsjcxxuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHbsjcxxuuid(String value) {
        this.hbsjcxxuuid = value;
    }

    /**
     * 获取zgswskfjDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    /**
     * 设置zgswskfjDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswskfjDm(String value) {
        this.zgswskfjDm = value;
    }

    /**
     * 获取xgrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * 设置xgrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * 获取lrrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * 设置lrrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * 获取xzqhszDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }

    /**
     * 设置xzqhszDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhszDm(String value) {
        this.xzqhszDm = value;
    }

    /**
     * 获取sfczhygc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfczhygc() {
        return sfczhygc;
    }

    /**
     * 设置sfczhygc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfczhygc(String value) {
        this.sfczhygc = value;
    }

    /**
     * 获取jbr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJbr() {
        return jbr;
    }

    /**
     * 设置jbr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJbr(String value) {
        this.jbr = value;
    }

    /**
     * 获取sjtbSj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjtbSj() {
        return sjtbSj;
    }

    /**
     * 设置sjtbSj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjtbSj(String value) {
        this.sjtbSj = value;
    }

    /**
     * 获取sjgsdq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * 设置sjgsdq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * 获取tbrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTbrq() {
        return tbrq;
    }

    /**
     * 设置tbrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTbrq(String value) {
        this.tbrq = value;
    }

    /**
     * 获取hblxr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHblxr() {
        return hblxr;
    }

    /**
     * 设置hblxr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHblxr(String value) {
        this.hblxr = value;
    }

    /**
     * 获取zywrwlbsDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZywrwlbsDm() {
        return zywrwlbsDm;
    }

    /**
     * 设置zywrwlbsDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZywrwlbsDm(String value) {
        this.zywrwlbsDm = value;
    }

    /**
     * 获取xgrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * 设置xgrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * 获取lrrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * 设置lrrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * 获取zzsyhzcmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZzsyhzcmc() {
        return zzsyhzcmc;
    }

    /**
     * 设置zzsyhzcmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZzsyhzcmc(String value) {
        this.zzsyhzcmc = value;
    }

}
