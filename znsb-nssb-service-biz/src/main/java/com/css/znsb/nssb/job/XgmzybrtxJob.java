package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.nssb.service.xgmzybrtx.XgmzybrtxService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class XgmzybrtxJob {

    @Resource
    private XgmzybrtxService xgmzybrtxService;

    /**
     * 小规模转一般人提醒
     */
    @XxlJob("XgmzybrtxJob")
    public void execute() {
        log.info("==========开始进行小规模转一般人提醒==========");
        xgmzybrtxService.initJob("");
        log.info("==========小规模转一般人提醒完成==========");
    }

}
