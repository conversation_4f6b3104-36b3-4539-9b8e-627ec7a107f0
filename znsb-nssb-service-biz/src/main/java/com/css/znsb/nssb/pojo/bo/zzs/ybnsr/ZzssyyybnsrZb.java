package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税纳税申报表（一般纳税人适用）》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_zb", propOrder = { "sbbhead", "zbGrid", "slxxForm" })
@Getter
@Setter
public class ZzssyyybnsrZb {
    /**
     * 申报表头信息
     */
    @XmlElement(nillable = true, required = true)
    protected Sbbheadkz1VO sbbhead;

    /**
     * 《增值税纳税申报表（一般纳税人适用）
     */
    @XmlElement(nillable = true, required = true)
    protected ZbGrid zbGrid;

    /**
     * 受理信息
     */
    @XmlElement(nillable = true, required = true)
    protected SlxxForm slxxForm;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "zbGridlbVO" })
    @Getter
    @Setter
    public static class ZbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<ZbGridlbVO> zbGridlbVO;

        /**
         * Gets the value of the zbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the zbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getZbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ZbGridlbVO}
         */
        public List<ZbGridlbVO> getZbGridlbVO() {
            if (zbGridlbVO == null) {
                zbGridlbVO = new ArrayList<ZbGridlbVO>();
            }
            return this.zbGridlbVO;
        }
    }
}