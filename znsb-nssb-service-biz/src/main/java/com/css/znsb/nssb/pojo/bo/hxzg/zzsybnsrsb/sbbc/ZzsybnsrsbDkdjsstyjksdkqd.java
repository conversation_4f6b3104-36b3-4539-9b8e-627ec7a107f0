
package com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc;

import com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc.general.SbbheadVO;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 《代扣代缴税收通用缴款书抵扣清单》
 * 
 * <p>Java class for zzsybnsrsb_dkdjsstyjksdkqd complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="zzsybnsrsb_dkdjsstyjksdkqd">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="sbbheadVO" type="{http://www.chinatax.gov.cn/dataspec/}sbbheadVO"/>
 *         &lt;element name="dkdjsstyjksdkqdGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded">
 *                   &lt;element name="dkdjsstyjksdkqdGridlb" type="{http://www.chinatax.gov.cn/dataspec/}dkdjsstyjksdkqdGridlbVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_dkdjsstyjksdkqd", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "sbbheadVO",
    "dkdjsstyjksdkqdGrid"
})
public class ZzsybnsrsbDkdjsstyjksdkqd
    implements Serializable
{

    private final static long serialVersionUID = 1468566763656402744L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected SbbheadVO sbbheadVO;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected DkdjsstyjksdkqdGrid dkdjsstyjksdkqdGrid;

    /**
     * Gets the value of the sbbheadVO property.
     * 
     * @return
     *     possible object is
     *     {@link SbbheadVO }
     *     
     */
    public SbbheadVO getSbbheadVO() {
        return sbbheadVO;
    }

    /**
     * Sets the value of the sbbheadVO property.
     * 
     * @param value
     *     allowed object is
     *     {@link SbbheadVO }
     *     
     */
    public void setSbbheadVO(SbbheadVO value) {
        this.sbbheadVO = value;
    }

    /**
     * Gets the value of the dkdjsstyjksdkqdGrid property.
     * 
     * @return
     *     possible object is
     *     {@link DkdjsstyjksdkqdGrid }
     *     
     */
    public DkdjsstyjksdkqdGrid getDkdjsstyjksdkqdGrid() {
        return dkdjsstyjksdkqdGrid;
    }

    /**
     * Sets the value of the dkdjsstyjksdkqdGrid property.
     * 
     * @param value
     *     allowed object is
     *     {@link DkdjsstyjksdkqdGrid }
     *     
     */
    public void setDkdjsstyjksdkqdGrid(DkdjsstyjksdkqdGrid value) {
        this.dkdjsstyjksdkqdGrid = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded">
     *         &lt;element name="dkdjsstyjksdkqdGridlb" type="{http://www.chinatax.gov.cn/dataspec/}dkdjsstyjksdkqdGridlbVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "dkdjsstyjksdkqdGridlb"
    })
    public static class DkdjsstyjksdkqdGrid
        implements Serializable
    {

        private final static long serialVersionUID = 1468566763656402744L;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
        protected List<DkdjsstyjksdkqdGridlbVO> dkdjsstyjksdkqdGridlb;

        /**
         * Gets the value of the dkdjsstyjksdkqdGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the dkdjsstyjksdkqdGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getDkdjsstyjksdkqdGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link DkdjsstyjksdkqdGridlbVO }
         * 
         * 
         */
        public List<DkdjsstyjksdkqdGridlbVO> getDkdjsstyjksdkqdGridlb() {
            if (dkdjsstyjksdkqdGridlb == null) {
                dkdjsstyjksdkqdGridlb = new ArrayList<DkdjsstyjksdkqdGridlbVO>();
            }
            return this.dkdjsstyjksdkqdGridlb;
        }

    }

}
