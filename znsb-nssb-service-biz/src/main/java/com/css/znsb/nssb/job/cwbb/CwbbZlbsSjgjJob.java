package com.css.znsb.nssb.job.cwbb;

import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.service.cwbb.CwbbBsService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CwbbZlbsSjgjJob {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private CwbbBsService cwbbBsService;

    /**
     * 财务报表资料报送数据归集
     */
    @XxlJob("cwbbZlbsSjgjJob")
    public void cwbbZlbsSjgjExecute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("cwbbZlbsSjgjJob start");
        String key = formatKey("cwbbZlbsSjgjJob:bbxx");
        if (validateCache(key)) {
            try {
                String sbny = "";
                String qydmz = "";
                String djxh = "";
                String jobParam = XxlJobHelper.getJobParam();
                if (!GyUtils.isNull(jobParam)) {
                    String[] split = jobParam.split(";");
                    sbny = split[0];
                    qydmz = split[1];
                    djxh = split[2];
                }
                cwbbBsService.initDataFormBpc(sbny, qydmz, djxh);
            } finally {
                delCache(key);
            }
        }
        stopWatch.stop();
        log.info("cxsShgjSyxxJob end,RT:{}", stopWatch.getTotalTimeSeconds());
    }


    private Boolean validateCache(String key) {
        Boolean hasKey = stringRedisTemplate.hasKey(key);
        if (hasKey) {
            return false;
        } else {
            stringRedisTemplate.opsForValue().set(key, "1", 300, TimeUnit.MINUTES);
            return true;
        }
    }

    private void delCache(String key) {
        stringRedisTemplate.delete(key);
    }

    private static String formatKey(String key) {
        return String.format("cwbbZlbsSjgjJob:job:%s", key);
    }

}
