package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>slxxFormVO complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="slxxFormVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="swrymc" type="{http://www.chinatax.gov.cn/dataspec/}slrDm" minOccurs="0"/>
 *         &lt;element name="swjgmc" type="{http://www.chinatax.gov.cn/dataspec/}slswjgDm" minOccurs="0"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}slrq"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "slxxFormVO", propOrder = { "swrymc", "swjgmc", "lrrq" })
public class SlxxFormVO {
    /**
     * 受理人代码
     */
    protected String swrymc;

    /**
     * 受理税务机关代码
     */
    protected String swjgmc;

    /**
     * 受理日期
     */
    @XmlElement(nillable = true, required = true)
    protected String lrrq;

    /**
     * 获取swrymc属性的值。
     * <p>
     * 受理人代码
     */
    public String getSwrymc() {
        return swrymc;
    }

    /**
     * 设置swrymc属性的值。
     */
    public void setSwrymc(String value) {
        this.swrymc = value;
    }

    /**
     * 获取swjgmc属性的值。
     * <p>
     * 受理税务机关代码
     */
    public String getSwjgmc() {
        return swjgmc;
    }

    /**
     * 设置swjgmc属性的值。
     */
    public void setSwjgmc(String value) {
        this.swjgmc = value;
    }

    /**
     * 获取lrrq属性的值。
     * <p>
     * 受理日期
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * 设置lrrq属性的值。
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }
}