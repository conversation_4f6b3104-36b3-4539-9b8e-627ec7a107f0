package com.css.znsb.nssb.pojo.bo.hxzg.sb000;


import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;

import java.util.List;
import java.util.Set;

/**
 *  金税三期工程核心征管及应用总集成项目 
 * gov.gt3.vo.sbzs.sb.sb000
 * File: SBNsrxxVO.java 创建时间:2014-6-29上午5:06:05
 * Title: 标题（要求能简洁地表达出类的功能和职责）
 * Description: 描述（简要描述类的职责、实现方式、使用注意事项等）
 * Copyright: Copyright (c) 2014 中国软件与技术服务股份有限公司
 * Company: 中国软件与技术服务股份有限公司
 * 模块: 平台架构
 * <AUTHOR>
 * @reviewer 审核人名字 
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */
public class SBNsrxxVO extends TaxBaseVO {

    /**
     * @description 序列化标示
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -752142524160065993L;
    /**
     * @description 国地税类型代码
     * @value value:gdslxDm
     */
    private String gdslxDm;

    /**
     * @description 纳税人识别号
     * @value value:nsrsbh
     */
    private String nsrsbh;

    /**
     * @description 纳税人名称
     * @value value:nsrmc
     */
    private String nsrmc;

    /**
     * @description 登记注册类型代码
     * @value value:djzclxDm
     */
    private String djzclxDm;

    /**
     * @description 纳税人状态代码
     * @value value:nsrztDm
     */
    private String nsrztDm;

    /**
     * @description 行业代码
     * @value value:hyDm
     */
    private String hyDm;

    /**
     * @description 街道乡镇代码
     * @value value:jdxzDm
     */
    private String jdxzDm;

    /**
     * @description 国地管户类型代码
     * @value value:gdghlxDm
     */
    private String gdghlxDm;

    /**
     * @description 登记机关代码
     * @value value:djjgDm
     */
    private String djjgDm;

    /**
     * @description 登记日期
     * @value value:djrq
     */
    private String djrq;

    /**
     * @description 核算方式代码
     * @value value:hsfsDm
     * 
     */
    private String hsfsDm;

    /**
     * @description 组织机构类型代码
     * @value value:zzjglxDm
     */
    private String zzjglxDm;

    /**
     * @description 会计制度（准则）代码
     * @value value:kjzdzzDm
     */
    private String kjzdzzDm;

    /**
     * @description 总分机构类型代码
     * @value value:zfjglxDm
     */
    private String zfjglxDm;

    /**
     * @description 文化事业建设费缴费信息登记标志
     * @value value:whsyjsfjfxxdjbz
     */
    private String whsyjsfjfxxdjbz;

    /**
     * @description 增值税经营类别
     * @value value:zzsjylb
     */
    private String zzsjylb;

    /**
     * @description 印花税缴纳方式代码
     * @value value:yhsjnfsDm
     */
    private String yhsjnfsDm;

    /**
     * @description 征收项目城乡标志代码
     * @value value:zsxmcxbzDm
     */
    private String zsxmcxbzDm;

    /**
     * @description 增值税企业类型代码
     * @value value:zzsqylxDm
     */
    private String zzsqylxDm;

    /**
     * @description 国家或地区数字代码
     * @value value:gjhdqszDm
     */
    private String gjhdqszDm;

    /**
     * @description 营改增纳税人类型代码
     * @value value:ygznsrlxDm
     */
    private String ygznsrlxDm;

    /**
     * @description 登记序号
     * @value value:djxh
     */
    private String djxh;

    /**
     * @description 汇算标记
     * @value value:hsbj
     */
    private String hsbj;

    /**
     * @description 注册类型类别
     * @value value:zclxlb
     */
    private String zclxlb;

    /**
     * @description 应征凭证种类代码
     * @value value:yzpzzlDm
     */
    private String yzpzzlDm;

    /**
     * @description 税款属性代码
     * @value value:sksxDm
     */
    private String[] sksxDm;

    /**
     * @description 申报日期
     * @value value:sbrq
     */
    private String sbrq1;

    /**
     * @description 填表日期
     * @value value:tbrq1
     */
    private String tbrq1;

    /**
     * @description 税款所属期起
     * @value value:skssqq
     */
    private String skssqq;

    /**
     * @description 税款所属期止
     * @value value:skssqz
     */
    private String skssqz;

    /**
     * @description 业务跳转口径（正常申报zcsb、申报错误更正cwgzbz、申报作废sbzf、申报表查看sbbck、申报导入sbdr、修改申报属期xgsq、修改申报类型xgsblx、网上申报wssb等，默认正常申报）
     * @value value:scenceCs
     */
    private String scenceCs = "zcsb";
    
    /**
     * @description 车购税业务跳转口径（正常功能菜单进入保存cgszssb，其他待办任务进入保存 cgsdb，应急模式受理环节保存，cgsyjmssl，应急模式办理环节保存cgsyjmsbl）
     * @value value:cgsScenceCs
     */
    private String cgsScenceCs;

    /**
     * @description XSD虚节点
     * @value value:xNodeSet
     */
    //    private Set<String> xNodeSet;

    /**
     * @description xsd行节点
     * @value value:trNodeSet
     */
    private Set<String> trNodeSet;

    /**
     * @description 页面表单XML字符串
     * @value value:xmlStr
     */
    private String xmlStr;

    /**
     * @description 税务人员代码
     * @value value:swryDm
     */
    private String swryDm;

    /**
     * @description 税务机关代码
     * @value value:swjgDm
     */
    private String swjgDm;

    /**
     * @description 受理人名称
     * @value value:slr
     */
    private String slr;

    /**
     * @description 数据归属地区
     * @value value:sjgsdq
     */
    private String sjgsdq;

    /**
     * @description 申报方式代码
     * @value value:sbfsDm
     */
    private String sbfsDm;

    /**
     * @description 文件内容标记
     * @value value:wjnrbj
     */
    private String wjnrbj;

    /**
     * @description 凭证序号
     * @value value:pzxh
     */
    private String pzxh;
    
    /**
     * @description 凭证序号
     * @value value:pzxh
     */
    private String scenceCs_Forjs;

    /**
     * @description 增值税应税货物劳务资格标志（N、Y）
     * @value value:yshwlwbz
     */
    private String yshwlwbz = "N";

    /**
     * @description 增值税应税服务资格标志（N、Y）
     * @value value:ysfwbz
     */
    private String ysfwbz = "N";

    /**
     * @description 申报属性代码
     * @value value:sbsxDm
     */
    private String sbsxDm1;

    /**
     * @description 税源编号
     * @value value:sybh
     */
    private String sybh;

    /**
     * @description 税款处理类型代码
     * @value value:skcllxDm
     */
    private String skcllxDm;

    /**
     * @description 纳税人资格类型代码
     * @value value:nsrzgDm
     */
    private String nsrzgDm;

    /**
     * @description 系统编码（数据来源渠道）
     * @value value:xtbm
     */
    private String xtbm;

    /**
     * @description 财务报表种类代码
     * @value value:cwbbzlDm
     */
    private String cwbbzlDm;

    /**
     * @description 财务报表报送期代码
     * @value value:bbbsqDm
     */
    private String bbbsqDm;

    /**
     * @description 财务报送资料代码
     * @value value:bszlDm
     */
    private String bszlDm;

    /**
     * @description 财务报表资料名称
     * @value value:bszlmc
     */
    private String bszlmc;

    /**
     * @description 财务报表是否必报
     * @value value:bb
     */
    private String bb;

    /**
     * @description 财务报表是否条件报送
     * @value value:tjbs
     */
    private String tjbs;

    /**
     * @description 财务报表是否需采集
     * @value value:sfxcj
     */
    private String sfxcj;

    /**
     * @description 财务报表链接位置（类似于一般申报申报表位置字段）
     * @value value:ljwz
     */
    private String ljwz;

    /**
     * @description 车购税车辆识别代号（车架号）
     * @value value:clsbdh
     */
    private String clsbdh;

    /**
     * @description 车辆完税证明号码
     * @value value:clwszmhm
     */
    private String clwszmhm;

    /**
     * @description 应补退税额
     * @value value:ybtse
     */
    private Double ybtse;

    /**
     * @description 放弃备案减免标志
     * @value value:fqbajmBz
     */
    private String fqbajmBz;

    /**
     * @description 发票已预缴额度
     * @value value:fpyyjed
     */
    private String fpyyjed;

    /**
     * @description 税收管理员代码
     * @value value:ssglyDm
     */
    private String ssglyDm;

    /**
     * @description 主管税务科所分局代码
     * @value value:zgswskfjDm
     */
    private String zgswskfjDm;

    /**
     * @description 录入人代码
     * @value value:lrrDm
     */
    private String lrrDm;

    /**
     * @description 修改人代码
     * @value value:xgrDm
     */
    private String xgrDm;

    /**
     * @description 申报uuid为申报主附表、数据表之间唯一标识
     * @value value:sbuuid
     */
    private String sbuuid;

    /**
     * @description 作废标识
     * @value value:zfbz1
     */
    private String zfbz1;

    /**
     * @description 作废日期
     * @value value:zfrq1
     */
    private String zfrq1;

    /**
     * @description 备注
     * @value value:bz
     */
    private String bz;

    /**
     * @description 作废人代码
     * @value value:zfrDm
     */
    private String zfrDm;

    /**
     * @description 更正类型代码
     * @value value:gzlxDm1
     */
    private String gzlxDm1;

    /**
     * @description 受理日期
     * @value value:slrq
     */
    private String slrq;

    /**
     * @description 受理人代码
     * @value value:slrDm
     */
    private String slrDm;

    /**
     * @description 受理税务机关代码
     * @value value:slswjgDm
     */
    private String slswjgDm;

    /**
     * @description 办税人姓名
     * @value value:bsrxm
     */
    private String bsrxm;

    /**
     * @description 代理经办人执业证件号码
     * @value value:dljbrzyzjhm
     */
    private String dljbrzyzjhm;

    /**
     * @description 财务负责人姓名
     * @value value:cwfzrxm
     */
    private String cwfzrxm;

    /**
     * @description 法定代表人姓名
     * @value value:fddbrxm
     */
    private String fddbrxm;

    /**
     * @description 办税人联系电话
     * @value value:bsrlxdh
     */
    private String bsrlxdh;

    /**
     * @description 代理人名称
     * @value value:dlrmc
     */
    private String dlrmc;

    /**
     * @description 经办人
     * @value value:jbrxm
     */
    private String jbrxm;

    /**
     * @description 经办人联系电话
     * @value value:jbrxm
     */
    private String jbrlxdh;

    /**
     * @description 税务代理人地址
     * @value value:swdlrdz
     */
    private String swdlrdz;

    /**
     * @description 税务代理人联系电话
     * @value value:swdlrlxdh
     */
    private String swdlrlxdh;

    /**
     * @description 录入日期
     * @value value:lrrq
     */
    private String lrrq;

    /**
     * @description 修改日期
     * @value value:xgrq
     */
    private String xgrq;

    /**
     * @description 非居民企业标志
     * @value value:fjmqybz
     */
    private String fjmqybz;

    /**
     * @description 总分机构类别（企业所得税专用）
     * @value value:zfjglbForQysds
     */
    private String zfjglbForQysds;
    /**
     * @description 查询预缴时是否查账自然人（现仅限居民企业所得税（A类）月（季）报2018版专用）
     * @value value:cxZrrForYjcx
     */
    private String cxZrrForYjcx = "Y";
    
    /**
     * @description 没有申报表记录的情况下在错误更正时冲账原预缴使用情况（现仅限居民企业所得税（A类）月（季）报2018版专用）
     * @value value:noSbbZfYjlsz
     */
    private boolean noSbbZfYjlsz = false; 
    
    /**
     * @description 报验户登记序号字符串，跨省时使用
     * @value value:byhDjxhs
     */
    private String byhDjxhs;

    /**
     * 以下为自然人纳税人主体数据信息
     */

    /**
     * @description 姓名
     * @value value:xm
     */
    private String xm;

    /**
     * @description 职业
     * @value value:zyDm
     */
    private String zyDm;

    /**
     * @description 证件种类
     * @value value:zjzlDm
     */
    private String zjzlDm;

    /**
     * @description 证件号码
     * @value value:zjhm
     */
    private String zjhm;

    /**
     * @description 联系电话
     * @value value:lxdh
     */
    private String lxdh;

    /**
     * @description 在华是否有住所:是Y 否N
     * @value value:sfyzs
     */
    private String sfyzs;

    /**
     * @description 在华无住所申请年度在华是否居住满一年:是Y 否N
     * @value value:sfmyn
     */
    private String sfmyn;

    /**
     * @description 居住地址
     * @value value:jzdz
     */
    private String jzdz;

    /**
     * @description 户籍地址
     * @value value:hjdz
     */
    private String hjdz;

    /**
     * @description 主管税务机关名称
     * @value value:zgswjgmc
     */
    private String zgswjgmc;
    /**
     * @description 主管税务局名称
     * @value value:zgswjgmc
     */
    private String zgswjmc;

    /**
     * @description 通讯地址
     * @value value:txdz
     */
    private String txdz;

    /**
     * @description 申请年度完税情况
     * @value value:nrsndwsqk
     */
    private String nrsndwsqk;

    /**
     * @description 自然人标志
     * @value value:zrrBz
     */
    private String zrrBz;

    /**
     * @description 单位隶属关系代码
     * @value value:dwlsgxDm
     */
    private String dwlsgxDm;

    /**
     * @description 国税登记序号,用于附加税计算计税依据
     * @value value:gsdjxh
     */
    private String gsdjxh;
    /**
     * @description 代扣代缴业务判断是否填写缴款书，没有填写默认为N，填写时为Y
     * @value value:dkdjIsJks
     */
    private String dkdjIsJks="N";
    /**
     * @description 更正产生最新的申报uuid
     * @value value:sbuuid_new
     */
    private String sbuuidNew;
    /**
     * @description 季中转一般人标志(增值税小规模季中转一般纳税人专用，默认为false 只有小规模季中转一般纳税人才会true)
     * @value value:jzzybrBz
     */
    private boolean jzzybrBz = false;
    /**
     * @description 季中转一般人标志(增值税小规模季中逾期未认定一般纳税人专用，默认为false 只有小规模季中第二个月逾期未认定一般纳税人才会true)
     * @value value:midyqwrdybrBz
     */
    private boolean midyqwrdybrBz = false;
    /**
     * @description 季中转一般人标志(增值税小规模季中逾期未认定一般纳税人专用，默认为false 只有小规模季中第三个月逾期未认定一般纳税人才会true)
     * @value value:lastyqwrdybrBz
     */
    private boolean lastyqwrdybrBz = false;
    /**
     * @description 税票号码
     * @value value:sphm
     */
    private String sphm;
    /**
     * @description 生产经营地址
     * @value value:sbuuid_new
     */
    private String scjydz;
    /**
     * @description 生产经营地联系电话
     * @value value:sbuuid_new
     */
    private String scjydlxdh;
    /**
     * @description 是否判断重复申报监控
     * @value value:sfpdcfsbjkBz
     */
    private String sfpdcfsbjkBz ="Y";
    /**
     * @description 开业受理日期
     * @value value:kyslrq
     */
    private String kyslrq;
    
    /**
     * @description 税收档案编号
     * @value value:ssdabh
     */
    private String ssdabh;
    
    /**
     * @description 课征主体登记类型代码
     * @value value:kzztdjlxDm
     */
    private String kzztdjlxDm;
    
    /**
     * @description 跨区税源登记标志
     * @value value:kzztdjlxDm
     */
    private String kqccsztdjbz;
    
    /**
     * @description 是否区分起征点金额   N：不区分  Y：区分
     * @value value:sfqfQzd
     */
    private String sfqfQzd="N";
    
    /**
     * @description 简并征期纳税期限
     * @value value:jbzqNsqxDm
     */
    private String jbzqNsqxDm;
    
    /**
     * @description 委托代征明细数据业务跳转口径（委托代征明细申报wtdzmxsb、委托代征汇总申报wtdzhzsb等，默认委托代征明细申报）
     * @value value:wtdzmxScenceCs
     */
    private String wtdzmxScenceCs = "wtdzmxsb";    
    /**
     * @description 是否区分是非居民自行申报还是扣缴企业所得税申报(由于这两个用例的yzpzzlDm相同无法区分) 默认为N
     * @value value:sfqfFjmzxsb
     */
    private String sfqfFjmzxsb="N";
    
    private String scjydzxzqhszDm;
    
    /**
     * @description 登记序号list(多个djxh)
     * @value value:djxhList
     */
    private List<String> djxhList;
    
    /**
     * @description 行政区划代码(跨地区查询使用)
     * @value value:xzqhszDm
     */
    private String xzqhszDm;
    
    /**
     * @description 凭证序号list(多个pzxh)
     * @value value:djxhList
     */
    private List<String> pzxhList;

    /**
     * @description 是否是十税合一土地增值税（N、Y）
     * @value value:is10sh1Tdzzs
     */
    private String is10sh1Tdzzs = "N";

    /**
     * @description cxstysbuuid
     * @value value:djxhList
     */
    private String cxstysbuuid;

    /**
     * @description 非税缓缴标志
     * @value value:is10sh1Tdzzs
     */
    private String fssrtysbhjbz = "Y";

    /**
     * @description 目标税务机关代码
     * @value value:mbSwjgDm
     */
    private String mbSwjgDm;

    /**
     * @description 社会信用代码
     * @value value:shxydm
     */
    private String shxydm;


    public String getFssrtysbhjbz() {
        return fssrtysbhjbz;
    }

    public void setFssrtysbhjbz(String fssrtysbhjbz) {
        this.fssrtysbhjbz = fssrtysbhjbz;
    }

    public String getMbSwjgDm() {
        return mbSwjgDm;
    }

    public void setMbSwjgDm(String mbSwjgDm) {
        this.mbSwjgDm = mbSwjgDm;
    }


    public String getIs10sh1Tdzzs() {
        return is10sh1Tdzzs;
    }

    public void setIs10sh1Tdzzs(String is10sh1Tdzzs) {
        this.is10sh1Tdzzs = is10sh1Tdzzs;
    }




    public String getCxstysbuuid() {
        return cxstysbuuid;
    }

    public void setCxstysbuuid(String cxstysbuuid) {
        this.cxstysbuuid = cxstysbuuid;
    }

    /**
     *创建时间:2018-6-21下午03:15:15
     *get方法
     * @return the xzqhszDm
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }

    /**
     * 创建时间:2018-8-11下午03:15:15
     * set方法
     * @param xzqhszDm the xzqhszDm to set
     */
    public void setXzqhszDm(String xzqhszDm) {
        this.xzqhszDm = xzqhszDm;
    }

    public String getSfqfFjmzxsb() {
        return sfqfFjmzxsb;
    }

    public void setSfqfFjmzxsb(String sfqfFjmzxsb) {
        this.sfqfFjmzxsb = sfqfFjmzxsb;
    }

    /**
     *创建时间:2016-1-11下午03:15:15
     *get方法
     * @return the kyslrq
     */
    public String getKyslrq() {
        return kyslrq;
    }

    /**
     * 创建时间:2016-1-11下午03:15:15
     * set方法
     * @param kyslrq the kyslrq to set
     */
    public void setKyslrq(String kyslrq) {
        this.kyslrq = kyslrq;
    }

    public String getScjydz() {
        return scjydz;
    }

    public void setScjydz(String scjydz) {
        this.scjydz = scjydz;
    }

    public String getScjydlxdh() {
        return scjydlxdh;
    }

    public void setScjydlxdh(String scjydlxdh) {
        this.scjydlxdh = scjydlxdh;
    }

    /**
     *创建时间:2014-12-22上午11:06:20
     *get方法
     * @return the sbuuidNew
     */
    public String getSphm() {
        return sphm;
    }

    /**
     * 创建时间:2014-12-22上午11:06:20
     * set方法
     * @param sbuuidNew the sbuuidNew to set
     */
    public void setSphm(String sphm) {
        this.sphm = sphm;
    }
    /**
     *创建时间:2014-12-22上午11:06:20
     *get方法
     * @return the sbuuidNew
     */
    public String getSbuuidNew() {
        return sbuuidNew;
    }

    /**
     * 创建时间:2014-12-22上午11:06:20
     * set方法
     * @param sbuuidNew the sbuuidNew to set
     */
    public void setSbuuidNew(String sbuuidNew) {
        this.sbuuidNew = sbuuidNew;
    }

    public String getDkdjIsJks() {
        return dkdjIsJks;
    }

    public void setDkdjIsJks(String dkdjIsJks) {
        this.dkdjIsJks = dkdjIsJks;
    }

    public String getGsdjxh() {
        return gsdjxh;
    }

    public void setGsdjxh(String gsdjxh) {
        this.gsdjxh = gsdjxh;
    }

    /**
     *创建时间:2014-8-15下午10:56:14
     *get方法
     * @return the dwlsgxDm
     */
    public String getDwlsgxDm() {
        return dwlsgxDm;
    }

    /**
     *创建时间:2015-5-30下午03:08:11
     *get方法
     * @return the scenceCs_Forjs
     */
    public String getScenceCs_Forjs() {
        return scenceCs_Forjs;
    }

    /**
     * 创建时间:2015-5-30下午03:08:11
     * set方法
     * @param scenceCsForjs the scenceCs_Forjs to set
     */
    public void setScenceCs_Forjs(String scenceCsForjs) {
        scenceCs_Forjs = scenceCsForjs;
    }

    /**
     * 创建时间:2014-8-15下午10:56:14
     * set方法
     * @param dwlsgxDm the dwlsgxDm to set
     */
    public void setDwlsgxDm(String dwlsgxDm) {
        this.dwlsgxDm = dwlsgxDm;
    }

    public String getZrrBz() {
        return zrrBz;
    }

    public void setZrrBz(String zrrBz) {
        this.zrrBz = zrrBz;
    }

    public String getGdslxDm() {
        return gdslxDm;
    }

    public void setGdslxDm(String gdslxDm) {
        this.gdslxDm = gdslxDm;
    }

    public String getNsrsbh() {
        return nsrsbh;
    }

    public void setNsrsbh(String nsrsbh) {
        this.nsrsbh = nsrsbh;
    }

    public String getNsrmc() {
        return nsrmc;
    }

    public void setNsrmc(String nsrmc) {
        this.nsrmc = nsrmc;
    }

    public String getDjzclxDm() {
        return djzclxDm;
    }

    public void setDjzclxDm(String djzclxDm) {
        this.djzclxDm = djzclxDm;
    }

    public String getNsrztDm() {
        return nsrztDm;
    }

    public void setNsrztDm(String nsrztDm) {
        this.nsrztDm = nsrztDm;
    }

    public String getHyDm() {
        return hyDm;
    }

    public void setHyDm(String hyDm) {
        this.hyDm = hyDm;
    }

    public String getJdxzDm() {
        return jdxzDm;
    }

    public void setJdxzDm(String jdxzDm) {
        this.jdxzDm = jdxzDm;
    }

    public String getGdghlxDm() {
        return gdghlxDm;
    }

    public void setGdghlxDm(String gdghlxDm) {
        this.gdghlxDm = gdghlxDm;
    }

    public String getDjjgDm() {
        return djjgDm;
    }

    public void setDjjgDm(String djjgDm) {
        this.djjgDm = djjgDm;
    }

    public String getDjrq() {
        return djrq;
    }

    public void setDjrq(String djrq) {
        this.djrq = djrq;
    }

    public String getHsfsDm() {
        return hsfsDm;
    }

    public void setHsfsDm(String hsfsDm) {
        this.hsfsDm = hsfsDm;
    }

    public String getZzjglxDm() {
        return zzjglxDm;
    }

    public void setZzjglxDm(String zzjglxDm) {
        this.zzjglxDm = zzjglxDm;
    }

    public String getKjzdzzDm() {
        return kjzdzzDm;
    }

    public void setKjzdzzDm(String kjzdzzDm) {
        this.kjzdzzDm = kjzdzzDm;
    }

    public String getZfjglxDm() {
        return zfjglxDm;
    }

    public void setZfjglxDm(String zfjglxDm) {
        this.zfjglxDm = zfjglxDm;
    }

    public String getWhsyjsfjfxxdjbz() {
        return whsyjsfjfxxdjbz;
    }

    public void setWhsyjsfjfxxdjbz(String whsyjsfjfxxdjbz) {
        this.whsyjsfjfxxdjbz = whsyjsfjfxxdjbz;
    }

    public String getZzsjylb() {
        return zzsjylb;
    }

    public void setZzsjylb(String zzsjylb) {
        this.zzsjylb = zzsjylb;
    }

    public String getYhsjnfsDm() {
        return yhsjnfsDm;
    }

    public void setYhsjnfsDm(String yhsjnfsDm) {
        this.yhsjnfsDm = yhsjnfsDm;
    }

    public String getZsxmcxbzDm() {
        return zsxmcxbzDm;
    }

    public void setZsxmcxbzDm(String zsxmcxbzDm) {
        this.zsxmcxbzDm = zsxmcxbzDm;
    }

    public String getZzsqylxDm() {
        return zzsqylxDm;
    }

    public void setZzsqylxDm(String zzsqylxDm) {
        this.zzsqylxDm = zzsqylxDm;
    }

    public String getGjhdqszDm() {
        return gjhdqszDm;
    }

    public void setGjhdqszDm(String gjhdqszDm) {
        this.gjhdqszDm = gjhdqszDm;
    }

    public String getYgznsrlxDm() {
        return ygznsrlxDm;
    }

    public void setYgznsrlxDm(String ygznsrlxDm) {
        this.ygznsrlxDm = ygznsrlxDm;
    }

    public String getDjxh() {
        return djxh;
    }

    public void setDjxh(String djxh) {
        this.djxh = djxh;
    }

    public String getHsbj() {
        return hsbj;
    }

    public void setHsbj(String hsbj) {
        this.hsbj = hsbj;
    }

    public String getZclxlb() {
        return zclxlb;
    }

    public void setZclxlb(String zclxlb) {
        this.zclxlb = zclxlb;
    }

    public String getYzpzzlDm() {
        return yzpzzlDm;
    }

    public void setYzpzzlDm(String yzpzzlDm) {
        this.yzpzzlDm = yzpzzlDm;
    }

    public String[] getSksxDm() {
        return sksxDm;
    }

    public void setSksxDm(String[] sksxDm) {
        this.sksxDm = sksxDm;
    }

    public String getSbrq1() {
        return sbrq1;
    }

    public void setSbrq1(String sbrq1) {
        this.sbrq1 = sbrq1;
    }

    public String getTbrq1() {
        return tbrq1;
    }

    public void setTbrq1(String tbrq1) {
        this.tbrq1 = tbrq1;
    }

    public String getSkssqq() {
        return skssqq;
    }

    public void setSkssqq(String skssqq) {
        this.skssqq = skssqq;
    }

    public String getSkssqz() {
        return skssqz;
    }

    public void setSkssqz(String skssqz) {
        this.skssqz = skssqz;
    }

    public String getScenceCs() {
        return scenceCs;
    }

    public void setScenceCs(String scenceCs) {
        this.scenceCs = scenceCs;
    }

    //    public Set<String> getxNodeSet() {
    //        return xNodeSet;
    //    }
    //
    //    public void setxNodeSet(Set<String> xNodeSet) {
    //        this.xNodeSet = xNodeSet;
    //    }

    public Set<String> getTrNodeSet() {
        return trNodeSet;
    }

    public void setTrNodeSet(Set<String> trNodeSet) {
        this.trNodeSet = trNodeSet;
    }

    public String getXmlStr() {
        return xmlStr;
    }

    public void setXmlStr(String xmlStr) {
        this.xmlStr = xmlStr;
    }

    public String getSwryDm() {
        return swryDm;
    }

    public void setSwryDm(String swryDm) {
        this.swryDm = swryDm;
    }

    public String getSwjgDm() {
        return swjgDm;
    }

    public void setSwjgDm(String swjgDm) {
        this.swjgDm = swjgDm;
    }

    public String getSlr() {
        return slr;
    }

    public void setSlr(String slr) {
        this.slr = slr;
    }

    public String getSjgsdq() {
        return sjgsdq;
    }

    public void setSjgsdq(String sjgsdq) {
        this.sjgsdq = sjgsdq;
    }

    public String getSbfsDm() {
        return sbfsDm;
    }

    public void setSbfsDm(String sbfsDm) {
        this.sbfsDm = sbfsDm;
    }

    public String getWjnrbj() {
        return wjnrbj;
    }

    public void setWjnrbj(String wjnrbj) {
        this.wjnrbj = wjnrbj;
    }

    public String getPzxh() {
        return pzxh;
    }

    public void setPzxh(String pzxh) {
        this.pzxh = pzxh;
    }

    public String getYshwlwbz() {
        return yshwlwbz;
    }

    public void setYshwlwbz(String yshwlwbz) {
        this.yshwlwbz = yshwlwbz;
    }

    public String getYsfwbz() {
        return ysfwbz;
    }

    public void setYsfwbz(String ysfwbz) {
        this.ysfwbz = ysfwbz;
    }

    public String getSbsxDm1() {
        return sbsxDm1;
    }

    public void setSbsxDm1(String sbsxDm1) {
        this.sbsxDm1 = sbsxDm1;
    }

    public String getSybh() {
        return sybh;
    }

    public void setSybh(String sybh) {
        this.sybh = sybh;
    }

    public String getSkcllxDm() {
        return skcllxDm;
    }

    public void setSkcllxDm(String skcllxDm) {
        this.skcllxDm = skcllxDm;
    }

    public String getZgswjmc() {
        return zgswjmc;
    }

    public void setZgswjmc(String zgswjmc) {
        this.zgswjmc = zgswjmc;
    }

    public String getNsrzgDm() {
        return nsrzgDm;
    }

    public void setNsrzgDm(String nsrzgDm) {
        this.nsrzgDm = nsrzgDm;
    }

    public String getXtbm() {
        return xtbm;
    }

    public void setXtbm(String xtbm) {
        this.xtbm = xtbm;
    }

    public String getCwbbzlDm() {
        return cwbbzlDm;
    }

    public void setCwbbzlDm(String cwbbzlDm) {
        this.cwbbzlDm = cwbbzlDm;
    }

    public String getBbbsqDm() {
        return bbbsqDm;
    }

    public void setBbbsqDm(String bbbsqDm) {
        this.bbbsqDm = bbbsqDm;
    }

    public String getBszlDm() {
        return bszlDm;
    }

    public void setBszlDm(String bszlDm) {
        this.bszlDm = bszlDm;
    }

    public String getBszlmc() {
        return bszlmc;
    }

    public void setBszlmc(String bszlmc) {
        this.bszlmc = bszlmc;
    }

    public String getBb() {
        return bb;
    }

    public void setBb(String bb) {
        this.bb = bb;
    }

    public String getTjbs() {
        return tjbs;
    }

    public void setTjbs(String tjbs) {
        this.tjbs = tjbs;
    }

    public String getSfxcj() {
        return sfxcj;
    }

    public void setSfxcj(String sfxcj) {
        this.sfxcj = sfxcj;
    }

    public String getLjwz() {
        return ljwz;
    }

    public void setLjwz(String ljwz) {
        this.ljwz = ljwz;
    }

    public String getClsbdh() {
        return clsbdh;
    }

    public void setClsbdh(String clsbdh) {
        this.clsbdh = clsbdh;
    }

    public String getClwszmhm() {
        return clwszmhm;
    }

    public void setClwszmhm(String clwszmhm) {
        this.clwszmhm = clwszmhm;
    }

    public Double getYbtse() {
        return ybtse;
    }

    public void setYbtse(Double ybtse) {
        this.ybtse = ybtse;
    }

    public String getFqbajmBz() {
        return fqbajmBz;
    }

    public void setFqbajmBz(String fqbajmBz) {
        this.fqbajmBz = fqbajmBz;
    }

    public String getFpyyjed() {
        return fpyyjed;
    }

    public void setFpyyjed(String fpyyjed) {
        this.fpyyjed = fpyyjed;
    }

    public String getSsglyDm() {
        return ssglyDm;
    }

    public void setSsglyDm(String ssglyDm) {
        this.ssglyDm = ssglyDm;
    }

    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    public void setZgswskfjDm(String zgswskfjDm) {
        this.zgswskfjDm = zgswskfjDm;
    }

    public String getLrrDm() {
        return lrrDm;
    }

    public void setLrrDm(String lrrDm) {
        this.lrrDm = lrrDm;
    }

    public String getXgrDm() {
        return xgrDm;
    }

    public void setXgrDm(String xgrDm) {
        this.xgrDm = xgrDm;
    }

    public String getSbuuid() {
        return sbuuid;
    }

    public void setSbuuid(String sbuuid) {
        this.sbuuid = sbuuid;
    }

    public String getZfbz1() {
        return zfbz1;
    }

    public void setZfbz1(String zfbz1) {
        this.zfbz1 = zfbz1;
    }

    public String getZfrq1() {
        return zfrq1;
    }

    public void setZfrq1(String zfrq1) {
        this.zfrq1 = zfrq1;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getZfrDm() {
        return zfrDm;
    }

    public void setZfrDm(String zfrDm) {
        this.zfrDm = zfrDm;
    }

    public String getGzlxDm1() {
        return gzlxDm1;
    }

    public void setGzlxDm1(String gzlxDm1) {
        this.gzlxDm1 = gzlxDm1;
    }

    public String getSlrq() {
        return slrq;
    }

    public void setSlrq(String slrq) {
        this.slrq = slrq;
    }

    public String getSlrDm() {
        return slrDm;
    }

    public void setSlrDm(String slrDm) {
        this.slrDm = slrDm;
    }

    public String getSlswjgDm() {
        return slswjgDm;
    }

    public void setSlswjgDm(String slswjgDm) {
        this.slswjgDm = slswjgDm;
    }

    public String getBsrxm() {
        return bsrxm;
    }

    public void setBsrxm(String bsrxm) {
        this.bsrxm = bsrxm;
    }

    public String getDljbrzyzjhm() {
        return dljbrzyzjhm;
    }

    public void setDljbrzyzjhm(String dljbrzyzjhm) {
        this.dljbrzyzjhm = dljbrzyzjhm;
    }

    public String getCwfzrxm() {
        return cwfzrxm;
    }

    public void setCwfzrxm(String cwfzrxm) {
        this.cwfzrxm = cwfzrxm;
    }

    public String getFddbrxm() {
        return fddbrxm;
    }

    public void setFddbrxm(String fddbrxm) {
        this.fddbrxm = fddbrxm;
    }

    public String getSwdlrdz() {
        return swdlrdz;
    }

    public void setSwdlrdz(String swdlrdz) {
        this.swdlrdz = swdlrdz;
    }

    public String getSwdlrlxdh() {
        return swdlrlxdh;
    }

    public void setSwdlrlxdh(String swdlrlxdh) {
        this.swdlrlxdh = swdlrlxdh;
    }

    public String getLrrq() {
        return lrrq;
    }

    public void setLrrq(String lrrq) {
        this.lrrq = lrrq;
    }

    public String getXgrq() {
        return xgrq;
    }

    public void setXgrq(String xgrq) {
        this.xgrq = xgrq;
    }

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getZyDm() {
        return zyDm;
    }

    public void setZyDm(String zyDm) {
        this.zyDm = zyDm;
    }

    public String getZjzlDm() {
        return zjzlDm;
    }

    public void setZjzlDm(String zjzlDm) {
        this.zjzlDm = zjzlDm;
    }

    public String getZjhm() {
        return zjhm;
    }

    public void setZjhm(String zjhm) {
        this.zjhm = zjhm;
    }

    public String getLxdh() {
        return lxdh;
    }

    public void setLxdh(String lxdh) {
        this.lxdh = lxdh;
    }

    public String getSfyzs() {
        return sfyzs;
    }

    public void setSfyzs(String sfyzs) {
        this.sfyzs = sfyzs;
    }

    public String getSfmyn() {
        return sfmyn;
    }

    public void setSfmyn(String sfmyn) {
        this.sfmyn = sfmyn;
    }

    public String getJzdz() {
        return jzdz;
    }

    public void setJzdz(String jzdz) {
        this.jzdz = jzdz;
    }

    public String getHjdz() {
        return hjdz;
    }

    public void setHjdz(String hjdz) {
        this.hjdz = hjdz;
    }

    public String getZgswjgmc() {
        return zgswjgmc;
    }

    public void setZgswjgmc(String zgswjgmc) {
        this.zgswjgmc = zgswjgmc;
    }

    public String getTxdz() {
        return txdz;
    }

    public void setTxdz(String txdz) {
        this.txdz = txdz;
    }

    public String getNrsndwsqk() {
        return nrsndwsqk;
    }

    public void setNrsndwsqk(String nrsndwsqk) {
        this.nrsndwsqk = nrsndwsqk;
    }

    /**
     *创建时间:2014-8-27下午10:17:26
     *get方法
     * @return the fjmqybz
     */
    public String getFjmqybz() {
        return fjmqybz;
    }

    /**
     * 创建时间:2014-8-27下午10:17:26
     * set方法
     * @param fjmqybz the fjmqybz to set
     */
    public void setFjmqybz(String fjmqybz) {
        this.fjmqybz = fjmqybz;
    }

    /**
     *创建时间:2014-9-20下午04:16:14
     *get方法
     * @return the zfjglbForQysds
     */
    public String getZfjglbForQysds() {
        return zfjglbForQysds;
    }

    /**
     * 创建时间:2014-9-20下午04:16:14
     * set方法
     * @param zfjglbForQysds the zfjglbForQysds to set
     */
    public void setZfjglbForQysds(String zfjglbForQysds) {
        this.zfjglbForQysds = zfjglbForQysds;
    }

    public String getBsrlxdh() {
        return bsrlxdh;
    }

    public void setBsrlxdh(String bsrlxdh) {
        this.bsrlxdh = bsrlxdh;
    }

    public String getDlrmc() {
        return dlrmc;
    }

    public void setDlrmc(String dlrmc) {
        this.dlrmc = dlrmc;
    }

    public String getJbrxm() {
        return jbrxm;
    }

    public void setJbrxm(String jbrxm) {
        this.jbrxm = jbrxm;
    }

    public String getJbrlxdh() {
        return jbrlxdh;
    }

    public void setJbrlxdh(String jbrlxdh) {
        this.jbrlxdh = jbrlxdh;
    }

    /**
     *创建时间:2014-9-27下午11:09:17
     *get方法
     * @return the cgsScenceCs
     */
    public String getCgsScenceCs() {
        return cgsScenceCs;
    }

    /**
     * 创建时间:2014-9-27下午11:09:17
     * set方法
     * @param cgsScenceCs the cgsScenceCs to set
     */
    public void setCgsScenceCs(String cgsScenceCs) {
        this.cgsScenceCs = cgsScenceCs;
    }

    /**
     *创建时间:2015-5-4下午09:15:50
     *get方法
     * @return the jzzybrBz
     */
    public boolean isJzzybrBz() {
        return jzzybrBz;
    }

    /**
     * 创建时间:2015-5-4下午09:15:50
     * set方法
     * @param jzzybrBz the jzzybrBz to set
     */
    public void setJzzybrBz(boolean jzzybrBz) {
        this.jzzybrBz = jzzybrBz;
    }
    /**
     *创建时间:2015-9-28下午09:15:50
     *get方法
     * @return the sfpdcfsbjkBz
     */
    public String getSfpdcfsbjkBz() {
        return sfpdcfsbjkBz;
    }
    /**
     * 创建时间:2015-9-28下午09:15:50
     * set方法
     * @param sfpdcfsbjkBz the sfpdcfsbjkBz to set
     */
    public void setSfpdcfsbjkBz(String sfpdcfsbjkBz) {
        this.sfpdcfsbjkBz = sfpdcfsbjkBz;
    }

    public String getSsdabh() {
        return ssdabh;
    }

    public void setSsdabh(String ssdabh) {
        this.ssdabh = ssdabh;
    }

    public String getKzztdjlxDm() {
        return kzztdjlxDm;
    }

    public void setKzztdjlxDm(String kzztdjlxDm) {
        this.kzztdjlxDm = kzztdjlxDm;
    }

    public String getKqccsztdjbz() {
        return kqccsztdjbz;
    }

    public void setKqccsztdjbz(String kqccsztdjbz) {
        this.kqccsztdjbz = kqccsztdjbz;
    }

    public String getSfqfQzd() {
        return sfqfQzd;
    }

    public void setSfqfQzd(String sfqfQzd) {
        this.sfqfQzd = sfqfQzd;
    }

    /**
     *创建时间:2016-10-17下午02:15:14
     *get方法
     * @return the jbzqNsqxDm
     */
    public String getJbzqNsqxDm() {
        return jbzqNsqxDm;
    }

    /**
     * 创建时间:2016-10-17下午02:15:14
     * set方法
     * @param jbzqNsqxDm the jbzqNsqxDm to set
     */
    public void setJbzqNsqxDm(String jbzqNsqxDm) {
        this.jbzqNsqxDm = jbzqNsqxDm;
    }

    /**
     * 创建时间:2016-11-8下午05:34:22
     * set方法
     * @param wtdzmxScenceCs the wtdzmxScenceCs to set
     */
    public void setWtdzmxScenceCs(String value) {
        this.wtdzmxScenceCs = value;
    }

    /**
     *创建时间:2016-11-8下午05:34:22
     *get方法
     * @return the wtdzmxScenceCs
     */
    public String getWtdzmxScenceCs() {
        return wtdzmxScenceCs;
    }

    /**
     *创建时间:2017-8-28下午02:05:41
     *get方法
     * @return the scjydzxzqhszDm
     */
    public String getScjydzxzqhszDm() {
        return scjydzxzqhszDm;
    }

    /**
     * 创建时间:2017-8-28下午02:05:41
     * set方法
     * @param scjydzxzqhszDm the scjydzxzqhszDm to set
     */
    public void setScjydzxzqhszDm(String scjydzxzqhszDm) {
        this.scjydzxzqhszDm = scjydzxzqhszDm;
    }


    public boolean isMidyqwrdybrBz() {
        return midyqwrdybrBz;
    }

    public void setMidyqwrdybrBz(boolean midyqwrdybrBz) {
        this.midyqwrdybrBz = midyqwrdybrBz;
    }

    public boolean isLastyqwrdybrBz() {
        return lastyqwrdybrBz;
    }

    public void setLastyqwrdybrBz(boolean lastyqwrdybrBz) {
        this.lastyqwrdybrBz = lastyqwrdybrBz;
    }

    public List<String> getDjxhList() {
        return djxhList;
    }

    public void setDjxhList(List<String> djxhList) {
        this.djxhList = djxhList;
    }

    public List<String> getPzxhList() {
        return pzxhList;
    }

    public void setPzxhList(List<String> pzxhList) {
        this.pzxhList = pzxhList;
    }

    /**
     *创建时间:2020年5月9日上午11:18:45
     *get方法
     * @return the cxZrrForYjcx
     */
    public String getCxZrrForYjcx() {
        return this.cxZrrForYjcx;
    }

    /**
     * 创建时间:2020年5月9日上午11:18:45
     * set方法
     * @param cxZrrForYjcx the cxZrrForYjcx to set
     */
    public void setCxZrrForYjcx(String cxZrrForYjcx) {
        this.cxZrrForYjcx = cxZrrForYjcx;
    }

    /**
     *创建时间:2020年7月2日下午3:42:01
     *get方法
     * @return the noSbbZfYjlsz
     */
    public boolean isNoSbbZfYjlsz() {
        return noSbbZfYjlsz;
    }

    /**
     * 创建时间:2020年7月2日下午3:42:01
     * set方法
     * @param noSbbZfYjlsz the noSbbZfYjlsz to set
     */
    public void setNoSbbZfYjlsz(boolean noSbbZfYjlsz) {
        this.noSbbZfYjlsz = noSbbZfYjlsz;
    }

    public String getShxydm() {
        return shxydm;
    }

    public void setShxydm(String shxydm) {
        this.shxydm = shxydm;
    }

    public String getByhDjxhs() {
        return byhDjxhs;
    }

    public void setByhDjxhs(String byhDjxhs) {
        this.byhDjxhs = byhDjxhs;
    }
    
}
