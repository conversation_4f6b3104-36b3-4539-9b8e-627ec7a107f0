package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.redis.utils.RedisUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.api.WsbfzjgxxcxFeignClient;
import com.css.znsb.nssb.pojo.dto.wsbfzjgxx.WsbfzjgDTO;
import com.css.znsb.nssb.pojo.dto.wsbfzjgxx.WsbfzjgcxReqDTO;
import com.css.znsb.nssb.pojo.dto.wsbfzjgxx.WsbfzjgqcDTO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ZzsybnsrYzlZfjgxxGjJob {

    @Resource
    private CompanyApi companyApi;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private WsbfzjgxxcxFeignClient wsbfzjgxxcxFeignClient;

    @XxlJob("zzsybnsrYzlZfjgxxGjJob")
    public void execute() {
        log.info("开始归集增值税一般纳税人预征率总分机构信息");
        CommonResult<List<CompanyInfoResDTO>> result = companyApi.getAllCompanyInfo();
        if (!result.isSuccess() || GyUtils.isNull(result.getData())) {
            log.info("获取全部企业信息失败，返回结果为空，结束执行");
            return;
        }

        // 获取当前日期并减去一个月
        LocalDate lastMonth = LocalDate.now().minusMonths(1);
        // 获取上个月第一天
        LocalDate firstDay = lastMonth.with(TemporalAdjusters.firstDayOfMonth());
        // 获取上个月最后一天
        LocalDate lastDay = lastMonth.with(TemporalAdjusters.lastDayOfMonth());

        String skssqq = firstDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String skssqz = lastDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<CompanyInfoResDTO> companyInfoList = result.getData();
        for (CompanyInfoResDTO companyInfo : companyInfoList) {
            ZnsbMhzcQyjbxxmxReqVO qyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            qyjbxxmxReqVO.setDjxh(companyInfo.getDjxh());
            qyjbxxmxReqVO.setNsrsbh(companyInfo.getNsrsbh());
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByDjxh(qyjbxxmxReqVO);
            if (!nsrxxResult.isSuccess() || GyUtils.isNull(nsrxxResult.getData()) || GyUtils.isNull(nsrxxResult.getData().getJbxxmxsj())) {
                log.info("通过登记序号：{}，纳税人识别号：{}，获取纳税人信息失败，跳过执行。", companyInfo.getDjxh(), companyInfo.getNsrsbh());
                continue;
            }
            List<JbxxmxsjVO> nsrjbxxList = nsrxxResult.getData().getJbxxmxsj();
            JbxxmxsjVO nsrjbxx = nsrjbxxList.stream()
                    .filter(t -> "1".equals(t.getZfjglxDm()) || "3".equals(t.getZfjglxDm()))
                    .findFirst()
                    .orElse(null);
            if (GyUtils.isNull(nsrjbxx)) {
                log.info("纳税人识别号：{}，登记序号：{}，非总机构或分总机构，跳过执行。", companyInfo.getNsrsbh(), companyInfo.getDjxh());
                continue;
            }

            WsbfzjgcxReqDTO queryWsbdfzjgqcReqDTO = new WsbfzjgcxReqDTO();
            queryWsbdfzjgqcReqDTO.setNsrsbh(nsrjbxx.getNsrsbh());
            queryWsbdfzjgqcReqDTO.setXzqhszDm(companyInfo.getXzqhszDm());
            queryWsbdfzjgqcReqDTO.setDjxh(nsrjbxx.getDjxh());
            queryWsbdfzjgqcReqDTO.setSkssqq(skssqq);
            queryWsbdfzjgqcReqDTO.setSkssqz(skssqz);
            CommonResult<WsbfzjgqcDTO> wsbfzjgxxResult = wsbfzjgxxcxFeignClient.listWsbfzjgxx(queryWsbdfzjgqcReqDTO);
            if (!wsbfzjgxxResult.isSuccess() || GyUtils.isNull(wsbfzjgxxResult.getData()) || GyUtils.isNull(wsbfzjgxxResult.getData().getWsbfzjgList())) {
                log.info("未申报分支机构清册为空，跳过执行。");
                continue;
            }
            List<WsbfzjgDTO> wsbfzjgList = wsbfzjgxxResult.getData().getWsbfzjgList();
            for (WsbfzjgDTO wsbfzjgDTO : wsbfzjgList) {
                Map<String, Object> wsbfzjgMap = new HashMap<>(6);
                wsbfzjgMap.put("zjgdjxh", nsrjbxx.getDjxh());
                wsbfzjgMap.put("zjgnsrsbh", nsrjbxx.getNsrsbh());
                wsbfzjgMap.put("fzjgnsrsbh", wsbfzjgDTO.getFzjgNsrsbh());
                wsbfzjgMap.put("fzjgnsrmc", wsbfzjgDTO.getFzjgNsrmc());
                wsbfzjgMap.put("fzjgdz", wsbfzjgDTO.getFzjgDz());
                wsbfzjgMap.put("fzjgzgswjgmc", wsbfzjgDTO.getFzjgZgswjmc());
                RedisUtils.putHash("zzs:yzlzfjgxx:" + wsbfzjgDTO.getFzjgNsrsbh(), wsbfzjgMap);
            }
        }
    }
}
