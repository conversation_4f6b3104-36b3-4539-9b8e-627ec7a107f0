package com.css.znsb.nssb.pojo.bo.hxzg.dj.general;

import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * @project 金税三期工程核心征管及应用总集成项目
 * @package gov.gt3.vo.nsrgl.dj
 * @file DJNsrxxVO.java 创建时间:2014-7-4下午02:04:01
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2014 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR> 刘元宏
 * @reviewer 审核人
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class DJNsrxxVO extends TaxBaseVO {

    private static final long serialVersionUID = 5102454195654018577L;

    //登记序号
    private BigDecimal djxh;

    //国地税类型代码 
    private String gdslxDm;

    //税收档案编号
    private String ssdabh;

    //纳税人识别号
    private String nsrsbh;

    //纳税人名称
    private String nsrmc;

    //课征主体登记类型代码
    private String kzztdjlxDm;

    //登记注册类型代码
    private String djzclxDm;

    //法定代表人姓名
    private String fddbrxm;

    //法定代表人身份证件类型代码
    private String fddbrsfzjlxDm;

    //生产经营地址
    private String scjydz;

    //法定代表人身份证号码
    private String fddbrsfzjhm;

    //生产经营地址行政区划数字代码
    private String scjydzxzqhszDm;

    //纳税人状态代码
    private String nsrztDm;

    //行业代码
    private String hyDm;

    //注册地址
    private String zcdz;

    //注册地址行政区划数字代码
    private String zcdzxzqhszDm;

    //街道乡镇代码
    private String jdxzDm;

    //单位隶属关系代码
    private String dwlsgxDm;

    //国地管户类型代码
    private String gdghlxDm;

    //登记机关代码
    private String djjgDm;

    //登记日期
    private Date djrq;

    //组织机构代码
    private String zzjgDm;

    //跨区财产税主体登记标志
    private String kqccsztdjbz;

    //户籍所在地
    private String hjszd;

    //经营范围
    private String jyfw;

    //注册地联系电话
    private String zcdlxdh;

    //注册地邮政编码
    private String zcdyzbm;

    //生产经营地联系电话
    private String scjydlxdh;

    //生产经营地邮政编码
    private String scjydyzbm;

    //核算方式代码
    private String hsfsDm;

    //从业人数
    private Double cyrs;

    //外籍从业人数
    private Double wjcyrs;

    //合伙人数
    private Double hhrs;

    //雇工人数
    private Double ggrs;

    //固定工人数
    private Double gdgrs;

    //组织机构类型代码
    private String zzjglxDm;

    //会计制度（准则）代码
    private String kjzdzzDm;

    //网址
    private String wz;

    //税务代理人联系电话
    private String swdlrlxdh;

    //税务代理人电子信箱
    private String swdlrdzxx;

    //注册资本
    private Double zczb;

    //投资总额
    private Double tzze;

    //自然人投资比例
    private Double zrrtzbl;

    //外资投资比例
    private Double wztzbl;

    //国有投资比例
    private Double gytzbl;

    //国有控股类型代码
    private String gykglxDm;

    //总分机构类型代码
    private String zfjglxDm;

    //办证方式代码
    private String bzfsDm;

    //法定代表人固定电话
    private String fddbrgddh;

    //法定代表人移动电话
    private String fddbryddh;

    //法定代表人电子信箱
    private String fddbrdzxx;

    //财务负责人姓名
    private String cwfzrxm;

    //财务负责人身份证件种类代码
    private String cwfzrsfzjzlDm;

    //财务负责人身份证件号码
    private String cwfzrsfzjhm;

    //财务负责人固定电话
    private String cwfzrgddh;

    //财务负责人移动电话
    private String cwfzryddh;

    //财务负责人电子信箱
    private String cwfzrdzxx;

    //办税人姓名
    private String bsrxm;

    //办税人身份证件种类代码
    private String bsrsfzjzlDm;

    //办税人身份证件号码
    private String bsrsfzjhm;

    //办税人固定电话
    private String bsrgddh;

    //办税人移动电话
    private String bsryddh;

    //办税人电子信箱
    private String bsrdzxx;

    //临时税务登记有效期起
    private Date lsswdjyxqq;

    //临时税务登记有效期止
    private Date lsswdjyxqz;

    //税务代理人纳税人识别号
    private String swdlrnsrsbh;

    //税务代理人名称
    private String swdlrmc;


    //文化事业建设费缴费信息登记标志
    private String whsyjsfjfxxdjbz;

    //增值税经营类别
    private String zzsjylb;

    //印花税缴纳方式代码
    private String yhsjnfsDm;

    //征收项目城乡标志代码
    private String zsxmcxbzDm;

    //增值税企业类型代码
    private String zzsqylxDm;

    //国家或地区数字代码
    private String gjhdqszDm;

    //营改增纳税人类型代码
    private String ygznsrlxDm;

    //纳税人主管税务机关信息集合
    private List<DJNsrzgswjgxxVO> nsrzgswjgxxList;

    //纳税人附营行业信息集合
    private List<DJNsrfyhyxxVO> nsrfyhyxxList;

    //登记注册类型名称
    private String djzclxmc;
    //纳税人状态mc
    private String nsrztmc;
    //主行业名称
    private String hymc;
    //课征主题名称
    private String kzztdjlxmc;
    //生产家经营的行政区划
    private String scjydzxzqhszmc;
    //注册的行政区划
    private String zcdzxzqhszmc;
    //法定人身份证种类名称
    private String fddbrsfzjlxmc;
    //主管税务局
    private String zgswjDm;
    //主管水务科所
    private String zgswskfjDm;
    //税收管理员
    private String ssglyDm;
    //非居民企业标志
    private String fjmqybz;
    //税务登记补录标志
    private String swdjblbz;
    //街道乡镇名称
    private String jdxzmc;
    
  //录入日期
    private String lrrq;
    private String lrrDm;
    
    //主管税务局名称
    private String zgswjmc;
    //主管税务科所名称
    private String zgswskfjmc;
    //税收管理员名称
    private String ssglymc;
    //纳税人编号
    private String nsrbm;
    private String wcjyhdssglzmbh;
    //社会信用代码
    private String shxydm;
    //登记机关
    private String djjgmc;
    
    //原纳税人识别号
    private String ynsrsbh;
    
  //国税纳税人登记序号
    private String gsdjxh;
    
    private String pzsljglxDm;
    private String pzsljgDm;
    private String pzsljgmc;
    private String pzzmhwjh;
    private String zzlxDm;
    private String zzhm;
    private Date kyslrq;
    private Date scjyqxq;
    private Date scjyqxz;
    
    private String gszgswjDm;
    private String gszgswskfjDm;
    
    private String pgjgDm;
    
    private String zcxs;
    //纳税人英文名称
    private String nsrywmc;
    //纳税人主体类型代码
    private String nsrztlxDm;
    //
    private String szgjdqnsrsbh;
    //
    private String ywzcdz;

    private String fddbrzs;
    //跨省纳税人标志
    private String ksnsrbz;
    //企业划型类别
    private String qyhxlbDm;
    //企业划型来源
    private String qyhxly;
    //工商录入日期（定时任务用）
    private String gslrrq;
    //受托方shxydm
    private String stfshxydm;
    //白名单生产企业编码
    private String bjscqybm;
    //市监市场主体乐星代码
    private String sjscztlxDm;
    //市监行业代码
    private String sjhyDm1;

    /**
     * @return the nsrztlxDm
     */
    public String getNsrztlxDm() {
        return nsrztlxDm;
    }

    /**
     * @param nsrztlxDm the nsrztlxDm to set
     */
    public void setNsrztlxDm(String nsrztlxDm) {
        this.nsrztlxDm = nsrztlxDm;
    }

    public String getNsrywmc() {
        return nsrywmc;
    }

    public void setNsrywmc(String nsrywmc) {
        this.nsrywmc = nsrywmc;
    }

    /**
     *创建时间:2016-9-9下午04:14:48
     *get方法
     * @return the zcxs
     */
    public String getZcxs() {
        return zcxs;
    }

    /**
     * 创建时间:2016-9-9下午04:14:48
     * set方法
     * @param zcxs the zcxs to set
     */
    public void setZcxs(String zcxs) {
        this.zcxs = zcxs;
    }

    /**
     *创建时间:2016-8-16上午10:57:16
     *get方法
     * @return the pgjgDm
     */
    public String getPgjgDm() {
        return pgjgDm;
    }

    /**
     * 创建时间:2016-8-16上午10:57:16
     * set方法
     * @param pgjgDm the pgjgDm to set
     */
    public void setPgjgDm(String pgjgDm) {
        this.pgjgDm = pgjgDm;
    }

    public String getGszgswjDm() {
        return gszgswjDm;
    }

    public void setGszgswjDm(String gszgswjDm) {
        this.gszgswjDm = gszgswjDm;
    }

    public String getGszgswskfjDm() {
        return gszgswskfjDm;
    }

    public void setGszgswskfjDm(String gszgswskfjDm) {
        this.gszgswskfjDm = gszgswskfjDm;
    }

    public String getGsdjxh() {
        return gsdjxh;
    }

    public void setGsdjxh(String gsdjxh) {
        this.gsdjxh = gsdjxh;
    }

    public String getYnsrsbh() {
        return ynsrsbh;
    }

    public void setYnsrsbh(String ynsrsbh) {
        this.ynsrsbh = ynsrsbh;
    }

    public String getDjjgmc() {
        return djjgmc;
    }

    public void setDjjgmc(String djjgmc) {
        this.djjgmc = djjgmc;
    }

    public String getShxydm() {
        return shxydm;
    }

    public void setShxydm(String shxydm) {
        this.shxydm = shxydm;
    }

    public String getWcjyhdssglzmbh() {
        return wcjyhdssglzmbh;
    }

    public void setWcjyhdssglzmbh(String wcjyhdssglzmbh) {
        this.wcjyhdssglzmbh = wcjyhdssglzmbh;
    }

    public String getNsrbm() {
        return nsrbm;
    }

    public void setNsrbm(String nsrbm) {
        this.nsrbm = nsrbm;
    }

    public String getPzsljglxDm() {
        return pzsljglxDm;
    }

    public void setPzsljglxDm(String pzsljglxDm) {
        this.pzsljglxDm = pzsljglxDm;
    }

    public String getPzsljgDm() {
        return pzsljgDm;
    }

    public void setPzsljgDm(String pzsljgDm) {
        this.pzsljgDm = pzsljgDm;
    }

    public String getPzsljgmc() {
        return pzsljgmc;
    }

    public void setPzsljgmc(String pzsljgmc) {
        this.pzsljgmc = pzsljgmc;
    }

    public String getPzzmhwjh() {
        return pzzmhwjh;
    }

    public void setPzzmhwjh(String pzzmhwjh) {
        this.pzzmhwjh = pzzmhwjh;
    }

    public String getZzlxDm() {
        return zzlxDm;
    }

    public void setZzlxDm(String zzlxDm) {
        this.zzlxDm = zzlxDm;
    }

    public String getZzhm() {
        return zzhm;
    }

    public void setZzhm(String zzhm) {
        this.zzhm = zzhm;
    }

    public Date getKyslrq() {
        return kyslrq;
    }

    public void setKyslrq(Date kyslrq) {
        this.kyslrq = kyslrq;
    }

    public Date getScjyqxq() {
        return scjyqxq;
    }

    public void setScjyqxq(Date scjyqxq) {
        this.scjyqxq = scjyqxq;
    }

    public Date getScjyqxz() {
        return scjyqxz;
    }

    public void setScjyqxz(Date scjyqxz) {
        this.scjyqxz = scjyqxz;
    }

   
    /**
     *创建时间:2014-9-16下午07:31:59
     *get方法
     * @return the zgswjmc
     */
    public String getZgswjmc() {
        return zgswjmc;
    }

    /**
     * 创建时间:2014-9-16下午07:31:59
     * set方法
     * @param zgswjmc the zgswjmc to set
     */
    public void setZgswjmc(String zgswjmc) {
        this.zgswjmc = zgswjmc;
    }

    /**
     *创建时间:2014-9-16下午07:31:59
     *get方法
     * @return the zgswskfjmc
     */
    public String getZgswskfjmc() {
        return zgswskfjmc;
    }

    /**
     * 创建时间:2014-9-16下午07:31:59
     * set方法
     * @param zgswskfjmc the zgswskfjmc to set
     */
    public void setZgswskfjmc(String zgswskfjmc) {
        this.zgswskfjmc = zgswskfjmc;
    }

    /**
     *创建时间:2014-9-16下午07:31:59
     *get方法
     * @return the ssglymc
     */
    public String getSsglymc() {
        return ssglymc;
    }

    /**
     * 创建时间:2014-9-16下午07:31:59
     * set方法
     * @param ssglymc the ssglymc to set
     */
    public void setSsglymc(String ssglymc) {
        this.ssglymc = ssglymc;
    }

    /**
     *创建时间:2014-9-3下午08:29:28
     *get方法
     * @return the lrrDm
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * 创建时间:2014-9-3下午08:29:28
     * set方法
     * @param lrrDm the lrrDm to set
     */
    public void setLrrDm(String lrrDm) {
        this.lrrDm = lrrDm;
    }

    public String getJdxzmc() {
        return jdxzmc;
    }

    public void setJdxzmc(String jdxzmc) {
        this.jdxzmc = jdxzmc;
    }

    /**
     *创建时间:2014-8-13下午04:56:37
     *get方法
     * @return the fjmqybz
     */
    public String getFjmqybz() {
        return fjmqybz;
    }

    /**
     * 创建时间:2014-8-13下午04:56:37
     * set方法
     * @param fjmqybz the fjmqybz to set
     */
    public void setFjmqybz(String fjmqybz) {
        this.fjmqybz = fjmqybz;
    }

    /**
     *创建时间:2014-8-13下午04:56:37
     *get方法
     * @return the swdjblbz
     */
    public String getSwdjblbz() {
        return swdjblbz;
    }

    /**
     * 创建时间:2014-8-13下午04:56:37
     * set方法
     * @param swdjblbz the swdjblbz to set
     */
    public void setSwdjblbz(String swdjblbz) {
        this.swdjblbz = swdjblbz;
    }

    /**
     *创建时间:2014-7-30下午11:25:24
     *get方法
     * @return the zgswjDm
     */
    public String getZgswjDm() {
        return zgswjDm;
    }

    /**
     * 创建时间:2014-7-30下午11:25:24
     * set方法
     * @param zgswjDm the zgswjDm to set
     */
    public void setZgswjDm(String zgswjDm) {
        this.zgswjDm = zgswjDm;
    }

    /**
     *创建时间:2014-7-30下午11:25:24
     *get方法
     * @return the zgswskfjDm
     */
    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    /**
     * 创建时间:2014-7-30下午11:25:24
     * set方法
     * @param zgswskfjDm the zgswskfjDm to set
     */
    public void setZgswskfjDm(String zgswskfjDm) {
        this.zgswskfjDm = zgswskfjDm;
    }

    /**
     *创建时间:2014-7-30下午11:25:24
     *get方法
     * @return the ssglyDm
     */
    public String getSsglyDm() {
        return ssglyDm;
    }

    /**
     * 创建时间:2014-7-30下午11:25:24
     * set方法
     * @param ssglyDm the ssglyDm to set
     */
    public void setSsglyDm(String ssglyDm) {
        this.ssglyDm = ssglyDm;
    }

    /**
     *创建时间:2014-7-30下午11:09:14
     *get方法
     * @return the djzclxmc
     */
    public String getDjzclxmc() {
        return djzclxmc;
    }

    /**
     * 创建时间:2014-7-30下午11:09:14
     * set方法
     * @param djzclxmc the djzclxmc to set
     */
    public void setDjzclxmc(String djzclxmc) {
        this.djzclxmc = djzclxmc;
    }

    /**
     *创建时间:2014-7-30下午11:09:14
     *get方法
     * @return the nsrztmc
     */
    public String getNsrztmc() {
        return nsrztmc;
    }

    /**
     * 创建时间:2014-7-30下午11:09:14
     * set方法
     * @param nsrztmc the nsrztmc to set
     */
    public void setNsrztmc(String nsrztmc) {
        this.nsrztmc = nsrztmc;
    }

    /**
     *创建时间:2014-7-30下午11:09:14
     *get方法
     * @return the hymc
     */
    public String getHymc() {
        return hymc;
    }

    /**
     * 创建时间:2014-7-30下午11:09:14
     * set方法
     * @param hymc the hymc to set
     */
    public void setHymc(String hymc) {
        this.hymc = hymc;
    }

    /**
     *创建时间:2014-7-30下午11:09:14
     *get方法
     * @return the kzztdjlxmc
     */
    public String getKzztdjlxmc() {
        return kzztdjlxmc;
    }

    /**
     * 创建时间:2014-7-30下午11:09:14
     * set方法
     * @param kzztdjlxmc the kzztdjlxmc to set
     */
    public void setKzztdjlxmc(String kzztdjlxmc) {
        this.kzztdjlxmc = kzztdjlxmc;
    }

    /**
     *创建时间:2014-7-30下午11:09:14
     *get方法
     * @return the scjydzxzqhszmc
     */
    public String getScjydzxzqhszmc() {
        return scjydzxzqhszmc;
    }

    /**
     * 创建时间:2014-7-30下午11:09:14
     * set方法
     * @param scjydzxzqhszmc the scjydzxzqhszmc to set
     */
    public void setScjydzxzqhszmc(String scjydzxzqhszmc) {
        this.scjydzxzqhszmc = scjydzxzqhszmc;
    }

    /**
     *创建时间:2014-7-30下午11:09:14
     *get方法
     * @return the zcdzxzqhszmc
     */
    public String getZcdzxzqhszmc() {
        return zcdzxzqhszmc;
    }

    /**
     * 创建时间:2014-7-30下午11:09:14
     * set方法
     * @param zcdzxzqhszmc the zcdzxzqhszmc to set
     */
    public void setZcdzxzqhszmc(String zcdzxzqhszmc) {
        this.zcdzxzqhszmc = zcdzxzqhszmc;
    }

    /**
     *创建时间:2014-7-30下午11:09:14
     *get方法
     * @return the fddbrsfzjlxmc
     */
    public String getFddbrsfzjlxmc() {
        return fddbrsfzjlxmc;
    }

    /**
     * 创建时间:2014-7-30下午11:09:14
     * set方法
     * @param fddbrsfzjlxmc the fddbrsfzjlxmc to set
     */
    public void setFddbrsfzjlxmc(String fddbrsfzjlxmc) {
        this.fddbrsfzjlxmc = fddbrsfzjlxmc;
    }

    /**
     *创建时间:2014-7-8下午10:22:12
     *get方法
     * @return the gdslxDm
     */
    public String getGdslxDm() {
        return gdslxDm;
    }

    /**
     * 创建时间:2014-7-8下午10:22:12
     * set方法
     * @param gdslxDm the gdslxDm to set
     */
    public void setGdslxDm(String gdslxDm) {
        this.gdslxDm = gdslxDm;
    }

    public List<DJNsrzgswjgxxVO> getNsrzgswjgxxList() {
        return nsrzgswjgxxList;
    }

    public void setNsrzgswjgxxList(List<DJNsrzgswjgxxVO> nsrzgswjgxxList) {
        this.nsrzgswjgxxList = nsrzgswjgxxList;
    }

    public List<DJNsrfyhyxxVO> getNsrfyhyxxList() {
        return nsrfyhyxxList;
    }

    public void setNsrfyhyxxList(List<DJNsrfyhyxxVO> nsrfyhyxxList) {
        this.nsrfyhyxxList = nsrfyhyxxList;
    }

    public BigDecimal getDjxh() {
        return djxh;
    }

    public void setDjxh(BigDecimal djxh) {
        this.djxh = djxh;
    }

    public String getSsdabh() {
        return ssdabh;
    }

    public void setSsdabh(String ssdabh) {
        this.ssdabh = ssdabh;
    }

    public String getNsrsbh() {
        return nsrsbh;
    }

    public void setNsrsbh(String nsrsbh) {
        this.nsrsbh = nsrsbh;
    }

    public String getNsrmc() {
        return nsrmc;
    }

    public void setNsrmc(String nsrmc) {
        this.nsrmc = nsrmc;
    }

    public String getKzztdjlxDm() {
        return kzztdjlxDm;
    }

    public void setKzztdjlxDm(String kzztdjlxDm) {
        this.kzztdjlxDm = kzztdjlxDm;
    }

    public String getDjzclxDm() {
        return djzclxDm;
    }

    public void setDjzclxDm(String djzclxDm) {
        this.djzclxDm = djzclxDm;
    }

    public String getFddbrxm() {
        return fddbrxm;
    }

    public void setFddbrxm(String fddbrxm) {
        this.fddbrxm = fddbrxm;
    }

    public String getFddbrsfzjlxDm() {
        return fddbrsfzjlxDm;
    }

    public void setFddbrsfzjlxDm(String fddbrsfzjlxDm) {
        this.fddbrsfzjlxDm = fddbrsfzjlxDm;
    }

    public String getScjydz() {
        return scjydz;
    }

    public void setScjydz(String scjydz) {
        this.scjydz = scjydz;
    }

    public String getFddbrsfzjhm() {
        return fddbrsfzjhm;
    }

    public void setFddbrsfzjhm(String fddbrsfzjhm) {
        this.fddbrsfzjhm = fddbrsfzjhm;
    }

    public String getScjydzxzqhszDm() {
        return scjydzxzqhszDm;
    }

    public void setScjydzxzqhszDm(String scjydzxzqhszDm) {
        this.scjydzxzqhszDm = scjydzxzqhszDm;
    }

    public String getNsrztDm() {
        return nsrztDm;
    }

    public void setNsrztDm(String nsrztDm) {
        this.nsrztDm = nsrztDm;
    }

    public String getHyDm() {
        return hyDm;
    }

    public void setHyDm(String hyDm) {
        this.hyDm = hyDm;
    }

    public String getZcdz() {
        return zcdz;
    }

    public void setZcdz(String zcdz) {
        this.zcdz = zcdz;
    }

    public String getZcdzxzqhszDm() {
        return zcdzxzqhszDm;
    }

    public void setZcdzxzqhszDm(String zcdzxzqhszDm) {
        this.zcdzxzqhszDm = zcdzxzqhszDm;
    }

    public String getJdxzDm() {
        return jdxzDm;
    }

    public void setJdxzDm(String jdxzDm) {
        this.jdxzDm = jdxzDm;
    }

    public String getDwlsgxDm() {
        return dwlsgxDm;
    }

    public void setDwlsgxDm(String dwlsgxDm) {
        this.dwlsgxDm = dwlsgxDm;
    }

    public String getGdghlxDm() {
        return gdghlxDm;
    }

    public void setGdghlxDm(String gdghlxDm) {
        this.gdghlxDm = gdghlxDm;
    }

    public String getDjjgDm() {
        return djjgDm;
    }

    public void setDjjgDm(String djjgDm) {
        this.djjgDm = djjgDm;
    }

    public Date getDjrq() {
        return djrq;
    }

    public void setDjrq(Date djrq) {
        this.djrq = djrq;
    }

    public String getZzjgDm() {
        return zzjgDm;
    }

    public void setZzjgDm(String zzjgDm) {
        this.zzjgDm = zzjgDm;
    }

    public String getKqccsztdjbz() {
        return kqccsztdjbz;
    }

    public void setKqccsztdjbz(String kqccsztdjbz) {
        this.kqccsztdjbz = kqccsztdjbz;
    }

    public String getHjszd() {
        return hjszd;
    }

    public void setHjszd(String hjszd) {
        this.hjszd = hjszd;
    }

    public String getJyfw() {
        return jyfw;
    }

    public void setJyfw(String jyfw) {
        this.jyfw = jyfw;
    }

    public String getZcdlxdh() {
        return zcdlxdh;
    }

    public void setZcdlxdh(String zcdlxdh) {
        this.zcdlxdh = zcdlxdh;
    }

    public String getZcdyzbm() {
        return zcdyzbm;
    }

    public void setZcdyzbm(String zcdyzbm) {
        this.zcdyzbm = zcdyzbm;
    }

    public String getScjydlxdh() {
        return scjydlxdh;
    }

    public void setScjydlxdh(String scjydlxdh) {
        this.scjydlxdh = scjydlxdh;
    }

    public String getScjydyzbm() {
        return scjydyzbm;
    }

    public void setScjydyzbm(String scjydyzbm) {
        this.scjydyzbm = scjydyzbm;
    }

    public String getHsfsDm() {
        return hsfsDm;
    }

    public void setHsfsDm(String hsfsDm) {
        this.hsfsDm = hsfsDm;
    }

    public Double getCyrs() {
        return cyrs;
    }

    public void setCyrs(Double cyrs) {
        this.cyrs = cyrs;
    }

    public Double getWjcyrs() {
        return wjcyrs;
    }

    public void setWjcyrs(Double wjcyrs) {
        this.wjcyrs = wjcyrs;
    }

    public Double getHhrs() {
        return hhrs;
    }

    public void setHhrs(Double hhrs) {
        this.hhrs = hhrs;
    }

    public Double getGgrs() {
        return ggrs;
    }

    public void setGgrs(Double ggrs) {
        this.ggrs = ggrs;
    }

    public Double getGdgrs() {
        return gdgrs;
    }

    public void setGdgrs(Double gdgrs) {
        this.gdgrs = gdgrs;
    }

    public String getZzjglxDm() {
        return zzjglxDm;
    }

    public void setZzjglxDm(String zzjglxDm) {
        this.zzjglxDm = zzjglxDm;
    }

    public String getKjzdzzDm() {
        return kjzdzzDm;
    }

    public void setKjzdzzDm(String kjzdzzDm) {
        this.kjzdzzDm = kjzdzzDm;
    }

    public String getWz() {
        return wz;
    }

    public void setWz(String wz) {
        this.wz = wz;
    }

    public String getSwdlrlxdh() {
        return swdlrlxdh;
    }

    public void setSwdlrlxdh(String swdlrlxdh) {
        this.swdlrlxdh = swdlrlxdh;
    }

    public String getSwdlrdzxx() {
        return swdlrdzxx;
    }

    public void setSwdlrdzxx(String swdlrdzxx) {
        this.swdlrdzxx = swdlrdzxx;
    }

    public Double getZczb() {
        return zczb;
    }

    public void setZczb(Double zczb) {
        this.zczb = zczb;
    }

    public Double getTzze() {
        return tzze;
    }

    public void setTzze(Double tzze) {
        this.tzze = tzze;
    }

    public Double getZrrtzbl() {
        return zrrtzbl;
    }

    public void setZrrtzbl(Double zrrtzbl) {
        this.zrrtzbl = zrrtzbl;
    }

    public Double getWztzbl() {
        return wztzbl;
    }

    public void setWztzbl(Double wztzbl) {
        this.wztzbl = wztzbl;
    }

    public Double getGytzbl() {
        return gytzbl;
    }

    public void setGytzbl(Double gytzbl) {
        this.gytzbl = gytzbl;
    }

    public String getGykglxDm() {
        return gykglxDm;
    }

    public void setGykglxDm(String gykglxDm) {
        this.gykglxDm = gykglxDm;
    }

    public String getZfjglxDm() {
        return zfjglxDm;
    }

    public void setZfjglxDm(String zfjglxDm) {
        this.zfjglxDm = zfjglxDm;
    }

    public String getBzfsDm() {
        return bzfsDm;
    }

    public void setBzfsDm(String bzfsDm) {
        this.bzfsDm = bzfsDm;
    }

    public String getFddbrgddh() {
        return fddbrgddh;
    }

    public void setFddbrgddh(String fddbrgddh) {
        this.fddbrgddh = fddbrgddh;
    }

    public String getFddbryddh() {
        return fddbryddh;
    }

    public void setFddbryddh(String fddbryddh) {
        this.fddbryddh = fddbryddh;
    }

    public String getFddbrdzxx() {
        return fddbrdzxx;
    }

    public void setFddbrdzxx(String fddbrdzxx) {
        this.fddbrdzxx = fddbrdzxx;
    }

    public String getCwfzrxm() {
        return cwfzrxm;
    }

    public void setCwfzrxm(String cwfzrxm) {
        this.cwfzrxm = cwfzrxm;
    }

    public String getCwfzrsfzjzlDm() {
        return cwfzrsfzjzlDm;
    }

    public void setCwfzrsfzjzlDm(String cwfzrsfzjzlDm) {
        this.cwfzrsfzjzlDm = cwfzrsfzjzlDm;
    }

    public String getCwfzrsfzjhm() {
        return cwfzrsfzjhm;
    }

    public void setCwfzrsfzjhm(String cwfzrsfzjhm) {
        this.cwfzrsfzjhm = cwfzrsfzjhm;
    }

    public String getCwfzrgddh() {
        return cwfzrgddh;
    }

    public void setCwfzrgddh(String cwfzrgddh) {
        this.cwfzrgddh = cwfzrgddh;
    }

    public String getCwfzryddh() {
        return cwfzryddh;
    }

    public void setCwfzryddh(String cwfzryddh) {
        this.cwfzryddh = cwfzryddh;
    }

    public String getCwfzrdzxx() {
        return cwfzrdzxx;
    }

    public void setCwfzrdzxx(String cwfzrdzxx) {
        this.cwfzrdzxx = cwfzrdzxx;
    }

    public String getBsrxm() {
        return bsrxm;
    }

    public void setBsrxm(String bsrxm) {
        this.bsrxm = bsrxm;
    }

    public String getBsrsfzjzlDm() {
        return bsrsfzjzlDm;
    }

    public void setBsrsfzjzlDm(String bsrsfzjzlDm) {
        this.bsrsfzjzlDm = bsrsfzjzlDm;
    }

    public String getBsrsfzjhm() {
        return bsrsfzjhm;
    }

    public void setBsrsfzjhm(String bsrsfzjhm) {
        this.bsrsfzjhm = bsrsfzjhm;
    }

    public String getBsrgddh() {
        return bsrgddh;
    }

    public void setBsrgddh(String bsrgddh) {
        this.bsrgddh = bsrgddh;
    }

    public String getBsryddh() {
        return bsryddh;
    }

    public void setBsryddh(String bsryddh) {
        this.bsryddh = bsryddh;
    }

    public String getBsrdzxx() {
        return bsrdzxx;
    }

    public void setBsrdzxx(String bsrdzxx) {
        this.bsrdzxx = bsrdzxx;
    }

    public Date getLsswdjyxqq() {
        return lsswdjyxqq;
    }

    public void setLsswdjyxqq(Date lsswdjyxqq) {
        this.lsswdjyxqq = lsswdjyxqq;
    }

    public Date getLsswdjyxqz() {
        return lsswdjyxqz;
    }

    public void setLsswdjyxqz(Date lsswdjyxqz) {
        this.lsswdjyxqz = lsswdjyxqz;
    }

    public String getSwdlrnsrsbh() {
        return swdlrnsrsbh;
    }

    public void setSwdlrnsrsbh(String swdlrnsrsbh) {
        this.swdlrnsrsbh = swdlrnsrsbh;
    }

    public String getSwdlrmc() {
        return swdlrmc;
    }

    public void setSwdlrmc(String swdlrmc) {
        this.swdlrmc = swdlrmc;
    }
    public String getWhsyjsfjfxxdjbz() {
        return whsyjsfjfxxdjbz;
    }

    public void setWhsyjsfjfxxdjbz(String whsyjsfjfxxdjbz) {
        this.whsyjsfjfxxdjbz = whsyjsfjfxxdjbz;
    }

    public String getZzsjylb() {
        return zzsjylb;
    }

    public void setZzsjylb(String zzsjylb) {
        this.zzsjylb = zzsjylb;
    }

    public String getYhsjnfsDm() {
        return yhsjnfsDm;
    }

    public void setYhsjnfsDm(String yhsjnfsDm) {
        this.yhsjnfsDm = yhsjnfsDm;
    }

    public String getZsxmcxbzDm() {
        return zsxmcxbzDm;
    }

    public void setZsxmcxbzDm(String zsxmcxbzDm) {
        this.zsxmcxbzDm = zsxmcxbzDm;
    }

    public String getZzsqylxDm() {
        return zzsqylxDm;
    }

    public void setZzsqylxDm(String zzsqylxDm) {
        this.zzsqylxDm = zzsqylxDm;
    }

    public String getGjhdqszDm() {
        return gjhdqszDm;
    }

    public void setGjhdqszDm(String gjhdqszDm) {
        this.gjhdqszDm = gjhdqszDm;
    }

    public String getYgznsrlxDm() {
        return ygznsrlxDm;
    }

    public void setYgznsrlxDm(String ygznsrlxDm) {
        this.ygznsrlxDm = ygznsrlxDm;
    }

    /**
     *创建时间:2014-8-28下午03:57:30
     *get方法
     * @return the lrrq
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * 创建时间:2014-8-28下午03:57:30
     * set方法
     * @param lrrq the lrrq to set
     */
    public void setLrrq(String lrrq) {
        this.lrrq = lrrq;
    }
    public String getSzgjdqnsrsbh() {
        return szgjdqnsrsbh;
    }

    public void setSzgjdqnsrsbh(String ygznsrlxDm) {
        this.szgjdqnsrsbh = ygznsrlxDm;
    }
    public String getYwzcdz() {
        return ywzcdz;
    }

    public void setYwzcdz(String ygznsrlxDm) {
        this.ywzcdz = ygznsrlxDm;
    }

    /**
     *创建时间:2019-05-28下午04:10:58
     *get方法
     * @return the fddbrzs
     */
    public String getFddbrzs() { return fddbrzs; }

    /**
     * 创建时间:2019-05-28下午04:10:58
     * set方法
     * @param fddbrzs the fddbrzs to set
     */
    public void setFddbrzs(String fddbrzs) { this.fddbrzs = fddbrzs; }

    public String getKsnsrbz() {
        return ksnsrbz;
    }

    public void setKsnsrbz(String ksnsrbz) {
        this.ksnsrbz = ksnsrbz;
    }
    
    public String getQyhxlbDm(){
        return qyhxlbDm;
    }
    /**
     * set方法
     * @param value the qyhxlbDm to set
     */
    public void setQyhxlbDm(String value){
        this.qyhxlbDm = value ;
    }
    
    public String getQyhxly(){
        return qyhxly;
    }
    /**
     * set方法
     * @param value the setQyhxly to set
     */
    public void setQyhxly(String value){
        this.qyhxly = value ;
    }

    public String getGslrrq() {
        return gslrrq;
    }

    /**
     * set方法
     * @param gslrrq the gslrrq to set
     */
    public void setGslrrq(String gslrrq) {
        this.gslrrq = gslrrq;
    }
    
    public String getStfshxydm() {
        return stfshxydm;
    }
    public void setStfshxydm(String stfshxydm) {
        this.stfshxydm = stfshxydm;
    }

    public String getBjscqybm() {
        return bjscqybm;
    }
    public void setBjscqybm(String value) {
        this.bjscqybm = value;
    }

    public String getSjscztlxDm() {
        return sjscztlxDm;
    }

    public void setSjscztlxDm(String sjscztlxDm) {
        this.sjscztlxDm = sjscztlxDm;
    }

    public String getSjhyDm1() {
        return sjhyDm1;
    }

    public void setSjhyDm1(String sjhyDm1) {
        this.sjhyDm1 = sjhyDm1;
    }
}
