package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 五、公路运输
 *
 * <p>glysGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="glysGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="glysGridlb" type="{http://www.chinatax.gov.cn/dataspec/}glysGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "glysGrid", propOrder = { "glysGridlb" })
public class GlysGrid {
    @XmlElement(nillable = true, required = true)
    protected List<GlysGridlbVO> glysGridlb;

    /**
     * Gets the value of the glysGridlb property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the glysGridlb property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getGlysGridlb().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link GlysGridlbVO}
     */
    public List<GlysGridlbVO> getGlysGridlb() {
        if (glysGridlb == null) {
            glysGridlb = new ArrayList<GlysGridlbVO>();
        }
        return this.glysGridlb;
    }
}