package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 纳税人信息VO
 *
 * <p>NsrxxFormVO complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="NsrxxFormVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="nsrmc" type="{http://www.chinatax.gov.cn/dataspec/}nsrmc"/>
 *         &lt;element name="bsrq" type="{http://www.chinatax.gov.cn/dataspec/}bsrq"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz"/>
 *         &lt;element name="bbssq" type="{http://www.chinatax.gov.cn/dataspec/}bbssq"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NsrxxFormVO", propOrder = { "nsrsbh", "nsrmc", "bsrq", "skssqq", "skssqz", "bbssq" })
public class NsrxxFormVO {
    /**
     * 纳税人识别号
     */
    @XmlElement(nillable = true, required = true)
    protected String nsrsbh;

    /**
     * 纳税人名称
     */
    @XmlElement(nillable = true, required = true)
    protected String nsrmc;

    /**
     * 报送日期
     */
    @XmlElement(nillable = true, required = true)
    protected String bsrq;

    /**
     * 税款所属期起
     */
    @XmlElement(nillable = true, required = true)
    protected String skssqq;

    /**
     * 税款所属期止
     */
    @XmlElement(nillable = true, required = true)
    protected String skssqz;

    /**
     * 报表所属期（如：201401）
     */
    @XmlElement(nillable = true, required = true)
    protected String bbssq;

    /**
     * 获取nsrsbh属性的值。
     * <p>
     * 纳税人识别号
     */
    public String getNsrsbh() {
        return nsrsbh;
    }

    /**
     * 设置nsrsbh属性的值。
     */
    public void setNsrsbh(String value) {
        this.nsrsbh = value;
    }

    /**
     * 获取nsrmc属性的值。
     * <p>
     * 纳税人名称
     */
    public String getNsrmc() {
        return nsrmc;
    }

    /**
     * 设置nsrmc属性的值。
     */
    public void setNsrmc(String value) {
        this.nsrmc = value;
    }

    /**
     * 获取bsrq属性的值。
     * <p>
     * 报送日期
     */
    public String getBsrq() {
        return bsrq;
    }

    /**
     * 设置bsrq属性的值。
     */
    public void setBsrq(String value) {
        this.bsrq = value;
    }

    /**
     * 获取skssqq属性的值。
     * <p>
     * 税款所属期起
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * 设置skssqq属性的值。
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * 获取skssqz属性的值。
     * <p>
     * 税款所属期止
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * 设置skssqz属性的值。
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * 获取bbssq属性的值。
     * <p>
     * 报表所属期（如：201401）
     */
    public String getBbssq() {
        return bbssq;
    }

    /**
     * 设置bbssq属性的值。
     */
    public void setBbssq(String value) {
        this.bbssq = value;
    }
}