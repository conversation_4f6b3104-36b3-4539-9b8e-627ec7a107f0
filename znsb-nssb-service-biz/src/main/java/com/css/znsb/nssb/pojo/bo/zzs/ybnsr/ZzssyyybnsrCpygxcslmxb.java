package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《成品油购销存数量明细表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_cpygxcslmxb", propOrder = { "sbbHead", "cpygxcslmxbGrid" })
@XmlSeeAlso({ ZzssyyybnsrCpygxcslmxbywbw.ZzssyyybnsrCpygxcslmxb.class })
@Getter
@Setter
public class ZzssyyybnsrCpygxcslmxb {
    /**
     * 申报表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbHead;

    /**
     * 成品油购销存数量明细表
     */
    @XmlElement(nillable = true, required = true)
    protected CpygxcslmxbGrid cpygxcslmxbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "cpygxcslmxbGridlbVO" })
    @Getter
    @Setter
    public static class CpygxcslmxbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<CpygxcslmxbGridlbVO> cpygxcslmxbGridlbVO;

        /**
         * Gets the value of the cpygxcslmxbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the cpygxcslmxbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getCpygxcslmxbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link CpygxcslmxbGridlbVO}
         */
        public List<CpygxcslmxbGridlbVO> getCpygxcslmxbGridlbVO() {
            if (cpygxcslmxbGridlbVO == null) {
                cpygxcslmxbGridlbVO = new ArrayList<CpygxcslmxbGridlbVO>();
            }
            return this.cpygxcslmxbGridlbVO;
        }
    }
}