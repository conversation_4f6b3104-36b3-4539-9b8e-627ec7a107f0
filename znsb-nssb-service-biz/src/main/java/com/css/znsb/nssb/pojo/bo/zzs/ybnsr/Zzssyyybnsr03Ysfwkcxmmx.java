package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税纳税申报表附列资料三（应税服务扣除项目明细）
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr03_ysfwkcxmmx", propOrder = { "sbbhead", "ysfwkcxmmxGrid" })
@Getter
@Setter
public class Zzssyyybnsr03Ysfwkcxmmx {
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbhead;

    /**
     * 增值税纳税申报表附列资料三（应税服务扣除项目明细）
     */
    @XmlElement(nillable = true, required = true)
    protected YsfwkcxmmxGrid ysfwkcxmmxGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "ysfwkcxmmxGridlbVO" })
    @Getter
    @Setter
    public static class YsfwkcxmmxGrid {
        @XmlElement(nillable = true, required = true)
        protected List<YsfwkcxmmxGridlbVO> ysfwkcxmmxGridlbVO;

        /**
         * Gets the value of the ysfwkcxmmxGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the ysfwkcxmmxGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getYsfwkcxmmxGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link YsfwkcxmmxGridlbVO}
         */
        public List<YsfwkcxmmxGridlbVO> getYsfwkcxmmxGridlbVO() {
            if (ysfwkcxmmxGridlbVO == null) {
                ysfwkcxmmxGridlbVO = new ArrayList<YsfwkcxmmxGridlbVO>();
            }
            return this.ysfwkcxmmxGridlbVO;
        }
    }
}