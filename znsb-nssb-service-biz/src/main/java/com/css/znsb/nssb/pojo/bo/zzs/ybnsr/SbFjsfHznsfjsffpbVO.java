package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
/**
 * 汇总纳税附加税费分配表VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbFjsfHznsfjsffpbVO", propOrder = { "uuid", "sbuuid", "pzxh", "fzjgdjxh", "fzjgnsrsbh", "fzjgmc", "zsxmDm", "zspmDm", "jsyj", "sl1", "ybtse", "rdpzuuid" })
@Getter
@Setter
public class SbFjsfHznsfjsffpbVO {
    /**
     * UUID
     */
    @XmlElement(nillable = true, required = true)
    protected String uuid;

    /**
     * 申报UUID
     */
    @XmlElement(nillable = true, required = true)
    protected String sbuuid;

    /**
     * 凭证序号
     */
    @XmlElement(nillable = true, required = true)
    protected String pzxh;

    /**
     * 分支机构登记序号
     */
    @XmlElement(nillable = true, required = true)
    protected String fzjgdjxh;

    /**
     * 分支机构纳税人识别号
     */
    @XmlElement(nillable = true, required = true)
    protected String fzjgnsrsbh;

    /**
     * 分支机构名称
     */
    @XmlElement(nillable = true, required = true)
    protected String fzjgmc;

    /**
     * 征收项目代码
     */
    @XmlElement(nillable = true, required = true)
    protected String zsxmDm;

    /**
     * 征收品目代码
     */
    @XmlElement(nillable = true, required = true)
    protected String zspmDm;

    /**
     * 计税依据
     */
    protected BigDecimal jsyj;

    /**
     * 税率
     */
    protected BigDecimal sl1;

    /**
     * 应补(退)税额
     */
    protected BigDecimal ybtse;

    /**
     * 认定凭证UUID
     */
    @XmlElement(nillable = true, required = true)
    protected String rdpzuuid;

    /**
     * 普惠减免性质代码
     */
    private String phjmxzDm;

    /**
     * 普惠减征比例
     */
    private BigDecimal phjzbl;

    /**
     * 普惠减免税额
     */
    private BigDecimal phjmse;
}