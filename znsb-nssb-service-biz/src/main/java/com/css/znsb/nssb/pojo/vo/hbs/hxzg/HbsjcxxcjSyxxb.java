
package com.css.znsb.nssb.pojo.vo.hbs.hxzg;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 环境保护税基础信息税源信息表
 * 
 * <p>HbsjcxxcjSyxxb complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="HbsjcxxcjSyxxb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pwxkzxxuuid" type="{http://www.chinatax.gov.cn/dataspec/}pwxkzxxuuid" minOccurs="0"/>
 *         &lt;element name="wbjhpfkuuid" type="{http://www.chinatax.gov.cn/dataspec/}wbjhpfkuuid" minOccurs="0"/>
 *         &lt;element name="syyxqq" type="{http://www.chinatax.gov.cn/dataspec/}syyxqq" minOccurs="0"/>
 *         &lt;element name="scjydz" type="{http://www.chinatax.gov.cn/dataspec/}scjydz" minOccurs="0"/>
 *         &lt;element name="pwxkzbh" type="{http://www.chinatax.gov.cn/dataspec/}pwxkzbh" minOccurs="0"/>
 *         &lt;element name="gjhbjgDm" type="{http://www.chinatax.gov.cn/dataspec/}gjhbjgDm" minOccurs="0"/>
 *         &lt;element name="pfklxDm" type="{http://www.chinatax.gov.cn/dataspec/}pfklxDm" minOccurs="0"/>
 *         &lt;element name="pffsDm" type="{http://www.chinatax.gov.cn/dataspec/}pffsDm" minOccurs="0"/>
 *         &lt;element name="zywrwlbDm" type="{http://www.chinatax.gov.cn/dataspec/}zywrwlbDm" minOccurs="0"/>
 *         &lt;element name="yxqz" type="{http://www.chinatax.gov.cn/dataspec/}yxqz" minOccurs="0"/>
 *         &lt;element name="hgbhssybh" type="{http://www.chinatax.gov.cn/dataspec/}hgbhssybh" minOccurs="0"/>
 *         &lt;element name="jd2" type="{http://www.chinatax.gov.cn/dataspec/}jd2" minOccurs="0"/>
 *         &lt;element name="jd2d" type="{http://www.chinatax.gov.cn/dataspec/}jd2" minOccurs="0"/>
 *         &lt;element name="jd2f" type="{http://www.chinatax.gov.cn/dataspec/}jd2" minOccurs="0"/>
 *         &lt;element name="jd2m" type="{http://www.chinatax.gov.cn/dataspec/}jd2" minOccurs="0"/>
 *         &lt;element name="jdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}jdxzDm" minOccurs="0"/>
 *         &lt;element name="pfkbh" type="{http://www.chinatax.gov.cn/dataspec/}pfkbh" minOccurs="0"/>
 *         &lt;element name="pfkmc" type="{http://www.chinatax.gov.cn/dataspec/}pfkmc" minOccurs="0"/>
 *         &lt;element name="yxqq" type="{http://www.chinatax.gov.cn/dataspec/}yxqq" minOccurs="0"/>
 *         &lt;element name="syuuid" type="{http://www.chinatax.gov.cn/dataspec/}syuuid"/>
 *         &lt;element name="dqwrwpfklbDm" type="{http://www.chinatax.gov.cn/dataspec/}dqwrwpfklbDm" minOccurs="0"/>
 *         &lt;element name="wd" type="{http://www.chinatax.gov.cn/dataspec/}wd" minOccurs="0"/>
 *         &lt;element name="wdd" type="{http://www.chinatax.gov.cn/dataspec/}wd" minOccurs="0"/>
 *         &lt;element name="wdf" type="{http://www.chinatax.gov.cn/dataspec/}wd" minOccurs="0"/>
 *         &lt;element name="wdm" type="{http://www.chinatax.gov.cn/dataspec/}wd" minOccurs="0"/>
 *         &lt;element name="hbsjcxxuuid" type="{http://www.chinatax.gov.cn/dataspec/}hbsjcxxuuid"/>
 *         &lt;element name="xkzfbwybm" type="{http://www.chinatax.gov.cn/dataspec/}xkzfbwybm" minOccurs="0"/>
 *         &lt;element name="xgrq" type="{http://www.chinatax.gov.cn/dataspec/}xgrq" minOccurs="0"/>
 *         &lt;element name="lrrDm" type="{http://www.chinatax.gov.cn/dataspec/}lrrDm"/>
 *         &lt;element name="yxbz" type="{http://www.chinatax.gov.cn/dataspec/}yxbz" minOccurs="0"/>
 *         &lt;element name="zgswskfjDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswskfjDm" minOccurs="0"/>
 *         &lt;element name="wspfqxDm" type="{http://www.chinatax.gov.cn/dataspec/}wspfqxDm" minOccurs="0"/>
 *         &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm" minOccurs="0"/>
 *         &lt;element name="sjtbSj" type="{http://www.chinatax.gov.cn/dataspec/}sjtbSj" minOccurs="0"/>
 *         &lt;element name="sjgsdq" type="{http://www.chinatax.gov.cn/dataspec/}sjgsdq"/>
 *         &lt;element name="pfkwybm" type="{http://www.chinatax.gov.cn/dataspec/}pfkwybm" minOccurs="0"/>
 *         &lt;element name="xgrDm" type="{http://www.chinatax.gov.cn/dataspec/}xgrDm" minOccurs="0"/>
 *         &lt;element name="lrrq" type="{http://www.chinatax.gov.cn/dataspec/}lrrq"/>
 *         &lt;element name="syyxqz" type="{http://www.chinatax.gov.cn/dataspec/}syyxqz" minOccurs="0"/>
 *         &lt;element name="pfkwz" type="{http://www.chinatax.gov.cn/dataspec/}pfkwz" minOccurs="0"/>
 *         &lt;element name="wrwpfljsffDm" type="{http://www.chinatax.gov.cn/dataspec/}wrwpfljsffDm" minOccurs="0"/>
 *         &lt;element name="fzspmDm" type="{http://www.chinatax.gov.cn/dataspec/}fzspmDm" minOccurs="0"/>
 *         &lt;element name="zspmDm" type="{http://www.chinatax.gov.cn/dataspec/}zspmDm" minOccurs="0"/>
 *         &lt;element name="zszmDm" type="{http://www.chinatax.gov.cn/dataspec/}zszmDm" minOccurs="0"/>
 *         &lt;element name="sbbz" type="{http://www.chinatax.gov.cn/dataspec/}sbbz" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HbsjcxxcjSyxxb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "pwxkzxxuuid",
    "wbjhpfkuuid",
    "syyxqq",
    "scjydz",
    "pwxkzbh",
    "gjhbjgDm",
    "pfklxDm",
    "pffsDm",
    "zywrwlbDm",
    "yxqz",
    "hgbhssybh",
    "jd2",
    "jd2D",
    "jd2F",
    "jd2M",
    "jdxzDm",
    "pfkbh",
    "pfkmc",
    "yxqq",
    "syuuid",
    "dqwrwpfklbDm",
    "wd",
    "wdd",
    "wdf",
    "wdm",
    "hbsjcxxuuid",
    "xkzfbwybm",
    "xgrq",
    "lrrDm",
    "yxbz",
    "zgswskfjDm",
    "wspfqxDm",
    "xzqhszDm",
    "sjtbSj",
    "sjgsdq",
    "pfkwybm",
    "xgrDm",
    "lrrq",
    "syyxqz",
    "pfkwz",
    "wrwpfljsffDm",
    "fzspmDm",
    "zspmDm",
    "zszmDm",
    "sbbz"
})
public class HbsjcxxcjSyxxb
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pwxkzxxuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String wbjhpfkuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String syyxqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String scjydz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pwxkzbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String gjhbjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pfklxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pffsDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zywrwlbDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yxqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String hgbhssybh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jd2;
    @XmlElement(name = "jd2d", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jd2D;
    @XmlElement(name = "jd2f", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jd2F;
    @XmlElement(name = "jd2m", namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double jd2M;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String jdxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pfkbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pfkmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yxqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String syuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String dqwrwpfklbDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String wd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double wdd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double wdf;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected Double wdm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbsjcxxuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xkzfbwybm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String yxbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zgswskfjDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String wspfqxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xzqhszDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sjtbSj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sjgsdq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pfkwybm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xgrDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String lrrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String syyxqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pfkwz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String wrwpfljsffDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String fzspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zspmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zszmDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbbz;
    protected String hxbuuid;
    protected String bczt;

    public String getHxbuuid() {
        return hxbuuid;
    }
    public String getBczt() {
        return bczt;
    }
    public void setHxbuuid(String hxbuuid) {
        this.hxbuuid = hxbuuid;
    }
    public void setBczt(String bczt) {
        this.bczt = bczt;
    }
    /**
     * 获取pwxkzxxuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPwxkzxxuuid() {
        return pwxkzxxuuid;
    }

    /**
     * 设置pwxkzxxuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPwxkzxxuuid(String value) {
        this.pwxkzxxuuid = value;
    }

    /**
     * 获取wbjhpfkuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWbjhpfkuuid() {
        return wbjhpfkuuid;
    }

    /**
     * 设置wbjhpfkuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWbjhpfkuuid(String value) {
        this.wbjhpfkuuid = value;
    }

    /**
     * 获取syyxqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSyyxqq() {
        return syyxqq;
    }

    /**
     * 设置syyxqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSyyxqq(String value) {
        this.syyxqq = value;
    }

    /**
     * 获取scjydz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getScjydz() {
        return scjydz;
    }

    /**
     * 设置scjydz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setScjydz(String value) {
        this.scjydz = value;
    }

    /**
     * 获取pwxkzbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPwxkzbh() {
        return pwxkzbh;
    }

    /**
     * 设置pwxkzbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPwxkzbh(String value) {
        this.pwxkzbh = value;
    }

    /**
     * 获取gjhbjgDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGjhbjgDm() {
        return gjhbjgDm;
    }

    /**
     * 设置gjhbjgDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGjhbjgDm(String value) {
        this.gjhbjgDm = value;
    }

    /**
     * 获取pfklxDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPfklxDm() {
        return pfklxDm;
    }

    /**
     * 设置pfklxDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPfklxDm(String value) {
        this.pfklxDm = value;
    }

    /**
     * 获取pffsDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPffsDm() {
        return pffsDm;
    }

    /**
     * 设置pffsDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPffsDm(String value) {
        this.pffsDm = value;
    }

    /**
     * 获取zywrwlbDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZywrwlbDm() {
        return zywrwlbDm;
    }

    /**
     * 设置zywrwlbDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZywrwlbDm(String value) {
        this.zywrwlbDm = value;
    }

    /**
     * 获取yxqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqz() {
        return yxqz;
    }

    /**
     * 设置yxqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqz(String value) {
        this.yxqz = value;
    }

    /**
     * 获取hgbhssybh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHgbhssybh() {
        return hgbhssybh;
    }

    /**
     * 设置hgbhssybh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHgbhssybh(String value) {
        this.hgbhssybh = value;
    }

    /**
     * 获取jd2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public String getJd2() {
        return jd2;
    }

    /**
     * 设置jd2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJd2(String value) {
        this.jd2 = value;
    }

    /**
     * 获取jd2D属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJd2D() {
        return jd2D;
    }

    /**
     * 设置jd2D属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJd2D(Double value) {
        this.jd2D = value;
    }

    /**
     * 获取jd2F属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJd2F() {
        return jd2F;
    }

    /**
     * 设置jd2F属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJd2F(Double value) {
        this.jd2F = value;
    }

    /**
     * 获取jd2M属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getJd2M() {
        return jd2M;
    }

    /**
     * 设置jd2M属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setJd2M(Double value) {
        this.jd2M = value;
    }

    /**
     * 获取jdxzDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJdxzDm() {
        return jdxzDm;
    }

    /**
     * 设置jdxzDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJdxzDm(String value) {
        this.jdxzDm = value;
    }

    /**
     * 获取pfkbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPfkbh() {
        return pfkbh;
    }

    /**
     * 设置pfkbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPfkbh(String value) {
        this.pfkbh = value;
    }

    /**
     * 获取pfkmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPfkmc() {
        return pfkmc;
    }

    /**
     * 设置pfkmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPfkmc(String value) {
        this.pfkmc = value;
    }

    /**
     * 获取yxqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxqq() {
        return yxqq;
    }

    /**
     * 设置yxqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxqq(String value) {
        this.yxqq = value;
    }

    /**
     * 获取syuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSyuuid() {
        return syuuid;
    }

    /**
     * 设置syuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSyuuid(String value) {
        this.syuuid = value;
    }

    /**
     * 获取dqwrwpfklbDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDqwrwpfklbDm() {
        return dqwrwpfklbDm;
    }

    /**
     * 设置dqwrwpfklbDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDqwrwpfklbDm(String value) {
        this.dqwrwpfklbDm = value;
    }

    /**
     * 获取wd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public String getWd() {
        return wd;
    }

    /**
     * 设置wd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setWd(String value) {
        this.wd = value;
    }

    /**
     * 获取wdd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getWdd() {
        return wdd;
    }

    /**
     * 设置wdd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setWdd(Double value) {
        this.wdd = value;
    }

    /**
     * 获取wdf属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getWdf() {
        return wdf;
    }

    /**
     * 设置wdf属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setWdf(Double value) {
        this.wdf = value;
    }

    /**
     * 获取wdm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getWdm() {
        return wdm;
    }

    /**
     * 设置wdm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setWdm(Double value) {
        this.wdm = value;
    }

    /**
     * 获取hbsjcxxuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHbsjcxxuuid() {
        return hbsjcxxuuid;
    }

    /**
     * 设置hbsjcxxuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHbsjcxxuuid(String value) {
        this.hbsjcxxuuid = value;
    }

    /**
     * 获取xkzfbwybm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXkzfbwybm() {
        return xkzfbwybm;
    }

    /**
     * 设置xkzfbwybm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXkzfbwybm(String value) {
        this.xkzfbwybm = value;
    }

    /**
     * 获取xgrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrq() {
        return xgrq;
    }

    /**
     * 设置xgrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrq(String value) {
        this.xgrq = value;
    }

    /**
     * 获取lrrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrDm() {
        return lrrDm;
    }

    /**
     * 设置lrrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrDm(String value) {
        this.lrrDm = value;
    }

    /**
     * 获取yxbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYxbz() {
        return yxbz;
    }

    /**
     * 设置yxbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYxbz(String value) {
        this.yxbz = value;
    }

    /**
     * 获取zgswskfjDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZgswskfjDm() {
        return zgswskfjDm;
    }

    /**
     * 设置zgswskfjDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZgswskfjDm(String value) {
        this.zgswskfjDm = value;
    }

    /**
     * 获取wspfqxDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWspfqxDm() {
        return wspfqxDm;
    }

    /**
     * 设置wspfqxDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWspfqxDm(String value) {
        this.wspfqxDm = value;
    }

    /**
     * 获取xzqhszDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }

    /**
     * 设置xzqhszDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhszDm(String value) {
        this.xzqhszDm = value;
    }

    /**
     * 获取sjtbSj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjtbSj() {
        return sjtbSj;
    }

    /**
     * 设置sjtbSj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjtbSj(String value) {
        this.sjtbSj = value;
    }

    /**
     * 获取sjgsdq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSjgsdq() {
        return sjgsdq;
    }

    /**
     * 设置sjgsdq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSjgsdq(String value) {
        this.sjgsdq = value;
    }

    /**
     * 获取pfkwybm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPfkwybm() {
        return pfkwybm;
    }

    /**
     * 设置pfkwybm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPfkwybm(String value) {
        this.pfkwybm = value;
    }

    /**
     * 获取xgrDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXgrDm() {
        return xgrDm;
    }

    /**
     * 设置xgrDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXgrDm(String value) {
        this.xgrDm = value;
    }

    /**
     * 获取lrrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLrrq() {
        return lrrq;
    }

    /**
     * 设置lrrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLrrq(String value) {
        this.lrrq = value;
    }

    /**
     * 获取syyxqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSyyxqz() {
        return syyxqz;
    }

    /**
     * 设置syyxqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSyyxqz(String value) {
        this.syyxqz = value;
    }

    /**
     * 获取pfkwz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPfkwz() {
        return pfkwz;
    }

    /**
     * 设置pfkwz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPfkwz(String value) {
        this.pfkwz = value;
    }

    /**
     * 获取wrwpfljsffDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWrwpfljsffDm() {
        return wrwpfljsffDm;
    }

    /**
     * 设置wrwpfljsffDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWrwpfljsffDm(String value) {
        this.wrwpfljsffDm = value;
    }

    /**
     * 获取fzspmDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFzspmDm() {
        return fzspmDm;
    }

    /**
     * 设置fzspmDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFzspmDm(String value) {
        this.fzspmDm = value;
    }

    /**
     * 获取zspmDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZspmDm() {
        return zspmDm;
    }

    /**
     * 设置zspmDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZspmDm(String value) {
        this.zspmDm = value;
    }

    /**
     * 获取zszmDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZszmDm() {
        return zszmDm;
    }

    /**
     * 设置zszmDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZszmDm(String value) {
        this.zszmDm = value;
    }

    /**
     * 获取sbbz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbbz() {
        return sbbz;
    }

    /**
     * 设置sbbz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbbz(String value) {
        this.sbbz = value;
    }

}
