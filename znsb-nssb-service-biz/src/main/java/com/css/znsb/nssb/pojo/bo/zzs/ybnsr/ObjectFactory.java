package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;

/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the gov.etax.dzswj.sbzt.common.jinsan.sb.sbbd.zzs.ybnsr package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {
    private static final QName _TaxML_QNAME = new QName("http://www.chinatax.gov.cn/dataspec/", "taxML");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: gov.etax.dzswj.sbzt.common.jinsan.sb.sbbd.zzs.ybnsr
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ZzsybnsrsbGdzcjxsedkqkb}
     */
    public ZzsybnsrsbGdzcjxsedkqkb createZzsybnsrsbGdzcjxsedkqkb() {
        return new ZzsybnsrsbGdzcjxsedkqkb();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw}
     */
    public ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw createZzsybnsrsbTlysqyfzjgzzshznsxxcddbw() {
        return new ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbNcphdkczzsjxsejsb}
     */
    public ZzsybnsrsbNcphdkczzsjxsejsb createZzsybnsrsbNcphdkczzsjxsejsb() {
        return new ZzsybnsrsbNcphdkczzsjxsejsb();
    }

    /**
     * Create an instance of {@link Dlqyzzsxxsehjxsecdd}
     */
    public Dlqyzzsxxsehjxsecdd createDlqyzzsxxsehjxsecdd() {
        return new Dlqyzzsxxsehjxsecdd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqyzzsfpbywbw}
     */
    public ZzssyyybnsrHznsqyzzsfpbywbw createZzssyyybnsrHznsqyzzsfpbywbw() {
        return new ZzssyyybnsrHznsqyzzsfpbywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqyzzsfpb}
     */
    public ZzssyyybnsrHznsqyzzsfpb createZzssyyybnsrHznsqyzzsfpb() {
        return new ZzssyyybnsrHznsqyzzsfpb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrCpygxcqkmxb}
     */
    public ZzssyyybnsrCpygxcqkmxb createZzssyyybnsrCpygxcqkmxb() {
        return new ZzssyyybnsrCpygxcqkmxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrKstlqyzzsfpbywbw}
     */
    public ZzssyyybnsrKstlqyzzsfpbywbw createZzssyyybnsrKstlqyzzsfpbywbw() {
        return new ZzssyyybnsrKstlqyzzsfpbywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrKstlqyzzsfpb}
     */
    public ZzssyyybnsrKstlqyzzsfpb createZzssyyybnsrKstlqyzzsfpb() {
        return new ZzssyyybnsrKstlqyzzsfpb();
    }

    /**
     * Create an instance of {@link Hqysqyfzjgcdd}
     */
    public Hqysqyfzjgcdd createHqysqyfzjgcdd() {
        return new Hqysqyfzjgcdd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJyzyfjyxxmxb}
     */
    public ZzssyyybnsrJyzyfjyxxmxb createZzssyyybnsrJyzyfjyxxmxb() {
        return new ZzssyyybnsrJyzyfjyxxmxb();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr04Bqjxsemxb}
     */
    public Zzssyyybnsr04Bqjxsemxb createZzssyyybnsr04Bqjxsemxb() {
        return new Zzssyyybnsr04Bqjxsemxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZb}
     */
    public ZzssyyybnsrZb createZzssyyybnsrZb() {
        return new ZzssyyybnsrZb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdclscqyxsqktjb}
     */
    public ZzssyyybnsrJdclscqyxsqktjb createZzssyyybnsrJdclscqyxsqktjb() {
        return new ZzssyyybnsrJdclscqyxsqktjb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJyzyfjyxxmxbywbw}
     */
    public ZzssyyybnsrJyzyfjyxxmxbywbw createZzssyyybnsrJyzyfjyxxmxbywbw() {
        return new ZzssyyybnsrJyzyfjyxxmxbywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrYqtqyzzsfpb}
     */
    public ZzssyyybnsrYqtqyzzsfpb createZzssyyybnsrYqtqyzzsfpb() {
        return new ZzssyyybnsrYqtqyzzsfpb();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr03Ysfwkcxmmx}
     */
    public Zzssyyybnsr03Ysfwkcxmmx createZzssyyybnsr03Ysfwkcxmmx() {
        return new Zzssyyybnsr03Ysfwkcxmmx();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrBqdkjxsejgmxb}
     */
    public ZzssyyybnsrBqdkjxsejgmxb createZzssyyybnsrBqdkjxsejgmxb() {
        return new ZzssyyybnsrBqdkjxsejgmxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHqysqyfzjgcdd}
     */
    public ZzssyyybnsrHqysqyfzjgcdd createZzssyyybnsrHqysqyfzjgcdd() {
        return new ZzssyyybnsrHqysqyfzjgcdd();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbYzqyfzjgzzshznsxxcdd}
     */
    public ZzsybnsrsbYzqyfzjgzzshznsxxcdd createZzsybnsrsbYzqyfzjgzzshznsxxcdd() {
        return new ZzsybnsrsbYzqyfzjgzzshznsxxcdd();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbDxqyfzjgzzshznsxxcdd}
     */
    public ZzsybnsrsbDxqyfzjgzzshznsxxcdd createZzsybnsrsbDxqyfzjgzzshznsxxcdd() {
        return new ZzsybnsrsbDxqyfzjgzzshznsxxcdd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw}
     */
    public ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw createZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw() {
        return new ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb}
     */
    public ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb createZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb() {
        return new ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZzsysfpdkqdywbw}
     */
    public ZzssyyybnsrZzsysfpdkqdywbw createZzssyyybnsrZzsysfpdkqdywbw() {
        return new ZzssyyybnsrZzsysfpdkqdywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZzsysfpdkqd}
     */
    public ZzssyyybnsrZzsysfpdkqd createZzssyyybnsrZzsysfpdkqd() {
        return new ZzssyyybnsrZzsysfpdkqd();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbJyzyxsyphzb}
     */
    public ZzsybnsrsbJyzyxsyphzb createZzsybnsrsbJyzyxsyphzb() {
        return new ZzsybnsrsbJyzyxsyphzb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHqysqyfzjgcddywbw}
     */
    public ZzssyyybnsrHqysqyfzjgcddywbw createZzssyyybnsrHqysqyfzjgcddywbw() {
        return new ZzssyyybnsrHqysqyfzjgcddywbw();
    }

    /**
     * Create an instance of {@link Zzsjmssbmxb}
     */
    public Zzsjmssbmxb createZzsjmssbmxb() {
        return new Zzsjmssbmxb();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr02Bqjxsemxb}
     */
    public Zzssyyybnsr02Bqjxsemxb createZzssyyybnsr02Bqjxsemxb() {
        return new Zzssyyybnsr02Bqjxsemxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcxstyfpqd}
     */
    public ZzssyyybnsrJdcxstyfpqd createZzssyyybnsrJdcxstyfpqd() {
        return new ZzssyyybnsrJdcxstyfpqd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqytycddywbw}
     */
    public ZzssyyybnsrHznsqytycddywbw createZzssyyybnsrHznsqytycddywbw() {
        return new ZzssyyybnsrHznsqytycddywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqytycdd}
     */
    public ZzssyyybnsrHznsqytycdd createZzssyyybnsrHznsqytycdd() {
        return new ZzssyyybnsrHznsqytycdd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrYgzsffxcsmxb}
     */
    public ZzssyyybnsrYgzsffxcsmxb createZzssyyybnsrYgzsffxcsmxb() {
        return new ZzssyyybnsrYgzsffxcsmxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcxstyfpqdywbw}
     */
    public ZzssyyybnsrJdcxstyfpqdywbw createZzssyyybnsrJdcxstyfpqdywbw() {
        return new ZzssyyybnsrJdcxstyfpqdywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrCpygxcslmxbywbw}
     */
    public ZzssyyybnsrCpygxcslmxbywbw createZzssyyybnsrCpygxcslmxbywbw() {
        return new ZzssyyybnsrCpygxcslmxbywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrCpygxcslmxb}
     */
    public ZzssyyybnsrCpygxcslmxb createZzssyyybnsrCpygxcslmxb() {
        return new ZzssyyybnsrCpygxcslmxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrYqtqyzzsfpbywbw}
     */
    public ZzssyyybnsrYqtqyzzsfpbywbw createZzssyyybnsrYqtqyzzsfpbywbw() {
        return new ZzssyyybnsrYqtqyzzsfpbywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcljxqyxsmxb}
     */
    public ZzssyyybnsrJdcljxqyxsmxb createZzssyyybnsrJdcljxqyxsmxb() {
        return new ZzssyyybnsrJdcljxqyxsmxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdclscqyxsmxb}
     */
    public ZzssyyybnsrJdclscqyxsmxb createZzssyyybnsrJdclscqyxsmxb() {
        return new ZzssyyybnsrJdclscqyxsmxb();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbDkdjsstyjksdkqd}
     */
    public ZzsybnsrsbDkdjsstyjksdkqd createZzsybnsrsbDkdjsstyjksdkqd() {
        return new ZzsybnsrsbDkdjsstyjksdkqd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcxstyfplycybb}
     */
    public ZzssyyybnsrJdcxstyfplycybb createZzssyyybnsrJdcxstyfplycybb() {
        return new ZzssyyybnsrJdcxstyfplycybb();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr01Bqxsqkmxb}
     */
    public Zzssyyybnsr01Bqxsqkmxb createZzssyyybnsr01Bqxsqkmxb() {
        return new Zzssyyybnsr01Bqxsqkmxb();
    }

    /**
     * Create an instance of {@link Hgwspzdklsjcjb}
     */
    public Hgwspzdklsjcjb createHgwspzdklsjcjb() {
        return new Hgwspzdklsjcjb();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbCbfhdccpzzsjxsejsb}
     */
    public ZzsybnsrsbCbfhdccpzzsjxsejsb createZzsybnsrsbCbfhdccpzzsjxsejsb() {
        return new ZzsybnsrsbCbfhdccpzzsjxsejsb();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr05Bdcfqdkjsb}
     */
    public Zzssyyybnsr05Bdcfqdkjsb createZzssyyybnsr05Bdcfqdkjsb() {
        return new Zzssyyybnsr05Bdcfqdkjsb();
    }

    /**
     * Create an instance of {@link HXZGSB00041Request}
     */
    public HXZGSB00041Request createHXZGSB00041Request() {
        return new HXZGSB00041Request();
    }

    /**
     * Create an instance of {@link HXZGSB00041Request.YjxxGrid}
     */
    public HXZGSB00041Request.YjxxGrid createHXZGSB00041RequestYjxxGrid() {
        return new HXZGSB00041Request.YjxxGrid();
    }

    /**
     * Create an instance of {@link HXZGSB00041Request.JmxxGrid}
     */
    public HXZGSB00041Request.JmxxGrid createHXZGSB00041RequestJmxxGrid() {
        return new HXZGSB00041Request.JmxxGrid();
    }

    /**
     * Create an instance of {@link HXZGSB00041Request.SbxxGrid}
     */
    public HXZGSB00041Request.SbxxGrid createHXZGSB00041RequestSbxxGrid() {
        return new HXZGSB00041Request.SbxxGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbNdhkysqyqsb}
     */
    public ZzsybnsrsbNdhkysqyqsb createZzsybnsrsbNdhkysqyqsb() {
        return new ZzsybnsrsbNdhkysqyqsb();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbNdhkysqyqsb.NdhkysqyqsbGrid}
     */
    public ZzsybnsrsbNdhkysqyqsb.NdhkysqyqsbGrid createZzsybnsrsbNdhkysqyqsbNdhkysqyqsbGrid() {
        return new ZzsybnsrsbNdhkysqyqsb.NdhkysqyqsbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrTljsjjnssbb}
     */
    public ZzssyyybnsrTljsjjnssbb createZzssyyybnsrTljsjjnssbb() {
        return new ZzssyyybnsrTljsjjnssbb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb}
     */
    public ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb createZzssyyybnsrGjncpzjxshdncpzzsjxsejsb() {
        return new ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw}
     */
    public ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw createZzssyyybnsrZzsybnsrbqynseayskmfjbywbw() {
        return new ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw}
     */
    public ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw createZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw() {
        return new ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr11Hqysqyfzjgcddywbw}
     */
    public Zzssyyybnsr11Hqysqyfzjgcddywbw createZzssyyybnsr11Hqysqyfzjgcddywbw() {
        return new Zzssyyybnsr11Hqysqyfzjgcddywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHgwspzdklsjcjbywbw}
     */
    public ZzssyyybnsrHgwspzdklsjcjbywbw createZzssyyybnsrHgwspzdklsjcjbywbw() {
        return new ZzssyyybnsrHgwspzdklsjcjbywbw();
    }

    /**
     * Create an instance of {@link Zzsjmssbmxbywbw}
     */
    public Zzsjmssbmxbywbw createZzsjmssbmxbywbw() {
        return new Zzsjmssbmxbywbw();
    }

    /**
     * Create an instance of {@link Sbbheadkz1VO}
     */
    public Sbbheadkz1VO createSbbheadkz1VO() {
        return new Sbbheadkz1VO();
    }

    /**
     * Create an instance of {@link BdcfqdkjsbGrid}
     */
    public BdcfqdkjsbGrid createBdcfqdkjsbGrid() {
        return new BdcfqdkjsbGrid();
    }

    /**
     * Create an instance of {@link HznsqytycddjxGridlbVO}
     */
    public HznsqytycddjxGridlbVO createHznsqytycddjxGridlbVO() {
        return new HznsqytycddjxGridlbVO();
    }

    /**
     * Create an instance of {@link BqdkjxsejgmxbGridlbVO}
     */
    public BqdkjxsejgmxbGridlbVO createBqdkjxsejgmxbGridlbVO() {
        return new BqdkjxsejgmxbGridlbVO();
    }

    /**
     * Create an instance of {@link QdjxsekGridlb}
     */
    public QdjxsekGridlb createQdjxsekGridlb() {
        return new QdjxsekGridlb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrYgzsffxcsmxbywbw}
     */
    public ZzssyyybnsrYgzsffxcsmxbywbw createZzssyyybnsrYgzsffxcsmxbywbw() {
        return new ZzssyyybnsrYgzsffxcsmxbywbw();
    }

    /**
     * Create an instance of {@link DkdjsstyjksdkqdGridlbVO}
     */
    public DkdjsstyjksdkqdGridlbVO createDkdjsstyjksdkqdGridlbVO() {
        return new DkdjsstyjksdkqdGridlbVO();
    }

    /**
     * Create an instance of {@link JxGrid}
     */
    public JxGrid createJxGrid() {
        return new JxGrid();
    }

    /**
     * Create an instance of {@link GlysGrid}
     */
    public GlysGrid createGlysGrid() {
        return new GlysGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrTljsjjnssbbywbw}
     */
    public ZzssyyybnsrTljsjjnssbbywbw createZzssyyybnsrTljsjjnssbbywbw() {
        return new ZzssyyybnsrTljsjjnssbbywbw();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrBfcpxstjbywbw}
     */
    public ZzssyyybnsrBfcpxstjbywbw createZzssyyybnsrBfcpxstjbywbw() {
        return new ZzssyyybnsrBfcpxstjbywbw();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr03Ysfwkcxmmxywbw}
     */
    public Zzssyyybnsr03Ysfwkcxmmxywbw createZzssyyybnsr03Ysfwkcxmmxywbw() {
        return new Zzssyyybnsr03Ysfwkcxmmxywbw();
    }

    /**
     * Create an instance of {@link SlxxFormVO}
     */
    public SlxxFormVO createSlxxFormVO() {
        return new SlxxFormVO();
    }

    /**
     * Create an instance of {@link SbbFjxxVO}
     */
    public SbbFjxxVO createSbbFjxxVO() {
        return new SbbFjxxVO();
    }

    /**
     * Create an instance of {@link BqjxsemxbGrid}
     */
    public BqjxsemxbGrid createBqjxsemxbGrid() {
        return new BqjxsemxbGrid();
    }

    /**
     * Create an instance of {@link Sbbheadkz3VO}
     */
    public Sbbheadkz3VO createSbbheadkz3VO() {
        return new Sbbheadkz3VO();
    }

    /**
     * Create an instance of {@link GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO}
     */
    public GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO createGjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO() {
        return new GjncpyyscjyqbgchwsthdncpzzsjxsejsbGridlbVO();
    }

    /**
     * Create an instance of {@link DkqdGridlbVO}
     */
    public DkqdGridlbVO createDkqdGridlbVO() {
        return new DkqdGridlbVO();
    }

    /**
     * Create an instance of {@link BqxsqkmxbGridlbVO}
     */
    public BqxsqkmxbGridlbVO createBqxsqkmxbGridlbVO() {
        return new BqxsqkmxbGridlbVO();
    }

    /**
     * Create an instance of {@link Sbbheadkz2VO}
     */
    public Sbbheadkz2VO createSbbheadkz2VO() {
        return new Sbbheadkz2VO();
    }

    /**
     * Create an instance of {@link JdclscqyxsqktjbGridlbVO}
     */
    public JdclscqyxsqktjbGridlbVO createJdclscqyxsqktjbGridlbVO() {
        return new JdclscqyxsqktjbGridlbVO();
    }

    /**
     * Create an instance of {@link GjncpzjxshdncpzzsjxseGridlbVO}
     */
    public GjncpzjxshdncpzzsjxseGridlbVO createGjncpzjxshdncpzzsjxseGridlbVO() {
        return new GjncpzjxshdncpzzsjxseGridlbVO();
    }

    /**
     * Create an instance of {@link GjncpzjxshdncpzzsjxseGrid}
     */
    public GjncpzjxshdncpzzsjxseGrid createGjncpzjxshdncpzzsjxseGrid() {
        return new GjncpzjxshdncpzzsjxseGrid();
    }

    /**
     * Create an instance of {@link HznsqyzzsfpbGrid}
     */
    public HznsqyzzsfpbGrid createHznsqyzzsfpbGrid() {
        return new HznsqyzzsfpbGrid();
    }

    /**
     * Create an instance of {@link SbbslxxVO}
     */
    public SbbslxxVO createSbbslxxVO() {
        return new SbbslxxVO();
    }

    /**
     * Create an instance of {@link Fjssbywbw}
     */
    public Fjssbywbw createFjssbywbw() {
        return new Fjssbywbw();
    }

    /**
     * Create an instance of {@link GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid}
     */
    public GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid createGjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid() {
        return new GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid();
    }

    /**
     * Create an instance of {@link ZzsjmssbmxbjsxmGridlbVO}
     */
    public ZzsjmssbmxbjsxmGridlbVO createZzsjmssbmxbjsxmGridlbVO() {
        return new ZzsjmssbmxbjsxmGridlbVO();
    }

    /**
     * Create an instance of {@link CpygxcqkmxbGrid}
     */
    public CpygxcqkmxbGrid createCpygxcqkmxbGrid() {
        return new CpygxcqkmxbGrid();
    }

    /**
     * Create an instance of {@link JyzyxsyphzbGrid}
     */
    public JyzyxsyphzbGrid createJyzyxsyphzbGrid() {
        return new JyzyxsyphzbGrid();
    }

    /**
     * Create an instance of {@link YqtqyzzsfpbFormVO}
     */
    public YqtqyzzsfpbFormVO createYqtqyzzsfpbFormVO() {
        return new YqtqyzzsfpbFormVO();
    }

    /**
     * Create an instance of {@link BqjxsebGridlbVO}
     */
    public BqjxsebGridlbVO createBqjxsebGridlbVO() {
        return new BqjxsebGridlbVO();
    }

    /**
     * Create an instance of {@link QdjxseqkGrid}
     */
    public QdjxseqkGrid createQdjxseqkGrid() {
        return new QdjxseqkGrid();
    }

    /**
     * Create an instance of {@link BqjxsebGrid}
     */
    public BqjxsebGrid createBqjxsebGrid() {
        return new BqjxsebGrid();
    }

    /**
     * Create an instance of {@link Sbbslxxkz3VO}
     */
    public Sbbslxxkz3VO createSbbslxxkz3VO() {
        return new Sbbslxxkz3VO();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbGdzcjxsedkqkbbw}
     */
    public ZzsybnsrsbGdzcjxsedkqkbbw createZzsybnsrsbGdzcjxsedkqkbbw() {
        return new ZzsybnsrsbGdzcjxsedkqkbbw();
    }

    /**
     * Create an instance of {@link JyzyfjyxxmxGrid}
     */
    public JyzyfjyxxmxGrid createJyzyfjyxxmxGrid() {
        return new JyzyfjyxxmxGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbNcphdkczzsjxsejsbbw}
     */
    public ZzsybnsrsbNcphdkczzsjxsejsbbw createZzsybnsrsbNcphdkczzsjxsejsbbw() {
        return new ZzsybnsrsbNcphdkczzsjxsejsbbw();
    }

    /**
     * Create an instance of {@link Sbbslxxkz2VO}
     */
    public Sbbslxxkz2VO createSbbslxxkz2VO() {
        return new Sbbslxxkz2VO();
    }

    /**
     * Create an instance of {@link DkdjsstyjksdkqdGrid}
     */
    public DkdjsstyjksdkqdGrid createDkdjsstyjksdkqdGrid() {
        return new DkdjsstyjksdkqdGrid();
    }

    /**
     * Create an instance of {@link ZzsnssbbDlqyzzsxxsehjxsecddywbw}
     */
    public ZzsnssbbDlqyzzsxxsehjxsecddywbw createZzsnssbbDlqyzzsxxsehjxsecddywbw() {
        return new ZzsnssbbDlqyzzsxxsehjxsecddywbw();
    }

    /**
     * Create an instance of {@link ZzsjmssbmxbjsxmGrid}
     */
    public ZzsjmssbmxbjsxmGrid createZzsjmssbmxbjsxmGrid() {
        return new ZzsjmssbmxbjsxmGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO}
     */
    public ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO createZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO() {
        return new ZzsybnsrsbCbfhdccpzzsjxsejsbbwGridlbVO();
    }

    /**
     * Create an instance of {@link DxyjnzzsqkGridlbVO}
     */
    public DxyjnzzsqkGridlbVO createDxyjnzzsqkGridlbVO() {
        return new DxyjnzzsqkGridlbVO();
    }

    /**
     * Create an instance of {@link QdjxsekGrid}
     */
    public QdjxsekGrid createQdjxsekGrid() {
        return new QdjxsekGrid();
    }

    /**
     * Create an instance of {@link YgzsffxcsmxbGrid}
     */
    public YgzsffxcsmxbGrid createYgzsffxcsmxbGrid() {
        return new YgzsffxcsmxbGrid();
    }

    /**
     * Create an instance of {@link FjsxxGrid}
     */
    public FjsxxGrid createFjsxxGrid() {
        return new FjsxxGrid();
    }

    /**
     * Create an instance of {@link JdcxstyfpqdGrid}
     */
    public JdcxstyfpqdGrid createJdcxstyfpqdGrid() {
        return new JdcxstyfpqdGrid();
    }

    /**
     * Create an instance of {@link HznsqytycddxxGrid}
     */
    public HznsqytycddxxGrid createHznsqytycddxxGrid() {
        return new HznsqytycddxxGrid();
    }

    /**
     * Create an instance of {@link HznsqytycddxxGridlbVO}
     */
    public HznsqytycddxxGridlbVO createHznsqytycddxxGridlbVO() {
        return new HznsqytycddxxGridlbVO();
    }

    /**
     * Create an instance of {@link ZzsjmssbmxbmsxmGridlbVO}
     */
    public ZzsjmssbmxbmsxmGridlbVO createZzsjmssbmxbmsxmGridlbVO() {
        return new ZzsjmssbmxbmsxmGridlbVO();
    }

    /**
     * Create an instance of {@link XxGrid}
     */
    public XxGrid createXxGrid() {
        return new XxGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZzsybnsrbqynseayskmfjb}
     */
    public ZzssyyybnsrZzsybnsrbqynseayskmfjb createZzssyyybnsrZzsybnsrbqynseayskmfjb() {
        return new ZzssyyybnsrZzsybnsrbqynseayskmfjb();
    }

    /**
     * Create an instance of {@link SkzsFormVO}
     */
    public SkzsFormVO createSkzsFormVO() {
        return new SkzsFormVO();
    }

    /**
     * Create an instance of {@link HznsqytycddFormVO}
     */
    public HznsqytycddFormVO createHznsqytycddFormVO() {
        return new HznsqytycddFormVO();
    }

    /**
     * Create an instance of {@link DkqdGrid}
     */
    public DkqdGrid createDkqdGrid() {
        return new DkqdGrid();
    }

    /**
     * Create an instance of {@link QdjxseGrid}
     */
    public QdjxseGrid createQdjxseGrid() {
        return new QdjxseGrid();
    }

    /**
     * Create an instance of {@link YqtqyzzsfpbGrid}
     */
    public YqtqyzzsfpbGrid createYqtqyzzsfpbGrid() {
        return new YqtqyzzsfpbGrid();
    }

    /**
     * Create an instance of {@link YgzsffxcsmxbGridlbVO}
     */
    public YgzsffxcsmxbGridlbVO createYgzsffxcsmxbGridlbVO() {
        return new YgzsffxcsmxbGridlbVO();
    }

    /**
     * Create an instance of {@link XxGridlbVO}
     */
    public XxGridlbVO createXxGridlbVO() {
        return new XxGridlbVO();
    }

    /**
     * Create an instance of {@link HgwspzdklsjGrid}
     */
    public HgwspzdklsjGrid createHgwspzdklsjGrid() {
        return new HgwspzdklsjGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid}
     */
    public ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid createZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid() {
        return new ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrbqynseayskmfjbFormVO}
     */
    public ZzsybnsrbqynseayskmfjbFormVO createZzsybnsrbqynseayskmfjbFormVO() {
        return new ZzsybnsrbqynseayskmfjbFormVO();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdclscqyxsqktjbywbw}
     */
    public ZzssyyybnsrJdclscqyxsqktjbywbw createZzssyyybnsrJdclscqyxsqktjbywbw() {
        return new ZzssyyybnsrJdclscqyxsqktjbywbw();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbYjnzzsqkForm}
     */
    public ZzsybnsrsbYjnzzsqkForm createZzsybnsrsbYjnzzsqkForm() {
        return new ZzsybnsrsbYjnzzsqkForm();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcxstyfplycybbywbw}
     */
    public ZzssyyybnsrJdcxstyfplycybbywbw createZzssyyybnsrJdcxstyfplycybbywbw() {
        return new ZzssyyybnsrJdcxstyfplycybbywbw();
    }

    /**
     * Create an instance of {@link ShxxFormVO}
     */
    public ShxxFormVO createShxxFormVO() {
        return new ShxxFormVO();
    }

    /**
     * Create an instance of {@link CpygxcslmxbGrid}
     */
    public CpygxcslmxbGrid createCpygxcslmxbGrid() {
        return new CpygxcslmxbGrid();
    }

    /**
     * Create an instance of {@link CpygxcslmxbGridlbVO}
     */
    public CpygxcslmxbGridlbVO createCpygxcslmxbGridlbVO() {
        return new CpygxcslmxbGridlbVO();
    }

    /**
     * Create an instance of {@link HznsqytycddjxGrid}
     */
    public HznsqytycddjxGrid createHznsqytycddjxGrid() {
        return new HznsqytycddjxGrid();
    }

    /**
     * Create an instance of {@link YsfwkcxmmxGridlbVO}
     */
    public YsfwkcxmmxGridlbVO createYsfwkcxmmxGridlbVO() {
        return new YsfwkcxmmxGridlbVO();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr02Bqjxsemxbywbw}
     */
    public Zzssyyybnsr02Bqjxsemxbywbw createZzssyyybnsr02Bqjxsemxbywbw() {
        return new Zzssyyybnsr02Bqjxsemxbywbw();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbTlysqyfzjgzzshznsxxcdd}
     */
    public ZzsybnsrsbTlysqyfzjgzzshznsxxcdd createZzsybnsrsbTlysqyfzjgzzshznsxxcdd() {
        return new ZzsybnsrsbTlysqyfzjgzzshznsxxcdd();
    }

    /**
     * Create an instance of {@link YjnzzskGrid}
     */
    public YjnzzskGrid createYjnzzskGrid() {
        return new YjnzzskGrid();
    }

    /**
     * Create an instance of {@link KstlqyzzsfpbFormVO}
     */
    public KstlqyzzsfpbFormVO createKstlqyzzsfpbFormVO() {
        return new KstlqyzzsfpbFormVO();
    }

    /**
     * Create an instance of {@link NsrxxFormVO}
     */
    public NsrxxFormVO createNsrxxFormVO() {
        return new NsrxxFormVO();
    }

    /**
     * Create an instance of {@link HznsqyzzsfpbGridlbVO}
     */
    public HznsqyzzsfpbGridlbVO createHznsqyzzsfpbGridlbVO() {
        return new HznsqyzzsfpbGridlbVO();
    }

    /**
     * Create an instance of {@link ZzsjmssbmxbmsxmGrid}
     */
    public ZzsjmssbmxbmsxmGrid createZzsjmssbmxbmsxmGrid() {
        return new ZzsjmssbmxbmsxmGrid();
    }

    /**
     * Create an instance of {@link YsfwkcxmmxGrid}
     */
    public YsfwkcxmmxGrid createYsfwkcxmmxGrid() {
        return new YsfwkcxmmxGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdclscqyxsmxbywbw}
     */
    public ZzssyyybnsrJdclscqyxsmxbywbw createZzssyyybnsrJdclscqyxsmxbywbw() {
        return new ZzssyyybnsrJdclscqyxsmxbywbw();
    }

    /**
     * Create an instance of {@link YqtqyzzsfpbGridlbVO}
     */
    public YqtqyzzsfpbGridlbVO createYqtqyzzsfpbGridlbVO() {
        return new YqtqyzzsfpbGridlbVO();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcljxqyxsmxb1}
     */
    public ZzssyyybnsrJdcljxqyxsmxb1 createZzssyyybnsrJdcljxqyxsmxb1() {
        return new ZzssyyybnsrJdcljxqyxsmxb1();
    }

    /**
     * Create an instance of {@link YjnzzsqkGridlbVO}
     */
    public YjnzzsqkGridlbVO createYjnzzsqkGridlbVO() {
        return new YjnzzsqkGridlbVO();
    }

    /**
     * Create an instance of {@link BqdkjxsejgmxbGrid}
     */
    public BqdkjxsejgmxbGrid createBqdkjxsejgmxbGrid() {
        return new BqdkjxsejgmxbGrid();
    }

    /**
     * Create an instance of {@link Sbbslxxkz1VO}
     */
    public Sbbslxxkz1VO createSbbslxxkz1VO() {
        return new Sbbslxxkz1VO();
    }

    /**
     * Create an instance of {@link ZbGridlbVO}
     */
    public ZbGridlbVO createZbGridlbVO() {
        return new ZbGridlbVO();
    }

    /**
     * Create an instance of {@link SbbVO}
     */
    public SbbVO createSbbVO() {
        return new SbbVO();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrCpygxcqkmxbywbw}
     */
    public ZzssyyybnsrCpygxcqkmxbywbw createZzssyyybnsrCpygxcqkmxbywbw() {
        return new ZzssyyybnsrCpygxcqkmxbywbw();
    }

    /**
     * Create an instance of {@link SbbxxVO}
     */
    public SbbxxVO createSbbxxVO() {
        return new SbbxxVO();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrBfcpxstjb}
     */
    public ZzssyyybnsrBfcpxstjb createZzssyyybnsrBfcpxstjb() {
        return new ZzssyyybnsrBfcpxstjb();
    }

    /**
     * Create an instance of {@link FjsxxGridlb}
     */
    public FjsxxGridlb createFjsxxGridlb() {
        return new FjsxxGridlb();
    }

    /**
     * Create an instance of {@link TrccfhdncpzzsjxseGridlbVO}
     */
    public TrccfhdncpzzsjxseGridlbVO createTrccfhdncpzzsjxseGridlbVO() {
        return new TrccfhdncpzzsjxseGridlbVO();
    }

    /**
     * Create an instance of {@link QdjxseqkGridlbVO}
     */
    public QdjxseqkGridlbVO createQdjxseqkGridlbVO() {
        return new QdjxseqkGridlbVO();
    }

    /**
     * Create an instance of {@link NdhkysqyqsbGrid}
     */
    public NdhkysqyqsbGrid createNdhkysqyqsbGrid() {
        return new NdhkysqyqsbGrid();
    }

    /**
     * Create an instance of {@link BfcpxstjbFormVO}
     */
    public BfcpxstjbFormVO createBfcpxstjbFormVO() {
        return new BfcpxstjbFormVO();
    }

    /**
     * Create an instance of {@link KstlqyzzsfpbGrid}
     */
    public KstlqyzzsfpbGrid createKstlqyzzsfpbGrid() {
        return new KstlqyzzsfpbGrid();
    }

    /**
     * Create an instance of {@link SbFjsfHznsfjsffpbVO}
     */
    public SbFjsfHznsfjsffpbVO createSbFjsfHznsfjsffpbVO() {
        return new SbFjsfHznsfjsffpbVO();
    }

    /**
     * Create an instance of {@link TljsjjnssbbGridlbVO}
     */
    public TljsjjnssbbGridlbVO createTljsjjnssbbGridlbVO() {
        return new TljsjjnssbbGridlbVO();
    }

    /**
     * Create an instance of {@link JdcljxqyxsmxbGridlbVO}
     */
    public JdcljxqyxsmxbGridlbVO createJdcljxqyxsmxbGridlbVO() {
        return new JdcljxqyxsmxbGridlbVO();
    }

    /**
     * Create an instance of {@link GlysGridlbVO}
     */
    public GlysGridlbVO createGlysGridlbVO() {
        return new GlysGridlbVO();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbQdjxseqkGridlb}
     */
    public ZzsybnsrsbQdjxseqkGridlb createZzsybnsrsbQdjxseqkGridlb() {
        return new ZzsybnsrsbQdjxseqkGridlb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcljxqyxsmxbywbw}
     */
    public ZzssyyybnsrJdcljxqyxsmxbywbw createZzssyyybnsrJdcljxqyxsmxbywbw() {
        return new ZzssyyybnsrJdcljxqyxsmxbywbw();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr05Bdcfqdkjsbywbw}
     */
    public Zzssyyybnsr05Bdcfqdkjsbywbw createZzssyyybnsr05Bdcfqdkjsbywbw() {
        return new Zzssyyybnsr05Bdcfqdkjsbywbw();
    }

    /**
     * Create an instance of {@link HznsqyzzsfpbFormVO}
     */
    public HznsqyzzsfpbFormVO createHznsqyzzsfpbFormVO() {
        return new HznsqyzzsfpbFormVO();
    }

    /**
     * Create an instance of {@link JdclscqyxsmxbGrid}
     */
    public JdclscqyxsmxbGrid createJdclscqyxsmxbGrid() {
        return new JdclscqyxsmxbGrid();
    }

    /**
     * Create an instance of {@link TaxList}
     */
    public TaxList createTaxList() {
        return new TaxList();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbDxqyfzjgzzshznsxxcddbw}
     */
    public ZzsybnsrsbDxqyfzjgzzshznsxxcddbw createZzsybnsrsbDxqyfzjgzzshznsxxcddbw() {
        return new ZzsybnsrsbDxqyfzjgzzshznsxxcddbw();
    }

    /**
     * Create an instance of {@link JdclscqyxsmxbGridlbVO}
     */
    public JdclscqyxsmxbGridlbVO createJdclscqyxsmxbGridlbVO() {
        return new JdclscqyxsmxbGridlbVO();
    }

    /**
     * Create an instance of {@link Zzsfjssb}
     */
    public Zzsfjssb createZzsfjssb() {
        return new Zzsfjssb();
    }

    /**
     * Create an instance of {@link TljsjjnssbbGrid}
     */
    public TljsjjnssbbGrid createTljsjjnssbbGrid() {
        return new TljsjjnssbbGrid();
    }

    /**
     * Create an instance of {@link NcphdkczzsjxsejsbGridlbVO}
     */
    public NcphdkczzsjxsejsbGridlbVO createNcphdkczzsjxsejsbGridlbVO() {
        return new NcphdkczzsjxsejsbGridlbVO();
    }

    /**
     * Create an instance of {@link HgwspzdklsjGridlbVO}
     */
    public HgwspzdklsjGridlbVO createHgwspzdklsjGridlbVO() {
        return new HgwspzdklsjGridlbVO();
    }

    /**
     * Create an instance of {@link KstlqyzzsfpbGridlbVO}
     */
    public KstlqyzzsfpbGridlbVO createKstlqyzzsfpbGridlbVO() {
        return new KstlqyzzsfpbGridlbVO();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbJyzyxsyphzbbw}
     */
    public ZzsybnsrsbJyzyxsyphzbbw createZzsybnsrsbJyzyxsyphzbbw() {
        return new ZzsybnsrsbJyzyxsyphzbbw();
    }

    /**
     * Create an instance of {@link BdcfqdkjsbGridlbVO}
     */
    public BdcfqdkjsbGridlbVO createBdcfqdkjsbGridlbVO() {
        return new BdcfqdkjsbGridlbVO();
    }

    /**
     * Create an instance of {@link SbbheadVO}
     */
    public SbbheadVO createSbbheadVO() {
        return new SbbheadVO();
    }

    /**
     * Create an instance of {@link ZzsysfpdkqdFormVO}
     */
    public ZzsysfpdkqdFormVO createZzsysfpdkqdFormVO() {
        return new ZzsysfpdkqdFormVO();
    }

    /**
     * Create an instance of {@link FjsxxForm}
     */
    public FjsxxForm createFjsxxForm() {
        return new FjsxxForm();
    }

    /**
     * Create an instance of {@link BqjxsemxbGridlbVO}
     */
    public BqjxsemxbGridlbVO createBqjxsemxbGridlbVO() {
        return new BqjxsemxbGridlbVO();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr01Bqxsqkmxbywbw}
     */
    public Zzssyyybnsr01Bqxsqkmxbywbw createZzssyyybnsr01Bqxsqkmxbywbw() {
        return new Zzssyyybnsr01Bqxsqkmxbywbw();
    }

    /**
     * Create an instance of {@link GdzcjxsedkqkbfromVO}
     */
    public GdzcjxsedkqkbfromVO createGdzcjxsedkqkbfromVO() {
        return new GdzcjxsedkqkbfromVO();
    }

    /**
     * Create an instance of {@link YjnzzsqkGrid}
     */
    public YjnzzsqkGrid createYjnzzsqkGrid() {
        return new YjnzzsqkGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZbbw}
     */
    public ZzssyyybnsrZbbw createZzssyyybnsrZbbw() {
        return new ZzssyyybnsrZbbw();
    }

    /**
     * Create an instance of {@link JyzyxsyphzbGridlbVO}
     */
    public JyzyxsyphzbGridlbVO createJyzyxsyphzbGridlbVO() {
        return new JyzyxsyphzbGridlbVO();
    }

    /**
     * Create an instance of {@link JdcljxqyxsmxbGrid}
     */
    public JdcljxqyxsmxbGrid createJdcljxqyxsmxbGrid() {
        return new JdcljxqyxsmxbGrid();
    }

    /**
     * Create an instance of {@link YjnzzskGridlb}
     */
    public YjnzzskGridlb createYjnzzskGridlb() {
        return new YjnzzskGridlb();
    }

    /**
     * Create an instance of {@link SBSaveLdxxVO}
     */
    public SBSaveLdxxVO createSBSaveLdxxVO() {
        return new SBSaveLdxxVO();
    }

    /**
     * Create an instance of {@link Zzsywbw}
     */
    public Zzsywbw createZzsywbw() {
        return new Zzsywbw();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbDkdjsstyjksdkqdbw}
     */
    public ZzsybnsrsbDkdjsstyjksdkqdbw createZzsybnsrsbDkdjsstyjksdkqdbw() {
        return new ZzsybnsrsbDkdjsstyjksdkqdbw();
    }

    /**
     * Create an instance of {@link JdcxstyfplycybbGrid}
     */
    public JdcxstyfplycybbGrid createJdcxstyfplycybbGrid() {
        return new JdcxstyfplycybbGrid();
    }

    /**
     * Create an instance of {@link CpygxcqkmxbGridlbVO}
     */
    public CpygxcqkmxbGridlbVO createCpygxcqkmxbGridlbVO() {
        return new CpygxcqkmxbGridlbVO();
    }

    /**
     * Create an instance of {@link SBSBbcTjqtxxVO}
     */
    public SBSBbcTjqtxxVO createSBSBbcTjqtxxVO() {
        return new SBSBbcTjqtxxVO();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbYzqyfzjgzzshznsxxcddbw}
     */
    public ZzsybnsrsbYzqyfzjgzzshznsxxcddbw createZzsybnsrsbYzqyfzjgzzshznsxxcddbw() {
        return new ZzsybnsrsbYzqyfzjgzzshznsxxcddbw();
    }

    /**
     * Create an instance of {@link HznsFjsffpbGrid}
     */
    public HznsFjsffpbGrid createHznsFjsffpbGrid() {
        return new HznsFjsffpbGrid();
    }

    /**
     * Create an instance of {@link JdclscqyxsqktjbGrid}
     */
    public JdclscqyxsqktjbGrid createJdclscqyxsqktjbGrid() {
        return new JdclscqyxsqktjbGrid();
    }

    /**
     * Create an instance of {@link NdhkysqyqsbGridlbVO}
     */
    public NdhkysqyqsbGridlbVO createNdhkysqyqsbGridlbVO() {
        return new NdhkysqyqsbGridlbVO();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr04Bqjxsemxbywbw}
     */
    public Zzssyyybnsr04Bqjxsemxbywbw createZzssyyybnsr04Bqjxsemxbywbw() {
        return new Zzssyyybnsr04Bqjxsemxbywbw();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbQdjxseqkGrid}
     */
    public ZzsybnsrsbQdjxseqkGrid createZzsybnsrsbQdjxseqkGrid() {
        return new ZzsybnsrsbQdjxseqkGrid();
    }

    /**
     * Create an instance of {@link JyzyfjyxxmxGridlbVO}
     */
    public JyzyfjyxxmxGridlbVO createJyzyfjyxxmxGridlbVO() {
        return new JyzyfjyxxmxGridlbVO();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbNdhkysqyqsbbw}
     */
    public ZzsybnsrsbNdhkysqyqsbbw createZzsybnsrsbNdhkysqyqsbbw() {
        return new ZzsybnsrsbNdhkysqyqsbbw();
    }

    /**
     * Create an instance of {@link BqxsqkmxbGrid}
     */
    public BqxsqkmxbGrid createBqxsqkmxbGrid() {
        return new BqxsqkmxbGrid();
    }

    /**
     * Create an instance of {@link NcphdkczzsjxsejsbGrid}
     */
    public NcphdkczzsjxsejsbGrid createNcphdkczzsjxsejsbGrid() {
        return new NcphdkczzsjxsejsbGrid();
    }

    /**
     * Create an instance of {@link ZbGrid}
     */
    public ZbGrid createZbGrid() {
        return new ZbGrid();
    }

    /**
     * Create an instance of {@link ZzsybsbSbbdxxVO}
     */
    public ZzsybsbSbbdxxVO createZzsybsbSbbdxxVO() {
        return new ZzsybsbSbbdxxVO();
    }

    /**
     * Create an instance of {@link JdcxstyfplycybbGridlbVO}
     */
    public JdcxstyfplycybbGridlbVO createJdcxstyfplycybbGridlbVO() {
        return new JdcxstyfplycybbGridlbVO();
    }

    /**
     * Create an instance of {@link JxGridlbVO}
     */
    public JxGridlbVO createJxGridlbVO() {
        return new JxGridlbVO();
    }

    /**
     * Create an instance of {@link QdjxseGridlbVO}
     */
    public QdjxseGridlbVO createQdjxseGridlbVO() {
        return new QdjxseGridlbVO();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrBqdkjxsejgmxbywbw}
     */
    public ZzssyyybnsrBqdkjxsejgmxbywbw createZzssyyybnsrBqdkjxsejgmxbywbw() {
        return new ZzssyyybnsrBqdkjxsejgmxbywbw();
    }

    /**
     * Create an instance of {@link TrccfhdncpzzsjxseGrid}
     */
    public TrccfhdncpzzsjxseGrid createTrccfhdncpzzsjxseGrid() {
        return new TrccfhdncpzzsjxseGrid();
    }

    /**
     * Create an instance of {@link QylxFormVO}
     */
    public QylxFormVO createQylxFormVO() {
        return new QylxFormVO();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbCbfhdccpzzsjxsejsbbw}
     */
    public ZzsybnsrsbCbfhdccpzzsjxsejsbbw createZzsybnsrsbCbfhdccpzzsjxsejsbbw() {
        return new ZzsybnsrsbCbfhdccpzzsjxsejsbbw();
    }

    /**
     * Create an instance of {@link SlxxForm}
     */
    public SlxxForm createSlxxForm() {
        return new SlxxForm();
    }

    /**
     * Create an instance of {@link JdcxstyfpqdGridlbVO}
     */
    public JdcxstyfpqdGridlbVO createJdcxstyfpqdGridlbVO() {
        return new JdcxstyfpqdGridlbVO();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbGdzcjxsedkqkb.GdzcjxsedkqkbformVO}
     */
    public ZzsybnsrsbGdzcjxsedkqkb.GdzcjxsedkqkbformVO createZzsybnsrsbGdzcjxsedkqkbGdzcjxsedkqkbformVO() {
        return new ZzsybnsrsbGdzcjxsedkqkb.GdzcjxsedkqkbformVO();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw.ZzsybnsrsbTlysqyfzjgzzshznsxxcdd}
     */
    public ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw.ZzsybnsrsbTlysqyfzjgzzshznsxxcdd createZzsybnsrsbTlysqyfzjgzzshznsxxcddbwZzsybnsrsbTlysqyfzjgzzshznsxxcdd() {
        return new ZzsybnsrsbTlysqyfzjgzzshznsxxcddbw.ZzsybnsrsbTlysqyfzjgzzshznsxxcdd();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbNcphdkczzsjxsejsb.NcphdkczzsjxsejsbGrid}
     */
    public ZzsybnsrsbNcphdkczzsjxsejsb.NcphdkczzsjxsejsbGrid createZzsybnsrsbNcphdkczzsjxsejsbNcphdkczzsjxsejsbGrid() {
        return new ZzsybnsrsbNcphdkczzsjxsejsb.NcphdkczzsjxsejsbGrid();
    }

    /**
     * Create an instance of {@link Dlqyzzsxxsehjxsecdd.XxGrid}
     */
    public Dlqyzzsxxsehjxsecdd.XxGrid createDlqyzzsxxsehjxsecddXxGrid() {
        return new Dlqyzzsxxsehjxsecdd.XxGrid();
    }

    /**
     * Create an instance of {@link Dlqyzzsxxsehjxsecdd.JxGrid}
     */
    public Dlqyzzsxxsehjxsecdd.JxGrid createDlqyzzsxxsehjxsecddJxGrid() {
        return new Dlqyzzsxxsehjxsecdd.JxGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqyzzsfpbywbw.ZzssyyybnsrHznsqyzzsfpb}
     */
    public ZzssyyybnsrHznsqyzzsfpbywbw.ZzssyyybnsrHznsqyzzsfpb createZzssyyybnsrHznsqyzzsfpbywbwZzssyyybnsrHznsqyzzsfpb() {
        return new ZzssyyybnsrHznsqyzzsfpbywbw.ZzssyyybnsrHznsqyzzsfpb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqyzzsfpb.HznsqyzzsfpbGrid}
     */
    public ZzssyyybnsrHznsqyzzsfpb.HznsqyzzsfpbGrid createZzssyyybnsrHznsqyzzsfpbHznsqyzzsfpbGrid() {
        return new ZzssyyybnsrHznsqyzzsfpb.HznsqyzzsfpbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrCpygxcqkmxb.CpygxcqkmxbGrid}
     */
    public ZzssyyybnsrCpygxcqkmxb.CpygxcqkmxbGrid createZzssyyybnsrCpygxcqkmxbCpygxcqkmxbGrid() {
        return new ZzssyyybnsrCpygxcqkmxb.CpygxcqkmxbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrKstlqyzzsfpbywbw.ZzssyyybnsrKstlqyzzsfpb}
     */
    public ZzssyyybnsrKstlqyzzsfpbywbw.ZzssyyybnsrKstlqyzzsfpb createZzssyyybnsrKstlqyzzsfpbywbwZzssyyybnsrKstlqyzzsfpb() {
        return new ZzssyyybnsrKstlqyzzsfpbywbw.ZzssyyybnsrKstlqyzzsfpb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrKstlqyzzsfpb.KstlqyzzsfpbGrid}
     */
    public ZzssyyybnsrKstlqyzzsfpb.KstlqyzzsfpbGrid createZzssyyybnsrKstlqyzzsfpbKstlqyzzsfpbGrid() {
        return new ZzssyyybnsrKstlqyzzsfpb.KstlqyzzsfpbGrid();
    }

    /**
     * Create an instance of {@link Hqysqyfzjgcdd.YjnzzsqkGrid}
     */
    public Hqysqyfzjgcdd.YjnzzsqkGrid createHqysqyfzjgcddYjnzzsqkGrid() {
        return new Hqysqyfzjgcdd.YjnzzsqkGrid();
    }

    /**
     * Create an instance of {@link Hqysqyfzjgcdd.QdjxseqkGrid}
     */
    public Hqysqyfzjgcdd.QdjxseqkGrid createHqysqyfzjgcddQdjxseqkGrid() {
        return new Hqysqyfzjgcdd.QdjxseqkGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJyzyfjyxxmxb.JyzyfjyxxmxGrid}
     */
    public ZzssyyybnsrJyzyfjyxxmxb.JyzyfjyxxmxGrid createZzssyyybnsrJyzyfjyxxmxbJyzyfjyxxmxGrid() {
        return new ZzssyyybnsrJyzyfjyxxmxb.JyzyfjyxxmxGrid();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr04Bqjxsemxb.BqjxsemxbGrid}
     */
    public Zzssyyybnsr04Bqjxsemxb.BqjxsemxbGrid createZzssyyybnsr04BqjxsemxbBqjxsemxbGrid() {
        return new Zzssyyybnsr04Bqjxsemxb.BqjxsemxbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZb.ZbGrid}
     */
    public ZzssyyybnsrZb.ZbGrid createZzssyyybnsrZbZbGrid() {
        return new ZzssyyybnsrZb.ZbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdclscqyxsqktjb.JdclscqyxsqktjbGrid}
     */
    public ZzssyyybnsrJdclscqyxsqktjb.JdclscqyxsqktjbGrid createZzssyyybnsrJdclscqyxsqktjbJdclscqyxsqktjbGrid() {
        return new ZzssyyybnsrJdclscqyxsqktjb.JdclscqyxsqktjbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJyzyfjyxxmxbywbw.ZzssyyybnsrJyzyfjyxxmxb}
     */
    public ZzssyyybnsrJyzyfjyxxmxbywbw.ZzssyyybnsrJyzyfjyxxmxb createZzssyyybnsrJyzyfjyxxmxbywbwZzssyyybnsrJyzyfjyxxmxb() {
        return new ZzssyyybnsrJyzyfjyxxmxbywbw.ZzssyyybnsrJyzyfjyxxmxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrYqtqyzzsfpb.YqtqyzzsfpbGrid}
     */
    public ZzssyyybnsrYqtqyzzsfpb.YqtqyzzsfpbGrid createZzssyyybnsrYqtqyzzsfpbYqtqyzzsfpbGrid() {
        return new ZzssyyybnsrYqtqyzzsfpb.YqtqyzzsfpbGrid();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr03Ysfwkcxmmx.YsfwkcxmmxGrid}
     */
    public Zzssyyybnsr03Ysfwkcxmmx.YsfwkcxmmxGrid createZzssyyybnsr03YsfwkcxmmxYsfwkcxmmxGrid() {
        return new Zzssyyybnsr03Ysfwkcxmmx.YsfwkcxmmxGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrBqdkjxsejgmxb.BqdkjxsejgmxbGrid}
     */
    public ZzssyyybnsrBqdkjxsejgmxb.BqdkjxsejgmxbGrid createZzssyyybnsrBqdkjxsejgmxbBqdkjxsejgmxbGrid() {
        return new ZzssyyybnsrBqdkjxsejgmxb.BqdkjxsejgmxbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHqysqyfzjgcdd.TrccfhdncpzzsjxseGrid}
     */
    public ZzssyyybnsrHqysqyfzjgcdd.TrccfhdncpzzsjxseGrid createZzssyyybnsrHqysqyfzjgcddTrccfhdncpzzsjxseGrid() {
        return new ZzssyyybnsrHqysqyfzjgcdd.TrccfhdncpzzsjxseGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbYzqyfzjgzzshznsxxcdd.ZzsybnsrsbQdjxseqkGrid}
     */
    public ZzsybnsrsbYzqyfzjgzzshznsxxcdd.ZzsybnsrsbQdjxseqkGrid createZzsybnsrsbYzqyfzjgzzshznsxxcddZzsybnsrsbQdjxseqkGrid() {
        return new ZzsybnsrsbYzqyfzjgzzshznsxxcdd.ZzsybnsrsbQdjxseqkGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbDxqyfzjgzzshznsxxcdd.DxyjnzzsqkGrid}
     */
    public ZzsybnsrsbDxqyfzjgzzshznsxxcdd.DxyjnzzsqkGrid createZzsybnsrsbDxqyfzjgzzshznsxxcddDxyjnzzsqkGrid() {
        return new ZzsybnsrsbDxqyfzjgzzshznsxxcdd.DxyjnzzsqkGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbDxqyfzjgzzshznsxxcdd.QdjxseqkGrid}
     */
    public ZzsybnsrsbDxqyfzjgzzshznsxxcdd.QdjxseqkGrid createZzsybnsrsbDxqyfzjgzzshznsxxcddQdjxseqkGrid() {
        return new ZzsybnsrsbDxqyfzjgzzshznsxxcdd.QdjxseqkGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw.ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb}
     */
    public ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw.ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb createZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbwZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb() {
        return new ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbywbw.ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb.GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid}
     */
    public ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb.GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid createZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsbGjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid() {
        return new ZzssyyybnsrGjncpyyscjyqbgchwsthdncpzzsjxsejsb.GjncpyyscjyqbgchwsthdncpzzsjxsejsbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZzsysfpdkqdywbw.ZzssyyybnsrZzsysfpdkqd}
     */
    public ZzssyyybnsrZzsysfpdkqdywbw.ZzssyyybnsrZzsysfpdkqd createZzssyyybnsrZzsysfpdkqdywbwZzssyyybnsrZzsysfpdkqd() {
        return new ZzssyyybnsrZzsysfpdkqdywbw.ZzssyyybnsrZzsysfpdkqd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZzsysfpdkqd.DkqdGrid}
     */
    public ZzssyyybnsrZzsysfpdkqd.DkqdGrid createZzssyyybnsrZzsysfpdkqdDkqdGrid() {
        return new ZzssyyybnsrZzsysfpdkqd.DkqdGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbJyzyxsyphzb.JyzyxsyphzbGrid}
     */
    public ZzsybnsrsbJyzyxsyphzb.JyzyxsyphzbGrid createZzsybnsrsbJyzyxsyphzbJyzyxsyphzbGrid() {
        return new ZzsybnsrsbJyzyxsyphzb.JyzyxsyphzbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHqysqyfzjgcddywbw.ZzssyyybnsrHqysqyfzjgcdd}
     */
    public ZzssyyybnsrHqysqyfzjgcddywbw.ZzssyyybnsrHqysqyfzjgcdd createZzssyyybnsrHqysqyfzjgcddywbwZzssyyybnsrHqysqyfzjgcdd() {
        return new ZzssyyybnsrHqysqyfzjgcddywbw.ZzssyyybnsrHqysqyfzjgcdd();
    }

    /**
     * Create an instance of {@link Zzsjmssbmxb.ZzsjmssbmxbjsxmGrid}
     */
    public Zzsjmssbmxb.ZzsjmssbmxbjsxmGrid createZzsjmssbmxbZzsjmssbmxbjsxmGrid() {
        return new Zzsjmssbmxb.ZzsjmssbmxbjsxmGrid();
    }

    /**
     * Create an instance of {@link Zzsjmssbmxb.ZzsjmssbmxbmsxmGrid}
     */
    public Zzsjmssbmxb.ZzsjmssbmxbmsxmGrid createZzsjmssbmxbZzsjmssbmxbmsxmGrid() {
        return new Zzsjmssbmxb.ZzsjmssbmxbmsxmGrid();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr02Bqjxsemxb.BqjxsemxbGrid}
     */
    public Zzssyyybnsr02Bqjxsemxb.BqjxsemxbGrid createZzssyyybnsr02BqjxsemxbBqjxsemxbGrid() {
        return new Zzssyyybnsr02Bqjxsemxb.BqjxsemxbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcxstyfpqd.JdcxstyfpqdGrid}
     */
    public ZzssyyybnsrJdcxstyfpqd.JdcxstyfpqdGrid createZzssyyybnsrJdcxstyfpqdJdcxstyfpqdGrid() {
        return new ZzssyyybnsrJdcxstyfpqd.JdcxstyfpqdGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqytycddywbw.ZzssyyybnsrHznsqytycdd}
     */
    public ZzssyyybnsrHznsqytycddywbw.ZzssyyybnsrHznsqytycdd createZzssyyybnsrHznsqytycddywbwZzssyyybnsrHznsqytycdd() {
        return new ZzssyyybnsrHznsqytycddywbw.ZzssyyybnsrHznsqytycdd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqytycdd.HznsqytycddxxGrid}
     */
    public ZzssyyybnsrHznsqytycdd.HznsqytycddxxGrid createZzssyyybnsrHznsqytycddHznsqytycddxxGrid() {
        return new ZzssyyybnsrHznsqytycdd.HznsqytycddxxGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHznsqytycdd.HznsqytycddjxGrid}
     */
    public ZzssyyybnsrHznsqytycdd.HznsqytycddjxGrid createZzssyyybnsrHznsqytycddHznsqytycddjxGrid() {
        return new ZzssyyybnsrHznsqytycdd.HznsqytycddjxGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrYgzsffxcsmxb.YgzsffxcsmxbGrid}
     */
    public ZzssyyybnsrYgzsffxcsmxb.YgzsffxcsmxbGrid createZzssyyybnsrYgzsffxcsmxbYgzsffxcsmxbGrid() {
        return new ZzssyyybnsrYgzsffxcsmxb.YgzsffxcsmxbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcxstyfpqdywbw.ZzssyyybnsrJdcxstyfpqd}
     */
    public ZzssyyybnsrJdcxstyfpqdywbw.ZzssyyybnsrJdcxstyfpqd createZzssyyybnsrJdcxstyfpqdywbwZzssyyybnsrJdcxstyfpqd() {
        return new ZzssyyybnsrJdcxstyfpqdywbw.ZzssyyybnsrJdcxstyfpqd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrCpygxcslmxbywbw.ZzssyyybnsrCpygxcslmxb}
     */
    public ZzssyyybnsrCpygxcslmxbywbw.ZzssyyybnsrCpygxcslmxb createZzssyyybnsrCpygxcslmxbywbwZzssyyybnsrCpygxcslmxb() {
        return new ZzssyyybnsrCpygxcslmxbywbw.ZzssyyybnsrCpygxcslmxb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrCpygxcslmxb.CpygxcslmxbGrid}
     */
    public ZzssyyybnsrCpygxcslmxb.CpygxcslmxbGrid createZzssyyybnsrCpygxcslmxbCpygxcslmxbGrid() {
        return new ZzssyyybnsrCpygxcslmxb.CpygxcslmxbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrYqtqyzzsfpbywbw.ZzssyyybnsrYqtqyzzsfpb}
     */
    public ZzssyyybnsrYqtqyzzsfpbywbw.ZzssyyybnsrYqtqyzzsfpb createZzssyyybnsrYqtqyzzsfpbywbwZzssyyybnsrYqtqyzzsfpb() {
        return new ZzssyyybnsrYqtqyzzsfpbywbw.ZzssyyybnsrYqtqyzzsfpb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcljxqyxsmxb.JdcljxqyxsmxbGrid}
     */
    public ZzssyyybnsrJdcljxqyxsmxb.JdcljxqyxsmxbGrid createZzssyyybnsrJdcljxqyxsmxbJdcljxqyxsmxbGrid() {
        return new ZzssyyybnsrJdcljxqyxsmxb.JdcljxqyxsmxbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdclscqyxsmxb.SbbheadVO}
     */
    public ZzssyyybnsrJdclscqyxsmxb.SbbheadVO createZzssyyybnsrJdclscqyxsmxbSbbheadVO() {
        return new ZzssyyybnsrJdclscqyxsmxb.SbbheadVO();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdclscqyxsmxb.JdcljxqyxsmxbGrid}
     */
    public ZzssyyybnsrJdclscqyxsmxb.JdcljxqyxsmxbGrid createZzssyyybnsrJdclscqyxsmxbJdcljxqyxsmxbGrid() {
        return new ZzssyyybnsrJdclscqyxsmxb.JdcljxqyxsmxbGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbDkdjsstyjksdkqd.DkdjsstyjksdkqdGrid}
     */
    public ZzsybnsrsbDkdjsstyjksdkqd.DkdjsstyjksdkqdGrid createZzsybnsrsbDkdjsstyjksdkqdDkdjsstyjksdkqdGrid() {
        return new ZzsybnsrsbDkdjsstyjksdkqd.DkdjsstyjksdkqdGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrJdcxstyfplycybb.JdcxstyfplycybbGrid}
     */
    public ZzssyyybnsrJdcxstyfplycybb.JdcxstyfplycybbGrid createZzssyyybnsrJdcxstyfplycybbJdcxstyfplycybbGrid() {
        return new ZzssyyybnsrJdcxstyfplycybb.JdcxstyfplycybbGrid();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr01Bqxsqkmxb.BqxsqkmxbGrid}
     */
    public Zzssyyybnsr01Bqxsqkmxb.BqxsqkmxbGrid createZzssyyybnsr01BqxsqkmxbBqxsqkmxbGrid() {
        return new Zzssyyybnsr01Bqxsqkmxb.BqxsqkmxbGrid();
    }

    /**
     * Create an instance of {@link Hgwspzdklsjcjb.HgwspzdklsjGrid}
     */
    public Hgwspzdklsjcjb.HgwspzdklsjGrid createHgwspzdklsjcjbHgwspzdklsjGrid() {
        return new Hgwspzdklsjcjb.HgwspzdklsjGrid();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbCbfhdccpzzsjxsejsb.ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid}
     */
    public ZzsybnsrsbCbfhdccpzzsjxsejsb.ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid createZzsybnsrsbCbfhdccpzzsjxsejsbZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid() {
        return new ZzsybnsrsbCbfhdccpzzsjxsejsb.ZzsybnsrsbCbfhdccpzzsjxsejsbbwGrid();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr05Bdcfqdkjsb.BdcfqdkjsbGrid}
     */
    public Zzssyyybnsr05Bdcfqdkjsb.BdcfqdkjsbGrid createZzssyyybnsr05BdcfqdkjsbBdcfqdkjsbGrid() {
        return new Zzssyyybnsr05Bdcfqdkjsb.BdcfqdkjsbGrid();
    }

    /**
     * Create an instance of {@link HXZGSB00041Request.YjxxGrid.YjxxGridlb}
     */
    public HXZGSB00041Request.YjxxGrid.YjxxGridlb createHXZGSB00041RequestYjxxGridYjxxGridlb() {
        return new HXZGSB00041Request.YjxxGrid.YjxxGridlb();
    }

    /**
     * Create an instance of {@link HXZGSB00041Request.JmxxGrid.JmxxGridlb}
     */
    public HXZGSB00041Request.JmxxGrid.JmxxGridlb createHXZGSB00041RequestJmxxGridJmxxGridlb() {
        return new HXZGSB00041Request.JmxxGrid.JmxxGridlb();
    }

    /**
     * Create an instance of {@link HXZGSB00041Request.SbxxGrid.SbxxGridlb}
     */
    public HXZGSB00041Request.SbxxGrid.SbxxGridlb createHXZGSB00041RequestSbxxGridSbxxGridlb() {
        return new HXZGSB00041Request.SbxxGrid.SbxxGridlb();
    }

    /**
     * Create an instance of {@link ZzsybnsrsbNdhkysqyqsb.NdhkysqyqsbGrid.NdhkysqyqsbGridlb}
     */
    public ZzsybnsrsbNdhkysqyqsb.NdhkysqyqsbGrid.NdhkysqyqsbGridlb createZzsybnsrsbNdhkysqyqsbNdhkysqyqsbGridNdhkysqyqsbGridlb() {
        return new ZzsybnsrsbNdhkysqyqsb.NdhkysqyqsbGrid.NdhkysqyqsbGridlb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrTljsjjnssbb.TljsjjnssbbGrid}
     */
    public ZzssyyybnsrTljsjjnssbb.TljsjjnssbbGrid createZzssyyybnsrTljsjjnssbbTljsjjnssbbGrid() {
        return new ZzssyyybnsrTljsjjnssbb.TljsjjnssbbGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb.GjncpzjxshdncpzzsjxseGrid}
     */
    public ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb.GjncpzjxshdncpzzsjxseGrid createZzssyyybnsrGjncpzjxshdncpzzsjxsejsbGjncpzjxshdncpzzsjxseGrid() {
        return new ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb.GjncpzjxshdncpzzsjxseGrid();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw.ZzssyyybnsrZzsybnsrbqynseayskmfjb}
     */
    public ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw.ZzssyyybnsrZzsybnsrbqynseayskmfjb createZzssyyybnsrZzsybnsrbqynseayskmfjbywbwZzssyyybnsrZzsybnsrbqynseayskmfjb() {
        return new ZzssyyybnsrZzsybnsrbqynseayskmfjbywbw.ZzssyyybnsrZzsybnsrbqynseayskmfjb();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw.ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb}
     */
    public ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw.ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb createZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbwZzssyyybnsrGjncpzjxshdncpzzsjxsejsb() {
        return new ZzssyyybnsrGjncpzjxshdncpzzsjxsejsbywbw.ZzssyyybnsrGjncpzjxshdncpzzsjxsejsb();
    }

    /**
     * Create an instance of {@link Zzssyyybnsr11Hqysqyfzjgcddywbw.Hqysqyfzjgcdd}
     */
    public Zzssyyybnsr11Hqysqyfzjgcddywbw.Hqysqyfzjgcdd createZzssyyybnsr11HqysqyfzjgcddywbwHqysqyfzjgcdd() {
        return new Zzssyyybnsr11Hqysqyfzjgcddywbw.Hqysqyfzjgcdd();
    }

    /**
     * Create an instance of {@link ZzssyyybnsrHgwspzdklsjcjbywbw.Hgwspzdklsjcjb}
     */
    public ZzssyyybnsrHgwspzdklsjcjbywbw.Hgwspzdklsjcjb createZzssyyybnsrHgwspzdklsjcjbywbwHgwspzdklsjcjb() {
        return new ZzssyyybnsrHgwspzdklsjcjbywbw.Hgwspzdklsjcjb();
    }

    /**
     * Create an instance of {@link Zzsjmssbmxbywbw.Zzsjmssbmxb}
     */
    public Zzsjmssbmxbywbw.Zzsjmssbmxb createZzsjmssbmxbywbwZzsjmssbmxb() {
        return new Zzsjmssbmxbywbw.Zzsjmssbmxb();
    }

    /**
     * Create an instance of {@link JAXBElement}{@code <}{@link TaxDoc}{@code >}}
     */
    @XmlElementDecl(namespace = "http://www.chinatax.gov.cn/dataspec/", name = "taxML")
    public JAXBElement<TaxDoc> createTaxML(TaxDoc value) {
        return new JAXBElement<TaxDoc>(_TaxML_QNAME, TaxDoc.class, null, value);
    }
}