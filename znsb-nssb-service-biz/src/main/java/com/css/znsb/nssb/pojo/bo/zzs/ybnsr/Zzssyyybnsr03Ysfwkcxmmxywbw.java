package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 增值税纳税申报表附列资料三（应税服务扣除项目明细）
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr03_ysfwkcxmmxywbw", propOrder = { "zzssyyybnsr03Ysfwkcxmmx" })
@Getter
@Setter
public class Zzssyyybnsr03Ysfwkcxmmxywbw extends TaxDoc {
    /**
     * 增值税纳税申报表附列资料三（应税服务扣除项目明细）
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr03_ysfwkcxmmx", required = true)
    @JSONField(name = "zzssyyybnsr03_ysfwkcxmmx")
    protected Zzssyyybnsr03Ysfwkcxmmx zzssyyybnsr03Ysfwkcxmmx;
}