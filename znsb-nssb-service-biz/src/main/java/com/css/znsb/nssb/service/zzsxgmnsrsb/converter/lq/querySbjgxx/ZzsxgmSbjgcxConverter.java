package com.css.znsb.nssb.service.zzsxgmnsrsb.converter.lq.querySbjgxx;

import cn.hutool.core.util.StrUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.Base64Utils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhHandlerDTO;
import com.css.znsb.framework.sjjh.service.sjjh.fw.converter.SjjhDataConverter;
import com.css.znsb.nssb.constants.YclxConstants;
import com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc.general.SBSbbdBdjgVO;
import com.css.znsb.nssb.pojo.bo.hxzg.zzsybnsrsb.sbbc.general.SBSbbdBdjgmxVO;
import com.css.znsb.nssb.pojo.dto.sbjgcx.LqfzptSbjgcxReqDTO;
import com.css.znsb.nssb.pojo.dto.sbjgcx.LqfzptSbjgcxRespDTO;
import com.css.znsb.nssb.pojo.dto.sbjgcx.LqsbjgDTO;
import com.css.znsb.nssb.pojo.dto.sbjgcx.SbjgcxRespDTO;
import com.css.znsb.nssb.pojo.dto.xgmnsr.scjgcx.ZzsxgmnsrScjgcxBdjgDTO;
import com.css.znsb.nssb.pojo.dto.xgmnsr.scjgcx.ZzsxgmnsrScjgcxBdjgmxDTO;
import com.css.znsb.nssb.pojo.dto.xgmnsr.scjgcx.ZzsxgmnsrScjgcxSbjgsjDTO;
import com.css.znsb.nssb.pojo.dto.xgmnsr.scjgcx.ZzsxgmnsrScjgcxSbxxDTO;
import com.css.znsb.nssb.pojo.vo.zzsyjsb.lq.saveSbxx.ZzsyjSaveLQReqVO;
import com.css.znsb.nssb.service.sbrw.ZnsbNssbSbrwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import static com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants.ERROR;
import static com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.css.znsb.nssb.constants.SbrwztConstants.SBZT_BDBTG_DM;
import static com.css.znsb.nssb.constants.SbrwztConstants.SBZT_SBCG_DM;
import static com.css.znsb.nssb.constants.SbrwztConstants.SBZT_SBSB_DM;
import static com.css.znsb.nssb.constants.SbrwztConstants.SBZT_XTERROR_DM;

@Slf4j
@Service(value = "ZzsxgmSbjgcxConverter")
public class ZzsxgmSbjgcxConverter implements SjjhDataConverter {

    @Resource
    private ZnsbNssbSbrwService sbrwService;

    @Override
    public String convertReq(SjjhHandlerDTO sjjhHandlerDTO) {
        /*
        -- 增值税预缴  申报结果查询
        -- cxfwbm  LQZZSYJSB03
        -- fwbm  LQZZSYJSB03
        -- jkbm 截取 LQ_CXLQZZSYJSBJG
        -- jkdz 目前是1 后续要改成乐企真实接口地址
        -- jkcsztdm 申报查询结果查询 必须一致 去掉中间无用状态
        -- ZzsyjSbjgcxConverter
        select * from znsb_csdm.cs_sjjh_pz where cxfwbm  ='LQZZSYJSB03';
        select t1.* from znsb_csdm.cs_sjjh_fwsp t1,znsb_csdm.cs_sjjh_fwpz t2 where t1.fwspuuid = t2.fwspuuid and t1.fwbm='LQZZSYJSB03';
        select t1.* from znsb_csdm.cs_znsb_jkpz t1,znsb_csdm.cs_sjjh_fwsp t2 where t1.jkbm ='LQ_CXLQZZSYJSBJG' and t1.jkbm = SUBSTRING_INDEX(SUBSTRING_INDEX(t2.pzms, '"', 4), '"', -1);
        select t1.* from znsb_csdm.xt_dbpzb t1,znsb_csdm.cs_znsb_jkpz t2 where t1.api = t2.jkbm and t1.api ='LQ_CXLQZZSYJSBJG';
        */
        log.info("增值税小规模查询申报结果待传入转换ZzsxgmSbjgcxConverter的convertReq入参req{}:", JsonUtils.toJson(sjjhHandlerDTO));
        final String bwnr = sjjhHandlerDTO.getSjjhDTO().getBwnr();
        final ZzsyjSaveLQReqVO sbDTO = JsonUtils.toBean(bwnr, ZzsyjSaveLQReqVO.class);
        final LqfzptSbjgcxReqDTO sbcxReq = new LqfzptSbjgcxReqDTO();
        sbcxReq.setDjxh(StrUtil.toString(sjjhHandlerDTO.getSjjhDTO().getDjxh()));
        sbcxReq.setPclsh(sbDTO.getPclsh());//放入批次流水号
        log.info("增值税小规模查询申报结果待传入转换ZzsxgmSbjgcxConverter的convertReq批次流水号"+sbDTO.getPclsh());
        log.info("增值税小规模查询申报结果待传入转换ZzsxgmSbjgcxConverter的convertReq入参res{}:",JsonUtils.toJson(sbcxReq));
        return JsonUtils.toJson(sbcxReq);
    }

    /*@Override
    public CommonResult<Object> convertResp(String resp) {
        log.debug("=====乐企辅助平台申报结果查询转换响应开始=====");
        log.debug("增值税小规模查询申报结果待返回转换ZzsxgmSbjgcxConverter的convertResp入参req：" + resp);
        final SbjgcxRespDTO sbjgcxRespDTO = new SbjgcxRespDTO();
        String yclxDm;
        String ycxx;
        String sbyysm;
        try {
            if (GyUtils.isNull(resp)) {
                yclxDm = YclxConstants.QYDYWYC_YCLX_DM;
                ycxx = "乐企查询上传处理结果返回数据为空";
                sbyysm = "乐企查询上传处理结果返回数据为空，请联系系统运维人员。";
                sbjgcxRespDTO.setYclxDm(yclxDm);
                sbjgcxRespDTO.setYcxx(ycxx);
                sbjgcxRespDTO.setSbyysm(sbyysm);
                sbjgcxRespDTO.setSbztDm(SBZT_SBSB_DM);
                return CommonResult.error(ERROR.getCode(), sbyysm, sbjgcxRespDTO);
            }
            final LqfzptSbjgcxRespDTO sbcxResp = JsonUtils.toBean(resp, LqfzptSbjgcxRespDTO.class);
            if (GyUtils.isNull(sbcxResp)) {
                yclxDm = YclxConstants.QYDYWYC_YCLX_DM;
                ycxx = "乐企查询上传处理结果返回报文转换失败";
                sbyysm = "乐企查询上传处理结果返回报文转换失败，请联系系统运维人员。";
                sbjgcxRespDTO.setYclxDm(yclxDm);
                sbjgcxRespDTO.setYcxx(ycxx);
                sbjgcxRespDTO.setSbyysm(sbyysm);
                sbjgcxRespDTO.setSbztDm(SBZT_SBSB_DM);
                return CommonResult.error(ERROR.getCode(), sbyysm, sbjgcxRespDTO);
            }
            // 判断状态
            final String returncode = sbcxResp.getReturncode();
            // 判断算税业务状态代码
            final String ssywztDm = sbcxResp.getSsywztDm();
            final String sycjjsbywztDm = sbcxResp.getSycjjsbywztDm();
            sbjgcxRespDTO.setSsywztDm(ssywztDm);
            if ("00".equals(returncode) && "00".equals(ssywztDm) && "00".equals(sycjjsbywztDm)
                    && GyUtils.isNotNull(sbcxResp.getSbjg())
                    && sbcxResp.getSbjg().stream().allMatch(t -> "00".equals(t.getClzt()))) {
                // 成功
                sbjgcxRespDTO.setPclsh(sbcxResp.getPclsh());
                sbjgcxRespDTO.setSbztDm(SBZT_SBCG_DM);
                handlerSbcg(sbcxResp, sbjgcxRespDTO);
                return CommonResult.success(sbjgcxRespDTO);
            } else {
                // 失败
                if ("99".equals(returncode)) {
                    yclxDm = YclxConstants.LQXTYC_YCLX_DM;
                    ycxx = sbcxResp.getReturnmsg();
                    sbyysm = sbcxResp.getReturnmsg();
                } else {
                    yclxDm = LQYWYC_YCLX_DM;
                    ycxx = sbcxResp.getReturnmsg();
                    sbyysm = sbcxResp.getReturnmsg();
                }
                if ("09".equals(ssywztDm)) {
                    yclxDm = LQYWYC_YCLX_DM;
                    ycxx = sbcxResp.getSsywztxx();
                    sbyysm = sbcxResp.getSsywztxx();
                } else if ("99".equals(ssywztDm)) {
                    yclxDm = YclxConstants.LQXTYC_YCLX_DM;
                    ycxx = sbcxResp.getSsywztxx();
                    sbyysm = sbcxResp.getSsywztxx();
                }
                if ("99".equals(sycjjsbywztDm)) {
                    yclxDm = YclxConstants.LQXTYC_YCLX_DM;
                    ycxx = sbcxResp.getSycjjsbywztxx();
                    sbyysm = sbcxResp.getSycjjsbywztxx();
                } else if ("01".equals(sycjjsbywztDm)) {
                    yclxDm = LQYWYC_YCLX_DM;
                    ycxx = sbcxResp.getSycjjsbywztxx();
                    sbyysm = sbcxResp.getSycjjsbywztxx();
                }
                sbjgcxRespDTO.setSsywztDm(ssywztDm);
                sbjgcxRespDTO.setPclsh(sbcxResp.getPclsh());
                if (GyUtils.isNull(yclxDm)) {
                    handlerSbcg(sbcxResp, sbjgcxRespDTO);
                    if (GyUtils.isNull(sbjgcxRespDTO.getPzxh()) || GyUtils.isNull(sbjgcxRespDTO.getSbuuid())
                            || GyUtils.isNull(sbjgcxRespDTO.getYbtse())) {
                        // 没有数据，失败
                        sbyysm = "乐企上报结果数据解析失败，请联系系统运维人员。";
                        ycxx = "乐企上报结果数据解析失败";
                        yclxDm = YclxConstants.QYDYWYC_YCLX_DM;
                    }
                }
                sbjgcxRespDTO.setSbyysm(sbyysm);
                sbjgcxRespDTO.setYcxx(ycxx);
                sbjgcxRespDTO.setYclxDm(yclxDm);
                String sbztDm = SBZT_SBSB_DM;
                if (LQYWYC_YCLX_DM.equals(yclxDm)) {
                    sbztDm = SBZT_XTERROR_DM;
                }
                sbjgcxRespDTO.setSbztDm(sbztDm);
                return CommonResult.error(ERROR.getCode(), sbyysm, sbjgcxRespDTO);
            }
        } catch (Exception e) {
            log.error("申报结果查询返回报文解析失败，失败原因：", e);
            final StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer));
            yclxDm = YclxConstants.QYDXTYC_YCLX_DM;
            ycxx = writer.toString();
            sbyysm = e.getMessage();
            sbjgcxRespDTO.setSbyysm(sbyysm);
            sbjgcxRespDTO.setYclxDm(yclxDm);
            sbjgcxRespDTO.setYcxx(ycxx);
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), sbyysm, sbjgcxRespDTO);
        }
    }

    *//**
     * 申报成功处理
     *
     * @param sbcxResp 申报查询返回
     * @param sbjgcxRespDTO 申报结果查询返回
     *//*
    private void handlerSbcg(LqfzptSbjgcxRespDTO sbcxResp, SbjgcxRespDTO sbjgcxRespDTO) {
        final List<LqsbjgDTO> sbjgList = sbcxResp.getSbjg();
        if (GyUtils.isNotNull(sbjgList)) {
            // 先只取第一条处理
            final LqsbjgDTO lqsbjgDTO = sbjgList.get(0);
            final String sbjgsj = lqsbjgDTO.getSbjgsj();
            if (GyUtils.isNotNull(sbjgsj)) {
                final String sbjgJson = Base64Utils.decode(sbjgsj);
                final LqsbjgsjDTO lqsbjgsjDTO = JsonUtils.toBean(sbjgJson, LqsbjgsjDTO.class);
                final LqsbxxDTO lqsbxxDTO = JsonUtils.toBean(sbjgJson, LqsbxxDTO.class);
                if (GyUtils.isNotNull(lqsbjgsjDTO) && GyUtils.isNotNull(lqsbjgsjDTO.getSbxx())) {
                    sbjgcxRespDTO.setYbtse(lqsbjgsjDTO.getSbxx().getYbtse());
                    sbjgcxRespDTO.setSbuuid(lqsbjgsjDTO.getSbxx().getSbuuid());
                    sbjgcxRespDTO.setPzxh(lqsbjgsjDTO.getSbxx().getPzxh());
                } else if (GyUtils.isNotNull(lqsbxxDTO)) {
                    sbjgcxRespDTO.setYbtse(lqsbxxDTO.getYbtse());
                    sbjgcxRespDTO.setSbuuid(lqsbxxDTO.getSbuuid());
                    sbjgcxRespDTO.setPzxh(lqsbxxDTO.getPzxh());
                }
            }
        }
    }*/

    //按照一般人比对不通过编写代码 如果乐企返回了比对不通过 则走改分支，如果乐企未返回走之前分支
    @Override
    public CommonResult<Object> convertResp(String resp) {
        log.info("=====乐企辅助平台申报结果查询转换响应开始=====");
        log.info("增值税小规模查询申报结果待返回转换ZzsxgmSbjgcxConverter的convertResp入参req：" + resp);
        try {
            ResponseHandler handler = ResponseHandlerFactory.createHandler(resp);
            return handler.handleResponse(resp);
        } catch (Exception e) {
            return ExceptionHandler.handle(e);
        }
    }

    // 策略接口
    interface ResponseHandler {
        CommonResult<Object> handleResponse(String resp);
    }

    // 空响应处理器
    static class EmptyResponseHandler implements ResponseHandler {
        @Override
        public CommonResult<Object> handleResponse(String resp) {
            return ErrorResultFactory.createEmptyResponseResult();
        }
    }

    // 转换失败处理器
    static class ConversionErrorHandler implements ResponseHandler {
        @Override
        public CommonResult<Object> handleResponse(String resp) {
            return ErrorResultFactory.createConversionErrorResult();
        }
    }

    // 成功响应处理器
    static class SuccessResponseHandler implements ResponseHandler {
        private final LqfzptSbjgcxRespDTO sbcxResp;

        SuccessResponseHandler(LqfzptSbjgcxRespDTO sbcxResp) {
            this.sbcxResp = sbcxResp;
        }

        @Override
        public CommonResult<Object> handleResponse(String resp) {
            SbjgcxRespDTO result = new SbjgcxRespDTO();
            result.setPclsh(sbcxResp.getPclsh());
            result.setSbztDm(SBZT_SBCG_DM);

            new SuccessDataProcessor(sbcxResp, result)
                    .processSbcg();

            return CommonResult.success(result);
        }
    }

    // 处理中响应处理器
    static class ProcessingResponseHandler implements ResponseHandler {
        private final LqfzptSbjgcxRespDTO sbcxResp;

        ProcessingResponseHandler(LqfzptSbjgcxRespDTO sbcxResp) {
            this.sbcxResp = sbcxResp;
        }

        @Override
        public CommonResult<Object> handleResponse(String resp) {
            SbjgcxRespDTO result = new SbjgcxRespDTO();
            result.setYclxDm(YclxConstants.LQXTYC_YCLX_DM);
            result.setYcxx("乐企一直处理中，无返回");
            result.setSbyysm("与税局接口通信无响应。");
            result.setSbztDm(SBZT_SBSB_DM);
            return CommonResult.error(ERROR.getCode(), result.getSbyysm(), result);
        }
    }

    // 错误响应处理器
    static class ErrorResponseHandler implements ResponseHandler {
        private final LqfzptSbjgcxRespDTO sbcxResp;

        ErrorResponseHandler(LqfzptSbjgcxRespDTO sbcxResp) {
            this.sbcxResp = sbcxResp;
        }

        @Override
        public CommonResult<Object> handleResponse(String resp) {
            ErrorContext context = new ErrorContext.Builder(sbcxResp)
                    .determineErrorType()
                    .buildErrorInfo();

            SbjgcxRespDTO result = new SbjgcxRespDTO();
            result.setSsywztDm(sbcxResp.getSsywztDm());
            result.setPclsh(sbcxResp.getPclsh());
            result.setSbyysm(context.getSbyysm());
            result.setYcxx(context.getYcxx());
            result.setYclxDm(context.getYclxDm());
            result.setSbztDm(context.getSbztDm());

            new ErrorDataProcessor(sbcxResp, result).processData();

            return CommonResult.error(ERROR.getCode(), context.getSbyysm(), result);
        }
    }

    // 响应处理器工厂
    static class ResponseHandlerFactory {
        static ResponseHandler createHandler(String resp) {
            if (GyUtils.isNull(resp)) {
                return new EmptyResponseHandler();
            }

            LqfzptSbjgcxRespDTO sbcxResp = JsonUtils.toBean(resp, LqfzptSbjgcxRespDTO.class);
            if (sbcxResp == null) {
                return new ConversionErrorHandler();
            }

            ResponseStatus status = ResponseStatusEvaluator.evaluate(sbcxResp);
            switch (status) {
                case SUCCESS:
                    return new SuccessResponseHandler(sbcxResp);
                case PROCESSING:
                    return new ProcessingResponseHandler(sbcxResp);
                default:
                    return new ErrorResponseHandler(sbcxResp);
            }
        }
    }

    // 响应状态评估器
    static class ResponseStatusEvaluator {
        static ResponseStatus evaluate(LqfzptSbjgcxRespDTO dto) {
            if (isSuccess(dto)) {
                return ResponseStatus.SUCCESS;
            }
            if (isProcessing(dto)) {
                return ResponseStatus.PROCESSING;
            }
            return ResponseStatus.ERROR;
        }

        private static boolean isSuccess(LqfzptSbjgcxRespDTO dto) {
            return "00".equals(dto.getReturncode())
                    && "00".equals(dto.getSsywztDm())
                    && "00".equals(dto.getSycjjsbywztDm())
                    && isValidSbjg(dto.getSbjg());
        }

        private static boolean isValidSbjg(List<LqsbjgDTO> sbjg) {
            return sbjg != null && sbjg.stream().allMatch(t -> "00".equals(t.getClzt()));
        }

        private static boolean isProcessing(LqfzptSbjgcxRespDTO dto) {
            return "03".equals(dto.getReturncode())
                    || "02".equals(dto.getSycjjsbywztDm())
                    || (dto.getSbjg() != null && dto.getSbjg().stream().anyMatch(t -> "03".equals(t.getClzt())));
        }
    }

    // 成功数据处理模板
    static class SuccessDataProcessor {
        private final LqfzptSbjgcxRespDTO sbcxResp;
        private final SbjgcxRespDTO result;

        SuccessDataProcessor(LqfzptSbjgcxRespDTO sbcxResp, SbjgcxRespDTO result) {
            this.sbcxResp = sbcxResp;
            this.result = result;
        }

        SuccessDataProcessor processSbcg() {
            SbjgDataParser.parseSbcgData(sbcxResp, result);
            return this;
        }

        SuccessDataProcessor processBdjg() {
            SbjgDataParser.parseBdjgData(sbcxResp, result);
            return this;
        }
    }

    // 数据解析工具类
    static class SbjgDataParser {
        static void parseSbcgData(LqfzptSbjgcxRespDTO sbcxResp, SbjgcxRespDTO result) {
            parseSbjgData(sbcxResp, result, dto -> {
                ZzsxgmnsrScjgcxSbxxDTO sbxx = dto.getSbxx();
                if (sbxx != null && GyUtils.isNotNull(sbxx.getYbtse()) && GyUtils.isNotNull(sbxx.getSbuuid())
                        && GyUtils.isNotNull(sbxx.getPzxh())) {
                    result.setYbtse(GyUtils.isNull(sbxx.getYbtse()) ? "0.00" : sbxx.getYbtse().toPlainString());
                    result.setSbuuid(sbxx.getSbuuid());
                    result.setPzxh(sbxx.getPzxh());
                }
            });
        }

        static void parseBdjgData(LqfzptSbjgcxRespDTO sbcxResp, SbjgcxRespDTO result) {
            parseSbjgData(sbcxResp, result, dto -> {
                ZzsxgmnsrScjgcxSbxxDTO sbxx = dto.getSbxx();
                if (sbxx != null && sbxx.getXgmycbdxx() != null) {
                    BdjgProcessor.process(sbxx.getXgmycbdxx(), result);
                }
            });
        }

        private static void parseSbjgData(LqfzptSbjgcxRespDTO sbcxResp,
                                          SbjgcxRespDTO result,
                                          Consumer<ZzsxgmnsrScjgcxSbjgsjDTO> processor) {
            Optional.ofNullable(sbcxResp.getSbjg())
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0))
                    .map(LqsbjgDTO::getSbjgsj)
                    .map(Base64Utils::decode)
                    .map(json -> JsonUtils.toBean(json, ZzsxgmnsrScjgcxSbjgsjDTO.class))
                    .ifPresent(processor);
        }
    }

    // 表单结果处理器
    static class BdjgProcessor {
        static void process(ZzsxgmnsrScjgcxBdjgDTO ycsbdxx, SbjgcxRespDTO result) {
            SBSbbdBdjgVO bdjgVO = new SBSbbdBdjgVO();
            String zbuuid = GyUtils.getUuid();
            bdjgVO.setUuid(zbuuid);

            List<SBSbbdBdjgmxVO> mxList = processBdjgmx(ycsbdxx, zbuuid);

            result.setSbztDm(SBZT_BDBTG_DM);
            result.setBdjgVO(bdjgVO);
            result.setBdjgmxList(mxList);
            result.setSfczzzssbbdycwcl("Y");
        }

        private static List<SBSbbdBdjgmxVO> processBdjgmx(ZzsxgmnsrScjgcxBdjgDTO ycsbdxx, String zbuuid) {
            List<SBSbbdBdjgmxVO> mxList = new ArrayList<>();
            int xh = 1;

            for (ZzsxgmnsrScjgcxBdjgmxDTO dto : ycsbdxx.getBdjgmxGrid().getBdjgmxGridlb()) {
                if (shouldProcessBdjgmx(dto)) {
                    mxList.add(createBdjgmxVO(dto, zbuuid, xh++));
                }
            }
            return mxList;
        }

        private static boolean shouldProcessBdjgmx(ZzsxgmnsrScjgcxBdjgmxDTO dto) {
            return "N".equals(dto.getBdjg())
                    && !"3".equals(dto.getJkjb())
                    && ("N".equals(dto.getZfbz1()) || GyUtils.isNull(dto.getZfbz1()));
        }

        private static SBSbbdBdjgmxVO createBdjgmxVO(ZzsxgmnsrScjgcxBdjgmxDTO dto, String zbuuid, int xh) {
            SBSbbdBdjgmxVO vo = new SBSbbdBdjgmxVO();
            vo.setUuid(GyUtils.getUuid());
            vo.setZbuuid(zbuuid);
            vo.setXh(xh);
            vo.setSbbdxmDm(dto.getBdgzDm());
            vo.setBdxmmc(dto.getBdmc());
            vo.setSbsxMc(dto.getBdmc());
            vo.setBtgyy(dto.getBdnr());
            vo.setTsxx(dto.getBdbfjgbcnr());
            vo.setBdjg(dto.getBdjg());
            setJkjb(vo, dto.getJkjb());
            return vo;
        }

        private static void setJkjb(SBSbbdBdjgmxVO vo, String jkjb) {
            if ("1".equals(jkjb)) {
                vo.setSfqzzdbz("02");
            } else if ("2".equals(jkjb)) {
                vo.setSfqzzdbz("01");
            }
        }
    }

    // 异常数据处理
    static class ErrorDataProcessor {
        private final LqfzptSbjgcxRespDTO sbcxResp;
        private final SbjgcxRespDTO result;

        ErrorDataProcessor(LqfzptSbjgcxRespDTO sbcxResp, SbjgcxRespDTO result) {
            this.sbcxResp = sbcxResp;
            this.result = result;
        }

        void processData() {
//            if (needSupplementData()) {
            supplementData();
            validateSupplementData();
//            }
        }

        private boolean needSupplementData() {
            return result.getYclxDm() == null;
        }

        private void supplementData() {
            new SuccessDataProcessor(sbcxResp, result)
                    .processSbcg()
                    .processBdjg();
        }

        private void validateSupplementData() {
            if (isInvalidSupplementData()) {
                result.setSbyysm(sbcxResp.getSycjjsbywztxx());
                result.setYcxx(sbcxResp.getSycjjsbywztxx());
                result.setYclxDm(YclxConstants.QYDYWYC_YCLX_DM);
                result.setSbztDm(SBZT_SBSB_DM);
            }
        }

        private boolean isInvalidSupplementData() {
            return (GyUtils.isNull(result.getPzxh())
                    || GyUtils.isNull(result.getSbuuid())
                    || GyUtils.isNull(result.getYbtse()))
                    && !"Y".equals(result.getSfczzzssbbdycwcl())
                    && "00".equals(result.getSsywztDm());
        }
    }

    // 异常上下文构建器
    static class ErrorContext {
        private final String yclxDm;
        private final String ycxx;
        private final String sbyysm;
        private final String sbztDm;

        private ErrorContext(Builder builder) {
            this.yclxDm = builder.yclxDm;
            this.ycxx = builder.ycxx;
            this.sbyysm = builder.sbyysm;
            this.sbztDm = builder.sbztDm;
        }

        static class Builder {
            private final LqfzptSbjgcxRespDTO dto;
            private String yclxDm;
            private String ycxx;
            private String sbyysm;
            private String sbztDm = SBZT_SBSB_DM;

            Builder(LqfzptSbjgcxRespDTO dto) {
                this.dto = dto;
            }

            Builder determineErrorType() {
                if (!"00".equals(dto.getReturncode())) {
                    handleReturnCodeError();
                } else if (!"00".equals(dto.getSsywztDm())) {
                    handleSsywError();
                } else {
                    handleSycjjError();
                }
                return this;
            }

            private void handleReturnCodeError() {
                yclxDm = YclxConstants.LQXTYC_YCLX_DM;
                ycxx = dto.getReturnmsg();
                sbyysm = dto.getReturnmsg();
            }

            private void handleSsywError() {
                yclxDm = YclxConstants.LQYWYC_YCLX_DM;
                ycxx = dto.getSsywztxx();
                sbyysm = dto.getSsywztxx();
            }

            private void handleSycjjError() {
                yclxDm = YclxConstants.LQYWYC_YCLX_DM;
                if ("99".equals(dto.getSycjjsbywztDm())) {
                    process99Error();
                } else {
                    ycxx = dto.getSycjjsbywztxx();
                    sbyysm = dto.getSycjjsbywztxx();
                }
            }

            private void process99Error() {
                Optional.ofNullable(dto.getSbjg())
                        .filter(list -> !list.isEmpty())
                        .map(list -> list.get(0))
                        .ifPresent(this::extractSbxxError);
            }

            private void extractSbxxError(LqsbjgDTO dto) {
                String message = Optional.ofNullable(dto.getSbjgsj())
                        .map(Base64Utils::decode)
                        .map(json -> JsonUtils.toBean(json, ZzsxgmnsrScjgcxSbjgsjDTO.class))
                        .map(ZzsxgmnsrScjgcxSbjgsjDTO::getSbxx)
                        .map(ZzsxgmnsrScjgcxSbxxDTO::getMessage)
                        .orElse(dto.getSbyy());
                ycxx = message;
                sbyysm = message;
            }

            ErrorContext buildErrorInfo() {
                if (YclxConstants.LQYWYC_YCLX_DM.equals(yclxDm)) {
                    sbztDm = SBZT_XTERROR_DM;
                }
                return new ErrorContext(this);
            }
        }

        // Getters
        public String getYclxDm() { return yclxDm; }
        public String getYcxx() { return ycxx; }
        public String getSbyysm() { return sbyysm; }
        public String getSbztDm() { return sbztDm; }
    }

    // 异常结果工厂
    static class ErrorResultFactory {
        static CommonResult<Object> createEmptyResponseResult() {
            return createErrorResult(
                    YclxConstants.QYDYWYC_YCLX_DM,
                    "乐企查询上传处理结果返回数据为空",
                    "乐企查询上传处理结果返回数据为空，请联系系统运维人员。",
                    SBZT_SBSB_DM
            );
        }

        static CommonResult<Object> createConversionErrorResult() {
            return createErrorResult(
                    YclxConstants.QYDYWYC_YCLX_DM,
                    "乐企查询上传处理结果返回报文转换失败",
                    "乐企查询上传处理结果返回报文转换失败，请联系系统运维人员。",
                    SBZT_SBSB_DM
            );
        }

        private static CommonResult<Object> createErrorResult(String yclxDm, String ycxx,
                                                              String sbyysm, String sbztDm) {
            SbjgcxRespDTO dto = new SbjgcxRespDTO();
            dto.setYclxDm(yclxDm);
            dto.setYcxx(ycxx);
            dto.setSbyysm(sbyysm);
            dto.setSbztDm(sbztDm);
            return CommonResult.error(ERROR.getCode(), sbyysm, dto);
        }
    }

    // 异常处理器
    static class ExceptionHandler {
        static CommonResult<Object> handle(Exception e) {
            log.error("上传算税过程返回报文解析失败，失败原因：", e);

            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer));

            SbjgcxRespDTO dto = new SbjgcxRespDTO();
            dto.setYclxDm(YclxConstants.QYDXTYC_YCLX_DM);
            dto.setYcxx(writer.toString());
            dto.setSbyysm(e.getMessage());
            dto.setSbztDm(SBZT_SBSB_DM);

            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage(), dto);
        }
    }

    enum ResponseStatus { SUCCESS, PROCESSING, ERROR }



}
