package com.css.znsb.nssb.service.cjrjybzj;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.nssb.pojo.domain.cchxwssb.ZnsbNssbCxsshgjsyxxjlbDO;
import com.css.znsb.nssb.pojo.domain.cjrjybzj.ZnsbCjrjybzjDO;
import com.css.znsb.nssb.pojo.vo.common.SbDyVO;
import com.css.znsb.nssb.pojo.vo.fssrsb.newTysb.TysbSbxxGridVO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface CjrjybzjService {
    ZnsbNssbCxsshgjsyxxjlbDO saveRzb(Map reqMap);

    CommonResult<Object> getSbResult(Map reqMap);

    CommonResult<Object> invokeSjjh(String ywbm, String sjjhlxDm, String jsonStr, Map reqVO);

    CommonResult<Object> sbZf(Map<String, Object> reqVO);

    List<ZnsbCjrjybzjDO> queryLocalCbj(ZnsbCjrjybzjDO cjrjybzjDO);

    boolean saveOrUpdateCjrjybzj(ZnsbCjrjybzjDO cjrjybzjDO);

    List<SbDyVO> getCbjDyxx(String nsrsbh, LocalDate skssqq, LocalDate skssqz);
}
