package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《海关完税凭证抵扣联数据采集表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_hgwspzdklsjcjbywbw", propOrder = { "hgwspzdklsjcjb" })
@Getter
@Setter
public class ZzssyyybnsrHgwspzdklsjcjbywbw extends TaxDoc {
    /**
     * 海关完税凭证抵扣联数据采集表
     */
    @XmlElement(nillable = true, required = true)
    protected Hgwspzdklsjcjb hgwspzdklsjcjb;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class Hgwspzdklsjcjb extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.Hgwspzdklsjcjb {}
}