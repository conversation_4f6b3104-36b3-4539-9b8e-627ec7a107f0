package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《农产品核定扣除增值税进项税额计算表（汇总表）》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_ncphdkczzsjxsejsb", propOrder = { "sbbheadVO", "ncphdkczzsjxsejsbGrid" })
@Getter
@Setter
public class ZzsybnsrsbNcphdkczzsjxsejsb {
    /**
     * 申报表信息
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 《农产品核定扣除增值税进项税额计算表（汇总表）》
     */
    @XmlElement(nillable = true, required = true)
    protected NcphdkczzsjxsejsbGrid ncphdkczzsjxsejsbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "ncphdkczzsjxsejsbGridlbVO" })
    @Getter
    @Setter
    public static class NcphdkczzsjxsejsbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<NcphdkczzsjxsejsbGridlbVO> ncphdkczzsjxsejsbGridlbVO;

        /**
         * Gets the value of the ncphdkczzsjxsejsbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the ncphdkczzsjxsejsbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getNcphdkczzsjxsejsbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link NcphdkczzsjxsejsbGridlbVO}
         */
        public List<NcphdkczzsjxsejsbGridlbVO> getNcphdkczzsjxsejsbGridlbVO() {
            if (ncphdkczzsjxsejsbGridlbVO == null) {
                ncphdkczzsjxsejsbGridlbVO = new ArrayList<NcphdkczzsjxsejsbGridlbVO>();
            }
            return this.ncphdkczzsjxsejsbGridlbVO;
        }
    }
}