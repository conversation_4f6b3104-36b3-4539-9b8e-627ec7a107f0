package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《农产品核定扣除增值税进项税额计算表（汇总表）》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_ncphdkczzsjxsejsbbw", propOrder = { "zzsybnsrsbNcphdkczzsjxsejsb" })
@Getter
@Setter
public class ZzsybnsrsbNcphdkczzsjxsejsbbw extends TaxDoc {
    /**
     * 《农产品核定扣除增值税进项税额计算表（汇总表）》
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_ncphdkczzsjxsejsb", required = true)
    @JSONField(name = "zzsybnsrsb_ncphdkczzsjxsejsb")
    protected ZzsybnsrsbNcphdkczzsjxsejsb zzsybnsrsbNcphdkczzsjxsejsb;
}