
package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 企业所得税汇总纳税分支机构分配表分支机构情况信息
 * 
 * <p>Java class for SBQysdsfzjgxxVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBQysdsfzjgxxVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="fzjgfpbfzjguuid" type="{http://www.chinatax.gov.cn/dataspec/}fzjgfpbfzjguuid"/>
 *         &lt;element name="pzxh" type="{http://www.chinatax.gov.cn/dataspec/}pzxh"/>
 *         &lt;element name="zjgnsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}zjgnsrsbh"/>
 *         &lt;element name="zjgdjxh" type="{http://www.chinatax.gov.cn/dataspec/}zjgdjxh"/>
 *         &lt;element name="fzjgnsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="fzjgdjxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="nsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *         &lt;element name="fzjgmc" type="{http://www.chinatax.gov.cn/dataspec/}fzjgmc"/>
 *         &lt;element name="fzjglxlb" type="{http://www.chinatax.gov.cn/dataspec/}fzjglxlb"/>
 *         &lt;element name="fzjgsrze" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="fzjggzze" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="fzjgzcze" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="fpbl" type="{http://www.chinatax.gov.cn/dataspec/}fpbl"/>
 *         &lt;element name="fpse" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="sfxsdfjm" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="xsdfjmje" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="xsdfjmfd" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="nsrztDm" type="{http://www.chinatax.gov.cn/dataspec/}nsrztDm"/>
 *         &lt;element name="fzjgxsqyxyhqk" type="{http://www.chinatax.gov.cn/dataspec/}fzjgxsqyxyhqk"/>
 *         &lt;element name="fzjgqnljfpje" type="{http://www.chinatax.gov.cn/dataspec/}fzjgqnljfpje"/>
 *         &lt;element name="xsmzdfyhfd" type="{http://www.chinatax.gov.cn/dataspec/}xsmzdfyhfd"/>
 *         &lt;element name="fzjgqnljyxsmzdfyhje" type="{http://www.chinatax.gov.cn/dataspec/}fzjgqnljyxsmzdfyhje"/>
 *         &lt;element name="fzjgqnljyxsmzdfyhje1" type="{http://www.chinatax.gov.cn/dataspec/}fzjgqnljyxsmzdfyhje1"/>
 *         &lt;element name="ymzdfyhtzfpje" type="{http://www.chinatax.gov.cn/dataspec/}ymzdfyhtzfpje"/>
 *         &lt;element name="sjfpse" type="{http://www.chinatax.gov.cn/dataspec/}sjfpse"/>
 *         &lt;element name="fzjgdjxh2" type="{http://www.chinatax.gov.cn/dataspec/}djxh"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBQysdsfzjgxxVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "fzjgfpbfzjguuid",
    "pzxh",
    "zjgnsrsbh",
    "zjgdjxh",
    "fzjgnsrsbh",
    "fzjgdjxh",
    "nsrsbh",
    "djxh",
    "fzjgmc",
    "fzjglxlb",
    "fzjgsrze",
    "fzjggzze",
    "fzjgzcze",
    "fpbl",
    "fpse",
    "sfxsdfjm",
    "xsdfjmje",
    "xsdfjmfd",
    "zgswjgmc",
    "zgswjgDm",
    "kssjyhqbz",
    "nsrztDm",
    "fzjgxsqyxyhqk",
    "fzjgqnljfpje",
    "xsmzdfyhfd",
    "fzjgqnljyxsmzdfyhje",
    "fzjgqnljyxsmzdfyhje1",
    "ymzdfyhtzfpje",
    "sjfpse",
    "fzjgdjxh2"

})
public class SBQysdsfzjgxxVO
    implements Serializable
{

    private final static long serialVersionUID = 5102454195654018577L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fzjgfpbfzjguuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String pzxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zjgnsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zjgdjxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fzjgnsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fzjgdjxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String nsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fzjgmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fzjglxlb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fzjgsrze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fzjggzze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fzjgzcze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fpbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fpse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sfxsdfjm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double xsdfjmje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double xsdfjmfd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zgswjgmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zgswjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kssjyhqbz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nsrztDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String fzjgxsqyxyhqk;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fzjgqnljfpje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double xsmzdfyhfd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fzjgqnljyxsmzdfyhje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double fzjgqnljyxsmzdfyhje1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double ymzdfyhtzfpje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double sjfpse;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fzjgdjxh2;

    protected String zgswjDm;

    protected double fzjgfpse;

    protected double srze;

    protected double zcze;

    protected double gzze;

    public double getFzjgfpse() { return fzjgfpse; }
    public double getSrze() { return srze; }
    public double getZcze() { return zcze; }
    public double getGzze() { return gzze; }

    public void setFzjgfpse(double fzjgfpse) {
        this.fzjgfpse = fzjgfpse;
    }
    public void setSrze(double srze) {
        this.srze = srze;
    }
    public void setZcze(double zcze) {
        this.zcze = zcze;
    }
    public void setGzze(double gzze) {
        this.gzze = gzze;
    }

    public String getZgswjDm() {
        return zgswjDm;
    }

    public void setZgswjDm(String zgswjDm) {
        this.zgswjDm = zgswjDm;
    }

    public String getZgswjgDm() {
        return zgswjgDm;
    }

    public void setZgswjgDm(String zgswjgDm) {
        this.zgswjgDm = zgswjgDm;
    }

    public String getZgswjgmc() {
        return zgswjgmc;
    }

    public void setZgswjgmc(String zgswjgmc) {
        this.zgswjgmc = zgswjgmc;
    }

    /**
     * Gets the value of the fzjgfpbfzjguuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFzjgfpbfzjguuid() {
        return fzjgfpbfzjguuid;
    }

    /**
     * Sets the value of the fzjgfpbfzjguuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFzjgfpbfzjguuid(String value) {
        this.fzjgfpbfzjguuid = value;
    }

    /**
     * Gets the value of the pzxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPzxh() {
        return pzxh;
    }

    /**
     * Sets the value of the pzxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPzxh(String value) {
        this.pzxh = value;
    }

    /**
     * Gets the value of the zjgnsrsbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZjgnsrsbh() {
        return zjgnsrsbh;
    }

    /**
     * Sets the value of the zjgnsrsbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZjgnsrsbh(String value) {
        this.zjgnsrsbh = value;
    }

    /**
     * Gets the value of the zjgdjxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZjgdjxh() {
        return zjgdjxh;
    }

    /**
     * Sets the value of the zjgdjxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZjgdjxh(String value) {
        this.zjgdjxh = value;
    }

    /**
     * Gets the value of the fzjgnsrsbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFzjgnsrsbh() {
        return fzjgnsrsbh;
    }

    /**
     * Sets the value of the fzjgnsrsbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFzjgnsrsbh(String value) {
        this.fzjgnsrsbh = value;
    }

    /**
     * Gets the value of the fzjgdjxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFzjgdjxh() {
        return fzjgdjxh;
    }

    /**
     * Sets the value of the fzjgdjxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFzjgdjxh(String value) {
        this.fzjgdjxh = value;
    }

    /**
     * Gets the value of the nsrsbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrsbh() {
        return nsrsbh;
    }

    /**
     * Sets the value of the nsrsbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrsbh(String value) {
        this.nsrsbh = value;
    }

    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the fzjgmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFzjgmc() {
        return fzjgmc;
    }

    /**
     * Sets the value of the fzjgmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFzjgmc(String value) {
        this.fzjgmc = value;
    }

    /**
     * Gets the value of the fzjglxlb property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFzjglxlb() {
        return fzjglxlb;
    }

    /**
     * Sets the value of the fzjglxlb property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFzjglxlb(String value) {
        this.fzjglxlb = value;
    }

    /**
     * Gets the value of the fzjgsrze property.
     * 
     */
    public double getFzjgsrze() {
        return fzjgsrze;
    }

    /**
     * Sets the value of the fzjgsrze property.
     * 
     */
    public void setFzjgsrze(double value) {
        this.fzjgsrze = value;
    }

    /**
     * Gets the value of the fzjggzze property.
     * 
     */
    public double getFzjggzze() {
        return fzjggzze;
    }

    /**
     * Sets the value of the fzjggzze property.
     * 
     */
    public void setFzjggzze(double value) {
        this.fzjggzze = value;
    }

    /**
     * Gets the value of the fzjgzcze property.
     * 
     */
    public double getFzjgzcze() {
        return fzjgzcze;
    }

    /**
     * Sets the value of the fzjgzcze property.
     * 
     */
    public void setFzjgzcze(double value) {
        this.fzjgzcze = value;
    }

    /**
     * Gets the value of the fpbl property.
     * 
     */
    public double getFpbl() {
        return fpbl;
    }

    /**
     * Sets the value of the fpbl property.
     * 
     */
    public void setFpbl(double value) {
        this.fpbl = value;
    }

    /**
     * Gets the value of the fpse property.
     * 
     */
    public double getFpse() {
        return fpse;
    }

    /**
     * Sets the value of the fpse property.
     * 
     */
    public void setFpse(double value) {
        this.fpse = value;
    }

    /**
     * Gets the value of the sfxsdfjm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfxsdfjm() {
        return sfxsdfjm;
    }

    /**
     * Sets the value of the sfxsdfjm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfxsdfjm(String value) {
        this.sfxsdfjm = value;
    }

    /**
     * Gets the value of the xsdfjmje property.
     * 
     */
    public double getXsdfjmje() {
        return xsdfjmje;
    }

    /**
     * Sets the value of the xsdfjmje property.
     * 
     */
    public void setXsdfjmje(double value) {
        this.xsdfjmje = value;
    }

    /**
     * Gets the value of the xsdfjmfd property.
     * 
     */
    public double getXsdfjmfd() {
        return xsdfjmfd;
    }

    /**
     * Sets the value of the xsdfjmfd property.
     * 
     */
    public void setXsdfjmfd(double value) {
        this.xsdfjmfd = value;
    }

    /**
     *创建时间:2021年6月4日上午10:58:13
     *get方法
     * @return the kssjyhqbz
     */
    public String getKssjyhqbz() {
        return kssjyhqbz;
    }

    /**
     * 创建时间:2021年6月4日上午10:58:13
     * set方法
     * @param kssjyhqbz the kssjyhqbz to set
     */
    public void setKssjyhqbz(String kssjyhqbz) {
        this.kssjyhqbz = kssjyhqbz;
    }

    /**
     *创建时间:2021年6月4日上午10:58:13
     *get方法
     * @return the nsrztDm
     */
    public String getNsrztDm() {
        return nsrztDm;
    }

    /**
     * 创建时间:2021年6月4日上午10:58:13
     * set方法
     * @param value the nsrztDm to set
     */
    public void setNsrztDm(String value) {
        this.nsrztDm = value;
    }

    /**
     *创建时间:2021年6月4日上午10:58:13
     *get方法
     * @return the fzjgxsqyxyhqk
     */
    public String getFzjgxsqyxyhqk() {
        return fzjgxsqyxyhqk;
    }

    /**
     * 创建时间:2021年6月4日上午10:58:13
     * set方法
     * @param value the fzjgxsqyxyhqk to set
     */
    public void setFzjgxsqyxyhqk(String value) {
        this.fzjgxsqyxyhqk = value;
    }

    /**
     * Gets the value of the fzjgqnljfpje property.
     *
     */
    public double getFzjgqnljfpje() {
        return fzjgqnljfpje;
    }

    /**
     * Sets the value of the fzjgqnljfpje property.
     *
     */
    public void setFzjgqnljfpje(double value) {
        this.fzjgqnljfpje = value;
    }

    /**
     * Gets the value of the xsmzdfyhfd property.
     *
     */
    public double getXsmzdfyhfd() {
        return xsmzdfyhfd;
    }

    /**
     * Sets the value of the xsmzdfyhfd property.
     *
     */
    public void setXsmzdfyhfd(double value) {
        this.xsmzdfyhfd = value;
    }

    /**
     * Gets the value of the fzjgqnljyxsmzdfyhje property.
     *
     */
    public double getFzjgqnljyxsmzdfyhje() {
        return fzjgqnljyxsmzdfyhje;
    }

    /**
     * Sets the value of the fzjgqnljyxsmzdfyhje property.
     *
     */
    public void setFzjgqnljyxsmzdfyhje(double value) {
        this.fzjgqnljyxsmzdfyhje = value;
    }

    /**
     * Gets the value of the fzjgqnljyxsmzdfyhje1 property.
     *
     */
    public double getFzjgqnljyxsmzdfyhje1() {
        return fzjgqnljyxsmzdfyhje1;
    }

    /**
     * Sets the value of the fzjgqnljyxsmzdfyhje1 property.
     *
     */
    public void setFzjgqnljyxsmzdfyhje1(double value) {
        this.fzjgqnljyxsmzdfyhje1 = value;
    }

    /**
     * Gets the value of the ymzdfyhtzfpje property.
     *
     */
    public double getYmzdfyhtzfpje() {
        return ymzdfyhtzfpje;
    }

    /**
     * Sets the value of the ymzdfyhtzfpje property.
     *
     */
    public void setYmzdfyhtzfpje(double value) {
        this.ymzdfyhtzfpje = value;
    }

    /**
     * Gets the value of the sjfpse property.
     *
     */
    public double getSjfpse() { return sjfpse; }

    /**
     * Sets the value of the sjfpse property.
     *
     */
    public void setSjfpse(double value) {
        this.sjfpse = value;
    }

    /**
     * Gets the value of the fzjgdjxh2 property.
     *
     */
    public String getFzjgdjxh2() {
        return fzjgdjxh2;
    }

    /**
     * Sets the value of the fzjgdjxh2 property.
     *
     */
    public void setFzjgdjxh2(String fzjgdjxh2) {
        this.fzjgdjxh2 = fzjgdjxh2;
    }

}
