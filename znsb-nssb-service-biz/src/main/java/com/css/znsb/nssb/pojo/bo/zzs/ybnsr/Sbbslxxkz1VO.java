package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 申报表受理信息VO扩展1，增加代理人相关信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Sbbslxxkz1VO", propOrder = { "swdlrmc", "swdlrlxdh", "swdlrjbr", "cwfzr", "fddbr", "bsr", "blrysfzjlxDm", "blrysfzjhm", "shyj" })
@Getter
@Setter
public class Sbbslxxkz1VO extends SbbslxxVO {
    /**
     * 税务代理人名称
     */
    protected String swdlrmc;

    /**
     * 税务代理人联系电话
     */
    protected String swdlrlxdh;

    /**
     * 税务代理人经办人
     */
    protected String swdlrjbr;

    /**
     * 财务负责人
     */
    protected String cwfzr;

    /**
     * 法定代表人
     */
    protected String fddbr;

    /**
     * 办税人
     */
    protected String bsr;

    /**
     * 办理人员身份证件类型
     */
    @XmlElement(nillable = true, required = true)
    protected String blrysfzjlxDm;

    /**
     * 办理人员身份证件号码
     */
    @XmlElement(nillable = true, required = true)
    protected String blrysfzjhm;

    /**
     * 审核意见
     */
    protected String shyj;
}