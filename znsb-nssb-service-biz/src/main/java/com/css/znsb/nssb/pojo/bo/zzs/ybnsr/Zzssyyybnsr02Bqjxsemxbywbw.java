package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税纳税申报表附表二（本期进项税额明细表）》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr02_bqjxsemxbywbw", propOrder = { "zzssyyybnsr02Bqjxsemxb" })
@Getter
@Setter
public class Zzssyyybnsr02Bqjxsemxbywbw extends TaxDoc {
    /**
     * 《增值税纳税申报表附表二（本期进项税额明细表）》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr02_bqjxsemxb", required = true)
    @JSONField(name = "zzssyyybnsr02_bqjxsemxb")
    protected Zzssyyybnsr02Bqjxsemxb zzssyyybnsr02Bqjxsemxb;
}