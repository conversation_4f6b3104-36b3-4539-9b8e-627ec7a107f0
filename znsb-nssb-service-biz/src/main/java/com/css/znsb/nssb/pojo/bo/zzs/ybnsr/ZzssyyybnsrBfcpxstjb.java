package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《部分产品销售统计表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_bfcpxstjb", propOrder = { "bfcpxstjbForm" })
@Getter
@Setter
public class ZzssyyybnsrBfcpxstjb {
    /**
     * 部分产品销售统计表
     */
    @XmlElement(nillable = true, required = true)
    protected BfcpxstjbFormVO bfcpxstjbForm;
}