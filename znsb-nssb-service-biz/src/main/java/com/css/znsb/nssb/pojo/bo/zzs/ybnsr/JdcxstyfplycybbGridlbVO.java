package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 机动车销售统一发票领用存月报表
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "jdcxstyfplycybbGridlbVO", propOrder = { "xm", "qcccFs", "qcccQshm", "qcccZzhm", "bqlgFs", "bqlgQshm", "bqlgZzhm", "bqkjFs", "bqkjQshm", "bqkjZzhm", "bqjhwkjslFs", "bqjhwkjslQshm", "bqjhwkjslZzhm", "bqzfhysFs", "bqzfhysQshm", "bqzfhysZzhm", "qmkcFs", "qmkcQshm", "qmkcZzhm" })
@Getter
@Setter
public class JdcxstyfplycybbGridlbVO {
    /**
     * 机动车销售统一发票
     */
    @XmlElement(nillable = true, required = true)
    protected String xm;

    /**
     * 期初库存份数
     */
    @XmlElement(name = "qccc_fs")
    @JSONField(name = "qccc_fs")
    protected BigDecimal qcccFs;

    /**
     * 期初库存起始号码
     */
    @XmlElement(nillable = true, name = "qccc_qshm", required = true)
    @JSONField(name = "qccc_qshm")
    protected String qcccQshm;

    /**
     * 期初库存终止号码
     */
    @XmlElement(nillable = true, name = "qccc_zzhm", required = true)
    @JSONField(name = "qccc_zzhm")
    protected String qcccZzhm;

    /**
     * 本期领购份数
     */
    @XmlElement(name = "bqlg_fs")
    @JSONField(name = "bqlg_fs")
    protected BigDecimal bqlgFs;

    /**
     * 本期领购发票起始号码
     */
    @XmlElement(nillable = true, name = "bqlg_qshm", required = true)
    @JSONField(name = "bqlg_qshm")
    protected String bqlgQshm;

    /**
     * 本期领购发票终止号码
     */
    @XmlElement(nillable = true, name = "bqlg_zzhm", required = true)
    @JSONField(name = "bqlg_zzhm")
    protected String bqlgZzhm;

    /**
     * 本期开具份数
     */
    @XmlElement(name = "bqkj_fs")
    @JSONField(name = "bqkj_fs")
    protected BigDecimal bqkjFs;

    /**
     * 本期开具发票起始号码
     */
    @XmlElement(nillable = true, name = "bqkj_qshm", required = true)
    @JSONField(name = "bqkj_qshm")
    protected String bqkjQshm;

    /**
     * 本期开具发票终止号码
     */
    @XmlElement(nillable = true, name = "bqkj_zzhm", required = true)
    @JSONField(name = "bqkj_zzhm")
    protected String bqkjZzhm;

    /**
     * 本期交回未开具数量份数
     */
    @XmlElement(name = "bqjhwkjsl_fs")
    @JSONField(name = "bqjhwkjsl_fs")
    protected BigDecimal bqjhwkjslFs;

    /**
     * 本期交回未开具发票起始号码
     */
    @XmlElement(nillable = true, name = "bqjhwkjsl_qshm", required = true)
    @JSONField(name = "bqjhwkjsl_qshm")
    protected String bqjhwkjslQshm;

    /**
     * 本期交回未开具发票终止号码
     */
    @XmlElement(nillable = true, name = "bqjhwkjsl_zzhm", required = true)
    @JSONField(name = "bqjhwkjsl_zzhm")
    protected String bqjhwkjslZzhm;

    /**
     * 本期作废或遗失份数
     */
    @XmlElement(name = "bqzfhys_fs")
    @JSONField(name = "bqzfhys_fs")
    protected BigDecimal bqzfhysFs;

    /**
     * 本期作废或遗失发票起始号码
     */
    @XmlElement(nillable = true, name = "bqzfhys_qshm", required = true)
    @JSONField(name = "bqzfhys_qshm")
    protected String bqzfhysQshm;

    /**
     * 本期作废或遗失发票终止号码
     */
    @XmlElement(nillable = true, name = "bqzfhys_zzhm", required = true)
    @JSONField(name = "bqzfhys_zzhm")
    protected String bqzfhysZzhm;

    /**
     * 期末库存份数
     */
    @XmlElement(name = "qmkc_fs")
    @JSONField(name = "qmkc_fs")
    protected BigDecimal qmkcFs;

    /**
     * 期末库存起始号码
     */
    @XmlElement(nillable = true, name = "qmkc_qshm", required = true)
    @JSONField(name = "qmkc_qshm")
    protected String qmkcQshm;

    /**
     * 期末库存终止号码
     */
    @XmlElement(nillable = true, name = "qmkc_zzhm", required = true)
    @JSONField(name = "qmkc_zzhm")
    protected String qmkcZzhm;
}