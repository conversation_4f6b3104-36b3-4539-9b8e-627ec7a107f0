package com.css.znsb.nssb.job;

import com.css.znsb.cxtj.api.SimpleQueryApi;
import com.css.znsb.cxtj.pojo.SimpleQueryReq;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.sjjh.pojo.dto.sjjh.SjjhDTO;
import com.css.znsb.framework.sjjh.service.sjjh.SjjhService;
import com.css.znsb.nssb.mapper.sbrw.SbSbbDOMapper;
import com.css.znsb.nssb.mapper.sbrw.SbSbxxDOMapper;
import com.css.znsb.nssb.pojo.domain.sbrw.SbSbbDO;
import com.css.znsb.nssb.pojo.domain.sbrw.SbSbxxDO;
import com.css.znsb.nssb.pojo.dto.RefreshSbrwZtReqDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbqkcxReqDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbqkcxRespDTO;
import com.css.znsb.nssb.pojo.dto.sbqkcx.SbxxDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.QuerySbxxRpaReqDTO;
import com.css.znsb.nssb.pojo.dto.sbrw.QuerySbxxRpaRespDTO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Slf4j
@Component
public class QydInitGjsbsjJob {

    @Resource
    private SimpleQueryApi queryApi;

    @Resource
    private SjjhService sjjhService;

    @Resource
    private SbSbbDOMapper sbSbbDOMapper;

    @Resource
    private SbSbxxDOMapper sbSbxxDOMapper;

    /**
     * 企业端初始化申报数据归集
     */
    @XxlJob("qydinitgjsbsjJob")
    public void execute(){
        log.info("==========开始进行企业端初始化申报数据归集任务==========");
        //查询全量企业信息进行归集
        final SimpleQueryReq simpleQueryReq = new SimpleQueryReq();
        simpleQueryReq.setPath("/sb/init/initQueryQyxx");
        final List<Map<String, Object>> resultList = queryApi.queryList(simpleQueryReq);
        if (!GyUtils.isNull(resultList)){
            for (Map<String, Object> map:resultList){
                //根据配置系统参数判断调用rpa接口还是调用乐企接口进行数据归集
                //按照当前日期月份判断需要调用几次组装请求入参时间范围
                int nowYf = LocalDate.now().getMonthValue();
                int nowYear = LocalDate.now().getYear();
                List<Map<String, Date>> dateList = new ArrayList<>();
                //默认至少查询1-3月
                Map<String, Date> mrdatemap = new HashMap<>();
                mrdatemap.put("skssqq", DateUtils.toDate(YearMonth.of(nowYear, 1).atDay(1)));
                mrdatemap.put("skssqz", DateUtils.toDate(LocalDate.of(nowYear, 3, 1).with(TemporalAdjusters.lastDayOfMonth())));
                dateList.add(mrdatemap);
                if (nowYf >= 3){
                    Map<String, Date> datemap = new HashMap<>();
                    datemap.put("skssqq", DateUtils.toDate(YearMonth.of(nowYear, 4).atDay(1)));
                    datemap.put("skssqz", DateUtils.toDate(LocalDate.of(nowYear, 6, 1).with(TemporalAdjusters.lastDayOfMonth())));
                    dateList.add(datemap);
                }
                if (nowYf >= 6){
                    Map<String, Date> datemap = new HashMap<>();
                    datemap.put("skssqq", DateUtils.toDate(YearMonth.of(nowYear, 7).atDay(1)));
                    datemap.put("skssqz", DateUtils.toDate(LocalDate.of(nowYear, 9, 1).with(TemporalAdjusters.lastDayOfMonth())));
                    dateList.add(datemap);
                }
                if (nowYf >= 9){
                    Map<String, Date> datemap = new HashMap<>();
                    datemap.put("skssqq", DateUtils.toDate(YearMonth.of(nowYear, 10).atDay(1)));
                    datemap.put("skssqz", DateUtils.toDate(LocalDate.of(nowYear, 12, 1).with(TemporalAdjusters.lastDayOfMonth())));
                    dateList.add(datemap);
                }

                //循环日期调用接口
                for (Map<String, Date> dateMap:dateList){
                    if ("Y".equals(CacheUtils.getXtcs("QYD_RPA"))){
                        //调用rpa查询申报情况数据
                        //启用RPA优先调用RPA
                        SjjhDTO sjjhDTO = new SjjhDTO();
                        sjjhDTO.setSjjhlxDm("SBRWZT0001");
                        sjjhDTO.setYwbm("SBRWZT-RPA");
                        sjjhDTO.setYwuuid(GyUtils.getUuid());
                        sjjhDTO.setDjxh(String.valueOf(map.get("djxh")));
                        sjjhDTO.setNsrsbh(String.valueOf(map.get("nsrsbh")));
                        sjjhDTO.setXzqhszDm(String.valueOf(map.get("xzqhszDm")));
                        //组装rpa报文
                        QuerySbxxRpaReqDTO querySbxxRpaReqDTO = new QuerySbxxRpaReqDTO();
                        querySbxxRpaReqDTO.setSbrqq(dateMap.get("skssqq"));
                        querySbxxRpaReqDTO.setSbrqz(dateMap.get("skssqz"));
                        querySbxxRpaReqDTO.setSkssqq(null);
                        querySbxxRpaReqDTO.setSkssqz(null);
                        querySbxxRpaReqDTO.setYzpzzlDm("");
                        sjjhDTO.setBwnr(JsonUtils.toJson(querySbxxRpaReqDTO));
                        CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
                        QuerySbxxRpaRespDTO querySbxxRpaRespDTO = JsonUtils.toBean(String.valueOf(result.getData()), QuerySbxxRpaRespDTO.class);
                        if ("0".equals(querySbxxRpaRespDTO.getCode())) {
                            //0为查询成功
                            //判断sbb是否返回数据
                            if (!GyUtils.isNull(querySbxxRpaRespDTO.getSbbList())) {
                                this.saveSbb(BeanUtils.toBean(querySbxxRpaRespDTO.getSbbList(), SbSbbDO.class),
                                        BeanUtils.toBean(querySbxxRpaRespDTO.getSbxxList(), SbSbxxDO.class),map);
                            }
                        } else {
                            log.info("初始化申报数据归集调用rpa接口出错：{}",querySbxxRpaRespDTO.getMessage());
                        }
                    } else if ("Y".equals(CacheUtils.getXtcs("QYD_LQQY"))){
                        //调用乐企查询申报情况数据
                        SjjhDTO sjjhDTO = new SjjhDTO();
                        sjjhDTO.setSjjhlxDm("SBRWZT0002");
                        sjjhDTO.setYwbm("SBRWZT-LQ");
                        sjjhDTO.setYwuuid(GyUtils.getUuid());
                        sjjhDTO.setDjxh(String.valueOf(map.get("djxh")));
                        sjjhDTO.setNsrsbh(String.valueOf(map.get("nsrsbh")));
                        sjjhDTO.setXzqhszDm(String.valueOf(map.get("xzqhszDm")));
                        //组装乐企报文
                        SbqkcxReqDTO sbqkcxReqDTO = new SbqkcxReqDTO();
                        sbqkcxReqDTO.setDjxh(String.valueOf(map.get("djxh")));
                        sbqkcxReqDTO.setSkssqq(dateMap.get("skssqq"));
                        sbqkcxReqDTO.setSkssqz(dateMap.get("skssqz"));
                        sbqkcxReqDTO.setYzpzzlDm("");
                        sbqkcxReqDTO.setZsxmDm("");
                        sjjhDTO.setBwnr(JsonUtils.toJson(sbqkcxReqDTO));
                        CommonResult<Object> result = sjjhService.saveSjjhJob(sjjhDTO);
                        SbqkcxRespDTO sbqkcxRespDTO = JsonUtils.toBean(String.valueOf(result.getData()), SbqkcxRespDTO.class);
                        if ("00".equals(sbqkcxRespDTO.getReturncode())) {
                            //00为成功
                            //判断sbb是否返回数据
                            if (!GyUtils.isNull(sbqkcxRespDTO.getSbxxList())) {
                                this.saveSbb(BeanUtils.toBean(sbqkcxRespDTO.getSbxxList(), SbSbbDO.class),
                                        BeanUtils.toBean(sbqkcxRespDTO.getSbmxxxList(), SbSbxxDO.class),map);
                            }
                        } else {
                            log.info("初始化申报数据归集调用乐企接口出错：{}",sbqkcxRespDTO.getReturnmsg());
                        }
                    }
                }

            }
        }


        log.info("==========企业端初始化申报数据归集任务结束==========");
    }

    private void saveSbb(List<SbSbbDO> sbsbbList, List<SbSbxxDO> sbsbxxList, Map<String,Object> map) {
        sbsbbList.forEach(sbbDO -> {
            //根据sbuuid先删除再插入，进行全量更新或归集
            sbSbbDOMapper.deleteBySbuuid(sbbDO.getSbuuid());
            sbSbbDOMapper.insert(sbbDO);
        });

        sbsbxxList.forEach(sbxxDO -> {
            //根据sbxxuuid先删除再插入，进行全量更新或归集
            sbSbxxDOMapper.deleteBySbxxuuid(sbxxDO.getSbxxuuid());
            sbSbxxDOMapper.insert(sbxxDO);
        });

        //归集申报明细数据
        this.saveSbmxxx(sbsbbList,map);
    }

    private void saveSbmxxx(List<SbSbbDO> sbsbbList, Map<String, Object> map) {
        //是否乐企企业
        if ("Y".equals(CacheUtils.getXtcs("QYD_LQQY"))) {
            //循环每条sbsbblist归集申报明细数据
            for (SbSbbDO sbsbbDO:sbsbbList){
                //判断是否乐企支持税种
                //归集sbb数据后归集申报明细数据，目前只支持归集乐企提供的税种，取配置发起数据交换
                Map<String, Object> lqszMap = this.getLqsz(sbsbbDO);
                //如果不是乐企支持税种不处理
                if (!GyUtils.isNull(lqszMap)) {
                    //获取对应税种数据交换配置，配置为空则不处理
                    final String sjjhlxDm = String.valueOf(lqszMap.get("sjjhlxDm"));
                    if (GyUtils.isNull(sjjhlxDm)) {
                        log.info("企业端初始化归集申报明细数据时未获取到乐企税种配置的数据交换类型代码，sbuuid为：{}",sbsbbDO.getSbuuid());
                        return;
                    }
                    final String ywbm = String.valueOf(lqszMap.get("ywbm"));
                    if (GyUtils.isNull(ywbm)) {
                        log.info("企业端初始化归集申报明细数据时未获取到乐企税种配置的业务编码，sbuuid为：{}", sbsbbDO.getSbuuid());
                        return;
                    }
                    // 发起申报明细查询
                    final SjjhDTO sjjhDTO = new SjjhDTO();
                    sjjhDTO.setDjxh(String.valueOf(map.get("djxh")));
                    sjjhDTO.setNsrsbh(String.valueOf(map.get("nsrsbh")));
                    sjjhDTO.setXzqhszDm(String.valueOf(map.get("xzqhszDm")));
                    sjjhDTO.setYwuuid(sbsbbDO.getSbuuid());
                    sjjhDTO.setYwbm(ywbm);
                    sjjhDTO.setSjjhlxDm(sjjhlxDm);
                    //由于部分接口调用rpa获取的申报情况查询，所以此处报文需要重新组装
                    final SbqkcxRespDTO sbqkDTO = new SbqkcxRespDTO();
                    final List<SbxxDTO> sbxxList = new ArrayList<>();
                    sbxxList.add(BeanUtils.toBean(sbsbbDO, SbxxDTO.class));
                    sbqkDTO.setSbxxList(sbxxList);
                    sjjhDTO.setBwnr(JsonUtils.toJson(sbqkDTO));
                    sjjhService.saveSjjhJob(sjjhDTO);
                }
            }

        }

    }

    private Map<String, Object> getLqsz(SbSbbDO sbbDO) {
        //判断是否乐企支持税种，如果不是则不处理
        List<Map<String, Object>> lqzcszList = CacheUtils.getTableData("cs_sb_lqjrszpzb");
        if (!GyUtils.isNull(lqzcszList)) {
            Map<String, Object> lqzcszMap = lqzcszList.stream().filter(map ->
                    String.valueOf(map.get("yzpzzlDm")).equals(sbbDO.getYzpzzlDm())
                            && !GyUtils.isNull(map.get("sjjhlxDm"))
            ).findAny().orElse(null);
            if (!GyUtils.isNull(lqzcszMap)) {
                return lqzcszMap;
            }
        }
        return null;
    }
}
