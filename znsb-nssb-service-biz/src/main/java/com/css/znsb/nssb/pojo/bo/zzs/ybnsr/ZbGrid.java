package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 增值税纳税申报表（一般纳税人适用）
 *
 * <p>zbGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="zbGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="zbGridlb" type="{http://www.chinatax.gov.cn/dataspec/}zbGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zbGrid", propOrder = { "zbGridlb" })
public class ZbGrid {
    @XmlElement(nillable = true, required = true)
    protected List<ZbGridlbVO> zbGridlb;

    /**
     * Gets the value of the zbGridlb property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the zbGridlb property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getZbGridlb().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ZbGridlbVO}
     */
    public List<ZbGridlbVO> getZbGridlb() {
        if (zbGridlb == null) {
            zbGridlb = new ArrayList<ZbGridlbVO>();
        }
        return this.zbGridlb;
    }
}