package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车辆生产企业销售情况统计表》
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdclscqyxsqktjb", propOrder = { "sbbheadVO", "jdclscqyxsqktjbGrid" })
@Getter
@Setter
public class ZzssyyybnsrJdclscqyxsqktjb {
    /**
     * 申报表头
     */
    @XmlElement(nillable = true, required = true)
    protected SbbheadVO sbbheadVO;

    /**
     * 机动车辆生产企业销售情况统计表
     */
    @XmlElement(nillable = true, required = true)
    protected JdclscqyxsqktjbGrid jdclscqyxsqktjbGrid;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "jdclscqyxsqktjbGridlbVO" })
    @Getter
    @Setter
    public static class JdclscqyxsqktjbGrid {
        @XmlElement(nillable = true, required = true)
        protected List<JdclscqyxsqktjbGridlbVO> jdclscqyxsqktjbGridlbVO;

        /**
         * Gets the value of the jdclscqyxsqktjbGridlbVO property.
         *
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jdclscqyxsqktjbGridlbVO property.
         *
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         * getJdclscqyxsqktjbGridlbVO().add(newItem);
         * </pre>
         *
         *
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link JdclscqyxsqktjbGridlbVO}
         */
        public List<JdclscqyxsqktjbGridlbVO> getJdclscqyxsqktjbGridlbVO() {
            if (jdclscqyxsqktjbGridlbVO == null) {
                jdclscqyxsqktjbGridlbVO = new ArrayList<JdclscqyxsqktjbGridlbVO>();
            }
            return this.jdclscqyxsqktjbGridlbVO;
        }
    }
}