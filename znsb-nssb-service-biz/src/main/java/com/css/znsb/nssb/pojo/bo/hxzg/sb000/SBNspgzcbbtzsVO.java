package com.css.znsb.nssb.pojo.bo.hxzg.sb000;

import com.css.znsb.nssb.pojo.bo.hxzg.gy.TaxBaseVO;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 *  金税三期工程核心征管及应用总集成项目 
 * gov.gt3.vo.sbzs.sb.sb000
 * File: SBNspgzcbbtzsVO.java 创建时间:2014-6-29上午5:05:59
 * Title: SBNspgzcbbtzsVO
 * Description: 描述（简要描述类的职责、实现方式、使用注意事项等）
 * Copyright: Copyright (c) 2014 中国软件与技术服务股份有限公司
 * Company: 中国软件与技术服务股份有限公司
 * 模块: 申报
 * <AUTHOR>
 * @reviewer 审核人名字 
 * @version 1.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBNspgzcbbtzsVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "nsrsbh",
    "nsrmc",
    "zg",
    "bzqx",
    "zxbztzsuuid"
})
public class SBNspgzcbbtzsVO extends TaxBaseVO {

    /**
     * @description 序列化标示字段
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = -6319367439099534310L;
    
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String nsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String nsrmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true, nillable = true)
    protected String zg;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String bzqx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zxbztzsuuid;

    /**
     * Gets the value of the nsrsbh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrsbh() {
        return nsrsbh;
    }

    /**
     * Sets the value of the nsrsbh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrsbh(String value) {
        this.nsrsbh = value;
    }

    /**
     * Gets the value of the nsrmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrmc() {
        return nsrmc;
    }

    /**
     * Sets the value of the nsrmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrmc(String value) {
        this.nsrmc = value;
    }

    /**
     * Gets the value of the zg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZg() {
        return zg;
    }

    /**
     * Sets the value of the zg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZg(String value) {
        this.zg = value;
    }

    /**
     * Gets the value of the bzqx property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBzqx() {
        return bzqx;
    }

    /**
     * Sets the value of the bzqx property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBzqx(String value) {
        this.bzqx = value;
    }

    /**
     * Gets the value of the zxbztzsuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZxbztzsuuid() {
        return zxbztzsuuid;
    }

    /**
     * Sets the value of the zxbztzsuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZxbztzsuuid(String value) {
        this.zxbztzsuuid = value;
    }

}
