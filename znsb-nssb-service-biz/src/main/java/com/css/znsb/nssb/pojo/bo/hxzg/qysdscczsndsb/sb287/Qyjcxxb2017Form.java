
package com.css.znsb.nssb.pojo.bo.hxzg.qysdscczsndsb.sb287;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 *  *********《企业基础信息表（A000000）》申报信息
 * 
 * <p>qyjcxxb2017Form complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="qyjcxxb2017Form">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nsrsbh" type="{http://www.chinatax.gov.cn/dataspec/}nsrsbh"/>
 *         &lt;element name="nsrmc" type="{http://www.chinatax.gov.cn/dataspec/}nsrmc"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz"/>
 *         &lt;element name="fddbr" type="{http://www.chinatax.gov.cn/dataspec/}fddbr"/>
 *         &lt;element name="kjzgmc" type="{http://www.chinatax.gov.cn/dataspec/}kjzgmc" minOccurs="0"/>
 *         &lt;element name="sbrq1" type="{http://www.chinatax.gov.cn/dataspec/}sbrq1" minOccurs="0"/>
 *         &lt;element name="tbrq" type="{http://www.chinatax.gov.cn/dataspec/}tbrq" minOccurs="0"/>
 *         &lt;element name="sbsxDm1" type="{http://www.chinatax.gov.cn/dataspec/}sbsxDm1" minOccurs="0"/>
 *         &lt;element name="hznsqy" type="{http://www.chinatax.gov.cn/dataspec/}hznsqy"/>
 *         &lt;element name="sshyDm" type="{http://www.chinatax.gov.cn/dataspec/}sshyDm" minOccurs="0"/>
 *         &lt;element name="zcze" type="{http://www.chinatax.gov.cn/dataspec/}zcze"/>
 *         &lt;element name="cyrs" type="{http://www.chinatax.gov.cn/dataspec/}cyrs"/>
 *         &lt;element name="csgjfxzhjzhy" type="{http://www.chinatax.gov.cn/dataspec/}csgjfxzhjzhy"/>
 *         &lt;element name="sffylzz" type="{http://www.chinatax.gov.cn/dataspec/}sffylzz"/>
 *         &lt;element name="czjwgljy" type="{http://www.chinatax.gov.cn/dataspec/}czjwgljy"/>
 *         &lt;element name="ssgs" type="{http://www.chinatax.gov.cn/dataspec/}ssgs"/>
 *         &lt;element name="zmcsgqtzyw" type="{http://www.chinatax.gov.cn/dataspec/}zmcsgqtzyw"/>
 *         &lt;element name="qykjzz" type="{http://www.chinatax.gov.cn/dataspec/}qykjzz" minOccurs="0"/>
 *         &lt;element name="xqykjzz" type="{http://www.chinatax.gov.cn/dataspec/}xqykjzz" minOccurs="0"/>
 *         &lt;element name="qykjzd" type="{http://www.chinatax.gov.cn/dataspec/}qykjzd" minOccurs="0"/>
 *         &lt;element name="sydwkjzz" type="{http://www.chinatax.gov.cn/dataspec/}sydwkjzz" minOccurs="0"/>
 *         &lt;element name="mjfylzzkjzd" type="{http://www.chinatax.gov.cn/dataspec/}mjfylzzkjzd" minOccurs="0"/>
 *         &lt;element name="cjtjjzzkjzd" type="{http://www.chinatax.gov.cn/dataspec/}cjtjjzzkjzd" minOccurs="0"/>
 *         &lt;element name="nmzyhzscwkjzd" type="{http://www.chinatax.gov.cn/dataspec/}nmzyhzscwkjzd" minOccurs="0"/>
 *         &lt;element name="kjzdqt" type="{http://www.chinatax.gov.cn/dataspec/}kjzdqt" minOccurs="0"/>
 *         &lt;element name="fszcgqhztsxswclsx" type="{http://www.chinatax.gov.cn/dataspec/}fszcgqhztsxswclsx"/>
 *         &lt;element name="fsfhbxzctzdynssx" type="{http://www.chinatax.gov.cn/dataspec/}fsfhbxzctzdynssx"/>
 *         &lt;element name="fsjsrgdynssx" type="{http://www.chinatax.gov.cn/dataspec/}fsjsrgdynssx"/>
 *         &lt;element name="fsqyczsx" type="{http://www.chinatax.gov.cn/dataspec/}fsqyczsx"/>
 *         &lt;element name="czkssj" type="{http://www.chinatax.gov.cn/dataspec/}skssqq"/>
 *         &lt;element name="czwcsj" type="{http://www.chinatax.gov.cn/dataspec/}skssqq"/>
 *         &lt;element name="flxsgb" type="{http://www.chinatax.gov.cn/dataspec/}flxsgb"/>
 *         &lt;element name="zwcz_zwr" type="{http://www.chinatax.gov.cn/dataspec/}zwcz"/>
 *         &lt;element name="zwcz_zqr" type="{http://www.chinatax.gov.cn/dataspec/}zwcz"/>
 *         &lt;element name="zwcz" type="{http://www.chinatax.gov.cn/dataspec/}bz1"/>
 *         &lt;element name="gqsg_bsgqy" type="{http://www.chinatax.gov.cn/dataspec/}gqsg"/>
 *         &lt;element name="gqsg_zrf" type="{http://www.chinatax.gov.cn/dataspec/}gqsg"/>
 *         &lt;element name="gqsg_sgf" type="{http://www.chinatax.gov.cn/dataspec/}gqsg"/>
 *         &lt;element name="gqsg" type="{http://www.chinatax.gov.cn/dataspec/}bz1"/>
 *         &lt;element name="zcsg_zrf" type="{http://www.chinatax.gov.cn/dataspec/}zcsg"/>
 *         &lt;element name="zcsg_sgf" type="{http://www.chinatax.gov.cn/dataspec/}zcsg"/>
 *         &lt;element name="zcsg" type="{http://www.chinatax.gov.cn/dataspec/}bz1"/>
 *         &lt;element name="hb_bhbqygd" type="{http://www.chinatax.gov.cn/dataspec/}hb"/>
 *         &lt;element name="hb_bhbqy" type="{http://www.chinatax.gov.cn/dataspec/}hb"/>
 *         &lt;element name="hb_hbqy" type="{http://www.chinatax.gov.cn/dataspec/}hb"/>
 *         &lt;element name="hb" type="{http://www.chinatax.gov.cn/dataspec/}bz1"/>
 *         &lt;element name="fenl_bflqygd" type="{http://www.chinatax.gov.cn/dataspec/}fenl"/>
 *         &lt;element name="fenl_bflqy" type="{http://www.chinatax.gov.cn/dataspec/}fenl"/>
 *         &lt;element name="fenl_flqy" type="{http://www.chinatax.gov.cn/dataspec/}fenl"/>
 *         &lt;element name="fenl" type="{http://www.chinatax.gov.cn/dataspec/}bz1"/>
 *         &lt;element name="qysdsndsbtbb" type="{http://www.chinatax.gov.cn/dataspec/}qysdsndsbtbb"/>
 *         &lt;element name="dljbr" type="{http://www.chinatax.gov.cn/dataspec/}dljbr"/>
 *         &lt;element name="dljbrzyzjhm" type="{http://www.chinatax.gov.cn/dataspec/}dljbrzyzjhm"/>
 *         &lt;element name="dlsbrq" type="{http://www.chinatax.gov.cn/dataspec/}dlsbrq"/>
 *         &lt;element name="slrxm" type="{http://www.chinatax.gov.cn/dataspec/}slrxm"/>
 *         &lt;element name="slswjgDm" type="{http://www.chinatax.gov.cn/dataspec/}slswjgDm"/>
 *         &lt;element name="slrq" type="{http://www.chinatax.gov.cn/dataspec/}slrq"/>
 *         &lt;element name="tbbdqk" type="{http://www.chinatax.gov.cn/dataspec/}tbbdqk"/>
 *         &lt;element name="qygdhjqyxtzsyje" type="{http://www.chinatax.gov.cn/dataspec/}je"/>
 *         &lt;element name="qygdhjtzbl" type="{http://www.chinatax.gov.cn/dataspec/}tzbl"/>
 *         &lt;element name="jdyjbl" type="{http://www.chinatax.gov.cn/dataspec/}jdyjbl"/>
 *         &lt;element name="zxbztzsuuid" type="{http://www.chinatax.gov.cn/dataspec/}zxbztzsuuid" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "qyjcxxb2017Form", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "nsrsbh",
    "nsrmc",
    "skssqq",
    "skssqz",
    "fddbr",
    "kjzgmc",
    "sbrq1",
    "tbrq",
    "sbsxDm1",
    "hznsqy",
    "sshyDm",
    "zcze",
    "cyrs",
    "csgjfxzhjzhy",
    "sffylzz",
    "czjwgljy",
    "ssgs",
    "zmcsgqtzyw",
    "qykjzz",
    "xqykjzz",
    "qykjzd",
    "sydwkjzz",
    "mjfylzzkjzd",
    "cjtjjzzkjzd",
    "nmzyhzscwkjzd",
    "kjzdqt",
    "fszcgqhztsxswclsx",
    "fsfhbxzctzdynssx",
    "fsjsrgdynssx",
    "fsqyczsx",
    "czkssj",
    "czwcsj",
    "flxsgb",
    "zwczZwr",
    "zwczZqr",
    "zwcz",
    "gqsgBsgqy",
    "gqsgZrf",
    "gqsgSgf",
    "gqsg",
    "zcsgZrf",
    "zcsgSgf",
    "zcsg",
    "hbBhbqygd",
    "hbBhbqy",
    "hbHbqy",
    "hb",
    "fenlBflqygd",
    "fenlBflqy",
    "fenlFlqy",
    "fenl",
    "qysdsndsbtbb",
    "dljbr",
    "dljbrzyzjhm",
    "dlsbrq",
    "slrxm",
    "slswjgDm",
    "slrq",
    "tbbdqk",
    "qygdhjqyxtzsyje",
    "qygdhjtzbl",
    "jdyjbl",
    "zxbztzsuuid"
})
public class Qyjcxxb2017Form  implements Serializable {

    /**
     * @description 字段功能描述
     * @value value:serialVersionUID
     */
    private static final long serialVersionUID = 7139667173525122094L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String nsrsbh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String nsrmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String skssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String skssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fddbr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjzgmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbrq1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tbrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbsxDm1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hznsqy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sshyDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double zcze;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String cyrs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String csgjfxzhjzhy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sffylzz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String czjwgljy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String ssgs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zmcsgqtzyw;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qykjzz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xqykjzz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String qykjzd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sydwkjzz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String mjfylzzkjzd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String cjtjjzzkjzd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String nmzyhzscwkjzd;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String kjzdqt;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fszcgqhztsxswclsx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fsfhbxzctzdynssx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fsjsrgdynssx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fsqyczsx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String czkssj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String czwcsj;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String flxsgb;
    @XmlElement(name = "zwcz_zwr", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zwczZwr;
    @XmlElement(name = "zwcz_zqr", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zwczZqr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zwcz;
    @XmlElement(name = "gqsg_bsgqy", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String gqsgBsgqy;
    @XmlElement(name = "gqsg_zrf", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String gqsgZrf;
    @XmlElement(name = "gqsg_sgf", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String gqsgSgf;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String gqsg;
    @XmlElement(name = "zcsg_zrf", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zcsgZrf;
    @XmlElement(name = "zcsg_sgf", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zcsgSgf;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String zcsg;
    @XmlElement(name = "hb_bhbqygd", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbBhbqygd;
    @XmlElement(name = "hb_bhbqy", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbBhbqy;
    @XmlElement(name = "hb_hbqy", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hbHbqy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String hb;
    @XmlElement(name = "fenl_bflqygd", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fenlBflqygd;
    @XmlElement(name = "fenl_bflqy", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fenlBflqy;
    @XmlElement(name = "fenl_flqy", namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fenlFlqy;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String fenl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String qysdsndsbtbb;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String dljbr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String dljbrzyzjhm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String dlsbrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slrxm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slswjgDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String tbbdqk;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double qygdhjqyxtzsyje;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double qygdhjtzbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected double jdyjbl;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zxbztzsuuid;

    /**
     * 获取nsrsbh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrsbh() {
        return nsrsbh;
    }

    /**
     * 设置nsrsbh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrsbh(String value) {
        this.nsrsbh = value;
    }

    /**
     * 获取nsrmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNsrmc() {
        return nsrmc;
    }

    /**
     * 设置nsrmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNsrmc(String value) {
        this.nsrmc = value;
    }

    /**
     * 获取skssqq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * 设置skssqq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * 获取skssqz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * 设置skssqz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * 获取fddbr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFddbr() {
        return fddbr;
    }

    /**
     * 设置fddbr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFddbr(String value) {
        this.fddbr = value;
    }

    /**
     * 获取kjzgmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKjzgmc() {
        return kjzgmc;
    }

    /**
     * 设置kjzgmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKjzgmc(String value) {
        this.kjzgmc = value;
    }

    /**
     * 获取sbrq1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbrq1() {
        return sbrq1;
    }

    /**
     * 设置sbrq1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbrq1(String value) {
        this.sbrq1 = value;
    }

    /**
     * 获取tbrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTbrq() {
        return tbrq;
    }

    /**
     * 设置tbrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTbrq(String value) {
        this.tbrq = value;
    }

    /**
     * 获取sbsxDm1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbsxDm1() {
        return sbsxDm1;
    }

    /**
     * 设置sbsxDm1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbsxDm1(String value) {
        this.sbsxDm1 = value;
    }

    /**
     * 获取hznsqy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHznsqy() {
        return hznsqy;
    }

    /**
     * 设置hznsqy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHznsqy(String value) {
        this.hznsqy = value;
    }

    /**
     * 获取sshyDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSshyDm() {
        return sshyDm;
    }

    /**
     * 设置sshyDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSshyDm(String value) {
        this.sshyDm = value;
    }

    /**
     * 获取zcze属性的值。
     * 
     */
    public double getZcze() {
        return zcze;
    }

    /**
     * 设置zcze属性的值。
     * 
     */
    public void setZcze(double value) {
        this.zcze = value;
    }

    /**
     * 获取cyrs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCyrs() {
        return cyrs;
    }

    /**
     * 设置cyrs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCyrs(String value) {
        this.cyrs = value;
    }

    /**
     * 获取csgjfxzhjzhy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCsgjfxzhjzhy() {
        return csgjfxzhjzhy;
    }

    /**
     * 设置csgjfxzhjzhy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCsgjfxzhjzhy(String value) {
        this.csgjfxzhjzhy = value;
    }

    /**
     * 获取sffylzz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSffylzz() {
        return sffylzz;
    }

    /**
     * 设置sffylzz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSffylzz(String value) {
        this.sffylzz = value;
    }

    /**
     * 获取czjwgljy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzjwgljy() {
        return czjwgljy;
    }

    /**
     * 设置czjwgljy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzjwgljy(String value) {
        this.czjwgljy = value;
    }

    /**
     * 获取ssgs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSsgs() {
        return ssgs;
    }

    /**
     * 设置ssgs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSsgs(String value) {
        this.ssgs = value;
    }

    /**
     * 获取zmcsgqtzyw属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZmcsgqtzyw() {
        return zmcsgqtzyw;
    }

    /**
     * 设置zmcsgqtzyw属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZmcsgqtzyw(String value) {
        this.zmcsgqtzyw = value;
    }

    /**
     * 获取qykjzz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQykjzz() {
        return qykjzz;
    }

    /**
     * 设置qykjzz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQykjzz(String value) {
        this.qykjzz = value;
    }

    /**
     * 获取xqykjzz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXqykjzz() {
        return xqykjzz;
    }

    /**
     * 设置xqykjzz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXqykjzz(String value) {
        this.xqykjzz = value;
    }

    /**
     * 获取qykjzd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQykjzd() {
        return qykjzd;
    }

    /**
     * 设置qykjzd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQykjzd(String value) {
        this.qykjzd = value;
    }

    /**
     * 获取sydwkjzz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSydwkjzz() {
        return sydwkjzz;
    }

    /**
     * 设置sydwkjzz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSydwkjzz(String value) {
        this.sydwkjzz = value;
    }

    /**
     * 获取mjfylzzkjzd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMjfylzzkjzd() {
        return mjfylzzkjzd;
    }

    /**
     * 设置mjfylzzkjzd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMjfylzzkjzd(String value) {
        this.mjfylzzkjzd = value;
    }

    /**
     * 获取cjtjjzzkjzd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCjtjjzzkjzd() {
        return cjtjjzzkjzd;
    }

    /**
     * 设置cjtjjzzkjzd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCjtjjzzkjzd(String value) {
        this.cjtjjzzkjzd = value;
    }

    /**
     * 获取nmzyhzscwkjzd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNmzyhzscwkjzd() {
        return nmzyhzscwkjzd;
    }

    /**
     * 设置nmzyhzscwkjzd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNmzyhzscwkjzd(String value) {
        this.nmzyhzscwkjzd = value;
    }

    /**
     * 获取kjzdqt属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKjzdqt() {
        return kjzdqt;
    }

    /**
     * 设置kjzdqt属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKjzdqt(String value) {
        this.kjzdqt = value;
    }

    /**
     * 获取fszcgqhztsxswclsx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFszcgqhztsxswclsx() {
        return fszcgqhztsxswclsx;
    }

    /**
     * 设置fszcgqhztsxswclsx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFszcgqhztsxswclsx(String value) {
        this.fszcgqhztsxswclsx = value;
    }

    /**
     * 获取fsfhbxzctzdynssx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFsfhbxzctzdynssx() {
        return fsfhbxzctzdynssx;
    }

    /**
     * 设置fsfhbxzctzdynssx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFsfhbxzctzdynssx(String value) {
        this.fsfhbxzctzdynssx = value;
    }

    /**
     * 获取fsjsrgdynssx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFsjsrgdynssx() {
        return fsjsrgdynssx;
    }

    /**
     * 设置fsjsrgdynssx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFsjsrgdynssx(String value) {
        this.fsjsrgdynssx = value;
    }

    /**
     * 获取fsqyczsx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFsqyczsx() {
        return fsqyczsx;
    }

    /**
     * 设置fsqyczsx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFsqyczsx(String value) {
        this.fsqyczsx = value;
    }

    /**
     * 获取czkssj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzkssj() {
        return czkssj;
    }

    /**
     * 设置czkssj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzkssj(String value) {
        this.czkssj = value;
    }

    /**
     * 获取czwcsj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCzwcsj() {
        return czwcsj;
    }

    /**
     * 设置czwcsj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCzwcsj(String value) {
        this.czwcsj = value;
    }

    /**
     * 获取flxsgb属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlxsgb() {
        return flxsgb;
    }

    /**
     * 设置flxsgb属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlxsgb(String value) {
        this.flxsgb = value;
    }

    /**
     * 获取zwczZwr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZwczZwr() {
        return zwczZwr;
    }

    /**
     * 设置zwczZwr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZwczZwr(String value) {
        this.zwczZwr = value;
    }

    /**
     * 获取zwczZqr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZwczZqr() {
        return zwczZqr;
    }

    /**
     * 设置zwczZqr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZwczZqr(String value) {
        this.zwczZqr = value;
    }

    /**
     * 获取zwcz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZwcz() {
        return zwcz;
    }

    /**
     * 设置zwcz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZwcz(String value) {
        this.zwcz = value;
    }

    /**
     * 获取gqsgBsgqy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGqsgBsgqy() {
        return gqsgBsgqy;
    }

    /**
     * 设置gqsgBsgqy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGqsgBsgqy(String value) {
        this.gqsgBsgqy = value;
    }

    /**
     * 获取gqsgZrf属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGqsgZrf() {
        return gqsgZrf;
    }

    /**
     * 设置gqsgZrf属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGqsgZrf(String value) {
        this.gqsgZrf = value;
    }

    /**
     * 获取gqsgSgf属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGqsgSgf() {
        return gqsgSgf;
    }

    /**
     * 设置gqsgSgf属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGqsgSgf(String value) {
        this.gqsgSgf = value;
    }

    /**
     * 获取gqsg属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGqsg() {
        return gqsg;
    }

    /**
     * 设置gqsg属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGqsg(String value) {
        this.gqsg = value;
    }

    /**
     * 获取zcsgZrf属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZcsgZrf() {
        return zcsgZrf;
    }

    /**
     * 设置zcsgZrf属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZcsgZrf(String value) {
        this.zcsgZrf = value;
    }

    /**
     * 获取zcsgSgf属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZcsgSgf() {
        return zcsgSgf;
    }

    /**
     * 设置zcsgSgf属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZcsgSgf(String value) {
        this.zcsgSgf = value;
    }

    /**
     * 获取zcsg属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZcsg() {
        return zcsg;
    }

    /**
     * 设置zcsg属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZcsg(String value) {
        this.zcsg = value;
    }

    /**
     * 获取hbBhbqygd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHbBhbqygd() {
        return hbBhbqygd;
    }

    /**
     * 设置hbBhbqygd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHbBhbqygd(String value) {
        this.hbBhbqygd = value;
    }

    /**
     * 获取hbBhbqy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHbBhbqy() {
        return hbBhbqy;
    }

    /**
     * 设置hbBhbqy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHbBhbqy(String value) {
        this.hbBhbqy = value;
    }

    /**
     * 获取hbHbqy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHbHbqy() {
        return hbHbqy;
    }

    /**
     * 设置hbHbqy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHbHbqy(String value) {
        this.hbHbqy = value;
    }

    /**
     * 获取hb属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHb() {
        return hb;
    }

    /**
     * 设置hb属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHb(String value) {
        this.hb = value;
    }

    /**
     * 获取fenlBflqygd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFenlBflqygd() {
        return fenlBflqygd;
    }

    /**
     * 设置fenlBflqygd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFenlBflqygd(String value) {
        this.fenlBflqygd = value;
    }

    /**
     * 获取fenlBflqy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFenlBflqy() {
        return fenlBflqy;
    }

    /**
     * 设置fenlBflqy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFenlBflqy(String value) {
        this.fenlBflqy = value;
    }

    /**
     * 获取fenlFlqy属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFenlFlqy() {
        return fenlFlqy;
    }

    /**
     * 设置fenlFlqy属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFenlFlqy(String value) {
        this.fenlFlqy = value;
    }

    /**
     * 获取fenl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFenl() {
        return fenl;
    }

    /**
     * 设置fenl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFenl(String value) {
        this.fenl = value;
    }

    /**
     * 获取qysdsndsbtbb属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQysdsndsbtbb() {
        return qysdsndsbtbb;
    }

    /**
     * 设置qysdsndsbtbb属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQysdsndsbtbb(String value) {
        this.qysdsndsbtbb = value;
    }

    /**
     * 获取dljbr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDljbr() {
        return dljbr;
    }

    /**
     * 设置dljbr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDljbr(String value) {
        this.dljbr = value;
    }

    /**
     * 获取dljbrzyzjhm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDljbrzyzjhm() {
        return dljbrzyzjhm;
    }

    /**
     * 设置dljbrzyzjhm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDljbrzyzjhm(String value) {
        this.dljbrzyzjhm = value;
    }

    /**
     * 获取dlsbrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDlsbrq() {
        return dlsbrq;
    }

    /**
     * 设置dlsbrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDlsbrq(String value) {
        this.dlsbrq = value;
    }

    /**
     * 获取slrxm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlrxm() {
        return slrxm;
    }

    /**
     * 设置slrxm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlrxm(String value) {
        this.slrxm = value;
    }

    /**
     * 获取slswjgDm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlswjgDm() {
        return slswjgDm;
    }

    /**
     * 设置slswjgDm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlswjgDm(String value) {
        this.slswjgDm = value;
    }

    /**
     * 获取slrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlrq() {
        return slrq;
    }

    /**
     * 设置slrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlrq(String value) {
        this.slrq = value;
    }

    /**
     * 获取tbbdqk属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTbbdqk() {
        return tbbdqk;
    }

    /**
     * 设置tbbdqk属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTbbdqk(String value) {
        this.tbbdqk = value;
    }

    /**
     * 获取qygdhjqyxtzsyje属性的值。
     * 
     */
    public double getQygdhjqyxtzsyje() {
        return qygdhjqyxtzsyje;
    }

    /**
     * 设置qygdhjqyxtzsyje属性的值。
     * 
     */
    public void setQygdhjqyxtzsyje(double value) {
        this.qygdhjqyxtzsyje = value;
    }

    /**
     * 获取qygdhjtzbl属性的值。
     * 
     */
    public double getQygdhjtzbl() {
        return qygdhjtzbl;
    }

    /**
     * 设置qygdhjtzbl属性的值。
     * 
     */
    public void setQygdhjtzbl(double value) {
        this.qygdhjtzbl = value;
    }

    /**
     * 获取jdyjbl属性的值。
     * 
     */
    public double getJdyjbl() {
        return jdyjbl;
    }

    /**
     * 设置jdyjbl属性的值。
     * 
     */
    public void setJdyjbl(double value) {
        this.jdyjbl = value;
    }

    /**
     * 获取zxbztzsuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZxbztzsuuid() {
        return zxbztzsuuid;
    }

    /**
     * 设置zxbztzsuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZxbztzsuuid(String value) {
        this.zxbztzsuuid = value;
    }

}
