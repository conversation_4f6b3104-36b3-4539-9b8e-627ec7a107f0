package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 申报表受理信息VO
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sbbslxxVO", propOrder = { "slrDm", "slswjgDm", "slrq" })
@XmlSeeAlso({ Sbbslxxkz2VO.class, Sbbslxxkz1VO.class })
@Getter
@Setter
public class SbbslxxVO {
    /**
     * 受理人代码
     */
    @XmlElement(nillable = true, required = true)
    protected String slrDm;

    /**
     * 受理税务机关代码
     */
    @XmlElement(nillable = true, required = true)
    protected String slswjgDm;

    /**
     * 受理日期
     */
    @XmlElement(nillable = true, required = true)
    protected String slrq;
}