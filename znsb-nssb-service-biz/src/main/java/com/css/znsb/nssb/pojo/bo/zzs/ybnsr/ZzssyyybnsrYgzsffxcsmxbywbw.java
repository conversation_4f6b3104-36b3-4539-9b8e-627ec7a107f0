package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《营改增税负分析测算明细表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_ygzsffxcsmxbywbw", propOrder = { "zzssyyybnsrYgzsffxcsmxb" })
@Getter
@Setter
public class ZzssyyybnsrYgzsffxcsmxbywbw extends TaxDoc {
    /**
     * 《营改增税负分析测算明细表》
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_ygzsffxcsmxb", required = true)
    @JSONField(name = "zzssyyybnsr_ygzsffxcsmxb")
    protected ZzssyyybnsrYgzsffxcsmxb zzssyyybnsrYgzsffxcsmxb;
}