package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《加油站月销售油品汇总表》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsybnsrsb_jyzyxsyphzbbw", propOrder = { "zzsybnsrsbJyzyxsyphzb" })
@Getter
@Setter
public class ZzsybnsrsbJyzyxsyphzbbw extends TaxDoc {
    /**
     * 《加油站月销售油品汇总表》业务报文
     */
    @XmlElement(nillable = true, name = "zzsybnsrsb_jyzyxsyphzb", required = true)
    @JSONField(name = "zzsybnsrsb_jyzyxsyphzb")
    protected ZzsybnsrsbJyzyxsyphzb zzsybnsrsbJyzyxsyphzb;
}