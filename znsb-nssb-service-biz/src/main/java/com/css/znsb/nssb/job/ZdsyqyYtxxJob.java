package com.css.znsb.nssb.job;

import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.mapper.semir.KjKmyebMapper;
import com.css.znsb.gjss.pojo.domain.semir.KjKmyebDO;
import com.css.znsb.nssb.api.cwbb.CwbbDwCxApi;
import com.css.znsb.nssb.pojo.domain.skjn.ZnsbNssbJksmxDO;
import com.css.znsb.nssb.pojo.domain.zdsy.ZnsbNssbZdsysjcjBsqc;
import com.css.znsb.nssb.pojo.dto.cwbb.*;
import com.css.znsb.nssb.pojo.dto.zdsycjxx.ZdsyQyxxDTO;
import com.css.znsb.nssb.pojo.vo.zdsybcxxcj.ZdsyKmyebReqVO;
import com.css.znsb.nssb.pojo.vo.zdsybcxxcj.qcsj.*;
import com.css.znsb.nssb.pojo.vo.zdsybcxxcj.qzcx.ZdsybcxxcjQzcxRespVO;
import com.css.znsb.nssb.service.skjn.ZnsbNssbJksmxService;
import com.css.znsb.nssb.service.zdsy.ZdsyLqService;
import com.css.znsb.nssb.service.zdsy.ZnsbNssbZdsysjcjBsqcService;
import com.css.znsb.tzzx.service.yjtz.ZnsbTzzxQysdsyjtzService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.css.znsb.nssb.constants.zdsy.ZdsyConstants.*;

@Slf4j
@Component
public class ZdsyqyYtxxJob {

    @Resource
    private ZdsyLqService zdsyLqService;
    @Resource
    private ZnsbNssbJksmxService znsbNssbJksmxService;
    @Resource
    private KjKmyebMapper kjKmyebMapper;
    @Resource
    private ZnsbNssbZdsysjcjBsqcService znsbNssbZdsysjcjBsqcService;
    @Resource
    private CwbbDwCxApi cwbbDwCxApi;
    @Resource
    private ZnsbTzzxQysdsyjtzService znsbTzzxQysdsyjtzService;

    /**
     * 重点税源企业预填信息
     */
    @XxlJob("ZdsyDataQuery")
    public void execute() {
        log.info("==========开始重点税源企业预填信息归集==========");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("入参登记序号jobParam{}",jobParam);
        String djxh = "";
        String nyr = "";
        if(GyUtils.isNotNull(jobParam)){
            String[] param = jobParam.split(";");
            djxh = param[0];
            nyr = param[1];
        }
        //当前时间
        LocalDate currentDate = LocalDate.now();
        if(GyUtils.isNotNull(nyr)){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            currentDate = LocalDate.parse(nyr, formatter);
        }
        //本年上期
        LocalDate bnsqLocal = currentDate.minusMonths(2);
        String bnsq = bnsqLocal.format(DateTimeFormatter.ofPattern("yyyyMM"));
        //本期
        LocalDate bnbqLocal = currentDate.minusMonths(1);
        String bnbq = bnbqLocal.format(DateTimeFormatter.ofPattern("yyyyMM"));
        LocalDate firstDayOfMonth = bnbqLocal.withDayOfMonth(1);
        Date firstDay = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        LocalDate lastDayOfMonth = bnbqLocal.withDayOfMonth(bnbqLocal.lengthOfMonth());
        Date lastDay = Date.from(lastDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        log.info("bnbq{}",bnbq);
        log.info("firstDay{}",firstDay);
        log.info("lastDay{}",lastDay);
        LocalDate bnbqCbLocal = currentDate.minusMonths(3);
        LocalDate cwbbFirstDay = bnbqCbLocal.withDayOfMonth(1);
        Date cwbbFirstDayD = Date.from(cwbbFirstDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
        log.info("cwbbFirstDayD{}",cwbbFirstDayD);
        //下期
        String bnxq = currentDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
        log.info("bnxq{}",bnxq);
        //上年下期
        String snxq = currentDate.minusYears(1).format(DateTimeFormatter.ofPattern("yyyyMM"));
        log.info("snxq{}",snxq);
        //上年本期
        LocalDate snbqLocal = bnbqLocal.minusYears(1);
        String snbq = snbqLocal.format(DateTimeFormatter.ofPattern("yyyyMM"));
        log.info("snbq{}",snbq);
        LocalDate sqFirstDayOfMonth = cwbbFirstDay.minusYears(1);
        Date sqFirstDay = Date.from(sqFirstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        LocalDate sqLastDayOfMonth = snbqLocal.withDayOfMonth(bnbqLocal.lengthOfMonth());
        Date sqLastDay = Date.from(sqLastDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        log.info("sqFirstDay{}",sqFirstDay);
        log.info("sqLastDay{}",sqLastDay);
        //上年下下期
        LocalDate snxxqLocal = currentDate.minusYears(1).plusMonths(1); // 去年
        String snxxq = snxxqLocal.format(DateTimeFormatter.ofPattern("yyyyMM"));
        log.info("snxxq{}",snxxq);
        //上年
        LocalDate lastYearJanuary = currentDate.minusYears(1).withMonth(1);// 一月
        LocalDate lastYearDecember = currentDate.minusYears(1).withMonth(12); //十二月
        String firstYear = lastYearJanuary.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String lastYear = lastYearDecember.format(DateTimeFormatter.ofPattern("yyyyMM"));
        log.info("firstYear{}",firstYear);
        log.info("lastYear{}",lastYear);
        //保存附表
        List<ZdsyQyxxDTO> dtoList = zdsyLqService.queryZdsyQyxx(djxh);
        for(ZdsyQyxxDTO zdsyQyxxDTO : dtoList){
            log.info("公司配置信息{}",JsonUtils.toJson(zdsyQyxxDTO));
            if("1002".equals(zdsyQyxxDTO.getGsh1())){
                continue;
            }
            //生成待采集清册列表
            String zlbscjuuid = GyUtils.getUuid();
            ZdsybcxxcjQzcxRespVO qzcxRespVO = new ZdsybcxxcjQzcxRespVO();
            qzcxRespVO.setDjxh(zdsyQyxxDTO.getDjxh());
            qzcxRespVO.setNsrsbh(zdsyQyxxDTO.getNsrsbh());
            qzcxRespVO.setSsny(bnbq);
            qzcxRespVO.setUuid(zlbscjuuid);
            qzcxRespVO.setSsqq(firstDay);
            qzcxRespVO.setSsqz(lastDay);
            ZnsbNssbZdsysjcjBsqc zdsysjcjBsqc = znsbNssbZdsysjcjBsqcService.queryZdsybcxxcjb(qzcxRespVO);
            if(GyUtils.isNull(zdsysjcjBsqc)){
                zdsyLqService.saveZdsysjcjBsqc(qzcxRespVO);
            }else{
                zlbscjuuid = zdsysjcjBsqc.getUuid();
            }
            //B1企业基本信息表
            zdsyQyxxDTO.setZlbscjuuid(zlbscjuuid);
            this.saveZdsyqyjbxxb(zdsyQyxxDTO);
            //B2企业税收信息表
            zdsyQyxxDTO.setSsq(bnbq);
            zdsyQyxxDTO.setSnbq(snbq);
            zdsyQyxxDTO.setBnsq(bnsq);
            zdsyQyxxDTO.setFirstDay(cwbbFirstDayD);
            zdsyQyxxDTO.setLastDay(lastDay);
            zdsyQyxxDTO.setSqFirstDay(sqFirstDay);
            zdsyQyxxDTO.setSqLastDay(sqLastDay);
            this.saveZdsyqySsxxb(zdsyQyxxDTO);
            //B4财务报表
            if("03,06,09,12".contains(bnbq.substring(4,6))){
                this.saveZdsyqyCwxx(zdsyQyxxDTO);
            }
            //B5景气表
            this.saveZdsyQyjqdcwjbJqjyxx(zdsyQyxxDTO);
            zdsyQyxxDTO.setSnxxq(snxxq);
            zdsyQyxxDTO.setSnnc(firstYear);
            zdsyQyxxDTO.setSnnm(lastYear);
            zdsyQyxxDTO.setBnxq(bnxq);
            zdsyQyxxDTO.setSnxq(snxq);
            this.saveZdsyQyjqdcwjbQnyc(zdsyQyxxDTO);
        }
        log.info("==========重点税源企业预填信息归集结束==========");
    }

    private void saveZdsyqyjbxxb(ZdsyQyxxDTO zdsyQyxxDTO){
        final Map<String,Object> zdsyqyJbxxbMap = JsonUtils.toMap(SBZDSYQYJBXX);
        final List<Map<String,String>> zdsyqyJbxxbList = (List<Map<String, String>>) zdsyqyJbxxbMap.get("sbZdsyqyJbxxb");
        final List<ZdsyqyJbxxbVO> sbZdsyqyJbxxb = BeanUtils.toBean(zdsyqyJbxxbList,ZdsyqyJbxxbVO.class);
        ZdsyqyJbxxbVO zdsyqyJbxxbVO = new ZdsyqyJbxxbVO();
        zdsyqyJbxxbVO.setEwbhxh("1");
        zdsyqyJbxxbVO.setEwbhlbm("TYSHXYDM");
        zdsyqyJbxxbVO.setHmc("统一社会信用代码");
        zdsyqyJbxxbVO.setZdsyqyjbxxnr(zdsyQyxxDTO.getNsrsbh());
        sbZdsyqyJbxxb.add(zdsyqyJbxxbVO);
        zdsyqyJbxxbVO = new ZdsyqyJbxxbVO();
        zdsyqyJbxxbVO.setEwbhxh("2");
        zdsyqyJbxxbVO.setEwbhlbm("QYMC");
        zdsyqyJbxxbVO.setHmc("企业名称");
        zdsyqyJbxxbVO.setZdsyqyjbxxnr(zdsyQyxxDTO.getNsrmc());
        sbZdsyqyJbxxb.add(zdsyqyJbxxbVO);
        zdsyLqService.saveZdsyQyjbxxb(sbZdsyqyJbxxb,zdsyQyxxDTO.getZlbscjuuid());
    }

    private void saveZdsyqySsxxb(ZdsyQyxxDTO zdsyQyxxDTO){
        final ZdsybcxxcjQzcxRespVO qzcxRespVO = new ZdsybcxxcjQzcxRespVO();
        qzcxRespVO.setDjxh(zdsyQyxxDTO.getDjxh());
        qzcxRespVO.setSsny(zdsyQyxxDTO.getBnsq());
        final ZnsbNssbZdsysjcjBsqc zdsysjcjBsqc = znsbNssbZdsysjcjBsqcService.queryZdsybcxxcjb(qzcxRespVO);
        if (GyUtils.isNotNull(zdsysjcjBsqc)) {
            final  List<ZdsyqySsxxbVO> sbZdsyqySsxxb = zdsyLqService.queryZdsyqySsxxb(zdsysjcjBsqc.getUuid());
            for(ZdsyqySsxxbVO zdsyqySsxxbVO : sbZdsyqySsxxb){
                if("181,182,183,184,185,186,187,188".contains(zdsyqySsxxbVO.getEwbhxh())){
                    continue;
                }
                zdsyqySsxxbVO.setBnbqs(BigDecimal.ZERO);
                zdsyqySsxxbVO.setSnbqs(BigDecimal.ZERO);
                zdsyLqService.saveZdsySsxxb(zdsyqySsxxbVO,zdsyQyxxDTO.getZlbscjuuid());
            }
        }

        QykjzzYbqyYzxVO qykjzzYbqyYzxVo = new QykjzzYbqyYzxVO();
        List<CwbbQykjzzybqyLrbyzxVO> cwbbBsLrbYzxVOList = new ArrayList<>();
        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbBqMap = new HashMap<>();
        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbSqMap = new HashMap<>();
        //财报本期数据
        CwbbSjcxReqVO cwbbSjcxReqVO = new CwbbSjcxReqVO();
        cwbbSjcxReqVO.setDjxh(zdsyQyxxDTO.getDjxh());
//        cwbbSjcxReqVO.setSkssqq(zdsyQyxxDTO.getFirstDay());
//        cwbbSjcxReqVO.setSkssqz(zdsyQyxxDTO.getLastDay());
        cwbbSjcxReqVO.setZlbsxlDm("ZL1001001");
        cwbbSjcxReqVO.setSszq(zdsyQyxxDTO.getSsq());
        log.info("本期财报数据请求reqVO{}",JsonUtils.toJson(cwbbSjcxReqVO));
        CwbbSjcxResVO cwbbSjcxResVO = cwbbDwCxApi.cwbbDwcxTzsj(cwbbSjcxReqVO).getData();
        log.info("本期财报数据返回cwbbSjcxResVO{}",JsonUtils.toJson(cwbbSjcxResVO));
        if(GyUtils.isNotNull(cwbbSjcxResVO)){
            qykjzzYbqyYzxVo = cwbbSjcxResVO.getQykjzzYbqyYzxVo();
            if(GyUtils.isNotNull(qykjzzYbqyYzxVo)){
                cwbbBsLrbYzxVOList = qykjzzYbqyYzxVo.getCwbbBsLrbYzxVOList();
                if (GyUtils.isNotNull(cwbbBsLrbYzxVOList)) {
                    lrbBqMap = cwbbBsLrbYzxVOList.stream()
                            .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, lrbyzxVO->lrbyzxVO));
                }
            }
        }
        //财报去年同期数据
//        cwbbSjcxReqVO.setSkssqq(zdsyQyxxDTO.getSqFirstDay());
//        cwbbSjcxReqVO.setSkssqz(zdsyQyxxDTO.getSqLastDay());
        cwbbSjcxReqVO.setSszq(zdsyQyxxDTO.getSnbq());
        log.info("去年同期财报数据请求reqVO{}",JsonUtils.toJson(cwbbSjcxReqVO));
        cwbbSjcxResVO = cwbbDwCxApi.cwbbDwcxTzsj(cwbbSjcxReqVO).getData();
        log.info("去年同期财报数据返回cwbbSjcxResVO{}",JsonUtils.toJson(cwbbSjcxResVO));
        if(GyUtils.isNotNull(cwbbSjcxResVO)){
            if(GyUtils.isNotNull(qykjzzYbqyYzxVo)){
                cwbbBsLrbYzxVOList = qykjzzYbqyYzxVo.getCwbbBsLrbYzxVOList();
                if (GyUtils.isNotNull(cwbbBsLrbYzxVOList)) {
                    lrbSqMap = cwbbBsLrbYzxVOList.stream()
                            .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, lrbyzxVO->lrbyzxVO));
                }
            }
        }

        //科目余额表请求VO
        final ZdsyKmyebReqVO kmyebReqVO = new ZdsyKmyebReqVO();
        kmyebReqVO.setNsrsbh(zdsyQyxxDTO.getNsrsbh());
        kmyebReqVO.setSszq(zdsyQyxxDTO.getSsq());
        kmyebReqVO.setGsh(zdsyQyxxDTO.getGsh1());
        final List<KjKmyebDO> kmyeList = kjKmyebMapper.querybykmbm(kmyebReqVO);
        final Map<String, KjKmyebDO> bnMap = kmyeList.stream()
                .collect(Collectors.toMap(KjKmyebDO::getKmbm, kjKmyebDO->kjKmyebDO));
        log.info("本期科目余额表{}",JsonUtils.toJson(bnMap));

        kmyebReqVO.setSszq(zdsyQyxxDTO.getSnbq());
        final List<KjKmyebDO> snKmyeList = kjKmyebMapper.querybykmbm(kmyebReqVO);
        final Map<String, KjKmyebDO> snMap = snKmyeList.stream()
                .collect(Collectors.toMap(KjKmyebDO::getKmbm, kjKmyebDO->kjKmyebDO));
        log.info("上期科目余额表{}",JsonUtils.toJson(bnMap));

        List<ZdsyQyxxDTO> fgsList = new ArrayList<>();
        if("1000".equals(zdsyQyxxDTO.getGsh1())){
            fgsList = zdsyLqService.queryZdsyFzjgQyxx(zdsyQyxxDTO.getDjxh());
        }

        //二维码行序号科目编码对照map
        final Map<String, String> kmbmMap = JsonUtils.toMap(ZDSYKMBM,String.class,String.class);
        //需要保存的行
        final List<ZdsyqySsxxbVO> zdsyqySsxxbList = JsonUtils.toList(ZDSYQYSSXXB,ZdsyqySsxxbVO.class);
        BigDecimal bnbqshj147 = BigDecimal.ZERO;
        BigDecimal snbqshj147 = BigDecimal.ZERO;
        BigDecimal bnljshj147 = BigDecimal.ZERO;
        BigDecimal snljshj147 = BigDecimal.ZERO;
        BigDecimal bnbqshj165 = BigDecimal.ZERO;
        BigDecimal snbqshj165 = BigDecimal.ZERO;
        BigDecimal bnljshj165 = BigDecimal.ZERO;
        BigDecimal snljshj165 = BigDecimal.ZERO;
        BigDecimal bnbqshj184 = BigDecimal.ZERO;
        BigDecimal snbqshj184 = BigDecimal.ZERO;
        BigDecimal bnljshj184 = BigDecimal.ZERO;
        BigDecimal snljshj184 = BigDecimal.ZERO;
        for(ZdsyqySsxxbVO zdsyqySsxxbVO : zdsyqySsxxbList){
            final String ewbhxh = zdsyqySsxxbVO.getEwbhxh();
            final String kmbm = kmbmMap.get(ewbhxh);
            log.info("ewbhxh{}",JsonUtils.toJson(ewbhxh));
            log.info("kmbm{}",JsonUtils.toJson(kmbm));
            BigDecimal bnbqs = BigDecimal.ZERO;
            BigDecimal bnljs = BigDecimal.ZERO;
            BigDecimal snbqs = BigDecimal.ZERO;
            BigDecimal snljs = BigDecimal.ZERO;
            if(GyUtils.isNotNull(kmbm)){
                final ZdsyqySsxxbVO ssxxbVODB = zdsyLqService.queryEwbhxhSsxx(zdsyQyxxDTO.getZlbscjuuid(),ewbhxh);
                //本期数
                KjKmyebDO kmyebDO = bnMap.get(kmbm);
                log.info("kmyebDO{}",JsonUtils.toJson(kmyebDO));
                if(GyUtils.isNotNull(kmyebDO)){
                    if("187".equals(ewbhxh)){
                        bnljs = toDec(kmyebDO.getBnjfljs());
                    }else {
                        bnbqs = toDec(kmyebDO.getByjffse());
                    }
                }
                //去年本期数
                kmyebDO = snMap.get(kmbm);
                if(GyUtils.isNotNull(kmyebDO)){
                    if("187".equals(ewbhxh)){
                        snljs = toDec(kmyebDO.getBnjfljs());
                    }else {
                        snbqs = toDec(kmyebDO.getByjffse());
                    }
                }
                if("149".equals(ewbhxh)){
                    kmyebDO = bnMap.get("2211030500");
                    if(GyUtils.isNotNull(kmyebDO)){
                        bnbqs = bnbqs.add(toDec(kmyebDO.getByjffse()));
                    }
                    kmyebDO = snMap.get("2211030500");
                    if(GyUtils.isNotNull(kmyebDO)){
                        snbqs = snbqs.add(toDec(kmyebDO.getByjffse()));
                    }
                }
                //分公司数据相加
                if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)){
                    for(ZdsyQyxxDTO fgsDTO : fgsList){
                        final ZdsyKmyebReqVO reqVO = new ZdsyKmyebReqVO();
                        reqVO.setNsrsbh(fgsDTO.getNsrsbh());
                        reqVO.setSszq(zdsyQyxxDTO.getSsq());
                        reqVO.setGsh(fgsDTO.getGsh1());
                        final List<KjKmyebDO> fgsbn = kjKmyebMapper.querybykmbm(reqVO);
                        final Map<String, KjKmyebDO> fgsbnM = fgsbn.stream()
                                .collect(Collectors.toMap(KjKmyebDO::getKmbm, kjKmyebDO->kjKmyebDO));
                        log.info("分公司号{},本期科目余额表{}",fgsDTO.getGsh1(),JsonUtils.toJson(bnMap));

                        reqVO.setSszq(zdsyQyxxDTO.getSnbq());
                        final List<KjKmyebDO> fgssn = kjKmyebMapper.querybykmbm(reqVO);
                        final Map<String, KjKmyebDO> fgssnM = fgssn.stream()
                                .collect(Collectors.toMap(KjKmyebDO::getKmbm, kjKmyebDO->kjKmyebDO));
                        log.info("分公司号{},上期科目余额表{}",fgsDTO.getGsh1(),JsonUtils.toJson(bnMap));
                        //本期数
                        kmyebDO = fgsbnM.get(kmbm);
                        if(GyUtils.isNotNull(kmyebDO)){
                            if("187".equals(ewbhxh)){
                                bnljs = bnljs.add(toDec(kmyebDO.getBnjfljs()));
                            }else {
                                bnbqs = bnbqs.add(toDec(kmyebDO.getByjffse()));
                            }
                        }
                        //去年本期数
                        kmyebDO = fgssnM.get(kmbm);
                        if(GyUtils.isNotNull(kmyebDO)){
                            if("187".equals(ewbhxh)){
                                snljs = snljs.add(toDec(kmyebDO.getBnjfljs()));
                            }else {
                                snbqs = snbqs.add(toDec(kmyebDO.getByjffse()));
                            }
                        }
                        if("149".equals(ewbhxh)){
                            kmyebDO = bnMap.get("2211030500");
                            if(GyUtils.isNotNull(kmyebDO)){
                                bnbqs = bnbqs.add(toDec(kmyebDO.getByjffse()));
                            }
                            kmyebDO = snMap.get("2211030500");
                            if(GyUtils.isNotNull(kmyebDO)){
                                snbqs = snbqs.add(toDec(kmyebDO.getByjffse()));
                            }
                        }
                    }
                }
                log.info("bnbqs{}",bnbqs);
                log.info("bnljs{}",bnljs);
                log.info("snbqs{}",snbqs);
                log.info("snljs{}",snljs);
                if(!"187".equals(ewbhxh)){
                    bnljs = bnbqs.divide(DECIMALDIV);
                    snljs = snbqs.divide(DECIMALDIV);
                    if(GyUtils.isNotNull(ssxxbVODB)){
                        bnljs = ssxxbVODB.getBnljs().add(bnljs);
                        snljs = ssxxbVODB.getSnljs().add(snljs);
                    }
                }else{
                    bnljs = bnljs.divide(DECIMALDIV);
                    snljs = snljs.divide(DECIMALDIV);
                }
                if("148,149,150,151,152".contains(ewbhxh)){
                    bnbqshj147 = bnbqshj147.add(bnbqs);
                    snbqshj147 = snbqshj147.add(snbqs);
                }
                if("166".equals(ewbhxh)){
                    bnbqshj165 = bnbqshj165.add(bnbqs);
                    snbqshj165 = snbqshj165.add(snbqs);
                }
                zdsyqySsxxbVO.setBnbqs(bnbqs.divide(DECIMALDIV));
                zdsyqySsxxbVO.setBnljs(bnljs);
                zdsyqySsxxbVO.setSnbqs(snbqs.divide(DECIMALDIV));
                zdsyqySsxxbVO.setSnljs(snljs);
                log.info("zdsyqySsxxbVO{}",JsonUtils.toJson(zdsyqySsxxbVO));
            } else if ("181,182,185".contains(ewbhxh)) {
                if("181".equals(ewbhxh)){
                    kmyebReqVO.setKmbm("16010");
                    kmyebReqVO.setZcswlxDm("100");
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSsq());
                    KjKmyebDO kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    log.info("181kmyebDO{}",JsonUtils.toJson(kmyebDO));
                    if(GyUtils.isNotNull(kmyebDO)){
                        bnljs = bnljs.add(toDec(kmyebDO.getBnjfljs()).divide(DECIMALDIV));
                    }
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSnbq());
                    kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    log.info("181kmyebDO{}",JsonUtils.toJson(kmyebDO));
                    if(GyUtils.isNotNull(kmyebDO)){
                        snljs = snljs.add(toDec(kmyebDO.getBnjfljs()).divide(DECIMALDIV));
                    }
                    //分公司数据相加
                    if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)) {
                        for (ZdsyQyxxDTO fgsDTO : fgsList) {
                            final ZdsyKmyebReqVO reqVO = new ZdsyKmyebReqVO();
                            reqVO.setNsrsbh(fgsDTO.getNsrsbh());
                            reqVO.setKmbm("16010");
                            reqVO.setZcswlxDm("100");
                            reqVO.setSszq(zdsyQyxxDTO.getSsq());
                            reqVO.setGsh(fgsDTO.getGsh1());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(reqVO);
                            if(GyUtils.isNotNull(kmyebDO)){
                                bnljs = bnljs.add(toDec(kmyebDO.getBnjfljs()).divide(DECIMALDIV));
                            }
                            reqVO.setSszq(zdsyQyxxDTO.getSnbq());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(reqVO);
                            if(GyUtils.isNotNull(kmyebDO)){
                                snljs = snljs.add(toDec(kmyebDO.getBnjfljs()).divide(DECIMALDIV));
                            }
                        }
                    }
                } else if ("182".equals(ewbhxh)) {
                    kmyebReqVO.setKmbm("1602");
                    kmyebReqVO.setZcswlxDm("500");
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSsq());
                    KjKmyebDO kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    log.info("182kmyebDO{}",JsonUtils.toJson(kmyebDO));
                    if(GyUtils.isNotNull(kmyebDO)){
                        bnljs = bnljs.add(toDec(kmyebDO.getBndfljs()).divide(DECIMALDIV));
                    }
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSnbq());
                    kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    log.info("182kmyebDO{}",JsonUtils.toJson(kmyebDO));
                    if(GyUtils.isNotNull(kmyebDO)){
                        snljs = snljs.add(toDec(kmyebDO.getBndfljs()).divide(DECIMALDIV));
                    }
                    //分公司数据相加
                    if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)) {
                        for (ZdsyQyxxDTO fgsDTO : fgsList) {
                            final ZdsyKmyebReqVO reqVO = new ZdsyKmyebReqVO();
                            reqVO.setNsrsbh(fgsDTO.getNsrsbh());
                            reqVO.setKmbm("1602");
                            reqVO.setZcswlxDm("500");
                            reqVO.setSszq(zdsyQyxxDTO.getSsq());
                            reqVO.setGsh(fgsDTO.getGsh1());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(reqVO);
                            if(GyUtils.isNotNull(kmyebDO)){
                                bnljs = bnljs.add(toDec(kmyebDO.getBndfljs()).divide(DECIMALDIV));
                            }
                            reqVO.setSszq(zdsyQyxxDTO.getSnbq());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(reqVO);
                            if(GyUtils.isNotNull(kmyebDO)){
                                snljs = snljs.add(toDec(kmyebDO.getBndfljs()).divide(DECIMALDIV));
                            }
                        }
                    }
                } else if ("185".equals(ewbhxh)) {
                    kmyebReqVO.setKmbm("66010101");
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSsq());
                    kmyebReqVO.setZcswlxDm("");
                    KjKmyebDO kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    log.info("kmyebDO{}",JsonUtils.toJson(kmyebDO));
                    if(GyUtils.isNotNull(kmyebDO)){
                        bnljs = bnljs.add(toDec(kmyebDO.getBnqmye()));
                    }
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSnbq());
                    kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    log.info("kmyebDO{}",JsonUtils.toJson(kmyebDO));
                    if(GyUtils.isNotNull(kmyebDO)){
                        snljs = snljs.add(toDec(kmyebDO.getBnqmye()));
                    }
                    kmyebReqVO.setKmbm("22110901");
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSsq());
                    kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    log.info("kmyebDO{}",JsonUtils.toJson(kmyebDO));
                    if(GyUtils.isNotNull(kmyebDO)){
                        bnljs = bnljs.subtract(toDec(kmyebDO.getBndfljs())).divide(DECIMALDIV);
                    }
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSnbq());
                    kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    log.info("kmyebDO{}",JsonUtils.toJson(kmyebDO));
                    if(GyUtils.isNotNull(kmyebDO)){
                        snljs = snljs.subtract(toDec(kmyebDO.getBndfljs())).divide(DECIMALDIV);
                    }
                    //分公司数据相加
                    if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)) {
                        for (ZdsyQyxxDTO fgsDTO : fgsList) {
                            final ZdsyKmyebReqVO reqVO = new ZdsyKmyebReqVO();
                            reqVO.setNsrsbh(fgsDTO.getNsrsbh());
                            kmyebReqVO.setKmbm("66010101");
                            kmyebReqVO.setSszq(zdsyQyxxDTO.getSsq());
                            kmyebReqVO.setZcswlxDm("");
                            reqVO.setGsh(fgsDTO.getGsh1());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(reqVO);
                            if(GyUtils.isNotNull(kmyebDO)){
                                bnljs = bnljs.add(toDec(kmyebDO.getBnqmye()));
                            }
                            reqVO.setSszq(zdsyQyxxDTO.getSnbq());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(reqVO);
                            if(GyUtils.isNotNull(kmyebDO)){
                                snljs = snljs.add(toDec(kmyebDO.getBndfljs()));
                            }
                            reqVO.setKmbm("22110901");
                            reqVO.setSszq(zdsyQyxxDTO.getSsq());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(reqVO);
                            if(GyUtils.isNotNull(kmyebDO)){
                                bnljs = bnljs.subtract(toDec(kmyebDO.getBndfljs())).divide(DECIMALDIV);
                            }
                            reqVO.setSszq(zdsyQyxxDTO.getSnbq());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(reqVO);
                            if(GyUtils.isNotNull(kmyebDO)){
                                snljs = snljs.subtract(toDec(kmyebDO.getBndfljs())).divide(DECIMALDIV);
                            }
                        }
                    }
                    bnbqshj184 = bnbqshj184.add(bnbqs.divide(DECIMALDIV));
                    bnljshj184 = bnljshj184.add(bnljs);
                    snbqshj184 = snbqshj184.add(snbqs.divide(DECIMALDIV));
                    snljshj184 = snljshj184.add(snljs);
                }
                zdsyqySsxxbVO.setBnbqs(bnbqs.divide(DECIMALDIV));
                zdsyqySsxxbVO.setBnljs(bnljs);
                zdsyqySsxxbVO.setSnbqs(snbqs.divide(DECIMALDIV));
                zdsyqySsxxbVO.setSnljs(snljs);
                log.info("zdsyqySsxxbVO{}",JsonUtils.toJson(zdsyqySsxxbVO));
            } else if("179,188".contains(ewbhxh)){
                if("179".equals(ewbhxh)){
                    final ZdsyqySsxxbVO ssxxbVODB = zdsyLqService.queryEwbhxhSsxx(zdsyQyxxDTO.getZlbscjuuid(),ewbhxh);
                    CwbbQykjzzybqyLrbyzxVO lrbyzxVO = lrbBqMap.get(new Long(6));
                    if(GyUtils.isNotNull(lrbyzxVO)){
                        bnljs = GyUtils.isNotNull(lrbyzxVO.getBqje())?lrbyzxVO.getBqje().divide(DECIMALDIV):BigDecimal.ZERO;
                        bnbqs = bnljs.subtract(ssxxbVODB.getBnljs());
                    }
                    lrbyzxVO = lrbSqMap.get(new Long(6));
                    if(GyUtils.isNotNull(lrbyzxVO)){
                        snljs = GyUtils.isNotNull(lrbyzxVO.getBqje())?lrbyzxVO.getBqje().divide(DECIMALDIV):BigDecimal.ZERO;
                        snbqs = GyUtils.isNotNull(lrbyzxVO.getByfse())?lrbyzxVO.getByfse().divide(DECIMALDIV):BigDecimal.ZERO;
                    }
                } else if ("188".equals(ewbhxh)) {
                    CwbbQykjzzybqyLrbyzxVO lrbyzxVO = lrbBqMap.get(new Long(8));
                    if(GyUtils.isNotNull(lrbyzxVO)){
                        bnljs = GyUtils.isNotNull(lrbyzxVO.getBqje())?lrbyzxVO.getBqje().divide(DECIMALDIV):BigDecimal.ZERO;
                    }
                    lrbyzxVO = lrbSqMap.get(new Long(6));
                    if(GyUtils.isNotNull(lrbyzxVO)){
                        snljs = GyUtils.isNotNull(lrbyzxVO.getBqje())?lrbyzxVO.getBqje().divide(DECIMALDIV):BigDecimal.ZERO;
                    }
                }
                //分公司数据相加
                if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)) {
                    for (ZdsyQyxxDTO fgsDTO : fgsList) {
                        //财报本期数据
                        CwbbSjcxReqVO fgsCwbbReqVO = new CwbbSjcxReqVO();
                        fgsCwbbReqVO.setDjxh(fgsDTO.getDjxh());
                        fgsCwbbReqVO.setSkssqq(zdsyQyxxDTO.getFirstDay());
                        fgsCwbbReqVO.setSkssqz(zdsyQyxxDTO.getLastDay());
                        fgsCwbbReqVO.setZlbsxlDm("ZL1001001");
                        CwbbSjcxResVO sjcxResVO = cwbbDwCxApi.cwbbDwcxTzsj(fgsCwbbReqVO).getData();
                        Map<Long,CwbbQykjzzybqyLrbyzxVO> fgsLrbBqMap = new HashMap<>();
                        if(GyUtils.isNotNull(sjcxResVO)){
                            QykjzzYbqyYzxVO ybqyYzxVO = sjcxResVO.getQykjzzYbqyYzxVo();
                            if(GyUtils.isNotNull(ybqyYzxVO)){
                                List<CwbbQykjzzybqyLrbyzxVO> lrbYzxVOList = ybqyYzxVO.getCwbbBsLrbYzxVOList();
                                if(GyUtils.isNotNull(lrbYzxVOList)){
                                    fgsLrbBqMap = lrbYzxVOList.stream()
                                            .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, lrbyzxVO->lrbyzxVO));
                                }
                            }
                        }
                        fgsCwbbReqVO.setSkssqz(zdsyQyxxDTO.getSqLastDay());
                        fgsCwbbReqVO.setSkssqq(zdsyQyxxDTO.getSqFirstDay());
                        CwbbSjcxResVO sqSjcxResVO = cwbbDwCxApi.cwbbDwcxTzsj(fgsCwbbReqVO).getData();
                        Map<Long,CwbbQykjzzybqyLrbyzxVO> fgsSqLrbBqMap = new HashMap<>();
                        if(GyUtils.isNotNull(sqSjcxResVO)){
                            QykjzzYbqyYzxVO ybqyYzxVO = sjcxResVO.getQykjzzYbqyYzxVo();
                            if(GyUtils.isNotNull(ybqyYzxVO)){
                                List<CwbbQykjzzybqyLrbyzxVO> lrbYzxVOList = ybqyYzxVO.getCwbbBsLrbYzxVOList();
                                if(GyUtils.isNotNull(lrbYzxVOList)){
                                    fgsSqLrbBqMap = lrbYzxVOList.stream()
                                            .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, lrbyzxVO->lrbyzxVO));
                                }
                            }
                        }
                        if("179".equals(ewbhxh)){
                            CwbbQykjzzybqyLrbyzxVO lrbyzxVO = fgsLrbBqMap.get(new Long(6));
                            if(GyUtils.isNotNull(lrbyzxVO)){
                                bnljs = bnljs.add(lrbyzxVO.getBqje().divide(DECIMALDIV));
                                bnbqs = bnbqs.add(lrbyzxVO.getByfse().divide(DECIMALDIV));
                            }
                            lrbyzxVO = fgsSqLrbBqMap.get(new Long(6));
                            if(GyUtils.isNotNull(lrbyzxVO)){
                                snljs = snljs.add(lrbyzxVO.getBqje().divide(DECIMALDIV));
                                snbqs = snbqs.add(lrbyzxVO.getByfse().divide(DECIMALDIV));
                            }
                        }else if ("188".equals(ewbhxh)) {
                            CwbbQykjzzybqyLrbyzxVO lrbyzxVO = fgsLrbBqMap.get(new Long(8));
                            if(GyUtils.isNotNull(lrbyzxVO)){
                                bnljs = bnljs.add(lrbyzxVO.getBqje().divide(DECIMALDIV));
                            }
                            lrbyzxVO = fgsSqLrbBqMap.get(new Long(8));
                            if(GyUtils.isNotNull(lrbyzxVO)){
                                snljs = snljs.add(lrbyzxVO.getBqje().divide(DECIMALDIV));
                            }
                        }
                    }
                }
                zdsyqySsxxbVO.setBnbqs(bnbqs);
                zdsyqySsxxbVO.setBnljs(bnljs);
                zdsyqySsxxbVO.setSnbqs(snbqs);
                zdsyqySsxxbVO.setSnljs(snljs);
                log.info("zdsyqySsxxbVO{}",JsonUtils.toJson(zdsyqySsxxbVO));
            }else {
                final ZdsyqySsxxbVO ssxxbVODB = zdsyLqService.queryEwbhxhSsxx(zdsyQyxxDTO.getZlbscjuuid(),ewbhxh);
                if (GyUtils.isNotNull(ssxxbVODB)) {
                    zdsyqySsxxbVO.setBnbqs(ssxxbVODB.getBnbqs());
                    zdsyqySsxxbVO.setBnljs(ssxxbVODB.getBnljs());
                    zdsyqySsxxbVO.setSnbqs(ssxxbVODB.getSnbqs());
                    zdsyqySsxxbVO.setSnljs(ssxxbVODB.getSnljs());
                }
            }
            zdsyLqService.saveZdsySsxxb(zdsyqySsxxbVO,zdsyQyxxDTO.getZlbscjuuid());
        }

        ZdsyqySsxxbVO ssxxbVODB = zdsyLqService.queryEwbhxhSsxx(zdsyQyxxDTO.getZlbscjuuid(),"147");
        bnljshj147 = bnbqshj147.divide(DECIMALDIV);
        snljshj147 = snbqshj147.divide(DECIMALDIV);
        if(GyUtils.isNotNull(ssxxbVODB)){
            bnljshj147 = ssxxbVODB.getBnljs().add(bnljshj147);
            snljshj147 = ssxxbVODB.getSnljs().add(snljshj147);
        }
        ZdsyqySsxxbVO zdsyqySsxxbVO = new ZdsyqySsxxbVO();
        zdsyqySsxxbVO.setEwbhxh("147");
        zdsyqySsxxbVO.setBnbqs(bnbqshj147.divide(DECIMALDIV));
        zdsyqySsxxbVO.setBnljs(bnljshj147);
        zdsyqySsxxbVO.setSnbqs(snbqshj147.divide(DECIMALDIV));
        zdsyqySsxxbVO.setSnljs(snljshj147);
        zdsyLqService.saveZdsySsxxb(zdsyqySsxxbVO,zdsyQyxxDTO.getZlbscjuuid());

        ssxxbVODB = zdsyLqService.queryEwbhxhSsxx(zdsyQyxxDTO.getZlbscjuuid(),"165");
        bnljshj165 = bnbqshj165.divide(DECIMALDIV);
        snljshj165 = snbqshj165.divide(DECIMALDIV);
        if(GyUtils.isNotNull(ssxxbVODB)){
            bnljshj165 = ssxxbVODB.getBnljs().add(bnljshj165);
            snljshj165 = ssxxbVODB.getSnljs().add(snljshj165);
        }
        zdsyqySsxxbVO = new ZdsyqySsxxbVO();
        zdsyqySsxxbVO.setEwbhxh("165");
        zdsyqySsxxbVO.setBnbqs(bnbqshj165.divide(DECIMALDIV));
        zdsyqySsxxbVO.setBnljs(bnljshj165);
        zdsyqySsxxbVO.setSnbqs(snbqshj165.divide(DECIMALDIV));
        zdsyqySsxxbVO.setSnljs(snljshj165);
        zdsyLqService.saveZdsySsxxb(zdsyqySsxxbVO,zdsyQyxxDTO.getZlbscjuuid());


        bnbqshj184 = bnbqshj184.add(bnbqshj147.divide(DECIMALDIV));
        bnljshj184 = bnljshj184.add(bnljshj147);
        snbqshj184 = snbqshj184.add(snbqshj147.divide(DECIMALDIV));
        snljshj184 = snljshj184.add(snljshj147);
        zdsyqySsxxbVO = new ZdsyqySsxxbVO();
        zdsyqySsxxbVO.setEwbhxh("184");
        zdsyqySsxxbVO.setBnbqs(bnbqshj184);
        zdsyqySsxxbVO.setBnljs(bnljshj184);
        zdsyqySsxxbVO.setSnbqs(snbqshj184);
        zdsyqySsxxbVO.setSnljs(snljshj184);
        zdsyLqService.saveZdsySsxxb(zdsyqySsxxbVO,zdsyQyxxDTO.getZlbscjuuid());
    }

    private void saveZdsyqyCwxx(ZdsyQyxxDTO zdsyQyxxDTO){
        log.info("==========开始财报信息归集==========");
        log.info("zdsyQyxxDTO{}",JsonUtils.toJson(zdsyQyxxDTO));
        QykjzzYbqyYzxVO qykjzzYbqyYzxVo = new QykjzzYbqyYzxVO();
        List<CwbbQykjzzybqyZcfzbyzxVO> cwbbBsZcfzbYzxVOList = new ArrayList<>();
        List<CwbbQykjzzybqyLrbyzxVO> cwbbBsLrbYzxVOList = new ArrayList<>();
        Map<Long,CwbbQykjzzybqyZcfzbyzxVO> zcfzbBqMap = new HashMap<>();
        Map<Long,CwbbQykjzzybqyZcfzbyzxVO> zcfzbSqMap = new HashMap<>();
        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbBqMap = new HashMap<>();
        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbSqMap = new HashMap<>();
        //财报本期数据
        CwbbSjcxReqVO cwbbSjcxReqVO = new CwbbSjcxReqVO();
        cwbbSjcxReqVO.setDjxh(zdsyQyxxDTO.getDjxh());
        cwbbSjcxReqVO.setSkssqq(zdsyQyxxDTO.getFirstDay());
        cwbbSjcxReqVO.setSkssqz(zdsyQyxxDTO.getLastDay());
        cwbbSjcxReqVO.setZlbsxlDm("ZL1001001");
        CwbbSjcxResVO cwbbSjcxResVO = cwbbDwCxApi.cwbbDwcxSbbsj(cwbbSjcxReqVO).getData();
        if(GyUtils.isNotNull(cwbbSjcxResVO)){
            qykjzzYbqyYzxVo = cwbbSjcxResVO.getQykjzzYbqyYzxVo();
            if(GyUtils.isNotNull(qykjzzYbqyYzxVo)){
                cwbbBsZcfzbYzxVOList = qykjzzYbqyYzxVo.getCwbbBsZcfzbYzxVOList();
                if(GyUtils.isNotNull(cwbbBsZcfzbYzxVOList)){
                    zcfzbBqMap = cwbbBsZcfzbYzxVOList.stream()
                            .collect(Collectors.toMap(CwbbQykjzzybqyZcfzbyzxVO::getEwbhxh, zcfzbyzxVO->zcfzbyzxVO));
                }
                cwbbBsLrbYzxVOList = qykjzzYbqyYzxVo.getCwbbBsLrbYzxVOList();
                if(GyUtils.isNotNull(cwbbBsLrbYzxVOList)){
                    lrbBqMap = cwbbBsLrbYzxVOList.stream()
                            .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, lrbyzxVO->lrbyzxVO));
                }
            }
        }

        //财报去年同期数据
        cwbbSjcxReqVO.setSkssqq(zdsyQyxxDTO.getSqFirstDay());
        cwbbSjcxReqVO.setSkssqz(zdsyQyxxDTO.getSqLastDay());
        cwbbSjcxResVO = cwbbDwCxApi.cwbbDwcxSbbsj(cwbbSjcxReqVO).getData();
        if(GyUtils.isNotNull(cwbbSjcxResVO)){
            qykjzzYbqyYzxVo = cwbbSjcxResVO.getQykjzzYbqyYzxVo();
            if(GyUtils.isNotNull(qykjzzYbqyYzxVo)){
                cwbbBsZcfzbYzxVOList = qykjzzYbqyYzxVo.getCwbbBsZcfzbYzxVOList();
                if(GyUtils.isNotNull(cwbbBsZcfzbYzxVOList)){
                    zcfzbSqMap = cwbbBsZcfzbYzxVOList.stream()
                            .collect(Collectors.toMap(CwbbQykjzzybqyZcfzbyzxVO::getEwbhxh, zcfzbyzxVO->zcfzbyzxVO));
                }
                cwbbBsLrbYzxVOList = qykjzzYbqyYzxVo.getCwbbBsLrbYzxVOList();
                if(GyUtils.isNotNull(cwbbBsLrbYzxVOList)){
                    lrbSqMap = cwbbBsLrbYzxVOList.stream()
                            .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, lrbyzxVO->lrbyzxVO));
                }
            }
        }

        final ZdsyqyCwxxVO zdsyqyCwxxVO = new ZdsyqyCwxxVO();
        final List<ZdsyqyCwxxLrbVO> sbZdsyqyCwxxLrb = JsonUtils.toList(SBZDSYQYCWXXLRB,ZdsyqyCwxxLrbVO.class);
        final Map<String, String> lrbEwbhxhMap = JsonUtils.toMap(CWXXLRBDZXH,String.class,String.class);
        for(ZdsyqyCwxxLrbVO lrbVO : sbZdsyqyCwxxLrb){
            final String ewbhxh = lrbEwbhxhMap.get(lrbVO.getEwbhxh());
            CwbbQykjzzybqyLrbyzxVO lrbyzxVO = lrbBqMap.get(new Long(ewbhxh));
            BigDecimal bnlj = toLrbDec(lrbyzxVO);
            lrbVO.setBnlj(GyUtils.isNotNull(bnlj)?divDecRound(bnlj):BigDecimal.ZERO);
            lrbyzxVO = lrbSqMap.get(new Long(ewbhxh));
            BigDecimal snlj = toLrbDec(lrbyzxVO);
            lrbVO.setSnlj(GyUtils.isNotNull(snlj)?divDecRound(snlj):BigDecimal.ZERO);
        }
        zdsyqyCwxxVO.setSbZdsyqyCwxxLrb(sbZdsyqyCwxxLrb);

        final List<ZdsyqyCwxxZcfzbVO> sbZdsyqyCwxxZcfzb = JsonUtils.toList(SBZDSYQYCWXXZCFZB,ZdsyqyCwxxZcfzbVO.class);
        final Map<String, String> ZcfzbZcEwbhxhMap = JsonUtils.toMap(ZCFZBZCEWBHXH,String.class,String.class);
        final Set<String> zcKey = ZcfzbZcEwbhxhMap.keySet();
        final Map<String, String> ZcfzbFzEwbhxhMap = JsonUtils.toMap(ZCFZBFZEWBHXH,String.class,String.class);
        final Set<String> fzKey = ZcfzbFzEwbhxhMap.keySet();
        for(ZdsyqyCwxxZcfzbVO zcfzbVO : sbZdsyqyCwxxZcfzb){
            log.info("zcfzbVO{}",JsonUtils.toJson(zcfzbVO));
            if("3".equals(zcfzbVO.getEwbhxh())){
                BigDecimal bnnc = BigDecimal.ZERO;
                BigDecimal bnqm = BigDecimal.ZERO;
                BigDecimal snnc = BigDecimal.ZERO;
                BigDecimal snqm = BigDecimal.ZERO;
                CwbbQykjzzybqyZcfzbyzxVO zcfzbyzxVO = zcfzbBqMap.get(new Long(14));
                log.info("zcfzbyzxVO{}",JsonUtils.toJson(zcfzbyzxVO));
                if(GyUtils.isNotNull(zcfzbyzxVO)){
                    bnnc = bnnc.add(GyUtils.isNotNull(zcfzbyzxVO.getSnnmyeZc())?zcfzbyzxVO.getSnnmyeZc():BigDecimal.ZERO);
                    bnqm = bnqm.add(GyUtils.isNotNull(zcfzbyzxVO.getQmyeZc())?zcfzbyzxVO.getQmyeZc():BigDecimal.ZERO);
                }
                zcfzbyzxVO = zcfzbBqMap.get(new Long(33));
                log.info("zcfzbyzxVO{}",JsonUtils.toJson(zcfzbyzxVO));
                if(GyUtils.isNotNull(zcfzbyzxVO)){
                    bnnc = bnnc.add(GyUtils.isNotNull(zcfzbyzxVO.getSnnmyeZc())?zcfzbyzxVO.getSnnmyeZc():BigDecimal.ZERO);
                    bnqm = bnqm.add(GyUtils.isNotNull(zcfzbyzxVO.getQmyeZc())?zcfzbyzxVO.getQmyeZc():BigDecimal.ZERO);
                }
                zcfzbyzxVO = zcfzbSqMap.get(new Long(14));
                if(GyUtils.isNotNull(zcfzbyzxVO)){
                    snnc = snnc.add(GyUtils.isNotNull(zcfzbyzxVO.getSnnmyeZc())?zcfzbyzxVO.getSnnmyeZc():BigDecimal.ZERO);
                    snqm = snqm.add(GyUtils.isNotNull(zcfzbyzxVO.getQmyeZc())?zcfzbyzxVO.getQmyeZc():BigDecimal.ZERO);
                }
                zcfzbyzxVO = zcfzbSqMap.get(new Long(33));
                if(GyUtils.isNotNull(zcfzbyzxVO)){
                    snnc = snnc.add(GyUtils.isNotNull(zcfzbyzxVO.getSnnmyeZc())?zcfzbyzxVO.getSnnmyeZc():BigDecimal.ZERO);
                    snqm = snqm.add(GyUtils.isNotNull(zcfzbyzxVO.getQmyeZc())?zcfzbyzxVO.getQmyeZc():BigDecimal.ZERO);
                }
                zcfzbVO.setBnnc(divDec(bnnc));
                zcfzbVO.setBnqm(divDec(bnqm));
                zcfzbVO.setSnnc(divDec(snnc));
                zcfzbVO.setSnqm(divDec(snqm));
            }else if(zcKey.contains(zcfzbVO.getEwbhxh())){
                final String ewbhxh = ZcfzbZcEwbhxhMap.get(zcfzbVO.getEwbhxh());
                CwbbQykjzzybqyZcfzbyzxVO zcfzbyzxVO = zcfzbBqMap.get(new Long(ewbhxh));
                BigDecimal bnnc = GyUtils.isNotNull(zcfzbyzxVO)?zcfzbyzxVO.getSnnmyeZc():BigDecimal.ZERO;
                BigDecimal bnqm = GyUtils.isNotNull(zcfzbyzxVO)?zcfzbyzxVO.getQmyeZc():BigDecimal.ZERO;
                zcfzbVO.setBnnc((GyUtils.isNotNull(bnnc)?divDec(bnnc):BigDecimal.ZERO));
                zcfzbVO.setBnqm((GyUtils.isNotNull(bnqm)?divDec(bnqm):BigDecimal.ZERO));
                zcfzbyzxVO = zcfzbSqMap.get(new Long(ewbhxh));
                BigDecimal snnc = GyUtils.isNotNull(zcfzbyzxVO)?zcfzbyzxVO.getSnnmyeZc():BigDecimal.ZERO;
                BigDecimal snqm = GyUtils.isNotNull(zcfzbyzxVO)?zcfzbyzxVO.getQmyeZc():BigDecimal.ZERO;
                zcfzbVO.setSnnc((GyUtils.isNotNull(snnc)?divDec(snnc):BigDecimal.ZERO));
                zcfzbVO.setSnqm((GyUtils.isNotNull(snqm)?divDec(snqm):BigDecimal.ZERO));
            }else if(fzKey.contains(zcfzbVO.getEwbhxh())){
                final String ewbhxh = ZcfzbFzEwbhxhMap.get(zcfzbVO.getEwbhxh());
                CwbbQykjzzybqyZcfzbyzxVO zcfzbyzxVO = zcfzbBqMap.get(new Long(ewbhxh));
                BigDecimal bnnc = GyUtils.isNotNull(zcfzbyzxVO)?zcfzbyzxVO.getSnnmyeQy():BigDecimal.ZERO;
                BigDecimal bnqm = GyUtils.isNotNull(zcfzbyzxVO)?zcfzbyzxVO.getQmyeQy():BigDecimal.ZERO;
                zcfzbVO.setBnnc((GyUtils.isNotNull(bnnc)?divDec(bnnc):BigDecimal.ZERO));
                zcfzbVO.setBnqm((GyUtils.isNotNull(bnqm)?divDec(bnqm):BigDecimal.ZERO));
                zcfzbyzxVO = zcfzbSqMap.get(new Long(ewbhxh));
                BigDecimal snnc = GyUtils.isNotNull(zcfzbyzxVO)?zcfzbyzxVO.getSnnmyeQy():BigDecimal.ZERO;
                BigDecimal snqm = GyUtils.isNotNull(zcfzbyzxVO)?zcfzbyzxVO.getQmyeQy():BigDecimal.ZERO;
                zcfzbVO.setSnnc((GyUtils.isNotNull(snnc)?divDec(snnc):BigDecimal.ZERO));
                zcfzbVO.setSnqm((GyUtils.isNotNull(snqm)?divDec(snqm):BigDecimal.ZERO));
            }
        }
        zdsyqyCwxxVO.setSbZdsyqyCwxxZcfzb(sbZdsyqyCwxxZcfzb);
        log.info("zdsyqyCwxxVO{}",JsonUtils.toJson(zdsyqyCwxxVO));
        zdsyLqService.saveZdsyqyCwxx(zdsyqyCwxxVO,zdsyQyxxDTO.getZlbscjuuid());
    }

    private BigDecimal toLrbDec(CwbbQykjzzybqyLrbyzxVO lrbyzxVO){
        return GyUtils.isNotNull(lrbyzxVO)?lrbyzxVO.getBqje():BigDecimal.ZERO;
    }

    private void saveZdsyQyjqdcwjbJqjyxx(ZdsyQyxxDTO zdsyQyxxDTO){
        final List<ZdsyqyJqdcwjbJqjyxxVO> zdsyQyjqdcwjbJqjyxx = JsonUtils.toList(ZDSYQYJQDCWJBJQJYXX,ZdsyqyJqdcwjbJqjyxxVO.class);
        BigDecimal ylsl = DECIMALYJSL;
        if(GyUtils.isNotNull(zdsyQyxxDTO.getSl1())){
            ylsl = zdsyQyxxDTO.getSl1();
        }
        final ZdsyqyJqdcwjbJqjyxxVO zdsyqyJqdcwjbJqjyxxVO = new ZdsyqyJqdcwjbJqjyxxVO();
        zdsyqyJqdcwjbJqjyxxVO.setEwbhlbm("JSXS");
        zdsyqyJqdcwjbJqjyxxVO.setEwbhxh("40");
        zdsyqyJqdcwjbJqjyxxVO.setNd(ylsl.toString());
        zdsyqyJqdcwjbJqjyxxVO.setNd1(ylsl.toString());
        zdsyqyJqdcwjbJqjyxxVO.setHmc("计算系数");
        zdsyQyjqdcwjbJqjyxx.add(zdsyqyJqdcwjbJqjyxxVO);
        zdsyLqService.saveZdsyQyjqdcwjbJqjyxx(zdsyQyjqdcwjbJqjyxx, zdsyQyxxDTO.getZlbscjuuid());
    }

    private void saveZdsyQyjqdcwjbQnyc(ZdsyQyxxDTO zdsyQyxxDTO){
        BigDecimal ylsl = DECIMALYJSL;
        if(GyUtils.isNotNull(zdsyQyxxDTO.getSl1())){
            ylsl = zdsyQyxxDTO.getSl1();
        }
        List<ZdsyQyxxDTO> fgsList = new ArrayList<>();
        if("1000".equals(zdsyQyxxDTO.getGsh1())){
            fgsList = zdsyLqService.queryZdsyFzjgQyxx(zdsyQyxxDTO.getDjxh());
        }

        //去年下期实际
        List<ZnsbNssbJksmxDO> snxxqList = znsbNssbJksmxService.querySdjeList(zdsyQyxxDTO.getDjxh(),
                this.getFirstDayOfMonth(zdsyQyxxDTO.getSnxxq()),
                this.getLastDayOfMonth(zdsyQyxxDTO.getSnxxq()));
        Map<String, BigDecimal> snxxqMap = snxxqList.stream()
                .collect(Collectors.toMap(ZnsbNssbJksmxDO::getZsxmDm, ZnsbNssbJksmxDO::getYbtse));
        log.info("上年下期实际缴款{}",JsonUtils.toJson(snxxqMap));
        //去年累计实际
        List<ZnsbNssbJksmxDO> snljList = znsbNssbJksmxService.querySdjeList(zdsyQyxxDTO.getDjxh(),
                this.getFirstDayOfMonth(zdsyQyxxDTO.getSnnc()),
                this.getLastDayOfMonth(zdsyQyxxDTO.getSnnm()));
        Map<String, BigDecimal> snljMap = snljList.stream()
                .collect(Collectors.toMap(ZnsbNssbJksmxDO::getZsxmDm, ZnsbNssbJksmxDO::getYbtse));
        log.info("去年累计实际缴款{}",JsonUtils.toJson(snljList));

        //财报去年同期数据
//        QykjzzYbqyYzxVO qykjzzYbqyYzxVo = new QykjzzYbqyYzxVO();
//        List<CwbbQykjzzybqyLrbyzxVO> cwbbBsLrbYzxVOList = new ArrayList<>();
//        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbSqMap = new HashMap<>();
//        CwbbSjcxReqVO cwbbSjcxReqVO = new CwbbSjcxReqVO();
//        cwbbSjcxReqVO.setDjxh(zdsyQyxxDTO.getDjxh());
//        cwbbSjcxReqVO.setZlbsxlDm("ZL1001001");
//        cwbbSjcxReqVO.setSszq(zdsyQyxxDTO.getSnxq());
//        CwbbSjcxResVO cwbbSjcxResVO = cwbbDwCxApi.cwbbDwcxTzsj(cwbbSjcxReqVO).getData();
//        if(GyUtils.isNotNull(cwbbSjcxResVO)){
//            qykjzzYbqyYzxVo = cwbbSjcxResVO.getQykjzzYbqyYzxVo();
//            cwbbBsLrbYzxVOList = qykjzzYbqyYzxVo.getCwbbBsLrbYzxVOList();
//            lrbSqMap = cwbbBsLrbYzxVOList.stream()
//                    .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, lrbyzxVO->lrbyzxVO));
//        }
        Map<String, BigDecimal> lrbSqMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(zdsyQyxxDTO.getGsh1(),zdsyQyxxDTO.getSnxq(),"SM03","E");
        log.info("lrbSqMap{}",JsonUtils.toJson(lrbSqMap));

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算上年年份
        int lastYear = currentDate.getYear() - 1;
        // 获取上年1月1日
        LocalDate firstDayOfLastYear = LocalDate.of(lastYear, 1, 1);
        // 获取上年12月31日
        LocalDate lastDayOfLastYear = LocalDate.of(lastYear, 12, 31);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        CwbbSjcxReqVO reqVO = new CwbbSjcxReqVO();
        reqVO.setDjxh(zdsyQyxxDTO.getDjxh());
        reqVO.setZlbsxlDm("ZL1001001");
        reqVO.setSkssqq(DateUtils.parseDate(firstDayOfLastYear.format(formatter),3));
        reqVO.setSkssqz(DateUtils.parseDate(lastDayOfLastYear.format(formatter),3));
        log.info("景气表reqVO{}",JsonUtils.toJson(reqVO));
        CwbbSjcxResVO sjcxResVO = cwbbDwCxApi.cwbbDwcxSbbsj(reqVO).getData();
        log.info("景气表sjcxResVO{}",JsonUtils.toJson(sjcxResVO));



        BigDecimal sntqsjhj42 = BigDecimal.ZERO;
        BigDecimal snsjhj42 = BigDecimal.ZERO;
        BigDecimal yychj42 = BigDecimal.ZERO;
        BigDecimal bnychj42 = BigDecimal.ZERO;

        //二维码行序号征收项目对照map
        final Map<String, String> zsxmMap = JsonUtils.toMap(ZDSYJKSMXZSXM,String.class,String.class);
        //需要保存的行
        final List<ZdsyqyJqdcwjbZyzbycVO> zdsyQyjqdcwjbQnycList = JsonUtils.toList(ZDSYQYJQDCWJBQNYC,ZdsyqyJqdcwjbZyzbycVO.class);
        for(ZdsyqyJqdcwjbZyzbycVO zyzbycVO : zdsyQyjqdcwjbQnycList){
            final String ewbhxh = zyzbycVO.getEwbhxh();
            final String zsxmDm = zsxmMap.get(ewbhxh);
            if(GyUtils.isNotNull(zsxmDm)){
                BigDecimal sntqsj = BigDecimal.ZERO;
                BigDecimal snsj = BigDecimal.ZERO;
                String[] zsxmDmList = zsxmDm.split(",");
                for(String zsxm :zsxmDmList){
                    sntqsj = sntqsj.add(toDec(snxxqMap.get(zsxm)).divide(DECIMALDIV));
                    snsj = snsj.add(toDec(snljMap.get(zsxm)).divide(DECIMALDIV));
                }
                //分公司数据相加
                if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)) {
                    for (ZdsyQyxxDTO fgsDTO : fgsList) {
                        List<ZnsbNssbJksmxDO> fgsSnxxq = znsbNssbJksmxService.querySdjeList(fgsDTO.getDjxh(),
                                this.getFirstDayOfMonth(zdsyQyxxDTO.getSnxxq()),
                                this.getLastDayOfMonth(zdsyQyxxDTO.getSnxxq()));
                        Map<String, BigDecimal> fgsSnxxqMap = fgsSnxxq.stream()
                                .collect(Collectors.toMap(ZnsbNssbJksmxDO::getZsxmDm, ZnsbNssbJksmxDO::getYbtse));
                        log.info("公司号{},上年下期实际缴款{}",fgsDTO.getGsh1(),JsonUtils.toJson(fgsSnxxqMap));
                        List<ZnsbNssbJksmxDO> fgsSnljJk = znsbNssbJksmxService.querySdjeList(fgsDTO.getDjxh(),
                                this.getFirstDayOfMonth(zdsyQyxxDTO.getSnnc()),
                                this.getLastDayOfMonth(zdsyQyxxDTO.getSnnm()));
                        Map<String, BigDecimal> fgsSnljJkMap = fgsSnljJk.stream()
                                .collect(Collectors.toMap(ZnsbNssbJksmxDO::getZsxmDm, ZnsbNssbJksmxDO::getYbtse));
                        log.info("公司号{},去年累计实际缴款{}",fgsDTO.getGsh1(),JsonUtils.toJson(fgsSnljJkMap));
                        for(String zsxm :zsxmDmList){
                            sntqsj = sntqsj.add(toDec(fgsSnxxqMap.get(zsxm)).divide(DECIMALDIV));
                            snsj = snsj.add(toDec(fgsSnljJkMap.get(zsxm)).divide(DECIMALDIV));
                        }
                    }
                }
                zyzbycVO.setYyc(sntqsj.multiply(ylsl));
                zyzbycVO.setBnyc(snsj.multiply(ylsl));
                zyzbycVO.setSnsj(snsj);
                zyzbycVO.setSntqysj(sntqsj);
                sntqsjhj42 = sntqsjhj42.add(sntqsj);
                snsjhj42 = snsjhj42.add(snsj);
                yychj42 = yychj42.add(sntqsj.multiply(ylsl));
                bnychj42 = bnychj42.add(snsj.multiply(ylsl));
            } else {
                BigDecimal yyc = BigDecimal.ZERO;
                BigDecimal sntqsj = BigDecimal.ZERO;
                BigDecimal bnyc = BigDecimal.ZERO;
                BigDecimal snsj = BigDecimal.ZERO;
                if("1,2".contains(ewbhxh)){
                    if("1".equals(ewbhxh)){
                        sntqsj = GyUtils.isNotNull(lrbSqMap.get("SM03-01PL1000F_PER"))?
                                divDecRound(lrbSqMap.get("SM03-01PL1000F_PER")):BigDecimal.ZERO;
                        yyc = sntqsj.multiply(ylsl);
                        if("01".equals(zdsyQyxxDTO.getBnxq().substring(4,6))){
                            if(GyUtils.isNotNull(sjcxResVO) && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo())
                                    && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList())){
                                List<CwbbQykjzzybqyLrbyzxVO> lrbYzxVOList = sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList();
                                Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbsbMap = lrbYzxVOList.stream()
                                        .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, qykjzzybqyLrbyzxVO->qykjzzybqyLrbyzxVO));
                                CwbbQykjzzybqyLrbyzxVO sblrbVO = lrbsbMap.get(new Long(1));
                                BigDecimal bqje = GyUtils.isNotNull(sblrbVO)?sblrbVO.getBqje():snsj;
                                snsj = (GyUtils.isNotNull(bqje)?bqje:snsj).divide(DECIMALDIV);
                                bnyc = snsj.multiply(ylsl);
                            }else {
                                snsj = GyUtils.isNotNull(lrbSqMap.get("SM03-01PL1000F_PER"))?
                                        divDecRound(lrbSqMap.get("SM03-01PL1000F_PER")):BigDecimal.ZERO;;
                                bnyc = bnyc.multiply(ylsl);
                            }
                        }else {
                            if(GyUtils.isNotNull(sjcxResVO) && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo())
                                    && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList())){
                                List<CwbbQykjzzybqyLrbyzxVO> lrbYzxVOList = sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList();
                                Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbsbMap = lrbYzxVOList.stream()
                                        .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, qykjzzybqyLrbyzxVO->qykjzzybqyLrbyzxVO));
                                CwbbQykjzzybqyLrbyzxVO sblrbVO = lrbsbMap.get(new Long(1));
                                BigDecimal bqje = GyUtils.isNotNull(sblrbVO)?sblrbVO.getBqje():snsj;
                                snsj = (GyUtils.isNotNull(bqje)?bqje:snsj).divide(DECIMALDIV);
                                bnyc = snsj.multiply(ylsl);
                            }
                        }
                    } else if ("2".equals(ewbhxh)) {
                        sntqsj = GyUtils.isNotNull(lrbSqMap.get("SM03-01PL4000F_PER"))?
                                divDecRound(lrbSqMap.get("SM03-01PL4000F_PER")):BigDecimal.ZERO;
                        yyc = sntqsj.multiply(ylsl);
                        if("01".equals(zdsyQyxxDTO.getBnxq().substring(4,6))){
                            if(GyUtils.isNotNull(sjcxResVO) && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo())
                                    && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList())){
                                List<CwbbQykjzzybqyLrbyzxVO> lrbYzxVOList = sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList();
                                Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbsbMap = lrbYzxVOList.stream()
                                        .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, qykjzzybqyLrbyzxVO->qykjzzybqyLrbyzxVO));
                                CwbbQykjzzybqyLrbyzxVO sblrbVO = lrbsbMap.get(new Long(22));
                                BigDecimal bqje = GyUtils.isNotNull(sblrbVO)?sblrbVO.getBqje():snsj;
                                snsj = (GyUtils.isNotNull(bqje)?bqje:snsj).divide(DECIMALDIV);
                                bnyc = snsj.multiply(ylsl);
                            }else {
                                snsj = GyUtils.isNotNull(lrbSqMap.get("SM03-01PL4000F_PER"))?
                                        divDecRound(lrbSqMap.get("SM03-01PL4000F_PER")):BigDecimal.ZERO;;
                                bnyc = bnyc.multiply(ylsl);
                            }
                        }else {
                            if(GyUtils.isNotNull(sjcxResVO) && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo())
                                    && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList())){
                                List<CwbbQykjzzybqyLrbyzxVO> lrbYzxVOList = sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList();
                                Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbsbMap = lrbYzxVOList.stream()
                                        .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, qykjzzybqyLrbyzxVO->qykjzzybqyLrbyzxVO));
                                CwbbQykjzzybqyLrbyzxVO sblrbVO = lrbsbMap.get(new Long(22));
                                BigDecimal bqje = GyUtils.isNotNull(sblrbVO)?sblrbVO.getBqje():snsj;
                                snsj = (GyUtils.isNotNull(bqje)?bqje:snsj).divide(DECIMALDIV);
                                bnyc = snsj.multiply(ylsl);
                            }
                        }
                    }
                    //分公司数据相加
                    if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)) {
                        for (ZdsyQyxxDTO fgsDTO : fgsList) {
                            //财报去年同期数据
                            Map<String, BigDecimal> fgsLrbMap= znsbTzzxQysdsyjtzService.queryLrbprbBySsq(fgsDTO.getGsh1(),zdsyQyxxDTO.getSnxq(),"SM03","E");
                            CwbbSjcxReqVO fgsSbReqVO = new CwbbSjcxReqVO();
                            fgsSbReqVO.setDjxh(zdsyQyxxDTO.getDjxh());
                            fgsSbReqVO.setZlbsxlDm("ZL1001001");
                            fgsSbReqVO.setSkssqq(DateUtils.parseDate(firstDayOfLastYear.format(formatter),3));
                            fgsSbReqVO.setSkssqq(DateUtils.parseDate(lastDayOfLastYear.format(formatter),3));
                            CwbbSjcxResVO fgsSbResVO = cwbbDwCxApi.cwbbDwcxSbbsj(fgsSbReqVO).getData();
                            if("1".equals(ewbhxh)){
                                sntqsj = GyUtils.isNotNull(fgsLrbMap.get("SM03-01PL1000F_PER"))?
                                        divDecRound(fgsLrbMap.get("SM03-01PL1000F_PER")):BigDecimal.ZERO;
                                yyc = sntqsj.multiply(ylsl);
                                if("01".equals(zdsyQyxxDTO.getBnxq().substring(4,6))){
                                    if(GyUtils.isNotNull(fgsSbResVO) && GyUtils.isNotNull(fgsSbResVO.getQykjzzYbqyYzxVo())
                                            && GyUtils.isNotNull(fgsSbResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList())){
                                        List<CwbbQykjzzybqyLrbyzxVO> fgsSbVOList = fgsSbResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList();
                                        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbsbMap = fgsSbVOList.stream()
                                                .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, qykjzzybqyLrbyzxVO->qykjzzybqyLrbyzxVO));
                                        CwbbQykjzzybqyLrbyzxVO sblrbVO = lrbsbMap.get(new Long(1));
                                        snsj = snsj.add((GyUtils.isNotNull(sblrbVO)?sblrbVO.getBqje():BigDecimal.ZERO).divide(DECIMALDIV));
                                        bnyc = snsj.multiply(ylsl);
                                    }else {
                                        sntqsj =  GyUtils.isNotNull(fgsLrbMap.get("SM03-01PL1000F_PER"))?
                                                divDecRound(fgsLrbMap.get("SM03-01PL1000F_PER")):BigDecimal.ZERO;
                                        bnyc = bnyc.multiply(ylsl);
                                    }
                                }else {
                                    if(GyUtils.isNotNull(sjcxResVO) && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo())
                                            && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList())){
                                        List<CwbbQykjzzybqyLrbyzxVO> fgsSbVOList = fgsSbResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList();
                                        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbsbMap = fgsSbVOList.stream()
                                                .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, qykjzzybqyLrbyzxVO->qykjzzybqyLrbyzxVO));
                                        CwbbQykjzzybqyLrbyzxVO sblrbVO = lrbsbMap.get(new Long(1));
                                        snsj = snsj.add((GyUtils.isNotNull(sblrbVO)?sblrbVO.getBqje():BigDecimal.ZERO).divide(DECIMALDIV));
                                        bnyc = snsj.multiply(ylsl);
                                    }
                                }
                            } else if ("2".equals(ewbhxh)) {
                                sntqsj =  GyUtils.isNotNull(lrbSqMap.get("SM03-01PL4000F_PER"))?
                                        divDecRound(lrbSqMap.get("SM03-01PL4000F_PER")):BigDecimal.ZERO;
                                yyc = sntqsj.multiply(ylsl);
                                if("01".equals(zdsyQyxxDTO.getBnxq().substring(4,6))){
                                    if(GyUtils.isNotNull(fgsSbResVO) && GyUtils.isNotNull(fgsSbResVO.getQykjzzYbqyYzxVo())
                                            && GyUtils.isNotNull(fgsSbResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList())){
                                        List<CwbbQykjzzybqyLrbyzxVO> fgsSbVOList = fgsSbResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList();
                                        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbsbMap = fgsSbVOList.stream()
                                                .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, qykjzzybqyLrbyzxVO->qykjzzybqyLrbyzxVO));
                                        CwbbQykjzzybqyLrbyzxVO sblrbVO = lrbsbMap.get(new Long(22));
                                        snsj = snsj.add((GyUtils.isNotNull(sblrbVO)?sblrbVO.getBqje():BigDecimal.ZERO).divide(DECIMALDIV));
                                        bnyc = snsj.multiply(ylsl);
                                    }else {
                                        sntqsj =  GyUtils.isNotNull(lrbSqMap.get("SM03-01PL4000F_PER"))?
                                                divDecRound(lrbSqMap.get("SM03-01PL4000F_PER")):BigDecimal.ZERO;
                                        bnyc = bnyc.multiply(ylsl);
                                    }
                                }else {
                                    if(GyUtils.isNotNull(sjcxResVO) && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo())
                                            && GyUtils.isNotNull(sjcxResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList())){
                                        List<CwbbQykjzzybqyLrbyzxVO> fgsSbVOList = fgsSbResVO.getQykjzzYbqyYzxVo().getCwbbBsLrbYzxVOList();
                                        Map<Long,CwbbQykjzzybqyLrbyzxVO> lrbsbMap = fgsSbVOList.stream()
                                                .collect(Collectors.toMap(CwbbQykjzzybqyLrbyzxVO::getEwbhxh, qykjzzybqyLrbyzxVO->qykjzzybqyLrbyzxVO));
                                        CwbbQykjzzybqyLrbyzxVO sblrbVO = lrbsbMap.get(new Long(22));
                                        snsj = snsj.add((GyUtils.isNotNull(sblrbVO)?sblrbVO.getBqje():BigDecimal.ZERO).divide(DECIMALDIV));
                                        bnyc = snsj.multiply(ylsl);
                                    }
                                }
                            }
                        }
                    }
                } else if ("11".contains(ewbhxh)) {
                    //科目余额表请求VO
                    final ZdsyKmyebReqVO kmyebReqVO = new ZdsyKmyebReqVO();
                    kmyebReqVO.setNsrsbh(zdsyQyxxDTO.getNsrsbh());
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getBnxq());
                    kmyebReqVO.setGsh(zdsyQyxxDTO.getGsh1());
                    kmyebReqVO.setKmbm("16010");
                    kmyebReqVO.setZcswlxDm("100");
                    KjKmyebDO kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
//                    if(GyUtils.isNotNull(kmyebDO)){
//                        yyc = yyc.add(kmyebDO.getByjffse());
//                        bnyc = bnyc.add(kmyebDO.getBnjfljs());
//                    }
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSnxq());
                    kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                    if(GyUtils.isNotNull(kmyebDO)) {
                        sntqsj = sntqsj.add(kmyebDO.getByjffse()).divide(DECIMALDIV);
                        snsj = snsj.add(kmyebDO.getBnjfljs()).divide(DECIMALDIV);
                    }
                    //分公司数据相加
                    if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)) {
                        for (ZdsyQyxxDTO fgsDTO : fgsList) {
                            kmyebReqVO.setNsrsbh(fgsDTO.getNsrsbh());
                            kmyebReqVO.setGsh(fgsDTO.getGsh1());
                            kmyebDO = kjKmyebMapper.querybyZcswlxDm(kmyebReqVO);
                            if(GyUtils.isNotNull(kmyebDO)) {
                                sntqsj = sntqsj.add(kmyebDO.getByjffse()).divide(DECIMALDIV);
                                snsj = snsj.add(kmyebDO.getBnjfljs()).divide(DECIMALDIV);
                            }
                        }
                    }
                }else if ("8".contains(ewbhxh)) {
                    //科目余额表请求VO
                    final ZdsyKmyebReqVO kmyebReqVO = new ZdsyKmyebReqVO();
                    kmyebReqVO.setNsrsbh(zdsyQyxxDTO.getNsrsbh());
                    kmyebReqVO.setGsh(zdsyQyxxDTO.getGsh1());
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSnxxq());
                    List<KjKmyebDO> snKmyeList = kjKmyebMapper.querybykmbm(kmyebReqVO);
                    Map<String, KjKmyebDO> snMap = snKmyeList.stream()
                            .collect(Collectors.toMap(KjKmyebDO::getKmbm, kjKmyebDO->kjKmyebDO));
                    KjKmyebDO yebDO = snMap.get("2221160000");
                    if(GyUtils.isNotNull(yebDO)){
                        sntqsj = sntqsj.add(yebDO.getByjffse()).divide(DECIMALDIV) ;
                        yyc = sntqsj.multiply(ylsl);
                    }
                    //全年
                    kmyebReqVO.setSszq(zdsyQyxxDTO.getSnnm());
                    kmyebReqVO.setKmbm("2221160000");
                    snKmyeList = kjKmyebMapper.querybykmbm(kmyebReqVO);
                    snMap = snKmyeList.stream()
                            .collect(Collectors.toMap(KjKmyebDO::getKmbm, kjKmyebDO->kjKmyebDO));
                    yebDO = snMap.get("2221160000");
                    if(GyUtils.isNotNull(yebDO)){
                        snsj = snsj.add(yebDO.getBnjfljs()).divide(DECIMALDIV) ;
                        bnyc = snsj.multiply(ylsl);
                    }
                    //分公司数据相加
                    if("1000".equals(zdsyQyxxDTO.getGsh1()) && GyUtils.isNotNull(fgsList)) {
                        for (ZdsyQyxxDTO fgsDTO : fgsList) {
                            kmyebReqVO.setNsrsbh(fgsDTO.getNsrsbh());
                            kmyebReqVO.setGsh(fgsDTO.getGsh1());
                            kmyebReqVO.setSszq(zdsyQyxxDTO.getSnxxq());
                            final List<KjKmyebDO> fgsYebList = kjKmyebMapper.querybykmbm(kmyebReqVO);
                            final Map<String, KjKmyebDO> fgsMap = fgsYebList.stream()
                                    .collect(Collectors.toMap(KjKmyebDO::getKmbm, kjKmyebDO->kjKmyebDO));
                            yebDO = fgsMap.get("2221160000");
                            if(GyUtils.isNotNull(yebDO)){
                                sntqsj = sntqsj.add(yebDO.getByjffse()).divide(DECIMALDIV);
                                yyc = sntqsj.multiply(ylsl);
                            }
                            //全年
                            kmyebReqVO.setSszqq(fgsDTO.getSnnc());
                            kmyebReqVO.setSszqz(fgsDTO.getSnnm());
                            kmyebReqVO.setKmbm("2221160000");
                            yebDO = kjKmyebMapper.querybyInSszq(kmyebReqVO);
                            if(GyUtils.isNotNull(yebDO)){
                                snsj = snsj.add(yebDO.getBnjfljs()).divide(DECIMALDIV) ;
                                bnyc = snsj.multiply(ylsl);
                            }
                        }
                    }
                    sntqsjhj42 = sntqsjhj42.add(sntqsj);
                    yychj42 = yychj42.add(yyc);
                    snsjhj42 = snsjhj42.add(snsj);
                    bnychj42 = bnychj42.add(bnyc);
                }
                zyzbycVO.setYyc(yyc);
                zyzbycVO.setBnyc(bnyc);
                zyzbycVO.setSnsj(snsj);
                zyzbycVO.setSntqysj(sntqsj);
            }
            zdsyLqService.saveZdsyQyjqdcwjbQnyc(zyzbycVO,zdsyQyxxDTO.getZlbscjuuid());
        }

        ZdsyqyJqdcwjbZyzbycVO zyzbycVO = new ZdsyqyJqdcwjbZyzbycVO();
        zyzbycVO.setEwbhxh("3");
        zyzbycVO.setYyc(yychj42);
        zyzbycVO.setBnyc(bnychj42);
        zyzbycVO.setSnsj(snsjhj42);
        zyzbycVO.setSntqysj(sntqsjhj42);
        zdsyLqService.saveZdsyQyjqdcwjbQnyc(zyzbycVO,zdsyQyxxDTO.getZlbscjuuid());

    }

    private String toStr(Object strObj){
        if (GyUtils.isNull(strObj)){
            return null;
        } else {
            return strObj.toString();
        }
    }

    private BigDecimal toDec(BigDecimal decimal){
        if (GyUtils.isNull(decimal)){
            return DECIMAL0;
        } else {
            return decimal;
        }
    }

    private String getLastDayOfMonth(String yearMonth) {
        // 创建日期格式化对象，指定输入的年月格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMM");
        // 创建日期格式化对象，指定输出的日期格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将输入的年月字符串解析为 YearMonth 对象
        YearMonth ym = YearMonth.parse(yearMonth, inputFormatter);
        // 获取该月的最后一天
        return ym.atEndOfMonth().format(outputFormatter);
    }

    private String getFirstDayOfMonth(String yearMonthStr) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMM");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, inputFormatter);
        LocalDate firstDay = yearMonth.atDay(1);
        return firstDay.format(outputFormatter);
    }


    private BigDecimal divDecRound(BigDecimal decimal){
        return decimal.divide(DECIMALDIV);
    }

    private BigDecimal divDec(BigDecimal decimal){
        return decimal.divide(DECIMALDIV, 2, RoundingMode.DOWN);
    }
}
