package com.css.znsb.nssb.controller.fssrsb.tysb;


import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.mapper.semir.KjKmyebMapper;
import com.css.znsb.gjss.pojo.domain.semir.KjKmyebDO;
import com.css.znsb.nssb.constants.TysbEnum;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.constants.enums.ZsxmDmEnum;
import com.css.znsb.nssb.mapper.sbrw.SbSbbDOMapper;
import com.css.znsb.nssb.pojo.dto.zzsybnsr.sbsjcx.ZzsybnsrSbsjcxResDTO;
import com.css.znsb.nssb.pojo.vo.fssrsb.newTysb.*;
import com.css.znsb.nssb.service.fssrsb.tysb.TysbNewService;
import com.css.znsb.nssb.service.zzsybnsrsb.sbsjcx.ZzsybnsrSbsjcxService;
import com.css.znsb.nssb.util.DeepCopyUtil;
import com.css.znsb.nssb.util.TysbGyUtils;
import com.css.znsb.tzzx.mapper.fssrtysb.ZnsbTzzxFssrtysbMapper;
import com.css.znsb.tzzx.pojo.domain.tzzx.fssrtysb.ZnsbTzzxFssrtysbDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Tag(name = "通用申报")
@RestController
@RequestMapping("/fssrsb/tysbNew")
@Validated
@Slf4j
public class TysbNewController {

    @Resource
    private TysbNewService tysbNewService;
    @Resource
    private ZnsbTzzxFssrtysbMapper znsbTzzxFssrtysbMapper;
    @Resource
    private SbSbbDOMapper sbbDOMapper;
    @Resource
    private ZzsybnsrSbsjcxService zzsybnsrSbsjcxService;
    @Resource
    private KjKmyebMapper kjKmyebMapper;

    @Operation(summary = "初始化前置")
    @PostMapping("/v1/initQz")
    public CommonResult<Object> initTysbData(@RequestBody TysbGyReqVO reqVO) {
        Map<String, Object> reqVOMap = JsonUtils.toMap(reqVO.getJsonStr());
        CommonResult<Object> resultCOMETPROACTOR = tysbNewService.invokeSjjh(TysbEnum.COMETPROACTOR.getDm(), reqVO.getJsonStr(), reqVO);
        List<Map<String, Object>> resList = new ArrayList<>();
        if ("1".equals(String.valueOf(resultCOMETPROACTOR.getCode()))) {
            final Map<String, Object> dataMap = (Map<String, Object>) resultCOMETPROACTOR.getData();
            final String SssqQ = String.valueOf(dataMap.get("SssqQ"));
            final String SssqZ = String.valueOf(dataMap.get("SssqZ"));
            Map<String, String> rcMap = new HashMap<>();
            rcMap.put("SssqQ", SssqQ);
            rcMap.put("SssqZ", SssqZ);
            final String jsonStr2 = JsonUtils.toJson(rcMap);
            CommonResult<Object> resultLISTNSQXDM = tysbNewService.invokeSjjh(TysbEnum.LISTNSQXDM.getDm(), jsonStr2, reqVO);
            if ("1".equals(String.valueOf(resultLISTNSQXDM.getCode()))) {
                final Map<String, Object> data1Map = (Map<String, Object>) resultLISTNSQXDM.getData();
                Object body = data1Map.get("Body");
                resList = JsonUtils.toMapList(String.valueOf(body));
                for (Map<String, Object> res : resList) {
                    Map<String, String> jkMap = new HashMap<>();
                    jkMap.put("ZsxmDm", String.valueOf(reqVOMap.get("ZsxmDm")));
                    jkMap.put("SssqQ", String.valueOf(res.get("skssqq")));
                    jkMap.put("SssqZ", String.valueOf(res.get("skssqz")));
                    jkMap.put("NsqxDm", String.valueOf(res.get("value")));
                    final String jsonStrjk = JsonUtils.toJson(jkMap);
                    CommonResult<Object> resultVERIFYBEGIN = tysbNewService.invokeSjjh(TysbEnum.VERIFYBEGIN.getDm(), jsonStrjk, reqVO);
                    if ("1".equals(String.valueOf(resultVERIFYBEGIN.getCode()))) {
                        final Map<String, Object> datajkMap = (Map<String, Object>) resultVERIFYBEGIN.getData();
                        Object jkbody = datajkMap.get("Body");
                        List<Map<String, Object>> resJk = JsonUtils.toMapList(String.valueOf(jkbody));
                        for (Map<String, Object> jk : resJk) {
                            if (!"tg".equals(String.valueOf(jk.get("tslxBm"))) && "重复申报监控".equals(String.valueOf(jk.get("jkxmlxMc")))) {
                                return CommonResult.error(-1, String.valueOf(jk.get("tsxx")));
                            }
                        }
                    }
                }
                return CommonResult.success(resList);
            } else {
//                return CommonResult.error(-1, resultLISTNSQXDM.getMsg());
                return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
            }
        } else {
//            return CommonResult.error(-1, resultCOMETPROACTOR.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }

    }

    @Operation(summary = "初始化页面数据")
    @PostMapping("/v1/initData")
    public CommonResult<TysbResVO> initData(@RequestBody TysbGyReqVO reqVO) {
        TysbResVO resVO = new TysbResVO();
        final String bq = reqVO.getBq();
        Map<String, Object> reqVOMap = JsonUtils.toMap(reqVO.getJsonStr());
        Map<String, String> jkMap = new HashMap<>();
        jkMap.put("ZsxmDm", String.valueOf(reqVOMap.get("ZsxmDm")));
        jkMap.put("SssqQ", String.valueOf(reqVOMap.get("SssqQ")));
        jkMap.put("SssqZ", String.valueOf(reqVOMap.get("SssqZ")));
        jkMap.put("NsqxDm", String.valueOf(reqVOMap.get("NsqxDm")));
        final String jsonStrjk = JsonUtils.toJson(jkMap);
        CommonResult<Object> resultVERIFYBEGIN = tysbNewService.invokeSjjh(TysbEnum.VERIFYBEGIN.getDm(), jsonStrjk, reqVO);
        if ("1".equals(String.valueOf(resultVERIFYBEGIN.getCode())) && !"1".equals(bq)) {
            final Map<String, Object> datajkMap = (Map<String, Object>) resultVERIFYBEGIN.getData();
            Object jkbody = datajkMap.get("Body");
            List<Map<String, Object>> resJk = JsonUtils.toMapList(String.valueOf(jkbody));
            for (Map<String, Object> jk : resJk) {
                if (!"tg".equals(String.valueOf(jk.get("tslxBm"))) && "重复申报监控".equals(String.valueOf(jk.get("jkxmlxMc")))) {
                    return CommonResult.error(-1, String.valueOf(jk.get("tsxx")));
                }
            }
        }

        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.INITDATA.getDm(), reqVO.getJsonStr(), reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            final Map<String, Object> dataMap = (Map<String, Object>) result.getData();
            Object body = dataMap.get("Body");
            final Map<String, Object> bodyMap = JsonUtils.toMap(String.valueOf(body));
            // fq_
            Map<String, Object> fqMap = (Map<String, Object>) bodyMap.get("fq_");
            final String fqSbrq = String.valueOf(fqMap.get("sbrq"));
            Map<String, Object> slrxxMap = (Map<String, Object>) fqMap.get("slrxx");
            Map<String, Object> nsrjbxxMap = (Map<String, Object>) fqMap.get("nsrjbxx");
            Map<String, Object> smzxxMap = (Map<String, Object>) fqMap.get("smzxx");
            Map<String, Object> sbxxGrid = (Map<String, Object>) fqMap.get("sbxxGrid");
            List<Map<String, Object>> sbxxGridlbVOList = (List<Map<String, Object>>) sbxxGrid.get("sbxxGridlbVO");
            //ht_
            Map<String, Object> htMap = (Map<String, Object>) bodyMap.get("ht_");
            Map<String, Object> fxmtysbbdVOMap = (Map<String, Object>) htMap.get("fxmtysbbdVO");
            Map<String, Object> fxmtySbbMap = (Map<String, Object>) fxmtysbbdVOMap.get("fxmtySbb");
            Map<String, Object> sbbheadMap = (Map<String, Object>) fxmtySbbMap.get("sbbhead");
            TysbSbbheadVO sbbhead = BeanUtils.toBean(sbbheadMap, TysbSbbheadVO.class);

            TysbSlrxxFormVO slrxxForm = new TysbSlrxxFormVO();
            slrxxForm.setBsr(String.valueOf(smzxxMap.get("xm")));
            slrxxForm.setBlrysfzjlxDm(String.valueOf(smzxxMap.get("zjlx")));
            String sfzjlx = CacheUtils.dm2mc("dm_gy_sfzjlx", String.valueOf(smzxxMap.get("zjlx")));
            slrxxForm.setBlrysfzjlx(sfzjlx);
            slrxxForm.setBlrysfzjhm(String.valueOf(smzxxMap.get("zjhm")));
            slrxxForm.setSlrq(fqSbrq);
            slrxxForm.setSlrMc(String.valueOf(slrxxMap.get("slrMc")));
            slrxxForm.setSlrDm(String.valueOf(slrxxMap.get("slrDm")));
            slrxxForm.setSlswjgDm(String.valueOf(nsrjbxxMap.get("slswjgDm")));
            slrxxForm.setSlswjgMc(String.valueOf(nsrjbxxMap.get("slswjgMc")));
            List<TysbSbxxGridVO> sbxxGridlb = new ArrayList<>();
            //0为正常初始化，其他为申报更正或申报任务查看初始化
            if ("0".equals(bq)) {
                sbxxGridlb = sbxxGridlbVOList.stream().map(o -> {
                    TysbSbxxGridVO sbxxGridVO = BeanUtils.toBean(o, TysbSbxxGridVO.class);
                    List<ZnsbTzzxFssrtysbDO> fssrtysbDOList = znsbTzzxFssrtysbMapper.queryFssrTysbList(reqVO.getDjxh(), reqVO.getNsrsbh(), DateUtils.strToDate(sbxxGridVO.getSkssqq()), DateUtils.strToDate(sbxxGridVO.getSkssqz()), sbxxGridVO.getZsxmDm());

                    sbxxGridVO.setYsx(TysbGyUtils.convertToBigDecimal(o.get("xssr")));
                    if (GyUtils.isNotNull(fssrtysbDOList)) {
                        for (ZnsbTzzxFssrtysbDO fssrtysbDO : fssrtysbDOList) {
                            if (sbxxGridVO.getSkssqq().equals(DateUtils.dateToString(fssrtysbDO.getSkssqq(), 3))
                                    && sbxxGridVO.getSkssqz().equals(DateUtils.dateToString(fssrtysbDO.getSkssqz(), 3))
                                    && sbxxGridVO.getZsxmDm().equals(fssrtysbDO.getZsxmDm()) && sbxxGridVO.getZspmDm().equals(fssrtysbDO.getZspmDm())
                                    && sbxxGridVO.getZszmDm().equals(GyUtils.isNull(fssrtysbDO.getZszmDm()) ? "" : fssrtysbDO.getZszmDm())) {
                                if (BigDecimal.ZERO.compareTo(sbxxGridVO.getYsx()) >= 0) {
                                    sbxxGridVO.setYsx(fssrtysbDO.getJsyj());
                                }
                            }
                        }
                    }

                    //标版+大连重工一户特殊
                    if (ZsxmDmEnum.SLJSJJ.getCode().equals(sbxxGridVO.getZsxmDm())) {
                        ZzsybnsrSbsjcxResDTO sjcxVo = zzsybnsrSbsjcxService.queryZzsybnsrYbtseAndXse(reqVO.getDjxh(), String.valueOf(reqVOMap.get("SssqQ")), String.valueOf(reqVOMap.get("SssqZ")));
                        if (GyUtils.isNotNull(sjcxVo) && !"10211502010000202509".equals(reqVO.getDjxh())) {
                            sbxxGridVO.setYsx(sjcxVo.getXse());
                        } else {
                            sbxxGridVO.setYsx(sjcxVo.getYbtse());
                            sbxxGridVO.setXse(sjcxVo.getXse());
                        }
                    }

                    final String jtbmSys = CacheUtils.getXtcs("GY00000001");
//                    if ("000002".equals(jtbmSys)) {
//                        if (ZsxmDmEnum.SLJSJJ.getCode().equals(sbxxGridVO.getZsxmDm())) {
//                            ZzsybnsrSbsjcxResDTO sjcxVo = zzsybnsrSbsjcxService.queryZzsybnsrYbtseAndXse(reqVO.getDjxh(), String.valueOf(reqVOMap.get("SssqQ")), String.valueOf(reqVOMap.get("SssqZ")));
////                            List<SbSbbDO> sbsbbList = sbbDOMapper.queryYbrSbbxx(reqVO.getDjxh(), String.valueOf(reqVOMap.get("SssqQ")), String.valueOf(reqVOMap.get("SssqZ")));
//                            if (GyUtils.isNotNull(sjcxVo)) {
//                                sbxxGridVO.setYsx(sjcxVo.getYbtse());
//                                sbxxGridVO.setXse(sjcxVo.getXse());
//                            }
//                        }
//                    }
                    //森马环境
                    if ("000001".equals(jtbmSys) && ZsxmDmEnum.GHJF.getCode().equals(sbxxGridVO.getZsxmDm()) && ("06".equals(String.valueOf(reqVOMap.get("NsqxDm"))) || "08".equals(String.valueOf(reqVOMap.get("NsqxDm"))))) {
                        List<String> syYfList = TysbGyUtils.extractSmartMonths(String.valueOf(reqVOMap.get("SssqQ")), String.valueOf(reqVOMap.get("SssqZ")));
                        KjKmyebDO kmyebDO = kjKmyebMapper.queryMxForTysb(reqVO.getNsrsbh(), syYfList);
                        if (GyUtils.isNotNull(kmyebDO)) {
                            sbxxGridVO.setYsx(GyUtils.isNotNull(kmyebDO.getByjffse()) ? kmyebDO.getByjffse() : BigDecimal.ZERO);
                        }
                    }

                    sbxxGridVO.setJcx(TysbGyUtils.convertToBigDecimal(o.get("kce")));
                    sbxxGridVO.setJsfyj("0".equals(o.get("jsyj")) ? BigDecimal.ONE : TysbGyUtils.convertToBigDecimal(o.get("jsyj")));
                    sbxxGridVO.setBqynsfe(TysbGyUtils.convertToBigDecimal(o.get("ynse")));
                    sbxxGridVO.setSflhdwse(TysbGyUtils.convertToBigDecimal(o.get("sl")));
                    Map<String, Object> zspmGrid = (Map<String, Object>) o.get("zspmGrid");
                    if (GyUtils.isNotNull(zspmGrid)) {
                        sbxxGridVO.setZspmGridlb((List<Map<String, Object>>) zspmGrid.get("zspmGridlb"));
                    }
                    Map<String, Object> zszmGrid = (Map<String, Object>) o.get("zszmGrid");
                    if (GyUtils.isNotNull(zszmGrid)) {
                        sbxxGridVO.setZszmGridlb((List<Map<String, Object>>) zszmGrid.get("zszmGridlb"));
                    }

                    if (GyUtils.isNotNull(o.get("option"))) {
                        sbxxGridVO.setJmxzList((List<Map<String, Object>>) o.get("option"));
                    }
                    return sbxxGridVO;
                }).collect(Collectors.toList());
            } else {
                sbxxGridlb = sbxxGridlbVOList.stream().map(o -> {
                    TysbSbxxGridVO sbxxGridVO = BeanUtils.toBean(o, TysbSbxxGridVO.class);
                    sbxxGridVO.setNsqxDm(String.valueOf(o.get("t_nsqxDm")));
                    Map<String, Object> zspmGrid = (Map<String, Object>) o.get("zspmGrid");
                    if (GyUtils.isNotNull(zspmGrid)) {
                        sbxxGridVO.setZspmGridlb((List<Map<String, Object>>) zspmGrid.get("zspmGridlb"));
                    }
                    Map<String, Object> zszmGrid = (Map<String, Object>) o.get("zszmGrid");
                    if (GyUtils.isNotNull(zszmGrid)) {
                        sbxxGridVO.setZszmGridlb((List<Map<String, Object>>) zszmGrid.get("zszmGridlb"));
                    }

                    if (GyUtils.isNotNull(o.get("option"))) {
                        sbxxGridVO.setJmxzList((List<Map<String, Object>>) o.get("option"));
                    }
                    return sbxxGridVO;
                }).collect(Collectors.toList());
            }

            resVO.setYsqxxid(String.valueOf(dataMap.get("Ysqxxid")));
            resVO.setSbbhead(sbbhead);
            resVO.setSlrxxForm(slrxxForm);
            resVO.setSbxxGridlb(sbxxGridlb);
            resVO.setJsonStr(JsonUtils.toJson(bodyMap));
            return CommonResult.success(resVO);
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "获取减免性质")
    @PostMapping("/v1/listJmxz")
    public CommonResult<Object> listJmxz(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.LISTJMXZ.getDm(), reqVO.getJsonStr(), reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "getZsxm1")
    @PostMapping("/v1/getZsxm1")
    public CommonResult<Object> getZsxm1(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.DM_GY_ZSXM1.getDm(), "", reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "getZspm")
    @PostMapping("/v1/getZspm")
    public CommonResult<Object> getZspm(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.DM_GY_ZSPM.getDm(), "", reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "getZszm")
    @PostMapping("/v1/getZszm")
    public CommonResult<Object> getZszm(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.DM_GY_ZSZM.getDm(), "", reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "getLslfDm")
    @PostMapping("/v1/getLslfDm")
    public CommonResult<Object> getLslfDm(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.DM_GY_LSLFDM.getDm(), "", reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "getSfzjlx")
    @PostMapping("/v1/getSfzjlx")
    public CommonResult<Object> getSfzjlx(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.DM_GY_SFZJLX.getDm(), "", reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "getPhjmxz")
    @PostMapping("/v1/getPhjmxz")
    public CommonResult<Object> getPhjmxz(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.PHJMXZ.getDm(), "", reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "getSbxx")
    @PostMapping("/v1/getSbxx")
    public CommonResult<Object> getSbxx(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.invokeSjjh(TysbEnum.GETSBXX.getDm(), reqVO.getJsonStr(), reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }


    @Operation(summary = "提交申报")
    @PostMapping("/v1/submitForm")
    public CommonResult<Object> submitForm(@RequestBody TysbBcReqVO reqVO) {
        TysbResVO tysbBc = reqVO.getTysbBcVO();
        final String jsonStr = tysbBc.getJsonStr();
        final Map<String, Object> dataMap = JsonUtils.toMap(jsonStr);
        Map<String, Object> htMap = (Map<String, Object>) dataMap.get("ht_");
        Map<String, Object> fxmtysbbdVOMap = new HashMap<>();
        Map<String, Object> fxmtySbbMap = new HashMap<>();
        Map<String, Object> sbbhead = new HashMap<>();
        Map<String, Object> slrxxForm = new HashMap<>();
        Map<String, Object> sbxxGrid = new HashMap<>();
        List<Map<String, Object>> sbxxGridlb = new ArrayList<>();
        List<Map<String, Object>> sbxxGridlbSb = new ArrayList<>();

        TysbSlrxxFormVO slrxxFormBd = tysbBc.getSlrxxForm();
        TysbSbbheadVO sbbheadBd = tysbBc.getSbbhead();
        List<TysbSbxxGridVO> sbxxGridlbBd = tysbBc.getSbxxGridlb();
        slrxxForm.put("bsr", slrxxFormBd.getBsr());
        slrxxForm.put("blrysfzjlxDm", slrxxFormBd.getBlrysfzjlxDm());
        slrxxForm.put("blrysfzjhm", slrxxFormBd.getBlrysfzjhm());
        slrxxForm.put("slrq", slrxxFormBd.getSlrq());
        slrxxForm.put("t_slrMc", slrxxFormBd.getSlrMc());
        slrxxForm.put("slrDm", slrxxFormBd.getSlrDm());
        slrxxForm.put("slswjgDm", slrxxFormBd.getSlswjgDm());
        slrxxForm.put("t_slswjgMc", slrxxFormBd.getSlswjgMc());
        sbbhead = BeanUtils.toMap(sbbheadBd);

        Map<String, Object> ysqxx = new HashMap<>();
        List<Map<String, Object>> sbmxlist = new ArrayList<>();

        for (TysbSbxxGridVO tysbBcBd : sbxxGridlbBd) {
            Map<String, Object> sbxxGridlbMap = new HashMap<>();
            sbxxGridlbMap.put("zsxmDm", tysbBcBd.getZsxmDm());
            sbxxGridlbMap.put("zspmDm", tysbBcBd.getZspmDm());
            sbxxGridlbMap.put("zszmDm", tysbBcBd.getZszmDm());
            sbxxGridlbMap.put("t_sjjyyf", tysbBcBd.getSjjyyf());
            sbxxGridlbMap.put("sfkssqq", tysbBcBd.getSkssqq());
            sbxxGridlbMap.put("sfkssqz", tysbBcBd.getSkssqz());
            sbxxGridlbMap.put("ysx", tysbBcBd.getYsx());
            sbxxGridlbMap.put("jcx", tysbBcBd.getJcx());
            sbxxGridlbMap.put("jsfyj", tysbBcBd.getJsfyj());
            sbxxGridlbMap.put("yssdl", tysbBcBd.getYssdl());
            sbxxGridlbMap.put("sskcs", tysbBcBd.getSskcs());
            sbxxGridlbMap.put("bqynsfe", tysbBcBd.getBqynsfe());
            sbxxGridlbMap.put("bqyjsfe", tysbBcBd.getBqyjsfe());
            sbxxGridlbMap.put("bqybtsfe", tysbBcBd.getBqybtsfe());
            sbxxGridlbMap.put("sflhdwse", tysbBcBd.getSflhdwse());
            sbxxGridlbMap.put("rdzsuuid", TysbGyUtils.convertToBigDecimal(tysbBcBd.getRdzsuuid()));
            sbxxGridlbMap.put("rdpzuuid", tysbBcBd.getRdpzuuid());
            sbxxGridlbMap.put("t_hdynsjye", tysbBcBd.getHdynsjye());
            sbxxGridlbMap.put("t_zfsbz", TysbGyUtils.convertToBigDecimal(tysbBcBd.getZfsbz()));
            sbxxGridlbMap.put("t_hyDm", tysbBcBd.getHyDm());
            sbxxGridlbMap.put("ysbse", tysbBcBd.getYsbse());
            sbxxGridlbMap.put("wdqzdbz", tysbBcBd.getWdqzdbz());
            sbxxGridlbMap.put("ssjmxzDm", tysbBcBd.getSsjmxzDm());
            sbxxGridlbMap.put("t_phjmBz", tysbBcBd.getPhjmBz());
            sbxxGridlbMap.put("t_ssjmxzMc", tysbBcBd.getSsjmxzMc());
            sbxxGridlbMap.put("t_nsqxDm", tysbBcBd.getNsqxDm());
            sbxxGridlbMap.put("t_checkedbz", tysbBcBd.getCheckedbz());
            sbxxGridlbMap.put("bqjmsfe", tysbBcBd.getBqjmsfe());
            sbxxGridlbMap.put("bqsfsyxgmyhzc", tysbBcBd.getBqsfsyxgmyhzc());
            sbxxGridlbMap.put("t_sftj", tysbBcBd.getSftj());
            sbxxGridlbMap.put("xh", TysbGyUtils.convertToBigDecimal(tysbBcBd.getXh()));
            sbxxGridlbMap.put("zszmMc", tysbBcBd.getZszmMc());
            sbxxGridlbMap.put("zsxmMc", tysbBcBd.getZsxmMc());
            sbxxGridlbMap.put("zspmMc", tysbBcBd.getZspmMc());
            sbxxGridlbMap.put("phjmse", tysbBcBd.getPhjmse());
            sbxxGridlbMap.put("t_changed", tysbBcBd.getChanged());
            sbxxGridlbMap.put("t_isXsjm", tysbBcBd.getIsXsjm());
            sbxxGridlb.add(sbxxGridlbMap);

            Map<String, Object> sbxxGridlbSbMap = new HashMap<>();
            sbxxGridlbSbMap.put("zsxmDm", tysbBcBd.getZsxmDm());
            sbxxGridlbSbMap.put("zspmDm", tysbBcBd.getZspmDm());
            sbxxGridlbSbMap.put("zszmDm", tysbBcBd.getZszmDm());
            sbxxGridlbSbMap.put("t_sjjyyf", tysbBcBd.getSjjyyf());
            sbxxGridlbSbMap.put("sfkssqq", tysbBcBd.getSkssqq());
            sbxxGridlbSbMap.put("sfkssqz", tysbBcBd.getSkssqz());
            sbxxGridlbSbMap.put("ysx", tysbBcBd.getYsx());
            sbxxGridlbSbMap.put("jcx", tysbBcBd.getJcx());
            sbxxGridlbSbMap.put("jsfyj", tysbBcBd.getJsfyj());
            sbxxGridlbSbMap.put("yssdl", tysbBcBd.getYssdl());
            sbxxGridlbSbMap.put("sskcs", tysbBcBd.getSskcs());
            sbxxGridlbSbMap.put("bqynsfe", tysbBcBd.getBqynsfe());
            sbxxGridlbSbMap.put("bqyjsfe", tysbBcBd.getBqyjsfe());
            sbxxGridlbSbMap.put("bqybtsfe", tysbBcBd.getBqybtsfe());
            sbxxGridlbSbMap.put("sflhdwse", tysbBcBd.getSflhdwse());
            sbxxGridlbSbMap.put("rdzsuuid", TysbGyUtils.convertToBigDecimal(tysbBcBd.getRdzsuuid()));
            sbxxGridlbSbMap.put("rdpzuuid", tysbBcBd.getRdpzuuid());
            sbxxGridlbSbMap.put("t_hdynsjye", tysbBcBd.getHdynsjye());
            sbxxGridlbSbMap.put("t_zfsbz", TysbGyUtils.convertToBigDecimal(tysbBcBd.getZfsbz()));
            sbxxGridlbSbMap.put("t_hyDm", tysbBcBd.getHyDm());
            sbxxGridlbSbMap.put("ysbse", tysbBcBd.getYsbse());
            sbxxGridlbSbMap.put("wdqzdbz", tysbBcBd.getWdqzdbz());
            sbxxGridlbSbMap.put("ssjmxzDm", tysbBcBd.getSsjmxzDm());
            sbxxGridlbSbMap.put("t_phjmBz", tysbBcBd.getPhjmBz());
            sbxxGridlbSbMap.put("t_ssjmxzMc", tysbBcBd.getSsjmxzMc());
            sbxxGridlbSbMap.put("t_nsqxDm", tysbBcBd.getNsqxDm());
            sbxxGridlbSbMap.put("t_checkedbz", tysbBcBd.getCheckedbz());
            sbxxGridlbSbMap.put("bqjmsfe", tysbBcBd.getBqjmsfe());
            sbxxGridlbSbMap.put("bqsfsyxgmyhzc", tysbBcBd.getBqsfsyxgmyhzc());
            sbxxGridlbSbMap.put("t_sftj", tysbBcBd.getSftj());
            sbxxGridlbSbMap.put("xh", TysbGyUtils.convertToBigDecimal(tysbBcBd.getXh()));
            sbxxGridlbSbMap.put("zszmMc", tysbBcBd.getZszmMc());
            sbxxGridlbSbMap.put("zsxmMc", tysbBcBd.getZsxmMc());
            sbxxGridlbSbMap.put("zspmMc", tysbBcBd.getZspmMc());
            sbxxGridlbSbMap.put("phjmse", tysbBcBd.getPhjmse());
            sbxxGridlbSbMap.put("t_changed", tysbBcBd.getChanged());
            sbxxGridlbSbMap.put("t_isXsjm", tysbBcBd.getIsXsjm());
            sbxxGridlbSbMap.put("t_pdf_zsxmMc", tysbBcBd.getZsxmMc());
            sbxxGridlbSbMap.put("t_pdf_zspmMc", tysbBcBd.getZspmMc());
            sbxxGridlbSbMap.put("t_sftj", "Y");

            sbxxGridlbSbMap.put("t_pdf_ssjmxzMc", tysbBcBd.getSsjmxzMc());
            sbxxGridlbSb.add(sbxxGridlbSbMap);

            Map<String, Object> sbmx = new HashMap<>();
            sbmx.put("zsxmDm", tysbBcBd.getZsxmDm());
            sbmx.put("zspmDm", tysbBcBd.getZspmDm());
            sbmx.put("ybtse", tysbBcBd.getBqybtsfe());
            sbmxlist.add(sbmx);
        }
        sbxxGrid.put("t_allSelected", tysbBc.getAllSelected());
        sbxxGrid.put("t_qylxxzBz", tysbBc.getQylxxzBz());
        sbxxGrid.put("t_bqynsfehjYgx", tysbBc.getBqynsfehjYgx());
        sbxxGrid.put("t_bqjmsfehjYgx", tysbBc.getBqjmsfehjYgx());
        sbxxGrid.put("t_bqyjsfehjYgx", tysbBc.getBqyjsfehjYgx());
        sbxxGrid.put("t_bqybtsfehjYgx", tysbBc.getBqybtsfehjYgx());
        sbxxGrid.put("t_allSelectedFunc", tysbBc.getAllSelectedFunc());
        sbxxGrid.put("t_bqynsfehj", tysbBc.getBqynsfehj());
        sbxxGrid.put("t_bqjmsfehj", tysbBc.getBqjmsfehj());
        sbxxGrid.put("t_bqyjsfehj", tysbBc.getBqyjsfehj());
        sbxxGrid.put("t_bqybtsfehj", tysbBc.getBqybtsfehj());
        sbxxGrid.put("t_jsfyjhj", tysbBc.getJsfyjhj());
        sbxxGrid.put("t_ysxhj", tysbBc.getYsxhj());
        sbxxGrid.put("t_phjmsehj", tysbBc.getPhjmsehj());
        sbxxGrid.put("t_jsfyjHjYgx", tysbBc.getJsfyjHjYgx());
        sbxxGrid.put("t_phjmseHjYgx", tysbBc.getPhjmseHjYgx());
        sbxxGrid.put("sbxxGridlb", sbxxGridlb);


        fxmtySbbMap.put("sbbhead", sbbhead);
        fxmtySbbMap.put("slrxxForm", slrxxForm);
        fxmtySbbMap.put("sbxxGrid", sbxxGrid);
        fxmtysbbdVOMap.put("fxmtySbb", fxmtySbbMap);
        htMap.replace("fxmtysbbdVO", fxmtysbbdVOMap);
        dataMap.replace("ht_", htMap);
        Map<String, Object> wbcshSb = new HashMap<>();
        dataMap.put("wbcsh", wbcshSb);

        Map<String, Object> sbMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            sbMap.put(entry.getKey(), DeepCopyUtil.deepCopy(entry.getValue()));
        }

        Map<String, Object> htSb = (Map<String, Object>) sbMap.get("ht_");
        Map<String, Object> fxmtysbbdVOSbMap = (Map<String, Object>) htSb.get("fxmtysbbdVO");
        Map<String, Object> fxmtySbbSbMap = (Map<String, Object>) fxmtysbbdVOSbMap.get("fxmtySbb");
        Map<String, Object> sbxxGridSbMap = (Map<String, Object>) fxmtySbbSbMap.get("sbxxGrid");
        sbxxGridSbMap.replace("sbxxGridlb", sbxxGridlbSb);

        Map<String, Object> kzSb = (Map<String, Object>) sbMap.get("kz_");
        Map<String, Object> ysqxxSbMap = new HashMap<>();
        ysqxxSbMap.put("yzpzzlDm", YzpzzlEnum.TYSB.getDm());
        ysqxxSbMap.put("yzpzmc", "税（费）通用申报表");
        ysqxxSbMap.put("sbmxlist", sbmxlist);
        kzSb.put("ysqxx", ysqxxSbMap);
        Map<String, Object> sbxx = new HashMap<>();
        sbxx.put("SssqQ", sbbheadBd.getSkssqq());
        sbxx.put("SssqZ", sbbheadBd.getSkssqz());
        sbxx.put("SwjgDm", "");
        sbxx.put("Ysqxxid", tysbBc.getYsqxxid());
        sbxx.put("Zcbw", JsonUtils.toJson(dataMap));
        sbxx.put("Dclbw", JsonUtils.toJson(sbMap));
        sbxx.put("Qdlx", "0");
        sbxx.put("Bsms", "0000");
        sbxx.put("Ywbm", "tysb");
        String bq = "0";
        if (GyUtils.isNotNull(tysbBc.getSbuuid()) && GyUtils.isNotNull(tysbBc.getPzxh())) {
            sbxx.put("Sbuuid", tysbBc.getSbuuid());
            sbxx.put("Pzxh", tysbBc.getPzxh());
            bq = "1";
        }
        final String tjJsonStr = JsonUtils.toJson(sbxx);
        CommonResult<Object> result = tysbNewService.invokeBcSjjh(TysbEnum.SUBMITFORM.getDm(), tjJsonStr, reqVO);
        if ("1".equals(String.valueOf(result.getCode()))) {
            tysbNewService.clsbrwSb(sbxxGridlbBd, reqVO.getDjxh(), reqVO.getNsrsbh(), reqVO.getNsrmc(), reqVO.getXzqhszDm(), bq);
            return result;
        } else {
//            return CommonResult.error(-1, result.getMsg());
            return CommonResult.error(-1, "与税务局通信失败，请稍后再试！");
        }
    }

    @Operation(summary = "获取申报结果")
    @PostMapping("/v1/getSbResult")
    public CommonResult<Object> getSbResult(@RequestBody TysbGyReqVO reqVO) {
        CommonResult<Object> result = tysbNewService.getSbResult(reqVO);
        if (!"1".equals(String.valueOf(result.getCode())) && !"2".equals(String.valueOf(result.getCode()))) {
            tysbNewService.saveRzb(reqVO);
            return CommonResult.success("成功");
        } else if ("2".equals(String.valueOf(result.getCode()))) {
            return CommonResult.success(result.getMsg());
        } else {
            return CommonResult.success("成功");
        }

    }


    @Operation(summary = "申报作废")
    @PostMapping("/v1/sbzf")
    public CommonResult<Object> sbzf(@RequestBody @Valid Map<String, Object> reqVO) {
        CommonResult<Object> result = tysbNewService.tysbZf(reqVO);
        return result;
    }


}
