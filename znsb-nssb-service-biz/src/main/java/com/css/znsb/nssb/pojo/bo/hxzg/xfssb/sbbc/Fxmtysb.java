
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 分项目通用申报
 * 
 * <p>Java class for fxmtysb complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="fxmtysb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="sbbhead">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;extension base="{http://www.chinatax.gov.cn/dataspec/}sbbheadkz1VO">
 *                 &lt;sequence>
 *                   &lt;element name="bsr" type="{http://www.chinatax.gov.cn/dataspec/}bsy"/>
 *                   &lt;element name="hyDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm" minOccurs="0"/>
 *                   &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm" minOccurs="0"/>
 *                   &lt;element name="jdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm" minOccurs="0"/>
 *                   &lt;element name="zgswskfjDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswskfjDm" minOccurs="0"/>
 *                   &lt;element name="qtgrczbdczjsfft" type="{http://www.chinatax.gov.cn/dataspec/}sfbzDm" minOccurs="0"/>
 *                   &lt;element name="qtgrczbdczlqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq" minOccurs="0"/>
 *                   &lt;element name="qtgrczbdczlqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz" minOccurs="0"/>
 *                   &lt;element name="qtgrczbdcfthyzjsr" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
 *                 &lt;/sequence>
 *               &lt;/extension>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="sbxxGrid">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded">
 *                   &lt;element name="sbxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}sbxxGridlbVo"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="jsList">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence maxOccurs="unbounded">
 *                   &lt;element name="jsListlb" type="{http://www.chinatax.gov.cn/dataspec/}sbFxmtysbGrczfcDskqxxVO"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="slrxxForm" type="{http://www.chinatax.gov.cn/dataspec/}Sbbslxxkz1VO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "fxmtysb", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "sbbhead",
    "sbxxGrid",
    "jsList",
    "slrxxForm"
})
public class Fxmtysb implements Serializable{

    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected Sbbhead sbbhead;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected SbxxGrid sbxxGrid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected JsList jsList;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected Sbbslxxkz1VO slrxxForm;

    /**
     * Gets the value of the sbbhead property.
     * 
     * @return
     *     possible object is
     *     {@link Sbbhead }
     *     
     */
    public Sbbhead getSbbhead() {
        return sbbhead;
    }

    /**
     * Sets the value of the sbbhead property.
     * 
     * @param value
     *     allowed object is
     *     {@link Sbbhead }
     *     
     */
    public void setSbbhead(Sbbhead value) {
        this.sbbhead = value;
    }

    /**
     * Gets the value of the sbxxGrid property.
     * 
     * @return
     *     possible object is
     *     {@link SbxxGrid }
     *     
     */
    public SbxxGrid getSbxxGrid() {
        return sbxxGrid;
    }

    /**
     * Sets the value of the sbxxGrid property.
     * 
     * @param value
     *     allowed object is
     *     {@link SbxxGrid }
     *     
     */
    public void setSbxxGrid(SbxxGrid value) {
        this.sbxxGrid = value;
    }

    /**
     * Gets the value of the jsList property.
     * 
     * @return
     *     possible object is
     *     {@link JsList }
     *     
     */
    public JsList getJsList() {
        return jsList;
    }

    /**
     * Sets the value of the jsList property.
     * 
     * @param value
     *     allowed object is
     *     {@link JsList }
     *     
     */
    public void setJsList(JsList value) {
        this.jsList = value;
    }

    /**
     * Gets the value of the slrxxForm property.
     * 
     * @return
     *     possible object is
     *     {@link Sbbslxxkz1VO }
     *     
     */
    public Sbbslxxkz1VO getSlrxxForm() {
        return slrxxForm;
    }

    /**
     * Sets the value of the slrxxForm property.
     * 
     * @param value
     *     allowed object is
     *     {@link Sbbslxxkz1VO }
     *     
     */
    public void setSlrxxForm(Sbbslxxkz1VO value) {
        this.slrxxForm = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded">
     *         &lt;element name="jsListlb" type="{http://www.chinatax.gov.cn/dataspec/}sbFxmtysbGrczfcDskqxxVO"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "jsListlb"
    })
    public static class JsList implements Serializable {

        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
        protected List<SbFxmtysbGrczfcDskqxxVO> jsListlb;

        /**
         * Gets the value of the jsListlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the jsListlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getJsListlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SbFxmtysbGrczfcDskqxxVO }
         * 
         * 
         */
        public List<SbFxmtysbGrczfcDskqxxVO> getJsListlb() {
            if (jsListlb == null) {
                jsListlb = new ArrayList<SbFxmtysbGrczfcDskqxxVO>();
            }
            return this.jsListlb;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;extension base="{http://www.chinatax.gov.cn/dataspec/}sbbheadkz1VO">
     *       &lt;sequence>
     *         &lt;element name="bsr" type="{http://www.chinatax.gov.cn/dataspec/}bsy"/>
     *         &lt;element name="hyDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm" minOccurs="0"/>
     *         &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm" minOccurs="0"/>
     *         &lt;element name="jdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}hyDm" minOccurs="0"/>
     *         &lt;element name="zgswskfjDm" type="{http://www.chinatax.gov.cn/dataspec/}zgswskfjDm" minOccurs="0"/>
     *         &lt;element name="qtgrczbdczjsfft" type="{http://www.chinatax.gov.cn/dataspec/}sfbzDm" minOccurs="0"/>
     *         &lt;element name="qtgrczbdczlqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq" minOccurs="0"/>
     *         &lt;element name="qtgrczbdczlqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz" minOccurs="0"/>
     *         &lt;element name="qtgrczbdcfthyzjsr" type="{http://www.chinatax.gov.cn/dataspec/}je" minOccurs="0"/>
     *       &lt;/sequence>
     *     &lt;/extension>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "bsr",
        "hyDm",
        "xzqhszDm",
        "jdxzDm",
        "zgswskfjDm",
        "qtgrczbdczjsfft",
        "qtgrczbdczlqq",
        "qtgrczbdczlqz",
        "qtgrczbdcfthyzjsr"
    })
    public static class Sbbhead
        extends Sbbheadkz1VO implements Serializable
    {

        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
        protected String bsr;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected String hyDm;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected String xzqhszDm;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected String jdxzDm;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected String zgswskfjDm;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected String qtgrczbdczjsfft;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected String qtgrczbdczlqq;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected String qtgrczbdczlqz;
        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
        protected Double qtgrczbdcfthyzjsr;

        /**
         * Gets the value of the bsr property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBsr() {
            return bsr;
        }

        /**
         * Sets the value of the bsr property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBsr(String value) {
            this.bsr = value;
        }

        /**
         * Gets the value of the hyDm property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getHyDm() {
            return hyDm;
        }

        /**
         * Sets the value of the hyDm property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setHyDm(String value) {
            this.hyDm = value;
        }

        /**
         * Gets the value of the xzqhszDm property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getXzqhszDm() {
            return xzqhszDm;
        }

        /**
         * Sets the value of the xzqhszDm property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setXzqhszDm(String value) {
            this.xzqhszDm = value;
        }

        /**
         * Gets the value of the jdxzDm property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getJdxzDm() {
            return jdxzDm;
        }

        /**
         * Sets the value of the jdxzDm property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setJdxzDm(String value) {
            this.jdxzDm = value;
        }

        /**
         * Gets the value of the zgswskfjDm property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getZgswskfjDm() {
            return zgswskfjDm;
        }

        /**
         * Sets the value of the zgswskfjDm property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setZgswskfjDm(String value) {
            this.zgswskfjDm = value;
        }

        /**
         * Gets the value of the qtgrczbdczjsfft property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getQtgrczbdczjsfft() {
            return qtgrczbdczjsfft;
        }

        /**
         * Sets the value of the qtgrczbdczjsfft property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setQtgrczbdczjsfft(String value) {
            this.qtgrczbdczjsfft = value;
        }

        /**
         * Gets the value of the qtgrczbdczlqq property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getQtgrczbdczlqq() {
            return qtgrczbdczlqq;
        }

        /**
         * Sets the value of the qtgrczbdczlqq property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setQtgrczbdczlqq(String value) {
            this.qtgrczbdczlqq = value;
        }

        /**
         * Gets the value of the qtgrczbdczlqz property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getQtgrczbdczlqz() {
            return qtgrczbdczlqz;
        }

        /**
         * Sets the value of the qtgrczbdczlqz property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setQtgrczbdczlqz(String value) {
            this.qtgrczbdczlqz = value;
        }

        /**
         * Gets the value of the qtgrczbdcfthyzjsr property.
         * 
         * @return
         *     possible object is
         *     {@link Double }
         *     
         */
        public Double getQtgrczbdcfthyzjsr() {
            return qtgrczbdcfthyzjsr;
        }

        /**
         * Sets the value of the qtgrczbdcfthyzjsr property.
         * 
         * @param value
         *     allowed object is
         *     {@link Double }
         *     
         */
        public void setQtgrczbdcfthyzjsr(Double value) {
            this.qtgrczbdcfthyzjsr = value;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence maxOccurs="unbounded">
     *         &lt;element name="sbxxGridlb" type="{http://www.chinatax.gov.cn/dataspec/}sbxxGridlbVo"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "sbxxGridlb"
    })
    public static class SbxxGrid implements Serializable{

        @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
        protected List<SbxxGridlbVo> sbxxGridlb;

        /**
         * Gets the value of the sbxxGridlb property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the sbxxGridlb property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getSbxxGridlb().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SbxxGridlbVo }
         * 
         * 
         */
        public List<SbxxGridlbVo> getSbxxGridlb() {
            if (sbxxGridlb == null) {
                sbxxGridlb = new ArrayList<SbxxGridlbVo>();
            }
            return this.sbxxGridlb;
        }

    }

}
