package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;

/**
 * 购进农产品直接销售核定农产品增值税进项税额Grid
 *
 * <p>gjncpzjxshdncpzzsjxseGrid complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="gjncpzjxshdncpzzsjxseGrid">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence maxOccurs="unbounded">
 *         &lt;element name="gjncpzjxshdncpzzsjxseGridlbVO" type="{http://www.chinatax.gov.cn/dataspec/}gjncpzjxshdncpzzsjxseGridlbVO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gjncpzjxshdncpzzsjxseGrid", propOrder = { "gjncpzjxshdncpzzsjxseGridlbVO" })
public class GjncpzjxshdncpzzsjxseGrid {
    @XmlElement(nillable = true, required = true)
    protected List<GjncpzjxshdncpzzsjxseGridlbVO> gjncpzjxshdncpzzsjxseGridlbVO;

    /**
     * Gets the value of the gjncpzjxshdncpzzsjxseGridlbVO property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the gjncpzjxshdncpzzsjxseGridlbVO property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     * getGjncpzjxshdncpzzsjxseGridlbVO().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link GjncpzjxshdncpzzsjxseGridlbVO}
     */
    public List<GjncpzjxshdncpzzsjxseGridlbVO> getGjncpzjxshdncpzzsjxseGridlbVO() {
        if (gjncpzjxshdncpzzsjxseGridlbVO == null) {
            gjncpzjxshdncpzzsjxseGridlbVO = new ArrayList<GjncpzjxshdncpzzsjxseGridlbVO>();
        }
        return this.gjncpzjxshdncpzzsjxseGridlbVO;
    }
}