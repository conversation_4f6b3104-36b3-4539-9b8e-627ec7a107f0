package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《增值税（一般纳税人）》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzsywbw", propOrder = { "zzsSbb" })
@Getter
@Setter
public class Zzsywbw extends TaxDoc {
    /**
     * 《增值税（一般纳税人）》业务报文
     */
    @XmlElement(nillable = true, required = true)
    protected ZzsybsbSbbdxxVO zzsSbb;
}