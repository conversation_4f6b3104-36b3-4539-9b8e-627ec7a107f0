package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 受理信息
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "slxxForm", propOrder = { "sfdlsb", "dlrmc", "dlrdz", "dlrsfzjzlDm1", "dlrsfzjhm1", "sqr", "smr", "slrq", "slr", "slswjg" })
@Getter
@Setter
public class SlxxForm {
    /**
     * 是否代理申报
     */
    @XmlElement(nillable = true, required = true)
    protected String sfdlsb;

    /**
     * 代理人名称
     */
    protected String dlrmc;

    /**
     * 代理人地址
     */
    protected String dlrdz;

    /**
     * 代理人员身份证件类型
     */
    protected String dlrsfzjzlDm1;

    /**
     * 代理人员身份证件号码
     */
    protected String dlrsfzjhm1;

    /**
     * 授权人
     */
    protected String sqr;

    /**
     * 声明人
     */
    @XmlElement(nillable = true, required = true)
    protected String smr;

    /**
     * 受理日期
     */
    @XmlElement(nillable = true, required = true)
    protected String slrq;

    /**
     * 受理人
     */
    @XmlElement(nillable = true, required = true)
    protected String slr;

    /**
     * 主管税务机关
     */
    @XmlElement(nillable = true, required = true)
    protected String slswjg;
}