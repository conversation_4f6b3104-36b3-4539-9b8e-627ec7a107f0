
package com.css.znsb.nssb.pojo.bo.hxzg.xfssb.sbbc;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for slxxForm complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="slxxForm">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="jbr" type="{http://www.chinatax.gov.cn/dataspec/}jbr"/>
 *         &lt;element name="dlr" type="{http://www.chinatax.gov.cn/dataspec/}dlr"/>
 *         &lt;element name="dlrlxdh" type="{http://www.chinatax.gov.cn/dataspec/}dlrlxdh"/>
 *         &lt;element name="sqr" type="{http://www.chinatax.gov.cn/dataspec/}sqr"/>
 *         &lt;element name="xzqhsf" type="{http://www.chinatax.gov.cn/dataspec/}xzqhsf"/>
 *         &lt;element name="xzqhds" type="{http://www.chinatax.gov.cn/dataspec/}xzqhds"/>
 *         &lt;element name="xzqhqx" type="{http://www.chinatax.gov.cn/dataspec/}xzqhqx"/>
 *         &lt;element name="jdxzDm" type="{http://www.chinatax.gov.cn/dataspec/}jdxzDm"/>
 *         &lt;element name="swdlrdz" type="{http://www.chinatax.gov.cn/dataspec/}swdlrdz"/>
 *         &lt;element name="swdlrmc" type="{http://www.chinatax.gov.cn/dataspec/}swdlrmc"/>
 *         &lt;element name="slr" type="{http://www.chinatax.gov.cn/dataspec/}slr"/>
 *         &lt;element name="slrq" type="{http://www.chinatax.gov.cn/dataspec/}slrq"/>
 *         &lt;element name="slswjg" type="{http://www.chinatax.gov.cn/dataspec/}slswjg"/>
 *         &lt;element name="blrysfzjlxDm" type="{http://www.chinatax.gov.cn/dataspec/}blrysfzjlxDm"/>
 *         &lt;element name="blrysfzjhm" type="{http://www.chinatax.gov.cn/dataspec/}blrysfzjhm"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "slxxForm", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
    "jbr",
    "dlr",
    "dlrlxdh",
    "sqr",
    "xzqhsf",
    "xzqhds",
    "xzqhqx",
    "jdxzDm",
    "swdlrdz",
    "swdlrmc",
    "slr",
    "slrq",
    "slswjg",
    "blrysfzjlxDm",
    "blrysfzjhm"
})
public class SlxxForm
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String jbr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String dlr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String dlrlxdh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sqr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xzqhsf;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xzqhds;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String xzqhqx;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String jdxzDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String swdlrdz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String swdlrmc;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slr;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String slswjg;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String blrysfzjlxDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String blrysfzjhm;

    /**
     * Gets the value of the jbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJbr() {
        return jbr;
    }

    /**
     * Sets the value of the jbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJbr(String value) {
        this.jbr = value;
    }

    /**
     * Gets the value of the dlr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDlr() {
        return dlr;
    }

    /**
     * Sets the value of the dlr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDlr(String value) {
        this.dlr = value;
    }

    /**
     * Gets the value of the dlrlxdh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDlrlxdh() {
        return dlrlxdh;
    }

    /**
     * Sets the value of the dlrlxdh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDlrlxdh(String value) {
        this.dlrlxdh = value;
    }

    /**
     * Gets the value of the sqr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSqr() {
        return sqr;
    }

    /**
     * Sets the value of the sqr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSqr(String value) {
        this.sqr = value;
    }

    /**
     * Gets the value of the xzqhsf property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhsf() {
        return xzqhsf;
    }

    /**
     * Sets the value of the xzqhsf property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhsf(String value) {
        this.xzqhsf = value;
    }

    /**
     * Gets the value of the xzqhds property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhds() {
        return xzqhds;
    }

    /**
     * Sets the value of the xzqhds property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhds(String value) {
        this.xzqhds = value;
    }

    /**
     * Gets the value of the xzqhqx property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhqx() {
        return xzqhqx;
    }

    /**
     * Sets the value of the xzqhqx property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhqx(String value) {
        this.xzqhqx = value;
    }

    /**
     * Gets the value of the jdxzDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJdxzDm() {
        return jdxzDm;
    }

    /**
     * Sets the value of the jdxzDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJdxzDm(String value) {
        this.jdxzDm = value;
    }

    /**
     * Gets the value of the swdlrdz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSwdlrdz() {
        return swdlrdz;
    }

    /**
     * Sets the value of the swdlrdz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSwdlrdz(String value) {
        this.swdlrdz = value;
    }

    /**
     * Gets the value of the swdlrmc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSwdlrmc() {
        return swdlrmc;
    }

    /**
     * Sets the value of the swdlrmc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSwdlrmc(String value) {
        this.swdlrmc = value;
    }

    /**
     * Gets the value of the slr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlr() {
        return slr;
    }

    /**
     * Sets the value of the slr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlr(String value) {
        this.slr = value;
    }

    /**
     * Gets the value of the slrq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlrq() {
        return slrq;
    }

    /**
     * Sets the value of the slrq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlrq(String value) {
        this.slrq = value;
    }

    /**
     * Gets the value of the slswjg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlswjg() {
        return slswjg;
    }

    /**
     * Sets the value of the slswjg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlswjg(String value) {
        this.slswjg = value;
    }

    /**
     * Gets the value of the blrysfzjlxDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBlrysfzjlxDm() {
        return blrysfzjlxDm;
    }

    /**
     * Sets the value of the blrysfzjlxDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBlrysfzjlxDm(String value) {
        this.blrysfzjlxDm = value;
    }

    /**
     * Gets the value of the blrysfzjhm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBlrysfzjhm() {
        return blrysfzjhm;
    }

    /**
     * Sets the value of the blrysfzjhm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBlrysfzjhm(String value) {
        this.blrysfzjhm = value;
    }

}
