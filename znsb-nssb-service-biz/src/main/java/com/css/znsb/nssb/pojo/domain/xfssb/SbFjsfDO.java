package com.css.znsb.nssb.pojo.domain.xfssb;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 附加税（费）申报
 * @TableName sb_fjsf
 */
@TableName(value ="sb_fjsf")
@Data
public class SbFjsfDO implements Serializable {
    /**
     * UUID||uuid
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 凭证序号
     */
    @TableField(value = "pzxh")
    private String pzxh;

    /**
     * 申报UUID
     */
    @TableField(value = "sbuuid")
    private String sbuuid;

    /**
     * 二维表行序号
     */
    @TableField(value = "ewbhxh")
    private String ewbhxh;

    /**
     * 征收项目代码
     */
    @TableField(value = "zsxm_dm")
    private String zsxmDm;

    /**
     * 征收品目代码
     */
    @TableField(value = "zspm_dm")
    private String zspmDm;

    /**
     * 一般增值税||一般增值税
     */
    @TableField(value = "ybzzs")
    private BigDecimal ybzzs;

    /**
     * 免抵税额||免抵税额
     */
    @TableField(value = "zzsmdse")
    private BigDecimal zzsmdse;

    /**
     * 消费税||消费税
     */
    @TableField(value = "xfs")
    private BigDecimal xfs;

    /**
     * 营业税
     */
    @TableField(value = "yys")
    private BigDecimal yys;

    /**
     * 合计
     */
    @TableField(value = "hj")
    private BigDecimal hj;

    /**
     * 税率
     */
    @TableField(value = "sl_1")
    private BigDecimal sl1;

    /**
     * 本期应纳税费额
     */
    @TableField(value = "bqynsfe")
    private BigDecimal bqynsfe;

    /**
     * 税收减免性质代码
     */
    @TableField(value = "ssjmxz_dm")
    private String ssjmxzDm;

    /**
     * 减免额
     */
    @TableField(value = "jme")
    private BigDecimal jme;

    /**
     * 本期预缴税额
     */
    @TableField(value = "bqyjse")
    private BigDecimal bqyjse;

    /**
     * 本期应补退税额
     */
    @TableField(value = "bqybtse")
    private BigDecimal bqybtse;

    /**
     * 认定凭证UUID
     */
    @TableField(value = "rdpzuuid")
    private String rdpzuuid;

    /**
     * 录入日期
     */
    @TableField(value = "lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField(value = "xgrq")
    private Date xgrq;

    /**
     * 数据归属地区
     */
    @TableField(value = "sjgsdq")
    private String sjgsdq;

    /**
     * 数据同步时间
     */
    @TableField(value = "sjtb_sj")
    private Date sjtbSj;

    /**
     * 零申报标志
     */
    @TableField(value = "lsbbz")
    private String lsbbz;

    /**
     * 主税征收项目代码
     */
    @TableField(value = "zszsxm_dm")
    private String zszsxmDm;

    /**
     * 销售收入
     */
    @TableField(value = "xssr")
    private BigDecimal xssr;

    /**
     * 数据剥离标志||仅限于数据剥离时使用
     */
    @TableField(value = "sjblbz")
    private Integer sjblbz;

    /**
     * 本期是否适用小规模优惠政策||本期是否适用增值税小规模纳税人优惠政策
     */
    @TableField(value = "bqsfsyxgmyhzc")
    private String bqsfsyxgmyhzc;

    /**
     * 普惠减免性质代码||普惠减免性质代码
     */
    @TableField(value = "phjmxz_dm")
    private String phjmxzDm;

    /**
     * 普惠减免税务事项代码||普惠减免税务事项代码
     */
    @TableField(value = "phjmswsx_dm")
    private String phjmswsxDm;

    /**
     * 普惠减征比例||普惠减征比例
     */
    @TableField(value = "phjzbl")
    private BigDecimal phjzbl;

    /**
     * 普惠减免税额||普惠减免税额
     */
    @TableField(value = "phjmse")
    private BigDecimal phjmse;

    /**
     * 增值税留底退税额
     */
    @TableField(value = "zzsldtse")
    private BigDecimal zzsldtse;

    /**
     * 产教融合减免性质
     */
    @TableField(value = "cjrhjmxz_dm")
    private String cjrhjmxzDm;

    /**
     * 本期产教融合型企业抵免金额
     */
    @TableField(value = "bqcjrhxqydmje")
    private BigDecimal bqcjrhxqydmje;

    /**
     * 税务事项代码
     */
    @TableField(value = "swsx_dm")
    private String swsxDm;

    /**
     * 增值税限额减免金额
     */
    @TableField(value = "zzsxejmje")
    private BigDecimal zzsxejmje;

    /**
     * 录入人身份id
     */
    @TableField(value = "lrrsfid")
    private String lrrsfid;

    /**
     * 修改人身份id
     */
    @TableField(value = "xgrsfid")
    private String xgrsfid;

    /**
     * 业务渠道代码
     */
    @TableField(value = "ywqd_dm")
    private String ywqdDm;

    /**
     * 数据产生地区
     */
    @TableField(value = "sjcsdq")
    private String sjcsdq;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SbFjsfDO other = (SbFjsfDO) that;
        return (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
            && (this.getPzxh() == null ? other.getPzxh() == null : this.getPzxh().equals(other.getPzxh()))
            && (this.getSbuuid() == null ? other.getSbuuid() == null : this.getSbuuid().equals(other.getSbuuid()))
            && (this.getEwbhxh() == null ? other.getEwbhxh() == null : this.getEwbhxh().equals(other.getEwbhxh()))
            && (this.getZsxmDm() == null ? other.getZsxmDm() == null : this.getZsxmDm().equals(other.getZsxmDm()))
            && (this.getZspmDm() == null ? other.getZspmDm() == null : this.getZspmDm().equals(other.getZspmDm()))
            && (this.getYbzzs() == null ? other.getYbzzs() == null : this.getYbzzs().equals(other.getYbzzs()))
            && (this.getZzsmdse() == null ? other.getZzsmdse() == null : this.getZzsmdse().equals(other.getZzsmdse()))
            && (this.getXfs() == null ? other.getXfs() == null : this.getXfs().equals(other.getXfs()))
            && (this.getYys() == null ? other.getYys() == null : this.getYys().equals(other.getYys()))
            && (this.getHj() == null ? other.getHj() == null : this.getHj().equals(other.getHj()))
            && (this.getSl1() == null ? other.getSl1() == null : this.getSl1().equals(other.getSl1()))
            && (this.getBqynsfe() == null ? other.getBqynsfe() == null : this.getBqynsfe().equals(other.getBqynsfe()))
            && (this.getSsjmxzDm() == null ? other.getSsjmxzDm() == null : this.getSsjmxzDm().equals(other.getSsjmxzDm()))
            && (this.getJme() == null ? other.getJme() == null : this.getJme().equals(other.getJme()))
            && (this.getBqyjse() == null ? other.getBqyjse() == null : this.getBqyjse().equals(other.getBqyjse()))
            && (this.getBqybtse() == null ? other.getBqybtse() == null : this.getBqybtse().equals(other.getBqybtse()))
            && (this.getRdpzuuid() == null ? other.getRdpzuuid() == null : this.getRdpzuuid().equals(other.getRdpzuuid()))
            && (this.getLrrq() == null ? other.getLrrq() == null : this.getLrrq().equals(other.getLrrq()))
            && (this.getXgrq() == null ? other.getXgrq() == null : this.getXgrq().equals(other.getXgrq()))
            && (this.getSjgsdq() == null ? other.getSjgsdq() == null : this.getSjgsdq().equals(other.getSjgsdq()))
            && (this.getSjtbSj() == null ? other.getSjtbSj() == null : this.getSjtbSj().equals(other.getSjtbSj()))
            && (this.getLsbbz() == null ? other.getLsbbz() == null : this.getLsbbz().equals(other.getLsbbz()))
            && (this.getZszsxmDm() == null ? other.getZszsxmDm() == null : this.getZszsxmDm().equals(other.getZszsxmDm()))
            && (this.getXssr() == null ? other.getXssr() == null : this.getXssr().equals(other.getXssr()))
            && (this.getSjblbz() == null ? other.getSjblbz() == null : this.getSjblbz().equals(other.getSjblbz()))
            && (this.getBqsfsyxgmyhzc() == null ? other.getBqsfsyxgmyhzc() == null : this.getBqsfsyxgmyhzc().equals(other.getBqsfsyxgmyhzc()))
            && (this.getPhjmxzDm() == null ? other.getPhjmxzDm() == null : this.getPhjmxzDm().equals(other.getPhjmxzDm()))
            && (this.getPhjmswsxDm() == null ? other.getPhjmswsxDm() == null : this.getPhjmswsxDm().equals(other.getPhjmswsxDm()))
            && (this.getPhjzbl() == null ? other.getPhjzbl() == null : this.getPhjzbl().equals(other.getPhjzbl()))
            && (this.getPhjmse() == null ? other.getPhjmse() == null : this.getPhjmse().equals(other.getPhjmse()))
            && (this.getZzsldtse() == null ? other.getZzsldtse() == null : this.getZzsldtse().equals(other.getZzsldtse()))
            && (this.getCjrhjmxzDm() == null ? other.getCjrhjmxzDm() == null : this.getCjrhjmxzDm().equals(other.getCjrhjmxzDm()))
            && (this.getBqcjrhxqydmje() == null ? other.getBqcjrhxqydmje() == null : this.getBqcjrhxqydmje().equals(other.getBqcjrhxqydmje()))
            && (this.getSwsxDm() == null ? other.getSwsxDm() == null : this.getSwsxDm().equals(other.getSwsxDm()))
            && (this.getZzsxejmje() == null ? other.getZzsxejmje() == null : this.getZzsxejmje().equals(other.getZzsxejmje()))
            && (this.getLrrsfid() == null ? other.getLrrsfid() == null : this.getLrrsfid().equals(other.getLrrsfid()))
            && (this.getXgrsfid() == null ? other.getXgrsfid() == null : this.getXgrsfid().equals(other.getXgrsfid()))
            && (this.getYwqdDm() == null ? other.getYwqdDm() == null : this.getYwqdDm().equals(other.getYwqdDm()))
            && (this.getSjcsdq() == null ? other.getSjcsdq() == null : this.getSjcsdq().equals(other.getSjcsdq()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getPzxh() == null) ? 0 : getPzxh().hashCode());
        result = prime * result + ((getSbuuid() == null) ? 0 : getSbuuid().hashCode());
        result = prime * result + ((getEwbhxh() == null) ? 0 : getEwbhxh().hashCode());
        result = prime * result + ((getZsxmDm() == null) ? 0 : getZsxmDm().hashCode());
        result = prime * result + ((getZspmDm() == null) ? 0 : getZspmDm().hashCode());
        result = prime * result + ((getYbzzs() == null) ? 0 : getYbzzs().hashCode());
        result = prime * result + ((getZzsmdse() == null) ? 0 : getZzsmdse().hashCode());
        result = prime * result + ((getXfs() == null) ? 0 : getXfs().hashCode());
        result = prime * result + ((getYys() == null) ? 0 : getYys().hashCode());
        result = prime * result + ((getHj() == null) ? 0 : getHj().hashCode());
        result = prime * result + ((getSl1() == null) ? 0 : getSl1().hashCode());
        result = prime * result + ((getBqynsfe() == null) ? 0 : getBqynsfe().hashCode());
        result = prime * result + ((getSsjmxzDm() == null) ? 0 : getSsjmxzDm().hashCode());
        result = prime * result + ((getJme() == null) ? 0 : getJme().hashCode());
        result = prime * result + ((getBqyjse() == null) ? 0 : getBqyjse().hashCode());
        result = prime * result + ((getBqybtse() == null) ? 0 : getBqybtse().hashCode());
        result = prime * result + ((getRdpzuuid() == null) ? 0 : getRdpzuuid().hashCode());
        result = prime * result + ((getLrrq() == null) ? 0 : getLrrq().hashCode());
        result = prime * result + ((getXgrq() == null) ? 0 : getXgrq().hashCode());
        result = prime * result + ((getSjgsdq() == null) ? 0 : getSjgsdq().hashCode());
        result = prime * result + ((getSjtbSj() == null) ? 0 : getSjtbSj().hashCode());
        result = prime * result + ((getLsbbz() == null) ? 0 : getLsbbz().hashCode());
        result = prime * result + ((getZszsxmDm() == null) ? 0 : getZszsxmDm().hashCode());
        result = prime * result + ((getXssr() == null) ? 0 : getXssr().hashCode());
        result = prime * result + ((getSjblbz() == null) ? 0 : getSjblbz().hashCode());
        result = prime * result + ((getBqsfsyxgmyhzc() == null) ? 0 : getBqsfsyxgmyhzc().hashCode());
        result = prime * result + ((getPhjmxzDm() == null) ? 0 : getPhjmxzDm().hashCode());
        result = prime * result + ((getPhjmswsxDm() == null) ? 0 : getPhjmswsxDm().hashCode());
        result = prime * result + ((getPhjzbl() == null) ? 0 : getPhjzbl().hashCode());
        result = prime * result + ((getPhjmse() == null) ? 0 : getPhjmse().hashCode());
        result = prime * result + ((getZzsldtse() == null) ? 0 : getZzsldtse().hashCode());
        result = prime * result + ((getCjrhjmxzDm() == null) ? 0 : getCjrhjmxzDm().hashCode());
        result = prime * result + ((getBqcjrhxqydmje() == null) ? 0 : getBqcjrhxqydmje().hashCode());
        result = prime * result + ((getSwsxDm() == null) ? 0 : getSwsxDm().hashCode());
        result = prime * result + ((getZzsxejmje() == null) ? 0 : getZzsxejmje().hashCode());
        result = prime * result + ((getLrrsfid() == null) ? 0 : getLrrsfid().hashCode());
        result = prime * result + ((getXgrsfid() == null) ? 0 : getXgrsfid().hashCode());
        result = prime * result + ((getYwqdDm() == null) ? 0 : getYwqdDm().hashCode());
        result = prime * result + ((getSjcsdq() == null) ? 0 : getSjcsdq().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", uuid=").append(uuid);
        sb.append(", pzxh=").append(pzxh);
        sb.append(", sbuuid=").append(sbuuid);
        sb.append(", ewbhxh=").append(ewbhxh);
        sb.append(", zsxmDm=").append(zsxmDm);
        sb.append(", zspmDm=").append(zspmDm);
        sb.append(", ybzzs=").append(ybzzs);
        sb.append(", zzsmdse=").append(zzsmdse);
        sb.append(", xfs=").append(xfs);
        sb.append(", yys=").append(yys);
        sb.append(", hj=").append(hj);
        sb.append(", sl1=").append(sl1);
        sb.append(", bqynsfe=").append(bqynsfe);
        sb.append(", ssjmxzDm=").append(ssjmxzDm);
        sb.append(", jme=").append(jme);
        sb.append(", bqyjse=").append(bqyjse);
        sb.append(", bqybtse=").append(bqybtse);
        sb.append(", rdpzuuid=").append(rdpzuuid);
        sb.append(", lrrq=").append(lrrq);
        sb.append(", xgrq=").append(xgrq);
        sb.append(", sjgsdq=").append(sjgsdq);
        sb.append(", sjtbSj=").append(sjtbSj);
        sb.append(", lsbbz=").append(lsbbz);
        sb.append(", zszsxmDm=").append(zszsxmDm);
        sb.append(", xssr=").append(xssr);
        sb.append(", sjblbz=").append(sjblbz);
        sb.append(", bqsfsyxgmyhzc=").append(bqsfsyxgmyhzc);
        sb.append(", phjmxzDm=").append(phjmxzDm);
        sb.append(", phjmswsxDm=").append(phjmswsxDm);
        sb.append(", phjzbl=").append(phjzbl);
        sb.append(", phjmse=").append(phjmse);
        sb.append(", zzsldtse=").append(zzsldtse);
        sb.append(", cjrhjmxzDm=").append(cjrhjmxzDm);
        sb.append(", bqcjrhxqydmje=").append(bqcjrhxqydmje);
        sb.append(", swsxDm=").append(swsxDm);
        sb.append(", zzsxejmje=").append(zzsxejmje);
        sb.append(", lrrsfid=").append(lrrsfid);
        sb.append(", xgrsfid=").append(xgrsfid);
        sb.append(", ywqdDm=").append(ywqdDm);
        sb.append(", sjcsdq=").append(sjcsdq);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}