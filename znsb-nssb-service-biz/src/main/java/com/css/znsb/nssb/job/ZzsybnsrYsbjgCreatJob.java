package com.css.znsb.nssb.job;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.css.znsb.nssb.service.zzsybnsrsb.plsb.ZzsybnsrYsbsjcjService;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * 增值税一般纳税人预申报结果创建定时任务
 */
@Slf4j
@Component
public class ZzsybnsrYsbjgCreatJob {

    @Resource
    private ZzsybnsrYsbsjcjService ysbsjcjService;

    @XxlJob("zzsybnsrYsbjgCreatJob")
    public void execute() {
        log.info("开始执行增值税一般纳税人申报预申报结果创建定时任务");
        ysbsjcjService.handlerYsbsj(null);
        log.info("增值税一般纳税人申报预申报结果创建定时任务执行完成");
    }
}
