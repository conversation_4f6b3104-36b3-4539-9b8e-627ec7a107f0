package com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.impl;

import cn.hutool.core.bean.BeanUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.pojo.bo.hxzg.dj.general.DJNsrxxVO;
import com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.bssb10868.HXZGSB10868Request;
import com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.sb228.*;
import com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.sbqysds.jmczzsyjd.SBJmQysdsCzzsYjdSqxxVO;
import com.css.znsb.nssb.pojo.bo.hxzg.sb.general.SBQysdsfzjgxxVO;
import com.css.znsb.nssb.pojo.bo.hxzg.sb.general.SBSBbcTjqtxxVO;
import com.css.znsb.nssb.pojo.bo.hxzg.sb.general.SBSbxxJhVO;
import com.css.znsb.nssb.pojo.vo.qysds.czzsyjd.csh.QysdsCzzsYjdQcxxItemVO;
import com.css.znsb.nssb.pojo.vo.qysds.czzsyjd.sbbc.QysdsczzsYjdSbbcRequestVO;
import com.css.znsb.nssb.service.qysds.qysdscczsyjdsb.SB228JmQysdsCzzsYjd2021Service;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.nssb.utils.GYObjectUtils;
import com.css.znsb.nssb.utils.GYSbSdsUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

/**
 * Package:com.css.znsb.nssb.service.qysdscczsyjdsb.impl
 * Description: 企业所得税查账征收月季度申报初始化服务接口实现类
 * <AUTHOR>
 * @Create 2024/8/22 11:10
 */
@Slf4j
@Validated
@Service
public class SB228JmQysdsCzzsYjd2021ServiceImpl extends SBJmQysdsCzzsYjdBaseServiceImpl implements SB228JmQysdsCzzsYjd2021Service {

    @Resource
    private NsrxxApi nsrxxApi;

    /**
     * @name 企业所得税查账征收月季度申报组装初始化数据
     * @param: djxh
     * @param: nsrsbh
     * @param: skssqq
     * @param: skssqz
     * @param: resVo
     * @return: QysdsCzzsYjdQcxxItemVO
     * <AUTHOR>
     **/
    @Override
    public List<QysdsCzzsYjdQcxxItemVO> assembleInitData(String djxh, String nsrsbh, String skssqq, String skssqz, Object[] objArray) {
        log.info("企业所得税月季度查账征收乐企辅助平台正常申报期初信息数据TO页面数据转换开始, resp---" + objArray);
        List<QysdsCzzsYjdQcxxItemVO> itemVOList = this.getInitData(djxh, nsrsbh,skssqq, skssqz, objArray);
        return itemVOList;
    }
    @Override
    protected QysdsCzzsYjdQcxxItemVO packageResData(final DJNsrxxVO djNsrxx, final String skssqq, final String skssqz, final Object[] objArr,
                                                    final QysdsCzzsYjdQcxxItemVO itemVO, final boolean isCwgz) {
        super.packageResData(djNsrxx, skssqq, skssqz, objArr, itemVO, isCwgz);

        final SBJmQysdsCzzsYjd2021CshQtxxVO qtxx = (SBJmQysdsCzzsYjd2021CshQtxxVO) objArr[7];
        final SBJmQysdsCzzsYjd2021SqxxVO sqxx = (SBJmQysdsCzzsYjd2021SqxxVO) objArr[8];
        itemVO.setQtxx(qtxx);
        itemVO.setSqsbxx(sqxx);

        //A201020 使用，是否包含海南企业
        if (!"4".equals(qtxx.getZfjglb())) {
            final List<SBQysdsfzjgxxVO> fzjgxxList = (List<SBQysdsfzjgxxVO>) objArr[5];
            itemVO.setSfbhhnqy(GYSbSdsUtils.isHnzmg(qtxx.getZgswjDm()) || GYSbSdsUtils.hasHnfzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfbhHqqy(GYSbSdsUtils.isHqyahzq(qtxx.getZgswjDm()) || GYSbSdsUtils.hasHqfzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfbhxbdkqqy(GYSbSdsUtils.isXbdkfdq(qtxx.getZgswjDm()) || GYSbSdsUtils.hasXbdkfFzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfbhZhPtSzqy( GYSbSdsUtils.isZhPtSz(qtxx.getZgswjDm()) || GYSbSdsUtils.hasZhPtSzFzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfbhPtqy(GYSbSdsUtils.isPt(qtxx.getZgswjDm()) || GYSbSdsUtils.hasPtFzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfbhSzqy(GYSbSdsUtils.isSz(qtxx.getZgswjDm()) || GYSbSdsUtils.hasSzFzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfbhSzhtqy(GYSbSdsUtils.isSzht(qtxx.getZgswjDm()) || GYSbSdsUtils.hasSzhtFzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfbhZhqy(GYSbSdsUtils.isZh(qtxx.getZgswjDm()) || GYSbSdsUtils.hasZhFzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfbhGdnsqy(GYSbSdsUtils.isGdns(qtxx.getZgswjDm()) || GYSbSdsUtils.hasGdnsFzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfShDq(GYSbSdsUtils.isSh(qtxx.getZgswjDm()) ? "Y" : "N");
            itemVO.setSfXjDq( GYSbSdsUtils.isXj(qtxx.getZgswjDm()) || GYSbSdsUtils.hasXjFzjg(fzjgxxList) || GYSbSdsUtils.hasKsFzjg(fzjgxxList) ? "Y" : "N");
            itemVO.setSfXjKsHegs(GYSbSdsUtils.isXjKsHegs(qtxx.getZgswjDm()) ? "Y" : "N");
            itemVO.setSfXjKkdl(GYSbSdsUtils.isXjKkdl(qtxx.getZgswjDm()) && GYSbSdsUtils.getNdFromSq(skssqq) >= 2023 ? "Y" : "N");
            itemVO.setSfXbdkfqy(GYSbSdsUtils.isXbdkfdq(qtxx.getZgswjDm()) ? "Y" : "N");
        }

        // 是否是深圳、珠海、汕头、厦门、上海
        itemVO.setJtqshpt(GYSbSdsUtils.isJtqShpt(qtxx.getZgswjDm()) ? "Y" : "N");

        // 根据预缴方式、总分机构类型加载附表树
        List<Map<String, String>> zfbTreeMap = this.assembleZfbTree(qtxx.getYjfsDm(), qtxx.getZfjglb(), "Y".equals(itemVO.getSfCzfzjg()), skssqz);
        itemVO.setZfbTree(zfbTreeMap);

        // 加载优惠信息
        this.loadYhxx(djNsrxx, skssqq, skssqz, itemVO, sqxx);
        //检查2023年第4季度申报是否填报《居民企业参股外国企业信息报告表》
        this.checkCgwgqyxx(GYSbSdsUtils.cast2StrNew(djNsrxx.getDjxh()), skssqq, skssqz, itemVO);
        //**********特殊纳税人行次处理
        itemVO.setIsJmczyjdYjtsnsr(GYSbSdsUtils.checkYjtsnsr(djNsrxx.getNsrsbh(), djNsrxx.getNsrmc(), skssqq, skssqz, "jmczyjdYjtsnsr")?"Y":"N");

        if (!isCwgz) {
            final Map<String, Object> nsrxxMap = new HashMap<String, Object>();
            // 初始化纳税人信息
            nsrxxMap.put("yjfs", qtxx.getYjfs());
            nsrxxMap.put("sbqylx", qtxx.getSbqylx());
            //总机构在外省的分支机构申报时，需获取到跨省总机构行政区划！
            final String kszjgXzqhszDm = this.getKszjgXzqhszDm(itemVO);
            nsrxxMap.put("xzqhszDm", kszjgXzqhszDm);
            itemVO.setNsrxxForm(nsrxxMap);
            // 加载小微信息
            this.loadXwxx(sqxx, skssqz, itemVO);
            // 加载附报事项
            this.loadFbsx(null, GyUtils.isNull(sqxx.getFbsxGrid()) ? null : sqxx.getFbsxGrid().getFbsxGridlb(), itemVO, skssqz);
            // 加载上期免税收入明细
            final Double mssrHj = this.loadMssrMx(itemVO, GyUtils.isNull(sqxx.getMssrGrid()) ? null : sqxx.getMssrGrid().getMssrGridlb(), null, isCwgz);
            this.addHjForm("sbxx1Form", "mssrLj", itemVO, mssrHj);
            // 加载上期所得减免明细
            final Double sdjmHj = this.loadSdjmMx(itemVO, GyUtils.isNull(sqxx.getSdjmGrid()) ? null : sqxx.getSdjmGrid().getSdjmGridlb());
            this.addHjForm("sbxx2Form", "sdjmLj", itemVO, sdjmHj);
            // 初始化A201020表
            this.loadA201020(null, sqxx, itemVO,skssqq,skssqz);
        }

        return itemVO;
    }
    /**
     * @description 获取跨省总机构行政区划代码
     * @time 2024/9/11 14:22
     * @param: itemVO
     * @return: java.lang.String
     * <AUTHOR>
     */
    private String getKszjgXzqhszDm(QysdsCzzsYjdQcxxItemVO itemVO) {
        String kszjgXzqhszDm = "";
        final SBJmQysdsCzzsYjd2021CshQtxxVO qtxxVO = itemVO.getQtxx();
        final String sbqylx = qtxxVO.getSbqylx();
        final String zfjglb = qtxxVO.getZfjglb();
        final String nsrsbh = itemVO.getNsrsbh();
        //判断当前企业是否为跨省分支机构
        if ("2".equals(sbqylx) && "4".equals(zfjglb)){
            ZnsbMhzcQyjbxxmxReqVO nsrxxCxReq = new ZnsbMhzcQyjbxxmxReqVO();
            nsrxxCxReq.setNsrsbh(nsrsbh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByNsrsbh(nsrxxCxReq);
            List<JbxxmxsjVO> jbxxmxsjVOList = nsrxxResult.getData().getJbxxmxsj();
            final JbxxmxsjVO zjgJbxxmxsjVO = jbxxmxsjVOList.stream()
                    .filter(t -> GyUtils.isNotNull(t.getZfjglxDm()) && t.getZfjglxDm().equals("1"))
                    .findFirst().orElse(null);
            if (GyUtils.isNotNull(zjgJbxxmxsjVO)){
                kszjgXzqhszDm = GyUtils.isNotNull(zjgJbxxmxsjVO.getScjydzxzqhszDm()) ? zjgJbxxmxsjVO.getScjydzxzqhszDm() : zjgJbxxmxsjVO.getZcdzxzqhszDm();
            }
        }

        return kszjgXzqhszDm;
    }

    /**
     *@name    添加明细和合计值到form中
     *@description 相关说明
     *@time    创建时间:2021年3月15日下午11:09:51
     *@param formName form名称
     *@param fieldName 字段名
     *@param itemVO 输出
     *@param hjVal 合计值
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void addHjForm(String formName, String fieldName, QysdsCzzsYjdQcxxItemVO itemVO, Double hjVal) {
        if (GYObjectUtils.isNull(hjVal)) {
            return;
        }
        final Map<String, Object> formMap = new HashMap<String, Object>();
        formMap.put(fieldName, BigDecimal.valueOf(hjVal).toString());
        if ("sbxx1Form".equals(formName)){
            itemVO.setSbxx1Form(formMap);
        }else  if ("sbxx2Form".equals(formName)){
            itemVO.setSbxx2Form(formMap);
        }
    }

    /**
     *@name    加载所得减免明细
     *@description 相关说明
     *@time    创建时间:2021年3月15日下午1:55:37
     *@param itemVO 返回数据
     *@param sdjmList 所得减免列表
     *<AUTHOR>
     *@return 合计
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private Double loadSdjmMx(QysdsCzzsYjdQcxxItemVO itemVO, List<JmCzzsYjd2021YhjmxxVO> sdjmList) {
        if (GYObjectUtils.isNull(sdjmList)) {
            return null;
        }

        Double ret = 0d;
        for (JmCzzsYjd2021YhjmxxVO sdjm : sdjmList) {
            final double yhjmje = sdjm.getYhjmje();
            ret = GYSbSdsUtils.add(ret, yhjmje);
            sdjm.setEwbhmc("8." + sdjm.getEwbhxh());
        }

        ret = GYSbSdsUtils.round(ret, 2);
        itemVO.setSdjmGrid(sdjmList);
        return ret;
    }

    /**
     *@name    加载免税收入明细
     *@description 相关说明
     *@time    创建时间:2021年3月15日下午2:06:37
     *@param itemVO 返回数据
     *@param sqMssrList 上期免税收入列表
     *@param bqMssrList 本期免税收入列表
     *@param isCwgz 是否錯誤更正
     *<AUTHOR>
     *@return 合计
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private Double loadMssrMx(QysdsCzzsYjdQcxxItemVO itemVO, List<JmCzzsYjd2021YhjmxxVO> sqMssrList, List<JmCzzsYjd2021YhjmxxVO> bqMssrList, final boolean isCwgz) {
        Double ret = null;
        if (GYObjectUtils.isNull(sqMssrList) && GYObjectUtils.isNull(bqMssrList)) {
            return ret;
        } else if (GYObjectUtils.isNull(sqMssrList)) {
            final List<Map<String, Object>> bqMssr = GYSbSdsUtils.beanListToMapList(bqMssrList);
            itemVO.setMssrGrid(bqMssr);
        } else if (GYObjectUtils.isNull(bqMssrList)) {
            ret = 0d;
            if(!isCwgz) {
                final List<Map<String, Object>> sqMssrMapList = GYSbSdsUtils.beanListToMapList(sqMssrList);
                for (Map<String, Object> sqMssr : sqMssrMapList) {
                    final double yhjmje = GYSbSdsUtils.cast2Double(sqMssr.get("yhjmje"));
                    ret = GYSbSdsUtils.add(ret, yhjmje);
                    sqMssr.put("sqyhjmje", yhjmje);
                    sqMssr.put("ewbhmc", "7." + sqMssr.get("ewbhxh"));
                    sqMssr.put("sqsbbz", "Y");
                }

                ret = GYSbSdsUtils.round(ret, 2);
                itemVO.setMssrGrid(sqMssrMapList);
            }
        } else {
            final List<Map<String, Object>> bqMssrMapList = GYSbSdsUtils.beanListToMapList(bqMssrList);
            final Map<String, JmCzzsYjd2021YhjmxxVO> sqMssrMap = GYSbSdsUtils.transListToMap(sqMssrList, "yhswsx");
            for (Map<String, Object> bqMssr : bqMssrMapList) {
                final String yhswsx = (String) bqMssr.get("yhswsx");
                if (sqMssrMap.containsKey(yhswsx)) {
                    bqMssr.put("sqyhjmje", sqMssrMap.get(yhswsx).getYhjmje());
                    bqMssr.put("ewbhmc", "7." + bqMssr.get("ewbhxh"));
                    bqMssr.put("sqsbbz", "Y");
                }
            }
            itemVO.setMssrGrid(bqMssrMapList);
        }
        if(!GYObjectUtils.isNull(sqMssrList)) {
            //上期减免数据返回页面
            itemVO.setSdjmGrid(sqMssrList);
        }
        return ret;
    }

    /**
     *@name    加载减免所得明细
     *@description 相关说明
     *@time    创建时间:2021年3月15日下午1:55:37
     *@param itemVO 返回数据
     *@param jmsdList 减免所得列表
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadJmsdMx(QysdsCzzsYjdQcxxItemVO itemVO, List<JmCzzsYjd2021YhjmxxVO> jmsdList){
        if (GyUtils.isNull(jmsdList)) {
            return;
        }

        final List<Map<String, Object>> jmsdMapList = GYSbSdsUtils.beanListToMapList(jmsdList);
        for (Map<String, Object> jmsdMap : jmsdMapList) {
            jmsdMap.put("ewbhmc", "13." + jmsdMap.get("ewbhxh"));
        }
        itemVO.setJmsdGrid(jmsdMapList);
    }

    /**
     *@name    加载上期附报事项
     *@description 相关说明
     *@time    创建时间:2021年3月11日上午10:07:38
     *@param bqFbsxList 本期附报事项列表
     *@param sqFbsxList 上期附报事项列表
     *@param itemVO 返回数据
     *@param skssqz 税款所属期起
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadFbsx(List<JmCzzsYjd2021FbsxVO> bqFbsxList, List<JmCzzsYjd2021FbsxVO> sqFbsxList, QysdsCzzsYjdQcxxItemVO itemVO, String skssqz) {
        final Map<String, String> fbsxMap = new HashMap<String, String>();
        if (GYObjectUtils.isNull(skssqz)) {
            return;
        }
        // 设置名称
        if (GYSbSdsUtils.getNdFromSq(skssqz) >= 2022) {
            fbsxMap.put("K01002_MC", "事项1");
            fbsxMap.put("Y01001_MC", "事项2");
            if(GYSbSdsUtils.getNdFromSq(skssqz) == 2022 && GYSbSdsUtils.getYfFromSq(skssqz) >= 10){
                fbsxMap.put("Y01002_MC", "事项3");
            }
        } else {
            fbsxMap.put("K01001_MC", "事项1");
            fbsxMap.put("K01002_MC", "事项2");
            fbsxMap.put("Y01001_MC", "事项3");
            if (GYSbSdsUtils.getYfFromSq(skssqz) > 3) {
                fbsxMap.put("K01001zjybz", "Y"); // 设置选项值禁用标志
                fbsxMap.put("K01001xxjybz", "Y"); // 设置选项禁用标志
            }
        }

        if(!GYObjectUtils.isNull(fbsxMap) && fbsxMap.containsKey("Y01002_MC")){
            final List<Map<String, Object>> y01002MapList = new ArrayList<Map<String, Object>>();
            final List<Map<String, Object>> topMapList = new ArrayList<Map<String, Object>>();
            final List<Map<String, Object>> secMapList = new ArrayList<Map<String, Object>>();

            final int nowNd = GYSbSdsUtils.getNdFromSq(skssqz);
            for(int i=0; i<4; i++){
                final Map<String, Object> ndMap = new HashMap<String, Object>();
                ndMap.put("code", String.valueOf(nowNd-i));
                ndMap.put("caption", nowNd-i);
                topMapList.add(ndMap);
            }
            for (Map<String, Object> topMap : topMapList) {
                final String codeTop = GYObjectUtils.isNull(topMap.get("code")) ? "" : String.valueOf(topMap.get("code"));
                for(int j=0; j<12; j++){
                    final Map<String, Object> codeMap = new HashMap<String, Object>();
                    final DecimalFormat df = new DecimalFormat("00");
                    final String yf = df.format(j+1);
                    codeMap.put("code", codeTop+"-"+yf);
                    codeMap.put("pcode", codeTop);
                    codeMap.put("caption", codeTop+"-"+yf);
                    secMapList.add(codeMap);
                }
            }
            topMapList.addAll(secMapList);
            y01002MapList.addAll(topMapList);
            itemVO.setY01002jehxxz(y01002MapList);
        }


        if (GYObjectUtils.isNull(bqFbsxList) && GYObjectUtils.isNull(sqFbsxList)) {
            itemVO.setFbsxForm(fbsxMap);
            return;
        } else if (GYObjectUtils.isNull(bqFbsxList)) {
            for (JmCzzsYjd2021FbsxVO fbsx : sqFbsxList) {
                if ("Y".equals(fbsx.getXxbz())) {
                    final String ewbhgjz = fbsx.getEwbhgjz();
                    fbsxMap.put(ewbhgjz + "xxbz", fbsx.getXxbz());
                    fbsxMap.put(ewbhgjz + "jehxxz", fbsx.getJehxxz());
                    fbsxMap.put(ewbhgjz + "sqxxbz", fbsx.getXxbz());
                    fbsxMap.put(ewbhgjz + "sqjehxxz", fbsx.getJehxxz());
                }
            }
        } else {
            final Map<String, JmCzzsYjd2021FbsxVO> sqFbsxMap = GYSbSdsUtils.transListToMap(sqFbsxList, "ewbhgjz");
            for (JmCzzsYjd2021FbsxVO fbsx : bqFbsxList) {
                final String ewbhgjz = fbsx.getEwbhgjz();
                fbsxMap.put(ewbhgjz + "xxbz", fbsx.getXxbz());
                fbsxMap.put(ewbhgjz + "jehxxz", fbsx.getJehxxz());
                if (!GYObjectUtils.isNull(sqFbsxMap) && !GYObjectUtils.isNull(sqFbsxMap.get(ewbhgjz))) {
                    fbsxMap.put(ewbhgjz + "sqxxbz", sqFbsxMap.get(ewbhgjz).getXxbz());
                    fbsxMap.put(ewbhgjz + "sqjehxxz", sqFbsxMap.get(ewbhgjz).getJehxxz());
                }
            }
        }
        itemVO.setFbsxForm(fbsxMap);
    }

    /**
     *@name    加载小微信息（从业人数和资产总额）
     *@description 相关说明
     *@time    创建时间:2021年3月11日下午2:58:51
     *@param sqxx 上期信息
     *@param skssqz 税款所属期止
     *@param itemVO 返回数据
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadXwxx(final SBJmQysdsCzzsYjd2021SqxxVO sqxx, final String skssqz, final QysdsCzzsYjdQcxxItemVO itemVO) {
        final int jds = GYSbSdsUtils.getJdFromSq(skssqz);
        if (jds < 1 || jds > 4) {
            return;
        }

        final Map<String, Object> sqxxMap = GYSbSdsUtils.beanToMap(sqxx);
        final Map<String, Object> xxMap = new HashMap<String, Object>();
        // 处理本季度前的申报季度数据
        for (int j = 1; j < jds; j++) {
            final Object sqQccyrs = sqxxMap.get("qccyrs" + j);
            if (!GYObjectUtils.isNull(sqQccyrs)) {
                xxMap.put("qccyrs" + j, GYSbSdsUtils.cast2StrNew(sqxxMap.get("qccyrs" + j)));
                xxMap.put("qmcyrs" + j, GYSbSdsUtils.cast2StrNew(sqxxMap.get("qmcyrs" + j)));
                xxMap.put("qczcze" + j, GYSbSdsUtils.cast2StrNew(sqxxMap.get("qczcze" + j)));
                xxMap.put("qmzcze" + j, GYSbSdsUtils.cast2StrNew(sqxxMap.get("qmzcze" + j)));
            }
        }

        itemVO.setXwxgxxForm(xxMap);
    }


    /**
     *@name    检查2023年第4季度或12月申报时：居民企业是否填报《居民企业参股外国企业信息报告表》
     *@description 检查居民企业是否填报《居民企业参股外国企业信息报告表》
     *@time    创建时间:2023年10月24日下午2:33:22
     *@param djxh djxh
     *@param skssqq skssqq
     *@param skssqz skssqz
     *@param itemVO itemVO
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void checkCgwgqyxx(final String djxh,  final String skssqq, final String skssqz, final QysdsCzzsYjdQcxxItemVO itemVO) {
        String isBlWgqyxx = "N"; //检查2023年是否办理《居民企业参股外国企业信息报告表》
        final String yzpzzlDm = YzpzzlEnum.QYSDSCZZS.getDm();
        final int sbsqzN = GYSbSdsUtils.getNdFromSq(skssqz);
        final int sbsqzY = GYSbSdsUtils.getYfFromSq(skssqz);
        if(!(sbsqzN == 2023 && sbsqzY == 12)) {
            return;
        }

//        final  String skssqqcx = "2023-01-01";
//        final  String skssqzcx = "2023-12-31";
//        //待接口确定后调整
//        final List<SBSbxxVO> sbbList = (List<SBSbxxVO>) BizServiceUtil.invokeBizService("SWZJ.HXZG.SB.CXSBBXXFORCWGZWITHOUTKSQY", djxh, "", skssqqcx, skssqzcx, yzpzzlDm);
//        final List<Map<String, Object>> sbList = new ArrayList<Map<String, Object>>();
//        if (!GYObjectUtils.isNull(sbbList)) {
//            for (SBSbxxVO sbxxvo : sbbList) {
//                final Map<String, Object> map = new HashMap<String, Object>();
//                map.put("sbuuid", sbxxvo.getSbuuid());
//                map.put("pzxh", sbxxvo.getPzxh());
//                sbList.add(map);
//            }
//        }
//        if(!GYObjectUtils.isNull(sbList)) {
//            //待接口确定后调整
////            isBlWgqyxx  = (String) BizServiceUtil.invokeBizService("SWZJ.HXZG.SB.CXJMQYCGWGQYXXBBLQK", sbList);
//            isBlWgqyxx = "Y";
//        }
//        sbxxMap.put("isBlWgqyxx", isBlWgqyxx);
    }

    /**
     *@name    加载优惠信息
     *@description 相关说明
     *@time    创建时间:2021年2月26日上午11:17:52
     *@param djNsrxx 纳税人信息
     *@param skssqq 税款所属期起
     *@param skssqz 税款所属期止
     *@param itemVO 输出参数
     *@param sqxx 上期信息
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadYhxx(DJNsrxxVO djNsrxx, String skssqq, String skssqz, final QysdsCzzsYjdQcxxItemVO itemVO, final SBJmQysdsCzzsYjd2021SqxxVO sqxx) {
        final String shxydm = djNsrxx.getNsrsbh();
        final String nsrmc = djNsrxx.getNsrmc();
        final String hyDm = djNsrxx.getHyDm();
        final Map<String, Map<String, Object>> sqMssrMap = this.transListToMap(GYObjectUtils.isNull(sqxx) || GYObjectUtils.isNull(sqxx.getMssrGrid()) ? null : sqxx.getMssrGrid().getMssrGridlb());
        final Map<String, String> tdnsrMap = GYSbSdsUtils.getTdnsrxx(shxydm, nsrmc, skssqq, skssqz, "jmczyjdMssr");
        final Map<String, List<Map<String, Object>>> yhpzMap = GYSbSdsUtils.getJmcz2021YhsxPzxx(skssqq, skssqz, hyDm, nsrmc);
        final String dzbzdszlDm = YzpzzlEnum.QYSDSCZZS.getDm();
        final String mssrKey = dzbzdszlDm + "_" + "jmczyjdMssr";
        final List<Map<String, Object>> mssrList = yhpzMap.get(mssrKey);
        if (!GYObjectUtils.isNull(tdnsrMap)) {
            mssrList.add(0, GYSbSdsUtils.configTreeNode(tdnsrMap.get("ssjmxzDm"), tdnsrMap.get("ssjmxzmc"), "0001", sqMssrMap));
        }
        itemVO.setMssrTree(this.sxMssrYhsx(skssqz, this.addYhxxSqsj(mssrList, sqMssrMap)));

        final Map<String, Map<String, Object>> sqSdjmMap = this.transListToMap(GYObjectUtils.isNull(sqxx) || GYObjectUtils.isNull(sqxx.getSdjmGrid()) ? null : sqxx.getSdjmGrid().getSdjmGridlb());
        final String sdjmKey = dzbzdszlDm + "_" + "jmczyjdSdjm";
        final List<Map<String, Object>> sdjmList = yhpzMap.get(sdjmKey);
        itemVO.setSdjmTree(this.addYhxxSqsj(sdjmList, sqSdjmMap));

        final String jmsdKey = dzbzdszlDm + "_" + "jmczyjdJmsd";
        final List<Map<String, Object>> jmsdList = yhpzMap.get(jmsdKey);
        this.sxJmsdYhxs(skssqz, jmsdList);
        final Map<String, String> bjdaNsrMap = GYSbSdsUtils.getTdnsrxx(shxydm, nsrmc, skssqq, skssqz, "jmczyjdJmsd");
        if (!GYObjectUtils.isNull(bjdaNsrMap)) {
            jmsdList.add(1, GYSbSdsUtils.configTreeNode(bjdaNsrMap.get("ssjmxzDm"), bjdaNsrMap.get("ssjmxzmc"), "", null));
        }
        itemVO.setJmsdTree(jmsdList);
    }

    /**
     *@name    将优惠减免信息列表转为Map
     *@description 相关说明
     *@time    创建时间:2021年2月10日上午11:00:28
     *@param srcList 源列表
     *@return map
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private Map<String, Map<String, Object>> transListToMap(final List<JmCzzsYjd2021YhjmxxVO> srcList) {
        if (GYObjectUtils.isNull(srcList)) {
            return null;
        }
        final Map<String, Map<String, Object>> retMap = new HashMap<String, Map<String, Object>>();
        for (final JmCzzsYjd2021YhjmxxVO item : srcList) {
            final String yhswsx = item.getYhswsx();
            final Map<String, Object> sqYhjmjeMap = new HashMap<String, Object>();
            sqYhjmjeMap.put("sqyhjmje", item.getYhjmje());
            retMap.put(yhswsx, sqYhjmjeMap);
        }

        return retMap;
    }

    /**
     *@name    中文名称
     *@description 相关说明
     *@time    创建时间:2021年2月22日下午4:03:59
     *@param objArr 方法
     *@return 信息
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    @NotNull
    protected Map<String, Object> getPackageParam(final Object[] objArr) {
        final Map<String, Object> retMap = new HashMap<String, Object>();
        final SBJmQysdsCzzsYjd2021CshQtxxVO qtxx =(SBJmQysdsCzzsYjd2021CshQtxxVO) objArr[7];
        retMap.put("zfjglb", qtxx.getZfjglb());
        retMap.put("kdqDm", qtxx.getKdsAndkxqFlag());
        return retMap;
    }

    /**
     *
     *@name    居民企业所得税月季度申报保存
     *@Description 相关说明
     *@Time    创建时间:2015-6-9上午10:57:28
     *@param reqVo
     *@return HXZGSB10868Request
     *<AUTHOR> 段敏
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public Object saveSbxx(QysdsczzsYjdSbbcRequestVO reqVo){
        return super.saveSbxxData(reqVo);
    }
    /**
     * @description 组装暂存信息，因暂存信息报文格式与期初信息不一致，需重新组装
     * @time 2024/9/3 14:55
     * @param: hxzgsb10868Request
     * @return: java.util.List<com.css.znsb.nssb.pojo.vo.qysds.czzsyjd.csh.QysdsCzzsYjdQcxxItemVO>
     * <AUTHOR>
     */
    @Override
    public List<QysdsCzzsYjdQcxxItemVO> assembleZcxx(HXZGSB10868Request zcYwbw) {
        final Jmczyjdsb2021Ywbw ywbw = zcYwbw.getYwbw();
        final JmCzzsYjd2021NsrxxFormVO nsrxx = ywbw.getA200000Ywbd().getNsrxx();
        final SBSBbcTjqtxxVO qtxx =zcYwbw.getBcTjqtxx();

        final String djxh = qtxx.getDjxh();
        final String skssqq = nsrxx.getSkssqq();
        final String skssqz = nsrxx.getSkssqz();
        final String sblxDm = nsrxx.getSbsxDm1();
        final String sbrq1 = nsrxx.getSbrq1();
        final String xzqhszDm = nsrxx.getXzqhszDm();

        //查询纳税人基本信息
        final ZnsbMhzcQyjbxxmxReqVO reqVO = new ZnsbMhzcQyjbxxmxReqVO();
        reqVO.setDjxh(djxh);
        reqVO.setNsrsbh(nsrxx.getNsrsbh());
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVO = nsrxxApi.getNsrxxByNsrsbh(reqVO);
        final JbxxmxsjVO nsrJbxxVO = nsrxxVO.getData().getJbxxmxsj().get(0);
        final DJNsrxxVO djNsrxx = new DJNsrxxVO();
        BeanUtils.copyBean(nsrJbxxVO,nsrJbxxVO);

        List<QysdsCzzsYjdQcxxItemVO> qcxxItemVOList = new ArrayList<QysdsCzzsYjdQcxxItemVO>();
        QysdsCzzsYjdQcxxItemVO qcxxItemVO = new QysdsCzzsYjdQcxxItemVO();
        this.loadCwgzPageData(djNsrxx, ywbw, qcxxItemVO, skssqq, skssqz, null, null, sblxDm, sbrq1, xzqhszDm);
        qcxxItemVOList.add(qcxxItemVO);
        return qcxxItemVOList;
    }
    /**
     *
     *@name    中文名称
     *@description 相关说明 组装返回数据
     *@time    创建时间:2018-5-7下午02:39:36
     *@param skssqq 税款所属期起
     *@param skssqz 税款所属期止
     *@param sblxDm 申报类型代码
     *@param djxh 登记序号
     *@param objArr 组装对象
     *@return 组装数据
     *<AUTHOR> 段敏
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public List<QysdsCzzsYjdQcxxItemVO> packageCwgzResData(String djxh, String skssqq, String skssqz, String sblxDm, Object[] objArr) {
        log.info("企业所得税月季度查账征收乐企辅助平台错误更正初始化数据TO页面数据转换开始, resp---" + objArr);
        List<QysdsCzzsYjdQcxxItemVO> itemVOList = new ArrayList<>();
        QysdsCzzsYjdQcxxItemVO itemVO = new QysdsCzzsYjdQcxxItemVO();

        ZnsbMhzcQyjbxxmxReqVO nsrxxCxReq = new ZnsbMhzcQyjbxxmxReqVO();
        nsrxxCxReq.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByDjxh(nsrxxCxReq);
        JbxxmxsjVO jbxxVo = nsrxxResult.getData().getJbxxmxsj().get(0);
        final DJNsrxxVO djNsrxx = BeanUtils.toBean(jbxxVo, DJNsrxxVO.class);

        itemVO = this.packageResData(djNsrxx, skssqq, skssqz, objArr, itemVO, true);
        int commCount = (Integer) objArr[0];
        final List<SBSbxxJhVO> sbxxList = (List<SBSbxxJhVO>) objArr[1];
        itemVO.setNsqxDm(GyUtils.isNull(sbxxList) ? "" : sbxxList.get(0).getNsqxDm());

        final SBJmQysdsCzzsYjd2021CshQtxxVO qtxx = (SBJmQysdsCzzsYjd2021CshQtxxVO) objArr[7];
        final SBJmQysdsCzzsYjd2021SqxxVO sqxx = (SBJmQysdsCzzsYjd2021SqxxVO) objArr[8];
        final Jmczyjdsb2021Ywbw ywbw = (Jmczyjdsb2021Ywbw) objArr[++commCount];
        final boolean changeQylx = this.handleCwgzQylxChange(ywbw, qtxx);
        this.loadCwgzPageData(djNsrxx, ywbw, itemVO, skssqq, skssqz, qtxx, sqxx, sblxDm,null,null);
        itemVO.setKzztdjlxDm(djNsrxx.getKzztdjlxDm());
        itemVO.setZfjglxDm(djNsrxx.getZfjglxDm());
        itemVO.setHyDm(djNsrxx.getHyDm());
        itemVO.setQylxGb( changeQylx ? "Y" : "N");
        itemVOList.add(itemVO);
        return itemVOList;
    }
    /**
     *@name    处理错误更正时企业类型变化
     *@description 相关说明
     *@time    创建时间:2021年4月27日上午10:34:22
     *@param ywbw 业务报文
     *@param qtxx 其他信息
     *@return true或false
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private boolean handleCwgzQylxChange(Jmczyjdsb2021Ywbw ywbw, SBJmQysdsCzzsYjd2021CshQtxxVO qtxx) {
        final A200000Ywbd a200000 = ywbw.getA200000Ywbd();
        if (GyUtils.isNull(a200000)) {
            return false;
        }

        boolean ret = false;
        if (GyUtils.isNull(a200000.getNsrxx().getSbqylx()) || !a200000.getNsrxx().getSbqylx().equals(qtxx.getSbqylx())) {
            if ("0".equals(qtxx.getSbqylx())) {
                // 一般企业，清空总分机构金额
                a200000.getSbxx().setZjgybtsdseBq(0d);
                a200000.getSbxx().setZjgftbl(0d);
                a200000.getSbxx().setZjgyftsdseBq(0d);
                a200000.getSbxx().setZjgczjzftbl(0d);
                a200000.getSbxx().setCzjzfpsdseBq(0d);
                a200000.getSbxx().setFzjgftbl(0d);
                a200000.getSbxx().setDlscjybmftbl(0d);
                a200000.getSbxx().setZjgdlscjybmyftsdseBq(0d);
                a200000.getSbxx().setFpbl(0d);
                a200000.getSbxx().setFzjgfpsdseBq(0d);
            }

            a200000.getNsrxx().setSbqylx(qtxx.getSbqylx());
            ret = true;
        }

        if (GyUtils.isNull(a200000.getNsrxx().getYjfs()) || !a200000.getNsrxx().getYjfs().equals(qtxx.getYjfs())) {
            a200000.getNsrxx().setYjfs(qtxx.getYjfs());
        }

        return ret;
    }

    /**
     *@name    错误更正加载页面数据
     *@description 相关说明
     *@time    创建时间:2021年3月9日上午9:08:01
     *@param djNsrxx 登记纳税人信息
     *@param ywbw 业务报文
     *@param qcxxItemVO 返回数据
     *@param skssqq 税款属期起
     *@param skssqz 税款属期止
     *@param qtxx 其他信息
     *@param sqxx 上期信息
     *@param sblxDm 申报类型
     *@param sbrq1 申报日期
     *@param xzqhDm 行政区划代码
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadCwgzPageData(final DJNsrxxVO djNsrxx, final Jmczyjdsb2021Ywbw ywbw, final QysdsCzzsYjdQcxxItemVO qcxxItemVO, final String skssqq, final String skssqz,
                                  final SBJmQysdsCzzsYjd2021CshQtxxVO qtxx, SBJmQysdsCzzsYjd2021SqxxVO sqxx, final String sblxDm, final String sbrq1, final String xzqhDm){
        final A200000Ywbd a200000 = ywbw.getA200000Ywbd();
        if (GyUtils.isNull(a200000)) {
            return;
        }

        // 加载主表表头
        a200000.getNsrxx().setNsrsbh(djNsrxx.getNsrsbh());
        a200000.getNsrxx().setNsrmc(djNsrxx.getNsrmc());
        a200000.getNsrxx().setSkssqq(skssqq);
        a200000.getNsrxx().setSkssqz(skssqz);
        a200000.getNsrxx().setSbsxDm1(sblxDm);
        a200000.getNsrxx().setSbrq1(sbrq1);
        if(GyUtils.isNotNull(a200000.getMssrGrid())){
            for (int i = 0; i < a200000.getMssrGrid().getMssrGridlb().size(); i++) {
                final String thswsx = a200000.getMssrGrid().getMssrGridlb().get(i).getYhswsx();
                if ("MSSR999".equals(thswsx) || "JJSR999".equals(thswsx)) {
                    if (GyUtils.isNotNull(a200000.getMssrGrid().getMssrGridlb().get(i).getSsjmxzDm())
                            && "0004129999".equals(a200000.getMssrGrid().getMssrGridlb().get(i).getSsjmxzDm())) {
                        a200000.getMssrGrid().getMssrGridlb().get(i).setSsjmxzDm(null);
                    }
                }
            }
        }
        if(GyUtils.isNotNull(a200000.getJmsdGrid())){
            for (int i = 0; i < a200000.getJmsdGrid().getJmsdGridlb().size(); i++) {
                final String jm = a200000.getJmsdGrid().getJmsdGridlb().get(i).getYhswsx();
                if (GyUtils.isNotNull(jm) && "JMSE99999".equals(jm)) {
                    if ("0004129999".equals(a200000.getJmsdGrid().getJmsdGridlb().get(i).getSsjmxzDm())) {
                        a200000.getJmsdGrid().getJmsdGridlb().get(0).setSsjmxzDm(null);
                    }
                }
            }
        }

        if (GyUtils.isNotNull(xzqhDm)) {
            a200000.getNsrxx().setXzqhszDm(xzqhDm);  //不为空的时，将值赋值到nsrx中。因错误更正时，前台页面选择行政区划代码时，会传值过来。
        }
        qcxxItemVO.setNsrxxForm(BeanUtil.beanToMap(a200000.getNsrxx()));
        this.loadA200000(a200000, qtxx, sqxx, qcxxItemVO);
        //加载 A201020
        this.loadA201020(ywbw.getA201020Ywbd(), sqxx, qcxxItemVO, skssqq, skssqz);
        //加载 A202000
        this.loadA202000(ywbw.getA202000Ywbd(), qcxxItemVO);
        // 加载参股外国企业报告表
//        this.loadCgwgqybgb(ywbw.getCgwgqyxxbgbYwbd(), qcxxItemVO);
        // 加载递延纳税备案表
        this.loadDynsbab(ywbw.getDynsbabYwbd(), qcxxItemVO);
    }

    /**
     *@name    加载A200000表单
     *@description 相关说明
     *@time    创建时间:2021年3月9日上午9:11:46
     *@param a200000 主表业务表单
     *@param qtxx 其他信息
     *@param sqxx 上期信息
     *@param qcxxItemVO 输出的数据
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadA200000(A200000Ywbd a200000, SBJmQysdsCzzsYjd2021CshQtxxVO qtxx, SBJmQysdsCzzsYjd2021SqxxVO sqxx, final QysdsCzzsYjdQcxxItemVO qcxxItemVO){
        // 纳税人由总机构变成非总分机构时，正常申报时为总机构，错误更正时为非总分机构
        final JmCzzsYjd2021SbxxFormVO sbxxForm = a200000.getSbxx();
        final String sbqylx = a200000.getNsrxx().getSbqylx();
        final String zgswjgDm = a200000.getQtxx().getZgswjg();

        if ("1".equals(sbqylx) && GyUtils.isNotNull(qtxx) && "0".equals(qtxx.getSbqylx())) {
            sbxxForm.setZjgczjzftbl(0.25);
            sbxxForm.setCzjzfpsdseBq(0.0);
            sbxxForm.setDlscjybmftbl(0.0);
            sbxxForm.setZjgdlscjybmyftsdseBq(0.0);
            sbxxForm.setZjgftbl(0.25);
            sbxxForm.setZjgybtsdseBq(0.0);
            sbxxForm.setZjgyftsdseBq(0.0);
        }

        final Map<String, Object> sbxxMap = BeanUtil.beanToMap(sbxxForm);
        qcxxItemVO.setXwxgxxForm(this.modifyCwgzXwxgxx(sqxx, sbxxMap, a200000.getNsrxx().getSkssqz()));
//        qcxxItemVO.setQtxxForm(a200000.getQtxx());

        final List<String> sbxx1List = Arrays.asList("yysrLj", "yycbLj", "lrzeLj", "tdywjsdynssdeLj", "bzssrLj",
                "gdzcjszjkctjeLj", "mssrLj");
        final Map<String, Object> sbxx1Map = new HashMap<String, Object>();
        final List<String> sbxx2List = Arrays.asList("sdjmLj");
        final Map<String, Object> sbxx2Map = new HashMap<String, Object>();
        final List<String> sbxx3List = Arrays.asList("mbyqndksLj", "sjlreLj", "slLj", "ynsdseLj", "jmsdseLj");
        final Map<String, Object> sbxx3Map = new HashMap<String, Object>();
        final List<String> sbxx4List = Arrays.asList("sjyyjsdseLj", "tdywyjzsdseLj", "yhjnsds", "fhtjxwqyyhjzsdseLj", "bhzsm",
                "ybtsdseLj", "zjgybtsdseBq", "zjgftbl", "zjgyftsdseBq", "zjgczjzftbl",
                "czjzfpsdseBq", "fzjgftbl", "dlscjybmftbl", "zjgdlscjybmyftsdseBq", "fpbl",
                "fzjgfpsdseBq", "zyjsrsjynseBq", "dfjsrynseBq", "jzmzlx", "jzfd",
                "mzzzdqdfjmBq", "mzzzdqdfjmLj", "mzzzdqdfjmzfjghjLj", "dfjsrsjynseBq", "sjybtsdseBq");
        final Map<String, Object> sbxx4Map = new HashMap<String, Object>();

        for (Map.Entry<String, Object> item : sbxxMap.entrySet()) {
            final String key = item.getKey();
            if (sbxx1List.contains(key)) {
                sbxx1Map.put(key, item.getValue());
            } else if (sbxx2List.contains(key)) {
                sbxx2Map.put(key, item.getValue());
            } else if (sbxx3List.contains(key)) {
                sbxx3Map.put(key, item.getValue());
            } else if (sbxx4List.contains(key)) {
                if ("jzmzlx".equals(key) && (GYSbSdsUtils.isXz(zgswjgDm) || GYSbSdsUtils.isNm(zgswjgDm))) {
                    final boolean isXz = GYSbSdsUtils.isXz(zgswjgDm);
                    final boolean isNm = GYSbSdsUtils.isNm(zgswjgDm);

                    // 西藏、内蒙调整减征免征类型
                    final String jzmzlx = (String) item.getValue();
                    if (GyUtils.isNotNull(jzmzlx) && Arrays.asList("3", "4", "5").contains(jzmzlx)) {
                        if ("3".equals(jzmzlx) && isNm) {
                            // "3"："内蒙小微地方分享优惠"
                            sbxx4Map.put("jzmzlx", "2");
                            sbxx4Map.put("dqjmxz", "Y");
                        } else if (("4".equals(jzmzlx) || "5".equals(jzmzlx)) && isXz) {
                            // "4"：西藏地方分享优惠(免征)"；"5"： caption="西藏地方分享优惠(减征)"
                            sbxx4Map.put("jzmzlx", "4".equals(jzmzlx) ? "2" : "1");
                            sbxx4Map.put("dqjmxz", "Y");
                        }
                    } else {
                        sbxx4Map.put(key, item.getValue());
                    }

                } else {
                    sbxx4Map.put(key, item.getValue());
                }
            }
        }
        qcxxItemVO.setSbxx1Form(sbxx1Map);
        qcxxItemVO.setSbxx2Form(sbxx2Map);
        qcxxItemVO.setSbxx3Form(sbxx3Map);
        qcxxItemVO.setSbxx4Form(sbxx4Map);

        // 加载优惠信息
        this.loadMssrMx(qcxxItemVO, GyUtils.isNull(sqxx)||GyUtils.isNull(sqxx.getMssrGrid()) ? null : sqxx.getMssrGrid().getMssrGridlb(),
                GyUtils.isNull(a200000.getMssrGrid()) ? null : a200000.getMssrGrid().getMssrGridlb(), true);
        this.loadSdjmMx(qcxxItemVO, GyUtils.isNull(a200000.getSdjmGrid()) ? null : a200000.getSdjmGrid().getSdjmGridlb());
        this.loadJmsdMx(qcxxItemVO, GyUtils.isNull(a200000.getJmsdGrid()) ? null : a200000.getJmsdGrid().getJmsdGridlb());

        // 加载附报事项
        final List<JmCzzsYjd2021FbsxVO> fbsxList = GyUtils.isNull(a200000.getFbsxGrid()) ? null : a200000.getFbsxGrid().getFbsxGridlb();
        this.loadFbsx(fbsxList, GyUtils.isNull(sqxx)||GyUtils.isNull(sqxx.getFbsxGrid()) ? null : sqxx.getFbsxGrid().getFbsxGridlb(), qcxxItemVO, a200000.getNsrxx().getSkssqq());
    }

    /**
     *@name    加载A201020表单
     *@description 相关说明
     *@time    创建时间:2021年3月9日上午9:11:46
     *@param a201020 主表业务表单
     *@param sqxx 上期信息
     *@param itemVO 输出的数据
     *@param skssqq 税款所属期起
     *@param skssqz 税款所属期止
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadA201020(A201020Ywbd a201020, SBJmQysdsCzzsYjd2021SqxxVO sqxx, QysdsCzzsYjdQcxxItemVO itemVO, String skssqq, String skssqz) {
        final String colNames = "bnxsyhdzcyz,bnljzjkcjedzz,bnljzjkcjedaz,bnljzjkcjedxs,bnljzjkcjedns,bnljzjkcjedjs";
        final String[] colArr = colNames.split(",");
        if (!GYObjectUtils.isNull(a201020) && !GYObjectUtils.isNull(a201020.getGdzcjszjkcMxbGrid())) {
            final List<Map<String, Object>> yhmxbList = new ArrayList<Map<String, Object>>();
            final List<JmCzzsYjd2021GdzcjszjkcmxbVO> gdzcjszjkcmxbList = a201020.getGdzcjszjkcMxbGrid().getGdzcjszjkcmxbGridlb();
            final List<JmCzzsYjd2021GdzcjszjkcmxbVO> sqList =
                    !GYObjectUtils.isNull(sqxx) && !GYObjectUtils.isNull(sqxx.getGdzcjszjkcMxbGrid()) ? sqxx.getGdzcjszjkcMxbGrid().getGdzcjszjkcMxbGridlb() : null;
            for (JmCzzsYjd2021GdzcjszjkcmxbVO mxbVO : gdzcjszjkcmxbList) {
                final String ewbhgjz = !GYObjectUtils.isNull(mxbVO.getEwbhgjz()) ? mxbVO.getEwbhgjz() : "";
                final String ssjmxzDm = !GYObjectUtils.isNull(mxbVO.getSsjmxzDm()) ? mxbVO.getSsjmxzDm() : "";
                final String yhswsx = !GYObjectUtils.isNull(mxbVO.getYhswsx()) ? mxbVO.getYhswsx() : "";
                final String dqKey = ssjmxzDm + yhswsx + ewbhgjz;
                final Map<String, Object> yhmxbMap = GYSbSdsUtils.beanToMap(mxbVO);
                if (!GYObjectUtils.isNull(yhswsx)) {
                    if ("JSZJ0010".equals(yhswsx)) {
                        yhmxbMap.put("xm", yhswsx);
                    } else {
                        yhmxbMap.put("xm",ssjmxzDm + "-" + yhswsx);
                    }
                }
                //构造上期相关数据
                if (!GYObjectUtils.isNull(sqList)){
                    for (JmCzzsYjd2021GdzcjszjkcmxbVO sqVO : sqList) {
                        final String sqewbhgjz = !GYObjectUtils.isNull(sqVO.getEwbhgjz()) ? sqVO.getEwbhgjz() : "";
                        final String sqssjmxzDm = !GYObjectUtils.isNull(sqVO.getSsjmxzDm()) ? sqVO.getSsjmxzDm() : "";
                        final String sqyhswsx = !GYObjectUtils.isNull(sqVO.getYhswsx()) ? sqVO.getYhswsx() : "";
                        final String sqKey = sqssjmxzDm + sqyhswsx + sqewbhgjz;
                        if(dqKey.equals(sqKey)){
                            final Map<String, Object> sqMap = GYSbSdsUtils.beanToMap(sqVO);
                            for (String colName : colArr) {
                                yhmxbMap.put("sq" + colName, sqMap.get(colName));
                            }
                            yhmxbMap.put("sqsbbz", "Y");
                            break;
                        }
                    }
                }
                yhmxbList.add(yhmxbMap);
            }
            itemVO.setYhmxbGrid(yhmxbList);
        } else {//正常申报时，自动带出上期数据
            if (!GYObjectUtils.isNull(sqxx) && !GYObjectUtils.isNull(sqxx.getGdzcjszjkcMxbGrid())) {
                final List<Map<String, Object>> yhmxbList = new ArrayList<Map<String, Object>>();
                final List<JmCzzsYjd2021GdzcjszjkcmxbVO> gdzcjszjkcmxbList = sqxx.getGdzcjszjkcMxbGrid().getGdzcjszjkcMxbGridlb();
                for (JmCzzsYjd2021GdzcjszjkcmxbVO mxbVO : gdzcjszjkcmxbList) {
                    final Map<String, Object> yhmxbMap = GYSbSdsUtils.beanToMap(mxbVO);
                    if (!GYObjectUtils.isNull(mxbVO.getYhswsx())) {
                        if ("JSZJ0010".equals(mxbVO.getYhswsx())) {
                            yhmxbMap.put("xm", mxbVO.getYhswsx());
                        } else {
                            yhmxbMap.put("xm", mxbVO.getSsjmxzDm() + "-" + mxbVO.getYhswsx());
                        }
                    }
                    for (String colName : colArr) {
                        yhmxbMap.put("sq" + colName, yhmxbMap.get(colName));
                    }
                    yhmxbMap.put("sqsbbz", "Y");
                    yhmxbList.add(yhmxbMap);
                }
                itemVO.setYhmxbGrid(yhmxbList);
            }
        }
        //加载下拉
        final List<Map<String,Object>> jmList = GYSbSdsUtils.get201020jmList(skssqq,skssqz);
        itemVO.setSsjmxzDefault(jmList);
    }

    /**
     * @name 加载A202000表单
     * @description 相关说明
     * @time 创建时间:2021-03-09 下午 14:07:10
     * @param a202000 a202000
     * @param itemVO
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadA202000(A202000Ywbd a202000, QysdsCzzsYjdQcxxItemVO itemVO) {
        if (GyUtils.isNull(a202000) || GyUtils.isNull(a202000.getZjgxxForm())) {
            return;
        }
        itemVO.setZjgForm(a202000.getZjgxxForm());


        if (GyUtils.isNull(a202000.getFzjgxxGrid())) {
            return;
        }
        itemVO.setFzjgGrid(a202000.getFzjgxxGrid().getFzjgxxGridlb());
    }

    /**
     *@name    加载递延纳税备案表
     *@description 相关说明
     *@time    创建时间:2021年3月10日上午9:58:02
     *@param ywbd 业务表单
     *@param itemVO 返回数据
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private void loadDynsbab(DynsbabYwbd ywbd,  QysdsCzzsYjdQcxxItemVO itemVO) {
        if (GyUtils.isNull(ywbd) || GyUtils.isNull(ywbd.getDynsbabGrid())) {
            return;
        }
        itemVO.setDynsbabGrid(ywbd.getDynsbabGrid().getDynsbabGridlb());
    }

    /**
     *@name    为错误更正修正数据（网报的数据在当前季度后的季度数据为0）
     *@description 相关说明
     *@time    创建时间:2020年7月16日上午10:32:14
     *@param sqxx 其他信息
     *@param bdMap 表单信息map
     *@param skssqz 税款所属期止
     *@return 修正后的数据
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    protected Map<String, Object> modifyCwgzXwxgxx(final SBJmQysdsCzzsYjdSqxxVO sqxx, final Map<String, Object> bdMap, final String skssqz){
        final int jds = GYSbSdsUtils.getJdFromSq(skssqz);
        if (jds < 1 || jds > 4) {
            return bdMap;
        }

        final Map<String, Object> retMap = new HashMap<String, Object>(bdMap);
        if (GyUtils.isNotNull(sqxx)) {
            final Map<String, Object> qtxxMap = BeanUtil.beanToMap(sqxx);
            // 处理本季度前的未申报季度
            for (int j = 1; j < jds; j++) {
                final Object sqQccyrs = qtxxMap.get("qccyrs" + j);
                if (GyUtils.isNull(sqQccyrs)) {
                    retMap.put("qccyrs" + j, null);
                    retMap.put("qmcyrs" + j, null);
                    retMap.put("qczcze" + j, null);
                    retMap.put("qmzcze" + j, null);
                }
            }
        }

        // 处理本季度后的季度
        for (int i = jds + 1; i <= 4; i++) {
            retMap.put("qccyrs" + i, null);
            retMap.put("qmcyrs" + i, null);
            retMap.put("qczcze" + i, null);
            retMap.put("qmzcze" + i, null);
        }

        return retMap;
    }

    /**
     *@name    筛选减免所得优惠事项
     *@description 相关说明
     *@time    创建时间:2022年9月6日上午11:03:50
     *@param skssqz 税款所属期止
     *@param jmsdList 减免所得列表
     *@return 筛选结果
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private List<Map<String, Object>> sxJmsdYhxs(final String skssqz, List<Map<String, Object>> jmsdList) {
        if(GYSbSdsUtils.getNdFromSq(skssqz) < 2022 || GYSbSdsUtils.compareRq(skssqz, "2022-09-30")>=0){
            return jmsdList;
        }

        final Iterator<Map<String, Object>> jmsdIt = jmsdList.iterator();
        while(jmsdIt.hasNext()){
            final Map<String, Object> jmsdMap = jmsdIt.next();
            final String code = (String) jmsdMap.get("code");
            if("JMSE00610".equals(code)){
                jmsdIt.remove();
                break;
            }
        }
        return jmsdList;
    }

    /**
     *@name    筛选免税收入优惠事项
     *@description 相关说明
     *@time    创建时间:2021年7月14日上午11:03:50
     *@param skssqz 税款所属期止
     *@param mssrList 免税收入列表
     *@return 筛选结果
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private List<Map<String, Object>> sxMssrYhsx(final String skssqz, List<Map<String, Object>> mssrList) {
        final int yf = GYSbSdsUtils.getYfFromSq(skssqz);
        final int nd = GYSbSdsUtils.getNdFromSq(skssqz);
        //将mssrList排序
        mssrList.sort(Comparator.comparing(map -> (String)map.get("code")));

        //2023年起2季度6月或3季度9月可调整加计扣除，其他时间3季度9月才可调整加计扣除
        if (GYObjectUtils.isNull(mssrList) || ((nd>=2023 && yf == 6) ||  yf == 9)) {
            return mssrList;
        }
        final Iterator<Map<String, Object>> mssrIt0 = mssrList.iterator();

        if(!(nd < 2022) && !(GYSbSdsUtils.compareRq(skssqz, "2022-09-30")>=0)) {
            while (mssrIt0.hasNext()) {
                final Map<String, Object> mssrMap = mssrIt0.next();
                final String code = (String) mssrMap.get("code");
                if ("MSSR100".equals(code)) {
                    mssrIt0.remove();
                    break;
                }
            }
        }

        final boolean qcJjkc = (nd <= 2022 && yf < 9) || (nd >=2023 && yf < 9 && yf !=7 && yf != 8); // 9/6月前不能选加计扣除优惠
        final boolean forSq = yf > 9 || (nd>=2023 && (yf ==7 || yf  == 8)); // 9/6月后若上期没有就不显示加计扣除项
        boolean blJjkcZxx = false; // 是否保留加计扣除主选项（只有9月后且上期没有选加计扣除优惠时生效）
        Iterator<Map<String, Object>> mssrIt = mssrList.iterator();
        while (mssrIt.hasNext()) {
            final Map<String, Object> mssrMap = mssrIt.next();
            final String code = (String) mssrMap.get("code");
            final String pcode = (String) mssrMap.get("pcode");
            if (qcJjkc && ("0003".equals(code) || "0003".equals(pcode))) {
                mssrIt.remove();
            }

            if (forSq && "0003".equals(pcode)) {
                if (mssrMap.containsKey("sqyhjmje") || "JJKC041".equals(code)) {
                    blJjkcZxx = true;
                } else {
                    mssrIt.remove();
                }
            }
        }
        if (!blJjkcZxx) {
            mssrIt = mssrList.iterator();
            while (mssrIt.hasNext()) {
                final Map<String, Object> mssrMap = mssrIt.next();
                final String code = (String) mssrMap.get("code");
                if ("0003".equals(code)) {
                    mssrIt.remove();
                }
            }
        }

        return mssrList;
    }

    /**
     *@name    添加优惠信息的上期数据
     *@description 相关说明
     *@time    创建时间:2021年5月13日下午4:26:19
     *@param yhxxList 优惠信息列表
     *@param sqMap 上期数据
     *@return 处理后的优惠信息
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private List<Map<String, Object>> addYhxxSqsj(final List<Map<String, Object>> yhxxList, Map<String, Map<String, Object>> sqMap) {
        if (GYObjectUtils.isNull(yhxxList) || GYObjectUtils.isNull(sqMap)) {
            return yhxxList;
        }

        for (Map<String, Object> yhxxMap : yhxxList) {
            final String yhswsx = (String) yhxxMap.get("code");
            if (sqMap.containsKey(yhswsx)) {
                yhxxMap.putAll(sqMap.get(yhswsx));
            }
        }

        return yhxxList;
    }

    /**
     *
     *@name    中文名称  根据总分机构类别和预缴方式加载下拉数
     *@description 相关说明
     *@time    创建时间:2021-2-28上午11:33:14
     *@param yjfsDm 预缴方式代码
     *@param zfjglb 总分机构类别 0正常企业，1按比例预缴的二级分支机构，2只申报不缴纳的企业，3非跨地区转移的汇总企业总机构，4分支机构，5跨省或者跨市或者跨县区的汇总企业总机构
     *@param sfCzfzjg 是否存在分支机构
     *@param skssqz 税款所属期止
     *@return 主附表树
     *<AUTHOR> 段敏
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    protected List<Map<String, String>> assembleZfbTree(String yjfsDm, String zfjglb, boolean sfCzfzjg, String skssqz) {
        final List<Map<String, String>> zfbTree = new ArrayList<Map<String, String>>();
        //构造公共表单
        final Map<String, String> node00 = new HashMap<String, String>();
        node00.put("code", "A200000");
        node00.put("caption", "A200000中华人民共和国企业所得税月（季）度预缴纳税申报表（A类）");
        node00.put("opened", "Y");
        zfbTree.add(node00);

        if (!"4".equals(zfjglb)) {
            final Map<String, String> a201020 = new HashMap<String, String>();
            a201020.put("code", "A201020");
            a201020.put("caption", "A201020资产加速折旧摊销(扣除)优惠明细表");
            a201020.put("opened", "N");
            zfbTree.add(a201020);
        }

        //A200000选择“跨地区经营汇总纳税企业”时，A202000表为必报表
        if ("5".equals(zfjglb) && sfCzfzjg) {
            final Map<String, String> a202000 = new HashMap<String, String>();
            a202000.put("code", "A202000");
            a202000.put("caption", "A202000企业所得税汇总纳税分支机构所得税分配表");
            a202000.put("opened", "N");
            zfbTree.add(a202000);
        }

        final Date sbsqz = DateUtil.toDate("yyyy-MM-dd", skssqz);
        final Date qxz = DateUtil.toDate("yyyy-MM-dd", "2023-12-01");
        //XQ20231253,2023年第四季度或12月申报不再填报《居民企业参股外国企业信息报告表》
        if(sbsqz.before(qxz)) {
            final Map<String, String> cwgwqyxxMap = new HashMap<String, String>();
            cwgwqyxxMap.put("code", "cgwgqybgb");
            cwgwqyxxMap.put("caption", "居民企业参股外国企业信息报告表");
            cwgwqyxxMap.put("opened", "N");
            zfbTree.add(cwgwqyxxMap);
        }

        final Map<String, String> dynsMap = new HashMap<String, String>();
        dynsMap.put("code", "dynsbab");
        dynsMap.put("caption", "技术成果投资入股企业所得税递延纳税备案表");
        dynsMap.put("opened", "N");
        zfbTree.add(dynsMap);
        return zfbTree;
    }

    /**
     *
     *@name    组装业务报文方法
     *@Description 相关说明
     *@Time    创建时间:2021-2-28下午02:26:18
     *@param req
     *@return 业务报文
     *<AUTHOR>  段敏
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    protected Jmczyjdsb2021Ywbw assembleYwbw(QysdsczzsYjdSbbcRequestVO req){
        final Jmczyjdsb2021Ywbw ywbw = new Jmczyjdsb2021Ywbw();
        super.assembleYwbw(req);
        ywbw.setA200000Ywbd(this.assembleA200000(req));
//        ywbw.setCgwgqyxxbgbYwbd(this.assembleCgwgqyxxbgbYwbd(req));
        ywbw.setDynsbabYwbd(this.assembleDynsbab(req));
        ywbw.setA202000Ywbd(this.assembleA202000(req));
        ywbw.setA201020Ywbd(this.assembleA201020(req));
        return ywbw;
    }

    /**
     *@name    组装A200000的业务数据
     *@description 相关说明
     *@time    创建时间:2021年3月5日上午10:09:00
     *@param req 输入数据
     *@return 200000的业务数据
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private A200000Ywbd assembleA200000(QysdsczzsYjdSbbcRequestVO req){
        final A200000Ywbd a200000 = new A200000Ywbd();
        final JmCzzsYjd2021NsrxxFormVO nsrxx = req.getNsrxx();// 主表的表头信息
        final JmCzzsYjd2021QtxxFormVO qtxx = req.getQtxx(); //其他信息
        final JmCzzsYjd2021SbxxFormVO sbxx = req.getSbxx(); //申报信息
        final List<JmCzzsYjd2021FbsxVO> fbsxList =req.getFbsxList();//附报事项信息集合
        final boolean isXz = GYSbSdsUtils.isXz(req.getZgswskfjDm());//西藏
        final boolean isNm = GYSbSdsUtils.isNm(req.getZgswskfjDm());//内蒙
        if ((isXz || isNm) && GyUtils.isNotNull(req.getDqjmxz())) {
            // 西藏、内蒙调整减征免征类型
            final String jzmzlx = sbxx.getJzmzlx();
            if (GyUtils.isNotNull(jzmzlx) && !"0".equals(jzmzlx) && isNm) {
                // "3"："内蒙小微地方分享优惠"
                sbxx.setJzmzlx("3");
            } else if (GyUtils.isNotNull(jzmzlx) && !"0".equals(jzmzlx) && isXz) {
                // "4"：西藏地方分享优惠(免征)"；"5"："西藏地方分享优惠(减征)"
                sbxx.setJzmzlx("2".equals(jzmzlx) ? "4" : "5");
            }
        }

        final int jds = GYSbSdsUtils.getJdFromSq(nsrxx.getSkssqz());
        if(jds >= 1 && jds <= 4) {
            final Map<String, Object> sbxxMap = BeanUtil.beanToMap(sbxx);
            sbxx.setQccyrs(GYSbSdsUtils.cast2Long(sbxxMap.get("qccyrs" + jds)));
            sbxx.setQmcyrs(GYSbSdsUtils.cast2Long(sbxxMap.get("qmcyrs" + jds)));
            sbxx.setQczcze(GYSbSdsUtils.cast2Double(sbxxMap.get("qczcze" + jds)));
            sbxx.setQmzcze(GYSbSdsUtils.cast2Double(sbxxMap.get("qmzcze" + jds)));
        }

        a200000.setSbxx(sbxx);
        a200000.setNsrxx(nsrxx);
        a200000.setQtxx(qtxx);

        if (GyUtils.isNotNull(fbsxList)) {
            //循环设置金额如果为空默认为0
            fbsxList.forEach(fbsx -> {
                if (GyUtils.isNull(fbsx.getJehxxz())){
                    fbsx.setJehxxz("0");
                }
            });
            final A200000Ywbd.FbsxGrid fbsxGrid = new A200000Ywbd.FbsxGrid();
            fbsxGrid.setFbsxGridlb(fbsxList);
            a200000.setFbsxGrid(fbsxGrid);
        }else{
            //乐企转换报文时需要放置空节点
            final A200000Ywbd.FbsxGrid fbsxGrid = new A200000Ywbd.FbsxGrid();
            List<com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.sb228.JmCzzsYjd2021FbsxVO> fbsxGridlb = new ArrayList<>();
            fbsxGrid.setFbsxGridlb(fbsxGridlb);
            a200000.setFbsxGrid(fbsxGrid);
        }

        final List<JmCzzsYjd2021YhjmxxVO> mssrList = req.getMssrList();
        if (GyUtils.isNotNull(mssrList)) {
            final A200000Ywbd.MssrGrid mssrGrid = new A200000Ywbd.MssrGrid();
            mssrGrid.setMssrGridlb(mssrList);
            a200000.setMssrGrid(mssrGrid);
        }else {
            //乐企转换报文时需要放置空节点
            final A200000Ywbd.MssrGrid mssrGrid = new A200000Ywbd.MssrGrid();
            List<com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.sb228.JmCzzsYjd2021YhjmxxVO> mssrGridlb = new ArrayList<>();
            mssrGrid.setMssrGridlb(mssrGridlb);
            a200000.setMssrGrid(mssrGrid);
        }
        final List<JmCzzsYjd2021YhjmxxVO> sdjmList = req.getSdjmList();
        if (GyUtils.isNotNull(sdjmList)) {
            final A200000Ywbd.SdjmGrid sdjmGrid = new A200000Ywbd.SdjmGrid();
            sdjmGrid.setSdjmGridlb(sdjmList);
            a200000.setSdjmGrid(sdjmGrid);
        }else {
            //乐企转换报文时需要放置空节点
            final A200000Ywbd.SdjmGrid sdjmGrid = new A200000Ywbd.SdjmGrid();
            List<com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.sb228.JmCzzsYjd2021YhjmxxVO> sdjmGridlb = new ArrayList<>();
            sdjmGrid.setSdjmGridlb(sdjmGridlb);
            a200000.setSdjmGrid(sdjmGrid);
        }

        final List<JmCzzsYjd2021YhjmxxVO> jmsdList = req.getJmsdList();
        if (GyUtils.isNotNull(jmsdList)) {
            final A200000Ywbd.JmsdGrid jmsdGrid = new A200000Ywbd.JmsdGrid();
            jmsdGrid.setJmsdGridlb(jmsdList);
            a200000.setJmsdGrid(jmsdGrid);
        }else {
            final A200000Ywbd.JmsdGrid jmsdGrid = new A200000Ywbd.JmsdGrid();
            List<com.css.znsb.nssb.pojo.bo.hxzg.qysdsyjdsb.sb228.JmCzzsYjd2021YhjmxxVO> jmsdGridlb = new ArrayList<>();
            jmsdGrid.setJmsdGridlb(jmsdGridlb);
            a200000.setJmsdGrid(jmsdGrid);
        }
        return a200000;
    }

    /**
     *@name    组装递延纳税备案表的业务数据
     *@description 相关说明
     *@time    创建时间:2021年3月5日上午10:09:00
     *@param req 输入数据
     *@return 递延纳税备案表的业务数据
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private DynsbabYwbd assembleDynsbab(QysdsczzsYjdSbbcRequestVO req) {
        final List<DynsbabGridlbVO> dynsbabList= req.getDynsbabList();
        if (GyUtils.isNull(dynsbabList)) {
            return null;
        }
        final DynsbabYwbd dynsbabYwbd = new DynsbabYwbd();
        final DynsbabYwbd.DynsbabGrid fbsix = new DynsbabYwbd.DynsbabGrid();
        fbsix.setDynsbabGridlb(dynsbabList);
        dynsbabYwbd.setDynsbabGrid(fbsix);

        return dynsbabYwbd;
    }

    /**
     *@name    组装A202000的业务数据
     *@description 相关说明
     *@time    创建时间:2021年3月5日上午10:09:00
     *@param req 输入数据
     *@return 202000的业务数据
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private A202000Ywbd assembleA202000(QysdsczzsYjdSbbcRequestVO req) {
        final ZjgxxFormVO zjgxxForm = req.getZjgForm();
        final List<FzjgxxGridlbVO> fzjgxxGridlb = req.getFzjgGrid();
        if (GyUtils.isNotNull(zjgxxForm) && GyUtils.isNotNull(fzjgxxGridlb)) {
            final A202000Ywbd ywbd = new A202000Ywbd();
            ywbd.setZjgxxForm(zjgxxForm);
            final A202000Ywbd.FzjgxxGrid fzjgxxGrid = new A202000Ywbd.FzjgxxGrid();
            if (GyUtils.isNotNull(fzjgxxGridlb)) {
                fzjgxxGrid.setFzjgxxGridlb(fzjgxxGridlb);
                ywbd.setFzjgxxGrid(fzjgxxGrid);
            }
            return ywbd;
        }

        return null;
    }

    /**
     *@name    组装A201020的业务数据
     *@description 相关说明
     *@time    创建时间:2021年3月5日上午10:09:00
     *@param req 输入数据
     *@return A201020的业务数据
     *<AUTHOR>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    private A201020Ywbd assembleA201020(QysdsczzsYjdSbbcRequestVO req) {
        final A201020Ywbd a201020 = new A201020Ywbd();
        final List<JmCzzsYjd2021GdzcjszjkcmxbVO> gdzcjszjkcmxbList = req.getGdzcjszjkcmxbList();
        if (GyUtils.isNotNull(gdzcjszjkcmxbList)) {
            final A201020Ywbd.GdzcjszjkcMxbGrid gdzcjszjkcMxbGrid = new A201020Ywbd.GdzcjszjkcMxbGrid();
            gdzcjszjkcMxbGrid.setGdzcjszjkcmxbGridlb(gdzcjszjkcmxbList);
            a201020.setGdzcjszjkcMxbGrid(gdzcjszjkcMxbGrid);
        }else{
            //组装默认合计行节点并置为0，后续由前台计算并传默认节点实现
            final A201020Ywbd.GdzcjszjkcMxbGrid gdzcjszjkcMxbGrid = new A201020Ywbd.GdzcjszjkcMxbGrid();
            List<JmCzzsYjd2021GdzcjszjkcmxbVO> gdzcjszjkcmxbGridlb = new ArrayList<>();
            JmCzzsYjd2021GdzcjszjkcmxbVO jszjHj = new JmCzzsYjd2021GdzcjszjkcmxbVO();
            jszjHj.setBnxsyhdzcyz(0.0);
            jszjHj.setSsjmxzDm("");
            jszjHj.setEwbhgjz("JSZJ");
            jszjHj.setBnljzjkcjedjs(0.0);
            jszjHj.setYhswsx("");
            jszjHj.setBnljzjkcjedns(0.0);
            jszjHj.setBnljzjkcjedxs(0.0);
            jszjHj.setBnljzjkcjedaz(0.0);
            jszjHj.setEwbhxh(0);
            jszjHj.setBnljzjkcjedzz(0.0);
            gdzcjszjkcmxbGridlb.add(jszjHj);
            JmCzzsYjd2021GdzcjszjkcmxbVO yckcHj = new JmCzzsYjd2021GdzcjszjkcmxbVO();
            yckcHj.setBnxsyhdzcyz(0.0);
            yckcHj.setSsjmxzDm("");
            yckcHj.setEwbhgjz("YCKC");
            yckcHj.setBnljzjkcjedjs(0.0);
            yckcHj.setYhswsx("");
            yckcHj.setBnljzjkcjedns(0.0);
            yckcHj.setBnljzjkcjedxs(0.0);
            yckcHj.setBnljzjkcjedaz(0.0);
            yckcHj.setEwbhxh(0);
            yckcHj.setBnljzjkcjedzz(0.0);
            gdzcjszjkcmxbGridlb.add(yckcHj);
            JmCzzsYjd2021GdzcjszjkcmxbVO hj = new JmCzzsYjd2021GdzcjszjkcmxbVO();
            hj.setBnxsyhdzcyz(0.0);
            hj.setSsjmxzDm("");
            hj.setEwbhgjz("HJ");
            hj.setBnljzjkcjedjs(0.0);
            hj.setYhswsx("");
            hj.setBnljzjkcjedns(0.0);
            hj.setBnljzjkcjedxs(0.0);
            hj.setBnljzjkcjedaz(0.0);
            hj.setEwbhxh(0);
            hj.setBnljzjkcjedzz(0.0);
            gdzcjszjkcmxbGridlb.add(hj);
            gdzcjszjkcMxbGrid.setGdzcjszjkcmxbGridlb(gdzcjszjkcmxbGridlb);
            a201020.setGdzcjszjkcMxbGrid(gdzcjszjkcMxbGrid);
        }
        return a201020;
    }

}
