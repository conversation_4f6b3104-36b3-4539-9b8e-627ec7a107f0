package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ysfwkcxmmxGridlbVO", propOrder = { "ewbhxh", "hmc", "qcye", "qmye", "msxse", "bqykcje", "bqsjkcje", "bqfse" })
@Getter
@Setter
public class YsfwkcxmmxGridlbVO {
    /**
     * 二维表行序号(特殊：营改增新增第4行6%税率的金融商品转让项目-->7;第5行5%征收率的项目-->8)
     */
    protected Long ewbhxh;

    /**
     * 行名称
     */
    @XmlElement(nillable = true, required = true)
    protected String hmc;

    /**
     * 期初余额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qcye;

    /**
     * 期末余额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal qmye;

    /**
     * 免税销售额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal msxse;

    /**
     * 应税服务扣除项目_本期应扣除金额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqykcje;

    /**
     * 应税服务扣除项目_本期实际扣除金额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqsjkcje;

    /**
     * 本期发生额
     */
    @XmlElement(nillable = true, required = true)
    protected BigDecimal bqfse;
}