package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import com.alibaba.fastjson.annotation.JSONField;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《机动车销售统一发票清单》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr_jdcxstyfpqdywbw", propOrder = { "zzssyyybnsrJdcxstyfpqd" })
@Getter
@Setter
public class ZzssyyybnsrJdcxstyfpqdywbw extends TaxDoc {
    /**
     * 机动车销售统一发票清单表单
     */
    @XmlElement(nillable = true, name = "zzssyyybnsr_jdcxstyfpqd", required = true)
    @JSONField(name = "zzssyyybnsr_jdcxstyfpqd")
    protected ZzssyyybnsrJdcxstyfpqd zzssyyybnsrJdcxstyfpqd;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class ZzssyyybnsrJdcxstyfpqd extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.ZzssyyybnsrJdcxstyfpqd {}
}