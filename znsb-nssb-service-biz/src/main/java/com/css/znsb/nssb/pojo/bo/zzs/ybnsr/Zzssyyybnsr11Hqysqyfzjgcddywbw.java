package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import lombok.Getter;
import lombok.Setter;
/**
 * 《航空运输企业分支机构传递单》业务报文
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "zzssyyybnsr11_hqysqyfzjgcddywbw", propOrder = { "hqysqyfzjgcdd" })
@Getter
@Setter
public class Zzssyyybnsr11Hqysqyfzjgcddywbw extends TaxDoc {
    /**
     * 航空运输企业分支机构传递单
     */
    @XmlElement(nillable = true, required = true)
    protected Hqysqyfzjgcdd hqysqyfzjgcdd;

    /**
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    @Getter
    @Setter
    public static class Hqysqyfzjgcdd extends com.css.znsb.nssb.pojo.bo.zzs.ybnsr.Hqysqyfzjgcdd {}
}