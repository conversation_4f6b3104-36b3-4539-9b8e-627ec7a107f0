
package com.css.znsb.nssb.pojo.bo.hxzg.sb.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * 申报纳税人主体数据查询信息对象
 * 
 * <p>Java class for SBNsrxxJhVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SBNsrxxJhVO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="djxh" type="{http://www.chinatax.gov.cn/dataspec/}djxh" minOccurs="0"/>
 *         &lt;element name="skssqq" type="{http://www.chinatax.gov.cn/dataspec/}skssqq" minOccurs="0"/>
 *         &lt;element name="skssqz" type="{http://www.chinatax.gov.cn/dataspec/}skssqz" minOccurs="0"/>
 *         &lt;element name="sbsxDm1" type="{http://www.chinatax.gov.cn/dataspec/}sbsxDm1"/>
 *         &lt;element name="yzpzzlDm" type="{http://www.chinatax.gov.cn/dataspec/}dzbzdszlDm"/>
 *         &lt;element name="sbrq1" type="{http://www.chinatax.gov.cn/dataspec/}sbrq1" minOccurs="0"/>
 *         &lt;element name="pzxh" type="{http://www.chinatax.gov.cn/dataspec/}pzxh" minOccurs="0"/>
 *         &lt;element name="sbuuid" type="{http://www.chinatax.gov.cn/dataspec/}sbuuid" minOccurs="0"/>
 *         &lt;element name="scenceCsForjs" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="xzqhszDm" type="{http://www.chinatax.gov.cn/dataspec/}xzqhszDm" minOccurs="0"/>
 *         &lt;element name="zxbztzsuuid" type="{http://www.chinatax.gov.cn/dataspec/}zxbztzsuuid" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SBNsrxxJhVO", namespace = "http://www.chinatax.gov.cn/dataspec/", propOrder = {
        "djxh",
        "skssqq",
        "skssqz",
        "sbsxDm1",
        "yzpzzlDm",
        "sbrq1",
        "pzxh",
        "sbuuid",
        "scenceCsForjs",
        "xzqhszDm",
        "zxbztzsuuid",
        "tbrq",
        "swjgDm"
})
public class SBNsrxxJhVO
    implements Serializable
{

    private final static long serialVersionUID = 1L;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String djxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String skssqz;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String sbsxDm1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/", required = true)
    protected String yzpzzlDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbrq1;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String pzxh;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String sbuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String scenceCsForjs;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String xzqhszDm;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String zxbztzsuuid;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String tbrq;
    @XmlElement(namespace = "http://www.chinatax.gov.cn/dataspec/")
    protected String swjgDm;

    public String getTbrq() {
        return tbrq;
    }

    public void setTbrq(String tbrq) {
        this.tbrq = tbrq;
    }
    /**
     * Gets the value of the djxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDjxh() {
        return djxh;
    }

    /**
     * Sets the value of the djxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDjxh(String value) {
        this.djxh = value;
    }

    /**
     * Gets the value of the skssqq property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqq() {
        return skssqq;
    }

    /**
     * Sets the value of the skssqq property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqq(String value) {
        this.skssqq = value;
    }

    /**
     * Gets the value of the skssqz property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkssqz() {
        return skssqz;
    }

    /**
     * Sets the value of the skssqz property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkssqz(String value) {
        this.skssqz = value;
    }

    /**
     * Gets the value of the sbsxDm1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbsxDm1() {
        return sbsxDm1;
    }

    /**
     * Sets the value of the sbsxDm1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbsxDm1(String value) {
        this.sbsxDm1 = value;
    }

    /**
     * Gets the value of the yzpzzlDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getYzpzzlDm() {
        return yzpzzlDm;
    }

    /**
     * Sets the value of the yzpzzlDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setYzpzzlDm(String value) {
        this.yzpzzlDm = value;
    }

    /**
     * Gets the value of the sbrq1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbrq1() {
        return sbrq1;
    }

    /**
     * Sets the value of the sbrq1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbrq1(String value) {
        this.sbrq1 = value;
    }

    /**
     * Gets the value of the pzxh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPzxh() {
        return pzxh;
    }

    /**
     * Sets the value of the pzxh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPzxh(String value) {
        this.pzxh = value;
    }

    /**
     * Gets the value of the sbuuid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSbuuid() {
        return sbuuid;
    }

    /**
     * Sets the value of the sbuuid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSbuuid(String value) {
        this.sbuuid = value;
    }

    /**
     * Gets the value of the scenceCsForjs property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getScenceCsForjs() {
        return scenceCsForjs;
    }

    /**
     * Sets the value of the scenceCsForjs property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setScenceCsForjs(String value) {
        this.scenceCsForjs = value;
    }
    
    /**
     * Gets the value of the xzqhszDm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXzqhszDm() {
        return xzqhszDm;
    }

    /**
     * Sets the value of the xzqhszDm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXzqhszDm(String xzqhszDm) {
        this.xzqhszDm = xzqhszDm;
    }

    public String getZxbztzsuuid() {
        return zxbztzsuuid;
    }

    public void setZxbztzsuuid(String zxbztzsuuid) {
        this.zxbztzsuuid = zxbztzsuuid;
    }

    public String getSwjgDm() {
        return swjgDm;
    }

    public void setSwjgDm(String swjgDm) {
        this.swjgDm = swjgDm;
    }
}
