package com.css.znsb.nssb.pojo.bo.zzs.ybnsr;

import com.alibaba.fastjson.annotation.JSONField;
import com.css.znsb.nssb.pojo.dto.fjs.SbFjsfDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsFbZzsjmssbmxbDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsJcdlqywgxpTzDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsXzjjdjzcjgDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFb1BqxsqkmxDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFb2BqjxsemxDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFb3YsfwkcxmDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFb4SedjqkDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbCbfhdncpDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbCpygxcqkDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbCpygxcslDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbDkdjjksdkDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbDlqyxxjxDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbDxqyDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbHdncpjxDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbHkysDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbHznsfpDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbHztycddDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbJyzyfjyxxDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbJyzyxshzDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbNcphdkchzDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbNcpyyscjyDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbNcpzjxsDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbTljsjjDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbTlysDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.SbZzsYbnsrFbYzqyDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.sbbw.YbnsrQtxxDTO;
import com.css.znsb.nssb.pojo.dto.sb.ybnsr.tzxx.YbnsrFpMxDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
@JsonSerialize
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class ZzsYbnsrYtxxBO extends ZzsYbnsrQrsYtxxBO {

    private static final long serialVersionUID = 6649813152753575651L;

    @JSONField(name = "sbFjsf")
    private SbFjsfDTO sbFjsf;
    @JSONField(name = "sbZzsFbZzsjmssbmxb")
    private SbZzsFbZzsjmssbmxbDTO sbZzsFbZzsjmssbmxb;
    @JSONField(name = "sbZzsYbnsr")
    private List<SbZzsYbnsrDTO> sbZzsYbnsr;
    @JSONField(name = "sbZzsYbnsrFb1Bqxsqkmx")
    private List<SbZzsYbnsrFb1BqxsqkmxDTO> sbZzsYbnsrFb1Bqxsqkmx;
    @JSONField(name = "sbZzsYbnsrFb2Bqjxsemx")
    private List<SbZzsYbnsrFb2BqjxsemxDTO> sbZzsYbnsrFb2Bqjxsemx;
    @JSONField(name = "sbZzsYbnsrFb3Ysfwkcxm")
    private List<SbZzsYbnsrFb3YsfwkcxmDTO> sbZzsYbnsrFb3Ysfwkcxm;
    @JSONField(name = "sbZzsYbnsrFb4Sedjqk")
    private List<SbZzsYbnsrFb4SedjqkDTO> sbZzsYbnsrFb4Sedjqk;
    @JSONField(name = "sbZzsYbnsrFbCbfhdncp")
    private List<SbZzsYbnsrFbCbfhdncpDTO> sbZzsYbnsrFbCbfhdncp;
    @JSONField(name = "sbZzsYbnsrFbCpygxcqk")
    private List<SbZzsYbnsrFbCpygxcqkDTO> sbZzsYbnsrFbCpygxcqk;
    @JSONField(name = "sbZzsYbnsrFbCpygxcsl")
    private List<SbZzsYbnsrFbCpygxcslDTO> sbZzsYbnsrFbCpygxcsl;
    @JSONField(name = "sbZzsYbnsrFbDkdjjksdk")
    private List<SbZzsYbnsrFbDkdjjksdkDTO> sbZzsYbnsrFbDkdjjksdk;
    @JSONField(name = "sbZzsYbnsrFbDlqyxxjx")
    private SbZzsYbnsrFbDlqyxxjxDTO sbZzsYbnsrFbDlqyxxjx;
    @JSONField(name = "sbZzsYbnsrFbDxqy")
    private SbZzsYbnsrFbDxqyDTO sbZzsYbnsrFbDxqy;
    @JSONField(name = "sbZzsYbnsrFbHdncpjx")
    private List<SbZzsYbnsrFbHdncpjxDTO> sbZzsYbnsrFbHdncpjx;
    @JSONField(name = "sbZzsYbnsrFbHkys")
    private SbZzsYbnsrFbHkysDTO sbZzsYbnsrFbHkys;
    @JSONField(name = "sbZzsYbnsrFbHznsfp")
    private SbZzsYbnsrFbHznsfpDTO sbZzsYbnsrFbHznsfp;
    @JSONField(name = "sbZzsYbnsrFbHztycdd")
    private SbZzsYbnsrFbHztycddDTO sbZzsYbnsrFbHztycdd;
    @JSONField(name = "sbZzsYbnsrFbJyzyfjyxx")
    private List<SbZzsYbnsrFbJyzyfjyxxDTO> sbZzsYbnsrFbJyzyfjyxx;
    @JSONField(name = "sbZzsYbnsrFbJyzyxshz")
    private List<SbZzsYbnsrFbJyzyxshzDTO> sbZzsYbnsrFbJyzyxshz;
    @JSONField(name = "sbZzsYbnsrFbNcphdkchz")
    private List<SbZzsYbnsrFbNcphdkchzDTO> sbZzsYbnsrFbNcphdkchz;
    @JSONField(name = "sbZzsYbnsrFbNcpyyscjy")
    private List<SbZzsYbnsrFbNcpyyscjyDTO> sbZzsYbnsrFbNcpyyscjy;
    @JSONField(name = "sbZzsYbnsrFbNcpzjxs")
    private List<SbZzsYbnsrFbNcpzjxsDTO> sbZzsYbnsrFbNcpzjxs;
    @JSONField(name = "sbZzsYbnsrFbTljsjj")
    private List<SbZzsYbnsrFbTljsjjDTO> sbZzsYbnsrFbTljsjj;
    @JSONField(name = "sbZzsYbnsrFbTlys")
    private SbZzsYbnsrFbTlysDTO sbZzsYbnsrFbTlys;
    @JSONField(name = "sbZzsYbnsrFbYzqy")
    private SbZzsYbnsrFbYzqyDTO sbZzsYbnsrFbYzqy;
    @JSONField(name = "SbZzsXzjjdjzcjg")
    private SbZzsXzjjdjzcjgDTO sbZzsXzjjdjzcjg;
    @JSONField(name = "sbZzsJcdlqywgxpTz")
    private List<SbZzsJcdlqywgxpTzDTO> sbZzsJcdlqywgxpTz;

    private YbnsrFpMxDTO fpxx;
    private YbnsrQtxxDTO qtxx;

}
