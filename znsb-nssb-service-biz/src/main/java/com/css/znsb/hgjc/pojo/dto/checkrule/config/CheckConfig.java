package com.css.znsb.hgjc.pojo.dto.checkrule.config;

import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYwDO;
import com.css.znsb.hgjc.pojo.dto.sb.QyYwmxDTO;
import lombok.Data;

import java.util.List;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.hgjc.pojo.dto.checkrule.config
 * @file CheckConfig.java 创建时间:2024/6/3 19:17
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 智能申报
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Data
public class CheckConfig {
    private ZnsbHgjcQyYwDO qyYwVo;
    private List<QyYwmxDTO> jkxmList;
    private String reason;
}
