package com.css.znsb.hgjc.mapper;

import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcYwmxDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_ywmx(合规检查业务明细)】的数据库操作Mapper
 * @createDate 2024-04-15 21:32:51
 * @Entity com.css.znsb.hgjc.pojo.domain.ZnsbHgjcYwmx
 */
@Mapper
public interface ZnsbHgjcYwmxMapper extends BaseMapperX<ZnsbHgjcYwmxDO> {

    default List<ZnsbHgjcYwmxDO> queryByYwbh(String ywbh) {
        return this.selectList(new LambdaQueryWrapperX<ZnsbHgjcYwmxDO>()
                .eq(ZnsbHgjcYwmxDO::getYwbh, ywbh)
                .eq(ZnsbHgjcYwmxDO::getYxbz, 'Y')
                .eq(ZnsbHgjcYwmxDO::getQybz, 'Y')
        );
    }
}




