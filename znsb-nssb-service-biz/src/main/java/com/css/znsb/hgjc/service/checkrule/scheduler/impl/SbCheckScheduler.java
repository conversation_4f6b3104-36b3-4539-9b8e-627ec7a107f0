package com.css.znsb.hgjc.service.checkrule.scheduler.impl;

import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.hgjc.constants.HgjcApiConstants;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgDO;
import com.css.znsb.hgjc.pojo.dto.checkrule.checkitem.CheckItemRes;
import com.css.znsb.hgjc.pojo.dto.sb.SbChcekItemConfig;
import com.css.znsb.hgjc.pojo.dto.sb.SbCheckItemReq;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyJcjgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.hgjc.service.sb.impl
 * @file SbCheckScheduler.java 创建时间:2024/4/16 19:29
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 智能申报
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Service
@Slf4j
public class SbCheckScheduler extends AbsCheckScheduler<SbCheckItemReq, SbChcekItemConfig> {

    @Autowired
    protected ZnsbHgjcQyJcjgService jgServce;

    protected void processResults(final SbCheckItemReq req, final List<CheckItemRes> checkItemResList) {
        //归集检查结果
        int jctgsl = 0;//检查通过数量
        int tstxsl = 0;//提示提醒数量
        int zdsl = 0;// 阻断数量
        for (final CheckItemRes res : checkItemResList) {
            if (res.isResult()) {
                jctgsl++;
            } else {
                if (HgjcApiConstants.JKLX_DM_ZD.equals(res.getJklxDm1())) {
                    zdsl++;
                } else {
                    tstxsl++;
                }
            }
        }
        final int jcbtgsl = zdsl + tstxsl;//检查不通过数量
        final int jcsl = checkItemResList.size();

        final String jyjg1;
        if (zdsl > 0) {
            jyjg1 = HgjcApiConstants.JCJG_ZD;
        } else if (tstxsl > 0) {
            jyjg1 = HgjcApiConstants.JCJG_TSTX;
        } else {
            jyjg1 = HgjcApiConstants.JCJG_TG;
        }
        final String msg = this.getMsg(jcsl, jctgsl, jcbtgsl, tstxsl, zdsl);

        final ZnsbHgjcQyJcjgDO jcjgDO = new ZnsbHgjcQyJcjgDO();
        jcjgDO.setJguuid(req.getJguuid());
        jcjgDO.setJcztDm(HgjcApiConstants.JCZT_DM_WCJC);
        jcjgDO.setJyjg1(jyjg1);
        jcjgDO.setJcsl1(String.valueOf(jcsl));
        jcjgDO.setJytgsl(String.valueOf(jctgsl));
        jcjgDO.setJybtgsl(String.valueOf(jcbtgsl));
        jcjgDO.setSjjcjg(msg);
        jcjgDO.setWcsj(DateUtils.getSystemCurrentTime(0));
        jcjgDO.setTstxsl(String.valueOf(tstxsl));
        jcjgDO.setZdsl(String.valueOf(zdsl));
        req.setJcjg(jcjgDO);
        this.log(jcjgDO);

    }

    protected String getMsg(int jcsl, int tgsl, int btgsl, int tstxsl, int zdsl) {
        final StringBuilder sb = new StringBuilder();
        sb.append("共完成").append(jcsl).append("项合规校验，通过了").append(tgsl).append("项合规校验");
        if (tstxsl > 0) {
            sb.append("，存在").append(tstxsl).append("项提示性监控没通过");
        }
        if (zdsl > 0) {
            sb.append("，存在").append(zdsl).append("项阻断性监控没通过");
        }
        return sb.toString();
    }


    protected void log(ZnsbHgjcQyJcjgDO res) {
        this.jgServce.updateResult(res);
    }
}
