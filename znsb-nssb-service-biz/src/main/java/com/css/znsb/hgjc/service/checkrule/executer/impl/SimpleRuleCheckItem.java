package com.css.znsb.hgjc.service.checkrule.executer.impl;

import com.css.znsb.cxtj.api.SimpleQueryApi;
import com.css.znsb.cxtj.pojo.SimpleQueryReq;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.template.TemplateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.hgjc.pojo.dto.sb.SbChcekItemConfig;
import com.css.znsb.hgjc.pojo.dto.sb.SbCheckItemReq;
import com.css.znsb.hgjc.pojo.dto.sb.YwgzpzDTO;
import com.css.znsb.hgjc.utils.ExpressionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.hgjc.biz.checkitem
 * @file DemoSbServiceCheckItem.java 创建时间:2024/4/16 20:14
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 智能申报
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Slf4j
@Component("SimpleRuleCheckItem")
public class SimpleRuleCheckItem extends SbChcekItemExecuter<Object> {

    @Resource
    private SimpleQueryApi simpleQueryApi;

    protected Object getBusinessData(SbCheckItemReq req, SbChcekItemConfig config) {
        final YwgzpzDTO ywgzpzDTO = this.getYwgzpzDTO(config);
        if (GyUtils.isNull(ywgzpzDTO)) {
            return this.getBusinessData(req);
        }
        final Map<String, Object> reqMap = BeanUtils.toMap(req);
        final SimpleQueryReq queryReq = new SimpleQueryReq();
        queryReq.setPath(ywgzpzDTO.getQueryPath());
        queryReq.setParams(reqMap);
        log.info("调用查询接口{},", ywgzpzDTO.getQueryPath());
        return this.simpleQueryApi.query(queryReq);
    }

    protected YwgzpzDTO getYwgzpzDTO(SbChcekItemConfig config) {
        return config.extendInfo(YwgzpzDTO.class);
    }

    @Override
    protected boolean checkRule(final SbChcekItemConfig config, final Object data) {
        //1. 数据转成对象 map举例  尽量使用VO data返回为magic api接口renturn的对象
        final Map<String, Object> map = (Map<String, Object>) data;
        //2.获取规则配置
        final YwgzpzDTO ywgzpzDTO = this.getYwgzpzDTO(config);
        if (GyUtils.isNull(ywgzpzDTO)) {
            return false;
        }
        final String ruleExc = ywgzpzDTO.getRule();
        final boolean execute = (boolean) ExpressionUtils.execute(ruleExc, map);
        return execute;
    }

    protected String getErrorMsg(SbChcekItemConfig config, Object data) {
        final String xxmbnr = config.getXxmbnr();
        if (GyUtils.isNotNull(xxmbnr)) {
            //按照需求编写检查不通过的消息
            return TemplateUtils.parseContent(xxmbnr, data);
        }
        return xxmbnr;
    }
}
