package com.css.znsb.hgjc.pojo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合规检查业务明细
 * @TableName znsb_hgjc_ywmx
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "znsb_hgjc_ywmx")
@Data
public class ZnsbHgjcYwmxDO extends BaseDO {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 业务明细UUID
     */
    @TableId(value = "ywmxuuid")
    private String ywmxuuid;
    /**
     * 业务编号||业务编号
     */
    @TableField(value = "ywbh")
    private String ywbh;
    /**
     * 序号
     */
    @TableField(value = "xh")
    private String xh;
    /**
     * 监控项目UUID||工作流监控项目规则主键
     */
    @TableField(value = "jkxmuuid")
    private String jkxmuuid;
    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;
    /**
     * 启用标志
     */
    @TableField(value = "qybz")
    private String qybz;
    /**
     * 是否可以设置启用标志
     */
    @TableField(value = "szqybz")
    private String szqybz;
}