package com.css.znsb.hgjc.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.css.znsb.nssb.constants.enums.NsqxEnum;
import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

@Slf4j
public class NsqxUtils {
    public static String getNsqxDmBySkssq(Date skssqq, Date skssqz) {
        if (skssqq.equals(skssqz)) {
            return NsqxEnum.TIME.getDm();
        }
        long day = DateUtil.betweenDay(skssqq, skssqz, false);
        if (day == 6) {
            return NsqxEnum.ZHOU.getDm();
        } else if (day == 9) {
            return NsqxEnum.DAY.getDm();
        } else if (day == 14) {
            return NsqxEnum.FIFTEEN_DAY.getDm();
        }
        int monthQ = DateUtil.month(skssqq);
        int monthZ = DateUtil.month(skssqz);
        int month = monthZ - monthQ;
        if (month == 0) {
            return NsqxEnum.MONTH.getDm();
        } else if (month == 2) {
            return NsqxEnum.QUARTER.getDm();
        } else if (month == 5) {
            return NsqxEnum.HALF_YEAR.getDm();
        } else if (month == 11) {
            return NsqxEnum.YEAR.getDm();
        }
        return "";
    }

    public static String getSkssqqByNy(String ny) {
        Calendar cal = Calendar.getInstance();
        cal.set(Integer.parseInt(ny.substring(0, 4)), Integer.parseInt(ny.substring(4, 6)) - 1, 1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(cal.getTime());
    }

    public static String getSkssqzByNy(String ny) {
        Calendar cal = Calendar.getInstance();
        cal.set(Integer.parseInt(ny.substring(0, 4)), Integer.parseInt(ny.substring(4, 6)), 1);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(cal.getTime());
    }

    public static String getSsnyByKjny(String kjny) {
        String date = addMonths(kjny + "01", 1);
        return date.substring(0, 6);
    }

    public static String addMonths(String dateStr, int months) {
        if (months == 0) {
            return dateStr;
        }
        Calendar calendar = Calendar.getInstance();
        String formatter = "";
        if (dateStr.indexOf('-') != -1 && dateStr.length() >= 8) {
            formatter = "yyyy-MM-dd";
            String[] temp = dateStr.split("-");
            calendar.set(Integer.parseInt(temp[0]), Integer.parseInt(temp[1]) - 1, Integer.parseInt(temp[2]));
        } else if (dateStr.indexOf('/') != -1 && dateStr.length() >= 8) {
            formatter = "yyyy/MM/dd";
            String[] temp = dateStr.split("/");
            calendar.set(Integer.parseInt(temp[0]), Integer.parseInt(temp[1]) - 1, Integer.parseInt(temp[2]));
        } else {
            formatter = "yyyyMMdd";
            calendar.set(Integer.parseInt(dateStr.substring(0, 4)), Integer.parseInt(dateStr.substring(4, 6)) - 1,
                    Integer.parseInt(dateStr.substring(6, 8)));
        }
        SimpleDateFormat myFmt = new SimpleDateFormat(formatter);
        calendar.add(Calendar.MONTH, months);
        String timeStr = myFmt.format(calendar.getTime()).toString();
        return timeStr;
    }

    public static boolean isSupportNsqx(String nsqxdm, String kjny) {
        String dqyfMm = getDqyfByKjny(kjny);
        String dqyf = Integer.parseInt(dqyfMm) < 10 ? dqyfMm.substring(1) : dqyfMm;
        if (!isYxsbByNsqx(nsqxdm, dqyf)) {
            return false;
        }
        return true;
    }

    public static String getDqyfByKjny(String kjny) {
        Calendar cal = Calendar.getInstance();
        cal.set(Integer.parseInt(kjny.substring(0, 4)), Integer.parseInt(kjny.substring(4, 6)), 1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        return dateFormat.format(cal.getTime()).substring(4, 6);
    }

    public static boolean isYxsbByNsqx(String nsqxDm, String yf) {
        boolean isYxbs = false;
        if (StrUtil.isBlank(yf)) {
            Calendar calendar = Calendar.getInstance();
            yf = String.valueOf(calendar.get(Calendar.MONTH) + 1);
        }
        String yxbsyf = NsqxEnum.getNsqxEnumByDm(nsqxDm).getYxbsyf();
        if (StrUtil.isNotBlank(yxbsyf) && yxbsyf.contains(yf)) {
            isYxbs = true;
        }
        return isYxbs;
    }

    public static String getSssqQByNsqxAndSssq(String nsqx, String sssq) {
        String fmt = "yyyy-MM-dd";
        if (NsqxEnum.TIME.getDm().equals(nsqx)) {
            String kjny = getKjny();
            if (kjny.equals(sssq)) {
                return DateUtil.today();
            }
            nsqx = NsqxEnum.MONTH.getDm();
        }
        int times = getTimesByNsqx(nsqx);
        Calendar rightNow = Calendar.getInstance();
        rightNow.set(Calendar.YEAR, Integer.valueOf(DateUtil.parse(sssq, DatePattern.SIMPLE_MONTH_PATTERN).year()));
        rightNow.set(Calendar.MONTH, Integer.valueOf(DateUtil.parse(sssq, DatePattern.SIMPLE_MONTH_PATTERN).month()));
        rightNow.set(Calendar.DAY_OF_MONTH, 1);
        while ((rightNow.get(Calendar.MONTH)) % times != 0) {
            rightNow.add(Calendar.MONTH, -1);
        }
        rightNow.add(Calendar.MONTH, -times);
        //兼容年报SBNY
        if (NsqxEnum.YEAR.getDm().equals(nsqx)) {
            if (sssq.endsWith("00")) {
                rightNow.add(Calendar.YEAR, 1);
            }
        }

        SimpleDateFormat sdf = new SimpleDateFormat(fmt);
        return sdf.format(rightNow.getTime());
    }

    public static String getKjny() {
        Calendar rightNow = Calendar.getInstance();
        rightNow.add(Calendar.MONTH, -1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        return dateFormat.format(rightNow.getTime());
    }

    private static int getTimesByNsqx(String nsqx) {
        if (NsqxEnum.MONTH.getDm().equals(nsqx)
                || NsqxEnum.DAY.getDm().equals(nsqx)
                || NsqxEnum.FIFTEEN_DAY.getDm().equals(nsqx)) {
            return 1;
        } else if (NsqxEnum.QUARTER.getDm().equals(nsqx)) {
            return 3;
        } else if (NsqxEnum.HALF_YEAR.getDm().equals(nsqx)) {
            return 6;
        } else {
            return 12;
        }
    }

    public static String getSkssqqBySsny(String nsqxDm, String ssny) {
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.parse(ssny, DatePattern.SIMPLE_MONTH_PATTERN));
        int[] acceptMonth = null;
        switch (nsqxDm) {
            case "10":
                acceptMonth = new int[]{1};
                break;
            case "09":
                acceptMonth = new int[]{1, 7};
                break;
            case "08":
                acceptMonth = new int[]{1, 4, 7, 10};
                break;
            default:
                acceptMonth = new int[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
                break;
        }
        while (true) {
            if (Arrays.binarySearch(acceptMonth, calendar.get(Calendar.MONTH) + 1) >= 0) {
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                return format1.format(calendar.getTime());
            }
            calendar.add(Calendar.MONTH, -1);
        }
    }

    public static String getSssqZByNsqxAndSssq(String nsqx, String sssq) {
        String fmt = "yyyy-MM-dd";
        if (NsqxEnum.TIME.getDm().equals(nsqx)) {
            String kjny = getKjny();
            if (kjny.equals(sssq)) {
                return DateUtil.today();
            }
            nsqx = NsqxEnum.MONTH.getDm();
        }
        int times = getTimesByNsqx(nsqx);
        Calendar rightNow = Calendar.getInstance();
        rightNow.set(Calendar.YEAR, Integer.valueOf(DateUtil.parse(sssq, DatePattern.SIMPLE_MONTH_PATTERN).year()));
        rightNow.set(Calendar.MONTH, Integer.valueOf(DateUtil.parse(sssq, DatePattern.SIMPLE_MONTH_PATTERN).month()));
        rightNow.set(Calendar.DAY_OF_MONTH, Integer.valueOf(DateUtil.parse(sssq, DatePattern.SIMPLE_MONTH_PATTERN).dayOfMonth()));

        while ((rightNow.get(Calendar.MONTH)) % times != 0) {
            rightNow.add(Calendar.MONTH, -1);
        }
        rightNow.add(Calendar.DATE, -1);
        //兼容年报SBNY
        if (NsqxEnum.YEAR.getDm().equals(nsqx)) {
            if (sssq.endsWith("00")) {
                rightNow.add(Calendar.YEAR, 1);
            }
        }

        SimpleDateFormat sdf = new SimpleDateFormat(fmt);
        return sdf.format(rightNow.getTime());
    }

    public static String getSkssqzBySsny(String nsqxDm, String ssny) {
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.parse(ssny, DatePattern.SIMPLE_MONTH_PATTERN));
        int[] acceptMonth = null;
        switch (nsqxDm) {
            case "10":
                acceptMonth = new int[]{12};
                break;
            case "09":
                acceptMonth = new int[]{6, 12};
                break;
            case "08":
                acceptMonth = new int[]{3, 6, 9, 12};
                break;
            default:
                acceptMonth = new int[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
                break;
        }
        while (true) {
            if (Arrays.binarySearch(acceptMonth, calendar.get(Calendar.MONTH) + 1) >= 0) {
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.add(Calendar.MONTH, 1);
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                return format1.format(calendar.getTime());
            }
            calendar.add(Calendar.MONTH, 1);
        }
    }

    public static String getLastDayOfMonth(String dateStr) {
        String lastDateStr = "";
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date date;
        try {
            date = df.parse(dateStr);
            Calendar ca = Calendar.getInstance();
            ca.setTime(date); // someDate 为你要获取的那个月的时间
            ca.set(Calendar.DAY_OF_MONTH, 1);
            ca.add(Calendar.MONTH, 1);
            ca.add(Calendar.DAY_OF_MONTH, -1);
            Date lastDate = ca.getTime();
            lastDateStr = df.format(lastDate);
        } catch (ParseException e) {
            log.error("输入日期格式不合法。", e);
        }
        return lastDateStr;
    }

    public static String getFirstDayOfMonth(String dateStr) {
        String firstDateStr = "";
        String formatter = null;

        if (dateStr.indexOf('-') != -1 && dateStr.length() >= 8) {
            formatter = "yyyy-MM-dd";
        } else if (dateStr.indexOf('/') != -1 && dateStr.length() >= 8) {
            formatter = "yyyy/MM/dd";
        } else {
            formatter = "yyyyMMdd";
        }
        DateFormat df = new SimpleDateFormat(formatter);
        try {
            Date date = df.parse(dateStr);
            Calendar ca = Calendar.getInstance();
            ca.setTime(date); // someDate 为你要获取的那个月的时间
            ca.set(Calendar.DAY_OF_MONTH, 1);
            firstDateStr = df.format(ca.getTime());
        } catch (ParseException e) {
            log.error("输入日期格式不合法。", e);
            return null;
        }
        return firstDateStr;
    }

    public static String getSssqQByNsqx(String nsqx, String... dateFormat) {
        int times = 1;
        if (NsqxEnum.QUARTER.getDm().equals(nsqx)) {
            times = 3;
        } else if (NsqxEnum.YEAR.getDm().equals(nsqx)) {
            times = 12;
        } else if (NsqxEnum.HALF_YEAR.getDm().equals(nsqx)) {
            times = 6;
        }

        String yyyyMMdd = DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT);
        Calendar rightNow = Calendar.getInstance();
        rightNow.set(Integer.parseInt(yyyyMMdd.substring(0, 4)), Integer.parseInt(yyyyMMdd.substring(4, 6)) - 1, 1);
        while ((rightNow.get(Calendar.MONTH)) % times != 0) {
            rightNow.add(Calendar.MONTH, -1);
        }
        rightNow.add(Calendar.MONTH, -times);

        String fmt = "yyyy-MM-dd";
        if (dateFormat != null && dateFormat.length > 0) {
            fmt = dateFormat[0];
        }
        SimpleDateFormat sdf = new SimpleDateFormat(fmt);
        return sdf.format(rightNow.getTime());
    }

    public static String getSssqZByNsqx(String nsqx, String... dateFormat) {
        int times = 1;
        if (NsqxEnum.QUARTER.getDm().equals(nsqx)) {
            times = 3;
        } else if (NsqxEnum.YEAR.getDm().equals(nsqx)) {
            times = 12;
        } else if (NsqxEnum.HALF_YEAR.getDm().equals(nsqx)) {
            times = 6;
        }
        String yyyyMMdd = DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT);
        Calendar rightNow = Calendar.getInstance();
        rightNow.set(Integer.parseInt(yyyyMMdd.substring(0, 4)), Integer.parseInt(yyyyMMdd.substring(4, 6)) - 1, 1);
        while ((rightNow.get(Calendar.MONTH)) % times != 0) {
            rightNow.add(Calendar.MONTH, -1);
        }
        rightNow.add(Calendar.DATE, -1);

        String fmt = "yyyy-MM-dd";
        if (dateFormat != null && dateFormat.length > 0) {
            fmt = dateFormat[0];
        }
        SimpleDateFormat sdf = new SimpleDateFormat(fmt);
        return sdf.format(rightNow.getTime());
    }

    /**
     * 计算距离征期结束天数
     * @return
     */
    public static String jsjlzqjsts() {
        final Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 15);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        final Date zqrl = calendar.getTime();
        return StrUtil.toString(DateUtil.betweenDay(DateUtil.date(), zqrl, false));
    }
}
