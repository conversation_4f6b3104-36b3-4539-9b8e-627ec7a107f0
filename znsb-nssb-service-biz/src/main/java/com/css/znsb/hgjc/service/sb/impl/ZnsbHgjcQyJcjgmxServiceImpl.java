package com.css.znsb.hgjc.service.sb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.hgjc.constants.HgjcApiConstants;
import com.css.znsb.hgjc.mapper.ZnsbHgjcQyJcjgmxMapper;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgmxDO;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyJcjgmxService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_jcjgmx(合规检查-企业检查结果明细)】的数据库操作Service实现
 * @createDate 2024-04-15 21:32:51
 */
@Service
public class ZnsbHgjcQyJcjgmxServiceImpl extends ServiceImpl<ZnsbHgjcQyJcjgmxMapper, ZnsbHgjcQyJcjgmxDO>
        implements ZnsbHgjcQyJcjgmxService {
    @Override
    public void updateResult(String jgmxuuid, boolean result, String msg) {
        final ZnsbHgjcQyJcjgmxDO jgDo = new ZnsbHgjcQyJcjgmxDO();
        jgDo.setJgmxuuid(jgmxuuid);
        jgDo.setJcztDm(HgjcApiConstants.JCZT_DM_WCJC);
        if (result) {
            jgDo.setCytgbz("Y");
        } else {
            jgDo.setCytgbz("N");
        }
        jgDo.setSjjcjg(msg);
        this.getBaseMapper().updateById(jgDo);
    }

    @Override
    public List<ZnsbHgjcQyJcjgmxDO> queryByJguuid(String jguuid) {
        return this.getBaseMapper().queryByJguuid(jguuid);
    }
}




