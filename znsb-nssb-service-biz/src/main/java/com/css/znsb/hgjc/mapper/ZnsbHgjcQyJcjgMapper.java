package com.css.znsb.hgjc.mapper;

import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgDO;
import com.css.znsb.hgjc.pojo.dto.result.SbCheckResultQueryReq;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_jcjg(合规检查-企业检查结果)】的数据库操作Mapper
 * @createDate 2024-04-15 21:32:51
 * @Entity com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjg
 */
@Mapper
public interface ZnsbHgjcQyJcjgMapper extends BaseMapperX<ZnsbHgjcQyJcjgDO> {
    default ZnsbHgjcQyJcjgDO queryLastResult(SbCheckResultQueryReq req) {
        return this.selectOne(new LambdaQueryWrapperX<ZnsbHgjcQyJcjgDO>()
                .eq(ZnsbHgjcQyJcjgDO::getYxbz, "Y")
                .eqIfPresent(ZnsbHgjcQyJcjgDO::getYzpzzlDm, req.getYzpzzlDm())
                .eqIfPresent(ZnsbHgjcQyJcjgDO::getJkhj, req.getJkhj())
                .eqIfPresent(ZnsbHgjcQyJcjgDO::getDyfs, req.getDyfs())
                .eqIfPresent(ZnsbHgjcQyJcjgDO::getNsrsbh, req.getNsrsbh())
                .eqIfPresent(ZnsbHgjcQyJcjgDO::getSkssqq, req.getSkssqq())
                .eqIfPresent(ZnsbHgjcQyJcjgDO::getSkssqz, req.getSkssqz())
                .eqIfPresent(ZnsbHgjcQyJcjgDO::getSbrwuuid, req.getSbrwuuid())
                .orderByDesc(ZnsbHgjcQyJcjgDO::getLrrq)
        );
    }
}




