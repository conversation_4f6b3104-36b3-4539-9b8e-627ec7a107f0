package com.css.znsb.hgjc.service.sb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.hgjc.mapper.ZnsbHgjcQyYwmxMapper;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYwmxDO;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyYwmxService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_ywmx(合规检查-企业业务明细)】的数据库操作Service实现
 * @createDate 2024-04-15 21:32:51
 */
@Service
public class ZnsbHgjcQyYwmxServiceImpl extends ServiceImpl<ZnsbHgjcQyYwmxMapper, ZnsbHgjcQyYwmxDO>
        implements ZnsbHgjcQyYwmxService {

    @Override
    public List<ZnsbHgjcQyYwmxDO> queryByYwuuid(final String ywuuid) {
        return this.baseMapper.queryByYwuuid(ywuuid);
    }
}




