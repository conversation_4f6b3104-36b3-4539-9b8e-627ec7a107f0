package com.css.znsb.hgjc.mapper;

import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcJkxmDO;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_jkxm(合规检查-监控项目)】的数据库操作Mapper
 * @createDate 2024-04-15 21:32:51
 * @Entity com.css.znsb.hgjc.pojo.domain.ZnsbHgjcJkxm
 */
@Mapper
public interface ZnsbHgjcJkxmMapper extends BaseMapperX<ZnsbHgjcJkxmDO> {
    default ZnsbHgjcJkxmDO selectById(Serializable id) {
        return this.selectOne(new LambdaQueryWrapperX<ZnsbHgjcJkxmDO>()
                .eq(ZnsbHgjcJkxmDO::getJkxmuuid, id)
                .eq(ZnsbHgjcJkxmDO::getYxbz, "Y")
        );
    }

    default List<ZnsbHgjcJkxmDO> selectBatchIds(Collection<? extends Serializable> idList) {
        return this.selectList(new LambdaQueryWrapperX<ZnsbHgjcJkxmDO>()
                .in(ZnsbHgjcJkxmDO::getJkxmuuid, idList)
                .eq(ZnsbHgjcJkxmDO::getYxbz, "Y")
        );
    }

}




