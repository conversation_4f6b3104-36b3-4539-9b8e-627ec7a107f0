package com.css.znsb.hgjc.service.sb.impl;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.string.StrUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.hgjc.constants.HgjcApiConstants;
import com.css.znsb.hgjc.pojo.check.sb.SbCheckReq;
import com.css.znsb.hgjc.pojo.check.sb.SbCheckRes;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcJkxmDO;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgDO;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgmxDO;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYwDO;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYwmxDO;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcYwDO;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcYwmxDO;
import com.css.znsb.hgjc.pojo.dto.checkrule.config.CheckConfig;
import com.css.znsb.hgjc.pojo.dto.sb.QyYwmxDTO;
import com.css.znsb.hgjc.pojo.dto.sb.SbChcekItemConfig;
import com.css.znsb.hgjc.pojo.dto.sb.SbCheckItemReq;
import com.css.znsb.hgjc.service.checkrule.scheduler.impl.SbCheckScheduler;
import com.css.znsb.hgjc.service.sb.ISbCheckService;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcJkxmService;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyJcjgService;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyJcjgmxService;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyYwService;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyYwmxService;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcYwService;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcYwmxService;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.hgjy.service.sb.impl
 * @file SbCheckServiceImpl.java 创建时间:2024/4/15 17:19
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 智能申报
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Service
@Slf4j
public class SbCheckServiceImpl implements ISbCheckService {

    private final ZnsbHgjcQyYwService qyYwService;

    private final ZnsbHgjcQyYwmxService qyYwmxService;

    private final ZnsbHgjcJkxmService jkxmService;

    private final ZnsbHgjcQyJcjgService qyJcjgService;

    private final ZnsbHgjcQyJcjgmxService qyJcjgmxService;

    private final SbCheckScheduler scheduler;

    private final ZnsbHgjcYwService ywService;

    private final ZnsbHgjcYwmxService ywmxService;

    private final CompanyApi companyApi;

    @Autowired
    public SbCheckServiceImpl(final ZnsbHgjcQyYwService qyYwService,
                              final ZnsbHgjcQyYwmxService qyYwmxService,
                              final ZnsbHgjcJkxmService jkxmService,
                              final ZnsbHgjcQyJcjgService qyJcjgService,
                              final ZnsbHgjcQyJcjgmxService qyJcjgmxService,
                              final SbCheckScheduler scheduler,
                              final ZnsbHgjcYwService ywService,
                              final ZnsbHgjcYwmxService ywmxService,
                              final CompanyApi companyApi) {
        this.qyYwService = qyYwService;
        this.qyYwmxService = qyYwmxService;
        this.jkxmService = jkxmService;
        this.qyJcjgService = qyJcjgService;
        this.qyJcjgmxService = qyJcjgmxService;
        this.scheduler = scheduler;
        this.ywService = ywService;
        this.ywmxService = ywmxService;
        this.companyApi = companyApi;
    }

    @Override
//    @Transactional
    public SbCheckRes createCheckTask(SbCheckReq req) {
        log.info("开始合规检查，入参：{}", req);
        final String yzpzzlDm = req.getYzpzzlDm();
        final String jkhj = req.getJkhj();
        //校验该企业是否配置合规检查任务
        CheckConfig checkConfig = this.getCheckConfig(req.getNsrsbh(), yzpzzlDm, jkhj);
        if (GyUtils.isNull(checkConfig)) {
            //执行默认合规检查配置
            checkConfig = this.getDefaultCheckConfig(yzpzzlDm, jkhj);
        }
        if (GyUtils.isNotNull(checkConfig.getReason())) {
            return SbCheckRes.buildWxjcRes(checkConfig.getReason());
        }
        // 生成检查结果
        final ZnsbHgjcQyJcjgDO jcjgDO = this.createJcjg(req, checkConfig);
        //生成检查结果明细
        final List<ZnsbHgjcQyJcjgmxDO> mxList = this.createJcjgmx(jcjgDO, checkConfig);
        //保存数据
        this.qyJcjgService.save(jcjgDO);
        this.qyJcjgmxService.saveBatch(mxList);
        if (HgjcApiConstants.DYFS_YW.equals(req.getDyfs())) {
            final ZnsbHgjcQyJcjgDO finalJcjgDO = this.processCheckTask(jcjgDO, mxList);
            return buildResByJcjg(finalJcjgDO);
        }
        return buildResByJcjg(jcjgDO);
    }

    private CheckConfig getCheckConfig(String nsrsbh, String yzpzzlDm, String jkhj) {
        // 查纳税人当前校验业务
        final ZnsbHgjcQyYwDO qyYwVo = this.qyYwService.queryByYzpzzlJkhj(nsrsbh, yzpzzlDm, jkhj);
        if (GyUtils.isNull(qyYwVo)) {
            return null;
        }
        final String ywuuid = qyYwVo.getYwuuid();
        // 查纳税人当前检查项目
        final List<QyYwmxDTO> jkxmList = this.getJkxmByYwuuid(ywuuid);
        final CheckConfig config = new CheckConfig();
        if (GyUtils.isNull(jkxmList)) {
            config.setReason("未配置有效的合规检查业务");
            return config;
        }
        config.setQyYwVo(qyYwVo);
        config.setJkxmList(jkxmList);
        return config;
    }

    private CheckConfig getDefaultCheckConfig(String yzpzzlDm, String jkhj) {
        //查询当前校验任务
        final ZnsbHgjcYwDO ywDO = this.ywService.queryByYzpzzlJkhj(yzpzzlDm, jkhj);
        final CheckConfig config = new CheckConfig();
        if (GyUtils.isNull(ywDO)) {
            config.setReason("未配置合规检查业务");
            return config;
        }
        final String ywbh = ywDO.getYwbh();
        //查询当前检查项目
        final List<QyYwmxDTO> ywjkxmList = this.getJkxmByYwbh(ywbh);
        if (GyUtils.isNull(ywjkxmList)) {
            config.setReason("未配置有效的合规检查业务");
            return config;
        }
        final ZnsbHgjcQyYwDO qyYwDO = BeanUtils.toBean(ywDO, ZnsbHgjcQyYwDO.class);
        config.setQyYwVo(qyYwDO);
        config.setJkxmList(ywjkxmList);
        return config;
    }

    private ZnsbHgjcQyJcjgDO createJcjg(final SbCheckReq req, final CheckConfig checkConfig) {
        final ZnsbHgjcQyYwDO qyYwVo = checkConfig.getQyYwVo();
        final List<QyYwmxDTO> jkxmList = checkConfig.getJkxmList();
        final ZnsbHgjcQyJcjgDO jcjgDO = BeanUtils.toBean(qyYwVo, ZnsbHgjcQyJcjgDO.class);
        if (GyUtils.isNull(jcjgDO.getPzuuid())) {
            jcjgDO.setPzuuid(req.getNsrsbh());
        }
        if (GyUtils.isNull(jcjgDO.getNsrsbh())) {
            jcjgDO.setNsrsbh(req.getNsrsbh());
        }
        if (GyUtils.isNull(jcjgDO.getNsrmc())) {
            String nsrmc = "";
            final CommonResult<CompanyBasicInfoDTO> response = companyApi.basicInfo(req.getDjxh(), req.getNsrsbh());
            log.debug("合规检查服务查询纳税人返回response");
            if (GyUtils.isNotNull(response) && GyUtils.isNotNull(response.getData())) {
                final CompanyBasicInfoDTO basicInfo = response.getData();
                nsrmc = GyUtils.isNull(basicInfo.getNsrmc()) ? "" : basicInfo.getNsrmc();

            }
            jcjgDO.setNsrmc(nsrmc);
        }
        if (GyUtils.isNull(jcjgDO.getYxbz())) {
            jcjgDO.setYxbz("Y");
        }
        jcjgDO.setJguuid(GyUtils.getUuid());
        jcjgDO.setJcztDm(HgjcApiConstants.JCZT_DM_WJC);
        jcjgDO.setJcsl1(String.valueOf(jkxmList.size()));
        jcjgDO.setCheckTime(DateUtils.getSystemCurrentTime(0));
        jcjgDO.setDyfs(req.getDyfs());

        if (GyUtils.isNotNull(req.getSkssqq())) {
            jcjgDO.setSkssqq(req.getSkssqq());
        } else {
            jcjgDO.setSkssqq(null);
        }

        if (GyUtils.isNotNull(req.getSkssqz())) {
            jcjgDO.setSkssqz(req.getSkssqz());
        } else {
            jcjgDO.setSkssqz(null);
        }

        jcjgDO.setDjxh(req.getDjxh());
        jcjgDO.setSbrwuuid(req.getSbrwuuid());
        return jcjgDO;
    }

    @NotNull
    private List<ZnsbHgjcQyJcjgmxDO> createJcjgmx(final ZnsbHgjcQyJcjgDO jcjgDO, final CheckConfig checkConfig) {
        final List<QyYwmxDTO> jkxmList = checkConfig.getJkxmList();
        final List<ZnsbHgjcQyJcjgmxDO> mxList = new ArrayList<>(jkxmList.size());
        for (final QyYwmxDTO jkxmVo : jkxmList) {
            final ZnsbHgjcJkxmDO jkxmConfig = jkxmVo.getJkxmConfig();
            // 校验项目配置
            final ZnsbHgjcQyJcjgmxDO mxVo = BeanUtils.toBean(jkxmConfig, ZnsbHgjcQyJcjgmxDO.class);
            // 企业校验项目配置
            BeanUtils.copyBean(jkxmVo, mxVo);
            // 主表信息
            BeanUtils.copyBean(jcjgDO, mxVo);
            // 结果明细信息
            mxVo.setJgmxuuid(GyUtils.getUuid());
            mxVo.setYxbz("Y");
            mxVo.setClbz("N");
            if (GyUtils.isNull(mxVo.getSkssqq())) {
                jcjgDO.setSkssqq(null);
            }

            if (GyUtils.isNull(mxVo.getSkssqz())) {
                mxVo.setSkssqz(null);
            }
            mxList.add(mxVo);
        }
        return mxList;
    }

    private SbCheckRes buildResByJcjg(final ZnsbHgjcQyJcjgDO jcjgDO) {
        return new SbCheckRes(jcjgDO.getJcztDm(), jcjgDO.getJguuid(), jcjgDO.getJyjg1(), jcjgDO.getSjjcjg());
    }

    private List<QyYwmxDTO> getJkxmByYwuuid(final String ywuuid) {
        // 查询纳税人当前校验业务明细
        final List<ZnsbHgjcQyYwmxDO> ywmxDoList = this.qyYwmxService.queryByYwuuid(ywuuid);
        if (GyUtils.isNull(ywmxDoList)) {
            return null;
        }
        final List<QyYwmxDTO> ywmxList = BeanUtils.toBean(ywmxDoList, QyYwmxDTO.class);
        this.handleYwmxList(ywmxList);
        return ywmxList;
    }

    private List<QyYwmxDTO> getJkxmByYwbh(final String ywbh) {
        //查询业务明细
        final List<ZnsbHgjcYwmxDO> mxDoList = this.ywmxService.queryByYwbh(ywbh);
        if (GyUtils.isNull(mxDoList)) {
            return null;
        }
        final List<QyYwmxDTO> ywmxList = BeanUtils.toBean(mxDoList, QyYwmxDTO.class);
        this.handleYwmxList(ywmxList);
        return ywmxList;
    }

    private List<QyYwmxDTO> handleYwmxList(final List<QyYwmxDTO> ywmxList) {
        final Map<String, QyYwmxDTO> indexMap = GyUtils.getIndexByKey(ywmxList, QyYwmxDTO::getJkxmuuid);

        final Set<String> jkxmuuidList = indexMap.keySet();
        final List<ZnsbHgjcJkxmDO> jkxmList = this.jkxmService.listByIds(jkxmuuidList);

        if (GyUtils.isNotNull(jkxmList)) {
            for (final ZnsbHgjcJkxmDO jkxmDO : jkxmList) {
                final String jkxmuuid = jkxmDO.getJkxmuuid();
                final QyYwmxDTO qyYwmxDTO = indexMap.get(jkxmuuid);
                if (GyUtils.isNotNull(qyYwmxDTO)) {
                    qyYwmxDTO.setJkxmConfig(jkxmDO);
                }
            }
        }
        return ywmxList;
    }

    @Override
    public ZnsbHgjcQyJcjgDO processCheckTask(final ZnsbHgjcQyJcjgDO jcjgDO, final List<ZnsbHgjcQyJcjgmxDO> mxList) {
        if (GyUtils.isNull(jcjgDO) || GyUtils.isNull(mxList)) {
            log.info("处理检查任务,入参为空,跳过处理");
            return jcjgDO;
        }
        final String taskName = StrUtils.join("-", jcjgDO.getJguuid(), jcjgDO.getNsrsbh(), jcjgDO.getNsrmc(),
                jcjgDO.getYzpzzlDm(), jcjgDO.getSkssqq(), jcjgDO.getSkssqz(), jcjgDO.getJkhj());
        log.info("开始处理检查任务,{},待处理任务数量{}", taskName, mxList.size());

        final SbCheckItemReq req = BeanUtils.toBean(jcjgDO, SbCheckItemReq.class);
        final List<SbChcekItemConfig> configList = BeanUtils.toBean(mxList, SbChcekItemConfig.class);


        this.scheduler.processCheck(req, configList);

        return req.getJcjg();
    }


}
