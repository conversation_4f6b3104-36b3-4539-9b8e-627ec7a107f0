package com.css.znsb.hgjc.service.sb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.hgjc.mapper.ZnsbHgjcQyJcjgMapper;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgDO;
import com.css.znsb.hgjc.pojo.dto.result.SbCheckResultQueryReq;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyJcjgService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_jcjg(合规检查-企业检查结果)】的数据库操作Service实现
 * @createDate 2024-04-15 21:32:51
 */
@Service
public class ZnsbHgjcQyJcjgServiceImpl extends ServiceImpl<ZnsbHgjcQyJcjgMapper, ZnsbHgjcQyJcjgDO>
        implements ZnsbHgjcQyJcjgService {

    @Override
    public void updateResult(ZnsbHgjcQyJcjgDO jgDo) {
        this.getBaseMapper().updateById(jgDo);
    }

    @Override
    public ZnsbHgjcQyJcjgDO queryLastResult(SbCheckResultQueryReq req) {
        return this.getBaseMapper().queryLastResult(req);
    }


}




