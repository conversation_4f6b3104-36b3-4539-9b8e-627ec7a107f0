package com.css.znsb.hgjc.mapper;

import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYwmxDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_ywmx(合规检查-企业业务明细)】的数据库操作Mapper
 * @createDate 2024-04-15 21:32:51
 * @Entity com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYwmx
 */
@Mapper
public interface ZnsbHgjcQyYwmxMapper extends BaseMapperX<ZnsbHgjcQyYwmxDO> {

    default List<ZnsbHgjcQyYwmxDO> queryByYwuuid(String ywuuid) {
        return this.selectList(new LambdaQueryWrapperX<ZnsbHgjcQyYwmxDO>()
                .eq(ZnsbHgjcQyYwmxDO::getYwuuid, ywuuid)
                .eq(ZnsbHgjcQyYwmxDO::getYxbz, "Y")
                .eq(ZnsbHgjcQyYwmxDO::getQybz, "Y")
        );
    }
}




