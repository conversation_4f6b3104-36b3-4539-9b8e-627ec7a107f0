package com.css.znsb.hgjc.mapper;

import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgmxDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_jcjgmx(合规检查-企业检查结果明细)】的数据库操作Mapper
 * @createDate 2024-04-15 21:32:51
 * @Entity com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgmx
 */
@Mapper
public interface ZnsbHgjcQyJcjgmxMapper extends BaseMapperX<ZnsbHgjcQyJcjgmxDO> {
    default List<ZnsbHgjcQyJcjgmxDO> queryByJguuid(String jguuid) {

        return this.selectList(new LambdaQueryWrapperX<ZnsbHgjcQyJcjgmxDO>()
                .eq(ZnsbHgjcQyJcjgmxDO::getJguuid, jguuid)
                .eq(ZnsbHgjcQyJcjgmxDO::getYxbz, "Y")
                .orderByAsc(ZnsbHgjcQyJcjgmxDO::getXh)
        );
    }
}




