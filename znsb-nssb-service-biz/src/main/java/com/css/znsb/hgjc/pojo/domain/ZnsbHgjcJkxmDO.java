package com.css.znsb.hgjc.pojo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合规检查-监控项目
 * @TableName znsb_hgjc_jkxm
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "znsb_hgjc_jkxm")
@Data
public class ZnsbHgjcJkxmDO extends BaseDO {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 监控项目UUID||工作流监控项目规则主键
     */
    @TableId(value = "jkxmuuid")
    private String jkxmuuid;
    /**
     * 监控项目编码
     */
    @TableField(value = "jkxmbm")
    private String jkxmbm;
    /**
     * 监控项目名称
     */
    @TableField(value = "jkxmmc")
    private String jkxmmc;
    /**
     * 监控项目描述
     */
    @TableField(value = "jkxmms")
    private String jkxmms;
    /**
     * 业务大类编码||业务大类编码
     */
    @TableField(value = "ywdlbm")
    private String ywdlbm;
    /**
     * 业务中类编码
     */
    @TableField(value = "ywzlbm")
    private String ywzlbm;
    /**
     * 业务小类编码||业务小类编码
     */
    @TableField(value = "ywxlbm")
    private String ywxlbm;
    /**
     * 检查项目
     */
    @TableField(value = "jcxm")
    private String jcxm;
    /**
     * 征收项目代码
     */
    @TableField(value = "zsxm_dm")
    private String zsxmDm;
    /**
     * 应征凭证种类代码
     */
    @TableField(value = "yzpzzl_dm")
    private String yzpzzlDm;
    /**
     * 行业中类代码
     */
    @TableField(value = "hyzl_dm")
    private String hyzlDm;
    /**
     * 行业代码
     */
    @TableField(value = "hy_dm")
    private String hyDm;
    /**
     * 监控类型代码
     */
    @TableField(value = "jklx_dm_1")
    private String jklxDm1;
    /**
     * 风险等级代码
     */
    @TableField(value = "fxdj_dm")
    private String fxdjDm;
    /**
     * 业务描述||业务描述
     */
    @TableField(value = "ywms")
    private String ywms;
    /**
     * 业务处理方式
     */
    @TableField(value = "ywclfs")
    private String ywclfs;
    /**
     * 处理建议||处理建议
     */
    @TableField(value = "cljy")
    private String cljy;
    /**
     * 处理建议说明
     */
    @TableField(value = "cljysm")
    private String cljysm;
    /**
     * 规则类型
     */
    @TableField(value = "gzlx")
    private String gzlx;
    /**
     * 业务规则配置||业务规则配置
     */
    @TableField(value = "ywgzpz")
    private String ywgzpz;
    /**
     * 版本号
     */
    @TableField(value = "bbh")
    private String bbh;
    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;
    /**
     * 消息模板内容||消息模板内容
     */
    @TableField(value = "xxmbnr")
    private String xxmbnr;
}