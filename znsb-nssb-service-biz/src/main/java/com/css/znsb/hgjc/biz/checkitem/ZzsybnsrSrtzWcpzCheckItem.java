package com.css.znsb.hgjc.biz.checkitem;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.hgjc.pojo.dto.sb.SbChcekItemConfig;
import com.css.znsb.hgjc.pojo.dto.sb.SbCheckItemReq;
import com.css.znsb.hgjc.service.checkrule.executer.impl.SbChcekItemExecuter;
import com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb;
import com.css.znsb.tzzx.service.srzz.ZnsbTzzxSrzzService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.css.znsb.hgjc.constants.CheckErrorMsgConstants.ZZSYBNSR_SRTZWSCWCPZ_ERROR_MSG;

@Service(value = "ZzsybnsrSrtzWcpzCheckItem")
@Slf4j
public class ZzsybnsrSrtzWcpzCheckItem extends SbChcekItemExecuter<String> {

    @Resource
    private ZnsbTzzxSrzzService srzzService;

    /**
     * 获取数据
     *
     * @param req 请求
     * @return {@link String}
     */
    protected String getBusinessData(SbCheckItemReq req) {
        try {
            log.info("ZzsybnsrSrtzWcpzCheckItem.getBusinessData，入参：{}", req);
            log.info("构造查询收入台账合理尾差数据");
            ZnsbTzzxSrzzQuerySrcybdhzb reqVO = new ZnsbTzzxSrzzQuerySrcybdhzb();
            reqVO.setDjxh(req.getDjxh());
            reqVO.setSszqq(Convert.toInt(DateUtil.format(DateUtil.parseDate(req.getSkssqq()), "yyyyMM")));
            reqVO.setSszqz(Convert.toInt(DateUtil.format(DateUtil.parseDate(req.getSkssqz()), "yyyyMM")));
            log.info("查询收入台账合理尾差请求报文：{}", reqVO);

            log.info("开始查询收入台账合理尾差");
            List<SrzzAndSrcybdhzbDTO> hlwcList = srzzService.querySrtzhlwc(reqVO);
            log.info("查询收入台账合理尾差完成，结果：{}", hlwcList);

            if (GyUtils.isNotNull(hlwcList)) {
                log.info("收入台账合理尾差结果不为空，返回校验不通过");
                return "N";
            }
        } catch (Exception e) {
            log.error("获取未开票收入比对结果出现异常，", e);
        }
        return "Y";
    }

    /**
     * 校验规则
     *
     * @param config 配置
     * @param wcpzscbz 收入台账比对结果
     * @return true/false
     */
    protected boolean checkRule(SbChcekItemConfig config, String wcpzscbz) {
        log.info("ZzsybnsrSrtzWcpzCheckItem.checkRule，尾差凭证生成结果：{}", wcpzscbz);
        return !"N".equals(wcpzscbz);
    }

    /**
     * 获取提示信息
     *
     * @param config 配置
     * @param wcpzscbz 尾差凭证生成标志
     * @return 提示信息
     */
    protected String getErrorMsg(SbChcekItemConfig config, String wcpzscbz) {
        log.info("ZzsybnsrSrtzWcpzCheckItem.getErrorMsg，尾差凭证生成结果：{}", wcpzscbz);
        return ZZSYBNSR_SRTZWSCWCPZ_ERROR_MSG;
    }
}
