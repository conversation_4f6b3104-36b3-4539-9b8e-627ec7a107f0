package com.css.znsb.hgjc.service.sb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgDO;
import com.css.znsb.hgjc.pojo.dto.result.SbCheckResultQueryReq;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_jcjg(合规检查-企业检查结果)】的数据库操作Service
 * @createDate 2024-04-15 21:32:51
 */
public interface ZnsbHgjcQyJcjgService extends IService<ZnsbHgjcQyJcjgDO> {

    void updateResult(ZnsbHgjcQyJcjgDO jgDo);

    ZnsbHgjcQyJcjgDO queryLastResult(SbCheckResultQueryReq req);
}
