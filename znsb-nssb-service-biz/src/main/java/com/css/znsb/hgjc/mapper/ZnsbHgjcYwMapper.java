package com.css.znsb.hgjc.mapper;

import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcYwDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_yw(合规检查业务)】的数据库操作Mapper
 * @createDate 2024-04-15 21:32:51
 * @Entity com.css.znsb.hgjc.pojo.domain.ZnsbHgjcYw
 */
@Mapper
public interface ZnsbHgjcYwMapper extends BaseMapperX<ZnsbHgjcYwDO> {

    default ZnsbHgjcYwDO queryByYzpzzlJkhj(String yzpzzlDm, String jkhj) {
        return this.selectOne(new LambdaQueryWrapperX<ZnsbHgjcYwDO>()
                .eq(ZnsbHgjcYwDO::getYzpzzlDm, yzpzzlDm)
                .eq(ZnsbHgjcYwDO::getJkhj, jkhj)
                .eq(ZnsbHgjcYwDO::getYxbz, "Y")
        );
    }
}




