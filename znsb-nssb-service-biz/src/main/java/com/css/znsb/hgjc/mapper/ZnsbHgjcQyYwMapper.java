package com.css.znsb.hgjc.mapper;

import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYwDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_yw(合规检查-企业业务)】的数据库操作Mapper
 * @createDate 2024-04-15 21:32:51
 * @Entity com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYw
 */
@Mapper
public interface ZnsbHgjcQyYwMapper extends BaseMapperX<ZnsbHgjcQyYwDO> {

    default ZnsbHgjcQyYwDO queryByYzpzzlJkhj(String nsrsbh, String yzpzzlDm, String jkhj) {
        return this.selectOne(new LambdaQueryWrapperX<ZnsbHgjcQyYwDO>()
                .eq(ZnsbHgjcQyYwDO::getNsrsbh, nsrsbh)
                .eq(ZnsbHgjcQyYwDO::getJkhj, jkhj)
                .eq(ZnsbHgjcQyYwDO::getYzpzzlDm, yzpzzlDm)
                .eq(ZnsbHgjcQyYwDO::getYxbz, "Y")
                .eq(ZnsbHgjcQyYwDO::getQybz, "Y")
        );
    }


}




