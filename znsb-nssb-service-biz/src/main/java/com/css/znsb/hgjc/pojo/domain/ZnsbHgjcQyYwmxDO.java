package com.css.znsb.hgjc.pojo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合规检查-企业业务明细
 * @TableName znsb_hgjc_qy_ywmx
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "znsb_hgjc_qy_ywmx")
@Data
public class ZnsbHgjcQyYwmxDO extends BaseDO {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 企业业务明细UUID
     */
    @TableId(value = "qyywmxuuid")
    private String qyywmxuuid;
    /**
     * 业务UUID
     */
    @TableField(value = "ywuuid")
    private String ywuuid;
    /**
     * 配置UUID
     */
    @TableField(value = "pzuuid")
    private String pzuuid;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private String djxh;
    /**
     * 纳税人识别号
     */
    @TableField(value = "nsrsbh")
    private String nsrsbh;
    /**
     * 纳税人名称
     */
    @TableField(value = "nsrmc")
    private String nsrmc;
    /**
     * 业务编号||业务编号
     */
    @TableField(value = "ywbh")
    private String ywbh;
    /**
     * 业务明细UUID
     */
    @TableField(value = "ywmxuuid")
    private String ywmxuuid;
    /**
     * 序号
     */
    @TableField(value = "xh")
    private String xh;
    /**
     * 监控项目UUID||工作流监控项目规则主键
     */
    @TableField(value = "jkxmuuid")
    private String jkxmuuid;
    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;
    /**
     * 启用标志
     */
    @TableField(value = "qybz")
    private String qybz;
    /**
     * 是否可以设置启用标志
     */
    @TableField(value = "szqybz")
    private String szqybz;
    /**
     * 消息模板内容||消息模板内容
     */
    @TableField(value = "xxmbnr")
    private String xxmbnr;
}