package com.css.znsb.hgjc.service.checkrule.executer.impl;

import com.css.znsb.hgjc.pojo.dto.checkrule.checkitem.CheckItemReq;
import com.css.znsb.hgjc.pojo.dto.checkrule.checkitem.CheckItemRes;
import com.css.znsb.hgjc.pojo.dto.checkrule.config.CheckItemConfig;
import com.css.znsb.hgjc.service.checkrule.executer.ICheckItemExecuter;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.hgjy.service.checkrule.impl
 * @file AbsChcekRuleItem.java 创建时间:2024/4/13 17:43
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 风险分析池
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public abstract class AbsChcekItemExecuter<C extends CheckItemConfig, R extends CheckItemReq, T> implements ICheckItemExecuter<C, R> {

    public CheckItemRes doCheck(C config, R req) {

        final CheckItemRes res = new CheckItemRes();
        res.setYwId(config.getYwId());
        res.setJklxDm1(config.getJklxDm1());
        try {
            // 请求数据
            final T data = this.getBusinessData(req, config);
            // 执行规则
            final boolean checkResult = this.checkRule(config, data);
            // 返回结果
            res.setResult(checkResult);
            res.setData(data);
            if (checkResult) {
                res.setMessage(this.getPassMsg(config, data));
            } else {
                res.setMessage(this.getErrorMsg(config, data));
            }
        } catch (Exception e) {
            res.setResult(false);
            res.setMessage("检查发生异常" + e.getMessage());
        }
        // 记录结果
        this.log(res);
        return res;
    }

    protected T getBusinessData(R req, C config) {
        return this.getBusinessData(req);
    }

    protected boolean checkRule(C config, T data) {
        return false;
    }

    protected String getPassMsg(C config, T data) {
        return "检查通过";
    }

    protected String getErrorMsg(C config, T data) {
        return config.getXxmbnr();
    }

    protected abstract void log(CheckItemRes res);

    protected T getBusinessData(R req) {
        return null;
    }

}
