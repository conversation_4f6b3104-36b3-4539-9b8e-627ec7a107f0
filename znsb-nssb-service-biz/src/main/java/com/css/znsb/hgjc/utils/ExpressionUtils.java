
package com.css.znsb.hgjc.utils;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Options;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * @project 云化电子税务局
 * @package com.css.brp.manager.util
 * @file ExpressionUtils.java 创建时间:2019年10月22日下午5:36:46
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2019 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 模块名称
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class ExpressionUtils {

    private static final AviatorEvaluatorInstance expressExcuter = AviatorEvaluator.newInstance();

    static {
        // expressExcuter.setOption(Options.TRACE_EVAL, true);
        // expressExcuter.setOption(Options.USE_USER_ENV_AS_TOP_ENV_DIRECTLY, false);
        expressExcuter.setOption(Options.ENABLE_PROPERTY_SYNTAX_SUGAR, true);
        expressExcuter.setOption(Options.NIL_WHEN_PROPERTY_NOT_FOUND, true);
    }

    public static Object getValue(Object obj, String exp) {
        final Map<String, Object> env = new HashMap<>();
        env.put("$", obj);
        final String express = "$." + exp;
        final Object value = expressExcuter.execute(express, env, true);
        return value;
    }

    public static Object execute(final String expression, final Map<String, Object> env) {
        String exp = expression;
        if (exp.contains("`")) {
            // TODO 处理模板串的逻辑 后续改成正则表达式匹配 这样写有bug
            exp = exp.replace("`", "\"");
            exp = exp.replace("${", "\"+");
            exp = exp.replace("}", "+\"");
        }

        return expressExcuter.execute(exp, env, true);
    }

    public static AviatorEvaluatorInstance getInstance() {
        return expressExcuter;
    }

    /**
     * @name 检查表达式是否合法
     * @description 相关说明
     * @time 创建时间:2021/10/27 21:26
     * @param exp:
     * @return: String
     * <AUTHOR>
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static String checkExp(String exp) {
        try {
            expressExcuter.compile(exp, false);
            return null;
        } catch (Exception e) {
            return e.getMessage();
        }
    }
}
