package com.css.znsb.hgjc.controller.result;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgDO;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgmxDO;
import com.css.znsb.hgjc.pojo.dto.result.SbCheckResultQueryReq;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyJcjgService;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyJcjgmxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project qyd-znsb
 * @package com.css.znsb.hgjc.controller.result
 * @file CheckResultController.java 创建时间:2024/4/18 10:04
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 智能申报
 * @reviewer 审核人
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */
@Tag(name = "检查结果")
@RestController
@RequestMapping("/checkResult/v1")
@Validated
@Slf4j
public class CheckResultController {

    private final ZnsbHgjcQyJcjgService jcjgService;

    private final ZnsbHgjcQyJcjgmxService jgmxService;

    @Autowired
    public CheckResultController(final ZnsbHgjcQyJcjgService jcjgService, final ZnsbHgjcQyJcjgmxService jgmxService) {
        this.jcjgService = jcjgService;
        this.jgmxService = jgmxService;
    }

    @Operation(summary = "查询最后一次扫描结果")
    @PostMapping("/queryLastResult")
    public CommonResult<ZnsbHgjcQyJcjgDO> queryLastResult(@RequestBody SbCheckResultQueryReq req) {
//        ZnsbHgjcQyJcjgDO jcjgDO = new ZnsbHgjcQyJcjgDO();
//        jcjgDO.setJguuid("0220bd2e70b248c4b8a0ab062eb7e822");
//        jcjgDO.setYzpzzlDm("BDA0611111");
//        jcjgDO.setSkssqq("2024-04-01");
//        jcjgDO.setSkssqz("2024-04-30");
//        jcjgDO.setNsrmc("演示集团北京分公司1");
//        jcjgDO.setNsrsbh("91310000MA1FL70BCS");
//        jcjgDO.setDjxh("10111525000001030001");
//        jcjgDO.setCheckTime("2024-05-22 10:00:00");
//        jcjgDO.setJytgsl("10");
//        return CommonResult.success(jcjgDO);
        return CommonResult.success(this.jcjgService.queryLastResult(req));
    }

    @Operation(summary = "查询检查结果明细")
    @PostMapping("/queryCheckResultDetail")
    public CommonResult<List<ZnsbHgjcQyJcjgmxDO>> queryCheckResultDetail(@RequestParam @NotNull(message = "结果UUID不能为空") String jguuid) {
//        final List<ZnsbHgjcQyJcjgmxDO> mxList = new ArrayList<>();
        final List<ZnsbHgjcQyJcjgmxDO> mxList = this.jgmxService.queryByJguuid(jguuid);
        return CommonResult.success(mxList);
    }


}
