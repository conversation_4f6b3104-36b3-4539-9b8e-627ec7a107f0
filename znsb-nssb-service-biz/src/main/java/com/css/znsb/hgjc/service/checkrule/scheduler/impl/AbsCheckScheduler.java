package com.css.znsb.hgjc.service.checkrule.scheduler.impl;

import com.css.znsb.framework.common.util.ioc.SpringIocUtils;
import com.css.znsb.hgjc.pojo.dto.checkrule.checkitem.CheckItemReq;
import com.css.znsb.hgjc.pojo.dto.checkrule.checkitem.CheckItemRes;
import com.css.znsb.hgjc.pojo.dto.checkrule.config.CheckItemConfig;
import com.css.znsb.hgjc.service.checkrule.executer.impl.AbsChcekItemExecuter;
import com.css.znsb.hgjc.service.checkrule.scheduler.ICheckScheduler;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.hgjc.service.checkrule.scheduler.impl
 * @file AbsCheck.java 创建时间:2024/4/16 20:33
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 智能申报
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public abstract class AbsCheckScheduler<R extends CheckItemReq, C extends CheckItemConfig> implements ICheckScheduler<R, C> {
    @Override
    public void processCheck(final R req, final List<C> configList) {
        final List<CheckItemRes> checkItemRes = this.innerProcessCheck(req, configList);
        this.processResults(req, checkItemRes);
    }

    @SuppressWarnings("rawtypes")
    protected List<CheckItemRes> innerProcessCheck(final R req, final List<C> configList) {
        final List<CheckItemRes> checkItemResList = new ArrayList<>(configList.size());
        for (final C config : configList) {
            final String gzlx = config.getGzlx();

            final AbsChcekItemExecuter executer = SpringIocUtils.getBean(gzlx, AbsChcekItemExecuter.class);

            final CheckItemRes checkItemRes = executer.doCheck(config, req);

            checkItemResList.add(checkItemRes);
        }

        return checkItemResList;
    }

    protected abstract void processResults(final R req, final List<CheckItemRes> checkItemResList);
}
