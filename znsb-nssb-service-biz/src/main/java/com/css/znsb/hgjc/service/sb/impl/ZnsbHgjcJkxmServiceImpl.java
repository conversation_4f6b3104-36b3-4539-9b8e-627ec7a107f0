package com.css.znsb.hgjc.service.sb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.hgjc.mapper.ZnsbHgjcJkxmMapper;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcJkxmDO;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcJkxmService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_jkxm(合规检查-监控项目)】的数据库操作Service实现
 * @createDate 2024-04-15 21:32:51
 */
@Service
public class ZnsbHgjcJkxmServiceImpl extends ServiceImpl<ZnsbHgjcJkxmMapper, ZnsbHgjcJkxmDO>
        implements ZnsbHgjcJkxmService {


}




