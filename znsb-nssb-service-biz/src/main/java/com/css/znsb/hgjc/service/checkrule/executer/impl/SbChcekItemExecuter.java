package com.css.znsb.hgjc.service.checkrule.executer.impl;

import com.css.znsb.hgjc.pojo.dto.checkrule.checkitem.CheckItemRes;
import com.css.znsb.hgjc.pojo.dto.sb.SbChcekItemConfig;
import com.css.znsb.hgjc.pojo.dto.sb.SbCheckItemReq;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyJcjgmxService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.hgjc.service.checkrule.impl
 * @file AbsServiceSbChcekItemExecuter.java 创建时间:2024/4/16 19:11
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 智能申报
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
public class SbChcekItemExecuter<T> extends AbsChcekItemExecuter<SbChcekItemConfig, SbCheckItemReq, T> {

    @Autowired
    protected ZnsbHgjcQyJcjgmxService jgmxServce;

    protected void log(CheckItemRes res) {
        this.jgmxServce.updateResult(res.getYwId(), res.isResult(), res.getMessage());
    }
}
