package com.css.znsb.hgjc.controller.sb;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.hgjc.pojo.check.sb.SbCheckReq;
import com.css.znsb.hgjc.pojo.check.sb.SbCheckRes;
import com.css.znsb.hgjc.service.sb.ISbCheckService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project qyd-znsb
 * @package com.css.znsb.hgjy.controller.hgjy
 * @file HgjyController.java 创建时间:2024/4/13 20:33
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 风险分析池
 * @reviewer 审核人
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 */
@Tag(name = "合规检查")
@RestController
@RequestMapping("/sbCheck/v1")
@Validated
@Slf4j
public class SbCheckController {

    private final ISbCheckService sbCheckService;

    @Autowired
    public SbCheckController(final ISbCheckService sbCheckService) {
        this.sbCheckService = sbCheckService;
    }

    @PostMapping("/doCheck")
    public CommonResult<SbCheckRes> doCheck(@RequestBody final SbCheckReq req) {
      /*  req.setYzpzzlDm("BDA0610606");
        req.setJkhj("01");
        req.setDyfs("0");
        req.setNsrsbh("91510100696274826L");
        req.setSkssqq("2024-01-01");
        req.setSkssqz("2024-01-31");*/
        final SbCheckRes res = this.sbCheckService.createCheckTask(req);
        return CommonResult.success(res);
    }
}
