package com.css.znsb.hgjc.service.sb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.hgjc.mapper.ZnsbHgjcYwmxMapper;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcYwmxDO;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcYwmxService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_ywmx(合规检查业务明细)】的数据库操作Service实现
 * @createDate 2024-04-15 21:32:51
 */
@Service
public class ZnsbHgjcYwmxServiceImpl extends ServiceImpl<ZnsbHgjcYwmxMapper, ZnsbHgjcYwmxDO>
        implements ZnsbHgjcYwmxService {

    @Override
    public List<ZnsbHgjcYwmxDO> queryByYwbh(String ywbh) {
        return this.baseMapper.queryByYwbh(ywbh);
    }
}




