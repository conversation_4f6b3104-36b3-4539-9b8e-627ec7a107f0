package com.css.znsb.hgjc.pojo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合规检查-企业配置
 * @TableName znsb_hgjc_qy_pz
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "znsb_hgjc_qy_pz")
@Data
public class ZnsbHgjcQyPzDO extends BaseDO {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 配置UUID
     */
    @TableId(value = "pzuuid")
    private String pzuuid;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private String djxh;
    /**
     * 纳税人识别号
     */
    @TableField(value = "nsrsbh")
    private String nsrsbh;
    /**
     * 纳税人名称
     */
    @TableField(value = "nsrmc")
    private String nsrmc;
    /**
     * 描述信息||描述信息
     */
    @TableField(value = "msxx")
    private String msxx;
    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;
}