package com.css.znsb.hgjc.service.sb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.hgjc.mapper.ZnsbHgjcQyYwMapper;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyYwDO;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcQyYwService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_yw(合规检查-企业业务)】的数据库操作Service实现
 * @createDate 2024-04-15 21:32:51
 */
@Service
public class ZnsbHgjcQyYwServiceImpl extends ServiceImpl<ZnsbHgjcQyYwMapper, ZnsbHgjcQyYwDO>
        implements ZnsbHgjcQyYwService {

    @Override
    public ZnsbHgjcQyYwDO queryByYzpzzlJkhj(String nsrsbh, String yzpzzlDm, String jkhj) {
        return this.baseMapper.queryByYzpzzlJkhj(nsrsbh, yzpzzlDm, jkhj);
    }


}




