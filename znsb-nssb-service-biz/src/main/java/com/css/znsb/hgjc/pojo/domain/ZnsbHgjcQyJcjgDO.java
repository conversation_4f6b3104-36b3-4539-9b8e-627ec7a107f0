package com.css.znsb.hgjc.pojo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合规检查-企业检查结果
 * @TableName znsb_hgjc_qy_jcjg
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "znsb_hgjc_qy_jcjg")
@Data
public class ZnsbHgjcQyJcjgDO extends BaseDO {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 结果UUID
     */
    @TableId(value = "jguuid")
    private String jguuid;
    /**
     * 配置UUID
     */
    @TableField(value = "pzuuid")
    private String pzuuid;
    /**
     * 纳税人识别号
     */
    @TableField(value = "nsrsbh")
    private String nsrsbh;
    /**
     * 纳税人名称
     */
    @TableField(value = "nsrmc")
    private String nsrmc;
    /**
     * 监测状态代码||监测状态代码
     */
    @TableField(value = "jczt_dm")
    private String jcztDm;
    /**
     * 校验结果
     */
    @TableField(value = "jyjg_1")
    private String jyjg1;
    /**
     * 检查数量
     */
    @TableField(value = "jcsl_1")
    private String jcsl1;
    /**
     * 校验通过数量||校验通过数量
     */
    @TableField(value = "jytgsl")
    private String jytgsl;
    /**
     * 校验不通过数量||校验不通过数量
     */
    @TableField(value = "jybtgsl")
    private String jybtgsl;
    /**
     * 数据检查结果
     */
    @TableField(value = "sjjcjg")
    private String sjjcjg;
    /**
     * 检查时间
     */
    @TableField(value = "check_time")
    private String checkTime;
    /**
     * 完成时间
     */
    @TableField(value = "wcsj")
    private String wcsj;
    /**
     * 调用方式||1弹出窗口，2直接调用
     */
    @TableField(value = "dyfs")
    private String dyfs;
    /**
     * 税款所属期起
     */
    @TableField(value = "skssqq")
    private String skssqq;
    /**
     * 税款所属期止
     */
    @TableField(value = "skssqz")
    private String skssqz;
    /**
     * 应征凭证种类代码
     */
    @TableField(value = "yzpzzl_dm")
    private String yzpzzlDm;
    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;
    /**
     * 业务编号||业务编号
     */
    @TableField(value = "ywbh")
    private String ywbh;
    /**
     * 业务UUID
     */
    @TableField(value = "ywuuid")
    private String ywuuid;
    /**
     * 监控环节
     */
    @TableField(value = "jkhj")
    private String jkhj;
    /**
     * 业务名称
     */
    @TableField(value = "itemnameyw")
    private String itemnameyw;
    /**
     * 业务描述||业务描述
     */
    @TableField(value = "ywms")
    private String ywms;

    /**
     * 提示提醒数量
     */
    @TableField(value = "tstxsl")
    private String tstxsl;

    /**
     * 阻断数量
     */
    @TableField(value = "zdsl")
    private String zdsl;

    /**
     * 阻断数量
     */
    @TableField(value = "sbrwuuid")
    private String sbrwuuid;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private String djxh;
}