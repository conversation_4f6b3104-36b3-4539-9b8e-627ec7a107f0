package com.css.znsb.hgjc.pojo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合规检查-企业业务
 * @TableName znsb_hgjc_qy_yw
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "znsb_hgjc_qy_yw")
@Data
public class ZnsbHgjcQyYwDO extends BaseDO {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 业务UUID
     */
    @TableId(value = "ywuuid")
    private String ywuuid;
    /**
     * 配置UUID
     */
    @TableField(value = "pzuuid")
    private String pzuuid;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private String djxh;
    /**
     * 纳税人识别号
     */
    @TableField(value = "nsrsbh")
    private String nsrsbh;
    /**
     * 纳税人名称
     */
    @TableField(value = "nsrmc")
    private String nsrmc;
    /**
     * 业务编号||业务编号
     */
    @TableField(value = "ywbh")
    private String ywbh;
    /**
     * 业务名称
     */
    @TableField(value = "itemnameyw")
    private String itemnameyw;
    /**
     * 业务描述||业务描述
     */
    @TableField(value = "ywms")
    private String ywms;
    /**
     * 应征凭证种类代码
     */
    @TableField(value = "yzpzzl_dm")
    private String yzpzzlDm;
    /**
     * 监控环节
     */
    @TableField(value = "jkhj")
    private String jkhj;
    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;
    /**
     * 启用标志
     */
    @TableField(value = "qybz")
    private String qybz;
    /**
     * 序号
     */
    @TableField(value = "xh")
    private String xh;
}