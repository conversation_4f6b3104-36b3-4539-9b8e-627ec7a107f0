package com.css.znsb.hgjc.service.sb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.hgjc.mapper.ZnsbHgjcYwMapper;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcYwDO;
import com.css.znsb.hgjc.service.sb.ZnsbHgjcYwService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_yw(合规检查业务)】的数据库操作Service实现
 * @createDate 2024-04-15 21:32:51
 */
@Service
public class ZnsbHgjcYwServiceImpl extends ServiceImpl<ZnsbHgjcYwMapper, ZnsbHgjcYwDO>
        implements ZnsbHgjcYwService {

    @Override
    public ZnsbHgjcYwDO queryByYzpzzlJkhj(String yzpzzlDm, String jkhj) {
        return this.baseMapper.queryByYzpzzlJkhj(yzpzzlDm, jkhj);
    }
}




