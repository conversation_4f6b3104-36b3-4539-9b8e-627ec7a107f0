/*
package com.css.znsb.hgjc.config;

import com.css.znsb.cxtj.api.SimpleQueryApi;
import com.css.znsb.dlfw.api.invoke.InvokeGt4ServiceApi;
import com.css.znsb.dlfw.api.invoke.InvokeServiceApi;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.nssb.api.SbLqZzsYbnsrApi;
import com.css.znsb.nssb.api.WsbfzjgxxcxFeignClient;
import com.css.znsb.nssb.api.gy.QueryHznsbaxxApi;
import com.css.znsb.nssb.api.xfs.XfsSbbdApi;
import com.css.znsb.nssb.api.yhshgjc.IYhssycjHgjcApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {
        InvokeGt4ServiceApi.class, InvokeServiceApi.class, SbLqZzsYbnsrApi.class, NsrxxApi.class, QueryHznsbaxxApi.class,
        SimpleQueryApi.class, WsbfzjgxxcxFeignClient.class, IYhssycjHgjcApi.class, XfsSbbdApi.class, CompanyApi.class
})
public class RpcConfiguration {
}
*/
