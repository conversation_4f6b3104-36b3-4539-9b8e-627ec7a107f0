package com.css.znsb.hgjc.pojo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合规检查-企业检查结果明细
 * @TableName znsb_hgjc_qy_jcjgmx
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "znsb_hgjc_qy_jcjgmx")
@Data
public class ZnsbHgjcQyJcjgmxDO extends BaseDO {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 结果明细UUID
     */
    @TableId(value = "jgmxuuid")
    private String jgmxuuid;
    /**
     * 结果UUID
     */
    @TableField(value = "jguuid")
    private String jguuid;
    /**
     * 监测状态代码||监测状态代码
     */
    @TableField(value = "jczt_dm")
    private String jcztDm;
    /**
     * 查验通过标志
     */
    @TableField(value = "cytgbz")
    private String cytgbz;
    /**
     * 数据检查结果
     */
    @TableField(value = "sjjcjg")
    private String sjjcjg;
    /**
     * 纳税人识别号
     */
    @TableField(value = "nsrsbh")
    private String nsrsbh;
    /**
     * 纳税人名称
     */
    @TableField(value = "nsrmc")
    private String nsrmc;
    /**
     * 税款所属期起
     */
    @TableField(value = "skssqq")
    private String skssqq;
    /**
     * 税款所属期止
     */
    @TableField(value = "skssqz")
    private String skssqz;
    /**
     * 应征凭证种类代码
     */
    @TableField(value = "yzpzzl_dm")
    private String yzpzzlDm;
    /**
     * 企业业务明细UUID
     */
    @TableField(value = "qyywmxuuid")
    private String qyywmxuuid;
    /**
     * 业务UUID
     */
    @TableField(value = "ywuuid")
    private String ywuuid;
    /**
     * 配置UUID
     */
    @TableField(value = "pzuuid")
    private String pzuuid;
    /**
     * 业务编号||业务编号
     */
    @TableField(value = "ywbh")
    private String ywbh;
    /**
     * 业务明细UUID
     */
    @TableField(value = "ywmxuuid")
    private String ywmxuuid;
    /**
     * 序号
     */
    @TableField(value = "xh")
    private String xh;
    /**
     * 监控项目UUID||工作流监控项目规则主键
     */
    @TableField(value = "jkxmuuid")
    private String jkxmuuid;
    /**
     * 监控项目编码
     */
    @TableField(value = "jkxmbm")
    private String jkxmbm;
    /**
     * 监控项目名称
     */
    @TableField(value = "jkxmmc")
    private String jkxmmc;
    /**
     * 监控项目描述
     */
    @TableField(value = "jkxmms")
    private String jkxmms;
    /**
     * 业务大类编码||业务大类编码
     */
    @TableField(value = "ywdlbm")
    private String ywdlbm;
    /**
     * 业务中类编码
     */
    @TableField(value = "ywzlbm")
    private String ywzlbm;
    /**
     * 业务小类编码||业务小类编码
     */
    @TableField(value = "ywxlbm")
    private String ywxlbm;
    /**
     * 检查项目
     */
    @TableField(value = "jcxm")
    private String jcxm;
    /**
     * 征收项目代码
     */
    @TableField(value = "zsxm_dm")
    private String zsxmDm;
    /**
     * 行业中类代码
     */
    @TableField(value = "hyzl_dm")
    private String hyzlDm;
    /**
     * 行业代码
     */
    @TableField(value = "hy_dm")
    private String hyDm;
    /**
     * 监控类型代码
     */
    @TableField(value = "jklx_dm_1")
    private String jklxDm1;
    /**
     * 风险等级代码
     */
    @TableField(value = "fxdj_dm")
    private String fxdjDm;
    /**
     * 业务描述||业务描述
     */
    @TableField(value = "ywms")
    private String ywms;
    /**
     * 业务处理方式
     */
    @TableField(value = "ywclfs")
    private String ywclfs;
    /**
     * 处理建议||处理建议
     */
    @TableField(value = "cljy")
    private String cljy;
    /**
     * 处理建议说明
     */
    @TableField(value = "cljysm")
    private String cljysm;
    /**
     * 规则类型
     */
    @TableField(value = "gzlx")
    private String gzlx;
    /**
     * 业务规则配置||业务规则配置
     */
    @TableField(value = "ywgzpz")
    private String ywgzpz;
    /**
     * 版本号
     */
    @TableField(value = "bbh")
    private String bbh;
    /**
     * 消息模板内容||消息模板内容
     */
    @TableField(value = "xxmbnr")
    private String xxmbnr;
    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;
    /**
     * 业务名称
     */
    @TableField(value = "itemnameyw")
    private String itemnameyw;
    /**
     * 监控环节
     */
    @TableField(value = "jkhj")
    private String jkhj;
    /**
     * 处理标志
     */
    @TableField(value = "clbz")
    private String clbz;
    /**
     * 处理内容
     */
    @TableField(value = "clnr")
    private String clnr;
    /**
     * 处理时间||处理时间（消费方使用字段）
     */
    @TableField(value = "clsj")
    private String clsj;
    /**
     * 处理人姓名
     */
    @TableField(value = "clrxm")
    private String clrxm;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private String djxh;
}