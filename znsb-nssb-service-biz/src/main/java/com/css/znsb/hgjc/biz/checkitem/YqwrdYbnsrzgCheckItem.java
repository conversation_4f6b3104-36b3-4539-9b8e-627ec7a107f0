package com.css.znsb.hgjc.biz.checkitem;

import cn.hutool.core.date.DateUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.hgjc.pojo.dto.sb.SbChcekItemConfig;
import com.css.znsb.hgjc.pojo.dto.sb.SbCheckItemReq;
import com.css.znsb.hgjc.service.checkrule.executer.impl.SbChcekItemExecuter;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.NsrbqxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

import static com.css.znsb.hgjc.constants.CheckErrorMsgConstants.ZZSXGM_YQWRDZZSYBNSR_ERROR_MSG;

@Slf4j
@Service(value = "YqwrdYbnsrzgCheckItem")
public class YqwrdYbnsrzgCheckItem extends SbChcekItemExecuter<NsrbqxxVO> {

    @Resource
    private NsrxxApi nsrxxApi;

    protected NsrbqxxVO getBusinessData(SbCheckItemReq req) {
        String djxh = req.getDjxh();
        String nsrsbh = req.getNsrsbh();
        ZnsbMhzcQyjbxxmxReqVO qyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        qyjbxxmxReqVO.setDjxh(djxh);
        qyjbxxmxReqVO.setNsrsbh(nsrsbh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxResult = nsrxxApi.getNsrxxByNsrsbh(qyjbxxmxReqVO);
        if (!nsrxxResult.isSuccess() || GyUtils.isNull(nsrxxResult.getData())
                || GyUtils.isNull(nsrxxResult.getData().getNsrbqxx())) {
            return null;
        }
        Date skssqq = DateUtil.parse(req.getSkssqq());
        Date skssqz = DateUtil.parse(req.getSkssqz());
        return nsrxxResult.getData().getNsrbqxx().stream()
                .filter(t -> "yqwsqdjybnsrzgqy".equals(t.getBqmc())
                        && !skssqq.before(t.getYxqq())
                        && !skssqz.after(t.getYxqz()))
                .findFirst()
                .orElse(null);
    }

    protected boolean checkRule(SbChcekItemConfig config, NsrbqxxVO data) {
        return GyUtils.isNull(data) || "N".equals(data.getYxbz());
    }

    protected String getErrorMsg(SbChcekItemConfig config, NsrbqxxVO data) {
        //按照需求编写检查不通过的消息
        return ZZSXGM_YQWRDZZSYBNSR_ERROR_MSG;
    }
}
