package com.css.znsb.hgjc.controller.test;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.ioc.SpringIocUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.hgjc.pojo.dto.checkrule.checkitem.CheckItemRes;
import com.css.znsb.hgjc.pojo.dto.sb.SbChcekItemConfig;
import com.css.znsb.hgjc.pojo.dto.sb.SbCheckItemReq;
import com.css.znsb.hgjc.pojo.dto.sb.YwgzpzDTO;
import com.css.znsb.hgjc.service.checkrule.executer.impl.AbsChcekItemExecuter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * @project qyd-znsb
 * @package com.css.znsb.nssb.controller.gy
 * @file CsdmController.java 创建时间:2024/4/11 9:16
 * @title 标题（要求能简洁地表达出类的功能和职责）
 * @description 描述（简要描述类的职责、实现方式、使用注意事项等）
 * @copyright Copyright (c) 2024 中国软件与技术服务股份有限公司
 * @company 中国软件与技术服务股份有限公司
 * @module 模块: 风险分析池
 * <AUTHOR>
 * @reviewer 审核人
 * @version 1.0.0
 * @history 修订历史（历次修订内容、修订人、修订时间等）
 *
 */
@Tag(name = "test")
@RestController
@RequestMapping("/test")
@Validated
@Slf4j
public class TestController {

    @GetMapping("/checkItem/{name}")
    public CommonResult<Object> testCheckItem(@PathVariable final String name) {
        final AbsChcekItemExecuter executer = SpringIocUtils.getBean(name, AbsChcekItemExecuter.class);

        YwgzpzDTO ywgzpzDTO = new YwgzpzDTO();
        ywgzpzDTO.setQueryPath("/test/hjjc/demo001");
        ywgzpzDTO.setRule("xzqhszDm=='3400000' && tbwczt == 'N'");

        final SbChcekItemConfig config = new SbChcekItemConfig();
        config.setGzlx(name);
        final String json = JsonUtils.toJson(ywgzpzDTO);
        config.setYwgzpz(json);
        config.setXxmbnr("测试消息模板:${jgmc1}");
        config.setJklxDm1("");
        config.setJgmxuuid("000");


        final SbCheckItemReq req = new SbCheckItemReq();
        req.setYzpzzlDm("BDA0610606");
        req.setJkhj("01");
        req.setDyfs("0");
        req.setNsrsbh("91340100790122917R");
        req.setDjxh("10111525000001032495");
        req.setSkssqq("2024-01-01");
        req.setSkssqz("2024-01-31");
        req.setJguuid("0");


        final CheckItemRes checkItemRes = executer.doCheck(config, req);
        return CommonResult.success(checkItemRes);
    }

}
