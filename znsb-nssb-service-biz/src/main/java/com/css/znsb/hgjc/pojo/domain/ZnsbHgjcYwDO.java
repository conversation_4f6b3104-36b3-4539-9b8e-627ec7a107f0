package com.css.znsb.hgjc.pojo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合规检查业务
 * @TableName znsb_hgjc_yw
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "znsb_hgjc_yw")
@Data
public class ZnsbHgjcYwDO extends BaseDO {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 业务编号||业务编号
     */
    @TableId(value = "ywbh")
    private String ywbh;
    /**
     * 业务名称
     */
    @TableField(value = "itemnameyw")
    private String itemnameyw;
    /**
     * 业务描述||业务描述
     */
    @TableField(value = "ywms")
    private String ywms;
    /**
     * 应征凭证种类代码
     */
    @TableField(value = "yzpzzl_dm")
    private String yzpzzlDm;
    /**
     * 监控环节
     */
    @TableField(value = "jkhj")
    private String jkhj;
    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;
}