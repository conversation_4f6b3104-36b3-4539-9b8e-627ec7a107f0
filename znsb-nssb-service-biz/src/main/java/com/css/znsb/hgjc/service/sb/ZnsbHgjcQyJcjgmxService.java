package com.css.znsb.hgjc.service.sb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.css.znsb.hgjc.pojo.domain.ZnsbHgjcQyJcjgmxDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【znsb_hgjc_qy_jcjgmx(合规检查-企业检查结果明细)】的数据库操作Service
 * @createDate 2024-04-15 21:32:51
 */
public interface ZnsbHgjcQyJcjgmxService extends IService<ZnsbHgjcQyJcjgmxDO> {

    void updateResult(String jgmxuuid, boolean result, String msg);

    List<ZnsbHgjcQyJcjgmxDO> queryByJguuid(String jguuid);
}
