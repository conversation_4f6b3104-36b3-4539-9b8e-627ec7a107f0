package com.css.znsb.tzzx.service.fcs.impl;

import cn.hutool.core.bean.BeanUtil;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.constants.enums.TzlxDmEnum;
import com.css.znsb.gjss.mapper.fcs.ZnsbTzzxFcsfyzctzMapper;
import com.css.znsb.gjss.mapper.fcs.ZnsbTzzxFcshtxxBzhtMapper;
import com.css.znsb.gjss.mapper.fcs.ZnsbTzzxFcshtxxBzhtZlxxMapper;
import com.css.znsb.gjss.mapper.fcs.ZnsbTzzxFcshtxxZlxxjgMapper;
import com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxFcshtxxBpmMapper;
import com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxFcshtxxBpmSpzbMapper;
import com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxFcshtxxBpmZlxxMapper;
import com.css.znsb.gjss.pojo.domain.yhs.httz.ZnsbTzzxFcshtxxBpmDO;
import com.css.znsb.gjss.pojo.domain.yhs.httz.ZnsbTzzxFcshtxxBpmSpzbDO;
import com.css.znsb.gjss.pojo.domain.yhs.httz.ZnsbTzzxFcshtxxBpmZlxxDO;
import com.css.znsb.gjss.pojo.domain.yhs.httz.fcs.ZnsbTzzxFcsfyzctzDO;
import com.css.znsb.gjss.pojo.domain.yhs.httz.fcs.ZnsbTzzxFcshtxxBzhtDO;
import com.css.znsb.gjss.pojo.domain.yhs.httz.fcs.ZnsbTzzxFcshtxxBzhtZlxxDO;
import com.css.znsb.gjss.pojo.domain.yhs.httz.fcs.ZnsbTzzxFcshtxxZlxxjgDO;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.api.zqrl.ZqrlApi;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.SfzrdmxxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.mhzc.pojo.xzj.GszqxxVO;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjbMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjb;
import com.css.znsb.nssb.pojo.vo.fcscztdsys.*;
import com.css.znsb.nssb.service.cchxwssb.fcscztdsys.FcsService;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.tzzx.constants.FcsTzConstans;
import com.css.znsb.tzzx.constants.fcs.FcsConstants;
import com.css.znsb.tzzx.controller.fcs.FcsUtil;
import com.css.znsb.tzzx.feign.SapApi;
import com.css.znsb.tzzx.mapper.fcstz.*;
import com.css.znsb.tzzx.pojo.domain.tzzx.fcs.*;
import com.css.znsb.tzzx.pojo.dto.fcs.FcsNsrxxDTO;
import com.css.znsb.tzzx.pojo.vo.fcs.CzfReqVO;
import com.css.znsb.tzzx.pojo.vo.fcs.InitDataResVO;
import com.css.znsb.tzzx.pojo.vo.sap.fcszcxx.FcszcxxReqItem;
import com.css.znsb.tzzx.pojo.vo.sap.fcszcxx.FcszcxxReqVO;
import com.css.znsb.tzzx.pojo.vo.sap.fcszcxx.FcszcxxResItem;
import com.css.znsb.tzzx.pojo.vo.sap.fcszcxx.FcszcxxResVO;
import com.css.znsb.tzzx.service.fcs.FcsDataProcJobService;
import com.css.znsb.tzzx.service.fcs.ZnsbTzzxFcjcxxtzbService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【znsb_tzzx_czpzmxb(从租凭证明细表)】的数据库操作Service实现
 * @createDate 2024-08-23 16:27:18
 */
@Slf4j
@Service
public class FcsDataProcJobServiceImpl implements FcsDataProcJobService {


    /**
     * 针对表【znsb_tzzx_fcssyfckmfpdzb(房产税税源房产科目分配对照表)】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxFcssyfckmfpdzbMapper znsbtzzxfcssyfckmfpdzbmapper;


    /**
     * @description 针对表【znsb_tzzx_httzxx(合同台账信息)】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxHttzxxMapper znsbtzzxhttzxxmapper;

    /**
     * 针对表【znsb_tzzx_fcshtxx_bpm(房产税合同信息(BPM))】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxFcshtxxBpmMapper znsbtzzxfcshtxxbpmmapper;

    /**
     * 针对表【znsb_tzzx_fcshtxx_bpm_spzb(房产税合同信息商铺子表(BPM))】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxFcshtxxBpmSpzbMapper znsbtzzxfcshtxxbpmspzbmapper;

    /**
     * 针对表【znsb_tzzx_fcshtxx_bpm_zlxx(房产税合同信息租赁信息子表(BPM))】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxFcshtxxBpmZlxxMapper znsbtzzxfcshtxxbpmzlxxmapper;

    /**
     * 纳税人信息查询api
     */
    @Resource
    private NsrxxApi nsrxxApi;

    /**
     * 针对表【znsb_tzzx_czpzmxb_zjb(从租凭证明细表(中间表))】的数据库操作Mapper
     **/
    @Resource
    private ZnsbTzzxCzpzmxbZjbMapper znsbtzzxczpzmxbzjbmapper;

    /**
     * @description 针对表【znsb_tzzx_fctzmxb(房产台账明细表)】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxFctzmxbMapper znsbtzzxfctzmxbmapper;


    /**
     * 针对表【znsb_tzzx_czpzmxb(从租凭证明细表)】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxCzpzmxbMapper znsbtzzxczpzmxbmapper;

    /**
     * 房产税税源service
     */
    @Resource
    private FcsService fcsservice;

    /**
     * 房产税税源service
     */
    @Resource
    private ZnsbCxsFyxxcjbMapper znsbCxsFyxxcjbMapper;

    /**
     * @description 针对表【znsb_tzzx_httzxx_mx(合同台账明细表)】的数据库操作Mapper
     **/
    @Resource
    private ZnsbTzzxHttzxxMxMapper znsbtzzxhttzxxmxmapper;

    /**
     * 房产税资产信息房源对照表Mapper
     */
    @Resource
    private ZnsbTzzxFcszcxxdzbMapper fcszcxxdzbmapper;

    /**
     * 房产税资产信息Mapper
     */
    @Resource
    private ZnsbTzzxFcszcxxMapper fcszcxxmapper;


    /**
     * 访问产权证书号对应房源编号对照表
     */
    @Resource
    private ZnsbTzzxFcssyfcdzbMapper fcssyfcdzbMapper;


    @Resource
    private ZnsbTzzxFcjcxxtzbService znsbtzzxfcjcxxtzbservice;

    @Resource
    private ZnsbTzzxFcsfyzctzMapper znsbtzzxFcsfyzctzMapper;

    /**
     * SAP系统房屋API
     */
    @Resource
    private SapApi sapApi;

    /**
     * 房产税台账访问service
     */
    @Resource
    private ZnsbTzzxFctzmxbServiceImpl fctzmxbservice;

    @Resource
    private ZnsbTzzxFcshtxxBzhtMapper znsbTzzxFcshtxxBzhtMapper;

    @Resource
    private ZnsbTzzxFcshtxxBzhtZlxxMapper znsbTzzxFcshtxxBzhtZlxxMapper;

    @Resource
    private ZnsbTzzxFcshtxxZlxxjgMapper znsbTzzxFcshtxxZlxxjgMapper;

    @Resource
    private ZqrlApi zqrlApi;

    @Resource
    private ZnsbTzzxFcsfyzctzMapper znsbTzzxFcsfyzctzMapper;

    /**
     * 根据BPM数据生成合同信息表数据
     */
    @Transactional
    public List<Map<String, String>> bpmToHtxx(String tbgsh) {
        log.info("FcsDataProcJobServiceImpl_bpmToHtxx  tbgsh=={}", tbgsh);
        List<ZnsbTzzxFcshtxxBpmDO> bpmDoList = znsbtzzxfcshtxxbpmmapper.selectAllUpdateData(tbgsh);
        List<ZnsbTzzxFcshtxxBpmDO> updateBpmDoList = new ArrayList<>();
        log.info("bpmDoList size:{}", bpmDoList.size());
        List<ZnsbTzzxHttzxxDO> allHttzxxDOList = new ArrayList<>();
        for (ZnsbTzzxFcshtxxBpmDO bpmDo : bpmDoList) {
            String htbh = bpmDo.getHtbh();
            String gsh = bpmDo.getGsh2();
            String zbuuid = bpmDo.getUuid();

            log.info("bpmDo gsh:{}", gsh);
            log.info("bpmDo htbh:{}", htbh);

            //根据合同编号同步合同信息
            if (GyUtils.isNotNull(htbh)) {
                log.info("bpmToHtxx htbh:{}", htbh);
                try {
                    //查询合同的出租房源信息
                    List<ZnsbTzzxFcshtxxBpmSpzbDO> spzbList = znsbtzzxfcshtxxbpmspzbmapper.selectFcshtxxBpmList(zbuuid);
                    //查询合同出租租赁信息
                    List<ZnsbTzzxFcshtxxBpmZlxxDO> zlxxList = znsbtzzxfcshtxxbpmzlxxmapper.selectFcshtxxBpmList(zbuuid);
                    //计算合同的出租总面积
                    double czzmj = spzbList.stream().mapToDouble(spzbDo -> spzbDo.getCzmj().doubleValue()).sum();
                    //查询合同的出租房源信息，每条房源信息会对应多条出租信息
                    for (ZnsbTzzxFcshtxxBpmSpzbDO znsbtzzxfcshtxxbpmspzbdo : spzbList) {
                        BigDecimal czmj = znsbtzzxfcshtxxbpmspzbdo.getCzmj();
                        String spmc = znsbtzzxfcshtxxbpmspzbdo.getSpmc1();
                        if (GyUtils.isNull(spmc)) {
                            continue;
                        }
                        ZnsbCxsFyxxcjb fyjcxx = znsbtzzxfcjcxxtzbservice.queryFyxxByFwcqzsh(spmc);
                        if (GyUtils.isNotNull(fyjcxx)) {
                            for (ZnsbTzzxFcshtxxBpmZlxxDO znsbtzzxfcshtxxbpmzlxxdo : zlxxList) {
                                ZnsbTzzxHttzxxDO znsbtzzxhttzxxdo = this.getHtxxDo(fyjcxx, bpmDo);
                                if (GyUtils.isNull(znsbtzzxhttzxxdo.getFwcqzsh())) {
                                    znsbtzzxhttzxxdo.setFwcqzsh(spmc);
                                }
                                znsbtzzxhttzxxdo.setCzmj(czmj);
                                znsbtzzxhttzxxdo.setZlrqq(znsbtzzxfcshtxxbpmzlxxdo.getZlrqq());
                                znsbtzzxhttzxxdo.setZlrqz(znsbtzzxfcshtxxbpmzlxxdo.getZlrqz());

                                double czzje = znsbtzzxfcshtxxbpmzlxxdo.getCzzje().doubleValue();
                                int czys = znsbtzzxfcshtxxbpmzlxxdo.getZlys();
                                double hsczjemy = czzje / czys;
                                //按照房源面积比例计算出租金额
                                double mjbl = czmj.doubleValue() / czzmj;
                                String sm = znsbtzzxfcshtxxbpmzlxxdo.getDm();
                                List<Map<String, Object>> slMapList = CacheUtils.getTableData("cs_tzzx_smsldzb");
                                List<Map<String, Object>> list = slMapList.stream().filter(slMap -> sm.equals(slMap.get("sm4"))).collect(Collectors.toList());
                                BigDecimal sl1 = new BigDecimal(0.0);
                                if (GyUtils.isNotNull(list)) {
                                    sl1 = (BigDecimal) list.get(0).get("sl1");
                                }
                                log.info("bpmToHtxx sm:{}", sm);
                                log.info("bpmToHtxx sl1:{}", sl1);
                                znsbtzzxhttzxxdo.setSl1(sl1);
                                double bhsczjemy = hsczjemy * mjbl / (1 + sl1.doubleValue());
                                double pjczzje = czzje * mjbl / (1 + sl1.doubleValue());
                                znsbtzzxhttzxxdo.setLy(FcsTzConstans.LY_DSRW);
                                znsbtzzxhttzxxdo.setYzjsr(new BigDecimal(bhsczjemy).setScale(6, BigDecimal.ROUND_HALF_UP));
                                double cZhsczjemy = bhsczjemy * (1 + sl1.doubleValue());
                                znsbtzzxhttzxxdo.setHsyzjsr(new BigDecimal(cZhsczjemy).setScale(6, BigDecimal.ROUND_HALF_UP));
                                znsbtzzxhttzxxdo.setHsje(new BigDecimal(pjczzje).setScale(2, BigDecimal.ROUND_HALF_UP));
                                znsbtzzxhttzxxdo.setSjsj();
                                log.info("FcsDataProcJobServiceImpl_bpmToHtxx  znsbtzzxhttzxxdo==" + JsonUtils.toJson(znsbtzzxhttzxxdo));
                                allHttzxxDOList.add(znsbtzzxhttzxxdo);
                            }
                        }
                    }
                    ZnsbTzzxFcshtxxBpmDO updateDO = new ZnsbTzzxFcshtxxBpmDO();
                    updateDO.setUuid(bpmDo.getUuid());
                    updateDO.setGzbz("N");
                    updateBpmDoList.add(updateDO);
                } catch (Exception e) {
                    log.error(htbh, e);
                }
            }
        }

        log.info("FcsDataProcJobServiceImpl_bpmToHtxx allHttzxxDOList1={}", JsonUtils.toJson(allHttzxxDOList));
        //相同合同编号的数据设置为相同uuid
        Map<String, ZnsbTzzxHttzxxDO> htxxMap = new HashMap<>();
        List<ZnsbTzzxHttzxxDO> insertHttzxx = new ArrayList<>();
        List<ZnsbTzzxHttzxxDO> updateHttzxx = new ArrayList<>();
        for (ZnsbTzzxHttzxxDO znsbtzzxhttzxxdo : allHttzxxDOList) {
            String htbh = znsbtzzxhttzxxdo.getHtbh();
            String fybh = znsbtzzxhttzxxdo.getFybh();
            String key = htbh + "_" + fybh;
            ZnsbTzzxHttzxxDO htxxDO = htxxMap.get(key);
            if (GyUtils.isNull(htxxDO)) {
                List<ZnsbTzzxHttzxxDO> htDoList = znsbtzzxhttzxxmapper.selectHtxxListByHtbh(htbh, fybh);
                if (GyUtils.isNotNull(htDoList)) {
                    ZnsbTzzxHttzxxDO zhttzxxDo = htDoList.get(0);
                    znsbtzzxhttzxxdo.setUuid(zhttzxxDo.getUuid());
                    updateHttzxx.add(znsbtzzxhttzxxdo);
                    htxxMap.put(key, znsbtzzxhttzxxdo);
                } else {
                    insertHttzxx.add(znsbtzzxhttzxxdo);
                    htxxMap.put(key, znsbtzzxhttzxxdo);
                }
            } else {
                znsbtzzxhttzxxdo.setUuid(htxxDO.getUuid());
                znsbtzzxhttzxxdo.setLy(htxxDO.getLy());
            }
        }

        log.info("FcsDataProcJobServiceImpl_bpmToHtxx allHttzxxDOList={}2", JsonUtils.toJson(allHttzxxDOList));

        //生成需要增加修改删除的租赁明细数据
        List<ZnsbTzzxHttzxxMxDO> insertHttzMxxx = new ArrayList<>();
        List<ZnsbTzzxHttzxxMxDO> updateHttzMxxx = new ArrayList<>();
        List<ZnsbTzzxHttzxxMxDO> deleteHttzMxxx = new ArrayList<>();
        List<Map<String, String>> updateFctzList = new ArrayList<>();

        Map<String, List<ZnsbTzzxHttzxxMxDO>> htmxListMap = this.getHtmxListMap(allHttzxxDOList);
        log.info("FcsDataProcJobServiceImpl_bpmToHtxx htmxListMap={}2", JsonUtils.toJson(htmxListMap));

        //根据本次拆分的合同明细加工合同明细需要新增、修改删除的合同明细数据
        Set<String> keySet = htmxListMap.keySet();
        for (String key : keySet) {
            //根据key拆分获取合同编号、合同uuid、房源编号
            String[] keyArray = key.split("_");
            String fybh = keyArray[1];
            String htUuid = keyArray[2];
            //获取本次同步的合同明细数据
            List<ZnsbTzzxHttzxxMxDO> bccfMxDOList = htmxListMap.get(key);
            List<ZnsbTzzxHttzxxMxDO> yyMxDOList = znsbtzzxhttzxxmxmapper.selectAllHtmxdoByZbUuid(htUuid);
            log.info("FcsDataProcJobServiceImpl_bpmToHtxx bccfMxDOList={}2", JsonUtils.toJson(bccfMxDOList));
            log.info("FcsDataProcJobServiceImpl_bpmToHtxx yyMxDOList={}2", JsonUtils.toJson(yyMxDOList));
            bccfMxDOList.forEach(mx -> {
                mx.setLy(FcsTzConstans.LY_DSRW);
            });

            //如果查询不到，所有子表数据全部新增
            if (GyUtils.isNull(yyMxDOList)) {
                insertHttzMxxx.addAll(bccfMxDOList);
                for (ZnsbTzzxHttzxxMxDO htmxDo : bccfMxDOList) {
                    Map<String, String> updateMap = new HashMap<>();
                    updateMap.put("fybh", fybh);
                    String ssyf = DateUtil.doDateFormat(htmxDo.getZlrqq(), "yyyy-MM-dd");
                    updateMap.put("ssyf", ssyf);
                    updateFctzList.add(updateMap);
                }
            } else {
                //获取修改和删除的合同租赁明细信息
                for (ZnsbTzzxHttzxxMxDO yymxDo : yyMxDOList) {
                    boolean delFlag = true;
                    for (ZnsbTzzxHttzxxMxDO htmxDo : bccfMxDOList) {
                        if (yymxDo.getSszq().intValue() == htmxDo.getSszq().intValue()) {
                            delFlag = false;
                            if (yymxDo.getLy().equals(FcsTzConstans.LY_DSRW)) {
                                htmxDo.setUuid(yymxDo.getUuid());
                                updateHttzMxxx.add(htmxDo);
                                Map<String, String> updateMap = new HashMap<>();
                                updateMap.put("fybh", fybh);
                                String ssyf = DateUtil.doDateFormat(htmxDo.getZlrqq(), "yyyy-MM-dd");
                                updateMap.put("ssyf", ssyf);
                                updateFctzList.add(updateMap);
                            }
                        }
                    }
                    if (delFlag) {
                        deleteHttzMxxx.add(yymxDo);
                        Map<String, String> updateMap = new HashMap<>();
                        updateMap.put("fybh", fybh);
                        String ssyf = DateUtil.doDateFormat(yymxDo.getZlrqq(), "yyyy-MM-dd");
                        updateMap.put("ssyf", ssyf);
                        updateFctzList.add(updateMap);
                    }
                }
                //获取新增的合同租赁明细信息
                for (ZnsbTzzxHttzxxMxDO tbDo : bccfMxDOList) {
                    boolean insertFlag = true;
                    for (ZnsbTzzxHttzxxMxDO htmxDo : yyMxDOList) {
                        if (htmxDo.getSszq().intValue() == tbDo.getSszq().intValue()) {
                            insertFlag = false;
                            break;
                        }
                    }
                    if (insertFlag) {
                        insertHttzMxxx.add(tbDo);
                        Map<String, String> updateMap = new HashMap<>();
                        updateMap.put("fybh", fybh);
                        String ssyf = DateUtil.doDateFormat(tbDo.getZlrqq(), "yyyy-MM-dd");
                        updateMap.put("ssyf", ssyf);
                        updateFctzList.add(updateMap);
                    }
                }
            }
        }

        if (GyUtils.isNotNull(insertHttzxx)) {
            log.info("FcsDataProcJobServiceImpl_bpmToHtxx insertHttzxx={}", JsonUtils.toJson(insertHttzxx));
            znsbtzzxhttzxxmapper.insertBatch(insertHttzxx);
        }

        if (GyUtils.isNotNull(updateHttzxx)) {
            log.info("FcsDataProcJobServiceImpl_bpmToHtxx updateHttzxx={}", JsonUtils.toJson(updateHttzxx));
            znsbtzzxhttzxxmapper.updateBatch(updateHttzxx);
        }

        if (GyUtils.isNotNull(insertHttzMxxx)) {
            log.info("FcsDataProcJobServiceImpl_bpmToHtxx insertHttzMxxx={}", JsonUtils.toJson(insertHttzMxxx));
            znsbtzzxhttzxxmxmapper.insertBatch(insertHttzMxxx);
        }

        if (GyUtils.isNotNull(updateHttzMxxx)) {
            log.info("FcsDataProcJobServiceImpl_bpmToHtxx updateHttzMxxx={}", JsonUtils.toJson(updateHttzMxxx));
            znsbtzzxhttzxxmxmapper.updateBatch(updateHttzMxxx);
        }

        if (GyUtils.isNotNull(deleteHttzMxxx)) {
            log.info("FcsDataProcJobServiceImpl_bpmToHtxx deleteHttzMxxx={}", JsonUtils.toJson(deleteHttzMxxx));
            znsbtzzxhttzxxmxmapper.deleteBatchIds(deleteHttzMxxx);
        }

//        if(GyUtils.isNotNull(bpmDoList)){
//            List<ZnsbTzzxFcshtxxBpmDO> bpmDoListUpdateList = new ArrayList<>();
//            bpmDoList.forEach(bpmDo->{
//                ZnsbTzzxFcshtxxBpmDO updateDO = new ZnsbTzzxFcshtxxBpmDO();
//                updateDO.setUuid(bpmDo.getUuid());
//                updateDO.setGzbz("N");
//                bpmDoListUpdateList.add(updateDO);
//            });
//        }
        znsbtzzxfcshtxxbpmmapper.updateBatch(updateBpmDoList);
        log.info("FcsDataProcJobServiceImpl_bpmToHtxx updateFctzList={}", JsonUtils.toJson(updateFctzList));
        return updateFctzList;
    }


    /**
     * 根据拆分出的合同信息，按合同编号和房源编号分组获取合同明细信息
     *
     * @param allHttzxxDOList
     * @return
     */
    private Map<String, List<ZnsbTzzxHttzxxMxDO>> getHtmxListMap(List<ZnsbTzzxHttzxxDO> allHttzxxDOList) {
        log.info("FcsDataProcJobServiceImpl_getHtmxListMap  allHttzxxDOList=={}", JsonUtils.toJson(allHttzxxDOList));
        Map<String, List<ZnsbTzzxHttzxxMxDO>> htmxListMap = new HashMap<>();
        for (ZnsbTzzxHttzxxDO htxxDo : allHttzxxDOList) {
            String key = htxxDo.getHtbh() + "_" + htxxDo.getFybh() + "_" + htxxDo.getUuid();
            List<ZnsbTzzxHttzxxMxDO> mxDOList = htmxListMap.get(key);
            if (GyUtils.isNull(mxDOList)) {
                List<ZnsbTzzxHttzxxDO> cxHtxx = new ArrayList<>();
                cxHtxx.add(htxxDo);
                mxDOList = this.getHtmxList(cxHtxx);
                htmxListMap.put(key, mxDOList);
            } else {
                List<ZnsbTzzxHttzxxDO> cxHtxx = new ArrayList<>();
                cxHtxx.add(htxxDo);
                List<ZnsbTzzxHttzxxMxDO> mxDOList1 = this.getHtmxList(cxHtxx);
                mxDOList.addAll(mxDOList1);
            }
        }
        return htmxListMap;
    }

    private ZnsbTzzxHttzxxDO getHtxxDo(ZnsbCxsFyxxcjb fyjcxx, ZnsbTzzxFcshtxxBpmDO bpmDo) {
        log.info("FcsDataProcJobServiceImpl_getHtxxDo  bpmDo=={}", JsonUtils.toJson(bpmDo));
        ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setDjxh(fyjcxx.getDjxh());
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxx = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
        ZnsbMhzcQyjbxxmxResVO znsbmhzcqyjbxxmxresvo = nsrxx.getData();
        List<JbxxmxsjVO> jbxxmxsjVOList = znsbmhzcqyjbxxmxresvo.getJbxxmxsj();
        JbxxmxsjVO jbxxmxsjvo = jbxxmxsjVOList.get(0);

        ZnsbTzzxHttzxxDO znsbtzzxhttzxxdo = new ZnsbTzzxHttzxxDO();
        this.setDefaultValue(znsbtzzxhttzxxdo);
        znsbtzzxhttzxxdo.setDjxh(new BigDecimal(fyjcxx.getDjxh()));
        znsbtzzxhttzxxdo.setNsrsbh(jbxxmxsjvo.getNsrsbh());
        znsbtzzxhttzxxdo.setNsrmc(jbxxmxsjvo.getNsrmc());
        znsbtzzxhttzxxdo.setZgswjgDm(fyjcxx.getZgswskfjDm());
        znsbtzzxhttzxxdo.setFybh(fyjcxx.getFybh());
        znsbtzzxhttzxxdo.setFwcqzsh(fyjcxx.getFwcqzsh());
        znsbtzzxhttzxxdo.setFwzldz(fyjcxx.getFwzldz());
        znsbtzzxhttzxxdo.setHtbh(bpmDo.getHtbh());
        znsbtzzxhttzxxdo.setHtmc(bpmDo.getHtmc());
        znsbtzzxhttzxxdo.setGsmc(bpmDo.getGsmc());
        znsbtzzxhttzxxdo.setGsh2(bpmDo.getGsh2());
        znsbtzzxhttzxxdo.setLrzx(bpmDo.getLrzx());
        znsbtzzxhttzxxdo.setHtqdrq(bpmDo.getHtqdrq());
        znsbtzzxhttzxxdo.setHtydsxrq(bpmDo.getHtydsxrq());
        znsbtzzxhttzxxdo.setHtydzzrq(bpmDo.getHtydzzrq());
//                                znsbtzzxhttzxxdo.setSl1(FcsTzConstans.CZSL);
        znsbtzzxhttzxxdo.setSkfs(bpmDo.getSkfs());
        znsbtzzxhttzxxdo.setZzrq(bpmDo.getZzrq());
        return znsbtzzxhttzxxdo;
    }

    /**
     * 根据BPM数据信息生成房产税台账
     */
    @Override
    @Transactional
    public void htxxToFcstz(String scyf, String tbfybh) {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("FcsDataProcJobServiceImpl_htxxToFcstz  start");
        log.info("htxxToFcstz jobParam::{}", jobParam);
        log.info("htxxToFcstz scyf::{}", scyf);
        log.info("htxxToFcstz tbfybh::{}", tbfybh);
        Date skssqq = this.getSkssqq(null);
        Date skssqz = this.getSkssqz(null);
        if (GyUtils.isNotNull(scyf)) {
            skssqq = this.getSkssqq(scyf);
            skssqz = this.getSkssqz(scyf);
        } else if (GyUtils.isNotNull(jobParam)) {
            skssqq = this.getSkssqq(jobParam);
            skssqz = this.getSkssqz(jobParam);
        }
        log.info("htxxToFcstz skssqq={}", skssqq);
        log.info("htxxToFcstz skssqz={}", skssqz);

        //获取合同信息
        Map<String, ZnsbTzzxFctzmxbDO> htfybytzxxMap = new HashMap<>();
        List<ZnsbCxsFyxxcjb> allFyxx = null;
        if (GyUtils.isNull(tbfybh)) {
            allFyxx = znsbCxsFyxxcjbMapper.selectList();
        } else {
            ZnsbCxsFyxxcjb Fyxxcjb = znsbCxsFyxxcjbMapper.queryByFybh(tbfybh, null, false);
            allFyxx = new ArrayList<>();
            allFyxx.add(Fyxxcjb);
        }
        //遍历房产信息生成房产税台账信息
        for (ZnsbCxsFyxxcjb jcxxDo : allFyxx) {
            //判断是否是上线房产税的企业
            final Map<String, Object> pzbMap = CacheUtils.getTableData("cs_znsb_ftssbpzb", String.valueOf(jcxxDo.getDjxh()));
            log.info("FctzmxController_initData  djxh::{}", jcxxDo.getDjxh());
            log.info("FctzmxController_initData  pzbMap::{}", JsonUtils.toJson(pzbMap));
            if (GyUtils.isNull(pzbMap)) {
                continue;
            }
            String fybh = jcxxDo.getFybh();
            List<ZnsbTzzxFcssyfckmfpdzbDO> fcssyfckmfpdzbList = znsbtzzxfcssyfckmfpdzbmapper.selecDzbxxListByFybh(fybh);
            if (GyUtils.isNotNull(fcssyfckmfpdzbList)) {
                continue;
            }
            if (FcsUtil.isNsywzz(jcxxDo, skssqz)) {
                continue;
            }
            ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdo = new ZnsbTzzxFctzmxbDO();
            this.setDefaultValue(znsbtzzxfctzmxbdo);
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(jcxxDo.getDjxh());
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxx = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            ZnsbMhzcQyjbxxmxResVO znsbmhzcqyjbxxmxresvo = nsrxx.getData();
            List<JbxxmxsjVO> jbxxmxsjVOList = znsbmhzcqyjbxxmxresvo.getJbxxmxsj();
            JbxxmxsjVO jbxxmxsjvo = jbxxmxsjVOList.get(0);
            znsbtzzxfctzmxbdo.setDjxh(new BigDecimal(jbxxmxsjvo.getDjxh()));

            znsbtzzxfctzmxbdo.setNsrmc(jbxxmxsjvo.getNsrmc());
            znsbtzzxfctzmxbdo.setNsrsbh(jbxxmxsjvo.getNsrsbh());
            znsbtzzxfctzmxbdo.setFybh(jcxxDo.getFybh());
            znsbtzzxfctzmxbdo.setFwcqzsh(jcxxDo.getFwcqzsh());
            znsbtzzxfctzmxbdo.setFwzldz(jcxxDo.getFwzldz());
            znsbtzzxfctzmxbdo.setSkssqq(skssqq);
            znsbtzzxfctzmxbdo.setSkssqz(skssqz);
            znsbtzzxfctzmxbdo.setCzbz("2");
            znsbtzzxfctzmxbdo.setCzmj(new BigDecimal("0.0"));
            znsbtzzxfctzmxbdo.setZjsr1(new BigDecimal("0.0"));
            znsbtzzxfctzmxbdo.setSl1(FcsTzConstans.CZSL);
            znsbtzzxfctzmxbdo.setYnse(new BigDecimal("0.0"));
            znsbtzzxfctzmxbdo.setZbuuid(jcxxDo.getUuid());
//                znsbtzzxfctzmxbdo.setFcyz(znsbtzzxfcjcxxtzbdo.getFcyz());
            htfybytzxxMap.put(jcxxDo.getFybh(), znsbtzzxfctzmxbdo);
        }


        //获取合同主表信息
        List<ZnsbTzzxHttzxxDO> htxxList = znsbtzzxhttzxxmapper.selectHtxxListByDate(skssqq, skssqz, tbfybh);

        log.info("htxxToFcstz htxxList size={}", htxxList.size());
        //遍历合同信息
        for (ZnsbTzzxHttzxxDO htdo : htxxList) {
            String fybh = htdo.getFybh();
            log.info("htxxToFcstz fybh={}", fybh);
            Date zzrq = htdo.getZzrq();
            //房源编号为空不加工
            if (GyUtils.isNull(fybh)) {
                continue;
            }
            //合同终止日期在当前税款所属期起之前的不加工
            if (GyUtils.isNotNull(zzrq) && skssqq.after(zzrq)) {
                continue;
            }

            //根据房源编号获取房屋基本信息，获取不到不加工
            ZnsbCxsFyxxcjb fyjcxx = znsbCxsFyxxcjbMapper.queryByFybh(fybh, null, false);
            if (GyUtils.isNull(fyjcxx) || FcsUtil.isNsywzz(fyjcxx, skssqz)) {
                continue;
            }

            //判断是否是上线房产税的企业
            final Map<String, Object> pzbMap = CacheUtils.getTableData("cs_znsb_ftssbpzb", String.valueOf(htdo.getDjxh()));
            log.info("FctzmxController_initData  djxh::{}", htdo.getDjxh());
            log.info("FctzmxController_initData  pzbMap::{}", JsonUtils.toJson(pzbMap));
            if (GyUtils.isNull(pzbMap)) {
                continue;
            }

            Integer sszq = Integer.parseInt(DateUtil.doDateFormat(skssqq, "yyyyMM"));
            List<ZnsbTzzxHttzxxMxDO> htmxDoList = znsbtzzxhttzxxmxmapper.selectHtmxdoByZbUuidSszq(htdo.getUuid(), sszq);
//            if(GyUtils.isNull(htmxDoList)){
//                continue;
//            }

            double byzjsr = 0.0;
            for (ZnsbTzzxHttzxxMxDO mxDo : htmxDoList) {
                byzjsr += mxDo.getSjyzjsr().doubleValue();
            }
            //本月应纳税额
            double byynse = byzjsr * FcsTzConstans.CZSL.doubleValue();
            log.info("htxxToFcstz byzjsr={}", byzjsr);
            log.info("htxxToFcstz byynse={}", byynse);
            BigDecimal byzjsrd = new BigDecimal(byzjsr).setScale(6, BigDecimal.ROUND_HALF_UP);
            BigDecimal byynsed = new BigDecimal(byynse).setScale(2, BigDecimal.ROUND_HALF_UP);

            //根据房源编号生成从租信息
            ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdo = htfybytzxxMap.get(fybh);
            if (GyUtils.isNull(znsbtzzxfctzmxbdo)) {
                znsbtzzxfctzmxbdo = new ZnsbTzzxFctzmxbDO();
                this.setDefaultValue(znsbtzzxfctzmxbdo);
                znsbtzzxfctzmxbdo.setDjxh(htdo.getDjxh());
                znsbtzzxfctzmxbdo.setNsrmc(htdo.getNsrmc());
                znsbtzzxfctzmxbdo.setNsrsbh(htdo.getNsrsbh());
                znsbtzzxfctzmxbdo.setFybh(fybh);
                znsbtzzxfctzmxbdo.setFwcqzsh(htdo.getFwcqzsh());
                znsbtzzxfctzmxbdo.setFwzldz(htdo.getFwzldz());
                znsbtzzxfctzmxbdo.setSkssqq(skssqq);
                znsbtzzxfctzmxbdo.setSkssqz(skssqz);
                znsbtzzxfctzmxbdo.setCzbz(FcsTzConstans.CZDM);
                double czmj = 0.0;
                for (ZnsbTzzxHttzxxMxDO mxDo : htmxDoList) {
                    czmj += mxDo.getSjczmj().doubleValue();
                }
                znsbtzzxfctzmxbdo.setCzmj(new BigDecimal(czmj));
                znsbtzzxfctzmxbdo.setZjsr1(byzjsrd);
                znsbtzzxfctzmxbdo.setSl1(FcsTzConstans.CZSL);
                znsbtzzxfctzmxbdo.setYnse(byynsed);
                znsbtzzxfctzmxbdo.setZbuuid(fyjcxx.getUuid());
                htfybytzxxMap.put(fybh, znsbtzzxfctzmxbdo);
            } else {
                double czmj = 0.0;
                for (ZnsbTzzxHttzxxMxDO mxDo : htmxDoList) {
                    czmj += mxDo.getSjczmj().doubleValue();
                }
                Double zczmj = znsbtzzxfctzmxbdo.getCzmj().doubleValue() + czmj;
                Double zzjsr = znsbtzzxfctzmxbdo.getZjsr1().doubleValue() + byzjsrd.doubleValue();
                Double zynse = znsbtzzxfctzmxbdo.getYnse().doubleValue() + byynsed.doubleValue();
                znsbtzzxfctzmxbdo.setCzmj(new BigDecimal(zczmj));
                znsbtzzxfctzmxbdo.setZjsr1(new BigDecimal(zzjsr));
                znsbtzzxfctzmxbdo.setYnse(new BigDecimal(zynse));
            }
        }

        //根据合同归集信息保存修改房产台账明细表
        Set<String> keySet = htfybytzxxMap.keySet();
        List<ZnsbTzzxFctzmxbDO> addList = new ArrayList<>();
        List<ZnsbTzzxFctzmxbDO> updateList = new ArrayList<>();
        for (String key : keySet) {
            log.info("htxxToFcstz key={}", key);
            ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdo = htfybytzxxMap.get(key);
            Double hjynse = znsbtzzxfctzmxbdo.getZjsr1().doubleValue() * FcsTzConstans.CZSL.doubleValue();
            znsbtzzxfctzmxbdo.setYnse(new BigDecimal(hjynse).setScale(2, BigDecimal.ROUND_HALF_UP));
            //根据房源编号加税款所属期起止查询房产税台账信息
            ZnsbTzzxFctzmxbDO cxdo = new ZnsbTzzxFctzmxbDO();
            cxdo.setFybh(key);
            cxdo.setSkssqq(skssqq);
            cxdo.setSkssqz(skssqz);
            List<ZnsbTzzxFctzmxbDO> znsbtzzxfctzmxbdocList = znsbtzzxfctzmxbmapper.selecFctzmxByFybhSkssq(cxdo);

            ZnsbCxsFyxxcjb fyjcxx = znsbCxsFyxxcjbMapper.queryByFybh(key, null, true);
            if (GyUtils.isNull(fyjcxx)) {
                continue;
            }
            CjmxVO cjmxVO = this.getCjxxVo(fyjcxx, skssqq);
            if (cjmxVO.getFcyz() == 0.0) {
                continue;
            }
            //如果查询不到则新增
            if (GyUtils.isNull(znsbtzzxfctzmxbdocList)) {
                log.info("htxxToFcstz insert key={}", key);
                //从价房产税台账
                ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdoCj = new ZnsbTzzxFctzmxbDO();
                BeanUtil.copyProperties(znsbtzzxfctzmxbdo, znsbtzzxfctzmxbdoCj, true);
                znsbtzzxfctzmxbdoCj.setCzbz("1");
                double fczmj = fyjcxx.getJzmj().doubleValue();
                //如果建筑面积等于0，不处理
                log.info("htxxToFcstz insert fczmj={}", fczmj);
                if (new BigDecimal(0.0).compareTo(fyjcxx.getJzmj()) == 0) {
                    continue;
                }
                double czmj = znsbtzzxfctzmxbdo.getCzmj().doubleValue();
                log.info("htxxToFcstz insert czmj={}", czmj);
                //获取房产原值
                double fcyz = cjmxVO.getFcyz();
                double czfcyz = (czmj / fczmj) * fcyz;
                double cjfcyz = fcyz - czfcyz;
                double jsbl = cjmxVO.getJsbl();

                //从租房产原值
                znsbtzzxfctzmxbdo.setFcyz(new BigDecimal(czfcyz).setScale(2, BigDecimal.ROUND_HALF_UP));
                //从价房产原值
                double ynse = cjfcyz * jsbl * FcsTzConstans.CJSL.doubleValue() / 12;
                znsbtzzxfctzmxbdoCj.setCzmj(znsbtzzxfctzmxbdo.getCzmj());
                znsbtzzxfctzmxbdoCj.setZjsr1(znsbtzzxfctzmxbdo.getZjsr1());
                znsbtzzxfctzmxbdoCj.setFcyz(new BigDecimal(cjfcyz).setScale(2, BigDecimal.ROUND_HALF_UP));
                znsbtzzxfctzmxbdoCj.setYnse(new BigDecimal(ynse).setScale(2, BigDecimal.ROUND_HALF_UP));
                znsbtzzxfctzmxbdoCj.setSl1(FcsTzConstans.CJSL);
                this.setDefaultValue(znsbtzzxfctzmxbdoCj);

                //新增从租从价房产税台账
                addList.add(znsbtzzxfctzmxbdo);
                addList.add(znsbtzzxfctzmxbdoCj);
                //查询到房产税台账信息修改
            } else {
                log.info("htxxToFcstz update key={}", key);
                ZnsbTzzxFctzmxbDO czfctzmxbdoUpdate = new ZnsbTzzxFctzmxbDO();
                ZnsbTzzxFctzmxbDO cjfctzmxbdoUpDate = new ZnsbTzzxFctzmxbDO();

                String syzt = "0";
                for (ZnsbTzzxFctzmxbDO tzmxbdo : znsbtzzxfctzmxbdocList) {
                    if ("1".equals(tzmxbdo.getCzbz())) {
                        tzmxbdo.setXgrq(new Date());
                        tzmxbdo.setXgrsfid("dsrw");
                        cjfctzmxbdoUpDate = tzmxbdo;
                    } else if ("2".equals(tzmxbdo.getCzbz())) {
                        syzt = tzmxbdo.getSyzt();
                        znsbtzzxfctzmxbdo.setXgrq(new Date());
                        znsbtzzxfctzmxbdo.setXgrsfid("dsrw");
                        czfctzmxbdoUpdate = tzmxbdo;
                    }
                }

                //房产建筑面积
                double fczmj = fyjcxx.getJzmj().doubleValue();
                //出租面积
                double czmj = znsbtzzxfctzmxbdo.getCzmj().doubleValue();
                //获取房产总原值
                double fcyz = cjmxVO.getFcyz().doubleValue();
                //出租房产原值
                double czfcyz = (czmj / fczmj) * fcyz;
                //从价房产原值
                double cjfcyz = fcyz - czfcyz;
                //计税比例
                double jsbl = cjmxVO.getJsbl().doubleValue();

                //本次同步出租面积
                BigDecimal bcCzmj = znsbtzzxfctzmxbdo.getCzmj().setScale(2, BigDecimal.ROUND_HALF_UP);
                //原始台账出租面积
                BigDecimal ysCzmj = czfctzmxbdoUpdate.getCzmj().setScale(2, BigDecimal.ROUND_HALF_UP);

                //本次同步租金收入
                BigDecimal bcZjsr = znsbtzzxfctzmxbdo.getZjsr1().setScale(6, BigDecimal.ROUND_HALF_UP);
                //原始同步租金收入
                BigDecimal ysZjsr = czfctzmxbdoUpdate.getZjsr1().setScale(6, BigDecimal.ROUND_HALF_UP);

                //本次同步租金收入
                BigDecimal bcFcyz = new BigDecimal(czfcyz).setScale(2, BigDecimal.ROUND_HALF_UP);
                //原始同步租金收入
                BigDecimal ysFcyz = czfctzmxbdoUpdate.getFcyz().setScale(2, BigDecimal.ROUND_HALF_UP);

                //如果税源状态不是未生成状态，并且数据改变，数据变更状态为已变更
                if (!"0".equals(syzt)) {
                    if (bcCzmj.compareTo(ysCzmj) != 0 || bcZjsr.compareTo(ysZjsr) != 0 || bcFcyz.compareTo(ysFcyz) != 0) {
                        czfctzmxbdoUpdate.setBgzt("Y");
                        cjfctzmxbdoUpDate.setBgzt("Y");
                    }
                }

                //生成从租修改vo
                czfctzmxbdoUpdate.setCzmj(znsbtzzxfctzmxbdo.getCzmj());
                czfctzmxbdoUpdate.setFcyz(new BigDecimal(czfcyz).setScale(2, BigDecimal.ROUND_HALF_UP));
                czfctzmxbdoUpdate.setZjsr1(znsbtzzxfctzmxbdo.getZjsr1());
                czfctzmxbdoUpdate.setYnse(znsbtzzxfctzmxbdo.getYnse());
                updateList.add(czfctzmxbdoUpdate);

                //生成从价修改vo
                double ynse = cjfcyz * jsbl * FcsTzConstans.CJSL.doubleValue() / 12;
                cjfctzmxbdoUpDate.setCzmj(new BigDecimal(czmj).setScale(2, BigDecimal.ROUND_HALF_UP));
                cjfctzmxbdoUpDate.setFcyz(new BigDecimal(cjfcyz).setScale(2, BigDecimal.ROUND_HALF_UP));
                cjfctzmxbdoUpDate.setZjsr1(znsbtzzxfctzmxbdo.getZjsr1());
                cjfctzmxbdoUpDate.setYnse(new BigDecimal(ynse).setScale(2, BigDecimal.ROUND_HALF_UP));
                updateList.add(cjfctzmxbdoUpDate);
            }
        }

        if (GyUtils.isNotNull(addList)) {
            log.info("htxxToFcstz addList={}", JsonUtils.toJson(addList));
            znsbtzzxfctzmxbmapper.insertBatch(addList);
        }
        if (GyUtils.isNotNull(updateList)) {
            log.info("htxxToFcstz updateList={}", JsonUtils.toJson(updateList));
            znsbtzzxfctzmxbmapper.updateBatch(updateList);
        }
    }

    @Override
    @Transactional
    public void htxxToFcstzNew() {
        log.info("htxxToFcstzNew start");
        List<ZnsbCxsFyxxcjb> allFyxx = znsbCxsFyxxcjbMapper.selectList();
        log.info("htxxToFcstzNew allFyxx size={}", allFyxx.size());
        Map<String, List<ZnsbCxsFyxxcjb>> fyxxMap = new HashMap<>();
        allFyxx.stream().forEach(fyxx -> {
            if (GyUtils.isNotNull(fyxx.getFybh()) && "Y".equals(fyxx.getYxbz()) && !FcsUtil.isNsywzz(fyxx, new Date())) {
                String djxh = fyxx.getDjxh();
                if (GyUtils.isNull(fyxxMap.get(djxh))) {
                    List<ZnsbCxsFyxxcjb> fyList = new ArrayList<>();
                    fyList.add(fyxx);
                    fyxxMap.put(djxh, fyList);
                } else {
                    fyxxMap.get(djxh).add(fyxx);
                }
            }
        });
        List<ZnsbTzzxFctzmxbDO> delList = new ArrayList<>();

        log.info("htxxToFcstzNew fyxxMap size={}", fyxxMap.size());
        if (GyUtils.isNotNull(fyxxMap)) {
            fyxxMap.entrySet().stream().forEach(entry -> {
                String key = entry.getKey();
                List<ZnsbCxsFyxxcjb> fyList = entry.getValue();
                log.info("htxxToFcstzNew key={}", key);
                log.info("htxxToFcstzNew fyList={}", JsonUtils.toJson(fyList));
                final Map<String, Object> pzbMap = CacheUtils.getTableData("cs_znsb_ftssbpzb", key);
                List<Map<String, Object>> pzbList = null;
                if (GyUtils.isNotNull(pzbMap)) {
                    pzbList = JsonUtils.toMapList((String) pzbMap.get("dataList"));
                    pzbList = pzbList.stream().filter(vo -> "101100800".equals(vo.get("zspmDm"))).collect(Collectors.toList());
                }
                if (GyUtils.isNotNull(pzbList)) {
                    CzfReqVO czfreqvo = new CzfReqVO();
                    czfreqvo.setDjxh(key);
                    InitDataResVO initDataResVO = fctzmxbservice.initData(czfreqvo);
                    String cxrqq = DateUtil.doDateFormat(initDataResVO.getCxrqq(), "yyyy-MM-dd");
                    String cxrqz = DateUtil.doDateFormat(initDataResVO.getCxrqz(), "yyyy-MM-dd");
                    log.info("htxxToFcstz cxrqq={},cxrqz={}", cxrqq, cxrqz);
                    int jgyf = FcsUtil.getJgyf(cxrqq, cxrqz) + 1;
                    log.info("htxxToFcstz jgyf={}", jgyf);
                    for (int i = 0; i < jgyf; i++) {
                        Calendar cxrqqC = Calendar.getInstance();
                        cxrqqC.setTime(initDataResVO.getCxrqq());
                        cxrqqC.add(Calendar.MONTH, i);
                        String tbyf = DateUtil.doDateFormat(cxrqqC.getTime(), "yyyy-MM");
                        fyList.stream().forEach(fyxx -> {
                            htxxToFcstz(tbyf, fyxx.getFybh());
                        });
                    }
                    czfreqvo.setSbsjq(cxrqz);
                    List<ZnsbTzzxFctzmxbDO> fctzList = znsbtzzxfctzmxbmapper.selectZnsbTzzxFctzmxbList(czfreqvo);
                    for (ZnsbTzzxFctzmxbDO fctzmx : fctzList) {
                        if (FcsTzConstans.SBZT_WSC.equals(fctzmx.getSyzt())) {
                            fctzmx.setScbz("Y");
                            fctzmx.setXgrq(new Date());
                            fctzmx.setXgrsfid("dsrwsc");
                            delList.add(fctzmx);
                        }
                    }
                } else {
                    CzfReqVO czfreqvo = new CzfReqVO();
                    czfreqvo.setDjxh(key);
                    List<ZnsbTzzxFctzmxbDO> fctzList = znsbtzzxfctzmxbmapper.selectZnsbTzzxFctzmxbList(czfreqvo);
                    for (ZnsbTzzxFctzmxbDO fctzmx : fctzList) {
                        if (FcsTzConstans.SBZT_WSC.equals(fctzmx.getSyzt())) {
                            fctzmx.setScbz("Y");
                            fctzmx.setXgrq(new Date());
                            fctzmx.setXgrsfid("dsrwsc");
                            delList.add(fctzmx);
                        }
                    }
                }
            });
        }
//        if(GyUtils.isNotNull(delList)){
//            log.info("htxxToFcstz delList={}",JsonUtils.toJson(delList));
//            znsbtzzxfctzmxbmapper.updateBatch(delList);
//        }
        log.info("htxxToFcstzNew end");
    }

    /**
     * 根据BPM数据信息生成房产税台账
     */
    @Override
    @Transactional
    public void sapToFcstz(String scyf, String tbfybh) {
        log.info("FcsDataProcJobServiceImpl_sapToFcstz  start");
        List<ZnsbTzzxFctzmxbDO> addList = new ArrayList<>();
        List<ZnsbTzzxFctzmxbDO> updateList = new ArrayList<>();
        String jobParam = XxlJobHelper.getJobParam();
        log.info("htxxToFcstz jobParam::{}", jobParam);
        log.info("htxxToFcstz cs::{}", scyf);
        Date skssqq = this.getSkssqq(jobParam);
        Date skssqz = this.getSkssqz(jobParam);
        if (GyUtils.isNotNull(scyf)) {
            skssqq = this.getSkssqq(scyf);
            skssqz = this.getSkssqz(scyf);
        }
        log.info("htxxToFcstz skssqq={}", skssqq);
        log.info("htxxToFcstz skssqz={}", skssqz);

//        List<ZnsbTzzxFcjcxxtzbDO> fcjcxxtzbDOList = znsbTzzxFcjcxxtzbMapper.querAllFwxxList(null,null);
//        this.resetFcyz(fcjcxxtzbDOList,skssqq);

        //处理SAP数据
        List<ZnsbTzzxCzpzmxbDO> czpzmxList = znsbtzzxczpzmxbmapper.selectCzpzmxList(skssqq, skssqz);
        Map<String, List<ZnsbTzzxCzpzmxbDO>> czpzmxMap = new HashMap<>();
        //根据房源编号分组
        for (ZnsbTzzxCzpzmxbDO znsbtzzxczpzmxbdo : czpzmxList) {
            List<ZnsbTzzxCzpzmxbDO> czpzList = czpzmxMap.get(znsbtzzxczpzmxbdo.getFybh());
            if (GyUtils.isNull(czpzList)) {
                czpzList = new ArrayList<>();
                czpzList.add(znsbtzzxczpzmxbdo);
                czpzmxMap.put(znsbtzzxczpzmxbdo.getFybh(), czpzList);
            } else {
                czpzList.add(znsbtzzxczpzmxbdo);
            }
        }


        //根据房源编号按月生成房产台账信息
        Set<String> czmxKeySet = czpzmxMap.keySet();
        for (String key : czmxKeySet) {
            List<ZnsbTzzxCzpzmxbDO> czpzList = czpzmxMap.get(key);
            log.info("sapToFcstz key={}", key);
            log.info("sapToFcstz czpzList={}", JsonUtils.toJson(czpzList));
            ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdo = new ZnsbTzzxFctzmxbDO();
            znsbtzzxfctzmxbdo.setFybh(key);
            znsbtzzxfctzmxbdo.setSkssqq(skssqq);
            znsbtzzxfctzmxbdo.setSkssqz(skssqz);
            List<ZnsbTzzxFctzmxbDO> znsbtzzxfctzmxbdocList = znsbtzzxfctzmxbmapper.selecFctzmxByFybhSkssq(znsbtzzxfctzmxbdo);
            if (GyUtils.isNull(znsbtzzxfctzmxbdocList)) {
                ZnsbTzzxCzpzmxbDO znsbtzzxczpzmxbdo = czpzList.get(0);
//                List<ZnsbTzzxFcjcxxtzbDO> fcjcxxtzbDOList1 = fcjcxxtzbDOList.stream().filter(zbDo->zbDo.getFybh().equals(znsbtzzxczpzmxbdo.getFybh())).collect(Collectors.toList());
                ZnsbCxsFyxxcjb fyjcxx = znsbCxsFyxxcjbMapper.queryByFybh(key, null, true);
                if (GyUtils.isNull(fyjcxx)) {
                    continue;
                }
                CjmxVO cjmxVO = this.getCjxxVo(fyjcxx, skssqq);
                ZnsbTzzxFctzmxbDO addCzFctzmxbDo = new ZnsbTzzxFctzmxbDO();
                ZnsbTzzxFctzmxbDO addCjFctzmxbDo = new ZnsbTzzxFctzmxbDO();
                this.setDefaultValue(addCzFctzmxbDo);
                addCzFctzmxbDo.setDjxh(znsbtzzxczpzmxbdo.getDjxh());
                addCzFctzmxbDo.setNsrmc(znsbtzzxczpzmxbdo.getNsrmc());
                addCzFctzmxbDo.setNsrsbh(znsbtzzxczpzmxbdo.getNsrsbh());
                addCzFctzmxbDo.setFybh(znsbtzzxczpzmxbdo.getFybh());
                addCzFctzmxbDo.setFwcqzsh(fyjcxx.getFwcqzsh());
                addCzFctzmxbDo.setFwzldz(fyjcxx.getFwzldz());
                addCzFctzmxbDo.setFcyz(new BigDecimal(0.0));
                addCzFctzmxbDo.setCzmj(new BigDecimal(0.0));
                double zjsr = czpzList.stream().mapToDouble(cctzmxbDo -> cctzmxbDo.getJe().doubleValue()).sum();
                addCzFctzmxbDo.setZjsr1(new BigDecimal(zjsr));
                addCzFctzmxbDo.setSkssqq(skssqq);
                addCzFctzmxbDo.setSkssqz(skssqz);
                addCzFctzmxbDo.setCzbz("2");
                addCzFctzmxbDo.setXtly("SAP");
                addCzFctzmxbDo.setSl1(FcsTzConstans.CZSL);
                addCzFctzmxbDo.setZbuuid(fyjcxx.getUuid());
                BeanUtil.copyProperties(addCzFctzmxbDo, addCjFctzmxbDo, true);
                this.setDefaultValue(addCjFctzmxbDo);
                addCjFctzmxbDo.setXtly("SAP");
                addCjFctzmxbDo.setCzbz("1");
                //获取房产原值
                addCjFctzmxbDo.setFcyz(new BigDecimal(cjmxVO.getFcyz()).setScale(2, BigDecimal.ROUND_HALF_UP));
                addCjFctzmxbDo.setCzmj(new BigDecimal(0.0));
                addCjFctzmxbDo.setSl1(FcsTzConstans.CJSL);
                addList.add(addCjFctzmxbDo);
                addList.add(addCzFctzmxbDo);
            }else {
                List<ZnsbTzzxFctzmxbDO> updateCzFctzmxbDoList = znsbtzzxfctzmxbdocList.stream().filter(fctzmxbdo -> fctzmxbdo.getCzbz().equals(FcsTzConstans.CZDM)).collect(Collectors.toList());
                if(GyUtils.isNotNull(updateCzFctzmxbDoList)){
                    ZnsbTzzxFctzmxbDO updateCzFctzmxbDo = updateCzFctzmxbDoList.get(0);
                    double zjsr = czpzList.stream().mapToDouble(cctzmxbDo -> cctzmxbDo.getJe().doubleValue()).sum();
                    if(!FcsUtil.numberCompare(new BigDecimal(zjsr), updateCzFctzmxbDo.getZjsr1(),2)){
                        znsbtzzxfctzmxbdocList.stream().forEach(fctzmxbdo -> {
                            fctzmxbdo.setZjsr1(new BigDecimal(zjsr));
                        });
                        if(GyUtils.isNotNull(updateCzFctzmxbDo.getYnse())){
                            updateCzFctzmxbDo.setYnse(new BigDecimal(zjsr * 0.12).setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        if(!updateCzFctzmxbDo.getSyzt().equals(FcsTzConstans.SBZT_WSC)){
                            znsbtzzxfctzmxbdocList.stream().forEach(fctzmxbdo -> {
                                fctzmxbdo.setBgzt("Y");
                            });
                        }
                        updateList.addAll(znsbtzzxfctzmxbdocList);
                    }
                }
            }
        }
        if (GyUtils.isNotNull(addList)) {
            znsbtzzxfctzmxbmapper.insertBatch(addList);
        }
        if (GyUtils.isNotNull(updateList)) {
            znsbtzzxfctzmxbmapper.updateBatch(updateList);
        }
    }


    @Override
    @Transactional
    public void synCzpzmxb() {
        log.info("FcsDataProcJobServiceImpl_synCzpzmxb  start");
        List<ZnsbTzzxCzpzmxbZjbDO> czpzmxZjDoList = znsbtzzxczpzmxbzjbmapper.selectSynCzpzxx();
        List<ZnsbTzzxCzpzmxbZjbDO> updateZjDoList = new ArrayList<>();
        Map<String, String> kjpzbhMap = new HashMap<>();
        List<ZnsbTzzxCzpzmxbDO> insertCzpzmxList = new ArrayList<>();
        List<ZnsbTzzxCzpzmxbDO> delCzpzmxList = new ArrayList<>();

        List<ZnsbTzzxFcssyfckmfpdzbDO> fckmfpdzbList = znsbtzzxfcssyfckmfpdzbmapper.selectAllDzbxxList();
        log.info("fckmfpdzbList size:{}", fckmfpdzbList.size());
        for (ZnsbTzzxCzpzmxbZjbDO czpzmxbZjbDO : czpzmxZjDoList) {
            ZnsbTzzxCzpzmxbDO znsbtzzxczpzmxbdo = new ZnsbTzzxCzpzmxbDO();
            BeanUtil.copyProperties(czpzmxbZjbDO, znsbtzzxczpzmxbdo);
            this.setDefaultValue(znsbtzzxczpzmxbdo);
            List<ZnsbTzzxFcssyfckmfpdzbDO> znsbtzzxfcssyfckmfpdzbdoList = fckmfpdzbList.stream().filter(dzbDo -> dzbDo.getGsh1().equals(czpzmxbZjbDO.getGsh())).collect(Collectors.toList());
            if (GyUtils.isNull(znsbtzzxfcssyfckmfpdzbdoList)) {
                continue;
            }
            String fybh = znsbtzzxfcssyfckmfpdzbdoList.get(0).getSybh1();
            znsbtzzxczpzmxbdo.setFybh(fybh);
            insertCzpzmxList.add(znsbtzzxczpzmxbdo);

            String tzlxDm = czpzmxbZjbDO.getTzlxDm();
            if ("4".equals(tzlxDm)) {
                String kjpzbh = czpzmxbZjbDO.getKjpzbh();
                if (GyUtils.isNull(kjpzbhMap.get(kjpzbh))) {
                    kjpzbhMap.put(kjpzbh, kjpzbh);
                    List<ZnsbTzzxCzpzmxbDO> czpzmxList = znsbtzzxczpzmxbmapper.selectListByKjpzxh(kjpzbh);
                    czpzmxList.forEach(czpzmxDo -> {
                        ZnsbTzzxCzpzmxbDO czpzmxbdo = new ZnsbTzzxCzpzmxbDO();
                        czpzmxbdo.setUuid(czpzmxDo.getUuid());
                        czpzmxbdo.setScbz("Y");
                        czpzmxbdo.setScsj1(new Date());
                        delCzpzmxList.add(czpzmxbdo);
                    });
                }
            }
            ZnsbTzzxCzpzmxbZjbDO czpzmxbZjbUpdateDO = new ZnsbTzzxCzpzmxbZjbDO();
            czpzmxbZjbUpdateDO.setUuid(czpzmxbZjbDO.getUuid());
            czpzmxbZjbUpdateDO.setTbbz("Y");
            updateZjDoList.add(czpzmxbZjbUpdateDO);
        }

        if (GyUtils.isNotNull(insertCzpzmxList)) {
            znsbtzzxczpzmxbmapper.insertBatch(insertCzpzmxList);
        }
        if (GyUtils.isNotNull(delCzpzmxList)) {
            znsbtzzxczpzmxbmapper.updateBatch(delCzpzmxList);
        }
        if (GyUtils.isNotNull(updateZjDoList)) {
            znsbtzzxczpzmxbzjbmapper.updateBatch(updateZjDoList);
        }
    }


    @Override
    @Transactional
    public void getSapFwzcxxData(String gsh) {
        log.info("FcsDataProcJobServiceImpl_getSapFwzcxxData::gsh =={}", gsh);
        String jobParam = XxlJobHelper.getJobParam();
        log.info("FcsDataProcJobServiceImpl_getSapFwzcxxData jobParam::{}", jobParam);
        if (GyUtils.isNotNull(jobParam)) {
            gsh = jobParam;
        }
        FcszcxxReqVO fcszcxxreqvo = new FcszcxxReqVO();
        if (GyUtils.isNotNull(gsh)) {
            List<FcszcxxReqItem> reqItemList = new ArrayList<>();
            String[] gshList = gsh.split(",");
            for (String csgsh : gshList) {
                FcszcxxReqItem reqItem = new FcszcxxReqItem();
                reqItem.setGsh(csgsh);
                reqItemList.add(reqItem);
            }
            fcszcxxreqvo.setReqItemList(reqItemList);
        }
        log.info("FcsDataProcJobServiceImpl_getSapFwzcxxData::fcszcxxreqvo =={}", JsonUtils.toJson(fcszcxxreqvo));
        String resutString = sapApi.getSapFwzcxx(fcszcxxreqvo);
        log.info("FcsDataProcJobServiceImpl_getSapFwzcxxData::resutString =={}", resutString);
        FcszcxxResVO resVO = JsonUtils.toBean(resutString, FcszcxxResVO.class);
        List<ZnsbTzzxFcszcxxdzbDO> dzbList = fcszcxxdzbmapper.selectList();
        log.info("SapDataServiceImpl_getSapFwzcxxData::dzbList =={}", JsonUtils.toJson(dzbList));
        List<ZnsbTzzxFcszcxxDO> insertList = new ArrayList<>();
        List<ZnsbTzzxFcszcxxDO> updateList = new ArrayList<>();
        final Map<String, JbxxmxsjVO> qyjbxxMap = new HashMap<>();
        if (GyUtils.isNotNull(resVO) && GyUtils.isNotNull(resVO.getResItemList())) {
            List<FcszcxxResItem> resItemList = resVO.getResItemList();
            for (FcszcxxResItem resItem : resItemList) {
                resItem.setNull();
                log.info("SapDataServiceImpl_getSapFwzcxxData::resItem =={}", JsonUtils.toJson(resItem));
                ZnsbTzzxFcszcxxDO zcDo = fcszcxxmapper.selectByGshZcbh(resItem.getGsh1(), resItem.getZcbh());
                if (GyUtils.isNull(zcDo)) {
                    log.info("SapDataServiceImpl_getSapFwzcxxData::zcDo is null");
                    zcDo = new ZnsbTzzxFcszcxxDO();
                    JbxxmxsjVO jbxxVO = qyjbxxMap.get(resItem.getGsh1());
                    //通过公司号获取用户信息
                    if (GyUtils.isNull(jbxxVO)) {
                        jbxxVO = this.getNsrxxByGsh(resItem.getGsh1());
                        if (GyUtils.isNull(jbxxVO)) {
                            continue;
                        } else {
                            qyjbxxMap.put(resItem.getGsh1(), jbxxVO);
                        }
                    }
                    List<ZnsbTzzxFcszcxxdzbDO> dzbxxList = dzbList.stream().filter(o -> (resItem.getGsh1().equals(o.getGsh1()) && resItem.getZcbh().equals(o.getZcbh()))).collect(Collectors.toList());
                    BeanUtil.copyProperties(resItem, zcDo);
                    if (GyUtils.isNotNull(dzbxxList)) {
                        ZnsbTzzxFcszcxxdzbDO dzbDo = dzbxxList.get(0);
                        zcDo.setFybh(dzbDo.getFybh());
                        zcDo.setBdcqzsh(dzbDo.getFwcqzsh());
                        zcDo.setClzt(FcsTzConstans.FPZT_YFP);
                    } else {
                        zcDo.setClzt(FcsTzConstans.FPZT_WQR);
                    }
                    zcDo.setDjxh(new BigDecimal(jbxxVO.getDjxh()));
                    zcDo.setNsrmc(jbxxVO.getNsrmc());
                    zcDo.setNsrsbh(jbxxVO.getNsrsbh());
                    zcDo.setDefaultValue();
                    insertList.add(zcDo);
                } else {
                    log.info("SapDataServiceImpl_getSapFwzcxxData::zcDo is not null");
                    if (zcDo.isChange(resItem)) {
                        BeanUtil.copyProperties(resItem, zcDo);
                    }
                    if (GyUtils.isNull(zcDo.getFybh())) {
                        List<ZnsbTzzxFcszcxxdzbDO> dzbxxList = dzbList.stream().filter(o -> (resItem.getGsh1().equals(o.getGsh1()) && resItem.getZcbh().equals(o.getZcbh()))).collect(Collectors.toList());
                        if (GyUtils.isNotNull(dzbxxList)) {
                            ZnsbTzzxFcszcxxdzbDO dzbDo = dzbxxList.get(0);
                            zcDo.setFybh(dzbDo.getFybh());
                            zcDo.setBdcqzsh(dzbDo.getFwcqzsh());
                            zcDo.setClzt(FcsTzConstans.FPZT_YFP);
                        }
                    }
                    updateList.add(zcDo);
                }
            }
        }
        if (GyUtils.isNotNull(insertList)) {
            fcszcxxmapper.insertBatch(insertList);
            log.info("FcsDataProcJobServiceImpl_getSapFwzcxxData::insertList =={}", JsonUtils.toJson(insertList));
        }
        if (GyUtils.isNotNull(updateList)) {
            fcszcxxmapper.updateBatch(updateList);
            log.info("FcsDataProcJobServiceImpl_getSapFwzcxxData::updateList =={}", JsonUtils.toJson(updateList));
        }
    }


    private JbxxmxsjVO getNsrxxByGsh(String gsh) {
        JbxxmxsjVO jbxxmxsjvo = null;
        ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setQydmz(gsh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByQydmz(znsbMhzcQyjbxxmxReqVO);
        if (GyUtils.isNull(nsrxxByNsrsbh) || GyUtils.isNull(nsrxxByNsrsbh.getData()) || GyUtils.isNull(nsrxxByNsrsbh.getData().getJbxxmxsj())) {
            return null;
        }
        for (JbxxmxsjVO jbxxUnit : nsrxxByNsrsbh.getData().getJbxxmxsj()) {
            final String kqccsztdjbz = jbxxUnit.getKqccsztdjbz();
            final String kzztdjlxDm = jbxxUnit.getKzztdjlxDm();
            final String nsrztDm = jbxxUnit.getNsrztDm();

            if ("N".equals(kqccsztdjbz)
                    && ("1110".equals(kzztdjlxDm) || "1120".equals(kzztdjlxDm))
                    && "03".equals(nsrztDm)) {
                jbxxmxsjvo = jbxxUnit;
            }
        }
        return jbxxmxsjvo;
    }


    /**
     * 合同台账历史数据处理
     */
    @Override
    @Transactional
    public void lssjcl() {
        List<ZnsbTzzxHttzxxDO> htlist = znsbtzzxhttzxxmapper.selectHtxxListByHtbh(null, null);
        List<ZnsbTzzxHttzxxDO> deletehtlist = new ArrayList<>();
        Map<String, ZnsbTzzxHttzxxDO> gjMap = new HashMap<>();
        if (GyUtils.isNotNull(htlist)) {
            htlist.forEach(htxx -> {
                String key = htxx.getHtbh() + "_" + htxx.getFybh();
                if (GyUtils.isNull(htxx.getHsyzjsr())) {
                    double hsyzjsr = htxx.getYzjsr().doubleValue() * (1 + htxx.getSl1().doubleValue());
                    htxx.setHsyzjsr(new BigDecimal(hsyzjsr).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                if (GyUtils.isNull(htxx.getLy())) {
                    htxx.setLy(FcsTzConstans.LY_DSRW);
                }
                ZnsbTzzxHttzxxDO htDo = gjMap.get(key);
                if (GyUtils.isNull(htDo)) {
                    gjMap.put(key, htxx);
                } else {
                    ZnsbTzzxHttzxxDO deleteDo = new ZnsbTzzxHttzxxDO();
                    deleteDo.setUuid(htxx.getUuid());
                    deleteDo.setScbz("Y");
                    deletehtlist.add(deleteDo);
                    htxx.setUuid(htDo.getUuid());
                }
            });
        }
        List<ZnsbTzzxHttzxxMxDO> htmxList = this.getHtmxList(htlist);
        znsbtzzxhttzxxmapper.updateBatch(deletehtlist);
        znsbtzzxhttzxxmxmapper.insertBatch(htmxList);
    }

    //根据合同主表获取合同明细表数据
    @Override
    public List<ZnsbTzzxHttzxxMxDO> getHtmxList(List<ZnsbTzzxHttzxxDO> htlist) {
        List<ZnsbTzzxHttzxxMxDO> htmxList = new ArrayList<>();
        for (ZnsbTzzxHttzxxDO znsbtzzxhttzxxdo : htlist) {
            String uuid = znsbtzzxhttzxxdo.getUuid();
            Date zlrqq = znsbtzzxhttzxxdo.getZlrqq();
            Date zlrqz = znsbtzzxhttzxxdo.getZlrqz();
            if (GyUtils.isNotNull(znsbtzzxhttzxxdo.getZzrq()) && zlrqz.after(znsbtzzxhttzxxdo.getZzrq())) {
                zlrqz = znsbtzzxhttzxxdo.getZzrq();
            }
            int jgyf = FcsUtil.getJgyf(DateUtil.doDateFormat(zlrqq, "yyyy-MM-dd"), DateUtil.doDateFormat(zlrqz, "yyyy-MM-dd")) + 1;

            Calendar startDate = Calendar.getInstance();
            startDate.setTime(zlrqq);
            for (int i = 0; i < jgyf; i++) {
                ZnsbTzzxHttzxxMxDO znsbtzzxhttzxxmxdo = new ZnsbTzzxHttzxxMxDO();
                znsbtzzxhttzxxmxdo.setDefaultValue();
                znsbtzzxhttzxxmxdo.setZbuuid(uuid);
                Calendar htzlrqq = Calendar.getInstance();
                Calendar htzlrqz = Calendar.getInstance();
                htzlrqq.setTime(startDate.getTime());
                htzlrqq.set(Calendar.DAY_OF_MONTH, 1);
                htzlrqz.setTime(startDate.getTime());
                htzlrqz.set(Calendar.DAY_OF_MONTH, htzlrqz.getActualMaximum(Calendar.DAY_OF_MONTH));
                znsbtzzxhttzxxmxdo.setZlrqq(htzlrqq.getTime());
                znsbtzzxhttzxxmxdo.setZlrqz(htzlrqz.getTime());
                znsbtzzxhttzxxmxdo.setCzmj(znsbtzzxhttzxxdo.getCzmj());
                Integer sszq = Integer.parseInt(DateUtil.doDateFormat(startDate.getTime(), "yyyyMM"));
                znsbtzzxhttzxxmxdo.setSszq(sszq);
                znsbtzzxhttzxxmxdo.setHsyzjsr(znsbtzzxhttzxxdo.getHsyzjsr());
                znsbtzzxhttzxxmxdo.setYzjsr(znsbtzzxhttzxxdo.getYzjsr());
                znsbtzzxhttzxxmxdo.setSl1(znsbtzzxhttzxxdo.getSl1());
                znsbtzzxhttzxxmxdo.setSjczmj(znsbtzzxhttzxxdo.getSjczmj());
                znsbtzzxhttzxxmxdo.setSjhsyzjsr(znsbtzzxhttzxxdo.getSjhsyzjsr());
                znsbtzzxhttzxxmxdo.setSjyzjsr(znsbtzzxhttzxxdo.getSjyzjsr());
                znsbtzzxhttzxxmxdo.setSjsl1(znsbtzzxhttzxxdo.getSjsl1());
                znsbtzzxhttzxxmxdo.setLy(znsbtzzxhttzxxdo.getLy());
                znsbtzzxhttzxxmxdo.setSjsj();
                htmxList.add(znsbtzzxhttzxxmxdo);
                startDate.add(Calendar.MONTH, 1);
            }
        }
        return htmxList;
    }

    @Override
    public CjmxVO getCjxxVo(ZnsbCxsFyxxcjb fyjcxx, Date ssq) {
        log.info("FcsDataProcJobServiceImpl_getCjxxVo::fyjcxx =={}", JsonUtils.toJson(fyjcxx));
        CjmxVO cjmxvo = null;
        FcsYsxxVO fcsysxxvo = fcsservice.queryFcsYsxxbyuuid(fyjcxx.getUuid(), fyjcxx.getZgswskfjDm());
        if (GyUtils.isNotNull(fcsysxxvo.getFcsCjYsxxVO())) {
            List<CjmxVO> cjList = fcsysxxvo.getFcsCjYsxxVO().getCjmxVOList();
            cjmxvo = FcsUtil.getCjVo(cjList, ssq);
        }
        if (GyUtils.isNull(cjmxvo)) {
            cjmxvo = new CjmxVO();
            cjmxvo.setFcyz(0.0);
            cjmxvo.setJsbl(0.0);
        }
        return cjmxvo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getStandardContract(String tbgsh) {
        log.info("转换标准合同开始，tbgsh：{}", tbgsh);
        List<ZnsbTzzxFcshtxxBpmDO> bpmDoList = znsbtzzxfcshtxxbpmmapper.selectAllUpdateData(tbgsh);
        log.info("需要转换标准合同数量：{}", bpmDoList.size());
        List<ZnsbTzzxFcshtxxBpmDO> updateBpmDoList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(bpmDoList)) {
            // 定义需要处理的list
            List<String> deleteUuidZlxxList = new ArrayList<>();
            List<ZnsbTzzxFcshtxxBzhtDO> insertBzhtList = new ArrayList<>();
            List<ZnsbTzzxFcshtxxBzhtZlxxDO> insertBzhtZlxxList = new ArrayList<>();
            List<String> zbuuidList = bpmDoList.stream().map(ZnsbTzzxFcshtxxBpmDO::getUuid).collect(Collectors.toList());
            List<String> htbhList = bpmDoList.stream().map(ZnsbTzzxFcshtxxBpmDO::getHtbh).collect(Collectors.toList());
            // 查询BPM合同的出租房源信息
            List<ZnsbTzzxFcshtxxBpmSpzbDO> spzbQueryList = znsbtzzxfcshtxxbpmspzbmapper.selectFcshtxxBpmListByZbuuidList(zbuuidList);
            // 查询BPM合同出租租赁信息
            List<ZnsbTzzxFcshtxxBpmZlxxDO> zlxxQueryList = znsbtzzxfcshtxxbpmzlxxmapper.selectFcshtxxBpmListByZbuuidList(zbuuidList);
            // 查询标准合同
            List<ZnsbTzzxFcshtxxBzhtDO> bzhtList = znsbTzzxFcshtxxBzhtMapper.queryBzhtByHtbh(htbhList);
            List<String> bzhtUuidList = bzhtList.stream().map(ZnsbTzzxFcshtxxBzhtDO::getUuid).collect(Collectors.toList());
            // 查询标准合同租赁信息
            List<ZnsbTzzxFcshtxxBzhtZlxxDO> bzhtZlxxList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(bzhtUuidList)) {
                bzhtZlxxList = znsbTzzxFcshtxxBzhtZlxxMapper.queryBzhtZlxxByZbuuid(htbhList);
            }
            // 组装map
            Map<String, List<ZnsbTzzxFcshtxxBpmSpzbDO>> spzbMap = spzbQueryList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcshtxxBpmSpzbDO::getZbuuid));
            Map<String, List<ZnsbTzzxFcshtxxBpmZlxxDO>> zlxxBpmMap = zlxxQueryList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcshtxxBpmZlxxDO::getZbuuid));
            Map<String, List<ZnsbTzzxFcshtxxBzhtDO>> bzhtMap = bzhtList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcshtxxBzhtDO::getHtbh));
            Map<String, List<ZnsbTzzxFcshtxxBzhtZlxxDO>> bzhtZlxxMap = bzhtZlxxList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcshtxxBzhtZlxxDO::getZbuuid));
            for (ZnsbTzzxFcshtxxBpmDO bpmDo : bpmDoList) {
                String htbh = bpmDo.getHtbh();
                String htmc = bpmDo.getHtmc();
                String zbuuid = bpmDo.getUuid();
                String gsh = bpmDo.getGsh2();
                log.info("标准合同数据转换，合同信息转换，合同编号：{}", htbh);
                log.info("标准合同数据转换，合同信息转换，公司号：{}", gsh);

                try {
                    //查询合同的出租房源信息
                    List<ZnsbTzzxFcshtxxBpmSpzbDO> spzbList = spzbMap.get(zbuuid);
                    //查询合同出租租赁信息
                    List<ZnsbTzzxFcshtxxBpmZlxxDO> zlxxList = zlxxBpmMap.get(zbuuid);
                    //查询合同的出租房源信息，每条房源信息会对应多条出租信息
                    for (ZnsbTzzxFcshtxxBpmSpzbDO znsbtzzxfcshtxxbpmspzbdo : spzbList) {
                        List<String> ycyyList = new ArrayList<>();
                        ZnsbTzzxFcshtxxBzhtDO bzhtDO = new ZnsbTzzxFcshtxxBzhtDO();
                        if (bzhtMap.containsKey(htbh)) {
                            bzhtDO = bzhtMap.get(htbh).get(0);
                        } else {
                            bzhtDO.setUuid(GyUtils.getUuid());
                        }
                        bzhtDO.setZbuuid(zbuuid);
                        bzhtDO.setHtbh(htbh);
                        bzhtDO.setHtmc(htmc);
                        bzhtDO.setHtqdrq(bpmDo.getHtqdrq());
                        bzhtDO.setHtydsxrq(bpmDo.getHtydsxrq());
                        bzhtDO.setHtydzzrq(bpmDo.getHtydzzrq());
                        bzhtDO.setZzrq(bpmDo.getZzrq());
                        bzhtDO.setGsh(gsh);
                        bzhtDO.setGsmc(bpmDo.getGsmc());
                        bzhtDO.setLrzx(bpmDo.getLrzx());
                        bzhtDO.setSkfs(bpmDo.getSkfs());

                        String zlxxZbuuid = bzhtDO.getUuid();
                        String fybh = "";
                        String fwcqzsh = "";
                        String dz = "";
                        String htycbz = FcsConstants.FCS_BZHT_NO;
                        String spmc = znsbtzzxfcshtxxbpmspzbdo.getSpmc1();
                        BigDecimal czmj = znsbtzzxfcshtxxbpmspzbdo.getCzmj();
                        //根据合同编号同步合同信息
                        if (GyUtils.isNull(htbh)) {
                            // 合同编号为空 记录不合规合同
                            htycbz = FcsConstants.FCS_BZHT_YES;
                            this.checkYcyy(FcsConstants.FCS_BZHT_ERROR_NO_CONTRACT_CODE, ycyyList);
                        }
                        if (GyUtils.isNull(spmc)) {
                            // 无商铺信息 记录异常合同
                            htycbz = FcsConstants.FCS_BZHT_YES;
                            this.checkYcyy(FcsConstants.FCS_BZHT_ERROR_NO_HOUSE, ycyyList);
                        }
                        if (czmj == null || czmj.compareTo(BigDecimal.ZERO) <= 0) {
                            // 出租面积小于0或者为0或者为空 记录异常合同
                            htycbz = FcsConstants.FCS_BZHT_YES;
                            this.checkYcyy(FcsConstants.FCS_BZHT_ERROR_SQUARE_IS_INVALID, ycyyList);
                        }
                        bzhtDO.setHtqdrq(bpmDo.getHtqdrq());
                        bzhtDO.setHtydsxrq(bpmDo.getHtydsxrq());
                        bzhtDO.setHtydzzrq(bpmDo.getHtydzzrq());
                        bzhtDO.setZzrq(bpmDo.getZzrq());
                        List<ZnsbTzzxFcshtxxBzhtZlxxDO> zlxxDOList = bzhtZlxxMap.get(zlxxZbuuid);
                        if (!CollectionUtils.isEmpty(zlxxDOList)) {
                            deleteUuidZlxxList.addAll(zlxxDOList.stream().map(ZnsbTzzxFcshtxxBzhtZlxxDO::getUuid).collect(Collectors.toList()));
                        }
                        ZnsbCxsFyxxcjb fyjcxx = znsbtzzxfcjcxxtzbservice.queryFyxxByFwcqzsh(spmc);
                        if (GyUtils.isNotNull(fyjcxx)) {
                            fybh = fyjcxx.getFybh();
                            fwcqzsh = fyjcxx.getFwcqzsh();
                            dz = fyjcxx.getFwzldz();
                        } else {
                            // 无法关联房源 记录异常合同
                            htycbz = FcsConstants.FCS_BZHT_YES;
                            this.checkYcyy(FcsConstants.FCS_BZHT_ERROR_NO_MATCH_HOUSE, ycyyList);
                        }
                        // 校验租赁期间
                        String check = this.checkZlqj(zlxxList);
                        if (!StringUtils.isEmpty(check)) {
                            htycbz = FcsConstants.FCS_BZHT_YES;
                            this.checkYcyy(check, ycyyList);
                        }
                        bzhtDO.setFybh(fybh);
                        bzhtDO.setBdcqzsh(fwcqzsh);
                        bzhtDO.setSpmc1(spmc);
                        bzhtDO.setDz(dz);
                        bzhtDO.setCzmj(czmj);
                        bzhtDO.setHtzt(FcsConstants.FCS_BZHT_HTZT_NORMAL);
                        bzhtDO.setYcbz(htycbz);
                        bzhtDO.setYcyy(this.concatString(ycyyList));
                        bzhtDO.setSjjgzt(FcsConstants.FCS_BZHT_NO);
                        this.setDefaultValue(bzhtDO);
                        insertBzhtList.add(bzhtDO);
                    }
                    for (ZnsbTzzxFcshtxxBpmZlxxDO znsbtzzxfcshtxxbpmzlxxdo : zlxxList) {
                        String zlxxYcbz = FcsConstants.FCS_BZHT_NO;
                        List<String> zlxxYcyyList = new ArrayList<>();
                        // 检查租赁时间段
                        if (!this.checkZlrq(znsbtzzxfcshtxxbpmzlxxdo.getZlrqq(), znsbtzzxfcshtxxbpmzlxxdo.getZlrqz())) {
                            zlxxYcbz = FcsConstants.FCS_BZHT_YES;
                            this.checkYcyy(FcsConstants.FCS_BZHT_ERROR_RENT_TIME_INVALID, zlxxYcyyList);
                        }
                        // 校验租金合法
                        if (znsbtzzxfcshtxxbpmzlxxdo.getCzzje() == null || znsbtzzxfcshtxxbpmzlxxdo.getCzzje().compareTo(BigDecimal.ZERO) <= 0) {
                            zlxxYcbz = FcsConstants.FCS_BZHT_YES;
                            this.checkYcyy(FcsConstants.FCS_BZHT_ERROR_RENT_IS_INVALID, zlxxYcyyList);
                        }
                        ZnsbTzzxFcshtxxBzhtZlxxDO zlxxDO = new ZnsbTzzxFcshtxxBzhtZlxxDO();
                        zlxxDO.setUuid(GyUtils.getUuid());
                        zlxxDO.setZbuuid(zbuuid);
                        zlxxDO.setDm(znsbtzzxfcshtxxbpmzlxxdo.getDm());
                        zlxxDO.setZlrqq(znsbtzzxfcshtxxbpmzlxxdo.getZlrqq());
                        zlxxDO.setZlrqz(znsbtzzxfcshtxxbpmzlxxdo.getZlrqz());
                        zlxxDO.setCzzje(znsbtzzxfcshtxxbpmzlxxdo.getCzzje());
                        zlxxDO.setTs(znsbtzzxfcshtxxbpmzlxxdo.getTs());
                        zlxxDO.setZlys(znsbtzzxfcshtxxbpmzlxxdo.getZlys());
                        zlxxDO.setSllxmc(znsbtzzxfcshtxxbpmzlxxdo.getSllxmc());
                        zlxxDO.setSjjgztDm(FcsConstants.FCS_BZHT_NO);
                        zlxxDO.setYcbz(zlxxYcbz);
                        zlxxDO.setYcyy(this.concatString(zlxxYcyyList));
                        this.setDefaultValue(zlxxDO);
                        insertBzhtZlxxList.add(zlxxDO);
                    }
                    ZnsbTzzxFcshtxxBpmDO updateDO = new ZnsbTzzxFcshtxxBpmDO();
                    updateDO.setUuid(bpmDo.getUuid());
                    updateDO.setGzbz("N");
                    updateBpmDoList.add(updateDO);
                } catch (Exception e) {
                    log.error(htbh, e);
                }
            }
            try {
                // 更新原合同状态
                znsbtzzxfcshtxxbpmmapper.updateBatch(updateBpmDoList);
                // 更新标准合同
                znsbTzzxFcshtxxBzhtMapper.insertOrUpdateBatch(insertBzhtList);
                // 删除旧合同租赁信息
                if (!deleteUuidZlxxList.isEmpty()) {
                    znsbTzzxFcshtxxBzhtZlxxMapper.deleteBatchIds(deleteUuidZlxxList);
                }
                // 新增新合同租赁信息
                znsbTzzxFcshtxxBzhtZlxxMapper.insertBatch(insertBzhtZlxxList);
            } catch (Exception e) {
                log.error("标准合同数据转换失败，失败原因：{}", e.getMessage());
            }
            log.info("标准合同数据转换完成,转换了：{}条合同。", insertBzhtList.size());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processingContract(String gsh) {
        // 用于存储需要新增、更新、删除的记录
        List<ZnsbTzzxFcshtxxZlxxjgDO> insertList = new ArrayList<>();
        List<ZnsbTzzxFcshtxxZlxxjgDO> updateList = new ArrayList<>();
        List<ZnsbTzzxFcshtxxBzhtZlxxDO> updateZlxxList = new ArrayList<>();
        List<ZnsbTzzxFcshtxxBzhtDO> updateBzhtList = new ArrayList<>();

        // 查询要加工的数据
        List<ZnsbTzzxFcshtxxBzhtDO> bzhtList = znsbTzzxFcshtxxBzhtMapper.queryProcessingDataByGsh(gsh);
        if (!CollectionUtils.isEmpty(bzhtList)) {
            List<String> zbuuidList = bzhtList.stream().map(ZnsbTzzxFcshtxxBzhtDO::getZbuuid).collect(Collectors.toList());
            List<String> uuidList = bzhtList.stream().map(ZnsbTzzxFcshtxxBzhtDO::getUuid).collect(Collectors.toList());
            // 查询需要加工的数据
            List<ZnsbTzzxFcshtxxBzhtZlxxDO> zlxxQureyList = znsbTzzxFcshtxxBzhtZlxxMapper.queryProcessingDataByZbuuid(zbuuidList);
            // 根据zbuuid查询加工好的数据
            List<ZnsbTzzxFcshtxxZlxxjgDO> zlxxjgQureyList = znsbTzzxFcshtxxZlxxjgMapper.queryZlxxjgByZbuuid(zbuuidList);
            // 按照zbuuid分组
            Map<String, List<ZnsbTzzxFcshtxxBzhtDO>> bzhtGroupMap = bzhtList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcshtxxBzhtDO::getZbuuid));
            Map<String, List<ZnsbTzzxFcshtxxBzhtZlxxDO>> zlxxGroupMap = zlxxQureyList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcshtxxBzhtZlxxDO::getZbuuid));
            Map<String, List<ZnsbTzzxFcshtxxZlxxjgDO>> zlxxjgGroupMap = zlxxjgQureyList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcshtxxZlxxjgDO::getZbuuid));
            // 税率
            List<Map<String, Object>> slMapList = CacheUtils.getTableData("cs_tzzx_smsldzb");
            //查询合同的出租总面积
            List<Map<String, Object>> mjblList = znsbTzzxFcshtxxBzhtMapper.queryMjblByUuidList(uuidList);
            Map<String, BigDecimal> mjblMap = new HashMap<>();
            for (Map<String, Object> row : mjblList) {
                String uuid = (String) row.get("uuid");
                BigDecimal mjbl = (BigDecimal) row.get("mjbl");
                mjblMap.put(uuid, mjbl);
            }
            for (Map.Entry<String, List<ZnsbTzzxFcshtxxBzhtDO>> bzhtEntry : bzhtGroupMap.entrySet()) {
                String zbuuid = bzhtEntry.getKey();
                List<ZnsbTzzxFcshtxxBzhtDO> bzhtMapList = bzhtEntry.getValue();
                List<ZnsbTzzxFcshtxxBzhtZlxxDO> zlxxList = zlxxGroupMap.containsKey(zbuuid) ? zlxxGroupMap.get(zbuuid) : new ArrayList<>();
                List<ZnsbTzzxFcshtxxZlxxjgDO> zlxxjgList = zlxxjgGroupMap.containsKey(zbuuid) ? zlxxjgGroupMap.get(zbuuid) : new ArrayList<>();
                List<ZnsbTzzxFcshtxxZlxxjgDO> expectedPeriods = new ArrayList<>();
                for (ZnsbTzzxFcshtxxBzhtDO bzht : bzhtMapList) {
                    ZnsbTzzxFcshtxxBzhtDO bzhtDO = new ZnsbTzzxFcshtxxBzhtDO();
                    String uuid = bzht.getUuid();
                    boolean isUpdateBzht = false;
                    // 出租面积比例
                    double mjbl = mjblMap.containsKey(uuid) ? mjblMap.get(uuid).doubleValue() : 0;
                    // 生成zlxxList中应该包含的所有月份区间
                    zlxxList = zlxxList.isEmpty() ? zlxxList : zlxxList.stream().sorted(Comparator.comparing(ZnsbTzzxFcshtxxBzhtZlxxDO::getZlrqq)).collect(Collectors.toList());
                    for (ZnsbTzzxFcshtxxBzhtZlxxDO zlxx : zlxxList) {
                        boolean isBreak = false;
                        Date startDate = zlxx.getZlrqq();
                        Date endDate = zlxx.getZlrqz();

                        Calendar cal = Calendar.getInstance();
                        cal.setTime(startDate);

                        // 计算月租金
                        double czzje = zlxx.getCzzje().doubleValue();
                        int czys = zlxx.getZlys();
                        double hsczjemy = czzje / czys;
                        //按照房源面积比例计算出租金额
                        String sm = zlxx.getDm();
                        List<Map<String, Object>> list = slMapList.stream().filter(slMap -> sm.equals(slMap.get("sm4"))).collect(Collectors.toList());
                        BigDecimal sl1 = new BigDecimal("0.0");
                        if (GyUtils.isNotNull(list)) {
                            sl1 = (BigDecimal) list.get(0).get("sl1");
                        }
                        bzhtDO.setDm(sm);
                        bzhtDO.setSl1(sl1);
                        log.info("标准合同数据转换，租赁信息加工，税目:{}", sm);
                        log.info("标准合同数据转换，租赁信息加工，税率:{}", sl1);
                        double bhsczjemy = hsczjemy * mjbl / (1 + sl1.doubleValue());
                        double cZhsczjemy = bhsczjemy * (1 + sl1.doubleValue());

                        // 按月生成区间
                        Calendar periodStart = (Calendar) cal.clone();
                        // 合同终止日期
                        Calendar zlrqzCal = Calendar.getInstance();
                        if (GyUtils.isNotNull(bzht.getZzrq())) {
                            zlrqzCal.setTime(bzht.getZzrq());
                        }
                        while (!periodStart.getTime().after(endDate)) {
                            // 如果租赁日期起对应年月大于终止日期对应年月 跳出循环
                            if (GyUtils.isNotNull(bzht.getZzrq())) {
                                if (zlrqzCal.get(Calendar.YEAR) == periodStart.get(Calendar.YEAR) &&
                                        periodStart.get(Calendar.MONTH) > zlrqzCal.get(Calendar.MONTH)) {
                                    isBreak = true;
                                    break;
                                }
                            }
                            Calendar periodEnd = (Calendar) periodStart.clone();
                            periodEnd.set(Calendar.DAY_OF_MONTH, periodEnd.getActualMaximum(Calendar.DAY_OF_MONTH));

                            // 如果月份结束日期超过租赁结束日期，则以租赁结束日期为准
                            if (periodEnd.getTime().after(endDate)) {
                                periodEnd.setTime(endDate);
                            }

                            // 检查该月是否已经有记录，如果有则合并数据
                            ZnsbTzzxFcshtxxZlxxjgDO existingJgDO = null;
                            String monthKey = DateUtil.doDateFormat(periodStart.getTime(), "yyyy-MM");
                            for (ZnsbTzzxFcshtxxZlxxjgDO jg : expectedPeriods) {
                                String jgMonthKey = DateUtil.doDateFormat(jg.getZlrqq(), "yyyy-MM");
                                if (monthKey.equals(jgMonthKey) &&
                                        Objects.equals(bzht.getZbuuid(), jg.getZbuuid()) &&
                                        Objects.equals(bzht.getSpmc1(), jg.getIndexColumns())) {
                                    existingJgDO = jg;
                                    break;
                                }
                            }
                            if (existingJgDO != null) {
                                // 合并数据
                                if (zlxx.getCzzje() != null) {
                                    BigDecimal existingCzzje = existingJgDO.getCzzje() != null ? existingJgDO.getCzzje() : BigDecimal.ZERO;
                                    existingJgDO.setCzzje(existingCzzje.add(zlxx.getCzzje()));
                                }
                                existingJgDO.setBhsyzjsr(existingJgDO.getBhsyzjsr().add(new BigDecimal(bhsczjemy).setScale(6, RoundingMode.HALF_UP)));
                                existingJgDO.setHsyzjsr(existingJgDO.getHsyzjsr().add(new BigDecimal(cZhsczjemy).setScale(6, RoundingMode.HALF_UP)));

                                // 更新时间范围为整个合并后的时间范围
                                if (periodStart.getTime().before(existingJgDO.getZlrqq())) {
                                    existingJgDO.setZlrqq(periodStart.getTime());
                                }
                                if (periodEnd.getTime().after(existingJgDO.getZlrqz())) {
                                    existingJgDO.setZlrqz(periodEnd.getTime());
                                }

                                // 更新实际面积和租金
                                existingJgDO.setSjczmj(existingJgDO.getCzmj());
                                existingJgDO.setSjbhsyzjsr(existingJgDO.getBhsyzjsr());
                                existingJgDO.setSjhsyzjsr(existingJgDO.getHsyzjsr());
                                isUpdateBzht = true;
                            } else {
                                // 创建对应的jgDO对象
                                ZnsbTzzxFcshtxxZlxxjgDO jgDO = new ZnsbTzzxFcshtxxZlxxjgDO();
                                jgDO.setUuid(GyUtils.getUuid());
                                jgDO.setZbuuid(bzht.getUuid());
                                jgDO.setZlrqq(periodStart.getTime());
                                jgDO.setZlrqz(periodEnd.getTime());
                                jgDO.setCzmj(bzht.getCzmj());
                                jgDO.setCzzje(zlxx.getCzzje());
                                jgDO.setSjczmj(bzht.getCzmj());
                                jgDO.setIndexColumns(bzht.getSpmc1());
                                jgDO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
                                jgDO.setBhsyzjsr(new BigDecimal(bhsczjemy).setScale(6, RoundingMode.HALF_UP));
                                jgDO.setHsyzjsr(new BigDecimal(cZhsczjemy).setScale(6, RoundingMode.HALF_UP));
                                jgDO.setSjhsyzjsr(jgDO.getHsyzjsr());
                                jgDO.setSjbhsyzjsr(jgDO.getBhsyzjsr());
                                this.setDefaultValue(jgDO);
                                expectedPeriods.add(jgDO);
                                isUpdateBzht = true;
                            }

                            // 移动到下个月
                            periodStart.add(Calendar.MONTH, 1);
                            periodStart.set(Calendar.DAY_OF_MONTH, 1);
                        }
                        if (isBreak) {
                            break;
                        }
                        if (isUpdateBzht) {
                            ZnsbTzzxFcshtxxBzhtZlxxDO updateZlxxDO = new ZnsbTzzxFcshtxxBzhtZlxxDO();
                            updateZlxxDO.setUuid(zlxx.getUuid());
                            updateZlxxDO.setSjjgztDm(FcsConstants.FCS_BZHT_YES);
                            updateZlxxList.add(updateZlxxDO);
                        }
                    }
                    bzhtDO.setUuid(bzht.getUuid());
                    bzhtDO.setSjjgzt(FcsConstants.FCS_BZHT_YES);
                    updateBzhtList.add(bzhtDO);
                }

                // 将zlxxjgList转为map便于查找，key为年月
                Map<String, ZnsbTzzxFcshtxxZlxxjgDO> existingPeriodsMap = new HashMap<>();
                if (zlxxjgList != null) {
                    for (ZnsbTzzxFcshtxxZlxxjgDO jg : zlxxjgList) {
                        String key = zbuuid + "_" + jg.getIndexColumns() + "_" + DateUtil.doDateFormat(jg.getZlrqq(), "yyyy-MM") + jg.getZlrqz();
                        existingPeriodsMap.put(key, jg);
                    }
                }

                // 将期望的periods也转为map，key为年月
                Map<String, ZnsbTzzxFcshtxxZlxxjgDO> expectedPeriodsMap = new HashMap<>();
                for (ZnsbTzzxFcshtxxZlxxjgDO jg : expectedPeriods) {
                    String key = zbuuid + "_" + jg.getIndexColumns() + "_" + DateUtil.doDateFormat(jg.getZlrqq(), "yyyy-MM") + jg.getZlrqz();
                    expectedPeriodsMap.put(key, jg);
                }

                // 检查哪些需要新增或更新
                for (Map.Entry<String, ZnsbTzzxFcshtxxZlxxjgDO> entry : expectedPeriodsMap.entrySet()) {
                    String key = entry.getKey();
                    ZnsbTzzxFcshtxxZlxxjgDO expectedJg = entry.getValue();

                    if (existingPeriodsMap.containsKey(key)) {
                        // 需要更新
                        ZnsbTzzxFcshtxxZlxxjgDO existingJg = existingPeriodsMap.get(key);
                        // 判断是否没有变化 如果没变化不更新
                        if (this.isJgFieldsEqual(expectedJg, existingJg)) {
                            existingPeriodsMap.remove(key);
                            continue;
                        }
                        // 做冲减处理，原数据调账类型更改为2 生成一条负数的冲减掉原来的 再生成一条新的
                        // 生成一条新的
                        expectedJg.setTzlxDm(TzlxDmEnum.XTZ.getTzlxDm());
                        insertList.add(expectedJg);

                        // 修改原来的为被调账
                        existingJg.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
                        existingJg.setXgrq(new Date());
                        updateList.add(existingJg);

                        // 生成调账后用于冲减的台账
                        ZnsbTzzxFcshtxxZlxxjgDO dto = new ZnsbTzzxFcshtxxZlxxjgDO();
                        BeanUtils.copyBean(existingJg, dto);
                        dto.setUuid(GyUtils.getUuid());
                        dto.setTzlxDm(TzlxDmEnum.TZHCJ.getTzlxDm());
                        this.setDefaultValue(dto);
                        insertList.add(dto);
                        existingPeriodsMap.remove(key);
                    } else {
                        // 需要新增
                        insertList.add(expectedJg);
                    }
                }

                // 检查哪些需要删除
                for (Map.Entry<String, ZnsbTzzxFcshtxxZlxxjgDO> entry : existingPeriodsMap.entrySet()) {
                    ZnsbTzzxFcshtxxZlxxjgDO existingJg = entry.getValue();

                    existingJg.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
                    existingJg.setXgrq(new Date());
                    updateList.add(existingJg);

                    //新增调账类型为3的对应数据
                    ZnsbTzzxFcshtxxZlxxjgDO dto = new ZnsbTzzxFcshtxxZlxxjgDO();
                    BeanUtils.copyBean(existingJg, dto);
                    dto.setTzlxDm(TzlxDmEnum.TZHCJ.getTzlxDm());
                    dto.setUuid(GyUtils.getUuid());
                    insertList.add(dto);
                }
            }
            try {
                // 执行数据库操作
                if (!insertList.isEmpty()) {
                    znsbTzzxFcshtxxZlxxjgMapper.insertBatch(insertList);
                }

                if (!updateList.isEmpty()) {
                    znsbTzzxFcshtxxZlxxjgMapper.updateBatch(updateList);
                }

                // 更新zlxx表状态
                if (!updateZlxxList.isEmpty()) {
                    znsbTzzxFcshtxxBzhtZlxxMapper.updateBatch(updateZlxxList);
                }

                // 更新标准合同表状态
                if (!updateBzhtList.isEmpty()) {
                    znsbTzzxFcshtxxBzhtMapper.updateBatch(updateBzhtList);
                }
            } catch (Exception e) {
                log.error("加工数据插入异常，{}", e.getMessage());
            }
        }
    }

    /**
     * 根据BPM数据生成合同台账数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map<String, String>> bzhtToHtxx(String tbgsh) {
        // 生成需要增加修改删除的合同台账数据
        List<ZnsbTzzxHttzxxDO> insertHttzxx = new ArrayList<>();
        List<ZnsbTzzxHttzxxDO> updateHttzxx = new ArrayList<>();
        // 生成需要增加修改删除的合同台账明细数据
        List<ZnsbTzzxHttzxxMxDO> insertHttzMxxx = new ArrayList<>();
        List<ZnsbTzzxHttzxxMxDO> updateHttzMxxx = new ArrayList<>();
        List<ZnsbTzzxHttzxxMxDO> deleteHttzMxxx = new ArrayList<>();
        List<Map<String, String>> updateFctzList = new ArrayList<>();
        Map<String, FcsNsrxxDTO> nsrxxMap = new HashMap<>();
        // 回更标准合同表和加工表
        List<String> updateBzhtUuidList = new ArrayList<>();
        List<String> updateZlxxUuidList = new ArrayList<>();
        log.info("FcsDataProcJobServiceImpl_bzhtToHtxx  tbgsh=={}", tbgsh);
        List<ZnsbTzzxFcshtxxBzhtDO> bzhtList = znsbTzzxFcshtxxBzhtMapper.selectAllUpdateData(tbgsh);
        List<ZnsbTzzxFcshtxxZlxxjgDO> zlxxjgQureyList = new ArrayList<>();
        List<String> uuidList = bzhtList.stream().map(ZnsbTzzxFcshtxxBzhtDO::getUuid).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(uuidList)) {
            //查询加工好的数据
            zlxxjgQureyList = znsbTzzxFcshtxxZlxxjgMapper.queryZlxxjgByZbuuid(uuidList);
        }
        Map<String, List<ZnsbTzzxFcshtxxZlxxjgDO>> bzhtZLxxjgMap = zlxxjgQureyList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcshtxxZlxxjgDO::getZbuuid));
        log.info("bpmDoList size:{}", bzhtList.size());
        // 生成 znsb_tzzx_httzxx
        for (ZnsbTzzxFcshtxxBzhtDO bzht : bzhtList) {
            List<ZnsbTzzxFcshtxxZlxxjgDO> zlxxjgList = bzhtZLxxjgMap.get(bzht.getUuid()).stream().sorted(Comparator.comparing(ZnsbTzzxFcshtxxZlxxjgDO::getZlrqq)).collect(Collectors.toList());
            // 查询纳税人信息
            ZnsbCxsFyxxcjb fyjcxx = new ZnsbCxsFyxxcjb();
            JbxxmxsjVO jbxxmxsjvo = new JbxxmxsjVO();
            if (nsrxxMap.containsKey(bzht.getSpmc1())) {
                fyjcxx = nsrxxMap.get(bzht.getSpmc1()).getFyjcxx();
                jbxxmxsjvo = nsrxxMap.get(bzht.getSpmc1()).getJbxxmxsjvo();
            } else {
                fyjcxx = znsbtzzxfcjcxxtzbservice.queryFyxxByFwcqzsh(bzht.getSpmc1());
                ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                znsbMhzcQyjbxxmxReqVO.setDjxh(fyjcxx.getDjxh());
                CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxx = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
                ZnsbMhzcQyjbxxmxResVO znsbmhzcqyjbxxmxresvo = nsrxx.getData();
                List<JbxxmxsjVO> jbxxmxsjVOList = znsbmhzcqyjbxxmxresvo.getJbxxmxsj();
                jbxxmxsjvo = jbxxmxsjVOList.get(0);
                FcsNsrxxDTO fcsNsrxxDTO = new FcsNsrxxDTO();
                fcsNsrxxDTO.setFyjcxx(fyjcxx);
                fcsNsrxxDTO.setJbxxmxsjvo(jbxxmxsjvo);
                nsrxxMap.put(bzht.getSpmc1(), fcsNsrxxDTO);
            }

            // 赋值
            ZnsbTzzxHttzxxDO httzxxDO = new ZnsbTzzxHttzxxDO();
            httzxxDO.setDjxh(new BigDecimal(fyjcxx.getDjxh()));
            httzxxDO.setNsrsbh(jbxxmxsjvo.getNsrsbh());
            httzxxDO.setNsrmc(jbxxmxsjvo.getNsrmc());
            httzxxDO.setZgswjgDm(fyjcxx.getZgswskfjDm());
            httzxxDO.setFybh(fyjcxx.getFybh());
            httzxxDO.setFwcqzsh(fyjcxx.getFwcqzsh());
            httzxxDO.setFwzldz(fyjcxx.getFwzldz());
            httzxxDO.setHtbh(bzht.getHtbh());
            httzxxDO.setHtmc(bzht.getHtmc());
            httzxxDO.setGsmc(bzht.getGsmc());
            httzxxDO.setGsh2(bzht.getGsh());
            httzxxDO.setLrzx(bzht.getLrzx());
            httzxxDO.setHtqdrq(bzht.getHtqdrq());
            httzxxDO.setHtydsxrq(bzht.getHtydsxrq());
            httzxxDO.setHtydzzrq(bzht.getHtydzzrq());
            httzxxDO.setSkfs(bzht.getSkfs());
            httzxxDO.setZzrq(bzht.getZzrq());
            httzxxDO.setFwcqzsh(bzht.getSpmc1());
            httzxxDO.setCzmj(bzht.getCzmj());
            httzxxDO.setZlrqq(bzht.getHtydsxrq());
            httzxxDO.setZlrqz(bzht.getHtydzzrq());
            httzxxDO.setSl1(bzht.getSl1());
            httzxxDO.setLy(FcsTzConstans.LY_DSRW);
            httzxxDO.setYzjsr(zlxxjgList.stream().map(ZnsbTzzxFcshtxxZlxxjgDO::getBhsyzjsr).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(6, RoundingMode.HALF_UP));
            httzxxDO.setHsyzjsr(zlxxjgList.stream().map(ZnsbTzzxFcshtxxZlxxjgDO::getHsyzjsr).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(6, RoundingMode.HALF_UP));
            httzxxDO.setHsje(httzxxDO.getHsyzjsr());
            httzxxDO.setSjsj();
            this.setDefaultValue(httzxxDO);

            // 判断是新增还是修改
            List<ZnsbTzzxHttzxxDO> htDoList = znsbtzzxhttzxxmapper.selectHtxxListByHtbh(bzht.getHtbh(), bzht.getFybh());
            if (GyUtils.isNotNull(htDoList)) {
                ZnsbTzzxHttzxxDO zhttzxxDo = htDoList.get(0);
                httzxxDO.setUuid(zhttzxxDo.getUuid());
                updateHttzxx.add(httzxxDO);
            } else {
                insertHttzxx.add(httzxxDO);
            }

            // 回更标准合同表
            updateBzhtUuidList.add(bzht.getUuid());

            // 生成znsb_tzzx_httzxx_mx
            List<ZnsbTzzxHttzxxMxDO> yyMxDOList = znsbtzzxhttzxxmxmapper.selectAllHtmxdoByZbUuid(httzxxDO.getUuid());
            Map<Integer, ZnsbTzzxHttzxxMxDO> zhttzxxMxMap = yyMxDOList.stream().collect(Collectors.toMap(ZnsbTzzxHttzxxMxDO::getSszq, zhttzxxMxDO -> zhttzxxMxDO));
            // 找出在zhttzxxMxMap中存在但在循环zlxxjgList时不存在的sszq
            Set<Integer> zlxxjgSszqSet = zlxxjgList.stream()
                    .map(zlxxjg -> {
                        Calendar startDate = Calendar.getInstance();
                        startDate.setTime(zlxxjg.getZlrqq());
                        return Integer.parseInt(DateUtil.doDateFormat(startDate.getTime(), "yyyyMM"));
                    })
                    .collect(Collectors.toSet());

            Set<Integer> toDeleteSszqSet = zhttzxxMxMap.keySet().stream()
                    .filter(sszq -> !zlxxjgSszqSet.contains(sszq))
                    .collect(Collectors.toSet());

            // 处理需要删除的记录
            if (!toDeleteSszqSet.isEmpty()) {
                toDeleteSszqSet.forEach(sszq -> {
                    deleteHttzMxxx.add(zhttzxxMxMap.get(sszq));
                });
            }
            for (ZnsbTzzxFcshtxxZlxxjgDO zlxxjg : zlxxjgList) {
                ZnsbTzzxHttzxxMxDO znsbtzzxhttzxxmxdo = new ZnsbTzzxHttzxxMxDO();
                znsbtzzxhttzxxmxdo.setDefaultValue();
                znsbtzzxhttzxxmxdo.setZbuuid(httzxxDO.getUuid());
                znsbtzzxhttzxxmxdo.setZlrqq(zlxxjg.getZlrqq());
                znsbtzzxhttzxxmxdo.setZlrqz(zlxxjg.getZlrqz());
                znsbtzzxhttzxxmxdo.setCzmj(zlxxjg.getCzmj());
                Calendar startDate = Calendar.getInstance();
                startDate.setTime(zlxxjg.getZlrqq());
                Integer sszq = Integer.parseInt(DateUtil.doDateFormat(startDate.getTime(), "yyyyMM"));
                znsbtzzxhttzxxmxdo.setSszq(sszq);
                znsbtzzxhttzxxmxdo.setHsyzjsr(zlxxjg.getHsyzjsr());
                znsbtzzxhttzxxmxdo.setYzjsr(zlxxjg.getBhsyzjsr());
                znsbtzzxhttzxxmxdo.setSl1(httzxxDO.getSl1());
                znsbtzzxhttzxxmxdo.setSjczmj(zlxxjg.getSjczmj());
                znsbtzzxhttzxxmxdo.setSjhsyzjsr(zlxxjg.getSjhsyzjsr());
                znsbtzzxhttzxxmxdo.setSjyzjsr(zlxxjg.getSjbhsyzjsr());
                znsbtzzxhttzxxmxdo.setSjsl1(httzxxDO.getSjsl1());
                znsbtzzxhttzxxmxdo.setLy(httzxxDO.getLy());
                znsbtzzxhttzxxmxdo.setSjsj();
                znsbtzzxhttzxxmxdo.setDefaultValue();
                if (yyMxDOList.isEmpty()) {
                    insertHttzMxxx.add(znsbtzzxhttzxxmxdo);
                } else if (zhttzxxMxMap.containsKey(sszq)) {
                    znsbtzzxhttzxxmxdo.setUuid(zhttzxxMxMap.get(sszq).getUuid());
                    updateHttzMxxx.add(znsbtzzxhttzxxmxdo);
                }
                updateZlxxUuidList.add(zlxxjg.getUuid());
            }
        }

        try {
            if (GyUtils.isNotNull(insertHttzxx)) {
                log.info("FcsDataProcJobServiceImpl_bpmToHtxx insertHttzxx={}", JsonUtils.toJson(insertHttzxx));
                znsbtzzxhttzxxmapper.insertBatch(insertHttzxx);
            }

            if (GyUtils.isNotNull(updateHttzxx)) {
                log.info("FcsDataProcJobServiceImpl_bpmToHtxx updateHttzxx={}", JsonUtils.toJson(updateHttzxx));
                znsbtzzxhttzxxmapper.updateBatch(updateHttzxx);
            }

            if (GyUtils.isNotNull(insertHttzMxxx)) {
                log.info("FcsDataProcJobServiceImpl_bpmToHtxx insertHttzMxxx={}", JsonUtils.toJson(insertHttzMxxx));
                znsbtzzxhttzxxmxmapper.insertBatch(insertHttzMxxx);
            }

            if (GyUtils.isNotNull(updateHttzMxxx)) {
                log.info("FcsDataProcJobServiceImpl_bpmToHtxx updateHttzMxxx={}", JsonUtils.toJson(updateHttzMxxx));
                znsbtzzxhttzxxmxmapper.updateBatch(updateHttzMxxx);
            }

            if (GyUtils.isNotNull(deleteHttzMxxx)) {
                log.info("FcsDataProcJobServiceImpl_bpmToHtxx deleteHttzMxxx={}", JsonUtils.toJson(deleteHttzMxxx));
                znsbtzzxhttzxxmxmapper.deleteBatchIds(deleteHttzMxxx);
            }

            if (GyUtils.isNotNull(updateBzhtUuidList)) {
                znsbTzzxFcshtxxBzhtMapper.updateHzscbzByUuids(updateBzhtUuidList, FcsConstants.FCS_BZHT_YES);
            }

            if (GyUtils.isNotNull(updateZlxxUuidList)) {
                znsbTzzxFcshtxxZlxxjgMapper.updateHzscbzByUuids(updateZlxxUuidList, FcsConstants.FCS_BZHT_YES);
            }
        } catch (Exception e) {
            log.error("生成合同台账异常，{}", e.getMessage());
        }
        log.info("FcsDataProcJobServiceImpl_bpmToHtxx updateFctzList={}", JsonUtils.toJson(updateFctzList));
        return updateFctzList;
    }

    @Override
    @Transactional
    public void htxxToFcstzDl() {
        log.info("htxxToFcstzDl start");
        List<ZnsbCxsFyxxcjb> allFyxx = znsbCxsFyxxcjbMapper.selectList();
        log.info("htxxToFcstzDl allFyxx size={}", allFyxx.size());
        Map<String, List<ZnsbCxsFyxxcjb>> fyxxMap = new HashMap<>();
        // 测试用
        //allFyxx = allFyxx.stream().filter(fyxx -> "F51000020250000216".equals(fyxx.getFybh())).collect(Collectors.toList());
        allFyxx.stream().forEach(fyxx -> {
            if (GyUtils.isNotNull(fyxx.getFybh()) && "Y".equals(fyxx.getYxbz()) && !FcsUtil.isNsywzz(fyxx, new Date())) {
                String djxh = fyxx.getDjxh();
                if (GyUtils.isNull(fyxxMap.get(djxh))) {
                    List<ZnsbCxsFyxxcjb> fyList = new ArrayList<>();
                    fyList.add(fyxx);
                    fyxxMap.put(djxh, fyList);
                } else {
                    fyxxMap.get(djxh).add(fyxx);
                }
            }
        });
        List<ZnsbTzzxFctzmxbDO> delList = new ArrayList<>();

        log.info("htxxToFcstzDl fyxxMap size={}", fyxxMap.size());
        if (GyUtils.isNotNull(fyxxMap)) {
            fyxxMap.entrySet().stream().forEach(entry -> {
                String key = entry.getKey();
                List<ZnsbCxsFyxxcjb> fyList = entry.getValue();
                log.info("htxxToFcstzDl key={}", key);
                log.info("htxxToFcstzDl fyList={}", JsonUtils.toJson(fyList));
                final Map<String, Object> pzbMap = CacheUtils.getTableData("cs_znsb_ftssbpzb", key);
                log.info("htxxToFcstzDl pzbMap={}", JsonUtils.toJson(pzbMap));
                List<Map<String, Object>> pzbList = null;
                if (GyUtils.isNotNull(pzbMap)) {
                    pzbList = JsonUtils.toMapList((String) pzbMap.get("dataList"));
                    pzbList = pzbList.stream().filter(vo -> "101100800".equals(vo.get("zspmDm"))).collect(Collectors.toList());
                }
                //if (GyUtils.isNotNull(pzbList)) {
                    CzfReqVO czfreqvo = new CzfReqVO();
                    czfreqvo.setDjxh(key);
                    String xzqhDm = fyList.get(0).getXzqhszDm();
                    // 查询征期 征期前都按照征期前对应的季度进行申报
                    CommonResult<List<GszqxxVO>> zqRes = zqrlApi.getSfAndZqList();
                    log.info("税费申报调用征期接口返回数据：{}", JsonUtils.toJson(zqRes));
                    // 获取征期时间
                    List<GszqxxVO> zqList = zqRes.getData();
                    // 优先匹配前四位，匹配不到再匹配前两位
                    GszqxxVO zqvo = zqList.stream()
                            .filter(vo -> vo.getXzqhszDm().contains(xzqhDm.substring(0, 4)))
                            .findAny()
                            .orElse(zqList.stream()
                                    .filter(vo -> vo.getXzqhszDm().contains(xzqhDm.substring(0, 2)))
                                    .findAny()
                                    .orElse(zqList.get(0)));
                    //判断税费申报征期控制参数
                    if (!GyUtils.isNull(zqvo)) {
                        log.info("税费申报获取到的当前征期为：{}", JsonUtils.toJson(zqvo));
                        Date zqsj = DateUtils.toDate(zqvo.getZqsj(), "yyyy-MM-dd hh:mm:ss");
                        Map<String, String> lastYearMonthMap = DateUtil.getQuarterStartAndEndTimeByZqsj(zqsj);
                        String cxrqq = lastYearMonthMap.get("startTime");
                        String cxrqz = lastYearMonthMap.get("endTime");
                        log.info("htxxToFcstzDl cxrqq={},cxrqz={}", cxrqq, cxrqz);
                        int jgyf = FcsUtil.getJgyf(cxrqq, cxrqz) + 1;
                        log.info("htxxToFcstzDl jgyf={}", jgyf);
                        for (int i = 0; i < jgyf; i++) {
                            Calendar cxrqqC = Calendar.getInstance();
                            cxrqqC.setTime(DateUtil.toDate("yyyy-MM-dd", cxrqq));
                            cxrqqC.add(Calendar.MONTH, i);
                            String tbyf = DateUtil.doDateFormat(cxrqqC.getTime(), "yyyy-MM");
                            fyList.stream().forEach(fyxx -> {
                                this.htxxToFcstzDl(tbyf, fyxx.getFybh());
                            });
                        }
                        czfreqvo.setSbsjq(cxrqz);
                        List<ZnsbTzzxFctzmxbDO> fctzList = znsbtzzxfctzmxbmapper.selectZnsbTzzxFctzmxbList(czfreqvo);
                        for (ZnsbTzzxFctzmxbDO fctzmx : fctzList) {
                            if (FcsTzConstans.SBZT_WSC.equals(fctzmx.getSyzt())) {
                                fctzmx.setScbz("Y");
                                fctzmx.setXgrq(new Date());
                                fctzmx.setXgrsfid("dsrwsc");
                                delList.add(fctzmx);
                            }
                        }
                    } else {
                        log.info("税费申报初始化未获取到有效的征期参数，传入参数sbny，xzqhszDm为：{}，{}", DateUtils.getSystemCurrentTime(17), xzqhDm);
                    }
                //}
//                else {
//                    CzfReqVO czfreqvo = new CzfReqVO();
//                    czfreqvo.setDjxh(key);
//                    List<ZnsbTzzxFctzmxbDO> fctzList = znsbtzzxfctzmxbmapper.selectZnsbTzzxFctzmxbList(czfreqvo);
//                    for (ZnsbTzzxFctzmxbDO fctzmx : fctzList) {
//                        if (FcsTzConstans.SBZT_WSC.equals(fctzmx.getSyzt())) {
//                            fctzmx.setScbz("Y");
//                            fctzmx.setXgrq(new Date());
//                            fctzmx.setXgrsfid("dsrwsc");
//                            delList.add(fctzmx);
//                        }
//                    }
//                }
            });
        }
//        if(GyUtils.isNotNull(delList)){
//            log.info("htxxToFcstz delList={}",JsonUtils.toJson(delList));
//            znsbtzzxfctzmxbmapper.updateBatch(delList);
//        }
        log.info("htxxToFcstzNew end");
    }

    /**
     * 大连重工生成房产税台账
     */
    @Override
    @Transactional
    public void htxxToFcstzDl(String scyf, String tbfybh) {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("FcsDataProcJobServiceImpl_htxxToFcstzDl  start");
        log.info("htxxToFcstzDl jobParam::{}", jobParam);
        log.info("htxxToFcstzDl scyf::{}", scyf);
        log.info("htxxToFcstzDl tbfybh::{}", tbfybh);
        Date skssqq = this.getSkssqq(scyf);
        Date skssqz = this.getSkssqz(scyf);
        log.info("htxxToFcstzDl skssqq={}", skssqq);
        log.info("htxxToFcstzDl skssqz={}", skssqz);
        int quarter = DateUtil.getQuarterByDate(skssqq);

        Map<String, ZnsbTzzxFctzmxbDO> htfybytzxxMap = new HashMap<>();
        List<ZnsbCxsFyxxcjb> allFyxx = null;
        if (GyUtils.isNull(tbfybh)) {
            allFyxx = znsbCxsFyxxcjbMapper.selectList();
        } else {
            ZnsbCxsFyxxcjb Fyxxcjb = znsbCxsFyxxcjbMapper.queryByFybh(tbfybh, null, false);
            allFyxx = new ArrayList<>();
            allFyxx.add(Fyxxcjb);
        }
        //遍历房产信息生成房产税台账信息
//        for (ZnsbCxsFyxxcjb jcxxDo : allFyxx) {
//            //判断是否是上线房产税的企业
//            //final Map<String, Object> pzbMap = CacheUtils.getTableData("cs_znsb_ftssbpzb", String.valueOf(jcxxDo.getDjxh()));
//            log.info("FctzmxController_initData  djxh::{}", jcxxDo.getDjxh());
////            log.info("FctzmxController_initData  pzbMap::{}", JsonUtils.toJson(pzbMap));
////            if (GyUtils.isNull(pzbMap)) {
////                continue;
////            }
//            String fybh = jcxxDo.getFybh();
//            List<ZnsbTzzxFcssyfckmfpdzbDO> fcssyfckmfpdzbList = znsbtzzxfcssyfckmfpdzbmapper.selecDzbxxListByFybh(fybh);
//            if (GyUtils.isNotNull(fcssyfckmfpdzbList)) {
//                continue;
//            }
//            if (FcsUtil.isNsywzz(jcxxDo, skssqz)) {
//                continue;
//            }
//            ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdo = new ZnsbTzzxFctzmxbDO();
//            this.setDefaultValue(znsbtzzxfctzmxbdo);
//            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
//            znsbMhzcQyjbxxmxReqVO.setDjxh(jcxxDo.getDjxh());
//            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxx = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
//            ZnsbMhzcQyjbxxmxResVO znsbmhzcqyjbxxmxresvo = nsrxx.getData();
//            List<JbxxmxsjVO> jbxxmxsjVOList = znsbmhzcqyjbxxmxresvo.getJbxxmxsj();
//            JbxxmxsjVO jbxxmxsjvo = jbxxmxsjVOList.get(0);
//            znsbtzzxfctzmxbdo.setDjxh(new BigDecimal(jbxxmxsjvo.getDjxh()));
//
//            znsbtzzxfctzmxbdo.setNsrmc(jbxxmxsjvo.getNsrmc());
//            znsbtzzxfctzmxbdo.setNsrsbh(jbxxmxsjvo.getNsrsbh());
//            znsbtzzxfctzmxbdo.setFybh(jcxxDo.getFybh());
//            znsbtzzxfctzmxbdo.setFwcqzsh(jcxxDo.getFwcqzsh());
//            znsbtzzxfctzmxbdo.setFwzldz(jcxxDo.getFwzldz());
//            znsbtzzxfctzmxbdo.setSkssqq(skssqq);
//            znsbtzzxfctzmxbdo.setSkssqz(skssqz);
//            znsbtzzxfctzmxbdo.setCzbz("2");
//            znsbtzzxfctzmxbdo.setCzmj(new BigDecimal("0.0"));
//            znsbtzzxfctzmxbdo.setZjsr1(new BigDecimal("0.0"));
//            znsbtzzxfctzmxbdo.setSl1(FcsTzConstans.CZSL);
//            znsbtzzxfctzmxbdo.setYnse(new BigDecimal("0.0"));
//            znsbtzzxfctzmxbdo.setZbuuid(jcxxDo.getUuid());
////                znsbtzzxfctzmxbdo.setFcyz(znsbtzzxfcjcxxtzbdo.getFcyz());
//            htfybytzxxMap.put(jcxxDo.getFybh(), znsbtzzxfctzmxbdo);
//        }

        // 获取房产台账表信息
        List<ZnsbTzzxFcsfyzctzDO> fcsfyzctzList = znsbtzzxFcsfyzctzMapper.selectFyzctzListByDate(skssqq, skssqz, tbfybh, quarter);
        if (!CollectionUtils.isEmpty(fcsfyzctzList)) {
            List<ZnsbTzzxFctzmxbDO> insertList = new ArrayList<>();
            List<ZnsbTzzxFctzmxbDO> updateList = new ArrayList<>();
            Map<String, List<ZnsbTzzxFcsfyzctzDO>> groupMap = fcsfyzctzList.stream().collect(Collectors.groupingBy(ZnsbTzzxFcsfyzctzDO::getSybh1));
            //遍历房产台账表信息
            for (String key : groupMap.keySet()) {
                log.info("htxxToFcstzDl fybh={}", key);

                // 房源取得时间下月才生成 判断如果房产取得时间对应的年月大于等于skssqq对应的年月，则跳过
                if (groupMap.get(key).get(0).getFcqdsj() != null && skssqq != null) {
                    // 将日期转换为年月格式进行比较 (yyyyMM)
                    Calendar fcqdsjCal = Calendar.getInstance();
                    fcqdsjCal.setTime(groupMap.get(key).get(0).getFcqdsj());
                    int fcqdsjYm = fcqdsjCal.get(Calendar.YEAR) * 100 + (fcqdsjCal.get(Calendar.MONTH) + 1);

                    Calendar skssqqCal = Calendar.getInstance();
                    skssqqCal.setTime(skssqq);
                    int skssqqYm = skssqqCal.get(Calendar.YEAR) * 100 + (skssqqCal.get(Calendar.MONTH) + 1);

                    if (fcqdsjYm >= skssqqYm) {
                        continue; // 房产取得时间大于等于税款所属期起，跳过处理
                    }
                }

                String djxh = allFyxx.stream().filter(fyjcxx -> fyjcxx.getFybh().equals(key)).
                        findFirst().map(ZnsbCxsFyxxcjb::getDjxh).orElse(null);

                //根据房源编号获取房屋基本信息，获取不到不加工
                ZnsbCxsFyxxcjb fyjcxx = znsbCxsFyxxcjbMapper.queryByFybh(key, null, false);
                if (GyUtils.isNull(fyjcxx) || FcsUtil.isNsywzz(fyjcxx, skssqz)) {
                    continue;
                }
                List<ZnsbTzzxFcsfyzctzDO> fyzctzList = groupMap.get(key);

                // 区分从价和从租
                List<ZnsbTzzxFcsfyzctzDO> cjList = fyzctzList.stream()
                        .filter(fyzctzDO -> fyzctzDO.getJsfs().equals(FcsTzConstans.CJDM)).collect(Collectors.toList());
                List<ZnsbTzzxFcsfyzctzDO> czList = fyzctzList.stream()
                        .filter(fyzctzDO -> fyzctzDO.getJsfs().equals(FcsTzConstans.CZDM)).collect(Collectors.toList());

                // 查询是否存在房产台账明细
                ZnsbTzzxFctzmxbDO cxdo = new ZnsbTzzxFctzmxbDO();
                cxdo.setFybh(key);
                cxdo.setSkssqq(skssqq);
                cxdo.setSkssqz(skssqz);
                List<ZnsbTzzxFctzmxbDO> znsbtzzxfctzmxbdocList = znsbtzzxfctzmxbmapper.selecFctzmxByFybhSkssq(cxdo);

                ZnsbTzzxFctzmxbDO czfctzmxbdoUpdate = new ZnsbTzzxFctzmxbDO();
                ZnsbTzzxFctzmxbDO cjfctzmxbdoUpdate = new ZnsbTzzxFctzmxbDO();

                String syzt = "0";
                for (ZnsbTzzxFctzmxbDO tzmxbdo : znsbtzzxfctzmxbdocList) {
                    if ("1".equals(tzmxbdo.getCzbz())) {
                        cjfctzmxbdoUpdate.setXgrsfid("dsrw");
                        cjfctzmxbdoUpdate = tzmxbdo;
                    } else if ("2".equals(tzmxbdo.getCzbz())) {
                        syzt = tzmxbdo.getSyzt();
                        czfctzmxbdoUpdate.setXgrsfid("dsrw");
                        czfctzmxbdoUpdate = tzmxbdo;
                    }
                }
                // 初始化从租和从价台账
                if (StringUtils.isEmpty(cjfctzmxbdoUpdate.getUuid())) {
                    this.setFctzmxValue(cjfctzmxbdoUpdate, fyzctzList.get(0), key, skssqq, skssqz, fyjcxx.getUuid(),
                            FcsTzConstans.CJDM, BigDecimal.ZERO, BigDecimal.ZERO, FcsTzConstans.CJSL, djxh);
                }
                if (StringUtils.isEmpty(czfctzmxbdoUpdate.getUuid())) {
                    this.setFctzmxValue(czfctzmxbdoUpdate, fyzctzList.get(0), key, skssqq, skssqz, fyjcxx.getUuid(),
                            FcsTzConstans.CZDM, BigDecimal.ZERO, BigDecimal.ZERO, FcsTzConstans.CZSL, djxh);
                }

                if (!CollectionUtils.isEmpty(cjList)) {
                    if (StringUtils.isNotEmpty(cjfctzmxbdoUpdate.getUuid())) {
                        this.setje(cjList, cjfctzmxbdoUpdate);
                        updateList.add(cjfctzmxbdoUpdate);
                    } else {
                        BigDecimal zjsr = cjList.stream().map(ZnsbTzzxFcsfyzctzDO::getJsyj)
                                .reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(12), 6, RoundingMode.HALF_UP);
                        BigDecimal ynse = cjList.stream().map(ZnsbTzzxFcsfyzctzDO::getYjfcsyd)
                                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
                        cjfctzmxbdoUpdate.setZjsr1(zjsr);
                        cjfctzmxbdoUpdate.setYnse(ynse);
                        cjfctzmxbdoUpdate.setUuid(GyUtils.getUuid());
                        insertList.add(cjfctzmxbdoUpdate);
                    }
                } else {
                    cjfctzmxbdoUpdate.setUuid(GyUtils.getUuid());
                    insertList.add(cjfctzmxbdoUpdate);
                }
                if (!CollectionUtils.isEmpty(czList)) {
                    if (StringUtils.isNotEmpty(czfctzmxbdoUpdate.getUuid())) {
                        //本次同步出租面积
                        BigDecimal bcCzmj = czfctzmxbdoUpdate.getCzmj().setScale(2, RoundingMode.HALF_UP);
                        //本次同步租金收入
                        BigDecimal bcZjsr = czfctzmxbdoUpdate.getZjsr1().setScale(6, RoundingMode.HALF_UP);
                        //本次同步房产原值
                        BigDecimal bcFcyz = czfctzmxbdoUpdate.getFcyz().setScale(2, RoundingMode.HALF_UP);
                        this.setje(cjList, czfctzmxbdoUpdate);

                        //原始台账出租面积
                        BigDecimal ysCzmj = czfctzmxbdoUpdate.getCzmj().setScale(2, RoundingMode.HALF_UP);
                        //原始同步租金收入
                        BigDecimal ysZjsr = czfctzmxbdoUpdate.getZjsr1().setScale(6, RoundingMode.HALF_UP);
                        //原始同步房产原值
                        BigDecimal ysFcyz = czfctzmxbdoUpdate.getFcyz().setScale(2, RoundingMode.HALF_UP);

                        //如果税源状态不是未生成状态，并且数据改变，数据变更状态为已变更
                        if (!"0".equals(syzt)) {
                            if (bcCzmj.compareTo(ysCzmj) != 0 || bcZjsr.compareTo(ysZjsr) != 0 || bcFcyz.compareTo(ysFcyz) != 0) {
                                czfctzmxbdoUpdate.setBgzt("Y");
                            }
                        }
                        updateList.add(czfctzmxbdoUpdate);
                    } else {
                        BigDecimal zjsr = czList.stream().map(ZnsbTzzxFcsfyzctzDO::getJsyj)
                                .reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(12), 6, RoundingMode.HALF_UP);
                        BigDecimal ynse = czList.stream().map(ZnsbTzzxFcsfyzctzDO::getYjfcsyd)
                                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
                        czfctzmxbdoUpdate.setZjsr1(zjsr);
                        czfctzmxbdoUpdate.setYnse(ynse);
                        czfctzmxbdoUpdate.setUuid(GyUtils.getUuid());
                        insertList.add(czfctzmxbdoUpdate);
                    }
                } else {
                    czfctzmxbdoUpdate.setUuid(GyUtils.getUuid());
                    insertList.add(czfctzmxbdoUpdate);
                }

                if (DateUtil.isLastMonthOfQuarter(skssqz)) {
                    fyzctzList.forEach(fyzctzDO -> {
                        fyzctzDO.setQuarter(DateUtil.getQuarterByDate(skssqz));
                        fyzctzDO.setZzscbz("Y");
                    });
                }
            }

            if (GyUtils.isNotNull(insertList)) {
                log.info("htxxToFcstzDl insertList={}", JsonUtils.toJson(insertList));
                znsbtzzxfctzmxbmapper.insertBatch(insertList);
            }
            if (GyUtils.isNotNull(updateList)) {
                log.info("htxxToFcstzDl updateList={}", JsonUtils.toJson(updateList));
                znsbtzzxfctzmxbmapper.updateBatch(updateList);
            }
            if (GyUtils.isNotNull(fcsfyzctzList)) {
                znsbTzzxFcsfyzctzMapper.updateBatch(fcsfyzctzList);
            }
        }
    }

    private void setje(List<ZnsbTzzxFcsfyzctzDO> list, ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdo) {
        BigDecimal ynse = list.stream().map(ZnsbTzzxFcsfyzctzDO::getYjfcsyd)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        BigDecimal czmj = list.stream().map(ZnsbTzzxFcsfyzctzDO::getFtmj)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        BigDecimal fcyz = list.stream().map(ZnsbTzzxFcsfyzctzDO::getFcyz)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        BigDecimal zjsr = list.stream().map(ZnsbTzzxFcsfyzctzDO::getJsyj)
                .reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(12), 6, RoundingMode.HALF_UP);
        znsbtzzxfctzmxbdo.setCzmj(znsbtzzxfctzmxbdo.getCzmj().add(czmj));
        znsbtzzxfctzmxbdo.setFcyz(znsbtzzxfctzmxbdo.getFcyz().add(fcyz));
        znsbtzzxfctzmxbdo.setZjsr1(znsbtzzxfctzmxbdo.getZjsr1().add(zjsr));
        znsbtzzxfctzmxbdo.setYnse(znsbtzzxfctzmxbdo.getYnse().add(ynse));
    }

    private void setFctzmxValue(ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdo, ZnsbTzzxFcsfyzctzDO fyzctzDO, String fybh,
                                Date skssqq, Date skssqz, String zbuuid, String czbz, BigDecimal zjsr, BigDecimal ynse, BigDecimal sl1, String djxh) {
        ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxx = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
        ZnsbMhzcQyjbxxmxResVO znsbmhzcqyjbxxmxresvo = nsrxx.getData();
        List<JbxxmxsjVO> jbxxmxsjVOList = znsbmhzcqyjbxxmxresvo.getJbxxmxsj();
        JbxxmxsjVO jbxxmxsjvo = jbxxmxsjVOList.get(0);
        znsbtzzxfctzmxbdo.setDjxh(new BigDecimal(jbxxmxsjvo.getDjxh()));
        znsbtzzxfctzmxbdo.setNsrmc(jbxxmxsjvo.getNsrmc());
        znsbtzzxfctzmxbdo.setNsrsbh(jbxxmxsjvo.getNsrsbh());
        znsbtzzxfctzmxbdo.setFybh(fybh);
        znsbtzzxfctzmxbdo.setFwcqzsh(fyzctzDO.getFwcqzsh());
        znsbtzzxfctzmxbdo.setFwzldz(fyzctzDO.getDz());
        znsbtzzxfctzmxbdo.setSkssqq(skssqq);
        znsbtzzxfctzmxbdo.setSkssqz(skssqz);
        znsbtzzxfctzmxbdo.setCzbz(czbz);
        znsbtzzxfctzmxbdo.setCzmj(fyzctzDO.getFtmj());
        znsbtzzxfctzmxbdo.setZjsr1(zjsr);
        znsbtzzxfctzmxbdo.setSl1(sl1);
        znsbtzzxfctzmxbdo.setYnse(ynse);
        znsbtzzxfctzmxbdo.setZbuuid(zbuuid);
        znsbtzzxfctzmxbdo.setFcyz(fyzctzDO.getFcyz());
        znsbtzzxfctzmxbdo.setLrrq(new Date());
        znsbtzzxfctzmxbdo.setXgrq(new Date());
        znsbtzzxfctzmxbdo.setBgzt("N");
        znsbtzzxfctzmxbdo.setYwqdDm("TZ_USER");
        znsbtzzxfctzmxbdo.setScbz("N");
        znsbtzzxfctzmxbdo.setSjtbSj(new Date());
        znsbtzzxfctzmxbdo.setSjgsdq("TZ_USER");
        znsbtzzxfctzmxbdo.setSjcsdq("TZ_USER");
        znsbtzzxfctzmxbdo.setXtly("BPM");
        znsbtzzxfctzmxbdo.setSyzt(FcsTzConstans.SBZT_WSC);
    }

    private boolean isJgFieldsEqual(ZnsbTzzxFcshtxxZlxxjgDO expectedJg, ZnsbTzzxFcshtxxZlxxjgDO existingJg) {
        if (expectedJg == existingJg) {
            return true;
        }

        if (expectedJg == null || existingJg == null) {
            return false;
        }

        return Objects.equals(expectedJg.getZbuuid(), existingJg.getZbuuid()) &&
                Objects.equals(expectedJg.getZlrqq(), existingJg.getZlrqq()) &&
                Objects.equals(expectedJg.getZlrqz(), existingJg.getZlrqz()) &&
                Objects.equals(expectedJg.getHsyzjsr(), existingJg.getHsyzjsr()) &&
                Objects.equals(expectedJg.getBhsyzjsr(), existingJg.getBhsyzjsr()) &&
                Objects.equals(expectedJg.getCzzje(), existingJg.getCzzje()) &&
                Objects.equals(expectedJg.getCzmj(), existingJg.getCzmj()) &&
                Objects.equals(expectedJg.getSjczmj(), existingJg.getSjczmj()) &&
                Objects.equals(expectedJg.getSjhsyzjsr(), existingJg.getSjhsyzjsr()) &&
                Objects.equals(expectedJg.getSjbhsyzjsr(), existingJg.getSjbhsyzjsr()) &&
                Objects.equals(expectedJg.getIndexColumns(), existingJg.getIndexColumns());
    }

    private void setDefaultValue(ZnsbTzzxFcshtxxZlxxjgDO zlxxjgDO) {
        zlxxjgDO.setHzscbz("N");
        zlxxjgDO.setLrrq(new Date());
        zlxxjgDO.setXgrq(new Date());
        zlxxjgDO.setYwqdDm("TZ_USER");
        zlxxjgDO.setLrrsfid("ZNSB.TZZX");
        zlxxjgDO.setXgrsfid("ZNSB.TZZX");
        zlxxjgDO.setSjtbSj(new Date());
        zlxxjgDO.setSjgsdq("TZ_USER");
        zlxxjgDO.setSjcsdq("TZ_USER");
    }

    private void setDefaultValue(ZnsbTzzxFcshtxxBzhtDO bzhtDO) {
        bzhtDO.setHzscbz("N");
        bzhtDO.setLrrq(new Date());
        bzhtDO.setXgrq(new Date());
        bzhtDO.setYwqdDm("TZ_USER");
        bzhtDO.setLrrsfid("ZNSB.TZZX");
        bzhtDO.setXgrsfid("ZNSB.TZZX");
        bzhtDO.setSjtbSj(new Date());
        bzhtDO.setSjgsdq("TZ_USER");
        bzhtDO.setSjcsdq("TZ_USER");
    }

    private void setDefaultValue(ZnsbTzzxFcshtxxBzhtZlxxDO zlxxDO) {
        zlxxDO.setLrrq(new Date());
        zlxxDO.setXgrq(new Date());
        zlxxDO.setYwqdDm("TZ_USER");
        zlxxDO.setLrrsfid("ZNSB.TZZX");
        zlxxDO.setXgrsfid("ZNSB.TZZX");
        zlxxDO.setSjtbSj(new Date());
        zlxxDO.setSjgsdq("TZ_USER");
        zlxxDO.setSjcsdq("TZ_USER");
    }

    private void setDefaultValue(ZnsbTzzxHttzxxDO znsbtzzxhttzxxdo) {
        znsbtzzxhttzxxdo.setUuid(GyUtils.getUuid());
        znsbtzzxhttzxxdo.setLrrq(new Date());
        znsbtzzxhttzxxdo.setXgrq(new Date());
        znsbtzzxhttzxxdo.setYwqdDm("TZ_USER");
        znsbtzzxhttzxxdo.setScbz("N");
        znsbtzzxhttzxxdo.setSjtbSj(new Date());
        znsbtzzxhttzxxdo.setSjgsdq("TZ_USER");
        znsbtzzxhttzxxdo.setSjcsdq("TZ_USER");
    }

    private void checkYcyy(String ycyy, List<String> ycyyList) {
        if (!ycyyList.contains(ycyy)) {
            ycyyList.add(ycyy);
        }
    }

    /**
     * 校验租赁区间是否连续,是否交叉
     *
     * @param zlxxList
     * @return
     */
    private String checkZlqj(List<ZnsbTzzxFcshtxxBpmZlxxDO> zlxxList) {
        if (zlxxList == null || zlxxList.isEmpty()) {
            return "";
        }

        // 按照zlrqq升序排序
        zlxxList.sort(Comparator.comparing(ZnsbTzzxFcshtxxBpmZlxxDO::getZlrqq));

        for (int i = 0; i < zlxxList.size() - 1; i++) {
            ZnsbTzzxFcshtxxBpmZlxxDO current = zlxxList.get(i);
            ZnsbTzzxFcshtxxBpmZlxxDO next = zlxxList.get(i + 1);

            // 检查当前记录的zlrqq是否大于下一条记录的zlrqq（交叉情况）
            if (current.getZlrqq() != null && next.getZlrqq() != null &&
                    current.getZlrqq().compareTo(next.getZlrqq()) > 0) {
                return FcsConstants.FCS_BZHT_ERROR_RENT_CROSSING;
            }

            // 检查当前记录的zlrqz是否大于等于下一条记录的zlrqq（交叉情况）
            if (current.getZlrqz() != null && next.getZlrqq() != null &&
                    current.getZlrqz().compareTo(next.getZlrqq()) >= 0) {
                // 检查是否完全重叠（当前结束时间大于下一个开始时间）
                if (current.getZlrqz().compareTo(next.getZlrqq()) > 0) {
                    return FcsConstants.FCS_BZHT_ERROR_RENT_CROSSING;
                }
                // 如果等于，说明是连续的，继续检查
            } else if (current.getZlrqz() != null && next.getZlrqq() != null) {
                // 当前记录的结束时间+1天不等于下一条记录的开始时间，说明不连续
                Calendar currentEnd = Calendar.getInstance();
                currentEnd.setTime(current.getZlrqz());
                currentEnd.add(Calendar.DAY_OF_MONTH, 1);

                if (currentEnd.getTime().compareTo(next.getZlrqq()) != 0) {
                    return FcsConstants.FCS_BZHT_ERROR_RENT_DISCONTINUOUS;
                }
            }
        }

        return ""; // 检查通过
    }

    /**
     * 校验租赁时间段
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public Boolean checkZlrq(Date startTime, Date endTime) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startTime);
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endTime);

        // 如果起止日期在同一个月内，则合格
        if (startCal.get(Calendar.YEAR) == endCal.get(Calendar.YEAR) &&
                startCal.get(Calendar.MONTH) == endCal.get(Calendar.MONTH)) {
            return true;
        }

        // 检查是否为完整的月份区间
        // 起始日期必须是月份的第一天
        if (startCal.get(Calendar.DAY_OF_MONTH) != 1) {
            return false;
        }

        // 结束日期必须是月份的最后一天
        int lastDayOfMonth = endCal.getActualMaximum(Calendar.DAY_OF_MONTH);
        if (endCal.get(Calendar.DAY_OF_MONTH) != lastDayOfMonth) {
            return false;
        }
        return true;
    }

    private String concatString(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }
        return String.join(";", list);
    }

    private void setDefaultValue(ZnsbTzzxFctzmxbDO znsbtzzxfctzmxbdo) {
        znsbtzzxfctzmxbdo.setUuid(GyUtils.getUuid());
        znsbtzzxfctzmxbdo.setLrrq(new Date());
        znsbtzzxfctzmxbdo.setXgrq(new Date());
        znsbtzzxfctzmxbdo.setBgzt("N");
        znsbtzzxfctzmxbdo.setYwqdDm("TZ_USER");
        znsbtzzxfctzmxbdo.setScbz("N");
        znsbtzzxfctzmxbdo.setSjtbSj(new Date());
        znsbtzzxfctzmxbdo.setSjgsdq("TZ_USER");
        znsbtzzxfctzmxbdo.setSjcsdq("TZ_USER");
        znsbtzzxfctzmxbdo.setXtly("BPM");
        znsbtzzxfctzmxbdo.setSyzt(FcsTzConstans.SBZT_WSC);
    }

    private void setDefaultValue(ZnsbTzzxCzpzmxbDO znsbtzzxczpzmxbdo) {
        znsbtzzxczpzmxbdo.setUuid(GyUtils.getUuid());
        znsbtzzxczpzmxbdo.setLrrq(new Date());
        znsbtzzxczpzmxbdo.setXgrq(new Date());
        znsbtzzxczpzmxbdo.setYwqdDm("TZ_USER");
        znsbtzzxczpzmxbdo.setScbz("N");
        znsbtzzxczpzmxbdo.setSjtbSj(new Date());
        znsbtzzxczpzmxbdo.setSjgsdq("TZ_USER");
        znsbtzzxczpzmxbdo.setSjcsdq("TZ_USER");
        znsbtzzxczpzmxbdo.setLy(FcsTzConstans.LY_DSRW);
    }

    private Date getSkssqq(String scyf) {
        Calendar skssqq = Calendar.getInstance();
        if (GyUtils.isNotNull(scyf)) {
//            skssqq.set(Calendar.MONTH,Integer.parseInt(scyf));
            Date paramDate = DateUtil.toDate("yyyy-MM", scyf);
            skssqq.setTime(paramDate);
            skssqq.add(Calendar.MONTH, 1);
        }

        skssqq.add(Calendar.MONTH, -1);
        skssqq.set(Calendar.DAY_OF_MONTH, 1);
        String dateStr = DateUtil.doDateFormat(skssqq.getTime(), "yyyy-MM-dd");
        Date skssqqD = DateUtil.toDate("yyyy-MM-dd", dateStr);
        return skssqqD;
    }

    private Date getSkssqz(String scyf) {
        Calendar skssqz = Calendar.getInstance();
        if (GyUtils.isNotNull(scyf)) {
//            skssqz.set(Calendar.MONTH,Integer.parseInt(scyf));
            Date paramDate = DateUtil.toDate("yyyy-MM", scyf);
            skssqz.setTime(paramDate);
            skssqz.add(Calendar.MONTH, 1);
        }
        skssqz.add(Calendar.MONTH, -1);
        skssqz.set(Calendar.DAY_OF_MONTH, skssqz.getActualMaximum(Calendar.DAY_OF_MONTH));
        String dateStr = DateUtil.doDateFormat(skssqz.getTime(), "yyyy-MM-dd");
        Date skssqzD = DateUtil.toDate("yyyy-MM-dd", dateStr);
        return skssqzD;
    }

}




