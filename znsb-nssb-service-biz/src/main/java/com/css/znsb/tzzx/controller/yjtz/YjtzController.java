package com.css.znsb.tzzx.controller.yjtz;

import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.pojo.domain.yjtz.ZnsbTzzxFzjgfztzDO;
import com.css.znsb.nssb.pojo.domain.yjtz.ZnsbTzzxQysdsyjtzDO;
import com.css.znsb.nssb.pojo.vo.yjtz.ZnsbTzzxQysdsyjtzItemVO;
import com.css.znsb.nssb.service.cwbb.CwbbBsService;
import com.css.znsb.tzzx.constants.TzzxErrorCodeConstants;
import com.css.znsb.tzzx.pojo.PageRecordsVO;
import com.css.znsb.tzzx.pojo.dto.qysdsyj.TygjDataRespDTO;
import com.css.znsb.tzzx.pojo.dto.qysdsyj.dlzg.CwbbReqDTO;
import com.css.znsb.tzzx.pojo.vo.datasync.ExtractDataReqVO;
import com.css.znsb.tzzx.pojo.vo.yjtz.QueryYjtzReqVO;
import com.css.znsb.tzzx.pojo.vo.yjtz.UpdateFzjgfztzReqVO;
import com.css.znsb.tzzx.service.yjtz.ZnsbTzzxDlzgDataSynService;
import com.css.znsb.tzzx.service.yjtz.ZnsbTzzxQysdDataSynService;
import com.css.znsb.tzzx.service.yjtz.ZnsbTzzxQysdsyjtzService;
import com.sap.conn.jco.JCoException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.css.znsb.framework.common.pojo.CommonResult.success;

@Tag(name = "企业所得税预缴台账")
@RestController
@RequestMapping("/yjtz")
@Validated
@Slf4j
public class YjtzController {

    @Resource
    private ZnsbTzzxQysdsyjtzService znsbTzzxQysdsyjtzService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ZnsbTzzxQysdDataSynService znsbTzzxQysdDataSynService;

    @Resource
    private ZnsbTzzxDlzgDataSynService znsbTzzxDlzgDataSynService;

    @Resource
    private CwbbBsService cwbbBsService;

    @PostMapping("/v1/queryNsrzInfo")
    @Operation(summary = "查询纳税人权限代码")
    public CommonResult<Map<String, Object>> queryNsrzInfo(@RequestBody Map<String, Object> nsrInfo) {
        log.info("查询纳税人权限代码入参{}:", JsonUtils.toJson(nsrInfo));
        Map<String, Object> resp = znsbTzzxQysdsyjtzService.queryNsrzInfo(nsrInfo);
        log.info("查询纳税人权限代码出参{}:", JsonUtils.toJson(resp));
        if (resp == null) {
            return CommonResult.error("未能查询到有效的纳税人权限信息。");
        }
        return success(resp);
    }

    @PostMapping("/v1/queryYjtz")
    @Operation(summary = "查询预缴台账")
    public CommonResult<PageRecordsVO> queryYjtz(@RequestBody QueryYjtzReqVO queryYjtzReqVO) {
        log.info("查询预缴台账入参{}:", JsonUtils.toJson(queryYjtzReqVO));
        PageRecordsVO resp = znsbTzzxQysdsyjtzService.queryYjtz(queryYjtzReqVO);
        log.info("查询预缴台账出参{}:", JsonUtils.toJson(resp));
        return success(resp);
    }

    @PostMapping("/v1/queryYjtzNew")
    @Operation(summary = "查询预缴台账（新）")
    public CommonResult<PageRecordsVO> queryYjtzNew(@RequestBody QueryYjtzReqVO queryYjtzReqVO) {
        log.info("查询预缴台账（新）入参{}:", JsonUtils.toJson(queryYjtzReqVO));
        PageRecordsVO resp = znsbTzzxQysdsyjtzService.queryYjtzNew(queryYjtzReqVO);
        log.info("查询预缴台账（新）出参{}:", JsonUtils.toJson(resp));
        return success(resp);
    }

    @PostMapping("/v1/updateYjtz")
    @Operation(summary = "修改预缴台账")
    public CommonResult<Object> updateYjtz(@RequestBody QueryYjtzReqVO queryYjtzReqVO) {
        znsbTzzxQysdsyjtzService.updateYjtz(queryYjtzReqVO);
        return success(null);
    }

    @PostMapping("/v1/updateYjtzNew")
    @Operation(summary = "修改预缴台账")
    public CommonResult<Object> updateYjtzNew(@RequestBody ZnsbTzzxQysdsyjtzItemVO itemVO) {
        znsbTzzxQysdsyjtzService.updateYjtzNew(itemVO);
        return success(null);
    }

    @PostMapping("/v1/queryFzjgfztz")
    @Operation(summary = "查询分支机构辅助台账")
    public CommonResult<PageRecordsVO> queryFzjgfztz(@RequestBody QueryYjtzReqVO queryYjtzReqVO) {
        log.info("查询分支机构辅助台账入参{}:", JsonUtils.toJson(queryYjtzReqVO));
        PageRecordsVO resp = znsbTzzxQysdsyjtzService.queryFzjgfztz(queryYjtzReqVO);
        log.info("查询分支机构辅助台账出参{}:", JsonUtils.toJson(resp));
        return success(resp);
    }

    @PostMapping("/v1/queryFzjgfztzHj")
    @Operation(summary = "查询分支机构辅助台账合计")
    public CommonResult<ZnsbTzzxFzjgfztzDO> queryFzjgfztzHj(@RequestBody QueryYjtzReqVO queryYjtzReqVO) {
        log.info("查询分支机构辅助台账合计入参{}:", JsonUtils.toJson(queryYjtzReqVO));
        ZnsbTzzxFzjgfztzDO resp = znsbTzzxQysdsyjtzService.queryFzjgfztzHj(queryYjtzReqVO);
        log.info("查询分支机构辅助台账合计出参{}:", JsonUtils.toJson(resp));
        return success(resp);
    }

    @PostMapping("/v1/updateFzjgfztz")
    @Operation(summary = "修改分支机构辅助台账")
    public CommonResult<Object> updateFzjgfztz(@RequestBody UpdateFzjgfztzReqVO updateFzjgfztzReqVO) {
        znsbTzzxQysdsyjtzService.updateFzjgfztz(updateFzjgfztzReqVO);
        return success(null);
    }

    @PostMapping("/v1/yjtzTqsj")
    @Operation(summary = "预缴台账手动触发提取bpc数据")
    public CommonResult<String> yjtzTqsj(@RequestBody ExtractDataReqVO reqVO) throws JCoException {
        final String ywlx = reqVO.getYwlx();

        if ("qysdsyj".equals(ywlx)) {
            Boolean hasKey = stringRedisTemplate.hasKey(formatKey(ywlx + reqVO.getGsh()));
            Boolean hasDsrwKey = stringRedisTemplate.hasKey(String.format("gjss:job:%s", "zzs:syncYjtzJob"));

            //判断是否有正在运行的数据抽取(包含预缴台账数据获取定时任务)
            if (hasKey || hasDsrwKey) {
                throw ServiceExceptionUtil.exception(TzzxErrorCodeConstants.EXIST_RUNNING_MISSION);
            } else {
                znsbTzzxQysdsyjtzService.yjtzTqsjAsync(reqVO.getGsh(), GyUtils.isNull(reqVO.getSszq())?null:Integer.toString(reqVO.getSszq()));
            }
        } else if ("fzjgfztz".equals(ywlx)) {
            Boolean hasKey = stringRedisTemplate.hasKey(formatKey(ywlx + reqVO.getNsrsbh()));

            //判断是否有正在运行的数据抽取
            if (hasKey) {
                throw ServiceExceptionUtil.exception(TzzxErrorCodeConstants.EXIST_RUNNING_MISSION);
            } else {
                znsbTzzxQysdsyjtzService.SyncFzjgfztzSxysAsyn(reqVO.getNsrsbh(), "");
            }
        }

        return success("Y");
    }

    @PostMapping("/v1/jyssReadyStatus")
    public CommonResult<String> jyssReadyStatus(@RequestBody ExtractDataReqVO req){
        return znsbTzzxQysdsyjtzService.jyssReadyStatus(req.getGsh(), req.getNsrsbh(), req.getYwlx());
    }

    @PostMapping("/v1/checkFzjgfztzKtb")
    @Operation(summary = "分支机构辅助台账检查是否有可同步数据")
    public CommonResult<String> checkFzjgfztzKtb(@RequestBody QueryYjtzReqVO queryYjtzReqVO) {
        return success(znsbTzzxQysdsyjtzService.checkFzjgfztzKtb(queryYjtzReqVO));
    }


    @PostMapping("/v1/test")
    @Operation(summary = "测试")
    public CommonResult<String> test(@RequestBody QueryYjtzReqVO queryYjtzReqVO) {
        String reuslt = "";
        if("1".equals(queryYjtzReqVO.getUuid())){
            reuslt = znsbTzzxQysdDataSynService.synZclrb(queryYjtzReqVO.getNsrsbh(), queryYjtzReqVO.getSszqq(),1);
        }else if("2".equals(queryYjtzReqVO.getUuid())){
            reuslt = znsbTzzxQysdDataSynService.synZcfzb(queryYjtzReqVO.getNsrsbh(), queryYjtzReqVO.getSszqq(),1);
        }else if("3".equals(queryYjtzReqVO.getUuid())){
            znsbTzzxQysdDataSynService.synQysdsyj(queryYjtzReqVO.getNsrsbh(), queryYjtzReqVO.getSszqq());
        }else if("4".equals(queryYjtzReqVO.getUuid())){
            znsbTzzxQysdDataSynService.syncyry(queryYjtzReqVO.getNsrsbh(), queryYjtzReqVO.getSszqq());
        }
        return CommonResult.success(reuslt);
    }

    @PostMapping("/v1/testDto")
    @Operation(summary = "请求参数测试")
    public CommonResult<String> testDto(@RequestBody TygjDataRespDTO respDTO) {
        List<Map<String, String>> dataList = respDTO.getData().getDataList();
        String gsh = dataList.get(0).get("entity");
        String result = "成功";
        if("1000001003".equals(gsh)){
            znsbTzzxQysdDataSynService.testSynZcfzb(respDTO.getData());
        }else if("1000001004".equals(gsh)){
            znsbTzzxQysdDataSynService.testSynZclrb(respDTO.getData());
        }else if("3".equals(respDTO.getCode())){
            result = znsbTzzxDlzgDataSynService.getLrbxx(null,respDTO.getMsg());
        }else if("4".equals(respDTO.getCode())){
            result = znsbTzzxDlzgDataSynService.getZcfzbxx(null,respDTO.getMsg());
        }else if("5".equals(respDTO.getCode())){
            result =znsbTzzxDlzgDataSynService.getXjlbxx(null,respDTO.getMsg());
        }else if("6".equals(respDTO.getCode())){
        }
        return CommonResult.success(result);
    }

    @PostMapping("/v1/testLrbxx")
    @Operation(summary = "测试大连重工接口")
    public CommonResult<String> testLrbxx(@RequestBody CwbbReqDTO cwbbreqdto) {
        String result = znsbTzzxDlzgDataSynService.testLrbxx(cwbbreqdto);
        return CommonResult.success(result);
    }

    @PostMapping("/v1/testZcfzbxx")
    @Operation(summary = "测试大连重工接口")
    public CommonResult<String> testZcfzbxx(@RequestBody CwbbReqDTO cwbbreqdto) {
        String result = znsbTzzxDlzgDataSynService.testZcfzbxx(cwbbreqdto);
        return CommonResult.success(result);
    }


    @PostMapping("/v1/testXjlbxx")
    @Operation(summary = "测试大连重工接口")
    public CommonResult<String> testXjlbxx(@RequestBody CwbbReqDTO cwbbreqdto) {
        String result = znsbTzzxDlzgDataSynService.testXjlbxx(cwbbreqdto);
        return CommonResult.success(result);
    }

    @PostMapping("/v1/syncZgxcmxtz")
    @Operation(summary = "同步职工薪酬明细台账")
    public CommonResult<Boolean> syncZgxcmxtz(@RequestParam String sszq) {
        if (StringUtils.isNotEmpty(sszq) && (sszq.length() != 6 || !sszq.matches("\\d{6}"))) {
            throw new RuntimeException("参数 sszq 必须为六位数字，如：202501");
        }
        znsbTzzxQysdsyjtzService.syncZgxcmxtz(sszq);
        return CommonResult.success(true);
    }

    @PostMapping("/v1/queryZgxcmxtz")
    @Operation(summary = "查询职工薪酬明细台账")
    public CommonResult<PageRecordsVO> queryZgxcmxtz(@RequestBody QueryYjtzReqVO queryYjtzReqVO) {
        log.info("查询职工薪酬明细台账入参{}:", JsonUtils.toJson(queryYjtzReqVO));
        PageRecordsVO resp = znsbTzzxQysdsyjtzService.queryZgxcmxtz(queryYjtzReqVO);
        log.info("查询职工薪酬明细台账出参{}:", JsonUtils.toJson(resp));
        return success(resp);
    }

    @PostMapping("/v1/updateZgxcmxtz")
    @Operation(summary = "修改职工薪酬明细台账")
    public CommonResult<Object> updateZgxcmxtz(@RequestBody ZnsbTzzxQysdsyjtzItemVO itemVO) {
        znsbTzzxQysdsyjtzService.updateZgxcmxtz(itemVO);
        return success(null);
    }



    private String formatKey(String key){
        return String.format("tzzx:qysdsyjdatasync:extract:%s",key);
    }
}
