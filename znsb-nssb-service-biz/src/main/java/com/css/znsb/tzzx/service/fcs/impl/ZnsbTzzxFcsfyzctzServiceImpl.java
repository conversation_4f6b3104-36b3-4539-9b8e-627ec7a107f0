package com.css.znsb.tzzx.service.fcs.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.common.pojo.PageResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.gjss.mapper.fcs.ZnsbTzzxFcsfyzctzMapper;
import com.css.znsb.gjss.pojo.domain.yhs.httz.*;
import com.css.znsb.gjss.pojo.domain.yhs.httz.fcs.ZnsbTzzxFcsfyzctzDO;
import com.css.znsb.tzzx.constants.FcsTzConstans;
import com.css.znsb.tzzx.pojo.dto.fcs.FcsfyzctzExcelDTO;
import com.css.znsb.tzzx.pojo.dto.fcs.ZnsbTzzxFcsfyzctzQueryParamDTO;
import com.css.znsb.tzzx.service.fcs.ZnsbTzzxFcsfyzctzService;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guorui
 * @Date: 2025/9/1 16:06
 * @Description: 房源资产台账service实现类
 **/

@Service
@Slf4j
public class ZnsbTzzxFcsfyzctzServiceImpl extends ServiceImpl<ZnsbTzzxFcsfyzctzMapper, ZnsbTzzxFcsfyzctzDO> implements ZnsbTzzxFcsfyzctzService {

    @Resource
    private ZnsbTzzxFcsfyzctzMapper znsbTzzxFcsfyzctzMapper;

    @Override
    public PageResult<ZnsbTzzxFcsfyzctzDO> queryFyzctzByCondition(ZnsbTzzxFcsfyzctzQueryParamDTO paramDTO) {
        return znsbTzzxFcsfyzctzMapper.queryFyzctzByCondition(paramDTO);
    }

    public void modifyFyzctz(ZnsbTzzxFcsfyzctzDO paramDTO) {
        // 校验分摊比例
        String checkFtbl = this.checkFtbl(paramDTO.getUuid(), paramDTO.getSybh1(), paramDTO.getZcbh(), paramDTO.getFtbl());
        if (StringUtils.isNotEmpty(checkFtbl)) {
            throw new RuntimeException(checkFtbl);
        }
        if (StringUtils.isEmpty(paramDTO.getUuid())) {
            paramDTO.setUuid(IdUtil.fastSimpleUUID());
            paramDTO.setYxbz("Y");
        }
        this.setDefaultValue(paramDTO);
        try {
            znsbTzzxFcsfyzctzMapper.insertOrUpdate(paramDTO);
        } catch (Exception e) {
            throw new RuntimeException("房源资产台账保存失败！", e);
        }
    }

    public void deleteFyzctz(List<String> uuidList) {
        LambdaQueryWrapperX<ZnsbTzzxFcsfyzctzDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(ZnsbTzzxFcsfyzctzDO::getUuid, uuidList);
        List<ZnsbTzzxFcsfyzctzDO> deleteList = znsbTzzxFcsfyzctzMapper.selectList(queryWrapper);
        deleteList.forEach(item -> {
            item.setYxbz("N");
            this.setDefaultValue(item);
        });
        try {
            znsbTzzxFcsfyzctzMapper.updateBatch(deleteList);
        } catch (Exception e) {
            throw new RuntimeException("删除房源资产台账报错：" + e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> uploadExcel(MultipartFile file) throws IOException {
        log.info("房源资产导入开始");
        final Map<String, Object> returnMap = new HashMap<>();

        //校验excel文件
        if (GyUtils.isNull(file)) {
            returnMap.put("code", "01");
            returnMap.put("msg", "请选择Excel文件");
            log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }

        //校验文件类型
        final String fullName = file.getOriginalFilename();
        final String fillType = fullName.substring(fullName.lastIndexOf(".") + 1);
        if (!"xls".equals(fillType) && !"xlsx".equals(fillType)) {
            returnMap.put("code", "01");
            returnMap.put("msg", "文件类型不正确");
            log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }

        ZnsbTzzxFcsfyzctzUploadListener listener = new ZnsbTzzxFcsfyzctzUploadListener();
        try {
            EasyExcel.read(file.getInputStream(), FcsfyzctzExcelDTO.class, listener)
                    .headRowNumber(1) // 表头在第2行（索引从0开始）
                    .sheet()
                    .doRead();
        } catch (IllegalArgumentException e) {
            returnMap.put("code", "01");
            returnMap.put("msg", "上传的Excel模板不正确，请检查表头字段和顺序");
            log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }
        final List<FcsfyzctzExcelDTO> excelList = listener.getDataList();

        //excel内容校验
        if (GyUtils.isNull(excelList) || excelList.isEmpty()) {
            //校验空表格
            returnMap.put("code", "01");
            returnMap.put("msg", "导入失败：导入文件内容为空");
            log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }

        List<ZnsbTzzxFcsfyzctzDO> importList = new ArrayList<>();
        // 按税源编号和资产编号分组校验分摊比例
        Map<String, List<FcsfyzctzExcelDTO>> groupMap = excelList.stream()
                .collect(Collectors.groupingBy(item -> item.getSybh1() + "_" + item.getZcbh()));
        for (String key : groupMap.keySet()) {
            String[] keyArr = key.split("_");
            if (StringUtils.isNotEmpty(keyArr[0]) && StringUtils.isNotEmpty(keyArr[1])) {
                String checkFtbl = this.checkFtbl(null, key.split("_")[0], key.split("_")[1],
                        excelList.stream()
                                .map(dto -> new BigDecimal(dto.getFtbl().replace("%", "")))
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO));
                if (StringUtils.isNotEmpty(checkFtbl)) {
                    returnMap.put("code", "01");
                    returnMap.put("msg", "导入失败：存在资产编号" + key + "的资产分摊比例之和超出100%");
                    log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                    return returnMap;
                }
            }
        }

        for (FcsfyzctzExcelDTO excel : excelList) {
            //获取行数据
            final String xh = excel.getXh();
            //必录数据校验
            if (GyUtils.isNull(excel.getSybh1())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，税源编号不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getSsdw())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，资产所属单位不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFcmc())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，房产名称不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getZcbh())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，资产编号不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getDz())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，房产地址不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getJd4())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，基地不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFcqdsj())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，房产取得时间不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFcyz())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，房产原值不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getSndzmj())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，总面积不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFtyj())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，分摊单位业务范围不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFtyjsl())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，分摊依据数量不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFtbl())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，分摊比例不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFtmj())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，分摊面积不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFtfcyzcj())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，分摊房产原值不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFtbnzjcz())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，分摊本年折旧不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFtfczjcz())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，分摊房产租金不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFttdyz())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，分摊土地原值不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getJsfs())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，计税方式不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getJsyj())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，计税依据不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getYjfcsnd())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，全年应交房产税不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getYjfcsjd())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，每季度应交房产税不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getYjfcsyd())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，每月应交房产税不能为空）");
                log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            // 校验房产取得时间格式必须为yyyy/MM/dd
            if (GyUtils.isNotNull(excel.getFcqdsj())) {
                try {
                    DateUtil.parse(excel.getFcqdsj(), "yyyy/MM/dd");
                } catch (Exception e) {
                    returnMap.put("code", "01");
                    returnMap.put("msg", "导入失败：日期格式不正确（序号" + xh + "，房产取得时间必须为yyyy/MM/dd格式）");
                    log.info("房源资产导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                    return returnMap;
                }
            }


            // 插入主表
            ZnsbTzzxFcsfyzctzDO dto = new ZnsbTzzxFcsfyzctzDO();
            // TODO 校验重复导入
            String uuid = IdUtil.fastSimpleUUID();
            dto.setUuid(uuid);
            dto.setDjxh(ZnsbSessionUtils.getDjxh());
            dto.setGsh(excel.getGsh());
            dto.setSybh1(excel.getSybh1());
            dto.setSsdw(excel.getSsdw());
            dto.setFcmc(excel.getFcmc());
            dto.setZcbh(excel.getZcbh());
            dto.setFwcqzsh(excel.getFwcqzsh());
            dto.setDz(excel.getDz());
            dto.setJd4(excel.getJd4());
            dto.setFcqdsj(DateUtil.parse(excel.getFcqdsj(), "yyyy/MM/dd"));
            dto.setFcyz(excel.getFcyz());
            dto.setSndtdyz(excel.getSndtdyz());
            dto.setBdyf(excel.getBdyf());
            dto.setSndzmj(excel.getSndzmj());
            dto.setZjje1(excel.getZjje1());
            dto.setZjsr1(excel.getZjsr1());
            dto.setFtdwmc(excel.getFtdwmc());
            dto.setFtyj(excel.getFtyj());
            dto.setFtyjsl(excel.getFtyjsl());
            dto.setFtbl(new BigDecimal(excel.getFtbl().replace("%", "")));
            dto.setFtmj(excel.getFtmj());
            dto.setFtfcyzcj(excel.getFtfcyzcj());
            dto.setFtbnzjcz(excel.getFtbnzjcz());
            dto.setFtfczjcz(excel.getFtfczjcz());
            dto.setFttdyz(excel.getFttdyz());
            dto.setYxbz("Y");
            if ("从价".equals(excel.getJsfs())) {
                dto.setJsfs(FcsTzConstans.CJDM);
            } else if ("从租".equals(excel.getJsfs())) {
                dto.setJsfs(FcsTzConstans.CZDM);
            }
            dto.setJsyj(excel.getJsyj());
            dto.setYjfcsnd(excel.getYjfcsnd());
            dto.setYjfcsjd(excel.getYjfcsjd());
            dto.setYjfcsyd(excel.getYjfcsyd());
            //录入固定字段
            this.setDefaultValue(dto);
            importList.add(dto);
        }

        try {
            znsbTzzxFcsfyzctzMapper.insertBatch(importList);
        } catch (Exception e) {
            throw new RuntimeException("数据插入异常，{}", e);
        }

        //写入返回信息
        returnMap.put("code", "00");
        returnMap.put("msg", "本次共导入" + importList.size() + "条记录");

        log.info("房源资产导入结束，导入成功，返回参数：{}", JsonUtils.toJson(returnMap));
        return returnMap;
    }

    private String checkFtbl(String uuid, String sybh, String zcbh, BigDecimal ftbl) {
        LambdaQueryWrapperX<ZnsbTzzxFcsfyzctzDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eqIfPresent(ZnsbTzzxFcsfyzctzDO::getSybh1, sybh)
                .eq(ZnsbTzzxFcsfyzctzDO::getZcbh, zcbh)
                .neIfPresent(ZnsbTzzxFcsfyzctzDO::getUuid, uuid)
                .eq(ZnsbTzzxFcsfyzctzDO::getYxbz, "Y");
        List<ZnsbTzzxFcsfyzctzDO> list = znsbTzzxFcsfyzctzMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            if (ftbl.add(list.stream().map(ZnsbTzzxFcsfyzctzDO::getFtbl).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .compareTo(new BigDecimal(100)) > 0) {
                return "该房源资产已存在，且分摊比例的和超过了100%，请检查";
            }
        }
        return null;
    }

    private void setDefaultValue(ZnsbTzzxFcsfyzctzDO fyzctzDO) {
        Date now = new Date();
        fyzctzDO.setLrrq(now);
        fyzctzDO.setXgrq(now);
        fyzctzDO.setYwqdDm("TZ_USER");
        fyzctzDO.setLrrsfid("ZNSB.TZZX");
        fyzctzDO.setXgrsfid("ZNSB.TZZX");
        fyzctzDO.setSjtbSj(now);
        fyzctzDO.setSjgsdq("TZ_USER");
        fyzctzDO.setSjcsdq("TZ_USER");
    }
}
