package com.css.znsb.tzzx.service.datareset.impl;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.mapper.kp.qd.DzfpKpywFpjcxxbMapper;
import com.css.znsb.gjss.mapper.kp.qd.DzfpKpywFpmxxxbMapper;
import com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxHtzzMapper;
import com.css.znsb.gjss.pojo.domain.kp.qd.DzfpKpywFpjcxxbDO;
import com.css.znsb.gjss.pojo.domain.kp.qd.DzfpKpywFpmxxxbDO;
import com.css.znsb.gjss.pojo.domain.yhs.httz.ZnsbTzzxHtzzDO;
import com.css.znsb.nssb.mapper.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxMapper;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbNssbCxsTdsyxxcjbDOMapper;
import com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhscjMapper;
import com.css.znsb.nssb.mapper.fcscztdsyssycj.SbCxsFtdcjglbMapper;
import com.css.znsb.tzzx.mapper.*;
import com.css.znsb.tzzx.mapper.resetjyss.ResetJyssMapper;
import com.css.znsb.tzzx.pojo.domain.jxsezc.ZnsbTzzxJxsezcmxz;
import com.css.znsb.tzzx.pojo.domain.jxsezc.ZnsbTzzxJxsezczz;
import com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxJmsmxbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxJmszzDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxLkysfwkspzmxzDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxQtkspzzzDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.jxfp.ZnsbTzzxJxfphwfwmxbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.jxfp.ZnsbTzzxJxfpmxbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.jxfp.ZnsbTzzxJxfpzzbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrmxbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrzzbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.xxfp.ZnsbTzzxXxfphwfwmxbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.xxfp.ZnsbTzzxXxfpmxbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.xxfp.ZnsbTzzxXxfpzzbDO;
import com.css.znsb.tzzx.service.datareset.JxfpResetDTO;
import com.css.znsb.tzzx.service.datareset.TzzxYssjResetService;
import com.css.znsb.tzzx.service.datareset.XxfpResetDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Slf4j
@Service
public class TzzxYssjResetServiceImpl implements TzzxYssjResetService {
    public static final SecureRandom RANDOM = new SecureRandom();
    @Resource
    ZnsbTzzxXxfpzzbMapper znsbTzzxXxfpzzbMapper;
    @Resource
    ZnsbTzzxXxfpmxbMapper znsbTzzxXxfpmxbMapper;
    @Resource
    ZnsbTzzxXxfphwfwmxbMapper znsbTzzxXxfphwfwmxbMapper;
    @Resource
    ZnsbTzzxSrzzbMapper znsbTzzxSrzzbMapper;
    @Resource
    ZnsbTzzxSrzzmxbMapper znsbTzzxSrzzmxbMapper;
    @Resource
    ZnsbTzzxJxfpzzbMapper znsbTzzxJxfpzzbMapper;
    @Resource
    ZnsbTzzxJxfpmxbMapper znsbTzzxJxfpmxbMapper;
    @Resource
    ZnsbTzzxJxfphwfwmxbMapper znsbTzzxJxfphwfwmxbMapper;
    @Resource
    ZnsbTzzxQtkspzzzMapper znsbTzzxQtkspzzzMapper;
    @Resource
    ZnsbTzzxLkysfwkspzmxzMapper znsbTzzxLkysfwkspzmxzMapper;
    @Resource
    ZnsbTzzxJxsezczzMapper znsbTzzxJxsezczzMapper;
    @Resource
    ZnsbTzzxJxsezcmxzMapper znsbTzzxJxsezcmxzMapper;
    @Resource
    ZnsbTzzxJmszzMapper znsbTzzxJmszzMapper;
    @Resource
    ZnsbTzzxJmsmxbMapper znsbTzzxJmsmxbMapper;
    @Resource
    ZnsbTzzxHtzzMapper znsbTzzxHtzzMapper;
    @Resource
    ZnsbNssbCxsbtxxMapper znsbNssbCxsbtxxMapper;
    @Resource
    ZnsbNssbYhscjMapper znsbNssbYhscjMapper;
    @Resource
    SbCxsFtdcjglbMapper sbCxsFtdcjglbMapper;
    @Resource
    ZnsbNssbCxsTdsyxxcjbDOMapper znsbNssbCxsTdsyxxcjbDOMapper;
    @Resource
    private ResetJyssMapper resetJyssMapper;
    @Resource
    DzfpKpywFpjcxxbMapper dzfpKpywFpjcxxbMapper;
    @Resource
    DzfpKpywFpmxxxbMapper dzfpKpywFpmxxxbMapper;

//    public void demo(){
//        ClassPathResource resource = new ClassPathResource("/mock/yshjsjcz/xxfp.json");
//        XxfpResetDTO xxfpResetDTO = JSONUtil.toBean(resource.readUtf8Str(), XxfpResetDTO.class);
//        List<DzfpKpywFpjcxxbDO> dzfpKpywFpjcxxb = xxfpResetDTO.getDZFP_KPYW_FPJCXXB();
//        List<DzfpKpywFpmxxxbDO> dzfpKpywFpmxxxb = xxfpResetDTO.getDZFP_KPYW_FPMXXXB();
//        List<ZnsbTzzxXxfpmxbDO> znsbTzzxXxfpmxb = xxfpResetDTO.getZnsb_tzzx_xxfpmxb();
//        List<ZnsbTzzxXxfphwfwmxbDO> znsbTzzxXxfphwfwmxb = xxfpResetDTO.getZnsb_tzzx_xxfphwfwmxb();
//
//        ClassPathResource resource2 = new ClassPathResource("/mock/yshjsjcz/jxfp.json");
//        JxfpResetDTO jxfpResetDTO = JSONUtil.toBean(resource2.readUtf8Str(), JxfpResetDTO.class);
//        List<ZnsbTzzxJxfpmxbDO> znsbTzzxJxfpmxb = jxfpResetDTO.getZnsb_tzzx_jxfpmxb();
//        List<ZnsbTzzxJxfphwfwmxbDO> znsbTzzxJxfphwfwmxb = jxfpResetDTO.getZnsb_tzzx_jxfphwfwmxb();
//    }

    public void resetXxfp(List<Map<String, Object>> jgxxList){
        ClassPathResource resource = new ClassPathResource("/mock/yshjsjcz/xxfp.json");
        XxfpResetDTO xxfpResetDTO = JSONUtil.toBean(resource.readUtf8Str(), XxfpResetDTO.class);
        List<DzfpKpywFpjcxxbDO> dzfpKpywFpjcxxbDOList = xxfpResetDTO.getDZFP_KPYW_FPJCXXB();
        for(DzfpKpywFpjcxxbDO dzfpKpywFpjcxxbDO: dzfpKpywFpjcxxbDOList){
            QueryWrapper<DzfpKpywFpjcxxbDO> qw = new QueryWrapper<>();
            qw.eq("fphm", dzfpKpywFpjcxxbDO.getFphm());
            dzfpKpywFpjcxxbDO.setKprq(transformKprqToDqyfInDateType(dzfpKpywFpjcxxbDO.getKprq()));
            dzfpKpywFpjcxxbMapper.delete(qw);
            dzfpKpywFpjcxxbMapper.insert(dzfpKpywFpjcxxbDO);
        }
        List<DzfpKpywFpmxxxbDO> dzfpKpywFpmxxxbDOList = xxfpResetDTO.getDZFP_KPYW_FPMXXXB();
        for(DzfpKpywFpmxxxbDO dzfpKpywFpmxxxbDO: dzfpKpywFpmxxxbDOList){
            QueryWrapper<DzfpKpywFpmxxxbDO> qw = new QueryWrapper<>();
            qw.eq("fphm", dzfpKpywFpmxxxbDO.getFphm());
            dzfpKpywFpmxxxbDO.setKprq(transformKprqToDqyfInDateType(dzfpKpywFpmxxxbDO.getKprq()));
            dzfpKpywFpmxxxbMapper.delete(qw);
            dzfpKpywFpmxxxbMapper.insert(dzfpKpywFpmxxxbDO);
        }
        for(Map<String, Object> jgxx: jgxxList){
            // 销项发票明细
            QueryWrapper<ZnsbTzzxXxfpmxbDO> qw1 = new QueryWrapper<>();
            qw1.eq("djxh", jgxx.get("djxh"));
            qw1.eq("sszq", Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            qw1.eq("nsrsbh", jgxx.get("nsrsbh"));
            qw1.eq("nsrmc", jgxx.get("nsrmc"));
            znsbTzzxXxfpmxbMapper.delete(qw1);
            List<ZnsbTzzxXxfpmxbDO> znsbTzzxXxfpmxbDOs = xxfpResetDTO.getZnsb_tzzx_xxfpmxb();
            for(ZnsbTzzxXxfpmxbDO znsbTzzxXxfpmxbDO: znsbTzzxXxfpmxbDOs){
                znsbTzzxXxfpmxbDO.setUuid(GyUtils.getUuid());
                znsbTzzxXxfpmxbDO.setDjxh((String) jgxx.get("djxh"));
                znsbTzzxXxfpmxbDO.setGsh((String) jgxx.get("gsh"));
                znsbTzzxXxfpmxbDO.setNsrsbh((String) jgxx.get("nsrsbh"));
                znsbTzzxXxfpmxbDO.setNsrmc((String) jgxx.get("nsrmc"));
                znsbTzzxXxfpmxbDO.setLrrq(LocalDateTime.now());
                znsbTzzxXxfpmxbDO.setXgrq(LocalDateTime.now());
                znsbTzzxXxfpmxbDO.setSjtbSj(LocalDateTime.now());
                znsbTzzxXxfpmxbDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
                znsbTzzxXxfpmxbDO.setKprq(transformKprqToDqyf(znsbTzzxXxfpmxbDO.getKprq()));
                znsbTzzxXxfpmxbMapper.insert(znsbTzzxXxfpmxbDO);
            }
            // 销项发票货物服务明细
            QueryWrapper<ZnsbTzzxXxfphwfwmxbDO> qw2 = new QueryWrapper<>();
            qw2.eq("djxh", jgxx.get("djxh"));
            qw2.eq("sszq", Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            qw2.eq("nsrsbh", jgxx.get("nsrsbh"));
            qw2.eq("nsrmc", jgxx.get("nsrmc"));
            znsbTzzxXxfphwfwmxbMapper.delete(qw2);
            List<ZnsbTzzxXxfphwfwmxbDO> znsbTzzxXxfphwfwmxbDOs = xxfpResetDTO.getZnsb_tzzx_xxfphwfwmxb();
            for(ZnsbTzzxXxfphwfwmxbDO tzzxXxfphwfwmxbDO: znsbTzzxXxfphwfwmxbDOs){
                tzzxXxfphwfwmxbDO.setUuid(GyUtils.getUuid());
                tzzxXxfphwfwmxbDO.setDjxh((String) jgxx.get("djxh"));
                tzzxXxfphwfwmxbDO.setGsh((String) jgxx.get("gsh"));
                tzzxXxfphwfwmxbDO.setNsrsbh((String) jgxx.get("nsrsbh"));
                tzzxXxfphwfwmxbDO.setNsrmc((String) jgxx.get("nsrmc"));
                tzzxXxfphwfwmxbDO.setJsfsDm("01");
                tzzxXxfphwfwmxbDO.setSl(new BigDecimal("0.130000"));
                tzzxXxfphwfwmxbDO.setLrrq(LocalDateTime.now());
                tzzxXxfphwfwmxbDO.setXgrq(LocalDateTime.now());
                tzzxXxfphwfwmxbDO.setSjtbSj(LocalDateTime.now());
                tzzxXxfphwfwmxbDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
                tzzxXxfphwfwmxbDO.setKprq(transformKprqToDqyf(tzzxXxfphwfwmxbDO.getKprq()));
                znsbTzzxXxfphwfwmxbMapper.insert(tzzxXxfphwfwmxbDO);
            }
        }

    }
    public void resetJxfp(List<Map<String, Object>> jgxxList){
        ClassPathResource resource2 = new ClassPathResource("/mock/yshjsjcz/jxfp.json");
        JxfpResetDTO jxfpResetDTO = JSONUtil.toBean(resource2.readUtf8Str(), JxfpResetDTO.class);
        for(Map<String, Object> jgxx: jgxxList){
            // 进项发票明细
            QueryWrapper<ZnsbTzzxJxfpmxbDO> qw1 = new QueryWrapper<>();
            qw1.eq("djxh", jgxx.get("djxh"));
            qw1.eq("sszq", Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            qw1.eq("nsrsbh", jgxx.get("nsrsbh"));
            qw1.eq("nsrmc", jgxx.get("nsrmc"));
            znsbTzzxJxfpmxbMapper.delete(qw1);
            List<ZnsbTzzxJxfpmxbDO> znsbTzzxJxfpmxbDOs = jxfpResetDTO.getZnsb_tzzx_jxfpmxb();
            for(ZnsbTzzxJxfpmxbDO znsbTzzxJxfpmxbDO: znsbTzzxJxfpmxbDOs){
                znsbTzzxJxfpmxbDO.setUuid(GyUtils.getUuid());
                znsbTzzxJxfpmxbDO.setDjxh((String) jgxx.get("djxh"));
                znsbTzzxJxfpmxbDO.setNsrsbh((String) jgxx.get("nsrsbh"));
                znsbTzzxJxfpmxbDO.setNsrmc((String) jgxx.get("nsrmc"));
                znsbTzzxJxfpmxbDO.setLrrq(LocalDateTime.now());
                znsbTzzxJxfpmxbDO.setXgrq(LocalDateTime.now());
                znsbTzzxJxfpmxbDO.setSjtbSj(LocalDateTime.now());
                znsbTzzxJxfpmxbDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
                znsbTzzxJxfpmxbDO.setSkssq(this.getSszqByDqny(LocalDate.now()));
                znsbTzzxJxfpmxbDO.setKprq(transformKprqToDqyf(znsbTzzxJxfpmxbDO.getKprq()));
                znsbTzzxJxfpmxbMapper.insert(znsbTzzxJxfpmxbDO);
            }
            // 进项发票货物服务明细
            QueryWrapper<ZnsbTzzxJxfphwfwmxbDO> qw2 = new QueryWrapper<>();
            qw2.eq("djxh", jgxx.get("djxh"));
            qw2.eq("sszq", Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            qw2.eq("nsrsbh", jgxx.get("nsrsbh"));
            qw2.eq("nsrmc", jgxx.get("nsrmc"));
            znsbTzzxJxfphwfwmxbMapper.delete(qw2);
            List<ZnsbTzzxJxfphwfwmxbDO> znsbTzzxJxfphwfwmxbDOs = jxfpResetDTO.getZnsb_tzzx_jxfphwfwmxb();
            for(ZnsbTzzxJxfphwfwmxbDO tzzxJxfphwfwmxbDO: znsbTzzxJxfphwfwmxbDOs){
                tzzxJxfphwfwmxbDO.setUuid(GyUtils.getUuid());
                tzzxJxfphwfwmxbDO.setDjxh((String) jgxx.get("djxh"));
                tzzxJxfphwfwmxbDO.setNsrsbh((String) jgxx.get("nsrsbh"));
                tzzxJxfphwfwmxbDO.setNsrmc((String) jgxx.get("nsrmc"));
                tzzxJxfphwfwmxbDO.setLrrq(LocalDateTime.now());
                tzzxJxfphwfwmxbDO.setXgrq(LocalDateTime.now());
                tzzxJxfphwfwmxbDO.setSjtbSj(LocalDateTime.now());
                tzzxJxfphwfwmxbDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
                tzzxJxfphwfwmxbDO.setKprq(transformKprqToDqyf(tzzxJxfphwfwmxbDO.getKprq()));
                znsbTzzxJxfphwfwmxbMapper.insert(tzzxJxfphwfwmxbDO);
            }
        }
    }

    @Override
    public boolean tzsjReset() {
        boolean flag= true;
        final List<Map<String, Object>> jgxxList = this.setJgxxList();
        // 本地测试暂时注掉
//        this.deleteJyssData(jgxxList);
        try{
            this.deleteTzzzData(jgxxList);
            this.deleteJyssData(jgxxList);
            this.resetCztdsy();
            this.deleteYhssyYssj(jgxxList);
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }
        try{
            this.resetZnsbTzzxHttz(this.setYssjDatasZnsbTzzxHtzzDO(jgxxList));
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }
        try{
            this.resetXxfp(jgxxList);
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }
        try{
            this.resetZnsbTzzxSrzzmxb(this.setYssjDatasZnsbTzzxSrmxbDO(jgxxList));
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }

        try{
            this.resetJxfp(jgxxList);
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }
      /*  try{
            this.resetZnsbTzzxQtkspzzz(this.setYssjDatasZnsbTzzxQtkspzzzDO(jgxxList));
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }*/
        try{
            this.resetZnsbTzzxLkysfwkspzmxz(this.setYssjDatasZnsbTzzxLkysfwkspzmxzDO(jgxxList));
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }
        try{
            this.resetZnsbTzzxJxsezcmxz(this.setYssjDatasJxsezcmxzDO(jgxxList));
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }
        try{
            this.resetZnsbTzzxJmsmxb(this.setYssjDatasZnsbTzzxJmsmxbDO(jgxxList));
        }catch (Exception e){
            log.error("",e);
            flag = false;
        }
        return flag;
    }
    //构建机构信息
    public List<Map<String, Object>> setJgxxList() {
        List<Map<String, Object>> jgxxList = new ArrayList<>();
        //暂时写死 应该从表中获取
        Map<String, Object> ysjg1 = new HashMap<>();
        ysjg1.put("djxh", "10111525000001030001");
        ysjg1.put("gsh", "1100");
        ysjg1.put("nsrsbh", "91310000MA1FL70BCS");
        ysjg1.put("nsrmc", "演示集团北京分公司");
        ysjg1.put("lrzx", "P000000001");
        jgxxList.add(ysjg1);
        Map<String, Object> ysjg2 = new HashMap<>();
        ysjg2.put("djxh", "10111525000001030002");
        ysjg2.put("gsh", "1100");
        ysjg2.put("nsrsbh", "9144030070846113CS");
        ysjg2.put("nsrmc", "演示集团四川分公司");
        ysjg2.put("lrzx", "P000000001");
        jgxxList.add(ysjg2);
        Map<String, Object> ysjg3 = new HashMap<>();
        ysjg3.put("djxh", "10111525000001030003");
        ysjg3.put("gsh", "1100");
        ysjg3.put("nsrsbh", "9111000071093573CS");
        ysjg3.put("nsrmc", "演示集团上海分公司");
        ysjg3.put("lrzx", "P000000001");
        jgxxList.add(ysjg3);
        Map<String, Object> ysjg4 = new HashMap<>();
        ysjg4.put("djxh", "10111525000001030004");
        ysjg4.put("gsh", "1100");
        ysjg4.put("nsrsbh", "9111000010001770CS");
        ysjg4.put("nsrmc", "演示集团浙江分公司");
        ysjg4.put("lrzx", "P000000001");
        jgxxList.add(ysjg4);
        return jgxxList;
    }
    //删除交易算税信息
    public void deleteJyssData(List<Map<String, Object>> jgxxList) {
        for(Map<String, Object> jgxx : jgxxList) {
            try{
                resetJyssMapper.deleteSsjgZzs((String) jgxx.get("djxh"), (String) jgxx.get("nsrsbh"));
                resetJyssMapper.deleteSsgcZzs((String) jgxx.get("djxh"), (String) jgxx.get("nsrsbh"));
                resetJyssMapper.deleteSsJgGgzc((String) jgxx.get("djxh"), (String) jgxx.get("nsrsbh"));
            }catch (Exception e){
                log.error("",e);
            }
        }
    }
    //删除台账总账数据
    public void deleteTzzzData(List<Map<String, Object>> jgxxList) {
        for(Map<String, Object> jgxx : jgxxList) {
            QueryWrapper qw = new QueryWrapper();
            qw.eq("djxh", jgxx.get("djxh"));
            qw.eq("sszq", Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            qw.eq("nsrsbh", jgxx.get("nsrsbh"));
            qw.eq("nsrmc", jgxx.get("nsrmc"));
            znsbTzzxXxfpzzbMapper.delete(qw);
            znsbTzzxSrzzbMapper.delete(qw);
            znsbTzzxJxfpzzbMapper.delete(qw);
            znsbTzzxQtkspzzzMapper.delete(qw);
            znsbTzzxJxsezczzMapper.delete(qw);
            znsbTzzxJmszzMapper.delete(qw);
        }
    }
    //销项总账
    public List<ZnsbTzzxXxfpzzbDO> setYssjDatasZnsbTzzxXxfpzzbDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxXxfpzzbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxXxfpzzbDO dataDO = new ZnsbTzzxXxfpzzbDO();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setTzlxDm("1");
            dataDO.setYxbz("Y");
            dataDO.setLrrq(LocalDateTime.now());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(LocalDateTime.now());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(LocalDateTime.now());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setDjxh((String) jgxx.get("djxh"));
            dataDO.setGsh((String) jgxx.get("gsh"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO.setJsfsDm("01");
            dataDO.setZsxmDm("02");
            dataDO.setSl(new BigDecimal("0.170000"));
            dataDO.setZyfpje(new BigDecimal("300.00"));
            dataDO.setZyfpse(new BigDecimal("51.00"));
            dataDO.setWkpje(new BigDecimal("0.00"));
            dataDO.setWkpse(new BigDecimal("0.00"));
            dataDO.setQtfpje(new BigDecimal("0.00"));
            dataDO.setQtfpse(new BigDecimal("0.00"));
            dataDO.setNsjctzje(new BigDecimal("0.00"));
            dataDO.setNsjctzse(new BigDecimal("0.00"));
            dataDOs.add(dataDO);

            ZnsbTzzxXxfpzzbDO dataDO2 = new ZnsbTzzxXxfpzzbDO();
            dataDO2.setUuid(GyUtils.getUuid());
            dataDO2.setTzlxDm("1");
            dataDO2.setYxbz("Y");
            dataDO2.setLrrq(LocalDateTime.now());
            dataDO2.setLrrsfid("yszh000001");
            dataDO2.setXgrq(LocalDateTime.now());
            dataDO2.setXgrsfid("yszh000001");
            dataDO2.setSjtbSj(LocalDateTime.now());
            dataDO2.setSjcsdq("00000000000");
            dataDO2.setSjgsdq("00000000000");
            dataDO2.setYwqdDm("TZ_USER");
            dataDO2.setDjxh((String) jgxx.get("djxh"));
            dataDO2.setGsh((String) jgxx.get("gsh"));
            dataDO2.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO2.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO2.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO2.setJsfsDm("02");
            dataDO2.setZsxmDm("01");
            dataDO2.setSl(new BigDecimal("0.130000"));
            dataDO2.setZyfpje(new BigDecimal("7000.00"));
            dataDO2.setZyfpse(new BigDecimal("910.00"));
            dataDO2.setWkpje(new BigDecimal("0.00"));
            dataDO2.setWkpse(new BigDecimal("0.00"));
            dataDO2.setQtfpje(new BigDecimal("0.00"));
            dataDO2.setQtfpse(new BigDecimal("0.00"));
            dataDO2.setNsjctzje(new BigDecimal("0.00"));
            dataDO2.setNsjctzse(new BigDecimal("0.00"));
            dataDOs.add(dataDO2);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxXxfpzzb(List<ZnsbTzzxXxfpzzbDO> dataDOs) {
        for(ZnsbTzzxXxfpzzbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxXxfpzzbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxXxfpzzbMapper.delete(qw);
            znsbTzzxXxfpzzbMapper.insert(dataDO);
        }
    }
    //销项发票明细
    public List<ZnsbTzzxXxfpmxbDO> setYssjDatasZnsbTzzxXxfpmxbDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxXxfpmxbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxXxfpmxbDO dataDO = this.makeXxfpmxbYssjDO(jgxx, "001", "24317000000282240064", "11675.72", "1517.840000", "13193.56");
            dataDOs.add(dataDO);
            ZnsbTzzxXxfpmxbDO dataDO1 = this.makeXxfpmxbYssjDO(jgxx, "002", "24317000000000730515", "634.61", "82.490000", "717.10");
            dataDOs.add(dataDO1);
            ZnsbTzzxXxfpmxbDO dataDO2 = this.makeXxfpmxbYssjDO(jgxx, "003", "24317000000000730272", "876.11", "113.890000", "990.00");
            dataDOs.add(dataDO2);
            ZnsbTzzxXxfpmxbDO dataDO3 = this.makeXxfpmxbYssjDO(jgxx, "004", "24317000000000731445", "655.22", "85.180000", "740.40");
            dataDOs.add(dataDO3);
            ZnsbTzzxXxfpmxbDO dataDO4 = this.makeXxfpmxbYssjDO(jgxx, "005", "24317000000000731099", "20979.34", "2727.330000", "23706.67");
            dataDOs.add(dataDO4);
            ZnsbTzzxXxfpmxbDO dataDO5 = this.makeXxfpmxbYssjDO(jgxx, "006", "24317000000000730315", "804.23", "104.550000", "908.78");
            dataDOs.add(dataDO5);
            ZnsbTzzxXxfpmxbDO dataDO6 = this.makeXxfpmxbYssjDO(jgxx, "007", "24317000000000729964", "726.37", "94.430000", "820.80");
            dataDOs.add(dataDO6);
            ZnsbTzzxXxfpmxbDO dataDO7 = this.makeXxfpmxbYssjDO(jgxx, "008", "24317000000282239827", "590.80", "76.800000", "667.60");
            dataDOs.add(dataDO7);
            ZnsbTzzxXxfpmxbDO dataDO8 = this.makeXxfpmxbYssjDO(jgxx, "009", "24317000000000731457", "70531.93", "9169.150000", "79701.08");
            dataDOs.add(dataDO8);
            ZnsbTzzxXxfpmxbDO dataDO9 = this.makeXxfpmxbYssjDO(jgxx, "010", "24317000000000731460", "1688.86", "219.540000", "1908.40");
            dataDOs.add(dataDO9);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxXxfpmxb(List<ZnsbTzzxXxfpmxbDO> dataDOs) {
        for(ZnsbTzzxXxfpmxbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxXxfpmxbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxXxfpmxbMapper.delete(qw);
            znsbTzzxXxfpmxbMapper.insert(dataDO);
        }
    }
    public ZnsbTzzxXxfpmxbDO makeXxfpmxbYssjDO(Map<String, Object> jgxx, String xh, String fphm, String je, String se, String jshj) {
        ZnsbTzzxXxfpmxbDO dataDO = new ZnsbTzzxXxfpmxbDO();
        dataDO.setUuid(GyUtils.getUuid());
        dataDO.setTzlxDm("1");
        dataDO.setYxbz("Y");
        dataDO.setLrrq(LocalDateTime.now());
        dataDO.setLrrsfid("yszh000001");
        dataDO.setXgrq(LocalDateTime.now());
        dataDO.setXgrsfid("yszh000001");
        dataDO.setSjtbSj(LocalDateTime.now());
        dataDO.setSjcsdq("00000000000");
        dataDO.setSjgsdq("00000000000");
        dataDO.setYwqdDm("TZ_USER");
        dataDO.setDjxh((String) jgxx.get("djxh"));
        dataDO.setGsh((String) jgxx.get("gsh"));
        dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
        dataDO.setNsrmc((String) jgxx.get("nsrmc"));
        dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
        dataDO.setFpdm(null);
        dataDO.setFphm(fphm);
        dataDO.setFpdmhm(fphm);
        dataDO.setFplxDm("81");
        dataDO.setFppzDm("01");
        dataDO.setCezsbz("N");
        dataDO.setJe(new BigDecimal(je));
        dataDO.setSe(new BigDecimal(se));
        dataDO.setJshj(new BigDecimal(jshj));
        dataDO.setGmfnsrsbh("91110********LHH9P");
        dataDO.setGmfnsrmc("演示购方名称");
        dataDO.setKprq(this.getKprqByDqyf());
        return dataDO;
    }
    //销项货物服务明细
    public List<ZnsbTzzxXxfphwfwmxbDO> setYssjDatasZnsbTzzxXxfphwfwmxbDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxXxfphwfwmxbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxXxfphwfwmxbDO dataDO = this.makeXxfphwfwmxbYssjDO(jgxx, "001", "24317000000000730870", "*服装*男款圆领短袖T恤", "1040201990000000000",
                    "11", "29.309734513274", "件", "322.41", "41.910000", "364.32", "0.00", "0.130000");
            dataDOs.add(dataDO);
            ZnsbTzzxXxfphwfwmxbDO dataDO1 = this.makeXxfphwfwmxbYssjDO(jgxx, "002", "24317000000282240142", "*服装*男款短袖POLO", "1040201110000000000",
                    "1", "41.185840707965", "件", "41.19", "5.350000", "46.54", "0.00", "0.130000");
            dataDOs.add(dataDO1);
            ZnsbTzzxXxfphwfwmxbDO dataDO2 = this.makeXxfphwfwmxbYssjDO(jgxx, "003", "24317000000000731196", "*服装*女款化纤茄克", "1040201990000000000",
                    "1", "83.955752212389", "件", "83.96", "10.910000", "94.87", "0.00", "0.130000");
            dataDOs.add(dataDO2);
            ZnsbTzzxXxfphwfwmxbDO dataDO3 = this.makeXxfphwfwmxbYssjDO(jgxx, "004", "24317000000000730346", "*服装*男款直筒裤", "1040201990000000000",
                    "1", "70.442477876106", "套", "70.44", "9.160000", "79.60", "0.00", "0.130000");
            dataDOs.add(dataDO3);
            ZnsbTzzxXxfphwfwmxbDO dataDO4 = this.makeXxfphwfwmxbYssjDO(jgxx, "005", "24317000000000731126", "*服装*男款合体裤", "1040201990000000000",
                    "30", "114.601769911504", "件", "3438.05", "446.950000", "3885.00", "0.00", "0.130000");
            dataDOs.add(dataDO4);
            ZnsbTzzxXxfphwfwmxbDO dataDO5 = this.makeXxfphwfwmxbYssjDO(jgxx, "006", "24317000000000731143", "*服装*女款锥型裤", "1040201990000000000",
                    "10", "81.008849557522", "件", "810.09", "105.310000", "915.40", "0.00", "0.130000");
            dataDOs.add(dataDO5);
            ZnsbTzzxXxfphwfwmxbDO dataDO6 = this.makeXxfphwfwmxbYssjDO(jgxx, "007", "24317000000000731097", "*服装*女款圆领短袖T恤", "1040201990000000000",
                    "1", "47.920353982301", "件", "47.92", "6.230000", "54.15", "0.00", "0.130000");
            dataDOs.add(dataDO6);
            ZnsbTzzxXxfphwfwmxbDO dataDO7 = this.makeXxfphwfwmxbYssjDO(jgxx, "008", "24317000000282239929", "*服装*女款阔腿裤", "1040201990000000000",
                    "5", "96.238938053097", "件", "481.19", "62.560000", "543.75", "0.00", "0.130000");
            dataDOs.add(dataDO7);
            ZnsbTzzxXxfphwfwmxbDO dataDO8 = this.makeXxfphwfwmxbYssjDO(jgxx, "009", "24317000000282239867", "*服装*中性款圆领短袖T恤", "1040201990000000000",
                    "49", "64.017699115044", "件", "3136.86", "407.800000", "3544.66", "0.00", "0.130000");
            dataDOs.add(dataDO8);
            ZnsbTzzxXxfphwfwmxbDO dataDO9 = this.makeXxfphwfwmxbYssjDO(jgxx, "010", "24317000000000731151", "*服装*女款直筒裤", "1040201990000000000",
                    "37", "81.008849557522", "件", "2997.33", "389.650000", "3386.98", "0.00", "0.130000");
            dataDOs.add(dataDO9);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxXxfphwfwmxb(List<ZnsbTzzxXxfphwfwmxbDO> dataDOs) {
        for(ZnsbTzzxXxfphwfwmxbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxXxfphwfwmxbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxXxfphwfwmxbMapper.delete(qw);
            znsbTzzxXxfphwfwmxbMapper.insert(dataDO);
        }
    }
    public ZnsbTzzxXxfphwfwmxbDO makeXxfphwfwmxbYssjDO(Map<String, Object> jgxx, String xh, String fphm, String hwhyslwfwmc, String sphfwssflhbbm,
                                                       String fpspsl, String fpspdj, String dw, String je, String se, String jshj, String kce,
                                                       String sl){
        ZnsbTzzxXxfphwfwmxbDO dataDO = new ZnsbTzzxXxfphwfwmxbDO();
        dataDO.setUuid(GyUtils.getUuid());
        dataDO.setDjxh((String) jgxx.get("djxh"));
        dataDO.setGsh((String) jgxx.get("gsh"));
        dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
        dataDO.setNsrmc((String) jgxx.get("nsrmc"));
        dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
        dataDO.setTzlxDm("1");
        dataDO.setLrrq(LocalDateTime.now());
        dataDO.setLrrsfid("yszh000001");
        dataDO.setXgrq(LocalDateTime.now());
        dataDO.setXgrsfid("yszh000001");
        dataDO.setSjtbSj(LocalDateTime.now());
        dataDO.setSjcsdq("00000000000");
        dataDO.setSjgsdq("00000000000");
        dataDO.setYwqdDm("TZ_USER");
        dataDO.setFpdm(null);
        dataDO.setFphm(fphm);
        dataDO.setFpdmhm(fphm);
        dataDO.setFplxDm("81");
        dataDO.setFppzDm("01");
        dataDO.setGmfnsrsbh("91110********LHH9P");
        dataDO.setGmfnsrmc("演示购方名称");
        dataDO.setHwhyslwfwmc(hwhyslwfwmc);
        dataDO.setSphfwssflhbbm(sphfwssflhbbm);
        dataDO.setGgxh("");
        dataDO.setFpspsl(fpspsl);
        dataDO.setFpspdj(fpspdj);
        dataDO.setDw(dw);
        dataDO.setJe(new BigDecimal(je));
        dataDO.setSe(new BigDecimal(se));
        dataDO.setJshj(new BigDecimal(jshj));
        dataDO.setKce(new BigDecimal(kce));
        dataDO.setKprq(this.getKprqByDqyf());
        dataDO.setJsfsDm("01");
        dataDO.setZsxmDm("01");
        dataDO.setSl(new BigDecimal(sl));
        dataDO.setJzjtbz("N");
        dataDO.setJyssbz("N");
        dataDO.setZzscbz("N");
        dataDO.setZbuuid("");
        return dataDO;
    }
    //收入总账
    public List<ZnsbTzzxSrzzbDO> setYssjDatasZnsbTzzxSrzzDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxSrzzbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxSrzzbDO dataDO = new ZnsbTzzxSrzzbDO();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh((String) jgxx.get("djxh"));
            dataDO.setGsh2((String) jgxx.get("gsh"));
            dataDO.setLrzx((String) jgxx.get("lrzx"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(this.getSszqByDqny(LocalDate.now()));
            dataDO.setLrrq(LocalDateTime.now());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(LocalDateTime.now());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(LocalDateTime.now());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setSrlxDm("120");
            dataDO.setJsfsDm1("01");
            dataDO.setZsxmDm("03");
            dataDO.setSl1(new BigDecimal("0.090000"));
            dataDO.setXssrkmdm("6051010100");
            dataDO.setXssrkmmc("其他业务收入-辅料-集成");
            dataDO.setXssr("3333.88");
            dataDO.setXssekmdm("6051010100");
            dataDO.setXssekmmc("其他业务收入-辅料-集成");
            dataDO.setXsse(new BigDecimal("300.050000"));
            dataDO.setYxbz("Y");
            dataDOs.add(dataDO);

            ZnsbTzzxSrzzbDO dataDO2 = new ZnsbTzzxSrzzbDO();
            dataDO2.setUuid(GyUtils.getUuid());
            dataDO2.setDjxh((String) jgxx.get("djxh"));
            dataDO2.setGsh2((String) jgxx.get("gsh"));
            dataDO2.setLrzx((String) jgxx.get("lrzx"));
            dataDO2.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO2.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO2.setSszq(this.getSszqByDqny(LocalDate.now()));
            dataDO2.setLrrq(LocalDateTime.now());
            dataDO2.setLrrsfid("yszh000001");
            dataDO2.setXgrq(LocalDateTime.now());
            dataDO2.setXgrsfid("yszh000001");
            dataDO2.setSjtbSj(LocalDateTime.now());
            dataDO2.setSjcsdq("00000000000");
            dataDO2.setSjgsdq("00000000000");
            dataDO2.setYwqdDm("TZ_USER");
            dataDO2.setSrlxDm("110");
            dataDO2.setJsfsDm1("01");
            dataDO2.setZsxmDm("01");
            dataDO2.setSl1(new BigDecimal("0.170000"));
            dataDO2.setXssrkmdm("6001010100");
            dataDO2.setXssrkmmc("主营业务收入-成品-集成");
            dataDO2.setXssr("8888.99");
            dataDO2.setXssekmdm("6601221600");
            dataDO2.setXssekmmc("期间费用-服务费-联营扣点");
            dataDO2.setXsse(new BigDecimal("1511.130000"));
            dataDO2.setYxbz("Y");
            dataDOs.add(dataDO2);

            ZnsbTzzxSrzzbDO dataDO3 = new ZnsbTzzxSrzzbDO();
            dataDO3.setUuid(GyUtils.getUuid());
            dataDO3.setDjxh((String) jgxx.get("djxh"));
            dataDO3.setGsh2((String) jgxx.get("gsh"));
            dataDO3.setLrzx((String) jgxx.get("lrzx"));
            dataDO3.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO3.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO3.setSszq(this.getSszqByDqny(LocalDate.now()));
            dataDO3.setLrrq(LocalDateTime.now());
            dataDO3.setLrrsfid("yszh000001");
            dataDO3.setXgrq(LocalDateTime.now());
            dataDO3.setXgrsfid("yszh000001");
            dataDO3.setSjtbSj(LocalDateTime.now());
            dataDO3.setSjcsdq("00000000000");
            dataDO3.setSjgsdq("00000000000");
            dataDO3.setYwqdDm("TZ_USER");
            dataDO3.setSrlxDm("130");
            dataDO3.setJsfsDm1("01");
            dataDO3.setZsxmDm("01");
            dataDO3.setSl1(new BigDecimal("0.130000"));
            dataDO3.setXssrkmdm("2221010400");
            dataDO3.setXssrkmmc("应交税费-应交增值税-销项税额");
            dataDO3.setXssr("40700.00");
            dataDO3.setXssekmdm("2221010400");
            dataDO3.setXssekmmc("应交税费-应交增值税-销项税额");
            dataDO3.setXsse(new BigDecimal("5291.000000"));
            dataDO3.setYxbz("Y");
            dataDOs.add(dataDO3);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxSrzzb(List<ZnsbTzzxSrzzbDO> dataDOs) {
        for(ZnsbTzzxSrzzbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxSrzzbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxSrzzbMapper.delete(qw);
            znsbTzzxSrzzbMapper.insert(dataDO);
        }
    }
    //收入明细
    public List<ZnsbTzzxSrmxbDO> setYssjDatasZnsbTzzxSrmxbDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxSrmxbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxSrmxbDO dataDO = new ZnsbTzzxSrmxbDO();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh((String) jgxx.get("djxh"));
            dataDO.setGsh2((String) jgxx.get("gsh"));
            dataDO.setLrzx((String) jgxx.get("lrzx"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(this.getSszqByDqny(LocalDate.now()));
            dataDO.setTzlxDm("1");
            dataDO.setYxbz("Y");
            dataDO.setLrrq(LocalDateTime.now());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(LocalDateTime.now());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(LocalDateTime.now());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setSrlxDm("120");
            dataDO.setJsfsDm1("01");
            dataDO.setZsxmDm("03");
            dataDO.setSl1(new BigDecimal("0.090000"));
            dataDO.setKmdm("6051010100");
            dataDO.setKmmc("其他业务收入-辅料-集成");
            dataDO.setBbje(new BigDecimal("3333.88"));
            dataDO.setKjpzbh("00000120240711000366");
            dataDO.setCkpzh(null);
            dataDO.setKjfp("");
            dataDO.setLy("1");
            dataDO.setTbbz("N");
            dataDO.setZzscbz("N");
            dataDO.setJyssbz("N");
            dataDO.setSrzzuuid("");
            dataDOs.add(dataDO);

            ZnsbTzzxSrmxbDO dataDO2 = new ZnsbTzzxSrmxbDO();
            dataDO2.setUuid(GyUtils.getUuid());
            dataDO2.setDjxh((String) jgxx.get("djxh"));
            dataDO2.setGsh2((String) jgxx.get("gsh"));
            dataDO2.setLrzx((String) jgxx.get("lrzx"));
            dataDO2.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO2.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO2.setSszq(this.getSszqByDqny(LocalDate.now()));
            dataDO2.setTzlxDm("1");
            dataDO2.setYxbz("Y");
            dataDO2.setLrrq(LocalDateTime.now());
            dataDO2.setLrrsfid("yszh000001");
            dataDO2.setXgrq(LocalDateTime.now());
            dataDO2.setXgrsfid("yszh000001");
            dataDO2.setSjtbSj(LocalDateTime.now());
            dataDO2.setSjcsdq("00000000000");
            dataDO2.setSjgsdq("00000000000");
            dataDO2.setYwqdDm("TZ_USER");
            dataDO2.setSrlxDm("110");
            dataDO2.setJsfsDm1("01");
            dataDO2.setZsxmDm("01");
            dataDO2.setSl1(new BigDecimal("0.170000"));
            dataDO2.setKmdm("6001010100");
            dataDO2.setKmmc("主营业务收入-成品-集成");
            dataDO2.setBbje(new BigDecimal("8888.99"));
            dataDO2.setKjpzbh("00000120240711000249");
            dataDO2.setCkpzh(null);
            dataDO2.setKjfp("");
            dataDO2.setLy("1");
            dataDO2.setTbbz("N");
            dataDO2.setZzscbz("N");
            dataDO2.setJyssbz("N");
            dataDO2.setSrzzuuid("");
            dataDOs.add(dataDO2);

            ZnsbTzzxSrmxbDO dataDO3 = new ZnsbTzzxSrmxbDO();
            dataDO3.setUuid(GyUtils.getUuid());
            dataDO3.setDjxh((String) jgxx.get("djxh"));
            dataDO3.setGsh2((String) jgxx.get("gsh"));
            dataDO3.setLrzx((String) jgxx.get("lrzx"));
            dataDO3.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO3.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO3.setSszq(this.getSszqByDqny(LocalDate.now()));
            dataDO3.setTzlxDm("1");
            dataDO3.setYxbz("Y");
            dataDO3.setLrrq(LocalDateTime.now());
            dataDO3.setLrrsfid("yszh000001");
            dataDO3.setXgrq(LocalDateTime.now());
            dataDO3.setXgrsfid("yszh000001");
            dataDO3.setSjtbSj(LocalDateTime.now());
            dataDO3.setSjcsdq("00000000000");
            dataDO3.setSjgsdq("00000000000");
            dataDO3.setYwqdDm("TZ_USER");
            dataDO3.setSrlxDm("130");
            dataDO3.setJsfsDm1("01");
            dataDO3.setZsxmDm("01");
            dataDO3.setSl1(new BigDecimal("0.130000"));
            dataDO3.setKmdm("2221010400");
            dataDO3.setKmmc("应交税费-应交增值税-销项税额");
            dataDO3.setBbje(new BigDecimal("5291.00"));
            dataDO3.setKjpzbh("00000120240704000081");
            dataDO3.setCkpzh(null);
            dataDO3.setKjfp("B17");
            dataDO3.setLy("1");
            dataDO3.setTbbz("N");
            dataDO3.setZzscbz("N");
            dataDO3.setJyssbz("N");
            dataDO3.setSrzzuuid("");
            dataDOs.add(dataDO3);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxSrzzmxb(List<ZnsbTzzxSrmxbDO> dataDOs) {
        for(ZnsbTzzxSrmxbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxSrmxbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxSrzzmxbMapper.delete(qw);
            znsbTzzxSrzzmxbMapper.insert(dataDO);
        }
    }
    //进项总账
    public List<ZnsbTzzxJxfpzzbDO> setYssjDatasZnsbTzzxJxfpzzbDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxJxfpzzbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxJxfpzzbDO dataDO = new ZnsbTzzxJxfpzzbDO();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh((String) jgxx.get("djxh"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO.setTzlxDm("1");
            dataDO.setLrrq(LocalDateTime.now());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(LocalDateTime.now());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(LocalDateTime.now());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setXmdl("01");
            dataDO.setXmxl("0101");
            dataDO.setFs(new BigDecimal("3.000000"));
            dataDO.setJe(new BigDecimal("20310.02"));
            dataDO.setSe(new BigDecimal("1025.120000"));
            dataDO.setScbz("0");
            dataDOs.add(dataDO);

            ZnsbTzzxJxfpzzbDO dataDO2 = new ZnsbTzzxJxfpzzbDO();
            dataDO2.setUuid(GyUtils.getUuid());
            dataDO2.setDjxh((String) jgxx.get("djxh"));
            dataDO2.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO2.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO2.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO2.setTzlxDm("1");
            dataDO2.setLrrq(LocalDateTime.now());
            dataDO2.setLrrsfid("yszh000001");
            dataDO2.setXgrq(LocalDateTime.now());
            dataDO2.setXgrsfid("yszh000001");
            dataDO2.setSjtbSj(LocalDateTime.now());
            dataDO2.setSjcsdq("00000000000");
            dataDO2.setSjgsdq("00000000000");
            dataDO2.setYwqdDm("TZ_USER");
            dataDO2.setXmdl("01");
            dataDO2.setXmxl("0110");
            dataDO2.setFs(new BigDecimal("4.000000"));
            dataDO2.setJe(new BigDecimal("1281.00"));
            dataDO2.setSe(new BigDecimal("72.900000"));
            dataDO2.setScbz("0");
            dataDOs.add(dataDO2);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxJxfpzzb(List<ZnsbTzzxJxfpzzbDO> dataDOs) {
        for(ZnsbTzzxJxfpzzbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxJxfpzzbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxJxfpzzbMapper.delete(qw);
            znsbTzzxJxfpzzbMapper.insert(dataDO);
        }
    }
    //进项发票明细
    public List<ZnsbTzzxJxfpmxbDO> setYssjDatasZnsbTzzxJxfpmxbDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxJxfpmxbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxJxfpmxbDO dataDO = this.makeJxfpmxbYssjDO(jgxx, "001", "24000000000003525423", "39646.02", "5153.980000", "44800.00");
            dataDOs.add(dataDO);
            ZnsbTzzxJxfpmxbDO dataDO1 = this.makeJxfpmxbYssjDO(jgxx, "002", "23000000000000781418", "634.61", "82.490000", "717.10");
            dataDOs.add(dataDO1);
            ZnsbTzzxJxfpmxbDO dataDO2 = this.makeJxfpmxbYssjDO(jgxx, "003", "24000000000003596113", "404.43", "52.570000", "457.00");
            dataDOs.add(dataDO2);
            ZnsbTzzxJxfpmxbDO dataDO3 = this.makeJxfpmxbYssjDO(jgxx, "004", "23467000000000014014", "566.04", "33.960000", "600.00");
            dataDOs.add(dataDO3);
            ZnsbTzzxJxfpmxbDO dataDO4 = this.makeJxfpmxbYssjDO(jgxx, "005", "23467000000000030221", "19348.70", "967.440000", "20316.14");
            dataDOs.add(dataDO4);
            ZnsbTzzxJxfpmxbDO dataDO5 = this.makeJxfpmxbYssjDO(jgxx, "006", "23467000000000030029", "30000.00", "3900.000000", "33900.00");
            dataDOs.add(dataDO5);
            ZnsbTzzxJxfpmxbDO dataDO6 = this.makeJxfpmxbYssjDO(jgxx, "007", "24442000000000770097", "77878.41", "10124.190000", "88002.60");
            dataDOs.add(dataDO6);
            ZnsbTzzxJxfpmxbDO dataDO7 = this.makeJxfpmxbYssjDO(jgxx, "008", "23467000000000000002", "78521.23", "17396.240000", "95917.47");
            dataDOs.add(dataDO7);
            ZnsbTzzxJxfpmxbDO dataDO8 = this.makeJxfpmxbYssjDO(jgxx, "009", "23000000000000781408", "563.72", "73.280000", "637.00");
            dataDOs.add(dataDO8);
            ZnsbTzzxJxfpmxbDO dataDO9 = this.makeJxfpmxbYssjDO(jgxx, "010", "24000000000001337243", "1688.86", "219.540000", "1908.40");
            dataDOs.add(dataDO9);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxJxfpmxb(List<ZnsbTzzxJxfpmxbDO> dataDOs) {
        for(ZnsbTzzxJxfpmxbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxJxfpmxbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxJxfpmxbMapper.delete(qw);
            znsbTzzxJxfpmxbMapper.insert(dataDO);
        }
    }
    public ZnsbTzzxJxfpmxbDO makeJxfpmxbYssjDO(Map<String, Object> jgxx, String xh, String fphm, String je, String se, String jshj) {
        ZnsbTzzxJxfpmxbDO dataDO = new ZnsbTzzxJxfpmxbDO();
        dataDO.setUuid(GyUtils.getUuid());
        dataDO.setDjxh((String) jgxx.get("djxh"));
        dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
        dataDO.setNsrmc((String) jgxx.get("nsrmc"));
        dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
        dataDO.setTzlxDm("1");
        dataDO.setLrrq(LocalDateTime.now());
        dataDO.setLrrsfid("yszh000001");
        dataDO.setXgrq(LocalDateTime.now());
        dataDO.setXgrsfid("yszh000001");
        dataDO.setSjtbSj(LocalDateTime.now());
        dataDO.setSjcsdq("00000000000");
        dataDO.setSjgsdq("00000000000");
        dataDO.setYwqdDm("TZ_USER");
        dataDO.setFplxDm("81");
        dataDO.setFppzDm("01");
        dataDO.setFpdm(null);
        dataDO.setFphm(fphm);
        dataDO.setFpdmhm(fphm);
        dataDO.setJe(new BigDecimal(je));
        dataDO.setSe(new BigDecimal(se));
        dataDO.setJshj(new BigDecimal(jshj));
        dataDO.setKprq(this.getKprqByDqyf());
        dataDO.setSkssq(this.getSszqByDqny(LocalDate.now()));
        dataDO.setXfsh("91110********LHH9P");
        dataDO.setXfmc("演示购方名称");
        dataDO.setGxrzsj(LocalDateTime.now());
        dataDO.setHxytDm("1");
        dataDO.setBdkyyDm(null);
        dataDO.setDkje(new BigDecimal(je));
        dataDO.setDkse(new BigDecimal(se));
        dataDO.setScbz("0");
        dataDO.setScsj(LocalDateTime.now());
        dataDO.setJyssbz("N");
        dataDO.setZzscbz("N");
        return dataDO;
    }
    //进项货物服务明细
    public List<ZnsbTzzxJxfphwfwmxbDO> setYssjDatasZnsbTzzxJxfphwfwmxbDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxJxfphwfwmxbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxJxfphwfwmxbDO dataDO = this.makeJxfphwfwmxbYssjDO(jgxx, "001", "24317000000000730870", "*服装*女款梭织连衣裙", "1040201990000000000",
                    "2", "113.778761061947", "件", "227.56", "29.580000", "257.14", "0.130000");
            dataDOs.add(dataDO);
            ZnsbTzzxJxfphwfwmxbDO dataDO1 = this.makeJxfphwfwmxbYssjDO(jgxx, "002", "24317000000282239929", "*服装*女款圆领短袖T恤", "1040201990000000000",
                    "6", "45.283185840708", "件", "271.70", "35.320000", "307.02", "0.130000");
            dataDOs.add(dataDO1);
            ZnsbTzzxJxfphwfwmxbDO dataDO2 = this.makeJxfphwfwmxbYssjDO(jgxx, "003", "24317000000000730885", "*服装*男款锥型裤", "1040201990000000000",
                    "40", "97.29203539823", "件", "3891.68", "505.920000", "4397.60", "0.130000");
            dataDOs.add(dataDO2);
            ZnsbTzzxJxfphwfwmxbDO dataDO3 = this.makeJxfphwfwmxbYssjDO(jgxx, "004", "24317000000000730783", "*服装*男款圆领短袖T恤", "1040201990000000000",
                    "9", "45.283185840708", "件", "407.55", "52.980000", "460.53", "0.130000");
            dataDOs.add(dataDO3);
            ZnsbTzzxJxfphwfwmxbDO dataDO4 = this.makeJxfphwfwmxbYssjDO(jgxx, "005", "24317000000000731097", "*服装*女款圆领短袖T恤", "1040201990000000000",
                    "1", "96.238938053097", "件", "96.24", "12.510000", "108.75", "0.130000");
            dataDOs.add(dataDO4);
            ZnsbTzzxJxfphwfwmxbDO dataDO5 = this.makeJxfphwfwmxbYssjDO(jgxx, "006", "24317000000282239929", "*服装*男款净色衬衫", "1040201990000000000",
                    "5", "105.433628318584", "件", "527.17", "68.530000", "595.70", "0.130000");
            dataDOs.add(dataDO5);
            ZnsbTzzxJxfphwfwmxbDO dataDO6 = this.makeJxfphwfwmxbYssjDO(jgxx, "007", "24317000000000730346", "*服装*男款直筒裤", "1040201990000000000",
                    "13", "55.973451327434", "件", "727.65", "94.600000", "822.25", "0.130000");
            dataDOs.add(dataDO6);
            ZnsbTzzxJxfphwfwmxbDO dataDO7 = this.makeJxfphwfwmxbYssjDO(jgxx, "008", "24317000000000731184", "*服装*女款圆领短袖T恤", "1040201990000000000",
                    "37", "81.008849557522", "件", "2997.33", "389.650000", "3386.98", "0.130000");
            dataDOs.add(dataDO7);
            ZnsbTzzxJxfphwfwmxbDO dataDO8 = this.makeJxfphwfwmxbYssjDO(jgxx, "009", "24317000000000730892", "*服装*女款直筒裤", "1040201990000000000",
                    "49", "64.017699115044", "件", "3136.86", "407.800000", "3544.66", "0.130000");
            dataDOs.add(dataDO8);
            ZnsbTzzxJxfphwfwmxbDO dataDO9 = this.makeJxfphwfwmxbYssjDO(jgxx, "010", "24317000000000731126", "*服装*中性款圆领短袖T恤", "1040201990000000000",
                    "5", "96.238938053097", "件", "481.19", "62.560000", "543.75", "0.130000");
            dataDOs.add(dataDO9);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxJxfphwfwmxb(List<ZnsbTzzxJxfphwfwmxbDO> dataDOs) {
        for(ZnsbTzzxJxfphwfwmxbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxJxfphwfwmxbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxJxfphwfwmxbMapper.delete(qw);
            znsbTzzxJxfphwfwmxbMapper.insert(dataDO);
        }
    }
    public ZnsbTzzxJxfphwfwmxbDO makeJxfphwfwmxbYssjDO(Map<String, Object> jgxx, String xh, String fphm, String hwhyslwfwmc, String sphfwssflhbbm,
                                                       String fpspsl, String fpspdj, String dw, String je, String se, String jshj, String sl) {
        ZnsbTzzxJxfphwfwmxbDO dataDO = new ZnsbTzzxJxfphwfwmxbDO();
        dataDO.setUuid(GyUtils.getUuid());
        dataDO.setDjxh((String) jgxx.get("djxh"));
        dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
        dataDO.setNsrmc((String) jgxx.get("nsrmc"));
        dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
        dataDO.setTzlxDm("1");
        dataDO.setLrrq(LocalDateTime.now());
        dataDO.setLrrsfid("yszh000001");
        dataDO.setXgrq(LocalDateTime.now());
        dataDO.setXgrsfid("yszh000001");
        dataDO.setSjtbSj(LocalDateTime.now());
        dataDO.setSjcsdq("00000000000");
        dataDO.setSjgsdq("00000000000");
        dataDO.setYwqdDm("TZ_USER");
        dataDO.setFplxDm("81");
        dataDO.setFppzDm("01");
        dataDO.setFpdm(null);
        dataDO.setFphm(fphm);
        dataDO.setFpdmhm(fphm);
        dataDO.setKprq(this.getKprqByDqyf());
        dataDO.setXsfnsrsbh("91110********LHH9P");
        dataDO.setXsfmc("演示购方名称");
        dataDO.setHwhyslwfwmc(hwhyslwfwmc);
        dataDO.setSphfwssflhbbm(sphfwssflhbbm);
        dataDO.setGgxh("");
        dataDO.setFpspsl(fpspsl);
        dataDO.setFpspdj(fpspdj);
        dataDO.setDw(dw);
        dataDO.setJe(new BigDecimal(je));
        dataDO.setSe(new BigDecimal(se));
        dataDO.setJshj(new BigDecimal(jshj));
        dataDO.setJzjtbz("N");
        dataDO.setSl(new BigDecimal(sl));
        dataDO.setYqrzbz("N");
        dataDO.setZzscbz("N");
        dataDO.setJyssbz("N");
        return dataDO;
    }
    //其他总账
    public List<ZnsbTzzxQtkspzzzDO> setYssjDatasZnsbTzzxQtkspzzzDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxQtkspzzzDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxQtkspzzzDO dataDO = new ZnsbTzzxQtkspzzzDO();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh((String) jgxx.get("djxh"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO.setTzlxDm("1");
            dataDO.setLrrq(LocalDateTime.now());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(LocalDateTime.now());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(LocalDateTime.now());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            LocalDate date = LocalDate.parse(dataDO.getSszq() + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
            dataDO.setSkssqq(date.with(TemporalAdjusters.firstDayOfMonth()));
            dataDO.setSkssqz(date.with(TemporalAdjusters.lastDayOfMonth()));
            dataDO.setQtkspzxmlx("3");
            dataDO.setDkfs2(new BigDecimal("2.000000"));
            dataDO.setDkje2(new BigDecimal("550.00"));
            dataDO.setDkse(new BigDecimal("16.500000"));
            dataDO.setDdkfs(new BigDecimal("0.000000"));
            dataDO.setDdkje(new BigDecimal("0.00"));
            dataDO.setDdkse(new BigDecimal("0.000000"));
            dataDO.setZzscbz("N");
            dataDO.setJyssbz("N");
            dataDO.setLy("1");
            dataDO.setJxzzuuid("");
            dataDOs.add(dataDO);

            ZnsbTzzxQtkspzzzDO dataDO2 = new ZnsbTzzxQtkspzzzDO();
            dataDO2.setUuid(GyUtils.getUuid());
            dataDO2.setDjxh((String) jgxx.get("djxh"));
            dataDO2.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO2.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO2.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO2.setTzlxDm("1");
            dataDO2.setLrrq(LocalDateTime.now());
            dataDO2.setLrrsfid("yszh000001");
            dataDO2.setXgrq(LocalDateTime.now());
            dataDO2.setXgrsfid("yszh000001");
            dataDO2.setSjtbSj(LocalDateTime.now());
            dataDO2.setSjcsdq("00000000000");
            dataDO2.setSjgsdq("00000000000");
            dataDO2.setYwqdDm("TZ_USER");
            LocalDate date2 = LocalDate.parse(dataDO2.getSszq() + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
            dataDO2.setSkssqq(date2.with(TemporalAdjusters.firstDayOfMonth()));
            dataDO2.setSkssqz(date2.with(TemporalAdjusters.lastDayOfMonth()));
            dataDO2.setQtkspzxmlx("1");
            dataDO2.setDkfs2(new BigDecimal("2.000000"));
            dataDO2.setDkje2(new BigDecimal("731.00"));
            dataDO2.setDkse(new BigDecimal("56.400000"));
            dataDO2.setDdkfs(new BigDecimal("0.000000"));
            dataDO2.setDdkje(new BigDecimal("0.00"));
            dataDO2.setDdkse(new BigDecimal("0.000000"));
            dataDO2.setZzscbz("N");
            dataDO2.setJyssbz("N");
            dataDO2.setLy("0");
            dataDO2.setJxzzuuid("");
            dataDOs.add(dataDO2);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxQtkspzzz(List<ZnsbTzzxQtkspzzzDO> dataDOs) {
        for(ZnsbTzzxQtkspzzzDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxQtkspzzzDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxQtkspzzzMapper.delete(qw);
            znsbTzzxQtkspzzzMapper.insert(dataDO);
        }
    }
    //旅客明细
    public List<ZnsbTzzxLkysfwkspzmxzDO> setYssjDatasZnsbTzzxLkysfwkspzmxzDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxLkysfwkspzmxzDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxLkysfwkspzmxzDO dataDO = new ZnsbTzzxLkysfwkspzmxzDO();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh((String) jgxx.get("djxh"));
            dataDO.setGsh2((String) jgxx.get("gsh"));
            dataDO.setLrzx((String) jgxx.get("lrzx"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(this.getSszqByDqny(LocalDate.now()));
            dataDO.setTzlxDm("1");
            dataDO.setLrrq(LocalDateTime.now());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(LocalDateTime.now());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(LocalDateTime.now());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setKjpzbh("00000120240704000100");
            dataDO.setBxpzlxDm("1");
            dataDO.setKmdm("2221010102");
            dataDO.setKmmc("应交税费-应交增值税-进项税额-计算抵扣");
            dataDO.setKdje(new BigDecimal("492.35"));
            dataDO.setSl1(new BigDecimal("0.100000"));
            dataDO.setKdse(new BigDecimal("49.24"));
            dataDO.setFs(new BigDecimal("1.000000"));
            dataDO.setScbz("0");
            dataDO.setScsj1(null);
            dataDO.setLy("1");
            dataDO.setZzscbz("N");
            dataDO.setJyssbz("N");
            dataDO.setJxzzuuid("");
            dataDO.setDkzzuuid("");
            dataDOs.add(dataDO);

            ZnsbTzzxLkysfwkspzmxzDO dataDO2 = new ZnsbTzzxLkysfwkspzmxzDO();
            dataDO2.setUuid(GyUtils.getUuid());
            dataDO2.setDjxh((String) jgxx.get("djxh"));
            dataDO2.setGsh2((String) jgxx.get("gsh"));
            dataDO2.setLrzx((String) jgxx.get("lrzx"));
            dataDO2.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO2.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO2.setSszq(this.getSszqByDqny(LocalDate.now()));
            dataDO2.setTzlxDm("1");
            dataDO2.setLrrq(LocalDateTime.now());
            dataDO2.setLrrsfid("yszh000001");
            dataDO2.setXgrq(LocalDateTime.now());
            dataDO2.setXgrsfid("yszh000001");
            dataDO2.setSjtbSj(LocalDateTime.now());
            dataDO2.setSjcsdq("00000000000");
            dataDO2.setSjgsdq("00000000000");
            dataDO2.setYwqdDm("TZ_USER");
            dataDO2.setKjpzbh("00000120240704000100");
            dataDO2.setBxpzlxDm("2");
            dataDO2.setKmdm("2221010102");
            dataDO2.setKmmc("应交税费-应交增值税-进项税额-计算抵扣");
            dataDO2.setKdje(new BigDecimal("238.65"));
            dataDO2.setSl1(new BigDecimal("0.030000"));
            dataDO2.setKdse(new BigDecimal("7.16"));
            dataDO2.setFs(new BigDecimal("1.000000"));
            dataDO2.setScbz("0");
            dataDO2.setScsj1(null);
            dataDO2.setLy("1");
            dataDO2.setZzscbz("N");
            dataDO2.setJyssbz("N");
            dataDO2.setJxzzuuid("");
            dataDO2.setDkzzuuid("");
            dataDOs.add(dataDO2);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxLkysfwkspzmxz(List<ZnsbTzzxLkysfwkspzmxzDO> dataDOs) {
        for(ZnsbTzzxLkysfwkspzmxzDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxLkysfwkspzmxzDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxLkysfwkspzmxzMapper.delete(qw);
            znsbTzzxLkysfwkspzmxzMapper.insert(dataDO);
        }
    }
    //进项税额转出总账
    public List<ZnsbTzzxJxsezczz> setYssjDatasJxsezczzDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxJxsezczz> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxJxsezczz dataDO = new ZnsbTzzxJxsezczz();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh(new BigDecimal((String) jgxx.get("djxh")));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO.setTzlxDm("1");
            dataDO.setLrrq(new Date());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(new Date());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(new Date());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setJxsezcxmDm("01");
            dataDO.setZcse(new BigDecimal("523000.000000"));
            dataDO.setJzjtse(new BigDecimal("0.000000"));
            dataDO.setLy("1");
            dataDO.setJyssbz("N");
            dataDOs.add(dataDO);

            ZnsbTzzxJxsezczz dataDO2 = new ZnsbTzzxJxsezczz();
            dataDO2.setUuid(GyUtils.getUuid());
            dataDO2.setDjxh(new BigDecimal((String) jgxx.get("djxh")));
            dataDO2.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO2.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO2.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO2.setTzlxDm("1");
            dataDO2.setLrrq(new Date());
            dataDO2.setLrrsfid("yszh000001");
            dataDO2.setXgrq(new Date());
            dataDO2.setXgrsfid("yszh000001");
            dataDO2.setSjtbSj(new Date());
            dataDO2.setSjcsdq("00000000000");
            dataDO2.setSjgsdq("00000000000");
            dataDO2.setYwqdDm("TZ_USER");
            dataDO2.setJxsezcxmDm("11");
            dataDO2.setZcse(new BigDecimal("2342.250000"));
            dataDO2.setJzjtse(new BigDecimal("0.000000"));
            dataDO2.setLy("1");
            dataDO2.setJyssbz("N");
            dataDOs.add(dataDO2);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxJxsezczz(List<ZnsbTzzxJxsezczz> dataDOs) {
        for(ZnsbTzzxJxsezczz dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxJxsezczz> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxJxsezczzMapper.delete(qw);
            znsbTzzxJxsezczzMapper.insert(dataDO);
        }
    }
    //进项税额转出明细
    public List<ZnsbTzzxJxsezcmxz> setYssjDatasJxsezcmxzDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxJxsezcmxz> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxJxsezcmxz dataDO = new ZnsbTzzxJxsezcmxz();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh(new BigDecimal((String) jgxx.get("djxh")));
            dataDO.setGsh((String) jgxx.get("gsh"));
            dataDO.setLrzx((String) jgxx.get("lrzx"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO.setTzlxDm("1");
            dataDO.setLrrq(new Date());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(new Date());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(new Date());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setZzuuid("");
            dataDO.setKjpzbh("00000120240704000446");
            dataDO.setJxsezcxmDm("11");
            dataDO.setZcrq(new Date());
            dataDO.setZcse(new BigDecimal("2342.250000"));
            dataDO.setJzjtse(null);
            dataDO.setKmbm("2221010500");
            dataDO.setKmmc("应交税费-应交增值税-进项税额转出");
            dataDO.setKjfpDm("C11");
            dataDO.setScbz("0");
            dataDO.setScsj1(null);
            dataDO.setLy("1");
            dataDO.setZzscbz("N");
            dataDO.setJyssbz("N");
            dataDOs.add(dataDO);

            ZnsbTzzxJxsezcmxz dataDO2 = new ZnsbTzzxJxsezcmxz();
            dataDO2.setUuid(GyUtils.getUuid());
            dataDO2.setDjxh(new BigDecimal((String) jgxx.get("djxh")));
            dataDO2.setGsh((String) jgxx.get("gsh"));
            dataDO2.setLrzx((String) jgxx.get("lrzx"));
            dataDO2.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO2.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO2.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO2.setTzlxDm("1");
            dataDO2.setLrrq(new Date());
            dataDO2.setLrrsfid("yszh000001");
            dataDO2.setXgrq(new Date());
            dataDO2.setXgrsfid("yszh000001");
            dataDO2.setSjtbSj(new Date());
            dataDO2.setSjcsdq("00000000000");
            dataDO2.setSjgsdq("00000000000");
            dataDO2.setYwqdDm("TZ_USER");
            dataDO2.setZzuuid("");
            dataDO2.setKjpzbh("00000120240702000588");
            dataDO2.setJxsezcxmDm("01");
            dataDO2.setZcrq(new Date());
            dataDO2.setZcse(new BigDecimal("523000.000000"));
            dataDO2.setJzjtse(null);
            dataDO2.setKmbm("2221010500");
            dataDO2.setKmmc("应交税费-应交增值税-进项税额转出");
            dataDO2.setKjfpDm("C01");
            dataDO2.setScbz("0");
            dataDO2.setScsj1(null);
            dataDO2.setLy("1");
            dataDO2.setZzscbz("N");
            dataDO2.setJyssbz("N");
            dataDOs.add(dataDO2);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxJxsezcmxz(List<ZnsbTzzxJxsezcmxz> dataDOs) {
        for(ZnsbTzzxJxsezcmxz dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxJxsezcmxz> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxJxsezcmxzMapper.delete(qw);
            znsbTzzxJxsezcmxzMapper.insert(dataDO);
        }
    }
    //减免总账
    public List<ZnsbTzzxJmszzDO> setYssjDatasZnsbTzzxJmszzDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxJmszzDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxJmszzDO dataDO = new ZnsbTzzxJmszzDO();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh((String) jgxx.get("djxh"));
            dataDO.setGsh((String) jgxx.get("gsh"));
            dataDO.setLrzx((String) jgxx.get("lrzx"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO.setTzlxDm("1");
            dataDO.setLrrq(LocalDateTime.now());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(LocalDateTime.now());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(LocalDateTime.now());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setSl1(new BigDecimal("0.030000"));
            dataDO.setJmzlxDm("02");
            dataDO.setSsjmxzDm("0001120601");
            dataDO.setSwsxDm("SXA031901051");
            dataDO.setBqfse(new BigDecimal("3000.00"));
            dataDO.setMzzzsxmxse(new BigDecimal("100000.00"));
            dataDO.setMzxsekcxmbqsjkcje(new BigDecimal("0.00"));
            dataDO.setScbz("0");
            dataDO.setScsj1(null);
            dataDOs.add(dataDO);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxJmszzb(List<ZnsbTzzxJmszzDO> dataDOs) {
        for(ZnsbTzzxJmszzDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxJmszzDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxJmszzMapper.delete(qw);
            znsbTzzxJmszzMapper.insert(dataDO);
        }
    }
    //减免明细
    public List<ZnsbTzzxJmsmxbDO> setYssjDatasZnsbTzzxJmsmxbDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxJmsmxbDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxJmsmxbDO dataDO = new ZnsbTzzxJmsmxbDO();
            dataDO.setUuid(GyUtils.getUuid());
            dataDO.setDjxh((String) jgxx.get("djxh"));
            dataDO.setGsh((String) jgxx.get("gsh"));
            dataDO.setLrzx((String) jgxx.get("lrzx"));
            dataDO.setNsrsbh((String) jgxx.get("nsrsbh"));
            dataDO.setNsrmc((String) jgxx.get("nsrmc"));
            dataDO.setSszq(Integer.parseInt(this.getSszqByDqny(LocalDate.now())));
            dataDO.setTzlxDm("1");
            dataDO.setLrrq(LocalDateTime.now());
            dataDO.setLrrsfid("yszh000001");
            dataDO.setXgrq(LocalDateTime.now());
            dataDO.setXgrsfid("yszh000001");
            dataDO.setSjtbSj(LocalDateTime.now());
            dataDO.setSjcsdq("00000000000");
            dataDO.setSjgsdq("00000000000");
            dataDO.setYwqdDm("TZ_USER");
            dataDO.setKjpzbh("00000120240701000134");
            dataDO.setSl1(new BigDecimal("0.030000"));
            dataDO.setKmdm("2221010800");
            dataDO.setKmmc("应交税费-应交增值税-减免税款");
            dataDO.setJmzlxDm("02");
            dataDO.setSsjmxzDm("0001120601");
            dataDO.setSwsxDm("SXA031901051");
            dataDO.setBqfse(new BigDecimal("3000.00"));
            dataDO.setMzzzsxmxse(new BigDecimal("100000.00"));
            dataDO.setMzxsekcxmbqsjkcje(new BigDecimal("0.00"));
            dataDO.setLy("1");
            dataDO.setZzscbz("N");
            dataDO.setJyssbz("N");
            dataDO.setScbz("0");
            dataDO.setScsj1(null);
            dataDO.setZzuuid("");
            dataDO.setKjfpDm("A04");
            dataDOs.add(dataDO);
        }
        return  dataDOs;
    }
    public void resetZnsbTzzxJmsmxb(List<ZnsbTzzxJmsmxbDO> dataDOs) {
        for(ZnsbTzzxJmsmxbDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxJmsmxbDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            qw.eq("nsrsbh", dataDO.getNsrsbh());
            qw.eq("nsrmc", dataDO.getNsrmc());
            znsbTzzxJmsmxbMapper.delete(qw);
            znsbTzzxJmsmxbMapper.insert(dataDO);
        }
    }

    //合同台账
    public List<ZnsbTzzxHtzzDO> setYssjDatasZnsbTzzxHtzzDO(List<Map<String, Object>> jgxxList) {
        List<ZnsbTzzxHtzzDO> dataDOs = new ArrayList<>();
        for(Map<String, Object> jgxx : jgxxList){
            ZnsbTzzxHtzzDO dataDO = this.makeYhshttzYssjDO(jgxx, "001", "11000000", "租赁合同", "101110114",
                    "101110114YH22002", "60000.00", 3);
            dataDOs.add(dataDO);
            ZnsbTzzxHtzzDO dataDO1 = this.makeYhshttzYssjDO(jgxx, "002", "12000000", "仓储保管合同", "101110116",
                    "", "590000.00", 0);
            dataDOs.add(dataDO1);
            ZnsbTzzxHtzzDO dataDO2 = this.makeYhshttzYssjDO(jgxx, "003", "13000000", "销售合同", "101110111",
                    "", "32000.00", 0);
            dataDOs.add(dataDO2);
        }
        return  dataDOs;
    }
    public ZnsbTzzxHtzzDO makeYhshttzYssjDO(Map<String, Object> jgxx, String xh, String htbh, String htmc, String zspmDm, String zszmDm, String bhsje, int monthAgo){
        ZnsbTzzxHtzzDO dataDO = new ZnsbTzzxHtzzDO();
        dataDO.setUuid(GyUtils.getUuid());
        dataDO.setDjxh((String) jgxx.get("djxh"));
        dataDO.setLrzx((String) jgxx.get("lrzx"));
        dataDO.setSszq(Integer.parseInt(DateUtils.dateToString(new Date(), 17)) - monthAgo);
        dataDO.setTzlxDm("1");
        dataDO.setLrrq(new Date());
        dataDO.setLrrsfid("yszh000001");
        dataDO.setXgrq(new Date());
        dataDO.setXgrsfid("yszh000001");
        dataDO.setSjtbSj(new Date());
        dataDO.setSjcsdq("00000000000");
        dataDO.setSjgsdq("00000000000");
        dataDO.setYwqdDm("TZ_USER");
        dataDO.setZzuuid(null);
        dataDO.setSyuuid(null);
        dataDO.setHtbh(htbh);
        dataDO.setHtmc(htmc);
        dataDO.setHtydzzrq(this.getHtzzrq(monthAgo));
        dataDO.setYnspzsllsrq(this.getHtqdrq(monthAgo));
        dataDO.setHtydsxrq(dataDO.getYnspzsllsrq());
        dataDO.setZspmDm(zspmDm);
        dataDO.setZszmDm(zszmDm);
        dataDO.setHtzjk1(new BigDecimal(bhsje));
        dataDO.setBhsje(new BigDecimal(bhsje));
        dataDO.setPzbh(null);
        dataDO.setPch(null);
        dataDO.setFbhtbj("N");
        dataDO.setKjhtbj("N");
        dataDO.setCqbz("0");
        dataDO.setScbz("0");
        dataDO.setScsj1(null);
        dataDO.setSymxuuid(null);
        dataDO.setZzscbz("N");
        dataDO.setJyssbz("N");
        dataDO.setHtbt("");
        dataDO.setHtsm1("");
        return dataDO;
    }
    public void resetZnsbTzzxHttz(List<ZnsbTzzxHtzzDO> dataDOs) {
        for(ZnsbTzzxHtzzDO dataDO : dataDOs) {
            QueryWrapper<ZnsbTzzxHtzzDO> qw = new QueryWrapper<>();
            qw.eq("djxh", dataDO.getDjxh());
            qw.eq("sszq", dataDO.getSszq());
            znsbTzzxHtzzMapper.delete(qw);
            znsbTzzxHtzzMapper.insert(dataDO);
        }
    }
    //印花税税源
    public void deleteYhssyYssj(List<Map<String, Object>> jgxxList){
        for(Map<String, Object> jgxx : jgxxList) {
            znsbNssbYhscjMapper.deleteYshjyssj((String) jgxx.get("djxh"));
            znsbNssbCxsbtxxMapper.deleteYshjyssj((String) jgxx.get("djxh"));
        }
    }
    //重置城土税源
    public void resetCztdsy(){
        sbCxsFtdcjglbMapper.updateFcsbczt();
        znsbNssbCxsTdsyxxcjbDOMapper.updateTdxxbczt();
    }
    //获取所属账期
    public String getSszqByDqny(LocalDate currentDate) {
        //所属账期为当前年月上一月，格式为“YYYYMM”
        LocalDate lastMonthDate = currentDate.minusMonths(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        return lastMonthDate.format(formatter);
    }
    //获取开票日期
    public LocalDateTime getKprqByDqyf() {
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1);
        LocalDateTime endOfMonth = startOfMonth.plusMonths(1).minusDays(1);
        // 计算当月有多少天
        long daysInMonth = ChronoUnit.DAYS.between(startOfMonth, endOfMonth);
        // 生成1到daysInMonth之间的随机数
        long randomDay = RANDOM.nextInt((int) daysInMonth) + 1;
        // 在startOfMonth的基础上加上随机天数
        LocalDateTime randomDate = startOfMonth.plusDays(randomDay);
        // 确保随机日期不超过当月15号
        return randomDate.isAfter(randomDate.withDayOfMonth(15)) ? randomDate.withDayOfMonth(15) : randomDate;
    }
    //将发票开票日期转换为当前月份
    public LocalDateTime transformKprqToDqyf(LocalDateTime targetTime) {
        // 获取当前月份的第一天
        LocalDate firstDayOfCurrentMonth = LocalDate.now().with(ChronoField.DAY_OF_MONTH, 1);
        // 获取指定日期的天数
        int dayOfTargetTime = targetTime.getDayOfMonth();
        // 将目标时间转换到当前月份
        LocalDateTime currentMonthTime = targetTime.withYear(LocalDate.now().getYear()).withMonth(firstDayOfCurrentMonth.getMonthValue()).withDayOfMonth(dayOfTargetTime);
        // 配合征期改为上个月
        return currentMonthTime.minusMonths(1);
    }
    public Date transformKprqToDqyfInDateType(Date targetTimeDate) {
        LocalDateTime targetTime = targetTimeDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return Date.from(this.transformKprqToDqyf(targetTime).atZone(ZoneId.systemDefault()).toInstant());
    }
    //生成合同签订日期
    public Date getHtqdrq(int monthAgo) {
        Calendar calendar = Calendar.getInstance();
        // 设置为当前月的1号
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 设置日期倒退月数
        calendar.add(Calendar.MONTH, -monthAgo);
        // 在1号到最后一天之间随机选一个数
        int randomDay = 1 + RANDOM.nextInt(10 - 1);
        // 设置为随机选择的日子
        calendar.set(Calendar.DAY_OF_MONTH, randomDay);
        // 返回随机生成的日期
        return calendar.getTime();
    }
    //生成合同终止日期
    public Date getHtzzrq(int monthAgo) {
        Calendar calendar = Calendar.getInstance();
        // 设置日期倒退月数
        calendar.add(Calendar.MONTH, -monthAgo);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }
}
