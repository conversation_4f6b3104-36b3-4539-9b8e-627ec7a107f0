package com.css.znsb.tzzx.service.yhs.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.alibaba.excel.EasyExcel;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.pojo.PageResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.gjss.constants.enums.TzlxDmEnum;
import com.css.znsb.gjss.constants.enums.YspzlxEnum;
import com.css.znsb.gjss.feign.jyss.JyssApi;
import com.css.znsb.gjss.mapper.yhs.httz.GsdmDlMapper;
import com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxHtmxDlMapper;
import com.css.znsb.gjss.mapper.yhs.httz.ZnsbTzzxYhslfkblMapper;
import com.css.znsb.gjss.pojo.domain.yhs.httz.*;
import com.css.znsb.gjss.pojo.dto.htmx.*;
import com.css.znsb.gjss.pojo.vo.yp.SbzbRequest;
import com.css.znsb.gjss.pojo.vo.yp.SbzbResponse;
import com.css.znsb.gjss.pojo.vo.yp.httz.HttzJyssDTO;
import com.css.znsb.gjss.service.yhs.httz.impl.ZnsbTzzxHtzzServiceImpl;
import com.css.znsb.gjss.util.GjssGyUtils;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.SfzrdmxxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.constants.BcztConstants;
import com.css.znsb.nssb.constants.enums.NsqxEnum;
import com.css.znsb.nssb.mapper.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxMapper;
import com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhscjMapper;
import com.css.znsb.nssb.mapper.cchxwssb.yhs.ZnsbNssbYhslfkblMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.cchxwsnssb.ZnsbNssbCxsbtxxDO;
import com.css.znsb.nssb.pojo.domain.cchxwssb.yhs.ZnsbNssbYhscjDO;
import com.css.znsb.nssb.pojo.domain.cchxwssb.yhs.ZnsbNssbYhslfkblDO;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.tzzx.constants.enums.YzpzzlEnum;
import com.css.znsb.tzzx.pojo.dto.dlzg.HtmxParamDTO;
import com.css.znsb.tzzx.pojo.dto.dlzg.YhsExternalResponseDTO;
import com.css.znsb.tzzx.pojo.tzzx.httz.DfslxxVO;
import com.css.znsb.tzzx.service.yhs.YhsHtmxDlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: guorui
 * @Date: 2025/6/17 14:30
 * @Description:
 **/

@Service
@Slf4j
public class YhsHtmxDlServiceImpl implements YhsHtmxDlService {

    private static final Pattern SAFE_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\u0028\\u0029\\uff08\\uff09]+$");

    private static final String companyName = "大连华锐重工集团股份有限公司";

    private final String logTitle = "大连重工合同台账：";

    @Resource
    private ZnsbTzzxHtmxDlMapper znsbTzzxHtmxDlMapper;

    @Resource
    private ZnsbTzzxYhslfkblMapper yhslfkblMapper;

    @Resource
    private GsdmDlMapper gsdmDlMapper;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private ZnsbNssbCxsbtxxMapper cxsbtxxMapper;

    @Resource
    private ZnsbTzzxHtzzServiceImpl znsbTzzxHtzzService;

    @Resource
    private JyssApi jyssApi;

    @Resource
    private ZnsbNssbYhscjMapper yhscjMapper;

    @Resource
    private ZnsbNssbYhslfkblMapper nssbYhslfkblMapper;

    @Override
    public PageResult<ZnsbTzzxHtmxDlDO> queryListByCondition(YhsHtmxDlQueryParamDTO paramDTO) {
        return znsbTzzxHtmxDlMapper.getHttzDlPage(paramDTO);
    }

    @Override
    public BigDecimal queryHj(YhsHtmxDlQueryParamDTO paramDTO) {
        List<ZnsbTzzxHtmxDlDO> list = znsbTzzxHtmxDlMapper.getHttzDl(paramDTO);
        return list.isEmpty() ? BigDecimal.ZERO : list.stream().map(ZnsbTzzxHtmxDlDO::getBhsje).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    @Override
    public YhsHtmxDlQueryDetailDTO queryDetail(String uuid) {
        YhsHtmxDlQueryDetailDTO detailDTO = new YhsHtmxDlQueryDetailDTO();
        List<ZnsbTzzxYhslfkblDO> kblList = yhslfkblMapper.getYhslfkblByZbuuid(uuid);
        detailDTO.setSlrList(kblList.stream().filter(m -> (!GyUtils.isNull(m.getDfslrnsrsbh()) || !GyUtils.isNull(m.getDfslrmc()))).collect(Collectors.toList()));
        detailDTO.setJsList(kblList.stream().filter(m -> !GyUtils.isNull(m.getSjjsje())).collect(Collectors.toList()));
        return detailDTO;
    }

    @Override
    public Map<String, YhsHtmxDlQueryDetailDTO> queryDetailList(List<String> uuidList) {
        List<ZnsbTzzxYhslfkblDO> kblList = yhslfkblMapper.getYhslfkblByZbuuidList(uuidList);
        Map<String, List<ZnsbTzzxYhslfkblDO>> groupedByZbuuid = kblList.stream()
                .collect(Collectors.groupingBy(ZnsbTzzxYhslfkblDO::getZbuuid));
        return groupedByZbuuid.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            YhsHtmxDlQueryDetailDTO detailDTO = new YhsHtmxDlQueryDetailDTO();

                            // 设置当前zbuuid对应的slrList和jsList
                            List<ZnsbTzzxYhslfkblDO> currentList = entry.getValue();
                            detailDTO.setSlrList(currentList.stream().filter(m -> !GyUtils.isNull(m.getDfslrnsrsbh())).collect(Collectors.toList()));
                            detailDTO.setJsList(currentList.stream().filter(m -> !GyUtils.isNull(m.getSjjsje())).collect(Collectors.toList()));

                            return detailDTO;
                        }
                ));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(YhsHtmxDlInsertOrUpdateDetailDTO dto) {
        // 非空校验
        this.checkInsertOrUpdateDataIsNull(dto);
        // 业务校验
        String checkResult = this.checkInsertOrUpdateData(dto);
        if (StringUtils.isNotEmpty(checkResult)) {
            throw new RuntimeException("新增校验出错：" + checkResult);
        }
        try {
            Date now = new Date();
            ZnsbTzzxHtmxDlDO znsbTzzxHtmxDlDO = this.setZnsbTzzxHtmxDlDO(dto.getYhsHtmxInsertDetailDTO());
            znsbTzzxHtmxDlDO.setZzscbz("N");
            znsbTzzxHtmxDlDO.setJyssbz("N");
            znsbTzzxHtmxDlDO.setLrrq(dto.getYhsHtmxInsertDetailDTO().getLrrq() == null ? now : dto.getYhsHtmxInsertDetailDTO().getLrrq());
            znsbTzzxHtmxDlDO.setXgrq(dto.getYhsHtmxInsertDetailDTO().getXgrq() == null ? now : dto.getYhsHtmxInsertDetailDTO().getXgrq());
            znsbTzzxHtmxDlDO.setSjtbSj(now);
            znsbTzzxHtmxDlDO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
            znsbTzzxHtmxDlDO.setLrrsfid("ZNSB.TZZX");
            znsbTzzxHtmxDlDO.setYwqdDm("00");
            znsbTzzxHtmxDlDO.setSjcsdq("00000000000");
            znsbTzzxHtmxDlDO.setSjgsdq("00000000000");
            znsbTzzxHtmxDlDO.setCgbz("Y");

            // 插入主表
            znsbTzzxHtmxDlMapper.insert(znsbTzzxHtmxDlDO);

            // 插入附表
            List<ZnsbTzzxYhslfkblDO> combinedList = new ArrayList<>(this.buildYhslfkblList(now, dto, "1", znsbTzzxHtmxDlDO.getUuid()));
            if (!combinedList.isEmpty()) {
                yhslfkblMapper.insertBatch(combinedList);
            }

            // 处理子公司分包合同
            this.insertZgs(dto, znsbTzzxHtmxDlDO, combinedList);
            return 1;
        } catch (Exception e) {
            throw new RuntimeException("新增合同出错：", e);
        }
    }

    private void insertZgs(YhsHtmxDlInsertOrUpdateDetailDTO dto, ZnsbTzzxHtmxDlDO znsbTzzxHtmxDlDO, List<ZnsbTzzxYhslfkblDO> combinedList) {
        // 分包合同处理
        LambdaQueryWrapperX<DmZnsbGsdmDlDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(DmZnsbGsdmDlDO::getNsrsbh, dto.getYhsHtmxInsertDetailDTO().getNsrsbh());
        DmZnsbGsdmDlDO zgs = gsdmDlMapper.selectOne(queryWrapperX);
        if (!Objects.isNull(zgs) && zgs.getGslb().equals("3") && "Y".equals(znsbTzzxHtmxDlDO.getFbhtbj())) {
            List<ZnsbTzzxHtmxDlDO> htmxList = new ArrayList<>();
            List<ZnsbTzzxYhslfkblDO> yhslfkblList = new ArrayList<>();
            // 获取母公司djxh
            LambdaQueryWrapperX<DmZnsbGsdmDlDO> mgsQueryWrapperX = new LambdaQueryWrapperX<>();
            mgsQueryWrapperX.eq(DmZnsbGsdmDlDO::getGslb, "1");
            DmZnsbGsdmDlDO mgs = gsdmDlMapper.selectOne(mgsQueryWrapperX);
            if (Objects.isNull(mgs)) {
                throw new RuntimeException("未找到母公司配置，请核实");
            }
            ZnsbMhzcQyjbxxmxReqVO mgsNsrReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            mgsNsrReqVO.setNsrsbh(mgs.getNsrsbh());
            CommonResult<ZnsbMhzcQyjbxxmxResVO> mgsNsrxxRes = nsrxxApi.getNsrxxByNsrsbh(mgsNsrReqVO);
            if (Objects.isNull(mgsNsrxxRes) || Objects.isNull(mgsNsrxxRes.getData())) {
                throw new RuntimeException("获取母公司信息失败，请核实");
            }
            String mgsDjxh = mgsNsrxxRes.getData().getJbxxmxsj().stream()
                    .filter(jbxxmxsjVO -> "N".equals(jbxxmxsjVO.getKqccsztdjbz())).findFirst().get().getDjxh();
            ZnsbTzzxHtmxDlDO htmx1DTO = SerializationUtils.clone(znsbTzzxHtmxDlDO);
            ZnsbTzzxHtmxDlDO htmx2DTO = SerializationUtils.clone(znsbTzzxHtmxDlDO);
            String uuid1 = IdUtil.fastSimpleUUID();
            String uuid2 = IdUtil.fastSimpleUUID();
            htmx1DTO.setUuid(uuid1);
            htmx1DTO.setZbuuid(uuid1);
            htmx1DTO.setNsrsbh(mgs.getNsrsbh());
            htmx1DTO.setNsrmc(mgs.getGsmc());
            htmx1DTO.setDjxh(mgsDjxh);
            htmx1DTO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
            htmx2DTO.setUuid(IdUtil.fastSimpleUUID());
            htmx2DTO.setZbuuid(uuid2);
            htmx2DTO.setNsrsbh(mgs.getNsrsbh());
            htmx2DTO.setNsrmc(mgs.getGsmc());
            htmx2DTO.setDjxh(mgsDjxh);
            htmx2DTO.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
            htmxList.add(htmx1DTO);
            htmxList.add(htmx2DTO);

            ZnsbTzzxYhslfkblDO slr = combinedList.stream().filter(m -> !GyUtils.isNull(m.getDfslrnsrsbh())).findFirst().orElse(null);

            if (!Objects.isNull(slr) && GyUtils.isNotNull(slr.getDfslrmc())) {
                ZnsbTzzxYhslfkblDO yhslfkbl1DO = SerializationUtils.clone(slr);
                ZnsbTzzxYhslfkblDO yhslfkbl2DO = SerializationUtils.clone(slr);
                yhslfkbl1DO.setZbuuid(htmx1DTO.getUuid());
                yhslfkbl1DO.setUuid(IdUtil.fastSimpleUUID());
                yhslfkbl1DO.setDfslrnsrsbh(zgs.getNsrsbh());
                yhslfkbl1DO.setDfslrmc(zgs.getGsmc());
                yhslfkbl2DO.setZbuuid(htmx2DTO.getUuid());
                yhslfkbl2DO.setUuid(IdUtil.fastSimpleUUID());
                yhslfkblList.add(yhslfkbl1DO);
                yhslfkblList.add(yhslfkbl2DO);
            }
            // 插入主表
            znsbTzzxHtmxDlMapper.insertBatch(htmxList);
            // 插入附表
            yhslfkblMapper.insertBatch(yhslfkblList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteSelected(List<String> uuidList) {
        // 查询删除详情
        try {
            List<ZnsbTzzxHtmxDlDO> deleteList = znsbTzzxHtmxDlMapper.selectBatchIds(uuidList);
            // 循环生成调整数据
            List<ZnsbTzzxHtmxDlDO> tzList = new ArrayList<>();
            for (ZnsbTzzxHtmxDlDO delete : deleteList) {
                //原数据调账类型更改为2
                delete.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
                delete.setXgrq(new Date());
                tzList.add(delete);

                //新增调账类型为3的对应数据
                tzList.add(this.setNewZnsbTzzxYhslfkblDO(delete, TzlxDmEnum.TZHCJ.getTzlxDm()));
            }
            boolean deleteFlag = znsbTzzxHtmxDlMapper.insertOrUpdateBatch(tzList);
            if (deleteFlag) {
                //删除副表（逻辑删除）
                final List<ZnsbTzzxYhslfkblDO> yhslfkblList = yhslfkblMapper.getYhslfkblByZbuuidList(uuidList);
                yhslfkblList.forEach(m -> {
                    m.setScbz("1");
                    m.setScsj(new Date());
                });
                if (!GyUtils.isNull(yhslfkblList)) {
                    yhslfkblMapper.updateBatch(yhslfkblList);
                }
            } else return 0;
            return uuidList.size();
        } catch (Exception e) {
            throw new RuntimeException("删除合同出错：{}", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFailSelected(List<String> uuidList) {
        // 删除主表
        int deleteCount = znsbTzzxHtmxDlMapper.deleteBatchIds(uuidList);
        if (deleteCount > 0) {
            // 删除副表
            final List<ZnsbTzzxYhslfkblDO> yhslfkblList = yhslfkblMapper.getYhslfkblByZbuuidList(uuidList);
            List<String> yhslfkblListUuidList = yhslfkblList.stream().map(ZnsbTzzxYhslfkblDO::getUuid).collect(Collectors.toList());
            if (!yhslfkblListUuidList.isEmpty()) {
                yhslfkblMapper.deleteBatchIds(yhslfkblListUuidList);
            }
        }
        return deleteCount;
    }

    @Override
    public int deleteAll(YhsHtmxDlQueryParamDTO paramDTO) {
        List<ZnsbTzzxHtmxDlDO> deleteList = znsbTzzxHtmxDlMapper.getHttzDl(paramDTO);
        if (paramDTO.getCgbz().equals("Y")) {
            deleteList = deleteList.stream().filter(m -> GyUtils.isNull(m.getZzuuid())).collect(Collectors.toList());
        }
        if (deleteList.isEmpty()) {
            throw new RuntimeException("未查询到需要删除的数据");
        }
        List<String> uuidList = deleteList.stream().map(ZnsbTzzxHtmxDlDO::getUuid).collect(Collectors.toList());
        if ("Y".equals(paramDTO.getCgbz())) {
            this.deleteSelected(uuidList);
        } else {
            this.deleteFailSelected(uuidList);
        }
        return deleteList.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(YhsHtmxDlInsertOrUpdateDetailDTO dto) {
        // 非空校验
        this.checkInsertOrUpdateDataIsNull(dto);
        // 业务校验
        String checkResult = this.checkInsertOrUpdateData(dto);
        if (StringUtils.isNotEmpty(checkResult)) {
            throw new RuntimeException("更新校验出错：" + checkResult);
        }
        try {
            Date now = new Date();
            List<ZnsbTzzxHtmxDlDO> updateMxList = new ArrayList<>();

            ZnsbTzzxHtmxDlDO unit = this.setZnsbTzzxHtmxDlDO(dto.getYhsHtmxInsertDetailDTO());
            unit.setTzlxDm(TzlxDmEnum.XTZ.getTzlxDm());
            unit.setZzscbz("N");
            unit.setJyssbz("N");
            unit.setLrrq(now);
            unit.setSjtbSj(now);
            unit.setXgrq(now);
            unit.setLrrsfid("ZNSB.TZZX");
            unit.setYwqdDm("00");
            unit.setSjcsdq("00000000000");
            unit.setSjgsdq("00000000000");
            unit.setCgbz("Y");
            // 查询主表旧数据
            ZnsbTzzxHtmxDlDO oldUnit = znsbTzzxHtmxDlMapper.selectById(dto.getYhsHtmxInsertDetailDTO().getUuid());
            oldUnit.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
            oldUnit.setXgrq(now);
            // 如果主表记录之前是不成功 需要改成成功
            if ("N".equals(unit.getCgbz())) {
                oldUnit.setCgbz("Y");
            }
            updateMxList.add(oldUnit);
            updateMxList.add(unit);
            unit.setZbuuid(oldUnit.getZbuuid());
            //生成主表调账冲减记录
            updateMxList.add(this.setNewZnsbTzzxYhslfkblDO(oldUnit, TzlxDmEnum.TZHCJ.getTzlxDm()));

            // 修改主表
            boolean mxUpdateFlag = znsbTzzxHtmxDlMapper.insertOrUpdateBatch(updateMxList);

            if (mxUpdateFlag) {
                // 修改附表
                List<ZnsbTzzxYhslfkblDO> combinedList = buildYhslfkblList(now, dto, "2", unit.getUuid());
                if (!combinedList.isEmpty()) {
                    yhslfkblMapper.insertOrUpdateBatch(combinedList);
                }
                // 处理子公司分包合同
                this.insertZgs(dto, unit, combinedList);
            }
            return 1;
        } catch (Exception e) {
            throw new RuntimeException("更新合同出错：{}", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> uploadExcel(MultipartFile file) throws IOException {
        log.info("印花税合同明细导入开始");
        final Map<String, Object> returnMap = new HashMap<>();

        //校验excel文件
        if (GyUtils.isNull(file)) {
            returnMap.put("code", "01");
            returnMap.put("msg", "请选择Excel文件");
            log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }

        //校验文件类型
        final String fullName = file.getOriginalFilename();
        final String fillType = fullName.substring(fullName.lastIndexOf(".") + 1);
        if (!"xls".equals(fillType) && !"xlsx".equals(fillType)) {
            returnMap.put("code", "01");
            returnMap.put("msg", "文件类型不正确");
            log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }

        HtmxDlUploadListener listener = new HtmxDlUploadListener();
        try {
            EasyExcel.read(file.getInputStream(), HtmxDlExcelDTO.class, listener)
                    .headRowNumber(1) // 表头在第2行（索引从0开始）
                    .sheet()
                    .doRead();
        } catch (IllegalArgumentException e) {
            returnMap.put("code", "01");
            returnMap.put("msg", "上传的Excel模板不正确，请检查表头字段和顺序");
            log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }
        final List<HtmxDlExcelDTO> excelList = listener.getDataList();

        //excel内容校验
        if (GyUtils.isNull(excelList) || excelList.isEmpty()) {
            //校验空表格
            returnMap.put("code", "01");
            returnMap.put("msg", "导入失败：导入文件内容为空");
            log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }

        // 查询所管辖下所有企业登记序号
        final List<String> djxhList = ZnsbSessionUtils.getKczDjxhList();
        // 初始化单位代码列表
        List<DmZnsbGsdmDlDO> gsdmList = gsdmDlMapper.getGsdmList();
        // 构建子公司map
        Map<String, List<DmZnsbGsdmDlDO>> zgsMap = gsdmList.stream().filter(item -> item.getGslb().equals("3"))
                .collect(Collectors.groupingBy(DmZnsbGsdmDlDO::getNsrsbh));
        // 母公司List
        List<DmZnsbGsdmDlDO> mgsList = gsdmList.stream().filter(item -> item.getGslb().equals("1")).collect(Collectors.toList());
        // 获取母公司djxh
        ZnsbMhzcQyjbxxmxReqVO mgsNsrReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        mgsNsrReqVO.setNsrsbh(mgsList.get(0).getNsrsbh());
        CommonResult<ZnsbMhzcQyjbxxmxResVO> mgsNsrxxRes = nsrxxApi.getNsrxxByNsrsbh(mgsNsrReqVO);
        if (Objects.isNull(mgsNsrxxRes) || Objects.isNull(mgsNsrxxRes.getData())) {
            returnMap.put("code", "01");
            returnMap.put("msg", "导入失败：获取母公司信息失败，请核实");
            log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
            return returnMap;
        }
        String mgsDjxh = mgsNsrxxRes.getData().getJbxxmxsj().stream()
                .filter(jbxxmxsjVO -> "N".equals(jbxxmxsjVO.getKqccsztdjbz())).findFirst().get().getDjxh();

        //行内数据校验，生成DOList
        List<ZnsbTzzxHtmxDlDO> inportDOList = new ArrayList<>();
        List<ZnsbTzzxYhslfkblDO> yhslfkblList = new ArrayList<>();

        for (HtmxDlExcelDTO excel : excelList) {

            //获取行数据
            final String xh = excel.getXh();

            //必录数据校验
            if (GyUtils.isNull(excel.getNsrsbh())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，纳税人识别号不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getNsrmc())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，纳税人名称不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getGsdm())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，公司代码不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getYzpzbh())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，应税凭证编号不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getYzpzmc())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，应税凭证名称不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getYspzslrq())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，应税凭证书立日期不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getJsje())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，计税金额不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getBhshtebz())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，是否不含税合同额不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getJryhsbz())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，是否计入印花税不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getBzshtbz())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，是否是不征税合同不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getFbhtbj())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，是否为集团分包合同不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            if (GyUtils.isNull(excel.getKjhtbj())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：存在未填写的必录项（序号" + xh + "，是否是框架合同不能为空）");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }

            // 业务校验 返回不成功原因 不成功原因为空，成功标志就为Y，反之为N
            String sbyy = this.checkImportData(excel);
            String cgbz = StringUtils.isEmpty(sbyy) ? "Y" : "N";

            //获取djxh
            ZnsbMhzcQyjbxxmxReqVO nsrreqVO = new ZnsbMhzcQyjbxxmxReqVO();
            nsrreqVO.setNsrsbh(excel.getNsrsbh());
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByNsrsbh(nsrreqVO);
            if (Objects.isNull(nsrxxRes) || Objects.isNull(nsrxxRes.getData())) {
                returnMap.put("code", "01");
                returnMap.put("msg", "导入失败：序号" + xh + "，根据纳税人识别号，查询不到企业信息，请核实");
                log.info("印花税合同明细导入结束，导入失败，返回参数：{}", JsonUtils.toJson(returnMap));
                return returnMap;
            }
            String djxh = nsrxxRes.getData().getJbxxmxsj().stream()
                    .filter(jbxxmxsjVO -> "N".equals(jbxxmxsjVO.getKqccsztdjbz())).findFirst().get().getDjxh();
            log.info("当前管理的企业列表：{}", djxhList);
            if (!Objects.isNull(djxhList) && !djxhList.contains(djxh)) {
                sbyy = "当前企业不在管理企业范围内，请核实";
                cgbz = "N";
            }

            // 插入主表
            ZnsbTzzxHtmxDlDO dto = new ZnsbTzzxHtmxDlDO();
            Date now = new Date();
            // 主表uuid
            String uuid = IdUtil.fastSimpleUUID();
            dto.setUuid(uuid);
            dto.setZbuuid(uuid);
            dto.setNsrsbh(excel.getNsrsbh());
            dto.setNsrmc(excel.getNsrmc());
            dto.setGsh2(excel.getGsdm());
            dto.setDjxh(djxh);
            dto.setSszq(Integer.parseInt(excel.getYspzslrq().replaceAll("-", "").substring(0, 6)));
            dto.setYwfw(excel.getYwfw());
            dto.setYzpzbh(excel.getYzpzbh());
            dto.setHtlx(YspzlxEnum.getYspzlxByName(excel.getYzpzmc()));
            dto.setHtzl(excel.getHtzl());
            dto.setHtqdrq(DateUtil.toDate("yyyy-MM-dd", excel.getYspzslrq()));
            dto.setBhsje(excel.getJsje());
            dto.setYdbz(this.getYesOrNoName(excel.getYdbz()));
            dto.setBhshtebz(this.getYesOrNoName(excel.getBhshtebz()));
            dto.setJryhsbz(this.getYesOrNoName(excel.getJryhsbz()));
            dto.setBzshtbz(this.getYesOrNoName(excel.getBzshtbz()));
            dto.setBzsyy(excel.getBzsyy());
            dto.setSldd(excel.getSldd());
            dto.setFbhtbj(this.getYesOrNoName(excel.getFbhtbj()));
            dto.setKjhtbj(this.getYesOrNoName(excel.getKjhtbj()));
            dto.setJsrqq(StringUtils.isNotEmpty(excel.getJsrqq()) ? DateUtil.toDate("yyyy-MM-dd", excel.getJsrqq()) : null);
            dto.setJsrqz(StringUtils.isNotEmpty(excel.getJsrqz()) ? DateUtil.toDate("yyyy-MM-dd", excel.getJsrqz()) : null);
            dto.setBz(excel.getBz());
            dto.setTzlxDm(TzlxDmEnum.WTZ.getTzlxDm());
            dto.setCgbz(cgbz);
            dto.setSbyy(sbyy);
            //录入固定字段
            dto.setZzscbz("N");
            dto.setJyssbz("N");
            dto.setLrrq(now);
            dto.setXgrq(now);
            dto.setLrrsfid("ZNSB.TZZX");
            dto.setYwqdDm("00");
            dto.setSjcsdq("00000000000");
            dto.setSjgsdq("00000000000");
            dto.setSjtbSj(now);
            inportDOList.add(dto);

            // 如果有书立人信息 插入附表
            ZnsbTzzxYhslfkblDO yhslfkblDO = new ZnsbTzzxYhslfkblDO();
            if (GyUtils.isNotNull(excel.getDfslrmc()) && GyUtils.isNotNull(excel.getDfslrsjje())) {
                yhslfkblDO.setUuid(IdUtil.fastSimpleUUID());
                yhslfkblDO.setZbuuid(uuid);
                yhslfkblDO.setDfslrmc(excel.getDfslrmc());
                yhslfkblDO.setDfslrnsrsbh(excel.getDfslrnssbh());
                yhslfkblDO.setDfslrsjje(excel.getDfslrsjje());
                yhslfkblDO.setZspmDm(excel.getYzpzmc());
                yhslfkblDO.setScbz("0");
                yhslfkblDO.setScsj(now);
                yhslfkblDO.setZzscbz("N");
                yhslfkblDO.setJyssbz("N");
                yhslfkblDO.setLrrq(now);
                yhslfkblDO.setXgrq(now);
                yhslfkblDO.setLrrsfid("ZNSB.TZZX");
                yhslfkblDO.setYwqdDm("TZ_USER");
                yhslfkblDO.setSjcsdq("00000000000");
                yhslfkblDO.setSjgsdq("00000000000");
                yhslfkblDO.setSjtbSj(now);
                yhslfkblList.add(yhslfkblDO);
            }

            // 如果子公司身份下导入合同，合同是否为分包合同显示为“是”，并且分包合同在母公司（非分公司）中，
            // 子公司合同明细正常生成模板合同数据，也需要在母公司下产生2条合同对应2个税源
            // （母公司生同样合同编号的两条数据，一条对方书立人是子公司，另一条是子公司的对方书立人。）
            if ("是".equals(excel.getFbhtbj()) && zgsMap.containsKey(excel.getNsrsbh())) {
                ZnsbTzzxHtmxDlDO htmx1DTO = SerializationUtils.clone(dto);
                ZnsbTzzxHtmxDlDO htmx2DTO = SerializationUtils.clone(dto);
                String uuid1 = IdUtil.fastSimpleUUID();
                String uuid2 = IdUtil.fastSimpleUUID();
                htmx1DTO.setUuid(uuid1);
                htmx1DTO.setZbuuid(uuid1);
                htmx1DTO.setNsrsbh(mgsList.get(0).getNsrsbh());
                htmx1DTO.setNsrmc(mgsList.get(0).getGsmc());
                htmx1DTO.setDjxh(mgsDjxh);
                htmx2DTO.setUuid(uuid2);
                htmx2DTO.setZbuuid(uuid2);
                htmx2DTO.setNsrsbh(mgsList.get(0).getNsrsbh());
                htmx2DTO.setNsrmc(mgsList.get(0).getGsmc());
                htmx2DTO.setDjxh(mgsDjxh);
                inportDOList.add(htmx1DTO);
                inportDOList.add(htmx2DTO);

                if (GyUtils.isNotNull(excel.getDfslrmc()) && GyUtils.isNotNull(excel.getDfslrsjje())) {
                    ZnsbTzzxYhslfkblDO yhslfkbl1DO = SerializationUtils.clone(yhslfkblDO);
                    ZnsbTzzxYhslfkblDO yhslfkbl2DO = SerializationUtils.clone(yhslfkblDO);
                    yhslfkbl1DO.setZbuuid(htmx1DTO.getUuid());
                    yhslfkbl1DO.setUuid(IdUtil.fastSimpleUUID());
                    yhslfkbl1DO.setDfslrnsrsbh(zgsMap.get(dto.getNsrsbh()).get(0).getNsrsbh());
                    yhslfkbl1DO.setDfslrmc(zgsMap.get(dto.getNsrsbh()).get(0).getGsmc());
                    yhslfkbl2DO.setZbuuid(htmx2DTO.getUuid());
                    yhslfkbl2DO.setUuid(IdUtil.fastSimpleUUID());
                    yhslfkblList.add(yhslfkbl1DO);
                    yhslfkblList.add(yhslfkbl2DO);
                }
            }
        }

        try {
            // 插入主表
            znsbTzzxHtmxDlMapper.insertBatch(inportDOList);
            // 插入附表
            yhslfkblMapper.insertBatch(yhslfkblList);
        } catch (Exception e) {
            throw new RuntimeException("数据插入异常，{}", e);
        }

        //写入返回信息
        returnMap.put("code", "00");
        returnMap.put("msg", "本次共导入" + inportDOList.size() + "条记录");

        log.info("印花税合同明细导入结束，导入成功，返回参数：{}", JsonUtils.toJson(returnMap));
        return returnMap;
    }

    /**
     * @param limit
     * @param days
     * @return boolean
     * @name 合同台账交易算税投递及生成总账
     * @description 相关说明
     * @history 修订历史（历次修订内容、修订人、修订时间等）
     */
    @Override
    public boolean httzZzCalculate(long limit, int days) {
        log.info(logTitle + "总账计算开始");

        //取最近的两个分区
        LocalDate startTime = LocalDate.now().minusDays(days);
        LocalDate endTime = LocalDate.now().plusDays(1);

        //获取需汇总数据
        final List<ZnsbTzzxHtmxDlDO> jyssData = znsbTzzxHtmxDlMapper.getJyssData(limit, startTime, endTime);
        log.info(logTitle + "需投递数据条目" + jyssData.size());

        //按登记序号、所属账期、征收品目、征收子目、合同编号分类，进行交易算税投递
        Function<ZnsbTzzxHtmxDlDO, List<Object>> compositeKeyJyss = mxbDO ->
                Arrays.asList(mxbDO.getDjxh(), mxbDO.getSszq(), mxbDO.getHtlx(), mxbDO.getHtzl(), mxbDO.getYzpzbh());
        Map<List<Object>, List<ZnsbTzzxHtmxDlDO>> groupingMapJyss =
                jyssData.stream().collect(Collectors.groupingBy(compositeKeyJyss, Collectors.toList()));

        groupingMapJyss.forEach((keys, httzDOS) -> {
            try {
                //查询纳税人信息
                ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                znsbMhzcQyjbxxmxReqVO.setDjxh(httzDOS.get(0).getDjxh());
                CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
                List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
                final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
                final String zgswjDm = jbxxmxsj.getZgswjDm();

                //获取税款所属期
                final Map<String, String> skssqqzMap = GjssGyUtils.calYhsSkssq(sfzrdmxxx, Integer.toString(httzDOS.get(0).getSszq()), httzDOS.get(0).getHtlx(),
                        httzDOS.get(0).getHtzl(), httzDOS.get(0).getHtqdrq());
                final String skssqq = skssqqzMap.get("skssqq");
                final String skssqz = skssqqzMap.get("skssqz");

                //查询表头信息
                List<ZnsbNssbCxsbtxxDO> btxx = cxsbtxxMapper.getCxsbtxx(httzDOS.get(0).getDjxh(), skssqq, skssqz);
                if (!GyUtils.isNull(btxx)) {
                    //如果该表头当前保存状态正在处理中或者已申报（其实是申报中），不生成总账
                    if (BcztConstants.CLZ.equals(btxx.get(0).getBczt()) || BcztConstants.SBZ.equals(btxx.get(0).getBczt())) {
                        return;
                    }
                }

                //获取纳税期限，获取不到则默认按次
                final String nsqxDm = sfzrdmxxx.stream()
                        .filter(sfzrdmxxxVO -> httzDOS.get(0).getHtlx().equals(sfzrdmxxxVO.getZspmDm())).map(SfzrdmxxxVO::getNsqxDm).findFirst().orElse(NsqxEnum.TIME.getDm());

                //2024.11.13:按次申报不在汇总时进行交易算税投递
                if (NsqxEnum.TIME.getDm().equals(nsqxDm)) {
                    return;
                }

                //更新标志List
                final List<ZnsbTzzxHtmxDlDO> gxJyssbzList = new ArrayList<>();

                //通过调账记录生成blnr
                final List<HttzJyssDTO> blnr = new ArrayList<>();
                for (ZnsbTzzxHtmxDlDO httzDO : httzDOS) {
                    final HttzJyssDTO blnrUnit = new HttzJyssDTO();
                    blnrUnit.setYwfsrq(DateUtils.dateToString(httzDO.getHtqdrq(), 3));//2024.10.17:业务发生时间改为取合同签订日期DateUtils.getSystemCurrentTime(3)
                    blnrUnit.setNsqx(nsqxDm);
                    blnrUnit.setYspzlxDm(YspzlxEnum.getYspzlxByZspm(httzDO.getHtlx()));
                    blnrUnit.setYspzbh(httzDO.getYzpzbh());
                    //                blnrUnit.setZy();
                    //                blnrUnit.setBz();
                    blnrUnit.setSspzhm(httzDO.getUuid());
                    blnrUnit.setZspmDm(httzDO.getHtlx());
                    blnrUnit.setZszmDm(httzDO.getHtzl());
                    blnrUnit.setYspzmc(CacheUtils.dm2mc("dm_gy_zspm", httzDO.getHtlx()));
                    blnrUnit.setYspzsl("1");
                    blnrUnit.setJmse(BigDecimal.ZERO);


                    //业务分类
                    if ("101110200".equals(httzDO.getHtlx())) {
                        //产权转移书据
                        if (TzlxDmEnum.WTZ.getTzlxDm().equals(httzDO.getTzlxDm())) {
                            blnrUnit.setYwfl("CQ001");
                        } else {
                            blnrUnit.setYwfl("CQ002");
                        }
                    } else {
                        //其他类型
                        if (TzlxDmEnum.WTZ.getTzlxDm().equals(httzDO.getTzlxDm())) {
                            blnrUnit.setYwfl("HT001");
                        } else {
                            blnrUnit.setYwfl("HT002");
                        }
                    }

                    //计算金额
                    blnrUnit.setJe(httzDO.getBhsje());

                    //获取税率（2024.10.12：更新税率获取规则，先从税费种认定开始获取，获取不到再从关联表获取）
                    //                    Map<String, Object> zspmData = CacheUtils.getTableData("dm_gy_zspm", httzDO.getZspmDm());
                    //                    final BigDecimal sl = BigDecimal.valueOf((Double) zspmData.get("sl1"));
                    String slStr = skssqqzMap.get("sl");//先从税费种认定里获取
                    if (GyUtils.isNull(slStr)) {
                        //若税费种认定没有，使用公共方法查询表cs_gy_glb_zspm
                        slStr = znsbTzzxHtzzService.getYhsSlByRedis("10111", httzDO.getHtlx(), httzDO.getHtzl(), skssqq, skssqz, zgswjDm);
                    }
                    final BigDecimal sl = new BigDecimal(slStr);


                    blnrUnit.setSl(sl);

                    //计算税额
                    blnrUnit.setSe(httzDO.getBhsje().multiply(sl));

                    //加入List
                    blnr.add(blnrUnit);

                    //记录交易算税更新
                    final ZnsbTzzxHtmxDlDO gxJyssbzUnit = new ZnsbTzzxHtmxDlDO();
                    gxJyssbzUnit.setUuid(httzDO.getUuid());
                    gxJyssbzUnit.setJyssbz("Y");
                    gxJyssbzList.add(gxJyssbzUnit);
                }

                //装填算税接口基础参数
                SbzbRequest sbzbRequest = new SbzbRequest();
                sbzbRequest.setDjxh(httzDOS.get(0).getDjxh());
                sbzbRequest.setSspzhm(httzDOS.get(0).getUuid());
                sbzbRequest.setSsywflDm("0520");//0520 补录-印花税-合同采集
                sbzbRequest.setYwfsrq(DateUtils.getSystemCurrentTime(3));
                sbzbRequest.setSkssqq(skssqq);
                sbzbRequest.setSkssqz(skssqz);
                sbzbRequest.setXwgzlxDm("0");
                sbzbRequest.setZsxmDm("10111");
                sbzbRequest.setBlnr(JsonUtils.toJson(blnr));

                //调用交易算税接口
                log.info(logTitle + "调用交易算税接口请求：" + JsonUtils.toJson(sbzbRequest));
                final CommonResult<SbzbResponse> result = jyssApi.sbzb(sbzbRequest);
                log.info(logTitle + "调用交易算税接口返回：" + JsonUtils.toJson(result));

                Integer code = result.getCode();
                if (!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code)) {
                    throw new RuntimeException(result.getMsg());
                }

                //更改交易算税标志
                znsbTzzxHtmxDlMapper.updateBatch(gxJyssbzList);
            } catch (Exception e) {
                log.error(logTitle + "交易算税投递接口调用失败，分片信息：" + JsonUtils.toJson(keys), e);
//                    throw new RuntimeException(e.getMessage());
            }
        });

        //获取需汇总数据
        final List<ZnsbTzzxHtmxDlDO> zzjsData = znsbTzzxHtmxDlMapper.getZzjsData(limit, startTime, endTime);
        log.info(logTitle + "需汇总数据条目" + zzjsData.size());

        //按登记序号、所属账期、征收品目、征收子目分类
        final Map<String, CommonResult<ZnsbMhzcQyjbxxmxResVO>> nsrxxMap = new HashMap<>();
//        Function<ZnsbTzzxHtmxDlDO, List<Object>> compositeKey = mxbDO ->
//                Arrays.asList(mxbDO.getDjxh(), mxbDO.getSszq(), mxbDO.getHtlx(), mxbDO.getHtzl());
        Map<String, List<ZnsbTzzxHtmxDlDO>> groupingMap =
                zzjsData.stream().collect(Collectors.groupingBy(ZnsbTzzxHtmxDlDO::getZbuuid));

        // 待汇总数据按照需要进行汇总
        groupingMap.forEach((key, htmxList) -> {
            //获取参数
            final String djxh = htmxList.get(0).getDjxh();
            final String sszq = htmxList.get(0).getSszq().toString();
            final String zspmDm = htmxList.get(0).getHtlx();
            final String zszmDm = htmxList.get(0).getHtzl();

            //查询纳税人信息
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh;
            if (nsrxxMap.containsKey(djxh)) {
                //获取过的情况直接取值
                nsrxxByNsrsbh = nsrxxMap.get(djxh);
            } else {
                //未获取过的情况调用接口获取
                String systemCurrentTime1 = DateUtils.getSystemCurrentTime(14);
                ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
                nsrxxByNsrsbh = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
                nsrxxMap.put(djxh, nsrxxByNsrsbh);
                String systemCurrentTime2 = DateUtils.getSystemCurrentTime(14);
                log.info(logTitle + "获取nsrxx开始时间：" + systemCurrentTime1 + ",结束时间：" + systemCurrentTime2);
            }
            List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();

            //获取纳税期限，获取不到则默认按次
            final String nsqxDmTemp = sfzrdmxxx.stream()
                    .filter(sfzrdmxxxVO -> zspmDm.equals(sfzrdmxxxVO.getZspmDm())).map(SfzrdmxxxVO::getNsqxDm).findFirst().orElse("no sfzrd");
            final String nsqxDm = nsqxDmTemp.equals("no sfzrd") ? NsqxEnum.TIME.getDm() : nsqxDmTemp;
            final String sfsfzrd = nsqxDmTemp.equals("no sfzrd") ? "N" : "Y";

            this.httzZzsc(htmxList, djxh, sszq, zspmDm, zszmDm, nsqxDm, sfsfzrd, nsrxxByNsrsbh);

        });

        log.info(logTitle + "总账计算结束");

        return true;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void httzZzsc(List<ZnsbTzzxHtmxDlDO> htmxList, String djxh, String sszq, String zspmDm, String zszmDm,
                         String nsqxDm, String sfsfzrd, CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh) {
        //查询纳税人信息
        List<SfzrdmxxxVO> sfzrdmxxx = nsrxxByNsrsbh.getData().getSfzrdmxxx();
        final JbxxmxsjVO jbxxmxsj = nsrxxByNsrsbh.getData().getJbxxmxsj().get(0);
        final String zgswjDm = jbxxmxsj.getZgswjDm();

        //获取税款所属期
        final Map<String, String> skssqqzMap = GjssGyUtils.calYhsSkssq(sfzrdmxxx, sszq, zspmDm, zszmDm, htmxList.get(0).getHtqdrq());
        final String skssqq = skssqqzMap.get("skssqq");
        final String skssqz = skssqqzMap.get("skssqz");
        final String sbqxlx = skssqqzMap.get("sbqxlx");

        //获取税率（2024.10.12：更新税率获取规则，先从税费种认定开始获取，获取不到再从关联表获取）
//        Map<String, Object> zspmData = CacheUtils.getTableData("dm_gy_zspm", zspmDm);
//        final BigDecimal sl = BigDecimal.valueOf((Double) zspmData.get("sl1"));
        String slStr = skssqqzMap.get("sl");//先从税费种认定里获取
        if (GyUtils.isNull(slStr)) {
            String systemCurrentTime1 = DateUtils.getSystemCurrentTime(14);
            //若税费种认定没有，使用公共方法查询表cs_gy_glb_zspm
            slStr = znsbTzzxHtzzService.getYhsSlByRedis("10111", zspmDm, zszmDm, skssqq, skssqz, zgswjDm);
            String systemCurrentTime2 = DateUtils.getSystemCurrentTime(14);
            log.info(logTitle + "获取税率开始时间：" + systemCurrentTime1 + ",结束时间：" + systemCurrentTime2);
        }
        final BigDecimal sl = new BigDecimal(slStr);

        //查询表头信息
        String btxxStart = DateUtils.getSystemCurrentTime(14);
        List<ZnsbNssbCxsbtxxDO> btxx = cxsbtxxMapper.getCxsbtxx(djxh, skssqq, skssqz);
        String btxxEnd = DateUtils.getSystemCurrentTime(14);
        log.info(logTitle + "查询表头时间：" + btxxStart + "|" + btxxEnd);
        final String zbuuid;
        final String bczt;
        if (GyUtils.isNull(btxx)) {
            //如果表头信息不存在，则构建表头
            zbuuid = IdUtil.fastSimpleUUID();
            bczt = BcztConstants.DTJ;
            final ZnsbNssbCxsbtxxDO newBt = new ZnsbNssbCxsbtxxDO();
            newBt.setUuid(zbuuid);
            newBt.setDjxh(new BigDecimal(djxh));
            newBt.setYzpzzlDm(YzpzzlEnum.YHS.getDm());
            newBt.setSbsxDm1("11");
            newBt.setSkssqq(GyUtils.cast2Date(skssqq));
            newBt.setSkssqz(GyUtils.cast2Date(skssqz));

            newBt.setZgswskfjDm(jbxxmxsj.getZgswskfjDm());
            newBt.setZfbz1("N");
            newBt.setYsbbz("N");
            newBt.setHyDm(jbxxmxsj.getHyDm());
            newBt.setXzqhszDm(jbxxmxsj.getZcdzxzqhszDm());
            newBt.setJdxzDm(jbxxmxsj.getJdxzDm());
            newBt.setBczt(BcztConstants.DTJ);

            //录入固定字段
            newBt.setLrrq(new Date());
            newBt.setXgrq(new Date());
            newBt.setLrrsfid("ZNSB.TZZX");
            newBt.setYwqdDm("TZ_SYS");
            newBt.setSjcsdq("00000000000");
            newBt.setSjgsdq("00000000000");
            newBt.setSjtbSj(new Date());

            //写入表头
            cxsbtxxMapper.insert(newBt);

            //表头首次写入进行零申报初始化
            znsbTzzxHtzzService.yhstzLsbInit(sfzrdmxxx, zbuuid, skssqq, skssqz);
        } else {
            //如果表头存在，获取uuid
            zbuuid = btxx.get(0).getUuid();
            bczt = btxx.get(0).getBczt();

            //如果该表头当前保存状态正在处理中或者已申报（其实是申报中），不生成总账
            if (BcztConstants.CLZ.equals(btxx.get(0).getBczt()) || BcztConstants.SBZ.equals(btxx.get(0).getBczt())) {
                return;
            }
        }

        //2025.04.10：合同信息分类集合，按登记序号、利润中心、合同标题、客户编号、供应商编码分类（客户编号和供应商编码不会同时存在）
        final List<String> htxxFlList = new ArrayList<>();

        //循环计算计税依据和原始凭证数量(新数据部分)
        final List<String> zzjsRecordUuidList = new ArrayList<>();
        int yspzsl = 0;
        BigDecimal jsyj = BigDecimal.ZERO;
        String ynspzbh = "";
        for (ZnsbTzzxHtmxDlDO htmx : htmxList) {
            //记录历史记录uuid
            zzjsRecordUuidList.add(htmx.getUuid());

            //计算计税依据
            jsyj = jsyj.add(htmx.getBhsje());

            //计算原始凭证数量，记录调账类型为1和4的
            if (TzlxDmEnum.WTZ.getTzlxDm().equals(htmx.getTzlxDm())
                    || TzlxDmEnum.XTZ.getTzlxDm().equals(htmx.getTzlxDm())) {
                yspzsl++;
            }

            //记录一条合同编号
            if (GyUtils.isNull(ynspzbh) && !GyUtils.isNull(htmx.getYzpzbh())) {
                ynspzbh = htmx.getYzpzbh();
            }
        }

        //获得该类总账生成的历史记录
        String zzjsStart = DateUtils.getSystemCurrentTime(14);
        final List<ZnsbTzzxHtmxDlDO> zzjsRecord = znsbTzzxHtmxDlMapper.getZzjsRecord(djxh, zbuuid, htmxList.get(0).getZbuuid());
        String zzjsEnd = DateUtils.getSystemCurrentTime(14);
        log.info(logTitle + "获得总账下其他已汇总合同时间：" + zzjsStart + "|" + zzjsEnd);

        //循环计算计税依据和原始凭证数量(历史记录部分)
        for (ZnsbTzzxHtmxDlDO zzjsUnit : zzjsRecord) {
            //修改时，新数据也会在历史记录被查询到，需要排除
            if (zzjsRecordUuidList.contains(zzjsUnit.getUuid())) {
                continue;
            }

            //记录历史记录uuid
            zzjsRecordUuidList.add(zzjsUnit.getUuid());

            //计算计税依据
            jsyj = jsyj.add(zzjsUnit.getBhsje());

            //计算原始凭证数量，记录调账类型为1和4的
            if (TzlxDmEnum.WTZ.getTzlxDm().equals(zzjsUnit.getTzlxDm())
                    || TzlxDmEnum.XTZ.getTzlxDm().equals(zzjsUnit.getTzlxDm())) {
                yspzsl++;
            }
        }

        //更新或新增总账
        final String zzuuid;
        final List<ZnsbNssbYhscjDO> yhscjJgList = new ArrayList<>();
        final List<ZnsbNssbYhscjDO> yhscjRecord = yhscjMapper.getDlzgYhscjRecord(zbuuid, htmxList.get(0).getZbuuid());
        log.info(logTitle + "税源明细查询历史记录结束时间：" + DateUtils.getSystemCurrentTime(14));
        if (GyUtils.isNull(yhscjRecord)) {
            //新增
            final ZnsbNssbYhscjDO newYhscj = new ZnsbNssbYhscjDO();
            zzuuid = IdUtil.fastSimpleUUID();
            newYhscj.setUuid(zzuuid);
            newYhscj.setZbuuid(zbuuid);
            newYhscj.setZspmDm(zspmDm);
            newYhscj.setZszmDm(zszmDm);
            newYhscj.setSkssqq(GyUtils.cast2Date(skssqq));
            newYhscj.setSkssqz(GyUtils.cast2Date(skssqz));
            newYhscj.setJsjehjs(jsyj);
            newYhscj.setSl1(sl);
            newYhscj.setYnse(jsyj.multiply(sl));
            newYhscj.setYbtse(jsyj.multiply(sl));
            newYhscj.setNsqxDm(nsqxDm);
            newYhscj.setZfbz1("N");
            newYhscj.setYnspzbh(GyUtils.isNull(ynspzbh) ? null : ynspzbh);
            newYhscj.setBczt(BcztConstants.DTJ);
            newYhscj.setHdbl(BigDecimal.ONE);
            newYhscj.setYsbbz("N");
            newYhscj.setYspzsl(Long.valueOf(yspzsl));
            newYhscj.setSfsfzrd(sfsfzrd);
            newYhscj.setSbqxlx(sbqxlx);//2024.12.20修改，按期取税费种认定，按次取04//00按期申报、01按次申报
            newYhscj.setYspzmc(CacheUtils.dm2mc("dm_gy_zspm", zspmDm));
            newYhscj.setYnspzsllsrq(htmxList.get(0).getHtqdrq());
            newYhscj.setSbsxdm1("11");
            newYhscj.setHxbmxuuid(htmxList.get(0).getZbuuid());

            //2024.12.26：税源明细应纳税额如果为0，则金额和应税凭证数量也需要置为0
            //2025.01.21：如果数值为负，也置为0
            final BigDecimal ynseSswr = newYhscj.getYnse().setScale(2, BigDecimal.ROUND_HALF_UP);
            if (jsyj.compareTo(BigDecimal.ZERO) <= 0 || ynseSswr.compareTo(BigDecimal.ZERO) <= 0) {
                newYhscj.setYspzsl(0L);
                newYhscj.setYnse(BigDecimal.ZERO);
                newYhscj.setJsjehjs(BigDecimal.ZERO);
            }

            //录入固定字段
            newYhscj.setLrrq(new Date());
            newYhscj.setXgrq(new Date());
            newYhscj.setLrrsfid("ZNSB.TZZX");
            newYhscj.setYwqdDm("TZ_SYS");
            newYhscj.setSjcsdq("00000000000");
            newYhscj.setSjgsdq("00000000000");
            newYhscj.setSjtbSj(new Date());

            //是否已采集
            if (BcztConstants.DTJ.equals(bczt) || BcztConstants.CLSB.equals(bczt)) {
                //未采集
                newYhscj.setGzbz("0");

            } else {
                //2024.08.15新增：删除一条税源且还未点击生成税源时，页面显示改条税源，金额均为0，变更状态为已变更，高亮显示
                //已生成税源时，所有修改都为"已变更"
                newYhscj.setBgzt("Y");

                //将gzbz置为0(新增)
                newYhscj.setGzbz("0");

                //已采集，则将其他条目gzbz置为1
                List<ZnsbNssbYhscjDO> otheryhscjDO = yhscjMapper.getYhscjByZbuuid(zbuuid);
                for (ZnsbNssbYhscjDO unit : otheryhscjDO) {
                    //已经是新增未处理的或删除的不进行修改
                    if (unit.getGzbz().equals("0") || unit.getGzbz().equals("2")) {
                        continue;
                    }

                    unit.setGzbz("1");
                    yhscjJgList.add(unit);
                }
            }

            yhscjJgList.add(newYhscj);
        } else {
            //修改
            zzuuid = yhscjRecord.get(0).getUuid();
            yhscjRecord.get(0).setJsjehjs(jsyj);
            yhscjRecord.get(0).setYnse(jsyj.multiply(sl));
            yhscjRecord.get(0).setYbtse(jsyj.multiply(sl));
            yhscjRecord.get(0).setYspzsl(Long.valueOf(yspzsl));
//            yhscjRecord.get(0).setBczt(BcztConstants.DTJ);
            yhscjRecord.get(0).setXgrq(new Date());
            yhscjRecord.get(0).setXgrsfid("ZNSB.TZZX");
            if (GyUtils.isNull(yhscjRecord.get(0).getYnspzbh()) && !GyUtils.isNull(ynspzbh)) {
                yhscjRecord.get(0).setYnspzbh(ynspzbh);
            }

            //2024.12.26：税源明细应纳税额如果为0，则应税凭证数量也需要置为0
            final BigDecimal ynseSswr = yhscjRecord.get(0).getYnse().setScale(2, BigDecimal.ROUND_HALF_UP);
            if (jsyj.compareTo(BigDecimal.ZERO) <= 0 || ynseSswr.compareTo(BigDecimal.ZERO) <= 0) {
                yhscjRecord.get(0).setYspzsl(0L);
                yhscjRecord.get(0).setYnse(BigDecimal.ZERO);
                yhscjRecord.get(0).setJsjehjs(BigDecimal.ZERO);
            }

            //是否已采集
            if (BcztConstants.DTJ.equals(bczt) || BcztConstants.CLSB.equals(bczt)) {
                //未采集
                yhscjRecord.get(0).setGzbz("0");

//                //未采集如果删除，作废标志直接改为Y
//                if (BigDecimal.ZERO.compareTo(jsyj) == 0){
//                    yhscjRecord.get(0).setZfbz1("Y");
//                    yhscjRecord.get(0).setZfrq1(new Date());
//                }

            } else if (BcztConstants.SBCG.equals(bczt)) {
                //已申报的，保存状态保持已申报
                yhscjRecord.get(0).setBgzt("Y");
                yhscjRecord.get(0).setGzbz("1");
            } else {
                //2024.08.15新增：删除一条税源且还未点击生成税源时，页面显示改条税源，金额均为0，变更状态为已变更，高亮显示
                //已生成税源时，所有修改都为"已变更"
                yhscjRecord.get(0).setBgzt("Y");

                //正常更正
                yhscjRecord.get(0).setGzbz("1");

                //如果不为直接删除，则需改变其他条目更正状态
                if (!"Y".equals(yhscjRecord.get(0).getZfbz1())) {
                    //已采集，则将其他条目gzbz置为1
                    List<ZnsbNssbYhscjDO> otheryhscjDO = yhscjMapper.getYhscjByZbuuid(zbuuid);
                    for (ZnsbNssbYhscjDO unit : otheryhscjDO) {
                        //除去修改中的数据自身
                        if (yhscjRecord.get(0).getUuid().equals(unit.getUuid())) {
                            continue;
                        }

                        //已经是新增未处理的不进行修改
                        if (unit.getGzbz().equals("0")) {
                            continue;
                        }

                        unit.setGzbz("1");
                        unit.setBczt(BcztConstants.DTJ);
                        yhscjJgList.add(unit);
                    }
                }
            }

            yhscjJgList.add(yhscjRecord.get(0));
        }
        log.info(logTitle + "汇总总账新增和修改条目结束时间：" + DateUtils.getSystemCurrentTime(14));

        //写入总账
        yhscjMapper.insertOrUpdateBatch(yhscjJgList);
        log.info(logTitle + "新增或更新总账" + yhscjJgList.size());
//        //将本次新增记录也计入历史记录，以使其他账期使用时计算的金额总数正确

        //更新合同台账总账生成标志
        htmxList.forEach(htmx -> {
            htmx.setZzscbz("Y");
            htmx.setZzuuid(zbuuid);
        });
        znsbTzzxHtmxDlMapper.updateBatch(htmxList);
        log.info(logTitle + "变更总账生成标志");

        //同步立法可变列，将主表uuid置为表头uuid,syuuid置为yhscj的uuid
        List<ZnsbTzzxYhslfkblDO> allYhslfkbl = yhslfkblMapper.getYhslfkblByZbuuidList(zzjsRecordUuidList);
        List<ZnsbNssbYhslfkblDO> nssbYhslfkblDOList = new ArrayList<>();
        for (ZnsbTzzxYhslfkblDO lfkblUnit : allYhslfkbl) {
            ZnsbNssbYhslfkblDO nssbYhslfkblDO = BeanUtils.toBean(lfkblUnit, ZnsbNssbYhslfkblDO.class);
            nssbYhslfkblDO.setZbuuid(zbuuid);
            nssbYhslfkblDO.setSyuuid(zzuuid);
            nssbYhslfkblDO.setZfbz1("N");
            nssbYhslfkblDOList.add(nssbYhslfkblDO);
        }

        nssbYhslfkblMapper.insertOrUpdateBatch(nssbYhslfkblDOList);
        log.info(logTitle + "新增或更新立法可变列" + nssbYhslfkblDOList.size());

        // 处理零申报数据
        List<ZnsbNssbYhscjDO> zeroList = yhscjMapper.getDlzgZeroRecord(zbuuid, zspmDm, StringUtils.isEmpty(zszmDm) ? "" : zszmDm);
        if (!zeroList.isEmpty()) {
            yhscjMapper.deleteBatchIds(zeroList.stream().map(ZnsbNssbYhscjDO::getUuid).collect(Collectors.toList()));
        }
    }

    @Override
    public YhsExternalResponseDTO getHtxx(List<HtmxParamDTO> dtoList) {
        YhsExternalResponseDTO returnDTO = new YhsExternalResponseDTO();
        List<HtmxParamDTO> insertList = dtoList.stream().filter(dto -> "1".equals(dto.getBgbz())).collect(Collectors.toList());
        List<HtmxParamDTO> updateList = dtoList.stream().filter(dto -> "2".equals(dto.getBgbz())).collect(Collectors.toList());
        List<HtmxParamDTO> deleteList = dtoList.stream().filter(dto -> "3".equals(dto.getBgbz())).collect(Collectors.toList());
        if (!insertList.isEmpty()) {
            List<ZnsbTzzxHtmxDlDO> insertHtmxList = new ArrayList<>();
            List<ZnsbTzzxYhslfkblDO> insertYhslfkblList = new ArrayList<>();
            Map<String, List<JbxxmxsjVO>> nsrMap = new HashMap<>();
            for (HtmxParamDTO htmx : insertList) {
                // 业务校验
                YhsHtmxDlInsertOrUpdateDetailDTO insertDetailDTO = new YhsHtmxDlInsertOrUpdateDetailDTO();

                Date now = new Date();
                ZnsbTzzxHtmxDlDO dlDto = this.setZnsbTzzxHtmxDlDO(htmx, TzlxDmEnum.WTZ.getTzlxDm());
                // 获取纳税人信息
                if (nsrMap.containsKey(htmx.getNsrsbh())) {
                    dlDto.setNsrmc(nsrMap.get(htmx.getNsrsbh()).get(0).getNsrmc());
                    dlDto.setDjxh(nsrMap.get(htmx.getNsrsbh()).get(0).getDjxh());
                } else {
                    //获取djxh
                    ZnsbMhzcQyjbxxmxReqVO nsrreqVO = new ZnsbMhzcQyjbxxmxReqVO();
                    nsrreqVO.setNsrsbh(htmx.getNsrsbh());
                    CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByNsrsbh(nsrreqVO);
                    if (Objects.isNull(nsrxxRes) || Objects.isNull(nsrxxRes.getData())) {
                        returnDTO.setCode("0");
                        returnDTO.setMsg("获取纳税人信息失败：根据纳税人识别号，查询不到企业信息，请核实");
                        return returnDTO;
                    }
                    List<JbxxmxsjVO> nsrList = nsrxxRes.getData().getJbxxmxsj().stream()
                            .filter(jbxxmxsjVO -> "N".equals(jbxxmxsjVO.getKqccsztdjbz())).collect(Collectors.toList());
                    if (nsrList.isEmpty()) {
                        returnDTO.setCode("0");
                        returnDTO.setMsg("获取纳税人信息失败：根据纳税人识别号，查询不到企业信息，请核实");
                        return returnDTO;
                    }
                    dlDto.setNsrmc(nsrList.get(0).getNsrmc());
                    dlDto.setDjxh(nsrList.get(0).getDjxh());
                    nsrMap.put(htmx.getNsrsbh(), nsrList);
                }
                insertHtmxList.add(dlDto);

                // 构建印花税立法可变列
                YhsHtmxDlInsertOrUpdateDetailDTO dto = new YhsHtmxDlInsertOrUpdateDetailDTO();
                dto.setSlrList(htmx.getDfslxx());
                dto.setJsList(htmx.getSjjsxx());
                insertYhslfkblList.addAll(new ArrayList<>(this.buildYhslfkblList(now, dto, "1", htmx.getUuid())));
            }
            try {
                // 插入主表
                znsbTzzxHtmxDlMapper.insertBatch(insertHtmxList);

                // 插入附表
                if (!insertYhslfkblList.isEmpty()) {
                    yhslfkblMapper.insertBatch(insertYhslfkblList);
                }
            } catch (Exception e) {
                returnDTO.setCode("0");
                returnDTO.setMsg("新增合同出错：" + e);
                return returnDTO;
            }
        }
        if (!updateList.isEmpty()) {
            List<ZnsbTzzxHtmxDlDO> updateHtmxList = new ArrayList<>();
            List<ZnsbTzzxYhslfkblDO> updateYhslfkblList = new ArrayList<>();
            Date now = new Date();
            for (HtmxParamDTO htmx : updateList) {
                ZnsbTzzxHtmxDlDO unit = this.setZnsbTzzxHtmxDlDO(htmx, TzlxDmEnum.XTZ.getTzlxDm());
                // 查询主表旧数据
                ZnsbTzzxHtmxDlDO oldUnit = znsbTzzxHtmxDlMapper.selectById(htmx.getUuid());
                oldUnit.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
                oldUnit.setXgrq(now);
                // 如果主表记录之前是不成功 需要改成成功
                if ("N".equals(unit.getCgbz())) {
                    oldUnit.setCgbz("Y");
                }
                updateHtmxList.add(oldUnit);
                updateHtmxList.add(unit);
                //生成主表调账冲减记录
                updateHtmxList.add(this.setNewZnsbTzzxYhslfkblDO(unit, TzlxDmEnum.TZHCJ.getTzlxDm()));

                // 修改附表
                YhsHtmxDlInsertOrUpdateDetailDTO dto = new YhsHtmxDlInsertOrUpdateDetailDTO();
                dto.setSlrList(htmx.getDfslxx());
                dto.setJsList(htmx.getSjjsxx());
                updateYhslfkblList.addAll(this.buildYhslfkblList(now, dto, "2", unit.getUuid()));
            }
            try {
                // 修改主表
                znsbTzzxHtmxDlMapper.updateBatch(updateHtmxList);
                yhslfkblMapper.updateBatch(updateYhslfkblList);
            } catch (Exception e) {
                returnDTO.setCode("0");
                returnDTO.setMsg("更新合同出错：" + e);
                return returnDTO;
            }
        }
        if (!deleteList.isEmpty()) {
            List<String> uuidList = deleteList.stream().map(HtmxParamDTO::getUuid).collect(Collectors.toList());
            // 查询删除详情
            try {
                List<ZnsbTzzxHtmxDlDO> deletequeryList = znsbTzzxHtmxDlMapper.selectBatchIds(uuidList);
                // 循环生成调整数据
                List<ZnsbTzzxHtmxDlDO> tzList = new ArrayList<>();
                for (ZnsbTzzxHtmxDlDO delete : deletequeryList) {
                    //原数据调账类型更改为2
                    delete.setTzlxDm(TzlxDmEnum.BTZ.getTzlxDm());
                    delete.setXgrq(new Date());
                    tzList.add(delete);

                    //新增调账类型为3的对应数据
                    tzList.add(this.setNewZnsbTzzxYhslfkblDO(delete, TzlxDmEnum.TZHCJ.getTzlxDm()));
                }
                boolean deleteFlag = znsbTzzxHtmxDlMapper.insertOrUpdateBatch(tzList);
                if (deleteFlag) {
                    //删除副表（逻辑删除）
                    final List<ZnsbTzzxYhslfkblDO> yhslfkblList = yhslfkblMapper.getYhslfkblByZbuuidList(uuidList);
                    yhslfkblList.forEach(m -> {
                        m.setScbz("1");
                        m.setScsj(new Date());
                    });
                    if (!GyUtils.isNull(yhslfkblList)) {
                        yhslfkblMapper.updateBatch(yhslfkblList);
                    }
                }
            } catch (Exception e) {
                returnDTO.setCode("0");
                returnDTO.setMsg("删除合同出错：" + e);
                return returnDTO;
            }
        }

        returnDTO.setCode("1");
        returnDTO.setMsg("成功");
        return returnDTO;
    }

    private ZnsbTzzxHtmxDlDO setZnsbTzzxHtmxDlDO(HtmxParamDTO htmx, String tzlxDm) {
        ZnsbTzzxHtmxDlDO dlDto = new ZnsbTzzxHtmxDlDO();
        dlDto.setUuid(htmx.getUuid());
        dlDto.setNsrsbh(htmx.getNsrsbh());
        dlDto.setGsh2(htmx.getGsh());
        dlDto.setYwfw(htmx.getLrzxDm());
        dlDto.setYzpzbh(htmx.getHtbh());
        dlDto.setHtqdrq(htmx.getHtqdrq());
        dlDto.setSszq(Integer.parseInt(DateUtil.doDateFormat(htmx.getHtqdrq(), "yyyyMM")));
        dlDto.setBhsje(htmx.getBhsje());
        dlDto.setYdbz(htmx.getSfyd());
        dlDto.setBhshtebz(htmx.getSfbhshte());
        dlDto.setJryhsbz(htmx.getSfjryhs());
        dlDto.setBzshtbz(htmx.getSfsbzsht());
        dlDto.setBzsyy(htmx.getBzsyy());
        dlDto.setSldd(htmx.getSldd());
        dlDto.setFbhtbj(htmx.getFbhtbz());
        dlDto.setKjhtbj(htmx.getKjhtbz());
        dlDto.setJsrqq(htmx.getYjjsrqq());
        dlDto.setJsrqz(htmx.getYjjsrqz());
        dlDto.setHtlx(htmx.getZspmDm());
        dlDto.setHtzl(htmx.getZszmDm());
        dlDto.setZzscbz("0");
        dlDto.setJyssbz("N");
        dlDto.setLrrq(htmx.getLrrq());
        dlDto.setXgrq(htmx.getXgrq());
        dlDto.setTzlxDm(tzlxDm);
        dlDto.setLrrsfid("ZNSB.TZZX");
        dlDto.setYwqdDm("00");
        dlDto.setSjcsdq("00000000000");
        dlDto.setSjgsdq("00000000000");
        dlDto.setCgbz("Y");
        return dlDto;
    }

    private String checkInsertOrUpdateData(YhsHtmxDlInsertOrUpdateDetailDTO dto) {
        YhsHtmxInsertDetailDTO htmxDTO = dto.getYhsHtmxInsertDetailDTO();
        List<DfslxxVO> slrList = dto.getSlrList();
        // 合同签订日期非当前申报所属期
//        if (!this.isContractInLastMonthQuarter(htmxDTO.getHtqdrq())) {
//            return "合同签订日期非当前申报所属期";
//        }
        // 是否是不征税合同”为是时，不含税金额必须为0否则校验
        if ("Y".equals(htmxDTO.getBzshtbz()) && !(BigDecimal.ZERO.compareTo(htmxDTO.getBhsje()) == 0)) {
            return "“是否是不征税合同”为是时，不含税金额必须为0";
        }
        // 如果“是否是框架合同”填报为是，预计结算期起”、“预计结算期止”这俩字段就必填 ，未填报要校验
        // 如果“是否是框架合同”填报为否，预计结算期起”、“预计结算期止”这俩字段不允许填报 ，必填为空
        if (StringUtils.isNotEmpty(htmxDTO.getKjhtbj()) && "Y".equals(htmxDTO.getKjhtbj())) {
            if (StringUtils.isEmpty(htmxDTO.getJsrqq()) || StringUtils.isEmpty(htmxDTO.getJsrqz())) {
                return "“是否是框架合同”为是，请填写“预计结算期起”、“预计结算期止”字段";
            }
        } else if (StringUtils.isNotEmpty(htmxDTO.getKjhtbj()) && "N".equals(htmxDTO.getKjhtbj())) {
            if (StringUtils.isNotEmpty(htmxDTO.getJsrqq()) || StringUtils.isNotEmpty(htmxDTO.getJsrqz())) {
                return "“是否是框架合同”为否，不允许填写“预计结算期起”、“预计结算期止”字段";
            }
        }
        // 20250704 新增如分公司在导入和新增时，分包合同标志选是时，增加校验对方书立人名称必填
        if (StringUtils.isNotEmpty(htmxDTO.getFbhtbj()) && "Y".equals(htmxDTO.getFbhtbj()) && slrList.isEmpty()) {
            return "当“分包合同标记”为是时，请填写对方书立人信息";
        }
        for (DfslxxVO slr : slrList) {
            // “对方书立人名称”校验是否含特殊符号。--模版校验
            if (StringUtils.isNotEmpty(slr.getDfslrmc()) && !this.isSafeString(slr.getDfslrmc())) {
                return "对方书立人名称不能包含特殊字符";
            }
            // “对方书立人名称”若是大连华锐重工集团股份有限公司只校验名称是否准确
            if (StringUtils.isNotEmpty(slr.getDfslrmc()) && slr.getDfslrmc().contains(companyName)) {
                if (!companyName.equals(slr.getDfslrmc())) {
                    return "对方书立人名称必须为大连华锐重工集团股份有限公司";
                }
            }
            // “对方书立人名称”是大连华锐重工集团股份有限公司，“分包合同标志”填报必须为是
//            if ("大连华锐重工集团股份有限公司".equals(slr.getDfslrmc()) && !Objects.equals(htmxDTO.getFbhtbj(), "是")) {
//                return "对方书立人名称是大连华锐重工集团股份有限公司，“分包合同标志”填报必须为是，当前“分包合同标志”为：" + htmxDTO.getFbhtbj();
//            }
            if ("Y".equals(htmxDTO.getFbhtbj()) && StringUtils.isEmpty(slr.getDfslrmc())) {
                return "当“分包合同标记”为是时，请填写“对方书立人名称”字段";
            }
        }
        // 20250708 “是否为不征税合同”为是时，不征税原因必填，“是否为不征税合同”为否时，不征税原因不用填
        if (StringUtils.isNotEmpty(htmxDTO.getBzsyy()) && "是".equals(htmxDTO.getBzshtbz()) && StringUtils.isEmpty(htmxDTO.getBzsyy())) {
            return "“是否为不征税合同”为是，不允许填写“预计结算期起”、“预计结算期止”字段";
        }
        // 查询所管辖下所有企业登记序号
        final List<String> djxhList = ZnsbSessionUtils.getKczDjxhList();
        log.info("当前管理的企业列表：{}", djxhList);
        if (!Objects.isNull(djxhList) && !djxhList.contains(htmxDTO.getDjxh())) {
            return "当前企业不在管理企业范围内，请核实";
        }
        return "";
    }

    private String checkImportData(HtmxDlExcelDTO excel) {
        // 是否重复校验
//        if (!CollectionUtils.isEmpty(checkYzpzbhList) && checkYzpzbhList.contains(excel.getYzpzbh())) {
//            return "应税凭证编号" + excel.getYzpzbh() + "已存在";
//        }
        // 合同签订日期非当前申报所属期
        if (!this.isContractInLastMonthQuarter(excel.getYspzslrq())) {
            return "合同签订日期非当前申报所属期";
        }
        // 是否是不征税合同”为是时，不含税金额必须为0否则校验
        if ("是".equals(excel.getBzshtbz()) && !(BigDecimal.ZERO.compareTo(excel.getJsje()) == 0)) {
            return "“是否是不征税合同”为是时，不含税金额必须为0，当前不含税金额为：" + excel.getJsje();
        }
        // 20250703 增加一个计税金额等于0的监控，这样的也给放到不合规里面
        if (BigDecimal.ZERO.compareTo(excel.getJsje()) == 0) {
            return "“计税金额”为0";
        }
        // “对方书立人名称”校验是否含特殊符号。--模版校验
        if (StringUtils.isNotEmpty(excel.getDfslrmc()) && !this.isSafeString(excel.getDfslrmc())) {
            return "对方书立人名称不能包含特殊字符";
        }
        // “对方书立人名称”若是大连华锐重工集团股份有限公司只校验名称是否准确
        if (StringUtils.isNotEmpty(excel.getDfslrmc()) && excel.getDfslrmc().contains(companyName)) {
            if (!companyName.equals(excel.getDfslrmc())) {
                return "对方书立人名称必须为大连华锐重工集团股份有限公司";
            }
        }
        // “对方书立人名称”是大连华锐重工集团股份有限公司，“分包合同标志”填报必须为是
//        if ("大连华锐重工集团股份有限公司".equals(excel.getDfslrmc()) && !Objects.equals(excel.getFbhtbj(), "是")) {
//            return "对方书立人名称是大连华锐重工集团股份有限公司，“分包合同标志”填报必须为是";
//        }
        // 20250704 新增如分公司在导入和新增时，分包合同标志选是时，增加校验对方书立人名称必填
        if (StringUtils.isNotEmpty(excel.getFbhtbj()) && "是".equals(excel.getFbhtbj()) && StringUtils.isEmpty(excel.getDfslrmc())) {
            return "当“分包合同标记”为是时，请填写“对方书立人名称”字段";
        }
        // 如果“是否是框架合同”填报为是，预计结算期起”、“预计结算期止”这俩字段就必填 ，未填报要校验
        // 如果“是否是框架合同”填报为否，预计结算期起”、“预计结算期止”这俩字段不允许填报 ，必填为空
        if (StringUtils.isNotEmpty(excel.getKjhtbj()) && "是".equals(excel.getKjhtbj())) {
            if (StringUtils.isEmpty(excel.getJsrqq()) || StringUtils.isEmpty(excel.getJsrqz())) {
                return "“是否是框架合同”为是，请填写“预计结算期起”、“预计结算期止”字段";
            }
        } else if (StringUtils.isNotEmpty(excel.getKjhtbj()) && "否".equals(excel.getKjhtbj())) {
            if (StringUtils.isNotEmpty(excel.getJsrqq()) || StringUtils.isNotEmpty(excel.getJsrqz())) {
                return "“是否是框架合同”为否，不允许填写“预计结算期起”、“预计结算期止”字段";
            }
        }
        // 20250708 “是否为不征税合同”为是时，不征税原因必填，“是否为不征税合同”为否时，不征税原因不用填
        if (StringUtils.isNotEmpty(excel.getBzsyy()) && "是".equals(excel.getBzshtbz()) && StringUtils.isEmpty(excel.getBzsyy())) {
            return "“是否为不征税合同”为是，不允许填写“预计结算期起”、“预计结算期止”字段";
        }
        return "";
    }

    private void checkInsertOrUpdateDataIsNull(YhsHtmxDlInsertOrUpdateDetailDTO dto) {
        YhsHtmxInsertDetailDTO htmxDTO = dto.getYhsHtmxInsertDetailDTO();
        AssertUtil.assertNotBlank(htmxDTO.getNsrsbh(), "纳税人识别号不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getNsrmc(), "纳税人名称不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getYzpzbh(), "应税凭证编号不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getHtlx(), "合同类型不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getHtqdrq(), "合同签订日期不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getBzshtbz(), "是否是不征税合同不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getBhshtebz(), "是否不含税合同额不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getJryhsbz(), "是否计入印花税不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getFbhtbj(), "分包合同标记不能为空");
        AssertUtil.assertNotBlank(htmxDTO.getKjhtbj(), "框架合同标记不能为空");
    }

    private List<ZnsbTzzxYhslfkblDO> buildYhslfkblList(Date now, YhsHtmxDlInsertOrUpdateDetailDTO dto, String status, String uuid) {
        List<ZnsbTzzxYhslfkblDO> combinedList = new ArrayList<>();
        if (dto.getSlrList() != null && !dto.getSlrList().isEmpty()) {
            dto.getSlrList().forEach(m -> {
                ZnsbTzzxYhslfkblDO tzzxYhslfkblDO = new ZnsbTzzxYhslfkblDO();
                if (GyUtils.isNull(m.getUuid())) {
                    tzzxYhslfkblDO.setUuid(IdUtil.fastSimpleUUID());
                } else {
                    tzzxYhslfkblDO.setUuid(m.getUuid());
                }
                tzzxYhslfkblDO.setDfslrmc(m.getDfslrmc());
                tzzxYhslfkblDO.setDfslrnsrsbh(m.getDfslrnsrsbh());
                tzzxYhslfkblDO.setDfslrsjje(m.getDfslrsjje());
                tzzxYhslfkblDO.setZbuuid(uuid);
                tzzxYhslfkblDO.setZspmDm(dto.getYhsHtmxInsertDetailDTO().getHtlx());
                setZnsbTzzxYhslfkblDO(now, tzzxYhslfkblDO, status);
                combinedList.add(tzzxYhslfkblDO);
            });
        }
        if (dto.getJsList() != null && !dto.getJsList().isEmpty()) {
            dto.getJsList().forEach(m -> {
                ZnsbTzzxYhslfkblDO tzzxYhslfkblDO = new ZnsbTzzxYhslfkblDO();
                if (GyUtils.isNull(m.getUuid())) {
                    tzzxYhslfkblDO.setUuid(IdUtil.fastSimpleUUID());
                } else {
                    tzzxYhslfkblDO.setUuid(m.getUuid());
                }
                tzzxYhslfkblDO.setSjjsrq(m.getSjjsrq());
                tzzxYhslfkblDO.setSjjsje(m.getSjjsje());
                tzzxYhslfkblDO.setZbuuid(uuid);
                tzzxYhslfkblDO.setZspmDm(dto.getYhsHtmxInsertDetailDTO().getHtlx());
                setZnsbTzzxYhslfkblDO(now, tzzxYhslfkblDO, status);
                combinedList.add(tzzxYhslfkblDO);
            });
        }
        return combinedList;
    }

    private void setZnsbTzzxYhslfkblDO(Date date, ZnsbTzzxYhslfkblDO dto, String status) {
        if ("1".equals(status)) {
            dto.setScsj(date);
        }
        dto.setLrrq(dto.getLrrq() == null ? date : dto.getLrrq());
        dto.setXgrq(dto.getXgrq() == null ? date : dto.getXgrq());
        dto.setScbz("0");
        dto.setZzscbz("N");
        dto.setJyssbz("N");
        dto.setLrrsfid("ZNSB.TZZX");
        dto.setYwqdDm("TZ_USER");
        dto.setSjcsdq("00000000000");
        dto.setSjgsdq("00000000000");
        dto.setSjtbSj(date);
    }

    private ZnsbTzzxHtmxDlDO setNewZnsbTzzxYhslfkblDO(ZnsbTzzxHtmxDlDO old, String tzlxdm) {
        ZnsbTzzxHtmxDlDO dto = new ZnsbTzzxHtmxDlDO();
        BeanUtils.copyBean(old, dto);
        dto.setUuid(IdUtil.fastSimpleUUID());
        dto.setBhsje(old.getBhsje().negate());
        dto.setTzlxDm(tzlxdm);
        dto.setYwqdDm("00");
        dto.setZzscbz("N");
        dto.setJyssbz("N");
        dto.setSjtbSj(new Date());
        return dto;
    }

    private ZnsbTzzxHtmxDlDO setZnsbTzzxHtmxDlDO(YhsHtmxInsertDetailDTO dto) {
        String uuid = IdUtil.fastSimpleUUID();
        ZnsbTzzxHtmxDlDO dlDto = new ZnsbTzzxHtmxDlDO();
        dlDto.setUuid(uuid);
        dlDto.setZbuuid(uuid);
        dlDto.setNsrsbh(dto.getNsrsbh());
        dlDto.setNsrmc(dto.getNsrmc());
        dlDto.setGsh2(dto.getGsh2());
        dlDto.setDjxh(dto.getDjxh());
        dlDto.setYwfw(dto.getYwfw());
        dlDto.setYzpzbh(dto.getYzpzbh());
        dlDto.setHtqdrq(DateUtil.toDate(dto.getHtqdrq()));
        dlDto.setSszq(Integer.parseInt(dto.getHtqdrq().replaceAll("-", "").substring(0, 6)));
        dlDto.setBhsje(dto.getBhsje());
        dlDto.setYdbz(dto.getYdbz());
        dlDto.setBhshtebz(dto.getBhshtebz());
        dlDto.setJryhsbz(dto.getJryhsbz());
        dlDto.setBzshtbz(dto.getBzshtbz());
        dlDto.setBzsyy(dto.getBzsyy());
        dlDto.setSldd(dto.getSldd());
        dlDto.setFbhtbj(dto.getFbhtbj());
        dlDto.setKjhtbj(dto.getKjhtbj());
        dlDto.setJsrqq(DateUtil.toDate(dto.getJsrqq()));
        dlDto.setJsrqz(DateUtil.toDate(dto.getJsrqz()));
        dlDto.setHtlx(dto.getHtlx());
        dlDto.setHtzl(dto.getHtzl());
        return dlDto;
    }

    /**
     * 判断给定的年月是否在“当前系统时间上一个月”所在的季度内
     */
    private boolean isContractInLastMonthQuarter(String contractDateStr) {
        // 解析输入的年月
        int contractYear = Integer.parseInt(contractDateStr.substring(0, 4));
        int contractMonth = Integer.parseInt(contractDateStr.substring(5, 7));

        // 获取当前时间并计算上个月
        Calendar now = Calendar.getInstance();
        Calendar lastMonth = (Calendar) now.clone();
        lastMonth.add(Calendar.MONTH, -1);

        int lastMonthYear = lastMonth.get(Calendar.YEAR);
        int lastMonthValue = lastMonth.get(Calendar.MONTH) + 1; // 月份从0开始

        // 计算上个月所在季度的第一个月
        int lastMonthQuarterStartMonth = ((lastMonthValue - 1) / 3) * 3 + 1;

        // 计算合同所属季度的第一个月
        int contractQuarterStartMonth = ((contractMonth - 1) / 3) * 3 + 1;

        // 判断年份和季度是否一致
        return contractYear == lastMonthYear && contractQuarterStartMonth == lastMonthQuarterStartMonth;
    }

    private String getYesOrNoName(String item) {
        if ("是".equals(item)) {
            return "Y";
        } else if ("否".equals(item)) {
            return "N";
        }
        return "";
    }

    private boolean isSafeString(String input) {
        return input != null && SAFE_PATTERN.matcher(input).matches();
    }
}
