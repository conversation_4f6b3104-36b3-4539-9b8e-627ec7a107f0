package com.css.znsb.tzzx.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.constants.enums.JtbmEnum;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.company.CompanyInfoResDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.NsrbqxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.NsrzgxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.tzzx.mapper.zzstz.xxfpzz.ZnsbTzzxLrzxdzbMapper;
import com.css.znsb.tzzx.pojo.domain.tzzx.zzstz.xxfpzz.ZnsbTzzxLrzxdzbDO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
public class TzzxXgmnsrUtil {

    private static TzzxXgmnsrUtil xgmnsrUtil;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ZnsbTzzxLrzxdzbMapper znsbTzzxLrzxdzbMapper;

    @PostConstruct
    public void init(){
        xgmnsrUtil = this;
        xgmnsrUtil.nsrxxApi = this.nsrxxApi;
        xgmnsrUtil.companyApi = this.companyApi;
        xgmnsrUtil.stringRedisTemplate = this.stringRedisTemplate;
        xgmnsrUtil.znsbTzzxLrzxdzbMapper = this.znsbTzzxLrzxdzbMapper;
    }

    @Schema(description = "是否为小规模")
    public static boolean isXgmnsr(BigDecimal sl1, String djxh, String nsrsbh) {
        //小规模是3%折算1% 需要考虑兼顾 3% 1% 5%考虑
        if (!(sl1.compareTo(new BigDecimal("0.01")) == 0  || sl1.compareTo(new BigDecimal("0.03")) == 0) || sl1.compareTo(new BigDecimal("0.05")) == 0) {
            return false;
        }

        String key = djxh + nsrsbh;
        String xgmnsrlx = xgmnsrUtil.stringRedisTemplate.opsForValue().get("xgmtzzxpd:"+key+":zdyTz");
        if(GyUtils.isNull(xgmnsrlx)){
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
            znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = xgmnsrUtil.nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
            if (!GyUtils.isNull(nsrxxByNsrsbh) && !GyUtils.isNull(nsrxxByNsrsbh.getData()) && !GyUtils.isNull(nsrxxByNsrsbh.getData().getNsrzgxx())){
                List<NsrzgxxVO> nsrzgxx = nsrxxByNsrsbh.getData().getNsrzgxx();
                if (!GyUtils.isNull(nsrzgxx)){
                    xgmnsrlx = nsrzgxx.get(0).getNsrlx();
                    xgmnsrUtil.stringRedisTemplate.opsForValue().set("xgmtzzxpd:"+key+":zdyTz", xgmnsrlx,30, TimeUnit.DAYS);
                }
            }

        }

        return "2".equals(xgmnsrlx);
    }

    @Schema(description = "获取对应季度第一个月份")
    public static int getQuarterStart(int sszq) {
        int year = sszq / 100;    // 提取年份
        int month = sszq % 100;   // 提取月份
        int startMonth = ((month - 1) / 3) * 3 + 1;  // 计算季度起始月
        return year * 100 + startMonth;  // 合并年份和起始月
    }

    @Schema(description = "根据原sszq计算季度首月")
    public static Integer getQuarterFirstMonth(Integer sszq) {
        int year = sszq / 100;        // 提取年份
        int month = sszq % 100;       // 提取月份
        // 计算季度首月：(月份-1)/3取商后*3+1
        int firstMonthOfQuarter = ((month - 1) / 3) * 3 + 1;
        return year * 100 + firstMonthOfQuarter; // 组合成新sszq
    }

    @Schema(description = "根据原sszq计算季度首月（String版本）")
    public static String calculateQuarterFirstMonth(String originalSszq) {
        int year = Integer.parseInt(originalSszq.substring(0, 4));  // 提取年份
        int month = Integer.parseInt(originalSszq.substring(4, 6));  // 提取月份
        // 计算季度首月
        int firstMonth = ((month - 1) / 3) * 3 + 1;
        // 格式化月份为两位数（如1→"01"）
        String formattedMonth = String.format("%02d", firstMonth);
        return year + formattedMonth; // 拼接新值
    }

    @Schema(description = "根据系统时间计算指定月份（String版本）")
    public static String generateDateString() {
        /*当系统时间处于 2025年5月份、2025年6月份、2025年7月份时要生成字符串202504;
        当系统时间处于 2025年8月份、2025年9月份、2025年10月份时要生成字符串202507;
        当系统时间处于 2025年11月份、2025年12月份、2026年1月份时要生成字符串202510;
        当系统时间处于 2026年2月份、2026年3月份、2026年4月份时要生成字符串202601;*/
        LocalDate currentDate = LocalDate.now();
        int year = currentDate.getYear();
        Month month = currentDate.getMonth();

        // 处理跨年季度（11月、12月、1月）
        if (month == Month.JANUARY) {
            return (year - 1) + "10";
        }

        // 根据月份范围返回对应字符串
        if (month.getValue() >= 5 && month.getValue() <= 7) {
            return year + "04";
        } else if (month.getValue() >= 8 && month.getValue() <= 10) {
            return year + "07";
        } else if (month.getValue() >= 11) { // 11月和12月
            return year + "10";
        } else { // 2月、3月、4月
            return year + "01";
        }
    }

    @Schema(description = "是否为小规模")
    public static boolean isXgmnsrBydjxh(String djxh) {
        String xgmnsrlx = xgmnsrUtil.stringRedisTemplate.opsForValue().get("xgmtzzxpddjxh:"+ djxh +":zdyTz");
        if(GyUtils.isNull(xgmnsrlx)){
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = xgmnsrUtil.nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            List<NsrzgxxVO> nsrzgxx = nsrxxByNsrsbh.getData().getNsrzgxx();
            if (!GyUtils.isNull(nsrzgxx)){
                xgmnsrlx = nsrzgxx.get(0).getNsrlx();
                xgmnsrUtil.stringRedisTemplate.opsForValue().set("xgmtzzxpddjxh:"+ djxh +":zdyTz", xgmnsrlx,30, TimeUnit.DAYS);
            }
        }
        return "2".equals(xgmnsrlx);
    }

    @Schema(description = "是否为跨区户")
    public static boolean isKqhBydjxh(String djxh) {
        String kqhlx = xgmnsrUtil.stringRedisTemplate.opsForValue().get("kqhtzzxpddjxh:"+ djxh +":zdyTz");
        if(GyUtils.isNull(kqhlx)){
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = xgmnsrUtil.nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            List<JbxxmxsjVO> nsrjbxx = nsrxxByNsrsbh.getData().getJbxxmxsj();
            if (!GyUtils.isNull(nsrjbxx)){
                kqhlx = nsrjbxx.get(0).getKqccsztdjbz();
                xgmnsrUtil.stringRedisTemplate.opsForValue().set("kqhtzzxpddjxh:"+ djxh +":zdyTz", kqhlx,30, TimeUnit.DAYS);
            }
        }
        return "Y".equals(kqhlx);
    }

    @Schema(description = "是否为正常纳税人状态")
    public static boolean isNsrztBydjxh(String djxh) {
        String nsrzt = xgmnsrUtil.stringRedisTemplate.opsForValue().get("nsrzttzzxpddjxh:"+ djxh +":zdyTz");
        if(GyUtils.isNull(nsrzt)){
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = xgmnsrUtil.nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            List<JbxxmxsjVO> nsrjbxx = nsrxxByNsrsbh.getData().getJbxxmxsj();
            if (!GyUtils.isNull(nsrjbxx)){
                nsrzt = nsrjbxx.get(0).getNsrztDm();
                xgmnsrUtil.stringRedisTemplate.opsForValue().set("nsrzttzzxpddjxh:"+ djxh +":zdyTz", nsrzt,10, TimeUnit.DAYS);
            }
        }
        return "03".equals(nsrzt);
    }

    @Schema(description = "根据原sszq计算季度第一天和季度最后一天")
    public static Date[] getQuarterDates(String dateStr) {
        // 解析字符串为年月
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        LocalDate inputDate = YearMonth.parse(dateStr, formatter).atDay(1);

        int year = inputDate.getYear();
        int month = inputDate.getMonthValue();

        // 计算季度起始月份 (1, 4, 7, 10)
        int quarterStartMonth = ((month - 1) / 3) * 3 + 1;

        // 季度第一天
        LocalDate firstDay = LocalDate.of(year, quarterStartMonth, 1);

        // 季度最后一天 (季度起始月+2个月的最后一天)
        LocalDate lastDay = LocalDate.of(year, quarterStartMonth + 2, 1)
                .with(TemporalAdjusters.lastDayOfMonth());

        // 转换为Date类型（使用系统默认时区）
        ZoneId zone = ZoneId.systemDefault();
        Date startDate = Date.from(firstDay.atStartOfDay(zone).toInstant());
        Date endDate = Date.from(lastDay.atStartOfDay(zone).toInstant());

        return new Date[]{startDate, endDate};
    }

    @Schema(description = "查询公司号")
    public static String cxgshBydjxh(String djxh) {
        String gsh = xgmnsrUtil.stringRedisTemplate.opsForValue().get("gshtzzxpddjxh:"+ djxh +":zdyTz");
        if(GyUtils.isNull(gsh)){
            CommonResult<CompanyBasicInfoDTO> result1 = xgmnsrUtil.companyApi.basicInfo(djxh, "");
            if (!GyUtils.isNull(result1)){
                gsh = result1.getData().getQydmz();
                xgmnsrUtil.stringRedisTemplate.opsForValue().set("gshtzzxpddjxh:"+ djxh +":zdyTz", gsh,30, TimeUnit.DAYS);
            }
        }
        return gsh;
    }

    @Schema(description = "查询纳税人名称")
    public static String cxnsrmcBydjxh(String djxh) {
        String nsrmc = xgmnsrUtil.stringRedisTemplate.opsForValue().get("nsrmctzzxpddjxh:"+ djxh +":zdyTz");
        if(GyUtils.isNull(nsrmc)){
            CommonResult<CompanyBasicInfoDTO> result1 = xgmnsrUtil.companyApi.basicInfo(djxh, "");
            if (!GyUtils.isNull(result1)){
                nsrmc = result1.getData().getNsrmc();
                xgmnsrUtil.stringRedisTemplate.opsForValue().set("nsrmctzzxpddjxh:"+ djxh +":zdyTz", nsrmc,30, TimeUnit.DAYS);
            }
        }
        return nsrmc;
    }

    @Schema(description = "根据原sszq计算季度数组")
    public static List<String> generateQuarterList(String input) {
        // 提取年份和月份
        int year = Integer.parseInt(input.substring(0, 4));
        int startMonth = Integer.parseInt(input.substring(4, 6));

        // 生成季度月份列表
        return IntStream.range(0, 3)
                .mapToObj(i -> {
                    int month = startMonth + i;
                    // 格式化月份为两位数
                    return String.format("%04d%02d", year, month);
                })
                .collect(Collectors.toList());
    }

    @Schema(description = "查询登记序号")
    public static String getDjxhByNsrsbh(String nsrsbh) {
        String djxh = xgmnsrUtil.stringRedisTemplate.opsForValue().get("djxhtzzxpdnsrsbh:"+ nsrsbh +":zdyTz");
        if(GyUtils.isNull(djxh)){
            try{
                ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                znsbMhzcQyjbxxmxReqVO.setNsrsbh(nsrsbh);
                CommonResult<ZnsbMhzcQyjbxxmxResVO> result1 = xgmnsrUtil.nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
                if (!GyUtils.isNull(result1)){
                    djxh = result1.getData().getJbxxmxsj()
                            .stream()
                            .filter(jbxxmxsjVO -> "N".equals(jbxxmxsjVO.getKqccsztdjbz())).findFirst().get().getDjxh();
                    xgmnsrUtil.stringRedisTemplate.opsForValue().set("djxhtzzxpdnsrsbh:"+ nsrsbh +":zdyTz", djxh,30, TimeUnit.DAYS);
                }
            } catch (Exception e) {
                djxh = null;
            }
        }
        return djxh;
    }

    @Schema(description = "查询nsrsbh")
    public static String getNsrsbhByGsh(String gsh) {
        String nsrsbh = xgmnsrUtil.stringRedisTemplate.opsForValue().get("gshtzzxpdnsrsbh:"+ gsh +":zdyTz");
        if(GyUtils.isNull(nsrsbh)){
            try{
                CommonResult<List<CompanyInfoResDTO>> result1 = xgmnsrUtil.companyApi.getAllCompanyInfo();

                if (!GyUtils.isNull(result1)){
                    nsrsbh = result1.getData().stream()
                            .filter(jbxxmxsjVO -> gsh.equals(jbxxmxsjVO.getQydmz())).findFirst().get().getNsrsbh();
                    xgmnsrUtil.stringRedisTemplate.opsForValue().set("gshtzzxpdnsrsbh:"+ gsh +":zdyTz", nsrsbh,30, TimeUnit.DAYS);
                }
            } catch (Exception e) {
                nsrsbh = null;
            }
        }
        return nsrsbh;
    }

    @Schema(description = "查询nsrsbh大连")
    public static String getNsrsbhByGshdl(String gsh) {
        String nsrsbh = xgmnsrUtil.stringRedisTemplate.opsForValue().get("gshtzzxpdnsrsbhdl:"+ gsh +":zdyTz");
        if(GyUtils.isNull(nsrsbh)){
            try{
                LambdaQueryWrapper<ZnsbTzzxLrzxdzbDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ZnsbTzzxLrzxdzbDO::getGsh,gsh).eq(ZnsbTzzxLrzxdzbDO::getYxbz,"Y").eq(ZnsbTzzxLrzxdzbDO::getJtbm, JtbmEnum.DLZG.getJtbm());
                List<ZnsbTzzxLrzxdzbDO> result1  = xgmnsrUtil.znsbTzzxLrzxdzbMapper.selectList(queryWrapper);

                if (!GyUtils.isNull(result1)){
                    nsrsbh = result1.get(0).getNsrsbh();
                    xgmnsrUtil.stringRedisTemplate.opsForValue().set("gshtzzxpdnsrsbhdl:"+ gsh +":zdyTz", nsrsbh,60, TimeUnit.MINUTES);
                }
            } catch (Exception e) {
                nsrsbh = null;
            }
        }
        return nsrsbh;
    }

    @Schema(description = "是否为即征即退企业")
    public static boolean isJzjtnsrBydjxh(String djxh) {
        //select * from  qyd_znsb_mhzc.znsb_mhzc_qybqxx where bqmc = 'zzsjzjtqy' and yxbz =  'Y'
        //select * from  znsb_mhzc.znsb_mhzc_qybqxx where bqmc = 'zzsjzjtqy' and yxbz =  'Y'
        String jzjtbq = xgmnsrUtil.stringRedisTemplate.opsForValue().get("jzjttzzxpddjxh:"+ djxh +":zdyTz");
        if(GyUtils.isNull(jzjtbq)){
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(djxh);
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = xgmnsrUtil.nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            List<NsrbqxxVO> nsrbqxx = nsrxxByNsrsbh.getData().getNsrbqxx();

            if (!GyUtils.isNull(nsrbqxx)){
                Optional<NsrbqxxVO> first = nsrbqxx.stream()
                        .filter(NsrbqxxVO -> "Y".equals(NsrbqxxVO.getYxbz()))
                        .filter(NsrbqxxVO -> "zzsjzjtqy".equals(NsrbqxxVO.getBqmc()))
                        .findFirst();
                if (first.isPresent()) {
                    jzjtbq = first.get().getBqmc();
                    xgmnsrUtil.stringRedisTemplate.opsForValue().set("jzjttzzxpddjxh:"+ djxh +":zdyTz", jzjtbq,10, TimeUnit.MINUTES);
                }
            }

        }
        return "zzsjzjtqy".equals(jzjtbq);
    }


}
