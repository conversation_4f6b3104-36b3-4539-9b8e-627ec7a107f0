package com.css.znsb.tzzx.service.datasync;

import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.constants.enums.JtbmEnum;
import com.css.znsb.gjss.service.rj.QsrjService;
import com.css.znsb.gjss.service.srzz.impl.ZnsbTzzxSrzzbServiceImpl;
import com.css.znsb.gjss.service.tygj.jxfp.TygjJxfpService;
import com.css.znsb.gjss.service.yhs.httz.ZnsbTzzxHtzzService;
import com.css.znsb.gjss.service.zzs.GjssZnsbTzzxDkdjpzmxService;
import com.css.znsb.gjss.service.zzs.GjssZnsbTzzxHgwsmxService;
import com.css.znsb.gjss.service.zzs.XxfpmxGjssService;
import com.css.znsb.gjss.service.zzs.XxfpzzGjssService;
import com.css.znsb.gjss.service.zzs.ZnsbTzzxLkysfwkspzmxzService;
import com.css.znsb.gjss.service.zzs.ZzsCybdService;
import com.css.znsb.gjss.service.zzs.jxfp.ZnsbTzzxJxfphwfwmxbService;
import com.css.znsb.gjss.service.zzs.jxfp.ZnsbTzzxJxfpmxbGjssService;
import com.css.znsb.gjss.service.zzs.jxfp.ZnsbTzzxJxfpmxbZjbService;
import com.css.znsb.gjss.service.zzs.jxfp.ZnsbTzzxJxfpzzService;
import com.css.znsb.gjss.service.zzs.jxsezc.ZnsbTzzxJxsezcmxzGjssService;
import com.css.znsb.gjss.service.zzs.jxsezc.ZnsbTzzxJxsezczzGjssService;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.tzzx.constants.TzzxErrorCodeConstants;
import com.css.znsb.tzzx.pojo.domain.dolphin.CompareParam;
import com.css.znsb.tzzx.pojo.domain.dolphin.GrtbParam;
import com.css.znsb.tzzx.pojo.domain.dolphin.GshParam;
import com.css.znsb.tzzx.pojo.vo.datasync.ExtractDataReqVO;
import com.css.znsb.tzzx.pojo.vo.datasync.SrCompareReqVO;
import com.css.znsb.tzzx.service.yjtz.ZnsbTzzxQysdsyjtzService;
import com.css.znsb.tzzx.util.TzzxDolphinService;
import com.css.znsb.tzzx.util.TzzxXgmnsrUtil;
import com.sap.conn.jco.JCoException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataSyncServiceImpl implements DataSyncService{

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TzzxDolphinService dolphinService;
    @Resource
    private XxfpzzGjssService xxfpzzService;
    @Resource
    private XxfpmxGjssService xxfpmxGjssService;


    @Value("${dolphin-scheduler.dataSyncAcq:N}")
    private String acquire;

    @Resource
    private ZzsCybdService zzsCybdService;

    @Resource
    private ZnsbTzzxJxsezcmxzGjssService jxsezcmxzGjssService;

    @Resource
    private ZnsbTzzxLkysfwkspzmxzService lkysfwkspzmxzService;

    @Resource
    private ZnsbTzzxJxsezczzGjssService jxsezczzGjssService;

    @Resource
    private ZnsbTzzxJxfpzzService jxfpzzService;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private ZnsbTzzxJxfpmxbZjbService znsbTzzxJxfpmxbZjbService;

    @Resource
    private ZnsbTzzxJxfpmxbGjssService jxfpmxbService;

    @Resource
    private ZnsbTzzxJxfphwfwmxbService jxfphwfwmxbService;

    @Resource
    private GjssZnsbTzzxHgwsmxService gjssZnsbTzzxHgwsmxService;

    @Resource
    private GjssZnsbTzzxDkdjpzmxService gjssZnsbTzzxDkdjpzmxService;

    @Resource
    private QsrjService qsrjService;

    @Resource
    private TygjJxfpService tygjJxfpService;

    @Resource
    private ZnsbTzzxSrzzbServiceImpl znsbTzzxSrzzbService;

    @Resource
    private ZnsbTzzxHtzzService htzzService;

    @Resource
    private ZnsbTzzxQysdsyjtzService znsbTzzxQysdsyjtzServiceImpl;

    @Override
    public CommonResult<String> extract(ExtractDataReqVO reqVO) {
        if (!"Y".equals(acquire)){
            return CommonResult.success("未开启");
        }

        GshParam gshParam = new GshParam();
        String gsh = reqVO.getGsh();
        if (GyUtils.isNull(gsh)){
            gsh = "default";
        }else {
            gshParam.setGsh(gsh);
        }
        gshParam.setNsrsbh(reqVO.getNsrsbh());
        if (GyUtils.isNull(reqVO.getSszq())){
            gshParam.setSszq(Integer.valueOf(DateTimeFormatter.ofPattern("yyyyMM").format(LocalDate.now())));
        }else {
            gshParam.setSszq(reqVO.getSszq());
        }
        gshParam.setYwlx(reqVO.getYwlx());
        gshParam.setDjxh(reqVO.getDjxh());
        gshParam.setType("1");

        Boolean hasKey = stringRedisTemplate.hasKey(formatKey(gsh));
        if (Boolean.TRUE.equals(hasKey)){
            throw ServiceExceptionUtil.exception(TzzxErrorCodeConstants.EXIST_RUNNING_MISSION);
        }else {
            stringRedisTemplate.opsForValue().set(formatKey(gsh),"1",1, TimeUnit.HOURS);
            try {
                if (GyUtils.isNull(gshParam.getGsh())){
                    dolphinService.startProcessInstance("SAP-DATASYNC");
                }else {
                    validateCache(formatKeyJyssUnReady(gshParam.getNsrsbh()+gshParam.getSszq()));
                    dolphinService.startProcessInstance("SAP-DATASYNC", JsonUtils.toJson(gshParam));
                }
            } catch (Exception e) {
                log.error("",e);
                stringRedisTemplate.delete(formatKey(gsh));
            }
        }
        return CommonResult.success("success");
    }

    @Override
    public CommonResult<String> extractDone(String gsh) {
        String key = formatKeyJob("zzs:lkysfwkspzhz");
        if (validateCache(key)) {
            try {
                lkysfwkspzmxzService.execute(500, 30);
            } finally {
                delCache(key);
            }
        }
        String key2 = formatKeyJob("zzs:jxsezc");
        if (validateCache(key2)){
            try {
                jxsezcmxzGjssService.execute(500, 30);
            } finally {
                delCache(key2);
            }
        }
        String key3 = formatKeyJob("jyss:jxzc");
        if (validateCache(key3)){
            try {
                jxsezczzGjssService.handleJxsezcJyss(500);
            } finally {
                delCache(key3);
            }
        }
        String key4 = formatKeyJob("ypss:lkysJyssJob");
        if (validateCache(key4)){
            try {
                jxfpzzService.jyss();
            } finally {
                delCache(key4);
            }
        }

        stringRedisTemplate.delete(formatKey(gsh));
        return CommonResult.success("success");
    }

    @Override
    public CommonResult<String> compare(SrCompareReqVO reqVO) {
        if (!"Y".equals(acquire)){
            return CommonResult.success("未开启");
        }

        CompareParam compareParam = new CompareParam();
        if (!GyUtils.isNull(reqVO.getNsrsbh()) && !GyUtils.isNull(reqVO.getSszq())){

            if (GyUtils.isNull(reqVO.getDjxh())){
                ZnsbMhzcQyjbxxmxReqVO req = new ZnsbMhzcQyjbxxmxReqVO();
                req.setNsrsbh(reqVO.getNsrsbh());
                CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByNsrsbh(req);
                compareParam.setDjxh(nsrxxByNsrsbh.getData().getJbxxmxsj()
                        .stream()
                        .filter(jbxxmxsjVO -> "N".equals(jbxxmxsjVO.getKqccsztdjbz())).findFirst().get().getDjxh());
            }else {
                compareParam.setDjxh(reqVO.getDjxh());
            }
            compareParam.setNsrsbh(reqVO.getNsrsbh());
            compareParam.setSszq(reqVO.getSszq());

            //森马 小规模重构后 要将对应季度月份 归属到所属季度第一个月份
            final String jtbmSys = CacheUtils.getXtcs("**********");
            if(JtbmEnum.SM.getJtbm().equals(jtbmSys)){
                boolean isXgmnsr = TzzxXgmnsrUtil.isXgmnsr(new BigDecimal("0.03"), compareParam.getDjxh(), compareParam.getNsrsbh());
                if (isXgmnsr) {
                    compareParam.setSszq(TzzxXgmnsrUtil.getQuarterStart(compareParam.getSszq()));
                }
            }

            compareParam.setType("1");
        }

        try {
            validateCache(formatKeyJyssUnReady(compareParam.getNsrsbh()+compareParam.getSszq()));
            dolphinService.startProcessInstance("YW-SRYCBD", JsonUtils.toJson(compareParam));
        } catch (Exception e) {
            log.error("",e);
        }

        return CommonResult.success("success");
    }

    @Override
    public CommonResult<String> compareDone(SrCompareReqVO reqVO) {
        String nsrsbh = reqVO.getNsrsbh();
        Integer sszq = reqVO.getSszq();
        String djxh = reqVO.getDjxh();
        String redisKey = String.format("tzzx:srzz:scwcpz:%s", nsrsbh + sszq);
        stringRedisTemplate.delete(redisKey);

        //20250904 由于大连没有收入明细归集和收入与销项交集，故只要在回调调用销项扩展表归集即可
        final String jtbmSys = CacheUtils.getXtcs("**********");
        if(JtbmEnum.WLJ.getJtbm().equals(jtbmSys)){//王老吉
            //20250915 没有收入明细汇总 也没有大连那套扩展
            String key1 = formatKeyJob("zzs:srzbListen");
            if (validateCache(key1)){
                try {
                    xxfpmxGjssService.srzbListen(djxh, String.valueOf(sszq));
                } finally {
                    delCache(key1);
                }
            }
        }else if(JtbmEnum.DLZG.getJtbm().equals(jtbmSys)){//大连重工
            String key1 = formatKeyJob("zzs:xxfpzzkzb");
            if (validateCache(key1)){
                try {
                    xxfpmxGjssService.xxzzhzbListen(djxh,String.valueOf(sszq));
                } finally {
                    delCache(key1);
                }
            }
        }else{//森马及大部分企业都走这个体系
            //20250717 由于森马130不参与比对 不存在收入汇总表 只存在收入总账表里 这样导致计算未开票时不实时 只能等收入明细汇总执行完
            //故此改造 在海豚回调里触发 不考虑缓存key的情况 不考虑 多个人同时操作一个人台账数据
            //后续收入明细一天只加工一次 一个人一次最多1条 生成尾差可能会多条 森马1000公司 一般尾差 都是10条 11条
            String key3 = formatKeyJob("zzs:srzzhz");
            if (validateCache(key3)){
                try {
                    znsbTzzxSrzzbService.execute(100L, Integer.parseInt("1"),djxh,String.valueOf(sszq));
                } finally {
                    delCache(key3);
                }
            }

            String key1 = formatKeyJob("zzs:srzbListen");
            if (validateCache(key1)){
                try {
                    xxfpmxGjssService.srzbListen(djxh, String.valueOf(sszq));
                } finally {
                    delCache(key1);
                }
            }

        }

        String key2 = formatKeyJob("jyss:wkp");
        if (validateCache(key2)){
            try {
                xxfpzzService.handleWkpJyss(5000,djxh);
            } finally {
                delCache(key2);
            }
        }

        delCache(formatKeyJyssUnReady(nsrsbh+sszq));
        zzsCybdService.handleCacheSingleton(Arrays.asList("wkpFlag","srtzFlag","jxfpFlag"),nsrsbh, String.valueOf(sszq));
        return CommonResult.success("success");
    }

    @Override
    public CommonResult<String> jyssReadyStatus(SrCompareReqVO reqVO) {
        String status;
        Boolean statusBool = stringRedisTemplate.hasKey(formatKeyJyssUnReady(reqVO.getNsrsbh() + reqVO.getSszq()));
        Boolean statusBool2 = stringRedisTemplate.hasKey(formatKeyFpJyssUnReady(reqVO.getNsrsbh() + reqVO.getSszq()));
        if (Boolean.TRUE.equals(statusBool) || Boolean.TRUE.equals(statusBool2)){
            status = "unReady";
        }else {
            status = "ready";
        }
        return CommonResult.success(status);
    }


    @Override
    public CommonResult<String> extractInvoice(ExtractDataReqVO reqVO) {
        if (!"Y".equals(acquire)){
            return CommonResult.success("未开启");
        }

        //获取集团编码
        final String jtbmSys = CacheUtils.getXtcs("**********");

        //如果使用接口获取源端数据，则先获取数据
        if (JtbmEnum.QS.getJtbm().equals(jtbmSys)) {
            //泉膳逻辑
            String key = String.format("qsjk:job:%s", "qszzs:jxdata");
            if (validateCache(key)){
                try {
                    //init为初始化模式，否则为T+1模式，传入nsrsbh为单体提取，否则为全体提取
                    qsrjService.getJxfpData("sync", reqVO.getNsrsbh(), null);
                } finally {
                    delCache(key);
                }

//                this.extractInvoiceDone(reqVO);
            }
        } else if (JtbmEnum.TYGJ.getJtbm().equals(jtbmSys)) {
            //通用国际逻辑
            String key = String.format("tygjjk:job:%s", "tygjzzs:jxdata");
            if (validateCache(key)){
                try {
                    //init为初始化模式，否则为T+1模式，传入nsrsbh为单体提取，否则为全体提取
                    tygjJxfpService.getJxfpData("sync", reqVO.getNsrsbh() , null, null);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } finally {
                    delCache(key);
                }

//                this.extractInvoiceDone(reqVO);
            }
        }

        //调用海豚加工原始数据
        GshParam gshParam = new GshParam();
        gshParam.setNsrsbh(reqVO.getNsrsbh());
        gshParam.setSszq(reqVO.getSszq());
        gshParam.setType("1");

        Boolean hasKey = stringRedisTemplate.hasKey(formatKeyFp(reqVO.getNsrsbh()));
        if (Boolean.TRUE.equals(hasKey)){
            throw ServiceExceptionUtil.exception(TzzxErrorCodeConstants.EXIST_RUNNING_MISSION);
        }else {
            stringRedisTemplate.opsForValue().set(formatKeyFp(reqVO.getNsrsbh()),"1",1, TimeUnit.HOURS);
            try {
                validateCache(formatKeyFpJyssUnReady(gshParam.getNsrsbh()+gshParam.getSszq()));
                dolphinService.startProcessInstance("FP-DATASYNC", JsonUtils.toJson(gshParam));
            } catch (Exception e) {
                log.error("",e);
                stringRedisTemplate.delete(formatKeyFp(reqVO.getNsrsbh()));
            }
        }

        return CommonResult.success("success");
    }

    @Override
    public CommonResult<String> extractInvoiceDone(ExtractDataReqVO reqVO) {
        log.info("extractInvoiceDone开始:" + JsonUtils.toJson(reqVO));

        //2025.06.09:因为进项发票汇总和进项发票勾选处理可能会加工同一条数据，同时运行可能导致数据异常，因此这两项任务共用同一个redisKey
        String key = formatKeyJob("zzs:jxfphzOrJxfpmxJg");
        if (validateCache(key)){
            try {
                znsbTzzxJxfpmxbZjbService.jxfpmxJgExcute(5000, 30);
            } finally {
                delCache(key);
            }
        }

        //2025.06.09:因为进项发票汇总和进项发票勾选处理可能会加工同一条数据，同时运行可能导致数据异常，因此这两项任务共用同一个redisKey
        String key2 = formatKeyJob("zzs:jxfphzOrJxfpmxJg");
        if (validateCache(key2)){
            try {
                jxfpmxbService.execute(5000,60);
            } finally {
                delCache(key2);
            }
        }

        String key3 = formatKeyJob("ypss:jxfphwmx");
        if (validateCache(key3)){
            try {
                jxfphwfwmxbService.handleJxfphwfwmxYpss(500, 30);
            } finally {
                delCache(key3);
            }
        }

        String key4 = formatKeyJob("zzs:dkdjpzmx");
        if (validateCache(key4)) {
            try {
                gjssZnsbTzzxDkdjpzmxService.execute(5000,60);
            } finally {
                delCache(key4);
            }
        }

        String key5 = formatKeyJob("zzs:hgwsmx");
        if (validateCache(key5)) {
            try {
                gjssZnsbTzzxHgwsmxService.execute(5000,60);
            } finally {
                delCache(key5);
            }
        }

        delCache(formatKeyFpJyssUnReady(reqVO.getNsrsbh()+reqVO.getSszq()));
        delCache(formatKeyFp(reqVO.getNsrsbh()));
        zzsCybdService.handleCacheSingleton(Collections.singletonList("jxfpFlag"),reqVO.getNsrsbh(), String.valueOf(reqVO.getSszq()));
        log.info("extractInvoiceDone结束:" + JsonUtils.toJson(reqVO));
        return CommonResult.success("success");
    }

    @Override
    public CommonResult<String> synNsrTzxx(List<String> gshList) {
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH);
        List<String> monthList = new ArrayList<>();
        String dys = DateUtil.doDateFormat(calendar.getTime(),"MM");
        monthList.add(dys);
        int monthYs = month % 3;
        if(monthYs == 0){
            for(int i = 1; i <= 3;i++){
                calendar.add(Calendar.MONTH, -1);
                String yfStr = DateUtil.doDateFormat(calendar.getTime(),"MM");
                monthList.add(yfStr);
            }
        }else {
            for(int i = 1; i <= monthYs;i++){
                calendar.add(Calendar.MONTH, -1);
                String yfStr = DateUtil.doDateFormat(calendar.getTime(),"MM");
                monthList.add(yfStr);
            }
        }
        try{
            String gshParm = gshList.stream().map(gsh ->"'" + gsh + "'").collect(Collectors.joining(","));
            String monthParm = monthList.stream().map(gsh ->"'" + gsh + "'").collect(Collectors.joining(","));;
            GrtbParam grtbparam = new GrtbParam();
            log.info("synNsrTzxx调用海豚服务参数:{}",JsonUtils.toJson(grtbparam));
            grtbparam.setGsh(gshParm);
            grtbparam.setBcyf(monthParm);
            dolphinService.startProcessInstance("GRTB-", JsonUtils.toJson(grtbparam));
            log.info("synNsrTzxx调用海豚服务结束!");
        }catch (Exception e){
            log.error("synNsrTzxx调用海豚服务异常",e);
            return CommonResult.error("调用失败");
        }
        return CommonResult.success("调用成功");
    }

    @Override
    public void ywbdAddredis() {
        String key = formatKeyJob("zzs:srzbListen");
        stringRedisTemplate.opsForValue().set(key, "1", 60, TimeUnit.MINUTES);
    }

    @Override
    public void ywbdDelredis() {
        String key = formatKeyJob("zzs:srzbListen");
        stringRedisTemplate.delete(key);
    }

    private String formatKey(String key){
        return String.format("tzzx:datasync:extract:%s",key);
    }

    private Boolean validateCache(String key){
        Boolean hasKey = stringRedisTemplate.hasKey(key);
        if (Boolean.TRUE.equals(hasKey)){
            return false;
        }else {
            stringRedisTemplate.opsForValue().set(key,"1",60, TimeUnit.MINUTES);
            return true;
        }
    }

    private void delCache(String key){
        stringRedisTemplate.delete(key);
    }

    private static String formatKeyJob(String key) {
        return String.format("gjss:job:%s", key);
    }

    private static String formatKeyJyssUnReady(String key) {
        return String.format("jyssUnready:%s", key);
    }

    private static String formatKeyFpJyssUnReady(String key) {
        return String.format("jyssFpUnready:%s", key);
    }

    private String formatKeyFp(String key){
        return String.format("tzzx:datasync:extract:fp:%s",key);
    }
}
