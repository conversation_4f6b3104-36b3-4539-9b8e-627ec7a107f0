package com.css.znsb.tzzx.pojo.domain.sshz;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 智能申报_台账中心_乐企算税汇总_进项税额转出算税
 * @TableName znsb_tzzx_lqsshz_jxsezcss
 */
@TableName(value ="znsb_tzzx_lqsshz_jxsezcss")
@Data
public class ZnsbTzzxLqsshzJxsezcssDO implements Serializable {
    /**
     * UUID||uuid
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 主表UUID
     */
    @TableField(value = "zbuuid")
    private String zbuuid;

    /**
     * 红字专用发票信息表注明的进项税额
     */
    @TableField(value = "hzzyfpjxsezc")
    private BigDecimal hzzyfpjxsezc;

    /**
     * 上期留抵税额抵减欠税
     */
    @TableField(value = "ldsedqjxsezc")
    private BigDecimal ldsedqjxsezc;

    /**
     * 上期留抵税额退税
     */
    @TableField(value = "ldsetsjxsezc")
    private BigDecimal ldsetsjxsezc;

    /**
     * 异常凭证转出进项税额
     */
    @TableField(value = "ycpzjxsezc")
    private BigDecimal ycpzjxsezc;

    /**
     * 红字专用发票信息表注明的进项税额交易算税标志
     */
    @TableField(value = "hzzyfpjxsezcjyssbz")
    private String hzzyfpjxsezcjyssbz;

    /**
     * 上期留抵税额抵减欠税交易算税标志
     */
    @TableField(value = "ldsedqjxsezcjyssbz")
    private String ldsedqjxsezcjyssbz;

    /**
     * 上期留抵税额退税交易算税标志
     */
    @TableField(value = "ldsetsjxsezcjyssbz")
    private String ldsetsjxsezcjyssbz;

    /**
     * 异常凭证转出进项税额交易算税标志
     */
    @TableField(value = "ycpzjxsezcjyssbz")
    private String ycpzjxsezcjyssbz;

    /**
     * 调账类型代码
     */
    @TableField(value = "tzlx_dm")
    private String tzlxDm;

    /**
     * 业务渠道代码
     */
    @TableField(value = "ywqd_dm")
    private String ywqdDm;

    /**
     * 录入日期
     */
    @TableField(value = "lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField(value = "xgrq")
    private Date xgrq;

    /**
     * 数据产生地区
     */
    @TableField(value = "sjcsdq")
    private String sjcsdq;

    /**
     * 数据归属地区
     */
    @TableField(value = "sjgsdq")
    private String sjgsdq;

    /**
     * 修改人身份id
     */
    @TableField(value = "xgrsfid")
    private String xgrsfid;

    /**
     * 录入人身份id
     */
    @TableField(value = "lrrsfid")
    private String lrrsfid;

    /**
     * 数据同步时间
     */
    @TableField(value = "sjtb_sj")
    private Date sjtbSj;

    /**
     * 自定义项1
     */
    @TableField(value = "cDefine1")
    private String cdefine1;

    /**
     * 自定义项2
     */
    @TableField(value = "cDefine2")
    private String cdefine2;

    /**
     * 自定义项3
     */
    @TableField(value = "cDefine3")
    private String cdefine3;

    /**
     * 自定义项4
     */
    @TableField(value = "cDefine4")
    private String cdefine4;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ZnsbTzzxLqsshzJxsezcssDO other = (ZnsbTzzxLqsshzJxsezcssDO) that;
        return (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
            && (this.getZbuuid() == null ? other.getZbuuid() == null : this.getZbuuid().equals(other.getZbuuid()))
            && (this.getHzzyfpjxsezc() == null ? other.getHzzyfpjxsezc() == null : this.getHzzyfpjxsezc().equals(other.getHzzyfpjxsezc()))
            && (this.getLdsedqjxsezc() == null ? other.getLdsedqjxsezc() == null : this.getLdsedqjxsezc().equals(other.getLdsedqjxsezc()))
            && (this.getLdsetsjxsezc() == null ? other.getLdsetsjxsezc() == null : this.getLdsetsjxsezc().equals(other.getLdsetsjxsezc()))
            && (this.getYcpzjxsezc() == null ? other.getYcpzjxsezc() == null : this.getYcpzjxsezc().equals(other.getYcpzjxsezc()))
            && (this.getHzzyfpjxsezcjyssbz() == null ? other.getHzzyfpjxsezcjyssbz() == null : this.getHzzyfpjxsezcjyssbz().equals(other.getHzzyfpjxsezcjyssbz()))
            && (this.getLdsedqjxsezcjyssbz() == null ? other.getLdsedqjxsezcjyssbz() == null : this.getLdsedqjxsezcjyssbz().equals(other.getLdsedqjxsezcjyssbz()))
            && (this.getLdsetsjxsezcjyssbz() == null ? other.getLdsetsjxsezcjyssbz() == null : this.getLdsetsjxsezcjyssbz().equals(other.getLdsetsjxsezcjyssbz()))
            && (this.getYcpzjxsezcjyssbz() == null ? other.getYcpzjxsezcjyssbz() == null : this.getYcpzjxsezcjyssbz().equals(other.getYcpzjxsezcjyssbz()))
            && (this.getYwqdDm() == null ? other.getYwqdDm() == null : this.getYwqdDm().equals(other.getYwqdDm()))
            && (this.getLrrq() == null ? other.getLrrq() == null : this.getLrrq().equals(other.getLrrq()))
            && (this.getXgrq() == null ? other.getXgrq() == null : this.getXgrq().equals(other.getXgrq()))
            && (this.getSjcsdq() == null ? other.getSjcsdq() == null : this.getSjcsdq().equals(other.getSjcsdq()))
            && (this.getSjgsdq() == null ? other.getSjgsdq() == null : this.getSjgsdq().equals(other.getSjgsdq()))
            && (this.getXgrsfid() == null ? other.getXgrsfid() == null : this.getXgrsfid().equals(other.getXgrsfid()))
            && (this.getLrrsfid() == null ? other.getLrrsfid() == null : this.getLrrsfid().equals(other.getLrrsfid()))
            && (this.getSjtbSj() == null ? other.getSjtbSj() == null : this.getSjtbSj().equals(other.getSjtbSj()))
                && (this.getCdefine1() == null ? other.getCdefine1() == null : this.getCdefine1().equals(other.getCdefine1()))
                && (this.getCdefine2() == null ? other.getCdefine2() == null : this.getCdefine2().equals(other.getCdefine2()))
                && (this.getCdefine3() == null ? other.getCdefine3() == null : this.getCdefine3().equals(other.getCdefine3()))
                && (this.getCdefine4() == null ? other.getCdefine4() == null : this.getCdefine4().equals(other.getCdefine4()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getZbuuid() == null) ? 0 : getZbuuid().hashCode());
        result = prime * result + ((getHzzyfpjxsezc() == null) ? 0 : getHzzyfpjxsezc().hashCode());
        result = prime * result + ((getLdsedqjxsezc() == null) ? 0 : getLdsedqjxsezc().hashCode());
        result = prime * result + ((getLdsetsjxsezc() == null) ? 0 : getLdsetsjxsezc().hashCode());
        result = prime * result + ((getYcpzjxsezc() == null) ? 0 : getYcpzjxsezc().hashCode());
        result = prime * result + ((getHzzyfpjxsezcjyssbz() == null) ? 0 : getHzzyfpjxsezcjyssbz().hashCode());
        result = prime * result + ((getLdsedqjxsezcjyssbz() == null) ? 0 : getLdsedqjxsezcjyssbz().hashCode());
        result = prime * result + ((getLdsetsjxsezcjyssbz() == null) ? 0 : getLdsetsjxsezcjyssbz().hashCode());
        result = prime * result + ((getYcpzjxsezcjyssbz() == null) ? 0 : getYcpzjxsezcjyssbz().hashCode());
        result = prime * result + ((getYwqdDm() == null) ? 0 : getYwqdDm().hashCode());
        result = prime * result + ((getLrrq() == null) ? 0 : getLrrq().hashCode());
        result = prime * result + ((getXgrq() == null) ? 0 : getXgrq().hashCode());
        result = prime * result + ((getSjcsdq() == null) ? 0 : getSjcsdq().hashCode());
        result = prime * result + ((getSjgsdq() == null) ? 0 : getSjgsdq().hashCode());
        result = prime * result + ((getXgrsfid() == null) ? 0 : getXgrsfid().hashCode());
        result = prime * result + ((getLrrsfid() == null) ? 0 : getLrrsfid().hashCode());
        result = prime * result + ((getSjtbSj() == null) ? 0 : getSjtbSj().hashCode());
        result = prime * result + ((getCdefine1() == null) ? 0 : getCdefine1().hashCode());
        result = prime * result + ((getCdefine2() == null) ? 0 : getCdefine2().hashCode());
        result = prime * result + ((getCdefine3() == null) ? 0 : getCdefine3().hashCode());
        result = prime * result + ((getCdefine4() == null) ? 0 : getCdefine4().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", uuid=").append(uuid);
        sb.append(", zbuuid=").append(zbuuid);
        sb.append(", hzzyfpjxsezc=").append(hzzyfpjxsezc);
        sb.append(", ldsedqjxsezc=").append(ldsedqjxsezc);
        sb.append(", ldsetsjxsezc=").append(ldsetsjxsezc);
        sb.append(", ycpzjxsezc=").append(ycpzjxsezc);
        sb.append(", hzzyfpjxsezcjyssbz=").append(hzzyfpjxsezcjyssbz);
        sb.append(", ldsedqjxsezcjyssbz=").append(ldsedqjxsezcjyssbz);
        sb.append(", ldsetsjxsezcjyssbz=").append(ldsetsjxsezcjyssbz);
        sb.append(", ycpzjxsezcjyssbz=").append(ycpzjxsezcjyssbz);
        sb.append(", ywqdDm=").append(ywqdDm);
        sb.append(", lrrq=").append(lrrq);
        sb.append(", xgrq=").append(xgrq);
        sb.append(", sjcsdq=").append(sjcsdq);
        sb.append(", sjgsdq=").append(sjgsdq);
        sb.append(", xgrsfid=").append(xgrsfid);
        sb.append(", lrrsfid=").append(lrrsfid);
        sb.append(", sjtbSj=").append(sjtbSj);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append(", cdefine1=").append(cdefine1);
        sb.append(", cdefine2=").append(cdefine2);
        sb.append(", cdefine3=").append(cdefine3);
        sb.append(", cdefine4=").append(cdefine4);
        sb.append("]");
        return sb.toString();
    }
}