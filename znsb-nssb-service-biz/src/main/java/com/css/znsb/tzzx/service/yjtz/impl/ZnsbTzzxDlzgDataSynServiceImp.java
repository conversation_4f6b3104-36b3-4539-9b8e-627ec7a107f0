package com.css.znsb.tzzx.service.yjtz.impl;


import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.nssb.mapper.yjzt.*;
import com.css.znsb.nssb.pojo.domain.yjtz.ZnsbCwbbQykjzzybqyLrbyzxDO;
import com.css.znsb.nssb.pojo.domain.yjtz.ZnsbCwbbQykjzzybqyXjllbDO;
import com.css.znsb.nssb.pojo.domain.yjtz.ZnsbCwbbQykjzzybqyZcfzbyzxDO;
import com.css.znsb.nssb.pojo.domain.yjtz.ZnsbCwbbZlbscjbDO;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.tzzx.pojo.dto.qysdsyj.dlzg.*;
import com.css.znsb.tzzx.service.yjtz.ZnsbTzzxDlzgDataSynService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ZnsbTzzxDlzgDataSynServiceImp implements ZnsbTzzxDlzgDataSynService {

    /**
     * 企业资产负债表访问Mapper
     */
    @Resource
    private ZnsbCwbbQykjzzybqyZcfzbyzxMapper zzcfzbMapper;

    /**
     * 企业利润表访问Mapper
     */
    @Resource
    private ZnsbCwbbQykjzzybqyLrbyzxMapper lrbMapper;

    /**
     * 企业利润表访问Mapper
     */
    @Resource
    private ZnsbCwbbQykjzzybqyXjllbMapper xjllbMapper;

    /**
     * 资料报送采集表访问Mapper
     */
    @Resource
    private ZnsbCwbbZlbscjbMapper zlbscjbMapper;

    /**
     * 纳税人信息访问API
     */
    @Resource
    private NsrxxApi nsrxxApi;

    /**
     * 获取利润表信息 ZF_ZLC03X_DISPLAY
     * @return
     */
    @Override
    @Transactional
    public String getLrbxx(String gsh,String tbny) {
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbxx  start");
        String jobParam = XxlJobHelper.getJobParam();
        if(GyUtils.isNotNull(jobParam)){
            String[] parmaArry = jobParam.split(",");
            if(parmaArry.length == 1){
                tbny = parmaArry[0];
            }else if(parmaArry.length == 2){
                gsh = parmaArry[0];
                tbny = parmaArry[1];
            }
        }

//        String url = "http://10.2.8.46:50000/RESTAdapter/SFP010/CriteriaIncomeSheet";
        String url = CacheUtils.getXtcs("dlzg_cwbb_url") + "/RESTAdapter/SFP010/CriteriaIncomeSheet";

        List<ZnsbCwbbQykjzzybqyLrbyzxDO> insertList = new ArrayList<>();
        List<ZnsbCwbbQykjzzybqyLrbyzxDO> updateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        if(GyUtils.isNotNull(tbny)){
            calendar.setTime(DateUtil.toDate("yyyyMM",tbny));
        }
        String year = DateUtil.doDateFormat(calendar.getTime(),"yyyy");
        String month = DateUtil.doDateFormat(calendar.getTime(),"MM");
        String sszq = DateUtil.doDateFormat(calendar.getTime(),"yyyyMM");
        Map<String,Date> ssqqz = getSsqqz(sszq);

        Map<String,String> jtMap = getJtxxMap();
        Map<String,String> gsxxMap = getGsxxMap();
        String finalGsh = gsh;
        jtMap.entrySet().stream().forEach(entry->{
            String jt = entry.getKey();
            boolean sftb = true;
            if(GyUtils.isNotNull(finalGsh) && !jt.equals(finalGsh)){
                sftb = false;
            }
            if(sftb){
                String nsrsbh = entry.getValue();
                CwbbReqDTO cwbbReqDTO = new CwbbReqDTO();
                cwbbReqDTO.setWaRitclg("C1");
                cwbbReqDTO.setWaCongr(jt);
                cwbbReqDTO.setWaRyear(year);
                cwbbReqDTO.setWaRpmax(month);
                cwbbReqDTO.setWaRbunit("");
                CwbbTdRbunitReqDTO cwbbTdRbunitReqDTO = new CwbbTdRbunitReqDTO();
                cwbbReqDTO.setCwbbTdRbunitReqDTO(cwbbTdRbunitReqDTO);
                String result = this.getResult(url,cwbbReqDTO);
                if(GyUtils.isNotNull(result)){
                    this.getLrbxx(insertList,updateList,result,ssqqz,jt,nsrsbh);
                }
            }
        });

        gsxxMap.entrySet().stream().forEach(entry->{
            String gs = entry.getKey();
            boolean sftb = true;
            if(GyUtils.isNotNull(finalGsh) && !gs.equals(finalGsh)){
                sftb = false;
            }
            if(sftb){
                String nsrsbh = entry.getValue();
                CwbbReqDTO cwbbReqDTO = new CwbbReqDTO();
                cwbbReqDTO.setWaRitclg("C1");
                cwbbReqDTO.setWaCongr("");
                cwbbReqDTO.setWaRyear(year);
                cwbbReqDTO.setWaRpmax(month);
                cwbbReqDTO.setWaRbunit("");
                CwbbTdRbunitReqDTO cwbbTdRbunitReqDTO = new CwbbTdRbunitReqDTO();
                Item item = new Item();
                item.setSign("I");
                item.setOption("EQ");
                item.setLow(gs);
                item.setHigh("");
                cwbbTdRbunitReqDTO.setItem(new Item[]{item});
                cwbbReqDTO.setCwbbTdRbunitReqDTO(cwbbTdRbunitReqDTO);

                String result = this.getResult(url,cwbbReqDTO);
                if(GyUtils.isNotNull(result)){
                    this.getLrbxx(insertList,updateList,result,ssqqz,gs,nsrsbh);
                }
            }
        });

        Map<String,ZnsbCwbbZlbscjbDO> zlbscjbMap = new HashMap<>();
        if(GyUtils.isNotNull(insertList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbxx  insertList={}" , insertList.size());
            this.getLrbZbxxMap(zlbscjbMap,insertList,sszq);
            lrbMapper.insertBatch(insertList);
        }
        if(GyUtils.isNotNull(updateList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbxx  updateList={}" , updateList.size());
            this.getLrbZbxxMap(zlbscjbMap,updateList,sszq);
            lrbMapper.updateBatch(updateList);
        }
        List<ZnsbCwbbZlbscjbDO> zbList = zlbscjbMap.entrySet().stream().map(entry->entry.getValue()).collect(Collectors.toList());
        if(GyUtils.isNotNull(zbList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbxx  zbList={}" , zbList.size());
            zlbscjbMapper.insertBatch(zbList);
        }
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbxx  end");
        return null;
    }


    private void getLrbxx(List<ZnsbCwbbQykjzzybqyLrbyzxDO> insertList,List<ZnsbCwbbQykjzzybqyLrbyzxDO> updateList,String result,Map<String,Date> ssqqz,String gsh,String nsrsbh) {
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbxx  result={}" , result);
        Map<String,String> dzMap = getLrHdzMap();
        Map<String,String> lrbHmc = getLryzxHmc();

        CwbbResDTO resDTO = JsonUtils.toBean(result, CwbbResDTO.class);
        if(GyUtils.isNotNull(resDTO.getTdZLC03XOutput())){
            List<ItemResDTO> itemResDTOS = resDTO.getTdZLC03XOutput().getItemResDTOs();
            if (GyUtils.isNotNull(itemResDTOS)){
                List<ZnsbCwbbQykjzzybqyLrbyzxDO> lrbListNew = new ArrayList<>();
                itemResDTOS.stream().forEach(itemResDTO->{
                    String ewhh = dzMap.get(String.valueOf(itemResDTO.getZlnumt()));
                    if(GyUtils.isNotNull(ewhh)){
                        ZnsbCwbbQykjzzybqyLrbyzxDO znsbcwbbqykjzzybqylrbyzxdo = new ZnsbCwbbQykjzzybqyLrbyzxDO();
                        this.setDefaultValue(znsbcwbbqykjzzybqylrbyzxdo);
                        znsbcwbbqykjzzybqylrbyzxdo.setGsh2(gsh);
                        znsbcwbbqykjzzybqylrbyzxdo.setBqje(itemResDTO.getToyear());
                        znsbcwbbqykjzzybqylrbyzxdo.setSqje1(itemResDTO.getBckyear());
                        znsbcwbbqykjzzybqylrbyzxdo.setByfse(itemResDTO.getTomenth());
                        String hmc = lrbHmc.get(ewhh);
                        znsbcwbbqykjzzybqylrbyzxdo.setHmc(hmc);
                        znsbcwbbqykjzzybqylrbyzxdo.setEwbhxh(new BigDecimal(ewhh));
                        znsbcwbbqykjzzybqylrbyzxdo.setNsrsbh(nsrsbh);
                        znsbcwbbqykjzzybqylrbyzxdo.setSsqq(ssqqz.get("ssqq"));
                        znsbcwbbqykjzzybqylrbyzxdo.setSsqz(ssqqz.get("ssqz"));
                        lrbListNew.add(znsbcwbbqykjzzybqylrbyzxdo);
                    }
                });

                lrbHmc.entrySet().stream().forEach(entry->{
                    String ewhh = entry.getKey();
                    String hmc = entry.getValue();
                    boolean isExist = false;
                    for(ZnsbCwbbQykjzzybqyLrbyzxDO lrbDo:lrbListNew){
                        if(ewhh.equals(lrbDo.getEwbhxh().toString())){
                            isExist = true;
                            break;
                        }
                    }
                    if(!isExist){
                        ZnsbCwbbQykjzzybqyLrbyzxDO znsbcwbbqykjzzybqylrbyzxdo = new ZnsbCwbbQykjzzybqyLrbyzxDO();
                        this.setDefaultValue(znsbcwbbqykjzzybqylrbyzxdo);
                        znsbcwbbqykjzzybqylrbyzxdo.setGsh2(gsh);
                        znsbcwbbqykjzzybqylrbyzxdo.setBqje(BigDecimal.ZERO);
                        znsbcwbbqykjzzybqylrbyzxdo.setSqje1(BigDecimal.ZERO);
                        znsbcwbbqykjzzybqylrbyzxdo.setByfse(BigDecimal.ZERO);
                        znsbcwbbqykjzzybqylrbyzxdo.setHmc(hmc);
                        znsbcwbbqykjzzybqylrbyzxdo.setEwbhxh(new BigDecimal(ewhh));
                        znsbcwbbqykjzzybqylrbyzxdo.setNsrsbh(nsrsbh);
                        znsbcwbbqykjzzybqylrbyzxdo.setSsqq(ssqqz.get("ssqq"));
                        znsbcwbbqykjzzybqylrbyzxdo.setSsqz(ssqqz.get("ssqz"));
                        lrbListNew.add(znsbcwbbqykjzzybqylrbyzxdo);
                    }
                });

                ZnsbCwbbQykjzzybqyLrbyzxDO znsbcwbbqykjzzybqylrbyzxdo = new ZnsbCwbbQykjzzybqyLrbyzxDO();
                znsbcwbbqykjzzybqylrbyzxdo.setGsh2(gsh);
                znsbcwbbqykjzzybqylrbyzxdo.setSsqq(ssqqz.get("ssqq"));
                znsbcwbbqykjzzybqylrbyzxdo.setSsqz(ssqqz.get("ssqz"));
                List<ZnsbCwbbQykjzzybqyLrbyzxDO> lrbList = lrbMapper.selectLrbList(znsbcwbbqykjzzybqylrbyzxdo);
//                List<ZnsbCwbbQykjzzybqyLrbyzxDO> lrbList = null;
                if(GyUtils.isNull(lrbList)){
                    insertList.addAll(lrbListNew);
                }else {
                    for(ZnsbCwbbQykjzzybqyLrbyzxDO newLrbDo:lrbListNew){
                        for(ZnsbCwbbQykjzzybqyLrbyzxDO oldLrbDo:lrbList){
                            if(oldLrbDo.getEwbhxh().compareTo(newLrbDo.getEwbhxh()) == 0){
                                oldLrbDo.setByfse(newLrbDo.getByfse());
                                oldLrbDo.setBqje(newLrbDo.getBqje());
                                oldLrbDo.setSqje1(newLrbDo.getSqje1());
                                updateList.add(oldLrbDo);
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    private void getLrbZbxxMap(Map<String,ZnsbCwbbZlbscjbDO> zlbscjbMap,List<ZnsbCwbbQykjzzybqyLrbyzxDO> lrbList,String sszq){
        lrbList.stream().forEach(lrbDO->{
            String key = lrbDO.getNsrsbh();
            if(GyUtils.isNull(zlbscjbMap.get(key))){
                ZnsbMhzcQyjbxxmxReqVO mhReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                mhReqVO.setNsrsbh(lrbDO.getNsrsbh());
                log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbZbxxMap  nsrsbh()={}" , lrbDO.getNsrsbh());
                CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxList = nsrxxApi.getNsrxxByNsrsbh(mhReqVO);
                if(GyUtils.isNotNull(nsrxxList) && GyUtils.isNotNull(nsrxxList.getData()) && GyUtils.isNotNull(nsrxxList.getData().getJbxxmxsj())){
                    List<JbxxmxsjVO> nsrList = nsrxxList.getData().getJbxxmxsj();
                    JbxxmxsjVO nsrxx = null;
                    for(JbxxmxsjVO jbxxmxsjvo:nsrList){
                        String kqccsztdjbz = jbxxmxsjvo.getKqccsztdjbz();
                        if("N".equals(kqccsztdjbz)){
                            nsrxx = jbxxmxsjvo;
                        }
                    }
                    log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbZbxxMap  nsrxx={}" , JsonUtils.toJson(nsrxx));
                    if(GyUtils.isNotNull(nsrxx)){
                        String djxh = nsrxx.getDjxh();
                        ZnsbCwbbZlbscjbDO zlbscjbDO = zlbscjbMapper.queryXxBySszq(djxh,sszq);
                        if(GyUtils.isNull(zlbscjbDO)){
                            zlbscjbDO = new ZnsbCwbbZlbscjbDO();
                            String zlbscjuuid = GyUtils.getUuid();
                            String uuid = GyUtils.getUuid();
                            lrbDO.setZlbscjuuid(zlbscjuuid);
                            zlbscjbDO.setLrrq(new Date());
                            zlbscjbDO.setYwqdDm("DLZG");
                            zlbscjbDO.setLrrsfid("DSRW");
                            zlbscjbDO.setZfbz1("N");
                            zlbscjbDO.setZlbsuuid(uuid);
                            zlbscjbDO.setZlbscjuuid(zlbscjuuid);
                            zlbscjbDO.setDjxh(djxh);
                            zlbscjbDO.setSszq(sszq);
                            zlbscjbDO.setQydmz(lrbDO.getGsh2());
                            zlbscjbMap.put(key,zlbscjbDO);
                        }else {
                            lrbDO.setZlbscjuuid(zlbscjbDO.getZlbscjuuid());
                        }
                    }
                }else {
                    log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbZbxxMap  nsrxx=null");
                }
            }else {
                log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbZbxxMap  Zlbscjuuid" , zlbscjbMap.get(key).getZlbscjuuid());
                lrbDO.setZlbscjuuid(zlbscjbMap.get(key).getZlbscjuuid());
            }
        });
    }

    /**
     * 获取利润表信息 ZF_ZLC01XN_DISPLAY
     * @return
     */
    @Override
    @Transactional
    public String getZcfzbxx(String gsh,String tbny) {
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx  start");

        String jobParam = XxlJobHelper.getJobParam();
        if(GyUtils.isNotNull(jobParam)){
            String[] parmaArry = jobParam.split(",");
            if(parmaArry.length == 1){
                tbny = parmaArry[0];
            }else if(parmaArry.length == 2){
                gsh = parmaArry[0];
                tbny = parmaArry[1];
            }
        }
//        String url = "http://10.2.8.46:50000/RESTAdapter/SFP009/RevenueCriteria_BalanceSheet";
        String url = CacheUtils.getXtcs("dlzg_cwbb_url") + "/RESTAdapter/SFP009/RevenueCriteria_BalanceSheet";
        List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> insertList = new ArrayList<>();
        List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> updateList = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        if(GyUtils.isNotNull(tbny)){
            calendar.setTime(DateUtil.toDate("yyyyMM",tbny));
        }
        String sszq = DateUtil.doDateFormat(calendar.getTime(),"yyyyMM");
        Map<String,Date> ssqqz = getSsqqz(sszq);
        String year = DateUtil.doDateFormat(calendar.getTime(),"yyyyy");
        String month = "0" + DateUtil.doDateFormat(calendar.getTime(),"MM");
        calendar.add(Calendar.MONTH, -1);
        String preMonth = "0" + DateUtil.doDateFormat(calendar.getTime(),"MM");
        String preYea = Integer.parseInt(year) - 1 + "";

        Map<String,String> jtMap = getJtxxMap();
        Map<String,String> gsxxMap = getGsxxMap();
        String finalGsh = gsh;
        jtMap.entrySet().stream().forEach(entry->{
            String jt = entry.getKey();
            boolean sftb = true;
            if(GyUtils.isNotNull(finalGsh) && !jt.equals(finalGsh)){
                sftb = false;
            }
            if(sftb){
                String nsrsbh = entry.getValue();
                CwbbReqDTO cwbbReqDTO = new CwbbReqDTO();
                cwbbReqDTO.setWaRitclg("C1");
                cwbbReqDTO.setWaCongr(jt);
                cwbbReqDTO.setWaRyear(year);
                cwbbReqDTO.setWaRpmax(month);
                cwbbReqDTO.setWaRbunit("");
                CwbbTdRbunitReqDTO cwbbTdRbunitReqDTO = new CwbbTdRbunitReqDTO();
                cwbbReqDTO.setCwbbTdRbunitReqDTO(cwbbTdRbunitReqDTO);
                String result = this.getResult(url,cwbbReqDTO);
                cwbbReqDTO.setWaRpmax(preMonth);
                String preMonthResult = this.getResult(url,cwbbReqDTO);
                cwbbReqDTO.setWaRpmax("013");
                cwbbReqDTO.setWaRyear(preYea);
                String preYeaResult = this.getResult(url,cwbbReqDTO);
                if(GyUtils.isNotNull(result)){
                    this.getZcfzxx(insertList,updateList,result,preMonthResult,preYeaResult,ssqqz,jt,nsrsbh);
                }
            }
        });

        gsxxMap.entrySet().stream().forEach(entry->{
            String gs = entry.getKey();
            boolean sftb = true;
            if(GyUtils.isNotNull(finalGsh) && !gs.equals(finalGsh)){
                sftb = false;
            }
            if(sftb){
                String nsrsbh = entry.getValue();
                CwbbReqDTO cwbbReqDTO = new CwbbReqDTO();
                cwbbReqDTO.setWaRitclg("C1");
                cwbbReqDTO.setWaCongr("");
                cwbbReqDTO.setWaRyear(year);
                cwbbReqDTO.setWaRpmax(month);
                cwbbReqDTO.setWaRbunit("");
                CwbbTdRbunitReqDTO cwbbTdRbunitReqDTO = new CwbbTdRbunitReqDTO();
                Item item = new Item();
                item.setSign("I");
                item.setOption("EQ");
                item.setLow(gs);
                item.setHigh("");
                cwbbTdRbunitReqDTO.setItem(new Item[]{item});
                cwbbReqDTO.setCwbbTdRbunitReqDTO(cwbbTdRbunitReqDTO);
                String result = this.getResult(url,cwbbReqDTO);
                cwbbReqDTO.setWaRpmax(preMonth);
                String preMonthResult = this.getResult(url,cwbbReqDTO);
                cwbbReqDTO.setWaRpmax("013");
                cwbbReqDTO.setWaRyear(preYea);
                String preYeaResult = this.getResult(url,cwbbReqDTO);
                if(GyUtils.isNotNull(result)){
                    this.getZcfzxx(insertList,updateList,result,preMonthResult,preYeaResult,ssqqz,gs,nsrsbh);
                }
            }
        });
        Map<String,ZnsbCwbbZlbscjbDO> zlbscjbMap = new HashMap<>();
        if(GyUtils.isNotNull(insertList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx::insertList.size() =={}",insertList.size());
            this.getZcfzbZbxxMap(zlbscjbMap,insertList,sszq);
            zzcfzbMapper.insertBatch(insertList);
        }

        if(GyUtils.isNotNull(updateList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx::updateList.size() =={}",updateList.size());
            this.getZcfzbZbxxMap(zlbscjbMap,updateList,sszq);
            zzcfzbMapper.updateBatch(updateList);
        }
        List<ZnsbCwbbZlbscjbDO> zlbscjbList = zlbscjbMap.entrySet().stream().map(entry->entry.getValue()).collect(Collectors.toList());
        if(GyUtils.isNotNull(zlbscjbList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx::zlbscjbList.size() =={}",zlbscjbList.size());
            zlbscjbMapper.insertBatch(zlbscjbList);
        }
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx  end");
        return null;
    }


    private void getZcfzxx(List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> insertList,List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> updateList,String result,String preMonthResult,String preYeaResult,Map<String,Date> ssqqz,String gsh,String nsrsbh) {
        List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> dqzcfzList = getZcfzbxx(result,ssqqz,gsh,nsrsbh);
        List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> preMonthQzcfzList = getZcfzbxx(preMonthResult,ssqqz,gsh,nsrsbh);
        List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> preYearQzcfzList = getZcfzbxx(preYeaResult,ssqqz,gsh,nsrsbh);
        if(GyUtils.isNotNull(dqzcfzList)){
            for(ZnsbCwbbQykjzzybqyZcfzbyzxDO bqzcfzDO:dqzcfzList){
                if(GyUtils.isNotNull(preMonthQzcfzList)){
                    for(ZnsbCwbbQykjzzybqyZcfzbyzxDO preMonthZcfzDO:preMonthQzcfzList){
                        if(preMonthZcfzDO.getEwbhxh().compareTo(bqzcfzDO.getEwbhxh()) == 0){
                            bqzcfzDO.setByfseQy(bqzcfzDO.getQmyeQy().subtract(preMonthZcfzDO.getQmyeQy()));
                            bqzcfzDO.setByfseZc(bqzcfzDO.getQmyeZc().subtract(preMonthZcfzDO.getQmyeZc()));
                        }
                    }
                }

                if(GyUtils.isNotNull(preYearQzcfzList)){
                    for(ZnsbCwbbQykjzzybqyZcfzbyzxDO preYearZcfzDO:preYearQzcfzList){
                        if (preYearZcfzDO.getEwbhxh().compareTo(bqzcfzDO.getEwbhxh()) == 0){
                            bqzcfzDO.setSnnmyeQy(preYearZcfzDO.getQmyeQy());
                            bqzcfzDO.setSnnmyeZc(preYearZcfzDO.getQmyeZc());
                        }
                    }
                }
            }
            ZnsbCwbbQykjzzybqyZcfzbyzxDO znsbcwbbqykjzzybqyzcfzbyzxdo = new ZnsbCwbbQykjzzybqyZcfzbyzxDO();
            znsbcwbbqykjzzybqyzcfzbyzxdo.setGsh2(gsh);
            znsbcwbbqykjzzybqyzcfzbyzxdo.setSsqq(ssqqz.get("ssqq"));
            znsbcwbbqykjzzybqyzcfzbyzxdo.setSsqz(ssqqz.get("ssqz"));
            List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> znsbcwbbqykjzzybqyzcfzbyzxdoList = zzcfzbMapper.selectLrbList(znsbcwbbqykjzzybqyzcfzbyzxdo);
            if(GyUtils.isNull(znsbcwbbqykjzzybqyzcfzbyzxdoList)){
                insertList.addAll(dqzcfzList);
            }else {
                for(ZnsbCwbbQykjzzybqyZcfzbyzxDO updateDO:znsbcwbbqykjzzybqyzcfzbyzxdoList){
                    for(ZnsbCwbbQykjzzybqyZcfzbyzxDO bqzcfzDO: dqzcfzList){
                        if(bqzcfzDO.getEwbhxh().compareTo(updateDO.getEwbhxh()) == 0){
                            updateDO.setByfseQy(bqzcfzDO.getByfseQy());
                            updateDO.setByfseZc(bqzcfzDO.getByfseZc());
                            updateDO.setQmyeQy(bqzcfzDO.getQmyeQy());
                            updateDO.setQmyeZc(bqzcfzDO.getQmyeZc());
                            updateDO.setSnnmyeQy(bqzcfzDO.getSnnmyeQy());
                            updateDO.setSnnmyeZc(bqzcfzDO.getSnnmyeZc());
                            updateDO.setXgrq(new Date());
                        }
                    }
                    updateList.add(updateDO);
                }
            }

        }
    }

    private List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> getZcfzbxx(String resultString,Map<String,Date> ssqqz,String gsh,String nsrsbh) {
        Map<String,String> dzMap = getZclrHdzMap();
        Map<String,String> zcfzHmc = getZcfzHmc();
        List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> dqzcfzList = new ArrayList<>();
        if(GyUtils.isNull(resultString)){
            return dqzcfzList;
        }
        CwbbResDTO resDTO = JsonUtils.toBean(resultString, CwbbResDTO.class);
        if(GyUtils.isNotNull(resDTO.getTdZLC01XOutput())) {
            List<ItemResDTO> itemResDTOS = resDTO.getTdZLC01XOutput().getItemResDTOs();
            itemResDTOS.stream().forEach(itemResDTO->{
                String dzKey = itemResDTO.getZrow() + "_" + itemResDTO.getZline();
                String ewhh = dzMap.get(dzKey);
                if(GyUtils.isNotNull(ewhh)){
                    ZnsbCwbbQykjzzybqyZcfzbyzxDO znsbcwbbqykjzzybqyzcfzbyzxdo = new ZnsbCwbbQykjzzybqyZcfzbyzxDO();
                    this.setDefaultValue(znsbcwbbqykjzzybqyzcfzbyzxdo);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setGsh2(gsh);
                    String hmc = zcfzHmc.get(ewhh);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setQmyeZc(itemResDTO.getToyear());
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setZcxmmc(hmc);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setEwbhxh(new BigDecimal(ewhh));
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setNsrsbh(nsrsbh);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setSsqq(ssqqz.get("ssqq"));
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setSsqz(ssqqz.get("ssqz"));
                    dqzcfzList.add(znsbcwbbqykjzzybqyzcfzbyzxdo);
                }
            });
        }
        Map<BigDecimal,ZnsbCwbbQykjzzybqyZcfzbyzxDO> hbMap1 = new HashMap<>();
        Map<BigDecimal,ZnsbCwbbQykjzzybqyZcfzbyzxDO> hbMap2 = new HashMap<>();
        Map<BigDecimal,List<ZnsbCwbbQykjzzybqyZcfzbyzxDO>> zcfzListMap = dqzcfzList.stream().collect(Collectors.groupingBy(ZnsbCwbbQykjzzybqyZcfzbyzxDO::getEwbhxh));
        zcfzListMap.entrySet().stream().forEach(entry->{
            List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> zcfzList = entry.getValue();
            BigDecimal jehj = new BigDecimal(zcfzList.stream().mapToDouble(zcfzdo->zcfzdo.getQmyeZc().doubleValue()).sum());
            ZnsbCwbbQykjzzybqyZcfzbyzxDO znsbcwbbqykjzzybqyzcfzbyzxdo = zcfzList.get(0);
            znsbcwbbqykjzzybqyzcfzbyzxdo.setQmyeZc(jehj);
            BigDecimal ewhh = entry.getKey();
            if(ewhh.intValue() > 34){
                hbMap2.put(ewhh,znsbcwbbqykjzzybqyzcfzbyzxdo);
            }else {
                hbMap1.put(ewhh,znsbcwbbqykjzzybqyzcfzbyzxdo);
            }
        });
        for(int i = 1; i <= 39; i++){
            if(i > 34){
                BigDecimal ewhh = new BigDecimal(i);
                ZnsbCwbbQykjzzybqyZcfzbyzxDO znsbcwbbqykjzzybqyzcfzbyzxdo = new ZnsbCwbbQykjzzybqyZcfzbyzxDO();
                this.setDefaultValue(znsbcwbbqykjzzybqyzcfzbyzxdo);
                znsbcwbbqykjzzybqyzcfzbyzxdo.setGsh2(gsh);
                znsbcwbbqykjzzybqyzcfzbyzxdo.setQmyeZc(BigDecimal.ZERO);
                znsbcwbbqykjzzybqyzcfzbyzxdo.setEwbhxh(ewhh);
                znsbcwbbqykjzzybqyzcfzbyzxdo.setNsrsbh(nsrsbh);
                znsbcwbbqykjzzybqyzcfzbyzxdo.setSsqq(ssqqz.get("ssqq"));
                znsbcwbbqykjzzybqyzcfzbyzxdo.setSsqz(ssqqz.get("ssqz"));
                hbMap1.put(ewhh,znsbcwbbqykjzzybqyzcfzbyzxdo);
            }else {
                BigDecimal ewhh = new BigDecimal(i);
                if(GyUtils.isNull(hbMap1.get(ewhh))){
                    ZnsbCwbbQykjzzybqyZcfzbyzxDO znsbcwbbqykjzzybqyzcfzbyzxdo = new ZnsbCwbbQykjzzybqyZcfzbyzxDO();
                    this.setDefaultValue(znsbcwbbqykjzzybqyzcfzbyzxdo);
                    String hmc = zcfzHmc.get(ewhh.toString());
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setGsh2(gsh);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setZcxmmc(hmc);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setQmyeZc(BigDecimal.ZERO);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setEwbhxh(ewhh);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setNsrsbh(nsrsbh);
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setSsqq(ssqqz.get("ssqq"));
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setSsqz(ssqqz.get("ssqz"));
                    hbMap1.put(ewhh,znsbcwbbqykjzzybqyzcfzbyzxdo);
                }
            }
        }

        List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> resultList = new ArrayList<>();
        Map<String,String> zclrHbhdcMap =  getZclrHbhdcMap();
        hbMap1.entrySet().stream().forEach(entry->{
            BigDecimal ewhh = entry.getKey();
            ZnsbCwbbQykjzzybqyZcfzbyzxDO znsbcwbbqykjzzybqyzcfzbyzxdo = entry.getValue();
            String hbhEwhh = zclrHbhdcMap.get(ewhh.toString());
            if(GyUtils.isNull(hbhEwhh)){
                znsbcwbbqykjzzybqyzcfzbyzxdo.setQyxmmc("");
                znsbcwbbqykjzzybqyzcfzbyzxdo.setQmyeQy(BigDecimal.ZERO);
            }else {
                ZnsbCwbbQykjzzybqyZcfzbyzxDO hbhDo = hbMap2.get(new BigDecimal(hbhEwhh));
                if(GyUtils.isNull(hbhDo)){
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setQyxmmc(zcfzHmc.get(hbhEwhh.toString()));
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setQmyeQy(BigDecimal.ZERO);
                }else {
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setQyxmmc(hbhDo.getZcxmmc());
                    znsbcwbbqykjzzybqyzcfzbyzxdo.setQmyeQy(hbhDo.getQmyeZc());
                }
            }
            resultList.add(znsbcwbbqykjzzybqyzcfzbyzxdo);
        });
        return resultList;
    }

    private void getZcfzbZbxxMap(Map<String,ZnsbCwbbZlbscjbDO> zlbscjbMap,List<ZnsbCwbbQykjzzybqyZcfzbyzxDO> lrbList,String sszq){
        lrbList.stream().forEach(zcfzbDO->{
            String key = zcfzbDO.getNsrsbh();
            if(GyUtils.isNull(zlbscjbMap.get(key))){
                ZnsbMhzcQyjbxxmxReqVO mhReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                mhReqVO.setNsrsbh(zcfzbDO.getNsrsbh());
                log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbZbxxMap  nsrsbh={}" , zcfzbDO.getNsrsbh());
                CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxList = nsrxxApi.getNsrxxByNsrsbh(mhReqVO);
                if(GyUtils.isNotNull(nsrxxList) && GyUtils.isNotNull(nsrxxList.getData()) && GyUtils.isNotNull(nsrxxList.getData().getJbxxmxsj())){
                    List<JbxxmxsjVO> nsrList = nsrxxList.getData().getJbxxmxsj();
                    JbxxmxsjVO nsrxx = null;
                    for(JbxxmxsjVO jbxxmxsjvo:nsrList){
                        String kqccsztdjbz = jbxxmxsjvo.getKqccsztdjbz();
                        if("N".equals(kqccsztdjbz)){
                            nsrxx = jbxxmxsjvo;
                        }
                    }
                    log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbZbxxMap  nsrxx={}" , JsonUtils.toJson(nsrxx));
                    if(GyUtils.isNotNull(nsrxx)){
                        String djxh = nsrxx.getDjxh();
                        ZnsbCwbbZlbscjbDO zlbscjbDO = zlbscjbMapper.queryXxBySszq(djxh,sszq);
                        if(GyUtils.isNull(zlbscjbDO)){
                            zlbscjbDO = new ZnsbCwbbZlbscjbDO();
                            String zlbscjuuid = GyUtils.getUuid();
                            String uuid = GyUtils.getUuid();
                            zcfzbDO.setZlbscjuuid(zlbscjuuid);
                            zlbscjbDO.setLrrq(new Date());
                            zlbscjbDO.setYwqdDm("DLZG");
                            zlbscjbDO.setLrrsfid("DSRW");
                            zlbscjbDO.setZfbz1("N");
                            zlbscjbDO.setZlbsuuid(uuid);
                            zlbscjbDO.setZlbscjuuid(zlbscjuuid);
                            zlbscjbDO.setDjxh(djxh);
                            zlbscjbDO.setSszq(sszq);
                            zlbscjbDO.setQydmz(zcfzbDO.getGsh2());
                            zlbscjbMap.put(key,zlbscjbDO);
                        }else {
                            zcfzbDO.setZlbscjuuid(zlbscjbDO.getZlbscjuuid());
                        }
                    }
                }else {
                    log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbZbxxMap  nsrxx=null");
                }
            }else {
                log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbZbxxMap  Zlbscjuuid" , zlbscjbMap.get(key).getZlbscjuuid());
                zcfzbDO.setZlbscjuuid(zlbscjbMap.get(key).getZlbscjuuid());
            }
        });
    }

    /**
     * 获取现金流量表信息 ZF_ZLC15_DISPLAY
     * @return
     */
    @Override
    @Transactional
    public String getXjlbxx(String gsh,String tbny) {
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjlbxx  start");
        String jobParam = XxlJobHelper.getJobParam();
        if(GyUtils.isNotNull(jobParam)){
            String[] parmaArry = jobParam.split(",");
            if(parmaArry.length == 1){
                tbny = parmaArry[0];
            }else if(parmaArry.length == 2){
                gsh = parmaArry[0];
                tbny = parmaArry[1];
            }
        }
//        String url = "http://10.2.8.46:50000/RESTAdapter/SFP011/CashFlowStatement";
        String url = CacheUtils.getXtcs("dlzg_cwbb_url") + "/RESTAdapter/SFP011/CashFlowStatement";
        List<ZnsbCwbbQykjzzybqyXjllbDO> insertList = new ArrayList<>();
        List<ZnsbCwbbQykjzzybqyXjllbDO> updateList = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        if(GyUtils.isNotNull(tbny)){
            calendar.setTime(DateUtil.toDate("yyyyMM",tbny));
        }
        String year = DateUtil.doDateFormat(calendar.getTime(),"yyyy");
        String month = 0 + DateUtil.doDateFormat(calendar.getTime(),"MM");
        String sszq = DateUtil.doDateFormat(calendar.getTime(),"yyyyMM");
        Map<String,Date> ssqqz = getSsqqz(sszq);

        calendar.add(Calendar.MONTH, -1);
        String preMonth = 0 + DateUtil.doDateFormat(calendar.getTime(),"MM");
        String preYea = Integer.parseInt(year) - 1 + "";

        Map<String,String> jtMap = getJtxxMap();
        Map<String,String> gsxxMap = getGsxxMap();
        String finalGsh = gsh;
        jtMap.entrySet().stream().forEach(entry->{
            String jt = entry.getKey();
            boolean sftb = true;
            if(GyUtils.isNotNull(finalGsh) && !jt.equals(finalGsh)){
                sftb = false;
            }
            if(sftb){
                String nsrsbh = entry.getValue();
                CwbbReqDTO cwbbReqDTO = new CwbbReqDTO();
                cwbbReqDTO.setWaRitclg("C1");
                cwbbReqDTO.setWaCongr(jt);
                cwbbReqDTO.setWaRyear(year);
                cwbbReqDTO.setWaRpmax(month);
                cwbbReqDTO.setWaRbunit("");
                CwbbTdRbunitReqDTO cwbbTdRbunitReqDTO = new CwbbTdRbunitReqDTO();
                cwbbReqDTO.setCwbbTdRbunitReqDTO(cwbbTdRbunitReqDTO);
                String result = this.getResult(url,cwbbReqDTO);
                cwbbReqDTO.setWaRpmax(preMonth);
                String preMonthResult = this.getResult(url,cwbbReqDTO);
                cwbbReqDTO.setWaRpmax(month);
                cwbbReqDTO.setWaRyear(preYea);
                String preYeaResult = this.getResult(url,cwbbReqDTO);
                if(GyUtils.isNotNull(result)){
                    this.getXjlbxx(insertList,updateList,result,preMonthResult,preYeaResult,ssqqz,jt,nsrsbh);
                }
            }
        });

        gsxxMap.entrySet().stream().forEach(entry->{
            String gs = entry.getKey();
            boolean sftb = true;
            if(GyUtils.isNotNull(finalGsh) && !gs.equals(finalGsh)){
                sftb = false;
            }
            if(sftb){
                String nsrsbh = entry.getValue();
                CwbbReqDTO cwbbReqDTO = new CwbbReqDTO();
                cwbbReqDTO.setWaRitclg("C1");
                cwbbReqDTO.setWaCongr("");
                cwbbReqDTO.setWaRyear(year);
                cwbbReqDTO.setWaRpmax(month);
                cwbbReqDTO.setWaRbunit("");
                CwbbTdRbunitReqDTO cwbbTdRbunitReqDTO = new CwbbTdRbunitReqDTO();
                Item item = new Item();
                item.setSign("I");
                item.setOption("EQ");
                item.setLow(gs);
                item.setHigh("");
                cwbbTdRbunitReqDTO.setItem(new Item[]{item});
                cwbbReqDTO.setCwbbTdRbunitReqDTO(cwbbTdRbunitReqDTO);

                String result = this.getResult(url,cwbbReqDTO);
                cwbbReqDTO.setWaRpmax(preMonth);
                String preMonthResult = this.getResult(url,cwbbReqDTO);
                cwbbReqDTO.setWaRpmax(month);
                cwbbReqDTO.setWaRyear(preYea);
                String preYeaResult = this.getResult(url,cwbbReqDTO);
                if(GyUtils.isNotNull(result)){
                    this.getXjlbxx(insertList,updateList,result,preMonthResult,preYeaResult,ssqqz,gs,nsrsbh);
                }
            }
        });

        Map<String,ZnsbCwbbZlbscjbDO> zlbscjbMap = new HashMap<>();
        if(GyUtils.isNotNull(insertList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjlbxx::insertList.size() =={}",insertList.size());
            this.getXjllbZbxxMap(zlbscjbMap,insertList,sszq);
            xjllbMapper.insertBatch(insertList);
        }

        if(GyUtils.isNotNull(updateList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjlbxx::updateList.size() =={}",updateList.size());
            this.getXjllbZbxxMap(zlbscjbMap,updateList,sszq);
            xjllbMapper.updateBatch(updateList);
        }
        List<ZnsbCwbbZlbscjbDO> zlbscjbList = zlbscjbMap.entrySet().stream().map(entry->entry.getValue()).collect(Collectors.toList());
        if(GyUtils.isNotNull(zlbscjbList)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjlbxx::zlbscjbList.size() =={}",zlbscjbList.size());
            zlbscjbMapper.insertBatch(zlbscjbList);
        }
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjlbxx  end");
        return null;
    }

    private void getXjlbxx(List<ZnsbCwbbQykjzzybqyXjllbDO> insertList,List<ZnsbCwbbQykjzzybqyXjllbDO> updateList,String result,String preMonthResult,String preYeaResult,Map<String,Date> ssqqz,String gsh,String nsrsbh) {
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjlbxx  start");
        List<ZnsbCwbbQykjzzybqyXjllbDO> xjllbDOList = getXjllb(result,ssqqz,gsh,nsrsbh);
        List<ZnsbCwbbQykjzzybqyXjllbDO> preMonthXjllbDOList = getXjllb(preMonthResult,ssqqz,gsh,nsrsbh);
        List<ZnsbCwbbQykjzzybqyXjllbDO> preYearXjllbDOList = getXjllb(preYeaResult,ssqqz,gsh,nsrsbh);
        if(GyUtils.isNull(xjllbDOList)){
            return;
        }
        xjllbDOList.stream().forEach(xjllbDO->{
            Date ssqq = xjllbDO.getSsqq();
            String month = DateUtil.doDateFormat(ssqq,"MM");
            if("01".equals( month)){
                xjllbDO.setByfse(xjllbDO.getBqje());
            }else {
                if(GyUtils.isNotNull(preMonthXjllbDOList)){
                    preMonthXjllbDOList.stream().forEach(preMonthXjllbDO->{
                        if(xjllbDO.getEwbhxh().equals(preMonthXjllbDO.getEwbhxh())){
                            xjllbDO.setByfse(xjllbDO.getBqje().subtract(preMonthXjllbDO.getBqje()));
                        }
                    });
                }
            }


            if(GyUtils.isNotNull(preYearXjllbDOList)){
                preYearXjllbDOList.stream().forEach(preYearXjllbDO->{
                    if(xjllbDO.getEwbhxh().equals(preYearXjllbDO.getEwbhxh())){
                        xjllbDO.setSqje1(preYearXjllbDO.getBqje());
                    }
                });
            }
        });

        List<ZnsbCwbbQykjzzybqyXjllbDO> xjllbDOListNew = new ArrayList<>();
        Map<Long,List<ZnsbCwbbQykjzzybqyXjllbDO>> xjlbListMap = xjllbDOList.stream().collect(Collectors.groupingBy(ZnsbCwbbQykjzzybqyXjllbDO::getEwbhxh));
        xjlbListMap.entrySet().stream().forEach(entry->{
            List<ZnsbCwbbQykjzzybqyXjllbDO> xjlList = entry.getValue();
            BigDecimal bqjehj = new BigDecimal(xjlList.stream().mapToDouble(zcfzdo->zcfzdo.getBqje().doubleValue()).sum());
            BigDecimal sqjehj = new BigDecimal(xjlList.stream().mapToDouble(zcfzdo->zcfzdo.getSqje1().doubleValue()).sum());
            BigDecimal byfsehj = new BigDecimal(xjlList.stream().mapToDouble(zcfzdo->zcfzdo.getByfse().doubleValue()).sum());
            ZnsbCwbbQykjzzybqyXjllbDO xjldo = xjlList.get(0);
            xjldo.setBqje(bqjehj.setScale(2, RoundingMode.HALF_UP));
            xjldo.setSqje1(sqjehj.setScale(2, RoundingMode.HALF_UP));
            xjldo.setByfse(byfsehj.setScale(2, RoundingMode.HALF_UP));
            xjllbDOListNew.add(xjldo);
        });
        Map<String,String> xjllHmc = getXjllbHmc();
        xjllHmc.entrySet().stream().forEach(entry->{
            String ewhhm = entry.getKey();
            String hmc = entry.getValue();
            boolean exitFlag = false;
            for(ZnsbCwbbQykjzzybqyXjllbDO xjllbDO:xjllbDOListNew){
                if (xjllbDO.getEwbhxh().toString().equals(ewhhm)){
                    exitFlag = true;
                    break;
                }
            }
            if(!exitFlag) {
                ZnsbCwbbQykjzzybqyXjllbDO xjllbDO = new ZnsbCwbbQykjzzybqyXjllbDO();
                this.setDefaultValue(xjllbDO);
                xjllbDO.setNsrsbh(nsrsbh);
                xjllbDO.setGsh2(gsh);
                xjllbDO.setHmc(hmc);
                xjllbDO.setEwbhxh(Long.parseLong(ewhhm));
                xjllbDO.setBqje(BigDecimal.ZERO);
                xjllbDO.setSqje1(BigDecimal.ZERO);
                xjllbDO.setByfse(BigDecimal.ZERO);
                xjllbDO.setSsqq(ssqqz.get("ssqq"));
                xjllbDO.setSsqz(ssqqz.get("ssqz"));
                xjllbDOListNew.add(xjllbDO);
            }
        });

        ZnsbCwbbQykjzzybqyXjllbDO xjllbDO = new ZnsbCwbbQykjzzybqyXjllbDO();
        xjllbDO.setGsh2(gsh);
        xjllbDO.setSsqq(ssqqz.get("ssqq"));
        xjllbDO.setSsqz(ssqqz.get("ssqz"));
        List<ZnsbCwbbQykjzzybqyXjllbDO> llbList = xjllbMapper.queryXjllbList(xjllbDO);
        if(GyUtils.isNull(llbList)){
            insertList.addAll(xjllbDOListNew);
        }else {
            llbList.stream().forEach(llbDO->{
                xjllbDOListNew.stream().forEach(xjlDO->{
                    if(llbDO.getEwbhxh() == xjlDO.getEwbhxh()){
                        llbDO.setByfse(xjlDO.getByfse());
                        llbDO.setBqje(xjlDO.getBqje());
                        llbDO.setSqje1(xjlDO.getSqje1());
                        llbDO.setXgrq(new Date());
                        updateList.add(llbDO);
                    }
                });
            });
        }
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjlbxx  end");
    }

    private List<ZnsbCwbbQykjzzybqyXjllbDO> getXjllb(String resultString,Map<String,Date> ssqqz,String gsh,String nsrsbh) {
        Map<String,String> dzMap = getXjlbHdzMap();
        Map<String,String> xjllHmc = getXjllbHmc();
        List<ZnsbCwbbQykjzzybqyXjllbDO> xjllbDOList = new ArrayList<>();
        if(GyUtils.isNull(resultString)){
            return xjllbDOList;
        }
        CwbbResDTO resDTO = JsonUtils.toBean(resultString, CwbbResDTO.class);
        if(GyUtils.isNotNull(resDTO) && GyUtils.isNotNull(resDTO.getTdZLC15Output())){
            List<ItemResDTO> itemResDTOS = resDTO.getTdZLC15Output().getItemResDTOs();
            itemResDTOS.stream().forEach(itemResDTO->{
                ZnsbCwbbQykjzzybqyXjllbDO xjllbDO = new ZnsbCwbbQykjzzybqyXjllbDO();
                String dzKey = itemResDTO.getZrow() + "_" + itemResDTO.getZlnumt();
                String ewhh = dzMap.get(dzKey);
                if(GyUtils.isNotNull(ewhh)){
                    String hmc = xjllHmc.get(ewhh);
                    this.setDefaultValue(xjllbDO);
                    xjllbDO.setNsrsbh(nsrsbh);
                    xjllbDO.setGsh2(gsh);
                    xjllbDO.setHmc(hmc);
                    xjllbDO.setEwbhxh(Long.parseLong(ewhh));
                    xjllbDO.setBqje(new BigDecimal(itemResDTO.getTmenth()));
                    xjllbDO.setSsqq(ssqqz.get("ssqq"));
                    xjllbDO.setSsqz(ssqqz.get("ssqz"));
                    xjllbDOList.add(xjllbDO);
                }
            });
        }
        return xjllbDOList;
    }

    private void getXjllbZbxxMap(Map<String,ZnsbCwbbZlbscjbDO> zlbscjbMap,List<ZnsbCwbbQykjzzybqyXjllbDO> xjllbList,String sszq){
        xjllbList.stream().forEach(xjllbDO->{
            String key = xjllbDO.getNsrsbh();
            if(GyUtils.isNull(zlbscjbMap.get(key))){
                ZnsbMhzcQyjbxxmxReqVO mhReqVO = new ZnsbMhzcQyjbxxmxReqVO();
                mhReqVO.setNsrsbh(xjllbDO.getNsrsbh());
                log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjllbZbxxMap  Nsrsbh()={}" , xjllbDO.getNsrsbh());
                CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxList = nsrxxApi.getNsrxxByNsrsbh(mhReqVO);
                if(GyUtils.isNotNull(nsrxxList) && GyUtils.isNotNull(nsrxxList.getData()) && GyUtils.isNotNull(nsrxxList.getData().getJbxxmxsj())){
                    List<JbxxmxsjVO> nsrList = nsrxxList.getData().getJbxxmxsj();
                    JbxxmxsjVO nsrxx = null;
                    for(JbxxmxsjVO jbxxmxsjvo:nsrList){
                        String kqccsztdjbz = jbxxmxsjvo.getKqccsztdjbz();
                        if("N".equals(kqccsztdjbz)){
                            nsrxx = jbxxmxsjvo;
                        }
                    }
                    log.info("ZnsbTzzxDlzgDataSynServiceImp_getLrbZbxxMap  nsrxx={}" , JsonUtils.toJson(nsrxx));
                    if(GyUtils.isNotNull(nsrxx)){
                        String djxh = nsrxx.getDjxh();
                        ZnsbCwbbZlbscjbDO zlbscjbDO = zlbscjbMapper.queryXxBySszq(djxh,sszq);
                        if(GyUtils.isNull(zlbscjbDO)){
                            zlbscjbDO = new ZnsbCwbbZlbscjbDO();
                            String zlbscjuuid = GyUtils.getUuid();
                            String uuid = GyUtils.getUuid();
                            xjllbDO.setZlbscjuuid(zlbscjuuid);
                            zlbscjbDO.setLrrq(new Date());
                            zlbscjbDO.setYwqdDm("DLZG");
                            zlbscjbDO.setLrrsfid("DSRW");
                            zlbscjbDO.setZfbz1("N");
                            zlbscjbDO.setZlbsuuid(uuid);
                            zlbscjbDO.setZlbscjuuid(zlbscjuuid);
                            zlbscjbDO.setDjxh(djxh);
                            zlbscjbDO.setSszq(sszq);
                            zlbscjbDO.setQydmz(xjllbDO.getGsh2());
                            zlbscjbMap.put(key,zlbscjbDO);
                        }else {
                            xjllbDO.setZlbscjuuid(zlbscjbDO.getZlbscjuuid());
                        }
                    }
                }else {
                    log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjllbZbxxMap  nsrxx=null");
                }
            }else {
                log.info("ZnsbTzzxDlzgDataSynServiceImp_getXjllbZbxxMap  Zlbscjuuid" , zlbscjbMap.get(key).getZlbscjuuid());
                xjllbDO.setZlbscjuuid(zlbscjbMap.get(key).getZlbscjuuid());
            }
        });
    }

    private String getResult(String url,CwbbReqDTO cwbbReqDTO) {
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getResult  start");
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getResult  url={}",url);
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getResult  cwbbReqDTO={}",JsonUtils.toJson(cwbbReqDTO));
        String result = "";
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        try {
            HttpPost request = new HttpPost(url);
            request.setHeader("Content-Type", "application/json");
//            String mm = "zinterfacesfp:DHHI@sfp_2024";
            String mm = CacheUtils.getXtcs("dlzg_cwbb_auth");
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getResult  mm={}",mm);
            request.setHeader("Authorization", "Basic " + Base64.getEncoder().encodeToString(mm.getBytes()));
            StringEntity entity = new StringEntity(JsonUtils.toJson(cwbbReqDTO));
            request.setEntity(entity);
            CloseableHttpResponse response = httpClient.execute(request);
            // 请求状态码
            int statusCode = response.getStatusLine().getStatusCode();
            log.info("ZnsbTzzxQysdDataSynServiceImp_ZnsbTzzxQysdDataSynServiceImp  statusCode={}" , statusCode);
            if(statusCode == 200){
                HttpEntity responseEntity = response.getEntity();
                if(responseEntity != null) {
                    result = EntityUtils.toString(responseEntity, "utf-8");
                }
            }else{
                HttpEntity responseEntity = response.getEntity();
                if(responseEntity != null) {
                    result = EntityUtils.toString(responseEntity, "utf-8");
                }
            }
        } catch(Exception ex) {
            log.error("ZnsbTzzxQysdDataSynServiceImp_ZnsbTzzxQysdDataSynServiceImp异常：" , ex);
        }finally {
            if(GyUtils.isNotNull(httpClient)){
                try {
                    httpClient.close();
                } catch (IOException e) {
                    log.error("ZnsbTzzxQysdDataSynServiceImp_ZnsbTzzxQysdDataSynServiceImp异常：" , e);
                }
            }
        }
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getResult  result={}",result);
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getResult  end");
        return result;
    }

    private Map<String,Date> getSsqqz(String sszq){
        Map<String,Date> map = new HashMap<>();
        String ssnn = sszq.substring(0,4);
        String ssmm = sszq.substring(sszq.length()-2);
        String ssqq = ssnn + "-" + ssmm + "-01";
        Date ssqqD = DateUtil.toDate("yyyy-MM-dd", ssqq);
        map.put("ssqq", ssqqD);
        Calendar ssqzCal = Calendar.getInstance();
        ssqzCal.setTime(ssqqD);
        ssqzCal.set(Calendar.DAY_OF_MONTH,ssqzCal.getActualMaximum(Calendar.DAY_OF_MONTH));
        map.put("ssqz", ssqzCal.getTime());
        return map;
    }

    private Map<String, String> getLrHdzMap() {
        Map<String, String> hdzMap = new HashMap<>();
        hdzMap.put("1", "1");//一、营业收入
        hdzMap.put("4", "2");//减:营业成本
        hdzMap.put("7", "3");//税金及附加
        hdzMap.put("8", "4");//销售费用
        hdzMap.put("9", "5");//管理费用
        hdzMap.put("10", "7");//财务费用
        hdzMap.put("11", "17");//资产减值损失（损失以“-”号填列）
        hdzMap.put("12", "16");//信用减值损失（损失以“—”号填列）
        hdzMap.put("13", "15");//公允价值变动收益（损失以“-”号填列）
        hdzMap.put("14", "11");//投资收益(损失以“—”号填列)
        hdzMap.put("15", "18");//资产处置收益（损失以“-”号填列）
        hdzMap.put("16", "10");//加：其他收益
        hdzMap.put("17", "19");//二、营业利润(亏损以“—”号填列)
        hdzMap.put("18", "20");//  　加:营业外收入
        hdzMap.put("19", "21");//  　减:营业外支出
        hdzMap.put("20", "22");//三、利润总额(亏损总额以“—”号填列)
        hdzMap.put("21", "23");// 　　减:所得税费用
        hdzMap.put("22", "24");//  　四、净利润(净亏损以“—”号填列)
        return hdzMap;
    }

    private Map<String, String> getLryzxHmc() {
        Map<String, String> lryzxColumn = new HashMap<>();
        lryzxColumn.put("1", "一、营业收入");
        lryzxColumn.put("2", "减：营业成本");
        lryzxColumn.put("3", "税金及附加");
        lryzxColumn.put("4", "销售费用");
        lryzxColumn.put("5", "管理费用");
        lryzxColumn.put("6", "研发费用");
        lryzxColumn.put("7", "财务费用");
        lryzxColumn.put("8", "其中：利息费用");
        lryzxColumn.put("9", "利息收入");
        lryzxColumn.put("10", "加：其他收益");
        lryzxColumn.put("11", "投资收益(损失以“—”号填列)");
        lryzxColumn.put("12", "其中:对联营企业和合营企业的投资收益");
        lryzxColumn.put("13", "以摊余成本计量的金融资产终止确认收益(损失以“—”号填列)");
        lryzxColumn.put("14", "净敞口套期收益(损失以“—”号填列)");
        lryzxColumn.put("15", "公允价值变动收益（损失以“-”号填列）");
        lryzxColumn.put("16", "信用减值损失（损失以“—”号填列）");
        lryzxColumn.put("17", "资产减值损失（损失以“-”号填列）");
        lryzxColumn.put("18", "资产处置收益（损失以“-”号填列）");
        lryzxColumn.put("19", "二、营业利润(亏损以“—”号填列)");
        lryzxColumn.put("20", "加：营业外收入");
        lryzxColumn.put("21", "减：营业外支出");
        lryzxColumn.put("22", "三、利润总额(亏损总额以“—”号填列)");
        lryzxColumn.put("23", "减:所得税费用");
        lryzxColumn.put("24", "四、净利润(净亏损以“—”号填列)");
        lryzxColumn.put("25", "（一）持续经营净利润（净亏损以“—”号填列）");
        lryzxColumn.put("26", "（二）终止经营净利润（净亏损以“—”号填列）");
        lryzxColumn.put("27", "五、其他综合收益的税后净额");
        lryzxColumn.put("28", "（一）不能重分类进损益的其他综合收益");
        lryzxColumn.put("29", "1．重新计量设定受益计划变动额");
        lryzxColumn.put("30", "2．权益法下不能转损益的其他综合收益");
        lryzxColumn.put("31", "3．其他权益工具投资公允价值变动");
        lryzxColumn.put("32", "4．企业自身信用风险公允价值变动");
        lryzxColumn.put("33", "（二）将重分类进损益的其他综合收益");
        lryzxColumn.put("34", "1．权益法下可转损益的其他综合收益");
        lryzxColumn.put("35", "2．其他债权投资公允价值变动");
        lryzxColumn.put("36", "3．金融资产重分类计入其他综合收益的金额");
        lryzxColumn.put("37", "4．其他债权投资信用减值准备");
        lryzxColumn.put("38", "5．现金流量套期储备");
        lryzxColumn.put("39", "6．外币财务报表折算差额");
        lryzxColumn.put("40", "六、综合收益总额");
        lryzxColumn.put("41", "（一）基本每股收益");
        lryzxColumn.put("42", "（二）稀释每股收益");
        return lryzxColumn;
    }


    private Map<String, String> getZclrHdzMap() {
        Map<String, String> hdzMap = new HashMap<>();
        hdzMap.put("1_0020", "1");//  货币资金
        hdzMap.put("1_0030", "2");//  交易性金融资产
        hdzMap.put("1_0035", "6");//  应收款项融资
        hdzMap.put("1_0040", "4");//  应收票据
        hdzMap.put("1_0050", "5");//  应收账款
        hdzMap.put("1_0060", "7");//  预付款项
        hdzMap.put("1_0100", "9");//存货
        hdzMap.put("1_0125", "10");//  合同资产
        hdzMap.put("1_0126", "11");//  持有待售资产
        hdzMap.put("1_0130", "12");//  一年内到期的非流动资产
        hdzMap.put("1_0140", "13");//  其他流动资产
        hdzMap.put("1_0150", "14");//  流动资产合计
        hdzMap.put("1_0180", "19");//    其他权益工具投资
        hdzMap.put("1_0190", "15");// 持有至到期投资????
        hdzMap.put("1_0180", "16");// 其他权益工具投资
        hdzMap.put("1_0200", "17");// 长期应收款
        hdzMap.put("1_0210", "18");// 长期股权投资
        hdzMap.put("1_0215", "20");// 其他非流动金融
        hdzMap.put("1_0220", "21");// 投资性房地产
        hdzMap.put("1_0225", "26");// 使用权资产
        hdzMap.put("1_0310", "27");// 无形资产
        hdzMap.put("1_0330", "29");// 商誉
        hdzMap.put("1_0340", "25");// 油气资产
        hdzMap.put("1_0350", "28");// 开发支出
        hdzMap.put("1_0360", "30");// 长期待摊费用
        hdzMap.put("1_0370", "31");// 递延所得税资产
        hdzMap.put("1_0380", "32");// 其他非流动资产
        hdzMap.put("1_0400", "33");// 非流动资产合计
        hdzMap.put("1_0410", "34");// 资 产 总 计

        hdzMap.put("2_0020", "35");// 短期借款
        hdzMap.put("2_0030", "36");// 交易性金融负债
        hdzMap.put("2_0035", "37");// 衍生金融负债
        hdzMap.put("2_0040", "38");// 应付票据
        hdzMap.put("2_0050", "39");// 应付账款
        hdzMap.put("2_0055", "40");// 预收款项
        hdzMap.put("2_0060", "41");// 合同负债
        hdzMap.put("2_0070", "42");// 应付职工薪酬
        hdzMap.put("2_0100", "43");// 应交税费
        hdzMap.put("2_0160", "46");// 一年内到期的非流动负债
        hdzMap.put("2_0170", "47");// 其他流动负债
        hdzMap.put("2_0190", "48");// 流动负债合计
        hdzMap.put("2_0210", "49");// 长期借款
        hdzMap.put("2_0215", "56");// 递延收益
        hdzMap.put("2_0220", "50");// 应付债券
        hdzMap.put("2_0225", "53");// 租赁负债
        hdzMap.put("2_0250", "55");// 预计负债
        hdzMap.put("2_0260", "57");// 递延所得税负债
        hdzMap.put("2_0270", "58");// 其他非流动负债
        hdzMap.put("2_0280", "59");// 非流动负债合计
        hdzMap.put("2_0290", "60");// 负 债 合 计
        hdzMap.put("2_0310", "61");// 实收资本（股本）
        hdzMap.put("2_0320", "65");// 资本公积
        hdzMap.put("2_0330", "66");// 减：库存股
        hdzMap.put("2_0340", "67");// 其他综合收益
        hdzMap.put("2_0350", "68");// 专项储备
        hdzMap.put("2_0360", "69");// 盈余公积
        hdzMap.put("2_0370", "70");// 未分配利润
        hdzMap.put("2_0380", "71");// 归属于母公司所有者权益合计
        hdzMap.put("2_0410", "72");// 负债和所有者权益总计

        hdzMap.put("1_0270", "22");// 固定资产---多行
        hdzMap.put("1_0280", "22");// 固定资产---多行
        hdzMap.put("1_0290", "23");// 在建工程---多行
        hdzMap.put("1_0300", "23");// 在建工程---多行
        hdzMap.put("1_0070", "8");//  其他应收款---多行
        hdzMap.put("1_0080", "8");//  其他应收款---多行
        hdzMap.put("1_0090", "8");//  其他应收款---多行
        hdzMap.put("2_0130", "44");//  其他应付款---多行
        hdzMap.put("2_0140", "44");//  其他应付款---多行
        hdzMap.put("2_0150", "44");//  其他应付款---多行
        hdzMap.put("2_0230", "54");//  长期应付款---多行
        hdzMap.put("2_0240", "54");//  长期应付款---多行
        return hdzMap;
    }

    private Map<String, String> getZclrHbhdcMap() {
        //11、 17、18、27、29、30、31特殊列
        Map<String, String> hdzMap = new HashMap<>();
        hdzMap.put("1", "35");
        hdzMap.put("2", "36");
        hdzMap.put("3", "37");
        hdzMap.put("6", "40");
        hdzMap.put("4", "38");
        hdzMap.put("5", "39");
        hdzMap.put("7", "41");
        hdzMap.put("8", "42");
        hdzMap.put("9", "43");
        hdzMap.put("10", "44");
        hdzMap.put("11", "45");
        hdzMap.put("12", "46");
        hdzMap.put("13", "47");
        hdzMap.put("14", "48");
        hdzMap.put("19", "53");
        hdzMap.put("15", "49");
        hdzMap.put("16", "50");
        hdzMap.put("17", "51");
        hdzMap.put("18", "52");
        hdzMap.put("20", "54");
        hdzMap.put("21", "55");
        hdzMap.put("26", "60");
        hdzMap.put("22", "56");
        hdzMap.put("23", "57");
        hdzMap.put("24", "58");
        hdzMap.put("25", "59");
        hdzMap.put("28", "61");
        hdzMap.put("29", "62");
        hdzMap.put("30", "63");
        hdzMap.put("31", "64");
        hdzMap.put("32", "65");
        hdzMap.put("33", "66");
        hdzMap.put("34", "67");
        hdzMap.put("35", "68");
        hdzMap.put("36", "69");
        hdzMap.put("37", "70");
        hdzMap.put("38", "71");
        hdzMap.put("39", "72");
        return hdzMap;
    }

    private Map<String, String> getZcfzHmc() {
        Map<String, String> lryzxColumn = new HashMap<>();
        lryzxColumn.put("1", "货币资金");
        lryzxColumn.put("2", "交易性金融资产");
        lryzxColumn.put("3", "衍生金融资产");
        lryzxColumn.put("4", "应收票据");
        lryzxColumn.put("5", "应收账款");
        lryzxColumn.put("6", "应收款项融资");
        lryzxColumn.put("7", "预付款项");
        lryzxColumn.put("8", "其他应收款");
        lryzxColumn.put("9", "存货");
        lryzxColumn.put("10", "合同资产");
        lryzxColumn.put("11", "持有待售资产");
        lryzxColumn.put("12", "一年内到期的非流动资产");
        lryzxColumn.put("13", "其他流动资产");
        lryzxColumn.put("14", "流动资产合计");
        lryzxColumn.put("15", "债权投资");
        lryzxColumn.put("16", "其他债券投资");
        lryzxColumn.put("17", "长期应收款");
        lryzxColumn.put("18", "长期股权投资");
        lryzxColumn.put("19", "其他权益工具投资");
        lryzxColumn.put("20", "其他非流动金融");
        lryzxColumn.put("21", "投资性房地产");
        lryzxColumn.put("22", "固定资产");
        lryzxColumn.put("23", "在建工程");
        lryzxColumn.put("24", "生产性生物资产");
        lryzxColumn.put("25", "油气资产");
        lryzxColumn.put("26", "使用权资产");
        lryzxColumn.put("27", "无形资产");
        lryzxColumn.put("28", "开发支出");
        lryzxColumn.put("29", "商誉");
        lryzxColumn.put("30", "长期待摊费用");
        lryzxColumn.put("31", "递延所得税资产");
        lryzxColumn.put("32", "其他非流动资产");
        lryzxColumn.put("33", "非流动资产合计");
        lryzxColumn.put("34", "资产合计");

        lryzxColumn.put("35", "短期借款");
        lryzxColumn.put("36", "交易性金融负债");
        lryzxColumn.put("37", "衍生金融负债");
        lryzxColumn.put("38", "应付票据");
        lryzxColumn.put("39", "应付账款");
        lryzxColumn.put("40", "预收款项");
        lryzxColumn.put("41", "合同负债");
        lryzxColumn.put("42", "应付职工薪酬");
        lryzxColumn.put("43", "应交税费");
        lryzxColumn.put("44", "其他应付款");
        lryzxColumn.put("45", "持有待售负债");
        lryzxColumn.put("46", "一年内到期的非流动负债");
        lryzxColumn.put("47", "其他流动负债");
        lryzxColumn.put("48", "流动负债合计");
        lryzxColumn.put("49", "长期借款");
        lryzxColumn.put("50", "应付债券");
        lryzxColumn.put("51", "其中：优先股");
        lryzxColumn.put("52", "永续债");
        lryzxColumn.put("53", "租赁负债");
        lryzxColumn.put("54", "长期应付款");
        lryzxColumn.put("55", "预计负债");
        lryzxColumn.put("56", "递延收益");
        lryzxColumn.put("57", "递延所得税负债");
        lryzxColumn.put("58", "其他非流动负债");
        lryzxColumn.put("59", "非流动负债合计");
        lryzxColumn.put("60", "负债合计");
        lryzxColumn.put("61", "实收资本（股本）");
        lryzxColumn.put("62", "其他权益工具");
        lryzxColumn.put("63", "其中：优先股");
        lryzxColumn.put("64", "永续债");
        lryzxColumn.put("65", "资本公积");
        lryzxColumn.put("66", "减：库存股");
        lryzxColumn.put("67", "其他综合收益");
        lryzxColumn.put("68", "专项储备");
        lryzxColumn.put("69", "盈余公积");
        lryzxColumn.put("70", "未分配利润");
        lryzxColumn.put("71", "所有者权益（或股东权益）合计");
        lryzxColumn.put("72", "负债和所有者权益（或股东权益）总计");
        return lryzxColumn;
    }

    private Map<String, String> getXjlbHdzMap() {
        Map<String, String> hdzMap = new HashMap<>();
        hdzMap.put("1_1", "1");//销售商品、提供劳务收到的现金
        hdzMap.put("1_3", "2");//收到的税费返还
        hdzMap.put("1_4", "3");//收到其他与经营活动有关的现金
        hdzMap.put("1_5", "4");//现金流入小计
        hdzMap.put("1_6", "5");//购买商品、接受劳务支付的现金
        hdzMap.put("1_8", "6");//支付给职工以及为职工支付的现金
        hdzMap.put("1_12", "8");//支付的其他与经营活动有关的现金
        hdzMap.put("1_13", "9");//现金流出小计
        hdzMap.put("1_14", "10");//经营活动产生的现金流量净额
        hdzMap.put("1_15", "11");//收回投资所收到的现金
        hdzMap.put("1_18", "13");//处置固定资产、无形资产和其他长期资产而收到的现金净额
        hdzMap.put("1_19", "15");//收到的其他与投资活动有关的现金
        hdzMap.put("1_20", "16");//投资活动现金流入小计
        hdzMap.put("1_21", "17");//购建固定资产、无形资产和其他长期资产所支付的现金
        hdzMap.put("1_25", "21");//现金流出小计
        hdzMap.put("1_26", "22");//投资活动产生的现金流量净额
        hdzMap.put("1_27", "23");//吸收投资收到的现金
        hdzMap.put("1_29", "24");//取得借款收到的现金
        hdzMap.put("1_30", "25");//收到的其他与筹资活动有关的现金
        hdzMap.put("1_31", "26");//筹资活动现金流入小计
        hdzMap.put("1_32", "27");//偿还债务支付的现金
        hdzMap.put("2_39", "30");//筹资活动现金流出小计
        hdzMap.put("2_40", "31");//筹资活动产生的现金流量净额
        hdzMap.put("2_41", "32");//汇率变动对现金及现金等价物的影响
        hdzMap.put("2_42", "33");//现金及现金等价物净增加额

        hdzMap.put("1_9", "7");//支付的各项税费-----多行
        hdzMap.put("1_10", "7");//支付的所得税款-----多行
        hdzMap.put("1_11", "7");//支付的除增值税、所得税以外的其他税费-----多行
        hdzMap.put("1_22", "20");//支付其他与投资活动有关的现金-----多行
        hdzMap.put("1_23", "20");//支付其他与投资活动有关的现金-----多行
        hdzMap.put("1_24", "20");//支付其他与投资活动有关的现金-----多行
        hdzMap.put("1_33", "28");//分配股利、利润或偿付利息支付的现金-----多行
        hdzMap.put("1_34", "28");//分配股利、利润或偿付利息支付的现金-----多行
        hdzMap.put("1_35", "28");//分配股利、利润或偿付利息支付的现金-----多行
        hdzMap.put("2_36", "28");//分配股利、利润或偿付利息支付的现金-----多行
        hdzMap.put("2_37", "29");//分配股利、利润或偿付利息支付的现金-----多行
        hdzMap.put("2_38", "29");//分配股利、利润或偿付利息支付的现金-----多行
        return hdzMap;
    }

    private Map<String, String> getXjllbHmc() {
        Map<String, String> xjllbColumn = new HashMap<>();
        xjllbColumn.put("1", "销售商品、提供劳务收到的现金");
        xjllbColumn.put("2", "收到的税费返还");
        xjllbColumn.put("3", "收到其他与经营活动有关的现金");
        xjllbColumn.put("4", "经营活动现金流入小计");
        xjllbColumn.put("5", "购买商品、接受劳务支付的现金");
        xjllbColumn.put("6", "支付给职工以及为职工支付的现金");
        xjllbColumn.put("7", "支付的各项税费");
        xjllbColumn.put("8", "支付其他与经营活动有关的现金");
        xjllbColumn.put("9", "经营活动现金流出小计");
        xjllbColumn.put("10", "经营活动产生的现金流量净额");
        xjllbColumn.put("11", "收回投资收到的现金");
        xjllbColumn.put("12", "取得投资收益收到的现金");
        xjllbColumn.put("13", "处置固定资产、无形资产和其他长期资产收回的现金净额");
        xjllbColumn.put("14", "处置子公司及其他营业单位收到的现金净额");
        xjllbColumn.put("15", "收到其他与投资活动有关的现金");
        xjllbColumn.put("16", "投资活动现金流入小计");
        xjllbColumn.put("17", "购建固定资产、无形资产和其他长期资产支付的现金");
        xjllbColumn.put("18", "投资支付的现金");
        xjllbColumn.put("19", "取得子公司及其他营业单位支付的现金净额");
        xjllbColumn.put("20", "支付其他与投资活动有关的现金");
        xjllbColumn.put("21", "投资活动现金流出小计");
        xjllbColumn.put("22", "投资活动产生的现金流量净额");
        xjllbColumn.put("23", "吸收投资收到的现金");
        xjllbColumn.put("24", "取得借款收到的现金");
        xjllbColumn.put("25", "收到其他与筹资活动有关的现金");
        xjllbColumn.put("26", "筹资活动现金流入小计");
        xjllbColumn.put("27", "偿还债务支付的现金");
        xjllbColumn.put("28", "分配股利、利润或偿付利息支付的现金");
        xjllbColumn.put("29", "支付其他与筹资活动有关的现金");
        xjllbColumn.put("30", "筹资活动现金流出小计");
        xjllbColumn.put("31", "筹资活动产生的现金流量净额");
        xjllbColumn.put("32", "四、汇率变动对现金及现金等价物的影响");
        xjllbColumn.put("33", "五、现金及现金等价物净增加额");
        xjllbColumn.put("34", "加：期初现金及现金等价物余额");
        xjllbColumn.put("35", "六、期末现金及现金等价物余额");
        return xjllbColumn;
    }

    private Map<String, String> getGsxxMap() {
        Map<String, String> gsxxMap = new HashMap<>();
        gsxxMap.put("C0111-4111","91210200118310450R");
        gsxxMap.put("C0221-4221","912102136740543399");
        gsxxMap.put("C0411-4411","91210211MA0XMBBM77");
        gsxxMap.put("C0144-4144","9121028167409682XF");
        gsxxMap.put("C0121-4121","91210200241114949Y");
        gsxxMap.put("C0141-4141","91210200726042816N");
        gsxxMap.put("C0181-4181","912102007920068131");
        gsxxMap.put("C0461-4461","91210203MA11F2A6X2");
        gsxxMap.put("C0132-4132","9121028167405569X5");
        gsxxMap.put("C0125-4125","91140121MA0KNXXE50");
        gsxxMap.put("C0124-4124","91130404MA091W876Q");
        gsxxMap.put("C0123-4123","91210311395856005C");
        gsxxMap.put("C0171-4171","91210212787346610C");
        gsxxMap.put("C0191-4191","912102127969045659");
        gsxxMap.put("C0211-4211","912102126692201957");
        gsxxMap.put("C0341-4341","91440800MA4WCFEGXF");
        gsxxMap.put("C0371-4371","91210211MA0UD1C208");
        gsxxMap.put("C0122-4122","91130230593596865E");
        gsxxMap.put("C0126-4126","91520221MA6J4YYQ9J");
        gsxxMap.put("C0143-4143","91320924551178606P");
        gsxxMap.put("C0471-4471","91210211MADPR5TJ4L");
        return gsxxMap;
    }

    private Map<String,String> getJtxxMap() {
        Map<String, String> jtxxMap = new HashMap<>();
        jtxxMap.put("3210","91210200716904902K");
        return jtxxMap;
    }


    private void setDefaultValue(ZnsbCwbbQykjzzybqyLrbyzxDO znsbcwbbqykjzzybqylrbyzxdo){
        znsbcwbbqykjzzybqylrbyzxdo.setUuid(GyUtils.getUuid());
        znsbcwbbqykjzzybqylrbyzxdo.setLrrq(new Date());
        znsbcwbbqykjzzybqylrbyzxdo.setXgrq(new Date());
        znsbcwbbqykjzzybqylrbyzxdo.setYwqdDm("SYS");
        znsbcwbbqykjzzybqylrbyzxdo.setSjtbSj(new Date());
        znsbcwbbqykjzzybqylrbyzxdo.setSjgsdq("TZ_USER");
        znsbcwbbqykjzzybqylrbyzxdo.setSjcsdq("TZ_USER");
    }

    private void setDefaultValue(ZnsbCwbbQykjzzybqyXjllbDO xjllbDO){
        xjllbDO.setUuid(GyUtils.getUuid());
        xjllbDO.setLrrq(new Date());
        xjllbDO.setXgrq(new Date());
        xjllbDO.setYwqdDm("SYS");
        xjllbDO.setSjtbSj(new Date());
        xjllbDO.setSjgsdq("TZ_USER");
        xjllbDO.setSjcsdq("TZ_USER");
    }

    private void setDefaultValue(ZnsbCwbbQykjzzybqyZcfzbyzxDO znsbcwbbqykjzzybqyzcfzbyzxdo){
        znsbcwbbqykjzzybqyzcfzbyzxdo.setUuid(GyUtils.getUuid());
        znsbcwbbqykjzzybqyzcfzbyzxdo.setLrrq(new Date());
        znsbcwbbqykjzzybqyzcfzbyzxdo.setXgrq(new Date());
        znsbcwbbqykjzzybqyzcfzbyzxdo.setYwqdDm("SYS");
        znsbcwbbqykjzzybqyzcfzbyzxdo.setSjtbSj(new Date());
        znsbcwbbqykjzzybqyzcfzbyzxdo.setSjgsdq("TZ_USER");
        znsbcwbbqykjzzybqyzcfzbyzxdo.setSjcsdq("TZ_USER");
    }

    @Override
    public String testZcfzbxx(CwbbReqDTO cwbbreqdto) {
        String url = "http://10.2.8.46:50000/RESTAdapter/SFP009/RevenueCriteria_BalanceSheet";
        String result = this.getResult(url,cwbbreqdto);
        if(GyUtils.isNotNull(result)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx  result={}" , result);
            CwbbResDTO resDTO = JsonUtils.toBean(result, CwbbResDTO.class);
        }
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx  end");

        return result;
    }

    @Override
    public String testLrbxx(CwbbReqDTO cwbbreqdto) {
        String url = "http://10.2.8.46:50000/RESTAdapter/SFP010/CriteriaIncomeSheet";
        String result = this.getResult(url,cwbbreqdto);
        if(GyUtils.isNotNull(result)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx  result={}" , result);
            CwbbResDTO resDTO = JsonUtils.toBean(result, CwbbResDTO.class);
        }
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx  end");
        return result;
    }

    @Override
    public String testXjlbxx(CwbbReqDTO cwbbreqdto) {
        String url = "http://10.2.8.46:50000/RESTAdapter/SFP011/CashFlowStatement";
        String result = this.getResult(url,cwbbreqdto);
        if(GyUtils.isNotNull(result)){
            log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx  result={}" , result);
            CwbbResDTO resDTO = JsonUtils.toBean(result, CwbbResDTO.class);
        }
        log.info("ZnsbTzzxDlzgDataSynServiceImp_getZcfzbxx  end");
        return result;
    }
}
