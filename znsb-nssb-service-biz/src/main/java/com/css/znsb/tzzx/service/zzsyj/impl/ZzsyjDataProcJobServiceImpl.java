package com.css.znsb.tzzx.service.zzsyj.impl;

import cn.hutool.core.bean.BeanUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.gjss.feign.jyss.JyssApi;
import com.css.znsb.gjss.pojo.vo.yp.Qybq;
import com.css.znsb.gjss.pojo.vo.yp.SbzbRequest;
import com.css.znsb.gjss.pojo.vo.yp.SbzbResponse;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.DjxhReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.NsrzgxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.mhzc.pojo.yhxx.RyxxVO;
import com.css.znsb.nssb.constants.SfEnum;
import com.css.znsb.nssb.mapper.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjbMapper;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.domain.cchxwssb.fcscztdsys.ZnsbCxsFyxxcjb;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.pojo.vo.zzsyjsb.lq.queryJyss.SsSbytZzsYjsbZbVO;
import com.css.znsb.nssb.pojo.vo.zzsyjsb.lq.queryJyss.ZzsyjJyssytReqVO;
import com.css.znsb.nssb.pojo.vo.zzsyjsb.lq.queryJyss.ZzsyjJyssytResVO;
import com.css.znsb.nssb.service.zzsyjsb.ZzsyjsbLqService;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.nssb.utils.CchxwsnssbGyUtils;
import com.css.znsb.nssb.utils.FtsCxsUtils;
import com.css.znsb.tzzx.constants.FcsTzConstans;
import com.css.znsb.tzzx.controller.fcs.FcsUtil;
import com.css.znsb.tzzx.mapper.ZnsbTzzxFcjcxxtzbMapper;
import com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxHttzxxMapper;
import com.css.znsb.tzzx.mapper.fcstz.ZnsbTzzxHttzxxMxMapper;
import com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxCzbdcyjmxMapper;
import com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxZzsyjjhMapper;
import com.css.znsb.tzzx.mapper.zzsyj.ZnsbTzzxZzsyjmxMapper;
import com.css.znsb.tzzx.pojo.domain.tzzx.fcs.ZnsbTzzxHttzxxDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.fcs.ZnsbTzzxHttzxxMxDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.zzsyj.ZnsbTzzxCzbdcyjmxDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.zzsyj.ZnsbTzzxZzsyjjhDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.zzsyj.ZnsbTzzxZzsyjmxDO;
import com.css.znsb.tzzx.pojo.vo.zzsyj.ZnsbTzzxZzsyjmxReqVO;
import com.css.znsb.tzzx.pojo.vo.zzsyj.ZzsyjjhReqVO;
import com.css.znsb.tzzx.service.zzsyj.ZzsyjDataProcJobService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.css.znsb.nssb.constants.SbrwztConstants.*;

/**
* <AUTHOR>
* @createDate 2024-08-23 16:27:18
*/
@Slf4j
@Service
public class ZzsyjDataProcJobServiceImpl implements ZzsyjDataProcJobService {


    /**
     * 房产基础信息台账表
     * 针对表【znsb_tzzx_fcjcxxtzb】的数据库操作Mapper
     **/
    @Resource
    private ZnsbTzzxFcjcxxtzbMapper znsbTzzxFcjcxxtzbMapper;


    @Resource
    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;


    /**
     * @description 针对表【znsb_tzzx_httzxx(合同台账信息)】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxHttzxxMapper znsbtzzxhttzxxmapper;

    /**
     * @description 针对表【znsb_tzzx_httzxxmx(合同台账明细信息)】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxHttzxxMxMapper znsbtzzxhttzxxmxmapper;

    /**
     * 纳税人信息查询api
     */
    @Resource
    private NsrxxApi nsrxxApi;

    /**
     * 交易算税api
     */
    @Resource
    private JyssApi jyssApi;

    /**
     * @description 针对表【znsb_tzzx_zzsyjmx(增值税预缴明细表)】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxZzsyjmxMapper znsbtzzxzzsyjmxmapper;

    /**
     * @description 针对表【znsb_tzzx_czbdcyjmx(出租不动产预缴明细表)】的数据库操作Mapper
     */
    @Resource
    private ZnsbTzzxCzbdcyjmxMapper znsbtzzxczbdcyjmxmapper;

    @Resource
    private ZzsyjsbLqService zzsyjsbLqService;

    @Resource
    private CompanyApi companyApi;

    @Resource
    private ZnsbTzzxZzsyjjhMapper zzsyjjhMapper;

    @Resource
    private ZnsbCxsFyxxcjbMapper znsbcxsfyxxcjbmapper;

    @Override
    @Transactional
    public void zzsyjDataProc(String tbfybh,String tbsbny) {
        log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc tbfybh = {}",tbfybh);
        log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc tbsbny = {}",tbsbny);
        String sbny = DateUtil.doDateFormat(new Date(),"yyyyMM");
        String fybh = tbfybh;
        String jobParam = XxlJobHelper.getJobParam();
        if(GyUtils.isNotNull(tbsbny)){
            sbny= tbsbny;
        }
        if(GyUtils.isNotNull(jobParam)){
            String[] param = jobParam.split(",");
            if(param.length == 1){
                fybh = param[0];
            }else if(param.length == 2){
                if(GyUtils.isNotNull(param[0])){
                    fybh = param[0];
                }
                if(GyUtils.isNotNull(param[1])){
                    sbny = param[1];
                }
            }
        }
        Date sbnyDate = DateUtil.toDate("yyyyMM",sbny);
        Date skssqq = this.getSkssqq(DateUtil.doDateFormat(sbnyDate,"yyyy-MM-dd"));
        Date skssqz = this.getSkssqz(DateUtil.doDateFormat(sbnyDate,"yyyy-MM-dd"));

        //新增出租不动产预缴明细List
        List<ZnsbTzzxCzbdcyjmxDO> insertCzbdcyjmxdoList = new ArrayList<>();
        //修改出租不动产预缴明细List
        List<ZnsbTzzxCzbdcyjmxDO> updateCzbdcyjmxdoList = new ArrayList<>();

        //新增增值税预缴预缴明细List
        List<ZnsbTzzxZzsyjmxDO> insertZnsbTzzxZzsyjmxDOList = new ArrayList<>();
        //新增增值税预缴预缴明细List
        List<ZnsbTzzxZzsyjmxDO> updateZnsbTzzxZzsyjmxDOList = new ArrayList<>();
        //新增申报任务List
        List<ZnsbNssbSbrwDO> insertZnsbnssbsbrwdoList = new ArrayList<>();


        //获取预缴计划，如果没有预缴计划则不生成
        ZzsyjjhReqVO reqVO = new ZzsyjjhReqVO();
        reqVO.setFybh(fybh);
        reqVO.setSbny(sbny);
        List<ZnsbTzzxZzsyjjhDO> yjjhList = zzsyjjhMapper.selectYjjhList(reqVO);
        log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc yjjhList=={}",JsonUtils.toJson(yjjhList));
        if(GyUtils.isNull(yjjhList)){
            return;
        }

        //根据房源编号分组
        Map<String,List<ZnsbTzzxZzsyjjhDO>> yjjhFzMap = new HashMap<>();
        for(ZnsbTzzxZzsyjjhDO yjjhDo:yjjhList){
            String key = yjjhDo.getFybh();
            List<ZnsbTzzxZzsyjjhDO> fzYjjhList = yjjhFzMap.get(key);
            if(GyUtils.isNull(fzYjjhList)){
                fzYjjhList = new ArrayList<>();
                fzYjjhList.add(yjjhDo);
                yjjhFzMap.put(key,fzYjjhList);
            }else {
                fzYjjhList.add(yjjhDo);
            }
        }

        //根据预缴计划生成增值税预缴数据
        Set<String> keySet = yjjhFzMap.keySet();
        for(String key:keySet){
            List<ZnsbTzzxZzsyjjhDO> fzYjjhList = yjjhFzMap.get(key);
            ZnsbTzzxZzsyjmxDO zzsyjmxDo = znsbtzzxzzsyjmxmapper.selectOneByFybhAndSsq(key,skssqq,skssqz);
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc key=={}",key);
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc zzsyjmxDo=={}",JsonUtils.toJson(zzsyjmxDo));
            List<ZnsbTzzxHttzxxDO> htxxDoList = znsbtzzxhttzxxmapper.selectHtxxListByHtbh(fzYjjhList.get(0).getHtbh(),fzYjjhList.get(0).getFybh());
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc htxxDoList=={}",JsonUtils.toJson(htxxDoList));
            if(GyUtils.isNull(htxxDoList)){
                continue;
            }
            ZnsbTzzxHttzxxDO htdo = htxxDoList.get(0);
            if(GyUtils.isNull(zzsyjmxDo)){
                zzsyjmxDo = new ZnsbTzzxZzsyjmxDO();
                BeanUtil.copyProperties(htdo,zzsyjmxDo,true);
                this.setDefaultValue(zzsyjmxDo);
                double hsje = 0.0;
                for(ZnsbTzzxZzsyjjhDO yjjh:fzYjjhList){
                    this.getCzbdcyjmx(yjjh,insertCzbdcyjmxdoList,updateCzbdcyjmxdoList);
                    hsje += yjjh.getSbje().doubleValue();
                }
                zzsyjmxDo.setYjlx("101016600");
                zzsyjmxDo.setHsje(new BigDecimal(hsje).setScale(2,BigDecimal.ROUND_HALF_UP));
                zzsyjmxDo.setNsrsbztDm(SBZT_WSB_DM);
                zzsyjmxDo.setSkssqq(this.getSkssqq(jobParam));
                zzsyjmxDo.setSkssqz(this.getSkssqz(jobParam));
//                //生成申报任务
                ZnsbNssbSbrwDO znsbnssbsbrwdo = this.getZnsbNssbSbrwDO(zzsyjmxDo);
                log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc znsbnssbsbrwdo=={}",JsonUtils.toJson(znsbnssbsbrwdo));
                try{
                    //投递交易算税
                    SbzbResponse sbzbresponse = this.sszb(zzsyjmxDo);
                    String code = sbzbresponse.getCode();
                    if(SbzbResponse.RESPONSE_CODE_SUCCESS.equals(code)){
                        insertZnsbTzzxZzsyjmxDOList.add(zzsyjmxDo);
                        insertZnsbnssbsbrwdoList.add(znsbnssbsbrwdo);
                    }
                }catch (Exception e){
                    log.error("调用交易算税接口请求失败！",e);
                }
            }else {
                double hsje = 0.0;
                for(ZnsbTzzxZzsyjjhDO yjjh:fzYjjhList){
                    this.getCzbdcyjmx(yjjh,insertCzbdcyjmxdoList,updateCzbdcyjmxdoList);
                    hsje += yjjh.getSbje().doubleValue();
                }
                BigDecimal sbhsje = new BigDecimal(hsje).setScale(2,BigDecimal.ROUND_HALF_UP);
                log.info("zzsyjDataProc zzsyjmxFzMap sbhsje{}",sbhsje);
                log.info("zzsyjDataProc zzsyjmxFzMap zzsyjmxDo.getHsje{}",zzsyjmxDo.getHsje());
                if(zzsyjmxDo.getHsje().compareTo(sbhsje) != 0){
                    try{
                        ZzsyjJyssytReqVO zzsyjjyssytreqvo = new ZzsyjJyssytReqVO();
                        zzsyjjyssytreqvo.setDjxh(zzsyjmxDo.getDjxh().toString());
                        zzsyjjyssytreqvo.setFybh(zzsyjmxDo.getFybh());
                        zzsyjjyssytreqvo.setSkssqq(DateUtil.doDateFormat(zzsyjmxDo.getSkssqq(),"yyyy-MM-dd"));
                        zzsyjjyssytreqvo.setSkssqz(DateUtil.doDateFormat(zzsyjmxDo.getSkssqz(),"yyyy-MM-dd"));
                        zzsyjjyssytreqvo.setZspmDm("101016600");
                        log.info("ZzsyjDataProcJobServiceImpl_sszb调用交易算税接口请求req：" + JsonUtils.toJson(zzsyjjyssytreqvo));
                        CommonResult<ZzsyjJyssytResVO> jyssxx = zzsyjsbLqService.queryJyssxx(zzsyjjyssytreqvo);
                        log.info("ZzsyjDataProcJobServiceImpl_sszb调用交易算税接口请求res：" + JsonUtils.toJson(jyssxx));
                        List<SsSbytZzsYjsbZbVO> yjsbmxzzslist = jyssxx.getData().getYjsbmxzzslist();
                        //20241209 由于交易算税存在传一个ZspmDm 其他ZspmDm都返回且是0 只能自己过滤
                        SsSbytZzsYjsbZbVO ssvo = new SsSbytZzsYjsbZbVO();
                        for(SsSbytZzsYjsbZbVO resVo:yjsbmxzzslist){
                            if("101016600".equals(resVo.getZspmDm())){
                                ssvo = resVo;
                                break;
                            }
                        }
                        ZnsbTzzxZzsyjmxDO znsbtzzxzzsyjmxdoHc = new ZnsbTzzxZzsyjmxDO();
                        BeanUtil.copyProperties(zzsyjmxDo,znsbtzzxzzsyjmxdoHc);
                        double hsjeD = 0.0 - ssvo.getXse().doubleValue();
                        znsbtzzxzzsyjmxdoHc.setHsje(new BigDecimal(hsjeD).setScale(2,BigDecimal.ROUND_HALF_UP));
                        SbzbResponse sbzbresponseHc = this.sszb(znsbtzzxzzsyjmxdoHc);
                        String codeHc = sbzbresponseHc.getCode();
                        if(SbzbResponse.RESPONSE_CODE_SUCCESS.equals(codeHc)){
                            zzsyjmxDo.setHsje(sbhsje);
                            zzsyjmxDo.setXgrsfid("TZ_USER");
                            zzsyjmxDo.setXgrq(new Date());
                            SbzbResponse sbzbresponse = this.sszb(zzsyjmxDo);
                            String code = sbzbresponse.getCode();
                            if(SbzbResponse.RESPONSE_CODE_SUCCESS.equals(code)){
                                updateZnsbTzzxZzsyjmxDOList.add(zzsyjmxDo);
                            }
                        }
                    }catch (Exception e){
                        log.error("调用交易算税接口请求失败！",e);
                    }
                }
            }
        }

        //插入申报任务表
        if(GyUtils.isNotNull(insertZnsbnssbsbrwdoList)){
            znsbNssbSbrwMapper.insertBatch(insertZnsbnssbsbrwdoList);
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc insertZnsbnssbsbrwdoList=={}",JsonUtils.toJson(insertZnsbnssbsbrwdoList));
        }

        //插入增值税预缴明细表
        if(GyUtils.isNotNull(insertZnsbTzzxZzsyjmxDOList)){
            znsbtzzxzzsyjmxmapper.insertBatch(insertZnsbTzzxZzsyjmxDOList);
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc insertZnsbTzzxZzsyjmxDOList=={}",JsonUtils.toJson(insertZnsbTzzxZzsyjmxDOList));
        }

        //修改增值税预缴明细表
        if(GyUtils.isNotNull(updateZnsbTzzxZzsyjmxDOList)){
            znsbtzzxzzsyjmxmapper.updateBatch(updateZnsbTzzxZzsyjmxDOList);
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc updateZnsbTzzxZzsyjmxDOList=={}",JsonUtils.toJson(updateZnsbTzzxZzsyjmxDOList));
        }

        //插入出租不动产明细表
        if(GyUtils.isNotNull(insertCzbdcyjmxdoList)){
            znsbtzzxczbdcyjmxmapper.insertBatch(insertCzbdcyjmxdoList);
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc insertCzbdcyjmxdoList=={}",JsonUtils.toJson(insertCzbdcyjmxdoList));
        }

        //修改出租不动产明细表
        if(GyUtils.isNotNull(updateCzbdcyjmxdoList)){
            znsbtzzxczbdcyjmxmapper.updateBatch(updateCzbdcyjmxdoList);
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjDataProc updateCzbdcyjmxdoList=={}",JsonUtils.toJson(updateCzbdcyjmxdoList));
        }
//            ZnsbTzzxZzsyjmxDO zzsyjmxDo = znsbtzzxzzsyjmxmapper.selectOneByFybhAndSsq(key,skssqq,skssqz);
//            if(GyUtils.isNull(zzsyjmxDo)){
//                zzsyjmxDo = new ZnsbTzzxZzsyjmxDO();
//                BeanUtil.copyProperties(htdo,zzsyjmxDo,true);
//                this.setDefaultValue(znsbtzzxzzsyjmxdo);
//                znsbtzzxzzsyjmxdo.setYjlx("101016600");
//                znsbtzzxzzsyjmxdo.setHsje(new BigDecimal(sbzje).setScale(2,BigDecimal.ROUND_HALF_UP));
//                znsbtzzxzzsyjmxdo.setNsrsbztDm(SBZT_WSB_DM);
//                znsbtzzxzzsyjmxdo.setSkssqq(this.getSkssqq(jobParam));
//                znsbtzzxzzsyjmxdo.setSkssqz(this.getSkssqz(jobParam));
//                zzsyjmxFzMap.put(key,znsbtzzxzzsyjmxdo);
//
//                log.info("zzsyjDataProc zzsyjmxFzMap if");
//                ZnsbNssbSbrwDO znsbnssbsbrwdo = this.getZnsbNssbSbrwDO(znsbtzzxzzsyjmxdo);
//                try{
//                    SbzbResponse sbzbresponse = this.sszb(znsbtzzxzzsyjmxdo);
//                    String code = sbzbresponse.getCode();
//                    if(SbzbResponse.RESPONSE_CODE_SUCCESS.equals(code)){
//                        insertZnsbTzzxZzsyjmxDOList.add(znsbtzzxzzsyjmxdo);
//                        insertZnsbnssbsbrwdoList.add(znsbnssbsbrwdo);
//                    }
//                }catch (Exception e){
//                    log.error("调用交易算税接口请求失败！",e);
//                }
//            }else {
//                log.info("zzsyjDataProc zzsyjmxFzMap else");
//                if(zzsyjmxDo.getHsje().doubleValue() != znsbtzzxzzsyjmxdo.getHsje().doubleValue()){
//                    log.info("zzsyjDataProc zzsyjmxFzMap else Hsje");
//                    try{
//                        ZzsyjJyssytReqVO zzsyjjyssytreqvo = new ZzsyjJyssytReqVO();
//                        zzsyjjyssytreqvo.setDjxh(znsbtzzxzzsyjmxdo.getDjxh().toString());
//                        zzsyjjyssytreqvo.setFybh(znsbtzzxzzsyjmxdo.getFybh());
//                        zzsyjjyssytreqvo.setSkssqq(DateUtil.doDateFormat(znsbtzzxzzsyjmxdo.getSkssqq(),"yyyy-MM-dd"));
//                        zzsyjjyssytreqvo.setSkssqz(DateUtil.doDateFormat(znsbtzzxzzsyjmxdo.getSkssqz(),"yyyy-MM-dd"));
//                        zzsyjjyssytreqvo.setZspmDm("101016600");
//                        log.info("ZzsyjDataProcJobServiceImpl_sszb调用交易算税接口请求req：" + JsonUtils.toJson(zzsyjjyssytreqvo));
//                        CommonResult<ZzsyjJyssytResVO> jyssxx = zzsyjsbLqService.queryJyssxx(zzsyjjyssytreqvo);
//                        log.info("ZzsyjDataProcJobServiceImpl_sszb调用交易算税接口请求res：" + JsonUtils.toJson(jyssxx));
//                        List<SsSbytZzsYjsbZbVO> yjsbmxzzslist = jyssxx.getData().getYjsbmxzzslist();
//                        //20241209 由于交易算税存在传一个ZspmDm 其他ZspmDm都返回且是0 只能自己过滤
//                        SsSbytZzsYjsbZbVO ssvo = new SsSbytZzsYjsbZbVO();
//                        for(SsSbytZzsYjsbZbVO resVo:yjsbmxzzslist){
//                            if("101016600".equals(resVo.getZspmDm())){
//                                ssvo = resVo;
//                                break;
//                            }
//                        }
//                        ZnsbTzzxZzsyjmxDO znsbtzzxzzsyjmxdoHc = new ZnsbTzzxZzsyjmxDO();
//                        BeanUtil.copyProperties(znsbtzzxzzsyjmxdo,znsbtzzxzzsyjmxdoHc);
//                        double hsjeD = 0.0 - ssvo.getXse().doubleValue();
//                        znsbtzzxzzsyjmxdoHc.setHsje(new BigDecimal(hsjeD).setScale(2,BigDecimal.ROUND_HALF_UP));
//                        SbzbResponse sbzbresponseHc = this.sszb(znsbtzzxzzsyjmxdoHc);
//                        String codeHc = sbzbresponseHc.getCode();
//                        if(SbzbResponse.RESPONSE_CODE_SUCCESS.equals(codeHc)){
//                            zzsyjmxDo.setHsje(znsbtzzxzzsyjmxdo.getHsje());
//                            zzsyjmxDo.setXgrsfid("TZ_USER");
//                            zzsyjmxDo.setXgrq(new Date());
//                            SbzbResponse sbzbresponse = this.sszb(znsbtzzxzzsyjmxdo);
//                            String code = sbzbresponse.getCode();
//                            if(SbzbResponse.RESPONSE_CODE_SUCCESS.equals(code)){
//                                updateZnsbTzzxZzsyjmxDOList.add(zzsyjmxDo);
//                            }
//                        }
//                    }catch (Exception e){
//                        log.error("调用交易算税接口请求失败！",e);
//                    }
//                }
//            }
    }

    @Override
    public void zzsyjjhsc(String htbh,String fybh) {
        List<ZnsbTzzxZzsyjjhDO> yjjhList = new ArrayList<>();
        log.info("ZzsyjDataProcJobServiceImpl_zzsyjjhlssjcl  htbh == {}",htbh);
        log.info("ZzsyjDataProcJobServiceImpl_zzsyjjhlssjcl  fybh == {}",fybh);
        List<ZnsbTzzxHttzxxDO> htxxList = znsbtzzxhttzxxmapper.selectHtxxListByHtbh(htbh,fybh);
        for(ZnsbTzzxHttzxxDO htxxDo:htxxList){
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjjhlssjcl htxxDo  djxh == {}",htxxDo.getDjxh());
            log.info("ZzsyjDataProcJobServiceImpl_zzsyjjhlssjcl htxxDo  htbh == {}",htbh);
            ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
            znsbMhzcQyjbxxmxReqVO.setDjxh(String.valueOf(htxxDo.getDjxh()));
            CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxx = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);
            if(GyUtils.isNotNull(nsrxx.getData())){
                ZnsbMhzcQyjbxxmxResVO znsbmhzcqyjbxxmxresvo = nsrxx.getData();
                List<JbxxmxsjVO> jbxxmxsjVOList = znsbmhzcqyjbxxmxresvo.getJbxxmxsj();
                JbxxmxsjVO jbxxmxsjvo = jbxxmxsjVOList.get(0);
                log.info("ZzsyjDataProcJobServiceImpl_zzsyjjhlssjcl Kqccsztdjbz = {}",jbxxmxsjvo.getKqccsztdjbz());
                if("Y".equals(jbxxmxsjvo.getKqccsztdjbz())){
                    ZzsyjjhReqVO reqVO = new ZzsyjjhReqVO();
                    reqVO.setHtbh(htxxDo.getHtbh());
                    reqVO.setFybh(htxxDo.getFybh());
                    List<ZnsbTzzxZzsyjjhDO> zzsyjjhList = zzsyjjhMapper.selectYjjhList(reqVO);
                    if(GyUtils.isNull(zzsyjjhList)){
                        List<ZnsbTzzxHttzxxMxDO> htmxListResult = znsbtzzxhttzxxmxmapper.selectAllHtmxdoByZbUuid(htxxDo.getUuid());
                        yjjhList.addAll(this.getZzsYjjh(htxxDo,htmxListResult));
                    }
                }
            }
        }

        if(GyUtils.isNotNull(yjjhList)){
            zzsyjjhMapper.insertBatch(yjjhList);
        }
    }


    private void getCzbdcyjmx(ZnsbTzzxZzsyjjhDO yjjhDo,List<ZnsbTzzxCzbdcyjmxDO> insertCzbdcyjmxdoList,List<ZnsbTzzxCzbdcyjmxDO> updateCzbdcyjmxdoList){
        log.info("ZzsyjDataProcJobServiceImpl_getCzbdcyjmx yjjhDo=={}",yjjhDo);
        log.info("ZzsyjDataProcJobServiceImpl_getCzbdcyjmx insertCzbdcyjmxdoList=={}",insertCzbdcyjmxdoList);
        log.info("ZzsyjDataProcJobServiceImpl_getCzbdcyjmx updateCzbdcyjmxdoList=={}",updateCzbdcyjmxdoList);
        ZnsbTzzxCzbdcyjmxDO czbdcyjmxDO = null;
        List<ZnsbTzzxHttzxxDO> htxxDoList = znsbtzzxhttzxxmapper.selectHtxxListByHtbh(yjjhDo.getHtbh(),yjjhDo.getFybh());
        if(GyUtils.isNull(htxxDoList)){
            return;
        }
        ZnsbTzzxHttzxxDO htxxDo = htxxDoList.get(0);
        //生成出租不动产预缴明细表数据
        czbdcyjmxDO = znsbtzzxczbdcyjmxmapper.selectOneByHtbhAndFybh(htxxDo.getHtbh(),htxxDo.getFybh());
        if(GyUtils.isNull(czbdcyjmxDO)){
            czbdcyjmxDO = new ZnsbTzzxCzbdcyjmxDO();
            BeanUtil.copyProperties(htxxDo,czbdcyjmxDO,true);
            this.setDefaultValue(czbdcyjmxDO);
            insertCzbdcyjmxdoList.add(czbdcyjmxDO);
        }else {
            czbdcyjmxDO.setXgrsfid("TZ_USER");
            czbdcyjmxDO.setXgrq(new Date());
            updateCzbdcyjmxdoList.add(czbdcyjmxDO);
        }
        czbdcyjmxDO.setHsyzjsr(new BigDecimal(yjjhDo.getSbje().doubleValue()/yjjhDo.getSbys()).setScale(2,BigDecimal.ROUND_HALF_UP));
        czbdcyjmxDO.setYzjsr(new BigDecimal(yjjhDo.getSjyzjsr().doubleValue()/yjjhDo.getSbys()).setScale(2,BigDecimal.ROUND_HALF_UP));
        czbdcyjmxDO.setSbje(yjjhDo.getSbje());
        czbdcyjmxDO.setSkssqq(this.getSkssqq(DateUtil.doDateFormat(yjjhDo.getZlrqq(),"yyyy-MM-dd")));
        czbdcyjmxDO.setSkssqz(this.getSkssqz(DateUtil.doDateFormat(yjjhDo.getZlrqq(),"yyyy-MM-dd")));
        czbdcyjmxDO.setZlrqq(yjjhDo.getZlrqq());
        czbdcyjmxDO.setZlrqz(yjjhDo.getZlrqz());
        log.info("ZzsyjDataProcJobServiceImpl_getCzbdcyjmx insertCzbdcyjmxdoList11=={}",insertCzbdcyjmxdoList);
        log.info("ZzsyjDataProcJobServiceImpl_getCzbdcyjmx updateCzbdcyjmxdoList11=={}",updateCzbdcyjmxdoList);
    }

    @Override
    public SbzbResponse testSbzbjk(ZnsbTzzxZzsyjmxReqVO znsbtzzxzzsyjmxreqvo) {
        ZnsbTzzxZzsyjmxDO znsbtzzxzzsyjmxdo = znsbtzzxzzsyjmxmapper.selectById(znsbtzzxzzsyjmxreqvo.getUuid());
        SbzbResponse sbzbresponse = this.sszb(znsbtzzxzzsyjmxdo);
        return sbzbresponse;
    }

    private SbzbResponse sszb(ZnsbTzzxZzsyjmxDO znsbtzzxzzsyjmxdo){
        //装填算税接口基础参数
        SbzbRequest sbzbRequest = new SbzbRequest();
        //装填红冲算税接口基础参数
        SbzbRequest sbzbRequestHc = new SbzbRequest();
        Qybq qybq = this.getQybq(znsbtzzxzzsyjmxdo);
        sbzbRequest.setQybq(qybq);
        sbzbRequest.setDjxh(String.valueOf(znsbtzzxzzsyjmxdo.getDjxh()));
        sbzbRequest.setSspzhm(znsbtzzxzzsyjmxdo.getUuid());
        sbzbRequest.setSsywflDm("0402");//0520 补录-印花税-合同采集
        sbzbRequest.setYwfsrq(DateUtils.getSystemCurrentTime(3));
        sbzbRequest.setSkssqq(DateUtil.doDateFormat(znsbtzzxzzsyjmxdo.getSkssqq(),"yyyy-MM-dd"));
        sbzbRequest.setSkssqz(DateUtil.doDateFormat(znsbtzzxzzsyjmxdo.getSkssqz(),"yyyy-MM-dd"));
        sbzbRequest.setXwgzlxDm("0");
        sbzbRequest.setZsxmDm("10101");
        Map<String,String> blnrMap = new HashMap<>();
        blnrMap.put("djxh",String.valueOf(znsbtzzxzzsyjmxdo.getDjxh()));
        blnrMap.put("zspmdm","101016600");

        blnrMap.put("spbm","3");
        double sl = znsbtzzxzzsyjmxdo.getSl1().doubleValue();
        if(sl>0.05){
            blnrMap.put("jsffdm","1");
        }else {
            blnrMap.put("jsffdm","2");
        }
        blnrMap.put("sl",String.valueOf(sl));
        blnrMap.put("xse",String.valueOf(znsbtzzxzzsyjmxdo.getHsje().setScale(2,BigDecimal.ROUND_HALF_UP)));
        blnrMap.put("fybh",znsbtzzxzzsyjmxdo.getFybh());
        List<Map<String,String>> blnrList = new ArrayList<>();
        blnrList.add(blnrMap);
        sbzbRequest.setBlnr(JsonUtils.toJson(blnrList));

        BeanUtil.copyProperties(sbzbRequest,sbzbRequestHc);
//        //调用交易算税接口
//        log.info("ZzsyjDataProcJobServiceImpl_sszb调用交易算税接口请求红冲：" + JsonUtils.toJson(sbzbRequest));
//        final CommonResult<SbzbResponse> resultHc = jyssApi.sbzb(sbzbRequestHc);
//        log.info("ZzsyjDataProcJobServiceImpl_sszb调用交易算税接口响应结果红冲：" + JsonUtils.toJson(resultHc));
        log.info("ZzsyjDataProcJobServiceImpl_sszb调用交易算税接口请求：" + JsonUtils.toJson(sbzbRequest));
        final CommonResult<SbzbResponse> result = jyssApi.sbzb(sbzbRequest);
        log.info("ZzsyjDataProcJobServiceImpl_sszb调用交易算税接口响应结果：" + JsonUtils.toJson(result));
        return result.getData();
    }


    private Qybq getQybq(ZnsbTzzxZzsyjmxDO znsbtzzxzzsyjmxdo){
        Qybq qybq = new Qybq();
        //拼装纳税人信息查询参数
        ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setDjxh(String.valueOf(znsbtzzxzzsyjmxdo.getDjxh()));
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(znsbtzzxzzsyjmxdo.getNsrsbh());
        znsbMhzcQyjbxxmxReqVO.setFhNsrzgxx(true);
        znsbMhzcQyjbxxmxReqVO.setFhNsrsfzrdxx(false);

        //获取纳税人资格类型代码
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxByNsrsbh = nsrxxApi.getNsrxxByNsrsbh(znsbMhzcQyjbxxmxReqVO);
        List<NsrzgxxVO> nsrzgxx = nsrxxByNsrsbh.getData().getNsrzgxx();
        String nsrlx = "";
        if (!GyUtils.isNull(nsrzgxx)){
            nsrlx = nsrzgxx.stream().filter(nsrzgxxVO -> {
                Date date = new Date();
                return date.before(nsrzgxxVO.getYxqz()) && date.after(nsrzgxxVO.getYxqq());
            }).map(NsrzgxxVO::getNsrlx).findFirst().orElse("");
        }
        if (Arrays.asList("1","3","4").contains(nsrlx)){
            qybq.setYbnsrbz("Y");
        }else {
            qybq.setYbnsrbz("N");
        }
        qybq.setQydSsYwxwfl("XW001");
        return qybq;
    }

    private void setDefaultValue(ZnsbTzzxCzbdcyjmxDO znsbtzzxczbdcyjmxdo) {
        znsbtzzxczbdcyjmxdo.setUuid(GyUtils.getUuid());
        znsbtzzxczbdcyjmxdo.setLrrq(new Date());
        znsbtzzxczbdcyjmxdo.setXgrq(new Date());
        znsbtzzxczbdcyjmxdo.setYwqdDm("TZ_USER");
        znsbtzzxczbdcyjmxdo.setScbz("N");
        znsbtzzxczbdcyjmxdo.setSjtbSj(new Date());
        znsbtzzxczbdcyjmxdo.setSjgsdq("TZ_USER");
        znsbtzzxczbdcyjmxdo.setSjcsdq("TZ_USER");
        znsbtzzxczbdcyjmxdo.setLy(FcsTzConstans.LY_DSRW);
    }

    private void setDefaultValue(ZnsbTzzxZzsyjmxDO znsbtzzxzzsyjmxdo) {
        znsbtzzxzzsyjmxdo.setUuid(GyUtils.getUuid());
        znsbtzzxzzsyjmxdo.setLrrq(new Date());
        znsbtzzxzzsyjmxdo.setXgrq(new Date());
        znsbtzzxzzsyjmxdo.setYwqdDm("TZ_USER");
        znsbtzzxzzsyjmxdo.setScbz("N");
        znsbtzzxzzsyjmxdo.setSjtbSj(new Date());
        znsbtzzxzzsyjmxdo.setSjgsdq("TZ_USER");
        znsbtzzxzzsyjmxdo.setSjcsdq("TZ_USER");
        znsbtzzxzzsyjmxdo.setLy(FcsTzConstans.LY_DSRW);
    }

    private List<ZnsbTzzxZzsyjjhDO> getZzsYjjh(ZnsbTzzxHttzxxDO htxxDo,List<ZnsbTzzxHttzxxMxDO> htmxResult){
        log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh htxxDo = {}",JsonUtils.toJson(htxxDo));
        log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh htmxResult = {}",JsonUtils.toJson(htmxResult));
        List<ZnsbTzzxZzsyjjhDO> yjjhList = new ArrayList<>();
        Date rqq = htxxDo.getHtydsxrq();
        Date rqz = htxxDo.getHtydzzrq();
        if(GyUtils.isNotNull(htxxDo.getZzrq())){
            rqz = htxxDo.getZzrq();
        }
        //根据合同起止日期获取合同间隔月数
        int jgyf = FcsUtil.getJgyf(DateUtil.doDateFormat(rqq,"yyyy-MM-dd"),DateUtil.doDateFormat(rqz,"yyyy-MM-dd")) + 1;
        log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh rqq = {}",DateUtil.doDateFormat(rqq,"yyyy-MM-dd"));
        log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh rqz = {}",DateUtil.doDateFormat(rqz,"yyyy-MM-dd"));
        log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh jgyf = {}",jgyf);

        String skfs = htxxDo.getSkfs();
        if(GyUtils.isNull(skfs)){
            return yjjhList;
        }

        //根据收款方式获取申报间隔月数
        int sbyf = FcsUtil.getgjyf(skfs);
        log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh sbyf = {}",sbyf);
        //获取申报的周期数
        int sbzq = (int)Math.ceil((double)jgyf/sbyf);
        log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh sbzq = {}",sbzq);
        for(int i = 0;i<sbzq;i++){
            //租赁日期起
            Calendar zlrqq = Calendar.getInstance();
            zlrqq.setTime(rqq);
            zlrqq.add(Calendar.MONTH,sbyf*i);
            //租赁日期止
            Calendar zlrqz = Calendar.getInstance();
            zlrqz.setTime(rqq);
            zlrqz.add(Calendar.MONTH,(sbyf*(i+1))-1);
            zlrqz.set(Calendar.DAY_OF_MONTH,zlrqz.getActualMaximum(Calendar.DAY_OF_MONTH));
            String zlrqqs = DateUtil.doDateFormat(zlrqq.getTime(),"yyyy-MM-dd");
            String zlrqzs = DateUtil.doDateFormat(zlrqz.getTime(),"yyyy-MM-dd");

            log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh zlrqq = {}",zlrqqs);
            log.info("ZzsyjDataProcJobServiceImpl_getZzsYjjh zlrqz = {}",zlrqzs);

            ZnsbTzzxZzsyjjhDO yjjhDo = new ZnsbTzzxZzsyjjhDO();
            yjjhDo.setDefaultValue();
            yjjhDo.setHtbh(htxxDo.getHtbh());
            yjjhDo.setFybh(htxxDo.getFybh());
            yjjhDo.setSbny(DateUtil.doDateFormat(zlrqq.getTime(),"yyyyMM"));
            yjjhDo.setZlrqq(zlrqq.getTime());
            yjjhDo.setZlrqz(zlrqz.getTime());
            yjjhDo.setSbys(sbyf);
            double sbje = 0.0;
            double sjyzjsr = 0.0;
            for(ZnsbTzzxHttzxxMxDO httzxxMxDO:htmxResult){
                if(httzxxMxDO.getZlrqq().compareTo(zlrqq.getTime())>=0 && httzxxMxDO.getZlrqq().before(zlrqz.getTime())){
                    sbje+=httzxxMxDO.getSjhsyzjsr().doubleValue();
                    sjyzjsr+=httzxxMxDO.getSjyzjsr().doubleValue();
                }
            }
            if(i == (sbzq-1)){
                yjjhDo.setZlrqz(htmxResult.get(0).getZlrqz());
            }
            yjjhDo.setSbje(new BigDecimal(sbje).setScale(2,BigDecimal.ROUND_HALF_UP));
            yjjhDo.setSjyzjsr(new BigDecimal(sjyzjsr).setScale(2,BigDecimal.ROUND_HALF_UP));
            yjjhList.add(yjjhDo);
        }
        return yjjhList;
    }


    private ZnsbNssbSbrwDO getZnsbNssbSbrwDO(ZnsbTzzxZzsyjmxDO znsbtzzxzzsyjmxdo){

        ZnsbCxsFyxxcjb znsbcxsfyxxcjb = znsbcxsfyxxcjbmapper.queryByFybh(znsbtzzxzzsyjmxdo.getFybh(),null,false);
        log.info("zzsyjDataProc fybh=={}",znsbtzzxzzsyjmxdo.getFybh());
        ZnsbNssbSbrwDO znsbnssbsbrwdo = new ZnsbNssbSbrwDO();
        znsbnssbsbrwdo.setSbrwuuid(GyUtils.getUuid());
        znsbnssbsbrwdo.setDjxh(String.valueOf(znsbtzzxzzsyjmxdo.getDjxh()));
        znsbnssbsbrwdo.setNsrsbh(znsbtzzxzzsyjmxdo.getNsrsbh());
        znsbnssbsbrwdo.setNsrmc(znsbtzzxzzsyjmxdo.getNsrmc());
        znsbnssbsbrwdo.setSkssqq(znsbtzzxzzsyjmxdo.getSkssqq());
        znsbnssbsbrwdo.setSkssqz(znsbtzzxzzsyjmxdo.getSkssqz());
        znsbnssbsbrwdo.setFybh(znsbtzzxzzsyjmxdo.getFybh());
        znsbnssbsbrwdo.setNsrsbztDm(SBZT_WSB_DM);
        znsbnssbsbrwdo.setYwqdDm("lq-qyd");
        znsbnssbsbrwdo.setRwztDm(RWZT_WSB_DM);
        znsbnssbsbrwdo.setRwlxDm(RWLX_HLS_DM);
        znsbnssbsbrwdo.setNsqxDm("06");
        znsbnssbsbrwdo.setZsxmDm("10101");
        znsbnssbsbrwdo.setSbny(DateUtil.doDateFormat(new Date(), "yyyyMM"));
        LocalDateTime newtime = LocalDateTime.now();
        znsbnssbsbrwdo.setLrrq(newtime);
        znsbnssbsbrwdo.setXgrq(newtime);
        znsbnssbsbrwdo.setSjcsdq(znsbtzzxzzsyjmxdo.getZgswjgDm());
        znsbnssbsbrwdo.setSjgsdq(znsbtzzxzzsyjmxdo.getZgswjgDm());
        znsbnssbsbrwdo.setSjtbSj(newtime);
        log.info("zzsyjDataProc xzqhszDmold=={}",znsbcxsfyxxcjb.getXzqhszDm());
        //20250325 谭政 由于乐企目前不支持到县区级 只能将行政区划后两位改成0 便于增值税预缴后续调用乐企
        String xzqhDm = znsbcxsfyxxcjb.getXzqhszDm();
        //xzqhDm = xzqhDm.substring(0,4)+"00";
        //20250418 行政区划改用SfEnum.getSsjXzqhszDmByXzqhszDm 处理
        xzqhDm = SfEnum.getSsjXzqhszDmByXzqhszDm(xzqhDm);
        log.info("zzsyjDataProc xzqhDmnew=={}",xzqhDm);
        znsbnssbsbrwdo.setXzqhszDm(xzqhDm);
        znsbnssbsbrwdo.setYzpzzlDm("BDA0610865");


        // 调用办税员列表获取办税员
        String bsymc = StringUtils.EMPTY;
        DjxhReqVO bsyreqVO = new DjxhReqVO();
        bsyreqVO.setDjxh(String.valueOf(znsbtzzxzzsyjmxdo.getDjxh()));
        CommonResult<List<RyxxVO>> ryxxResult = companyApi.getBsyByQy(bsyreqVO);
        if (!GyUtils.isNull(ryxxResult.getData())) {
            List<RyxxVO> ryxxList = ryxxResult.getData();
            if (!GyUtils.isNull(ryxxList)) {
                bsymc = ryxxList.get(0).getZsxm1();
            }
        }

        // 获取相关配置
        final List<Map<String, Object>> sbqxwbAll = FtsCxsUtils.getAllCacheData("cs_gy_sbqxwh_sbqx");// 申报期限维护表，700条左右
        final List<Map<String, Object>> jjrAll = FtsCxsUtils.getAllCacheData("cs_gy_jjr");// 节假日配置表，4000条左右
        final List<Map<String, Object>> zqtzAll = FtsCxsUtils.getAllCacheData("cs_gy_zqtz");// 征期调整配置表。50条

        log.info("ZnsbNssbSbrwDO sbqxwbAll=={}",JsonUtils.toJson(sbqxwbAll));
        log.info("ZnsbNssbSbrwDO jjrAll=={}",JsonUtils.toJson(jjrAll));
        log.info("ZnsbNssbSbrwDO zqtzAll=={}",JsonUtils.toJson(zqtzAll));

        final Map<Object, Map<Object, List<Map<String, Object>>>> sbqxwbGroup =
                sbqxwbAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                        .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        final Map<Object, Map<Object, List<Map<String, Object>>>> jjrGroup =
                jjrAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                        .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        final Map<Object, Map<Object, List<Map<String, Object>>>> zqtzGroup =
                zqtzAll.stream().filter(f -> !GyUtils.isNull(f.get("yyyf")))
                        .collect(Collectors.groupingBy(g -> g.get("swjgDm"), Collectors.groupingBy(g -> g.get("yyyf"))));
        // 取主管税务所科分局代码
        final List<String> swjgDmList = new ArrayList<>();
        // 根据行政区划取省级机关传入
        FtsCxsUtils.getSwjgList(swjgDmList,
                SfEnum.getSjSwjgDmByXzqhszDm(xzqhDm), new HashMap<>());


        // 调用纳税人信息接口查询认定信息
        ZnsbMhzcQyjbxxmxReqVO znsbMhzcQyjbxxmxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        znsbMhzcQyjbxxmxReqVO.setDjxh(String.valueOf(znsbtzzxzzsyjmxdo.getDjxh()));
        znsbMhzcQyjbxxmxReqVO.setNsrsbh(znsbtzzxzzsyjmxdo.getNsrsbh());
        CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxRes = nsrxxApi.getNsrxxByDjxh(znsbMhzcQyjbxxmxReqVO);

        // 计算属期和申报日期止
        String nsqxDm = "06";
        String sbqxDm = "04";
        final Calendar calendarMrskssqq = Calendar.getInstance();
        calendarMrskssqq.setTime(znsbtzzxzzsyjmxdo.getSkssqq());
        final Calendar calendarMrskssqz = Calendar.getInstance();
        calendarMrskssqz.setTime(znsbtzzxzzsyjmxdo.getSkssqz());
        log.info("ZnsbNssbSbrwDO swjgDmList=={}",JsonUtils.toJson(swjgDmList));
        log.info("ZnsbNssbSbrwDO sbqxDm=={}",sbqxDm);
        log.info("ZnsbNssbSbrwDO nsqxDm=={}",nsqxDm);
        log.info("ZnsbNssbSbrwDO calendarMrskssqq=={}",DateUtil.doDateFormat(calendarMrskssqq.getTime(),"yyyy-MM-dd"));
        log.info("ZnsbNssbSbrwDO calendarMrskssqz=={}",DateUtil.doDateFormat(calendarMrskssqz.getTime(),"yyyy-MM-dd"));
        log.info("ZnsbNssbSbrwDO sbqxwbGroup=={}",JsonUtils.toJson(sbqxwbGroup));
        log.info("ZnsbNssbSbrwDO jjrGroup=={}",JsonUtils.toJson(jjrGroup));
        log.info("ZnsbNssbSbrwDO zqtzGroup=={}",JsonUtils.toJson(zqtzGroup));
        final String sbqx = CchxwsnssbGyUtils.getSbqx(swjgDmList, sbqxDm, nsqxDm, "10101",
                calendarMrskssqq, calendarMrskssqz, sbqxwbGroup, jjrGroup, zqtzGroup);
        znsbnssbsbrwdo.setBsy(bsymc);
        znsbnssbsbrwdo.setSbqx(DateUtils.toDate(sbqx, "yyyy-MM-dd"));
        //BDA0610865
//        znsbnssbsbrwdo.setSyuuid(hxbuuid);
//                    znsbnssbsbrwdo.setYbtse();
//                    znsbnssbsbrwdo.setSbuuid();
//                    znsbnssbsbrwdo.setSbqx();
//                    znsbnssbsbrwdo.setPclsh();
//                    znsbnssbsbrwdo.setFlzlpclsh();
//                    znsbnssbsbrwdo.setSbrq1();
//                    znsbnssbsbrwdo.setSbyysm();
//                    znsbnssbsbrwdo.setPzxh();
//                    znsbnssbsbrwdo.setSbjkztDm();
//                    znsbnssbsbrwdo.setLsztDm();
//                    /**
//                     * 税源uuid
//                     */
//                    @TableField(value = "syuuid")
//                    private String syuuid;
        return znsbnssbsbrwdo;
    }

    private Date getSbssqq(String ssrq){
        Calendar skssqq = Calendar.getInstance();
        if(GyUtils.isNotNull(ssrq)){
            Date ssdate = DateUtil.toDate("yyyy-MM-dd",ssrq);
            skssqq.setTime(ssdate);
        }
//        skssqq.add(Calendar.MONTH,1);
        skssqq.set(Calendar.DAY_OF_MONTH,1);
        String dateStr = DateUtil.doDateFormat(skssqq.getTime(),"yyyy-MM-dd");
        Date skssqqD = DateUtil.toDate("yyyy-MM-dd",dateStr);
        return skssqqD;
    }


    private Date getSkssqq(String ssrq){
        Calendar skssqq = Calendar.getInstance();
        if(GyUtils.isNotNull(ssrq)){
            Date ssdate = DateUtil.toDate("yyyy-MM-dd",ssrq);
            skssqq.setTime(ssdate);
        }
        skssqq.add(Calendar.MONTH,-1);
        skssqq.set(Calendar.DAY_OF_MONTH,1);
        String dateStr = DateUtil.doDateFormat(skssqq.getTime(),"yyyy-MM-dd");
        Date skssqzD = DateUtil.toDate("yyyy-MM-dd",dateStr);
        return skssqzD;
    }

    private Date getSkssqz(String ssrq){
        Calendar skssqz = Calendar.getInstance();
        if(GyUtils.isNotNull(ssrq)){
            Date ssdate = DateUtil.toDate("yyyy-MM-dd",ssrq);
            skssqz.setTime(ssdate);
        }
        skssqz.add(Calendar.MONTH,-1);
        skssqz.set(Calendar.DAY_OF_MONTH,skssqz.getActualMaximum(Calendar.DAY_OF_MONTH));
        String dateStr = DateUtil.doDateFormat(skssqz.getTime(),"yyyy-MM-dd");
        Date skssqzD = DateUtil.toDate("yyyy-MM-dd",dateStr);
        return skssqzD;
    }

    private void setSbrqqz(int jgyf,ZnsbTzzxCzbdcyjmxDO znsbtzzxczbdcyjmxdo){
        Calendar sbrqq = Calendar.getInstance();
        Calendar sbrqz = Calendar.getInstance();

        sbrqq.set(Calendar.DAY_OF_MONTH,1);
        String dateStr = DateUtil.doDateFormat(sbrqq.getTime(),"yyyy-MM-dd");
        Date sbrqqD = DateUtil.toDate("yyyy-MM-dd",dateStr);
        znsbtzzxczbdcyjmxdo.setZlrqq(sbrqqD);

        sbrqz.add(Calendar.MONTH,jgyf-1);
        sbrqz.set(Calendar.DAY_OF_MONTH,sbrqz.getActualMaximum(Calendar.DAY_OF_MONTH));
        String dateStrZ = DateUtil.doDateFormat(sbrqz.getTime(),"yyyy-MM-dd");
        Date sbrqzD = DateUtil.toDate("yyyy-MM-dd",dateStrZ);
        znsbtzzxczbdcyjmxdo.setZlrqz(sbrqzD);
    }
}




