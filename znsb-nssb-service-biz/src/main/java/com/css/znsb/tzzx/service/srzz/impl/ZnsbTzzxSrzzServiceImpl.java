package com.css.znsb.tzzx.service.srzz.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.gjss.constants.enums.JtbmEnum;
import com.css.znsb.gjss.constants.enums.TzlxDmEnum;
import com.css.znsb.gjss.mapper.bzpz.ZnsbTzzxBzpzMapper;
import com.css.znsb.gjss.pojo.domain.bzpz.ZnsbTzzxBzpzDO;
import com.css.znsb.gjss.service.zzs.ZzsCybdService;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.nsrxx.ZfjgVO;
import com.css.znsb.nssb.mapper.gy.RdHznsqyrdsqMxbMapper;
import com.css.znsb.nssb.pojo.domain.gy.RdHznsqyrdsqMxbDO;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.nssb.utils.GYCastUtils;
import com.css.znsb.tzzx.mapper.PzFlMxMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxCybdhlbMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxCybdmxMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxJmszzMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxJxfphwfwmxbMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxJxfpzzbMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxJxsezczzMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxSrcybdhzbMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxSrcybdmxMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzbMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxSrzzmxbMapper;
import com.css.znsb.tzzx.mapper.ZnsbTzzxZzshzfpbMapper;
import com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzJxsezcssMapper;
import com.css.znsb.tzzx.mapper.sshz.ZnsbTzzxLqsshzMapper;
import com.css.znsb.tzzx.pojo.PageRecordsVO;
import com.css.znsb.tzzx.pojo.domain.jxsezc.ZnsbTzzxJxsezczz;
import com.css.znsb.tzzx.pojo.domain.sshz.ZnsbTzzxLqsshzDO;
import com.css.znsb.tzzx.pojo.domain.sshz.ZnsbTzzxLqsshzJxsezcssDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.PzFlMxDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.ZnsbTzzxJmszzDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.jxfp.ZnsbTzzxJxfphwfwmxbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.jxfp.ZnsbTzzxJxfpzzbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxCybdhlbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxCybdmxDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrcybdhzbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrcybdmxDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrmxbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxSrzzbDO;
import com.css.znsb.tzzx.pojo.domain.tzzx.srzz.ZnsbTzzxZzshzfpbDO;
import com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrcyFlbDTO;
import com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrcybdmxDTO;
import com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrmxbDTO;
import com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzAndSrcybdhzbDTO;
import com.css.znsb.tzzx.pojo.dto.zzstz.srzz.SrzzDTO;
import com.css.znsb.tzzx.pojo.tzzx.HlztVO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxBdmxhlVO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxHzfpQueryVO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxJxfpzzbVO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxKjpzMxHj;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxKjpzMxVO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrcybdmxQueryVO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzInitReqVO;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxSrzzQuerySrcybdhzb;
import com.css.znsb.tzzx.pojo.tzzx.ZnsbTzzxZzResultVO;
import com.css.znsb.tzzx.pojo.tzzx.gy.DmToMcUtil;
import com.css.znsb.tzzx.pojo.tzzx.jmstz.ZnsbTzzxJmszzVO;
import com.css.znsb.tzzx.service.jxfp.impl.ZnsbTzxxJxfpzzbServiceImpl;
import com.css.znsb.tzzx.service.srzz.ZnsbTzzxSrzzService;
import com.css.znsb.tzzx.util.TzzxFzjgUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ZnsbTzzxSrzzServiceImpl extends ServiceImpl<ZnsbTzzxSrzzbMapper,ZnsbTzzxSrzzbDO> implements ZnsbTzzxSrzzService {
    @Resource
    ZnsbTzzxSrzzbMapper znsbTzzxSrzzbMapper;
    @Resource
    ZnsbTzzxSrcybdmxMapper znsbTzzxSrcybdmxMapper;
    @Resource
    PzFlMxMapper pzFlMxMapper;
    @Resource
    ZnsbTzzxSrzzmxbMapper znsbTzzxSrzzmxbMapper;

    @Resource
    private ZnsbTzzxZzshzfpbMapper znsbTzzxZzshzfpbMapper;

    @Resource
    private ZnsbTzzxSrcybdhzbMapper znsbTzzxSrcybdhzbMapper;

    @Resource
    private ZnsbTzzxCybdhlbMapper znsbTzzxCybdhlbMapper;

    @Resource
    private ZnsbTzzxJxfpzzbMapper znsbTzzxJxfpzzbMapper;

    @Resource
    ZnsbTzzxLqsshzMapper znsbTzzxLqsshzMapper;

    @Resource
    private ZnsbTzzxLqsshzJxsezcssMapper znsbTzzxLqsshzJxsezcssMapper;

    @Resource
    private ZnsbTzzxCybdmxMapper znsbTzzxCybdmxMapper;

    @Resource
    private ZnsbTzzxJmszzMapper znsbTzzxJmszzMapper;

    @Resource
    private ZnsbTzzxJxfphwfwmxbMapper znsbTzzxJxfphwfwmxbMapper;

    @Resource
    private ZnsbTzzxBzpzMapper sxydpzMapper;

    @Resource
    private ZnsbTzxxJxfpzzbServiceImpl znsbTzxxJxfpzzbService;

    @Resource
    private ZnsbTzzxJxsezczzMapper znsbTzzxJxsezczzMapper;

    @Resource
    private ZzsCybdService zzsCybdService;

    @Resource
    private NsrxxApi nsrxxApi;

    @Resource
    private RdHznsqyrdsqMxbMapper rdHznsqyrdsqMxbMapper;


    @Override
    public PageRecordsVO initSrzzQuery(ZnsbTzzxSrzzInitReqVO znsbTzzxSrzzInitReqVO) {
        Long pageSize = znsbTzzxSrzzInitReqVO.getPageSize();
        Long pageNum = znsbTzzxSrzzInitReqVO.getPageNum();
        PageRecordsVO pageRecordsVO = new PageRecordsVO();
        pageRecordsVO.setPageNum(pageNum);
        pageRecordsVO.setPageSize(pageSize);
        //山西移动特色 分支机构用户下
        //税率分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他收入都带出预征率 3%
        //税额等于 收入乘以预征率 3%
        String fzjgbz = "N";
        final String jtbmSys = CacheUtils.getXtcs("**********");
        if(JtbmEnum.SXYD.getJtbm().equals(jtbmSys)){
            Map<String,String> zjgxx = TzzxFzjgUtil.getZjgxx(znsbTzzxSrzzInitReqVO.getNsrsbh());
            fzjgbz = zjgxx.get("fzjgbz");
        }
        //根据传入的登记序号djxh和所属账期sszq查询收入总账表数据返回
        //分页查询,先查总数
        final LambdaQueryWrapper<ZnsbTzzxSrzzbDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ZnsbTzzxSrzzbDO::getDjxh,znsbTzzxSrzzInitReqVO.getDjxh());//登记序号必传
        queryWrapper.eq(ZnsbTzzxSrzzbDO::getYxbz,"Y");//有效标志 过滤
        if (GyUtils.isNotNull(znsbTzzxSrzzInitReqVO.getJsfsDm1())){//计税方式代码
            queryWrapper.in(ZnsbTzzxSrzzbDO::getJsfsDm1,znsbTzzxSrzzInitReqVO.getJsfsDm1().split(","));
        }
        if (GyUtils.isNotNull(znsbTzzxSrzzInitReqVO.getZsxmDm1())){//征收项目代码
            queryWrapper.in(ZnsbTzzxSrzzbDO::getZsxmDm,znsbTzzxSrzzInitReqVO.getZsxmDm1().split(","));
        }
        if (GyUtils.isNotNull(znsbTzzxSrzzInitReqVO.getSszqq())){//所属账期起
            queryWrapper.ge(ZnsbTzzxSrzzbDO::getSszq,znsbTzzxSrzzInitReqVO.getSszqq());
        }
        if (GyUtils.isNotNull(znsbTzzxSrzzInitReqVO.getSszqz())){//所属账期止
            queryWrapper.le(ZnsbTzzxSrzzbDO::getSszq,znsbTzzxSrzzInitReqVO.getSszqz());
        }
        if (GyUtils.isNotNull(znsbTzzxSrzzInitReqVO.getSl1())){//税率
            queryWrapper.in(ZnsbTzzxSrzzbDO::getSl1,znsbTzzxSrzzInitReqVO.getSl1().split(","));
        }
        if (GyUtils.isNotNull(znsbTzzxSrzzInitReqVO.getLrzx())){//利润中心
            queryWrapper.in(ZnsbTzzxSrzzbDO::getLrzx,znsbTzzxSrzzInitReqVO.getLrzx().split(","));
        }
        if (GyUtils.isNotNull(znsbTzzxSrzzInitReqVO.getSrlxDm())){//收入类型代码
            queryWrapper.in(ZnsbTzzxSrzzbDO::getSrlxDm,znsbTzzxSrzzInitReqVO.getSrlxDm().split(","));
        }
        if (GyUtils.isNotNull(znsbTzzxSrzzInitReqVO.getXssrkmdm())){//销售税额科目代码
            queryWrapper.in(ZnsbTzzxSrzzbDO::getXssrkmdm,znsbTzzxSrzzInitReqVO.getXssrkmdm().split(","));
        }
        queryWrapper.orderByDesc(ZnsbTzzxSrzzbDO::getLrzx,ZnsbTzzxSrzzbDO::getSrlxDm,ZnsbTzzxSrzzbDO::getSl1,ZnsbTzzxSrzzbDO::getXssekmdm);
        final long pageTotal = znsbTzzxSrzzbMapper.selectCount(queryWrapper);
        //无数据直接返回，有数据进行分页查询
        if (pageTotal>0){
            pageRecordsVO.setPageTotal(pageTotal);
            final IPage<ZnsbTzzxSrzzbDO> page = new Page<>(pageNum,pageSize);
            final List<ZnsbTzzxSrzzbDO> listDO = znsbTzzxSrzzbMapper.selectPage(page,queryWrapper).getRecords();
            //DO =>DTO
            final List<SrzzDTO> listDto =  BeanUtils.toBean(listDO,SrzzDTO.class);
            String jtbm = CacheUtils.getXtcs("**********");//获取集团编码进行查询
            List<Map<String, Object>> ywlx = CacheUtils.getTableData("dm_tzzx_ywlx");
            String finalFzjgbz = fzjgbz;
            listDto.forEach(dto -> {
                dto.setZsxmmc(CacheUtils.dm2mc("dm_tzzx_zsxm1",dto.getZsxmDm()));//征税项目名称
                dto.setJsfsmc(CacheUtils.dm2mc("dm_tzzx_jsfs",dto.getJsfsDm1()));//计税方式名称
                for (Map yw:ywlx){//设置收入类型名称
                    if (yw.get("ywlx").equals(dto.getSrlxDm()) && jtbm.equals(yw.get("jtbm"))){
                        dto.setSrlxmc((String) yw.get("ywlxmc"));
                    }
                }
                //山西移动特色 分支机构用户下
                //税率分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他收入都带出预征率 3%
                //税额等于 收入乘以预征率 3%
                Double fzjgyzl = 0.03;
                if("Y".equals(finalFzjgbz)){
                    String zsfsDm = dto.getJsfsDm1();
                    BigDecimal sl1 = new BigDecimal(GYCastUtils.cast2Str(dto.getSl1()));
                    String zsxmDm = dto.getZsxmDm();
                    if(!("02".equals(zsfsDm) && sl1.compareTo(new BigDecimal("0.05")) == 0 && Arrays.asList("03","04","05").contains(zsxmDm))){
                        dto.setSl1(fzjgyzl);
                        BigDecimal yzse = NumberUtil.round(NumberUtil.mul(dto.getXssr(),new BigDecimal(GYCastUtils.cast2Str(fzjgyzl))),2);//乘以并保留两位
                        dto.setXsse(yzse);
                    }
                }

            });
            pageRecordsVO.setRecords(listDto);
        }else {
            pageRecordsVO.setPageTotal(0L);
        }
        return pageRecordsVO;
    }
    @Override
    public Map<String,Object> initSrzzQueryHj(ZnsbTzzxSrzzInitReqVO znsbTzzxSrzzInitReqVO) {
        //山西移动特色 分支机构用户下
        //税率分支机构除了简易计税5%征收率的服务、不动产和无形资产以外其他收入都带出预征率 3%
        //税额等于 收入乘以预征率 3%
        String fzjgbz = "N";
        final String jtbmSys = CacheUtils.getXtcs("**********");
        if(JtbmEnum.SXYD.getJtbm().equals(jtbmSys)){
            Map<String,String> zjgxx = TzzxFzjgUtil.getZjgxx(znsbTzzxSrzzInitReqVO.getNsrsbh());
            fzjgbz = zjgxx.get("fzjgbz");
        }
        if("N".equals(fzjgbz)){//老版 森马
            return znsbTzzxSrzzbMapper.querSrzzHj(znsbTzzxSrzzInitReqVO);
        }else{//山西移动特色
            return znsbTzzxSrzzbMapper.querSrzzSxydHj(znsbTzzxSrzzInitReqVO);
        }
    }
    @Override
    public List<Map<String,Object>> querySrzzAllList(ZnsbTzzxSrzzInitReqVO queryData) {
        final LambdaQueryWrapper<ZnsbTzzxSrzzbDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ZnsbTzzxSrzzbDO::getDjxh,queryData.getDjxh());//登记序号必传
        queryWrapper.ge(ZnsbTzzxSrzzbDO::getSszq,queryData.getSszqq());
        queryWrapper.le(ZnsbTzzxSrzzbDO::getSszq,queryData.getSszqz());
        if (GyUtils.isNotNull(queryData.getJsfsDm1())){//计税方式代码
            queryWrapper.eq(ZnsbTzzxSrzzbDO::getJsfsDm1,queryData.getJsfsDm1());
        }
        if (GyUtils.isNotNull(queryData.getZsxmDm1())){//征收项目代码
            queryWrapper.eq(ZnsbTzzxSrzzbDO::getZsxmDm,queryData.getZsxmDm1());
        }
        queryWrapper.orderByDesc(ZnsbTzzxSrzzbDO::getLrrq);
        List<Map<String,Object>> srzzbList = this.listMaps(queryWrapper);
        //代码转名称
        DmToMcUtil.dmToMcforMap(srzzbList);
        return srzzbList;
    }
    @Override
    public PageRecordsVO querySrzzAndSrcybdhzb(ZnsbTzzxSrzzQuerySrcybdhzb queryData) {
        PageRecordsVO pageRecordsVO = new PageRecordsVO();
        final Page<SrzzAndSrcybdhzbDTO> page = new Page<>(queryData.getPageNum(),queryData.getPageSize());
        IPage<SrzzAndSrcybdhzbDTO> srzzAndSrcyList = new Page<>();
        String jgbz;
        final String jtbmSys = CacheUtils.getXtcs("**********");
        //final String jtbmSys = "000004";
        if(JtbmEnum.SXYD.getJtbm().equals(jtbmSys)){
            jgbz = "sxyd";
        } else if(JtbmEnum.QS.getJtbm().equals(jtbmSys)){
            jgbz = "qs";
        } else if(JtbmEnum.SM.getJtbm().equals(jtbmSys)){
            jgbz = "sm";
        } else {
            jgbz = "";
        }
        if("sm".equals(jgbz) || JtbmEnum.TYGJ.getJtbm().equals(jtbmSys)){//森马
            if (GyUtils.isNull(queryData.getSrlxDm())){
                srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrzzAndSrcybdhzb(page,queryData);
            }else if ("180".equals(queryData.getSrlxDm())){
                srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrcybdhzb(page,queryData);
            }else if ("130".equals(queryData.getSrlxDm())){
                srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrzzhzb(page,queryData);
            }
        }else if("sxyd".equals(jgbz)){//山西移动
            //参照之前森马180的 全查znsb_tzzx_srcybdhzb
            srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrcybdhzb(page,queryData);
        }else if("qs".equals(jgbz)){//泉膳
            if (GyUtils.isNull(queryData.getSrlxDm())){//泉膳 需要将暂估收入也放到其中
                srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrzzAndSrcybdhzbqs(page,queryData);
            }else if ("180".equals(queryData.getSrlxDm())){
                srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrcybdhzb(page,queryData);
            }else if ("130".equals(queryData.getSrlxDm())){
                srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrzzhzb(page,queryData);
            }else if("140".equals(queryData.getSrlxDm())){//泉膳 暂估收入
                srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrzzhzbqszgsr(page,queryData);
            }
            //clCyse(queryData, srzzAndSrcyList);

        }

        List<SrzzAndSrcybdhzbDTO> ListDTO = srzzAndSrcyList.getRecords();
        ListDTO.forEach(dto -> {
            dto.setXxse(dto.getXxse().setScale(2, RoundingMode.HALF_UP));
            dto.setXxskmse(dto.getXxskmse().setScale(2, RoundingMode.HALF_UP));
            if ("180".equals(dto.getSrlxdm())) {
                BigDecimal hlwc =  GyUtils.isNull(dto.getHlwc())? BigDecimal.valueOf(0) :dto.getHlwc();//需要使用差异税额的绝对值进行比较
                BigDecimal pzsl = dto.getPzsl() != null ? new BigDecimal(dto.getPzsl()) : new BigDecimal(1);
                BigDecimal wcl = dto.getCyse().abs().divide(pzsl, 2, RoundingMode.HALF_UP);
                int cy = wcl.compareTo(hlwc);//需要使用差异税额的绝对值进行比较
                if (hlwc.compareTo(BigDecimal.ZERO) == 0) {
                    dto.setCcyz(cy > 0 ? "Y" : "N");
                } else {
                    dto.setCcyz(cy > 0 ? "Y" : "N");//差异税额大于合理尾差的话超出阈值为Y
                }
                if ("Y".equals(dto.getCcyz())) {
                    if (dto.getCzrymc() != null) {
                        HlztVO hlztVO = new HlztVO();
                        hlztVO.setHlczry(dto.getCzrymc());
                        hlztVO.setHlczsj(DateUtil.doDateFormat(dto.getCzsj(), "yyyy-MM-dd"));
                        dto.setHlzt(hlztVO);
                    }
                    if("sxyd".equals(jgbz)){
                        dto.setCcmsg("成本中心段:"+dto.getLrzx()+"，税率:"+String.format("%.2f%%", dto.getSl1()*100)+"，销项税额:"
                                +dto.getXxse()+"与销项税科目税额"+dto.getXxskmse()+",相差"+dto.getCyse()
                                +"元,凭证数量"+pzsl+"个,尾差率为"+wcl.setScale(2, BigDecimal.ROUND_HALF_UP)
                                +"存在大于系统设置的阈值"+hlwc.setScale(2, BigDecimal.ROUND_HALF_UP)+"的凭证明细信息");
                    }else if("qs".equals(jgbz)){
                        dto.setCcmsg("税率:"+String.format("%.2f%%", dto.getSl1()*100)+"，销项税额:"
                                +dto.getXxse()+"与销项税科目税额"+dto.getXxskmse()+",相差"+dto.getCyse()
                                +"元,凭证数量"+pzsl+"个,尾差率为"+wcl.setScale(2, BigDecimal.ROUND_HALF_UP)
                                +"存在大于系统设置的阈值"+hlwc.setScale(2, BigDecimal.ROUND_HALF_UP)+"的凭证明细信息");
                    }else{
                        dto.setCcmsg("利润中心:"+dto.getLrzx()+"，税率:"+String.format("%.2f%%", dto.getSl1()*100)+"，销项税额:"
                                +dto.getXxse()+"与销项税科目税额"+dto.getXxskmse()+",相差"+dto.getCyse()
                                +"元,凭证数量"+pzsl+"个,尾差率为"+wcl.setScale(2, BigDecimal.ROUND_HALF_UP)
                                +"存在大于系统设置的阈值"+hlwc.setScale(2, BigDecimal.ROUND_HALF_UP)+"的凭证明细信息");
                    }

                }
            } else {
                dto.setCcyz("N");
            }
            dto.setJsfsmc((CacheUtils.dm2mc("dm_tzzx_jsfs",dto.getJsfsDm1())));
            dto.setZsxmmc((CacheUtils.dm2mc("dm_tzzx_zsxm1",dto.getZsxmDm())));
        });
        pageRecordsVO.setPageTotal(srzzAndSrcyList.getTotal());
        pageRecordsVO.setPageNum(queryData.getPageNum());
        pageRecordsVO.setPageSize(queryData.getPageSize());
        pageRecordsVO.setRecords(ListDTO);
        return pageRecordsVO;
    }

    @Override
    public Map<String, Object> querySrzzAndSrcybdhzbHj(ZnsbTzzxSrzzQuerySrcybdhzb queryData) {
        Map<String,Object> sumMap = new HashMap<>();
        final String jtbmSys = CacheUtils.getXtcs("**********");
        //final String jtbmSys = "000004";
        if(JtbmEnum.SM.getJtbm().equals(jtbmSys) || JtbmEnum.TYGJ.getJtbm().equals(jtbmSys)){//森马
            if (GyUtils.isNull(queryData.getSrlxDm())){
                Map<String,Object> ssrhzhj =  znsbTzzxSrzzbMapper.querySrcybdhzbHj(queryData);
                Map<String,Object> srhj =  znsbTzzxSrzzbMapper.querySrzzhzbHj(queryData);
                if (GyUtils.isNull(ssrhzhj)) {
                    ssrhzhj = new HashMap<>();
                    ssrhzhj.put("xssr", BigDecimal.ZERO);
                    ssrhzhj.put("xxse", BigDecimal.ZERO);
                    ssrhzhj.put("xxskmse", BigDecimal.ZERO);
                    ssrhzhj.put("cyse", BigDecimal.ZERO);
                }
                if (GyUtils.isNotNull(srhj) && srhj.size() >= 5) {
                    BigDecimal xssr = ((BigDecimal)srhj.get("xssr")).add((BigDecimal)ssrhzhj.get("xssr"));
                    BigDecimal xxse = ((BigDecimal)srhj.get("xxse")).add((BigDecimal)ssrhzhj.get("xxse"));
                    BigDecimal xxskmse = ((BigDecimal)srhj.get("xxskmse")).add((BigDecimal)ssrhzhj.get("xxskmse"));
                    BigDecimal cyse = (BigDecimal)ssrhzhj.get("cyse");
                    sumMap.put("xssr",xssr);
                    sumMap.put("xxse",xxse);
                    sumMap.put("xxskmse",xxskmse);
                    sumMap.put("cyse",cyse);
                } else {
                    sumMap = ssrhzhj;
                }
            }else if ("180".equals(queryData.getSrlxDm())){
                sumMap =  znsbTzzxSrzzbMapper.querySrcybdhzbHj(queryData);
            }else if ("130".equals(queryData.getSrlxDm())){
                sumMap =  znsbTzzxSrzzbMapper.querySrzzhzbHj(queryData);
            }
        }else if(JtbmEnum.SXYD.getJtbm().equals(jtbmSys)){//山西移动 参照森马180
            //山西移动特色 没有收入类型 参照180
            sumMap =  znsbTzzxSrzzbMapper.querySrcybdhzbHj(queryData);
        }else if(JtbmEnum.QS.getJtbm().equals(jtbmSys)){//泉膳
            if (GyUtils.isNull(queryData.getSrlxDm())){
                Map<String,Object> ssrhzhj =  znsbTzzxSrzzbMapper.querySrcybdhzbHj(queryData);
                Map<String,Object> srhj =  znsbTzzxSrzzbMapper.querySrzzhzbHjqs(queryData);
                if (GyUtils.isNull(ssrhzhj)) {
                    ssrhzhj = new HashMap<>();
                    ssrhzhj.put("xssr", BigDecimal.ZERO);
                    ssrhzhj.put("xxse", BigDecimal.ZERO);
                    ssrhzhj.put("xxskmse", BigDecimal.ZERO);
                    ssrhzhj.put("cyse", BigDecimal.ZERO);
                }
                if (GyUtils.isNotNull(srhj) && srhj.size() >= 5) {
                    BigDecimal xssr = ((BigDecimal)srhj.get("xssr")).add((BigDecimal)ssrhzhj.get("xssr"));
                    BigDecimal xxse = ((BigDecimal)srhj.get("xxse")).add((BigDecimal)ssrhzhj.get("xxse"));
                    BigDecimal xxskmse = ((BigDecimal)srhj.get("xxskmse")).add((BigDecimal)ssrhzhj.get("xxskmse"));
                    BigDecimal cyse = (BigDecimal)ssrhzhj.get("cyse");
                    sumMap.put("xssr",xssr);
                    sumMap.put("xxse",xxse);
                    sumMap.put("xxskmse",xxskmse);
                    sumMap.put("cyse",cyse);
                } else {
                    sumMap = ssrhzhj;
                }
            }else if ("180".equals(queryData.getSrlxDm())){
                sumMap =  znsbTzzxSrzzbMapper.querySrcybdhzbHj(queryData);
            }else if ("130".equals(queryData.getSrlxDm())){//复用之前森马的
                sumMap =  znsbTzzxSrzzbMapper.querySrzzhzbHj(queryData);
            }else if ("140".equals(queryData.getSrlxDm())){
                sumMap =  znsbTzzxSrzzbMapper.querySrzzhzbHjqszgsr(queryData);
            }

            //clCyseHj(queryData, sumMap);

        }

        return sumMap;
    }

    @Override
    public PageRecordsVO querySrcybdmx(ZnsbTzzxSrcybdmxQueryVO tzzxSrcybdmxQueryVO) {
        PageRecordsVO pageRecordsVO = new PageRecordsVO();
        Long pageSize = tzzxSrcybdmxQueryVO.getPageSize();
        Long pageNum = tzzxSrcybdmxQueryVO.getPageNum();
        pageRecordsVO.setPageNum(pageNum);
        pageRecordsVO.setPageSize(pageSize);
        final LambdaQueryWrapper<ZnsbTzzxSrcybdmxDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getDjxh,tzzxSrcybdmxQueryVO.getDjxh()).eq(ZnsbTzzxSrcybdmxDO::getLy,"0");
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getSszq())){//所属账期
            queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getSszq,tzzxSrcybdmxQueryVO.getSszq());
        }
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getSszqq())){//所属账期起
            queryWrapper.ge(ZnsbTzzxSrcybdmxDO::getSszq,tzzxSrcybdmxQueryVO.getSszqq());
        }
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getSszqz())){//所属账期止
            queryWrapper.le(ZnsbTzzxSrcybdmxDO::getSszq,tzzxSrcybdmxQueryVO.getSszqz());
        }
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getSl1())){//税率
            queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getSl1,tzzxSrcybdmxQueryVO.getSl1());
        }
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getCkpzh())){//参考凭证号
            queryWrapper.like(ZnsbTzzxSrcybdmxDO::getCkpzh,tzzxSrcybdmxQueryVO.getCkpzh());
        }
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getNsrsbh())){//纳税人识别号
            queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getNsrsbh,tzzxSrcybdmxQueryVO.getNsrsbh());
        }
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getLrzx())){//利润中心
            queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getLrzx,tzzxSrcybdmxQueryVO.getLrzx());
        }
        //因为泉膳 前台公司号是CN01000001 但是后台表里是 CN01 且该字段前台未展示，故去掉查询条件
        /*if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getGsh2())){//公司号
            queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getGsh2,tzzxSrcybdmxQueryVO.getGsh2());
        }*/
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getJsfsDm1())){//计税方式代码
            queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getJsfsDm1,tzzxSrcybdmxQueryVO.getJsfsDm1());
        }
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getZsxmDm1())){//征税项目代码
            queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getZsxmDm,tzzxSrcybdmxQueryVO.getZsxmDm1());
        }
        if (GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getCcyz()) && "Y".equals(tzzxSrcybdmxQueryVO.getCcyz())){//超出阈值
            queryWrapper.apply(" abs(cyse/pzsl_2) > hlwc");
        }
        queryWrapper.eq(ZnsbTzzxSrcybdmxDO::getLy, "0");
        //queryWrapper.orderByDesc(ZnsbTzzxSrcybdmxDO::getLrzx,ZnsbTzzxSrcybdmxDO::getSl1,ZnsbTzzxSrcybdmxDO::getJsfsDm1,ZnsbTzzxSrcybdmxDO::getZsxmDm,ZnsbTzzxSrcybdmxDO::getCkpzh);
        queryWrapper.last(" ORDER BY abs(cyse) DESC, lrzx DESC,sl1 DESC,jsfs_dm_1 DESC,zsxm_dm DESC,ckpzh DESC");
        long pageTotal = znsbTzzxSrcybdmxMapper.selectCount(queryWrapper);
        //无数据直接返回，有数据进行分页查询
        if (pageTotal>0){
            pageRecordsVO.setPageTotal(pageTotal);
            final IPage<ZnsbTzzxSrcybdmxDO> page = new Page<>(pageNum,pageSize);
            final List<ZnsbTzzxSrcybdmxDO> listDO = znsbTzzxSrcybdmxMapper.selectPage(page,queryWrapper).getRecords();
            //DO =>DTO
            List<SrcybdmxDTO> listDTO = BeanUtils.toBean(listDO,SrcybdmxDTO.class);
            listDTO.forEach(dto -> {
                dto.setXssr(dto.getZyjqtxssr());
                dto.setJsfsmc(CacheUtils.dm2mc("dm_tzzx_jsfs",dto.getJsfsDm1()));
                dto.setZsxmmc(CacheUtils.dm2mc("dm_tzzx_zsxm1",dto.getZsxmDm()));
            });
            pageRecordsVO.setRecords(listDTO);
        }else {
            pageRecordsVO.setPageTotal(0L);
        }
        return pageRecordsVO;
    }
    @Override
    public Map<String,Object> querySrcybdmxHj(ZnsbTzzxSrcybdmxQueryVO tzzxSrcybdmxQueryVO) {
        Map sumMap = znsbTzzxSrcybdmxMapper.querySrcybdmxHj(tzzxSrcybdmxQueryVO);
        return sumMap;
    }
    @Override
    public PageRecordsVO queryFlbByckpzbh(ZnsbTzzxSrcybdmxQueryVO tzzxSrcybdmxQueryVO) {
        PageRecordsVO pageRecordsVO = new PageRecordsVO();

        final LambdaQueryWrapper<ZnsbTzzxSrmxbDO> querySRWrapper = new LambdaQueryWrapperX<>();
        querySRWrapper.eq(ZnsbTzzxSrmxbDO::getCkpzh,tzzxSrcybdmxQueryVO.getCkpzh())
                .eq(GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getLrzx()),ZnsbTzzxSrmxbDO::getLrzx,tzzxSrcybdmxQueryVO.getLrzx())
                .eq(ZnsbTzzxSrmxbDO::getNsrsbh,tzzxSrcybdmxQueryVO.getNsrsbh())
                .eq(ZnsbTzzxSrmxbDO::getDjxh,tzzxSrcybdmxQueryVO.getDjxh())
                .eq(ZnsbTzzxSrmxbDO::getLy, "1")
                .eq(GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getGsh()), ZnsbTzzxSrmxbDO::getGsh2,tzzxSrcybdmxQueryVO.getGsh())
                .eq(GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getSszq()), ZnsbTzzxSrmxbDO::getSszq,tzzxSrcybdmxQueryVO.getSszq())
                .ge(GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getSszqq()), ZnsbTzzxSrmxbDO::getSszq,tzzxSrcybdmxQueryVO.getSszqq())
                .le(GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getSszqz()), ZnsbTzzxSrmxbDO::getSszq,tzzxSrcybdmxQueryVO.getSszqz())
                .in(ZnsbTzzxSrmxbDO::getTzlxDm,"1","4");
        List<ZnsbTzzxSrmxbDO> srmxbDOS = znsbTzzxSrzzmxbMapper.selectList(querySRWrapper);
        //DO =>DTO
        List<SrcyFlbDTO> listDTO;
        String jtbm = CacheUtils.getXtcs("**********");//获取集团编码进行查询
        if(JtbmEnum.SM.getJtbm().equals(jtbm)){//森马查询原来的 pz_fl_mx
            final LambdaQueryWrapper<PzFlMxDO> queryFlWrapper = new LambdaQueryWrapper<>();
            queryFlWrapper.eq(PzFlMxDO::getCdefine4,tzzxSrcybdmxQueryVO.getCkpzh()).eq(PzFlMxDO::getLrzx,tzzxSrcybdmxQueryVO.getLrzx()).eq(PzFlMxDO::getNsrsbh,tzzxSrcybdmxQueryVO.getNsrsbh());
            List<PzFlMxDO> listDO = pzFlMxMapper.selectList(queryFlWrapper);
            listDTO = BeanUtils.toBean(listDO,SrcyFlbDTO.class);
            if(GyUtils.isNotNull(listDTO)){
                for(SrcyFlbDTO flbDTO :listDTO){
                    String olduuid = flbDTO.getUuid();
                    for(PzFlMxDO mxDO : listDO){
                        String newuuid = mxDO.getUuid();
                        if(olduuid.equals(newuuid)){
                            flbDTO.setCDefine4(mxDO.getCdefine4());
                        }
                    }
                }
            }
        }else{//山西移动 泉膳 查询 znsb_tzzx_bzpz
            final LambdaQueryWrapper<ZnsbTzzxBzpzDO> queryFlWrapper = new LambdaQueryWrapper<>();
            queryFlWrapper.eq(ZnsbTzzxBzpzDO::getCdefine4,tzzxSrcybdmxQueryVO.getCkpzh()).eq(GyUtils.isNotNull(tzzxSrcybdmxQueryVO.getLrzx()),ZnsbTzzxBzpzDO::getLrzx,tzzxSrcybdmxQueryVO.getLrzx()).eq(ZnsbTzzxBzpzDO::getNsrsbh,tzzxSrcybdmxQueryVO.getNsrsbh());
            List<ZnsbTzzxBzpzDO> listDO = sxydpzMapper.selectList(queryFlWrapper);
            listDTO = BeanUtils.toBean(listDO,SrcyFlbDTO.class);
            if(GyUtils.isNotNull(listDTO)){
                for(SrcyFlbDTO flbDTO :listDTO){
                    String olduuid = flbDTO.getUuid();
                    for(ZnsbTzzxBzpzDO mxDO : listDO){
                        String newuuid = mxDO.getUuid();
                        if(olduuid.equals(newuuid)){
                            flbDTO.setCDefine4(mxDO.getCdefine4());
                        }
                    }
                }
            }
        }
        for (ZnsbTzzxSrmxbDO znsbTzzxSrmxbDO:srmxbDOS) {
            SrcyFlbDTO srcyFlbDTO = new SrcyFlbDTO();
            srcyFlbDTO.setUuid(znsbTzzxSrmxbDO.getUuid());
            srcyFlbDTO.setGsh(znsbTzzxSrmxbDO.getGsh2());
            srcyFlbDTO.setLrzx(znsbTzzxSrmxbDO.getLrzx());
            srcyFlbDTO.setCkpzh(znsbTzzxSrmxbDO.getCkpzh());
            srcyFlbDTO.setKjpzbh(znsbTzzxSrmxbDO.getKjpzbh());
            srcyFlbDTO.setSl1(znsbTzzxSrmxbDO.getSl1().doubleValue());
            srcyFlbDTO.setKmbm(znsbTzzxSrmxbDO.getKmdm());
            srcyFlbDTO.setKmmc(znsbTzzxSrmxbDO.getKmmc());
            srcyFlbDTO.setKjfpdm(znsbTzzxSrmxbDO.getKjfp());
            srcyFlbDTO.setBbje(znsbTzzxSrmxbDO.getBbje());
            listDTO.add(srcyFlbDTO);
        }
        List<Map<String, Object>> kjfpxx = CacheUtils.getTableData("dm_tzzx_kjfp");

        List<SrcyFlbDTO> collect = listDTO.stream().peek(vo -> {
            if (GyUtils.isNull(vo.getCkpzh())) {//如果参考凭证号为空需要
                vo.setCkpzh(vo.getCDefine4());
            }
            if(JtbmEnum.SM.getJtbm().equals(jtbm)){
                for (Map kj : kjfpxx) {
                    if (kj.get("kjfpDm").equals(vo.getKjfpdm()) && jtbm.equals(kj.get("jtbm"))) {
                        vo.setKjfpmc((String) kj.get("kjfpMc"));
                    } else {
                        vo.setKjfpmc(vo.getKjfpdm());
                    }
                }
            }

        }).collect(Collectors.toList());
        pageRecordsVO.setRecords(collect);
        return pageRecordsVO;
    }

    @Override
    public PageRecordsVO queryHzfp(ZnsbTzzxHzfpQueryVO znsbTzzxHzfpQueryVO) {
        QueryWrapper<ZnsbTzzxZzshzfpbDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("djxh", znsbTzzxHzfpQueryVO.getDjxh());
        if (GyUtils.isNotNull(znsbTzzxHzfpQueryVO.getSszqq())) {
            queryWrapper.ge("ssyf_2", znsbTzzxHzfpQueryVO.getSszqq());
        }
        if (GyUtils.isNotNull(znsbTzzxHzfpQueryVO.getSszqz())) {
            queryWrapper.le("ssyf_2", znsbTzzxHzfpQueryVO.getSszqz());
        }
        if (GyUtils.isNotNull(znsbTzzxHzfpQueryVO.getFzjgnsrsbh())) {
            queryWrapper.like("fzjgnsrsbh", znsbTzzxHzfpQueryVO.getFzjgnsrsbh());
        }
        if (GyUtils.isNotNull(znsbTzzxHzfpQueryVO.getFzjgnsrmc())) {
            queryWrapper.like("fzjgnsrmc", znsbTzzxHzfpQueryVO.getFzjgnsrmc());
        }
        /*if (GyUtils.isNotNull(znsbTzzxHzfpQueryVO.getNsrsbh())) {
            queryWrapper.eq("nsrsbh", znsbTzzxHzfpQueryVO.getNsrsbh());
        }
        if (GyUtils.isNotNull(znsbTzzxHzfpQueryVO.getNsrmc())) {
            queryWrapper.like("nsrmc", znsbTzzxHzfpQueryVO.getNsrmc());
        }*/
        // TODO 总机构下所有分机构
        IPage<ZnsbTzzxZzshzfpbDO> page = new Page<>(znsbTzzxHzfpQueryVO.getPageNum(), znsbTzzxHzfpQueryVO.getPageSize());
        IPage<ZnsbTzzxZzshzfpbDO> pageResult = znsbTzzxZzshzfpbMapper.selectPage(page, queryWrapper);
        pageResult.getRecords().forEach(item -> {
            if (GyUtils.isNotNull(item.getSsyf())) {
                item.setSsyf(item.getSsyf().substring(0, 4) + "-" + item.getSsyf().substring(4));
            }
        });
        // 查询合计
        QueryWrapper<ZnsbTzzxZzshzfpbDO> totalQueryWrapper = new QueryWrapper();
        queryWrapper.select("sum(fzjgybhwjlwjzjtxssr) fzjgybhwjlwjzjtxssr, sum(fzjgybhwjlwxssr) fzjgybhwjlwxssr, " +
                "sum(fzjgysfwxssr) fzjgysfwxssr, sum(fzjgysfwjzjtxssr) fzjgysfwjzjtxssr");
        ZnsbTzzxZzshzfpbDO totalRecord = znsbTzzxZzshzfpbMapper.selectOne(queryWrapper);

        List<ZnsbTzzxZzshzfpbDO> records = new ArrayList<>(pageResult.getRecords());
        if (totalRecord != null) {
            records.add(totalRecord);
        }
        PageRecordsVO pageRecordsVO = new PageRecordsVO();
        pageRecordsVO.setRecords(records);
        pageRecordsVO.setPageTotal(pageResult.getTotal());
        pageRecordsVO.setPageSize(znsbTzzxHzfpQueryVO.getPageSize());
        pageRecordsVO.setPageNum(znsbTzzxHzfpQueryVO.getPageNum());
        return pageRecordsVO;
    }

    @Override
    public Boolean sfzjg(ZnsbTzzxHzfpQueryVO reqvo) {

        // 提取日期部分
        String dateStr = GYCastUtils.cast2Str(reqvo.getSszqz());
        if(GyUtils.isNull(dateStr)){
            return false;
        }
        int year = Integer.parseInt(dateStr.substring(0, 4));
        int month = Integer.parseInt(dateStr.substring(4, 6));

        // 获取月份的最后一天
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate lastDay = yearMonth.atEndOfMonth();

        // 转换为Date（使用系统默认时区）
        ZonedDateTime zdt = lastDay.atStartOfDay(ZoneId.systemDefault());
        Date lastday = Date.from(zdt.toInstant());

        List<RdHznsqyrdsqMxbDO> rdList = rdHznsqyrdsqMxbMapper.checkHzfpzg(reqvo.getDjxh(), lastday);
        return GyUtils.isNotNull(rdList);

    }

    @Override
    public void cybdhl(ZnsbTzzxBdmxhlVO bdmxhlVO) {
        //获取集团编码
        final String jtbmSys = CacheUtils.getXtcs("**********");

        // TODO 判空
        //2025.03.21:加入忽略信息
        final HlztVO hlztVO = new HlztVO();
        hlztVO.setHlsy(bdmxhlVO.getHlsy());
        hlztVO.setHlyy(bdmxhlVO.getHlyy());
        hlztVO.setHldm(bdmxhlVO.getHlDm());

        //100	收入总账
        if (bdmxhlVO.getYwlxDm().equals("100")) {
            ZnsbTzzxSrcybdhzbDO znsbTzzxSrcybdhzbDO = znsbTzzxSrcybdhzbMapper.selectById(bdmxhlVO.getUuid());
            String md5 = DigestUtils.md5Hex(
                    znsbTzzxSrcybdhzbDO.getDjxh()
                    + znsbTzzxSrcybdhzbDO.getSszq()
                    + znsbTzzxSrcybdhzbDO.getNsrsbh()
                    + znsbTzzxSrcybdhzbDO.getGsh2()
                    + znsbTzzxSrcybdhzbDO.getLrzx()
                    + znsbTzzxSrcybdhzbDO.getJsfsDm1()
                    + znsbTzzxSrcybdhzbDO.getZsxmDm()
                    + znsbTzzxSrcybdhzbDO.getSl1());
            saveCybdhlb(bdmxhlVO.getYwlxDm(), znsbTzzxSrcybdhzbDO.getSszq(), bdmxhlVO.getNsrmc(), md5, znsbTzzxSrcybdhzbDO.getCyse(), hlztVO);
            //500	进项台账
        } else if (bdmxhlVO.getYwlxDm().equals("500")) {
            ZnsbTzzxJxfpzzbDO znsbTzzxJxfpzzbDO = znsbTzzxJxfpzzbMapper.selectById(bdmxhlVO.getUuid());
            ZnsbTzzxJxfpzzbVO znsbTzzxJxfpzzbVO = new ZnsbTzzxJxfpzzbVO();
            BeanUtil.copyProperties(znsbTzzxJxfpzzbDO, znsbTzzxJxfpzzbVO);
            String md5 = DigestUtils.md5Hex(znsbTzzxJxfpzzbDO.getSszq() + znsbTzzxJxfpzzbDO.getDjxh() + znsbTzzxJxfpzzbDO.getNsrmc()
                    + znsbTzzxJxfpzzbDO.getNsrsbh() + znsbTzzxJxfpzzbDO.getXmxl());
            BigDecimal cyse = BigDecimal.ZERO;
//            if (XmdlEnum.SBDKDJXSE.getDm().equals(znsbTzzxJxfpzzbDO.getXmdl())
//                    && XmxlEnum.BQRZXFQBQSBDK1.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())) {
//                //通过科目编码“2221010101”，所属月份查询比对明细表
//                znsbTzzxJxfpzzbVO.setKmbm("2221010101");
//            } else if (XmdlEnum.SBDKDJXSE.getDm().equals(znsbTzzxJxfpzzbDO.getXmdl())
//                    && XmxlEnum.DKDJSSJKPZ1.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())) {
//                //通过科目编码“2221010101”，所属月份查询比对明细表 kjfpdm F01
//                znsbTzzxJxfpzzbVO.setKmbm("2221010101");
//                znsbTzzxJxfpzzbVO.setKjfpDm("F01");
//            } else if (XmdlEnum.SBDKDJXSE.getDm().equals(znsbTzzxJxfpzzbDO.getXmdl())
//                    && XmxlEnum.HGJKZZSZYJKS1.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())) {
//                //通过科目编码“2221010101”，所属月份查询比对明细表 kjfpdm F02
//                znsbTzzxJxfpzzbVO.setKmbm("2221010101");
//                znsbTzzxJxfpzzbVO.setKjfpDm("F02");
//            } else if (XmdlEnum.DDKJXSE.getDm().equals(znsbTzzxJxfpzzbDO.getXmdl())
//                    && XmxlEnum.BQRZXFQBQSBDK2.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())) {
//                //通过科目编码“1221170000”，所属月份查询比对明细表
//                znsbTzzxJxfpzzbVO.setKmbm("1221170000");
//            } else if (XmdlEnum.SBDKDJXSE.getDm().equals(znsbTzzxJxfpzzbDO.getXmdl())
//                    && XmxlEnum.ZZSZYFP.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())) {
//                znsbTzzxJxfpzzbVO.setXmxl(znsbTzzxJxfpzzbDO.getXmxl());
//            } else if (XmdlEnum.SBDKDJXSE.getDm().equals(znsbTzzxJxfpzzbDO.getXmdl())
//                    && XmxlEnum.LKYSDZFP.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())) {
//                znsbTzzxJxfpzzbVO.setXmxl(znsbTzzxJxfpzzbDO.getXmxl());
//            } else if (XmdlEnum.SBDKDJXSE.getDm().equals(znsbTzzxJxfpzzbDO.getXmdl())
//                    && XmxlEnum.LKYSKSPZ.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())) {
//                znsbTzzxJxfpzzbVO.setXmxl(znsbTzzxJxfpzzbDO.getXmxl());
//            }

            PageRecordsVO record = znsbTzxxJxfpzzbService.getKjpzMxByXmxl(znsbTzzxJxfpzzbVO);
//            if (JtbmEnum.SXYD.getJtbm().equals(jtbmSys)) {
//                ////山西移动
//                record = znsbTzxxJxfpzzbService.getKjpzMxByXmxl(znsbTzzxJxfpzzbVO);
//            } else {
//                ////默认规则（森马）
//                if (XmxlEnum.ZZSZYFP.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())
//                        || XmxlEnum.LKYSDZFP.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())
//                        || XmxlEnum.LKYSKSPZ.getDm().equals(znsbTzzxJxfpzzbDO.getXmxl())) {
//                    record = znsbTzxxJxfpzzbService.getKjpzMxByXmxl(znsbTzzxJxfpzzbVO);
//                } else {
//                    record = znsbTzxxJxfpzzbService.getKjpzMx(znsbTzzxJxfpzzbVO);
//                }
//            }

            ZnsbTzzxZzResultVO<List<ZnsbTzzxKjpzMxVO>, ZnsbTzzxKjpzMxHj> znsbTzzxZzKjpzResultVO = (ZnsbTzzxZzResultVO<List<ZnsbTzzxKjpzMxVO>, ZnsbTzzxKjpzMxHj>)record.getRecords();
            if (GyUtils.isNotNull(znsbTzzxZzKjpzResultVO)) {
                ZnsbTzzxKjpzMxHj hj = znsbTzzxZzKjpzResultVO.getHj();
                BigDecimal sumSe = BigDecimal.ZERO;
                if (!GyUtils.isNull(hj)){
                    sumSe = hj.getBbje() == null ? BigDecimal.ZERO : hj.getBbje();
                }
                cyse = znsbTzzxJxfpzzbDO.getSe().subtract(sumSe);
            }
            saveCybdhlb(bdmxhlVO.getYwlxDm(), String.valueOf(znsbTzzxJxfpzzbDO.getSszq()), bdmxhlVO.getNsrmc(), md5, cyse, hlztVO);
            zzsCybdService.handleCacheSingleton(Collections.singletonList("jxfpFlag"),znsbTzzxJxfpzzbDO.getNsrsbh(), String.valueOf(znsbTzzxJxfpzzbDO.getSszq()));
            //310	进项税额转出台账
        } else if (bdmxhlVO.getYwlxDm().equals("310")) {
            String md5 = DigestUtils.md5Hex(bdmxhlVO.getDjxh() + bdmxhlVO.getSszq() + bdmxhlVO.getJxsezcxmDm());
            BigDecimal zcse = BigDecimal.ZERO;

            //获取总分机构nsrshb列表
            final String nsrsbh = bdmxhlVO.getNsrsbh();
            final String sszq = bdmxhlVO.getSszq();
            final List<String> nsrsbhList = new ArrayList<>();
            nsrsbhList.add(nsrsbh);

            //获取本条转出总账(忽略乐企数据以外转出税额为负数疑点时使用)
            final List<ZnsbTzzxJxsezczz> jxsezczzCyhl = znsbTzzxJxsezczzMapper.getJxsezczzCyhl(bdmxhlVO.getDjxh(), sszq, sszq, bdmxhlVO.getJxsezcxmDm());

            //获取乐企算税转出数值
            if (JtbmEnum.SXYD.getJtbm().equals(jtbmSys)) {
                ////山西移动
                //判断是否有分支机构
                final List<ZfjgVO> fzjgList = nsrxxApi.getFjgxxList(nsrsbh).getData();
                final List<String> fzjgNsrsbhList = new ArrayList<>();
                if (!GyUtils.isNull(fzjgList)) {
                    //摘取分支机构nsrsbh集合
                    for (ZfjgVO fzjgUnit:fzjgList) {
                        fzjgNsrsbhList.add(fzjgUnit.getNsrsbh());
                    }

                    nsrsbhList.addAll(fzjgNsrsbhList);
                }

                //查询分总机构汇总乐企算税数据
                LambdaQueryWrapper<ZnsbTzzxLqsshzDO> lqsshzQuery = new LambdaQueryWrapper<>();
                lqsshzQuery.in(ZnsbTzzxLqsshzDO::getNsrsbh,nsrsbhList).eq(ZnsbTzzxLqsshzDO::getSkssq,sszq).eq(ZnsbTzzxLqsshzDO::getReturncode,"00");
                List<ZnsbTzzxLqsshzDO> tzzxLqsshzDOList = znsbTzzxLqsshzMapper.selectList(lqsshzQuery);

                //根据乐企算税汇总查询相关的乐企进项转出
                if (GyUtils.isNotNull(tzzxLqsshzDOList)){//不为空的时候查询
                    //取主表
                    final List<String> lqsshzUuidList = new ArrayList<>();
                    for (ZnsbTzzxLqsshzDO lqsshzUnit:tzzxLqsshzDOList) {
                        lqsshzUuidList.add(lqsshzUnit.getUuid());
                    }

                    //取进项转出附表并加和
                    LambdaQueryWrapper<ZnsbTzzxLqsshzJxsezcssDO> lqsshjxsezcssQuery = new LambdaQueryWrapper<>();
                    lqsshjxsezcssQuery.in(ZnsbTzzxLqsshzJxsezcssDO::getZbuuid,lqsshzUuidList)
                            .in(ZnsbTzzxLqsshzJxsezcssDO::getTzlxDm, TzlxDmEnum.WTZ.getTzlxDm(), TzlxDmEnum.XTZ.getTzlxDm());
                    final List<ZnsbTzzxLqsshzJxsezcssDO> lqsshzJxsezcssList = znsbTzzxLqsshzJxsezcssMapper.selectList(lqsshjxsezcssQuery);

                    if (Objects.equals(bdmxhlVO.getJxsezcxmDm(), "07")) {
                        BigDecimal hzzyfpHj = lqsshzJxsezcssList.stream()
                                .map(ZnsbTzzxLqsshzJxsezcssDO::getHzzyfpjxsezc)
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        zcse = hzzyfpHj;
                    } else if (Objects.equals(bdmxhlVO.getJxsezcxmDm(), "08")) {
                        BigDecimal ldsedqHj = lqsshzJxsezcssList.stream()
                                .map(ZnsbTzzxLqsshzJxsezcssDO::getLdsedqjxsezc)
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        zcse = ldsedqHj;
                    } else if  (Objects.equals(bdmxhlVO.getJxsezcxmDm(), "09")) {
                        BigDecimal ldsetsHj = lqsshzJxsezcssList.stream()
                                .map(ZnsbTzzxLqsshzJxsezcssDO::getLdsetsjxsezc)
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        zcse = ldsetsHj;
                    } else if  (Objects.equals(bdmxhlVO.getJxsezcxmDm(), "10")) {
                        BigDecimal ycpzHj = lqsshzJxsezcssList.stream()
                                .map(ZnsbTzzxLqsshzJxsezcssDO::getYcpzjxsezc)
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        zcse = ycpzHj;
                    } else {
                        if (!GyUtils.isNull(jxsezczzCyhl.get(0))) {
                            zcse = jxsezczzCyhl.get(0).getZcse();
                        }
                    }
                }
            } else {
                ////默认规则（森马）
                if (StringUtils.isNotBlank(bdmxhlVO.getUuid())) {
                    ZnsbTzzxLqsshzJxsezcssDO znsbTzzxLqsshzJxsezcssDO = znsbTzzxLqsshzJxsezcssMapper.selectById(bdmxhlVO.getUuid());
                    if (Objects.equals(bdmxhlVO.getJxsezcxmDm(), "07")) {
                        zcse = znsbTzzxLqsshzJxsezcssDO.getHzzyfpjxsezc();
                    } else if (Objects.equals(bdmxhlVO.getJxsezcxmDm(), "08")) {
                        zcse = znsbTzzxLqsshzJxsezcssDO.getLdsedqjxsezc();
                    } else if  (Objects.equals(bdmxhlVO.getJxsezcxmDm(), "09")) {
                        zcse = znsbTzzxLqsshzJxsezcssDO.getLdsetsjxsezc();
                    } else if  (Objects.equals(bdmxhlVO.getJxsezcxmDm(), "10")) {
                        zcse = znsbTzzxLqsshzJxsezcssDO.getYcpzjxsezc();
                    } else {
                        if (!GyUtils.isNull(jxsezczzCyhl.get(0))) {
                            zcse = jxsezczzCyhl.get(0).getZcse();
                        }
                    }
                }
            }

            BigDecimal cypzbdse = BigDecimal.ZERO;
            BigDecimal bdse = BigDecimal.ZERO;

            //查询差异对比表
            final List<ZnsbTzzxCybdmxDO> cybdmxDOS;
            if (JtbmEnum.SXYD.getJtbm().equals(jtbmSys)) {
                //山西移动
                //通过科目编码“2104020900.070205”(红字专用发票)，所属月份查询比对明细表
                LambdaQueryWrapper<ZnsbTzzxCybdmxDO> cyqw = new LambdaQueryWrapper<>();
                cyqw.in(ZnsbTzzxCybdmxDO::getNsrsbh, nsrsbhList).eq(ZnsbTzzxCybdmxDO::getSszq, sszq).eq(ZnsbTzzxCybdmxDO::getKmbm,"2104020900.070205");
                cybdmxDOS = znsbTzzxCybdmxMapper.selectList(cyqw);
            } else {
                //默认规则（森马）
                //通过科目编码“2221010500”，所属月份查询比对明细表
                LambdaQueryWrapper<ZnsbTzzxCybdmxDO> cyqw = new LambdaQueryWrapper<>();
                cyqw.eq(ZnsbTzzxCybdmxDO::getNsrsbh,bdmxhlVO.getNsrsbh()).eq(ZnsbTzzxCybdmxDO::getSszq,bdmxhlVO.getSszq()).eq(ZnsbTzzxCybdmxDO::getKmbm,"2221010500");
                cybdmxDOS = znsbTzzxCybdmxMapper.selectList(cyqw);
            }


            boolean jxsezcxmDmMatched = false;
            for (ZnsbTzzxCybdmxDO znsbTzzxCybdmxDO : cybdmxDOS) {
                if ("C07".equals(znsbTzzxCybdmxDO.getKjfpDm()) && "07".equals(bdmxhlVO.getJxsezcxmDm())
                        || "C08".equals(znsbTzzxCybdmxDO.getKjfpDm()) && "08".equals(bdmxhlVO.getJxsezcxmDm())
                        || "C09".equals(znsbTzzxCybdmxDO.getKjfpDm()) && "09".equals(bdmxhlVO.getJxsezcxmDm())) {
                    bdse = bdse.add(GyUtils.isNull(znsbTzzxCybdmxDO.getSe()) ? BigDecimal.valueOf(0) :znsbTzzxCybdmxDO.getSe());
                    jxsezcxmDmMatched = true;
                }
                if ("C10".equals(znsbTzzxCybdmxDO.getKjfpDm()) || "C12".equals(znsbTzzxCybdmxDO.getKjfpDm())) {
                    cypzbdse = cypzbdse.add(GyUtils.isNull(znsbTzzxCybdmxDO.getSe()) ? BigDecimal.valueOf(0) :znsbTzzxCybdmxDO.getSe());
                }
            }
            if ("10".equals(bdmxhlVO.getJxsezcxmDm()) && !jxsezcxmDmMatched) {
                bdse = cypzbdse;
            }

            saveCybdhlb(bdmxhlVO.getYwlxDm(), String.valueOf(bdmxhlVO.getSszq()), bdmxhlVO.getNsrmc(), md5,
                    zcse.subtract(bdse), hlztVO);
            zzsCybdService.handleCacheSingleton(Collections.singletonList("jxsezcFlag"),bdmxhlVO.getNsrsbh(), String.valueOf(bdmxhlVO.getSszq()));
            //210	减免税台账
        } else if (bdmxhlVO.getYwlxDm().equals("210")) {
            Page<ZnsbTzzxJmszzDO> page = new Page<>(1, 10);
            ZnsbTzzxJmszzVO znsbTzzxJmsZzVO = new ZnsbTzzxJmszzVO();
            znsbTzzxJmsZzVO.setDjxh(bdmxhlVO.getDjxh());
            znsbTzzxJmsZzVO.setSsyfq(Integer.valueOf(bdmxhlVO.getSszq()));
            znsbTzzxJmsZzVO.setSsyfz(Integer.valueOf(bdmxhlVO.getSszq()));
            IPage<ZnsbTzzxJmszzDO> pageResult = znsbTzzxJmszzMapper.selectInit(page, znsbTzzxJmsZzVO);
            List<ZnsbTzzxJmszzDO> znsbTzzxJmszzDOList = pageResult.getRecords();
            for (ZnsbTzzxJmszzDO znsbTzzxJmszzDO : znsbTzzxJmszzDOList) {
                if (!znsbTzzxJmszzDO.getSsjmxzDm().equals("0001129914")) {
                    continue;
                }
                ZnsbTzzxJxfphwfwmxbDO znsbTzzxJxfphwfwmxbDO = znsbTzzxJxfphwfwmxbMapper.queryXkjshj(znsbTzzxJmszzDO.getNsrsbh(), znsbTzzxJmsZzVO.getSsyfq(), znsbTzzxJmsZzVO.getSsyfz());
                if (znsbTzzxJxfphwfwmxbDO != null && znsbTzzxJxfphwfwmxbDO.getJshj().compareTo(znsbTzzxJmszzDO.getBqfse()) == 0) {
                    break;
                }
                String md5 = DigestUtils.md5Hex(bdmxhlVO.getDjxh() + bdmxhlVO.getSszq() + znsbTzzxJmszzDO.getJmzlxDm() + znsbTzzxJmszzDO.getSsjmxzDm());
                BigDecimal jshj = znsbTzzxJxfphwfwmxbDO != null ? znsbTzzxJxfphwfwmxbDO.getJshj() : BigDecimal.ZERO;
                saveCybdhlb(bdmxhlVO.getYwlxDm(), bdmxhlVO.getSszq(), bdmxhlVO.getNsrmc(), md5, jshj.subtract(znsbTzzxJmszzDO.getBqfse()), hlztVO);
            }
            zzsCybdService.handleCacheSingleton(Collections.singletonList("jmsFlag"),bdmxhlVO.getNsrsbh(), String.valueOf(bdmxhlVO.getSszq()));
        }
    }

    private void saveCybdhlb(String ywlxDm, String ssyf, String czrymc, String md5, BigDecimal cyse, HlztVO hlztVO) {
        znsbTzzxCybdhlbMapper.delete(new LambdaUpdateWrapper<ZnsbTzzxCybdhlbDO>()
                .eq(ZnsbTzzxCybdhlbDO::getMd5, md5));
        ZnsbTzzxCybdhlbDO znsbTzzxCybdhlbDO = new ZnsbTzzxCybdhlbDO();
        znsbTzzxCybdhlbDO.setUuid(IdUtil.fastSimpleUUID());
        znsbTzzxCybdhlbDO.setMd5(md5);
        znsbTzzxCybdhlbDO.setCzrymc(ZnsbSessionUtils.getZsxm());
        znsbTzzxCybdhlbDO.setCyse(cyse);
        znsbTzzxCybdhlbDO.setSszq(ssyf);
        znsbTzzxCybdhlbDO.setYwlx(ywlxDm);
        znsbTzzxCybdhlbDO.setHlsy(hlztVO.getHlsy());
        znsbTzzxCybdhlbDO.setHlyy(hlztVO.getHlyy());
        znsbTzzxCybdhlbDO.setHlDm(hlztVO.getHldm());

        znsbTzzxCybdhlbDO.setLrrq(new Date());
        znsbTzzxCybdhlbDO.setXgrq(new Date());
        znsbTzzxCybdhlbDO.setLrrsfid("ZNSB.GJSS.JOB");
        znsbTzzxCybdhlbDO.setYwqdDm("SYS");
        znsbTzzxCybdhlbDO.setSjcsdq("00000000000");
        znsbTzzxCybdhlbDO.setSjgsdq("00000000000");
        znsbTzzxCybdhlbDO.setSjtbSj(new Date());
        znsbTzzxCybdhlbMapper.insert(znsbTzzxCybdhlbDO);
    }

    @Override
    public List<SrzzAndSrcybdhzbDTO> querySrcybdhzbBtg(ZnsbTzzxSrzzQuerySrcybdhzb znsbTzzxSrzzQuerySrcybdhzb) {
        Page<SrzzAndSrcybdhzbDTO> page = new Page<>(1, Integer.MAX_VALUE);
        znsbTzzxSrzzQuerySrcybdhzb.setCzcy("Y");
        znsbTzzxSrzzQuerySrcybdhzb.setHlbz("N");
        if (GyUtils.isNotNull(znsbTzzxSrzzQuerySrcybdhzb.getSszq())) {
            znsbTzzxSrzzQuerySrcybdhzb.setSszqq(znsbTzzxSrzzQuerySrcybdhzb.getSszq());
            znsbTzzxSrzzQuerySrcybdhzb.setSszqz(znsbTzzxSrzzQuerySrcybdhzb.getSszq());
        }
        IPage<SrzzAndSrcybdhzbDTO> srzzAndSrcyList = znsbTzzxSrzzbMapper.querySrcybdhzb(page, znsbTzzxSrzzQuerySrcybdhzb);
        return srzzAndSrcyList.getRecords();
    }

    @Override
    public PageRecordsVO initXgmnsrSrzzQuery(ZnsbTzzxSrzzInitReqVO znsbTzzxSrzzInitReqVO) {
        Long pageSize = znsbTzzxSrzzInitReqVO.getPageSize();
        Long pageNum = znsbTzzxSrzzInitReqVO.getPageNum();

        final Page<ZnsbTzzxSrzzbDO> page = new Page<>(pageNum,pageSize);
        final IPage<ZnsbTzzxSrzzbDO> pageResult = znsbTzzxSrzzbMapper.initXgmnsrSrzzQuery(page, znsbTzzxSrzzInitReqVO);
        //DO =>DTO
        List<ZnsbTzzxSrzzbDO> listDO = pageResult.getRecords();
        final List<SrzzDTO> listDto =  BeanUtils.toBean(listDO,SrzzDTO.class);
        String jtbm = CacheUtils.getXtcs("**********");//获取集团编码进行查询
        List<Map<String, Object>> ywlx = CacheUtils.getTableData("dm_tzzx_ywlx");
        listDto.forEach(dto -> {
            dto.setZsxmmc(CacheUtils.dm2mc("dm_tzzx_zsxm1",dto.getZsxmDm()));//征税项目名称
            dto.setJsfsmc(CacheUtils.dm2mc("dm_tzzx_jsfs",dto.getJsfsDm1()));//计税方式名称
            for (Map yw:ywlx){//设置收入类型名称
                if (yw.get("ywlx").equals(dto.getSrlxDm()) && jtbm.equals(yw.get("jtbm"))){
                    dto.setSrlxmc((String) yw.get("ywlxmc"));
                }
            }
            dto.setUuid(IdUtil.fastSimpleUUID());
            String year = dto.getSszq().substring(0, 4);
            Integer jd = Integer.valueOf(dto.getSszq().substring(4));
            String jdq = jd * 3 + 1 < 10 ? "0" + (jd * 3 + 1) : jd * 3 + 1 + "";
            String jdz = jd * 3 + 3 < 10 ? "0" + (jd * 3 + 3) : jd * 3 + 3 + "";
            dto.setSszq(year + "-" + jdq + "至" + year + "-" + jdz);
        });
        PageRecordsVO pageRecordsVO = new PageRecordsVO();
        pageRecordsVO.setPageNum(pageNum);
        pageRecordsVO.setPageSize(pageSize);
        pageRecordsVO.setRecords(listDto);
        pageRecordsVO.setPageTotal(pageResult.getTotal());
        return pageRecordsVO;
    }

    @Override
    public PageRecordsVO queryXgmnsrSrzzAndSrcybdhzb(ZnsbTzzxSrzzQuerySrcybdhzb queryData) {
        PageRecordsVO pageRecordsVO = new PageRecordsVO();
        final Page<SrzzAndSrcybdhzbDTO> page = new Page<>(queryData.getPageNum(),queryData.getPageSize());
        IPage<SrzzAndSrcybdhzbDTO> srzzAndSrcyList = new Page<>();
        //srzzAndSrcyList =  znsbTzzxSrzzbMapper.queryXgmnsrSrcybdhzb(page,queryData);
        //srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrzzAndSrcybdhzb(page,queryData);
        if (GyUtils.isNull(queryData.getSrlxDm())){
            srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrzzAndSrcybdhzb(page,queryData);
        }else if ("180".equals(queryData.getSrlxDm())){
            srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrcybdhzb(page,queryData);
        }else if ("130".equals(queryData.getSrlxDm())){
            srzzAndSrcyList =  znsbTzzxSrzzbMapper.querySrzzhzb(page,queryData);
        }
        List<SrzzAndSrcybdhzbDTO> ListDTO = srzzAndSrcyList.getRecords();
        ListDTO.forEach(dto -> {
            dto.setXxse(dto.getXxse().setScale(2, RoundingMode.HALF_UP));
            dto.setXxskmse(dto.getXxskmse().setScale(2, RoundingMode.HALF_UP));
            //20250410 小规模针对1分钱特殊修改 算税时候只取收入 不看税额 故此处假装通过
            /*String xgmflag = "N";
            BigDecimal cysenew = NumberUtil.round(NumberUtil.mul(dto.getXssr(),dto.getSl1()),2);//乘以并保留两位
            BigDecimal cznew = NumberUtil.sub(cysenew,dto.getXxskmse());
            if(cznew.compareTo(BigDecimal.ZERO) == 0 && dto.getCyse().abs().compareTo(new BigDecimal("0.01")) == 0){
                xgmflag = "Y";
            }
            if("Y".equals(xgmflag)){//特殊处理
                dto.setCyse(BigDecimal.ZERO);
                dto.setXxse(cysenew);
            }*/
            //log.info("new BigDecimal(dto.getSl1()))"+new BigDecimal(dto.getSl1()));
            //log.info("new BigDecimal(GYCastUtils.cast2Str(dto.getSl1()))"+new BigDecimal(GYCastUtils.cast2Str(dto.getSl1())));
            // 税率
            if (new BigDecimal("0.03").compareTo(new BigDecimal(GYCastUtils.cast2Str(dto.getSl1()))) != 0) {
                dto.setCcyz("Y");
                dto.setCcmsg("征税项目：" + CacheUtils.dm2mc("dm_tzzx_zsxm1", dto.getZsxmDm()) + "，存在不为3%税率的发票");
            } else {
                // 税额差异
                BigDecimal hlwc =  GyUtils.isNull(dto.getHlwc())? BigDecimal.valueOf(0) :dto.getHlwc();//需要使用差异税额的绝对值进行比较
                BigDecimal pzsl = dto.getPzsl() != null ? new BigDecimal(dto.getPzsl()) : new BigDecimal(1);
                BigDecimal wcl = dto.getCyse().abs().divide(pzsl, 2, RoundingMode.HALF_UP);
                int cy = wcl.compareTo(hlwc);//需要使用差异税额的绝对值进行比较
                if (hlwc.compareTo(BigDecimal.ZERO) == 0) {
                    dto.setCcyz(cy > 0 ? "Y" : "N");
                } else {
                    dto.setCcyz(cy > 0 ? "Y" : "N");//差异税额大于合理尾差的话超出阈值为Y
                }
                if ("Y".equals(dto.getCcyz())) {
                    if (dto.getCzrymc() != null) {
                        HlztVO hlztVO = new HlztVO();
                        hlztVO.setHlczry(dto.getCzrymc());
                        hlztVO.setHlczsj(DateUtil.doDateFormat(dto.getCzsj(), "yyyy-MM-dd"));
                        dto.setHlzt(hlztVO);
                    }
                    dto.setCcmsg("利润中心:"+dto.getLrzx()+"，税率:"+String.format("%.2f%%", dto.getSl1()*100)+"，销项税额:"
                            +dto.getXxse()+"与销项税科目税额"+dto.getXxskmse()+",相差"+dto.getCyse()
                            +"元,凭证数量"+pzsl+"个,尾差率为"+wcl.setScale(2, BigDecimal.ROUND_HALF_UP)
                            +"存在大于系统设置的阈值"+hlwc.setScale(2, BigDecimal.ROUND_HALF_UP)+"的凭证明细信息");
                }
            }

            dto.setJsfsmc((CacheUtils.dm2mc("dm_tzzx_jsfs",dto.getJsfsDm1())));
            dto.setZsxmmc((CacheUtils.dm2mc("dm_tzzx_zsxm1",dto.getZsxmDm())));
            //
            String year = dto.getSszq().substring(0, 4);
            String yf = dto.getSszq().substring(4, 6);
            //Integer jd = Integer.valueOf(dto.getSszq().substring(4));
            int jd = 0 ;
            if(Arrays.asList("01","02","03").contains(yf)){
                jd = 0 ;
            }else if(Arrays.asList("04","05","06").contains(yf)){
                jd = 1 ;
            }else if(Arrays.asList("07","08","09").contains(yf)){
                jd = 2 ;
            }else if(Arrays.asList("10","11","12").contains(yf)){
                jd = 3 ;
            }
            String jdq = jd * 3 + 1 < 10 ? "0" + (jd * 3 + 1) : jd * 3 + 1 + "";
            String jdz = jd * 3 + 3 < 10 ? "0" + (jd * 3 + 3) : jd * 3 + 3 + "";
            dto.setSszq(year + "-" + jdq + "至" + year + "-" + jdz);
        });
        pageRecordsVO.setPageTotal(srzzAndSrcyList.getTotal());
        pageRecordsVO.setPageNum(queryData.getPageNum());
        pageRecordsVO.setPageSize(queryData.getPageSize());
        pageRecordsVO.setRecords(ListDTO);
        return pageRecordsVO;
    }

    @Override
    public List<SrzzAndSrcybdhzbDTO> querySrtzhlwc(ZnsbTzzxSrzzQuerySrcybdhzb queryData) {
        //禅道 任务1796 提交申报表前增加合规检查监控，若收入台账仍存在尾差则阻断申报。就是那种在合理尾差范围内 但是存在尾差的情况
        queryData.setPageNum(1L);
        queryData.setPageSize(10L);
        final Page<SrzzAndSrcybdhzbDTO> page = new Page<>(queryData.getPageNum(),queryData.getPageSize());
        IPage<SrzzAndSrcybdhzbDTO> srzzAndSrcyList = znsbTzzxSrzzbMapper.querySrzzAndSrcybdhzb(page,queryData);
        List<SrzzAndSrcybdhzbDTO> ListDTO = srzzAndSrcyList.getRecords();
        List<SrzzAndSrcybdhzbDTO> ListDTOnew = CollectionUtil.newArrayList();
        ListDTO.forEach(dto -> {
            dto.setXxse(dto.getXxse().setScale(2, RoundingMode.HALF_UP));
            dto.setXxskmse(dto.getXxskmse().setScale(2, RoundingMode.HALF_UP));
            dto.setJsfsmc((CacheUtils.dm2mc("dm_tzzx_jsfs",dto.getJsfsDm1())));
            dto.setZsxmmc((CacheUtils.dm2mc("dm_tzzx_zsxm1",dto.getZsxmDm())));
            if ("180".equals(dto.getSrlxdm())) {
                BigDecimal hlwc =  GyUtils.isNull(dto.getHlwc())? BigDecimal.valueOf(0) :dto.getHlwc();//需要使用差异税额的绝对值进行比较
                BigDecimal pzsl = dto.getPzsl() != null ? new BigDecimal(dto.getPzsl()) : new BigDecimal(1);
                BigDecimal wcl = dto.getCyse().abs().divide(pzsl, 2, RoundingMode.HALF_UP);
                int cy = wcl.compareTo(hlwc);//需要使用差异税额的绝对值进行比较
                if (hlwc.compareTo(BigDecimal.ZERO) == 0) {
                    dto.setCcyz(cy > 0 ? "Y" : "N");
                } else {
                    dto.setCcyz(cy > 0 ? "Y" : "N");//差异税额大于合理尾差的话超出阈值为Y
                }
                if(dto.getCyse().compareTo(BigDecimal.ZERO) != 0 && "N".equals(dto.getCcyz())){
                    ListDTOnew.add(dto);
                }
            }
        });
        return ListDTOnew;
    }

    private void clCyse(ZnsbTzzxSrzzQuerySrcybdhzb queryData, IPage<SrzzAndSrcybdhzbDTO> srzzAndSrcyList) {
        final Page<SrmxbDTO> page1 = new Page<>(queryData.getPageNum(), queryData.getPageSize());
        IPage<SrmxbDTO> srzzAndSrcyList1 = znsbTzzxSrzzmxbMapper.querSrmxzdyXseandSe(page1, queryData);
        List<SrzzAndSrcybdhzbDTO> ListDTO1 = srzzAndSrcyList.getRecords();
        List<SrmxbDTO> ListDTO2 = srzzAndSrcyList1.getRecords();
        if(GyUtils.isNotNull(ListDTO1) && GyUtils.isNotNull(ListDTO2)){
            for(SrzzAndSrcybdhzbDTO dto1 :  ListDTO1){
                for(SrmxbDTO dto2 :  ListDTO2){
                    //泉膳利润中心特殊 不使用利润中心作为相等条件判断 && dto1.getLrzx().equals(dto2.getLrzx())
                    if(dto1.getDjxh().equals(dto2.getDjxh()) && dto1.getSszq().equals(GYCastUtils.cast2Str(dto2.getSszq()))
                            && dto1.getSrlxdm().equals(dto2.getSrlxDm()) && dto1.getNsrsbh().equals(dto2.getNsrsbh())
                            && dto1.getGsh2().equals(dto2.getGsh2())
                            && dto1.getJsfsDm1().equals(dto2.getJsfsDm1()) && dto1.getZsxmDm().equals(dto2.getZsxmDm())
                            && dto1.getSl1().equals(dto2.getSl1())
                    ){
                        dto1.setXxse(NumberUtil.add(dto1.getXxse(),dto2.getSe()));
                        if("130".equals(dto2.getSrlxDm()) || "140".equals(dto2.getSrlxDm()) ){
                            //由于差异比对存在四舍五入 之前生成自定义尾差用的就是cyse 所以此处不用xxse减去xxskmse 否足额存在类似0.0017类似尾差
                            dto1.setXxskmse(NumberUtil.add(dto1.getCyse(),dto2.getSe()));
                            dto1.setCyse(BigDecimal.ZERO);
                        }else{//110 和120的 暂时这样
                            //由于差异比对存在四舍五入 之前生成自定义尾差用的就是cyse 所以此处不用xxse减去xxskmse 否足额存在类似0.0017类似尾差
                            dto1.setCyse(NumberUtil.add(dto1.getCyse(),dto2.getSe()));
                        }
                    }

                }
            }
        }
    }

    private void clCyseHj(ZnsbTzzxSrzzQuerySrcybdhzb queryData, Map<String, Object> sumMap) {
        final Page<SrmxbDTO> page1 = new Page<>(queryData.getPageNum(), queryData.getPageSize());
        IPage<SrmxbDTO> srzzAndSrcyList1 = znsbTzzxSrzzmxbMapper.querSrmxzdyXseandSe(page1, queryData);
        List<SrmxbDTO> ListDTO2 = srzzAndSrcyList1.getRecords();

        if(GyUtils.isNotNull(sumMap) && GyUtils.isNotNull(ListDTO2)){

            if(GyUtils.isNotNull(queryData.getSrlxDm())){
                ListDTO2 = ListDTO2.stream().filter(dto -> dto.getSrlxDm().equals(queryData.getSrlxDm())).collect(Collectors.toList());
            }

            if(GyUtils.isNotNull(ListDTO2)){
                BigDecimal xseSum = ListDTO2.stream()
                        .map(dto -> dto.getXse() != null ? dto.getXse() : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal seSum = ListDTO2.stream()
                        .map(dto -> dto.getSe() != null ? dto.getSe() : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //由于四舍五入存在尾差 前台可能会出现-0.00 故新增之前 xxse要先四舍五入
                BigDecimal xxse = (BigDecimal) sumMap.get("xxse");
                xxse = xxse.setScale(2, RoundingMode.HALF_UP);
                sumMap.put("xxse",NumberUtil.add(xxse,seSum));
                sumMap.put("cyse",NumberUtil.add((BigDecimal) sumMap.get("cyse"),seSum));
            }

        }
    }

}
