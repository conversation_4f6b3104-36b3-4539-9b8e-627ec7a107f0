<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>znsb-nssb</artifactId>
        <groupId>com.css.znsb</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>znsb-nssb-service-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        智能申报-纳税申报中心 biz
    </description>

    <properties>
        <!-- 添加Sentinel版本属性 -->
        <sentinel.version>1.8.6</sentinel.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.css.znsb</groupId>
                <artifactId>znsb-framework-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!-- 下边是业务的依赖-->
        <!-- Web 相关 -->
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-common</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-cache</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-trace</artifactId>
        </dependency>

        <!-- Job 相关 -->
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-job</artifactId>
        </dependency>

        <!-- 对API的依赖 -->
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-nssb-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-dlfw-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-mhzc-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>framework-starter-xxzx-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.gov.chinatax.gt4.szc.sj</groupId>
            <artifactId>sjszzhzzs-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>${aviator.version}</version>
        </dependency>

        <!--<dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-compiler</artifactId>
            <version>${drools.version}</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-decisiontables</artifactId>
            <version>${drools.version}</version>
        </dependency>-->

        <!-- magic api-->
        <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api-spring-boot-starter</artifactId>
            <version>2.1.1</version>
        </dependency>


        <!--<dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-tzzx-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>-->
        <!--<dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-cxtj-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>-->
        <!--<dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-xxzx-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>-->

        <!-- sentinel相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!--kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <version>2.8.11</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>2.8.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-sjjh</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sap</groupId>
            <artifactId>sapjco3</artifactId>
            <version>3.0.9</version>
        </dependency>

        <dependency>
            <groupId>com.css.znsb</groupId>
            <artifactId>znsb-framework-starter-rzzx</artifactId>
        </dependency>

    </dependencies>


    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
