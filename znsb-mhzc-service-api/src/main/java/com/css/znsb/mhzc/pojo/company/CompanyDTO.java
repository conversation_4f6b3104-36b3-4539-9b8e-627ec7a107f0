package com.css.znsb.mhzc.pojo.company;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "企业基本信息")
@Data
public class CompanyDTO {

    @Schema(description = "机构UUID")
    private String jguuid1;
    @Schema(description = "机构名称")
    private String jgmc1;
    @Schema(description = "纳税人识别号")
    private String nsrsbh;
    @Schema(description = "社会信用代码")
    private String shxydm;
    @Schema(description = "省市级税务机关代码")
    private String ssjswjgDm;
    @Schema(description = "登记序号")
    private String djxh;
    @Schema(description = "上级机构UUID")
    private String sjJguuid;
    @Schema(description = "行政区划数字代码")
    private String xzqhszDm;
    @Schema(description = "纳税人名称")
    private String nsrmc;
    @Schema(description = "企业代码值")
    private String qydmz;//公司号
    @Schema(description = "企业类型值")
    private String qylxz;//1主户2报验户3跨区税源户
    @Schema(description = "组织uuid")
    private String zzuuid;
    @Schema(description = "用户uuid")
    private String yhuuid;

}
