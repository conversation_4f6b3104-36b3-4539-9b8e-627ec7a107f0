package com.css.znsb.cxtj.api;

import com.css.znsb.cxtj.pojo.SimpleQueryReq;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(name = "nssb-service")
public interface SimpleQueryApi {
    @PostMapping("/devwang/simpleQuery/queryList")
    @Operation(summary = "调用magic api服务")
    List<Map<String, Object>> queryList(@RequestBody SimpleQueryReq req);

    @PostMapping("/devwang/simpleQuery/query")
    @Operation(summary = "调用magic api服务")
    Object query(@RequestBody SimpleQueryReq req);

}
