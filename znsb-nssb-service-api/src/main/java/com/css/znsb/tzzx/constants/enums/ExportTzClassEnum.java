package com.css.znsb.tzzx.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ExportTzClassEnum {
    CZFSYMX("czfcsyMxTz", "ExportZnsbTzzxCzfcsymxbController"),
    ZZSLDTS("zzsldtsTz", "ExportZzsldtsController"),
    ZZSYJFZJG("zzsyjFzjgTz", "ExportZzsyjFzjgtzbController"),
    ZZSYJKQYJY("zzsyjKqyjyTz", "ExportZzsyjKqyjytzbController"),
    JXFPKJPZMX("jxfpKjpzMxTz", "ExportJxfpKjpzMxController"),
    HGWSMX("hgwsMx", "ExportHgwsmxController"),
    SRZZ("srZzTz", "ExportSrzzController"),
    XGMNSRSRZZ("xgmnsrSrZzTz", "ExportXgmnsrSrzzController"),
    XXSPZ("xxspzTz", "ExportXxspzController"),
    ZYFCSYMXZB("zyfcsyMxzbTz", "ExportZyfcsymxzbController"),
    SrzzAndSrcybdhzb("SrzzAndSrcybdhzb", "ExportSrzzAndSrcybdhzbController"),
    XgmnsrSrzzAndSrcybdhzb("XgmnsrSrzzAndSrcybdhzb", "ExportXgmnsrSrzzAndSrcybdhzbController"),
    Jjdjtzb("jjdjtzb", "ExportJjdjtzbController"),
    JxseQueryCy("JxseQueryCy", "ExportJxseQueryCyController"),
    FlbByckpzbh("flbByckpzbh", "ExportFlbByckpzbhController"),
    Srcybdmx("Srcybdmx", "ExportSrcybdmxController"),
    XGMNSRSRCYBDMX("xgmnsrSrcybdmx", "ExportXgmnsrSrcybdmxController"),
    SRMXB("srMxbTz", "ExportSrmxbController"),
    XGMNSRSRMXBTZ("xgmnsrSrMxbTz", "ExportXgmnsrSrmxbController"),
    SRHZFP("srHjzp", "ExportHzfpController"),
    LKYSFWKSPZMXZ("lkysfwkspzMxz", "ExportLkysfwkspzmxzController"),
    LKYSFWKSFPMX("lkysfwksfpmx", "ExportLkysfwfpmxController"),
    QTKSPZZZ("qtkspzZz","ExportQtkspzzzController"),
    JXSEZCZZ("jxsezcZzTz", "ExportJxsezcZzController"),
    JXSEZCMXZ("jxsezcMxzTz", "ExportJxsezcMxzController"),
    XXFPZZTZ("xxfpZzTz", "ExportXxfpZzController"),
    XGMNSRXXFPZZTZ("xgmnsrXxfpZzTz", "ExportXgmnsrXxfpZzController"),
    XXFPMXTZ("xxfpMxTz", "ExportXxfpMxController"),
    XGMNSRXXFPMXTZ("xgmnsrXxfpMxTz", "ExportXgmnsrXxfpMxController"),
    XXFPHWFWMXTZ("xxfpHwfwMxTz", "ExportXxfpHwfwMxController"),
    XGMNSRXXFPHWFWMXTZ("xgmnsrXxfpHwfwMxTz", "ExportXgmnsrXxfpHwfwMxController"),
    JXFPZZTZ("jxfpZzTz", "ExportJxfpZzController"),
    JXFPMXTZ("jxfpMxTz", "ExportJxfpMxController"),
    JXFPHWFWMXTZ("jxfpHwfwMxTz", "ExportJxfpHwfwMxController"),
    JMSZZ("jmsZzTz","ExportJmsZzController"),
    JMSMXZ("jmsMxzTz","ExportJmsMxzController"),
    DKDJPZMX("dkdjpzMxTz","ExportDkdjpzMxController"),
    CKHWZNXZMMX("ckhwznxzmMxTz","ExportCkhwznxzmMxTzController"),
    YHSTZXX("yhstzxx","ExportYhstzController"),
    YHSYYZB("yhsyyzb","ExportYybController"),
    FCSZB("fcszb","ExportFcjcxxtzbController"),
    FCSZYF("fcszyf","ExportZyfcsymxzbController"),
    FCHTMX("fcshtmx","ExportHtmxxxController"),
    FCSCZF("fcsczf","ExportZnsbTzzxCzfcsymxbController"),
    FCSZCXX("fcszcxx","ExportFcszcxxController"),
    YJTZ("yjtz","ExportYjtzController"),
    FZJGFZTZ("fzjgfztz","ExportFzjgfztzController"),
    CZTDSYSTZXX("cztdsystzxx","ExportCztdstzController"),
    KHBMHYLBDZ("khbhhylbdz","KhbhhylbdzController"),
    ZZSYBNSRSBRWMXONE("zzsybnsrsbrwmxone","SbrwExportController"),
    HTZZMX("htzzmx","HttzController"),
    PZMX("pzmx","PzmxController"),
    ZZSYJMX("zzsyjmx","ExportZzsyjmxController"),
    ZZSYJQKMX("zzsyjqkmx","ExportZzsyjqkmxController"),
    CZBDCYJMX("czbdcyjmx","ExportCzbdcyjmxController"),
    TESTTZ("testTz", "ExportTestController"),
    TESTTZ2("", ""),
    SZFBEXCEL("szfbExcel", "SzfbExportController"),
    CYBDEXCEL("cybd", "SemirCheckController"),
    HTMXDL("htmxdl", "YhsHtmxDlController"),
    HTMXBHGDL("htmxbhgdl", "YhsHtmxDlFailController"),
    LRZXDZ("lrzxdz", "LrzxController"),
    ZZSSJJTB("zzssjjtb", "ZzssjjtbController"),
    LDTSSQXSETJB("ldtssqxsetjb", "LdtssqxsetjbController"),
    WKJFPMXB("wkjfpMxbTz", "ExportWkpmxbController"),
    JZJTXXB("jzjtxxbTz", "ExportJzjtxxbController"),
    NSJCTZMXB("nsjctzMxTz", "ExportNsjctzmxbController"),
    SRMXWLJB("srMxwljTz", "ExportWkpmxbController"),
    SRZZWLJ("srZzwljTz", "ExportSrzzController"),
    JJDJTZ("jjdjtz", "JjdjtzController"),
    YDYJTZ("ydyjtz", "YdyjtzController"),
    FYZCTZ("fyzctz", "znsbTzzxFcsfyzctzController"),
    SWZHXXGL("swzhxx", "znsbTzzxFcsfyzctzController"),
    ;

    /**
     * 台账类型
     */
    private final String tzlx;

    /**
     * ExportInterface实现类类名
     */
    private final String className;

    public static ExportTzClassEnum getClassNameByTzlx(String tzlx) {
        return Arrays.stream(ExportTzClassEnum.values()).filter(t -> t.getTzlx().equals(tzlx)).findAny().orElse(null);
    }

}
