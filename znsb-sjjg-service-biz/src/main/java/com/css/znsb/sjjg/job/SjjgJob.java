package com.css.znsb.sjjg.job;

import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.sjjg.service.logger.LoginLogService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SjjgJob {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LoginLogService loginLogService;

    @XxlJob("updateOfflineStatusJob")
    public void pdateOfflineStatus() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("updateOfflineStatusJob start");
        String key = formatKey("updateOfflineStatusJob");
        if (validateCache(key)){
            try {
                String jobParam = GyUtils.isNull(XxlJobHelper.getJobParam())?"8":XxlJobHelper.getJobParam();
                loginLogService.updateOfflineStatus(Integer.valueOf(jobParam));
            } finally {
                delCache(key);
            }
        }
        stopWatch.stop();
        log.info("updateOfflineStatusJob end,RT:{}",stopWatch.getTotalTimeSeconds());
    }

    private Boolean validateCache(String key){
        Boolean hasKey = stringRedisTemplate.hasKey(key);
        if (hasKey){
            return false;
        }else {
            stringRedisTemplate.opsForValue().set(key,"1",60, TimeUnit.MINUTES);
            return true;
        }
    }

    private void delCache(String key){
        stringRedisTemplate.delete(key);
    }

    private static String formatKey(String key) {
        return String.format("sjjg:job:%s", key);
    }


}
