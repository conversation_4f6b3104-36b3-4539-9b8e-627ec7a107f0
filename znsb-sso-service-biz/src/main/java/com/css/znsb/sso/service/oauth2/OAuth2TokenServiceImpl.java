package com.css.znsb.sso.service.oauth2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.nacos.shaded.com.google.gson.internal.LinkedTreeMap;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.sso.constant.SsoConstants;
import com.css.znsb.sso.logger.LogoutLog;
import com.css.znsb.sso.mapper.oauth2.OAuth2AccessTokenMapper;
import com.css.znsb.sso.mapper.oauth2.OAuth2RefreshTokenMapper;
import com.css.znsb.sso.pojo.domain.oauth2.OAuth2AccessTokenDO;
import com.css.znsb.sso.pojo.domain.oauth2.OAuth2ClientDO;
import com.css.znsb.sso.pojo.domain.oauth2.OAuth2CodeDO;
import com.css.znsb.sso.pojo.domain.oauth2.OAuth2RefreshTokenDO;
import com.css.znsb.sso.pojo.domain.piaoshui.LqyqGlhtUserDO;
import com.css.znsb.sso.pojo.dto.ResponseDTO;
import com.css.znsb.sso.pojo.dto.session.AccessTokenInfo;
import com.css.znsb.sso.pojo.dto.session.SessionInfo;
import com.css.znsb.sso.pojo.dto.session.YhInfo;
import com.css.znsb.sso.pojo.dto.session.ZzInfo;
import com.css.znsb.sso.pojo.jgxx.JgInfo;
import com.css.znsb.sso.pojo.vo.NxdlSessionReqVO;
import com.css.znsb.sso.service.yhxx.YhxxService;
import com.css.znsb.sso.util.OAuth2AccessTokenRedisDAO;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.css.znsb.tzzx.enums.LoginChannelEnum;
import com.css.znsb.tzzx.enums.LoginTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.css.znsb.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.css.znsb.framework.common.util.collection.CollectionUtils.convertSet;


/**
 * OAuth2.0 Token Service 实现类
 */
@Slf4j
@Service
public class OAuth2TokenServiceImpl implements OAuth2TokenService {

    @Resource
    private OAuth2AccessTokenMapper oauth2AccessTokenMapper;
    @Resource
    private OAuth2RefreshTokenMapper oauth2RefreshTokenMapper;

    @Resource
    private OAuth2AccessTokenRedisDAO oauth2AccessTokenRedisDAO;

    @Resource
    private OAuth2ClientService oauth2ClientService;

    @Resource
    private YhxxService yhxxService;


    @Override
    @Transactional
    public OAuth2AccessTokenDO createAccessToken(String userId, String clientId, List<String> scopes) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        SessionInfo sessionInfo = new SessionInfo();
        if (!SsoConstants.CLIENT_DEFAULT.equals(clientDO.getClientid())){
            // 创建刷新令牌
            sessionInfo = createOAuth2RefreshToken(userId, clientDO, scopes);
        }else {
            sessionInfo.setCurrentYhuuid(userId);
        }
        // 创建访问令牌
        return createOAuth2AccessToken(sessionInfo, clientDO,null);
    }

    @Override
    @Transactional
    public OAuth2AccessTokenDO createAccessToken(String clientId) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        // 创建访问令牌
        return createOAuth2AccessToken(clientDO);
    }

    @Override
    public OAuth2AccessTokenDO createAccessToken(LinkedTreeMap<String, Object> result) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache("default");
        // 创建访问令牌
        return createOAuth2AccessToken(result, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO createAccessToken(LqyqGlhtUserDO userDO) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache("default");
        // 创建访问令牌
        return createOAuth2AccessToken(userDO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO createAccessToken(NxdlSessionReqVO reqVO) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache("default");
        // 创建访问令牌
        return createOAuth2AccessToken(reqVO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO createAdminAccessToken(String userId, String clientId) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        final SessionInfo sessionInfo = yhxxService.initAdminInfo(userId);

        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(sessionInfo.getCurrentYhuuid())
                .setClientid(clientDO.getClientid())
                .setRefreshToken("")
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getFwlpyxq())))
                .setZzuuid(sessionInfo.getCurrentZzuuid()).setJguuid1(sessionInfo.getCurrentJguuid());
        accessTokenDO.setYwqdDm("ZNSB.SSO");
        accessTokenDO.setLrrsfid("ZNSB.SSO");
        accessTokenDO.setXgrsfid("ZNSB.SSO");
        accessTokenDO.setSjcsdq("0000000000");
        accessTokenDO.setSjgsdq("0000000000");
        oauth2AccessTokenMapper.insert(accessTokenDO);

        final AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
        accessTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
        accessTokenInfo.setRefreshToken(accessTokenDO.getRefreshToken());
        accessTokenInfo.setUuid(accessTokenDO.getUuid());
        accessTokenInfo.setYhUuid(accessTokenDO.getYhUuid());
        accessTokenInfo.setJguuid1(accessTokenDO.getJguuid1());
        accessTokenInfo.setZzuuid(accessTokenDO.getZzuuid());
        accessTokenInfo.setGqsj(accessTokenDO.getGqsj());
        accessTokenInfo.setClientid(accessTokenDO.getClientid());
        accessTokenInfo.setSqnr(accessTokenDO.getSqnr());
        accessTokenInfo.setGllp(accessTokenDO.getGllp());
        sessionInfo.setTokenInfo(accessTokenInfo);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(sessionInfo);
        return accessTokenDO;

    }

    @Override
    public OAuth2AccessTokenDO createAccessToken(OAuth2CodeDO codeDO,OAuth2ClientDO client) {
        //创建刷新令牌
        final String oAuth2RefreshToken = createOAuth2RefreshToken(codeDO, client);
        //创建访问令牌
        final OAuth2AccessTokenDO accessTokenDO = createOAuth2AccessToken(oAuth2RefreshToken, codeDO, client);
        //更新本系统令牌的关联第三方令牌
        final OAuth2AccessTokenDO tokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessTokenDO.getGllp());
        final String oldGllp = tokenDO.getGllp();
        if (!GyUtils.isNull(oldGllp)){
            oauth2AccessTokenMapper.updateGllp(tokenDO.getUuid(),oldGllp+","+accessTokenDO.getAccessToken());
            tokenDO.setGllp(oldGllp+","+accessTokenDO.getAccessToken());
        }else {
            oauth2AccessTokenMapper.updateGllp(tokenDO.getUuid(),accessTokenDO.getAccessToken());
            tokenDO.setGllp(accessTokenDO.getAccessToken());
        }

        oauth2AccessTokenRedisDAO.updateAccessTokenInfo(tokenDO);

        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO refreshAccessToken(String refreshToken, String clientId) {
        OAuth2RefreshTokenDO refreshTokenDO = oauth2RefreshTokenMapper.selectByRefreshToken(refreshToken);
        if (refreshTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "无效的刷新令牌");
        }

        // 校验 Client 匹配
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        if (ObjectUtil.notEqual(clientId, refreshTokenDO.getClientid())) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "刷新令牌的客户端编号不正确");
        }

        // 移除相关的访问令牌
        List<OAuth2AccessTokenDO> accessTokenDOs = oauth2AccessTokenMapper.selectListByRefreshToken(refreshToken);
        if (CollUtil.isNotEmpty(accessTokenDOs)) {
            oauth2AccessTokenMapper.deleteBatchIds(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getUuid));
            oauth2AccessTokenRedisDAO.deleteList(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getAccessToken));
        }

        // 已过期的情况下，删除刷新令牌
        if (DateUtils.isExpired(refreshTokenDO.getGqsj())) {
            oauth2RefreshTokenMapper.deleteById(refreshTokenDO.getUuid());
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "刷新令牌已过期");
        }
        final OAuth2AccessTokenDO oAuth2AccessTokenDO = accessTokenDOs.stream().max(Comparator.comparing(OAuth2AccessTokenDO::getGqsj)).get();
        final OAuth2AccessTokenDO accessTokenDO = createOAuth2AccessToken(refreshTokenDO, clientDO, oAuth2AccessTokenDO.getGllp());

        //更新本系统令牌的关联第三方令牌
        final OAuth2AccessTokenDO tokenDO = oauth2AccessTokenMapper.selectByAccessToken(oAuth2AccessTokenDO.getGllp());
        final List<String> list = Arrays.asList(tokenDO.getGllp().split(","));
        if (!GyUtils.isNull(list)){
            StringJoiner stringJoiner = new StringJoiner(",");
            final List<String> collect = accessTokenDOs.stream().map(OAuth2AccessTokenDO::getAccessToken).collect(Collectors.toList());
            final List<String> collect1 = list.stream().filter(s -> !collect.contains(s)).collect(Collectors.toList());
            if (!GyUtils.isNull(collect1)) {
                collect1.forEach(stringJoiner::add);
            }
            stringJoiner.add(accessTokenDO.getAccessToken());
            oauth2AccessTokenMapper.updateGllp(tokenDO.getUuid(),stringJoiner.toString());
            tokenDO.setGllp(stringJoiner.toString());
        }else {
            oauth2AccessTokenMapper.updateGllp(tokenDO.getUuid(),"");
            tokenDO.setGllp(accessTokenDO.getAccessToken());
        }
        oauth2AccessTokenRedisDAO.updateAccessTokenInfo(tokenDO);

        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO getAccessToken(String accessToken) {
        final AccessTokenInfo tokenInfo = oauth2AccessTokenRedisDAO.getTokenInfo(accessToken);
        // 优先从 Redis 中获取
        OAuth2AccessTokenDO accessTokenDO;
        if (tokenInfo != null && tokenInfo.getAccessToken()!=null) {
            return BeanUtils.toBean(tokenInfo,OAuth2AccessTokenDO.class);
        }

        // 获取不到，从 MySQL 中获取
        accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        // 如果在 MySQL 存在，则往 Redis 中写入
        if (accessTokenDO != null && !DateUtils.isExpired(accessTokenDO.getGqsj())) {
            final AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
            accessTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
            accessTokenInfo.setRefreshToken(accessTokenDO.getRefreshToken());
            accessTokenInfo.setUuid(accessTokenDO.getUuid());
            accessTokenInfo.setYhUuid(accessTokenDO.getYhUuid());
            accessTokenInfo.setJguuid1(accessTokenDO.getJguuid1());
            accessTokenInfo.setZzuuid(accessTokenDO.getZzuuid());
            accessTokenInfo.setGqsj(accessTokenDO.getGqsj());
            accessTokenInfo.setClientid(accessTokenDO.getClientid());
            accessTokenInfo.setSqnr(accessTokenDO.getSqnr());
            accessTokenInfo.setGllp(accessTokenDO.getGllp());
            final SessionInfo sessionInfo = yhxxService.getSessionInfo(accessTokenDO.getYhUuid(), accessTokenDO.getJguuid1(),accessTokenDO.getZzuuid());
            sessionInfo.setTokenInfo(accessTokenInfo);
            oauth2AccessTokenRedisDAO.set(sessionInfo);
        }
        return accessTokenDO;
    }

    @Transactional
    @Override
    public SessionInfo checkAccessToken(String accessToken) {
        OAuth2AccessTokenDO accessTokenDO = getAccessToken(accessToken);
        if (accessTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        if (DateUtils.isExpired(accessTokenDO.getGqsj())) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌已过期");
        }
        if (!GyUtils.isNull(accessTokenDO.getYhUuid())&&accessTokenDO.getYhUuid().startsWith("open")){
            return oauth2AccessTokenRedisDAO.getSessionInfo(accessToken);
        }
        if (SsoConstants.CLIENT_DEFAULT.equals(accessTokenDO.getClientid())){
            final long seconds = Duration.between(LocalDateTime.now(), accessTokenDO.getGqsj()).getSeconds();
            if (seconds > 0 && 10 * 60 > seconds){
                OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(accessTokenDO.getClientid());
                final SessionInfo sessionInfo = oauth2AccessTokenRedisDAO.getSessionInfo(accessToken);
                sessionInfo.setCurrentZzuuid(accessTokenDO.getZzuuid());
                sessionInfo.setCurrentJguuid(accessTokenDO.getJguuid1());
                sessionInfo.setCurrentYhuuid(accessTokenDO.getYhUuid());
                final OAuth2AccessTokenDO newAccessToken = createOAuth2AccessToken(sessionInfo, clientDO,accessTokenDO.getGllp());
                //刷新第三方令牌关联
                if (!GyUtils.isNull(accessTokenDO.getGllp())){
                    final List<String> list = Arrays.asList(accessTokenDO.getGllp().split(","));
                    oauth2AccessTokenMapper.updateGllpByTokens(list,newAccessToken.getAccessToken());
                }
                return oauth2AccessTokenRedisDAO.getSessionInfo(newAccessToken.getAccessToken());
            }
        }else {
            //第三方令牌会话信息取关联令牌
            return oauth2AccessTokenRedisDAO.getSessionInfo(accessTokenDO.getGllp());
        }

        return oauth2AccessTokenRedisDAO.getSessionInfo(accessToken);
    }

    @Override
    public AccessTokenInfo checkThirdPartyToken(String accessToken) {
        OAuth2AccessTokenDO accessTokenDO = getAccessToken(accessToken);
        if (accessTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        if (DateUtils.isExpired(accessTokenDO.getGqsj())) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌已过期");
        }

        return oauth2AccessTokenRedisDAO.getTokenInfo(accessToken);
    }

    @LogoutLog(loginType = LoginTypeEnum.LOGIN_TYPE_USERNAME,
            loginChannel = LoginChannelEnum.LOGIN_CHANNEL_OAUTH
            ,sessionId = "#accessToken",userId = "#clientId")
    @Transactional
    @Override
    public OAuth2AccessTokenDO removeAccessToken(String clientId,String accessToken) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        if (SsoConstants.CLIENT_DEFAULT.equals(clientId)){
            if (accessTokenDO == null) {
                return null;
            }
            final String gllp = accessTokenDO.getGllp();
            if (!GyUtils.isNull(gllp)){
                final List<String> list = Arrays.asList(gllp.split(","));
                final List<OAuth2AccessTokenDO> oAuth2AccessTokenDOS = oauth2AccessTokenMapper.selectRefreshTokenByAccessTokens(list);
                if (!GyUtils.isNull(oAuth2AccessTokenDOS)){
                    final List<String> collect = oAuth2AccessTokenDOS.stream().map(OAuth2AccessTokenDO::getRefreshToken).collect(Collectors.toList());
                    oauth2RefreshTokenMapper.deleteByTokens(collect);
                }
                oauth2AccessTokenMapper.deleteByTokens(list);
                oauth2AccessTokenRedisDAO.deleteList(list);
            }
            oauth2AccessTokenMapper.deleteById(accessTokenDO.getUuid());
            oauth2AccessTokenRedisDAO.delete(accessToken);
            // 删除刷新令牌
            if (GyUtils.isNull(accessTokenDO.getRefreshToken())){
                oauth2RefreshTokenMapper.deleteByRefreshToken(accessTokenDO.getRefreshToken());
            }
        }else {
            if (accessTokenDO == null) {
                return null;
            }
            final String gllp = accessTokenDO.getGllp();
            //更新本系统令牌的关联第三方令牌
            final OAuth2AccessTokenDO tokenDO = oauth2AccessTokenMapper.selectByAccessToken(gllp);
            final List<String> list = Arrays.asList(tokenDO.getGllp().split(","));
            final List<String> collect = list.stream().filter(s -> !s.equals(accessTokenDO.getAccessToken())).collect(Collectors.toList());
            if (!GyUtils.isNull(collect)){
                StringJoiner stringJoiner = new StringJoiner(",");
                collect.forEach(stringJoiner::add);
                oauth2AccessTokenMapper.updateGllp(tokenDO.getUuid(),stringJoiner.toString());
                tokenDO.setGllp(stringJoiner.toString());
            }else {
                oauth2AccessTokenMapper.updateGllp(tokenDO.getUuid(),"");
                tokenDO.setGllp("");
            }
            oauth2AccessTokenRedisDAO.updateAccessTokenInfo(tokenDO);

            oauth2AccessTokenMapper.deleteById(accessTokenDO.getUuid());
            oauth2AccessTokenRedisDAO.delete(accessToken);
            // 删除刷新令牌
            if (GyUtils.isNull(accessTokenDO.getRefreshToken())){
                oauth2RefreshTokenMapper.deleteByRefreshToken(accessTokenDO.getRefreshToken());
            }
        }

        return accessTokenDO;
    }

    @LogoutLog(loginType = LoginTypeEnum.LOGIN_TYPE_USERNAME,
            loginChannel = LoginChannelEnum.LOGIN_CHANNEL_WEB
            ,sessionId = "#accessToken")
    @Override
    public List<OAuth2AccessTokenDO> removeAccessToken(String accessToken) {
        List<OAuth2AccessTokenDO> result = new ArrayList<>();
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        if (accessTokenDO == null) {
            return null;
        }
        final String gllp = accessTokenDO.getGllp();
        if (!GyUtils.isNull(gllp)){
            final List<String> list = Arrays.asList(gllp.split(","));
            final List<OAuth2AccessTokenDO> oAuth2AccessTokenDOS = oauth2AccessTokenMapper.selectRefreshTokenByAccessTokens(list);
            result.addAll(oAuth2AccessTokenDOS);
            if (!GyUtils.isNull(oAuth2AccessTokenDOS)){
                final List<String> collect = oAuth2AccessTokenDOS.stream().map(OAuth2AccessTokenDO::getRefreshToken).collect(Collectors.toList());
                oauth2RefreshTokenMapper.deleteByTokens(collect);
            }
            oauth2AccessTokenMapper.deleteByTokens(list);
            oauth2AccessTokenRedisDAO.deleteList(list);
        }
        oauth2AccessTokenMapper.deleteById(accessTokenDO.getUuid());
        oauth2AccessTokenRedisDAO.delete(accessToken);
        // 删除刷新令牌
        if (!GyUtils.isNull(accessTokenDO.getRefreshToken())){
            oauth2RefreshTokenMapper.deleteByRefreshToken(accessTokenDO.getRefreshToken());
        }

        return result;
    }

    @Override
    public SessionInfo checkYfpAccessToken(String accessToken) {
        String baseurl = CacheUtils.dm2mc("xt_xtcs", "SaaSmhUrl");
        if (GyUtils.isNull(baseurl)) throw new IllegalStateException("配置错误，请联系运维人员");
        String url = baseurl+"/ggyw/oauth2/token/check?tokenType=yfp&accessToken="+accessToken;
        String body2 = HttpRequest.get(url)
                .header("Content-Type","application/json")
                .execute().body();
        if (GyUtils.isNull(body2)){
            oauth2AccessTokenRedisDAO.delete(accessToken);
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        ResponseDTO responseDTO = JsonUtils.toBean(body2, ResponseDTO.class);
        if (GyUtils.isNull(responseDTO)||1!=responseDTO.getCode()) {
            oauth2AccessTokenRedisDAO.delete(accessToken);
            throw new IllegalStateException("访问令牌不存在");
        }
        LinkedTreeMap<String, Object> result = responseDTO.getData();
        if (GyUtils.isNull(result)) {
            oauth2AccessTokenRedisDAO.delete(accessToken);
            throw new IllegalStateException("访问令牌不存在");
        }

        return getSession(result,accessToken);
    }

    private SessionInfo getSession(LinkedTreeMap<String, Object> result,String accessToken){
        final SessionInfo sessionInfo = new SessionInfo();
        AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
        accessTokenInfo.setAccessToken(accessToken);
        accessTokenInfo.setGqsj(LocalDateTime.now().plusSeconds(3600L));
        sessionInfo.setTokenInfo(accessTokenInfo);

        LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) result.get("sessionInfo");
        YhInfo yhInfo = new YhInfo();
        yhInfo.setYhuuid((String) map.get("yhUuid"));
        yhInfo.setYhlx("4");//SaaS登录
        yhInfo.setZsxm((String) map.get("zsxm"));
        yhInfo.setSjhm((String) map.get("sjhm"));
        sessionInfo.setYhxx(yhInfo);
        sessionInfo.setCurrentYhuuid((String) map.get("yhUuid"));

        ZzInfo zzInfo = new ZzInfo();
        zzInfo.setZzuuid((String) map.get("zzuuid"));
        zzInfo.setZzmc((String) map.get("zzmc"));
        zzInfo.setTopZzbz("Y");
        zzInfo.setTopZzmc((String) map.get("zzmc"));
        zzInfo.setTopZzuuid((String) map.get("zzuuid"));

        sessionInfo.setZzxx(zzInfo);
        sessionInfo.setCurrentZzuuid((String) map.get("zzuuid"));
        List<String> bxjzzList = new ArrayList<>();
        bxjzzList.add((String) map.get("zzuuid"));
        sessionInfo.setBxjsaaszzuuidList(bxjzzList);
        List<LinkedHashMap<String, Object>> list = (List<LinkedHashMap<String, Object>>) map.get("qyList");
        List<JgInfo> jgxxList = new ArrayList<>();
        List<String> djxhList = new ArrayList<>();
        if (!GyUtils.isNull(list)) list.forEach(l->{
            JgInfo jgInfo = new JgInfo();
            jgInfo.setJguuid((String) l.get("qyuuid"));
            jgInfo.setDjxh((String) l.get("qyuuid"));
            jgInfo.setNsrsbh((String) l.get("nsrsbh"));
            jgInfo.setJgmc((String) l.get("nsrmc"));
            jgxxList.add(jgInfo);
            djxhList.add((String) l.get("qyuuid"));
        });
        sessionInfo.setKczDjxhList(djxhList);
        sessionInfo.setKczsaasjgxxList(jgxxList);

        SessionInfo redisSession = oauth2AccessTokenRedisDAO.getSessionInfo(accessToken);
        log.info("redissession值查询：{}",redisSession);
        log.info("yfp构建session值：{}",sessionInfo);
        if (GyUtils.isNull(redisSession)||GyUtils.isNull(redisSession.getCurrentYhuuid())) oauth2AccessTokenRedisDAO.set(sessionInfo);

        return sessionInfo;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(SessionInfo sessionInfo, OAuth2ClientDO clientDO, String gllp) {
        String refreshToken = "";
        if (GyUtils.isNull(sessionInfo.getTokenInfo())){
            sessionInfo = yhxxService.initYhxx(sessionInfo.getCurrentYhuuid());
        }else {
            refreshToken = sessionInfo.getTokenInfo().getRefreshToken();
        }

        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(sessionInfo.getCurrentYhuuid())
                .setClientid(clientDO.getClientid())
                .setRefreshToken(refreshToken)
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getFwlpyxq())))
                .setZzuuid(sessionInfo.getCurrentZzuuid()).setJguuid1(sessionInfo.getCurrentJguuid())
                .setGllp(gllp);
        accessTokenDO.setYwqdDm("SSO");
        accessTokenDO.setLrrsfid("SSO");
        accessTokenDO.setXgrsfid("SSO");
        accessTokenDO.setSjcsdq("0000000000");
        accessTokenDO.setSjgsdq("0000000000");
        oauth2AccessTokenMapper.insert(accessTokenDO);

        final AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
        accessTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
        accessTokenInfo.setRefreshToken(accessTokenDO.getRefreshToken());
        accessTokenInfo.setUuid(accessTokenDO.getUuid());
        accessTokenInfo.setYhUuid(accessTokenDO.getYhUuid());
        accessTokenInfo.setJguuid1(accessTokenDO.getJguuid1());
        accessTokenInfo.setZzuuid(accessTokenDO.getZzuuid());
        accessTokenInfo.setGqsj(accessTokenDO.getGqsj());
        accessTokenInfo.setClientid(accessTokenDO.getClientid());
        accessTokenInfo.setSqnr(accessTokenDO.getSqnr());
        accessTokenInfo.setGllp(accessTokenDO.getGllp());
        sessionInfo.setTokenInfo(accessTokenInfo);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(sessionInfo);
        return accessTokenDO;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(OAuth2ClientDO clientDO) {
        SessionInfo sessionInfo = new SessionInfo();
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid("open"+clientDO.getCdxdz())
                .setClientid(clientDO.getClientid())
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getFwlpyxq())));
        accessTokenDO.setYwqdDm("SSO");
        accessTokenDO.setLrrsfid("SSO");
        accessTokenDO.setXgrsfid("SSO");
        accessTokenDO.setSjcsdq("0000000000");
        accessTokenDO.setSjgsdq("0000000000");
        oauth2AccessTokenMapper.insert(accessTokenDO);

        YhInfo yhInfo = new YhInfo();
        yhInfo.setYhuuid(accessTokenDO.getYhUuid());
        yhInfo.setYhlx("7");//三方用户类型
        sessionInfo.setYhxx(yhInfo);

        final AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
        accessTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
        accessTokenInfo.setUuid(accessTokenDO.getUuid());
        accessTokenInfo.setYhUuid(accessTokenDO.getYhUuid());
        accessTokenInfo.setJguuid1(accessTokenDO.getJguuid1());
        accessTokenInfo.setZzuuid(accessTokenDO.getZzuuid());
        accessTokenInfo.setGqsj(accessTokenDO.getGqsj());
        accessTokenInfo.setClientid(accessTokenDO.getClientid());
        accessTokenInfo.setSqnr(accessTokenDO.getSqnr());
        accessTokenInfo.setGllp(accessTokenDO.getGllp());
        sessionInfo.setTokenInfo(accessTokenInfo);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(sessionInfo);
        return accessTokenDO;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(LinkedTreeMap<String, Object> result, OAuth2ClientDO clientDO) {
        String refreshToken = "";
        SessionInfo sessionInfo = yhxxService.initSaaSYhxx(result);


        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(sessionInfo.getCurrentYhuuid())
                .setClientid(clientDO.getClientid())
                .setRefreshToken(refreshToken)
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getFwlpyxq())))
                .setZzuuid(sessionInfo.getCurrentZzuuid())
                .setJguuid1(sessionInfo.getCurrentJguuid())
                .setGllp(null);
        accessTokenDO.setYwqdDm("SSO");
        accessTokenDO.setLrrsfid("SSO");
        accessTokenDO.setXgrsfid("SSO");
        accessTokenDO.setSjcsdq("0000000000");
        accessTokenDO.setSjgsdq("0000000000");
        oauth2AccessTokenMapper.insert(accessTokenDO);

        final AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
        accessTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
        accessTokenInfo.setRefreshToken(accessTokenDO.getRefreshToken());
        accessTokenInfo.setUuid(accessTokenDO.getUuid());
        accessTokenInfo.setYhUuid(accessTokenDO.getYhUuid());
        accessTokenInfo.setJguuid1(accessTokenDO.getJguuid1());
        accessTokenInfo.setZzuuid(accessTokenDO.getZzuuid());
        accessTokenInfo.setGqsj(accessTokenDO.getGqsj());
        accessTokenInfo.setClientid(accessTokenDO.getClientid());
        accessTokenInfo.setSqnr(accessTokenDO.getSqnr());
        accessTokenInfo.setGllp(accessTokenDO.getGllp());
        sessionInfo.setTokenInfo(accessTokenInfo);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(sessionInfo);
        return accessTokenDO;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(LqyqGlhtUserDO userDO, OAuth2ClientDO clientDO) {
        String refreshToken = "";
        SessionInfo sessionInfo = new SessionInfo();
        YhInfo yhInfo = new YhInfo();
        yhInfo.setYhuuid(userDO.getUuid());
        yhInfo.setYhlx("3");//票税用户
        yhInfo.setZsxm(userDO.getRealname());
        yhInfo.setSjhm(userDO.getMobile());
        sessionInfo.setYhxx(yhInfo);
        sessionInfo.setCurrentYhuuid(String.valueOf(userDO.getUserId()));

        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(sessionInfo.getCurrentYhuuid())
                .setClientid(clientDO.getClientid())
                .setRefreshToken(refreshToken)
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getFwlpyxq())))
                .setZzuuid(sessionInfo.getCurrentZzuuid())
                .setJguuid1(sessionInfo.getCurrentJguuid())
                .setGllp(null);
        accessTokenDO.setYwqdDm("SSO");
        accessTokenDO.setLrrsfid("SSO");
        accessTokenDO.setXgrsfid("SSO");
        accessTokenDO.setSjcsdq("0000000000");
        accessTokenDO.setSjgsdq("0000000000");
        oauth2AccessTokenMapper.insert(accessTokenDO);

        final AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
        accessTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
        accessTokenInfo.setRefreshToken(accessTokenDO.getRefreshToken());
        accessTokenInfo.setUuid(accessTokenDO.getUuid());
        accessTokenInfo.setYhUuid(accessTokenDO.getYhUuid());
        accessTokenInfo.setJguuid1(accessTokenDO.getJguuid1());
        accessTokenInfo.setZzuuid(accessTokenDO.getZzuuid());
        accessTokenInfo.setGqsj(accessTokenDO.getGqsj());
        accessTokenInfo.setClientid(accessTokenDO.getClientid());
        accessTokenInfo.setSqnr(accessTokenDO.getSqnr());
        accessTokenInfo.setGllp(accessTokenDO.getGllp());
        sessionInfo.setTokenInfo(accessTokenInfo);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(sessionInfo);
        return accessTokenDO;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(NxdlSessionReqVO reqVO, OAuth2ClientDO clientDO) {
        String refreshToken = "";
        SessionInfo sessionInfo = new SessionInfo();
        YhInfo yhInfo = new YhInfo();
        yhInfo.setYhuuid(reqVO.getYhuuid());
        yhInfo.setYhlx("7");//三方用户
        yhInfo.setZsxm(reqVO.getZsxm());
        yhInfo.setSjhm(reqVO.getSjhm());
        sessionInfo.setYhxx(yhInfo);
        JgInfo jgInfo = new JgInfo();
        jgInfo.setDjxh(reqVO.getDjxh());
        jgInfo.setNsrsbh(reqVO.getNsrsbh());
        jgInfo.setJgmc(reqVO.getJgmc());
        jgInfo.setJguuid(reqVO.getJguuid());
        jgInfo.setShxydm(reqVO.getShxydm());
        sessionInfo.setJgxx(jgInfo);
        ZzInfo zzInfo = new ZzInfo();
        zzInfo.setZzuuid(reqVO.getZzid());
        zzInfo.setZzmc(reqVO.getZzmc());
        sessionInfo.setZzxx(zzInfo);
        sessionInfo.setKczDjxhList(reqVO.getKczDjxhList());
        sessionInfo.setCurrentYhuuid(String.valueOf(reqVO.getYhuuid()));

        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(sessionInfo.getCurrentYhuuid())
                .setClientid(clientDO.getClientid())
                .setRefreshToken(refreshToken)
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getFwlpyxq())))
                .setZzuuid(sessionInfo.getCurrentZzuuid())
                .setJguuid1(sessionInfo.getCurrentJguuid())
                .setGllp(null);
        accessTokenDO.setYwqdDm("SSO");
        accessTokenDO.setLrrsfid("SSO");
        accessTokenDO.setXgrsfid("SSO");
        accessTokenDO.setSjcsdq("0000000000");
        accessTokenDO.setSjgsdq("0000000000");
        oauth2AccessTokenMapper.insert(accessTokenDO);

        final AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
        accessTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
        accessTokenInfo.setRefreshToken(accessTokenDO.getRefreshToken());
        accessTokenInfo.setUuid(accessTokenDO.getUuid());
        accessTokenInfo.setYhUuid(accessTokenDO.getYhUuid());
        accessTokenInfo.setJguuid1(accessTokenDO.getJguuid1());
        accessTokenInfo.setZzuuid(accessTokenDO.getZzuuid());
        accessTokenInfo.setGqsj(accessTokenDO.getGqsj());
        accessTokenInfo.setClientid(accessTokenDO.getClientid());
        accessTokenInfo.setSqnr(accessTokenDO.getSqnr());
        accessTokenInfo.setGllp(accessTokenDO.getGllp());
        sessionInfo.setTokenInfo(accessTokenInfo);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(sessionInfo);
        return accessTokenDO;
    }

    private SessionInfo createOAuth2RefreshToken(String userId, OAuth2ClientDO clientDO, List<String> scopes) {
        final SessionInfo sessionInfo = yhxxService.initYhxx(userId);

        OAuth2RefreshTokenDO refreshToken = new OAuth2RefreshTokenDO().setRefreshToken(generateRefreshToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(userId)
                .setZzuuid(sessionInfo.getCurrentZzuuid())
                .setJguuid1(sessionInfo.getCurrentJguuid())
                .setClientid(clientDO.getClientid())
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getSxlpyxq())));
        refreshToken.setYwqdDm("SSO");
        refreshToken.setLrrsfid("SSO");
        refreshToken.setXgrsfid("SSO");
        refreshToken.setSjcsdq("0000000000");
        refreshToken.setSjgsdq("0000000000");
        oauth2RefreshTokenMapper.insert(refreshToken);

        final AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
        accessTokenInfo.setRefreshToken(refreshToken.getRefreshToken());
        accessTokenInfo.setYhUuid(refreshToken.getYhUuid());
        sessionInfo.setTokenInfo(accessTokenInfo);
        return sessionInfo;
    }

    private String createOAuth2RefreshToken(OAuth2CodeDO codeDO,OAuth2ClientDO client) {
        OAuth2RefreshTokenDO refreshToken = new OAuth2RefreshTokenDO().setRefreshToken(generateRefreshToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(codeDO.getYhUuid())
                .setClientid(client.getClientid())
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(client.getSxlpyxq())));
        refreshToken.setYwqdDm("ZNSB.SSO");
        refreshToken.setLrrsfid("ZNSB.SSO");
        refreshToken.setXgrsfid("ZNSB.SSO");
        refreshToken.setSjcsdq("0000000000");
        refreshToken.setSjgsdq("0000000000");
        oauth2RefreshTokenMapper.insert(refreshToken);
        return refreshToken.getRefreshToken();
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(String refreshToken,OAuth2CodeDO codeDO, OAuth2ClientDO clientDO) {
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(codeDO.getYhUuid())
                .setClientid(clientDO.getClientid())
                .setRefreshToken(refreshToken)
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getFwlpyxq())))
                .setGllp(codeDO.getAccessToken());
        accessTokenDO.setYwqdDm("ZNSB.SSO");
        accessTokenDO.setLrrsfid("ZNSB.SSO");
        accessTokenDO.setXgrsfid("ZNSB.SSO");
        accessTokenDO.setSjcsdq("0000000000");
        accessTokenDO.setSjgsdq("0000000000");
        oauth2AccessTokenMapper.insert(accessTokenDO);

        final AccessTokenInfo thirdPartyTokenInfo = new AccessTokenInfo();
        thirdPartyTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
        thirdPartyTokenInfo.setRefreshToken(accessTokenDO.getRefreshToken());
        thirdPartyTokenInfo.setUuid(accessTokenDO.getUuid());
        thirdPartyTokenInfo.setGqsj(accessTokenDO.getGqsj());
        thirdPartyTokenInfo.setClientid(accessTokenDO.getClientid());
        thirdPartyTokenInfo.setGllp(codeDO.getAccessToken());

        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(thirdPartyTokenInfo);
        return accessTokenDO;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(OAuth2RefreshTokenDO refreshTokenDO, OAuth2ClientDO clientDO,String gllp) {
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUuid(IdUtil.fastSimpleUUID())
                .setYhUuid(refreshTokenDO.getYhUuid())
                .setClientid(clientDO.getClientid())
                .setRefreshToken(refreshTokenDO.getRefreshToken())
                .setGqsj(LocalDateTime.now().plusSeconds(Long.parseLong(clientDO.getFwlpyxq())))
                .setGllp(gllp);
        accessTokenDO.setYwqdDm("ZNSB.SSO");
        accessTokenDO.setLrrsfid("ZNSB.SSO");
        accessTokenDO.setXgrsfid("ZNSB.SSO");
        accessTokenDO.setSjcsdq("0000000000");
        accessTokenDO.setSjgsdq("0000000000");
        oauth2AccessTokenMapper.insert(accessTokenDO);

        final AccessTokenInfo thirdPartyTokenInfo = new AccessTokenInfo();
        thirdPartyTokenInfo.setAccessToken(accessTokenDO.getAccessToken());
        thirdPartyTokenInfo.setRefreshToken(accessTokenDO.getRefreshToken());
        thirdPartyTokenInfo.setUuid(accessTokenDO.getUuid());
        thirdPartyTokenInfo.setGqsj(accessTokenDO.getGqsj());
        thirdPartyTokenInfo.setClientid(accessTokenDO.getClientid());
        thirdPartyTokenInfo.setGllp(gllp);

        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(thirdPartyTokenInfo);
        return accessTokenDO;
    }

    private static String generateAccessToken() {
        return IdUtil.fastSimpleUUID();
    }

    private static String generateRefreshToken() {
        return IdUtil.fastSimpleUUID();
    }

}
