package com.css.znsb.htgl.controller.xtpz;

import com.css.framework.dlfw.api.invoke.SjcjServiceApi;
import com.css.framework.dlfw.pojo.sjcj.DlfwCsdmbGjjlDTO;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.TypeUtils;
import com.css.znsb.framework.common.util.object.keyconverter.KeyCoverters;
import com.css.znsb.framework.common.util.string.StrUtils;
import com.css.znsb.framework.common.util.template.TemplateUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.util.SqlGenerator;
import com.css.znsb.htgl.pojo.dto.sjcj.SjcjrwCsdmbDTO;
import com.css.znsb.htgl.pojo.dto.sjcj.SjcjrwCsdmbExtendInfoDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

import static com.css.znsb.framework.common.pojo.CommonResult.success;

@Tag(name = "系统配置")
@RestController
@RequestMapping("/csdm")
@Validated
@Slf4j
public class CsdmController {

    @Resource
    private SjcjServiceApi sjcjServiceApi;

    // 计划单列市行政区划代码集合
    private static final Set<String> SPECIAL_XZQH_CODES = new HashSet<>(Arrays.asList("210200", "330200", "350200", "370200", "440200"));
    // 省级行政区划代码集合
    private static final Set<String> PROVINCE_XZQH_CODES = new HashSet<>(Arrays.asList("210000", "330000", "350000", "370000", "440000"));
    // 税务任务类型代码
    private static final String SW_RWLX_DM = "03";
    // 默认税务机关字段名
    private static final String DEFAULT_SWJG_FIELD = "SWJG_DM";

    @PostMapping("/updateXtcsz")
    @Operation(summary = "更新系统参数值")
    public CommonResult<String> updateXtcsz(@RequestParam("csbm") String csbm,@RequestParam("csz") String csz){

        log.info("mhzc的请求过来啦参数是" + csbm + "@@@@@" + csz);

        return success("1");
    }

    @PostMapping("/clcsdm")
    @Operation(summary = "clcsdm")
    public CommonResult<String> clcsdm(@RequestParam("xzqhszDm") String xzqhszDm,@RequestParam("uuid") String uuid){

        final SjcjrwCsdmbDTO header = new SjcjrwCsdmbDTO();
        header.setTableName("DM_GY_SWJG");
        header.setBzwmc("税务机关代码表");
        header.setSjlyDm("LQPT");
        header.setWbxtTableName("DM_GY_SWJG");
        header.setDmblxBm("LQ_DM_GY_SWJG");
        header.setSfscysj("Y");
        header.setHcbmc("dm_gy_swjg");
        header.setDatasource("csdm");
        header.setYxbz("Y");
        header.setRwlxDm("03");
        header.setXzqhszDm(xzqhszDm);

        final DlfwCsdmbGjjlDTO csdmbGjjlDTO = sjcjServiceApi.queryCsdmbGjjl(uuid);
        if (GyUtils.isNull(csdmbGjjlDTO)) {
            log.info("{}数据采集记录不存在，跳过本次处理", uuid);
            return success("1");
        }

        final String msg = csdmbGjjlDTO.getBusinessclob();

        if (GyUtils.isNull(msg)) {
            log.info("{}数据为空，跳过本次处理", uuid);
            return success("1");
        }

//        final SjcjrwCsdmbExtendInfoDTO extendInfoDTO = header.extendInfo(SjcjrwCsdmbExtendInfoDTO.class);
        String extendInfo = "{\"deleteSql\":\"DELETE FROM DM_GY_SWJG WHERE SWJG_DM = '***********' \",\"insertSql\": \"INSERT INTO DM_GY_SWJG(SWJG_DM,SWJGMC,SWJGJC,SWJGBZ,SWJGJG,SJSWJG_DM,JGJC_DM,SWJGDZ,XZQHSZ_DM,XYBZ,YXBZ,BSFWTBZ,GHBZ) VALUES ('${swjgdm}','${swjgmc}','${swjgjc}','${swjgbz}','${swjgjg}','${sjswjgdm}','${jgjcdm}','${swjgdz}','${xzqhszdm}','${xybz}','${yxbz}','${bsfwtbz}','${ghbz}')\", \"dataColumns\": \"${swjgdm},${swjgmc},${swjgjc},${swjgbz},${sjswjgdm},${jgjcdm},${swjgyzbm},${swjgdz},${czdh},${dzxx},${xzqhszdm},${xybz},${yxbz},${bsfwtbz},${ghbz}\", \"tableColumns\": \"SWJG_DM,SWJGMC,SWJGJC,SWJGBZ,SWJGJG,SJSWJG_DM,JGJC_DM,SWJGDZ,XZQHSZ_DM,XYBZ,YXBZ,BSFWTBZ,GHBZ\"}";
        final SjcjrwCsdmbExtendInfoDTO extendInfoDTO = JsonUtils.toBean(extendInfo, SjcjrwCsdmbExtendInfoDTO.class);
        final List<Map<String, Object>> dataList = this.getDataList(extendInfoDTO, msg.replace("'",""));
        final String tableName = header.getTableName();
        log.info("开始数据采集{}", tableName);
        if (GyUtils.isNull(dataList)) {
            log.info("{}数据采集为空，跳过本次处理", tableName);
            return success("1");
        }

        assert dataList != null;

        // 处理税务机关的特殊逻辑
        this.processSwjgDm(dataList, extendInfoDTO, header);


        final List<String> sqlList = this.getSqlList(header, extendInfoDTO, dataList);
        StringBuilder sqlBuilder = new StringBuilder();
        for (final String sql : sqlList){
            sqlBuilder.append( sql).append(";");
        }

        log.info("sqlList:{}", sqlBuilder);
        return success(sqlBuilder.toString());
    }

    private List<Map<String, Object>> getDataList(SjcjrwCsdmbExtendInfoDTO extendInfoDTO, String msg) {
        final List<Map<String, Object>> dataList = JsonUtils.toMapList(msg);

        TypeUtils.convertKey(dataList, true, KeyCoverters.LOWER_TRIM_CONVERTER);

        if (GyUtils.isNull(dataList)) {
            return null;
        }

        if (GyUtils.isNotNull(extendInfoDTO.getInsertSql())) {
            return dataList;
        }

        assert dataList != null;

        final String tableColumns = extendInfoDTO.getTableColumns();
        if (GyUtils.isNull(tableColumns)) {
            return dataList;
        }
        final String dataColumns = extendInfoDTO.getDataColumns();

        if (GyUtils.isNotNull(dataColumns)) {
            // 整理插入数据
            final String[] tableColumnArr = StrUtils.split(tableColumns, ",");
            final String[] dataColumnArr = StrUtils.split(dataColumns, ",");
            for (final Map<String, Object> dataMap : dataList) {
                this.processData(dataMap, tableColumnArr, dataColumnArr);
            }
        }

        return dataList;
    }

    protected void processSwjgDm(List<Map<String, Object>> dataList, SjcjrwCsdmbExtendInfoDTO extendInfoDTO, SjcjrwCsdmbDTO header) {
        if (GyUtils.isNull(dataList)) {
            return;
        }
        final String rwlxDm = header.getRwlxDm();
        final String xzqhszDm = header.getXzqhszDm();
        log.info("xzqhszDm:{}", xzqhszDm);
        if ("03".equals(rwlxDm)) {
            final String swjgDmKey = GyUtils.defaultValue(extendInfoDTO.getTableSwjg(), "SWJG_DM");
            final String specicalSwjgDm = "00001" + xzqhszDm;
            for (final Map<String, Object> dataMap : dataList) {
                //swjgDm处理
                final String swjgDm = (String) dataMap.get(swjgDmKey);
                if ("***********".equals(swjgDm)) {
                    dataMap.put(swjgDmKey, specicalSwjgDm);
                }
            }
        }
    }


    private List<String> getSqlList(final SjcjrwCsdmbDTO header, final SjcjrwCsdmbExtendInfoDTO extendInfoDTO, final List<Map<String, Object>> dataList) {
        final String tableName = header.getTableName();
        final String[] tableColumns = this.getTableColumns(extendInfoDTO.getTableColumns());
        final List<String> sqlList = new LinkedList<>();

        // 添加配置的删除语句
        addConfiguredDeleteSql(sqlList, extendInfoDTO);

        // 根据是否删除原数据添加删除SQL
        if (isDeleteOriginalData(header)) {
            addDeleteSqlForOriginalData(sqlList, header, extendInfoDTO, tableName);
        }

        // 添加插入SQL
        addInsertSql(sqlList, header, extendInfoDTO, tableName, tableColumns, dataList);

        return sqlList;
    }

    /**
     * 添加配置的删除语句
     */
    private void addConfiguredDeleteSql(List<String> sqlList, SjcjrwCsdmbExtendInfoDTO extendInfoDTO) {
        final String deleteSqlConfig = extendInfoDTO.getDeleteSql();
        if (GyUtils.isNotNull(deleteSqlConfig)) {
            sqlList.add(deleteSqlConfig);
        }
    }

    /**
     * 判断是否删除原数据
     */
    private boolean isDeleteOriginalData(SjcjrwCsdmbDTO header) {
        return "Y".equals(header.getSfscysj());
    }

    /**
     * 添加删除原数据的SQL
     */
    private void addDeleteSqlForOriginalData(List<String> sqlList, SjcjrwCsdmbDTO header,
                                             SjcjrwCsdmbExtendInfoDTO extendInfoDTO, String tableName) {
        final String rwlxDm = header.getRwlxDm();

        if (SW_RWLX_DM.equals(rwlxDm)) {
            addSwjgDeleteSql(sqlList, header, extendInfoDTO, tableName);
        } else {
            final String deleteSql = SqlGenerator.deleteSql(tableName);
            sqlList.add(deleteSql);
        }
    }

    /**
     * 添加税务机关相关的删除SQL
     */
    private void addSwjgDeleteSql(List<String> sqlList, SjcjrwCsdmbDTO header,
                                  SjcjrwCsdmbExtendInfoDTO extendInfoDTO, String tableName) {
        final String tableSwjg = GyUtils.defaultValue(extendInfoDTO.getTableSwjg(), DEFAULT_SWJG_FIELD);
        final String xzqhszDm = header.getXzqhszDm();

        if (DEFAULT_SWJG_FIELD.equals(tableSwjg)) {
            addSwjgDmDeleteSql(sqlList, tableName, tableSwjg, xzqhszDm);
        } else {
            // 非SWJG_DM字段的删除逻辑
            String delSql = String.format("delete from %s where %s = '%s'", tableName, tableSwjg, xzqhszDm);
            sqlList.add(delSql);
        }
    }

    /**
     * 添加SWJG_DM字段的删除SQL
     */
    private void addSwjgDmDeleteSql(List<String> sqlList, String tableName, String tableSwjg, String xzqhszDm) {
        // 主删除SQL
        String delSql = buildMainDeleteSql(tableName, tableSwjg, xzqhszDm);
        sqlList.add(delSql);

        // 特殊区划的额外删除SQL
        if (SPECIAL_XZQH_CODES.contains(xzqhszDm)) {
            String additionalDelSql = String.format("delete from %s where %s like '2%s'",
                    tableName, tableSwjg, this.getSwjgPrefix(xzqhszDm));
            sqlList.add(additionalDelSql);
        }
    }

    /**
     * 构建主要的删除SQL
     */
    private String buildMainDeleteSql(String tableName, String tableSwjg, String xzqhszDm) {
        String delSql = String.format("delete from %s where %s like '1%s'",
                tableName, tableSwjg, this.getSwjgPrefix(xzqhszDm));

        // 省级区划需要排除特殊子区划
        if (PROVINCE_XZQH_CODES.contains(xzqhszDm)) {
            String specialCode = getSpecialCodeForProvince(xzqhszDm);
            if (specialCode != null) {
                delSql = delSql + String.format(" and %s not like '1%s'",
                        tableSwjg, this.getSwjgPrefix(specialCode));
            }
        }

        return delSql;
    }

    /**
     * 获取省级区划对应的特殊子区划代码
     */
    private String getSpecialCodeForProvince(String provinceCode) {
        switch (provinceCode) {
            case "210000": return "210200";
            case "330000": return "330200";
            case "350000": return "350200";
            case "370000": return "370200";
            case "440000": return "440200";
            default: return null;
        }
    }

    /**
     * 添加插入SQL
     */
    private void addInsertSql(List<String> sqlList, SjcjrwCsdmbDTO header, SjcjrwCsdmbExtendInfoDTO extendInfoDTO,
                              String tableName, String[] tableColumns, List<Map<String, Object>> dataList) {
        final String insertSqlTemp = extendInfoDTO.getInsertSql();

        if (GyUtils.isNotNull(insertSqlTemp)) {
            addCustomInsertSql(sqlList, insertSqlTemp, dataList);
        } else {
            addBatchInsertSql(sqlList, tableName, tableColumns, dataList, isDeleteOriginalData(header));
        }
    }

    /**
     * 添加自定义插入SQL
     */
    private void addCustomInsertSql(List<String> sqlList, String insertSqlTemp, List<Map<String, Object>> dataList) {
        for (final Map<String, Object> dataMap : dataList) {
            // 替换uuid键的值
            if (dataMap.containsKey("uuid")) {
                dataMap.put("uuid", GyUtils.getUuid());
            }
            final String sql = TemplateUtils.parseContent(insertSqlTemp, dataMap);
            sqlList.add(sql);
        }
    }

    /**
     * 添加批量插入SQL
     */
    private void addBatchInsertSql(List<String> sqlList, String tableName, String[] tableColumns,
                                   List<Map<String, Object>> dataList, boolean isDel) {
        final List<String> insertSqlList = SqlGenerator.insertSqlBatch(tableName, tableColumns, dataList, 500, !isDel);
        sqlList.addAll(insertSqlList);
    }

    private void processData(Map<String, Object> dataMap, String[] tableColumns, String[] dataColumns) {
        if (GyUtils.isNull(tableColumns) || GyUtils.isNull(dataColumns)) {
            return;
        }

        for (int i = 0; i < tableColumns.length; i++) {
            final String name = tableColumns[i].trim();
            final String dataColumn = dataColumns[i].trim();
            String value = null;
            try {
                value = TemplateUtils.parseContent(dataColumn, dataMap);
            } catch (Exception ignore) {
                log.info(ignore.getMessage());
            }
            dataMap.put(name, value);
        }
    }

    private String[] getTableColumns(String tableColumns) {
        final String[] tableColumnArr = StrUtils.split(tableColumns, ",");
        if (GyUtils.isNull(tableColumnArr)) {
            return null;
        }
        assert tableColumnArr != null;
        for (int i = 0; i < tableColumnArr.length; i++) {
            tableColumnArr[i] = tableColumnArr[i].trim();
        }
        return tableColumnArr;
    }

    public String getSwjgPrefix(String swjgDm) {
        return swjgDm.replaceAll("(00)+$", "%");
    }
}
