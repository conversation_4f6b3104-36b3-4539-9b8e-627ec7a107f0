package com.css.znsb.mhzc.mapper.gxb;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.QueryWrapperX;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxZzjgGxbDO;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ZnsbMhqxZzjgGxbMapper extends BaseMapperX<ZnsbMhqxZzjgGxbDO> {

    default List<ZnsbMhqxZzjgGxbDO> selectJgByZz(List<String> zzuuids,String zszzbz){
        QueryWrapper<ZnsbMhqxZzjgGxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxZzjgGxbDO::getZzuuid,ZnsbMhqxZzjgGxbDO::getJguuid1,ZnsbMhqxZzjgGxbDO::getZszzbz1,ZnsbMhqxZzjgGxbDO::getUuid);
        wrapper.lambda().in(!GyUtils.isNull(zzuuids),ZnsbMhqxZzjgGxbDO::getZzuuid,zzuuids)
                .eq(!GyUtils.isNull(zszzbz),ZnsbMhqxZzjgGxbDO::getZszzbz1,zszzbz);
        return selectList(wrapper);
    }

    default List<ZnsbMhqxZzjgGxbDO> selectZzByJg(Set<String> jguuids){
        QueryWrapper<ZnsbMhqxZzjgGxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxZzjgGxbDO::getZzuuid,ZnsbMhqxZzjgGxbDO::getJguuid1)
                .in(ZnsbMhqxZzjgGxbDO::getJguuid1,jguuids);
        return selectList(wrapper);
    }


    default Integer deleteZzJgGx(String zzuuid,String jguuid){
        QueryWrapper<ZnsbMhqxZzjgGxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ZnsbMhqxZzjgGxbDO::getZzuuid,zzuuid)
                .eq(ZnsbMhqxZzjgGxbDO::getJguuid1,jguuid);
        return delete(wrapper);
    }

    default Boolean saveBatch(List<ZnsbMhqxZzjgGxbDO> list){
        return insertBatch(list);
    }


    default List<ZnsbMhqxZzjgGxbDO> selectAll(){
        QueryWrapperX<ZnsbMhqxZzjgGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .select(ZnsbMhqxZzjgGxbDO::getZzuuid,ZnsbMhqxZzjgGxbDO::getJguuid1);
        return selectList(wrapperX);
    }


    default Integer deleteByZzuuids(List<String> zzuuids){
        if (GyUtils.isNull(zzuuids)){
            return 0;
        }
        QueryWrapper<ZnsbMhqxZzjgGxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ZnsbMhqxZzjgGxbDO::getZzuuid,zzuuids);
        return delete(wrapper);
    }

    default void updZszzbz(List<String> b){
        UpdateWrapper<ZnsbMhqxZzjgGxbDO> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(ZnsbMhqxZzjgGxbDO::getZszzbz1,"Y")
                .in(ZnsbMhqxZzjgGxbDO::getUuid,b);
        update(wrapper);
    }

    default List<ZnsbMhqxZzjgGxbDO> getByZzAndJg(String zzuuid,String jguuid){
        QueryWrapper<ZnsbMhqxZzjgGxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ZnsbMhqxZzjgGxbDO::getZzuuid,zzuuid)
                .eq(ZnsbMhqxZzjgGxbDO::getJguuid1,jguuid);
        return selectList(wrapper);
    }
}




