package com.css.znsb.mhzc.mapper.gxb;


import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.QueryWrapperX;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxYhjgGxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxYhxxbDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ZnsbMhqxYhjgGxbMapper extends BaseMapperX<ZnsbMhqxYhjgGxbDO> {


    default Integer deleteByYhuuid(List<String> yhuuids){
        QueryWrapperX<ZnsbMhqxYhjgGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .in(ZnsbMhqxYhjgGxbDO::getYhUuid,yhuuids);
        return delete(wrapperX);
    }


    default List<ZnsbMhqxYhjgGxbDO> selectAllJgByYhuuid(String yhuuid){
        QueryWrapperX<ZnsbMhqxYhjgGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .select(ZnsbMhqxYhjgGxbDO::getZzuuid,ZnsbMhqxYhjgGxbDO::getJguuid1,ZnsbMhqxYhjgGxbDO::getYhUuid)
                .eq(ZnsbMhqxYhjgGxbDO::getYhUuid,yhuuid);
        return selectList(wrapperX);
    }

    default List<ZnsbMhqxYhjgGxbDO> selectAllJgByYhZz(String yhuuid,String zzuuid){
        QueryWrapperX<ZnsbMhqxYhjgGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .select(ZnsbMhqxYhjgGxbDO::getZzuuid,ZnsbMhqxYhjgGxbDO::getJguuid1,ZnsbMhqxYhjgGxbDO::getYhUuid)
                .eq(ZnsbMhqxYhjgGxbDO::getYhUuid,yhuuid)
                .eq(ZnsbMhqxYhjgGxbDO::getZzuuid,zzuuid);
        return selectList(wrapperX);
    }

    default Integer deleteByuuid(String zzuuid,String jguuid,String yhuuid){
        QueryWrapperX<ZnsbMhqxYhjgGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .eq(ZnsbMhqxYhjgGxbDO::getZzuuid,zzuuid)
                .eq(!GyUtils.isNull(jguuid),ZnsbMhqxYhjgGxbDO::getJguuid1,jguuid)
                .eq(ZnsbMhqxYhjgGxbDO::getYhUuid,yhuuid);
        return delete(wrapperX);
    }

    default Integer deleteByZzuuids(List<String> zzuuids){
        if (GyUtils.isNull(zzuuids)){
            return 0;
        }
        QueryWrapperX<ZnsbMhqxYhjgGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .in(ZnsbMhqxYhjgGxbDO::getZzuuid,zzuuids);
        return delete(wrapperX);
    }

    default List<ZnsbMhqxYhjgGxbDO> getczrByQy(String jguuid1) {
        QueryWrapperX<ZnsbMhqxYhjgGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().select(ZnsbMhqxYhjgGxbDO::getYhUuid).eq(ZnsbMhqxYhjgGxbDO::getJguuid1, jguuid1);
        return selectList(wrapperX);
    }

    List<ZnsbMhqxYhxxbDO> getBsyByQy(String qyuuid);

    List<ZnsbMhqxYhxxbDO> getBsyByNsrsbh(String nsrsbh);

    default List<ZnsbMhqxYhjgGxbDO> getByYhAndZzAndQy(String yhuuid,String zzuuid,String qyuuid){
        QueryWrapperX<ZnsbMhqxYhjgGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().eq(ZnsbMhqxYhjgGxbDO::getZzuuid,zzuuid)
                .eq(ZnsbMhqxYhjgGxbDO::getYhUuid,yhuuid)
                .eq(ZnsbMhqxYhjgGxbDO::getJguuid1,qyuuid);
        return selectList(wrapperX);
    }

}




