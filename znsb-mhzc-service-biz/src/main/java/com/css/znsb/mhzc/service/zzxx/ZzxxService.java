package com.css.znsb.mhzc.service.zzxx;

import com.baomidou.mybatisplus.extension.service.IService;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxZzxxbDO;
import com.css.znsb.mhzc.pojo.saas.SaasZzxxVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.AllZzxxVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.SwitchZzVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxBaseVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxVO;
import java.util.List;
import java.util.Map;

public interface ZzxxService extends IService<ZnsbMhqxZzxxbDO> {

    List<ZzxxVO> getAllZz(AllZzxxVO allZzxxVO);

    List<ZzxxVO> getAllZzOfCurrentUser();

    Integer insert(ZzxxVO zzxxVO);

    String generateCcm(String sjzzuuid);

    Integer update(ZzxxVO zzxxVO);

    Integer delete(ZzxxBaseVO baseVO);

    List<SwitchZzVO> switchZzList();

    List<String> getGLZzid();

    ZzxxVO getZzxxbyId(String zzuuid);

    String getZzuuidByMc(String zzmc);

    Map<String, List<ZnsbMhqxZzxxbDO>> getZzidByMc(List<String> zzmcList);

    List<SaasZzxxVO> switchSaasZzList();

    CommonResult<String> bindYhQyAuto(String zzuuid, String qyuuid, String yhuuid);
}
