package com.css.znsb.mhzc.mapper.gxb;


import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.QueryWrapperX;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxZzyhGxbDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ZnsbMhqxZzyhGxbMapper extends BaseMapperX<ZnsbMhqxZzyhGxbDO> {


    default List<ZnsbMhqxZzyhGxbDO> selectYhByZzuuids(List<String> zzuuids){
        QueryWrapperX<ZnsbMhqxZzyhGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .select(ZnsbMhqxZzyhGxbDO::getZzuuid,ZnsbMhqxZzyhGxbDO::getYhUuid)
                .in(ZnsbMhqxZzyhGxbDO::getZzuuid,zzuuids)
                .orderByDesc(ZnsbMhqxZzyhGxbDO::getLrrq);
        return selectList(wrapperX);
    }


    default Integer deleteByuuid(String zzuuid,String yhuuid){
        QueryWrapperX<ZnsbMhqxZzyhGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().eq(ZnsbMhqxZzyhGxbDO::getZzuuid,zzuuid)
                .eq(ZnsbMhqxZzyhGxbDO::getYhUuid,yhuuid);
        return delete(wrapperX);
    }

    default Integer deleteByYhuuid(List<String> yhuuids){
        QueryWrapperX<ZnsbMhqxZzyhGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().in(ZnsbMhqxZzyhGxbDO::getYhUuid,yhuuids);
        return delete(wrapperX);
    }


    default Integer deleteByZzuuids(List<String> zzuuids){
        if (GyUtils.isNull(zzuuids)){
            return 0;
        }
        QueryWrapperX<ZnsbMhqxZzyhGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().in(ZnsbMhqxZzyhGxbDO::getZzuuid,zzuuids);
        return delete(wrapperX);
    }

    default List<ZnsbMhqxZzyhGxbDO> selectZzuuidByYhuuid(String yhuuid){
        QueryWrapperX<ZnsbMhqxZzyhGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .select(ZnsbMhqxZzyhGxbDO::getZzuuid,ZnsbMhqxZzyhGxbDO::getYhUuid)
                .eq(ZnsbMhqxZzyhGxbDO::getYhUuid,yhuuid);
        return selectList(wrapperX);
    }

    default List<ZnsbMhqxZzyhGxbDO> getByZzAndYh(String zzuuid,String yhuuid){
        QueryWrapperX<ZnsbMhqxZzyhGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().eq(ZnsbMhqxZzyhGxbDO::getZzuuid,zzuuid)
                .eq(ZnsbMhqxZzyhGxbDO::getYhUuid,yhuuid);
        return selectList(wrapperX);
    }

}




