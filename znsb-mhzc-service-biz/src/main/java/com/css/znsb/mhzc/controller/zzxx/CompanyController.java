package com.css.znsb.mhzc.controller.zzxx;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.pojo.company.*;
import com.css.znsb.mhzc.pojo.dto.nsrxx.SaveZgxxReqVO;
import com.css.znsb.mhzc.pojo.vo.company.*;
import com.css.znsb.mhzc.pojo.vo.util.ExcelUtilVo;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxBaseVO;
import com.css.znsb.mhzc.pojo.yhxx.RyxxVO;
import com.css.znsb.mhzc.service.nsrxx.IZnsbMhzcNsrzgxxService;
import com.css.znsb.mhzc.service.nsrxx.IZnsbMhzcQybqxxService;
import com.css.znsb.mhzc.service.nsrxx.IZnsbMhzcQyjbxxmxService;
import com.css.znsb.mhzc.service.nsrxx.IZnsbMhzcSfzrdxxService;
import com.css.znsb.mhzc.service.zzxx.CompanyService;
import com.css.znsb.mhzc.util.MhzcGyUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Tag(name = "组织与机构关系维护接口")
@RestController
@RequestMapping("/zzjg")
@Validated
@Slf4j
public class CompanyController {

    @Resource
    private CompanyService companyService;

    @Resource
    private IZnsbMhzcQyjbxxmxService qyjbxxmxService;

    @Resource
    private IZnsbMhzcNsrzgxxService nsrzgxxService;

    @Resource
    private IZnsbMhzcSfzrdxxService sfzrdxxService;

    @Resource
    private IZnsbMhzcQybqxxService qybqxxService;


    @PostMapping("/getAllJgOfZz")
    @Operation(summary = "获取组织下全部机构")
    public CommonResult<List<CompanyResVO>> getAllJgOfZz(@RequestBody ZzxxBaseVO reqVO){
        return CommonResult.success(companyService.getAllJgOfZz(reqVO));
    }

    @PostMapping("/getAllJgxx")
    @Operation(summary = "获取全部机构",description = "参数模糊匹配")
    public CommonResult<List<JgxxVO>> getAllJgxx(@RequestBody AllCompanyReqVO reqVO){
        return CommonResult.success(companyService.getAllJgxx(reqVO));
    }

    @PostMapping("/getAllDjxh")
    @Operation(summary = "获取所有登记序号")
    public CommonResult<List<String>> getAllDjxh(){
        return CommonResult.success(companyService.getAllDjxh());
    }

    @PostMapping("/getAllNsrsbh")
    @Operation(summary = "获取所有纳税人识别号")
    public CommonResult<List<String>> getAllNsrsbh(){
        return CommonResult.success(companyService.getAllNsrsbh());
    }

    @PostMapping("/getJguuidsOfzz")
    @Operation(summary = "获取组织已绑定的机构ID列表")
    public CommonResult<List<String>> getJgidsOfZz(@RequestBody ZzxxBaseVO baseVO){
        return CommonResult.success(companyService.getJgidsOfZz(baseVO));
    }

    @PostMapping("/insert")
    @Operation(summary = "绑定组织与机构",description = "批量新增一个组织与若干机构的绑定关系")
    public CommonResult<Boolean> insert(@RequestBody CompanyBindVO reqVO){
        return CommonResult.success(companyService.insert(reqVO));
    }

    @PostMapping("/delete")
    @Operation(summary = "解绑组织与机构",description = "批量解除一个组织与若干机构的绑定关系")
    public CommonResult<Boolean> delete(@RequestBody CompanyBindVO reqVO){
        return CommonResult.success(companyService.delete(reqVO));
    }

    @PostMapping("/qhzz")
    @Operation(summary = "切换组织",description = "切换组织同时切换机关，当组织下没有机关时，机关信息相关字段返回为空")
    public CommonResult<SwitchZzResVO> switchZz(@RequestBody SwitchZzReqVO reqVO){
        return CommonResult.success(companyService.switchZz(reqVO));
    }

    @PostMapping("/qhjg")
    @Operation(summary = "切换机构")
    public CommonResult<SwitchJgResVO> switchJg(@RequestBody SwitchJgReqVO reqVO){
        return CommonResult.success(companyService.switchJg(reqVO));
    }

    @PostMapping("/bindJgxx")
    @Operation(summary = "绑定与解绑机构与组织关系",description = "批量操作")
    public CommonResult<Boolean> bindJgxx(@RequestBody CompanyBindBatchVO bindBatchVO){
        return CommonResult.success(companyService.bindJgxx(bindBatchVO));
    }

    @PostMapping("/getAllCompanyInfo")
    @Operation(summary = "获取全部企业信息",description = "RPC接口")
    public CommonResult<List<CompanyInfoResDTO>> getAllCompanyInfo(){
        return CommonResult.success(companyService.getAllCompanyInfo());
    }

    @PostMapping("/getAllCompanyInfoWithStatus")
    @Operation(summary = "获取全部企业信息及可操作状态")
    public CommonResult<List<CompanyInfoWithStatusDTO>> getAllCompanyInfoWithStatus(@RequestBody CompanyInfoWithStatusReqDTO reqDTO){
        return CommonResult.success(companyService.getAllCompanyInfoWithStatus(reqDTO.getYhuuid(),reqDTO.getZzuuid()));
    }

    @PostMapping("/switchJgList")
    @Operation(summary = "当前用户所选择组织下可切换的机构列表")
    public CommonResult<List<SwitchJgVO>> switchJgList(){
        return CommonResult.success(companyService.switchJgList());
    }

    @PostMapping("/switchKqJgList")
    @Operation(summary = "当前用户所选择组织下可切换的跨区机构列表")
    public CommonResult<List<SwitchJgVO>> switchKqJgList(){
        return CommonResult.success(companyService.switchKqJgList());
    }

    @PostMapping("/bindedJgList")
    @Operation(summary = "当前用户所选择组织下已绑定的机构信息列表")
    public CommonResult<List<BindedJgVO>> bindedJgList(){
        return CommonResult.success(companyService.bindedJgList());
    }


    @PostMapping("/basicInfo")
    @Operation(summary = "获取企业基本信息",description = "RPC接口")
    public CommonResult<CompanyBasicInfoDTO> basicInfo(@RequestParam("djxh")String djxh, @RequestParam("nsrsbh")String nsrsbh){
        return CommonResult.success(companyService.basicInfo(djxh,nsrsbh));
    }

    @PostMapping("/getZjgBasicInfoForSenmir")
    @Operation(summary = "获取总机构企业基本信息（森马适用）", description = "RPC接口")
    public CommonResult<CompanyBasicInfoDTO> getZjgBasicInfoForSenmir(@RequestParam("djxh")String djxh, @RequestParam("nsrsbh")String nsrsbh) {
        return CommonResult.success(companyService.getZjgBasicInfoForSenmir(djxh, nsrsbh));
    }

    @PostMapping("/getjgxxsByNsrsbh")
    @Operation(summary = "获取企业基本信息",description = "RPC接口")
    public CommonResult<List<CompanyDTO>> getjgxxsByNsrsbh(@RequestParam("nsrsbh")String nsrsbh){
        return CommonResult.success(companyService.jgxxsByNsrsbh(nsrsbh));
    }

    @PostMapping("/glqymx")
    @Operation(summary = "查询管理企业明细")
    public CommonResult<List<GlqymxResVO>> glqymx(@RequestBody GlqymxReqVO reqVO){
        return CommonResult.success(qyjbxxmxService.glqymx(reqVO));
    }

    @PostMapping("/glqysfzrdxx")
    @Operation(summary = "查询管理企业税费种认定信息")
    public CommonResult<List<GlqySfzrdxxResVO>> glqySfzrdxx(@RequestBody SfzrdReqVO sfzrdReqVO){
        return CommonResult.success(sfzrdxxService.glqySfzrdxx(sfzrdReqVO));
    }

    @PostMapping("/getZsxmList")
    @Operation(summary = "获取征收项目下拉框列表")
    public CommonResult<List<ZsxmResVO>> getZsxmList(@RequestBody SfzrdReqVO sfzrdReqVO){
        return CommonResult.success(sfzrdxxService.getZsxmList(sfzrdReqVO));
    }

    @PostMapping("/glqynsrxx")
    @Operation(summary = "查询管理企业纳税人基础信息")
    public CommonResult<GlqyNsrjbxxResVO> glqyNsrxx(@RequestParam(value = "djxh",required = false)String djxh){
        return CommonResult.success(qyjbxxmxService.glqyNsrxx(djxh));
    }

    @PostMapping("/glqynsrzgxx")
    @Operation(summary = "查询管理企业纳税人资格信息")
    public CommonResult<List<GlqyNsrzgxxResVO>> glqyNsrzgxx(@RequestBody NsrzgReqVO nsrzgReqVO){
        return CommonResult.success(nsrzgxxService.glqyNsrzgxx(nsrzgReqVO));
    }

    @PostMapping("/glqyNsrzgxxKz")
    @Operation(summary = "查询管理企业纳税人资格信息")
    public CommonResult<List<GlqyNsrzgxxResVO>> glqyNsrzgxxKz(@RequestBody NsrzgReqVO nsrzgReqVO){
        return CommonResult.success(nsrzgxxService.glqyNsrzgxxKz(nsrzgReqVO));
    }

    @PostMapping("/addZgxx")
    @Operation(summary = "新增资格信息")
    public CommonResult<String> addZgxx(@RequestBody SaveZgxxReqVO nsrzgxxKzDTO){
        nsrzgxxService.addZgxx(nsrzgxxKzDTO);
        return CommonResult.success("ok");
    }

    @GetMapping("/delZgxx")
    @Operation(summary = "新增资格信息")
    public CommonResult<String> delZgxx(@RequestParam("uuid") List<String> uuid){
        nsrzgxxService.delZgxx(uuid);
        return CommonResult.success("ok");
    }

    @PostMapping("/glqyqybqxx")
    @Operation(summary = "查询管理企业标签信息")
    public CommonResult<List<GlqyNsrbqxxResVO>> glqyQybqxx(@RequestParam(value = "djxh",required = false)String djxh){
        return CommonResult.success(qybqxxService.glqyQybqxx(djxh));
    }

    @PostMapping("/getUuidsByDjxhs")
    @Operation(summary = "通过登记序号集合获取机构uuid集合",description = "RPC接口")
    public CommonResult<List<String>> getUuidsByDjxhs(@RequestParam("djxhs")List<String> djxhs){
        return CommonResult.success(companyService.getUuidsByDjxhs(djxhs));
    }

    @PostMapping("/getJgxxByDjxhs")
    @Operation(summary = "通过登记序号集合获取企业基本信息集合",description = "RPC接口")
    public CommonResult<List<CompanyBasicInfoDTO>> getJgxxByDjxhs(@RequestParam("djxhs")List<String> djxhs){
        return CommonResult.success(companyService.getJgxxByDjxhs(djxhs));
    }

    @PostMapping("/qyxxgl")
    @Operation(summary = "查询同步数据企业")
    public CommonResult<Page<QyxxglResVO>> qyxxgl(@RequestBody QyxxglReqVO reqVO){
        return CommonResult.success(companyService.qyxxgl(reqVO));
    }

    @PostMapping("/addQyxx")
    @Operation(summary = "新增企业信息")
    public CommonResult<String> addQyxx(@RequestBody QyxxglReqVO reqVO){
        if (GyUtils.isNull(reqVO.getJguuid1())){
            List<QyxxglReqVO> list = new ArrayList<>();
            list.add(reqVO);
            companyService.addQyxx(list);
        }else {
            companyService.updQyxx(reqVO);
        }

        return CommonResult.success("success");
    }

    @PostMapping("/deleteQyByUuid")
    @Operation(summary = "通过uuid删除企业信息")
    public CommonResult<Integer> deleteQyByUuid(@RequestBody QyxxglReqVO reqVO){
        return CommonResult.success(companyService.deleteQyByUuid(reqVO));
    }

    @PostMapping("/getBjCompanyInfo")
    @Operation(summary = "获取本级企业信息")
    public CommonResult<List<CompanyInfoResDTO>> getBjCompanyInfo(@RequestBody AllCompanyReqVO reqVO){
        return CommonResult.success(companyService.getBjCompanyInfo(reqVO));
    }

    @PostMapping("/addQyxxExportExcel")
    @Operation(summary = "门户数据批量添加模板导出")
    public void addmhsjExportExcel(HttpServletResponse response) throws IOException {

        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("excelMb/智能申报企业信息初始化模板.xlsx");
        byte[] bs = new byte[1024];
        int read;
        while ((read = inputStream.read(bs))!=-1){
            response.getOutputStream().write(bs,0,read);
        }
        response.flushBuffer();
        inputStream.close();
    }

    @PostMapping("/addQyxxImportExcel")
    @Operation(summary = "门户数据批量添加模板导入")
    public CommonResult<String> input(@RequestBody MultipartFile file){
        companyService.addQyxxImportExcel(file);
        return CommonResult.success("success");
    }

    @PostMapping("/countGlqy")
    @Operation(summary = "查询管理企业标签信息")
    public CommonResult<CountGlqyVO> countGlqy() {
        return CommonResult.success(qyjbxxmxService.countGlqy());
    }

    @PostMapping("/getBsyByQy")
    @Operation(summary = "查询企业的所有办税员")
    public CommonResult<List<RyxxVO>> getBsyByQy(@RequestBody DjxhReqVO reqVO) {
        return CommonResult.success(companyService.getBsyByQy(reqVO));
    }

    @PostMapping("/getJgxxListByQydmz")
    @Operation(summary = "根据企业代码值前两位获取机构数量")
    public CommonResult<Integer> getJgxxListByQydmz(@RequestParam("qydmz") String qydmz) {
        return companyService.getJgxxListByQydmz(qydmz);
    }

    @GetMapping("/getKczDjxhList")
    @Operation(summary = "查询管理企业标签信息")
    public CommonResult<List<String>> getKczDjxhList(@RequestParam("token") String token) {
        return CommonResult.success(companyService.getKczDjxhList(token));
    }


    @PostMapping("/exportQyjbxx")
    @Operation(summary = "导出企业基本信息")
    public void exportQyjbxx(HttpServletResponse response, @RequestBody List<GlqymxResVO> glqymxs) throws IOException {
        if (GyUtils.isNotNull(glqymxs)) {
            List<GlqyjbxxExcelMbVO> list = BeanUtils.toBean(glqymxs, GlqyjbxxExcelMbVO.class);
            int index = 1;
            for (GlqyjbxxExcelMbVO item : list) {
                String nsrlxMc = dm2Mc(item.getNsrlx(), "qyzglx");
                item.setNsrlx(nsrlxMc);
                item.setXh(index++);
            }
            // 设置响应内容类型和文件名
            response.setContentType("application/vnd.ms-excel");
            //构建excel
            List<ExcelUtilVo> excelUtilsVoList = new ArrayList<>();
            ExcelUtilVo nsrjbxxExcelVo = new ExcelUtilVo();
            nsrjbxxExcelVo.setClazz(GlqyjbxxExcelMbVO.class);
            nsrjbxxExcelVo.setList(list);
            nsrjbxxExcelVo.setSheetName("企业基本信息");
            excelUtilsVoList.add(nsrjbxxExcelVo);
            MhzcGyUtils.exportExcelByMb(response,excelUtilsVoList,"企业基本信息","excelMb/企业基本信息.xlsx");
//            // 设置响应内容类型和文件名
//            response.setContentType("application/vnd.ms-excel");
//            QyxxglExcelColumnWidthStyle style = new QyxxglExcelColumnWidthStyle(40);
//            EasyExcel.write(response.getOutputStream(), GlqyjbxxExcelMbVO.class)
//                    .registerWriteHandler(style)
//                    .excelType(ExcelTypeEnum.XLS).sheet("企业基本信息").doWrite(list);
            // 设置文件名
//            response.setContentType("application/octet-stream");
//            // 设置中文文件名，对文件名进行编码
//            String chineseFileName = "企业基本信息.xls";
//            String encodedFileName = "attachment; filename*=" + java.net.URLEncoder.encode(chineseFileName, "UTF-8");
//            response.setHeader("Content-Disposition", encodedFileName);
//            response.flushBuffer();
        }
    }
    @PostMapping("/exportQyxqxx")
    @Operation(summary = "导出企业详情信息")
    public void exportQyxqxx(HttpServletResponse response,@RequestBody List<GlqymxResVO> glqymxs) throws IOException {
        List<GlqyNsrjbxxExcelMbVO> nsrjbxxList = new ArrayList<>();
        List<GlqySfzrdxxExcelMbVO> sfzrdList = new ArrayList<>();
        List<GlqyNsrzgxxExcelMbVO> nsrzgxxList = new ArrayList<>();
        List<GlqyNsrbqxxExcelMbVO> nsrbqxxList = new ArrayList<>();
        int nsrjbxxIndex = 1;
        int sfzrdIndex = 1;
        int nsrzgxxIndex = 1;
        int nsrbqxxIndex = 1;
        for(GlqymxResVO jbxx : glqymxs){
            //纳税人基本信息
            GlqyNsrjbxxResVO nsrjbxx = qyjbxxmxService.glqyNsrxx(jbxx.getDjxh());
            //代码转换
            if(GyUtils.isNull(nsrjbxx)){
                throw ServiceExceptionUtil.exception(500, "纳税人基本信息不能为空");
            }
            GlqyNsrjbxxExcelMbVO glqyNsrjbxxExcelMbVO = BeanUtils.toBean(nsrjbxx,GlqyNsrjbxxExcelMbVO.class);
            glqyNsrjbxxExcelMbVO.setXh(nsrjbxxIndex++);
            glqyNsrjbxxExcelMbVO.setQydmz(jbxx.getQydmz());
            glqyNsrjbxxExcelMbVO.setZfjglxmc(dm2Mc(nsrjbxx.getZfjglxDm(),"zfjglx"));
            glqyNsrjbxxExcelMbVO.setKdqfzfzjgbz(dm2Mc(nsrjbxx.getKdqfzfzjgbz(),"yOrN"));
            glqyNsrjbxxExcelMbVO.setKqccsztdjbz(dm2Mc(nsrjbxx.getKqccsztdjbz(),"yOrN"));
            glqyNsrjbxxExcelMbVO.setFjmqybz(dm2Mc(nsrjbxx.getFjmqybz(),"yOrN"));
            nsrjbxxList.add(glqyNsrjbxxExcelMbVO);
            //税费种认定信息
            SfzrdReqVO sfzrdReqVO = new SfzrdReqVO();
            sfzrdReqVO.setDjxh(jbxx.getDjxh());
            List<GlqySfzrdxxResVO> sfzrdxxResVOList = sfzrdxxService.glqySfzrdxx(sfzrdReqVO);
            if(GyUtils.isNotNull(sfzrdxxResVOList)){
                for(GlqySfzrdxxResVO sfzrdxxResVO: sfzrdxxResVOList){
                    GlqySfzrdxxExcelMbVO sfzrdxxExcelMbVO = BeanUtils.toBean(sfzrdxxResVO,GlqySfzrdxxExcelMbVO.class);
                    sfzrdxxExcelMbVO.setXh(sfzrdIndex++);
                    sfzrdxxExcelMbVO.setQydmz(jbxx.getQydmz());
                    sfzrdxxExcelMbVO.setNsrsbh(jbxx.getNsrsbh());
                    sfzrdxxExcelMbVO.setNsrmc(jbxx.getNsrmc());
                    if(GyUtils.isNotNull(sfzrdxxResVO.getRdyxqq())){
                        sfzrdxxExcelMbVO.setRdyxqq(JsonUtils.toJson(sfzrdxxResVO.getRdyxqq()).substring(1,11));
                    }
                    if(GyUtils.isNotNull(sfzrdxxResVO.getRdyxqz())){
                        sfzrdxxExcelMbVO.setRdyxqz(JsonUtils.toJson(sfzrdxxResVO.getRdyxqz()).substring(1,11));
                    }
                    sfzrdList.add(sfzrdxxExcelMbVO);
                }
            }
            //企业资格信息
            NsrzgReqVO nsrzgReqVO = new NsrzgReqVO();
            nsrzgReqVO.setDjxh(jbxx.getDjxh());
            List<GlqyNsrzgxxResVO> glqyNsrzgxx = nsrzgxxService.glqyNsrzgxx(nsrzgReqVO);
            if(GyUtils.isNotNull(glqyNsrzgxx)){
                for(GlqyNsrzgxxResVO glqyNsrzgxxResVO:glqyNsrzgxx){
                    GlqyNsrzgxxExcelMbVO glqyNsrzgxxExcelMbVO = BeanUtils.toBean(glqyNsrzgxxResVO,GlqyNsrzgxxExcelMbVO.class);
                    glqyNsrzgxxExcelMbVO.setXh(nsrzgxxIndex++);
                    glqyNsrzgxxExcelMbVO.setQydmz(jbxx.getQydmz());
                    glqyNsrzgxxExcelMbVO.setNsrsbh(jbxx.getNsrsbh());
                    glqyNsrzgxxExcelMbVO.setNsrmc(jbxx.getNsrmc());
                    glqyNsrzgxxExcelMbVO.setNsrlx(dm2Mc(glqyNsrzgxxExcelMbVO.getNsrlx(),"qyzglx"));
                    if(GyUtils.isNotNull(glqyNsrzgxxResVO.getYxqq())){
                        glqyNsrzgxxExcelMbVO.setYxqq(JsonUtils.toJson(glqyNsrzgxxResVO.getYxqq()).substring(1,11));
                    }
                    if(GyUtils.isNotNull(glqyNsrzgxxResVO.getYxqz())){
                        glqyNsrzgxxExcelMbVO.setYxqz(JsonUtils.toJson(glqyNsrzgxxResVO.getYxqz()).substring(1,11));
                    }
                    nsrzgxxList.add(glqyNsrzgxxExcelMbVO);
                }
            }
            //企业标签信息
            List<GlqyNsrbqxxResVO> glqyQybqxx = qybqxxService.glqyQybqxx(jbxx.getDjxh());
            if(GyUtils.isNotNull(glqyQybqxx)){
                for(GlqyNsrbqxxResVO glqyNsrbqxxResVO:glqyQybqxx){
                    GlqyNsrbqxxExcelMbVO glqyNsrbqxxExcelMbVO = BeanUtils.toBean(glqyNsrbqxxResVO,GlqyNsrbqxxExcelMbVO.class);
                    glqyNsrbqxxExcelMbVO.setXh(nsrbqxxIndex++);
                    glqyNsrbqxxExcelMbVO.setQydmz(jbxx.getQydmz());
                    glqyNsrbqxxExcelMbVO.setNsrsbh(jbxx.getNsrsbh());
                    glqyNsrbqxxExcelMbVO.setNsrmc(jbxx.getNsrmc());
                    if(GyUtils.isNotNull(glqyNsrbqxxResVO.getYxqq())){
                        glqyNsrbqxxExcelMbVO.setYxqq(JsonUtils.toJson(glqyNsrbqxxResVO.getYxqq()).substring(1,11));
                    }
                    if(GyUtils.isNotNull(glqyNsrbqxxResVO.getYxqz())){
                        glqyNsrbqxxExcelMbVO.setYxqz(JsonUtils.toJson(glqyNsrbqxxResVO.getYxqz()).substring(1,11));
                    }
                    nsrbqxxList.add(glqyNsrbqxxExcelMbVO);
                }
            }
        }
        // 设置响应内容类型和文件名
        response.setContentType("application/vnd.ms-excel");
        //构建excel
        List<ExcelUtilVo> excelUtilsVoList = new ArrayList<>();
        ExcelUtilVo nsrjbxxExcelVo = new ExcelUtilVo();
        nsrjbxxExcelVo.setClazz(GlqyNsrjbxxExcelMbVO.class);
        nsrjbxxExcelVo.setList(nsrjbxxList);
        nsrjbxxExcelVo.setSheetName("纳税人基本信息");
        excelUtilsVoList.add(nsrjbxxExcelVo);
        ExcelUtilVo sfzrdExcelVo = new ExcelUtilVo();
        sfzrdExcelVo.setClazz(GlqySfzrdxxExcelMbVO.class);
        sfzrdExcelVo.setList(sfzrdList);
        sfzrdExcelVo.setSheetName("税费种认定信息");
        excelUtilsVoList.add(sfzrdExcelVo);
        ExcelUtilVo nsrzgxxExcelVo = new ExcelUtilVo();
        nsrzgxxExcelVo.setClazz(GlqyNsrzgxxExcelMbVO.class);
        nsrzgxxExcelVo.setList(nsrzgxxList);
        nsrzgxxExcelVo.setSheetName("企业资格信息");
        excelUtilsVoList.add(nsrzgxxExcelVo);
        ExcelUtilVo nsrbqxxExcelVo = new ExcelUtilVo();
        nsrbqxxExcelVo.setClazz(GlqyNsrbqxxExcelMbVO.class);
        nsrbqxxExcelVo.setList(nsrbqxxList);
        nsrbqxxExcelVo.setSheetName("企业标签信息");
        excelUtilsVoList.add(nsrbqxxExcelVo);
//        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("excelMb/企业详情信息.xlsx");
        MhzcGyUtils.exportExcelByMb(response,excelUtilsVoList,"企业详情信息","excelMb/企业详情信息.xlsx");
    }

    @PostMapping("/initJgxx")
    @Operation(summary = "初始化机构信息")
    CommonResult<String> initJgxx(@RequestBody CompanyDTO companyDTO){
        companyService.initJgxx(companyDTO);
        return CommonResult.success("success");
    }

    @PostMapping("/initJgxxNew")
    @Operation(summary = "初始化机构信息")
    CommonResult<String> initJgxxNew(@RequestBody CompanyDTO companyDTO){
        return companyService.initJgxxNew(companyDTO);
    }

    private String dm2Mc(String dm,String type) {
        if(GyUtils.isNull(dm)){
            return "";
        }
        String value = "";
        if("yOrN".equals(type)){
            switch (dm) {
                case "Y":
                    value = "是";
                    break;
                case "N":
                    value = "否";
                    break;
                default:
                    break;
            }
        }
        if("zfjglx".equals(type)){
            switch (dm) {
                case "0":
                    value = "非总分机构";
                    break;
                case "1":
                    value = "总机构";
                    break;
                case "2":
                    value = "分支机构";
                    break;
                case "3":
                    value = "分总机构";
                    break;
                default:
                    break;
            }
        }
        if("qyzglx".equals(type)){
            switch (dm) {
                case "1":
                    value = "一般纳税人";
                    break;
                case "2":
                    value = "小规模纳税人";
                    break;
                case "3":
                    value = "辅导期一般纳税人";
                    break;
                case "4":
                    value = "其它";
                    break;
                default:
                    break;
            }
        }
        return value;
    }
//    @PostMapping("/test")
//    @Operation(summary = "发送消息")
//    public void test() {
//        Map map = new HashMap<>();
//        map.put("nsrmc","AA");
//        map.put("zgbgrq","2024年11月5日");
//        map.put("oldZglx","old");
//        map.put("newZglx","new");
//        List<String> jgList = new ArrayList<>();
//        jgList.add("5f5dd426d8ce411db5ccfb99a2491231");
//        CommonResult result = xxService.sendXx("11", JsonUtils.toJson(map),new ArrayList<>(),jgList,new ArrayList<>());
//        log.info("",result);
//    }
//    @Resource
//    private MhMainJob mhMainJob;
//    @PostMapping("/test")
//    @Operation(summary = "测试")
//    public CommonResult<String> test(){
//        mhMainJob.mainWork();
//        return CommonResult.success("");
//    }
}
