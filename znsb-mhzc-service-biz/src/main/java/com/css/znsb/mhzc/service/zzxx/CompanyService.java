package com.css.znsb.mhzc.service.zzxx;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.mhzc.pojo.company.*;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxYhjgGxbDO;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxZzjgGxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxJgxxbDO;
import com.css.znsb.mhzc.pojo.vo.company.*;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxBaseVO;
import com.css.znsb.mhzc.pojo.yhxx.RyxxVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface CompanyService extends IService<ZnsbMhqxJgxxbDO> {

    List<CompanyResVO> getAllJgOfZz(ZzxxBaseVO reqVO);

    List<ZnsbMhqxJgxxbDO> getJgxxByNsrsbh(String nsrsbh);

    List<String> getJgidsOfZz(ZzxxBaseVO reqVO);

    Boolean delete(CompanyBindVO reqVO);

    List<JgxxVO> getAllJgxx(AllCompanyReqVO reqVO);

    Boolean insert(CompanyBindVO reqVO);

    SwitchZzResVO switchZz(SwitchZzReqVO reqVO);


    SwitchJgResVO switchJg(SwitchJgReqVO reqVO);

    Boolean bindJgxx(CompanyBindBatchVO bindBatchVO);

    List<CompanyInfoResDTO> getAllCompanyInfo();

    List<CompanyInfoWithStatusDTO> getAllCompanyInfoWithStatus(String yhuuid,String zzuuid);

    List<SwitchJgVO> switchJgList();

    List<SwitchJgVO> switchKqJgList();

    List<BindedJgVO> bindedJgList();

    CompanyBasicInfoDTO basicInfo(String djxh, String nsrsbh);

    CompanyBasicInfoDTO getZjgBasicInfoForSenmir(String djxh, String nsrsbh);

    List<String> getUuidsByDjxhs(List<String> djxhs);

    Page<QyxxglResVO> qyxxgl(QyxxglReqVO reqVO);

    int deleteQyByUuid(QyxxglReqVO reqVO);

    List<CompanyInfoResDTO> getBjCompanyInfo(AllCompanyReqVO reqVO);

    void addQyxx(List<QyxxglReqVO> list);

    Map<String, List<ZnsbMhqxJgxxbDO>> getJgidByMc(List<String> list);

    void zzjgBatch(List<ZnsbMhqxZzjgGxbDO> zzjgs);

    void yhjgBatch(List<ZnsbMhqxYhjgGxbDO> yhjgs);

    void updQyxx(QyxxglReqVO reqVO);

    void addQyxxImportExcel(MultipartFile file);

    ZnsbMhqxJgxxbDO getJgxxByJguuid(String jguuid);

    String getJgxxByQydmz(String qydmz);

    void copyQyxx(CopyQyxxVO qyxxVO);

    List<RyxxVO> getBsyByQy(DjxhReqVO reqVO);

    List<String> getAllDjxh();

    void refreshZszzbz2();

    List<ZnsbMhqxJgxxbDO> selectZnsbMhqxJgxxb();

    void updateBatchByDjxh(List<ZnsbMhqxJgxxbDO> updJgxxByDjxhList);

    List<CompanyDTO> jgxxsByNsrsbh(String nsrsbh);

    List<String> getAllNsrsbh();

    void initJgxx(CompanyDTO companyDTO);

    CommonResult<Integer> getJgxxListByQydmz(String qydmz);

    List<String> getKczDjxhList(String token);

    List<CompanyBasicInfoDTO> getJgxxByDjxhs(List<String> djxhs);

    CommonResult<String> initJgxxNew(CompanyDTO companyDTO);
}
