package com.css.znsb.mhzc.service.yhxx.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.nacos.shaded.com.google.gson.internal.LinkedTreeMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.exception.util.ServiceExceptionUtil;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.monitor.TracerUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.servlet.ServletUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.redis.utils.RedisUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.mhzc.constants.ErrorCodeEnum;
import com.css.znsb.mhzc.constants.MhzcConstants;
import com.css.znsb.mhzc.mapper.gxb.ZnsbMhqxJsqxGxbMapper;
import com.css.znsb.mhzc.mapper.gxb.ZnsbMhqxYhjgGxbMapper;
import com.css.znsb.mhzc.mapper.gxb.ZnsbMhqxYhjsGxbMapper;
import com.css.znsb.mhzc.mapper.gxb.ZnsbMhqxZzyhGxbMapper;
import com.css.znsb.mhzc.mapper.nsrxx.LqyqGlhtUserMapper;
import com.css.znsb.mhzc.mapper.xxb.*;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxJsqxGxbDO;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxYhjgGxbDO;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxYhjsGxbDO;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxZzyhGxbDO;
import com.css.znsb.mhzc.pojo.domain.piaoshui.LqyqGlhtUserDO;
import com.css.znsb.mhzc.pojo.domain.xxb.*;
import com.css.znsb.mhzc.pojo.dto.common.ResponseDTO;
import com.css.znsb.mhzc.pojo.dto.permission.RoleIncludePermissionDTO;
import com.css.znsb.mhzc.pojo.vo.user.*;
import com.css.znsb.mhzc.pojo.vo.user.tygj.TygjCommonResult;
import com.css.znsb.mhzc.pojo.vo.user.tygj.TygjUserReqVO;
import com.css.znsb.mhzc.pojo.vo.user.wlj.*;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxBaseVO;
import com.css.znsb.mhzc.pojo.yhxx.UserInfoByIdsReqVO;
import com.css.znsb.mhzc.pojo.yhxx.YhxxVO;
import com.css.znsb.mhzc.service.role.PermissionService;
import com.css.znsb.mhzc.service.yhxx.UserService;
import com.css.znsb.mhzc.util.PasswordUtils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.css.znsb.mhzc.util.YgswUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.css.znsb.mhzc.constants.MhzcConstants.MHZC_KFPURI;
import static com.css.znsb.mhzc.constants.MhzcConstants.MHZC_SFPURI;
import static com.css.znsb.mhzc.constants.MhzcConstants.MHZC_FPURI;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private ZnsbMhqxYhxxbMapper yhxxbMapper;

    @Resource
    private ZnsbMhqxZzxxbMapper zzxxbMapper;

    @Resource
    private ZnsbMhqxZzyhGxbMapper zzyhGxbMapper;

    @Resource
    private ZnsbMhqxYhjsGxbMapper yhjsGxbMapper;

    @Resource
    private ZnsbMhqxJsqxGxbMapper jsqxGxbMapper;

    @Resource
    private ZnsbMhqxYhjgGxbMapper yhjgGxbMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private ZnsbMhqxJsxxbMapper jsxxbMapper;

    @Resource
    private ZnsbMhqxQxxxbMapper qxxxbMapper;

    @Resource
    private ZnsbMhqxJgxxbMapper jgxxbMapper;

    @Resource
    private ZnsbMhqxHtyhxxbMapper htyhxxbMapper;

    @Resource
    private LqyqGlhtUserMapper lqyqGlhtUserMapper;

    @Value("${mhzc.mhlx:shsm}")
    private String mhlx;

    @Override
    public String getRedirectUri() {
        log.info("开始获取iam重定向地址");
        String iam = CacheUtils.dm2mc("xt_xtcs","SSO-IAM");
        if (GyUtils.isNull(iam)) return "";
        String[] split = iam.split(";");
        String baseUri = split[0];
        String serverCode = split[1];
        if (GyUtils.isNull(baseUri)||GyUtils.isNull(serverCode)) return "";
        String uri = baseUri+"/user/iam/api/sso/url/"+serverCode;
        log.info("mhzc获取iam重定向地址返回参数为:{}",uri);
        return uri;
    }

    @Override
    public InitSessionResVO init() {
        log.info(ZnsbSessionUtils.getJguuid()+","+ZnsbSessionUtils.getDjxh());
        final InitSessionResVO resVO = new InitSessionResVO();
        resVO.setYhuuid(ZnsbSessionUtils.getYhUuid());
        resVO.setZsxm(ZnsbSessionUtils.getZsxm());
        resVO.setDjxh(ZnsbSessionUtils.getDjxh());
        resVO.setNsrsbh(ZnsbSessionUtils.getNsrsbh());
        resVO.setXzqhszDm(ZnsbSessionUtils.getXzqhszDm());
        resVO.setYhlx(ZnsbSessionUtils.getYhlx());
        resVO.setQylxz(ZnsbSessionUtils.getQylxz());
        ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = yhxxbMapper.selectById(ZnsbSessionUtils.getYhUuid());
        if (GyUtils.isNull(znsbMhqxYhxxbDO)){
            return resVO;
        }
        resVO.setYhtx(znsbMhqxYhxxbDO.getTxdzurl());
        String scjguuid = znsbMhqxYhxxbDO.getScdljguuid();
        String scdlzzuuid = znsbMhqxYhxxbDO.getScdlzzuuid();
        if (GyUtils.isNull(scjguuid)){
            scjguuid = ZnsbSessionUtils.getJguuid();
            resVO.setJgmc(ZnsbSessionUtils.getJgmc());
        }
        if (GyUtils.isNull(scdlzzuuid)){
            scdlzzuuid = ZnsbSessionUtils.getZzuuid();
        }
        resVO.setZzuuid(scdlzzuuid);

        if (!GyUtils.isNull(resVO.getZzuuid())){
            ZnsbMhqxZzxxbDO znsbMhqxZzxxbDO = zzxxbMapper.selectZzxxByZzuuid(resVO.getZzuuid());
            if (!GyUtils.isNull(znsbMhqxZzxxbDO)){
                resVO.setZzmc(znsbMhqxZzxxbDO.getZzmc2());
            }
        }

        resVO.setJguuid(scjguuid);
        if (!GyUtils.isNull(scjguuid)){
            QueryWrapper<ZnsbMhqxJgxxbDO> wrapper = new QueryWrapper<>();
            wrapper.lambda().select(ZnsbMhqxJgxxbDO::getQydmz,ZnsbMhqxJgxxbDO::getJgmc1)
                    .eq(ZnsbMhqxJgxxbDO::getJguuid1,resVO.getJguuid());
            ZnsbMhqxJgxxbDO znsbMhqxJgxxbDO = jgxxbMapper.selectOne(wrapper);
            if (!GyUtils.isNull(znsbMhqxJgxxbDO)){
                resVO.setQydmz(znsbMhqxJgxxbDO.getQydmz());
                resVO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
            }
        }

        if (!GyUtils.isNull(scdlzzuuid)){
            final List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByUuid(resVO.getYhuuid(), scdlzzuuid);
            if (!GyUtils.isNull(znsbMhqxYhjsGxbDOS)){
                final String jsuuid = znsbMhqxYhjsGxbDOS.get(0).getJsuuid();
                final List<ZnsbMhqxJsxxbDO> jsxxbDOS = jsxxbMapper.selectJsxxByUuids(Collections.singletonList(jsuuid));
                final ZnsbMhqxJsxxbDO znsbMhqxJsxxbDO = jsxxbDOS.get(0);
                resVO.setYhjs(znsbMhqxJsxxbDO.getJsmc());

                final List<String> jsuuids = znsbMhqxYhjsGxbDOS.stream().map(ZnsbMhqxYhjsGxbDO::getJsuuid).collect(Collectors.toList());
                final RoleIncludePermissionDTO permissionOfRole = permissionService.getPermissionOfRole(jsuuids);
                resVO.setQxbmArr(permissionOfRole.getQxbmArr());
                resVO.setMenu(permissionOfRole.getFunctionTreeVO());
            }
        }
        resVO.setMhlx(mhlx);
        resVO.setKfp(CacheUtils.dm2mc("xt_xtcs",MHZC_KFPURI));
        resVO.setSfp(CacheUtils.dm2mc("xt_xtcs",MHZC_SFPURI));
        resVO.setFp(CacheUtils.dm2mc("xt_xtcs",MHZC_FPURI));
        resVO.setCheckPassword(RedisUtils.get(formatKey(ZnsbSessionUtils.getYhUuid())));
        return resVO;
    }


    @Override
    public InitSessionResVO saasinit() {
        InitSessionResVO resVO = new InitSessionResVO();
        resVO.setYhlx(ZnsbSessionUtils.getYhlx());
        resVO.setZzmc(ZnsbSessionUtils.getZzmc());
        resVO.setZzuuid(ZnsbSessionUtils.getZzuuid());
        resVO.setTopZzbz(ZnsbSessionUtils.getTopZzbz());
        resVO.setTopZzmc(ZnsbSessionUtils.getTopZzmc());
        resVO.setTopZzuuid(ZnsbSessionUtils.getTopZzuuid());
        resVO.setYhuuid(ZnsbSessionUtils.getYhUuid());
        resVO.setZsxm(ZnsbSessionUtils.getZsxm());
        resVO.setKczsaasjgxxList(ZnsbSessionUtils.getKczsaasjgxxList());
        resVO.setBxjsaaszzuuidList(ZnsbSessionUtils.getBxjsaaszzuuidList());
        resVO.setMhlx(mhlx);
        return resVO;
    }

    @Override
    public void syncIAMUser(int pageParam) {
        String iam = CacheUtils.dm2mc("xt_xtcs","SSO-IAM");
        if (GyUtils.isNull(iam)) throw new IllegalStateException("iam相关配置为空，请联系运维");
        String[] split = iam.split(";");
        if (split.length<5) throw new IllegalStateException("iam相关配置为空，请联系运维");
        String baseurl = split[0];
        String serverCode = split[1];
        String secret = split[3];
        String allSync = split[4];
        if (GyUtils.isNull(baseurl)||GyUtils.isNull(serverCode)||GyUtils.isNull(secret)) throw new IllegalStateException("iam相关配置为空，请联系运维");
        String url = baseurl+"/user/api/iam/query/user/list?serverCode="+serverCode;
        log.info("iam单点认证checktoken_url：{}",url);
        IamUserParamVO paramVO = new IamUserParamVO();
        paramVO.setPage(String.valueOf(pageParam));
        paramVO.setSize("5000");
        paramVO.setServerCode(serverCode);
        if (GyUtils.isNull(allSync)||!"all".equals(allSync)){
            paramVO.setStartTime(getLastDay());
        }
        String httpParamsSign = "";
        try {
            httpParamsSign = getHttpParamsSign(paramVO, secret);
        } catch (Exception e) {
            throw new IllegalStateException("IAM用户同步sign生成失败");
        }
        paramVO.setSign(httpParamsSign);
        log.info("调用iam用户同步接口开始，url：{}，入参：{}",url,paramVO);
        String body2 = HttpRequest.post(url)
                .body(JsonUtils.toJson(paramVO))
                .execute().body();
        log.info("iam用户同步接口返回：{}",body2);
        if (!GyUtils.isNull(body2)){
            List<ZnsbMhqxYhxxbDO> znsbMhqxYhxxbDOS = yhxxbMapper.selectAll(null, null, null, null);
            List<String> ghs = znsbMhqxYhxxbDOS.stream().map(ZnsbMhqxYhxxbDO::getGh).filter(v->!GyUtils.isNull(v)).collect(Collectors.toList());

            ResponseDTO responseDTO = JsonUtils.toBean(body2, ResponseDTO.class);
            if (!GyUtils.isNull(responseDTO)&&0==responseDTO.getCode()){
                LinkedTreeMap<String, Object> result = responseDTO.getData();
                List<LinkedHashMap<String, Object>> list = (List<LinkedHashMap<String, Object>>) result.get("list");
                batchIAMUser(list,ghs);
                String total = (String) result.get("total");
                Integer size = (Integer) result.get("size");
                Integer page = (Integer) result.get("page");
                int cursize = size*page;
                if (Integer.parseInt(total)>cursize){
                    syncIAMUser(page+1);
                }
            }else {
                throw new IllegalStateException(!GyUtils.isNull(responseDTO)?responseDTO.getError_msg():"响应参数为空");
            }
        }
    }

    @Override
    public Integer updUserYxbz(UserYxbzVO yxbzVO) {
        ZnsbMhqxYhxxbDO yhxxbDO = new ZnsbMhqxYhxxbDO();
        yhxxbDO.setYhUuid(yxbzVO.getYhuuid());
        yhxxbDO.setYxbz(yxbzVO.getYxbz());
        return yhxxbMapper.updateById(yhxxbDO);
    }

    @Override
    public YhxxVO getUserBySjhmAndZsxm(String sjhm, String zsxm) {
        List<ZnsbMhqxYhxxbDO> userBySjhmAndZsxm = yhxxbMapper.getUserBySjhmAndZsxm(sjhm, zsxm);
        if (GyUtils.isNull(userBySjhmAndZsxm)) return null;
        return BeanUtil.toBean(userBySjhmAndZsxm.get(0),YhxxVO.class);
    }

    @Override
    public UserResVO UserCreateService(UserCreateReqVO reqVO) {
        log.info("王老吉新增用户：{}",reqVO);
        checkWljUser(reqVO.getIamRemoteUser(),reqVO.getIamRemotePwd());
        UserResVO resVO = new UserResVO();

        String dlzh = reqVO.getWorkNo();
        ZnsbMhqxYhxxbDO userByDlzh = yhxxbMapper.getUserByDlzh(dlzh);
        ZnsbMhqxYhxxbDO yhxxbDO = new ZnsbMhqxYhxxbDO();
        yhxxbDO.setDlzh(dlzh);
        yhxxbDO.setYxdz(reqVO.getEmail());
        yhxxbDO.setSjhm1(reqVO.getPhoneNo());
        yhxxbDO.setZsxm1(reqVO.getRealName());
        if (GyUtils.isNull(userByDlzh)){
            String yhuuid = IdUtil.fastSimpleUUID();
            yhxxbDO.setYhUuid(yhuuid);
            yhxxbDO.setYxbz("Y");
            yhxxbDO.setDlmm(MD5.create().digestHex("WLJ123456"));
            yhxxbDO.setYhlx("0");
            yhxxbDO.setGh(dlzh);
            yhxxbDO.setSfzjlx("201");
            yhxxbDO.setSdbz("0".equals(reqVO.getStatus())?"N":"Y");
            yhxxbDO.setLrrq(new Date());
            yhxxbDO.setXgrq(new Date());
            yhxxbDO.setLrrsfid("WLJ_REMOTE");
            yhxxbDO.setXgrsfid("WLJ_REMOTE");
            yhxxbDO.setYwqdDm("WLJ_REMOTE");
            yhxxbDO.setSjcsdq("WLJ_REMOTE");
            yhxxbDO.setSjgsdq("ZNSB_MHZC");
            yhxxbDO.setSjtbSj(new Date());
            yhxxbMapper.insert(yhxxbDO);
        }else {
            yhxxbDO.setYhUuid(userByDlzh.getYhUuid());
            yhxxbDO.setYxbz("Y");
            yhxxbDO.setXgrsfid("WLJ_REMOTE");
            yhxxbDO.setXgrq(new Date());
            if (!GyUtils.isNull(reqVO.getStatus())) yhxxbDO.setSdbz("0".equals(reqVO.getStatus())?"N":"Y");
            yhxxbMapper.updateById(yhxxbDO);
        }
        resVO.setUid(dlzh);
        resVO.setAimRequestId(reqVO.getIamRequestId());
        resVO.setResultCode("0");
        resVO.setMessage("success");
        return resVO;
    }

    @Override
    public UserResVO UserUpdateService(UserCreateReqVO reqVO) {
        log.info("王老吉修改用户：{}",reqVO);
        checkWljUser(reqVO.getIamRemoteUser(),reqVO.getIamRemotePwd());
        if (GyUtils.isNull(reqVO.getUid())) throw new IllegalStateException("uid不能为空");
        ZnsbMhqxYhxxbDO userByDlzh = yhxxbMapper.getUserByDlzh(reqVO.getUid());
        if (GyUtils.isNull(userByDlzh)) throw new IllegalStateException("用户不存在");
        UserResVO resVO = new UserResVO();
        ZnsbMhqxYhxxbDO yhxxbDO = new ZnsbMhqxYhxxbDO();
        yhxxbDO.setDlzh(reqVO.getWorkNo());
        yhxxbDO.setYxdz(reqVO.getEmail());
        yhxxbDO.setSjhm1(reqVO.getPhoneNo());
        yhxxbDO.setZsxm1(reqVO.getRealName());
        if (!GyUtils.isNull(reqVO.getStatus())) yhxxbDO.setSdbz("0".equals(reqVO.getStatus())?"N":"Y");
        yhxxbDO.setYhUuid(userByDlzh.getYhUuid());
        yhxxbDO.setXgrq(new Date());
        yhxxbDO.setXgrsfid("WLJ_REMOTE");
        yhxxbMapper.updateById(yhxxbDO);
        resVO.setAimRequestId(reqVO.getIamRequestId());
        resVO.setResultCode("0");
        resVO.setMessage("success");
        return resVO;
    }

    @Override
    public UserResVO UserDeleteService(UserDeleteReqVO reqVO) {
        log.info("王老吉删除用户：{}",reqVO);
        checkWljUser(reqVO.getIamRemoteUser(),reqVO.getIamRemotePwd());
        if (GyUtils.isNull(reqVO.getUid())) throw new IllegalStateException("uid不能为空");
        ZnsbMhqxYhxxbDO userByDlzh = yhxxbMapper.getUserByDlzh(reqVO.getUid());
        if (GyUtils.isNull(userByDlzh)) throw new IllegalStateException("用户不存在");
        UserResVO resVO = new UserResVO();
        ZnsbMhqxYhxxbDO yhxxbDO = new ZnsbMhqxYhxxbDO();
        yhxxbDO.setYhUuid(userByDlzh.getYhUuid());
        yhxxbDO.setYxbz("N");
        yhxxbDO.setXgrq(new Date());
        yhxxbDO.setXgrsfid("WLJ_REMOTE");
        yhxxbMapper.updateById(yhxxbDO);
        resVO.setAimRequestId(reqVO.getIamRequestId());
        resVO.setResultCode("0");
        resVO.setMessage("success");
        return resVO;
    }

    @Override
    public SchemaResVO SchemaService(UserCreateReqVO reqVO) {
        checkWljUser(reqVO.getIamRemoteUser(),reqVO.getIamRemotePwd());
        SchemaResVO resVO = new SchemaResVO();
        List<SchemaVO> list = new ArrayList<>();
        SchemaVO schemaVO = new SchemaVO();
        schemaVO.setMultivalued(false);
        schemaVO.setName("workNo");
        schemaVO.setRequired(true);
        schemaVO.setType("String");
        list.add(schemaVO);
        SchemaVO schemaVO1 = new SchemaVO();
        schemaVO1.setMultivalued(false);
        schemaVO1.setName("phoneNo");
        schemaVO1.setRequired(true);
        schemaVO1.setType("String");
        list.add(schemaVO1);
        SchemaVO schemaVO2 = new SchemaVO();
        schemaVO2.setMultivalued(false);
        schemaVO2.setName("email");
        schemaVO2.setRequired(true);
        schemaVO2.setType("String");
        list.add(schemaVO2);
        SchemaVO schemaVO3 = new SchemaVO();
        schemaVO3.setMultivalued(false);
        schemaVO3.setName("realName");
        schemaVO3.setRequired(true);
        schemaVO3.setType("String");
        list.add(schemaVO3);
        resVO.setAccount(list);
        resVO.setAimRequestId(reqVO.getIamRequestId());
        return resVO;
    }

    @Override
    public TygjCommonResult<Object> UserCreate(TygjUserReqVO reqVO) {
        log.info("通用国际新增用户：{}",reqVO);
        String timestamp = ServletUtils.getRequest().getHeader("timestamp");
        //校验请求时间戳如果超过五分钟就返回过期
        if (checkTimestamp(timestamp)) TygjCommonResult.error("请求已过期");

        String operation = reqVO.getOperation();
        String dlzh = reqVO.getUsername();
        String yhuuid = reqVO.getUserId();
        ZnsbMhqxYhxxbDO userByDlzh = yhxxbMapper.selectById(yhuuid);
        if ("0".equals(operation)){
            ZnsbMhqxYhxxbDO yhxxbDO = new ZnsbMhqxYhxxbDO();
            yhxxbDO.setYhUuid(yhuuid);
            yhxxbDO.setDlzh(dlzh);
            yhxxbDO.setYxdz(reqVO.getEmail());
            yhxxbDO.setSjhm1(reqVO.getPhoneNo());
            yhxxbDO.setZsxm1(reqVO.getFullname());
            yhxxbDO.setGh(reqVO.getEmployeeNo());
            yhxxbDO.setXgrq(new Date());
            yhxxbDO.setXgrsfid("TYGJ_REMOTE");
            yhxxbDO.setYxbz("Y".equals(reqVO.getUserState())?"Y":"N");
            if (GyUtils.isNull(userByDlzh)){
                yhxxbDO.setYxbz("Y");
                yhxxbDO.setDlmm(MD5.create().digestHex("TYGJ123456"));
                yhxxbDO.setYhlx("0");
                yhxxbDO.setSfzjlx("201");
                yhxxbDO.setSdbz("N");
                yhxxbDO.setLrrq(new Date());
                yhxxbDO.setLrrsfid("TYGJ_REMOTE");
                yhxxbDO.setYwqdDm("TYGJ_REMOTE");
                yhxxbDO.setSjcsdq("TYGJ_REMOTE");
                yhxxbDO.setSjgsdq("ZNSB_MHZC");
                yhxxbDO.setSjtbSj(new Date());
                yhxxbMapper.insert(yhxxbDO);
            }else {
                yhxxbMapper.updateById(yhxxbDO);
            }
        }else if ("2".equals(operation)){
            if (GyUtils.isNull(userByDlzh)) return TygjCommonResult.error("用户不存在");
            ZnsbMhqxYhxxbDO yhxxbDO = new ZnsbMhqxYhxxbDO();
            yhxxbDO.setYhUuid(reqVO.getUserId());
            yhxxbDO.setYxbz("N");
            yhxxbDO.setXgrq(new Date());
            yhxxbDO.setXgrsfid("TYGJ_REMOTE");
            yhxxbMapper.updateById(yhxxbDO);
        }

        return TygjCommonResult.success();
    }

    public static boolean checkTimestamp(String timestamp) {
        try {
            // 定义日期时间格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 解析输入的时间戳
            Date inputDate = sdf.parse(timestamp);

            // 获取当前时间
            Date currentDate = new Date();

            // 计算时间差（毫秒）
            long diffInMillis = currentDate.getTime() - inputDate.getTime();

            // 5分钟对应的毫秒数 (5 * 60 * 1000)
            final long FIVE_MINUTES_IN_MILLIS = 300000;

            // 比较时间差是否超过5分钟
            return diffInMillis > FIVE_MINUTES_IN_MILLIS;

        } catch (Exception e) {
            // 如果时间戳格式不正确，可以根据需求处理异常
            // 这里简单返回false，实际应用中可能需要更复杂的错误处理
            log.info("时间戳格式不正确");
            return true;
        }
    }

    private void checkWljUser(String user,String password){
        String s = CacheUtils.dm2mc("xt_xtcs", "REMOTE_WLJ");
        if (GyUtils.isNull(s)) throw new IllegalStateException("配置缺失，请联系运维人员");
        String[] split = s.split(";");
        if (split.length<2) throw new IllegalStateException("配置缺失，请联系运维人员");
        String md5 = MD5.create().digestHex(password);
        if (!user.equals(split[0]))  throw new IllegalStateException("账号不存在");
        if (!md5.equals(split[1]))  throw new IllegalStateException("密码不正确");
    }

    private void batchIAMUser(List<LinkedHashMap<String, Object>> list,List<String> ghs){
        if (GyUtils.isNull(list)) return;
        List<ZnsbMhqxYhxxbDO> users = list.stream().filter(v -> !GyUtils.isNull(v.get("idt_user__work_no")) && !ghs.contains((String) v.get("idt_user__work_no"))).map(m -> {
            ZnsbMhqxYhxxbDO yhxxbDO = new ZnsbMhqxYhxxbDO();
            yhxxbDO.setYhUuid(IdUtil.fastSimpleUUID());
            yhxxbDO.setYhlx("0");
            yhxxbDO.setSdbz("N");
            yhxxbDO.setZsxm1(GyUtils.isNull(m.get("idt_user__user_name")) ? "" : (String) m.get("idt_user__user_name"));
            yhxxbDO.setYxdz(GyUtils.isNull(m.get("idt_user__email")) ? "" : (String) m.get("idt_user__email"));
            yhxxbDO.setSjhm1(GyUtils.isNull(m.get("idt_user__mobile")) ? "" : (String) m.get("idt_user__mobile"));
            yhxxbDO.setGh(GyUtils.isNull(m.get("idt_user__work_no")) ? "" : (String) m.get("idt_user__work_no"));
            yhxxbDO.setYxbz("N");
            yhxxbDO.setDlzh(GyUtils.isNull(m.get("app_account__account_no")) ? "" : (String) m.get("app_account__account_no"));
            yhxxbDO.setDlmm(MD5.create().digestHex("SM123456"));
            yhxxbDO.setSfzjlx("201");
            yhxxbDO.setYwqdDm(MhzcConstants.YWQDDM);
            yhxxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
            yhxxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
            Date date = new Date();
            yhxxbDO.setLrrq(date);
            yhxxbDO.setXgrq(date);
            yhxxbDO.setLrrsfid("ZNSB_IAM");
            yhxxbDO.setXgrsfid("ZNSB_IAM");
            yhxxbDO.setSjtbSj(date);
            return yhxxbDO;
        }).collect(Collectors.toList());
        yhxxbMapper.insertBatch(users);
    }

    private String getLastDay(){
        // 获取当前时间戳
        long currentTimeStamp = System.currentTimeMillis();

        // 创建一个Calendar实例，并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(currentTimeStamp);

        // 将Calendar实例的时间向前推一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        // 获取前一天的时间戳
        return String.valueOf(calendar.getTimeInMillis());
    }

    public static String getHttpParamsSign(Object object,String securityCode) throws Exception{
        //请求对象先转Map
        TreeMap<String, String> map = new TreeMap<>();
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            String value = (String) field.get(object);
            if (StringUtils.equals("sign", fieldName)) {
                continue;
            }
            if (StringUtils.isEmpty(value)) {
                continue;
            }
            map.put(fieldName, value);
        }
        //拼接参数进行MD5
        StringBuilder str = new StringBuilder();
        for (Map.Entry<String, String> e : map.entrySet()) {
            str.append(e.getKey()).append("=").append(e.getValue()).append("&");
        }
        if (!map.isEmpty()) {
            str.deleteCharAt(str.length() - 1);
        }
        str.append(securityCode);
        log.info("组装IAM用户同步签名:{}",str);
        return MD5.create().digestHex(str.toString());
    }

    @Override
    public List<UserInfoVO> getAll(AllUserInfoReqVO reqVO) {
        final String dlzh = GyUtils.isNull(reqVO.getDlzh())?null:reqVO.getDlzh();
        final String zsxm = GyUtils.isNull(reqVO.getZsxm())?null:reqVO.getZsxm();
        final String sdbz = "Y".equals(reqVO.getSdbz())?"Y":null;
        final String yxbz = "N".equals(reqVO.getYxbz())?"N":"Y";

        final List<ZnsbMhqxYhxxbDO> znsbMhqxYhxxbDOS = yhxxbMapper.selectAll(dlzh,zsxm,sdbz,yxbz);
        List<UserInfoVO> result = new ArrayList<>();
        if (!GyUtils.isNull(znsbMhqxYhxxbDOS)){
            znsbMhqxYhxxbDOS.forEach(yhxx -> {
                final UserInfoVO userInfoVO = new UserInfoVO();
                BeanUtil.copyProperties(yhxx,userInfoVO,true);
                userInfoVO.setZsxm(yhxx.getZsxm1());
                userInfoVO.setSjhm(yhxx.getSjhm1());
                userInfoVO.setSfzjlxmc(CacheUtils.dm2mc("dm_gy_sfzjlx", userInfoVO.getSfzjlx()));
                result.add(userInfoVO);
            });
        }

        return result;
    }

    @Override
    public List<YhidsOfZzVO> getYhidsOfZz(ZzxxBaseVO baseVO) {
        final String zzuuid = baseVO.getZzuuid();
        final String yhmc = baseVO.getYhmc();
        List<YhidsOfZzVO> result = new ArrayList<>();
        if (!GyUtils.isNull(zzuuid)){
            final List<ZnsbMhqxZzyhGxbDO> znsbMhqxZzyhGxbDOS = zzyhGxbMapper.selectYhByZzuuids(Collections.singletonList(zzuuid));
            if (!GyUtils.isNull(znsbMhqxZzyhGxbDOS)){
                List<String> collect = znsbMhqxZzyhGxbDOS.stream().map(ZnsbMhqxZzyhGxbDO::getYhUuid).collect(Collectors.toList());
                List<ZnsbMhqxYhxxbDO> znsbMhqxYhxxbDOS = yhxxbMapper.selectByUuids(collect, yhmc);
                znsbMhqxYhxxbDOS.stream().map(ZnsbMhqxYhxxbDO::getYhUuid).collect(Collectors.toList()).forEach(s->{
                    YhidsOfZzVO vo = new YhidsOfZzVO();
                    vo.setYhuuid(s);
                    List<ZnsbMhqxYhjgGxbDO> znsbMhqxYhjgGxbDOS = yhjgGxbMapper.selectAllJgByYhZz(s, zzuuid);
                    if (!GyUtils.isNull(znsbMhqxYhjgGxbDOS)){
                        vo.setFpqysl(znsbMhqxYhjgGxbDOS.size());
                    }else {
                        vo.setFpqysl(0);
                    }
                    List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByUuid(s, zzuuid);
                    if (!GyUtils.isNull(znsbMhqxYhjsGxbDOS)){
                        vo.setFpjssl(znsbMhqxYhjsGxbDOS.size());
                    }else {
                        vo.setFpjssl(0);
                    }
                    result.add(vo);
                });
            }
        }
        return result;
    }


    @Override
    public Boolean bindZz(UserZzBindVO bindVO) {
        final Date date = new Date();
        final String zzuuid = bindVO.getZzuuid();
        final List<String> yhuuidList = bindVO.getYhuuidList();
        if(!GyUtils.isNull(yhuuidList) && !GyUtils.isNull(zzuuid)){
            List<ZnsbMhqxZzyhGxbDO> insertList = new ArrayList<>();
            yhuuidList.forEach(yhuuid -> {
                final ZnsbMhqxZzyhGxbDO yhjgGxbDO = new ZnsbMhqxZzyhGxbDO();
                yhjgGxbDO.setUuid(IdUtil.fastSimpleUUID());
                yhjgGxbDO.setZzuuid(zzuuid);
                yhjgGxbDO.setYhUuid(yhuuid);
                yhjgGxbDO.setYwqdDm(MhzcConstants.YWQDDM);
                yhjgGxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
                yhjgGxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
                yhjgGxbDO.setLrrq(date);
                yhjgGxbDO.setXgrq(date);
                yhjgGxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
                yhjgGxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
                yhjgGxbDO.setSjtbSj(date);
                insertList.add(yhjgGxbDO);
            });
            zzyhGxbMapper.insertBatch(insertList);
        }

        return true;
    }

    @Override
    public void zzyhBatch(List<ZnsbMhqxZzyhGxbDO> zzyhs) {
        final Date date = new Date();
        List<ZnsbMhqxZzyhGxbDO> collect = zzyhs.stream().map(z -> {
            z.setUuid(IdUtil.fastSimpleUUID());
            z.setYwqdDm(MhzcConstants.YWQDDM);
            z.setSjcsdq(MhzcConstants.SJCSDQ);
            z.setSjgsdq(MhzcConstants.SJGSDQ);
            z.setLrrq(date);
            z.setXgrq(date);
            z.setLrrsfid(ZnsbSessionUtils.getYhUuid());
            z.setXgrsfid(ZnsbSessionUtils.getYhUuid());
            z.setSjtbSj(date);
            return z;
        }).collect(Collectors.toList());
        zzyhGxbMapper.insertBatch(collect);
    }

    @Transactional
    @Override
    public Boolean unBindZz(UserZzBindVO bindVO) {
        final String zzuuid = bindVO.getZzuuid();
        final List<String> yhuuidList = bindVO.getYhuuidList();
        if(!GyUtils.isNull(yhuuidList) && !GyUtils.isNull(zzuuid)){
//            yhuuidList.forEach(yhuuid -> zzyhGxbMapper.deleteByuuid(zzuuid,yhuuid));
            for (String yhuuid:yhuuidList){
                zzyhGxbMapper.deleteByuuid(zzuuid,yhuuid);
                yhjgGxbMapper.deleteByuuid(zzuuid,null,yhuuid);
                yhjsGxbMapper.deleteByUuid(zzuuid,yhuuid,null);
            }
        }
        return true;
    }

    @Transactional
    @Override
    public Integer insertYhxx(UserInsertReqVO infoVO) {
        final Date date = new Date();
        String s = CacheUtils.dm2mc("xt_xtcs", "mh_xtcs_0003");
        PasswordUtils.passwordCheck(infoVO.getDlmm(),s);
        final ZnsbMhqxYhxxbDO yhxx = yhxxbMapper.hasAccount(infoVO.getDlzh());

        if (!GyUtils.isNull(Optional.ofNullable(yhxx).map(ZnsbMhqxYhxxbDO::getDlzh).orElse(null))){
            throw ServiceExceptionUtil.exception(ErrorCodeEnum.ACCOUNT_EXISTED.getErrorCode());
        }

        if (GyUtils.isNull(infoVO.getSfzjlx()) || GyUtils.isNull(infoVO.getSfzjhm())){
            throw ServiceExceptionUtil.exception(ErrorCodeEnum.SFZJ_NOT_NULL.getErrorCode());
        }

        final ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = new ZnsbMhqxYhxxbDO();
        znsbMhqxYhxxbDO.setYhUuid(IdUtil.fastSimpleUUID());
        znsbMhqxYhxxbDO.setDlmm(MD5.create().digestHex(infoVO.getDlmm()));
        znsbMhqxYhxxbDO.setDlzh(infoVO.getDlzh());
        znsbMhqxYhxxbDO.setZsxm1(infoVO.getZsxm());
        znsbMhqxYhxxbDO.setSjhm1(infoVO.getSjhm());
        znsbMhqxYhxxbDO.setYxdz(infoVO.getYxdz());
        znsbMhqxYhxxbDO.setSdbz("N");
        znsbMhqxYhxxbDO.setYxbz("Y");
        znsbMhqxYhxxbDO.setSfzjlx(infoVO.getSfzjlx());
        znsbMhqxYhxxbDO.setSfzjhm(infoVO.getSfzjhm());
        znsbMhqxYhxxbDO.setYhlx(infoVO.getYhlx());
        znsbMhqxYhxxbDO.setYwqdDm(MhzcConstants.YWQDDM);
        znsbMhqxYhxxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
        znsbMhqxYhxxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
        znsbMhqxYhxxbDO.setLrrq(date);
        znsbMhqxYhxxbDO.setXgrq(date);
        znsbMhqxYhxxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
        znsbMhqxYhxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
        znsbMhqxYhxxbDO.setSjtbSj(date);

        return yhxxbMapper.insert(znsbMhqxYhxxbDO);
    }

    @Transactional
    @Override
    public void batchYhxx(List<ZnsbMhqxYhxxbDO> list) {
        final Date date = new Date();
//        int i = 1;
        for (ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO:list){
//            PasswordUtils.passwordCheck(znsbMhqxYhxxbDO.getDlmm());
            final ZnsbMhqxYhxxbDO yhxx = yhxxbMapper.hasAccount(znsbMhqxYhxxbDO.getDlzh());

            if (!GyUtils.isNull(Optional.ofNullable(yhxx).map(ZnsbMhqxYhxxbDO::getDlzh).orElse(null))){
//                ErrorCode ACCOUNT_EXISTED = new ErrorCode(1001006, "第"+i+"条数据:账号已存在");
//                throw ServiceExceptionUtil.exception(ACCOUNT_EXISTED);
                return;
            }

//            if (GyUtils.isNull(znsbMhqxYhxxbDO.getSfzjhm())){
////                ErrorCode SFZJ_NOT_NULL = new ErrorCode(1001008, "第"+i+"条数据:身份证号码不可为空");
////                throw ServiceExceptionUtil.exception(SFZJ_NOT_NULL);
//                return;
//            }

            znsbMhqxYhxxbDO.setYhUuid(IdUtil.fastSimpleUUID());
            znsbMhqxYhxxbDO.setDlmm(MD5.create().digestHex(znsbMhqxYhxxbDO.getDlmm()));
            znsbMhqxYhxxbDO.setSdbz("N");
            znsbMhqxYhxxbDO.setYxbz("Y");
            znsbMhqxYhxxbDO.setSfzjlx("201");
            znsbMhqxYhxxbDO.setYwqdDm(MhzcConstants.YWQDDM);
            znsbMhqxYhxxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
            znsbMhqxYhxxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
            znsbMhqxYhxxbDO.setLrrq(date);
            znsbMhqxYhxxbDO.setXgrq(date);
            znsbMhqxYhxxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
            znsbMhqxYhxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
            znsbMhqxYhxxbDO.setSjtbSj(date);
//            i+=1;
        }
        yhxxbMapper.insertBatch(list);
    }

    @Override
    public Integer updateYhxx(UserUpdateReqVO infoVO) {
        final ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = new ZnsbMhqxYhxxbDO();
        znsbMhqxYhxxbDO.setYhUuid(infoVO.getYhuuid());
        znsbMhqxYhxxbDO.setDlzh(infoVO.getDlzh());
        znsbMhqxYhxxbDO.setZsxm1(infoVO.getZsxm());
        znsbMhqxYhxxbDO.setSjhm1(infoVO.getSjhm());
        znsbMhqxYhxxbDO.setYxdz(infoVO.getYxdz());
        znsbMhqxYhxxbDO.setYhlx(infoVO.getYhlx());
        znsbMhqxYhxxbDO.setXgrq(new Date());
        znsbMhqxYhxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
        if (GyUtils.isNull(infoVO.getSfzjlx()) || GyUtils.isNull(infoVO.getSfzjhm())){
            throw ServiceExceptionUtil.exception(ErrorCodeEnum.SFZJ_NOT_NULL.getErrorCode());
        }
        znsbMhqxYhxxbDO.setSfzjlx(infoVO.getSfzjlx());
        znsbMhqxYhxxbDO.setSfzjhm(infoVO.getSfzjhm());

        return yhxxbMapper.updateById(znsbMhqxYhxxbDO);
    }

    @Transactional
    @Override
    public Integer deleteYhxx(List<String> yhuuids) {
        final int i = yhxxbMapper.deleteBatchIds(yhuuids);
        final Integer j = zzyhGxbMapper.deleteByYhuuid(yhuuids);
        final Integer k = yhjsGxbMapper.deleteByYhuuid(yhuuids);
        final Integer l = yhjgGxbMapper.deleteByYhuuid(yhuuids);

        return i+j+k+l;
    }

    @Override
    public Integer resetPassword(AdminResetPasswordVO infoVO) {
        final String yhuuid = infoVO.getYhuuid();
        final String dlmm = infoVO.getDlmm();
        final ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = new ZnsbMhqxYhxxbDO();
        znsbMhqxYhxxbDO.setYhUuid(yhuuid);
        znsbMhqxYhxxbDO.setDlmm(MD5.create().digestHex(dlmm));
        znsbMhqxYhxxbDO.setXgrq(new Date());
        znsbMhqxYhxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
        String s = CacheUtils.dm2mc("xt_xtcs", "mh_xtcs_0003");
        PasswordUtils.passwordCheck(dlmm,s);
        return yhxxbMapper.updateById(znsbMhqxYhxxbDO);
    }

    @Override
    public Integer lockUser(UserLockVO infoVO) {
        final Date date = new Date();
        final ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = new ZnsbMhqxYhxxbDO();
        znsbMhqxYhxxbDO.setYhUuid(infoVO.getYhuuid());
        znsbMhqxYhxxbDO.setSdbz("Y");
        znsbMhqxYhxxbDO.setSdyy(infoVO.getSdyy());
        znsbMhqxYhxxbDO.setSdsj1(date);
        znsbMhqxYhxxbDO.setSdryid(ZnsbSessionUtils.getYhUuid());
        znsbMhqxYhxxbDO.setXgrq(date);
        znsbMhqxYhxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());

        return yhxxbMapper.updateById(znsbMhqxYhxxbDO);
    }

    @Override
    public Integer unlockUser(UserBaseVO infoVO) {
        return yhxxbMapper.unlock(infoVO.getYhuuid(),new Date(),ZnsbSessionUtils.getYhUuid());
    }

    @Override
    public List<UserRoleBindVO> getAllJsxx(UserBaseVO infoVO) {
        List<UserRoleBindVO> result = new ArrayList<>();
        final List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByYhuuid(infoVO.getYhuuid());
        if (!GyUtils.isNull(znsbMhqxYhjsGxbDOS)){
            znsbMhqxYhjsGxbDOS.forEach(znsbMhqxYhjsGxbDO -> {
                final UserRoleBindVO vo = new UserRoleBindVO();
                vo.setJsuuid(znsbMhqxYhjsGxbDO.getJsuuid());
                vo.setYhuuid(znsbMhqxYhjsGxbDO.getYhUuid());
                vo.setZzuuid(znsbMhqxYhjsGxbDO.getZzuuid());
                result.add(vo);
            });
        }

        return result;
    }

    @Transactional
    @Override
    public Boolean bindJsxx(UserRoleBindBatchVO bindBatchVO) {
        final String yhuuid = bindBatchVO.getYhuuid();
        final String zzuuid = bindBatchVO.getZzuuid();

        final Date date = new Date();
        final List<String> addJsuuidList = bindBatchVO.getAddJsuuidList();
        List<ZnsbMhqxYhjsGxbDO> insertList = new ArrayList<>();
        if (!GyUtils.isNull(addJsuuidList)){
            addJsuuidList.forEach(jsuuid -> {
                final ZnsbMhqxYhjsGxbDO yhjsGxbDO = new ZnsbMhqxYhjsGxbDO();
                yhjsGxbDO.setUuid(IdUtil.fastSimpleUUID());
                yhjsGxbDO.setYhUuid(yhuuid);
                yhjsGxbDO.setZzuuid(zzuuid);
                yhjsGxbDO.setJsuuid(jsuuid);
                ZnsbMhqxJsxxbDO znsbMhqxJsxxbDO = jsxxbMapper.selectJsxxByUuid(jsuuid);
                yhjsGxbDO.setJsmc(GyUtils.isNull(znsbMhqxJsxxbDO)?"":znsbMhqxJsxxbDO.getJsmc());
                yhjsGxbDO.setYwqdDm(MhzcConstants.YWQDDM);
                yhjsGxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
                yhjsGxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
                yhjsGxbDO.setLrrq(date);
                yhjsGxbDO.setXgrq(date);
                yhjsGxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
                yhjsGxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
                yhjsGxbDO.setSjtbSj(date);
                insertList.add(yhjsGxbDO);
            });
            yhjsGxbMapper.insertBatch(insertList);
        }

        final List<String> delJsuuidList = bindBatchVO.getDelJsuuidList();
        if (!GyUtils.isNull(delJsuuidList)){
            delJsuuidList.forEach(jsuuid -> yhjsGxbMapper.deleteByUuid(zzuuid, yhuuid, jsuuid));
        }

        return true;
    }

    @Override
    public List<UserCompanyBindVO> getAllJgxx(UserBaseVO infoVO) {
        List<UserCompanyBindVO> result = new ArrayList<>();
        final List<ZnsbMhqxYhjgGxbDO> yhjgGxbDOS = yhjgGxbMapper.selectAllJgByYhuuid(infoVO.getYhuuid());
        if (!GyUtils.isNull(yhjgGxbDOS)){
            yhjgGxbDOS.forEach(znsbMhqxYhjgGxbDO -> {
                final UserCompanyBindVO vo = new UserCompanyBindVO();
                vo.setJguuid(znsbMhqxYhjgGxbDO.getJguuid1());
                vo.setZzuuid(znsbMhqxYhjgGxbDO.getZzuuid());
                vo.setYhuuid(znsbMhqxYhjgGxbDO.getYhUuid());
                result.add(vo);
            });
        }

        return result;
    }

    @Override
    public Boolean bindJgxx(UserCompanyBindBatchVO bindBatchVO) {
        final String yhuuid = bindBatchVO.getYhuuid();
        final String zzuuid = bindBatchVO.getZzuuid();

        final Date date = new Date();
        List<ZnsbMhqxYhjgGxbDO> insertList = new ArrayList<>();
        final List<String> addJguuidList = bindBatchVO.getAddJguuidList();
        if (!GyUtils.isNull(addJguuidList)){
            addJguuidList.forEach(jguuid -> {
                final ZnsbMhqxYhjgGxbDO yhjgGxbDO = new ZnsbMhqxYhjgGxbDO();
                yhjgGxbDO.setUuid(IdUtil.fastSimpleUUID());
                yhjgGxbDO.setYhUuid(yhuuid);
                yhjgGxbDO.setJguuid1(jguuid);
                yhjgGxbDO.setZzuuid(zzuuid);
                yhjgGxbDO.setYwqdDm(MhzcConstants.YWQDDM);
                yhjgGxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
                yhjgGxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
                yhjgGxbDO.setLrrq(date);
                yhjgGxbDO.setXgrq(date);
                yhjgGxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
                yhjgGxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
                yhjgGxbDO.setSjtbSj(date);
                insertList.add(yhjgGxbDO);
            });
            yhjgGxbMapper.insertBatch(insertList);
        }

        final List<String> delJguuidList = bindBatchVO.getDelJguuidList();
        if (!GyUtils.isNull(delJguuidList)){
            delJguuidList.forEach(jguuid -> yhjgGxbMapper.deleteByuuid(zzuuid, jguuid,yhuuid));
        }

        return true;
    }

    @Override
    public void upPicUrl(String url) {
        ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = new ZnsbMhqxYhxxbDO();
        znsbMhqxYhxxbDO.setTxdzurl(url);
        znsbMhqxYhxxbDO.setYhUuid(ZnsbSessionUtils.getYhUuid());
        yhxxbMapper.updateById(znsbMhqxYhxxbDO);
    }

    @Override
    public UserInfoVO getPicUrl() {
        String yhUuid = ZnsbSessionUtils.getYhUuid();
        if (GyUtils.isNull(yhUuid)){
            return null;
        }
        ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = yhxxbMapper.selectById(yhUuid);
        UserInfoVO vo = new UserInfoVO();
        vo.setTxdzurl(znsbMhqxYhxxbDO.getTxdzurl());
        return vo;
    }

    @Override
    public String sfgly() {
        String yhUuid = ZnsbSessionUtils.getYhUuid();
        if (sfht()){
            return "Y";
        }

        List<ZnsbMhqxZzxxbDO> zzuuid = zzxxbMapper.getGjdZzuuid();
        if (GyUtils.isNull(zzuuid)){
            return "N";
        }

        List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByUuid(yhUuid, zzuuid.get(0).getZzuuid());
        List<String> collect = znsbMhqxYhjsGxbDOS.stream().map(ZnsbMhqxYhjsGxbDO::getJsuuid).collect(Collectors.toList());
        if (GyUtils.isNull(collect)){
            return "N";
        }
        List<ZnsbMhqxJsqxGxbDO> znsbMhqxJsqxGxbDOS = jsqxGxbMapper.selectQxByJsuuids(collect);
        List<String> collect1 = znsbMhqxJsqxGxbDOS.stream().map(ZnsbMhqxJsqxGxbDO::getQxuuid1).collect(Collectors.toList());
        if (GyUtils.isNull(qxxxbMapper.sfglyByQxuuids(collect1))){
            return "N";
        }else {
            return "Y";
        }

    }

    @Override
    public boolean sfht(){
        String yhuuid = ZnsbSessionUtils.getYhUuid();
        log.info("调用是否后台用户接口，Session取值yhuuid:{}",yhuuid);
        ZnsbMhqxHtyhxxbDO htyhxxByid = htyhxxbMapper.getHtyhxxByid(yhuuid);
        if (GyUtils.isNull(htyhxxByid)){
            return false;
        }else {
            return true;
        }
    }

    @Override
    public Map<String, List<ZnsbMhqxYhxxbDO>> getYhidByDlzh(List<String> dlzhs) {
        QueryWrapper<ZnsbMhqxYhxxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxYhxxbDO::getYhUuid,ZnsbMhqxYhxxbDO::getDlzh,ZnsbMhqxYhxxbDO::getZsxm1)
                .in(ZnsbMhqxYhxxbDO::getDlzh,dlzhs);
        List<ZnsbMhqxYhxxbDO> znsbMhqxYhxxbDOS = yhxxbMapper.selectList(wrapper);
        if (GyUtils.isNull(znsbMhqxYhxxbDOS)){
            return null;
        }else {
            return znsbMhqxYhxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxYhxxbDO::getDlzh));
        }
    }

    @Override
    public void yhjsBatch(List<ZnsbMhqxYhjsGxbDO> yhjss) {
        final Date date = new Date();
        List<ZnsbMhqxYhjsGxbDO> collect = yhjss.stream().map(v -> {
            String jsuuid = v.getJsuuid();
            ZnsbMhqxJsxxbDO znsbMhqxJsxxbDO = jsxxbMapper.selectJsxxByUuid(jsuuid);
            v.setJsmc(GyUtils.isNull(znsbMhqxJsxxbDO)?"":znsbMhqxJsxxbDO.getJsmc());
            v.setUuid(IdUtil.fastSimpleUUID());
            v.setYwqdDm(MhzcConstants.YWQDDM);
            v.setLrrq(date);
            v.setXgrq(date);
            v.setLrrsfid(ZnsbSessionUtils.getYhUuid());
            v.setXgrsfid(ZnsbSessionUtils.getYhUuid());
            v.setSjcsdq(MhzcConstants.SJCSDQ);
            v.setSjgsdq(MhzcConstants.SJGSDQ);
            v.setSjtbSj(date);
            return v;
        }).collect(Collectors.toList());
        yhjsGxbMapper.insertBatch(collect);
    }

    @Override
    public void savescdluuid() {
        ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = new ZnsbMhqxYhxxbDO();
        znsbMhqxYhxxbDO.setScdljguuid(ZnsbSessionUtils.getJguuid());
        znsbMhqxYhxxbDO.setScdlzzuuid(ZnsbSessionUtils.getZzuuid());
        znsbMhqxYhxxbDO.setYhUuid(ZnsbSessionUtils.getYhUuid());
        yhxxbMapper.updateById(znsbMhqxYhxxbDO);

    }

    @Override
    public Integer forceResetPassword(AdminResetPasswordVO vo) {
        Integer integer = resetPassword(vo);
        if (integer>0){
            RedisUtils.delete(formatKey(ZnsbSessionUtils.getYhUuid()));
        }
        return integer;
    }

    @Override
    public YhxxVO getUserInfoById(String yhUuid) {
        YhxxVO userInfoVO = new YhxxVO();
        ZnsbMhqxYhxxbDO znsbMhqxYhxxbDO = yhxxbMapper.selectById(yhUuid);
        if (!GyUtils.isNull(znsbMhqxYhxxbDO) && !GyUtils.isNull(znsbMhqxYhxxbDO.getZsxm1())){
            BeanUtils.copyBean(znsbMhqxYhxxbDO,userInfoVO);
            return userInfoVO;
        }else {
            if ("000001".equals(CacheUtils.getXtcs("**********"))){
                LqyqGlhtUserDO lqyqGlhtUserDO = lqyqGlhtUserMapper.selectById(yhUuid);
                userInfoVO.setZsxm1(lqyqGlhtUserDO.getRealname());
            }
        }
        return userInfoVO;
    }

    @Override
    public List<YhxxVO> getUserInfoByIds(UserInfoByIdsReqVO reqVO) {
        List<String> ids = reqVO.getIds();
        List<ZnsbMhqxYhxxbDO> znsbMhqxYhxxbDOS = yhxxbMapper.selectByUuids(ids);
        return BeanUtils.toBean(znsbMhqxYhxxbDOS, YhxxVO.class);
    }

    @Override
    public void syncYGSWUser(String type,int pageIndex) throws Exception {
        int pageSize = 100;
        if (pageIndex>5000) throw new IllegalStateException("YGSW-用户同步异常");
        log.info("ygsw用户同步开始，入参：type:{}，pageIndex:{}",type,pageIndex);
        String ygsw = CacheUtils.dm2mc("xt_xtcs","MHZC-YGSW-USERSYNC");
        if (GyUtils.isNull(ygsw)) throw new IllegalStateException("YGSW-USERSYNC相关配置为空，请联系运维");
        String[] split = ygsw.split(";");
        String baseurl = split[0];
        String clientId = split[1];
        String id = split[2];
        String pw = split[3];
        String operationCode = split[4];
        log.info("ygsw单点认证getToken_url：{}",baseurl);
        Map<String,Object> map = new HashMap<>();
        map.put("id",id);
        map.put("pw", YgswUtil.encrypt(pw));
        map.put("action","login");
        String realUrl = baseurl+"?id="+id+"&pw="+YgswUtil.encrypt(pw)+"&action=login";
        log.info("ygsw单点认证getToke接口开始，url：{}，入参：{},operationCode:{},clientId:{}",realUrl,map,operationCode,clientId);
        String body = HttpRequest.post(realUrl)
                .header("OperationCode",operationCode)
                .header("ClientId",clientId)
                .execute().body();
        log.info("ygsw单点认证getToke接口返回：{}",body);
        if (!GyUtils.isNull(body)){
            ResponseDTO responseDTO = JsonUtils.toBean(body, ResponseDTO.class);
            if (!GyUtils.isNull(responseDTO)){
                LinkedTreeMap<String, Object> result = responseDTO.getData();
                Integer state = (Integer) result.get("state");
                if (1!=state) throw new IllegalStateException("获取授权码失败，错误代码："+state);
                LinkedHashMap<String, Object> resMap = (LinkedHashMap<String, Object>) result.get("logintoken");
                if (GyUtils.isNull(resMap)) throw new IllegalStateException("获取logintoken失败");
                String authorization = (String) resMap.get("Authorization");
                if (GyUtils.isNull(authorization)) throw new IllegalStateException("获取authorization失败");
                String yhurl = split[5];
                String yhoperationCode = split[6];
                String masterId = split[7];
                Map<String,Object> request = new HashMap<>();
                Map<String,Object> map2 = new HashMap<>();
                map2.put("masterId",masterId);
                if (GyUtils.isNull(type)||!"all".equals(type)){
                    List<Map<String,Object>> list = new ArrayList<>();
                    Map<String,Object> filter = new HashMap<>();
                    filter.put("field","SYS_MODIFYDATE_");
                    filter.put("op","1");
                    filter.put("value",getLastDayNYRSFM());
                    filter.put("rl","1");
                    list.add(filter);
                    map2.put("filter", list);
                }
                map2.put("pageIndex",String.valueOf(pageIndex));
                map2.put("pageSize",String.valueOf(pageSize));

                request.put("request",map2);
                log.info("ygsw批量查询人员信息接口开始，url：{}，入参：{},operationCode:{},clientId:{},loginId:{},Authorization:{}"
                        ,yhurl,request,yhoperationCode,clientId,id,authorization);
                String body2 = HttpRequest.post(yhurl)
                        .header("OperationCode",yhoperationCode)
                        .header("loginId",id)
                        .header("Authorization",authorization)
                        .header("ClientId",clientId)
                        .body(JsonUtils.toJson(request))
                        .execute().body();
                log.info("ygsw批量查询人员信息接口返回：{}",body2);
                if (!GyUtils.isNull(body2)) {
                    Map<String,Object> respMap = JsonUtils.toMap(body2);
                    if (!GyUtils.isNull(respMap)) {
                        List<Map<String,Object>> result2 = (List<Map<String, Object>>) respMap.get("data");
                        saveBatchYgswYhxx(result2);
                        if (result2.size() == pageSize) syncYGSWUser(type,pageIndex+1);
                    }
                }
            }else {
                throw new IllegalStateException(!GyUtils.isNull(responseDTO)?responseDTO.getMessage():"响应参数为空");
            }
        }
    }

    public void saveBatchYgswYhxx(List<Map<String,Object>> list) {
        if (GyUtils.isNull(list)) return;
        List<ZnsbMhqxYhxxbDO> users = new ArrayList<>();
        list.forEach(m -> {
            if (!GyUtils.isNull(m)){
                List<Map<String,Object>> ryxxList = (List<Map<String, Object>>) m.get("RY_XXB");
                if (!GyUtils.isNull(ryxxList)&&ryxxList.size()>0){
                    Map<String, Object> ryxx = ryxxList.get(0);
                    if (!GyUtils.isNull(ryxx.get("SYS_STATE_")) && 3 == (Integer) ryxx.get("SYS_STATE_") && !GyUtils.isNull(ryxx.get("EMPLOYEE_STATUS")) && "1".equals(ryxx.get("EMPLOYEE_STATUS"))){
                        String gh = (String) ryxx.get("EMPLOYEE_CODE");
                        ZnsbMhqxYhxxbDO userByDlzh = yhxxbMapper.getUserByDlzh(gh);
                        if (GyUtils.isNull(userByDlzh)){
                            ZnsbMhqxYhxxbDO yhxxbDO = new ZnsbMhqxYhxxbDO();
                            yhxxbDO.setYhUuid(IdUtil.fastSimpleUUID());
                            yhxxbDO.setYhlx("0");
                            yhxxbDO.setSdbz("N");
                            yhxxbDO.setZsxm1(GyUtils.isNull(ryxx.get("EMPLOYEE_NAME")) ? "" : (String) ryxx.get("EMPLOYEE_NAME"));
                            yhxxbDO.setYxdz(GyUtils.isNull(ryxx.get("EMAIL")) ? "" : (String) ryxx.get("EMAIL"));
                            yhxxbDO.setSjhm1(GyUtils.isNull(ryxx.get("CELL_PHONE_NUMBER")) ? "" : (String) ryxx.get("CELL_PHONE_NUMBER"));
                            yhxxbDO.setGh(GyUtils.isNull(ryxx.get("EMPLOYEE_CODE")) ? "" : (String) ryxx.get("EMPLOYEE_CODE"));
                            yhxxbDO.setYxbz("Y");
                            yhxxbDO.setDlzh(GyUtils.isNull(ryxx.get("EMPLOYEE_CODE")) ? "" : (String) ryxx.get("EMPLOYEE_CODE"));
                            yhxxbDO.setDlmm(MD5.create().digestHex("Sungrow@123"));
                            yhxxbDO.setSfzjlx("201");
                            yhxxbDO.setYwqdDm(MhzcConstants.YWQDDM);
                            yhxxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
                            yhxxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
                            Date date = new Date();
                            yhxxbDO.setLrrq(date);
                            yhxxbDO.setXgrq(date);
                            yhxxbDO.setLrrsfid("ZNSB_YGSW");
                            yhxxbDO.setXgrsfid("ZNSB_YGSW");
                            yhxxbDO.setSjtbSj(date);
                            users.add(yhxxbDO);
                        }
                    }
                }
            }
        });
        yhxxbMapper.insertBatch(users);
    }

    private String getLastDayNYRSFM(){
        // 获取当前时间戳
        long currentTimeStamp = System.currentTimeMillis();

        // 创建一个Calendar实例，并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(currentTimeStamp);

        // 将Calendar实例的时间向前推一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 获取前一天的时间
        return sdf.format(calendar.getTime());
    }

    private static String formatKey(String yhuuid) {
        if (GyUtils.isNull(yhuuid)) return "";
        String check = "check_password:%s";
        return String.format(check, yhuuid);
    }

}
