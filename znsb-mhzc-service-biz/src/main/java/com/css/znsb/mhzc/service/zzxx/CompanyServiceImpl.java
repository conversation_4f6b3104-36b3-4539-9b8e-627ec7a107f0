package com.css.znsb.mhzc.service.zzxx;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.servlet.ServletUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.redis.utils.RedisUtils;
import com.css.znsb.framework.session.ZnsbSessionManager;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.mhzc.constants.MhzcConstants;
import com.css.znsb.mhzc.mapper.gxb.ZnsbMhqxYhjgGxbMapper;
import com.css.znsb.mhzc.mapper.gxb.ZnsbMhqxZzjgGxbMapper;
import com.css.znsb.mhzc.mapper.nsrxx.ZnsbMhzcNsrzgxxMapper;
import com.css.znsb.mhzc.mapper.nsrxx.ZnsbMhzcQybqxxMapper;
import com.css.znsb.mhzc.mapper.nsrxx.ZnsbMhzcQyjbxxmxMapper;
import com.css.znsb.mhzc.mapper.nsrxx.ZnsbMhzcSfzrdxxMapper;
import com.css.znsb.mhzc.mapper.xxb.ZnsbMhqxJgxxbMapper;
import com.css.znsb.mhzc.mapper.xxb.ZnsbMhqxYhxxbMapper;
import com.css.znsb.mhzc.mapper.xxb.ZnsbMhqxZzxxbMapper;
import com.css.znsb.mhzc.pojo.company.*;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxYhjgGxbDO;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxYhjsGxbDO;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxZzjgGxbDO;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxZzyhGxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxJgxxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxJsxxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxYhxxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxZzxxbDO;
import com.css.znsb.mhzc.pojo.dto.nsrxx.ZnsbMhzcNsrzgxxDTO;
import com.css.znsb.mhzc.pojo.dto.nsrxx.ZnsbMhzcQybqxxDTO;
import com.css.znsb.mhzc.pojo.dto.nsrxx.ZnsbMhzcQyjbxxmxDTO;
import com.css.znsb.mhzc.pojo.dto.nsrxx.ZnsbMhzcSfzrdxxDTO;
import com.css.znsb.mhzc.pojo.vo.company.*;
import com.css.znsb.mhzc.pojo.vo.user.UserExcelMbVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxBaseVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxVO;
import com.css.znsb.mhzc.pojo.yhxx.RyxxVO;
import com.css.znsb.mhzc.service.role.RoleService;
import com.css.znsb.mhzc.service.yhxx.UserService;
import com.css.znsb.mhzc.util.MhzcGyUtils;
import com.css.znsb.sso.api.yhxx.SwitchSessionApi;
import com.css.znsb.sso.pojo.yhxx.SwitchCompanyResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CompanyServiceImpl extends ServiceImpl<ZnsbMhqxJgxxbMapper, ZnsbMhqxJgxxbDO> implements CompanyService {

    @Resource
    private ZzxxService zzxxService;

    @Resource
    private ZnsbMhqxJgxxbMapper jgxxbMapper;

    @Resource
    private ZnsbMhqxZzjgGxbMapper zzjgGxbMapper;

    @Resource
    private ZnsbMhqxYhjgGxbMapper yhjgGxbMapper;

    @Resource
    private ZnsbMhqxZzxxbMapper zzxxbMapper;

    @Resource
    private ZnsbMhzcQyjbxxmxMapper qyjbxxmxMapper;

    @Resource
    private ZnsbMhzcSfzrdxxMapper sfzrdxxMapper;

    @Resource
    private ZnsbMhzcNsrzgxxMapper nsrzgxxMapper;

    @Resource
    private ZnsbMhzcQybqxxMapper qybqxxMapper;

    @Resource
    private SwitchSessionApi switchSessionApi;

    @Resource
    private UserService userService;

    @Resource
    private RoleService roleService;

    @Resource
    private ZnsbMhqxYhxxbMapper yhxxbMapper;

    @Override
    public List<CompanyResVO> getAllJgOfZz(ZzxxBaseVO reqVO) {
        List<String> zzuuids = new ArrayList<>();
        String zzuuid = reqVO.getZzuuid();
        String zszzbz = reqVO.getZszzbz1();
        String jgmc1 = reqVO.getJgmc1();
        if (GyUtils.isNull(zzuuid)) {//如果没有选定组织 获取当前用户下的组织
            final List<ZzxxVO> allZzOfCurrentUser = zzxxService.getAllZzOfCurrentUser();
            if (!GyUtils.isNull(allZzOfCurrentUser)) {
                zzuuids.addAll(allZzOfCurrentUser.stream().map(ZzxxVO::getZzuuid).collect(Collectors.toList()));
            }
        } else {
            zzuuids.add(zzuuid);
        }

        final List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS = zzjgGxbMapper.selectJgByZz(zzuuids,zszzbz);//组织下的机构
        List<CompanyResVO> result = new ArrayList<>();
        if (!GyUtils.isNull(znsbMhqxZzjgGxbDOS)) {
            final List<String> collect = znsbMhqxZzjgGxbDOS.stream().map(ZnsbMhqxZzjgGxbDO::getJguuid1).distinct().collect(Collectors.toList());
            final List<ZnsbMhqxJgxxbDO> znsbMhqxJgxxbDOS = jgxxbMapper.selectJgxxByUuids(collect, jgmc1, null);
            if (GyUtils.isNull(znsbMhqxJgxxbDOS)) {
                return result;
            }
            final Map<String, List<ZnsbMhqxJgxxbDO>> map = znsbMhqxJgxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxJgxxbDO::getJguuid1));
            znsbMhqxZzjgGxbDOS.stream().distinct().collect(Collectors.toList()).forEach(znsbMhqxZzjgGxbDO -> {
                if (!GyUtils.isNull(map.get(znsbMhqxZzjgGxbDO.getJguuid1()))) {
                    final CompanyResVO companyResVO = new CompanyResVO();
                    companyResVO.setZzuuid(znsbMhqxZzjgGxbDO.getZzuuid());
                    final ZnsbMhqxJgxxbDO znsbMhqxJgxxbDO = map.get(znsbMhqxZzjgGxbDO.getJguuid1()).get(0);
                    companyResVO.setJguuid(znsbMhqxZzjgGxbDO.getJguuid1());
                    companyResVO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
                    companyResVO.setNsrsbh(znsbMhqxJgxxbDO.getNsrsbh());
                    companyResVO.setBz(znsbMhqxJgxxbDO.getBz());
                    companyResVO.setSjjguuid(znsbMhqxJgxxbDO.getSjJguuid());
                    companyResVO.setXzqhmc(MhzcGyUtils.getSjxzqhmc(znsbMhqxJgxxbDO.getXzqhszDm()));
                    companyResVO.setShxydm(znsbMhqxJgxxbDO.getShxydm());
                    companyResVO.setQydmz(znsbMhqxJgxxbDO.getQydmz());
                    companyResVO.setZszzbz1(znsbMhqxZzjgGxbDO.getZszzbz1());
                    result.add(companyResVO);
                }
            });
        }
        return result.stream().map(v -> {
            if (GyUtils.isNull(v.getQydmz())) {
                v.setQydmz(null);
            }
            return v;
        }).sorted(Comparator.comparing(
                CompanyResVO::getQydmz,
                Comparator.nullsLast(String::compareTo))
                .thenComparing(CompanyResVO::getJgmc))
                .collect(Collectors.toList());
    }

    @Override
    public List<ZnsbMhqxJgxxbDO> getJgxxByNsrsbh(String nsrsbh){
        return jgxxbMapper.selectByNsrsbh(nsrsbh);
    }

    @Override
    public List<String> getJgidsOfZz(ZzxxBaseVO reqVO) {
        final String zzuuid = reqVO.getZzuuid();
        List<String> result = new ArrayList<>();
        final List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS = zzjgGxbMapper.selectJgByZz(Collections.singletonList(zzuuid),"");
        if (!GyUtils.isNull(znsbMhqxZzjgGxbDOS)) {
            result.addAll(znsbMhqxZzjgGxbDOS.stream().map(ZnsbMhqxZzjgGxbDO::getJguuid1).collect(Collectors.toList()));
        }

        return result;
    }

    @Transactional
    @Override
    public Boolean delete(CompanyBindVO reqVO) {
        final List<String> jguuidList = reqVO.getJguuidList();
        final String zzuuid = reqVO.getZzuuid();
        if (!GyUtils.isNull(jguuidList)) {
            jguuidList.forEach(jguuid -> {
                zzjgGxbMapper.deleteZzJgGx(zzuuid, jguuid);
            });
        }
        return true;
    }

    @Override
    public List<JgxxVO> getAllJgxx(AllCompanyReqVO reqVO) {
        String jgmc = GyUtils.isNull(reqVO.getJgmc()) ? null : reqVO.getJgmc();
        String shxydm = GyUtils.isNull(reqVO.getShxydm()) ? null : reqVO.getShxydm();
        final List<ZnsbMhqxJgxxbDO> znsbMhqxJgxxbDOS = jgxxbMapper.selectAllJgxx(jgmc, shxydm);
        List<JgxxVO> result = new ArrayList<>();
        if (!GyUtils.isNull(znsbMhqxJgxxbDOS)) {
            Map<String, List<ZnsbMhqxZzxxbDO>> collect1 = new HashMap<>();
            Map<String, List<ZnsbMhqxZzjgGxbDO>> collect2 = new HashMap<>();
            final Map<String, List<ZnsbMhqxJgxxbDO>> collect = znsbMhqxJgxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxJgxxbDO::getJguuid1));
            Set<String> set = collect.keySet();
            List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS = zzjgGxbMapper.selectZzByJg(set);
            if (!GyUtils.isNull(znsbMhqxZzjgGxbDOS)) {
                collect2 = znsbMhqxZzjgGxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxZzjgGxbDO::getJguuid1));
                Set<String> zzuuids = znsbMhqxZzjgGxbDOS.stream().map(ZnsbMhqxZzjgGxbDO::getZzuuid).collect(Collectors.toSet());
                List<ZnsbMhqxZzxxbDO> zzList = zzxxbMapper.selectZzxxByZzuuidSet(zzuuids);
                if (!GyUtils.isNull(zzList)) {
                    collect1 = zzList.stream().collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getZzuuid));
                }
            }

            for (ZnsbMhqxJgxxbDO znsbMhqxJgxxbDO : znsbMhqxJgxxbDOS) {
                final JgxxVO jgxxVO = new JgxxVO();
                jgxxVO.setJguuid(znsbMhqxJgxxbDO.getJguuid1());
                jgxxVO.setNsrsbh(znsbMhqxJgxxbDO.getNsrsbh());
                jgxxVO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
                jgxxVO.setSjjguuid(znsbMhqxJgxxbDO.getSjJguuid());
                jgxxVO.setBz(znsbMhqxJgxxbDO.getBz());
                //jgxxVO.setSjjgmc(GyUtils.isNull(znsbMhqxJgxxbDO.getSjJguuid())?"": collect.get(znsbMhqxJgxxbDO.getSjJguuid()).get(0).getJgmc1());

                if (!GyUtils.isNull(collect2)) {
                    List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS1 = collect2.get(znsbMhqxJgxxbDO.getJguuid1());
                    if (!GyUtils.isNull(znsbMhqxZzjgGxbDOS1)) {
                        List<String> zzuuidsOfJg = znsbMhqxZzjgGxbDOS1.stream().map(ZnsbMhqxZzjgGxbDO::getZzuuid).distinct().collect(Collectors.toList());
                        StringJoiner stringJoiner = new StringJoiner(",");
                        for (String zzuuid : zzuuidsOfJg) {
                            List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = collect1.get(zzuuid);
                            if (!GyUtils.isNull(znsbMhqxZzxxbDOS)) {
                                znsbMhqxZzxxbDOS.stream().sorted(Comparator.comparing(ZnsbMhqxZzxxbDO::getXh)).forEach(znsbMhqxZzxxbDO -> stringJoiner.add(znsbMhqxZzxxbDO.getZzmc2()));
                            }
                        }
                        jgxxVO.setSszz(stringJoiner.toString());
                    }
                }

                result.add(jgxxVO);
            }
        }

        return result;
    }

    @Override
    public Boolean insert(CompanyBindVO reqVO) {
        final String zzuuid = reqVO.getZzuuid();
        final List<String> jgList = reqVO.getJguuidList();
        List<ZnsbMhqxZzjgGxbDO> list = new ArrayList<>();
        jgList.forEach(jguuid -> {
            final ZnsbMhqxZzjgGxbDO znsbMhqxZzjgGxbDO = new ZnsbMhqxZzjgGxbDO();
            znsbMhqxZzjgGxbDO.setUuid(IdUtil.fastSimpleUUID());
            znsbMhqxZzjgGxbDO.setZzuuid(zzuuid);
            znsbMhqxZzjgGxbDO.setJguuid1(jguuid);
            znsbMhqxZzjgGxbDO.setYwqdDm("ZNSB.MHZC");
            znsbMhqxZzjgGxbDO.setLrrq(new Date());
            znsbMhqxZzjgGxbDO.setXgrq(new Date());
            znsbMhqxZzjgGxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
            znsbMhqxZzjgGxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
            znsbMhqxZzjgGxbDO.setSjcsdq("00000000000");
            znsbMhqxZzjgGxbDO.setSjgsdq("00000000000");
            znsbMhqxZzjgGxbDO.setSjtbSj(new Date());
            list.add(znsbMhqxZzjgGxbDO);
        });

        return zzjgGxbMapper.saveBatch(list);
    }

    @Override
    public SwitchZzResVO switchZz(SwitchZzReqVO reqVO) {
        final SwitchZzResVO switchZzResVO = new SwitchZzResVO();
        switchZzResVO.setZzuuid(reqVO.getZzuuid());
        final Cookie[] cookies = ServletUtils.getRequest().getCookies();
        String token = null;
        if (!GyUtils.isNull(cookies)) {
            for (Cookie cookie : cookies) {
                final String name = cookie.getName();
                if ("ACCESS_TOKEN".equals(name)) {
                    token = cookie.getValue();
                    break;
                }
            }
        }
        final CommonResult<SwitchCompanyResDTO> dto = switchSessionApi.switchZz(token, reqVO.getZzuuid());
        final SwitchCompanyResDTO data = dto.getData();
        if (!GyUtils.isNull(data.getJguuid())) {
            switchZzResVO.setJguuid(data.getJguuid());
            switchZzResVO.setJgmc(data.getJgmc());
            switchZzResVO.setNsrsbh(data.getNsrsbh());
        }

        return switchZzResVO;
    }

    @Override
    public SwitchJgResVO switchJg(SwitchJgReqVO reqVO) {
        final SwitchJgResVO switchJgResVO = new SwitchJgResVO();
        final Cookie[] cookies = ServletUtils.getRequest().getCookies();
        String token = null;
        if (!GyUtils.isNull(cookies)) {
            for (Cookie cookie : cookies) {
                final String name = cookie.getName();
                if ("ACCESS_TOKEN".equals(name)) {
                    token = cookie.getValue();
                    break;
                }
            }
        }
        final CommonResult<SwitchCompanyResDTO> result = switchSessionApi.switchCompany(token, reqVO.getJguuid());
        final SwitchCompanyResDTO data = result.getData();
        if (!GyUtils.isNull(data.getJguuid())) {
            switchJgResVO.setJguuid(data.getJguuid());
            switchJgResVO.setJgmc(data.getJgmc());
            switchJgResVO.setNsrsbh(data.getNsrsbh());
            switchJgResVO.setQydmz(data.getQydmz());
            switchJgResVO.setQylxz(data.getQylxz());
        } else {
            switchJgResVO.setJguuid(ZnsbSessionUtils.getJguuid());
            switchJgResVO.setJgmc(ZnsbSessionUtils.getJgmc());
            switchJgResVO.setNsrsbh(ZnsbSessionUtils.getNsrsbh());
            switchJgResVO.setQydmz(ZnsbSessionUtils.getQydmz());
            switchJgResVO.setQylxz(ZnsbSessionUtils.getQylxz());
        }

        return switchJgResVO;
    }


    @Override
    public Boolean bindJgxx(CompanyBindBatchVO bindBatchVO) {
        final String zzuuid = bindBatchVO.getZzuuid();

        final Date date = new Date();
        final List<String> addJguuidList = bindBatchVO.getAddJguuidList();
        if (!GyUtils.isNull(addJguuidList)) {
            List<ZnsbMhqxZzjgGxbDO> insertList = new ArrayList<>();
            addJguuidList.forEach(jguuid -> {
                final ZnsbMhqxZzjgGxbDO znsbMhqxZzjgGxbDO = new ZnsbMhqxZzjgGxbDO();
                znsbMhqxZzjgGxbDO.setUuid(IdUtil.fastSimpleUUID());
                znsbMhqxZzjgGxbDO.setZzuuid(zzuuid);
                znsbMhqxZzjgGxbDO.setJguuid1(jguuid);
                znsbMhqxZzjgGxbDO.setYwqdDm(MhzcConstants.YWQDDM);
                znsbMhqxZzjgGxbDO.setLrrq(date);
                znsbMhqxZzjgGxbDO.setXgrq(date);
                znsbMhqxZzjgGxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
                znsbMhqxZzjgGxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
                znsbMhqxZzjgGxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
                znsbMhqxZzjgGxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
                znsbMhqxZzjgGxbDO.setSjtbSj(date);
                insertList.add(znsbMhqxZzjgGxbDO);
            });
            zzjgGxbMapper.insertBatch(insertList);
        }

        final List<String> delJguuidList = bindBatchVO.getDelJguuidList();
        if (!GyUtils.isNull(delJguuidList)) {
            delJguuidList.forEach(jguuid -> zzjgGxbMapper.deleteZzJgGx(zzuuid, jguuid));
        }

        return true;
    }


    @Override
    public List<CompanyInfoResDTO> getAllCompanyInfo() {
        List<CompanyInfoResDTO> result = new ArrayList<>();

        final List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectAllJgxx(null, null);
        if (!GyUtils.isNull(jgxxbDOS)) {

            final List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS = zzjgGxbMapper.selectAll();
            if (!GyUtils.isNull(znsbMhqxZzjgGxbDOS)) {
                final Map<String, List<ZnsbMhqxZzjgGxbDO>> collect = znsbMhqxZzjgGxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxZzjgGxbDO::getJguuid1));
                jgxxbDOS.forEach(znsbMhqxJgxxbDO -> {
                    final CompanyInfoResDTO companyInfoResDTO = new CompanyInfoResDTO();
                    BeanUtil.copyProperties(znsbMhqxJgxxbDO, companyInfoResDTO, true);
                    companyInfoResDTO.setJguuid(znsbMhqxJgxxbDO.getJguuid1());
                    companyInfoResDTO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
                    companyInfoResDTO.setZzuuids(Optional.ofNullable(collect.get(znsbMhqxJgxxbDO.getJguuid1())).orElse(new ArrayList<>()).stream().map(ZnsbMhqxZzjgGxbDO::getZzuuid).collect(Collectors.toList()));
                    result.add(companyInfoResDTO);
                });
            } else {
                jgxxbDOS.forEach(znsbMhqxJgxxbDO -> {
                    final CompanyInfoResDTO companyInfoResDTO = new CompanyInfoResDTO();
                    BeanUtil.copyProperties(znsbMhqxJgxxbDO, companyInfoResDTO, true);
                    companyInfoResDTO.setJguuid(znsbMhqxJgxxbDO.getJguuid1());
                    companyInfoResDTO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
                    result.add(companyInfoResDTO);
                });
            }
        }
        return result;
    }


    @Override
    public List<CompanyInfoWithStatusDTO> getAllCompanyInfoWithStatus(String yhuuid, String zzuuid) {
        List<CompanyInfoWithStatusDTO> result = new ArrayList<>();

        final List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectAllJgxx(null, null);
        if (!GyUtils.isNull(jgxxbDOS)) {
            final List<ZnsbMhqxYhjgGxbDO> yhjgGxbDOS = yhjgGxbMapper.selectAllJgByYhZz(yhuuid, zzuuid);
            List<String> kczJguuids = new ArrayList<>();
            if (!GyUtils.isNull(yhjgGxbDOS)) {
                kczJguuids.addAll(yhjgGxbDOS.stream().map(ZnsbMhqxYhjgGxbDO::getJguuid1).collect(Collectors.toList()));
            }
            jgxxbDOS.forEach(znsbMhqxJgxxbDO -> {
                final CompanyInfoWithStatusDTO companyInfoResDTO = new CompanyInfoWithStatusDTO();
                BeanUtil.copyProperties(znsbMhqxJgxxbDO, companyInfoResDTO, true);
                companyInfoResDTO.setJguuid(znsbMhqxJgxxbDO.getJguuid1());
                companyInfoResDTO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
                companyInfoResDTO.setSfkcz(kczJguuids.contains(znsbMhqxJgxxbDO.getJguuid1()));
                result.add(companyInfoResDTO);
            });
        }

        return result;
    }

    @Override
    public List<SwitchJgVO> switchJgList() {
//        List<SwitchJgVO> result = new ArrayList<>();
        final String yhUuid = ZnsbSessionUtils.getYhUuid();
        final String zzuuid = ZnsbSessionUtils.getZzuuid();
        QueryWrapper<ZnsbMhqxYhjgGxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxYhjgGxbDO::getZzuuid, ZnsbMhqxYhjgGxbDO::getJguuid1)
                .eq(ZnsbMhqxYhjgGxbDO::getYhUuid, yhUuid);
        List<ZnsbMhqxYhjgGxbDO> znsbMhqxYhjgGxbDOS = yhjgGxbMapper.selectList(wrapper);//查询当前登录用户下的所有组织机构信息
        if (GyUtils.isNull(znsbMhqxYhjgGxbDOS)) {
            return null;
        }
        Set<String> zzids = znsbMhqxYhjgGxbDOS.stream().map(ZnsbMhqxYhjgGxbDO::getZzuuid).collect(Collectors.toSet());//获取当前登录用户下的所有组织id
        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.selectZzxxByZzuuidSet(zzids);//获取当前登录用户下的所有组织信息
        if (GyUtils.isNull(znsbMhqxZzxxbDOS)) {
            return null;
        }
        Map<String, List<ZnsbMhqxZzxxbDO>> collect1 = znsbMhqxZzxxbDOS.stream()
                .filter(v -> !GyUtils.isNull(v.getSjZzuuid())).collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getSjZzuuid));//过滤掉没有 上级组织 的组织并按上级组织分组

        List<ZnsbMhqxZzxxbDO> collect2 = znsbMhqxZzxxbDOS.stream().filter(v -> zzuuid.equals(v.getZzuuid())).collect(Collectors.toList());//拿到本级组织
        if (GyUtils.isNull(collect2)) {
            return null;
        }
        List<ZnsbMhqxZzxxbDO> bxjzzList = new ArrayList<>();
        bxjzzList.add(collect2.get(0));//将本级组织先添加到本下级组织列表中
        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS1 = bxjZz(zzuuid, collect1, bxjzzList);//递归加工组织信息 获取本下级的所有组织信息
        if (GyUtils.isNull(znsbMhqxZzxxbDOS1)) {
            return null;
        }
        List<String> collect3 = znsbMhqxZzxxbDOS1.stream().map(ZnsbMhqxZzxxbDO::getZzuuid).distinct().collect(Collectors.toList());//拿到本下级组织uuidList
        List<String> jguuids = znsbMhqxYhjgGxbDOS.stream()
                .filter(v -> collect3.contains(v.getZzuuid())).map(ZnsbMhqxYhjgGxbDO::getJguuid1).distinct().collect(Collectors.toList());//获取本下级组织相对应的机构UUID

        final List<SwitchJgVO> jgxxbDOS = jgxxbMapper.getSwitchJgxxList(jguuids);//通过JGUUID获取机构信息
        if (GyUtils.isNull(jgxxbDOS)) return null;
        return jgxxbDOS.stream().map(jgxx -> {
            String qydmz = jgxx.getQydmz();
            jgxx.setJgmc(GyUtils.isNull(qydmz) ? jgxx.getJgmc() : qydmz + "|" + jgxx.getJgmc());
            jgxx.setXzqhmc(CacheUtils.dm2mc("dm_gy_xzqh", jgxx.getXzqhszDm()));
            return jgxx;
        }).sorted(Comparator.comparing(
                SwitchJgVO::getQydmz,
                Comparator.nullsLast(String::compareTo))
                .thenComparing(SwitchJgVO::getJgmc)).collect(Collectors.toList());
    }

    @Override
    public List<SwitchJgVO> switchKqJgList() {
        final String yhUuid = ZnsbSessionUtils.getYhUuid();
        final String zzuuid = ZnsbSessionUtils.getZzuuid();
        QueryWrapper<ZnsbMhqxYhjgGxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxYhjgGxbDO::getZzuuid, ZnsbMhqxYhjgGxbDO::getJguuid1)
                .eq(ZnsbMhqxYhjgGxbDO::getYhUuid, yhUuid);
        List<ZnsbMhqxYhjgGxbDO> znsbMhqxYhjgGxbDOS = yhjgGxbMapper.selectList(wrapper);//查询当前登录用户下的所有组织机构信息
        if (GyUtils.isNull(znsbMhqxYhjgGxbDOS)) {
            return null;
        }
        Set<String> zzids = znsbMhqxYhjgGxbDOS.stream().map(ZnsbMhqxYhjgGxbDO::getZzuuid).collect(Collectors.toSet());//获取当前登录用户下的所有组织id
        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.selectZzxxByZzuuidSet(zzids);//获取当前登录用户下的所有组织信息
        if (GyUtils.isNull(znsbMhqxZzxxbDOS)) {
            return null;
        }
        Map<String, List<ZnsbMhqxZzxxbDO>> collect1 = znsbMhqxZzxxbDOS.stream()
                .filter(v -> !GyUtils.isNull(v.getSjZzuuid())).collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getSjZzuuid));//过滤掉没有 上级组织 的组织并按上级组织分组

        List<ZnsbMhqxZzxxbDO> collect2 = znsbMhqxZzxxbDOS.stream().filter(v -> zzuuid.equals(v.getZzuuid())).collect(Collectors.toList());//拿到本级组织
        if (GyUtils.isNull(collect2)) {
            return null;
        }
        List<ZnsbMhqxZzxxbDO> bxjzzList = new ArrayList<>();
        bxjzzList.add(collect2.get(0));//将本级组织先添加到本下级组织列表中
        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS1 = bxjZz(zzuuid, collect1, bxjzzList);//递归加工组织信息 获取本下级的所有组织信息
        if (GyUtils.isNull(znsbMhqxZzxxbDOS1)) {
            return null;
        }
        List<String> collect3 = znsbMhqxZzxxbDOS1.stream().map(ZnsbMhqxZzxxbDO::getZzuuid).distinct().collect(Collectors.toList());//拿到本下级组织uuidList
        List<String> jguuids = znsbMhqxYhjgGxbDOS.stream()
                .filter(v -> collect3.contains(v.getZzuuid())).map(ZnsbMhqxYhjgGxbDO::getJguuid1).distinct().collect(Collectors.toList());//获取本下级组织相对应的机构UUID

        List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectList(ZnsbMhqxJgxxbDO::getJguuid1, jguuids);
        if (GyUtils.isNull(jgxxbDOS)) return null;
        List<String> nsrsbhs = jgxxbDOS.stream().map(ZnsbMhqxJgxxbDO::getNsrsbh).collect(Collectors.toList());
        List<SwitchJgVO> switchJgVOS = jgxxbMapper.selectListByNsrs(nsrsbhs);
        return switchJgVOS.stream().map(jgxx -> {
            String qydmz = jgxx.getQydmz();
            jgxx.setJgmc(GyUtils.isNull(qydmz) ? jgxx.getJgmc() : qydmz + "|" + jgxx.getJgmc());
            jgxx.setXzqhmc(CacheUtils.dm2mc("dm_gy_xzqh", jgxx.getXzqhszDm()));
            return jgxx;
        }).sorted(Comparator.comparing(
                SwitchJgVO::getQydmz,
                Comparator.nullsLast(String::compareTo))
                .thenComparing(SwitchJgVO::getJgmc)).collect(Collectors.toList());
    }

    private List<ZnsbMhqxZzxxbDO> bxjZz(String zzuuid, Map<String, List<ZnsbMhqxZzxxbDO>> collect, List<ZnsbMhqxZzxxbDO> result) {
        if (GyUtils.isNull(collect)) {
            return result;
        }
        if (GyUtils.isNull(collect.get(zzuuid))) {
            return result;
        }
        List<ZnsbMhqxZzxxbDO> zzxxbDOS = collect.get(zzuuid);
        result.addAll(zzxxbDOS);
        for (ZnsbMhqxZzxxbDO zzxx : zzxxbDOS) {
            result = bxjZz(zzxx.getZzuuid(), collect, result);
        }
        return result;
    }


    @Override
    public List<BindedJgVO> bindedJgList() {
        List<BindedJgVO> result = new ArrayList<>();
        final String yhUuid = ZnsbSessionUtils.getYhUuid();
        final String zzuuid = ZnsbSessionUtils.getZzuuid();
        final List<ZnsbMhqxYhjgGxbDO> yhjgGxbDOS = yhjgGxbMapper.selectAllJgByYhZz(yhUuid, zzuuid);
        if (!GyUtils.isNull(yhjgGxbDOS)) {
            final List<String> collect = yhjgGxbDOS.stream().map(ZnsbMhqxYhjgGxbDO::getJguuid1).collect(Collectors.toList());
            final List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectJgxxByUuids(collect, null, null);
            jgxxbDOS.forEach(jgxx -> {
                final BindedJgVO bindedJgVO = new BindedJgVO();
                bindedJgVO.setJguuid(jgxx.getJguuid1());
                bindedJgVO.setJgmc(jgxx.getJgmc1());
                bindedJgVO.setNsrsbh(jgxx.getNsrsbh());
                bindedJgVO.setXzqhmc(MhzcGyUtils.getSjxzqhmc(jgxx.getXzqhszDm()));
                bindedJgVO.setBz(jgxx.getBz());
                result.add(bindedJgVO);
            });
        }

        return result;
    }


    @Override
    public CompanyBasicInfoDTO basicInfo(String djxh, String nsrsbh) {
        final CompanyBasicInfoDTO companyBasicInfoDTO = new CompanyBasicInfoDTO();
        final ZnsbMhqxJgxxbDO znsbMhqxJgxxbDO = jgxxbMapper.selectByDjxhAndNsrsbh(djxh, nsrsbh);
        if (!GyUtils.isNull(znsbMhqxJgxxbDO)) {
            BeanUtil.copyProperties(znsbMhqxJgxxbDO, companyBasicInfoDTO, true);
            companyBasicInfoDTO.setJguuid(znsbMhqxJgxxbDO.getJguuid1());
            companyBasicInfoDTO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());

            final ZnsbMhzcQyjbxxmxDTO znsbMhzcQyjbxxmxDTO = qyjbxxmxMapper.selectById(djxh);
            BeanUtil.copyProperties(znsbMhzcQyjbxxmxDTO, companyBasicInfoDTO, true);
        }
        return companyBasicInfoDTO;
    }

    @Override
    public CompanyBasicInfoDTO getZjgBasicInfoForSenmir(String djxh, String nsrsbh) {
        CompanyBasicInfoDTO fzjgBasicInfoDTO = basicInfo(djxh, nsrsbh);
        String qydmz = fzjgBasicInfoDTO.getQydmz();
        if (GyUtils.isNull(qydmz)) {
            return new CompanyBasicInfoDTO();
        }
        String zjgQydmz = qydmz.substring(0, 2) + "00";
        QueryWrapper<ZnsbMhqxJgxxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ZnsbMhqxJgxxbDO::getQydmz, zjgQydmz);
        wrapper.lambda().eq(ZnsbMhqxJgxxbDO::getQylxz, "1");
        List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectList(wrapper);
        if (GyUtils.isNotNull(jgxxbDOS)) {
            ZnsbMhqxJgxxbDO zjgJgxx = jgxxbDOS.get(0);
            String zjgNsrsbh = zjgJgxx.getNsrsbh();
            String zjgDjxh = zjgJgxx.getDjxh();
            return basicInfo(zjgDjxh, zjgNsrsbh);
        }
        return new CompanyBasicInfoDTO();
    }

    @Override
    public List<String> getUuidsByDjxhs(List<String> djxhs) {
        if (!GyUtils.isNull(djxhs)) {
            return jgxxbMapper.getUuidsByDjxhs(djxhs);
        } else {
            return null;
        }
    }

    @Override
    public Page<QyxxglResVO> qyxxgl(QyxxglReqVO reqVO) {
        Page<ZnsbMhqxJgxxbDO> list = jgxxbMapper.qyxxgl(reqVO);
        List<QyxxglResVO> qyxxglResVOS = BeanUtils.toBean(list.getRecords(), QyxxglResVO.class);
        Page<QyxxglResVO> page = new Page<>();
        page.setRecords(qyxxglResVOS);
        page.setSize(list.getSize());
        page.setTotal(list.getTotal());
        page.setPages(list.getPages());
        return page;
    }

    @Override
    public int deleteQyByUuid(QyxxglReqVO reqVO) {
        if ("Y".equals(reqVO.getTbwczt())) {
            return -1;
        }
        jgxxbMapper.deleteById(reqVO.getJguuid1());
        return 0;
    }

    @Override
    public List<CompanyInfoResDTO> getBjCompanyInfo(AllCompanyReqVO baseVO) {
        List<CompanyInfoResDTO> result = new ArrayList<>();
        String zzuuid = baseVO.getZzuuid();
        List<String> list = new ArrayList<>();
        List<ZnsbMhqxJgxxbDO> jgxxbDOS;
        if (!GyUtils.isNull(zzuuid)) {
            list.add(zzuuid);
            final List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS = zzjgGxbMapper.selectJgByZz(list,"");//获取组织下的全部机构uuid
            if (GyUtils.isNull(znsbMhqxZzjgGxbDOS)) {
                return null;
            }
            List<String> uuids = znsbMhqxZzjgGxbDOS.stream().map(ZnsbMhqxZzjgGxbDO::getJguuid1).collect(Collectors.toList());
            jgxxbDOS = jgxxbMapper.selectJgxxByUuids(uuids, baseVO.getJgmc(), baseVO.getShxydm());//查询机构信息
            if (!GyUtils.isNull(jgxxbDOS)) {
                if (!GyUtils.isNull(znsbMhqxZzjgGxbDOS)) {
                    final Map<String, List<ZnsbMhqxZzjgGxbDO>> collect = znsbMhqxZzjgGxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxZzjgGxbDO::getJguuid1));//按jguuid分组
                    jgxxbDOS.forEach(znsbMhqxJgxxbDO -> {
                        final CompanyInfoResDTO companyInfoResDTO = new CompanyInfoResDTO();
                        BeanUtil.copyProperties(znsbMhqxJgxxbDO, companyInfoResDTO, true);
                        companyInfoResDTO.setJguuid(znsbMhqxJgxxbDO.getJguuid1());
                        companyInfoResDTO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
                        companyInfoResDTO.setXzqhmc(MhzcGyUtils.getSjxzqhmc(znsbMhqxJgxxbDO.getXzqhszDm()));
                        companyInfoResDTO.setZzuuids(Optional.ofNullable(collect.get(znsbMhqxJgxxbDO.getJguuid1())).orElse(new ArrayList<>()).stream().map(ZnsbMhqxZzjgGxbDO::getZzuuid).collect(Collectors.toList()));
                        result.add(companyInfoResDTO);
                    });
                } else {
                    jgxxbDOS.forEach(znsbMhqxJgxxbDO -> {
                        final CompanyInfoResDTO companyInfoResDTO = new CompanyInfoResDTO();
                        BeanUtil.copyProperties(znsbMhqxJgxxbDO, companyInfoResDTO, true);
                        companyInfoResDTO.setJguuid(znsbMhqxJgxxbDO.getJguuid1());
                        companyInfoResDTO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
                        result.add(companyInfoResDTO);
                    });
                }
            }
        } else {
            jgxxbDOS = jgxxbMapper.selectAllJgxx("", "");
            jgxxbDOS.forEach(znsbMhqxJgxxbDO -> {
                final CompanyInfoResDTO companyInfoResDTO = new CompanyInfoResDTO();
                BeanUtil.copyProperties(znsbMhqxJgxxbDO, companyInfoResDTO, true);
                companyInfoResDTO.setJguuid(znsbMhqxJgxxbDO.getJguuid1());
                companyInfoResDTO.setJgmc(znsbMhqxJgxxbDO.getJgmc1());
                result.add(companyInfoResDTO);
            });
        }
        return result;
    }

    @Override
    public void addQyxx(List<QyxxglReqVO> list) {
        final Date date = new Date();
        List<ZnsbMhqxJgxxbDO> znsbMhqxJgxxbDOS = BeanUtils.toBean(list, ZnsbMhqxJgxxbDO.class);
        List<ZnsbMhqxJgxxbDO> collect = znsbMhqxJgxxbDOS.stream().map(v -> {
            v.setShxydm(v.getNsrsbh());
            v.setDjxh(ZnsbSessionUtils.getDjxh());
            v.setYxbz("Y");
            if (GyUtils.isNull(v.getJguuid1())) v.setJguuid1(IdUtil.fastSimpleUUID());
            v.setYwqdDm(MhzcConstants.YWQDDM);
            v.setSjcsdq(MhzcConstants.SJCSDQ);
            v.setSjgsdq(MhzcConstants.SJGSDQ);
            v.setLrrq(date);
            v.setXgrq(date);
            v.setLrrsfid(ZnsbSessionUtils.getYhUuid());
            v.setSjtbSj(date);
            v.setTbwczt("N");
            v.setSsjswjgDm("1"+v.getXzqhszDm()+"0000");
            return v;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }

    @Override
    public Map<String, List<ZnsbMhqxJgxxbDO>> getJgidByMc(List<String> list) {
        QueryWrapper<ZnsbMhqxJgxxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxJgxxbDO::getJguuid1, ZnsbMhqxJgxxbDO::getJgmc1, ZnsbMhqxJgxxbDO::getNsrsbh)
                .in(ZnsbMhqxJgxxbDO::getNsrsbh, list.stream().distinct().collect(Collectors.toList()));
        List<ZnsbMhqxJgxxbDO> znsbMhqxJgxxbDOS = jgxxbMapper.selectList(wrapper);
        return znsbMhqxJgxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxJgxxbDO::getNsrsbh));
    }


    public Map<String, List<ZnsbMhqxJgxxbDO>> getJgidByjgmc() {
        QueryWrapper<ZnsbMhqxJgxxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxJgxxbDO::getJguuid1, ZnsbMhqxJgxxbDO::getJgmc1, ZnsbMhqxJgxxbDO::getNsrsbh)
                .eq(ZnsbMhqxJgxxbDO::getYxbz, "Y");
        List<ZnsbMhqxJgxxbDO> znsbMhqxJgxxbDOS = jgxxbMapper.selectList(wrapper);
        return znsbMhqxJgxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxJgxxbDO::getJgmc1));
    }

    @Override
    public void zzjgBatch(List<ZnsbMhqxZzjgGxbDO> zzjgs) {
        final Date date = new Date();
        List<ZnsbMhqxZzjgGxbDO> collect = zzjgs.stream().map(v -> {
            v.setUuid(IdUtil.fastSimpleUUID());
            v.setYwqdDm(MhzcConstants.YWQDDM);
            v.setLrrq(date);
            v.setXgrq(date);
            v.setLrrsfid(ZnsbSessionUtils.getYhUuid());
            v.setXgrsfid(ZnsbSessionUtils.getYhUuid());
            v.setSjcsdq(MhzcConstants.SJCSDQ);
            v.setSjgsdq(MhzcConstants.SJGSDQ);
            v.setSjtbSj(date);
            return v;
        }).collect(Collectors.toList());
        zzjgGxbMapper.insertBatch(collect);
    }

    @Override
    public void yhjgBatch(List<ZnsbMhqxYhjgGxbDO> yhjgs) {
        final Date date = new Date();
        List<ZnsbMhqxYhjgGxbDO> collect = yhjgs.stream().map(v -> {
            v.setUuid(IdUtil.fastSimpleUUID());
            v.setYwqdDm(MhzcConstants.YWQDDM);
            v.setLrrq(date);
            v.setXgrq(date);
            v.setLrrsfid(ZnsbSessionUtils.getYhUuid());
            v.setXgrsfid(ZnsbSessionUtils.getYhUuid());
            v.setSjcsdq(MhzcConstants.SJCSDQ);
            v.setSjgsdq(MhzcConstants.SJGSDQ);
            v.setSjtbSj(date);
            return v;
        }).collect(Collectors.toList());
        yhjgGxbMapper.insertBatch(collect);
    }

    @Override
    public void updQyxx(QyxxglReqVO reqVO) {
        Date date = new Date();
        ZnsbMhqxJgxxbDO znsbMhqxJgxxbDO = BeanUtils.toBean(reqVO, ZnsbMhqxJgxxbDO.class);
        znsbMhqxJgxxbDO.setXgrq(date);
        znsbMhqxJgxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
        jgxxbMapper.updateById(znsbMhqxJgxxbDO);
    }

    @Override
    public void addQyxxImportExcel(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 读取所有Sheet
            List<ReadSheet> readSheets = EasyExcel.read(inputStream).build().excelExecutor().sheetList();
            for (ReadSheet sheet : readSheets) {//遍历sheet页 根据sheet页名用对应的对象处理sheet页数据
                String sheetName = sheet.getSheetName();

                switch (sheetName) {
                    case "企业信息":
                        List<GlqyExcelMbVO> qylist = buildQyxx(file.getInputStream(), sheet, GlqyExcelMbVO.class);
                        if (!GyUtils.isNull(qylist)) {
                            List<QyxxglReqVO> qyxxAddList = qylist.stream().map(a -> {
                                QueryWrapper<ZnsbMhqxJgxxbDO> wrapper = new QueryWrapper<>();
                                wrapper.lambda().select(ZnsbMhqxJgxxbDO::getJguuid1).eq(ZnsbMhqxJgxxbDO::getNsrsbh, a.getNsrsbh());
                                if (!GyUtils.isNull(jgxxbMapper.selectList(wrapper))) {//验证是否已经存在该企业
                                    return null;
                                }
                                QyxxglReqVO reqVO = BeanUtils.toBean(a, QyxxglReqVO.class);
                                String sjNsrsbh = a.getSjNsrsbh();
                                String sjJgmc1 = a.getSjJgmc1();
                                if (!GyUtils.isNull(sjJgmc1) || !GyUtils.isNull(sjNsrsbh)) {
                                    reqVO.setSjJguuid(sjNsrsbh);
                                }
                                if (!GyUtils.isNull(a.getXzqhszDm())) {
                                    String dm = CacheUtils.dm2mc("dm_gy_xzqh_getdm", a.getXzqhszDm());
                                    reqVO.setXzqhszDm(dm);
                                }
                                reqVO.setJguuid1(IdUtil.fastSimpleUUID());
                                return reqVO;
                            }).collect(Collectors.toList());
                            List<String> nsrList = qylist.stream().map(GlqyExcelMbVO::getSjNsrsbh).collect(Collectors.toList());
                            Map<String, List<ZnsbMhqxJgxxbDO>> jgidByNsr = getJgidByMc(nsrList);//根据nsrsbh分组
                            Map<String, List<QyxxglReqVO>> collect = qyxxAddList.stream().collect(Collectors.groupingBy(QyxxglReqVO::getNsrsbh));
                            List<QyxxglReqVO> collect1 = qyxxAddList.stream().map(m -> {
                                String sjJguuid = m.getSjJguuid();
                                if (!GyUtils.isNull(sjJguuid)) {
                                    List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgidByNsr.get(sjJguuid);
                                    if (GyUtils.isNull(jgxxbDOS)) {
                                        List<QyxxglReqVO> qyxxglReqVOS = collect.get(sjJguuid);
                                        if (GyUtils.isNull(qyxxglReqVOS)) {
                                            return m;
                                        } else {
                                            m.setSjJguuid(qyxxglReqVOS.get(0).getJguuid1());
                                        }
                                    } else {
                                        m.setSjJguuid(jgxxbDOS.get(0).getJguuid1());
                                    }
                                    return m;
                                } else {
                                    return m;
                                }
                            }).collect(Collectors.toList());
                            addQyxx(collect1);
                        }
                        break;
                    case "组织信息":
                        List<ZzxxExcelMbVO> zzlist = buildQyxx(file.getInputStream(), sheet, ZzxxExcelMbVO.class);
                        if (!GyUtils.isNull(zzlist)) {
                            List<ZnsbMhqxZzxxbDO> y = zzlist.stream().map(v -> {
                                final Date date = new Date();
                                final ZnsbMhqxZzxxbDO zzxxbDO = new ZnsbMhqxZzxxbDO();
                                zzxxbDO.setZzuuid(IdUtil.fastSimpleUUID());
                                zzxxbDO.setZzmc2(v.getZzmc2());
                                if (!GyUtils.isNull(v.getXh())) {
                                    zzxxbDO.setXh(Integer.parseInt(v.getXh()));
                                }
                                zzxxbDO.setBz(v.getBz());
                                zzxxbDO.setYxbz("Y");
                                zzxxbDO.setYwqdDm("ZNSB.MHZC");
                                zzxxbDO.setLrrq(date);
                                zzxxbDO.setXgrq(date);
                                zzxxbDO.setLrrsfid("SYS");
                                zzxxbDO.setXgrsfid("SYS");
                                zzxxbDO.setSjgsdq("00000000000");
                                zzxxbDO.setSjcsdq("00000000000");
                                zzxxbDO.setSjtbSj(new Date());
                                return zzxxbDO;
                            }).filter(a -> !GyUtils.isNull(a)).collect(Collectors.toList());

                            Map<String, List<ZnsbMhqxZzxxbDO>> collect = y.stream().collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getZzmc2));

                            List<ZnsbMhqxZzxxbDO> collect1 = y.stream().map(v -> {

                                String sjzzmc = v.getSjZzuuid();
                                if (!GyUtils.isNull(sjzzmc)) {
                                    String sjzzuuid = zzxxService.getZzuuidByMc(sjzzmc);//根据上级组织名称获取sjzzuuid
                                    if (GyUtils.isNull(sjzzuuid)) {
                                        List<ZnsbMhqxZzxxbDO> zzxxbDOS = collect.get(sjzzmc);
                                        if (!GyUtils.isNull(zzxxbDOS)) {
                                            sjzzuuid = zzxxbDOS.get(0).getZzuuid();
                                        }
                                    }
                                    v.setSjZzuuid(sjzzuuid);
                                }
                                String s = zzxxService.generateCcm(v.getSjZzuuid());
                                v.setCcm(s);
                                return v;
                            }).collect(Collectors.toList());

                            zzxxService.saveBatch(collect1);
                        }
                        break;
                    case "用户信息":
                        List<UserExcelMbVO> userlist = buildQyxx(file.getInputStream(), sheet, UserExcelMbVO.class);
                        if (!GyUtils.isNull(userlist)) {
                            List<ZnsbMhqxYhxxbDO> list = BeanUtils.toBean(userlist, ZnsbMhqxYhxxbDO.class);
                            List<ZnsbMhqxYhxxbDO> collect = list.stream().filter(a -> !GyUtils.isNull(a.getZsxm1())).collect(Collectors.toList());
                            userService.batchYhxx(collect);
                        }
                        break;
                    case "角色信息":
                        List<JsxxExcelMbVO> jslist = buildQyxx(file.getInputStream(), sheet, JsxxExcelMbVO.class);
                        if (!GyUtils.isNull(jslist)) {
                            List<ZnsbMhqxJsxxbDO> list = BeanUtils.toBean(jslist, ZnsbMhqxJsxxbDO.class);
                            roleService.batch(list);
                        }
                        break;
                    case "组织与企业关系":
                        List<ZzjgExcelMbVO> zzjglist = buildQyxx(file.getInputStream(), sheet, ZzjgExcelMbVO.class);
                        if (!GyUtils.isNull(zzjglist)) {
                            List<String> nsrList = zzjglist.stream().map(ZzjgExcelMbVO::getNsrsbh).collect(Collectors.toList());
                            List<String> zzmcList = zzjglist.stream().map(ZzjgExcelMbVO::getZzmc2).collect(Collectors.toList());
                            Map<String, List<ZnsbMhqxJgxxbDO>> jgidByNsr = getJgidByMc(nsrList);//根据nsrsbh分组
                            Map<String, List<ZnsbMhqxZzxxbDO>> zzidByMc = zzxxService.getZzidByMc(zzmcList);//根据zzmc分组
                            List<ZnsbMhqxZzjgGxbDO> zzjgs = zzjglist.stream().map(v -> {
                                List<ZnsbMhqxJgxxbDO> znsbMhqxJgxxbDOS = jgidByNsr.get(v.getNsrsbh());//根据nsrsbh获取对应的机构信息List
                                String jguuid;
                                if (GyUtils.isNull(znsbMhqxJgxxbDOS)) {
                                    return null;
                                } else {
                                    List<String> jguuids = znsbMhqxJgxxbDOS.stream().map(j -> {
                                        if (v.getJgmc1().equals(j.getJgmc1())) {//判断纳税人相等的情况下 机构名称是否也相等 不相等就返回null跳过
                                            return j.getJguuid1();
                                        } else {
                                            return null;
                                        }
                                    }).filter(a -> !GyUtils.isNull(a)).collect(Collectors.toList());
                                    if (!GyUtils.isNull(jguuids)) {
                                        jguuid = jguuids.get(0);//如果处理结果不是空就返回需要保存的jguuid
                                    } else {
                                        return null;
                                    }
                                }
                                if (!GyUtils.isNull(zzidByMc.get(v.getZzmc2())) && !GyUtils.isNull(jguuid)) {//sheet页中的组织名称有效并且jguuid不是空则组装DO并返回
                                    ZnsbMhqxZzjgGxbDO znsbMhqxZzjgGxbDO = new ZnsbMhqxZzjgGxbDO();
                                    znsbMhqxZzjgGxbDO.setZzuuid(zzidByMc.get(v.getZzmc2()).get(0).getZzuuid());
                                    znsbMhqxZzjgGxbDO.setJguuid1(jguuid);
                                    return znsbMhqxZzjgGxbDO;
                                } else {
                                    return null;
                                }
                            }).filter(a -> !GyUtils.isNull(a)).collect(Collectors.toList());
                            if (GyUtils.isNull(zzjgs)) {
                                break;
                            }
                            zzjgBatch(zzjgs);
                        }
                        break;
                    case "组织与用户与管理企业关系":
                        List<YhjgExcelMbVO> yhjgList = buildQyxx(file.getInputStream(), sheet, YhjgExcelMbVO.class);
                        if (GyUtils.isNull(yhjgList)) {
                            break;
                        }

                        List<String> dlzhs2 = yhjgList.stream().map(YhjgExcelMbVO::getDlzh).collect(Collectors.toList());
                        Map<String, List<ZnsbMhqxYhxxbDO>> yhids2 = userService.getYhidByDlzh(dlzhs2);//根据dlzh分组sheet页中用户名称对应的用户信息
                        if (GyUtils.isNull(yhids2)) {
                            break;
                        }
                        List<ZnsbMhqxZzxxbDO> zzxxbDOS = zzxxbMapper.selectAllZz(null);
                        Map<String, List<ZnsbMhqxZzxxbDO>> zzidByMc2 = zzxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getZzmc2));//根据zzmc分组所有组织信息
                        Map<String, List<ZnsbMhqxJgxxbDO>> jgidByNsr = getJgidByjgmc();//根据jgmc分组所有企业信息

                        List<String> collect = yhjgList.stream().map(YhjgExcelMbVO::getJsmc).collect(Collectors.toList());
                        List<String> jsmcList = new ArrayList<>();
                        collect.forEach(v -> {
                            if (!GyUtils.isNull(v)) {
                                String[] split = v.split("/");
                                jsmcList.addAll(Arrays.asList(split));
                            }
                        });
                        Map<String, List<ZnsbMhqxJsxxbDO>> jsidByMc = roleService.getJsidByMc(jsmcList);//获取所有角色信息（因为）   并去掉（系统管理员） 并按zzmc分组

                        List<ZnsbMhqxYhjsGxbDO> yhjss = new ArrayList<>();
                        List<ZnsbMhqxYhjgGxbDO> yhjgs = new ArrayList<>();
                        List<ZnsbMhqxZzyhGxbDO> zzyhs = new ArrayList<>();
                        yhjgList.forEach(v -> {
                            String dlzh = v.getDlzh();
                            String zzmc2 = v.getZzmc2();
                            String jgmc1 = v.getJgmc1();
                            String jsmcs = v.getJsmc();
                            String yhuuid = yhids2.get(dlzh).get(0).getYhUuid();

                            if (!GyUtils.isNull(zzmc2) && !GyUtils.isNull(yhuuid)) {

                                if ("全部组织机构".equals(zzmc2)) {
                                    Set<String> strings = jgidByNsr.keySet();
                                    strings.forEach(s -> {
                                        String zzuuid1 = zzidByMc2.get(s).get(0).getZzuuid();
                                        String jguuid1 = jgidByNsr.get(s).get(0).getJguuid1();
                                        ZnsbMhqxZzyhGxbDO zzyh = new ZnsbMhqxZzyhGxbDO();
                                        zzyh.setYhUuid(yhuuid);
                                        zzyh.setZzuuid(zzuuid1);
                                        zzyhs.add(zzyh);
                                        ZnsbMhqxYhjgGxbDO yhjgGxbDO = new ZnsbMhqxYhjgGxbDO();
                                        yhjgGxbDO.setZzuuid(zzuuid1);
                                        yhjgGxbDO.setJguuid1(jguuid1);
                                        yhjgGxbDO.setYhUuid(yhuuid);
                                        yhjgs.add(yhjgGxbDO);
                                        if (!GyUtils.isNull(jsmcs)) {
                                            String[] split = jsmcs.split("/");
                                            List<String> jsList = Arrays.asList(split);
                                            jsList.forEach(j -> {
                                                String jsuuid = jsidByMc.get(j).get(0).getJsuuid();
                                                ZnsbMhqxYhjsGxbDO yhjsGxbDO = new ZnsbMhqxYhjsGxbDO();
                                                yhjsGxbDO.setZzuuid(zzuuid1);
                                                yhjsGxbDO.setJsuuid(jsuuid);
                                                yhjsGxbDO.setYhUuid(yhuuid);
                                                yhjss.add(yhjsGxbDO);
                                            });
                                        }
                                    });
                                } else {
                                    String[] split = zzmc2.split("/");
                                    List<String> zzList = Arrays.asList(split);
                                    List<String> jgList1 = new ArrayList<>();
                                    if ("全部企业".equals(jgmc1)) {
                                        List<ZnsbMhqxJgxxbDO> alljg = jgxxbMapper.getAlljg();
                                        jgList1 = alljg.stream().map(ZnsbMhqxJgxxbDO::getJgmc1).collect(Collectors.toList());
                                    } else {
                                        String[] split2 = jgmc1.split("/");
                                        jgList1 = Arrays.asList(split2);
                                    }
                                    List<String> jgList = jgList1;
                                    zzList.forEach(s -> {
                                                String zzuuid1 = zzidByMc2.get(s).get(0).getZzuuid();

                                                ZnsbMhqxZzyhGxbDO zzyh = new ZnsbMhqxZzyhGxbDO();
                                                zzyh.setYhUuid(yhuuid);
                                                zzyh.setZzuuid(zzuuid1);
                                                zzyhs.add(zzyh);

                                                jgList.forEach(jg -> {
                                                    String jguuid1 = jgidByNsr.get(jg).get(0).getJguuid1();
                                                    ZnsbMhqxYhjgGxbDO yhjgGxbDO = new ZnsbMhqxYhjgGxbDO();
                                                    yhjgGxbDO.setZzuuid(zzuuid1);
                                                    yhjgGxbDO.setJguuid1(jguuid1);
                                                    yhjgGxbDO.setYhUuid(yhuuid);
                                                    yhjgs.add(yhjgGxbDO);
                                                });
                                                if (!GyUtils.isNull(jsmcs)) {
                                                    String[] split1 = jsmcs.split("/");
                                                    List<String> jsList = Arrays.asList(split1);
                                                    jsList.forEach(j -> {
                                                        String jsuuid = jsidByMc.get(j).get(0).getJsuuid();
                                                        ZnsbMhqxYhjsGxbDO yhjsGxbDO = new ZnsbMhqxYhjsGxbDO();
                                                        yhjsGxbDO.setZzuuid(zzuuid1);
                                                        yhjsGxbDO.setJsuuid(jsuuid);
                                                        yhjsGxbDO.setYhUuid(yhuuid);
                                                        yhjss.add(yhjsGxbDO);
                                                    });
                                                }
                                            }
                                    );
                                }
                            }
                        });
                        if (!GyUtils.isNull(zzyhs)) {
                            userService.zzyhBatch(zzyhs);
                        }
                        if (!GyUtils.isNull(yhjgs)) {
                            this.yhjgBatch(yhjgs);
                        }
                        if (!GyUtils.isNull(yhjss)) {
                            userService.yhjsBatch(yhjss);
                        }
//                        List<String> dlzhs2 = yhjgList.stream().map(YhjgExcelMbVO::getDlzh).collect(Collectors.toList());
//                        Map<String, List<ZnsbMhqxYhxxbDO>> yhids2 = userService.getYhidByDlzh(dlzhs2);//根据dlzh分组sheet页中用户名称对应的用户信息
//                        if (GyUtils.isNull(yhids2)){
//                            break;
//                        }
//                        List<String> zzmcList2 = yhjgList.stream().map(YhjgExcelMbVO::getZzmc2).collect(Collectors.toList());
//                        Map<String, List<ZnsbMhqxZzxxbDO>> zzidByMc2 = zzxxService.getZzidByMc(zzmcList2);//根据组织名称分组sheet页对应的组织信息
//                        if (GyUtils.isNull(zzidByMc2)){
//                            break;
//                        }
//                        List<String> nsrList = yhjgList.stream().map(YhjgExcelMbVO::getNsrsbh).collect(Collectors.toList());
//                        Map<String, List<ZnsbMhqxJgxxbDO>> jgidByNsr = getJgidByMc(nsrList);//根据纳税人名称分组sheet页对应的企业信息
//
//                        List<String> jsmcList = yhjgList.stream().map(YhjgExcelMbVO::getJsmc).collect(Collectors.toList());
//                        Map<String, List<ZnsbMhqxJsxxbDO>> jsidByMc = roleService.getJsidByMc(jsmcList);//获取所有角色信息（因为）   并去掉（系统管理员） 并按zzmc分组
//
//                        List<YhxxInitDTO> collect = yhjgList.stream().map(v -> {
//                            YhxxInitDTO initDTO = new YhxxInitDTO();
//                            List<ZnsbMhqxYhxxbDO> znsbMhqxYhxxbDOS = yhids2.get(v.getDlzh());//获取当前循环下登陆账号对应的用户信息List
//                            List<ZnsbMhqxJgxxbDO> znsbMhqxJgxxbDOS = jgidByNsr.get(v.getNsrsbh());//获取当前循环下纳税人识别号对应的企业信息List
//                            List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzidByMc2.get(v.getZzmc2());//获取当前循环下组织信息
//                            String jsmc = v.getJsmc();
//                            if (GyUtils.isNull(znsbMhqxZzxxbDOS) || GyUtils.isNull(znsbMhqxYhxxbDOS)) {
//                                return null;
//                            }
//                            List<String> yhuuids = znsbMhqxYhxxbDOS.stream().map(j -> {
//                                if (v.getZsxm().equals(j.getZsxm1())) {//在dlzh相等的情况下 判断sheet页中输入的真实姓名是否也相等 相等就返回到yhuuids
//                                    return j.getYhUuid();
//                                } else {
//                                    return null;
//                                }
//                            }).filter(a->!GyUtils.isNull(a)).collect(Collectors.toList());
//                            if (GyUtils.isNull(yhuuids)) {
//                                return null;
//                            }
//                            String yhuuid = yhuuids.get(0);
//                            String zzuuid = znsbMhqxZzxxbDOS.get(0).getZzuuid();
//
//                            ZnsbMhqxZzyhGxbDO zzyhGxbDO = new ZnsbMhqxZzyhGxbDO();
//                            zzyhGxbDO.setYhUuid(yhuuid);
//                            zzyhGxbDO.setZzuuid(zzuuid);
//                            initDTO.setZzyh(zzyhGxbDO);
//
//                            List<String> jguuids = znsbMhqxJgxxbDOS.stream().map(j -> {
//                                if (v.getJgmc1().equals(j.getJgmc1())) {//在nsrsbh相等的情况下 判断sheet页中输入的企业名称是否也相等 相等就返回到jguuids
//                                    return j.getJguuid1();
//                                } else {
//                                    return null;
//                                }
//                            }).filter(a->!GyUtils.isNull(a)).collect(Collectors.toList());
//
//                            if (GyUtils.isNull(jguuids)) {
//                                return null;
//                            }
//                            String jguuid = jguuids.get(0);
//                            ZnsbMhqxYhjgGxbDO yhjgGxbDO = new ZnsbMhqxYhjgGxbDO();
//                            yhjgGxbDO.setYhUuid(yhuuid);
//                            yhjgGxbDO.setZzuuid(zzuuid);
//                            yhjgGxbDO.setJguuid1(jguuid);
//                            initDTO.setYhjg(yhjgGxbDO);
//
//                            if (GyUtils.isNull(jsidByMc.get(jsmc))) {//检查sheet页中的角色名称是否存在
//                                return null;
//                            }
//                            String jsuuid = jsidByMc.get(jsmc).get(0).getJsuuid();
//                            ZnsbMhqxYhjsGxbDO yhjsGxbDO = new ZnsbMhqxYhjsGxbDO();
//                            yhjsGxbDO.setYhUuid(yhuuid);
//                            yhjsGxbDO.setZzuuid(zzuuid);
//                            yhjsGxbDO.setJsuuid(jsuuid);
//                            initDTO.setYhjs(yhjsGxbDO);
//
//                            return initDTO;
//                        }).filter(a->!GyUtils.isNull(a)).collect(Collectors.toList());
//                        if (!GyUtils.isNull(collect)){
//                            List<ZnsbMhqxZzyhGxbDO> zzyhs = collect.stream().map(YhxxInitDTO::getZzyh).distinct().collect(Collectors.toList());
//                            List<ZnsbMhqxZzyhGxbDO> zzyhGxbDOList = BeanUtils.toBean(zzyhs,ZnsbMhqxZzyhGxbDO.class);
//                            userService.zzyhBatch(zzyhGxbDOList);
//                            List<ZnsbMhqxYhjgGxbDO> yhjgs = collect.stream().map(YhxxInitDTO::getYhjg).distinct().collect(Collectors.toList());
//                            List<ZnsbMhqxYhjgGxbDO> yhjgGxbDOList = BeanUtils.toBean(yhjgs,ZnsbMhqxYhjgGxbDO.class);
//                            this.yhjgBatch(yhjgGxbDOList);
//                            List<ZnsbMhqxYhjsGxbDO> yhjss = collect.stream().map(YhxxInitDTO::getYhjs).distinct().collect(Collectors.toList());
//                            List<ZnsbMhqxYhjsGxbDO> yhjsGxbDOList = BeanUtils.toBean(yhjss,ZnsbMhqxYhjsGxbDO.class);
//                            userService.yhjsBatch(yhjsGxbDOList);
//                        }
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.debug(e.getMessage());
        }
    }

    @Override
    public ZnsbMhqxJgxxbDO getJgxxByJguuid(String jguuid) {
        return this.getById(jguuid);
    }

    @Override
    public String getJgxxByQydmz(String qydmz) {
        QueryWrapper<ZnsbMhqxJgxxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ZnsbMhqxJgxxbDO::getQydmz, qydmz);
        List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectList(wrapper);
        List<String> nsrsbhs = jgxxbDOS.stream().map(ZnsbMhqxJgxxbDO::getNsrsbh).collect(Collectors.toList());
        if (GyUtils.isNull(nsrsbhs)) {
            return "";
        }
        return nsrsbhs.get(0);
    }

    @Transactional
    @Override
    public void copyQyxx(CopyQyxxVO qyxxVO) {
        if (!checkParam(qyxxVO)) throw new IllegalStateException("参数错误");
        String olddjxh = qyxxVO.getOlddjxh();
        List<ZnsbMhqxJgxxbDO> jgxxList = jgxxbMapper.getByDjxh(olddjxh);
        if (GyUtils.isNull(jgxxList)) {
            throw new IllegalStateException("未查询到旧djxh对应的机构信息");
        }
        List<ZnsbMhzcQyjbxxmxDTO> znsbMhzcQyjbxxmxDTOS = qyjbxxmxMapper.getjbxxByDjxh(olddjxh);
        if (GyUtils.isNull(znsbMhzcQyjbxxmxDTOS)) {
            throw new IllegalStateException("未查询到旧djxh对应的企业基本明细信息");
        }
        List<ZnsbMhzcSfzrdxxDTO> sfzrdxxDTOList = sfzrdxxMapper.getByDjxh(olddjxh);
        if (GyUtils.isNull(sfzrdxxDTOList)) {
            throw new IllegalStateException("未查询到旧djxh对应的税费种信息");
        }
        String jgmc1 = qyxxVO.getJgmc1();
        String nsrsbh = qyxxVO.getNsrsbh();
        String djxh = qyxxVO.getDjxh();
        String xzqhszDm = qyxxVO.getXzqhszDm();
        String qydmz = qyxxVO.getQydmz();
        ZnsbMhqxJgxxbDO jgxxbDO1 = jgxxList.get(0);
        jgxxbDO1.setJguuid1(IdUtil.fastSimpleUUID());
        jgxxbDO1.setJgmc1(jgmc1);
        jgxxbDO1.setNsrsbh(nsrsbh);
        jgxxbDO1.setShxydm(nsrsbh);
        jgxxbDO1.setDjxh(djxh);
        if (!GyUtils.isNull(xzqhszDm)) jgxxbDO1.setXzqhszDm(xzqhszDm);
        if (!GyUtils.isNull(qydmz)) jgxxbDO1.setQydmz(qydmz);
        jgxxbDO1.setLrrq(new Date());
        jgxxbMapper.insert(jgxxbDO1);
        ZnsbMhzcQyjbxxmxDTO qyjbxxmxDTO = znsbMhzcQyjbxxmxDTOS.get(0);
        qyjbxxmxDTO.setDjxh(djxh);
        qyjbxxmxDTO.setNsrmc(jgmc1);
        qyjbxxmxDTO.setNsrsbh(nsrsbh);
        qyjbxxmxDTO.setLrrq(new Date());
        qyjbxxmxMapper.insert(qyjbxxmxDTO);
        List<ZnsbMhzcSfzrdxxDTO> list = new ArrayList<>();
        sfzrdxxDTOList.forEach(v -> {
            ZnsbMhzcSfzrdxxDTO sfzrdxx = BeanUtils.toBean(v, ZnsbMhzcSfzrdxxDTO.class);
            sfzrdxx.setRdpzuuid(IdUtil.fastSimpleUUID());
            sfzrdxx.setDjxh(djxh);
            sfzrdxx.setNsrsbh(nsrsbh);
            sfzrdxx.setNsrmc(jgmc1);
            sfzrdxx.setLrrq(new Date());
            list.add(sfzrdxx);
        });
        sfzrdxxMapper.insertBatch(list);

        List<ZnsbMhzcNsrzgxxDTO> newzgxxs = new ArrayList<>();
        List<ZnsbMhzcNsrzgxxDTO> zgxxs = nsrzgxxMapper.getByDjxh(olddjxh);
        if (!GyUtils.isNull(zgxxs)) {
            zgxxs.forEach(v -> {
                ZnsbMhzcNsrzgxxDTO znsbMhzcNsrzgxxDTO = BeanUtils.toBean(v, ZnsbMhzcNsrzgxxDTO.class);
                znsbMhzcNsrzgxxDTO.setUuid(IdUtil.fastSimpleUUID());
                znsbMhzcNsrzgxxDTO.setDjxh(djxh);
                znsbMhzcNsrzgxxDTO.setLrrq(new Date());
                znsbMhzcNsrzgxxDTO.setNsrmc(jgmc1);
                znsbMhzcNsrzgxxDTO.setNsrsbh(nsrsbh);
                newzgxxs.add(znsbMhzcNsrzgxxDTO);
            });
            nsrzgxxMapper.insertBatch(newzgxxs);
        }

        List<ZnsbMhzcQybqxxDTO> newbqxxs = new ArrayList<>();
        List<ZnsbMhzcQybqxxDTO> bqxxs = qybqxxMapper.getByDjxh(olddjxh);
        if (!GyUtils.isNull(bqxxs)) {
            bqxxs.forEach(v -> {
                ZnsbMhzcQybqxxDTO qybqxxDTO = BeanUtils.toBean(v, ZnsbMhzcQybqxxDTO.class);
                qybqxxDTO.setUuid(IdUtil.fastSimpleUUID());
                qybqxxDTO.setDjxh(djxh);
                qybqxxDTO.setLrrq(new Date());
                qybqxxDTO.setNsrmc(jgmc1);
                qybqxxDTO.setNsrsbh(nsrsbh);
                newbqxxs.add(qybqxxDTO);
            });
            qybqxxMapper.insertBatch(newbqxxs);
        }
    }

    private boolean checkParam(CopyQyxxVO qyxxVO) {
        if (GyUtils.isNull(qyxxVO)) {
            return false;
        }
        if (GyUtils.isNull(qyxxVO.getOlddjxh())) {
            return false;
        }
        if (GyUtils.isNull(qyxxVO.getNsrsbh())) {
            return false;
        }
        if (GyUtils.isNull(qyxxVO.getJgmc1())) {
            return false;
        }
        if (GyUtils.isNull(qyxxVO.getDjxh())) {
            return false;
        }
        return true;
    }

    //根据class类型处理sheet页数据
    public <T> List<T> buildQyxx(InputStream inputStream, ReadSheet sheet, Class<T> clazz) {
        List<T> list = new ArrayList<>();
        EasyExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {

            @Override
            public void invoke(T data, AnalysisContext context) {
                list.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).excelType(ExcelTypeEnum.XLSX).sheet(sheet.getSheetNo()).doRead();
        return list;
    }


    @Override
    public List<RyxxVO> getBsyByQy(DjxhReqVO reqVO) {
        String djxh = reqVO.getDjxh();
        if (GyUtils.isNull(djxh)) {
            log.info("====获取办税员API入参djxh为null====");
            return null;
        }
        ZnsbMhqxJgxxbDO jgxxbDO = jgxxbMapper.selectByDjxhAndNsrsbh(djxh, null);
        if (GyUtils.isNull(jgxxbDO)) {
            return null;
        }
        if (!"1".equals(jgxxbDO.getQylxz())) {
            List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectZhByNsrsbh(jgxxbDO.getNsrsbh());
            if (GyUtils.isNull(jgxxbDOS)) return null;
            jgxxbDO = jgxxbDOS.get(0);
        }
        List<ZnsbMhqxYhxxbDO> yhxxs = yhjgGxbMapper.getBsyByQy(jgxxbDO.getJguuid1());
        if (!GyUtils.isNull(yhxxs)) {
            return BeanUtils.toBean(yhxxs, RyxxVO.class);
        } else {
            List<ZnsbMhqxYhjgGxbDO> yhjgs = yhjgGxbMapper.getczrByQy(jgxxbDO.getJguuid1());
            List<String> yhuuids = yhjgs.stream().distinct().map(ZnsbMhqxYhjgGxbDO::getYhUuid).collect(Collectors.toList());
            if (GyUtils.isNull(yhuuids)) return null;
            List<ZnsbMhqxYhxxbDO> znsbMhqxYhxxbDOS = yhxxbMapper.selectByUuids(yhuuids);
            if (GyUtils.isNull(znsbMhqxYhxxbDOS)) {
                return null;
            }
            return BeanUtils.toBean(znsbMhqxYhxxbDOS, RyxxVO.class);
        }
    }

    @Override
    public List<String> getAllDjxh() {
        List<ZnsbMhzcQyjbxxmxDTO> list = qyjbxxmxMapper.getAllDjxh();
        if (GyUtils.isNull(list)) return new ArrayList<>();
        return list.stream().map(ZnsbMhzcQyjbxxmxDTO::getDjxh).collect(Collectors.toList());
    }

    public void refreshZszzbz(){
        List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS = zzjgGxbMapper.selectJgByZz(null, null);
        List<ZnsbMhqxZzxxbDO> zzxxbDOS = zzxxbMapper.selectAllZz("");
        List<String> uuids = new ArrayList<>();
        List<String> a = a(znsbMhqxZzjgGxbDOS,zzxxbDOS,uuids);
        System.out.println(a);
    }

    public List<String> a(List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS,List<ZnsbMhqxZzxxbDO> zzxxbDOS,List<String> uuids){

        if (GyUtils.isNull(znsbMhqxZzjgGxbDOS)) return uuids;
        List<String> jguuids = new ArrayList<>();
        Map<String, List<ZnsbMhqxZzjgGxbDO>> zzjgByZz = znsbMhqxZzjgGxbDOS.stream().distinct().collect(Collectors.groupingBy(ZnsbMhqxZzjgGxbDO::getZzuuid));

        List<String> zzuuids = zzxxbDOS.stream().map(ZnsbMhqxZzxxbDO::getZzuuid).collect(Collectors.toList());
        List<String> sjzzuuids = zzxxbDOS.stream().map(ZnsbMhqxZzxxbDO::getSjZzuuid).filter(z->!GyUtils.isNull(z)).collect(Collectors.toList());
        List<String> mjzzuuids = zzuuids.stream().filter(z -> !sjzzuuids.contains(z)).collect(Collectors.toList());
        mjzzuuids.forEach(m->{
            List<ZnsbMhqxZzjgGxbDO> jgs = zzjgByZz.get(m);
            if (!GyUtils.isNull(jgs)){
                List<String> collect = jgs.stream().map(ZnsbMhqxZzjgGxbDO::getUuid).collect(Collectors.toList());
                uuids.addAll(collect);
                List<String> collect2 = jgs.stream().map(ZnsbMhqxZzjgGxbDO::getJguuid1).collect(Collectors.toList());
                jguuids.addAll(collect2);
            }
        });
        List<ZnsbMhqxZzjgGxbDO> newZzjgGxb = znsbMhqxZzjgGxbDOS.stream().filter(z -> !jguuids.contains(z.getJguuid1())).collect(Collectors.toList());
        return a(newZzjgGxb,zzxxbDOS.stream().filter(z->!mjzzuuids.contains(z.getZzuuid())).collect(Collectors.toList()), uuids);
    }

    public void refreshZszzbz2(){
        List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS = zzjgGxbMapper.selectJgByZz(null, null);
        List<ZnsbMhqxZzxxbDO> zzxxbDOS = zzxxbMapper.selectAllZz("");
        List<String> uuids = new ArrayList<>();
        Map<String,List<String>> filterJguuid = new HashMap<>();
        List<String> b = b(znsbMhqxZzjgGxbDOS,zzxxbDOS,uuids,filterJguuid);
        zzjgGxbMapper.updZszzbz(b);
    }

    public List<String> b(List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS,List<ZnsbMhqxZzxxbDO> zzxxbDOS,List<String> uuids,Map<String,List<String>> filterJguuid){

        if (GyUtils.isNull(zzxxbDOS)) return uuids;

        Map<String, List<ZnsbMhqxZzjgGxbDO>> zzjgByZz = znsbMhqxZzjgGxbDOS.stream().distinct().collect(Collectors.groupingBy(ZnsbMhqxZzjgGxbDO::getZzuuid));

        List<String> zzuuids = zzxxbDOS.stream().map(ZnsbMhqxZzxxbDO::getZzuuid).collect(Collectors.toList());
        Map<String, List<ZnsbMhqxZzxxbDO>> zzxxByZz = zzxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getZzuuid));
        List<String> sjzzuuids = zzxxbDOS.stream().map(ZnsbMhqxZzxxbDO::getSjZzuuid).filter(z->!GyUtils.isNull(z)).collect(Collectors.toList());
        List<String> mjzzuuids = zzuuids.stream().filter(z -> !sjzzuuids.contains(z)).collect(Collectors.toList());
        mjzzuuids.forEach(m->{
            List<ZnsbMhqxZzjgGxbDO> jgs = zzjgByZz.get(m);
            if (!GyUtils.isNull(jgs)){
                List<String> filterJguuids = filterJguuid.get(m);
                if (!GyUtils.isNull(filterJguuids)){
                    List<String> collect = jgs.stream().filter(j->!filterJguuids.contains(j.getJguuid1())).map(ZnsbMhqxZzjgGxbDO::getUuid).collect(Collectors.toList());
                    uuids.addAll(collect);
                }else {
                    List<String> collect = jgs.stream().map(ZnsbMhqxZzjgGxbDO::getUuid).collect(Collectors.toList());
                    uuids.addAll(collect);
                }

                List<String> jguuids = jgs.stream().map(ZnsbMhqxZzjgGxbDO::getJguuid1).collect(Collectors.toList());
                String sjZzuuid = zzxxByZz.get(m).get(0).getSjZzuuid();
                List<String> list = filterJguuid.get(sjZzuuid);
                if (GyUtils.isNull(list)){
                    filterJguuid.put(sjZzuuid,jguuids);
                }else {
                    Set<String> set = new LinkedHashSet<>(list);
                    set.addAll(jguuids);
                    filterJguuid.put(sjZzuuid,new ArrayList<>(set));
                }
            }
        });
        return b(znsbMhqxZzjgGxbDOS,zzxxbDOS.stream().filter(z->!mjzzuuids.contains(z.getZzuuid())).collect(Collectors.toList()), uuids,filterJguuid);
    }

    public List<ZnsbMhqxJgxxbDO> selectZnsbMhqxJgxxb(){
        return jgxxbMapper.selectZnsbMhqxJgxxb();
    }

    @Override
    public void updateBatchByDjxh(List<ZnsbMhqxJgxxbDO> updJgxxByDjxhList) {
        updJgxxByDjxhList.forEach(j->jgxxbMapper.updateByDjxh(j.getDjxh(),j.getJgmc1(),j.getNsrsbh()));
    }

    @Override
    public List<CompanyDTO> jgxxsByNsrsbh(String nsrsbh) {
        List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectTbwcjgsByNsrsbh(nsrsbh);
        if (GyUtils.isNull(jgxxbDOS)) return new ArrayList<>();
        return BeanUtils.toBean(jgxxbDOS, CompanyDTO.class);
    }

    @Override
    public List<String> getAllNsrsbh() {
        List<ZnsbMhqxJgxxbDO> list = jgxxbMapper.getAllNsrsbh();
        if (GyUtils.isNull(list)) return new ArrayList<>();
        return list.stream().map(ZnsbMhqxJgxxbDO::getNsrsbh).collect(Collectors.toList());
    }

    @Override
    public void initJgxx(CompanyDTO companyDTO) {
        checkParam(companyDTO);
        String nsrsbh = companyDTO.getNsrsbh();
        List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectByNsrsbh(nsrsbh);
        if (GyUtils.isNull(jgxxbDOS)||jgxxbDOS.size()==0){
            log.info("通过账户信息新增企业，nsrsh：{}",nsrsbh);
            ZnsbMhqxJgxxbDO jgxxbDO = BeanUtils.toBean(companyDTO, ZnsbMhqxJgxxbDO.class);
            jgxxbDO.setJguuid1(IdUtil.fastSimpleUUID());
            jgxxbDO.setJgmc1(companyDTO.getNsrmc());
            jgxxbDO.setShxydm(nsrsbh);
            jgxxbDO.setSsjswjgDm("1"+companyDTO.getXzqhszDm()+"0000");
            jgxxbDO.setTbwczt("N");
            jgxxbDO.setLrrq(new Date());
            jgxxbDO.setXgrq(new Date());
            jgxxbDO.setYxbz("Y");
            jgxxbDO.setYwqdDm("MHZC-ZHXX");
            jgxxbDO.setSjtbSj(new Date());
            jgxxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
            jgxxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
            jgxxbDO.setLrrsfid("MHZC-ZHXX");
            jgxxbDO.setXgrsfid("MHZC-ZHXX");
            this.save(jgxxbDO);
        }
    }

    @Override
    public CommonResult<Integer> getJgxxListByQydmz(String qydmz) {
        List<ZnsbMhqxJgxxbDO> jgxxbDOList = jgxxbMapper.getJgxxListByQydmz(qydmz);
        if (GyUtils.isNotNull(jgxxbDOList)) {
            return CommonResult.success(jgxxbDOList.size());
        } else {
            return CommonResult.success(0);
        }
    }

    @Override
    public List<String> getKczDjxhList(String token) {
        return JsonUtils.toList(RedisUtils.getHash(String.format("oauth2_access_token:%s", token),"kczDjxhList"),String.class);
    }

    @Override
    public List<CompanyBasicInfoDTO> getJgxxByDjxhs(List<String> djxhs) {
        // 参数校验
        if (GyUtils.isNull(djxhs) || djxhs.isEmpty()) {
            return Collections.emptyList();
        }

        // 批量查询机构信息
        final List<ZnsbMhqxJgxxbDO> jgxxbDOList = jgxxbMapper.getJgxxByDjxhs(djxhs);
        if (GyUtils.isNull(jgxxbDOList) || jgxxbDOList.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取登记序号列表，用于批量查询企业基本信息
        final List<String> djxhList = jgxxbDOList.stream()
                .map(ZnsbMhqxJgxxbDO::getDjxh)
                .filter(djxh -> !GyUtils.isNull(djxh))
                .collect(Collectors.toList());

        // 批量查询企业基本信息，避免N+1查询问题
        final Map<String, ZnsbMhzcQyjbxxmxDTO> qyjbxxMap = batchQueryQyjbxxByDjxhs(djxhList);

        // 转换并组装结果
        return jgxxbDOList.stream()
                .map(jgxxbDO -> buildCompanyBasicInfoDTO(jgxxbDO, qyjbxxMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Transactional
    @Override
    public CommonResult<String> initJgxxNew(CompanyDTO companyDTO) {
        checkParam(companyDTO);
        String nsrsbh = companyDTO.getNsrsbh();
        List<ZnsbMhqxJgxxbDO> jgxxbDOS = jgxxbMapper.selectByNsrsbh(nsrsbh);
        String jguuid = "";
        if (GyUtils.isNull(jgxxbDOS)||jgxxbDOS.size()==0){
            log.info("通过账户信息新增企业，nsrsh：{}",nsrsbh);
            ZnsbMhqxJgxxbDO jgxxbDO = BeanUtils.toBean(companyDTO, ZnsbMhqxJgxxbDO.class);
            jgxxbDO.setJguuid1(IdUtil.fastSimpleUUID());
            jgxxbDO.setJgmc1(companyDTO.getNsrmc());
            jgxxbDO.setShxydm(nsrsbh);
            jgxxbDO.setSsjswjgDm("1"+companyDTO.getXzqhszDm()+"0000");
            jgxxbDO.setTbwczt("N");
            jgxxbDO.setLrrq(new Date());
            jgxxbDO.setXgrq(new Date());
            jgxxbDO.setYxbz("Y");
            jgxxbDO.setYwqdDm("MHZC-ZHXX");
            jgxxbDO.setSjtbSj(new Date());
            jgxxbDO.setSjcsdq(MhzcConstants.SJCSDQ);
            jgxxbDO.setSjgsdq(MhzcConstants.SJGSDQ);
            jgxxbDO.setLrrsfid("MHZC-ZHXX");
            jgxxbDO.setXgrsfid("MHZC-ZHXX");
            this.save(jgxxbDO);
        }else {
            List<String> collect = jgxxbDOS.stream().filter(m -> m.getQylxz().equals("1")).map(ZnsbMhqxJgxxbDO::getJguuid1).collect(Collectors.toList());
            jguuid = collect.get(0);
        }
        CommonResult<String> res = zzxxService.bindYhQyAuto(companyDTO.getZzuuid(), jguuid, companyDTO.getYhuuid());
        if (res.getCode()==-1) return res;
        return CommonResult.success(jguuid);
    }

    /**
     * 批量查询企业基本信息，避免N+1查询问题
     *
     * @param djxhList 登记序号列表
     * @return 登记序号与企业基本信息的映射
     */
    private Map<String, ZnsbMhzcQyjbxxmxDTO> batchQueryQyjbxxByDjxhs(List<String> djxhList) {
        if (GyUtils.isNull(djxhList) || djxhList.isEmpty()) {
            return Collections.emptyMap();
        }

        // 使用批量查询方法（假设mapper支持批量查询，如果不支持需要添加）
        // 如果mapper不支持批量查询，可以使用MyBatis-Plus的selectBatchIds方法
        final List<ZnsbMhzcQyjbxxmxDTO> qyjbxxList = qyjbxxmxMapper.selectBatchIds(djxhList);

        return qyjbxxList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        ZnsbMhzcQyjbxxmxDTO::getDjxh,
                        dto -> dto,
                        (existing, replacement) -> existing // 处理重复key的情况
                ));
    }

    /**
     * 构建企业基本信息DTO
     *
     * @param jgxxbDO 机构信息DO
     * @param qyjbxxMap 企业基本信息映射
     * @return 企业基本信息DTO，如果企业基本信息不存在则返回null
     */
    private CompanyBasicInfoDTO buildCompanyBasicInfoDTO(ZnsbMhqxJgxxbDO jgxxbDO,
                                                        Map<String, ZnsbMhzcQyjbxxmxDTO> qyjbxxMap) {
        if (GyUtils.isNull(jgxxbDO) || GyUtils.isNull(jgxxbDO.getDjxh())) {
            return null;
        }

        // 获取对应的企业基本信息
        final ZnsbMhzcQyjbxxmxDTO qyjbxxDTO = qyjbxxMap.get(jgxxbDO.getDjxh());
        if (GyUtils.isNull(qyjbxxDTO)) {
            return null;
        }

        // 创建并填充企业基本信息DTO
        final CompanyBasicInfoDTO companyBasicInfoDTO = new CompanyBasicInfoDTO();

        // 复制机构信息
        BeanUtil.copyProperties(jgxxbDO, companyBasicInfoDTO, true);
        companyBasicInfoDTO.setJguuid(jgxxbDO.getJguuid1());
        companyBasicInfoDTO.setJgmc(jgxxbDO.getJgmc1());

        // 复制企业基本信息
        BeanUtil.copyProperties(qyjbxxDTO, companyBasicInfoDTO, true);

        return companyBasicInfoDTO;
    }

    void checkParam(CompanyDTO companyDTO) {
        if (GyUtils.isNull(companyDTO)) throw new IllegalStateException("请求参数不能为空");
        if (GyUtils.isNull(companyDTO.getNsrsbh())) throw new IllegalStateException("纳税人识别号不能为空");
        if (GyUtils.isNull(companyDTO.getNsrmc())) throw new IllegalStateException("纳税人名称不能为空");
        if (GyUtils.isNull(companyDTO.getQydmz())) throw new IllegalStateException("公司号不能为空");
        if (GyUtils.isNull(companyDTO.getXzqhszDm())) throw new IllegalStateException("行政区划不能为空");
        if (GyUtils.isNull(companyDTO.getZzuuid())) throw new IllegalStateException("组织uuid不能为空");
        if (GyUtils.isNull(companyDTO.getYhuuid())) throw new IllegalStateException("用户uuid不能为空");
    }

}
