package com.css.znsb.mhzc.controller.yhxx;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxYhxxbDO;
import com.css.znsb.mhzc.pojo.vo.user.*;
import com.css.znsb.mhzc.pojo.vo.user.tygj.TygjCommonResult;
import com.css.znsb.mhzc.pojo.vo.user.tygj.TygjUserReqVO;
import com.css.znsb.mhzc.pojo.vo.user.wlj.SchemaResVO;
import com.css.znsb.mhzc.pojo.vo.user.wlj.UserCreateReqVO;
import com.css.znsb.mhzc.pojo.vo.user.wlj.UserDeleteReqVO;
import com.css.znsb.mhzc.pojo.vo.user.wlj.UserResVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxBaseVO;
import com.css.znsb.mhzc.pojo.yhxx.UserInfoByIdsReqVO;
import com.css.znsb.mhzc.pojo.yhxx.YhxxVO;
import com.css.znsb.mhzc.service.yhxx.UserService;
import com.css.znsb.mhzc.util.MinioUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Tag(name = "用户信息接口")
@RestController
@RequestMapping("/user")
@Validated
@Slf4j
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private MinioUtils minioUtils;

    @PostMapping("/beforeInit")
    @Operation(summary = "初始化会话信息",description = "登录后提供用户初始化会话信息")
    public CommonResult<String> beforeInit(){
        return CommonResult.success("ok");
    }

    @PostMapping("/init")
    @Operation(summary = "初始化会话信息",description = "登录后提供用户初始化会话信息")
    public CommonResult<InitSessionResVO> initSession(){
        return CommonResult.success(userService.init());
    }

    @PostMapping("/saasinit")
    @Operation(summary = "初始化会话信息",description = "登录后提供用户初始化会话信息")
    public CommonResult<InitSessionResVO> saasinit(){
        return CommonResult.success(userService.saasinit());
    }

    @PostMapping("/getDjxhAndSkssq")
    @Operation(summary = "获取登记序号和税款所属期")
    public CommonResult<SessionVO> getDjxhAndSkssq(){
        SessionVO sessionVO = new SessionVO();
        sessionVO.setDjxh(ZnsbSessionUtils.getDjxh());
        sessionVO.setJguuid(ZnsbSessionUtils.getJguuid());
        sessionVO.setSfzjhm(ZnsbSessionUtils.getSfzjhm());
        sessionVO.setSfzjlx(ZnsbSessionUtils.getSfzjlx());
        sessionVO.setSfzjlx(ZnsbSessionUtils.getSfzjlx());
        sessionVO.setJgmc(ZnsbSessionUtils.getJgmc());
        sessionVO.setJguuid(ZnsbSessionUtils.getJguuid());
        sessionVO.setSwjgDm(ZnsbSessionUtils.getSwjgDm());
        sessionVO.setNsrsbh(ZnsbSessionUtils.getNsrsbh());
        sessionVO.setShxydm(ZnsbSessionUtils.getShxydm());
        sessionVO.setSjhm(ZnsbSessionUtils.getSjhm());
        sessionVO.setZsxm(ZnsbSessionUtils.getZsxm());
        sessionVO.setZzuuid(ZnsbSessionUtils.getZzuuid());
        sessionVO.setZzmc(ZnsbSessionUtils.getZzmc());
        sessionVO.setXzqhszDm(ZnsbSessionUtils.getXzqhszDm());
        Calendar calendar = Calendar.getInstance();

        // 设置为上个月份的第一天
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDayOfMonth = calendar.getTime();

        // 设置为上个月份的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为当前月的第一天
        calendar.add(Calendar.DATE, -1); // 减去一天，得到上个月的最后一天
        Date lastDayOfMonth = calendar.getTime();
        sessionVO.setSkssqq(DateUtils.toDate(DateUtils.toDateStrByFormatIndex(firstDayOfMonth, 3), "yyyy-MM-dd"));
        sessionVO.setSkssqz(DateUtils.toDate(DateUtils.toDateStrByFormatIndex(lastDayOfMonth, 3), "yyyy-MM-dd"));
        return CommonResult.success(sessionVO);
    }

    @PostMapping("/getAll")
    @Operation(summary = "获取全部用户信息",description = "参数模糊匹配")
    public CommonResult<List<UserInfoVO>> getAll(@RequestBody AllUserInfoReqVO reqVO){
        return CommonResult.success(userService.getAll(reqVO));
    }

    @PostMapping("/sfzjlxList")
    @Operation(summary = "获取身份证件类型列表")
    public CommonResult<List<SfzjlxVO>> sfzjlxList(){
        List<SfzjlxVO> result = new ArrayList<>();
        List<Map<String,Object>> listMap = CacheUtils.getTableData("dm_gy_sfzjlx");
        if (!GyUtils.isNull(listMap)){
            listMap.stream().filter(stringObjectMap -> "200".equals(stringObjectMap.get("sjsfzjlxDm"))).forEach(stringObjectMap -> {
                final SfzjlxVO sfzjlxVO = new SfzjlxVO();
                sfzjlxVO.setSfzjlxDm((String) stringObjectMap.get("sfzjlxDm"));
                sfzjlxVO.setSfzjlxmc((String) stringObjectMap.get("sfzjlxmc"));
                result.add(sfzjlxVO);
            });
        }
        return CommonResult.success(result);
    }


    @PostMapping("/getYhuuidsOfZz")
    @Operation(summary = "获取组织下已绑定的用户ID列表")
    public CommonResult<List<YhidsOfZzVO>> getYhuuidsOfZZ(@RequestBody ZzxxBaseVO baseVO){
        return CommonResult.success(userService.getYhidsOfZz(baseVO));
    }

    @PostMapping("/bindZz")
    @Operation(summary = "绑定组织",description = "批量保存一个组织与若干用户的绑定关系")
    public CommonResult<Boolean> bindZz(@RequestBody UserZzBindVO bindVO){
        return CommonResult.success(userService.bindZz(bindVO));
    }

    @PostMapping("/unBindZz")
    @Operation(summary = "解绑组织",description = "批量删除一个组织与若干用户的绑定关系")
    public CommonResult<Boolean> unBindZz(@RequestBody UserZzBindVO bindVO){
        return CommonResult.success(userService.unBindZz(bindVO));
    }


    @PostMapping("/insertYhxx")
    @Operation(summary = "新增用户信息")
    public CommonResult<Integer> insertYhxx(@RequestBody UserInsertReqVO reqVO){
        return CommonResult.success(userService.insertYhxx(reqVO));
    }

    @PostMapping("/updateYhxx")
    @Operation(summary = "修改用户信息")
    public CommonResult<Integer> updateYhxx(@RequestBody UserUpdateReqVO reqVO){
        return CommonResult.success(userService.updateYhxx(reqVO));
    }

    @PostMapping("/deleteYhxx")
    @Operation(summary = "删除用户信息")
    public CommonResult<Integer> deleteYhxx(@RequestBody UserBaseVO userBaseVO){
        if (GyUtils.isNull(userBaseVO)){
            return CommonResult.error(-1,"参数不能为空！");
        }
        return CommonResult.success(userService.deleteYhxx(userBaseVO.getInfoVOs()));
    }

    @PostMapping("/resetPassword")
    @Operation(summary = "重置密码")
    public CommonResult<Integer> resetPassword(@RequestBody AdminResetPasswordVO vo){
        return CommonResult.success(userService.resetPassword(vo));
    }

    @PostMapping("/forceResetPassword")
    @Operation(summary = "重置密码")
    public CommonResult<Integer> forceResetPassword(@RequestBody AdminResetPasswordVO vo){
        return CommonResult.success(userService.forceResetPassword(vo));
    }

    @PostMapping("/lockUser")
    @Operation(summary = "锁定用户")
    public CommonResult<Integer> lockUser(@RequestBody UserLockVO lockVO){
        return CommonResult.success(userService.lockUser(lockVO));
    }

    @PostMapping("/unlockUser")
    @Operation(summary = "解锁用户")
    public CommonResult<Integer> unlockUser(@RequestBody UserBaseVO infoVO){
        return CommonResult.success(userService.unlockUser(infoVO));
    }

    @PostMapping("/updUserYxbz")
    @Operation(summary = "启用/禁用用户")
    public CommonResult<Integer> updUserYxbz(@RequestBody @Valid UserYxbzVO yxbzVO){
        return CommonResult.success(userService.updUserYxbz(yxbzVO));
    }

    @PostMapping("/getAllJsxx")
    @Operation(summary = "获取用户的全部角色信息")
    public CommonResult<List<UserRoleBindVO>> getAllJsxx(@RequestBody UserBaseVO infoVO){
        return CommonResult.success(userService.getAllJsxx(infoVO));
    }

    @PostMapping("/bindJsxxBatch")
    @Operation(summary = "绑定与解绑角色信息")
    public CommonResult<Boolean> bindJsxxBatch(@RequestBody UserRoleBindBatchVO bindBatchVO){
        return CommonResult.success(userService.bindJsxx(bindBatchVO));
    }

    @PostMapping("/getAllJgxx")
    @Operation(summary = "获取用户的全部机构信息")
    public CommonResult<List<UserCompanyBindVO>> getAllJgxx(@RequestBody UserBaseVO infoVO){
        return CommonResult.success(userService.getAllJgxx(infoVO));
    }

    @PostMapping("/bindJgxxBatch")
    @Operation(summary = "绑定与解绑机构信息")
    public CommonResult<Boolean> bindJgxxBatch(@RequestBody UserCompanyBindBatchVO bindBatchVO){
        return CommonResult.success(userService.bindJgxx(bindBatchVO));
    }

    @PostMapping("/exportExcel")
    @Operation(summary = "用户信息批量添加模板导出")
    public void exportExcel(HttpServletResponse response) throws IOException {
//        List<GlqyExcelMbVO> list = new ArrayList<>();
//        QyxxglExcelColumnWidthStyle style = new QyxxglExcelColumnWidthStyle(40);
//        EasyExcel.write(response.getOutputStream(), UserExcelMbVO.class)
//                .registerWriteHandler(style)
//                .excelType(ExcelTypeEnum.XLS).sheet("模板").doWrite(list);
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("excelMb/用户管理模板.xls");
        byte[] bs = new byte[1024];
        int read;
        while ((read = inputStream.read(bs))!=-1){
            response.getOutputStream().write(bs,0,read);
        }
        response.flushBuffer();
        inputStream.close();
    }

    @PostMapping("/importExcel")
    @Operation(summary = "用户信息批量添加模板导入")
    public CommonResult<String> input(@RequestBody MultipartFile file) throws IOException {
        List<UserExcelMbVO> list = new ArrayList<>();
        EasyExcel.read(file.getInputStream(),UserExcelMbVO.class,new AnalysisEventListener<UserExcelMbVO>(){

            @Override
            public void invoke(UserExcelMbVO o, AnalysisContext analysisContext) {
                list.add(o);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {

            }
        }).sheet().doRead();
        if (GyUtils.isNull(list)){
            return CommonResult.error("file is empty");
        }
        List<ZnsbMhqxYhxxbDO> list1 = BeanUtils.toBean(list, ZnsbMhqxYhxxbDO.class);
        userService.batchYhxx(list1);
        return CommonResult.success("success");
    }

    @PostMapping("/upPicUrl")
    @Operation(summary = "上传头像url")
    public CommonResult<String> upPicUrl(@RequestBody MultipartFile file) throws IOException {
        String name = IdUtil.fastSimpleUUID();
        minioUtils.uploadFile(name,file.getInputStream(),file.getContentType());
        return CommonResult.success(name);
    }

    @PostMapping("/saveTxdzUrl")
    @Operation(summary = "保存头像地址")
    public CommonResult<String> saveTxdzUrl(@RequestBody UserInsertReqVO req){
        if (GyUtils.isNull(req)||GyUtils.isNull(req.getTxdzurl())){
            return CommonResult.error(-1,"头像地址为空");
        }
        String txdzurl = req.getTxdzurl();
        userService.upPicUrl(txdzurl);
        return CommonResult.success("success");
    }

    @PostMapping("/getPicUrl")
    @Operation(summary = "查询头像url")
    public CommonResult<String> getPicUrl(HttpServletResponse response,@RequestBody UserInsertReqVO req) throws IOException {
        InputStream inputStream = minioUtils.downloadFile(req.getTxdzurl());
        byte[] buffer = new byte[16384];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1){
            response.getOutputStream().write(buffer,0,bytesRead);
        }
        response.flushBuffer();
        inputStream.close();
        return CommonResult.success("success");
    }

    @GetMapping("/getPic")
    @Operation(summary = "查询头像url")
    public CommonResult<String> getPic(HttpServletResponse response,@RequestParam String objectName) throws IOException {
        if (GyUtils.isNull(objectName)){
            return CommonResult.error(-1,"头像地址为空");
        }
        InputStream inputStream = minioUtils.downloadFile(objectName);
        byte[] buffer = new byte[16384];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1){
            response.getOutputStream().write(buffer,0,bytesRead);
        }
        response.flushBuffer();
        inputStream.close();
        return CommonResult.success("success");
    }

    @PostMapping("/sfgly")
    @Operation(summary = "是否管理员")
    public CommonResult<String> sfgly(){
        return CommonResult.success(userService.sfgly());
    }

    @PostMapping("/savescdluuid")
    @Operation(summary = "保存上次登录jg/zzuuid")
    public CommonResult<String> savescdluuid(){
        userService.savescdluuid();
        return CommonResult.success("success");
    }

    @PostMapping("/getUserBySjhmAndZsxm")
    @Operation(summary = "通过手机号码和姓名获取用户")
    public CommonResult<YhxxVO> getUserBySjhmAndZsxm(@RequestParam("sjhm")String sjhm, @RequestParam("zsxm")String zsxm){
        return CommonResult.success(userService.getUserBySjhmAndZsxm(sjhm,zsxm));
    }

    @PostMapping("/SchemaService")
    @Operation(summary = "获取属性信息")
    public SchemaResVO SchemaService(@RequestBody UserCreateReqVO reqVO){
        return userService.SchemaService(reqVO);
    }

    @PostMapping("/UserCreateService")
    @Operation(summary = "新增用户信息")
    public UserResVO UserCreateService(@RequestBody UserCreateReqVO reqVO){
        return userService.UserCreateService(reqVO);
    }

    @PostMapping("/UserUpdateService")
    @Operation(summary = "修改用户信息")
    public UserResVO UserUpdateService(@RequestBody UserCreateReqVO reqVO){
        return userService.UserUpdateService(reqVO);
    }

    @PostMapping("/UserDeleteService")
    @Operation(summary = "删除用户信息")
    public UserResVO UserDeleteService(@RequestBody UserDeleteReqVO reqVO){
        return userService.UserDeleteService(reqVO);
    }

    @PostMapping("/tygj/UserCreate")
    @Operation(summary = "新增用户信息")
    public TygjCommonResult<Object> UserCreate(@RequestBody TygjUserReqVO reqVO){
        return userService.UserCreate(reqVO);
    }

    @PostMapping("/getUserInfoById")
    @Operation(summary = "根据用户ID查询用户信息")
    public CommonResult<YhxxVO> getUserInfoById(@RequestParam("yhUuid")String yhUuid){
        return CommonResult.success(userService.getUserInfoById(yhUuid));
    }

    @PostMapping("/getUserInfoByIds")
    @Operation(summary = "根据用户ID列表查询用户信息")
    public CommonResult<List<YhxxVO>> getUserInfoByIds(@RequestBody UserInfoByIdsReqVO reqVO) {
        return CommonResult.success(userService.getUserInfoByIds(reqVO));
    }

}
