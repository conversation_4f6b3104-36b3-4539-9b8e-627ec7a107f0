package com.css.znsb.mhzc.service.zzxx;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.mhzc.mapper.gxb.*;
import com.css.znsb.mhzc.mapper.xxb.ZnsbMhqxJgxxbMapper;
import com.css.znsb.mhzc.mapper.xxb.ZnsbMhqxJsxxbMapper;
import com.css.znsb.mhzc.mapper.xxb.ZnsbMhqxQxxxbMapper;
import com.css.znsb.mhzc.mapper.xxb.ZnsbMhqxZzxxbMapper;
import com.css.znsb.mhzc.pojo.domain.gxb.*;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxJgxxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxJsxxbDO;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxZzxxbDO;
import com.css.znsb.mhzc.pojo.saas.SaasZzxxVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.AllZzxxVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.SwitchZzVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxBaseVO;
import com.css.znsb.mhzc.pojo.vo.zzxx.ZzxxVO;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;

import com.css.znsb.mhzc.service.yhxx.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ZzxxServiceImpl extends ServiceImpl<ZnsbMhqxZzxxbMapper, ZnsbMhqxZzxxbDO> implements ZzxxService{

    @Resource
    private ZnsbMhqxZzxxbMapper zzxxbMapper;

    @Resource
    private ZnsbMhqxZzjgGxbMapper zzjgGxbMapper;

    @Resource
    private ZnsbMhqxZzyhGxbMapper zzyhGxbMapper;

    @Resource
    private ZnsbMhqxYhjsGxbMapper yhjsGxbMapper;

    @Resource
    private ZnsbMhqxYhjgGxbMapper yhjgGxbMapper;

    @Resource
    private ZnsbMhqxJsqxGxbMapper jsqxGxbMapper;

    @Resource
    private ZnsbMhqxJgxxbMapper jgxxbMapper;

    @Resource
    private ZnsbMhqxJsxxbMapper jsxxbMapper;

    @Resource
    private ZnsbMhqxQxxxbMapper qxxxbMapper;

    @Resource
    private UserService userService;


    @Override
    public List<ZzxxVO> getAllZz(AllZzxxVO allZzxxVO) {
        List<ZzxxVO> result = new ArrayList<>();
        String yhUuid = ZnsbSessionUtils.getYhUuid();
        String zzmc = GyUtils.isNull(allZzxxVO.getZzmc())?null:allZzxxVO.getZzmc();//入参组织名称
        List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByYhuuid(yhUuid);
        List<String> sszzuuids = znsbMhqxYhjsGxbDOS.stream().map(ZnsbMhqxYhjsGxbDO::getZzuuid).distinct().collect(Collectors.toList());//查询当前登录用户分配的角色所属组织
        List<ZnsbMhqxYhjgGxbDO> znsbMhqxYhjgGxbDOS = yhjgGxbMapper.selectAllJgByYhuuid(yhUuid);
        List<String> jgsszzuuids = znsbMhqxYhjgGxbDOS.stream().map(ZnsbMhqxYhjgGxbDO::getZzuuid).distinct().collect(Collectors.toList());//查询当前登录用户分配的机构所属组织
        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.selectAllZz(zzmc);
        if (!GyUtils.isNull(znsbMhqxZzxxbDOS)){
            Map<String, List<ZnsbMhqxZzxxbDO>> collect = znsbMhqxZzxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getZzuuid));//把所有组织按zzuuid分组
            Map<String, List<ZnsbMhqxZzxxbDO>> map = znsbMhqxZzxxbDOS.stream()//按上级组织uuid分组
                    .filter(znsbMhqxZzxxbDO -> !GyUtils.isNull(znsbMhqxZzxxbDO.getSjZzuuid()))//过滤掉上级组织uuid是空的组织
                    .collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getSjZzuuid));
            znsbMhqxZzxxbDOS.stream().sorted(Comparator.comparing(ZnsbMhqxZzxxbDO::getXh)).forEach(znsbMhqxZzxxbDO -> {//排序并遍历
                final ZzxxVO zzxxVO = convert(znsbMhqxZzxxbDO);//组装组织信息VO
                if (!GyUtils.isNull(zzxxVO.getSjzzuuid()) && !GyUtils.isNull(collect.get(zzxxVO.getSjzzuuid()))){//为有上级组织的组织组装上级组织名称
                    zzxxVO.setSjzzmc(collect.get(zzxxVO.getSjzzuuid()).get(0).getZzmc2());
                }

                List<String> zzuuids = new ArrayList<>();
                zzuuids.add(znsbMhqxZzxxbDO.getZzuuid());
                recursion2(zzuuids,znsbMhqxZzxxbDO.getZzuuid(),map);//递归组装组织树uuid
                List<ZnsbMhqxZzjgGxbDO> znsbMhqxZzjgGxbDOS = zzjgGxbMapper.selectJgByZz(zzuuids,"");
                if (!GyUtils.isNull(znsbMhqxZzjgGxbDOS)){
                    List<String> jguuids = znsbMhqxZzjgGxbDOS.stream().map(ZnsbMhqxZzjgGxbDO::getJguuid1).distinct().collect(Collectors.toList());
                    List<ZnsbMhqxJgxxbDO> znsbMhqxJgxxbDOS = jgxxbMapper.selectJgxxByUuids(jguuids,null,null);
                    if (!GyUtils.isNull(znsbMhqxJgxxbDOS)){
                        StringJoiner stringJoiner = new StringJoiner(",");
                        znsbMhqxJgxxbDOS.forEach(znsbMhqxJgxxbDO -> stringJoiner.add(znsbMhqxJgxxbDO.getJgmc1()));
                        zzxxVO.setGlqy(stringJoiner.toString());
                    }
                }
                if (sszzuuids.contains(znsbMhqxZzxxbDO.getZzuuid())){
                    zzxxVO.setSszz("Y");
                    List<String> list = znsbMhqxYhjsGxbDOS.stream().filter(v ->
                        znsbMhqxZzxxbDO.getZzuuid().equals(v.getZzuuid())
                    ).map(ZnsbMhqxYhjsGxbDO::getJsuuid).distinct().collect(Collectors.toList());
                    if (!GyUtils.isNull(list)){
                        List<ZnsbMhqxJsxxbDO> znsbMhqxJsxxbDOS = jsxxbMapper.selectJsxxByUuids(list);
                        List<String> list2 = znsbMhqxJsxxbDOS.stream().map(ZnsbMhqxJsxxbDO::getJsmc).distinct().collect(Collectors.toList());
                        zzxxVO.setYhjs(String.join(",",list2));
                    }
                }
                if (jgsszzuuids.contains(znsbMhqxZzxxbDO.getZzuuid())){
                    zzxxVO.setSfbhjg("Y");
                }
                result.add(zzxxVO);
            });
        }
        return result;
    }

    @Override
    public List<ZzxxVO> getAllZzOfCurrentUser() {
        List<ZzxxVO> result = new ArrayList<>();
        final String zzuuid = ZnsbSessionUtils.getZzuuid();
        if (GyUtils.isNull(zzuuid)){
            return result;
        }
        final ZnsbMhqxZzxxbDO znsbMhqxZzxxbDO = zzxxbMapper.selectZzxxByZzuuid(zzuuid);
        final ZzxxVO zzxxVO = convert(znsbMhqxZzxxbDO);
        result.add(zzxxVO);
        recursion(zzxxVO,result);
        return result.stream().sorted(Comparator.comparing(ZzxxVO::getXh)).collect(Collectors.toList());
    }

    @Override
    public Integer insert(ZzxxVO zzxxVO) {
        final Date date = new Date();
        final ZnsbMhqxZzxxbDO zzxxbDO = new ZnsbMhqxZzxxbDO();
        zzxxbDO.setZzuuid(IdUtil.fastSimpleUUID());
        zzxxbDO.setZzmc2(zzxxVO.getZzmc());
        String sjzzuuid = zzxxVO.getSjzzuuid();
        zzxxbDO.setSjZzuuid(sjzzuuid);
        zzxxbDO.setCcm(generateCcm(sjzzuuid));
        zzxxbDO.setXh(zzxxVO.getXh());
        zzxxbDO.setBz(zzxxVO.getBz());
        zzxxbDO.setYxbz("Y");
        zzxxbDO.setYwqdDm("ZNSB.MHZC");
        zzxxbDO.setLrrq(date);
        zzxxbDO.setXgrq(date);
        zzxxbDO.setLrrsfid(ZnsbSessionUtils.getYhUuid());
        zzxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
        zzxxbDO.setSjgsdq("00000000000");
        zzxxbDO.setSjcsdq("00000000000");
        zzxxbDO.setSjtbSj(new Date());
        return zzxxbMapper.insert(zzxxbDO);
    }

    @Override
    public String generateCcm(String sjzzuuid){
        if (!GyUtils.isNull(sjzzuuid)){
            List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.getCcmBySjzzid(sjzzuuid);
            if (!GyUtils.isNull(znsbMhqxZzxxbDOS)){
                ZnsbMhqxZzxxbDO max = znsbMhqxZzxxbDOS.stream().max(Comparator.comparing(ZnsbMhqxZzxxbDO::getCcm)).orElse(new ZnsbMhqxZzxxbDO());
                int i = Integer.parseInt(max.getCcm())+1;
                return "0"+i;
            }else {
                ZnsbMhqxZzxxbDO znsbMhqxZzxxbDO = zzxxbMapper.getCcmByZzid(sjzzuuid);
                return znsbMhqxZzxxbDO.getCcm()+"01";
            }
        }else {
            List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.getCcmByNull();
            if (GyUtils.isNull(znsbMhqxZzxxbDOS)){
                return "0701";
            }
            ZnsbMhqxZzxxbDO max = znsbMhqxZzxxbDOS.stream().max(Comparator.comparing(ZnsbMhqxZzxxbDO::getCcm)).orElse(new ZnsbMhqxZzxxbDO());
            int i = Integer.parseInt(max.getCcm())+1;
            return "0"+i;
        }
    }

    @Override
    public Integer update(ZzxxVO zzxxVO) {
        final ZnsbMhqxZzxxbDO zzxxbDO = new ZnsbMhqxZzxxbDO();
        zzxxbDO.setZzuuid(zzxxVO.getZzuuid());
        zzxxbDO.setZzmc2(zzxxVO.getZzmc());
        zzxxbDO.setSjZzuuid(zzxxVO.getSjzzuuid());
        zzxxbDO.setXh(zzxxVO.getXh());
        zzxxbDO.setBz(zzxxVO.getBz());
        zzxxbDO.setXgrq(new Date());
        zzxxbDO.setXgrsfid(ZnsbSessionUtils.getYhUuid());
        return zzxxbMapper.updateById(zzxxbDO);
    }

    @Transactional
    @Override
    public Integer delete(ZzxxBaseVO baseVO) {
        final String zzuuid = baseVO.getZzuuid();

        List<String> list = new ArrayList<>();
        list.add(zzuuid);
        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.selectAllZz(null);
        List<String> bxjZzuuids = getBxjZzuuids(list, znsbMhqxZzxxbDOS);
        final Integer i = zzjgGxbMapper.deleteByZzuuids(bxjZzuuids);
        final Integer j = zzyhGxbMapper.deleteByZzuuids(bxjZzuuids);
        final Integer k = yhjsGxbMapper.deleteByZzuuids(bxjZzuuids);
        final Integer m = yhjgGxbMapper.deleteByZzuuids(bxjZzuuids);
        final int l = zzxxbMapper.deleteBatchIds(bxjZzuuids);
        return i+j+k+l+m;
    }

    private List<String> getBxjZzuuids(List<String> zzuuids,List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS){
        List<String> collect = znsbMhqxZzxxbDOS.stream().filter(z -> zzuuids.contains(z.getSjZzuuid())).map(ZnsbMhqxZzxxbDO::getZzuuid).distinct().collect(Collectors.toList());
        if (GyUtils.isNull(collect)||zzuuids.containsAll(collect)){
            return zzuuids;
        }
        List<String> collect2 = Stream.concat(collect.stream(), zzuuids.stream()).distinct().collect(Collectors.toList());
        return getBxjZzuuids(collect2,znsbMhqxZzxxbDOS);
    }

    @Override
    public List<SwitchZzVO> switchZzList() {
        List<SwitchZzVO> result = new ArrayList<>();
        final String yhUuid = ZnsbSessionUtils.getYhUuid();
        List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByYhuuid(yhUuid);
        if (!GyUtils.isNull(znsbMhqxYhjsGxbDOS)){
            List<String> zzuuids = znsbMhqxYhjsGxbDOS.stream().map(ZnsbMhqxYhjsGxbDO::getZzuuid).distinct().collect(Collectors.toList());
            final List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.selectZzxxByZzuuidList(zzuuids);
            znsbMhqxZzxxbDOS.forEach(zzxx -> {
                final SwitchZzVO switchZzVO = new SwitchZzVO();
                switchZzVO.setZzuuid(zzxx.getZzuuid());
                switchZzVO.setZzmc(zzxx.getZzmc2());
                result.add(switchZzVO);
            });
        }

        return result;
    }

    @Override
    public List<String> getGLZzid() {
        String yhUuid = ZnsbSessionUtils.getYhUuid();
        if (userService.sfht()){
            List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.selectAllZz(null);
            return znsbMhqxZzxxbDOS.stream().map(ZnsbMhqxZzxxbDO::getZzuuid).collect(Collectors.toList());
        }
        List<String> list = new ArrayList<>();
        List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByYhuuid(yhUuid);
        Map<String, List<ZnsbMhqxYhjsGxbDO>> collect = znsbMhqxYhjsGxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxYhjsGxbDO::getZzuuid));
        collect.forEach((k, volist) -> {
            List<String> collect1 = volist.stream().map(ZnsbMhqxYhjsGxbDO::getJsuuid).collect(Collectors.toList());
            List<ZnsbMhqxJsqxGxbDO> znsbMhqxJsqxGxbDOS = jsqxGxbMapper.selectQxByJsuuids(collect1);
            if (!GyUtils.isNull(znsbMhqxJsqxGxbDOS)) {
                List<String> qxuuids = znsbMhqxJsqxGxbDOS.stream().map(ZnsbMhqxJsqxGxbDO::getQxuuid1).collect(Collectors.toList());
                if (!GyUtils.isNull(qxxxbMapper.sfglyByQxuuids(qxuuids))) {
                    list.add(k);
                }
            }
        });
        return list;
    }

    @Override
    public ZzxxVO getZzxxbyId(String zzuuid) {
        List<String> list = new ArrayList<>();
        list.add(zzuuid);
        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.selectZzxxByZzuuidList(list);
        ZnsbMhqxZzxxbDO znsbMhqxZzxxbDO = znsbMhqxZzxxbDOS.get(0);
        ZzxxVO vo = BeanUtils.toBean(znsbMhqxZzxxbDO, ZzxxVO.class);
        vo.setZzmc(znsbMhqxZzxxbDO.getZzmc2());
        vo.setSjzzuuid(znsbMhqxZzxxbDO.getSjZzuuid());
        return vo;
    }

    @Override
    public String getZzuuidByMc(String zzmc) {
        ZnsbMhqxZzxxbDO znsbMhqxZzxxbDO = zzxxbMapper.getZzidByMc(zzmc);
        if (GyUtils.isNull(znsbMhqxZzxxbDO)){
            return "";
        }
        return znsbMhqxZzxxbDO.getZzuuid();
    }

    @Override
    public Map<String, List<ZnsbMhqxZzxxbDO>> getZzidByMc(List<String> zzmcList) {
        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.getZzidsByMcs(zzmcList);
        return znsbMhqxZzxxbDOS.stream().collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getZzmc2));
    }

    private void recursion(ZzxxVO zzxxVO, List<ZzxxVO> result){
        final List<ZnsbMhqxZzxxbDO> xjzzs = zzxxbMapper.selectXjzzByZzuuid(zzxxVO.getZzuuid());
        if (GyUtils.isNull(xjzzs)){
            return;
        }
        xjzzs.forEach(znsbMhqxZzxxbDO -> {
            final ZzxxVO convert = convert(znsbMhqxZzxxbDO);
            convert.setSjzzmc(zzxxVO.getZzmc());
            result.add(convert);
            recursion(convert,result);
        });
    }

    private void recursion2(List<String> zzuuids,String zzuuid,Map<String, List<ZnsbMhqxZzxxbDO>> map){
        if (GyUtils.isNull(zzuuid)){
            return;
        }

        List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = map.get(zzuuid);
        if (!GyUtils.isNull(znsbMhqxZzxxbDOS)){
            List<String> collect = znsbMhqxZzxxbDOS.stream().map(ZnsbMhqxZzxxbDO::getZzuuid).collect(Collectors.toList());
            zzuuids.addAll(collect);
            collect.forEach(s -> recursion2(zzuuids,s,map));
        }
    }



    private ZzxxVO convert(ZnsbMhqxZzxxbDO znsbMhqxZzxxbDO){
        return new ZzxxVO().setZzuuid(znsbMhqxZzxxbDO.getZzuuid())
                .setZzmc(znsbMhqxZzxxbDO.getZzmc2())
                .setSjzzuuid(znsbMhqxZzxxbDO.getSjZzuuid())
                .setXh(znsbMhqxZzxxbDO.getXh())
                .setBz(znsbMhqxZzxxbDO.getBz());
    }

    @Override
    public List<SaasZzxxVO> switchSaasZzList() {
        List<SaasZzxxVO> result = new ArrayList<>();
        final String yhUuid = ZnsbSessionUtils.getYhUuid();
        List<ZnsbMhqxYhjsGxbDO> znsbMhqxYhjsGxbDOS = yhjsGxbMapper.selectAllJsByYhuuid(yhUuid);
        if (!GyUtils.isNull(znsbMhqxYhjsGxbDOS)){
            List<String> zzuuids = znsbMhqxYhjsGxbDOS.stream().map(ZnsbMhqxYhjsGxbDO::getZzuuid).distinct().collect(Collectors.toList());
            final List<ZnsbMhqxZzxxbDO> znsbMhqxZzxxbDOS = zzxxbMapper.selectZzxxByZzuuidList(zzuuids);

            List<ZnsbMhqxZzxxbDO> allZz = zzxxbMapper.selectAllZz(null);
            if (!GyUtils.isNull(allZz)){
                Map<String, List<ZnsbMhqxZzxxbDO>> collect = allZz.stream().collect(Collectors.groupingBy(ZnsbMhqxZzxxbDO::getZzuuid));//把所有组织按zzuuid分组
                znsbMhqxZzxxbDOS.forEach(zzxx -> {
                    final SaasZzxxVO saasZzxxVO = new SaasZzxxVO();
                    saasZzxxVO.setZzuuid(zzxx.getZzuuid());
                    saasZzxxVO.setZzmc(zzxx.getZzmc2());
                    saasZzxxVO.setXh(zzxx.getXh());
                    if (!GyUtils.isNull(zzxx.getSjZzuuid()) && !GyUtils.isNull(collect.get(zzxx.getSjZzuuid()))) {//为有上级组织的组织组装上级组织名称
                        saasZzxxVO.setSjzzuuid(zzxx.getSjZzuuid());
                        saasZzxxVO.setSjzzmc(collect.get(zzxx.getSjZzuuid()).get(0).getZzmc2());
                    }
                    result.add(saasZzxxVO);
                });
            }
        }

        return result;
    }

    @Transactional
    @Override
    public CommonResult<String> bindYhQyAuto(String zzuuid,String qyuuid,String yhuuid){
        ZnsbMhqxJgxxbDO jgxxbDO = jgxxbMapper.selectById(qyuuid);
        if (GyUtils.isNull(jgxxbDO)) return CommonResult.error("企业不存在");
        if (GyUtils.isNull(jgxxbDO.getTbwczt())) return CommonResult.error("企业未同步");
        if (!jgxxbDO.getTbwczt().equals("Y")) return CommonResult.error("企业未同步");
        Date date = new Date();
        List<ZnsbMhqxZzjgGxbDO> list = new ArrayList<>();
        List<String> bsjZzuuids = getDcrBsjZzuuids(new ArrayList<>(), zzuuid, qyuuid);
        bsjZzuuids.forEach(s->{
            ZnsbMhqxZzjgGxbDO zzjgGxbDO = new ZnsbMhqxZzjgGxbDO();
            zzjgGxbDO.setJguuid1(qyuuid);
            zzjgGxbDO.setZzuuid(s);
            zzjgGxbDO.setUuid(IdUtil.fastSimpleUUID());
            zzjgGxbDO.setLrrq(date);
            zzjgGxbDO.setLrrsfid("AUTO");
            zzjgGxbDO.setSjcsdq("AUTO");
            zzjgGxbDO.setSjgsdq("AUTO");
            zzjgGxbDO.setXgrsfid("AUTO");
            zzjgGxbDO.setYwqdDm("AUTO");
            zzjgGxbDO.setXgrq(date);
            zzjgGxbDO.setSjtbSj(date);
            list.add(zzjgGxbDO);
        });
        zzjgGxbMapper.saveBatch(list);
        List<ZnsbMhqxZzyhGxbDO> byZzAndYh = zzyhGxbMapper.getByZzAndYh(zzuuid, yhuuid);
        if (GyUtils.isNull(byZzAndYh)) {
            ZnsbMhqxZzyhGxbDO zzyhGxbDO = new ZnsbMhqxZzyhGxbDO();
            zzyhGxbDO.setUuid(IdUtil.fastSimpleUUID());
            zzyhGxbDO.setZzuuid(zzuuid);
            zzyhGxbDO.setYhUuid(yhuuid);
            zzyhGxbDO.setLrrq(date);
            zzyhGxbDO.setXgrq(date);
            zzyhGxbDO.setSjtbSj(date);
            zzyhGxbDO.setYwqdDm("AUTO");
            zzyhGxbDO.setSjcsdq("AUTO");
            zzyhGxbDO.setSjgsdq("AUTO");
            zzyhGxbDO.setLrrsfid("AUTO");
            zzyhGxbDO.setXgrsfid("AUTO");
            zzyhGxbMapper.insert(zzyhGxbDO);
        }
        List<ZnsbMhqxYhjgGxbDO> byYhAndZzAndQy = yhjgGxbMapper.getByYhAndZzAndQy(yhuuid, zzuuid, qyuuid);
        if (GyUtils.isNull(byYhAndZzAndQy)) {
            ZnsbMhqxYhjgGxbDO yhjgGxbDO = new ZnsbMhqxYhjgGxbDO();
            yhjgGxbDO.setUuid(IdUtil.fastSimpleUUID());
            yhjgGxbDO.setZzuuid(zzuuid);
            yhjgGxbDO.setJguuid1(qyuuid);
            yhjgGxbDO.setYhUuid(yhuuid);
            yhjgGxbDO.setLrrq(date);
            yhjgGxbDO.setXgrq(date);
            yhjgGxbDO.setSjtbSj(date);
            yhjgGxbDO.setYwqdDm("AUTO");
            yhjgGxbDO.setSjcsdq("AUTO");
            yhjgGxbDO.setSjgsdq("AUTO");
            yhjgGxbDO.setLrrsfid("AUTO");
            yhjgGxbDO.setXgrsfid("AUTO");
            yhjgGxbMapper.insert(yhjgGxbDO);
        }
        ZnsbMhqxJsxxbDO znsbMhqxJsxxbDO = jsxxbMapper.selectByJsmc("办税员");
        if (!GyUtils.isNull(znsbMhqxJsxxbDO)){
            String jsuuid = znsbMhqxJsxxbDO.getJsuuid();
            List<ZnsbMhqxYhjsGxbDO> byYhAndZzAndJs = yhjsGxbMapper.getByYhAndZzAndJs(yhuuid, zzuuid, jsuuid);
            if (GyUtils.isNull(byYhAndZzAndJs)){
                ZnsbMhqxYhjsGxbDO yhjsGxbDO = new ZnsbMhqxYhjsGxbDO();
                yhjsGxbDO.setUuid(IdUtil.fastSimpleUUID());
                yhjsGxbDO.setZzuuid(zzuuid);
                yhjsGxbDO.setJsuuid(jsuuid);
                yhjsGxbDO.setYhUuid(yhuuid);
                yhjsGxbDO.setLrrq(date);
                yhjsGxbDO.setXgrq(date);
                yhjsGxbDO.setSjtbSj(date);
                yhjsGxbDO.setYwqdDm("AUTO");
                yhjsGxbDO.setSjcsdq("AUTO");
                yhjsGxbDO.setSjgsdq("AUTO");
                yhjsGxbDO.setLrrsfid("AUTO");
                yhjsGxbDO.setXgrsfid("AUTO");
                yhjsGxbMapper.insert(yhjsGxbDO);
            }
        }
        return CommonResult.success("ok");
    }

    //查询未绑定企业的所有本上级组织uuid
    private List<String> getDcrBsjZzuuids(List<String> topZzuuids,String zzuuid,String qyuuid){
        List<ZnsbMhqxZzjgGxbDO> byZzAndJg = zzjgGxbMapper.getByZzAndJg(zzuuid, qyuuid);
        if (GyUtils.isNull(byZzAndJg)) topZzuuids.add(zzuuid);
        ZnsbMhqxZzxxbDO znsbMhqxZzxxbDO = zzxxbMapper.selectById(zzuuid);
        if (GyUtils.isNull(znsbMhqxZzxxbDO.getSjZzuuid())){
            return topZzuuids;
        }
        return getDcrBsjZzuuids(topZzuuids,znsbMhqxZzxxbDO.getSjZzuuid(),qyuuid);
    }

}
