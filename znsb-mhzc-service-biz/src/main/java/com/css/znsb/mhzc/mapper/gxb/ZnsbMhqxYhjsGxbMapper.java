package com.css.znsb.mhzc.mapper.gxb;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.mybatis.core.mapper.BaseMapperX;
import com.css.znsb.framework.mybatis.core.query.QueryWrapperX;
import com.css.znsb.mhzc.pojo.domain.gxb.ZnsbMhqxYhjsGxbDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ZnsbMhqxYhjsGxbMapper extends BaseMapperX<ZnsbMhqxYhjsGxbDO> {


    default Integer deleteByYhuuid(List<String> yhuuids){
        QueryWrapperX<ZnsbMhqxYhjsGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().in(ZnsbMhqxYhjsGxbDO::getYhUuid,yhuuids);
        return delete(wrapperX);
    }

    default List<ZnsbMhqxYhjsGxbDO> selectAllJsByYhuuid(String yhuuid){
        QueryWrapperX<ZnsbMhqxYhjsGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .select(ZnsbMhqxYhjsGxbDO::getYhUuid,ZnsbMhqxYhjsGxbDO::getZzuuid,ZnsbMhqxYhjsGxbDO::getJsuuid)
                .eq(ZnsbMhqxYhjsGxbDO::getYhUuid,yhuuid);
        return selectList(wrapperX);
    }

    default Integer deleteByUuid(String zzuuid,String yhuuid,String jsuuid){
        QueryWrapperX<ZnsbMhqxYhjsGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .eq(ZnsbMhqxYhjsGxbDO::getYhUuid,yhuuid)
                .eq(ZnsbMhqxYhjsGxbDO::getZzuuid,zzuuid)
                .eq(!GyUtils.isNull(jsuuid),ZnsbMhqxYhjsGxbDO::getJsuuid,jsuuid);
        return delete(wrapperX);
    }

    default List<ZnsbMhqxYhjsGxbDO> getByYhAndZzAndJs(String yhuuid, String zzuuid, String jsuuid){
        QueryWrapperX<ZnsbMhqxYhjsGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .eq(ZnsbMhqxYhjsGxbDO::getYhUuid,yhuuid)
                .eq(ZnsbMhqxYhjsGxbDO::getZzuuid,zzuuid)
                .eq(ZnsbMhqxYhjsGxbDO::getJsuuid,jsuuid);
        return selectList(wrapperX);
    }

    default List<ZnsbMhqxYhjsGxbDO> selectAllJsByUuid(String yhuuid,String zzuuid){
        QueryWrapperX<ZnsbMhqxYhjsGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().
                select(ZnsbMhqxYhjsGxbDO::getYhUuid,ZnsbMhqxYhjsGxbDO::getZzuuid,ZnsbMhqxYhjsGxbDO::getJsuuid)
                .eq(ZnsbMhqxYhjsGxbDO::getYhUuid,yhuuid)
                .eq(ZnsbMhqxYhjsGxbDO::getZzuuid,zzuuid);
        return selectList(wrapperX);
    }


    default Integer deleteByZzuuids(List<String> zzuuids){
        if (GyUtils.isNull(zzuuids)){
            return 0;
        }
        QueryWrapperX<ZnsbMhqxYhjsGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .in(ZnsbMhqxYhjsGxbDO::getZzuuid,zzuuids);
        return delete(wrapperX);
    }

    default Integer deleteByJsuuid(String jsuuid){
        QueryWrapperX<ZnsbMhqxYhjsGxbDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda()
                .eq(ZnsbMhqxYhjsGxbDO::getJsuuid,jsuuid);
        return delete(wrapperX);
    }



    default List<ZnsbMhqxYhjsGxbDO> selectJsxxByZzuuid(String zzuuid){
        QueryWrapper<ZnsbMhqxYhjsGxbDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(ZnsbMhqxYhjsGxbDO::getJsuuid)
                .eq(ZnsbMhqxYhjsGxbDO::getZzuuid,zzuuid);
        return selectList(wrapper);
    }
}




