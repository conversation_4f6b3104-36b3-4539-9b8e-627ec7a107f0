package com.css.znsb.mhzc.service.nsrxx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.session.ZnsbSessionUtils;
import com.css.znsb.mhzc.mapper.nsrxx.ZnsbMhzcNsrfxxxMapper;
import com.css.znsb.mhzc.mapper.nsrxx.ZnsbMhzcQyjbxxmxMapper;
import com.css.znsb.mhzc.pojo.domain.xxb.ZnsbMhqxJgxxbDO;
import com.css.znsb.mhzc.pojo.dto.nsrxx.ZnsbMhzcNsrfxxxDTO;
import com.css.znsb.mhzc.pojo.dto.nsrxx.ZnsbMhzcQyjbxxmxDTO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.vo.company.*;
import com.css.znsb.mhzc.service.nsrxx.IZnsbMhzcQyjbxxmxService;
import com.css.znsb.mhzc.util.MhzcGyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【znsb_mhzc_qyjbxxmx(企业基本信息)】的数据库操作Service实现
* @createDate 2024-04-12 21:04:07
*/
@Slf4j
@Service
@Transactional
public class ZnsbMhzcQyjbxxmxServiceImpl extends ServiceImpl<ZnsbMhzcQyjbxxmxMapper, ZnsbMhzcQyjbxxmxDTO>
    implements IZnsbMhzcQyjbxxmxService{
    @Resource
    private ZnsbMhzcQyjbxxmxMapper znsbmhzcqyjbxxmxmapper;
    @Resource
    private ZnsbMhzcNsrfxxxMapper nsrfxxxMapper;
    @Override
    public List<JbxxmxsjVO> getZnsbMhzcQyjbxxmxByNsrsbh(ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo){
//        List<JbxxmxsjVO>  jbxxmxsjVOList = znsbmhzcqyjbxxmxmapper.getZnsbMhzcQyjbxxmxByNsrsbh(znsbmhzcqyjbxxmxreqvo);
        QueryWrapper<ZnsbMhzcQyjbxxmxDTO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ZnsbMhzcQyjbxxmxDTO::getNsrsbh,znsbmhzcqyjbxxmxreqvo.getNsrsbh());
        List<ZnsbMhzcQyjbxxmxDTO> znsbMhzcQyjbxxmxDTOS = znsbmhzcqyjbxxmxmapper.getjbxxByNsr(znsbmhzcqyjbxxmxreqvo.getNsrsbh());
        if (GyUtils.isNull(znsbMhzcQyjbxxmxDTOS)){
            return null;
        }
        return BeanUtils.toBean(znsbMhzcQyjbxxmxDTOS, JbxxmxsjVO.class);
    }

    @Override
    public List<JbxxmxsjVO> getZnsbMhzcQyjbxxmxByDjxh(ZnsbMhzcQyjbxxmxReqVO znsbmhzcqyjbxxmxreqvo) {
        List<ZnsbMhzcQyjbxxmxDTO> znsbMhzcQyjbxxmxDTOS = znsbmhzcqyjbxxmxmapper.getjbxxByDjxh(znsbmhzcqyjbxxmxreqvo.getDjxh());
        if (GyUtils.isNull(znsbMhzcQyjbxxmxDTOS)){
            return null;
        }
        return BeanUtils.toBean(znsbMhzcQyjbxxmxDTOS, JbxxmxsjVO.class);
    }

    @Override
    public List<ZnsbMhzcQyjbxxmxDTO> getInsetJbxxmxList(List<JbxxmxsjVO> jbxxmxsjVOList){
        if(!GyUtils.isNull(jbxxmxsjVOList)){
            List<ZnsbMhzcQyjbxxmxDTO> znsbMhzcQyjbxxmxDTOS = BeanUtils.toBean(jbxxmxsjVOList, ZnsbMhzcQyjbxxmxDTO.class);
//            this.removeBatchByIds(znsbMhzcQyjbxxmxDTOS);
            List<ZnsbMhzcQyjbxxmxDTO> collect = znsbMhzcQyjbxxmxDTOS.stream().map(v -> {
                v.setLrrq(new Date());
                v.setXgrq(new Date());
                v.setLrrsfid("qyd");
                v.setXgrsfid("qyd");
                v.setSjcsdq("lq");
                v.setSjgsdq("qyd");
                v.setYwqdDm("qyd");
                if (GyUtils.isNull(v.getZfjglxDm())){
                    v.setZfjglxDm("x");
                }
                if (GyUtils.isNull(v.getHyDm())){
                    v.setHymc("");
                }else {
                    v.setHymc(CacheUtils.dm2mc("dm_gy_hy",v.getHyDm()));
                }
                if (GyUtils.isNull(v.getZgswskfjDm())){
                    v.setZgswskfjmc("");
                }else {
                    v.setZgswskfjmc(CacheUtils.dm2mc("dm_gy_swjg",v.getZgswskfjDm()));
                }
                if (GyUtils.isNull(v.getZgswjDm())){
                    v.setSwjgmc("");
                }else {
                    v.setSwjgmc(CacheUtils.dm2mc("dm_gy_swjg",v.getZgswjDm()));
                }
                v.setZzsqylxDm("0");
                return v;
            }).collect(Collectors.toList());
            return collect;
        }
        return null;
    }

    @Override
    public List<GlqymxResVO> glqymx(GlqymxReqVO reqVO) {
        List<String> kczDjxhList = ZnsbSessionUtils.getKczDjxhList();
        if (GyUtils.isNull(kczDjxhList)){
            log.debug("获取管理企业明细-ZnsbSessionUtils获取djxhList为空");
            return null;
        }
        List<BigInteger> collect = kczDjxhList.stream().filter(v->!GyUtils.isNull(v)).map(v -> new BigInteger(v.trim())).collect(Collectors.toList());
        if (GyUtils.isNull(collect)){
            return null;
        }
        reqVO.setList(collect);
        List<GlqymxResVO> list = znsbmhzcqyjbxxmxmapper.glqymx(reqVO);
        if (GyUtils.isNull(list)){
            return null;
        }
        List<GlqymxResVO> glqymxResVOS = BeanUtils.toBean(list, GlqymxResVO.class);
        return glqymxResVOS.stream().map(v->{
            if (GyUtils.isNull(v.getQydmz())){
                v.setQydmz(null);
            }
            return v;
        }).sorted(Comparator.comparing(
                        GlqymxResVO::getQydmz,
                        Comparator.nullsLast(String::compareTo))
                        .thenComparing(GlqymxResVO::getNsrmc))
                .collect(Collectors.toList());
    }

    @Override
    public GlqyNsrjbxxResVO glqyNsrxx(String djxh) {
        ZnsbMhzcQyjbxxmxDTO vo = znsbmhzcqyjbxxmxmapper.glqyNsrxx(djxh);
        if (GyUtils.isNull(vo)){
            return null;
        }

        GlqyNsrjbxxResVO newVo = BeanUtils.toBean(vo,GlqyNsrjbxxResVO.class);
        String nsrsbh = vo.getNsrsbh();
        List<ZnsbMhzcNsrfxxxDTO> byNsrsbh = nsrfxxxMapper.getByNsrsbh(nsrsbh);
        if (!GyUtils.isNull(byNsrsbh)&&byNsrsbh.size()>0){
            newVo.setNsrxydjDm(byNsrsbh.get(0).getNsrxydjDm());
        }else {
            newVo.setNsrxydjDm("未知");
        }
//        Map<String, Object> map = CacheUtils.getTableData("DM_GY_NSRZT", vo.getNsrztDm());
        String nsrzt = CacheUtils.dm2mc("dm_gy_nsrzt", vo.getNsrztDm());
        newVo.setNsrzt(nsrzt);
        String zsfsmc = CacheUtils.dm2mc("dm_gy_zsfs", vo.getQysdszsfsDm());
        newVo.setQysdszsfsmc(zsfsmc);
        String fddbrsfzjlx = CacheUtils.dm2mc("dm_gy_sfzjlx", vo.getFddbrsfzjlxDm());
        newVo.setFddbrsfzjlxmc(fddbrsfzjlx);
        String bsrrsfzjlx = CacheUtils.dm2mc("dm_gy_sfzjlx", vo.getBsrsfzjlxDm());
        newVo.setBsrsfzjlxmc(bsrrsfzjlx);
        String cwfzrrsfzjlx = CacheUtils.dm2mc("dm_gy_sfzjlx", vo.getCwfzrsfzjlxDm());
        newVo.setCwfzrsfzjlxmc(cwfzrrsfzjlx);
        String kzztdjlx = CacheUtils.dm2mc("dm_gy_kzztdjlx", vo.getKzztdjlxDm());
        newVo.setKzztdjlxmc(kzztdjlx);
        String jdxzmc = CacheUtils.dm2mc("dm_gy_jdxz", vo.getJdxzDm());
        newVo.setJdxzmc(jdxzmc);
        String zcdxzqhmc = CacheUtils.dm2mc("dm_gy_xzqh",vo.getZcdzxzqhszDm());
        newVo.setZcdzxzqhmc(zcdxzqhmc);
        String scdxzqhmc = CacheUtils.dm2mc("dm_gy_xzqh",vo.getScjydzxzqhszDm());
        newVo.setScjydzxzqhmc(scdxzqhmc);
        String djzclxmc = CacheUtils.dm2mc("dm_gy_djzclx",vo.getDjzclxDm());
        newVo.setDjzclxmc(djzclxmc);
        String swjgmc = CacheUtils.dm2mc("dm_gy_swjg", vo.getZgswjDm());
        newVo.setSwjgmc(swjgmc);
        String zgswskfjmc = CacheUtils.dm2mc("dm_gy_swjg", vo.getZgswskfjDm());
        newVo.setZgswskfjmc(zgswskfjmc);
        String hymc = CacheUtils.dm2mc("dm_gy_hy", vo.getHyDm());
        newVo.setHymc(hymc);
        newVo.setFddbrsfzjhm(GyUtils.isNull(newVo.getFddbrsfzjhm())?"": MhzcGyUtils.maskIDNumber(newVo.getFddbrsfzjhm()));
        newVo.setCwfzrsfzjhm(GyUtils.isNull(newVo.getCwfzrsfzjhm())?"": MhzcGyUtils.maskIDNumber(newVo.getCwfzrsfzjhm()));
        newVo.setBsrsfzjhm(GyUtils.isNull(newVo.getBsrsfzjhm())?"": MhzcGyUtils.maskIDNumber(newVo.getBsrsfzjhm()));
        return newVo;
    }

    @Override
    public CountGlqyVO countGlqy() {
        List<String> list = ZnsbSessionUtils.getKczDjxhList();
        log.info("countGlqy获取可操作djxhList：{}",list);
        List<BigInteger> kczDjxhList = list.stream().map(v -> new BigInteger(v.trim())).collect(Collectors.toList());
        CountGlqyVO countGlqyVO = znsbmhzcqyjbxxmxmapper.countGlqy(kczDjxhList);
        log.info("countGlqy查询结果：{}",countGlqyVO);
        if (GyUtils.isNull(countGlqyVO)){
            countGlqyVO = new CountGlqyVO();
            countGlqyVO.setGlqysl("0");
            countGlqyVO.setXgmnsrsl("0");
            countGlqyVO.setYbnsrsl("0");
        }
        return countGlqyVO;
    }
}




