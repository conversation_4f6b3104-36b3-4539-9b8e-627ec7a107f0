--- #################### 注册中心相关配置 ####################

spring:
  cloud:
    nacos:
      server-addr: 10.23.10.91:8848
      username: qyddev
      password: 9!1%Sw#QydNacosDev
      discovery:
        namespace: 0ac06fee-c2c9-4ca0-9a26-4921d553adde # 命名空间。这里使用 dev 开发环境
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布

--- #################### 配置中心相关配置 ####################

spring:
  cloud:
    nacos:
      # Nacos Config 配置项，对应 NacosConfigProperties 配置属性类
      config:
        server-addr: 10.23.10.91:8848 # Nacos 服务器地址
        username: qyddev
        password: 9!1%Sw#QydNacosDev
        namespace: 0ac06fee-c2c9-4ca0-9a26-4921d553adde # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        name: ${spring.application.name} # 使用的 Nacos 配置集的 dataId，默认为 spring.application.name
        file-extension: yaml # 使用的 Nacos 配置集的 dataId 的文件拓展名，同时也是 Nacos 配置集的配置格式，默认为 properties


local-feign:
  enable: true #开发环境如果需要启用 请改成true
  rule:
#    dlfw-service: http://dlfw.znsb.tax.cn
    dlfw-service: http://localhost:48097
#    nssb-service: http://nssb.znsb.tax.cn
    nssb-service: http://localhost:48100
    mhzc-service: http://localhost:48099
